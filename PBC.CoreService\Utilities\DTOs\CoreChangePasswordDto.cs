using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;

namespace PBC.CoreService.Utilities.DTOs
{
    #region ChangePasswordListsAndObjs
    /// <summary>
    /// Request object for checking old password
    /// </summary>
    public class CheckOldPasswordList
    {
        /// <summary>
        /// User ID to check password for
        /// </summary>
        [Required]
        public int User_ID { get; set; }

        /// <summary>
        /// Encrypted old password to verify
        /// </summary>
        [Required]
        public string OldPassword { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request object for changing user password
    /// </summary>
    public class ChangePasswordList
    {
        /// <summary>
        /// User ID whose password is being changed
        /// </summary>
        [Required]
        public int User_ID { get; set; }

        /// <summary>
        /// JSON string containing encrypted old and new passwords
        /// Format: {"OldPassword":"encrypted_value","NewPassword":"encrypted_value"}
        /// </summary>
        [Required]
        [Description("JSON string with OldPassword and NewPassword fields")]
        public string data { get; set; } = string.Empty;

        /// <summary>
        /// Company ID
        /// </summary>
        [Required]
        public int Company_ID { get; set; }

        /// <summary>
        /// Branch ID
        /// </summary>
        [Required]
        public int Branch { get; set; }

        /// <summary>
        /// Menu ID for audit purposes
        /// </summary>
        [Required]
        public int MenuID { get; set; }

        /// <summary>
        /// Timestamp when user logged in
        /// </summary>
        [Required]
        public DateTime LoggedINDateTime { get; set; }
    }

    public class GNM_User
    {
        public int User_ID { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        // Add other properties as needed
    }
    #endregion

    #region Request DTOs with Configuration (from gateway to PBC.CoreService)
    /// <summary>
    /// Request object for checking old password with configuration (gateway to CoreService)
    /// </summary>
    public class CheckOldPasswordRequestWithConfig
    {
        /// <summary>
        /// User ID to check password for
        /// </summary>
        [Required]
        public int User_ID { get; set; }

        /// <summary>
        /// Encrypted old password to verify
        /// </summary>
        [Required]
        public string OldPassword { get; set; } = string.Empty;

        /// <summary>
        /// Database connection string (from gateway configuration)
        /// </summary>
        [Required]
        public string ConnString { get; set; } = string.Empty;

        /// <summary>
        /// Log exception flag (from gateway configuration)
        /// </summary>
        [Required]
        public int LogException { get; set; }
    }

    /// <summary>
    /// Request object for changing user password with configuration (gateway to CoreService)
    /// </summary>
    public class ChangePasswordRequestWithConfig
    {
        /// <summary>
        /// User ID whose password is being changed
        /// </summary>
        [Required]
        public int User_ID { get; set; }

        /// <summary>
        /// JSON string containing encrypted old and new passwords
        /// Format: {"OldPassword":"encrypted_value","NewPassword":"encrypted_value"}
        /// </summary>
        [Required]
        [Description("JSON string with OldPassword and NewPassword fields")]
        public string data { get; set; } = string.Empty;

        /// <summary>
        /// Company ID
        /// </summary>
        [Required]
        public int Company_ID { get; set; }

        /// <summary>
        /// Branch ID
        /// </summary>
        [Required]
        public int Branch { get; set; }

        /// <summary>
        /// Menu ID for audit purposes
        /// </summary>
        [Required]
        public int MenuID { get; set; }

        /// <summary>
        /// Timestamp when user logged in
        /// </summary>
        [Required]
        public DateTime LoggedINDateTime { get; set; }

        /// <summary>
        /// Database connection string (from gateway configuration)
        /// </summary>
        [Required]
        public string ConnString { get; set; } = string.Empty;

        /// <summary>
        /// Log exception flag (from gateway configuration)
        /// </summary>
        [Required]
        public int LogException { get; set; }
    }
    #endregion

    #region HTTP Response DTOs (Local to PBC.CoreService)
    /// <summary>
    /// Local DTO for deserializing BCrypt service responses from PBC.UtilityService
    /// This is a local copy to maintain microservice isolation
    /// </summary>
    /// <typeparam name="T">Type of data returned</typeparam>
    public class UtilityServiceBCryptResponse<T>
    {
        /// <summary>
        /// Indicates if the operation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// The result data
        /// </summary>
        public T? Data { get; set; }

        /// <summary>
        /// Error message if operation failed
        /// </summary>
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// Local DTO for deserializing Extension Methods service responses from PBC.UtilityService
    /// This is a local copy to maintain microservice isolation
    /// </summary>
    /// <typeparam name="T">Type of data returned</typeparam>
    public class UtilityServiceExtensionMethodsResponse<T>
    {
        /// <summary>
        /// Indicates if the operation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// The result data
        /// </summary>
        public T? Data { get; set; }

        /// <summary>
        /// Error message if operation failed
        /// </summary>
        public string? ErrorMessage { get; set; }
    }
    #endregion
}
