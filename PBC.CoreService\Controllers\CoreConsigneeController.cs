using Microsoft.AspNetCore.Mvc;
using PBC.CoreService.Services;
using PBC.CoreService.Utilities.DTOs;

namespace PBC.CoreService.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CoreConsigneeController : ControllerBase
    {
        private readonly ICoreConsigneeServices _coreConsigneeServices;
        private readonly ILogger<CoreConsigneeController> _logger;

        public CoreConsigneeController(ICoreConsigneeServices coreConsigneeServices, ILogger<CoreConsigneeController> logger)
        {
            _coreConsigneeServices = coreConsigneeServices;
            _logger = logger;
        }

        #region LoadBranchDropdown
        /// <summary>
        /// Load branch dropdown data
        /// </summary>
        /// <param name="request">LoadBranchDropdown request with connection string</param>
        /// <returns>Branch dropdown data</returns>
        [HttpPost("load-branch-dropdown")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> LoadBranchDropdown([FromBody] LoadBranchDropdownRequestWithConfig request)
        {
            try
            {
                _logger.LogInformation("POST /api/coreconsignee/load-branch-dropdown");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var loadRequest = new LoadBranchDropdownList
                {
                    Company_ID = request.Company_ID,
                    Branch = request.Branch
                };

                var result = await _coreConsigneeServices.LoadBranchDropdown(loadRequest, request.ConnString, request.LogException);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading branch dropdown");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while loading branch dropdown");
            }
        }
        #endregion

        #region Select
        /// <summary>
        /// Select consignee data with pagination and filtering
        /// </summary>
        /// <param name="request">Select request with connection string</param>
        /// <returns>Paginated consignee data</returns>
        [HttpPost("select")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Select([FromBody] SelectConsigneeRequestWithConfig request)
        {
            try
            {
                _logger.LogInformation("POST /api/coreconsignee/select");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _coreConsigneeServices.Select(
                    request.ConnString,
                    request.SelectConsigneeObj,
                    request.Sidx,
                    request.Rows,
                    request.Page,
                    request.Sord,
                    request.Search,
                    request.Nd,
                    request.Filters,
                    request.Advnce,
                    request.AdvanceFilter
                );

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error selecting consignee data");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while selecting consignee data");
            }
        }
        #endregion

        #region Save
        /// <summary>
        /// Save consignee data
        /// </summary>
        /// <param name="request">Save request with connection string</param>
        /// <returns>Save result</returns>
        [HttpPost("save")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Save([FromBody] SaveConsigneeRequestWithConfig request)
        {
            try
            {
                _logger.LogInformation("POST /api/coreconsignee/save");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _coreConsigneeServices.Save(request.SaveObj, request.ConnString, request.LogException);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving consignee data");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while saving consignee data");
            }
        }
        #endregion

        #region CheckConsignee
        /// <summary>
        /// Check consignee existence
        /// </summary>
        /// <param name="request">CheckConsignee request with connection string</param>
        /// <returns>Check result</returns>
        [HttpPost("check-consignee")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> CheckConsignee([FromBody] CheckConsigneeRequestWithConfig request)
        {
            try
            {
                _logger.LogInformation("POST /api/coreconsignee/check-consignee");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _coreConsigneeServices.CheckConsignee(request.CheckConsigneeObj, request.ConnString, request.LogException);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking consignee");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while checking consignee");
            }
        }
        #endregion

        #region CheckConsigneeAddress
        /// <summary>
        /// Check consignee address existence
        /// </summary>
        /// <param name="request">CheckConsigneeAddress request with connection string</param>
        /// <returns>Check result</returns>
        [HttpPost("check-consignee-address")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> CheckConsigneeAddress([FromBody] CheckConsigneeAddressRequestWithConfig request)
        {
            try
            {
                _logger.LogInformation("POST /api/coreconsignee/check-consignee-address");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _coreConsigneeServices.CheckConsigneeAddress(request.CheckConsigneeAddressObj, request.ConnString, request.LogException);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking consignee address");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while checking consignee address");
            }
        }
        #endregion

        #region SelectParticularConsignee
        /// <summary>
        /// Select particular consignee details
        /// </summary>
        /// <param name="request">SelectParticularConsignee request with connection string</param>
        /// <returns>Consignee details</returns>
        [HttpPost("select-particular-consignee")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SelectParticularConsignee([FromBody] SelectParticularConsigneeRequestWithConfig request)
        {
            try
            {
                _logger.LogInformation("POST /api/coreconsignee/select-particular-consignee");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _coreConsigneeServices.SelectParticularConsignee(request.SelectParticularConsigneeObj, request.ConnString, request.LogException);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error selecting particular consignee");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while selecting particular consignee");
            }
        }
        #endregion

        #region Delete
        /// <summary>
        /// Delete consignee data
        /// </summary>
        /// <param name="request">Delete request with connection string</param>
        /// <returns>Delete result</returns>
        [HttpPost("delete")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Delete([FromBody] DeleteConsigneeRequestWithConfig request)
        {
            try
            {
                _logger.LogInformation("POST /api/coreconsignee/delete");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _coreConsigneeServices.Delete(request.DeleteObj, request.ConnString, request.LogException);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting consignee");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while deleting consignee");
            }
        }
        #endregion

        #region UpdateLocale
        /// <summary>
        /// Update consignee locale data
        /// </summary>
        /// <param name="request">UpdateLocale request with connection string</param>
        /// <returns>Update result</returns>
        [HttpPost("update-locale")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateLocale([FromBody] UpdateLocaleRequestWithConfig request)
        {
            try
            {
                _logger.LogInformation("POST /api/coreconsignee/update-locale");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _coreConsigneeServices.UpdateLocale(request.UpdateLocaleObj, request.ConnString, request.LogException);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating locale");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while updating locale");
            }
        }
        #endregion

        #region CheckConsigneeLocale
        /// <summary>
        /// Check consignee locale existence
        /// </summary>
        /// <param name="request">CheckConsigneeLocale request with connection string</param>
        /// <returns>Check result</returns>
        [HttpPost("check-consignee-locale")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> CheckConsigneeLocale([FromBody] CheckConsigneeLocaleRequestWithConfig request)
        {
            try
            {
                _logger.LogInformation("POST /api/coreconsignee/check-consignee-locale");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _coreConsigneeServices.CheckConsigneeLocale(request.CheckConsigneeLocaleObj, request.ConnString, request.LogException);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking consignee locale");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while checking consignee locale");
            }
        }
        #endregion

        #region CheckConsigneeAddressLocale
        /// <summary>
        /// Check consignee address locale existence
        /// </summary>
        /// <param name="request">CheckConsigneeAddressLocale request with connection string</param>
        /// <returns>Check result</returns>
        [HttpPost("check-consignee-address-locale")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> CheckConsigneeAddressLocale([FromBody] CheckConsigneeAddressLocaleRequestWithConfig request)
        {
            try
            {
                _logger.LogInformation("POST /api/coreconsignee/check-consignee-address-locale");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _coreConsigneeServices.CheckConsigneeAddressLocale(request.CheckConsigneeAddressLocaleObj, request.ConnString, request.LogException);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking consignee address locale");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while checking consignee address locale");
            }
        }
        #endregion

        #region CheckWareHouse
        /// <summary>
        /// Check warehouse existence
        /// </summary>
        /// <param name="request">CheckWareHouse request with connection string</param>
        /// <returns>Check result</returns>
        [HttpPost("check-warehouse")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> CheckWareHouse([FromBody] CheckWareHouseRequestWithConfig request)
        {
            try
            {
                _logger.LogInformation("POST /api/coreconsignee/check-warehouse");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _coreConsigneeServices.CheckWareHouse(request.CheckWareHouseObj, request.ConnString, request.LogException);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking warehouse");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while checking warehouse");
            }
        }
        #endregion

        #region Export
        /// <summary>
        /// Export consignee data
        /// </summary>
        /// <param name="request">Export request with connection string</param>
        /// <returns>Export result</returns>
        [HttpPost("export")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Export([FromBody] ExportConsigneeRequestWithConfig request)
        {
            try
            {
                _logger.LogInformation("POST /api/coreconsignee/export");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _coreConsigneeServices.Export(
                    request.ExportObj,
                    request.ConnString,
                    request.LogException,
                    request.Filter,
                    request.AdvanceFilter,
                    request.Sidx,
                    request.Sord
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting consignee data");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while exporting consignee data");
            }
        }
        #endregion
    }
}
