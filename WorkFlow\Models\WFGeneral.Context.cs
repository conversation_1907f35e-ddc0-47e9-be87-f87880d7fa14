﻿//------------------------------------------------------------------------------
// <auto-generated>
//    This code was generated from a template.
//
//    Manual changes to this file may cause unexpected behavior in your application.
//    Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace WorkFlow.Models
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    
    public partial class GenEntities : DbContext
    {
        public GenEntities(string Dbname)
            : base(WFCommon.GetConnection(Dbname, "WFGeneral").ConnectionString)
        {
            this.Configuration.LazyLoadingEnabled = true;
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        public DbSet<WF_Branch> WF_Branch { get; set; }
        public DbSet<WF_Company> WF_Company { get; set; }
        public DbSet<WF_CompanyEmployee> WF_CompanyEmployee { get; set; }
        public DbSet<WF_Email> WF_Email { get; set; }
        public DbSet<WF_Menu> WF_Menu { get; set; }
        public DbSet<WF_MenuLocale> WF_MenuLocale { get; set; }
        public DbSet<WF_Module> WF_Module { get; set; }
        public DbSet<WF_ModuleLocale> WF_ModuleLocale { get; set; }
        public DbSet<WF_Object> WF_Object { get; set; }
        public DbSet<WF_PartyBranchAssociation> WF_PartyBranchAssociation { get; set; }
        public DbSet<WF_PrefixSuffix> WF_PrefixSuffix { get; set; }
        public DbSet<WF_RefMasterDetail> WF_RefMasterDetail { get; set; }
        public DbSet<WF_Role> WF_Role { get; set; }
        public DbSet<WF_RoleObject> WF_RoleObject { get; set; }
        public DbSet<WF_Sms> WF_Sms { get; set; }
        public DbSet<WF_User> WF_User { get; set; }
        public DbSet<WF_UserRole> WF_UserRole { get; set; }
        public DbSet<WF_EmployeeBranch> WF_EmployeeBranch { get; set; }
        public DbSet<WF_UserLocale> WF_UserLocale { get; set; }
    }
}
