﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Web;
using System.Web.Http;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreTaxStructureMappingController : ApiController
    {
        #region Select vinay n 23/9/24
        /// <summary>
        /// Select
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        [Route("api/CoreTaxStructureMapping/SelAllTaxMapping")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelAllTaxMapping([FromBody] SelAllTaxMappingList obj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreTaxStructureMappingServices.SelAllTaxMapping(obj, connstring, LogException, _search, filters, advnceFilters, advnce, sidx, sord, page, rows);


            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion
        #region Save vinay n 25/9/24
        /// <summary>
        /// Save
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        [Route("api/CoreTaxStructureMapping/Save")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public void Save([FromBody] SaveTaxMappingList obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                CoreTaxStructureMappingServices.Save(obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }


        }
        #endregion
        #region Delete vinay n 25/9/24
        /// <summary>
        /// Delete
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        [Route("api/CoreTaxStructureMapping/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteTaxMappingList obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreTaxStructureMappingServices.Delete(obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion
        #region CheckMapping vinay n 25/9/24
        /// <summary>
        /// CheckMapping
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        [Route("api/CoreTaxStructureMapping/CheckMapping")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckMapping([FromBody] CheckMappingList obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreTaxStructureMappingServices.CheckMapping(obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
    }
}