using PBC.UtilityService.Utilities;
using PBC.UtilityService.Utilities.DTOs;

namespace PBC.UtilityService.Services
{
    public class BCryptService : IBCryptService
    {
        private readonly ILogger<BCryptService> _logger;

        public BCryptService(ILogger<BCryptService> logger)
        {
            _logger = logger;
        }

        /// <inheritdoc/>
        public async Task<BCryptResponse<string>> GenerateDMSPasswordAsync(GenerateDMSPasswordRequest request)
        {
            try
            {
                _logger.LogInformation("Generating DMS password hash");

                await Task.Delay(1); // Simulate async operation

                // Generate password hash using the utility method
                var hashedPassword = Common.GenerateDMSPassword(request.UserPassword);
                
                return new BCryptResponse<string>
                {
                    Success = true,
                    Data = hashedPassword
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating DMS password hash");
                return new BCryptResponse<string>
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <inheritdoc/>
        public async Task<BCryptResponse<bool>> CheckPasswordWithDBAsync(CheckPasswordRequest request)
        {
            try
            {
                _logger.LogInformation("Checking password against database hash");

                await Task.Delay(1); // Simulate async operation

                // Check password using the utility method
                var passwordMatches = Common.CheckPasswordWithDB(request.HashedPwdFromDatabase, request.UserEnteredPassword);
                
                return new BCryptResponse<bool>
                {
                    Success = true,
                    Data = passwordMatches
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking password against database hash");
                return new BCryptResponse<bool>
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }
    }
}
