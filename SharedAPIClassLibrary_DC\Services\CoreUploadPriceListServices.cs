﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using LS = SharedAPIClassLibrary_AMERP.Utilities;



namespace SharedAPIClassLibrary_AMERP
{
    public class CoreUploadPriceListServices
    {

        public static int? ErrorCode = null;



        #region ::: UploadFileParts Uday Kumar J B 19-08-2024:::
        /// <summary>
        /// To Upload File Parts
        /// </summary>
        /// 
        public static HttpResponseMessage UploadFileParts(IFormFile partsPriceFile, string connString, UploadFilePartsCoreUploadPriceList UploadFilePartsCoreUploadPriceListobj)
        {
            HttpResponseMessage response;
            DataTable dt = null;
            string fileExtension = Path.GetExtension(partsPriceFile.FileName).ToLower();
            int supplierID = Convert.ToInt32(UploadFilePartsCoreUploadPriceListobj.supplierID);
            DateTime effectiveFrom = Convert.ToDateTime(UploadFilePartsCoreUploadPriceListobj.EffectiveFrom);
            // Check file extension validity
            if (fileExtension != ".xls" && fileExtension != ".xlsx" && fileExtension != ".csv")
            {
                return new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Invalid file format.")
                };
            }
            else
            {
                ErrorCode = 3;
            }
            try
            {
                // Read file content into a DataTable
                using (var stream = new MemoryStream())
                {
                    partsPriceFile.CopyTo(stream);
                    dt = Common.ExcelReader(stream, partsPriceFile.FileName);

                    // Validate required columns
                    if (!ValidateColumns(dt))
                    {
                        return new HttpResponseMessage(HttpStatusCode.BadRequest)
                        {
                            Content = new StringContent("Invalid column headers.")
                        };
                    }

                    else { ErrorCode = 4; }
                }

                // Process and validate data
                EncapPartsClass partClass = ValidateParts(dt, supplierID, effectiveFrom, connString, UploadFilePartsCoreUploadPriceListobj);

                if (partClass.PartsList.Count > 0)
                {
                    // Save validated parts data
                    SavePartsPrice(partClass.PartsList, connString, UploadFilePartsCoreUploadPriceListobj);
                }

                // Handle errors if present
                if (partClass.HasError)
                {
                    response = new HttpResponseMessage(HttpStatusCode.OK)
                    {
                        Content = new StringContent(partClass.ErrorString)
                    };
                    response.Content.Headers.ContentDisposition = new System.Net.Http.Headers.ContentDispositionHeaderValue("attachment")
                    {
                        FileName = "ErrorFile.xls"
                    };
                    response.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/vnd.ms-excel");
                    return response;
                }

                ErrorCode = partClass.HasError && partClass.PartsList.Count > 0 ? 1 : !partClass.HasError && partClass.PartsList.Count > 0 ? 2 : 0;
                //  gbl.InsertGPSDetails(Convert.ToInt32(UploadFilePartsCoreUploadPriceListobj.Company_ID.ToString()), Convert.ToInt32(Session["Branch"]), User.User_ID, Common.GetObjectID("CoreUploadPriceList"), 0, 0, 0, "Uploaded Price List", false, Convert.ToInt32(UploadFilePartsCoreUploadPriceListobj.MenuID), Convert.ToDateTime(UploadFilePartsCoreUploadPriceListobj.LoggedINDateTime));

                response = new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new StringContent("File uploaded and processed successfully.")
                };
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, $"{ex.GetType().FullName}: {ex.Message}", ex.TargetSite.ToString(), ex.StackTrace);
                response = new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("An error occurred during file upload.")
                };
            }

            return response;
        }
        // Column validation method
        private static bool ValidateColumns(DataTable dt)
        {
            string[] requiredColumns = { "PREFIX", "PARTNUMBER", "SUPPLIER PRICE", "STANDARD PACKING QUANTITY", "SUPPLIER PARTS PREFIX", "SUPPLIER PARTS NUMBER", "MANUFACTURER WARRANTY (IN DAYS)", "LIST PRICE", "FORMULA COST PRICE", "MRP" };
            foreach (var col in requiredColumns)
            {
                if (!dt.Columns.Contains(col))
                    return false;
            }
            return true;
        }


        #endregion


        #region ::: UploadFile Uday Kumar J B 19-08-2024:::
        /// <summary>
        /// To Upload File
        /// </summary>
        /// 
        public static HttpResponseMessage UploadFile(IFormFile partsPrice, string connString, UploadFilePartsCoreUploadPriceList UploadFilePartsCoreUploadPriceListobj)
        {
            string fileExtension = Path.GetExtension(partsPrice.FileName);
            int supplierID = Convert.ToInt32(UploadFilePartsCoreUploadPriceListobj.supplierID);
            DateTime effectiveFrom = Convert.ToDateTime(UploadFilePartsCoreUploadPriceListobj.EffectiveFrom);
            DataTable dt = null;

            if (fileExtension == ".xls" || fileExtension == ".xlsx" || fileExtension == ".csv")
            {
                dt = Common.ExcelReader((Stream)partsPrice, partsPrice.FileName);

                // Validate the column names
                if (dt.Columns[0].Caption.ToUpper().Trim() == "PREFIX" &&
                    dt.Columns[1].Caption.ToUpper().Trim() == "PARTNUMBER" &&
                    dt.Columns[2].Caption.ToUpper().Trim() == "SUPPLIER PRICE" &&
                    dt.Columns[3].Caption.ToUpper().Trim() == "STANDARD PACKING QUANTITY" &&
                    dt.Columns[4].Caption.ToUpper().Trim() == "SUPPLIER PARTS PREFIX" &&
                    dt.Columns[5].Caption.ToUpper().Trim() == "SUPPLIER PARTS NUMBER" &&
                    dt.Columns[6].Caption.ToUpper().Trim() == "MANUFACTURER WARRANTY (IN DAYS)" &&
                    dt.Columns[7].Caption.ToUpper().Trim() == "LIST PRICE" &&
                    dt.Columns[8].Caption.ToUpper().Trim() == "FORMULA COST PRICE" &&
                    dt.Columns[9].Caption.ToUpper().Trim() == "MRP")
                {
                    if (dt != null)
                    {
                        EncapPartsClass partClass = ValidateParts(dt, supplierID, effectiveFrom, connString, UploadFilePartsCoreUploadPriceListobj);

                        if (partClass.PartsList.Count > 0)
                        {
                            SavePartsSupplierPrice(partClass.PartsList, connString, UploadFilePartsCoreUploadPriceListobj);
                        }

                        if (partClass.HasError)
                        {
                            var errorFile = GenerateErrorFile(partClass.ErrorString);
                            return new HttpResponseMessage
                            {
                                Content = new ByteArrayContent(errorFile),
                                StatusCode = System.Net.HttpStatusCode.OK,
                                Headers =
                        {
                            { "Content-Disposition", "attachment; filename=ErrorFile.xls" },
                            { "Content-Type", "application/excel" }
                        }
                            };
                        }

                        ErrorCode = partClass.HasError && partClass.PartsList.Count > 0 ? 1 :
                                    !partClass.HasError && partClass.PartsList.Count > 0 ? 2 : 0;

                        // Insert GPS details to database
                        //  gbl.InsertGPSDetails(Convert.ToInt32(UploadFilePartsCoreUploadPriceListobj.Company_ID.ToString()), Convert.ToInt32(UploadFilePartsCoreUploadPriceListobj.Branch), UploadFilePartsCoreUploadPriceListobj.User_ID, Common.GetObjectID("CoreUploadPriceList"), 0, 0, 0, "Uploaded Price List", false, Convert.ToInt32(UploadFilePartsCoreUploadPriceListobj.MenuID), Convert.ToDateTime(UploadFilePartsCoreUploadPriceListobj.LoggedINDateTime));
                    }
                }
                else
                {
                    ErrorCode = 4; // Invalid column names
                }
            }
            else
            {
                ErrorCode = 3; // Invalid file extension
            }

            // Return an empty response with the error code
            return new HttpResponseMessage
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest,
                Content = new StringContent($"Error Code: {ErrorCode}")
            };
        }

        private static byte[] GenerateErrorFile(string errorString)
        {
            // Convert the error string to a byte array for the file download
            using (MemoryStream ms = new MemoryStream())
            {
                using (StreamWriter writer = new StreamWriter(ms))
                {
                    writer.Write(errorString);
                    writer.Flush();
                    return ms.ToArray();
                }
            }
        }


        #endregion


        #region ::: SelSupplier Uday Kumar J B 19-08-2024:::
        /// <summary>
        /// To Sel Supplier
        /// </summary>
        /// 
        public static IActionResult SelSupplier(string connString, SelSupplierList SelSupplierobj)
        {
            int companyId = Convert.ToInt32(SelSupplierobj.Company_ID);
            int branchId = Convert.ToInt32(SelSupplierobj.Branch);
            bool filterBasedOnCompany;


            // Check if we need to filter based on company
            using (SqlConnection connection = new SqlConnection(connString))
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand("Up_Sel_AM_ERP_CheckFilterPartyBasedonCompany", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@CompanyID", companyId);
                    SqlParameter filterParam = new SqlParameter("@Filter", SqlDbType.Bit) { Direction = ParameterDirection.Output };
                    command.Parameters.Add(filterParam);

                    command.ExecuteNonQuery();
                    filterBasedOnCompany = Convert.ToBoolean(filterParam.Value);
                }
            }

            // Fetch suppliers
            List<dynamic> supplierArray = new List<dynamic>();
            using (SqlConnection connection = new SqlConnection(connString))
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand("Up_Sel_AM_ERP_GetSuppliers", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@CompanyID", companyId);
                    command.Parameters.AddWithValue("@UserLanguageID", SelSupplierobj.userLanguageID);
                    command.Parameters.AddWithValue("@GeneralLanguageID", SelSupplierobj.generalLanguageID);
                    command.Parameters.AddWithValue("@Filter", filterBasedOnCompany);

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var supplier = new
                            {
                                Party_ID = reader["Party_ID"],
                                Party_Name = reader["Party_Name"]
                            };
                            supplierArray.Add(supplier);
                        }
                    }
                }
            }

            return new JsonResult(supplierArray);
        }


        #endregion


        #region ::: ValidateParts Uday Kumar J B 19-08-2024:::
        /// <summary>
        /// To Validate Parts
        /// </summary>
        /// 
        public static EncapPartsClass ValidateParts(DataTable dt, int supplierID, DateTime effectiveFrom, string connString, UploadFilePartsCoreUploadPriceList UploadFilePartsCoreUploadPriceListobj)
        {
            List<PriceUploadParts> partsList = new List<PriceUploadParts>();
            StringBuilder sb = new StringBuilder();
            sb.Append("<table border='1'><thead><tr style='font-weight:bold'><td>Prefix</td><td>PartNumber</td><td>Supplier Price</td><td>Standard Packing Quantity</td><td>Supplier Parts Prefix</td><td>Supplier Parts Number</td><td>Manufacturer Warranty</td><td>List Price</td><td>Formula Cost Price</td><td>MRP</td><td>Remarks</td></tr></thead>");
            bool IsError = false;

            // Get CurrencyID using ADO.NET
            int currencyID = 0;
            string UserCulture = " ";
            int Company_ID = 0;
            using (SqlConnection conn = new SqlConnection(connString))
            {
                conn.Open();
                using (SqlCommand cmd = new SqlCommand("sp_GetCurrencyID", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@SupplierID", supplierID);

                    currencyID = Convert.ToInt32(cmd.ExecuteScalar());
                }
            }
            if (UploadFilePartsCoreUploadPriceListobj.UserCulture != null)
            {
                UserCulture = UploadFilePartsCoreUploadPriceListobj.UserCulture.ToString();
            }
            else
            {
                UserCulture = UploadFilePartsCoreUploadPriceListobj.UserCulture.ToString();
            }
            if (UploadFilePartsCoreUploadPriceListobj.Company_ID != null)
            {
                Company_ID = UploadFilePartsCoreUploadPriceListobj.Company_ID;
            }
            else
            {
                Company_ID = UploadFilePartsCoreUploadPriceListobj.Company_ID;
            }
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                IsError = false;
                string Remarks = string.Empty;
                PriceUploadParts rowObj = null;

                if (dt.Rows[i][0] == null || dt.Rows[i][0].ToString() == "")
                {
                    IsError = true;
                    Remarks += CommonFunctionalities.GetResourceString(UserCulture.ToString(), "PartPrefixisBlank").ToString() + "<br/>";
                }

                if (dt.Rows[i][1] == null || dt.Rows[i][1].ToString() == "")
                {
                    IsError = true;
                    Remarks += CommonFunctionalities.GetResourceString(UserCulture.ToString(), "PartNumberisBlank").ToString() + "<br/>";
                }

                if (dt.Rows[i][0].ToString() != "" && dt.Rows[i][1].ToString() != "")
                {
                    string prefix = dt.Rows[i][0].ToString();
                    string partNumber = dt.Rows[i][1].ToString();

                    // Check if Part Exists using ADO.NET
                    bool partExists = false;
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        conn.Open();
                        using (SqlCommand cmd = new SqlCommand("sp_CheckIfPartExists", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Prefix", prefix);
                            cmd.Parameters.AddWithValue("@PartNumber", partNumber);

                            partExists = Convert.ToInt32(cmd.ExecuteScalar()) > 0;
                        }
                    }

                    if (!partExists)
                    {
                        IsError = true;
                        Remarks += CommonFunctionalities.GetResourceString(UserCulture.ToString(), "PartNumbernotfound").ToString() + "<br/>";
                    }
                    else
                    {
                        rowObj = SelectPart(prefix, partNumber, connString, Company_ID);
                        if (rowObj == null)
                        {
                            IsError = true;
                            Remarks += CommonFunctionalities.GetResourceString(UserCulture.ToString(), "PartNumbernotfound").ToString() + "<br/>";
                        }
                        else
                        {
                            rowObj.supplierID = supplierID;
                            rowObj.effectiveFrom = effectiveFrom;
                            rowObj.currencyID = currencyID;
                        }
                        if (dt.AsEnumerable().Where(a => a[1].ToString() == rowObj.PartNumber && a[0].ToString() == rowObj.PartPrefix).Count() > 1)
                        {
                            Remarks = Remarks + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "Duplicatepartnumber").ToString() + "<br/>";
                            IsError = true;
                        }
                        if (dt.Rows[i][2] == null || dt.Rows[i][2].ToString() == "")
                        {
                            if ((dt.Rows[i][9].ToString() != "") && (dt.Rows[i][7].ToString() != ""))
                            { }
                            else
                            {
                                IsError = true;
                                Remarks = Remarks + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "Supplierpriceisempty").ToString() + "<br/>";
                            }
                        }
                        else
                        {
                            if (!IsNumber(dt.Rows[i][2]))
                            {
                                Remarks = Remarks + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "Invalidsupplierprice").ToString() + "<br/>";
                                IsError = true;
                            }
                            else
                            {
                                if (Convert.ToDecimal(dt.Rows[i][2].ToString().Count()) > 13)
                                {
                                    Remarks = Remarks + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "SupplierPriceisbeyondacceptablelimit").ToString() + "<br/>";
                                    IsError = true;
                                }
                                else
                                {
                                    rowObj.supplierPrice = Convert.ToDecimal(dt.Rows[i][2]);
                                }
                            }
                        }
                        if (dt.Rows[i][3] == null || dt.Rows[i][3].ToString() == "")
                        {
                            if (dt.Rows[i][2] != null || dt.Rows[i][2].ToString() != "")
                            {
                                Remarks = Remarks + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "StandardPackingQuantityIsEmpty").ToString() + "<br/>";
                                IsError = true;
                            }
                        }
                        else
                        {
                            if (!IsNumber(dt.Rows[i][3]))
                            {
                                Remarks = Remarks + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "InvalidStandardPackingQuantity").ToString() + "<br/>";
                                IsError = true;
                            }
                            else
                            {
                                if (Convert.ToDecimal(dt.Rows[i][3].ToString().Count()) > 13)
                                {
                                    Remarks = Remarks + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "StandardPackingQuantityisbeyondacceptablelimit").ToString() + "<br/>";
                                    IsError = true;
                                }
                                else
                                {
                                    rowObj.standardPackingQuantity = Convert.ToDecimal(dt.Rows[i][3]);
                                }
                            }
                        }

                        if (dt.Rows[i][4] == null || dt.Rows[i][4].ToString() == "")
                        {
                            rowObj.supplierPartPrefix = rowObj.supplierPartPrefix == null ? string.Empty : rowObj.supplierPartPrefix;
                        }
                        else
                        {
                            rowObj.supplierPartPrefix = dt.Rows[i][4].ToString();
                        }
                        if (dt.Rows[i][5] == null || dt.Rows[i][5].ToString() == "")
                        {
                            rowObj.supplierPartNumber = rowObj.supplierPartNumber == null ? string.Empty : rowObj.supplierPartNumber;
                        }
                        else
                        {
                            rowObj.supplierPartNumber = dt.Rows[i][5].ToString();
                        }

                        if (dt.Rows[i][6].ToString() != "")
                        {
                            if (!IsNumber(dt.Rows[i][6]))
                            {
                                Remarks = Remarks + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "InvalidManufacturerWarranty").ToString() + "<br/>";
                                IsError = true;
                            }
                            else
                            {
                                if (Convert.ToInt64(dt.Rows[i][6].ToString()) > Int32.MaxValue)
                                {
                                    Remarks = Remarks + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "ManufacturerWarrantyisbeyondacceptablelimit").ToString() + "<br/>";
                                    IsError = true;
                                }
                                else
                                {
                                    rowObj.ManufacturerWarranty = Convert.ToInt32(dt.Rows[i][6].ToString());
                                }
                            }
                        }
                        if (dt.Rows[i][7] != null && dt.Rows[i][7].ToString() != "")
                        {
                            if (dt.Rows[i][9] == null || dt.Rows[i][9].ToString() == "")
                            {
                                Remarks = Remarks + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "FormulaCostPriceisEmpty").ToString() + "<br/>";
                                IsError = true;
                            }
                            else
                            {
                                if (!IsNumber(dt.Rows[i][7]))
                                {
                                    Remarks = Remarks + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "InvalidListPrice").ToString() + "<br/>";
                                    IsError = true;
                                }
                                else
                                {
                                    if (Convert.ToDecimal(dt.Rows[i][7].ToString().Count()) > 13)
                                    {
                                        Remarks = Remarks + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "ListPriceisbeyondacceptablelimit").ToString() + "<br/>";
                                        IsError = true;
                                    }
                                    else
                                    {
                                        rowObj.ListPrice = Convert.ToDecimal(dt.Rows[i][7]);
                                    }
                                }
                            }
                        }

                        if (dt.Rows[i][8] != null && dt.Rows[i][8].ToString() != "")
                        {
                            if (!IsNumber(dt.Rows[i][8]))
                            {
                                Remarks = Remarks + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "InvalidMRP").ToString() + "<br/>";
                                IsError = true;
                            }
                            else
                            {
                                if (Convert.ToDecimal(dt.Rows[i][8].ToString().Count()) > 13)
                                {
                                    Remarks = Remarks + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "MRPisbeyondacceptablelimit").ToString() + "<br/>";
                                    IsError = true;
                                }
                                else
                                {
                                    rowObj.MRP = Convert.ToDecimal(dt.Rows[i][8]);
                                }
                            }
                        }
                    }
                }
                else
                {
                    Remarks = CommonFunctionalities.GetResourceString(UserCulture.ToString(), "PartNumberandPartPrefixareblank").ToString() + "<br/>";
                    IsError = true;
                }
                if (IsError)
                {
                    sb.Append("<tr><td>" + dt.Rows[i][0].ToString() + "</td><td>" + dt.Rows[i][1].ToString() + "</td><td>" + dt.Rows[i][2].ToString() + "</td><td>" + dt.Rows[i][3].ToString() + "</td><td>" + dt.Rows[i][4].ToString() + "</td><td>" + dt.Rows[i][5].ToString() + "</td><td>" + dt.Rows[i][6].ToString() + "</td><td>" + dt.Rows[i][7].ToString() + "</td><td>" + dt.Rows[i][8].ToString() + "</td><td>" + dt.Rows[i][9].ToString() + "</td><td>" + Remarks + "</td></tr>");
                }
                else
                {
                    partsList.Add(rowObj);
                }
            }
            sb.Append("</table>");
            EncapPartsClass resultObj = new EncapPartsClass();
            resultObj.PartsList = partsList;
            resultObj.HasError = IsError;
            resultObj.ErrorString = sb.ToString();
            return resultObj;
        }

        private static bool IsNumber(object number)
        {
            try
            {
                Convert.ToDecimal(number);
                return true;
            }
            catch (FormatException fx)
            {
                return false;
            }
        }
        #endregion


        #region ::: SelectPart Uday Kumar J B 19-08-2024 :::
        /// <summary>
        /// To Select Part
        /// </summary>
        /// 

        public static PriceUploadParts SelectPart(string partPrefix, string partNumber, string connString, int Company_ID)
        {
            PriceUploadParts partUploadRow = null;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    int companyID = Convert.ToInt32(Company_ID);

                    using (SqlCommand cmd = new SqlCommand("dbo.Up_Sel_AM_ERP_SelectPart", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@PartPrefix", partPrefix);
                        cmd.Parameters.AddWithValue("@PartNumber", partNumber);
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                partUploadRow = new PriceUploadParts
                                {
                                    partID = reader.GetInt32(reader.GetOrdinal("Parts_ID")),
                                    PartPrefix = reader.GetString(reader.GetOrdinal("Parts_PartPrefix")),
                                    PartNumber = reader.GetString(reader.GetOrdinal("Parts_PartsNumber")),
                                    companyID = companyID
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return partUploadRow;
        }

        #endregion


        #region ::: SavePartsPrice Uday Kumar J B 19-08-2024 :::
        /// <summary>
        /// To Save Parts Price
        /// </summary>
        /// 
        public static void SavePartsPrice(List<PriceUploadParts> partsList, string connString, UploadFilePartsCoreUploadPriceList UploadFilePartsCoreUploadPriceListobj)
        {
            int companyID = Convert.ToInt32(UploadFilePartsCoreUploadPriceListobj.Company_ID);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    foreach (var part in partsList)
                    {
                        try
                        {
                            int partID = part.partID;
                            int supplierID = part.supplierID;
                            DateTime effectiveFrom = part.effectiveFrom;
                            decimal supplierPrice = part.supplierPrice;

                            // Call stored procedure to insert or update supplier details
                            using (SqlCommand cmd = new SqlCommand("Up_Ins_AM_ERP_SaveSupplierDetails", connection))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@Parts_ID", partID);
                                cmd.Parameters.AddWithValue("@Company_ID", part.companyID);
                                cmd.Parameters.AddWithValue("@Currency_ID", part.currencyID);
                                cmd.Parameters.AddWithValue("@SupplierPrice", part.supplierPrice);
                                cmd.Parameters.AddWithValue("@Supplier_ID", part.supplierID);
                                cmd.Parameters.AddWithValue("@SupplierPartNumber", part.supplierPartNumber);
                                cmd.Parameters.AddWithValue("@SupplierPartPrefix", part.supplierPartPrefix);
                                cmd.Parameters.AddWithValue("@Effectivefrom", effectiveFrom);
                                cmd.Parameters.AddWithValue("@StandardPackingQuantity", part.standardPackingQuantity);
                                cmd.Parameters.AddWithValue("@ManufacturerWarranty", part.ManufacturerWarranty);

                                cmd.ExecuteNonQuery();
                            }

                            // Get parts category ID
                            int? partsCategoryID = null;
                            using (SqlCommand cmd = new SqlCommand("Up_Sel_AM_ERP_GetPartsCategoryID", connection))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@Parts_ID", partID);

                                partsCategoryID = (int?)cmd.ExecuteScalar();
                            }

                            // Update parts price detail if parts category ID is not null
                            if (partsCategoryID != null)
                            {
                                decimal conversionFactor = 0.00M;
                                decimal profitFactor = 0.00M;
                                decimal mrpFactor = 0.00M;

                                // Get category definition
                                using (SqlCommand cmd = new SqlCommand("Up_Sel_AM_ERP_GetCategoryDefinition", connection))
                                {
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.Parameters.AddWithValue("@PartsCategory_ID", partsCategoryID.Value);
                                    cmd.Parameters.AddWithValue("@Company_ID", companyID);

                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        if (reader.Read())
                                        {
                                            conversionFactor = reader["ConversionFactor"] != DBNull.Value ? (decimal)reader["ConversionFactor"] : 0.00M;
                                            profitFactor = reader["ProfitValue"] != DBNull.Value ? (decimal)reader["ProfitValue"] : 0.00M;
                                            mrpFactor = reader["MRPFactor"] != DBNull.Value ? (decimal)reader["MRPFactor"] : 0.00M;
                                        }
                                    }
                                }

                                // Calculate prices
                                decimal formulaCostPrice = supplierPrice * conversionFactor;
                                decimal listPrice = formulaCostPrice * profitFactor;
                                decimal mrp = listPrice * mrpFactor;

                                // Call stored procedure to insert or update parts price details
                                using (SqlCommand cmd = new SqlCommand("Up_Ins_AM_ERP_SavePartsPriceDetails", connection))
                                {
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.Parameters.AddWithValue("@Company_ID", companyID);
                                    cmd.Parameters.AddWithValue("@Parts_ID", partID);
                                    cmd.Parameters.AddWithValue("@EffectiveFrom", effectiveFrom);
                                    cmd.Parameters.AddWithValue("@FormulaCostPrice", formulaCostPrice);
                                    cmd.Parameters.AddWithValue("@ListPrice", listPrice);
                                    cmd.Parameters.AddWithValue("@MRP", mrp);

                                    cmd.ExecuteNonQuery();
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
        }

        #endregion


        #region ::: SavePartsSupplierPrice uday Kumar J B 19-08-2024 :::
        /// <summary>
        /// To Save Parts Supplier Price
        /// </summary>
        /// 
        public static void SavePartsSupplierPrice(List<PriceUploadParts> partsList, string connString, UploadFilePartsCoreUploadPriceList UploadFilePartsCoreUploadPriceobj)
        {
            int Company_ID = Convert.ToInt32(UploadFilePartsCoreUploadPriceobj.Company_ID);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    for (int i = 0; i < partsList.Count; i++)
                    {
                        try
                        {
                            int partID = partsList[i].partID;
                            int supplierID = partsList[i].supplierID;
                            DateTime effectiveFrom = partsList[i].effectiveFrom;
                            decimal? supplierPrice = partsList[i].supplierPrice;
                            decimal? ListPrice = partsList[i].ListPrice;
                            decimal? MRP = partsList[i].MRP;

                            if (supplierPrice != 0 && ListPrice == 0)
                            {
                                using (SqlCommand cmd = new SqlCommand("Up_Ins_AM_ERP_SavePartsSupplierDetail", connection))
                                {
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.Parameters.AddWithValue("@Parts_ID", partID);
                                    cmd.Parameters.AddWithValue("@Company_ID", partsList[i].companyID);
                                    cmd.Parameters.AddWithValue("@Currency_ID", partsList[i].currencyID);
                                    cmd.Parameters.AddWithValue("@SupplierPrice", supplierPrice);
                                    cmd.Parameters.AddWithValue("@Supplier_ID", supplierID);
                                    cmd.Parameters.AddWithValue("@SupplierPartNumber", partsList[i].supplierPartNumber);
                                    cmd.Parameters.AddWithValue("@SupplierPartPrefix", partsList[i].supplierPartPrefix);
                                    cmd.Parameters.AddWithValue("@EffectiveFrom", effectiveFrom);
                                    cmd.Parameters.AddWithValue("@StandardPackingQuantity", partsList[i].standardPackingQuantity);
                                    cmd.Parameters.AddWithValue("@ManufacturerWarranty", partsList[i].ManufacturerWarranty);

                                    cmd.ExecuteNonQuery();
                                }
                            }
                            else if (supplierPrice != 0 && ListPrice != 0)
                            {
                                using (SqlCommand cmdSupplier = new SqlCommand("Up_Ins_AM_ERP_SavePartsPriceDetail", connection))
                                {
                                    cmdSupplier.CommandType = CommandType.StoredProcedure;
                                    cmdSupplier.Parameters.AddWithValue("@Parts_ID", partID);
                                    cmdSupplier.Parameters.AddWithValue("@Company_ID", partsList[i].companyID);
                                    cmdSupplier.Parameters.AddWithValue("@Currency_ID", partsList[i].currencyID);
                                    cmdSupplier.Parameters.AddWithValue("@SupplierPrice", supplierPrice);
                                    cmdSupplier.Parameters.AddWithValue("@Supplier_ID", supplierID);
                                    cmdSupplier.Parameters.AddWithValue("@SupplierPartNumber", partsList[i].supplierPartNumber);
                                    cmdSupplier.Parameters.AddWithValue("@SupplierPartPrefix", partsList[i].supplierPartPrefix);
                                    cmdSupplier.Parameters.AddWithValue("@EffectiveFrom", effectiveFrom);
                                    cmdSupplier.Parameters.AddWithValue("@StandardPackingQuantity", partsList[i].standardPackingQuantity);
                                    cmdSupplier.Parameters.AddWithValue("@ManufacturerWarranty", partsList[i].ManufacturerWarranty);

                                    cmdSupplier.ExecuteNonQuery();
                                }

                                using (SqlCommand cmdPrice = new SqlCommand("usp_SavePartsPriceDetail", connection))
                                {
                                    cmdPrice.CommandType = CommandType.StoredProcedure;
                                    cmdPrice.Parameters.AddWithValue("@Company_ID", Company_ID);
                                    cmdPrice.Parameters.AddWithValue("@Parts_ID", partID);
                                    cmdPrice.Parameters.AddWithValue("@EffectiveFrom", effectiveFrom);
                                    cmdPrice.Parameters.AddWithValue("@FormulaCostPrice", partsList[i].FormulaCostPrice);
                                    cmdPrice.Parameters.AddWithValue("@ListPrice", ListPrice);
                                    cmdPrice.Parameters.AddWithValue("@MRP", MRP);

                                    cmdPrice.ExecuteNonQuery();
                                }
                            }
                            else if (ListPrice != 0 && MRP != 0)
                            {
                                using (SqlCommand cmd = new SqlCommand("usp_SavePartsPriceDetail", connection))
                                {
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                                    cmd.Parameters.AddWithValue("@Parts_ID", partID);
                                    cmd.Parameters.AddWithValue("@EffectiveFrom", effectiveFrom);
                                    cmd.Parameters.AddWithValue("@FormulaCostPrice", partsList[i].FormulaCostPrice);
                                    cmd.Parameters.AddWithValue("@ListPrice", ListPrice);
                                    cmd.Parameters.AddWithValue("@MRP", MRP);

                                    cmd.ExecuteNonQuery();
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
        }


        //public int CheckForError()
        //{
        //    return Convert.ToInt32(Session["ErrorCode"]);
        //}


        #endregion


        #region ::: CoreUploadPriceList List and obj classes uday Kumar J B 19-08-2024 :::
        /// <summary>
        /// CoreUploadPriceList
        /// </summary>

        public class ImportPartsTemplateCoreUploadPriceList
        {
            public List<GNM_User> UserDetails { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }

        }

        public class CheckIFPartExistsCoreUploadPriceListList
        {
            public string partPrefix { get; set; }
            public string partNumber { get; set; }
            public int Company_ID { get; set; }
        }
        public class SelSupplierList
        {
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int userLanguageID { get; set; }
            public int generalLanguageID { get; set; }
        }

        public class UploadFilePartsCoreUploadPriceList
        {
            public int User_ID { get; set; }
            public string PartsPrice { get; set; }
            public int supplierID { get; set; }
            public DateTime EffectiveFrom { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string UserCulture { get; set; }
        }
        #endregion


        #region ::: PriceUploadParts Classes :::
        /// <summary>
        /// PriceUploadParts
        /// </summary>
        /// 

        public class PriceUploadParts
        {
            public int partID { get; set; }
            public int supplierID { get; set; }
            public int currencyID { get; set; }
            public decimal standardPackingQuantity { get; set; }
            public decimal supplierPrice { get; set; }
            public DateTime effectiveFrom { get; set; }
            public string supplierPartPrefix { get; set; }
            public string supplierPartNumber { get; set; }
            public string PartPrefix { get; set; }
            public string PartNumber { get; set; }
            public int companyID { get; set; }
            public bool HasError { get; set; }
            public int ManufacturerWarranty { get; set; }//added by Kavitha for Version2.0 Correction
            public decimal ListPrice { get; set; }
            public decimal FormulaCostPrice { get; set; }
            public decimal MRP { get; set; }
        }
        public class EncapPartsClass
        {
            public List<PriceUploadParts> PartsList { get; set; }
            public bool HasError { get; set; }
            public string ErrorString { get; set; }
        }
        #endregion

    }
}
