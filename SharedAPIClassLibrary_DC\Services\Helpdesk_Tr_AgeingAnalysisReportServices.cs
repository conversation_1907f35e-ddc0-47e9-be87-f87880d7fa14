﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.Utilities.CoreCompanyCalenderMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class Helpdesk_Tr_AgeingAnalysisReportServices
    {
        // Variable Declaration
        public static List<TimeSlot> TimeSlotList = null;
        public static string BranchName = string.Empty;
        //++++++++++++++++++++++++++++++++++++++++

        #region ::: Initial Set Up:::
        /// <summary>
        /// To get initial set up
        /// </summary>
        /// <returns>...</returns>
        public static ActionResult InitialSetup(AgeingAnalysisReport_InitialSetupList OBJ, string connString, int LogException)
        {
            JsonResult jr = null;
            try
            {
                int objectid = Convert.ToInt32(OBJ.ObjectID);
                jr = Common.InitialSetup(objectid, OBJ.User_ID, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jr);
        }
        #endregion


        private static TimeSlotFilter GetTimeSlotFilter(int Hours, int LogException)
        {
            try
            {
                List<TimeSlot> timeSlotList = TimeSlotList.Cast<TimeSlot>().ToList();
                List<int> HourrsList = timeSlotList.Select(S => Convert.ToInt32(S.Hours)).ToList();
                int Position = HourrsList.IndexOf(Hours);
                TimeSlotFilter Filter = new TimeSlotFilter();

                if (HourrsList[0] == Hours)//First
                {
                    Filter.Type = 1; Filter.Min = Hours; Filter.Max = 0;
                    return Filter;
                }
                else if (HourrsList[HourrsList.Count - 1] == Hours)//Last
                {
                    Filter.Type = 2; Filter.Min = 0; Filter.Max = Hours;
                    return Filter;
                }
                else
                {
                    Filter.Type = 3; Filter.Min = HourrsList[Position - 1]; Filter.Max = HourrsList[Position];
                    return Filter;
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new TimeSlotFilter();
        }

        public static List<TimeSlot> CalculateTimeSlot(List<TimeSlot> Timeslot, int Value, int LogException)
        {
            try
            {
                for (int i = 0; i < Timeslot.Count; i++)
                {
                    if (Value >= Convert.ToInt32(Timeslot[Timeslot.Count - 1].Name))
                    {
                        Timeslot[Timeslot.Count - 1].Count++;
                        break;
                    }
                    else if (Value <= Convert.ToInt32(Timeslot[i].Name) - 1)
                    {
                        Timeslot[i].Count++;
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return Timeslot;
        }

        #region LoadBranchDD
        /// <summary>
        /// LoadBranchDD
        /// </summary>
        public static IActionResult LoadBranchDD(LoadBranchDDList OBJ, string connString, int LogException)
        {
            var jsonobj = default(dynamic);
            IEnumerable<Branch_List> Branch = null;
            IEnumerable<BranchLocale_List> BranchLocale = null;
            try
            {

                List<EmployeeBranch_List> EmployeeBranchList = new List<EmployeeBranch_List>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_EmployeeBranch";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new EmployeeBranch_List
                                {
                                    EmployeeBranch_ID = reader["EmployeeBranch_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["EmployeeBranch_ID"]),
                                    CompanyEmployee_ID = reader["CompanyEmployee_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CompanyEmployee_ID"]),
                                    Branch_ID = reader["Branch_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Branch_ID"]),
                                };

                                EmployeeBranchList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                List<Branch_List> BranchList = new List<Branch_List>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_Branch";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new Branch_List
                                {
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    Branch_ID = reader["Branch_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Branch_ID"]),
                                    Branch_Name = reader["Branch_Name"] == DBNull.Value ? null : reader["Branch_Name"].ToString(),
                                };

                                BranchList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                List<BranchLocale_List> BranchLocaleList = new List<BranchLocale_List>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "select BL.Branch_ID,BL.Branch_Name, B.Company_ID from GNM_BranchLocale BL JOIN GNM_Branch B ON B.Branch_ID=BL.Branch_ID";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new BranchLocale_List
                                {
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    Branch_ID = reader["Branch_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Branch_ID"]),
                                    Branch_Name = reader["Branch_Name"] == DBNull.Value ? null : reader["Branch_Name"].ToString(),
                                };

                                BranchLocaleList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }
                IEnumerable<EmployeeBranch_List> gEmployeeBranch = EmployeeBranchList.Where(e => e.CompanyEmployee_ID == OBJ.Employee_ID);
                int Language_ID = Convert.ToInt32(OBJ.UserLanguageID);
                int CompanyID = Convert.ToInt32(OBJ.Company_ID);
                if (OBJ.GeneralLanguageCode.ToString() == OBJ.UserLanguageCode.ToString())
                {
                    Branch = BranchList.Where(i => i.Company_ID == CompanyID);
                    jsonobj = new
                    {
                        Branch = from a in Branch
                                 join b in gEmployeeBranch on a.Branch_ID equals b.Branch_ID
                                 select new
                                 {
                                     ID = a.Branch_ID,
                                     Name = a.Branch_Name
                                 }
                    };
                }
                else
                {
                    BranchLocale = BranchLocaleList.Where(i => i.Company_ID == CompanyID);
                    jsonobj = new
                    {
                        Branch = from a in BranchLocale
                                 join b in gEmployeeBranch on a.Branch_ID equals b.Branch_ID
                                 select new
                                 {
                                     ID = a.Branch_ID,
                                     Name = a.Branch_Name
                                 }
                    };
                }

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonobj);
        }
        #endregion

        #region Select
        /// <summary>
        /// Select
        /// </summary>
        public static IActionResult Select(AgeingAnalysis_SelectList OBJ, string connString, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters)
        {
            var jsonobj = default(dynamic);
            int count = 0;
            int total = 0;
            DateTime localTime = DateTime.Now;
            //int userid = User.User_ID;

            List<AgeingAnalysis> RSList = new List<AgeingAnalysis>();
            IEnumerable<HD_ServiceRequest> SRequestAll = null;
            try
            {
                //Added by Manju M for HelpDesk CR-5 Changes 25-Aug-2015 -- Start

                localTime = Common.LocalTime(Convert.ToInt32(OBJ.Branch.ToString()), DateTime.Now, connString);
                OBJ.branchIDs = OBJ.branchIDs.TrimEnd(new char[] { ',' });
                OBJ.branchIDs = OBJ.branchIDs == "" ? "0" : OBJ.branchIDs;
                OBJ.CompanyIDs = OBJ.CompanyIDs.TrimEnd(new char[] { ',' });
                OBJ.CompanyIDs = OBJ.CompanyIDs == "" ? "0" : OBJ.CompanyIDs;
                string wherecondition = string.Empty;
                string FilterCondition = string.Empty;
                int CompanyID = Convert.ToInt32(OBJ.Company_ID);

                if (OBJ.branchIDs != "0")
                {
                    FilterCondition = FilterCondition + " and Branch_ID IN (" + OBJ.branchIDs + ")";
                }
                string Querydata = string.Empty;
                Querydata = "SELECT     ServiceRequest_ID,  IsDealer, IsDealerList,ServiceRequestNumber, ServiceRequestDate, Quotation_ID, QuotationNumber, JobCard_ID, JobCardNumber, CallStatus_ID, ParentIssue_ID, Product_Unique_Number, Party_ID, PartyContactPerson_ID, Model_ID, Brand_ID, ProductType_ID, SerialNumber, ProductReading, IsUnderWarranty, CallMode_ID, CallPriority_ID, CallComplexity_ID, CallDateAndTime, PromisedCompletionDate, Region_ID, CallDescription, IssueArea_ID, IssueSubArea_ID, FunctionGroup_ID, IsUnderBreakDown, QuestionaryLevel1_ID, QuestionaryLevel2_ID, QuestionaryLevel3_ID, DefectGroup_ID, DefectName_ID, RootCause, InformationCollected, CallClosureDateAndTime, ClosureType_ID, ClosingDescription, Company_ID, Branch_ID, ModifiedDate, ModifiedBy_ID, Document_no, CaseType_ID, ActionRemarks, Product_ID, CustomerRating, FinancialYear, IsCallBlocked, StockBlocking_ID, EnquiryStage_ID, SalesQuotation_ID, SalesQuotationNumber, SalesOrder_ID, SalesOrderNumber, SalesOrderDate, CallOwner_ID, Flexi1, Flexi2, ResolutionTime, ResponseTime,AttachmentCount,CustomerFeedbackDate,IsNegetiveFeedback,ProductRateType,ChildTicket_Sequence_ID,ResponseTime24,ResolutionTime24,Current_AssignTo,ContractorID,ContractorContactPerson_ID FROM         HD_ServiceRequest  where Company_ID in (" + OBJ.CompanyIDs + ")" + FilterCondition + " and CallClosureDateAndTime is null and CallStatus_ID!=(Select WFStepStatus_ID from GNM_WFStepStatus where WFStepStatus_Nm like '%Closed%')";

                List<HD_ServiceRequest> ServiceRequestList = new List<HD_ServiceRequest>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = Querydata;

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new HD_ServiceRequest
                                {
                                    ServiceRequest_ID = reader["ServiceRequest_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["ServiceRequest_ID"]),
                                    ServiceRequestNumber = reader["ServiceRequestNumber"] == DBNull.Value ? string.Empty : reader["ServiceRequestNumber"].ToString(),
                                    ServiceRequestDate = reader["ServiceRequestDate"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(reader["ServiceRequestDate"]),
                                    Quotation_ID = reader["Quotation_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Quotation_ID"]),
                                    QuotationNumber = reader["QuotationNumber"] == DBNull.Value ? string.Empty : reader["QuotationNumber"].ToString(),
                                    JobCard_ID = reader["JobCard_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["JobCard_ID"]),
                                    JobCardNumber = reader["JobCardNumber"] == DBNull.Value ? string.Empty : reader["JobCardNumber"].ToString(),
                                    CallStatus_ID = reader["CallStatus_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CallStatus_ID"]),
                                    ParentIssue_ID = reader["ParentIssue_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ParentIssue_ID"]),
                                    Product_Unique_Number = reader["Product_Unique_Number"] == DBNull.Value ? string.Empty : reader["Product_Unique_Number"].ToString(),
                                    Party_ID = reader["Party_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Party_ID"]),
                                    PartyContactPerson_ID = reader["PartyContactPerson_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["PartyContactPerson_ID"]),
                                    Model_ID = reader["Model_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Model_ID"]),
                                    Brand_ID = reader["Brand_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Brand_ID"]),
                                    ProductType_ID = reader["ProductType_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ProductType_ID"]),
                                    SerialNumber = reader["SerialNumber"] == DBNull.Value ? string.Empty : reader["SerialNumber"].ToString(),
                                    ProductReading = reader["ProductReading"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ProductReading"]),
                                    IsUnderWarranty = reader["IsUnderWarranty"] != DBNull.Value && Convert.ToBoolean(reader["IsUnderWarranty"]),
                                    CallMode_ID = reader["CallMode_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CallMode_ID"]),
                                    CallPriority_ID = reader["CallPriority_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["CallPriority_ID"]),
                                    CallComplexity_ID = reader["CallComplexity_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CallComplexity_ID"]),
                                    CallDateAndTime = reader["CallDateAndTime"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(reader["CallDateAndTime"]),
                                    PromisedCompletionDate = reader["PromisedCompletionDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["PromisedCompletionDate"]),
                                    Region_ID = reader["Region_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Region_ID"]),
                                    CallDescription = reader["CallDescription"] == DBNull.Value ? string.Empty : reader["CallDescription"].ToString(),
                                    IssueArea_ID = reader["IssueArea_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["IssueArea_ID"]),
                                    IssueSubArea_ID = reader["IssueSubArea_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["IssueSubArea_ID"]),
                                    FunctionGroup_ID = reader["FunctionGroup_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["FunctionGroup_ID"]),
                                    IsUnderBreakDown = reader["IsUnderBreakDown"] != DBNull.Value && Convert.ToBoolean(reader["IsUnderBreakDown"]),
                                    QuestionaryLevel1_ID = reader["QuestionaryLevel1_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["QuestionaryLevel1_ID"]),
                                    QuestionaryLevel2_ID = reader["QuestionaryLevel2_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["QuestionaryLevel2_ID"]),
                                    QuestionaryLevel3_ID = reader["QuestionaryLevel3_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["QuestionaryLevel3_ID"]),
                                    DefectGroup_ID = reader["DefectGroup_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["DefectGroup_ID"]),
                                    DefectName_ID = reader["DefectName_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["DefectName_ID"]),
                                    RootCause = reader["RootCause"] == DBNull.Value ? string.Empty : reader["RootCause"].ToString(),
                                    InformationCollected = reader["InformationCollected"] == DBNull.Value ? string.Empty : reader["InformationCollected"].ToString(),
                                    CallClosureDateAndTime = reader["CallClosureDateAndTime"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["CallClosureDateAndTime"]),
                                    ClosureType_ID = reader["ClosureType_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ClosureType_ID"]),
                                    ClosingDescription = reader["ClosingDescription"] == DBNull.Value ? string.Empty : reader["ClosingDescription"].ToString(),
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    Branch_ID = reader["Branch_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Branch_ID"]),
                                    ModifiedDate = reader["ModifiedDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["ModifiedDate"]),
                                    //ExpectedArrivalDateTime = reader["ExpectedArrivalDateTime"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["ExpectedArrivalDateTime"]),
                                    //ActualArrivalDateTime = reader["ActualArrivalDateTime"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["ActualArrivalDateTime"]),
                                    //ExpectedDepartureDateTime = reader["ExpectedDepartureDateTime"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["ExpectedDepartureDateTime"]),
                                    //ActualDepartureDateTime = reader["ActualDepartureDateTime"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["ActualDepartureDateTime"]),
                                    ModifiedBy_ID = reader["ModifiedBy_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ModifiedBy_ID"]),
                                    Document_no = reader["Document_no"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Document_no"]),
                                    CaseType_ID = reader["CaseType_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CaseType_ID"]),
                                    ActionRemarks = reader["ActionRemarks"] == DBNull.Value ? string.Empty : reader["ActionRemarks"].ToString(),
                                    Product_ID = reader["Product_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Product_ID"]),
                                    CustomerRating = reader["CustomerRating"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["CustomerRating"]),
                                    FinancialYear = reader["FinancialYear"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["FinancialYear"]),
                                    IsCallBlocked = reader["IsCallBlocked"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IsCallBlocked"]),
                                    StockBlocking_ID = reader["StockBlocking_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["StockBlocking_ID"]),
                                    EnquiryStage_ID = reader["EnquiryStage_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["EnquiryStage_ID"]),
                                    SalesQuotation_ID = reader["SalesQuotation_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["SalesQuotation_ID"]),
                                    SalesQuotationNumber = reader["SalesQuotationNumber"] == DBNull.Value ? string.Empty : reader["SalesQuotationNumber"].ToString(),
                                    SalesOrder_ID = reader["SalesOrder_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["SalesOrder_ID"]),
                                    SalesOrderNumber = reader["SalesOrderNumber"] == DBNull.Value ? string.Empty : reader["SalesOrderNumber"].ToString(),
                                    SalesOrderDate = reader["SalesOrderDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["SalesOrderDate"]),
                                    CallOwner_ID = reader["CallOwner_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["CallOwner_ID"]),
                                    Current_AssignTo = reader["Current_AssignTo"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Current_AssignTo"]),
                                    ContractorID = reader["ContractorID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ContractorID"]),
                                    ContractorContactPerson_ID = reader["ContractorContactPerson_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ContractorContactPerson_ID"]),
                                    //ScheduledType_ID = reader["ScheduledType_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ScheduledType_ID"]),
                                    //NoofTechs = reader["NoofTechs"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["NoofTechs"]),
                                    //ShiftHours = reader["ShiftHours"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ShiftHours"]),
                                    Flexi1 = reader["Flexi1"] == DBNull.Value ? string.Empty : reader["Flexi1"].ToString(),
                                    Flexi2 = reader["Flexi2"] == DBNull.Value ? string.Empty : reader["Flexi2"].ToString(),
                                    ResolutionTime = reader["ResolutionTime"] == DBNull.Value ? string.Empty : reader["ResolutionTime"].ToString(),
                                    ResponseTime = reader["ResponseTime"] == DBNull.Value ? string.Empty : reader["ResponseTime"].ToString(),
                                    AttachmentCount = reader["AttachmentCount"] == DBNull.Value ? (byte?)null : Convert.ToByte(reader["AttachmentCount"]),
                                    CustomerFeedbackDate = reader["CustomerFeedbackDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["CustomerFeedbackDate"]),
                                    IsNegetiveFeedback = reader["IsNegetiveFeedback"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IsNegetiveFeedback"]),
                                    IsDealer = reader["IsDealer"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IsDealer"]),
                                    //IsWIPBay = reader["IsWIPBay"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IsWIPBay"]),
                                    IsDealerList = reader["IsDealerList"] == DBNull.Value ? (byte?)null : Convert.ToByte(reader["IsDealerList"]),
                                    ProductRateType = reader["ProductRateType"] == DBNull.Value ? (byte?)null : Convert.ToByte(reader["ProductRateType"]),
                                    ChildTicket_Sequence_ID = reader["ChildTicket_Sequence_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ChildTicket_Sequence_ID"]),
                                    ResponseTime24 = reader["ResponseTime24"] == DBNull.Value ? string.Empty : reader["ResponseTime24"].ToString(),
                                    ResolutionTime24 = reader["ResolutionTime24"] == DBNull.Value ? string.Empty : reader["ResolutionTime24"].ToString(),
                                };

                                ServiceRequestList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                SRequestAll = ServiceRequestList;

                List<GNM_CompParam> CompParamList = new List<GNM_CompParam>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_CompParam";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_CompParam
                                {
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    Param_Name = reader["Param_Name"] == DBNull.Value ? null : reader["Param_Name"].ToString(),
                                    Param_value = reader["Param_value"] == DBNull.Value ? null : reader["Param_value"].ToString(),
                                };

                                CompParamList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                //---------------------------------------------------------
                string AgeingSlot = string.Empty;
                List<TimeSlot> timeSlotList = new List<TimeSlot>();
                string[] TimeSlotArray;
                string AgeingReportIn = CompParamList.Where(i => i.Company_ID == OBJ.Company_ID && i.Param_Name.ToUpper() == "AGEINGREPORT").Select(i => i.Param_value).FirstOrDefault();
                if (AgeingReportIn.ToUpper() == "HOURS")
                {
                    AgeingSlot = CompParamList.Where(i => i.Company_ID == OBJ.Company_ID && i.Param_Name.ToUpper() == "TIMESLOT").Select(i => i.Param_value).FirstOrDefault();
                    TimeSlotArray = AgeingSlot.Split(',');
                    for (int i = 0; i < TimeSlotArray.Length; i++)
                    {
                        timeSlotList.Add(new TimeSlot { Name = TimeSlotArray[i], Count = 0, Hours = TimeSlotArray[i] });
                    }
                    timeSlotList.Add(new TimeSlot { Name = (Convert.ToInt32(TimeSlotArray[TimeSlotArray.Length - 1]) + 1).ToString(), Count = 0, Hours = (Convert.ToInt32(TimeSlotArray[TimeSlotArray.Length - 1]) + 1).ToString() });

                    TimeSlotList = timeSlotList;

                    foreach (var sr in SRequestAll)
                    {
                        int THour = Convert.ToInt32(localTime.Subtract(Convert.ToDateTime(sr.CallDateAndTime)).TotalHours);
                        timeSlotList = CalculateTimeSlot(timeSlotList, THour, LogException);
                    }
                    for (int i = 0; i < timeSlotList.Count; i++)
                    {
                        if (i == 0)
                        {
                            timeSlotList[i].Name = CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "LessThan").ToString() + " " + timeSlotList[i].Hours + " " + CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "Hours").ToString();
                        }
                        else if (i == timeSlotList.Count - 1)
                        {
                            timeSlotList[i].Name = CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "GreaterThan").ToString() + " " + timeSlotList[i - 1].Hours + " " + CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "Hours").ToString();
                        }
                        else
                        {
                            timeSlotList[i].Name = timeSlotList[i - 1].Hours + " " + CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "To").ToString() + " " + timeSlotList[i].Hours + " " + CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "Hours").ToString();
                        }
                    }
                }
                else if (AgeingReportIn.ToUpper() == "DAYS")
                {
                    AgeingSlot = CompParamList.Where(i => i.Company_ID == OBJ.Company_ID && i.Param_Name.ToUpper() == "DAYSLOT").Select(i => i.Param_value).FirstOrDefault();
                    TimeSlotArray = AgeingSlot.Split(',');
                    for (int i = 0; i < TimeSlotArray.Length; i++)
                    {
                        timeSlotList.Add(new TimeSlot { Name = TimeSlotArray[i], Count = 0, Hours = TimeSlotArray[i] });
                    }
                    timeSlotList.Add(new TimeSlot { Name = (Convert.ToInt32(TimeSlotArray[TimeSlotArray.Length - 1]) + 1).ToString(), Count = 0, Hours = (Convert.ToInt32(TimeSlotArray[TimeSlotArray.Length - 1]) + 1).ToString() });

                    TimeSlotList = timeSlotList;

                    foreach (var sr in SRequestAll)
                    {
                        int THour = Convert.ToInt32(localTime.Subtract(Convert.ToDateTime(sr.CallDateAndTime)).TotalDays);
                        timeSlotList = CalculateTimeSlot(timeSlotList, THour, LogException);
                    }
                    for (int i = 0; i < timeSlotList.Count; i++)
                    {
                        if (i == 0)
                        {
                            timeSlotList[i].Name = CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "LessThan").ToString() + " " + timeSlotList[i].Hours + " " + CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "Day").ToString();
                        }
                        else if (i == timeSlotList.Count - 1)
                        {
                            timeSlotList[i].Name = CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "GreaterThan").ToString() + " " + timeSlotList[i - 1].Hours + " " + CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "Days").ToString();
                        }
                        else
                        {
                            timeSlotList[i].Name = timeSlotList[i - 1].Hours + " " + CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "To").ToString() + " " + timeSlotList[i].Hours + " " + CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "Days").ToString();
                        }
                    }
                }

                count = timeSlotList.Count;
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }
                jsonobj = new
                {
                    TotalPages = total,
                    PageNo = page,
                    RecordCount = count,
                    rows = (from result in timeSlotList
                            select new
                            {
                                Hours = result.Hours,
                                Ageing = result.Name,
                                Count = result.Count
                            }).ToArray(),
                    TotalRecordsCount = SRequestAll.Count()
                };
                //gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"].ToString()), Convert.ToInt32(Session["Branch"]), OBJ.User_ID, Common.GetObjectID("AgeingAnalysisReport"), 0, 0, 0, "Generated - Ageing Analysis Report ", false, Convert.ToInt32(OBJ.MenuID), Convert.ToDateTime(OBJ.LoggedINDateTime));
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonobj);
        }
        #endregion

        #region SelectSlotWise
        /// <summary>
        /// SelectSlotWise
        /// </summary>
        public static IActionResult SelectSlotWise(AgeingAnalysis_SelectSlotWiseList OBJ, string connString, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters)
        {
            var jsonobj = default(dynamic);
            int count = 1;
            int total = 1;
            int userid = OBJ.User_ID;
            string BranchID = string.Empty;
            DateTime localTime = DateTime.Now;
            //int CompanyID = User.Company_ID;
            List<AgeingAnalysis> RSList = new List<AgeingAnalysis>();

            IEnumerable<HD_ServiceRequest> SRequestAll = null;
            IQueryable<ServiceRequestAgeing> iQServiceReq = null;
            IEnumerable<ServiceRequestAgeing> ServiceReq = null;
            IEnumerable<GNM_Party> gParty = null;
            IEnumerable<GNM_PartyLocale> PartyLocale = null;
            IEnumerable<GNM_Model> gModel = null;
            IEnumerable<GNM_ModelLocale> ModelLocale = null;
            IEnumerable<GNM_RefMaster> RefMaster = null;
            IEnumerable<GNM_RefMasterDetail> Brand = null;
            IEnumerable<GNM_RefMasterDetailLocale> BrandLocale = null;

            try
            {
                //Added by Manju M for HelpDesk CR-5 Changes 25-Aug-2015 -- Start                
                localTime = Common.LocalTime(Convert.ToInt32(OBJ.Branch.ToString()), DateTime.Now, connString);
                //Added by Manjunatha M for HelpDesk CR-5 Changes 25-Aug-2015 -- End
                int LangID = OBJ.Language_ID;

                OBJ.branchIDs = OBJ.branchIDs.TrimEnd(new char[] { ',' });
                OBJ.branchIDs = OBJ.branchIDs == "" ? "0" : OBJ.branchIDs;
                OBJ.CompanyIDs = OBJ.CompanyIDs.TrimEnd(new char[] { ',' });
                OBJ.CompanyIDs = OBJ.CompanyIDs == "" ? "0" : OBJ.CompanyIDs;
                string wherecondition = string.Empty;
                string FilterCondition = string.Empty;
                int CompanyID = Convert.ToInt32(OBJ.Company_ID);

                string GenLangCode = OBJ.GeneralLanguageCode.ToString();
                string UserLangCode = OBJ.UserLanguageCode.ToString();
                int userLanguageID = Convert.ToInt32(OBJ.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(OBJ.GeneralLanguageID);
                //Company companys = new Company();
                //if (Request.Params["CompanyData"] != null && Request.Params["CompanyData"] != "")
                //{
                //    companys = JObject.Parse(Common.DecryptString(Request.Params["CompanyData"])).ToObject<Company>(); //~Manju M
                //}

                //if (Request.Params["CompanyData"] != null && Request.Params["CompanyData"] != "")
                //{
                //    if (companys.Companys.Count > 0)
                //    {
                //        for (int i = 0; i < companys.Companys.Count; i++)
                //        {
                //            CompanyID = CompanyID + companys.Companys[i].ID + ", ";
                //        }
                //        CompanyID = CompanyID.Remove(CompanyID.LastIndexOf(','), 1);
                //        //Session["Company_ID"] = CompanyID;
                //    }
                //}


                Branch branchs = new Branch();
                if (OBJ.BranchData != null && OBJ.BranchData != "")
                {
                    branchs = JObject.Parse(Common.DecryptString(Common.DecryptString(OBJ.BranchData))).ToObject<Branch>(); //~Manju M
                }

                if (OBJ.BranchData != null && OBJ.BranchData != "")
                {

                    for (int i = 0; i < branchs.Branchs.Count; i++)
                    {
                        BranchID = BranchID + branchs.Branchs[i].ID + ", ";
                    }
                    BranchID = BranchID.Remove(BranchID.LastIndexOf(','), 1);
                    //Session["Branch_ID"] = OBJ.BranchID;
                }
                //SRequestAll = SRClient.HD_ServiceRequest.Where(sr => sr.Company_ID == CompanyID && sr.CallClosureDateAndTime == null);
                string Querydata = string.Empty;

                if (BranchID != "" && BranchID != null)
                {
                    Querydata = "SELECT     ServiceRequest_ID, IsDealer, IsDealerList,ServiceRequestNumber, ServiceRequestDate, Quotation_ID, QuotationNumber, JobCard_ID, JobCardNumber, CallStatus_ID, ParentIssue_ID, Product_Unique_Number, Party_ID, PartyContactPerson_ID, Model_ID, Brand_ID, ProductType_ID, SerialNumber, ProductReading, IsUnderWarranty, CallMode_ID, CallPriority_ID, CallComplexity_ID, CallDateAndTime, PromisedCompletionDate, Region_ID, CallDescription, IssueArea_ID, IssueSubArea_ID, FunctionGroup_ID, IsUnderBreakDown, QuestionaryLevel1_ID, QuestionaryLevel2_ID, QuestionaryLevel3_ID, DefectGroup_ID, DefectName_ID, RootCause, InformationCollected, CallClosureDateAndTime, ClosureType_ID, ClosingDescription, Company_ID, Branch_ID, ModifiedDate, ModifiedBy_ID, Document_no, CaseType_ID, ActionRemarks, Product_ID, CustomerRating, FinancialYear, IsCallBlocked, StockBlocking_ID, EnquiryStage_ID, SalesQuotation_ID, SalesQuotationNumber, SalesOrder_ID, SalesOrderNumber, SalesOrderDate, CallOwner_ID, Flexi1, Flexi2, ResolutionTime, ResponseTime,AttachmentCount,CustomerFeedbackDate,IsNegetiveFeedback,ProductRateType,ChildTicket_Sequence_ID,ResponseTime24,ResolutionTime24,Current_AssignTo,ContractorID,ContractorContactPerson_ID FROM         HD_ServiceRequest  where Company_ID in (" + OBJ.CompanyIDs + ")" + "and Branch_ID in (" + BranchID + ") and CallClosureDateAndTime is null and CallStatus_ID!=(Select WFStepStatus_ID from GNM_WFStepStatus where WFStepStatus_Nm like '%Closed%')";

                }
                else
                {
                    Querydata = "SELECT     ServiceRequest_ID,IsDealer,IsDealerList, ServiceRequestNumber, ServiceRequestDate, Quotation_ID, QuotationNumber, JobCard_ID, JobCardNumber, CallStatus_ID, ParentIssue_ID, Product_Unique_Number, Party_ID, PartyContactPerson_ID, Model_ID, Brand_ID, ProductType_ID, SerialNumber, ProductReading, IsUnderWarranty, CallMode_ID, CallPriority_ID, CallComplexity_ID, CallDateAndTime, PromisedCompletionDate, Region_ID, CallDescription, IssueArea_ID, IssueSubArea_ID, FunctionGroup_ID, IsUnderBreakDown, QuestionaryLevel1_ID, QuestionaryLevel2_ID, QuestionaryLevel3_ID, DefectGroup_ID, DefectName_ID, RootCause, InformationCollected, CallClosureDateAndTime, ClosureType_ID, ClosingDescription, Company_ID, Branch_ID, ModifiedDate, ModifiedBy_ID, Document_no, CaseType_ID, ActionRemarks, Product_ID, CustomerRating, FinancialYear, IsCallBlocked, StockBlocking_ID, EnquiryStage_ID, SalesQuotation_ID, SalesQuotationNumber, SalesOrder_ID, SalesOrderNumber, SalesOrderDate, CallOwner_ID, Flexi1, Flexi2, ResolutionTime, ResponseTime,AttachmentCount,CustomerFeedbackDate,IsNegetiveFeedback,ProductRateType,ChildTicket_Sequence_ID,ResponseTime24,ResolutionTime24,Current_AssignTo,ContractorID,ContractorContactPerson_ID FROM         HD_ServiceRequest  where Company_ID in (" + OBJ.CompanyIDs + ")" + " and CallClosureDateAndTime is null and CallStatus_ID!=(Select WFStepStatus_ID from GNM_WFStepStatus where WFStepStatus_Nm like '%Closed%')";
                }

                List<HD_ServiceRequest> ServiceRequestList = new List<HD_ServiceRequest>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = Querydata;

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new HD_ServiceRequest
                                {
                                    ServiceRequest_ID = reader["ServiceRequest_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["ServiceRequest_ID"]),
                                    ServiceRequestNumber = reader["ServiceRequestNumber"] == DBNull.Value ? string.Empty : reader["ServiceRequestNumber"].ToString(),
                                    ServiceRequestDate = reader["ServiceRequestDate"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(reader["ServiceRequestDate"]),
                                    Quotation_ID = reader["Quotation_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Quotation_ID"]),
                                    QuotationNumber = reader["QuotationNumber"] == DBNull.Value ? string.Empty : reader["QuotationNumber"].ToString(),
                                    JobCard_ID = reader["JobCard_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["JobCard_ID"]),
                                    JobCardNumber = reader["JobCardNumber"] == DBNull.Value ? string.Empty : reader["JobCardNumber"].ToString(),
                                    CallStatus_ID = reader["CallStatus_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CallStatus_ID"]),
                                    ParentIssue_ID = reader["ParentIssue_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ParentIssue_ID"]),
                                    Product_Unique_Number = reader["Product_Unique_Number"] == DBNull.Value ? string.Empty : reader["Product_Unique_Number"].ToString(),
                                    Party_ID = reader["Party_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Party_ID"]),
                                    PartyContactPerson_ID = reader["PartyContactPerson_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["PartyContactPerson_ID"]),
                                    Model_ID = reader["Model_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Model_ID"]),
                                    Brand_ID = reader["Brand_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Brand_ID"]),
                                    ProductType_ID = reader["ProductType_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ProductType_ID"]),
                                    SerialNumber = reader["SerialNumber"] == DBNull.Value ? string.Empty : reader["SerialNumber"].ToString(),
                                    ProductReading = reader["ProductReading"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ProductReading"]),
                                    IsUnderWarranty = reader["IsUnderWarranty"] != DBNull.Value && Convert.ToBoolean(reader["IsUnderWarranty"]),
                                    CallMode_ID = reader["CallMode_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CallMode_ID"]),
                                    CallPriority_ID = reader["CallPriority_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["CallPriority_ID"]),
                                    CallComplexity_ID = reader["CallComplexity_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CallComplexity_ID"]),
                                    CallDateAndTime = reader["CallDateAndTime"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(reader["CallDateAndTime"]),
                                    PromisedCompletionDate = reader["PromisedCompletionDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["PromisedCompletionDate"]),
                                    Region_ID = reader["Region_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Region_ID"]),
                                    CallDescription = reader["CallDescription"] == DBNull.Value ? string.Empty : reader["CallDescription"].ToString(),
                                    IssueArea_ID = reader["IssueArea_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["IssueArea_ID"]),
                                    IssueSubArea_ID = reader["IssueSubArea_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["IssueSubArea_ID"]),
                                    FunctionGroup_ID = reader["FunctionGroup_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["FunctionGroup_ID"]),
                                    IsUnderBreakDown = reader["IsUnderBreakDown"] != DBNull.Value && Convert.ToBoolean(reader["IsUnderBreakDown"]),
                                    QuestionaryLevel1_ID = reader["QuestionaryLevel1_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["QuestionaryLevel1_ID"]),
                                    QuestionaryLevel2_ID = reader["QuestionaryLevel2_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["QuestionaryLevel2_ID"]),
                                    QuestionaryLevel3_ID = reader["QuestionaryLevel3_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["QuestionaryLevel3_ID"]),
                                    DefectGroup_ID = reader["DefectGroup_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["DefectGroup_ID"]),
                                    DefectName_ID = reader["DefectName_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["DefectName_ID"]),
                                    RootCause = reader["RootCause"] == DBNull.Value ? string.Empty : reader["RootCause"].ToString(),
                                    InformationCollected = reader["InformationCollected"] == DBNull.Value ? string.Empty : reader["InformationCollected"].ToString(),
                                    CallClosureDateAndTime = reader["CallClosureDateAndTime"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["CallClosureDateAndTime"]),
                                    ClosureType_ID = reader["ClosureType_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ClosureType_ID"]),
                                    ClosingDescription = reader["ClosingDescription"] == DBNull.Value ? string.Empty : reader["ClosingDescription"].ToString(),
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    Branch_ID = reader["Branch_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Branch_ID"]),
                                    ModifiedDate = reader["ModifiedDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["ModifiedDate"]),
                                    //ExpectedArrivalDateTime = reader["ExpectedArrivalDateTime"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["ExpectedArrivalDateTime"]),
                                    //ActualArrivalDateTime = reader["ActualArrivalDateTime"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["ActualArrivalDateTime"]),
                                    //ExpectedDepartureDateTime = reader["ExpectedDepartureDateTime"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["ExpectedDepartureDateTime"]),
                                    //ActualDepartureDateTime = reader["ActualDepartureDateTime"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["ActualDepartureDateTime"]),
                                    ModifiedBy_ID = reader["ModifiedBy_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ModifiedBy_ID"]),
                                    Document_no = reader["Document_no"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Document_no"]),
                                    CaseType_ID = reader["CaseType_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CaseType_ID"]),
                                    ActionRemarks = reader["ActionRemarks"] == DBNull.Value ? string.Empty : reader["ActionRemarks"].ToString(),
                                    Product_ID = reader["Product_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Product_ID"]),
                                    CustomerRating = reader["CustomerRating"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["CustomerRating"]),
                                    FinancialYear = reader["FinancialYear"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["FinancialYear"]),
                                    IsCallBlocked = reader["IsCallBlocked"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IsCallBlocked"]),
                                    StockBlocking_ID = reader["StockBlocking_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["StockBlocking_ID"]),
                                    EnquiryStage_ID = reader["EnquiryStage_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["EnquiryStage_ID"]),
                                    SalesQuotation_ID = reader["SalesQuotation_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["SalesQuotation_ID"]),
                                    SalesQuotationNumber = reader["SalesQuotationNumber"] == DBNull.Value ? string.Empty : reader["SalesQuotationNumber"].ToString(),
                                    SalesOrder_ID = reader["SalesOrder_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["SalesOrder_ID"]),
                                    SalesOrderNumber = reader["SalesOrderNumber"] == DBNull.Value ? string.Empty : reader["SalesOrderNumber"].ToString(),
                                    SalesOrderDate = reader["SalesOrderDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["SalesOrderDate"]),
                                    CallOwner_ID = reader["CallOwner_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["CallOwner_ID"]),
                                    Current_AssignTo = reader["Current_AssignTo"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Current_AssignTo"]),
                                    ContractorID = reader["ContractorID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ContractorID"]),
                                    ContractorContactPerson_ID = reader["ContractorContactPerson_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ContractorContactPerson_ID"]),
                                    //ScheduledType_ID = reader["ScheduledType_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ScheduledType_ID"]),
                                    //NoofTechs = reader["NoofTechs"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["NoofTechs"]),
                                    //ShiftHours = reader["ShiftHours"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ShiftHours"]),
                                    Flexi1 = reader["Flexi1"] == DBNull.Value ? string.Empty : reader["Flexi1"].ToString(),
                                    Flexi2 = reader["Flexi2"] == DBNull.Value ? string.Empty : reader["Flexi2"].ToString(),
                                    ResolutionTime = reader["ResolutionTime"] == DBNull.Value ? string.Empty : reader["ResolutionTime"].ToString(),
                                    ResponseTime = reader["ResponseTime"] == DBNull.Value ? string.Empty : reader["ResponseTime"].ToString(),
                                    AttachmentCount = reader["AttachmentCount"] == DBNull.Value ? (byte?)null : Convert.ToByte(reader["AttachmentCount"]),
                                    CustomerFeedbackDate = reader["CustomerFeedbackDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["CustomerFeedbackDate"]),
                                    IsNegetiveFeedback = reader["IsNegetiveFeedback"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IsNegetiveFeedback"]),
                                    IsDealer = reader["IsDealer"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IsDealer"]),
                                    //IsWIPBay = reader["IsWIPBay"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IsWIPBay"]),
                                    IsDealerList = reader["IsDealerList"] == DBNull.Value ? (byte?)null : Convert.ToByte(reader["IsDealerList"]),
                                    ProductRateType = reader["ProductRateType"] == DBNull.Value ? (byte?)null : Convert.ToByte(reader["ProductRateType"]),
                                    ChildTicket_Sequence_ID = reader["ChildTicket_Sequence_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ChildTicket_Sequence_ID"]),
                                    ResponseTime24 = reader["ResponseTime24"] == DBNull.Value ? string.Empty : reader["ResponseTime24"].ToString(),
                                    ResolutionTime24 = reader["ResolutionTime24"] == DBNull.Value ? string.Empty : reader["ResolutionTime24"].ToString(),
                                };

                                ServiceRequestList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                SRequestAll = ServiceRequestList;
                List<GNM_CompParam> CompParamList = new List<GNM_CompParam>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_CompParam";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_CompParam
                                {
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    Param_Name = reader["Param_Name"] == DBNull.Value ? null : reader["Param_Name"].ToString(),
                                    Param_value = reader["Param_value"] == DBNull.Value ? null : reader["Param_value"].ToString(),
                                };

                                CompParamList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }
                List<GNM_Branch> Branch_List = new List<GNM_Branch>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_Branch";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_Branch
                                {
                                    Branch_ID = reader["Branch_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Branch_ID"]),
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    Branch_Name = reader["Branch_Name"] == DBNull.Value ? string.Empty : reader["Branch_Name"].ToString(),
                                    Branch_ShortName = reader["Branch_ShortName"] == DBNull.Value ? string.Empty : reader["Branch_ShortName"].ToString(),
                                    Branch_ZipCode = reader["Branch_ZipCode"] == DBNull.Value ? string.Empty : reader["Branch_ZipCode"].ToString(),
                                    Country_ID = reader["Country_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Country_ID"]),
                                    State_ID = reader["State_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["State_ID"]),
                                    Branch_Phone = reader["Branch_Phone"] == DBNull.Value ? string.Empty : reader["Branch_Phone"].ToString(),
                                    Branch_Fax = reader["Branch_Fax"] == DBNull.Value ? string.Empty : reader["Branch_Fax"].ToString(),
                                    Branch_HeadOffice = reader["Branch_HeadOffice"] != DBNull.Value && Convert.ToBoolean(reader["Branch_HeadOffice"]),
                                    Branch_Active = reader["Branch_Active"] != DBNull.Value && Convert.ToBoolean(reader["Branch_Active"]),
                                    Branch_Address = reader["Branch_Address"] == DBNull.Value ? string.Empty : reader["Branch_Address"].ToString(),
                                    Branch_Location = reader["Branch_Location"] == DBNull.Value ? string.Empty : reader["Branch_Location"].ToString(),
                                    Branch_Email = reader["Branch_Email"] == DBNull.Value ? string.Empty : reader["Branch_Email"].ToString(),
                                    Branch_Mobile = reader["Branch_Mobile"] == DBNull.Value ? string.Empty : reader["Branch_Mobile"].ToString(),
                                    Branch_External = reader["Branch_External"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["Branch_External"]),
                                    TimeZoneID = reader["TimeZoneID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["TimeZoneID"]),
                                    Region_ID = reader["Region_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Region_ID"]),
                                    Currency_ID = reader["Currency_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Currency_ID"]),
                                    LanguageID = reader["LanguageID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["LanguageID"]),
                                    IsOverTimeDWM = reader["IsOverTimeDWM"] == DBNull.Value ? (byte?)null : Convert.ToByte(reader["IsOverTimeDWM"]),
                                    Yearly_Sales_Target = reader["Yearly_Sales_Target"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["Yearly_Sales_Target"]),
                                    Rework_Target = reader["Rework_Target"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["Rework_Target"]),
                                    Cust_Satisfaction_Target = reader["Cust_Satisfaction_Target"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["Cust_Satisfaction_Target"]),
                                    RO_with_Rework_Target = reader["RO_with_Rework_Target"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["RO_with_Rework_Target"]),
                                    RO_with_Cust_Satisfaction_Target = reader["RO_with_Cust_Satisfaction_Target"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["RO_with_Cust_Satisfaction_Target"]),
                                    DueDays = reader["DueDays"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["DueDays"]),
                                    PayrollSystem_ID = reader["PayrollSystem_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["PayrollSystem_ID"]),
                                    MaxCarryOverHours = reader["MaxCarryOverHours"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["MaxCarryOverHours"]),
                                    ConsumedCarryOverByDate = reader["ConsumedCarryOverByDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["ConsumedCarryOverByDate"]),
                                    IsHourlyRateBranchWise = reader["IsHourlyRateBranchWise"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IsHourlyRateBranchWise"]),
                                    PayrollFileType_ID = reader["PayrollFileType_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["PayrollFileType_ID"]),
                                    Yearly_Parts_Target = reader["Yearly_Parts_Target"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["Yearly_Parts_Target"]),
                                    Variance_Percentage = reader["Variance_Percentage"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["Variance_Percentage"]),
                                    Variance_Value = reader["Variance_Value"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["Variance_Value"]),
                                    ETOExtensionHours = reader["ETOExtensionHours"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ETOExtensionHours"]),
                                    ETOMultiples = reader["ETOMultiples"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ETOMultiples"]),
                                    BilledVsActualVariance_Percentage = reader["BilledVsActualVariance_Percentage"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["BilledVsActualVariance_Percentage"]),
                                    TypeofPayroll = reader["TypeofPayroll"] == DBNull.Value ? (byte?)null : Convert.ToByte(reader["TypeofPayroll"]),
                                    LessVariance_Percentage = reader["LessVariance_Percentage"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["LessVariance_Percentage"]),
                                    LessVariance_Value = reader["LessVariance_Value"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["LessVariance_Value"]),
                                    IIMoreVariance_Percentage = reader["IIMoreVariance_Percentage"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["IIMoreVariance_Percentage"]),
                                    IIMoreVariance_Value = reader["IIMoreVariance_Value"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["IIMoreVariance_Value"]),
                                    IILessVariance_Percentage = reader["IILessVariance_Percentage"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["IILessVariance_Percentage"]),
                                    IILessVariance_Value = reader["IILessVariance_Value"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["IILessVariance_Value"]),
                                    WorkingMinutes = reader["WorkingMinutes"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["WorkingMinutes"])
                                };

                                Branch_List.Add(refMasterDetailObj);
                            }
                        }
                    }
                }
                string BranchNames = string.Empty;
                for (int i = 0; i < branchs.Branchs.Count; i++)
                {
                    int ID = branchs.Branchs[i].ID;
                    BranchNames = BranchNames + Branch_List.Where(a => a.Branch_ID == ID).FirstOrDefault().Branch_Name + ", ";
                }
                BranchNames = BranchNames.Remove(BranchNames.LastIndexOf(','), 1);
                BranchName = BranchNames;

                //Modified by Shashi for Helpdesk Enhancement
                TimeSlotFilter Filter = GetTimeSlotFilter(OBJ.Hours, LogException);
                string AgeingReportIn = CompParamList.Where(i => i.Company_ID == OBJ.Company_ID && i.Param_Name.ToUpper() == "AGEINGREPORT").Select(i => i.Param_value).FirstOrDefault();
                if (AgeingReportIn.ToUpper() == "HOURS")
                {
                    if (Filter.Type == 1)//First
                    {
                        SRequestAll = (from a in SRequestAll
                                       join b in branchs.Branchs on a.Branch_ID equals b.ID
                                       where (Convert.ToInt32(localTime.Subtract(a.CallDateAndTime).TotalHours) < Filter.Min)
                                       select a).ToList();
                    }
                    else if (Filter.Type == 2)//Last
                    {
                        SRequestAll = (from a in SRequestAll
                                       join b in branchs.Branchs on a.Branch_ID equals b.ID
                                       where (Convert.ToInt32(localTime.Subtract(a.CallDateAndTime).TotalHours) >= Filter.Max)
                                       select a).ToList();
                    }
                    else if (Filter.Type == 3)
                    {
                        SRequestAll = (from a in SRequestAll
                                       join b in branchs.Branchs on a.Branch_ID equals b.ID
                                       where (Convert.ToInt32(localTime.Subtract(a.CallDateAndTime).TotalHours) >= Filter.Min)
                                       && (Convert.ToInt32(localTime.Subtract(a.CallDateAndTime).TotalHours) < Filter.Max)
                                       select a).ToList();
                    }
                }
                else if (AgeingReportIn.ToUpper() == "DAYS")
                {
                    if (Filter.Type == 1)//First
                    {
                        SRequestAll = (from a in SRequestAll
                                       join b in branchs.Branchs on a.Branch_ID equals b.ID
                                       where (Convert.ToInt32(localTime.Subtract(a.CallDateAndTime).TotalDays) < Filter.Min)
                                       select a).ToList();
                    }
                    else if (Filter.Type == 2)//Last
                    {
                        SRequestAll = (from a in SRequestAll
                                       join b in branchs.Branchs on a.Branch_ID equals b.ID
                                       where (Convert.ToInt32(localTime.Subtract(a.CallDateAndTime).TotalDays) >= Filter.Max)
                                       select a).ToList();
                    }
                    else if (Filter.Type == 3)
                    {
                        SRequestAll = (from a in SRequestAll
                                       join b in branchs.Branchs on a.Branch_ID equals b.ID
                                       where (Convert.ToInt32(localTime.Subtract(a.CallDateAndTime).TotalDays) >= Filter.Min)
                                       && (Convert.ToInt32(localTime.Subtract(a.CallDateAndTime).TotalDays) < Filter.Max)
                                       select a).ToList();
                    }
                }
                //Changes Ends
                //---------------------------------------------------------

                List<GNM_Company> CompanyList = new List<GNM_Company>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_Company";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_Company
                                {
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    Company_Name = reader["Company_Name"] == DBNull.Value ? null : reader["Company_Name"].ToString(),
                                };

                                CompanyList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                List<WF_WFStepStatus> WFStepStatusList = new List<WF_WFStepStatus>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_WFStepStatus";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new WF_WFStepStatus
                                {
                                    WFStepStatus_ID = reader["WFStepStatus_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["WFStepStatus_ID"]),
                                    WFStepStatus_Nm = reader["WFStepStatus_Nm"] == DBNull.Value ? null : reader["WFStepStatus_Nm"].ToString(),
                                };

                                WFStepStatusList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                List<GNM_Party> Party_List = new List<GNM_Party>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_Party";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_Party
                                {
                                    Party_ID = reader["Party_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Party_ID"]),
                                    Party_Name = reader["Party_Name"] == DBNull.Value ? null : reader["Party_Name"].ToString(),
                                };

                                Party_List.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                List<GNM_PartyLocale> PartyLocale_List = new List<GNM_PartyLocale>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_PartyLocale";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_PartyLocale
                                {
                                    Party_ID = reader["Party_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Party_ID"]),
                                    Language_ID = reader["Language_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Language_ID"]),
                                    Party_Name = reader["Party_Name"] == DBNull.Value ? null : reader["Party_Name"].ToString(),
                                };

                                PartyLocale_List.Add(refMasterDetailObj);
                            }
                        }
                    }
                }
                List<GNM_Model> Model_Detail = new List<GNM_Model>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_Model";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_Model
                                {
                                    Model_IsActive = (bool)(reader["Model_IsActive"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["Model_IsActive"])),
                                    Model_ID = reader["Model_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Model_ID"]),
                                };

                                Model_Detail.Add(refMasterDetailObj);
                            }
                        }
                    }
                }
                List<GNM_ModelLocale> ModelLocale_Detail = new List<GNM_ModelLocale>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT ML.Language_ID, ML.Model_ID, M.Model_IsActive FROM GNM_ModelLocale ML JOIN GNM_Model M ON ML.Model_ID=M.Model_ID";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_ModelLocale
                                {
                                    Model_IsActive = (bool)(reader["Model_IsActive"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["Model_IsActive"])),
                                    Model_ID = reader["Model_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Model_ID"]),
                                    Language_ID = reader["Language_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Language_ID"]),
                                };

                                ModelLocale_Detail.Add(refMasterDetailObj);
                            }
                        }
                    }
                }
                List<GNM_RefMasterDetail> refDetail = new List<GNM_RefMasterDetail>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_RefMasterDetail";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_RefMasterDetail
                                {
                                    RefMaster_ID = reader["RefMaster_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["RefMaster_ID"]),
                                    RefMasterDetail_Name = reader["RefMasterDetail_Name"] == DBNull.Value ? null : reader["RefMasterDetail_Name"].ToString(),
                                    RefMasterDetail_ID = reader["RefMasterDetail_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    RefMasterDetail_IsActive = (bool)(reader["RefMasterDetail_IsActive"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["RefMasterDetail_IsActive"])),
                                };

                                refDetail.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                List<GNM_RefMasterDetailLocale> RefMasterDetailLocaleList = new List<GNM_RefMasterDetailLocale>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT RL.RefMaster_ID, RL.RefMasterDetail_ID, RL.Language_ID, R.RefMasterDetail_IsActive FROM GNM_RefMasterDetailLocale RL JOIN GNM_RefMasterDetail R ON RL.RefMasterDetail_ID=R.RefMasterDetail_ID";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_RefMasterDetailLocale
                                {
                                    RefMaster_ID = reader["RefMaster_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["RefMaster_ID"]),
                                    RefMasterDetail_ID = reader["RefMasterDetail_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                    Language_ID = reader["Language_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Language_ID"]),
                                    RefMasterDetail_IsActive = (bool)(reader["RefMasterDetail_IsActive"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["RefMasterDetail_IsActive"])),
                                };

                                RefMasterDetailLocaleList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }
                List<GNM_RefMaster> refMaster = new List<GNM_RefMaster>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_RefMaster";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_RefMaster
                                {
                                    RefMaster_ID = reader["RefMaster_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["RefMaster_ID"]),
                                    RefMaster_Name = reader["RefMaster_Name"] == DBNull.Value ? null : reader["RefMaster_Name"].ToString(),
                                };

                                refMaster.Add(refMasterDetailObj);
                            }
                        }
                    }
                }
                gParty = Party_List;//.Where(i => i.Company_ID == CompanyID);
                PartyLocale = PartyLocale_List.Where(lid => lid.Language_ID == LangID);// && lid.GNM_Party.Company_ID == CompanyID);
                gModel = Model_Detail;
                ModelLocale = ModelLocale_Detail.Where(lid => lid.Language_ID == LangID);
                RefMaster = refMaster;
                Brand = refDetail;
                BrandLocale = RefMasterDetailLocaleList.Where(lid => lid.Language_ID == LangID);
                List<WF_WFStepStatus> statusList = WFStepStatusList.ToList();
                List<GNM_Branch> branchlist = Branch_List.ToList();
                if (GenLangCode == UserLangCode)
                {

                    ServiceReq = from SR in SRequestAll
                                 join Party in gParty on SR.Party_ID equals Party.Party_ID into partyList
                                 from partyFinal in partyList.DefaultIfEmpty(new GNM_Party { Party_Name = "" })
                                 join d in branchlist on SR.Party_ID equals d.Branch_ID into oldbranch
                                 from newbranch in oldbranch.DefaultIfEmpty(new GNM_Branch { Branch_ID = 0 })
                                 join Model in gModel on SR.Model_ID equals Model.Model_ID into models
                                 from modelfinal in models.DefaultIfEmpty(new GNM_Model { Model_ID = 0, Model_Name = "" })
                                 join Rfid in Brand on SR.Brand_ID equals Rfid.RefMasterDetail_ID into Brands
                                 from BrandFinal in Brands.DefaultIfEmpty(new GNM_RefMasterDetail { RefMasterDetail_ID = 0, RefMasterDetail_Name = "" })
                                 join h in statusList on SR.CallStatus_ID equals h.WFStepStatus_ID
                                 select new ServiceRequestAgeing()
                                 {
                                     Region = CommonFunctionalities.getRegionName(Convert.ToInt32(userLanguageID), Convert.ToInt32(generalLanguageID), SR.Branch_ID, connString, LogException),
                                     CompanyName = CompanyList.Where(x => x.Company_ID == SR.Company_ID).Select(x => x.Company_Name).FirstOrDefault(),
                                     ServiceRequest_ID = SR.ServiceRequest_ID,
                                     Brand_Name = BrandFinal.RefMasterDetail_Name,
                                     Model_Name = modelfinal.Model_Name,
                                     Party_Name = SR.IsDealer == true ? newbranch.Branch_Name : partyFinal.Party_Name,
                                     SerialNumber = (SR.SerialNumber == null) ? "" : SR.SerialNumber,
                                     CallDateOrder = SR.CallDateAndTime,
                                     CallDateAndTime = SR.CallDateAndTime.ToString("dd-MMM-yyyy"),
                                     ServiceRequestNumber = SR.ServiceRequestNumber,
                                     status = h.WFStepStatus_Nm,
                                     //CallDateAndTime = SR.CallDateAndTime,
                                     AgeingHoursMinutes = Convert.ToInt32(localTime.Subtract(SR.CallDateAndTime).TotalMinutes),
                                     AgeingHours = Convert.ToInt32((Convert.ToInt32(localTime.Subtract(SR.CallDateAndTime).TotalMinutes)) / 60).ToString() + ":" + ((Convert.ToInt32((localTime.Subtract(SR.CallDateAndTime).TotalMinutes) % 60).ToString().Length == 1 ? "0" + Convert.ToInt32((localTime.Subtract(SR.CallDateAndTime).TotalMinutes) % 60).ToString() : Convert.ToInt32((localTime.Subtract(SR.CallDateAndTime).TotalMinutes) % 60).ToString()).ToString()),
                                     AgeingDays = (localTime.Subtract(SR.CallDateAndTime).Days).ToString(),
                                     Company_ID = SR.Company_ID,
                                     BranchName = Branch_List.Where(j => j.Branch_ID == SR.Branch_ID).Select(j => j.Branch_Name).FirstOrDefault(),
                                 };
                }
                else
                {

                    ServiceReq = from SR in SRequestAll
                                 join PartyL in PartyLocale on SR.Party_ID equals PartyL.Party_ID into PartyList
                                 from PartyFinal in PartyList.DefaultIfEmpty(new GNM_PartyLocale { Party_Name = "" })
                                 join d in branchlist on SR.Party_ID equals d.Branch_ID into oldbranch
                                 from newbranch in oldbranch.DefaultIfEmpty(new GNM_Branch { Branch_ID = 0 })
                                 join ModelL in ModelLocale on SR.Model_ID equals ModelL.Model_ID into ModelList
                                 from ModelFinal in ModelList.DefaultIfEmpty(new GNM_ModelLocale { Model_Name = "" })
                                 join BrandL in BrandLocale on SR.Brand_ID equals BrandL.RefMasterDetail_ID into BrandList
                                 from BrandFinal in BrandList.DefaultIfEmpty(new GNM_RefMasterDetailLocale { RefMasterDetail_Name = "" })
                                 join h in statusList on SR.CallStatus_ID equals h.WFStepStatus_ID
                                 select new ServiceRequestAgeing()
                                 {
                                     Region = CommonFunctionalities.getRegionName(Convert.ToInt32(userLanguageID), Convert.ToInt32(generalLanguageID), SR.Branch_ID, connString, LogException),
                                     CompanyName = CompanyList.Where(x => x.Company_ID == SR.Company_ID).Select(x => x.Company_Name).FirstOrDefault(),
                                     ServiceRequest_ID = SR.ServiceRequest_ID,
                                     Brand_Name = BrandFinal.RefMasterDetail_Name,
                                     Model_Name = ModelFinal.Model_Name,
                                     Party_Name = SR.IsDealer == true ? newbranch.Branch_Name : PartyFinal.Party_Name,
                                     CallDateOrder = SR.CallDateAndTime,
                                     SerialNumber = (SR.SerialNumber == null) ? "" : SR.SerialNumber,
                                     CallDateAndTime = SR.CallDateAndTime.ToString("dd-MMM-yyyy"),
                                     ServiceRequestNumber = SR.ServiceRequestNumber,
                                     status = h.WFStepStatus_Nm,
                                     // CallDateAndTime = SR.CallDateAndTime,
                                     AgeingHoursMinutes = Convert.ToInt32(localTime.Subtract(SR.CallDateAndTime).TotalMinutes),
                                     AgeingHours = Convert.ToInt32((Convert.ToInt32(localTime.Subtract(SR.CallDateAndTime).TotalMinutes)) / 60).ToString() + ":" + ((Convert.ToInt32((localTime.Subtract(SR.CallDateAndTime).TotalMinutes) % 60).ToString().Length == 1 ? "0" + Convert.ToInt32((localTime.Subtract(SR.CallDateAndTime).TotalMinutes) % 60).ToString() : Convert.ToInt32((localTime.Subtract(SR.CallDateAndTime).TotalMinutes) % 60).ToString()).ToString()),
                                     AgeingDays = (localTime.Subtract(SR.CallDateAndTime).Days).ToString(),
                                     Company_ID = SR.Company_ID,
                                     BranchName = Branch_List.Where(j => j.Branch_ID == SR.Branch_ID).Select(j => j.Branch_Name).FirstOrDefault(),
                                 };
                }
                iQServiceReq = ServiceReq.AsQueryable<ServiceRequestAgeing>();
                if (_search)
                {
                    Filters filtersOBJ = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    if (filtersOBJ.rules.Count() > 0)
                    {
                        for (int i = 0; i < filtersOBJ.rules.Count(); i++)
                        {
                            if (filtersOBJ.rules.ElementAt(i).field == "AgeingHoursMinutes")
                            {
                                filtersOBJ.rules.ElementAt(i).field = "AgeingHours";
                            }
                        }
                        iQServiceReq = iQServiceReq.FilterSearch<ServiceRequestAgeing>(filtersOBJ);
                    }
                }

                iQServiceReq = iQServiceReq.OrderByField<ServiceRequestAgeing>(sidx, sord);

                count = iQServiceReq.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                jsonobj = new
                {
                    TotalPages = total,
                    PageNo = page,
                    RecordCount = count,
                    rows = (from SR in iQServiceReq
                            select new
                            {
                                Region = SR.Region,
                                CompanyName = SR.CompanyName,
                                BranchName = SR.BranchName,
                                SR.ServiceRequest_ID,
                                ServiceRequestNumber = "<span key='" + SR.ServiceRequest_ID + "' style='cursor:pointer'  class='TxtHyperLink' >" + SR.ServiceRequestNumber + "</span>",//Server.HtmlEncode(SR.ServiceRequestNumber),
                                CallDate = SR.CallDateAndTime,
                                Party_Name = SR.Party_Name,
                                Brand_Name = SR.Brand_Name,
                                Model_Name = SR.Model_Name,
                                SerialNumber = (SR.SerialNumber == null || SR.SerialNumber == "") ? "" : SR.SerialNumber,
                                status = SR.status,
                                AgeingHoursMinutes = Convert.ToInt32((Convert.ToInt32(localTime.Subtract(SR.CallDateOrder).TotalMinutes)) / 60).ToString() + ":" + ((Convert.ToInt32((localTime.Subtract(SR.CallDateOrder).TotalMinutes) % 60).ToString().Length == 1 ? "0" + Convert.ToInt32((localTime.Subtract(SR.CallDateOrder).TotalMinutes) % 60).ToString() : Convert.ToInt32((localTime.Subtract(SR.CallDateOrder).TotalMinutes) % 60).ToString()).ToString()),
                                AgeingDays = SR.AgeingDays,
                                Company_ID = SR.Company_ID,
                            }).ToList().Paginate(page, rows),
                    AgeingIn = AgeingReportIn.ToUpper()
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonobj);
        }
        #endregion

        #region ExportAgeing
        /// <summary>
        /// ExportAgeing
        /// </summary>
        public static async Task<object> ExportAgeing(ExportAgeing_ExportList OBJ, string connString, int LogException)
        {
            int companyID = Convert.ToInt32(OBJ.Company_ID);
            int branchID = Convert.ToInt32(OBJ.Branch);
            int userid = OBJ.User_ID;
            DateTime localTime = DateTime.Now;
            //  int CompanyID = User.Company_ID;
            var results = default(dynamic);
            List<AgeingAnalysis> RSList = new List<AgeingAnalysis>();

            IEnumerable<HD_ServiceRequest> SRequestAll = null;
            IQueryable<ServiceRequestAgeing> iQServiceReq = null;
            IEnumerable<ServiceRequestAgeing> ServiceReq = null;
            IEnumerable<GNM_Party> gParty = null;
            IEnumerable<GNM_PartyLocale> PartyLocale = null;
            IEnumerable<GNM_Model> gModel = null;
            IEnumerable<GNM_ModelLocale> ModelLocale = null;
            IEnumerable<GNM_RefMaster> RefMaster = null;
            IEnumerable<GNM_RefMasterDetail> Brand = null;
            IEnumerable<GNM_RefMasterDetailLocale> BrandLocale = null;

            try
            {
                //Added by Manju M for HelpDesk CR-5 Changes 25-Aug-2015 -- Start                
                localTime = Common.LocalTime(Convert.ToInt32(OBJ.Branch.ToString()), DateTime.Now, connString);
                //Added by Manjunatha M for HelpDesk CR-5 Changes 25-Aug-2015 -- End
                int LangID = OBJ.Language_ID;
                string GenLangCode = OBJ.GeneralLanguageCode.ToString();
                string UserLangCode = OBJ.UserLanguageCode.ToString();
                int userLanguageID = Convert.ToInt32(OBJ.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(OBJ.GeneralLanguageID);

                Company companies = new Company();
                string CompanyNames = string.Empty;

                if (OBJ.Company != null && OBJ.Company != "")
                {
                    companies = JObject.Parse(Common.DecryptString(OBJ.Company)).ToObject<Company>();
                }

                Branch branchs = new Branch();

                if (OBJ.BranchObj != null && OBJ.BranchObj != "")
                {
                    branchs = JObject.Parse(Common.DecryptString(OBJ.BranchObj)).ToObject<Branch>();
                }
                List<HD_ServiceRequest> ServiceRequestList = new List<HD_ServiceRequest>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM HD_ServiceRequest";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new HD_ServiceRequest
                                {
                                    ServiceRequest_ID = reader["ServiceRequest_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["ServiceRequest_ID"]),
                                    ServiceRequestNumber = reader["ServiceRequestNumber"] == DBNull.Value ? string.Empty : reader["ServiceRequestNumber"].ToString(),
                                    ServiceRequestDate = reader["ServiceRequestDate"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(reader["ServiceRequestDate"]),
                                    Quotation_ID = reader["Quotation_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Quotation_ID"]),
                                    QuotationNumber = reader["QuotationNumber"] == DBNull.Value ? string.Empty : reader["QuotationNumber"].ToString(),
                                    JobCard_ID = reader["JobCard_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["JobCard_ID"]),
                                    JobCardNumber = reader["JobCardNumber"] == DBNull.Value ? string.Empty : reader["JobCardNumber"].ToString(),
                                    CallStatus_ID = reader["CallStatus_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CallStatus_ID"]),
                                    ParentIssue_ID = reader["ParentIssue_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ParentIssue_ID"]),
                                    Product_Unique_Number = reader["Product_Unique_Number"] == DBNull.Value ? string.Empty : reader["Product_Unique_Number"].ToString(),
                                    Party_ID = reader["Party_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Party_ID"]),
                                    PartyContactPerson_ID = reader["PartyContactPerson_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["PartyContactPerson_ID"]),
                                    Model_ID = reader["Model_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Model_ID"]),
                                    Brand_ID = reader["Brand_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Brand_ID"]),
                                    ProductType_ID = reader["ProductType_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ProductType_ID"]),
                                    SerialNumber = reader["SerialNumber"] == DBNull.Value ? string.Empty : reader["SerialNumber"].ToString(),
                                    ProductReading = reader["ProductReading"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ProductReading"]),
                                    IsUnderWarranty = reader["IsUnderWarranty"] != DBNull.Value && Convert.ToBoolean(reader["IsUnderWarranty"]),
                                    CallMode_ID = reader["CallMode_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CallMode_ID"]),
                                    CallPriority_ID = reader["CallPriority_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["CallPriority_ID"]),
                                    CallComplexity_ID = reader["CallComplexity_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CallComplexity_ID"]),
                                    CallDateAndTime = reader["CallDateAndTime"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(reader["CallDateAndTime"]),
                                    PromisedCompletionDate = reader["PromisedCompletionDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["PromisedCompletionDate"]),
                                    Region_ID = reader["Region_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Region_ID"]),
                                    CallDescription = reader["CallDescription"] == DBNull.Value ? string.Empty : reader["CallDescription"].ToString(),
                                    IssueArea_ID = reader["IssueArea_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["IssueArea_ID"]),
                                    IssueSubArea_ID = reader["IssueSubArea_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["IssueSubArea_ID"]),
                                    FunctionGroup_ID = reader["FunctionGroup_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["FunctionGroup_ID"]),
                                    IsUnderBreakDown = reader["IsUnderBreakDown"] != DBNull.Value && Convert.ToBoolean(reader["IsUnderBreakDown"]),
                                    QuestionaryLevel1_ID = reader["QuestionaryLevel1_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["QuestionaryLevel1_ID"]),
                                    QuestionaryLevel2_ID = reader["QuestionaryLevel2_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["QuestionaryLevel2_ID"]),
                                    QuestionaryLevel3_ID = reader["QuestionaryLevel3_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["QuestionaryLevel3_ID"]),
                                    DefectGroup_ID = reader["DefectGroup_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["DefectGroup_ID"]),
                                    DefectName_ID = reader["DefectName_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["DefectName_ID"]),
                                    RootCause = reader["RootCause"] == DBNull.Value ? string.Empty : reader["RootCause"].ToString(),
                                    InformationCollected = reader["InformationCollected"] == DBNull.Value ? string.Empty : reader["InformationCollected"].ToString(),
                                    CallClosureDateAndTime = reader["CallClosureDateAndTime"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["CallClosureDateAndTime"]),
                                    ClosureType_ID = reader["ClosureType_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ClosureType_ID"]),
                                    ClosingDescription = reader["ClosingDescription"] == DBNull.Value ? string.Empty : reader["ClosingDescription"].ToString(),
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    Branch_ID = reader["Branch_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Branch_ID"]),
                                    ModifiedDate = reader["ModifiedDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["ModifiedDate"]),
                                    ExpectedArrivalDateTime = reader["ExpectedArrivalDateTime"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["ExpectedArrivalDateTime"]),
                                    ActualArrivalDateTime = reader["ActualArrivalDateTime"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["ActualArrivalDateTime"]),
                                    ExpectedDepartureDateTime = reader["ExpectedDepartureDateTime"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["ExpectedDepartureDateTime"]),
                                    ActualDepartureDateTime = reader["ActualDepartureDateTime"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["ActualDepartureDateTime"]),
                                    ModifiedBy_ID = reader["ModifiedBy_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ModifiedBy_ID"]),
                                    Document_no = reader["Document_no"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Document_no"]),
                                    CaseType_ID = reader["CaseType_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CaseType_ID"]),
                                    ActionRemarks = reader["ActionRemarks"] == DBNull.Value ? string.Empty : reader["ActionRemarks"].ToString(),
                                    Product_ID = reader["Product_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Product_ID"]),
                                    CustomerRating = reader["CustomerRating"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["CustomerRating"]),
                                    FinancialYear = reader["FinancialYear"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["FinancialYear"]),
                                    IsCallBlocked = reader["IsCallBlocked"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IsCallBlocked"]),
                                    StockBlocking_ID = reader["StockBlocking_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["StockBlocking_ID"]),
                                    EnquiryStage_ID = reader["EnquiryStage_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["EnquiryStage_ID"]),
                                    SalesQuotation_ID = reader["SalesQuotation_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["SalesQuotation_ID"]),
                                    SalesQuotationNumber = reader["SalesQuotationNumber"] == DBNull.Value ? string.Empty : reader["SalesQuotationNumber"].ToString(),
                                    SalesOrder_ID = reader["SalesOrder_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["SalesOrder_ID"]),
                                    SalesOrderNumber = reader["SalesOrderNumber"] == DBNull.Value ? string.Empty : reader["SalesOrderNumber"].ToString(),
                                    SalesOrderDate = reader["SalesOrderDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["SalesOrderDate"]),
                                    CallOwner_ID = reader["CallOwner_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["CallOwner_ID"]),
                                    Current_AssignTo = reader["Current_AssignTo"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Current_AssignTo"]),
                                    ContractorID = reader["ContractorID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ContractorID"]),
                                    ContractorContactPerson_ID = reader["ContractorContactPerson_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ContractorContactPerson_ID"]),
                                    ScheduledType_ID = reader["ScheduledType_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ScheduledType_ID"]),
                                    NoofTechs = reader["NoofTechs"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["NoofTechs"]),
                                    ShiftHours = reader["ShiftHours"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ShiftHours"]),
                                    Flexi1 = reader["Flexi1"] == DBNull.Value ? string.Empty : reader["Flexi1"].ToString(),
                                    Flexi2 = reader["Flexi2"] == DBNull.Value ? string.Empty : reader["Flexi2"].ToString(),
                                    ResolutionTime = reader["ResolutionTime"] == DBNull.Value ? string.Empty : reader["ResolutionTime"].ToString(),
                                    ResponseTime = reader["ResponseTime"] == DBNull.Value ? string.Empty : reader["ResponseTime"].ToString(),
                                    AttachmentCount = reader["AttachmentCount"] == DBNull.Value ? (byte?)null : Convert.ToByte(reader["AttachmentCount"]),
                                    CustomerFeedbackDate = reader["CustomerFeedbackDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["CustomerFeedbackDate"]),
                                    IsNegetiveFeedback = reader["IsNegetiveFeedback"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IsNegetiveFeedback"]),
                                    IsDealer = reader["IsDealer"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IsDealer"]),
                                    IsWIPBay = reader["IsWIPBay"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IsWIPBay"]),
                                    IsDealerList = reader["IsDealerList"] == DBNull.Value ? (byte?)null : Convert.ToByte(reader["IsDealerList"]),
                                    ProductRateType = reader["ProductRateType"] == DBNull.Value ? (byte?)null : Convert.ToByte(reader["ProductRateType"]),
                                    ChildTicket_Sequence_ID = reader["ChildTicket_Sequence_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ChildTicket_Sequence_ID"]),
                                    ResponseTime24 = reader["ResponseTime24"] == DBNull.Value ? string.Empty : reader["ResponseTime24"].ToString(),
                                    ResolutionTime24 = reader["ResolutionTime24"] == DBNull.Value ? string.Empty : reader["ResolutionTime24"].ToString(),
                                };

                                ServiceRequestList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                List<WF_WFStepStatus> WFStepStatusList = new List<WF_WFStepStatus>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_WFStepStatus";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new WF_WFStepStatus
                                {
                                    WFStepStatus_ID = reader["WFStepStatus_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["WFStepStatus_ID"]),
                                    WFStepStatus_Nm = reader["WFStepStatus_Nm"] == DBNull.Value ? null : reader["WFStepStatus_Nm"].ToString(),
                                };

                                WFStepStatusList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }
                List<GNM_Branch> Branch_List = new List<GNM_Branch>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_Branch";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_Branch
                                {
                                    Branch_ID = reader["Branch_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Branch_ID"]),
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    Branch_Name = reader["Branch_Name"] == DBNull.Value ? string.Empty : reader["Branch_Name"].ToString(),
                                    Branch_ShortName = reader["Branch_ShortName"] == DBNull.Value ? string.Empty : reader["Branch_ShortName"].ToString(),
                                    Branch_ZipCode = reader["Branch_ZipCode"] == DBNull.Value ? string.Empty : reader["Branch_ZipCode"].ToString(),
                                    Country_ID = reader["Country_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Country_ID"]),
                                    State_ID = reader["State_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["State_ID"]),
                                    Branch_Phone = reader["Branch_Phone"] == DBNull.Value ? string.Empty : reader["Branch_Phone"].ToString(),
                                    Branch_Fax = reader["Branch_Fax"] == DBNull.Value ? string.Empty : reader["Branch_Fax"].ToString(),
                                    Branch_HeadOffice = reader["Branch_HeadOffice"] != DBNull.Value && Convert.ToBoolean(reader["Branch_HeadOffice"]),
                                    Branch_Active = reader["Branch_Active"] != DBNull.Value && Convert.ToBoolean(reader["Branch_Active"]),
                                    Branch_Address = reader["Branch_Address"] == DBNull.Value ? string.Empty : reader["Branch_Address"].ToString(),
                                    Branch_Location = reader["Branch_Location"] == DBNull.Value ? string.Empty : reader["Branch_Location"].ToString(),
                                    Branch_Email = reader["Branch_Email"] == DBNull.Value ? string.Empty : reader["Branch_Email"].ToString(),
                                    Branch_Mobile = reader["Branch_Mobile"] == DBNull.Value ? string.Empty : reader["Branch_Mobile"].ToString(),
                                    Branch_External = reader["Branch_External"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["Branch_External"]),
                                    TimeZoneID = reader["TimeZoneID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["TimeZoneID"]),
                                    Region_ID = reader["Region_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Region_ID"]),
                                    Currency_ID = reader["Currency_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["Currency_ID"]),
                                    LanguageID = reader["LanguageID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["LanguageID"]),
                                    IsOverTimeDWM = reader["IsOverTimeDWM"] == DBNull.Value ? (byte?)null : Convert.ToByte(reader["IsOverTimeDWM"]),
                                    Yearly_Sales_Target = reader["Yearly_Sales_Target"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["Yearly_Sales_Target"]),
                                    Rework_Target = reader["Rework_Target"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["Rework_Target"]),
                                    Cust_Satisfaction_Target = reader["Cust_Satisfaction_Target"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["Cust_Satisfaction_Target"]),
                                    RO_with_Rework_Target = reader["RO_with_Rework_Target"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["RO_with_Rework_Target"]),
                                    RO_with_Cust_Satisfaction_Target = reader["RO_with_Cust_Satisfaction_Target"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["RO_with_Cust_Satisfaction_Target"]),
                                    DueDays = reader["DueDays"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["DueDays"]),
                                    PayrollSystem_ID = reader["PayrollSystem_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["PayrollSystem_ID"]),
                                    MaxCarryOverHours = reader["MaxCarryOverHours"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["MaxCarryOverHours"]),
                                    ConsumedCarryOverByDate = reader["ConsumedCarryOverByDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["ConsumedCarryOverByDate"]),
                                    IsHourlyRateBranchWise = reader["IsHourlyRateBranchWise"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IsHourlyRateBranchWise"]),
                                    PayrollFileType_ID = reader["PayrollFileType_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["PayrollFileType_ID"]),
                                    Yearly_Parts_Target = reader["Yearly_Parts_Target"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["Yearly_Parts_Target"]),
                                    Variance_Percentage = reader["Variance_Percentage"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["Variance_Percentage"]),
                                    Variance_Value = reader["Variance_Value"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["Variance_Value"]),
                                    ETOExtensionHours = reader["ETOExtensionHours"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ETOExtensionHours"]),
                                    ETOMultiples = reader["ETOMultiples"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["ETOMultiples"]),
                                    BilledVsActualVariance_Percentage = reader["BilledVsActualVariance_Percentage"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["BilledVsActualVariance_Percentage"]),
                                    TypeofPayroll = reader["TypeofPayroll"] == DBNull.Value ? (byte?)null : Convert.ToByte(reader["TypeofPayroll"]),
                                    LessVariance_Percentage = reader["LessVariance_Percentage"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["LessVariance_Percentage"]),
                                    LessVariance_Value = reader["LessVariance_Value"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["LessVariance_Value"]),
                                    IIMoreVariance_Percentage = reader["IIMoreVariance_Percentage"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["IIMoreVariance_Percentage"]),
                                    IIMoreVariance_Value = reader["IIMoreVariance_Value"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["IIMoreVariance_Value"]),
                                    IILessVariance_Percentage = reader["IILessVariance_Percentage"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["IILessVariance_Percentage"]),
                                    IILessVariance_Value = reader["IILessVariance_Value"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["IILessVariance_Value"]),
                                    WorkingMinutes = reader["WorkingMinutes"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["WorkingMinutes"])
                                };

                                Branch_List.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                List<GNM_Party> Party_List = new List<GNM_Party>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_Party";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_Party
                                {
                                    Party_ID = reader["Party_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Party_ID"]),
                                    Party_Name = reader["Party_Name"] == DBNull.Value ? null : reader["Party_Name"].ToString(),
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                };

                                Party_List.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                List<GNM_PartyLocale> PartyLocale_List = new List<GNM_PartyLocale>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT PL.Party_ID, PL.Party_Name, PL.Language_ID, P.Company_ID  FROM GNM_PartyLocale PL JOIN GNM_Party P ON PL.Party_ID=P.Party_ID";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_PartyLocale
                                {
                                    Party_ID = reader["Party_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Party_ID"]),
                                    Language_ID = reader["Language_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Language_ID"]),
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    Party_Name = reader["Party_Name"] == DBNull.Value ? null : reader["Party_Name"].ToString(),
                                };

                                PartyLocale_List.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                List<GNM_Model> Model_Detail = new List<GNM_Model>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_Model";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_Model
                                {
                                    Model_IsActive = (bool)(reader["Model_IsActive"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["Model_IsActive"])),
                                    Model_ID = reader["Model_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Model_ID"]),
                                };

                                Model_Detail.Add(refMasterDetailObj);
                            }
                        }
                    }
                }
                List<GNM_ModelLocale> ModelLocale_Detail = new List<GNM_ModelLocale>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT ML.Language_ID, ML.Model_ID, M.Model_IsActive FROM GNM_ModelLocale ML JOIN GNM_Model M ON ML.Model_ID=M.Model_ID";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_ModelLocale
                                {
                                    Model_IsActive = (bool)(reader["Model_IsActive"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["Model_IsActive"])),
                                    Model_ID = reader["Model_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Model_ID"]),
                                    Language_ID = reader["Language_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Language_ID"]),
                                };

                                ModelLocale_Detail.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                List<GNM_RefMasterDetail> refDetail = new List<GNM_RefMasterDetail>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_RefMasterDetail";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_RefMasterDetail
                                {
                                    RefMaster_ID = reader["RefMaster_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["RefMaster_ID"]),
                                    RefMasterDetail_Name = reader["RefMasterDetail_Name"] == DBNull.Value ? null : reader["RefMasterDetail_Name"].ToString(),
                                    RefMasterDetail_ID = reader["RefMasterDetail_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    RefMasterDetail_IsActive = (bool)(reader["RefMasterDetail_IsActive"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["RefMasterDetail_IsActive"])),
                                };

                                refDetail.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                List<GNM_RefMasterDetailLocale> RefMasterDetailLocaleList = new List<GNM_RefMasterDetailLocale>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT RL.RefMaster_ID, RL.RefMasterDetail_ID, RL.Language_ID, R.RefMasterDetail_IsActive FROM GNM_RefMasterDetailLocale RL JOIN GNM_RefMasterDetail R ON RL.RefMasterDetail_ID=R.RefMasterDetail_ID";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_RefMasterDetailLocale
                                {
                                    RefMaster_ID = reader["RefMaster_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["RefMaster_ID"]),
                                    RefMasterDetail_ID = reader["RefMasterDetail_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                    Language_ID = reader["Language_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Language_ID"]),
                                    RefMasterDetail_IsActive = (bool)(reader["RefMasterDetail_IsActive"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["RefMasterDetail_IsActive"])),
                                };

                                RefMasterDetailLocaleList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }
                List<GNM_RefMaster> refMaster = new List<GNM_RefMaster>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_RefMaster";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_RefMaster
                                {
                                    RefMaster_ID = reader["RefMaster_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["RefMaster_ID"]),
                                    RefMaster_Name = reader["RefMaster_Name"] == DBNull.Value ? null : reader["RefMaster_Name"].ToString(),
                                };

                                refMaster.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                List<GNM_Company> CompanyList = new List<GNM_Company>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_Company";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_Company
                                {
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    Company_Name = reader["Company_Name"] == DBNull.Value ? null : reader["Company_Name"].ToString(),
                                };

                                CompanyList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }
                List<GNM_CompParam> CompParamList = new List<GNM_CompParam>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_CompParam";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_CompParam
                                {
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    Param_Name = reader["Param_Name"] == DBNull.Value ? null : reader["Param_Name"].ToString(),
                                    Param_value = reader["Param_value"] == DBNull.Value ? null : reader["Param_value"].ToString(),
                                };

                                CompParamList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }
                // SRequestAll = SRClient.HD_ServiceRequest.Where(sr => sr.Company_ID == CompanyID && sr.CallClosureDateAndTime == null);
                SRequestAll = (from a in ServiceRequestList.AsEnumerable()
                               join b in companies.Companys on a.Company_ID equals b.ID
                               where a.CallClosureDateAndTime == null && a.CallStatus_ID != (WFStepStatusList.Where(s => s.WFStepStatus_Nm.ToLower().Contains("Closed".ToLower())).Select(s => s.WFStepStatus_ID).FirstOrDefault())
                               select a).ToList();

                string BranchNames = string.Empty;
                for (int i = 0; i < branchs.Branchs.Count; i++)
                {
                    int ID = branchs.Branchs[i].ID;
                    BranchNames = BranchNames + Branch_List.Where(a => a.Branch_ID == ID).FirstOrDefault().Branch_Name + ", ";
                }
                BranchNames = BranchNames.Remove(BranchNames.LastIndexOf(','), 1);
                string IsAllBranch = Convert.ToString(OBJ.IsAllBranch);
                if (IsAllBranch == "checked")
                {
                    BranchName = CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "all").ToString();
                }
                else
                {
                    BranchName = BranchNames;
                }

                SRequestAll = (from a in SRequestAll
                               join b in branchs.Branchs on a.Branch_ID equals b.ID
                               select a).ToList();

                //---------------------------------------------------------
                //gParty = PartyMasterClient.GNM_Party.Where(i => i.Company_ID == CompanyID);
                gParty = (from a in Party_List.AsEnumerable()
                          join b in companies.Companys on a.Company_ID equals b.ID
                          select a).ToList();

                PartyLocale = (from a in PartyLocale_List.AsEnumerable()
                               join b in companies.Companys on a.Company_ID equals b.ID
                               where a.Language_ID == LangID
                               select a).ToList();

                //PartyLocale = PartyMasterClient.GNM_PartyLocale.Where(lid => lid.Language_ID == LangID && lid.GNM_Party.Company_ID == CompanyID);
                gModel = Model_Detail;
                ModelLocale = ModelLocale_Detail.Where(lid => lid.Language_ID == LangID);
                RefMaster = refMaster;
                Brand = refDetail;
                BrandLocale = RefMasterDetailLocaleList.Where(lid => lid.Language_ID == LangID);
                List<WF_WFStepStatus> statusList = WFStepStatusList.ToList();
                List<GNM_Branch> branchlist = Branch_List.ToList();
                if (GenLangCode == UserLangCode)
                {
                    ServiceReq = from SR in SRequestAll
                                 join Party in gParty on SR.Party_ID equals Party.Party_ID into partyList
                                 from partyFinal in partyList.DefaultIfEmpty(new GNM_Party { Party_Name = "" })
                                 join d in branchlist on SR.Party_ID equals d.Branch_ID into oldbranch
                                 from newbranch in oldbranch.DefaultIfEmpty(new GNM_Branch { Branch_ID = 0 })
                                 join Model in gModel on SR.Model_ID equals Model.Model_ID into models
                                 from modelfinal in models.DefaultIfEmpty(new GNM_Model { Model_ID = 0, Model_Name = "" })
                                 join Rfid in Brand on SR.Brand_ID equals Rfid.RefMasterDetail_ID into Brands
                                 from BrandFinal in Brands.DefaultIfEmpty(new GNM_RefMasterDetail { RefMasterDetail_ID = 0, RefMasterDetail_Name = "" })
                                 join h in statusList on SR.CallStatus_ID equals h.WFStepStatus_ID
                                 select new ServiceRequestAgeing()
                                 {
                                     Region = CommonFunctionalities.getRegionName(Convert.ToInt32(userLanguageID), Convert.ToInt32(generalLanguageID), SR.Branch_ID, connString, LogException),
                                     CompanyName = (CompanyList.Where(c => c.Company_ID == SR.Company_ID).Select(c => c.Company_Name).FirstOrDefault()).ToString(),
                                     BranchName = (Branch_List.Where(c => c.Branch_ID == SR.Branch_ID).Select(c => c.Branch_Name).FirstOrDefault()).ToString(),
                                     ServiceRequest_ID = SR.ServiceRequest_ID,
                                     Brand_Name = BrandFinal.RefMasterDetail_Name,
                                     Model_Name = modelfinal.Model_Name,
                                     Party_Name = SR.IsDealer == true ? newbranch.Branch_Name : partyFinal.Party_Name,
                                     SerialNumber = (SR.SerialNumber == null) ? "" : SR.SerialNumber,
                                     CallDateOrder = SR.CallDateAndTime,
                                     CallDateAndTime = SR.CallDateAndTime.ToString("dd-MMM-yyyy"),
                                     ServiceRequestNumber = SR.ServiceRequestNumber,
                                     status = h.WFStepStatus_Nm,
                                     AgeingHoursMinutes = Convert.ToInt32(localTime.Subtract(SR.CallDateAndTime).TotalMinutes),
                                     AgeingHours = Convert.ToInt32((Convert.ToInt32(localTime.Subtract(SR.CallDateAndTime).TotalMinutes)) / 60).ToString() + ":" + ((Convert.ToInt32((localTime.Subtract(SR.CallDateAndTime).TotalMinutes) % 60).ToString().Length == 1 ? "0" + Convert.ToInt32((localTime.Subtract(SR.CallDateAndTime).TotalMinutes) % 60).ToString() : Convert.ToInt32((localTime.Subtract(SR.CallDateAndTime).TotalMinutes) % 60).ToString()).ToString()),
                                     AgeingDays = (localTime.Subtract(SR.CallDateAndTime).Days).ToString(),
                                 };
                }
                else
                {
                    ServiceReq = from SR in SRequestAll
                                 join PartyL in PartyLocale on SR.Party_ID equals PartyL.Party_ID into PartyList
                                 from PartyFinal in PartyList.DefaultIfEmpty(new GNM_PartyLocale { Party_Name = "" })
                                 join ModelL in ModelLocale on SR.Model_ID equals ModelL.Model_ID into ModelList
                                 from ModelFinal in ModelList.DefaultIfEmpty(new GNM_ModelLocale { Model_Name = "" })
                                 join BrandL in BrandLocale on SR.Brand_ID equals BrandL.RefMasterDetail_ID into BrandList
                                 from BrandFinal in BrandList.DefaultIfEmpty(new GNM_RefMasterDetailLocale { RefMasterDetail_Name = "" })
                                 join h in statusList on SR.CallStatus_ID equals h.WFStepStatus_ID
                                 select new ServiceRequestAgeing()
                                 {
                                     Region = CommonFunctionalities.getRegionName(Convert.ToInt32(userLanguageID), Convert.ToInt32(generalLanguageID), SR.Branch_ID, connString, LogException),
                                     CompanyName = (CompanyList.Where(c => c.Company_ID == SR.Company_ID).Select(c => c.Company_Name).FirstOrDefault()).ToString(),
                                     BranchName = (Branch_List.Where(c => c.Branch_ID == SR.Branch_ID).Select(c => c.Branch_Name).FirstOrDefault()).ToString(),
                                     ServiceRequest_ID = SR.ServiceRequest_ID,
                                     Brand_Name = BrandFinal.RefMasterDetail_Name,
                                     Model_Name = ModelFinal.Model_Name,
                                     Party_Name = PartyFinal.Party_Name,
                                     CallDateOrder = SR.CallDateAndTime,
                                     SerialNumber = (SR.SerialNumber == null) ? "" : SR.SerialNumber,
                                     CallDateAndTime = SR.CallDateAndTime.ToString("dd-MMM-yyyy"),
                                     ServiceRequestNumber = SR.ServiceRequestNumber,
                                     status = h.WFStepStatus_Nm,
                                     AgeingHoursMinutes = Convert.ToInt32(localTime.Subtract(SR.CallDateAndTime).TotalMinutes),
                                     AgeingHours = Convert.ToInt32((Convert.ToInt32(localTime.Subtract(SR.CallDateAndTime).TotalMinutes)) / 60).ToString() + ":" + ((Convert.ToInt32((localTime.Subtract(SR.CallDateAndTime).TotalMinutes) % 60).ToString().Length == 1 ? "0" + Convert.ToInt32((localTime.Subtract(SR.CallDateAndTime).TotalMinutes) % 60).ToString() : Convert.ToInt32((localTime.Subtract(SR.CallDateAndTime).TotalMinutes) % 60).ToString()).ToString()),
                                     AgeingDays = (localTime.Subtract(SR.CallDateAndTime).Days).ToString(),
                                 };
                }
                iQServiceReq = ServiceReq.AsQueryable();

                DataTable dt = new DataTable();
                int cnt = 0;

                dt.Columns.Add(@CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "Region").ToString());
                dt.Columns.Add(@CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "Company").ToString());
                dt.Columns.Add(@CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "Branch").ToString());


                dt.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "ServiceRequestNumber").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "Date").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "PartyName").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "Brand").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "model").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "serialnumber").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "status").ToString());
                DataTable dtAlignment = new DataTable();

                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "Region").ToString());
                dtAlignment.Columns.Add("CompanyName");
                dtAlignment.Columns.Add("BranchName");


                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "ServiceRequestNumber").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "Date").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "PartyName").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "Brand").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "model").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "serialnumber").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "status").ToString());
                var SRArray = iQServiceReq.ToList();
                cnt = SRArray.Count();
                string AgeingReportIn = CompParamList.Where(i => i.Company_ID == OBJ.Company_ID && i.Param_Name.ToUpper() == "AGEINGREPORT").Select(i => i.Param_value).FirstOrDefault();
                if (AgeingReportIn.ToUpper() == "HOURS")
                {
                    dt.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "AgeingHours").ToString());
                    dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "AgeingHours").ToString());
                    for (int i = 0; i < cnt; i++)
                    {
                        dt.Rows.Add(SRArray[i].Region,
                            SRArray[i].CompanyName,
                            SRArray[i].BranchName,
                            SRArray[i].ServiceRequestNumber,
                            SRArray[i].CallDateAndTime,
                            SRArray[i].Party_Name,
                            SRArray[i].Brand_Name,
                            SRArray[i].Model_Name,
                            SRArray[i].SerialNumber,
                            SRArray[i].status,
                            SRArray[i].AgeingHours);
                    }
                }
                else if (AgeingReportIn.ToUpper() == "DAYS")
                {
                    dt.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "AgeingDays").ToString());
                    dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "AgeingDays").ToString());
                    for (int i = 0; i < cnt; i++)
                    {
                        dt.Rows.Add(SRArray[i].Region,
                            SRArray[i].CompanyName,
                         SRArray[i].BranchName,
                         SRArray[i].ServiceRequestNumber,
                            SRArray[i].CallDateAndTime,
                            SRArray[i].Party_Name,
                            SRArray[i].Brand_Name,
                            SRArray[i].Model_Name,
                            SRArray[i].SerialNumber,
                            SRArray[i].status,
                            SRArray[i].AgeingDays);
                    }
                }
                dtAlignment.Rows.Add(0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 2);
                DataTable DateRange = new DataTable();
                DataSet DS = new DataSet();
                //  DateRange.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "Branch").ToString());
                //  DateRange.Rows.Add(Session["BranchName"].ToString());
                //ReportExport.Export(exprtType, dt, DateRange, dtAlignment, "AgeingAnalysis", CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "AgeingAnalysisReport").ToString());
                //ReportExportCR5.Export(OBJ.exprtType, dt, DateRange, DS, dtAlignment, "AgeingAnalysis", CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "AgeingAnalysisReport").ToString());
                ReportExportList exportList = new ReportExportList
                {
                    Branch = OBJ.BranchID,
                    GeneralLanguageID = OBJ.GeneralLanguageID,
                    UserLanguageID = OBJ.UserLanguageID,
                    Company_ID = Convert.ToInt32(OBJ.Company_ID),
                    UserCulture = OBJ.UserCulture,
                    Options = DateRange,
                    exprtType = OBJ.exprtType,
                    Alignment = dtAlignment,
                    FileName = "AgeingAnalysis",
                    Header = CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "AgeingAnalysisReport").ToString(),
                    dt = dt,
                };
                results = await ReportExport.Export(exportList, connString, LogException);
                //gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"].ToString()), branchID, User.User_ID, Common.GetObjectID("AgeingAnalysisReport"), 0, 0, 0, "Ageing Analysis Report-Export ", false, Convert.ToInt32(Session["MenuID"]), Convert.ToDateTime(Session["LoggedINDateTime"]));
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return results.Value;
        }
        #endregion
    }

    // Properties

    public partial class AgeingAnalysisReport_InitialSetupList
    {
        public int ObjectID { get; set; }
        public int User_ID { get; set; }
    }
    public class TimeSlotFilter
    {
        public byte Type { get; set; }
        public int Min { get; set; }
        public int Max { get; set; }
    }

    public partial class Branch_List
    {

        public int Branch_ID { get; set; }
        public int Company_ID { get; set; }
        public string Branch_Name { get; set; }
    }

    public partial class BranchLocale_List
    {

        public int Branch_ID { get; set; }
        public int Company_ID { get; set; }
        public string Branch_Name { get; set; }
    }

    public partial class GNM_BranchLocale
    {
        public int Branch_Locale_ID { get; set; }
        public int Branch_ID { get; set; }
        public int Language_ID { get; set; }
        public string Branch_Name { get; set; }
        public string Branch_Address { get; set; }
        public string Branch_Location { get; set; }
        public string Branch_ShortName { get; set; }
    }

    public partial class EmployeeBranch_List
    {
        public int EmployeeBranch_ID { get; set; }
        public int CompanyEmployee_ID { get; set; }
        public int Branch_ID { get; set; }
        public Nullable<bool> IsDefault { get; set; }

    }

    public partial class LoadBranchDDList
    {
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int Employee_ID { get; set; }
        public string UserLanguageCode { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string Company_ID { get; set; }
    }

    public partial class AgeingAnalysis_SelectList
    {
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public string UserLanguageCode { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string FromDate { get; set; }
        public string ToDate { get; set; }
        public string UserCulture { get; set; }
        public string branchIDs { get; set; }
        public string Branch { get; set; }
        public string CompanyIDs { get; set; }
        public int Company_ID { get; set; }
    }

    public partial class AgeingAnalysis_SelectSlotWiseList
    {
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public string UserLanguageCode { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string FromDate { get; set; }
        public string ToDate { get; set; }
        public string UserCulture { get; set; }
        public string branchIDs { get; set; }
        public string Branch { get; set; }
        public string CompanyIDs { get; set; }
        public string BranchData { get; set; }
        public int Hours { get; set; }
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public int Language_ID { get; set; }
    }

    public class AgeingAnalysis
    {
        public string Ageing { get; set; }
        public int Count { get; set; }
    }

    public class ServiceRequestAgeing
    {
        public int ServiceRequest_ID { get; set; }
        public string ServiceRequestNumber { get; set; }
        public DateTime CallDateOrder { get; set; }
        public string CallDateAndTime { get; set; }
        public string Party_Name { get; set; }
        public string Brand_Name { get; set; }
        public string Model_Name { get; set; }
        public string SerialNumber { get; set; }
        public string FinancialYear { get; set; }
        public string Lock { get; set; }
        public string status { get; set; }
        public string QuotationNumber { get; set; }
        public string JobNumber { get; set; }
        public int AgeingHoursMinutes { get; set; }
        public string AgeingHours { get; set; }
        public string AgeingDays { get; set; }
        public string ProductType_Name { get; set; }
        public DateTime CallDateAndTimeDate { get; set; }
        public int AgeingDaysInt { get; set; }
        public string ActionRemarks { get; set; }
        public string NotesDescription { get; set; }
        public string Name { get; set; }
        public string Department { get; set; }
        public string QueryEscalatedTo { get; set; }
        public string CompanyName { get; set; } // Added by Venkateshwari.. CR 5 Changes ---- 10_sep-2015.
        public string BranchName { get; set; } // Added by Venkateshwari  CR 5 Changes QA Correction -29-Sep-2015
        public int Company_ID { get; set; } // Added by Venkateshwari.. CR 5 Changes ---- 10_sep-2015.
        public string Region { get; set; }

    }
    public class WF_WFStepStatus
    {
        public int WFStepStatus_ID { get; set; }

        public string WFStepStatus_Nm { get; set; }

        public string StepStatusCode { get; set; }
    }
    public partial class ExportAgeing_ExportList
    {
        public DataTable Options { get; set; }
        public DataTable dt { get; set; }
        public DataTable Alignment { get; set; }
        public string FileName { get; set; }
        public string Header { get; set; }
        public int exprtType { get; set; }
        public int User_ID { get; set; }
        public int Language_ID { get; set; }
        public string UserCulture { get; set; }
        public string ExportTitle { get; set; }
        public string SLAType { get; set; }
        public string SLAFilterType { get; set; }
        public string fromdateFromSLAreport { get; set; }
        public string todateFromSLAreport { get; set; }
        public string SelectedBrachinSLAReport { get; set; }
        public string mode { get; set; }
        public string Type { get; set; }
        public string frmdate { get; set; }
        public string todate { get; set; }
        public string Company { get; set; }
        public string BranchID { get; set; }
        public int Company_ID { get; set; }
        public string BranchObj { get; set; }
        public string Branch { get; set; }
        public string IsAllBranch { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public string UserLanguageCode { get; set; }
        public string GeneralLanguageCode { get; set; }
    }
}
