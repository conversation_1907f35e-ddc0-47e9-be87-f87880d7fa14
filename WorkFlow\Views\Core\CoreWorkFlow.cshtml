﻿
@{
    ViewBag.Title = "CoreMainView";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>WorkFlow</title>
    <style>
        .body
        {
            font-size: 12px;
            font-family: Cambria;
        }
    </style>
    <link href="~/Content/jquery.jqplot.min.css" rel="stylesheet" />

    <link href="~/Content/ui.jqgrid.css" rel="stylesheet" />
    <link href="~/Content/Styles/Basic.css" rel="stylesheet" />

    <script src="~/Scripts/JQuery/jquery-1.8.2.js"></script>
    <script src="~/Scripts/JQuery/jquery-ui-1.9.0.custom.min.js"></script>
    <script src="~/Scripts/JQGrid/jquery.jqGrid.min.js"></script>
    <script src="~/Scripts/JQGrid/grid.locale-en.js"></script>
    <script src="~/Scripts/Common/Common.js"></script>
    <script src="~/Scripts/Common/FieldSearch.js" type="text/javascript"></script>
    <script src="~/Scripts/JQuery/jquery.nestedAccordion.js"></script>
    <script src="~/Scripts/JQuery/fullcalendar.js"></script>
    <script src="~/Scripts/jquery-ui-timepicker-addon.js"></script>


    <script type="text/javascript">
        var RoleAccess = $.parseJSON(unescape('@Session["RoleAccess"]'));
        $(document).ready(function () {
           
            var editlabel = '@HttpContext.GetGlobalResourceObject("Resource_en", "Edit")';
            var deletelabel = '@HttpContext.GetGlobalResourceObject("Resource_en", "Delete")';

            //Grids variables
            var WFGrid = $('#WFGrid');
            var WFStepsGrid = $('#WFStepsGrid');
            var WFStepLinkGrid = $('#WFStepLinkGrid');
            var WFActionGrid = $('#WFActionGrid');
            var WFRolesGrid = $('#WFRolesGrid');
            var WFRoleUserGrid = $('#WFRoleUserGrid');
            $('#tabWFHdr').tabs();
            //DropDown Data
            var WFStepTypeOptions = "";
            var WFStepStatusOptions = "";
            var WFRoleNmOptions = "";
            var WFExternalCompany = "";
            var WFParentWorkFlow = "";
            var WFStepLinkRow = "";
            var WFChildObject = "";
            var WFFields = "";
            var separator = { sepclass: 'ui-separator', sepcontent: '' };
            //$('button').button();

            //--------------Work Flow-------------------------------------------------------------
            WFGrid.jqGrid({
                url: $.url('/WorkFlow/SelectWorkFlow'),
                datatype: 'json',
                mtype: 'POST',
                colNames: ['@HttpContext.GetGlobalResourceObject("Resource_en", "WorkFlowID")', '@HttpContext.GetGlobalResourceObject("Resource_en", "WorkFlowName")', '@HttpContext.GetGlobalResourceObject("Resource_en", "FilterAllQueBranch")', ''],
                colModel: [
                    { name: 'WorkFlow_ID', advncName: 'WorkFlow ID', align: 'right', index: 'WorkFlow_ID', width: 100, key: true, advanceSearch: true, advncOptions: { name: 'WorkFlow ID', IsInteger: true, IsMandatory: true } },
                    { name: 'WorkFlow_Name', index: 'WorkFlow_Name', advncName: 'WorkFlow Name', align: 'left', index: 'WorkFlow_Name', width: 400, advanceSearch: true, advncOptions: { name: 'WorkFlow Name', IsInteger: false, IsMandatory: true } },
                    { name: 'IsAllQueBranchFilter', index: 'IsAllQueBranchFilter', index: 'IsAllQueBranchFilter', width: 180 },
                    { name: 'WorkFlow', sortable: false, editable: false, width: 30, align: 'center', search: false }, ],
                pager: '#WFpager',
                rowNum: '20',
                rowList: [5, 10, 15, 25,20],
                viewrecords: true,
                sortname: 'WorkFlow_ID',
                sortorder: 'asc',
                caption: '@HttpContext.GetGlobalResourceObject("Resource_en", "WorkFlow")',
                recordpos: 'right',
                jsonReader: {
                        root: "rows",
                        page: "page",
                        total: "total",
                        records: "records",
                        repeatitems: false
                    },
                width: '100%',
                height: '100%',
                loadComplete: function () {
                        //Define WorkFlow
                        
                        try {
                            $('.DefineWFImg').click(function () {
                                var WFID = $(this).attr('key');
                                $('#hdnWFID').attr('value', WFID);
                                var WFName = $(this).attr('WFName');
                                $.LoadDetails();
                                $('#WFDetailTabs').tabs("select", 0);
                                $('#WFDetail').dialog({ modal: true, width: window.screen.availWidth - 100, height: window.screen.availHeight / 2, title: WFName, position: [$(this).offset().left + 100, $(this).offset().top + 20], });
                            });

                        WFGrid.makeRowEditable('WFEdtImg');
                        WFGrid.checkable('WFDelChkImg');
                        var filter = $('#WFGrid').getGridParam('postData').filters;
                        if (filter == undefined) {
                            $('#gs_WorkFlow_ID').focus();
                        }
                        $('.WFEdtImg').click(function () {
                            var id = $($(this).parent('td').parent('tr')).attr('id');
                            WFGrid.txtUniqueness(id, 'WorkFlow_Name', 'WorkFlow Name', $.url('/WorkFlow/WFExists'));
                        });
                    }
                    catch (ex) {
                    }
                }
            });

            //WorkFlow Navigator
            WFGrid.navGrid('#WFpager', { view: false, add: false, edit: false, del: false, search: false, refresh: false });

            //WorkFlow Search ToolBar
            WFGrid.jqGrid('filterToolbar', {
                searchOnEnter: true, stringResult: true,
                beforeSearch: function () {
                    var f = WFGrid.getGridParam('postData').filters;
                    if (f = undefined)
                        f = $.EncryptString(f);
                    WFGrid.setGridParam({ search: true, postData: { filters: f } });
                    WFGrid.trigger('reloadGrid');
                },
                afterSearch: function () {
                    var f = WFGrid.getGridParam('postData').filters;
                    if (f != undefined)
                        f = $.DecryptString(f);
                    WFGrid.setGridParam({ search: true, postData: { filters: f } });
                }
            });

            //Adding refresh Button to Grid
            WFGrid.navButtonAdd('#WFpager', {
                buttonicon: 'ui-icon-refresh',
                caption: '',
                title: 'Refresh',
                position: 'last',
                onClickButton: function () {
                    WFGrid.trigger('reloadGrid');
                }
            }).navSeparatorAdd("#WFpager", separator);

            WFGrid.jqGrid();

            //-----WF Steps -----------------------------------------------------------------------
           
            WFStepsGrid.jqGrid({
                colNames: [editlabel, deletelabel, '', '@HttpContext.GetGlobalResourceObject("Resource_en", "StepName")', '@HttpContext.GetGlobalResourceObject("Resource_en", "StepType")', '@HttpContext.GetGlobalResourceObject("Resource_en", "StepStatus")', '@HttpContext.GetGlobalResourceObject("Resource_en", "IsActive")'],
                colModel: [{ name: 'edit', sortable: false, editable: false, width: 50, align: 'center', search: false },
                    { name: 'delete', sortable: false, editable: false, width: 50, align: 'center', search: false, hidden: (!RoleAccess.IsDelete) },
                    { name: 'WFSteps_ID', index: 'WFSteps_ID', width: '-1', editable: false, hidden: true, key: true, advanceSearch: true, advncOptions: { name: 'WorkFlow ID', IsInteger: true, IsMandatory: true } },
                    { name: 'WFStep_Name', index: 'WFStep_Name', align: 'left', advncName: 'WorkFlow Step Name', index: 'WFStep_Name', width: 200, editable: true, edittype: 'text', editoptions: { readonly: false, maxlength: '50' } },
                    { name: 'WFStepType_Nm', index: 'WFStepType_Nm', advncName: 'WorkFlow Step Type', index: 'WFStepType_Nm', width: 150, editable: true, edittype: 'select', editoptions: { value: function () { return WFStepTypeOptions } } },
                    { name: 'WFStepStatus_Nm', index: 'WFStepStatus_Nm', advncName: 'WorkFlow Step Status', index: 'WFStepStatus_Nm', width: 150, editable: true, edittype: 'select', editoptions: { value: function () { return WFStepStatusOptions } } },
                    { name: 'WFStep_IsActive', index: 'WFStep_IsActive', advncName: 'Is Active', index: 'WFStep_IsActive', width: 100, editable: true, edittype: 'checkbox', editoptions: { value: "Yes:No", dataEvents: [{ type: 'click', fn: function () { $(this).check(); } }] } },
                ],
                pager: '#WFStepspager',
                rowNum: 5,
                rowList: [5, 10, 15, 25],
                viewrecords: true,
                sortname: 'WFSteps_ID',
                sortorder: 'asc',
                caption: '@HttpContext.GetGlobalResourceObject("Resource_en", "WorkFlowSteps")',
                width: '100%',
                height: '100%',
                datatype: 'json',
                mtype: 'POST',
                jsonReader: {
                    root: "rows",
                    page: "page",
                    total: "total",
                    records: "records",
                    WFName: "WFName",
                    WFStatusType: "WFStatusType",
                    repeatitems: false
                },

                loadComplete: function (data) {
                    
                    WFStepTypeOptions = data.WFStepType;
                    WFStepStatusOptions = data.WFStepStatus;
                    WFStepsGrid.makeRowEditable('WFStepsEdtImg', RoleAccess.IsEdit);
                    WFStepsGrid.checkable('WFStepsDelChkImg');
                    $('.WFStepsEdtImg').click(function () {
                        var WorkFlowID = $('#hdnWFID').val();
                        var id = $($(this).parent('td').parent('tr')).attr('id');
                        var primID = WFStepsGrid.getCell(id, 'WFSteps_ID');
                        primID = primID == '' ? 0 : primID;
                        WFStepsGrid.txtUniqueness(id, 'WFStep_Name', 'Step', $.url('/WorkFlow/CheckStepExists?primID=' + primID + '&id=' + WorkFlowID));
                    });
                }
            });

            //WFStepGrid Navigator
            WFStepsGrid.navGrid('#WFStepspager', { view: false, add: false, edit: false, del: false, search: false, refresh: false });
            //WorkFlow Search ToolBar
            //WorkFlow Search ToolBar
            WFStepsGrid.jqGrid('filterToolbar', {
                searchOnEnter: true, stringResult: true,
                beforeSearch: function () {
                    var f = WFStepsGrid.getGridParam('postData').filters;
                    if (f = undefined)
                        f = $.EncryptString(f);
                    WFStepsGrid.setGridParam({ search: true, postData: { filters: f } });
                    WFStepsGrid.trigger('reloadGrid');
                },
                afterSearch: function () {
                    var f = WFStepsGrid.getGridParam('postData').filters;
                    if (f != undefined)
                        f = $.DecryptString(f);
                    WFStepsGrid.setGridParam({ search: true, postData: { filters: f } });
                }
            });
            if (RoleAccess.IsAdd) {
                //WFStepsGrid Adding Custom Button
                var newbtnparsWFS = {
                    buttonicon: 'ui-icon-plus',
                    caption: '',
                    title: '@HttpContext.GetGlobalResourceObject("Resource_en", "Add")',
                    position: 'first',
                    onClickButton: function () {

                        WFStepsGrid.addGridRow('WFStepRemove');
                        var WorkFlowID = $('#hdnWFID').val();
                        var rowIDS = WFStepsGrid.jqGrid('getDataIDs');
                        var id = rowIDS[rowIDS.length - 1];
                        var primID = WFStepsGrid.getCell(id, 'WFSteps_ID');
                        primID = primID == '' ? 0 : primID;
                        WFStepsGrid.txtUniqueness(id, 'WFStep_Name', 'Step', $.url('/WorkFlow/CheckStepExists?primID=' + primID + '&id=' + WorkFlowID));
                    }
                };
                WFStepsGrid.navButtonAdd('#WFStepspager', newbtnparsWFS);
            }

            if (RoleAccess.IsDelete) {
                //Adding - Button to WFStepsGrid
                WFStepsGrid.navButtonAdd('#WFStepspager', {
                    buttonicon: 'ui-icon-minus',
                    caption: '',
                    title: '@HttpContext.GetGlobalResourceObject("Resource_en", "Delete")',
                    position: 'last',
                    onClickButton: function () {
                        WFStepsGrid.delGridRows($.url('/WorkFlow/DeleteWorkFlowSteps/'));
                    }
                });
            }

            if (RoleAccess.IsAdd || RoleAccess.IsEdit) {
                //Adding Save Button to WFStepsGrid
                WFStepsGrid.navButtonAdd('#WFStepspager', {

                    buttonicon: 'ui-icon-disk',
                    caption: '',
                    title: '@HttpContext.GetGlobalResourceObject("Resource_en", "Save")',
                    position: 'last',
                    onClickButton: function () {
                        try {
                            var IsDEntrd = true;
                            var edited = false;
                            var rowIDS = WFStepsGrid.jqGrid('getDataIDs');
                            var WorkFlowID = $('#hdnWFID').val();

                            var datas = '{WorkFlowID:\'' + WorkFlowID + '\',rows:[';
                            var JWFStepsGrid = document.getElementById('WFStepsGrid');
                            for (i = 1; i <= rowIDS.length; i++) {

                                var Data = JWFStepsGrid.rows[i];
                                var editmode = $(Data.children[0].innerHTML).attr('editmode');

                                if (editmode != 'false') {
                                    var WFStepID = Data.children[2].innerHTML;
                                    WFStepID = (WFStepID == '&nbsp;' || WFStepID == '') ? '' : WFStepID;
                                    var WFStepName = $.EncryptString($.trim(Data.children[3].firstChild.value));
                                    var WFStepType = Data.children[4].firstChild.value;
                                    var WFStepStatus = Data.children[5].firstChild.value;
                                    var WFStepIsActive = $(Data.children[6].firstChild).attr('checked');
                                    WFStepName.length == 0 ? $(Data.children[3].firstChild).addClass('input-validation-error') : $(Data.children[3].firstChild).removeClass('input-validation-error');
                                    WFStepType == '-1' ? $(Data.children[4].firstChild).addClass('input-validation-error') : $(Data.children[4].firstChild).removeClass('input-validation-error');
                                    WFStepStatus == '-1' ? $(Data.children[5].firstChild).addClass('input-validation-error') : $(Data.children[5].firstChild).removeClass('input-validation-error');

                                    if (WFStepName.length == 0 || WFStepType == '-1' || WFStepStatus == '-1') {

                                        IsDEntrd = false;
                                    }
                                    else {
                                        if (editmode == 'true' || editmode == '') {
                                            datas = datas.concat('{WFStepID:\'' + WFStepID + '\', WFStepName:\'' + WFStepName + '\',' + 'WFStepType:\'' + WFStepType + '\',');
                                            datas = datas.concat('WFStepStatus:\'' + WFStepStatus + '\',WFStepIsActive:\'' + (WFStepIsActive == "checked" ? true : false) + '\'},');
                                            edited = true;
                                        }
                                    }
                                }
                            }
                            datas = datas.concat(']}');

                            if (IsDEntrd) {
                                if (!edited) {
                                    alert('@HttpContext.GetGlobalResourceObject("Resource_en", "NoChangesMade")');
                                }
                                else {
                                    $.ajax(
                                        {
                                            url: $.url('/WorkFlow/WorkFlowStepsSave'),
                                            data: { key: datas },
                                            type: 'POST',
                                            datatype: 'json',
                                            success: function () {
                                                WFStepsGrid.trigger('reloadGrid');
                                            },
                                            error: function (e) {
                                                alert("Error in Saving");
                                            }
                                        });
                                }
                            }
                            else {
                                alert('@HttpContext.GetGlobalResourceObject("Resource_en", "HighlightedFieldsareMandatory")');
                            }
                        }
                        catch (ex) {
                        }
                    }
                });
            }

          
            //Adding refresh Button to Grid
            WFStepsGrid.navButtonAdd('#WFStepspager', {
                buttonicon: 'ui-icon-refresh',
                caption: '',
                title: '@HttpContext.GetGlobalResourceObject("Resource_en", "Refresh")',
                position: 'last',
                onClickButton: function () {
                    WFStepsGrid.trigger('reloadGrid');
                }
            });

            //-------------------------Work Flow Action---------------------------------

            WFActionGrid.jqGrid({
                datatype: 'json',
                mtype: 'POST',
                colNames: [editlabel, deletelabel, '', '@HttpContext.GetGlobalResourceObject("Resource_en", "ActionName")'],
                colModel: [{ name: 'edit', sortable: false, editable: false, width: 30, align: 'center', search: false, align: 'center' },
                   { name: 'delete', sortable: false, editable: false, width: 50, align: 'center', search: false, align: 'center', hidden: (!RoleAccess.IsDelete) },
                   { name: 'WFAction_ID', index: 'WFAction_ID', width: 0, editable: false, key: true, hidden: true },
                   { name: 'WFAction_Name', index: 'WFAction_Name', align: 'left', index: 'WFAction_Name', width: 400, editable: true, editoptions: { maxlength: '30' } }
                ],
                pager: '#WFActionpager',
                rowNum: 5,
                rowList: [5, 10, 15, 25],
                viewrecords: true,
                sortname: 'WFAction_ID',
                sortorder: 'asc',
                caption: '@HttpContext.GetGlobalResourceObject("Resource_en", "Actions")',
                width: '100%',
                height: '100%',
                jsonReader: {
                    root: "rows",
                    page: "page",
                    total: "total",
                    records: "records",
                    repeatitems: false
                },
                loadComplete: function () {
                    WFActionGrid.makeRowEditable('WFActionEdtImg', RoleAccess.IsEdit);
                    WFActionGrid.checkable('WFActionDelChkImg');
                    $('.WFActionEdtImg').click(function () {
                        var WorkFlowID = $('#hdnWFID').val();
                        var id = $($(this).parent('td').parent('tr')).attr('id');
                        var primID = WFActionGrid.getCell(id, 'WFAction_ID');
                        primID = primID == '' ? 0 : primID;
                        WFActionGrid.txtUniqueness(id, 'WFAction_Name', 'Action', $.url('/WorkFlow/CheckActionExists?primID=' + primID + '&id=' + WorkFlowID));
                    });
                }
            });

            //WFActionGrid Navigator
            WFActionGrid.navGrid('#WFActionpager', { view: false, add: false, edit: false, del: false, search: false, refresh: false });

            if (RoleAccess.IsAdd) {
                //WFAction + Button
                WFActionGrid.navButtonAdd('#WFActionpager', {
                    buttonicon: 'ui-icon-plus',
                    caption: '',
                    title: '@HttpContext.GetGlobalResourceObject("Resource_en", "Add")',
                    position: 'first',
                    onClickButton: function () {

                        WFActionGrid.addGridRow('WFActionRemove');
                        var WorkFlowID = $('#hdnWFID').val();
                        var rowIDS = WFActionGrid.jqGrid('getDataIDs');
                        var id = rowIDS[rowIDS.length - 1];
                        var primID = WFActionGrid.getCell(id, 'WFAction_ID');
                        primID = primID == '' ? 0 : primID;
                        WFActionGrid.txtUniqueness(id, 'WFAction_Name', 'Action', $.url('/WorkFlow/CheckActionExists?primID=' + primID + '&id=' + WorkFlowID));

                    }
                });
            }

            if (RoleAccess.IsDelete) {
                //Adding - Button to WFStepsGrid
                WFActionGrid.navButtonAdd('#WFActionpager', {
                    buttonicon: 'ui-icon-minus',
                    caption: '',
                    title: '@HttpContext.GetGlobalResourceObject("Resource_en", "Delete")',
                    position: 'last',
                    onClickButton: function () {

                        WFActionGrid.delGridRows($.url('/WorkFlow/WFActionsDelete'))
                    }

                });
            }

            if (RoleAccess.IsAdd || RoleAccess.IsEdit) {
                //Adding Save Button to WFActionGrid
                WFActionGrid.navButtonAdd('#WFActionpager', {

                    buttonicon: 'ui-icon-disk',
                    caption: '',
                    title: '@HttpContext.GetGlobalResourceObject("Resource_en", "Save")',
                    position: 'last',
                    onClickButton: function () {
                        try {
                            var IsDEntrd = true;
                            var edited = false;
                            var rowIDS = WFActionGrid.jqGrid('getDataIDs');
                            var WorkFlowID = $('#hdnWFID').val();
                            var datas = '{WorkFlowID:\'' + WorkFlowID + '\',rows:[';
                            var JWFActionGrid = document.getElementById('WFActionGrid');
                            for (i = 1; i <= rowIDS.length; i++) {
                                var Data = JWFActionGrid.rows[i];
                                var editmode = $(Data.children[0].innerHTML).attr('editmode');
                                if (editmode != 'false') {
                                    var WFActionID = Data.children[2].innerHTML;
                                    WFActionID = (WFActionID == '&nbsp;' || WFActionID == '') ? '' : WFActionID;
                                    var WFActionNm = $.EncryptString($.trim(Data.children[3].firstChild.value));
                                    WFActionNm.length == 0 ? $(Data.children[3].firstChild).addClass('input-validation-error') : $(Data.children[3].firstChild).removeClass('input-validation-error');

                                    if (WFActionNm.length == 0) {
                                        IsDEntrd = false;
                                    }
                                    else {
                                        if (editmode == 'true' || editmode == '') {
                                            datas = datas.concat('{WFActionID:\'' + WFActionID + '\', WFActionNm:\'' + WFActionNm + '\'},');
                                            edited = true;
                                        }
                                    }
                                }
                            }
                            datas = datas.concat(']}');

                            if (IsDEntrd) {
                                if (!edited) {
                                    alert('@HttpContext.GetGlobalResourceObject("Resource_en", "NoChangesMade")');
                                }
                                else {
                                    $.ajax(
                                        {
                                            url: $.url('/WorkFlow/WFActionsSave'),
                                            data: { key: datas },
                                            type: 'POST',
                                            datatype: 'json',
                                            success: function () {
                                                WFActionGrid.trigger('reloadGrid');
                                            },
                                            error: function (e) {
                                                alert("Error in Saving");
                                            }
                                        });
                                }
                            }
                            else {
                                alert('@HttpContext.GetGlobalResourceObject("Resource_en", "HighlightedFieldsareMandatory")');
                            }
                        }
                        catch (ex) {
                        }
                    }
                });

            }

            //Adding refresh Button to WFActionGrid
            WFActionGrid.navButtonAdd('#WFActionpager', {
                buttonicon: 'ui-icon-refresh',
                caption: '',
                title: 'Refresh',
                position: 'last',
                onClickButton: function () {
                    WFActionGrid.trigger('reloadGrid');

                }
            });




            //---------------WF Roles------------------------------------------------

            $.fn.EditRoleRow = function (className, IsEdit) {
                
                var grd = $(this);
                IsEdit = IsEdit == undefined ? true : IsEdit;
                $('.' + className).click(function (event) {
                    
                    if (IsEdit) {
                        this.rowid = $(this).parent('td').parent('tr').attr('id');
                        if ($(this).attr('editmode') == 'true') {
                            $(this).attr('editmode', false)
                            grd.restoreRow(this.rowid);
                            $(this).attr('src', $.url('/Content/Images/edit.gif'));

                            event.stopImmediatePropagation();
                        }
                        else {

                            $(this).attr('editmode', true)
                            grd.editRow(this.rowid);

                            var rowid = $($(this).parent('td').parent('tr')).attr('id');
                          
                            var rows = WFRolesGrid.getRowData(rowid);
                            var IsExt = ($(rows.WFRole_IsExternal).attr("Checked")=="checked")?true:false;
                            if (IsExt) {
                                var id = $(rows.WFRole_ExternalCompanyName).attr("id");
                                document.getElementById(id).disabled = false;
                            }
                            else {
                                var id = $(rows.WFRole_ExternalCompanyName).attr("id");
                                document.getElementById(id).disabled = true;
                            }

                            $(this).attr('src', $.url('/Content/Images/Cancel.gif'))
                        }
                    }
                    else {
                        alert($.getMessage('Youdonothaveeditpermission'));
                    }
                });
            }

            //--------------

            $.fn.checkIsExternal = function () {
                var rowid = $($(this).parent('td').parent('tr')).attr('id');
                var chkd = $(this).attr('checked');
                var rows = WFRolesGrid.getRowData(rowid);
                if (chkd == 'checked') {
                    $(this).attr('checked', chkd);
                    var id = $(rows.WFRole_ExternalCompanyName).attr("id");
                    document.getElementById(id).disabled = false;
                }
                else {
                    $(this).removeAttr('checked');
                    var id = $(rows.WFRole_ExternalCompanyName).attr("id");
                    document.getElementById(id).disabled = true;
                    document.getElementById(id).value = "-1";
                }
            }

            WFRolesGrid.jqGrid({
                datatype: 'json',
                mtype: 'POST',
                colNames: [editlabel, deletelabel, '', '@HttpContext.GetGlobalResourceObject("Resource_en", "RoleName")', '@HttpContext.GetGlobalResourceObject("Resource_en", "IsAdmin")', '@HttpContext.GetGlobalResourceObject("Resource_en", "AutoAllocationAllowed")', 'Is External','Company','',''],
                colModel: [
                   { name: 'edit', sortable: false, editable: false, width: 30, align: 'center', search: false },
                   { name: 'delete', sortable: false, editable: false, width: 50, align: 'center', search: false },
                   { name: 'WFRole_ID', advncName: 'WorkFlow Role ID', index: 'WFRole_ID', width: 0, editable: false, key: true, hidden: true },
                   { name: 'WFRole_Name', index: 'WFRole_Name', align: 'left', advncName: 'WorkFlow Role Name', index: 'WFRole_Name', width: 400, editable: true, editoptions: { maxlength: '50' } },
                   { name: 'WfRole_IsAdmin', index: 'WfRole_IsAdmin', width: 70, editable: true, edittype: 'checkbox', editoptions: { value: "Yes:No", dataEvents: [{ type: 'click', fn: function () { $(this).check(); } }] }, align: 'center' },
                   { name: 'WfRole_AutoAllocationAllowed', index: 'WfRole_AutoAllocationAllowed', width: 170, editable: true, edittype: 'checkbox', editoptions: { value: "Yes:No", dataEvents: [{ type: 'click', fn: function () { $(this).check(); } }] }, align: 'center' },
                   { name: 'WFRole_IsExternal', index: 'WFRole_IsExternal', sortable: false, width: 60, search: false, editable: true, edittype: 'checkbox', editoptions: { value: "Yes:No", dataEvents: [{ type: 'click', fn: function () { $(this).checkIsExternal(); } }] }, align: 'center' },
                   { name: 'WFRole_ExternalCompanyName', index: 'WFRole_ExternalCompanyName', sortable: false, edittype: 'select', editable: true, editoptions: { value: function () { return WFExternalCompany },"disabled":"disabled"  }, width: 200, align: 'left', search: true, },
                   { name: 'RoleUser', sortable: false, editable: false, width: 30, align: 'center', search: false, },
                   { name: 'ExtCompID', index: 'ExtCompID', hidden:true},
                ],
                pager: '#WFRolespager',
                rowNum: 5,
                rowList: [5, 10, 15, 25],
                viewrecords: true,
                sortname: 'WFRole_ID',
                sortorder: 'asc',
                caption: '@HttpContext.GetGlobalResourceObject("Resource_en", "Roles")',
                width: '100%',
                height: '100%',
                jsonReader: {
                    root: "rows",
                    page: "page",
                    total: "total",
                    records: "records",
                    repeatitems: false
                },
                loadComplete: function (data) {

                    if (data != undefined && data != null) {
                        WFExternalCompany = data.CompanyDDl;
                    }

                    $('.RoleUserImg').click(function () {
                        try {
                            var RoleID = $(this).attr('key');
                            var rowid = $($(this).parent('td').parent('tr')).attr('id');
                            $('#hdnRoleID').attr('value', RoleID);
                            var rows = WFRolesGrid.getRowData(rowid);
                            var Cid = rows.ExtCompID;
                            
                            var RoleName = $(this).attr('RoleName');
                            WFRoleUserGrid.clearGridData(true);
                            WFRoleUserGrid.setGridParam({url: $.url('/WorkFlow/WFUserRoles?advnce=false&id=' + RoleID+'&CompID='+Cid)});
                            WFRoleUserGrid.trigger('reloadGrid');

                            $('#WFRoleUserDialog').dialog({ modal: true, width: '70%', position: 'center', height: window.screen.availHeight / 2, title: RoleName });
                        }
                        catch (ex) {
                        }
                    });

                    WFRolesGrid.EditRoleRow('WFRolesEdtImg', RoleAccess.IsEdit);

                    WFRolesGrid.checkable('WFRolesDelChkImg');
                    $('.WFRolesEdtImg').click(function () {
                        var WorkFlowID = $('#hdnWFID').val();
                        var id = $($(this).parent('td').parent('tr')).attr('id');
                        var primID = WFRolesGrid.getCell(id, 'WFRole_ID');
                        primID = primID == '' ? 0 : primID;
                        WFRolesGrid.txtUniqueness(id, 'WFRole_Name', 'Role Name', $.url('/WorkFlow/CheckRoleNameExists?primID=' + primID + '&id=' + WorkFlowID));

                    });
                }
            });

            //WFRolesGrid Navigator
            WFRolesGrid.navGrid('#WFRolespager', { view: false, add: false, edit: false, del: false, search: false, refresh: false });
            if (RoleAccess.IsAdd) {
                //WFRolesGrid + Button
                WFRolesGrid.navButtonAdd('#WFRolespager', {
                    buttonicon: 'ui-icon-plus',
                    caption: '',
                    title: '@HttpContext.GetGlobalResourceObject("Resource_en", "Add")',
                    position: 'first',
                    onClickButton: function () {
                        var WorkFlowID = $('#hdnWFID').val();
                        WFRolesGrid.addGridRow('WFRolesRemove');
                        var rowIDS = WFRolesGrid.jqGrid('getDataIDs');
                        var id = rowIDS[rowIDS.length - 1];
                        var primID = WFRolesGrid.getCell(id, 'WFRole_ID');
                        primID = primID == '' ? 0 : primID;
                        WFRolesGrid.txtUniqueness(id, 'WFRole_Name', 'Role Name', $.url('/WorkFlow/CheckRoleNameExists?primID=' + primID + '&id=' + WorkFlowID));

                    }
                });
            }
            if (RoleAccess.IsDelete) {
                //Adding - Button to WFStepsGrid
                WFRolesGrid.navButtonAdd('#WFRolespager', {
                    buttonicon: 'ui-icon-minus',
                    caption: '',
                    title: '@HttpContext.GetGlobalResourceObject("Resource_en", "Delete")',
                    position: 'last',
                    onClickButton: function () {

                        WFRolesGrid.delGridRows($.url('/WorkFlow/WFRolesDelete'))
                    }

                });

            }

            if (RoleAccess.IsAdd || RoleAccess.IsEdit) {
                //Adding Save Button to WFActionGrid
                WFRolesGrid.navButtonAdd('#WFRolespager', {

                    buttonicon: 'ui-icon-disk',
                    caption: '',
                    title: '@HttpContext.GetGlobalResourceObject("Resource_en", "Save")',
                    position: 'last',
                    onClickButton: function () {
                        try {
                            
                            var IsDEntrd = true;
                            var edited = false;
                            var rowIDS = WFRolesGrid.jqGrid('getDataIDs');
                            var WorkFlowID = $('#hdnWFID').val();
                            
                            var datas = '{rows:[';
                            var JWFRolesGrid = document.getElementById('WFRolesGrid');
                            for (i = 1; i <= rowIDS.length; i++) {


                                var Data = JWFRolesGrid.rows[i];
                                var editmode = $(Data.children[0].innerHTML).attr('editmode');
                                if (editmode != 'false') {
                                    var WFRoleID = Data.children[2].innerHTML;
                                    WFRoleID = (WFRoleID == '&nbsp;' || WFRoleID == '' ? '0' : WFRoleID);
                                    var WFRoleNm = $.EncryptString($.trim(Data.children[3].firstChild.value));
                                    var isAdmin = ($(Data.children[4].firstChild).attr('checked') == 'checked' ? true : false);
                                    var isAllocation = ($(Data.children[5].firstChild).attr('checked') == 'checked' ? true : false);
                                    var isExternal = ($(Data.children[6].firstChild).attr('checked') == 'checked' ? true : false);
                                     var ExternalCompany = (Data.children[7].firstChild.value == "-1") ? 0 : Data.children[7].firstChild.value;
                                    //var ExternalCompany = document.getElementById($(Data.children[7].firstChild).attr("id")).value;

                                    WFRoleNm.length == 0 ? $(Data.children[3].firstChild).addClass('input-validation-error') : $(Data.children[3].firstChild).removeClass('input-validation-error');
                                    (isExternal == true && ExternalCompany == "-1") ? $(Data.children[7].firstChild).addClass('input-validation-error') : $(Data.children[7].firstChild).removeClass('input-validation-error');

                                    if (WFRoleNm.length == 0 || (isExternal == true && ExternalCompany == "-1")) {
                                        IsDEntrd = false;
                                    }
                                    else {
                                        if (editmode == 'true' || editmode == '') {
                                            datas = datas.concat('{WFRole_ID:\'' + WFRoleID + '\',WorkFlow_ID:\'' + WorkFlowID + '\', WFRole_Name:\'' + WFRoleNm + '\',WfRole_IsAdmin:\'' + isAdmin + '\',WfRole_AutoAllocationAllowed:\'' + isAllocation + '\',WFRole_IsRoleExternal:\'' + isExternal + '\',WFRole_ExternalCompany_ID:\''+ExternalCompany+'\'},');
                                            edited = true;
                                        }
                                    }
                                }

                            }
                            datas = datas.concat(']}');

                            if (IsDEntrd) {
                                if (!edited) {

                                    alert('@HttpContext.GetGlobalResourceObject("Resource_en", "NoChangesMade")');
                                }
                                else {
                                    $.ajax(
                                        {
                                            url: $.url('/WorkFlow/SaveWFRoles'),
                                            data: { key: datas },
                                            type: 'POST',
                                            datatype: 'json',
                                            success: function () {
                                                WFRolesGrid.trigger('reloadGrid');
                                            },
                                            error: function (e) {

                                                alert("Error in Saving");
                                            }
                                        });
                                }
                            }
                            else {

                                alert('@HttpContext.GetGlobalResourceObject("Resource_en", "HighlightedFieldsareMandatory")');
                            }
                        }
                        catch (ex) {
                        }
                    }
                });
            }
            //Adding refresh Button to WFActionGrid
            WFRolesGrid.navButtonAdd('#WFRolespager', {
                buttonicon: 'ui-icon-refresh',
                caption: '',
                title: '@HttpContext.GetGlobalResourceObject("Resource_en", "Refresh")',
                position: 'last',
                onClickButton: function () {
                    WFRolesGrid.trigger('reloadGrid');

                }
            });
            //---------------WF Detail Tabs--------------------------------------

            $('#WFDetailTabs').tabs();
            //Work Flow Detail Tabs
            $.LoadDetails = function () {
                try {
                    var id = $('#hdnWFID').val();
                    WFRolesGrid.clearGridData(true);
                    WFRolesGrid.setGridParam({
                        url: $.url('/WorkFlow/SelectWFRole?id=' + id),
                    });
                    $('#WFRolesGrid').trigger('reloadGrid');

                    WFStepLinkGrid.clearGridData(true);
                    WFStepsGrid.setGridParam({
                        url: $.url('/WorkFlow/WFStepsData?id=' + id),
                    });
                    $('#WFStepsGrid').trigger('reloadGrid');

                    WFActionGrid.clearGridData(true);
                    WFActionGrid.setGridParam({
                        url: $.url('/WorkFlow/WFActions?id=' + id),
                    });
                    $('#WFActionGrid').trigger('reloadGrid');

                    $.ajax({
                        url: $.url('/WorkFlow/GetDefaultCompanyID?workFlowID=' + id),
                        success: function (data) {
                            var ddSLCmpny = $('#ddSLCmpny');
                            ddSLCmpny.html('');
                            for (i = 0; i < data.Company.length; i++) {
                                ddSLCmpny.append($(data.Company[i]));
                            }
                            //$('#ddSLCmpny').attr('value', data.companyID)
                            WFStepLinkGrid.clearGridData(true);
                            WFStepLinkGrid.setGridParam({
                                url: $.url('/WorkFlow/WFStepLinkData?id=' + id + '&companyID=' + data.companyID),
                            });
                            $('#WFStepLinkGrid').trigger('reloadGrid');
                        }
                    });
                }
                catch (ex) {
                }
            };
            //Company Change
            $('#ddSLCmpny').change(function () {
                try {
                    SelectedCompanyID = $(this).val();
                    WFStepLinkGrid.clearGridData(true);
                    WFStepLinkGrid.setGridParam({
                        url: $.url('/WorkFlow/WFStepLinkData?advnce=false&id=' + $('#hdnWFID').val() + '&companyID=' + $(this).val()),
                    });
                    WFStepLinkGrid.trigger('reloadGrid');
                }
                catch (ex) {
                }
            });
            var SelectedCompanyID = "";
            //-------------------------------------WFStepLinkGrid--------------------------------------
            var WFStepLinkCompany = "";
            var WFStepLinkSteps = "";
            var WFStepLinkRoles = "";
            var WFStepLinkActions = "";
            var JWFStepLinkGrid = document.getElementById('WFStepLinkGrid');
            var defaultStepLink = "-1";
            //----------edit handle---

            $.fn.EditStepLinkRow = function (className, IsEdit) {

                var grd = $(this);
                IsEdit = IsEdit == undefined ? true : IsEdit;
                $('.' + className).click(function (event) {
                    
                    if (IsEdit) {
                        this.rowid = $(this).parent('td').parent('tr').attr('id');

                        if ($(this).attr('editmode') == 'true') {
                            $(this).attr('editmode', false)
                            grd.restoreRow(this.rowid);
                            $(this).attr('src', $.url('/Content/Images/edit.gif'));

                            event.stopImmediatePropagation();
                        }
                        else {
                            
                            $(this).attr('editmode', true)
                            grd.editRow(this.rowid);

                            var rowid = $(this).attr("key");
                         
                            var rows = WFStepLinkGrid.getRowData(rowid);
                            var IsExt = $("#" + ($(rows.ParentWorkFlow).attr("id")) + " option:selected").val();
                            if (IsExt !="-1") {
                                var StepLinkid = $(rows.ParentWorkFlowStepLink).attr("id");
                                var WorkFlowid = $(rows.ParentWorkFlow).attr("id");
                                defaultStepLink = rows.ParentWFStepLinkID;
                                $.LoadStepLinkData(WorkFlowid, IsExt, defaultStepLink);

                            }
                            else {
                                var StepLinkid = $(rows.ParentWorkFlowStepLink).attr("id");
                                document.getElementById(StepLinkid).disabled = true;
                            }                            
                            var ChildObjectID = $("#" + ($(rows.WFChildObject).attr("id")) + " option:selected").val();
                            $.LoadChildActions($(rows.ChildObjectAction).attr("id"), ChildObjectID);

                            $(this).attr('src', $.url('/Content/Images/Cancel.gif'))
                        }
                    }
                    else {
                        alert($.getMessage('Youdonothaveeditpermission'));
                    }
                });
            }

            $.LoadChildActions = function (RowID, ChildObjectID) {                
                var childAction = document.getElementById(RowID);
                $.ClearDropDown(RowID);
                $.ajax({
                    cache: false,
                    url: $.url('/WorkFlow/getChildActions?objectID=' + ChildObjectID),
                    type: 'POST',
                    datatype: 'json',
                    success: function (resp) {                        
                        for (var i = 0; i < resp.length; i++) {
                            var opt = new Option(resp[i].Name, resp[i].ID, false, false);
                            childAction.add(opt, 1);
                        }                        
                        if (resp.length == 1) {
                            $('#' + RowID).attr('value', resp[0].ID);
                        }
                    },
                    error: function (e) {
                        alert("Error in Saving");
                    }
                });
            }

            //--------Load Step link  Drop down----//
            $.LoadStepLinkData = function (WorkFlowid, ParentWFval, defaultval) {
                if (ParentWFval != '') {
                    var Steplinkid = WorkFlowid.replace("ParentWorkFlow", "ParentWorkFlowStepLink");
                    document.getElementById(Steplinkid).disabled = false;
                    var steplink = $("#" + Steplinkid);
                    $.ajax({
                        url: $.url('/WorkFlow/GetStepLinkData?WorkFlowid=' + ParentWFval + '&CompID=' + SelectedCompanyID),
                        success: function (data) {
                            
                            this.steplink = steplink;
                            steplink.html('');
                            steplink.html($("<option value='-1'>--select--</option>"));

                            for (i = 0; i < data.length; i++) {
                                this.steplink.append($("<option value='" + data[i].ID + "' >" + data[i].Name + "</option>"));
                            }
                            document.getElementById(Steplinkid).value = defaultval;
                        }
                    });
                }
                else {
                    document.getElementById(Steplinkid).selectedIndex = 0;
                    document.getElementById(Steplinkid).disabled = true;
                }
            }


            $.ValidateWorkFLowField = function (id) {
                var RegVal = document.getElementById(id).value;
                var AutoCond = id.replace('WorkFlowField', 'AutoCondition');
                if (RegVal != '-1') {
                    $('#' + AutoCond).removeAttr('disabled');
                }
                else {
                    $('#' + AutoCond).attr('disabled', true);
                    $('#' + AutoCond).attr('value', '');
                }
            }

            $.EnableControls = function (id) {
                var val = document.getElementById(id).value
                var WFFieldID = id.replace('Addresse_Flag', 'WorkFlowField');
                var AutoCon = id.replace('Addresse_Flag', 'AutoCondition');
                if (val == 2) {
                    $('#' + WFFieldID).removeAttr('disabled');
                    //$('#' + AutoCon).removeAttr('disabled');
                }
                else {
                    $('#' + WFFieldID).attr('disabled', true);
                    $('#' + WFFieldID).attr('value', '-1');
                    $('#' + AutoCon).attr('value', '');
                    $('#' + AutoCon).attr('disabled', true);
                    //$('#' + AutoCon).attr('disabled',true);
                }
                //var rowID = $('#' + id).parent('td').parent('tr').attr('id');
            }

            WFStepLinkGrid.jqGrid({
                colNames: [editlabel, deletelabel, '', '@HttpContext.GetGlobalResourceObject("Resource_en", "FromStep")', '@HttpContext.GetGlobalResourceObject("Resource_en", "ToStep")', '@HttpContext.GetGlobalResourceObject("Resource_en", "Action")', '@HttpContext.GetGlobalResourceObject("Resource_en", "RoleName")', '@HttpContext.GetGlobalResourceObject("Resource_en", "AddresseFlag")', '@HttpContext.GetGlobalResourceObject("Resource_en", "AutoAllocationAllowed")',
                 '@HttpContext.GetGlobalResourceObject("Resource_en", "IsVersionEnabled")', 'Parent WorkFlow', 'Parent WorkFlowS tepLink', 'Child Object', 'Child Object Action', '@HttpContext.GetGlobalResourceObject("Resource_en", "SMSToCustomer")', '@HttpContext.GetGlobalResourceObject("Resource_en", "EmailToCustomer")', '@HttpContext.GetGlobalResourceObject("Resource_en", "SMSToAddressee")', '@HttpContext.GetGlobalResourceObject("Resource_en", "EmailToAddresse")','Auto Allocation Field Name','Auto Allocation Condition',''],
                colModel: [
                            { name: 'edit', sortable: false, editable: false, width: 30, align: 'center', search: false, align: 'center', },
                            { name: 'delete', sortable: false, editable: false, width: 50, align: 'center', search: false, align: 'center', hidden: (!RoleAccess.IsDelete) },
                            { name: 'WFStepLink_ID', index: 'WFStepLink_ID', width: 0, editable: false, key: true, hidden: true },
                            { name: 'FrmStepNm', index: 'FrmStepNm', width: 150, editable: true, edittype: 'select', editoptions: { value: function () { return WFStepLinkSteps } }, align: 'left' },
                            { name: 'ToStepNm', index: 'ToStepNm', width: 150, edittype: 'select', editable: true, editoptions: { value: function () { return WFStepLinkSteps } }, align: 'left' },
                            { name: 'WFAction_Name', index: 'WFAction_Name', width: 150, edittype: 'select', editable: true, editoptions: { value: function () { return WFStepLinkActions } }, align: 'left' },
                            { name: 'WFRole_Name', index: 'WFRole_Name', width: 150, edittype: 'select', editable: true, editoptions: { value: function () { return WFStepLinkRoles } }, align: 'left' },
                            { name: 'Addresse_Flag', index: 'Addresse_Flag', align: 'center', width: 100, editable: true, edittype: 'select',  editoptions: { value: '-1:--Select--;0:Role;1:Individual;2:Auto', dataEvents: [{ type: 'change', fn: function () { $.EnableControls(this.id)  } }] }, align: 'center' },
                            { name: 'AutoAllocationAllowed', index: 'AutoAllocationAllowed', align: 'left', index: 'AutoAllocationAllowed', width: 150, editable: true, edittype: 'checkbox', editoptions: { value: "Yes:No", dataEvents: [{ type: 'click', fn: function () { $(this).check(); $.ValidateWorkFLowField(this.id) } }] } },
                            { name: 'IsVersionEnabled', index: 'IsVersionEnabled', width: 150, editable: true, edittype: 'checkbox', editoptions: { value: "Yes:No", dataEvents: [{ type: 'click', fn: function () { $(this).check(); } }] }, align: 'center' },

                            { name: 'ParentWorkFlow', index: 'ParentWorkFlow', width: 150, edittype: 'select', editable: true, editoptions: { value: function () { return WFParentWorkFlow }, dataEvents: [{ type: 'change', fn: function () { return $.LoadStepLinkData(this.id, $(this).val(), '-1'); } }] }, align: 'left' },
                            { name: 'ParentWorkFlowStepLink', index: 'ParentWorkFlowStepLink', align: 'left', width: 300, editable: true, edittype: 'select', editoptions: { value: function () { return WFStepLinkRow }, "disabled": "disabled" } },
                            {
                                name: 'WFChildObject', index: 'WFChildObject', align: 'left', width: 200, editable: true, edittype: 'select', editoptions: {
                                    value: function () { return WFChildObject }, dataEvents: [{
                                        type: 'change', fn: function () {

                                            var id = $(this).attr('id');
                                            var objectID = $('#' + id + ' option:selected').val();

                                            var ddlID = id.replace('_WFChildObject', '_ChildObjectAction');
                                            //var childAction = document.getElementById(ddlID);                                                                                        
                                            $.LoadChildActions(ddlID, objectID);
                                        }
                                    }]
                                },
                            },
                            //{ name: 'ChildObjectAction', index: 'ChildObjectAction', width: 200, editable: true, edittype: 'text',editoptions: { maxlength: 50 }, align: 'left' },
                            { name: 'ChildObjectAction', index: 'ChildObjectAction', align: 'left', width: 200, editable: true, edittype: 'select', editoptions: { value: function () { return WFChildObjectAction } }, },

                            { name: 'IsSMSSentToCustomer', index: 'IsSMSSentToCustomer', width: 150, editable: true, edittype: 'checkbox', editoptions: { value: "Yes:No", dataEvents: [{ type: 'click', fn: function () { $(this).check(); } }] }, align: 'center' },
                            { name: 'IsEmailSentToCustomer', index: 'IsEmailSentToCustomer', width: 150, editable: true, edittype: 'checkbox', editoptions: { value: "Yes:No", dataEvents: [{ type: 'click', fn: function () { $(this).check(); } }] }, align: 'center' },
                            { name: 'IsSMSSentToAddressee', index: 'IsSMSSentToAddressee', width: 150, editable: true, edittype: 'checkbox', editoptions: { value: "Yes:No", dataEvents: [{ type: 'click', fn: function () { $(this).check(); } }] }, align: 'center' },
                            { name: 'IsEmailSentToAddresse', index: 'IsEmailSentToAddresse', width: 150, editable: true, edittype: 'checkbox', editoptions: { value: "Yes:No", dataEvents: [{ type: 'click', fn: function () { $(this).check(); } }] }, align: 'center' },

                            { name: 'WorkFlowField', index: 'WorkFlowField', width: 150, edittype: 'select', editable: true, editoptions: {  value: function () { return WFFields }, dataEvents: [{ type: 'change', fn: function () { $.ValidateWorkFLowField(this.id, 1) } }] }, align: 'left' },
                            { name: 'AutoCondition', index: 'AutoCondition', width: 150, editable: true, align: 'left', editoptions: { maxlength: 100, dataEvents: [{ type: 'change', fn: function () { $.ValidateWorkFLowField(this.id, 2) } }] } },
                            { name: 'ParentWFStepLinkID', index: 'ParentWFStepLinkID', hidden: true }
                ],
                pager: '#WFStepLinkpager',
                rowNum: 5,
                rowList: [5, 10, 15, 25],
                viewrecords: true,
                sortname: 'WFStepLink_ID',
                sortorder: 'asc',
                caption: '@HttpContext.GetGlobalResourceObject("Resource_en", "StepLink")',
                datatype: 'json',
                mtype: 'POST',
                jsonReader: {
                    root: "rows",
                    page: "page",
                    total: "total",
                    records: "records",
                    repeatitems: false
                },
                height: '100%',

                loadComplete: function (data) {                    
                    WFStepLinkCompany = data.Company;
                    WFStepLinkSteps = data.WFSteps;
                    WFStepLinkRoles = data.WFRoleNames;
                    WFStepLinkActions = data.WFActions;
                    WFParentWorkFlow = data.WorkFlowDdl;
                    WFStepLinkRow = data.WorkFlowSteplink;
                    WFChildObject = data.ChildObjectDdl;
                    WFChildObjectAction = data.ChildObjectActionDdl;
                    WFFields = data.WFFields;
                    WFStepLinkGrid.EditStepLinkRow('WFStepLinkEdtImg', RoleAccess.IsEdit);

                    WFStepLinkGrid.checkable('WFStepLinkDelChkImg');
                }
            });
            WFStepLinkGrid.navGrid('#WFStepLinkpager', { view: false, add: false, edit: false, del: false, search: false, refresh: false });
            if (RoleAccess.IsAdd) {
                //WFStepLinkGrid Adding Custom Button
                var newbtnparsWFSL = {
                    buttonicon: 'ui-icon-plus',
                    caption: '',
                    title: '@HttpContext.GetGlobalResourceObject("Resource_en", "Add")',
                    position: 'first',
                    onClickButton: function () {
                        WFStepLinkGrid.addGridRow('WFStepLinkRemove');
                    }
                };
                WFStepLinkGrid.navButtonAdd('#WFStepLinkpager', newbtnparsWFSL);
            }
            //WFStepLinkGrid Navigator

            if (RoleAccess.IsDelete) {
                //Adding - Button 
                WFStepLinkGrid.navButtonAdd('#WFStepLinkpager', {
                    buttonicon: 'ui-icon-minus',
                    caption: '',
                    title: '@HttpContext.GetGlobalResourceObject("Resource_en", "Delete")',
                    position: 'last',
                    onClickButton: function () {
                        WFStepLinkGrid.delGridRows($.url('/WorkFlow/WFStepLinkDelete'))
                    }
                });
            }

            if (RoleAccess.IsAdd || RoleAccess.IsEdit) {
                //Adding Save Button 
                WFStepLinkGrid.navButtonAdd('#WFStepLinkpager', {

                    buttonicon: 'ui-icon-disk',
                    caption: '',
                    title: '@HttpContext.GetGlobalResourceObject("Resource_en", "Save")',
                    position: 'last',
                    onClickButton: function () {
                        try {
                            
                            var IsDEntrd = true;
                            var edited = false;
                            var rowIDS = WFStepLinkGrid.jqGrid('getDataIDs');
                            var WorkFlowID = $('#hdnWFID').val();
                            var CompanyID = $('#ddSLCmpny').val();

                            if (CompanyID != '-1') {

                                var datas = '{WorkFlowID:\'' + WorkFlowID + '\',CompanyID:\'' + CompanyID + '\',rows:[';
                               
                                for (i = 1; i <= rowIDS.length; i++) {
                                    
                                    var Data = JWFStepLinkGrid.rows[i];
                                    var editmode = $(Data.children[0].innerHTML).attr('editmode');

                                    if (editmode != 'false') {
                                        var WFStepLinkID = Data.children[2].innerHTML;
                                        WFStepLinkID = (WFStepLinkID == '&nbsp;' || WFStepLinkID == '') ? '' : WFStepLinkID;
                                        var FrmStepID = Data.children[3].firstChild.value;
                                        var ToStepID = Data.children[4].firstChild.value;
                                        var WFActionID = Data.children[5].firstChild.value;
                                        var WFRoleID = Data.children[6].firstChild.value;
                                        var AddresseFlag = Data.children[7].firstChild.value;
                                        var IsSMSSentToCustomer = $(Data.children[14].firstChild).attr('checked');
                                        var IsEmailSentToCustomer = $(Data.children[15].firstChild).attr('checked');
                                        var IsSMSSentToAddressee = $(Data.children[16].firstChild).attr('checked');
                                        var IsEmailSentToAddresse = $(Data.children[17].firstChild).attr('checked');
                                        var IsVersionEnabled = $(Data.children[9].firstChild).attr('checked');
                                        var AutoAllocationAllowed = $(Data.children[8].firstChild).attr('checked');
                                        
                                        var ParentWFID = (Data.children[10].firstChild.value == "-1") ? 0 : Data.children[10].firstChild.value;
                                        var ParentWFStepLinkID = (Data.children[11].firstChild.value == "-1") ? 0 : Data.children[11].firstChild.value;
                                        var WFChildObjID = (Data.children[12].firstChild.value == "-1") ? 0 : Data.children[12].firstChild.value;
                                        var ChildAction = Data.children[13].firstChild.value;

                                        var WFAutoConditionField = (Data.children[18].firstChild.value == "") ? 0 : Data.children[18].firstChild.value;
                                        var WFAutoCondition = $.EncryptString($.trim(Data.children[19].firstChild.value));

                                        FrmStepID == '-1' ? $(Data.children[3].firstChild).addClass('input-validation-error') : $(Data.children[3].firstChild).removeClass('input-validation-error');
                                        ToStepID == '-1' ? $(Data.children[4].firstChild).addClass('input-validation-error') : $(Data.children[4].firstChild).removeClass('input-validation-error');
                                        WFActionID == '-1' ? $(Data.children[5].firstChild).addClass('input-validation-error') : $(Data.children[5].firstChild).removeClass('input-validation-error');
                                        WFRoleID == '-1' ? $(Data.children[6].firstChild).addClass('input-validation-error') : $(Data.children[6].firstChild).removeClass('input-validation-error');
                                        AddresseFlag == '-1' ? $(Data.children[7].firstChild).addClass('input-validation-error') : $(Data.children[7].firstChild).removeClass('input-validation-error');
                                        (ParentWFID != 0 && ParentWFStepLinkID == 0) ? $(Data.children[11].firstChild).addClass('input-validation-error') : $(Data.children[11].firstChild).removeClass('input-validation-error');
                                        (WFChildObjID != 0 && ChildAction == 0) ? $(Data.children[13].firstChild).addClass('input-validation-error') : $(Data.children[13].firstChild).removeClass('input-validation-error');


                                        if (FrmStepID == '-1' || ToStepID == '-1' || WFActionID == '-1' || WFRoleID == '-1' || AddresseFlag == '-1' || (ParentWFID != 0 && ParentWFStepLinkID == 0) || (WFChildObjID != 0 && ChildAction == 0)) {
                                            IsDEntrd = false;
                                        }
                                        else {
                                            if (editmode == 'true' || editmode == '') {
                                                datas = datas.concat('{WFStepLinkID:\'' + WFStepLinkID + '\', FrmWFSteps_ID:\'' + FrmStepID + '\',' + 'ToWFSteps_ID:\'' + ToStepID + '\',');
                                                datas = datas.concat('WFAction_ID:\'' + WFActionID + '\', Addresse_WFRole_ID:\'' + WFRoleID + '\',' + 'Addresse_Flag:\'' + AddresseFlag + '\',');
                                                datas = datas.concat('InvokeParentWF_ID:\'' + ParentWFID + '\', InvokeParentWFLink_ID:\'' + ParentWFStepLinkID + '\',' + 'InvokeChildObject_ID:\'' + WFChildObjID + '\',' + 'InvokeChildObjectAction:\'' + ChildAction +'\',');
                                                datas = datas.concat('IsVersionEnabled:\'' + (IsVersionEnabled == "checked" ? true : false) + '\',AutoAllocationAllowed:\'' + (AutoAllocationAllowed == "checked" ? true : false) + '\',IsSMSSentToCustomer:\'' + (IsSMSSentToCustomer == "checked" ? true : false) + '\', IsEmailSentToCustomer:\'' + (IsEmailSentToCustomer == "checked" ? true : false) + '\', IsSMSSentToAddressee:\'' + (IsSMSSentToAddressee == "checked" ? true : false) + '\',' + 'IsEmailSentToAddresse:\'' + (IsEmailSentToAddresse == "checked" ? true : false) + '\' ,'+ 'WFField_ID:\'' + WFAutoConditionField + '\',' + 'AutoCondition:\'' + WFAutoCondition + '\'},');

                                                edited = true;
                                            }
                                        }
                                    }

                                }
                                datas = datas.concat(']}');

                                if (IsDEntrd) {
                                    if (!edited) {

                                        alert('@HttpContext.GetGlobalResourceObject("Resource_en", "NoChangesMade")');
                                    }
                                    else {
                                        $.ajax(
                                            {
                                                url: $.url('/WorkFlow/WFStepLinkSave'),
                                                data: { key: datas },
                                                type: 'POST',
                                                datatype: 'json',
                                                success: function () {
                                                    WFStepLinkGrid.trigger('reloadGrid');
                                                },
                                                error: function (e) {
                                                    alert("Error in Saving");
                                                }
                                            });
                                    }
                                }
                                else {
                                    alert('@HttpContext.GetGlobalResourceObject("Resource_en", "HighlightedFieldsareMandatory")');
                                }
                            }
                            else {
                                alert('@HttpContext.GetGlobalResourceObject("Resource_en", "SelectCompany")');
                            }

                        }
                        catch (ex) {
                        }
                    }
                });
            }
            //Adding refresh Button to WFStepLinkGrid
            WFStepLinkGrid.navButtonAdd('#WFStepLinkpager', {
                buttonicon: 'ui-icon-refresh',
                caption: '',
                title: '@HttpContext.GetGlobalResourceObject("Resource_en", "Refresh")',
                position: 'last',
                onClickButton: function () {
                    WFStepLinkGrid.trigger('reloadGrid');

                }
            });
            //----WF Role Users---------------------------
            var WFUsers = '';
            WFRoleUserGrid.jqGrid({

                datatype: 'json',
                mtype: 'POST',
                colNames: [editlabel, deletelabel, '', '@HttpContext.GetGlobalResourceObject("Resource_en", "UserName")', '@HttpContext.GetGlobalResourceObject("Resource_en", "ApprovalLimit")'],
                colModel: [{ name: 'edit', sortable: false, editable: false, width: 30, align: 'center', search: false },
                   { name: 'delete', sortable: false, editable: false, width: 50, align: 'center', search: false, hidden: (!RoleAccess.IsDelete) },
                   { name: 'WFRoleUser_ID', advncName: 'Role User ID', index: 'WFRoleUser_ID', width: 0, editable: false, key: true, hidden: true },
                   { name: 'User_Name', index: 'User_Name', align: 'left', index: 'User_Name', width: 400, editable: true, edittype: 'select', editoptions: { value: function () { return WFUsers }, dataEvents: [{ type: 'change', fn: function () { $(this).ValidateUserRole() } }] } },
                    { name: 'ApprovalLimit', hidden: true, index: 'ApprovalLimit', align: 'right', index: 'ApprovalLimit', width: 400, editable: true, edittype: 'text', editoptions: { maxlength: 8 } },
                ],
                pager: '#WFRoleUserpager',
                rowNum: 5,
                rowList: [5, 10, 15, 25],
                viewrecords: true,
                sortname: 'WFRoleUser_ID',
                sortorder: 'asc',
                caption: '@HttpContext.GetGlobalResourceObject("Resource_en", "UserRoles")',
                width: '100%',
                height: '100%',
                jsonReader: {
                    root: "rows",
                    page: "page",
                    total: "total",
                    records: "records",
                    repeatitems: false
                },
                loadComplete: function (data) {

                    WFUsers = data.WFUsers;

                    WFRoleUserGrid.makeRowEditable('WFUserRoleEdtImg', RoleAccess.IsEdit);
                    WFRoleUserGrid.checkable('WFUserRoleDelChkImg');
                }
            });

            //WFRoleUserGrid Navigator
            WFRoleUserGrid.navGrid('#WFRoleUserpager', { view: false, add: false, edit: false, del: false, search: false, refresh: false });
            if (RoleAccess.IsAdd) {
                //WFRoleUserGrid + Button
                WFRoleUserGrid.navButtonAdd('#WFRoleUserpager', {
                    buttonicon: 'ui-icon-plus',
                    caption: '',
                    title: '@HttpContext.GetGlobalResourceObject("Resource_en", "Add")',
                    position: 'first',
                    onClickButton: function () {

                        WFRoleUserGrid.addGridRow('WFRoleUserRemove');
                    }
                });
            }
            if (RoleAccess.IsDelete) {
                //Adding - Button to WFRoleUserGrid
                WFRoleUserGrid.navButtonAdd('#WFRoleUserpager', {
                    buttonicon: 'ui-icon-minus',
                    caption: '',
                    title: '@HttpContext.GetGlobalResourceObject("Resource_en", "Delete")',
                    position: 'last',
                    onClickButton: function () {

                        WFRoleUserGrid.delGridRows($.url('/WorkFlow/WFUserRoleDelete'))
                    }

                });
            }

            if (RoleAccess.IsAdd || RoleAccess.IsEdit) {
                //Adding Save Button to WFRoleUserGrid
                WFRoleUserGrid.navButtonAdd('#WFRoleUserpager', {

                    buttonicon: 'ui-icon-disk',
                    caption: '',
                    title: '@HttpContext.GetGlobalResourceObject("Resource_en", "Save")',
                    position: 'last',
                    onClickButton: function () {
                        try {
                            var IsDEntrd = true;
                            var edited = false;
                            var rowIDS = WFRoleUserGrid.jqGrid('getDataIDs');
                            var RoleID = $('#hdnRoleID').val();

                            var datas = '{RoleID:\'' + RoleID + '\',rows:[';
                            var JWFRoleUserGrid = document.getElementById('WFRoleUserGrid');
                            for (i = 1; i <= rowIDS.length; i++) {

                                var Data = JWFRoleUserGrid.rows[i];
                                var editmode = $(Data.children[0].innerHTML).attr('editmode');

                                if (editmode != 'false') {
                                    var RoleUserID = Data.children[2].innerHTML;
                                    RoleUserID = (RoleUserID == '&nbsp;' || RoleUserID == '') ? '' : RoleUserID;
                                    var UserNm = Data.children[3].firstChild.value;
                                    var AprvlLmt = 1;
                                    UserNm == '-1' ? $(Data.children[3].firstChild).addClass('input-validation-error') : $(Data.children[3].firstChild).removeClass('input-validation-error');
                                    AprvlLmt.length == 0 ? $(Data.children[4].firstChild).addClass('input-validation-error') : isNaN(AprvlLmt) == true ? $(Data.children[4].firstChild).addClass('input-validation-error') : $(Data.children[4].firstChild).removeClass('input-validation-error');
                                    //isNaN(AprvlLmt) == true ? $(Data.children[4].firstChild).addClass('input-validation-error') : $(Data.children[4].firstChild).removeClass('input-validation-error');

                                    if (UserNm == '-1' || AprvlLmt.length == 0 || isNaN(AprvlLmt)) {

                                        IsDEntrd = false;
                                    }
                                    else {
                                        if (editmode == 'true' || editmode == '') {
                                            datas = datas.concat('{RoleUserID:\'' + RoleUserID + '\',UserID:\'' + UserNm + '\', AprvlLmt:\'' + AprvlLmt + '\'},');
                                            edited = true;
                                        }
                                    }
                                }

                            }
                            datas = datas.concat(']}');

                            if (IsDEntrd) {
                                if (!edited) {

                                    alert('@HttpContext.GetGlobalResourceObject("Resource_en", "NoChangesMade")');
                                }
                                else {
                                    $.ajax(
                                        {
                                            url: $.url('/WorkFlow/WFUserRoleSave'),
                                            data: { key: datas },
                                            type: 'POST',
                                            datatype: 'json',
                                            success: function () {

                                                WFRoleUserGrid.trigger('reloadGrid');
                                            },
                                            error: function (e) {

                                                alert("Error in Saving");
                                            }
                                        });
                                }
                            }
                            else {

                                alert('@HttpContext.GetGlobalResourceObject("Resource_en", "HighlightedFieldsareMandatory")');
                            }
                        }
                        catch (ex) {
                        }
                    }
                });
            }
            //Adding refresh Button to WFRoleUserGrid
            WFRoleUserGrid.navButtonAdd('#WFRoleUserpager', {
                buttonicon: 'ui-icon-refresh',
                caption: '',
                title: '@HttpContext.GetGlobalResourceObject("Resource_en", "Refresh")',
                position: 'last',
                onClickButton: function () {
                    WFRoleUserGrid.trigger('reloadGrid');

                }
            });

            WFGrid.advanceSearch();
            WFStepsGrid.advanceSearch();

            //--- To validate user role associations-----//

            $.fn.ValidateUserRole = function () {
                try {
                    var selval = $(this).attr("value");
                    var rowData = WFRoleUserGrid.jqGrid('getDataIDs');
                    var ID = $(this).attr("id");
                    var CurrentID = $($(this).parent('td').parent('tr')).attr('id');

                    var isValid = true;
                    var RoleID = $("#hdnRoleID").attr("value");
                    var CurrentMode = '';
                    var Roleid = '';

                    $.post($.absoluteurl('/WorkFlow/validateUserRole?UserID=' + selval + '&RoleID=' + RoleID), function (response) {
                        if (response == "True") {

                            alert('@HttpContext.GetGlobalResourceObject("Resource_en", "Duplicateentries")');
                            document.getElementById(ID).selectedIndex = 0;
                        }
                        else {

                            for (var i = 0; i < rowData.length; i++) {
                                if (rowData[i] != CurrentID) {
                                    CurrentMode = $(WFRoleUserGrid.getCell(rowData[i], 'edit')).attr('editmode');
                                    if (CurrentMode != '') {
                                        if ($('#' + ID + ' option:selected').text() == WFRoleUserGrid.getCell(rowData[i], 'User_Name')) {
                                            alert('@HttpContext.GetGlobalResourceObject("Resource_en", "Duplicateentries")');
                                            document.getElementById(ID).selectedIndex = 0;
                                            break;
                                        }
                                    }
                                    else {
                                        var ddlSel = $(WFRoleUserGrid.getCell(rowData[i], 'User_Name'));
                                        var prevText = document.getElementById(ddlSel.attr('id')).options[document.getElementById(ddlSel.attr('id')).selectedIndex].text
                                        if ($('#' + ID + ' option:selected').text() == prevText) {
                                            alert('@HttpContext.GetGlobalResourceObject("Resource_en", "Duplicateentries")');
                                                document.getElementById(ID).selectedIndex = 0;
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                    });
                    }
                catch (e) {
                    $(this).LogException("ValidateUserRole", e.description, e.name, e.number);
                }
            }

        });

    </script>
</head>
<body class="body">
    <div>
        <table id="WFGrid">
            <tr>
                <td />
            </tr>
        </table>
        <div id="WFpager">
        </div>
    </div>
    <div id="WFDetail" class="dialog" style="display: none;">
        <input id="hdnWFID" type="hidden" />
        <div id="WFDetailTabs">
            <ul>
                <li><a href="#WFRolesTab">@HttpContext.GetGlobalResourceObject("Resource_en", "Roles")</a></li>
                <li><a href="#WFStepsTab">@HttpContext.GetGlobalResourceObject("Resource_en", "Steps")</a></li>
                <li><a href="#WFActionTab">@HttpContext.GetGlobalResourceObject("Resource_en", "Actions")</a></li>
                <li><a href="#WFStepLinkTab">@HttpContext.GetGlobalResourceObject("Resource_en", "StepLink")</a></li>
            </ul>
            <div id="WFRolesTab">
                <table id="WFRolesGrid">
                    <tr>
                        <td />
                    </tr>

                </table>
                <div id="WFRolespager"></div>

            </div>
            <div id="WFStepsTab">
                <table id="WFStepsGrid">
                    <tr>
                        <td />
                    </tr>

                </table>
                <div id="WFStepspager"></div>

            </div>
            <div id="WFActionTab">
                <table id="WFActionGrid">
                    <tr>
                        <td />
                    </tr>

                </table>
                <div id="WFActionpager"></div>

            </div>
            <div id="WFStepLinkTab" style="overflow: scroll">
                @HttpContext.GetGlobalResourceObject("Resource_en", "Company"):
                <select id="ddSLCmpny">
                </select>
                <table id="WFStepLinkGrid">
                    <tr>
                        <td />
                    </tr>
                </table>
                <div id="WFStepLinkpager"></div>
            </div>
        </div>
    </div>
    <div id="WFRoleUserDialog" class="dialog" style="display: none">
        <input id="hdnRoleID" type="hidden" />
        <table id="WFRoleUserGrid">
            <tr>
                <td />
            </tr>
        </table>
        <div id="WFRoleUserpager"></div>
    </div>
</body>
</html>
