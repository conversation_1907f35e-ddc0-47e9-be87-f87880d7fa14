﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ActionNo" xml:space="preserve">
    <value>Non</value>
  </data>
  <data name="Actiontobetaken" xml:space="preserve">
    <value>Les mesures à prendre</value>
  </data>
  <data name="ActionYes" xml:space="preserve">
    <value>Oui</value>
  </data>
  <data name="AddedSuccessfully" xml:space="preserve">
    <value>Ajouté avec succès</value>
  </data>
  <data name="AddMode" xml:space="preserve">
    <value>Ajouter</value>
  </data>
  <data name="AddOnlyimagefile!" xml:space="preserve">
    <value>Veuillez ajouter uniquement le fichier image</value>
  </data>
  <data name="ALPHAINDEX" xml:space="preserve">
    <value>INDEX / PAGES ALPHABÉTIQUES</value>
  </data>
  <data name="AlreadyaUserLoggedinthisBrowser" xml:space="preserve">
    <value>Un autre utilisateur s'est déja connecté dans ce navigateur</value>
  </data>
  <data name="Alternate Part Number" xml:space="preserve">
    <value>Autre numéro de pièce</value>
  </data>
  <data name="API" xml:space="preserve">
    <value>Index alphabétique?</value>
  </data>
  <data name="Apply" xml:space="preserve">
    <value>Appliquer le filtre</value>
  </data>
  <data name="Areyousure" xml:space="preserve">
    <value>Êtes-vous sûr?</value>
  </data>
  <data name="Areyousure?" xml:space="preserve">
    <value>Êtes-vous sûr?</value>
  </data>
  <data name="AreYouSureYouWantToCancel" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir annuler?</value>
  </data>
  <data name="AreYouSureYouWantToClear" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir effacer?</value>
  </data>
  <data name="AreYouSureYouWantToClose" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir fermer?</value>
  </data>
  <data name="AreYouSureYouWantToDelete" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir supprimer?</value>
  </data>
  <data name="AreYouSureYouWantToDeleteDoc" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir Effacer?</value>
  </data>
  <data name="AreYousureyouwanttoExcelUploadData" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir télécharger des données Excel?</value>
  </data>
  <data name="AreYousureyouwanttoExcelUploadvalidData" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir télécharger des données Excel valides?</value>
  </data>
  <data name="AreYousureyouwanttologout" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir vous déconnecter?</value>
  </data>
  <data name="AreYousureyouwanttoOvercopy" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir copier?</value>
  </data>
  <data name="AreYousureyouwanttoOverwrite" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir écraser le fichier?</value>
  </data>
  <data name="AreYouSureYouWantToRemove" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir supprimer du panier?</value>
  </data>
  <data name="AreYousureyouwanttoreplace" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir remplacer?</value>
  </data>
  <data name="AreYouSureYouWantToReset" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir réinitialiser?</value>
  </data>
  <data name="AssemblyNo" xml:space="preserve">
    <value>Numéro d'Assemblage</value>
  </data>
  <data name="BOM" xml:space="preserve">
    <value>Nomenclature</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>Marque</value>
  </data>
  <data name="BrandInformationissavedsuccessfully" xml:space="preserve">
    <value>Les informations sur la marque ont été enregistrées avec succès</value>
  </data>
  <data name="BuildTree" xml:space="preserve">
    <value>Construire arborescence</value>
  </data>
  <data name="CannotBuildTreePlsAddMissingMainSubToplevel" xml:space="preserve">
    <value>Veuillez ajouter les sections principales et secondaires manquantes au Maître (Master)  de niveau supérieur</value>
  </data>
  <data name="CannotpublishCheckerrorreportformoreinfo" xml:space="preserve">
    <value>Nous ne pouvons pas publier. Consultez le rapport d'erreur pour plus d'informations</value>
  </data>
  <data name="CatalogueDeletedSuccessfully" xml:space="preserve">
    <value>Supprimé avec succès</value>
  </data>
  <data name="CatPreview" xml:space="preserve">
    <value>Aperçu du catalogue</value>
  </data>
  <data name="CheckConnection" xml:space="preserve">
    <value>Vérifier la connexion .. Connexion impossible</value>
  </data>
  <data name="CheckConnectionUnabletoConnect" xml:space="preserve">
    <value>Vérifier la connexion .. Impossible de se connecter</value>
  </data>
  <data name="Common Notes" xml:space="preserve">
    <value>Notes communes</value>
  </data>
  <data name="Common_Notes" xml:space="preserve">
    <value>Notes communes</value>
  </data>
  <data name="ConfirmOrderDel" xml:space="preserve">
    <value>Veuillez confirmer pour supprimer la commande de la base de données</value>
  </data>
  <data name="Contains" xml:space="preserve">
    <value>Contient</value>
  </data>
  <data name="CurrentVIN#" xml:space="preserve">
    <value>NIV (Numéro Identification Véhicule) actuel</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>Client</value>
  </data>
  <data name="Customer Name" xml:space="preserve">
    <value>Nom du Client</value>
  </data>
  <data name="Customer Part MFG Code" xml:space="preserve">
    <value>Code de fabrication Client</value>
  </data>
  <data name="Customer Part Number" xml:space="preserve">
    <value>Numéro de pièce Client</value>
  </data>
  <data name="CustomerAdd" xml:space="preserve">
    <value>Client - Ajouter</value>
  </data>
  <data name="CustomerName" xml:space="preserve">
    <value>Nom du Client</value>
  </data>
  <data name="CustomerPrint" xml:space="preserve">
    <value>Numéro de client</value>
  </data>
  <data name="CustomerSearch" xml:space="preserve">
    <value>Client - Chercher</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="DBAssemblyRemarks" xml:space="preserve">
    <value>Assemblage - Supprimer</value>
  </data>
  <data name="DBConfigurationnotsetPleaseContactAdmin" xml:space="preserve">
    <value>Configuration de base de données non définie. Veuillez contacter l'administrateur</value>
  </data>
  <data name="DBMainSec" xml:space="preserve">
    <value>Section principale de la base de données</value>
  </data>
  <data name="DBMainSectionRemarks" xml:space="preserve">
    <value>Section principale - Supprimer</value>
  </data>
  <data name="DBORderNo" xml:space="preserve">
    <value>Numéro de commande DB</value>
  </data>
  <data name="DBPart_Assembly_No" xml:space="preserve">
    <value>Numéro d'assemblage de la base de données</value>
  </data>
  <data name="DBSubSec" xml:space="preserve">
    <value>Sous-section de la base de données</value>
  </data>
  <data name="DBSubSectionRemarks" xml:space="preserve">
    <value>Sous-section - Supprimer</value>
  </data>
  <data name="DelOrder" xml:space="preserve">
    <value>Supprimer la commande</value>
  </data>
  <data name="Dependancyfoundinactiveit" xml:space="preserve">
    <value>Dépendance trouvée</value>
  </data>
  <data name="DependencyFoundandrecordsalreadybeenInactivated" xml:space="preserve">
    <value>Dépendance détectée et enregistrements déjà désactivés</value>
  </data>
  <data name="Dependencyfoundcannotdeletetherecords" xml:space="preserve">
    <value>La dépendance détectée ne peut pas supprimer les enregistrements</value>
  </data>
  <data name="DependencyFoundRequestyoutoIn-Activeitsinceitcannotbedeleted" xml:space="preserve">
    <value>Dépendance détectée: vous devez la rendre inactive, car elle ne peut pas être supprimée</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Dev" xml:space="preserve">
    <value>Dev.</value>
  </data>
  <data name="DownloadPDF" xml:space="preserve">
    <value>Télécharger le PDF</value>
  </data>
  <data name="Doyouwanttoaddrecommendedpartsalongwithselectedpart" xml:space="preserve">
    <value>Voulez-vous ajouter des pièces recommandées avec la pièce sélectionnée?</value>
  </data>
  <data name="DoYouWanttoPublishTheOrder" xml:space="preserve">
    <value>Voulez-vous publier la commande?</value>
  </data>
  <data name="Doyouwanttoremoverecommendedpartalong" xml:space="preserve">
    <value>Voulez-vous supprimer les pièces recommandées avec la pièce sélectionnée?</value>
  </data>
  <data name="EditMode" xml:space="preserve">
    <value>Modifier</value>
  </data>
  <data name="EditOptions" xml:space="preserve">
    <value>Modifier les options</value>
  </data>
  <data name="EndWith" xml:space="preserve">
    <value>Terminer par</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>Anglais</value>
  </data>
  <data name="EnglishOptionDescription" xml:space="preserve">
    <value>Description de l'option en anglais</value>
  </data>
  <data name="EnterCustomer" xml:space="preserve">
    <value>Entrez le numéro de client</value>
  </data>
  <data name="EnterCustomerName" xml:space="preserve">
    <value>Entrez le nom du client</value>
  </data>
  <data name="Entervaliddata" xml:space="preserve">
    <value>Entrez des données valides</value>
  </data>
  <data name="Equal" xml:space="preserve">
    <value>Égale</value>
  </data>
  <data name="FileAssemblyRemarks" xml:space="preserve">
    <value>Assemblage Varnet - Inclure</value>
  </data>
  <data name="FileMainSectionRemarks" xml:space="preserve">
    <value>Section principale Varnet - Inclure</value>
  </data>
  <data name="Filesizeislargerthan 5MB!" xml:space="preserve">
    <value>La taille du fichier est supérieure à 5 Mo (MegaOctet)</value>
  </data>
  <data name="FileSubSectionRemarks" xml:space="preserve">
    <value>Sous-section Varnet - Inclure</value>
  </data>
  <data name="First" xml:space="preserve">
    <value>D'abord</value>
  </data>
  <data name="French" xml:space="preserve">
    <value>Français</value>
  </data>
  <data name="FrenchOptionDescription" xml:space="preserve">
    <value>Description de l'option en français</value>
  </data>
  <data name="French_Description" xml:space="preserve">
    <value>Descriptif en français</value>
  </data>
  <data name="French_Part_Assembly_Description" xml:space="preserve">
    <value>Description Français</value>
  </data>
  <data name="FromMFGCode" xml:space="preserve">
    <value>À partir du code de fabrication</value>
  </data>
  <data name="FromPartNumber" xml:space="preserve">
    <value>À partir du numéro de la pièce</value>
  </data>
  <data name="FromQty" xml:space="preserve">
    <value>À partir de la quantité</value>
  </data>
  <data name="Function Group Code" xml:space="preserve">
    <value>Code de groupe de fonctions</value>
  </data>
  <data name="FunctionGroupCode" xml:space="preserve">
    <value>Code du groupe de fonctions</value>
  </data>
  <data name="FunctionGroupCodeAlreadyPresent" xml:space="preserve">
    <value>Le code de groupe de fonctions donné est déjà disponible dans les détails du groupe de fonctions</value>
  </data>
  <data name="FunctionGroupName" xml:space="preserve">
    <value>Nom du groupe de fonctions</value>
  </data>
  <data name="GreaterThan" xml:space="preserve">
    <value>Plus grand que</value>
  </data>
  <data name="GreaterThanorEqualto" xml:space="preserve">
    <value>Plus grand ou égal à</value>
  </data>
  <data name="ID" xml:space="preserve">
    <value>Identifiant</value>
  </data>
  <data name="ImportedDateTime" xml:space="preserve">
    <value>Date d'importation</value>
  </data>
  <data name="ImportIgnoreOptions" xml:space="preserve">
    <value>Importer Ignorer les options</value>
  </data>
  <data name="ImportVarNetFile" xml:space="preserve">
    <value>Importer un fichier Varnet</value>
  </data>
  <data name="Import_Status" xml:space="preserve">
    <value>Statut d'importation</value>
  </data>
  <data name="IncludeServiceManual" xml:space="preserve">
    <value>Manuel de service? </value>
  </data>
  <data name="Informationisnotsaved" xml:space="preserve">
    <value>Les informations ne sont pas enregistrées</value>
  </data>
  <data name="InsertedSuccesfully" xml:space="preserve">
    <value>Ajouté avec succès</value>
  </data>
  <data name="InvalidCustomer" xml:space="preserve">
    <value>Client non valide</value>
  </data>
  <data name="InvalidDate" xml:space="preserve">
    <value>Date non valide</value>
  </data>
  <data name="Invalidemailid" xml:space="preserve">
    <value>Identification e-mail non valide</value>
  </data>
  <data name="InvalidLoginID" xml:space="preserve">
    <value>Identification de connexion non valide</value>
  </data>
  <data name="InvalidOrderNumber" xml:space="preserve">
    <value>Numéro de commande non valide</value>
  </data>
  <data name="InvalidUserID/Password" xml:space="preserve">
    <value>Identification utilisateur / mot de passe non valide</value>
  </data>
  <data name="Is Active" xml:space="preserve">
    <value>Est Actif</value>
  </data>
  <data name="Is Main Section" xml:space="preserve">
    <value>Est la section principale</value>
  </data>
  <data name="Is Part Section" xml:space="preserve">
    <value>Est dans la section des pièces</value>
  </data>
  <data name="Is Purchasable" xml:space="preserve">
    <value>Est Achetable</value>
  </data>
  <data name="Is Section" xml:space="preserve">
    <value>Est la section</value>
  </data>
  <data name="Is Service Section" xml:space="preserve">
    <value>Est la section de service</value>
  </data>
  <data name="Is Visible" xml:space="preserve">
    <value>Est visible</value>
  </data>
  <data name="IsActive" xml:space="preserve">
    <value>Est Actif</value>
  </data>
  <data name="IsEmpty" xml:space="preserve">
    <value>Est vide</value>
  </data>
  <data name="IsEqual" xml:space="preserve">
    <value>Est égal</value>
  </data>
  <data name="IsNull" xml:space="preserve">
    <value>Est nul</value>
  </data>
  <data name="IsReplacedBy" xml:space="preserve">
    <value>est remplacé par ?</value>
  </data>
  <data name="Is_PartSection" xml:space="preserve">
    <value>Est la section des pièces?</value>
  </data>
  <data name="Is_Purchasable" xml:space="preserve">
    <value>Est Achetable?</value>
  </data>
  <data name="Is_Section" xml:space="preserve">
    <value>Est la section principale?</value>
  </data>
  <data name="Is_ServiceSection" xml:space="preserve">
    <value>Est la section de service?</value>
  </data>
  <data name="Is_SubSection" xml:space="preserve">
    <value>Est la section?</value>
  </data>
  <data name="Is_TopSection" xml:space="preserve">
    <value>Est-ce de niveau supérieur?</value>
  </data>
  <data name="ITEM" xml:space="preserve">
    <value>Numéro d'article</value>
  </data>
  <data name="ItemNo" xml:space="preserve">
    <value>Numéro d'article</value>
  </data>
  <data name="ItemQty" xml:space="preserve">
    <value>Quantité d'article</value>
  </data>
  <data name="ITPDocuments" xml:space="preserve">
    <value>Documents ITP</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Langue</value>
  </data>
  <data name="Last" xml:space="preserve">
    <value>Dernier</value>
  </data>
  <data name="LessThan" xml:space="preserve">
    <value>Moins que</value>
  </data>
  <data name="LessthanorEqualto" xml:space="preserve">
    <value>Inférieur ou égal à</value>
  </data>
  <data name="Level" xml:space="preserve">
    <value>Niveau</value>
  </data>
  <data name="Like" xml:space="preserve">
    <value>Comme</value>
  </data>
  <data name="LOCALISATION" xml:space="preserve">
    <value>LOCALISATION</value>
  </data>
  <data name="LogoHeight" xml:space="preserve">
    <value>Largeur et hauteur du logo</value>
  </data>
  <data name="MainSectionMissingTopLevelMaster" xml:space="preserve">
    <value>Section principale manquante - Maître (Master) de haut niveau</value>
  </data>
  <data name="Matching_OP" xml:space="preserve">
    <value>Codes d'option correspondants</value>
  </data>
  <data name="MaximumZoomed-In" xml:space="preserve">
    <value>Zoom avant maximum</value>
  </data>
  <data name="MaximumZoomed-Out" xml:space="preserve">
    <value>Zoom arrière maximal</value>
  </data>
  <data name="MFG Code" xml:space="preserve">
    <value>Code MFG</value>
  </data>
  <data name="MFGAssociation" xml:space="preserve">
    <value>Association manufacturière</value>
  </data>
  <data name="MFGCode" xml:space="preserve">
    <value>Code de fabrication</value>
  </data>
  <data name="MFGCodeAlreadyPresent" xml:space="preserve">
    <value>Le Code de fabrication est déjà disponible dans les données manufacturières</value>
  </data>
  <data name="MFGYear" xml:space="preserve">
    <value>Année de fabrication</value>
  </data>
  <data name="MFG_Code" xml:space="preserve">
    <value>Code de fabrication</value>
  </data>
  <data name="Model" xml:space="preserve">
    <value>Modèle</value>
  </data>
  <data name="ModelAdd" xml:space="preserve">
    <value>Ajouter un modèle</value>
  </data>
  <data name="ModelSearch" xml:space="preserve">
    <value>Recherche de modèle</value>
  </data>
  <data name="MsgAllowOnlyAlphabetsNumbers" xml:space="preserve">
    <value>Veuillez n'utiliser que des lettres alphabetiques</value>
  </data>
  <data name="MsgAssemblyAssociationNotFound" xml:space="preserve">
    <value>Assemblages associés introuvables</value>
  </data>
  <data name="MsgBeforeSaveConditionPleaseAddCondition" xml:space="preserve">
    <value>Avant d'enregistrer, veuillez ajouter une condition</value>
  </data>
  <data name="MsgCannotcopypartdetailstoitselfpartNo" xml:space="preserve">
    <value>Impossible de copier les détails du numéro de pièce / assemblage, dans le même numéro de pièce / assemblage</value>
  </data>
  <data name="MsgCopiedsuccessfully" xml:space="preserve">
    <value>Copié avec succès</value>
  </data>
  <data name="MsgCurrentName" xml:space="preserve">
    <value>Aucun changement dans la pièce # trouvé</value>
  </data>
  <data name="MsgCustomerNotFound" xml:space="preserve">
    <value>Client non trouvé</value>
  </data>
  <data name="MsgCustomerPartAddedSuccessfull" xml:space="preserve">
    <value>Pièce client ajoutée avec succès</value>
  </data>
  <data name="MsgCustomerPartUpdatedSuccessfull" xml:space="preserve">
    <value>Pièce client mise à jour avec succès</value>
  </data>
  <data name="MsgDataNotPresent" xml:space="preserve">
    <value>Données non trouvées</value>
  </data>
  <data name="MsgDoYouWanttoremoveAllAssociatedOrders" xml:space="preserve">
    <value>FN-Do you want remove all associated order?</value>
  </data>
  <data name="MsgDuplicateEntry" xml:space="preserve">
    <value>Dupliquer l'Entrée</value>
  </data>
  <data name="MsgDuplicateEntryForAbbrevation" xml:space="preserve">
    <value>Dupliquer l'Entrée pour l'Abréviation</value>
  </data>
  <data name="MsgDuplicateEntryForCartName" xml:space="preserve">
    <value>Dupliquer l'Entrée pour le Nom du Panier</value>
  </data>
  <data name="MsgDuplicateEntryForCustomerCode" xml:space="preserve">
    <value>Dupliquer l'Entrée pour le code client</value>
  </data>
  <data name="MsgDuplicateEntryForCustomerName" xml:space="preserve">
    <value>Entrée en double pour le nom du client</value>
  </data>
  <data name="MsgDuplicateEntryForCustomerPharmaCode" xml:space="preserve">
    <value>Dupliquer l'entrée pour le code Parma</value>
  </data>
  <data name="MsgDuplicateEntryForMainSectionCode" xml:space="preserve">
    <value>Dupliquer l'entrée pour la section principale</value>
  </data>
  <data name="MsgDuplicateEntryForMaskingActionCode" xml:space="preserve">
    <value>Dupliquer l'Entrée pour le code d'action de masquage</value>
  </data>
  <data name="MsgDuplicateEntryForMaskingActionDescription" xml:space="preserve">
    <value>Dupliquer l'entrée pour la description de l'action de masquage</value>
  </data>
  <data name="MsgDuplicateEntryForOptionCode" xml:space="preserve">
    <value>Dupliquer l'Entrée pour le code d'option</value>
  </data>
  <data name="MsgDuplicateEntryForOptionDescription" xml:space="preserve">
    <value>Dupliquer l'entrée pour la description de l'option</value>
  </data>
  <data name="MsgDuplicateEntryForSectionCode" xml:space="preserve">
    <value>Dupliquer l'Entrée pour le code de section</value>
  </data>
  <data name="MsgDuplicateEntryForSubSectionCode" xml:space="preserve">
    <value>Dupliquer l'entrée pour le code de sous-section</value>
  </data>
  <data name="MsgDuplicateEntryForSupersessionRemarks" xml:space="preserve">
    <value>Dupliquer l'entrée pour les remarques de remplacement</value>
  </data>
  <data name="MsgDuplicateEntryForUserEmployeeCode" xml:space="preserve">
    <value>Dupliquer l'Entrée pour le code d'employé utilisateur</value>
  </data>
  <data name="MsgDuplicateEntryForUserLoginID" xml:space="preserve">
    <value>Dupliquer l'Entrée pour l'Identification de connexion de l'utilisateur</value>
  </data>
  <data name="MsgDuplicateEntryForUserRole" xml:space="preserve">
    <value>Dupliquer l'entrée pour le rôle d'utilisateur</value>
  </data>
  <data name="MsgDuplicateEntryForVendorCode" xml:space="preserve">
    <value>Dupliquer l'Entrée pour le pour</value>
  </data>
  <data name="MsgDuplicateEntryForVendorName" xml:space="preserve">
    <value>Dupliquer l'entrée pour le nom du fournisseur</value>
  </data>
  <data name="MsgDuplicateEntryForVendorParamCode" xml:space="preserve">
    <value>Dupliquer l'Entrée pour le code de paramètre du fournisseur</value>
  </data>
  <data name="MsgDuplicateEntryForVINNumber" xml:space="preserve">
    <value>Numéro de route déjà modifié pour ce NIV (Numéro Identification Véhicule)</value>
  </data>
  <data name="MsgEmailidisnotregistered" xml:space="preserve">
    <value>L'identifiant de messagerie électronique (E-mail) n'est pas enregistré</value>
  </data>
  <data name="MsgEmailsentsuccessfully" xml:space="preserve">
    <value>E-mail envoyé avec succès</value>
  </data>
  <data name="MsgEnteredLevelisNotValid" xml:space="preserve">
    <value>Niveau invalide</value>
  </data>
  <data name="MsgEnterEmailIDNotValid" xml:space="preserve">
    <value>Identification du e-mail non valide</value>
  </data>
  <data name="MsgEnterMobileNumberNotValid" xml:space="preserve">
    <value>Numéro de portable (Téléphone Cellulaire) invalide</value>
  </data>
  <data name="MsgEnterNumberisNotValid" xml:space="preserve">
    <value>Numéro Invalide</value>
  </data>
  <data name="MsgEnterOrderQtyisNotValid" xml:space="preserve">
    <value>Quantité de commande non valide</value>
  </data>
  <data name="MsgEntervaliddatewithvalidformate" xml:space="preserve">
    <value>Entrez une date valide avec un format JJ-MMM-AAAA</value>
  </data>
  <data name="MsgError" xml:space="preserve">
    <value>Erreur</value>
  </data>
  <data name="MsgFaildToAdd" xml:space="preserve">
    <value>Échec de l'ajout</value>
  </data>
  <data name="MsgFaildToAdd1" xml:space="preserve">
    <value>Échec de l'ajout</value>
  </data>
  <data name="MsgFaildToDelete" xml:space="preserve">
    <value>Échec de la suppression</value>
  </data>
  <data name="MsgFaildToDelete1" xml:space="preserve">
    <value>Échec de la suppression</value>
  </data>
  <data name="MsgFaildToUpdate" xml:space="preserve">
    <value>Échec de mise à jour</value>
  </data>
  <data name="MsgFaildToUpdate1" xml:space="preserve">
    <value>Échec de mise à jour</value>
  </data>
  <data name="MsgFailedToPublish" xml:space="preserve">
    <value>Échec de mise à Publier</value>
  </data>
  <data name="MsgFailedToUpdate" xml:space="preserve">
    <value>Échec de mise à jour</value>
  </data>
  <data name="MsgFalse" xml:space="preserve">
    <value>FALSE</value>
  </data>
  <data name="MsgFunctionGroupDeleteSuccessfull" xml:space="preserve">
    <value>Groupe de fonctions utilisé supprimé avec succès</value>
  </data>
  <data name="MsgFunctionGroupUsedAddedSuccessfull" xml:space="preserve">
    <value>Groupe de fonctions utilisé ajouté avec succès</value>
  </data>
  <data name="MsgFunctionGroupUsedUpdatedSuccessfull" xml:space="preserve">
    <value>Groupe de fonctions utilisé mis à jour avec succès</value>
  </data>
  <data name="MsgHasLockedThisAssemblyForModification" xml:space="preserve">
    <value>A verrouillé cet assemblage pour modification</value>
  </data>
  <data name="MsgHighlightedFieldsAreMandatory" xml:space="preserve">
    <value>Les champs en surbrillance sont obligatoires</value>
  </data>
  <data name="MsgIncorrectPwd" xml:space="preserve">
    <value>Mot de passe incorrect</value>
  </data>
  <data name="MsgInformationalreadyavailable" xml:space="preserve">
    <value>Informations déjà disponibles</value>
  </data>
  <data name="MsgInvalidCurrentRoadNumber" xml:space="preserve">
    <value>Numéro de route actuel non valide</value>
  </data>
  <data name="MsgInvalidCustomerName" xml:space="preserve">
    <value>Nom de client non valide</value>
  </data>
  <data name="MsgInvalidModelName" xml:space="preserve">
    <value>Nom de modèle non valide</value>
  </data>
  <data name="MsgInvalidOrderNumber" xml:space="preserve">
    <value>Numéro de commande non valide</value>
  </data>
  <data name="MsgInvalidPartNumber" xml:space="preserve">
    <value>Numéro de pièce non valide</value>
  </data>
  <data name="MsgInvalidProductTypeName" xml:space="preserve">
    <value>Nom de type de produit non valide</value>
  </data>
  <data name="MsgInvalidVINNumber" xml:space="preserve">
    <value>NIV (Numéro Identification Véhicule) non valide</value>
  </data>
  <data name="MsgMainSectionNotFound" xml:space="preserve">
    <value>Aucune section principale trouvée</value>
  </data>
  <data name="MsgMFGAddedSuccessfull" xml:space="preserve">
    <value>Ajouté avec succès</value>
  </data>
  <data name="MsgMFGAddedSuccessfull1" xml:space="preserve">
    <value>Ajouté avec succès</value>
  </data>
  <data name="MsgMFGUpdatedSuccessfull" xml:space="preserve">
    <value>Mis à jour avec succès</value>
  </data>
  <data name="MsgMFGUpdatedSuccessfull1" xml:space="preserve">
    <value>Mis à jour avec succès</value>
  </data>
  <data name="MsgNewOwnershouldnotbesamehascurrentOwner" xml:space="preserve">
    <value>FN-New owner should not be same has current owner</value>
  </data>
  <data name="MsgNewPasswordcantbesameasPrevious" xml:space="preserve">
    <value>Le nouveau mot de passe ne peut pas être identique au précédent</value>
  </data>
  <data name="MsgNodataFound" xml:space="preserve">
    <value>Aucune donnée disponible</value>
  </data>
  <data name="MsgNoInformationEnterValidVinnumber" xml:space="preserve">
    <value>Aucune information trouvée pour générer Vinshortnum Entrez un numéro de Vin valide</value>
  </data>
  <data name="MsgNoInformationFound" xml:space="preserve">
    <value>Aucune information trouvée</value>
  </data>
  <data name="MsgNoLocaleDetailsFound" xml:space="preserve">
    <value>Aucun détail de langue trouvé</value>
  </data>
  <data name="MsgNoOneFound" xml:space="preserve">
    <value>Personne n'a été trouvé</value>
  </data>
  <data name="MsgNoPartInCartToExport" xml:space="preserve">
    <value>Les pièces ne se trouvent pas dans le panier pour l'exportation</value>
  </data>
  <data name="MsgNoPartToRequestQuote" xml:space="preserve">
    <value>Veuillez sélectionner une pièce pour demander un devis</value>
  </data>
  <data name="MsgNoRangeFound" xml:space="preserve">
    <value>Veuillez donner l'Intervalle</value>
  </data>
  <data name="MsgNoRecentsFound" xml:space="preserve">
    <value>Aucun récent trouvé</value>
  </data>
  <data name="MsgNoRecord" xml:space="preserve">
    <value>Aucun Enregistrement Trouvé</value>
  </data>
  <data name="MsgNoRecordsToUpdate" xml:space="preserve">
    <value>Aucun enregistrement à mettre à jour</value>
  </data>
  <data name="MsgNoSection" xml:space="preserve">
    <value>Aucune section trouvée</value>
  </data>
  <data name="MsgNoSubSection" xml:space="preserve">
    <value>Aucune sous-section trouvée</value>
  </data>
  <data name="MsgNoSuchDataFound" xml:space="preserve">
    <value>Aucune donnée de ce type n'a été trouvée</value>
  </data>
  <data name="MsgNoUserIsAssociated" xml:space="preserve">
    <value>Aucun utilisateur n'est associé au client</value>
  </data>
  <data name="MsgNumberAlreadyFound" xml:space="preserve">
    <value>Le numéro de pièce existe déjà</value>
  </data>
  <data name="MsgOrder#NotFound" xml:space="preserve">
    <value>Numéro de Commande introuvable</value>
  </data>
  <data name="MsgOrderAssociationNotFound" xml:space="preserve">
    <value>L'association de commande est introuvable pour cette option</value>
  </data>
  <data name="MsgOrderNumber" xml:space="preserve">
    <value>Numéro de commande</value>
  </data>
  <data name="MsgOrderNumberMandatory" xml:space="preserve">
    <value>Numéro de Commande obligatoire</value>
  </data>
  <data name="MsgPartsmergedwithselectedcart" xml:space="preserve">
    <value>Fusionné avec le panier</value>
  </data>
  <data name="MsgPasswordChangedSuccesfully" xml:space="preserve">
    <value>Le mot de passe a été changé avec succès</value>
  </data>
  <data name="MsgPleaseAssociateOrders" xml:space="preserve">
    <value>Veuillez associer les commandes</value>
  </data>
  <data name="MsgPleaseselectatlest2cataloguebeforecompare" xml:space="preserve">
    <value>Veuillez sélectionner au moins 2 numéros de commande de catalogue avant de comparer</value>
  </data>
  <data name="MsgPleaseselectCarttogetAttachmentinMail" xml:space="preserve">
    <value>Veuillez sélectionner le panier pour obtenir la pièce jointe dans le courrier</value>
  </data>
  <data name="MsgPleaseSelectCheckbox" xml:space="preserve">
    <value>Veuillez sélectionner la case à cocher</value>
  </data>
  <data name="MsgPleaseselectfiletype" xml:space="preserve">
    <value>Veuillez sélectionner le type de fichier</value>
  </data>
  <data name="MsgPleaseSelectMFGCode" xml:space="preserve">
    <value>Veuillez sélectionner le Code de fabrication</value>
  </data>
  <data name="MsgPleaseSelectModelPartCatalogue" xml:space="preserve">
    <value>Sélectionnez le numéro de pièce, le catalogue, le modèle</value>
  </data>
  <data name="MsgPleasewait" xml:space="preserve">
    <value>Veuillez patienter</value>
  </data>
  <data name="MsgPleaseWaitWhilewereadHotSpots" xml:space="preserve">
    <value>Veuillez patienter, pendant que nous lisons et dessinons des points chauds (Hotspots)</value>
  </data>
  <data name="MsgPlsSelectBrand" xml:space="preserve">
    <value>Veuillez sélectionner la marque</value>
  </data>
  <data name="MsgPlsSelectforToc" xml:space="preserve">
    <value>Veuillez sélectionner le numéro de route, le NIV (Numéro Identification Véhicule), le numéro de commande, le modèle, le nom du client</value>
  </data>
  <data name="MsgPublishedAssembly" xml:space="preserve">
    <value>Publié avec succès</value>
  </data>
  <data name="MsgPwdDidntmatched" xml:space="preserve">
    <value>Le mot de passe ne correspond pas</value>
  </data>
  <data name="MsgRecomAddedSuccessfull" xml:space="preserve">
    <value>Pièce recommandée ajoutée avec succès</value>
  </data>
  <data name="MsgRecomDeleteSuccessfull" xml:space="preserve">
    <value>Pièce recommandée supprimée avec succès</value>
  </data>
  <data name="MsgRecomUpdateSuccessfull" xml:space="preserve">
    <value>Pièce recommandée mise à jour avec succès</value>
  </data>
  <data name="MsgRecordsDeleteSuccessfull" xml:space="preserve">
    <value>Supprimé avec succès</value>
  </data>
  <data name="MsgRecordsDeleteSuccessfull1" xml:space="preserve">
    <value>Supprimé avec succès</value>
  </data>
  <data name="MsgRecordsHaveAlreadybeenInserted" xml:space="preserve">
    <value>Des enregistrements ont déjà été ajoutés</value>
  </data>
  <data name="MsgRecordsHaveBeenDeleted" xml:space="preserve">
    <value>Supprimé avec succès</value>
  </data>
  <data name="MsgRecordsHaveBeenImoprted" xml:space="preserve">
    <value>Importé avec succès</value>
  </data>
  <data name="MsgRecordsHaveBeenImported" xml:space="preserve">
    <value>Importé avec succès</value>
  </data>
  <data name="MsgRecordsHaveBeenInserted" xml:space="preserve">
    <value>Ajouté avec succès</value>
  </data>
  <data name="MsgRecordsHaveBeenUpdated" xml:space="preserve">
    <value>Mis à jour avec succès</value>
  </data>
  <data name="MsgReplacecompletedsuccessfully" xml:space="preserve">
    <value>Remplacer terminé avec succès</value>
  </data>
  <data name="MsgResErrorinUploadedExcelFile" xml:space="preserve">
    <value>Erreur lors du téléchargement du fichier Excel</value>
  </data>
  <data name="MsgRoad#NotFound" xml:space="preserve">
    <value>Numéro de Route introuvable</value>
  </data>
  <data name="MsgRoleNameNotPresent" xml:space="preserve">
    <value>Nom de rôle introuvable</value>
  </data>
  <data name="MsgSameVersionOfAssemblyCannotcompare" xml:space="preserve">
    <value>Même version d'assemblages que nous ne pouvons pas comparer</value>
  </data>
  <data name="MsgSectionNotFound" xml:space="preserve">
    <value>Section introuvable</value>
  </data>
  <data name="MsgSelectAssemblytPublish" xml:space="preserve">
    <value>Veuillez sélectionner l'assemblage à libérer</value>
  </data>
  <data name="MsgSelectCustomer" xml:space="preserve">
    <value>Sélectionnez un client</value>
  </data>
  <data name="MsgSelectedParthavingalreadypartdetails" xml:space="preserve">
    <value>La pièce sélectionnée a déjà des détails sur la pièce</value>
  </data>
  <data name="MsgSelectedPartNoDoesnotcontainBompartdetails" xml:space="preserve">
    <value>Le numéro de pièce sélectionné ne contient pas les détails de la pièce de nomenclature</value>
  </data>
  <data name="MsgSelectOrderNumber" xml:space="preserve">
    <value>Sélectionnez le catalogue, le modèle, le type de produit, la marque</value>
  </data>
  <data name="MsgSelectPartNumber" xml:space="preserve">
    <value>Sélectionnez le numéro de pièce</value>
  </data>
  <data name="MsgSelectRoadNumber" xml:space="preserve">
    <value>Sélectionnez le numéro de route</value>
  </data>
  <data name="MsgSelectVINNumber" xml:space="preserve">
    <value>Sélectionnez  le NIV (Numéro Identification Véhicule)</value>
  </data>
  <data name="MsgSendnotificationUpdatedSuccessfull" xml:space="preserve">
    <value>mise à jour avec succès</value>
  </data>
  <data name="MsgSentSuccessfully" xml:space="preserve">
    <value>Courrier envoyé avec succès</value>
  </data>
  <data name="MsgSetAddedSuccessfull" xml:space="preserve">
    <value>Pièce définie ajoutée avec succès</value>
  </data>
  <data name="MsgSetDeleteSuccessfull" xml:space="preserve">
    <value>Pièce définie supprimée avec succès</value>
  </data>
  <data name="MsgSetUpdateSuccessfull" xml:space="preserve">
    <value>Pièce définie mise à jour avec succès</value>
  </data>
  <data name="MsgShiptoaddressselect" xml:space="preserve">
    <value>Sélectionnez l'adresse de livraison</value>
  </data>
  <data name="MsgSomethingWentWrongPleasetryAgain" xml:space="preserve">
    <value>Une erreur s'est produite. Veuillez réessayer</value>
  </data>
  <data name="MsgSubSectionNotFound" xml:space="preserve">
    <value>Sous-section introuvable</value>
  </data>
  <data name="MsgSuccess" xml:space="preserve">
    <value>Marque valide</value>
  </data>
  <data name="MsgSupersessiondatanotfound" xml:space="preserve">
    <value>Remplacement introuvable</value>
  </data>
  <data name="MsgThereisnopartdetailsareselectedtocopy" xml:space="preserve">
    <value>Veuillez sélectionner le numéro de pièce à copier</value>
  </data>
  <data name="MsgTrue" xml:space="preserve">
    <value>TRUE</value>
  </data>
  <data name="MsgVendorAddedSuccessfull" xml:space="preserve">
    <value>Informations sur la pièce du fournisseur ajoutées avec succès</value>
  </data>
  <data name="MsgVendorDeleteSuccessfull" xml:space="preserve">
    <value>Informations sur la pièce du fournisseur supprimées avec succès</value>
  </data>
  <data name="MsgVendorUpdatedSuccessfull" xml:space="preserve">
    <value>Informations sur la pièce du fournisseur mises à jour avec succès</value>
  </data>
  <data name="MsgVIN" xml:space="preserve">
    <value>Numéro d'Identification du Véhicule</value>
  </data>
  <data name="MsgVIN#NotFound" xml:space="preserve">
    <value>NIV (Numéro Identification Véhicule) non trouvé</value>
  </data>
  <data name="MultipleFileUploadMsg" xml:space="preserve">
    <value>Fichiers multiples sélectionnés, cliquez sur Oui pour créer une arborescence et générer des journaux</value>
  </data>
  <data name="MultipOptionCodes" xml:space="preserve">
    <value>Codes d'option multiples</value>
  </data>
  <data name="NameCustEn" xml:space="preserve">
    <value>Nom du client - Anglais</value>
  </data>
  <data name="NameCustFn" xml:space="preserve">
    <value>Nom du client - Français</value>
  </data>
  <data name="NewOldNoChange" xml:space="preserve">
    <value>Nouveau / Existant / Aucun changement - Commande</value>
  </data>
  <data name="NewOrderVarnetMsg" xml:space="preserve">
    <value>C'est une nouvelle commande - Continuez à générer un journal de travail et à créer une arborescence</value>
  </data>
  <data name="NewPasswordissenttoyouremail" xml:space="preserve">
    <value>Le nouveau mot de passe est envoyé à votre identifiant de messagerie enregistré</value>
  </data>
  <data name="NewPasswordWillbesenttoemaildoyouwanttocontinue" xml:space="preserve">
    <value>Le nouveau mot de passe sera envoyé à votre adresse de messagerie électronique (e-mail), voulez-vous continuer?</value>
  </data>
  <data name="NewVIN#" xml:space="preserve">
    <value>Nouveau NIV (Numéro Identification Véhicule)</value>
  </data>
  <data name="Next" xml:space="preserve">
    <value>Suivant</value>
  </data>
  <data name="Next Release information" xml:space="preserve">
    <value>Informations sur la prochaine version</value>
  </data>
  <data name="NextReleaseinformation" xml:space="preserve">
    <value>Informations sur la prochaine version</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>Non</value>
  </data>
  <data name="NoAssembliesFound" xml:space="preserve">
    <value>Aucun assemblage trouvé</value>
  </data>
  <data name="NoBrandisAssociated" xml:space="preserve">
    <value>Aucune marque n'est associée</value>
  </data>
  <data name="NoBrandisAssociated.PleaseSelectbrand" xml:space="preserve">
    <value>Aucune marque n'est associée. Veuillez sélectionner la marque</value>
  </data>
  <data name="NoCatalogueisAssociated" xml:space="preserve">
    <value>Aucun catalogue n'est associé</value>
  </data>
  <data name="NoChangeinCartNameNorDate" xml:space="preserve">
    <value>Aucun changement dans le nom ou la date du panier</value>
  </data>
  <data name="NoChangesFoundVarnetFileDB" xml:space="preserve">
    <value>Comparer le résultat - Aucune modification trouvée entre le fichier Varnet et la base de données</value>
  </data>
  <data name="NoChildAssembliesFound" xml:space="preserve">
    <value>Aucun assemblages pour enfants trouvé</value>
  </data>
  <data name="NoLogFound" xml:space="preserve">
    <value>Aucun journal trouvé</value>
  </data>
  <data name="NoParentAssembliesFound" xml:space="preserve">
    <value>Aucun assemblage parent trouvé</value>
  </data>
  <data name="NoProductTypeisAssociated" xml:space="preserve">
    <value>Aucun type de produit n'est associé</value>
  </data>
  <data name="NoRef" xml:space="preserve">
    <value>Pas de référence</value>
  </data>
  <data name="NoSectionisAssociatedtothisVIN" xml:space="preserve">
    <value>Aucune section n'est associée à ce NIV (Numéro Identification Véhicule)</value>
  </data>
  <data name="NotContains" xml:space="preserve">
    <value>Ne contient pas</value>
  </data>
  <data name="NotEmpty" xml:space="preserve">
    <value>Pas vide</value>
  </data>
  <data name="NotEqual" xml:space="preserve">
    <value>Pas égal</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="NotinHotspotCopyMode" xml:space="preserve">
    <value>Pas en mode de copie Hotspot</value>
  </data>
  <data name="NotinHotspotMoveMode" xml:space="preserve">
    <value>Pas en mode de déplacement Hotspot</value>
  </data>
  <data name="NotinTree" xml:space="preserve">
    <value>Pas dans la nomenclature</value>
  </data>
  <data name="NotNull" xml:space="preserve">
    <value>Pas nul</value>
  </data>
  <data name="NotOpenedby" xml:space="preserve">
    <value>Non ouvert par</value>
  </data>
  <data name="NotusedinanyAssembly" xml:space="preserve">
    <value>Non utilisé dans aucun assemblage</value>
  </data>
  <data name="NOVABUS" xml:space="preserve">
    <value>#NOVABUS</value>
  </data>
  <data name="NovaOrderList" xml:space="preserve">
    <value>Commande / Liste de premier niveau</value>
  </data>
  <data name="NoVehicleisAssociated" xml:space="preserve">
    <value>Aucun véhicule n'est associé</value>
  </data>
  <data name="NoVersionsCreated" xml:space="preserve">
    <value>Aucune version créée</value>
  </data>
  <data name="NUMERICALINDEX" xml:space="preserve">
    <value>INDICE NUMÉRIQUE</value>
  </data>
  <data name="OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="OnlyAlphabetsandCharactersareAllowedSpecialCharactersarenotAllowed" xml:space="preserve">
    <value>Seuls les alphabets et les caractères sont autorisés. Les caractères spéciaux ne sont pas autorisés</value>
  </data>
  <data name="Openedby" xml:space="preserve">
    <value>Ouvert par</value>
  </data>
  <data name="OpeningsLog" xml:space="preserve">
    <value>Journal des ouvertures du document</value>
  </data>
  <data name="OptionCode" xml:space="preserve">
    <value>Code d'option</value>
  </data>
  <data name="OptionsAdd" xml:space="preserve">
    <value>Options - Ajouter</value>
  </data>
  <data name="Option_NotFound" xml:space="preserve">
    <value>Code d'option non valide</value>
  </data>
  <data name="OrderBook" xml:space="preserve">
    <value>Carnet de commande</value>
  </data>
  <data name="OrderCompareList" xml:space="preserve">
    <value>Liste de comparaison des commandes</value>
  </data>
  <data name="OrderDeletedSucess" xml:space="preserve">
    <value>Commande supprimée avec succès</value>
  </data>
  <data name="OrderExistsandPublished" xml:space="preserve">
    <value>Le numéro de commande existe déjà dans la base de données et a été publié, affichez la liste de comparaison et créez une nouvelle version pour la commande?</value>
  </data>
  <data name="OrderExistsNotPublished" xml:space="preserve">
    <value>Le numéro de commande existe déjà dans la base de données et n'est pas publié, affichez la liste de comparaison et recréez l'arborescence à partir du fichier Varnet sélectionné?</value>
  </data>
  <data name="OrderImportinProgress" xml:space="preserve">
    <value>Commande sélectionnée - L'importation est déjà en cours de travail par nom d'utilisateur -</value>
  </data>
  <data name="OrderNo" xml:space="preserve">
    <value>Numéro de commande</value>
  </data>
  <data name="ordernoalreadypresent" xml:space="preserve">
    <value>Le numéro de commande donné est déjà disponible dans les détails du véhicule</value>
  </data>
  <data name="OrderNumber" xml:space="preserve">
    <value>Numéro de commande</value>
  </data>
  <data name="OrderNumberMismatchinFile" xml:space="preserve">
    <value>Non-correspondance du numéro de la commande - Contenu du fichier et nom du fichier</value>
  </data>
  <data name="OrderNumberVal" xml:space="preserve">
    <value>Numéro de commande</value>
  </data>
  <data name="OrderPublished" xml:space="preserve">
    <value>Commande publiée avec succès</value>
  </data>
  <data name="OrderQuantity" xml:space="preserve">
    <value>Quantité de commande</value>
  </data>
  <data name="OrderSearch" xml:space="preserve">
    <value>Recherche par numéro de commande</value>
  </data>
  <data name="PAGETITLE" xml:space="preserve">
    <value>TITRE DE LA PAGE</value>
  </data>
  <data name="Part" xml:space="preserve">
    <value>Pièces</value>
  </data>
  <data name="Part Assembly No" xml:space="preserve">
    <value>No Assemblage</value>
  </data>
  <data name="Part Description in English" xml:space="preserve">
    <value>Description de Piece Anglaise</value>
  </data>
  <data name="Part Description in French" xml:space="preserve">
    <value>Description de Piece Français</value>
  </data>
  <data name="Part Number" xml:space="preserve">
    <value>Numero de piece</value>
  </data>
  <data name="Part Or Assembly No" xml:space="preserve">
    <value>Numéro de pièce / d'assemblage</value>
  </data>
  <data name="Part Status" xml:space="preserve">
    <value>État de la pièce</value>
  </data>
  <data name="PartDesc" xml:space="preserve">
    <value>Description de la pièce</value>
  </data>
  <data name="PartDescription" xml:space="preserve">
    <value>Description de la pièce</value>
  </data>
  <data name="PartNumber" xml:space="preserve">
    <value>Numéro de la pièce</value>
  </data>
  <data name="PartRemovedFromCart" xml:space="preserve">
    <value>Pièce supprimée du panier</value>
  </data>
  <data name="PartsBookFor" xml:space="preserve">
    <value>Manuel de piece pour</value>
  </data>
  <data name="PartsBookPDFPrintinginProgress" xml:space="preserve">
    <value>Cliquez sur Oui pour démarrer l'impression PDF du livre de pièces</value>
  </data>
  <data name="PartsManual" xml:space="preserve">
    <value>MANUEL DE PIÈCES</value>
  </data>
  <data name="PartsManualFileName" xml:space="preserve">
    <value>Nom du fichier PDF du manuel de pièces</value>
  </data>
  <data name="Part_Assembly_AlternateNo" xml:space="preserve">
    <value>Autre numéro de pièce</value>
  </data>
  <data name="Part_Assembly_CreatedDate" xml:space="preserve">
    <value>Date de création</value>
  </data>
  <data name="Part_Assembly_Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Part_Assembly_HasVersion" xml:space="preserve">
    <value>A la version?</value>
  </data>
  <data name="Part_Assembly_IsActive" xml:space="preserve">
    <value>Est Actif?</value>
  </data>
  <data name="Part_Assembly_IsAssembly" xml:space="preserve">
    <value>Est l'Assemblée?</value>
  </data>
  <data name="Part_Assembly_IsLock" xml:space="preserve">
    <value>Est Fermé?</value>
  </data>
  <data name="Part_Assembly_IsPurchasable" xml:space="preserve">
    <value>Est Achetable?</value>
  </data>
  <data name="Part_Assembly_IsRecommendedPart" xml:space="preserve">
    <value>Est une Partie Recommandée?</value>
  </data>
  <data name="Part_Assembly_IsReferred" xml:space="preserve">
    <value>Est référé?</value>
  </data>
  <data name="Part_Assembly_IsSetMember" xml:space="preserve">
    <value>Est un Membre Défini?</value>
  </data>
  <data name="Part_Assembly_IsSuperseeded" xml:space="preserve">
    <value>Est remplacé?</value>
  </data>
  <data name="Part_Assembly_IsSuperseeding" xml:space="preserve">
    <value>Est le remplacement?</value>
  </data>
  <data name="Part_Assembly_IsSupersession" xml:space="preserve">
    <value>Est le remplacement?</value>
  </data>
  <data name="Part_Assembly_IsVisible" xml:space="preserve">
    <value>Est visible?</value>
  </data>
  <data name="Part_Assembly_ModifiedDate" xml:space="preserve">
    <value>Date modifiée</value>
  </data>
  <data name="Part_Assembly_No" xml:space="preserve">
    <value>Numéro de pièce / d'assemblage</value>
  </data>
  <data name="Part_Assembly_ShowVendorPartToCustomer" xml:space="preserve">
    <value>Afficher la partie fournisseur au client?</value>
  </data>
  <data name="Part_Assembly_Specification" xml:space="preserve">
    <value>Spécification</value>
  </data>
  <data name="Part_Is_Published" xml:space="preserve">
    <value>Est publié?</value>
  </data>
  <data name="PCYANDWIWIP" xml:space="preserve">
    <value>Cliquez sur Oui pour créer une arborescence et générer un journal de travail</value>
  </data>
  <data name="PDFPRINTLOG" xml:space="preserve">
    <value>Journal d'impression PDF</value>
  </data>
  <data name="PleaseChoosethebrand" xml:space="preserve">
    <value>Veuillez choisir la marque</value>
  </data>
  <data name="Pleasecloseallwindowsopenedwithpreviousbrand" xml:space="preserve">
    <value>Veuillez fermer toutes les fenêtres ouvertes</value>
  </data>
  <data name="pleaseenteremailid" xml:space="preserve">
    <value>Veuillez saisir votre adresse de messagerie électronique (e-mai)l</value>
  </data>
  <data name="PleaseEnterLoginID" xml:space="preserve">
    <value>Veuillez saisir votre Identifiant de connexion</value>
  </data>
  <data name="Pleaseentervalidemailid" xml:space="preserve">
    <value>Veuillez saisir une adresse e-mail valide</value>
  </data>
  <data name="Pleasefillallfields" xml:space="preserve">
    <value>Veuillez remplir toutes les informations</value>
  </data>
  <data name="Pleaseselectcheckbox" xml:space="preserve">
    <value>Veuillez cocher la case</value>
  </data>
  <data name="PleaseSelectCustomerName" xml:space="preserve">
    <value>Veuillez sélectionner le nom du client</value>
  </data>
  <data name="PleaseselectEffectiveFromdateFirst" xml:space="preserve">
    <value>Veuillez sélectionner la date d'entrée en vigueur en premier</value>
  </data>
  <data name="Pleaseselectfiletype" xml:space="preserve">
    <value>Veuillez sélectionner le type de fichier</value>
  </data>
  <data name="PleaseSelectFonttoRunOCR" xml:space="preserve">
    <value>Veuillez sélectionner la police pour exécuter la reconnaissance optique de caractères (ROC)</value>
  </data>
  <data name="PleaseselectMaskingCodefirst" xml:space="preserve">
    <value>Veuillez d'abord sélectionner le code de masquage</value>
  </data>
  <data name="PNI" xml:space="preserve">
    <value>Index numérique?</value>
  </data>
  <data name="Previous" xml:space="preserve">
    <value>Précédent</value>
  </data>
  <data name="PrintCompleted" xml:space="preserve">
    <value>Impression PDF terminée</value>
  </data>
  <data name="PrintedBy" xml:space="preserve">
    <value>Imprimé par</value>
  </data>
  <data name="PrintedDate" xml:space="preserve">
    <value>Date d'impression</value>
  </data>
  <data name="PrintedStatus" xml:space="preserve">
    <value>État de l'impression</value>
  </data>
  <data name="PrintPartsBook" xml:space="preserve">
    <value>Imprimer le livre de pièces</value>
  </data>
  <data name="ProductTypeCodeduplicatedforSameBrand" xml:space="preserve">
    <value>Dupliquer Type de produit </value>
  </data>
  <data name="PWTBFMSSSMAWIP" xml:space="preserve">
    <value>Veuillez cliquer sur Oui pour confirmer et patienter - Construction de l'arborescence pour la section manquante, la sous-section et l'assemblage principal en cours</value>
  </data>
  <data name="QTY" xml:space="preserve">
    <value>Quantité</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Quantité</value>
  </data>
  <data name="RecordshavebeenIn-activated" xml:space="preserve">
    <value>Les enregistrements ont été activés</value>
  </data>
  <data name="ReEPC" xml:space="preserve">
    <value>Catalogue de pièces électroniques</value>
  </data>
  <data name="ReleaseDate" xml:space="preserve">
    <value>Date de sortie</value>
  </data>
  <data name="ReleaseNoteFileName" xml:space="preserve">
    <value>Nom du fichier de note de publication</value>
  </data>
  <data name="ReleaseNoteInformation" xml:space="preserve">
    <value>Informations sur la note de publication</value>
  </data>
  <data name="ReleaseVerion" xml:space="preserve">
    <value>Version finale</value>
  </data>
  <data name="ReleaseVersion" xml:space="preserve">
    <value>Version finale</value>
  </data>
  <data name="relogin" xml:space="preserve">
    <value>Se reconnecter</value>
  </data>
  <data name="Remarks" xml:space="preserve">
    <value>Remarques</value>
  </data>
  <data name="RemoveAllCustomers" xml:space="preserve">
    <value>Supprimer tous les clients</value>
  </data>
  <data name="RemovedFromCartSuccessfully" xml:space="preserve">
    <value>Retiré du panier avec succès</value>
  </data>
  <data name="ReplacedBy" xml:space="preserve">
    <value>Remplacer par</value>
  </data>
  <data name="Res*Remarks" xml:space="preserve">
    <value>Entrez des remarques</value>
  </data>
  <data name="ResAbbrevationList" xml:space="preserve">
    <value>Liste d'abréviations</value>
  </data>
  <data name="ResAbbrevationList1" xml:space="preserve">
    <value>Liste d'abréviations</value>
  </data>
  <data name="ResAbbreviation" xml:space="preserve">
    <value>Abréviation</value>
  </data>
  <data name="ResAbbreviation1" xml:space="preserve">
    <value>Abréviation</value>
  </data>
  <data name="ResAbbreviationDescription" xml:space="preserve">
    <value>Description Abréviation</value>
  </data>
  <data name="ResAbbreviationDescription1" xml:space="preserve">
    <value>Description Abréviation</value>
  </data>
  <data name="ResAbbreviationDescriptionSearchList" xml:space="preserve">
    <value>Description Abréviation Liste de recherche</value>
  </data>
  <data name="ResAbbreviationDescriptionSearchList1" xml:space="preserve">
    <value>Description Abréviation Liste de recherche</value>
  </data>
  <data name="ResAbbreviationDescriptionUpdation" xml:space="preserve">
    <value>Description Abréviation Mise à jour</value>
  </data>
  <data name="ResAbbreviationDescriptionUpdation1" xml:space="preserve">
    <value>Description Abréviation Mise à jour</value>
  </data>
  <data name="ResAbbreviationDesUpdation" xml:space="preserve">
    <value>Description Abréviation Mise à jour</value>
  </data>
  <data name="ResAbbreviationDesUpdation1" xml:space="preserve">
    <value>Description Abréviation Mise à jour</value>
  </data>
  <data name="ResAbbreviationDetails" xml:space="preserve">
    <value>Détails de l'abréviation</value>
  </data>
  <data name="ResAbbreviationFrenchDescription" xml:space="preserve">
    <value>Description de Abréviation Français</value>
  </data>
  <data name="ResAbbreviationLanguageList" xml:space="preserve">
    <value>Liste des langues d'abréviation</value>
  </data>
  <data name="ResAbbreviationP" xml:space="preserve">
    <value>Abréviations</value>
  </data>
  <data name="ResAbbreviations" xml:space="preserve">
    <value>Afficher les abréviations</value>
  </data>
  <data name="ResAbbreviations1" xml:space="preserve">
    <value>Abréviations</value>
  </data>
  <data name="ResAccessRights" xml:space="preserve">
    <value>Droits d'accès</value>
  </data>
  <data name="ResAction" xml:space="preserve">
    <value>Action</value>
  </data>
  <data name="ResActionBy" xml:space="preserve">
    <value>Action par</value>
  </data>
  <data name="ResActionBy1" xml:space="preserve">
    <value>Action par</value>
  </data>
  <data name="ResActionName" xml:space="preserve">
    <value>Nom de l'action</value>
  </data>
  <data name="ResActionType" xml:space="preserve">
    <value>Type d'action</value>
  </data>
  <data name="ResActionType1" xml:space="preserve">
    <value>Type d'action</value>
  </data>
  <data name="ResActivityName" xml:space="preserve">
    <value>Nom de l'activité</value>
  </data>
  <data name="ResActivityName1" xml:space="preserve">
    <value>Nom de l'activité</value>
  </data>
  <data name="ResAdd" xml:space="preserve">
    <value>Ajouter</value>
  </data>
  <data name="ResAdd1" xml:space="preserve">
    <value>Ajouter</value>
  </data>
  <data name="ResAddAbbrevation" xml:space="preserve">
    <value>Ajouter une Abréviation</value>
  </data>
  <data name="ResAddAbbreviationDescription" xml:space="preserve">
    <value>Ajouter une description d'Abréviation</value>
  </data>
  <data name="ResAddAnnotation" xml:space="preserve">
    <value>Ajouter une annotation</value>
  </data>
  <data name="ResAddBlankLine" xml:space="preserve">
    <value>Ajouter une ligne vierge</value>
  </data>
  <data name="ResAddBOMPart" xml:space="preserve">
    <value>Ajouter la Pièce de Nomenclature</value>
  </data>
  <data name="ResAddBookmark" xml:space="preserve">
    <value>Ajouter un Signet</value>
  </data>
  <data name="ResAddCondition" xml:space="preserve">
    <value>Ajouter une Condition / Ajouter État ?</value>
  </data>
  <data name="ResAddCondition1" xml:space="preserve">
    <value>Ajouter une Condition / Ajouter État ?</value>
  </data>
  <data name="ResAddCustomer" xml:space="preserve">
    <value>Ajouter un client</value>
  </data>
  <data name="ResAddCustomer1" xml:space="preserve">
    <value>Ajouter un client</value>
  </data>
  <data name="ResAddCustomerPartInfo" xml:space="preserve">
    <value>Ajouter des informations sur la pièce client</value>
  </data>
  <data name="ResAddCustomerPartInfo1" xml:space="preserve">
    <value>Ajouter des informations sur la pièce client</value>
  </data>
  <data name="ResAddDictionary" xml:space="preserve">
    <value>Ajouter un dictionnaire</value>
  </data>
  <data name="ResAddedSelectedPartandSetMemberSuccessfully" xml:space="preserve">
    <value>La pièce sélectionnée et le ou les membres définis ont bien été ajoutés</value>
  </data>
  <data name="ResAddedSuccessfully" xml:space="preserve">
    <value>Ajouté avec succès</value>
  </data>
  <data name="ResAddFilter" xml:space="preserve">
    <value>Ajouter un filtre</value>
  </data>
  <data name="ResAddFont" xml:space="preserve">
    <value>Ajouter une police de caractère</value>
  </data>
  <data name="ResAddFunctionGroup" xml:space="preserve">
    <value>Ajouter un groupe de fonctions</value>
  </data>
  <data name="ResAddFunctionGroup1" xml:space="preserve">
    <value>Ajouter un groupe de fonctions</value>
  </data>
  <data name="ResAddFunctionGroupUsed" xml:space="preserve">
    <value>Ajouter un groupe de fonctions utilisées</value>
  </data>
  <data name="ResAddGUIConfig" xml:space="preserve">
    <value>Ajouter une configuration GUI</value>
  </data>
  <data name="ResAddHighlighter" xml:space="preserve">
    <value>Ajouter un surligneur</value>
  </data>
  <data name="ResAddHiglighter" xml:space="preserve">
    <value>Ajouter un surligneur</value>
  </data>
  <data name="ResAddHotSpot" xml:space="preserve">
    <value>Ajouter un hotspot </value>
  </data>
  <data name="ResAddImage" xml:space="preserve">
    <value>Ajouter une image</value>
  </data>
  <data name="ResAddMaskingAction" xml:space="preserve">
    <value>Ajouter une action de masquage</value>
  </data>
  <data name="ResAddNewCustomerPart" xml:space="preserve">
    <value>Ajouter une nouvelle pièce client</value>
  </data>
  <data name="ResAddNewMFGCode" xml:space="preserve">
    <value>Code de fabrication - Ajouter</value>
  </data>
  <data name="ResAddNewMFGCode1" xml:space="preserve">
    <value>Ajouter un code de fabrication</value>
  </data>
  <data name="ResAddNewModel" xml:space="preserve">
    <value>Ajouter un nouveau modèle</value>
  </data>
  <data name="ResAddNewModel1" xml:space="preserve">
    <value>Ajouter un nouveau modèle</value>
  </data>
  <data name="ResAddNewOwnership" xml:space="preserve">
    <value>Ajouter la propriété d'un nouveau véhicule</value>
  </data>
  <data name="ResAddNewVINNumber" xml:space="preserve">
    <value>Ajouter un nouveau NIV (Numéro Identification Véhicule)</value>
  </data>
  <data name="ResAddOnlyDescription" xml:space="preserve">
    <value>Ajouter seulement la description</value>
  </data>
  <data name="ResAddOptions" xml:space="preserve">
    <value>Ajouter des options</value>
  </data>
  <data name="ResAddPartDetails" xml:space="preserve">
    <value>Ajouter les détails de la pièce</value>
  </data>
  <data name="ResAddParttoBOM" xml:space="preserve">
    <value>Ajouter une pièce à la nomenclature</value>
  </data>
  <data name="ResAddParttoBOM1" xml:space="preserve">
    <value>Ajouter une pièce à la nomenclature</value>
  </data>
  <data name="ResAddRecommendedParts" xml:space="preserve">
    <value>Ajouter les pièces recommandées</value>
  </data>
  <data name="ResAddress1" xml:space="preserve">
    <value>Addresse 1</value>
  </data>
  <data name="ResAddress2" xml:space="preserve">
    <value>Addresse 2</value>
  </data>
  <data name="ResAddRoadHistory" xml:space="preserve">
    <value>Ajouter un nouveau numéro de route</value>
  </data>
  <data name="ResAddSectionItem" xml:space="preserve">
    <value>Ajouter un élément de section</value>
  </data>
  <data name="ResAddSectionItem1" xml:space="preserve">
    <value>Ajouter un élément de section</value>
  </data>
  <data name="ResAddSetMember" xml:space="preserve">
    <value>Ajouter un membre défini</value>
  </data>
  <data name="ResAddSupersedingDetailer" xml:space="preserve">
    <value>Ajouter un Détaillant de remplacement</value>
  </data>
  <data name="ResAddSupersession" xml:space="preserve">
    <value>Ajouter un remplacement</value>
  </data>
  <data name="ResAddSupersession1" xml:space="preserve">
    <value>Ajouter un remplacement</value>
  </data>
  <data name="ResAddSupersessionDetailer" xml:space="preserve">
    <value>Ajouter un Détaillant de remplacement</value>
  </data>
  <data name="ResAddText" xml:space="preserve">
    <value>Ajouter du texte</value>
  </data>
  <data name="ResAddToCart" xml:space="preserve">
    <value>Ajouter au panier</value>
  </data>
  <data name="ResAddToCart1" xml:space="preserve">
    <value>Ajouter au panier</value>
  </data>
  <data name="ResAddToShoppingCart" xml:space="preserve">
    <value>Ajouter au panier</value>
  </data>
  <data name="ResAddUser" xml:space="preserve">
    <value>Ajouter un utilisateur</value>
  </data>
  <data name="ResAddVendor" xml:space="preserve">
    <value>Ajouter un fournisseur</value>
  </data>
  <data name="ResAddVendor1" xml:space="preserve">
    <value>Ajouter un fournisseur</value>
  </data>
  <data name="ResAddVendorInfo" xml:space="preserve">
    <value>Ajouter des informations sur le fournisseur</value>
  </data>
  <data name="ResAddVendorName" xml:space="preserve">
    <value>Ajouter le nom du fournisseur</value>
  </data>
  <data name="ResAddVendorPartInfo" xml:space="preserve">
    <value>Ajouter des informations sur la pièce du fournisseur</value>
  </data>
  <data name="ResAdmin" xml:space="preserve">
    <value>Administratrice</value>
  </data>
  <data name="ResAdvanceSearch" xml:space="preserve">
    <value>Recherche Avancée</value>
  </data>
  <data name="ResAdvanceSearch1" xml:space="preserve">
    <value>Recherche Avancée</value>
  </data>
  <data name="ResAdvanceSearchCreateModifyQuery" xml:space="preserve">
    <value>Recherche avancée - Créer / modifier une requête</value>
  </data>
  <data name="ResAdvanceSearchCreateModifyQuery1" xml:space="preserve">
    <value>Recherche avancée - Créer / modifier une requête</value>
  </data>
  <data name="ResAdvanceSearchCreateQuery" xml:space="preserve">
    <value>Recherche avancée - Créer une requête</value>
  </data>
  <data name="ResAdvanceSearchCreateQuery1" xml:space="preserve">
    <value>Recherche avancée - Créer une requête</value>
  </data>
  <data name="ResAdvanceSearchModifyQuery" xml:space="preserve">
    <value>Recherche avancée - Modifier la requête</value>
  </data>
  <data name="ResAdvanceSearchModifyQuery1" xml:space="preserve">
    <value>Recherche avancée - Modifier la requête</value>
  </data>
  <data name="ResAdvanceSearchResult" xml:space="preserve">
    <value>Résultat de la recherche avancée</value>
  </data>
  <data name="ResAdvanceSearchResult1" xml:space="preserve">
    <value>Résultat de la recherche avancée</value>
  </data>
  <data name="ResAgeing" xml:space="preserve">
    <value>Vieillissement</value>
  </data>
  <data name="ResAgeing1" xml:space="preserve">
    <value>Vieillissement</value>
  </data>
  <data name="ResAgeingindays" xml:space="preserve">
    <value>Vieillissement en jours</value>
  </data>
  <data name="ResAlign" xml:space="preserve">
    <value>Aligner</value>
  </data>
  <data name="ResAll" xml:space="preserve">
    <value>Tout</value>
  </data>
  <data name="ResAll1" xml:space="preserve">
    <value>Tout</value>
  </data>
  <data name="ResAllCustomers" xml:space="preserve">
    <value>Tous les clients</value>
  </data>
  <data name="ResAllDocuments" xml:space="preserve">
    <value>Tous les documents</value>
  </data>
  <data name="ResAllDocumnets" xml:space="preserve">
    <value>Tous les documents</value>
  </data>
  <data name="ResAllocated" xml:space="preserve">
    <value>Alloué</value>
  </data>
  <data name="ResAllocated1" xml:space="preserve">
    <value>Alloué</value>
  </data>
  <data name="ResAllocatedTo" xml:space="preserve">
    <value>Attribué à</value>
  </data>
  <data name="ResAllOrders" xml:space="preserve">
    <value>Toutes les commandes</value>
  </data>
  <data name="ResAlternateDescription" xml:space="preserve">
    <value>Description alternative</value>
  </data>
  <data name="ResAlternateDescription1" xml:space="preserve">
    <value>Description alternative</value>
  </data>
  <data name="ResAlternatePart#" xml:space="preserve">
    <value>Autre numéro de pièce</value>
  </data>
  <data name="ResAlternatePart#1" xml:space="preserve">
    <value>Autre numéro de pièce</value>
  </data>
  <data name="ResAND" xml:space="preserve">
    <value>ET</value>
  </data>
  <data name="ResAND1" xml:space="preserve">
    <value>ET</value>
  </data>
  <data name="ResAnewdocument" xml:space="preserve">
    <value>Un nouveau document -</value>
  </data>
  <data name="ResAnnotation" xml:space="preserve">
    <value>Ajouter des annotations</value>
  </data>
  <data name="ResAnnotation1" xml:space="preserve">
    <value>Annotation</value>
  </data>
  <data name="ResAnnotationDetails" xml:space="preserve">
    <value>Détails de l'annotation</value>
  </data>
  <data name="ResAnnotationDetails1" xml:space="preserve">
    <value>Détails de l'annotation</value>
  </data>
  <data name="ResAnnotationName" xml:space="preserve">
    <value>Nom de l'annotation</value>
  </data>
  <data name="ResAnyQueries" xml:space="preserve">
    <value>Pour toute question ou clarification, écrivez à </value>
  </data>
  <data name="ResApplicationWillBeDownOn" xml:space="preserve">
    <value>L'application sera </value>
  </data>
  <data name="ResAreYouSureYouWantToClose" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir fermer ?</value>
  </data>
  <data name="ResAreyousureyouwanttoDelete" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir supprimer?</value>
  </data>
  <data name="ResAreyousureyouwanttoDelete?" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir supprimer?</value>
  </data>
  <data name="ResAssembliesAlphaIndex" xml:space="preserve">
    <value>INDEX ALPHABÉTIQUE DES ASSEMBLAGES</value>
  </data>
  <data name="ResAssembly" xml:space="preserve">
    <value>Assemblage</value>
  </data>
  <data name="ResAssembly#" xml:space="preserve">
    <value>Numéro d'Assemblage</value>
  </data>
  <data name="ResAssembly#1" xml:space="preserve">
    <value>Numéro d'Assemblage</value>
  </data>
  <data name="ResAssembly&amp;Parts" xml:space="preserve">
    <value>Assemblage et pièces</value>
  </data>
  <data name="ResAssembly&amp;PartsCustomerDocumentLink" xml:space="preserve">
    <value>Lien vers le document client Assemblage et pièces</value>
  </data>
  <data name="ResAssembly&amp;PartsCustomerView" xml:space="preserve">
    <value>Vue client de l'assemblage et des pièces</value>
  </data>
  <data name="ResAssembly&amp;PartsViewer" xml:space="preserve">
    <value>Visionneuse d'assemblage et de pièces</value>
  </data>
  <data name="ResAssembly1" xml:space="preserve">
    <value>Assemblage</value>
  </data>
  <data name="ResAssemblyAssociation" xml:space="preserve">
    <value>Association d'Assemblage</value>
  </data>
  <data name="ResAssemblyAssociation1" xml:space="preserve">
    <value>Association d'Assemblage</value>
  </data>
  <data name="ResAssemblyAttachments" xml:space="preserve">
    <value>Pièces jointes d'Assemblage</value>
  </data>
  <data name="ResAssemblyAttachments1" xml:space="preserve">
    <value>Pièces jointes d'Assemblage</value>
  </data>
  <data name="ResAssemblyBOMPartDetails" xml:space="preserve">
    <value>Détails des pièces de la nomenclature</value>
  </data>
  <data name="ResAssemblyCount" xml:space="preserve">
    <value>Nombre d'assemblages</value>
  </data>
  <data name="ResAssemblyCreatedDate" xml:space="preserve">
    <value>Date de création de l'assemblage</value>
  </data>
  <data name="ResAssemblyCustomerLink" xml:space="preserve">
    <value>Document client d'assemblage</value>
  </data>
  <data name="ResAssemblyCustomerView" xml:space="preserve">
    <value>Vue client de l'assemblage</value>
  </data>
  <data name="ResAssemblyCustomerView1" xml:space="preserve">
    <value>Vue client de l'assemblage</value>
  </data>
  <data name="ResAssemblyDescription" xml:space="preserve">
    <value>Description de l'assemblage</value>
  </data>
  <data name="ResAssemblyDescription1" xml:space="preserve">
    <value>Description de l'assemblage</value>
  </data>
  <data name="ResAssemblyDocuments" xml:space="preserve">
    <value>Documents d'assemblage</value>
  </data>
  <data name="ResAssemblyDocuments1" xml:space="preserve">
    <value>Documents d'assemblage</value>
  </data>
  <data name="ResAssemblyImages" xml:space="preserve">
    <value>Images d'assemblage</value>
  </data>
  <data name="ResAssemblyImages1" xml:space="preserve">
    <value>Images d'assemblage</value>
  </data>
  <data name="ResAssemblyImageUpload" xml:space="preserve">
    <value>Téléchargement d'image d'assemblage</value>
  </data>
  <data name="ResAssemblyImageUpload1" xml:space="preserve">
    <value>Téléchargement d'image d'assemblage</value>
  </data>
  <data name="ResAssemblyImport" xml:space="preserve">
    <value>Importation d'assemblage</value>
  </data>
  <data name="ResAssemblyImport1" xml:space="preserve">
    <value>Importation d'assemblage</value>
  </data>
  <data name="ResAssemblyList" xml:space="preserve">
    <value>Liste d'assemblage</value>
  </data>
  <data name="ResAssemblyList1" xml:space="preserve">
    <value>Liste d'assemblage</value>
  </data>
  <data name="ResAssemblyMaster" xml:space="preserve">
    <value>Maître d'assemblage</value>
  </data>
  <data name="ResAssemblyMaster1" xml:space="preserve">
    <value>Maître d'assemblage</value>
  </data>
  <data name="ResAssemblyModifiedDate" xml:space="preserve">
    <value>Date de modification de l'assemblage</value>
  </data>
  <data name="ResAssemblyName" xml:space="preserve">
    <value>Nom de l'assemblage</value>
  </data>
  <data name="ResAssemblyName1" xml:space="preserve">
    <value>Nom de l'assemblage</value>
  </data>
  <data name="ResAssemblyNo" xml:space="preserve">
    <value>Numéro d'Assemblage</value>
  </data>
  <data name="ResAssemblyPart" xml:space="preserve">
    <value>Assemblage</value>
  </data>
  <data name="ResAssemblyPartConfigurations" xml:space="preserve">
    <value>Configurations de pièces</value>
  </data>
  <data name="ResAssemblyPartDetails" xml:space="preserve">
    <value>Détails des pièces d'assemblage</value>
  </data>
  <data name="ResAssemblyPartDetaResAssemblyPartDeta" xml:space="preserve">
    <value />
  </data>
  <data name="ResAssemblyPartsServiceDocumentLink" xml:space="preserve">
    <value>Lien vers le document de service d'assemblage et de pièces</value>
  </data>
  <data name="ResAssemblyServiceDocumentLink" xml:space="preserve">
    <value>Lien vers le document de service d'assemblage</value>
  </data>
  <data name="ResAssemblyServiceDocumentLink1" xml:space="preserve">
    <value>Lien vers le document de service d'assemblage</value>
  </data>
  <data name="ResAssemblyServiceDocumentLink2" xml:space="preserve">
    <value>Lien vers le document de service d'assemblage</value>
  </data>
  <data name="ResAssemblyServiceLink" xml:space="preserve">
    <value>Lien du service d'assemblage</value>
  </data>
  <data name="ResAssemblyToolbar Confi." xml:space="preserve">
    <value>Configuration de la barre d'outils d'assemblage</value>
  </data>
  <data name="ResAssemblyToolbar Confi.1" xml:space="preserve">
    <value>Configuration de la barre d'outils d'assemblage</value>
  </data>
  <data name="ResAssemblyToolbarConfig" xml:space="preserve">
    <value>Configuration de la barre d'outils d'assemblage</value>
  </data>
  <data name="ResAssemblyToolbarConfig1" xml:space="preserve">
    <value>Configuration de la barre d'outils d'assemblage</value>
  </data>
  <data name="ResAssemblyToolBarConfiguration" xml:space="preserve">
    <value>Configuration de la barre d'outils d'assemblage</value>
  </data>
  <data name="ResAssemblyToolBarConfiguration1" xml:space="preserve">
    <value>Configuration de la barre d'outils d'assemblage</value>
  </data>
  <data name="ResAssemblyViewConfig" xml:space="preserve">
    <value>Configuration de la vue d'assemblage</value>
  </data>
  <data name="ResAssemblyViewConfig1" xml:space="preserve">
    <value>Configuration de la vue d'assemblage</value>
  </data>
  <data name="ResAssemblyViewConfiguration-List" xml:space="preserve">
    <value>Liste de configuration de la vue d'assemblage</value>
  </data>
  <data name="ResAssemblyViewConfiguration-List1" xml:space="preserve">
    <value>Liste de configuration de la vue d'assemblage</value>
  </data>
  <data name="ResAssemblyViewConfigure-Edit" xml:space="preserve">
    <value>Configurer-Modifier la vue d'assemblage</value>
  </data>
  <data name="ResAssemblyViewConfigure-Edit1" xml:space="preserve">
    <value>Configurer-Modifier la vue d'assemblage</value>
  </data>
  <data name="ResAssemblyViewDetails" xml:space="preserve">
    <value>Détails Visualiseur d'assemblage</value>
  </data>
  <data name="ResAssemblyViewer" xml:space="preserve">
    <value>Visualiseur d'assemblage</value>
  </data>
  <data name="ResAssemblyViewer1" xml:space="preserve">
    <value>Visualiseur d'assemblage</value>
  </data>
  <data name="ResAsslyDescriptionXML" xml:space="preserve">
    <value>Description_de_l_assemblage</value>
  </data>
  <data name="ResAssociateAssembly" xml:space="preserve">
    <value>Associer l'assemblage</value>
  </data>
  <data name="ResAssociateAssembly-List" xml:space="preserve">
    <value>Associer la liste d'assemblage</value>
  </data>
  <data name="ResAssociateAssembly-List1" xml:space="preserve">
    <value>Associer la liste d'assemblage</value>
  </data>
  <data name="ResAssociateAssembly1" xml:space="preserve">
    <value>Associer l'assemblage</value>
  </data>
  <data name="ResAssociateCatalogue" xml:space="preserve">
    <value>Associer Catalogue </value>
  </data>
  <data name="ResAssociateCatalogue1" xml:space="preserve">
    <value>Associer Catalogue </value>
  </data>
  <data name="ResAssociateCatalogueUpdation" xml:space="preserve">
    <value>Associer la mise à jour du catalogue</value>
  </data>
  <data name="ResAssociateCatalogueUpdation1" xml:space="preserve">
    <value>Associer la mise à jour du catalogue</value>
  </data>
  <data name="ResAssociatedAssemblies" xml:space="preserve">
    <value>Assemblages associés</value>
  </data>
  <data name="ResAssociatedOrderDetails" xml:space="preserve">
    <value>FN-Associated Order Details</value>
  </data>
  <data name="ResAssociatedOrders" xml:space="preserve">
    <value>FN-Associated Orders</value>
  </data>
  <data name="ResAssociateDrawings" xml:space="preserve">
    <value>Associer des dessins</value>
  </data>
  <data name="ResAssociateDrawings1" xml:space="preserve">
    <value>Associer des dessins</value>
  </data>
  <data name="ResAssociateDrawingtocatalogue" xml:space="preserve">
    <value>Associer le dessin au catalogue</value>
  </data>
  <data name="ResAssociateDrawingtocatalogue1" xml:space="preserve">
    <value>Associer le dessin au catalogue</value>
  </data>
  <data name="ResAssociatedSections" xml:space="preserve">
    <value>Sections associée</value>
  </data>
  <data name="ResAssociatedSections1" xml:space="preserve">
    <value>Sections associée</value>
  </data>
  <data name="ResAssociateOptions" xml:space="preserve">
    <value>Associer  le Options</value>
  </data>
  <data name="ResAssociateOptions1" xml:space="preserve">
    <value>Associer  le Options</value>
  </data>
  <data name="ResAssociateParts" xml:space="preserve">
    <value>Associer des pièces</value>
  </data>
  <data name="ResAssociateParts1" xml:space="preserve">
    <value>Associer des pièces</value>
  </data>
  <data name="ResAssociateSections" xml:space="preserve">
    <value>Associer des sections</value>
  </data>
  <data name="ResAssociateSections1" xml:space="preserve">
    <value>Associer des sections</value>
  </data>
  <data name="ResAssociateServiceSection" xml:space="preserve">
    <value>Associer Section des services</value>
  </data>
  <data name="ResAssociateServiceSection1" xml:space="preserve">
    <value>Associer Section des services</value>
  </data>
  <data name="ResAssociationofnovaorderswillberemoveddoyouwanttocontinue" xml:space="preserve">
    <value>FN-Association of NOVA Orders will be removed do you want to continue?</value>
  </data>
  <data name="ResAssyAssociation" xml:space="preserve">
    <value>Associer assemblage</value>
  </data>
  <data name="ResAssyAssociation1" xml:space="preserve">
    <value>Associer assemblage</value>
  </data>
  <data name="ResAttach" xml:space="preserve">
    <value>Documents joints</value>
  </data>
  <data name="ResAttach1" xml:space="preserve">
    <value>Documents joints</value>
  </data>
  <data name="ResAttachDownload" xml:space="preserve">
    <value>Joindre / Télécharger</value>
  </data>
  <data name="ResAttachment" xml:space="preserve">
    <value>Documents joints</value>
  </data>
  <data name="ResAttachment-Add" xml:space="preserve">
    <value>Ajout de document joint</value>
  </data>
  <data name="ResAttachment-Add1" xml:space="preserve">
    <value>Ajout de document joint</value>
  </data>
  <data name="ResAttachment-Edit" xml:space="preserve">
    <value>Document joint - Modifier</value>
  </data>
  <data name="ResAttachment-Edit1" xml:space="preserve">
    <value>Document joint - Modifier</value>
  </data>
  <data name="ResAttachment1" xml:space="preserve">
    <value>Documents joints</value>
  </data>
  <data name="ResAttachmentCategory" xml:space="preserve">
    <value>Catégorie de document joint</value>
  </data>
  <data name="ResAttachmentList" xml:space="preserve">
    <value>Document joint - Liste</value>
  </data>
  <data name="ResAttachments" xml:space="preserve">
    <value>Documents joints</value>
  </data>
  <data name="ResAttachments1" xml:space="preserve">
    <value>Documents joints</value>
  </data>
  <data name="ResAttachmentType" xml:space="preserve">
    <value>Type de Documents joints </value>
  </data>
  <data name="ResAttachmentType1" xml:space="preserve">
    <value>Type de Documents joints </value>
  </data>
  <data name="ResAttachmentUpload" xml:space="preserve">
    <value>Téléchargement de Documents joints </value>
  </data>
  <data name="ResAttachmentUpload1" xml:space="preserve">
    <value>Téléchargement de Documents joints </value>
  </data>
  <data name="ResAuthor" xml:space="preserve">
    <value>Auteur</value>
  </data>
  <data name="ResAuthorAttachments" xml:space="preserve">
    <value> Documents joints  de l'auteur</value>
  </data>
  <data name="ResAuthorAttachments1" xml:space="preserve">
    <value> Documents joints  de l'auteur</value>
  </data>
  <data name="ResAuthorErrorReportAttachments" xml:space="preserve">
    <value>Documents joints du rapport d'erreur de l'auteur</value>
  </data>
  <data name="ResAuthoringActivity" xml:space="preserve">
    <value>Activité de création</value>
  </data>
  <data name="ResAuthoringActivity1" xml:space="preserve">
    <value>Activité de création</value>
  </data>
  <data name="ResAuthoringActivityDefinition" xml:space="preserve">
    <value>Définition de l'activité de création</value>
  </data>
  <data name="ResAuthoringActivityDefinition1" xml:space="preserve">
    <value>Définition de l'activité de création</value>
  </data>
  <data name="ResAuthoringActivityList" xml:space="preserve">
    <value>Création d'une liste d'activités</value>
  </data>
  <data name="ResAuthoringActivityList1" xml:space="preserve">
    <value>Création d'une liste d'activités</value>
  </data>
  <data name="ResAuthoringErrorLog" xml:space="preserve">
    <value>Journal des erreurs de création</value>
  </data>
  <data name="ResAuthoringErrorLog1" xml:space="preserve">
    <value>Journal des erreurs de création</value>
  </data>
  <data name="ResAuthoringErrorLogReport" xml:space="preserve">
    <value>Rapport du journal des erreurs de création</value>
  </data>
  <data name="ResAuthoringLog" xml:space="preserve">
    <value>Journal de création</value>
  </data>
  <data name="ResAuthoringLog-Edit" xml:space="preserve">
    <value>Journal de création - modification</value>
  </data>
  <data name="ResAuthoringLog-Edit1" xml:space="preserve">
    <value>Journal de création - modification</value>
  </data>
  <data name="ResAuthoringLog1" xml:space="preserve">
    <value>Journal de création</value>
  </data>
  <data name="ResAuthoringLogDetails" xml:space="preserve">
    <value>Détails du journal de création</value>
  </data>
  <data name="ResAuthoringLogList" xml:space="preserve">
    <value>Liste des journaux de création</value>
  </data>
  <data name="ResAuthoringLogReport" xml:space="preserve">
    <value>Rapport de journal de création</value>
  </data>
  <data name="ResAuthoringLogUpdate" xml:space="preserve">
    <value>Mise à jour du journal de création</value>
  </data>
  <data name="ResAuthoringLogUpdate1" xml:space="preserve">
    <value>Mise à jour du journal de création</value>
  </data>
  <data name="ResAuthoringNotes" xml:space="preserve">
    <value>Notes de rédaction</value>
  </data>
  <data name="ResAuthoringNotes1" xml:space="preserve">
    <value>Notes de rédaction</value>
  </data>
  <data name="ResAuthoringNotesAdd" xml:space="preserve">
    <value>Notes de rédaction - Ajouter</value>
  </data>
  <data name="ResAuthoringNotesAdd1" xml:space="preserve">
    <value>Notes de rédaction - Ajouter</value>
  </data>
  <data name="ResAuthoringNotesEdit" xml:space="preserve">
    <value>Notes de rédaction - Modifier</value>
  </data>
  <data name="ResAuthoringNotesEdit1" xml:space="preserve">
    <value>Notes de rédaction - Modifier</value>
  </data>
  <data name="ResAuthoringNotesList" xml:space="preserve">
    <value>Création d'une liste unique de notes</value>
  </data>
  <data name="ResAuthoringUsers" xml:space="preserve">
    <value>Utilisateurs auteurs</value>
  </data>
  <data name="ResAuthoringUsers1" xml:space="preserve">
    <value>Utilisateurs auteurs</value>
  </data>
  <data name="ResAuthorLog" xml:space="preserve">
    <value>Journal de l'auteur</value>
  </data>
  <data name="ResAuthorLog1" xml:space="preserve">
    <value>Journal de l'auteur</value>
  </data>
  <data name="ResAuthorNotes" xml:space="preserve">
    <value>Notes de l'auteur</value>
  </data>
  <data name="ResAuthorNotes1" xml:space="preserve">
    <value>Notes de l'auteur</value>
  </data>
  <data name="ResAuthorNotesAttachments" xml:space="preserve">
    <value>Documents joints aux notes de l'auteur</value>
  </data>
  <data name="ResAuthorNotesAttachments1" xml:space="preserve">
    <value>Documents joints aux notes de l'auteur</value>
  </data>
  <data name="ResAuthorProductionDetails" xml:space="preserve">
    <value>Détails de production de l'auteur</value>
  </data>
  <data name="ResAuthorTimeSheet" xml:space="preserve">
    <value>Feuille de temps de l'auteur</value>
  </data>
  <data name="ResAutogenerationEmail" xml:space="preserve">
    <value>Il s'agit d'un courrier généré automatiquement. Merci de ne pas répondre à cet e-mail.</value>
  </data>
  <data name="ResAutoHotspot" xml:space="preserve">
    <value>Hotspot automatique</value>
  </data>
  <data name="ResBacktologin" xml:space="preserve">
    <value>Retour à la connexion</value>
  </data>
  <data name="ResBackToTopLevel" xml:space="preserve">
    <value>Retour au niveau supérieur</value>
  </data>
  <data name="ResBCC" xml:space="preserve">
    <value>Copie carbone invisible</value>
  </data>
  <data name="ResBCC1" xml:space="preserve">
    <value>Copie carbone invisible</value>
  </data>
  <data name="ResBOMAdd" xml:space="preserve">
    <value>Ajouter une nomenclature</value>
  </data>
  <data name="ResBOMAdd1" xml:space="preserve">
    <value>Ajouter une nomenclature</value>
  </data>
  <data name="ResBOMDetails" xml:space="preserve">
    <value>Détails de la nomenclature</value>
  </data>
  <data name="ResBOMEdit" xml:space="preserve">
    <value>Modifier la nomenclature</value>
  </data>
  <data name="ResBOMEdit1" xml:space="preserve">
    <value>Modifier la nomenclature</value>
  </data>
  <data name="ResBOMGridConfiguration" xml:space="preserve">
    <value>Configuration de la grille de nomenclature</value>
  </data>
  <data name="ResBOMGUIConfigure" xml:space="preserve">
    <value>Configuration de l'interface graphique de la nomenclature</value>
  </data>
  <data name="ResBOMPart" xml:space="preserve">
    <value>Pièce de nomenclature</value>
  </data>
  <data name="ResBOMPartNotFound" xml:space="preserve">
    <value>Pièce de nomenclature introuvable</value>
  </data>
  <data name="ResBOMPrint" xml:space="preserve">
    <value>Impression de la nomenclature</value>
  </data>
  <data name="ResBOMPrint1" xml:space="preserve">
    <value>Impression de la nomenclature</value>
  </data>
  <data name="ResBookmark" xml:space="preserve">
    <value>Signet</value>
  </data>
  <data name="ResBookMarkDetails" xml:space="preserve">
    <value>Gérer les détails des signets</value>
  </data>
  <data name="ResBookMarkListDetails" xml:space="preserve">
    <value>Détails du signet</value>
  </data>
  <data name="ResBookmarkName" xml:space="preserve">
    <value>Nom du signet</value>
  </data>
  <data name="ResBookmarkName1" xml:space="preserve">
    <value>Nom du signet</value>
  </data>
  <data name="ResBookMarks" xml:space="preserve">
    <value>Signet</value>
  </data>
  <data name="ResBookMarks1" xml:space="preserve">
    <value>Signet</value>
  </data>
  <data name="ResBoth" xml:space="preserve">
    <value>Tous les deux?</value>
  </data>
  <data name="ResBoth?" xml:space="preserve">
    <value>Tous les deux?</value>
  </data>
  <data name="ResBoth?1" xml:space="preserve">
    <value>Tous les deux?</value>
  </data>
  <data name="ResBrand" xml:space="preserve">
    <value>Marque</value>
  </data>
  <data name="ResBrand-Add" xml:space="preserve">
    <value>Marque - Ajouter</value>
  </data>
  <data name="ResBrand-Add1" xml:space="preserve">
    <value>Marque - Ajouter</value>
  </data>
  <data name="ResBrand1" xml:space="preserve">
    <value>Marque</value>
  </data>
  <data name="ResBrandAssociation" xml:space="preserve">
    <value>Association de marques</value>
  </data>
  <data name="ResBrandAssociation1" xml:space="preserve">
    <value>Association de marques</value>
  </data>
  <data name="ResBrandAttachments" xml:space="preserve">
    <value>Marque - Documents joints</value>
  </data>
  <data name="ResBrandCode" xml:space="preserve">
    <value>Code de marque</value>
  </data>
  <data name="ResBrandCode1" xml:space="preserve">
    <value>Code de marque</value>
  </data>
  <data name="ResBrandDetails" xml:space="preserve">
    <value>Détails de la marque</value>
  </data>
  <data name="ResBrandImages" xml:space="preserve">
    <value>Images de marque</value>
  </data>
  <data name="ResBrandImages1" xml:space="preserve">
    <value>Images de marque</value>
  </data>
  <data name="ResBrandList" xml:space="preserve">
    <value>Liste des marques</value>
  </data>
  <data name="ResBrandList1" xml:space="preserve">
    <value>Liste des marques</value>
  </data>
  <data name="ResBrandName" xml:space="preserve">
    <value>Nom de marque</value>
  </data>
  <data name="ResBrandName1" xml:space="preserve">
    <value>Nom de marque</value>
  </data>
  <data name="ResBrandNameSearchList" xml:space="preserve">
    <value>Liste de recherche par nom de marque</value>
  </data>
  <data name="ResBrandNameSearchList1" xml:space="preserve">
    <value>Liste de recherche par nom de marque</value>
  </data>
  <data name="ResBrowse" xml:space="preserve">
    <value>Choisir le fichier</value>
  </data>
  <data name="ResCalendar" xml:space="preserve">
    <value>Calendrier</value>
  </data>
  <data name="ResCalendar1" xml:space="preserve">
    <value>Calendrier</value>
  </data>
  <data name="ResCancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="ResCancelQuery" xml:space="preserve">
    <value>Annuler la requête</value>
  </data>
  <data name="ResCannotExceedMoreThan255" xml:space="preserve">
    <value>La séquence doit être comprise entre 1 et 255</value>
  </data>
  <data name="ResCartName" xml:space="preserve">
    <value>Nom du panier</value>
  </data>
  <data name="ResCartName1" xml:space="preserve">
    <value>Nom du panier</value>
  </data>
  <data name="ResCartUIList" xml:space="preserve">
    <value>Liste de l'interface utilisateur du panier</value>
  </data>
  <data name="ResCartUIList1" xml:space="preserve">
    <value>Liste de l'interface utilisateur du panier</value>
  </data>
  <data name="ResCassic" xml:space="preserve">
    <value>Classique</value>
  </data>
  <data name="ResCassic1" xml:space="preserve">
    <value>Classique</value>
  </data>
  <data name="ResCatalogue" xml:space="preserve">
    <value>Catalogue</value>
  </data>
  <data name="ResCatalogue#" xml:space="preserve">
    <value>Numéro de catalogue</value>
  </data>
  <data name="ResCatalogue#1" xml:space="preserve">
    <value>Numéro de catalogue</value>
  </data>
  <data name="ResCatalogue1" xml:space="preserve">
    <value>Catalogue 1</value>
  </data>
  <data name="ResCatalogue11" xml:space="preserve">
    <value>Catalogue 1</value>
  </data>
  <data name="ResCatalogue2" xml:space="preserve">
    <value>Catalogue 2</value>
  </data>
  <data name="ResCatalogue21" xml:space="preserve">
    <value>Catalogue 2</value>
  </data>
  <data name="ResCatalogue3" xml:space="preserve">
    <value>Catalogue 3</value>
  </data>
  <data name="ResCatalogue31" xml:space="preserve">
    <value>Catalogue 3</value>
  </data>
  <data name="ResCatalogue4" xml:space="preserve">
    <value>Catalogue 4</value>
  </data>
  <data name="ResCatalogue41" xml:space="preserve">
    <value>Catalogue 4</value>
  </data>
  <data name="ResCatalogue5" xml:space="preserve">
    <value>Catalogue 5</value>
  </data>
  <data name="ResCatalogue51" xml:space="preserve">
    <value>Catalogue 5</value>
  </data>
  <data name="ResCatalogue6" xml:space="preserve">
    <value>Catalogue</value>
  </data>
  <data name="ResCatalogueAssociation" xml:space="preserve">
    <value>Association de catalogue</value>
  </data>
  <data name="ResCatalogueAssociation1" xml:space="preserve">
    <value>Association de catalogue</value>
  </data>
  <data name="ResCatalogueCompare" xml:space="preserve">
    <value>Comparation Catalogue </value>
  </data>
  <data name="ResCatalogueCompare1" xml:space="preserve">
    <value>Comparation Catalogue </value>
  </data>
  <data name="ResCatalogueCompareResult" xml:space="preserve">
    <value>Résultat de la comparaison de catalogue</value>
  </data>
  <data name="ResCatalogueCompareResult1" xml:space="preserve">
    <value>Résultat de la comparaison de catalogue</value>
  </data>
  <data name="ResCatalogueCustomerAttachment" xml:space="preserve">
    <value>Catalogue - Document joint client </value>
  </data>
  <data name="ResCatalogueCustomerLinkDocument" xml:space="preserve">
    <value>Catalogue - Lien Document client</value>
  </data>
  <data name="ResCatalogueDesc" xml:space="preserve">
    <value>Description du catalogue</value>
  </data>
  <data name="ResCatalogueDesc1" xml:space="preserve">
    <value>Description du catalogue</value>
  </data>
  <data name="ResCatalogueDescription" xml:space="preserve">
    <value>Description du catalogue</value>
  </data>
  <data name="ResCatalogueDescription1" xml:space="preserve">
    <value>Description du catalogue</value>
  </data>
  <data name="ResCatalogueDetails" xml:space="preserve">
    <value>Détails de catalogue</value>
  </data>
  <data name="ResCatalogueError" xml:space="preserve">
    <value>Erreur de catalogue</value>
  </data>
  <data name="ResCatalogueImages" xml:space="preserve">
    <value>Images du catalogue</value>
  </data>
  <data name="ResCatalogueImages1" xml:space="preserve">
    <value>Images du catalogue</value>
  </data>
  <data name="ResCatalogueLink" xml:space="preserve">
    <value>Lien du catalogue</value>
  </data>
  <data name="ResCatalogueLink1" xml:space="preserve">
    <value>Lien du catalogue</value>
  </data>
  <data name="ResCatalogueList" xml:space="preserve">
    <value>Liste de catalogue</value>
  </data>
  <data name="ResCatalogueList1" xml:space="preserve">
    <value>Liste de catalogue</value>
  </data>
  <data name="ResCatalogueName" xml:space="preserve">
    <value>Nom du catalogue</value>
  </data>
  <data name="ResCatalogueName1" xml:space="preserve">
    <value>Nom du catalogue</value>
  </data>
  <data name="ResCataloguePartDescription" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="ResCataloguePublish" xml:space="preserve">
    <value>Publication de catalogue</value>
  </data>
  <data name="ResCataloguePublishNotification" xml:space="preserve">
    <value>Notification de publication de catalogue</value>
  </data>
  <data name="ResCatalogueRevisionDetails" xml:space="preserve">
    <value>Détails de la révision du catalogue</value>
  </data>
  <data name="ResCatalogueRevisionHistory" xml:space="preserve">
    <value>Historique des révisions du catalogue</value>
  </data>
  <data name="ResCatalogueRevisionHistory1" xml:space="preserve">
    <value>Historique des révisions du catalogue</value>
  </data>
  <data name="ResCatalogueSearchList" xml:space="preserve">
    <value>Liste de recherche de catalogue</value>
  </data>
  <data name="ResCatalogueSearchList1" xml:space="preserve">
    <value>Liste de recherche de catalogue</value>
  </data>
  <data name="ResCatalogueServiceAttachment" xml:space="preserve">
    <value>Service de catalogue - Pièce jointe  </value>
  </data>
  <data name="ResCatalogueServiceLinkDocument" xml:space="preserve">
    <value>Document de lien de service de catalogue</value>
  </data>
  <data name="ResCatalogueStatusReport" xml:space="preserve">
    <value>Rapport sur l'état du catalogue</value>
  </data>
  <data name="ResCatalogueViewConfig" xml:space="preserve">
    <value>Configuration de la vue catalogue</value>
  </data>
  <data name="ResCatalogueViewConfig1" xml:space="preserve">
    <value>Configuration de la vue catalogue</value>
  </data>
  <data name="ResCatalogueViewConfiguration-List" xml:space="preserve">
    <value>Liste de configuration de la vue catalogue</value>
  </data>
  <data name="ResCatalogueViewConfiguration-List1" xml:space="preserve">
    <value>Liste de configuration de la vue catalogue</value>
  </data>
  <data name="ResCategory" xml:space="preserve">
    <value>Catégorie</value>
  </data>
  <data name="ResCC" xml:space="preserve">
    <value>CC (copie carbone)</value>
  </data>
  <data name="ResCC1" xml:space="preserve">
    <value>CC (copie carbone)</value>
  </data>
  <data name="ResChange?" xml:space="preserve">
    <value>Changement? Changer?</value>
  </data>
  <data name="ResChange?1" xml:space="preserve">
    <value>Changement? Changer?</value>
  </data>
  <data name="ResChangeOwnershipOfaVehilce" xml:space="preserve">
    <value>Changer la propriété d'un véhicule</value>
  </data>
  <data name="ResChangeOwnershipOfaVehilce1" xml:space="preserve">
    <value>Changer la propriété d'un véhicule</value>
  </data>
  <data name="ResChangePassword" xml:space="preserve">
    <value>Changer le mot de passe</value>
  </data>
  <data name="ResChangePassword1" xml:space="preserve">
    <value>Changer le mot de passe</value>
  </data>
  <data name="ResChangeRoad#ofaVehicle" xml:space="preserve">
    <value>Modifier le numéro de route d'un véhicule</value>
  </data>
  <data name="ResChangeRoad#ofaVehicle1" xml:space="preserve">
    <value>Modifier le numéro de route d'un véhicule</value>
  </data>
  <data name="ResChangeRoadDetails" xml:space="preserve">
    <value>Modifier les détails de la route</value>
  </data>
  <data name="ResChangeRoadDetails1" xml:space="preserve">
    <value>Modifier les détails de la route</value>
  </data>
  <data name="ResChangeRoadDetailsAttachments" xml:space="preserve">
    <value>Modifier les documents joints des détails de la route</value>
  </data>
  <data name="ResChangeRoadNumber" xml:space="preserve">
    <value>Changer le numéro de route</value>
  </data>
  <data name="ResChangeVehicleOwnership" xml:space="preserve">
    <value>Changer la propriété du véhicule</value>
  </data>
  <data name="ResChangeVehicleOwnership1" xml:space="preserve">
    <value>Changer la propriété du véhicule</value>
  </data>
  <data name="ResChangeVehicleOwnerShipAttachments" xml:space="preserve">
    <value>Modifier les documents joints de propriété du véhicule</value>
  </data>
  <data name="ResChangeVehicleOwnershipList" xml:space="preserve">
    <value>Liste de propriété du véhicule</value>
  </data>
  <data name="ResChangeVehicleOwnershipList1" xml:space="preserve">
    <value>Liste de propriété du véhicule</value>
  </data>
  <data name="ResChangeVIN" xml:space="preserve">
    <value>Changer le NIV (Numéro Identification Véhicule)</value>
  </data>
  <data name="ResChangeVIN#ofaVehicle" xml:space="preserve">
    <value>Changer le NIV (Numéro Identification Véhicule) d'un véhicule</value>
  </data>
  <data name="ResChangeVIN#ofaVehicle1" xml:space="preserve">
    <value>Changer le NIV (Numéro Identification Véhicule) d'un véhicule</value>
  </data>
  <data name="ResChangeVINDetails" xml:space="preserve">
    <value>Changer le NIV (Numéro Identification Véhicule) Détail</value>
  </data>
  <data name="ResChangeVINDetails1" xml:space="preserve">
    <value>Changer le NIV (Numéro Identification Véhicule) Détail</value>
  </data>
  <data name="ResChangeVINDetailsAttachments" xml:space="preserve">
    <value>Changer le NIV (Numéro Identification Véhicule )Détail Documents Joints</value>
  </data>
  <data name="ResCharacterleft" xml:space="preserve">
    <value>Charactères restants</value>
  </data>
  <data name="ResCharacterleft1" xml:space="preserve">
    <value>Charactères restants</value>
  </data>
  <data name="ResChild" xml:space="preserve">
    <value>Enfant</value>
  </data>
  <data name="ResChild1" xml:space="preserve">
    <value>Enfant</value>
  </data>
  <data name="ResChildDetails" xml:space="preserve">
    <value>Détails de l'enfant</value>
  </data>
  <data name="ResChildDetails1" xml:space="preserve">
    <value>Détails de l'enfant</value>
  </data>
  <data name="ResChildPartDetails" xml:space="preserve">
    <value>Détails de la pièce enfant</value>
  </data>
  <data name="ResChildPartDetails1" xml:space="preserve">
    <value>Détails de la pièce enfant</value>
  </data>
  <data name="ResChooseFile" xml:space="preserve">
    <value>Choisir le fichier</value>
  </data>
  <data name="ResChooseFile1" xml:space="preserve">
    <value>Choisir le fichier</value>
  </data>
  <data name="ResChooseFilewhichBeginswithDocumenttypeCode" xml:space="preserve">
    <value>Choisissez le fichier Commence par le type de document Code</value>
  </data>
  <data name="ResCircle" xml:space="preserve">
    <value>Cercle</value>
  </data>
  <data name="ResCity" xml:space="preserve">
    <value>Ville</value>
  </data>
  <data name="ResClassic" xml:space="preserve">
    <value>Classique</value>
  </data>
  <data name="ResClassic1" xml:space="preserve">
    <value>Classique</value>
  </data>
  <data name="ResClear" xml:space="preserve">
    <value>Effacer</value>
  </data>
  <data name="ResClickOnHotSpotsToUpdate" xml:space="preserve">
    <value>Cliquez sur Hotspot pour créer un lien.</value>
  </data>
  <data name="ResClicktoSaveHotspot" xml:space="preserve">
    <value>Cliquez pousauvegarder Hotspot</value>
  </data>
  <data name="ResClose" xml:space="preserve">
    <value>Fermer</value>
  </data>
  <data name="ResClose1" xml:space="preserve">
    <value>Fermer</value>
  </data>
  <data name="ResClosed" xml:space="preserve">
    <value>Fermé</value>
  </data>
  <data name="ResClosed1" xml:space="preserve">
    <value>Fermé</value>
  </data>
  <data name="ResCode" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="ResCode1" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="ResCollapse" xml:space="preserve">
    <value>Réduire</value>
  </data>
  <data name="ResCollapseAll" xml:space="preserve">
    <value>Tout Réduire</value>
  </data>
  <data name="ResColumnName" xml:space="preserve">
    <value>Nom de colonne</value>
  </data>
  <data name="ResColumnName1" xml:space="preserve">
    <value>Nom de colonne</value>
  </data>
  <data name="ResColumnValue" xml:space="preserve">
    <value>Valeur de la colonne</value>
  </data>
  <data name="ResColumnValue1" xml:space="preserve">
    <value>Valeur de la colonne</value>
  </data>
  <data name="ResComanyName" xml:space="preserve">
    <value>Nom de la société</value>
  </data>
  <data name="ResComments" xml:space="preserve">
    <value>Commentaires</value>
  </data>
  <data name="ResComments1" xml:space="preserve">
    <value>Commentaires</value>
  </data>
  <data name="ResCommonNotes" xml:space="preserve">
    <value>Notes communes</value>
  </data>
  <data name="ResCommonNotes1" xml:space="preserve">
    <value>Notes communes</value>
  </data>
  <data name="ResCommonNotesHistory" xml:space="preserve">
    <value>Historique des notes communes</value>
  </data>
  <data name="ResCommonNotesHistory1" xml:space="preserve">
    <value>Historique des notes communes</value>
  </data>
  <data name="ResCompare" xml:space="preserve">
    <value>Comparer</value>
  </data>
  <data name="ResCompare1" xml:space="preserve">
    <value>Comparer</value>
  </data>
  <data name="ResCompareAssemblies" xml:space="preserve">
    <value>Comparer les assemblages</value>
  </data>
  <data name="ResCompareAssemblies1" xml:space="preserve">
    <value>Comparer les assemblages</value>
  </data>
  <data name="ResComparedVersionList" xml:space="preserve">
    <value>Liste des versions comparées</value>
  </data>
  <data name="ResCompareVersion" xml:space="preserve">
    <value>Comparer la version</value>
  </data>
  <data name="ResCompareVersion1" xml:space="preserve">
    <value>Comparer la version</value>
  </data>
  <data name="ResCondition" xml:space="preserve">
    <value>État</value>
  </data>
  <data name="ResCondition1" xml:space="preserve">
    <value>État</value>
  </data>
  <data name="ResConfigure" xml:space="preserve">
    <value>Configurer</value>
  </data>
  <data name="ResConfigure1" xml:space="preserve">
    <value>Configurer</value>
  </data>
  <data name="ResConfigureAssemblyBOMUIAuthor" xml:space="preserve">
    <value>Interface utilisateur de la nomenclature de l'assemblage de configuration - Auteur</value>
  </data>
  <data name="ResConfigureAssemblyBOMUIViewer" xml:space="preserve">
    <value>Interface utilisateur de la nomenclature de l'assemblage de configuration - Visionneuse</value>
  </data>
  <data name="ResConfigureAssemblyBOMUIViewer1" xml:space="preserve">
    <value>Interface utilisateur de la nomenclature de l'assemblage de configuration - Visionneuse</value>
  </data>
  <data name="ResConfigureAssemblyBOMUIViewer2" xml:space="preserve">
    <value>Interface utilisateur de la nomenclature de l'assemblage de configuration - Visionneuse</value>
  </data>
  <data name="ResConfigureCartUI" xml:space="preserve">
    <value>Configurer</value>
  </data>
  <data name="ResConfigureCartUI1" xml:space="preserve">
    <value>Configurer l'interface utilisateur du panier</value>
  </data>
  <data name="ResConfigurePartsUI" xml:space="preserve">
    <value>Configurer l'interface utilisateur des pièces</value>
  </data>
  <data name="ResConfigurePartsUI1" xml:space="preserve">
    <value>Configurer l'interface utilisateur des pièces</value>
  </data>
  <data name="ResConfigureToolbar" xml:space="preserve">
    <value>Configurer la barre d'outils</value>
  </data>
  <data name="ResConfigureToolbar-List" xml:space="preserve">
    <value>Configurer la barre d'outils - Liste</value>
  </data>
  <data name="ResConfigureToolbar-List1" xml:space="preserve">
    <value>Configurer la barre d'outils - Liste</value>
  </data>
  <data name="ResConfigureToolbar1" xml:space="preserve">
    <value>Configurer la barre d'outils</value>
  </data>
  <data name="ResConfirmPassword" xml:space="preserve">
    <value>Confirmez le mot de passe</value>
  </data>
  <data name="ResConfirmPassword1" xml:space="preserve">
    <value>Confirmez le mot de passe</value>
  </data>
  <data name="ResConsumptionCode" xml:space="preserve">
    <value>Code de consommation</value>
  </data>
  <data name="ResConsumptionCode1" xml:space="preserve">
    <value>Code de consommation</value>
  </data>
  <data name="ResContains" xml:space="preserve">
    <value>Contient</value>
  </data>
  <data name="ResContains1" xml:space="preserve">
    <value>Contient</value>
  </data>
  <data name="ResCopy" xml:space="preserve">
    <value>Copier</value>
  </data>
  <data name="ResCopy1" xml:space="preserve">
    <value>Copier</value>
  </data>
  <data name="ResCopyAllSections" xml:space="preserve">
    <value>Copier toutes les sections</value>
  </data>
  <data name="ResCopyAllSections1" xml:space="preserve">
    <value>Copier toutes les sections</value>
  </data>
  <data name="ResCopyBOM" xml:space="preserve">
    <value>Copier la nomenclature</value>
  </data>
  <data name="ResCopyBOM1" xml:space="preserve">
    <value>Copier la nomenclature</value>
  </data>
  <data name="ResCopyCatalogue" xml:space="preserve">
    <value>Copier le catalogue</value>
  </data>
  <data name="ResCopyCatalogue1" xml:space="preserve">
    <value>Copier le catalogue</value>
  </data>
  <data name="ResCopyDetails" xml:space="preserve">
    <value>Copier les détails</value>
  </data>
  <data name="ResCopyDetails1" xml:space="preserve">
    <value>Copier les détails</value>
  </data>
  <data name="ResCopyLocalRemaksDetsils" xml:space="preserve">
    <value>Copier les remarques local</value>
  </data>
  <data name="ResCopyLocalRemarksTo" xml:space="preserve">
    <value>Copier les remarques locales dans</value>
  </data>
  <data name="ResCopyRoleFrom" xml:space="preserve">
    <value>Copier le rôle depuis</value>
  </data>
  <data name="ResCopyRoleFrom1" xml:space="preserve">
    <value>Copier le rôle depuis</value>
  </data>
  <data name="ResCopySections" xml:space="preserve">
    <value>Copier les sections</value>
  </data>
  <data name="ResCopySections1" xml:space="preserve">
    <value>Copier les sections</value>
  </data>
  <data name="ResCount" xml:space="preserve">
    <value>Compter</value>
  </data>
  <data name="ResCountry" xml:space="preserve">
    <value>Pays</value>
  </data>
  <data name="ResCreate" xml:space="preserve">
    <value>Créer</value>
  </data>
  <data name="ResCreate1" xml:space="preserve">
    <value>Créer</value>
  </data>
  <data name="ResCreateCrossReference" xml:space="preserve">
    <value>Créer une référence croisée</value>
  </data>
  <data name="ResCreatedBy" xml:space="preserve">
    <value>Créé par</value>
  </data>
  <data name="ResCreatedDate" xml:space="preserve">
    <value>Date de création</value>
  </data>
  <data name="ResCreateNew" xml:space="preserve">
    <value>Créer un nouveau</value>
  </data>
  <data name="ResCreateNew1" xml:space="preserve">
    <value>Créer un nouveau</value>
  </data>
  <data name="ResCreateNewQuery" xml:space="preserve">
    <value>Créer une nouvelle requête</value>
  </data>
  <data name="ResCreateNewQuery1" xml:space="preserve">
    <value>Créer une nouvelle requête</value>
  </data>
  <data name="ResCurrentOwner" xml:space="preserve">
    <value>Propriétaire actuel</value>
  </data>
  <data name="ResCurrentOwner1" xml:space="preserve">
    <value>Propriétaire actuel</value>
  </data>
  <data name="ResCurrentOwnerName" xml:space="preserve">
    <value>Nom du propriétaire actuel</value>
  </data>
  <data name="ResCurrentPassword" xml:space="preserve">
    <value>Mot de passe actuel</value>
  </data>
  <data name="ResCurrentRoad#" xml:space="preserve">
    <value>Numéro de route actuel</value>
  </data>
  <data name="ResCurrentRoad#1" xml:space="preserve">
    <value>Numéro de route actuel</value>
  </data>
  <data name="ResCurrentVIN#" xml:space="preserve">
    <value>NIV (Numéro Identification Véhicule) actuel</value>
  </data>
  <data name="ResCurrentVIN#1" xml:space="preserve">
    <value>NIV (Numéro Identification Véhicule) actuel</value>
  </data>
  <data name="ResCurrentVINS" xml:space="preserve">
    <value>FN-Current Owner VINs</value>
  </data>
  <data name="ResCustomer" xml:space="preserve">
    <value>Client</value>
  </data>
  <data name="ResCustomer-AssemblyViewConfiguration" xml:space="preserve">
    <value>Configuration de la vue d'assemblage Client</value>
  </data>
  <data name="ResCustomer-AssemblyViewConfiguration-List" xml:space="preserve">
    <value>Configuration de la vue d'assemblage Client - Liste</value>
  </data>
  <data name="ResCustomer-AssemblyViewConfiguration-List1" xml:space="preserve">
    <value>Configuration de la vue d'assemblage Client - Liste</value>
  </data>
  <data name="ResCustomer-AssemblyViewConfiguration1" xml:space="preserve">
    <value>Configuration de la vue d'assemblage Client</value>
  </data>
  <data name="ResCustomer-CatalogueViewConfiguration-List" xml:space="preserve">
    <value>Liste de configuration de la vue du catalogue Client</value>
  </data>
  <data name="ResCustomer-CatalogueViewConfiguration-List1" xml:space="preserve">
    <value>Liste de configuration de la vue du catalogue Client</value>
  </data>
  <data name="ResCustomer-PartsInformation" xml:space="preserve">
    <value>Informations pièces - Client</value>
  </data>
  <data name="ResCustomer-PartsInformation1" xml:space="preserve">
    <value>Informations pièces - Client</value>
  </data>
  <data name="ResCustomer-PartsViewConfiguration-List" xml:space="preserve">
    <value>Liste de configuration de la vue des pièces - Client</value>
  </data>
  <data name="ResCustomer-PartsViewConfiguration-List1" xml:space="preserve">
    <value>Liste de configuration de la vue des pièces - Client</value>
  </data>
  <data name="ResCustomer-ShoppingCartConfiguration" xml:space="preserve">
    <value>Configuration du panier d'achat - Client </value>
  </data>
  <data name="ResCustomer-ShoppingCartConfiguration1" xml:space="preserve">
    <value>Configuration du panier d'achat - Client </value>
  </data>
  <data name="ResCustomer1" xml:space="preserve">
    <value>Client</value>
  </data>
  <data name="ResCustomerAccountlength" xml:space="preserve">
    <value>La longueur du numéro de compte client doit être comprise entre 1 et 8</value>
  </data>
  <data name="ResCustomerAccountNumber" xml:space="preserve">
    <value>Numéro de compte client</value>
  </data>
  <data name="ResCustomerAnnotation" xml:space="preserve">
    <value>Annotation/Commentaire client</value>
  </data>
  <data name="ResCustomerAnnotation1" xml:space="preserve">
    <value>Annotation/Commentaire client</value>
  </data>
  <data name="ResCustomerAssociation" xml:space="preserve">
    <value>Association de clients</value>
  </data>
  <data name="ResCustomerAssociation1" xml:space="preserve">
    <value>Association de clients</value>
  </data>
  <data name="ResCustomerAttachments" xml:space="preserve">
    <value>Documents joints-Client</value>
  </data>
  <data name="ResCustomerAttachments1" xml:space="preserve">
    <value>Documents joints - Client</value>
  </data>
  <data name="ResCustomerCode" xml:space="preserve">
    <value>Code Client</value>
  </data>
  <data name="ResCustomerCode1" xml:space="preserve">
    <value>Code Client</value>
  </data>
  <data name="ResCustomerCopy" xml:space="preserve">
    <value>Copie du client</value>
  </data>
  <data name="ResCustomerDetails" xml:space="preserve">
    <value>Détails du client</value>
  </data>
  <data name="ResCustomerDoesNotExist" xml:space="preserve">
    <value>Le Client n'existe pas</value>
  </data>
  <data name="ResCustomerErrorLogReport" xml:space="preserve">
    <value>Rapport du journal des erreurs Client</value>
  </data>
  <data name="ResCustomerErrorReportAttachments" xml:space="preserve">
    <value>Rapport d'erreur Client - Documents joints </value>
  </data>
  <data name="ResCustomerImages" xml:space="preserve">
    <value>Images des Clients</value>
  </data>
  <data name="ResCustomerInfo" xml:space="preserve">
    <value>Informations concernant le Client</value>
  </data>
  <data name="ResCustomerInfo1" xml:space="preserve">
    <value>Informations concernant le Client</value>
  </data>
  <data name="ResCustomerLangaugeList" xml:space="preserve">
    <value>Liste des langues du Client</value>
  </data>
  <data name="ResCustomerLevel" xml:space="preserve">
    <value>Niveau Client</value>
  </data>
  <data name="ResCustomerLevel1" xml:space="preserve">
    <value>Niveau Client</value>
  </data>
  <data name="ResCustomerList" xml:space="preserve">
    <value>Liste de Clients</value>
  </data>
  <data name="ResCustomerList1" xml:space="preserve">
    <value>Liste de Clients</value>
  </data>
  <data name="ResCustomerLog" xml:space="preserve">
    <value>Journal Client</value>
  </data>
  <data name="ResCustomerLog1" xml:space="preserve">
    <value>Journal Client</value>
  </data>
  <data name="ResCustomerMFGCode" xml:space="preserve">
    <value>Code de fabrication Client</value>
  </data>
  <data name="ResCustomerMFGCode1" xml:space="preserve">
    <value>Code de fabrication Client</value>
  </data>
  <data name="ResCustomerName" xml:space="preserve">
    <value>Nom du Client</value>
  </data>
  <data name="ResCustomerName1" xml:space="preserve">
    <value>Nom du Client</value>
  </data>
  <data name="ResCustomerNameSearch" xml:space="preserve">
    <value>Recherche par nom de Client</value>
  </data>
  <data name="ResCustomerNameSearchList" xml:space="preserve">
    <value>Liste de recherche de Clients</value>
  </data>
  <data name="ResCustomerNameSearchList1" xml:space="preserve">
    <value>Liste de recherche de Clients</value>
  </data>
  <data name="ResCustomerOrder" xml:space="preserve">
    <value>Commande du Client</value>
  </data>
  <data name="ResCustomerOrder/Road#" xml:space="preserve">
    <value>Commande du Client / Numéro de route</value>
  </data>
  <data name="ResCustomerOrder/Road#1" xml:space="preserve">
    <value>Commande du Client / Numéro de route</value>
  </data>
  <data name="ResCustomerOrder1" xml:space="preserve">
    <value>Commande du Client</value>
  </data>
  <data name="ResCustomerOrderDetails" xml:space="preserve">
    <value>Détails de la commande Client</value>
  </data>
  <data name="ResCustomerOrderList" xml:space="preserve">
    <value>Détails de la commande</value>
  </data>
  <data name="ResCustomerOrderReport" xml:space="preserve">
    <value>Rapport de commande Client</value>
  </data>
  <data name="ResCustomerOrderRoad#" xml:space="preserve">
    <value>Commande du Client / Numéro de route</value>
  </data>
  <data name="ResCustomerOrderRoad#1" xml:space="preserve">
    <value>Commande du Client / Numéro de route</value>
  </data>
  <data name="ResCustomerOrderSearchList" xml:space="preserve">
    <value>Liste de recherche de commande Client</value>
  </data>
  <data name="ResCustomerOtherAttachments" xml:space="preserve">
    <value>Client / Autres documents joints</value>
  </data>
  <data name="ResCustomerPart#" xml:space="preserve">
    <value>Numéro de pièce Client</value>
  </data>
  <data name="ResCustomerPart#1" xml:space="preserve">
    <value>Numéro de pièce Client</value>
  </data>
  <data name="ResCustomerPart#XML" xml:space="preserve">
    <value>Numero_de_la_piece_Client</value>
  </data>
  <data name="ResCustomerPartInfo" xml:space="preserve">
    <value>Informations sur la pièce Client</value>
  </data>
  <data name="ResCustomerPartInfo1" xml:space="preserve">
    <value>Informations sur la pièce Client</value>
  </data>
  <data name="ResCustomerPartInfoDetails" xml:space="preserve">
    <value>Détails des informations sur les pièces du client</value>
  </data>
  <data name="ResCustomerPartInfoReport" xml:space="preserve">
    <value>Rapport d'informations sur les pièces du client</value>
  </data>
  <data name="ResCustomerPartInfoReports" xml:space="preserve">
    <value>Rapport d'informations sur les pièces du client</value>
  </data>
  <data name="ResCustomerPartInformationImport" xml:space="preserve">
    <value>Importation d'informations sur les pièces client</value>
  </data>
  <data name="ResCustomerPartInformationImport1" xml:space="preserve">
    <value>Importation d'informations sur les pièces client</value>
  </data>
  <data name="ResCustomerPartInfoTemplate" xml:space="preserve">
    <value>CustomerPartInfoTemplate</value>
  </data>
  <data name="ResCustomerPartMFGCode" xml:space="preserve">
    <value>Code de fabrication Client</value>
  </data>
  <data name="ResCustomerPartNumber" xml:space="preserve">
    <value>Numéro de pièce Client</value>
  </data>
  <data name="ResCustomerPartsInformation" xml:space="preserve">
    <value>Informations pièces - Client</value>
  </data>
  <data name="ResCustomerPartsInformation1" xml:space="preserve">
    <value>Informations pièces - Client</value>
  </data>
  <data name="ResCustomerPrefix" xml:space="preserve">
    <value>Préfixe Client</value>
  </data>
  <data name="ResCustomerPrefix1" xml:space="preserve">
    <value>Préfixe Client</value>
  </data>
  <data name="ResCustomerRef#" xml:space="preserve">
    <value>Numéro de référence - Client</value>
  </data>
  <data name="ResCustomerRef#1" xml:space="preserve">
    <value>Numéro de référence - Client</value>
  </data>
  <data name="ResCustomerReferenceNumer" xml:space="preserve">
    <value>Numéro de référence du client</value>
  </data>
  <data name="ResCustomers" xml:space="preserve">
    <value>Les clients</value>
  </data>
  <data name="ResCustomerSearch" xml:space="preserve">
    <value>Recherche de clients</value>
  </data>
  <data name="ResCustomerSerachList" xml:space="preserve">
    <value>Liste de recherche de Clients</value>
  </data>
  <data name="ResCustomerUsageLog" xml:space="preserve">
    <value>Journal d'utilisation du Client</value>
  </data>
  <data name="ResCustomerUsageLog1" xml:space="preserve">
    <value>Journal d'utilisation du Client</value>
  </data>
  <data name="ResCustomerUserList" xml:space="preserve">
    <value>Liste des utilisateurs Client</value>
  </data>
  <data name="ResCustomerUserRole" xml:space="preserve">
    <value>Rôle de l'utilisateur - Client</value>
  </data>
  <data name="ResCustomerUserRole1" xml:space="preserve">
    <value>Rôle de l'utilisateur - Client</value>
  </data>
  <data name="ResCustomerUsers" xml:space="preserve">
    <value>Utilisateurs Clients</value>
  </data>
  <data name="ResCustomerUsers1" xml:space="preserve">
    <value>Utilisateurs Clients</value>
  </data>
  <data name="ResCustomerVehicleDetails" xml:space="preserve">
    <value>Détails du véhicule - Client</value>
  </data>
  <data name="ResCustomerVehicleReport" xml:space="preserve">
    <value>Rapport de véhicule - Client</value>
  </data>
  <data name="ResCustromerName" xml:space="preserve">
    <value>Nom du Client</value>
  </data>
  <data name="ResCustromerName1" xml:space="preserve">
    <value>Nom du Client</value>
  </data>
  <data name="ResDashboard" xml:space="preserve">
    <value>Tableau de bord</value>
  </data>
  <data name="ResDataForOffline" xml:space="preserve">
    <value>Données pour hors ligne</value>
  </data>
  <data name="ResDate" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="ResDate&amp;Time" xml:space="preserve">
    <value>Date et heure</value>
  </data>
  <data name="ResDate&amp;Time1" xml:space="preserve">
    <value>Date et heure</value>
  </data>
  <data name="ResDate1" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="ResDear" xml:space="preserve">
    <value>Bonjour</value>
  </data>
  <data name="ResDearSir/Madam" xml:space="preserve">
    <value>Cher Monsieur / Madame</value>
  </data>
  <data name="ResDearSirMadam" xml:space="preserve">
    <value>Cher Monsieur / Madame</value>
  </data>
  <data name="ResDefaultFont" xml:space="preserve">
    <value>Police de caractères par défaut</value>
  </data>
  <data name="ResDefaultFont1" xml:space="preserve">
    <value>Police de caractères par défaut</value>
  </data>
  <data name="ResDefaultName" xml:space="preserve">
    <value>Nom par défaut</value>
  </data>
  <data name="ResDefaultName1" xml:space="preserve">
    <value>Nom par défaut</value>
  </data>
  <data name="ResDefaultSearchForNova" xml:space="preserve">
    <value>Rechercher NOVA</value>
  </data>
  <data name="ResDefaultSearchForPrevost" xml:space="preserve">
    <value>Rechercher PREVOST</value>
  </data>
  <data name="ResDefectNotification" xml:space="preserve">
    <value>Notification de défaut</value>
  </data>
  <data name="ResDelete" xml:space="preserve">
    <value>Effacer</value>
  </data>
  <data name="ResDelete1" xml:space="preserve">
    <value>Effacer</value>
  </data>
  <data name="ResDeleteAllHighlighter" xml:space="preserve">
    <value>Supprimer tous les surligneurs</value>
  </data>
  <data name="ResDeleteAnnotation" xml:space="preserve">
    <value>Supprimer l'annotation</value>
  </data>
  <data name="ResDeleteBOMPartItem" xml:space="preserve">
    <value>Supprimer un article de la nomenclature</value>
  </data>
  <data name="ResDeleteBOMPartItem1" xml:space="preserve">
    <value>Supprimer un article de la nomenclature</value>
  </data>
  <data name="ResDeleteHighlighter" xml:space="preserve">
    <value>Supprimer le surligneur</value>
  </data>
  <data name="ResDeleteHotspot" xml:space="preserve">
    <value>Supprimer le hotspot</value>
  </data>
  <data name="ResDeleteQuery" xml:space="preserve">
    <value>Supprimer la requête</value>
  </data>
  <data name="ResDeleteQuery1" xml:space="preserve">
    <value>Supprimer la requête</value>
  </data>
  <data name="ResDeleteSectionItem" xml:space="preserve">
    <value>Supprimer l'élément de section</value>
  </data>
  <data name="ResDeleteSectionItem1" xml:space="preserve">
    <value>Supprimer l'élément de section</value>
  </data>
  <data name="ResDeleteSelected" xml:space="preserve">
    <value>Supprimer sélectionné</value>
  </data>
  <data name="ResDepartment" xml:space="preserve">
    <value>Département</value>
  </data>
  <data name="ResDepartment1" xml:space="preserve">
    <value>Département</value>
  </data>
  <data name="ResDepartmentCode" xml:space="preserve">
    <value>Code départemental</value>
  </data>
  <data name="ResDepartmentCode1" xml:space="preserve">
    <value>Code départemental</value>
  </data>
  <data name="ResDepartmentLanguage-Add" xml:space="preserve">
    <value>Langue Département - Ajouter</value>
  </data>
  <data name="ResDepartmentLanguage-Add1" xml:space="preserve">
    <value>Langue Département - Ajouter</value>
  </data>
  <data name="ResDepartmentLanguage-Edit" xml:space="preserve">
    <value>Langue Département - Modifier</value>
  </data>
  <data name="ResDepartmentLanguage-Edit1" xml:space="preserve">
    <value>Langue Département - Modifier</value>
  </data>
  <data name="ResDepartmentLanguage-List" xml:space="preserve">
    <value>Langue Département - Liste</value>
  </data>
  <data name="ResDepartmentLanguage-List1" xml:space="preserve">
    <value>Langue Département - Liste</value>
  </data>
  <data name="ResDepartmentList" xml:space="preserve">
    <value>Liste des départements</value>
  </data>
  <data name="ResDepartmentList1" xml:space="preserve">
    <value>Liste des départements</value>
  </data>
  <data name="ResDepartmentName" xml:space="preserve">
    <value>Nom du département</value>
  </data>
  <data name="ResDepartmentName1" xml:space="preserve">
    <value>Nom du département</value>
  </data>
  <data name="ResDepartmentSearchList" xml:space="preserve">
    <value>Liste de recherche de département</value>
  </data>
  <data name="ResDepartmentSearchList1" xml:space="preserve">
    <value>Liste de recherche de département</value>
  </data>
  <data name="ResDepartmentUpdation" xml:space="preserve">
    <value>Mise à jour du département</value>
  </data>
  <data name="ResDepartmentUpdation1" xml:space="preserve">
    <value>Mise à jour du département</value>
  </data>
  <data name="ResDescription" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="ResDescription1" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="ResDescriptionDoesNotExist" xml:space="preserve">
    <value>La description n'existe pas</value>
  </data>
  <data name="ResDescriptionLangiageList" xml:space="preserve">
    <value>Liste des langues</value>
  </data>
  <data name="ResDesignation" xml:space="preserve">
    <value>Désignation</value>
  </data>
  <data name="ResDesignation1" xml:space="preserve">
    <value>Désignation</value>
  </data>
  <data name="ResDesignationCode" xml:space="preserve">
    <value>Code de désignation</value>
  </data>
  <data name="ResDesignationCode1" xml:space="preserve">
    <value>Code de désignation</value>
  </data>
  <data name="ResDesignationLanguage-Add" xml:space="preserve">
    <value>Désignation Langue-Ajouter</value>
  </data>
  <data name="ResDesignationLanguage-Add1" xml:space="preserve">
    <value>Désignation Langue-Ajouter</value>
  </data>
  <data name="ResDesignationLanguage-Edit" xml:space="preserve">
    <value>Désignation Langue-Modifier</value>
  </data>
  <data name="ResDesignationLanguage-Edit1" xml:space="preserve">
    <value>Désignation Langue-Modifier</value>
  </data>
  <data name="ResDesignationList" xml:space="preserve">
    <value>Liste de désignation</value>
  </data>
  <data name="ResDesignationList1" xml:space="preserve">
    <value>Liste de désignation</value>
  </data>
  <data name="ResDesignationName" xml:space="preserve">
    <value>Nom de la désignation</value>
  </data>
  <data name="ResDesignationName1" xml:space="preserve">
    <value>Nom de la désignation</value>
  </data>
  <data name="ResDesignationSearchList" xml:space="preserve">
    <value>Liste de recherche de désignation</value>
  </data>
  <data name="ResDesignationSearchList1" xml:space="preserve">
    <value>Liste de recherche de désignation</value>
  </data>
  <data name="ResDesignationUpdation" xml:space="preserve">
    <value>Mise à jour de la désignation</value>
  </data>
  <data name="ResDesignationUpdation1" xml:space="preserve">
    <value>Mise à jour de la désignation</value>
  </data>
  <data name="ResDetail" xml:space="preserve">
    <value>Detail</value>
  </data>
  <data name="ResDictionary" xml:space="preserve">
    <value>Dictionnaire</value>
  </data>
  <data name="ResDictionary1" xml:space="preserve">
    <value>Dictionnaire</value>
  </data>
  <data name="ResDictionaryDescriptionUpdation" xml:space="preserve">
    <value>Mise à jour de la description du dictionnaire</value>
  </data>
  <data name="ResDictionaryDescriptionUpdation1" xml:space="preserve">
    <value>Mise à jour de la description du dictionnaire</value>
  </data>
  <data name="ResDictionaryLanguage-List" xml:space="preserve">
    <value>Liste des langues du dictionnaire</value>
  </data>
  <data name="ResDictionaryLanguage-List1" xml:space="preserve">
    <value>Liste des langues du dictionnaire</value>
  </data>
  <data name="ResDictionaryLanguageList" xml:space="preserve">
    <value>Liste des langues du dictionnaire</value>
  </data>
  <data name="ResDictionaryList" xml:space="preserve">
    <value>Liste des dictionnaires</value>
  </data>
  <data name="ResDictionaryList1" xml:space="preserve">
    <value>Liste des dictionnaires</value>
  </data>
  <data name="ResDigitalPartsCatalogue" xml:space="preserve">
    <value>Catalogue de pièces numériques</value>
  </data>
  <data name="ResDisplayColumnName" xml:space="preserve">
    <value>Afficher le nom de la colonne</value>
  </data>
  <data name="ResDisplayMode" xml:space="preserve">
    <value>Afficher</value>
  </data>
  <data name="ResDisplayName" xml:space="preserve">
    <value>Afficher un nom</value>
  </data>
  <data name="ResDisplayName1" xml:space="preserve">
    <value>Afficher un nom</value>
  </data>
  <data name="ResDocumentNotification" xml:space="preserve">
    <value>Avis de documents</value>
  </data>
  <data name="ResDocumentCategory" xml:space="preserve">
    <value>Catégorie de documents</value>
  </data>
  <data name="ResDocumentType" xml:space="preserve">
    <value>Type de document</value>
  </data>
  <data name="ResDocumentTypeName" xml:space="preserve">
    <value>Nom du type de document</value>
  </data>
  <data name="ResDownload" xml:space="preserve">
    <value>Télécharger</value>
  </data>
  <data name="ResDownloadAsPDF" xml:space="preserve">
    <value>Télécharger en format PDF</value>
  </data>
  <data name="ResDownloadAsPDF1" xml:space="preserve">
    <value>Télécharger en format PDF</value>
  </data>
  <data name="ResDownLoadReleaseNote" xml:space="preserve">
    <value>Télécharger la note de publication</value>
  </data>
  <data name="ResDownoadTemplate" xml:space="preserve">
    <value>Télécharger le modèle</value>
  </data>
  <data name="ResDrawing" xml:space="preserve">
    <value>Dessin</value>
  </data>
  <data name="ResDrawing#" xml:space="preserve">
    <value>Numéro du dessin</value>
  </data>
  <data name="ResDrawing#1" xml:space="preserve">
    <value>Numéro du dessin</value>
  </data>
  <data name="ResDrawingBOMDetails" xml:space="preserve">
    <value>Détails de la nomenclature du dessin</value>
  </data>
  <data name="ResDrawingDelete" xml:space="preserve">
    <value>Supprimer le dessin</value>
  </data>
  <data name="ResDrawingDelete1" xml:space="preserve">
    <value>Supprimer le dessin</value>
  </data>
  <data name="ResDrawingList" xml:space="preserve">
    <value>Liste de dessin</value>
  </data>
  <data name="ResDrawingList1" xml:space="preserve">
    <value>Liste de dessin</value>
  </data>
  <data name="ResDrawingLog" xml:space="preserve">
    <value>Journal de dessin</value>
  </data>
  <data name="ResDrawingLog1" xml:space="preserve">
    <value>Journal de dessin</value>
  </data>
  <data name="ResDrawingName" xml:space="preserve">
    <value>Nom du dessin</value>
  </data>
  <data name="ResDrawingName1" xml:space="preserve">
    <value>Nom du dessin</value>
  </data>
  <data name="ResDrawingToolbar" xml:space="preserve">
    <value>Configuration de l'outil de dessin</value>
  </data>
  <data name="ResDrawingToolbar1" xml:space="preserve">
    <value>Configuration de l'outil de dessin</value>
  </data>
  <data name="ResDrawingToolbarAttachment" xml:space="preserve">
    <value>Barre d'outils de dessin - Document joint </value>
  </data>
  <data name="ResDrawingToolbarAttachment1" xml:space="preserve">
    <value>Barre d'outils de dessin - Document joint </value>
  </data>
  <data name="ResDrawingToolbarConfig" xml:space="preserve">
    <value>Configuration de la barre d'outils de dessin.</value>
  </data>
  <data name="ResDrawingToolbarConfig1" xml:space="preserve">
    <value>Configuration de la barre d'outils de dessin.</value>
  </data>
  <data name="Resduetorelease" xml:space="preserve">
    <value>sera relaché</value>
  </data>
  <data name="ResDuplicateCartName" xml:space="preserve">
    <value>Dupliquer Nom du panier</value>
  </data>
  <data name="ResDuplicateCatalogueNo" xml:space="preserve">
    <value>Dupliquer Numéro de catalogue</value>
  </data>
  <data name="ResDuplicateCustomer" xml:space="preserve">
    <value>Dupliquer Client</value>
  </data>
  <data name="ResDuplicateFile" xml:space="preserve">
    <value>Dupliquer le fichier</value>
  </data>
  <data name="ResDuplicateOptionEntry" xml:space="preserve">
    <value>Dupliquer Code d'option</value>
  </data>
  <data name="ResDuplicatePartRecords" xml:space="preserve">
    <value>Dupliquer les enregistrements de pièces</value>
  </data>
  <data name="ResDuplicateQueryName" xml:space="preserve">
    <value>Dupliquer Nom de requête</value>
  </data>
  <data name="ResDuplicateRecords" xml:space="preserve">
    <value>Dupliquer les informations</value>
  </data>
  <data name="ResDuplicateRoleName" xml:space="preserve">
    <value>Dupliquer Nom de rôle</value>
  </data>
  <data name="ResEdit" xml:space="preserve">
    <value>Modifier</value>
  </data>
  <data name="ResEdit1" xml:space="preserve">
    <value>Modifier</value>
  </data>
  <data name="ResEditAbbrevation" xml:space="preserve">
    <value>Modifier l'abréviation</value>
  </data>
  <data name="ResEditAbbreviationDescription" xml:space="preserve">
    <value>Modifier la description de l'abréviation</value>
  </data>
  <data name="ResEditable" xml:space="preserve">
    <value>Modifiable</value>
  </data>
  <data name="ResEditAnnotation" xml:space="preserve">
    <value>Modifier l'annotation</value>
  </data>
  <data name="ResEditBOMLineItem" xml:space="preserve">
    <value>Modifier l'élément de ligne de nomenclature</value>
  </data>
  <data name="ResEditBOMLineItem1" xml:space="preserve">
    <value>Modifier l'élément de ligne de nomenclature</value>
  </data>
  <data name="ResEditCustomer" xml:space="preserve">
    <value>Modifier le client</value>
  </data>
  <data name="ResEditCustomerPartInfo" xml:space="preserve">
    <value>Modifier les informations sur la pièce client</value>
  </data>
  <data name="ResEditCustomerPartInfo1" xml:space="preserve">
    <value>Modifier les informations sur la pièce client</value>
  </data>
  <data name="ResEditDictionary" xml:space="preserve">
    <value>Modifier le dictionnaire</value>
  </data>
  <data name="ResEditFont" xml:space="preserve">
    <value>Modifier la police de caractère</value>
  </data>
  <data name="ResEditFunctionGroup" xml:space="preserve">
    <value>Modifier le groupe de fonctions</value>
  </data>
  <data name="ResEditGUIConfig" xml:space="preserve">
    <value>Modifier la configuration de l'interface graphique</value>
  </data>
  <data name="ResEditImage" xml:space="preserve">
    <value>Modifier l'image</value>
  </data>
  <data name="ResEditLineItem" xml:space="preserve">
    <value>Modifier l'élément de campagne</value>
  </data>
  <data name="ResEditLineItem1" xml:space="preserve">
    <value>Modifier l'élément de campagne</value>
  </data>
  <data name="ResEditMaskingAction" xml:space="preserve">
    <value>Modifier - Action de masquage</value>
  </data>
  <data name="ResEditMFGCode" xml:space="preserve">
    <value>Modifier le code de fabrication</value>
  </data>
  <data name="ResEditMFGCode1" xml:space="preserve">
    <value>Modifier le code de fabrication</value>
  </data>
  <data name="ResEditModel" xml:space="preserve">
    <value>Modifier le modèle</value>
  </data>
  <data name="ResEditModel1" xml:space="preserve">
    <value>Modifier le modèle</value>
  </data>
  <data name="ResEditOptions" xml:space="preserve">
    <value>Modifier les options</value>
  </data>
  <data name="ResEditRecommendedParts" xml:space="preserve">
    <value>Modifier les pièces recommandées</value>
  </data>
  <data name="ResEditRoadHistory" xml:space="preserve">
    <value>Modifier le numéro de route</value>
  </data>
  <data name="ResEditSetMember" xml:space="preserve">
    <value>Modifier le membre de l'ensemble</value>
  </data>
  <data name="ResEditSupersession" xml:space="preserve">
    <value>Modifier le remplacement</value>
  </data>
  <data name="ResEditSupersession1" xml:space="preserve">
    <value>Modifier le remplacement</value>
  </data>
  <data name="ResEditUser" xml:space="preserve">
    <value>Modifier l'utilisateur</value>
  </data>
  <data name="ResEditVehicleInfo" xml:space="preserve">
    <value>Modifier les informations du véhicule</value>
  </data>
  <data name="ResEditVehicleOwnership" xml:space="preserve">
    <value>Modifier la propriété du véhicule</value>
  </data>
  <data name="ResEditVendorInfo" xml:space="preserve">
    <value>Modifier les informations du fournisseur</value>
  </data>
  <data name="ResEditVendorName" xml:space="preserve">
    <value>Modifier le nom du fournisseur</value>
  </data>
  <data name="ResEffectiveDate" xml:space="preserve">
    <value>Date effective</value>
  </data>
  <data name="ResEffectiveDate1" xml:space="preserve">
    <value>Date effective</value>
  </data>
  <data name="ResEffectiveFromDate" xml:space="preserve">
    <value>Date d'entrée en vigueur</value>
  </data>
  <data name="ResEffectiveFromDate1" xml:space="preserve">
    <value>Date d'entrée en vigueur</value>
  </data>
  <data name="ResEffectiveToDate" xml:space="preserve">
    <value>En vigueur à ce jour</value>
  </data>
  <data name="ResEffectiveToDate1" xml:space="preserve">
    <value>En vigueur à ce jour</value>
  </data>
  <data name="ResElectricalDiagrams" xml:space="preserve">
    <value>Schémas électriques</value>
  </data>
  <data name="ResEmail" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="ResEmail1" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="ResEmailalreadyexistforanotheruser" xml:space="preserve">
    <value>L'e-mail existe déjà pour un autre utilisateur</value>
  </data>
  <data name="ResEmailID" xml:space="preserve">
    <value>Courriel:</value>
  </data>
  <data name="ResEmployeeCode" xml:space="preserve">
    <value>Code de l'employé</value>
  </data>
  <data name="ResEmployeeCode1" xml:space="preserve">
    <value>Code de l'employé</value>
  </data>
  <data name="ResEmployeeDetails" xml:space="preserve">
    <value>Détails de l'employé</value>
  </data>
  <data name="ResEmployeeName" xml:space="preserve">
    <value>Nom de l'employé</value>
  </data>
  <data name="ResEndWith" xml:space="preserve">
    <value>Terminer par</value>
  </data>
  <data name="ResEndWith1" xml:space="preserve">
    <value>Terminer par</value>
  </data>
  <data name="ResEnglish" xml:space="preserve">
    <value>Anglais</value>
  </data>
  <data name="ResEnglishDocument" xml:space="preserve">
    <value>Chemin du document en anglais</value>
  </data>
  <data name="ResEnhanced" xml:space="preserve">
    <value>Renforcé</value>
  </data>
  <data name="ResEnhanced1" xml:space="preserve">
    <value>Renforcé</value>
  </data>
  <data name="ResEnhanced11" xml:space="preserve">
    <value>Renforcé</value>
  </data>
  <data name="ResEnhanced2" xml:space="preserve">
    <value>Renforcé</value>
  </data>
  <data name="ResEnterBrandCode" xml:space="preserve">
    <value>Entrez le code de la marque</value>
  </data>
  <data name="ResEnterBrandCode1" xml:space="preserve">
    <value>Entrez le code de la marque</value>
  </data>
  <data name="resEnterbw1to999" xml:space="preserve">
    <value>Entrez l'épaisseur entre [1-999]</value>
  </data>
  <data name="ResEnterCartDate" xml:space="preserve">
    <value>Entrez la date du panier</value>
  </data>
  <data name="ResEnterCartDate1" xml:space="preserve">
    <value>Entrez la date du panier</value>
  </data>
  <data name="ResEnterCartName" xml:space="preserve">
    <value>Entrez le nom du panier</value>
  </data>
  <data name="ResEnterCartName1" xml:space="preserve">
    <value>Entrez le nom du panier</value>
  </data>
  <data name="ResEnterCode" xml:space="preserve">
    <value>Entrez le code</value>
  </data>
  <data name="ResEnterCode1" xml:space="preserve">
    <value>Entrez le code</value>
  </data>
  <data name="ResEnterDescription" xml:space="preserve">
    <value>Entrez la description</value>
  </data>
  <data name="ResEnteredInvalidSequenceNo" xml:space="preserve">
    <value>Numéro de séquence saisi non valide</value>
  </data>
  <data name="ResEnteredMaskingDescriptionalreadyexist" xml:space="preserve">
    <value>La description de masquage saisie existe déjà</value>
  </data>
  <data name="ResEnteredOptionCodealreadyexist" xml:space="preserve">
    <value>Le code d'option saisi existe déjà</value>
  </data>
  <data name="ResEnteredOptionDescriptionalreadyexist" xml:space="preserve">
    <value>La description d'option saisie existe déjà</value>
  </data>
  <data name="ResEnteredSequenceNoAlreadyExists" xml:space="preserve">
    <value>Le numéro de séquence entré existe déjà</value>
  </data>
  <data name="ResEnteredURLIsInvalid" xml:space="preserve">
    <value>Adresse du site ou de la page Internet  invalide</value>
  </data>
  <data name="ResEnterItem#" xml:space="preserve">
    <value>Entrez le numéro d'article</value>
  </data>
  <data name="ResEnterMainSection" xml:space="preserve">
    <value>Entrez dans la section principale</value>
  </data>
  <data name="ResEnterName" xml:space="preserve">
    <value>Entrez le nom</value>
  </data>
  <data name="ResEnterName1" xml:space="preserve">
    <value>Entrez le nom</value>
  </data>
  <data name="ResEnterRemarks" xml:space="preserve">
    <value>Entrez des remarques</value>
  </data>
  <data name="ResEnterRemarks1" xml:space="preserve">
    <value>Entrez des remarques</value>
  </data>
  <data name="ResEnterSection" xml:space="preserve">
    <value>Entrez dans la section</value>
  </data>
  <data name="ResEnterSequence" xml:space="preserve">
    <value>Entrez la séquence</value>
  </data>
  <data name="ResEnterSubSection" xml:space="preserve">
    <value>Entrez dans la sous-section</value>
  </data>
  <data name="ResEnterText" xml:space="preserve">
    <value>Entrez du texte</value>
  </data>
  <data name="resEntertheTexttoAdd" xml:space="preserve">
    <value>Entrez le texte à ajouter</value>
  </data>
  <data name="ResEnterThickness" xml:space="preserve">
    <value>Entrez l'épaisseur</value>
  </data>
  <data name="ResEntervalidquantity" xml:space="preserve">
    <value>Entrez une quantité valide</value>
  </data>
  <data name="ResEnterVendorName" xml:space="preserve">
    <value>Entrez le nom du fournisseur</value>
  </data>
  <data name="ResEntredDuplicatePartNumber" xml:space="preserve">
    <value>Numéro de pièce en double entré</value>
  </data>
  <data name="ResErase" xml:space="preserve">
    <value>Effacer</value>
  </data>
  <data name="ResError" xml:space="preserve">
    <value>Erreur</value>
  </data>
  <data name="ResErrorAttachments" xml:space="preserve">
    <value>Erreur de Dossier Joint</value>
  </data>
  <data name="ResErrorAttachments1" xml:space="preserve">
    <value>Erreur de Dossier Joint</value>
  </data>
  <data name="ResErrorDate&amp;Time" xml:space="preserve">
    <value>Erreur de Date et heure</value>
  </data>
  <data name="ResErrorDate&amp;Time1" xml:space="preserve">
    <value>Erreur de Date et heure</value>
  </data>
  <data name="ResErrorDateTime" xml:space="preserve">
    <value>Erreur Date Heure</value>
  </data>
  <data name="ResErrorDateTime1" xml:space="preserve">
    <value>Erreur Date Heure</value>
  </data>
  <data name="ResErrorDescription" xml:space="preserve">
    <value>Erreur de description</value>
  </data>
  <data name="ResErrorDescription1" xml:space="preserve">
    <value>Erreur de description</value>
  </data>
  <data name="ResErrorinUploadedDataPleaseOpenExcel" xml:space="preserve">
    <value>Erreur dans le fichier téléchargé, veuillez ouvrir Excel</value>
  </data>
  <data name="ResErrorinUploadedPartsPleaseOpenExcel" xml:space="preserve">
    <value>Erreur dans le fichier téléchargé, veuillez ouvrir Excel</value>
  </data>
  <data name="ResErrorinUploadedPartsPleaseOpenImportLog" xml:space="preserve">
    <value>Erreur dans document Excel téléchargé, veuillez ouvrir le journal d'importation</value>
  </data>
  <data name="ResErrorList" xml:space="preserve">
    <value>Liste des erreurs ou Erreur de liste</value>
  </data>
  <data name="ResErrorList1" xml:space="preserve">
    <value>Liste des erreurs ou Erreur de liste</value>
  </data>
  <data name="ResErrorLog" xml:space="preserve">
    <value>Journal des erreurs ou Erreur de journal</value>
  </data>
  <data name="ResErrorLog1" xml:space="preserve">
    <value>Journal des erreurs ou Erreur de journal</value>
  </data>
  <data name="ResErrorLogAuthor" xml:space="preserve">
    <value>Journal des erreurs - Auteur</value>
  </data>
  <data name="ResErrorLogAuthor1" xml:space="preserve">
    <value>Journal des erreurs - Auteur</value>
  </data>
  <data name="ResErrorLogCustomer" xml:space="preserve">
    <value>Journal des erreurs - Client</value>
  </data>
  <data name="ResErrorLogCustomer1" xml:space="preserve">
    <value>Journal des erreurs - Client</value>
  </data>
  <data name="ResErrorLogCustomerDetails" xml:space="preserve">
    <value>Journal des erreurs Détails du client</value>
  </data>
  <data name="ResErrorLogReportDetails" xml:space="preserve">
    <value>Détails du rapport du journal des erreurs</value>
  </data>
  <data name="ResErrorReport" xml:space="preserve">
    <value>Rapport d'erreur</value>
  </data>
  <data name="ResErrorReportList" xml:space="preserve">
    <value>Liste des rapports d'erreurs</value>
  </data>
  <data name="ResErrorType" xml:space="preserve">
    <value>Type d'erreur</value>
  </data>
  <data name="ResErrorType1" xml:space="preserve">
    <value>Type d'erreur</value>
  </data>
  <data name="ResExcel" xml:space="preserve">
    <value>Excel</value>
  </data>
  <data name="ResExclusion" xml:space="preserve">
    <value>Exclusion</value>
  </data>
  <data name="ResExclusionRange-Options" xml:space="preserve">
    <value>Options de plage d'exclusion / 
Options de gamme d'exclusion</value>
  </data>
  <data name="ResExpandAll" xml:space="preserve">
    <value>Développer tout</value>
  </data>
  <data name="ResExport" xml:space="preserve">
    <value>Exportation</value>
  </data>
  <data name="ResExport1" xml:space="preserve">
    <value>Exportation</value>
  </data>
  <data name="ResExport11" xml:space="preserve">
    <value>Exportation</value>
  </data>
  <data name="ResExport2" xml:space="preserve">
    <value>Exportation</value>
  </data>
  <data name="ResExportMedia" xml:space="preserve">
    <value>Exporter les médias</value>
  </data>
  <data name="ResExporttoExcel" xml:space="preserve">
    <value>Exporter vers Excel</value>
  </data>
  <data name="ResExporttoExcel1" xml:space="preserve">
    <value>Exporter vers Excel</value>
  </data>
  <data name="ResExtension1" xml:space="preserve">
    <value>Prolongation1</value>
  </data>
  <data name="ResExtension2" xml:space="preserve">
    <value>Prolongation2</value>
  </data>
  <data name="ResExternal" xml:space="preserve">
    <value>Externe</value>
  </data>
  <data name="ResExternalType" xml:space="preserve">
    <value>Type externe</value>
  </data>
  <data name="ResFailedToSend" xml:space="preserve">
    <value>Échec de l'envoi</value>
  </data>
  <data name="ResFGList" xml:space="preserve">
    <value>Liste des groupes de fonctions</value>
  </data>
  <data name="ResFGList1" xml:space="preserve">
    <value>Liste des groupes de fonctions</value>
  </data>
  <data name="ResFGName" xml:space="preserve">
    <value>Nom FG</value>
  </data>
  <data name="ResFGNameDoesNotExist" xml:space="preserve">
    <value>Le groupe de fonctions n'existe pas</value>
  </data>
  <data name="ResFGNameOrCodeDoesNotExist" xml:space="preserve">
    <value>Le code de groupe de fonctions n'existe pas</value>
  </data>
  <data name="ResFGUsed" xml:space="preserve">
    <value>Groupe de fonctions utilisé</value>
  </data>
  <data name="ResFGUsed1" xml:space="preserve">
    <value>Groupe de fonctions utilisé</value>
  </data>
  <data name="ResFGUsedList" xml:space="preserve">
    <value>Liste des groupes de fonctions</value>
  </data>
  <data name="ResFieldName" xml:space="preserve">
    <value>Nom de domaine</value>
  </data>
  <data name="ResFieldName1" xml:space="preserve">
    <value>Nom de domaine</value>
  </data>
  <data name="ResFile" xml:space="preserve">
    <value>Fichier</value>
  </data>
  <data name="ResFileAlreadyExistsDoYouWantToReplace" xml:space="preserve">
    <value>Le fichier existe déjà Voulez-vous le remplacer</value>
  </data>
  <data name="ResFileExtensionIcon" xml:space="preserve">
    <value>Icône d'extension de fichier</value>
  </data>
  <data name="ResFileName" xml:space="preserve">
    <value>Nom de fichier</value>
  </data>
  <data name="ResFileName1" xml:space="preserve">
    <value>Nom de fichier</value>
  </data>
  <data name="ResFileNameorURL" xml:space="preserve">
    <value>Nom de fichier ou Site Web</value>
  </data>
  <data name="ResFileNameorURL1" xml:space="preserve">
    <value>Nom de fichier ou Site Web</value>
  </data>
  <data name="ResFileSize" xml:space="preserve">
    <value>Taille du fichier</value>
  </data>
  <data name="ResFileType" xml:space="preserve">
    <value>Type de fichier</value>
  </data>
  <data name="ResFileType1" xml:space="preserve">
    <value>Type de fichier</value>
  </data>
  <data name="ResFinalVehicleRecords" xml:space="preserve">
    <value>Dossiers finals du véhicule</value>
  </data>
  <data name="ResFirstName" xml:space="preserve">
    <value>Prénom</value>
  </data>
  <data name="ResFirstName1" xml:space="preserve">
    <value>Prénom</value>
  </data>
  <data name="ResFirstPage" xml:space="preserve">
    <value>Première page</value>
  </data>
  <data name="ResFittowindow" xml:space="preserve">
    <value>Ajuster à la fenêtre</value>
  </data>
  <data name="ResFittowindow1" xml:space="preserve">
    <value>Ajuster à la fenêtre</value>
  </data>
  <data name="ResFitToWindow2" xml:space="preserve">
    <value>Ajuster à la fenêtre</value>
  </data>
  <data name="ResFlag(New)" xml:space="preserve">
    <value>Drapeau (nouveau)?</value>
  </data>
  <data name="ResFlag(New)?" xml:space="preserve">
    <value>Le drapeau est-il (nouveau)?</value>
  </data>
  <data name="ResFlag(No)" xml:space="preserve">
    <value>Drapeau (non)?</value>
  </data>
  <data name="ResFlag(No)?" xml:space="preserve">
    <value>Le drapeau est-il (non)?</value>
  </data>
  <data name="ResFlexi1" xml:space="preserve">
    <value>Flexi 1</value>
  </data>
  <data name="ResFlexi10" xml:space="preserve">
    <value>Flexi 10</value>
  </data>
  <data name="ResFlexi101" xml:space="preserve">
    <value>Flexi 10</value>
  </data>
  <data name="ResFlexi11" xml:space="preserve">
    <value>Flexi 1</value>
  </data>
  <data name="ResFlexi2" xml:space="preserve">
    <value>Flexi 2</value>
  </data>
  <data name="ResFlexi21" xml:space="preserve">
    <value>Flexi 2</value>
  </data>
  <data name="ResFlexi3" xml:space="preserve">
    <value>Flexi 3</value>
  </data>
  <data name="ResFlexi31" xml:space="preserve">
    <value>Flexi 3</value>
  </data>
  <data name="ResFlexi4" xml:space="preserve">
    <value>Flexi 4</value>
  </data>
  <data name="ResFlexi41" xml:space="preserve">
    <value>Flexi 4</value>
  </data>
  <data name="ResFlexi5" xml:space="preserve">
    <value>Flexi 5</value>
  </data>
  <data name="ResFlexi51" xml:space="preserve">
    <value>Flexi 5</value>
  </data>
  <data name="ResFlexi6" xml:space="preserve">
    <value>Flexi 6</value>
  </data>
  <data name="ResFlexi61" xml:space="preserve">
    <value>Flexi 6</value>
  </data>
  <data name="ResFlexi7" xml:space="preserve">
    <value>Flexi 7</value>
  </data>
  <data name="ResFlexi71" xml:space="preserve">
    <value>Flexi 7</value>
  </data>
  <data name="ResFlexi8" xml:space="preserve">
    <value>Flexi 8</value>
  </data>
  <data name="ResFlexi81" xml:space="preserve">
    <value>Flexi 8</value>
  </data>
  <data name="ResFlexi9" xml:space="preserve">
    <value>Flexi 9</value>
  </data>
  <data name="ResFlexi91" xml:space="preserve">
    <value>Flexi 9</value>
  </data>
  <data name="ResFlexiFields" xml:space="preserve">
    <value>Champs Flexi</value>
  </data>
  <data name="ResFont" xml:space="preserve">
    <value>Police de caractère</value>
  </data>
  <data name="ResFont1" xml:space="preserve">
    <value>Police de caractère</value>
  </data>
  <data name="ResFontColor" xml:space="preserve">
    <value>Couleur de la police </value>
  </data>
  <data name="ResFontColor1" xml:space="preserve">
    <value>Couleur de la police </value>
  </data>
  <data name="ResFontColour" xml:space="preserve">
    <value>Couleur de la police </value>
  </data>
  <data name="ResFontColour1" xml:space="preserve">
    <value>Couleur de la police </value>
  </data>
  <data name="ResFontDetails" xml:space="preserve">
    <value>Détails de la police</value>
  </data>
  <data name="ResFontDetails1" xml:space="preserve">
    <value>Détails de la police</value>
  </data>
  <data name="ResFontFontName" xml:space="preserve">
    <value>Nom de la police</value>
  </data>
  <data name="ResFontHistory" xml:space="preserve">
    <value>Historique des polices</value>
  </data>
  <data name="ResFontHistory1" xml:space="preserve">
    <value>Historique des polices</value>
  </data>
  <data name="ResFontHistoryList" xml:space="preserve">
    <value>Historique des polices</value>
  </data>
  <data name="ResFontHistoryList1" xml:space="preserve">
    <value>Historique des polices</value>
  </data>
  <data name="ResFontList" xml:space="preserve">
    <value>Liste des polices</value>
  </data>
  <data name="ResFontList1" xml:space="preserve">
    <value>Liste des polices</value>
  </data>
  <data name="ResFontName" xml:space="preserve">
    <value>Nom de la police</value>
  </data>
  <data name="ResFontName1" xml:space="preserve">
    <value>Nom de la police</value>
  </data>
  <data name="ResFontNameSearchList" xml:space="preserve">
    <value>Liste de recherche par nom de police</value>
  </data>
  <data name="ResFontNameSearchList1" xml:space="preserve">
    <value>Liste de recherche par nom de police</value>
  </data>
  <data name="ResFontPreview" xml:space="preserve">
    <value>Aperçu de la police</value>
  </data>
  <data name="ResFontSize" xml:space="preserve">
    <value>Taille de police</value>
  </data>
  <data name="ResFontSize1" xml:space="preserve">
    <value>Taille de police</value>
  </data>
  <data name="ResFontStyle" xml:space="preserve">
    <value>Style de police</value>
  </data>
  <data name="ResFontStyle1" xml:space="preserve">
    <value>Style de police</value>
  </data>
  <data name="ResFontTemplate" xml:space="preserve">
    <value>Modèle de police</value>
  </data>
  <data name="ResFontTemplate1" xml:space="preserve">
    <value>Modèle de police</value>
  </data>
  <data name="ResFontTemplateName" xml:space="preserve">
    <value>Nom du modèle de police</value>
  </data>
  <data name="ResFontTemplates" xml:space="preserve">
    <value>Modèles de polices</value>
  </data>
  <data name="ResFontTemplates1" xml:space="preserve">
    <value>Modèles de polices</value>
  </data>
  <data name="ResFor" xml:space="preserve">
    <value>Pour</value>
  </data>
  <data name="ResForgotPassword" xml:space="preserve">
    <value>J'ai oublié mon mot de passe</value>
  </data>
  <data name="ResFormatter" xml:space="preserve">
    <value>Formateur</value>
  </data>
  <data name="ResFoundInCatalogue" xml:space="preserve">
    <value>Trouvé dans le catalogue</value>
  </data>
  <data name="ResFoundInCatalogue1" xml:space="preserve">
    <value>Trouvé dans le catalogue</value>
  </data>
  <data name="ResFreeLine" xml:space="preserve">
    <value>Ligne libre</value>
  </data>
  <data name="ResFrench" xml:space="preserve">
    <value>Français</value>
  </data>
  <data name="ResFrenchDescription" xml:space="preserve">
    <value>Description Français</value>
  </data>
  <data name="ResFrenchDocument" xml:space="preserve">
    <value>Chemin du document en français</value>
  </data>
  <data name="ResFrenchLanguage" xml:space="preserve">
    <value>Français</value>
  </data>
  <data name="ResFrenchRemarks" xml:space="preserve">
    <value>Remarques en français</value>
  </data>
  <data name="ResFrequentlyPurchasedParts" xml:space="preserve">
    <value>Pièces fréquemment achetées</value>
  </data>
  <data name="ResFrequentlyPurchaseHistory" xml:space="preserve">
    <value>Historique des achats fréquents</value>
  </data>
  <data name="ResFrom" xml:space="preserve">
    <value>De</value>
  </data>
  <data name="ResFromDate" xml:space="preserve">
    <value>Date d'entrée</value>
  </data>
  <data name="ResFromMFGCode" xml:space="preserve">
    <value>À partir du code de fabrication</value>
  </data>
  <data name="ResFromMFGCode1" xml:space="preserve">
    <value>À partir du code de fabrication</value>
  </data>
  <data name="ResFromPart#" xml:space="preserve">
    <value>À partir du numéro de la pièce</value>
  </data>
  <data name="ResFromPart#1" xml:space="preserve">
    <value>À partir du numéro de la pièce</value>
  </data>
  <data name="ResFromPartNumber" xml:space="preserve">
    <value>À partir du numéro de la pièce</value>
  </data>
  <data name="ResFromPartNumber1" xml:space="preserve">
    <value>À partir du numéro de la pièce</value>
  </data>
  <data name="ResFromQty" xml:space="preserve">
    <value>À partir de la quantité</value>
  </data>
  <data name="ResFromQty1" xml:space="preserve">
    <value>À partir de la quantité</value>
  </data>
  <data name="ResFromSN" xml:space="preserve">
    <value>À partir de</value>
  </data>
  <data name="ResFromSNShortCode" xml:space="preserve">
    <value>À partir de SN Code Court</value>
  </data>
  <data name="ResFromTo" xml:space="preserve">
    <value>De à</value>
  </data>
  <data name="ResFunctionGroup" xml:space="preserve">
    <value>Groupe de fonctions</value>
  </data>
  <data name="ResFunctionGroup1" xml:space="preserve">
    <value>Groupe de fonctions</value>
  </data>
  <data name="ResFunctionGroupAttacments" xml:space="preserve">
    <value>Documents joints au groupe de fonctions</value>
  </data>
  <data name="ResFunctionGroupAttacments1" xml:space="preserve">
    <value>Documents joints au groupe de fonctions</value>
  </data>
  <data name="ResFunctionGroupDetails" xml:space="preserve">
    <value>Détails de groupe de fonctions</value>
  </data>
  <data name="ResFunctionGroupImages" xml:space="preserve">
    <value>Images du groupe de fonctions</value>
  </data>
  <data name="ResFunctionGroupLanguageList" xml:space="preserve">
    <value>Liste des langues du groupe de fonctions</value>
  </data>
  <data name="ResFunctionGroupList" xml:space="preserve">
    <value>Liste des groupes de fonctions</value>
  </data>
  <data name="ResFunctionGroupList1" xml:space="preserve">
    <value>Liste des groupes de fonctions</value>
  </data>
  <data name="ResFunctionGroups" xml:space="preserve">
    <value>Groupes de fonctions</value>
  </data>
  <data name="ResFunctionGroupUsed" xml:space="preserve">
    <value>Groupe de fonctions utilisé</value>
  </data>
  <data name="ResFunctionGroupUsedList" xml:space="preserve">
    <value>Liste des groupes de fonctions utilisés</value>
  </data>
  <data name="ResFunctionGroupXML" xml:space="preserve">
    <value>Groupe_de_fonctions</value>
  </data>
  <data name="ResGenerate" xml:space="preserve">
    <value>Produire</value>
  </data>
  <data name="ResGenerateReport" xml:space="preserve">
    <value>Générer un rapport</value>
  </data>
  <data name="ResGermanDocument" xml:space="preserve">
    <value>Document Allemand</value>
  </data>
  <data name="ResGivenNumberAlreadyAvailableInParts" xml:space="preserve">
    <value>Le numéro de pièce existe déjà</value>
  </data>
  <data name="ResGreaterorEqualto" xml:space="preserve">
    <value>Supérieur ou égal à</value>
  </data>
  <data name="ResGreaterThan" xml:space="preserve">
    <value>Plus grand que</value>
  </data>
  <data name="ResGreaterThan1" xml:space="preserve">
    <value>Plus grand que</value>
  </data>
  <data name="ResGreaterThanorEqualto" xml:space="preserve">
    <value>Plus grand ou égal à</value>
  </data>
  <data name="ResGreaterThanorEqualto1" xml:space="preserve">
    <value>Plus grand ou égal à</value>
  </data>
  <data name="ResGreaterThen" xml:space="preserve">
    <value>Plus grand que</value>
  </data>
  <data name="ResGUIConfig" xml:space="preserve">
    <value>GUI de configuration</value>
  </data>
  <data name="ResGUIConfig_ID" xml:space="preserve">
    <value>Identification  de configuration de l'interface utilisateur graphique</value>
  </data>
  <data name="ResHasImageAttachments" xml:space="preserve">
    <value>A des documents joints d'image</value>
  </data>
  <data name="ResHasImageAttachments1" xml:space="preserve">
    <value>A des documents joints d'image</value>
  </data>
  <data name="ResHasPartAttachments" xml:space="preserve">
    <value>A des documents joints de pièces</value>
  </data>
  <data name="ResHasPartAttachments1" xml:space="preserve">
    <value>A des documents joints de pièces</value>
  </data>
  <data name="ResHasRecommendedPart" xml:space="preserve">
    <value>A une pièce recommandée</value>
  </data>
  <data name="ResHasRecommendedPart1" xml:space="preserve">
    <value>A une pièce recommandée</value>
  </data>
  <data name="ResHasSetMemberParts" xml:space="preserve">
    <value>A une pièce Membre Défini</value>
  </data>
  <data name="ResHasSetMemberParts1" xml:space="preserve">
    <value>A une pièce Membre Défini</value>
  </data>
  <data name="ResHasSupersessionornot" xml:space="preserve">
    <value>A un remplacement ou non</value>
  </data>
  <data name="ResHasSupersessionornot1" xml:space="preserve">
    <value>A un remplacement ou non</value>
  </data>
  <data name="ResHasVersion" xml:space="preserve">
    <value>A la version?</value>
  </data>
  <data name="ResHello" xml:space="preserve">
    <value>Bonjour</value>
  </data>
  <data name="ResHexagon" xml:space="preserve">
    <value>Hexagone</value>
  </data>
  <data name="ResHideAssemblyNo" xml:space="preserve">
    <value>Masquer le numéro d'assemblage</value>
  </data>
  <data name="ResHideItem#" xml:space="preserve">
    <value>Masquer le numéro d'article</value>
  </data>
  <data name="ResHighlighter" xml:space="preserve">
    <value>Mettre l'image en surbrillance</value>
  </data>
  <data name="ResHighlighter1" xml:space="preserve">
    <value>Surligneur</value>
  </data>
  <data name="ResHistory" xml:space="preserve">
    <value>Historique</value>
  </data>
  <data name="ResHistory1" xml:space="preserve">
    <value>Historique</value>
  </data>
  <data name="ResHome" xml:space="preserve">
    <value>Accueil</value>
  </data>
  <data name="ResHorizontalSplitter" xml:space="preserve">
    <value>Répartiteur horizontal</value>
  </data>
  <data name="ResHorizontalSplitter1" xml:space="preserve">
    <value>Répartiteur horizontal</value>
  </data>
  <data name="ResHotspot" xml:space="preserve">
    <value>Hotspot / Point chaud / point d'accès</value>
  </data>
  <data name="ResHotspot1" xml:space="preserve">
    <value>Hotspot</value>
  </data>
  <data name="ResHotspot?" xml:space="preserve">
    <value>Est-ce que Hotspot?</value>
  </data>
  <data name="ResHotspot?1" xml:space="preserve">
    <value>Est-ce que Hotspot?</value>
  </data>
  <data name="ResHotspotDelete" xml:space="preserve">
    <value>Supprimer le Hotspot </value>
  </data>
  <data name="ResHotspotDelete1" xml:space="preserve">
    <value>Supprimer le Hotspot </value>
  </data>
  <data name="ResHotspotdone" xml:space="preserve">
    <value>Hotspot fait</value>
  </data>
  <data name="ResHotspotdone1" xml:space="preserve">
    <value>Hotspot fait</value>
  </data>
  <data name="ResHotSpotsCopy" xml:space="preserve">
    <value>Copier le Hotspot</value>
  </data>
  <data name="ResHotspotShapes" xml:space="preserve">
    <value>Formes de Hotspots</value>
  </data>
  <data name="ResHotspotShapes1" xml:space="preserve">
    <value>Formes de Hotspots</value>
  </data>
  <data name="ResHotSpotsMove" xml:space="preserve">
    <value>Déplacer le Hotspot</value>
  </data>
  <data name="ResHotSpotsonoff" xml:space="preserve">
    <value>Hotspots [Activé / Désactivé]</value>
  </data>
  <data name="ResID" xml:space="preserve">
    <value>Identifiant</value>
  </data>
  <data name="ResIgnoreAllError" xml:space="preserve">
    <value>Ignorer toutes les erreurs</value>
  </data>
  <data name="ResIgnoredBy" xml:space="preserve">
    <value>Ignoré par</value>
  </data>
  <data name="ResIgnoredDateTime" xml:space="preserve">
    <value>Date ignorée</value>
  </data>
  <data name="ResIgnoreErrorAndPublish" xml:space="preserve">
    <value>Ignorer et publier</value>
  </data>
  <data name="ResIgnoreforPartsCatalogue" xml:space="preserve">
    <value>Ignorer pour le catalogue de pièces</value>
  </data>
  <data name="ResIgnoreforPartsCatalogue1" xml:space="preserve">
    <value>Ignorer pour le catalogue de pièces</value>
  </data>
  <data name="ResIgnoreforServiceManuals" xml:space="preserve">
    <value>Ignorer pour les manuels d'entretien</value>
  </data>
  <data name="ResIgnoreforServiceManuals1" xml:space="preserve">
    <value>Ignorer pour les manuels d'entretien</value>
  </data>
  <data name="ResIhopethattheinformationsuppliedisadequateIfIcanbeofanyfurtherhelporhavenotmademyselfclearpleasedonthesitatetocommunicatewithmeatyourconvenience" xml:space="preserve">
    <value>Dans le cas ou vous auriez besoin d'aide, communiquez avec moi par</value>
  </data>
  <data name="ResImage" xml:space="preserve">
    <value>Image</value>
  </data>
  <data name="ResImg" xml:space="preserve">
    <value>Img</value>
  </data>
  <data name="ResImg1" xml:space="preserve">
    <value>Img</value>
  </data>
  <data name="ResImport" xml:space="preserve">
    <value>Importer</value>
  </data>
  <data name="ResImport1" xml:space="preserve">
    <value>Importer</value>
  </data>
  <data name="ResImportAllCustomer" xml:space="preserve">
    <value>Importer tous les clients</value>
  </data>
  <data name="ResImportAllOrder" xml:space="preserve">
    <value>Importer toute la commande</value>
  </data>
  <data name="ResImportAssembly&amp;Parts" xml:space="preserve">
    <value>Importer l'assemblage et les pièces</value>
  </data>
  <data name="ResImportAssemblyFile" xml:space="preserve">
    <value>Importer un fichier d'assemblage</value>
  </data>
  <data name="ResImportAssemblyFile1" xml:space="preserve">
    <value>Importer un fichier d'assemblage</value>
  </data>
  <data name="ResImportAssemblyParts" xml:space="preserve">
    <value>Importer l'assemblage les pièces</value>
  </data>
  <data name="ResImportBOM" xml:space="preserve">
    <value>Importer une nomenclature</value>
  </data>
  <data name="ResImportBOMfromExcel" xml:space="preserve">
    <value>Importer une nomenclature depuis Excel</value>
  </data>
  <data name="ResImportBOMfromExcel1" xml:space="preserve">
    <value>Importer une nomenclature depuis Excel</value>
  </data>
  <data name="ResImportCustomerPartsFile" xml:space="preserve">
    <value>Importer le fichier de pièce client</value>
  </data>
  <data name="ResImportCustomerPartsFile1" xml:space="preserve">
    <value>Importer le fichier de pièce client</value>
  </data>
  <data name="ResImportCustomerPartsInfo" xml:space="preserve">
    <value>Importer les informations sur les pièces client</value>
  </data>
  <data name="ResImportCustomerPartsInformation" xml:space="preserve">
    <value>Importer les informations sur les pièces client</value>
  </data>
  <data name="ResimportDrawing" xml:space="preserve">
    <value>Importer un dessin</value>
  </data>
  <data name="ResimportDrawing1" xml:space="preserve">
    <value>Importer un dessin</value>
  </data>
  <data name="ResImportedBy" xml:space="preserve">
    <value>Importé par</value>
  </data>
  <data name="ResImportedBy1" xml:space="preserve">
    <value>Importé par</value>
  </data>
  <data name="ResImportedCount" xml:space="preserve">
    <value>Nombre importé</value>
  </data>
  <data name="ResImportedCount1" xml:space="preserve">
    <value>Nombre importé</value>
  </data>
  <data name="ResImportedFileName" xml:space="preserve">
    <value>Nom du fichier importé</value>
  </data>
  <data name="ResImportFile" xml:space="preserve">
    <value>Importer le fichier</value>
  </data>
  <data name="ResImportFile1" xml:space="preserve">
    <value>Importer le fichier</value>
  </data>
  <data name="ResImportFileFormat" xml:space="preserve">
    <value>Importer le format de fichier</value>
  </data>
  <data name="ResImportFileFormat1" xml:space="preserve">
    <value>Importer le format de fichier</value>
  </data>
  <data name="ResImportFromSN" xml:space="preserve">
    <value>DeSN</value>
  </data>
  <data name="ResImportLog" xml:space="preserve">
    <value>Importer le journal</value>
  </data>
  <data name="ResImportLog1" xml:space="preserve">
    <value>Importer le journal</value>
  </data>
  <data name="ResImportMaskingCode" xml:space="preserve">
    <value>Code de masquage</value>
  </data>
  <data name="ResImportNewRoad#" xml:space="preserve">
    <value>Importer le numéro de route</value>
  </data>
  <data name="ResImportOptions" xml:space="preserve">
    <value>Importer Options</value>
  </data>
  <data name="ResImportPartsFile" xml:space="preserve">
    <value>Importer un fichier de pièces</value>
  </data>
  <data name="ResImportPartsFile1" xml:space="preserve">
    <value>Importer un fichier de pièces</value>
  </data>
  <data name="ResImportS upersession" xml:space="preserve">
    <value />
  </data>
  <data name="ResImportSectionFile" xml:space="preserve">
    <value>Importer un fichier de section</value>
  </data>
  <data name="ResImportSectionFile1" xml:space="preserve">
    <value>Importer un fichier de section</value>
  </data>
  <data name="ResImportSectionfromExcel" xml:space="preserve">
    <value>Importer une section depuis Excel</value>
  </data>
  <data name="ResImportSectionfromExcel1" xml:space="preserve">
    <value>Importer une section depuis Excel</value>
  </data>
  <data name="ResImportSections" xml:space="preserve">
    <value>Importer des sections</value>
  </data>
  <data name="ResImportSectionsfromExcel" xml:space="preserve">
    <value>Importer des sections d'Excel</value>
  </data>
  <data name="ResImportSectionsfromExcel1" xml:space="preserve">
    <value>Importer des sections d'Excel</value>
  </data>
  <data name="ResImportShoppingCart" xml:space="preserve">
    <value>Importer le panier</value>
  </data>
  <data name="ResImportSupersession" xml:space="preserve">
    <value>Importer le remplacement</value>
  </data>
  <data name="ResImportSupersessionPartFile" xml:space="preserve">
    <value>Importer le fichier de pièces de remplacement</value>
  </data>
  <data name="ResImportSupersessionPartFile1" xml:space="preserve">
    <value>Importer le fichier de pièces de remplacement</value>
  </data>
  <data name="ResImportTemplate" xml:space="preserve">
    <value>Importer un modèle</value>
  </data>
  <data name="ResImportTemplate1" xml:space="preserve">
    <value>Importer un modèle</value>
  </data>
  <data name="ResImportUpToSN" xml:space="preserve">
    <value>Jusqu'àSN</value>
  </data>
  <data name="ResImportVARNETFile" xml:space="preserve">
    <value>Importer un fichier Varnet</value>
  </data>
  <data name="ResImportVendorPartsFile" xml:space="preserve">
    <value>Importer le fichier de pièces du fournisseur</value>
  </data>
  <data name="ResImportVendorPartsFile1" xml:space="preserve">
    <value>Importer le fichier de pièces du fournisseur</value>
  </data>
  <data name="ResImportVendorPartsInfo" xml:space="preserve">
    <value>Importer les informations sur les pièces du fournisseur</value>
  </data>
  <data name="ResImportVendorPartsInformation" xml:space="preserve">
    <value>Importer les informations sur les pièces du fournisseur</value>
  </data>
  <data name="ResImportVIN" xml:space="preserve">
    <value>Importer le numéro d'identification du véhicule</value>
  </data>
  <data name="ResImportVIN#" xml:space="preserve">
    <value>Importer le numéro d'identification du véhicule</value>
  </data>
  <data name="ResImportVIN#1" xml:space="preserve">
    <value>Importer le numéro d'identification du véhicule</value>
  </data>
  <data name="ResImportVINDetailsTemplate" xml:space="preserve">
    <value>Importer le modèle de détails du numéro d'identification du véhicule</value>
  </data>
  <data name="ResImportVINDetailsTemplate1" xml:space="preserve">
    <value>Importer le modèle de détails du numéro d'identification du véhicule</value>
  </data>
  <data name="ResImportVINfromExcel" xml:space="preserve">
    <value>Importer le numéro d'identification du véhicule depuis Excel</value>
  </data>
  <data name="ResInavlidModelName" xml:space="preserve">
    <value>Nom de modèle non valide</value>
  </data>
  <data name="ResIncludeetmembersinPartList" xml:space="preserve">
    <value>Inclure les membres définis dans la liste de pièces?</value>
  </data>
  <data name="ResIncludeetmembersinPartList?" xml:space="preserve">
    <value>Inclure les membres définis dans la liste de pièces?</value>
  </data>
  <data name="ResIncludeInBOM" xml:space="preserve">
    <value>Inclure dans la liste de pièces</value>
  </data>
  <data name="ResIncludeSetmemberInPartList" xml:space="preserve">
    <value>Définir les pièces dans la nomenclature?</value>
  </data>
  <data name="ResInclusion" xml:space="preserve">
    <value>Inclusion</value>
  </data>
  <data name="ResInclusionRange-Options" xml:space="preserve">
    <value>Plage d'inclusion -Options</value>
  </data>
  <data name="ResIndent" xml:space="preserve">
    <value>Retrait</value>
  </data>
  <data name="ResIndent1" xml:space="preserve">
    <value>Retrait</value>
  </data>
  <data name="ResInProgress" xml:space="preserve">
    <value>En cours</value>
  </data>
  <data name="ResInProgress1" xml:space="preserve">
    <value>En cours</value>
  </data>
  <data name="ResInputisNotInCorrectFormat" xml:space="preserve">
    <value>La section n'est pas au format correct</value>
  </data>
  <data name="ResInspectionandTestPlan" xml:space="preserve">
    <value>Inspection et plan de test</value>
  </data>
  <data name="ResInternal" xml:space="preserve">
    <value>Interne</value>
  </data>
  <data name="ResInternal1" xml:space="preserve">
    <value>Interne</value>
  </data>
  <data name="ResInternalAttachments" xml:space="preserve">
    <value>Documents joints internes</value>
  </data>
  <data name="ResInternalAttachments1" xml:space="preserve">
    <value>Documents joints internes</value>
  </data>
  <data name="ResInternalNo" xml:space="preserve">
    <value>Non interne</value>
  </data>
  <data name="ResInvalidFile" xml:space="preserve">
    <value>Fichier invalide</value>
  </data>
  <data name="ResInvalidFileType" xml:space="preserve">
    <value>Type de fichier invalide</value>
  </data>
  <data name="ResInvalidOption" xml:space="preserve">
    <value>Option invalide</value>
  </data>
  <data name="ResInvalidVIN#" xml:space="preserve">
    <value>Numéro d'identification du véhicule  invalide</value>
  </data>
  <data name="ResIsActive?" xml:space="preserve">
    <value>Est Actif?</value>
  </data>
  <data name="ResIsActive?1" xml:space="preserve">
    <value>Est Actif?</value>
  </data>
  <data name="ResIsanSubAssembly" xml:space="preserve">
    <value>Est un sous-assemblage</value>
  </data>
  <data name="ResIsanSubAssembly1" xml:space="preserve">
    <value>Est un sous-assemblage</value>
  </data>
  <data name="ResIsAss?" xml:space="preserve">
    <value>Est assemblé?</value>
  </data>
  <data name="ResIsAss?1" xml:space="preserve">
    <value>Est assemblé?</value>
  </data>
  <data name="ResIsAssembly" xml:space="preserve">
    <value>Est assemblé?</value>
  </data>
  <data name="ResIsAssembly?" xml:space="preserve">
    <value>Est assemblé?</value>
  </data>
  <data name="ResIsAssembly?1" xml:space="preserve">
    <value>Est assemblé?</value>
  </data>
  <data name="ResIsAssy" xml:space="preserve">
    <value>Est assemblé?</value>
  </data>
  <data name="ResIsBookmarked" xml:space="preserve">
    <value>Est-il ajouté aux favoris?</value>
  </data>
  <data name="ResIsBookmarked1" xml:space="preserve">
    <value>Est-il ajouté aux favoris?</value>
  </data>
  <data name="ResIsBookMarked?" xml:space="preserve">
    <value>Est-il ajouté aux favoris?</value>
  </data>
  <data name="ResIsBookMarked?1" xml:space="preserve">
    <value>Est-il ajouté aux favoris?</value>
  </data>
  <data name="ResIsBookmarkForSection?" xml:space="preserve">
    <value>Le signet est-il pour la section?</value>
  </data>
  <data name="ResIsBookmarkForSection?1" xml:space="preserve">
    <value>Le signet est-il pour la section?</value>
  </data>
  <data name="ResIsConfigVisiable" xml:space="preserve">
    <value>Est visible</value>
  </data>
  <data name="ResIsDefault?" xml:space="preserve">
    <value>Est-ce par défaut?</value>
  </data>
  <data name="ResIsDefault?1" xml:space="preserve">
    <value>Est-ce par défaut?</value>
  </data>
  <data name="ResIsEmailNotification" xml:space="preserve">
    <value>est la notification par e-mail ?</value>
  </data>
  <data name="ResIsEmpty" xml:space="preserve">
    <value>Est vide</value>
  </data>
  <data name="ResIsEmpty1" xml:space="preserve">
    <value>Est vide</value>
  </data>
  <data name="ResIsEqual" xml:space="preserve">
    <value>Est égal</value>
  </data>
  <data name="ResIsEqual1" xml:space="preserve">
    <value>Est égal</value>
  </data>
  <data name="ResIsHotspot?" xml:space="preserve">
    <value>Est-ce que Hotspot?</value>
  </data>
  <data name="ResIsLock" xml:space="preserve">
    <value>Est Fermé?</value>
  </data>
  <data name="ResIsLock?" xml:space="preserve">
    <value>Est Fermé?</value>
  </data>
  <data name="ResIsLock?1" xml:space="preserve">
    <value>Est Fermé?</value>
  </data>
  <data name="ResIsLocked?" xml:space="preserve">
    <value>Est verrouillé?</value>
  </data>
  <data name="ResIsLocked?1" xml:space="preserve">
    <value>Est verrouillé?</value>
  </data>
  <data name="ResIsMandatory" xml:space="preserve">
    <value>Est obligatoire?</value>
  </data>
  <data name="ResIsPartSection?" xml:space="preserve">
    <value>Est dans la section des pièces?</value>
  </data>
  <data name="ResIsPartSection?1" xml:space="preserve">
    <value>Est dans la section des pièces?</value>
  </data>
  <data name="ResIsPrecedingPart?" xml:space="preserve">
    <value>Est la pièce précédente?</value>
  </data>
  <data name="ResIsPrecedingPart?1" xml:space="preserve">
    <value>Est la pièce précédente?</value>
  </data>
  <data name="ResIsPrimary?" xml:space="preserve">
    <value>Est primaire?</value>
  </data>
  <data name="ResIsPrimary?1" xml:space="preserve">
    <value>Est primaire?</value>
  </data>
  <data name="ResIsPublished?" xml:space="preserve">
    <value>Est publié?</value>
  </data>
  <data name="ResIsPublished?1" xml:space="preserve">
    <value>Est publié?</value>
  </data>
  <data name="ResIsPurchasable" xml:space="preserve">
    <value>Est Achetable?</value>
  </data>
  <data name="ResIsPurchasable?" xml:space="preserve">
    <value>Est Achetable?</value>
  </data>
  <data name="ResIsPurchasable?1" xml:space="preserve">
    <value>Est Achetable?</value>
  </data>
  <data name="ResIsRecommendedPart" xml:space="preserve">
    <value>A une pièce recommandée?</value>
  </data>
  <data name="ResIsRecommendedPart?" xml:space="preserve">
    <value>A une pièce recommandée?</value>
  </data>
  <data name="ResIsRecommendedPart?1" xml:space="preserve">
    <value>A une pièce recommandée?</value>
  </data>
  <data name="ResIsReferred?" xml:space="preserve">
    <value>Est référé?</value>
  </data>
  <data name="ResIsReferred?1" xml:space="preserve">
    <value>Est référé?</value>
  </data>
  <data name="ResIsSection" xml:space="preserve">
    <value>Est la section?</value>
  </data>
  <data name="ResIsSection?" xml:space="preserve">
    <value>Est la section?</value>
  </data>
  <data name="ResIsSection?1" xml:space="preserve">
    <value>Est la section principale?</value>
  </data>
  <data name="ResIsSendNotification" xml:space="preserve">
    <value>Envoyer une notification</value>
  </data>
  <data name="ResIsServiceSection" xml:space="preserve">
    <value>Est la Section de service</value>
  </data>
  <data name="ResIsServiceSection?" xml:space="preserve">
    <value>Est la section de service?</value>
  </data>
  <data name="ResIsServiceSection?1" xml:space="preserve">
    <value>Est la section de service?</value>
  </data>
  <data name="ResIsSetMember" xml:space="preserve">
    <value>A membre défini?</value>
  </data>
  <data name="ResIsSetMember?" xml:space="preserve">
    <value>A membre défini?</value>
  </data>
  <data name="ResIsSetMember?1" xml:space="preserve">
    <value>A membre défini?</value>
  </data>
  <data name="ResIsSubSection" xml:space="preserve">
    <value>Est la sous-section?</value>
  </data>
  <data name="ResIsSupersedingPart?" xml:space="preserve">
    <value>Est la pièce de remplacement?</value>
  </data>
  <data name="ResIsSupersedingPart?1" xml:space="preserve">
    <value>Est la pièce de remplacement?</value>
  </data>
  <data name="ResIsSuperseededPart?" xml:space="preserve">
    <value>Est une pièce remplacée?</value>
  </data>
  <data name="ResIsSupersession?" xml:space="preserve">
    <value>Est le remplacement?</value>
  </data>
  <data name="ResIsSupersession?1" xml:space="preserve">
    <value>Est le remplacement?</value>
  </data>
  <data name="ResIsSupersession?11" xml:space="preserve">
    <value>Est le remplacement?</value>
  </data>
  <data name="ResIsSupersession?2" xml:space="preserve">
    <value>Est le remplacement?</value>
  </data>
  <data name="ResIsTopLevel" xml:space="preserve">
    <value>Est-ce de niveau supérieur?</value>
  </data>
  <data name="ResIsTopLevel?" xml:space="preserve">
    <value>Est-ce de niveau supérieur?</value>
  </data>
  <data name="ResIsTopLevel?1" xml:space="preserve">
    <value>Est-ce de niveau supérieur?</value>
  </data>
  <data name="ResIsURL" xml:space="preserve">
    <value>Est-ce que l'adresse internet?</value>
  </data>
  <data name="ResIsVisiable?" xml:space="preserve">
    <value>Est visible?</value>
  </data>
  <data name="ResIsVisible" xml:space="preserve">
    <value>Est visible?</value>
  </data>
  <data name="ResIsVisible?" xml:space="preserve">
    <value>Est visible?</value>
  </data>
  <data name="ResIsVisible?1" xml:space="preserve">
    <value>Est visible?</value>
  </data>
  <data name="ResIs_PartDuplicate" xml:space="preserve">
    <value>La pièce est-elle en double?</value>
  </data>
  <data name="ResIs_Supersession?" xml:space="preserve">
    <value>Est le remplacement?</value>
  </data>
  <data name="ResItem" xml:space="preserve">
    <value>Numéro d'article</value>
  </data>
  <data name="ResItem#" xml:space="preserve">
    <value>Numéro d'article</value>
  </data>
  <data name="ResItem#1" xml:space="preserve">
    <value>Numéro d'article</value>
  </data>
  <data name="ResItem1" xml:space="preserve">
    <value>Numéro d'article</value>
  </data>
  <data name="ResItemNumber" xml:space="preserve">
    <value>Numéro d'article</value>
  </data>
  <data name="ResLanguage" xml:space="preserve">
    <value>Langue</value>
  </data>
  <data name="ResLanguage1" xml:space="preserve">
    <value>Langue</value>
  </data>
  <data name="ResLanguageCode" xml:space="preserve">
    <value>Code de langue</value>
  </data>
  <data name="ResLanguageCode1" xml:space="preserve">
    <value>Code de langue</value>
  </data>
  <data name="ResLanguageList" xml:space="preserve">
    <value>Liste des langues</value>
  </data>
  <data name="ResLanguageList1" xml:space="preserve">
    <value>Liste des langues</value>
  </data>
  <data name="ResLanguageName" xml:space="preserve">
    <value>Nom de la langue</value>
  </data>
  <data name="ResLanguageName1" xml:space="preserve">
    <value>Nom de la langue</value>
  </data>
  <data name="ResLanguageNameSearchList" xml:space="preserve">
    <value>Liste de recherche par nom de langue</value>
  </data>
  <data name="ResLanguageNameSearchList1" xml:space="preserve">
    <value>Liste de recherche par nom de langue</value>
  </data>
  <data name="ResLastModifiedDate" xml:space="preserve">
    <value>Date de la dernière modification</value>
  </data>
  <data name="ResLastModifiedDate1" xml:space="preserve">
    <value>Date de la dernière modification</value>
  </data>
  <data name="ResLastName" xml:space="preserve">
    <value>Nom de famille</value>
  </data>
  <data name="ResLastName1" xml:space="preserve">
    <value>Nom de famille</value>
  </data>
  <data name="ResLastPage" xml:space="preserve">
    <value>Dernière page</value>
  </data>
  <data name="ResLastPurchase" xml:space="preserve">
    <value>Dernière acquisition</value>
  </data>
  <data name="ResLastUpdatedBy" xml:space="preserve">
    <value>Dernière mise à jour par</value>
  </data>
  <data name="ResLastUpdatedBy1" xml:space="preserve">
    <value>Dernière mise à jour par</value>
  </data>
  <data name="ResLastupdateddateLog" xml:space="preserve">
    <value>Date de la dernière mise à jour Journal</value>
  </data>
  <data name="ResLastupdateddateLog1" xml:space="preserve">
    <value>Date de la dernière mise à jour Journal</value>
  </data>
  <data name="ResLatestVersion" xml:space="preserve">
    <value>Dernière version</value>
  </data>
  <data name="ResLess" xml:space="preserve">
    <value>Montrer moins</value>
  </data>
  <data name="ResLess1" xml:space="preserve">
    <value>Moins</value>
  </data>
  <data name="ResLessorEqualto" xml:space="preserve">
    <value>Inférieur ou égal à</value>
  </data>
  <data name="ResLessThan" xml:space="preserve">
    <value>Moins que</value>
  </data>
  <data name="ResLessThan1" xml:space="preserve">
    <value>Moins que</value>
  </data>
  <data name="ResLessthanorEqualto" xml:space="preserve">
    <value>Inférieur ou égal à</value>
  </data>
  <data name="ResLessthanorEqualto1" xml:space="preserve">
    <value>Inférieur ou égal à</value>
  </data>
  <data name="ResLevel" xml:space="preserve">
    <value>Niveau</value>
  </data>
  <data name="ResLevel1" xml:space="preserve">
    <value>Niveau</value>
  </data>
  <data name="ResLineThickness" xml:space="preserve">
    <value>Épaisseur de ligne</value>
  </data>
  <data name="ResLinkHotSpots" xml:space="preserve">
    <value>Lier les hotspots</value>
  </data>
  <data name="ResLinkingHotspots" xml:space="preserve">
    <value>Liaison des hotspots</value>
  </data>
  <data name="ResList" xml:space="preserve">
    <value>Liste</value>
  </data>
  <data name="ResList1" xml:space="preserve">
    <value>Liste</value>
  </data>
  <data name="ResListAnnotation" xml:space="preserve">
    <value>Annotation de liste</value>
  </data>
  <data name="ResListOfAssemblies" xml:space="preserve">
    <value>Liste des assemblages</value>
  </data>
  <data name="ResListOfAssemblies1" xml:space="preserve">
    <value>Liste des assemblages</value>
  </data>
  <data name="ResListofOptions" xml:space="preserve">
    <value>Liste des options</value>
  </data>
  <data name="ResListofOptions1" xml:space="preserve">
    <value>Liste des options</value>
  </data>
  <data name="ResListOfPartAssemblies" xml:space="preserve">
    <value>Liste des assemblages de pièces</value>
  </data>
  <data name="ResListofParts" xml:space="preserve">
    <value>Liste des pièces</value>
  </data>
  <data name="ResListofParts1" xml:space="preserve">
    <value>Liste des pièces</value>
  </data>
  <data name="ResLoading" xml:space="preserve">
    <value>Chargement...</value>
  </data>
  <data name="ResLoadPartsFrom" xml:space="preserve">
    <value>Charger des pièces depuis</value>
  </data>
  <data name="ResLocalCodeNotFound" xml:space="preserve">
    <value>Code local introuvable</value>
  </data>
  <data name="ResLocale" xml:space="preserve">
    <value>Lieu</value>
  </data>
  <data name="ResLocalGlobalRemarks" xml:space="preserve">
    <value>Remarques locales / globales</value>
  </data>
  <data name="ResLocalNote" xml:space="preserve">
    <value>Note locale</value>
  </data>
  <data name="ResLocalNote1" xml:space="preserve">
    <value>Note locale</value>
  </data>
  <data name="ResLocalNoteCode" xml:space="preserve">
    <value>Code de note local</value>
  </data>
  <data name="ResLocalNoteDetails" xml:space="preserve">
    <value>Détails de la note locale</value>
  </data>
  <data name="ResLocalNoteList" xml:space="preserve">
    <value>Liste des notes locales</value>
  </data>
  <data name="ResLocalNotes" xml:space="preserve">
    <value>Notes locales</value>
  </data>
  <data name="ResLocalNotes1" xml:space="preserve">
    <value>Notes locales</value>
  </data>
  <data name="ResLocalNotesDetails" xml:space="preserve">
    <value>Détails de la note locale</value>
  </data>
  <data name="ResLocalRemarks" xml:space="preserve">
    <value>Remarques Note locale</value>
  </data>
  <data name="ResLocalRemarksCopiedSuccessfully" xml:space="preserve">
    <value>Remarques locales copiées avec succès</value>
  </data>
  <data name="ResLocalRemarksUpdation" xml:space="preserve">
    <value>Mise à jour des remarques locales</value>
  </data>
  <data name="ResLocation" xml:space="preserve">
    <value>Emplacement</value>
  </data>
  <data name="ResLock" xml:space="preserve">
    <value>Verrouiller</value>
  </data>
  <data name="ResLock1" xml:space="preserve">
    <value>Verrouiller</value>
  </data>
  <data name="ResLockedBy" xml:space="preserve">
    <value>Verrouillé par</value>
  </data>
  <data name="ResLockedBy1" xml:space="preserve">
    <value>Verrouillé par</value>
  </data>
  <data name="ResLockedDate" xml:space="preserve">
    <value>Date verrouillée ou Date Verrouillage</value>
  </data>
  <data name="ResLockedDate1" xml:space="preserve">
    <value>Date verrouillée ou Date Verrouillage</value>
  </data>
  <data name="ResLockList" xml:space="preserve">
    <value>Verrouiller la liste</value>
  </data>
  <data name="ResLockList1" xml:space="preserve">
    <value>Verrouiller la liste</value>
  </data>
  <data name="ResLockLog" xml:space="preserve">
    <value>Verrouiller le journal</value>
  </data>
  <data name="ResLockLog1" xml:space="preserve">
    <value>Verrouiller le journal</value>
  </data>
  <data name="ResLockLogDetails" xml:space="preserve">
    <value>Détails du journal de verrouillage</value>
  </data>
  <data name="ResLockUnlock" xml:space="preserve">
    <value>Verrouiller / Déverrouiller</value>
  </data>
  <data name="ResLockUnlock1" xml:space="preserve">
    <value>Verrouiller / Déverrouiller</value>
  </data>
  <data name="ResLockUnlockLog" xml:space="preserve">
    <value>Verrouiller / Déverrouiller le journal</value>
  </data>
  <data name="ResLockUnlockLog1" xml:space="preserve">
    <value>Verrouiller / Déverrouiller le journal</value>
  </data>
  <data name="ResLogin" xml:space="preserve">
    <value>Connexion</value>
  </data>
  <data name="ResLoginID" xml:space="preserve">
    <value>Identifiant de connexion / E-mail - Identifiant</value>
  </data>
  <data name="ResLoginID1" xml:space="preserve">
    <value>Identifiant de connexion</value>
  </data>
  <data name="ResLogout" xml:space="preserve">
    <value>Se déconnecter</value>
  </data>
  <data name="ResMachedCodes" xml:space="preserve">
    <value>Codes appariés</value>
  </data>
  <data name="ResMachedCodes1" xml:space="preserve">
    <value>Codes appariés</value>
  </data>
  <data name="ResMainSection" xml:space="preserve">
    <value>Section principale</value>
  </data>
  <data name="ResMainSectionDetails" xml:space="preserve">
    <value>Détails de Section principale</value>
  </data>
  <data name="ResMainSectionLanguageList" xml:space="preserve">
    <value>Liste des langues de la section principale</value>
  </data>
  <data name="ResMainSectionList" xml:space="preserve">
    <value>Liste des sections principales</value>
  </data>
  <data name="ResMaintenanceManuals" xml:space="preserve">
    <value>Manuels d'entretien</value>
  </data>
  <data name="ResManageBookmarks" xml:space="preserve">
    <value>Gérer les signet</value>
  </data>
  <data name="ResManageBookmarks1" xml:space="preserve">
    <value>Gérer les signet</value>
  </data>
  <data name="ResManageDrawingGUIConfigure" xml:space="preserve">
    <value>Gérer la configuration de l'interface graphique de dessin</value>
  </data>
  <data name="ResManageDrawings" xml:space="preserve">
    <value>Gérer les dessins</value>
  </data>
  <data name="ResManageDrawings1" xml:space="preserve">
    <value>Gérer les dessins</value>
  </data>
  <data name="ResManageMFGCode" xml:space="preserve">
    <value>CodeMFG</value>
  </data>
  <data name="ResManagePartNumber" xml:space="preserve">
    <value>Numéro de la pièce</value>
  </data>
  <data name="ResManageParts" xml:space="preserve">
    <value>Gérer les pièces</value>
  </data>
  <data name="ResManageParts1" xml:space="preserve">
    <value>Gérer les pièces</value>
  </data>
  <data name="ResManagePartsDetails" xml:space="preserve">
    <value>Gérer les détails de la pièce</value>
  </data>
  <data name="ResManagePartSearchList" xml:space="preserve">
    <value>Gérer la liste de recherche de pièces</value>
  </data>
  <data name="ResManagePartsGUIConfigure" xml:space="preserve">
    <value>Gérer la configuration de l'interface graphique des pièces</value>
  </data>
  <data name="ResManageSection-Add" xml:space="preserve">
    <value>Gérer la section - Ajouter</value>
  </data>
  <data name="ResManageSection-Add1" xml:space="preserve">
    <value>Gérer la section - Ajouter</value>
  </data>
  <data name="ResManageSections" xml:space="preserve">
    <value>Gérer les sections</value>
  </data>
  <data name="ResManageSections1" xml:space="preserve">
    <value>Gérer les sections</value>
  </data>
  <data name="ResManufacturerCode" xml:space="preserve">
    <value>Code constructeur</value>
  </data>
  <data name="ResManufacturingYear" xml:space="preserve">
    <value>Année de fabrication</value>
  </data>
  <data name="ResManufacturingYear1" xml:space="preserve">
    <value>Année de fabrication</value>
  </data>
  <data name="Resmanytomany" xml:space="preserve">
    <value>Plusieurs à plusieurs</value>
  </data>
  <data name="Resmanytoone" xml:space="preserve">
    <value>Plusieurs à un</value>
  </data>
  <data name="ResMaskingAction" xml:space="preserve">
    <value>Action de masquage</value>
  </data>
  <data name="ResMaskingAction1" xml:space="preserve">
    <value>Action de masquage</value>
  </data>
  <data name="ResMaskingActionLangaugeList" xml:space="preserve">
    <value>Liste des langages d'action de masquage</value>
  </data>
  <data name="ResMaskingActionLanguageList" xml:space="preserve">
    <value>Langages d'action de masquage</value>
  </data>
  <data name="ResMaskingActionLanguageList1" xml:space="preserve">
    <value>Langages d'action de masquage</value>
  </data>
  <data name="ResMaskingActionList" xml:space="preserve">
    <value>Liste des actions de masquage</value>
  </data>
  <data name="ResMaskingActionList1" xml:space="preserve">
    <value>Liste des actions de masquage</value>
  </data>
  <data name="ResMaskingActions" xml:space="preserve">
    <value>Actions de masquage</value>
  </data>
  <data name="ResMaskingActions1" xml:space="preserve">
    <value>Actions de masquage</value>
  </data>
  <data name="ResMaskingActionsList" xml:space="preserve">
    <value>Liste des actions de masquage</value>
  </data>
  <data name="ResMaskingActionsList1" xml:space="preserve">
    <value>Liste des actions de masquage</value>
  </data>
  <data name="ResMaskingCode" xml:space="preserve">
    <value>Code de masquage</value>
  </data>
  <data name="ResMaskingCode1" xml:space="preserve">
    <value>Code de masquage</value>
  </data>
  <data name="ResMaskingCodeNotFound" xml:space="preserve">
    <value>Code de masquage introuvable</value>
  </data>
  <data name="ResMaskingDescription" xml:space="preserve">
    <value>Description du masquage</value>
  </data>
  <data name="ResMaskingDescription1" xml:space="preserve">
    <value>Description du masquage</value>
  </data>
  <data name="ResMaskingDescriptionUpdation" xml:space="preserve">
    <value>Mise à jour de la description du masquage</value>
  </data>
  <data name="ResMaskingDescriptionUpdation1" xml:space="preserve">
    <value>Mise à jour de la description du masquage</value>
  </data>
  <data name="ResMaskingDetails" xml:space="preserve">
    <value>Détails du masquage</value>
  </data>
  <data name="ResMaskingDetails1" xml:space="preserve">
    <value>Détails du masquage</value>
  </data>
  <data name="ResMaskingNotes" xml:space="preserve">
    <value>Notes de masquage</value>
  </data>
  <data name="ResMaskingNotes1" xml:space="preserve">
    <value>Notes de masquage</value>
  </data>
  <data name="ResMaster" xml:space="preserve">
    <value>Maître</value>
  </data>
  <data name="ResMaster1" xml:space="preserve">
    <value>Maître</value>
  </data>
  <data name="ResMasterTransaction" xml:space="preserve">
    <value>Maître / Transaction</value>
  </data>
  <data name="ResMauroCredali" xml:space="preserve">
    <value>Mauro Credali</value>
  </data>
  <data name="ResMaxZoomIn" xml:space="preserve">
    <value>Zoom avant maximum</value>
  </data>
  <data name="ResMaxZoomOut" xml:space="preserve">
    <value>Zoom arrière maximal</value>
  </data>
  <data name="ResMegEnterSectionName" xml:space="preserve">
    <value>Entrez le nom de la section</value>
  </data>
  <data name="ResMenu" xml:space="preserve">
    <value>Menu</value>
  </data>
  <data name="ResMenu1" xml:space="preserve">
    <value>Menu</value>
  </data>
  <data name="ResMenuName" xml:space="preserve">
    <value>Nom du menu</value>
  </data>
  <data name="ResMFGCode" xml:space="preserve">
    <value>Code de fabrication</value>
  </data>
  <data name="ResMFGCode1" xml:space="preserve">
    <value>Code de fabrication</value>
  </data>
  <data name="ResMFGCodecannotbeempty" xml:space="preserve">
    <value>Le code de fabrication ne peut pas être vide</value>
  </data>
  <data name="ResMFGCodeDetails" xml:space="preserve">
    <value>Détails du code de fabrication</value>
  </data>
  <data name="ResMFGCodeNameSearchList" xml:space="preserve">
    <value>Liste de recherche par nom de code de fabrication</value>
  </data>
  <data name="ResMFGCodeNameSearchList1" xml:space="preserve">
    <value>Liste de recherche par nom de code de fabrication</value>
  </data>
  <data name="ResMFGCodeXML" xml:space="preserve">
    <value>Code_de_fabrication</value>
  </data>
  <data name="ResMFGDetails" xml:space="preserve">
    <value>Détails MFG</value>
  </data>
  <data name="ResMFGList" xml:space="preserve">
    <value>Liste de fabrication</value>
  </data>
  <data name="ResMFGList1" xml:space="preserve">
    <value>Liste de fabrication</value>
  </data>
  <data name="ResMFGName" xml:space="preserve">
    <value>Nom de fabrication</value>
  </data>
  <data name="ResMFGName1" xml:space="preserve">
    <value>Nom de fabrication</value>
  </data>
  <data name="ResMfrer" xml:space="preserve">
    <value>Fabricant</value>
  </data>
  <data name="ResMiddleName" xml:space="preserve">
    <value>Deuxième nom</value>
  </data>
  <data name="ResMiddleName1" xml:space="preserve">
    <value>Deuxième nom</value>
  </data>
  <data name="ResMngPartsSetItemNo" xml:space="preserve">
    <value>Définir le numéro d'article</value>
  </data>
  <data name="ResMobile#" xml:space="preserve">
    <value>Numéro de portable</value>
  </data>
  <data name="ResMobile#1" xml:space="preserve">
    <value>Numéro de portable</value>
  </data>
  <data name="ResMobileNumber" xml:space="preserve">
    <value>Numéro de portable</value>
  </data>
  <data name="ResMobileNumber1" xml:space="preserve">
    <value>Numéro de portable</value>
  </data>
  <data name="ResModalImage" xml:space="preserve">
    <value>Images de modèles</value>
  </data>
  <data name="ResModalImage1" xml:space="preserve">
    <value>Images de modèles</value>
  </data>
  <data name="ResMode" xml:space="preserve">
    <value>Mode</value>
  </data>
  <data name="ResMode1" xml:space="preserve">
    <value>Mode</value>
  </data>
  <data name="ResModel" xml:space="preserve">
    <value>Modèle</value>
  </data>
  <data name="ResModel-Add" xml:space="preserve">
    <value>Modèle - Ajouter</value>
  </data>
  <data name="ResModel-Add1" xml:space="preserve">
    <value>Modèle - Ajouter</value>
  </data>
  <data name="ResModel1" xml:space="preserve">
    <value>Modèle</value>
  </data>
  <data name="ResModelAddNewName" xml:space="preserve">
    <value>Ajouter un nouveau modèle</value>
  </data>
  <data name="ResModelAddNewName1" xml:space="preserve">
    <value>Ajouter un nouveau modèle</value>
  </data>
  <data name="ResModelAttachments" xml:space="preserve">
    <value>Modèle - Documents joints</value>
  </data>
  <data name="ResModelCode" xml:space="preserve">
    <value>Code modèle</value>
  </data>
  <data name="ResModelCode1" xml:space="preserve">
    <value>Code modèle</value>
  </data>
  <data name="ResModelDetils" xml:space="preserve">
    <value>Détails du modèle</value>
  </data>
  <data name="ResModelImages" xml:space="preserve">
    <value>Images de modèles</value>
  </data>
  <data name="ResModelImages1" xml:space="preserve">
    <value>Images de modèles</value>
  </data>
  <data name="ResModelList" xml:space="preserve">
    <value>Liste des modèles</value>
  </data>
  <data name="ResModelList1" xml:space="preserve">
    <value>Liste des modèles</value>
  </data>
  <data name="ResModelName" xml:space="preserve">
    <value>Nom du modèle</value>
  </data>
  <data name="ResModelName1" xml:space="preserve">
    <value>Nom du modèle</value>
  </data>
  <data name="ResModelNameSearchList" xml:space="preserve">
    <value>Liste de recherche par nom de modèle</value>
  </data>
  <data name="ResModelNameSearchList1" xml:space="preserve">
    <value>Liste de recherche par nom de modèle</value>
  </data>
  <data name="ResModifiedBy" xml:space="preserve">
    <value>Modifié par</value>
  </data>
  <data name="ResModifiedDate" xml:space="preserve">
    <value>Date modifiée</value>
  </data>
  <data name="ResModifyQuery" xml:space="preserve">
    <value>Modifier la requête</value>
  </data>
  <data name="ResModifyQuery1" xml:space="preserve">
    <value>Modifier la requête</value>
  </data>
  <data name="ResModule" xml:space="preserve">
    <value>Module</value>
  </data>
  <data name="ResMonth" xml:space="preserve">
    <value>Mois</value>
  </data>
  <data name="ResMore" xml:space="preserve">
    <value>Montrer plus</value>
  </data>
  <data name="ResMore1" xml:space="preserve">
    <value>Plus</value>
  </data>
  <data name="ResMoreFields" xml:space="preserve">
    <value>Plus</value>
  </data>
  <data name="ResMostrecent" xml:space="preserve">
    <value>Le plus récent</value>
  </data>
  <data name="ResMostrecent1" xml:space="preserve">
    <value>Le plus récent</value>
  </data>
  <data name="ResMoveAnnotation" xml:space="preserve">
    <value>Déplacer l'annotation</value>
  </data>
  <data name="ResMsgAbbrevationDescription" xml:space="preserve">
    <value>Entrez la description de l'abréviation</value>
  </data>
  <data name="ResmsgAssemblyAlreadyBookmarked" xml:space="preserve">
    <value>Supprimer le signet</value>
  </data>
  <data name="ResMsgAssemblyNoCannotBeEmpty" xml:space="preserve">
    <value>Le numéro d'assemblage ne peut pas être vide</value>
  </data>
  <data name="ResmsgbackupDate" xml:space="preserve">
    <value>Date de sauvegarde</value>
  </data>
  <data name="ResMsgCombinationOfItemNoLvelSeqAlreadyExists" xml:space="preserve">
    <value>La combinaison du numéro d'article, du niveau et de la séquence existe déjà</value>
  </data>
  <data name="ResMsgCommonNotesHistory" xml:space="preserve">
    <value>Historique des notes communes</value>
  </data>
  <data name="ResMsgCommonNotesHistory1" xml:space="preserve">
    <value>Historique des notes communes</value>
  </data>
  <data name="ResMsgCompareAssembliesOnlyforselectedAssemblyFromAssemblyPartHeader" xml:space="preserve">
    <value>Comparer les assemblages uniquement pour l'assemblage sélectionné</value>
  </data>
  <data name="ResMsgCopyRoleFrom" xml:space="preserve">
    <value />
  </data>
  <data name="ResMsgCopyRoleFrom1" xml:space="preserve">
    <value />
  </data>
  <data name="ResMsgCustomerCodeCannotBeEmpty" xml:space="preserve">
    <value>Le code client ne peut pas être vide</value>
  </data>
  <data name="ResMsgCustomerNameCannotBeEmpty" xml:space="preserve">
    <value>Le nom du client ne peut pas être vide</value>
  </data>
  <data name="ResMsgCustomerRerence#" xml:space="preserve">
    <value>Entrez le numéro de référence du client</value>
  </data>
  <data name="ResMsgDatemustbeBiggerorEqualtotodaydate" xml:space="preserve">
    <value>La date doit être supérieure ou égale à la date du jour</value>
  </data>
  <data name="ResmsgDeletedCannotbeRecoverd" xml:space="preserve">
    <value>Les enregistrements supprimés ne peuvent pas être récupérés.</value>
  </data>
  <data name="ResMsgDependencyFound" xml:space="preserve">
    <value>Dépendance trouvée</value>
  </data>
  <data name="ResMsgDescriptionShouldbemandatory" xml:space="preserve">
    <value>La description doit être obligatoire</value>
  </data>
  <data name="ResMsgDetailsAlreadyExists" xml:space="preserve">
    <value>Les détails sont déjà disponibles</value>
  </data>
  <data name="ResmsgDoyouwantoremove" xml:space="preserve">
    <value>Voulez-vous supprimer?</value>
  </data>
  <data name="ResmsgDoyouwantoreplace" xml:space="preserve">
    <value>Voulez-vous remplacer partout?</value>
  </data>
  <data name="ResmsgDuplicateImage" xml:space="preserve">
    <value>Dupliquer l'image</value>
  </data>
  <data name="ResMsgDuplicateItemNo" xml:space="preserve">
    <value>Dupliquer Numéro d'article</value>
  </data>
  <data name="ResMsgDuplicateSequenceNo" xml:space="preserve">
    <value>Dupliquer Numéro de séquence</value>
  </data>
  <data name="ResMsgEnterAbbreviation" xml:space="preserve">
    <value>Entrez l'abréviation</value>
  </data>
  <data name="ResMsgEnterAbbreviationDescription" xml:space="preserve">
    <value>Entrez la description de l'abréviation</value>
  </data>
  <data name="ResMsgEnterAbbreviationDescription1" xml:space="preserve">
    <value>Entrez la description de l'abréviation</value>
  </data>
  <data name="ResMsgEnterActivityName" xml:space="preserve">
    <value>Entrez le nom de l'activité</value>
  </data>
  <data name="ResMsgEnterActivityName1" xml:space="preserve">
    <value>Entrez le nom de l'activité</value>
  </data>
  <data name="ResMsgEnterAddress1" xml:space="preserve">
    <value>Entrez l'adresse 1</value>
  </data>
  <data name="ResMsgEnterAddress2" xml:space="preserve">
    <value>Entrez l'adresse 2</value>
  </data>
  <data name="ResMsgEnterAlternateDesc" xml:space="preserve">
    <value>Entrez une autre description</value>
  </data>
  <data name="ResMsgEnterAlternateDesc1" xml:space="preserve">
    <value>Entrez une autre description</value>
  </data>
  <data name="ResMsgEnterAlternatePart#" xml:space="preserve">
    <value>Entrez un autre numéro de pièce</value>
  </data>
  <data name="ResMsgEnterAlternatePart#1" xml:space="preserve">
    <value>Entrez un autre numéro de pièce</value>
  </data>
  <data name="ResMsgEnterAssembly#" xml:space="preserve">
    <value>Entrez le numéro d'assemblage</value>
  </data>
  <data name="ResMsgEnterAssembly#1" xml:space="preserve">
    <value>Entrez le numéro d'assemblage</value>
  </data>
  <data name="ResMsgEnterAssemblyName" xml:space="preserve">
    <value>Entrez le nom de l'assemblage</value>
  </data>
  <data name="ResMsgEnterAssemblyName1" xml:space="preserve">
    <value>Entrez le nom de l'assemblage</value>
  </data>
  <data name="ResMsgEnterBrandName" xml:space="preserve">
    <value>Entrez le nom de la marque</value>
  </data>
  <data name="ResMsgEnterBrandName1" xml:space="preserve">
    <value>Entrez le nom de la marque</value>
  </data>
  <data name="ResMsgEnterCartDate" xml:space="preserve">
    <value>Entrez la date du panier</value>
  </data>
  <data name="ResMsgEnterCartDate1" xml:space="preserve">
    <value>Entrez la date du panier</value>
  </data>
  <data name="ResMsgEnterCatalogueName" xml:space="preserve">
    <value>Entrez le nom du catalogue</value>
  </data>
  <data name="ResMsgEnterCatalogueName1" xml:space="preserve">
    <value>Entrez le nom du catalogue</value>
  </data>
  <data name="ResMsgEntercc" xml:space="preserve">
    <value>Entrer en copie </value>
  </data>
  <data name="ResMsgEnterCity" xml:space="preserve">
    <value>Entrez la ville</value>
  </data>
  <data name="ResMsgEnterCode" xml:space="preserve">
    <value>Entrez le code</value>
  </data>
  <data name="ResMsgEnterCode1" xml:space="preserve">
    <value>Entrez le code</value>
  </data>
  <data name="ResMsgEnterColumnValue" xml:space="preserve">
    <value>Entrez la valeur de la colonne</value>
  </data>
  <data name="ResMsgEnterColumnValue1" xml:space="preserve">
    <value>Entrez la valeur de la colonne</value>
  </data>
  <data name="ResMsgEnterCommonNotes" xml:space="preserve">
    <value>Entrez des notes communes</value>
  </data>
  <data name="ResMsgEnterCommonNotes1" xml:space="preserve">
    <value>Entrez des notes communes</value>
  </data>
  <data name="ResMsgEnterConfirmPassword" xml:space="preserve">
    <value>Entrez Confirmer le mot de passe</value>
  </data>
  <data name="ResMsgEnterConfirmPassword1" xml:space="preserve">
    <value>Entrez Confirmer le mot de passe</value>
  </data>
  <data name="ResMsgEnterCurrentOwner" xml:space="preserve">
    <value>Entrez le propriétaire actuel</value>
  </data>
  <data name="ResMsgEnterCurrentOwner1" xml:space="preserve">
    <value>Entrez le propriétaire actuel</value>
  </data>
  <data name="ResMsgEnterCurrentPassword" xml:space="preserve">
    <value>Saisissez le mot de passe actuel</value>
  </data>
  <data name="ResMsgEnterCurrentPassword1" xml:space="preserve">
    <value>Saisissez le mot de passe actuel</value>
  </data>
  <data name="ResMsgEnterCustomer" xml:space="preserve">
    <value>Entrez le client</value>
  </data>
  <data name="ResMsgEnterCustomer1" xml:space="preserve">
    <value>Entrez le client</value>
  </data>
  <data name="ResMsgEnterCustomerCode" xml:space="preserve">
    <value>Entrez le code client</value>
  </data>
  <data name="ResMsgEnterCustomerCode1" xml:space="preserve">
    <value>Entrez le code client</value>
  </data>
  <data name="ResMsgEnterCustomerMFGCode" xml:space="preserve">
    <value>Entrez le code de fabrication du client</value>
  </data>
  <data name="ResMsgEnterCustomerMFGCode1" xml:space="preserve">
    <value>Entrez le code de fabrication du client</value>
  </data>
  <data name="ResMsgEnterCustomerName" xml:space="preserve">
    <value>Entrez le nom du client</value>
  </data>
  <data name="ResMsgEnterCustomerName1" xml:space="preserve">
    <value>Entrez le nom du client</value>
  </data>
  <data name="ResMsgEnterCustomerPart#" xml:space="preserve">
    <value>Entrez le numéro de pièce du client</value>
  </data>
  <data name="ResMsgEnterCustomerPart#1" xml:space="preserve">
    <value>Entrez le numéro de pièce du client</value>
  </data>
  <data name="ResMsgEnterDefaultFontName" xml:space="preserve">
    <value>Entrez le nom de policede caractère par défaut</value>
  </data>
  <data name="ResMsgEnterDefaultFontName1" xml:space="preserve">
    <value>Entrez le nom de policede caractère par défaut</value>
  </data>
  <data name="ResMsgEnterDepartment" xml:space="preserve">
    <value>Entrez le département</value>
  </data>
  <data name="ResMsgEnterDepartment1" xml:space="preserve">
    <value>Entrez le département</value>
  </data>
  <data name="ResMsgEnterDepartmentCode" xml:space="preserve">
    <value>Entrez le code du département</value>
  </data>
  <data name="ResMsgEnterDepartmentCode1" xml:space="preserve">
    <value>Entrez le code du département</value>
  </data>
  <data name="ResMsgEnterDepartmentName" xml:space="preserve">
    <value>Entrez le nom du service</value>
  </data>
  <data name="ResMsgEnterDepartmentName1" xml:space="preserve">
    <value>Entrez le nom du service</value>
  </data>
  <data name="ResMsgEnterDescription" xml:space="preserve">
    <value>Erreur de description</value>
  </data>
  <data name="ResMsgEnterDescription1" xml:space="preserve">
    <value>Erreur de description</value>
  </data>
  <data name="ResMsgEnterDesignation" xml:space="preserve">
    <value>Entrez la désignation</value>
  </data>
  <data name="ResMsgEnterDesignation1" xml:space="preserve">
    <value>Entrez la désignation</value>
  </data>
  <data name="ResMsgEnterDesignationCode" xml:space="preserve">
    <value>Entrez le code de désignation</value>
  </data>
  <data name="ResMsgEnterDesignationCode1" xml:space="preserve">
    <value>Entrez le code de désignation</value>
  </data>
  <data name="ResMsgEnterDesignationName" xml:space="preserve">
    <value>Entrez le nom de la désignation</value>
  </data>
  <data name="ResMsgEnterDesignationName1" xml:space="preserve">
    <value>Entrez le nom de la désignation</value>
  </data>
  <data name="ResMsgEnterDictionary" xml:space="preserve">
    <value>Entrez la description du dictionnaire</value>
  </data>
  <data name="ResMsgEnterDisplayName" xml:space="preserve">
    <value>Entrez le nom d'affichage</value>
  </data>
  <data name="ResMsgEnterDisplayName1" xml:space="preserve">
    <value>Entrez le nom d'affichage</value>
  </data>
  <data name="ResMsgEnterDocumentDescription" xml:space="preserve">
    <value>Entrez le titre du document</value>
  </data>
  <data name="ResMsgEnterDrawingName" xml:space="preserve">
    <value>Entrez le nom du dessin</value>
  </data>
  <data name="ResMsgEnterDrawingName1" xml:space="preserve">
    <value>Entrez le nom du dessin</value>
  </data>
  <data name="ResMsgEnterEffectiveDateFrom" xml:space="preserve">
    <value>Entrez la date d'entrée en vigueur du</value>
  </data>
  <data name="ResMsgEnterEffectiveDateFrom1" xml:space="preserve">
    <value>Entrez la date d'entrée en vigueur du</value>
  </data>
  <data name="ResMsgEnterEffectiveDateTo" xml:space="preserve">
    <value>Entrez la date d'entrée en vigueur au</value>
  </data>
  <data name="ResMsgEnterEffectiveDateTo1" xml:space="preserve">
    <value>Entrez la date d'entrée en vigueur au</value>
  </data>
  <data name="ResMsgEnterEmail" xml:space="preserve">
    <value>Entrez votre e-mail</value>
  </data>
  <data name="ResMsgEnterEmail1" xml:space="preserve">
    <value>Entrez votre e-mail</value>
  </data>
  <data name="ResMsgEnterEmployeeCode" xml:space="preserve">
    <value>Entrez le code d'employé</value>
  </data>
  <data name="ResMsgEnterEmployeeCode1" xml:space="preserve">
    <value>Entrez le code d'employé</value>
  </data>
  <data name="ResMsgEnterExtension1" xml:space="preserve">
    <value>Entrez l'extension 1</value>
  </data>
  <data name="ResMsgEnterExtension2" xml:space="preserve">
    <value>Entrez l'extension 2</value>
  </data>
  <data name="ResMsgEnterFGName" xml:space="preserve">
    <value>Entrez le nom du FG</value>
  </data>
  <data name="ResMsgEnterFGName1" xml:space="preserve">
    <value>Entrez le nom du FG</value>
  </data>
  <data name="ResMsgEnterFirstName" xml:space="preserve">
    <value>Entrez votre prénom</value>
  </data>
  <data name="ResMsgEnterFirstName1" xml:space="preserve">
    <value>Entrez votre prénom</value>
  </data>
  <data name="ResMsgEnterFlexi1" xml:space="preserve">
    <value>Entrez Flexi 1</value>
  </data>
  <data name="ResMsgEnterFlexi10" xml:space="preserve">
    <value>Entrez Flexi 10</value>
  </data>
  <data name="ResMsgEnterFlexi101" xml:space="preserve">
    <value>Entrez Flexi 10</value>
  </data>
  <data name="ResMsgEnterFlexi11" xml:space="preserve">
    <value>Entrez Flexi 1</value>
  </data>
  <data name="ResMsgEnterFlexi2" xml:space="preserve">
    <value>Entrez Flexi 2</value>
  </data>
  <data name="ResMsgEnterFlexi21" xml:space="preserve">
    <value>Entrez Flexi 2</value>
  </data>
  <data name="ResMsgEnterFlexi3" xml:space="preserve">
    <value>Entrez Flexi 3</value>
  </data>
  <data name="ResMsgEnterFlexi31" xml:space="preserve">
    <value>Entrez Flexi 3</value>
  </data>
  <data name="ResMsgEnterFlexi4" xml:space="preserve">
    <value>Entrez Flexi 4</value>
  </data>
  <data name="ResMsgEnterFlexi41" xml:space="preserve">
    <value>Entrez Flexi 4</value>
  </data>
  <data name="ResMsgEnterFlexi5" xml:space="preserve">
    <value>Entrez Flexi 5</value>
  </data>
  <data name="ResMsgEnterFlexi51" xml:space="preserve">
    <value>Entrez Flexi 5</value>
  </data>
  <data name="ResMsgEnterFlexi6" xml:space="preserve">
    <value>Entrez Flexi 6</value>
  </data>
  <data name="ResMsgEnterFlexi61" xml:space="preserve">
    <value>Entrez Flexi 6</value>
  </data>
  <data name="ResMsgEnterFlexi7" xml:space="preserve">
    <value>Entrez Flexi 7</value>
  </data>
  <data name="ResMsgEnterFlexi71" xml:space="preserve">
    <value>Entrez Flexi 7</value>
  </data>
  <data name="ResMsgEnterFlexi8" xml:space="preserve">
    <value>Entrez Flexi 8</value>
  </data>
  <data name="ResMsgEnterFlexi81" xml:space="preserve">
    <value>Entrez Flexi 8</value>
  </data>
  <data name="ResMsgEnterFlexi9" xml:space="preserve">
    <value>Entrez Flexi 9</value>
  </data>
  <data name="ResMsgEnterFlexi91" xml:space="preserve">
    <value>Entrez Flexi 9</value>
  </data>
  <data name="ResMsgEnterFontName" xml:space="preserve">
    <value>Entrez le nom de la police de caractère</value>
  </data>
  <data name="ResMsgEnterFontName1" xml:space="preserve">
    <value>Entrez le nom de la police de caractère</value>
  </data>
  <data name="ResMsgEnterFromSN" xml:space="preserve">
    <value>Entrer depuis SN</value>
  </data>
  <data name="ResMsgEnterIndent" xml:space="preserve">
    <value>Entrez un retrait</value>
  </data>
  <data name="ResMsgEnterIndentation" xml:space="preserve">
    <value>Entrez Échancrure </value>
  </data>
  <data name="ResMsgEnterIndentation1" xml:space="preserve">
    <value>Entrez Échancrure </value>
  </data>
  <data name="ResMsgEnterItem#" xml:space="preserve">
    <value>Entrez le numéro d'article</value>
  </data>
  <data name="ResMsgEnterItem#1" xml:space="preserve">
    <value>Entrez le numéro d'article</value>
  </data>
  <data name="ResMsgEnterLanguageCode" xml:space="preserve">
    <value>Entrez le code de langue</value>
  </data>
  <data name="ResMsgEnterLanguageCode1" xml:space="preserve">
    <value>Entrez le code de langue</value>
  </data>
  <data name="ResMsgEnterLanguageName" xml:space="preserve">
    <value>Entrez le nom de la langue</value>
  </data>
  <data name="ResMsgEnterLanguageName1" xml:space="preserve">
    <value>Entrez le nom de la langue</value>
  </data>
  <data name="ResMsgEnterLastName" xml:space="preserve">
    <value>Entrer le nom de famille</value>
  </data>
  <data name="ResMsgEnterLastName1" xml:space="preserve">
    <value>Entrer le nom de famille</value>
  </data>
  <data name="ResMsgEnterLevel" xml:space="preserve">
    <value>Entrez le niveau</value>
  </data>
  <data name="ResMsgEnterLevel1" xml:space="preserve">
    <value>Entrez le niveau</value>
  </data>
  <data name="ResMsgEnterLocalNoteCode" xml:space="preserve">
    <value>Entrez le code de note local</value>
  </data>
  <data name="ResMsgEnterLocalNotes" xml:space="preserve">
    <value>Entrez les notes locales</value>
  </data>
  <data name="ResMsgEnterLocalNotes1" xml:space="preserve">
    <value>Entrez les notes locales</value>
  </data>
  <data name="ResMsgEnterLocalRemarks" xml:space="preserve">
    <value>Entrez les remarques locales</value>
  </data>
  <data name="ResMsgEnterLocation" xml:space="preserve">
    <value>Entrez l'emplacement</value>
  </data>
  <data name="ResMsgEnterLoginID" xml:space="preserve">
    <value>Entrez l'identifiant de connexion</value>
  </data>
  <data name="ResMsgEnterLoginID1" xml:space="preserve">
    <value>Entrez l'identifiant de connexion</value>
  </data>
  <data name="ResMsgEnterManufacturingYear" xml:space="preserve">
    <value>Entrez l'année de fabrication</value>
  </data>
  <data name="ResMsgEnterManufacturingYear1" xml:space="preserve">
    <value>Entrez l'année de fabrication</value>
  </data>
  <data name="ResMsgEnterMaskingCode" xml:space="preserve">
    <value>Entrez le code de masquage</value>
  </data>
  <data name="ResMsgEnterMaskingCode1" xml:space="preserve">
    <value>Entrez le code de masquage</value>
  </data>
  <data name="ResMsgEnterMaskingDescription" xml:space="preserve">
    <value>Entrez la description du masquage</value>
  </data>
  <data name="ResMsgEnterMaskingDescription1" xml:space="preserve">
    <value>Entrez la description du masquage</value>
  </data>
  <data name="ResMsgEnterMFGCode" xml:space="preserve">
    <value>Entrez le code de fabrication</value>
  </data>
  <data name="ResMsgEnterMFGCode1" xml:space="preserve">
    <value>Entrez le code de fabrication</value>
  </data>
  <data name="ResMsgEnterMFGName" xml:space="preserve">
    <value>Entrez le nom de fabrication</value>
  </data>
  <data name="ResMsgEnterMFGName1" xml:space="preserve">
    <value>Entrez le nom de fabrication</value>
  </data>
  <data name="ResMsgEnterMfrer" xml:space="preserve">
    <value>Entrez le fabricant</value>
  </data>
  <data name="ResMsgEnterMiddleName" xml:space="preserve">
    <value>Entrez le deuxième prénom</value>
  </data>
  <data name="ResMsgEnterMiddleName1" xml:space="preserve">
    <value>Entrez le deuxième prénom</value>
  </data>
  <data name="ResMsgEnterMobileNumber" xml:space="preserve">
    <value>Entrez le numéro de mobile</value>
  </data>
  <data name="ResMsgEnterMobileNumber1" xml:space="preserve">
    <value>Entrez le numéro de mobile</value>
  </data>
  <data name="ResMsgEnterModelCode" xml:space="preserve">
    <value>Entrez le code du modèle</value>
  </data>
  <data name="ResMsgEnterModelCode1" xml:space="preserve">
    <value>Entrez le code du modèle</value>
  </data>
  <data name="ResMsgEnterModelName" xml:space="preserve">
    <value>Entrez le nom du modèle</value>
  </data>
  <data name="ResMsgEnterModelName1" xml:space="preserve">
    <value>Entrez le nom du modèle</value>
  </data>
  <data name="ResMsgEnterNewOwner" xml:space="preserve">
    <value>Entrez un nouveau propriétaire</value>
  </data>
  <data name="ResMsgEnterNewOwner1" xml:space="preserve">
    <value>Entrez un nouveau propriétaire</value>
  </data>
  <data name="ResMsgEnterNewPassword" xml:space="preserve">
    <value>Entrez un nouveau mot de passe</value>
  </data>
  <data name="ResMsgEnterNewPassword1" xml:space="preserve">
    <value>Entrez un nouveau mot de passe</value>
  </data>
  <data name="ResMsgEnterNotes" xml:space="preserve">
    <value>Entrer des notes</value>
  </data>
  <data name="ResMsgEnterOptionDescription" xml:space="preserve">
    <value>Entrez la description de l'option</value>
  </data>
  <data name="ResMsgEnterOptionDescription1" xml:space="preserve">
    <value>Entrez la description de l'option</value>
  </data>
  <data name="ResMsgEnterOrder#" xml:space="preserve">
    <value>Entrez le numéro de commande</value>
  </data>
  <data name="ResMsgEnterOrder#1" xml:space="preserve">
    <value>Entrez le numéro de commande</value>
  </data>
  <data name="ResMsgEnterOrderDate" xml:space="preserve">
    <value>Entrez la date de la commande</value>
  </data>
  <data name="ResMsgEnterOrderDate1" xml:space="preserve">
    <value>Entrez la date de la commande</value>
  </data>
  <data name="ResMsgEnterOrderTopLevel" xml:space="preserve">
    <value>Entrez le numéro de commande / Niveau supérieur</value>
  </data>
  <data name="ResMsgEnterParmaCode" xml:space="preserve">
    <value>Entrez Code Parma</value>
  </data>
  <data name="ResMsgEnterParmaCode1" xml:space="preserve">
    <value>Entrez Code Parma</value>
  </data>
  <data name="ResMsgEnterPart#" xml:space="preserve">
    <value>Entrez le numéro de pièce</value>
  </data>
  <data name="ResMsgEnterPart#1" xml:space="preserve">
    <value>Entrez le numéro de pièce</value>
  </data>
  <data name="ResMsgEnterPartDesc" xml:space="preserve">
    <value>Entrez la description de la pièce</value>
  </data>
  <data name="ResMsgEnterPartDesc1" xml:space="preserve">
    <value>Entrez la description de la pièce</value>
  </data>
  <data name="ResMsgEnterPartDescription" xml:space="preserve">
    <value>Entrez la description de la pièce</value>
  </data>
  <data name="ResMsgEnterPartDescription1" xml:space="preserve">
    <value>Entrez la description de la pièce</value>
  </data>
  <data name="ResMsgEnterPartStatus" xml:space="preserve">
    <value>Entrez le statut de la pièce</value>
  </data>
  <data name="ResMsgEnterPassword" xml:space="preserve">
    <value>Entrez le mot de passe</value>
  </data>
  <data name="ResMsgEnterPassword1" xml:space="preserve">
    <value>Entrez le mot de passe</value>
  </data>
  <data name="ResMsgEnterPharmaCode" xml:space="preserve">
    <value>Entrez Code Parma</value>
  </data>
  <data name="ResMsgEnterPhone1" xml:space="preserve">
    <value>Entrez Code Parma</value>
  </data>
  <data name="ResMsgEnterPhone2" xml:space="preserve">
    <value>Entrez Code Parma</value>
  </data>
  <data name="ResMsgEnterProductTypeName" xml:space="preserve">
    <value>Entrez le nom du type de produit</value>
  </data>
  <data name="ResMsgEnterProductTypeName1" xml:space="preserve">
    <value>Entrez le nom du type de produit</value>
  </data>
  <data name="ResMsgEnterQty" xml:space="preserve">
    <value>Entrez la quantité</value>
  </data>
  <data name="ResMsgEnterQty1" xml:space="preserve">
    <value>Entrez la quantité</value>
  </data>
  <data name="ResMsgEnterQueryName" xml:space="preserve">
    <value>Entrez le nom de la requête</value>
  </data>
  <data name="ResMsgEnterQueryName1" xml:space="preserve">
    <value>Entrez le nom de la requête</value>
  </data>
  <data name="ResMsgEnterReferenceAssembly#" xml:space="preserve">
    <value>Entrez le numéro d'assemblage de référence</value>
  </data>
  <data name="ResMsgEnterReferenceAssembly#1" xml:space="preserve">
    <value>Entrez le numéro d'assemblage de référence</value>
  </data>
  <data name="ResMsgEnterReferenceAssemblyDescription" xml:space="preserve">
    <value>Entrez la description de l'assemblage de référence</value>
  </data>
  <data name="ResMsgEnterReferenceAssemblyMFGCode" xml:space="preserve">
    <value>Entrez le code de fabrication de l'assemblage de référence</value>
  </data>
  <data name="ResMsgEnterReferenceAssemblyMFGCode1" xml:space="preserve">
    <value>Entrez le code de fabrication de l'assemblage de référence</value>
  </data>
  <data name="ResMsgEnterReferenceAssemblyName" xml:space="preserve">
    <value>Entrez le nom de l'assemblage de référence</value>
  </data>
  <data name="ResMsgEnterReferenceAssemblyName1" xml:space="preserve">
    <value>Entrez le nom de l'assemblage de référence</value>
  </data>
  <data name="ResMsgEnterRemarks" xml:space="preserve">
    <value>Entrez des remarques</value>
  </data>
  <data name="ResMsgEnterRemarks1" xml:space="preserve">
    <value>Entrez des remarques</value>
  </data>
  <data name="ResMsgEnterRenamePart#as" xml:space="preserve">
    <value>Entrez Renommer le numéro de pièce comme</value>
  </data>
  <data name="ResMsgEnterRenamePart#as1" xml:space="preserve">
    <value>Entrez Renommer le numéro de pièce comme</value>
  </data>
  <data name="ResMsgEnterReplacebyPart#" xml:space="preserve">
    <value>Entrez Remplacer par numéro de pièce</value>
  </data>
  <data name="ResMsgEnterReplacebyPart#1" xml:space="preserve">
    <value>Entrez Remplacer par numéro de pièce</value>
  </data>
  <data name="ResMsgEnterResolution" xml:space="preserve">
    <value>Entrez la résolution</value>
  </data>
  <data name="ResMsgEnterResolution1" xml:space="preserve">
    <value>Entrez la résolution</value>
  </data>
  <data name="ResMsgEnterRespondedDate&amp;Time" xml:space="preserve">
    <value>Entrez la date et l'heure de réponse</value>
  </data>
  <data name="ResMsgEnterRespondedDate&amp;Time1" xml:space="preserve">
    <value>Entrez la date et l'heure de réponse</value>
  </data>
  <data name="ResMsgEnterRoad#" xml:space="preserve">
    <value>Entrez le numéro de route</value>
  </data>
  <data name="ResMsgEnterRoad#1" xml:space="preserve">
    <value>Entrez le numéro de route</value>
  </data>
  <data name="ResMsgEnterRoleName" xml:space="preserve">
    <value>Entrez le nom du rôle</value>
  </data>
  <data name="ResMsgEnterRoleName1" xml:space="preserve">
    <value>Entrez le nom du rôle</value>
  </data>
  <data name="ResMsgEnterSection#" xml:space="preserve">
    <value>Entrez le numéro de section</value>
  </data>
  <data name="ResMsgEnterSection#1" xml:space="preserve">
    <value>Entrez le numéro de section</value>
  </data>
  <data name="ResMsgEnterSectionName" xml:space="preserve">
    <value>Entrez le nom de la section</value>
  </data>
  <data name="ResMsgEnterSectionName1" xml:space="preserve">
    <value>Entrez le nom de la section</value>
  </data>
  <data name="ResMsgEnterSequence" xml:space="preserve">
    <value>Entrez la séquence</value>
  </data>
  <data name="ResMsgEnterSequence#" xml:space="preserve">
    <value>Entrez le numéro de séquence</value>
  </data>
  <data name="ResMsgEnterSequence#1" xml:space="preserve">
    <value>Entrez le numéro de séquence</value>
  </data>
  <data name="ResMsgEnterSetItemNoSequence" xml:space="preserve">
    <value>Entrer le numéro d'article de la séquence</value>
  </data>
  <data name="ResMsgEnterSpecification" xml:space="preserve">
    <value>Entrez la spécification</value>
  </data>
  <data name="ResMsgEnterSpecification1" xml:space="preserve">
    <value>Entrez la spécification</value>
  </data>
  <data name="ResMsgEnterSpecifiction" xml:space="preserve">
    <value>Entrez la spécification</value>
  </data>
  <data name="ResMsgEnterSpecifiction1" xml:space="preserve">
    <value>Entrez la spécification</value>
  </data>
  <data name="ResMsgEnterTemplateCode" xml:space="preserve">
    <value>Entrez le code du modèle</value>
  </data>
  <data name="ResMsgEnterTemplateCode1" xml:space="preserve">
    <value>Entrez le code du modèle</value>
  </data>
  <data name="ResMsgEnterTemplateName" xml:space="preserve">
    <value>Entrez le nom du modèle</value>
  </data>
  <data name="ResMsgEnterTemplateName1" xml:space="preserve">
    <value>Entrez le nom du modèle</value>
  </data>
  <data name="ResMsgEnterTimeTaken" xml:space="preserve">
    <value>Entrez le temps pris</value>
  </data>
  <data name="ResMsgEnterTimeTaken1" xml:space="preserve">
    <value>Entrez le temps pris</value>
  </data>
  <data name="ResMsgEnterUOMCode" xml:space="preserve">
    <value>Entrez Code d'unité de mesure</value>
  </data>
  <data name="ResMsgEnterUOMCode1" xml:space="preserve">
    <value>Entrez Code d'unité de mesure</value>
  </data>
  <data name="ResMsgEnterUOMDescription" xml:space="preserve">
    <value>Entrez description unité de mesure</value>
  </data>
  <data name="ResMsgEnterUOMDescription1" xml:space="preserve">
    <value>Entrez description unité de mesure</value>
  </data>
  <data name="ResMsgEnterUpToSN" xml:space="preserve">
    <value>Entrer jusqu'à SN</value>
  </data>
  <data name="ResMsgEnterValidLocation" xml:space="preserve">
    <value>Emplacement invalide</value>
  </data>
  <data name="ResMsgEnterVendorCode" xml:space="preserve">
    <value>Entrez le code du fournisseur</value>
  </data>
  <data name="ResMsgEnterVendorCode1" xml:space="preserve">
    <value>Entrez le code du fournisseur</value>
  </data>
  <data name="ResMsgEnterVendorName" xml:space="preserve">
    <value>Entrez le nom du fournisseur</value>
  </data>
  <data name="ResMsgEnterVendorName1" xml:space="preserve">
    <value>Entrez le nom du fournisseur</value>
  </data>
  <data name="ResMsgEnterVIN#" xml:space="preserve">
    <value>Entrez le numéro d'identité du véhicule</value>
  </data>
  <data name="ResMsgEnterVIN#1" xml:space="preserve">
    <value>Entrez le numéro d'identité du véhicule</value>
  </data>
  <data name="ResMsgEnterVINshort#" xml:space="preserve">
    <value>Entrez NIV abrégé</value>
  </data>
  <data name="ResMsgEnterVINShortCode" xml:space="preserve">
    <value>Entrez le code abrégé du numéro d'identification du véhicule (NIV)</value>
  </data>
  <data name="ResMsgEnterYourComments" xml:space="preserve">
    <value>Entrez vos commentaires</value>
  </data>
  <data name="ResMsgEnterYourComments1" xml:space="preserve">
    <value>Entrez vos commentaires</value>
  </data>
  <data name="ResMsgEnterZipCode" xml:space="preserve">
    <value>Entrer le code postal</value>
  </data>
  <data name="ResmsgErrorsFound" xml:space="preserve">
    <value>Erreurs trouvées</value>
  </data>
  <data name="ResmsgFileAlreadyExist" xml:space="preserve">
    <value>L'image existe déjà</value>
  </data>
  <data name="ResMsgFilenameshouldnotexceed50characters" xml:space="preserve">
    <value>Le nom du fichier ne doit pas dépasser 50 caractères</value>
  </data>
  <data name="ResMsgFilesizeshouldnotexceed2MB" xml:space="preserve">
    <value>La taille du fichier ne doit pas dépasser 2 Mo</value>
  </data>
  <data name="ResMsgFilesizeshouldnotexceed50MB" xml:space="preserve">
    <value>La taille du fichier ne doit pas dépasser 50 Mo</value>
  </data>
  <data name="ResMsgFontColour" xml:space="preserve">
    <value>Sélectionnez la couleur de la police de caractère</value>
  </data>
  <data name="ResMsgFontColour1" xml:space="preserve">
    <value>Sélectionnez la couleur de la police de caractère</value>
  </data>
  <data name="ResMsgFromDate" xml:space="preserve">
    <value>Date d'entrée</value>
  </data>
  <data name="ResMsgGivenNumberalreadyavailableinParts" xml:space="preserve">
    <value>Le numéro donné est déjà disponible dans les Pièces</value>
  </data>
  <data name="ResMsgHighlightedFieldsAreMandatory" xml:space="preserve">
    <value>Les champs en surbrillance sont obligatoires</value>
  </data>
  <data name="ResMsgHotsSpotsOncceDeletedCantBeRecovered" xml:space="preserve">
    <value>Une fois supprimés, les hotspots ne peuvent pas être récupérés</value>
  </data>
  <data name="ResMsgHotsSpotsSelected" xml:space="preserve">
    <value>Hotspots sélectionnés</value>
  </data>
  <data name="ResMsgIndentLevelmustbeadigitof0to9" xml:space="preserve">
    <value>Le niveau de retrait doit être un chiffre de (0 à 9)</value>
  </data>
  <data name="ResMsgInvalidAssociationCustomerCodeAndCustomerName" xml:space="preserve">
    <value>Code client d'association et nom de client non valides</value>
  </data>
  <data name="ResMsgInvalidAssociationVendorCodeAndVendorName" xml:space="preserve">
    <value>Association incorrecte du code du fournisseur et du nom du fournisseur</value>
  </data>
  <data name="ResMsgInvalidCustomerCode" xml:space="preserve">
    <value>Code client invalide</value>
  </data>
  <data name="ResMsgInvalidCustomerName" xml:space="preserve">
    <value>Nom de client non valide</value>
  </data>
  <data name="ResMsgInvalidMaskingCode" xml:space="preserve">
    <value>Code de masquage non valide</value>
  </data>
  <data name="ResMsgInvalidMFGCode" xml:space="preserve">
    <value>Code de fabrication non valide</value>
  </data>
  <data name="ResMsgInvalidPartAssemblyNo" xml:space="preserve">
    <value>Numéro d'assemblage de pièce non valide</value>
  </data>
  <data name="ResMsgInvalidPartDescription" xml:space="preserve">
    <value>Description de la pièce non valide</value>
  </data>
  <data name="ResMsgInvalidPartNO" xml:space="preserve">
    <value>Numéro de pièce invalide ou pièce non publiée</value>
  </data>
  <data name="ResMsgInvalidReferencePartNumber" xml:space="preserve">
    <value>Numéro de référence de la pièce non valide</value>
  </data>
  <data name="ResMsgInvalidVendorCode" xml:space="preserve">
    <value>Code fournisseur non valide</value>
  </data>
  <data name="ResMsgInvalidVendorName" xml:space="preserve">
    <value>Nom de fournisseur non valide</value>
  </data>
  <data name="ResMsgItemNoAlreadyExists" xml:space="preserve">
    <value>Le numéro d'article existe déjà</value>
  </data>
  <data name="ResMsgItemNoCannotBeEmpty" xml:space="preserve">
    <value>Le numéro d'article ne peut pas être vide</value>
  </data>
  <data name="ResmsgItemnumberNotFound" xml:space="preserve">
    <value>Numéro d'article introuvable</value>
  </data>
  <data name="ResMsgLoading" xml:space="preserve">
    <value>Chargement</value>
  </data>
  <data name="Resmsglocalremarksexisreplace" xml:space="preserve">
    <value>Des remarques locales existent déjà, souhaitez-vous les remplacer?</value>
  </data>
  <data name="ResMsgMFGCodeCannotBeEmpty" xml:space="preserve">
    <value>Le code de fabrication ne peut pas être vide</value>
  </data>
  <data name="resmsgnImageSavedSuccessfully" xml:space="preserve">
    <value>Image enregistrée avec succès</value>
  </data>
  <data name="Resmsgnolocalremarks" xml:space="preserve">
    <value>L'assemblage actuel n'a pas de remarques locales</value>
  </data>
  <data name="resmsgnOriginalDatafound" xml:space="preserve">
    <value>Aucune donnée d'origine trouvée pour cet assemblage</value>
  </data>
  <data name="resmsgnOriginalRestored" xml:space="preserve">
    <value>Données d'origine restaurées</value>
  </data>
  <data name="ResMsgNotUsedInAnyAssembly" xml:space="preserve">
    <value>Non utilisé dans aucun assemblage</value>
  </data>
  <data name="ResmsgnoVinrecordsfound" xml:space="preserve">
    <value>Aucun enregistrement de numéro d'identification de véhicule trouvé</value>
  </data>
  <data name="ResMsgOnlyreplaceapartbyapartORanassemblybyanassembly" xml:space="preserve">
    <value>Vous pouvez remplacer une pièce par une pièce OU un assemblage par un assemblage</value>
  </data>
  <data name="ResMsgOrderNumber" xml:space="preserve">
    <value>Entrez le numéro de commande</value>
  </data>
  <data name="ResmsgOriginalDataFound" xml:space="preserve">
    <value>Données originales trouvées</value>
  </data>
  <data name="ResMsgPartNumberCannotBeEmpty" xml:space="preserve">
    <value>Le numéro de pièce ne peut pas être vide</value>
  </data>
  <data name="ResMsgPartNumberShouldbemandatory" xml:space="preserve">
    <value>Le numéro de pièce doit être obligatoire</value>
  </data>
  <data name="ResmsgPleaseAddAnotherImage" xml:space="preserve">
    <value>Veuillez essayer d'ajouter une autre image</value>
  </data>
  <data name="ResMsgPleaseenterpartno" xml:space="preserve">
    <value>Veuillez saisir le numéro de pièce / d'assemblage</value>
  </data>
  <data name="ResMsgPleaseenterpartOrAssemblyno" xml:space="preserve">
    <value>Veuillez saisir le numéro de pièce / d'assemblage</value>
  </data>
  <data name="ResMsgPleaseSelectColumn" xml:space="preserve">
    <value>Veuillez sélectionner une colonne</value>
  </data>
  <data name="ResMsgPleaseSelectOperator" xml:space="preserve">
    <value>Veuillez sélectionner un opérateur</value>
  </data>
  <data name="ResMsgPlsFillMandatoryFields" xml:space="preserve">
    <value>Veuillez remplir tous les champs obligatoires</value>
  </data>
  <data name="ResMsgProductTypeCode" xml:space="preserve">
    <value>Entrez le code de type de produit</value>
  </data>
  <data name="ResMsgProductTypeCode1" xml:space="preserve">
    <value>Entrez le code de type de produit</value>
  </data>
  <data name="ResmsgRecordissafe" xml:space="preserve">
    <value>L'enregistrement est sûr</value>
  </data>
  <data name="ResMsgReEnterPassword" xml:space="preserve">
    <value>Retaper le mot de passe</value>
  </data>
  <data name="ResMsgReEnterPassword1" xml:space="preserve">
    <value>Retaper le mot de passe</value>
  </data>
  <data name="ResmsgRemove" xml:space="preserve">
    <value>Retirer</value>
  </data>
  <data name="ResmsgReplace" xml:space="preserve">
    <value>Remplacer</value>
  </data>
  <data name="ResmsgReplaceBookmark" xml:space="preserve">
    <value>Remplacer les remarques locales</value>
  </data>
  <data name="ResmsgReplaceImage" xml:space="preserve">
    <value>Oui Remplacer</value>
  </data>
  <data name="ResMsgReplacePartalreadypresentinSameBOM" xml:space="preserve">
    <value>Remplacer la pièce déjà présente dans la même nomenclature</value>
  </data>
  <data name="ResMsgReplacePartalreadypresentwithintheSameAssembly" xml:space="preserve">
    <value>Remplacer la pièce déjà présente dans le même assemblage</value>
  </data>
  <data name="ResmsgrestoreOriginal" xml:space="preserve">
    <value>Restaurer l'original</value>
  </data>
  <data name="ResMsgSelectAttachmentType" xml:space="preserve">
    <value>Sélectionnez le type de document joint</value>
  </data>
  <data name="ResMsgSelectAttachmentType1" xml:space="preserve">
    <value>Sélectionnez le type de document joint</value>
  </data>
  <data name="ResMsgSelectCountry" xml:space="preserve">
    <value>Choisissez le pays</value>
  </data>
  <data name="ResMsgSelectErrorType" xml:space="preserve">
    <value>Sélectionnez le type d'erreur</value>
  </data>
  <data name="ResMsgSelectErrorType1" xml:space="preserve">
    <value>Sélectionnez le type d'erreur</value>
  </data>
  <data name="ResMsgSelectFG" xml:space="preserve">
    <value>Sélectionnez un groupe de fonctions</value>
  </data>
  <data name="ResMsgSelectFG1" xml:space="preserve">
    <value>Sélectionnez un groupe de fonctions</value>
  </data>
  <data name="ResMsgSelectFontSize" xml:space="preserve">
    <value>Sélectionnez la taille de la police de caratère</value>
  </data>
  <data name="ResMsgSelectFontSize1" xml:space="preserve">
    <value>Sélectionnez la taille de la police de caratère</value>
  </data>
  <data name="ResMsgSelectFontStyle" xml:space="preserve">
    <value>Sélectionnez un style de police de caratère</value>
  </data>
  <data name="ResMsgSelectFontStyle1" xml:space="preserve">
    <value>Sélectionnez un style de police de caratère</value>
  </data>
  <data name="ResMsgSelectFontTemplate" xml:space="preserve">
    <value>Sélectionnez un modèle de police de caratère</value>
  </data>
  <data name="ResMsgSelectLanguage" xml:space="preserve">
    <value>Choisir la langue</value>
  </data>
  <data name="ResMsgSelectLanguage1" xml:space="preserve">
    <value>Choisir la langue</value>
  </data>
  <data name="ResMsgSelectMFGCode" xml:space="preserve">
    <value>Sélectionnez le code de fabrication</value>
  </data>
  <data name="ResMsgSelectMFGCode1" xml:space="preserve">
    <value>Sélectionnez le code de fabrication</value>
  </data>
  <data name="ResMsgSelectQuery" xml:space="preserve">
    <value>Sélectionner une requête</value>
  </data>
  <data name="ResMsgSelectQuery1" xml:space="preserve">
    <value>Sélectionner une requête</value>
  </data>
  <data name="ResMsgselectrecordstodelete" xml:space="preserve">
    <value>Veuillez sélectionner les enregistrements à supprimer</value>
  </data>
  <data name="ResMsgSelectScreen" xml:space="preserve">
    <value>Sélection d'écran</value>
  </data>
  <data name="ResMsgSelectState" xml:space="preserve">
    <value>Sélectionnez l'état / province</value>
  </data>
  <data name="ResMsgSelectToDelete" xml:space="preserve">
    <value>Sélectionnez les enregistrements à supprimer</value>
  </data>
  <data name="ResMsgSelectUOM" xml:space="preserve">
    <value>Sélectionnez l'unité de mesure</value>
  </data>
  <data name="ResMsgSelectUOM1" xml:space="preserve">
    <value>Sélectionnez l'unité de mesure</value>
  </data>
  <data name="ResMsgSequencealreadyExists" xml:space="preserve">
    <value>La séquence existe déjà</value>
  </data>
  <data name="ResMsgSequenceShouldnotexceedsMorethan255" xml:space="preserve">
    <value>La séquence ne doit pas dépasser plus de 255</value>
  </data>
  <data name="ResMsgShipToAddress" xml:space="preserve">
    <value>Entrez l'adresse de livraison</value>
  </data>
  <data name="ResMsgSpecialInstructions" xml:space="preserve">
    <value>Entrez des instructions spéciales</value>
  </data>
  <data name="ResMsgToDate" xml:space="preserve">
    <value>À ce jour</value>
  </data>
  <data name="ResMsgVendorCodeCannotBeEmpty" xml:space="preserve">
    <value>Le code du fournisseur ne peut pas être vide</value>
  </data>
  <data name="ResmsgVendorDoesNotExist" xml:space="preserve">
    <value>Le fournisseur n'existe pas</value>
  </data>
  <data name="ResMsgVendorMfgCodeCannotBeEmpty" xml:space="preserve">
    <value>Le code de fabrication du fournisseur ne peut pas être vide</value>
  </data>
  <data name="ResMsgVendorNameCannotBeEmpty" xml:space="preserve">
    <value>Le nom du fournisseur ne peut pas être vide</value>
  </data>
  <data name="ResMsgVendorParmaCodeCannotBeEmpty" xml:space="preserve">
    <value>Le code Parma du fournisseur ne peut pas être vide</value>
  </data>
  <data name="ResMsgVendorPartNumberCannotBeEmpty" xml:space="preserve">
    <value>Le numéro de pièce du fournisseur ne peut pas être vide</value>
  </data>
  <data name="ResMsgViewAssemblyImg" xml:space="preserve">
    <value>Afficher l'image de l'assemblage</value>
  </data>
  <data name="ResMsgViewAssemblyImg1" xml:space="preserve">
    <value>Afficher l'image de l'assemblage</value>
  </data>
  <data name="ResMsgVIN" xml:space="preserve">
    <value> NIV (Numéro d'Identification du Véhicule) </value>
  </data>
  <data name="ResMsgYoucannotaddsamepartasreferencepart" xml:space="preserve">
    <value>Impossible d'ajouter la même pièce comme pièce de référence</value>
  </data>
  <data name="ResMultipleDeleteHotspot" xml:space="preserve">
    <value>Suppression de plusieurs Hotspot</value>
  </data>
  <data name="ResMultiplePartsSelection" xml:space="preserve">
    <value>Sélection de plusieurs pièces</value>
  </data>
  <data name="ResMultiplePartsSelection1" xml:space="preserve">
    <value>Sélection de plusieurs pièces</value>
  </data>
  <data name="ResName" xml:space="preserve">
    <value>Nom</value>
  </data>
  <data name="ResName1" xml:space="preserve">
    <value>Nom</value>
  </data>
  <data name="ResNewCartAddedSuccessfully" xml:space="preserve">
    <value>Nouveau panier ajouté avec succès</value>
  </data>
  <data name="ResNewCodes" xml:space="preserve">
    <value>Nouveaux codes</value>
  </data>
  <data name="ResNewCodes1" xml:space="preserve">
    <value>Nouveaux codes</value>
  </data>
  <data name="ResNewDrawing#" xml:space="preserve">
    <value>Nouveau numéro de dessin</value>
  </data>
  <data name="ResNewDrawing#1" xml:space="preserve">
    <value>Nouveau numéro de dessin</value>
  </data>
  <data name="ResNewMFGCode" xml:space="preserve">
    <value>Nouveau code de fabrication</value>
  </data>
  <data name="ResNewOwner" xml:space="preserve">
    <value>Nouveau propriétaire</value>
  </data>
  <data name="ResNewOwner1" xml:space="preserve">
    <value>Nouveau propriétaire</value>
  </data>
  <data name="ResNewOwnerName" xml:space="preserve">
    <value>Nom du nouveau propriétaire</value>
  </data>
  <data name="ResNewPart#" xml:space="preserve">
    <value>Nouveau numéro de pièce</value>
  </data>
  <data name="ResNewPart#1" xml:space="preserve">
    <value>Nouveau numéro de pièce</value>
  </data>
  <data name="ResNewPassword" xml:space="preserve">
    <value>Nouveau mot de passe</value>
  </data>
  <data name="ResNewPassword1" xml:space="preserve">
    <value>Nouveau mot de passe</value>
  </data>
  <data name="ResNewRoad#" xml:space="preserve">
    <value>Nouveau numéro de route</value>
  </data>
  <data name="ResNewRoad#1" xml:space="preserve">
    <value>Nouveau numéro de route</value>
  </data>
  <data name="ResNewTemplateName" xml:space="preserve">
    <value>Nouveau nom de police de caractère</value>
  </data>
  <data name="ResNewVIN#" xml:space="preserve">
    <value>Nouveau NIV (Numéro d'Identification du Véhicule)</value>
  </data>
  <data name="ResNewVIN#1" xml:space="preserve">
    <value>Nouveau NIV (Numéro d'Identification du Véhicule)</value>
  </data>
  <data name="ResNewVINS" xml:space="preserve">
    <value>FN-New Owner VINs</value>
  </data>
  <data name="ResNext" xml:space="preserve">
    <value>Prochain</value>
  </data>
  <data name="ResNextAssembly" xml:space="preserve">
    <value>Assemblage suivant</value>
  </data>
  <data name="ResNextImage" xml:space="preserve">
    <value>Image suivante</value>
  </data>
  <data name="ResNextPage" xml:space="preserve">
    <value>Page suivante</value>
  </data>
  <data name="ResNextReleaseInformation" xml:space="preserve">
    <value>Informations sur la prochaine version</value>
  </data>
  <data name="ResNextReleseInformation" xml:space="preserve">
    <value>Informations sur la prochaine version</value>
  </data>
  <data name="ResNextstopbuttonposition" xml:space="preserve">
    <value>Position du bouton d'arrêt suivant</value>
  </data>
  <data name="ResNo" xml:space="preserve">
    <value>Non</value>
  </data>
  <data name="ResNo1" xml:space="preserve">
    <value>Non</value>
  </data>
  <data name="ResNoDrawing" xml:space="preserve">
    <value>Pas de dessin</value>
  </data>
  <data name="ResNoFileChoosen" xml:space="preserve">
    <value>Aucun fichier choisi</value>
  </data>
  <data name="ResNoFunctionGroupFound" xml:space="preserve">
    <value>Aucun groupe de fonctions trouvé</value>
  </data>
  <data name="ResNoImageAvailable" xml:space="preserve">
    <value>Pas d'image disponible</value>
  </data>
  <data name="ResNone" xml:space="preserve">
    <value>Aucun</value>
  </data>
  <data name="ResNone1" xml:space="preserve">
    <value>Aucun</value>
  </data>
  <data name="ResNonEditable" xml:space="preserve">
    <value>Non modifiable</value>
  </data>
  <data name="ResNoNewAssociationSelected" xml:space="preserve">
    <value>Aucune nouvelle association sélectionnée à enregistrer</value>
  </data>
  <data name="ResNoRecordFound" xml:space="preserve">
    <value>Aucun Enregistrement Trouvé</value>
  </data>
  <data name="ResNoRecordFoundtoDelete" xml:space="preserve">
    <value>Aucun enregistrement trouvé à supprimer</value>
  </data>
  <data name="ResNoRecordsAreFoundToAdd" xml:space="preserve">
    <value>Sélectionnez l'enregistrement à ajouter</value>
  </data>
  <data name="ResNoRecordsAreFoundToDelete" xml:space="preserve">
    <value>Aucun enregistrement trouvé à ajouter</value>
  </data>
  <data name="ResNoRecordsAreFoundToLock" xml:space="preserve">
    <value>Aucun enregistrement trouvé à verrouiller</value>
  </data>
  <data name="ResNoRecordsAreFoundToUnLock" xml:space="preserve">
    <value>Aucun enregistrement trouvé à déverrouiller</value>
  </data>
  <data name="ResNorecordstoview" xml:space="preserve">
    <value>Aucun enregistrement à afficher</value>
  </data>
  <data name="ResNoRestrictions" xml:space="preserve">
    <value>Pas de restrictions</value>
  </data>
  <data name="ResNoRestrictions1" xml:space="preserve">
    <value>Pas de restrictions</value>
  </data>
  <data name="ResNotContains" xml:space="preserve">
    <value>Ne contient pas</value>
  </data>
  <data name="ResNotContains1" xml:space="preserve">
    <value>Ne contient pas</value>
  </data>
  <data name="ResNote" xml:space="preserve">
    <value>Note</value>
  </data>
  <data name="ResNotEmpty" xml:space="preserve">
    <value>Pas vide</value>
  </data>
  <data name="ResNotEmpty1" xml:space="preserve">
    <value>Pas vide</value>
  </data>
  <data name="ResNotEqual" xml:space="preserve">
    <value>Pas égal</value>
  </data>
  <data name="ResNotEqual1" xml:space="preserve">
    <value>Pas égal</value>
  </data>
  <data name="ResNotes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="ResNotes1" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="ResNoteText" xml:space="preserve">
    <value>Remarque / texte</value>
  </data>
  <data name="ResNotifications" xml:space="preserve">
    <value>Notifications</value>
  </data>
  <data name="ResNotImportedCount" xml:space="preserve">
    <value>Nombre non importé</value>
  </data>
  <data name="ResNotImportedCount1" xml:space="preserve">
    <value>Nombre non importé</value>
  </data>
  <data name="ResNotInAddMode" xml:space="preserve">
    <value>Pas en mode ajout</value>
  </data>
  <data name="ResNotinAssembly" xml:space="preserve">
    <value>Pas en assemblage</value>
  </data>
  <data name="ResNotinTree" xml:space="preserve">
    <value>Pas dans l'arbre</value>
  </data>
  <data name="Resnova" xml:space="preserve">
    <value>NOVA</value>
  </data>
  <data name="ResNOVABUS" xml:space="preserve">
    <value>NOVABUS</value>
  </data>
  <data name="ResNOVABUS1" xml:space="preserve">
    <value>NOVABUS</value>
  </data>
  <data name="ResNumberOfVehicles" xml:space="preserve">
    <value>Nombre de véhicules</value>
  </data>
  <data name="ResNUMERICAL" xml:space="preserve">
    <value>INDEX NUMÉRIQUE</value>
  </data>
  <data name="ResObject_ID" xml:space="preserve">
    <value>Identification des objets</value>
  </data>
  <data name="ResOCR" xml:space="preserve">
    <value>reconnaissance optique de caractères (ROC)</value>
  </data>
  <data name="ResOCR1" xml:space="preserve">
    <value>reconnaissance optique de caractères (ROC)</value>
  </data>
  <data name="ResOf" xml:space="preserve">
    <value>De</value>
  </data>
  <data name="ResOK" xml:space="preserve">
    <value>D'accord</value>
  </data>
  <data name="ResOldDrawing#" xml:space="preserve">
    <value>Ancien numéro de dessin</value>
  </data>
  <data name="ResOldDrawing#1" xml:space="preserve">
    <value>Ancien numéro de dessin</value>
  </data>
  <data name="ResOldMFGCode" xml:space="preserve">
    <value>Ancien code de fabrication</value>
  </data>
  <data name="ResOldOwnerName" xml:space="preserve">
    <value>Nom de l'ancien propriétaire</value>
  </data>
  <data name="ResOldPart#" xml:space="preserve">
    <value>Ancien numéro de pièce</value>
  </data>
  <data name="ResOldPart#1" xml:space="preserve">
    <value>Ancien numéro de pièce</value>
  </data>
  <data name="ResOldPassword" xml:space="preserve">
    <value>Ancien mot de passe</value>
  </data>
  <data name="ResOldRoad#" xml:space="preserve">
    <value>Ancien numéro de route</value>
  </data>
  <data name="ResOldTemplateName" xml:space="preserve">
    <value>Ancien nom de police de caractère</value>
  </data>
  <data name="ResOne" xml:space="preserve">
    <value>Un</value>
  </data>
  <data name="ResOne1" xml:space="preserve">
    <value>Un 1</value>
  </data>
  <data name="ResOne10" xml:space="preserve">
    <value>Un 10</value>
  </data>
  <data name="ResOne2" xml:space="preserve">
    <value>Un 2</value>
  </data>
  <data name="ResOne3" xml:space="preserve">
    <value>Un 3</value>
  </data>
  <data name="ResOne4" xml:space="preserve">
    <value>Un 4</value>
  </data>
  <data name="ResOne5" xml:space="preserve">
    <value>Un 5</value>
  </data>
  <data name="ResOne6" xml:space="preserve">
    <value>Un 6</value>
  </data>
  <data name="ResOne7" xml:space="preserve">
    <value>Un 7</value>
  </data>
  <data name="ResOne8" xml:space="preserve">
    <value>Un 8</value>
  </data>
  <data name="ResOne9" xml:space="preserve">
    <value>Un 9</value>
  </data>
  <data name="Resonetomany" xml:space="preserve">
    <value>Un à plusieurs</value>
  </data>
  <data name="Resonetoone" xml:space="preserve">
    <value>Un par un</value>
  </data>
  <data name="ResOpen" xml:space="preserve">
    <value>Ouvert</value>
  </data>
  <data name="ResOpen1" xml:space="preserve">
    <value>Ouvert</value>
  </data>
  <data name="ResOperator" xml:space="preserve">
    <value>Opérateur</value>
  </data>
  <data name="ResOperator1" xml:space="preserve">
    <value>Opérateur</value>
  </data>
  <data name="ResOperatorsManual" xml:space="preserve">
    <value>Manuel de l'opérateur</value>
  </data>
  <data name="ResOptionCode" xml:space="preserve">
    <value>Code d'option</value>
  </data>
  <data name="ResOptionCode1" xml:space="preserve">
    <value>Code d'option</value>
  </data>
  <data name="ResOptionDescription" xml:space="preserve">
    <value>Description de l'option</value>
  </data>
  <data name="ResOptionDescription1" xml:space="preserve">
    <value>Description de l'option</value>
  </data>
  <data name="ResOptionDescriptionLanguage-List" xml:space="preserve">
    <value>Description de l'option Langue - Liste</value>
  </data>
  <data name="ResOptionDescriptionLanguage-List1" xml:space="preserve">
    <value>Description de l'option Langue - Liste</value>
  </data>
  <data name="ResOptionDescriptionList" xml:space="preserve">
    <value>Liste Description de l'option  </value>
  </data>
  <data name="ResOptionDescriptionList1" xml:space="preserve">
    <value>Liste Description de l'option  </value>
  </data>
  <data name="ResOptionDescriptionUpdation" xml:space="preserve">
    <value>Mise à jour Description de l'option  </value>
  </data>
  <data name="ResOptionDescriptionUpdation1" xml:space="preserve">
    <value>Mise à jour Description de l'option  </value>
  </data>
  <data name="ResOptionDetails" xml:space="preserve">
    <value>Détails de l'option</value>
  </data>
  <data name="ResOptionEntryCantbeblank" xml:space="preserve">
    <value>L'entrée d'option ne peut pas être vide</value>
  </data>
  <data name="ResOptionIsActive?" xml:space="preserve">
    <value>L'option est active?</value>
  </data>
  <data name="ResOptionLanguage-Add" xml:space="preserve">
    <value>Option Langue - Ajouter</value>
  </data>
  <data name="ResOptionLanguage-Add1" xml:space="preserve">
    <value>Option Langue - Ajouter</value>
  </data>
  <data name="ResOptionLanguage-Edit" xml:space="preserve">
    <value>Option Langue - Modifier</value>
  </data>
  <data name="ResOptionLanguage-Edit1" xml:space="preserve">
    <value>Option Langue - Modifier</value>
  </data>
  <data name="ResOptionLanguageList" xml:space="preserve">
    <value>Option Langue - Liste</value>
  </data>
  <data name="ResOptionList" xml:space="preserve">
    <value>Liste d'options</value>
  </data>
  <data name="ResOptionList1" xml:space="preserve">
    <value>Liste d'options</value>
  </data>
  <data name="ResOptionRemarks" xml:space="preserve">
    <value>Remarques Options </value>
  </data>
  <data name="ResOptions" xml:space="preserve">
    <value>Options</value>
  </data>
  <data name="ResOptions1" xml:space="preserve">
    <value>Options</value>
  </data>
  <data name="ResOptionsDetailsCannotbeEmpty" xml:space="preserve">
    <value>Les détails de l'option ne peuvent pas être vides</value>
  </data>
  <data name="ResOptionSectionAssociation" xml:space="preserve">
    <value>Association de section d'options</value>
  </data>
  <data name="ResOptionSectionAssociation1" xml:space="preserve">
    <value>Association de section d'options</value>
  </data>
  <data name="ResOptionsisNotYetAssociated" xml:space="preserve">
    <value>Les options ne sont pas encore associées</value>
  </data>
  <data name="ResOR" xml:space="preserve">
    <value>OU</value>
  </data>
  <data name="ResOR1" xml:space="preserve">
    <value>OU</value>
  </data>
  <data name="ResOrder" xml:space="preserve">
    <value>commande</value>
  </data>
  <data name="ResOrder#" xml:space="preserve">
    <value>Numéro de commande</value>
  </data>
  <data name="ResOrder#1" xml:space="preserve">
    <value>Numéro de commande</value>
  </data>
  <data name="ResOrder#11" xml:space="preserve">
    <value>Numéro de commande</value>
  </data>
  <data name="ResOrder#2" xml:space="preserve">
    <value>Numéro de commande</value>
  </data>
  <data name="ResOrder#SearchList" xml:space="preserve">
    <value>Liste de recherche Numéro de commande</value>
  </data>
  <data name="ResOrder#SearchList1" xml:space="preserve">
    <value>Liste de recherche Numéro de commande</value>
  </data>
  <data name="ResOrderDate" xml:space="preserve">
    <value>Date de commande</value>
  </data>
  <data name="ResOrderDate1" xml:space="preserve">
    <value>Date de commande</value>
  </data>
  <data name="ResOrderDetails" xml:space="preserve">
    <value>Détails de la commande</value>
  </data>
  <data name="ResOrderDetails1" xml:space="preserve">
    <value>Détails de la commande</value>
  </data>
  <data name="ResOrderesQty" xml:space="preserve">
    <value>Quantité commandée</value>
  </data>
  <data name="ResOrderList" xml:space="preserve">
    <value>Liste de commande</value>
  </data>
  <data name="ResOrderQty" xml:space="preserve">
    <value>Quantité de commande</value>
  </data>
  <data name="ResOrderQty1" xml:space="preserve">
    <value>Quantité de commande</value>
  </data>
  <data name="ResOrderQtyXML" xml:space="preserve">
    <value>Quantite_de_commande</value>
  </data>
  <data name="ResOrderQuantity" xml:space="preserve">
    <value>Quantité de commande</value>
  </data>
  <data name="ResOrders" xml:space="preserve">
    <value>Numéros de commande</value>
  </data>
  <data name="ResOrderSearch" xml:space="preserve">
    <value>Liste de recherche de commande</value>
  </data>
  <data name="ResOrderSearch1" xml:space="preserve">
    <value>Liste de recherche de commande</value>
  </data>
  <data name="ResOrdersSelection" xml:space="preserve">
    <value>Sélection des commandes</value>
  </data>
  <data name="ResOrdersVINSelection" xml:space="preserve">
    <value>Sélection Commande/NIV</value>
  </data>
  <data name="ResOrderToplevel" xml:space="preserve">
    <value>Numéro de commande / niveau supérieur</value>
  </data>
  <data name="ResOriginalColumnName" xml:space="preserve">
    <value>Nom de la colonne d'origine</value>
  </data>
  <data name="ResOwner" xml:space="preserve">
    <value>Propriétaire</value>
  </data>
  <data name="ResOwnershipHistory" xml:space="preserve">
    <value>Historique de propriété</value>
  </data>
  <data name="ResOwnershipHistory1" xml:space="preserve">
    <value>Historique de propriété</value>
  </data>
  <data name="ResPage" xml:space="preserve">
    <value>Page</value>
  </data>
  <data name="ResPanning" xml:space="preserve">
    <value>Panoramique</value>
  </data>
  <data name="ResPanning1" xml:space="preserve">
    <value>Panoramique</value>
  </data>
  <data name="Respanning2" xml:space="preserve">
    <value>Panoramique de la résolution</value>
  </data>
  <data name="ResParent" xml:space="preserve">
    <value>parente</value>
  </data>
  <data name="ResParent1" xml:space="preserve">
    <value>Parent</value>
  </data>
  <data name="ResParentDescriptions" xml:space="preserve">
    <value>Descriptions des parents</value>
  </data>
  <data name="ResParentDetails" xml:space="preserve">
    <value>Détails Parent</value>
  </data>
  <data name="ResParentDetails1" xml:space="preserve">
    <value>Détails Parent</value>
  </data>
  <data name="ResParentMFGCode" xml:space="preserve">
    <value>Code de fabrication Parent</value>
  </data>
  <data name="ResParentPart#" xml:space="preserve">
    <value>Assemblée des parents #</value>
  </data>
  <data name="ResParentPartDetails" xml:space="preserve">
    <value>Détails de la pièce parente</value>
  </data>
  <data name="ResParentPartDetails1" xml:space="preserve">
    <value>Détails de la pièce parente</value>
  </data>
  <data name="ResParmaCode" xml:space="preserve">
    <value>Code Parma</value>
  </data>
  <data name="ResParmaCode1" xml:space="preserve">
    <value>Code Parma</value>
  </data>
  <data name="ResPart#" xml:space="preserve">
    <value>Numéro de la pièce</value>
  </data>
  <data name="ResPart#1" xml:space="preserve">
    <value>Numéro de la pièce</value>
  </data>
  <data name="ResPart#XML" xml:space="preserve">
    <value>Numero_d_Assemblage</value>
  </data>
  <data name="ResPartAdd" xml:space="preserve">
    <value>Ajout de pièce</value>
  </data>
  <data name="ResPartAlreadyExistsInCart" xml:space="preserve">
    <value>Pièce déjà disponible dans le panier</value>
  </data>
  <data name="ResPartAssembly#" xml:space="preserve">
    <value>Numéro de pièce / assemblage</value>
  </data>
  <data name="ResPartAssembly#1" xml:space="preserve">
    <value>Numéro de pièce / assemblage</value>
  </data>
  <data name="ResPartDescription" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="ResPartDescription1" xml:space="preserve">
    <value>Description de la pièce</value>
  </data>
  <data name="ResPartDescriptionLanguageList" xml:space="preserve">
    <value>Liste des langues de description de pièce</value>
  </data>
  <data name="ResPartDetails" xml:space="preserve">
    <value>Détails de la pièce</value>
  </data>
  <data name="ResPartDetails1" xml:space="preserve">
    <value>Détails de la pièce</value>
  </data>
  <data name="ResPartDetailsAdd" xml:space="preserve">
    <value>Ajouter les détails de la pièce</value>
  </data>
  <data name="ResPartDetailsAdd1" xml:space="preserve">
    <value>Ajouter les détails de la pièce</value>
  </data>
  <data name="ResPartDetailsEdit" xml:space="preserve">
    <value>Modifier Détails de la pièce </value>
  </data>
  <data name="ResPartDetailsXML" xml:space="preserve">
    <value>Details_de_la_piece</value>
  </data>
  <data name="ResPartDocuments" xml:space="preserve">
    <value>Documents de pièce</value>
  </data>
  <data name="ResPartDocuments1" xml:space="preserve">
    <value>Documents de pièce</value>
  </data>
  <data name="ResPartImage" xml:space="preserve">
    <value>Image de la pièce</value>
  </data>
  <data name="ResPartImage1" xml:space="preserve">
    <value>Image de la pièce</value>
  </data>
  <data name="ResPartImages" xml:space="preserve">
    <value>Images de la pièce</value>
  </data>
  <data name="ResPartImages1" xml:space="preserve">
    <value>Images de la pièce</value>
  </data>
  <data name="ResPartList" xml:space="preserve">
    <value>Liste des pièces</value>
  </data>
  <data name="ResPartList1" xml:space="preserve">
    <value>Liste des pièces</value>
  </data>
  <data name="ResPartName" xml:space="preserve">
    <value>Nom de la pièce</value>
  </data>
  <data name="ResPartName1" xml:space="preserve">
    <value>Nom de la pièce</value>
  </data>
  <data name="ResPartNoCannotBeEmpty" xml:space="preserve">
    <value>Le numéro de pièce ne peut pas être vide</value>
  </data>
  <data name="ResPartNotes" xml:space="preserve">
    <value>Notes de pièce</value>
  </data>
  <data name="ResPartNotes1" xml:space="preserve">
    <value>Notes de pièce</value>
  </data>
  <data name="ResPartNotFound" xml:space="preserve">
    <value>Pièce non trouvée</value>
  </data>
  <data name="ResPartnumber" xml:space="preserve">
    <value>Numéro de la pièce</value>
  </data>
  <data name="ResPartNumberDetails" xml:space="preserve">
    <value>Détails de la pièce</value>
  </data>
  <data name="ResPartNumberisAlredaySuperseded" xml:space="preserve">
    <value>Le numéro de pièce est déjà remplacé</value>
  </data>
  <data name="ResParts" xml:space="preserve">
    <value>Pièces</value>
  </data>
  <data name="ResParts&amp;ServiceSection?" xml:space="preserve">
    <value>Est la section des pièces et service?</value>
  </data>
  <data name="ResParts1" xml:space="preserve">
    <value>Pièces</value>
  </data>
  <data name="ResPartsAssist" xml:space="preserve">
    <value>Parts Assist</value>
  </data>
  <data name="ResPartsDescriptionUpdation" xml:space="preserve">
    <value>Mise à jour de la description des pièces</value>
  </data>
  <data name="ResPartsDescriptionUpdation1" xml:space="preserve">
    <value>Mise à jour de la description des pièces</value>
  </data>
  <data name="ResPartsDetailsCannotbeEmpty" xml:space="preserve">
    <value>Les détails des pièces ne peuvent pas être vides</value>
  </data>
  <data name="ResPartSearchList" xml:space="preserve">
    <value>Liste de recherche de pièces</value>
  </data>
  <data name="ResPartServiceDocumentLink" xml:space="preserve">
    <value>Lien de document de service pièce</value>
  </data>
  <data name="ResPartServiceDocumentLink1" xml:space="preserve">
    <value>Lien de document de service pièce</value>
  </data>
  <data name="ResPartsImageUpload" xml:space="preserve">
    <value>Téléchargement d'image de pièces</value>
  </data>
  <data name="ResPartsImageUpload1" xml:space="preserve">
    <value>Téléchargement d'image de pièces</value>
  </data>
  <data name="ResPartsImport" xml:space="preserve">
    <value>Importation de pièces</value>
  </data>
  <data name="ResPartsImport1" xml:space="preserve">
    <value>Importation de pièces</value>
  </data>
  <data name="ResPartsInformation" xml:space="preserve">
    <value>Informations sur les pièces</value>
  </data>
  <data name="ResPartsInformation1" xml:space="preserve">
    <value>Informations sur les pièces</value>
  </data>
  <data name="ResPartsInShoppingCart" xml:space="preserve">
    <value>Pièces dans le panier</value>
  </data>
  <data name="ResPartsManuals" xml:space="preserve">
    <value>Manuels de pièces</value>
  </data>
  <data name="ResPartsMaster" xml:space="preserve">
    <value>Pièces Maître</value>
  </data>
  <data name="ResPartsMaster-CustomerView" xml:space="preserve">
    <value>Pièces Maître - Vue client</value>
  </data>
  <data name="ResPartsMaster-CustomerView1" xml:space="preserve">
    <value>Pièces Maître - Vue client</value>
  </data>
  <data name="ResPartsMaster-Header" xml:space="preserve">
    <value>Pièces Maître - Entête</value>
  </data>
  <data name="ResPartsMaster-Header1" xml:space="preserve">
    <value>Pièces Maître - Entête</value>
  </data>
  <data name="ResPartsMaster1" xml:space="preserve">
    <value>Pièces Maître</value>
  </data>
  <data name="ResPartsSectionAssociation" xml:space="preserve">
    <value>Association de la section des pièces</value>
  </data>
  <data name="ResPartsSectionAssociation1" xml:space="preserve">
    <value>Association de la section des pièces</value>
  </data>
  <data name="ResPartStatus" xml:space="preserve">
    <value>État de la pièce</value>
  </data>
  <data name="ResPartsUsedReport" xml:space="preserve">
    <value>Rapport sur les pièces utilisées</value>
  </data>
  <data name="ResPartsViewConfig" xml:space="preserve">
    <value>Configuration de la vue des pièces</value>
  </data>
  <data name="ResPartsViewConfig1" xml:space="preserve">
    <value>Configuration de la vue des pièces</value>
  </data>
  <data name="ResPartsViewConfiguration-List" xml:space="preserve">
    <value>Configuration de la vue des pièces - Liste</value>
  </data>
  <data name="ResPartsViewConfiguration-List1" xml:space="preserve">
    <value>Configuration de la vue des pièces - Liste</value>
  </data>
  <data name="ResPartsViewConfigure-Edit" xml:space="preserve">
    <value>Configuration de la vue des pièces - Modifier</value>
  </data>
  <data name="ResPartsViewConfigure-Edit1" xml:space="preserve">
    <value>Configuration de la vue des pièces - Modifier</value>
  </data>
  <data name="ResPartUsageReport" xml:space="preserve">
    <value>Rapport d'utilisation de pièce</value>
  </data>
  <data name="ResPartUsedReport" xml:space="preserve">
    <value>Rapport de pièce utilisée</value>
  </data>
  <data name="ResPartWhereUsed" xml:space="preserve">
    <value>Où pièce est utilisée</value>
  </data>
  <data name="ResPassword" xml:space="preserve">
    <value>Mot de passe</value>
  </data>
  <data name="ResPassword1" xml:space="preserve">
    <value>Mot de passe</value>
  </data>
  <data name="ResPCodes" xml:space="preserve">
    <value>Codes P</value>
  </data>
  <data name="ResPDF" xml:space="preserve">
    <value>PDF</value>
  </data>
  <data name="ResPDFConfigure" xml:space="preserve">
    <value>Configurer PDF </value>
  </data>
  <data name="ResPDFPublish" xml:space="preserve">
    <value>Publier PDF</value>
  </data>
  <data name="ResPDFTemplate" xml:space="preserve">
    <value>Modèle PDF</value>
  </data>
  <data name="ResPDFTemplate1" xml:space="preserve">
    <value>Modèle PDF</value>
  </data>
  <data name="ResPentagon" xml:space="preserve">
    <value>Pentagone</value>
  </data>
  <data name="ResPharmaCode" xml:space="preserve">
    <value>Code Parma</value>
  </data>
  <data name="ResPhone" xml:space="preserve">
    <value>Téléphone</value>
  </data>
  <data name="ResPhone1" xml:space="preserve">
    <value>Téléphone1</value>
  </data>
  <data name="ResPhone2" xml:space="preserve">
    <value>Téléphone2</value>
  </data>
  <data name="ResPleaseconsultit" xml:space="preserve">
    <value>Veuillez S.V.P. le consulter.</value>
  </data>
  <data name="ResPleaseenterlogininformation" xml:space="preserve">
    <value>S'il vous plaît entrer vos informations de connexion</value>
  </data>
  <data name="ResPleaseEnterRegisteredEmailID" xml:space="preserve">
    <value>Veuillez saisir votre identifiant de courrier électronique enregistré</value>
  </data>
  <data name="ResPleaseenterTimeinHH" xml:space="preserve">
    <value>Veuillez saisir l'heure au format HH: MM Ex: 00: 10</value>
  </data>
  <data name="ResPleaseEntervalidOptions" xml:space="preserve">
    <value>FN-Please enter valid options</value>
  </data>
  <data name="ResPleasefillmandatoryfileds" xml:space="preserve">
    <value>Veuillez remplir les champs obligatoires</value>
  </data>
  <data name="ResPleasefindtheorderedlistofpartsinbelowtable" xml:space="preserve">
    <value>Veuillez trouver la liste des pièces commandées dans le tableau ci-dessous</value>
  </data>
  <data name="ResPleaseselectcheckbox" xml:space="preserve">
    <value>Veuillez cocher la case</value>
  </data>
  <data name="ResPleaseSelectEmployee" xml:space="preserve">
    <value>Veuillez sélectionner un employé</value>
  </data>
  <data name="ResPleaseselectFile" xml:space="preserve">
    <value>Veuillez sélectionner un fichier</value>
  </data>
  <data name="ResPleaseSelectheckbox" xml:space="preserve">
    <value>Veuillez cocher la case</value>
  </data>
  <data name="ResPleaseSelectMFGCodeFirst" xml:space="preserve">
    <value>Veuillez d'abord sélectionner le code de fabrication</value>
  </data>
  <data name="ResPleaseSelectTheBrand" xml:space="preserve">
    <value>Veuillez sélectionner le type de marque</value>
  </data>
  <data name="ResPleaseSelectTheRecord" xml:space="preserve">
    <value>Veuillez sélectionner un enregistrement</value>
  </data>
  <data name="ResPleaseSelecttheRecordstoDelete" xml:space="preserve">
    <value>Veuillez sélectionner les enregistrements à supprimer</value>
  </data>
  <data name="ResPleaseSelectValue" xml:space="preserve">
    <value>Veuillez sélectionner une valeur</value>
  </data>
  <data name="ResPlsSelHotSpotToDelete" xml:space="preserve">
    <value>Veuillez sélectionner Hotspot à supprimer</value>
  </data>
  <data name="ResPlsSelHotSpotToMove" xml:space="preserve">
    <value>Veuillez sélectionner Hotspot à déplacer</value>
  </data>
  <data name="ResPlsVerifyBeforePublish" xml:space="preserve">
    <value>Vérifiez le rapport d'erreur avant de publier</value>
  </data>
  <data name="ResPlsWaitWhilewePublish" xml:space="preserve">
    <value>Veuillez patienter pendant que nous publions</value>
  </data>
  <data name="ResPneumaticDiagrams" xml:space="preserve">
    <value>Diagrammes pneumatiques</value>
  </data>
  <data name="ResPramaCode" xml:space="preserve">
    <value>Code Parma</value>
  </data>
  <data name="ResPreceedingAssembly" xml:space="preserve">
    <value>Assemblage précédent</value>
  </data>
  <data name="ResPreceedingPart?" xml:space="preserve">
    <value>Partie précédente?</value>
  </data>
  <data name="ResPreceedingPart?1" xml:space="preserve">
    <value>Partie précédente?</value>
  </data>
  <data name="ResPrefix" xml:space="preserve">
    <value>Préfixe</value>
  </data>
  <data name="ResPrefix1" xml:space="preserve">
    <value>Préfixe</value>
  </data>
  <data name="ResPreview" xml:space="preserve">
    <value>Aperçu</value>
  </data>
  <data name="ResPrevImage" xml:space="preserve">
    <value>Image précédente</value>
  </data>
  <data name="ResPrevious" xml:space="preserve">
    <value>Précédent</value>
  </data>
  <data name="ResPreviousCommonHistoryNotFound" xml:space="preserve">
    <value>Historique commune précédente introuvable</value>
  </data>
  <data name="ResPreviousPurchaseHistory" xml:space="preserve">
    <value>Historique des achats précédents</value>
  </data>
  <data name="ResPrevost" xml:space="preserve">
    <value>Prévost</value>
  </data>
  <data name="ResPrevost1" xml:space="preserve">
    <value>Prévost</value>
  </data>
  <data name="ResPrevPage" xml:space="preserve">
    <value>Page précédente</value>
  </data>
  <data name="ResPrint" xml:space="preserve">
    <value>Impression</value>
  </data>
  <data name="ResPrint1" xml:space="preserve">
    <value>Impression</value>
  </data>
  <data name="ResPrint2" xml:space="preserve">
    <value>Impression</value>
  </data>
  <data name="ResPrintBOM" xml:space="preserve">
    <value>Imprimer la nomenclature</value>
  </data>
  <data name="ResPrintBOM1" xml:space="preserve">
    <value>Imprimer la nomenclature</value>
  </data>
  <data name="ResPrintBoth" xml:space="preserve">
    <value>Imprimer les deux</value>
  </data>
  <data name="ResPrintBoth1" xml:space="preserve">
    <value>Imprimer les deux</value>
  </data>
  <data name="ResPrintCatalogue" xml:space="preserve">
    <value>Imprimer Catalogue </value>
  </data>
  <data name="ResPrintDrawing" xml:space="preserve">
    <value>Imprimer le dessin</value>
  </data>
  <data name="ResPrintDrawing1" xml:space="preserve">
    <value>Imprimer le dessin</value>
  </data>
  <data name="ResProductType" xml:space="preserve">
    <value>Type de produit</value>
  </data>
  <data name="ResProductType-Add" xml:space="preserve">
    <value>Type de produit - Ajouter</value>
  </data>
  <data name="ResProductType-Add1" xml:space="preserve">
    <value>Type de produit - Ajouter</value>
  </data>
  <data name="ResProductType1" xml:space="preserve">
    <value>Type de produit</value>
  </data>
  <data name="ResProductType11" xml:space="preserve">
    <value>Type de produit</value>
  </data>
  <data name="ResProductType2" xml:space="preserve">
    <value>Type de produit</value>
  </data>
  <data name="ResProductTypeAttachments" xml:space="preserve">
    <value>Type de produit - Document joint</value>
  </data>
  <data name="ResProductTypeCode" xml:space="preserve">
    <value>Type de produit - Code </value>
  </data>
  <data name="ResProductTypeCode1" xml:space="preserve">
    <value>Type de produit - Code </value>
  </data>
  <data name="ResProductTypeImages" xml:space="preserve">
    <value>Type de produit - Images</value>
  </data>
  <data name="ResProductTypeImages1" xml:space="preserve">
    <value>Type de produit - Images</value>
  </data>
  <data name="ResProductTypeList" xml:space="preserve">
    <value>Type de produit - Liste</value>
  </data>
  <data name="ResProductTypeList1" xml:space="preserve">
    <value>Type de produit - Liste</value>
  </data>
  <data name="ResProductTypeName" xml:space="preserve">
    <value>Type de produit - Nom</value>
  </data>
  <data name="ResProductTypeName1" xml:space="preserve">
    <value>Type de produit - Nom</value>
  </data>
  <data name="ResProductTypeNameSearchList" xml:space="preserve">
    <value>Type de produit - Liste de recherche par nom</value>
  </data>
  <data name="ResProductTypeNameSearchList1" xml:space="preserve">
    <value>Type de produit - Liste de recherche par nom</value>
  </data>
  <data name="ResProdutTypeList" xml:space="preserve">
    <value>Type de produit - Liste</value>
  </data>
  <data name="ResProdutTypeList1" xml:space="preserve">
    <value>Type de produit - Liste</value>
  </data>
  <data name="ResProgramsandRevisionsList" xml:space="preserve">
    <value>Liste des programmes et des révisions</value>
  </data>
  <data name="ResPublicationandServiceAfterSalesCoordinator" xml:space="preserve">
    <value>Coordonnateur de la publication et du service après-vente</value>
  </data>
  <data name="ResPublish" xml:space="preserve">
    <value>Publier</value>
  </data>
  <data name="ResPublishedDate" xml:space="preserve">
    <value>Date de publication</value>
  </data>
  <data name="ResPublishedDate1" xml:space="preserve">
    <value>Date de publication</value>
  </data>
  <data name="ResPublishList" xml:space="preserve">
    <value>Publier la liste</value>
  </data>
  <data name="ResPublishLog" xml:space="preserve">
    <value>Publier le journal</value>
  </data>
  <data name="ResPublishLog1" xml:space="preserve">
    <value>Publier le journal</value>
  </data>
  <data name="ResQty" xml:space="preserve">
    <value>Quantité</value>
  </data>
  <data name="ResQty1" xml:space="preserve">
    <value>Quantité</value>
  </data>
  <data name="ResQuantity" xml:space="preserve">
    <value>Quantité</value>
  </data>
  <data name="ResQueryName" xml:space="preserve">
    <value>Nom de la requête</value>
  </data>
  <data name="ResQueryName1" xml:space="preserve">
    <value>Nom de la requête</value>
  </data>
  <data name="ResQuotationRequest" xml:space="preserve">
    <value>Demande de devis</value>
  </data>
  <data name="ResQuotationRequestAdd" xml:space="preserve">
    <value>Demande de devis - Ajouter</value>
  </data>
  <data name="ResQuotationRequestedSuccessfully" xml:space="preserve">
    <value>Demande de devis envoyée avec succès</value>
  </data>
  <data name="ResQuotationRequestPart" xml:space="preserve">
    <value>Devis Pièces demandées</value>
  </data>
  <data name="ResRange" xml:space="preserve">
    <value>Intervalle</value>
  </data>
  <data name="ResRange1" xml:space="preserve">
    <value>Intervalle1</value>
  </data>
  <data name="ResRange10" xml:space="preserve">
    <value>Intervalle10</value>
  </data>
  <data name="ResRange2" xml:space="preserve">
    <value>Intervalle2</value>
  </data>
  <data name="ResRange3" xml:space="preserve">
    <value>Intervalle3</value>
  </data>
  <data name="ResRange4" xml:space="preserve">
    <value>Intervalle4</value>
  </data>
  <data name="ResRange5" xml:space="preserve">
    <value>Intervalle5</value>
  </data>
  <data name="ResRange6" xml:space="preserve">
    <value>Intervalle6</value>
  </data>
  <data name="ResRange7" xml:space="preserve">
    <value>Intervalle7</value>
  </data>
  <data name="ResRange8" xml:space="preserve">
    <value>Intervalle8</value>
  </data>
  <data name="ResRange9" xml:space="preserve">
    <value>Intervalle9</value>
  </data>
  <data name="resReachedLastLowOftheGrid" xml:space="preserve">
    <value>A atteint la dernière rangée de la grille</value>
  </data>
  <data name="ResRead" xml:space="preserve">
    <value>Lire</value>
  </data>
  <data name="ResRead1" xml:space="preserve">
    <value>Lire</value>
  </data>
  <data name="ResRec" xml:space="preserve">
    <value>Enregistrer</value>
  </data>
  <data name="ResRec1" xml:space="preserve">
    <value>Enregistrer</value>
  </data>
  <data name="ResRecent" xml:space="preserve">
    <value>Récent</value>
  </data>
  <data name="ResRecommendedPartList" xml:space="preserve">
    <value>Liste des pièces recommandées</value>
  </data>
  <data name="ResRecommendedPartList1" xml:space="preserve">
    <value>Liste des pièces recommandées</value>
  </data>
  <data name="ResRecommendedParts" xml:space="preserve">
    <value>Pièces recommandées</value>
  </data>
  <data name="ResRecommendedParts1" xml:space="preserve">
    <value>Pièces recommandées</value>
  </data>
  <data name="ResRecommendedPartsDetails" xml:space="preserve">
    <value>Détails des pièces recommandées</value>
  </data>
  <data name="ResRecomPart" xml:space="preserve">
    <value>Pièce recommandée</value>
  </data>
  <data name="ResRecomPart1" xml:space="preserve">
    <value>Pièce recommandée</value>
  </data>
  <data name="ResRecordCount" xml:space="preserve">
    <value>Nombre d'enregistrements - </value>
  </data>
  <data name="ResRecordIsLocked" xml:space="preserve">
    <value>L'enregistrement est verrouillé</value>
  </data>
  <data name="ResRectangle" xml:space="preserve">
    <value>Rectangle</value>
  </data>
  <data name="ResRedo" xml:space="preserve">
    <value>Refaire</value>
  </data>
  <data name="ResRedoHotspot" xml:space="preserve">
    <value>Refaire Hotspot</value>
  </data>
  <data name="ResRedoHotspot1" xml:space="preserve">
    <value>Refaire Hotspot</value>
  </data>
  <data name="ResRef.Ass.Description" xml:space="preserve">
    <value>Description assemblage de référence</value>
  </data>
  <data name="ResRef.AssemblyNo" xml:space="preserve">
    <value>Numéro assemblage de référence</value>
  </data>
  <data name="ResRefAssembliesInfo" xml:space="preserve">
    <value>Informations sur les assemblages de référence</value>
  </data>
  <data name="ResRefAssembliesInfo1" xml:space="preserve">
    <value>Informations sur les assemblages de référence</value>
  </data>
  <data name="ResRefAssembliesInfo11" xml:space="preserve">
    <value>Informations sur les assemblages de référence</value>
  </data>
  <data name="ResRefAssembliesInfo2" xml:space="preserve">
    <value>Informations sur les assemblages de référence</value>
  </data>
  <data name="ResRefAssemblyDes" xml:space="preserve">
    <value>Description assemblage de référence</value>
  </data>
  <data name="ResRefAssemblyDes1" xml:space="preserve">
    <value>Description assemblage de référence</value>
  </data>
  <data name="ResRefAssemblyMFGCode" xml:space="preserve">
    <value>Code de fabrication assemblage de référence</value>
  </data>
  <data name="ResRefAssemblyMFGCode1" xml:space="preserve">
    <value>Code de fabrication assemblage de référence</value>
  </data>
  <data name="ResRefAssMFGCode" xml:space="preserve">
    <value>Code de fabrication assemblage de référence</value>
  </data>
  <data name="ResReferenceAssembly" xml:space="preserve">
    <value>Numéro  assemblage de référence</value>
  </data>
  <data name="ResReferenceAssembly#" xml:space="preserve">
    <value>Numéro  assemblage de référence</value>
  </data>
  <data name="ResReferenceAssembly#1" xml:space="preserve">
    <value>Numéro  assemblage de référence</value>
  </data>
  <data name="ResReferenceAssemblyDescription" xml:space="preserve">
    <value>Description assemblage de référence</value>
  </data>
  <data name="ResReferenceAssemblyName" xml:space="preserve">
    <value>Nom assemblage de référence</value>
  </data>
  <data name="ResReferenceAssemblyName1" xml:space="preserve">
    <value>Nom assemblage de référence</value>
  </data>
  <data name="ResRefNo" xml:space="preserve">
    <value>Numéro de référence</value>
  </data>
  <data name="ResRefresh" xml:space="preserve">
    <value>Rafraîchir</value>
  </data>
  <data name="ResReleaseNoteInformation" xml:space="preserve">
    <value>Informations sur la note de publication</value>
  </data>
  <data name="ResRemarks" xml:space="preserve">
    <value>Remarques</value>
  </data>
  <data name="ResRemarks1" xml:space="preserve">
    <value>Remarques</value>
  </data>
  <data name="ResRemarksType" xml:space="preserve">
    <value>Type de remarques</value>
  </data>
  <data name="ResRemove" xml:space="preserve">
    <value>Retirer</value>
  </data>
  <data name="ResRemoveBookmark" xml:space="preserve">
    <value>Supprimer le signet</value>
  </data>
  <data name="ResRemoveFilter" xml:space="preserve">
    <value>Retirer un filtre</value>
  </data>
  <data name="ResRemovefromcart" xml:space="preserve">
    <value>Retirer du panier</value>
  </data>
  <data name="ResRemovefromcart1" xml:space="preserve">
    <value>Retirer du panier</value>
  </data>
  <data name="ResRenameDrawing" xml:space="preserve">
    <value>Renommer le dessin</value>
  </data>
  <data name="ResRenameDrawing1" xml:space="preserve">
    <value>Renommer le dessin</value>
  </data>
  <data name="ResRenameDrawingLog" xml:space="preserve">
    <value>Renommer le journal de dessin</value>
  </data>
  <data name="ResRenameDrawingLog1" xml:space="preserve">
    <value>Renommer le journal de dessin</value>
  </data>
  <data name="ResRenamePart" xml:space="preserve">
    <value>Renommer la pièce</value>
  </data>
  <data name="ResRenamePart#as" xml:space="preserve">
    <value>Renommer le numéro de pièce comme étant</value>
  </data>
  <data name="ResRenamePart#as1" xml:space="preserve">
    <value>Renommer le numéro de pièce comme étant</value>
  </data>
  <data name="ResRenamePart1" xml:space="preserve">
    <value>Renommer la pièce</value>
  </data>
  <data name="ResRenamePartHistory" xml:space="preserve">
    <value>Renommer l'historique des pièces</value>
  </data>
  <data name="ResRenamePartLog" xml:space="preserve">
    <value>Renommer le journal des pièces</value>
  </data>
  <data name="ResRenamePartLog1" xml:space="preserve">
    <value>Renommer le journal des pièces</value>
  </data>
  <data name="ResRepalcedPartAddedtoShoppingCart" xml:space="preserve">
    <value>Pièce(s) remplacée(s) ajoutée(s) au panier</value>
  </data>
  <data name="ResReplacebyPart#" xml:space="preserve">
    <value>Remplacer par numéro de pièce</value>
  </data>
  <data name="ResReplacebyPart#1" xml:space="preserve">
    <value>Remplacer par numéro de pièce</value>
  </data>
  <data name="ResReplacePart" xml:space="preserve">
    <value>Remplacer la pièce</value>
  </data>
  <data name="ResReplacePart1" xml:space="preserve">
    <value>Remplacer la pièce</value>
  </data>
  <data name="ResReplacePartHistory" xml:space="preserve">
    <value>Historique des pièces remplacer</value>
  </data>
  <data name="ResReplacePartLog" xml:space="preserve">
    <value>Remplacer le journal des pièces</value>
  </data>
  <data name="ResReplacePartLog1" xml:space="preserve">
    <value>Remplacer le journal des pièces</value>
  </data>
  <data name="ResReplacingCode" xml:space="preserve">
    <value>Remplacement du code</value>
  </data>
  <data name="ResReplacingCode1" xml:space="preserve">
    <value>Remplacement du code</value>
  </data>
  <data name="ResReport" xml:space="preserve">
    <value>Rapport</value>
  </data>
  <data name="ResReport1" xml:space="preserve">
    <value>Rapport</value>
  </data>
  <data name="ResReportedAttachments" xml:space="preserve">
    <value>Rapport Documents joints</value>
  </data>
  <data name="ResReportedBy" xml:space="preserve">
    <value>Rapporté par</value>
  </data>
  <data name="ResReportedBy1" xml:space="preserve">
    <value>Rapporté par</value>
  </data>
  <data name="ResReportedByEmail" xml:space="preserve">
    <value>Signalé par e-mail</value>
  </data>
  <data name="ResReportedDate&amp;Time" xml:space="preserve">
    <value>Date et heure signalées</value>
  </data>
  <data name="ResReportedDate&amp;Time1" xml:space="preserve">
    <value>Date et heure signalées</value>
  </data>
  <data name="ResReportError" xml:space="preserve">
    <value>Signaler une erreur</value>
  </data>
  <data name="ResReportError1" xml:space="preserve">
    <value>Signaler une erreur</value>
  </data>
  <data name="ResRequest#" xml:space="preserve">
    <value>Numéro de demande</value>
  </data>
  <data name="ResRequest#1" xml:space="preserve">
    <value>Numéro de demande</value>
  </data>
  <data name="ResRequestDate" xml:space="preserve">
    <value>Date de la demande</value>
  </data>
  <data name="ResRequestDate1" xml:space="preserve">
    <value>Date de la demande</value>
  </data>
  <data name="ResRequester" xml:space="preserve">
    <value>Demandeur</value>
  </data>
  <data name="ResRequesterEmail" xml:space="preserve">
    <value>Courriel du demandeur</value>
  </data>
  <data name="ResRequesterPhone" xml:space="preserve">
    <value>Numéro de téléphone du demandeur</value>
  </data>
  <data name="ResRequestforCustomerAssociation" xml:space="preserve">
    <value>Demande de devis Association de clients</value>
  </data>
  <data name="ResReset" xml:space="preserve">
    <value>Réinitialiser</value>
  </data>
  <data name="ResResetPassword" xml:space="preserve">
    <value>Réinitialiser le mot de passe</value>
  </data>
  <data name="ResResetPassword1" xml:space="preserve">
    <value>Réinitialiser le mot de passe</value>
  </data>
  <data name="ResResolution" xml:space="preserve">
    <value>Résolution</value>
  </data>
  <data name="ResResolution1" xml:space="preserve">
    <value>Résolution</value>
  </data>
  <data name="ResResolvedBy" xml:space="preserve">
    <value>Résolu par</value>
  </data>
  <data name="ResResolvedBy1" xml:space="preserve">
    <value>Résolu par</value>
  </data>
  <data name="ResResolvedDate&amp;Time" xml:space="preserve">
    <value>Résolu Date et heure </value>
  </data>
  <data name="ResResolvedDate&amp;Time1" xml:space="preserve">
    <value>Résolu Date et heure </value>
  </data>
  <data name="ResRespondedAttachments" xml:space="preserve">
    <value>Documents joints ayant répondu</value>
  </data>
  <data name="ResRespondedBy" xml:space="preserve">
    <value>Répondu par</value>
  </data>
  <data name="ResRespondedBy1" xml:space="preserve">
    <value>Répondu par</value>
  </data>
  <data name="ResRespondedDate&amp;Time" xml:space="preserve">
    <value>Répondu Date et heure </value>
  </data>
  <data name="ResRespondedDate&amp;Time1" xml:space="preserve">
    <value>Répondu Date et heure </value>
  </data>
  <data name="ResRetrievepassword" xml:space="preserve">
    <value>Récupérer le mot de passe</value>
  </data>
  <data name="ResRevisionHistory" xml:space="preserve">
    <value>Historique des révisions</value>
  </data>
  <data name="ResRevisionHistoryList" xml:space="preserve">
    <value>Liste de l'historique des révisions</value>
  </data>
  <data name="ResRghtClickToCopy" xml:space="preserve">
    <value>Faites un clic droit sur le hotspot pour copier</value>
  </data>
  <data name="ResRoad#" xml:space="preserve">
    <value>Numéro de route</value>
  </data>
  <data name="ResRoad#1" xml:space="preserve">
    <value>Numéro de route</value>
  </data>
  <data name="ResRoad#History" xml:space="preserve">
    <value>Historique Numéro de route</value>
  </data>
  <data name="ResRoad#History1" xml:space="preserve">
    <value>Historique Numéro de route</value>
  </data>
  <data name="ResRoad#Search" xml:space="preserve">
    <value>Recherche Numéro de route</value>
  </data>
  <data name="ResRoad#Search1" xml:space="preserve">
    <value>Recherche Numéro de route</value>
  </data>
  <data name="ResRoad#SearchList" xml:space="preserve">
    <value>Liste Numéro de route</value>
  </data>
  <data name="ResRoad#SearchList1" xml:space="preserve">
    <value>Liste Numéro de route</value>
  </data>
  <data name="ResRoadNumber" xml:space="preserve">
    <value>Numéro de route</value>
  </data>
  <data name="ResRoadNumberDetails" xml:space="preserve">
    <value>Détails du numéro de route</value>
  </data>
  <data name="ResRole" xml:space="preserve">
    <value>Rôle</value>
  </data>
  <data name="ResRole1" xml:space="preserve">
    <value>Rôle</value>
  </data>
  <data name="ResRoleAuthor" xml:space="preserve">
    <value>Rôle - Auteur</value>
  </data>
  <data name="ResRoleAuthor1" xml:space="preserve">
    <value>Rôle - Auteur</value>
  </data>
  <data name="ResRoleDetails" xml:space="preserve">
    <value>Détails du rôle</value>
  </data>
  <data name="ResRoleList" xml:space="preserve">
    <value>Liste des rôles</value>
  </data>
  <data name="ResRoleList1" xml:space="preserve">
    <value>Liste des rôles</value>
  </data>
  <data name="ResRoleName" xml:space="preserve">
    <value>Nom de rôle</value>
  </data>
  <data name="ResRoleName1" xml:space="preserve">
    <value>Nom de rôle</value>
  </data>
  <data name="ResRolesAssociatedToUser" xml:space="preserve">
    <value>Le(s) rôle(s) sélectionné(s) est(sont) déjà associé(s) à l'utilisateur</value>
  </data>
  <data name="ResRoleViewer" xml:space="preserve">
    <value>Rôle - Visionneuse</value>
  </data>
  <data name="ResRoleViewer1" xml:space="preserve">
    <value>Rôle - Visionneuse</value>
  </data>
  <data name="Ressamepartcannotreplace" xml:space="preserve">
    <value>La même pièce ne peut pas être remplacée</value>
  </data>
  <data name="RessamePartNopresentinPartDeatils" xml:space="preserve">
    <value>Même numéro de pièce présent dans les détails de la pièce</value>
  </data>
  <data name="RessamePartNopresentinReplaceDeatils" xml:space="preserve">
    <value>Même numéro de pièce présent dans les détails de la pièce de remplacement</value>
  </data>
  <data name="ResSave" xml:space="preserve">
    <value>Sauvegarder</value>
  </data>
  <data name="ResSave1" xml:space="preserve">
    <value>Sauvegarder</value>
  </data>
  <data name="ResSavedCart1" xml:space="preserve">
    <value>Panier enregistré</value>
  </data>
  <data name="ResSavedCartName" xml:space="preserve">
    <value>Nom du panier enregistré</value>
  </data>
  <data name="ResSaveImage" xml:space="preserve">
    <value>Enregistrer l'image</value>
  </data>
  <data name="ResSaveQuery" xml:space="preserve">
    <value>Enregistrer la requête</value>
  </data>
  <data name="ResSaveQuery1" xml:space="preserve">
    <value>Enregistrer la requête</value>
  </data>
  <data name="ResSearch" xml:space="preserve">
    <value>Chercher</value>
  </data>
  <data name="ResSearch1" xml:space="preserve">
    <value>Chercher</value>
  </data>
  <data name="ResSearchAssembly" xml:space="preserve">
    <value>Rechercher un assemblage</value>
  </data>
  <data name="ResSearchAssembly1" xml:space="preserve">
    <value>Rechercher un assemblage</value>
  </data>
  <data name="ResSearchBy" xml:space="preserve">
    <value>Rechercher par</value>
  </data>
  <data name="ResSearchedCustomerList" xml:space="preserve">
    <value>Liste des clients recherchés</value>
  </data>
  <data name="ResSearchedCustomerList1" xml:space="preserve">
    <value>Liste des clients recherchés</value>
  </data>
  <data name="ResSearchformenu" xml:space="preserve">
    <value>Rechercher un menu …</value>
  </data>
  <data name="ResSearchNova" xml:space="preserve">
    <value>Recherche par numéro de commande, page de section, page de pièce, pièce, etc.</value>
  </data>
  <data name="ResSearchPartAssembly" xml:space="preserve">
    <value>Recherche de pièce / assemblage</value>
  </data>
  <data name="ResSearchPrevost" xml:space="preserve">
    <value>Recherche par Numéro d'identification de véhicule, page de section, page de pièce, pièce, etc.</value>
  </data>
  <data name="ResSearchProduct" xml:space="preserve">
    <value>Rechercher produit</value>
  </data>
  <data name="ResSearchVIN" xml:space="preserve">
    <value>Rechercher NIV (Numéro d'Identification du Véhicule)</value>
  </data>
  <data name="ResSearchVINToplevel" xml:space="preserve">
    <value>Rechercher le Numéro d'identification du véhicule, niveaux supérieurs</value>
  </data>
  <data name="ResSection" xml:space="preserve">
    <value>Section</value>
  </data>
  <data name="ResSection#" xml:space="preserve">
    <value>Numéro de section</value>
  </data>
  <data name="ResSection#1" xml:space="preserve">
    <value>Numéro de section</value>
  </data>
  <data name="ResSectionAssociation" xml:space="preserve">
    <value>Association de section</value>
  </data>
  <data name="ResSectionAssociation1" xml:space="preserve">
    <value>Association de section</value>
  </data>
  <data name="ResSectionAttachments" xml:space="preserve">
    <value>Section Documents Joints</value>
  </data>
  <data name="ResSectionAttachments1" xml:space="preserve">
    <value>Section Documents Joints</value>
  </data>
  <data name="ResSectionCode" xml:space="preserve">
    <value>Code de section</value>
  </data>
  <data name="ResSectionCode1" xml:space="preserve">
    <value>Code de section</value>
  </data>
  <data name="ResSectionCustomerLink" xml:space="preserve">
    <value>Section Document client</value>
  </data>
  <data name="ResSectionCustomerLinkDocument" xml:space="preserve">
    <value>Section Document de lien client </value>
  </data>
  <data name="ResSectionCustomerView" xml:space="preserve">
    <value>Section - Vue client</value>
  </data>
  <data name="ResSectionCustomerView1" xml:space="preserve">
    <value>Section - Vue client</value>
  </data>
  <data name="ResSectionDescriptionchangeswillapplytoallApplicableOrders" xml:space="preserve">
    <value>FN-Section Description changes will apply to all Applicable Orders</value>
  </data>
  <data name="ResSectionDetails" xml:space="preserve">
    <value>Détails de Section</value>
  </data>
  <data name="ResSectionDrawingLog" xml:space="preserve">
    <value>Section Journal de dessin</value>
  </data>
  <data name="ResSectionDrawingLog1" xml:space="preserve">
    <value>Section Journal de dessin</value>
  </data>
  <data name="ResSectionImages" xml:space="preserve">
    <value>Section Images</value>
  </data>
  <data name="ResSectionImages1" xml:space="preserve">
    <value>Section Images</value>
  </data>
  <data name="ResSectionImport" xml:space="preserve">
    <value>Section Importer</value>
  </data>
  <data name="ResSectionImport1" xml:space="preserve">
    <value>Section Importer</value>
  </data>
  <data name="ResSectionLanguage-Add" xml:space="preserve">
    <value>Section - Ajouter</value>
  </data>
  <data name="ResSectionLanguage-Add1" xml:space="preserve">
    <value>Section - Ajouter</value>
  </data>
  <data name="ResSectionLanguage-Edit" xml:space="preserve">
    <value>Section - Modifier</value>
  </data>
  <data name="ResSectionLanguage-Edit1" xml:space="preserve">
    <value>Section - Modifier</value>
  </data>
  <data name="ResSectionLanguage-List" xml:space="preserve">
    <value>Section - Liste des langues</value>
  </data>
  <data name="ResSectionLanguage-List1" xml:space="preserve">
    <value>Section - Liste des langues</value>
  </data>
  <data name="ResSectionLanguageList" xml:space="preserve">
    <value>Section Liste des langues</value>
  </data>
  <data name="ResSectionList" xml:space="preserve">
    <value>Section Liste</value>
  </data>
  <data name="ResSectionList1" xml:space="preserve">
    <value>Section Liste</value>
  </data>
  <data name="ResSectionName" xml:space="preserve">
    <value>Section Nom</value>
  </data>
  <data name="ResSectionName1" xml:space="preserve">
    <value>Section Nom</value>
  </data>
  <data name="ResSectionNameUpdation" xml:space="preserve">
    <value>Section Mise à jour du nom</value>
  </data>
  <data name="ResSectionNameUpdation1" xml:space="preserve">
    <value>Section Mise à jour du nom</value>
  </data>
  <data name="ResSectionPrint" xml:space="preserve">
    <value>Section Imprimer</value>
  </data>
  <data name="ResSectionPrint1" xml:space="preserve">
    <value>Section Imprimer</value>
  </data>
  <data name="ResSectionRemarks" xml:space="preserve">
    <value>Remarques sur la section</value>
  </data>
  <data name="ResSections" xml:space="preserve">
    <value>Sections</value>
  </data>
  <data name="ResSections1" xml:space="preserve">
    <value>Sections</value>
  </data>
  <data name="ResSectionsAssociation" xml:space="preserve">
    <value>Section  Association</value>
  </data>
  <data name="ResSectionsAssociation1" xml:space="preserve">
    <value>Section  Association</value>
  </data>
  <data name="ResSectionServiceDocumentLink" xml:space="preserve">
    <value>Section Lien du document de service</value>
  </data>
  <data name="ResSectionServiceDocumentLink1" xml:space="preserve">
    <value>Section Lien du document de service</value>
  </data>
  <data name="ResSectionServiceDocumentLink2" xml:space="preserve">
    <value>Section Lien du document de service</value>
  </data>
  <data name="ResSectionServiceLink" xml:space="preserve">
    <value>Section Lien de service</value>
  </data>
  <data name="ResSectionServiceLinkDocument" xml:space="preserve">
    <value>Section Document de lien de service</value>
  </data>
  <data name="ResSelect" xml:space="preserve">
    <value>Sélectionnez</value>
  </data>
  <data name="ResSelect1" xml:space="preserve">
    <value>Sélectionnez</value>
  </data>
  <data name="ResSelectAll" xml:space="preserve">
    <value>Tout sélectionner</value>
  </data>
  <data name="ResSelectBrandName" xml:space="preserve">
    <value>Sélectionnez le nom de la marque</value>
  </data>
  <data name="ResSelectColumn" xml:space="preserve">
    <value>Sélectionnez une colonne</value>
  </data>
  <data name="ResSelectColumn1" xml:space="preserve">
    <value>Sélectionnez une colonne</value>
  </data>
  <data name="ResSelectCondition" xml:space="preserve">
    <value>Sélectionnez la condition</value>
  </data>
  <data name="ResSelectCondition1" xml:space="preserve">
    <value>Sélectionnez la condition</value>
  </data>
  <data name="ResSelectDropdown" xml:space="preserve">
    <value>Sélectionnez la liste déroulante</value>
  </data>
  <data name="ResSelectedAssemblyHasNoBOMpartDetails" xml:space="preserve">
    <value>L'assemblage sélectionné n'a pas de détails de pièce de nomenclature</value>
  </data>
  <data name="ResSelectedConditions" xml:space="preserve">
    <value>Conditions sélectionnées</value>
  </data>
  <data name="ResSelectedConditions1" xml:space="preserve">
    <value>Conditions sélectionnées</value>
  </data>
  <data name="ResSelectedFileisnotanExcelFile" xml:space="preserve">
    <value>Le fichier sélectionné n'est pas un fichier Excel</value>
  </data>
  <data name="ResSelectedFileisnotanTextFile" xml:space="preserve">
    <value>Le fichier sélectionné n'est pas un fichier texte (.txt)</value>
  </data>
  <data name="ResSelectedMaskingCodeNotFound" xml:space="preserve">
    <value>Code de masquage sélectionné introuvable</value>
  </data>
  <data name="ResSelectedOrders" xml:space="preserve">
    <value>Commandes sélectionnées</value>
  </data>
  <data name="ResSelectFromList" xml:space="preserve">
    <value>Sélectionnez dans la liste</value>
  </data>
  <data name="ResSelectFromList1" xml:space="preserve">
    <value>Sélectionnez dans la liste</value>
  </data>
  <data name="ResSelectMFGCode" xml:space="preserve">
    <value>Sélectionnez le code de fabrication</value>
  </data>
  <data name="ResSelectMFGCode1" xml:space="preserve">
    <value>Sélectionnez le code de fabrication</value>
  </data>
  <data name="ResSelectMFGcodeFirst" xml:space="preserve">
    <value>Sélectionnez d'abord le code de fabrication</value>
  </data>
  <data name="ResSelectModel" xml:space="preserve">
    <value>Sélectionnez le modèle</value>
  </data>
  <data name="ResSelectOperator" xml:space="preserve">
    <value>Sélectionnez un opérateur</value>
  </data>
  <data name="ResSelectOperator1" xml:space="preserve">
    <value>Sélectionnez un opérateur</value>
  </data>
  <data name="ResSelectProductType" xml:space="preserve">
    <value>Sélectionnez le type de produit</value>
  </data>
  <data name="ResSelectQuery" xml:space="preserve">
    <value>Sélectionner une requête</value>
  </data>
  <data name="ResSelectQuery1" xml:space="preserve">
    <value>Sélectionner une requête</value>
  </data>
  <data name="ResSelectScreen" xml:space="preserve">
    <value>Sélectionnez l'écran</value>
  </data>
  <data name="ResSelectSections" xml:space="preserve">
    <value>Sélectionnez des sections</value>
  </data>
  <data name="ResSelectSections1" xml:space="preserve">
    <value>Sélectionnez des sections</value>
  </data>
  <data name="ResSelectUserType" xml:space="preserve">
    <value>Veuillez sélectionner le type d'utilisateur</value>
  </data>
  <data name="ResSeletedFileCannotbeEmpty" xml:space="preserve">
    <value>Le fichier sélectionné ne peut pas être vide</value>
  </data>
  <data name="ResSend" xml:space="preserve">
    <value>Envoyer</value>
  </data>
  <data name="ResSend1" xml:space="preserve">
    <value>Envoyer</value>
  </data>
  <data name="ResSendMail" xml:space="preserve">
    <value>Envoyer un mail</value>
  </data>
  <data name="ResSendMail1" xml:space="preserve">
    <value>Envoyer un mail</value>
  </data>
  <data name="ResSendMe" xml:space="preserve">
    <value>Envoyer le mot de passe</value>
  </data>
  <data name="ResSeq" xml:space="preserve">
    <value>Sequence</value>
  </data>
  <data name="ResSeq1" xml:space="preserve">
    <value>Sequence</value>
  </data>
  <data name="ResSequence" xml:space="preserve">
    <value>Sequence</value>
  </data>
  <data name="ResSequence#" xml:space="preserve">
    <value>Numéro de séquence</value>
  </data>
  <data name="ResSequence#1" xml:space="preserve">
    <value>Numéro de séquence</value>
  </data>
  <data name="ResSequence1" xml:space="preserve">
    <value>Sequence</value>
  </data>
  <data name="Ressequencelessthan255" xml:space="preserve">
    <value>Entrez séquence inférieure à 255</value>
  </data>
  <data name="ResSequenceNo" xml:space="preserve">
    <value>Numéro de séquence</value>
  </data>
  <data name="ResSequenceNo1" xml:space="preserve">
    <value>Numéro de séquence</value>
  </data>
  <data name="ResSequenceNumber" xml:space="preserve">
    <value>Numéro de séquence</value>
  </data>
  <data name="ResSerial#AssociationDetails" xml:space="preserve">
    <value>#N/A</value>
  </data>
  <data name="ResSerialAssociation" xml:space="preserve">
    <value>Association série</value>
  </data>
  <data name="ResSerialNoAssociation" xml:space="preserve">
    <value>Association du numéro de série</value>
  </data>
  <data name="ResSerialNumberAndModelAssociation" xml:space="preserve">
    <value>Numéro de série et association de modèle</value>
  </data>
  <data name="ResServiceDocument" xml:space="preserve">
    <value>Document de service</value>
  </data>
  <data name="ResServiceDocuments" xml:space="preserve">
    <value>Documents de service</value>
  </data>
  <data name="ResServiceLink" xml:space="preserve">
    <value>Lien de service</value>
  </data>
  <data name="ResServiceLink1" xml:space="preserve">
    <value>Lien de service</value>
  </data>
  <data name="ResServiceLinkAttach" xml:space="preserve">
    <value>Lien de service Documents Joints</value>
  </data>
  <data name="ResServiceLinkAttach1" xml:space="preserve">
    <value>Lien de service Documents Joints</value>
  </data>
  <data name="ResServiceLinkAttachments" xml:space="preserve">
    <value>Lien de service / Documents Joints</value>
  </data>
  <data name="ResServiceLinkAttachments1" xml:space="preserve">
    <value>Lien de service / Documents Joints</value>
  </data>
  <data name="ResServiceLinkOrAttachments" xml:space="preserve">
    <value>Lien de service / Documents Joints</value>
  </data>
  <data name="ResServiceLinkOrAttachments1" xml:space="preserve">
    <value>Lien de service / Documents Joints</value>
  </data>
  <data name="ResServiceManuals" xml:space="preserve">
    <value>Manuels d'entretien</value>
  </data>
  <data name="ResServiceManuals1" xml:space="preserve">
    <value>Manuels d'entretien</value>
  </data>
  <data name="ResServiceManualsSearch" xml:space="preserve">
    <value>Recherche de manuels de service</value>
  </data>
  <data name="ResServiceProvider" xml:space="preserve">
    <value>Fournisseur de services</value>
  </data>
  <data name="ResServiceSectionAssociation" xml:space="preserve">
    <value>Association de la section de service</value>
  </data>
  <data name="ResServiceSectionAssociation1" xml:space="preserve">
    <value>Association de la section de service</value>
  </data>
  <data name="ResServiceSub-Section" xml:space="preserve">
    <value>Sous-section des services</value>
  </data>
  <data name="ResSet" xml:space="preserve">
    <value>Définir</value>
  </data>
  <data name="ResSet1" xml:space="preserve">
    <value>Définir</value>
  </data>
  <data name="ResSetMember" xml:space="preserve">
    <value>Définir membre</value>
  </data>
  <data name="ResSetMember1" xml:space="preserve">
    <value>Définir membre</value>
  </data>
  <data name="ResSetMemberDetails" xml:space="preserve">
    <value>detail membre</value>
  </data>
  <data name="ResSetMemberList" xml:space="preserve">
    <value>Définir la liste des membres</value>
  </data>
  <data name="ResSetMemberList1" xml:space="preserve">
    <value>Définir la liste des membres</value>
  </data>
  <data name="ResSetMembers" xml:space="preserve">
    <value>Définir membres</value>
  </data>
  <data name="ResSetMembers1" xml:space="preserve">
    <value>Définir membres</value>
  </data>
  <data name="ResShareAnnotation" xml:space="preserve">
    <value>Partager les annotations</value>
  </data>
  <data name="ResShipToAddress" xml:space="preserve">
    <value>Envoyer à l'adresse</value>
  </data>
  <data name="ResShipToAddressDetails" xml:space="preserve">
    <value>Détails de l'adresse de livraison</value>
  </data>
  <data name="ResShipToAddressLanguageList" xml:space="preserve">
    <value>Liste de langues des adresses de livraison</value>
  </data>
  <data name="ResShipToAdressAdd" xml:space="preserve">
    <value>Adresse de livraison - Ajouter</value>
  </data>
  <data name="ResShipToAdressEdit" xml:space="preserve">
    <value>Modifier l'adresse de livraison</value>
  </data>
  <data name="ResShoppingCart" xml:space="preserve">
    <value>Panier d'achat</value>
  </data>
  <data name="ResShoppingCart1" xml:space="preserve">
    <value>Panier d'achat</value>
  </data>
  <data name="ResShoppingCartAttachments" xml:space="preserve">
    <value>Panier d'achat Documents joints</value>
  </data>
  <data name="ResShoppingCartAttachments1" xml:space="preserve">
    <value>Panier d'achat Documents joints</value>
  </data>
  <data name="ResShoppingCartConfig" xml:space="preserve">
    <value>Configuration du panier d'achat</value>
  </data>
  <data name="ResShoppingCartConfig1" xml:space="preserve">
    <value>Configuration du panier d'achat</value>
  </data>
  <data name="ResShoppingCartConfigure-Edit" xml:space="preserve">
    <value>Configuration du panier d'achat - Modifier</value>
  </data>
  <data name="ResShoppingCartConfigure-Edit1" xml:space="preserve">
    <value>Configuration du panier d'achat - Modifier</value>
  </data>
  <data name="ResShoppingCartGUIConfiguration" xml:space="preserve">
    <value>Configuration de l'interface graphique du panier d'achat</value>
  </data>
  <data name="ResShoppingCartImport" xml:space="preserve">
    <value>Importation du panier d'achat</value>
  </data>
  <data name="ResShoppingCartImport1" xml:space="preserve">
    <value>Importation du panier d'achat</value>
  </data>
  <data name="ResShoppingCartList" xml:space="preserve">
    <value>Liste de panier d'achat</value>
  </data>
  <data name="ResShoppingCartXML" xml:space="preserve">
    <value>Détails_de_le_panier</value>
  </data>
  <data name="ResShoppingDetails" xml:space="preserve">
    <value>Détails de le panier d'achat</value>
  </data>
  <data name="ResShoppingDetailsXML" xml:space="preserve">
    <value>Détails_de_le_panier_dachat</value>
  </data>
  <data name="ResShoppingList" xml:space="preserve">
    <value>Liste d'achats</value>
  </data>
  <data name="ResShoppingList1" xml:space="preserve">
    <value>Liste d'achats</value>
  </data>
  <data name="ResshortCode" xml:space="preserve">
    <value>Petit code</value>
  </data>
  <data name="ResShowGUIConfig" xml:space="preserve">
    <value>Configuration GUI</value>
  </data>
  <data name="ResShowHideHotspots" xml:space="preserve">
    <value>Afficher / masquer les hotspots</value>
  </data>
  <data name="ResShowQueryResult" xml:space="preserve">
    <value>Afficher le résultat de la requête</value>
  </data>
  <data name="ResShowResult" xml:space="preserve">
    <value>Afficher le résultat</value>
  </data>
  <data name="ResShowResult1" xml:space="preserve">
    <value>Afficher le résultat</value>
  </data>
  <data name="ResShowRevisionHistory" xml:space="preserve">
    <value>Afficher l'historique des révisions</value>
  </data>
  <data name="ResShowRevisionHistory1" xml:space="preserve">
    <value>Afficher l'historique des révisions</value>
  </data>
  <data name="ResShowVendorDetails?" xml:space="preserve">
    <value>FN-Show Vendor Details?</value>
  </data>
  <data name="ResShowVendorParttoCustomer" xml:space="preserve">
    <value>Afficher la partie fournisseur au client?</value>
  </data>
  <data name="ResShowvendorParttoCustomer?" xml:space="preserve">
    <value>Afficher la partie fournisseur?</value>
  </data>
  <data name="ResShowvendorParttoCustomer?1" xml:space="preserve">
    <value>Afficher la partie fournisseur au client?</value>
  </data>
  <data name="ResSINo" xml:space="preserve">
    <value>SI.No.</value>
  </data>
  <data name="ResSLN" xml:space="preserve">
    <value>SI.No.</value>
  </data>
  <data name="ResSlNo" xml:space="preserve">
    <value>SI.No.</value>
  </data>
  <data name="ResSlNo1" xml:space="preserve">
    <value>SI.No.</value>
  </data>
  <data name="ResSLNum" xml:space="preserve">
    <value>SI.No.</value>
  </data>
  <data name="ResSpaceisnotAllowed" xml:space="preserve">
    <value>L'espace n'est pas autorisé</value>
  </data>
  <data name="ResSpec" xml:space="preserve">
    <value>Spec.</value>
  </data>
  <data name="ResSpec1" xml:space="preserve">
    <value>Spec.</value>
  </data>
  <data name="ResSpecialInstructions" xml:space="preserve">
    <value>Instructions spéciales</value>
  </data>
  <data name="ResSpecification" xml:space="preserve">
    <value>Spécification</value>
  </data>
  <data name="ResSpecification1" xml:space="preserve">
    <value>Spécification</value>
  </data>
  <data name="ResSquare" xml:space="preserve">
    <value>Carré</value>
  </data>
  <data name="ResStack" xml:space="preserve">
    <value>Empiler</value>
  </data>
  <data name="ResStandardRepairTime" xml:space="preserve">
    <value>Temps de réparation standard</value>
  </data>
  <data name="ResStartWith" xml:space="preserve">
    <value>Commencer avec</value>
  </data>
  <data name="ResStartWith1" xml:space="preserve">
    <value>Commencer avec</value>
  </data>
  <data name="ResState" xml:space="preserve">
    <value>État / Province</value>
  </data>
  <data name="ResStatus" xml:space="preserve">
    <value>Statut</value>
  </data>
  <data name="ResStatus1" xml:space="preserve">
    <value>Statut</value>
  </data>
  <data name="ResStatusLegend" xml:space="preserve">
    <value>Légende du statut</value>
  </data>
  <data name="ResStatusLegend1" xml:space="preserve">
    <value>Légende du statut</value>
  </data>
  <data name="ResStrightLine" xml:space="preserve">
    <value>Ligne droite</value>
  </data>
  <data name="ResSubject" xml:space="preserve">
    <value>Sujet</value>
  </data>
  <data name="ResSubject1" xml:space="preserve">
    <value>Sujet</value>
  </data>
  <data name="ResSubSection" xml:space="preserve">
    <value>Sous-section</value>
  </data>
  <data name="ResSubSectionCustomerLink" xml:space="preserve">
    <value>Document client sous-section</value>
  </data>
  <data name="ResSubSectionRemarks" xml:space="preserve">
    <value>Remarques de sous-section</value>
  </data>
  <data name="ResSubSectionServiceLink" xml:space="preserve">
    <value>Lien de service de sous-section</value>
  </data>
  <data name="ResSuggestedQty" xml:space="preserve">
    <value>Quantité suggérée</value>
  </data>
  <data name="ResSuggestedQty1" xml:space="preserve">
    <value>Quantité suggérée</value>
  </data>
  <data name="ResSuggestedQtyXML" xml:space="preserve">
    <value>Quantite</value>
  </data>
  <data name="ResSummary" xml:space="preserve">
    <value>Sommaire</value>
  </data>
  <data name="ResSup" xml:space="preserve">
    <value>Remplacement</value>
  </data>
  <data name="ResSup1" xml:space="preserve">
    <value>Remplacement</value>
  </data>
  <data name="ResSupersceedingPart?" xml:space="preserve">
    <value>Pièce de remplacement?</value>
  </data>
  <data name="ResSupersceedingPart?1" xml:space="preserve">
    <value>Pièce de remplacement?</value>
  </data>
  <data name="ResSupersedingPartsDetail" xml:space="preserve">
    <value>Remplacé par les détails de la pièce</value>
  </data>
  <data name="ResSupersedingPartsDetail1" xml:space="preserve">
    <value>Détail des pièces de remplacement</value>
  </data>
  <data name="ResSuperseededPartDetail" xml:space="preserve">
    <value>Pièce sélectionnée</value>
  </data>
  <data name="ResSuperseedingPartDetail" xml:space="preserve">
    <value>Remplacée par la pièce</value>
  </data>
  <data name="ResSuperseedingPartDetails" xml:space="preserve">
    <value>Détails des pièces de remplacement</value>
  </data>
  <data name="ResSuperseedingPartDetailsAdd" xml:space="preserve">
    <value>Ajouter détails de la pièce de remplacement</value>
  </data>
  <data name="ResSuperseedingPartDetailsAdd1" xml:space="preserve">
    <value>Ajouter détails de la pièce de remplacement</value>
  </data>
  <data name="ResSupersession" xml:space="preserve">
    <value>Remplacement</value>
  </data>
  <data name="ResSupersession1" xml:space="preserve">
    <value>Remplacement</value>
  </data>
  <data name="ResSupersession?" xml:space="preserve">
    <value>Remplacement?</value>
  </data>
  <data name="ResSupersession?1" xml:space="preserve">
    <value>Remplacement?</value>
  </data>
  <data name="ResSupersessionDetails" xml:space="preserve">
    <value>Détails de remplacement</value>
  </data>
  <data name="ResSupersessionFromPartDeatils" xml:space="preserve">
    <value>À partir des détails de la pièce</value>
  </data>
  <data name="ResSupersessionList" xml:space="preserve">
    <value>Liste de remplacement</value>
  </data>
  <data name="ResSupersessionRemarks" xml:space="preserve">
    <value>Remarques de remplacement</value>
  </data>
  <data name="ResSupersessionType" xml:space="preserve">
    <value>Type de remplacement</value>
  </data>
  <data name="ResSupersessionType1" xml:space="preserve">
    <value>Type de remplacement</value>
  </data>
  <data name="ResSwitchLanguages" xml:space="preserve">
    <value>Changer de langue</value>
  </data>
  <data name="ResTelephone4388431764" xml:space="preserve">
    <value>Téléphone: ************</value>
  </data>
  <data name="ResTemplateCode" xml:space="preserve">
    <value>Code du modèle</value>
  </data>
  <data name="ResTemplateCode1" xml:space="preserve">
    <value>Code du modèle</value>
  </data>
  <data name="ResTemplateName" xml:space="preserve">
    <value>Nom du modèle</value>
  </data>
  <data name="ResTemplateName1" xml:space="preserve">
    <value>Nom du modèle</value>
  </data>
  <data name="ResTemplateSearchList" xml:space="preserve">
    <value>Liste de recherche de modèles</value>
  </data>
  <data name="ResTemplateSearchList1" xml:space="preserve">
    <value>Liste de recherche de modèles</value>
  </data>
  <data name="ResText" xml:space="preserve">
    <value>Texte</value>
  </data>
  <data name="ResThanksRegards" xml:space="preserve">
    <value>Merci et salutations</value>
  </data>
  <data name="ResThankYou" xml:space="preserve">
    <value>Merci</value>
  </data>
  <data name="ResThisisasystemgeneratedemail.Pleasedonotreply!" xml:space="preserve">
    <value>Ceci est un courriel généré par le système. Merci de ne pas répondre!</value>
  </data>
  <data name="ResTimeOutException" xml:space="preserve">
    <value>Exception de délai d'attente</value>
  </data>
  <data name="ResTimesheetReport" xml:space="preserve">
    <value>Rapport de feuille de temps</value>
  </data>
  <data name="ResTimeTaken" xml:space="preserve">
    <value>Temps pris</value>
  </data>
  <data name="ResTimeTaken1" xml:space="preserve">
    <value>Temps pris</value>
  </data>
  <data name="ResTimeZone" xml:space="preserve">
    <value>Fuseau horaire</value>
  </data>
  <data name="ResTitleBrandAdd" xml:space="preserve">
    <value>Marque - Ajouter</value>
  </data>
  <data name="ResTo" xml:space="preserve">
    <value>À</value>
  </data>
  <data name="ResTo1" xml:space="preserve">
    <value>À</value>
  </data>
  <data name="ResTOC" xml:space="preserve">
    <value>Modèle: Compact </value>
  </data>
  <data name="ResTOC1" xml:space="preserve">
    <value>Modèle: Compact </value>
  </data>
  <data name="ResTOCSelection" xml:space="preserve">
    <value>Sélection Modèle: Compact </value>
  </data>
  <data name="ResTOCSelection1" xml:space="preserve">
    <value>Sélection Modèle: Compact </value>
  </data>
  <data name="ResToDate" xml:space="preserve">
    <value>À ce jour</value>
  </data>
  <data name="ResToggleBOM" xml:space="preserve">
    <value>Basculer à la nomenclature</value>
  </data>
  <data name="ResToggleDrawing" xml:space="preserve">
    <value>Basculer au dessin</value>
  </data>
  <data name="ResToggleScreen" xml:space="preserve">
    <value>Basculer l'écran</value>
  </data>
  <data name="ResToMFGCode" xml:space="preserve">
    <value>Vers le code de fabrication</value>
  </data>
  <data name="ResToMFGCode1" xml:space="preserve">
    <value>Vers le code de fabrication</value>
  </data>
  <data name="ResToolbarConfigure-Edit" xml:space="preserve">
    <value>Configurer la barre d'outils - Modifier</value>
  </data>
  <data name="ResToolbarConfigure-Edit1" xml:space="preserve">
    <value>Configurer la barre d'outils - Modifier</value>
  </data>
  <data name="ResToolBarSettings" xml:space="preserve">
    <value>Paramètres de la barre d'outils</value>
  </data>
  <data name="ResToolBarSettings1" xml:space="preserve">
    <value>Paramètres de la barre d'outils</value>
  </data>
  <data name="ResToolName" xml:space="preserve">
    <value>Nom de l'outil</value>
  </data>
  <data name="ResToolName1" xml:space="preserve">
    <value>Nom de l'outil</value>
  </data>
  <data name="ResToPart#" xml:space="preserve">
    <value>Vers le numéro de la pièce</value>
  </data>
  <data name="ResToPart#1" xml:space="preserve">
    <value>Vers le numéro de la pièce</value>
  </data>
  <data name="ResToPartNumber" xml:space="preserve">
    <value>Vers le numéro de la pièce</value>
  </data>
  <data name="ResToPartNumber1" xml:space="preserve">
    <value>Vers le numéro de la pièce</value>
  </data>
  <data name="ResTopLevel" xml:space="preserve">
    <value>Premier niveau</value>
  </data>
  <data name="ResTopLevelCustomerLinkDocument" xml:space="preserve">
    <value>Document de lien client de premier niveau</value>
  </data>
  <data name="ResTopLevelServiceLinkDocument" xml:space="preserve">
    <value>Document de lien de service de premier niveau</value>
  </data>
  <data name="ResTopSectionCustomerLink" xml:space="preserve">
    <value>Document client de la section supérieure</value>
  </data>
  <data name="ResTopSectionServiceLink" xml:space="preserve">
    <value>Lien de service de la section supérieure</value>
  </data>
  <data name="ResToQty" xml:space="preserve">
    <value>Vers Quantité</value>
  </data>
  <data name="ResToQty1" xml:space="preserve">
    <value>Vers Quantité</value>
  </data>
  <data name="ResToSN" xml:space="preserve">
    <value>À</value>
  </data>
  <data name="ResTotalCount" xml:space="preserve">
    <value>Compte total</value>
  </data>
  <data name="ResTotalCount1" xml:space="preserve">
    <value>Compte total</value>
  </data>
  <data name="ResTotalHours" xml:space="preserve">
    <value>Heures totales</value>
  </data>
  <data name="ResTotalOptionCodes" xml:space="preserve">
    <value>Codes d'option totaux</value>
  </data>
  <data name="ResTotalOptionCodes1" xml:space="preserve">
    <value>Codes d'option totaux</value>
  </data>
  <data name="ResTransaction" xml:space="preserve">
    <value>Transaction</value>
  </data>
  <data name="ResTransaction1" xml:space="preserve">
    <value>Transaction</value>
  </data>
  <data name="ResTreeView" xml:space="preserve">
    <value>Vue arborescente</value>
  </data>
  <data name="ResTreeView1" xml:space="preserve">
    <value>Vue arborescente</value>
  </data>
  <data name="ResTriangle" xml:space="preserve">
    <value>Triangle</value>
  </data>
  <data name="ResUnabletoDelete" xml:space="preserve">
    <value>Impossible de supprimer</value>
  </data>
  <data name="ResUndo" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="ResUndoHotspot" xml:space="preserve">
    <value>Annuler Hotspot</value>
  </data>
  <data name="ResUndoHotspot1" xml:space="preserve">
    <value>Annuler Hotspot</value>
  </data>
  <data name="ResUnlock" xml:space="preserve">
    <value>Déverrouiller</value>
  </data>
  <data name="ResUnlock1" xml:space="preserve">
    <value>Déverrouiller</value>
  </data>
  <data name="ResUnlock11" xml:space="preserve">
    <value>Déverrouiller</value>
  </data>
  <data name="ResUnlock2" xml:space="preserve">
    <value>Déverrouiller</value>
  </data>
  <data name="ResUnlockList" xml:space="preserve">
    <value>Déverrouiller la liste</value>
  </data>
  <data name="ResUnlockList1" xml:space="preserve">
    <value>Déverrouiller la liste</value>
  </data>
  <data name="ResUnlockList11" xml:space="preserve">
    <value>Déverrouiller la liste</value>
  </data>
  <data name="ResUnlockList2" xml:space="preserve">
    <value>Déverrouiller la liste</value>
  </data>
  <data name="ResUnLockLogDetails" xml:space="preserve">
    <value>Déverrouiller les détails du journal</value>
  </data>
  <data name="ResUOM" xml:space="preserve">
    <value>Unité de mesure</value>
  </data>
  <data name="ResUOM1" xml:space="preserve">
    <value>Unité de mesure</value>
  </data>
  <data name="ResUOMCode" xml:space="preserve">
    <value>Code Unité de mesure</value>
  </data>
  <data name="ResUOMCode1" xml:space="preserve">
    <value>Code Unité de mesure</value>
  </data>
  <data name="ResUOMDescription" xml:space="preserve">
    <value>Description Unité de mesure</value>
  </data>
  <data name="ResUOMDescription1" xml:space="preserve">
    <value>Description Unité de mesure</value>
  </data>
  <data name="ResUOMDescriptionSearchList" xml:space="preserve">
    <value>Liste de recherche de description des unités de mesure</value>
  </data>
  <data name="ResUOMDescriptionSearchList1" xml:space="preserve">
    <value>Liste de recherche de description des unités de mesure</value>
  </data>
  <data name="ResUOMDetails" xml:space="preserve">
    <value>Détails de l'unité de mesure</value>
  </data>
  <data name="ResUOMList" xml:space="preserve">
    <value>Liste Unité de mesure</value>
  </data>
  <data name="ResUOMList1" xml:space="preserve">
    <value>Liste Unité de mesure</value>
  </data>
  <data name="ResUpdate" xml:space="preserve">
    <value>Mettre à jour</value>
  </data>
  <data name="ResUpdate1" xml:space="preserve">
    <value>Mettre à jour</value>
  </data>
  <data name="ResUpdateConfigureAssemblyBOMUI-Viewer" xml:space="preserve">
    <value>Mettre à jour la visionneuse de l'interface utilisateur de configuration de la nomenclature d'assemblage</value>
  </data>
  <data name="ResUpdateConfigureAssemblyBOMUI-Viewer1" xml:space="preserve">
    <value>Mettre à jour la visionneuse de l'interface utilisateur de configuration de la nomenclature d'assemblage</value>
  </data>
  <data name="ResUpdateConfigureCartUI" xml:space="preserve">
    <value />
  </data>
  <data name="ResUpdateConfigureCartUI1" xml:space="preserve">
    <value />
  </data>
  <data name="ResUpdateConfigurePartsUI" xml:space="preserve">
    <value>Mettre à jour l'interface utilisateur de Configurer les pièces</value>
  </data>
  <data name="ResUpdateConfigurePartsUI1" xml:space="preserve">
    <value>Mettre à jour l'interface utilisateur de Configurer les pièces</value>
  </data>
  <data name="ResUpdateConfigureToolbar" xml:space="preserve">
    <value>Mettre à jour la barre d'outils de configuration</value>
  </data>
  <data name="ResUpdateConfigureToolbar1" xml:space="preserve">
    <value>Mettre à jour la barre d'outils de configuration</value>
  </data>
  <data name="ResUpdateCustomerDocument" xml:space="preserve">
    <value>Mettre à jour le document client</value>
  </data>
  <data name="ResUpdatedSuccessfully" xml:space="preserve">
    <value>Mis à jour avec succès</value>
  </data>
  <data name="ResUpdateNote" xml:space="preserve">
    <value>Mettre à jour Note</value>
  </data>
  <data name="ResUpdateQuery" xml:space="preserve">
    <value>Mettre à jour Requete</value>
  </data>
  <data name="ResUpdateQuery1" xml:space="preserve">
    <value>Mettre à jour Requete</value>
  </data>
  <data name="ResUpdateServiceDocument" xml:space="preserve">
    <value>Mettre à jour Document de service</value>
  </data>
  <data name="ResUpdateServiceDocument1" xml:space="preserve">
    <value>Mettre à jour Document de service</value>
  </data>
  <data name="ResUpdateShoppingCartConfiguration" xml:space="preserve">
    <value>Mettre à jour Configuration du panier d'achat</value>
  </data>
  <data name="ResUpdateShoppingCartConfiguration1" xml:space="preserve">
    <value>Mettre à jour Configuration du panier d'achat</value>
  </data>
  <data name="ResUpdateStatus" xml:space="preserve">
    <value>Mettre à jour Statut</value>
  </data>
  <data name="ResUpdateToolbar" xml:space="preserve">
    <value>Mettre à jour Barre d'outils</value>
  </data>
  <data name="ResUpdateToolbar1" xml:space="preserve">
    <value>Mettre à jour Barre d'outils</value>
  </data>
  <data name="ResUpdateViewerToolbarConfigure" xml:space="preserve">
    <value>Mettre à jour Configuration de la barre d'outils de la visionneuse</value>
  </data>
  <data name="ResUpdateViewerToolbarConfigure1" xml:space="preserve">
    <value>Mettre à jour Configuration de la barre d'outils de la visionneuse</value>
  </data>
  <data name="ResUpdationConfigureToolbar" xml:space="preserve">
    <value>Mise à jour de la barre d'outils de configuration</value>
  </data>
  <data name="ResUpdationConfigureToolbar1" xml:space="preserve">
    <value>Mise à jour de la barre d'outils de configuration</value>
  </data>
  <data name="ResUploadDocumnets" xml:space="preserve">
    <value>Téléverser Documents de service</value>
  </data>
  <data name="ResUploadedBy" xml:space="preserve">
    <value>Telechargé par</value>
  </data>
  <data name="ResUploadedBy1" xml:space="preserve">
    <value>Telechargé par</value>
  </data>
  <data name="ResUploadedDate" xml:space="preserve">
    <value>Date de téléchargement</value>
  </data>
  <data name="ResUploadedDate1" xml:space="preserve">
    <value>Date de téléchargement</value>
  </data>
  <data name="ResUploadFile" xml:space="preserve">
    <value>Téléverser un fichier</value>
  </data>
  <data name="ResUploadFile1" xml:space="preserve">
    <value>Téléverser un fichier</value>
  </data>
  <data name="ResUploadServiceDocument" xml:space="preserve">
    <value>Téléverser Document de service</value>
  </data>
  <data name="ResUpToSN" xml:space="preserve">
    <value>Jusqu'à SN</value>
  </data>
  <data name="ResUpToSNShortCode" xml:space="preserve">
    <value>Jusqu'à SN Code court</value>
  </data>
  <data name="ResURL" xml:space="preserve">
    <value>Adresse d'un site sur  Internet</value>
  </data>
  <data name="ResUsageCount" xml:space="preserve">
    <value>Compte d'utilisation</value>
  </data>
  <data name="ResUser" xml:space="preserve">
    <value>Utilisateur</value>
  </data>
  <data name="ResUser1" xml:space="preserve">
    <value>Utilisateur</value>
  </data>
  <data name="ResUserAccessRights" xml:space="preserve">
    <value>Droits d'accès des utilisateurs</value>
  </data>
  <data name="ResUserAccessRights1" xml:space="preserve">
    <value>Droits d'accès des utilisateurs</value>
  </data>
  <data name="ResUserCategory" xml:space="preserve">
    <value>Catégorie d'utilisateur</value>
  </data>
  <data name="ResUserDetails" xml:space="preserve">
    <value>Détails de l'utilisateur</value>
  </data>
  <data name="ResUserList" xml:space="preserve">
    <value>liste d'utilisateur</value>
  </data>
  <data name="ResUserList1" xml:space="preserve">
    <value>liste d'utilisateur</value>
  </data>
  <data name="ResUserLogReportAuthor" xml:space="preserve">
    <value>Rapport du journal utilisateur Auteur </value>
  </data>
  <data name="ResUserLogReportCustomer" xml:space="preserve">
    <value>Rapport du journal utilisateur Client</value>
  </data>
  <data name="ResUserManual" xml:space="preserve">
    <value>Manuel de l'Utilisateur</value>
  </data>
  <data name="ResUserName" xml:space="preserve">
    <value>Nom d'utilisateur</value>
  </data>
  <data name="ResUserName1" xml:space="preserve">
    <value>Nom d'utilisateur</value>
  </data>
  <data name="ResUserType" xml:space="preserve">
    <value>Type d'utilisateur</value>
  </data>
  <data name="ResValidFile" xml:space="preserve">
    <value>Fichier valide</value>
  </data>
  <data name="ResValue" xml:space="preserve">
    <value>Valeur</value>
  </data>
  <data name="ResValue1" xml:space="preserve">
    <value>Valeur</value>
  </data>
  <data name="ResVBNAUserRole" xml:space="preserve">
    <value>Rôle d'utilisateur VBNA</value>
  </data>
  <data name="ResVBNAUserRole1" xml:space="preserve">
    <value>Rôle d'utilisateur VBNA</value>
  </data>
  <data name="ResVBNAUsers" xml:space="preserve">
    <value>Utilisateurs VBNA</value>
  </data>
  <data name="ResVBNAUsers1" xml:space="preserve">
    <value>Utilisateurs VBNA</value>
  </data>
  <data name="ResVehicle" xml:space="preserve">
    <value>Véhicule</value>
  </data>
  <data name="ResVehicle-Add" xml:space="preserve">
    <value>Véhicule - Ajouter</value>
  </data>
  <data name="ResVehicle1" xml:space="preserve">
    <value>Véhicule</value>
  </data>
  <data name="ResVehicleAssociation" xml:space="preserve">
    <value>Association de véhicules</value>
  </data>
  <data name="ResVehicleAssociation1" xml:space="preserve">
    <value>Association de véhicules</value>
  </data>
  <data name="ResVehicleAttachments" xml:space="preserve">
    <value>Véhicule Documents joints</value>
  </data>
  <data name="ResVehicleAttachments1" xml:space="preserve">
    <value>Véhicule Documents joints</value>
  </data>
  <data name="ResVehicleDetails" xml:space="preserve">
    <value>Détails du véhicule</value>
  </data>
  <data name="ResVehicleDetails1" xml:space="preserve">
    <value>Détails du véhicule</value>
  </data>
  <data name="ResVehicleDetailsCannotbeEmpty" xml:space="preserve">
    <value>Les détails du véhicule ne peuvent pas être vides</value>
  </data>
  <data name="ResVehicleImages" xml:space="preserve">
    <value>Images de véhicules</value>
  </data>
  <data name="ResVehicleList" xml:space="preserve">
    <value>Liste des véhicules</value>
  </data>
  <data name="ResVehicleList1" xml:space="preserve">
    <value>Liste des véhicules</value>
  </data>
  <data name="ResVehicleOwnerShipHistoryDeatils" xml:space="preserve">
    <value>Détails sur l'historique de propriété du véhicule</value>
  </data>
  <data name="ResVehicleOwnershipHistoryEffectiveDate" xml:space="preserve">
    <value>Date effective</value>
  </data>
  <data name="ResVehicleRoad#ChangeList" xml:space="preserve">
    <value>Liste de changement de numéro de route du véhicule</value>
  </data>
  <data name="ResVehicleRoad#ChangeList1" xml:space="preserve">
    <value>Liste de changement de numéro de route du véhicule</value>
  </data>
  <data name="ResVehicleRoadHistoryDetails" xml:space="preserve">
    <value>Détails de l'historique des routes du véhicule</value>
  </data>
  <data name="ResVehicleVINHistoryDetails" xml:space="preserve">
    <value>Détails de l'historique du numéro d'identification du véhicule (NIV)</value>
  </data>
  <data name="ResVehicle_OrderNo" xml:space="preserve">
    <value>Numéro de commande du véhicule</value>
  </data>
  <data name="ResVendor" xml:space="preserve">
    <value>Fournisseur</value>
  </data>
  <data name="ResVendor1" xml:space="preserve">
    <value>Fournisseur</value>
  </data>
  <data name="ResVendorAttachments" xml:space="preserve">
    <value>Fournisseur Documents joints</value>
  </data>
  <data name="ResVendorAttachments1" xml:space="preserve">
    <value>Fournisseur Documents joints</value>
  </data>
  <data name="ResVendorCode" xml:space="preserve">
    <value>Code de fournisseur</value>
  </data>
  <data name="ResVendorCode1" xml:space="preserve">
    <value>Code de fournisseur</value>
  </data>
  <data name="ResVendorDetails" xml:space="preserve">
    <value>Détails du fournisseur</value>
  </data>
  <data name="ResVendorDetailsOtherLanguage" xml:space="preserve">
    <value>Détails du fournisseur - Autre langue</value>
  </data>
  <data name="ResVendorDetailsOtherLanguage1" xml:space="preserve">
    <value>Détails du fournisseur - Autre langue</value>
  </data>
  <data name="ResVendorDoesNotExist" xml:space="preserve">
    <value>Le fournisseur n'existe pas</value>
  </data>
  <data name="ResVendorImages" xml:space="preserve">
    <value>Images du fournisseur</value>
  </data>
  <data name="ResVendorInfo" xml:space="preserve">
    <value>Info fournisseur</value>
  </data>
  <data name="ResVendorInfo1" xml:space="preserve">
    <value>Info fournisseur</value>
  </data>
  <data name="ResVendorLangaugeList" xml:space="preserve">
    <value>Liste des langues des fournisseurs</value>
  </data>
  <data name="ResVendorList" xml:space="preserve">
    <value>Liste de fournisseurs</value>
  </data>
  <data name="ResVendorList1" xml:space="preserve">
    <value>Liste de fournisseurs</value>
  </data>
  <data name="ResVendorListOtherLanguage" xml:space="preserve">
    <value>Liste des fournisseurs - Autre langue</value>
  </data>
  <data name="ResVendorListOtherLanguage1" xml:space="preserve">
    <value>Liste des fournisseurs - Autre langue</value>
  </data>
  <data name="ResVendorLocaleNameList" xml:space="preserve">
    <value>Liste des noms de paramètres régionaux du fournisseur</value>
  </data>
  <data name="ResVendorMFG" xml:space="preserve">
    <value>Code de fabrication du fournisseur</value>
  </data>
  <data name="ResVendorMFGCode" xml:space="preserve">
    <value>Code de fabrication du fournisseur</value>
  </data>
  <data name="ResVendorMFGCode1" xml:space="preserve">
    <value>Code de fabrication du fournisseur</value>
  </data>
  <data name="ResVendorName" xml:space="preserve">
    <value>Nom du fournisseur</value>
  </data>
  <data name="ResVendorName1" xml:space="preserve">
    <value>Nom du fournisseur</value>
  </data>
  <data name="ResVendorNameDoesNOtExist" xml:space="preserve">
    <value>Le nom du fournisseur n'existe pas</value>
  </data>
  <data name="ResVendorNo" xml:space="preserve">
    <value>Code de fournisseur</value>
  </data>
  <data name="ResVendorPart#" xml:space="preserve">
    <value>Numéro de pièce du fournisseur</value>
  </data>
  <data name="ResVendorPart#1" xml:space="preserve">
    <value>Numéro de pièce du fournisseur</value>
  </data>
  <data name="ResVendorPartInfo" xml:space="preserve">
    <value>Informations sur la pièce fournisseur</value>
  </data>
  <data name="ResVendorPartInfo1" xml:space="preserve">
    <value>Informations sur la pièce fournisseur</value>
  </data>
  <data name="ResVendorPartInfoDetails" xml:space="preserve">
    <value>FN-VendorPartInfo Details</value>
  </data>
  <data name="ResVendorPartInfoReport" xml:space="preserve">
    <value>FN-Vendor Part Info Report</value>
  </data>
  <data name="ResVendorPartInfoReports" xml:space="preserve">
    <value>FN-Vendor Part Info Reports</value>
  </data>
  <data name="ResVendorPartList" xml:space="preserve">
    <value>Liste des pièces du fournisseur</value>
  </data>
  <data name="ResVendorPartList1" xml:space="preserve">
    <value>Liste des pièces du fournisseur</value>
  </data>
  <data name="ResVendorPartNumber" xml:space="preserve">
    <value>Numéro de pièce du fournisseur</value>
  </data>
  <data name="ResVendorPartPrefix" xml:space="preserve">
    <value>Préfixe de pièce fournisseur</value>
  </data>
  <data name="ResVendorPartPrefix1" xml:space="preserve">
    <value>Préfixe de pièce fournisseur</value>
  </data>
  <data name="ResVendorPrefix" xml:space="preserve">
    <value>Préfixe du fournisseur</value>
  </data>
  <data name="ResVendorPrefix1" xml:space="preserve">
    <value>Préfixe du fournisseur</value>
  </data>
  <data name="ResVENDOR_NAME" xml:space="preserve">
    <value>Nom du fournisseur</value>
  </data>
  <data name="ResVENDOR_NO" xml:space="preserve">
    <value>Code de fournisseur</value>
  </data>
  <data name="ResVerHistory" xml:space="preserve">
    <value>Historique des versions</value>
  </data>
  <data name="ResVerHistory1" xml:space="preserve">
    <value>Historique des versions</value>
  </data>
  <data name="ResVersion" xml:space="preserve">
    <value>Version</value>
  </data>
  <data name="ResVersion#" xml:space="preserve">
    <value>Numéro de version</value>
  </data>
  <data name="ResVersion#1" xml:space="preserve">
    <value>Numéro de version</value>
  </data>
  <data name="ResVersion1" xml:space="preserve">
    <value>Version 1</value>
  </data>
  <data name="ResVersion2" xml:space="preserve">
    <value>Version 2</value>
  </data>
  <data name="ResVersionHistoryList" xml:space="preserve">
    <value>Liste de l'historique des versions</value>
  </data>
  <data name="ResVersionHistoryList1" xml:space="preserve">
    <value>Liste de l'historique des versions</value>
  </data>
  <data name="ResVersionUpdate" xml:space="preserve">
    <value>Mise à jour de version</value>
  </data>
  <data name="ResVerticleSplitter" xml:space="preserve">
    <value>Répartiteur vertical</value>
  </data>
  <data name="ResVerticleSplitter1" xml:space="preserve">
    <value>Répartiteur vertical</value>
  </data>
  <data name="ResView" xml:space="preserve">
    <value>Vue</value>
  </data>
  <data name="ResView1" xml:space="preserve">
    <value>Vue</value>
  </data>
  <data name="ResViewBOMOnly" xml:space="preserve">
    <value>Afficher uniquement la nomenclature</value>
  </data>
  <data name="ResViewConfigurationList" xml:space="preserve">
    <value>Afficher la configuration de la barre d'outils</value>
  </data>
  <data name="ResViewConfigurationList1" xml:space="preserve">
    <value>Afficher la configuration de la barre d'outils</value>
  </data>
  <data name="ResViewDrawing" xml:space="preserve">
    <value>Voir le dessin</value>
  </data>
  <data name="ResViewer" xml:space="preserve">
    <value>Visionneuse</value>
  </data>
  <data name="ResViewerActivity" xml:space="preserve">
    <value>Visionneuse - Activité</value>
  </data>
  <data name="ResViewerActivity1" xml:space="preserve">
    <value>Visionneuse - Activité</value>
  </data>
  <data name="ResViewerActivityDefinition" xml:space="preserve">
    <value>Visionneuse - Définition Activité</value>
  </data>
  <data name="ResViewerActivityDefinition1" xml:space="preserve">
    <value>Visionneuse - Définition Activité</value>
  </data>
  <data name="ResViewerActivityList" xml:space="preserve">
    <value>Visionneuse - Liste d'activités</value>
  </data>
  <data name="ResViewerActivityList1" xml:space="preserve">
    <value>Visionneuse - Liste d'activités</value>
  </data>
  <data name="ResViewerDashboard" xml:space="preserve">
    <value>Tableau de bord de l'utilisateur</value>
  </data>
  <data name="ResViewerDisplay" xml:space="preserve">
    <value>Affichage de la visionneuse</value>
  </data>
  <data name="ResViewerFromAuthor" xml:space="preserve">
    <value>Téléspectatrice</value>
  </data>
  <data name="ResViewErrorLogFile" xml:space="preserve">
    <value>Afficher le fichier journal des erreurs</value>
  </data>
  <data name="ResViewErrorReport" xml:space="preserve">
    <value>Afficher le rapport d'erreur</value>
  </data>
  <data name="ResViewErrorReport1" xml:space="preserve">
    <value>Afficher le rapport d'erreur</value>
  </data>
  <data name="ResViewerTheme" xml:space="preserve">
    <value>Visionneuse - Thème</value>
  </data>
  <data name="ResViewerTheme1" xml:space="preserve">
    <value>Visionneuse - Thème</value>
  </data>
  <data name="ResViewerToolbarConfig" xml:space="preserve">
    <value>Visionneuse - Configuration de la barre d'outils</value>
  </data>
  <data name="ResViewerToolbarConfig1" xml:space="preserve">
    <value>Visionneuse - Configuration de la barre d'outils</value>
  </data>
  <data name="ResViewerToolbarConfigure-Edit" xml:space="preserve">
    <value>Visionneuse - Modifier la configuration de la barre d'outils</value>
  </data>
  <data name="ResViewerToolbarConfigure-Edit1" xml:space="preserve">
    <value>Visionneuse - Modifier la configuration de la barre d'outils</value>
  </data>
  <data name="ResViewerUsers" xml:space="preserve">
    <value>Visionneuse - Utilisateurs</value>
  </data>
  <data name="ResViewerUsers1" xml:space="preserve">
    <value>Visionneuse - Utilisateurs</value>
  </data>
  <data name="ResViewerview" xml:space="preserve">
    <value>Vue du spectateur</value>
  </data>
  <data name="ResViewFile" xml:space="preserve">
    <value>Afficher le fichier</value>
  </data>
  <data name="ResViewFile1" xml:space="preserve">
    <value>Afficher le fichier</value>
  </data>
  <data name="ResViewImage" xml:space="preserve">
    <value>Afficher l'image</value>
  </data>
  <data name="ResViewImage1" xml:space="preserve">
    <value>Afficher l'image</value>
  </data>
  <data name="ResViewImages" xml:space="preserve">
    <value>Afficher les images</value>
  </data>
  <data name="ResViewImages1" xml:space="preserve">
    <value>Afficher les images</value>
  </data>
  <data name="ResViewMode" xml:space="preserve">
    <value>mode visionnement</value>
  </data>
  <data name="ResViewOnlyDrawing" xml:space="preserve">
    <value>Afficher uniquement le dessin</value>
  </data>
  <data name="ResViewOrDownLoad" xml:space="preserve">
    <value>Voir / Télécharger</value>
  </data>
  <data name="ResViewPartImage" xml:space="preserve">
    <value>Afficher Image Pièce</value>
  </data>
  <data name="ResViewPartImage1" xml:space="preserve">
    <value>Afficher Image Pièce</value>
  </data>
  <data name="ResViewSectionImage" xml:space="preserve">
    <value>Afficher Image en coupe</value>
  </data>
  <data name="ResViewSectionImage1" xml:space="preserve">
    <value>Afficher Image en coupe</value>
  </data>
  <data name="ResViewToolbarConfiguration" xml:space="preserve">
    <value>Afficher Configuration de la barre d'outils</value>
  </data>
  <data name="ResViewToolbarConfiguration1" xml:space="preserve">
    <value>Afficher Configuration de la barre d'outils</value>
  </data>
  <data name="ResViewTree" xml:space="preserve">
    <value>Afficher l'arborescence</value>
  </data>
  <data name="ResVIN" xml:space="preserve">
    <value>NIV (Numéro d'Identification du Véhicule)</value>
  </data>
  <data name="ResVIN#" xml:space="preserve">
    <value>NIV (Numéro d'Identification du Véhicule)</value>
  </data>
  <data name="ResVIN#1" xml:space="preserve">
    <value>NIV (Numéro d'Identification du Véhicule)</value>
  </data>
  <data name="ResVIN#History" xml:space="preserve">
    <value>NIV (Numéro d'Identification du Véhicule) -  Historique</value>
  </data>
  <data name="ResVIN#History1" xml:space="preserve">
    <value>NIV (Numéro d'Identification du Véhicule) -  Historique</value>
  </data>
  <data name="ResVIN#Import" xml:space="preserve">
    <value>NIV (Numéro d'Identification du Véhicule) - Importer</value>
  </data>
  <data name="ResVIN#Import1" xml:space="preserve">
    <value>NIV (Numéro d'Identification du Véhicule) - Importer</value>
  </data>
  <data name="ResVIN#Search" xml:space="preserve">
    <value>NIV (Numéro d'Identification du Véhicule) - Chercher</value>
  </data>
  <data name="ResVIN#Search1" xml:space="preserve">
    <value>NIV (Numéro d'Identification du Véhicule) - Chercher</value>
  </data>
  <data name="ResVIN#SearchList" xml:space="preserve">
    <value>NIV (Numéro d'Identification du Véhicule) - Liste de recherche</value>
  </data>
  <data name="ResVIN#SearchList1" xml:space="preserve">
    <value>NIV (Numéro d'Identification du Véhicule) - Liste de recherche</value>
  </data>
  <data name="ResVINAssociation" xml:space="preserve">
    <value>NIV (Numéro d'Identification du Véhicule) - Association</value>
  </data>
  <data name="ResVINAssociation1" xml:space="preserve">
    <value>NIV (Numéro d'Identification du Véhicule) - Association</value>
  </data>
  <data name="ResVINDetails" xml:space="preserve">
    <value>NIV (Numéro d'Identification du Véhicule) - Details</value>
  </data>
  <data name="ResVINImport" xml:space="preserve">
    <value>NIV (Numéro d'Identification du Véhicule) - Importer</value>
  </data>
  <data name="ResVINLevel" xml:space="preserve">
    <value>NIV (Numéro d'Identification du Véhicule) - Niveau</value>
  </data>
  <data name="ResVINLevel1" xml:space="preserve">
    <value>NIV (Numéro d'Identification du Véhicule) - Niveau</value>
  </data>
  <data name="ResVINList" xml:space="preserve">
    <value>NIV (Numéro d'Identification du Véhicule) - Liste</value>
  </data>
  <data name="ResVINNumber" xml:space="preserve">
    <value>Numéro VIN</value>
  </data>
  <data name="ResVINNumberDetails" xml:space="preserve">
    <value>Détails du numéro Vin</value>
  </data>
  <data name="ResVINNumberDoesNOtExist" xml:space="preserve">
    <value>Le numéro d'identification du véhicule n'existe pas</value>
  </data>
  <data name="ResVinOrderDetails" xml:space="preserve">
    <value>Détails de la commande VIN</value>
  </data>
  <data name="ResVINShort#" xml:space="preserve">
    <value>Numéro abrégé du numéro d'identification du véhicule</value>
  </data>
  <data name="ResVINShortCode" xml:space="preserve">
    <value>NIV (Numéro d'Identification du Véhicule) - Code Court</value>
  </data>
  <data name="ResVinShortName" xml:space="preserve">
    <value>NIV (Numéro d'Identification du Véhicule) - Nom Court</value>
  </data>
  <data name="ResWarrantyPoliciesandProceduresManual" xml:space="preserve">
    <value>Manuel des politiques et procédures de garantie</value>
  </data>
  <data name="ReswasreleasedontheNovaBusExtranet" xml:space="preserve">
    <value>a été publié sur l'extranet Nova Bus / Parts Assist :</value>
  </data>
  <data name="ResWearetakingthisactionas" xml:space="preserve">
    <value>Le présent programme s'inscrit dans le cadre des efforts incessants que nous déployons pour tenir nos clients au courant et conserver leur confiance. Nous espérons que ce programme après-vente confirme notre engagement à assurer votre entière satisfaction.</value>
  </data>
  <data name="ResWelcome" xml:space="preserve">
    <value>Bienvenue</value>
  </data>
  <data name="ResWhereUsed" xml:space="preserve">
    <value>Utilisé où</value>
  </data>
  <data name="ResWidth" xml:space="preserve">
    <value>Largeur</value>
  </data>
  <data name="ResWindowZoom" xml:space="preserve">
    <value>afficher le zoom de la fenêtre</value>
  </data>
  <data name="ResWindowZoom1" xml:space="preserve">
    <value>Zoom de la fenêtre</value>
  </data>
  <data name="ResWithVersion" xml:space="preserve">
    <value>Avec version</value>
  </data>
  <data name="ResWithVersion1" xml:space="preserve">
    <value>Avec version</value>
  </data>
  <data name="ResXML" xml:space="preserve">
    <value>XML</value>
  </data>
  <data name="ResYear" xml:space="preserve">
    <value>Année</value>
  </data>
  <data name="ResYes" xml:space="preserve">
    <value>Oui</value>
  </data>
  <data name="ResYes1" xml:space="preserve">
    <value>Oui</value>
  </data>
  <data name="ResYoursSelectedRecordIsLocked" xml:space="preserve">
    <value>L'enregistrement sélectionné est verrouillé</value>
  </data>
  <data name="ResYoursSelectedRecordIsUnLocked" xml:space="preserve">
    <value>L'enregistrement sélectionné est déverrouillé</value>
  </data>
  <data name="ResYoursverytruly," xml:space="preserve">
    <value>Bien à vous,</value>
  </data>
  <data name="ResZipCode" xml:space="preserve">
    <value>Code postal</value>
  </data>
  <data name="ResZoomIn" xml:space="preserve">
    <value>Zoomer / Agrandir</value>
  </data>
  <data name="ResZoomIn1" xml:space="preserve">
    <value>Zoomer / Agrandir</value>
  </data>
  <data name="ResZoomIn2" xml:space="preserve">
    <value>Zoomer / Agrandir</value>
  </data>
  <data name="ResZoomOut" xml:space="preserve">
    <value>Dézoomer / Diminuer</value>
  </data>
  <data name="ResZoomOut1" xml:space="preserve">
    <value>Dézoomer / Diminuer</value>
  </data>
  <data name="ResZoomOut2" xml:space="preserve">
    <value>Dézoomer / Diminuer</value>
  </data>
  <data name="Res_Ignore_error" xml:space="preserve">
    <value>Ignorer l'erreur</value>
  </data>
  <data name="REV" xml:space="preserve">
    <value>Revision</value>
  </data>
  <data name="RevisionandDate" xml:space="preserve">
    <value>Révision et date</value>
  </data>
  <data name="ReWindowZoom" xml:space="preserve">
    <value>Zoom de la fenêtre</value>
  </data>
  <data name="Road#" xml:space="preserve">
    <value>Numéro de route</value>
  </data>
  <data name="RoadNo" xml:space="preserve">
    <value>Numéro de route</value>
  </data>
  <data name="roadnumberpresent" xml:space="preserve">
    <value>Le numéro de route donné est déjà disponible dans les détails du véhicule</value>
  </data>
  <data name="RoleAdd" xml:space="preserve">
    <value>Rôle - Ajouter</value>
  </data>
  <data name="Section" xml:space="preserve">
    <value>Section</value>
  </data>
  <data name="SectionNo" xml:space="preserve">
    <value>Numéro de section</value>
  </data>
  <data name="SelectAll" xml:space="preserve">
    <value>Tout sélectionner</value>
  </data>
  <data name="SelectColumn" xml:space="preserve">
    <value>Sélectionnez une colonne</value>
  </data>
  <data name="Sequence" xml:space="preserve">
    <value>Sequence</value>
  </data>
  <data name="SerailNumberModelFilter" xml:space="preserve">
    <value>Appliquer le numéro d'identification du véhicule (NIV) et le filtre de modèle</value>
  </data>
  <data name="ServiceLinkAdd" xml:space="preserve">
    <value>Lien de service Ajouter</value>
  </data>
  <data name="ServiceLinkRemove" xml:space="preserve">
    <value>Lien de service Supprimer</value>
  </data>
  <data name="ServiceManualFileName" xml:space="preserve">
    <value>Nom du fichier PDF du manuel d'entretien</value>
  </data>
  <data name="ServiceSections" xml:space="preserve">
    <value>Sections de service</value>
  </data>
  <data name="Show Vendor Part" xml:space="preserve">
    <value>Afficher la partie fournisseur</value>
  </data>
  <data name="ShowSetPartinBOM" xml:space="preserve">
    <value>Définir les pièces dans la nomenclature?</value>
  </data>
  <data name="Show_Set_Mem_In_Part_List" xml:space="preserve">
    <value>Définir les pièces dans la nomenclature?</value>
  </data>
  <data name="Specification" xml:space="preserve">
    <value>Spécification</value>
  </data>
  <data name="StartWith" xml:space="preserve">
    <value>Commencer avec</value>
  </data>
  <data name="StockNo" xml:space="preserve">
    <value>Numéro de stock</value>
  </data>
  <data name="String1" xml:space="preserve">
    <value>Est Actif?</value>
  </data>
  <data name="String2" xml:space="preserve">
    <value />
  </data>
  <data name="String3" xml:space="preserve">
    <value>Notes communes</value>
  </data>
  <data name="String4" xml:space="preserve">
    <value />
  </data>
  <data name="String5" xml:space="preserve">
    <value>Développer tout</value>
  </data>
  <data name="String7" xml:space="preserve">
    <value />
  </data>
  <data name="String8" xml:space="preserve">
    <value />
  </data>
  <data name="String9" xml:space="preserve">
    <value />
  </data>
  <data name="SubDiv" xml:space="preserve">
    <value>Sous-division</value>
  </data>
  <data name="SubSectionMissingTopLevelMaster" xml:space="preserve">
    <value>Sous-section manquante - Maître de premier niveau</value>
  </data>
  <data name="SysDivSDBOMID" xml:space="preserve">
    <value>Identifiant SYS DIV SDBOM</value>
  </data>
  <data name="Syst" xml:space="preserve">
    <value>Système</value>
  </data>
  <data name="Template" xml:space="preserve">
    <value>Modèle</value>
  </data>
  <data name="TemplateCode" xml:space="preserve">
    <value>Code du modèle</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Titre</value>
  </data>
  <data name="TOC" xml:space="preserve">
    <value>TABLE DES MATIÈRES</value>
  </data>
  <data name="TOCLbl" xml:space="preserve">
    <value>Table des matières ?</value>
  </data>
  <data name="ToMFGCode" xml:space="preserve">
    <value>Vers le code de fabrication</value>
  </data>
  <data name="ToPartNumber" xml:space="preserve">
    <value>Vers le numéro de la pièce</value>
  </data>
  <data name="ToQty" xml:space="preserve">
    <value>Vers Quantité</value>
  </data>
  <data name="TreeBuildEnd" xml:space="preserve">
    <value>Construction de l'arborescence terminée, génération du journal ...</value>
  </data>
  <data name="TreeBuildStart" xml:space="preserve">
    <value>Toutes les options trouvées, création de l'arborescence </value>
  </data>
  <data name="TreeBuildSuccess" xml:space="preserve">
    <value>Construction de l'arborescence terminée avec succès!</value>
  </data>
  <data name="UnabletoMoveHotspot" xml:space="preserve">
    <value>Impossible de déplacer le point d'accès (Hotspot)</value>
  </data>
  <data name="Unabletoperformaction" xml:space="preserve">
    <value>Impossible d'exécuter l'action</value>
  </data>
  <data name="UOM" xml:space="preserve">
    <value>Unité de mesure</value>
  </data>
  <data name="UOMAdd" xml:space="preserve">
    <value>Unité de mesure - Ajouter</value>
  </data>
  <data name="UOM_Name" xml:space="preserve">
    <value>Unité de mesure</value>
  </data>
  <data name="UpdatedSuccessfully" xml:space="preserve">
    <value>Mis à jour avec succès</value>
  </data>
  <data name="UserAdd" xml:space="preserve">
    <value>Utilisateur - Ajouter</value>
  </data>
  <data name="UserCode" xml:space="preserve">
    <value>Code d'utilisateur</value>
  </data>
  <data name="UserName" xml:space="preserve">
    <value>Nom d'utilisateur</value>
  </data>
  <data name="VarnetandDatabaseCompareList" xml:space="preserve">
    <value>Liste de comparaison Varnet et base de données</value>
  </data>
  <data name="VarnetAssemblyNumber" xml:space="preserve">
    <value>Numéro d'assemblage Varnet</value>
  </data>
  <data name="VarnetImportLogList" xml:space="preserve">
    <value>Liste des journaux d'importation Varnet</value>
  </data>
  <data name="VarnetMainSection" xml:space="preserve">
    <value>Section principale Varnet</value>
  </data>
  <data name="VarnetOptionCode" xml:space="preserve">
    <value>Code d'option Varnet</value>
  </data>
  <data name="VarnetOrderNuber" xml:space="preserve">
    <value>Numéro de commande Varnet</value>
  </data>
  <data name="VarnetSubSection" xml:space="preserve">
    <value>Sous-section Varnet</value>
  </data>
  <data name="VarNetTextFile" xml:space="preserve">
    <value>Fichier texte de commande non valide</value>
  </data>
  <data name="VehicleDeletedSuccessfully" xml:space="preserve">
    <value>Supprimé avec succès</value>
  </data>
  <data name="VENDOR" xml:space="preserve">
    <value>Fournisseur</value>
  </data>
  <data name="Vendor Name" xml:space="preserve">
    <value>Nom du fournisseur</value>
  </data>
  <data name="Vendor Parma Code" xml:space="preserve">
    <value>Code Parma du fournisseur</value>
  </data>
  <data name="Vendor Part MFG Code" xml:space="preserve">
    <value>Code MFG de la partie fournisseur</value>
  </data>
  <data name="Vendor Part Number" xml:space="preserve">
    <value>Numéro de pièce du fournisseur</value>
  </data>
  <data name="VENDORINDEX" xml:space="preserve">
    <value>INDEX FOURNISSEUR</value>
  </data>
  <data name="VENDORNo" xml:space="preserve">
    <value>Code de fournisseur</value>
  </data>
  <data name="VendorPartInfo_MFGCode" xml:space="preserve">
    <value>Code de fabrication du fournisseur</value>
  </data>
  <data name="VendorPartInfo_PartNo" xml:space="preserve">
    <value>Numéro de pièce du fournisseur</value>
  </data>
  <data name="Vendor_Code" xml:space="preserve">
    <value>Code de fournisseur</value>
  </data>
  <data name="Vendor_Name" xml:space="preserve">
    <value>Nom du fournisseur</value>
  </data>
  <data name="VersionNumber" xml:space="preserve">
    <value>Numéro de version</value>
  </data>
  <data name="ViewedDateTime" xml:space="preserve">
    <value>Date et heure de consultation</value>
  </data>
  <data name="VIN" xml:space="preserve">
    <value>Index Fournisseur</value>
  </data>
  <data name="VIN#" xml:space="preserve">
    <value>NIV (Numéro d'Identification du Véhicule)</value>
  </data>
  <data name="vinalreadypresent" xml:space="preserve">
    <value>Le numéro d'identification du véhicule donné est déjà disponible dans les détails du véhicule</value>
  </data>
  <data name="VINShortNo" xml:space="preserve">
    <value>Numéro abrégé du numéro d'identification du véhicule</value>
  </data>
  <data name="warning" xml:space="preserve">
    <value>Avertissement</value>
  </data>
  <data name="WIPNEWOrderReImport" xml:space="preserve">
    <value>Il s'agit d'une nouvelle commande - Réimportation, veuillez confirmer - réimportation des codes d'option manquants</value>
  </data>
  <data name="WIPNOTPUBLISHEDReImport" xml:space="preserve">
    <value>La commande existe déjà mais n'est pas publiée - Réimportation, veuillez confirmer - Réimportation des codes d'option manquants</value>
  </data>
  <data name="WIPPUBLISHEDReImport" xml:space="preserve">
    <value>La commande existe déjà et publiée - N ° de version - Réimportation, veuillez confirmer - Réimportation des codes d'option manquants</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Oui</value>
  </data>
  <data name="YouhavebeenLoggedoutsuccessfully" xml:space="preserve">
    <value>Vous avez été déconnecté avec succès</value>
  </data>
  <data name="Yourrecordsaresafe" xml:space="preserve">
    <value>Vos dossiers sont en sécurité</value>
  </data>
  <data name="YouwanttoLogout" xml:space="preserve">
    <value>Vous souhaitez vous déconnecter</value>
  </data>
  <data name="Youwillnotbeabletorecoverthisdata!" xml:space="preserve">
    <value>Vous ne pourrez pas récupérer ces données!</value>
  </data>
  <data name="ResViewerDisplayNOVA" xml:space="preserve">
    <value>Affichage de la visionneuse Nova</value>
  </data>
  <data name="ResViewerDisplayPREVOST" xml:space="preserve">
    <value>Affichage de la visionneuse Prevost</value>
  </data>
  <data name="Recent" xml:space="preserve">
    <value>Récent</value>
  </data>
  <data name="RecentAssemblyPart" xml:space="preserve">
    <value>Pièce d'assemblage récente</value>
  </data>
  <data name="HideItemNumber" xml:space="preserve">
    <value>Masquer le numéro d'article</value>
  </data>
  <data name="NotinAssembly" xml:space="preserve">
    <value>NotinAssembly</value>
  </data>
  <data name="ResIsNotinTree" xml:space="preserve">
    <value>NotinArbre</value>
  </data>
  <data name="LocalNote" xml:space="preserve">
    <value>LocalRemarque</value>
  </data>
  <data name="ResDocumentTypeNameUser" xml:space="preserve">
    <value>Nom du type de document</value>
  </data>
  <data name="ResDivAssembliesAssociatedDrawing" xml:space="preserve">
    <value>Assemblages Dessin Associé</value>
  </data>
  <data name="ResAssembliesAssociatedDrawingDetails" xml:space="preserve">
    <value>Assemblages associés aux détails du dessin</value>
  </data>
  <data name="ResAssembliesAssociatedtoDrawingReport" xml:space="preserve">
    <value>Assemblages associés au rapport de dessin</value>
  </data>
  <data name="ResEn-Description" xml:space="preserve">
    <value>En-Description</value>
  </data>
  <data name="ResFn-Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="MsgNoImageFound" xml:space="preserve">
    <value>Aucune image trouvée</value>
  </data>
  <data name="ResImageName" xml:space="preserve">
    <value>Nom de l'image</value>
  </data>
  <data name="ResMsgEnterImageName" xml:space="preserve">
    <value>Msg Entrez le nom de l'image</value>
  </data>
  <data name="ResAssembliesAssociatedDrawingList" xml:space="preserve">
    <value>Liste des dessins assemblage</value>
  </data>
  <data name="Youwill" xml:space="preserve">
    <value>Youwill</value>
  </data>
  <data name="ResFavourites" xml:space="preserve">
    <value>Favoris</value>
  </data>
  <data name="ResManageParts_ImportLog" xml:space="preserve">
    <value>Gérer le journal d'importation des pièces</value>
  </data>
  <data name="ResMisMatchRecords" xml:space="preserve">
    <value>Enregistrements incorrects</value>
  </data>
  <data name="ResEnglishCretedBy" xml:space="preserve">
    <value>FR - Créé par</value>
  </data>
  <data name="ResEnglishCretedDate" xml:space="preserve">
    <value>FR - Date de création</value>
  </data>
  <data name="ResENRevisedBy" xml:space="preserve">
    <value>FR - Révisé par</value>
  </data>
  <data name="ResENRevisionDate" xml:space="preserve">
    <value>FR - Date de révision</value>
  </data>
  <data name="ResFNCretedBy" xml:space="preserve">
    <value>FN - Créé par</value>
  </data>
  <data name="ResFNCretedDate" xml:space="preserve">
    <value>FN - Date de création</value>
  </data>
  <data name="ResFNRevisedBy" xml:space="preserve">
    <value>FN - Révisé par</value>
  </data>
  <data name="ResFNRevisionDate" xml:space="preserve">
    <value>FN - Date de révision</value>
  </data>
  <data name="ResFavourite" xml:space="preserve">
    <value>Favoris</value>
  </data>
  <data name="ResAllVINNumberInfoReport" xml:space="preserve">
    <value>Tous les rapports d'informations sur le numéro VIN</value>
  </data>
  <data name="ResPartIndexReport" xml:space="preserve">
    <value>Rapport d'index de pièces</value>
  </data>
  <data name="ResMsgEnterOrder#/TopLevel" xml:space="preserve">
    <value>Entrez le numéro de commande / niveau supérieur</value>
  </data>
  <data name="ResOrder#/TopLevel" xml:space="preserve">
    <value>Numéro de commande / niveau supérieur</value>
  </data>
  <data name="ResPartIndex" xml:space="preserve">
    <value>Index des pièces</value>
  </data>
  <data name="ResPartIndexDetails" xml:space="preserve">
    <value>Détails de l'index des pièces</value>
  </data>
  <data name="ResSetSerail#Range" xml:space="preserve">
    <value>Définir la plage de numéros de sérail</value>
  </data>
  <data name="MsgInvalidAssembly#" xml:space="preserve">
    <value>Numéro d'assemblage invalide</value>
  </data>
  <data name="MsgInvalidImageName" xml:space="preserve">
    <value>Nom d'image non valide</value>
  </data>
  <data name="FilterApplied" xml:space="preserve">
    <value>Filtre appliqué</value>
  </data>
  <data name="OfflineDataDatetime" xml:space="preserve">
    <value>Date de dernière mise à jour du fichier de données hors ligne</value>
  </data>
  <data name="MsgRecordsHaveBeenShared" xml:space="preserve">
    <value>Partagé avec succès</value>
  </data>
  <data name="ResManypartselectedManagepartwith" xml:space="preserve">
    <value>Toute pièce sélectionnée dans Gérer la pièce avec cette action de masquage ne pourra pas être achetée dans la visionneuse</value>
  </data>
  <data name="ResSuperSessionReportDetails" xml:space="preserve">
    <value>Détails du rapport de remplacement</value>
  </data>
  <data name="LastUpdatedDate" xml:space="preserve">
    <value>Date de la dernière purge</value>
  </data>
  <data name="CannotImportAsThereAreUnpublishedBaseAssemblies" xml:space="preserve">
    <value>Le fichier sélectionné ne peut pas être importé, car nous avons des assemblages de base non publiés répertoriés dans Excel</value>
  </data>
  <data name="Pleaseenterfilterdetails" xml:space="preserve">
    <value>Veuillez entrer les détails du filtre</value>
  </data>
  <data name="ResENDesc" xml:space="preserve">
    <value>EN Desc</value>
  </data>
  <data name="ResENRev" xml:space="preserve">
    <value>EN Rev</value>
  </data>
  <data name="ResFRDesc" xml:space="preserve">
    <value>FR Desc</value>
  </data>
  <data name="ResFRRev" xml:space="preserve">
    <value>FR Rev</value>
  </data>
  <data name="ResLastImportDate" xml:space="preserve">
    <value>Date de la dernière purge</value>
  </data>
  <data name="ResPartsSectionRevisionReport" xml:space="preserve">
    <value>Rapport de révision de la section des pièces</value>
  </data>
  <data name="ResSectionNumber" xml:space="preserve">
    <value>Numéro de section</value>
  </data>
  <data name="ResServiceSectionRevisionReport" xml:space="preserve">
    <value>Rapport de révision de la section des services</value>
  </data>
  <data name="ResEnglishRevisionDate" xml:space="preserve">
    <value>Date de révision en anglais</value>
  </data>
  <data name="ResENNewPDFName" xml:space="preserve">
    <value>Anglais -Nouveau nom du PDF</value>
  </data>
  <data name="ResFNNewPDFName" xml:space="preserve">
    <value>Francais -Nouveau nom du PDF</value>
  </data>
  <data name="ResFrenchRevisionDate" xml:space="preserve">
    <value>Date de révision en francais</value>
  </data>
  <data name="ResIgnore" xml:space="preserve">
    <value>Ignorer</value>
  </data>
  <data name="ResRevise" xml:space="preserve">
    <value>Réviser</value>
  </data>
  <data name="ResSectionRevisionDetails" xml:space="preserve">
    <value>FN-Section Revision Details</value>
  </data>
  <data name="ResNew" xml:space="preserve">
    <value>Nouvelle</value>
  </data>
  <data name="ResRevised" xml:space="preserve">
    <value>modifié</value>
  </data>
  <data name="ResHotSpotMissing?" xml:space="preserve">
    <value>Point d'accès manquant ?</value>
  </data>
  <data name="ResItem#Missing?" xml:space="preserve">
    <value>Article # manquant ?</value>
  </data>
  <data name="ResPublishDeviationErrorReportDetails" xml:space="preserve">
    <value>Publier les détails du rapport d'erreur d'écart</value>
  </data>
  <data name="ResTranslationMissing?" xml:space="preserve">
    <value>Traduction manquante ?</value>
  </data>
  <data name="ResBOMMFGCode" xml:space="preserve">
    <value>Nomenclature Code fabricant</value>
  </data>
  <data name="ResBOMPart#" xml:space="preserve">
    <value>BON numéro de pièce</value>
  </data>
  <data name="ResFromSNNum" xml:space="preserve">
    <value>De SN</value>
  </data>
  <data name="ResHasRecommendedPart?" xml:space="preserve">
    <value>A une pièce recommandée ?</value>
  </data>
  <data name="ResHasSetMember?" xml:space="preserve">
    <value>A SetMember ?</value>
  </data>
  <data name="ResIgnoredDate" xml:space="preserve">
    <value>Date ignorée</value>
  </data>
  <data name="ResItemNum#" xml:space="preserve">
    <value>Article #</value>
  </data>
  <data name="ResParentPart#Description" xml:space="preserve">
    <value>Référence parente Description</value>
  </data>
  <data name="ResParentParts#" xml:space="preserve">
    <value>Numéro de pièce parent</value>
  </data>
  <data name="ResQty." xml:space="preserve">
    <value>Qté.</value>
  </data>
  <data name="ResQtyMissing" xml:space="preserve">
    <value>Qté manquante</value>
  </data>
  <data name="ResSeq." xml:space="preserve">
    <value>Séq.</value>
  </data>
  <data name="ResSpec." xml:space="preserve">
    <value>Spéc.</value>
  </data>
  <data name="ResToSNNum" xml:space="preserve">
    <value>Vers SN</value>
  </data>
  <data name="cannotdeletetherecords" xml:space="preserve">
    <value>FN-cannot delete the records</value>
  </data>
  <data name="Dependencyfound" xml:space="preserve">
    <value>FN-Dependency found</value>
  </data>
  <data name="MsgIgnoredSuccessfully" xml:space="preserve">
    <value>FN-Ignored Successfully</value>
  </data>
  <data name="MsgRevisedSuccessfully" xml:space="preserve">
    <value>FN-Revised Successfully</value>
  </data>
  <data name="ResPleasefillmandatoryfield" xml:space="preserve">
    <value>Fn-Please fill mandatory field</value>
  </data>
  <data name="ResSuperSessionReport" xml:space="preserve">
    <value>Rapport sur les détails du remplacement</value>
  </data>
  <data name="ActionOK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="ResOrderNP#" xml:space="preserve">
    <value>Numéro de commande / Niveau supérieur</value>
  </data>
  <data name="ResOrdernum" xml:space="preserve">
    <value>Numéro de commande</value>
  </data>
  <data name="ResServiceSubSection" xml:space="preserve">
    <value>Sous-section des services</value>
  </data>
  <data name="Thank You" xml:space="preserve">
    <value>Merci</value>
  </data>
  <data name="ResDependency" xml:space="preserve">
    <value>FN-Dependency</value>
  </data>
  <data name="ResDependencyList" xml:space="preserve">
    <value>FN-Dependency List</value>
  </data>
  <data name="ResPleaseSelecttheCheckbox" xml:space="preserve">
    <value>Veuillez cocher la case</value>
  </data>
  <data name="ResAdditionalPartDetails" xml:space="preserve">
    <value>Détails supplémentaires sur les pièces</value>
  </data>
  <data name="ResFilterforwithoutPriceParts" xml:space="preserve">
    <value>Filtrer pour sans Prix Pièces</value>
  </data>
  <data name="ResFilterforwithoutVendorParts" xml:space="preserve">
    <value>Filtrer sans pièces fournisseur</value>
  </data>
  <data name="ResFilterforwithPriceParts" xml:space="preserve">
    <value>Filtrer avec Prix Pièces</value>
  </data>
  <data name="ResFilterforwithVendorParts" xml:space="preserve">
    <value>Filtrer avec les pièces du fournisseur</value>
  </data>
  <data name="ResHideColumn" xml:space="preserve">
    <value>Masquer la colonne</value>
  </data>
  <data name="ResLeadTime" xml:space="preserve">
    <value>Délai de mise en œuvre</value>
  </data>
  <data name="ResPage#" xml:space="preserve">
    <value>Groupe</value>
  </data>
  <data name="ResPrice" xml:space="preserve">
    <value>Prix</value>
  </data>
  <data name="ResPriceDetails" xml:space="preserve">
    <value>Détails du prix</value>
  </data>
  <data name="ResRecommendedBuyList" xml:space="preserve">
    <value>Liste d'achat recommandée</value>
  </data>
  <data name="ResShowNonPurchasableParts?" xml:space="preserve">
    <value>Afficher les pièces non achetables ?</value>
  </data>
  <data name="ResShowRecommendedParts?" xml:space="preserve">
    <value>Afficher les pièces recommandées ?</value>
  </data>
  <data name="ResShowSetMembers?" xml:space="preserve">
    <value>Afficher les membres de l'ensemble ?</value>
  </data>
  <data name="ResU/M" xml:space="preserve">
    <value>U/M</value>
  </data>
  <data name="ResAllwitandwithoutPriceParts" xml:space="preserve">
    <value>Tous (avec et sans pièces de prix)</value>
  </data>
  <data name="ResAllwitandwithoutVendorParts" xml:space="preserve">
    <value>Tous (avec et sans pièces du vendeur)</value>
  </data>
  <data name="ResFilterations" xml:space="preserve">
    <value>Filtrations</value>
  </data>
  <data name="ResApply" xml:space="preserve">
    <value>FN-Apply</value>
  </data>
  <data name="ResBOMList" xml:space="preserve">
    <value>FN-BOM List</value>
  </data>
  <data name="PartsSectionNotif" xml:space="preserve">
    <value>FN-New/Modified Orders/Assemblies are available, please generate "Parts Section Revision Report" for more detail</value>
  </data>
  <data name="ResGoahead" xml:space="preserve">
    <value>FN-Go ahead</value>
  </data>
  <data name="ResIsIgnore" xml:space="preserve">
    <value>FN-Is Ignore?</value>
  </data>
  <data name="ResOtherDependency" xml:space="preserve">
    <value>Autre dépendance</value>
  </data>
  <data name="ResSelfDependency" xml:space="preserve">
    <value>FN-Self Dependency</value>
  </data>
  <data name="ResSelfOrOthers" xml:space="preserve">
    <value>FN-Self or Others</value>
  </data>
  <data name="Reswillbereplacedwithselectedparts" xml:space="preserve">
    <value>FN-will be replaced with selected parts</value>
  </data>
  <data name="ServiceSecMnlNoti" xml:space="preserve">
    <value>FN-New/Modified Service Manuals PDF files are available, please generate "Service Section Revision Report" for more detail</value>
  </data>
  <data name="ErrorDetails" xml:space="preserve">
    <value>Détails de l'erreur</value>
  </data>
  <data name="OfflineDataDownLoad" xml:space="preserve">
    <value>Téléchargement de données hors ligne</value>
  </data>
  <data name="OfflineEXEDownload" xml:space="preserve">
    <value>Téléchargement exécutable hors ligne</value>
  </data>
  <data name="ResActualFont" xml:space="preserve">
    <value>Font actuel</value>
  </data>
  <data name="ResAllVINs" xml:space="preserve">
    <value>tous NIV</value>
  </data>
  <data name="ResMsgEnterEmployeeName" xml:space="preserve">
    <value>Entrez le nom de l'employé</value>
  </data>
  <data name="ResOrderorVINLevel" xml:space="preserve">
    <value>Niveau Commande/NIV</value>
  </data>
  <data name="ResSectionDesc" xml:space="preserve">
    <value>Description de la section</value>
  </data>
  <data name="ResFilterationsselectioncriteria" xml:space="preserve">
    <value>Les critères de sélection</value>
  </data>
  <data name="ResPriceDetailsSelection" xml:space="preserve">
    <value>Critères de sélection-Détails des prix</value>
  </data>
  <data name="ResPlsWaitFinishImpactedOrders" xml:space="preserve">
    <value>FN-Please wait-finishing for all impacted orders-Top Levels</value>
  </data>
  <data name="ResIsDrawingmissing" xml:space="preserve">
    <value>Le dessin manque-t-il ?</value>
  </data>
  <data name="ResUploadDocumentDetails" xml:space="preserve">
    <value>FN-Upload Document Details</value>
  </data>
  <data name="MsgAddtoNewCartaddtotheexitingCart" xml:space="preserve">
    <value>FN-Create New/Load Exitsting Shopping Cart</value>
  </data>
  <data name="ResIsAddToShoppinCart" xml:space="preserve">
    <value>FN-IsAddToShoppingCart</value>
  </data>
  <data name="ResNYCT" xml:space="preserve">
    <value>NYCT</value>
  </data>
  <data name="ResSetShipToAddress" xml:space="preserve">
    <value>Liste de panier d'achat-Définir l'adresse d'expédition</value>
  </data>
  <data name="ResSelectShipToAddress" xml:space="preserve">
    <value>FN-Select Ship to Address</value>
  </data>
  <data name="ResSetShipAddress" xml:space="preserve">
    <value>Définir l'adresse d'expédition</value>
  </data>
  <data name="MsgShipToAddressisSetForAllParts" xml:space="preserve">
    <value>L'adresse d'expédition est définie pour toutes les pièces</value>
  </data>
  <data name="MsgSelectShippingAddressForAllParts" xml:space="preserve">
    <value>Veuillez sélectionner l'adresse d'expédition pour toutes les pièces</value>
  </data>
  <data name="MsgMFGRemovedSuccessfull" xml:space="preserve">
    <value>FN - Removed Successfully</value>
  </data>
  <data name="MsgSaveCartandmove" xml:space="preserve">
    <value>FN-Please Save Merged Parts for Carts</value>
  </data>
  <data name="MsgShipToAddressisSetForAllPartsAlready" xml:space="preserve">
    <value>FN-Ship to Address is Set for all Parts Already</value>
  </data>
  <data name="ParentItemNo" xml:space="preserve">
    <value>Numéro d'article parent</value>
  </data>
  <data name="ResNewSpecification" xml:space="preserve">
    <value>Nouvelle spécification</value>
  </data>
  <data name="ResOldSpecification" xml:space="preserve">
    <value>ancienne spécification</value>
  </data>
  <data name="ResActionCancelled" xml:space="preserve">
    <value>Action annulée !</value>
  </data>
  <data name="ResFromSNPrnt" xml:space="preserve">
    <value>De</value>
  </data>
  <data name="ItemNoprint" xml:space="preserve">
    <value>Article#</value>
  </data>
  <data name="QTYprint" xml:space="preserve">
    <value>QTÉ</value>
  </data>
  <data name="MsgPleaseEnternumberafter" xml:space="preserve">
    <value>FN-Please enter only numbers after -</value>
  </data>
  <data name="PartsNotification" xml:space="preserve">
    <value>FN-Parts Notification -</value>
  </data>
  <data name="ResUpdates" xml:space="preserve">
    <value> Updates</value>
  </data>
  <data name="ServiceManualsNotification" xml:space="preserve">
    <value>FN-Service Manuals Notification - </value>
  </data>
  <data name="Forinquiresortocreateanaccount(US&amp;Canada),pleasecontacttheappropriateadministratorbelow" xml:space="preserve">
    <value>Pour toute demande de renseignements ou pour la création d'un compte (États-Unis et Canada), veuillez contacter l'administrateur approprié ci-dessous</value>
  </data>
  <data name="ResAddorUpdate" xml:space="preserve">
    <value>Ajouter/Mettre à jour</value>
  </data>
  <data name="ResDocuments" xml:space="preserve">
    <value>Documents</value>
  </data>
  <data name="ResLocalNotesLanguageList" xml:space="preserve">
    <value>FN-Local Notes Language List</value>
  </data>
  <data name="ResOrderAccessControl" xml:space="preserve">
    <value>Commander le contrôle d'accès</value>
  </data>
  <data name="ResOrderUserAccess" xml:space="preserve">
    <value>Commande - Accès utilisateur</value>
  </data>
  <data name="ResSearchOrder" xml:space="preserve">
    <value>Commande de recherche#</value>
  </data>
  <data name="ResSearchSection" xml:space="preserve">
    <value>Section de recherche#</value>
  </data>
  <data name="ResSearchSubSection" xml:space="preserve">
    <value>Rechercher dans la sous-section#</value>
  </data>
  <data name="ResADDefectNotification" xml:space="preserve">
    <value>FN-AD - Defect Notification</value>
  </data>
  <data name="ResAddOrSave" xml:space="preserve">
    <value>Ajouter/Enregistrer</value>
  </data>
  <data name="ResEDElectricDiagrams" xml:space="preserve">
    <value>Schémas électriques</value>
  </data>
  <data name="ResITPManual" xml:space="preserve">
    <value>Inspection et plan de test</value>
  </data>
  <data name="ResOPOperatorManual" xml:space="preserve">
    <value>Manuel de l'opérateur</value>
  </data>
  <data name="ResPDPneumaticdiagrams" xml:space="preserve">
    <value>Schémas pneumatiques</value>
  </data>
  <data name="ResPMPartsManual" xml:space="preserve">
    <value>FN- PM-Parts Manual</value>
  </data>
  <data name="ResSRTStandardRepairTime" xml:space="preserve">
    <value>FN-SRT - Standard Repair Time</value>
  </data>
  <data name="ResDocumentSelection" xml:space="preserve">
    <value>Sélection de documents</value>
  </data>
  <data name="ResPleaseSelectAtleastoneSubSectionunderthisSection" xml:space="preserve">
    <value>Veuillez sélectionner au moins une sous-section dans cette section</value>
  </data>
  <data name="ResOrderAccessDetails" xml:space="preserve">
    <value>Détails d'accès à la commande</value>
  </data>
  <data name="ResPleaseSelectAtleastoneOrder" xml:space="preserve">
    <value>FN-Please select an order</value>
  </data>
  <data name="ResPlsWaitOrderAssociationInProgress" xml:space="preserve">
    <value>FN-Please wait order association in progress</value>
  </data>
  <data name="ResProgramsandRevisionList" xml:space="preserve">
    <value>Programmes et liste de révision</value>
  </data>
  <data name="ResWarantyandPolicymanual" xml:space="preserve">
    <value>Manuel des politiques et procédures de garantie</value>
  </data>
  <data name="ResNoChangesInOrderAssociation" xml:space="preserve">
    <value>FN-No changes in order association</value>
  </data>
  <data name="AreYouSureYouWantreselectAll" xml:space="preserve">
    <value>Voulez-vous vraiment sélectionner à nouveau Toutes les commandes ?</value>
  </data>
  <data name="AreYouSureYouWantToUnSelectAll" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir tout désélectionner</value>
  </data>
  <data name="ResPleaseSelectAtleastoneOrderOrNoChangesinOrderSelection" xml:space="preserve">
    <value>Veuillez sélectionner au moins une commande / Aucun changement dans la sélection de commande</value>
  </data>
  <data name="ResPleaseSelectAtleastonesectionandsubsection" xml:space="preserve">
    <value>Veuillez sélectionner au moins une section et une sous-section</value>
  </data>
  <data name="ResServManuals" xml:space="preserve">
    <value>Manuels de service</value>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>La date de livraison</value>
  </data>
  <data name="Qty/Vehiclecnt" xml:space="preserve">
    <value>Qté./Nb. de VIN</value>
  </data>
  <data name="ResImportOrderWiseDeliverDate" xml:space="preserve">
    <value>Date de livraison de la commande d''importation</value>
  </data>
  <data name="ResOrderDetailsCannotbeEmpty" xml:space="preserve">
    <value>Les détails de la commande ne peuvent pas être vides</value>
  </data>
  <data name="ResSecSelected" xml:space="preserve">
    <value>Choisie</value>
  </data>
  <data name="ResSelSeccode" xml:space="preserve">
    <value>Selectionnez le code de section</value>
  </data>
  <data name="ChasisSpecificCatalogue" xml:space="preserve">
    <value>Catalogue spécifique au châssis</value>
  </data>
  <data name="ModelOrderSpecificCatalogue" xml:space="preserve">
    <value>Catalogue spécifique au modèle et à la commande</value>
  </data>
  <data name="ResReleaseinformation" xml:space="preserve">
    <value>Divulguer des renseignements</value>
  </data>
  <data name="ResMaskingActionDetails" xml:space="preserve">
    <value>Détails de l'action de masquage</value>
  </data>
  <data name="ResProductTypeDetails" xml:space="preserve">
    <value>Détails du type de produit</value>
  </data>
  <data name="ResImportHistory" xml:space="preserve">
    <value>Historique des importations</value>
  </data>
  <data name="ResOnlyslsxfile" xml:space="preserve">
    <value>Uniquement un fichier .xlsx|.xls|.csv de 30 Mo ou moins contenant les en-têtes de colonnes requis.</value>
  </data>
  <data name="ResAssemblyPartList" xml:space="preserve">
    <value>Liste des pièces d'assemblage</value>
  </data>
  <data name="ResMenuSwitch" xml:space="preserve">
    <value>Changement de menu</value>
  </data>
  <data name="MsgPleaseSelectOrderVINtoChange" xml:space="preserve">
    <value>Veuillez sélectionner Commande/VIN pour changer de nouveau propriétaire</value>
  </data>
  <data name="ResDownloadPDFFile" xml:space="preserve">
    <value>Télécharger le fichier PDF</value>
  </data>
  <data name="ResReportProblemorFeedback" xml:space="preserve">
    <value>Signaler un problème ou un commentaire</value>
  </data>
  <data name="ResShowOnlyImage" xml:space="preserve">
    <value>Afficher uniquement l'image</value>
  </data>
  <data name="ResUploadAttachments" xml:space="preserve">
    <value>Télécharger les pièces jointes</value>
  </data>
  <data name="ResViewServiceManuals" xml:space="preserve">
    <value>Afficher les manuels de service</value>
  </data>
  <data name="ResMFGCode2" xml:space="preserve">
    <value>Code constructeur</value>
  </data>
</root>