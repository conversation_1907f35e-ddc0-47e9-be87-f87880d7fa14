﻿using AMMSCore.Models;
using DocumentFormat.OpenXml.Office.Word;
using DocumentFormat.OpenXml.Office2010.Excel;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Services;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class AllocationLogicServices
    {
        public static void UpdatePartsStock(int partID, decimal invoicedQty, int puchaseOrderID, int wareHouseID, DateTime invoiceDate, decimal landingCost, int supplierID, int Company_ID, int Branch, string connString, int LogException)
        {
            try
            {





                int branchID = Convert.ToInt32(Branch);
                //Update Parts Stock

                int binLocationID = 0;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "SELECT * FROM GNM_PartsStockDetail WHERE Parts_ID = @Parts_ID AND WareHouse_ID = @WareHouse_ID";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@Parts_ID", partID);
                            cmd.Parameters.AddWithValue("@WareHouse_ID", wareHouseID);
                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                // Check if a record exists
                                if (reader.Read())
                                {
                                    decimal totalStock = reader["TotalStock"] != DBNull.Value ? Convert.ToDecimal(reader["TotalStock"]) + invoicedQty : invoicedQty;
                                    decimal gitQuantity = reader["GITQuantity"] != DBNull.Value ? Convert.ToDecimal(reader["GITQuantity"]) + invoicedQty : invoicedQty;
                                    decimal pendingPurchaseOrderQuantity = reader["PendingPurchaseOrderQuantity"] != DBNull.Value ? Convert.ToDecimal(reader["PendingPurchaseOrderQuantity"]) : 0;

                                    string updateQuery = "UPDATE GNM_PartsStockDetail SET TotalStock = @TotalStock, GITQuantity = @GITQuantity";
                                    if (puchaseOrderID != 0)
                                    {
                                        pendingPurchaseOrderQuantity -= invoicedQty;
                                        updateQuery += ", PendingPurchaseOrderQuantity = @PendingPurchaseOrderQuantity";
                                    }
                                    updateQuery += " WHERE Parts_ID = @Parts_ID AND WareHouse_ID = @WareHouse_ID";
                                    using (SqlCommand updateCmd = new SqlCommand(updateQuery, conn))
                                    {
                                        updateCmd.CommandType = CommandType.Text;
                                        updateCmd.Parameters.AddWithValue("@TotalStock", totalStock);
                                        updateCmd.Parameters.AddWithValue("@GITQuantity", gitQuantity);
                                        updateCmd.Parameters.AddWithValue("@PendingPurchaseOrderQuantity", pendingPurchaseOrderQuantity);
                                        updateCmd.Parameters.AddWithValue("@Parts_ID", partID);
                                        updateCmd.Parameters.AddWithValue("@WareHouse_ID", wareHouseID);
                                        updateCmd.ExecuteNonQuery();
                                    }
                                }
                                else
                                {
                                    string binLocationQuery = "SELECT BinLocation_ID FROM GNM_BinLocation WHERE Branch_ID = @Branch_ID AND Company_ID = @Company_ID AND BinLocation_IsDefault = 1 AND BinLocation_IsActive = 1 AND WareHouse_ID = @WareHouse_ID";
                                    using (SqlCommand binLocationCmd = new SqlCommand(binLocationQuery, conn))
                                    {
                                        binLocationCmd.CommandType = CommandType.Text;
                                        binLocationCmd.Parameters.AddWithValue("@Branch_ID", branchID);
                                        binLocationCmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                                        binLocationCmd.Parameters.AddWithValue("@WareHouse_ID", wareHouseID);
                                        binLocationID = (int)cmd.ExecuteScalar();



                                    }
                                    string insertQuery = "INSERT INTO GNM_PartsStockDetail (WareHouse_ID, Branch_ID, Company_ID, MinOrderQty, Parts_ID, TotalStock, GITQuantity, ReOrderLevel, ReOrderLevelQuantity, BinLocation_ID) VALUES (@WareHouse_ID, @Branch_ID, @Company_ID, @MinOrderQty, @Parts_ID, @TotalStock, @GITQuantity, @ReOrderLevel, @ReOrderLevelQuantity, @BinLocation_ID)";
                                    using (SqlCommand insertcmd = new SqlCommand(insertQuery, conn))
                                    {
                                        insertcmd.CommandType = CommandType.Text;

                                        insertcmd.Parameters.AddWithValue("@WareHouse_ID", wareHouseID);
                                        insertcmd.Parameters.AddWithValue("@Branch_ID", branchID);
                                        insertcmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                                        insertcmd.Parameters.AddWithValue("@MinOrderQty", 1);
                                        insertcmd.Parameters.AddWithValue("@Parts_ID", partID);
                                        insertcmd.Parameters.AddWithValue("@TotalStock", invoicedQty);
                                        insertcmd.Parameters.AddWithValue("@GITQuantity", invoicedQty);
                                        insertcmd.Parameters.AddWithValue("@ReOrderLevel", 1);
                                        insertcmd.Parameters.AddWithValue("@ReOrderLevelQuantity", 1);
                                        insertcmd.Parameters.AddWithValue("@BinLocation_ID", binLocationID);
                                        insertcmd.ExecuteNonQuery();



                                    }

                                }
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                       
                    }
                }


                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = @"
                        SELECT TOP 1 * 
                        FROM GNM_PartsSupplierDetail 
                        WHERE Parts_ID = @Parts_ID AND Supplier_ID = @Supplier_ID 
                        ORDER BY Effectivefrom DESC";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@Parts_ID", partID);
                            cmd.Parameters.AddWithValue("@Supplier_ID", supplierID);
                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    string updateQuery = @"
                                    UPDATE GNM_PartsSupplierDetail
                                    SET LastInvoicedDate = @LastInvoicedDate, LatestPurchaseCost = @LatestPurchaseCost
                                    WHERE Parts_ID = @Parts_ID AND Supplier_ID = @Supplier_ID AND Effectivefrom = (
                                        SELECT MAX(Effectivefrom)
                                        FROM GNM_PartsSupplierDetail
                                        WHERE Parts_ID = @Parts_ID AND Supplier_ID = @Supplier_ID
                                    )";

                                    using (cmd = new SqlCommand(updateQuery, conn))
                                    {
                                        cmd.CommandType = CommandType.Text;
                                        cmd.Parameters.AddWithValue("@LastInvoicedDate", invoiceDate);
                                        cmd.Parameters.AddWithValue("@LatestPurchaseCost", landingCost);
                                        cmd.Parameters.AddWithValue("@Parts_ID", partID);
                                        cmd.Parameters.AddWithValue("@Supplier_ID", supplierID);
                                        cmd.ExecuteNonQuery();
                                    }


                                }
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                       
                    }
                }



                int PurchaseOrderPartsCount = 0;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "SELECT COUNT(*) FROM PRT_PurchaseOrderPartsDetail WHERE PurchaseOrder_ID = @PurchaseOrder_ID AND Parts_ID = @Parts_ID";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@PurchaseOrder_ID", puchaseOrderID);
                            cmd.Parameters.AddWithValue("@Parts_ID", partID);
                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            PurchaseOrderPartsCount = (int)cmd.ExecuteScalar();

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        
                    }
                }

                //purchase order update
                if (puchaseOrderID != 0)
                {
                    //if purchase order type is not back order
                    if (PurchaseOrderPartsCount == 1)
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            string query = "SELECT COUNT(*) FROM PRT_PurchaseOrderPartsDetail WHERE PurchaseOrder_ID = @PurchaseOrder_ID AND Parts_ID = @Parts_ID";
                            SqlCommand cmd = null;

                            try
                            {
                                using (cmd = new SqlCommand(query, conn))
                                {
                                    cmd.CommandType = CommandType.Text;
                                    cmd.Parameters.AddWithValue("@PurchaseOrder_ID", puchaseOrderID);
                                    cmd.Parameters.AddWithValue("@Parts_ID", partID);

                                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                    {
                                        conn.Open();
                                    }
                                    int count = (int)cmd.ExecuteScalar();
                                    if (count == 1)
                                    {
                                        decimal invoicedQuantity = 0;
                                        string selectQuery = "SELECT * FROM PRT_PurchaseOrderPartsDetail WHERE PurchaseOrder_ID = @PurchaseOrder_ID AND Parts_ID = @Parts_ID";
                                        using (cmd = new SqlCommand(selectQuery, conn))
                                        {
                                            cmd.CommandType = CommandType.Text;
                                            cmd.Parameters.AddWithValue("@PurchaseOrder_ID", puchaseOrderID);
                                            cmd.Parameters.AddWithValue("@Parts_ID", partID);
                                            using (SqlDataReader reader = cmd.ExecuteReader())
                                            {
                                                if (reader.Read())
                                                {
                                                    invoicedQuantity = reader["InvoicedQuantity"] != DBNull.Value ? Convert.ToDecimal(reader["InvoicedQuantity"]) + invoicedQty : invoicedQty;

                                                }
                                            }


                                        }
                                        string updateQuery = "UPDATE PRT_PurchaseOrderPartsDetail SET InvoicedQuantity = @InvoicedQuantity WHERE PurchaseOrder_ID = @PurchaseOrder_ID AND Parts_ID = @Parts_ID";
                                        using (cmd = new SqlCommand(updateQuery, conn))
                                        {
                                            cmd.CommandType = CommandType.Text;
                                            cmd.Parameters.AddWithValue("@InvoicedQuantity", invoicedQuantity);
                                            cmd.Parameters.AddWithValue("@PurchaseOrder_ID", puchaseOrderID);
                                            cmd.Parameters.AddWithValue("@Parts_ID", partID);

                                            cmd.ExecuteNonQuery();

                                        }
                                    }



                                }
                            }
                            catch (Exception ex)
                            {
                                if (LogException == 1)
                                {
                                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                }

                            }
                            finally
                            {
                                
                            }
                        }

                    }
                    else
                    {



                        List<PRM_AllocationPriority> allocationList = new List<PRM_AllocationPriority>();
                        List<PRT_PartsOrder> partsOrderList = new List<PRT_PartsOrder>();
                        List<PRT_PurchaseOrderPartsDetail> purchaseOrderPartsList1 = new List<PRT_PurchaseOrderPartsDetail>();

                        List<SqlParameter> sqlParameters = new List<SqlParameter>
                    {
                        new SqlParameter("@Company_ID", SqlDbType.Int) { Value = Company_ID }

                    };
                        string query3 = "SELECT * FROM PRM_AllocationPriority WHERE Company_ID = @Company_ID";
                        allocationList = Common.GetValueFromDB<List<PRM_AllocationPriority>>(query3, sqlParameters, connString, LogException, false, 0, false);
                        string query = "select distinct a.* from PRT_PartsOrder a inner join PRT_PurchaseOrderPartsDetail b on a.PartsOrder_ID = b.PartsOrder_ID where b.PurchaseOrder_ID=" + puchaseOrderID;



                        partsOrderList = Common.GetValueFromDB<List<PRT_PartsOrder>>(query, null, connString, LogException, false, 0, false);

                        string query2 = "SELECT * FROM PRT_PurchaseOrderPartsDetail WHERE PurchaseOrder_ID = @PurchaseOrder_ID";
                        List<SqlParameter> sqlParameters2 = new List<SqlParameter>
                    {
                        new SqlParameter("@PurchaseOrder_ID", SqlDbType.Int) { Value = puchaseOrderID }

                    };
                        purchaseOrderPartsList1 = Common.GetValueFromDB<List<PRT_PurchaseOrderPartsDetail>>(query2, sqlParameters2, connString, LogException, false, 0, false);


                        List<int> partsOrderIDs = new List<int>();
                        var x = (from a in purchaseOrderPartsList1
                                 join b in partsOrderList on a.PartsOrder_ID equals b.PartsOrder_ID
                                 join c in allocationList on b.PartsOrderType_ID equals c.OrderType_ID
                                 where b.CustomerOrderClass_ID == c.OrderClass_ID
                                 orderby c.AllocationPriority_Priority descending
                                 select new
                                 {
                                     b.PartsOrder_ID
                                 }).Distinct();

                        foreach (var xobj in x)
                        {
                            partsOrderIDs.Add(xobj.PartsOrder_ID);
                        }

                        //looping through the parts order list and updating the Purchase Order Parts Detail
                        for (int i = 0; i < partsOrderIDs.Count; i++)
                        {
                            int partsOrderID = partsOrderIDs[i];

                            decimal pendingQty = 0;


                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                string selectQuery = @"
                                SELECT ApprovedQuantity, InvoicedQuantity, BackOrderCancelledQuantity 
                                FROM PRT_PurchaseOrderPartsDetail 
                                WHERE PurchaseOrder_ID = @PurchaseOrderID AND Parts_ID = @PartID AND PartsOrder_ID = @PartsOrderID";
                                SqlCommand cmd = null;

                                try
                                {
                                    using (cmd = new SqlCommand(query, conn))
                                    {
                                        cmd.CommandType = CommandType.Text;
                                        cmd.Parameters.AddWithValue("@PurchaseOrderID", puchaseOrderID);
                                        cmd.Parameters.AddWithValue("@PartID", partID);
                                        cmd.Parameters.AddWithValue("@PartsOrderID", partsOrderID);

                                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                        {
                                            conn.Open();
                                        }
                                        using (SqlDataReader reader = cmd.ExecuteReader())
                                        {
                                            decimal invoicedQuantity = 0;
                                            if (reader.Read())
                                            {
                                                decimal approvedQuantity = reader["ApprovedQuantity"] != DBNull.Value ? Convert.ToDecimal(reader["ApprovedQuantity"]) : 0;
                                                invoicedQuantity = reader["InvoicedQuantity"] != DBNull.Value ? Convert.ToDecimal(reader["InvoicedQuantity"]) : 0;
                                                decimal backOrderCancelledQuantity = reader["BackOrderCancelledQuantity"] != DBNull.Value ? Convert.ToDecimal(reader["BackOrderCancelledQuantity"]) : 0;
                                                pendingQty = approvedQuantity - invoicedQuantity - backOrderCancelledQuantity;
                                            }


                                            decimal? currentInvoicedQuantity = invoicedQuantity;
                                            if (invoicedQty > pendingQty)
                                            {

                                                string updateQuery = @"
                                            UPDATE PRT_PurchaseOrderPartsDetail 
                                            SET InvoicedQuantity = @NewInvoicedQuantity 
                                            WHERE PurchaseOrder_ID = @PurchaseOrderID 
                                              AND Parts_ID = @PartID 
                                              AND PartsOrder_ID = @PartsOrderID";
                                                decimal newInvoicedQuantity = (currentInvoicedQuantity ?? 0) + pendingQty;
                                                using (cmd = new SqlCommand(updateQuery, conn))
                                                {
                                                    cmd.CommandType = CommandType.Text;
                                                    cmd.Parameters.AddWithValue("@NewInvoicedQuantity", newInvoicedQuantity);
                                                    cmd.Parameters.AddWithValue("@PurchaseOrderID", puchaseOrderID);
                                                    cmd.Parameters.AddWithValue("@PartID", partID);
                                                    cmd.Parameters.AddWithValue("@PartsOrderID", partsOrderID);
                                                    cmd.ExecuteNonQuery();
                                                    invoicedQty = invoicedQty - pendingQty;

                                                }

                                            }
                                            else
                                            {
                                                string updateQuery = @"
                                            UPDATE PRT_PurchaseOrderPartsDetail 
                                            SET InvoicedQuantity = @NewInvoicedQuantity 
                                            WHERE PurchaseOrder_ID = @PurchaseOrderID 
                                              AND Parts_ID = @PartID 
                                              AND PartsOrder_ID = @PartsOrderID";
                                                decimal newInvoicedQuantity = (currentInvoicedQuantity ?? 0) + invoicedQty;
                                                using (cmd = new SqlCommand(updateQuery, conn))
                                                {
                                                    cmd.CommandType = CommandType.Text;
                                                    cmd.Parameters.AddWithValue("@NewInvoicedQuantity", newInvoicedQuantity);
                                                    cmd.Parameters.AddWithValue("@PurchaseOrderID", puchaseOrderID);
                                                    cmd.Parameters.AddWithValue("@PartID", partID);
                                                    cmd.Parameters.AddWithValue("@PartsOrderID", partsOrderID);
                                                    cmd.ExecuteNonQuery();
                                                    invoicedQty = 0;
                                                    return;

                                                }
                                            }



                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    if (LogException == 1)
                                    {
                                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    }

                                }
                                finally
                                {
                                   
                                }
                            }
                        }
                    }
                    //for closing the purchase order
                    List<PRT_PurchaseOrderPartsDetail> purchaseOrderPartsList = new List<PRT_PurchaseOrderPartsDetail>();
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = @"
                SELECT 
                    PurchaseOrderPartsDetail_ID,
                    PurchaseOrder_ID,
                    Parts_ID,
                    SupplierPrice,
                    RequestedQuantity,
                    ApprovedQuantity,
                    InvoicedQuantity,
                    BackOrderCancelledQuantity,
                    DiscountPercentage,
                    DiscountAmount,
                    TaxStructure_ID,
                    TaxAmount,
                    Amount,
                    PartsOrder_ID,
                    DiscountedAmount,
                    MRP
                FROM PRT_PurchaseOrderPartsDetail
                WHERE PurchaseOrder_ID = @PurchaseOrderID";
                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(query, conn))
                            {
                                cmd.CommandType = CommandType.Text;
                                cmd.Parameters.AddWithValue("@PurchaseOrderID", puchaseOrderID);

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {

                                    while (reader.Read())
                                    {
                                        var detail = new PRT_PurchaseOrderPartsDetail
                                        {
                                            PurchaseOrderPartsDetail_ID = Convert.ToInt32(reader["PurchaseOrderPartsDetail_ID"]),
                                            PurchaseOrder_ID = Convert.ToInt32(reader["PurchaseOrder_ID"]),
                                            Parts_ID = Convert.ToInt32(reader["Parts_ID"]),
                                            SupplierPrice = Convert.ToDecimal(reader["SupplierPrice"]),
                                            RequestedQuantity = reader["RequestedQuantity"] as decimal?,
                                            ApprovedQuantity = reader["ApprovedQuantity"] as decimal?,
                                            InvoicedQuantity = reader["InvoicedQuantity"] as decimal?,
                                            BackOrderCancelledQuantity = reader["BackOrderCancelledQuantity"] as decimal?,
                                            DiscountPercentage = reader["DiscountPercentage"] as decimal?,
                                            DiscountAmount = reader["DiscountAmount"] as decimal?,
                                            TaxStructure_ID = reader["TaxStructure_ID"] as int?,
                                            TaxAmount = reader["TaxAmount"] as decimal?,
                                            Amount = Convert.ToDecimal(reader["Amount"]),
                                            PartsOrder_ID = reader["PartsOrder_ID"] as int?,
                                            DiscountedAmount = reader["DiscountedAmount"] as decimal?,
                                            MRP = reader["MRP"] as decimal?
                                        };

                                        purchaseOrderPartsList.Add(detail);
                                    }





                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                           
                        }
                    }

                    decimal pendingQuty = 0;
                    for (int i = 0; i < purchaseOrderPartsList.Count; i++)
                    {
                        decimal eachPendingQuty = 0;
                        eachPendingQuty = (purchaseOrderPartsList[i].ApprovedQuantity != null ? purchaseOrderPartsList[i].ApprovedQuantity.Value : 0) - (purchaseOrderPartsList[i].InvoicedQuantity != null ? purchaseOrderPartsList[i].InvoicedQuantity.Value : 0) - (purchaseOrderPartsList[i].BackOrderCancelledQuantity != null ? purchaseOrderPartsList[i].BackOrderCancelledQuantity.Value : 0);
                        pendingQuty += eachPendingQuty;
                    }

                    string statusName = pendingQuty <= 0 ? "Closed" : "Partial";
                    int poStatusID = 0;
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string statusQuery = @"
                SELECT RefMasterDetail_ID
                FROM GNM_RefMasterDetail
                INNER JOIN GNM_RefMaster ON GNM_RefMaster.RefMaster_ID = GNM_RefMasterDetail.RefMaster_ID
                WHERE GNM_RefMaster.RefMaster_Name = 'POSTATUS' AND RefMasterDetail_Short_Name = @StatusName";
                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(statusQuery, conn))
                            {
                                cmd.CommandType = CommandType.Text;
                                cmd.Parameters.AddWithValue("@StatusName", statusName);

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                poStatusID = Convert.ToInt32(cmd.ExecuteScalar());
                                if (poStatusID > 0)
                                {
                                    string updateQuery = @"
                                    UPDATE PRT_PurchaseOrder
                                    SET POStatus_ID = @POStatusID
                                    WHERE PurchaseOrder_ID = @PurchaseOrderID";
                                    using (SqlCommand cmd2 = new SqlCommand(updateQuery, conn))
                                    {
                                        cmd2.CommandType = CommandType.Text;
                                        cmd2.Parameters.AddWithValue("@POStatusID", poStatusID);
                                        cmd2.Parameters.AddWithValue("@PurchaseOrderID", puchaseOrderID);

                                        cmd2.ExecuteNonQuery();

                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                         
                        }
                    }


                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #region ::: UpdatePartsStockGRN :::
        /// <summary>
        /// 
        /// </summary>
        public static WAC UpdatePartsStockGRN(UpdatePartsStockGRNList Obj, string connString, int LogException, int partID, int wareHouseID, int binlocationID, decimal receiptQty, decimal landingRate, bool allocation, bool GITUpdate, bool TotalStockUpdate, decimal damageQty, decimal invoiceQty, int Company_ID = 0, int branchID = 0)
        {
            WAC returnWAC = new WAC();
            try
            {
                if (Company_ID == 0) { Company_ID = Convert.ToInt32(Obj.Company_ID); }
                if (branchID == 0) { branchID = Convert.ToInt32(Obj.Branch); }


                returnWAC = CalculationServices.WAC_calculation(partID, wareHouseID, binlocationID, receiptQty, landingRate, allocation, GITUpdate, TotalStockUpdate, damageQty, connString, LogException);

                

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string selectQuery = @"
                SELECT * 
                FROM GNM_PartsStockDetail 
                WHERE Parts_ID = @PartsID AND WareHouse_ID = @WareHouseID";
                    SqlCommand command = new SqlCommand(selectQuery, conn);
                    command.Parameters.AddWithValue("@PartsID", partID);
                    command.Parameters.AddWithValue("@WareHouseID", wareHouseID);
                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                    {
                        conn.Open();
                    }
                    SqlDataReader reader = command.ExecuteReader();
                    if (reader.HasRows)
                    {
                        reader.Read();
                        int partsStockDetailID = Convert.ToInt32(reader["PartsStockDetail_ID"]);
                        decimal? currentTotalStock = reader["TotalStock"] as decimal?;
                        decimal? currentBinStock = reader["BinStock"] as decimal?;
                        decimal? currentFreeStock = reader["FreeStock"] as decimal?;
                        decimal? currentGITQuantity = reader["GITQuantity"] as decimal?;
                        DateTime? lastStockUpdatedDate = reader["LastStockUpdatedDate"] as DateTime?;
                        reader.Close();
                        decimal totalStock = currentTotalStock.HasValue ? currentTotalStock.Value - damageQty : 0;
                        if (TotalStockUpdate)
                        {
                            totalStock = currentTotalStock.HasValue ? currentTotalStock.Value + receiptQty : receiptQty;
                        }
                        decimal GITQuantity = 0;
                        if (GITUpdate)
                        {
                             GITQuantity= currentGITQuantity.HasValue != null ? currentGITQuantity.Value - invoiceQty : 0;
                        }
                        string updateQuery = @"
                    UPDATE GNM_PartsStockDetail
                    SET 
                        WeightedAverageCost = @WeightedAverageCost,
                        LastStockUpdatedDate = @LastStockUpdatedDate,
                        TotalStock = @TotalStock,
                        BinStock = @BinStock,
                        FreeStock = @FreeStock";

                        if (GITUpdate)
                        {
                            updateQuery += ", GITQuantity = @GITQuantity"; // Fix: added comma before GITQuantity
                        }

                        updateQuery += " WHERE PartsStockDetail_ID = @PartsStockDetail_ID";
                        SqlCommand updateCommand = new SqlCommand(updateQuery, conn);
                        updateCommand.Parameters.AddWithValue("@WeightedAverageCost", returnWAC.ClosingWAC);
                        updateCommand.Parameters.AddWithValue("@LastStockUpdatedDate", lastStockUpdatedDate ?? DateTime.Now);
                        updateCommand.Parameters.AddWithValue("@TotalStock", totalStock);
                        updateCommand.Parameters.AddWithValue("@BinStock", currentBinStock.HasValue ? currentBinStock.Value + receiptQty : receiptQty);
                        updateCommand.Parameters.AddWithValue("@FreeStock", currentFreeStock.HasValue ? currentFreeStock.Value + receiptQty : receiptQty);
                        if (GITUpdate)
                        {
                            updateCommand.Parameters.AddWithValue("@GITQuantity", GITQuantity);
                        }
                        updateCommand.Parameters.AddWithValue("@PartsStockDetail_ID", partsStockDetailID);
                        updateCommand.ExecuteNonQuery();


                    }
                    else
                    {
                        reader.Close();
                        string insertQuery = @"
                    INSERT INTO GNM_PartsStockDetail
                    (Parts_ID, WareHouse_ID, BinLocation_ID, BinStock, FreeStock, TotalStock, WeightedAverageCost, Company_ID, Branch_ID)
                    VALUES
                    (@PartsID, @WareHouseID, @BinLocationID, @BinStock, @FreeStock, @TotalStock, @WeightedAverageCost, @CompanyID, @BranchID)";
                        SqlCommand insertCommand = new SqlCommand(insertQuery, conn);
                        insertCommand.Parameters.AddWithValue("@PartsID", partID);
                        insertCommand.Parameters.AddWithValue("@WareHouseID", wareHouseID);
                        insertCommand.Parameters.AddWithValue("@BinLocationID", binlocationID);  // BinLocation_ID can be null initially
                        insertCommand.Parameters.AddWithValue("@BinStock", receiptQty);
                        insertCommand.Parameters.AddWithValue("@FreeStock", receiptQty);
                        insertCommand.Parameters.AddWithValue("@TotalStock", receiptQty);
                        insertCommand.Parameters.AddWithValue("@WeightedAverageCost", landingRate);
                        insertCommand.Parameters.AddWithValue("@CompanyID", Company_ID);
                        insertCommand.Parameters.AddWithValue("@BranchID", branchID);

                        insertCommand.ExecuteNonQuery();

                    }
                    conn.Close();
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return returnWAC;
        }
        #endregion  

        public static void StockLedgerInsert(string constring, int LogException, int Company_ID, int Branch, bool transactionType, int objectID, string transactionNumber, int partsID, decimal? quantity, int warehouseID, int financialYear, decimal transactionRate = 0.00M, int companyID = 0, int branchID = 0)
        {
            try
            {
                if (companyID == 0)
                {
                    companyID = Convert.ToInt32(Company_ID);
                }
                if (branchID == 0)
                {
                    branchID = Convert.ToInt32(Branch);
                }

                decimal openingStock = 0;
                decimal openingWAC = 0;
                decimal closingStock = 0;
                decimal closingWAC = 0;

                // Fetch the stock details
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Retrieve PartsStock details
                    using (SqlCommand command = new SqlCommand(
                        "SELECT BinStock, WeightedAverageCost FROM GNM_PartsStockDetail WHERE Parts_ID = @PartsID AND Company_ID = @CompanyID AND Branch_ID = @BranchID AND WareHouse_ID = @WarehouseID", connection))
                    {
                        command.Parameters.AddWithValue("@PartsID", partsID);
                        command.Parameters.AddWithValue("@CompanyID", companyID);
                        command.Parameters.AddWithValue("@BranchID", branchID);
                        command.Parameters.AddWithValue("@WarehouseID", warehouseID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                closingStock = reader.GetDecimal(reader.GetOrdinal("BinStock"));
                                closingWAC = reader.GetDecimal(reader.GetOrdinal("WeightedAverageCost"));
                            }
                        }
                    }

                    // Retrieve the last transaction for opening balance
                    using (SqlCommand command = new SqlCommand(
                        "SELECT TOP 1 Closing_Stock, Closing_WAC FROM PRT_StockLedger WHERE Warehouse_ID = @WarehouseID AND Parts_ID = @PartsID ORDER BY Transaction_Date DESC", connection))
                    {
                        command.Parameters.AddWithValue("@WarehouseID", warehouseID);
                        command.Parameters.AddWithValue("@PartsID", partsID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                openingStock = reader.GetDecimal(reader.GetOrdinal("Closing_Stock"));
                                openingWAC = reader.GetDecimal(reader.GetOrdinal("Closing_WAC"));
                            }
                        }
                    }

                    // Insert into PRT_StockLedger
                    using (SqlCommand command = new SqlCommand(
                        "INSERT INTO PRT_StockLedger (Company_ID, FinancialYear, Object_ID, Parts_ID, Transaction_Code, Transaction_Date, Transaction_Quantity, Transaction_Type, Warehouse_ID, Opening_Stock, Opening_WAC, Closing_Stock, Closing_WAC, Transaction_Rate) " +
                        "VALUES (@CompanyID, @FinancialYear, @ObjectID, @PartsID, @TransactionCode, @TransactionDate, @TransactionQuantity, @TransactionType, @WarehouseID, @OpeningStock, @OpeningWAC, @ClosingStock, @ClosingWAC, @TransactionRate)", connection))
                    {
                        command.Parameters.AddWithValue("@CompanyID", companyID);
                        command.Parameters.AddWithValue("@FinancialYear", financialYear);
                        command.Parameters.AddWithValue("@ObjectID", objectID);
                        command.Parameters.AddWithValue("@PartsID", partsID);
                        command.Parameters.AddWithValue("@TransactionCode", transactionNumber);
                        command.Parameters.AddWithValue("@TransactionDate", DateTime.Now);
                        command.Parameters.AddWithValue("@TransactionQuantity", quantity.HasValue ? (object)quantity.Value : DBNull.Value);
                        command.Parameters.AddWithValue("@TransactionType", transactionType);
                        command.Parameters.AddWithValue("@WarehouseID", warehouseID);
                        command.Parameters.AddWithValue("@OpeningStock", openingStock);
                        command.Parameters.AddWithValue("@OpeningWAC", openingWAC);
                        command.Parameters.AddWithValue("@ClosingStock", closingStock);
                        command.Parameters.AddWithValue("@ClosingWAC", closingWAC);
                        command.Parameters.AddWithValue("@TransactionRate", transactionType ? closingWAC : transactionRate);

                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
        }
        public static void UpdatePartsStockReturn(string constring, int LogException, int Company_ID, int Branch, int partID, decimal returnInvoicedQty, int purchaseOrderID, int wareHouseID)
        {
            try
            {
                int companyID = Convert.ToInt32(Company_ID);
                int branchID = Convert.ToInt32(Branch);


                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Update Parts Stock
                    string updatePartsStockQuery = @"
         UPDATE GNM_PartsStockDetail
         SET TotalStock = ISNULL(TotalStock, 0) - @ReturnInvoicedQty,
             GITQuantity = ISNULL(GITQuantity, 0) - @ReturnInvoicedQty,
             PendingPurchaseOrderQuantity = CASE 
                 WHEN @PurchaseOrderID != 0 THEN ISNULL(PendingPurchaseOrderQuantity, 0) + @ReturnInvoicedQty 
                 ELSE PendingPurchaseOrderQuantity 
             END
         WHERE Parts_ID = @PartID AND WareHouse_ID = @WareHouseID";

                    using (SqlCommand cmd = new SqlCommand(updatePartsStockQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@PartID", partID);
                        cmd.Parameters.AddWithValue("@ReturnInvoicedQty", returnInvoicedQty);
                        cmd.Parameters.AddWithValue("@PurchaseOrderID", purchaseOrderID);
                        cmd.Parameters.AddWithValue("@WareHouseID", wareHouseID);
                        cmd.ExecuteNonQuery();
                    }

                    if (purchaseOrderID != 0)
                    {
                        // Check if Purchase Order Type is not Back Order
                        string checkPurchaseOrderQuery = @"
             SELECT COUNT(*)
             FROM PRT_PurchaseOrderPartsDetail
             WHERE PurchaseOrder_ID = @PurchaseOrderID AND Parts_ID = @PartID";

                        int count;
                        using (SqlCommand cmd = new SqlCommand(checkPurchaseOrderQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@PurchaseOrderID", purchaseOrderID);
                            cmd.Parameters.AddWithValue("@PartID", partID);
                            count = (int)cmd.ExecuteScalar();
                        }

                        if (count == 1)
                        {
                            // Update Purchase Order Parts Detail
                            string updatePurchaseOrderQuery = @"
                 UPDATE PRT_PurchaseOrderPartsDetail
                 SET InvoicedQuantity = ISNULL(InvoicedQuantity, 0) - @ReturnInvoicedQty
                 WHERE PurchaseOrder_ID = @PurchaseOrderID AND Parts_ID = @PartID";

                            using (SqlCommand cmd = new SqlCommand(updatePurchaseOrderQuery, conn))
                            {
                                cmd.Parameters.AddWithValue("@PurchaseOrderID", purchaseOrderID);
                                cmd.Parameters.AddWithValue("@PartID", partID);
                                cmd.Parameters.AddWithValue("@ReturnInvoicedQty", returnInvoicedQty);
                                cmd.ExecuteNonQuery();
                            }
                        }
                        else
                        {
                            // Handle Back Order logic
                            string allocationQuery = @"SELECT DISTINCT a.PartsOrder_ID
                 FROM PRT_PartsOrder a
                 INNER JOIN PRT_PurchaseOrderPartsDetail b ON a.PartsOrder_ID = b.PartsOrder_ID
                 INNER JOIN PRM_AllocationPriority c ON b.PartsOrderType_ID = c.OrderType_ID
                 WHERE b.PurchaseOrder_ID = @PurchaseOrderID
                 AND b.CustomerOrderClass_ID = c.OrderClass_ID
                 ORDER BY c.AllocationPriority_Priority ASC";

                            List<int> partsOrderIDs = new List<int>();

                            using (SqlCommand cmd = new SqlCommand(allocationQuery, conn))
                            {
                                cmd.Parameters.AddWithValue("@PurchaseOrderID", purchaseOrderID);
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        partsOrderIDs.Add(reader.GetInt32(0));
                                    }
                                }
                            }

                            foreach (int partsOrderID in partsOrderIDs)
                            {
                                string updateBackOrderQuery = @"
                     UPDATE PRT_PurchaseOrderPartsDetail
                     SET InvoicedQuantity = CASE
                         WHEN @ReturnInvoicedQty > ISNULL(InvoicedQuantity, 0) THEN 0
                         ELSE ISNULL(InvoicedQuantity, 0) - @ReturnInvoicedQty
                     END
                     WHERE PurchaseOrder_ID = @PurchaseOrderID AND Parts_ID = @PartID AND PartsOrder_ID = @PartsOrderID";

                                using (SqlCommand cmd = new SqlCommand(updateBackOrderQuery, conn))
                                {
                                    cmd.Parameters.AddWithValue("@PurchaseOrderID", purchaseOrderID);
                                    cmd.Parameters.AddWithValue("@PartID", partID);
                                    cmd.Parameters.AddWithValue("@PartsOrderID", partsOrderID);
                                    cmd.Parameters.AddWithValue("@ReturnInvoicedQty", returnInvoicedQty);
                                    cmd.ExecuteNonQuery();
                                }
                            }
                        }

                        // Update Purchase Order Status
                        string updatePOStatusQuery = @"
             UPDATE PRT_PurchaseOrder
             SET POStatus_ID = CASE
                 WHEN @PendingQty <= 0 THEN 
                     (SELECT RefMasterDetail_ID FROM GNM_RefMasterDetail WHERE RefMaster_Name = 'POSTATUS' AND RefMasterDetail_Short_Name = 'Closed')
                 ELSE 
                     (SELECT RefMasterDetail_ID FROM GNM_RefMasterDetail WHERE RefMaster_Name = 'POSTATUS' AND RefMasterDetail_Short_Name = 'Partial')
             END
             WHERE PurchaseOrder_ID = @PurchaseOrderID";

                        decimal pendingQty = 0;
                        string calculatePendingQtyQuery = @"
             SELECT SUM(ISNULL(ApprovedQuantity, 0) - ISNULL(InvoicedQuantity, 0) - ISNULL(BackOrderCancelledQuantity, 0))
             FROM PRT_PurchaseOrderPartsDetail
             WHERE PurchaseOrder_ID = @PurchaseOrderID";

                        using (SqlCommand cmd = new SqlCommand(calculatePendingQtyQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@PurchaseOrderID", purchaseOrderID);
                            object result = cmd.ExecuteScalar();
                            pendingQty = result != DBNull.Value ? Convert.ToDecimal(result) : 0;
                        }

                        using (SqlCommand cmd = new SqlCommand(updatePOStatusQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@PendingQty", pendingQty);
                            cmd.Parameters.AddWithValue("@PurchaseOrderID", purchaseOrderID);
                            cmd.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
        }
        public static void UpdatePendingPartsOrderQty(int PartsID, decimal Quantity, int WareHouseID, int CompanyID, int BranchID, string connectionString)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    // Check if the Partsstock exists in the database
                    string selectPartsStockQuery = "SELECT TOP 1 * FROM GNM_PartsStockDetail WHERE Parts_ID = @Parts_ID AND Company_ID = @Company_ID AND Branch_ID = @Branch_ID AND WareHouse_ID = @WareHouse_ID";

                    using (SqlCommand cmd = new SqlCommand(selectPartsStockQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@Parts_ID", PartsID);
                        cmd.Parameters.AddWithValue("@Company_ID", CompanyID);
                        cmd.Parameters.AddWithValue("@Branch_ID", BranchID);
                        cmd.Parameters.AddWithValue("@WareHouse_ID", WareHouseID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                // Partsstock exists, update the PendingPartsOrderQuantity
                                decimal currentPendingQty = reader["PendingPartsOrderQuantity"] != DBNull.Value ? (decimal)reader["PendingPartsOrderQuantity"] : 0;
                                decimal newPendingQty = currentPendingQty + Quantity;

                                string updatePartsStockQuery = "UPDATE GNM_PartsStockDetail SET PendingPartsOrderQuantity = @PendingPartsOrderQuantity WHERE Parts_ID = @Parts_ID AND Company_ID = @Company_ID AND Branch_ID = @Branch_ID AND WareHouse_ID = @WareHouse_ID";

                                using (SqlCommand updateCmd = new SqlCommand(updatePartsStockQuery, conn))
                                {
                                    updateCmd.Parameters.AddWithValue("@PendingPartsOrderQuantity", newPendingQty);
                                    updateCmd.Parameters.AddWithValue("@Parts_ID", PartsID);
                                    updateCmd.Parameters.AddWithValue("@Company_ID", CompanyID);
                                    updateCmd.Parameters.AddWithValue("@Branch_ID", BranchID);
                                    updateCmd.Parameters.AddWithValue("@WareHouse_ID", WareHouseID);
                                    updateCmd.ExecuteNonQuery();
                                }
                            }
                            else
                            {
                                // Partsstock doesn't exist, insert a new record
                                string selectBinLocationQuery = "SELECT TOP 1 BinLocation_ID FROM GNM_BinLocation WHERE Branch_ID = @Branch_ID AND WareHouse_ID = @WareHouse_ID AND BinLocation_IsActive = 1 AND BinLocation_IsDefault = 1";

                                int binID = 0;
                                using (SqlCommand binCmd = new SqlCommand(selectBinLocationQuery, conn))
                                {
                                    binCmd.Parameters.AddWithValue("@Branch_ID", BranchID);
                                    binCmd.Parameters.AddWithValue("@WareHouse_ID", WareHouseID);
                                    object binResult = binCmd.ExecuteScalar();
                                    binID = binResult != DBNull.Value ? Convert.ToInt32(binResult) : 0;
                                }

                                string insertPartsStockQuery = "INSERT INTO GNM_PartsStockDetail (Parts_ID, BinLocation_ID, WareHouse_ID, BinStock, FreeStock, TotalStock, WeightedAverageCost, Company_ID, Branch_ID, PendingPartsOrderQuantity) " +
                                    "VALUES (@Parts_ID, @BinLocation_ID, @WareHouse_ID, 0, 0, 0, 0, @Company_ID, @Branch_ID, @PendingPartsOrderQuantity)";

                                using (SqlCommand insertCmd = new SqlCommand(insertPartsStockQuery, conn))
                                {
                                    insertCmd.Parameters.AddWithValue("@Parts_ID", PartsID);
                                    insertCmd.Parameters.AddWithValue("@BinLocation_ID", binID);
                                    insertCmd.Parameters.AddWithValue("@WareHouse_ID", WareHouseID);
                                    insertCmd.Parameters.AddWithValue("@Company_ID", CompanyID);
                                    insertCmd.Parameters.AddWithValue("@Branch_ID", BranchID);
                                    insertCmd.Parameters.AddWithValue("@PendingPartsOrderQuantity", Quantity);
                                    insertCmd.ExecuteNonQuery();
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

    }
    public class UpdatePartsStockGRNList
    {
        public int Company_ID { get; set; }
        public int Branch {  get; set; }
    }
    public partial class PRM_AllocationPriority
    {
        public int AllocationPriority_ID { get; set; }
        public int Company_ID { get; set; }
        public int OrderType_ID { get; set; }
        public int OrderClass_ID { get; set; }
        public int AllocationPriority_Priority { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }
    }
    public class WAC
    {
        public int partID { get; set; }
        public int wareHouseID { get; set; }
        public decimal ClosingStock { get; set; }
        public decimal ClosingWAC { get; set; }
    }
    public partial class PRT_PartsOrderAllocation
    {
        public int PartsOrderAllocation_ID { get; set; }
        public int PartsOrderPartsDetail_ID { get; set; }
        public Nullable<int> WareHouse_ID { get; set; }
        public Nullable<decimal> AllocatedQuantity { get; set; }
        public Nullable<decimal> PickedQuantity { get; set; }
        public Nullable<decimal> InvoicedQuantity { get; set; }
        public Nullable<decimal> BackOrderQuantity { get; set; }
        public Nullable<decimal> NullPickQuantity { get; set; }
        public Nullable<decimal> CanceledQuantity { get; set; }
        public Nullable<decimal> IssuedQuantity { get; set; }
        public Nullable<decimal> ReturnedQuantity { get; set; }
        public int PartsOrder_ID { get; set; }
        public int Parts_ID { get; set; }
        public string Remarks { get; set; }

        public virtual PRT_PartsOrderPartsDetails PRT_PartsOrderPartsDetails { get; set; }
        public virtual PRT_PartsOrder PRT_PartsOrder { get; set; }
    }
    public partial class PRT_PartsOrderPartsDetails
    {
        public PRT_PartsOrderPartsDetails()
        {
            this.PRT_PartsOrderAllocation = new HashSet<PRT_PartsOrderAllocation>();
        }

        public int PartsOrderPartsDetail_ID { get; set; }
        public int PartsOrder_ID { get; set; }
        public int Parts_ID { get; set; }
        public decimal? FreeStock { get; set; }
        public decimal OrderQuantity { get; set; }
        public decimal Rate { get; set; }
        public Nullable<decimal> AcceptedQuantity { get; set; }
 
        public Nullable<decimal> DiscountPercentage { get; set; }
        public Nullable<decimal> DiscountAmount { get; set; }
        public Nullable<int> TaxStructure_ID { get; set; }
        public Nullable<decimal> TaxAmount { get; set; }
        public Nullable<decimal> Amount { get; set; }
        public decimal MRP { get; set; }

        public virtual ICollection<PRT_PartsOrderAllocation> PRT_PartsOrderAllocation { get; set; }
        public virtual PRT_PartsOrder PRT_PartsOrder { get; set; }
    }
    public partial class PRT_PartsOrder
    {
        public PRT_PartsOrder()
        {
            this.PRT_PartsOrderAllocation = new HashSet<PRT_PartsOrderAllocation>();
            this.PRT_PartsOrderPartsDetails = new HashSet<PRT_PartsOrderPartsDetails>();
        }

        public int PartsOrder_ID { get; set; }
        public string PartsOrderNumber { get; set; }
        public System.DateTime PartsOrderDate { get; set; }
        public int PartsOrderType_ID { get; set; }
        public Nullable<int> ServiceRequest_ID { get; set; }
        public Nullable<int> PartsQuotation_ID { get; set; }
        public int CustomerOrderClass_ID { get; set; }
        public Nullable<int> Party_ID { get; set; }
        public Nullable<int> OrderBranch_ID { get; set; }
        public string PartyReferenceDetail { get; set; }
        public Nullable<int> ConsigneeAddress_ID { get; set; }
        public Nullable<int> InvoiceAddress_ID { get; set; }
        public string PaymentTerms { get; set; }
        public Nullable<bool> EnableVersion { get; set; }
        public Nullable<bool> IsArchived { get; set; }
        public Nullable<decimal> TotalAmount { get; set; }
        public int PartsOrderStatus_ID { get; set; }
        public Nullable<int> PurchaseOrder_ID { get; set; }
        public Nullable<bool> IsBackToBackOrderAllocation { get; set; }
        public Nullable<decimal> DiscountPercentage { get; set; }
        public Nullable<decimal> DiscountAmount { get; set; }
        public Nullable<decimal> DiscountedAmount { get; set; }
        public Nullable<int> TaxStructure_ID { get; set; }
        public Nullable<decimal> TotalTaxableAmount { get; set; }
        public Nullable<decimal> TaxAmount { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public Nullable<int> FinancialYear { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public Nullable<int> DocumentNumber { get; set; }
        public Nullable<int> PartsOrderVersion { get; set; }
        public Nullable<int> JobCard_ID { get; set; }
        public string JobCardNumber { get; set; }
        public Nullable<int> StockTransferRequest_ID { get; set; }
        public string StockTransferRequestNumber { get; set; }
        public Nullable<int> OrderBranchConsignee_ID { get; set; }
        public Nullable<int> WareHouse_ID { get; set; }
        public string Remarks { get; set; }
        public Nullable<bool> IsDealer { get; set; }

        public virtual PRT_PartsOrder PRT_PartsOrder1 { get; set; }
        public virtual PRT_PartsOrder PRT_PartsOrder2 { get; set; }
        public virtual ICollection<PRT_PartsOrderAllocation> PRT_PartsOrderAllocation { get; set; }
        public virtual ICollection<PRT_PartsOrderPartsDetails> PRT_PartsOrderPartsDetails { get; set; }
    }
}
