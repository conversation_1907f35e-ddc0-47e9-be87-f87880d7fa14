using Microsoft.AspNetCore.Mvc;
using PBC.UtilityService.Services;
using PBC.UtilityService.Utilities.DTOs;
using System.ComponentModel.DataAnnotations;

namespace PBC.UtilityService.Controllers
{
    /// <summary>
    /// Controller for Help Desk Service Request API operations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class HelpDeskServiceRequestAPIController : ControllerBase
    {
        private readonly IHelpDeskServiceRequestAPIService _helpDeskServiceRequestAPIService;
        private readonly ILogger<HelpDeskServiceRequestAPIController> _logger;

        public HelpDeskServiceRequestAPIController(
            IHelpDeskServiceRequestAPIService helpDeskServiceRequestAPIService,
            ILogger<HelpDeskServiceRequestAPIController> logger)
        {
            _helpDeskServiceRequestAPIService = helpDeskServiceRequestAPIService;
            _logger = logger;
        }

        /// <summary>
        /// Gets the service request query for help desk operations
        /// </summary>
        /// <param name="request">Service request query request</param>
        /// <returns>SQL query string for service requests</returns>
        /// <response code="200">Returns the service request query</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("service-request-query")]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<string>> GetServiceRequestQuery([FromBody] GetServiceRequestQueryRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/helpdeskservicerequestapi/service-request-query - Getting service request query for mode: {Mode}, user: {UserId}", 
                    request.Mode, request.User_ID);
                
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _helpDeskServiceRequestAPIService.GetServiceRequestQueryAsync(
                    request.ConnectionString, 
                    request.LogException, 
                    request.LangID,
                    request.GenLangCode, 
                    request.UserLangCode, 
                    request.Mode, 
                    request.User_ID, 
                    request.Company_ID, 
                    request.Branch_ID, 
                    request.Sidx, 
                    request.Sord, 
                    request.DbName);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting service request query for mode: {Mode}, user: {UserId}", 
                    request.Mode, request.User_ID);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while generating service request query");
            }
        }
    }
}
