﻿using AMMSCore.Models;
using DocumentFormat.OpenXml.Drawing.Diagrams;
using DocumentFormat.OpenXml.Office.Word;
using DocumentFormat.OpenXml.Office2010.Excel;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Services;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Security.Cryptography.Pkcs;
using System.Threading.Tasks;
using System.Transactions;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class PRT_PurchaseInvoiceServices
    {
        #region ::: Save Vinay N:::
        /// <summary>
        /// Save
        /// Modified by DK
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult Save(SavePurchaseInvoiceList Obj, string connString, int LogException)
        {
            JObject jObj = JObject.Parse(Obj.Data);
            int companyId = Obj.Company_ID;
            int branchId = Obj.Branch;
            jObj["Company_ID"] = companyId;
            jObj["Branch_ID"] = branchId;
            JObject header = jObj;


            JArray details = (JArray)jObj["PRT_PurchaseInvoicePartsDetails"];
            var headerColumns = new Dictionary<string, bool>
            {
                { "Company_ID", true },
                { "Branch_ID", true },
                {"TotalPIAmount",true },

                { "Currency_ID", true },
                { "ExchangeRate", true },
                { "SupplierInvoiceNumber", true },
                { "SupplierInvoiceDate", true },
                { "Remarks", true },
                { "TotalAmount", true },
                { "TaxStructure_ID", true },
                { "TaxableOtherCharges", true },
                { "TaxablePercentage", true },
                { "TaxableOtherChargesAmount", true },
                { "TaxOnTaxableOtherCharges", true },
                { "TotalTaxableAmount", true },
                { "TaxAmount", false },
                { "NonTaxableOtherCharges", true },
                { "NonTaxablePercentage", true },
                { "NonTaxableOtherChargesAmount", true },
                { "TotalInvoiceAmount", true },
                { "WareHouse_ID", true },
                { "Roundoff", true }



            };
            List<string> invalidColumns;
            bool isHeaderValid = Common.ValidateAndLog(header, headerColumns, out invalidColumns);
            if (!isHeaderValid)
            {

                return new JsonResult(new { Response = "Header Validation Failed", Message = "Header contains invalid or missing fields." });
            }
            bool allRowsValid = true;
            foreach (var row in details)
            {
                var detailColumns = new Dictionary<string, bool>
                    {

                        { "PurchaseOrder_ID", true },

                        { "DiscountPercentage", true },
                        { "DiscountAmount", true },
                        { "TaxStructure_ID", true },
                        { "TaxAmount", true },
                        { "Amount", false },
                        { "DiscountedAmount", true }
                    };
                List<string> invalidColumns2;
                bool isRowValid = Common.ValidateAndLog(row.ToObject<JObject>(), detailColumns, out invalidColumns2);

                if (!isRowValid)
                {
                    allRowsValid = false;

                }
            }
            if (!allRowsValid)
            {

                return new JsonResult(new { Response = "Detailer Validation Failed", Message = "Detailer contains invalid or missing fields." });
            }

            var jsonData = default(dynamic);
            var js2 = default(dynamic);


            try
            {


                List<dynamic> Js = new List<dynamic>();
                Js.Add(Obj.UserDetails);//0
                Js.Add(Obj.Branch);//1
                Js.Add(Obj.PurchaseInvoicePartsDelete);//2
                Js.Add(Obj.Data);//3
                Js.Add(Obj.LoggedINDateTime);//4
                Js.Add(Convert.ToInt32(Obj.MenuID));//5
                Save_BLPartsInvoiceList objList = new Save_BLPartsInvoiceList()
                {
                    Company_ID = Obj.Company_ID,
                    Branch = Obj.Branch,
                    User_ID = Obj.User_ID

                };


                js2 = PartsCommon.Save_BL(objList, connString, LogException, Js);
                jsonData = js2.Value;
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                jsonData = new
                {
                    IsPSExists = true,
                    IsSuccess = false,
                    PurchaseInvoice_ID = 0,
                    PurchaseInvoiceNumber = ""
                };
            }
            // }
            return new JsonResult(jsonData);
        }
        #endregion
        #region ::: CreateAutoGRN :::
        /// <summary>
        /// Save Vinay N
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        public static void CreateAutoGRN(CreateAutoGRNList Obj, string connString, int LogException)
        {
            try
            {

                int companyID = Obj.Company_ID;
                int userid = Obj.User_ID;
                int branchID = Convert.ToInt32(Obj.Branch);
                int newGRNId = 0;




                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string purchaseInvoiceQuery = @"
                    SELECT 
                        PurchaseInvoice_ID, Branch_ID, Company_ID, FinancialYear, Remarks, Supplier_ID, 
                        Updated_By, Updated_Date, SupplierInvoiceNumber,WareHouse_ID
                    FROM PRT_PurchaseInvoice
                    WHERE PurchaseInvoice_ID = @PurchaseInvoiceID";


                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(purchaseInvoiceQuery, conn))
                        {
                            command.CommandType = CommandType.Text;
                            command.Parameters.AddWithValue("@PurchaseInvoiceID", Obj.purchaseInvoiceID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    int branchId = Convert.ToInt32(reader["Branch_ID"]);
                                    int companyId = Convert.ToInt32(reader["Company_ID"]);
                                    int financialYear = Convert.ToInt32(reader["FinancialYear"]);
                                    string remarks = reader["Remarks"].ToString();
                                    int supplierId = Convert.ToInt32(reader["Supplier_ID"]);
                                    int updatedBy = Convert.ToInt32(reader["Updated_By"]);
                                    DateTime updatedDate = Convert.ToDateTime(reader["Updated_Date"]);
                                    string supplierInvoiceNumber = reader["SupplierInvoiceNumber"].ToString();
                                    int? WareHouse_ID = reader["WareHouse_ID"] != DBNull.Value ? Convert.ToInt32(reader["WareHouse_ID"]) : (int?)null;
                                    WareHouse_ID = WareHouse_ID ?? 0;
                                    string insertGRNQuery = @"
                          DECLARE @InsertedGRN TABLE (PurchaseGRN_ID INT);
                            INSERT INTO PRT_PurchaseGRN 
                            (Branch_ID, Company_ID, FinancialYear, PurchaseGRNDate, Remarks, Supplier_ID, Updated_By, Updated_Date, PurchaseGRNNumber,WareHouse_ID,GDRSettlement_ID)
                            OUTPUT INSERTED.PurchaseGRN_ID INTO @InsertedGRN
                            VALUES 
                            (@BranchID, @CompanyID, @FinancialYear, @PurchaseGRNDate, @Remarks, @SupplierID, @UpdatedBy, @UpdatedDate, @PurchaseGRNNumber,@WareHouse_ID,@GDRSettlement_ID)
                            SELECT PurchaseGRN_ID FROM @InsertedGRN";

                                    using (command = new SqlCommand(insertGRNQuery, conn))
                                    {
                                        command.Parameters.AddWithValue("@BranchID", branchId);
                                        command.Parameters.AddWithValue("@CompanyID", companyId);
                                        command.Parameters.AddWithValue("@FinancialYear", financialYear);
                                        command.Parameters.AddWithValue("@PurchaseGRNDate", DateTime.Now);
                                        command.Parameters.AddWithValue("@Remarks", remarks);
                                        command.Parameters.AddWithValue("@SupplierID", supplierId);
                                        command.Parameters.AddWithValue("@UpdatedBy", updatedBy);
                                        command.Parameters.AddWithValue("@UpdatedDate", updatedDate);
                                        command.Parameters.AddWithValue("@PurchaseGRNNumber", "");
                                        command.Parameters.AddWithValue("@WareHouse_ID", WareHouse_ID ?? (object)DBNull.Value);
                                        command.Parameters.AddWithValue("@GDRSettlement_ID", 0);


                                        // Execute the update command
                                        newGRNId = Convert.ToInt32(command.ExecuteScalar());
                                    }
                                    string partsDetailsQuery = @"
                            SELECT 
                                Quantity, LandingCostInLocalCurrency, Parts_ID, PartsOrder_ID, PurchaseOrder_ID, WareHouse_ID
                            FROM PRT_PurchaseInvoicePartsDetails
                            WHERE PurchaseInvoice_ID = @PurchaseInvoiceID";
                                    using (command = new SqlCommand(partsDetailsQuery, conn))
                                    {
                                        command.Parameters.AddWithValue("@PurchaseInvoiceID", Obj.purchaseInvoiceID);

                                        // Execute the update command
                                        using (SqlDataReader partsReader = command.ExecuteReader())
                                        {
                                            while (partsReader.Read())
                                            {
                                                decimal quantity = Convert.ToDecimal(partsReader["Quantity"]);
                                                decimal rate = Convert.ToDecimal(partsReader["LandingCostInLocalCurrency"]);
                                                int partsId = Convert.ToInt32(partsReader["Parts_ID"]);
                                                int? partsOrderId = partsReader["PartsOrder_ID"] as int?;
                                                int? purchaseOrderId = partsReader["PurchaseOrder_ID"] as int?;
                                                int wareHouseId = Convert.ToInt32(partsReader["WareHouse_ID"]);
                                                string binLocationQuery = @"
                                                SELECT BinLocation_ID
                                                FROM GNM_BinLocation
                                                WHERE Branch_ID = @BranchID AND Company_ID = @CompanyID AND BinLocation_IsDefault = 1 AND BinLocation_IsActive = 1 AND WareHouse_ID = @WareHouseID";
                                                SqlCommand cmd = new SqlCommand(binLocationQuery, conn);
                                                cmd.Parameters.AddWithValue("@BranchID", branchID);
                                                cmd.Parameters.AddWithValue("@CompanyID", companyID);
                                                cmd.Parameters.AddWithValue("@WareHouseID", wareHouseId);
                                                int binLocationId = Convert.ToInt32(cmd.ExecuteScalar());

                                                string insertPartDetailQuery = @"
                                            INSERT INTO PRT_PurchaseGRNPartDetails 
                                            (PurchaseGRN_ID, InvoicedQuantity, PartsRate, LandingRate, Parts_ID, PartsOrder_ID, BinLocation_ID, 
                                             PurchaseInvoice_ID, PurchaseOrder_ID, ReceivedQuantity, SupplierInvoiceNumber, LandingCost, WareHouse_ID,Closing_WAC,Closing_Stock_Balance,MRP)
                                            VALUES 
                                            (@PurchaseGRNID, @InvoicedQuantity, @PartsRate, @LandingRate, @PartsID, @PartsOrderID, @BinLocationID, 
                                             @PurchaseInvoiceID, @PurchaseOrderID, @ReceivedQuantity, @SupplierInvoiceNumber, @LandingCost, @WareHouseID,@ClosingWAC,@Closing_Stock_Balance,@MRP)";

                                                SqlCommand insertPartDetailCommand = new SqlCommand(insertPartDetailQuery, conn);
                                                insertPartDetailCommand.Parameters.AddWithValue("@PurchaseGRNID", newGRNId);
                                                insertPartDetailCommand.Parameters.AddWithValue("@InvoicedQuantity", quantity);
                                                insertPartDetailCommand.Parameters.AddWithValue("@PartsRate", rate);
                                                insertPartDetailCommand.Parameters.AddWithValue("@LandingRate", rate);
                                                insertPartDetailCommand.Parameters.AddWithValue("@PartsID", partsId);
                                                insertPartDetailCommand.Parameters.AddWithValue("@PartsOrderID", (object)partsOrderId ?? DBNull.Value);
                                                insertPartDetailCommand.Parameters.AddWithValue("@BinLocationID", binLocationId);
                                                insertPartDetailCommand.Parameters.AddWithValue("@PurchaseInvoiceID", Obj.purchaseInvoiceID);
                                                insertPartDetailCommand.Parameters.AddWithValue("@PurchaseOrderID", (object)purchaseOrderId ?? DBNull.Value);
                                                insertPartDetailCommand.Parameters.AddWithValue("@ReceivedQuantity", quantity);
                                                insertPartDetailCommand.Parameters.AddWithValue("@SupplierInvoiceNumber", supplierInvoiceNumber);
                                                insertPartDetailCommand.Parameters.AddWithValue("@LandingCost", quantity * rate);
                                                insertPartDetailCommand.Parameters.AddWithValue("@WareHouseID", wareHouseId);
                                                insertPartDetailCommand.Parameters.AddWithValue("@ClosingWAC", 0.0);
                                                insertPartDetailCommand.Parameters.AddWithValue("@Closing_Stock_Balance", 0.0);
                                                insertPartDetailCommand.Parameters.AddWithValue("@MRP", 0.0);
                                                insertPartDetailCommand.ExecuteNonQuery();


                                            }
                                        }
                                    }
                                }
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {

                    }
                }



                PRT_PurchaseGRN pgrn = new PRT_PurchaseGRN();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = @"
                        SELECT PurchaseGRN_ID, PurchaseGRNNumber, PurchaseGRNDate, Supplier_ID, WareHouse_ID, GDRSettlement_ID,
                               Remarks, DocumentNumber, FinancialYear, Company_ID, Branch_ID, Updated_By, Updated_Date, AttachmentCount, IsDealer
                        FROM PRT_PurchaseGRN
                        WHERE PurchaseGRN_ID = @PurchaseGRN_ID";


                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.Text;
                            command.Parameters.AddWithValue("@PurchaseGRN_ID", newGRNId);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    pgrn = new PRT_PurchaseGRN
                                    {
                                        PurchaseGRN_ID = reader.GetInt32(reader.GetOrdinal("PurchaseGRN_ID")),
                                        PurchaseGRNNumber = reader.IsDBNull(reader.GetOrdinal("PurchaseGRNNumber")) ? null : reader.GetString(reader.GetOrdinal("PurchaseGRNNumber")),
                                        PurchaseGRNDate = reader.GetDateTime(reader.GetOrdinal("PurchaseGRNDate")),
                                        Supplier_ID = reader.GetInt32(reader.GetOrdinal("Supplier_ID")),
                                        WareHouse_ID = reader.GetInt32(reader.GetOrdinal("WareHouse_ID")),
                                        GDRSettlement_ID = reader.GetInt32(reader.GetOrdinal("GDRSettlement_ID")),
                                        Remarks = reader.IsDBNull(reader.GetOrdinal("Remarks")) ? null : reader.GetString(reader.GetOrdinal("Remarks")),
                                        DocumentNumber = reader.IsDBNull(reader.GetOrdinal("DocumentNumber")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("DocumentNumber")),
                                        FinancialYear = reader.GetInt32(reader.GetOrdinal("FinancialYear")),
                                        Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                        Branch_ID = reader.GetInt32(reader.GetOrdinal("Branch_ID")),
                                        Updated_By = reader.GetInt32(reader.GetOrdinal("Updated_By")),
                                        Updated_Date = reader.GetDateTime(reader.GetOrdinal("Updated_Date")),
                                        AttachmentCount = reader.IsDBNull(reader.GetOrdinal("AttachmentCount")) ? (byte?)null : reader.GetByte(reader.GetOrdinal("AttachmentCount")),
                                        IsDealer = reader.IsDBNull(reader.GetOrdinal("IsDealer")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsDealer"))
                                    };
                                }
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {

                    }
                }
                //WAC CALCULATION AND BACK ORDER ALLOCATION
                string allocationEnabled = string.Empty;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = @"
                        SELECT TOP 1 Param_value
                        FROM GNM_CompParam
                        WHERE Company_ID = @Company_ID
                        AND UPPER(Param_Name) = @Param_Name";


                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.Text;
                            command.Parameters.AddWithValue("@Company_ID", companyID);
                            command.Parameters.AddWithValue("@Param_Name", "ALLOCATION_ENABLED");

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            allocationEnabled = command.ExecuteScalar() as string;

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {

                    }
                }
                List<PRT_PurchaseGRNPartDetails> newlyInsertedPartsList = new List<PRT_PurchaseGRNPartDetails>();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = @"
                    SELECT PurchaseGRNPartsDetail_ID, PurchaseGRN_ID, PurchaseInvoice_ID, PurchaseOrder_ID, 
                           PartsOrder_ID, Parts_ID, SupplierInvoiceNumber, InvoicedQuantity, ReceivedQuantity, 
                           ShortQuantity, ExcessQuantity, DamagedQuantity, PartsRate, LandingRate, LandingCost, 
                           Remarks, IsGDRClosed, BinLocation_ID, Closing_WAC, Closing_Stock_Balance, WareHouse_ID, 
                           MRP
                    FROM PRT_PurchaseGRNPartDetails
                    WHERE PurchaseGRN_ID = @PurchaseGRN_ID";


                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.Text;
                            command.Parameters.AddWithValue("@PurchaseGRN_ID", newGRNId);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    PRT_PurchaseGRNPartDetails partDetail = new PRT_PurchaseGRNPartDetails
                                    {
                                        PurchaseGRNPartsDetail_ID = reader.GetInt32(reader.GetOrdinal("PurchaseGRNPartsDetail_ID")),
                                        PurchaseGRN_ID = reader.GetInt32(reader.GetOrdinal("PurchaseGRN_ID")),
                                        PurchaseInvoice_ID = reader.GetInt32(reader.GetOrdinal("PurchaseInvoice_ID")),
                                        PurchaseOrder_ID = reader.IsDBNull(reader.GetOrdinal("PurchaseOrder_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("PurchaseOrder_ID")),
                                        PartsOrder_ID = reader.IsDBNull(reader.GetOrdinal("PartsOrder_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("PartsOrder_ID")),
                                        Parts_ID = reader.GetInt32(reader.GetOrdinal("Parts_ID")),
                                        SupplierInvoiceNumber = reader.IsDBNull(reader.GetOrdinal("SupplierInvoiceNumber")) ? null : reader.GetString(reader.GetOrdinal("SupplierInvoiceNumber")),
                                        InvoicedQuantity = reader.GetDecimal(reader.GetOrdinal("InvoicedQuantity")),
                                        ReceivedQuantity = reader.GetDecimal(reader.GetOrdinal("ReceivedQuantity")),
                                        ShortQuantity = reader.IsDBNull(reader.GetOrdinal("ShortQuantity")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("ShortQuantity")),
                                        ExcessQuantity = reader.IsDBNull(reader.GetOrdinal("ExcessQuantity")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("ExcessQuantity")),
                                        DamagedQuantity = reader.IsDBNull(reader.GetOrdinal("DamagedQuantity")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("DamagedQuantity")),
                                        PartsRate = reader.GetDecimal(reader.GetOrdinal("PartsRate")),
                                        LandingRate = reader.GetDecimal(reader.GetOrdinal("LandingRate")),
                                        LandingCost = reader.GetDecimal(reader.GetOrdinal("LandingCost")),
                                        Remarks = reader.IsDBNull(reader.GetOrdinal("Remarks")) ? null : reader.GetString(reader.GetOrdinal("Remarks")),
                                        IsGDRClosed = reader.IsDBNull(reader.GetOrdinal("IsGDRClosed")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsGDRClosed")),
                                        BinLocation_ID = reader.GetInt32(reader.GetOrdinal("BinLocation_ID")),
                                        Closing_WAC = reader.GetDecimal(reader.GetOrdinal("Closing_WAC")),
                                        Closing_Stock_Balance = reader.GetDecimal(reader.GetOrdinal("Closing_Stock_Balance")),
                                        WareHouse_ID = reader.GetInt32(reader.GetOrdinal("WareHouse_ID")),
                                        MRP = reader.IsDBNull(reader.GetOrdinal("MRP")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("MRP"))
                                    };

                                    newlyInsertedPartsList.Add(partDetail);
                                }
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {

                    }
                }
                for (int i = 0; i < newlyInsertedPartsList.Count; i++)
                {
                    //WAC CALCULATION
                    UpdatePartsStockGRNList objList = new UpdatePartsStockGRNList()
                    {
                        Company_ID = Obj.Company_ID,
                        Branch = Obj.Branch
                    };
                    WAC returnValues = AllocationLogicServices.UpdatePartsStockGRN(objList, connString, LogException, newlyInsertedPartsList[i].Parts_ID, newlyInsertedPartsList[i].WareHouse_ID, newlyInsertedPartsList[i].BinLocation_ID, newlyInsertedPartsList[i].ReceivedQuantity, newlyInsertedPartsList[i].LandingRate, (allocationEnabled == "T" ? true : false), true, false, 0, newlyInsertedPartsList[i].InvoicedQuantity); //wacObj.WAC_calculation(newlyInsertedPartsList[i].Parts_ID, newlyInsertedPartsList[i].WareHouse_ID, newlyInsertedPartsList[i].BinLocation_ID, newlyInsertedPartsList[i].ReceivedQuantity, newlyInsertedPartsList[i].LandingRate, (allocationEnabled == "T" ? true : false), true, false, damagedQty);

                    //BACK ORDER ALLOCATION
                    int partsOrderID = newlyInsertedPartsList[i].PartsOrder_ID != null ? newlyInsertedPartsList[i].PartsOrder_ID.Value : 0;
                    BackOrder_AllocationList objList2 = new BackOrder_AllocationList()
                    {
                        Company_ID = Obj.Company_ID,
                        Branch = Obj.Branch,
                    };
                    CalculationServices.BackOrder_Allocation(objList2, connString, LogException, newlyInsertedPartsList[i].Parts_ID, newlyInsertedPartsList[i].WareHouse_ID, partsOrderID);
                    AllocationLogicServices.StockLedgerInsert(connString, LogException, Obj.Company_ID, Obj.Branch, false, Convert.ToInt32(Common.GetObjectID("PRT_PurchaseGRN")), pgrn.PurchaseGRNNumber, newlyInsertedPartsList[i].Parts_ID, newlyInsertedPartsList[i].ReceivedQuantity, newlyInsertedPartsList[i].WareHouse_ID, pgrn.FinancialYear, newlyInsertedPartsList[i].LandingRate);

                    //GRN Parts Detail Updation
                    int GRNPartsDetailID = newlyInsertedPartsList[i].PurchaseGRNPartsDetail_ID;



                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string updatePartDetailsQuery = @"
                        UPDATE PRT_PurchaseGRNPartDetails
                        SET Closing_Stock_Balance = @ClosingStock, Closing_WAC = @ClosingWAC
                        WHERE PurchaseGRNPartsDetail_ID = @GRNPartsDetailID";


                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(updatePartDetailsQuery, conn))
                            {
                                command.CommandType = CommandType.Text;
                                command.Parameters.AddWithValue("@ClosingStock", returnValues.ClosingStock);
                                command.Parameters.AddWithValue("@ClosingWAC", returnValues.ClosingWAC);
                                command.Parameters.AddWithValue("@GRNPartsDetailID", GRNPartsDetailID);


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                command.ExecuteNonQuery();

                            }
                            string updateInvoiceQuery = @"
                        UPDATE PRT_PurchaseInvoice
                        SET PurchaseGRN_ID = @PurchaseGRN_ID
                        WHERE PurchaseInvoice_ID = @PurchaseInvoice_ID";
                            using (SqlCommand cmdInvoice = new SqlCommand(updateInvoiceQuery, conn))
                            {
                                cmdInvoice.Parameters.AddWithValue("@PurchaseGRN_ID", newGRNId);
                                cmdInvoice.Parameters.AddWithValue("@PurchaseInvoice_ID", Obj.purchaseInvoiceID);

                                cmdInvoice.ExecuteNonQuery();
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {

                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion


      
    }
    public class PurchaseInvoiceInterfaceList
    {
        public DateTime PurchaseInvoiceDate { get; set; }
        public string Party_Code { get; set; }
        public string Currency_Short_Name { get; set; }
        public decimal ExchangeRate { get; set; }
        public string SupplierInvoiceNumber { get; set; }
        public DateTime SupplierInvoiceDate { get; set; }
        public string ModeOfShipment_Short_Name { get; set; }
        public decimal TotalInvoiceAmountInLocalCurrency { get; set; }
        public decimal LandingCostFactor { get; set; }
        public string Remarks { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal DiscountPercentage { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal DiscountedAmount { get; set; }
        public string TaxStructure_Name { get; set; }
        public string TaxableOtherCharges { get; set; }
        public decimal TaxablePercentage { get; set; }
        public decimal TaxableOtherChargesAmount { get; set; }
        public decimal TaxOnTaxableOtherCharges { get; set; }
        public decimal TotalTaxableAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public string NonTaxableOtherCharges { get; set; }
        public decimal NonTaxablePercentage { get; set; }
        public decimal NonTaxableOtherChargesAmount { get; set; }
        public decimal TotalInvoiceAmount { get; set; }
        public int FinancialYear { get; set; }
        public string BRANCH_SHORTNAME { get; set; }
        public string User_LognID { get; set; }
        public DateTime Updated_Date { get; set; }
        public decimal Roundoff { get; set; }
        public decimal TotalPIAmount { get; set; }
        public string SAP_OrderNumber { get; set; }
        public string STATUS { get; set; }
        public bool IsImport { get; set; }
        public bool IsDealer { get; set; }
        public List<PurchaseInvoiceDetail> PurchaseOrderDetails { get; set; }

        public PurchaseInvoiceInterfaceList()
        {
            PurchaseOrderDetails = new List<PurchaseInvoiceDetail>();
            AssignDefaults();
        }
        public void AssignDefaults()
        {
            if (ModeOfShipment_Short_Name == null) ModeOfShipment_Short_Name = "1";
            if (BRANCH_SHORTNAME == null) BRANCH_SHORTNAME = "BNG";
            if (User_LognID == null) User_LognID = "Admin";
            PurchaseInvoiceDate = PurchaseInvoiceDate == default(DateTime) ? DateTime.Now : PurchaseInvoiceDate;
            Updated_Date = Updated_Date == default(DateTime) ? DateTime.Now : Updated_Date;
        }
    }
    public class PurchaseInvoiceDetail
    {
        public string PurchaseOrderNumber { get; set; }
        public string Parts_PartPrefix { get; set; }
        public string Parts_PartsNumber { get; set; }
        public decimal Rate { get; set; }
        public decimal Quantity { get; set; }
        public decimal DiscountPercentage { get; set; }
        public decimal DiscountAmount { get; set; }
        public string TaxStructure_Name { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal Amount { get; set; }
        public decimal LandingCostInLocalCurrency { get; set; }
        public decimal VarianceinRate { get; set; }
        public decimal DiscountedAmount { get; set; }
        public decimal MRP { get; set; }
        public PurchaseInvoiceDetail()
        {
            AssignDefaults();
        }
        public void AssignDefaults()
        {
            if (string.IsNullOrEmpty(Parts_PartPrefix)) Parts_PartPrefix = "OEM";

        }
    }

    public class SavePurchaseInvoiceList
    {
        public GNM_User UserDetails { get; set; }
        public int Branch { get; set; }
        public bool PurchaseInvoicePartsDelete { get; set; }
        public string Data { get; set; }
        public DateTime LoggedINDateTime { get; set; }
        public int MenuID { get; set; }
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
    }
    public class CreateAutoGRNList
    {
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public int Branch { get; set; }
        public int purchaseInvoiceID { get; set; }
    }
    public partial class PRT_PurchaseGRN
    {
        public PRT_PurchaseGRN()
        {
            this.PRT_PurchaseGRNExpensesDetail = new HashSet<PRT_PurchaseGRNExpensesDetail>();
            this.PRT_PurchaseGRNPartDetails = new HashSet<PRT_PurchaseGRNPartDetails>();
        }

        public int PurchaseGRN_ID { get; set; }
        public string PurchaseGRNNumber { get; set; }
        public System.DateTime PurchaseGRNDate { get; set; }
        public int Supplier_ID { get; set; }
        public int WareHouse_ID { get; set; }
        public int GDRSettlement_ID { get; set; }
        public string Remarks { get; set; }
        public Nullable<int> DocumentNumber { get; set; }
        public int FinancialYear { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public int Updated_By { get; set; }
        public System.DateTime Updated_Date { get; set; }
        public Nullable<byte> AttachmentCount { get; set; }
        public Nullable<bool> IsDealer { get; set; }

        public virtual ICollection<PRT_PurchaseGRNExpensesDetail> PRT_PurchaseGRNExpensesDetail { get; set; }
        public virtual ICollection<PRT_PurchaseGRNPartDetails> PRT_PurchaseGRNPartDetails { get; set; }
    }
    public partial class PRT_PurchaseGRNExpensesDetail
    {
        public int PurchaseGRNExpensesDetail_ID { get; set; }
        public int PurchaseGRN_ID { get; set; }
        public int PurchaseInvoice_ID { get; set; }
        public string CustomsDocumentReference { get; set; }
        public Nullable<decimal> CustomsDuty { get; set; }
        public Nullable<decimal> ClearanceCharges { get; set; }
        public Nullable<decimal> EntryTax { get; set; }
        public Nullable<decimal> Freight { get; set; }
        public Nullable<decimal> Insurance { get; set; }
        public Nullable<decimal> OthersCharges { get; set; }
        public Nullable<decimal> TotalExpenses { get; set; }

        public virtual PRT_PurchaseGRN PRT_PurchaseGRN { get; set; }
    }
    public partial class PRT_PurchaseGRNPartDetails
    {
        public int PurchaseGRNPartsDetail_ID { get; set; }
        public int PurchaseGRN_ID { get; set; }
        public int PurchaseInvoice_ID { get; set; }
        public Nullable<int> PurchaseOrder_ID { get; set; }
        public Nullable<int> PartsOrder_ID { get; set; }
        public int Parts_ID { get; set; }
        public string SupplierInvoiceNumber { get; set; }
        public decimal InvoicedQuantity { get; set; }
        public decimal ReceivedQuantity { get; set; }
        public Nullable<decimal> ShortQuantity { get; set; }
        public Nullable<decimal> ExcessQuantity { get; set; }
        public Nullable<decimal> DamagedQuantity { get; set; }
        public decimal PartsRate { get; set; }
        public decimal LandingRate { get; set; }
        public decimal LandingCost { get; set; }
        public string Remarks { get; set; }
        public Nullable<bool> IsGDRClosed { get; set; }
        public int BinLocation_ID { get; set; }
        public decimal Closing_WAC { get; set; }
        public decimal Closing_Stock_Balance { get; set; }
        public int WareHouse_ID { get; set; }
        public Nullable<decimal> MRP { get; set; }

        public virtual PRT_PurchaseGRN PRT_PurchaseGRN { get; set; }
    }
}
