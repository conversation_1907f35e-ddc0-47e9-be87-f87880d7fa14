﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace SharedAPIClassLibrary_AMERP
{
    public class HelpDesk_Rpt_CallSummaryServices
    {

        #region ::: GetCallOwners Uday Kumar J B 12-11-2024:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult GetCallOwners(GetCallOwnersList GetCallOwnersobj, string connString, int LogException)
        {
            var JsonResult = new List<object>();
            try
            {
                int userLanguageID = Convert.ToInt32(GetCallOwnersobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(GetCallOwnersobj.GeneralLanguageID);
                int companyID = Convert.ToInt32(GetCallOwnersobj.Company_ID);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    SqlCommand cmd = new SqlCommand();
                    cmd.Connection = conn;

                    // Determine which stored procedure to use based on the language ID comparison
                    if (userLanguageID == generalLanguageID)
                    {
                        cmd.CommandText = "SP_AMERP_HelpDesk_GetCallOwnersByDefaultLanguage";
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", companyID);
                    }
                    else
                    {
                        cmd.CommandText = "SP_AMERP_HelpDesk_GetCallOwnersByUserLanguage";
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", companyID);
                        cmd.Parameters.AddWithValue("@Language_ID", userLanguageID);
                    }

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            JsonResult.Add(new
                            {
                                ID = reader["ID"],
                                Name = reader["Name"]
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return new JsonResult(JsonResult);
        }
        #endregion


        #region ::: Select Uday Kumar J B 12-11-2024:::
        /// <summary>
        /// To Select 
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult Select(SelectCallSummaryList SelectCallSummaryobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonResult = default(dynamic);
            try
            {
                int Count = 0;
                int Total = 0;
                string frmdate = SelectCallSummaryobj.FromDate;
                string todate = SelectCallSummaryobj.ToDate;
                string CallSummaryQuery = string.Empty;
                string BranchName = string.Empty;
                int OB = 0, NC = 0, CCOB = 0, CCN = 0, TCC = 0, WIP = 0, OH = 0, ESC = 0, CB = 0;

                bool BusinessAreaAll = Convert.ToBoolean(SelectCallSummaryobj.BusinessAreaInternational);
                bool BusinessAreaDomestic = Convert.ToBoolean(SelectCallSummaryobj.BusinessAreaDomestic);
                bool BusineesRole = Convert.ToBoolean(SelectCallSummaryobj.BusineesRole);
                bool BusinessIndividual = Convert.ToBoolean(SelectCallSummaryobj.BusinessIndividual);
                int Branch_ID = Convert.ToInt32(SelectCallSummaryobj.Branch_ID);
                int Company_ID = Convert.ToInt32(SelectCallSummaryobj.Company_ID);
                string branchIDs = Convert.ToString(SelectCallSummaryobj.branchIDs).TrimEnd(',');
                string CompanyIDs = Convert.ToString(SelectCallSummaryobj.CompanyIDs).TrimEnd(',');

                bool ROLE = BusinessIndividual;

                List<CallSummary1> iECallSummary = new List<CallSummary1>();
                IQueryable<CallSummary1> iQCallSummary = null;
                // Connection string from web.config

                if (BusinessAreaDomestic)
                {
                    for (int i = 0; i < CompanyIDs.Split(',').Length; i++)
                    {
                        for (int j = 0; j < branchIDs.Split(',').Length; j++)
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            using (SqlCommand cmd = new SqlCommand("dbo.UP_ProductionSummary", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@StartDate", Convert.ToDateTime(frmdate));
                                cmd.Parameters.AddWithValue("@EndDate", Convert.ToDateTime(todate));
                                cmd.Parameters.AddWithValue("@BRANCH_ID", branchIDs.Split(',')[j]);
                                cmd.Parameters.AddWithValue("@COMPANY_ID", CompanyIDs.Split(',')[i]);
                                cmd.Parameters.AddWithValue("@ISLOCAL", false);
                                cmd.Parameters.AddWithValue("@ISINDIVIDUAL", ROLE);

                                conn.Open();
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        CallSummary1 callSummary = new CallSummary1
                                        {
                                            Region = reader["Region"].ToString(),
                                            CompanyName = reader["CompanyName"].ToString(),
                                            BranchName = reader["BranchName"].ToString(),
                                            CallSummary_DateSort = Convert.ToDateTime(reader["CallSummary_Date"]).ToString("dd-MMM-yyyy"),
                                            CallSummary_Date = Convert.ToDateTime(reader["CallSummary_Date"]),
                                            CallOwner_ID = Convert.ToInt32(reader["CallOwner_ID"]),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            MonthName = reader["MonthName"].ToString(),
                                            Week = Convert.ToInt32(reader["Week"]),
                                            Day = Convert.ToString(reader["Day"]),
                                            CallOwner = reader["CallOwner"].ToString(),
                                            OpeningBalance = Convert.ToInt32(reader["OpeningBalance"]),
                                            NewCalls = Convert.ToInt32(reader["NewCalls"]),
                                            CallsClosedOpeningBalance = Convert.ToInt32(reader["CallsClosedOpeningBalance"]),
                                            CallsClosedNew = Convert.ToInt32(reader["CallsClosedNew"]),
                                            TotalCallesClosed = Convert.ToInt32(reader["TotalCallesClosed"]),
                                            WorkInProgress = Convert.ToInt32(reader["WorkInProgress"]),
                                            OnHold = Convert.ToInt32(reader["OnHold"]),
                                            Escalated = Convert.ToInt32(reader["Escalated"]),
                                            ClosingBalance = Convert.ToInt32(reader["ClosingBalance"]),
                                            IsLocal = Convert.ToBoolean(reader["IsLocal"])
                                        };
                                        iECallSummary.Add(callSummary);
                                    }
                                }
                            }
                        }
                    }
                }
                else
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    using (SqlCommand cmd = new SqlCommand("dbo.UP_ProductionSummary", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@StartDate", Convert.ToDateTime(frmdate));
                        cmd.Parameters.AddWithValue("@EndDate", Convert.ToDateTime(todate));
                        cmd.Parameters.AddWithValue("@BRANCH_ID", Branch_ID);
                        cmd.Parameters.AddWithValue("@COMPANY_ID", Company_ID);
                        cmd.Parameters.AddWithValue("@ISLOCAL", true);
                        cmd.Parameters.AddWithValue("@ISINDIVIDUAL", ROLE);

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                CallSummary1 callSummary = new CallSummary1
                                {
                                    Region = reader["Region"].ToString(),
                                    CompanyName = reader["CompanyName"].ToString(),
                                    BranchName = reader["BranchName"].ToString(),
                                    CallSummary_DateSort = Convert.ToDateTime(reader["CallSummary_Date"]).ToString("dd-MMM-yyyy"),
                                    CallSummary_Date = Convert.ToDateTime(reader["CallSummary_Date"]),
                                    CallOwner_ID = Convert.ToInt32(reader["CallOwner_ID"]),
                                    Year = Convert.ToInt32(reader["Year"]),
                                    MonthName = reader["MonthName"].ToString(),
                                    Week = Convert.ToInt32(reader["Week"]),
                                    Day = Convert.ToString(reader["Day"]),
                                    CallOwner = reader["CallOwner"].ToString(),
                                    OpeningBalance = Convert.ToInt32(reader["OpeningBalance"]),
                                    NewCalls = Convert.ToInt32(reader["NewCalls"]),
                                    CallsClosedOpeningBalance = Convert.ToInt32(reader["CallsClosedOpeningBalance"]),
                                    CallsClosedNew = Convert.ToInt32(reader["CallsClosedNew"]),
                                    TotalCallesClosed = Convert.ToInt32(reader["TotalCallesClosed"]),
                                    WorkInProgress = Convert.ToInt32(reader["WorkInProgress"]),
                                    OnHold = Convert.ToInt32(reader["OnHold"]),
                                    Escalated = Convert.ToInt32(reader["Escalated"]),
                                    ClosingBalance = Convert.ToInt32(reader["ClosingBalance"]),
                                    IsLocal = Convert.ToBoolean(reader["IsLocal"])
                                };
                                iECallSummary.Add(callSummary);
                            }
                        }
                    }
                }

                // Order the list first and then convert it to IQueryable
                iQCallSummary = iECallSummary.OrderByDescending(i => i.CallSummary_Date).AsQueryable();

                if (_search)
                {
                    Filters Filterobj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    if (Filterobj.rules.Count() > 0)
                        iQCallSummary = iQCallSummary.FilterSearch<CallSummary1>(Filterobj);
                }
                iQCallSummary = iQCallSummary.OrderByField<CallSummary1>(sidx, sord);
                Count = iQCallSummary.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }
                OB = iQCallSummary.Sum(i => i.OpeningBalance);
                NC = iQCallSummary.Sum(i => i.NewCalls);
                CCOB = iQCallSummary.Sum(i => i.CallsClosedOpeningBalance);
                CCN = iQCallSummary.Sum(i => i.CallsClosedNew);
                TCC = iQCallSummary.Sum(i => i.TotalCallesClosed);
                WIP = iQCallSummary.Sum(i => i.WorkInProgress);
                OH = iQCallSummary.Sum(i => i.OnHold);
                ESC = iQCallSummary.Sum(i => i.Escalated);
                CB = iQCallSummary.Sum(i => i.ClosingBalance);



                jsonResult = new
                {
                    total = Total,
                    page = page,
                    records = Count,
                    data = (iQCallSummary).ToList().Paginate(page, rows),
                    OB,
                    NC,
                    CCOB,
                    CCN,
                    TCC,
                    WIP,
                    OH,
                    ESC,
                    CB
                };
                // gbl.InsertGPSDetails(Convert.ToInt32(SelectCallSummaryobj.Company_ID.ToString()), Convert.ToInt32(SelectCallSummaryobj.Branch_ID), SelectCallSummaryobj.User_ID, Common.GetObjectID("HelpDesk_Rpt_CallSummary"), 0, 0, 0, "Generated - Call Summary Report ", false, Convert.ToInt32(SelectCallSummaryobj.MenuID), Convert.ToDateTime(SelectCallSummaryobj.LoggedINDateTime));
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonResult);
        }
        #endregion


        #region ::: Check The Date Uday Kumar J B 11-11-2024:::
        /// <summary>
        /// To check the Date 
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult CheckValidDate(CheckValidDateList CheckValidDateobj)
        {
            string FromDate = CheckValidDateobj.FromDate;
            string ToDate = CheckValidDateobj.ToDate;
            if (FromDate != "" && ToDate != "")
            {
                if (CheckValidFromDate(FromDate) == 0 && CheckValidToDate(ToDate) == 0)
                {
                    return new JsonResult(0);
                }
                else if (CheckValidFromDate(FromDate) == 0)
                {
                    return new JsonResult(1);
                }
                else if (CheckValidToDate(ToDate) == 0)
                {
                    return new JsonResult(2);
                }
            }
            else if (FromDate != "" && ToDate == "")
            {
                if (CheckValidFromDate(FromDate) == 0)
                {
                    return new JsonResult(1);
                }
            }
            else if (FromDate == "" && ToDate != "")
            {
                if (CheckValidToDate(ToDate) == 0)
                {
                    return new JsonResult(2);
                }
            }
            return new JsonResult(-1);
        }

        public static int CheckValidFromDate(string FromDate)
        {
            try
            {
                DateTime temp = Convert.ToDateTime(FromDate);
                return 1;
            }
            catch (Exception e)
            {
                return 0;
            }
        }

        public static int CheckValidToDate(string ToDate)
        {
            try
            {
                DateTime temp = Convert.ToDateTime(ToDate);
                return 1;
            }
            catch (Exception e)
            {
                return 0;
            }
        }
        #endregion


        #region ::: Export Uday Kumar J B 12-11-2024:::
        /// <summary>
        /// Export
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static async Task<object> Export(CallSummaryExportList CallSummaryExportobj, string connString, int LogException, string filter, string advnceFilter, string sidx, string sord)
        {

            try
            {
                string frmdate = CallSummaryExportobj.FromDate;
                string todate = CallSummaryExportobj.ToDate;
                string CallSummaryQuery = string.Empty;
                string BranchName = string.Empty;
                bool BusinessAreaAll = Convert.ToBoolean(CallSummaryExportobj.BusinessAreaInternational);
                bool BusinessAreaDomestic = Convert.ToBoolean(CallSummaryExportobj.BusinessAreaDomestic);
                bool BusineesRole = Convert.ToBoolean(CallSummaryExportobj.BusineesRole);
                bool BusinessIndividual = Convert.ToBoolean(CallSummaryExportobj.BusinessIndividual);
                int Branch_ID = Convert.ToInt32(CallSummaryExportobj.Branch_ID);
                int Company_ID = Convert.ToInt32(CallSummaryExportobj.Company_ID);
                string branchIDs = Convert.ToString(CallSummaryExportobj.branchIDs).TrimEnd(',');
                string CompanyIDs = Convert.ToString(CallSummaryExportobj.CompanyIDs).TrimEnd(',');
                BranchName = CallSummaryExportobj.CallSummaryBranchName.ToString();
                string FromDate = CallSummaryExportobj.CallSummaryFromDate.ToString();
                string ToDate = CallSummaryExportobj.CallSummaryToDate.ToString();
                string BusinessArea = CallSummaryExportobj.BusinessArea.ToString();
                string BusinessRoles = CallSummaryExportobj.BusinessRoles.ToString();
                bool ROLE = BusinessIndividual;

                List<CallSummary1> iECallSummary = new List<CallSummary1>();
                IQueryable<CallSummary1> iQCallSummary = null;
                // Connection string from web.config

                if (BusinessAreaDomestic)
                {
                    for (int i = 0; i < CompanyIDs.Split(',').Length; i++)
                    {
                        for (int j = 0; j < branchIDs.Split(',').Length; j++)
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            using (SqlCommand cmd = new SqlCommand("dbo.UP_ProductionSummary", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@StartDate", Convert.ToDateTime(frmdate));
                                cmd.Parameters.AddWithValue("@EndDate", Convert.ToDateTime(todate));
                                cmd.Parameters.AddWithValue("@BRANCH_ID", branchIDs.Split(',')[j]);
                                cmd.Parameters.AddWithValue("@COMPANY_ID", CompanyIDs.Split(',')[i]);
                                cmd.Parameters.AddWithValue("@ISLOCAL", false);
                                cmd.Parameters.AddWithValue("@ISINDIVIDUAL", ROLE);

                                conn.Open();
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        CallSummary1 callSummary = new CallSummary1
                                        {
                                            Region = reader["Region"].ToString(),
                                            CompanyName = reader["CompanyName"].ToString(),
                                            BranchName = reader["BranchName"].ToString(),
                                            CallSummary_DateSort = Convert.ToDateTime(reader["CallSummary_Date"]).ToString("dd-MMM-yyyy"),
                                            CallSummary_Date = Convert.ToDateTime(reader["CallSummary_Date"]),
                                            CallOwner_ID = Convert.ToInt32(reader["CallOwner_ID"]),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            MonthName = reader["MonthName"].ToString(),
                                            Week = Convert.ToInt32(reader["Week"]),
                                            Day = Convert.ToString(reader["Day"]),
                                            CallOwner = reader["CallOwner"].ToString(),
                                            OpeningBalance = Convert.ToInt32(reader["OpeningBalance"]),
                                            NewCalls = Convert.ToInt32(reader["NewCalls"]),
                                            CallsClosedOpeningBalance = Convert.ToInt32(reader["CallsClosedOpeningBalance"]),
                                            CallsClosedNew = Convert.ToInt32(reader["CallsClosedNew"]),
                                            TotalCallesClosed = Convert.ToInt32(reader["TotalCallesClosed"]),
                                            WorkInProgress = Convert.ToInt32(reader["WorkInProgress"]),
                                            OnHold = Convert.ToInt32(reader["OnHold"]),
                                            Escalated = Convert.ToInt32(reader["Escalated"]),
                                            ClosingBalance = Convert.ToInt32(reader["ClosingBalance"]),
                                            IsLocal = Convert.ToBoolean(reader["IsLocal"])
                                        };
                                        iECallSummary.Add(callSummary);
                                    }
                                }
                            }
                        }
                    }
                }
                else
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    using (SqlCommand cmd = new SqlCommand("dbo.UP_ProductionSummary", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@StartDate", Convert.ToDateTime(frmdate));
                        cmd.Parameters.AddWithValue("@EndDate", Convert.ToDateTime(todate));
                        cmd.Parameters.AddWithValue("@BRANCH_ID", Branch_ID);
                        cmd.Parameters.AddWithValue("@COMPANY_ID", Company_ID);
                        cmd.Parameters.AddWithValue("@ISLOCAL", true);
                        cmd.Parameters.AddWithValue("@ISINDIVIDUAL", ROLE);

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                CallSummary1 callSummary = new CallSummary1
                                {
                                    Region = reader["Region"].ToString(),
                                    CompanyName = reader["CompanyName"].ToString(),
                                    BranchName = reader["BranchName"].ToString(),
                                    CallSummary_DateSort = Convert.ToDateTime(reader["CallSummary_Date"]).ToString("dd-MMM-yyyy"),
                                    CallSummary_Date = Convert.ToDateTime(reader["CallSummary_Date"]),
                                    CallOwner_ID = Convert.ToInt32(reader["CallOwner_ID"]),
                                    Year = Convert.ToInt32(reader["Year"]),
                                    MonthName = reader["MonthName"].ToString(),
                                    Week = Convert.ToInt32(reader["Week"]),
                                    Day = Convert.ToString(reader["Day"]),
                                    CallOwner = reader["CallOwner"].ToString(),
                                    OpeningBalance = Convert.ToInt32(reader["OpeningBalance"]),
                                    NewCalls = Convert.ToInt32(reader["NewCalls"]),
                                    CallsClosedOpeningBalance = Convert.ToInt32(reader["CallsClosedOpeningBalance"]),
                                    CallsClosedNew = Convert.ToInt32(reader["CallsClosedNew"]),
                                    TotalCallesClosed = Convert.ToInt32(reader["TotalCallesClosed"]),
                                    WorkInProgress = Convert.ToInt32(reader["WorkInProgress"]),
                                    OnHold = Convert.ToInt32(reader["OnHold"]),
                                    Escalated = Convert.ToInt32(reader["Escalated"]),
                                    ClosingBalance = Convert.ToInt32(reader["ClosingBalance"]),
                                    IsLocal = Convert.ToBoolean(reader["IsLocal"])
                                };
                                iECallSummary.Add(callSummary);
                            }
                        }
                    }
                }

                // Order the list first and then convert it to IQueryable
                iQCallSummary = iECallSummary.OrderByDescending(i => i.CallSummary_Date).AsQueryable();

                if (filter.ToString() != "null")
                {
                    Filters filters = JObject.Parse(Common.DecryptString(filter)).ToObject<Filters>();
                    if (filters.rules.Any())
                    {
                        iQCallSummary = iQCallSummary.FilterSearch<CallSummary1>(filters);
                    }
                }
                iQCallSummary = iQCallSummary.OrderByField<CallSummary1>(sidx, sord);

                List<CallSummary1> CallSummaryList = iQCallSummary.ToList();
                DataTable Dt = new DataTable();

                //Modified by Shashi for call# TML-2014103002 - Removed BusinessType
                var CallSummaryArray = from a in CallSummaryList
                                       select new
                                       {
                                           a.Region,
                                           a.CompanyName,
                                           a.BranchName,
                                           a.Year,
                                           a.MonthName,
                                           a.Week,
                                           a.Day,
                                           CallSummary_Date = a.CallSummary_Date.ToString("dd-MMM-yyyy"),
                                           a.CallOwner,
                                           a.OpeningBalance,
                                           a.NewCalls,
                                           a.CallsClosedOpeningBalance,
                                           a.CallsClosedNew,
                                           a.TotalCallesClosed,
                                           a.WorkInProgress,
                                           a.OnHold,
                                           a.Escalated,
                                           a.ClosingBalance,
                                           IsLocal = a.IsLocal == true ? CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "Domestic").ToString() : CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "International").ToString(),
                                       };

                Dt.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "Region").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "Company").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "Branch").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "Year").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "month").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "Week").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "Day").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "Date").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "Name").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "OpeningBalance").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "NewCalls").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "CallsClosedOpeningBalance").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "CallsClosedNew").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "TotalCallsClosed").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "WorkInProgress").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "OnHold").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "Escalated").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "ClosingBalance").ToString());

                DataTable dtAlignment = new DataTable();
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "Region").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "Company").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "Branch").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "Year").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "month").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "Week").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "Day").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "Date").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "Name").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "OpeningBalance").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "NewCalls").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "CallsClosedOpeningBalance").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "CallsClosedNew").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "TotalCallsClosed").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "WorkInProgress").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "OnHold").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "Escalated").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "ClosingBalance").ToString());
                dtAlignment.Rows.Add(0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2);


                DataTable DateRange = new DataTable();
                //DateRange.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "Branch").ToString());
                DateRange.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "fromdate").ToString());
                DateRange.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "todate").ToString());
                DateRange.Columns.Add(CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "BusinessArea").ToString());
                //DateRange.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "BusinessRoles").ToString());
                DateRange.Rows.Add(FromDate, ToDate, BusinessArea);

                if (CallSummaryArray.Count() > 0)
                {
                    for (int i = 0; i < CallSummaryArray.Count(); i++)
                    {
                        Dt.Rows.Add(CallSummaryArray.ElementAt(i).Region, CallSummaryArray.ElementAt(i).CompanyName, CallSummaryArray.ElementAt(i).BranchName, CallSummaryArray.ElementAt(i).Year, CallSummaryArray.ElementAt(i).MonthName, CallSummaryArray.ElementAt(i).Week, CallSummaryArray.ElementAt(i).Day, CallSummaryArray.ElementAt(i).CallSummary_Date, CallSummaryArray.ElementAt(i).CallOwner, CallSummaryArray.ElementAt(i).OpeningBalance, CallSummaryArray.ElementAt(i).NewCalls, CallSummaryArray.ElementAt(i).CallsClosedOpeningBalance, CallSummaryArray.ElementAt(i).CallsClosedNew, CallSummaryArray.ElementAt(i).TotalCallesClosed, CallSummaryArray.ElementAt(i).WorkInProgress, CallSummaryArray.ElementAt(i).OnHold, CallSummaryArray.ElementAt(i).Escalated, CallSummaryArray.ElementAt(i).ClosingBalance); //CallSummaryArray.ElementAt(i).IsLocal
                    }
                    ReportExportList reportExportList = new ReportExportList
                    {
                        Company_ID = CallSummaryExportobj.Company_ID, // Assuming this is available in ExportObj
                        Branch = CallSummaryExportobj.Branch_ID.ToString(),
                        GeneralLanguageID = CallSummaryExportobj.GeneralLanguageID,
                        UserLanguageID = CallSummaryExportobj.LanguageID,
                        Options = new DataTable(),
                        dt = Dt,
                        Alignment = dtAlignment,
                        FileName = "CallSummary", // Set a default or dynamic filename
                        Header = CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "CallSummary").ToString(), // Set a default or dynamic header
                        exprtType = CallSummaryExportobj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                        UserCulture = CallSummaryExportobj.UserCulture
                    };

                    var result = await ReportExport.Export(reportExportList, connString, LogException);
                    return result.Value;
                    //  ReportExport.Export(CallSummaryExportobj.exprtType, Dt, DateRange, dtAlignment, "CallSummary", CommonFunctionalities.GetResourceString(CallSummaryExportobj.UserCulture.ToString(), "CallSummary").ToString());
                }
                //Changes Ends
                //  gbl.InsertGPSDetails(Convert.ToInt32(CallSummaryExportobj.Company_ID.ToString()), Convert.ToInt32(CallSummaryExportobj.Branch_ID), CallSummaryExportobj.User_ID, Common.GetObjectID("HelpDesk_Rpt_CallSummary"), 0, 0, 0, "Export - Call Summary Report ", false, Convert.ToInt32(CallSummaryExportobj.MenuID), Convert.ToDateTime(CallSummaryExportobj.LoggedINDateTime));
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return false;
        }
        #endregion


        #region ::: HelpDesk_Rpt_CallSummary List and obj Classes Uday Kumar J B 12-11-2024:::
        /// <summary>
        /// Export
        /// </summary>
        /// <returns>...</returns>
        /// 
        public class CheckValidDateList
        {
            public string FromDate { get; set; }
            public string ToDate { get; set; }

        }
        public class CallSummaryExportList
        {
            public string FromDate { get; set; }
            public string ToDate { get; set; }
            public bool BusinessAreaInternational { get; set; }
            public bool BusinessAreaDomestic { get; set; }
            public bool BusinessIndividual { get; set; }
            public bool BusineesRole { get; set; }
            public int Branch_ID { get; set; }
            public int Company_ID { get; set; }
            public string branchIDs { get; set; }
            public string CompanyIDs { get; set; }
            public int MenuID { get; set; }
            public int User_ID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string CallSummaryBranchName { get; set; }
            public string CallSummaryFromDate { get; set; }
            public string CallSummaryToDate { get; set; }
            public string BusinessArea { get; set; }
            public string BusinessRoles { get; set; }
            public string UserCulture { get; set; }
            public int exprtType { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filter { get; set; }
            public string advanceFilter { get; set; }
            public int GeneralLanguageID { get; set; }
            public int LanguageID { get; set; }
        }
        public class SelectCallSummaryList
        {
            public string FromDate { get; set; }
            public string ToDate { get; set; }
            public bool BusinessAreaInternational { get; set; }
            public bool BusinessAreaDomestic { get; set; }
            public bool BusinessIndividual { get; set; }
            public bool BusineesRole { get; set; }
            public int Branch_ID { get; set; }
            public int Company_ID { get; set; }
            public string branchIDs { get; set; }
            public string CompanyIDs { get; set; }
            public int MenuID { get; set; }
            public int User_ID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }

        public class GetCallOwnersList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Company_ID { get; set; }
        }
        #endregion


        #region ::: HelpDesk_Rpt_CallSummary Classes Uday Kumar J B 12-11-2024:::
        /// <summary>
        /// Export
        /// </summary>
        /// <returns>...</returns>
        /// 
        public class CallSummary1
        {
            public int CallSummary_ID { get; set; }
            public DateTime CallSummary_Date { get; set; }
            public string CallSummary_DateSort { get; set; }
            public int OpeningBalance { get; set; }
            public int NewCalls { get; set; }
            public int CallsClosedOpeningBalance { get; set; }
            public int CallsClosedNew { get; set; }
            public int TotalCallesClosed { get; set; }
            public int WorkInProgress { get; set; }
            public int OnHold { get; set; }
            public int Escalated { get; set; }
            public int ClosingBalance { get; set; }
            public bool IsLocal { get; set; }
            public int Year { get; set; }
            public int Month { get; set; }
            public int Week { get; set; }
            public string MonthName { get; set; }
            public string Date { get; set; }
            public int CallOwner_ID { get; set; }
            public string CallOwner { get; set; }
            public string Day { get; set; }
            public string Region { get; set; }
            public string CompanyName { get; set; }
            public string BranchName { get; set; }
        }
        #endregion


    }
}
