﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Internal;
using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreUploadPriceListServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreUploadPriceListController : ApiController
    {

        #region ::: UploadFileParts Uday Kumar J B 19-08-2024:::
        /// <summary>
        /// To Upload File Parts
        /// </summary>
        ///
        [Route("api/CoreUploadPriceList/UploadFileParts")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> UploadFileParts(UploadFilePartsCoreUploadPriceList UploadFilePartsCoreUploadPriceListobj)
        {

            try
            {
                var provider = new MultipartMemoryStreamProvider();

                // Read the multipart form data into the provider
                await Request.Content.ReadAsMultipartAsync(provider);

                // Retrieve form data
                string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                string supplierID = null;
                string effectiveFrom = null;
                string userId = null;
                string companyId = null;
                string branchId = null;
                string loggedInDateTime = null;
                string userCulture = null;
                IFormFile partsPriceFile = null;


                foreach (var content in provider.Contents)
                {
                    var name = content.Headers.ContentDisposition.Name.Trim('"');

                    // Check if content is a form field or a file
                    if (content.Headers.ContentDisposition.FileName == null)
                    {
                        // Read form field data
                        var value = await content.ReadAsStringAsync();

                        switch (name)
                        {
                            case "supplierID":
                                supplierID = value;
                                break;
                            case "companyId":
                                companyId = value;
                                break;
                            case "userId":
                                userId = value;
                                break;
                            case "branchId":
                                branchId = value;
                                break;
                            case "effectiveFrom":
                                effectiveFrom = value;
                                break;
                            case "loggedInDateTime":
                                loggedInDateTime = value;
                                break;
                            case "userCulture":
                                userCulture = value;
                                break;
                            default:
                                // Handle other form fields if necessary
                                break;
                        }
                    }
                    else
                    {
                        // Check if the content is the file we want
                        if (name == "formFile")
                        {
                            // Read file content
                            var stream = await content.ReadAsStreamAsync();
                            partsPriceFile = new FormFile(stream, 0, stream.Length, content.Headers.ContentDisposition.Name.Trim('"'), content.Headers.ContentDisposition.FileName.Trim('"'));
                        }
                    }
                }

                // Call your service method to process the data
                var Response = default(dynamic);
                Response = CoreUploadPriceListServices.UploadFileParts(partsPriceFile, connString, UploadFilePartsCoreUploadPriceListobj);

                return Ok(Response.Value);
            }
            catch (Exception ex)
            {
                // Log or handle exceptions
                return InternalServerError(ex);
            }
        }

        #endregion


        #region ::: UploadFile Uday Kumar J B 19-08-2024:::
        /// <summary>
        /// To Upload File
        /// </summary>
        /// 
        [Route("api/CoreUploadPriceList/UploadFile")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> UploadFile(UploadFilePartsCoreUploadPriceList UploadFilePartsCoreUploadPriceListobj)
        {

            try
            {
                var provider = new MultipartMemoryStreamProvider();

                // Read the multipart form data into the provider
                await Request.Content.ReadAsMultipartAsync(provider);

                // Retrieve form data
                string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                string supplierID = null;
                string effectiveFrom = null;
                string companyId = null;
                string userCulture = null;
                IFormFile partsPrice = null;


                foreach (var content in provider.Contents)
                {
                    var name = content.Headers.ContentDisposition.Name.Trim('"');

                    // Check if content is a form field or a file
                    if (content.Headers.ContentDisposition.FileName == null)
                    {
                        // Read form field data
                        var value = await content.ReadAsStringAsync();

                        switch (name)
                        {
                            case "supplierID":
                                supplierID = value;
                                break;
                            case "companyId":
                                companyId = value;
                                break;
                            case "effectiveFrom":
                                effectiveFrom = value;
                                break;
                            case "userCulture":
                                userCulture = value;
                                break;
                            default:
                                // Handle other form fields if necessary
                                break;
                        }
                    }
                    else
                    {
                        // Check if the content is the file we want
                        if (name == "formFile")
                        {
                            // Read file content
                            var stream = await content.ReadAsStreamAsync();
                            partsPrice = new FormFile(stream, 0, stream.Length, content.Headers.ContentDisposition.Name.Trim('"'), content.Headers.ContentDisposition.FileName.Trim('"'));
                        }
                    }
                }

                // Call your service method to process the data
                var Response = default(dynamic);
                Response = CoreUploadPriceListServices.UploadFile(partsPrice, connString, UploadFilePartsCoreUploadPriceListobj);

                return Ok(Response.Value);
            }
            catch (Exception ex)
            {
                // Log or handle exceptions
                return InternalServerError(ex);
            }
        }

        #endregion


        #region ::: SelSupplier Uday Kumar J B 19-08-2024:::
        /// <summary>
        /// To Sel Supplier
        /// </summary>
        /// 
        [Route("api/CoreUploadPriceList/SelSupplier")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelSupplier([FromBody] SelSupplierList SelSupplierobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreUploadPriceListServices.SelSupplier(connString, SelSupplierobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion

    }
}