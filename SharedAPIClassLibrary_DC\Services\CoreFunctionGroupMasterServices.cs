﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace SharedAPIClassLibrary_AMERP
{
    public class CoreFunctionGroupMasterServices
    {


        #region ::: Select Function Groups Uday Kumar J B 08-08-2024 :::
        /// <summary>
        /// to get the logged in users permission
        /// </summary>  

        public static IActionResult SelAllFunctionGroups(string connString, SelAllFunctionGroupsList SelAllFunctionGroupsobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filterString, bool advnce, string advnceFilters)
        {
            int count = 0;
            int total = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            bool IsGeneral = true;
            string AppPath = string.Empty;
            List<FunctionGroupObjects> functionGroupList = new List<FunctionGroupObjects>();
            try
            {
                string GenLangCode = SelAllFunctionGroupsobj.GeneralLanguageCode.ToString();
                string UserLangCode = SelAllFunctionGroupsobj.UserLanguageCode.ToString();
                //GNM_User User = SelAllFunctionGroupsobj.UserDetails.FirstOrDefault();
                int companyID = SelAllFunctionGroupsobj.Company_ID;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("Up_Sel_AM_ERP_SelAllFunctionGroups", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                FunctionGroupObjects fgo = new FunctionGroupObjects
                                {
                                    FunctionGroup_ID = Convert.ToInt32(reader["FunctionGroup_ID"]),
                                    Brand = reader["Brand"] != DBNull.Value ? reader["Brand"].ToString() : "",
                                    FunctionGroup_Name = reader["FunctionGroup_Name"].ToString(),
                                    FunctionGroup_IsActive = Convert.ToBoolean(reader["FunctionGroup_IsActive"]) ? "Yes" : "No"
                                };
                                functionGroupList.Add(fgo);
                            }
                        }
                    }
                }

                if (GenLangCode != UserLangCode)
                {
                    IsGeneral = false;
                }

                IQueryable<FunctionGroupObjects> iQFunGroup = functionGroupList.AsQueryable();

                // FilterToolBar Search
                if (_search)
                {
                    Filters filters = JObject.Parse(Common.DecryptString(filterString)).ToObject<Filters>();
                    if (filters.rules.Count() > 0)
                        iQFunGroup = iQFunGroup.FilterSearch(filters);
                }
                // Advance Search
                else if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse(advnceFilters).ToObject<AdvanceFilter>();
                    iQFunGroup = iQFunGroup.AdvanceSearch(advnfilter);
                }

                // Sorting
                iQFunGroup = iQFunGroup.OrderByField(sidx, sord);

                // Storing in Session (Alternative approach required)
                // HttpContext.Current.Session["FunctionGroupMaster"] = iQFunGroup;

                count = iQFunGroup.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                // Prepare edit titles before the query
                string editTitle = CommonFunctionalities.GetGlobalResourceObject(SelAllFunctionGroupsobj.UserCulture.ToString(), "edit").ToString();

                var jsonResult = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = (from a in iQFunGroup
                            select new
                            {
                                edit = $"<a title='{editTitle}' href='#' id='{a.FunctionGroup_ID}' key='{a.FunctionGroup_ID}' class='editFunGroup font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = $"<input type='checkbox' key='{a.FunctionGroup_ID}' defaultchecked='' id='chk{a.FunctionGroup_ID}' class='chkClick'/>",
                                a.FunctionGroup_ID,
                                a.Brand,
                                FunctionGroup_Name = a.FunctionGroup_Name,
                                a.FunctionGroup_IsActive,
                                Locale = (IsGeneral) ? "" : $"<a src='{AppPath}/Content/images/local.png' height='20' key4='{a.FunctionGroup_IsActive}' width='20' class='Locale' key='{a.FunctionGroup_ID}'><i class='fa fa-globe'></i></a>"
                            }).ToList().Paginate(page, rows),
                    filter = filterString,
                    advanceFilter = advnceFilters,
                };

                return new JsonResult(jsonResult);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                return new JsonResult(null);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(null);
            }
        }
        #endregion


        #region ::: Select Function Groups Native Uday Kumar J B 08-08-2024:::
        /// <summary>
        /// to get the logged in users permission
        /// </summary>   
        /// 

        public static IActionResult SelFunctionGroupsNative(string connString, SelFunctionGroupsNativeList SelFunctionGroupsNativeobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int count = 0;
            int total = 0;
            string AppPath = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                //var user = SelFunctionGroupsNativeobj.UserDetails.FirstOrDefault();
                //if (user == null)
                //{
                //    throw new Exception("User session is null");
                //}

                int companyID = SelFunctionGroupsNativeobj.Company_ID;
                int LangID = SelFunctionGroupsNativeobj.Language_ID;

                List<dynamic> functionGroupArray = new List<dynamic>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("Up_Sel_AM_ERP_SelFunctionGroupsNative", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);
                        cmd.Parameters.AddWithValue("@LangID", LangID);

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                string userCulture = SelFunctionGroupsNativeobj.UserCulture?.ToString();
                                if (string.IsNullOrEmpty(userCulture))
                                {
                                    throw new Exception("User culture session is null or empty");
                                }

                                functionGroupArray.Add(new
                                {
                                    FunctionGroup_ID = reader["FunctionGroup_ID"],
                                    edit = $"<img id='{reader["FunctionGroup_ID"]}' src='{AppPath}/Content/images/plus.gif' key='{reader["FunctionGroup_ID"]}' class='editFunGroupL' editmode='false'/>",
                                    Brand = reader["Brand"].ToString(),
                                    FunctionGroup_Name = reader["FunctionGroup_Name"].ToString(),
                                    FunctionGroup_IsActive = Convert.ToBoolean(reader["FunctionGroup_IsActive"])
                                        ? CommonFunctionalities.GetResourceString(userCulture, "yes").ToString()
                                        : CommonFunctionalities.GetResourceString(userCulture, "no").ToString()
                                });
                            }
                        }
                    }
                }
                IQueryable<dynamic> functionGroupQueryable = functionGroupArray.AsQueryable();

                if (_search && !string.IsNullOrEmpty(filters))
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(filters))).ToObject<Filters>();
                    functionGroupQueryable = functionGroupQueryable.FilterSearch(filtersObj);
                }
                else if (advnce && !string.IsNullOrEmpty(advnceFilters))
                {
                    AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<AdvanceFilter>();
                    functionGroupQueryable = functionGroupQueryable.AdvanceSearch(advnfilter);
                    page = 1;
                }

                count = functionGroupQueryable.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                var jsonResult = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = functionGroupQueryable
                };

                return new JsonResult(jsonResult);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                return new JsonResult(null);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(null);
            }
        }
        #endregion


        #region ::: to Select Function Group Operations Uday Kumar J B 08-08-2024 :::
        /// <summary>
        /// to Select Function Group Operatins
        /// </summary> 
        /// 
        public static IActionResult SelFunGroupsOperations(string connString, SelFunGroupsOperationsList SelFunGroupsOperationsobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filterString, bool advnce, string advnceFilters)
        {
            int count = 0;
            int total = 0;
            var operations = new List<dynamic>();
            var x = new object();
            int GenLangID = Convert.ToInt32(SelFunGroupsOperationsobj.GeneralLanguageID);
            //GNM_User User = SelFunGroupsOperationsobj.UserDetails.FirstOrDefault();
            int companyID = SelFunGroupsOperationsobj.Company_ID;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string YesE = CommonFunctionalities.GetResourceString(SelFunGroupsOperationsobj.GeneralCulture.ToString(), "Yes").ToString();
            string NoE = CommonFunctionalities.GetResourceString(SelFunGroupsOperationsobj.GeneralCulture.ToString(), "No").ToString();
            string YesL = CommonFunctionalities.GetResourceString(SelFunGroupsOperationsobj.UserCulture.ToString(), "Yes").ToString();
            string NoL = CommonFunctionalities.GetResourceString(SelFunGroupsOperationsobj.UserCulture.ToString(), "No").ToString();

            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand(GenLangID == SelFunGroupsOperationsobj.LangID ? "sp_GetOperations" : "sp_GetLocalizedOperations", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);
                        cmd.Parameters.AddWithValue("@FunctionGroupID", SelFunGroupsOperationsobj.id);
                        if (GenLangID != SelFunGroupsOperationsobj.LangID)
                        {
                            cmd.Parameters.AddWithValue("@LanguageID", SelFunGroupsOperationsobj.LangID);
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                operations.Add(new
                                {
                                    FunctionGroup_ID = reader["FunctionGroup_ID"],
                                    Operation_Code = reader["Operation_Code"],
                                    Operation_Description = reader["Operation_Description"],
                                    Operation_IsActive = (bool)reader["Operation_IsActive"] ? (GenLangID == SelFunGroupsOperationsobj.LangID ? YesE : YesL) : (GenLangID == SelFunGroupsOperationsobj.LangID ? NoE : NoL)
                                });
                            }
                        }
                    }
                }

                count = operations.Count;
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                if (_search)
                {
                    Filters filters = JObject.Parse(filterString).ToObject<Filters>();
                    if (filters.rules.Count > 0)
                    {
                        operations = ApplyFilters(operations, filters);
                    }
                }

                // operations = ApplySorting(operations, sidx, sord).Skip((page - 1) * rows).Take(rows).ToList();

                x = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = operations
                };

                return new JsonResult(x);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                return new JsonResult(null);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(null);
            }
        }

        private static List<dynamic> ApplyFilters(List<dynamic> operations, Filters filters)
        {
            foreach (var rule in filters.rules)
            {
                operations = operations.Where(op => op.GetType().GetProperty(rule.field).GetValue(op, null).ToString() == rule.data).ToList();
            }
            return operations;
        }

        private static List<dynamic> ApplySorting(List<dynamic> operations, string sidx, string sord)
        {
            var propertyInfo = typeof(object).GetProperty(sidx);
            if (sord == "desc")
            {
                operations = operations.OrderByDescending(op => propertyInfo.GetValue(op, null)).ToList();
            }
            else
            {
                operations = operations.OrderBy(op => propertyInfo.GetValue(op, null)).ToList();
            }
            return operations;
        }

        #endregion


        #region ::: SelParticularFunctionGroup Uday Kumar J B 08-08-2024 :::
        /// <summary>
        /// to SelParticularFunctionGroup
        /// </summary>
        /// 
        public static IActionResult SelParticularFunctionGroup(string connString, SelParticularFunctionGroupList SelParticularFunctionGroupobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                GNM_FunctionGroup functionGroupList = null;

                using (SqlConnection con = new SqlConnection(connString))
                {
                    con.Open();
                    SqlCommand cmd = con.CreateCommand();
                    cmd.CommandText = "Up_Sel_GNM_FunctionGroup";
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@FunctionGroupID", SelParticularFunctionGroupobj.id);
                    SqlDataReader reader = cmd.ExecuteReader();
                    if (reader.Read())
                    {
                        functionGroupList = new GNM_FunctionGroup
                        {
                            FunctionGroup_ID = Convert.ToInt32(reader["FunctionGroup_ID"]),
                            FunctionGroup_Name = reader["FunctionGroup_Name"].ToString(),
                            FunctionGroup_IsActive = reader["FunctionGroup_IsActive"] != DBNull.Value ? Convert.ToBoolean(reader["FunctionGroup_IsActive"]) : false,
                            Brand_ID = reader["Brand_ID"] != DBNull.Value ? Convert.ToInt32(reader["Brand_ID"]) : 0
                        };

                    }
                    reader.Close();
                }

                var specilizationRow = default(dynamic);
                if (SelParticularFunctionGroupobj.languageID == 1)
                {
                    using (SqlConnection con = new SqlConnection(connString))
                    {
                        con.Open();
                        SqlCommand cmd = con.CreateCommand();
                        cmd.CommandText = "Up_Sel_GNM_RefMasterDetail";
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@BrandID", functionGroupList.Brand_ID);
                        SqlDataReader reader = cmd.ExecuteReader();
                        GNM_RefMasterDetail Ref = null;
                        if (reader.Read())
                        {
                            Ref = new GNM_RefMasterDetail
                            {
                                RefMasterDetail_ID = Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                RefMasterDetail_Name = reader["RefMasterDetail_Name"].ToString()
                            };
                        }
                        reader.Close();

                        specilizationRow = new
                        {
                            FunctionGroup_ID = functionGroupList.FunctionGroup_ID,
                            FunctionGroup_Name = functionGroupList.FunctionGroup_Name,
                            FunctionGroup_IsActive = functionGroupList.FunctionGroup_IsActive,
                            Brand_ID = functionGroupList.Brand_ID,
                            Brand_Name = Ref != null ? Ref.RefMasterDetail_Name : ""
                        };
                    }
                }
                else
                {
                    using (SqlConnection con = new SqlConnection(connString))
                    {
                        con.Open();
                        SqlCommand cmd = con.CreateCommand();
                        cmd.CommandText = "Up_Sel_GNM_FunctionGroupLocale";
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@FunctionGroupID", SelParticularFunctionGroupobj.id);
                        cmd.Parameters.AddWithValue("@LanguageID", SelParticularFunctionGroupobj.languageID);
                        SqlDataReader reader = cmd.ExecuteReader();
                        GNM_FunctionGroupLocale functionLocale = null;
                        if (reader.Read())
                        {
                            functionLocale = new GNM_FunctionGroupLocale
                            {
                                FunctionGroup_ID = Convert.ToInt32(reader["FunctionGroup_ID"]),
                                FunctionGroup_Name = reader["FunctionGroup_Name"].ToString()
                            };
                        }
                        reader.Close();

                        cmd.CommandText = "Up_Sel_GNM_RefMasterDetailLocale";
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@BrandID", functionGroupList.Brand_ID);
                        cmd.Parameters.AddWithValue("@LanguageID", SelParticularFunctionGroupobj.languageID);
                        reader = cmd.ExecuteReader();
                        GNM_RefMasterDetailLocale RefL = null;
                        if (reader.Read())
                        {
                            RefL = new GNM_RefMasterDetailLocale
                            {
                                RefMasterDetail_ID = Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                RefMasterDetail_Name = reader["RefMasterDetail_Name"].ToString()
                            };
                        }
                        reader.Close();

                        if (functionLocale != null)
                        {
                            specilizationRow = new
                            {
                                FunctionGroup_ID = functionGroupList.FunctionGroup_ID,
                                FunctionGroup_Name = functionLocale.FunctionGroup_Name,
                                FunctionGroup_IsActive = functionGroupList.FunctionGroup_IsActive,
                                Brand_ID = functionGroupList.Brand_ID,
                                Brand_Name = RefL != null ? RefL.RefMasterDetail_Name : ""
                            };
                        }
                    }
                }

                return new JsonResult(specilizationRow);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                return new JsonResult(null);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(null);
            }
        }
        #endregion


        #region :::  Select FunctionGroup Locale Uday Kumar J B 08-08-2024:::
        /// <summary>
        /// Select FunctionGroup Locale
        /// </summary>
        /// 

        public static IActionResult SelFunctionGroupLocale(string connString, SelFunctionGroupLocaleList SelFunctionGroupLocaleobj)
        {
            dynamic dummy = null;
            var jsonResult = dummy;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                //GNM_User User = SelFunctionGroupLocaleobj.UserDetails.FirstOrDefault();
                int langid = SelFunctionGroupLocaleobj.Language_ID;
                string FGLname = "";
                int FGLid = 0;

                using (SqlConnection con = new SqlConnection(connString))
                {
                    con.Open();
                    SqlCommand cmd = con.CreateCommand();
                    cmd.CommandText = "Up_Sel_AM_ERP_FunctionGroupLocale";
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@FunctionGroupID", SelFunctionGroupLocaleobj.id);
                    cmd.Parameters.AddWithValue("@LanguageID", langid);
                    SqlDataReader reader = cmd.ExecuteReader();
                    if (reader.Read())
                    {
                        FGLname = reader["FunctionGroup_Name"].ToString();
                        FGLid = Convert.ToInt32(reader["FunctionGroupLocale_ID"]);
                    }
                    reader.Close();
                }

                jsonResult = new
                {
                    FGLocaleName = FGLname,
                    FGLocaleID = FGLid
                };
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                return new JsonResult(null);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(null);
            }
            return new JsonResult(jsonResult);
        }
        #endregion


        #region :::  Save Function Group Uday Kumar J B 08-08-2024 :::
        /// <summary>
        /// Save Function Group
        /// </summary>
        /// 

        public static IActionResult SaveFunctionGroup(string connString, SaveFunctionGroupList SaveFunctionGroupobj)
        {
            string Mode = string.Empty;
            int companyID = 0;
            GNM_FunctionGroup FunGroup = null;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // GNM_User User = SaveFunctionGroupobj.UserDetails.FirstOrDefault();
                companyID = SaveFunctionGroupobj.Company_ID;

                JObject jObj = JObject.Parse(SaveFunctionGroupobj.Data);
                FunGroup = JObject.Parse(SaveFunctionGroupobj.Data).ToObject<GNM_FunctionGroup>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    if (FunGroup.FunctionGroup_ID == 0)
                    {
                        using (SqlCommand cmd = new SqlCommand("sp_InsertFunctionGroup", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@FunctionGroup_Name", Common.DecryptString(FunGroup.FunctionGroup_Name));
                            cmd.Parameters.AddWithValue("@Company_ID", companyID);
                            cmd.Parameters.AddWithValue("@ModifiedBy", SaveFunctionGroupobj.User_ID);
                            cmd.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                            SqlParameter outputIdParam = new SqlParameter("@FunctionGroup_ID", SqlDbType.Int)
                            {
                                Direction = ParameterDirection.Output
                            };
                            cmd.Parameters.Add(outputIdParam);

                            cmd.ExecuteNonQuery();

                            FunGroup.FunctionGroup_ID = (int)outputIdParam.Value;

                            // gbl.InsertGPSDetails(Convert.ToInt32(SaveFunctionGroupobj.Company_ID), Convert.ToInt32(SaveFunctionGroupobj.Branch), Convert.ToInt32(SaveFunctionGroupobj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreFunctionGroupMaster")), FunGroup.FunctionGroup_ID, 0, 0, "Inserted " + FunGroup.FunctionGroup_Name, false, Convert.ToInt32(SaveFunctionGroupobj.MenuID), Convert.ToDateTime(SaveFunctionGroupobj.LoggedINDateTime));
                        }
                    }
                    else
                    {
                        using (SqlCommand cmd = new SqlCommand("sp_UpdateFunctionGroup", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@FunctionGroup_ID", FunGroup.FunctionGroup_ID);
                            cmd.Parameters.AddWithValue("@FunctionGroup_IsActive", FunGroup.FunctionGroup_IsActive);
                            cmd.Parameters.AddWithValue("@FunctionGroup_Name", Common.DecryptString(FunGroup.FunctionGroup_Name));
                            cmd.Parameters.AddWithValue("@Brand_ID", FunGroup.Brand_ID);
                            cmd.Parameters.AddWithValue("@ModifiedBy", SaveFunctionGroupobj.User_ID);
                            cmd.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                            cmd.ExecuteNonQuery();

                            // gbl.InsertGPSDetails(Convert.ToInt32(SaveFunctionGroupobj.Company_ID), Convert.ToInt32(SaveFunctionGroupobj.Branch), Convert.ToInt32(SaveFunctionGroupobj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreFunctionGroupMaster")), FunGroup.FunctionGroup_ID, 0, 0, "Updated " + FunGroup.FunctionGroup_Name, false, Convert.ToInt32(SaveFunctionGroupobj.MenuID), Convert.ToDateTime(SaveFunctionGroupobj.LoggedINDateTime));
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }
        #endregion


        #region :::  Delete FunctionGroup Uday Kumar J B 08-08-2024:::
        /// <summary>
        /// Delete FunctionGroup
        /// </summary>
        /// 

        public static IActionResult DeleteFunctionGroup(string connString, DeleteFunctionGroupList DeleteFunctionGroupobj)
        {
            string errorMsg = "";
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JTokenReader reader = null;
                JObject jobj = JObject.Parse(DeleteFunctionGroupobj.key);
                int rowCount = jobj["rows"].Count();
                int id = 0;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    SqlTransaction transaction = conn.BeginTransaction();

                    try
                    {
                        for (int i = 0; i < rowCount; i++)
                        {
                            reader = new JTokenReader(jobj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                            reader.Read();
                            id = Convert.ToInt32(reader.Value);

                            // Delete from GNM_FunctionGroupLocale
                            using (SqlCommand cmd = new SqlCommand("sp_DeleteFunctionGroupLocale", conn, transaction))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@FunctionGroup_ID", id);
                                cmd.ExecuteNonQuery();
                            }

                            // Delete from GNM_FunctionGroup
                            using (SqlCommand cmd = new SqlCommand("sp_DeleteFunctionGroup", conn, transaction))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@FunctionGroup_ID", id);
                                cmd.ExecuteNonQuery();
                            }

                            // gbl.InsertGPSDetails(Convert.ToInt32(DeleteFunctionGroupobj.Company_ID), Convert.ToInt32(DeleteFunctionGroupobj.Branch), Convert.ToInt32(DeleteFunctionGroupobj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreFunctionGroupMaster")), id, 0, 0, "Delete", false, Convert.ToInt32(DeleteFunctionGroupobj.MenuID));
                        }

                        transaction.Commit();
                        errorMsg = CommonFunctionalities.GetGlobalResourceObject(DeleteFunctionGroupobj.GeneralCulture.ToString(), "deletedsuccessfully").ToString(); // "Deleted successfully";
                    }
                    catch (Exception)
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                errorMsg = CommonFunctionalities.GetResourceString(DeleteFunctionGroupobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString(); // "Dependency found cannot delete the record";
            }
            return new JsonResult(errorMsg);
        }
        #endregion


        #region :::  Insert Function Group Locale Uday Kumar J B 08-08-2024 :::
        /// <summary>
        /// Insert Function Group Locale
        /// </summary>
        /// 

        public static IActionResult InsertFunctionGroupLocale(string connString, InsertFunctionGroupLocaleList InsertFunctionGroupLocaleobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // GNM_User User = InsertFunctionGroupLocaleobj.UserDetails.FirstOrDefault();
                int langid = InsertFunctionGroupLocaleobj.Language_ID;
                GNM_FunctionGroupLocale FGLocale = JObject.Parse(InsertFunctionGroupLocaleobj.Data).ToObject<GNM_FunctionGroupLocale>();
                FGLocale.Language_ID = langid;
                FGLocale.FunctionGroup_Name = Common.DecryptString(FGLocale.FunctionGroup_Name);

                using (SqlConnection con = new SqlConnection(connString))
                {
                    con.Open();
                    SqlCommand cmd = new SqlCommand("Up_Ins_AM_ERP_FunctionGroupLocale", con);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@FunctionGroupID", FGLocale.FunctionGroup_ID);
                    cmd.Parameters.AddWithValue("@LanguageID", FGLocale.Language_ID);
                    cmd.Parameters.AddWithValue("@FunctionGroupName", FGLocale.FunctionGroup_Name);
                    cmd.ExecuteNonQuery();
                }

                // gbl.InsertGPSDetails(Convert.ToInt32(InsertFunctionGroupLocaleobj.Company_ID), Convert.ToInt32(InsertFunctionGroupLocaleobj.Branch), Convert.ToInt32(InsertFunctionGroupLocaleobj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreFunctionGroupMaster")), FGLocale.FunctionGroup_ID, 0, 0, "Update", false, Convert.ToInt32(InsertFunctionGroupLocaleobj.MenuID));
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }
        #endregion


        #region :::  Update Function Group Locale Uday Kumar J B 08-08-2024 :::
        /// <summary>
        /// Update Function Group Locale
        /// </summary>
        /// 

        public static IActionResult UpdateFunctionGroupLocale(string connString, UpdateFunctionGroupLocaleList UpdateFunctionGroupLocaleobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // GNM_User User = UpdateFunctionGroupLocaleobj.UserDetails.FirstOrDefault();
                int langid = UpdateFunctionGroupLocaleobj.Language_ID;
                GNM_FunctionGroupLocale FGLocale = JObject.Parse(UpdateFunctionGroupLocaleobj.Data).ToObject<GNM_FunctionGroupLocale>();

                using (SqlConnection con = new SqlConnection(connString))
                {
                    con.Open();

                    // Fetch existing FunctionGroupLocale
                    SqlCommand cmd = new SqlCommand("Up_Sel_And_Upd_AM_ERP_GNM_FunctionGroupLocale", con);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@FunctionGroupLocaleID", FGLocale.FunctionGroupLocale_ID);
                    cmd.Parameters.AddWithValue("@IsUpdate", 0);  // 0 indicates SELECT operation

                    SqlDataReader reader = cmd.ExecuteReader();
                    GNM_FunctionGroupLocale FGLocaleEdit = null;
                    if (reader.Read())
                    {
                        FGLocaleEdit = new GNM_FunctionGroupLocale
                        {
                            FunctionGroupLocale_ID = Convert.ToInt32(reader["FunctionGroupLocale_ID"]),
                            FunctionGroup_ID = Convert.ToInt32(reader["FunctionGroup_ID"]),
                            FunctionGroup_Name = Common.DecryptString(reader["FunctionGroup_Name"].ToString()),
                            Language_ID = Convert.ToInt32(reader["Language_ID"])
                        };
                    }
                    reader.Close();

                    if (FGLocaleEdit != null)
                    {
                        FGLocaleEdit.FunctionGroup_ID = FGLocale.FunctionGroup_ID;
                        FGLocaleEdit.FunctionGroup_Name = Common.DecryptString(FGLocale.FunctionGroup_Name);
                        FGLocaleEdit.Language_ID = langid;

                        // Update FunctionGroupLocale
                        cmd.Parameters.Clear();
                        cmd.Parameters.AddWithValue("@FunctionGroupLocaleID", FGLocaleEdit.FunctionGroupLocale_ID);
                        cmd.Parameters.AddWithValue("@FunctionGroupID", FGLocaleEdit.FunctionGroup_ID);
                        cmd.Parameters.AddWithValue("@FunctionGroupName", FGLocaleEdit.FunctionGroup_Name);
                        cmd.Parameters.AddWithValue("@LanguageID", FGLocaleEdit.Language_ID);
                        cmd.Parameters.AddWithValue("@IsUpdate", 1);  // 1 indicates UPDATE operation
                        cmd.ExecuteNonQuery();
                    }
                }

                // gbl.InsertGPSDetails(Convert.ToInt32(UpdateFunctionGroupLocaleobj.Company_ID), Convert.ToInt32(UpdateFunctionGroupLocaleobj.Branch), Convert.ToInt32(UpdateFunctionGroupLocaleobj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreFunctionGroupMaster")), FGLocale.FunctionGroup_ID, 0, 0, "Update", false, Convert.ToInt32(UpdateFunctionGroupLocaleobj.MenuID));
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }
        #endregion


        #region :::  FunctionGroupExport Uday Kumar J B 08-08-2024:::
        /// <summary>
        /// Exporting FunctionGroup Grid
        /// </summary>
        /// 

        public static async Task<object> FunctionGroupExport(FunctionGroupExportList FunctionGroupExportobj, string connString, string filters, string advnceFilters, string sidx, string sord)
        {
            int companyID = Convert.ToInt32(FunctionGroupExportobj.Company_ID);
            List<FunctionGroupObjects> functionGroupDataList = new List<FunctionGroupObjects>();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // Fetch data from database using stored procedure
                string GenLangCode = FunctionGroupExportobj.GeneralLanguageCode.ToString();
                string UserLangCode = FunctionGroupExportobj.UserLanguageCode.ToString();
                int LangID = FunctionGroupExportobj.Language_ID;  // Ensure LangID is passed correctly

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // First stored procedure: Up_Sel_AM_ERP_SelAllFunctionGroups
                    using (SqlCommand cmd = new SqlCommand("Up_Sel_AM_ERP_SelAllFunctionGroups", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                FunctionGroupObjects fgo = new FunctionGroupObjects
                                {
                                    FunctionGroup_ID = Convert.ToInt32(reader["FunctionGroup_ID"]),
                                    Brand = reader["Brand"] != DBNull.Value ? reader["Brand"].ToString() : "",
                                    FunctionGroup_Name = reader["FunctionGroup_Name"].ToString(),
                                    FunctionGroup_IsActive = Convert.ToBoolean(reader["FunctionGroup_IsActive"]) ? "Yes" : "No"
                                };
                                functionGroupDataList.Add(fgo);
                            }
                        }
                    }

                    // Only execute second stored procedure if LangID is provided and valid
                    if (LangID == FunctionGroupExportobj.GeneralLanguageID)
                    {
                        using (SqlCommand cmd = new SqlCommand("Up_Sel_AM_ERP_SelFunctionGroupsNative", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@CompanyID", companyID);
                            cmd.Parameters.AddWithValue("@LangID", LangID);  // Ensure LangID is passed

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    // Process and add data to functionGroupDataList or create separate logic based on LangID
                                    FunctionGroupObjects fgo = new FunctionGroupObjects
                                    {
                                        FunctionGroup_ID = Convert.ToInt32(reader["FunctionGroup_ID"]),
                                        Brand = reader["Brand"] != DBNull.Value ? reader["Brand"].ToString() : "",
                                        FunctionGroup_Name = reader["FunctionGroup_Name"].ToString(),
                                        FunctionGroup_IsActive = Convert.ToBoolean(reader["FunctionGroup_IsActive"]) ? "Yes" : "No"
                                    };
                                    functionGroupDataList.Add(fgo);
                                }
                            }
                        }
                    }
                }

                // Apply standard filters if present
                var functionGroupList = functionGroupDataList.AsQueryable().OrderByField(sidx, sord);
                // Apply standard filters if present
                if (!string.IsNullOrEmpty(filters) && filters != "null" && filters != "undefined")
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    if (filtersObj.rules.Count > 0)
                        functionGroupList = functionGroupList.FilterSearch(filtersObj);
                }


                // Apply advanced filters if present
                if (!string.IsNullOrEmpty(advnceFilters) && advnceFilters != "null")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Common.DecryptString(advnceFilters)).ToObject<AdvanceFilter>();
                    functionGroupList = functionGroupList.AdvanceSearch(advnfilter);
                }

                // Convert filtered data to DataTable
                DataTable dt = new DataTable();
                dt.Columns.Add("FunctionGroupName");
                dt.Columns.Add("Brand");
                dt.Columns.Add("IsActive");

                foreach (var functionGroup in functionGroupList)
                {
                    DataRow row = dt.NewRow();
                    row["FunctionGroupName"] = functionGroup.FunctionGroup_Name;
                    row["Brand"] = functionGroup.Brand;
                    row["IsActive"] = functionGroup.FunctionGroup_IsActive;
                    dt.Rows.Add(row);
                }

                // Dummy data table for demo purposes
                DataTable dt1 = new DataTable();
                dt1.Columns.Add("FunctionGroupName");
                dt1.Columns.Add("Brand");
                dt1.Columns.Add("IsActive");
                dt1.Rows.Add("0", "0", "1");

                ExportList reportExportList = new ExportList
                {
                    Company_ID = FunctionGroupExportobj.Company_ID, // Assuming this is available in ExportObj
                    Branch = FunctionGroupExportobj.Branch,
                    dt1 = dt1,


                    dt = dt,

                    FileName = "Function Groups", // Set a default or dynamic filename
                    Header = CommonFunctionalities.GetResourceString(FunctionGroupExportobj.UserCulture.ToString(), "FunctionGroups").ToString(), // Set a default or dynamic header
                    exprtType = FunctionGroupExportobj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                    UserCulture = FunctionGroupExportobj.UserCulture
                };
                // Call the Export method
                //DocumentExport.Export(FunctionGroupExportobj.exprtType, dt, dt1, "FunctionGroups", "Function Groups");
                // return DocumentExport.Export(reportExportList, connString, LogException);
                var result = await DocumentExport.Export(reportExportList, connString, LogException);
                return result.Value;
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return null;
        }
        #endregion


        #region :::  Get Brand Details Uday Kumar J B 08-08-2024 :::
        /// <summary>
        /// Get Brand Details
        /// </summary>
        /// 
        public static IActionResult GetBrandDetails(string connString, GetBrandDetailsList GetBrandDetailsobj)
        {
            dynamic jsonResult = null;
            int LangID = 0;
            List<BrandDetail> BrandList = new List<BrandDetail>();
            List<BrandLocaleDetail> BrandLocaleList = new List<BrandLocaleDetail>();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                //GNM_User User = GetBrandDetailsobj.UserDetails.FirstOrDefault();
                LangID = GetBrandDetailsobj.Language_ID;



                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Fetch Brand Details
                    using (SqlCommand cmd = new SqlCommand("GetBrandDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", GetBrandDetailsobj.Company_ID);
                        cmd.Parameters.AddWithValue("@RefMasterName", "BRAND");

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                BrandList.Add(new BrandDetail
                                {
                                    RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                    RefMasterDetail_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"))
                                });
                            }
                        }
                    }

                    // Fetch Brand Locale Details if needed
                    if (GetBrandDetailsobj.languageID != 1)
                    {
                        using (SqlCommand cmd = new SqlCommand("GetBrandLocaleDetails", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@LanguageID", LangID);
                            cmd.Parameters.AddWithValue("@CompanyID", GetBrandDetailsobj.Company_ID);
                            cmd.Parameters.AddWithValue("@RefMasterName", "BRAND");

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    BrandLocaleList.Add(new BrandLocaleDetail
                                    {
                                        RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                        RefMasterDetail_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"))
                                    });
                                }
                            }
                        }

                        jsonResult = new
                        {
                            brandList = (from brand in BrandList
                                         join brandL in BrandLocaleList on brand.RefMasterDetail_ID equals brandL.RefMasterDetail_ID
                                         select new
                                         {
                                             Name = brandL.RefMasterDetail_Name,
                                             Id = brandL.RefMasterDetail_ID
                                         }).ToArray()
                        };
                    }
                    else
                    {
                        jsonResult = new
                        {
                            brandList = BrandList.Select(brand => new
                            {
                                Name = brand.RefMasterDetail_Name,
                                Id = brand.RefMasterDetail_ID
                            }).ToArray()
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonResult);
        }
        #endregion


        #region :::  LoadBrandforLanguage Uday Kumar J B 08-08-2024 :::
        /// <summary>
        /// LoadBrandforLanguage
        /// </summary>
        /// 
        public static IActionResult LoadBrandforLanguage(string connString, GetBrandDetailsList GetBrandDetailsobj)
        {
            var BrandData = default(dynamic);
            int LangID;
            int CompanyID;
            DataTable CompBrandsTable;
            DataTable BrandTable;
            DataTable BrandLocaleTable = null;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));

            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // GNM_User UserD = GetBrandDetailsobj.UserDetails.FirstOrDefault();
                    LangID = GetBrandDetailsobj.Language_ID;
                    CompanyID = Convert.ToInt32(GetBrandDetailsobj.Company_ID);

                    // Fetch Company Brands
                    using (SqlCommand cmd = new SqlCommand("GetCompanyBrandsByCompanyID", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", CompanyID);

                        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        {
                            CompBrandsTable = new DataTable();
                            da.Fill(CompBrandsTable);
                        }
                    }

                    if (GetBrandDetailsobj.languageID == Convert.ToInt32(GetBrandDetailsobj.GeneralLanguageID))
                    {
                        // Fetch RefMasterDetail
                        using (SqlCommand cmd = new SqlCommand("GetRefMasterDetailsLoadBrandforLanguage", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;

                            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                            {
                                BrandTable = new DataTable();
                                da.Fill(BrandTable);
                            }
                        }

                        // Join CompBrands and Brand
                        var result = from DataRow cb in CompBrandsTable.Rows
                                     join DataRow refDetail in BrandTable.Rows on cb["Brand_ID"] equals refDetail["RefMasterDetail_ID"]
                                     select new
                                     {
                                         ID = refDetail["RefMasterDetail_ID"],
                                         Name = refDetail["RefMasterDetail_Name"]
                                     };

                        BrandData = result;
                    }
                    else
                    {
                        // Fetch RefMasterDetailLocale by LanguageID
                        using (SqlCommand cmd = new SqlCommand("GetRefMasterDetailLocaleByLanguageID", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@LanguageID", GetBrandDetailsobj.languageID);

                            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                            {
                                BrandLocaleTable = new DataTable();
                                da.Fill(BrandLocaleTable);
                            }
                        }

                        // Fetch RefMasterDetail
                        using (SqlCommand cmd = new SqlCommand("GetRefMasterDetails", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;

                            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                            {
                                BrandTable = new DataTable();
                                da.Fill(BrandTable);
                            }
                        }

                        // Join CompBrands, BrandLocale, and Brand
                        var result = from DataRow cb in CompBrandsTable.Rows
                                     join DataRow refL in BrandLocaleTable.Rows on cb["Brand_ID"] equals refL["RefMasterDetail_ID"]
                                     join DataRow refDetail in BrandTable.Rows on refL["RefMasterDetail_ID"] equals refDetail["RefMasterDetail_ID"]
                                     select new
                                     {
                                         ID = refL["RefMasterDetail_ID"],
                                         Name = refL["RefMasterDetail_Name"]
                                     };

                        BrandData = result;
                    }

                    return new JsonResult(BrandData);
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }
        #endregion


        #region :::  Check If FunctionExits Uday Kumar J B 08-08-2024 :::
        /// <summary>
        /// CheckIfFunctionExits
        /// </summary>
        /// 
        public static IActionResult CheckIfFunctionExits(string connString, CheckIfFunctionExitsList CheckIfFunctionExitsobj)
        {
            //GNM_User UserDetails = CheckIfFunctionExitsobj.UserDetails.FirstOrDefault();
            int companyID = CheckIfFunctionExitsobj.Company_ID;
            string FunVal = Common.DecryptString(CheckIfFunctionExitsobj.value);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            bool exists = false;

            using (SqlConnection conn = new SqlConnection(connString))
            {
                using (SqlCommand cmd = new SqlCommand("CheckIfFunctionExists", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@FunVal", FunVal.ToLower());
                    cmd.Parameters.AddWithValue("@BrandID", CheckIfFunctionExitsobj.brndID);
                    cmd.Parameters.AddWithValue("@FncID", CheckIfFunctionExitsobj.fncID);
                    cmd.Parameters.AddWithValue("@CompanyID", companyID);

                    conn.Open();
                    int count = (int)cmd.ExecuteScalar();
                    exists = count > 0;
                }
            }

            return new JsonResult(exists);
        }
        #endregion


        #region :::  Check If FunctionLocale Exits Uday Kumar J B 08-08-2024 :::
        /// <summary>
        /// CheckIfFunctionLocaleExits
        /// </summary>
        ///
        public static IActionResult CheckIfFunctionLocaleExits(string connString, CheckIfFunctionLocaleExitsList CheckIfFunctionLocaleExitsobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                //GNM_User UserDetails = CheckIfFunctionLocaleExitsobj.UserDetails.FirstOrDefault();
                int companyID = CheckIfFunctionLocaleExitsobj.Company_ID;
                string decodedValue = Common.DecryptString(CheckIfFunctionLocaleExitsobj.value);
                string value = Uri.UnescapeDataString(decodedValue);
                int languageID = Convert.ToInt32(CheckIfFunctionLocaleExitsobj.UserLanguageID);

                bool exists = false;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("CheckIfFunctionLocaleExists", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Value", value.ToLower());
                        cmd.Parameters.AddWithValue("@FnID", CheckIfFunctionLocaleExitsobj.fnID);
                        cmd.Parameters.AddWithValue("@FncLID", CheckIfFunctionLocaleExitsobj.fncLID);
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);
                        cmd.Parameters.AddWithValue("@LanguageID", languageID);

                        conn.Open();
                        int count = (int)cmd.ExecuteScalar();
                        exists = count > 0;
                    }
                }

                return new JsonResult(exists);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }
        #endregion


        #region :::  FUnctionGroup Classes and obj Uday Kumar J B 08-08-2024 :::
        /// <summary>
        /// CheckIfFunctionLocaleExits
        /// </summary>
        ///
        public class CheckIfFunctionLocaleExitsList
        {
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public List<GNM_User> UserDetails { get; set; }
            public string value { get; set; }
            public int fnID { get; set; }

            public int fncLID { get; set; }
        }
        public class CheckIfFunctionExitsList
        {
            public List<GNM_User> UserDetails { get; set; }
            public int Company_ID { get; set; }
            public string value { get; set; }
            public int brndID { get; set; }
            public int fncID { get; set; }
        }
        public class GetBrandDetailsList
        {
            public List<GNM_User> UserDetails { get; set; }
            public int languageID { get; set; }
            public int Company_ID { get; set; }
            public int Language_ID { get; set; }
            public int GeneralLanguageID { get; set; }

        }
        public class BrandDetail
        {
            public int RefMasterDetail_ID { get; set; }
            public string RefMasterDetail_Name { get; set; }
        }

        public class BrandLocaleDetail
        {
            public int RefMasterDetail_ID { get; set; }
            public string RefMasterDetail_Name { get; set; }
        }

        public class FunctionGroupExportList
        {
            public int exprtType { get; set; }
            public int Company_ID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
            public int Language_ID { get; set; }
            public int Branch { get; set; }
            public string UserCulture { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filter { get; set; }
            public string advanceFilter { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        public class UpdateFunctionGroupLocaleList
        {
            public string Data { get; set; }
            public List<GNM_User> UserDetails { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Language_ID { get; set; }
        }
        public class InsertFunctionGroupLocaleList
        {
            public string Data { get; set; }
            public List<GNM_User> UserDetails { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Language_ID { get; set; }
            public string GeneralCulture { get; set; }
        }
        public class DeleteFunctionGroupList
        {
            public string key { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public string GeneralCulture { get; set; }
        }
        public class SaveFunctionGroupList
        {
            public string Data { get; set; }
            public List<GNM_User> UserDetails { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }
        public class SelFunctionGroupLocaleList
        {
            public int id { get; set; }
            public List<GNM_User> UserDetails { get; set; }
            public int Language_ID { get; set; }
        }
        public class SelParticularFunctionGroupList
        {
            public int id { get; set; }
            public int languageID { get; set; }
        }
        public class SelFunGroupsOperationsList
        {
            public int GeneralLanguageID { get; set; }

            public List<GNM_User> UserDetails { get; set; }
            public string GeneralCulture { get; set; }
            public string UserCulture { get; set; }
            public int LangID { get; set; }
            public int Company_ID { get; set; }
            public int id { get; set; }
        }

        public class SelFunctionGroupsNativeList
        {
            public List<GNM_User> UserDetails { get; set; }

            public string UserCulture { get; set; }
            public int Company_ID { get; set; }
            public int Language_ID { get; set; }
        }
        public class SelAllFunctionGroupsList
        {
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
            public List<GNM_User> UserDetails { get; set; }
            public int Company_ID { get; set; }
            public string UserCulture { get; set; }
        }

        #endregion


        #region :::  FunctionGroup Classes Uday Kumar J B 08-08-2024 :::
        /// <summary>
        ///  FunctionGroup Classes
        /// </summary>
        ///
        public class FunctionGroupObjects
        {
            public int FunctionGroup_ID { get; set; }
            public string FunctionGroup_Name { get; set; }
            public string Brand { get; set; }
            public string FunctionGroup_IsActive { get; set; }
        }
        public partial class GNM_FunctionGroup
        {
            public GNM_FunctionGroup()
            {
                this.GNM_FunctionGroupLocale = new HashSet<GNM_FunctionGroupLocale>();
            }

            public int FunctionGroup_ID { get; set; }
            public int Company_ID { get; set; }
            public string FunctionGroup_Name { get; set; }
            public Nullable<int> Brand_ID { get; set; }
            public bool FunctionGroup_IsActive { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }

            public virtual ICollection<GNM_FunctionGroupLocale> GNM_FunctionGroupLocale { get; set; }
        }
        #endregion

    }
}
