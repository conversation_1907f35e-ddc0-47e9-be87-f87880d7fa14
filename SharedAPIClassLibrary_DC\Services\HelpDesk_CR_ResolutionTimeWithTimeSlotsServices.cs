﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using static SharedAPIClassLibrary_AMERP.CoreProductMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;





namespace SharedAPIClassLibrary_AMERP
{

    public class HelpDesk_CR_ResolutionTimeWithTimeSlotsServices
    {
        public static string RSTCompanyID = "";
        public static string startDate = "";
        public static string endDate = "";
        public static string BranchName = "";
        public static List<ServiceRequest> SRequestAllYearWiseGlobal = new List<ServiceRequest>();
        public static List<TimeSlot> ResolutionWithTimeSlot = new List<TimeSlot>();
        public static List<TimeSlot> ResolutionWithTimeSlotYear = new List<TimeSlot>();
        public static List<TimeSlot> ResolutionWithTimeSlotMonth = new List<TimeSlot>();
        public static List<TimeSlot> ResolutionWithTimeSlotDate = new List<TimeSlot>();
        public static List<ServiceRequest> SRequestAllMonthWiseGlobal = new List<ServiceRequest>();
        public static List<ServiceRequest> SRequestAllDateWiseVar = new List<ServiceRequest>();
        #region  vinay n ado.net common vinay n 20/11/24
        public static T GetValueFromDB<T>(string query, List<SqlParameter> parameters, string connectionString)
        {
            T result = default;

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                conn.Open();

                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    // Add parameters to the command
                    if (parameters != null && parameters.Count > 0)
                    {
                        cmd.Parameters.AddRange(parameters.ToArray());
                    }

                    // Execute the query
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        // If the result type is a collection (e.g., List<T>)
                        if (typeof(T).IsGenericType && typeof(T).GetGenericTypeDefinition() == typeof(List<>))
                        {

                            var list = Activator.CreateInstance<T>();
                            var columnPropertyMapping = typeof(T).GetGenericArguments()[0]
                                    .GetProperties()
                                    .Where(property => Enumerable.Range(0, reader.FieldCount)
                                                                 .Select(reader.GetName)
                                                                 .Contains(property.Name))
                                    .Select(property => new
                                    {
                                        Property = property,
                                        Ordinal = reader.GetOrdinal(property.Name),
                                        TargetType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType
                                    })
                                    .ToList();
                            while (reader.Read())
                            {
                                var item = Activator.CreateInstance(typeof(T).GetGenericArguments()[0]);

                                foreach (var mapping in columnPropertyMapping)
                                {
                                    var value = reader[mapping.Ordinal];
                                    if (value != DBNull.Value)
                                    {
                                        // Set property value directly using the precomputed type
                                        mapping.Property.SetValue(item, Convert.ChangeType(value, mapping.TargetType));
                                    }
                                }

                              ((IList)list).Add(item);
                            }
                            result = (T)list;
                        }
                        // If the result is a scalar type (like string, bool, int)
                        else if (typeof(T) == typeof(string))
                        {
                            if (reader.Read())
                            {
                                result = (T)Convert.ChangeType(reader[0], typeof(T));
                            }
                        }
                        else if (typeof(T) == typeof(bool))
                        {
                            if (reader.Read())
                            {
                                result = (T)Convert.ChangeType(reader[0].ToString().ToUpper() == "TRUE", typeof(T));
                            }
                        }
                        else if (typeof(T) == typeof(int))
                        {
                            if (reader.Read())
                            {
                                result = (T)Convert.ChangeType(reader[0], typeof(T));
                            }
                        }
                        // Handle cases for custom classes/complex objects
                        else if (typeof(T).IsClass && typeof(T) != typeof(string))
                        {
                            if (reader.Read())
                            {
                                result = Activator.CreateInstance<T>(); // Create an instance of the class
                                var columnPropertyMapping = typeof(T)
                                  .GetProperties()
                                  .Where(property => Enumerable.Range(0, reader.FieldCount)
                                                               .Select(reader.GetName)
                                                               .Contains(property.Name))
                                  .Select(property => new
                                  {
                                      Property = property,
                                      Ordinal = reader.GetOrdinal(property.Name),
                                      TargetType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType
                                  })
                                  .ToList();

                                foreach (var mapping in columnPropertyMapping)
                                {
                                    var value = reader[mapping.Ordinal];
                                    if (value != DBNull.Value)
                                    {
                                        // Set property value directly using the precomputed type
                                        mapping.Property.SetValue(result, Convert.ChangeType(value, mapping.TargetType));
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return result;
        }


        #endregion
        #region GetData Vinay n 20/11/24
        public static IActionResult GetData(GetDataHelpDesk_CR_ResolutionTimeWithTimeSlotsList Obj, string connString, int LogException)
        {



            var jsonobj = default(dynamic);


            List<ServiceRequest> SRequestAll = new List<ServiceRequest>();


            try
            {
                string frmdate = Obj.FDate ?? "";
                string todate = Obj.TDate ?? "";

                string Query = string.Empty;
                string FilterCondition = string.Empty;
                //FilterCondition = " AND CallClosureDateAndTime IS NOT NULL";

                string BranchNames = string.Empty;
                string Branchlist = string.Empty;


                if (Obj.Branch != null && Obj.Branch != "")
                {
                    string decodedValue = Uri.UnescapeDataString(Obj.Branch);
                    string decodedValue2 = Uri.UnescapeDataString(decodedValue);
                    RTS_Branch branchs = JObject.Parse(Common.DecryptString(decodedValue2)).ToObject<RTS_Branch>();

                    for (int i = 0; i < branchs.Branchs.Count; i++)
                    {
                        BranchNames = BranchNames + branchs.Branchs[i].ID + ", ";
                        Branchlist = Branchlist + branchs.Branchs[i].Name + ", ";
                    }
                    BranchNames = BranchNames.Remove(BranchNames.LastIndexOf(','), 1);
                    Branchlist = Branchlist.Remove(Branchlist.LastIndexOf(','), 1);
                }
                int CompanyID = 0;
                Company companys = new Company();
                if (Obj.Company != null && Obj.Company != "")
                {
                    string decodedValue = Uri.UnescapeDataString((Obj.Company));
                    string decodedValue2 = Uri.UnescapeDataString((decodedValue));
                    companys = JObject.Parse(Common.DecryptString(decodedValue2)).ToObject<Company>(); //~Manju M
                }
                string Company_ID = string.Empty;

                //start
                CompanyID = Convert.ToInt32(Obj.Company_ID);
                GNM_Company companydetail = new GNM_Company();
                string querycompanydetail = @"
                SELECT *
                FROM GNM_Company
                WHERE Company_ID = @CompanyID";
                List<SqlParameter> parameterscompanydetail = new List<SqlParameter>
                {
                    new SqlParameter("@CompanyID", CompanyID)
                };
                companydetail = GetValueFromDB<GNM_Company>(querycompanydetail, parameterscompanydetail, connString);



                if (companydetail.Company_Type != "M")
                {
                    querycompanydetail = @"
                    SELECT *
                    FROM GNM_Company
                    WHERE Company_ID = @ParentCompanyID";

                    parameterscompanydetail = new List<SqlParameter>
                    {
                   new SqlParameter("@ParentCompanyID", companydetail.Company_Parent_ID)
                };
                    companydetail = GetValueFromDB<GNM_Company>(querycompanydetail, parameterscompanydetail, connString);

                    CompanyID = companydetail.Company_ID;
                }
                //end

                if (Obj.Company != null && Obj.Company != "")
                {

                    for (int i = 0; i < companys.Companys.Count; i++)
                    {
                        Company_ID = Company_ID + companys.Companys[i].ID + ", ";
                    }
                    Company_ID = Company_ID.Remove(Company_ID.LastIndexOf(','), 1);
                }

                RSTCompanyID = Company_ID;
                if (frmdate == string.Empty && todate == string.Empty)
                {
                    FilterCondition = "";
                    startDate = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "all").ToString();
                    endDate = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "all").ToString();
                }

                if (frmdate != string.Empty && todate == string.Empty)
                {
                    DateTime FromDate = Convert.ToDateTime(frmdate);
                    FilterCondition = " AND CallClosureDateAndTime >='" + FromDate.ToString("dd-MMM-yyyy") + "'";
                    startDate = frmdate;
                    endDate = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "all").ToString();
                }

                if (frmdate == string.Empty && todate != string.Empty)
                {
                    DateTime ToDate = Convert.ToDateTime(todate).AddDays(1);
                    FilterCondition = " AND CallClosureDateAndTime <'" + ToDate.ToString("dd-MMM-yyyy") + "'";
                    startDate = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "all").ToString();
                    endDate = todate;
                }

                if (frmdate != string.Empty && todate != string.Empty)
                {
                    DateTime ToDate = Convert.ToDateTime(todate).AddDays(1);
                    DateTime FromDate = Convert.ToDateTime(frmdate);
                    FilterCondition = " AND CallClosureDateAndTime >='" + FromDate.ToString("dd-MMM-yyyy") + "' AND CallClosureDateAndTime <'" + ToDate.ToString("dd-MMM-yyyy") + "'";
                    startDate = frmdate;
                    endDate = todate;
                }


                FilterCondition = (BranchNames == "") ? FilterCondition : FilterCondition + " AND Branch_ID in (" + BranchNames + ")";
                FilterCondition = FilterCondition + " AND CallClosureDateAndTime IS NOT NULL";



                BranchName = Branchlist.ToString();



                if (Obj.Type == 1)//Company Hours
                {
                    Query = "SELECT ServiceRequest_ID,CallDateAndTime, CallClosureDateAndTime,ResolutionTime FROM HD_ServiceRequest WHERE Company_ID   in (" + Company_ID + ") " + FilterCondition;
                }
                else//24 Hours
                {
                    Query = "SELECT ServiceRequest_ID,CallDateAndTime, CallClosureDateAndTime, CONVERT(VARCHAR(50),DATEDIFF(HOUR,CallDateAndTime, CallClosureDateAndTime)) AS ResolutionTime FROM HD_ServiceRequest WHERE Company_ID in (" + Company_ID + ") " + FilterCondition;
                }

                SRequestAll = GetValueFromDB<List<ServiceRequest>>(Query, null, connString);

                List<TimeSlot> timeSlotList = BuildTimeSlotList(CompanyID, Obj.UserCulture, connString);

                foreach (var sr in SRequestAll)
                {
                    int THour = 0;
                    if (Obj.Type == 1)//Company Hours
                    {
                        THour = Convert.ToInt32(sr.ResolutionTime.Split(':')[0]);
                    }
                    else//24 Hours
                    {
                        THour = Convert.ToInt32(sr.ResolutionTime);
                    }
                    timeSlotList = CalculateTimeSlot(timeSlotList, THour);
                }
                for (int i = 0; i < timeSlotList.Count; i++)
                {
                    if (i == 0)
                    {
                        timeSlotList[i].Name = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "LessThan").ToString() + " " + timeSlotList[i].Hours + " " + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Hours").ToString();
                    }
                    else if (i == timeSlotList.Count - 1)
                    {
                        timeSlotList[i].Name = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "GreaterThan").ToString() + " " + timeSlotList[i - 1].Hours + " " + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Hours").ToString();
                    }
                    else
                    {
                        timeSlotList[i].Name = timeSlotList[i - 1].Hours + " " + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "To").ToString() + " " + timeSlotList[i].Hours + " " + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Hours").ToString();
                    }
                }

                ResolutionWithTimeSlot = timeSlotList;

                jsonobj = new
                {
                    TotalPages = 1,
                    PageNo = 1,
                    RecordCount = 1,
                    timeSlotList = timeSlotList
                };
                // gbl.InsertGPSDetails(Convert.ToInt32(Obj.Company_ID.ToString()), branchID, Obj.User_ID, Common.GetObjectID("HelpDesk_CR_ResolutionTimeWithTimeSlots"), 0, 0, 0, "Generated - Resolution Time With Time Slots", false, Convert.ToInt32(Obj.MenuID), Convert.ToDateTime(Obj.LoggedINDateTime));
            }
            catch (Exception ex)
            {
                if (LogException == 1) LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return new JsonResult(jsonobj);
        }

        #endregion
        #region BuildTimeSlotList  Vinay n 20/11/24
        private static List<TimeSlot> BuildTimeSlotList(int Company_ID, string UserCulture, string connString)
        {

            string query = @"
            SELECT Param_value
            FROM GNM_CompParam
            WHERE Company_ID = @Company_ID AND UPPER(Param_Name) = 'TIMESLOT'";
            List<SqlParameter> parameters = new List<SqlParameter>
                {
                    new SqlParameter("@Company_ID", Company_ID)
                };
            string TimeSlot = GetValueFromDB<string>(query, parameters, connString);
            string[] TimeSlotArray = TimeSlot.Split(',');
            List<TimeSlot> timeSlotList = new List<TimeSlot>();

            //To create slots based on company parameter
            for (int i = 0; i < TimeSlotArray.Length; i++)
            {
                timeSlotList.Add(new TimeSlot { Name = TimeSlotArray[i], Count = 0, Hours = TimeSlotArray[i] });
            }
            timeSlotList.Add(new TimeSlot { Name = (Convert.ToInt32(TimeSlotArray[TimeSlotArray.Length - 1]) + 1).ToString(), Count = 0, Hours = (Convert.ToInt32(TimeSlotArray[TimeSlotArray.Length - 1]) + 1).ToString() });

            //To Update the Slots name
            for (int i = 0; i < timeSlotList.Count; i++)
            {
                if (i == 0)
                {
                    timeSlotList[i].TempName = CommonFunctionalities.GetResourceString(UserCulture.ToString(), "LessThan").ToString() + " " + timeSlotList[i].Hours + " " + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "Hours").ToString();
                }
                else if (i == timeSlotList.Count - 1)
                {
                    timeSlotList[i].TempName = CommonFunctionalities.GetResourceString(UserCulture.ToString(), "GreaterThan").ToString() + " " + timeSlotList[i - 1].Hours + " " + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "Hours").ToString();
                }
                else
                {
                    timeSlotList[i].TempName = timeSlotList[i - 1].Hours + " " + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "To").ToString() + " " + timeSlotList[i].Hours + " " + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "Hours").ToString();
                }
            }
            return timeSlotList;
        }
        #endregion
        #region CalculateTimeSlot  Vinay n 20/11/24
        public static List<TimeSlot> CalculateTimeSlot(List<TimeSlot> Timeslot, int Value)
        {
            try
            {
                for (int i = 0; i < Timeslot.Count; i++)
                {
                    if (Value >= Convert.ToInt32(Timeslot[Timeslot.Count - 1].Name))
                    {
                        Timeslot[Timeslot.Count - 1].Count++;
                        break;
                    }
                    else if (Value <= Convert.ToInt32(Timeslot[i].Name) - 1)
                    {
                        Timeslot[i].Count++;
                        break;
                    }
                }
            }
            catch (Exception ex)
            {

            }
            return Timeslot;
        }
        #endregion
        #region GetYearData vinay n 21/11/24
        public static IActionResult GetYearData(GetYearDataList Obj, string connString, int LogException)
        {
            var jsonobj = default(dynamic);
            //int CompanyID = Convert.ToInt32(Session["Company_ID"]);
            List<ServiceRequest> SRequestAll = new List<ServiceRequest>();
            List<ServiceRequest> SRequestAllYearWise = new List<ServiceRequest>();


            try
            {
                string frmdate = Obj.FDate ?? "";
                string todate = Obj.TDate ?? "";
                string Query = string.Empty;
                string Query1 = string.Empty;
                string FilterCondition = string.Empty;

                string BranchNames = string.Empty;

                if (Obj.Branch != null && Obj.Branch != "")
                {
                    string decodedValue = Uri.UnescapeDataString(Obj.Branch);
                    string decodedValue2 = Uri.UnescapeDataString(decodedValue);
                    RTS_Branch branchs = JObject.Parse(Common.DecryptString(decodedValue2)).ToObject<RTS_Branch>();

                    for (int i = 0; i < branchs.Branchs.Count; i++)
                    {
                        BranchNames = BranchNames + branchs.Branchs[i].ID + ", ";
                    }
                    BranchNames = BranchNames.Remove(BranchNames.LastIndexOf(','), 1);
                }
                int CompanyID = 0;


                //start
                CompanyID = Convert.ToInt32(Obj.Company_ID);
                string querycompanydetail = @"
                SELECT TOP 1 *
                FROM GNM_Company
                WHERE Company_ID = @Company_ID";
                List<SqlParameter> parameterscompanydetail = new List<SqlParameter>
                {
                    new SqlParameter("@Company_ID", CompanyID)
                };
                GNM_Company companydetail = GetValueFromDB<GNM_Company>(querycompanydetail, parameterscompanydetail, connString);



                if (companydetail.Company_Type != "M")
                {
                    string query = @"
                    SELECT TOP 1 *
                    FROM GNM_Company
                    WHERE Company_ID = @ParentCompany_ID";
                    List<SqlParameter> parameters = new List<SqlParameter>
                    {
                        new SqlParameter("@ParentCompany_ID", companydetail.Company_Parent_ID.Value)
                    };
                    companydetail = GetValueFromDB<GNM_Company>(query, parameters, connString);
                    CompanyID = companydetail.Company_ID;
                }

                string Company_ID = RSTCompanyID.ToString();
                //FilterCondition = " AND CallClosureDateAndTime IS NOT NULL";
                if (frmdate == string.Empty && todate == string.Empty)
                {
                    FilterCondition = "";
                    startDate = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "all").ToString();
                    endDate = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "all").ToString();
                }

                if (frmdate != string.Empty && todate == string.Empty)
                {
                    DateTime FromDate = Convert.ToDateTime(frmdate);
                    FilterCondition = " AND CallClosureDateAndTime >='" + FromDate.ToString("dd-MMM-yyyy") + "'";
                    startDate = frmdate;
                    endDate = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "all").ToString();
                }

                if (frmdate == string.Empty && todate != string.Empty)
                {
                    DateTime ToDate = Convert.ToDateTime(todate).AddDays(1);
                    FilterCondition = " AND CallClosureDateAndTime <'" + ToDate.ToString("dd-MMM-yyyy") + "'";
                    startDate = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "all").ToString();
                    endDate = todate;
                }

                if (frmdate != string.Empty && todate != string.Empty)
                {
                    DateTime ToDate = Convert.ToDateTime(todate).AddDays(1);
                    DateTime FromDate = Convert.ToDateTime(frmdate);
                    FilterCondition = " AND CallClosureDateAndTime >='" + FromDate.ToString("dd-MMM-yyyy") + "' AND CallClosureDateAndTime <'" + ToDate.ToString("dd-MMM-yyyy") + "'";
                    startDate = frmdate;
                    endDate = todate;
                }

                FilterCondition = (BranchNames == "") ? FilterCondition : FilterCondition + " AND Branch_ID in (" + BranchNames + ")";
                FilterCondition = FilterCondition + " AND CallClosureDateAndTime IS NOT NULL";

                if (Obj.Type == 1)//Company Hours
                {
                    Query = "SELECT ServiceRequest_ID,CallDateAndTime, CallClosureDateAndTime,ResolutionTime FROM HD_ServiceRequest WHERE Company_ID in (" + Company_ID + ") " + FilterCondition;
                    Query1 = "SELECT distinct  DATEPART(YEAR,CallClosureDateAndTime)as Year FROM HD_ServiceRequest WHERE Company_ID in (" + Company_ID + ") " + FilterCondition;
                }
                else//24 Hours
                {
                    Query = "SELECT ServiceRequest_ID,CallDateAndTime, CallClosureDateAndTime, CONVERT(VARCHAR(50),DATEDIFF(HOUR,CallDateAndTime, CallClosureDateAndTime)) AS ResolutionTime FROM HD_ServiceRequest WHERE Company_ID in (" + Company_ID + ") " + FilterCondition;
                    Query1 = "SELECT distinct  DATEPART(YEAR,CallClosureDateAndTime)as Year FROM HD_ServiceRequest WHERE Company_ID in (" + Company_ID + ") " + FilterCondition;
                }

                SRequestAll = GetValueFromDB<List<ServiceRequest>>(Query, null, connString);
                SRequestAllYearWise = GetValueFromDB<List<ServiceRequest>>(Query1, null, connString);

                SRequestAllYearWiseGlobal = SRequestAllYearWise;
                List<TimeSlot> timeSlotList = new List<TimeSlot>();
                for (int j = 0; j < SRequestAllYearWise.Count; j++)
                {
                    int year = SRequestAllYearWise[j].Year;
                    List<TimeSlot> timeslotlist1 = null;
                    timeslotlist1 = BuildTimeSlotYearList(CompanyID, year, Obj.UserCulture, connString);
                    foreach (var sr in SRequestAll)
                    {
                        int THour = 0;
                        if (Convert.ToDateTime(sr.CallClosureDateAndTime).Year == year)
                        {
                            if (Obj.Type == 1)//Company Hours
                            {
                                THour = Convert.ToInt32(sr.ResolutionTime.Split(':')[0]);
                            }
                            else//24 Hours
                            {
                                THour = Convert.ToInt32(sr.ResolutionTime);
                            }
                            timeslotlist1 = CalculateTimeSlot(timeslotlist1, THour);
                        }
                    }
                    for (int i = 0; i < timeslotlist1.Count; i++)
                    {
                        if (i == 0)
                        {
                            timeslotlist1[i].Name = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "LessThan").ToString() + " " + timeslotlist1[i].Hours + " " + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Hours").ToString();
                        }
                        else if (i == timeslotlist1.Count - 1)
                        {
                            timeslotlist1[i].Name = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "GreaterThan").ToString() + " " + timeslotlist1[i - 1].Hours + " " + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Hours").ToString();
                        }
                        else
                        {
                            timeslotlist1[i].Name = timeslotlist1[i - 1].Hours + " " + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "To").ToString() + " " + timeslotlist1[i].Hours + " " + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Hours").ToString();
                        }
                    }
                    if (timeSlotList == null)
                    {
                        timeSlotList = timeslotlist1;
                    }
                    else
                    {
                        timeSlotList.AddRange(timeslotlist1);
                    }
                }
                int page = 1;
                ResolutionWithTimeSlotYear = timeSlotList;
                jsonobj = new
                {
                    rows = new object[SRequestAllYearWise.Count],
                    page = page,
                    total = SRequestAllYearWise.Count,
                    records = SRequestAllYearWise.Count
                };
                for (int i = 0; i < SRequestAllYearWise.Count; i++)
                {
                    int year = SRequestAllYearWise[i].Year;

                    var row = new Dictionary<string, object>
                        {
                            { "col_" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Year").ToString(), year }
                        };

                    for (int j = 0; j < timeSlotList.Count; j++)
                    {
                        if (year == timeSlotList[j].Year)
                        {
                            row.Add("col_" + timeSlotList[j].TempName.Replace(" ", ""), timeSlotList[j].Count);
                        }
                    }

                    jsonobj.rows[i] = row;
                }
                //System.Text.StringBuilder sb = new System.Text.StringBuilder();
                //sb.Append("<?xml version='1.0' encoding='utf-8'?>");
                //sb.Append("<rows>");
                //sb.Append("<total>");
                //sb.Append(SRequestAllYearWise.Count);
                //sb.Append("</total>");
                //sb.Append("<page>");
                //sb.Append(page);
                //sb.Append("</page>");
                //sb.Append("<records>");
                //sb.Append(SRequestAllYearWise.Count);
                //sb.Append("</records>");
                //for (int i = 0; i < SRequestAllYearWise.Count; i++)
                //{
                //    int year = SRequestAllYearWise[i].Year;
                //    sb.Append("<row>");
                //    sb.Append("<" + "col_" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Year").ToString() + ">");

                //    sb.Append(year.ToString());

                //    sb.Append("</" + "col_" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Year").ToString() + ">");

                //    for (int j = 0; j < timeSlotList.Count; j++)
                //    {
                //        if (year == timeSlotList[j].Year)
                //        {
                //            sb.Append("<" + "col_" + timeSlotList[j].TempName.Replace(" ", "") + ">");

                //            sb.Append(timeSlotList[j].Count.ToString());

                //            sb.Append("</" + "col_" + timeSlotList[j].TempName.Replace(" ", "") + ">");
                //        }
                //    }
                //    sb.Append("</row>");
                //}
                //sb.Append("</rows>");
                //Response.Clear();
                //Response.ContentType = "text/xml";
                //Response.Write(sb.ToString());
                //Response.End();

            }
            catch (Exception ex)
            {
                if (LogException == 1) LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return new JsonResult(jsonobj);
        }

        #endregion
        #region BuildTimeSlotYearList vinay n 21/11/24
        private static List<TimeSlot> BuildTimeSlotYearList(int Company_ID, int year, string UserCulture, string connString)
        {
            string query = "SELECT Param_value FROM GNM_CompParam WHERE Company_ID = @Company_ID AND UPPER(Param_Name) = @Param_Name";
            List<SqlParameter> parameters = new List<SqlParameter>
            {
                new SqlParameter("@Company_ID", Company_ID),
                new SqlParameter("@Param_Name", "TIMESLOT")
            };
            string TimeSlot = GetValueFromDB<string>(query, parameters, connString);
            string[] TimeSlotArray = TimeSlot.Split(',');
            List<TimeSlot> timeSlotList = new List<TimeSlot>();
            //To create slots based on company parameter
            for (int i = 0; i < TimeSlotArray.Length; i++)
            {
                timeSlotList.Add(new TimeSlot { Name = TimeSlotArray[i], Count = 0, Hours = TimeSlotArray[i], Year = year });
            }
            timeSlotList.Add(new TimeSlot { Name = (Convert.ToInt32(TimeSlotArray[TimeSlotArray.Length - 1]) + 1).ToString(), Count = 0, Hours = (Convert.ToInt32(TimeSlotArray[TimeSlotArray.Length - 1]) + 1).ToString(), Year = year });

            //To Update the Slots name
            for (int i = 0; i < timeSlotList.Count; i++)
            {
                if (i == 0)
                {
                    timeSlotList[i].TempName = CommonFunctionalities.GetResourceString(UserCulture.ToString(), "LessThan").ToString() + " " + timeSlotList[i].Hours + " " + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "Hours").ToString();
                }
                else if (i == timeSlotList.Count - 1)
                {
                    timeSlotList[i].TempName = CommonFunctionalities.GetResourceString(UserCulture.ToString(), "GreaterThan").ToString() + " " + timeSlotList[i - 1].Hours + " " + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "Hours").ToString();
                }
                else
                {
                    timeSlotList[i].TempName = timeSlotList[i - 1].Hours + " " + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "To").ToString() + " " + timeSlotList[i].Hours + " " + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "Hours").ToString();
                }
            }
            //}
            return timeSlotList;
        }
        #endregion
        #region GetYearWiseBarChartData vinay n 21/11/24
        public static IActionResult GetYearWiseBarChartData(GetYearWiseBarChartDataList Obj, string connString, int LogException)
        {
            var jsonobj = default(dynamic);
            try
            {
                if (ResolutionWithTimeSlotYear != null)
                {
                    List<TimeSlot> timeSlotList = (List<TimeSlot>)ResolutionWithTimeSlotYear;
                    List<TimeSlot> timeSlotYear = new List<TimeSlot>();
                    for (int i = 0; i < timeSlotList.Count; i++)
                    {
                        if (timeSlotList[i].Year == Obj.Year)
                        {
                            timeSlotYear.Add(timeSlotList[i]);
                        }
                    }
                    jsonobj = new
                    {
                        timeSlotList = timeSlotYear
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1) LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return new JsonResult(jsonobj);
        }
        #endregion
        #region GetMonthData vinay n 21/11/24
        public static IActionResult GetMonthData(GetMonthDataList Obj, string connString, int LogException)
        {
            var jsonobj = default(dynamic);

            List<ServiceRequest> SRequestAll = new List<ServiceRequest>();
            List<ServiceRequest> SRequestAllMonthWise = new List<ServiceRequest>();


            try
            {
                string frmdate = Obj.FDate ?? "";
                string todate = Obj.TDate ?? "";
                string Query = string.Empty;
                string Query1 = string.Empty;
                string FilterCondition = string.Empty;

                string BranchNames = string.Empty;

                if (Obj.Branch != null && Obj.Branch != "")
                {
                    string decodedValue = Uri.UnescapeDataString(Obj.Branch);
                    string decodedValue2 = Uri.UnescapeDataString(decodedValue);
                    RTS_Branch branchs = JObject.Parse(Common.DecryptString(decodedValue2)).ToObject<RTS_Branch>();

                    for (int i = 0; i < branchs.Branchs.Count; i++)
                    {
                        BranchNames = BranchNames + branchs.Branchs[i].ID + ", ";
                    }
                    BranchNames = BranchNames.Remove(BranchNames.LastIndexOf(','), 1);
                }
                int CompanyID = 0;
                //start
                CompanyID = Convert.ToInt32(Obj.Company_ID);
                string fetchCompanyQuery = "SELECT TOP 1 * FROM GNM_Company WHERE Company_ID = @Company_ID";
                List<SqlParameter> companyParameters = new List<SqlParameter>
                    {
                        new SqlParameter("@Company_ID", CompanyID)
                    };
                GNM_Company companydetail = new GNM_Company();
                companydetail = GetValueFromDB<GNM_Company>(fetchCompanyQuery, companyParameters, connString);


                if (companydetail.Company_Type != "M")
                {
                    string fetchParentCompanyQuery = "SELECT TOP 1 * FROM GNM_Company WHERE Company_ID = @ParentCompany_ID";

                    // Define the parameter
                    List<SqlParameter> parentCompanyParameters = new List<SqlParameter>
                    {
                        new SqlParameter("@ParentCompany_ID", companydetail.Company_Parent_ID) // Use the parent ID from the previous result
                    };
                    companydetail = GetValueFromDB<GNM_Company>(fetchParentCompanyQuery, parentCompanyParameters, connString);
                    CompanyID = companydetail.Company_ID;
                }
                //end

                string Company_ID = RSTCompanyID.ToString();

                if (frmdate == string.Empty && todate == string.Empty)
                {
                    FilterCondition = "";
                    startDate = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "all").ToString();
                }
                endDate = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "all").ToString();

                if (frmdate != string.Empty && todate == string.Empty)
                {
                    DateTime FromDate = Convert.ToDateTime(frmdate);
                    FilterCondition = " AND CallClosureDateAndTime >='" + FromDate.ToString("dd-MMM-yyyy") + "'";
                    startDate = frmdate;
                    endDate = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "all").ToString();
                }

                if (frmdate == string.Empty && todate != string.Empty)
                {
                    DateTime ToDate = Convert.ToDateTime(todate).AddDays(1);
                    FilterCondition = " AND CallClosureDateAndTime <'" + ToDate.ToString("dd-MMM-yyyy") + "'";
                    startDate = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "all").ToString();
                    endDate = todate;
                }

                if (frmdate != string.Empty && todate != string.Empty)
                {
                    DateTime ToDate = Convert.ToDateTime(todate).AddDays(1);
                    DateTime FromDate = Convert.ToDateTime(frmdate);
                    FilterCondition = " AND CallClosureDateAndTime >='" + FromDate.ToString("dd-MMM-yyyy") + "' AND CallClosureDateAndTime <'" + ToDate.ToString("dd-MMM-yyyy") + "'";
                    startDate = frmdate;
                    endDate = todate;
                }

                FilterCondition = (BranchNames == "") ? FilterCondition : FilterCondition + " AND Branch_ID in (" + BranchNames + ")";

                FilterCondition = FilterCondition + " AND CallClosureDateAndTime IS NOT NULL";
                if (Obj.Type == 1)//Company Hours
                {
                    Query = "SELECT ServiceRequest_ID,CallDateAndTime, CallClosureDateAndTime,ResolutionTime FROM HD_ServiceRequest WHERE DATEPART(YEAR,CallClosureDateAndTime)=" + Obj.Year + " AND Company_ID in (" + Company_ID + ") " + FilterCondition;
                    Query1 = "SELECT distinct  DATEPART(MONTH,CallClosureDateAndTime)as Month FROM HD_ServiceRequest WHERE DATEPART(YEAR,CallClosureDateAndTime)=" + Obj.Year + " AND Company_ID in (" + Company_ID + ") " + FilterCondition;
                }
                else//24 Hours
                {
                    Query = "SELECT ServiceRequest_ID,CallDateAndTime, CallClosureDateAndTime, CONVERT(VARCHAR(50),DATEDIFF(HOUR,CallDateAndTime, CallClosureDateAndTime)) AS ResolutionTime FROM HD_ServiceRequest WHERE DATEPART(YEAR,CallClosureDateAndTime)=" + Obj.Year + " and Company_ID in (" + Company_ID + ") " + FilterCondition;
                    Query1 = "SELECT distinct  DATEPART(MONTH,CallClosureDateAndTime)as Month FROM HD_ServiceRequest WHERE DATEPART(YEAR,CallClosureDateAndTime)=" + Obj.Year + " AND Company_ID in (" + Company_ID + ") " + FilterCondition;

                }

                SRequestAll = GetValueFromDB<List<ServiceRequest>>(Query, null, connString);
                SRequestAllMonthWise = GetValueFromDB<List<ServiceRequest>>(Query1, null, connString);
                SRequestAllMonthWiseGlobal = SRequestAllMonthWise;

                List<TimeSlot> timeSlotList = new List<TimeSlot>();
                for (int j = 0; j < SRequestAllMonthWise.Count; j++)
                {
                    int month = SRequestAllMonthWise[j].Month;

                    List<TimeSlot> timeslotlist1 = null;
                    timeslotlist1 = BuildTimeSlotMonthList(CompanyID, month, Obj.UserCulture, connString);

                    foreach (var sr in SRequestAll)
                    {
                        int THour = 0;
                        if (Convert.ToDateTime(sr.CallClosureDateAndTime).Month == month)
                        {
                            if (Obj.Type == 1)//Company Hours
                            {
                                THour = Convert.ToInt32(sr.ResolutionTime.Split(':')[0]);
                            }
                            else//24 Hours
                            {
                                THour = Convert.ToInt32(sr.ResolutionTime);
                            }
                            timeslotlist1 = CalculateTimeSlot(timeslotlist1, THour);
                        }
                    }
                    for (int i = 0; i < timeslotlist1.Count; i++)
                    {
                        if (i == 0)
                        {
                            timeslotlist1[i].Name = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "LessThan").ToString() + " " + timeslotlist1[i].Hours + " " + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Hours").ToString();
                        }
                        else if (i == timeslotlist1.Count - 1)
                        {
                            timeslotlist1[i].Name = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "GreaterThan").ToString() + " " + timeslotlist1[i - 1].Hours + " " + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Hours").ToString();
                        }
                        else
                        {
                            timeslotlist1[i].Name = timeslotlist1[i - 1].Hours + " " + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "To").ToString() + " " + timeslotlist1[i].Hours + " " + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Hours").ToString();
                        }
                    }
                    if (timeSlotList == null)
                    {
                        timeSlotList = timeslotlist1;
                    }
                    else
                    {
                        timeSlotList.AddRange(timeslotlist1);
                    }
                }

                ResolutionWithTimeSlotMonth = timeSlotList;
                int page = 1;
                jsonobj = new
                {
                    rows = new object[SRequestAllMonthWise.Count],
                    page = page,
                    total = SRequestAllMonthWise.Count,
                    records = SRequestAllMonthWise.Count,
                    timeSlotList = timeSlotList.OrderBy(a => a.Month)
                };
                for (int i = 0; i < SRequestAllMonthWise.Count; i++)
                {
                    int month = SRequestAllMonthWise[i].Month;

                    // Create a dictionary to represent each row
                    var row = new Dictionary<string, object>
                    {
                        { "col_" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "month").ToString(),
                            AMMSCore.Utilities.Utilities.GetMonthName(month, Obj.UserCulture).ToString() }
                    };

                    for (int j = 0; j < timeSlotList.Count; j++)
                    {
                        if (month == timeSlotList[j].Month)
                        {
                            row.Add("col_" + timeSlotList[j].TempName.Replace(" ", ""), timeSlotList[j].Count);
                        }
                    }

                    jsonobj.rows[i] = row;
                }


                //System.Text.StringBuilder sb = new System.Text.StringBuilder();
                //sb.Append("<?xml version='1.0' encoding='utf-8'?>");
                //sb.Append("<rows>");
                //sb.Append("<total>");
                //sb.Append(SRequestAllMonthWise.Count);
                //sb.Append("</total>");
                //sb.Append("<page>");
                //sb.Append(page);
                //sb.Append("</page>");
                //sb.Append("<records>");
                //sb.Append(SRequestAllMonthWise.Count);
                //sb.Append("</records>");
                //for (int i = 0; i < SRequestAllMonthWise.Count; i++)
                //{
                //    int month = SRequestAllMonthWise[i].Month;
                //    sb.Append("<row>");
                //    sb.Append("<" + "col_" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "month").ToString() + ">");

                //    sb.Append(AMMSCore.Utilities.Utilities.GetMonthName(month, Obj.UserCulture).ToString());

                //    sb.Append("</" + "col_" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "month").ToString() + ">");

                //    for (int j = 0; j < timeSlotList.Count; j++)
                //    {
                //        if (month == timeSlotList[j].Month)
                //        {
                //            sb.Append("<" + "col_" + timeSlotList[j].TempName.Replace(" ", "") + ">");

                //            sb.Append(timeSlotList[j].Count.ToString());

                //            sb.Append("</" + "col_" + timeSlotList[j].TempName.Replace(" ", "") + ">");
                //        }
                //    }
                //    sb.Append("</row>");
                //}
                //sb.Append("</rows>");
                //Response.Clear();
                //Response.ContentType = "text/xml";
                //Response.Write(sb.ToString());
                //Response.End();

            }
            catch (Exception ex)
            {
                if (LogException == 1) LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return new JsonResult(jsonobj);
        }
        #endregion
        #region BuildTimeSlotMonthList vinay n 21/11/24
        private static List<TimeSlot> BuildTimeSlotMonthList(int Company_ID, int month, string UserCulture, string connString)
        {
            string timeSlotQuery = @"
                SELECT Param_value 
                FROM GNM_CompParam 
                WHERE Company_ID = @Company_ID 
                  AND UPPER(Param_Name) = 'TIMESLOT'";
            var sqlParameters = new List<SqlParameter>
            {
                new SqlParameter("@Company_ID", Company_ID)
            };

            string TimeSlot = GetValueFromDB<string>(
                    timeSlotQuery,
                    sqlParameters,
                    connString
                );
            string[] TimeSlotArray = TimeSlot.Split(',');
            List<TimeSlot> timeSlotList = new List<TimeSlot>();

            //To create slots based on company parameter
            for (int i = 0; i < TimeSlotArray.Length; i++)
            {
                timeSlotList.Add(new TimeSlot { Name = TimeSlotArray[i], Count = 0, Hours = TimeSlotArray[i], Month = month });
            }
            timeSlotList.Add(new TimeSlot { Name = (Convert.ToInt32(TimeSlotArray[TimeSlotArray.Length - 1]) + 1).ToString(), Count = 0, Hours = (Convert.ToInt32(TimeSlotArray[TimeSlotArray.Length - 1]) + 1).ToString(), Month = month });

            //To Update the Slots name
            for (int i = 0; i < timeSlotList.Count; i++)
            {
                if (i == 0)
                {
                    timeSlotList[i].TempName = CommonFunctionalities.GetResourceString(UserCulture.ToString(), "LessThan").ToString() + " " + timeSlotList[i].Hours + " " + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "Hours").ToString();
                }
                else if (i == timeSlotList.Count - 1)
                {
                    timeSlotList[i].TempName = CommonFunctionalities.GetResourceString(UserCulture.ToString(), "GreaterThan").ToString() + " " + timeSlotList[i - 1].Hours + " " + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "Hours").ToString();
                }
                else
                {
                    timeSlotList[i].TempName = timeSlotList[i - 1].Hours + " " + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "To").ToString() + " " + timeSlotList[i].Hours + " " + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "Hours").ToString();
                }
            }
            //}
            return timeSlotList;
        }
        #endregion
        #region GetDatewiseData vinay n 21/11/24
        public static IActionResult GetDatewiseData(GetDatewiseDataList Obj, string connString, int LogException)
        {
            var jsonobj = default(dynamic);
            //int CompanyID = Convert.ToInt32(Session["Company_ID"]);
            List<ServiceRequest> SRequestAll = new List<ServiceRequest>();
            List<ServiceRequest> SRequestAllDateWise = new List<ServiceRequest>();
            string MonthName = Obj.MonthName;

            string FDate = Obj.FDate ?? "";
            string TDate = Obj.TDate ?? "";
            int Type = Obj.Type;
            int Year = Obj.Year;
            int page = Obj.page;
            int rows = Obj.rows;

            int Month = 0;
            if (MonthName.ToLower() == "jan".ToLower()) { Month = 1; }
            else if (MonthName.ToLower() == "feb".ToLower()) { Month = 2; }
            else if (MonthName.ToLower() == "mar".ToLower()) { Month = 3; }
            else if (MonthName.ToLower() == "apr".ToLower()) { Month = 4; }
            else if (MonthName.ToLower() == "may".ToLower()) { Month = 5; }
            else if (MonthName.ToLower() == "jun".ToLower()) { Month = 6; }
            else if (MonthName.ToLower() == "jul".ToLower()) { Month = 7; }
            else if (MonthName.ToLower() == "aug".ToLower()) { Month = 8; }
            else if (MonthName.ToLower() == "sep".ToLower()) { Month = 9; }
            else if (MonthName.ToLower() == "oct".ToLower()) { Month = 10; }
            else if (MonthName.ToLower() == "nov".ToLower()) { Month = 11; }
            else if (MonthName.ToLower() == "dec".ToLower()) { Month = 12; }
            try
            {
                string frmdate = FDate;
                string todate = TDate;
                string Query = string.Empty;
                string Query1 = string.Empty;
                string FilterCondition = string.Empty;
                //FilterCondition = " AND CallClosureDateAndTime IS NOT NULL";

                string BranchNames = string.Empty;

                if (Obj.Branch != null && Obj.Branch != "")
                {
                    string decodedValue = Uri.UnescapeDataString(Obj.Branch);
                    string decodedValue2 = Uri.UnescapeDataString(decodedValue);
                    RTS_Branch branchs = JObject.Parse(Common.DecryptString(decodedValue2)).ToObject<RTS_Branch>();

                    for (int i = 0; i < branchs.Branchs.Count; i++)
                    {
                        BranchNames = BranchNames + branchs.Branchs[i].ID + ", ";
                    }
                    BranchNames = BranchNames.Remove(BranchNames.LastIndexOf(','), 1);
                }
                int CompanyID = 0;
                //start
                CompanyID = Convert.ToInt32(Obj.Company_ID);
                string fetchCompanyDetailsQuery = @"
                SELECT TOP 1 * 
                FROM GNM_Company 
                WHERE Company_ID = @Company_ID";
                var fetchCompanyDetailsParams = new List<SqlParameter>
                {
                    new SqlParameter("@Company_ID", CompanyID)
                };
                GNM_Company companydetail = new GNM_Company();
                companydetail = GetValueFromDB<GNM_Company>(
                    fetchCompanyDetailsQuery,
                    fetchCompanyDetailsParams,
                    connString
                );


                if (companydetail.Company_Type != "M")
                {
                    string fetchParentCompanyDetailsQuery = @"
                    SELECT TOP 1 * 
                    FROM GNM_Company 
                    WHERE Company_ID = @ParentCompanyID";
                    var fetchParentCompanyParams = new List<SqlParameter>
                    {
                        new SqlParameter("@ParentCompanyID", companydetail.Company_Parent_ID)
                    };
                    companydetail = GetValueFromDB<GNM_Company>(
                        fetchParentCompanyDetailsQuery,
                        fetchParentCompanyParams,
                        connString
                    );
                    CompanyID = companydetail.Company_ID;
                }
                //end
                //CompanyID = Convert.ToInt32(Session["RSTCompanyID"].ToString());
                string Company_ID = RSTCompanyID.ToString();
                if (frmdate == string.Empty && todate == string.Empty)
                {
                    FilterCondition = "";
                    startDate = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "all").ToString();
                    endDate = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "all").ToString();
                }

                if (frmdate != string.Empty && todate == string.Empty)
                {
                    DateTime FromDate = Convert.ToDateTime(frmdate);
                    FilterCondition = " AND CallClosureDateAndTime >='" + FromDate.ToString("dd-MMM-yyyy") + "'";
                    startDate = frmdate;
                    endDate = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "all").ToString();
                }

                if (frmdate == string.Empty && todate != string.Empty)
                {
                    DateTime ToDate = Convert.ToDateTime(todate).AddDays(1);
                    FilterCondition = " AND CallClosureDateAndTime <'" + ToDate.ToString("dd-MMM-yyyy") + "'";
                    startDate = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "all").ToString();
                    endDate = todate;
                }

                if (frmdate != string.Empty && todate != string.Empty)
                {
                    DateTime ToDate = Convert.ToDateTime(todate).AddDays(1);
                    DateTime FromDate = Convert.ToDateTime(frmdate);
                    FilterCondition = " AND CallClosureDateAndTime >='" + FromDate.ToString("dd-MMM-yyyy") + "' AND CallClosureDateAndTime <'" + ToDate.ToString("dd-MMM-yyyy") + "'";
                    startDate = frmdate;
                    endDate = todate;
                }

                FilterCondition = (BranchNames == "") ? FilterCondition : FilterCondition + " AND Branch_ID in (" + BranchNames + ")";

                FilterCondition = FilterCondition + " AND CallClosureDateAndTime IS NOT NULL";
                if (Type == 1)//Company Hours
                {
                    Query = "SELECT ServiceRequest_ID,CallDateAndTime, CallClosureDateAndTime,ResolutionTime FROM HD_ServiceRequest WHERE DATEPART(YEAR,CallClosureDateAndTime)=" + Year + " AND DATEPART(MONTH,CallClosureDateAndTime)=" + Month + " AND Company_ID in (" + Company_ID + ") " + FilterCondition;
                    Query1 = "SELECT distinct  CONVERT(date,CallClosureDateAndTime) as CallClosureDateAndTime FROM HD_ServiceRequest WHERE DATEPART(YEAR,CallClosureDateAndTime)=" + Year + " AND DATEPART(MONTH,CallClosureDateAndTime)=" + Month + " AND Company_ID in (" + Company_ID + ") " + FilterCondition;
                }
                else//24 Hours
                {
                    Query = "SELECT ServiceRequest_ID,CallDateAndTime, CallClosureDateAndTime, CONVERT(VARCHAR(50),DATEDIFF(HOUR,CallDateAndTime, CallClosureDateAndTime)) AS ResolutionTime FROM HD_ServiceRequest WHERE DATEPART(YEAR,CallClosureDateAndTime)=" + Year + " AND DATEPART(MONTH,CallClosureDateAndTime)=" + Month + " and Company_ID in (" + Company_ID + ") " + FilterCondition;
                    Query1 = "SELECT distinct  CONVERT(date,CallClosureDateAndTime) as CallClosureDateAndTime FROM HD_ServiceRequest WHERE DATEPART(YEAR,CallClosureDateAndTime)=" + Year + " AND DATEPART(MONTH,CallClosureDateAndTime)=" + Month + " AND Company_ID in (" + Company_ID + ") " + FilterCondition;

                }

                SRequestAll = GetValueFromDB<List<ServiceRequest>>(
                    Query,
                    null,
                    connString
                );
                SRequestAllDateWise = GetValueFromDB<List<ServiceRequest>>(
                    Query1,
                    null,
                    connString
                );
                SRequestAllDateWiseVar = SRequestAllDateWise;

                List<TimeSlot> timeSlotList = null;
                for (int j = 0; j < SRequestAllDateWise.Count; j++)
                {
                    DateTime closuredate = Convert.ToDateTime(SRequestAllDateWise[j].CallClosureDateAndTime).Date;

                    List<TimeSlot> timeslotlist1 = null;
                    timeslotlist1 = BuildTimeSlotDateList(CompanyID, closuredate, Obj.UserCulture, connString);

                    foreach (var sr in SRequestAll)
                    {
                        int THour = 0;
                        if (Convert.ToDateTime(sr.CallClosureDateAndTime).Date == closuredate)
                        {
                            if (Type == 1)//Company Hours
                            {
                                THour = Convert.ToInt32(sr.ResolutionTime.Split(':')[0]);
                            }
                            else//24 Hours
                            {
                                THour = Convert.ToInt32(sr.ResolutionTime);
                            }
                            timeslotlist1 = CalculateTimeSlot(timeslotlist1, THour);
                        }
                    }
                    for (int i = 0; i < timeslotlist1.Count; i++)
                    {
                        if (i == 0)
                        {
                            timeslotlist1[i].Name = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "LessThan").ToString() + " " + timeslotlist1[i].Hours + " " + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Hours").ToString();
                        }
                        else if (i == timeslotlist1.Count - 1)
                        {
                            timeslotlist1[i].Name = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "GreaterThan").ToString() + " " + timeslotlist1[i - 1].Hours + " " + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Hours").ToString();
                        }
                        else
                        {
                            timeslotlist1[i].Name = timeslotlist1[i - 1].Hours + " " + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "To").ToString() + " " + timeslotlist1[i].Hours + " " + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Hours").ToString();
                        }
                    }
                    if (timeSlotList == null)
                    {
                        timeSlotList = timeslotlist1;
                    }
                    else
                    {
                        timeSlotList.AddRange(timeslotlist1);
                    }
                }

                ResolutionWithTimeSlotDate = timeSlotList;
                //int page = 1;
                List<object> _rows = new List<object>();

                // Create the response structure
                jsonobj = new
                {
                    total = SRequestAllDateWise.Count,
                    page = page,
                    records = SRequestAllDateWise.Count,
                    rows = _rows
                };
                for (int i = 0; i < SRequestAllDateWise.Count; i++)
                {
                    DateTime date = Convert.ToDateTime(SRequestAllDateWise[i].CallClosureDateAndTime);
                    var row = new Dictionary<string, object>();

                    // Adding the Date column with localized name
                    row["col_" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Date").ToString()] = date.ToString("dd-MMM-yyyy");

                    // Iterate through timeSlotList to add additional columns
                    foreach (var timeSlot in timeSlotList)
                    {
                        if (date == timeSlot.Date)
                        {
                            row["col_" + timeSlot.TempName.Replace(" ", "")] = timeSlot.Count.ToString();
                        }
                    }

                    // Add the row to the rows list
                    _rows.Add(row);
                }
                //System.Text.StringBuilder sb = new System.Text.StringBuilder();
                //sb.Append("<?xml version='1.0' encoding='utf-8'?>");
                //sb.Append("<rows>");
                //sb.Append("<total>");
                //sb.Append(SRequestAllDateWise.Count);
                //sb.Append("</total>");
                //sb.Append("<page>");
                //sb.Append(page);
                //sb.Append("</page>");
                //sb.Append("<records>");
                //sb.Append(SRequestAllDateWise.Count);
                //sb.Append("</records>");
                //for (int i = 0; i < SRequestAllDateWise.Count; i++)
                //{
                //    DateTime date = Convert.ToDateTime(SRequestAllDateWise[i].CallClosureDateAndTime);
                //    sb.Append("<row>");
                //    sb.Append("<" + "col_" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Date").ToString() + ">");

                //    sb.Append(date.ToString("dd-MMM-yyyy"));

                //    sb.Append("</" + "col_" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Date").ToString() + ">");

                //    for (int j = 0; j < timeSlotList.Count; j++)
                //    {
                //        if (date == timeSlotList[j].Date)
                //        {
                //            sb.Append("<" + "col_" + timeSlotList[j].TempName.Replace(" ", "") + ">");

                //            sb.Append(timeSlotList[j].Count.ToString());

                //            sb.Append("</" + "col_" + timeSlotList[j].TempName.Replace(" ", "") + ">");
                //        }
                //    }
                //    sb.Append("</row>");
                //}
                //sb.Append("</rows>");
                //Response.Clear();
                //Response.ContentType = "text/xml";
                //Response.Write(sb.ToString());
                //Response.End();
                //int Count = SRequestAllDateWise.Count();
                //int Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                //jsonobj = new
                //{
                //    total = Total,
                //    page = page,
                //    records = Count,
                //    timeSlotList = timeSlotList.Paginate(page, rows).OrderBy(a => a.Date)
                //};
            }
            catch (Exception ex)
            {
                if (LogException == 1) LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return new JsonResult(jsonobj);
        }
        #endregion
        #region BuildTimeSlotDateList vinay n 21/11/24

        private static List<TimeSlot> BuildTimeSlotDateList(int Company_ID, DateTime ClosureDate, string UserCulture, string connString)
        {
            string fetchTimeSlotQuery = @"
                SELECT Param_value 
                FROM GNM_CompParam 
                WHERE Company_ID = @Company_ID 
                AND UPPER(Param_Name) = 'TIMESLOT'";
            var parameters = new List<SqlParameter>
                {
                    new SqlParameter("@Company_ID", SqlDbType.Int) { Value = Company_ID }
                };

            string TimeSlot = GetValueFromDB<string>(fetchTimeSlotQuery, parameters, connString);
            string[] TimeSlotArray = TimeSlot.Split(',');
            List<TimeSlot> timeSlotList = new List<TimeSlot>();

            //To create slots based on company parameter
            for (int i = 0; i < TimeSlotArray.Length; i++)
            {
                timeSlotList.Add(new TimeSlot { Name = TimeSlotArray[i], Count = 0, Hours = TimeSlotArray[i], Date = ClosureDate });
            }
            timeSlotList.Add(new TimeSlot { Name = (Convert.ToInt32(TimeSlotArray[TimeSlotArray.Length - 1]) + 1).ToString(), Count = 0, Hours = (Convert.ToInt32(TimeSlotArray[TimeSlotArray.Length - 1]) + 1).ToString(), Date = ClosureDate });

            //To Update the Slots name
            for (int i = 0; i < timeSlotList.Count; i++)
            {
                if (i == 0)
                {
                    timeSlotList[i].TempName = CommonFunctionalities.GetResourceString(UserCulture.ToString(), "LessThan").ToString() + " " + timeSlotList[i].Hours + " " + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "Hours").ToString();
                }
                else if (i == timeSlotList.Count - 1)
                {
                    timeSlotList[i].TempName = CommonFunctionalities.GetResourceString(UserCulture.ToString(), "GreaterThan").ToString() + " " + timeSlotList[i - 1].Hours + " " + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "Hours").ToString();
                }
                else
                {
                    timeSlotList[i].TempName = timeSlotList[i - 1].Hours + " " + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "To").ToString() + " " + timeSlotList[i].Hours + " " + CommonFunctionalities.GetResourceString(UserCulture.ToString(), "Hours").ToString();
                }
            }
            //}
            return timeSlotList;
        }
        #endregion
        #region GetMonthWiseBarChartData vinay n 21/11/24
        public static IActionResult GetMonthWiseBarChartData(GetMonthWiseBarChartDataList Obj, string connString, int LogException)
        {
            var jsonobj = default(dynamic);
            string MonthName = Obj.MonthName;
            int Month = 0;
            if (MonthName.ToLower() == "jan".ToLower()) { Month = 1; }
            else if (MonthName.ToLower() == "feb".ToLower()) { Month = 2; }
            else if (MonthName.ToLower() == "mar".ToLower()) { Month = 3; }
            else if (MonthName.ToLower() == "apr".ToLower()) { Month = 4; }
            else if (MonthName.ToLower() == "may".ToLower()) { Month = 5; }
            else if (MonthName.ToLower() == "jun".ToLower()) { Month = 6; }
            else if (MonthName.ToLower() == "jul".ToLower()) { Month = 7; }
            else if (MonthName.ToLower() == "aug".ToLower()) { Month = 8; }
            else if (MonthName.ToLower() == "sep".ToLower()) { Month = 9; }
            else if (MonthName.ToLower() == "oct".ToLower()) { Month = 10; }
            else if (MonthName.ToLower() == "nov".ToLower()) { Month = 11; }
            else if (MonthName.ToLower() == "dec".ToLower()) { Month = 12; }
            try
            {
                if (ResolutionWithTimeSlotMonth != null)
                {
                    List<TimeSlot> timeSlotList = (List<TimeSlot>)ResolutionWithTimeSlotMonth;
                    List<TimeSlot> timeSlotMonth = new List<TimeSlot>();
                    for (int i = 0; i < timeSlotList.Count; i++)
                    {
                        if (timeSlotList[i].Month == Month)
                        {
                            timeSlotMonth.Add(timeSlotList[i]);
                        }
                    }
                    jsonobj = new
                    {
                        timeSlotList = timeSlotMonth
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1) LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return new JsonResult(jsonobj);
        }

        #endregion
        #region GetDateWiseBarChartData vinay n 21/11/24
        public static IActionResult GetDateWiseBarChartData(GetDateWiseBarChartDataList Obj, int LogException)
        {
            var jsonobj = default(dynamic);
            try
            {
                if (ResolutionWithTimeSlotDate != null)
                {
                    List<TimeSlot> timeSlotList = (List<TimeSlot>)ResolutionWithTimeSlotDate;
                    List<TimeSlot> timeSlotMonth = new List<TimeSlot>();
                    for (int i = 0; i < timeSlotList.Count; i++)
                    {
                        if (timeSlotList[i].Date.Date == Obj.date)
                        {
                            timeSlotMonth.Add(timeSlotList[i]);
                        }
                    }
                    jsonobj = new
                    {
                        timeSlotList = timeSlotMonth
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1) LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return new JsonResult(jsonobj);
        }
        #endregion
        #region GetDateWiseBarChartData vinay n 21/11/24
        public static async Task<object> Export(ExportHelpDesk_CR_ResolutionTimeWithTimeSlotsList Obj, string connString, int LogException)
        {
            try
            {


                int userID = Obj.User_ID;
                int companyID = Convert.ToInt32(Obj.Company_ID);
                int branchID = Convert.ToInt32(Obj.Branch);
                DataTable dtOptions = new DataTable();
                dtOptions.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Branch").ToString());
                dtOptions.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "fromdate").ToString());
                dtOptions.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "todate").ToString());
                dtOptions.Rows.Add(BranchName.ToString(), startDate.ToString(), endDate.ToString());

                DataTable dtData = new DataTable();
                DataTable DtAlignment = new DataTable();

                List<TimeSlot> timeSlotList = (List<TimeSlot>)ResolutionWithTimeSlot;
                int Count = timeSlotList.Count;
                object[] Alignment = new object[Count];
                object[] Value = new object[Count];
                for (int i = 0; i < Count; i++)
                {
                    dtData.Columns.Add(timeSlotList[i].Name);
                    DtAlignment.Columns.Add(timeSlotList[i].Name);
                    Alignment[i] = 2;
                    Value[i] = timeSlotList[i].Count;
                }

                DtAlignment.Rows.Add(Alignment);
                dtData.Rows.Add(Value);
                ReportExportList reportExportList = new ReportExportList
                {
                    Company_ID = Obj.Company_ID, // Assuming this is available in ExportObj
                    Branch = Obj.Branch_ID.ToString(),
                    GeneralLanguageID = Obj.LanguageID,
                    UserLanguageID = Obj.UserLanguageID,
                    Options = dtOptions,
                    dt = dtData,
                    Alignment = DtAlignment,
                    FileName = "Resolutiontimewithtimeslots", // Set a default or dynamic filename
                    Header = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Resolutiontimewithtimeslots").ToString(), // Set a default or dynamic header
                    exprtType = Obj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                    UserCulture = Obj.UserCulture
                };
                var result = await ReportExport.Export(reportExportList, connString, LogException);
                return result.Value;


                // gbl.InsertGPSDetails(Convert.ToInt32(Obj.Company_ID.ToString()), branchID, Obj.User_ID, Common.GetObjectID("HelpDesk_CR_ResolutionTimeWithTimeSlots"), 0, 0, 0, "Resolution Time With Time Slots-Export ", false, Convert.ToInt32(Obj.MenuID), Convert.ToDateTime(Obj.LoggedINDateTime));
            }

            catch (Exception ex)
            {
                if (LogException == 1) LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return null;
        }
        #endregion
        #region LoadYearGrid vinay n 21/11/24
        public static void LoadYearGrid(LoadYearGridList Obj, string sidx, string sord, int page, int rows)
        {
            try
            {
                if (ResolutionWithTimeSlotYear != null)
                {
                    List<TimeSlot> timeSlotList = (List<TimeSlot>)ResolutionWithTimeSlotYear;
                    List<ServiceRequest> SRequestAllYearWise = (List<ServiceRequest>)SRequestAllYearWiseGlobal;
                    System.Text.StringBuilder sb = new System.Text.StringBuilder();
                    sb.Append("<?xml version='1.0' encoding='utf-8'?>");
                    sb.Append("<rows>");
                    sb.Append("<total>");
                    sb.Append(SRequestAllYearWise.Count);
                    sb.Append("</total>");
                    sb.Append("<page>");
                    sb.Append(page);
                    sb.Append("</page>");
                    sb.Append("<records>");
                    sb.Append(SRequestAllYearWise.Count);
                    sb.Append("</records>");
                    for (int i = 0; i < SRequestAllYearWise.Count; i++)
                    {
                        int year = SRequestAllYearWise[i].Year;
                        sb.Append("<row>");
                        sb.Append("<" + "col_" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Year").ToString() + ">");

                        sb.Append(year.ToString());

                        sb.Append("</" + "col_" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Year").ToString() + ">");

                        for (int j = 0; j < timeSlotList.Count; j++)
                        {
                            if (year == timeSlotList[j].Year)
                            {
                                sb.Append("<" + "col_" + timeSlotList[j].TempName.Replace(" ", "") + ">");

                                sb.Append(timeSlotList[j].Count.ToString());

                                sb.Append("</" + "col_" + timeSlotList[j].TempName.Replace(" ", "") + ">");
                            }
                        }
                        sb.Append("</row>");
                    }
                    sb.Append("</rows>");
                    //Response.Clear();
                    //Response.ContentType = "text/xml";
                    //Response.Write(sb.ToString());
                    //Response.End();
                }
            }
            catch (Exception ex) { }
        }
        #endregion
        #region LoadMonthGrid vinay n 21/11/24
        public static void LoadMonthGrid(LoadMonthGridList Obj, string sidx, string sord, int page, int rows)
        {
            try
            {
                if (ResolutionWithTimeSlotMonth != null)
                {
                    List<TimeSlot> timeSlotList = (List<TimeSlot>)ResolutionWithTimeSlotMonth;
                    List<ServiceRequest> SRequestAllMonthWise = (List<ServiceRequest>)SRequestAllMonthWiseGlobal;
                    System.Text.StringBuilder sb = new System.Text.StringBuilder();
                    sb.Append("<?xml version='1.0' encoding='utf-8'?>");
                    sb.Append("<rows>");
                    sb.Append("<total>");
                    sb.Append(SRequestAllMonthWise.Count);
                    sb.Append("</total>");
                    sb.Append("<page>");
                    sb.Append(page);
                    sb.Append("</page>");
                    sb.Append("<records>");
                    sb.Append(SRequestAllMonthWise.Count);
                    sb.Append("</records>");
                    for (int i = 0; i < SRequestAllMonthWise.Count; i++)
                    {
                        int month = SRequestAllMonthWise[i].Month;
                        sb.Append("<row>");
                        sb.Append("<" + "col_" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "month").ToString() + ">");

                        sb.Append(AMMSCore.Utilities.Utilities.GetMonthName(month, Obj.UserCulture).ToString());

                        sb.Append("</" + "col_" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "month").ToString() + ">");

                        for (int j = 0; j < timeSlotList.Count; j++)
                        {
                            if (month == timeSlotList[j].Month)
                            {
                                sb.Append("<" + "col_" + timeSlotList[j].TempName.Replace(" ", "") + ">");

                                sb.Append(timeSlotList[j].Count.ToString());

                                sb.Append("</" + "col_" + timeSlotList[j].TempName.Replace(" ", "") + ">");
                            }
                        }
                        sb.Append("</row>");
                    }
                    sb.Append("</rows>");
                    //Response.Clear();
                    //Response.ContentType = "text/xml";
                    //Response.Write(sb.ToString());
                    //Response.End();
                }
            }
            catch (Exception ex) { }
        }
        #endregion
        #region LoadGrid vinay n 21/11/24
        public static IActionResult LoadGrid(string sidx, string sord, int page, int rows)
        {
            try
            {
                if (ResolutionWithTimeSlot != null)
                {
                    List<TimeSlot> timeSlotList = (List<TimeSlot>)ResolutionWithTimeSlot;
                    var rowData = new Dictionary<string, object>();
                    foreach (var timeSlot in timeSlotList)
                    {
                        string columnName = "col_" + timeSlot.TempName.Replace(" ", "");
                        rowData[columnName] = timeSlot.Count;
                    }
                    var jsonResponse = new
                    {
                        total = 1, // Total number of pages
                        page = page, // Current page
                        records = 1, // Total number of records
                        rows = new[]
                          {
                                rowData // Each row's data
                            }
                    };

                    //System.Text.StringBuilder sb = new System.Text.StringBuilder();
                    //sb.Append("<?xml version='1.0' encoding='utf-8'?>");
                    //sb.Append("<rows>");
                    //sb.Append("<total>");
                    //sb.Append(1);
                    //sb.Append("</total>");
                    //sb.Append("<page>");
                    //sb.Append(page);
                    //sb.Append("</page>");
                    //sb.Append("<records>");
                    //sb.Append(1);
                    //sb.Append("</records>");
                    //sb.Append("<row>");
                    //for (int j = 0; j < timeSlotList.Count; j++)
                    //{
                    //    sb.Append("<" + "col_" + timeSlotList[j].TempName.Replace(" ", "") + ">");

                    //    sb.Append(timeSlotList[j].Count.ToString());

                    //    sb.Append("</" + "col_" + timeSlotList[j].TempName.Replace(" ", "") + ">");
                    //}
                    //sb.Append("</row>");
                    //sb.Append("</rows>");
                    return new JsonResult(jsonResponse);
                }
            }
            catch (Exception ex) { }
            return null;
        }
        #endregion
        #region GetTimeSlots vinay n 21/11/24
        public static IActionResult GetTimeSlots(GetTimeSlotsList Obj, string connString)
        {
            var JsonResult = default(dynamic);
            try
            {
                int Company_ID = Convert.ToInt32(Obj.Company_ID);
                List<TimeSlot> timeSlotList = BuildTimeSlotList(Company_ID, Obj.UserCulture, connString);
                //To generate Grid Colname & ColModel
                List<string> colNames = timeSlotList.Select(S => S.TempName).ToList();
                List<JQGridColModel> colModel = new List<JQGridColModel>();
                for (int i = 0; i < timeSlotList.Count; i++)
                {
                    string Name = string.Empty;
                    timeSlotList[i].TempName = timeSlotList[i].TempName.Replace(" ", "");
                    colModel.Add(new JQGridColModel() { name = "col_" + timeSlotList[i].TempName, index = "col_" + timeSlotList[i].TempName, width = 200, align = "center" });
                }

                JsonResult = new
                {
                    timeSlotList,
                    colNames,
                    colModel
                };

            }
            catch (Exception ex)
            {

            }
            return new JsonResult(JsonResult);
        }
        #endregion
        #region GetTimeSlotsYearWise vinay n 21/11/24
        public static IActionResult GetTimeSlotsYearWise(GetTimeSlotsYearWiseList Obj, string connString)
        {
            var JsonResult = default(dynamic);
            try
            {
                int Company_ID = Convert.ToInt32(Obj.Company_ID);
                List<TimeSlot> timeSlotList = BuildTimeSlotList(Company_ID, Obj.UserCulture, connString);
                //To generate Grid Colname & ColModel
                List<string> colNames = new List<string>();
                colNames.Add("Year");
                colNames.AddRange(timeSlotList.Select(S => S.TempName).ToList());
                List<JQGridColModel> colModel = new List<JQGridColModel>();
                for (int i = 0; i < timeSlotList.Count; i++)
                {
                    string Name = string.Empty;
                    if (i == 0)
                    {
                        colModel.Add(new JQGridColModel() { name = "col_" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Year").ToString(), index = "col_" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Year").ToString(), width = 150, align = "center" });
                        timeSlotList[i].TempName = timeSlotList[i].TempName.Replace(" ", "");
                        colModel.Add(new JQGridColModel() { name = "col_" + timeSlotList[i].TempName, index = "col_" + timeSlotList[i].TempName, width = 190, align = "center" });
                    }
                    else
                    {
                        timeSlotList[i].TempName = timeSlotList[i].TempName.Replace(" ", "");
                        colModel.Add(new JQGridColModel() { name = "col_" + timeSlotList[i].TempName, index = "col_" + timeSlotList[i].TempName, width = 190, align = "center" });
                    }
                }

                JsonResult = new
                {
                    timeSlotList,
                    colNames,
                    colModel
                };

            }
            catch (Exception ex)
            {

            }
            return new JsonResult(JsonResult);
        }
        #endregion
        #region GetTimeSlotsMonthWise vinay n 21/11/24
        public static IActionResult GetTimeSlotsMonthWise(GetTimeSlotsMonthWiseList Obj, string connString)
        {
            var JsonResult = default(dynamic);
            try
            {
                int Company_ID = Convert.ToInt32(Obj.Company_ID);
                List<TimeSlot> timeSlotList = BuildTimeSlotList(Company_ID, Obj.UserCulture, connString);
                //To generate Grid Colname & ColModel
                List<string> colNames = new List<string>();
                colNames.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "month").ToString());
                colNames.AddRange(timeSlotList.Select(S => S.TempName).ToList());
                List<JQGridColModel> colModel = new List<JQGridColModel>();
                for (int i = 0; i < timeSlotList.Count; i++)
                {
                    string Name = string.Empty;
                    if (i == 0)
                    {
                        colModel.Add(new JQGridColModel() { name = "col_" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "month").ToString(), index = "col_" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "month").ToString(), sortable = false, width = 150, align = "center" });
                        timeSlotList[i].TempName = timeSlotList[i].TempName.Replace(" ", "");
                        colModel.Add(new JQGridColModel() { name = "col_" + timeSlotList[i].TempName, index = "col_" + timeSlotList[i].TempName, sortable = false, width = 180, align = "center" });
                    }
                    else
                    {
                        timeSlotList[i].TempName = timeSlotList[i].TempName.Replace(" ", "");
                        colModel.Add(new JQGridColModel() { name = "col_" + timeSlotList[i].TempName, index = "col_" + timeSlotList[i].TempName, sortable = false, width = 180, align = "center" });
                    }
                }

                JsonResult = new
                {
                    timeSlotList,
                    colNames,
                    colModel
                };

            }
            catch (Exception ex)
            {

            }
            return new JsonResult(JsonResult);
        }
        #endregion
        #region GetTimeSlotsDateWise vinay n 21/11/24
        public static IActionResult GetTimeSlotsDateWise(GetTimeSlotsDateWiseList Obj, string connString)
        {
            var JsonResult = default(dynamic);
            try
            {
                int Company_ID = Convert.ToInt32(Obj.Company_ID);
                List<TimeSlot> timeSlotList = BuildTimeSlotList(Company_ID, Obj.UserCulture, connString);
                //To generate Grid Colname & ColModel
                List<string> colNames = new List<string>();
                colNames.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Date").ToString());
                colNames.AddRange(timeSlotList.Select(S => S.TempName).ToList());
                List<JQGridColModel> colModel = new List<JQGridColModel>();
                for (int i = 0; i < timeSlotList.Count; i++)
                {
                    string Name = string.Empty;
                    if (i == 0)
                    {
                        colModel.Add(new JQGridColModel() { name = "col_" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Date").ToString(), index = "col_" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Date").ToString(), sortable = false, width = 150, align = "center" });
                        timeSlotList[i].TempName = timeSlotList[i].TempName.Replace(" ", "");
                        colModel.Add(new JQGridColModel() { name = "col_" + timeSlotList[i].TempName, index = "col_" + timeSlotList[i].TempName, sortable = false, width = 170, align = "center" });
                    }
                    else
                    {
                        timeSlotList[i].TempName = timeSlotList[i].TempName.Replace(" ", "");
                        colModel.Add(new JQGridColModel() { name = "col_" + timeSlotList[i].TempName, index = "col_" + timeSlotList[i].TempName, sortable = false, width = 170, align = "center" });
                    }
                }

                JsonResult = new
                {
                    timeSlotList,
                    colNames,
                    colModel
                };

            }
            catch (Exception ex)
            {

            }
            return new JsonResult(JsonResult);
        }
        #endregion
    }
    #region HelpDesk_CR_ResolutionTimeWithTimeSlotsListsAndObjs
    public class GetDateWiseBarChartDataList
    {
        public DateTime date { get; set; }
    }


    public class GetTimeSlotsMonthWiseList
    {
        public int Company_ID { get; set; }
        public string UserCulture { get; set; }
    }
    public class GetTimeSlotsDateWiseList
    {
        public int Company_ID { get; set; }
        public string UserCulture { get; set; }
    }
    public class GetTimeSlotsYearWiseList
    {
        public int Company_ID { get; set; }
        public string UserCulture { get; set; }
    }
    public class GetTimeSlotsList
    {
        public int Company_ID { get; set; }
        public string UserCulture { get; set; }
    }
    public class LoadMonthGridList
    {
        public string UserCulture { get; set; }
    }
    public class LoadYearGridList
    {
        public string UserCulture { get; set; }
    }
    public class ExportHelpDesk_CR_ResolutionTimeWithTimeSlotsList
    {
        public int User_ID { get; set; }
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public string UserCulture { get; set; }
        public string Branch_ID { get; set; }
        public int LanguageID { get; set; }
        public int UserLanguageID { get; set; }
        public int exprtType { get; set; }
    }
    public class GetMonthWiseBarChartDataList
    {
        public string MonthName { get; set; }
    }
    public class GetDatewiseDataList
    {
        public string FDate { get; set; }
        public string TDate { get; set; }
        public int Type { get; set; }
        public int Year { get; set; }
        public string MonthName { get; set; }
        public int page { get; set; }
        public int rows { get; set; }
        public string Branch { get; set; }
        public int Company_ID { get; set; }
        public string UserCulture { get; set; }

    }
    public class GetMonthDataList
    {
        public string FDate { get; set; }
        public string TDate { get; set; }
        public int Type { get; set; }
        public int Year { get; set; }
        public string Branch { get; set; }
        public int Company_ID { get; set; }
        public string UserCulture { get; set; }
    }
    public class GetYearWiseBarChartDataList
    {
        public int Year { get; set; }
    }


    public class GetYearDataList
    {
        public string FDate { get; set; }
        public string TDate { get; set; }
        public string Branch { get; set; }
        public int Company_ID { get; set; }
        public string UserCulture { get; set; }
        public int Type { get; set; }
    }
    public class GetDataHelpDesk_CR_ResolutionTimeWithTimeSlotsList
    {
        public string Branch { get; set; }
        public string FDate { get; set; }
        public string TDate { get; set; }
        public string Company { get; set; }
        public int Company_ID { get; set; }
        public int Type { get; set; }
        public string UserCulture { get; set; }
    }
    #endregion
    #region HelpDesk_CR_ResolutionTimeWithTimeSlotsMasterClasses
    public class RTS_Branch
    {
        public List<RTS_Branchs> Branchs
        {
            get;
            set;
        }
    }
    public class RTS_Branchs
    {

        public int ID
        {
            get;
            set;
        }
        public string Name
        {
            get;
            set;
        }
    }

    public class TimeSlot
    {
        public string Name { get; set; }
        public string TempName { get; set; }
        public int Count { get; set; }
        public string Hours { get; set; }
        public int Year { get; set; }
        public int Month { get; set; }
        public DateTime Date { get; set; }
    }
    public partial class ServiceRequest
    {

        public string ResolutionTime { get; set; }
        public int Year { get; set; }
        public int Month { get; set; }
    }
    public class JQGridColModel
    {
        public string name { get; set; }
        public string index { get; set; }
        public int width { get; set; }
        public string align { get; set; }
        public bool sortable { get; set; }
    }
    #endregion
}
