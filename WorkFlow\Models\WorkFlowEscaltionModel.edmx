﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
    <Schema Namespace="AMERP_DEVMajorModel.Store" Alias="Self" Provider="System.Data.SqlClient" ProviderManifestToken="2008" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityContainer Name="AMERP_DEVMajorModelStoreContainer">
          <EntitySet Name="GNM_WFEscalation" EntityType="AMERP_DEVMajorModel.Store.GNM_WFEscalation" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_WFEscalationHistory" EntityType="AMERP_DEVMajorModel.Store.GNM_WFEscalationHistory" store:Type="Tables" Schema="dbo" />
          <AssociationSet Name="FK_GNM_WFEscalationHistory_CompanyEmployee" Association="AMERP_DEVMajorModel.Store.FK_GNM_WFEscalationHistory_CompanyEmployee">
            <End Role="GNM_WFEscalation" EntitySet="GNM_WFEscalation" />
            <End Role="GNM_WFEscalationHistory" EntitySet="GNM_WFEscalationHistory" />
          </AssociationSet>
        </EntityContainer>
        <EntityType Name="GNM_WFEscalation">
          <Key>
            <PropertyRef Name="Escalation_ID" />
          </Key>
          <Property Name="Escalation_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="Company_ID" Type="int" Nullable="false" />
          <Property Name="WorkFlow_ID" Type="int" Nullable="false" />
          <Property Name="WFRole_ID" Type="int" Nullable="false" />
          <Property Name="Escalation_Hours" Type="decimal" Nullable="false" Precision="5" Scale="2" />
          <Property Name="Escalate_To_EmployeeID" Type="int" Nullable="false" />
          <Property Name="Escalate_IsEmail" Type="bit" Nullable="false" />
          <Property Name="Escalate_IsMobile" Type="bit" Nullable="false" />
          <Property Name="CCToAssignee" Type="bit" Nullable="false" />
        </EntityType>
        <EntityType Name="GNM_WFEscalationHistory">
          <Key>
            <PropertyRef Name="Escalation_History_ID" />
          </Key>
          <Property Name="Escalation_History_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="Escalation_ID" Type="int" Nullable="false" />
          <Property Name="WorkFlow_ID" Type="int" Nullable="false" />
          <Property Name="Transaction_ID" Type="int" Nullable="false" />
          <Property Name="Escalation_Date" Type="datetime" Nullable="false" />
          <Property Name="Escalate_To_EmployeeID" Type="int" Nullable="false" />
        </EntityType>
        <Association Name="FK_GNM_WFEscalationHistory_CompanyEmployee">
          <End Role="GNM_WFEscalation" Type="AMERP_DEVMajorModel.Store.GNM_WFEscalation" Multiplicity="1" />
          <End Role="GNM_WFEscalationHistory" Type="AMERP_DEVMajorModel.Store.GNM_WFEscalationHistory" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WFEscalation">
              <PropertyRef Name="Escalation_ID" />
            </Principal>
            <Dependent Role="GNM_WFEscalationHistory">
              <PropertyRef Name="Escalation_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
      </Schema></edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="WorkFlowEscalationModel" Alias="Self" p1:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:p1="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityContainer Name="WorkFlowEscalationEntities" p1:LazyLoadingEnabled="true">
          <EntitySet Name="GNM_WFEscalation" EntityType="WorkFlowEscalationModel.GNM_WFEscalation" />
          <EntitySet Name="GNM_WFEscalationHistory" EntityType="WorkFlowEscalationModel.GNM_WFEscalationHistory" />
          <AssociationSet Name="FK_GNM_WFEscalationHistory_CompanyEmployee" Association="WorkFlowEscalationModel.FK_GNM_WFEscalationHistory_CompanyEmployee">
            <End Role="GNM_WFEscalation" EntitySet="GNM_WFEscalation" />
            <End Role="GNM_WFEscalationHistory" EntitySet="GNM_WFEscalationHistory" />
          </AssociationSet>
        </EntityContainer>
        <EntityType Name="GNM_WFEscalation">
          <Key>
            <PropertyRef Name="Escalation_ID" />
          </Key>
          <Property Name="Escalation_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="Company_ID" Type="Int32" Nullable="false" />
          <Property Name="WorkFlow_ID" Type="Int32" Nullable="false" />
          <Property Name="WFRole_ID" Type="Int32" Nullable="false" />
          <Property Name="Escalation_Hours" Type="Decimal" Nullable="false" Precision="5" Scale="2" />
          <Property Name="Escalate_To_EmployeeID" Type="Int32" Nullable="false" />
          <Property Name="Escalate_IsEmail" Type="Boolean" Nullable="false" />
          <Property Name="Escalate_IsMobile" Type="Boolean" Nullable="false" />
          <NavigationProperty Name="GNM_WFEscalationHistory" Relationship="WorkFlowEscalationModel.FK_GNM_WFEscalationHistory_CompanyEmployee" FromRole="GNM_WFEscalation" ToRole="GNM_WFEscalationHistory" />
          <Property Type="Boolean" Name="CCToAssignee" Nullable="false" />
        </EntityType>
        <EntityType Name="GNM_WFEscalationHistory">
          <Key>
            <PropertyRef Name="Escalation_History_ID" />
          </Key>
          <Property Name="Escalation_History_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="Escalation_ID" Type="Int32" Nullable="false" />
          <Property Name="WorkFlow_ID" Type="Int32" Nullable="false" />
          <Property Name="Transaction_ID" Type="Int32" Nullable="false" />
          <Property Name="Escalation_Date" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="Escalate_To_EmployeeID" Type="Int32" Nullable="false" />
          <NavigationProperty Name="GNM_WFEscalation" Relationship="WorkFlowEscalationModel.FK_GNM_WFEscalationHistory_CompanyEmployee" FromRole="GNM_WFEscalationHistory" ToRole="GNM_WFEscalation" />
        </EntityType>
        <Association Name="FK_GNM_WFEscalationHistory_CompanyEmployee">
          <End Role="GNM_WFEscalation" Type="WorkFlowEscalationModel.GNM_WFEscalation" Multiplicity="1" />
          <End Role="GNM_WFEscalationHistory" Type="WorkFlowEscalationModel.GNM_WFEscalationHistory" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WFEscalation">
              <PropertyRef Name="Escalation_ID" />
            </Principal>
            <Dependent Role="GNM_WFEscalationHistory">
              <PropertyRef Name="Escalation_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
      </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="AMERP_DEVMajorModelStoreContainer" CdmEntityContainer="WorkFlowEscalationEntities">
          <EntitySetMapping Name="GNM_WFEscalation">
            <EntityTypeMapping TypeName="WorkFlowEscalationModel.GNM_WFEscalation">
              <MappingFragment StoreEntitySet="GNM_WFEscalation">
                <ScalarProperty Name="CCToAssignee" ColumnName="CCToAssignee" />
                <ScalarProperty Name="Escalation_ID" ColumnName="Escalation_ID" />
                <ScalarProperty Name="Company_ID" ColumnName="Company_ID" />
                <ScalarProperty Name="WorkFlow_ID" ColumnName="WorkFlow_ID" />
                <ScalarProperty Name="WFRole_ID" ColumnName="WFRole_ID" />
                <ScalarProperty Name="Escalation_Hours" ColumnName="Escalation_Hours" />
                <ScalarProperty Name="Escalate_To_EmployeeID" ColumnName="Escalate_To_EmployeeID" />
                <ScalarProperty Name="Escalate_IsEmail" ColumnName="Escalate_IsEmail" />
                <ScalarProperty Name="Escalate_IsMobile" ColumnName="Escalate_IsMobile" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="GNM_WFEscalationHistory">
            <EntityTypeMapping TypeName="WorkFlowEscalationModel.GNM_WFEscalationHistory">
              <MappingFragment StoreEntitySet="GNM_WFEscalationHistory">
                <ScalarProperty Name="Escalation_History_ID" ColumnName="Escalation_History_ID" />
                <ScalarProperty Name="Escalation_ID" ColumnName="Escalation_ID" />
                <ScalarProperty Name="WorkFlow_ID" ColumnName="WorkFlow_ID" />
                <ScalarProperty Name="Transaction_ID" ColumnName="Transaction_ID" />
                <ScalarProperty Name="Escalation_Date" ColumnName="Escalation_Date" />
                <ScalarProperty Name="Escalate_To_EmployeeID" ColumnName="Escalate_To_EmployeeID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="True" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="True" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="None" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>