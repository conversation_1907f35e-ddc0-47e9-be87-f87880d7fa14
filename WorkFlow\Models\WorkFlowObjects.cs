﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace WorkFlow.Models
{

    public class WorkFlowObjects
    {
        public int WorkFlow_ID
        {
            get;

            set;

        }
        public string WorkFlow_Name
        {
            get;

            set;

        }
        public string IsAllQueBranchFilter
        {
            get;

            set;
        }

    }
    public class WorkFlowSteps
    {
        public int WFSteps_ID
        {
            get;

            set;

        }
        public string WFStep_Name
        {
            get;

            set;

        }
        public string WFStepType_Nm
        {
            get;

            set;
        }
        public string WFStepStatus_Nm
        {
            get;

            set;
        }
        public string WFStep_IsActive
        {
            get;

            set;
        }

    }
    public class WorkFlowRole
    {
        public int WFRole_ID
        {
            get;

            set;

        }

        public int WFRole_ExternalCompany_ID
        {
            get;

            set;

        }

        public string WFRole_IsExternal
        {
            get;

            set;

        }

        public string WFRole_ExternalCompanyName
        {
            get;

            set;

        }

        public string WFRole_Name
        {
            get;

            set;

        }
        public string WfRole_IsAdmin
        {
            get;

            set;
        }
        public string WfRole_AutoAllocationAllowed
        {
            get;

            set;
        }


    }
    public class WorkFlowRoleUsers
    {
        public int WFRoleUser_ID
        {
            get;

            set;

        }
        public string User_Name
        {
            get;

            set;

        }
        public int ApprovalLimit
        {
            get;

            set;
        }


    }
    public class WorkFlowStepLinks
    {
        public int WFStepLink_ID
        {
            get;

            set;

        }

        public string FrmStepNm
        {
            get;

            set;

        }


        public int ParentWFID
        {
            get;

            set;

        }

        public string ParentWorkFlow
        {
            get;

            set;

        }


        public int ParentWFStepLinkID
        {
            get;

            set;

        }

        public string ParentWorkFlowStepLink
        {
            get;

            set;

        }


        public int WFChildObjectID
        {
            get;

            set;

        }

        public string WFChildObject
        {
            get;

            set;

        }


        public string ChildObjectAction
        {
            get;

            set;

        }

        public int ChildObjectActionID
        {
            get;

            set;

        }

        public string WorkFlowField
        {
            get;

            set;
        }
        public string AutoCondition
        {
            get;

            set;
        }

        public string ToStepNm
        {
            get;

            set;
        }
        public string WFAction_Name
        {
            get;

            set;
        }
        public string WFRole_Name
        {
            get;

            set;
        }
        public string Addresse_Flag
        {
            get;

            set;
        }
        public string IsSMSSentToCustomer
        {
            get;

            set;
        }
        public string IsEmailSentToCustomer
        {
            get;

            set;
        }
        public string IsSMSSentToAddressee
        {
            get;

            set;
        }
        public string IsEmailSentToAddresse
        {
            get;

            set;
        }
        public string AutoAllocationAllowed
        {
            get;

            set;
        }
        public string IsVersionEnabled
        {
            get;

            set;
        }
    }
    public class MovementHistory
    {
        private int action_Chosen;

        public int Action_Chosen
        {
            get { return action_Chosen; }
            set { action_Chosen = value; }
        }
        private string action_Remarks;

        public string Action_Remarks
        {
            get { return action_Remarks; }
            set { action_Remarks = value; }
        }
        private string action_Time;

        public string Action_Time
        {
            get { return action_Time; }
            set { action_Time = value; }
        }
        private int actioned_By;

        public int Actioned_By
        {
            get { return actioned_By; }
            set { actioned_By = value; }
        }
        private int addresse_ID;

        public int Addresse_ID
        {
            get { return addresse_ID; }
            set { addresse_ID = value; }
        }
        private DateTime received_Time;

        public DateTime Received_Time
        {
            get { return received_Time; }
            set { received_Time = value; }
        }
        private int transaction_ID;

        public int Transaction_ID
        {
            get { return transaction_ID; }
            set { transaction_ID = value; }
        }
        private int wFCaseProgress_ID;

        public int WFCaseProgress_ID
        {
            get { return wFCaseProgress_ID; }
            set { wFCaseProgress_ID = value; }
        }
        private int wFSteps_ID;

        public int WFSteps_ID
        {
            get { return wFSteps_ID; }
            set { wFSteps_ID = value; }
        }
        private int workFlow_ID;

        public int WorkFlow_ID
        {
            get { return workFlow_ID; }
            set { workFlow_ID = value; }
        }
        private string workFlow_Name;

        public string WorkFlow_Name
        {
            get { return workFlow_Name; }
            set { workFlow_Name = value; }
        }
        private string wFStep_Name;

        public string WFStep_Name
        {
            get { return wFStep_Name; }
            set { wFStep_Name = value; }
        }
        private string wFAction_Name;

        public string WFAction_Name
        {
            get { return wFAction_Name; }
            set { wFAction_Name = value; }
        }
        private string userorRoleName;

        public string UserorRoleName
        {
            get { return userorRoleName; }
            set { userorRoleName = value; }
        }

        private string assignTO;

        public string AssignTO
        {
            get { return assignTO; }
            set { assignTO = value; }
        }

        private byte addresse_Flag;

        public byte Addresse_Flag
        {
            get { return addresse_Flag; }
            set { addresse_Flag = value; }
        }
    }      
}