﻿using SharedAPIClassLibrary_AMERP;
using SharedAPIClassLibrary_AMERP.Services;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web;
using System.Web.Http;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class PRT_PurchaseOrderCancellationController : ApiController
    {
        #region ::: Save PO Cancellation:::
        /// <summary>
        /// Method to insert the PO Cancellation
        /// </summary>   
        [Route("api/PRT_PurchaseOrderCancellation/Save")]
        [HttpPost]
       // [JwtTokenValidationFilter]
        public IHttpActionResult Save([FromBody] POCSaveList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = PRT_PurchaseOrderCancellationServices.Save(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


      

    }
}
