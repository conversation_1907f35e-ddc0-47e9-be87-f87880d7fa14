using System;
using System.Collections.Generic;

namespace PBC.UtilityService.Utilities.Models
{
    public class GNM_Parts
    {
        public int Parts_ID { get; set; }
        public string? Parts_PartPrefix { get; set; }
        public string? Parts_PartsNumber { get; set; }
        public string? Parts_PartsDescription { get; set; }
        public string? Parts_AliasPartPrefix { get; set; }
        public string? Parts_AliasPartNumber { get; set; }
        public int? SuperceededPart_ID { get; set; }
        public int? SuperceedingPart_ID { get; set; }
        public int MovementType_ID { get; set; }
        public decimal? Parts_Weight { get; set; }
        public string? Parts_Dimensions { get; set; }
        public int? PartsCategory_ID { get; set; }
        public int? PartsFunctionGroup_ID { get; set; }
        public int? PartsCustomsCode_ID { get; set; }
        public int UnitOfMeasurement_ID { get; set; }
        public int? SupersessionType_ID { get; set; }
        public bool Parts_IsActive { get; set; }
        public bool Parts_IsHazardousGood { get; set; }
        public int ModifiedBy { get; set; }
        public DateTime ModifiedDate { get; set; }
        public int Company_ID { get; set; }
        public bool Parts_IsLocal { get; set; }
        public bool Parts_IsComponent { get; set; }
        public bool IsKitPart { get; set; }
        public int? ExciseDuty_ID { get; set; }
        public int? SalvagePart_ID { get; set; }
        public int? PartType { get; set; }
        public byte? AttachmentCount { get; set; }
        public int? PartsDisposal_ID { get; set; }
        public DateTime? IntroductionDate { get; set; }
        public int? LatestSupersession_ID { get; set; }
        public int? CustomerWarrantyInDays { get; set; }
        public int? ReadingLimit { get; set; }
    }

    public class PRT_PurchaseOrderPartsDetail
    {
        public int PurchaseOrderPartsDetail_ID { get; set; }
        public int PurchaseOrder_ID { get; set; }
        public int Parts_ID { get; set; }
        public decimal SupplierPrice { get; set; }
        public decimal? RequestedQuantity { get; set; }
        public decimal? ApprovedQuantity { get; set; }
        public decimal? InvoicedQuantity { get; set; }
        public decimal? BackOrderCancelledQuantity { get; set; }
        public decimal? DiscountPercentage { get; set; }
        public decimal? DiscountAmount { get; set; }
        public int? TaxStructure_ID { get; set; }
        public decimal? TaxAmount { get; set; }
        public decimal Amount { get; set; }
        public int? PartsOrder_ID { get; set; }
        public decimal? DiscountedAmount { get; set; }
        public decimal? MRP { get; set; }
    }

    public class PRT_PurchaseInvoice
    {
        public int PurchaseInvoice_ID { get; set; }
        public string? PurchaseInvoiceNumber { get; set; }
        public DateTime PurchaseInvoiceDate { get; set; }
        public int Supplier_ID { get; set; }
        public int Currency_ID { get; set; }
        public decimal ExchangeRate { get; set; }
        public string? SupplierInvoiceNumber { get; set; }
        public DateTime? SupplierInvoiceDate { get; set; }
        public bool IsImport { get; set; }
        public int? ModeOfShipment_ID { get; set; }
        public decimal TotalInvoiceAmountInLocalCurrency { get; set; }
        public decimal LandingCostFactor { get; set; }
        public string? Remarks { get; set; }
        public decimal? TotalAmount { get; set; }
        public decimal? DiscountPercentage { get; set; }
        public decimal? DiscountAmount { get; set; }
        public decimal? DiscountedAmount { get; set; }
        public int? TaxStructure_ID { get; set; }
        public string? TaxableOtherCharges { get; set; }
        public decimal? TaxablePercentage { get; set; }
        public decimal? TaxableOtherChargesAmount { get; set; }
        public decimal? TaxOnTaxableOtherCharges { get; set; }
        public decimal? TotalTaxableAmount { get; set; }
        public decimal? TaxAmount { get; set; }
        public string? NonTaxableOtherCharges { get; set; }
        public decimal? NonTaxablePercentage { get; set; }
        public decimal? NonTaxableOtherChargesAmount { get; set; }
        public decimal? TotalInvoiceAmount { get; set; }
        public int? DocumentNumber { get; set; }
        public int FinancialYear { get; set; }
        public int? Company_ID { get; set; }
        public int? Branch_ID { get; set; }
        public int Updated_By { get; set; }
        public DateTime Updated_Date { get; set; }
        public int? PurchaseGRN_ID { get; set; }
        public int? WareHouse_ID { get; set; }
        public int? PurchaseOrderClass_ID { get; set; }
        public int? ItemLevelTaxStructure_ID { get; set; }
        public bool? IsDealer { get; set; }
        public decimal? Roundoff { get; set; }
        public decimal? TotalPIAmount { get; set; }

        public List<PRT_PurchaseInvoicePartsDetails> PRT_PurchaseInvoicePartsDetails { get; set; } = new List<PRT_PurchaseInvoicePartsDetails>();
    }

    public class PRT_PurchaseInvoicePartsDetails
    {
        public int PurchaseInvoicePartsDetail_ID { get; set; }
        public int PurchaseInvoice_ID { get; set; }
        public int? PurchaseOrder_ID { get; set; }
        public int Parts_ID { get; set; }
        public int? PartsOrder_ID { get; set; }
        public decimal Rate { get; set; }
        public decimal Quantity { get; set; }
        public decimal? DiscountPercentage { get; set; }
        public decimal? DiscountAmount { get; set; }
        public int? TaxStructure_ID { get; set; }
        public decimal? TaxAmount { get; set; }
        public decimal Amount { get; set; }
        public decimal? LandingCostInLocalCurrency { get; set; }
        public decimal? VarianceinRate { get; set; }
        public decimal? DiscountedAmount { get; set; }
        public int WareHouse_ID { get; set; }
        public decimal? MRP { get; set; }
    }

    public class CreateAutoGRNList
    {
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public int Branch { get; set; }
        public int purchaseInvoiceID { get; set; }
    }

    public class Save_BLPartsInvoiceList
    {
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int User_ID { get; set; }
    }

    public class StockLessPartDetails
    {
        public int Parts_ID { get; set; }
        public decimal FreeStock { get; set; }
        public decimal CurrentStock { get; set; }
        public decimal RequestedQty { get; set; }
        public decimal Quantity { get; set; }
        public string? PartsPrefix { get; set; }
        public string? PartNumber { get; set; }
        public string? Parts { get; set; }
    }
}
