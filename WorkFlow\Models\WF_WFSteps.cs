//------------------------------------------------------------------------------
// <auto-generated>
//    This code was generated from a template.
//
//    Manual changes to this file may cause unexpected behavior in your application.
//    Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace WorkFlow.Models
{
    using System;
    using System.Collections.Generic;
    
    public partial class WF_WFSteps
    {
        public WF_WFSteps()
        {
            this.GNM_WFStepLink = new HashSet<WF_WFStepLink>();
            this.GNM_WFStepLink1 = new HashSet<WF_WFStepLink>();
            this.GNM_WFStepsLocale = new HashSet<WF_WFStepsLocale>();
        }
    
        public int WFSteps_ID { get; set; }
        public int WorkFlow_ID { get; set; }
        public string WFStep_Name { get; set; }
        public int WFStepType_ID { get; set; }
        public int WFStepStatus_ID { get; set; }
        public bool WFStep_IsActive { get; set; }
        public string BranchCode { get; set; }
    
        public virtual WF_WFStepType GNM_WFStepType { get; set; }
        public virtual WF_WFStepStatus GNM_WFStepStatus { get; set; }
        public virtual WF_WorkFlow GNM_WorkFlow { get; set; }
        public virtual ICollection<WF_WFStepLink> GNM_WFStepLink { get; set; }
        public virtual ICollection<WF_WFStepLink> GNM_WFStepLink1 { get; set; }
        public virtual ICollection<WF_WFStepsLocale> GNM_WFStepsLocale { get; set; }
    }
}
