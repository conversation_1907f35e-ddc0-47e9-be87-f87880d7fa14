﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.CoreProductMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreSecondarySegmentMasterServices
    {

        #region ::: Select Uday Kumar J B 09-08-2024:::
        /// <summary>
        /// To Select Secondary Segment for a Primary Segment
        /// </summary>

        public static IActionResult Select(string connString, SelectCoreSecondarySegmentList SelectCoreSecondarySegmentobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filterString, bool advnce, string advnceFilters)
        {
            var jsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int Count = 0;
                int Total = 0;
                string AppPath = string.Empty;
                List<SecondarySegmentMaster> secondarySegmentMasters = new List<SecondarySegmentMaster>();

                string yesE = CommonFunctionalities.GetResourceString(SelectCoreSecondarySegmentobj.GeneralCulture.ToString(), "Yes").ToString();
                string NoE = CommonFunctionalities.GetResourceString(SelectCoreSecondarySegmentobj.GeneralCulture.ToString(), "No").ToString();
                string yesL = CommonFunctionalities.GetResourceString(SelectCoreSecondarySegmentobj.UserCulture.ToString(), "Yes").ToString();
                string NoL = CommonFunctionalities.GetResourceString(SelectCoreSecondarySegmentobj.UserCulture.ToString(), "No").ToString();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Sel_AM_ERP_SelectSecondarySegments", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@PrimarySegmentID", SelectCoreSecondarySegmentobj.PrimarySegmentID);
                        cmd.Parameters.AddWithValue("@LanguageID", SelectCoreSecondarySegmentobj.LanguageID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var secondarySegment = new SecondarySegmentMaster
                                {
                                    SecondarySegment_ID = reader.GetInt32(0),
                                    SecondarySegment_Description = reader.IsDBNull(3) ? reader.GetString(1) : reader.GetString(3),
                                    SecondarySegment_IsActive = reader.GetBoolean(2) ? (SelectCoreSecondarySegmentobj.LanguageID == Convert.ToInt32(SelectCoreSecondarySegmentobj.GeneralLanguageID) ? yesE : yesL) : (SelectCoreSecondarySegmentobj.LanguageID == Convert.ToInt32(SelectCoreSecondarySegmentobj.GeneralLanguageID) ? NoE : NoL)
                                };
                                secondarySegmentMasters.Add(secondarySegment);
                            }
                        }
                    }

                    var IQSecondarySegmentMaster = secondarySegmentMasters.AsQueryable();

                    if (_search)
                    {
                        Filters filters = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(filterString))).ToObject<Filters>();
                        if (filters != null && filters.rules.Count > 0)
                        {

                            IQSecondarySegmentMaster = IQSecondarySegmentMaster.FilterSearch<SecondarySegmentMaster>(filters);

                        }
                    }
                    else if (advnce)
                    {
                        AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<AdvanceFilter>();
                        IQSecondarySegmentMaster = IQSecondarySegmentMaster.AdvanceSearch<SecondarySegmentMaster>(advnfilter);
                    }

                    IQSecondarySegmentMaster = IQSecondarySegmentMaster.OrderByField<SecondarySegmentMaster>(sidx, sord);

                    Count = IQSecondarySegmentMaster.Count();
                    Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                    if (Count < (rows * page) && Count != 0)
                    {
                        page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                    }

                    jsonData = new
                    {
                        total = Total,
                        page = page,
                        rows = (from a in IQSecondarySegmentMaster.AsEnumerable()
                                select new
                                {
                                    ID = a.SecondarySegment_ID,
                                    edit = "<a title='Edit' href='#' id='" + a.SecondarySegment_ID + "' key='" + a.SecondarySegment_ID + "' class='SecondarySegmentEdit' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                    delete = "<input type='checkbox' key='" + a.SecondarySegment_ID + "' defaultchecked=''  id='chk" + a.SecondarySegment_ID + "' class='SecondarySegmentDelete'/>",
                                    SecondarySegment_Description = a.SecondarySegment_Description,
                                    SecondarySegment_IsActive = a.SecondarySegment_IsActive,
                                    Locale = "<img key='" + a.SecondarySegment_ID + "' src='" + AppPath + "/Content/local.png' class='SecondarySegmentLocale' alt='Localize' width='20' height='20'  title='Localize'/>",
                                    View = "<img id='" + a.SecondarySegment_ID + "' src='" + AppPath + "/Content/plus.gif' key='" + a.SecondarySegment_ID + "' class='ViewSecondarySegmentLocale' SecondarySegment='" + a.SecondarySegment_Description + "'/>",
                                }).ToList().Paginate(page, rows),
                        records = Count,
                        filter = filterString,
                        advanceFilter = advnceFilters,
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: SelectReferenceMaster Uday Kumar J B 09-08-2024 :::
        /// <summary>
        /// To Select Primary Segment 
        /// </summary> 
        /// 

        public static IActionResult SelectReferenceMaster(string connString, SelectReferenceMasterCoreSecondarySegmentList SelectReferenceMasterCoreSecondarySegmentobj)
        {
            var Masterdata = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int Language_ID = Convert.ToInt32(SelectReferenceMasterCoreSecondarySegmentobj.UserLanguageID);
                string userLanguageCode = SelectReferenceMasterCoreSecondarySegmentobj.UserLanguageCode.ToString();
                string generalLanguageCode = SelectReferenceMasterCoreSecondarySegmentobj.GeneralLanguageCode.ToString();

                List<dynamic> referenceMasterData = new List<dynamic>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Sel_AM_ERP_SelectPrimarySegment", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@UserLanguageCode", userLanguageCode);
                        cmd.Parameters.AddWithValue("@GeneralLanguageCode", generalLanguageCode);

                        if (userLanguageCode != generalLanguageCode)
                        {
                            cmd.Parameters.AddWithValue("@LanguageID", Language_ID);
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                referenceMasterData.Add(new
                                {
                                    ID = reader.GetInt32(0),
                                    Name = reader.GetString(1)
                                });
                            }
                        }
                    }
                }

                Masterdata = new
                {
                    ReferenceMasterData = referenceMasterData
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Masterdata);
        }
        #endregion


        #region ::: SelectParticularSecondarySegment Uday Kumar J B 09-08-2024:::
        /// <summary>
        /// To Select Particular Secondary Segment
        /// </summary>
        /// 

        public static IActionResult SelectParticularSecondarySegment(string connString, SelectParticularSecondarySegmentList SelectParticularSecondarySegmentobj)
        {
            var x = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int Language_ID = Convert.ToInt32(SelectParticularSecondarySegmentobj.UserLanguageID);

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand("Up_Sel_AM_ERP_SelectParticularSecondarySegment", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@SecondarySegmentID", SelectParticularSecondarySegmentobj.SecondarySegmentID);
                        command.Parameters.AddWithValue("@LanguageID", Language_ID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                x = new
                                {
                                    SecondarySegment_ID = reader["SecondarySegment_ID"],
                                    SecondarySegment_Description = reader["SecondarySegment_Description"],
                                    SecondarySegment_IsActive = reader["SecondarySegment_IsActive"],
                                    SecondarySegmentLocale_ID = reader["SecondarySegmentLocale_ID"],
                                    SecondarySegmentLocale_Description = reader["SecondarySegmentLocale_Description"]
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(x);
        }
        #endregion


        #region ::: Save Uday Kumar J B 09-08-2024:::
        /// <summary>
        /// To Insert and Update Secondary Segment
        /// </summary>
        ///
        public static IActionResult Save(string connString, SaveCoreSecondarySegmentMasterList SaveCoreSecondarySegmentMasterobj)
        {
            JObject jObj = null;
            string Msg = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int BranchID = Convert.ToInt32(SaveCoreSecondarySegmentMasterobj.Branch);
                //GNM_User User = SaveCoreSecondarySegmentMasterobj.UserDetails.FirstOrDefault();
                jObj = JObject.Parse(SaveCoreSecondarySegmentMasterobj.data);
                int Count = jObj["rows"].Count();

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();
                    using (SqlTransaction transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            for (int i = 0; i < Count; i++)
                            {
                                GNM_SecondarySegment SSRow = jObj["rows"].ElementAt(i).ToObject<GNM_SecondarySegment>();

                                if (SSRow.SecondarySegment_ID != 0)
                                {
                                    // Update operation
                                    using (SqlCommand command = new SqlCommand("Up_Ins_AM_ERP_UpdateSecondarySegment1", connection, transaction))
                                    {
                                        command.CommandType = CommandType.StoredProcedure;
                                        command.Parameters.AddWithValue("@Description", Uri.UnescapeDataString(SSRow.SecondarySegment_Description));
                                        command.Parameters.AddWithValue("@IsActive", SSRow.SecondarySegment_IsActive);
                                        command.Parameters.AddWithValue("@PrimarySegmentID", SSRow.PrimarySegment_ID);
                                        command.Parameters.AddWithValue("@ModifiedBy", Convert.ToInt32(SaveCoreSecondarySegmentMasterobj.User_ID));
                                        command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                                        command.Parameters.AddWithValue("@SecondarySegmentID", SSRow.SecondarySegment_ID);

                                        command.ExecuteNonQuery();
                                    }

                                    //  gbl.InsertGPSDetails(Convert.ToInt32(SaveCoreSecondarySegmentMasterobj.Company_ID.ToString()), BranchID, User.User_ID, Common.GetObjectID("CoreSecondarySegmentMaster"), SSRow.SecondarySegment_ID, 0, 0, "Updated Secondary Segment- " + SSRow.SecondarySegment_Description + "", false, Convert.ToInt32(SaveCoreSecondarySegmentMasterobj.MenuID), Convert.ToDateTime(SaveCoreSecondarySegmentMasterobj.LoggedINDateTime));
                                }
                                else
                                {
                                    // Insert operation
                                    using (SqlCommand command = new SqlCommand("Up_Ins_AM_ERP_InsertSecondarySegment1", connection, transaction))
                                    {
                                        command.CommandType = CommandType.StoredProcedure;
                                        command.Parameters.AddWithValue("@Description", Uri.UnescapeDataString(SSRow.SecondarySegment_Description));
                                        command.Parameters.AddWithValue("@IsActive", SSRow.SecondarySegment_IsActive);
                                        command.Parameters.AddWithValue("@PrimarySegmentID", SSRow.PrimarySegment_ID);
                                        command.Parameters.AddWithValue("@ModifiedBy", Convert.ToInt32(SaveCoreSecondarySegmentMasterobj.User_ID));
                                        command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                                        SSRow.SecondarySegment_ID = Convert.ToInt32(command.ExecuteScalar());
                                    }

                                    //  gbl.InsertGPSDetails(Convert.ToInt32(SaveCoreSecondarySegmentMasterobj.Company_ID.ToString()), BranchID, User.User_ID, Common.GetObjectID("CoreSecondarySegmentMaster"), SSRow.SecondarySegment_ID, 0, 0, "Inserted Secondary Segment- " + SSRow.SecondarySegment_Description + "", false, Convert.ToInt32(SaveCoreSecondarySegmentMasterobj.MenuID), Convert.ToDateTime(SaveCoreSecondarySegmentMasterobj.LoggedINDateTime));
                                }
                            }

                            transaction.Commit();
                            Msg = "Saved";
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                Msg = string.Empty;
            }
            return new JsonResult(Msg);
        }
        #endregion


        #region ::: UpdateLocale Uday Kumar J B 09-08-2024:::
        /// <summary>
        /// To Update Secondary Segment Locale
        /// </summary>
        public static IActionResult UpdateLocale(string connString, UpdateLocaleListb UpdateLocaleobj)
        {
            var x = default(dynamic);
            int SecondarySegmentLocale_ID = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                GNM_SecondarySegmentLocale SSLRow = null;
                JObject jObj = JObject.Parse(UpdateLocaleobj.data);
                SSLRow = jObj.ToObject<GNM_SecondarySegmentLocale>();

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();
                    using (SqlTransaction transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            if (SSLRow.SecondarySegmentLocale_ID != 0)
                            {
                                // Update operation
                                using (SqlCommand command = new SqlCommand("Up_Upd_AM_ERP_UpdateSecondarySegmentLocale", connection, transaction))
                                {
                                    command.CommandType = CommandType.StoredProcedure;
                                    command.Parameters.AddWithValue("@Description", Common.DecryptString(SSLRow.SecondarySegment_Description));
                                    command.Parameters.AddWithValue("@SecondarySegmentLocaleID", SSLRow.SecondarySegmentLocale_ID);

                                    command.ExecuteNonQuery();
                                }

                                SecondarySegmentLocale_ID = SSLRow.SecondarySegmentLocale_ID;
                            }
                            else
                            {
                                // Insert operation
                                using (SqlCommand command = new SqlCommand("Up_Ins_AM_ERP_InsertSecondarySegmentLocale1", connection, transaction))
                                {
                                    command.CommandType = CommandType.StoredProcedure;
                                    command.Parameters.AddWithValue("@Description", Common.DecryptString(SSLRow.SecondarySegment_Description));
                                    command.Parameters.AddWithValue("@SecondarySegmentID", SSLRow.SecondarySegment_ID);
                                    command.Parameters.AddWithValue("@Language_ID", UpdateLocaleobj.Language_ID);
                                    SecondarySegmentLocale_ID = Convert.ToInt32(command.ExecuteScalar());
                                }
                            }

                            // Insert GPS details
                            //  gbl.InsertGPSDetails(Convert.ToInt32(UpdateLocaleobj.Company_ID), Convert.ToInt32(UpdateLocaleobj.Branch), Convert.ToInt32(UpdateLocaleobj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreSecondarySegmentMaster")), SSLRow.SecondarySegment_ID, 0, 0, "Update", false, Convert.ToInt32(UpdateLocaleobj.MenuID));

                            transaction.Commit();

                            x = new
                            {
                                SecondarySegmentLocale_ID = SecondarySegmentLocale_ID
                            };
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(x);
        }
        #endregion


        #region ::: Delete Uday Kumar J B 09-08-2024 :::
        /// <summary>
        /// To Delete Secondary Segment
        /// </summary>
        /// 

        public static IActionResult Delete(string connString, DeleteCoreSecondarySegmentList DeleteCoreSecondarySegmentobj)
        {
            string msg = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jObj = JObject.Parse(DeleteCoreSecondarySegmentobj.key);
                int branchID = Convert.ToInt32(DeleteCoreSecondarySegmentobj.Branch);
                // GNM_User user = DeleteCoreSecondarySegmentobj.UserDetails.FirstOrDefault();
                int count = jObj["rows"].Count();
                int id = 0;

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();
                    using (SqlTransaction transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            for (int i = 0; i < count; i++)
                            {
                                id = Convert.ToInt32(jObj["rows"].ElementAt(i)["id"]);

                                // Delete the secondary segment locale
                                using (SqlCommand command = new SqlCommand("DeleteSecondarySegmentLocale", connection, transaction))
                                {
                                    command.CommandType = CommandType.StoredProcedure;
                                    command.Parameters.AddWithValue("@SecondarySegmentID", id);
                                    command.ExecuteNonQuery();
                                }

                                // Delete the secondary segment
                                using (SqlCommand command = new SqlCommand("DeleteSecondarySegment", connection, transaction))
                                {
                                    command.CommandType = CommandType.StoredProcedure;
                                    command.Parameters.AddWithValue("@SecondarySegmentID", id);
                                    command.ExecuteNonQuery();
                                }

                                // Log the deletion
                                //gbl.InsertGPSDetails(
                                //    Convert.ToInt32(DeleteCoreSecondarySegmentobj.Company_ID.ToString()),
                                //    branchID,
                                //    user.User_ID,
                                //    Common.GetObjectID("CoreSecondarySegmentMaster"),
                                //    id,
                                //    0,
                                //    0,
                                //    "Deleted Secondary Segment",
                                //    false,
                                //    Convert.ToInt32(DeleteCoreSecondarySegmentobj.MenuID),
                                //    Convert.ToDateTime(DeleteCoreSecondarySegmentobj.LoggedINDateTime));
                            }

                            transaction.Commit();
                            msg += CommonFunctionalities.GetResourceString(DeleteCoreSecondarySegmentobj.GeneralCulture.ToString(), "deletedsuccessfully").ToString();
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            if (ex.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                            {
                                msg += CommonFunctionalities.GetResourceString(DeleteCoreSecondarySegmentobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                            }
                            else
                            {
                                throw;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(msg);
        }
        #endregion


        #region ::: Export Uday Kumar J B 09-08-2024 :::
        /// <summary>
        /// To Export Secondary Segment
        /// </summary>
        /// 

        public static async Task<object> Export(ExportCoreSecondarySegmentList ExportCoreSecondarySegmentobj, string connString, string filters, string advnceFilters, string sidx, string sord)
        {
            int Count = 0;
            string msg = " ";
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));

            try
            {
                DataTable DtData = new DataTable();
                int BranchID = Convert.ToInt32(ExportCoreSecondarySegmentobj.Branch);
                string AppPath = string.Empty;
                List<SecondarySegmentMaster> secondarySegmentMasters = new List<SecondarySegmentMaster>();

                string yesE = CommonFunctionalities.GetResourceString(ExportCoreSecondarySegmentobj.GeneralCulture.ToString(), "Yes").ToString();
                string NoE = CommonFunctionalities.GetResourceString(ExportCoreSecondarySegmentobj.GeneralCulture.ToString(), "No").ToString();
                string yesL = CommonFunctionalities.GetResourceString(ExportCoreSecondarySegmentobj.UserCulture.ToString(), "Yes").ToString();
                string NoL = CommonFunctionalities.GetResourceString(ExportCoreSecondarySegmentobj.UserCulture.ToString(), "No").ToString();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Sel_AM_ERP_SelectSecondarySegments", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@PrimarySegmentID", ExportCoreSecondarySegmentobj.PrimarySegmentID);
                        cmd.Parameters.AddWithValue("@LanguageID", ExportCoreSecondarySegmentobj.LanguageID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var secondarySegment = new SecondarySegmentMaster
                                {
                                    SecondarySegment_Description = reader.IsDBNull(3) ? reader.GetString(1) : reader.GetString(3),
                                    SecondarySegment_IsActive = reader.GetBoolean(2) ? (ExportCoreSecondarySegmentobj.LanguageID == Convert.ToInt32(ExportCoreSecondarySegmentobj.GeneralLanguageID) ? yesE : yesL) : (ExportCoreSecondarySegmentobj.LanguageID == Convert.ToInt32(ExportCoreSecondarySegmentobj.GeneralLanguageID) ? NoE : NoL)
                                };
                                secondarySegmentMasters.Add(secondarySegment);
                            }
                        }
                    }

                    var IQSecondarySegmentMaster = secondarySegmentMasters.AsQueryable().OrderByField(sidx, sord);

                    // Apply standard filters if present
                    if (!string.IsNullOrEmpty(filters) && filters != "null" && filters != "undefined")
                    {
                        Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                        if (filtersObj.rules.Count > 0)
                            IQSecondarySegmentMaster = IQSecondarySegmentMaster.FilterSearch(filtersObj);
                    }

                    // Apply advanced filters if present
                    if (!string.IsNullOrEmpty(advnceFilters) && advnceFilters != "null")
                    {
                        AdvanceFilter advnfilter = JObject.Parse(Common.DecryptString(advnceFilters)).ToObject<AdvanceFilter>();
                        IQSecondarySegmentMaster = IQSecondarySegmentMaster.AdvanceSearch(advnfilter);
                    }

                    IQSecondarySegmentMaster = IQSecondarySegmentMaster.OrderByField<SecondarySegmentMaster>(sidx, sord);

                    var SecondarySegmentArray = from a in IQSecondarySegmentMaster
                                                select new
                                                {
                                                    a.SecondarySegment_Description,
                                                    a.SecondarySegment_IsActive
                                                };

                    Count = SecondarySegmentArray.AsEnumerable().Count();
                    if (Count > 0)
                    {
                        DtData.Columns.Add(CommonFunctionalities.GetResourceString(ExportCoreSecondarySegmentobj.GeneralCulture.ToString(), "SecondarySegment").ToString());
                        DtData.Columns.Add(CommonFunctionalities.GetResourceString(ExportCoreSecondarySegmentobj.GeneralCulture.ToString(), "Active").ToString());

                        for (int i = 0; i < Count; i++)
                        {
                            if (i < SecondarySegmentArray.Count())
                            {
                                DtData.Rows.Add(SecondarySegmentArray.ElementAt(i).SecondarySegment_Description, SecondarySegmentArray.ElementAt(i).SecondarySegment_IsActive);
                            }
                        }

                        DataTable DtCriteria = new DataTable();
                        DtCriteria.Columns.Add(CommonFunctionalities.GetResourceString(ExportCoreSecondarySegmentobj.UserCulture.ToString(), "PrimarySegment").ToString());
                        DtCriteria.Rows.Add(Common.DecryptString(ExportCoreSecondarySegmentobj.PrimarySegmentName));

                        DataTable DtAlignment = new DataTable();
                        DtAlignment.Columns.Add("SecondarySegment");
                        DtAlignment.Columns.Add("Active");
                        DtAlignment.Rows.Add(0, 1);

                        ExportList reportExportList = new ExportList
                        {
                            Company_ID = ExportCoreSecondarySegmentobj.Company_ID,
                            Branch = ExportCoreSecondarySegmentobj.Branch,
                            dt1 = DtCriteria,
                            dt = DtData,
                            FileName = "SecondarySegment",
                            Header = CommonFunctionalities.GetResourceString(ExportCoreSecondarySegmentobj.UserCulture.ToString(), "SecondarySegment").ToString(),
                            exprtType = ExportCoreSecondarySegmentobj.exprtType,
                            UserCulture = ExportCoreSecondarySegmentobj.UserCulture
                        };

                        var result = await DocumentExport.Export(reportExportList, connString, LogException);
                        return result.Value;


                        //return DocumentExport.Export(reportExportList, connString, LogException);
                        //  ReportExport.Export(ExportCoreSecondarySegmentobj.exprtType, DtData, DtCriteria, DtAlignment, "SecondarySegment",GetGlobalResourceObject(ExportCoreSecondarySegmentobj.GeneralCulture.ToString(), "SecondarySegment").ToString());
                        // gbl.InsertGPSDetails(Convert.ToInt32(ExportCoreSecondarySegmentobj.Company_ID.ToString()), BranchID, User.User_ID, Common.GetObjectID("CoreSecondarySegmentMaster"), 0, 0, 0, "Secondary Segment-Export", false, Convert.ToInt32(ExportCoreSecondarySegmentobj.MenuID), Convert.ToDateTime(ExportCoreSecondarySegmentobj.LoggedINDateTime));
                    }
                    msg = "No Records in the Grid to Export";
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return msg;
        }

        #endregion


        #region ::: CheckSecondarySegment Uday Kumar J B 09-08-2024:::
        /// <summary>
        /// To Check Secondary Segment already exists 
        /// </summary>
        /// 

        public static IActionResult CheckSecondarySegment(string connString, CheckSecondarySegmentList CheckSecondarySegmentobj)
        {
            int Count = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                string SecondarySegment = Uri.UnescapeDataString(CheckSecondarySegmentobj.SecondarySegment);

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    using (SqlCommand command = new SqlCommand("Up_Sel_AM_ERP_CheckSecondarySegment", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@PrimarySegmentID", CheckSecondarySegmentobj.PrimarySegmentID);
                        command.Parameters.AddWithValue("@SecondarySegment", SecondarySegment);
                        command.Parameters.AddWithValue("@SecondarySegmentID", CheckSecondarySegmentobj.SecondarySegmentID);

                        connection.Open();
                        Count = (int)command.ExecuteScalar();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Count);
        }
        #endregion


        #region ::: CheckSecondarySegmentLocale Uday Kumar J B 09-08-2024:::
        /// <summary>
        /// To Check SecondarySegment Locale already exists 
        /// </summary>
        /// 

        public static IActionResult CheckSecondarySegmentLocale(string connString, CheckSecondarySegmentLocaleList CheckSecondarySegmentLocaleobj)
        {
            int Count = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                string SecondarySegment = Common.DecryptString(CheckSecondarySegmentLocaleobj.SecondarySegment);
                int Language_ID = Convert.ToInt32(CheckSecondarySegmentLocaleobj.UserLanguageID);

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    using (SqlCommand command = new SqlCommand("Up_Sel_AM_ERP_CheckSecondarySegmentLocale", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@PrimarySegmentID", CheckSecondarySegmentLocaleobj.PrimarySegmentID);
                        command.Parameters.AddWithValue("@SecondarySegment", SecondarySegment);
                        command.Parameters.AddWithValue("@SecondarySegmentLocaleID", CheckSecondarySegmentLocaleobj.SecondarySegmentLocaleID);
                        command.Parameters.AddWithValue("@LanguageID", Language_ID);

                        connection.Open();
                        Count = (int)command.ExecuteScalar();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Count);
        }
        #endregion


        #region ::: CoreSecondarySegmentMaster List and Obj  Classes Uday Kumar J B 09-08-2024:::
        /// <summary>
        /// CoreSecondarySegmentMaster List and Obj  Classes
        /// </summary>
        /// 
        public class CheckSecondarySegmentLocaleList
        {
            public int PrimarySegmentID { get; set; }
            public string SecondarySegment { get; set; }
            public int SecondarySegmentLocaleID { get; set; }
            public int UserLanguageID { get; set; }
        }
        public class CheckSecondarySegmentList
        {
            public int PrimarySegmentID { get; set; }
            public string SecondarySegment { get; set; }

            public int SecondarySegmentID { get; set; }
        }

        public class ExportCoreSecondarySegmentList
        {
            public int Branch { get; set; }
            public List<GNM_User> UserDetails { get; set; }
            public string GeneralCulture { get; set; }
            public string UserCulture { get; set; }
            public string PrimarySegmentName { get; set; }
            public int exprtType { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public int PrimarySegmentID { set; get; }
            public int LanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filter { get; set; }
            public string advanceFilter { get; set; }
        }

        public class DeleteCoreSecondarySegmentList
        {
            public string key { get; set; }
            public int Branch { get; set; }
            public List<GNM_User> UserDetails { get; set; }

            public string GeneralCulture { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public int MenuID { get; set; }
            public int Company_ID { get; set; }
        }

        public class UpdateLocaleListb
        {
            public string data { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Language_ID { get; set; }

        }
        public class SaveCoreSecondarySegmentMasterList
        {
            public int Branch { get; set; }
            public List<GNM_User> UserDetails { get; set; }
            public string data { get; set; }
            public int User_ID { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }

        public class SelectParticularSecondarySegmentList
        {
            public int UserLanguageID { get; set; }
            public int SecondarySegmentID { get; set; }
        }
        public class SelectReferenceMasterCoreSecondarySegmentList
        {
            public int UserLanguageID { get; set; }
            public string UserLanguageCode { get; set; }
            public string GeneralLanguageCode { get; set; }
        }

        public class SelectCoreSecondarySegmentList
        {
            public string GeneralCulture { get; set; }
            public string UserCulture { get; set; }
            public int PrimarySegmentID { get; set; }
            public int LanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        #endregion


        #region ::: SecondarySegmentMaster Uday Kumar J B 09-08-2024:::
        /// <summary>
        /// Secondary Segment Master
        /// </summary>
        public class SecondarySegmentMaster
        {
            public int SecondarySegment_ID
            {
                get;
                set;
            }

            public string SecondarySegment_Description
            {
                get;
                set;
            }

            public string SecondarySegment_IsActive
            {
                get;
                set;
            }
        }
        #endregion 


    }
}
