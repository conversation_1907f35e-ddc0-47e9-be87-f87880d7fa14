﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.PRM_PartsCategoryServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class PRM_PartsCategoryController : ApiController
    {

        #region ::: Select Uday Kumar J B 14-08-2024 :::
        /// <summary>
        /// To Select PartsCategory
        /// </summary>
        /// 
        [Route("api/PRM_PartsCategory/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] SelectPRM_PartsCategoryList SelectPRM_PartsCategoryobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = " ";


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = PRM_PartsCategoryServices.Select(connString, SelectPRM_PartsCategoryobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Save Uday Kumar J B 14-08-2024 :::
        /// <summary>
        /// To Insert and Update PartsCategory
        /// </summary>
        /// 
        [Route("api/PRM_PartsCategory/Save")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Save([FromBody] SavePRM_PartsCategoryList SavePRM_PartsCategoryobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = PRM_PartsCategoryServices.Save(connString, SavePRM_PartsCategoryobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckPartsCategory Uday Kumar J B 14-08-2024:::
        /// <summary>
        /// To Check PartsCategory already exists 
        /// </summary>
        /// 
        [Route("api/PRM_PartsCategory/CheckPartsCategory")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckPartsCategory([FromBody] CheckPartsCategoryList CheckPartsCategoryobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = PRM_PartsCategoryServices.CheckPartsCategory(connString, CheckPartsCategoryobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckPartsCategory Uday Kumar J B 14-08-2024:::
        /// <summary>
        /// To Check PartsCategory already exists 
        /// </summary>
        /// 
        [Route("api/PRM_PartsCategory/SelectParticularPartsCategory")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularPartsCategory([FromBody] SelectParticularPartsCategoryList SelectParticularPartsCategoryobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = PRM_PartsCategoryServices.SelectParticularPartsCategory(connString, SelectParticularPartsCategoryobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Delete Uday Kumar J B 14-08-2024 :::
        /// <summary>
        /// To Delete PartsCategory
        /// </summary>
        /// 
        [Route("api/PRM_PartsCategory/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeletePRM_PartsCategoryList DeletePRM_PartsCategoryobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = PRM_PartsCategoryServices.Delete(connString, DeletePRM_PartsCategoryobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Delete Uday Kumar J B 14-08-2024 :::
        /// <summary>
        /// To Delete PartsCategory
        /// </summary>
        /// 
        [Route("api/PRM_PartsCategory/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Export([FromBody] ExportPRM_PartsCategoryList ExportPRM_PartsCategoryobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = PRM_PartsCategoryServices.Export(connString, ExportPRM_PartsCategoryobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: UpdateLocale Uday Kumar J B 14-08-2024 :::
        /// <summary>
        /// To Update PartsCategory Locale
        /// </summary>
        /// 
        [Route("api/PRM_PartsCategory/UpdateLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateLocale([FromBody] UpdateLocaleListb UpdateLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = PRM_PartsCategoryServices.UpdateLocale(connString, UpdateLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckPartsCategoryLocale Uday Kumar J B 14-08-2024 :::
        /// <summary>
        /// To Check PartsCategoryLocale already exists 
        /// </summary>
        /// 
        [Route("api/PRM_PartsCategory/CheckPartsCategoryLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckPartsCategoryLocale([FromBody] CheckPartsCategoryLocaleList CheckPartsCategoryLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = PRM_PartsCategoryServices.CheckPartsCategoryLocale(connString, CheckPartsCategoryLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


    }
}