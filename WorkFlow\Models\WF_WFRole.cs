//------------------------------------------------------------------------------
// <auto-generated>
//    This code was generated from a template.
//
//    Manual changes to this file may cause unexpected behavior in your application.
//    Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace WorkFlow.Models
{
    using System;
    using System.Collections.Generic;
    
    public partial class WF_WFRole
    {
        public WF_WFRole()
        {
            this.GNM_WFRoleUser = new HashSet<WF_WFRoleUser>();
            this.GNM_WFStepLink = new HashSet<WF_WFStepLink>();
            this.GNM_WFRoleLocale = new HashSet<WF_WFRoleLocale>();
        }
    
        public int WFRole_ID { get; set; }
        public int WorkFlow_ID { get; set; }
        public string WFRole_Name { get; set; }
        public bool WfRole_IsAdmin { get; set; }
        public bool WfRole_AutoAllocationAllowed { get; set; }
        public Nullable<bool> WFRole_IsRoleExternal { get; set; }
        public Nullable<int> WFRole_ExternalCompany_ID { get; set; }
    
        public virtual ICollection<WF_WFRoleUser> GNM_WFRoleUser { get; set; }
        public virtual WF_WorkFlow GNM_WorkFlow { get; set; }
        public virtual ICollection<WF_WFStepLink> GNM_WFStepLink { get; set; }
        public virtual ICollection<WF_WFRoleLocale> GNM_WFRoleLocale { get; set; }
    }
}
