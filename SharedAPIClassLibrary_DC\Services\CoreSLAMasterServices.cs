﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreSLAMasterServices
    {
        public static string AppPath = null;
        #region ::: Select :::
        /// <summary>
        /// Select
        /// </summary>
        /// <param name="SelectObj"></param>
        /// <param name="sidx"></param>
        /// <param name="rows"></param>
        /// <param name="page"></param>
        /// <param name="sord"></param>
        /// <param name="_search"></param>
        /// <param name="nd"></param>
        /// <param name="filters"></param>
        /// <param name="advnce"></param>
        /// <param name="advanceFilter"></param>
        /// <param name="LogException"></param>
        /// <param name="constring"></param>
        /// <returns></returns>
        public static IActionResult Select(SelectTaxStructureMappingList SelectObj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advanceFilter, int LogException, string constring)
        {
            try
            {
                int Count = 0;
                int Total = 0;
                int CompanyID = Convert.ToInt32(SelectObj.Company_ID);
                List<GNM_RefMaster> refMaster = new List<GNM_RefMaster>();
                List<GNM_RefMasterDetail> refDetail = new List<GNM_RefMasterDetail>();
                List<GNM_RefMasterDetailLocale> refDetailLocale = new List<GNM_RefMasterDetailLocale>();
                //List<GNM_RefMaster> refMaster = refClient.GNM_RefMaster.ToList();
                //List<GNM_RefMasterDetail> refDetail = refClient.GNM_RefMasterDetail.Where(a => a.Company_ID == CompanyID).ToList();//Updated by Ravi on 05-Jan-2015 for HelpDesk QA Corrections
                //List<GNM_RefMasterDetailLocale> refDetailLocale = refClient.GNM_RefMasterDetailLocale.ToList();
                List<HD_ServiceLevelAgreement> IESLALi = new List<HD_ServiceLevelAgreement>();
                IEnumerable<HD_ServiceLevelAgreement> IESLAList = default(dynamic);
                //Session["PartyNameOFSLAFiltering"] = "General - Not Party Specific";
                //if (SelectObj.PartyID == -1)
                //    IESLAList = SLAMasterClient.HD_ServiceLevelAgreement.Where(i => i.Company_ID == CompanyID && i.Party_ID == null);
                //else
                //{
                //    IESLAList = SLAMasterClient.HD_ServiceLevelAgreement.Where(i => i.Company_ID == CompanyID && i.Party_ID == SelectObj.PartyID);
                //    //Session["PartyNameOFSLAFiltering"] = PartyClient.GNM_Party.Where(a => a.Party_ID == SelectObj.PartyID).Select(i => i.Party_Name).FirstOrDefault();
                //}
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    string query = "UP_SELECT_AM_ERP_Select_SLAMaster";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.Add(new SqlParameter("@CompanyID", SqlDbType.Int) { Value = CompanyID });
                            command.Parameters.Add(new SqlParameter("@PartyID", SqlDbType.Int) { Value = SelectObj.PartyID });

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {


                                while (reader.Read())
                                {
                                    GNM_RefMaster refM = new GNM_RefMaster
                                    {
                                        RefMaster_ID = reader.GetInt32(reader.GetOrdinal("RefMaster_ID")),
                                        RefMaster_Name = reader.GetString(reader.GetOrdinal("RefMaster_Name")),
                                        IsCompanySpecific = reader.IsDBNull(reader.GetOrdinal("IsCompanySpecific")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsCompanySpecific")),
                                        ModifiedBy = reader.GetInt32(reader.GetOrdinal("ModifiedBy")),
                                        ModifiedDate = reader.GetDateTime(reader.GetOrdinal("ModifiedDate")),
                                        IsSystemMaster = reader.GetBoolean(reader.GetOrdinal("IsSystemMaster"))
                                    };
                                    refMaster.Add(refM);
                                }

                                // Move to the next result set
                                if (reader.NextResult())
                                {
                                    // Read GNM_RefMasterDetail
                                    while (reader.Read())
                                    {
                                        GNM_RefMasterDetail refDet = new GNM_RefMasterDetail
                                        {
                                            RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                            RefMasterDetail_IsActive = reader.GetBoolean(reader.GetOrdinal("RefMasterDetail_IsActive")),
                                            RefMasterDetail_Short_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Short_Name")),
                                            RefMasterDetail_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name")),
                                            Company_ID = reader.IsDBNull(reader.GetOrdinal("Company_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                            ModifiedBy = reader.GetInt32(reader.GetOrdinal("ModifiedBy")),
                                            ModifiedDate = reader.GetDateTime(reader.GetOrdinal("ModifiedDate")),
                                            RefMaster_ID = reader.GetInt32(reader.GetOrdinal("RefMaster_ID")),
                                            RefMasterDetail_IsDefault = reader.GetBoolean(reader.GetOrdinal("RefMasterDetail_IsDefault")),
                                            Region_ID = reader.IsDBNull(reader.GetOrdinal("Region_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Region_ID")),
                                            SystemCondition = reader.IsDBNull(reader.GetOrdinal("SystemCondition")) ? null : reader.GetString(reader.GetOrdinal("SystemCondition"))

                                        };
                                        refDetail.Add(refDet);
                                    }
                                }

                                // Move to the next result set
                                if (reader.NextResult())
                                {
                                    // Read GNM_RefMasterDetailLocale
                                    while (reader.Read())
                                    {
                                        GNM_RefMasterDetailLocale refDetailLoc = new GNM_RefMasterDetailLocale
                                        {
                                            RefMasterDetailLocale_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetailLocale_ID")),
                                            RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                            RefMaster_ID = reader.GetInt32(reader.GetOrdinal("RefMaster_ID")),
                                            RefMasterDetail_Short_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Short_Name")),
                                            RefMasterDetail_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name")),
                                            Language_ID = reader.GetInt32(reader.GetOrdinal("Language_ID"))
                                        };
                                        refDetailLocale.Add(refDetailLoc);
                                    }
                                }

                                // Move to the next result set for HD_ServiceLevelAgreement
                                if (reader.NextResult())
                                {
                                    // Read HD_ServiceLevelAgreement
                                    while (reader.Read())
                                    {
                                        HD_ServiceLevelAgreement sla = new HD_ServiceLevelAgreement
                                        {
                                            ServiceLevelAgreement_ID = reader.GetInt32(reader.GetOrdinal("ServiceLevelAgreement_ID")),
                                            CallComplexity_ID = reader.GetInt32(reader.GetOrdinal("CallComplexity_ID")),
                                            CallPriority_ID = reader.GetInt32(reader.GetOrdinal("CallPriority_ID")),
                                            ServiceLevelAgreement_Hours = reader.GetDecimal(reader.GetOrdinal("ServiceLevelAgreement_Hours")),
                                            Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                            ServiceLevelAgreementHours_IsActive = reader.GetBoolean(reader.GetOrdinal("ServiceLevelAgreementHours_IsActive")),
                                            Party_ID = reader.IsDBNull(reader.GetOrdinal("Party_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Party_ID"))
                                        };

                                        IESLALi.Add(sla);
                                    }
                                    IESLAList = IESLALi.AsEnumerable();

                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                IQueryable<SLAMaster> IQSLAMaster = null;
                IEnumerable<SLAMaster> IESLAMasterArray = null;

                var jsonData = default(dynamic);
                var CallComplexities = default(dynamic);
                var CallPriorities = default(dynamic);
                if (Convert.ToInt32(SelectObj.UserLanguageID) == Convert.ToInt32(SelectObj.GeneralLanguageID))
                {
                    CallComplexities = from a in refMaster
                                       join b in refDetail on a.RefMaster_ID equals b.RefMaster_ID
                                       where a.RefMaster_Name == "CALLCOMPLEXITY" && b.RefMasterDetail_IsActive == true //&& b.Company_ID == CompanyID
                                       orderby b.RefMasterDetail_Name//added by kavitha -sorting
                                       select new
                                       {
                                           b.RefMasterDetail_ID,
                                           b.RefMasterDetail_Name
                                       };

                    CallPriorities = from a in refMaster
                                     join b in refDetail on a.RefMaster_ID equals b.RefMaster_ID
                                     where a.RefMaster_Name == "CALLPRIORITY" && b.RefMasterDetail_IsActive == true && b.Company_ID == CompanyID
                                     orderby b.RefMasterDetail_Name //added by kavitha -sorting
                                     select new
                                     {
                                         b.RefMasterDetail_ID,
                                         b.RefMasterDetail_Name
                                     };
                }
                else
                {
                    CallComplexities = from a in refMaster
                                       join b in refDetail on a.RefMaster_ID equals b.RefMaster_ID
                                       join c in refDetailLocale on b.RefMasterDetail_ID equals c.RefMasterDetail_ID
                                       where a.RefMaster_Name == "CALLCOMPLEXITY" && b.RefMasterDetail_IsActive == true && c.Language_ID == Convert.ToInt32(SelectObj.UserLanguageID) //&& b.Company_ID == CompanyID 
                                       orderby c.RefMasterDetail_Name//added by kavitha -sorting
                                       select new
                                       {
                                           c.RefMasterDetail_ID,
                                           c.RefMasterDetail_Name
                                       };

                    CallPriorities = from a in refMaster
                                     join b in refDetail on a.RefMaster_ID equals b.RefMaster_ID
                                     join c in refDetailLocale on b.RefMasterDetail_ID equals c.RefMasterDetail_ID
                                     where a.RefMaster_Name == "CALLPRIORITY" && b.RefMasterDetail_IsActive == true && b.Company_ID == CompanyID && c.Language_ID == Convert.ToInt32(SelectObj.UserLanguageID)
                                     orderby c.RefMasterDetail_Name//added by kavitha -sorting
                                     select new
                                     {
                                         c.RefMasterDetail_ID,
                                         c.RefMasterDetail_Name
                                     };
                }
                string ComplexityNames = "-1:--" + CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "select").ToString() + "--;";

                foreach (var ComplexityObj in CallComplexities)
                {
                    ComplexityNames = ComplexityNames + ComplexityObj.RefMasterDetail_ID + ":" + ComplexityObj.RefMasterDetail_Name + ";";
                }
                ComplexityNames = ComplexityNames.TrimEnd(new char[] { ';' });

                string PriorityNames = "-1:--" + CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "select").ToString() + "--;";
                foreach (var PriorityObj in CallPriorities)
                {
                    PriorityNames = PriorityNames + PriorityObj.RefMasterDetail_ID + ":" + PriorityObj.RefMasterDetail_Name + ";";
                }
                PriorityNames = PriorityNames.TrimEnd(new char[] { ';' });

                if (Convert.ToInt32(SelectObj.UserLanguageID) == Convert.ToInt32(SelectObj.GeneralLanguageID))
                {
                    IESLAMasterArray = from a in IESLAList
                                       join b in refDetail on a.CallComplexity_ID equals b.RefMasterDetail_ID
                                       join c in refDetail on a.CallPriority_ID equals c.RefMasterDetail_ID
                                       select new SLAMaster()
                                       {
                                           ServiceLevelAgreement_ID = a.ServiceLevelAgreement_ID,
                                           CallComplexity = b.RefMasterDetail_Name,
                                           CallPriority = c.RefMasterDetail_Name,
                                           ServiceLevelAgreement_Hours = a.ServiceLevelAgreement_Hours,
                                           ServiceLevelAgreementHours_IsActive = (a.ServiceLevelAgreementHours_IsActive == true ? "Yes" : "No")
                                       };
                }
                else
                {
                    IESLAMasterArray = from a in IESLAList
                                       join b in refDetailLocale on a.CallComplexity_ID equals b.RefMasterDetail_ID
                                       join c in refDetailLocale on a.CallPriority_ID equals c.RefMasterDetail_ID
                                       where c.Language_ID == Convert.ToInt32(SelectObj.UserLanguageID)
                                       select new SLAMaster()
                                       {
                                           ServiceLevelAgreement_ID = a.ServiceLevelAgreement_ID,
                                           CallComplexity = b.RefMasterDetail_Name,
                                           CallPriority = c.RefMasterDetail_Name,
                                           ServiceLevelAgreement_Hours = a.ServiceLevelAgreement_Hours,
                                           ServiceLevelAgreementHours_IsActive = (a.ServiceLevelAgreementHours_IsActive == true ? "Yes" : "No")
                                       };
                }

                IQSLAMaster = IESLAMasterArray.AsQueryable<SLAMaster>();


                if (_search)
                {
                    string decodedValue = Uri.UnescapeDataString(filters);
                    Filters filterObj = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<Filters>();
                    if (filterObj.rules.Count > 0)
                    {
                        IQSLAMaster = IQSLAMaster.FilterSearch<SLAMaster>(filterObj);
                    }
                }

                if (advnce)
                {
                    string decodedAdvanceFilter = HttpUtility.UrlDecode(advanceFilter);

                    AdvanceFilter advnfilterObj = JObject.Parse(Common.DecryptString(decodedAdvanceFilter)).ToObject<AdvanceFilter>();
                    IQSLAMaster = IQSLAMaster.AdvanceSearch<SLAMaster>(advnfilterObj);
                }
                IQSLAMaster = IQSLAMaster.OrderByField<SLAMaster>(sidx, sord);
                //Session["IQSLA"] = IQSLAMaster.AsEnumerable();
                Count = IQSLAMaster.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                //Added by Ravi on 05-Jan-2014 for HelpDesk QA Corrections Begin
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }
                //---End
                jsonData = new
                {
                    total = Total,
                    page = page,
                    data = (from a in IQSLAMaster.AsEnumerable()
                            select new
                            {
                                ID = a.ServiceLevelAgreement_ID,
                                //edit = "<img id='" + a.ServiceLevelAgreement_ID + "' src='" + AppPath + "/Content/edit.gif' key='" + a.ServiceLevelAgreement_ID + "' class='SLAMasterEdit' editmode='false'/>",
                                edit = "<a title='Edit' href='#' id='" + a.ServiceLevelAgreement_ID + "' src='" + AppPath + "/Content/edit.gif' key='" + a.ServiceLevelAgreement_ID + "' class='SLAMasterEdit font-icon-class' editmode='false' ><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input type='checkbox' key='" + a.ServiceLevelAgreement_ID + "' defaultchecked=''  id='chk" + a.ServiceLevelAgreement_ID + "' class='SLAMasterDelete'/>",
                                CallComplexity = a.CallComplexity,
                                //CallComplexity_ID=a.CallComplexity_ID,
                                CallPriority = a.CallPriority,
                                //CallPriority_ID=a.CallPriority_ID,
                                ServiceLevelAgreement_Hours = (a.ServiceLevelAgreement_Hours),
                                ServiceLevelAgreementHours_IsActive = a.ServiceLevelAgreementHours_IsActive
                            }).ToList().Paginate(page, rows),
                    records = Count,
                    PriorityNames,
                    ComplexityNames
                };

                return new JsonResult(jsonData);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return null;
        }
        #endregion
        #region ::: SelectParticularSLAMaster :::
        /// <summary>
        /// SelectParticularSLAMaster
        /// </summary>
        /// <param name="SelectParticularSLAMasterObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult SelectParticularSLAMaster(SelectParticularSLAMasterList SelectParticularSLAMasterObj, string connString, int LogException)
        {
            try
            {
                var x = default(dynamic);
                HD_ServiceLevelAgreement SLAMasterList = null;
                //HD_ServiceLevelAgreement SLAMasterList = SLAMasterClient.HD_ServiceLevelAgreement.Where(i => i.ServiceLevelAgreement_ID == SelectParticularSLAMasterObj.SLAMasterID).FirstOrDefault();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_SELECT_AM_ERP_SelectParticularSLAMaster_SLAMaster";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@ServiceLevelAgreement_ID", SelectParticularSLAMasterObj.SLAMasterID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {


                                if (reader.Read())
                                {
                                    SLAMasterList = new HD_ServiceLevelAgreement
                                    {
                                        ServiceLevelAgreement_ID = reader.GetInt32(reader.GetOrdinal("ServiceLevelAgreement_ID")),
                                        CallComplexity_ID = reader.GetInt32(reader.GetOrdinal("CallComplexity_ID")),
                                        CallPriority_ID = reader.GetInt32(reader.GetOrdinal("CallPriority_ID")),
                                        ServiceLevelAgreement_Hours = reader.GetDecimal(reader.GetOrdinal("ServiceLevelAgreement_Hours")),
                                        Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                        ServiceLevelAgreementHours_IsActive = reader.GetBoolean(reader.GetOrdinal("ServiceLevelAgreementHours_IsActive")),
                                        Party_ID = reader.IsDBNull(reader.GetOrdinal("Party_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Party_ID"))
                                    };
                                }


                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                x = new
                {
                    ServiceLevelAgreement_ID = SLAMasterList.ServiceLevelAgreement_ID,
                    CallComplexity_ID = SLAMasterList.CallComplexity_ID,
                    CallPriority_ID = SLAMasterList.CallPriority_ID,
                    ServiceLevelAgreement_Hours = SLAMasterList.ServiceLevelAgreement_Hours,
                    ServiceLevelAgreementHours_IsActive = SLAMasterList.ServiceLevelAgreementHours_IsActive,
                };
                return new JsonResult(x);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return null;
        }
        #endregion
        #region ::: Save :::
        /// <summary>
        /// Save
        /// </summary>
        /// <param name="SaveSLAMasterObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult Save(SaveSLAMasterList SaveSLAMasterObj, string connString, int LogException)
        {
            var Msg = string.Empty;
            try
            {


                var jObj = JObject.Parse(SaveSLAMasterObj.data);
                int Count = jObj["rows"].Count();
                int CompanyID = Convert.ToInt32(SaveSLAMasterObj.Company_ID);
                for (int i = 0; i < Count; i++)
                {

                    var jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["ServiceLevelAgreement_ID"]);
                    jTR.Read();
                    int ServiceLevelAgreement_ID = Convert.ToInt32(jTR.Value.ToString() == "" ? "0" : jTR.Value.ToString());

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["CallComplexity_ID"]);
                    jTR.Read();
                    int CallComplexity_ID = Convert.ToInt32(jTR.Value.ToString());

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["CallPriority_ID"]);
                    jTR.Read();
                    int CallPriority_ID = Convert.ToInt32(jTR.Value.ToString());

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["ServiceLevelAgreement_Hours"]);
                    jTR.Read();
                    decimal ServiceLevelAgreement_Hours = Convert.ToDecimal(jTR.Value.ToString());

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["ServiceLevelAgreementHours_IsActive"]);
                    jTR.Read();
                    bool ServiceLevelAgreementHours_IsActive = Convert.ToBoolean(jTR.Value.ToString());

                    HD_ServiceLevelAgreement UpdateSLAMaster = default(dynamic);
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_Save_AM_ERP_Save_SLAMaster";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@ServiceLevelAgreement_ID", ServiceLevelAgreement_ID);
                                command.Parameters.AddWithValue("@Company_ID", CompanyID);
                                command.Parameters.AddWithValue("@CallComplexity_ID", CallComplexity_ID);
                                command.Parameters.AddWithValue("@CallPriority_ID", CallPriority_ID);
                                command.Parameters.AddWithValue("@ServiceLevelAgreement_Hours", ServiceLevelAgreement_Hours);
                                command.Parameters.AddWithValue("@ServiceLevelAgreementHours_IsActive", ServiceLevelAgreementHours_IsActive);
                                command.Parameters.AddWithValue("@Party_ID", SaveSLAMasterObj.PartyID);

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                command.ExecuteNonQuery();
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                    if (UpdateSLAMaster != null)
                    {


                        //gbl.InsertGPSDetails(Convert.ToInt32(SaveSLAMasterObj.Company_ID.ToString()), Convert.ToInt32(SaveSLAMasterObj.Branch), SaveSLAMasterObj.User_ID, Common.GetObjectID("CoreSLAMaster"), UpdateSLAMaster.ServiceLevelAgreement_ID, 0, 0, "Updated Service Level Agreement", false, Convert.ToInt32(SaveSLAMasterObj.MenuID), Convert.ToDateTime(SaveSLAMasterObj.LoggedINDateTime));
                    }
                    else
                    {


                        //gbl.InsertGPSDetails(Convert.ToInt32(SaveSLAMasterObj.Company_ID.ToString()), Convert.ToInt32(SaveSLAMasterObj.Branch), SaveSLAMasterObj.User_ID, Common.GetObjectID("CoreSLAMaster"), NewRow.ServiceLevelAgreement_ID, 0, 0, "Inserted Service Level Agreement", false, Convert.ToInt32(SaveSLAMasterObj.MenuID), Convert.ToDateTime(SaveSLAMasterObj.LoggedINDateTime));
                    }

                }
                Msg = "Saved";
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }
                Msg = string.Empty;
            }
            return new JsonResult(Msg);

        }
        #endregion
        #region ::: Delete :::
        /// <summary>
        /// Delete
        /// </summary>
        /// <param name="DelObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult Delete(DeleteSLAMasterList DelObj, string connString, int LogException)
        {
            string Msg = string.Empty;
            try
            {
                var jObj = JObject.Parse(DelObj.key);
                int Count = jObj["rows"].Count();
                int CompanyID = Convert.ToInt32(DelObj.Company_ID);

                HD_ServiceLevelAgreement deleteRow = null;
                int ID = 0;
                for (int i = 0; i < Count; i++)
                {
                    var jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jTR.Read();
                    ID = Convert.ToInt32(jTR.Value);
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_Delete_AM_ERP_Delete_SLAMaster";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@ServiceLevelAgreement_ID", ID);
                                command.Parameters.AddWithValue("@Company_ID", CompanyID);

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                command.ExecuteNonQuery();
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                    //gbl.InsertGPSDetails(Convert.ToInt32(DelObj.Company_ID.ToString()), Convert.ToInt32(DelObj.Branch), DelObj.User_ID, Common.GetObjectID("CoreSLAMaster"), deleteRow.ServiceLevelAgreement_ID, 0, 0, "Deleted Service Level Agreement", false, Convert.ToInt32(DelObj.MenuID), Convert.ToDateTime(DelObj.LoggedINDateTime));
                }

                //gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"]), Convert.ToInt32(Session["Branch"]), Convert.ToInt32(Session["User_ID"]), Convert.ToInt32(Common.GetObjectID("CoreSLAMaster")), ID, 0, 0, "Delete", false);
                Msg += CommonFunctionalities.GetResourceString(DelObj.UserCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (Exception ex)
            {
                if (ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    Msg += CommonFunctionalities.GetResourceString(DelObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
            }
            return new JsonResult(Msg);
        }
        #endregion
        #region SelectList 
        /// <summary>
        /// SelectList
        /// </summary>
        /// <param name="SelectObj"></param>
        /// <param name="LogException"></param>
        /// <param name="constring"></param>
        /// <returns></returns>
        public static IQueryable<SLAMaster> SelectList(SelectTaxStructureMappingList SelectObj, int LogException, string constring)
        {
            try
            {
                int Count = 0;
                int Total = 0;
                int CompanyID = Convert.ToInt32(SelectObj.Company_ID);
                List<GNM_RefMaster> refMaster = new List<GNM_RefMaster>();
                List<GNM_RefMasterDetail> refDetail = new List<GNM_RefMasterDetail>();
                List<GNM_RefMasterDetailLocale> refDetailLocale = new List<GNM_RefMasterDetailLocale>();
                //List<GNM_RefMaster> refMaster = refClient.GNM_RefMaster.ToList();
                //List<GNM_RefMasterDetail> refDetail = refClient.GNM_RefMasterDetail.Where(a => a.Company_ID == CompanyID).ToList();//Updated by Ravi on 05-Jan-2015 for HelpDesk QA Corrections
                //List<GNM_RefMasterDetailLocale> refDetailLocale = refClient.GNM_RefMasterDetailLocale.ToList();
                List<HD_ServiceLevelAgreement> IESLALi = new List<HD_ServiceLevelAgreement>();
                IEnumerable<HD_ServiceLevelAgreement> IESLAList = default(dynamic);
                //Session["PartyNameOFSLAFiltering"] = "General - Not Party Specific";
                //if (SelectObj.PartyID == -1)
                //    IESLAList = SLAMasterClient.HD_ServiceLevelAgreement.Where(i => i.Company_ID == CompanyID && i.Party_ID == null);
                //else
                //{
                //    IESLAList = SLAMasterClient.HD_ServiceLevelAgreement.Where(i => i.Company_ID == CompanyID && i.Party_ID == SelectObj.PartyID);
                //    //Session["PartyNameOFSLAFiltering"] = PartyClient.GNM_Party.Where(a => a.Party_ID == SelectObj.PartyID).Select(i => i.Party_Name).FirstOrDefault();
                //}
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    string query = "UP_SELECT_AM_ERP_Select_SLAMaster";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.Add(new SqlParameter("@CompanyID", SqlDbType.Int) { Value = CompanyID });
                            command.Parameters.Add(new SqlParameter("@PartyID", SqlDbType.Int) { Value = SelectObj.PartyID });

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {


                                while (reader.Read())
                                {
                                    GNM_RefMaster refM = new GNM_RefMaster
                                    {
                                        RefMaster_ID = reader.GetInt32(reader.GetOrdinal("RefMaster_ID")),
                                        RefMaster_Name = reader.GetString(reader.GetOrdinal("RefMaster_Name")),
                                        IsCompanySpecific = reader.IsDBNull(reader.GetOrdinal("IsCompanySpecific")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsCompanySpecific")),
                                        ModifiedBy = reader.GetInt32(reader.GetOrdinal("ModifiedBy")),
                                        ModifiedDate = reader.GetDateTime(reader.GetOrdinal("ModifiedDate")),
                                        IsSystemMaster = reader.GetBoolean(reader.GetOrdinal("IsSystemMaster"))
                                    };
                                    refMaster.Add(refM);
                                }

                                // Move to the next result set
                                if (reader.NextResult())
                                {
                                    // Read GNM_RefMasterDetail
                                    while (reader.Read())
                                    {
                                        GNM_RefMasterDetail refDet = new GNM_RefMasterDetail
                                        {
                                            RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                            RefMasterDetail_IsActive = reader.GetBoolean(reader.GetOrdinal("RefMasterDetail_IsActive")),
                                            RefMasterDetail_Short_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Short_Name")),
                                            RefMasterDetail_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name")),
                                            Company_ID = reader.IsDBNull(reader.GetOrdinal("Company_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                            ModifiedBy = reader.GetInt32(reader.GetOrdinal("ModifiedBy")),
                                            ModifiedDate = reader.GetDateTime(reader.GetOrdinal("ModifiedDate")),
                                            RefMaster_ID = reader.GetInt32(reader.GetOrdinal("RefMaster_ID")),
                                            RefMasterDetail_IsDefault = reader.GetBoolean(reader.GetOrdinal("RefMasterDetail_IsDefault")),
                                            Region_ID = reader.IsDBNull(reader.GetOrdinal("Region_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Region_ID")),
                                            SystemCondition = reader.IsDBNull(reader.GetOrdinal("SystemCondition")) ? null : reader.GetString(reader.GetOrdinal("SystemCondition")),

                                        };
                                        refDetail.Add(refDet);
                                    }
                                }

                                // Move to the next result set
                                if (reader.NextResult())
                                {
                                    // Read GNM_RefMasterDetailLocale
                                    while (reader.Read())
                                    {
                                        GNM_RefMasterDetailLocale refDetailLoc = new GNM_RefMasterDetailLocale
                                        {
                                            RefMasterDetailLocale_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetailLocale_ID")),
                                            RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                            RefMaster_ID = reader.GetInt32(reader.GetOrdinal("RefMaster_ID")),
                                            RefMasterDetail_Short_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Short_Name")),
                                            RefMasterDetail_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name")),
                                            Language_ID = reader.GetInt32(reader.GetOrdinal("Language_ID"))
                                        };
                                        refDetailLocale.Add(refDetailLoc);
                                    }
                                }

                                // Move to the next result set for HD_ServiceLevelAgreement
                                if (reader.NextResult())
                                {
                                    // Read HD_ServiceLevelAgreement
                                    while (reader.Read())
                                    {
                                        HD_ServiceLevelAgreement sla = new HD_ServiceLevelAgreement
                                        {
                                            ServiceLevelAgreement_ID = reader.GetInt32(reader.GetOrdinal("ServiceLevelAgreement_ID")),
                                            CallComplexity_ID = reader.GetInt32(reader.GetOrdinal("CallComplexity_ID")),
                                            CallPriority_ID = reader.GetInt32(reader.GetOrdinal("CallPriority_ID")),
                                            ServiceLevelAgreement_Hours = reader.GetDecimal(reader.GetOrdinal("ServiceLevelAgreement_Hours")),
                                            Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                            ServiceLevelAgreementHours_IsActive = reader.GetBoolean(reader.GetOrdinal("ServiceLevelAgreementHours_IsActive")),
                                            Party_ID = reader.IsDBNull(reader.GetOrdinal("Party_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Party_ID"))
                                        };

                                        IESLALi.Add(sla);
                                    }
                                    IESLAList = IESLALi.AsEnumerable();

                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                IQueryable<SLAMaster> IQSLAMaster = null;
                IEnumerable<SLAMaster> IESLAMasterArray = null;

                var jsonData = default(dynamic);
                var CallComplexities = default(dynamic);
                var CallPriorities = default(dynamic);
                if (Convert.ToInt32(SelectObj.UserLanguageID) == Convert.ToInt32(SelectObj.GeneralLanguageID))
                {
                    CallComplexities = from a in refMaster
                                       join b in refDetail on a.RefMaster_ID equals b.RefMaster_ID
                                       where a.RefMaster_Name == "CALLCOMPLEXITY" && b.RefMasterDetail_IsActive == true //&& b.Company_ID == CompanyID
                                       orderby b.RefMasterDetail_Name//added by kavitha -sorting
                                       select new
                                       {
                                           b.RefMasterDetail_ID,
                                           b.RefMasterDetail_Name
                                       };

                    CallPriorities = from a in refMaster
                                     join b in refDetail on a.RefMaster_ID equals b.RefMaster_ID
                                     where a.RefMaster_Name == "CALLPRIORITY" && b.RefMasterDetail_IsActive == true && b.Company_ID == CompanyID
                                     orderby b.RefMasterDetail_Name //added by kavitha -sorting
                                     select new
                                     {
                                         b.RefMasterDetail_ID,
                                         b.RefMasterDetail_Name
                                     };
                }
                else
                {
                    CallComplexities = from a in refMaster
                                       join b in refDetail on a.RefMaster_ID equals b.RefMaster_ID
                                       join c in refDetailLocale on b.RefMasterDetail_ID equals c.RefMasterDetail_ID
                                       where a.RefMaster_Name == "CALLCOMPLEXITY" && b.RefMasterDetail_IsActive == true && c.Language_ID == Convert.ToInt32(SelectObj.UserLanguageID) //&& b.Company_ID == CompanyID 
                                       orderby c.RefMasterDetail_Name//added by kavitha -sorting
                                       select new
                                       {
                                           c.RefMasterDetail_ID,
                                           c.RefMasterDetail_Name
                                       };

                    CallPriorities = from a in refMaster
                                     join b in refDetail on a.RefMaster_ID equals b.RefMaster_ID
                                     join c in refDetailLocale on b.RefMasterDetail_ID equals c.RefMasterDetail_ID
                                     where a.RefMaster_Name == "CALLPRIORITY" && b.RefMasterDetail_IsActive == true && b.Company_ID == CompanyID && c.Language_ID == Convert.ToInt32(SelectObj.UserLanguageID)
                                     orderby c.RefMasterDetail_Name//added by kavitha -sorting
                                     select new
                                     {
                                         c.RefMasterDetail_ID,
                                         c.RefMasterDetail_Name
                                     };
                }
                string ComplexityNames = "-1:--" + CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "select").ToString() + "--;";

                foreach (var ComplexityObj in CallComplexities)
                {
                    ComplexityNames = ComplexityNames + ComplexityObj.RefMasterDetail_ID + ":" + ComplexityObj.RefMasterDetail_Name + ";";
                }
                ComplexityNames = ComplexityNames.TrimEnd(new char[] { ';' });

                string PriorityNames = "-1:--" + CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "select").ToString() + "--;";
                foreach (var PriorityObj in CallPriorities)
                {
                    PriorityNames = PriorityNames + PriorityObj.RefMasterDetail_ID + ":" + PriorityObj.RefMasterDetail_Name + ";";
                }
                PriorityNames = PriorityNames.TrimEnd(new char[] { ';' });

                if (Convert.ToInt32(SelectObj.UserLanguageID) == Convert.ToInt32(SelectObj.GeneralLanguageID))
                {
                    IESLAMasterArray = from a in IESLAList
                                       join b in refDetail on a.CallComplexity_ID equals b.RefMasterDetail_ID
                                       join c in refDetail on a.CallPriority_ID equals c.RefMasterDetail_ID
                                       select new SLAMaster()
                                       {
                                           ServiceLevelAgreement_ID = a.ServiceLevelAgreement_ID,
                                           CallComplexity = b.RefMasterDetail_Name,
                                           CallPriority = c.RefMasterDetail_Name,
                                           ServiceLevelAgreement_Hours = a.ServiceLevelAgreement_Hours,
                                           ServiceLevelAgreementHours_IsActive = (a.ServiceLevelAgreementHours_IsActive == true ? "Yes" : "No")
                                       };
                }
                else
                {
                    IESLAMasterArray = from a in IESLAList
                                       join b in refDetailLocale on a.CallComplexity_ID equals b.RefMasterDetail_ID
                                       join c in refDetailLocale on a.CallPriority_ID equals c.RefMasterDetail_ID
                                       where c.Language_ID == Convert.ToInt32(SelectObj.UserLanguageID)
                                       select new SLAMaster()
                                       {
                                           ServiceLevelAgreement_ID = a.ServiceLevelAgreement_ID,
                                           CallComplexity = b.RefMasterDetail_Name,
                                           CallPriority = c.RefMasterDetail_Name,
                                           ServiceLevelAgreement_Hours = a.ServiceLevelAgreement_Hours,
                                           ServiceLevelAgreementHours_IsActive = (a.ServiceLevelAgreementHours_IsActive == true ? "Yes" : "No")
                                       };
                }

                IQSLAMaster = IESLAMasterArray.AsQueryable<SLAMaster>();


                if (SelectObj.filters != "null" && SelectObj.filters != "undefined")
                {
                    string decodedValue = Uri.UnescapeDataString(SelectObj.filters);
                    Filters filterObj = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<Filters>();
                    if (filterObj.rules.Count > 0)
                    {
                        IQSLAMaster = IQSLAMaster.FilterSearch<SLAMaster>(filterObj);
                    }
                }

                if (SelectObj.advanceFilter != "null" && SelectObj.advanceFilter != "undefined")
                {
                    string decodedAdvanceFilter = HttpUtility.UrlDecode(SelectObj.advanceFilter);

                    AdvanceFilter advnfilterObj = JObject.Parse(Common.DecryptString(decodedAdvanceFilter)).ToObject<AdvanceFilter>();
                    IQSLAMaster = IQSLAMaster.AdvanceSearch<SLAMaster>(advnfilterObj);
                }
                IQSLAMaster = IQSLAMaster.OrderByField<SLAMaster>(SelectObj.sidx, SelectObj.sord);
                //Session["IQSLA"] = IQSLAMaster.AsEnumerable();

                return IQSLAMaster.AsQueryable();
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return null;
        }
        #endregion
        #region ::: GetCustomerDetails :::
        /// <summary>
        /// To get customer details
        /// </summary>

        public static IActionResult GetData(GetDataSLAMasterList GetDataObj, string connString, int LogException)
        {
            var like = GetDataObj.starts_with.Trim();
            List<GNM_Party> PartyList = new List<GNM_Party>();
            // PartyList = PartyClient.GNM_Party.Where(a => a.Party_Name.StartsWith(like)).Take(5).ToList();

            var jsonData = default(dynamic);

            int companyID = GetDataObj.Company_ID;

            using (SqlConnection conn = new SqlConnection(connString))
            {
                string query = "UP_Get_AM_ERP_GetData_SLAMaster";

                SqlCommand command = null;

                try
                {
                    using (command = new SqlCommand(query, conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@like", like);
                        command.Parameters.AddWithValue("@companyID", companyID);
                        command.Parameters.AddWithValue("@languageID", GetDataObj.LanguageID);
                        command.Parameters.AddWithValue("@generalLanguageID", GetDataObj.GeneralLanguageID);

                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }
                        using (SqlDataReader reader = command.ExecuteReader())
                        {


                            while (reader.Read())
                            {
                                PartyList.Add(new GNM_Party
                                {
                                    Party_ID = reader.GetInt32(reader.GetOrdinal("ID")),
                                    Party_Name = reader.GetString(reader.GetOrdinal("Name")),
                                    Party_Mobile = reader.GetString(reader.GetOrdinal("Mobile")),
                                    Party_Email = reader.GetString(reader.GetOrdinal("Email")),
                                    Party_Location = reader.GetString(reader.GetOrdinal("Location")),
                                });
                            }
                            jsonData = from a in PartyList
                                       select new
                                       {
                                           ID = a.Party_ID,
                                           Name = a.Party_Name == null ? "" : a.Party_Name,
                                           Mobile = a.Party_Mobile == null ? "" : a.Party_Mobile,
                                           Email = a.Party_Email == null ? "" : a.Party_Email,
                                           Location = a.Party_Location == null ? "" : a.Party_Location,
                                       };


                        }
                    }
                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }
                finally
                {
                    command.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion
        #region ::: Export :::
        public static async Task<object> Export(SelectTaxStructureMappingList ExportObj, string connString, int LogException)
        {
            IQueryable<SLAMaster> IQSLAMaster = null;
            DataTable Dt = new DataTable();
            int Count = 0;
            try
            {
                string Pname = "";

                if (ExportObj.PartyID == -1)
                    Pname = "General - Not Party Specific";

                else
                {

                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_Get_AM_ERP_Export_SLAMaster";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@PartyID", ExportObj.PartyID);

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }




                                Pname = command.ExecuteScalar() as string;



                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                }
                IQSLAMaster = ((IQueryable<SLAMaster>)SelectList(ExportObj, LogException, connString));
                var SLAMasterArray = from a in IQSLAMaster.AsEnumerable()
                                     select new
                                     {
                                         a.CallComplexity,
                                         a.CallPriority,
                                         a.ServiceLevelAgreement_Hours,
                                         a.ServiceLevelAgreementHours_IsActive
                                     };

                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "callcomplexity").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "callpriority").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "slahours").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "active").ToString());

                DataTable DtAlignment = new DataTable();
                DtAlignment.Columns.Add("Call Complexity");
                DtAlignment.Columns.Add("Call Priority");
                DtAlignment.Columns.Add("SLA Hours");
                DtAlignment.Columns.Add("Is Active?");
                DtAlignment.Rows.Add(0, 0, 2, 0);

                Count = SLAMasterArray.AsEnumerable().Count();
                if (Count > 0)
                {
                    for (int i = 0; i < Count; i++)
                    {
                        Dt.Rows.Add(SLAMasterArray.ElementAt(i).CallComplexity, SLAMasterArray.ElementAt(i).CallPriority, SLAMasterArray.ElementAt(i).ServiceLevelAgreement_Hours, SLAMasterArray.ElementAt(i).ServiceLevelAgreementHours_IsActive);
                    }

                    DataTable Dt1 = new DataTable();
                    Dt1.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "Party").ToString());
                    Dt1.Rows.Add(Pname);
                    ReportExportList reportExportList = new ReportExportList
                    {
                        Company_ID = ExportObj.Company_ID, // Assuming this is available in ExportObj
                        Branch = ExportObj.Branch_ID.ToString(),
                        GeneralLanguageID = ExportObj.LanguageID,
                        UserLanguageID = ExportObj.UserLanguageID,
                        Options = Dt1,
                        dt = Dt,
                        Alignment = DtAlignment,
                        FileName = "ServiceLevelAgreement", // Set a default or dynamic filename
                        Header = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "ServiceLevelAgreement").ToString(), // Set a default or dynamic header
                        exprtType = ExportObj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                        UserCulture = ExportObj.UserCulture
                    };

                    var result = await ReportExport.Export(reportExportList, connString, LogException);
                    return result.Value;

                    //return ReportExport.Export(reportExportList, connString, LogException);

                    //ReportExport.Export(ExportObj.exprtType, Dt, Dt1, DtAlignment, "ServiceLevelAgreement", CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "ServiceLevelAgreement").ToString());
                    //gbl.InsertGPSDetails(Convert.ToInt32(ExportObj.Company_ID.ToString()), Convert.ToInt32(ExportObj.Branch), ExportObj.User_ID, Common.GetObjectID("CoreSLAMaster"), 0, 0, 0, "Service Level Agreement-Export", false, Convert.ToInt32(ExportObj.MenuID), Convert.ToDateTime(ExportObj.LoggedINDateTime));
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return null;

        }
        #endregion
        #region ::: SLAAlreadyExits :::
        public static IActionResult SLAAlreadyExits(SLAAlreadyExitsList SLAAlreadyExitsObj, string connString, int LogException)
        {
            bool repeate = true;
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_Chk_AM_ERP_SLAAlreadyExits_SLAMaster";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@prim", SLAAlreadyExitsObj.prim);
                            command.Parameters.AddWithValue("@sec", SLAAlreadyExitsObj.sec);
                            command.Parameters.AddWithValue("@pid", SLAAlreadyExitsObj.pid);
                            command.Parameters.AddWithValue("@ActualCC", SLAAlreadyExitsObj.ActualCC);
                            command.Parameters.AddWithValue("@ActualCP", SLAAlreadyExitsObj.ActualCP);
                            SqlParameter repeateParam = new SqlParameter("@repeate", SqlDbType.Bit);
                            repeateParam.Direction = ParameterDirection.Output;
                            command.Parameters.Add(repeateParam);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }




                            command.ExecuteNonQuery();

                            repeate = (bool)repeateParam.Value;



                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                //if (SLAAlreadyExitsObj.prim == SLAAlreadyExitsObj.ActualCC && SLAAlreadyExitsObj.sec == SLAAlreadyExitsObj.ActualCP)
                //{
                //    repeate = false;
                //}
                //else if (SLAAlreadyExitsObj.prim != SLAAlreadyExitsObj.ActualCC && SLAAlreadyExitsObj.sec != SLAAlreadyExitsObj.ActualCP)
                //{
                //    if (SLAAlreadyExitsObj.pid == -1)
                //        repeate = SLAMasterClient.HD_ServiceLevelAgreement.Where(a => a.CallComplexity_ID == SLAAlreadyExitsObj.prim && a.CallPriority_ID == SLAAlreadyExitsObj.sec && a.Party_ID == null && a.CallComplexity_ID != SLAAlreadyExitsObj.ActualCC && a.CallPriority_ID != SLAAlreadyExitsObj.ActualCP).Count() > 0 ? true : false;
                //    else
                //        repeate = SLAMasterClient.HD_ServiceLevelAgreement.Where(a => a.CallComplexity_ID == SLAAlreadyExitsObj.prim && a.CallPriority_ID == SLAAlreadyExitsObj.sec && a.Party_ID == SLAAlreadyExitsObj.pid && a.CallComplexity_ID != SLAAlreadyExitsObj.ActualCC && a.CallPriority_ID != SLAAlreadyExitsObj.ActualCP).Count() > 0 ? true : false;
                //}
                //else if (SLAAlreadyExitsObj.prim != SLAAlreadyExitsObj.ActualCC && SLAAlreadyExitsObj.sec == SLAAlreadyExitsObj.ActualCP)
                //{
                //    if (SLAAlreadyExitsObj.pid == -1)
                //        repeate = SLAMasterClient.HD_ServiceLevelAgreement.Where(a => a.CallComplexity_ID == prim && a.CallPriority_ID == sec && a.Party_ID == null && a.CallComplexity_ID != ActualCC).Count() > 0 ? true : false;
                //    else
                //        repeate = SLAMasterClient.HD_ServiceLevelAgreement.Where(a => a.CallComplexity_ID == prim && a.CallPriority_ID == sec && a.Party_ID == pid && a.CallComplexity_ID != ActualCC).Count() > 0 ? true : false;
                //}
                //else if (SLAAlreadyExitsObj.prim == SLAAlreadyExitsObj.ActualCC && SLAAlreadyExitsObj.sec != SLAAlreadyExitsObj.ActualCP)
                //{
                //    if (SLAAlreadyExitsObj.pid == -1)
                //        repeate = SLAMasterClient.HD_ServiceLevelAgreement.Where(a => a.CallComplexity_ID == prim && a.CallPriority_ID == sec && a.Party_ID == null && a.CallPriority_ID != ActualCP).Count() > 0 ? true : false;
                //    else
                //        repeate = SLAMasterClient.HD_ServiceLevelAgreement.Where(a => a.CallComplexity_ID == prim && a.CallPriority_ID == sec && a.Party_ID == pid && a.CallPriority_ID != ActualCP).Count() > 0 ? true : false;
                //}
            }
            catch (Exception e) { }
            return new JsonResult(repeate);
        }
        #endregion
    }


    #region TaxStructureMappingListsAndObj Vinay 23/9/24
    public class SelectParticularSLAMasterList
    {
        public int SLAMasterID { get; set; }
    }
    public class SelectTaxStructureMappingList
    {

        public int Company_ID { get; set; }
        public int PartyID { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public string UserCulture { get; set; }

        //select
        public string GeneralCulture { get; set; }
        public int exprtType { get; set; }
        public int Branch { get; set; }
        public int User_ID { get; set; }
        public int MenuID { get; set; }
        public DateTime LoggedINDateTime { set; get; }
        public string filters { get; set; }
        public string advanceFilter { get; set; }
        public string sidx { get; set; }
        public string sord { get; set; }
        public int Branch_ID { get; set; }
        public int LanguageID { get; set; }
        //export
    }
    public class SaveSLAMasterList
    {
        public string data { get; set; }
        public int Company_ID { set; get; }
        public int PartyID { set; get; }
        public int Branch { set; get; }
        public int User_ID { set; get; }
        public int MenuID { set; get; }
        public DateTime LoggedINDateTime { set; get; }
    }
    public class DeleteSLAMasterList
    {
        public string key { set; get; }
        public int Company_ID { set; get; }
        public int Branch { get; set; }
        public int User_ID { get; set; }
        public int MenuID { get; set; }
        public DateTime LoggedINDateTime { get; set; }
        public string UserCulture { set; get; }

    }
    public class GetDataSLAMasterList
    {
        public string starts_with { set; get; }
        public int Company_ID { get; set; }
        public int LanguageID { set; get; }
        public int GeneralLanguageID { set; get; }
    }
    public class SLAAlreadyExitsList
    {
        public int prim { set; get; }
        public int sec { set; get; }
        public int pid { set; get; }
        public int ActualCC { set; get; }
        public int ActualCP { set; get; }
    }
    #endregion
    #region Classes Vinay 23/9/24
    /// <summary>
    /// TaxStructureMappingListsAndClasses
    /// </summary>
    public partial class HD_ServiceLevelAgreement
    {
        public int ServiceLevelAgreement_ID { get; set; }
        public int CallComplexity_ID { get; set; }
        public int CallPriority_ID { get; set; }
        public decimal ServiceLevelAgreement_Hours { get; set; }
        public int Company_ID { get; set; }
        public bool ServiceLevelAgreementHours_IsActive { get; set; }
        public Nullable<int> Party_ID { get; set; }

        public virtual HD_ServiceLevelAgreement HD_ServiceLevelAgreement1 { get; set; }
        public virtual HD_ServiceLevelAgreement HD_ServiceLevelAgreement2 { get; set; }
    }
    public class SLAMaster
    {
        public int ServiceLevelAgreement_ID
        {
            get;
            set;
        }
        public int CallComplexity_ID
        {
            get;
            set;
        }
        public int CallPriority_ID
        {
            get;
            set;
        }
        public decimal ServiceLevelAgreement_Hours
        {
            get;
            set;
        }
        public string CallComplexity
        {
            get;
            set;
        }
        public string CallPriority
        {
            get;
            set;
        }
        public string ServiceLevelAgreementHours_IsActive
        {
            get;
            set;
        }
    }
    #endregion
}
