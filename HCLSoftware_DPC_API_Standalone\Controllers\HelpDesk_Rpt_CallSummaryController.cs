﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.HelpDesk_Rpt_CallSummaryServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class HelpDesk_Rpt_CallSummaryController : ApiController
    {


        #region ::: GetCallOwners Uday Kumar J B 12-11-2024:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDesk_Rpt_CallSummary/GetCallOwners")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetCallOwners([FromBody] GetCallOwnersList GetCallOwnersobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Rpt_CallSummaryServices.GetCallOwners(GetCallOwnersobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Check The Date Uday Kumar J B 11-11-2024:::
        /// <summary>
        /// To check the Date 
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Rpt_CallSummary/CheckValidDate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckValidDate([FromBody] CheckValidDateList CheckValidDateobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Rpt_CallSummaryServices.CheckValidDate(CheckValidDateobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Select Uday Kumar J B issue  12-11-2024:::
        /// <summary>
        /// To Select 
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDesk_Rpt_CallSummary/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] SelectCallSummaryList SelectCallSummaryobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Rpt_CallSummaryServices.Select(SelectCallSummaryobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Export Uday Kumar J B issue  12-11-2024:::
        /// <summary>
        /// Export
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Rpt_CallSummary/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] CallSummaryExportList CallSummaryExportobj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = CallSummaryExportobj.sidx;
            string sord = CallSummaryExportobj.sord;
            string filter = CallSummaryExportobj.filter;
            string advnceFilter = CallSummaryExportobj.advanceFilter;

            try
            {


                Object Response = await HelpDesk_Rpt_CallSummaryServices.Export(CallSummaryExportobj, connstring, LogException, filter, advnceFilter, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion


    }
}