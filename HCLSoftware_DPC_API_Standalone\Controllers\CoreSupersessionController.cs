﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreSupersessionServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreSupersessionController : ApiController
    {

        #region ::: Select Uday Kumar J B 18-07-2024 :::
        /// <summary>
        /// To Select All Supersession 
        /// </summary>
        /// 
        [Route("api/CoreSupersession/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] SelectCoreSupersessionList SelectCoreSupersessionobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreSupersessionServices.Select(connstring, SelectCoreSupersessionobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Save Uday Kumar J B 18-07-2024:::
        /// <summary>
        /// To Save Supersession Details 
        /// </summary>
        /// 
        [Route("api/CoreSupersession/Save")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Save([FromBody] SaveCoreSupersessionList SaveCoreSupersessionobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreSupersessionServices.Save(connString, SaveCoreSupersessionobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Export Supersession Uday Kumar J B 18-07-2024 :::
        /// <summary>
        /// Exporting Supersession  
        /// </summary>
        /// 
        [Route("api/CoreSupersession/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] ExportCoreSupersessionList ExportCoreSupersessionobj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportCoreSupersessionobj.sidx;
            string sord = ExportCoreSupersessionobj.sord;
            string filter = ExportCoreSupersessionobj.filter;
            string advnceFilter = ExportCoreSupersessionobj.advanceFilter;

            try
            {


                Object Response = await CoreSupersessionServices.Export(ExportCoreSupersessionobj, connstring, filter, advnceFilter, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion


        #region ::: SelectSupersessionFromDetails  Uday Kumar J B 18-07-2024 :::
        /// <summary>
        /// SelectSupersessionFromDetails  
        /// </summary>
        /// 
        [Route("api/CoreSupersession/SelectSupersessionFromDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSupersessionFromDetails([FromBody] SelectSupersessionFromDetailsList SelectSupersessionFromDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreSupersessionServices.SelectSupersessionFromDetails(connstring, SelectSupersessionFromDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectSupersessionToDetails  Uday Kumar J B 18-07-2024 :::
        /// <summary>
        /// SelectSupersessionToDetails  
        /// </summary>
        /// 
        [Route("api/CoreSupersession/SelectSupersessionToDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSupersessionToDetails([FromBody] SelectSupersessionToDetailsList SelectSupersessionToDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreSupersessionServices.SelectSupersessionToDetails(connstring, SelectSupersessionToDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectMultiplePartPrefix   Uday Kumar J B 18-07-2024 :::
        /// <summary>
        /// SelectMultiplePartPrefix
        /// </summary>
        ///  
        [Route("api/CoreSupersession/SelectMultiplePartPrefix")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectMultiplePartPrefix([FromBody] SelectMultiplePartPrefixList SelectMultiplePartPrefixobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreSupersessionServices.SelectMultiplePartPrefix(connString, SelectMultiplePartPrefixobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: GetFromPartDetails Uday Kumar J B 18-07-2024  :::
        /// <summary>
        /// To Select Part Number Details           
        /// </summary>
        /// 
        [Route("api/CoreSupersession/GetFromPartDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetFromPartDetails([FromBody] GetFromPartDetailsList GetFromPartDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreSupersessionServices.GetFromPartDetails(connString, GetFromPartDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: GetToPartDetails  Uday Kumar J B 18-07-2024:::
        /// <summary>
        /// To Select Part Number Details        
        /// </summary>
        /// 
        [Route("api/CoreSupersession/GetToPartDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetToPartDetails([FromBody] GetToPartDetailsList GetToPartDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreSupersessionServices.GetToPartDetails(connString, GetToPartDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectReferenceMaster Uday Kumar J B 18-07-2024 :::
        /// <summary>
        /// To Select Refrence Master records       
        /// </summary> 
        /// 
        [Route("api/CoreSupersession/SelectReferenceMaster")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectReferenceMaster([FromBody] SelectReferenceMasterCoreSupersessionList SelectReferenceMasterCoreSupersessionobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreSupersessionServices.SelectReferenceMaster(connString, SelectReferenceMasterCoreSupersessionobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectParticularSupersession  Uday Kumar J B 18-07-2024 :::
        /// <summary>
        /// To get Model master data       
        /// </summary> 
        ///
        [Route("api/CoreSupersession/SelectParticularSupersession")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularSupersession([FromBody] SelectParticularSupersessionList SelectParticularSupersessionobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreSupersessionServices.SelectParticularSupersession(connString, SelectParticularSupersessionobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: InitializeSupersessionType Uday Kumar J B 18-07-2024 Not Used If needed used for the view:::
        /// <summary>
        /// To Initialize Supersession Type     
        /// </summary> 
        /// 
        [Route("api/CoreSupersession/InitializeSupersessionType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InitializeSupersessionType([FromBody] InitializeSupersessionTypeList InitializeSupersessionTypeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreSupersessionServices.InitializeSupersessionType(connString, InitializeSupersessionTypeobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckPartsForSupersessionFrom Uday Kumar J B 18-07-2024 :::
        /// <summary>
        /// To Check Parts For Supersession From        
        /// </summary>
        /// 
        [Route("api/CoreSupersession/CheckPartsForSupersessionFrom")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckPartsForSupersessionFrom([FromBody] CheckPartsForSupersessionFromList CheckPartsForSupersessionFromobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreSupersessionServices.CheckPartsForSupersessionFrom(connString, CheckPartsForSupersessionFromobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckPartsForSupersessionTo Uday Kumar J B 18-07-2024 :::
        /// <summary>
        /// To Check Part For Supersession To   
        /// </summary>
        ///
        [Route("api/CoreSupersession/CheckPartsForSupersessionTo")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckPartsForSupersessionTo([FromBody] CheckPartsForSupersessionToList CheckPartsForSupersessionToobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreSupersessionServices.CheckPartsForSupersessionTo(connString, CheckPartsForSupersessionToobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


    }
}

