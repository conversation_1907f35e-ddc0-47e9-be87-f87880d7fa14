# PBC Microservices Migration Status

## PBC.CoreService Functions (59 Functions)

- [x] CoreChangePasswordServices.cs
- [ ] CoreUserMasterServices.cs
- [ ] CoreCompanyCalenderMasterServices.cs
- [x] CoreCompanyFinancialYearServices.cs
- [ ] CoreCompanyMasterServices.cs
- [x] CoreConsigneeServices.cs
- [ ] CoreDefectGroupMasterServices.cs
- [ ] CoreDefectNameMasterServices.cs
- [ ] CoreEmployeeMasterServices.cs
- [ ] CoreExchangeRateMasterServices.cs
- [ ] CoreFunctionGroupMasterServices.cs
- [ ] CoreModelMasterServices.cs
- [ ] CoreModuleMasterServices.cs
- [ ] CoreObjectMasterServices.cs
- [ ] CoreProductMasterServices.cs
- [ ] CoreProductTypeMasterServices.cs
- [ ] CoreReferenceMasterServices.cs
- [ ] CoreSecondarySegmentMasterServices.cs
- [ ] CoreServiceTypeMasterServices.cs
- [ ] CoreStateMasterServices.cs
- [ ] CorePartsMasterServices.cs
- [ ] CorePartsRatecontractImportServices.cs
- [ ] CoreSupersessionServices.cs
- [ ] CorePartyMasterServices.cs
- [ ] CoreKeyCustomerUpdateServices.cs
- [ ] CoreEmailTemplateService.cs
- [ ] CoreHeaderFooterPrintServices.cs
- [ ] CoreHourlyRateServices.cs
- [ ] CoreLinksMasterServices.cs
- [ ] CorePrefixSuffixServices.cs
- [ ] CoreRosterMasterServices.cs
- [ ] CoreSLAMasterServices.cs
- [ ] CoreTermsAndConditionsServices.cs
- [ ] CoreImportInterfaceServices.cs
- [ ] CoreUploadOpeningBalanceServices.cs
- [ ] CoreUploadPriceListServices.cs
- [ ] CoreIssueSubAreaMasterServices.cs
- [ ] CoreQuestionnaireLevel1MasterServices.cs
- [ ] CoreQuestionnaireLevel2MasterServices.cs
- [ ] CoreQuestionnaireLevel3MasterServices.cs
- [ ] CoreQuestionnaireLevel3Services.cs
- [ ] CoreMovementTypeDefinitionServices.cs
- [ ] CoreMovementTypeServices.cs
- [ ] CoreWareHouseServices.cs
- [ ] CoreWareHouseOrderClassAssociationServices.cs
- [x] CoreChangeVehicleOwnershipServices.cs
- [ ] CoreMachineUpdateServices.cs
- [ ] CoreTaxStructureMappingServices.cs
- [ ] CoreTaxTypeAccountCodeMappingMasterServices.cs
- [ ] CoreServiceChargesServices.cs
- [ ] CoreLogsheetReportServices.cs
- [ ] CoreWorkFlowEscalation1Services.cs

## PBC.HelpdeskService Functions (21 Functions)

- [ ] HelpDeskCustomerFeedBackServices.cs
- [ ] HelpDeskCustomerFeedBackQuestionsServices.cs
- [ ] HelpDeskCustomerFeedBackReportsServices.cs
- [ ] HelpDeskServiceRequestServices.cs
- [ ] HelpDeskServiceRequestKnowledgeBaseServices.cs
- [ ] HelpDeskUnregisteredServiceRequestServices.cs
- [ ] HelpDeskFollowUpServices.cs
- [ ] HelpDeskManagerLandingPageServices.cs
- [ ] HelpDeskUserLandingPageServices.cs
- [ ] HelpDeskTop10ModelsServices.cs
- [ ] HelpDeskTicketReportServices.cs
- [ ] HelpDesk_Rpt_CallLogServices.cs
- [ ] HelpDesk_Rpt_CallSummaryServices.cs
- [ ] HelpDesk_Rpt_IssueArea_IssueSubAreaServices.cs
- [ ] HelpDesk_Rpt_SLAExceededServices.cs
- [ ] HelpDesk_Rpt_SummaryOfIBDomesticQueriesServices.cs
- [ ] HelpDesk_Rpt_TicketReminderServices.cs
- [ ] Helpdesk_Rpt_ResponseTimeServices.cs
- [ ] HelpDesk_CR_ResolutionTimeWithTimeSlotsServices.cs
- [ ] HelpDesk_Tr_AverageResolutionTimeServices.cs
- [ ] HelpDesk_Tr_AverageResponseTimeServices.cs
- [ ] HelpDesk_Tr_CaseDistributionChartServices.cs
- [ ] HelpDesk_Tr_CaseRegistrationCountServices.cs
- [ ] HelpDesk_Tr_CaseSummaryServices.cs
- [ ] Helpdesk_Tr_AgeingAnalysisReportServices.cs

## PBC.UtilityService Functions

### Shared Utilities
- [ ] Common.cs
- [x] CommonFunctionalities.cs
- [x] ExtensionMethods.cs
- [x] Utilities.cs
- [ ] WorkFlowCommon.cs
- [x] PartsCommon.cs (TODO: Add Helper functions content)
- [x] LogSheetExporter.cs
- [x] HelpDeskCommon.cs
- [x] HelpDeskServiceRequestAPI.cs
- [x] ACLProperties.cs
- [x] AdvanceFilter.cs
- [x] Filters.cs
- [x] rules.cs
- [ ] DocumentExport.cs

## PBC.AggregatorService Functions
- [ ] Route requests to PBC.CoreService
- [ ] Route requests to PBC.HelpdeskService
- [ ] Route requests to PBC.UtilityService
- [ ] Handle authentication forwarding
- [ ] Manage connection string passing and Tenant Isolation

## Migration Instructions

### How to Mark Functions as Complete:
- Change `[ ]` to `[x]` when a function is successfully migrated
- Use `~~strikethrough~~` for functions that are no longer needed
- Example: ~~[ ] ObsoleteFunction.cs~~ (function removed)

### Priority Order:
1. **PBC.UtilityService** - Foundation utilities first
2. **PBC.CoreService** - Core business logic
3. **PBC.HelpdeskService** - Helpdesk functionality
4. **PBC.AggregatorService** - Gateway routing last
