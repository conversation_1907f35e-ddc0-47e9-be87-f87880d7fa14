﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreModelMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreModelMasterController : ApiController
    {
        #region ::: Select Model Landing grid /Mithun:::
        /// <summary>
        /// To select Model for Brand and Product Type
        /// </summary>
        [Route("api/CoreModelMaster/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] SelectModelList SelectObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.Select(SelectObj, Conn, LogException, sidx, sord, page, rows, _search, advnce, filters, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }

        #endregion

        #region ::: Select Particular Model Header in edit mode /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        [Route("api/CoreModelMaster/SelectParticularModelHeader")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularModelHeader([FromBody] SelectParticularModelHeaderList SelectParticularModelHeaderObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.SelectParticularModelHeader(SelectParticularModelHeaderObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Company Available stock /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        [Route("api/CoreModelMaster/SelectAvailableStock")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectAvailableStock([FromBody] SelectAvailableStockList SelectAvailableStockObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelectAvailableStock(SelectAvailableStockObj, Conn, LogException, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }

        #endregion

        #region ::: Select Branch wise available stock /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        [Route("api/CoreModelMaster/SelectBranchWise")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectBranchWise([FromBody] SelectBranchWiseList SelectBranchWiseObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelectBranchWise(SelectBranchWiseObj, Conn, LogException, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }

        #endregion

        #region ::: Select Serial Number and Status /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        [Route("api/CoreModelMaster/SelectSerialNumber")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSerialNumber([FromBody] SelectSerialNumberList SelectSerialNumberObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelectSerialNumber(SelectSerialNumberObj, Conn, LogException, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }

        #endregion

        #region ::: Select Company Physical stock /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary>
        [Route("api/CoreModelMaster/SelectPhysicalStock")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPhysicalStock([FromBody] SelectPhysicalStockList SelectPhysicalStockObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelectPhysicalStock(SelectPhysicalStockObj, Conn, LogException, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }

        #endregion

        #region ::: Select Branch wise physical stock /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        [Route("api/CoreModelMaster/SelectBranchWisePhysicalStock")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectBranchWisePhysicalStock([FromBody] SelectBranchWisePhysicalStockList SelectBranchWisePhysicalStockObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelectBranchWisePhysicalStock(SelectBranchWisePhysicalStockObj, Conn, LogException, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }

        #endregion

        #region ::: Select Warehouser /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        [Route("api/CoreModelMaster/SelectWarehouseWisePhysicalStock")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectWarehouseWisePhysicalStock([FromBody] SelectWarehouseWisePhysicalStockList SelectWarehouseWisePhysicalStockObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelectWarehouseWisePhysicalStock(SelectWarehouseWisePhysicalStockObj, Conn, LogException, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }

        #endregion

        #region::: SelectSerialNumberOfPhysicalStock /Mithun:::
        /// <summary>
        ///  SelectSerialNumberOfPhysicalStock
        /// </summary>
        [Route("api/CoreModelMaster/SelectSerialNumberOfPhysicalStock")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSerialNumberOfPhysicalStock([FromBody] SelectSerialNumberOfPhysicalStockList SelectSerialNumberOfPhysicalStockObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelectSerialNumberOfPhysicalStock(SelectSerialNumberOfPhysicalStockObj, Conn, LogException, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }

        #endregion

        #region ::: Select Company GIT stock /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        [Route("api/CoreModelMaster/SelectGITStock")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectGITStock([FromBody] SelectGITStockList SelectGITStockObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelectGITStock(SelectGITStockObj, Conn, LogException, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }

        #endregion

        #region ::: Select Branch wise GIT stock /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        [Route("api/CoreModelMaster/SelectGITBranchWise")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectGITBranchWise([FromBody] SelectGITBranchWiseList SelectGITBranchWiseObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelectGITBranchWise(SelectGITBranchWiseObj, Conn, LogException, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }

        #endregion

        #region ::: Select GIT Serial Number and Status /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        [Route("api/CoreModelMaster/SelectSerialNumberAndStatus")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSerialNumberAndStatus([FromBody] SelectSerialNumberAndStatusList SelectSerialNumberAndStatusObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelectSerialNumberAndStatus(SelectSerialNumberAndStatusObj, Conn, LogException, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }

        #endregion

        #region ::: Select Company OWH stock /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        [Route("api/CoreModelMaster/SelectOWHStock")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectOWHStock([FromBody] SelectOWHStockList SelectOWHStockObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelectOWHStock(SelectOWHStockObj, Conn, LogException, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }

        #endregion

        #region ::: Select Branch wise OWH stock /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        [Route("api/CoreModelMaster/SelectOWHBranchWise")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectOWHBranchWise([FromBody] SelectOWHBranchWiseList SelectOWHBranchWiseObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelectOWHBranchWise(SelectOWHBranchWiseObj, Conn, LogException, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }

        #endregion

        #region ::: Select Out of WH Serial Number and Status /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary>
        [Route("api/CoreModelMaster/SelectOWHSerialNumberAndStatus")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectOWHSerialNumberAndStatus([FromBody] SelectOWHSerialNumberAndStatusList SelectOWHSerialNumberAndStatusObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelectOWHSerialNumberAndStatus(SelectOWHSerialNumberAndStatusObj, Conn, LogException, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Company UCL stock /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        [Route("api/CoreModelMaster/SelectUCLStock")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectUCLStock([FromBody] SelectUCLStockList SelectUCLStockObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelectUCLStock(SelectUCLStockObj, Conn, LogException, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Branch wise UCL stock /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary> 
        [Route("api/CoreModelMaster/SelectUCLBranchWise")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectUCLBranchWise([FromBody] SelectUCLBranchWiseList SelectUCLBranchWiseObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelectUCLBranchWise(SelectUCLBranchWiseObj, Conn, LogException, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Out of WH Serial Number and Status /Mithun:::
        /// <summary>
        /// To get Model master data
        /// </summary>
        [Route("api/CoreModelMaster/SelectUCLSerialNumberAndStatus")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectUCLSerialNumberAndStatus([FromBody] SelectUCLSerialNumberAndStatusList SelectUCLSerialNumberAndStatusObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelectUCLSerialNumberAndStatus(SelectUCLSerialNumberAndStatusObj, Conn, LogException, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }
        #endregion

        #region ::: Insert Model Header /Mithun:::
        /// <summary>
        /// To Insert Model Header
        /// </summary>
        [Route("api/CoreModelMaster/Insert")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Insert([FromBody] InsertModelMasterList InsertObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.Insert(InsertObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Update Model Header /Mithun:::
        /// <summary>
        /// To Update MOdel
        /// </summary>
        [Route("api/CoreModelMaster/Update")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Update([FromBody] UpdateModelMasterList UpdateModelObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.Update(UpdateModelObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select ServiceType in Model Header /Mithun:::
        /// <summary>
        /// To Select Service Type
        /// </summary> 
        [Route("api/CoreModelMaster/SelectServiceType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectServiceType([FromBody] SelectServiceTypeList SelectServiceTypeObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.SelectServiceType(SelectServiceTypeObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Brand /Mithun:::
        /// <summary>
        /// To Select Brand
        /// </summary> 
        [Route("api/CoreModelMaster/SelectReferenceMaster")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectReferenceMaster([FromBody] SelectReferenceMasterModelMasterList SelectReferenceMasterObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.SelectReferenceMaster(SelectReferenceMasterObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select ProductType /Mithun:::
        /// <summary>
        /// To Select Product type
        /// </summary> 
        [Route("api/CoreModelMaster/SelectProductType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectProductType([FromBody] SelectProductTypeModelList SelectProductTypeObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.SelectProductType(SelectProductTypeObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Check Duplicate Servicetype In Mandatory Service details Grid /Mithun:::
        /// <summary>
        /// to check if the Service type is already exists or not
        /// </summary>
        [Route("api/CoreModelMaster/CheckServiceType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckServiceType([FromBody] CheckServiceTypeList CheckServiceTypeObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.CheckServiceType(CheckServiceTypeObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Mandatory Service details and Load Service Type /Mithun:::
        /// <summary>
        /// To select Mandatory Service details and load service type
        /// </summary>
        [Route("api/CoreModelMaster/SelectServiceChargedetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectServiceChargedetail([FromBody] SelectServiceChargedetailList SelectServiceChargedetailObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelectServiceChargedetail(SelectServiceChargedetailObj, Conn, LogException, sidx, sord, page, rows, _search, advnce, filters, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }
        #endregion

        #region ::: Insert Mandatory service details /Mithun:::
        /// <summary>
        /// To Save Mandatory Service Details
        /// </summary>
        [Route("api/CoreModelMaster/InsertServiceChargeDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertServiceChargeDetail([FromBody] InsertServiceChargeDetailList InsertServiceChargeDetailObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.InsertServiceChargeDetail(InsertServiceChargeDetailObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Particular Model when click on globe /Mithun:::
        /// <summary>
        /// To Select  Particular Model when click on globe
        /// </summary>
        [Route("api/CoreModelMaster/SelectParticularModel")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularModel([FromBody] SelectParticularModelList SelectParticularModelObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.SelectParticularModel(SelectParticularModelObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Model Locale data when click on globe /Mithun:::
        /// <summary>
        ///  To select the Model Locale data when click on globe
        /// </summary> 
        [Route("api/CoreModelMaster/SelectSingleModelLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSingleModelLocale([FromBody] SelectSingleModelLocaleList SelectSingleModelLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.SelectSingleModelLocale(SelectSingleModelLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Insert Model Locale /Mithun:::
        /// <summary>
        ///  Method to Insert Model Locale
        /// </summary>  
        [Route("api/CoreModelMaster/InsertModelLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertModelLocale([FromBody] InsertModelLocaleList InsertModelLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.InsertModelLocale(InsertModelLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Update Model Locale /Mithun:::
        /// <summary>
        /// To Update Model Locale
        /// </summary>
        [Route("api/CoreModelMaster/UpdateLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateLocale([FromBody] UpdateLocaleModelList UpdateLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.UpdateLocale(UpdateLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Delete Model /Mithun:::
        /// <summary>
        /// To Delete Model
        /// </summary>
        [Route("api/CoreModelMaster/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteModelList DeleteObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.Delete(DeleteObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Delete Mandatory Service Details /Mithun:::
        /// <summary>
        /// To Delete Mandatory Service Details
        /// </summary>
        [Route("api/CoreModelMaster/DeleteServiceChargeDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteServiceChargeDetail([FromBody] DeleteServiceChargeDetailList DeleteServiceChargeDetailObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.DeleteServiceChargeDetail(DeleteServiceChargeDetailObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Delete Quick Checklist Details /Mithun:::
        /// <summary>
        /// To Delete Mandatory Service Details
        /// </summary>
        [Route("api/CoreModelMaster/DeleteQuickChecklist")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteQuickChecklist([FromBody] DeleteQuickChecklistList DeleteQuickChecklistObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.DeleteQuickChecklist(DeleteQuickChecklistObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Delete Red Carpet Checklist Details /Mithun:::
        /// <summary>
        /// To Delete Mandatory Service Details
        /// </summary>
        [Route("api/CoreModelMaster/DeleteRedCarpetChecklist")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteRedCarpetChecklist([FromBody] DeleteRedCarpetChecklistList DeleteRedCarpetChecklistObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.DeleteRedCarpetChecklist(DeleteRedCarpetChecklistObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Check Duplicate Model Locale Name /Mithun:::
        /// <summary>
        /// To Check if Model name Already exists for the Brand and producttype
        /// </summary>
        [Route("api/CoreModelMaster/CheckModelNameLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckModelNameLocale([FromBody] CheckModelNameLocaleList CheckModelNameLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.CheckModelNameLocale(CheckModelNameLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Check Duplicate Model Name /Mithun:::
        /// <summary>
        /// To Check if Model name Already exists for the Brand and producttype
        /// </summary>
        [Route("api/CoreModelMaster/CheckModelName")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckModelName([FromBody] CheckModelNameList CheckModelNameObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.CheckModelName(CheckModelNameObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Model Price List details /Mithun:::
        /// <summary>
        /// To select Model Price List details
        /// </summary>
        [Route("api/CoreModelMaster/SelectPriceList")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPriceList([FromBody] SelectPriceListList SelectPriceListObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelectPriceList(SelectPriceListObj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }
        #endregion

        #region ::: MakeLastRowEditable /Mithun:::
        /// <summary>
        /// MakeLastRowEditable
        /// </summary>
        [Route("api/CoreModelMaster/MakeLastRowEditable")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult MakeLastRowEditable([FromBody] MakeLastRowEditableModelList MakeLastRowEditableObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.MakeLastRowEditable(MakeLastRowEditableObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Insert Model PriceList details /Mithun:::
        /// <summary>
        /// To Save Model PriceList Details
        /// </summary>
        [Route("api/CoreModelMaster/InsertModelPriceListDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertModelPriceListDetail([FromBody] InsertModelPriceListDetailList InsertModelPriceListDetailObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.InsertModelPriceListDetail(InsertModelPriceListDetailObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Insert Model Quick Checklist Detail /Mithun:::
        /// <summary>
        /// To Save Model PriceList Details
        /// </summary>
        [Route("api/CoreModelMaster/InsertModelQuickChecklistDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertModelQuickChecklistDetail([FromBody] InsertModelQuickChecklistDetailList InsertModelQuickChecklistDetailObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.InsertModelQuickChecklistDetail(InsertModelQuickChecklistDetailObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Insert Model RedCarpet Checklist Detail /Mithun:::
        /// <summary>
        /// To Save Model PriceList Details
        /// </summary>
        [Route("api/CoreModelMaster/InsertModelRedCarpetChecklistDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertModelRedCarpetChecklistDetail([FromBody] InsertModelRedCarpetChecklistDetailList InsertModelRedCarpetChecklistDetailObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.InsertModelRedCarpetChecklistDetail(InsertModelRedCarpetChecklistDetailObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: getPreviousDate /Mithun:::
        /// <summary>
        /// To select the All parts 
        /// </summary> 
        [Route("api/CoreModelMaster/getPreviousDate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult getPreviousDate([FromBody] getPreviousDateModelList getPreviousDateObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.getPreviousDate(getPreviousDateObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Model Supplier Price Detail Grid /Mithun:::
        /// <summary>
        ///  Select Model Supplier Price Detail Grid
        /// </summary>
        [Route("api/CoreModelMaster/SelectModelSupplierDetailGrid")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectModelSupplierDetailGrid([FromBody] SelectModelSupplierDetailGridList SelectModelSupplierDetailGridObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelectModelSupplierDetailGrid(SelectModelSupplierDetailGridObj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Model Sales Price Detail Grid /Mithun:::
        /// <summary>
        /// Select Model Sales Price Detail Grid 
        /// </summary>
        [Route("api/CoreModelMaster/SelectModelSalesPriceDetailGrid")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectModelSalesPriceDetailGrid([FromBody] SelectModelSalesPriceDetailGridList SelectModelSalesPriceDetailGridObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelectModelSalesPriceDetailGrid(SelectModelSalesPriceDetailGridObj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Model Quick Checklist Detail Grid /Mithun:::
        /// <summary>
        /// Select Model Quick Checklist Detail Grid 
        /// </summary>
        [Route("api/CoreModelMaster/SelectModelQuickChecklistDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectModelQuickChecklistDetail([FromBody] SelectModelQuickChecklistDetailList SelectModelQuickChecklistDetailObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelectModelQuickChecklistDetail(SelectModelQuickChecklistDetailObj, Conn, LogException, sidx, sord, page, rows, _search, advnce, filters, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectParticularQuickChecklistLocale /Mithun:::
        /// <summary>
        /// To Select Particular Product type
        /// </summary>
        [Route("api/CoreModelMaster/SelectParticularQuickChecklistLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularQuickChecklistLocale([FromBody] SelectParticularQuickChecklistLocaleList SelectParticularQuickChecklistLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.SelectParticularQuickChecklistLocale(SelectParticularQuickChecklistLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: InsertQuickChecklistLocale /Mithun:::
        /// <summary>
        ///  Method to Insert Model Locale
        /// </summary>   
        [Route("api/CoreModelMaster/InsertQuickChecklistLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertQuickChecklistLocale([FromBody] InsertQuickChecklistLocaleList InsertQuickChecklistLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.InsertQuickChecklistLocale(InsertQuickChecklistLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Model RedCarpet Checklist Detail Grid /Mithun:::
        /// <summary>
        /// Select Model RedCarpet Checklist Detail Grid 
        /// </summary>
        [Route("api/CoreModelMaster/SelectModelRedCarpetChecklistDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectModelRedCarpetChecklistDetail([FromBody] SelectModelRedCarpetChecklistDetailList SelectModelRedCarpetChecklistDetailObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelectModelRedCarpetChecklistDetail(SelectModelRedCarpetChecklistDetailObj, Conn, LogException, sidx, sord, page, rows, _search, filters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }
        #endregion

        #region ::: InsertRedCarpetChecklistLocale /Mithun:::
        /// <summary>
        ///  Method to Insert Model Locale
        /// </summary>
        [Route("api/CoreModelMaster/InsertRedCarpetChecklistLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertRedCarpetChecklistLocale([FromBody] InsertRedCarpetChecklistLocaleList InsertRedCarpetChecklistLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.InsertRedCarpetChecklistLocale(InsertRedCarpetChecklistLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectParticularRedCarpetChecklistLocale /Mithun:::
        /// <summary>
        /// To Select Particular Product type
        /// </summary>
        [Route("api/CoreModelMaster/SelectParticularRedCarpetChecklistLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularRedCarpetChecklistLocale([FromBody] SelectParticularRedCarpetChecklistLocaleList SelectParticularRedCarpetChecklistLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.SelectParticularRedCarpetChecklistLocale(SelectParticularRedCarpetChecklistLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CheckDuplicateDescription /Mithun:::
        /// <summary>
        /// CheckDuplicateDescription
        /// </summary>
        [Route("api/CoreModelMaster/CheckDuplicateDescription")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckDuplicateDescription([FromBody] CheckDuplicateDescriptionList CheckDuplicateDescriptionObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.CheckDuplicateDescription(CheckDuplicateDescriptionObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CheckDuplicateDescriptionRed /Mithun:::
        /// <summary>
        /// CheckDuplicateDescriptionRed
        /// </summary>
        [Route("api/CoreModelMaster/CheckDuplicateDescriptionRed")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckDuplicateDescriptionRed([FromBody] CheckDuplicateDescriptionRedList CheckDuplicateDescriptionRedObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.CheckDuplicateDescriptionRed(CheckDuplicateDescriptionRedObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Insert Model Supplier PriceList details /Mithun:::
        /// <summary>
        /// To Save Model Supplier PriceList Details
        /// </summary>
        [Route("api/CoreModelMaster/InsertModelSupplierPriceListDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertModelSupplierPriceListDetail([FromBody] InsertModelSupplierPriceListDetailList InsertModelSupplierPriceListDetailObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.InsertModelSupplierPriceListDetail(InsertModelSupplierPriceListDetailObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Insert Model Sales PriceList details /Mithun:::
        /// <summary>
        /// To Save Model Supplier PriceList Details
        /// </summary>
        [Route("api/CoreModelMaster/InsertModelSalesPriceListDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertModelSalesPriceListDetail([FromBody] InsertModelSalesPriceListDetailList InsertModelSalesPriceListDetailObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.InsertModelSalesPriceListDetail(InsertModelSalesPriceListDetailObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Delete Model SupplierPrice Detail Mithun:::
        /// <summary>
        ///to Delete Model SupplierPrice Details record
        /// </summary>  
        [Route("api/CoreModelMaster/DeleteModelSupplierPriceDetailsRows")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteModelSupplierPriceDetailsRows([FromBody] DeleteModelSupplierPriceDetailsRowsList DeleteModelSupplierPriceDetailsRowsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.DeleteModelSupplierPriceDetailsRows(DeleteModelSupplierPriceDetailsRowsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Delete Model SalesPrice Detail /Mithun:::
        /// <summary>
        ///to Delete Model SupplierPrice Details record
        /// </summary> 
        [Route("api/CoreModelMaster/DeleteModelSalesPriceDetailsRows")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteModelSalesPriceDetailsRows([FromBody] DeleteModelSalesPriceDetailsRowsList DeleteModelSalesPriceDetailsRowsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.DeleteModelSalesPriceDetailsRows(DeleteModelSalesPriceDetailsRowsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: getSupplierPricePreviousDate /Mithun:::
        /// <summary>
        /// To select the All parts 
        /// </summary>    
        [Route("api/CoreModelMaster/GetSupplierPricePreviousDate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetSupplierPricePreviousDate([FromBody] GetSupplierPricePreviousDateList GetSupplierPricePreviousDateObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.GetSupplierPricePreviousDate(GetSupplierPricePreviousDateObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Party /Mithun:::
        /// <summary>
        /// To Select Party when enter Party and press tab
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CoreModelMaster/ChangePartyName")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ChangePartyName([FromBody] ChangePartyNameList ChangePartyNameObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.ChangePartyName(ChangePartyNameObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Field Search for Party /Mithun:::
        /// <summary>
        /// To Select Field Search for Party
        /// </summary>  
        [Route("api/CoreModelMaster/SelectFieldSearchParty")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectFieldSearchParty([FromBody] SelectFieldSearchPartyList SelectFieldSearchPartyObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelectFieldSearchParty(SelectFieldSearchPartyObj, Conn, LogException, sidx, sord, page, rows, _search, advnce, filters, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }
        #endregion

        #region ::: Check Valid Date Format :::

        [Route("api/CoreModelMaster/CheckValidDateFormat")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckValidDateFormat([FromBody] CheckValidDateFormatList CheckValidDateFormatObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.CheckValidDateFormat(CheckValidDateFormatObj, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region::: SelAllAttachment /Mithun:::
        /// <summary>
        /// SelAllAttachment
        /// </summary>
        [Route("api/CoreModelMaster/SelAllAttachment")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelAllAttachment([FromBody] SelAllAttachmentList SelAllAttachmentObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreModelMasterServices.SelAllAttachment(SelAllAttachmentObj, Conn, LogException, sidx, sord, page, rows, _search, filters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }
        #endregion

        #region::: DeleteAttachments /Mithun:::
        /// <summary>
        /// DeleteAttachments
        /// </summary>
        [Route("api/CoreModelMaster/DeleteAttachments")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteAttachments([FromBody] DeleteAttachmentsList DeleteAttachmentsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreModelMasterServices.DeleteAttachments(DeleteAttachmentsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



    }
}
