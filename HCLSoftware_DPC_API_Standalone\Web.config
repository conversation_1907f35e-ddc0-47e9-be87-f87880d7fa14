﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit TEST NAVEEN
  https://go.microsoft.com/fwlink/?LinkId=301879
  -->
<configuration>
  <appSettings>
	  <add key="EmailToAddress" value="<EMAIL>,<EMAIL>,<EMAIL>" />
	  <add key="EmailCcAddress" value="<EMAIL>,<EMAIL>,<EMAIL>" />
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
	<add key="DCAPIKEY" value="DKRD$46CwF5uSoYuSGhpVz9xKQovBZWjJorr/zUxMkByRP4FRE=" />
	<add key="DCIssuerKEY" value="https://localhost:44317/api/JWT/JWTGenOnLogin" />
	<add key="DCAPITimeOut" value="60" />
	<add key="DCParty" value="DCPARTY$46" />

	  <add key="UserName" value="HCLERPV2" />
	  <add key="Password" value="DKRD$46zUxMkByRP4FRE=" />
	  
	  <add key="ShoppingCartChangeNova" value="true" />
	  <add key="ShoppingCartChangePrevost" value="true" />
	<add key="EPCGetServerPath" value="https://nampdemo.hcltechswnp.com/EPC_UploadedFiles/Images/" />
	<add key="EPCGetPublicationPath" value="MANUEL\ENTR\ENTR-LFS" />
	<add key="EPCStoringImagePath" value="\\*************\SD-Shared-Folder\EPC_UploadedFiles\Images\" />
	<add key="EPCGetImagePath" value="https://nampdemo.hcltechswnp.com/EPC_UploadedFiles/Images/" />
	<add key="GetUploadedServiceDocPath" value="https://nampdemo.hcltechswnp.com/EPC_UploadedFiles/ServiceDocument/docs\" />
	  <add key="GetImagePathProd" value="https://nampdemo.hcltechswnp.com/Content/Images/DashBoardImages/" />
	  <add key="GetAttachmentsPath" value="http:\\************\Images_Uploader\EPC_Viewer\" />
	  <add key="AutoAddSupersessionPart" value="true" />
	  <add key="UserName" value="HCLERPV2" />
	  <add key="Password" value="DKRD$46zUxMkByRP4FRE=" />

	
  </appSettings>
  <system.web>
    <compilation debug="true" targetFramework="4.7.2" />
    <httpRuntime targetFramework="4.7.2" />
  </system.web>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" />
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1*******" newVersion="1*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="1.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-5.3.0.0" newVersion="5.3.0.0" />
      </dependentAssembly>
		<dependentAssembly>
			<assemblyIdentity name="System.Data.SqlClient" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
			<bindingRedirect oldVersion="0.0.0.0-4.8.6" newVersion="4.8.6" />
		</dependentAssembly>
		<dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.Tokens" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.IdentityModel.Tokens.Jwt" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.4.0" newVersion="4.1.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.Abstractions" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Identity.Client" publicKeyToken="0a613f4dd989e8ae" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.45.0.0" newVersion="4.45.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.Logging" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.JsonWebTokens" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Security.Principal.Windows" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ComponentModel.Annotations" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Caching.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Collections.Immutable" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.2.1.0" newVersion="1.2.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Azure.Core" publicKeyToken="92742159e12e44c8" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.24.0.0" newVersion="1.24.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Options" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <system.codedom>
    <compilers>
      <compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701" />
      <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" />
    </compilers>
  </system.codedom>
	<connectionStrings>
		<!--<add name="EPC" connectionString="Data Source=quest-demo-rds-enc.cew1ojzgpj8q.ap-south-1.rds.amazonaws.com; Initial Catalog=PartsAssist_Nova_DPC_Ecom; User ID=sd_dev;password=**********;multipleactiveresultsets=True;" providerName="System.Data.SqlClient" />-->
		<!--<add name="EPC" connectionString="Data Source=quest-qa-rds-enc.cew1ojzgpj8q.ap-south-1.rds.amazonaws.com; Initial Catalog=HCLSoftware_DPC_Nova; User ID=sd_dev;password=**********;multipleactiveresultsets=True;" providerName="System.Data.SqlClient" />-->
		<!--<add name="EPC" connectionString="Data Source=quest-qa-rds-enc.cew1ojzgpj8q.ap-south-1.rds.amazonaws.com; Initial Catalog=HCLSoftware_DPC_Nova; User ID=sd_dev;password=**********;multipleactiveresultsets=True;" providerName="System.Data.SqlClient" />-->

		<!--<add name="EPC" connectionString="Data Source=quest-qa-rds-enc.cew1ojzgpj8q.ap-south-1.rds.amazonaws.com; Initial Catalog=HCLSoftware_DPC_Nova; User ID=sd_dev;password=**********;multipleactiveresultsets=True;" providerName="System.Data.SqlClient" />-->

		<!--<add name="EPC" connectionString="Data Source=quest-dev-rds-enc.cew1ojzgpj8q.ap-south-1.rds.amazonaws.com; Initial Catalog=PartsAssist_Nova_LiveRep; User ID=sd_dev;password=**********;multipleactiveresultsets=True;" providerName="System.Data.SqlClient" />

	  <add name="EPCPREVOST" connectionString="Data Source=quest-qa-rds-enc.cew1ojzgpj8q.ap-south-1.rds.amazonaws.com; Initial Catalog=HCLSoftware_DPC_PREVOST; User ID=sd_dev;password=**********;multipleactiveresultsets=True;" providerName="System.Data.SqlClient" />-->
		
		<!--<add name="YANMAR" connectionString="Data Source=quest-qa-rds-enc.cew1ojzgpj8q.ap-south-1.rds.amazonaws.com; Initial Catalog=CILS_DMS_QA; User ID=SD_DEV;password=**********;multipleactiveresultsets=True;" providerName="System.Data.SqlClient" />-->
		<add name="FSMGOLD" connectionString="Data Source=quest-dev-rds-enc.cew1ojzgpj8q.ap-south-1.rds.amazonaws.com; Initial Catalog=HCLSoftware_AMP_MicroService_GUI; User ID=sd_dev;password=**********;multipleactiveresultsets=True;" providerName="System.Data.SqlClient" />
		<add name="YANMAR" connectionString="Data Source=quest-dev-rds-enc.cew1ojzgpj8q.ap-south-1.rds.amazonaws.com; Initial Catalog=YANMAR_DMS_STG; User ID=sd_dev;password=**********;multipleactiveresultsets=True;" providerName="System.Data.SqlClient" />

	</connectionStrings>
<system.webServer>
    <handlers>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <remove name="OPTIONSVerbHandler" />
      <remove name="TRACEVerbHandler" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers>
  </system.webServer></configuration>