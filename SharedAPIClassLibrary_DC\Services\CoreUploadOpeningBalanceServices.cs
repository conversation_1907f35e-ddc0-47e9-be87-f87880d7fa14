﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Text;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreUploadOpeningBalanceServices
    {

        public static int? ErrorCode = null;
        public static string templateDirectory = "Template";
        public static string BucketFilePath = "quest-partsassist\\EPC_UploadedFiles";
        public static DataTable ActualData = null;
        public static DataTable UploadedColumnNames = null;
        public static List<Attachements> AttachmentData = null;

        #region ::: ImportPartsTemplate Uday Kumar J B 16-08-2024 :::
        /// <summary>
        /// ImportPartsTemplate
        /// </summary>
        public static HttpResponseMessage ImportPartsTemplate(int LogException)
        {
            HttpResponseMessage response;
            try
            {
                // Define the file path and check if the file exists
                string filePath = Path.Combine(templateDirectory, "OpeningBalanceTemplate.xlsx");
                if (!File.Exists(filePath))
                {
                    response = new HttpResponseMessage(HttpStatusCode.NotFound)
                    {
                        Content = new StringContent("Template file not found.")
                    };
                    return response;
                }

                // Read the file as byte array
                byte[] fileBytes = File.ReadAllBytes(filePath);

                // Create response message
                response = new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new ByteArrayContent(fileBytes)
                };
                response.Content.Headers.ContentDisposition = new System.Net.Http.Headers.ContentDispositionHeaderValue("attachment")
                {
                    FileName = "OpeningBalanceTemplate.xlsx"
                };
                response.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            }
            catch (Exception ex)
            {
                // Log exception if LogException flag is set
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, $"{ex.GetType().FullName}: {ex.Message}", ex.TargetSite.ToString(), ex.StackTrace);
                }

                // Return an error response in case of failure
                response = new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("An error occurred while generating the template.")
                };
            }
            return response;
        }

        #endregion


        #region ::: UploadFile Uday Kumar J B 16-08-2024 :::
        /// <summary>
        /// UploadFile
        /// </summary>
        public static HttpResponseMessage UploadFile(IFormFile partsPriceFile, UploadFileCoreUploadOpeningBalanceList UploadFileCoreUploadOpeningBalanceobj, string connString, int LogException)
        {
            HttpResponseMessage responseMessage;
            string fileExtension = Path.GetExtension(partsPriceFile.FileName);

            try
            {
                DataTable dt = null;

                if (fileExtension == ".xls" || fileExtension == ".xlsx" || fileExtension == ".csv")
                {
                    using (var stream = new MemoryStream())
                    {
                        partsPriceFile.CopyTo(stream);
                        stream.Position = 0;

                        // Assuming `ExcelReader` can read from a MemoryStream
                        dt = Common.ExcelReader(stream, partsPriceFile.FileName);
                    }

                    if (dt != null)
                    {
                        EncapOpenbalPartsClass partClass = ValidateParts(dt, UploadFileCoreUploadOpeningBalanceobj.EffectiveFrom, connString);

                        if (partClass.PartsList.Count > 0)
                        {
                            SaveOpeningBalance(connString, UploadFileCoreUploadOpeningBalanceobj);
                        }

                        if (partClass.HasError)
                        {
                            // Prepare response for error file download
                            responseMessage = new HttpResponseMessage(HttpStatusCode.OK)
                            {
                                Content = new StreamContent(new MemoryStream(System.Text.Encoding.UTF8.GetBytes(partClass.ErrorString)))
                            };
                            responseMessage.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/vnd.ms-excel");
                            responseMessage.Content.Headers.ContentDisposition = new System.Net.Http.Headers.ContentDispositionHeaderValue("attachment")
                            {
                                FileName = "ErrorFile.xls"
                            };

                            return responseMessage;
                        }

                        // Set session code for successful process
                        ErrorCode = partClass.HasError && partClass.PartsList.Count > 0 ? 1 :
                                                                   !partClass.HasError && partClass.PartsList.Count > 0 ? 2 : 0;
                    }
                }

                // Success message, no errors found
                responseMessage = new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new StringContent("<script>window.close();</script>", System.Text.Encoding.UTF8, "text/html")
                };
            }
            catch (Exception ex)
            {
                // Error alert response
                string userCulture = UploadFileCoreUploadOpeningBalanceobj.UserCulture?.ToString();
                string errorMessage = CommonFunctionalities.GetResourceString(userCulture, "InvalidFile")?.ToString() ?? "Invalid File";

                responseMessage = new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent($"<script>alert('{errorMessage}');window.close();</script>", System.Text.Encoding.UTF8, "text/html")
                };

                // Set error code for session tracking
                ErrorCode = -1;
            }

            return responseMessage;
        }
        #endregion


        #region ::: SaveOpeningBalance Uday Kumar J B 16-08-2024 :::
        /// <summary>
        /// SaveOpeningBalance
        /// </summary>
        /// 
        public static void SaveOpeningBalance(string connString, UploadFileCoreUploadOpeningBalanceList UploadFileCoreUploadOpeningBalanceobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            List<UploadOpeningBalance> partsList = new List<UploadOpeningBalance>();
            using (SqlConnection connection = new SqlConnection(connString))
            {
                connection.Open();

                using (SqlCommand command = new SqlCommand("Up_ins_AM_ERP_SaveOpeningBalance", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    foreach (var part in partsList)
                    {
                        command.Parameters.Clear();

                        command.Parameters.AddWithValue("@Company_ID", Convert.ToInt32(UploadFileCoreUploadOpeningBalanceobj.Company_ID));
                        command.Parameters.AddWithValue("@Branch_ID", Convert.ToInt32(UploadFileCoreUploadOpeningBalanceobj.Branch));
                        command.Parameters.AddWithValue("@Object_ID", Convert.ToInt32(UploadFileCoreUploadOpeningBalanceobj.ObjectID));
                        command.Parameters.AddWithValue("@Part_ID", part.partID);
                        command.Parameters.AddWithValue("@Warehouse_ID", part.WarehouseID);
                        command.Parameters.AddWithValue("@BinLocation_ID", part.BinlocationID);
                        command.Parameters.AddWithValue("@PhysicalStock", part.PhysicalStock);
                        command.Parameters.AddWithValue("@WAC", part.WAC);
                        command.Parameters.AddWithValue("@EffectiveFrom", UploadFileCoreUploadOpeningBalanceobj.EffectiveFrom);

                        try
                        {
                            command.ExecuteNonQuery();
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }
                        }
                    }
                }
            }
        }

        #endregion


        #region ::: ValidateParts Uday Kumar J B 16-08-2024 :::
        /// <summary>
        /// ValidateParts
        /// </summary>
        ///
        public static EncapOpenbalPartsClass ValidateParts(DataTable dt, DateTime effectiveFrom, string connString)
        {
            List<UploadOpeningBalance> partsList = new List<UploadOpeningBalance>();
            StringBuilder sb = new StringBuilder();
            sb.Append("<table border='1'><thead><tr><td>Prefix</td><td>PartNumber</td><td>SupplierPrice</td><td>StandardPackingQuantity</td><td>SupplierPartsPrefix</td><td>SupplierPartsNumber</td><td>Remarks</td></tr></thead>");
            bool IsError = false;

            using (SqlConnection connection = new SqlConnection(connString))
            {
                connection.Open();

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    IsError = false;
                    string Remarks = string.Empty;

                    string prefix = dt.Rows[i][0].ToString();
                    string partNumber = dt.Rows[i][1].ToString();
                    decimal supplierPrice = dt.Rows[i][2] != DBNull.Value ? Convert.ToDecimal(dt.Rows[i][2]) : 0;
                    int standardPackingQuantity = dt.Rows[i][3] != DBNull.Value ? Convert.ToInt32(dt.Rows[i][3]) : 0;
                    string supplierPartsPrefix = dt.Rows[i][4].ToString();
                    string supplierPartsNumber = dt.Rows[i][5].ToString();

                    using (SqlCommand command = new SqlCommand("Up_Sel_AM_ERP_ValidateParts", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@Prefix", prefix);
                        command.Parameters.AddWithValue("@PartNumber", partNumber);
                        command.Parameters.AddWithValue("@SupplierPrice", supplierPrice);
                        command.Parameters.AddWithValue("@StandardPackingQuantity", standardPackingQuantity);
                        command.Parameters.AddWithValue("@SupplierPartsPrefix", supplierPartsPrefix);
                        command.Parameters.AddWithValue("@SupplierPartsNumber", supplierPartsNumber);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (reader.HasRows)
                            {
                                while (reader.Read())
                                {
                                    string fetchedRemarks = reader["Remarks"].ToString();
                                    if (!string.IsNullOrEmpty(fetchedRemarks))
                                    {
                                        IsError = true;
                                        Remarks += fetchedRemarks;
                                    }
                                    else
                                    {
                                        UploadOpeningBalance rowObj = new UploadOpeningBalance
                                        {
                                            Prefix = reader["Prefix"].ToString(),
                                            PartNumber = reader["PartNumber"].ToString(),
                                            SupplierPrice = Convert.ToDecimal(reader["SupplierPrice"]),
                                            StandardPackingQuantity = Convert.ToInt32(reader["StandardPackingQuantity"]),
                                            SupplierPartsPrefix = reader["SupplierPartsPrefix"].ToString(),
                                            SupplierPartsNumber = reader["SupplierPartsNumber"].ToString()
                                        };
                                        partsList.Add(rowObj);
                                    }
                                }
                            }
                        }
                    }

                    if (IsError)
                    {
                        sb.Append("<tr><td>" + prefix + "</td><td>" + partNumber + "</td>");
                        sb.Append("<td>" + supplierPrice + "</td><td>" + standardPackingQuantity + "</td><td>" + supplierPartsPrefix + "</td><td>" + supplierPartsNumber + "</td><td>" + Remarks + "</td></tr>");
                    }
                }
            }

            sb.Append("</table>");
            EncapOpenbalPartsClass result = new EncapOpenbalPartsClass
            {
                HasError = IsError,
                PartsList = partsList,
                ErrorString = sb.ToString()
            };
            return result;
        }


        #endregion


        #region ::: SelectPart Uday Kumar J B 16-08-2024  :::
        /// <summary>
        /// SelectPart
        /// </summary>
        ///
        public static UploadOpeningBalance SelectPart(string connString, SelectPartCoreUploadOpeningBalanceList SelectPartCoreUploadOpeningBalanceobj)
        {
            UploadOpeningBalance partUploadRow = null;
            int Company_ID = Convert.ToInt32(SelectPartCoreUploadOpeningBalanceobj.Company_ID);
            using (SqlConnection connection = new SqlConnection(connString))
            {
                connection.Open();

                using (SqlCommand command = new SqlCommand("Up_Sel_AM_ERP_SelectPart1", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@PartPrefix", SelectPartCoreUploadOpeningBalanceobj.partPrefix);
                    command.Parameters.AddWithValue("@PartNumber", SelectPartCoreUploadOpeningBalanceobj.partNumber);
                    command.Parameters.AddWithValue("@WarehouseID", SelectPartCoreUploadOpeningBalanceobj.WarehouseID);
                    command.Parameters.AddWithValue("@BinlocationID", SelectPartCoreUploadOpeningBalanceobj.BinlocationID);
                    command.Parameters.AddWithValue("@PhysicalStock", SelectPartCoreUploadOpeningBalanceobj.PhysicalStock);
                    command.Parameters.AddWithValue("@WAC", SelectPartCoreUploadOpeningBalanceobj.WAC);
                    command.Parameters.AddWithValue("@CompanyID", Company_ID);

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read() && !reader.IsDBNull(reader.GetOrdinal("PartID")))
                        {
                            partUploadRow = new UploadOpeningBalance
                            {
                                partID = reader.GetInt32(reader.GetOrdinal("PartID")),
                                PartPrefix = reader.GetString(reader.GetOrdinal("PartPrefix")),
                                PartNumber = reader.GetString(reader.GetOrdinal("PartNumber")),
                                WarehouseID = SelectPartCoreUploadOpeningBalanceobj.WarehouseID,
                                BinlocationID = SelectPartCoreUploadOpeningBalanceobj.BinlocationID,
                                PhysicalStock = SelectPartCoreUploadOpeningBalanceobj.PhysicalStock,
                                WAC = SelectPartCoreUploadOpeningBalanceobj.WAC,
                                companyID = Company_ID
                            };
                        }
                    }
                }
            }

            return partUploadRow;
        }

        #endregion


        #region ::: CheckIFPartExists Uday Kumar J B 16-08-2024  :::
        /// <summary>
        /// CheckIFPartExists
        /// </summary>
        ///
        public static IActionResult CheckIFPartExists(string connString, CheckIFPartExistsCoreUploadOpeningBalanceList CheckIFPartExistsCoreUploadOpeningBalanceobj)
        {
            bool isvalid = false;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int Company_ID = Convert.ToInt32(CheckIFPartExistsCoreUploadOpeningBalanceobj.Company_ID);
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand("Up_Sel_AM_ERP__CheckIFPartExists", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@PartPrefix", CheckIFPartExistsCoreUploadOpeningBalanceobj.partPrefix);
                        command.Parameters.AddWithValue("@PartNumber", CheckIFPartExistsCoreUploadOpeningBalanceobj.partNumber);
                        command.Parameters.AddWithValue("@CompanyID", Company_ID);

                        int partExists = Convert.ToInt32(command.ExecuteScalar());
                        if (partExists == 1)
                        {
                            isvalid = true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(isvalid);
        }

        //public int CheckForError()
        //{
        //    return Convert.ToInt32(Session["ErrorCode"]);
        //}

        #endregion


        #region ::: CheckStockLedgerTable  Uday Kumar J B 16-08-2024 :::
        /// <summary>
        /// CheckStockLedgerTable
        /// </summary>
        ///
        public static IActionResult CheckStockLedgerTable(string connString)
        {
            int stockLedgerCount = 0;
            using (SqlConnection conn = new SqlConnection(connString))
            {
                using (SqlCommand cmd = new SqlCommand("usp_CheckStockLedgerTable", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;

                    // Open the connection
                    conn.Open();

                    // Execute the stored procedure and get the result
                    stockLedgerCount = (int)cmd.ExecuteScalar();
                }
            }

            return new JsonResult(stockLedgerCount);
        }

        #endregion


        #region ::: UploadOpeningBalance List and obj Classes :::
        /// <summary>
        /// UploadOpeningBalance List and obj Classes
        /// </summary>
        ///
        public class CheckIFPartExistsCoreUploadOpeningBalanceList
        {
            public int Company_ID { get; set; }
            public string partPrefix { get; set; }
            public string partNumber { get; set; }
        }
        public class SelectPartCoreUploadOpeningBalanceList
        {
            public string partPrefix { get; set; }
            public string partNumber { get; set; }
            public int WarehouseID { get; set; }
            public int BinlocationID { get; set; }
            public decimal PhysicalStock { get; set; }
            public decimal WAC { get; set; }
            public int Company_ID { get; set; }
        }
        public class UploadFileCoreUploadOpeningBalanceList
        {
            public string PartsPrice { get; set; }
            public DateTime EffectiveFrom { get; set; }
            public string UserCulture { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int ObjectID { get; set; }
        }
        #endregion


        #region ::: UploadOpeningBalance Classes :::
        /// <summary>
        /// UploadOpeningBalance
        /// </summary>
        ///
        public class UploadOpeningBalance
        {
            public int partID { get; set; }
            public int BinlocationID { get; set; }
            public int WarehouseID { get; set; }
            public decimal PhysicalStock { get; set; }
            public DateTime effectiveFrom { get; set; }
            public decimal WAC { get; set; }
            public string PartPrefix { get; set; }
            public string PartNumber { get; set; }
            public int companyID { get; set; }
            public bool HasError { get; set; }
            public string Prefix { get; set; }
            public decimal SupplierPrice { get; set; }
            public int StandardPackingQuantity { get; set; }
            public string SupplierPartsPrefix { get; set; }
            public string SupplierPartsNumber { get; set; }
        }
        public class EncapOpenbalPartsClass
        {
            public List<UploadOpeningBalance> PartsList { get; set; }
            public bool HasError { get; set; }
            public string ErrorString { get; set; }
        }
        #endregion

    }
}
