﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Text;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class HelpDeskCustomerFeedBackQuestionsServices
    {
        public static IEnumerable<QuestionData> IQQuestionVar = null;
        public static string AppPath = "";
        public static T GetValueFromDB<T>(string query, List<SqlParameter> parameters, string connectionString)
        {
            T result = default;

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                conn.Open();

                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    // Add parameters to the command
                    if (parameters != null && parameters.Count > 0)
                    {
                        cmd.Parameters.AddRange(parameters.ToArray());
                    }

                    // Execute the query
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        // If the result type is a collection (e.g., List<T>)
                        if (typeof(T).IsGenericType && typeof(T).GetGenericTypeDefinition() == typeof(List<>))
                        {

                            var list = Activator.CreateInstance<T>();
                            var columnPropertyMapping = typeof(T).GetGenericArguments()[0]
                                    .GetProperties()
                                    .Where(property => Enumerable.Range(0, reader.FieldCount)
                                                                 .Select(reader.GetName)
                                                                 .Contains(property.Name))
                                    .Select(property => new
                                    {
                                        Property = property,
                                        Ordinal = reader.GetOrdinal(property.Name),
                                        TargetType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType
                                    })
                                    .ToList();
                            while (reader.Read())
                            {
                                var item = Activator.CreateInstance(typeof(T).GetGenericArguments()[0]);

                                foreach (var mapping in columnPropertyMapping)
                                {
                                    var value = reader[mapping.Ordinal];
                                    if (value != DBNull.Value)
                                    {
                                        // Set property value directly using the precomputed type
                                        mapping.Property.SetValue(item, Convert.ChangeType(value, mapping.TargetType));
                                    }
                                }

                              ((IList)list).Add(item);
                            }
                            result = (T)list;
                        }
                        // If the result is a scalar type (like string, bool, int)
                        else if (typeof(T) == typeof(string))
                        {
                            if (reader.Read())
                            {
                                result = (T)Convert.ChangeType(reader[0], typeof(T));
                            }
                        }
                        else if (typeof(T) == typeof(bool))
                        {
                            if (reader.Read())
                            {
                                result = (T)Convert.ChangeType(reader[0].ToString().ToUpper() == "TRUE", typeof(T));
                            }
                        }
                        else if (typeof(T) == typeof(int))
                        {
                            if (reader.Read())
                            {
                                result = (T)Convert.ChangeType(reader[0], typeof(T));
                            }
                        }
                        // Handle cases for custom classes/complex objects
                        else if (typeof(T).IsClass && typeof(T) != typeof(string))
                        {
                            if (reader.Read())
                            {
                                result = Activator.CreateInstance<T>(); // Create an instance of the class
                                var columnPropertyMapping = typeof(T)
                                  .GetProperties()
                                  .Where(property => Enumerable.Range(0, reader.FieldCount)
                                                               .Select(reader.GetName)
                                                               .Contains(property.Name))
                                  .Select(property => new
                                  {
                                      Property = property,
                                      Ordinal = reader.GetOrdinal(property.Name),
                                      TargetType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType
                                  })
                                  .ToList();

                                foreach (var mapping in columnPropertyMapping)
                                {
                                    var value = reader[mapping.Ordinal];
                                    if (value != DBNull.Value)
                                    {
                                        // Set property value directly using the precomputed type
                                        mapping.Property.SetValue(result, Convert.ChangeType(value, mapping.TargetType));
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return result;
        }

        #region GetCaseTypes vinay n 11/11/24
        /// <summary>
        /// GetCaseTypes
        /// </summary>
        /// <param name="CaseType_IDs"></param>
        /// <returns></returns>
        public static string GetCaseTypes(string CaseType_IDs)
        {
            string CaseTypes = string.Empty;
            List<string> CaseIDList = CaseType_IDs.Split(',').ToList<string>();
            foreach (var csList in CaseIDList)
            {
                if (csList == "2")
                {
                    CaseTypes = CaseTypes + "Parts,";
                }
                else if (csList == "3")
                {
                    CaseTypes = CaseTypes + "Service,";
                }
                else if (csList == "4")
                {
                    CaseTypes = CaseTypes + "Sales,";
                }
                else if (csList == "1")
                {
                    CaseTypes = CaseTypes + "Support,";
                }

            }
            CaseTypes = CaseTypes.TrimEnd(new char[] { ',' });
            return CaseTypes;
        }
        #endregion
        #region vinay n 11/11/24
        /// <summary>
        /// GetIssueAreas
        /// </summary>
        /// <param name="IssueAreas_IDs"></param>
        /// <returns></returns>
        public static string GetIssueAreas(GetIssueAreasList Obj, string connString, int LogException)
        {
            string IssueAreas = string.Empty;
            int CompanyID = Convert.ToInt32(Obj.Company_ID);
            List<string> IssueAreasList = Obj.IssueAreas_IDs.Split(',').ToList<string>();
            foreach (var IsList in IssueAreasList)
            {
                int ILT = Convert.ToInt32(IsList);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = @"
                    SELECT TOP 1 RefMasterDetail_Name
                    FROM GNM_RefMasterDetail rmd
                    JOIN GNM_RefMaster rm ON rmd.RefMaster_ID = rm.RefMaster_ID
                    WHERE rm.RefMaster_Name = 'ISSUEAREA'
                      AND rmd.RefMasterDetail_IsActive = 1
                      AND rmd.RefMasterDetail_ID = @ILT
                      AND ((rm.IsCompanySpecific = 1 AND rmd.Company_ID = @CompanyID) OR rm.IsCompanySpecific = 0)";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.Text;
                            command.Parameters.AddWithValue("@CompanyID", CompanyID);
                            command.Parameters.AddWithValue("@ILT", ILT);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            object result = command.ExecuteScalar();
                            if (result != null)
                            {
                                IssueAreas += result.ToString() + ",";
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

            }
            IssueAreas = IssueAreas.TrimEnd(new char[] { ',' });
            return IssueAreas;
        }
        #endregion
        #region GetIssueSubAreas vinay n 11/11/24
        /// <summary>
        /// GetIssueSubAreas
        /// </summary>
        /// <param name="IssueSubAreas_IDs"></param>
        /// <returns></returns>
        public static string GetIssueSubAreas(GetIssueSubAreasList Obj, string connString, int LogException)
        {
            string IssueSubAreas = string.Empty;
            List<string> IssueSubList = Obj.IssueSubAreas_IDs.Split(',').ToList<string>();
            foreach (var IssList in IssueSubList)
            {
                int ISSLT = Convert.ToInt32(IssList);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = @"
                     SELECT TOP 1 IssueSubArea_Description
            FROM HD_IssueSubArea
            WHERE IssueSubArea_ID = @IssueSubAreaID";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.Text;
                            command.Parameters.AddWithValue("@IssueSubAreaID", ISSLT);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            object result = command.ExecuteScalar();
                            if (result != null)
                            {
                                IssueSubAreas += result.ToString() + ",";
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

            }
            IssueSubAreas = IssueSubAreas.TrimEnd(new char[] { ',' });
            return IssueSubAreas;
        }
        #endregion
        #region ::: Select vinay n 11/11/24:::
        /// <summary>
        /// To Select IssueSubArea for a IssueArea
        /// </summary>
        public static IActionResult SelectIssueSubArea(SelectIssueSubAreaList Obj, string connString, int LogException)
        {
            try
            {
                int Count = 0;
                int Total = 0;
                int CompanyID = Convert.ToInt32(Obj.Company_ID);
                string QueryData = string.Empty;
                QueryData = "select * from HD_IssueSubArea where Company_ID=" + CompanyID + " and IssueSubArea_IsActive=1";
                if (!string.IsNullOrWhiteSpace(Obj.IssueAreaID))
                {
                    QueryData += " and IssueArea_ID in (" + Obj.IssueAreaID + ")";
                }
                if (!string.IsNullOrWhiteSpace(Obj.EnquiryType_IDs))
                {
                    QueryData += " and EnquiryType_ID in (" + Obj.EnquiryType_IDs + ")";
                }
                List<HD_IssueSubArea> hD_IssueSubAreas = new List<HD_IssueSubArea>();

                IEnumerable<HD_IssueSubArea> IssueSubAreaList = null;
                using (SqlConnection conn = new SqlConnection(connString))
                {


                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(QueryData, conn))
                        {
                            command.CommandType = CommandType.Text;



                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    HD_IssueSubArea issueSubArea = new HD_IssueSubArea
                                    {
                                        IssueSubArea_ID = reader["IssueSubArea_ID"] != DBNull.Value ? Convert.ToInt32(reader["IssueSubArea_ID"]) : 0,
                                        IssueArea_ID = reader["IssueArea_ID"] != DBNull.Value ? Convert.ToInt32(reader["IssueArea_ID"]) : 0,
                                        IssueSubArea_ShortName = reader["IssueSubArea_ShortName"] != DBNull.Value ? reader["IssueSubArea_ShortName"].ToString() : string.Empty,
                                        IssueSubArea_Description = reader["IssueSubArea_Description"] != DBNull.Value ? reader["IssueSubArea_Description"].ToString() : string.Empty,
                                        IssueSubArea_IsActive = reader["IssueSubArea_IsActive"] != DBNull.Value ? Convert.ToBoolean(reader["IssueSubArea_IsActive"]) : false,
                                        Company_ID = reader["Company_ID"] != DBNull.Value ? Convert.ToInt32(reader["Company_ID"]) : 0,
                                        EnquiryType_ID = reader["EnquiryType_ID"] != DBNull.Value ? Convert.ToInt32(reader["EnquiryType_ID"]) : 0
                                    };
                                    hD_IssueSubAreas.Add(issueSubArea);
                                }
                                IssueSubAreaList = hD_IssueSubAreas.AsEnumerable();
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                var jsonData = default(dynamic);

                var IssueSubAreaData = (from a in IssueSubAreaList
                                        orderby a.IssueSubArea_Description
                                        select new
                                        {
                                            ID = a.IssueSubArea_ID,
                                            Name = a.IssueSubArea_Description,
                                        }).ToList();
                jsonData = new
                {
                    IssueSubArea = IssueSubAreaData,
                };

                return new JsonResult(jsonData);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return null;
            }
        }
        #endregion
        #region ::: Save vinay n 11/11/24:::
        /// <summary>
        /// To Insert and Update Question
        /// </summary>
        public static IActionResult Save(SaveCustomerFeedBackQuestionsList Obj, string connString, int LogException)
        {
            string ErrorMsg = string.Empty;
            try
            {

                var jTR = default(dynamic);
                int CompanyID = Convert.ToInt32(Obj.Company_ID);

                var jObj = JObject.Parse(Obj.data);
                int Count = jObj["rows"].Count();
                for (int i = 0; i < Count; i++)
                {
                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Question_ID"]);
                    jTR.Read();
                    int Question_ID = Convert.ToInt32(jTR.Value.ToString() == "" ? "0" : jTR.Value.ToString());


                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Question"]);
                    jTR.Read();
                    string Question = Common.DecryptString(jTR.Value.ToString());


                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["IsMandatory"]);
                    jTR.Read();
                    bool IsMandatory = Convert.ToBoolean(jTR.Value.ToString());

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Rating"]);
                    jTR.Read();
                    bool Rating = Convert.ToBoolean(jTR.Value.ToString());

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Feedback"]);
                    jTR.Read();
                    bool Feedback = Convert.ToBoolean(jTR.Value.ToString());

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["CaseType_IDs"]);
                    jTR.Read();
                    string CaseType_IDs = Common.DecryptString(jTR.Value.ToString());

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["IssueArea_IDs"]);
                    jTR.Read();
                    string IssueArea_IDs = Common.DecryptString(jTR.Value.ToString());

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["IssueSubArea_IDs"]);
                    jTR.Read();
                    string IssueSubArea_IDs = Common.DecryptString(jTR.Value.ToString());


                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Question_IsActive"]);
                    jTR.Read();
                    bool Question_IsActive = Convert.ToBoolean(jTR.Value.ToString());

                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_SAVE_HelpDesk_Save_HelpDeskCustomerFeedBackQuestionsServices";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.Add(new SqlParameter("@Question_ID", SqlDbType.Int)).Value = Question_ID;
                                command.Parameters.Add(new SqlParameter("@Question", SqlDbType.NVarChar, -1)).Value = Question;
                                command.Parameters.Add(new SqlParameter("@IsMandatory", SqlDbType.Bit)).Value = IsMandatory;
                                command.Parameters.Add(new SqlParameter("@IsRating", SqlDbType.Bit)).Value = Rating;
                                command.Parameters.Add(new SqlParameter("@IsFeedback", SqlDbType.Bit)).Value = Feedback;
                                command.Parameters.Add(new SqlParameter("@CaseType_IDs", SqlDbType.NVarChar, -1)).Value = CaseType_IDs;
                                command.Parameters.Add(new SqlParameter("@IssueArea_IDs", SqlDbType.NVarChar, -1)).Value = IssueArea_IDs;
                                command.Parameters.Add(new SqlParameter("@IssueSubArea_IDs", SqlDbType.NVarChar, -1)).Value = IssueSubArea_IDs;
                                command.Parameters.Add(new SqlParameter("@Question_IsActive", SqlDbType.Bit)).Value = Question_IsActive;
                                command.Parameters.Add(new SqlParameter("@Company_ID", SqlDbType.Int)).Value = CompanyID;



                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                command.ExecuteNonQuery();

                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }


                }
                ErrorMsg = "Saved";
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                ErrorMsg = string.Empty;

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }
                ErrorMsg = string.Empty;
            }
            return new JsonResult(ErrorMsg);

        }
        #endregion

        #region ::: Delete :::
        /// <summary>
        /// To Delete Question
        /// </summary>
        public static IActionResult Delete(DeleteCustomerFeedBackQuestionsList Obj, string connString, int LogException)
        {
            string ErrorMsg = string.Empty;
            try
            {
                var jObj = JObject.Parse(Obj.key);
                int Count = jObj["rows"].Count();
                HD_CustomerFeedbackQuestion deleteRow = null;

                int ID = 0;
                for (int i = 0; i < Count; i++)
                {
                    var jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jTR.Read();
                    ID = Convert.ToInt32(jTR.Value);



                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "DELETE FROM HD_CustomerFeedbackQuestion WHERE Question_ID = @Question_ID";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.Text;
                                command.Parameters.Add(new SqlParameter("@Question_ID", SqlDbType.Int)).Value = ID;



                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                command.ExecuteNonQuery();

                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                }
                ErrorMsg += CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (Exception ex)
            {
                if (ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    ErrorMsg += CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
            }
            return new JsonResult(ErrorMsg);
        }
        #endregion
        #region ::: LoadCustomerQuestion:::
        /// <summary>
        ////to Load Customer Question
        /// </summary> 
        public static IActionResult LoadCustomerQuestion(GetIssueAreasList Obj, string connString, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {

            var PartPoducteDet = default(dynamic);


            int CompanyID = Convert.ToInt32(Obj.Company_ID);
            int Language_ID = Convert.ToInt32(Obj.UserLanguageID);
            var jsonData = default(dynamic);
            try
            {
                int Count = 0;
                int Total = 0;
                string queryCustomerFeedbackQuestions = @"
                SELECT *
                FROM HD_CustomerFeedbackQuestion
                WHERE Company_ID = @CompanyID";
                List<SqlParameter> parametersCustomerFeedbackQuestions = new List<SqlParameter>
                {
                    new SqlParameter("@CompanyID", CompanyID)
                };
                List<HD_CustomerFeedbackQuestion> customerFeedbackQuestions =
               GetValueFromDB<List<HD_CustomerFeedbackQuestion>>(queryCustomerFeedbackQuestions, parametersCustomerFeedbackQuestions, connString);
                IEnumerable<HD_CustomerFeedbackQuestion> QuestionList = customerFeedbackQuestions.AsEnumerable();
                IQueryable<QuestionData> IQQuestion = null;
                IEnumerable<QuestionData> IEQuestionArray = null;
                string YesE = CommonFunctionalities.GetResourceString(Obj.GeneralCulture.ToString(), "yes").ToString();
                string NoE = CommonFunctionalities.GetResourceString(Obj.GeneralCulture.ToString(), "no").ToString();
                string YesL = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "yes").ToString();
                string NoL = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "no").ToString();
                string queryRefMasterDetail = @"
                SELECT rmd.*
                FROM GNM_RefMasterDetail rmd
                JOIN GNM_RefMaster rm ON rmd.RefMaster_ID = rm.RefMaster_ID
                WHERE rm.RefMaster_Name = 'ISSUEAREA'
                  AND rmd.RefMasterDetail_IsActive = 1
                  AND (
                    (rm.IsCompanySpecific = 1 AND rmd.Company_ID = @CompanyID)
                    OR rm.IsCompanySpecific = 0
                  );";
                List<SqlParameter> parametersRefMasterDetail = new List<SqlParameter>
                {
                    new SqlParameter("@CompanyID", CompanyID)
                };

                // Fetch the data
                List<GNM_RefMasterDetail> refMasterDetails =
                    GetValueFromDB<List<GNM_RefMasterDetail>>(queryRefMasterDetail, parametersRefMasterDetail, connString);
                IEnumerable<GNM_RefMasterDetail> RefMasterDetail = refMasterDetails.AsEnumerable();
                var ReferenceMasterData = (from Ref in RefMasterDetail
                                           orderby Ref.RefMasterDetail_Name
                                           select new
                                           {
                                               ID = Ref.RefMasterDetail_ID,
                                               Name = Ref.RefMasterDetail_Name
                                           }).ToList();

                StringBuilder IssueArea = new StringBuilder();
                for (int i = 0; i < ReferenceMasterData.Count(); i++)
                {
                    if (i == 0)
                    {
                        IssueArea.Append(+ReferenceMasterData.ElementAt(i).ID + ":" + ReferenceMasterData.ElementAt(i).Name);
                    }
                    else
                    {
                        IssueArea.Append(";" + ReferenceMasterData.ElementAt(i).ID + ":" + ReferenceMasterData.ElementAt(i).Name);
                    }
                }

                IEQuestionArray = (from a in QuestionList
                                   select new QuestionData()
                                   {
                                       Question_ID = a.Question_ID,
                                       Question = a.Question,
                                       IsMandatory = (a.IsMandatory == true ? YesE : NoE),
                                       IsRating = (a.IsRating == true ? YesE : NoE),
                                       IsFeedback = (a.IsFeedback == true ? YesE : NoE),
                                       IsActive = (a.IsActive == true ? YesE : NoE),
                                       CaseType_IDs = GetCaseTypes(a.CaseType_IDs),
                                       IssueArea_IDs = a.IssueArea_IDs == "" ? "" : GetIssueAreas(new GetIssueAreasList
                                       {
                                           IssueAreas_IDs = a.IssueArea_IDs,
                                           Company_ID = Obj.Company_ID,

                                       }, connString, LogException),
                                       IssueSubArea_ID = a.IssueSubArea_IDs == "" ? "" : GetIssueSubAreas(new GetIssueSubAreasList
                                       {
                                           IssueSubAreas_IDs = a.IssueSubArea_IDs

                                       }, connString, LogException),
                                       IssueAr_IDs = a.IssueArea_IDs,
                                       CaseTypeint_IDs = a.CaseType_IDs,
                                       IssueSubAreaint_IDs = a.IssueSubArea_IDs
                                   });


                IQQuestion = IEQuestionArray.AsQueryable<QuestionData>();

                if (_search == true)
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    if (filtersObj.rules.Count() > 0)
                        IQQuestion = IQQuestion.FilterSearch<QuestionData>(filtersObj);
                }
                if (advnce == true)
                {
                    AdvanceFilter advnfilter = JObject.Parse((Query)).ToObject<AdvanceFilter>();
                    IQQuestion = IQQuestion.AdvanceSearch<QuestionData>(advnfilter);
                }

                IQQuestion = IQQuestion.OrderByField<QuestionData>(sidx, sord);

                if (Language_ID == Convert.ToInt32(Obj.GeneralLanguageID))
                {
                    IQQuestionVar = IQQuestion.AsEnumerable();
                }

                //Sorting

                Count = IQQuestion.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }


                jsonData = new
                {
                    total = Total,
                    page = page,
                    data = (from a in IQQuestion.AsEnumerable()
                            select new
                            {
                                ID = a.Question_ID,
                                edit = "<img id='" + a.Question_ID + "' src='" + AppPath + "/Content/edit.gif' key='" + a.Question_ID + "' class='QuestionEdit' editmode='false'/>",
                                delete = "<input type='checkbox' key='" + a.Question_ID + "' defaultchecked=''  id='chk" + a.Question_ID + "' class='QuestionDelete'/>",
                                Question = a.Question,
                                IsMandatory = a.IsMandatory,
                                IsRating = a.IsRating,
                                IsFeedback = a.IsFeedback,
                                IsActive = a.IsActive,
                                CaseType = a.CaseType_IDs,
                                IssueArea = a.IssueArea_IDs,
                                IssueSubArea = a.IssueSubArea_ID,
                                IssueAr_IDs = a.IssueAr_IDs,
                                CaseTypeint_IDs = a.CaseTypeint_IDs,
                                IssueSubAreaint_IDs = a.IssueSubAreaint_IDs
                            }).ToList().Paginate(page, rows),
                    records = Count,
                    IssueArea = IssueArea.ToString(),
                    IssueSubArea = ""
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);

        }
        #endregion
        #region ::: CheckQuestions :::
        /// <summary>
        /// CheckQuestions
        /// </summary> 
        public static IActionResult CheckQuestions(CheckQuestionsList Obj, string connString, int LogException)
        {
            int Count = 0;
            int CompanyID = Convert.ToInt32(Obj.Company_ID);
            try
            {
                string queryQuestionnaireLevel2 = @"
                SELECT *
                FROM HD_CustomerFeedbackQuestion
                WHERE Question = @Question
                  AND Company_ID = @CompanyID
                  AND Question_ID != @QuestionID;";
                List<SqlParameter> parametersQuestionnaireLevel2 = new List<SqlParameter>
                {
                    new SqlParameter("@Question", Obj.Question),
                    new SqlParameter("@CompanyID", CompanyID),
                    new SqlParameter("@QuestionID", Obj.QuestionID)
                };
                List<HD_CustomerFeedbackQuestion> QuestionnaireLevel2Row = GetValueFromDB<List<HD_CustomerFeedbackQuestion>>(queryQuestionnaireLevel2, parametersQuestionnaireLevel2, connString);

                if (QuestionnaireLevel2Row.Count > 0)
                {
                    Count = 1;
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                return null;
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return null;
            }
            return new JsonResult(Count);
        }
        #endregion
    }

    #region CustomerFeedBackQuestionsListsAndObjs Vinay n 11/11/24
    public class CheckQuestionsList
    {
        public int Company_ID { get; set; }
        public string Question { get; set; }
        public int QuestionID { get; set; }
    }
    public class DeleteCustomerFeedBackQuestionsList
    {
        public string key { get; set; }
        public string UserCulture { get; set; }
    }
    public class GetIssueAreasList
    {
        public int Company_ID { get; set; }
        public string IssueAreas_IDs { get; set; }


        //LoadCustomerQuestion
        public int UserLanguageID { get; set; }
        public string GeneralCulture { get; set; }
        public string UserCulture { get; set; }
        public int GeneralLanguageID { get; set; }


    }
    public class GetIssueSubAreasList
    {
        public string IssueSubAreas_IDs { get; set; }
    }
    public class SelectIssueSubAreaList
    {
        public int Company_ID { get; set; }
        public string IssueAreaID { get; set; }
        public string EnquiryType_IDs { get; set; }
    }
    public class SaveCustomerFeedBackQuestionsList
    {
        public int Company_ID { get; set; }
        public string data { get; set; }

    }

    #endregion
    #region HelpDeskCustomerFeedBackQuestionsMasterClasses vinay n 11/11/24
    /// <summary>
    /// HelpDeskCustomerFeedBackQuestionsMasterClasses
    /// </summary>
    public partial class HD_CustomerFeedbackQuestion
    {
        public HD_CustomerFeedbackQuestion()
        {
            this.HD_SRCustomerQuestionFeedBack = new HashSet<HD_SRCustomerQuestionFeedBack>();
        }

        public int Question_ID { get; set; }
        public string Question { get; set; }
        public string CaseType_IDs { get; set; }
        public string IssueArea_IDs { get; set; }
        public string IssueSubArea_IDs { get; set; }
        public bool IsMandatory { get; set; }
        public bool IsRating { get; set; }
        public bool IsFeedback { get; set; }
        public bool IsActive { get; set; }
        public int Company_ID { get; set; }
        public Nullable<int> QuestionCode { get; set; }

        public virtual ICollection<HD_SRCustomerQuestionFeedBack> HD_SRCustomerQuestionFeedBack { get; set; }
    }
    public partial class HD_SRCustomerQuestionFeedBack
    {
        public int QuestionFeedBack_ID { get; set; }
        public int ServiceRequest_ID { get; set; }
        public int Question_ID { get; set; }
        public System.DateTime FeedBackDate { get; set; }
        public string FeedBack { get; set; }
        public Nullable<byte> Rating { get; set; }

        public virtual HD_CustomerFeedbackQuestion HD_CustomerFeedbackQuestion { get; set; }
        public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
    }
    public partial class HD_ServiceRequest
    {
        public HD_ServiceRequest()
        {
            this.HD_ServiceRequestNotesDetail = new HashSet<HD_ServiceRequestNotesDetail>();
            this.HD_SRFollowUpDetails = new HashSet<HD_SRFollowUpDetails>();
            this.HD_SRFollowUpInviteDetails = new HashSet<HD_SRFollowUpInviteDetails>();
            this.HD_CRERCRepository = new HashSet<HD_CRERCRepository>();
            this.HD_ServiceRequestAttachmentInfo = new HashSet<HD_ServiceRequestAttachmentInfo>();
            this.HD_ServiceRequestPartsList = new HashSet<HD_ServiceRequestPartsList>();
            this.HD_SRProductDetails = new HashSet<HD_SRProductDetails>();
            this.HD_SRCustomerQuestionFeedBack = new HashSet<HD_SRCustomerQuestionFeedBack>();
        }

        public int ServiceRequest_ID { get; set; }
        public string ServiceRequestNumber { get; set; }
        public System.DateTime ServiceRequestDate { get; set; }
        public Nullable<int> Quotation_ID { get; set; }
        public string QuotationNumber { get; set; }
        public Nullable<int> JobCard_ID { get; set; }
        public string JobCardNumber { get; set; }
        public int CallStatus_ID { get; set; }
        public Nullable<int> ParentIssue_ID { get; set; }
        public string Product_Unique_Number { get; set; }
        public int Party_ID { get; set; }
        public int PartyContactPerson_ID { get; set; }
        public Nullable<int> Model_ID { get; set; }
        public Nullable<int> Brand_ID { get; set; }
        public Nullable<int> ProductType_ID { get; set; }
        public string SerialNumber { get; set; }
        public Nullable<int> ProductReading { get; set; }
        public bool IsUnderWarranty { get; set; }
        public int CallMode_ID { get; set; }
        public Nullable<int> CallPriority_ID { get; set; }
        public int CallComplexity_ID { get; set; }
        public System.DateTime CallDateAndTime { get; set; }
        public Nullable<System.DateTime> PromisedCompletionDate { get; set; }
        public Nullable<int> Region_ID { get; set; }
        public string CallDescription { get; set; }
        public Nullable<int> IssueArea_ID { get; set; }
        public Nullable<int> IssueSubArea_ID { get; set; }
        public Nullable<int> FunctionGroup_ID { get; set; }
        public bool IsUnderBreakDown { get; set; }
        public Nullable<int> QuestionaryLevel1_ID { get; set; }
        public Nullable<int> QuestionaryLevel2_ID { get; set; }
        public Nullable<int> QuestionaryLevel3_ID { get; set; }
        public Nullable<int> DefectGroup_ID { get; set; }
        public Nullable<int> DefectName_ID { get; set; }
        public string RootCause { get; set; }
        public string InformationCollected { get; set; }
        public Nullable<System.DateTime> CallClosureDateAndTime { get; set; }
        public Nullable<int> ClosureType_ID { get; set; }
        public string ClosingDescription { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public Nullable<System.DateTime> ModifiedDate { get; set; }
        public Nullable<int> ModifiedBy_ID { get; set; }
        public Nullable<int> Document_no { get; set; }
        public int CaseType_ID { get; set; }
        public string ActionRemarks { get; set; }
        public Nullable<int> Product_ID { get; set; }
        public Nullable<int> CustomerRating { get; set; }
        public Nullable<int> FinancialYear { get; set; }
        public Nullable<bool> IsCallBlocked { get; set; }
        public Nullable<int> StockBlocking_ID { get; set; }
        public Nullable<int> EnquiryStage_ID { get; set; }
        public Nullable<int> SalesQuotation_ID { get; set; }
        public string SalesQuotationNumber { get; set; }
        public Nullable<int> SalesOrder_ID { get; set; }
        public string SalesOrderNumber { get; set; }
        public Nullable<System.DateTime> SalesOrderDate { get; set; }
        public Nullable<int> CallOwner_ID { get; set; }
        public string Flexi1 { get; set; }
        public string Flexi2 { get; set; }
        public string ResolutionTime { get; set; }
        public string ResponseTime { get; set; }
        public Nullable<byte> AttachmentCount { get; set; }
        public Nullable<System.DateTime> CustomerFeedbackDate { get; set; }
        public Nullable<bool> IsNegetiveFeedback { get; set; }
        public Nullable<bool> IsDealer { get; set; }
        public Nullable<byte> IsDealerList { get; set; }
        public Nullable<byte> ProductRateType { get; set; }
        public Nullable<int> ChildTicket_Sequence_ID { get; set; }
        public string ResponseTime24 { get; set; }
        public string ResolutionTime24 { get; set; }
        public Nullable<int> Current_AssignTo { get; set; }
        public Nullable<int> ContractorID { get; set; }
        public Nullable<int> ContractorContactPerson_ID { get; set; }
        public Nullable<int> ScheduledType_ID { get; set; }
        public Nullable<System.DateTime> ExpectedArrivalDateTime { get; set; }
        public Nullable<System.DateTime> ActualArrivalDateTime { get; set; }
        public Nullable<System.DateTime> ExpectedDepartureDateTime { get; set; }
        public Nullable<System.DateTime> ActualDepartureDateTime { get; set; }
        public Nullable<int> NoofTechs { get; set; }
        public Nullable<int> ShiftHours { get; set; }
        public Nullable<bool> IsWIPBay { get; set; }

        public virtual ICollection<HD_ServiceRequestNotesDetail> HD_ServiceRequestNotesDetail { get; set; }
        public virtual ICollection<HD_SRFollowUpDetails> HD_SRFollowUpDetails { get; set; }
        public virtual ICollection<HD_SRFollowUpInviteDetails> HD_SRFollowUpInviteDetails { get; set; }
        public virtual ICollection<HD_CRERCRepository> HD_CRERCRepository { get; set; }
        public virtual ICollection<HD_ServiceRequestAttachmentInfo> HD_ServiceRequestAttachmentInfo { get; set; }
        public virtual ICollection<HD_ServiceRequestPartsList> HD_ServiceRequestPartsList { get; set; }
        public virtual ICollection<HD_SRProductDetails> HD_SRProductDetails { get; set; }
        public virtual ICollection<HD_SRCustomerQuestionFeedBack> HD_SRCustomerQuestionFeedBack { get; set; }
    }
    public partial class HD_SRProductDetails
    {
        public HD_SRProductDetails()
        {
            this.HD_SRPRODUCTDETAILSALLOCATION = new HashSet<HD_SRPRODUCTDETAILSALLOCATION>();
        }

        public int SRProductDetails_ID { get; set; }
        public int ServiceRequest_ID { get; set; }
        public int Model_ID { get; set; }
        public int Brand_ID { get; set; }
        public int ProductType_ID { get; set; }
        public decimal Quantity { get; set; }
        public Nullable<decimal> ActiveQuantity { get; set; }
        public Nullable<decimal> WonQuantity { get; set; }
        public string LostSaleReasons { get; set; }
        public Nullable<int> Competitor_ID { get; set; }
        public Nullable<int> CompetitorModel_ID { get; set; }
        public Nullable<int> CompetitorBrand_ID { get; set; }
        public Nullable<int> CompetitorProductType_ID { get; set; }
        public Nullable<decimal> CompetitorPrice { get; set; }
        public Nullable<int> PrimarySegment_ID { get; set; }
        public Nullable<int> SecondarySegment_ID { get; set; }
        public string Remarks { get; set; }
        public Nullable<decimal> Rate { get; set; }
        public Nullable<int> PACKINGTYPE_ID { get; set; }
        public string ReferenceDetails { get; set; }

        public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
        public virtual ICollection<HD_SRPRODUCTDETAILSALLOCATION> HD_SRPRODUCTDETAILSALLOCATION { get; set; }
    }
    public partial class HD_SRPRODUCTDETAILSALLOCATION
    {
        public int SRPRODUCTDETAILSALLOCATION_ID { get; set; }
        public int SRPRODUCTDETAILS_ID { get; set; }
        public int PRODUCT_ID { get; set; }

        public virtual HD_SRProductDetails HD_SRProductDetails { get; set; }
    }
    public partial class HD_ServiceRequestPartsList
    {
        public int ServiceRequestPartsList_ID { get; set; }
        public int ServiceRequest_ID { get; set; }
        public int Parts_ID { get; set; }
        public int UnitOfMeasurement { get; set; }
        public decimal Quantity { get; set; }
        public string Remarks { get; set; }
        public decimal Rate { get; set; }
        public Nullable<decimal> MRP { get; set; }

        public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
    }
    public partial class HD_ServiceRequestAttachmentInfo
    {
        public int ServiceRequestAttachmentInfo_ID { get; set; }
        public int ServiceRequest_ID { get; set; }
        public string FileName { get; set; }
        public int FileSize { get; set; }
        public string FileDescription { get; set; }
        public System.DateTime FileUploadDate { get; set; }
        public int FileUploadedByEmployee_ID { get; set; }
        public bool IsMailAttachment { get; set; }

        public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
    }
    public partial class HD_CRERCRepository
    {
        public int CRERCRepository_ID { get; set; }
        public int ServiceRequest_ID { get; set; }
        public string RequestPartNumber { get; set; }
        public string RequestPartDescription { get; set; }
        public Nullable<int> Model_ID { get; set; }
        public Nullable<int> Brand_ID { get; set; }
        public Nullable<int> ProductType_ID { get; set; }
        public Nullable<int> Parts_ID { get; set; }
        public bool IsEPC { get; set; }
        public bool IsPricedInSAP { get; set; }
        public Nullable<decimal> Rate { get; set; }
        public bool IsNLS { get; set; }
        public bool IsIPN { get; set; }
        public Nullable<System.DateTime> LRDDate { get; set; }
        public bool IsMailAttachment { get; set; }

        public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
    }
    public partial class HD_SRFollowUpInviteDetails
    {
        public int SRFollowUpInviteDetails_ID { get; set; }
        public int ServiceRequest_ID { get; set; }
        public int SRFollowUpDetails_ID { get; set; }
        public bool Invitee_Type { get; set; }
        public Nullable<int> Employee_ID { get; set; }
        public Nullable<int> Party_ID { get; set; }
        public bool IsAttended { get; set; }

        public virtual HD_SRFollowUpDetails HD_SRFollowUpDetails { get; set; }
        public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
    }
    public partial class HD_ServiceRequestNotesDetail
    {
        public int ServiceRequestNotes_ID { get; set; }
        public int ServiceRequest_ID { get; set; }
        public int CreatedBy { get; set; }
        public System.DateTime CreatedDateAndTime { get; set; }
        public string NotesDescription { get; set; }
        public string Department { get; set; }
        public string Name { get; set; }
        public Nullable<bool> IsFollowUp { get; set; }

        public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
    }
    public partial class HD_SRFollowUpDetails
    {
        public HD_SRFollowUpDetails()
        {
            this.HD_SRFollowUpInviteDetails = new HashSet<HD_SRFollowUpInviteDetails>();
        }

        public int SRFollowUpDetails_ID { get; set; }
        public int ServiceRequest_ID { get; set; }
        public string FollowUpDescription { get; set; }
        public System.DateTime StartDateandTime { get; set; }
        public Nullable<System.DateTime> EndDateandTime { get; set; }
        public int FollowUpMode_ID { get; set; }
        public int FollowUpStatus_ID { get; set; }
        public string Remarks { get; set; }

        public virtual ICollection<HD_SRFollowUpInviteDetails> HD_SRFollowUpInviteDetails { get; set; }
        public virtual HD_ServiceRequest HD_ServiceRequest { get; set; }
    }
    public class QuestionData
    {
        public int Question_ID
        {
            get;
            set;
        }
        public int ID
        {
            get;
            set;
        }
        public string CaseType_IDs
        {
            get;
            set;
        }
        public string IssueSubArea_ID
        {
            get;
            set;
        }

        public string IssueArea_IDs
        {
            get;
            set;
        }

        public string Question
        {
            get;
            set;
        }

        public string IsMandatory
        {
            get;
            set;
        }
        public string IsRating
        {
            get;
            set;
        }
        public string IsFeedback
        {
            get;
            set;
        }
        public string IsActive
        {
            get;
            set;
        }
        public string IssueAr_IDs
        {
            get;
            set;
        }
        public string CaseTypeint_IDs
        {
            get;
            set;
        }
        public string IssueSubAreaint_IDs
        {
            get;
            set;
        }
        public byte? Rating
        {
            get;
            set;
        }
        public string Feedback
        {
            get;
            set;
        }
    }
    #endregion
}
