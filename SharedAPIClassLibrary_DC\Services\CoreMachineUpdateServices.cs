﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.CoreProductTypeMasterServices;
using static SharedAPIClassLibrary_AMERP.Utilities.CoreCompanyCalenderMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreMachineUpdateServices
    {

        #region::: GETCompanyParam /Mithun:::
        /// <summary>
        /// GETCompanyParam
        /// </summary>
        /// <param name="GETCompanyParamObj"></param>
        /// <param name="constring"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult GETCompanyParam(GETCompanyParamList GETCompanyParamObj, string constring, int LogException)
        {
            bool IsProductPartySalesInvoiceDetails = false;
            string paramName = "ISPRODUCTPARTYSALESINVOICEDETAILS";

            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Define the SQL query
                    string query = "SELECT Param_value FROM GNM_CompParam WHERE UPPER(Param_Name) = @ParamName";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        // Set the parameter for the query
                        cmd.Parameters.AddWithValue("@ParamName", paramName.ToUpper());

                        // Execute the query and retrieve the value
                        var result = cmd.ExecuteScalar();

                        // Check if result is not null and matches "TRUE"
                        if (result != null && result.ToString().ToUpper() == "TRUE")
                        {
                            IsProductPartySalesInvoiceDetails = true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the exception if needed
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return IsProductPartySalesInvoiceDetails;
            return new JsonResult(IsProductPartySalesInvoiceDetails);
        }
        #endregion

        #region ::: Select /not woking:::
        /// <summary>
        /// To select All product
        /// </summary>
        public static IActionResult Select(SelectMachineUpdateList SelectObj, string constring, int LogException, string sidx, string sord, int page, int rows)
        {
            var jsonData = default(dynamic);
            try
            {
                int Count = 0;
                int Total = 0;
                int Company_ID = Convert.ToInt32(SelectObj.Company_ID);
                List<Machine_ProductMaster> IEProductMasterArray = new List<Machine_ProductMaster>();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Set the Company_Name session variable
                    SqlCommand cmd = new SqlCommand("SELECT Company_Name FROM GNM_Company WHERE Company_ID = @Company_ID", conn);
                    cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                    SqlDataReader reader = cmd.ExecuteReader();
                    if (reader.Read())
                    {
                        SelectObj.Company_Name = reader["Company_Name"].ToString();
                    }
                    reader.Close();

                    // Call the simplified stored procedure to fetch product details
                    cmd = new SqlCommand("GetProductDetails", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@Company_ID", Company_ID);

                    reader = cmd.ExecuteReader();

                    // Skip to the starting record of the current page
                    int recordsToSkip = (page - 1) * rows;
                    int recordsToRead = rows;

                    // Move to the starting record of the current page
                    for (int i = 0; i < recordsToSkip; i++)
                    {
                        if (!reader.Read())
                        {
                            break; // Break if there are no more records
                        }
                    }

                    // Read records for the current page
                    while (reader.Read() && recordsToRead > 0)
                    {
                        var product = new Machine_ProductMaster
                        {
                            Product_ID = Convert.ToInt32(reader["Product_ID"]),
                            Product_UniqueNo = reader["Product_UniqueNo"].ToString(),
                            MachineStatus_ID = Convert.ToInt32(reader["MachineStatus_ID"]),
                            Company_ID = Convert.ToInt32(reader["Company_ID"]),
                            Model_ID = Convert.ToInt32(reader["Model_ID"]),
                            Model_Name = reader["Model_Name"].ToString(),
                            BrandName = reader["BrandName"] != DBNull.Value ? reader["BrandName"].ToString() : null,
                            ProductType_Name = reader["ProductType_Name"] != DBNull.Value ? reader["ProductType_Name"].ToString() : null,
                            Party_Name = reader["Party_Name"] != DBNull.Value ? reader["Party_Name"].ToString() : null,
                            MachineStatus = reader["MachineStatus"] != DBNull.Value ? reader["MachineStatus"].ToString() : null,
                            ProductSiteAddress_SiteAddress = reader["ProductSiteAddress_SiteAddress"] != DBNull.Value ? reader["ProductSiteAddress_SiteAddress"].ToString() : null,
                            ProductSiteAddress_Location = reader["ProductSiteAddress_Location"] != DBNull.Value ? reader["ProductSiteAddress_Location"].ToString() : null,
                            Reading = reader["Reading"] != DBNull.Value ? Convert.ToInt32(reader["Reading"]) : 0, // Default value if NULL
                            Brand_ID = Convert.ToInt32(reader["Brand_ID"]),
                            ProductType_ID = Convert.ToInt32(reader["ProductType_ID"]),
                            Product_SerialNumber = reader["Product_SerialNumber"] != DBNull.Value ? reader["Product_SerialNumber"].ToString() : null,
                            IsActive = Convert.ToBoolean(reader["IsActive"]),
                            edit = "<a title='View' href='#' style='font-size: 13px;' id='" + reader["Product_ID"] + "' key='" + reader["Product_ID"] + "' Model='" + (reader["Model_Name"] != DBNull.Value ? reader["Model_Name"].ToString() : "") + "' SerialNumber='" + (reader["Product_SerialNumber"] != DBNull.Value ? reader["Product_SerialNumber"].ToString() : "") + "' MachineStatus='" + (reader["MachineStatus"] != DBNull.Value ? reader["MachineStatus"].ToString() : "") + "' SiteAddress='" + (reader["ProductSiteAddress_SiteAddress"] != DBNull.Value ? reader["ProductSiteAddress_SiteAddress"].ToString() : "") + "' Location='" + (reader["ProductSiteAddress_Location"] != DBNull.Value ? reader["ProductSiteAddress_Location"].ToString() : "") + "' MachineHMR='" + (reader["Reading"] != DBNull.Value ? reader["Reading"].ToString() : "0") + "' StatusID='" + (reader["MachineStatus"] != DBNull.Value ? reader["MachineStatus"].ToString() : "") + "' class='EditMachineUpdate' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                        };
                        IEProductMasterArray.Add(product);
                        recordsToRead--;
                    }

                    // Get total count of records
                    if (reader.NextResult() && reader.Read())
                    {
                        Count = Convert.ToInt32(reader["Count"]);
                    }
                }

                // Calculate total pages
                Total = (int)Math.Ceiling((double)Count / rows);

                // Prepare JSON response
                jsonData = new
                {

                    total = Total,
                    page = page,
                    records = Count,
                    rows = IEProductMasterArray
                };

                //return Json(jsonData, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonData);
            }
            catch (Exception ex)
            {
                // Log exceptions if necessary
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return Json(jsonData, JsonRequestBehavior.AllowGet); // Return empty or partial data on error
                return new JsonResult(jsonData);
            }
        }


        #endregion

        #region ::: CheckMachineUpdateReadingDetails /Mithun:::
        /// <summary>
        /// To Check if Service History is Done
        /// </summary>
        public static IActionResult CheckMachineUpdateReadingDetails(CheckMachineUpdateReadingDetailsList CheckMachineUpdateReadingDetailsObj, string constring, int LogException)
        {
            int count = 0;

            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Get the product row
                    GNM_Product productRow = null;
                    string productQuery = "SELECT * FROM GNM_Product WHERE Product_ID = @ProductID";
                    using (SqlCommand cmd = new SqlCommand(productQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@ProductID", CheckMachineUpdateReadingDetailsObj.ProductID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                productRow = new GNM_Product
                                {
                                    Product_ID = Convert.ToInt32(reader["Product_ID"]),
                                    // Add other properties as needed
                                };
                            }
                        }
                    }

                    if (productRow == null)
                    {
                        // Product not found
                        //return count;
                        return new JsonResult(count);
                    }

                    // Get the product reading row
                    int productReading = 0;
                    GNM_ProductReading productReadingRow = null;
                    string readingQuery = CheckMachineUpdateReadingDetailsObj.ProductComponent_ID > 0
                        ? "SELECT TOP 1 * FROM GNM_ProductReading WHERE Product_ID = @ProductID AND ProductComponent_ID = @ProductComponentID ORDER BY Reading DESC"
                        : "SELECT TOP 1 * FROM GNM_ProductReading WHERE Product_ID = @ProductID AND ProductComponent_ID IS NULL ORDER BY Reading DESC";

                    using (SqlCommand cmd = new SqlCommand(readingQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@ProductID", CheckMachineUpdateReadingDetailsObj.ProductID);
                        if (CheckMachineUpdateReadingDetailsObj.ProductComponent_ID > 0)
                        {
                            cmd.Parameters.AddWithValue("@ProductComponentID", CheckMachineUpdateReadingDetailsObj.ProductComponent_ID);
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                productReadingRow = new GNM_ProductReading
                                {
                                    Product_ID = Convert.ToInt32(reader["Product_ID"]),
                                    ProductComponent_ID = reader["ProductComponent_ID"] as int?,
                                    Reading = Convert.ToInt32(reader["Reading"]),
                                    // Add other properties as needed
                                };
                                productReading = Convert.ToInt32(reader["Reading"]);
                            }
                        }
                    }

                    if (productReadingRow != null)
                    {
                        if (CheckMachineUpdateReadingDetailsObj.ProductComponent_ID > 0)
                        {
                            if (CheckMachineUpdateReadingDetailsObj.Reading <= productReading)
                            {
                                count = 1;
                            }
                        }
                        else
                        {
                            if (CheckMachineUpdateReadingDetailsObj.Reading < productReading)
                            {
                                count = 1;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            //return count;
            return new JsonResult(count);
        }

        #endregion

        #region ::: MachineUpdate /Mithun:::
        /// <summary>
        /// To Machine Update
        /// </summary>
        public static IActionResult MachineUpdate(MachineUpdateList MachineUpdateObj, string constring, int LogException)
        {
            var JsonData = default(dynamic);
            try
            {
                string SiteAddress = Common.DecryptString(MachineUpdateObj.SiteAddress);
                string SiteLocation = Common.DecryptString(MachineUpdateObj.Location);
                int BranchID = Convert.ToInt32(MachineUpdateObj.Branch);
                //GNM_User User = (GNM_User)Session["UserDetails"];
                GNM_User User = MachineUpdateObj.UserDetails.FirstOrDefault();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    SqlCommand cmd;
                    SqlDataReader reader;

                    // Retrieve product details
                    cmd = new SqlCommand("Up_GetProductDetails", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@Product_ID", MachineUpdateObj.Product_ID);
                    reader = cmd.ExecuteReader();
                    GNM_Product Productdetails = null;
                    if (reader.Read())
                    {
                        Productdetails = new GNM_Product
                        {
                            // Assign properties from reader
                        };
                    }
                    reader.Close();

                    if (MachineUpdateObj.MachineHMR > 0 && MachineUpdateObj.MachineHMR != MachineUpdateObj.PreviousHMR)
                    {
                        // Insert new reading
                        cmd = new SqlCommand("Up_INS_ProductReading", conn);
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Product_ID", MachineUpdateObj.Product_ID);
                        cmd.Parameters.AddWithValue("@Reading", MachineUpdateObj.MachineHMR);
                        cmd.Parameters.AddWithValue("@Reference_Date", DateTime.Now);
                        cmd.Parameters.AddWithValue("@Mode", 1);
                        cmd.Parameters.AddWithValue("@Company_ID", Convert.ToInt32(MachineUpdateObj.Company_ID));
                        cmd.ExecuteNonQuery();

                        JsonData = new
                        {
                            IsSuccess = true,
                            Duplicate = false,
                        };
                    }

                    if (MachineUpdateObj.MachineStatusId > 0)
                    {
                        // Retrieve current machine status ID
                        cmd = new SqlCommand("Up_GetCurrentMachineStatusId", conn);
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Product_ID", MachineUpdateObj.Product_ID);
                        reader = cmd.ExecuteReader();
                        int currentMachineStatusId = 0;
                        if (reader.Read())
                        {
                            currentMachineStatusId = Convert.ToInt32(reader["MachineStatus_ID"]);
                        }
                        reader.Close();

                        if (MachineUpdateObj.MachineStatusId != currentMachineStatusId)
                        {
                            // Update machine status ID
                            cmd = new SqlCommand("Up_UPD_MachineStatusId", conn);
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Product_ID", MachineUpdateObj.Product_ID);
                            cmd.Parameters.AddWithValue("@MachineStatusId", MachineUpdateObj.MachineStatusId);
                            cmd.ExecuteNonQuery();

                            // Insert product status history
                            cmd = new SqlCommand("Up_INS_ProductStatusHistory", conn);
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Product_ID", MachineUpdateObj.Product_ID);
                            cmd.Parameters.AddWithValue("@ProductStatus_Date", DateTime.Now);
                            cmd.Parameters.AddWithValue("@MachineStatusId", MachineUpdateObj.MachineStatusId);
                            cmd.Parameters.AddWithValue("@Company_ID", Convert.ToInt32(MachineUpdateObj.Company_ID));
                            cmd.ExecuteNonQuery();

                            JsonData = new
                            {
                                IsSuccess = true,
                                Duplicate = false,
                            };
                        }
                    }

                    if (SiteAddress != string.Empty && MachineUpdateObj.Location != string.Empty)
                    {
                        // Check if site address exists
                        cmd = new SqlCommand("Up_CheckSiteAddress", conn);
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@SiteAddress", SiteAddress.ToUpper());
                        cmd.Parameters.AddWithValue("@SiteLocation", SiteLocation.ToUpper());
                        cmd.Parameters.AddWithValue("@Product_ID", MachineUpdateObj.Product_ID);
                        reader = cmd.ExecuteReader();
                        bool addressExists = reader.Read();
                        reader.Close();

                        if (!addressExists)
                        {
                            // Deactivate previous addresses
                            cmd = new SqlCommand("Up_DeactivatePreviousSiteAddresses", conn);
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Product_ID", MachineUpdateObj.Product_ID);
                            cmd.ExecuteNonQuery();

                            // Insert new address
                            cmd = new SqlCommand("Up_INS_SiteAddress", conn);
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Product_ID", MachineUpdateObj.Product_ID);
                            cmd.Parameters.AddWithValue("@IsActive", true);
                            cmd.Parameters.AddWithValue("@SiteAddress", SiteAddress);
                            cmd.Parameters.AddWithValue("@SiteLocation", SiteLocation);
                            cmd.Parameters.AddWithValue("@Company_ID", Convert.ToInt32(MachineUpdateObj.Company_ID));
                            cmd.ExecuteNonQuery();

                            JsonData = new
                            {
                                IsSuccess = true,
                                Duplicate = false,
                            };
                        }
                        else
                        {
                            JsonData = new
                            {
                                IsSuccess = false,
                                Duplicate = true
                            };
                        }
                    }

                    // Insert GPS details
                    //   gbl.InsertGPSDetails(Convert.ToInt32(MachineUpdateObj.Company_ID.ToString()), BranchID, User.User_ID, Common.GetObjectID("MachineUpdate",constring), Productdetails.Product_ID, 0, 0, "Updated Product " + Productdetails.Product_SerialNumber, false, Convert.ToInt32(MachineUpdateObj.MenuID), Convert.ToDateTime(MachineUpdateObj.LoggedINDateTime));
                }
            }
            catch (Exception ex)
            {
                JsonData = new
                {
                    IsSuccess = false,
                    Duplicate = false
                };
                if (LogException == 1) LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            //return Json(JsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(JsonData);
        }

        #endregion


        #region::: SaveAttachment /Mithun:::
        /// <summary>
        /// SaveAttachment
        /// </summary>
        /// <returns></returns>
        public static IActionResult SaveAttachment(SaveAttachmentList SaveAttachmentObj, string constring, int LogException)
        {
            var jsonData = default(dynamic);
            try
            {

                int ObjectID = Common.GetObjectID("CoreProductMaster");
                //GNM_User User = (GNM_User)Session["UserDetails"];
                //GNM_User User = SaveAttachmentObj.UserDetails.FirstOrDefault();
                int companyID = SaveAttachmentObj.Company_ID;

                //================================================================================================
                //------------------------------------------------------------     

                if (SaveAttachmentObj.AttachmentData != null)
                {
                    string SrcPath = string.Empty;

                    List<Attachements> dsattachment = new List<Attachements>();
                    JObject jObj = JObject.Parse(SaveAttachmentObj.AttachmentsData);
                    int Count = jObj["rows"].Count();
                    Attachements[] ds = new Attachements[Count];
                    for (int i = 0; i < Count; i++)
                    {
                        Attachements detail = new Attachements();
                        ds[i] = detail;
                        JTokenReader reader = null;

                        reader = new JTokenReader(jObj["rows"][i]["ATTACHMENTDETAIL_ID"]);
                        reader.Read();
                        ds[i].ATTACHMENTDETAIL_ID = Convert.ToInt32(reader.Value.ToString());

                        reader = new JTokenReader(jObj["rows"][i]["FILENAME"]);
                        reader.Read();
                        ds[i].FILE_NAME = Common.DecryptString(reader.Value.ToString());

                        reader = new JTokenReader(jObj["rows"][i]["FILEDESCRIPTION"]);
                        reader.Read();
                        ds[i].FILEDESCRIPTION = Common.DecryptString(reader.Value.ToString());

                        reader = new JTokenReader(jObj["rows"][i]["Upload"]);
                        reader.Read();
                        ds[i].Upload = Convert.ToInt32(reader.Value.ToString());

                        reader = new JTokenReader(jObj["rows"][i]["UPLOADDATE"]);
                        reader.Read();
                        ds[i].UPLOADDATE = Convert.ToDateTime(reader.Value.ToString());

                        reader = new JTokenReader(jObj["rows"][i]["OBJECT_ID"]);
                        reader.Read();
                        ds[i].OBJECTID = Convert.ToInt32(reader.Value.ToString());

                        ds[i].DetailID = SaveAttachmentObj.SalesHistoryID;
                        ds[i].Tablename = "SLT_SALESHISTORY";
                        string SMP = (SaveAttachmentObj.AppPathString + " /Attachments");
                        string DstPath = SMP + "/" + ds[i].OBJECTID + "-" + SaveAttachmentObj.ProductId + "-" + SaveAttachmentObj.SalesHistoryID + "-" + Common.DecryptString(ds[i].FILE_NAME);
                        SrcPath = SMP + "/" + "Temp_" + Convert.ToInt32(Common.GetObjectID("CoreProductMaster")) + "_" + Convert.ToInt32(SaveAttachmentObj.User_ID.ToString()) + " /" + Common.DecryptString(ds[i].FILE_NAME);

                        if (System.IO.File.Exists(SrcPath))
                        {
                            System.IO.File.Move(SrcPath, DstPath);
                        }
                    }
                    List<Attachements> c = CommonFunctionalities.UploadAttachment(ds, SaveAttachmentObj.ProductId, Convert.ToInt32(SaveAttachmentObj.User_ID.ToString()), Convert.ToInt32(SaveAttachmentObj.Company_ID.ToString()), SaveAttachmentObj.SalesHistoryID, constring);

                    SaveAttachmentObj.AttachmentData = null;
                }

                List<Attachements> dsattachdelete = new List<Attachements>();
                JObject jObj1 = new JObject();
                jObj1 = JObject.Parse(SaveAttachmentObj.AttachmentDelete);
                int Count1 = jObj1["rows"].Count();
                Attachements[] ds1 = new Attachements[Count1];
                for (int i = 0; i < Count1; i++)
                {
                    Attachements detail = new Attachements();
                    ds1[i] = detail;
                    JTokenReader reader = null;
                    reader = new JTokenReader(jObj1["rows"][i]["id"]);
                    reader.Read();
                    ds1[i].ATTACHMENTDETAIL_ID = Convert.ToInt32(reader.Value.ToString());

                    reader = new JTokenReader(jObj1["rows"][i]["FileName"]);
                    reader.Read();
                    ds1[i].FILE_NAME = Common.DecryptString(reader.Value.ToString());

                    reader = new JTokenReader(jObj1["rows"][i]["Object_ID"]);
                    reader.Read();
                    ds1[i].OBJECTID = Convert.ToInt32(reader.Value.ToString());

                    ds1[i].TransactionID = SaveAttachmentObj.ProductId;
                }
                CommonFunctionalities.DeleteAttachments(ds1, (SaveAttachmentObj.AppPathString + " /Attachments"), constring);

                int AttCount = CommonFunctionalities.GetAttachmentCount(ObjectID, SaveAttachmentObj.ProductId, SaveAttachmentObj.SalesHistoryID, constring);

                // ADO.NET Code for updating the attachment count in CoreSLT_SALESHISTORY table
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    string query = "UPDATE CoreSLT_SALESHISTORY SET ATTACHMENTCOUNT = @AttCount WHERE SALESHISTORY_ID = @SalesHistoryID";
                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@AttCount", AttCount);
                        cmd.Parameters.AddWithValue("@SalesHistoryID", SaveAttachmentObj.SalesHistoryID);
                        cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion




        #region

        public static IActionResult Select(MachineUpdateSelectList Obj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string Query, string constring, int LogException)
        {
            var jsonData = default(dynamic);
            string query = "";
            string wherecondition = "";
            int LanguageID = Obj.LanguageID;
            try
            {
                string YesE = CommonFunctionalities.GetResourceString(Obj.GeneralCulture.ToString(), "Yes").ToString();
                string NoE = CommonFunctionalities.GetResourceString(Obj.GeneralCulture.ToString(), "No").ToString();
                string YesL = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Yes").ToString();
                string NoL = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "No").ToString();

                int Count = 0;
                int Total = 0;
                int Company_ID = Convert.ToInt32(Obj.Company_ID);
                IEnumerable<Machine_ProductMaster> IEProductList = null;//updated by kavitha for Writgen changes
                IEnumerable<GNM_ProductReading> IEProductReadingList = null;
                IQueryable<Machine_ProductMaster> IQProudctMaster = null;
                IEnumerable<Machine_ProductMaster> IEProductMasterArray = null;
                GNM_Company companydetail = new GNM_Company();
                //companydetail = CompanyClient.GNM_Company.Where(a => a.Company_ID == Company_ID).FirstOrDefault();



                //Session["Company_Name"] = companydetail.Company_Name;
                wherecondition = "WHERE pc.ProductCustomer_ToDate is null ";
                //---FilterToolBar Search
                if (_search)
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    if (filtersObj.rules.Count() > 0)
                    {
                        for (int i = 0; i < filtersObj.rules.Count(); i++)
                        {
                            if (filtersObj.rules.ElementAt(i).field == "BrandName")
                            {
                                filtersObj.rules.ElementAt(i).field = "b.RefMasterDetail_Name";
                            }
                            if (filtersObj.rules.ElementAt(i).field == "Product_IsActive")
                            {
                                string[] yesArray = { "Y", "YE", "YES", "E", "ES" };
                                string[] noArray = { "N", "O", "NO" };
                                if (yesArray.Contains(filtersObj.rules.ElementAt(i).data.ToUpper()))
                                {
                                    filtersObj.rules.ElementAt(i).data = "1";
                                }
                                else if (noArray.Contains(filtersObj.rules.ElementAt(i).data.ToUpper()))
                                {
                                    filtersObj.rules.ElementAt(i).data = "0";
                                }
                                else
                                {
                                    filtersObj.rules.ElementAt(i).data = "3";
                                }
                            }
                            else if (filtersObj.rules.ElementAt(i).field == "RefMasterDetail_Name")
                            {
                                filtersObj.rules.ElementAt(i).field = "ma.RefMasterDetail_Name";
                            }

                            //end
                            filtersObj.rules.ElementAt(i).data = filtersObj.rules.ElementAt(i).data.Replace("'", "''");
                            wherecondition = wherecondition + " AND " + filtersObj.rules.ElementAt(i).field + " like " + "'%" + filtersObj.rules.ElementAt(i).data + "%'";
                        }
                    }
                }

                if (advnce)
                {
                    string op = "";
                    AdvanceFilter advnfilter = JObject.Parse(Common.DecryptString(Query)).ToObject<AdvanceFilter>();
                    if (advnfilter.rules.Count() > 0)
                    {
                        for (int i = 0; i < advnfilter.rules.Count(); i++)
                        {
                            if (advnfilter.rules.ElementAt(i).Field == "Model_Name")
                            {
                                advnfilter.rules.ElementAt(i).Field = "m.Model_Name";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "ProductType_Name")
                            {
                                advnfilter.rules.ElementAt(i).Field = "pt.ProductType_Name";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "BrandName")
                            {
                                advnfilter.rules.ElementAt(i).Field = "b.RefMasterDetail_Name";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "MachineStatus")
                            {
                                advnfilter.rules.ElementAt(i).Field = "ma.RefMasterDetail_Name";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "Party_Name")
                            {
                                if (advnfilter.rules.ElementAt(i).Data.ToUpper() == "")
                                {
                                    advnfilter.rules.ElementAt(i).Field = "pc.Party_ID is null";
                                    advnfilter.rules.ElementAt(i).Data = "is null";
                                }
                                else
                                {
                                    advnfilter.rules.ElementAt(i).Field = "Party_Name";
                                }
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "SiteAddress")
                            {
                                advnfilter.rules.ElementAt(i).Field = "ProductSiteAddress_SiteAddress";
                            }
                            else
                                if (advnfilter.rules.ElementAt(i).Field == "IsActive")//updated by kavitha for Writgen changes
                            {
                                advnfilter.rules.ElementAt(i).Field = "Product_IsActive";//updated by kavitha for Writgen changes
                                if (advnfilter.rules.ElementAt(i).Data.ToUpper() == "YES")
                                {
                                    advnfilter.rules.ElementAt(i).Data = "1";
                                }
                                else if (advnfilter.rules.ElementAt(i).Data.ToUpper() == "NO")
                                {
                                    advnfilter.rules.ElementAt(i).Data = "0";
                                }
                                else
                                {
                                    advnfilter.rules.ElementAt(i).Data = "3";
                                }
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "MachineHMR")
                            {
                                advnfilter.rules.ElementAt(i).Field = "Reading.Reading";
                            }
                            advnfilter.rules.ElementAt(i).Data = advnfilter.rules.ElementAt(i).Data.Replace("'", "''");
                            op = Common.getoperator(advnfilter.rules.ElementAt(i).Operator);

                            if (i == 0)
                            {
                                if (op == "like")
                                {
                                    wherecondition = wherecondition + " AND " + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + advnfilter.rules.ElementAt(i).Data + "%'";
                                }
                                else
                                {
                                    wherecondition = wherecondition + " AND " + advnfilter.rules.ElementAt(i).Field + " " + op + " '" + advnfilter.rules.ElementAt(i).Data + "'";
                                }
                            }
                            else
                            {
                                if (op == "like")
                                {
                                    wherecondition = wherecondition + " AND " + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + advnfilter.rules.ElementAt(i).Data + "%'";
                                }
                                else
                                {
                                    wherecondition = wherecondition + " " + advnfilter.rules.ElementAt(i).Condition + " " + advnfilter.rules.ElementAt(i).Field + " " + op + " '" + advnfilter.rules.ElementAt(i).Data + "'";
                                }
                            }
                        }
                        wherecondition = wherecondition.Replace("= 'is null'", "");
                    }
                }
                wherecondition = wherecondition;

                query = ";WITH NewT AS(SELECT p.Product_ID,Product_UniqueNo ,p.MachineStatus_ID,p.Company_ID,p.Model_ID,m.Model_Name, b.RefMasterDetail_Name,pt.ProductType_Name,pa.Party_Name,ma.RefMasterDetail_Name as 'MachineStatus', psa.ProductSiteAddress_SiteAddress, psa.ProductSiteAddress_Location,  Reading.Reading,p.Brand_ID,p.ProductType_ID,Product_SerialNumber, p.IsActive   FROM GNM_Product p  ";
                query += " left outer join GNM_RefMasterDetail b on p.Brand_ID=b.RefMasterDetail_ID left outer join GNM_ProductType pt  on p.ProductType_ID=pt.ProductType_ID left outer join GNM_Model m on p.Model_ID=m.Model_ID left outer join GNM_ProductCustomer pc on   p.Product_ID=pc.Product_ID and Pc.ProductCustomer_ToDate is null left outer join SLT_SALESHISTORY S on s.PRODUCT_ID=pc.Product_ID and s.ProductCustomer_ID ";
                query += " =pc.ProductCustomer_ID   left outer join GNM_Party pa on p.Party_ID=pa.Party_ID left outer join GNM_RefMasterDetail ma on p.MachineStatus_ID=ma.RefMasterDetail_ID    left outer join GNM_ProductSiteAddress psa on psa.Product_ID=p.Product_ID and psa.ProductSiteAddress_IsActive=1  AND psa.Company_ID=" + Convert.ToInt32(Obj.Company_ID) + "   left outer join GNM_ProductReading as Reading on Reading.ProductReadingID in(select B.ProductReadingID from GNM_ProductReading B  ";
                query += " cross Apply(select A.Product_ID,MAX(A.ProductReadingID) Readingid from GNM_ProductReading A group by A.Product_ID)Temp where Temp.Product_ID=b.Product_ID    and B.ProductReadingID=Temp.Readingid ) and P.Product_ID=Reading.Product_ID " + wherecondition + "   and ((IsWholeSaleUser=1 and WholeSaleDealerid=" + Convert.ToInt32(Obj.Company_ID) + " ) or p.ServiceCompany=" + Convert.ToInt32(Obj.Company_ID) + " )   union ";

                query += " SELECT distinct p.Product_ID,Product_UniqueNo ,p.MachineStatus_ID,p.Company_ID,p.Model_ID,m.Model_Name, b.RefMasterDetail_Name,pt.ProductType_Name,pa.Party_Name,ma.RefMasterDetail_Name as 'MachineStatus', psa.ProductSiteAddress_SiteAddress, psa.ProductSiteAddress_Location,  Reading.Reading,p.Brand_ID,p.ProductType_ID,Product_SerialNumber, p.IsActive   FROM GNM_Product p    left outer join GNM_RefMasterDetail b on p.Brand_ID=b.RefMasterDetail_ID left outer join GNM_ProductType pt   on p.ProductType_ID=pt.ProductType_ID left outer join GNM_Model m on p.Model_ID=m.Model_ID join GNM_ProductCustomer pc on ";
                query += "  p.Product_ID=pc.Product_ID and PC.ProductCustomer_ToDate is null   join SLT_SALESHISTORY S on S.PRODUCT_ID=pc.Product_ID and Pc.ProductCustomer_ID=s.ProductCustomer_ID   left outer join GNM_Party pa on p.Party_ID=pa.Party_ID left outer join GNM_RefMasterDetail ma on p.MachineStatus_ID=ma.RefMasterDetail_ID    left outer join GNM_ProductSiteAddress psa on psa.Product_ID=p.Product_ID and psa.ProductSiteAddress_IsActive=1  AND psa.Company_ID=" + Convert.ToInt32(Obj.Company_ID) + "   left outer join GNM_ProductReading as Reading on Reading.ProductReadingID in(select B.ProductReadingID from GNM_ProductReading B ";
                query += " cross Apply(select A.Product_ID,MAX(A.ProductReadingID) Readingid from GNM_ProductReading A group by A.Product_ID)Temp where Temp.Product_ID=b.Product_ID   and B.ProductReadingID=Temp.Readingid ) and P.Product_ID=Reading.Product_ID  " + wherecondition + "  and ISWHOLESALE in(0,2)) select * from NewT ";

                string countquery = string.Empty;
                countquery = query + " select count(Product_ID) from  NewT  drop table #newtremp ";
                //IEnumerable<int> rcount = ProductClient.Database.SqlQuery(typeof(int), countquery).Cast<int>();
                IEnumerable<int> rcount;
                List<int> countList = new List<int>();
                using (SqlConnection connection = new SqlConnection(constring))
                {


                    SqlCommand command = new SqlCommand(countquery, connection);


                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        countList.Add(reader.GetInt32(0));
                    }
                    rcount = countList;
                    reader.Close();
                }
                sidx = sidx == "RefMasterDetail_Name" ? "MachineStatus" : sidx;
                // psa.ProductSiteAddress_SiteAddress,psa.ProductSiteAddress_Location,Reading.Reading,p.Brand_ID,p.ProductType_ID,Product_SerialNumber, p.IsActive 

                string CompanyQuery = "";
                CompanyQuery = ";WITH ParentComapany([Company_ID],[Company_Name],[Company_Parent_ID]) as (SELECT [Company_ID],[Company_Name],[Company_Parent_ID] FROM GNM_Company WHERE [Company_ID] IN (" + Company_ID + ") UNION ALL SELECT child.[Company_ID], child.[Company_Name], child.[Company_Parent_ID] FROM GNM_Company AS child JOIN ParentComapany ON child.[Company_ID] = ParentComapany.[Company_Parent_ID]) SELECT * INTO #A FROM ParentComapany;";

                //IEProductList = ProductClient.Database.SqlQuery(typeof(Machine_ProductMaster), query).Cast<Machine_ProductMaster>().ToList();

                List<Machine_ProductMaster> ProductList = new List<Machine_ProductMaster>();

                using (SqlConnection connection = new SqlConnection(constring))
                {


                    SqlCommand command = new SqlCommand(query, connection);


                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        Machine_ProductMaster product = new Machine_ProductMaster
                        {
                            Product_ID = reader.IsDBNull(reader.GetOrdinal("Product_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Product_ID")),
                            BrandName = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_Name")) ? null : reader.GetString(reader.GetOrdinal("RefMasterDetail_Name")),
                            Model_Name = reader.IsDBNull(reader.GetOrdinal("Model_Name")) ? null : reader.GetString(reader.GetOrdinal("Model_Name")),
                            Party_Name = reader.IsDBNull(reader.GetOrdinal("Party_Name")) ? null : reader.GetString(reader.GetOrdinal("Party_Name")),
                            Product_IsActive = reader.IsDBNull(reader.GetOrdinal("IsActive")) ? null : reader.GetBoolean(reader.GetOrdinal("IsActive")) ? "true" : "false",
                            Product_SerialNumber = reader.IsDBNull(reader.GetOrdinal("Product_SerialNumber")) ? null : reader.GetString(reader.GetOrdinal("Product_SerialNumber")),
                            Product_UniqueNo = reader.IsDBNull(reader.GetOrdinal("Product_UniqueNo")) ? null : reader.GetString(reader.GetOrdinal("Product_UniqueNo")),
                            ProductType_Name = reader.IsDBNull(reader.GetOrdinal("ProductType_Name")) ? null : reader.GetString(reader.GetOrdinal("ProductType_Name")),
                            RefMasterDetail_Name = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_Name")) ? null : reader.GetString(reader.GetOrdinal("RefMasterDetail_Name")),
                            ProductSiteAddress_SiteAddress = reader.IsDBNull(reader.GetOrdinal("ProductSiteAddress_SiteAddress")) ? null : reader.GetString(reader.GetOrdinal("ProductSiteAddress_SiteAddress")),
                            ProductSiteAddress_Location = reader.IsDBNull(reader.GetOrdinal("ProductSiteAddress_Location")) ? null : reader.GetString(reader.GetOrdinal("ProductSiteAddress_Location")),
                            MachineStatus_ID = reader.IsDBNull(reader.GetOrdinal("MachineStatus_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("MachineStatus_ID")),
                            Company_ID = reader.IsDBNull(reader.GetOrdinal("Company_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Company_ID")),
                            //Company_Name = reader.IsDBNull(reader.GetOrdinal("Company_Name")) ? null : reader.GetString(reader.GetOrdinal("Company_Name")),
                            //WareHouse = reader.IsDBNull(reader.GetOrdinal("WareHouse")) ? null : reader.GetString(reader.GetOrdinal("WareHouse")),
                            //Product_EngineSerialNumber = reader.IsDBNull(reader.GetOrdinal("Product_EngineSerialNumber")) ? null : reader.GetString(reader.GetOrdinal("Product_EngineSerialNumber")),
                            //PrimarySegmnet = reader.IsDBNull(reader.GetOrdinal("PrimarySegmnet")) ? null : reader.GetString(reader.GetOrdinal("PrimarySegmnet")),
                            //SecondarySegment = reader.IsDBNull(reader.GetOrdinal("SecondarySegment")) ? null : reader.GetString(reader.GetOrdinal("SecondarySegment")),
                            //CurrentReadingIC = reader.IsDBNull(reader.GetOrdinal("CurrentReadingIC")) ? 0 : reader.GetInt32(reader.GetOrdinal("CurrentReadingIC")),
                            //CurrentReading = reader.IsDBNull(reader.GetOrdinal("CurrentReading")) ? 0 : reader.GetInt32(reader.GetOrdinal("CurrentReading")),
                            //CurrentSiteAddress = reader.IsDBNull(reader.GetOrdinal("CurrentSiteAddress")) ? null : reader.GetString(reader.GetOrdinal("CurrentSiteAddress")),
                            //NextServiceTypestring = reader.IsDBNull(reader.GetOrdinal("NextServiceTypestring")) ? null : reader.GetString(reader.GetOrdinal("NextServiceTypestring")),
                            //OnDatestring = reader.IsDBNull(reader.GetOrdinal("OnDatestring")) ? null : reader.GetString(reader.GetOrdinal("OnDatestring")),
                            //NextServiceDate = reader.IsDBNull(reader.GetOrdinal("NextServiceDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("NextServiceDate")),
                            //Prefix = reader.IsDBNull(reader.GetOrdinal("Prefix")) ? null : reader.GetString(reader.GetOrdinal("Prefix")),
                            //PartNum = reader.IsDBNull(reader.GetOrdinal("PartNum")) ? null : reader.GetString(reader.GetOrdinal("PartNum")),
                            //DateOfManufacture = reader.IsDBNull(reader.GetOrdinal("DateOfManufacture")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("DateOfManufacture")),
                            //DateOFManufacturestring = reader.IsDBNull(reader.GetOrdinal("DateOFManufacturestring")) ? null : reader.GetString(reader.GetOrdinal("DateOFManufacturestring")),
                            //DateofSalestring = reader.IsDBNull(reader.GetOrdinal("DateofSalestring")) ? null : reader.GetString(reader.GetOrdinal("DateofSalestring")),
                            //DateOfInstallationstring = reader.IsDBNull(reader.GetOrdinal("DateOfInstallationstring")) ? null : reader.GetString(reader.GetOrdinal("DateOfInstallationstring")),
                            //MachineStatus = reader.IsDBNull(reader.GetOrdinal("MachineStatus")) ? null : reader.GetString(reader.GetOrdinal("MachineStatus")),
                            //Brand_ID = reader.IsDBNull(reader.GetOrdinal("Brand_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Brand_ID")),
                            //ProductType_ID = reader.IsDBNull(reader.GetOrdinal("ProductType_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("ProductType_ID")),
                            //Model_ID = reader.IsDBNull(reader.GetOrdinal("Model_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Model_ID")),
                            //LastHMRUpdatedDate = reader.IsDBNull(reader.GetOrdinal("LastHMRUpdatedDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("LastHMRUpdatedDate")),
                            //LastHMRUpdatedDateSort = reader.IsDBNull(reader.GetOrdinal("LastHMRUpdatedDateSort")) ? null : reader.GetString(reader.GetOrdinal("LastHMRUpdatedDateSort")),
                            //LastServiceDate = reader.IsDBNull(reader.GetOrdinal("LastServiceDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("LastServiceDate")),
                            //LastServiceDateSort = reader.IsDBNull(reader.GetOrdinal("LastServiceDateSort")) ? null : reader.GetString(reader.GetOrdinal("LastServiceDateSort")),
                            //Warranty = reader.IsDBNull(reader.GetOrdinal("Warranty")) ? null : reader.GetString(reader.GetOrdinal("Warranty")),
                            //WarrantyAggreement = reader.IsDBNull(reader.GetOrdinal("WarrantyAggreement")) ? null : reader.GetString(reader.GetOrdinal("WarrantyAggreement")),
                            //MachineLocation = reader.IsDBNull(reader.GetOrdinal("MachineLocation")) ? null : reader.GetString(reader.GetOrdinal("MachineLocation")),
                        };
                        ProductList.Add(product);
                    }
                    reader.Close();
                }

                IEProductList = ProductList.AsQueryable();

                //Session["Export"] = wherecondition + "Order By " + sidx + " " + sord;
                //Session["ExpLangID"] = LanguageID.ToString();

                //List<GNM_ProductSiteAddress> ActiveSiteAddressList = ProductClient.GNM_ProductSiteAddress.Where(A => A.ProductSiteAddress_IsActive == true).Select(B => B).ToList();
                List<GNM_ProductSiteAddress> ActiveSiteAddressList = new List<GNM_ProductSiteAddress>();
                string queryP = @"
                SELECT ProductSiteAddress_ID, Product_ID, ProductSiteAddress_SiteAddress, 
                       ProductSiteAddress_Location, ProductSiteAddress_IsActive, 
                       Company_ID, Region_ID, Country_ID, State_ID
                FROM GNM_ProductSiteAddress
                WHERE ProductSiteAddress_IsActive = 1;";
                using (SqlConnection connection = new SqlConnection(constring))
                {


                    SqlCommand command = new SqlCommand(queryP, connection);


                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        GNM_ProductSiteAddress siteAddress = new GNM_ProductSiteAddress
                        {
                            ProductSiteAddress_ID = reader.GetInt32(reader.GetOrdinal("ProductSiteAddress_ID")),
                            Product_ID = reader.GetInt32(reader.GetOrdinal("Product_ID")),
                            ProductSiteAddress_SiteAddress = reader.IsDBNull(reader.GetOrdinal("ProductSiteAddress_SiteAddress"))
                                ? null
                                : reader.GetString(reader.GetOrdinal("ProductSiteAddress_SiteAddress")),
                            ProductSiteAddress_Location = reader.IsDBNull(reader.GetOrdinal("ProductSiteAddress_Location"))
                                ? null
                                : reader.GetString(reader.GetOrdinal("ProductSiteAddress_Location")),
                            ProductSiteAddress_IsActive = reader.GetBoolean(reader.GetOrdinal("ProductSiteAddress_IsActive")),
                            Company_ID = reader.IsDBNull(reader.GetOrdinal("Company_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Company_ID")),
                            Region_ID = reader.IsDBNull(reader.GetOrdinal("Region_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Region_ID")),
                            Country_ID = reader.IsDBNull(reader.GetOrdinal("Country_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Country_ID")),
                            State_ID = reader.IsDBNull(reader.GetOrdinal("State_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("State_ID")),
                        };
                        ActiveSiteAddressList.Add(siteAddress);
                    }
                    reader.Close();
                }

                string queryR = "select B.* from GNM_ProductReading B cross Apply(select A.Product_ID,MAX(A.Reference_Date) Reference_Date from GNM_ProductReading A group by A.Product_ID)Temp where Temp.Product_ID=b.Product_ID and B.Reference_Date=Temp.Reference_Date";
                //IEProductReadingList = ProductClient.Database.SqlQuery(typeof(GNM_ProductReading), queryR).Cast<GNM_ProductReading>().ToList();

                List<GNM_ProductReading> ProductReadingList = new List<GNM_ProductReading>();
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    SqlCommand command = new SqlCommand(queryR, connection);
                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        GNM_ProductReading productReading = new GNM_ProductReading
                        {
                            ProductReadingID = reader.GetInt32(reader.GetOrdinal("ProductReadingID")),
                            Product_ID = reader.IsDBNull(reader.GetOrdinal("Product_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Product_ID")),
                            ProductComponent_ID = reader.IsDBNull(reader.GetOrdinal("ProductComponent_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ProductComponent_ID")),
                            Mode = reader.IsDBNull(reader.GetOrdinal("Mode")) ? 0 : reader.GetInt32(reader.GetOrdinal("Mode")),
                            Company_ID = reader.IsDBNull(reader.GetOrdinal("Company_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Company_ID")),
                            Reference_Number = reader.IsDBNull(reader.GetOrdinal("Reference_Number")) ? string.Empty : reader.GetString(reader.GetOrdinal("Reference_Number")),
                            Reference_Date = (DateTime)(reader.IsDBNull(reader.GetOrdinal("Reference_Date")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("Reference_Date"))),
                            Reading = reader.IsDBNull(reader.GetOrdinal("Reading")) ? 0 : reader.GetInt32(reader.GetOrdinal("Reading")),
                            JobCardId = reader.IsDBNull(reader.GetOrdinal("JobCardId")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("JobCardId")),
                            AccumulatedHMR = reader.IsDBNull(reader.GetOrdinal("AccumulatedHMR")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("AccumulatedHMR")),
                            GNM_Product = new GNM_Product
                            {
                                Product_ID = reader.IsDBNull(reader.GetOrdinal("Product_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Product_ID")),
                            },
                            GNM_ProductComponent = new GNM_ProductComponent
                            {
                                ProductComponent_ID = reader.IsDBNull(reader.GetOrdinal("ProductComponent_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("ProductComponent_ID")),
                            }
                        };
                        ProductReadingList.Add(productReading);
                    }

                    reader.Close();
                }
                IQueryable<GNM_ProductReading> IQProducting = ProductReadingList.AsQueryable();
                // refmaster detail 
                string queryList = "SELECT * FROM GNM_RefMasterDetailLocale;";
                List<GNM_RefMasterDetailLocale> refMasterDetailLocaleList = new List<GNM_RefMasterDetailLocale>();


                using (SqlConnection connection = new SqlConnection(constring))
                {

                    SqlCommand command = new SqlCommand(queryList, connection);


                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        while (reader.Read())
                        {
                            GNM_RefMasterDetailLocale refMasterDetailLocale = new GNM_RefMasterDetailLocale
                            {
                                RefMasterDetailLocale_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetailLocale_ID")),
                                RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                RefMaster_ID = reader.GetInt32(reader.GetOrdinal("RefMaster_ID")),
                                RefMasterDetail_Short_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Short_Name")),
                                RefMasterDetail_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name")),
                                Language_ID = reader.GetInt32(reader.GetOrdinal("Language_ID")),
                            };
                            refMasterDetailLocaleList.Add(refMasterDetailLocale);
                        }
                    }
                    reader.Close();
                }

                List<GNM_ProductTypeLocale> productTypeLocaleList = new List<GNM_ProductTypeLocale>();

                string GNM_ProductTypeLocale = "SELECT * FROM GNM_ProductTypeLocale;";
                using (SqlConnection connection = new SqlConnection(constring))
                {

                    SqlCommand command = new SqlCommand(GNM_ProductTypeLocale, connection);


                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        while (reader.Read())
                        {
                            while (reader.Read())
                            {
                                GNM_ProductTypeLocale productTypeLocale = new GNM_ProductTypeLocale
                                {
                                    ProductTypeLocale_ID = reader.GetInt32(reader.GetOrdinal("ProductTypeLocale_ID")),
                                    ProductType_ID = reader.GetInt32(reader.GetOrdinal("ProductType_ID")),
                                    ProductType_Name = reader.IsDBNull(reader.GetOrdinal("ProductType_Name")) ? null : reader.GetString(reader.GetOrdinal("ProductType_Name")),
                                    Language_ID = reader.GetInt32(reader.GetOrdinal("Language_ID")),
                                };
                                productTypeLocaleList.Add(productTypeLocale);
                            }
                        }
                    }
                    reader.Close();
                }


                List<GNM_ModelLocale> ModelLocaleList = new List<GNM_ModelLocale>();

                string GNM_ModelLocale = "select * from GNM_ModelLocale;";
                using (SqlConnection connection = new SqlConnection(constring))
                {

                    SqlCommand command = new SqlCommand(GNM_ModelLocale, connection);


                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        while (reader.Read())
                        {
                            while (reader.Read())
                            {
                                GNM_ModelLocale ModelTypeLocale = new GNM_ModelLocale
                                {
                                    ModelLocale_ID = reader.GetInt32(reader.GetOrdinal("ModelLocale_ID")),
                                    Model_ID = reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                    Model_Name = reader.IsDBNull(reader.GetOrdinal("Model_Name")) ? null : reader.GetString(reader.GetOrdinal("Model_Name")),
                                    Language_ID = reader.GetInt32(reader.GetOrdinal("Language_ID")),
                                    Model_Description = reader.IsDBNull(reader.GetOrdinal("Model_Description")) ? null : reader.GetString(reader.GetOrdinal("Model_Description")),
                                };
                                ModelLocaleList.Add(ModelTypeLocale);
                            }
                        }
                    }
                    reader.Close();
                }

                List<GNM_ProductCustomer> ProductCustomerList = new List<GNM_ProductCustomer>();

                string ProductCustomerQ = "select * from GNM_ProductCustomer;";
                using (SqlConnection connection = new SqlConnection(constring))
                {

                    SqlCommand command = new SqlCommand(ProductCustomerQ, connection);


                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        while (reader.Read())
                        {
                            while (reader.Read())
                            {
                                GNM_ProductCustomer ProductCustomer = new GNM_ProductCustomer
                                {
                                    ProductCustomer_ID = reader.GetInt32(reader.GetOrdinal("ProductCustomer_ID")),
                                    Product_ID = reader.GetInt32(reader.GetOrdinal("Product_ID")),
                                    Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                    ProductCustomer_FromDate = reader.GetDateTime(reader.GetOrdinal("ProductCustomer_FromDate")),
                                    ProductCustomer_ToDate = reader.IsDBNull(reader.GetOrdinal("ProductCustomer_ToDate")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("ProductCustomer_ToDate")),
                                    Company_ID = reader.IsDBNull(reader.GetOrdinal("Company_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                    IsTransacted = reader.IsDBNull(reader.GetOrdinal("IsTransacted")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsTransacted")),
                                };
                                ProductCustomerList.Add(ProductCustomer);
                            }
                        }
                    }
                    reader.Close();
                }

                List<GNM_PartyLocale> PartyLocaleList = new List<GNM_PartyLocale>();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    string GNM_PartyLocale = "select * from GNM_PartyLocale;";

                    SqlCommand command = new SqlCommand(GNM_PartyLocale, connection);


                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        while (reader.Read())
                        {
                            while (reader.Read())
                            {
                                GNM_PartyLocale PartyLocale = new GNM_PartyLocale
                                {
                                    Party_Locale_ID = reader.GetInt32(reader.GetOrdinal("Party_Locale_ID")),
                                    Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                    Party_Name = reader.IsDBNull(reader.GetOrdinal("Party_Name")) ? null : reader.GetString(reader.GetOrdinal("Party_Name")),
                                    Party_Location = reader.IsDBNull(reader.GetOrdinal("Party_Location")) ? null : reader.GetString(reader.GetOrdinal("Party_Location")),
                                    Party_PaymentTerms = reader.IsDBNull(reader.GetOrdinal("Party_PaymentTerms")) ? null : reader.GetString(reader.GetOrdinal("Party_PaymentTerms")),
                                    Language_ID = reader.GetInt32(reader.GetOrdinal("Language_ID")),
                                    Party_Address = reader.IsDBNull(reader.GetOrdinal("Party_Address")) ? null : reader.GetString(reader.GetOrdinal("Party_Address")),
                                    Party_Code = reader.IsDBNull(reader.GetOrdinal("Party_Code")) ? null : reader.GetString(reader.GetOrdinal("Party_Code")),
                                };
                                PartyLocaleList.Add(PartyLocale);
                            }
                        }
                    }
                    reader.Close();
                }



                if (LanguageID == Convert.ToInt32(Obj.GeneralLanguageID))
                {

                    IEProductMasterArray = from a in IEProductList
                                           select new Machine_ProductMaster
                                           {
                                               Product_ID = a.Product_ID,
                                               BrandName = a.RefMasterDetail_Name,
                                               Model_Name = a.Model_Name,
                                               Party_Name = a.Party_Name,
                                               Product_IsActive = (a.IsActive == true ? YesE : NoE),//updated by kavitha for Writgen changes
                                               Product_SerialNumber = a.Product_SerialNumber,
                                               Product_UniqueNo = a.Product_UniqueNo,
                                               ProductType_Name = a.ProductType_Name,
                                               MachineStatus = a.MachineStatus,
                                               ProductSiteAddress_SiteAddress = a.ProductSiteAddress_SiteAddress,
                                               Reading = (a.Reading == null ? 0 : a.Reading),
                                               ProductSiteAddress_Location = a.ProductSiteAddress_Location,
                                               MachineStatus_ID = a.MachineStatus_ID,

                                           };


                }
                else
                {
                    IEProductMasterArray = from a in IEProductList
                                           join b in refMasterDetailLocaleList on a.Brand_ID equals b.RefMasterDetail_ID
                                           join c in productTypeLocaleList on a.ProductType_ID equals c.ProductType_ID
                                           join d in ModelLocaleList on a.Model_ID equals d.Model_ID
                                           join e in ProductCustomerList on a.Product_ID equals e.Product_ID into ProductCustomer
                                           from ProductCustomerDeatils in ProductCustomer.DefaultIfEmpty(new GNM_ProductCustomer { Product_ID = 0, Party_ID = 0 })
                                           join f in PartyLocaleList on ProductCustomerDeatils.Party_ID equals f.Party_ID into Party
                                           from PartyDetails in Party.DefaultIfEmpty(new GNM_PartyLocale { Party_ID = 0, Language_ID = LanguageID, Party_Name = "" })
                                           join ma in refMasterDetailLocaleList on a.MachineStatus_ID equals ma.RefMasterDetail_ID into MSList
                                           from MSFinal in MSList.DefaultIfEmpty(new GNM_RefMasterDetailLocale { RefMasterDetail_ID = 0, RefMasterDetail_Name = "" })
                                           join ASA in ActiveSiteAddressList on a.Product_ID equals ASA.Product_ID into SitList
                                           from SitListFinal in SitList.DefaultIfEmpty(new GNM_ProductSiteAddress { Product_ID = 0, ProductSiteAddress_SiteAddress = "", ProductSiteAddress_Location = "" })
                                           join hmr in IEProductReadingList on a.Product_ID equals hmr.Product_ID into readingList
                                           from readinglistFinal in readingList.DefaultIfEmpty(new GNM_ProductReading { Reading = 0 })
                                           where ProductCustomerDeatils.ProductCustomer_ToDate == null && b.Language_ID == LanguageID && c.Language_ID == LanguageID && d.Language_ID == LanguageID && PartyDetails.Language_ID == LanguageID && MSFinal.Language_ID == LanguageID
                                           select new Machine_ProductMaster
                                           {
                                               Product_ID = a.Product_ID,
                                               BrandName = b.RefMasterDetail_Name,
                                               Model_Name = d.Model_Name,
                                               Party_Name = PartyDetails.Party_Name,
                                               Product_IsActive = (a.IsActive == true ? YesL : NoL),//updated by kavitha for Writgen changes
                                               Product_SerialNumber = a.Product_SerialNumber,
                                               Product_UniqueNo = a.Product_UniqueNo,
                                               ProductType_Name = c.ProductType_Name,
                                               RefMasterDetail_Name = MSFinal.RefMasterDetail_Name,
                                               SiteAddress = SitListFinal.ProductSiteAddress_SiteAddress,
                                               MachineHMR = readinglistFinal.Reading,
                                               ProductSiteAddress_Location = SitListFinal.ProductSiteAddress_Location,
                                               MachineStatus_ID = MSFinal.RefMasterDetail_ID
                                           };
                }

                IQProudctMaster = IEProductMasterArray.AsQueryable<Machine_ProductMaster>();
                IQProudctMaster = IQProudctMaster.OrderByField<Machine_ProductMaster>(sidx, sord);
                Count = IQProudctMaster.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                jsonData = new
                {
                    total = Total,
                    page = page,
                    rows = (from a in IQProudctMaster.AsEnumerable()
                            select new
                            {
                                edit = "<a title='View' href='#' style='font-size: 13px;' id='" + a.Product_ID + "' key='" + a.Product_ID + "' Model='" + a.Model_Name + "' SerialNumber='" + a.Product_SerialNumber + "' MachineStatus='" + a.MachineStatus + "' SiteAddress='" + a.ProductSiteAddress_SiteAddress + "' Location='" + a.ProductSiteAddress_Location + "' MAchineHMR='" + a.Reading + "' StatusID='" + a.MachineStatus_ID + "'class='EditMachineUpdate' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input type='checkbox' key='" + a.Product_ID + "' defaultchecked=''  id='chk" + a.Product_ID + "' class='ProductDelete'/>",
                                Model_Name = (a.Model_Name),
                                Party_Name = (a.Party_Name),
                                a.IsActive,//updated by kavitha for Writgen changes
                                Product_SerialNumber = (a.Product_SerialNumber),
                                Product_UniqueNo = (a.Product_UniqueNo),
                                ProductType_Name = (a.ProductType_Name),
                                BrandName = (a.BrandName),
                                MachineStatus = (a.MachineStatus),
                                ProductSiteAddress_SiteAddress = (a.ProductSiteAddress_SiteAddress),
                                Reading = a.Reading,
                            }).ToList().Paginate(page, rows),
                    records = Count,
                    ProudctMaster = IQProudctMaster,
                    filter = filters,
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }

        #endregion



        private static bool ColumnExists(SqlDataReader reader, string columnName)
        {
            try
            {
                return reader.GetOrdinal(columnName) >= 0;
            }
            catch (IndexOutOfRangeException)
            {
                return false;

            }
        }





        public class MachineUpdateSelectList
        {
            public string GeneralCulture { get; set; }
            public string UserCulture { get; set; }
            public int Company_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int LanguageID { get; set; }
        }
        public partial class SaveAttachmentList
        {
            public string AttachmentData { get; set; }
            public string AttachmentsData { get; set; }
            public string AttachmentDelete { get; set; }
            public string AppPathString { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int LanguageID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public int ProductId { get; set; }
            public int SalesHistoryID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public partial class MachineUpdateList
        {
            public int Product_ID { get; set; }
            public int MachineHMR { get; set; }
            public int MachineStatusId { get; set; }
            public int PreviousHMR { get; set; }
            public string SiteAddress { get; set; }
            public string Location { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int LanguageID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public partial class CheckMachineUpdateReadingDetailsList
        {
            public int Reading { get; set; }
            public int ProductID { get; set; }
            public int ProductComponent_ID { get; set; }
        }
        public partial class GNM_Product
        {
            public GNM_Product()
            {
                this.GNM_ChangeVehicleOwnershipLog = new HashSet<GNM_ChangeVehicleOwnershipLog>();
                this.GNM_ProductCSA = new HashSet<GNM_ProductCSA>();
                this.GNM_ProductComponent = new HashSet<GNM_ProductComponent>();
                this.GNM_ProductComponentWarranty = new HashSet<GNM_ProductComponentWarranty>();
                this.GNM_ProductContractor = new HashSet<GNM_ProductContractor>();
                this.GNM_ProductCSA1 = new HashSet<GNM_ProductCSA>();
                this.GNM_ProductCSAServiceSchedule = new HashSet<GNM_ProductCSAServiceSchedule>();
                this.GNM_ProductCustomer = new HashSet<GNM_ProductCustomer>();
                this.GNM_ProductInvoiceDetail = new HashSet<GNM_ProductInvoiceDetail>();
                this.GNM_ProductSiteAddress = new HashSet<GNM_ProductSiteAddress>();
                this.GNM_ProductStatusHistory = new HashSet<GNM_ProductStatusHistory>();
                this.GNM_ProductWarranty = new HashSet<GNM_ProductWarranty>();
                this.GNM_ProductReading = new HashSet<GNM_ProductReading>();
            }

            public int Product_ID { get; set; }
            public int Company_ID { get; set; }
            public string Product_UniqueNo { get; set; }
            public int Model_ID { get; set; }
            public int Brand_ID { get; set; }
            public int ProductType_ID { get; set; }
            public string Product_SerialNumber { get; set; }
            public Nullable<int> PrimarySegment_ID { get; set; }
            public Nullable<int> SecondarySegment_ID { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }
            public Nullable<bool> IsComponent { get; set; }
            public Nullable<int> Party_ID { get; set; }
            public Nullable<int> NextServiceType_ID { get; set; }
            public Nullable<System.DateTime> NextServiceDate { get; set; }
            public Nullable<int> MachineStatus_ID { get; set; }
            public Nullable<int> AverageReadingPerDay { get; set; }
            public Nullable<System.DateTime> CommissioningDate { get; set; }
            public Nullable<bool> IsActive { get; set; }
            public Nullable<int> Warehouse_ID { get; set; }
            public Nullable<int> Parts_ID { get; set; }
            public Nullable<int> SerialStatus { get; set; }
            public Nullable<System.DateTime> DateOfManufacture { get; set; }
            public string Product_EngineSerialNumber { get; set; }
            public Nullable<int> ServiceCompany { get; set; }
            public Nullable<System.DateTime> InvoiceDate { get; set; }
            public Nullable<decimal> LandingCost { get; set; }
            public Nullable<System.DateTime> WarrantyEndDate { get; set; }
            public Nullable<System.DateTime> DateOfSale { get; set; }
            public Nullable<byte> AttachmentCount { get; set; }
            public Nullable<int> WholeSaleDealerid { get; set; }
            public Nullable<bool> IsWholeSaleUser { get; set; }
            public Nullable<int> ServiceEngineer_ID { get; set; }
            public string Reading_Unit { get; set; }
            public Nullable<int> LastServiceBranch { get; set; }
            public Nullable<bool> IsPCPApplicable { get; set; }
            public Nullable<byte> PCPFrequency { get; set; }
            public Nullable<byte> PCPUsedCount { get; set; }
            public Nullable<bool> IsClaasMachine { get; set; }
            public string Series { get; set; }
            public Nullable<bool> isSeriesAttachmentAdded { get; set; }

            public virtual ICollection<GNM_ChangeVehicleOwnershipLog> GNM_ChangeVehicleOwnershipLog { get; set; }
            public virtual ICollection<GNM_ProductCSA> GNM_ProductCSA { get; set; }
            public virtual ICollection<GNM_ProductComponent> GNM_ProductComponent { get; set; }
            public virtual ICollection<GNM_ProductComponentWarranty> GNM_ProductComponentWarranty { get; set; }
            public virtual ICollection<GNM_ProductContractor> GNM_ProductContractor { get; set; }
            public virtual ICollection<GNM_ProductCSA> GNM_ProductCSA1 { get; set; }
            public virtual ICollection<GNM_ProductCSAServiceSchedule> GNM_ProductCSAServiceSchedule { get; set; }
            public virtual ICollection<GNM_ProductCustomer> GNM_ProductCustomer { get; set; }
            public virtual ICollection<GNM_ProductInvoiceDetail> GNM_ProductInvoiceDetail { get; set; }
            public virtual ICollection<GNM_ProductSiteAddress> GNM_ProductSiteAddress { get; set; }
            public virtual ICollection<GNM_ProductStatusHistory> GNM_ProductStatusHistory { get; set; }
            public virtual ICollection<GNM_ProductWarranty> GNM_ProductWarranty { get; set; }
            public virtual ICollection<GNM_ProductReading> GNM_ProductReading { get; set; }
        }

        public partial class GNM_ProductSiteAddress
        {
            public int ProductSiteAddress_ID { get; set; }
            public int Product_ID { get; set; }
            public string ProductSiteAddress_SiteAddress { get; set; }
            public string ProductSiteAddress_Location { get; set; }
            public bool ProductSiteAddress_IsActive { get; set; }
            public Nullable<int> Company_ID { get; set; }
            public Nullable<int> Region_ID { get; set; }
            public Nullable<int> Country_ID { get; set; }
            public Nullable<int> State_ID { get; set; }

            public virtual GNM_Product GNM_Product { get; set; }
        }

        public partial class GNM_ProductReading
        {
            public int ProductReadingID { get; set; }
            public Nullable<int> Product_ID { get; set; }
            public Nullable<int> ProductComponent_ID { get; set; }
            public int Mode { get; set; }
            public int Company_ID { get; set; }
            public string Reference_Number { get; set; }
            public System.DateTime Reference_Date { get; set; }
            public int Reading { get; set; }
            public Nullable<int> JobCardId { get; set; }
            public Nullable<decimal> AccumulatedHMR { get; set; }

            public virtual GNM_Product GNM_Product { get; set; }
            public virtual GNM_ProductComponent GNM_ProductComponent { get; set; }
        }

        public partial class GNM_PartyBranchAssociation
        {
            public int PartyBranch_ID { get; set; }
            public int Party_ID { get; set; }
            public int Branch_ID { get; set; }

            public virtual GNM_Party GNM_Party { get; set; }
        }
        public partial class GNM_PartyProductAssociation
        {
            public int PartyProduct_ID { get; set; }
            public int Party_ID { get; set; }
            public int Brand_ID { get; set; }
            public Nullable<int> ProductType_ID { get; set; }
            public Nullable<int> Model_ID { get; set; }

            public virtual GNM_Party GNM_Party { get; set; }
        }
        public partial class GNM_PartySkillset
        {
            public int Party_Skillset_ID { get; set; }
            public int Party_ID { get; set; }
            public int Party_Skillset_Rating { get; set; }
            public int Skillset_ID { get; set; }

            public virtual GNM_Party GNM_Party { get; set; }
        }
        public partial class GNM_ServiceSchedule
        {
            public int PartyServiceSchedule_ID { get; set; }
            public int Party_ID { get; set; }
            public int ServiceType_ID { get; set; }
            public System.DateTime ServiceDate { get; set; }
            public Nullable<byte> Status { get; set; }
            public string Closure_reason { get; set; }
            public Nullable<int> JobCard_ID { get; set; }

            public virtual GNM_Party GNM_Party { get; set; }
        }
        public partial class GNM_PartyLocale
        {
            public int Party_Locale_ID { get; set; }
            public int Party_ID { get; set; }
            public string Party_Name { get; set; }
            public string Party_Location { get; set; }
            public string Party_PaymentTerms { get; set; }
            public int Language_ID { get; set; }
            public string Party_Address { get; set; }
            public string Party_Code { get; set; }

            public virtual GNM_Party GNM_Party { get; set; }
        }

        public partial class GNM_PartySegmentDetails
        {
            public int PartySegment_ID { get; set; }
            public int Party_ID { get; set; }
            public int PrimarySegment_ID { get; set; }
            public Nullable<int> SecondarySegment_ID { get; set; }

            public virtual GNM_Party GNM_Party { get; set; }
        }
        public partial class GNM_PartyContactLocale
        {
            public int PartyContactLocale_ID { get; set; }
            public int PartyContactPerson_ID { get; set; }
            public string PartyContact_Name { get; set; }
            public int Language_ID { get; set; }
            public string PartyContact_Department { get; set; }
            public string PartyContact_Remarks { get; set; }
            public Nullable<int> Party_ID { get; set; }

            public virtual GNM_PartyContactPersonDetails GNM_PartyContactPersonDetails { get; set; }
        }
        public partial class GNM_PartyAddressLocale
        {
            public int PartyAddressLocale_ID { get; set; }
            public string PartyAddressLocale_Location { get; set; }
            public string PartyAddressLocale_Address { get; set; }
            public int Language_ID { get; set; }
            public int PartyAddress_ID { get; set; }

            public virtual GNM_PartyAddress GNM_PartyAddress { get; set; }
        }
        public partial class GNM_PARTYCONTRACTDETAILS
        {
            public int PartyContract_ID { get; set; }
            public int Party_ID { get; set; }
            public string AgreementNumber { get; set; }
            public System.DateTime FromDate { get; set; }
            public System.DateTime ToDate { get; set; }
            public decimal ContractValue { get; set; }
            public string Unit { get; set; }
            public int Currency { get; set; }
            public string Remarks { get; set; }

            public virtual GNM_Party GNM_Party { get; set; }
        }

        public partial class GNM_PartyTaxStructure
        {
            public int PartyTaxStructure_ID { get; set; }
            public int Party_ID { get; set; }
            public int TaxStructure_ID { get; set; }
            public string TaxStructure_Name { get; set; }

            public virtual GNM_Party GNM_Party { get; set; }
        }
        public partial class GNM_PartyDiscount
        {
            public int PartyDiscount_ID { get; set; }
            public int Party_ID { get; set; }
            public Nullable<decimal> Parts_Discount { get; set; }
            public Nullable<decimal> Service_Discount { get; set; }
            public Nullable<System.DateTime> Effective_Date { get; set; }

            public virtual GNM_Party GNM_Party { get; set; }
        }
        public partial class GNM_PartyPartsRatecontract
        {
            public int PartyPartsRateContract_ID { get; set; }
            public int Party_ID { get; set; }
            public int Part_ID { get; set; }
            public decimal Rate { get; set; }
            public Nullable<decimal> Quantity { get; set; }
            public System.DateTime Effective_FromDate { get; set; }
            public Nullable<System.DateTime> Effective_ToDate { get; set; }
            public Nullable<int> Currency_ID { get; set; }
            public Nullable<int> UploadedBy { get; set; }
            public Nullable<System.DateTime> UploadedDate { get; set; }

            public virtual GNM_Party GNM_Party { get; set; }
        }
        public partial class GNM_PartyAmount
        {
            public int PartyAmount_ID { get; set; }
            public int Party_ID { get; set; }
            public int Company_ID { get; set; }
            public Nullable<decimal> PartsOutStandingCredit { get; set; }
            public Nullable<decimal> ServiceOutStandingCredit { get; set; }
            public Nullable<decimal> RemanOutStandingCredit { get; set; }
            public Nullable<decimal> SalesOutStandingCredit { get; set; }
            public Nullable<decimal> PartyAdvanceAmount { get; set; }
            public Nullable<int> Currency_ID { get; set; }

            public virtual GNM_Party GNM_Party { get; set; }
        }
        public partial class GNM_CreditDetails
        {
            public int CreditDetails_ID { get; set; }
            public Nullable<int> Currency_ID { get; set; }
            public Nullable<int> Party_ID { get; set; }
            public Nullable<decimal> Parts_Credit_Limit { get; set; }
            public Nullable<decimal> Service_Credit_Limit { get; set; }
            public Nullable<decimal> Sales_Credit_Limit { get; set; }

            public virtual GNM_Party GNM_Party { get; set; }
        }
        public partial class GNM_PartyCreditLimitLog
        {
            public int PartyCreditLimitLog_ID { get; set; }
            public System.DateTime UpdatedDateTime { get; set; }
            public int Currency_ID { get; set; }
            public decimal CreditLimit { get; set; }
            public string Remarks { get; set; }
            public Nullable<int> Party_ID { get; set; }

            public virtual GNM_Party GNM_Party { get; set; }
        }
        public partial class GNM_PartyAddress
        {
            public GNM_PartyAddress()
            {
                this.GNM_PartyAddressLocale = new HashSet<GNM_PartyAddressLocale>();
            }

            public int PartyAddress_ID { get; set; }
            public int Party_ID { get; set; }
            public string PartyAddress_Location { get; set; }
            public string PartyAddress_Address { get; set; }
            public int PartyAddress_LeadTimeInDays { get; set; }
            public Nullable<int> PartyAddress_CountryID { get; set; }
            public Nullable<int> PartyAddress_StateID { get; set; }
            public bool PartyAddress_Active { get; set; }
            public string PartyAddress_ZIP { get; set; }
            public bool IsDefault { get; set; }
            public Nullable<int> EquivalentConsignee_ID { get; set; }
            public Nullable<int> Region_ID { get; set; }

            public virtual GNM_Party GNM_Party { get; set; }
            public virtual ICollection<GNM_PartyAddressLocale> GNM_PartyAddressLocale { get; set; }
        }
        public partial class GNM_PartyContactPersonDetails
        {
            public GNM_PartyContactPersonDetails()
            {
                this.GNM_PartyContactLocale = new HashSet<GNM_PartyContactLocale>();
            }

            public int PartyContactPerson_ID { get; set; }
            public Nullable<int> Party_ID { get; set; }
            public string PartyContactPerson_Name { get; set; }
            public string PartyContactPerson_Email { get; set; }
            public string PartyContactPerson_Department { get; set; }
            public string PartyContactPerson_Mobile { get; set; }
            public string PartyContactPerson_Phone { get; set; }
            public bool Party_IsDefaultContact { get; set; }
            public bool PartyContactPerson_IsActive { get; set; }
            public string PartyContactPerson_Remarks { get; set; }
            public int Language_ID { get; set; }
            public Nullable<System.DateTime> PartyContactPerson_DOB { get; set; }

            public virtual GNM_Party GNM_Party { get; set; }
            public virtual ICollection<GNM_PartyContactLocale> GNM_PartyContactLocale { get; set; }
        }
        public partial class GNM_PartyTaxDetails
        {
            public int PartyTax_ID { get; set; }
            public int Party_ID { get; set; }
            public string PartyTax_TaxCode { get; set; }
            public string PartyTax_TaxCodeDescription { get; set; }
            public bool PartyTaxDetails_IsActive { get; set; }

            public virtual GNM_Party GNM_Party { get; set; }
        }
        public partial class GNM_Party
        {
            public GNM_Party()
            {
                this.GNM_PartyBranchAssociation = new HashSet<GNM_PartyBranchAssociation>();
                this.GNM_PartyProductAssociation = new HashSet<GNM_PartyProductAssociation>();
                this.GNM_PartySkillset = new HashSet<GNM_PartySkillset>();
                this.GNM_ServiceSchedule = new HashSet<GNM_ServiceSchedule>();
                this.GNM_PartyLocale = new HashSet<GNM_PartyLocale>();
                this.GNM_PartySegmentDetails = new HashSet<GNM_PartySegmentDetails>();
                this.GNM_PartyTaxDetails = new HashSet<GNM_PartyTaxDetails>();
                this.GNM_PartyContactPersonDetails = new HashSet<GNM_PartyContactPersonDetails>();
                this.GNM_PartyAddress = new HashSet<GNM_PartyAddress>();
                this.GNM_PARTYCONTRACTDETAILS = new HashSet<GNM_PARTYCONTRACTDETAILS>();
                this.GNM_PartyTaxStructure = new HashSet<GNM_PartyTaxStructure>();
                this.GNM_PartyDiscount = new HashSet<GNM_PartyDiscount>();
                this.GNM_PartyPartsRatecontract = new HashSet<GNM_PartyPartsRatecontract>();
                this.GNM_PartyCreditLimitLog = new HashSet<GNM_PartyCreditLimitLog>();
                this.GNM_PartyAmount = new HashSet<GNM_PartyAmount>();
                this.GNM_CreditDetails = new HashSet<GNM_CreditDetails>();
            }

            public int Party_ID { get; set; }
            public bool Party_IsActive { get; set; }
            public bool Party_IsLocked { get; set; }
            public string Party_Name { get; set; }
            public string Party_Location { get; set; }
            public string Party_Email { get; set; }
            public string Party_Phone { get; set; }
            public string Party_Fax { get; set; }
            public string Party_PaymentTerms { get; set; }
            public byte PartyType { get; set; }
            public string Party_Mobile { get; set; }
            public int ModifiedBY { get; set; }
            public System.DateTime ModifiedDate { get; set; }
            public Nullable<int> Country_ID { get; set; }
            public Nullable<int> State_ID { get; set; }
            public Nullable<int> Company_ID { get; set; }
            public Nullable<bool> IsOEM { get; set; }
            public Nullable<bool> IsDealer { get; set; }
            public Nullable<int> Relationship_Branch_ID { get; set; }
            public Nullable<int> Relationship_Company_ID { get; set; }
            public Nullable<decimal> PartsCreditLimit { get; set; }
            public Nullable<decimal> SalesCreditLimit { get; set; }
            public Nullable<int> Currency_ID { get; set; }
            public bool IsImportExport { get; set; }
            public Nullable<decimal> ServiceCreditLimit { get; set; }
            public Nullable<int> PaymentDueDays { get; set; }
            public Nullable<decimal> PartsOutStandingCredit { get; set; }
            public Nullable<decimal> ServiceOutStandingCredit { get; set; }
            public Nullable<decimal> PartyAdvanceAmount { get; set; }
            public Nullable<decimal> RemanOutStandingCredit { get; set; }
            public Nullable<decimal> SalesOutStandingCredit { get; set; }
            public Nullable<int> CustomerType_ID { get; set; }
            public Nullable<bool> IsKeyCustomer { get; set; }
            public Nullable<int> Region_ID { get; set; }
            public string Party_Code { get; set; }
            public Nullable<bool> SupplierHasInterface { get; set; }
            public Nullable<byte> CustomerType { get; set; }
            public Nullable<int> CustomerLanguageID { get; set; }
            public Nullable<decimal> ServiceCreditLimitinUSD { get; set; }
            public Nullable<decimal> ServiceOutStandingCreditinUSD { get; set; }
            public Nullable<bool> IsPONumberMandatory { get; set; }
            public Nullable<decimal> CAD_ExchangeRate { get; set; }
            public Nullable<decimal> US_ExchangeRate { get; set; }
            public Nullable<bool> CreditExceededMailSent { get; set; }
            public Nullable<bool> IsInternalCustomer { get; set; }
            public Nullable<decimal> Variance_Percentage { get; set; }
            public Nullable<decimal> Variance_Value { get; set; }

            public virtual ICollection<GNM_PartyBranchAssociation> GNM_PartyBranchAssociation { get; set; }
            public virtual ICollection<GNM_PartyProductAssociation> GNM_PartyProductAssociation { get; set; }
            public virtual ICollection<GNM_PartySkillset> GNM_PartySkillset { get; set; }
            public virtual ICollection<GNM_ServiceSchedule> GNM_ServiceSchedule { get; set; }
            public virtual ICollection<GNM_PartyLocale> GNM_PartyLocale { get; set; }
            public virtual ICollection<GNM_PartySegmentDetails> GNM_PartySegmentDetails { get; set; }
            public virtual ICollection<GNM_PartyTaxDetails> GNM_PartyTaxDetails { get; set; }
            public virtual ICollection<GNM_PartyContactPersonDetails> GNM_PartyContactPersonDetails { get; set; }
            public virtual ICollection<GNM_PartyAddress> GNM_PartyAddress { get; set; }
            public virtual ICollection<GNM_PARTYCONTRACTDETAILS> GNM_PARTYCONTRACTDETAILS { get; set; }
            public virtual ICollection<GNM_PartyTaxStructure> GNM_PartyTaxStructure { get; set; }
            public virtual ICollection<GNM_PartyDiscount> GNM_PartyDiscount { get; set; }
            public virtual ICollection<GNM_PartyPartsRatecontract> GNM_PartyPartsRatecontract { get; set; }
            public virtual ICollection<GNM_PartyCreditLimitLog> GNM_PartyCreditLimitLog { get; set; }
            public virtual ICollection<GNM_PartyAmount> GNM_PartyAmount { get; set; }
            public virtual ICollection<GNM_CreditDetails> GNM_CreditDetails { get; set; }
        }

        public partial class GNM_ServiceTypeLocale
        {
            public int ServiceTypeLocale_ID { get; set; }
            public int ServiceType_ID { get; set; }
            public string ServiceType_Name { get; set; }
            public int Language_ID { get; set; }

            public virtual GNM_ServiceType GNM_ServiceType { get; set; }
        }
        public partial class GNM_ServiceTypeOperationDetail
        {
            public int ServiceTypeOperationDetail_ID { get; set; }
            public int ServiceType_ID { get; set; }
            public int Operation_ID { get; set; }

            public virtual GNM_ServiceType GNM_ServiceType { get; set; }
            public virtual CoreSRM_Operation SRM_Operation { get; set; }
        }
        public partial class CoreSRM_OperationBranchDetail
        {
            public int OperationBranchDetail_ID { get; set; }
            public int Operation_ID { get; set; }
            public int Branch_ID { get; set; }

            public virtual CoreSRM_Operation SRM_Operation { get; set; }
        }
        public partial class CoreSRM_OperationCheckListLocaleDetail
        {
            public int OperationCheckListLocaleDetail_ID { get; set; }
            public int OperationCheckListDetail_ID { get; set; }
            public string CheckListLocaleDescription { get; set; }
            public int Language_ID { get; set; }

            public virtual CoreSRM_OperationCheckListDetail SRM_OperationCheckListDetail { get; set; }
        }
        public partial class CoreSRM_OperationProductDetail
        {
            public int OperationProductDetail_ID { get; set; }
            public int Operation_ID { get; set; }
            public int Brand_ID { get; set; }
            public Nullable<int> ProductType_ID { get; set; }
            public Nullable<int> Model_ID { get; set; }

            public virtual CoreSRM_Operation SRM_Operation { get; set; }
        }
        public partial class CoreSRM_OperationLocale
        {
            public int OperationLocale_ID { get; set; }
            public int Operation_ID { get; set; }
            public string Operation_Description { get; set; }
            public int Language_ID { get; set; }

            public virtual CoreSRM_Operation SRM_Operation { get; set; }
        }
        public partial class CoreSRM_OperationCheckListDetail
        {
            public CoreSRM_OperationCheckListDetail()
            {
                this.SRM_OperationCheckListLocaleDetail = new HashSet<CoreSRM_OperationCheckListLocaleDetail>();
            }

            public int OperationCheckListDetail_ID { get; set; }
            public int Operation_ID { get; set; }
            public string CheckListDescription { get; set; }
            public Nullable<bool> IsMandatory { get; set; }
            public Nullable<bool> IsSpecialTools { get; set; }
            public Nullable<bool> IsSafetyMeasures { get; set; }

            public virtual CoreSRM_Operation SRM_Operation { get; set; }
            public virtual ICollection<CoreSRM_OperationCheckListLocaleDetail> SRM_OperationCheckListLocaleDetail { get; set; }
        }
        public partial class CoreSRM_Operation
        {
            public CoreSRM_Operation()
            {
                this.GNM_ServiceTypeOperationDetail = new HashSet<GNM_ServiceTypeOperationDetail>();
                this.SRM_OperationBranchDetail = new HashSet<CoreSRM_OperationBranchDetail>();
                this.SRM_OperationCheckListDetail = new HashSet<CoreSRM_OperationCheckListDetail>();
                this.SRM_OperationLocale = new HashSet<CoreSRM_OperationLocale>();
                this.SRM_OperationProductDetail = new HashSet<CoreSRM_OperationProductDetail>();
            }

            public int Operation_ID { get; set; }
            public int Company_ID { get; set; }
            public string Operation_Description { get; set; }
            public string Operation_Code { get; set; }
            public Nullable<int> FunctionGroup_ID { get; set; }
            public Nullable<int> Skill_ID { get; set; }
            public Nullable<byte> Operation_SkillLevel { get; set; }
            public Nullable<decimal> Operation_StandardTime { get; set; }
            public Nullable<decimal> Operation_Time { get; set; }
            public bool Operation_IsActive { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }

            public virtual ICollection<GNM_ServiceTypeOperationDetail> GNM_ServiceTypeOperationDetail { get; set; }
            public virtual ICollection<CoreSRM_OperationBranchDetail> SRM_OperationBranchDetail { get; set; }
            public virtual ICollection<CoreSRM_OperationCheckListDetail> SRM_OperationCheckListDetail { get; set; }
            public virtual ICollection<CoreSRM_OperationLocale> SRM_OperationLocale { get; set; }
            public virtual ICollection<CoreSRM_OperationProductDetail> SRM_OperationProductDetail { get; set; }
        }
        public partial class GNM_ServiceType
        {
            public GNM_ServiceType()
            {
                this.GNM_ServiceTypeLocale = new HashSet<GNM_ServiceTypeLocale>();
                this.GNM_ServiceTypeOperationDetail = new HashSet<GNM_ServiceTypeOperationDetail>();
            }

            public int ServiceType_ID { get; set; }
            public int Company_ID { get; set; }
            public string ServiceType_Name { get; set; }
            public bool ServiceType_Active { get; set; }
            public Nullable<bool> IsMandatoryService { get; set; }
            public Nullable<int> ServiceDueHours { get; set; }
            public Nullable<int> ServiceDueDays { get; set; }
            public Nullable<bool> IsWarrantyClaimable { get; set; }
            public Nullable<bool> IsDemandDrive { get; set; }
            public Nullable<bool> IsInsuranceJob { get; set; }
            public string ServiceType_Code { get; set; }
            public Nullable<byte> ServiceType_Priority { get; set; }
            public Nullable<bool> IsCommissioning { get; set; }
            public Nullable<bool> IsNotificationEligible { get; set; }
            public Nullable<bool> ChargeToTypeInvoice { get; set; }
            public Nullable<bool> ChargeToTypeInternalInvoice { get; set; }
            public Nullable<bool> ChargeToTypeWarranty { get; set; }
            public Nullable<bool> IsUnderStandardWarranty { get; set; }
            public Nullable<bool> IsUnderExtendedWarranty { get; set; }
            public Nullable<bool> IsNotUnderStandardandExtendedWarranty { get; set; }
            public Nullable<bool> IsInvoice { get; set; }
            public Nullable<bool> IsInternalInvoice { get; set; }
            public string PrevostNotifT { get; set; }
            public string NovaNotifT { get; set; }
            public string SystemCondition { get; set; }
            public Nullable<bool> IsStandardText { get; set; }

            public virtual ICollection<GNM_ServiceTypeLocale> GNM_ServiceTypeLocale { get; set; }
            public virtual ICollection<GNM_ServiceTypeOperationDetail> GNM_ServiceTypeOperationDetail { get; set; }
        }

        public partial class GNM_RefMasterDetailLocale
        {
            public int RefMasterDetailLocale_ID { get; set; }
            public int RefMasterDetail_ID { get; set; }
            public int RefMaster_ID { get; set; }
            public string RefMasterDetail_Short_Name { get; set; }
            public string RefMasterDetail_Name { get; set; }
            public int Language_ID { get; set; }

            public virtual GNM_RefMaster GNM_RefMaster { get; set; }
            public virtual GNM_RefMasterDetail GNM_RefMasterDetail { get; set; }
        }

        public partial class GNM_RefMaster
        {
            public GNM_RefMaster()
            {
                this.GNM_RefMasterDetail = new HashSet<GNM_RefMasterDetail>();
                this.GNM_RefMasterDetailLocale = new HashSet<GNM_RefMasterDetailLocale>();
            }

            public int RefMaster_ID { get; set; }
            public string RefMaster_Name { get; set; }
            public Nullable<bool> IsCompanySpecific { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }
            public bool IsSystemMaster { get; set; }

            public virtual ICollection<GNM_RefMasterDetail> GNM_RefMasterDetail { get; set; }
            public virtual ICollection<GNM_RefMasterDetailLocale> GNM_RefMasterDetailLocale { get; set; }
        }
        public partial class GNM_RefMasterDetail
        {
            public GNM_RefMasterDetail()
            {
                this.GNM_RefMasterDetailLocale = new HashSet<GNM_RefMasterDetailLocale>();
            }

            public int RefMasterDetail_ID { get; set; }
            public bool RefMasterDetail_IsActive { get; set; }
            public string RefMasterDetail_Short_Name { get; set; }
            public string RefMasterDetail_Name { get; set; }
            public Nullable<int> Company_ID { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }
            public int RefMaster_ID { get; set; }
            public bool RefMasterDetail_IsDefault { get; set; }
            public Nullable<int> Region_ID { get; set; }
            public string SystemCondition { get; set; }
            public bool IsCompanySpecific { get; set; }

            public virtual GNM_RefMaster GNM_RefMaster { get; set; }
            public virtual ICollection<GNM_RefMasterDetailLocale> GNM_RefMasterDetailLocale { get; set; }
        }

        public partial class GNM_SecondarySegmentLocale
        {
            public int SecondarySegmentLocale_ID { get; set; }
            public int SecondarySegment_ID { get; set; }
            public string SecondarySegment_Description { get; set; }
            public int Language_ID { get; set; }

            public virtual GNM_SecondarySegment GNM_SecondarySegment { get; set; }
        }
        public partial class GNM_SecondarySegment
        {
            public GNM_SecondarySegment()
            {
                this.GNM_SecondarySegmentLocale = new HashSet<GNM_SecondarySegmentLocale>();
            }

            public int SecondarySegment_ID { get; set; }
            public int PrimarySegment_ID { get; set; }
            public bool SecondarySegment_IsActive { get; set; }
            public string SecondarySegment_Description { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }

            public virtual ICollection<GNM_SecondarySegmentLocale> GNM_SecondarySegmentLocale { get; set; }
        }

        public partial class GNM_ChangeVehicleOwnershipLog
        {
            public int ChangeVehicleOwnership_ID { get; set; }
            public Nullable<int> Product_ID { get; set; }
            public Nullable<int> PreviousParty_ID { get; set; }
            public Nullable<int> CurrentParty_ID { get; set; }
            public Nullable<int> UpdatedBy_ID { get; set; }
            public Nullable<System.DateTime> UpdatedDate { get; set; }

            public virtual GNM_Product GNM_Product { get; set; }
        }

        public partial class GNM_ProductCSA
        {
            public int ProductCSA_ID { get; set; }
            public int Product_ID { get; set; }
            public int Company_ID { get; set; }
            public string CSANumber { get; set; }
            public Nullable<System.DateTime> CSAFromDate { get; set; }
            public Nullable<System.DateTime> CSAToDate { get; set; }
            public Nullable<int> CSAType { get; set; }
            public Nullable<decimal> CSAValue { get; set; }
            public string Remarks { get; set; }
            public Nullable<bool> IsActive { get; set; }
            public Nullable<int> ServiceType_ID { get; set; }
            public bool ScheduleType { get; set; }
            public Nullable<System.DateTime> RecurringStartDate { get; set; }
            public Nullable<int> RepeatEveryDays { get; set; }

            public virtual GNM_Product GNM_Product { get; set; }
            public virtual GNM_Product GNM_Product1 { get; set; }
        }

        public partial class GNM_ProductComponent
        {
            public GNM_ProductComponent()
            {
                this.GNM_ProductReading = new HashSet<GNM_ProductReading>();
            }

            public int ProductComponent_ID { get; set; }
            public int Product_ID { get; set; }
            public int Parts_ID { get; set; }
            public Nullable<int> Model_ID { get; set; }
            public string ProductComponent_SerialNumber { get; set; }
            public Nullable<int> Brand_ID { get; set; }
            public Nullable<int> ProductType_ID { get; set; }
            public int Company_ID { get; set; }
            public Nullable<int> Reading { get; set; }
            public Nullable<bool> IsActive { get; set; }
            public string Remarks { get; set; }
            public Nullable<int> ExpectedLife { get; set; }
            public Nullable<int> ReManCycle { get; set; }
            public string UsageArea { get; set; }

            public virtual GNM_Product GNM_Product { get; set; }
            public virtual ICollection<GNM_ProductReading> GNM_ProductReading { get; set; }
        }
        public partial class GNM_ProductComponentWarrantyLocale
        {
            public int ProductComponentWarrantyLocale_ID { get; set; }
            public int ProductComponentWarranty_ID { get; set; }
            public int Language_ID { get; set; }
            public string Remarks { get; set; }

            public virtual GNM_ProductComponentWarranty GNM_ProductComponentWarranty { get; set; }
        }
        public partial class GNM_ProductComponentWarranty
        {
            public GNM_ProductComponentWarranty()
            {
                this.GNM_ProductComponentWarrantyLocale = new HashSet<GNM_ProductComponentWarrantyLocale>();
            }

            public int ProductComponentWarranty_ID { get; set; }
            public int Product_ID { get; set; }
            public Nullable<int> WarrantyType_ID { get; set; }
            public Nullable<int> Brand_ID { get; set; }
            public Nullable<int> ProductType_ID { get; set; }
            public Nullable<int> Model_ID { get; set; }
            public string SerialNumber { get; set; }
            public Nullable<int> Parts_ID { get; set; }
            public string Narration { get; set; }
            public Nullable<System.DateTime> StartDate { get; set; }
            public Nullable<System.DateTime> EndDate { get; set; }
            public Nullable<int> OdometerLimit { get; set; }
            public string Remarks { get; set; }
            public Nullable<byte> AttachmentCount { get; set; }
            public Nullable<int> WarrantyIdentifier_ID { get; set; }
            public Nullable<int> OdometerLimitInMiles { get; set; }
            public Nullable<int> OdometerLimitInKM { get; set; }

            public virtual ICollection<GNM_ProductComponentWarrantyLocale> GNM_ProductComponentWarrantyLocale { get; set; }
            public virtual GNM_Product GNM_Product { get; set; }
        }
        public partial class GNM_ProductContractor
        {
            public int ProductContractor_ID { get; set; }
            public int Product_ID { get; set; }
            public int Customer_ID { get; set; }
            public int Contractor_ID { get; set; }
            public System.DateTime ProductContractor_FromDate { get; set; }
            public Nullable<System.DateTime> ProductContractor_ToDate { get; set; }
            public Nullable<int> Company_ID { get; set; }
            public Nullable<int> Branch_ID { get; set; }
            public Nullable<int> ContractType { get; set; }
            public Nullable<int> Product_ContractType { get; set; }
            public Nullable<int> AttachmentCount { get; set; }

            public virtual GNM_Product GNM_Product { get; set; }
        }
        public partial class GNM_CSASCHEDULEOPERATION
        {
            public int CSASCHEDULEOPERATION_ID { get; set; }
            public int ProductCSAServiceSchedule_ID { get; set; }
            public int Operation_ID { get; set; }

            public virtual GNM_ProductCSAServiceSchedule GNM_ProductCSAServiceSchedule { get; set; }
        }

        public partial class GNM_ProductCSAServiceSchedule
        {
            public GNM_ProductCSAServiceSchedule()
            {
                this.GNM_CSASCHEDULEOPERATION = new HashSet<GNM_CSASCHEDULEOPERATION>();
            }

            public int ProductCSAServiceSchedule_ID { get; set; }
            public int Product_ID { get; set; }
            public int Company_ID { get; set; }
            public Nullable<System.DateTime> ServiceDueDate { get; set; }
            public Nullable<byte> ServiceSchedule_Status { get; set; }
            public Nullable<int> ProductCSA_ID { get; set; }
            public Nullable<int> JobCard_ID { get; set; }
            public Nullable<int> Warranty_ID { get; set; }

            public virtual ICollection<GNM_CSASCHEDULEOPERATION> GNM_CSASCHEDULEOPERATION { get; set; }
            public virtual GNM_Product GNM_Product { get; set; }
        }
        public partial class GNM_ProductCustomer
        {
            public int ProductCustomer_ID { get; set; }
            public int Product_ID { get; set; }
            public int Party_ID { get; set; }
            public System.DateTime ProductCustomer_FromDate { get; set; }
            public Nullable<System.DateTime> ProductCustomer_ToDate { get; set; }
            public Nullable<int> Company_ID { get; set; }
            public Nullable<bool> IsTransacted { get; set; }

            public virtual GNM_Product GNM_Product { get; set; }
        }
        public partial class GNM_ProductInvoiceDetail
        {
            public int ProductInvoice_ID { get; set; }
            public int Product_ID { get; set; }
            public int Company_ID { get; set; }
            public string InvoiceNumber { get; set; }
            public Nullable<System.DateTime> InvoiceDate { get; set; }
            public Nullable<decimal> LandingCost { get; set; }
            public Nullable<int> WarrantyPeriod { get; set; }
            public Nullable<int> HMR { get; set; }
            public Nullable<int> InvoiceID { get; set; }

            public virtual GNM_Product GNM_Product { get; set; }
        }
        public partial class GNM_ProductStatusHistory
        {
            public int ProductServiceHistory_ID { get; set; }
            public int Product_ID { get; set; }
            public byte Mode { get; set; }
            public int Company_ID { get; set; }
            public System.DateTime ProductStatus_Date { get; set; }
            public string MobileNumber { get; set; }
            public int MachineStatus_ID { get; set; }

            public virtual GNM_Product GNM_Product { get; set; }
        }
        public partial class GNM_ProductWarranty
        {
            public GNM_ProductWarranty()
            {
                this.GNM_ProductWarrantyLocale = new HashSet<GNM_ProductWarrantyLocale>();
            }

            public int ProductWarranty_ID { get; set; }
            public int Product_ID { get; set; }
            public string ProductWarranty_Description { get; set; }
            public System.DateTime ProductWarranty_IssueDate { get; set; }
            public Nullable<System.DateTime> ProductWarranty_FromDate { get; set; }
            public Nullable<System.DateTime> ProductWarranty_ToDate { get; set; }
            public Nullable<int> ProductWarranty_ReadingLimit { get; set; }
            public Nullable<int> WarrantyType_ID { get; set; }
            public Nullable<int> Company_ID { get; set; }
            public Nullable<int> ServiceType_ID { get; set; }
            public bool ScheduleType { get; set; }
            public Nullable<System.DateTime> RecurringStartDate { get; set; }
            public Nullable<int> RepeatEveryDays { get; set; }
            public Nullable<bool> IsAutometicWarranty { get; set; }
            public Nullable<int> WarrantyIdentifier_ID { get; set; }
            public Nullable<int> OdometerLimitInMiles { get; set; }
            public Nullable<int> OdometerLimitInKM { get; set; }

            public virtual ICollection<GNM_ProductWarrantyLocale> GNM_ProductWarrantyLocale { get; set; }
            public virtual GNM_Product GNM_Product { get; set; }
        }
        public partial class GNM_ProductWarrantyLocale
        {
            public int ProductWarrantyLocale_ID { get; set; }
            public int ProductWarranty_ID { get; set; }
            public int Language_ID { get; set; }
            public string Description { get; set; }

            public virtual GNM_ProductWarranty GNM_ProductWarranty { get; set; }
        }

        public class SelectMachineUpdateList
        {
            public int Company_ID { get; set; }
            public int LanguageID { get; set; }
            public string Company_Name { get; set; }
        }
        public class Machine_ProductMaster
        {
            public class Rule
            {
                public string field { get; set; }
                public string op { get; set; }  // This is the operation (eq, ne, lt, etc.)
                public string data { get; set; }
            }

            public int Product_ID
            {
                get;
                set;
            }

            public string Product_UniqueNo
            {
                get;
                set;
            }
            public string edit
            {
                get;
                set;
            }

            public string delete
            {
                get;
                set;
            }

            public string Party_Name
            {
                get;
                set;
            }
            public string BrandName
            {
                get;
                set;
            }
            public string ProductType_Name
            {
                get;
                set;
            }
            public string Model_Name
            {
                get;
                set;
            }
            public string Product_SerialNumber
            {
                get;
                set;
            }
            public string RefMasterDetail_Name
            {
                get;
                set;
            }
            //public string machinestatus//triveni
            //{
            //    get;
            //    set;
            //}
            public int Company_ID
            {
                get;
                set;
            }
            public string Company_Name
            {
                get;
                set;
            }

            //triveni
            public bool? IsComponent
            {
                get;
                set;
            }
            public int? Warehouse_ID
            {
                get;
                set;
            }
            public string WareHouse
            {
                get;
                set;
            }
            public string Product_EngineSerialNumber
            {
                get;
                set;
            }
            public int? PrimarySegment_ID
            {
                get;
                set;
            }
            public int? SecondarySegment_ID
            {
                get;
                set;
            }
            public string PrimarySegmnet
            {
                get;
                set;
            }
            public string SecondarySegment
            {
                get;
                set;
            }
            public int CurrentReadingIC
            {
                get;
                set;
            }
            public int CurrentReading
            {
                get;
                set;
            }
            public string CurrentSiteAddress
            {
                get;
                set;
            }
            public string NextServiceTypestring
            {
                get;
                set;
            }
            public string OnDatestring
            {
                get;
                set;
            }
            public DateTime? NextServiceDate
            {
                get;
                set;
            }
            public string Prefix
            {
                get;
                set;
            }
            public string PartNum
            {
                get;
                set;
            }
            public DateTime? DateOfManufacture
            {
                get;
                set;
            }
            public string DateOFManufacturestring
            {
                get;
                set;
            }
            public string DateofSalestring
            {
                get;
                set;
            }

            public string DateOfInstallationstring
            {
                get;
                set;
            }
            public int? ServiceCompany
            {
                get;
                set;
            }
            public string ServiceCompanystring
            {
                get;
                set;
            }
            public int? NextServiceType_ID
            {
                get;
                set;
            }
            public int? Parts_ID
            {
                get;
                set;
            }
            public bool? IsActive { get; set; }
            ////added by kavitha -writgen changes-
            public int? MachineStatus_ID { get; set; }
            public string MachineStatus { get; set; }
            public int Brand_ID { get; set; }
            public int ProductType_ID { get; set; }
            public int Model_ID { get; set; }
            //public int? row_number { get; set; }
            public DateTime? LastHMRUpdatedDate { get; set; }
            public string LastHMRUpdatedDateSort { get; set; }
            public DateTime? LastServiceDate { get; set; }
            public string LastServiceDateSort { get; set; }
            //added by kavitha -writgen changes-
            public string Warranty { get; set; }
            public string WarrantyAggreement { get; set; }
            public string MachineLocation { get; set; }
            //added by kavitha -writgen changes-
            public string Product_IsActive
            {
                get;
                set;
            }
            //end
            public int? MachineHMR
            {
                get;
                set;
            }
            public int? Reading
            {
                get;
                set;
            }

            public string SiteAddress
            {
                get;
                set;
            }
            public string ProductSiteAddress_SiteAddress
            {
                get;
                set;
            }
            public string ProductSiteAddress_Location
            {
                get;
                set;
            }
            //end
            //end
        }
        public class GETCompanyParamList
        {

        }
    }
}
