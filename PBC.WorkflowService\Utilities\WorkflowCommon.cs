using System;
using System.Data;
using System.Data.SqlClient;
using PBC.WorkflowService.Models;

namespace PBC.WorkflowService.Utilities
{
    public class WorkflowCommon
    {
        #region CheckPreffixSuffix
        public static bool CheckPreffixSuffix(int companyID, int branchID, string objectName, string dbName, string constring)
        {
            bool flag = false;
            int objectID = GetObjectID(objectName, dbName, constring);

            try
            {
                // Construct the connection string with the provided database name (dbName)
                SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(constring)
                {
                    InitialCatalog = dbName
                };

                using (SqlConnection conn = new SqlConnection(builder.ToString()))
                {
                    conn.Open();

                    // Define the current date to use in both queries
                    DateTime currentDate = DateTime.Now;

                    // First Query: Check PrefixSuffix with specific Branch ID
                    string query1 = @"SELECT COUNT(1) 
                              FROM WF_PrefixSuffix 
                              WHERE Branch_ID = @BranchID 
                                AND Object_ID = @ObjectID 
                                AND FromDate <= @CurrentDate 
                                AND ToDate >= @CurrentDate";

                    using (SqlCommand cmd = new SqlCommand(query1, conn))
                    {
                        cmd.Parameters.AddWithValue("@BranchID", branchID);
                        cmd.Parameters.AddWithValue("@ObjectID", objectID);
                        cmd.Parameters.AddWithValue("@CurrentDate", currentDate);

                        int count = Convert.ToInt32(cmd.ExecuteScalar());
                        flag = count > 0;
                    }

                    // Second Query: Check PrefixSuffix with null Branch ID if the first check fails
                    if (!flag)
                    {
                        string query2 = @"SELECT COUNT(1) 
                                  FROM WF_PrefixSuffix 
                                  WHERE Company_ID = @CompanyID 
                                    AND Branch_ID IS NULL 
                                    AND Object_ID = @ObjectID 
                                    AND FromDate <= @CurrentDate 
                                    AND ToDate >= @CurrentDate";

                        using (SqlCommand cmd = new SqlCommand(query2, conn))
                        {
                            cmd.Parameters.AddWithValue("@CompanyID", companyID);
                            cmd.Parameters.AddWithValue("@ObjectID", objectID);
                            cmd.Parameters.AddWithValue("@CurrentDate", currentDate);

                            int count = Convert.ToInt32(cmd.ExecuteScalar());
                            flag = count > 0;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Use LogSheetExporter for error logging in static utility classes
                LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return flag;
        }
        public static int GetObjectID(string name, string dbName, string constring)
        {
            int result = 0;

            try
            {

                SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(constring)
                {
                    InitialCatalog = dbName
                };

                using (SqlConnection conn = new SqlConnection(builder.ToString()))
                {
                    conn.Open();

                    // Query to get the Object_ID based on Object_Name
                    string query = @"SELECT TOP 1 Object_ID 
                             FROM WF_Object 
                             WHERE UPPER(Object_Name) = UPPER(@ObjectName)";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@ObjectName", name);

                        // Execute the query and retrieve the result
                        object obj = cmd.ExecuteScalar();
                        if (obj != null && obj != DBNull.Value)
                        {
                            result = Convert.ToInt32(obj);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Use LogSheetExporter for error logging in static utility classes
                LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
            }

            return result;
        }
        #endregion
    }
}