﻿using Microsoft.IdentityModel.Tokens;
using SharedAPIClassLibrary_AMERP.Services;
using System;
using System.Configuration;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Web.Http;
using System.Web.Http.Cors;


namespace HCLSoftware_DPC_API_Standalone.Controllers
{

    [EnableCors(origins: "*", headers: "*", methods: "*")]
    public class JWTController : ApiController
    {
        private static readonly string SecretKey = Convert.ToString(ConfigurationManager.AppSettings.Get("DCAPIKEY")); // Store this securely
        private static readonly string Issuer = Convert.ToString(ConfigurationManager.AppSettings.Get("DCIssuerKEY")); // Replace with your API's URL
        private static readonly string Audience = Convert.ToString(ConfigurationManager.AppSettings.Get("DCParty")); // Customize as needed
        private static readonly int TokenExpiryMinutes = Convert.ToInt32(ConfigurationManager.AppSettings.Get("DCAPITimeOut")); // Token expiration time

        private static readonly string UserName = Convert.ToString(ConfigurationManager.AppSettings.Get("UserName")); // Store this securely
        private static readonly string Password = Convert.ToString(ConfigurationManager.AppSettings.Get("Password")); // Replace with your API's URL

        string Culturevalue = string.Empty;
        public static string isPreviewAuthorValue = string.Empty;

        #region JWT token Gen
        /// <summary>
        ///  To generate JWT TOKEN - DK - use for Internal
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [System.Web.Http.HttpPost]
        [Route("api/JWT/JWTGenOnLogin")]
        [AllowAnonymous]
        public IHttpActionResult JWTGenOnLogin([FromBody] SharedAPIClassLibrary_DC.UserLoginModel model)
        {
            var securityKey = new SymmetricSecurityKey(Convert.FromBase64String(SecretKey));
            var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);
            var header = new JwtHeader(credentials);
            int TokenExpiryMinutes = Convert.ToInt32(ConfigurationManager.AppSettings.Get("DCAPITimeOut")); //Token expiration time
                                                                                                            // Create the token payload (claims)
            var claims = new[]
            {
                    new Claim("exp", DateTimeOffset.Now.AddMinutes(60).ToUnixTimeSeconds().ToString()),
                    new Claim("iss", Issuer),
                    new Claim("aud", Audience),
                };
            var payload = new JwtPayload(claims);

            var jwtSecurityToken = new JwtSecurityToken(header, payload);
            var tokenString = "";
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                tokenString = tokenHandler.WriteToken(jwtSecurityToken);
            }
            catch (Exception ex)
            {

            }
            return Ok(tokenString);
        }
        #endregion

        /// <summary>
        /// DK - 09-FEB-2024 - NEW Method Uss this for all External - SAP , or any other Systems
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("api/JWT/JWTGenOnLoginAMERP")]
        [AllowAnonymous] // DK - Remove this once authentication is fully implemented
        public IHttpActionResult JWTGenOnLoginAMERP([FromBody] SharedAPIClassLibrary_DC.UserLoginModel model)
        {
            string username = null;
            string password = null;

            // 1️⃣ Check if credentials are sent via Basic Authentication
            var authHeader = Request.Headers.Authorization;
            if (authHeader != null && authHeader.Scheme.Equals("Basic", StringComparison.OrdinalIgnoreCase))
            {
                try
                {
                    var credentialBytes = Convert.FromBase64String(authHeader.Parameter);
                    var credentials = System.Text.Encoding.UTF8.GetString(credentialBytes).Split(':');

                    if (credentials.Length == 2)
                    {
                        username = credentials[0];
                        password = credentials[1];
                    }
                }
                catch
                {
                    return BadRequest("Invalid Authorization header format.");
                }
            }
            // 2️ If not using Basic Auth, get credentials from request body
            else if (model != null)
            {
                username = model.Username;
                password = model.Password;
            }

            // 3️ Validate credentials
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            {
                return BadRequest("Invalid login details.");
            }

            if (!AuthenticateUser(username, password))
            {
                return Unauthorized();
            }

            try
            {
                // 4️⃣ Generate JWT token
                var token = JwtHelper.GenerateJwtToken(username);
                return Ok(new { Token = token });
            }
            catch (Exception ex)
            {
                return InternalServerError(new Exception("Error generating JWT token", ex));
            }
        }


        /// <summary>
        ///  authentication method (need to do DB validation)
        /// </summary>
        private bool AuthenticateUser(string username, string password)
        {
            // Replace with actual database validation (hashed passwords)
            return (username == UserName && password == Password); 
        }
    }
}