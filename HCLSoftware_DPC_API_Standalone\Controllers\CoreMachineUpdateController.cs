﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreMachineUpdateServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreMachineUpdateController : ApiController
    {

        #region::: GETCompanyParam /Mithun:::
        /// <summary>
        /// GETCompanyParam
        /// </summary>
        /// 
        [Route("api/CoreMachineUpdate/GETCompanyParam")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GETCompanyParam([FromBody] GETCompanyParamList GETCompanyParamObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreMachineUpdateServices.GETCompanyParam(GETCompanyParamObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select /Mithun:::
        /// <summary>
        /// To select All product
        /// </summary>
        [Route("api/CoreMachineUpdate/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] MachineUpdateSelectList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                // Call service to get response
                Response = CoreMachineUpdateServices.Select(Obj, sidx, rows, page, sord, _search, nd, filters, advnce, Query, Conn, LogException);

                // Ensure Response.Value is cast as an object for compatibility with Ok()
                return Ok(Response.Value);
            }
            catch (Exception ex)
            {
                // Log the exception if enabled
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);
            }
        }
        #endregion

        #region ::: CheckMachineUpdateReadingDetails /Mithun:::
        /// <summary>
        /// To Check if Service History is Done
        /// </summary>
        /// 
        [Route("api/CoreMachineUpdate/CheckMachineUpdateReadingDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckMachineUpdateReadingDetails([FromBody] CheckMachineUpdateReadingDetailsList CheckMachineUpdateReadingDetailsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreMachineUpdateServices.CheckMachineUpdateReadingDetails(CheckMachineUpdateReadingDetailsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: MachineUpdate /Mithun:::
        /// <summary>
        /// To Machine Update
        /// </summary>
        /// 
        [Route("api/CoreMachineUpdate/MachineUpdate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult MachineUpdate([FromBody] MachineUpdateList MachineUpdateObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreMachineUpdateServices.MachineUpdate(MachineUpdateObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region::: SaveAttachment /Mithun:::
        /// <summary>
        /// SaveAttachment
        /// </summary>
        /// <returns></returns>
        /// 
        [Route("api/CoreMachineUpdate/SaveAttachment")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveAttachment([FromBody] SaveAttachmentList SaveAttachmentObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreMachineUpdateServices.SaveAttachment(SaveAttachmentObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

    }
}
