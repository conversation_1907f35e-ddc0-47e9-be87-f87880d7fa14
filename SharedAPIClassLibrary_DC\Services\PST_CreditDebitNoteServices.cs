﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.CoreTermsAndConditionsServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace SharedAPIClassLibrary_AMERP.Services
{
    public class PST_CreditDebitNoteServices
    {
        public static string AppPath = "";
        public static string IsDealerToDealerSupply = "F";
        #region ::: Select Field Search For Party Vinay N 20/1/25:::
        /// <summary>
        /// SelectFieldSearchParty
        /// </summary>
        /// <param name="sidx"></param>
        /// <param name="sord"></param>
        /// <param name="page"></param>
        /// <param name="rows"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public static IActionResult SelectFieldSearchParty(SelectFieldSearchPartyList obj, string connString, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters)
        {
            bool FilterPartyBasedonCompany = true;
            var value = obj.value;
            value = Common.DecryptString(value);
            value = value.Trim();

            IQueryable<GNM_Party> Party = null;
            IQueryable<GNM_PartyLocale> PartyLocale = null;
            IQueryable<FieldSearch> flSrch = null;
            int Count = 0;
            int Total = 0;
            int Company_ID = Convert.ToInt32(obj.Company_ID);
            int LangID = Convert.ToInt32(obj.LanguageID);
            int userLanguageID = Convert.ToInt32(obj.UserLanguageID);
            int generalLanguageID = Convert.ToInt32(obj.GeneralLanguageID);

            int PartyType = 1;
            if (Convert.ToBoolean(obj.CreditNote) == false)
            {
                PartyType = 4;
            }
            //FilterToolBar Search
            if (_search == true)
            {
                string decodedValue = Uri.UnescapeDataString(filters);
                Filters filtersObj = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<Filters>();
                if (filtersObj.rules.Count > 0)
                {
                    //flSrch = flSrch.FilterSearch<FieldSearch>(filters);
                    value = Common.DecryptString(filtersObj.rules.ElementAt(0).data);
                }
                else { value = ""; }

            }



            if (generalLanguageID == userLanguageID)
            {
                List<SqlParameter> sqlParameters = new List<SqlParameter>
                {
                    new SqlParameter("@Company_ID", obj.Company_ID),
                    new SqlParameter("@PartyType", PartyType),
                    new SqlParameter("@Value", obj.value ?? (object)DBNull.Value),
                    new SqlParameter("@LangID", LangID),
                    new SqlParameter("@GeneralLanguageID", generalLanguageID),
                    new SqlParameter("@UserLanguageID", userLanguageID)
                };
                List<GNM_Party> partyList = new List<GNM_Party>();
                partyList = Common.GetValueFromDB<List<GNM_Party>>("SP_AMERP_PartsandServices_SelectFieldSearchParty", sqlParameters, connString, LogException, false, 0, true);
                Party = partyList.AsQueryable();
                flSrch = (from q in Party
                          select new FieldSearch()
                          {
                              ID = q.Party_ID,
                              Name = q.Party_Name,
                              Location = q.Party_Location
                          });
            }
            else
            {
                List<GNM_PartyLocale> partyLocaleList = new List<GNM_PartyLocale>();
                List<SqlParameter> sqlParameters = new List<SqlParameter>
                {
                    new SqlParameter("@Company_ID", obj.Company_ID),
                    new SqlParameter("@PartyType", PartyType),
                    new SqlParameter("@Value", obj.value ?? (object)DBNull.Value),
                    new SqlParameter("@LangID",LangID),
                    new SqlParameter("@GeneralLanguageID", generalLanguageID),
                    new SqlParameter("@UserLanguageID", userLanguageID)
                };
                partyLocaleList = Common.GetValueFromDB<List<GNM_PartyLocale>>(" SP_AMERP_PartsandServices_SelectFieldSearchParty", sqlParameters, connString, LogException, false, 0, true);
                PartyLocale = partyLocaleList.AsQueryable();
                flSrch = (from q in PartyLocale
                          select new FieldSearch()
                          {
                              ID = q.Party_ID,
                              Name = q.Party_Name,
                              Location = q.Party_Location
                          });
            }

            flSrch = flSrch.OrderByField<FieldSearch>(sidx, sord);

            Count = flSrch.Count();
            Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;


            var jsonData = new
            {
                total = Total,
                page = page,
                records = Count,
                rows = (from q in flSrch.AsEnumerable()
                        select new
                        {
                            ID = q.ID,
                            Name = q.Name,
                            Location = q.Location,
                            //Select = "<img id='" + q.ID + "' class='FieldSrch' src='" + Common.appPath + "/images/accept.png'></img>"
                            Select = "<a title=" + CommonFunctionalities.GetResourceString(obj.UserCulture.ToString(), "select").ToString() + " href='#' style='font-size: 13px;' id='" + q.ID + "' class='FieldSrch' style='cursor:pointer'><i class='fa fa-check'></i></a>",
                        }
                ).ToList().Paginate(page, rows),
            };


            return new JsonResult(jsonData);

        }
        #endregion
        #region ::: Get Party Dtails  Vinay N 20/1/25:::
        /// <summary>
        /// GetPartyDetails
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult GetPartyDetails(GetPartyDetailsList obj, string connString, int LogException)
        {
            string PartyName = obj.Party_Name;

            PartyName = Common.DecryptString(PartyName);
            var jsonResult = default(dynamic);
            IEnumerable<GNM_Party> Party = null;
            GNM_Party PartyOne = null;
            bool PartyLock = false;
            IEnumerable<GNM_PartyLocale> PartyLocale = null;
            //IEnumerable<GNM_PartyContactPersonDetails> PartyContactDetails = null;
            int PartyID = 0;
            bool Party_IsActive = true;
            int Company_ID = Convert.ToInt32(obj.Company_ID);
            int PartyType = 1;
            if (Convert.ToBoolean(obj.CreditNote) == false)
            {
                PartyType = 4;
            }

            try
            {

                int LangID = obj.Language_ID;
                int userLanguageID = Convert.ToInt32(obj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(obj.GeneralLanguageID);


                if (userLanguageID == generalLanguageID)
                {
                    List<SqlParameter> sqlParameters = new List<SqlParameter>
                    {
                        new SqlParameter("@Company_ID", SqlDbType.Int) { Value = obj.Company_ID },
                        new SqlParameter("@PartyType", SqlDbType.Int) { Value = PartyType },
                        new SqlParameter("@PartyName", SqlDbType.NVarChar) { Value = PartyName ?? (object)DBNull.Value },
                        new SqlParameter("@userLanguageID", SqlDbType.Int) { Value = userLanguageID },
                        new SqlParameter("@generalLanguageID", SqlDbType.Int) { Value = generalLanguageID }
                    };

                    List<GNM_Party> partyList = new List<GNM_Party>();
                    partyList = Common.GetValueFromDB<List<GNM_Party>>(" SP_AMERP_PartsandServices_GetPartyDetails", sqlParameters, connString, LogException, false, 0, true);
                    Party = partyList.AsEnumerable();
                    if (Party.Count() == 1)
                    {
                        foreach (var p in Party)
                        {
                            PartyID = p.Party_ID;
                            PartyLock = Convert.ToBoolean(p.Party_IsLocked);
                            Party_IsActive = p.Party_IsActive;
                        }

                        jsonResult = new
                        {
                            Result = "1",
                            Party_ID = PartyID,
                            Party_IsActive = Party_IsActive,
                            Party_IsLocked = PartyLock,
                            Party_Name = PartyName,
                        };

                    }
                    else if (Party.Count() > 0)
                    {
                        jsonResult = new
                        {
                            Result = 2
                        };
                    }
                    else if (Party.Count() == 0)
                    {
                        jsonResult = new
                        {
                            Result = "0"
                        };

                    }
                }
                else
                {
                    List<SqlParameter> sqlParameters = new List<SqlParameter>
                    {
                        new SqlParameter("@Company_ID", SqlDbType.Int) { Value = obj.Company_ID },
                        new SqlParameter("@PartyType", SqlDbType.Int) { Value = PartyType },
                        new SqlParameter("@PartyName", SqlDbType.NVarChar) { Value = PartyName ?? (object)DBNull.Value },
                        new SqlParameter("@userLanguageID", SqlDbType.Int) { Value = userLanguageID },
                        new SqlParameter("@generalLanguageID", SqlDbType.Int) { Value = generalLanguageID }
                    };

                    List<GNM_PartyLocale> partyList = new List<GNM_PartyLocale>();
                    partyList = Common.GetValueFromDB<List<GNM_PartyLocale>>(" SP_AMERP_PartsandServices_GetPartyDetails", sqlParameters, connString, LogException, true, 1, true);
                    PartyLocale = partyList.AsEnumerable();

                    if (PartyLocale != null)
                    {
                        if (PartyLocale.Count() == 1)
                        {

                            PartyOne = Common.GetValueFromDB<GNM_Party>(" SP_AMERP_PartsandServices_GetPartyDetails", sqlParameters, connString, LogException, false, 0, true);


                            jsonResult = new
                            {
                                Result = "1",
                                Party_ID = (PartyOne == null) ? 0 : PartyOne.Party_ID,
                                Party_IsLocked = (PartyOne == null) ? true : PartyOne.Party_IsLocked,
                                Party_Name = PartyName,
                                Party_IsActive = (PartyOne == null) ? false : PartyOne.Party_IsActive,
                            };

                        }
                        else if (PartyLocale.Count() > 0)
                        {
                            jsonResult = new
                            {
                                Result = 2
                            };
                        }

                        else if (PartyLocale.Count() == 0)
                        {

                            jsonResult = new
                            {
                                Result = "0"
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                jsonResult = new
                {
                    Result = "0"//Not Exists                 
                };
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonResult);
        }
        #endregion
        #region ::: Get Party Dtails By ID vinay n 20/1/25:::
        /// <summary>
        /// GetPartyDetailsByPartyID
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult GetPartyDetailsByPartyID(GetPartyDetailsByPartyIDList Obj, string connString, int LogException)
        {

            var jsonResult = default(dynamic);
            IEnumerable<GNM_Party> Party = null;
            GNM_Party PartyOne = null;
            string PartyName = string.Empty;
            bool PartyLock = false;
            IEnumerable<GNM_PartyLocale> PartyLocale = null;

            bool Party_IsActive = true;
            int Company_ID = Convert.ToInt32(Obj.Company_ID);

            int PartyType = 1;
            int PartyID = Obj.Party_ID;
            if (Convert.ToBoolean(Obj.CreditNote) == false)
            {
                PartyType = 4;
            }

            try
            {

                int LangID = Obj.Language_ID;
                int userLanguageID = Convert.ToInt32(Obj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(Obj.GeneralLanguageID);


                if (userLanguageID == generalLanguageID)
                {
                    List<SqlParameter> sqlParameters = new List<SqlParameter>
                    {
                        new SqlParameter("@Company_ID", SqlDbType.Int) { Value = Company_ID },
                        new SqlParameter("@PartyID", SqlDbType.Int) { Value = PartyID },
                        new SqlParameter("@PartyType", SqlDbType.Int) { Value = PartyType },

                        new SqlParameter("@UserLanguageID", SqlDbType.Int) { Value = userLanguageID },
                        new SqlParameter("@GeneralLanguageID", SqlDbType.Int) { Value = generalLanguageID },
                        new SqlParameter("@Language_ID", SqlDbType.Int) { Value = LangID }
                    };

                    List<GNM_Party> partyList = new List<GNM_Party>();
                    partyList = Common.GetValueFromDB<List<GNM_Party>>("EXEC SP_AMERP_PartsandServices_GetPartyDetailsByPartyID", sqlParameters, connString, LogException, false, 0, true);
                    Party = partyList.AsEnumerable();
                    PartyName = Party.FirstOrDefault().Party_Name;
                    if (Party.Count() == 1)
                    {
                        foreach (var p in Party)
                        {
                            PartyID = p.Party_ID;
                            PartyLock = Convert.ToBoolean(p.Party_IsLocked);
                            Party_IsActive = p.Party_IsActive;
                        }

                        jsonResult = new
                        {
                            Result = "1",
                            Party_ID = PartyID,
                            Party_IsActive = Party_IsActive,
                            Party_IsLocked = PartyLock,
                            Party_Name = PartyName,
                        };

                    }
                    else if (Party.Count() == 0)
                    {
                        jsonResult = new
                        {
                            Result = "0"
                        };

                    }
                }
                else
                {

                    List<SqlParameter> sqlParameters = new List<SqlParameter>
                    {
                        new SqlParameter("@Company_ID", SqlDbType.Int) { Value = Company_ID },
                        new SqlParameter("@PartyID", SqlDbType.Int) { Value = PartyID },
                        new SqlParameter("@PartyType", SqlDbType.Int) { Value = PartyType },

                        new SqlParameter("@UserLanguageID", SqlDbType.Int) { Value = userLanguageID },
                        new SqlParameter("@GeneralLanguageID", SqlDbType.Int) { Value = generalLanguageID },
                        new SqlParameter("@Language_ID", SqlDbType.Int) { Value = LangID }
                    };

                    int locCount = 0;
                    locCount = Common.GetValueFromDB<int>(" SP_AMERP_PartsandServices_GetPartyDetailsByPartyID", sqlParameters, connString, LogException, true, 1, true);

                    if (locCount == 1)
                    {


                        List<GNM_PartyLocale> PartyLocaleList = new List<GNM_PartyLocale>();
                        PartyLocaleList = Common.GetValueFromDB<List<GNM_PartyLocale>>(" SP_AMERP_PartsandServices_GetPartyDetailsByPartyID", sqlParameters, connString, LogException, false, 0, true);
                        PartyLocale = PartyLocaleList.AsEnumerable();

                        PartyName = PartyLocale.FirstOrDefault().Party_Name;  // Added by Srikanth , Date : 10/16/2013
                        jsonResult = new
                        {
                            Result = "1",
                            Party_ID = (PartyOne == null) ? 0 : PartyOne.Party_ID,
                            Party_IsLocked = (PartyOne == null) ? true : PartyOne.Party_IsLocked,
                            Party_Name = PartyName,
                            Party_IsActive = (PartyOne == null) ? false : PartyOne.Party_IsActive,
                        };

                    }

                    else if (locCount == 0)
                    {

                        jsonResult = new
                        {
                            Result = "0"
                        };
                    }

                }
            }
            catch (Exception ex)
            {
                jsonResult = new
                {
                    Result = "0"//Not Exists                 
                };
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonResult);
        }
        #endregion
        #region ::: SelAllClaimsDetails vinay n 20/1/25:::
        /// <summary>
        /// SelCreditDebitNoteDetail
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>

        public static IActionResult SelCreditDebitNoteDetail(SelCreditDebitNoteDetailList Obj, string connString, int LogException)
        {
            int userLanguageID = Convert.ToInt32(Obj.UserLanguageID);
            int generalLanguageID = Convert.ToInt32(Obj.GeneralLanguageID);


            var jsonData = default(dynamic);
            int Company_ID = Convert.ToInt32(Obj.Company_ID);
            int Count = 0;
            int Total = 0;
            string TotalAmount = "0.00";
            try
            {

                List<SqlParameter> sqlParameters = new List<SqlParameter>
                    {
                        new SqlParameter("@CreditDebitNoteID", SqlDbType.Int) { Value = Obj.CreditDebitNoteID }

                    };

                List<PST_CreditDebitNoteDetail> CreditDebitNote = new List<PST_CreditDebitNoteDetail>();
                string query = " SELECT *  FROM PST_CreditDebitNoteDetail   WHERE CreditDebitNote_ID = @CreditDebitNoteID;";
                CreditDebitNote = Common.GetValueFromDB<List<PST_CreditDebitNoteDetail>>(query, sqlParameters, connString, LogException);
                IEnumerable<PST_CreditDebitNoteDetail> CreditDebitNoteList = CreditDebitNote.AsEnumerable();
                IEnumerable<ClaimMembers> iClaimList = null;

                iClaimList = (from a in CreditDebitNoteList.Distinct()
                              select new ClaimMembers
                              {
                                  edit = string.Empty,
                                  Number = a.TransactionNumber,//"<span key='" + a.Transaction_ID+ "'  style='cursor:pointer'  class='LabelClick'>" + a.TransactionNumber + "</span>",
                                  Date = Convert.ToDateTime(a.TransactionDate).ToString("dd-MMM-yyyy"),
                                  GrossAmount = Convert.ToDecimal(a.GrossAmount).ToString("0.00"),
                                  ID = a.Transaction_ID,
                                  GrossAmountCal = Convert.ToDecimal(a.GrossAmount),
                              });


                TotalAmount = iClaimList.Sum(a => a.GrossAmountCal).ToString("0.00");

                jsonData = new
                {
                    Result = 1,
                    total = Total,
                    TotalAmount,
                    records = Count,
                    data = iClaimList.ToArray()

                };

                return new JsonResult(jsonData);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                return null;
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }
                return null;
            }
        }
        #endregion

        #region ::: SelAllCrediDebitNotes vinay n 20/1/25::
        /// <summary>
        /// Select
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <param name="sidx"></param>
        /// <param name="sord"></param>
        /// <param name="page"></param>
        /// <param name="rows"></param>
        /// <param name="_search"></param>
        /// <param name="filters"></param>
        /// <param name="advnce"></param>
        /// <param name="Query"></param>
        /// <returns></returns>
        public static IActionResult Select(GetAllCreditDebitList obj, string connString, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters, bool advnce, string Query)
        {

            int count = 0;
            int total = 0;

            try
            {

                IQueryable<SRTCreditNoteHeader> icreditDebitNoteArray = null;

                icreditDebitNoteArray = GetAllCreditDebit(obj, connString, LogException);
                //FilterToolBar Search
                if (_search == true)
                {
                    string decodedValue = Uri.UnescapeDataString(filters);
                    Filters filtersObj = JObject.Parse(decodedValue).ToObject<Filters>();
                    icreditDebitNoteArray = icreditDebitNoteArray.FilterSearch<SRTCreditNoteHeader>(filtersObj);

                }//Advance Search
                else if (advnce = true)
                {
                    string decodedValue = Uri.UnescapeDataString(Query);
                    AdvanceFilter advnfilter = JObject.Parse(decodedValue).ToObject<AdvanceFilter>();

                    icreditDebitNoteArray = icreditDebitNoteArray.AdvanceSearch<SRTCreditNoteHeader>(advnfilter);
                }
                //Sorting 

                if (sidx == "TransactionNumber" || sidx == "TransactionDate")
                {
                    icreditDebitNoteArray = icreditDebitNoteArray.OrderByField<SRTCreditNoteHeader>("CreditDebitNote_ID", sord);

                }
                else
                {
                    icreditDebitNoteArray = icreditDebitNoteArray.OrderByField<SRTCreditNoteHeader>(sidx, sord);
                }
                //Session["CreditDebitNote"] = icreditDebitNoteArray;
                int PartsInvoiceObjectID = Common.GetObjectID("PRT_SalesInvoiceReturn");
                count = icreditDebitNoteArray.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                var jsonResult = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = icreditDebitNoteArray.ToList().Paginate(page, rows),
                    CurrentDate = DateTime.Today.ToString("dd-MMM-yyyy"),
                    PartsInvoiceObjectID,
                    filter = filters,
                    advanceFilter = Query,
                };
                return new JsonResult(jsonResult);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                return null;
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }
                return null;
            }
        }
        #endregion

        #region ::: Get All Credit/Debit Notes vinay n 20/1/25:::
        /// <summary>
        /// GetAllCreditDebit
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        private static IQueryable<SRTCreditNoteHeader> GetAllCreditDebit(GetAllCreditDebitList Obj, string connString, int LogException)
        {
            IQueryable<SRTCreditNoteHeader> icreditDebitNoteArray = null;
            try
            {
                int userLanguageID = Convert.ToInt32(Obj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(Obj.GeneralLanguageID);
                int Company_ID = Convert.ToInt32(Obj.Company_ID);
                int Branch_ID = Convert.ToInt32(Obj.Branch);
                string CreditOrDebit = "C";
                if (Convert.ToBoolean(Obj.CreditNote) == false)
                {
                    CreditOrDebit = "D";
                }

                IEnumerable<SRTCreditNoteHeader> creditDebitNoteArray = null;
                string MandatoryClaim = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "MandatoryClaim").ToString();
                string WarrantyClaim = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "WarrantyClaim").ToString();
                string ServiceInvoiceReturn = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ServiceInvoiceReturn").ToString();
                string PartsInvoiceRetrurn = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "PartsInvoiceReturn").ToString();
                string GDRSettlement = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "GDRSettlement").ToString();
                string SalesInvoiceReturn = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "SalesInvoiceReturn").ToString();
                List<SqlParameter> sqlParameters = new List<SqlParameter>
                {
                    new SqlParameter("@GeneralLanguageID", SqlDbType.Int) { Value = userLanguageID },
                    new SqlParameter("@UserLanguageID", SqlDbType.Int) { Value =generalLanguageID },
                    new SqlParameter("@Company_ID", SqlDbType.Int) { Value = Company_ID },
                    new SqlParameter("@Branch_ID", SqlDbType.Int) { Value =Branch_ID },
                    new SqlParameter("@CreditOrDebit", SqlDbType.VarChar, 255) { Value = CreditOrDebit }
                };


                List<SRTCreditNoteHeader> CreditNoteHeaderList = new List<SRTCreditNoteHeader>();

                CreditNoteHeaderList = Common.GetValueFromDB<List<SRTCreditNoteHeader>>("SP_AMERP_PartsandServices_GetAllCreditDebit", sqlParameters, connString, LogException, false, 0, true);
                icreditDebitNoteArray = creditDebitNoteArray.AsQueryable<SRTCreditNoteHeader>();

            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return icreditDebitNoteArray;
        }
        #endregion
        #region ::: Get Party Detail Grid vinay n 20/1/25:::
        /// <summary>
        /// SelectPartyDetailGrid
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <param name="sidx"></param>
        /// <param name="sord"></param>
        /// <param name="page"></param>
        /// <param name="rows"></param>
        /// <param name="_search"></param>
        /// <param name="filters"></param>
        /// <param name="advnce"></param>
        /// <param name="Query"></param>
        /// <returns></returns>
        public static IActionResult SelectPartyDetailGrid(SelectPartyDetailGridPST_CreditDebitNoteList obj, string connString, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters, bool advnce, string Query)
        {
            var jsonobj = default(dynamic);
            IEnumerable<GNM_Party> Party = null;
            IEnumerable<GNM_PartyLocale> PartyLocale = null;
            int total = 0;
            string Pname = Common.DecryptString(obj.PartyName);
            try
            {

                string GenLangCode = obj.GeneralLanguageCode.ToString();
                string UserLangCode = obj.UserLanguageCode.ToString();
                int Company_ID = Convert.ToInt32(obj.Company_ID);


                if (GenLangCode == UserLangCode)
                {
                    List<SqlParameter> sqlParameters = new List<SqlParameter>
                    {
                        new SqlParameter("@Company_ID", SqlDbType.Int) { Value = Company_ID },
                        new SqlParameter("@GenLangCode", SqlDbType.NVarChar, 10) { Value = GenLangCode },
                        new SqlParameter("@UserLangCode", SqlDbType.NVarChar, 10) { Value = UserLangCode },
                        new SqlParameter("@Pname", SqlDbType.NVarChar, 255) { Value = Pname },
                        new SqlParameter("@CreditNote", SqlDbType.Bit) { Value = obj.CreditNote },
                        new SqlParameter("@Language_ID", SqlDbType.Int) { Value = obj.Language_ID },
                        new SqlParameter("@Party_IsActive", SqlDbType.Bit) { Value = obj.Party_IsActive }
                    };



                    List<GNM_Party> partyList = new List<GNM_Party>();

                    partyList = Common.GetValueFromDB<List<GNM_Party>>(" SP_AMERP_PartsandServices_SelectPartyDetailGrid", sqlParameters, connString, LogException, false, 0, true);
                    var list = from PartyDetails in Party
                               select new
                               {
                                   Party_ID = PartyDetails.Party_ID,
                                   Select = "<label  key='" + PartyDetails.Party_ID + "' class='PartySelect' style='color:blue;text-decoration:underline'>Select</label>",
                                   Party_Name = PartyDetails.Party_Name,
                                   Party_Location = PartyDetails.Party_Location
                               };

                    total = Convert.ToInt32(Math.Ceiling(Convert.ToDouble(list.ToList().Count)) / rows);
                    List<dynamic> arr = new List<dynamic>();
                    for (int i = 0; i < list.ToList().Count; i++)
                    {
                        if ((i >= (page * rows) - rows) && (i < (page * rows) + rows))
                        {
                            arr.Add(list.ToList()[i]);
                        }
                    }
                    jsonobj = new
                    {
                        TotalPages = total,
                        PageNo = page,
                        RecordCount = arr.Count(),
                        rows = arr.ToArray()
                    };
                }
                else
                {
                    List<SqlParameter> sqlParameters = new List<SqlParameter>
                    {
                        new SqlParameter("@Company_ID", SqlDbType.Int) { Value = Company_ID },
                        new SqlParameter("@GenLangCode", SqlDbType.NVarChar, 10) { Value = GenLangCode },
                        new SqlParameter("@UserLangCode", SqlDbType.NVarChar, 10) { Value = UserLangCode },
                        new SqlParameter("@Pname", SqlDbType.NVarChar, 255) { Value = Pname },
                        new SqlParameter("@CreditNote", SqlDbType.Bit) { Value = obj.CreditNote },
                        new SqlParameter("@Language_ID", SqlDbType.Int) { Value = obj.Language_ID },
                        new SqlParameter("@Party_IsActive", SqlDbType.Bit) { Value = obj.Party_IsActive }
                    };



                    List<GNM_Party> partyList = new List<GNM_Party>();

                    partyList = Common.GetValueFromDB<List<GNM_Party>>(" SP_AMERP_PartsandServices_SelectPartyDetailGrid", sqlParameters, connString, LogException, false, 0, true);

                    var list = from PartyLocDetails in partyList

                               select new
                               {
                                   Party_ID = PartyLocDetails.Party_ID,
                                   Select = "<label  key='" + PartyLocDetails.Party_ID + "' class='PartySelect' style='color:blue;text-decoration:underline'>Select</label>",
                                   Party_Name = PartyLocDetails.Party_Name,
                                   Party_Location = PartyLocDetails.Party_Location
                               };

                    total = Convert.ToInt32(Math.Ceiling(Convert.ToDouble(list.ToList().Count)) / rows);
                    List<dynamic> arr = new List<dynamic>();
                    for (int i = 0; i < list.ToList().Count; i++)
                    {
                        if ((i >= (page * rows) - rows) && (i < (page * rows) + rows))
                        {
                            arr.Add(list.ToList()[i]);
                        }
                    }
                    jsonobj = new
                    {
                        TotalPages = total,
                        PageNo = page,
                        RecordCount = arr.Count(),
                        rows = arr.ToArray()
                    };

                }

            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                //  return RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }
                    // 
                }
                //  return RedirectToAction("Error");
            }

            return new JsonResult(jsonobj);
        }
        #endregion

        #region ::: To Export vinay n 20/1/25:::
        /// <summary>
        /// Export
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static async Task<object> Export(GetAllCreditDebitList Obj, string connString, int LogException)
        {
            try
            {
                DataTable CreditDebitNoteTable = new DataTable();
                string CreditDebitNote = "";
                string CreditDebitENote = "";
                if (Convert.ToBoolean(Obj.CreditNote) == false)
                {
                    CreditDebitENote = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "DebitENote").ToString();
                    CreditDebitNote = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "DebitNote").ToString();
                    CreditDebitNoteTable.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "DebitNoteNumber").ToString());
                    CreditDebitNoteTable.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Date").ToString());
                    CreditDebitNoteTable.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "DocumentType").ToString());
                    CreditDebitNoteTable.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "PartyType").ToString());
                    CreditDebitNoteTable.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Party").ToString());
                    CreditDebitNoteTable.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "DebitNoteAmount").ToString());


                }
                else
                {
                    CreditDebitENote = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CreditENote").ToString();
                    CreditDebitNote = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CreditNote").ToString();
                    CreditDebitNoteTable.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CreditNoteNumber").ToString());
                    CreditDebitNoteTable.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Date").ToString());
                    CreditDebitNoteTable.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "DocumentType").ToString());
                    if (Convert.ToBoolean(Obj.CreditNote) == true && IsDealerToDealerSupply == "T")
                    {
                        CreditDebitNoteTable.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "IsDealer").ToString());
                    }
                    CreditDebitNoteTable.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "PartyType").ToString());
                    CreditDebitNoteTable.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Party").ToString());
                    CreditDebitNoteTable.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CreditNoteAmount").ToString());
                }


                DataTable dtAlignment = new DataTable();
                dtAlignment.Columns.Add("Note Number");
                dtAlignment.Columns.Add("Date");
                dtAlignment.Columns.Add("DocumentType");
                dtAlignment.Columns.Add("PartyType");
                dtAlignment.Columns.Add("Party");
                dtAlignment.Columns.Add("NoteAmount");
                if (Convert.ToBoolean(Obj.CreditNote) == true && IsDealerToDealerSupply == "T")
                {
                    dtAlignment.Columns.Add("IsDealer"); dtAlignment.Rows.Add(0, 0, 0, 0, 0, 2, 0);
                }
                else
                {
                    dtAlignment.Rows.Add(0, 0, 0, 0, 0, 2);
                }

                IQueryable<SRTCreditNoteHeader> iJobCardArray = null;
                iJobCardArray = GetAllCreditDebit(Obj, connString, LogException);//(IQueryable<SRTCreditNoteHeader>)Session["CreditDebitNote"];
                if (Obj.filter.ToString() != "null")
                {
                    Filters filters = JObject.Parse(Common.DecryptString(Obj.filter)).ToObject<Filters>();
                    if (filters.rules.Count() > 0)
                    {
                        iJobCardArray = iJobCardArray.FilterSearch<SRTCreditNoteHeader>(filters);
                    }
                }
                else if (Obj.advanceFilter.ToString() != "null")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Obj.advanceFilter).ToObject<AdvanceFilter>();
                    iJobCardArray = iJobCardArray.AdvanceSearch<SRTCreditNoteHeader>(advnfilter);
                }
                if (Obj.sidx.ToString() == "TransactionNumber" || Obj.sidx.ToString() == "TransactionDate")
                {
                    iJobCardArray = iJobCardArray.OrderByField<SRTCreditNoteHeader>("CreditDebitNote_ID", Obj.sord.ToString());
                }
                else
                {
                    iJobCardArray = iJobCardArray.OrderByField<SRTCreditNoteHeader>(Obj.sidx.ToString(), Obj.sord.ToString());
                }

                int count = iJobCardArray.Count();
                if (Convert.ToBoolean(Obj.CreditNote) == true && IsDealerToDealerSupply == "T")
                {
                    for (int i = 0; i < count; i++)
                    {
                        CreditDebitNoteTable.Rows.Add(iJobCardArray.ElementAt(i).TransactionNumber, iJobCardArray.ElementAt(i).TransactionDate, iJobCardArray.ElementAt(i).DocumentType, iJobCardArray.ElementAt(i).IsDealer, iJobCardArray.ElementAt(i).PartyType, iJobCardArray.ElementAt(i).Party_Name, iJobCardArray.ElementAt(i).TotalAmount);
                    }
                }
                else
                {
                    for (int i = 0; i < count; i++)
                    {
                        CreditDebitNoteTable.Rows.Add(iJobCardArray.ElementAt(i).TransactionNumber, iJobCardArray.ElementAt(i).TransactionDate, iJobCardArray.ElementAt(i).DocumentType, iJobCardArray.ElementAt(i).PartyType, iJobCardArray.ElementAt(i).Party_Name, iJobCardArray.ElementAt(i).TotalAmount);
                    }
                }
                ExportList reportExportList = new ExportList
                {
                    Company_ID = Obj.Company_ID, // Assuming this is available in ExportObj
                    Branch = Obj.Branch,
                    dt1 = dtAlignment,

                    dt = CreditDebitNoteTable,

                    FileName = CreditDebitENote, // Set a default or dynamic filename
                    Header = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), CreditDebitENote).ToString(), // Set a default or dynamic header
                    exprtType = Obj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                    UserCulture = Obj.UserCulture
                };
                var res = await DocumentExport.Export(reportExportList, connString, LogException);
                return res.Value;
                // DocumentExport.Export(Obj.exprtType, CreditDebitNoteTable, dtAlignment, CreditDebitENote, CreditDebitNote);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return null;
        }
        #endregion


        #region ::: SelAllClaims vinay n 21/1/25:::
        /// <summary>
        /// SelAllClaims
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult SelAllClaims(SelAllClaimsList Obj, string connString, int LogException)
        {
            var jsonData = default(dynamic);
            int DocumentType = Obj.DocumentType;

            int userLanguageID = Convert.ToInt32(Obj.UserLanguageID);
            int generalLanguageID = Convert.ToInt32(Obj.GeneralLanguageID);


            List<DropdownValues> taxStruct = new List<DropdownValues>();
            List<SRT_WarrantyClaimHeaderCN> WarrantyClaimOEMList = new List<SRT_WarrantyClaimHeaderCN>();
            List<SRT_MandatoryClaimHeaderCN> MandatoryClaimList = new List<SRT_MandatoryClaimHeaderCN>();
            List<SRT_ServiceInvoiceReturnHeaderCN> ServiceInvoiceReturnList = new List<SRT_ServiceInvoiceReturnHeaderCN>();
            List<PRT_SalesInvoiceReturnCN> SalesInvoiceReturnList = new List<PRT_SalesInvoiceReturnCN>();
            List<PRT_SalesInvoiceRP> SalesInvoiceList = new List<PRT_SalesInvoiceRP>();
            List<PRT_GDRSettlementDetailsRP> GDRistDetail = new List<PRT_GDRSettlementDetailsRP>();
            List<PRT_GDRSettlementRP> GDRist = new List<PRT_GDRSettlementRP>();
            List<GNM_RefMasterDetail> refDetail = new List<GNM_RefMasterDetail>();
            GNM_RefMaster refMaster = null;
            GNM_RefMasterDetail refdetaislaa = null;
            List<GNM_Party> PartyList = new List<GNM_Party>();

            try
            {
                int Company_ID = Convert.ToInt32(Obj.Company_ID);

                int Count = 0;
                int Total = 0;
                string TotalAmount = "0.00";


                List<SqlParameter> sqlParameters = new List<SqlParameter>
                {
                    new SqlParameter("@DocumentType", SqlDbType.Int) { Value = DocumentType }
                };







                if (DocumentType == 2)
                {
                    WarrantyClaimOEMList = Common.GetValueFromDB<List<SRT_WarrantyClaimHeaderCN>>("SP_AMERP_PartsandServices_SelAllClaims_1", sqlParameters, connString, LogException, false, 0, true);
                    //  WarrantyClaimOEMList = CreditDebitNoteChildClient.SRT_WarrantyClaimHeaderCN.ToList();
                }
                else if (DocumentType == 1)
                {
                    MandatoryClaimList = Common.GetValueFromDB<List<SRT_MandatoryClaimHeaderCN>>("SP_AMERP_PartsandServices_SelAllClaims_1", sqlParameters, connString, LogException, false, 0, true);
                    // MandatoryClaimList = CreditDebitNoteChildClient.SRT_MandatoryClaimHeaderCN.ToList();
                }
                else if (DocumentType == 3)
                {
                    ServiceInvoiceReturnList = Common.GetValueFromDB<List<SRT_ServiceInvoiceReturnHeaderCN>>("SP_AMERP_PartsandServices_SelAllClaims_1", sqlParameters, connString, LogException, false, 0, true);
                    //ServiceInvoiceReturnList = CreditDebitNoteChildClient.SRT_ServiceInvoiceReturnHeaderCN.ToList();
                }
                else if (DocumentType == 4)
                {
                    SalesInvoiceReturnList = Common.GetValueFromDB<List<PRT_SalesInvoiceReturnCN>>("SP_AMERP_PartsandServices_SelAllClaims_1", sqlParameters, connString, LogException, false, 0, true);
                    //SalesInvoiceReturnList = CreditDebitNoteChildClient.PRT_SalesInvoiceReturnCN.ToList();
                }
                else if (DocumentType == 5)
                {
                    SalesInvoiceList = Common.GetValueFromDB<List<PRT_SalesInvoiceRP>>("SP_AMERP_PartsandServices_SelAllClaims_1", sqlParameters, connString, LogException, false, 0, true);
                    //SalesInvoiceList = CreditDebitNoteChildClient.PRT_SalesInvoiceRP.Where(a => a.CampaignID > 0 && (a.IsClaimed == false || a.IsClaimed == null)).ToList();
                }
                else if (DocumentType == 7)
                {
                    GDRistDetail = Common.GetValueFromDB<List<PRT_GDRSettlementDetailsRP>>("SP_AMERP_PartsandServices_SelAllClaims_1", sqlParameters, connString, LogException, false, 0, true);
                    GDRist = Common.GetValueFromDB<List<PRT_GDRSettlementRP>>("SP_AMERP_PartsandServices_SelAllClaims_1", sqlParameters, connString, LogException, true, 1, true);
                    // GDRistDetail = CreditDebitNoteChildClient.PRT_GDRSettlementDetailsRP.ToList();
                    // GDRist = CreditDebitNoteChildClient.PRT_GDRSettlementRP.ToList();
                }




                List<SqlParameter> sqlParameters2 = new List<SqlParameter>
                {
                    new SqlParameter("@DocumentType", SqlDbType.Int) { Value = DocumentType },
                    new SqlParameter("@OEM", SqlDbType.Bit) { Value = Convert.ToBoolean(Obj.OEM) },
                    new SqlParameter("@CreditNote", SqlDbType.Bit) { Value = Convert.ToBoolean(Obj.CreditNote) },
                    new SqlParameter("@PartyID", SqlDbType.Int) { Value = Obj.PartyID }
                };
                PartyList = Common.GetValueFromDB<List<GNM_Party>>("SP_AMERP_PartsandServices_SelAllClaims_2", sqlParameters2, connString, LogException, false, 0, true);




                List<SqlParameter> sqlParameters3 = new List<SqlParameter>
                {
                    new SqlParameter("@Company_ID", SqlDbType.Int) { Value = Company_ID },
                    new SqlParameter("@IsDealer", SqlDbType.Bit) { Value = Obj.IsDealer },
                    new SqlParameter("@DocumentType", SqlDbType.Int) { Value = DocumentType },
                    new SqlParameter("@userLanguageID", SqlDbType.Int) { Value = userLanguageID },
                    new SqlParameter("@generalLanguageID", SqlDbType.Int) { Value = generalLanguageID },
                    new SqlParameter("@PartyID", SqlDbType.Int) { Value = Obj.PartyID }
                };
                List<GNM_Branch> branchlist = new List<GNM_Branch>();

                List<DropdownValues> partyList = new List<DropdownValues>();

                partyList = Common.GetValueFromDB<List<DropdownValues>>("SP_AMERP_PartsandServices_SelAllClaims_3", sqlParameters3, connString, LogException, false, 0, true);


                string ISOEM = Common.GetValueFromDB<string>("SP_AMERP_PartsandServices_SelAllClaims_3", sqlParameters3, connString, LogException, true, 1, true);


                if (Obj.IsDealer == true && DocumentType == 4)
                {
                    branchlist = Common.GetValueFromDB<List<GNM_Branch>>("SP_AMERP_PartsandServices_SelAllClaims_3", sqlParameters3, connString, LogException, true, 1, true);
                }



                if (userLanguageID == generalLanguageID)
                {
                    taxStruct = (from tax in partyList.AsEnumerable()

                                 select new DropdownValues()
                                 {
                                     ID = tax.ID,
                                     Name = tax.Name
                                 }).ToList();
                }
                else
                {
                    taxStruct = (from tax in partyList.AsEnumerable()

                                 select new DropdownValues()
                                 {
                                     ID = tax.ID,
                                     Name = tax.Name
                                 }).ToList();
                }









                string ddlTaxStr = "0:----Select----;";
                for (int i = 0; i < taxStruct.Count; i++)
                {
                    ddlTaxStr = ddlTaxStr + taxStruct.ElementAt(i).ID + ":" + taxStruct.ElementAt(i).Name + ";";
                }

                ddlTaxStr = ddlTaxStr.TrimEnd(new char[] { ';' });


                IEnumerable<ClaimMembers> iClaimList = null;
                List<SRT_ServiceInvoiceHeaderRP> SRT_ServiceInvoiceHeaderRPList = new List<SRT_ServiceInvoiceHeaderRP>();
                List<PRT_SalesInvoiceRP> PRT_SalesInvoiceList = new List<PRT_SalesInvoiceRP>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "SELECT * FROM SRT_ServiceInvoiceHeader";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.Text;


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    SRT_ServiceInvoiceHeaderRP ql = new SRT_ServiceInvoiceHeaderRP
                                    {
                                        ServiceInvoice_ID = reader.GetInt32(reader.GetOrdinal("ServiceInvoice_ID"))

                                    };
                                    SRT_ServiceInvoiceHeaderRPList.Add(ql);
                                }



                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }


                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "  SELECT * FROM PRT_SalesInvoice;";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.Text;


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    PRT_SalesInvoiceRP ql = new PRT_SalesInvoiceRP
                                    {
                                        SalesInvoice_ID = reader.GetInt32(reader.GetOrdinal("SalesInvoice_ID")),
                                        IsDealer = reader.GetBoolean(reader.GetOrdinal("IsDealer"))

                                    };
                                    PRT_SalesInvoiceList.Add(ql);
                                }



                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }



                if (PartyList.Count > 0 || branchlist.Count > 0)
                {

                    if (DocumentType == 2) //IF(DOCUMENT TYPE IS wARRANTY clAIM
                    {
                        int Rejected = 0;


                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            string query = "SELECT WFStepStatus_ID FROM WF_WFStepStatus WHERE StepStatusCode = @StepStatusCode";

                            SqlCommand command = null;

                            try
                            {
                                using (command = new SqlCommand(query, conn))
                                {
                                    command.CommandType = CommandType.Text;


                                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                    {
                                        conn.Open();
                                    }
                                    using (SqlDataReader reader = command.ExecuteReader())
                                    {
                                        if (reader.Read())
                                        {
                                            Rejected = Convert.ToInt32(reader["WFStepStatus_ID"]);
                                        }



                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                if (LogException == 1)
                                {
                                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                }

                            }
                            finally
                            {
                                command.Dispose();
                                conn.Close();
                                conn.Dispose();
                                SqlConnection.ClearAllPools();
                            }
                        }


                        if (Convert.ToBoolean(Obj.CreditNote) == true)
                        {
                            if (Convert.ToBoolean(Obj.OEM) == true)
                            {
                                iClaimList = (from a in WarrantyClaimOEMList.Distinct()
                                              join b in PartyList on a.Branch_ID equals b.Relationship_Branch_ID
                                              where (a.ClaimStatus_ID == 0) && a.WorkFlow_ClaimStatus_ID != Rejected && (a.CreditNote_ID == null || a.CreditNote_ID == 0)

                                              select new ClaimMembers
                                              {
                                                  edit = "<img id='" + a.WarrantyClaim_ID + "' src='" + AppPath + "/Content/Images/Minus.gif' key='" + a.WarrantyClaim_ID + "' class='RemoveTransaction'/>",
                                                  Number = "<span key='" + a.WarrantyClaim_ID + "' Company_ID ='" + a.Company_ID + "' style='cursor:pointer'  class='LabelClick'>" + a.WarrantyClaimNumber + "</span>",
                                                  NumberHidden = a.WarrantyClaimNumber,
                                                  Date = Convert.ToDateTime(a.WarrantyClaimDate).ToString("dd-MMM-yyyy"),
                                                  GrossAmount = Convert.ToDecimal(a.TotalApprovedAmount).ToString("0.00"),
                                                  ID = a.WarrantyClaim_ID,
                                                  GrossAmountCal = Convert.ToDecimal(a.TotalApprovedAmount),
                                              });
                            }
                        }
                        else
                        {

                            iClaimList = (from a in WarrantyClaimOEMList.Distinct()
                                          where (a.ClaimStatus_ID == 0) && a.WorkFlow_ClaimStatus_ID != Rejected && (a.CreditNote_ID == null || a.CreditNote_ID == 0)
                                          && (a.Company_ID == Company_ID) && (a.Branch_ID == Convert.ToInt32(Obj.Branch))
                                          select new ClaimMembers
                                          {
                                              edit = "<img id='" + a.WarrantyClaim_ID + "' src='" + AppPath + "/Content/Images/Minus.gif' key='" + a.WarrantyClaim_ID + "' class='RemoveTransaction'/>",
                                              Number = "<span key='" + a.WarrantyClaim_ID + "' Company_ID ='" + a.Company_ID + "' style='cursor:pointer'  class='LabelClick'>" + a.WarrantyClaimNumber + "</span>",
                                              NumberHidden = a.WarrantyClaimNumber,
                                              Date = Convert.ToDateTime(a.WarrantyClaimDate).ToString("dd-MMM-yyyy"),
                                              GrossAmount = Convert.ToDecimal(a.TotalApprovedAmount).ToString("0.00"),
                                              ID = a.WarrantyClaim_ID,
                                              GrossAmountCal = Convert.ToDecimal(a.TotalApprovedAmount),
                                          });
                        }
                    }

                    else if (DocumentType == 1)
                    {
                        if (Convert.ToBoolean(Obj.CreditNote) == true)
                        {

                            if (Convert.ToBoolean(Obj.OEM) == true)
                            {
                                iClaimList = (from a in MandatoryClaimList.Distinct()
                                              join b in PartyList on a.Branch_ID equals b.Relationship_Branch_ID
                                              where a.Status == 1 && (a.CreditOrDebitNote_ID == null || a.CreditOrDebitNote_ID == 0)
                                              select new ClaimMembers
                                              {
                                                  edit = "<img id='" + a.MandatoryClaim_ID + "' src='" + AppPath + "/Content/Images/Minus.gif' key='" + a.MandatoryClaim_ID + "' class='RemoveTransaction'/>",
                                                  Number = "<span key='" + a.MandatoryClaim_ID + "' Company_ID ='" + a.Company_ID + "' style='cursor:pointer'  class='LabelClick'>" + a.MandatoryClaimNumber + "</span>",
                                                  NumberHidden = a.MandatoryClaimNumber,
                                                  Date = Convert.ToDateTime(a.MandatoryClaimDate).ToString("dd-MMM-yyyy"),
                                                  GrossAmount = Convert.ToDecimal(a.ApprovedAmount).ToString("0.00"),
                                                  ID = a.MandatoryClaim_ID,
                                                  GrossAmountCal = Convert.ToDecimal(a.ApprovedAmount),


                                              });
                            }
                        }
                        else
                        {
                            iClaimList = (from a in MandatoryClaimList.Distinct()
                                          where a.Company_ID == Company_ID && a.Branch_ID == Convert.ToInt32(Obj.Branch) && a.Status == 1 && (a.CreditOrDebitNote_ID == null || a.CreditOrDebitNote_ID == 0)
                                          select new ClaimMembers
                                          {
                                              edit = "<img id='" + a.MandatoryClaim_ID + "' src='" + AppPath + "/Content/Images/Minus.gif' key='" + a.MandatoryClaim_ID + "' class='RemoveTransaction'/>",
                                              Number = "<span key='" + a.MandatoryClaim_ID + "' Company_ID ='" + a.Company_ID + "' style='cursor:pointer'  class='LabelClick'>" + a.MandatoryClaimNumber + "</span>",
                                              NumberHidden = a.MandatoryClaimNumber,
                                              Date = Convert.ToDateTime(a.MandatoryClaimDate).ToString("dd-MMM-yyyy"),
                                              GrossAmount = Convert.ToDecimal(a.ApprovedAmount).ToString("0.00"),
                                              ID = a.MandatoryClaim_ID,
                                              GrossAmountCal = Convert.ToDecimal(a.ApprovedAmount),
                                          });
                        }
                    }
                    else if (DocumentType == 3)
                    {
                        if (Convert.ToBoolean(Obj.OEM) == true)
                        {
                            iClaimList = (from a in ServiceInvoiceReturnList.Distinct()
                                          join c in SRT_ServiceInvoiceHeaderRPList on a.Invoice_Id equals c.ServiceInvoice_ID
                                          join b in PartyList on c.Party_ID equals b.Party_ID
                                          where (a.CreditDebitNote_ID == null || a.CreditDebitNote_ID == 0) && (a.Company_ID == Company_ID) && (b.Party_ID == Obj.PartyID) && (a.Branch_ID == Convert.ToInt32(Obj.Branch))


                                          select new ClaimMembers
                                          {
                                              edit = "<img id='" + a.ServiceInvoiceReturn_ID + "' src='" + AppPath + "/Content/Images/Minus.gif' key='" + a.ServiceInvoiceReturn_ID + "' class='RemoveTransaction'/>",
                                              Number = "<span key='" + a.ServiceInvoiceReturn_ID + "' Company_ID ='" + a.Company_ID + "' style='cursor:pointer'  class='LabelClick'>" + a.InvoiceReturnNumber + "</span>",
                                              NumberHidden = a.InvoiceReturnNumber,
                                              Date = Convert.ToDateTime(a.InvoiceReturnDate).ToString("dd-MMM-yyyy"),
                                              GrossAmount = Convert.ToDecimal(a.TotalReturnedAmount).ToString("0.00"),
                                              ID = a.ServiceInvoiceReturn_ID,
                                              GrossAmountCal = Convert.ToDecimal(a.TotalReturnedAmount),
                                          });
                        }
                        else
                        {
                            iClaimList = (from a in ServiceInvoiceReturnList.Distinct()
                                          join c in SRT_ServiceInvoiceHeaderRPList on a.Invoice_Id equals c.ServiceInvoice_ID
                                          join b in PartyList on c.Party_ID equals b.Party_ID
                                          where (a.CreditDebitNote_ID == null || a.CreditDebitNote_ID == 0) && (a.Company_ID == Company_ID) && (b.Party_ID == Obj.PartyID) && (a.Branch_ID == Convert.ToInt32(Obj.Branch))
                                          select new ClaimMembers
                                          {
                                              edit = "<img id='" + a.ServiceInvoiceReturn_ID + "' src='" + AppPath + "/Content/Images/Minus.gif' key='" + a.ServiceInvoiceReturn_ID + "' class='RemoveTransaction'/>",
                                              Number = "<span key='" + a.ServiceInvoiceReturn_ID + "' Company_ID ='" + a.Company_ID + "' style='cursor:pointer'  class='LabelClick'>" + a.InvoiceReturnNumber + "</span>",
                                              NumberHidden = a.InvoiceReturnNumber,
                                              Date = Convert.ToDateTime(a.InvoiceReturnDate).ToString("dd-MMM-yyyy"),
                                              GrossAmount = Convert.ToDecimal(a.TotalReturnedAmount).ToString("0.00"),
                                              ID = a.ServiceInvoiceReturn_ID,
                                              GrossAmountCal = Convert.ToDecimal(a.TotalReturnedAmount),
                                          });
                        }

                    }
                    else if (DocumentType == 4)
                    {
                        if (Convert.ToBoolean(Obj.OEM) == true)
                        {
                            if (Obj.IsDealer == false || Obj.IsDealer == null)
                            {
                                iClaimList = (from a in SalesInvoiceReturnList.Distinct()
                                              join c in PRT_SalesInvoiceList on a.SalesInvoice_ID equals c.SalesInvoice_ID
                                              join b in PartyList on c.Party_ID equals b.Party_ID
                                              where (a.CreditDebitNote_ID == null || a.CreditDebitNote_ID == 0) && (b.Party_ID == Obj.PartyID) && (c.IsDealer == false || c.IsDealer == null)
                                              select new ClaimMembers
                                              {
                                                  edit = "<img id='" + a.SalesInvoiceReturn_ID + "' src='" + AppPath + "/Content/Images/Minus.gif' key='" + a.SalesInvoiceReturn_ID + "' class='RemoveTransaction'/>",
                                                  Number = "<span key='" + a.SalesInvoiceReturn_ID + "' Company_ID ='" + a.Company_ID + "' style='cursor:pointer'  class='LabelClick'>" + a.SalesInvoiceReturnNumber + "</span>",
                                                  NumberHidden = a.SalesInvoiceReturnNumber,
                                                  Date = Convert.ToDateTime(a.SalesInvoiceReturnDate).ToString("dd-MMM-yyyy"),
                                                  GrossAmount = Convert.ToDecimal(a.TotalInvoiceReturnAmount).ToString("0.00"),
                                                  ID = a.SalesInvoiceReturn_ID,
                                                  GrossAmountCal = Convert.ToDecimal(a.TotalInvoiceReturnAmount),
                                              });
                            }
                            else
                            {
                                iClaimList = (from a in SalesInvoiceReturnList.Distinct()
                                              join c in PRT_SalesInvoiceList on a.SalesInvoice_ID equals c.SalesInvoice_ID
                                              join b in branchlist on c.Party_ID equals b.Branch_ID
                                              where (a.CreditDebitNote_ID == null || a.CreditDebitNote_ID == 0) && (b.Branch_ID == Obj.PartyID) && (c.IsDealer == true)
                                              select new ClaimMembers
                                              {
                                                  edit = "<img id='" + a.SalesInvoiceReturn_ID + "' src='" + AppPath + "/Content/Images/Minus.gif' key='" + a.SalesInvoiceReturn_ID + "' class='RemoveTransaction'/>",
                                                  Number = "<span key='" + a.SalesInvoiceReturn_ID + "' Company_ID ='" + a.Company_ID + "' style='cursor:pointer'  class='LabelClick'>" + a.SalesInvoiceReturnNumber + "</span>",
                                                  NumberHidden = a.SalesInvoiceReturnNumber,
                                                  Date = Convert.ToDateTime(a.SalesInvoiceReturnDate).ToString("dd-MMM-yyyy"),
                                                  GrossAmount = Convert.ToDecimal(a.TotalInvoiceReturnAmount).ToString("0.00"),
                                                  ID = a.SalesInvoiceReturn_ID,
                                                  GrossAmountCal = Convert.ToDecimal(a.TotalInvoiceReturnAmount),
                                              });
                            }
                        }
                        else
                        {
                            if (Obj.IsDealer == false || Obj.IsDealer == null)
                            {
                                iClaimList = (from a in SalesInvoiceReturnList.Distinct()
                                              join c in PRT_SalesInvoiceList on a.SalesInvoice_ID equals c.SalesInvoice_ID
                                              join b in PartyList on c.Party_ID equals b.Party_ID
                                              where (a.CreditDebitNote_ID == null || a.CreditDebitNote_ID == 0) && (a.Company_ID == Company_ID) && (b.Party_ID == Obj.PartyID) && (a.Branch_ID == Convert.ToInt32(Obj.Branch)) && (c.IsDealer == false || c.IsDealer == null)
                                              select new ClaimMembers
                                              {
                                                  edit = "<img id='" + a.SalesInvoiceReturn_ID + "' src='" + AppPath + "/Content/Images/Minus.gif' key='" + a.SalesInvoiceReturn_ID + "' class='RemoveTransaction'/>",
                                                  Number = "<span key='" + a.SalesInvoiceReturn_ID + "' Company_ID ='" + a.Company_ID + "' style='cursor:pointer'  class='LabelClick'>" + a.SalesInvoiceReturnNumber + "</span>",
                                                  NumberHidden = a.SalesInvoiceReturnNumber,
                                                  Date = Convert.ToDateTime(a.SalesInvoiceReturnDate).ToString("dd-MMM-yyyy"),
                                                  GrossAmount = Convert.ToDecimal(a.TotalInvoiceReturnAmount).ToString("0.00"),
                                                  ID = a.SalesInvoiceReturn_ID,
                                                  GrossAmountCal = Convert.ToDecimal(a.TotalInvoiceReturnAmount),
                                              });
                            }
                            else
                            {
                                iClaimList = (from a in SalesInvoiceReturnList.Distinct()
                                              join c in PRT_SalesInvoiceList on a.SalesInvoice_ID equals c.SalesInvoice_ID
                                              join b in branchlist on c.Party_ID equals b.Branch_ID
                                              where (a.CreditDebitNote_ID == null || a.CreditDebitNote_ID == 0) && (a.Company_ID == Company_ID) && (b.Branch_ID == Obj.PartyID) && (a.Branch_ID == Convert.ToInt32(Obj.Branch)) && (c.IsDealer == true)
                                              select new ClaimMembers
                                              {
                                                  edit = "<img id='" + a.SalesInvoiceReturn_ID + "' src='" + AppPath + "/Content/Images/Minus.gif' key='" + a.SalesInvoiceReturn_ID + "' class='RemoveTransaction'/>",
                                                  Number = "<span key='" + a.SalesInvoiceReturn_ID + "' Company_ID ='" + a.Company_ID + "' style='cursor:pointer'  class='LabelClick'>" + a.SalesInvoiceReturnNumber + "</span>",
                                                  NumberHidden = a.SalesInvoiceReturnNumber,
                                                  Date = Convert.ToDateTime(a.SalesInvoiceReturnDate).ToString("dd-MMM-yyyy"),
                                                  GrossAmount = Convert.ToDecimal(a.TotalInvoiceReturnAmount).ToString("0.00"),
                                                  ID = a.SalesInvoiceReturn_ID,
                                                  GrossAmountCal = Convert.ToDecimal(a.TotalInvoiceReturnAmount),
                                              });
                            }
                        }

                    }
                    else if (DocumentType == 5)
                    {
                        string query = "";
                        if (ISOEM == "True")
                        {
                            query = "select a.SalesInvoice_ID as ID,a.SalesInvoiceNumber as NumberHidden, convert(varchar(25), a.SalesInvoiceDate) as Date,convert(varchar(25),sum(((b.MRP*b.Quantity)*c.OEMdiscount)/100)) as GrossAmount from PRT_SalesInvoice as a join PRT_SalesInvoiceDetails as b on a.SalesInvoice_ID=b.SalesInvoice_ID join SRM_CampaignPartsDetails as c on b.Parts_ID=c.Parts_ID and b.Quantity>0";
                            query = query + " and a.CampaignID=c.Campaign_ID join GNM_Party as p on a.Party_ID=p.Party_ID where a.CampaignID>0 and (a.IsClaimed=0 or a.IsClaimed is null) and  a.Company_ID=" + Company_ID + " and a.Branch_ID=" + Convert.ToInt32(Obj.Branch) + " group by a.SalesInvoice_ID,a.SalesInvoiceNumber, a.SalesInvoiceDate";
                        }
                        else
                        {
                            query = "select a.SalesInvoice_ID as ID,a.SalesInvoiceNumber as NumberHidden, convert(varchar(25), a.SalesInvoiceDate) as Date,convert(varchar(25),sum(((b.MRP*b.Quantity)*c.OEMdiscount)/100)) as GrossAmount from PRT_SalesInvoice as a join PRT_SalesInvoiceDetails as b on a.SalesInvoice_ID=b.SalesInvoice_ID join SRM_CampaignPartsDetails as c on b.Parts_ID=c.Parts_ID and b.Quantity>0";
                            query = query + " and a.CampaignID=c.Campaign_ID join GNM_Party as p on a.Party_ID=p.Party_ID where a.CampaignID>0 and (a.IsClaimed=0 or a.IsClaimed is null) and a.Party_ID=" + Obj.PartyID + " and a.Company_ID=" + Company_ID + " and a.Branch_ID=" + Convert.ToInt32(Obj.Branch) + " group by a.SalesInvoice_ID,a.SalesInvoiceNumber, a.SalesInvoiceDate";
                        }
                        List<ClaimMembers> iClaim = new List<ClaimMembers>();
                        iClaim = Common.GetValueFromDB<List<ClaimMembers>>(query, null, connString, LogException, false, 0, false);
                        iClaimList = iClaim.AsEnumerable();
                        iClaimList = (from a in iClaimList
                                      select new ClaimMembers
                                      {
                                          edit = "<img id='" + a.ID + "' src='" + AppPath + "/Content/Images/Minus.gif' key='" + a.ID + "' class='RemoveTransaction'/>",
                                          Number = "<span key='" + a.ID + "' Company_ID ='" + Company_ID + "' style='cursor:pointer'  class='LabelClick'>" + a.NumberHidden + "</span>",
                                          NumberHidden = a.NumberHidden,
                                          Date = Convert.ToDateTime(a.Date).ToString("dd-MMM-yyyy"),
                                          GrossAmount = Convert.ToDecimal(a.GrossAmount).ToString("0.00"),
                                          ID = a.ID,
                                          GrossAmountCal = Convert.ToDecimal(a.GrossAmount),
                                      });

                    }
                    else if (DocumentType == 6)
                    {
                        string query = "";
                        if (ISOEM == "True")
                        {
                            query = "select a.ServiceInvoiceNumber as NumberHidden,a.ServiceInvoice_ID as ID,convert(varchar(25),a.ServiceInvoiceDate) as Date,convert(varchar(25),sum(((ISNULL(b.MRP,0)*ISNULL(b.Quantity,0))*ISNULL(c.OEMdiscount,0))/100) + SUM(((ISNULL(s.Rate,0)*ISNULL(s.Quantity,0))*ISNULL(cs.OEMdiscount,0))/100)) as GrossAmount from SRT_ServiceInvoiceHeader as a left outer join SRT_ServiceInvoicePartsDetail as b on a.ServiceInvoice_ID=b.ServiceInvoice_ID";
                            query = query + " join SRT_JobCardHeader as j on a.JobCard_ID=j.JobCard_ID  join SRM_CampaignPartsDetails as c on b.Parts_ID=c.Parts_ID and c.Campaign_ID=j.Campaign_ID left outer join SRT_ServiceInvoiceServiceChargeDetail as s on a.ServiceInvoice_ID=s.ServiceInvoice_ID join SRM_CampaignServiceChargeDetails as cs on s.ServiceCharge_ID=cs.ServiceCharge_ID and cs.Campaign_ID=j.Campaign_ID";
                            query = query + " where j.Campaign_ID>0 and (a.IsClaimed=0 or a.IsClaimed is null) and  a.Company_ID=" + Company_ID + " and a.Branch_ID=" + Convert.ToInt32(Obj.Branch) + " group by a.ServiceInvoice_ID,a.ServiceInvoiceNumber, a.ServiceInvoiceDate";
                        }
                        else
                        {
                            query = "select a.ServiceInvoiceNumber as NumberHidden,a.ServiceInvoice_ID as ID,convert(varchar(25),a.ServiceInvoiceDate) as Date,convert(varchar(25),sum(((ISNULL(b.MRP,0)*ISNULL(b.Quantity,0))*ISNULL(c.OEMdiscount,0))/100) + SUM(((ISNULL(s.Rate,0)*ISNULL(s.Quantity,0))*ISNULL(cs.OEMdiscount,0))/100)) as GrossAmount from SRT_ServiceInvoiceHeader as a left outer join SRT_ServiceInvoicePartsDetail as b on a.ServiceInvoice_ID=b.ServiceInvoice_ID";
                            query = query + " join SRT_JobCardHeader as j on a.JobCard_ID=j.JobCard_ID  join SRM_CampaignPartsDetails as c on b.Parts_ID=c.Parts_ID and c.Campaign_ID=j.Campaign_ID left outer join SRT_ServiceInvoiceServiceChargeDetail as s on a.ServiceInvoice_ID=s.ServiceInvoice_ID join SRM_CampaignServiceChargeDetails as cs on s.ServiceCharge_ID=cs.ServiceCharge_ID and cs.Campaign_ID=j.Campaign_ID";
                            query = query + " where j.Campaign_ID>0 and (a.IsClaimed=0 or a.IsClaimed is null) and a.Party_ID=" + Obj.PartyID + " and a.Company_ID=" + Company_ID + " and a.Branch_ID=" + Convert.ToInt32(Obj.Branch) + " group by a.ServiceInvoice_ID,a.ServiceInvoiceNumber, a.ServiceInvoiceDate";

                        }
                        List<ClaimMembers> iClaim = new List<ClaimMembers>();
                        iClaim = Common.GetValueFromDB<List<ClaimMembers>>(query, null, connString, LogException, false, 0, false);
                        iClaimList = iClaim.AsEnumerable();

                        iClaimList = (from a in iClaimList
                                      select new ClaimMembers
                                      {
                                          edit = "<img id='" + a.ID + "' src='" + AppPath + "/Content/Images/Minus.gif' key='" + a.ID + "' class='RemoveTransaction'/>",
                                          Number = "<span key='" + a.ID + "' Company_ID ='" + Company_ID + "' style='cursor:pointer'  class='LabelClick'>" + a.NumberHidden + "</span>",
                                          NumberHidden = a.NumberHidden,
                                          Date = Convert.ToDateTime(a.Date).ToString("dd-MMM-yyyy"),
                                          GrossAmount = Convert.ToDecimal(a.GrossAmount).ToString("0.00"),
                                          ID = a.ID,
                                          GrossAmountCal = Convert.ToDecimal(a.GrossAmount),
                                      });

                    }
                    else if (DocumentType == 7)
                    {
                        if (Convert.ToBoolean(Obj.CreditNote) == true)
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                string query = "SELECT * FROM GNM_RefMasterDetail WHERE RefMasterDetail_Short_Name = 'CreditNote'";

                                SqlCommand command = null;

                                try
                                {
                                    using (command = new SqlCommand(query, conn))
                                    {
                                        command.CommandType = CommandType.Text;


                                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                        {
                                            conn.Open();
                                        }
                                        using (SqlDataReader reader = command.ExecuteReader())
                                        {
                                            while (reader.Read())
                                            {
                                                GNM_RefMasterDetail ql = new GNM_RefMasterDetail
                                                {
                                                    RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID"))


                                                };
                                                refDetail.Add(ql);
                                            }



                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    if (LogException == 1)
                                    {
                                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    }

                                }
                                finally
                                {
                                    command.Dispose();
                                    conn.Close();
                                    conn.Dispose();
                                    SqlConnection.ClearAllPools();
                                }
                            }


                            // if (Convert.ToBoolean(Session["OEM"]) == true)
                            // {
                            iClaimList = (from a in GDRist
                                          join c in GDRistDetail on a.GDRSettlement_ID equals c.GDRSettlement_ID
                                          join b in refDetail on c.SettlementType_ID equals b.RefMasterDetail_ID
                                          where (c.ExcessQuantity > 0) && (a.CreditNote_ID == null || a.CreditNote_ID == 0) && a.Company_ID == Company_ID
                                          select new ClaimMembers
                                          {
                                              edit = "<img id='" + a.GDRSettlement_ID + "' src='" + AppPath + "/Content/Images/Minus.gif' key='" + a.GDRSettlement_ID + "' class='RemoveTransaction'/>",
                                              Number = "<span key='" + a.GDRSettlement_ID + "' Company_ID ='" + a.Company_ID + "' style='cursor:pointer'  class='LabelClick'>" + a.GDRNumber + "</span>",
                                              NumberHidden = a.GDRNumber,
                                              Date = Convert.ToDateTime(a.GDRSettlementDate).ToString("dd-MMM-yyyy"),
                                              GrossAmount = Convert.ToDecimal(GetGDRSettlementDetails(a.GDRSettlement_ID, c.SettlementType_ID, connString).Sum(z => z.ExcessAmount)).ToString("0.00"),
                                              //GrossAmount = Convert.ToDecimal(c.ExcessAmount).ToString("0.00"),
                                              ID = a.GDRSettlement_ID,
                                              GrossAmountCal = Convert.ToDecimal(GetGDRSettlementDetails(a.GDRSettlement_ID, c.SettlementType_ID, connString).Sum(z => z.ExcessAmount)),
                                          }).Distinct();
                            // }
                        }
                        else
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                string query = "SELECT * FROM GNM_RefMasterDetail WHERE RefMasterDetail_Short_Name = 'DebitNote'";

                                SqlCommand command = null;

                                try
                                {
                                    using (command = new SqlCommand(query, conn))
                                    {
                                        command.CommandType = CommandType.Text;


                                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                        {
                                            conn.Open();
                                        }
                                        using (SqlDataReader reader = command.ExecuteReader())
                                        {
                                            while (reader.Read())
                                            {
                                                GNM_RefMasterDetail ql = new GNM_RefMasterDetail
                                                {
                                                    RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID"))


                                                };
                                                refDetail.Add(ql);
                                            }



                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    if (LogException == 1)
                                    {
                                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    }

                                }
                                finally
                                {
                                    command.Dispose();
                                    conn.Close();
                                    conn.Dispose();
                                    SqlConnection.ClearAllPools();
                                }
                            }

                            iClaimList = (from a in GDRist
                                          join c in GDRistDetail on a.GDRSettlement_ID equals c.GDRSettlement_ID
                                          join b in refDetail on c.SettlementType_ID equals b.RefMasterDetail_ID
                                          where (c.ShortQuantity > 0 || c.DamagedQuantity > 0) && (a.DebitNote_ID == null || a.DebitNote_ID == 0) && a.Company_ID == Company_ID
                                          select new ClaimMembers
                                          {
                                              edit = "<img id='" + a.GDRSettlement_ID + "' src='" + AppPath + "/Content/Images/Minus.gif' key='" + a.GDRSettlement_ID + "' class='RemoveTransaction'/>",
                                              Number = "<span key='" + a.GDRSettlement_ID + "' Company_ID ='" + a.Company_ID + "' style='cursor:pointer'  class='LabelClick'>" + a.GDRNumber + "</span>",
                                              NumberHidden = a.GDRNumber,
                                              Date = Convert.ToDateTime(a.GDRSettlementDate).ToString("dd-MMM-yyyy"),
                                              GrossAmount = Convert.ToDecimal(GetGDRSettlementDetails(a.GDRSettlement_ID, c.SettlementType_ID, connString).Sum(z => z.DamagedAmount) + GetGDRSettlementDetails(a.GDRSettlement_ID, c.SettlementType_ID, connString).Sum(z => z.ShortAmount)).ToString("0.00"),
                                              //GrossAmount = Convert.ToDecimal(a.ApprovedAmount).ToString("0.00"),
                                              ID = a.GDRSettlement_ID,
                                              GrossAmountCal = Convert.ToDecimal(GetGDRSettlementDetails(a.GDRSettlement_ID, c.SettlementType_ID, connString).Sum(z => z.DamagedAmount) + GetGDRSettlementDetails(a.GDRSettlement_ID, c.SettlementType_ID, connString).Sum(z => z.ShortAmount)),
                                          });

                        }
                    }
                    var distinctdetail = (from A in iClaimList
                                          select new
                                          {
                                              A.edit,
                                              A.Number,
                                              A.NumberHidden,
                                              A.Date,
                                              A.GrossAmount,
                                              A.ID,
                                              A.GrossAmountCal,
                                          }).Distinct().ToList();

                    if (iClaimList != null)
                    {
                        TotalAmount = distinctdetail.Sum(a => a.GrossAmountCal).ToString("0.00");
                    }
                    Count = distinctdetail.Count();

                    jsonData = new
                    {
                        Result = 2,
                        taxStruct = (taxStruct.OrderBy(a => a.Name)),
                        total = Total,
                        TotalAmount,
                        records = Count,
                        TotalCount = Count,
                        data = distinctdetail.ToArray(),

                        Date = DateTime.Today.ToString("dd-MMM-yyy")

                    };

                }
                else
                {
                    jsonData = new
                    {
                        TotalAmount,
                        TotalCount = Count,
                    };
                }
                return new JsonResult(jsonData);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                return null;
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }
                return null;
            }
        }
        public static List<PRT_GDRSettlementDetailsRP> GetGDRSettlementDetails(int gdrSettlementID, int? settlementTypeID, string connectionString)
        {
            string query = "SELECT GDRSettlement_ID, SettlementType_ID, ExcessAmount, DamagedAmount, ShortAmount " +
                           "FROM PRT_GDRSettlementDetails " +
                           "WHERE GDRSettlement_ID = @GDRSettlement_ID AND SettlementType_ID = @SettlementType_ID";

            List<PRT_GDRSettlementDetailsRP> result = new List<PRT_GDRSettlementDetailsRP>();

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    cmd.Parameters.AddWithValue("@GDRSettlement_ID", gdrSettlementID);
                    cmd.Parameters.AddWithValue("@SettlementType_ID", settlementTypeID);

                    conn.Open();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            result.Add(new PRT_GDRSettlementDetailsRP
                            {
                                GDRSettlement_ID = reader.GetInt32(reader.GetOrdinal("GDRSettlement_ID")),
                                SettlementType_ID = reader.GetInt32(reader.GetOrdinal("SettlementType_ID")),
                                ExcessAmount = reader.GetDecimal(reader.GetOrdinal("ExcessAmount"))
                            });
                        }
                    }
                }
            }

            return result;
        }

        #endregion

        #region ::: Save  vinay n 21/1/25:::
        /// <summary>
        /// Save
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>

        //public static IActionResult Save(SaveCreditDebitNoteList Obj, string connString, int LogException)
        //{
        //    var jsonData = default(dynamic);
        //    using (System.Transactions.TransactionScope scope = new System.Transactions.TransactionScope())
        //    {
        //        try
        //        {
        //            //JObject jObjCausing;
        //            //JTokenReader jTRCausing;
        //            int Company_ID = Convert.ToInt32(Obj.Company_ID);

        //            int Branch_ID = Convert.ToInt32(Obj.Branch);
        //            int objectID = Convert.ToInt32(Obj.ObjectID);

        //            JObject jobj = JObject.Parse(Obj.Data);
        //            PST_CreditDebitNoteHeader headerRow = jobj.ToObject<PST_CreditDebitNoteHeader>();

        //            headerRow.Company_ID = Company_ID;
        //            headerRow.Branch_ID = Branch_ID;
        //            headerRow.Party_ID = headerRow.Party_ID;
        //            headerRow.IsDealer = Convert.ToBoolean(headerRow.IsDealer);
        //            headerRow.TransactionDate = DateTime.Today;
        //            headerRow.Remarks = headerRow.Remarks;
        //            if (headerRow.IsDealer == true)
        //            {
        //                headerRow.PartyType = 2;
        //            }
        //            int id = Convert.ToInt32(Obj.ObjectID);
        //            int UpdateRow_CreditDebitNote_ID = 0;
        //            string UpdateRow_TransactionNumber = "";
        //            bool CheckprefixSuffix = false;

        //            using (SqlConnection conn = new SqlConnection(connString))
        //            {
        //                string query = "SP_AMERP_PartsandServices_SaveCreditDebitNote_1";

        //                SqlCommand command = null;

        //                try
        //                {
        //                    using (command = new SqlCommand(query, conn))
        //                    {
        //                        command.CommandType = CommandType.StoredProcedure;
        //                        command.Parameters.AddWithValue("@Company_ID", headerRow.Company_ID);
        //                        command.Parameters.AddWithValue("@Branch_ID", headerRow.Branch_ID);
        //                        command.Parameters.AddWithValue("@Party_ID", headerRow.Party_ID);
        //                        command.Parameters.AddWithValue("@IsDealer", headerRow.IsDealer);
        //                        command.Parameters.AddWithValue("@TransactionDate", headerRow.TransactionDate);
        //                        command.Parameters.AddWithValue("@Remarks", headerRow.Remarks ?? (object)DBNull.Value); // To handle NULL values for Remarks
        //                        command.Parameters.AddWithValue("@ObjectID", id);
        //                        command.Parameters.AddWithValue("@PartyType", headerRow.PartyType);
        //                        command.Parameters.AddWithValue("@CreditDebitNote_ID", headerRow.CreditDebitNote_ID);

        //                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
        //                        {
        //                            conn.Open();
        //                        }
        //                        using (SqlDataReader reader = command.ExecuteReader())
        //                        {


        //                            if (reader.HasRows)
        //                            {
        //                                while (reader.Read())
        //                                {

        //                                    UpdateRow_CreditDebitNote_ID = reader.GetInt32(reader.GetOrdinal("CreditDebitNote_ID"));
        //                                    UpdateRow_TransactionNumber = reader.GetString(reader.GetOrdinal("TransactionNumber"));


        //                                }
        //                            }
        //                            if (reader.NextResult())
        //                            {
        //                                if (reader.Read())
        //                                {
        //                                    CheckprefixSuffix = reader.GetBoolean(reader.GetOrdinal("CheckprefixSuffix"));


        //                                }
        //                            }

        //                        }
        //                    }
        //                }
        //                catch (Exception ex)
        //                {
        //                    if (LogException == 1)
        //                    {
        //                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
        //                    }

        //                }
        //                finally
        //                {
        //                    command.Dispose();
        //                    conn.Close();
        //                    conn.Dispose();
        //                    SqlConnection.ClearAllPools();
        //                }
        //            }



        //            if (CheckprefixSuffix == true)
        //            {



        //                // gbl.InsertGPSDetails(Convert.ToInt32(Obj.Company_ID), Convert.ToInt32(Obj.Branch), Convert.ToInt32(Obj.User_ID), Convert.ToInt32(Common.GetObjectID("PST_CreditDebitNote")), headerRow.CreditDebitNote_ID, 0, 0, "Insert", false, Convert.ToInt32(Obj.MenuID), Convert.ToDateTime(Obj.LoggedIDateTime));







        //                for (int i = 0; i < headerRow.PST_CreditDebitNoteDetail.Count; i++)
        //                {

        //                    int TransactionID = Convert.ToInt32(headerRow.PST_CreditDebitNoteDetail.ElementAt(i).Transaction_ID);
        //                    using (SqlConnection conn = new SqlConnection(connString))
        //                    {
        //                        string query = "SP_AMERP_PartsandServices_SaveCreditDebitNote_2";

        //                        SqlCommand command = null;

        //                        try
        //                        {
        //                            using (command = new SqlCommand(query, conn))
        //                            {
        //                                command.CommandType = CommandType.StoredProcedure;
        //                                command.Parameters.AddWithValue("@TransactionID", TransactionID); // INT
        //                                command.Parameters.AddWithValue("@DocumentType", headerRow.DocumentType); // INT
        //                                command.Parameters.AddWithValue("@CreditDebitNote_ID", UpdateRow_CreditDebitNote_ID); // INT
        //                                command.Parameters.AddWithValue("@TransactionNumber", UpdateRow_TransactionNumber); // NVARCHAR(50)
        //                                command.Parameters.AddWithValue("@CreditNote", Convert.ToBoolean(Obj.CreditNote)); // BIT

        //                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
        //                                {
        //                                    conn.Open();
        //                                }
        //                                command.ExecuteNonQuery();
        //                            }
        //                        }
        //                        catch (Exception ex)
        //                        {
        //                            if (LogException == 1)
        //                            {
        //                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
        //                            }

        //                        }
        //                        finally
        //                        {
        //                            command.Dispose();
        //                            conn.Close();
        //                            conn.Dispose();
        //                            SqlConnection.ClearAllPools();
        //                        }
        //                    }

        //                    // gbl.InsertGPSDetails(Convert.ToInt32(Obj.Company_ID), Convert.ToInt32(Obj.Branch), Convert.ToInt32(Obj.User_ID), Convert.ToInt32(Common.GetObjectID("PST_CreditDebitNote")), headerRow.CreditDebitNote_ID, 0, 0, "Update", false, (userLanguageID == generalLanguageID ? 0 : userLanguageID));

        //                }
        //                // string SMP = Server.MapPath(AppPath + "/Attachments");
        //                string SMP = "";
        //                //------------------------------------------------------------     
        //                if (headerRow.CreditDebitNote_ID != null)
        //                {
        //                    int CreditDebitNoteID = headerRow.CreditDebitNote_ID;
        //                    if (Obj.AttachmentData != null)
        //                    {
        //                        int History = headerRow.CreditDebitNote_ID;

        //                        string SrcPath = string.Empty;

        //                        List<Attachements> dsattachment = new List<Attachements>();
        //                        JObject jObj1 = JObject.Parse(Obj.AttachmentsData);
        //                        int Count = jObj1["rows"].Count();
        //                        Attachements[] ds = new Attachements[Count];
        //                        for (int i = 0; i < Count; i++)
        //                        {
        //                            Attachements detail = new Attachements();
        //                            ds[i] = detail;
        //                            JTokenReader reader = null;
        //                            reader = new JTokenReader(jObj1["rows"][i]["ATTACHMENTDETAIL_ID"]);
        //                            reader.Read();
        //                            ds[i].ATTACHMENTDETAIL_ID = Convert.ToInt32(reader.Value.ToString());
        //                            reader = new JTokenReader(jObj1["rows"][i]["FILENAME"]);
        //                            reader.Read();
        //                            ds[i].FILE_NAME = Common.DecryptString(reader.Value.ToString());
        //                            reader = new JTokenReader(jObj1["rows"][i]["FILEDESCRIPTION"]);
        //                            reader.Read();
        //                            ds[i].FILEDESCRIPTION = Common.DecryptString(reader.Value.ToString());

        //                            reader = new JTokenReader(jObj1["rows"][i]["Upload"]);
        //                            reader.Read();
        //                            ds[i].Upload = Convert.ToInt32(reader.Value.ToString());

        //                            reader = new JTokenReader(jObj1["rows"][i]["UPLOADDATE"]);
        //                            reader.Read();
        //                            ds[i].UPLOADDATE = Convert.ToDateTime(reader.Value.ToString());


        //                            reader = new JTokenReader(jObj1["rows"][i]["OBJECT_ID"]);
        //                            reader.Read();
        //                            ds[i].OBJECTID = Convert.ToInt32(reader.Value.ToString());


        //                            ds[i].DetailID = 0;
        //                            string DstPath = SMP + "/" + ds[i].OBJECTID + "-" + headerRow.CreditDebitNote_ID + "-" + Common.DecryptString(ds[i].FILE_NAME);
        //                            SrcPath = SMP + "/" + "Temp_" + Convert.ToInt32(Obj.ObjectID.ToString()) + "_" + Convert.ToInt32(Obj.User_ID.ToString()) + " /" + Common.DecryptString(ds[i].FILE_NAME);

        //                            if (System.IO.File.Exists(SrcPath))
        //                            {
        //                                System.IO.File.Move(SrcPath, DstPath);

        //                            }
        //                        }
        //                        List<Attachements> c = CommonFunctionalities.UploadAttachment(ds, headerRow.CreditDebitNote_ID, Convert.ToInt32(Obj.User_ID.ToString()), Convert.ToInt32(Obj.Company_ID.ToString()), 0, connString, LogException);

        //                        Obj.AttachmentData = null;
        //                    }


        //                    List<Attachements> dsattachdelete = new List<Attachements>();
        //                    JObject jObj2 = new JObject();
        //                    jObj2 = JObject.Parse(Obj.CreditAttachmentDelete);
        //                    int Count1 = jObj2["rows"].Count();
        //                    Attachements[] ds1 = new Attachements[Count1];
        //                    for (int i = 0; i < Count1; i++)
        //                    {
        //                        Attachements detail = new Attachements();
        //                        ds1[i] = detail;
        //                        JTokenReader reader = null;
        //                        reader = new JTokenReader(jObj2["rows"][i]["id"]);
        //                        reader.Read();
        //                        ds1[i].ATTACHMENTDETAIL_ID = Convert.ToInt32(reader.Value.ToString());
        //                        reader = new JTokenReader(jObj2["rows"][i]["FileName"]);
        //                        reader.Read();
        //                        ds1[i].FILE_NAME = Common.DecryptString(reader.Value.ToString());
        //                        reader = new JTokenReader(jObj2["rows"][i]["Object_ID"]);
        //                        reader.Read();
        //                        ds1[i].OBJECTID = Convert.ToInt32(reader.Value.ToString());
        //                        ds1[i].TransactionID = headerRow.CreditDebitNote_ID;
        //                    }
        //                    CommonFunctionalities.DeleteAttachments(ds1, SMP, connString, LogException);

        //                    int AttCount = CommonFunctionalities.GetAttachmentCount(objectID, headerRow.CreditDebitNote_ID, 0, connString, LogException);


                            
        //                    using (SqlConnection conn = new SqlConnection(connString))
        //                    {
        //                        string query = "UPDATE PST_CreditDebitNoteHeader SET AttachmentCount = @AttCount WHERE CreditDebitNote_ID = @CreditDebitNoteID";

        //                        SqlCommand command = null;

        //                        try
        //                        {
        //                            using (command = new SqlCommand(query, conn))
        //                            {
        //                                command.CommandType = CommandType.StoredProcedure;
        //                                command.Parameters.AddWithValue("@AttCount", AttCount);
        //                                command.Parameters.AddWithValue("@CreditDebitNoteID", CreditDebitNoteID);
        //                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
        //                                {
        //                                    conn.Open();
        //                                }
        //                                command.ExecuteNonQuery();
        //                            }
        //                        }
        //                        catch (Exception ex)
        //                        {
        //                            if (LogException == 1)
        //                            {
        //                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
        //                            }

        //                        }
        //                        finally
        //                        {
        //                            command.Dispose();
        //                            conn.Close();
        //                            conn.Dispose();
        //                            SqlConnection.ClearAllPools();
        //                        }
        //                    }
        //                }
        //                jsonData = new
        //                {
        //                    UpdateRow_CreditDebitNote_ID,
        //                    Saved = true,
        //                    Number = UpdateRow_TransactionNumber
        //                };
        //            }
        //            else
        //            {
        //                jsonData = new
        //                {
        //                    Saved = false,
        //                    CreditDebitNote_ID = 0
        //                };
        //            }
        //            scope.Complete();
        //        }
        //        catch (Exception ex)
        //        {
        //            if (LogException == 1)
        //            {
        //                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
        //            }
        //            jsonData = null;

        //        }
        //    }
        //    return new JsonResult(jsonData);
        //}
        #endregion


        #region ::: SelectParticularCreditDebitNote vinay n 21/1/25:::
        /// <summary>
        /// SelectParticularCreditDebitNote
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult SelectParticularCreditDebitNote(SelectParticularCreditDebitNoteList Obj, string connString, int LogException)
        {
            int userLanguageID = Convert.ToInt32(Obj.UserLanguageID);
            int generalLanguageID = Convert.ToInt32(Obj.GeneralLanguageID);
            try
            {
                var JsonResult = default(dynamic);

                GNM_Party Party = new GNM_Party();
                GNM_PartyLocale PartyLocale = new GNM_PartyLocale();
                int Company_ID = Convert.ToInt32(Obj.Company_ID);
                int Branch_ID = Convert.ToInt32(Obj.Branch);


                string CustomerE = CommonFunctionalities.GetResourceString(Obj.GeneralCulture.ToString(), "Customer").ToString();
                string SupplierE = CommonFunctionalities.GetResourceString(Obj.GeneralCulture.ToString(), "Supplier").ToString();


                string CustomerL = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Customer").ToString();
                string SupplierL = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Supplier").ToString();
                string CreditOrDebit = "C";
                if (Convert.ToBoolean(Obj.CreditNote) == false)
                {
                    CreditOrDebit = "D";
                }

                PST_CreditDebitNoteHeader CreditDebitNoteRow = null;
                GNM_Branch branchdata = new GNM_Branch();
                List<DropdownValues> taxStruct = new List<DropdownValues>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "SP_AMERP_PartsandServices_SelectParticularCreditDebitNote_1";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@Company_ID", Company_ID); // Replace 'companyID' with the actual variable
                            command.Parameters.AddWithValue("@Branch_ID", Branch_ID);   // Replace 'branchID' with the actual variable
                            command.Parameters.AddWithValue("@CreditDebitNoteID", (object)Obj.CreditDebitNoteID ?? DBNull.Value); // Use DBNull for NULL
                            command.Parameters.AddWithValue("@CreditOrDebit", CreditOrDebit); // Replace 'creditOrDebit' with the actual variable
                            command.Parameters.AddWithValue("@Mode", Obj.mode);            // Replace 'mode' with the actual variable
                            command.Parameters.AddWithValue("@UserLanguageID", userLanguageID); // Replace 'userLanguageID' with the actual variable
                            command.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    if (CreditDebitNoteRow.IsDealer == true)
                                    {
                                        branchdata = new GNM_Branch
                                        {
                                            Branch_ID = reader["Branch_ID"] != DBNull.Value ? Convert.ToInt32(reader["Branch_ID"]) : 0,
                                            Branch_Name = reader["Branch_Name"] != DBNull.Value ? reader["Branch_Name"].ToString() : string.Empty,

                                        };
                                    }
                                    else
                                    {
                                        Party = new GNM_Party
                                        {
                                            Party_ID = reader["Party_ID"] != DBNull.Value ? Convert.ToInt32(reader["Party_ID"]) : 0,
                                            Party_Name = reader["Party_Name"] != DBNull.Value ? reader["Party_Name"].ToString() : string.Empty,

                                        };
                                    }

                                }
                                if (reader.NextResult())
                                {
                                    DropdownValues drop = new DropdownValues
                                    {
                                        ID = reader["ID"] != DBNull.Value ? Convert.ToInt32(reader["ID"]) : 0,
                                        Name = reader["Name"] != DBNull.Value ? reader["Name"].ToString() : string.Empty,
                                    };
                                    taxStruct.Add(drop);
                                }



                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }




                int PartyobjectID = CommonFunctionalities.GetObjectID("CorePartyMaster", connString, LogException);
                int PartsInvoiceObjectID = CommonFunctionalities.GetObjectID("PRT_SalesInvoiceReturn", connString, LogException);





                //------------------------------------------------------------------------
                string ddlTaxStr = "0:----Select----;";
                for (int i = 0; i < taxStruct.Count; i++)
                {
                    ddlTaxStr = ddlTaxStr + taxStruct.ElementAt(i).ID + ":" + taxStruct.ElementAt(i).Name + ";";
                }

                ddlTaxStr = ddlTaxStr.TrimEnd(new char[] { ';' });

                if (generalLanguageID == userLanguageID)
                {

                    JsonResult = new
                    {
                        CreditDebitNote_ID = CreditDebitNoteRow.CreditDebitNote_ID,
                        TransactionNumber = CreditDebitNoteRow.TransactionNumber,
                        TransactionDate = Convert.ToDateTime(CreditDebitNoteRow.TransactionDate).ToString("dd-MMM-yyyy"),
                        DocumentType = CreditDebitNoteRow.DocumentType,
                        TaxStructure_ID = CreditDebitNoteRow.TaxStructure_ID,
                        TotalAmount = Convert.ToDecimal(CreditDebitNoteRow.TotalAmount).ToString("0.00"),
                        Party_ID = CreditDebitNoteRow.IsDealer == true ? branchdata.Branch_ID : CreditDebitNoteRow.Party_ID,
                        PartyType = (CreditDebitNoteRow.PartyType == 1 ? SupplierE : CreditDebitNoteRow.IsDealer == true ? "Dealer" : CustomerE),
                        Party_Name = CreditDebitNoteRow.IsDealer == true ? branchdata.Branch_Name : Party.Party_Name,
                        TaxAmount = Convert.ToDecimal(CreditDebitNoteRow.TaxAmount).ToString("0.00"),
                        taxStruct = (taxStruct),
                        PartyobjectID,
                        PartsInvoiceObjectID,
                        CreditDebitNoteRow.IsDealer,
                        Remarks = CreditDebitNoteRow.Remarks
                    };
                    return new JsonResult(JsonResult);
                }
                else
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "SELECT TOP 1 * FROM GNM_PartyLocale WHERE Party_ID = @Party_ID AND Language_ID = @UserLanguageID";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@Party_ID", CreditDebitNoteRow.Party_ID);
                                command.Parameters.AddWithValue("@UserLanguageID", userLanguageID);

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {


                                        PartyLocale = new GNM_PartyLocale
                                        {

                                            Party_Name = reader["Party_Name"] != DBNull.Value ? reader["Party_Name"].ToString() : string.Empty,

                                        };


                                    }




                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }


                    //-----------------------------------------------------
                    JsonResult = new
                    {
                        CreditDebitNote_ID = CreditDebitNoteRow.CreditDebitNote_ID,
                        TransactionNumber = CreditDebitNoteRow.TransactionNumber,
                        TransactionDate = Convert.ToDateTime(CreditDebitNoteRow.TransactionDate).ToString("dd-MMM-yyyy"),
                        DocumentType = CreditDebitNoteRow.DocumentType,
                        TaxStructure_ID = CreditDebitNoteRow.TaxStructure_ID,
                        TotalAmount = Convert.ToDecimal(CreditDebitNoteRow.TotalAmount).ToString("0.00"),
                        Party_ID = CreditDebitNoteRow.Party_ID,
                        PartyType = (CreditDebitNoteRow.PartyType == 1 ? SupplierL : CustomerL),
                        Party_Name = (PartyLocale == null ? string.Empty : PartyLocale.Party_Name),
                        TaxAmount = Convert.ToDecimal(CreditDebitNoteRow.TaxAmount).ToString("0.00"),
                        taxStruct = (taxStruct),
                        PartyobjectID,
                        PartsInvoiceObjectID,
                        CreditDebitNoteRow.IsDealer,
                        Remarks = CreditDebitNoteRow.Remarks
                    };
                    return new JsonResult(JsonResult);
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }
                return null;
            }
        }
        #endregion

        //#region ::: PrintView  vinay n 21/1/25:::
        ///// <summary>
        ///// Print of the Internal Invoice
        ///// </summary>
        //public static IActionResult PrintView(PrintViewList Obj, string connString, int LogException)
        //{
        //    try
        //    {
        //        int userLanguageID = Convert.ToInt32(Obj.UserLanguageID);
        //        int generalLanguageID = Convert.ToInt32(Obj.GeneralLanguageID);
        //        //-------------------------------------------
        //        var jsonResult = default(dynamic);

        //        int companyID = Obj.Company_ID;
        //        int branchID = Convert.ToInt32(Obj.Branch);





        //        string CustomerE = CommonFunctionalities.GetResourceString(Obj.GeneralCulture.ToString(), "Customer").ToString();
        //        string SupplierE = CommonFunctionalities.GetResourceString(Obj.GeneralCulture.ToString(), "Supplier").ToString();
        //        string DealerE = CommonFunctionalities.GetResourceString(Obj.GeneralCulture.ToString(), "Dealer").ToString();

        //        string DealerL = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Dealer").ToString();
        //        string CustomerL = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Customer").ToString();
        //        string SupplierL = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Supplier").ToString();




        //        string MandatoryClaim = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "MandatoryClaim").ToString();
        //        string WarrantyClaim = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "WarrantyClaim").ToString();
        //        string ServiceInvoiceReturn = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ServiceInvoiceReturn").ToString();
        //        string PartsInvoiceRetrurn = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "PartsInvoiceReturn").ToString();
        //        string GDRSettlement = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "GDRSettlement").ToString();
        //        bool CreditNote = false;
        //        ClaimMember claimMember = new ClaimMember();
        //        List<ClaimMember> claimMemberList = new List<ClaimMember>();


        //        if (Convert.ToBoolean(Obj.CreditNote) == true)
        //        {
        //            CreditNote = true;
        //        }
        //        CreditDebitNote creditDebitNote = null;
        //        using (SqlConnection conn = new SqlConnection(connString))
        //        {
        //            string query = "SP_AMERP_PartsandServices_PrintView";

        //            SqlCommand command = null;

        //            try
        //            {
        //                using (command = new SqlCommand(query, conn))
        //                {
        //                    command.CommandType = CommandType.StoredProcedure;
        //                    command.Parameters.AddWithValue("@CreditDebitNoteID", Obj.CreditDebitNoteID);

        //                    command.Parameters.AddWithValue("@CompanyID", companyID);
        //                    command.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
        //                    command.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

        //                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
        //                    {
        //                        conn.Open();
        //                    }
        //                    using (SqlDataReader reader = command.ExecuteReader())
        //                    {
        //                        if (reader.HasRows)
        //                        {
        //                            while (reader.Read())
        //                            {
        //                                if (userLanguageID == generalLanguageID)
        //                                {
        //                                    creditDebitNote = new CreditDebitNote
        //                                    {
        //                                        CreditDebitNote_ID = reader.GetInt32(reader.GetOrdinal("CreditDebitNote_ID")),
        //                                        TransactionNumber = reader.GetString(reader.GetOrdinal("TransactionNumber")),
        //                                        TransactionDate = reader.GetDateTime(reader.GetOrdinal("TransactionDate")),
        //                                        DocumentType = reader.GetInt32(reader.GetOrdinal("DocumentType")),
        //                                        TaxStructure_Name = reader.GetString(reader.GetOrdinal("TaxStructure_Name")),
        //                                        TaxAmount = reader.GetDecimal(reader.GetOrdinal("TaxAmount")),
        //                                        TotalAmount = reader.GetDecimal(reader.GetOrdinal("TotalAmount")),
        //                                        Remarks = reader.GetString(reader.GetOrdinal("Remarks")),
        //                                        Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID")),
        //                                        PartyType = reader.GetInt32(reader.GetOrdinal("PartyType")),
        //                                        Party_Name = reader.GetString(reader.GetOrdinal("Party_Name")),
        //                                        CompanyLogoName = reader.GetString(reader.GetOrdinal("CompanyLogoName")),
        //                                        IsPrintLogo = reader.GetBoolean(reader.GetOrdinal("IsPrintLogo")),
        //                                        CompName = reader.GetString(reader.GetOrdinal("CompName")),
        //                                        CompAddress = reader.GetString(reader.GetOrdinal("CompAddress")),
        //                                        BranchName = reader.GetString(reader.GetOrdinal("BranchName")),
        //                                        Branch_ID = reader.GetInt32(reader.GetOrdinal("Branch_ID"))
        //                                    };
        //                                }
        //                                else
        //                                {
        //                                    // Handle localized results when userLanguageID is not equal to generalLanguageID
        //                                    creditDebitNote = new CreditDebitNote
        //                                    {
        //                                        CreditDebitNote_ID = reader.GetInt32(reader.GetOrdinal("CreditDebitNote_ID")),
        //                                        TransactionNumber = reader.GetString(reader.GetOrdinal("TransactionNumber")),
        //                                        TransactionDate = reader.GetDateTime(reader.GetOrdinal("TransactionDate")),
        //                                        DocumentType = reader.GetInt32(reader.GetOrdinal("DocumentType")),
        //                                        TaxStructure_ID = reader.GetInt32(reader.GetOrdinal("TaxStructure_ID")),
        //                                        TaxStructure_Name = reader.GetString(reader.GetOrdinal("TaxStructure_Name")),
        //                                        TaxAmount = reader.GetDecimal(reader.GetOrdinal("TaxAmount")),
        //                                        TotalAmount = reader.GetDecimal(reader.GetOrdinal("TotalAmount")),
        //                                        Remarks = reader.GetString(reader.GetOrdinal("Remarks")),
        //                                        Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID")),
        //                                        PartyType = reader.GetInt32(reader.GetOrdinal("PartyType")),
        //                                        Party_Name = reader.GetString(reader.GetOrdinal("Party_Name")),
        //                                        CompanyLogoName = reader.GetString(reader.GetOrdinal("CompanyLogoName")),
        //                                        IsPrintLogo = reader.GetBoolean(reader.GetOrdinal("IsPrintLogo")),
        //                                        CompName = reader.GetString(reader.GetOrdinal("CompName")),
        //                                        CompAddress = reader.GetString(reader.GetOrdinal("CompAddress")),
        //                                        BranchName = reader.GetString(reader.GetOrdinal("BranchName")),
        //                                        Branch_ID = reader.GetInt32(reader.GetOrdinal("Branch_ID"))
        //                                    };
        //                                }
        //                            }
        //                        }

        //                        // Reading claim members (assuming separate read for claim members)
        //                        if (reader.NextResult())  // This is assuming a new result set for ClaimMembers
        //                        {
        //                            while (reader.Read())
        //                            {
        //                                claimMember = new ClaimMember
        //                                {
        //                                    Number = reader.GetString(reader.GetOrdinal("TransactionNumber")),
        //                                    Date = reader.GetString(reader.GetOrdinal("TransactionDate")),
        //                                    GrossAmount = reader.GetString(reader.GetOrdinal("GrossAmount"))

        //                                };
        //                                claimMemberList.Add(claimMember);


        //                            }
        //                        }




        //                    }
        //                }
        //            }
        //            catch (Exception ex)
        //            {
        //                if (LogException == 1)
        //                {
        //                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
        //                }

        //            }
        //            finally
        //            {
        //                command.Dispose();
        //                conn.Close();
        //                conn.Dispose();
        //                SqlConnection.ClearAllPools();
        //            }
        //        }















        //        if (userLanguageID == generalLanguageID)
        //        {
        //            jsonResult = new
        //            {
        //                CreditDebitNote_ID = creditDebitNote.CreditDebitNote_ID,
        //                TransactionNumber = creditDebitNote.TransactionNumber,
        //                TransactionDate = Convert.ToDateTime(creditDebitNote.TransactionDate).ToString("dd-MMM-yyyy"),
        //                DocumentType = ((creditDebitNote.DocumentType == 1) ? MandatoryClaim : ((creditDebitNote.DocumentType == 2) ? WarrantyClaim : ((creditDebitNote.DocumentType == 3) ? ServiceInvoiceReturn : creditDebitNote.DocumentType == 7 ? GDRSettlement : PartsInvoiceRetrurn))),
        //                taxStructName = creditDebitNote.TaxStructure_Name,
        //                TaxAmount = creditDebitNote.TaxAmount,
        //                TotalAmount = creditDebitNote.TotalAmount,
        //                Remarks = creditDebitNote.Remarks,
        //                Party_ID = creditDebitNote.Party_ID,
        //                PartyType = (creditDebitNote.PartyType == 1 ? SupplierE : (creditDebitNote.PartyType == 0) ? CustomerE : DealerE),
        //                Party_Name = creditDebitNote.Party_Name,
        //                iClaimList = claimMemberList,
        //                partsTotal = claimMemberList.Sum(i => i.GrossAmountCal),
        //                CreditNote,
        //                creditDebitNote.CompanyLogoName,
        //                creditDebitNote.IsPrintLogo,
        //                CompName = creditDebitNote.CompName,
        //                CompAddress = creditDebitNote.CompAddress,
        //                Branch = creditDebitNote.BranchName,
        //                RecordBranch_id = creditDebitNote.Branch_ID
        //            };
        //        }
        //        else
        //        {


        //            jsonResult = new
        //            {
        //                CreditDebitNote_ID = creditDebitNote.CreditDebitNote_ID,
        //                TransactionNumber = creditDebitNote.TransactionNumber,
        //                TransactionDate = Convert.ToDateTime(creditDebitNote.TransactionDate).ToString("dd-MMM-yyyy"),
        //                DocumentType = ((creditDebitNote.DocumentType == 1) ? MandatoryClaim : ((creditDebitNote.DocumentType == 2) ? WarrantyClaim : ((creditDebitNote.DocumentType == 3) ? ServiceInvoiceReturn : PartsInvoiceRetrurn))),
        //                TaxStructure_ID = creditDebitNote.TaxStructure_ID,
        //                taxStructName = creditDebitNote.TaxStructure_Name,
        //                TaxAmount = creditDebitNote.TaxAmount,
        //                Remarks = creditDebitNote.Remarks,
        //                TotalAmount = creditDebitNote.TotalAmount,
        //                Party_ID = creditDebitNote.Party_ID,
        //                PartyType = (creditDebitNote.PartyType == 1 ? SupplierL : (creditDebitNote.PartyType == 0) ? CustomerL : DealerL),
        //                CreditNote,
        //                Party_Name = creditDebitNote.Party_Name,
        //                iClaimList = claimMemberList,
        //                partsTotal = claimMemberList.Sum(i => i.GrossAmountCal),
        //                creditDebitNote.CompanyLogoName,
        //                creditDebitNote.IsPrintLogo,
        //                CompName = creditDebitNote.CompName,
        //                CompAddress = creditDebitNote.CompAddress,
        //                Branch = creditDebitNote.BranchName,
        //                RecordBranch_id = creditDebitNote.Branch_ID
        //            };

        //        }
        //        return new JsonResult(jsonResult);//updated by Kavitha
        //    }
        //    catch (Exception ex)
        //    {
        //        if (LogException == 1)
        //        {
        //            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

        //        }
        //        return null;
        //    }



        //}
        //#endregion






    }
    #region PST_CreditDebitNoteListsAndObjs vinay n 20/1/25
    public class PrintViewList
    {
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int CreditDebitNoteID { get; set; }
        public bool CreditNote { get; set; }
        public string GeneralCulture { get; set; }
        public string UserCulture { get; set; }
    }
    public class SelectParticularCreditDebitNoteList
    {
        public int UserLanguageID { get; set; }
        public string UserCulture { get; set; }
        public bool mode { get; set; }
        public int GeneralLanguageID { get; set; }
        public string GeneralCulture { get; set; }
        public bool CreditNote { get; set; }
        public int CreditDebitNoteID { get; set; }
        public int Company_ID { get; set; }
        public int Branch { get; set; }
    }


    public class SaveCreditDebitNoteList
    {
        public int ObjectID { get; set; }
        public int User_ID { get; set; }
        public string Data { get; set; }
        public bool CreditNote { get; set; }
        public string CreditAttachmentDelete { get; set; }
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public string AttachmentsData { get; set; }
        public string AttachmentData { get; set; }
    }


    public class SelAllClaimsList
    {
        public int Branch { get; set; }
        public int Company_ID { get; set; }
        public bool CreditNote { get; set; }
        public int DocumentType { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public bool IsDealer { get; set; }
        public bool OEM { get; set; }
        public int PartyID { get; set; }
    }
    public class SelectPartyDetailGridPST_CreditDebitNoteList
    {
        public string PartyName { get; set; }
        public string GeneralLanguageCode { get; set; }
        public int Company_ID { get; set; }
        public bool CreditNote { get; set; }
        public int Language_ID { get; set; }
        public bool Party_IsActive { get; set; }
        public string UserLanguageCode { get; set; }
    }


    public class GetAllCreditDebitList
    {
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public bool CreditNote { get; set; }
        public string UserCulture { get; set; }

        //Export
        public string filter { get; set; }
        public string advanceFilter { get; set; }
        public int exprtType { get; set; }
        public string sidx { get; set; }
        public string sord { get; set; }
    }
    public class SelCreditDebitNoteDetailList
    {
        public int UserLanguageID { get; set; }
        public int Language_ID { get; set; }
        public int CreditDebitNoteID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int Company_ID { get; set; }
    }
    public class GetPartyDetailsByPartyIDList
    {
        public int Company_ID { get; set; }
        public int Party_ID { get; set; }
        public bool CreditNote { get; set; }
        public int Language_ID { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
    }


    public class GetPartyDetailsList
    {
        public string Party_Name { get; set; }
        public int Company_ID { get; set; }
        public bool CreditNote { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int Language_ID { get; set; }
    }
    public class SelectFieldSearchPartyList
    {
        public string value { get; set; }
        public int Company_ID { get; set; }
        public bool CreditNote { get; set; }
        public int LanguageID { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public string UserCulture { get; set; }
    }
    #endregion
    #region PST_CreditDebitNoteListsAndObjsMasterClasses
    public partial class PST_CreditDebitNoteDetail
    {
        public int CreditDebitNoteDetail_ID { get; set; }
        public int CreditDebitNote_ID { get; set; }
        public int Transaction_ID { get; set; }
        public string TransactionNumber { get; set; }
        public decimal GrossAmount { get; set; }
        public System.DateTime TransactionDate { get; set; }

        public virtual PST_CreditDebitNoteHeader PST_CreditDebitNoteHeader { get; set; }
    }
    public partial class PST_CreditDebitNoteHeader
    {
        public PST_CreditDebitNoteHeader()
        {
            this.PST_CreditDebitNoteDetail = new HashSet<PST_CreditDebitNoteDetail>();
        }

        public int CreditDebitNote_ID { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public string CreditNoteOrDebitNote { get; set; }
        public string TransactionNumber { get; set; }
        public System.DateTime TransactionDate { get; set; }
        public byte PartyType { get; set; }
        public int Party_ID { get; set; }
        public byte DocumentType { get; set; }
        public Nullable<int> TaxStructure_ID { get; set; }
        public Nullable<decimal> TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public Nullable<int> DocumentNumber { get; set; }
        public Nullable<int> FinancialYear { get; set; }
        public Nullable<byte> AttachmentCount { get; set; }
        public string Remarks { get; set; }
        public Nullable<bool> IsDealer { get; set; }

        public virtual ICollection<PST_CreditDebitNoteDetail> PST_CreditDebitNoteDetail { get; set; }
    }
    public class ClaimMembers
    {
        public string Number
        {
            get;
            set;

        }
        public string edit
        {
            get;
            set;

        }
        public string NumberHidden
        {
            get;
            set;

        }
        public int ID
        {
            get;
            set;

        }
        public string Date
        {
            get;
            set;
        }
        public string GrossAmount
        {
            get;
            set;
        }
        public decimal GrossAmountCal
        {
            get;
            set;
        }
    }
    public class SRTCreditNoteHeader
    {
        public string PartyType { get; set; }
        public string IsDealer { get; set; }
        public string edit
        {
            get;
            set;
        }
        public int CreditDebitNote_ID
        {
            get;
            set;
        }
        public string TransactionNumber
        {
            get;
            set;
        }
        public string TransactionDate
        {
            get;
            set;
        }
        public string DocumentType
        {
            get;
            set;
        }
        public string TotalAmount
        {
            get;
            set;
        }
        public string Party_Name
        {
            get;
            set;

        }
        public string Attachmentcount
        {
            get;
            set;
        }

    }
    public partial class SRT_WarrantyClaimHeaderCN
    {
        public int WarrantyClaim_ID { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public string WarrantyClaimNumber { get; set; }
        public System.DateTime WarrantyClaimDate { get; set; }
        public Nullable<int> JobCard_ID { get; set; }
        public Nullable<int> ParentClaim_ID { get; set; }
        public Nullable<int> ServiceType_ID { get; set; }
        public int Party_ID { get; set; }
        public Nullable<int> Model_ID { get; set; }
        public Nullable<int> Brand_ID { get; set; }
        public Nullable<int> ProductType_ID { get; set; }
        public string SerialNumber { get; set; }
        public Nullable<int> Reading { get; set; }
        public bool IsUnderWarranty { get; set; }
        public bool IsComponent { get; set; }
        public Nullable<int> PrimarySegment_ID { get; set; }
        public Nullable<int> SecondarySegment_ID { get; set; }
        public int Supplier_ID { get; set; }
        public Nullable<System.DateTime> DateOfFailure { get; set; }
        public Nullable<System.DateTime> DateOfRepair { get; set; }
        public Nullable<int> CreditNote_ID { get; set; }
        public Nullable<int> FailureReading { get; set; }
        public Nullable<int> RepairReading { get; set; }
        public int DefectType_ID { get; set; }
        public int ClaimStatus_ID { get; set; }
        public string CustomerComplaint { get; set; }
        public string CauseOfFailure { get; set; }
        public string CorrectiveAction { get; set; }
        public Nullable<int> DocumentNumber { get; set; }
        public Nullable<int> FinancialYear { get; set; }
        public Nullable<decimal> TotalClaimedAmount { get; set; }
        public Nullable<decimal> TotalApprovedAmount { get; set; }
        public string CreditDebitNoteNumber { get; set; }
        public Nullable<int> CLAIMVERIFICATION_ID { get; set; }
        public Nullable<byte> AttachmentCount { get; set; }
        public string Remarks { get; set; }
        public Nullable<byte> JobSeqNumber { get; set; }
        public Nullable<int> ParentWarrantyClaim_ID { get; set; }
        public Nullable<int> Charge_To { get; set; }
        public Nullable<int> Charge_To_Type { get; set; }
        public Nullable<int> WarrantyType_ID { get; set; }
        public Nullable<int> FailCode_ID { get; set; }
        public Nullable<int> WorkFlow_ClaimStatus_ID { get; set; }
        public string SAPInvoiceNumber { get; set; }
        public Nullable<decimal> TotalClaimedAmount_PCP { get; set; }
        public Nullable<decimal> TotalApprovedAmount_PCP { get; set; }
        public Nullable<bool> IsBranchMobileRate { get; set; }
    }
    public partial class SRT_MandatoryClaimHeaderCN
    {
        public int MandatoryClaim_ID { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public string MandatoryClaimNumber { get; set; }
        public System.DateTime MandatoryClaimDate { get; set; }
        public decimal ClaimedAmount { get; set; }
        public Nullable<decimal> ApprovedAmount { get; set; }
        public Nullable<int> DocumentNumber { get; set; }
        public int FinancialYear { get; set; }
        public string Remarks { get; set; }
        public string CreditOrDebitNoteNumber { get; set; }
        public Nullable<int> CreditOrDebitNote_ID { get; set; }
        public Nullable<byte> Status { get; set; }
        public Nullable<byte> AttachmentCount { get; set; }
    }
    public partial class SRT_ServiceInvoiceReturnHeaderCN
    {
        public int ServiceInvoiceReturn_ID { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public string InvoiceReturnNumber { get; set; }
        public System.DateTime InvoiceReturnDate { get; set; }
        public int Invoice_Id { get; set; }
        public Nullable<bool> IsPartial { get; set; }
        public Nullable<bool> ReturnTax { get; set; }
        public decimal TotalReturnedAmount { get; set; }
        public Nullable<int> DocumentNumber { get; set; }
        public Nullable<int> FinancialYear { get; set; }
        public string Remarks { get; set; }
        public Nullable<int> CreditDebitNote_ID { get; set; }
        public Nullable<byte> AttachmentCount { get; set; }
        public Nullable<System.DateTime> TallyGeneratedDate { get; set; }
        public Nullable<bool> TallySentStatus { get; set; }
        public string AcknowledgementNumber { get; set; }
        public Nullable<System.DateTime> AcknowledgementDate { get; set; }
        public string InvoiceReferenceNumber { get; set; }
        public string SignedInvoice { get; set; }
        public string SignedQRCode { get; set; }
        public string IRNResponseStatus { get; set; }
        public Nullable<System.DateTime> IRNCancelDate { get; set; }
    }
    public partial class PRT_SalesInvoiceReturnCN
    {
        public int SalesInvoiceReturn_ID { get; set; }
        public string SalesInvoiceReturnNumber { get; set; }
        public System.DateTime SalesInvoiceReturnDate { get; set; }
        public bool IsReturnTax { get; set; }
        public int SalesInvoice_ID { get; set; }
        public string ReasonForReturn { get; set; }
        public bool IsPartial { get; set; }
        public Nullable<decimal> TotalInvoiceReturnAmount { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public Nullable<int> DocumentNumber { get; set; }
        public Nullable<int> FinancialYear { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public int ModifiedBy { get; set; }
        public Nullable<decimal> TotalAmount { get; set; }
        public Nullable<int> CreditDebitNote_ID { get; set; }
        public Nullable<decimal> DiscountPercentage { get; set; }
        public Nullable<decimal> DiscountAmount { get; set; }
        public Nullable<decimal> DiscountedAmount { get; set; }
        public Nullable<int> TaxStructure_ID { get; set; }
        public string TaxableOtherCharges { get; set; }
        public Nullable<decimal> TaxablePercentage { get; set; }
        public Nullable<decimal> TaxableOtherChargesAmount { get; set; }
        public Nullable<decimal> TaxOnTaxableOtherCharges { get; set; }
        public Nullable<decimal> TotalTaxableAmount { get; set; }
        public Nullable<decimal> TaxAmount { get; set; }
        public string NonTaxableOtherCharges { get; set; }
        public Nullable<decimal> NonTaxablePercentage { get; set; }
        public Nullable<decimal> NonTaxableOtherChargesAmount { get; set; }
        public Nullable<byte> AttachmentCount { get; set; }
        public string Remarks { get; set; }
        public Nullable<System.DateTime> TallyGeneratedDate { get; set; }
        public Nullable<bool> TallySentStatus { get; set; }
        public Nullable<decimal> RoundoffAmount { get; set; }
        public string AcknowledgementNumber { get; set; }
        public Nullable<System.DateTime> AcknowledgementDate { get; set; }
        public string InvoiceReferenceNumber { get; set; }
        public string SignedInvoice { get; set; }
        public string SignedQRCode { get; set; }
        public string IRNResponseStatus { get; set; }
        public Nullable<System.DateTime> IRNCancelDate { get; set; }

        public virtual PRT_SalesInvoiceRP PRT_SalesInvoice { get; set; }
    }
    public partial class PRT_SalesInvoiceRP
    {
        public PRT_SalesInvoiceRP()
        {
            this.PRT_SalesInvoiceReturn = new HashSet<PRT_SalesInvoiceReturnCN>();
        }

        public int SalesInvoice_ID { get; set; }
        public string SalesInvoiceNumber { get; set; }
        public System.DateTime SalesInvoiceDate { get; set; }
        public Nullable<int> InvoiceType_ID { get; set; }
        public Nullable<int> PartsOrder_ID { get; set; }
        public Nullable<int> Party_ID { get; set; }
        public Nullable<int> PackingList_ID { get; set; }
        public string PaymentTerms { get; set; }
        public Nullable<System.DateTime> PaymentDueDate { get; set; }
        public string TermsAndConditions { get; set; }
        public string Remarks { get; set; }
        public Nullable<decimal> TotalAmount { get; set; }
        public Nullable<decimal> DiscountPercentage { get; set; }
        public Nullable<decimal> DiscountAmount { get; set; }
        public Nullable<decimal> DiscountedAmount { get; set; }
        public Nullable<int> TaxStructure_ID { get; set; }
        public string TaxableOtherCharges { get; set; }
        public Nullable<decimal> TaxablePercentage { get; set; }
        public Nullable<decimal> TaxableOtherChargesAmount { get; set; }
        public Nullable<decimal> TaxOnTaxableOtherCharges { get; set; }
        public Nullable<decimal> TotalTaxableAmount { get; set; }
        public Nullable<decimal> TaxAmount { get; set; }
        public string NonTaxableOtherCharges { get; set; }
        public Nullable<decimal> NonTaxablePercentage { get; set; }
        public Nullable<decimal> NonTaxableOtherChargesAmount { get; set; }
        public Nullable<decimal> TotalInvoiceAmount { get; set; }
        public Nullable<int> Company_ID { get; set; }
        public Nullable<int> Branch_ID { get; set; }
        public Nullable<int> DocumentNumber { get; set; }
        public Nullable<int> FinancialYear { get; set; }
        public Nullable<int> ModifiedBy { get; set; }
        public Nullable<System.DateTime> ModifiedDate { get; set; }
        public Nullable<int> ConsigneeAddress_ID { get; set; }
        public Nullable<int> InvoiceAddress_ID { get; set; }
        public Nullable<decimal> TotalReceivedAmount { get; set; }
        public Nullable<bool> Status { get; set; }
        public Nullable<bool> IsFullReturn { get; set; }
        public Nullable<decimal> TotalReturnAmount { get; set; }
        public Nullable<bool> IsReInvoice { get; set; }
        public Nullable<bool> IsDirectInvoice { get; set; }
        public Nullable<bool> IsEligibleAccount { get; set; }
        public Nullable<System.DateTime> ExpectedDate { get; set; }
        public Nullable<int> CoreReceipt_ID { get; set; }
        public Nullable<byte> AttachmentCount { get; set; }
        public Nullable<bool> IsCashInvoice { get; set; }
        public Nullable<int> Model_ID { get; set; }
        public Nullable<int> Brand_ID { get; set; }
        public Nullable<int> ProductType_ID { get; set; }
        public Nullable<int> Product_ID { get; set; }
        public Nullable<int> PartyContactPerson_ID { get; set; }
        public Nullable<int> CampaignID { get; set; }
        public Nullable<bool> IsClaimed { get; set; }
        public Nullable<bool> IsItemLevel { get; set; }
        public Nullable<int> Warehouse_ID { get; set; }
        public Nullable<bool> IsDealer { get; set; }
        public Nullable<System.DateTime> TallyGeneratedDate { get; set; }
        public Nullable<bool> TallySentStatus { get; set; }
        public Nullable<decimal> RoundoffAmount { get; set; }
        public string AcknowledgementNumber { get; set; }
        public Nullable<System.DateTime> AcknowledgementDate { get; set; }
        public string InvoiceReferenceNumber { get; set; }
        public string SignedInvoice { get; set; }
        public string SignedQRCode { get; set; }
        public string IRNResponseStatus { get; set; }
        public Nullable<System.DateTime> EWayBillNumber { get; set; }
        public Nullable<System.DateTime> EWayBillDate { get; set; }
        public Nullable<System.DateTime> EWayBillValidTill { get; set; }
        public Nullable<System.DateTime> EWayBillResponseRemarks { get; set; }
        public Nullable<System.DateTime> EWayBillCancelDate { get; set; }
        public Nullable<System.DateTime> IRNCancelDate { get; set; }

        public virtual ICollection<PRT_SalesInvoiceReturnCN> PRT_SalesInvoiceReturn { get; set; }
    }
    public partial class PRT_GDRSettlementDetailsRP
    {
        public int GDRSettlementDetail_ID { get; set; }
        public int GDRSettlement_ID { get; set; }
        public Nullable<int> PurchaseInvoice_ID { get; set; }
        public int Parts_ID { get; set; }
        public decimal InvoicedQuantity { get; set; }
        public decimal ReceivedQuantity { get; set; }
        public Nullable<decimal> ShortQuantity { get; set; }
        public Nullable<decimal> ExcessQuantity { get; set; }
        public Nullable<decimal> DamagedQuantity { get; set; }
        public decimal ReceivedAmount { get; set; }
        public Nullable<decimal> ExcessAmount { get; set; }
        public Nullable<decimal> ShortAmount { get; set; }
        public Nullable<decimal> DamagedAmount { get; set; }
        public string Remarks { get; set; }
        public bool IsClosed { get; set; }
        public Nullable<System.DateTime> ClosedDate { get; set; }
        public Nullable<int> SettlementType_ID { get; set; }
        public Nullable<decimal> Closing_stock_bal { get; set; }
        public Nullable<decimal> Closing_WAC { get; set; }
        public Nullable<decimal> MRP { get; set; }

        public virtual PRT_GDRSettlementRP PRT_GDRSettlement { get; set; }
    }
    public partial class PRT_GDRSettlementRP
    {
        public PRT_GDRSettlementRP()
        {
            this.PRT_GDRSettlementDetails = new HashSet<PRT_GDRSettlementDetailsRP>();
        }

        public int GDRSettlement_ID { get; set; }
        public System.DateTime GDRSettlementDate { get; set; }
        public bool GRN_Type { get; set; }
        public Nullable<int> GRN_ID { get; set; }
        public Nullable<int> StockReceiptGRN_ID { get; set; }
        public bool IsAllGDRSettlementDone { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public int Updated_By { get; set; }
        public System.DateTime Updated_Date { get; set; }
        public string GDRNumber { get; set; }
        public int FinancialYear { get; set; }
        public Nullable<int> DocumentNumber { get; set; }
        public Nullable<byte> AttachmentCount { get; set; }
        public string Remarks { get; set; }
        public Nullable<int> CreditNote_ID { get; set; }
        public Nullable<int> DebitNote_ID { get; set; }

        public virtual ICollection<PRT_GDRSettlementDetailsRP> PRT_GDRSettlementDetails { get; set; }
    }
    public partial class GNM_BranchLocale
    {
        public int Branch_Locale_ID { get; set; }
        public int Branch_ID { get; set; }
        public int Language_ID { get; set; }
        public string Branch_Name { get; set; }
        public string Branch_Address { get; set; }
        public string Branch_Location { get; set; }
        public string Branch_ShortName { get; set; }

        public virtual GNM_Branch GNM_Branch { get; set; }
    }
    public partial class GNM_BranchTaxStructure
    {
        public int BranchTaxStructure_ID { get; set; }
        public int Branch_ID { get; set; }
        public int TaxStructure_ID { get; set; }

        public virtual GNM_Branch GNM_Branch { get; set; }
    }
    public partial class GNM_EmployeeBranch
    {
        public int EmployeeBranch_ID { get; set; }
        public int CompanyEmployee_ID { get; set; }
        public int Branch_ID { get; set; }
        public Nullable<bool> IsDefault { get; set; }

        public virtual GNM_Branch GNM_Branch { get; set; }
        public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
    }
    public partial class GNM_EmployeeTrainingDetails
    {
        public GNM_EmployeeTrainingDetails()
        {
            this.GNM_EmployeeTrainingLocaleDetails = new HashSet<GNM_EmployeeTrainingLocaleDetails>();
        }

        public int TrainingDetails_ID { get; set; }
        public Nullable<int> Employee_ID { get; set; }
        public Nullable<int> Brand_ID { get; set; }
        public Nullable<int> ProductType_ID { get; set; }
        public string TrainingName { get; set; }
        public Nullable<int> Level_ID { get; set; }
        public Nullable<System.DateTime> FromDate { get; set; }
        public Nullable<System.DateTime> ToDate { get; set; }
        public string Evaluation { get; set; }
        public string Faculty { get; set; }
        public Nullable<int> Status { get; set; }

        public virtual ICollection<GNM_EmployeeTrainingLocaleDetails> GNM_EmployeeTrainingLocaleDetails { get; set; }
        public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
    }
    public partial class GNM_EmployeeTrainingLocaleDetails
    {
        public int TrainingLocaleDetails_ID { get; set; }
        public Nullable<int> Training_ID { get; set; }
        public string TrainingLocale_Name { get; set; }
        public string TrainingLocale_Evaluation { get; set; }
        public string TrainingLocale_Faculty { get; set; }
        public int Language_ID { get; set; }

        public virtual GNM_EmployeeTrainingDetails GNM_EmployeeTrainingDetails { get; set; }
    }
    public partial class GNM_CompanyEmployeeLocale
    {
        public int Company_Employee_Locale_ID { get; set; }
        public int Company_Employee_ID { get; set; }
        public int Language_ID { get; set; }
        public string Company_Employee_Name { get; set; }
        public string Company_Employee_Address { get; set; }
        public string Company_Employee_Location { get; set; }

        public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
    }
    public partial class GNM_CompanyEmployeeSkillset
    {
        public int Employee_Skillset_ID { get; set; }
        public int CompnayEmployee_ID { get; set; }
        public int Skillset_ID { get; set; }
        public int Employee_Skillset_Rating { get; set; }
        public Nullable<int> Level_ID { get; set; }

        public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
    }
    public partial class GNM_EmployeeETODetails
    {
        public int EmployeeETO_ID { get; set; }
        public int Employee_ID { get; set; }
        public Nullable<int> Year { get; set; }
        public Nullable<int> AvailableHours { get; set; }
        public Nullable<int> UsedHours { get; set; }
        public Nullable<int> PendingHours { get; set; }
        public Nullable<int> CarryoverHours { get; set; }
        public Nullable<int> UsedCarryoverHours { get; set; }
        public Nullable<int> BankCode_ID { get; set; }
        public Nullable<int> UsedCurrentYearETOHoursJanToMar { get; set; }
        public Nullable<System.DateTime> Validity { get; set; }
        public Nullable<bool> Isactive { get; set; }

        public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
    }
    public partial class GNM_EmployeeETOLogDetails
    {
        public int EmployeeETOLog_ID { get; set; }
        public int Employee_ID { get; set; }
        public int ModifiedBy { get; set; }
        public Nullable<System.DateTime> ModifiedDate { get; set; }
        public Nullable<bool> IsAdded { get; set; }

        public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
    }
    public partial class GNM_EmployeedownLines
    {
        public int EmployeeDownLine_ID { get; set; }
        public int CompanyEmployee_ID { get; set; }
        public int Branch_ID { get; set; }
        public int ManagerEmployee_ID { get; set; }

        public virtual GNM_Branch GNM_Branch { get; set; }
        public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
        public virtual GNM_CompanyEmployee GNM_CompanyEmployee1 { get; set; }
    }
    public partial class GNM_CompanyEmployeeQUALIFICATION
    {
        public int EMPLOYEEQUALIFICATION_ID { get; set; }
        public int Company_Employee_ID { get; set; }
        public string QUALIFICATION { get; set; }
        public string COURSE { get; set; }
        public string INSTITUTENAME { get; set; }
        public Nullable<int> YOP { get; set; }
        public string PASSPERCENTAGE { get; set; }

        public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
    }
    public partial class GNM_CompanyEmployeeEXPERIENCE
    {
        public int GNM_CompanyEmployeeWORKING_ID { get; set; }
        public int Company_Employee_ID { get; set; }
        public string COMPANY_NAME { get; set; }
        public Nullable<int> Department_ID { get; set; }
        public Nullable<int> DESIGNATION_ID { get; set; }
        public Nullable<int> EXPERIENCE { get; set; }

        public virtual GNM_CompanyEmployee GNM_CompanyEmployee { get; set; }
    }
    public partial class GNM_CompanyEmployee
    {
        public GNM_CompanyEmployee()
        {
            this.GNM_EmployeeTrainingDetails = new HashSet<GNM_EmployeeTrainingDetails>();
            this.GNM_CompanyEmployeeLocale = new HashSet<GNM_CompanyEmployeeLocale>();
            this.GNM_CompanyEmployeeSkillset = new HashSet<GNM_CompanyEmployeeSkillset>();
            this.GNM_EmployeeBranch = new HashSet<GNM_EmployeeBranch>();
            this.GNM_EmployeeETODetails = new HashSet<GNM_EmployeeETODetails>();
            this.GNM_EmployeeETOLogDetails = new HashSet<GNM_EmployeeETOLogDetails>();
            this.GNM_EmployeedownLines = new HashSet<GNM_EmployeedownLines>();
            this.GNM_EmployeedownLines1 = new HashSet<GNM_EmployeedownLines>();
            this.GNM_CompanyEmployeeQUALIFICATION = new HashSet<GNM_CompanyEmployeeQUALIFICATION>();
            this.GNM_CompanyEmployeeEXPERIENCE = new HashSet<GNM_CompanyEmployeeEXPERIENCE>();
        }

        public int Company_Employee_ID { get; set; }
        public string Employee_ID { get; set; }
        public string Company_Employee_Name { get; set; }
        public int Company_ID { get; set; }
        public int Country_ID { get; set; }
        public int State_ID { get; set; }
        public string Company_Employee_MobileNumber { get; set; }
        public string Company_Employee_Landline_Number { get; set; }
        public string Company_Employee_ZipCode { get; set; }
        public Nullable<System.DateTime> Company_Employee_ActiveFrom { get; set; }
        public Nullable<System.DateTime> Company_Employee_ValidateUpTo { get; set; }
        public bool Company_Employee_Active { get; set; }
        public Nullable<int> Company_Employee_Manager_ID { get; set; }
        public string Company_Employee_Address { get; set; }
        public string Company_Employee_Location { get; set; }
        public string Company_Employee_Email { get; set; }
        public int Company_Employee_Department_ID { get; set; }
        public int Company_Employee_Designation_ID { get; set; }
        public int ModifiedBy { get; set; }
        public Nullable<System.DateTime> ModifiedDate { get; set; }
        public Nullable<decimal> HourlyRate { get; set; }
        public Nullable<int> Region_ID { get; set; }
        public Nullable<bool> IsEligibleForOT { get; set; }
        public Nullable<byte> ExemptionHours { get; set; }
        public Nullable<bool> IsOnJob { get; set; }
        public Nullable<int> JobID { get; set; }
        public Nullable<bool> IsEligibleForIncentive { get; set; }
        public Nullable<bool> IsUnderProductiveMonitoring { get; set; }
        public Nullable<bool> IsConsideredForPayroll { get; set; }
        public Nullable<int> Bay_ID { get; set; }
        public string PHOTONAME { get; set; }
        public string EmployeeImagePath { get; set; }
        public Nullable<System.DateTime> ClockedOnJobStartTime { get; set; }

        public virtual GNM_Company GNM_Company { get; set; }
        public virtual ICollection<GNM_EmployeeTrainingDetails> GNM_EmployeeTrainingDetails { get; set; }
        public virtual ICollection<GNM_CompanyEmployeeLocale> GNM_CompanyEmployeeLocale { get; set; }
        public virtual ICollection<GNM_CompanyEmployeeSkillset> GNM_CompanyEmployeeSkillset { get; set; }
        public virtual ICollection<GNM_EmployeeBranch> GNM_EmployeeBranch { get; set; }
        public virtual ICollection<GNM_EmployeeETODetails> GNM_EmployeeETODetails { get; set; }
        public virtual ICollection<GNM_EmployeeETOLogDetails> GNM_EmployeeETOLogDetails { get; set; }
        public virtual ICollection<GNM_EmployeedownLines> GNM_EmployeedownLines { get; set; }
        public virtual ICollection<GNM_EmployeedownLines> GNM_EmployeedownLines1 { get; set; }
        public virtual ICollection<GNM_CompanyEmployeeQUALIFICATION> GNM_CompanyEmployeeQUALIFICATION { get; set; }
        public virtual ICollection<GNM_CompanyEmployeeEXPERIENCE> GNM_CompanyEmployeeEXPERIENCE { get; set; }
    }
    public partial class GNM_HEADERFOOTERPRINTLOCALE
    {
        public int HEADERFOOTERPRINTLOCALE_ID { get; set; }
        public int HEADERFOOTERPRINT_ID { get; set; }
        public int LANGUAGE_ID { get; set; }
        public string HEADERTEMPLATE { get; set; }
        public string FOOTERTEMPLATE { get; set; }

        public virtual GNM_HEADERFOOTERPRINT GNM_HEADERFOOTERPRINT { get; set; }
    }
    public partial class GNM_HEADERFOOTERPRINT
    {
        public GNM_HEADERFOOTERPRINT()
        {
            this.GNM_HEADERFOOTERPRINTLOCALE = new HashSet<GNM_HEADERFOOTERPRINTLOCALE>();
        }

        public int HEADERFOOTERPRINT_ID { get; set; }
        public Nullable<int> COMPANY_ID { get; set; }
        public string HEADERTEMPLATE { get; set; }
        public string FOOTERTEMPLATE { get; set; }
        public Nullable<int> Branch_ID { get; set; }

        public virtual GNM_Branch GNM_Branch { get; set; }
        public virtual GNM_Company GNM_Company { get; set; }
        public virtual ICollection<GNM_HEADERFOOTERPRINTLOCALE> GNM_HEADERFOOTERPRINTLOCALE { get; set; }
    }
    public partial class GNM_Company_Company_Relation
    {
        public int Company_Relationship_ID { get; set; }
        public int ManufacturerCompany_ID { get; set; }
        public int DealerCompany_ID { get; set; }

        public virtual GNM_Company GNM_Company { get; set; }
        public virtual GNM_Company GNM_Company1 { get; set; }
    }
    public partial class GNM_CompanyBrands
    {
        public int Company_Brand_ID { get; set; }
        public int Company_ID { get; set; }
        public int Brand_ID { get; set; }

        public virtual GNM_Company GNM_Company { get; set; }
    }
    public partial class GNM_CompanyLocale
    {
        public int Company_Locale_ID { get; set; }
        public int Company_ID { get; set; }
        public string Company_Name { get; set; }
        public string Company_ShortName { get; set; }
        public string Company_Address { get; set; }
        public int Language_ID { get; set; }

        public virtual GNM_Company GNM_Company { get; set; }
    }
    public partial class GNM_CompanyFinancialYear
    {
        public int Company_FinancialYear_ID { get; set; }
        public Nullable<int> Company_ID { get; set; }
        public int Company_FinancialYear { get; set; }
        public System.DateTime Company_FinancialYear_FromDate { get; set; }
        public System.DateTime Company_FinancialYear_ToDate { get; set; }

        public virtual GNM_Company GNM_Company { get; set; }
    }
    public partial class GNM_TERMSANDCONDITIONS
    {
        public GNM_TERMSANDCONDITIONS()
        {
            this.GNM_TERMSANDCONDITIONSLOCALE = new HashSet<GNM_TERMSANDCONDITIONSLOCALE>();
        }

        public int TERMSANDCONDITIONS_ID { get; set; }
        public Nullable<int> COMPANY_ID { get; set; }
        public Nullable<int> Branch_ID { get; set; }
        public Nullable<int> TERMS_ID { get; set; }
        public Nullable<int> OBJECT_ID { get; set; }
        public string TERMSANDCONDITIONS { get; set; }
        public bool ISDEFAULT { get; set; }

        public virtual GNM_Branch GNM_Branch { get; set; }
        public virtual GNM_Company GNM_Company { get; set; }
        public virtual ICollection<GNM_TERMSANDCONDITIONSLOCALE> GNM_TERMSANDCONDITIONSLOCALE { get; set; }
    }
    public partial class GNM_TERMSANDCONDITIONSLOCALE
    {
        public int TERMSANDCONDITIONSLOCALE_ID { get; set; }
        public int TERMSANDCONDITIONS_ID { get; set; }
        public string TERMSANDCONDITIONS { get; set; }

        public virtual GNM_TERMSANDCONDITIONS GNM_TERMSANDCONDITIONS { get; set; }
    }
    public partial class GNM_HourlyRate
    {
        public int HourlyRate_ID { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public decimal HourlyRate { get; set; }
        public System.DateTime EffectiveDate { get; set; }
        public Nullable<int> Modifiedby { get; set; }
        public Nullable<System.DateTime> ModifiedDate { get; set; }
        public Nullable<decimal> OvertimeHourlyRate { get; set; }
        public Nullable<decimal> DoubleTimeHourlyRate { get; set; }
        public Nullable<decimal> BranchMobileRate { get; set; }

        public virtual GNM_Branch GNM_Branch { get; set; }
        public virtual GNM_Company GNM_Company { get; set; }
    }
    public partial class GNM_Company
    {
        public GNM_Company()
        {
            this.GNM_Company_Company_Relation = new HashSet<GNM_Company_Company_Relation>();
            this.GNM_Company_Company_Relation1 = new HashSet<GNM_Company_Company_Relation>();
            this.GNM_CompanyBrands = new HashSet<GNM_CompanyBrands>();
            this.GNM_CompanyLocale = new HashSet<GNM_CompanyLocale>();
            this.GNM_Branch = new HashSet<GNM_Branch>();
            this.GNM_CompanyFinancialYear = new HashSet<GNM_CompanyFinancialYear>();
            this.GNM_HEADERFOOTERPRINT = new HashSet<GNM_HEADERFOOTERPRINT>();
            this.GNM_TERMSANDCONDITIONS = new HashSet<GNM_TERMSANDCONDITIONS>();
            this.GNM_HourlyRate = new HashSet<GNM_HourlyRate>();
            this.GNM_CompanyEmployee = new HashSet<GNM_CompanyEmployee>();
        }

        public int Company_ID { get; set; }
        public string Company_Name { get; set; }
        public string Company_ShortName { get; set; }
        public int Currency_ID { get; set; }
        public string Company_Address { get; set; }
        public string Company_Type { get; set; }
        public bool Company_Active { get; set; }
        public string Company_LogoName { get; set; }
        public Nullable<int> Company_Parent_ID { get; set; }
        public string Remarks { get; set; }
        public byte DefaultGridSize { get; set; }
        public Nullable<decimal> JobCardCushionHours { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public Nullable<int> CompanyTheme_ID { get; set; }
        public Nullable<int> QuotationValidity { get; set; }
        public string CompanyFont { get; set; }
        public Nullable<decimal> InventoryCarryingFactoy_Percentage { get; set; }
        public Nullable<int> OrderingCost { get; set; }

        public virtual ICollection<GNM_Company_Company_Relation> GNM_Company_Company_Relation { get; set; }
        public virtual ICollection<GNM_Company_Company_Relation> GNM_Company_Company_Relation1 { get; set; }
        public virtual ICollection<GNM_CompanyBrands> GNM_CompanyBrands { get; set; }
        public virtual ICollection<GNM_CompanyLocale> GNM_CompanyLocale { get; set; }
        public virtual ICollection<GNM_Branch> GNM_Branch { get; set; }
        public virtual ICollection<GNM_CompanyFinancialYear> GNM_CompanyFinancialYear { get; set; }
        public virtual ICollection<GNM_HEADERFOOTERPRINT> GNM_HEADERFOOTERPRINT { get; set; }
        public virtual ICollection<GNM_TERMSANDCONDITIONS> GNM_TERMSANDCONDITIONS { get; set; }
        public virtual ICollection<GNM_HourlyRate> GNM_HourlyRate { get; set; }
        public virtual ICollection<GNM_CompanyEmployee> GNM_CompanyEmployee { get; set; }
    }
    public partial class GNM_BranchTaxCodes
    {
        public int BranchTaxCode_ID { get; set; }
        public int Branch_ID { get; set; }
        public string BranchTaxCodeName { get; set; }
        public string BranchTaxCode { get; set; }
        public string GSTINPortalUserName { get; set; }
        public string GSTINPortalPassword { get; set; }

        public virtual GNM_Branch GNM_Branch { get; set; }
    }
    public partial class GNM_Branch
    {
        public GNM_Branch()
        {
            this.GNM_BranchLocale = new HashSet<GNM_BranchLocale>();
            this.GNM_BranchTaxStructure = new HashSet<GNM_BranchTaxStructure>();
            this.GNM_EmployeeBranch = new HashSet<GNM_EmployeeBranch>();
            this.GNM_HEADERFOOTERPRINT = new HashSet<GNM_HEADERFOOTERPRINT>();
            this.GNM_TERMSANDCONDITIONS = new HashSet<GNM_TERMSANDCONDITIONS>();
            this.GNM_HourlyRate = new HashSet<GNM_HourlyRate>();
            this.GNM_EmployeedownLines = new HashSet<GNM_EmployeedownLines>();
            this.GNM_BranchTaxCodes = new HashSet<GNM_BranchTaxCodes>();
        }

        public int Branch_ID { get; set; }
        public int Company_ID { get; set; }
        public string Branch_Name { get; set; }
        public string Branch_ShortName { get; set; }
        public string Branch_ZipCode { get; set; }
        public int Country_ID { get; set; }
        public int State_ID { get; set; }
        public string Branch_Phone { get; set; }
        public string Branch_Fax { get; set; }
        public bool Branch_HeadOffice { get; set; }
        public bool Branch_Active { get; set; }
        public string Branch_Address { get; set; }
        public string Branch_Location { get; set; }
        public string Branch_Email { get; set; }
        public string Branch_Mobile { get; set; }
        public Nullable<bool> Branch_External { get; set; }
        public Nullable<int> TimeZoneID { get; set; }
        public Nullable<int> Region_ID { get; set; }
        public Nullable<int> Currency_ID { get; set; }
        public Nullable<int> LanguageID { get; set; }
        public Nullable<byte> IsOverTimeDWM { get; set; }
        public Nullable<decimal> Yearly_Sales_Target { get; set; }
        public Nullable<decimal> Rework_Target { get; set; }
        public Nullable<decimal> Cust_Satisfaction_Target { get; set; }
        public Nullable<decimal> RO_with_Rework_Target { get; set; }
        public Nullable<decimal> RO_with_Cust_Satisfaction_Target { get; set; }
        public Nullable<int> DueDays { get; set; }
        public Nullable<int> PayrollSystem_ID { get; set; }
        public Nullable<int> MaxCarryOverHours { get; set; }
        public Nullable<System.DateTime> ConsumedCarryOverByDate { get; set; }
        public Nullable<bool> IsHourlyRateBranchWise { get; set; }
        public Nullable<int> PayrollFileType_ID { get; set; }
        public Nullable<decimal> Yearly_Parts_Target { get; set; }
        public Nullable<decimal> Variance_Percentage { get; set; }
        public Nullable<decimal> Variance_Value { get; set; }
        public Nullable<int> ETOExtensionHours { get; set; }
        public Nullable<int> ETOMultiples { get; set; }
        public Nullable<decimal> BilledVsActualVariance_Percentage { get; set; }
        public Nullable<byte> TypeofPayroll { get; set; }
        public Nullable<decimal> LessVariance_Percentage { get; set; }
        public Nullable<decimal> LessVariance_Value { get; set; }
        public Nullable<decimal> IIMoreVariance_Percentage { get; set; }
        public Nullable<decimal> IIMoreVariance_Value { get; set; }
        public Nullable<decimal> IILessVariance_Percentage { get; set; }
        public Nullable<decimal> IILessVariance_Value { get; set; }
        public Nullable<int> WorkingMinutes { get; set; }

        public virtual GNM_Company GNM_Company { get; set; }
        public virtual ICollection<GNM_BranchLocale> GNM_BranchLocale { get; set; }
        public virtual ICollection<GNM_BranchTaxStructure> GNM_BranchTaxStructure { get; set; }
        public virtual ICollection<GNM_EmployeeBranch> GNM_EmployeeBranch { get; set; }
        public virtual ICollection<GNM_HEADERFOOTERPRINT> GNM_HEADERFOOTERPRINT { get; set; }
        public virtual ICollection<GNM_TERMSANDCONDITIONS> GNM_TERMSANDCONDITIONS { get; set; }
        public virtual ICollection<GNM_HourlyRate> GNM_HourlyRate { get; set; }
        public virtual ICollection<GNM_EmployeedownLines> GNM_EmployeedownLines { get; set; }
        public virtual ICollection<GNM_BranchTaxCodes> GNM_BranchTaxCodes { get; set; }
    }
    public partial class SRT_ServiceInvoiceHeaderRP
    {
        public int ServiceInvoice_ID { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public string ServiceInvoiceNumber { get; set; }
        public System.DateTime ServiceInvoiceDate { get; set; }
        public int JobCard_ID { get; set; }
        public string JobCardNumber { get; set; }
        public int InvoiceType_ID { get; set; }
        public int Party_ID { get; set; }
        public int PartyInvoiceAddress_ID { get; set; }
        public System.DateTime PaymentDueDate { get; set; }
        public int Model_ID { get; set; }
        public int Brand_ID { get; set; }
        public int ProductType_ID { get; set; }
        public string SerialNumber { get; set; }
        public int Reading { get; set; }
        public Nullable<bool> IsCSA { get; set; }
        public int Terms_ID { get; set; }
        public string TermsandCondition { get; set; }
        public string PaymentTerms { get; set; }
        public decimal TotalInvoiceAmount { get; set; }
        public decimal ReceivedAmount { get; set; }
        public decimal TotalReturnedAmount { get; set; }
        public Nullable<int> DocumentNumber { get; set; }
        public Nullable<int> FinancialYear { get; set; }
        public int UpdatedBy { get; set; }
        public System.DateTime UpdatedDate { get; set; }
        public bool Status { get; set; }
        public Nullable<bool> IsReinvoice { get; set; }
        public Nullable<byte> AttachmentCount { get; set; }
        public string Remarks { get; set; }
        public bool IsCashInvoice { get; set; }
        public Nullable<bool> IsClaimed { get; set; }
        public Nullable<int> ParentServiceInvoice_ID { get; set; }
        public Nullable<int> JobSeqNumber { get; set; }
        public string CustomerComplaint { get; set; }
        public string CauseOfFailure { get; set; }
        public string CorrectiveAction { get; set; }
        public Nullable<int> ContractorID { get; set; }
        public Nullable<int> ContractorContactPerson_ID { get; set; }
        public bool IsBillTocustomer { get; set; }
        public string PONumber { get; set; }
        public Nullable<decimal> Final_Subtotal { get; set; }
        public Nullable<decimal> Environmental_Charges { get; set; }
        public Nullable<decimal> Shop_Supplies { get; set; }
        public Nullable<decimal> Battery_Fees { get; set; }
        public Nullable<decimal> Tires_Fees { get; set; }
        public Nullable<decimal> Battery_Tax { get; set; }
        public Nullable<decimal> Tires_Tax { get; set; }
        public Nullable<decimal> State_Tax { get; set; }
        public Nullable<decimal> City_Tax { get; set; }
        public Nullable<decimal> DistrictOther_Tax { get; set; }
        public Nullable<decimal> FinalInvoice_Amount { get; set; }
        public Nullable<decimal> Total_FSES { get; set; }
        public Nullable<int> Currency_ID { get; set; }
        public Nullable<decimal> NET_PRICE { get; set; }
        public Nullable<bool> IsTaxReceivedFromSAP { get; set; }
        public string SAPInvoiceNumber { get; set; }
        public Nullable<decimal> USCADExchangeRate { get; set; }
        public Nullable<System.DateTime> SAPInvoiceDate { get; set; }
        public Nullable<decimal> DraftInvoiceAmount { get; set; }
        public Nullable<bool> IsMailsent { get; set; }
        public Nullable<System.DateTime> TallyGeneratedDate { get; set; }
        public Nullable<bool> TallySentStatus { get; set; }
        public string AcknowledgementNumber { get; set; }
        public Nullable<System.DateTime> AcknowledgementDate { get; set; }
        public string InvoiceReferenceNumber { get; set; }
        public string SignedInvoice { get; set; }
        public string SignedQRCode { get; set; }
        public string IRNResponseStatus { get; set; }
        public Nullable<System.DateTime> IRNCancelDate { get; set; }
    }
    public class CreditDebitNote
    {
        public int CreditDebitNote_ID { get; set; }
        public int TaxStructure_ID { get; set; }
        public string TransactionNumber { get; set; }
        public DateTime TransactionDate { get; set; }
        public int DocumentType { get; set; }
        public string TaxStructure_Name { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public string Remarks { get; set; }
        public int Party_ID { get; set; }
        public int PartyType { get; set; }
        public string Party_Name { get; set; }
        public string CompanyLogoName { get; set; }
        public bool IsPrintLogo { get; set; }
        public string CompName { get; set; }
        public string CompAddress { get; set; }
        public string BranchName { get; set; }
        public int Branch_ID { get; set; }
        public List<ClaimMember> ClaimMembers { get; set; } = new List<ClaimMember>();
    }

    public class ClaimMember
    {
        public string Number { get; set; }
        public string Date { get; set; }
        public string GrossAmount { get; set; }
        public decimal GrossAmountCal { get; set; }
    }
    #endregion
}
