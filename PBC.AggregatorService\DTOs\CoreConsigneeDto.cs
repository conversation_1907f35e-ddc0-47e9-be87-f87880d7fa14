namespace PBC.AggregatorService.DTOs
{
    #region ::: LoadBranchDropdown :::
    public class LoadBranchDropdownList
    {
        public int Company_ID { get; set; }
        public string? Branch { get; set; }
    }
    #endregion

    #region ::: Select :::
    public class GetAllconsigneeList
    {
        public int Branch_ID { get; set; }
        public int LanguageID { get; set; }
        public int UserLanguageID { get; set; }
        public string? GeneralCulture { get; set; }
        public string? UserCulture { get; set; }
        public string? BranchName { get; set; }
        public int Company_ID { get; set; }
        public int exprtType { get; set; }
    }

    public class SelectConsigneeRequest
    {
        public GetAllconsigneeList SelectConsigneeObj { get; set; } = new();
        public string Sidx { get; set; } = string.Empty;
        public int Rows { get; set; }
        public int Page { get; set; }
        public string Sord { get; set; } = string.Empty;
        public bool Search { get; set; }
        public long Nd { get; set; }
        public string Filters { get; set; } = string.Empty;
        public bool Advnce { get; set; }
        public string AdvanceFilter { get; set; } = string.Empty;
    }
    #endregion

    #region ::: Save :::
    public class SaveList
    {
        public string? data { get; set; }
        public int Company_ID { get; set; }
        public string? Branch { get; set; }
        public int MenuID { get; set; }
        public DateTime LoggedINDateTime { get; set; }
        public string? GeneralCulture { get; set; }
    }
    #endregion

    #region ::: CheckConsignee :::
    public class CheckConsigneeList
    {
        public int BranchID { get; set; }
        public string? ConsigneeLocation { get; set; }
        public int ConsigneeID { get; set; }
        public int UserLanguageID { get; set; }
    }
    #endregion

    #region ::: CheckConsigneeAddress :::
    public class CheckConsigneeAddressList
    {
        public int BranchID { get; set; }
        public string? ConsigneeAddress { get; set; }
        public int ConsigneeID { get; set; }
        public int UserLanguageID { get; set; }
    }
    #endregion

    #region ::: SelectParticularConsignee :::
    public class SelectParticularConsigneeList
    {
        public int ConsigneeID { get; set; }
        public int UserLanguageID { get; set; }
    }
    #endregion

    #region ::: Delete :::
    public class DeleteList
    {
        public string? key { get; set; }
        public int Company_ID { get; set; }
        public string? Branch { get; set; }
        public int MenuID { get; set; }
        public DateTime LoggedINDateTime { get; set; }
        public string? GeneralCulture { get; set; }
    }
    #endregion

    #region ::: UpdateLocale :::
    public class UpdateLocaleList
    {
        public string? data { get; set; }
        public int Company_ID { get; set; }
        public string? Branch { get; set; }
        public int MenuID { get; set; }
        public DateTime LoggedINDateTime { get; set; }
        public string? GeneralCulture { get; set; }
    }
    #endregion

    #region ::: CheckConsigneeLocale :::
    public class CheckConsigneeLocaleList
    {
        public int BranchID { get; set; }
        public string? ConsigneeLocation { get; set; }
        public int ConsigneeLocaleID { get; set; }
        public int UserLanguageID { get; set; }
    }
    #endregion

    #region ::: CheckConsigneeAddressLocale :::
    public class CheckConsigneeAddressLocaleList
    {
        public int BranchID { get; set; }
        public string? ConsigneeAddress { get; set; }
        public int ConsigneeLocaleID { get; set; }
        public int UserLanguageID { get; set; }
    }
    #endregion

    #region ::: CheckWareHouse :::
    public class CheckWareHouseList
    {
        public int Company_ID { get; set; }
        public int WareHouse_ID { get; set; }
        public int ConsigneeID { get; set; }
    }
    #endregion

    #region ::: Export :::
    public class ExportConsigneeRequest
    {
        public GetAllconsigneeList ExportObj { get; set; } = new();
        public string Filter { get; set; } = string.Empty;
        public string AdvanceFilter { get; set; } = string.Empty;
        public string Sidx { get; set; } = string.Empty;
        public string Sord { get; set; } = string.Empty;
    }
    #endregion
}
