﻿/// <summary>
/// To create role access objectand store it in session
/// </summary>



using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using WorkFlow.Models;
using System.Configuration;
using System.Net;
using LS = LogSheetExporter;
using WorkFlow.Controllers;
using System.Text;
using WorkFlow.Utilities;
using WorkFlow.Models;

namespace WorkFlow.Controllers
{
    public class CoreWFMainController : Controller
    {
        //
        // GET: /Main/
        WorkFlowEntity wrkFlowEnt = null;
        GenEntities genEnt = null;

        int userid = 0;
        int UserID = 0;
        int ModuleID = 0;
        List<int> myIntArr = new List<int>();
        List<LoginProperties> Current = null;
        Utilities.Utilities uti = new Utilities.Utilities();
        static string AppPath = string.Empty;
        dynamic dummy = null;
      

        /// <summary>
        /// To create role access objectand store it in session.
        /// </summary>
        /// <returns>...</returns>        
        public ActionResult CoreMainView()
        {
            Session["IsNewSession"] = null;          

            return View("~/Views/Core/CoreMainView.cshtml");
        }
               

        public ActionResult SelAllRecentMenuActivities()
        {
            dynamic dummy = null;
            var x = dummy;
            //try
            //{               
            //    genEnt = new GenEntities("AMMS");

            //    WF_User User = (WF_User)Session["UserDetails"];
            //    int userID = User.User_ID;

            //    string query = "select top (5) * from GNM_ActivityLog where User_ID=" + userID + "order by ActivityLog_Count desc";

            //    List<GNM_ActivityLog> menuActivityList = genClient.Database.SqlQuery(typeof(GNM_ActivityLog), query).Cast<GNM_ActivityLog>().ToList();

            //    x = from a in menuActivityList
            //            join b in genClient.GNM_Menu on a.Object_ID equals b.Menu_ID
            //            select new
            //            {
            //                b.Menu_ID,
            //                b.Menu_Description,
            //                b.Menu_Path
            //            };
               return Json(x, JsonRequestBehavior.AllowGet);
            //}
            //catch (Exception ex)
            //{
            //    return Json(x, JsonRequestBehavior.AllowGet);
            //}
        }
    }

    public class LoginProperties
    {
      public  int Module_ID{get;set;}
      public int Parentmenu_ID { get; set; }
      public int Object_ID { get; set; }
      public int Menu_ID { get; set; }
      public string Menu_Description { get; set; }
      public string Menu_Path { get; set; }
      public byte Menu_SortOrder { get; set; }
      public bool Menu_IsActive { get; set; }
    }

    public class ACLProperties
    {
        public int Object_ID { get; set; }
        public int RoleObject_Create { get; set; }
        public int RoleObject_Read { get; set; }
        public int RoleObject_Update { get; set; }
        public int RoleObject_Delete { get; set; }
        public int RoleObject_Print { get; set; }
        public int RoleObject_Export { get; set; }
        public int RoleObject_Import { get; set; }
    }

    public class TopModelObjects
    {

        public string Model
        {
            get;
            set;
        }
        public int Count
        {
            get;
            set;
        }
    }

    public class ServiceRequestCount
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public string Type { get; set; }
        public int Count { get; set; }
        public string Date { get; set; }
        public int StatusID { get; set; }
        public List<int> StatusIDs { get; set; }
        public int StatusCount { get; set; }
        public string MonthName { get; set; }
        public string StatusName { get; set; }
        public int CompletedCount { get; set; }
        public int PendingCount { get; set; }
        public int InProgressCount { get; set; }
        public int OnHoldCount { get; set; }
        public int ClosedCount { get; set; }
        public double TimeDiff { get; set; }
        public string AvgResolution { get; set; }
    }
}
