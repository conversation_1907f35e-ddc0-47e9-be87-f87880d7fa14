﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;



namespace SharedAPIClassLibrary_AMERP
{
    public class CoreObjectMasterServices
    {
        #region ::: To select all objects:::
        /// <summary>
        /// To select all objects
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult Select(SelectMovementtypList SelectObj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string Query, string connString, int LogException)
        {
            dynamic dummy = null;
            var jsonData = dummy;
            int count = 0;
            int total = 0;
            IQueryable<ObjectClass> gnmObj = SelectList(SelectObj, sidx, rows, page, sord, _search, nd, filters, advnce, Query, connString, LogException);
            try
            {
                count = gnmObj.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                jsonData = new
                {
                    TotalPages = total,
                    PageNo = page,
                    RecordCount = count,
                    rows = (from ObjectDetails in gnmObj.AsEnumerable()
                            select new
                            {
                                Object_ID = ObjectDetails.Object_ID,
                                //Edit = "<img id='" + ObjectDetails.Object_ID + "' src='" + AppPath + "/Content/images/edit.gif' mode='Read' class='edtClick' />" ,
                                Edit = "<a title='Edit' href='#' id='" + ObjectDetails.Object_ID + "' mode='Read' class='edtClick font-icon-class'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = (SelectObj.IsDelete) ? ("<input key='" + ObjectDetails.Object_ID + "' type='checkbox' defaultchecked='' class='chkClick' />") : ("<input key='" + ObjectDetails.Object_ID + "' type='checkbox' defaultchecked='' class='chkClick' disabled=" + true + " />"),

                                Object_Name = ObjectDetails.Object_Name,
                                Read_Action = ObjectDetails.Read_Action,
                                Create_Action = ObjectDetails.Create_Action,
                                Update_Action = ObjectDetails.Update_Action,
                                Delete_Action = ObjectDetails.Delete_Action,
                                Print_Action = ObjectDetails.Print_Action,
                                Export_Action = ObjectDetails.Export_Action,
                                Import_Action = ObjectDetails.Import_Action,
                                Object_Active = ObjectDetails.Object_IsActive,
                                Object_Type = ObjectDetails.Object_Type,
                                Object_Description = ObjectDetails.Object_Description
                            }
                    ).ToList().Paginate(page, rows),
                };
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    //
                }
            }
            return new JsonResult(jsonData);
        }

        #endregion
        #region ::: To select all objects:::
        /// <summary>
        /// To select all objects
        /// </summary>
        /// <returns>...</returns>
        public static IQueryable<ObjectClass> SelectList(SelectMovementtypList SelectObj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string Query, string connString, int LogException)
        {
            dynamic dummy = null;
            var jsonData = dummy;
            int count = 0;
            int total = 0;
            IQueryable<ObjectClass> gnmObj = null;
            List<ObjectClass> results = new List<ObjectClass>();
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_Select_AM_ERP_Select_CoreObjectMaster";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                // First result set: MovementTypeDefinition data
                                while (reader.Read())
                                {

                                    ObjectClass obj = new ObjectClass
                                    {
                                        Create_Action = reader["Create_Action"].ToString(),
                                        Delete_Action = reader["Delete_Action"].ToString(),
                                        Export_Action = reader["Export_Action"].ToString(),
                                        Import_Action = reader["Import_Action"].ToString(),
                                        Object_Description = reader["Object_Description"].ToString(),
                                        Object_IsActive = reader["Object_IsActive"].ToString(),
                                        Object_Name = reader["Object_Name"].ToString(),
                                        Object_Type = reader["Object_Type"].ToString(),
                                        Print_Action = reader["Print_Action"].ToString(),
                                        Read_Action = reader["Read_Action"].ToString(),
                                        Update_Action = reader["Update_Action"].ToString(),
                                        Object_ID = Convert.ToInt32(reader["Object_ID"])
                                    };
                                    results.Add(obj);



                                }


                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log exception
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                    }
                    finally
                    {
                        command?.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                gnmObj = results.AsQueryable();
                //FilterToolBar Search
                if (SelectObj.filters != "null" && SelectObj.filters != "undefined" && SelectObj.filters != null)
                {

                    string decodedValue = Uri.UnescapeDataString(filters);
                    decodedValue = HttpUtility.UrlDecode(decodedValue);
                    string decryptedValue = Common.DecryptString(decodedValue);
                    // Filters filters = JObject.Parse(Request.Params["filters"]).ToObject<Filters>();
                    Filters filtersObj = JObject.Parse(Common.DecryptString(decryptedValue)).ToObject<Filters>();
                    gnmObj = gnmObj.FilterSearch<ObjectClass>(filtersObj);

                }//Advance Search
                else if (SelectObj.Query != "null" && SelectObj.Query != "undefined" && SelectObj.Query != null)

                {
                    string decodedValue = Uri.UnescapeDataString(Query);
                    decodedValue = HttpUtility.UrlDecode(decodedValue);
                    string decryptedValue = Common.DecryptString(decodedValue);
                    string decodedValuead = Uri.UnescapeDataString(decodedValue);
                    AdvanceFilter advnfilter = JObject.Parse(decodedValuead).ToObject<AdvanceFilter>();

                    gnmObj = gnmObj.AdvanceSearch<ObjectClass>(advnfilter);
                }
                //Sorting 
                gnmObj = gnmObj.OrderByField<ObjectClass>(sidx, sord);



                return gnmObj;


            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    //
                }
            }
            return null;
        }
        #endregion
        #region ::: To save the changes and to insert new objects:::
        /// <summary>
        /// To insert new objects and to Edit existing Ojects
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult Save(SaveCoreObjectMasterList SaveObj, string connString, int LogException)
        {
            string Display = "";
            GNM_Object gObj = null;
            try
            {
                JObject jObj = JObject.Parse(SaveObj.Data);
                int rowcount = jObj["rows"].Count();
                for (int i = 0; i < rowcount; i++)
                {
                    gObj = jObj["rows"].ElementAt(i).ToObject<GNM_Object>();
                    gObj.Create_Action = Uri.UnescapeDataString(gObj.Create_Action);
                    gObj.Delete_Action = Uri.UnescapeDataString(gObj.Delete_Action);
                    gObj.Export_Action = Uri.UnescapeDataString(gObj.Export_Action);
                    gObj.Import_Action = Uri.UnescapeDataString(gObj.Import_Action);
                    gObj.Object_Description = Uri.UnescapeDataString(gObj.Object_Description);
                    gObj.Object_Name = Uri.UnescapeDataString(gObj.Object_Name);
                    gObj.Print_Action = Uri.UnescapeDataString(gObj.Print_Action);
                    gObj.Read_Action = Uri.UnescapeDataString(gObj.Read_Action);
                    gObj.Update_Action = Uri.UnescapeDataString(gObj.Update_Action);
                    //GEClient.GNM_Object.Add(gObj);
                    //GEClient.SaveChanges();
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_InsertUpdate_AM_ERP_Save_CoreObjectMaster";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@Object_ID", gObj.Object_ID);
                                command.Parameters.AddWithValue("@Create_Action", gObj.Create_Action);
                                command.Parameters.AddWithValue("@Delete_Action", gObj.Delete_Action);
                                command.Parameters.AddWithValue("@Export_Action", gObj.Export_Action);
                                command.Parameters.AddWithValue("@Import_Action", gObj.Import_Action);
                                command.Parameters.AddWithValue("@Object_Description", gObj.Object_Description);
                                command.Parameters.AddWithValue("@Object_Name", gObj.Object_Name);
                                command.Parameters.AddWithValue("@Object_IsActive", gObj.Object_IsActive);
                                command.Parameters.AddWithValue("@Object_Type", gObj.Object_Type);




                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                command.ExecuteNonQuery();
                            }
                        }
                        catch (Exception ex)
                        {
                            // Log exception
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }
                        }
                        finally
                        {
                            command?.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }

                    if (gObj.Object_ID == 0)
                    {

                        //gbl.InsertGPSDetails(Convert.ToInt32(SaveObj.Company_ID), Convert.ToInt32(SaveObj.Branch), Convert.ToInt32(SaveObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreObjectMaster")), gObj.Object_ID, 0, 0, "Insert", false, Convert.ToInt32(SaveObj.MenuID));
                    }
                    else
                    {

                        //gbl.InsertGPSDetails(Convert.ToInt32(SaveObj.Company_ID), Convert.ToInt32(SaveObj.Branch), Convert.ToInt32(SaveObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreObjectMaster")), gObj.Object_ID, 0, 0, "Update", false, Convert.ToInt32(SaveObj.MenuID));
                    }
                }

                Display = "success";
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                // 
                // return RedirectToAction("Error");
                Display = "Fail";
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                // return RedirectToAction("Error");
                Display = "Fail";
            }
            return new JsonResult(Display);
        }
        #endregion

        #region ::: To delete the objects:::
        /// <summary>
        /// To delete the objects
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult Delete(DeleteCoreObjectMasterList DelObj, string connString, int LogException)
        {
            string Result = "";
            GNM_Object gObj = null;
            JTokenReader jr = null;

            JObject jObj = JObject.Parse(DelObj.key);
            int rowcount = jObj["rows"].Count();
            int id = 0;
            List<int> objectIds = new List<int>();
            try
            {
                for (int i = 0; i < rowcount; i++)
                {
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jr.Read();
                    id = Convert.ToInt32(jr.Value);
                    objectIds.Add(id);
                    //gObj = GEClient.GNM_Object.Where(objid => objid.Object_ID == id).First();
                    //GEClient.GNM_Object.Remove(gObj);
                }
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_Delete_AM_ERP_Delete_CoreObjectMaster";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@Object_IDs", string.Join(",", objectIds));




                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            command.ExecuteNonQuery();
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log exception
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                    }
                    finally
                    {
                        command?.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                //GEClient.SaveChanges();
                //gbl.InsertGPSDetails(Convert.ToInt32(DelObj.Company_ID), Convert.ToInt32(DelObj.Branch), Convert.ToInt32(DelObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreObjectMaster")), gObj.Object_ID, 0, 0, "Delete", false, Convert.ToInt32(DelObj.MenuID));
                Result = "Deleted Successfully";
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                // 
                // return RedirectToAction("Error");
                Result = "Fail";
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                // return RedirectToAction("Error");
                Result = "Fail";
            }
            return new JsonResult(Result);
        }
        #endregion
        #region::: To Export:::
        /// <summary>
        /// To Export 
        /// </summary>
        /// <returns>...</returns>
        public static async Task<object> Export(SelectMovementtypList ExpObj, int LogException, string constring)
        {
            DataTable dt = new DataTable();
            try
            {
                IQueryable<ObjectClass> Obj = (IQueryable<ObjectClass>)SelectList(ExpObj, ExpObj.sidx, ExpObj.rows, ExpObj.page, ExpObj.sord, ExpObj._search, ExpObj.nd, ExpObj.filters, ExpObj.advance, ExpObj.Query, constring, LogException);

                int cnt = Obj.Count();
                var arr =
                from ObjectDetails in Obj
                select new
                {
                    Object_Name = ObjectDetails.Object_Name,
                    Read_Action = ObjectDetails.Read_Action,
                    Create_Action = ObjectDetails.Create_Action,
                    Update_Action = ObjectDetails.Update_Action,
                    Delete_Action = ObjectDetails.Delete_Action,
                    Print_Action = ObjectDetails.Print_Action,
                    Export_Action = ObjectDetails.Export_Action,
                    Object_Description = ObjectDetails.Object_Description,
                    Import_Action = ObjectDetails.Import_Action,
                    IsActive = ObjectDetails.Object_IsActive
                };

                dt.Columns.Add("Object Name");
                dt.Columns.Add("Object Description");
                dt.Columns.Add("Read Action");
                dt.Columns.Add("Add Action");
                dt.Columns.Add("Edit Action");
                dt.Columns.Add("Delete Action");
                dt.Columns.Add("Print Action");
                dt.Columns.Add("Export Action");
                dt.Columns.Add("Import Action");
                dt.Columns.Add("Is Active?");

                DataTable dt1 = new DataTable();
                dt1.Columns.Add("Object Name");
                dt1.Columns.Add("Object Description");
                dt1.Columns.Add("Read Action");
                dt1.Columns.Add("Add Action");
                dt1.Columns.Add("Edit Action");
                dt1.Columns.Add("Delete Action");
                dt1.Columns.Add("Print Action");
                dt1.Columns.Add("Export Action");
                dt1.Columns.Add("Import Action");
                dt1.Columns.Add("Is Active?");
                dt1.Rows.Add(0, 0, 0, 0, 0, 0, 0, 0, 0, 1);
                cnt = arr.Count();

                for (int i = 0; i < cnt; i++)
                {
                    dt.Rows.Add(arr.ElementAt(i).Object_Name, arr.ElementAt(i).Object_Description, arr.ElementAt(i).Read_Action, arr.ElementAt(i).Create_Action, arr.ElementAt(i).Update_Action, arr.ElementAt(i).Delete_Action, arr.ElementAt(i).Print_Action, arr.ElementAt(i).Export_Action, arr.ElementAt(i).Import_Action, arr.ElementAt(i).IsActive);
                }
                ExportList reportExportList = new ExportList
                {
                    Company_ID = ExpObj.Company_ID, // Assuming this is available in ExportObj
                    Branch = ExpObj.Branch_ID,
                    dt1 = dt1,



                    dt = dt,

                    FileName = "MovementTypeDefinition", // Set a default or dynamic filename
                    Header = CommonFunctionalities.GetResourceString(ExpObj.UserCulture.ToString(), "MovementTypeDefinition").ToString(), // Set a default or dynamic header
                    exprtType = ExpObj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                    UserCulture = ExpObj.UserCulture
                };



                var result = await DocumentExport.Export(reportExportList, constring, LogException);
                return result.Value;
                //DocumentExport.Export(exprtType, dt, dt1, "Objects", "Objects");
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //  
                // RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
            }
            return null;
        }
        #endregion
    }
    #region CoreObjectMasterListsandclasses vinay n
    /// <summary>
    /// CoreObjectMasterclasses
    /// </summary>
    public class SaveCoreObjectMasterList
    {
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int User_ID { get; set; }
        public int MenuID { get; set; }
        public string Data { get; set; }
    }
    public class DeleteCoreObjectMasterList
    {
        public string key { get; set; }
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int User_ID { get; set; }
        public int MenuID { get; set; }
    }
    public class ObjectClass
    {
        public string Create_Action { get; set; }
        public string Delete_Action { get; set; }
        public string Export_Action { get; set; }
        public string Import_Action { get; set; }
        public string Object_Description { get; set; }
        public string Object_IsActive { get; set; }
        public string Object_Name { get; set; }
        public string Object_Type { get; set; }
        public string Print_Action { get; set; }
        public string Read_Action { get; set; }
        public string Update_Action { get; set; }
        public int Object_ID { get; set; }
    }

    #endregion

}
