using PBC.UtilityService.Utilities;
using System.Threading.Tasks;

namespace PBC.UtilityService.Services
{
    /// <summary>
    /// Service implementation for Help Desk Service Request API operations
    /// </summary>
    public class HelpDeskServiceRequestAPIService : IHelpDeskServiceRequestAPIService
    {
        private readonly ILogger<HelpDeskServiceRequestAPIService> _logger;

        public HelpDeskServiceRequestAPIService(ILogger<HelpDeskServiceRequestAPIService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Gets the service request query for help desk operations
        /// </summary>
        /// <param name="connectionString">Database connection string</param>
        /// <param name="logException">Flag to enable/disable exception logging</param>
        /// <param name="langID">Language ID</param>
        /// <param name="genLangCode">General language code</param>
        /// <param name="userLangCode">User language code</param>
        /// <param name="mode">Query mode (0=All, 1=My-Q, 2=Group-Q, 3=All-Q)</param>
        /// <param name="userId">User ID</param>
        /// <param name="companyId">Company ID</param>
        /// <param name="branchId">Branch ID</param>
        /// <param name="sidx">Sort index</param>
        /// <param name="sord">Sort order</param>
        /// <param name="dbName">Database name</param>
        /// <returns>SQL query string for service requests</returns>
        public async Task<string> GetServiceRequestQueryAsync(string connectionString, int logException, int langID, 
            string genLangCode = "en", string userLangCode = "en", int mode = 0, int userId = 0, 
            int companyId = 0, int branchId = 0, string sidx = "", string sord = "", string dbName = "")
        {
            try
            {
                _logger.LogInformation("Getting service request query for mode: {Mode}, user: {UserId}, company: {CompanyId}", 
                    mode, userId, companyId);
                
                // Execute the static method asynchronously
                await Task.Delay(1); // Simulate async operation
                var result = HelpDeskServiceRequestAPI.GetServiceRequestQuery(connectionString, logException, langID, 
                    genLangCode, userLangCode, mode, userId, companyId, branchId, sidx, sord, dbName);
                
                _logger.LogInformation("Successfully generated service request query for mode: {Mode}, user: {UserId}", 
                    mode, userId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating service request query for mode: {Mode}, user: {UserId}", 
                    mode, userId);
                throw;
            }
        }
    }
}
