﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using System.Xml.Linq;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreStateMasterServices
    {
        static string AppPath = string.Empty;
        private static JTokenReader jTR;

        #region ::: Select All State /Mithun:::
        /// <summary>
        /// Select All Parts Order Cancellation
        /// </summary>

        private static IQueryable<StateMaster> GetAllState(SelectStateMAsterList SelectObj, string constring, int LogException, int CountryID, int LanguageID, string GeneralCulture, string UserCulture, int GeneralLanguageID)
        {
            IEnumerable<StateMaster> IEStateMasterArray = null;
            IQueryable<StateMaster> IQStateMaster = null;
            try
            {
                string YesE = CommonFunctionalities.GetResourceString(GeneralCulture.ToString(), "yes").ToString();
                string NoE = CommonFunctionalities.GetResourceString(GeneralCulture.ToString(), "no").ToString();
                string YesL = CommonFunctionalities.GetResourceString(UserCulture.ToString(), "yes").ToString();
                string NoL = CommonFunctionalities.GetResourceString(UserCulture.ToString(), "no").ToString();

                List<StateMaster> stateList = new List<StateMaster>();



                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Get states using stored procedure
                    SqlCommand cmdStates = new SqlCommand("Up_Sel_Am_Erp_GetStatesByCountryID", conn);
                    cmdStates.CommandType = CommandType.StoredProcedure;
                    cmdStates.Parameters.AddWithValue("@CountryID", CountryID);

                    List<StateMaster> states = new List<StateMaster>();
                    using (SqlDataReader reader = cmdStates.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            states.Add(new StateMaster
                            {
                                State_ID = (int)reader["State_ID"],
                                State_Name = reader["State_Name"].ToString(),
                                Region_ID = (int)reader["Region_ID"],
                                Region_Name = reader["Region_Name"].ToString(),
                                State_IsActive = (bool)reader["State_IsActive"] ? YesE : NoE
                            });
                        }
                    }

                    // Get state locales if necessary
                    List<StateMaster> stateLocales = null;
                    if (LanguageID != Convert.ToInt32(GeneralLanguageID))
                    {
                        SqlCommand cmdLocales = new SqlCommand("Up_Sel_Am_Erp_GetStatesByCountryLanguageID", conn);
                        cmdLocales.CommandType = CommandType.StoredProcedure;
                        cmdLocales.Parameters.AddWithValue("@CountryID", CountryID);
                        cmdLocales.Parameters.AddWithValue("@LanguageID", LanguageID);

                        stateLocales = new List<StateMaster>();
                        using (SqlDataReader reader = cmdLocales.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                stateLocales.Add(new StateMaster
                                {
                                    State_ID = (int)reader["State_ID"],
                                    State_Name = reader["State_Name"].ToString(),
                                    Region_ID = (int)reader["Region_ID"], // Adjust as per your database schema or map from states
                                    Region_Name = reader["Region_Name"].ToString(), // Adjust as per your database schema or map from states
                                    State_IsActive = (bool)reader["State_IsActive"] ? YesE : NoE // Assuming this is always for user language
                                });
                            }
                        }
                    }

                    // Combine data
                    if (LanguageID == Convert.ToInt32(GeneralLanguageID))
                    {
                        stateList = states;
                    }
                    else
                    {
                        stateList = (from a in states
                                     join b in stateLocales on a.State_ID equals b.State_ID
                                     select new StateMaster
                                     {
                                         State_ID = b.State_ID,
                                         State_Name = b.State_Name,
                                         Region_ID = a.Region_ID,
                                         Region_Name = a.Region_Name,
                                         State_IsActive = b.State_IsActive
                                     }).ToList();
                    }
                }

                IEStateMasterArray = stateList;
                IQStateMaster = IEStateMasterArray.AsQueryable();
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return IQStateMaster;
            return (IQStateMaster);
        }

        #endregion

        #region ::: Select /Mithun:::
        /// <summary>
        /// To Select States for a Country
        /// </summary>

        public static IActionResult Select(SelectStateMAsterList SelectObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {
            var jsonData = default(dynamic);
            IQueryable<StateMaster> IQStateMaster = null;

            try
            {
                int Count = 0;
                int Total = 0;
                int Company_ID = Convert.ToInt32(SelectObj.Company_ID);

                // Fetch regions using ADO.NET with SqlDataReader by calling the stored procedure
                string companyRegionNames = "0:--Select--;";

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("UP_SEL_AMERP_GetCompanyRegions", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add(new SqlParameter("@Company_ID", Company_ID));

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                companyRegionNames += $"{reader["RefMasterDetail_ID"]}:{reader["RefMasterDetail_Name"].ToString().Replace(";", ":")};";
                            }
                        }
                    }
                }

                companyRegionNames = companyRegionNames.TrimEnd(';');

                // Fetch state data
                IQStateMaster = GetAllState(SelectObj, constring, LogException, SelectObj.CountryID, SelectObj.LanguageID, SelectObj.GeneralCulture, SelectObj.UserCulture, SelectObj.GeneralLanguageID);

                // Apply search filters
                if (_search)
                {
                    Filters filtersobj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    IQStateMaster = IQStateMaster.FilterSearch<StateMaster>(filtersobj);
                }
                else if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse(Query).ToObject<AdvanceFilter>();
                    IQStateMaster = IQStateMaster.AdvanceSearch<StateMaster>(advnfilter);
                }

                // Apply sorting
                IQStateMaster = IQStateMaster.OrderByField<StateMaster>(sidx, sord);

                Count = IQStateMaster.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                string Lbl_Refresh = CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "refresh").ToString();
                string Lbl_AdvanceSearch = CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "advancesearch").ToString();

                jsonData = new
                {
                    total = Total,
                    page = page,
                    rows = (from a in IQStateMaster.AsEnumerable()
                            select new
                            {
                                ID = a.State_ID,
                                edit = $"<a title='{CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "edit")}' href='#' style='font-size: 13px;' id='{a.State_ID}' class='StateEdit' key='{a.State_ID}' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = $"<input type='checkbox' key='{a.State_ID}' defaultchecked='' id='chk{a.State_ID}' class='StateDelete'/>",
                                State_Name = (a.State_Name),
                                Region_ID = a.Region_ID,
                                Region_Name = (a.Region_Name),
                                State_IsActive = a.State_IsActive,
                                Locale = $"<a title='Localize' href='#' style='font-size: 13px;' width='20' height='20' src='{AppPath}/Content/local.png' class='StateLocale' key='{a.State_ID}'><i class='fa fa-globe'></i></a>",
                                View = $"<a title='Add' href='#' style='font-size: 13px;' id='{a.State_ID}' class='ViewStateLocale' key='{a.State_ID}' State='{a.State_Name}'><i class='fa fa-plus'></i></a>",
                            }).ToList().Paginate(page, rows),
                    records = Count,
                    SelectObj.CountryID,
                    SelectObj.LanguageID,
                    companyRegionNames,
                    Lbl_Refresh,
                    Lbl_AdvanceSearch
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }


        #endregion

        #region ::: SelectReferenceMaster /Mithun:::
        /// <summary>
        /// To Select Country
        /// </summary> 
        public static IActionResult SelectReferenceMaster(SelectReferenceMasterStateList SelectReferenceMasterObj, string constring, int LogException)
        {
            dynamic Masterdata = null;
            try
            {
                int Language_ID = Convert.ToInt32(SelectReferenceMasterObj.UserLanguageID);
                string UserLanguageCode = SelectReferenceMasterObj.UserLanguageCode.ToString();
                string GeneralLanguageCode = SelectReferenceMasterObj.GeneralLanguageCode.ToString();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetReferenceMasterData", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@LanguageID", Language_ID);
                    cmd.Parameters.AddWithValue("@UserLanguageCode", UserLanguageCode);
                    cmd.Parameters.AddWithValue("@GeneralLanguageCode", GeneralLanguageCode);

                    SqlDataReader reader = cmd.ExecuteReader();

                    var referenceMasterData = new List<object>();

                    while (reader.Read())
                    {
                        referenceMasterData.Add(new
                        {
                            ID = reader["ID"] != DBNull.Value ? (int)reader["ID"] : 0,
                            Name = reader["Name"] != DBNull.Value ? (string)reader["Name"] : string.Empty
                        });
                    }

                    // Create the JSON object
                    Masterdata = new
                    {
                        ReferenceMasterData = referenceMasterData
                    };

                    reader.Close();
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(Masterdata, JsonRequestBehavior.AllowGet);
            return new JsonResult(Masterdata);
        }

        #endregion

        #region ::: SelectParticularState /Mithun:::
        /// <summary>
        /// To Select Particular State
        /// </summary>
        public static IActionResult SelectParticularState(SelectParticularStateList SelectParticularStateObj, string constring, int LogException)
        {
            dynamic x = null;
            try
            {
                int BranchID = Convert.ToInt32(SelectParticularStateObj.Branch);
                //GNM_User User = (GNM_User)Session["UserDetails"];
                //  GNM_User User = SelectParticularStateObj.UserDetails.FirstOrDefault();
                int Language_ID = Convert.ToInt32(SelectParticularStateObj.UserLanguageID);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetParticularState", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@StateID", SelectParticularStateObj.StateID);
                    cmd.Parameters.AddWithValue("@LanguageID", Language_ID);

                    SqlDataReader reader = cmd.ExecuteReader();

                    if (reader.Read())
                    {
                        int stateID = reader["State_ID"] != DBNull.Value ? (int)reader["State_ID"] : 0;
                        string stateName = reader["State_Name"] != DBNull.Value ? (string)reader["State_Name"] : string.Empty;
                        bool stateIsActive = reader["State_IsActive"] != DBNull.Value ? (bool)reader["State_IsActive"] : false;

                        reader.NextResult(); // Move to the next result set for StateLocale

                        string stateLocaleID = string.Empty;
                        string stateLocaleName = string.Empty;
                        if (reader.Read())
                        {
                            stateLocaleID = reader["StateLocale_ID"] != DBNull.Value ? reader["StateLocale_ID"].ToString() : string.Empty;
                            stateLocaleName = reader["State_Name"] != DBNull.Value ? (string)reader["State_Name"] : string.Empty;
                        }

                        // Create the JSON object
                        x = new
                        {
                            State_ID = stateID,
                            State_Name = stateName,
                            State_IsActive = stateIsActive,
                            StateLocale_ID = stateLocaleID,
                            StateLocale_Name = stateLocaleName
                        };

                        //gbl.InsertGPSDetails(
                        //    Convert.ToInt32(SelectParticularStateObj.Company_ID.ToString()),
                        //    BranchID,
                        //    User.User_ID,
                        //    Common.GetObjectID("CoreStateMaster",constring),
                        //    stateID,
                        //    0,
                        //    0,
                        //    "Viewed State- " + stateName,
                        //    false,
                        //    Convert.ToInt32(SelectParticularStateObj.MenuID),
                        //    Convert.ToDateTime(SelectParticularStateObj.LoggedINDateTime)
                        //);
                    }

                    reader.Close();
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(x, JsonRequestBehavior.AllowGet);
            return new JsonResult(x);
        }

        #endregion

        #region ::: Save /Mithun:::
        /// <summary>
        /// To Insert and Update State
        /// </summary>
        public static IActionResult Save(SaveStateList SaveObj, string constring, int LogException)
        {
            string Msg = string.Empty;
            try
            {
                int BranchID = Convert.ToInt32(SaveObj.Branch);
                //GNM_User User = (GNM_User)Session["UserDetails"];
                //GNM_User User = SaveObj.UserDetails.FirstOrDefault();
                JObject jObj = JObject.Parse(SaveObj.data);
                int Count = jObj["rows"].Count();

                // Create an XML document from the JSON object
                XDocument stateXml = new XDocument(new XElement("States",
                    from row in jObj["rows"]
                    select new XElement("State",
                        new XElement("State_ID", (int?)row["State_ID"] ?? 0),
                        new XElement("State_Name", Uri.UnescapeDataString((string)row["State_Name"])),
                        new XElement("State_IsActive", (bool)row["State_IsActive"]),
                        new XElement("Country_ID", (int)row["Country_ID"]),
                        new XElement("Region_ID", (int)row["Region_ID"]),
                        new XElement("ModifiedBy", Convert.ToInt32(SaveObj.User_ID)),
                        new XElement("ModifiedDate", DateTime.Now)
                    )
                ));

                // Call stored procedure
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    SqlCommand command = new SqlCommand("Sp_Ins_AM_ERP_SaveState", connection);
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.Add(new SqlParameter("@StateData", SqlDbType.Xml) { Value = stateXml.ToString() });
                    command.ExecuteNonQuery();
                }

                // Log GPS details for each state
                foreach (var row in jObj["rows"])
                {
                    int stateID = (int?)row["State_ID"] ?? 0;
                    string stateName = Uri.UnescapeDataString((string)row["State_Name"]);
                    string operation = stateID != 0 ? "Updated" : "Inserted";

                    //gbl.InsertGPSDetails(
                    //    Convert.ToInt32(SaveObj.Company_ID.ToString()),
                    //    BranchID,
                    //    User.User_ID,
                    //    Common.GetObjectID("CoreStateMaster",constring),
                    //    stateID,
                    //    0,
                    //    0,
                    //    $"{operation} State- {stateName}",
                    //    false,
                    //    Convert.ToInt32(SaveObj.MenuID),
                    //    Convert.ToDateTime(SaveObj.LoggedINDateTime)
                    //);
                }

                Msg = "Saved";
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                Msg = string.Empty;
            }
            //return Msg;
            return new JsonResult(Msg);
        }

        #endregion

        #region ::: UpdateLocale /Mithun:::
        /// <summary>
        /// To Update State Locale
        /// </summary>

        public static IActionResult UpdateLocale(UpdateLocaleStateList UpdateLocaleObj, string constring, int LogException)
        {
            int StateLocale_ID = 0;
            var result = new { StateLocale_ID = 0 };

            try
            {
                JObject jObj = JObject.Parse(UpdateLocaleObj.data);
                var SLRow = jObj.ToObject<GNM_StateLocale>();
                string decryptedStateName = Common.DecryptString(SLRow.State_Name);


                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand("Up_Ins_Upd_Am_Erp_UpdateLocale", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@StateLocale_ID", (object)SLRow.StateLocale_ID ?? DBNull.Value);
                        command.Parameters.AddWithValue("@State_Name", decryptedStateName);
                        command.Parameters.AddWithValue("@State_ID", SLRow.State_ID);
                        command.Parameters.AddWithValue("@User_ID", Convert.ToInt32(UpdateLocaleObj.User_ID));

                        SqlParameter outputIdParam = new SqlParameter("@NewStateLocale_ID", SqlDbType.Int)
                        {
                            Direction = ParameterDirection.Output
                        };
                        command.Parameters.Add(outputIdParam);

                        command.ExecuteNonQuery();
                        StateLocale_ID = (int)outputIdParam.Value;
                    }
                }

                // Insert GPS Details
                //gbl.InsertGPSDetails(
                //    Convert.ToInt32(UpdateLocaleObj.Company_ID),
                //    Convert.ToInt32(UpdateLocaleObj.Branch),
                //    Convert.ToInt32(UpdateLocaleObj.User_ID),
                //    Convert.ToInt32(Common.GetObjectID("CoreStateMaster",constring)),
                //    SLRow.StateLocale_ID, 0, 0, "Update", false, Convert.ToInt32(UpdateLocaleObj.MenuID)
                //);

                result = new
                {
                    StateLocale_ID = StateLocale_ID
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(result, JsonRequestBehavior.AllowGet);
            return new JsonResult(result);
        }



        #endregion

        #region ::: Delete /Mithun:::
        /// <summary>
        /// To Delete State
        /// </summary>

        public static IActionResult Delete(DeleteStateList DeleteObj, string constring, int LogException)
        {
            string Msg = string.Empty;
            SqlConnection connection = null;
            SqlTransaction transaction = null;
            var Culture = "Resource_" + DeleteObj.Lang;
            try
            {
                // Initialize the SQL connection
                connection = new SqlConnection(constring);
                connection.Open();
                transaction = connection.BeginTransaction();

                // Parse the JSON object
                JObject jObj = JObject.Parse(DeleteObj.key);
                int Count = jObj["rows"].Count();

                int BranchID = Convert.ToInt32(DeleteObj.Branch);
                //GNM_User User = (GNM_User)Session["UserDetails"];
                GNM_User User = DeleteObj.UserDetails.FirstOrDefault();
                int ID = 0;

                for (int i = 0; i < Count; i++)
                {
                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jTR.Read();
                    ID = Convert.ToInt32(jTR.Value);

                    // Check if the state exists
                    SqlCommand selectCommand = new SqlCommand("SELECT * FROM GNM_State WHERE State_ID = @ID", connection, transaction);
                    selectCommand.Parameters.AddWithValue("@ID", ID);
                    SqlDataReader reader = selectCommand.ExecuteReader();

                    if (reader.HasRows)
                    {
                        reader.Read();
                        string stateName = reader["State_Name"].ToString();
                        reader.Close();

                        // Delete the state locale if exists
                        SqlCommand deleteLocaleCommand = new SqlCommand("DELETE FROM GNM_StateLocale WHERE State_ID = @ID", connection, transaction);
                        deleteLocaleCommand.Parameters.AddWithValue("@ID", ID);
                        deleteLocaleCommand.ExecuteNonQuery();

                        // Delete the state
                        SqlCommand deleteCommand = new SqlCommand("DELETE FROM GNM_State WHERE State_ID = @ID", connection, transaction);
                        deleteCommand.Parameters.AddWithValue("@ID", ID);
                        deleteCommand.ExecuteNonQuery();

                        // Insert GPS details
                        //gbl.InsertGPSDetails(
                        //    Convert.ToInt32(DeleteObj.Company_ID),
                        //    BranchID,
                        //    User.User_ID,
                        //    Common.GetObjectID("CoreStateMaster",constring),
                        //    ID,
                        //    0,
                        //    0,
                        //    "Deleted State- " + stateName,
                        //    false,
                        //    Convert.ToInt32(DeleteObj.MenuID),
                        //    Convert.ToDateTime(DeleteObj.LoggedINDateTime)
                        //);

                        Msg += CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "deletedsuccessfully").ToString();
                    }
                    else
                    {
                        reader.Close();
                    }
                }

                // Commit the transaction
                transaction.Commit();
            }
            catch (Exception ex)
            {
                // Rollback transaction in case of an error
                if (transaction != null)
                {
                    transaction.Rollback();
                }

                if (ex.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    Msg += CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    // Log other exceptions
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }
                }
            }
            finally
            {
                // Close the connection
                if (connection != null)
                {
                    connection.Close();
                }
            }
            //return Msg;
            return new JsonResult(Msg);
        }

        #endregion

        #region ::: CheckState /Mihtun:::
        /// <summary>
        /// To Check State already exists 
        /// </summary>
        public static IActionResult CheckState(CheckStateList CheckStateObj, string constring, int LogException)
        {
            int Count = 0;
            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    SqlCommand command = new SqlCommand("Up_Sel_AM_ERP_CheckDuplicateState", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.AddWithValue("@CountryID", CheckStateObj.CountryID);
                    command.Parameters.AddWithValue("@StateName", CheckStateObj.StateName);
                    command.Parameters.AddWithValue("@StateID", CheckStateObj.StateID);

                    SqlParameter countParameter = new SqlParameter("@Count", SqlDbType.Int);
                    countParameter.Direction = ParameterDirection.ReturnValue;
                    command.Parameters.Add(countParameter);

                    command.ExecuteNonQuery();

                    Count = (int)countParameter.Value;
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Count;
            return new JsonResult(Count);
        }

        #endregion

        #region ::: CheckStateLocale /Mithun:::
        /// <summary>
        /// To Check StateLocale already exists 
        /// </summary>
        public static IActionResult CheckStateLocale(CheckStateLocaleList CheckStateLocaleObj, string constring, int LogException)
        {
            int Count = 0;
            try
            {
                string StateName = Common.DecryptString(CheckStateLocaleObj.StateName);
                int Language_ID = Convert.ToInt32(CheckStateLocaleObj.UserLanguageID);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand("Up_Chk_Am_Erp_CheckStateLocale", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@CountryID", CheckStateLocaleObj.CountryID);
                        command.Parameters.AddWithValue("@StateName", StateName);
                        command.Parameters.AddWithValue("@StateLocaleID", CheckStateLocaleObj.StateLocaleID);
                        command.Parameters.AddWithValue("@LanguageID", Language_ID);

                        Count = (int)command.ExecuteScalar();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Count;
            return new JsonResult(Count);
        }

        #endregion



        #region ::: Export :::
        public static async Task<object> Export(SelectStateMAsterList ExportObj, string constring, int LogException, string filters, string Query, string sidx, string sord)
        {
            try
            {
                DataTable DtData = new DataTable();
                int BranchID = Convert.ToInt32(ExportObj.Branch);
                // GNM_User User = (GNM_User)Session["UserDetails"];
                int CountryID = Convert.ToInt32(ExportObj.CountryID);
                int LanguageID = Convert.ToInt32(ExportObj.LanguageID);

                // Fetch all states using GetAllState method
                //  IQueryable<StateMaster> IQStateMaster = GetAllState(CountryID, LanguageID);

                IQueryable<StateMaster> IQStateMaster = GetAllState(ExportObj, constring, LogException, ExportObj.CountryID, ExportObj.LanguageID, ExportObj.GeneralCulture, ExportObj.UserCulture, ExportObj.GeneralLanguageID); // Assuming this method returns IQueryable<StateMaster>


                if (filters != "null" && filters != "undefined")
                {
                    Filters filtersobj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    IQStateMaster = IQStateMaster.FilterSearch<StateMaster>(filtersobj);
                }
                else if (Query != "null" && Query != "undefined")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Query).ToObject<AdvanceFilter>();
                    IQStateMaster = IQStateMaster.AdvanceSearch<StateMaster>(advnfilter);
                }

                // Apply sorting
                IQStateMaster = IQStateMaster.OrderByField<StateMaster>(sidx, sord);

                // Create a DataTable schema
                DtData.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "State").ToString());
                DtData.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "Region").ToString());
                DtData.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "Active").ToString());

                // Fill the DataTable with data
                foreach (var state in IQStateMaster)
                {
                    DtData.Rows.Add(state.State_Name, state.Region_Name, state.State_IsActive);
                }


                // Create criteria DataTable
                DataTable DtCriteria = new DataTable();
                DtCriteria.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "Country").ToString());
                DtCriteria.Rows.Add(Common.DecryptString(ExportObj.CountryName));

                // Create alignment DataTable
                DataTable DtAlignment = new DataTable();
                DtAlignment.Columns.Add("State");
                DtAlignment.Columns.Add("Region");
                DtAlignment.Columns.Add("Active");
                DtAlignment.Rows.Add(0, 0, 1);

                ReportExportList reportExportList = new ReportExportList
                {
                    Company_ID = ExportObj.Company_ID, // Assuming this is available in ExportObj
                    Branch = ExportObj.Branch.ToString(),
                    GeneralLanguageID = ExportObj.GeneralLanguageID,
                    UserLanguageID = ExportObj.LanguageID,
                    Options = DtCriteria,
                    dt = DtData,
                    Alignment = DtAlignment,
                    FileName = "State", // Set a default or dynamic filename
                    Header = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "state").ToString(), // Set a default or dynamic header
                    exprtType = ExportObj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                    UserCulture = ExportObj.UserCulture
                };


                //return ReportExport.Export(reportExportList, constring, LogException);
                var result = await ReportExport.Export(reportExportList, constring, LogException);
                return result.Value;


                // Call the export method
                // ReportExport.Export(exprtType, DtData, DtCriteria, DtAlignment, "State", HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "State").ToString());

                // Insert GPS details
                //gbl.InsertGPSDetails(
                //    Convert.ToInt32(Session["Company_ID"]),
                //    BranchID,
                //    User.User_ID,
                //    Common.GetObjectID("CoreStateMaster"),
                //    0, 0, 0,
                //    "State-Export",
                //    false,
                //    Convert.ToInt32(Session["MenuID"]),
                //    Convert.ToDateTime(Session["LoggedINDateTime"])
                //);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return null;
        }

        #endregion
















        public class CheckStateLocaleList
        {
            public int UserLanguageID { get; set; }
            public int CountryID { get; set; }
            public int StateLocaleID { get; set; }
            public string StateName { get; set; }
        }
        public class CheckStateList
        {
            public int CountryID { get; set; }
            public int StateID { get; set; }
            public string StateName { get; set; }
        }

        public class DeleteStateList
        {
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string key { get; set; }
            public string Lang { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public class UpdateLocaleStateList
        {
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string data { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public partial class GNM_State
        {
            public GNM_State()
            {
                this.GNM_StateLocale = new HashSet<GNM_StateLocale>();
            }

            public int State_ID { get; set; }
            public int Country_ID { get; set; }
            public string State_Name { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }
            public bool State_IsActive { get; set; }
            public Nullable<int> Region_ID { get; set; }
            public string StateCode { get; set; }
            public string Stcd { get; set; }

            public virtual ICollection<GNM_StateLocale> GNM_StateLocale { get; set; }
        }
        public partial class GNM_StateLocale
        {
            public int StateLocale_ID { get; set; }
            public int State_ID { get; set; }
            public string State_Name { get; set; }
            public int Language_ID { get; set; }

            public virtual GNM_State GNM_State { get; set; }
        }
        public class SaveStateList
        {
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string data { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public class SelectParticularStateList
        {
            public int StateID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }


        public class SelectReferenceMasterStateList
        {
            public int UserLanguageID { get; set; }
            public string UserLanguageCode { get; set; }
            public string GeneralLanguageCode { get; set; }
        }

        public class SelectStateMAsterList
        {
            public int CountryID { get; set; }
            public int LanguageID { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int exprtType { get; set; }
            public int GeneralLanguageID { get; set; }
            public string GeneralCulture { get; set; }
            public string UserCulture { get; set; }
            public string CountryName { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filters { get; set; }
            public string Query { get; set; }
        }
        public class GetAllStateList
        {
            public int GeneralLanguageID { get; set; }
            public string GeneralCulture { get; set; }
            public string UserCulture { get; set; }
        }

        public class StateMaster
        {
            public int State_ID
            {
                get;
                set;
            }
            public int? Region_ID
            {
                get;
                set;
            }
            public string Region_Name
            {
                get;
                set;
            }
            public string State_Name
            {
                get;
                set;
            }

            public string State_IsActive
            {
                get;
                set;
            }
        }
    }
}
