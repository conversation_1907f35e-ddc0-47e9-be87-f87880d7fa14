//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resource_en {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource_en() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.Resource_en", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abandon.
        /// </summary>
        internal static string Abandon {
            get {
                return ResourceManager.GetString("Abandon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abandon &amp; Delay Reason.
        /// </summary>
        internal static string AbandonDelayReason {
            get {
                return ResourceManager.GetString("AbandonDelayReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abandoned.
        /// </summary>
        internal static string Abandoned {
            get {
                return ResourceManager.GetString("Abandoned", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Accept.
        /// </summary>
        internal static string Accept {
            get {
                return ResourceManager.GetString("Accept", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account Number.
        /// </summary>
        internal static string AccountNumber {
            get {
                return ResourceManager.GetString("AccountNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Action.
        /// </summary>
        internal static string Action {
            get {
                return ResourceManager.GetString("Action", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Action By.
        /// </summary>
        internal static string ActionBy {
            get {
                return ResourceManager.GetString("ActionBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Action Date.
        /// </summary>
        internal static string ActionDate {
            get {
                return ResourceManager.GetString("ActionDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Action For Next Service.
        /// </summary>
        internal static string ActionForNextService {
            get {
                return ResourceManager.GetString("ActionForNextService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Action Name.
        /// </summary>
        internal static string ActionName {
            get {
                return ResourceManager.GetString("ActionName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Action Remarks.
        /// </summary>
        internal static string ActionRemarks {
            get {
                return ResourceManager.GetString("ActionRemarks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Action remarks max limit exceeded.
        /// </summary>
        internal static string ActionRemarksMaxlimitexceeded {
            get {
                return ResourceManager.GetString("ActionRemarksMaxlimitexceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Actions.
        /// </summary>
        internal static string Actions {
            get {
                return ResourceManager.GetString("Actions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Active?.
        /// </summary>
        internal static string Active {
            get {
                return ResourceManager.GetString("Active", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active From.
        /// </summary>
        internal static string ActiveFrom {
            get {
                return ResourceManager.GetString("ActiveFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active from date cannot be greater than active to date.
        /// </summary>
        internal static string ActiveFromdatecannotbegreaterthanActiveTodate {
            get {
                return ResourceManager.GetString("ActiveFromdatecannotbegreaterthanActiveTodate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active To.
        /// </summary>
        internal static string ActiveTo {
            get {
                return ResourceManager.GetString("ActiveTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Actual Hours.
        /// </summary>
        internal static string ActualHours {
            get {
                return ResourceManager.GetString("ActualHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add.
        /// </summary>
        internal static string Add {
            get {
                return ResourceManager.GetString("Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Action.
        /// </summary>
        internal static string AddAction {
            get {
                return ResourceManager.GetString("AddAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Branch.
        /// </summary>
        internal static string AddBranch {
            get {
                return ResourceManager.GetString("AddBranch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Branch Tax Details.
        /// </summary>
        internal static string AddBranchTaxDetails {
            get {
                return ResourceManager.GetString("AddBranchTaxDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Brands.
        /// </summary>
        internal static string AddBrands {
            get {
                return ResourceManager.GetString("AddBrands", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Company.
        /// </summary>
        internal static string AddCompany {
            get {
                return ResourceManager.GetString("AddCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Company Relation.
        /// </summary>
        internal static string AddCompanyRelation {
            get {
                return ResourceManager.GetString("AddCompanyRelation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Company Tax Details.
        /// </summary>
        internal static string AddCompanyTaxDetails {
            get {
                return ResourceManager.GetString("AddCompanyTaxDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Company Terms.
        /// </summary>
        internal static string AddCompanyTerms {
            get {
                return ResourceManager.GetString("AddCompanyTerms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Component Details.
        /// </summary>
        internal static string addcomponentdetails {
            get {
                return ResourceManager.GetString("addcomponentdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Customer.
        /// </summary>
        internal static string addcustomer {
            get {
                return ResourceManager.GetString("addcustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Customer Quotation.
        /// </summary>
        internal static string AddCustomerQuotation {
            get {
                return ResourceManager.GetString("AddCustomerQuotation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Employee.
        /// </summary>
        internal static string AddEmployee {
            get {
                return ResourceManager.GetString("AddEmployee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Events.
        /// </summary>
        internal static string AddEvents {
            get {
                return ResourceManager.GetString("AddEvents", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Filter.
        /// </summary>
        internal static string AddFilter {
            get {
                return ResourceManager.GetString("AddFilter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Parts Free Stock.
        /// </summary>
        internal static string addfreestock {
            get {
                return ResourceManager.GetString("addfreestock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Function Group.
        /// </summary>
        internal static string AddFunctionGroup {
            get {
                return ResourceManager.GetString("AddFunctionGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Job Card.
        /// </summary>
        internal static string AddJobCard {
            get {
                return ResourceManager.GetString("AddJobCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Master.
        /// </summary>
        internal static string AddMaster {
            get {
                return ResourceManager.GetString("AddMaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Model.
        /// </summary>
        internal static string addmodel {
            get {
                return ResourceManager.GetString("addmodel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Part.
        /// </summary>
        internal static string addnewpart {
            get {
                return ResourceManager.GetString("addnewpart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Operation Details.
        /// </summary>
        internal static string addoperation {
            get {
                return ResourceManager.GetString("addoperation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Operation Employee Details.
        /// </summary>
        internal static string AddOperationEmployeeDetails {
            get {
                return ResourceManager.GetString("AddOperationEmployeeDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Part.
        /// </summary>
        internal static string AddPart {
            get {
                return ResourceManager.GetString("AddPart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Part Price.
        /// </summary>
        internal static string addpartprice {
            get {
                return ResourceManager.GetString("addpartprice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Parts.
        /// </summary>
        internal static string AddParts {
            get {
                return ResourceManager.GetString("AddParts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Party.
        /// </summary>
        internal static string AddParty {
            get {
                return ResourceManager.GetString("AddParty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Prefix Suffix.
        /// </summary>
        internal static string AddPrefixSuffix {
            get {
                return ResourceManager.GetString("AddPrefixSuffix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Product.
        /// </summary>
        internal static string addproduct {
            get {
                return ResourceManager.GetString("addproduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Product Detail.
        /// </summary>
        internal static string addproductdetail {
            get {
                return ResourceManager.GetString("addproductdetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Product Type.
        /// </summary>
        internal static string addproducttype {
            get {
                return ResourceManager.GetString("addproducttype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Part Product Type Details.
        /// </summary>
        internal static string addproducttypedetails {
            get {
                return ResourceManager.GetString("addproducttypedetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Request.
        /// </summary>
        internal static string AddRequest {
            get {
                return ResourceManager.GetString("AddRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address.
        /// </summary>
        internal static string Address {
            get {
                return ResourceManager.GetString("Address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address Line 1.
        /// </summary>
        internal static string Address1 {
            get {
                return ResourceManager.GetString("Address1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address Line 2.
        /// </summary>
        internal static string Address2 {
            get {
                return ResourceManager.GetString("Address2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address Line 3.
        /// </summary>
        internal static string Address3 {
            get {
                return ResourceManager.GetString("Address3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Addresse Flag.
        /// </summary>
        internal static string AddresseFlag {
            get {
                return ResourceManager.GetString("AddresseFlag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Role.
        /// </summary>
        internal static string AddRole {
            get {
                return ResourceManager.GetString("AddRole", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Service Charge.
        /// </summary>
        internal static string addServiceCharge {
            get {
                return ResourceManager.GetString("addServiceCharge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Service Charge Details.
        /// </summary>
        internal static string addServiceChargeDetails {
            get {
                return ResourceManager.GetString("addServiceChargeDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Service Charges.
        /// </summary>
        internal static string addservicecharges {
            get {
                return ResourceManager.GetString("addservicecharges", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Service Type.
        /// </summary>
        internal static string AddServiceType {
            get {
                return ResourceManager.GetString("AddServiceType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Site Address.
        /// </summary>
        internal static string addsiteaddress {
            get {
                return ResourceManager.GetString("addsiteaddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Skills.
        /// </summary>
        internal static string AddSkills {
            get {
                return ResourceManager.GetString("AddSkills", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Specialization.
        /// </summary>
        internal static string AddSpecialization {
            get {
                return ResourceManager.GetString("AddSpecialization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Step.
        /// </summary>
        internal static string AddStep {
            get {
                return ResourceManager.GetString("AddStep", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Step Link.
        /// </summary>
        internal static string AddStepLink {
            get {
                return ResourceManager.GetString("AddStepLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Sundry.
        /// </summary>
        internal static string AddSundry {
            get {
                return ResourceManager.GetString("AddSundry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Tax Code.
        /// </summary>
        internal static string AddTaxCode {
            get {
                return ResourceManager.GetString("AddTaxCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add tax Structure.
        /// </summary>
        internal static string addtaxstructure {
            get {
                return ResourceManager.GetString("addtaxstructure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Tax Structure Details.
        /// </summary>
        internal static string AddTaxStructureDetails {
            get {
                return ResourceManager.GetString("AddTaxStructureDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Tax Structure Details.
        /// </summary>
        internal static string addtaxtstructuredetails {
            get {
                return ResourceManager.GetString("addtaxtstructuredetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add User.
        /// </summary>
        internal static string AddUser {
            get {
                return ResourceManager.GetString("AddUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Warranty Details.
        /// </summary>
        internal static string addwarrantydetails {
            get {
                return ResourceManager.GetString("addwarrantydetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Work Details.
        /// </summary>
        internal static string AddWorkDetails {
            get {
                return ResourceManager.GetString("AddWorkDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Advance Search.
        /// </summary>
        internal static string advancesearch {
            get {
                return ResourceManager.GetString("advancesearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All.
        /// </summary>
        internal static string all {
            get {
                return ResourceManager.GetString("all", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allocate.
        /// </summary>
        internal static string Allocate {
            get {
                return ResourceManager.GetString("Allocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allocated Hours.
        /// </summary>
        internal static string AllocatedHours {
            get {
                return ResourceManager.GetString("AllocatedHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allocation Not Possible.
        /// </summary>
        internal static string AllocationNotPossible {
            get {
                return ResourceManager.GetString("AllocationNotPossible", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allocated Successfully.
        /// </summary>
        internal static string AlloctedSuccessfully {
            get {
                return ResourceManager.GetString("AlloctedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allocation failed.
        /// </summary>
        internal static string AlloctionFailed {
            get {
                return ResourceManager.GetString("AlloctionFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All Queue.
        /// </summary>
        internal static string AllQueue {
            get {
                return ResourceManager.GetString("AllQueue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Already Associated Please Select from Drop Down.
        /// </summary>
        internal static string AlreadyAssociatedPleaseSelectfromDropDown {
            get {
                return ResourceManager.GetString("AlreadyAssociatedPleaseSelectfromDropDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to already exists.
        /// </summary>
        internal static string alreadyexists {
            get {
                return ResourceManager.GetString("alreadyexists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to already exists for the location.
        /// </summary>
        internal static string alreadyexistsforthelocation {
            get {
                return ResourceManager.GetString("alreadyexistsforthelocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amount.
        /// </summary>
        internal static string Amount {
            get {
                return ResourceManager.GetString("Amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amount is  blank.
        /// </summary>
        internal static string AmountBlank {
            get {
                return ResourceManager.GetString("AmountBlank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amount Is Beyond Acceptable Limit.
        /// </summary>
        internal static string AmountIsBeyondAcceptableLimit {
            get {
                return ResourceManager.GetString("AmountIsBeyondAcceptableLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amount should be less than.
        /// </summary>
        internal static string AmountShouldbeLessThan {
            get {
                return ResourceManager.GetString("AmountShouldbeLessThan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AND.
        /// </summary>
        internal static string AND {
            get {
                return ResourceManager.GetString("AND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approval Limit.
        /// </summary>
        internal static string ApprovalLimit {
            get {
                return ResourceManager.GetString("ApprovalLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Apr.
        /// </summary>
        internal static string April {
            get {
                return ResourceManager.GetString("April", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are sure want to delete.
        /// </summary>
        internal static string Aresurewanttodelete {
            get {
                return ResourceManager.GetString("Aresurewanttodelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to cancel?.
        /// </summary>
        internal static string Areyousurewanttocancel {
            get {
                return ResourceManager.GetString("Areyousurewanttocancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete?.
        /// </summary>
        internal static string Areyousurewanttodelete {
            get {
                return ResourceManager.GetString("Areyousurewanttodelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to abandon the request.
        /// </summary>
        internal static string AreyousureyouwanttoAbandontheRequest {
            get {
                return ResourceManager.GetString("AreyousureyouwanttoAbandontheRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to logout.
        /// </summary>
        internal static string AreyousureyouwanttoLogout {
            get {
                return ResourceManager.GetString("AreyousureyouwanttoLogout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to move the event?.
        /// </summary>
        internal static string areyousureyouwanttomovetheevent {
            get {
                return ResourceManager.GetString("areyousureyouwanttomovetheevent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assign.
        /// </summary>
        internal static string Assign {
            get {
                return ResourceManager.GetString("Assign", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assigned To.
        /// </summary>
        internal static string AssignedTo {
            get {
                return ResourceManager.GetString("AssignedTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assign To.
        /// </summary>
        internal static string AssignTo {
            get {
                return ResourceManager.GetString("AssignTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Atleast select one detail.
        /// </summary>
        internal static string Atleastselectonedetail {
            get {
                return ResourceManager.GetString("Atleastselectonedetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aug.
        /// </summary>
        internal static string August {
            get {
                return ResourceManager.GetString("August", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorized Signatory.
        /// </summary>
        internal static string AuthorizedSignatory {
            get {
                return ResourceManager.GetString("AuthorizedSignatory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auto Allocate.
        /// </summary>
        internal static string autoallocate {
            get {
                return ResourceManager.GetString("autoallocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auto Allocation Allowed.
        /// </summary>
        internal static string AutoAllocationAllowed {
            get {
                return ResourceManager.GetString("AutoAllocationAllowed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Average Resolution Time.
        /// </summary>
        internal static string AverageResolutionTime {
            get {
                return ResourceManager.GetString("AverageResolutionTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Average Resolution Time - Year Wise.
        /// </summary>
        internal static string averageresolutiontimeyearwise {
            get {
                return ResourceManager.GetString("averageresolutiontimeyearwise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Average Response Time.
        /// </summary>
        internal static string averageresponsetime {
            get {
                return ResourceManager.GetString("averageresponsetime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Average Response Time -Year Wise.
        /// </summary>
        internal static string averageresponsetimeyearwise {
            get {
                return ResourceManager.GetString("averageresponsetimeyearwise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Average Time.
        /// </summary>
        internal static string AverageTime {
            get {
                return ResourceManager.GetString("AverageTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Avg Resolution Time.
        /// </summary>
        internal static string AvgResolutionTime {
            get {
                return ResourceManager.GetString("AvgResolutionTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bank Name.
        /// </summary>
        internal static string BankName {
            get {
                return ResourceManager.GetString("BankName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bar Chart.
        /// </summary>
        internal static string BarChart {
            get {
                return ResourceManager.GetString("BarChart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Branch.
        /// </summary>
        internal static string Branch {
            get {
                return ResourceManager.GetString("Branch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Branch Association.
        /// </summary>
        internal static string BranchAssociation {
            get {
                return ResourceManager.GetString("BranchAssociation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Branch Detail.
        /// </summary>
        internal static string branchdetail {
            get {
                return ResourceManager.GetString("branchdetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Branch Name.
        /// </summary>
        internal static string BranchName {
            get {
                return ResourceManager.GetString("BranchName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Branch Name.
        /// </summary>
        internal static string BranchNameisalreadypresent {
            get {
                return ResourceManager.GetString("BranchNameisalreadypresent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Branch Tax Code.
        /// </summary>
        internal static string BranchTaxCode {
            get {
                return ResourceManager.GetString("BranchTaxCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Branch Tax Details.
        /// </summary>
        internal static string BranchTaxDetails {
            get {
                return ResourceManager.GetString("BranchTaxDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Brand.
        /// </summary>
        internal static string Brand {
            get {
                return ResourceManager.GetString("Brand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Brand Name.
        /// </summary>
        internal static string BrandName {
            get {
                return ResourceManager.GetString("BrandName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Brands Association.
        /// </summary>
        internal static string BrandsAssociation {
            get {
                return ResourceManager.GetString("BrandsAssociation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Break.
        /// </summary>
        internal static string Break {
            get {
                return ResourceManager.GetString("Break", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Break Hours cannot be greator than Working Hours.
        /// </summary>
        internal static string BreakHourscannotbegreatorthanWorkingHours {
            get {
                return ResourceManager.GetString("BreakHourscannotbegreatorthanWorkingHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse.
        /// </summary>
        internal static string Browse {
            get {
                return ResourceManager.GetString("Browse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calculate Formula.
        /// </summary>
        internal static string calculateformula {
            get {
                return ResourceManager.GetString("calculateformula", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calendar.
        /// </summary>
        internal static string Calendar {
            get {
                return ResourceManager.GetString("Calendar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Call Back Date.
        /// </summary>
        internal static string CallBackDate {
            get {
                return ResourceManager.GetString("CallBackDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Call Back Time.
        /// </summary>
        internal static string CallBackTime {
            get {
                return ResourceManager.GetString("CallBackTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Call Closure Date.
        /// </summary>
        internal static string CallClosureDate {
            get {
                return ResourceManager.GetString("CallClosureDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Call Closure Time.
        /// </summary>
        internal static string CallClosureTime {
            get {
                return ResourceManager.GetString("CallClosureTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Call Date.
        /// </summary>
        internal static string CallDate {
            get {
                return ResourceManager.GetString("CallDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Call Date and Time cannot be greater than current Date and Time.
        /// </summary>
        internal static string CallDateCanNotBeGreaterThanCurrentDate {
            get {
                return ResourceManager.GetString("CallDateCanNotBeGreaterThanCurrentDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Call Description.
        /// </summary>
        internal static string CallDescription {
            get {
                return ResourceManager.GetString("CallDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Call Details.
        /// </summary>
        internal static string CallDetails {
            get {
                return ResourceManager.GetString("CallDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Call Mode.
        /// </summary>
        internal static string CallMode {
            get {
                return ResourceManager.GetString("CallMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Call Nature.
        /// </summary>
        internal static string CallNature {
            get {
                return ResourceManager.GetString("CallNature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Call Status.
        /// </summary>
        internal static string CallStatus {
            get {
                return ResourceManager.GetString("CallStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Call Time.
        /// </summary>
        internal static string CallTime {
            get {
                return ResourceManager.GetString("CallTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Call Type.
        /// </summary>
        internal static string CallType {
            get {
                return ResourceManager.GetString("CallType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        internal static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot close the job without product details.
        /// </summary>
        internal static string cannotclosethejobwithoutproductdetails {
            get {
                return ResourceManager.GetString("cannotclosethejobwithoutproductdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot delete as Tax Type is referenced.
        /// </summary>
        internal static string CannotdeleteasTaxTypeisreferenced {
            get {
                return ResourceManager.GetString("CannotdeleteasTaxTypeisreferenced", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot delete in edit mode.
        /// </summary>
        internal static string CannotDeleteinEditMode {
            get {
                return ResourceManager.GetString("CannotDeleteinEditMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Caps Lock is on.
        /// </summary>
        internal static string capslockison {
            get {
                return ResourceManager.GetString("capslockison", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Case Progress.
        /// </summary>
        internal static string CaseProgress {
            get {
                return ResourceManager.GetString("CaseProgress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Case Progress History.
        /// </summary>
        internal static string CaseProgressHistory {
            get {
                return ResourceManager.GetString("CaseProgressHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cause of Failure.
        /// </summary>
        internal static string CauseofFailure {
            get {
                return ResourceManager.GetString("CauseofFailure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CC To Assignee.
        /// </summary>
        internal static string CCToAssignee {
            get {
                return ResourceManager.GetString("CCToAssignee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change Password.
        /// </summary>
        internal static string ChangePassword {
            get {
                return ResourceManager.GetString("ChangePassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to cancel?.
        /// </summary>
        internal static string Changeswillbelostdoyouwanttoproceed {
            get {
                return ResourceManager.GetString("Changeswillbelostdoyouwanttoproceed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choose column names.
        /// </summary>
        internal static string ChooseColumnNames {
            get {
                return ResourceManager.GetString("ChooseColumnNames", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clear Formula.
        /// </summary>
        internal static string clearformula {
            get {
                return ResourceManager.GetString("clearformula", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close.
        /// </summary>
        internal static string close {
            get {
                return ResourceManager.GetString("close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Closed.
        /// </summary>
        internal static string Closed {
            get {
                return ResourceManager.GetString("Closed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Closed Count.
        /// </summary>
        internal static string ClosedCount {
            get {
                return ResourceManager.GetString("ClosedCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Closure Details.
        /// </summary>
        internal static string ClosureDetails {
            get {
                return ResourceManager.GetString("ClosureDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Closure Reason.
        /// </summary>
        internal static string closurereason {
            get {
                return ResourceManager.GetString("closurereason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Closure Type.
        /// </summary>
        internal static string ClosureType {
            get {
                return ResourceManager.GetString("ClosureType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code.
        /// </summary>
        internal static string code {
            get {
                return ResourceManager.GetString("code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Column Names.
        /// </summary>
        internal static string ColumnNames {
            get {
                return ResourceManager.GetString("ColumnNames", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Commissioning Date.
        /// </summary>
        internal static string commissioningdate {
            get {
                return ResourceManager.GetString("commissioningdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company.
        /// </summary>
        internal static string Company {
            get {
                return ResourceManager.GetString("Company", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Brands.
        /// </summary>
        internal static string CompanyBrands {
            get {
                return ResourceManager.GetString("CompanyBrands", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Calender.
        /// </summary>
        internal static string CompanyCalender {
            get {
                return ResourceManager.GetString("CompanyCalender", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Financial Year.
        /// </summary>
        internal static string CompanyFinancialYear {
            get {
                return ResourceManager.GetString("CompanyFinancialYear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Header.
        /// </summary>
        internal static string CompanyHeader {
            get {
                return ResourceManager.GetString("CompanyHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Master.
        /// </summary>
        internal static string CompanyMaster {
            get {
                return ResourceManager.GetString("CompanyMaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Name.
        /// </summary>
        internal static string CompanyName {
            get {
                return ResourceManager.GetString("CompanyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company-Company Relation.
        /// </summary>
        internal static string CompanyRelation {
            get {
                return ResourceManager.GetString("CompanyRelation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Relationships.
        /// </summary>
        internal static string CompanyRelationships {
            get {
                return ResourceManager.GetString("CompanyRelationships", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company header saved success fully, Please associate atleast one Brand, Please Create One Branch, Employee and associate Employeee to Branch.
        /// </summary>
        internal static string CompanySavedPleaseAssociateatleastoneBrand {
            get {
                return ResourceManager.GetString("CompanySavedPleaseAssociateatleastoneBrand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Tax Code.
        /// </summary>
        internal static string CompanyTaxCode {
            get {
                return ResourceManager.GetString("CompanyTaxCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Tax Details.
        /// </summary>
        internal static string CompanyTaxDetails {
            get {
                return ResourceManager.GetString("CompanyTaxDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Terms.
        /// </summary>
        internal static string CompanyTerms {
            get {
                return ResourceManager.GetString("CompanyTerms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Theme.
        /// </summary>
        internal static string CompanyTheme {
            get {
                return ResourceManager.GetString("CompanyTheme", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Type.
        /// </summary>
        internal static string CompanyType {
            get {
                return ResourceManager.GetString("CompanyType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Completed.
        /// </summary>
        internal static string Completed {
            get {
                return ResourceManager.GetString("Completed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Completed Count.
        /// </summary>
        internal static string CompletedCount {
            get {
                return ResourceManager.GetString("CompletedCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Complete entering detail.
        /// </summary>
        internal static string CompleteEnteringDetail {
            get {
                return ResourceManager.GetString("CompleteEnteringDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Complete entering parts detail.
        /// </summary>
        internal static string CompleteEnteringDetailParts {
            get {
                return ResourceManager.GetString("CompleteEnteringDetailParts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Complete entering service detail.
        /// </summary>
        internal static string CompleteEnteringDetailService {
            get {
                return ResourceManager.GetString("CompleteEnteringDetailService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Complete entering sundry detail.
        /// </summary>
        internal static string CompleteEnteringDetailSundry {
            get {
                return ResourceManager.GetString("CompleteEnteringDetailSundry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Component Details.
        /// </summary>
        internal static string componentdetails {
            get {
                return ResourceManager.GetString("componentdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Password.
        /// </summary>
        internal static string ConfirmPassword {
            get {
                return ResourceManager.GetString("ConfirmPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Person.
        /// </summary>
        internal static string ContactPerson {
            get {
                return ResourceManager.GetString("ContactPerson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Persons.
        /// </summary>
        internal static string ContactPersons {
            get {
                return ResourceManager.GetString("ContactPersons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy Role From.
        /// </summary>
        internal static string CopyRoleFrom {
            get {
                return ResourceManager.GetString("CopyRoleFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Corrective Action.
        /// </summary>
        internal static string CorrectiveAction {
            get {
                return ResourceManager.GetString("CorrectiveAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Count.
        /// </summary>
        internal static string Count {
            get {
                return ResourceManager.GetString("Count", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country.
        /// </summary>
        internal static string Country {
            get {
                return ResourceManager.GetString("Country", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Created.
        /// </summary>
        internal static string Created {
            get {
                return ResourceManager.GetString("Created", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create JobCard.
        /// </summary>
        internal static string CreateJobCard {
            get {
                return ResourceManager.GetString("CreateJobCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create New.
        /// </summary>
        internal static string CreateNew {
            get {
                return ResourceManager.GetString("CreateNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create Quotation.
        /// </summary>
        internal static string CreateQuotation {
            get {
                return ResourceManager.GetString("CreateQuotation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create Service Request.
        /// </summary>
        internal static string CreateServiceRequest {
            get {
                return ResourceManager.GetString("CreateServiceRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Critical.
        /// </summary>
        internal static string crictical {
            get {
                return ResourceManager.GetString("crictical", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Critical.
        /// </summary>
        internal static string Critical {
            get {
                return ResourceManager.GetString("Critical", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Currency.
        /// </summary>
        internal static string Currency {
            get {
                return ResourceManager.GetString("Currency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Step.
        /// </summary>
        internal static string CurrentStep {
            get {
                return ResourceManager.GetString("CurrentStep", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer.
        /// </summary>
        internal static string Customer {
            get {
                return ResourceManager.GetString("Customer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Complaint.
        /// </summary>
        internal static string CustomerComplaint {
            get {
                return ResourceManager.GetString("CustomerComplaint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Person.
        /// </summary>
        internal static string CustomerContact {
            get {
                return ResourceManager.GetString("CustomerContact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Person Mobile.
        /// </summary>
        internal static string CustomerContactPhone {
            get {
                return ResourceManager.GetString("CustomerContactPhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Details.
        /// </summary>
        internal static string customerdetails {
            get {
                return ResourceManager.GetString("customerdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Due for Service.
        /// </summary>
        internal static string customerdueservices {
            get {
                return ResourceManager.GetString("customerdueservices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer is locked, do you want to continue?.
        /// </summary>
        internal static string CustomerisLockedDoyouwanttocontinue {
            get {
                return ResourceManager.GetString("CustomerisLockedDoyouwanttocontinue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer is not active.
        /// </summary>
        internal static string Customerisnotactive {
            get {
                return ResourceManager.GetString("Customerisnotactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Location.
        /// </summary>
        internal static string CustomerLocation {
            get {
                return ResourceManager.GetString("CustomerLocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Name.
        /// </summary>
        internal static string customername {
            get {
                return ResourceManager.GetString("customername", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer not found.
        /// </summary>
        internal static string CustomerNotFound {
            get {
                return ResourceManager.GetString("CustomerNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Quotation.
        /// </summary>
        internal static string CustomerQuotation {
            get {
                return ResourceManager.GetString("CustomerQuotation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Quotation Archived.
        /// </summary>
        internal static string CustomerQuotationArchived {
            get {
                return ResourceManager.GetString("CustomerQuotationArchived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quotation Date.
        /// </summary>
        internal static string CustomerQuotationDate {
            get {
                return ResourceManager.GetString("CustomerQuotationDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quotation Number.
        /// </summary>
        internal static string CustomerQuotationNumber {
            get {
                return ResourceManager.GetString("CustomerQuotationNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Quotation Summary.
        /// </summary>
        internal static string CustomerQuotationSummary {
            get {
                return ResourceManager.GetString("CustomerQuotationSummary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Rating.
        /// </summary>
        internal static string CustomerRating {
            get {
                return ResourceManager.GetString("CustomerRating", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Search.
        /// </summary>
        internal static string customersearch {
            get {
                return ResourceManager.GetString("customersearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Service History.
        /// </summary>
        internal static string CustomerServiceHistory {
            get {
                return ResourceManager.GetString("CustomerServiceHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data saved successfully.
        /// </summary>
        internal static string DataSavedSuccessfully {
            get {
                return ResourceManager.GetString("DataSavedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date.
        /// </summary>
        internal static string Date {
            get {
                return ResourceManager.GetString("Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Effective from date cannot be less than current date.
        /// </summary>
        internal static string DateCannotbelessthenCurrentDate {
            get {
                return ResourceManager.GetString("DateCannotbelessthenCurrentDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date cannot be less then or equal to the date of previous records.
        /// </summary>
        internal static string Datecannotbelessthenpreviousdate {
            get {
                return ResourceManager.GetString("Datecannotbelessthenpreviousdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Selected must be greater than previous Customer.
        /// </summary>
        internal static string dateselectedmustbegreaterthenpreviouscustomer {
            get {
                return ResourceManager.GetString("dateselectedmustbegreaterthenpreviouscustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Days Left.
        /// </summary>
        internal static string DaysLeft {
            get {
                return ResourceManager.GetString("DaysLeft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dealer.
        /// </summary>
        internal static string Dealer {
            get {
                return ResourceManager.GetString("Dealer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dealers/Company.
        /// </summary>
        internal static string DealersorCompany {
            get {
                return ResourceManager.GetString("DealersorCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dec.
        /// </summary>
        internal static string December {
            get {
                return ResourceManager.GetString("December", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Grid Size.
        /// </summary>
        internal static string DefaultGridSize {
            get {
                return ResourceManager.GetString("DefaultGridSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default grid size already available.
        /// </summary>
        internal static string Defaultgridsizealreadyavailable {
            get {
                return ResourceManager.GetString("Defaultgridsizealreadyavailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default grid size should be between 0 and 255.
        /// </summary>
        internal static string DefaultgridSizeshouldbebetweenzeroandtwofiftyfive {
            get {
                return ResourceManager.GetString("DefaultgridSizeshouldbebetweenzeroandtwofiftyfive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        internal static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Action.
        /// </summary>
        internal static string DeleteAction {
            get {
                return ResourceManager.GetString("DeleteAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Branch.
        /// </summary>
        internal static string DeleteBranch {
            get {
                return ResourceManager.GetString("DeleteBranch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Branch Tax Details.
        /// </summary>
        internal static string DeleteBranchTaxDetails {
            get {
                return ResourceManager.GetString("DeleteBranchTaxDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Brands.
        /// </summary>
        internal static string DeleteBrands {
            get {
                return ResourceManager.GetString("DeleteBrands", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Company.
        /// </summary>
        internal static string DeleteCompany {
            get {
                return ResourceManager.GetString("DeleteCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Company Relation.
        /// </summary>
        internal static string DeleteCompanyRelation {
            get {
                return ResourceManager.GetString("DeleteCompanyRelation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Company Terms.
        /// </summary>
        internal static string DeleteCompanyTerms {
            get {
                return ResourceManager.GetString("DeleteCompanyTerms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deleted Successfully.
        /// </summary>
        internal static string deletedsuccessfully {
            get {
                return ResourceManager.GetString("deletedsuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Employee.
        /// </summary>
        internal static string DeleteEmployee {
            get {
                return ResourceManager.GetString("DeleteEmployee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Part Free Stock.
        /// </summary>
        internal static string deletefreestock {
            get {
                return ResourceManager.GetString("deletefreestock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Function Group.
        /// </summary>
        internal static string DeleteFunctionGroup {
            get {
                return ResourceManager.GetString("DeleteFunctionGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Job Card.
        /// </summary>
        internal static string DeleteJobCard {
            get {
                return ResourceManager.GetString("DeleteJobCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Master.
        /// </summary>
        internal static string DeleteMaster {
            get {
                return ResourceManager.GetString("DeleteMaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Model.
        /// </summary>
        internal static string deletemodel {
            get {
                return ResourceManager.GetString("deletemodel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Operation.
        /// </summary>
        internal static string deleteoperation {
            get {
                return ResourceManager.GetString("deleteoperation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Operation Employee Details.
        /// </summary>
        internal static string DeleteOperationEmployeeDetails {
            get {
                return ResourceManager.GetString("DeleteOperationEmployeeDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Part.
        /// </summary>
        internal static string deletepart {
            get {
                return ResourceManager.GetString("deletepart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Part Price.
        /// </summary>
        internal static string deletepartprice {
            get {
                return ResourceManager.GetString("deletepartprice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Part product Type.
        /// </summary>
        internal static string deletepartproducttype {
            get {
                return ResourceManager.GetString("deletepartproducttype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Parts.
        /// </summary>
        internal static string DeleteParts {
            get {
                return ResourceManager.GetString("DeleteParts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Party.
        /// </summary>
        internal static string DeleteParty {
            get {
                return ResourceManager.GetString("DeleteParty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Prefix Suffix.
        /// </summary>
        internal static string DeletePrefixSuffix {
            get {
                return ResourceManager.GetString("DeletePrefixSuffix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Product.
        /// </summary>
        internal static string deleteproduct {
            get {
                return ResourceManager.GetString("deleteproduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Product Detail.
        /// </summary>
        internal static string deleteproductdetail {
            get {
                return ResourceManager.GetString("deleteproductdetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Product Type.
        /// </summary>
        internal static string deleteproducttype {
            get {
                return ResourceManager.GetString("deleteproducttype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Quotation.
        /// </summary>
        internal static string DeleteQuotation {
            get {
                return ResourceManager.GetString("DeleteQuotation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Request.
        /// </summary>
        internal static string DeleteRequest {
            get {
                return ResourceManager.GetString("DeleteRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Role.
        /// </summary>
        internal static string DeleteRole {
            get {
                return ResourceManager.GetString("DeleteRole", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Service Charge.
        /// </summary>
        internal static string deleteServiceCharge {
            get {
                return ResourceManager.GetString("deleteServiceCharge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Service Charge Details.
        /// </summary>
        internal static string deleteServiceChargeDetails {
            get {
                return ResourceManager.GetString("deleteServiceChargeDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Service Type.
        /// </summary>
        internal static string DeleteServiceType {
            get {
                return ResourceManager.GetString("DeleteServiceType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Skills.
        /// </summary>
        internal static string DeleteSkills {
            get {
                return ResourceManager.GetString("DeleteSkills", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Specialization.
        /// </summary>
        internal static string DeleteSpecialization {
            get {
                return ResourceManager.GetString("DeleteSpecialization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Step.
        /// </summary>
        internal static string DeleteStep {
            get {
                return ResourceManager.GetString("DeleteStep", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Step Link.
        /// </summary>
        internal static string DeleteStepLink {
            get {
                return ResourceManager.GetString("DeleteStepLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Sundry.
        /// </summary>
        internal static string DeleteSundry {
            get {
                return ResourceManager.GetString("DeleteSundry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Tax Code.
        /// </summary>
        internal static string DeleteTaxCode {
            get {
                return ResourceManager.GetString("DeleteTaxCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Tax Details.
        /// </summary>
        internal static string DeleteTaxDetails {
            get {
                return ResourceManager.GetString("DeleteTaxDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Tax Structure.
        /// </summary>
        internal static string deletetaxstructure {
            get {
                return ResourceManager.GetString("deletetaxstructure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Tax Structure Details.
        /// </summary>
        internal static string deletetaxtstructuredetails {
            get {
                return ResourceManager.GetString("deletetaxtstructuredetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete User.
        /// </summary>
        internal static string DeleteUser {
            get {
                return ResourceManager.GetString("DeleteUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Warranty Details.
        /// </summary>
        internal static string deletewarrantydetails {
            get {
                return ResourceManager.GetString("deletewarrantydetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Work Details.
        /// </summary>
        internal static string DeleteWorkDetails {
            get {
                return ResourceManager.GetString("DeleteWorkDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delivery Date.
        /// </summary>
        internal static string DeliveryDate {
            get {
                return ResourceManager.GetString("DeliveryDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Department.
        /// </summary>
        internal static string Department {
            get {
                return ResourceManager.GetString("Department", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dependency found cannot delete the records.
        /// </summary>
        internal static string Dependencyfoundcannotdeletetherecords {
            get {
                return ResourceManager.GetString("Dependencyfoundcannotdeletetherecords", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description.
        /// </summary>
        internal static string description {
            get {
                return ResourceManager.GetString("description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Designation.
        /// </summary>
        internal static string Designation {
            get {
                return ResourceManager.GetString("Designation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Destination Columns.
        /// </summary>
        internal static string DestinationColumns {
            get {
                return ResourceManager.GetString("DestinationColumns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Detail.
        /// </summary>
        internal static string Detail {
            get {
                return ResourceManager.GetString("Detail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deviation Hours.
        /// </summary>
        internal static string DeviationHours {
            get {
                return ResourceManager.GetString("DeviationHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deviation%.
        /// </summary>
        internal static string DeviationPercentage {
            get {
                return ResourceManager.GetString("DeviationPercentage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discount.
        /// </summary>
        internal static string Discount {
            get {
                return ResourceManager.GetString("Discount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discount Amount.
        /// </summary>
        internal static string Discountamount {
            get {
                return ResourceManager.GetString("Discountamount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discounted Amount.
        /// </summary>
        internal static string DiscountedAmount {
            get {
                return ResourceManager.GetString("DiscountedAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discount Percentage.
        /// </summary>
        internal static string DiscountPercentage {
            get {
                return ResourceManager.GetString("DiscountPercentage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discount should be less than.
        /// </summary>
        internal static string DiscountShouldbeLessThan {
            get {
                return ResourceManager.GetString("DiscountShouldbeLessThan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application error occured.
        /// </summary>
        internal static string DivideByZeroException {
            get {
                return ResourceManager.GetString("DivideByZeroException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document Export.
        /// </summary>
        internal static string DocumentExport {
            get {
                return ResourceManager.GetString("DocumentExport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do not enter space.
        /// </summary>
        internal static string DonotEnterSpace {
            get {
                return ResourceManager.GetString("DonotEnterSpace", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to add this serial number.
        /// </summary>
        internal static string DoyouwanttoaddthisSerialNumber {
            get {
                return ResourceManager.GetString("DoyouwanttoaddthisSerialNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to change the association?.
        /// </summary>
        internal static string Doyouwanttochangetheassociation {
            get {
                return ResourceManager.GetString("Doyouwanttochangetheassociation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to create New Version?.
        /// </summary>
        internal static string Doyouwanttocreatenewversion {
            get {
                return ResourceManager.GetString("Doyouwanttocreatenewversion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Due Date.
        /// </summary>
        internal static string DueDate {
            get {
                return ResourceManager.GetString("DueDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Due date cannot be blank.
        /// </summary>
        internal static string DueDatecannotbeBlank {
            get {
                return ResourceManager.GetString("DueDatecannotbeBlank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Due Range.
        /// </summary>
        internal static string DueRange {
            get {
                return ResourceManager.GetString("DueRange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate.
        /// </summary>
        internal static string Duplicate {
            get {
                return ResourceManager.GetString("Duplicate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Branch.
        /// </summary>
        internal static string duplicatebranch {
            get {
                return ResourceManager.GetString("duplicatebranch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Branch name is already present.
        /// </summary>
        internal static string DuplicateBranchName {
            get {
                return ResourceManager.GetString("DuplicateBranchName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Brand.
        /// </summary>
        internal static string DuplicateBrand {
            get {
                return ResourceManager.GetString("DuplicateBrand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Code.
        /// </summary>
        internal static string DuplicateCode {
            get {
                return ResourceManager.GetString("DuplicateCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Description.
        /// </summary>
        internal static string DuplicateCode1 {
            get {
                return ResourceManager.GetString("DuplicateCode1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Code and Description.
        /// </summary>
        internal static string DuplicateCodeandDescription {
            get {
                return ResourceManager.GetString("DuplicateCodeandDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Code and Description.
        /// </summary>
        internal static string DuplicateCodeandDescription1 {
            get {
                return ResourceManager.GetString("DuplicateCodeandDescription1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Company Name.
        /// </summary>
        internal static string DuplicateCompanyName {
            get {
                return ResourceManager.GetString("DuplicateCompanyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Date.
        /// </summary>
        internal static string duplicatedate {
            get {
                return ResourceManager.GetString("duplicatedate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate description.
        /// </summary>
        internal static string DuplicateDescription {
            get {
                return ResourceManager.GetString("DuplicateDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate emails found.
        /// </summary>
        internal static string DuplicateEmailsFound {
            get {
                return ResourceManager.GetString("DuplicateEmailsFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Employee.
        /// </summary>
        internal static string DuplicateEmployee {
            get {
                return ResourceManager.GetString("DuplicateEmployee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Entries.
        /// </summary>
        internal static string Duplicateentries {
            get {
                return ResourceManager.GetString("Duplicateentries", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate.
        /// </summary>
        internal static string Duplicateentriesof {
            get {
                return ResourceManager.GetString("Duplicateentriesof", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Function Group.
        /// </summary>
        internal static string DuplicateFunctionGroup {
            get {
                return ResourceManager.GetString("DuplicateFunctionGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Login Id.
        /// </summary>
        internal static string DuplicateLoginId {
            get {
                return ResourceManager.GetString("DuplicateLoginId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Master.
        /// </summary>
        internal static string DuplicateMaster {
            get {
                return ResourceManager.GetString("DuplicateMaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate model.
        /// </summary>
        internal static string duplicatemodel {
            get {
                return ResourceManager.GetString("duplicatemodel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Module.
        /// </summary>
        internal static string DuplicateModule {
            get {
                return ResourceManager.GetString("DuplicateModule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Operation Code.
        /// </summary>
        internal static string DuplicateOperationCode {
            get {
                return ResourceManager.GetString("DuplicateOperationCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate part number.
        /// </summary>
        internal static string Duplicatepartnumber {
            get {
                return ResourceManager.GetString("Duplicatepartnumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate phone numbers found.
        /// </summary>
        internal static string DuplicatePhoneNumbersFound {
            get {
                return ResourceManager.GetString("DuplicatePhoneNumbersFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate product type.
        /// </summary>
        internal static string duplicateproducttype {
            get {
                return ResourceManager.GetString("duplicateproducttype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Role.
        /// </summary>
        internal static string DuplicateRole {
            get {
                return ResourceManager.GetString("DuplicateRole", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate secondary segment.
        /// </summary>
        internal static string duplicatesecondarysegment {
            get {
                return ResourceManager.GetString("duplicatesecondarysegment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Service code.
        /// </summary>
        internal static string Duplicateservicecode {
            get {
                return ResourceManager.GetString("Duplicateservicecode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate service date.
        /// </summary>
        internal static string duplicateservicedate {
            get {
                return ResourceManager.GetString("duplicateservicedate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate service type.
        /// </summary>
        internal static string duplicateservicetype {
            get {
                return ResourceManager.GetString("duplicateservicetype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate service type is not allowed.
        /// </summary>
        internal static string DuplicateServiceTypeisnotallowed {
            get {
                return ResourceManager.GetString("DuplicateServiceTypeisnotallowed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate state.
        /// </summary>
        internal static string duplicatestate {
            get {
                return ResourceManager.GetString("duplicatestate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate sundry description is not allowed.
        /// </summary>
        internal static string DuplicateSundryDescriptionisnotAllowed {
            get {
                return ResourceManager.GetString("DuplicateSundryDescriptionisnotAllowed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate tax name.
        /// </summary>
        internal static string DuplicateTaxname {
            get {
                return ResourceManager.GetString("DuplicateTaxname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate tax type.
        /// </summary>
        internal static string DuplicateTaxType {
            get {
                return ResourceManager.GetString("DuplicateTaxType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Unique identifier.
        /// </summary>
        internal static string DuplicateUniqueidentifier {
            get {
                return ResourceManager.GetString("DuplicateUniqueidentifier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit.
        /// </summary>
        internal static string edit {
            get {
                return ResourceManager.GetString("edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Action.
        /// </summary>
        internal static string EditAction {
            get {
                return ResourceManager.GetString("EditAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Company.
        /// </summary>
        internal static string EditCompany {
            get {
                return ResourceManager.GetString("EditCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Component Details.
        /// </summary>
        internal static string editcomponentdetails {
            get {
                return ResourceManager.GetString("editcomponentdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Events.
        /// </summary>
        internal static string EditEvents {
            get {
                return ResourceManager.GetString("EditEvents", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Job Card.
        /// </summary>
        internal static string EditJobCard {
            get {
                return ResourceManager.GetString("EditJobCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Model.
        /// </summary>
        internal static string editmodel {
            get {
                return ResourceManager.GetString("editmodel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Operation.
        /// </summary>
        internal static string editoperation {
            get {
                return ResourceManager.GetString("editoperation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Operation Details.
        /// </summary>
        internal static string EditOperationDetails {
            get {
                return ResourceManager.GetString("EditOperationDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Parts Master.
        /// </summary>
        internal static string EditPartsMaster {
            get {
                return ResourceManager.GetString("EditPartsMaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Product.
        /// </summary>
        internal static string editproduct {
            get {
                return ResourceManager.GetString("editproduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Product Type.
        /// </summary>
        internal static string editproducttype {
            get {
                return ResourceManager.GetString("editproducttype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Secondary Segment.
        /// </summary>
        internal static string editsecondarysegment {
            get {
                return ResourceManager.GetString("editsecondarysegment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Service Charges.
        /// </summary>
        internal static string editsevicecharges {
            get {
                return ResourceManager.GetString("editsevicecharges", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Site Address.
        /// </summary>
        internal static string editsiteaddress {
            get {
                return ResourceManager.GetString("editsiteaddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit State.
        /// </summary>
        internal static string editstate {
            get {
                return ResourceManager.GetString("editstate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Tax Structure.
        /// </summary>
        internal static string edittaxstructure {
            get {
                return ResourceManager.GetString("edittaxstructure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Warranty Deatils.
        /// </summary>
        internal static string editwarrantydeatils {
            get {
                return ResourceManager.GetString("editwarrantydeatils", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Effective From.
        /// </summary>
        internal static string effectivefrom {
            get {
                return ResourceManager.GetString("effectivefrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Less Than 8 Hours.
        /// </summary>
        internal static string EightHour {
            get {
                return ResourceManager.GetString("EightHour", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 8 To 16 Hours.
        /// </summary>
        internal static string EightToSixteenHours {
            get {
                return ResourceManager.GetString("EightToSixteenHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        internal static string Email {
            get {
                return ResourceManager.GetString("Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email To Addresse.
        /// </summary>
        internal static string EmailToAddresse {
            get {
                return ResourceManager.GetString("EmailToAddresse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email To Customer.
        /// </summary>
        internal static string EmailToCustomer {
            get {
                return ResourceManager.GetString("EmailToCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee.
        /// </summary>
        internal static string Employee {
            get {
                return ResourceManager.GetString("Employee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee - Branch.
        /// </summary>
        internal static string EmployeeBranch {
            get {
                return ResourceManager.GetString("EmployeeBranch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee Details.
        /// </summary>
        internal static string EmployeeDetails {
            get {
                return ResourceManager.GetString("EmployeeDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee Code.
        /// </summary>
        internal static string EmployeeID {
            get {
                return ResourceManager.GetString("EmployeeID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Employee code.
        /// </summary>
        internal static string EmployeeIDisalreadyused {
            get {
                return ResourceManager.GetString("EmployeeIDisalreadyused", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee is already associated with the branch.
        /// </summary>
        internal static string EmployeeisalreadyassociatedwiththeBranch {
            get {
                return ResourceManager.GetString("EmployeeisalreadyassociatedwiththeBranch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee is already associated with the specialization.
        /// </summary>
        internal static string EmployeeisalreadyassociatedwiththeSpecialization {
            get {
                return ResourceManager.GetString("EmployeeisalreadyassociatedwiththeSpecialization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee.
        /// </summary>
        internal static string EmployeeName {
            get {
                return ResourceManager.GetString("EmployeeName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee not found.
        /// </summary>
        internal static string EmployeeNotFound {
            get {
                return ResourceManager.GetString("EmployeeNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Skills.
        /// </summary>
        internal static string EmployeeSkills {
            get {
                return ResourceManager.GetString("EmployeeSkills", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Specialization.
        /// </summary>
        internal static string EmployeeSpecialization {
            get {
                return ResourceManager.GetString("EmployeeSpecialization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End date cannot be less than start date.
        /// </summary>
        internal static string EndDatecannotbelessthanStartDate {
            get {
                return ResourceManager.GetString("EndDatecannotbelessthanStartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to English.
        /// </summary>
        internal static string English {
            get {
                return ResourceManager.GetString("English", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enquiry Date.
        /// </summary>
        internal static string EnquiryDate {
            get {
                return ResourceManager.GetString("EnquiryDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enquiry Number.
        /// </summary>
        internal static string EnquiryNumber {
            get {
                return ResourceManager.GetString("EnquiryNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Code.
        /// </summary>
        internal static string EnterCode {
            get {
                return ResourceManager.GetString("EnterCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Description.
        /// </summary>
        internal static string EnterDescription {
            get {
                return ResourceManager.GetString("EnterDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Entered number doesnot belongs to customer or prospect.
        /// </summary>
        internal static string EnteredNumberdoesnotbelongstocustomerorprospect {
            get {
                return ResourceManager.GetString("EnteredNumberdoesnotbelongstocustomerorprospect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter From Date.
        /// </summary>
        internal static string enterfromdate {
            get {
                return ResourceManager.GetString("enterfromdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter master name.
        /// </summary>
        internal static string EnterMasterName {
            get {
                return ResourceManager.GetString("EnterMasterName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter non taxable other charges 1.
        /// </summary>
        internal static string enterNonTaxableothercharges1 {
            get {
                return ResourceManager.GetString("enterNonTaxableothercharges1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter non taxable other charges 2.
        /// </summary>
        internal static string enterNonTaxableothercharges2 {
            get {
                return ResourceManager.GetString("enterNonTaxableothercharges2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter taxable other charges 1.
        /// </summary>
        internal static string enterTaxableothercharges1 {
            get {
                return ResourceManager.GetString("enterTaxableothercharges1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter taxable other charges 2.
        /// </summary>
        internal static string enterTaxableothercharges2 {
            get {
                return ResourceManager.GetString("enterTaxableothercharges2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Year.
        /// </summary>
        internal static string enteryear {
            get {
                return ResourceManager.GetString("enteryear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Entry Tax Percentage.
        /// </summary>
        internal static string EntryTaxPercentage {
            get {
                return ResourceManager.GetString("EntryTaxPercentage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Equal.
        /// </summary>
        internal static string Equal {
            get {
                return ResourceManager.GetString("Equal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        internal static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error in uploaded parts. Please open excel.
        /// </summary>
        internal static string ErrorinUploadedPartsPleaseOpenExcel {
            get {
                return ResourceManager.GetString("ErrorinUploadedPartsPleaseOpenExcel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error occured while locking.
        /// </summary>
        internal static string ErrorOccuredwhileLocking {
            get {
                return ResourceManager.GetString("ErrorOccuredwhileLocking", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error Saving.
        /// </summary>
        internal static string ErrorSaving {
            get {
                return ResourceManager.GetString("ErrorSaving", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Escalate To.
        /// </summary>
        internal static string EscalateTo {
            get {
                return ResourceManager.GetString("EscalateTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Escalation Hours.
        /// </summary>
        internal static string EscalationHours {
            get {
                return ResourceManager.GetString("EscalationHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Event Name.
        /// </summary>
        internal static string EventName {
            get {
                return ResourceManager.GetString("EventName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Events.
        /// </summary>
        internal static string Events {
            get {
                return ResourceManager.GetString("Events", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Event start date cannot be less than Job card date.
        /// </summary>
        internal static string EventStartDateCannotbeLessthanJobCardDate {
            get {
                return ResourceManager.GetString("EventStartDateCannotbeLessthanJobCardDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Event start time coincides with other events, do you want to continue?.
        /// </summary>
        internal static string Eventstarttimecoincideswithothereventsdoyouwanttocontinue {
            get {
                return ResourceManager.GetString("Eventstarttimecoincideswithothereventsdoyouwanttocontinue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Excel.
        /// </summary>
        internal static string Excel {
            get {
                return ResourceManager.GetString("Excel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export.
        /// </summary>
        internal static string Export {
            get {
                return ResourceManager.GetString("Export", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export Action.
        /// </summary>
        internal static string ExportAction {
            get {
                return ResourceManager.GetString("ExportAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export to Document.
        /// </summary>
        internal static string ExporttoDocument {
            get {
                return ResourceManager.GetString("ExporttoDocument", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to save new contact person.
        /// </summary>
        internal static string FailedtosavenewContactPerson {
            get {
                return ResourceManager.GetString("FailedtosavenewContactPerson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to save new party.
        /// </summary>
        internal static string FailedtosavenewParty {
            get {
                return ResourceManager.GetString("FailedtosavenewParty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to save new serial number.
        /// </summary>
        internal static string FailedtosavenewSerialNumber {
            get {
                return ResourceManager.GetString("FailedtosavenewSerialNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to save serial.
        /// </summary>
        internal static string FailedToSaveSerial {
            get {
                return ResourceManager.GetString("FailedToSaveSerial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FAX.
        /// </summary>
        internal static string FAX {
            get {
                return ResourceManager.GetString("FAX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Feb.
        /// </summary>
        internal static string February {
            get {
                return ResourceManager.GetString("February", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fields highlighted are mandatory.
        /// </summary>
        internal static string Fieldshighlightedaremandatory {
            get {
                return ResourceManager.GetString("Fieldshighlightedaremandatory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fields marked with * are mandatory.
        /// </summary>
        internal static string FieldsmarkedwithStararemandatory {
            get {
                return ResourceManager.GetString("FieldsmarkedwithStararemandatory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filter.
        /// </summary>
        internal static string Filter {
            get {
                return ResourceManager.GetString("Filter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filter All Que Branch Specific ?.
        /// </summary>
        internal static string FilterAllQueBranch {
            get {
                return ResourceManager.GetString("FilterAllQueBranch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filter Criteria.
        /// </summary>
        internal static string FilterCriteria {
            get {
                return ResourceManager.GetString("FilterCriteria", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Financial Year.
        /// </summary>
        internal static string FinancialYear {
            get {
                return ResourceManager.GetString("FinancialYear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Financial year already selected.
        /// </summary>
        internal static string financialyearalredyselected {
            get {
                return ResourceManager.GetString("financialyearalredyselected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Financial year cannot be greater than next rows Financial year.
        /// </summary>
        internal static string FinancialyearcannotbeGreaterthannextrowsfinancialyear {
            get {
                return ResourceManager.GetString("FinancialyearcannotbeGreaterthannextrowsfinancialyear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Financial year cannot be less than previous rows Financial year.
        /// </summary>
        internal static string Financialyearcannotbelessthanpreviousrowsfinancialyear {
            get {
                return ResourceManager.GetString("Financialyearcannotbelessthanpreviousrowsfinancialyear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For.
        /// </summary>
        internal static string For {
            get {
                return ResourceManager.GetString("For", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Forgot Password?.
        /// </summary>
        internal static string forgotpassword {
            get {
                return ResourceManager.GetString("forgotpassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Formula.
        /// </summary>
        internal static string formula {
            get {
                return ResourceManager.GetString("formula", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Formula Summary.
        /// </summary>
        internal static string formulasummary {
            get {
                return ResourceManager.GetString("formulasummary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 48 To 90 Hours.
        /// </summary>
        internal static string FourtyEightToNintyHours {
            get {
                return ResourceManager.GetString("FourtyEightToNintyHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Free Hours.
        /// </summary>
        internal static string FreeHours {
            get {
                return ResourceManager.GetString("FreeHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Free Stock.
        /// </summary>
        internal static string freestock {
            get {
                return ResourceManager.GetString("freestock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Friday.
        /// </summary>
        internal static string Friday {
            get {
                return ResourceManager.GetString("Friday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From.
        /// </summary>
        internal static string From {
            get {
                return ResourceManager.GetString("From", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Date.
        /// </summary>
        internal static string fromdate {
            get {
                return ResourceManager.GetString("fromdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Date and To Date cannot be empty.
        /// </summary>
        internal static string FromDateandTodatecannotbeEmpty {
            get {
                return ResourceManager.GetString("FromDateandTodatecannotbeEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Date cannot be greater than To Date.
        /// </summary>
        internal static string FromDatecannotbegreaterthanToDate {
            get {
                return ResourceManager.GetString("FromDatecannotbegreaterthanToDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Date cannot be greater than To Date.
        /// </summary>
        internal static string fromdatecannotbegreaterthentodate {
            get {
                return ResourceManager.GetString("fromdatecannotbegreaterthentodate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Date cannot be greater than To Date.
        /// </summary>
        internal static string fromdatecannotbegreatorthantodate {
            get {
                return ResourceManager.GetString("fromdatecannotbegreatorthantodate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Date cannot be less than or equal to previous rows To Date.
        /// </summary>
        internal static string Fromdatecannotbelessthanorequaltopreviousrowtodate {
            get {
                return ResourceManager.GetString("Fromdatecannotbelessthanorequaltopreviousrowtodate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Date cannot be greater than To Date.
        /// </summary>
        internal static string FromDatecannotbelessthanToDate {
            get {
                return ResourceManager.GetString("FromDatecannotbelessthanToDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Date cannot be less than current date.
        /// </summary>
        internal static string fromdatecannotbelessthencurrentdate {
            get {
                return ResourceManager.GetString("fromdatecannotbelessthencurrentdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Date must be greater than or equal to Issue date.
        /// </summary>
        internal static string fromdatemustbegreaterthanorequaltoissuedate {
            get {
                return ResourceManager.GetString("fromdatemustbegreaterthanorequaltoissuedate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Date must be lesser than To Date.
        /// </summary>
        internal static string fromdatemustbelesserthentodate {
            get {
                return ResourceManager.GetString("fromdatemustbelesserthentodate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Step.
        /// </summary>
        internal static string FromStep {
            get {
                return ResourceManager.GetString("FromStep", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Time cannot be greater than To Time.
        /// </summary>
        internal static string FromTimecannotbegreaterthanToTime {
            get {
                return ResourceManager.GetString("FromTimecannotbegreaterthanToTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Function Group.
        /// </summary>
        internal static string FunctionGroup {
            get {
                return ResourceManager.GetString("FunctionGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Function Group Header.
        /// </summary>
        internal static string FunctionGroupHeader {
            get {
                return ResourceManager.GetString("FunctionGroupHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Function Group ID.
        /// </summary>
        internal static string FunctionGroupID {
            get {
                return ResourceManager.GetString("FunctionGroupID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Function Group Name.
        /// </summary>
        internal static string FunctionGroupName {
            get {
                return ResourceManager.GetString("FunctionGroupName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Function Group Native.
        /// </summary>
        internal static string FunctionGroupNative {
            get {
                return ResourceManager.GetString("FunctionGroupNative", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Function Group Operations.
        /// </summary>
        internal static string FunctionGroupOperations {
            get {
                return ResourceManager.GetString("FunctionGroupOperations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Generate Report.
        /// </summary>
        internal static string GenerateReport {
            get {
                return ResourceManager.GetString("GenerateReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Graph Category Selection.
        /// </summary>
        internal static string GraphCategorySelection {
            get {
                return ResourceManager.GetString("GraphCategorySelection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Graph Type.
        /// </summary>
        internal static string GraphType {
            get {
                return ResourceManager.GetString("GraphType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Greater Than.
        /// </summary>
        internal static string GreaterThan {
            get {
                return ResourceManager.GetString("GreaterThan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group Queue.
        /// </summary>
        internal static string GroupQue {
            get {
                return ResourceManager.GetString("GroupQue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group Queue.
        /// </summary>
        internal static string GroupQueue {
            get {
                return ResourceManager.GetString("GroupQueue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Header.
        /// </summary>
        internal static string Header {
            get {
                return ResourceManager.GetString("Header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Heading.
        /// </summary>
        internal static string Heading {
            get {
                return ResourceManager.GetString("Heading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to High.
        /// </summary>
        internal static string high {
            get {
                return ResourceManager.GetString("high", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Highlighted fields are mandatory.
        /// </summary>
        internal static string HighlightedFieldsareMandatory {
            get {
                return ResourceManager.GetString("HighlightedFieldsareMandatory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HMR.
        /// </summary>
        internal static string HMR {
            get {
                return ResourceManager.GetString("HMR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hold.
        /// </summary>
        internal static string Hold {
            get {
                return ResourceManager.GetString("Hold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Holidays.
        /// </summary>
        internal static string Holidays {
            get {
                return ResourceManager.GetString("Holidays", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ID.
        /// </summary>
        internal static string ID {
            get {
                return ResourceManager.GetString("ID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import.
        /// </summary>
        internal static string Import {
            get {
                return ResourceManager.GetString("Import", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import Action.
        /// </summary>
        internal static string ImportAction {
            get {
                return ResourceManager.GetString("ImportAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Imported successfully.
        /// </summary>
        internal static string ImportedSuccessfully {
            get {
                return ResourceManager.GetString("ImportedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import Into Database.
        /// </summary>
        internal static string ImportIntoDatabase {
            get {
                return ResourceManager.GetString("ImportIntoDatabase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import Parts.
        /// </summary>
        internal static string ImportParts {
            get {
                return ResourceManager.GetString("ImportParts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In Active Product.
        /// </summary>
        internal static string inactiveproduct {
            get {
                return ResourceManager.GetString("inactiveproduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incomplete operations are present, do you want to close the Job card?.
        /// </summary>
        internal static string IncompleteOperationsarepresent {
            get {
                return ResourceManager.GetString("IncompleteOperationsarepresent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incorrect Password.
        /// </summary>
        internal static string IncorrectPassword {
            get {
                return ResourceManager.GetString("IncorrectPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application error occured.
        /// </summary>
        internal static string IndexOutOfRangeException {
            get {
                return ResourceManager.GetString("IndexOutOfRangeException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In Progress.
        /// </summary>
        internal static string InProgress {
            get {
                return ResourceManager.GetString("InProgress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In Progress Count.
        /// </summary>
        internal static string InProgressCount {
            get {
                return ResourceManager.GetString("InProgressCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inserted successfully.
        /// </summary>
        internal static string InsertedSuccessfully {
            get {
                return ResourceManager.GetString("InsertedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Internal.
        /// </summary>
        internal static string Internal {
            get {
                return ResourceManager.GetString("Internal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid.
        /// </summary>
        internal static string Invalid {
            get {
                return ResourceManager.GetString("Invalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prefixsuffix is created as company specific. Cannot change to Branch..
        /// </summary>
        internal static string InvalidBranchSelection {
            get {
                return ResourceManager.GetString("InvalidBranchSelection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application error occured.
        /// </summary>
        internal static string InvalidCastException {
            get {
                return ResourceManager.GetString("InvalidCastException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prefixsuffix is created as Branch specific. Cannot change to Company. .
        /// </summary>
        internal static string InvalidCompanySelection {
            get {
                return ResourceManager.GetString("InvalidCompanySelection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid date.
        /// </summary>
        internal static string InvalidDate {
            get {
                return ResourceManager.GetString("InvalidDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Decimal.
        /// </summary>
        internal static string InvalidDecimal {
            get {
                return ResourceManager.GetString("InvalidDecimal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid email.
        /// </summary>
        internal static string InvalidEmail {
            get {
                return ResourceManager.GetString("InvalidEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid file.
        /// </summary>
        internal static string InvalidFile {
            get {
                return ResourceManager.GetString("InvalidFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Mobile.
        /// </summary>
        internal static string InvalidMobile {
            get {
                return ResourceManager.GetString("InvalidMobile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid model.
        /// </summary>
        internal static string InvalidModel {
            get {
                return ResourceManager.GetString("InvalidModel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid model or model is inactive.
        /// </summary>
        internal static string invalidmodelormodelisinactive {
            get {
                return ResourceManager.GetString("invalidmodelormodelisinactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid party name.
        /// </summary>
        internal static string InvalidName {
            get {
                return ResourceManager.GetString("InvalidName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Database error occured.
        /// </summary>
        internal static string InvalidOperationException {
            get {
                return ResourceManager.GetString("InvalidOperationException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid part number or part number is inactive.
        /// </summary>
        internal static string invalidpartnumberorpartnumberisinactive {
            get {
                return ResourceManager.GetString("invalidpartnumberorpartnumberisinactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Phone.
        /// </summary>
        internal static string InvalidPhone {
            get {
                return ResourceManager.GetString("InvalidPhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Mobile No.
        /// </summary>
        internal static string InvalidPhoneNo {
            get {
                return ResourceManager.GetString("InvalidPhoneNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid product.
        /// </summary>
        internal static string InvalidProduct {
            get {
                return ResourceManager.GetString("InvalidProduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Product Unique Identifier or Product is not active.
        /// </summary>
        internal static string InvalidProductUniqueNumber {
            get {
                return ResourceManager.GetString("InvalidProductUniqueNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Quantity..
        /// </summary>
        internal static string InvalidQuantity {
            get {
                return ResourceManager.GetString("InvalidQuantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Rate..
        /// </summary>
        internal static string InvalidRate {
            get {
                return ResourceManager.GetString("InvalidRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid registered mobile number.
        /// </summary>
        internal static string InvalidRegisteredMobileNumber {
            get {
                return ResourceManager.GetString("InvalidRegisteredMobileNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid selection.
        /// </summary>
        internal static string invalidselection {
            get {
                return ResourceManager.GetString("invalidselection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid serial number.
        /// </summary>
        internal static string InvalidSerialNumber {
            get {
                return ResourceManager.GetString("InvalidSerialNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Service Request Number.
        /// </summary>
        internal static string InvalidServiceRequestNumber {
            get {
                return ResourceManager.GetString("InvalidServiceRequestNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid unique identification number.
        /// </summary>
        internal static string InValidUniqueIdentificationNumber {
            get {
                return ResourceManager.GetString("InValidUniqueIdentificationNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Active?.
        /// </summary>
        internal static string IsActive {
            get {
                return ResourceManager.GetString("IsActive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Admin.
        /// </summary>
        internal static string IsAdmin {
            get {
                return ResourceManager.GetString("IsAdmin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Base Amount Included?.
        /// </summary>
        internal static string isbaseamountincluded {
            get {
                return ResourceManager.GetString("isbaseamountincluded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Company Specific.
        /// </summary>
        internal static string IsCompanySpecific {
            get {
                return ResourceManager.GetString("IsCompanySpecific", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Component.
        /// </summary>
        internal static string iscomponent {
            get {
                return ResourceManager.GetString("iscomponent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IsCustomer.
        /// </summary>
        internal static string IsCustomer {
            get {
                return ResourceManager.GetString("IsCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Default Contact ?.
        /// </summary>
        internal static string IsDefaultContact {
            get {
                return ResourceManager.GetString("IsDefaultContact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Email.
        /// </summary>
        internal static string IsEmail {
            get {
                return ResourceManager.GetString("IsEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is External.
        /// </summary>
        internal static string IsExternal {
            get {
                return ResourceManager.GetString("IsExternal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Hazardous.
        /// </summary>
        internal static string isHazardous {
            get {
                return ResourceManager.GetString("isHazardous", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Head Office.
        /// </summary>
        internal static string IsHeadOffice {
            get {
                return ResourceManager.GetString("IsHeadOffice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to is Inactive.
        /// </summary>
        internal static string isinactive {
            get {
                return ResourceManager.GetString("isinactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Operation Completed.
        /// </summary>
        internal static string IsOperationCompleted {
            get {
                return ResourceManager.GetString("IsOperationCompleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Sms.
        /// </summary>
        internal static string IsSms {
            get {
                return ResourceManager.GetString("IsSms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Issued Date.
        /// </summary>
        internal static string issueddate {
            get {
                return ResourceManager.GetString("issueddate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Issued date sholud be less than or equal to current date.
        /// </summary>
        internal static string issueddatesholudbelessthanorequaltocurrentdate {
            get {
                return ResourceManager.GetString("issueddatesholudbelessthanorequaltocurrentdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Under Break Down?.
        /// </summary>
        internal static string IsUnderBreakDown {
            get {
                return ResourceManager.GetString("IsUnderBreakDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Under Warranty?.
        /// </summary>
        internal static string isunderwarranty {
            get {
                return ResourceManager.GetString("isunderwarranty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Version Allowed?.
        /// </summary>
        internal static string isversionallowed {
            get {
                return ResourceManager.GetString("isversionallowed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Version Enabled ?.
        /// </summary>
        internal static string IsVersionEnabled {
            get {
                return ResourceManager.GetString("IsVersionEnabled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jan.
        /// </summary>
        internal static string January {
            get {
                return ResourceManager.GetString("January", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job amended, will create new version want to proceed?.
        /// </summary>
        internal static string Jobamendedwillcreatenewversionwanttoproceed {
            get {
                return ResourceManager.GetString("Jobamendedwillcreatenewversionwanttoproceed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Card.
        /// </summary>
        internal static string JobCard {
            get {
                return ResourceManager.GetString("JobCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Card Abandon Reason.
        /// </summary>
        internal static string JobCardAbandonReason {
            get {
                return ResourceManager.GetString("JobCardAbandonReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job card Archived.
        /// </summary>
        internal static string JobcardArchived {
            get {
                return ResourceManager.GetString("JobcardArchived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Card Closure Date.
        /// </summary>
        internal static string JobCardClosureDate {
            get {
                return ResourceManager.GetString("JobCardClosureDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Card Cushion Hours.
        /// </summary>
        internal static string JobCardCushionHours {
            get {
                return ResourceManager.GetString("JobCardCushionHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Card Date.
        /// </summary>
        internal static string JobCardDate {
            get {
                return ResourceManager.GetString("JobCardDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Card Delay Reason.
        /// </summary>
        internal static string JobCardDelayReason {
            get {
                return ResourceManager.GetString("JobCardDelayReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Card Field Search.
        /// </summary>
        internal static string JobCardFieldSearch {
            get {
                return ResourceManager.GetString("JobCardFieldSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job card is already created for this service request number.
        /// </summary>
        internal static string JobCardisalreadycreatedforthisServiceRequestNumber {
            get {
                return ResourceManager.GetString("JobCardisalreadycreatedforthisServiceRequestNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Card Number.
        /// </summary>
        internal static string JobCardNumber {
            get {
                return ResourceManager.GetString("JobCardNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job card number not found.
        /// </summary>
        internal static string JobcardNumbernotfound {
            get {
                return ResourceManager.GetString("JobcardNumbernotfound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Card Number Search.
        /// </summary>
        internal static string JobCardNumberSearch {
            get {
                return ResourceManager.GetString("JobCardNumberSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Card Pending Count.
        /// </summary>
        internal static string JobCardPendingCount {
            get {
                return ResourceManager.GetString("JobCardPendingCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Card Status.
        /// </summary>
        internal static string JobCardStatus {
            get {
                return ResourceManager.GetString("JobCardStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Card Summary.
        /// </summary>
        internal static string JobCardSummary {
            get {
                return ResourceManager.GetString("JobCardSummary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Version.
        /// </summary>
        internal static string JobCardVersion {
            get {
                return ResourceManager.GetString("JobCardVersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Card WIP Count.
        /// </summary>
        internal static string JobCardWIPCount {
            get {
                return ResourceManager.GetString("JobCardWIPCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Description.
        /// </summary>
        internal static string JobDescription {
            get {
                return ResourceManager.GetString("JobDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job End Date.
        /// </summary>
        internal static string JobEndDate {
            get {
                return ResourceManager.GetString("JobEndDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Priority.
        /// </summary>
        internal static string JobPriority {
            get {
                return ResourceManager.GetString("JobPriority", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Site Address.
        /// </summary>
        internal static string JobSiteAddress {
            get {
                return ResourceManager.GetString("JobSiteAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Start Date.
        /// </summary>
        internal static string JobStartDate {
            get {
                return ResourceManager.GetString("JobStartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Joined Tables.
        /// </summary>
        internal static string JoinedTables {
            get {
                return ResourceManager.GetString("JoinedTables", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Join With.
        /// </summary>
        internal static string JoinWith {
            get {
                return ResourceManager.GetString("JoinWith", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jul.
        /// </summary>
        internal static string July {
            get {
                return ResourceManager.GetString("July", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jun.
        /// </summary>
        internal static string June {
            get {
                return ResourceManager.GetString("June", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Landline.
        /// </summary>
        internal static string Landline {
            get {
                return ResourceManager.GetString("Landline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Language.
        /// </summary>
        internal static string Language {
            get {
                return ResourceManager.GetString("Language", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Language.
        /// </summary>
        internal static string LanguageName {
            get {
                return ResourceManager.GetString("LanguageName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Less Than.
        /// </summary>
        internal static string LessThan {
            get {
                return ResourceManager.GetString("LessThan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Like.
        /// </summary>
        internal static string Like {
            get {
                return ResourceManager.GetString("Like", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loading.
        /// </summary>
        internal static string Loading {
            get {
                return ResourceManager.GetString("Loading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Local.
        /// </summary>
        internal static string Local {
            get {
                return ResourceManager.GetString("Local", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Locale.
        /// </summary>
        internal static string Locale {
            get {
                return ResourceManager.GetString("Locale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Locale Details.
        /// </summary>
        internal static string LocaleDetails {
            get {
                return ResourceManager.GetString("LocaleDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Locale details are not avaliable for brand.
        /// </summary>
        internal static string LocaledetailsarenotavaliableforBrand {
            get {
                return ResourceManager.GetString("LocaledetailsarenotavaliableforBrand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Locale details are not avaliable for product model.
        /// </summary>
        internal static string LocaledetailsarenotavaliableforProductModel {
            get {
                return ResourceManager.GetString("LocaledetailsarenotavaliableforProductModel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Locale details are not avaliable for product type.
        /// </summary>
        internal static string LocaledetailsarenotavaliableforProductType {
            get {
                return ResourceManager.GetString("LocaledetailsarenotavaliableforProductType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Location.
        /// </summary>
        internal static string Location {
            get {
                return ResourceManager.GetString("Location", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lock.
        /// </summary>
        internal static string Lock {
            get {
                return ResourceManager.GetString("Lock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Locked.
        /// </summary>
        internal static string locked {
            get {
                return ResourceManager.GetString("locked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Locked By.
        /// </summary>
        internal static string lockedby {
            get {
                return ResourceManager.GetString("lockedby", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login.
        /// </summary>
        internal static string Login {
            get {
                return ResourceManager.GetString("Login", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login ID.
        /// </summary>
        internal static string LoginID {
            get {
                return ResourceManager.GetString("LoginID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Logo Name.
        /// </summary>
        internal static string LogoName {
            get {
                return ResourceManager.GetString("LogoName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Low.
        /// </summary>
        internal static string low {
            get {
                return ResourceManager.GetString("low", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manager.
        /// </summary>
        internal static string Manager {
            get {
                return ResourceManager.GetString("Manager", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mandatory Services.
        /// </summary>
        internal static string mandatoryservices {
            get {
                return ResourceManager.GetString("mandatoryservices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manufacturer.
        /// </summary>
        internal static string Manufacturer {
            get {
                return ResourceManager.GetString("Manufacturer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Map Columns.
        /// </summary>
        internal static string MapColumns {
            get {
                return ResourceManager.GetString("MapColumns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mapped Columns.
        /// </summary>
        internal static string MappedColumns {
            get {
                return ResourceManager.GetString("MappedColumns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mar.
        /// </summary>
        internal static string March {
            get {
                return ResourceManager.GetString("March", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Master ID.
        /// </summary>
        internal static string MasterID {
            get {
                return ResourceManager.GetString("MasterID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Master Name.
        /// </summary>
        internal static string MasterName {
            get {
                return ResourceManager.GetString("MasterName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to May.
        /// </summary>
        internal static string May {
            get {
                return ResourceManager.GetString("May", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Medium.
        /// </summary>
        internal static string medium {
            get {
                return ResourceManager.GetString("medium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Menu Details.
        /// </summary>
        internal static string MenuDetail {
            get {
                return ResourceManager.GetString("MenuDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Menu Details.
        /// </summary>
        internal static string MenuDetails {
            get {
                return ResourceManager.GetString("MenuDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Menu Name.
        /// </summary>
        internal static string MenuName {
            get {
                return ResourceManager.GetString("MenuName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Menu name can not be blank.
        /// </summary>
        internal static string MenuNamecannotbeblank {
            get {
                return ResourceManager.GetString("MenuNamecannotbeblank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Menu Path.
        /// </summary>
        internal static string MenuPath {
            get {
                return ResourceManager.GetString("MenuPath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mobile.
        /// </summary>
        internal static string Mobile {
            get {
                return ResourceManager.GetString("Mobile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mobile Number.
        /// </summary>
        internal static string MobileNumber {
            get {
                return ResourceManager.GetString("MobileNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Model.
        /// </summary>
        internal static string model {
            get {
                return ResourceManager.GetString("model", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Model English.
        /// </summary>
        internal static string modelenglish {
            get {
                return ResourceManager.GetString("modelenglish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Model Field Search.
        /// </summary>
        internal static string ModelFieldSearch {
            get {
                return ResourceManager.GetString("ModelFieldSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Model is inactive.
        /// </summary>
        internal static string modelisinactive {
            get {
                return ResourceManager.GetString("modelisinactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Model Locale.
        /// </summary>
        internal static string modellocale {
            get {
                return ResourceManager.GetString("modellocale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Model Master.
        /// </summary>
        internal static string modelmaster {
            get {
                return ResourceManager.GetString("modelmaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Model Name.
        /// </summary>
        internal static string modelname {
            get {
                return ResourceManager.GetString("modelname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Model not found.
        /// </summary>
        internal static string modelnotfound {
            get {
                return ResourceManager.GetString("modelnotfound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Model Search.
        /// </summary>
        internal static string ModelSearch {
            get {
                return ResourceManager.GetString("ModelSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Module.
        /// </summary>
        internal static string Module {
            get {
                return ResourceManager.GetString("Module", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Module Name.
        /// </summary>
        internal static string ModuleName {
            get {
                return ResourceManager.GetString("ModuleName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Module name cannot be blank.
        /// </summary>
        internal static string ModuleNameCannotbeblank {
            get {
                return ResourceManager.GetString("ModuleNameCannotbeblank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Monday.
        /// </summary>
        internal static string Monday {
            get {
                return ResourceManager.GetString("Monday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Month.
        /// </summary>
        internal static string month {
            get {
                return ResourceManager.GetString("month", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Queue.
        /// </summary>
        internal static string MyQue {
            get {
                return ResourceManager.GetString("MyQue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Queue.
        /// </summary>
        internal static string MyQueue {
            get {
                return ResourceManager.GetString("MyQueue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        internal static string Name {
            get {
                return ResourceManager.GetString("Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Password.
        /// </summary>
        internal static string NewPassword {
            get {
                return ResourceManager.GetString("NewPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Password and Confirm Password are not matching.
        /// </summary>
        internal static string newpasswordandConfirmpasswordarenotmatching {
            get {
                return ResourceManager.GetString("newpasswordandConfirmpasswordarenotmatching", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to More Than 90 Hours.
        /// </summary>
        internal static string NintyHour {
            get {
                return ResourceManager.GetString("NintyHour", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        internal static string no {
            get {
                return ResourceManager.GetString("no", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No changes made.
        /// </summary>
        internal static string NoChangesMade {
            get {
                return ResourceManager.GetString("NoChangesMade", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No changes made to save.
        /// </summary>
        internal static string NochangesmadetoSave {
            get {
                return ResourceManager.GetString("NochangesmadetoSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Edit permission for Product Master.
        /// </summary>
        internal static string Noeditpermissionforproductmaster {
            get {
                return ResourceManager.GetString("Noeditpermissionforproductmaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Party is InActive or No matching record found, Do you want to add this party.
        /// </summary>
        internal static string NomatchingrecordfoundDoyouwanttoadd {
            get {
                return ResourceManager.GetString("NomatchingrecordfoundDoyouwanttoadd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Non Taxable.
        /// </summary>
        internal static string NonTaxable {
            get {
                return ResourceManager.GetString("NonTaxable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Non Taxable other charges 1.
        /// </summary>
        internal static string NonTaxableothercharges1 {
            get {
                return ResourceManager.GetString("NonTaxableothercharges1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Non Taxable other charges 1 Amount.
        /// </summary>
        internal static string NonTaxableothercharges1Amount {
            get {
                return ResourceManager.GetString("NonTaxableothercharges1Amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Non Taxable other charges 2.
        /// </summary>
        internal static string NonTaxableothercharges2 {
            get {
                return ResourceManager.GetString("NonTaxableothercharges2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Non Taxable other charges 2 Amount.
        /// </summary>
        internal static string NonTaxableothercharges2Amount {
            get {
                return ResourceManager.GetString("NonTaxableothercharges2Amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number Of Service Request Completed.
        /// </summary>
        internal static string NoOfSRCompleted {
            get {
                return ResourceManager.GetString("NoOfSRCompleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number Of Service Request Recieved.
        /// </summary>
        internal static string NoOfSRRecieved {
            get {
                return ResourceManager.GetString("NoOfSRRecieved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No product is associated with selected customer.
        /// </summary>
        internal static string Noproductisassociatedwithselectedcustomer {
            get {
                return ResourceManager.GetString("Noproductisassociatedwithselectedcustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No records to view.
        /// </summary>
        internal static string Norecordstoview {
            get {
                return ResourceManager.GetString("Norecordstoview", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not Equal.
        /// </summary>
        internal static string NotEqual {
            get {
                return ResourceManager.GetString("NotEqual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not Found.
        /// </summary>
        internal static string NotFound {
            get {
                return ResourceManager.GetString("NotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nov.
        /// </summary>
        internal static string November {
            get {
                return ResourceManager.GetString("November", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application error occured.
        /// </summary>
        internal static string NullReferenceException {
            get {
                return ResourceManager.GetString("NullReferenceException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number.
        /// </summary>
        internal static string number {
            get {
                return ResourceManager.GetString("number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Object Description.
        /// </summary>
        internal static string ObjectDescription {
            get {
                return ResourceManager.GetString("ObjectDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Object description cannot be blank.
        /// </summary>
        internal static string ObjectDescriptioncannotbeblank {
            get {
                return ResourceManager.GetString("ObjectDescriptioncannotbeblank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Object.
        /// </summary>
        internal static string ObjectMaster {
            get {
                return ResourceManager.GetString("ObjectMaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Object Name.
        /// </summary>
        internal static string ObjectName {
            get {
                return ResourceManager.GetString("ObjectName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Object name cannot be blank.
        /// </summary>
        internal static string ObjectNamecannotbeblank {
            get {
                return ResourceManager.GetString("ObjectNamecannotbeblank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Objects saved successfully.
        /// </summary>
        internal static string ObjectssavedSuccessfully {
            get {
                return ResourceManager.GetString("ObjectssavedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Oct.
        /// </summary>
        internal static string October {
            get {
                return ResourceManager.GetString("October", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to of.
        /// </summary>
        internal static string of {
            get {
                return ResourceManager.GetString("of", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Old Password.
        /// </summary>
        internal static string OldPassword {
            get {
                return ResourceManager.GetString("OldPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OnHold Count.
        /// </summary>
        internal static string OnHoldCount {
            get {
                return ResourceManager.GetString("OnHoldCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Only active customer deatils can be edited.
        /// </summary>
        internal static string onlyactivecustomerdeatilscanbeedited {
            get {
                return ResourceManager.GetString("onlyactivecustomerdeatilscanbeedited", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Only active warranty can be edited.
        /// </summary>
        internal static string onlyactivewarrantycanbeedited {
            get {
                return ResourceManager.GetString("onlyactivewarrantycanbeedited", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open.
        /// </summary>
        internal static string open {
            get {
                return ResourceManager.GetString("open", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Report.
        /// </summary>
        internal static string OpenReport {
            get {
                return ResourceManager.GetString("OpenReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operation.
        /// </summary>
        internal static string operation {
            get {
                return ResourceManager.GetString("operation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operation and employee is already associated.
        /// </summary>
        internal static string OperationandEmployeeisalreadyassociated {
            get {
                return ResourceManager.GetString("OperationandEmployeeisalreadyassociated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operation Code.
        /// </summary>
        internal static string OperationCode {
            get {
                return ResourceManager.GetString("OperationCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operation code not found.
        /// </summary>
        internal static string OperationCodeNotFound {
            get {
                return ResourceManager.GetString("OperationCodeNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operation Description.
        /// </summary>
        internal static string OperationDescription {
            get {
                return ResourceManager.GetString("OperationDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operation Details.
        /// </summary>
        internal static string OperationDetails {
            get {
                return ResourceManager.GetString("OperationDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operation Deviation Report.
        /// </summary>
        internal static string OperationDeviationReport {
            get {
                return ResourceManager.GetString("OperationDeviationReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operation Employee Details.
        /// </summary>
        internal static string OperationEmployeeDetails {
            get {
                return ResourceManager.GetString("OperationEmployeeDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operation End Date.
        /// </summary>
        internal static string OperationEndDate {
            get {
                return ResourceManager.GetString("OperationEndDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operation end date cannot be less than operation start date.
        /// </summary>
        internal static string OperationEndDateCannotbelessthanoperationStartDate {
            get {
                return ResourceManager.GetString("OperationEndDateCannotbelessthanoperationStartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operation English.
        /// </summary>
        internal static string operationenglish {
            get {
                return ResourceManager.GetString("operationenglish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operation Field Search.
        /// </summary>
        internal static string OperationFieldSearch {
            get {
                return ResourceManager.GetString("OperationFieldSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operation Header.
        /// </summary>
        internal static string operationheader {
            get {
                return ResourceManager.GetString("operationheader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operation Hours.
        /// </summary>
        internal static string OperationHours {
            get {
                return ResourceManager.GetString("OperationHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operation is already associated with another employee.
        /// </summary>
        internal static string Operationisalreadyassociatedwithanotheremployee {
            get {
                return ResourceManager.GetString("Operationisalreadyassociatedwithanotheremployee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operation Locale.
        /// </summary>
        internal static string operationlocale {
            get {
                return ResourceManager.GetString("operationlocale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operation Master.
        /// </summary>
        internal static string operationmaster {
            get {
                return ResourceManager.GetString("operationmaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operation Start Date.
        /// </summary>
        internal static string OperationStartDate {
            get {
                return ResourceManager.GetString("OperationStartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operation start date cannot be less than job card date.
        /// </summary>
        internal static string OperationStartdatecannotbelessthanJobcarddate {
            get {
                return ResourceManager.GetString("OperationStartdatecannotbelessthanJobcarddate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operator.
        /// </summary>
        internal static string Operator {
            get {
                return ResourceManager.GetString("Operator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OR.
        /// </summary>
        internal static string OR {
            get {
                return ResourceManager.GetString("OR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other Detail.
        /// </summary>
        internal static string OtherDetail {
            get {
                return ResourceManager.GetString("OtherDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other Details.
        /// </summary>
        internal static string OtherDetails {
            get {
                return ResourceManager.GetString("OtherDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Others.
        /// </summary>
        internal static string Others {
            get {
                return ResourceManager.GetString("Others", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application error occured.
        /// </summary>
        internal static string OutOfMemoryException {
            get {
                return ResourceManager.GetString("OutOfMemoryException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parent Company.
        /// </summary>
        internal static string ParentCompany {
            get {
                return ResourceManager.GetString("ParentCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parent company operation cannot be deleted.
        /// </summary>
        internal static string parentcompanyoperationcannotbedeleted {
            get {
                return ResourceManager.GetString("parentcompanyoperationcannotbedeleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parent company operation cannot be edited.
        /// </summary>
        internal static string parentcompanyoperationcannotbeedited {
            get {
                return ResourceManager.GetString("parentcompanyoperationcannotbeedited", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parent Menu.
        /// </summary>
        internal static string ParentMenu {
            get {
                return ResourceManager.GetString("ParentMenu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Already Added..
        /// </summary>
        internal static string PartAlreadyAdded {
            get {
                return ResourceManager.GetString("PartAlreadyAdded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Category.
        /// </summary>
        internal static string partcategory {
            get {
                return ResourceManager.GetString("partcategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Description.
        /// </summary>
        internal static string partdescription {
            get {
                return ResourceManager.GetString("partdescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Function Group.
        /// </summary>
        internal static string partfunctiongroup {
            get {
                return ResourceManager.GetString("partfunctiongroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Partner.
        /// </summary>
        internal static string Partner {
            get {
                return ResourceManager.GetString("Partner", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Partner.
        /// </summary>
        internal static string PartnerName {
            get {
                return ResourceManager.GetString("PartnerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Number.
        /// </summary>
        internal static string partnumber {
            get {
                return ResourceManager.GetString("partnumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Number already selected.
        /// </summary>
        internal static string PartNumberalreadyselected {
            get {
                return ResourceManager.GetString("PartNumberalreadyselected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Number is blank..
        /// </summary>
        internal static string PartNumberisBlank {
            get {
                return ResourceManager.GetString("PartNumberisBlank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Number not associated to the selected product details..
        /// </summary>
        internal static string PartNumbernotassociatedtotheselectedproductdetails {
            get {
                return ResourceManager.GetString("PartNumbernotassociatedtotheselectedproductdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part number not found.
        /// </summary>
        internal static string PartNumbernotfound {
            get {
                return ResourceManager.GetString("PartNumbernotfound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Number Search.
        /// </summary>
        internal static string partnumbersearch {
            get {
                return ResourceManager.GetString("partnumbersearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Price.
        /// </summary>
        internal static string partprice {
            get {
                return ResourceManager.GetString("partprice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Price Details.
        /// </summary>
        internal static string partpricepdetails {
            get {
                return ResourceManager.GetString("partpricepdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parts Product Type Details.
        /// </summary>
        internal static string partproducttypedetails {
            get {
                return ResourceManager.GetString("partproducttypedetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parts.
        /// </summary>
        internal static string Parts {
            get {
                return ResourceManager.GetString("Parts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parts Detail.
        /// </summary>
        internal static string PartsDetail {
            get {
                return ResourceManager.GetString("PartsDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parts Details.
        /// </summary>
        internal static string PartsDetails {
            get {
                return ResourceManager.GetString("PartsDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parts .
        /// </summary>
        internal static string partsenglish {
            get {
                return ResourceManager.GetString("partsenglish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parts Field Search.
        /// </summary>
        internal static string PartsFieldSearch {
            get {
                return ResourceManager.GetString("PartsFieldSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parts Free Stock details.
        /// </summary>
        internal static string partsfreestockdetails {
            get {
                return ResourceManager.GetString("partsfreestockdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parts .
        /// </summary>
        internal static string partslocale {
            get {
                return ResourceManager.GetString("partslocale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parts Master.
        /// </summary>
        internal static string partsmaster {
            get {
                return ResourceManager.GetString("partsmaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parts Master .
        /// </summary>
        internal static string partsmasterlocale {
            get {
                return ResourceManager.GetString("partsmasterlocale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parts Master .
        /// </summary>
        internal static string partspmasterheader {
            get {
                return ResourceManager.GetString("partspmasterheader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parts Price Details.
        /// </summary>
        internal static string partspricedetails {
            get {
                return ResourceManager.GetString("partspricedetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parts Product Type .
        /// </summary>
        internal static string partsproducttypelocale {
            get {
                return ResourceManager.GetString("partsproducttypelocale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parts Template.
        /// </summary>
        internal static string PartsTemplate {
            get {
                return ResourceManager.GetString("PartsTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parts Total Amount.
        /// </summary>
        internal static string PartsTotalAmount {
            get {
                return ResourceManager.GetString("PartsTotalAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Party.
        /// </summary>
        internal static string Party {
            get {
                return ResourceManager.GetString("Party", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Party Details.
        /// </summary>
        internal static string PartyDetails {
            get {
                return ResourceManager.GetString("PartyDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Party Field Search.
        /// </summary>
        internal static string PartyFielSearch {
            get {
                return ResourceManager.GetString("PartyFielSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Party Location.
        /// </summary>
        internal static string PartyLocation {
            get {
                return ResourceManager.GetString("PartyLocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Party Mobile.
        /// </summary>
        internal static string PartyMobile {
            get {
                return ResourceManager.GetString("PartyMobile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Party Name.
        /// </summary>
        internal static string PartyName {
            get {
                return ResourceManager.GetString("PartyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Party Not Found.
        /// </summary>
        internal static string PartyNotFound {
            get {
                return ResourceManager.GetString("PartyNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Party Phone.
        /// </summary>
        internal static string PartyPhone {
            get {
                return ResourceManager.GetString("PartyPhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Party Schedule.
        /// </summary>
        internal static string PartySchedule {
            get {
                return ResourceManager.GetString("PartySchedule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Party Search.
        /// </summary>
        internal static string PartySearch {
            get {
                return ResourceManager.GetString("PartySearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Party Type.
        /// </summary>
        internal static string PartyType {
            get {
                return ResourceManager.GetString("PartyType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password.
        /// </summary>
        internal static string Password {
            get {
                return ResourceManager.GetString("Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm password does not match with password given.
        /// </summary>
        internal static string Passwordandconfirmpasswordshouldmatch {
            get {
                return ResourceManager.GetString("Passwordandconfirmpasswordshouldmatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Payment Terms.
        /// </summary>
        internal static string PaymentTerms {
            get {
                return ResourceManager.GetString("PaymentTerms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PDF.
        /// </summary>
        internal static string PDF {
            get {
                return ResourceManager.GetString("PDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pending.
        /// </summary>
        internal static string Pending {
            get {
                return ResourceManager.GetString("Pending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Percentage Deviation.
        /// </summary>
        internal static string PercentageDeviation {
            get {
                return ResourceManager.GetString("PercentageDeviation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Personal.
        /// </summary>
        internal static string Personal {
            get {
                return ResourceManager.GetString("Personal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Personal Calendar.
        /// </summary>
        internal static string PersonalCalender {
            get {
                return ResourceManager.GetString("PersonalCalender", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phone.
        /// </summary>
        internal static string Phone {
            get {
                return ResourceManager.GetString("Phone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phone.
        /// </summary>
        internal static string PhoneNo {
            get {
                return ResourceManager.GetString("PhoneNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pie Chart.
        /// </summary>
        internal static string PieChart {
            get {
                return ResourceManager.GetString("PieChart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Planned Completion Date.
        /// </summary>
        internal static string PlannedCompletionDate {
            get {
                return ResourceManager.GetString("PlannedCompletionDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Planned completion date cannot be less than start date.
        /// </summary>
        internal static string PlannedCompletionDatecannotbelessthanStartDate {
            get {
                return ResourceManager.GetString("PlannedCompletionDatecannotbelessthanStartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Planned Start Date.
        /// </summary>
        internal static string PlannedStartDate {
            get {
                return ResourceManager.GetString("PlannedStartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please add atleast one operation detail.
        /// </summary>
        internal static string Pleaseaddatleastoneoperationdetail {
            get {
                return ResourceManager.GetString("Pleaseaddatleastoneoperationdetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please complete entering details.
        /// </summary>
        internal static string pleasecompleteenteringdetails {
            get {
                return ResourceManager.GetString("pleasecompleteenteringdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Enter Date As.
        /// </summary>
        internal static string PleaseEnterDateAs {
            get {
                return ResourceManager.GetString("PleaseEnterDateAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PleaseenterIntegerValue.
        /// </summary>
        internal static string PleaseenterIntegerValue {
            get {
                return ResourceManager.GetString("PleaseenterIntegerValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter login ID.
        /// </summary>
        internal static string PleaseEnterLoginID {
            get {
                return ResourceManager.GetString("PleaseEnterLoginID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter party name.
        /// </summary>
        internal static string PleaseenterPartyName {
            get {
                return ResourceManager.GetString("PleaseenterPartyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter password.
        /// </summary>
        internal static string PleaseEnterPassword {
            get {
                return ResourceManager.GetString("PleaseEnterPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter report header.
        /// </summary>
        internal static string PleaseenterReportHeader {
            get {
                return ResourceManager.GetString("PleaseenterReportHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter report name.
        /// </summary>
        internal static string PleaseenterReportName {
            get {
                return ResourceManager.GetString("PleaseenterReportName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter serial number.
        /// </summary>
        internal static string Pleaseenterserailnumber {
            get {
                return ResourceManager.GetString("Pleaseenterserailnumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter user details.
        /// </summary>
        internal static string Pleaseenteruserdetails {
            get {
                return ResourceManager.GetString("Pleaseenteruserdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter valid model.
        /// </summary>
        internal static string pleaseentervalidmodel {
            get {
                return ResourceManager.GetString("pleaseentervalidmodel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter value.
        /// </summary>
        internal static string Pleaseentervalue {
            get {
                return ResourceManager.GetString("Pleaseentervalue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter value1.
        /// </summary>
        internal static string Pleaseentervalue1 {
            get {
                return ResourceManager.GetString("Pleaseentervalue1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please provide menu name.
        /// </summary>
        internal static string PleaseprovideMenuName {
            get {
                return ResourceManager.GetString("PleaseprovideMenuName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please provide module name.
        /// </summary>
        internal static string PleaseprovideModuleName {
            get {
                return ResourceManager.GetString("PleaseprovideModuleName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please provide password.
        /// </summary>
        internal static string Pleaseprovidepassword {
            get {
                return ResourceManager.GetString("Pleaseprovidepassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please save the operation details.
        /// </summary>
        internal static string pleasesavetheOperationdata {
            get {
                return ResourceManager.GetString("pleasesavetheOperationdata", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please save the parts details.
        /// </summary>
        internal static string pleasesavethepartsdata {
            get {
                return ResourceManager.GetString("pleasesavethepartsdata", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please save the service details.
        /// </summary>
        internal static string pleasesavetheServicedata {
            get {
                return ResourceManager.GetString("pleasesavetheServicedata", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please save the sundry details.
        /// </summary>
        internal static string pleasesavethesundrydata {
            get {
                return ResourceManager.GetString("pleasesavethesundrydata", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select a Column.
        /// </summary>
        internal static string PleaseselectaColumn {
            get {
                return ResourceManager.GetString("PleaseselectaColumn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select a Condition.
        /// </summary>
        internal static string PleaseselectaCondition {
            get {
                return ResourceManager.GetString("PleaseselectaCondition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select a file to upload.
        /// </summary>
        internal static string Pleaseselectafiletoupload {
            get {
                return ResourceManager.GetString("Pleaseselectafiletoupload", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select a Operator.
        /// </summary>
        internal static string PleaseselectaOperator {
            get {
                return ResourceManager.GetString("PleaseselectaOperator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select branch.
        /// </summary>
        internal static string PleaseSelectBranch {
            get {
                return ResourceManager.GetString("PleaseSelectBranch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select brand.
        /// </summary>
        internal static string PleaseselectBrand {
            get {
                return ResourceManager.GetString("PleaseselectBrand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select company.
        /// </summary>
        internal static string Pleaseselectcompany {
            get {
                return ResourceManager.GetString("Pleaseselectcompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select condition.
        /// </summary>
        internal static string Pleaseselectcondition {
            get {
                return ResourceManager.GetString("Pleaseselectcondition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select file.
        /// </summary>
        internal static string PleaseselectFile {
            get {
                return ResourceManager.GetString("PleaseselectFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Select Graph Category.
        /// </summary>
        internal static string PleaseSelectGraphCategory {
            get {
                return ResourceManager.GetString("PleaseSelectGraphCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Select Graph Type.
        /// </summary>
        internal static string PleaseSelectGraphType {
            get {
                return ResourceManager.GetString("PleaseSelectGraphType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select model.
        /// </summary>
        internal static string pleaseselectModel {
            get {
                return ResourceManager.GetString("pleaseselectModel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pleases select model and serial number.
        /// </summary>
        internal static string PleaseselectmodelandSerialNumber {
            get {
                return ResourceManager.GetString("PleaseselectmodelandSerialNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select operator.
        /// </summary>
        internal static string Pleaseselectoperator {
            get {
                return ResourceManager.GetString("Pleaseselectoperator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select records to delete.
        /// </summary>
        internal static string Pleaseselectrecordstodelete {
            get {
                return ResourceManager.GetString("Pleaseselectrecordstodelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select service request number.
        /// </summary>
        internal static string PleaseselectServicerequestnumber {
            get {
                return ResourceManager.GetString("PleaseselectServicerequestnumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select the column name.
        /// </summary>
        internal static string PleaseselecttheColumnName {
            get {
                return ResourceManager.GetString("PleaseselecttheColumnName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select the columns to map.
        /// </summary>
        internal static string Pleaseselectthecolumnstomap {
            get {
                return ResourceManager.GetString("Pleaseselectthecolumnstomap", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select the operator.
        /// </summary>
        internal static string Pleaseselecttheoperator {
            get {
                return ResourceManager.GetString("Pleaseselecttheoperator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select the table name.
        /// </summary>
        internal static string PleaseselecttheTableName {
            get {
                return ResourceManager.GetString("PleaseselecttheTableName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select users to save.
        /// </summary>
        internal static string PleaseselectUserstosave {
            get {
                return ResourceManager.GetString("PleaseselectUserstosave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Preffix Suffix Doesnt Exists.
        /// </summary>
        internal static string PreffixSuffixDoesntExists {
            get {
                return ResourceManager.GetString("PreffixSuffixDoesntExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prefix.
        /// </summary>
        internal static string prefix {
            get {
                return ResourceManager.GetString("prefix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prefix Suffix.
        /// </summary>
        internal static string prefixsuffix {
            get {
                return ResourceManager.GetString("prefixsuffix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Previous date cannot be empty.
        /// </summary>
        internal static string PreviousdateCannotbeempty {
            get {
                return ResourceManager.GetString("PreviousdateCannotbeempty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price cannot be blank or zero.
        /// </summary>
        internal static string PricecannotbeBlankorZero {
            get {
                return ResourceManager.GetString("PricecannotbeBlankorZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Primary Segment.
        /// </summary>
        internal static string PrimarySegment {
            get {
                return ResourceManager.GetString("PrimarySegment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print.
        /// </summary>
        internal static string Print {
            get {
                return ResourceManager.GetString("Print", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Action.
        /// </summary>
        internal static string PrintAction {
            get {
                return ResourceManager.GetString("PrintAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Priority.
        /// </summary>
        internal static string Priority {
            get {
                return ResourceManager.GetString("Priority", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Priority should be between 0 and 255.
        /// </summary>
        internal static string PriorityShouldBeBetweenzeroandtwofiftyfive {
            get {
                return ResourceManager.GetString("PriorityShouldBeBetweenzeroandtwofiftyfive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product.
        /// </summary>
        internal static string product {
            get {
                return ResourceManager.GetString("product", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Association.
        /// </summary>
        internal static string ProductAssociation {
            get {
                return ResourceManager.GetString("ProductAssociation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Detail.
        /// </summary>
        internal static string productdetail {
            get {
                return ResourceManager.GetString("productdetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Details.
        /// </summary>
        internal static string productdetails {
            get {
                return ResourceManager.GetString("productdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product ID.
        /// </summary>
        internal static string productid {
            get {
                return ResourceManager.GetString("productid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product is currently not asscociated with any customer.
        /// </summary>
        internal static string Productisnotasscociatedwithanycustomer {
            get {
                return ResourceManager.GetString("Productisnotasscociatedwithanycustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Location.
        /// </summary>
        internal static string ProductLocation {
            get {
                return ResourceManager.GetString("ProductLocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Reading.
        /// </summary>
        internal static string ProductReading {
            get {
                return ResourceManager.GetString("ProductReading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Schedule.
        /// </summary>
        internal static string ProductSchedule {
            get {
                return ResourceManager.GetString("ProductSchedule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Service History.
        /// </summary>
        internal static string ProductServiceHistory {
            get {
                return ResourceManager.GetString("ProductServiceHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Type.
        /// </summary>
        internal static string Producttype {
            get {
                return ResourceManager.GetString("Producttype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Type Master.
        /// </summary>
        internal static string producttypemaster {
            get {
                return ResourceManager.GetString("producttypemaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Type Name.
        /// </summary>
        internal static string producttypename {
            get {
                return ResourceManager.GetString("producttypename", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unique Identifier.
        /// </summary>
        internal static string ProductUniqueNo {
            get {
                return ResourceManager.GetString("ProductUniqueNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prospect.
        /// </summary>
        internal static string Prospect {
            get {
                return ResourceManager.GetString("Prospect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity.
        /// </summary>
        internal static string Quantity {
            get {
                return ResourceManager.GetString("Quantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity Cannot be Zero.
        /// </summary>
        internal static string QuantityCannotbeZero {
            get {
                return ResourceManager.GetString("QuantityCannotbeZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quest Informatics Private Limited.
        /// </summary>
        internal static string QuestInformaticsPrivateLimited {
            get {
                return ResourceManager.GetString("QuestInformaticsPrivateLimited", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Queue.
        /// </summary>
        internal static string Queue {
            get {
                return ResourceManager.GetString("Queue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quick Links.
        /// </summary>
        internal static string Quicklinks {
            get {
                return ResourceManager.GetString("Quicklinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quotation.
        /// </summary>
        internal static string Quotation {
            get {
                return ResourceManager.GetString("Quotation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quotation Amount.
        /// </summary>
        internal static string QuotationAmount {
            get {
                return ResourceManager.GetString("QuotationAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quotation Detail.
        /// </summary>
        internal static string QuotationDetail {
            get {
                return ResourceManager.GetString("QuotationDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quotation Number.
        /// </summary>
        internal static string QuotationNumber {
            get {
                return ResourceManager.GetString("QuotationNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quotation Priority.
        /// </summary>
        internal static string QuotationPriority {
            get {
                return ResourceManager.GetString("QuotationPriority", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Range cannot be blank.
        /// </summary>
        internal static string RangecannotbeBlank {
            get {
                return ResourceManager.GetString("RangecannotbeBlank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rate.
        /// </summary>
        internal static string Rate {
            get {
                return ResourceManager.GetString("Rate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rate Cannot be Zero.
        /// </summary>
        internal static string RateCannotbeZero {
            get {
                return ResourceManager.GetString("RateCannotbeZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rating.
        /// </summary>
        internal static string Rating {
            get {
                return ResourceManager.GetString("Rating", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rating should be between 1 and 10.
        /// </summary>
        internal static string Ratingshouldbebetween1and10 {
            get {
                return ResourceManager.GetString("Ratingshouldbebetween1and10", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rating should between 1 and 10.
        /// </summary>
        internal static string RatingshouldBetween1and10 {
            get {
                return ResourceManager.GetString("RatingshouldBetween1and10", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Read.
        /// </summary>
        internal static string Read {
            get {
                return ResourceManager.GetString("Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Read Action.
        /// </summary>
        internal static string ReadAction {
            get {
                return ResourceManager.GetString("ReadAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reading.
        /// </summary>
        internal static string reading {
            get {
                return ResourceManager.GetString("reading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reading Limit.
        /// </summary>
        internal static string readinglimit {
            get {
                return ResourceManager.GetString("readinglimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reading Log.
        /// </summary>
        internal static string readinglog {
            get {
                return ResourceManager.GetString("readinglog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Realization Report.
        /// </summary>
        internal static string realizationreport {
            get {
                return ResourceManager.GetString("realizationreport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reason for Inactive.
        /// </summary>
        internal static string reasonforinactive {
            get {
                return ResourceManager.GetString("reasonforinactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Count.
        /// </summary>
        internal static string ReceivedCount {
            get {
                return ResourceManager.GetString("ReceivedCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recent Activity Links.
        /// </summary>
        internal static string RecentActivityLinks {
            get {
                return ResourceManager.GetString("RecentActivityLinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Time.
        /// </summary>
        internal static string RecievedTime {
            get {
                return ResourceManager.GetString("RecievedTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Count.
        /// </summary>
        internal static string RecieviedCount {
            get {
                return ResourceManager.GetString("RecieviedCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reference Detail.
        /// </summary>
        internal static string ReferenceDetail {
            get {
                return ResourceManager.GetString("ReferenceDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reference Masters.
        /// </summary>
        internal static string ReferenceMasters {
            get {
                return ResourceManager.GetString("ReferenceMasters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reference Tables.
        /// </summary>
        internal static string ReferenceTables {
            get {
                return ResourceManager.GetString("ReferenceTables", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refresh.
        /// </summary>
        internal static string refresh {
            get {
                return ResourceManager.GetString("refresh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Region.
        /// </summary>
        internal static string Region {
            get {
                return ResourceManager.GetString("Region", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Registered.
        /// </summary>
        internal static string Registered {
            get {
                return ResourceManager.GetString("Registered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Registered Mobile.
        /// </summary>
        internal static string RegisteredMobile {
            get {
                return ResourceManager.GetString("RegisteredMobile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Re-Login.
        /// </summary>
        internal static string relogin {
            get {
                return ResourceManager.GetString("relogin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remarks.
        /// </summary>
        internal static string Remarks {
            get {
                return ResourceManager.GetString("Remarks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove Filter.
        /// </summary>
        internal static string RemoveFilter {
            get {
                return ResourceManager.GetString("RemoveFilter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Report Wizard.
        /// </summary>
        internal static string ReportWizard {
            get {
                return ResourceManager.GetString("ReportWizard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Req Date.
        /// </summary>
        internal static string ReqDate {
            get {
                return ResourceManager.GetString("ReqDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Req Number.
        /// </summary>
        internal static string ReqNumber {
            get {
                return ResourceManager.GetString("ReqNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request Description.
        /// </summary>
        internal static string RequestDescription {
            get {
                return ResourceManager.GetString("RequestDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request Number.
        /// </summary>
        internal static string RequestNumber {
            get {
                return ResourceManager.GetString("RequestNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset.
        /// </summary>
        internal static string Reset {
            get {
                return ResourceManager.GetString("Reset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resolution Time.
        /// </summary>
        internal static string resolutiontime {
            get {
                return ResourceManager.GetString("resolutiontime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resolution Time With Time Slots.
        /// </summary>
        internal static string Resolutiontimewithtimeslots {
            get {
                return ResourceManager.GetString("Resolutiontimewithtimeslots", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource Utilization Report.
        /// </summary>
        internal static string ResourceUtilizationReport {
            get {
                return ResourceManager.GetString("ResourceUtilizationReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Response Time.
        /// </summary>
        internal static string responsetime {
            get {
                return ResourceManager.GetString("responsetime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Revenue cannot be blank.
        /// </summary>
        internal static string RevenuecannotbeBlankorzero {
            get {
                return ResourceManager.GetString("RevenuecannotbeBlankorzero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Revenue Generated.
        /// </summary>
        internal static string RevenueGenerated {
            get {
                return ResourceManager.GetString("RevenueGenerated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Revenue More Then.
        /// </summary>
        internal static string revenuemorethen {
            get {
                return ResourceManager.GetString("revenuemorethen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Role.
        /// </summary>
        internal static string Role {
            get {
                return ResourceManager.GetString("Role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Role Definition.
        /// </summary>
        internal static string RoleDefinition {
            get {
                return ResourceManager.GetString("RoleDefinition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Role Name.
        /// </summary>
        internal static string RoleName {
            get {
                return ResourceManager.GetString("RoleName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Role name cannot be blank.
        /// </summary>
        internal static string RoleNameCannotbeblank {
            get {
                return ResourceManager.GetString("RoleNameCannotbeblank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Role Object.
        /// </summary>
        internal static string RoleObject {
            get {
                return ResourceManager.GetString("RoleObject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Roles.
        /// </summary>
        internal static string Roles {
            get {
                return ResourceManager.GetString("Roles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Round Off.
        /// </summary>
        internal static string RoundOff {
            get {
                return ResourceManager.GetString("RoundOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saturday.
        /// </summary>
        internal static string Saturday {
            get {
                return ResourceManager.GetString("Saturday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        internal static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Action.
        /// </summary>
        internal static string SaveAction {
            get {
                return ResourceManager.GetString("SaveAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saved Successfully.
        /// </summary>
        internal static string SavedSuccessfully {
            get {
                return ResourceManager.GetString("SavedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Format and Generate Report.
        /// </summary>
        internal static string SaveFormatandGenerateReport {
            get {
                return ResourceManager.GetString("SaveFormatandGenerateReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Parts Free Stock Details.
        /// </summary>
        internal static string savefreestockdetails {
            get {
                return ResourceManager.GetString("savefreestockdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Header.
        /// </summary>
        internal static string saveheader {
            get {
                return ResourceManager.GetString("saveheader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Part Price Details.
        /// </summary>
        internal static string savepartprice {
            get {
                return ResourceManager.GetString("savepartprice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Prefix Suffix.
        /// </summary>
        internal static string SavePrefixSuffix {
            get {
                return ResourceManager.GetString("SavePrefixSuffix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Product Detail.
        /// </summary>
        internal static string saveproductdetail {
            get {
                return ResourceManager.GetString("saveproductdetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Part Product Type.
        /// </summary>
        internal static string saveproducttype {
            get {
                return ResourceManager.GetString("saveproducttype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Role.
        /// </summary>
        internal static string SaveRole {
            get {
                return ResourceManager.GetString("SaveRole", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Step.
        /// </summary>
        internal static string SaveStep {
            get {
                return ResourceManager.GetString("SaveStep", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Step Link.
        /// </summary>
        internal static string SaveStepLink {
            get {
                return ResourceManager.GetString("SaveStepLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saved Successfully.
        /// </summary>
        internal static string SaveSuccessfull {
            get {
                return ResourceManager.GetString("SaveSuccessfull", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Tax Structure.
        /// </summary>
        internal static string savetaxstructure {
            get {
                return ResourceManager.GetString("savetaxstructure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save User.
        /// </summary>
        internal static string SaveUser {
            get {
                return ResourceManager.GetString("SaveUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Schedule Type.
        /// </summary>
        internal static string ScheduleType {
            get {
                return ResourceManager.GetString("ScheduleType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Secondary Segment.
        /// </summary>
        internal static string SecondarySegment {
            get {
                return ResourceManager.GetString("SecondarySegment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Secondary Segment Description.
        /// </summary>
        internal static string SecondarySegmentdescription {
            get {
                return ResourceManager.GetString("SecondarySegmentdescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Secondary Segment English.
        /// </summary>
        internal static string secondarysegmentenglish {
            get {
                return ResourceManager.GetString("secondarysegmentenglish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Secondary Segment Locale.
        /// </summary>
        internal static string secondarysegmentlocale {
            get {
                return ResourceManager.GetString("secondarysegmentlocale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Segment Detail.
        /// </summary>
        internal static string SegmentDetail {
            get {
                return ResourceManager.GetString("SegmentDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select.
        /// </summary>
        internal static string select {
            get {
                return ResourceManager.GetString("select", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select All.
        /// </summary>
        internal static string SelectAll {
            get {
                return ResourceManager.GetString("SelectAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Brand.
        /// </summary>
        internal static string selectbrand {
            get {
                return ResourceManager.GetString("selectbrand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Column.
        /// </summary>
        internal static string SelectColumn {
            get {
                return ResourceManager.GetString("SelectColumn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Company.
        /// </summary>
        internal static string selectcompany {
            get {
                return ResourceManager.GetString("selectcompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Condition.
        /// </summary>
        internal static string SelectCondition {
            get {
                return ResourceManager.GetString("SelectCondition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ---------Select---------.
        /// </summary>
        internal static string SelectDDl {
            get {
                return ResourceManager.GetString("SelectDDl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected file is not an excel file.
        /// </summary>
        internal static string SelectedFileisnotanExcelFile {
            get {
                return ResourceManager.GetString("SelectedFileisnotanExcelFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selection Criteria.
        /// </summary>
        internal static string SelectionCriteria {
            get {
                return ResourceManager.GetString("SelectionCriteria", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Model.
        /// </summary>
        internal static string SelectModel {
            get {
                return ResourceManager.GetString("SelectModel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Model and Request.
        /// </summary>
        internal static string SelectModelandRequest {
            get {
                return ResourceManager.GetString("SelectModelandRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Operator.
        /// </summary>
        internal static string SelectOperator {
            get {
                return ResourceManager.GetString("SelectOperator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Party Type.
        /// </summary>
        internal static string SelectPartyType {
            get {
                return ResourceManager.GetString("SelectPartyType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Product Type.
        /// </summary>
        internal static string selectproducttype {
            get {
                return ResourceManager.GetString("selectproducttype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Records to Delete.
        /// </summary>
        internal static string SelectRecordstoDelete {
            get {
                return ResourceManager.GetString("SelectRecordstoDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Report From Previously Stored Formats.
        /// </summary>
        internal static string SelectReportFromPreviouslyStoredFormats {
            get {
                return ResourceManager.GetString("SelectReportFromPreviouslyStoredFormats", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Request Number.
        /// </summary>
        internal static string SelectReqNumber {
            get {
                return ResourceManager.GetString("SelectReqNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Service Request.
        /// </summary>
        internal static string SelectServiceRequest {
            get {
                return ResourceManager.GetString("SelectServiceRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Shift.
        /// </summary>
        internal static string selectshift {
            get {
                return ResourceManager.GetString("selectshift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Table Name.
        /// </summary>
        internal static string SelectTableName {
            get {
                return ResourceManager.GetString("SelectTableName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sep.
        /// </summary>
        internal static string September {
            get {
                return ResourceManager.GetString("September", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sequence No.
        /// </summary>
        internal static string sequenceno {
            get {
                return ResourceManager.GetString("sequenceno", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Serial.
        /// </summary>
        internal static string Serial {
            get {
                return ResourceManager.GetString("Serial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Serial Number.
        /// </summary>
        internal static string serialnumber {
            get {
                return ResourceManager.GetString("serialnumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Serial number already exists for this model.
        /// </summary>
        internal static string serialnumberalreadyexistsforthismodel {
            get {
                return ResourceManager.GetString("serialnumberalreadyexistsforthismodel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Serial Number Field Search.
        /// </summary>
        internal static string SerialNumberFieldSearch {
            get {
                return ResourceManager.GetString("SerialNumberFieldSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Serial number not found for the selected model.
        /// </summary>
        internal static string SerialNumbernotfoundfortheselectedmodel {
            get {
                return ResourceManager.GetString("SerialNumbernotfoundfortheselectedmodel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service.
        /// </summary>
        internal static string Service {
            get {
                return ResourceManager.GetString("Service", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Charge Code.
        /// </summary>
        internal static string ServiceChargeCode {
            get {
                return ResourceManager.GetString("ServiceChargeCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service charge code not found.
        /// </summary>
        internal static string servicechargecodenotfound {
            get {
                return ResourceManager.GetString("servicechargecodenotfound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Charge Detail.
        /// </summary>
        internal static string ServiceChargeDetail {
            get {
                return ResourceManager.GetString("ServiceChargeDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Charge Field Search.
        /// </summary>
        internal static string ServiceChargeFieldSearch {
            get {
                return ResourceManager.GetString("ServiceChargeFieldSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Charges.
        /// </summary>
        internal static string servicecharges {
            get {
                return ResourceManager.GetString("servicecharges", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Charges Detail.
        /// </summary>
        internal static string servicechargesdetail {
            get {
                return ResourceManager.GetString("servicechargesdetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Charges Details.
        /// </summary>
        internal static string ServiceChargesDetails {
            get {
                return ResourceManager.GetString("ServiceChargesDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Charges.
        /// </summary>
        internal static string servicechargesenglish {
            get {
                return ResourceManager.GetString("servicechargesenglish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Charges .
        /// </summary>
        internal static string servicechargesheader {
            get {
                return ResourceManager.GetString("servicechargesheader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Charges .
        /// </summary>
        internal static string servicechargeslocale {
            get {
                return ResourceManager.GetString("servicechargeslocale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Charges Master.
        /// </summary>
        internal static string servicechargesmaster {
            get {
                return ResourceManager.GetString("servicechargesmaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Charges Total Amount.
        /// </summary>
        internal static string ServiceChargesTotalAmount {
            get {
                return ResourceManager.GetString("ServiceChargesTotalAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Code.
        /// </summary>
        internal static string ServiceCode {
            get {
                return ResourceManager.GetString("ServiceCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Date.
        /// </summary>
        internal static string servicedate {
            get {
                return ResourceManager.GetString("servicedate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service date cannot be less than Current date.
        /// </summary>
        internal static string servicedatecannotbelessthancurrentdate {
            get {
                return ResourceManager.GetString("servicedatecannotbelessthancurrentdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Details.
        /// </summary>
        internal static string ServiceDetails {
            get {
                return ResourceManager.GetString("ServiceDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service History.
        /// </summary>
        internal static string servicehistory {
            get {
                return ResourceManager.GetString("servicehistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Priority.
        /// </summary>
        internal static string ServicePriority {
            get {
                return ResourceManager.GetString("ServicePriority", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quotation Number.
        /// </summary>
        internal static string ServiceQuotationNumber {
            get {
                return ResourceManager.GetString("ServiceQuotationNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Request.
        /// </summary>
        internal static string ServiceRequest {
            get {
                return ResourceManager.GetString("ServiceRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Request Abandoned.
        /// </summary>
        internal static string ServiceRequestAbandoned {
            get {
                return ResourceManager.GetString("ServiceRequestAbandoned", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Request Count.
        /// </summary>
        internal static string ServiceRequestCount {
            get {
                return ResourceManager.GetString("ServiceRequestCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Request Date.
        /// </summary>
        internal static string ServiceRequestDate {
            get {
                return ResourceManager.GetString("ServiceRequestDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Request Distribution Chart.
        /// </summary>
        internal static string ServiceRequestDistributionChart {
            get {
                return ResourceManager.GetString("ServiceRequestDistributionChart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service request Field Search.
        /// </summary>
        internal static string ServicerequestFieldSearch {
            get {
                return ResourceManager.GetString("ServicerequestFieldSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Request Number.
        /// </summary>
        internal static string ServiceRequestNumber {
            get {
                return ResourceManager.GetString("ServiceRequestNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service request number not found.
        /// </summary>
        internal static string ServiceRequestNumbernotfound {
            get {
                return ResourceManager.GetString("ServiceRequestNumbernotfound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Request Summary.
        /// </summary>
        internal static string ServiceRequestSummary {
            get {
                return ResourceManager.GetString("ServiceRequestSummary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Schedule.
        /// </summary>
        internal static string ServiceSchedule {
            get {
                return ResourceManager.GetString("ServiceSchedule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Type.
        /// </summary>
        internal static string ServiceType {
            get {
                return ResourceManager.GetString("ServiceType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Type Name.
        /// </summary>
        internal static string ServiceTypeName {
            get {
                return ResourceManager.GetString("ServiceTypeName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shift.
        /// </summary>
        internal static string Shift {
            get {
                return ResourceManager.GetString("Shift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Short Name.
        /// </summary>
        internal static string ShortName {
            get {
                return ResourceManager.GetString("ShortName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Site Address.
        /// </summary>
        internal static string siteaddress {
            get {
                return ResourceManager.GetString("siteaddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Site Address Details.
        /// </summary>
        internal static string siteaddressdetails {
            get {
                return ResourceManager.GetString("siteaddressdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 16 To 24 Hours.
        /// </summary>
        internal static string SixteenToTwentyFourHours {
            get {
                return ResourceManager.GetString("SixteenToTwentyFourHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Skill.
        /// </summary>
        internal static string skill {
            get {
                return ResourceManager.GetString("skill", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Skill is already associated with the employee.
        /// </summary>
        internal static string Skillisalreadyassociatedwiththeemployee {
            get {
                return ResourceManager.GetString("Skillisalreadyassociatedwiththeemployee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Skill Level.
        /// </summary>
        internal static string skilllevel {
            get {
                return ResourceManager.GetString("skilllevel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Skill level should be between 1 to 10.
        /// </summary>
        internal static string skilllevelshouldbebetween1to10 {
            get {
                return ResourceManager.GetString("skilllevelshouldbebetween1to10", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Skill Set.
        /// </summary>
        internal static string Skillset {
            get {
                return ResourceManager.GetString("Skillset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sl No.
        /// </summary>
        internal static string slno {
            get {
                return ResourceManager.GetString("slno", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SMS To Addressee.
        /// </summary>
        internal static string SMSToAddressee {
            get {
                return ResourceManager.GetString("SMSToAddressee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SMS To Customer.
        /// </summary>
        internal static string SMSToCustomer {
            get {
                return ResourceManager.GetString("SMSToCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SMTP Mail Box.
        /// </summary>
        internal static string SMTPMailBox {
            get {
                return ResourceManager.GetString("SMTPMailBox", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SMTP Password.
        /// </summary>
        internal static string SMTPPassword {
            get {
                return ResourceManager.GetString("SMTPPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SMTP Server Name.
        /// </summary>
        internal static string SMTPServerName {
            get {
                return ResourceManager.GetString("SMTPServerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SMTP User Name.
        /// </summary>
        internal static string SMTPUserName {
            get {
                return ResourceManager.GetString("SMTPUserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sn.
        /// </summary>
        internal static string Sn {
            get {
                return ResourceManager.GetString("Sn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sort Order.
        /// </summary>
        internal static string SortOrder {
            get {
                return ResourceManager.GetString("SortOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sort order can not be blank.
        /// </summary>
        internal static string SortOrdercannotbeblank {
            get {
                return ResourceManager.GetString("SortOrdercannotbeblank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sort order can not be blank for menu.
        /// </summary>
        internal static string sortordercannotbeblankforMenu {
            get {
                return ResourceManager.GetString("sortordercannotbeblankforMenu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sort order cannot be greater than 255.
        /// </summary>
        internal static string SortOrderCannotbegreaterthan {
            get {
                return ResourceManager.GetString("SortOrderCannotbegreaterthan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Source Columns.
        /// </summary>
        internal static string SourceColumns {
            get {
                return ResourceManager.GetString("SourceColumns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Specialization.
        /// </summary>
        internal static string Specialization {
            get {
                return ResourceManager.GetString("Specialization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Specialization Master.
        /// </summary>
        internal static string SpecializationMaster {
            get {
                return ResourceManager.GetString("SpecializationMaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Database error occured.
        /// </summary>
        internal static string SqlException {
            get {
                return ResourceManager.GetString("SqlException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Request Based on Call Type.
        /// </summary>
        internal static string SRBasedonCalltype {
            get {
                return ResourceManager.GetString("SRBasedonCalltype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Requests Count.
        /// </summary>
        internal static string SRCount {
            get {
                return ResourceManager.GetString("SRCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service request not found.
        /// </summary>
        internal static string SRNotFound {
            get {
                return ResourceManager.GetString("SRNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Standard Hours.
        /// </summary>
        internal static string StandardHours {
            get {
                return ResourceManager.GetString("StandardHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Standard Time.
        /// </summary>
        internal static string standardtime {
            get {
                return ResourceManager.GetString("standardtime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Number.
        /// </summary>
        internal static string startnumber {
            get {
                return ResourceManager.GetString("startnumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start number cannot be null or zero.
        /// </summary>
        internal static string startnumbercannotbenullorzero {
            get {
                return ResourceManager.GetString("startnumbercannotbenullorzero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to State.
        /// </summary>
        internal static string State {
            get {
                return ResourceManager.GetString("State", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to State English.
        /// </summary>
        internal static string stateenglish {
            get {
                return ResourceManager.GetString("stateenglish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to State Locale.
        /// </summary>
        internal static string statelocale {
            get {
                return ResourceManager.GetString("statelocale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status.
        /// </summary>
        internal static string status {
            get {
                return ResourceManager.GetString("status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Step Link.
        /// </summary>
        internal static string StepLink {
            get {
                return ResourceManager.GetString("StepLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Step Name.
        /// </summary>
        internal static string StepName {
            get {
                return ResourceManager.GetString("StepName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Steps.
        /// </summary>
        internal static string Steps {
            get {
                return ResourceManager.GetString("Steps", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Step Status.
        /// </summary>
        internal static string StepStatus {
            get {
                return ResourceManager.GetString("StepStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Step Type.
        /// </summary>
        internal static string StepType {
            get {
                return ResourceManager.GetString("StepType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Success.
        /// </summary>
        internal static string Success {
            get {
                return ResourceManager.GetString("Success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Suffix.
        /// </summary>
        internal static string suffix {
            get {
                return ResourceManager.GetString("suffix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary.
        /// </summary>
        internal static string Summary {
            get {
                return ResourceManager.GetString("Summary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sunday.
        /// </summary>
        internal static string Sunday {
            get {
                return ResourceManager.GetString("Sunday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sundry Detail.
        /// </summary>
        internal static string SundryDetail {
            get {
                return ResourceManager.GetString("SundryDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sundry Details.
        /// </summary>
        internal static string SundryDetails {
            get {
                return ResourceManager.GetString("SundryDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sundry Job Description.
        /// </summary>
        internal static string SundryJobDescription {
            get {
                return ResourceManager.GetString("SundryJobDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sundry Total Amount.
        /// </summary>
        internal static string SundryTotalAmount {
            get {
                return ResourceManager.GetString("SundryTotalAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Table Name.
        /// </summary>
        internal static string TableName {
            get {
                return ResourceManager.GetString("TableName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax.
        /// </summary>
        internal static string Tax {
            get {
                return ResourceManager.GetString("Tax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Taxable.
        /// </summary>
        internal static string Taxable {
            get {
                return ResourceManager.GetString("Taxable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Taxable Other Charges 1.
        /// </summary>
        internal static string Taxableothercharges1 {
            get {
                return ResourceManager.GetString("Taxableothercharges1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Taxable Other Charges 1 Amount.
        /// </summary>
        internal static string Taxableothercharges1Amount {
            get {
                return ResourceManager.GetString("Taxableothercharges1Amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Taxable Other Charges 2.
        /// </summary>
        internal static string Taxableothercharges2 {
            get {
                return ResourceManager.GetString("Taxableothercharges2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Taxable Other Charges 2 Amount.
        /// </summary>
        internal static string Taxableothercharges2Amount {
            get {
                return ResourceManager.GetString("Taxableothercharges2Amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Amount.
        /// </summary>
        internal static string Taxamount {
            get {
                return ResourceManager.GetString("Taxamount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Code.
        /// </summary>
        internal static string TaxCode {
            get {
                return ResourceManager.GetString("TaxCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Code Name.
        /// </summary>
        internal static string TaxCodeName {
            get {
                return ResourceManager.GetString("TaxCodeName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Detail.
        /// </summary>
        internal static string TaxDetail {
            get {
                return ResourceManager.GetString("TaxDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Details.
        /// </summary>
        internal static string TaxDetails {
            get {
                return ResourceManager.GetString("TaxDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax percentage.
        /// </summary>
        internal static string taxpercentage {
            get {
                return ResourceManager.GetString("taxpercentage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Structure.
        /// </summary>
        internal static string TaxStructure {
            get {
                return ResourceManager.GetString("TaxStructure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Structure Detail.
        /// </summary>
        internal static string taxstructuredetail {
            get {
                return ResourceManager.GetString("taxstructuredetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Structure Details.
        /// </summary>
        internal static string taxstructuredetails {
            get {
                return ResourceManager.GetString("taxstructuredetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Structure .
        /// </summary>
        internal static string taxstructureenglish {
            get {
                return ResourceManager.GetString("taxstructureenglish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Structure Header.
        /// </summary>
        internal static string taxstructureheader {
            get {
                return ResourceManager.GetString("taxstructureheader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Structure .
        /// </summary>
        internal static string taxstructurelocale {
            get {
                return ResourceManager.GetString("taxstructurelocale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Structure Name.
        /// </summary>
        internal static string taxstructurename {
            get {
                return ResourceManager.GetString("taxstructurename", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Type.
        /// </summary>
        internal static string taxtype {
            get {
                return ResourceManager.GetString("taxtype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax type is referenced in formula cannot delete.
        /// </summary>
        internal static string taxtypeisreferencedinformulacannotdelete {
            get {
                return ResourceManager.GetString("taxtypeisreferencedinformulacannotdelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Team.
        /// </summary>
        internal static string Team {
            get {
                return ResourceManager.GetString("Team", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Terms.
        /// </summary>
        internal static string Terms {
            get {
                return ResourceManager.GetString("Terms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Terms And Conditions.
        /// </summary>
        internal static string TermsAndConditions {
            get {
                return ResourceManager.GetString("TermsAndConditions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Terms &amp; Conditions.
        /// </summary>
        internal static string TermsConditions {
            get {
                return ResourceManager.GetString("TermsConditions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The company/dealer has already been associated.
        /// </summary>
        internal static string TheCompanyDealerhasalreadybeenassociated {
            get {
                return ResourceManager.GetString("TheCompanyDealerhasalreadybeenassociated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The tax structure has already been selected .
        /// </summary>
        internal static string ThetaxStructurehasalreadybeenselected {
            get {
                return ResourceManager.GetString("ThetaxStructurehasalreadybeenselected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 36 To 48 Hours.
        /// </summary>
        internal static string ThirtySixToFourtyEightHours {
            get {
                return ResourceManager.GetString("ThirtySixToFourtyEightHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This role is already selected for the user.
        /// </summary>
        internal static string Thisroleisalreadyselectedfortheuser {
            get {
                return ResourceManager.GetString("Thisroleisalreadyselectedfortheuser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This serial number is already associated with the customer.
        /// </summary>
        internal static string ThisSerialNumberisalreadyassociatedwiththecustomer {
            get {
                return ResourceManager.GetString("ThisSerialNumberisalreadyassociatedwiththecustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thursday.
        /// </summary>
        internal static string Thursday {
            get {
                return ResourceManager.GetString("Thursday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To.
        /// </summary>
        internal static string To {
            get {
                return ResourceManager.GetString("To", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To Date.
        /// </summary>
        internal static string todate {
            get {
                return ResourceManager.GetString("todate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To Date cannot be greater than Current Date.
        /// </summary>
        internal static string ToDatecannotbegreaterthanCurrentDate {
            get {
                return ResourceManager.GetString("ToDatecannotbegreaterthanCurrentDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To Date cannot be less than Current Date.
        /// </summary>
        internal static string ToDatecannotbelessthanCurrentDate {
            get {
                return ResourceManager.GetString("ToDatecannotbelessthanCurrentDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To date cannot be less than from date.
        /// </summary>
        internal static string todatecannotbelessthenfromdatedate {
            get {
                return ResourceManager.GetString("todatecannotbelessthenfromdatedate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To date must be greater than or equal to From date.
        /// </summary>
        internal static string todatemustbegreaterthanorequaltofromdate {
            get {
                return ResourceManager.GetString("todatemustbegreaterthanorequaltofromdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top 10 Models.
        /// </summary>
        internal static string TopModel {
            get {
                return ResourceManager.GetString("TopModel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top 10 Models with Maximum Service Request.
        /// </summary>
        internal static string TopModelwithMaximumSR {
            get {
                return ResourceManager.GetString("TopModelwithMaximumSR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To Step.
        /// </summary>
        internal static string ToStep {
            get {
                return ResourceManager.GetString("ToStep", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total.
        /// </summary>
        internal static string Total {
            get {
                return ResourceManager.GetString("Total", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Allocated Hours.
        /// </summary>
        internal static string TotalAllocatedHours {
            get {
                return ResourceManager.GetString("TotalAllocatedHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Amount.
        /// </summary>
        internal static string TotalAmount {
            get {
                return ResourceManager.GetString("TotalAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total On.
        /// </summary>
        internal static string TotalOn {
            get {
                return ResourceManager.GetString("TotalOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Quotation Amount.
        /// </summary>
        internal static string TotalQuotationAmount {
            get {
                return ResourceManager.GetString("TotalQuotationAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Taxable Amount.
        /// </summary>
        internal static string TotalTaxableAmount {
            get {
                return ResourceManager.GetString("TotalTaxableAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total taxable amount is blank.
        /// </summary>
        internal static string TotalTaxableAmountBlank {
            get {
                return ResourceManager.GetString("TotalTaxableAmountBlank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Working Hours.
        /// </summary>
        internal static string TotalWorkingHours {
            get {
                return ResourceManager.GetString("TotalWorkingHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transaction has already been locked.
        /// </summary>
        internal static string TransactionisalreadybeenLocked {
            get {
                return ResourceManager.GetString("TransactionisalreadybeenLocked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transaction has already been unlocked.
        /// </summary>
        internal static string TransactionisalreadybeenUnLocked {
            get {
                return ResourceManager.GetString("TransactionisalreadybeenUnLocked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transaction Locked Successfully.
        /// </summary>
        internal static string TransactionLockedSuccessfully {
            get {
                return ResourceManager.GetString("TransactionLockedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transaction Unlocked Successfully.
        /// </summary>
        internal static string TransactionUnLockedSuccessfully {
            get {
                return ResourceManager.GetString("TransactionUnLockedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tuesday.
        /// </summary>
        internal static string Tuesday {
            get {
                return ResourceManager.GetString("Tuesday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 24 To 36 Hours.
        /// </summary>
        internal static string TwentyFourToThirtySixHours {
            get {
                return ResourceManager.GetString("TwentyFourToThirtySixHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type.
        /// </summary>
        internal static string Type {
            get {
                return ResourceManager.GetString("Type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unit Of Measurement.
        /// </summary>
        internal static string unitofmeasurement {
            get {
                return ResourceManager.GetString("unitofmeasurement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UnLock.
        /// </summary>
        internal static string UnLock {
            get {
                return ResourceManager.GetString("UnLock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Verification Queue.
        /// </summary>
        internal static string UnRegisteredServiceRequest {
            get {
                return ResourceManager.GetString("UnRegisteredServiceRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UOM.
        /// </summary>
        internal static string uom {
            get {
                return ResourceManager.GetString("uom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload File.
        /// </summary>
        internal static string UploadFile {
            get {
                return ResourceManager.GetString("UploadFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload Parts.
        /// </summary>
        internal static string UploadParts {
            get {
                return ResourceManager.GetString("UploadParts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Usage Environment.
        /// </summary>
        internal static string usageenvironment {
            get {
                return ResourceManager.GetString("usageenvironment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User.
        /// </summary>
        internal static string User {
            get {
                return ResourceManager.GetString("User", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User data saved successfully.
        /// </summary>
        internal static string UserDataSavedSuccessfully {
            get {
                return ResourceManager.GetString("UserDataSavedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Detail.
        /// </summary>
        internal static string UserDetail {
            get {
                return ResourceManager.GetString("UserDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Details.
        /// </summary>
        internal static string UserDetails {
            get {
                return ResourceManager.GetString("UserDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User dont have access to Party Master.
        /// </summary>
        internal static string Userdonthaveaccesstopartymaster {
            get {
                return ResourceManager.GetString("Userdonthaveaccesstopartymaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User dont have access to Product Master.
        /// </summary>
        internal static string Userdonthaveaccesstoproductmaster {
            get {
                return ResourceManager.GetString("Userdonthaveaccesstoproductmaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User dont have Edit Access.
        /// </summary>
        internal static string Userdonthaveeditaccess {
            get {
                return ResourceManager.GetString("Userdonthaveeditaccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User dont have Edit access to Product Master.
        /// </summary>
        internal static string Userdonthaveeditaccesstoproductmaster {
            get {
                return ResourceManager.GetString("Userdonthaveeditaccesstoproductmaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User is locked.
        /// </summary>
        internal static string Userislocked {
            get {
                return ResourceManager.GetString("Userislocked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User is not active.
        /// </summary>
        internal static string Userisnotactive {
            get {
                return ResourceManager.GetString("Userisnotactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Name.
        /// </summary>
        internal static string UserName {
            get {
                return ResourceManager.GetString("UserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User name or password you entered is incorrect. Please try again...
        /// </summary>
        internal static string UserNameOrPasswordYouEnteredIsIncorrectPleaseTryAgain {
            get {
                return ResourceManager.GetString("UserNameOrPasswordYouEnteredIsIncorrectPleaseTryAgain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User-Role Details.
        /// </summary>
        internal static string UserRoleDetails {
            get {
                return ResourceManager.GetString("UserRoleDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Roles.
        /// </summary>
        internal static string UserRoles {
            get {
                return ResourceManager.GetString("UserRoles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Type.
        /// </summary>
        internal static string UserType {
            get {
                return ResourceManager.GetString("UserType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Utilization %.
        /// </summary>
        internal static string UtilizationPercentage {
            get {
                return ResourceManager.GetString("UtilizationPercentage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Value.
        /// </summary>
        internal static string Value {
            get {
                return ResourceManager.GetString("Value", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Value is Mandatory for selected Column.
        /// </summary>
        internal static string ValueisMandatoryforselectedColumn {
            get {
                return ResourceManager.GetString("ValueisMandatoryforselectedColumn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ver.
        /// </summary>
        internal static string Ver {
            get {
                return ResourceManager.GetString("Ver", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Verification Queue.
        /// </summary>
        internal static string VerificationQueue {
            get {
                return ResourceManager.GetString("VerificationQueue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Version.
        /// </summary>
        internal static string Version {
            get {
                return ResourceManager.GetString("Version", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Version Date.
        /// </summary>
        internal static string VersionDate {
            get {
                return ResourceManager.GetString("VersionDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Version Number.
        /// </summary>
        internal static string VersionNumber {
            get {
                return ResourceManager.GetString("VersionNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Version Number and Date.
        /// </summary>
        internal static string VersionNumberandDate {
            get {
                return ResourceManager.GetString("VersionNumberandDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View.
        /// </summary>
        internal static string view {
            get {
                return ResourceManager.GetString("view", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Job Card.
        /// </summary>
        internal static string ViewJobCard {
            get {
                return ResourceManager.GetString("ViewJobCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Parts Master.
        /// </summary>
        internal static string ViewPartsMaster {
            get {
                return ResourceManager.GetString("ViewPartsMaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warranty Date.
        /// </summary>
        internal static string WarrantyDate {
            get {
                return ResourceManager.GetString("WarrantyDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Products Under Warranty.
        /// </summary>
        internal static string warrantydetails {
            get {
                return ResourceManager.GetString("warrantydetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Website.
        /// </summary>
        internal static string Website {
            get {
                return ResourceManager.GetString("Website", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Wednesday.
        /// </summary>
        internal static string Wednesday {
            get {
                return ResourceManager.GetString("Wednesday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Welcome :.
        /// </summary>
        internal static string Welcome {
            get {
                return ResourceManager.GetString("Welcome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Where.
        /// </summary>
        internal static string Where {
            get {
                return ResourceManager.GetString("Where", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Work Flow.
        /// </summary>
        internal static string WorkFlow {
            get {
                return ResourceManager.GetString("WorkFlow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Work Flow ID.
        /// </summary>
        internal static string WorkFlowID {
            get {
                return ResourceManager.GetString("WorkFlowID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Work Flow Name.
        /// </summary>
        internal static string WorkFlowName {
            get {
                return ResourceManager.GetString("WorkFlowName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Work Flow Steps.
        /// </summary>
        internal static string WorkFlowSteps {
            get {
                return ResourceManager.GetString("WorkFlowSteps", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Working Days.
        /// </summary>
        internal static string WorkingDays {
            get {
                return ResourceManager.GetString("WorkingDays", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Working Time.
        /// </summary>
        internal static string WorkingTime {
            get {
                return ResourceManager.GetString("WorkingTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Year.
        /// </summary>
        internal static string Year {
            get {
                return ResourceManager.GetString("Year", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Year should be between 2000 and 2999.
        /// </summary>
        internal static string Yearshouldbebetween2000and2999 {
            get {
                return ResourceManager.GetString("Yearshouldbebetween2000and2999", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        internal static string yes {
            get {
                return ResourceManager.GetString("yes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You do not have edit permission.
        /// </summary>
        internal static string Youdonothaveeditpermission {
            get {
                return ResourceManager.GetString("Youdonothaveeditpermission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have been Logged out successfully.
        /// </summary>
        internal static string YouhavebeenLoggedoutsuccessfully {
            get {
                return ResourceManager.GetString("YouhavebeenLoggedoutsuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zip Code.
        /// </summary>
        internal static string ZipCode {
            get {
                return ResourceManager.GetString("ZipCode", resourceCulture);
            }
        }
    }
}
