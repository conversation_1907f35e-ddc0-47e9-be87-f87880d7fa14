﻿//Coded By Muralidhara H M :-P
//Setting and Getting Resource 
var Resources = {
    RateCannotbeZero: '', QuantityCannotbeZero: '',
    NotFound: '', Duplicate: '', Areyousurewanttodelete: '', Error: '', Pleaseselectrecordstodelete: '', AmountIsBeyondAcceptableLimit: '', DocumentExport: '', Excel: '', PDF: '', Export: ''
   , Equal: '', NotEqual: '', Like: '', LessThan: '', GreaterThan: '', AND: '', OR: '', AdvanceSearch: '', SelectColumn: '', SelectOperator: '', Value: '', SelectCondition: '', PleaseselectaColumn: '', PleaseselectaOperator: '',
    ValueisMandatoryforselectedColumn: '', PleaseenterIntegerValue: '', PleaseselectaCondition: '', AddFilter: '', RemoveFilter: '', From: '', Where: '', Youdonothaveeditpermission: '', RateCannotbeZero: '',
    supersessionexist: '', PrecedingPartDetails: '', Prefix: '', PartNumber: '', Description: '', CurrentPartDetails: '', NewPartDetails: '', PartDetails: '', Cancel: '', Inactive: '',
};

$.getMessage = function (key) {
    return Resources[key];
}

$.setMessage = function (key, Msg) {
    Resources[key] = Msg;
}
$.SelectDefaultValInDDL = function (id_DDL) {
    var length = $('#' + id_DDL)[0].length;
    if (length == 2) {
        $('#' + id_DDL).val($($('#' + id_DDL).children()[1]).val());
    }
};
//----------------------------------------------

//Coded By Muralidhara H M :-P
//-----------------------------------------------------JQGrid Functionality-----------------------------------------------------------------------------------------
//Adding New Row with EmptyData to JQGrid and Making it Editable 
$.fn.addGridRow = function (className) {

    var grd = $(this);
    var rowsid = grd.jqGrid('getDataIDs');
    var nRwID = $(this).attr('id') + rowsid[rowsid.length - 1] + rowsid.length;

    var img = "<img src='" + $.url('/Content/Images/Minus.gif') + "' + class='" + className + "' id='img" + nRwID + "' editmode='' />";


    grd.addRowData(nRwID, { edit: img }, 'last');
    rowsid = grd.jqGrid('getDataIDs');  //Included by siva
    $('.' + className).unbind('click');
    $('.' + className).click(function () {
        //Included by siva--Start
        var NoOfRowEdited = 0;
        for (var i = 0; i < rowsid.length; i++) {
            var RowEdited = $(grd.getCell(rowsid[i], "edit")).attr("editmode");
            if (RowEdited == "true" || RowEdited == '')
                NoOfRowEdited += 1;
        } //--End
        this.rowid = $(this).parent('td').parent('tr').attr('id');
        grd.delRowData(this.rowid);

        //Included by siva--Start
        if (NoOfRowEdited == 1) {
            grd.trigger("reloadGrid");
        }//--End
    });

    grd.editRow(nRwID);
};

//Coded By Muralidhara H M :-P
//Adding New Row withData to JQGrid and Making it Editable 
$.fn.AddJQGridRow = function (Obj) {
    
    var grd = $(this);
    var className = $(this).attr('id') + 'addRow';
    var rowsid = grd.jqGrid('getDataIDs');
    var nRwID = $(this).attr('id') + rowsid[rowsid.length - 1] + rowsid.length;

    var img = "<img src='" + $.url('/Content/Images/Minus.gif') + "' + class='" + className + "' id='img" + nRwID + "' editmode='' />";

    var obj = {};

    if (Obj.data != undefined && Obj.data != null) {
        obj = Obj.data;
    }
    obj.edit = img;
    grd.addRowData(nRwID, obj, 'last');
    rowsid = grd.jqGrid('getDataIDs');  //Included by siva
    $('.' + className).unbind('click');
    $('.' + className).click(function () {
        //Included by siva--Start
        var NoOfRowEdited = 0;
        for (var i = 0; i < rowsid.length; i++) {
            var RowEdited = $(grd.getCell(rowsid[i], "edit")).attr("editmode");
            if (RowEdited == "true" || RowEdited == '')
                NoOfRowEdited += 1;
        } //--End

        this.rowid = $(this).parent('td').parent('tr').attr('id');
        grd.delRowData(this.rowid);

        //Included by siva--Start
        if (NoOfRowEdited == 1) {
            grd.trigger("reloadGrid");
        }//--End

    });

    grd.editRow(nRwID);

};
//Coded By Muralidhara H M :-P
//Deleting JQGrid Rows and from Database
$.fn.delGridRows = function (url) {
    var grid = $(this);
    var rows = grid.jqGrid('getDataIDs');
    var sel = false;
    var ids = "{rows:[";
    for (i = 0; i < rows.length; i++) {

        var chkbox = $(grid.getCell(rows[i], 'delete'));

        if (chkbox.attr('checked') == 'checked') {
            sel = true;
            ids = ids.concat("{id:\'" + chkbox.attr('key') + "\'},");
        }

    }
    ids = ids.concat("]}");
    if (sel) {

        if (confirm($.getMessage('Areyousurewanttodelete'))) {
            $.ajax(
           {
               url: url,
               data: { key: ids },
               type: 'POST',
               datatype: 'json',
               success: function (data) {
                   if ($.getMessage('UserCulturedeletedsuccessfully') == data || $.getMessage('GeneralCulturedeletedsuccessfully') == data) {
                       grid.trigger('reloadGrid');
                   }
                   alert(data);

               },
               error: function (e) {

                   alert($.getMessage('Error'));
               }
           });
        }
    }
    else {
        alert($.getMessage('Pleaseselectrecordstodelete'));
    }
}

//Coded By Muralidhara H M :-P
//Making Inline Editing for JQGrid
$.fn.makeRowEditable = function (className, IsEdit) {
    
    
    var grd = $(this);
    IsEdit = IsEdit == undefined ? true : IsEdit;
    $('.' + className).click(function (event) {
        
        
        if (IsEdit) {
            this.rowid = $(this).parent('td').parent('tr').attr('id');
            if ($(this).attr('editmode') == 'true') {
                //Included by siva--Start
                rowsid = grd.jqGrid('getDataIDs');
                var NoOfRowEdited = 0;
                for (var i = 0; i < rowsid.length; i++) {
                    var RowEdited = $(grd.getCell(rowsid[i], "edit")).attr("editmode");
                    if (RowEdited == "true" || RowEdited == '')
                        NoOfRowEdited += 1;
                }

                if (NoOfRowEdited == 1) {
                    grd.trigger("reloadGrid");
                }//--End
                $(this).attr('editmode', false)
                grd.restoreRow(this.rowid);
                $(this).attr('src', $.url('/Content/Images/edit.gif'));

                event.stopImmediatePropagation();
            }
            else {

                $(this).attr('editmode', true)
                grd.editRow(this.rowid);
                $(this).attr('src', $.url('/Content/Images/Cancel.gif'));
            }
        }
        else {
            alert($.getMessage('Youdonothaveeditpermission'));
        }
    });
}

//Coded By Muralidhara H M :-P
//Making Checkbox checked in Grid
$.fn.checkable = function (className) {
    $('.' + className).click(function () {
        $(this).check();
    });

}
$.fn.check = function () {
    var chkd = $(this).attr('checked');
    if (chkd == 'checked') {
        $(this).attr('checked', chkd);
    }
    else {
        $(this).removeAttr('checked');
    }
}
//Coded By Muralidhara H M :-P
//Checking for existence of records and duplicate entries in the JQGrid text Box 
$.fn.txtUniqueness = function (rowID, columnName, friendlyColName, urlPath) {    
    var grd = $(this);
    var txtid = '#' + $(grd.getCell(rowID, columnName)).attr('id');
    $(txtid).attr('duplicate', 'false');
    $(txtid).attr('exists', 'false');
    $(txtid).change(function () {
        
        var isexist = false;
        var txtValue =  $.trim($(this).val()).toUpperCase();
        if (txtValue != "") {
            var rowIDS = grd.jqGrid('getDataIDs');
            for (i = 0; i < rowIDS.length; i++) {
                var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');
                if (editmode != 'false' && rowIDS[i] != rowID) {
                    var colValue = document.getElementById($(grd.getCell(rowIDS[i], columnName)).attr('id')).value;
                    colValue = $.trim(colValue).toUpperCase();
                    if (txtValue == colValue) {
                        isexist = true;
                        if (document.getElementById($(txtid).attr('id')).nodeName.toLocaleLowerCase() == 'select') {
                            $(txtid).attr('value', '-1');
                        }
                        else {
                            $(txtid).attr('value', '');
                        }
                        alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                        return;
                    }

                }
            }
        }
        $.ajax(
                {
                    url: urlPath,
                    type: 'POST',
                    data: { name: $.EncryptString(txtValue) },
                    datatype: 'json',
                    success: function (data) {
                        
                        if (data.toLocaleLowerCase() == 'true') {
                            if (document.getElementById($(txtid).attr('id')).nodeName.toLocaleLowerCase() == 'select') {
                                $(txtid).attr('value', '-1');
                            }
                            else {
                                $(txtid).attr('value', '');
                            }
                            alert($.getMessage('Duplicate') + ' ' + friendlyColName);

                        }

                    },
                    error: function (e) {

                        alert($.getMessage('Error'));
                    }
                });
    });
};

//Coded By Muralidhara H M :-P
///Load DropDown with JsonData
$.fn.LoadOptions = function (data, name, value) {
    if (data != undefined && data != null) {
        this.sel = $(this);
        for (i = 0; i < data.length; i++) {
            this.sel.append($("<option style='color:black;' title='" + $(data[i]).attr(name) + "' value='" + $(data[i]).attr(value) + "' >" + $(data[i]).attr(name) + "</option>"));
        }
        if ($(this).selector != '#SelServiceType' && $(this).selector != '#SelJobCardDelayReason' && $(this).selector != '#SelJobCardAbandonReason' && $(this).selector != '#ddlContactPerson' && $(this).selector != '#SelJobPriority' && $(this).selector != '#DdlPrimarySegment' && $(this).selector != '#DdlSecondarySegment' && $(this).selector != '#SelMachineStatus' && $(this).selector != '#SelCampaign' && $(this).selector != '#SelJobSiteAddress') {
            //if (data.length == 1) {
            //    document.getElementById($(this).attr('id')).selectedIndex = 1;
            //    $(this).change();
            //}
        }
    }


}

///Clear DropDown 
$.ClearDropdown = function (DdlObject) {
    var OptionsDdlObject = DdlObject.options;
    for (var j = 1; j < OptionsDdlObject.length; j++) {
        OptionsDdlObject.remove(j);
        j--;
    }
}

//Adding CSS to grid Cell
$.AddRemoveValidationCSS = function (ControlObject, Type) {
    if (Type == 1) {

        if (ControlObject.className.indexOf("input-validation-error") == -1)
            ControlObject.className = ControlObject.className + " input-validation-error";
    }
    else {
        ControlObject.className = (ControlObject.className).replace("input-validation-error", "");
    }

}

$.fn.ValidatePaste = function (obj) {


    switch (obj.ValidateType) {
        case 'Number':
            if (window.event.dataTransfer == null) {
                var b = clipboardData.getData("Text");
                if (!isNaN(b)) {
                    if (obj.ValidateLength) {
                        var d = $(this).attr('value');

                        if (d.length <= obj.length) {
                            clipboardData.setData("Text", b.substr(0, (obj.length - d.length)));
                        }
                        else {
                            clipboardData.setData("Text", '');
                        }
                    }
                }
                else {
                    clipboardData.setData("Text", '');
                }
            }
            else {
                var b = window.event.dataTransfer.getData("Text");

                if (!isNaN(b)) {
                    if (obj.ValidateLength) {
                        var d = $(this).attr('value');

                        if (d.length <= obj.length) {
                            window.event.dataTransfer.setData("Text", b.substr(0, (obj.length - d.length)));
                        }
                        else {
                            window.event.dataTransfer.setData("Text", '');
                        }
                    }
                }
                else {
                    window.event.dataTransfer.setData("Text", '');
                }
            }

            break;
        case 'Decimal':
            var b = isNaN(clipboardData.getData("Text"));
            if (b) {
                clipboardData.setData("Text", '');
            }
        case 'Text':
            if (window.event.dataTransfer == null) {
                var b = clipboardData.getData("Text");

                if (obj.ValidateLength) {
                    var d = $(this).attr('value');

                    if (d.length <= obj.length) {
                        clipboardData.setData("Text", b.substr(0, (obj.length - d.length)));
                    }
                    else {
                        clipboardData.setData("Text", '');
                    }
                }
            }
            else {
                var b = window.event.dataTransfer.getData("Text");

                if (obj.ValidateLength) {
                    var d = $(this).attr('value');

                    if (d.length <= obj.length) {
                        window.event.dataTransfer.setData("Text", b.substr(0, (obj.length - d.length)));
                    }
                    else {
                        window.event.dataTransfer.setData("Text", '');
                    }
                }
            }
            break;
        case 'OnlyText':
            if (window.event.dataTransfer == null) {

                var b = clipboardData.getData("Text");
                var isInvalid = false;
                for (i = 0; i < b.length; i++) {

                    if (b.charCodeAt(i) < 65 || b.charCodeAt(i) > 122 || isNaN(b.charCodeAt(i))) {
                        isInvalid = true;
                    }
                }
                if (!isInvalid) {
                    if (obj.ValidateLength) {
                        var d = $(this).attr('value');

                        if (d.length <= obj.length) {
                            clipboardData.setData("Text", b.substr(0, (obj.length - d.length)));
                        }
                        else {
                            clipboardData.setData("Text", '');
                        }
                    }
                }
                else {
                    clipboardData.setData("Text", '');
                }
            }
            else {
                var b = window.event.dataTransfer.getData("Text");
                var isInvalid = false;
                for (i = 0; i < b.length; i++) {

                    if (b.charCodeAt(i) < 65 || b.charCodeAt(i) > 122 || isNaN(b.charCodeAt(i))) {
                        isInvalid = true;
                    }
                }
                if (!isInvalid) {
                    if (obj.ValidateLength) {
                        var d = $(this).attr('value');

                        if (d.length <= obj.length) {
                            window.event.dataTransfer.setData("Text", b.substr(0, (obj.length - d.length)));
                        }
                        else {
                            window.event.dataTransfer.setData("Text", '');
                        }
                    }
                }
                else {
                    window.event.dataTransfer.setData("Text", '');
                }
            }
            break;
        default:
            break;
    }

    return true;
}
$.fn.checkTextLength = function (len, e) {
    var val = $(this).attr('value');
    if (e.which == 8 || e.which == 0 || e.which == 37 || e.which == 38 || e.which == 39 || e.which == 40) {
        return true;
    }
    else if (val.length < len) {
        return true
    }
    else {
        e.preventDefault();
    }
}
//Coded By Muralidhara H M :-P
//Making Inline Editing for JQGrid
$.fn.makeRowEditableWithCustomFunc = function (className) {

    var grd = $(this);

    $('.' + className).click(function (event) {
        if ($(this).attr('editmode') == 'true') {
            $(this).attr('editmode', false)
            this.rowid = $(this).parent('td').parent('tr').attr('id');
            this.oldval = $(grd.getCell(this.rowid, 'Amount')).attr('value');
            this.oldval = this.oldval == '' ? 0 : Math.abs(this.oldval);
            this.sum = $(grd.footerData('get')).attr('Amount');
            this.sum = this.sum == '' ? 0 : Math.abs(this.sum);
            this.sum = (this.sum - this.oldval);
            grd.restoreRow($(this).attr('key'));
            this.oldval = grd.getCell(this.rowid, 'Amount');
            this.oldval = this.oldval == '' ? 0 : Math.abs(this.oldval);
            this.sum = (this.sum + this.oldval);
            grd.footerData('set', { Amount: this.sum, Quantity: 'Total' });

            $(this).attr('src', $.url('/Content/Images/edit.gif'));

        }
        else {

            $(this).attr('editmode', true)
            grd.editRow($(this).attr('key'));
            $(this).attr('src', $.url('/Content/Images/Cancel.gif'))
        }
    });
}

//Coded By Muralidhara H M :-P
//To enter only decimal len-Total length, prec- Precision, e-Event
$.fn.checkDecimal = function (len, prec, e) {

    if (e.which == 8 || e.which == 0) {
        return true;
    }
    else if ((e.which < 48 || e.which > 57) && e.which != 46) {
        e.preventDefault();
    }

    else {

        var value = $(this).val();

        if (e.which == 46) {
            if (value.indexOf('.') == '-1' && prec != 0) {

                return true;

            }
            else {
                e.preventDefault();
            }
        }
        else {

            if (value.indexOf('.') == '-1' && value.length < (len - prec)) {
                return true;
            }
            else if (value.indexOf('.') != '-1') {

                if ((value.substr(value.indexOf('.')).length - 1) < prec && e.target.selectionStart > value.indexOf('.')) {
                    return true;
                }
                else if (e.target.selectionStart < (len - prec) && (value.substr(0, value.indexOf('.')).length < (len - prec)) && (e.target.selectionStart - 1 <= value.indexOf('.'))) {

                    return true;
                }
                else {
                    e.preventDefault();
                }
            }
            else {
                e.preventDefault();
            }
        }
    }

};


$.fn.checkDecimalParty = function (len, prec, e) {

    if (e.which == 8 || e.which == 0 || e.which == 32 || e.which == 44) {
        return true;
    }
    else if ((e.which < 48 || e.which > 57) && e.which != 46) {
        e.preventDefault();
    }

    else {

        var value = $(this).val();

        if (e.which == 46) {
            if (value.indexOf('.') == '-1' && prec != 0) {

                return true;

            }
            else {
                e.preventDefault();
            }
        }
        else {

            if (value.indexOf('.') == '-1' && value.length < (len - prec)) {
                return true;
            }
            else if (value.indexOf('.') != '-1') {

                if ((value.substr(value.indexOf('.')).length - 1) < prec && e.target.selectionStart > value.indexOf('.')) {
                    return true;
                }
                else if (e.target.selectionStart < (len - prec) && (value.substr(0, value.indexOf('.')).length < (len - prec)) && (e.target.selectionStart - 1 <= value.indexOf('.'))) {

                    return true;
                }
                else {
                    e.preventDefault();
                }
            }
            else {
                e.preventDefault();
            }
        }
    }

};

$.fn.checkNegativeDecimal = function (len, prec, e) {

    if (e.which == 8 || e.which == 0) {
        return true;
    }
    else if ((e.which < 48 || e.which > 57) && e.which != 46 && e.which != 45) {
        e.preventDefault();
    }

    else {

        var value = $(this).val();
        if (value.indexOf('-') != '-1') {
            len = len + 1;

        }

        if (e.which == 45) {
            if (value.indexOf('-') == '-1' && e.target.selectionStart == 0) {

                return true;

            }
            else {
                e.preventDefault();
            }
        }
        else if (e.which == 46) {
            if (value.indexOf('.') == '-1' && prec != 0) {

                return true;

            }
            else {
                e.preventDefault();
            }
        }
        else {

            if (value.indexOf('.') == '-1' && value.length < (len - prec)) {
                return true;
            }
            else if (value.indexOf('.') != '-1') {

                if ((value.substr(value.indexOf('.')).length - 1) < prec && e.target.selectionStart > value.indexOf('.')) {
                    return true;
                }
                else if (e.target.selectionStart < (len - prec) && (value.substr(0, value.indexOf('.')).length < (len - prec)) && (e.target.selectionStart - 1 <= value.indexOf('.'))) {

                    return true;
                }
                else {
                    e.preventDefault();
                }
            }
            else {
                e.preventDefault();
            }
        }
    }


};
$.IsValidDecimal = function (value, len, prec) {
    value = value.toString()
    var val = '';
    var dec = 0;
    if (value.indexOf('.') == -1) {
        val = value.substr(0);
        dec = 0;
    }
    else {
        val = value.substr(0, value.indexOf('.'));
        dec = value.substr(value.indexOf('.'));
    }

    if (val.length <= (len - prec) && dec <= prec) {
        return true;
    }
    else {
        return false;
    }
}
$.fn.checkDot = function () {
    var val = $(this).val();
    if (val.indexOf('.') == '0' && val.length == 1) {
        $(this).attr('value', '');
    }
}

$.fn.IsValidNumber = function (evt) {
    var charCode = (evt.which) ? evt.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57))
        return false;
    return true;
}
//Coded By Muralidhara H M :-P
$.fn.disableDotDelete = function (len, prec, e) {

    if (e.which == 8 || e.which == 46) {
        var value = $(this).val();

        if (value.indexOf('.') != '-1') {
            return true;
        }
        else {

            if (value.length > (len - prec)) {

                var oldval = '';
                var id = $(this).attr('id');
                for (i = 0; i < value.length; i++) {
                    if (e.target.selectionStart == i) {
                        oldval = oldval.concat('.');
                    }
                    oldval = oldval.concat(value[i]);
                }
                document.getElementById(id).value = oldval;
                document.getElementById(id).focus();
            }
        }
    }
}
//Coded By Muralidhara H M :-P
$.fn.CalRateQuantity = function () {
    var grd = $(this);
    this.evnts = [{ type: 'keypress', fn: function (e) { $(this).checkDecimal(12, 2, e); } }, {
        type: 'change', fn: function (e) {
            $(this).checkDot();
            this.grd = grd;
            this.rowid = $(this).parent('td').parent('tr').attr('id');
            this.rateid = $(this.grd.getCell(this.rowid, 'Rate')).attr('id')
            this.rate = document.getElementById(this.rateid).value;
            this.qntyid = $(this.grd.getCell(this.rowid, 'Quantity')).attr('id')
            this.qnty = document.getElementById(this.qntyid).value;
            if (Math.abs(this.rate) == 0 && this.rate.length > 0) {
                document.getElementById(this.rateid).value = '';
                this.rate = '';
                alert($.getMessage('RateCannotbeZero'));

            }
            else if (Math.abs(this.qnty) == 0 && this.qnty.length > 0) {
                document.getElementById(this.qntyid).value = '';
                this.qnty = '';
                alert($.getMessage('QuantityCannotbeZero'));

            }
            this.rate = this.rate == '' ? 0 : this.rate;
            this.qnty = this.qnty == '' ? 0 : this.qnty;
            this.Id = $(this.grd.getCell(this.rowid, 'Amount')).attr('id');
            this.oldval = document.getElementById(this.Id).value;
            this.oldval = this.oldval == '' ? 0 : Math.abs(this.oldval);

            var IsValid = $.IsValidDecimal((parseFloat(this.rate) * parseFloat(this.qnty)), 12, 2);
            var rq = IsValid == true ? Math.abs(parseFloat(this.rate) * parseFloat(this.qnty)).toFixed(2) : 0;
            rq = parseFloat(rq);
            $('#' + this.Id).attr('value', rq);


            this.sum = $(this.grd.footerData('get')).attr('Amount');
            this.sum = $.DefaultNumber(this.sum);
            this.sum = (parseFloat(this.sum) - parseFloat(this.oldval));
            this.footerData = parseFloat(this.sum + rq).toFixed(2);

            this.grd.footerData('set', { Amount: this.footerData, Quantity: 'Total' });
            if (!IsValid) {

                $(this).attr('value', '');
                alert($.getMessage('AmountIsBeyondAcceptableLimit'));
            }
        }
    },
    //{ type: 'paste', fn: function (e) { $(this).ValidatePaste({ ValidateType: 'Decimal' }) } },
    { type: 'paste', fn: function () { return false; } },
    { type: 'keyup', fn: function (e) { $(this).disableDotDelete(12, 2, e); } }];

    grd.setColProp('Amount', { editable: true, edittype: 'text', editoptions: { ondrop: 'return false', tabindex: "-1", readonly: 'readonly', style: 'text-align:right', dataEvents: [{ type: 'mousedown', fn: function () { return false; } }] } });
    grd.setColProp('Rate', { editoptions: { ondrop: 'return false', dataEvents: this.evnts, style: 'text-align:right' } });
    grd.setColProp('Quantity', { editoptions: { ondrop: 'return false', dataEvents: this.evnts, style: 'text-align:right' } });

}
var IsLens = false;
$.SetIsLens = function (mode) {
    if (mode == 1) {
        IsLens = true;
    }
    else {
        IsLens = false;
    }
}
//Coded By Muralidhara H M :-P
$.fn.GridInlineSearch = function (Obj) {
    var grd = $(this);
    var TxtCol = Obj.TxtCol;
    var TxtMap = Obj.TxtMap;
    var SrchCol = Obj.SrchCol;
    var url = Obj.url;
    var ColMap = Obj.ColMap;
    var extraGrdPars = Obj.extraGrdPars;
    var IsFieldSearch = Obj.IsFieldSearch;
    var IdField = undefined;
    var FldUrl = undefined;
    var FldSrchID = undefined;
    var headerid = undefined;
    var headername = undefined;
    var events = Obj.events;
    var colModels = [];
    var colName = [];
    var FieldSearchName = undefined;
    var DefaultSortColName = undefined;

    if (IsFieldSearch != undefined && Obj.FieldSearch != undefined) {
        if (IsFieldSearch) {
            IdField = Obj.FieldSearch.IdField;
            FldUrl = Obj.FieldSearch.url;
            FldSrchID = Obj.FieldSearch.FldSrchID;
            headerid = Obj.FieldSearch.headerid == undefined ? '' : Obj.FieldSearch.headerid;
            headername = Obj.FieldSearch.headername == undefined ? '' : Obj.FieldSearch.headername;
            colModels = Obj.FieldSearch.colModels == undefined ? '' : Obj.FieldSearch.colModels;
            colName = Obj.FieldSearch.colName == undefined ? '' : Obj.FieldSearch.colName;
            FieldSearchName = Obj.FieldSearch.FieldSearchName == undefined ? '' : Obj.FieldSearch.FieldSearchName;
            DefaultSortColName = Obj.FieldSearch.DefaultSortColName == undefined ? '' : Obj.FieldSearch.DefaultSortColName;

        }
    }
    var checkDuplicate = Obj.checkDuplicate;
    var DupCol = '';
    var friendlyColName = '';
    var primCol = '';
    if (checkDuplicate) {
        DupCol = Obj.DupCol.ColName;
        friendlyColName = Obj.DupCol.friendlyColName;
        primCol = Obj.DupCol.primCol;
    }
    //else if (Obj.url == '/CoreProductMaster/SelectCustomer') {
        friendlyColName = Obj.DupCol.friendlyColName;
    //}

    this.evnts = [{
        type: 'change', fn: function (e) {
            if (!IsLens) {
                this.grd = grd;
                this.rowid = $(this).parent('td').parent('tr').attr('id');
                var rowid = this.rowid;

                this.txtBx = $(this.grd.getCell(this.rowid, TxtCol));
                this.Id = this.txtBx.attr('id');
                var Id = this.Id;

                this.txt = document.getElementById(this.Id).value;
                this.value = '';
                var txt = this.txt;
                $('#' + Id).attr('duplicate', 'false');
                $('#' + Id).attr('exists', 'false');
                $('#' + Id).focus();
                if (this.txt == '') {

                    jQuery.each(ColMap, function (key, val) {
                        //Modified by Shashi on 26-Feb-2014 for clearing dropdown
                        var hdn = "";
                        if (key != "IsReWork") {
                            if (key == "Dropdown") {
                                $.ClearDropDown($(grd.getCell(rowid, val)).attr('id'));
                            }
                            else {
                                hdn = $(grd.getCell(rowid, key)).attr('id');
                                $('#' + hdn).attr('value', '');
                                if (key == "Rate") {
                                    $('#' + hdn).change();
                                }
                            }
                        }
                        else {
                            hdn = $(grd.getCell(rowid, key)).attr('id');
                            $('#' + hdn).attr('checked', false);
                        }
                        //Changes Ends
                    });

                    $('#' + Id).attr('value', '');
                }
                else {

                    var extra = "?Key=" + $.EncryptString(txt);
                    if (extraGrdPars != null && extraGrdPars != undefined) {

                        jQuery.each(extraGrdPars, function (key, val) {

                            switch (val) {
                                case 'check':
                                    extra = extra.concat("&" + key + "=" + ($(grd.getCell(rowid, key)).attr('checked') == 'checked' ? true : false));
                                    break;
                                case 'text':
                                    extra = extra.concat("&" + key + "=" + document.getElementById($(grd.getCell(rowid, key)).attr('id')).value);
                                    break;
                                case 'select':
                                    extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                    break;
                                default:
                                    extra = extra.concat("&" + key + "=" + grd.getCell(rowid, key));
                                    break;
                            }
                        });
                    }
                    if (checkDuplicate != undefined && checkDuplicate) {
                        extra = extra + "&mode=" + (grd.getCell(rowid, primCol) == '' ? "add" : "edit") + "&primID=" + grd.getCell(rowid, primCol);
                    }

                    $.ajax({
                        url: url + extra + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                        async: false,
                        cached: false,
                        type: 'POST',
                        datatype: 'json',
                        success: function (data) {
                            if ($(data).attr(TxtMap) == '') {
                                jQuery.each(ColMap, function (key, val) {
                                    //Modified by Shashi for clearing dropdown
                                    if (key == "Dropdown") {
                                        $.ClearDropDown($(grd.getCell(rowid, val)).attr('id'));
                                    }
                                    else {
                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                        $('#' + hdn).attr('value', '');
                                        if (key == "Rate") {
                                            $('#' + hdn).change();
                                        }
                                    }
                                    //Changes Ends
                                });
                                $('#' + Id).attr('value', txt);
                                document.getElementById(Id).value = "";
                                alert($.getMessage('Invalid') + ' ' + friendlyColName);
                            }
                            else {
                                jQuery.each(ColMap, function (key, val) {
                                    //Modified by Shashi on 26-Feb-2014 for loading dropdown
                                    var hdn = "";
                                    if (key != "IsReWork") {
                                        if (key == "Dropdown") {
                                            $.ClearDropDown($(grd.getCell(rowid, val)).attr('id'));
                                            $('#' + $(grd.getCell(rowid, val)).attr('id')).LoadOptions(data.DropdownOptions, 'Name', 'ID');
                                        }
                                        else {
                                            hdn = $(grd.getCell(rowid, key)).attr('id');
                                            $('#' + hdn).attr('value', $(data).attr(val));
                                        }
                                    }
                                    else {
                                        hdn = $(grd.getCell(rowid, key)).attr('id');
                                        $('#' + hdn).attr('checked', $(data).attr(val));
                                    }
                                    //Changes Ends
                                    if (key == "FreeStock") {
                                        grd.setCell(rowid, 'PartNumber', '', '', { title: 'Free Stock :' + $(data).attr(val) });
                                    }
                                });

                                //if (events != undefined && events != null) {
                                //    jQuery.each(events, function (key, evt) {
                                //        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                //        $('#' + hdn).trigger(evt);
                                //    });
                                //}
                                $('#' + Id).attr('value', $(data).attr(TxtMap));

                                if (checkDuplicate != undefined && checkDuplicate) {
                                    var rowIDS = grd.jqGrid('getDataIDs');
                                    var txtValue = document.getElementById($(grd.getCell(rowid, DupCol)).attr('id')).value;
                                    var isexist = false;
                                    for (i = 0; i < rowIDS.length; i++) {
                                        var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');
                                        if (editmode != 'false' && rowIDS[i] != rowid) {
                                            var colValue = document.getElementById($(grd.getCell(rowIDS[i], DupCol)).attr('id')).value;
                                            if (txtValue == colValue) {
                                                isexist = true;
                                                $('#' + Id).attr('value', '');
                                                $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                jQuery.each(ColMap, function (key, val) {
                                                    //Modified by Shashi for clearing dropdown
                                                    if (key != "IsReWork") {
                                                        if (key == "Dropdown") {
                                                            $.ClearDropDown($(grd.getCell(rowid, val)).attr('id'));
                                                        }
                                                        else {
                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                            $('#' + hdn).attr('value', '');
                                                            if (key == "Rate") {
                                                                $('#' + hdn).change();
                                                            }
                                                        }
                                                        //Changes Ends
                                                    }
                                                    else {
                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                        $('#' + hdn).attr('checked', false);
                                                    }
                                                });

                                                alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                return;
                                            }
                                        }
                                    }

                                    if (data.IsExists && !isexist) {

                                        $('#' + Id).attr('value', '');
                                        $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                        jQuery.each(ColMap, function (key, val) {
                                            //Modified by Shashi for clearing dropdown
                                            if (key != "IsReWork") {
                                                if (key == "Dropdown") {
                                                    $.ClearDropDown($(grd.getCell(rowid, val)).attr('id'));
                                                }
                                                else {
                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                    $('#' + hdn).attr('value', '');
                                                    if (key == "Rate") {
                                                        $('#' + hdn).change();
                                                    }
                                                }
                                                //Changes Ends
                                            }
                                            else {
                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                $('#' + hdn).attr('checked', false);
                                            }
                                        });
                                        alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                    }
                                }
                                if ($(data).attr('IsActive') == false) {
                                    jQuery.each(ColMap, function (key, val) {
                                        //Modified by Shashi for clearing dropdown
                                        if (key == "Dropdown") {
                                            $.ClearDropDown($(grd.getCell(rowid, val)).attr('id'));
                                        }
                                        else {
                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                            $('#' + hdn).attr('value', '');
                                            if (key == "Rate") {
                                                $('#' + hdn).change();
                                            }
                                        }
                                        //Changes Ends
                                    });
                                    $('#' + Id).attr('value', txt);
                                    document.getElementById(Id).value = "";
                                    $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                    alert($.getMessage('Inactive') + ' ' + friendlyColName);
                                    return;
                                }

                                if (events != undefined && events != null) {
                                    jQuery.each(events, function (key, evt) {
                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                        $('#' + hdn).trigger(evt);
                                    });
                                }
                            }
                        },
                        error: function (e, d, f) {
                            alert("");
                        }
                    });
                }
            }
        }
    },
    {
        type: 'drop', fn: function () {
            return false;
        }
    }];
    jQuery.each(ColMap, function (key, val) {

        if (key != 'IsReWork') {
            grd.setColProp(key, { editable: true, edittype: 'text' });
        }
        else if (key == 'IsReWork') {
            grd.setColProp(key, { editable: true, edittype: 'checkbox' });
        }
    });

    this.fld = [{
        type: 'mousedown', fn: function (e) {

            IsLens = true;
            this.grd = grd;
            this.rowid = $(this).parent('td').parent('tr').attr('id');
            var rowid = this.rowid;

            this.txtBx = $(this.grd.getCell(this.rowid, TxtCol));
            this.Id = this.txtBx.attr('id');
            this.txtId = $(this.grd.getCell(this.rowid, IdField));
            this.txtId = this.txtId.attr('id');

            var extra = "";
            if (extraGrdPars != null && extraGrdPars != undefined) {
                jQuery.each(extraGrdPars, function (key, val) {
                    switch (val) {
                        case 'check':
                            extra = extra.concat("&" + key + "=" + ($(grd.getCell(rowid, key)).attr('checked') == 'checked' ? true : false));
                            break;
                        case 'text':
                            extra = extra.concat("&" + key + "=" + document.getElementById($(grd.getCell(rowid, key)).attr('id')).value);
                            break;
                        case 'select':
                            extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                            break;
                        default:
                            extra = extra.concat("&" + key + "=" + grd.getCell(rowid, key));
                            break;
                    }
                });
            }
            var extraParameters = extra + (Obj.pars != undefined ? Obj.pars.call(this) : '');
            $("#" + FldSrchID).FieldSearch("Search", { ResultTextField: this.Id, ResultValueField: this.txtId, headerNames: { id: headerid, Name: headername }, ExtraParam: extraParameters });
            IsLens = false;


        }
    }];
    grd.setColProp(TxtCol, { editable: true, editoptions: { dataEvents: this.evnts } });

    if (IsFieldSearch != undefined) {
        if (IsFieldSearch) {

            $("#" + FldSrchID).FieldSearch({ url: FldUrl, colModels: colModels, colName: colName, FieldSearchName: FieldSearchName, DefaultSortColName: DefaultSortColName });

            grd.setColProp(SrchCol, { editable: true, edittype: 'image', editoptions: { dataEvents: this.fld, src: $.url('/Content/SearchLens.jpg') } });
        }
    }
    else {
        grd.setColProp(SrchCol, { editable: true, edittype: 'image', editoptions: { src: $.url('/Content/SearchLens.jpg') } });
    }

}

//Coded By Muralidhara H M :-P
$.DefaultNumber = function (obj) {

    if (isNaN(obj)) {
        return 0;
    }
    else {
        return Number(obj);
    }
};

//Coded By Muralidhara H M :-P
//To Validate From and To Date
$.ValidateFromToDate = function (frmDate, toDate) {
    // var reg = new RegExp('([0-9]{2})-(\\w{3})-([0-9]{4})');
    var reg = new RegExp('([0-9]{2})-(\\w{3})-([0-9]{4})(\\s{1})?(([0-9]{2}):([0-9]{2}))?');
    var frm = reg.exec(frmDate);
    var To = reg.exec(toDate);

    var monthNamesShort = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];

    //var date1 = new Date(frmDate);
    var frmYear = frm[3];
    var frmMnth = (monthNamesShort.indexOf(frm[2].toUpperCase()));
    var frmDay = frm[1];
    // var date2 = new Date(toDate);
    var toYear = To[3];
    var toMnth = (monthNamesShort.indexOf(To[2].toUpperCase()));
    var toDay = To[1];
    if (toYear < frmYear) {
        return false;
    }
    else if (toYear == frmYear && toMnth < frmMnth) {
        return false;
    }
    else if (toYear == frmYear && frmMnth == toMnth && toDay < frmDay) {
        return false;
    }
    else {
        if (frm[5] != undefined && frm[5] != '' && frm[5] != null && To[5] != undefined && To[5] != '' && To[5] != null) {
            //var FrmhhmmReg = new RegExp('(([0-9]{2}):([0-9]{2}))');
            //var TohhmmReg = new RegExp('(([0-9]{2}):([0-9]{2}))');

            // if (hhmmReg.test(m[5])) {
            if (toYear == frmYear && frmMnth == toMnth && toDay == frmDay && To[6] < frm[6]) {
                return false;
            }
            else if (toYear == frmYear && frmMnth == toMnth && toDay == frmDay && frm[6] == To[6] && To[7] < frm[7]) {
                return false;
            }
            else {
                return true;
            }

            // }
            // else {
            isValid = false;
            // }
        }
        else {
            return true;
        }

    }
}

$.ValidateFromToDate1 = function (frmDate, toDate) {

    // var reg = new RegExp('([0-9]{2})-(\\w{3})-([0-9]{4})');
    var reg = new RegExp('([0-9]{2})-(\\w{3})-([0-9]{4})(\\s{1})?(([0-9]{2}):([0-9]{2}))?');
    var frm = reg.exec(frmDate);
    var To = reg.exec(toDate);

    //var monthNamesShort = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    var monthNamesShort = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];

    //var date1 = new Date(frmDate);
    var frmYear = frm[3];
    var frmMnth = (monthNamesShort.indexOf(frm[2].toUpperCase()));
    var frmDay = frm[1];
    // var date2 = new Date(toDate);
    var toYear = To[3];
    var toMnth = (monthNamesShort.indexOf(To[2].toUpperCase()));
    var toDay = To[1];
    if (toYear < frmYear) {
        return false;
    }
    else if (toYear == frmYear && toMnth < frmMnth) {
        return false;
    }
    else if (toYear == frmYear && frmMnth == toMnth && toDay < frmDay) {
        return false;
    }
    else {
        if (frm[5] != undefined && frm[5] != '' && frm[5] != null && To[5] != undefined && To[5] != '' && To[5] != null) {
            //var FrmhhmmReg = new RegExp('(([0-9]{2}):([0-9]{2}))');
            //var TohhmmReg = new RegExp('(([0-9]{2}):([0-9]{2}))');

            // if (hhmmReg.test(m[5])) {
            if (toYear == frmYear && frmMnth == toMnth && toDay == frmDay && To[6] < frm[6]) {
                return false;
            }
            else if (toYear == frmYear && frmMnth == toMnth && toDay == frmDay && frm[6] == To[6] && To[7] < frm[7]) {
                return false;
            }
            else {
                return true;
            }

            // }
            // else {
            isValid = false;
            // }
        }
        else {
            return true;
        }

    }
}

$.ValidateFromToDateTime1 = function (frmDate, toDate) {
    //var reg = new RegExp('([0-9]{2})/([0-9]{2})/([0-9]{4}) ([0-9]{2}):([0-9]{2})');
    var reg = new RegExp('([0-9]{2})-(\\w{3})-([0-9]{4})(\\s{1})(([0-9]{2}):([0-9]{2}))');
    var frm = reg.exec(frmDate);
    var To = reg.exec(toDate);
    var monthNamesShort = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];

    var frmYear = frm[3];
    var frmMnth = frm[2];
    var frmDay = frm[1];
    var frmHH = frm[6];
    var frmMM = frm[7];
    var toYear = To[3];
    var toMnth = To[2];
    var toDay = To[1];
    var toHH = To[6];
    var toMM = To[7];

    var FromMonth = 0;
    var ToMonth = 0;

    for (i = 0; i < monthNamesShort.length; i++) {
        if (frmMnth.toUpperCase() == monthNamesShort[i].toUpperCase()) {
            FromMonth = i;
        }
    }

    for (i = 0; i < monthNamesShort.length; i++) {
        if (toMnth.toUpperCase() == monthNamesShort[i].toUpperCase()) {
            ToMonth = i;
        }
    }

    if (toYear < frmYear) {
        return false;
    }
    else if (toYear == frmYear && ToMonth < FromMonth) {
        return false;
    }
    else if (toYear == frmYear && FromMonth == ToMonth && toDay < frmDay) {
        return false;
    }
    else if (toYear == frmYear && FromMonth == ToMonth && toDay == frmDay && toHH < frmHH) {
        return false;
    }
    else if (toYear == frmYear && FromMonth == ToMonth && toDay == frmDay && toMM < frmMM) {
        return false;
    }
    else {
        return true;
    }
}


//Coded By Muralidhara H M :-P
//To Validate From and To Date //With mm/dd/yyyy hh:mm format
$.ValidateFromToDateTime = function (frmDate, toDate) {

    var reg = new RegExp('([0-9]{2})/([0-9]{2})/([0-9]{4}) ([0-9]{2}):([0-9]{2})');
    var frm = reg.exec(frmDate);
    var To = reg.exec(toDate);


    var frmYear = frm[3];
    var frmMnth = frm[1];
    var frmDay = frm[2];
    var frmHH = frm[4];
    var frmMM = frm[5];
    var toYear = To[3];
    var toMnth = To[1];
    var toDay = To[2];
    var toHH = To[4];
    var toMM = To[5];

    if (toYear < frmYear) {
        return false;
    }
    else if (toYear == frmYear && toMnth < frmMnth) {
        return false;
    }
    else if (toYear == frmYear && frmMnth == toMnth && toDay < frmDay) {
        return false;
    }
    else if (toYear == frmYear && frmMnth == toMnth && toDay == frmDay && toHH < frmHH) {
        return false;
    }
    else if (toYear == frmYear && frmMnth == toMnth && toDay == frmDay && toMM < frmMM) {
        return false;
    }
    else {
        return true;
    }
}

//Coded By Muralidhara H M :-P
//To Check valid Date Format
$.IsValidDate = function (date) {
    // var reg = new RegExp('([0-9]{2})-(\\w{3})-([0-9]{4})');
    var reg = new RegExp('([0-9]{2})-(\\w{3})-([0-9]{4})(\\s{1})?(([0-9]{2}):([0-9]{2}))?');
    //var monthNamesShort = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    var monthNamesShort = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];

    if (reg.test(date)) {
        var isValid = true;
        var isValidMnth = false;
        var isValidHr = false;
        var isValidMn = false;
        var m = reg.exec(date);

        if (m[1] < 1 || m[1] > 31) {
            isValid = false;
        }

        if (m[3] < 1900) {
            isValid = false;
        }

        for (i = 0; i < monthNamesShort.length; i++) {
            if (m[2].toUpperCase() == monthNamesShort[i].toUpperCase()) {
                isValidMnth = true; break;
            }
        }

        if (m[5] != undefined && m[5] != '' && m[5] != null) {
            var hhmmReg = new RegExp('([0-9]{2})-(\\w{3})-([0-9]{4})(\\s{1})?(([0-9]{2}):([0-9]{2}\\|[^ \\S]))?');
            if (hhmmReg.test(date)) {
                if (m[6] < 0 || m[6] > 23) {
                    isValid = false;
                }
                if (m[7] < 0 || m[7] > 59) {
                    isValid = false;
                }
            }
            else {
                isValid = false;
            }
        }
        if (isValid && isValidMnth) {
            //Added by Shashi
            var dd = new Date(m[3], i + 1, 0); if (m[1] > dd.getDate()) return false; //T0 validate leap year
            //Ends
            return true;
        }
        else {
            return false;
        }
    }
    else {
        return false;
    }
}
//Coded By Muralidhara H M :-P
//With mm/dd/yyyy hh:mm format
$.IsValidDateTime = function (date) {
    var reg = new RegExp('([0-9]{2})-(\\w{3})-([0-9]{4})(\\s{1})(([0-9]{2}):([0-9]{2}))');
    var monthNamesShort = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
    var isValidMnth = false;
    if (reg.test(date)) {
        var isValid = true;
        var m = reg.exec(date);
        //if (m[1] < 1 || m[1] > 12) {
        //    isValid = false;
        //}
        if (m[1] < 1 || m[1] > 31) {
            isValid = false;
        }
        for (i = 0; i < monthNamesShort.length; i++) {
            if (m[2].toUpperCase() == monthNamesShort[i].toUpperCase()) {
                isValidMnth = true;
            }
        }

        if (m[6] < 0 || m[6] > 23) {
            isValid = false;
        }

        if (m[7] < 0 || m[7] > 59) {
            isValid = false;
        }

        if (isValid && isValidMnth) {
            return true;
        }
        else {
            return false;
        }
    }
    else {

        return false;
    }


}
//Coded By Muralidhara H M :-P
$.toStandardDate = function (date) {
    //var monthNamesShort = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    var monthNamesShort = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
    var thisdate = new Date(date);

    var Year = thisdate.getFullYear();
    var Month = monthNamesShort[thisdate.getMonth()];
    var Day = thisdate.getDate();
    Day = Day.toString().length == 1 ? ('0' + Day) : Day;
    return (Day + '-' + Month + '-' + Year);
}

//Coded By Muralidhara H M :-P
$.Print = function (obj) {

    var d = $('#' + obj.id).parent().parent().parent().parent();
    var s = $(d[0].outerHTML.replace(obj.id, 'printWindow'));

    var win = window.open();
    win.document.write("<html><head>");

    for (i = 0; i < document.styleSheets.length; i++) {
        win.document.write("<link href='" + document.styleSheets[i].href + "' rel='stylesheet' />");
    }
    win.document.write("</head>");
    win.document.write("<body  > " + s[0].outerHTML + " </body></html>");
    win.print();

}


//Coded By Muralidhara H M :-P
//------------------------------------------------------JQGrid Widgets----------------------------------------------------------------------------------
//Common Export Widget for All JQGrid
$.widget("ui.documentExport",
  {
      options:
      {
          url: null,
          div: null,
          width: null,
          height: null,
          parameters: null,
          grd: null,
      },
      _init: function () {
          this.options.width = this.options.width == undefined ? 'auto' : this.options.width;
          this.options.height = this.options.height == undefined ? 'auto' : this.options.height;
      },
      _create: function () {

          var main = this;
          this.grd = this.element;
          this.options.grd = this.grd;
          this.div = $("<div style='display:none'></div>");
          this.options.div = this.div;
          this.select = $("<select style='width:100px'> <option value='1'>" + $.getMessage('Excel') + "</option> <option value='2'>" + $.getMessage('PDF') + "</option></select>");
          this.btnExport = $("<button>" + $.getMessage('Export') + "</button>");
          var select = this.select;
          var url = this.options.url;

          this.btnExport.click(function () {
              this.parameters = main.getParameters();
              if (this.parameters != null && this.parameters != undefined) {
                  window.open(url + '?exprtType=' + select.val() + this.parameters);
              }
              else {
                  window.open(url + '?exprtType=' + select.val());
              }
          });
          this.div.append(this.select);
          this.div.append(this.btnExport);
          this.grd.append(this.div);
      },
      show: function () {

          if ($(this.options.grd).jqGrid('getDataIDs').length > 0) {

              this.options.div.dialog({ modal: true, position: 'center', width: this.options.width, height: this.options.height, title: $.getMessage('DocumentExport') });
          }
          else {
              // alert($.getMessage('DocumentExport'));
          }
      },
      setParameters: function (par) {

          this.options.parameters = par;
      },
      getParameters: function () {
          return this.options.parameters;
      }
  });

//Coded By Muralidhara H M :-P
//Common Advance Search Widget for All JQGrid
$.widget("ui.advanceSearch", {
    options:
    {
        grd: null,
        AdvDiv: null,
        width: null,
        height: null,
        parameters: null,
    },
    _init: function () {

        this.options.width = this.options.width == undefined ? 'auto' : this.options.width;
        this.options.height = this.options.height == undefined ? 'auto' : this.options.height;
    },
    _create: function () {

        var main = this;
        this.grd = this.element;
        this.options.grd = this.grd;

        var defaultOption = "<option value='-1'>--" + $.getMessage('Select') + "--</option>";
        this.colModel = this.grd.getGridParam("colModel");
        this.AdvDiv = $("<div  style='display:none;' ></div>");
        this.options.AdvDiv = this.AdvDiv;
        this.grd.append(this.AdvDiv);
        this.AdvnMsg = $("<div style=' background-color:rosybrown'></div>");
        this.AdvDiv.append(this.AdvnMsg);
        this.table = $("<table></table>");
        this.AdvDiv.append(this.table);
        this.ddlAdvnCols = $("<select>" + defaultOption + "</select>");


        //Operators---------------
        this.ddlAdvnOpts = $("<select></select>");
        var ddlAdvnOpts = this.ddlAdvnOpts;
        ddlAdvnOpts.html(defaultOption);

        this.ddlAdvnCols.change(function () {

            this.ddlAdnv = $(this);
            if (this.ddlAdnv.val() != '-1') {
                this.isIntgr = $(this.ddlAdnv[0].options[this.ddlAdnv[0].selectedIndex]).attr('IsInteger');
                ddlAdvnOpts.html('');
                ddlAdvnOpts.append(defaultOption);
                if (this.isIntgr == 'true') {

                    ddlAdvnOpts.append("<option value=' = '>equal</option>");
                    ddlAdvnOpts.append("<option value=' != '>not equal</option>");
                    ddlAdvnOpts.append("<option value=' like '>like</option>");
                    ddlAdvnOpts.append("<option value=' < '>less than</option>");
                    ddlAdvnOpts.append("<option value=' > '>greater than</option>");

                }
                else {
                    ddlAdvnOpts.append("<option value=' = '>equal</option>");
                    ddlAdvnOpts.append("<option value=' != '>not equal</option>");
                    ddlAdvnOpts.append("<option value=' like '>like</option>");
                }

            }
            else {
                ddlAdvnOpts.html(defaultOption);
            }

        });

        //--------------------------
        this.txtAdvnQry = $("<textarea  cols='80' rows='4' class='ReadOnly' ></textarea>");
        this.txtAdvnValue = $("<input  type='text' style='width: 200px' />");
        this.ddlAdvnCond = $("<select><option value='-1'>--" + $.getMessage('Select') + "--</option><option value='AND'>AND</option><option value='OR'>OR</option></select>");
        this.btnAddFilter = $("<button >Add Filter</button>");
        this.btnRmveFilter = $("<button >Remove Filter</button>");

        this.tr = $('<tr></tr>');
        //Column
        this.td = $('<td>' + $.getMessage('SelectColumn') + ':</td>');
        this.tr.append(this.td);
        this.td = $('<td></td>');
        this.td.append(this.ddlAdvnCols);
        this.tr.append(this.td);

        //Operator
        this.td = $('<td>' + $.getMessage('SelectOperator') + ':</td>');
        this.tr.append(this.td);
        this.td = $('<td></td>');
        this.td.append(this.ddlAdvnOpts);
        this.tr.append(this.td);
        this.table.append(this.tr);
        //---------------------------------------------------------
        this.tr = $('<tr></tr>');
        //Value
        this.td = $('<td' + $.getMessage('Value') + ':</td>');
        this.tr.append(this.td);
        this.td = $('<td></td>');
        this.td.append(this.txtAdvnValue);
        this.tr.append(this.td);

        //Condition
        this.td = $('<td>' + $.getMessage('SelectCondition') + ':</td>');
        this.tr.append(this.td);
        this.td = $('<td></td>');
        this.td.append(this.ddlAdvnCond);
        this.tr.append(this.td);
        this.table.append(this.tr);
        //---------------------------------------------------------
        this.tr = $('<tr></tr>');
        //Add Filter
        this.td = $('<td></td>');
        this.td.append(this.btnAddFilter);
        this.tr.append(this.td);

        //Remove Filter
        this.td = $('<td></td>');
        this.td.append(this.btnRmveFilter);
        this.tr.append(this.td);
        this.table.append(this.tr);

        //---------------------------------------------------------
        this.tr = $('<tr></tr>');
        //Query
        this.td = $("<td colspan='4'></td>");
        this.td.append(this.txtAdvnQry);
        this.tr.append(this.td);
        this.table.append(this.tr);



        var ddlAdvnCols = this.ddlAdvnCols;
        var ddlAdvnOpts = this.ddlAdvnOpts;
        var AdvnMsg = this.AdvnMsg;
        var txtAdvnValue = this.txtAdvnValue;
        var txtAdvnQry = this.txtAdvnQry;
        var grd = this.grd;
        var ddlAdvnCond = this.ddlAdvnCond;


        this.advnName = '';
        this.IsInteger = false;
        this.IsNumber = false;
        this.IsMandatory = false;
        this.option = $("");
        this.AdvnMsg.hide();

        for (col = 0; col < this.colModel.length; col++) {
            if (this.colModel[col].hasOwnProperty('advanceSearch')) {

                if (this.colModel[col].advanceSearch) {

                    if (this.colModel[col].hasOwnProperty('advncOptions')) {
                        this.advnName = this.colModel[col].advncOptions.hasOwnProperty('name') == true ? this.colModel[col].advncOptions.name : this.colModel[col].name;
                        this.IsInteger = this.colModel[col].advncOptions.hasOwnProperty('IsInteger') == true ? this.colModel[col].advncOptions.IsInteger : false;
                        this.IsNumber = this.colModel[col].advncOptions.hasOwnProperty('IsNumber') == true ? this.colModel[col].advncOptions.IsNumber : false;
                        this.IsMandatory = this.colModel[col].advncOptions.hasOwnProperty('IsMandatory') == true ? this.colModel[col].advncOptions.IsMandatory : false;
                        this.option = $("<option value='" + this.colModel[col].name + "'  >" + this.advnName + "</option>");
                        this.option.attr('IsMandatory', this.IsMandatory);
                        this.option.attr('IsInteger', this.IsInteger);
                        this.option.attr('IsNumber', this.IsNumber);
                        this.ddlAdvnCols.append(this.option);
                    }
                }
            }
        }

        this.whereCond = '';

        var parameters = this.options.parameters;
        this.btnAddFilter.click(function () {

            this.table = '';
            this.txtareatxt = '';
            this.isMdtory = $(ddlAdvnCols[0].options[ddlAdvnCols[0].selectedIndex]).attr('IsMandatory');
            this.isIntgr = $(ddlAdvnCols[0].options[ddlAdvnCols[0].selectedIndex]).attr('IsInteger');
            this.isnum = $(ddlAdvnCols[0].options[ddlAdvnCols[0].selectedIndex]).attr('IsNumber');

            AdvnMsg.html('');
            AdvnMsg.hide();
            if (ddlAdvnCols.val() == '-1') {
                AdvnMsg.html($.getMessage('PleaseselectaColumn'));
                AdvnMsg.show();
                return;
            }
            else if (ddlAdvnOpts.val() == '-1') {
                AdvnMsg.html($.getMessage('PleaseselectaOperator'));
                AdvnMsg.show();
                return;
            }
            else if (this.isMdtory == 'true' && txtAdvnValue.val() == '') {
                AdvnMsg.html($.getMessage('ValueisMandatoryforselectedColumn'));
                AdvnMsg.show();
                return;
            }
            else if ((this.isIntgr == 'true' && txtAdvnValue.val() != '') && isNaN(txtAdvnValue.val())) {
                AdvnMsg.html($.getMessage('PleaseenterIntegerValue'));
                AdvnMsg.show();
                return;
            }
            else {

                this.colTxt = ddlAdvnCols[0].options[ddlAdvnCols[0].selectedIndex].text;
                this.oper = ddlAdvnOpts.val();
                this.txtAdvVal = txtAdvnValue.val();
                this.advVal = $.EncryptString(this.oper == ' like ' ? '%' + this.txtAdvVal + '%' : this.txtAdvVal);
                if (txtAdvnQry.val() == '') {

                    //this.txtareatxt = 'select * from ' + this.table + ' where ' + this.colTxt + ' ' + ddlAdvnOpts.val() + ' ' + this.txtAdvVal;
                    this.txtareatxt = this.colTxt + ' ' + ddlAdvnOpts.val() + ' ' + this.txtAdvVal;
                    whereCond = ddlAdvnCols.val() + ddlAdvnOpts.val() + '\'' + this.advVal + '\'';
                }
                else {
                    if (ddlAdvnCond.val() == '-1') {
                        AdvnMsg.html($.getMessage('PleaseselectaCondition'));
                        AdvnMsg.show();
                        return;
                    }
                    else {
                        this.txtareatxt = txtAdvnQry.val() + ' ' + ddlAdvnCond.val() + ' ' + this.colTxt + ' ' + ddlAdvnOpts.val() + ' ' + this.txtAdvVal;
                        whereCond = whereCond + ' ' + ddlAdvnCond.val() + ' ' + ddlAdvnCols.val() + ddlAdvnOpts.val() + '\'' + this.advVal + '\'';
                    }
                }
                txtAdvnQry.attr('value', this.txtareatxt);



                this.crntUrl = main.getgrdUrl();
                this.crntCond = main.getgrdUrlCond();
                this.nxturl = '';


                if (this.crntUrl.indexOf('advnce=true') == '-1' && this.crntUrl.indexOf('advnce=false') == '-1') {
                    grd.setGridParam({ url: this.crntUrl + this.crntCond + 'advnce=true&Query=' + whereCond });
                    this.nxturl = this.crntUrl + this.crntCond + 'advnce=true&Query=' + whereCond;
                }
                else {
                    grd.setGridParam({ url: this.crntUrl.replace('advnce=false', 'advnce=true') + '&Query=' + whereCond });
                    this.nxturl = this.crntUrl.replace('advnce=false', 'advnce=true') + '&Query=' + whereCond
                }
                grd.trigger('reloadGrid');

                grd.setGridParam({ url: this.nxturl.replace('advnce=true', 'advnce=false').replace('&Query=' + whereCond, '') });



            }
        });
        this.btnRmveFilter.click(
            function () {

                txtAdvnQry.attr('value', '');
                ddlAdvnOpts.attr('value', '-1');
                ddlAdvnCols.attr('value', '-1');
                txtAdvnValue.attr('value', '');
                ddlAdvnCond.attr('value', '-1');
                grd.trigger('reloadGrid');
            });
    },
    show: function () {

        this.options.AdvDiv.dialog({ modal: true, position: 'center', width: this.options.width, height: this.options.height, title: 'Advance Search' });
    },
    getgrdUrl: function () {
        return this.options.grd.getGridParam('url');
    },
    getgrdUrlCond: function () {

        this.grdUrl = this.options.grd.getGridParam('url');
        this.HasUrlPars = false;
        this.urlCond = '';
        if (this.grdUrl.indexOf("?") != '-1') {
            this.HasUrlPars = true;
            this.urlCond = '&';
        }
        else {
            this.urlCond = '?';
        }
        return this.urlCond;
    },
});

//Coded By Muralidhara H M :-P
//Common Advance Search Widget for All JQGrid
$.widget("ui.advanceSrch", {
    options:
    {
        grd: null,
        AdvDiv: null,
        width: null,
        height: null,
        parameters: null,
    },
    _init: function () {

        this.options.width = this.options.width == undefined ? 'auto' : this.options.width;
        this.options.height = this.options.height == undefined ? 'auto' : this.options.height;
    },
    _create: function () {

        var main = this;
        this.grd = this.element;
        this.options.grd = this.grd;

        var defaultOption = "<option value='-1'>--" + $.getMessage('Select') + "--</option>";
        this.colModel = this.grd.getGridParam("colModel");
        this.AdvDiv = $("<div  style='display:none' ></div>");
        this.options.AdvDiv = this.AdvDiv;
        this.grd.append(this.AdvDiv);
        this.AdvnMsg = $("<div style=' background-color:rosybrown'></div>");
        this.AdvDiv.append(this.AdvnMsg);
        this.table = $("<table  ></table>");
        this.AdvDiv.append(this.table);
        this.ddlAdvnCols = $("<select>" + defaultOption + "</select>");


        //Operators---------------
        this.ddlAdvnOpts = $("<select></select>");
        var ddlAdvnOpts = this.ddlAdvnOpts;
        ddlAdvnOpts.html(defaultOption);

        this.ddlAdvnCols.change(function () {

            this.ddlAdnv = $(this);
            if (this.ddlAdnv.val() != '-1') {
                this.isIntgr = $(this.ddlAdnv[0].options[this.ddlAdnv[0].selectedIndex]).attr('IsInteger');
                ddlAdvnOpts.html('');
                ddlAdvnOpts.append(defaultOption);
                if (this.isIntgr == 'true') {

                    ddlAdvnOpts.append("<option value='Equal'>" + $.getMessage('Equal') + "</option>");
                    ddlAdvnOpts.append("<option value='NotEqual'>" + $.getMessage('NotEqual') + "</option>");
                    ddlAdvnOpts.append("<option value='Like'>" + $.getMessage('Like') + "</option>");
                    ddlAdvnOpts.append("<option value='LessThan'>" + $.getMessage('LessThan') + "</option>");
                    ddlAdvnOpts.append("<option value='GreaterThan'>" + $.getMessage('GreaterThan') + "</option>");

                }
                else {
                    ddlAdvnOpts.append("<option value='Equal'>" + $.getMessage('Equal') + "</option>");
                    ddlAdvnOpts.append("<option value='NotEqual'>" + $.getMessage('NotEqual') + "</option>");
                    ddlAdvnOpts.append("<option value='Like'>" + $.getMessage('Like') + "</option>");
                }

            }
            else {
                ddlAdvnOpts.html(defaultOption);
            }

        });

        //--------------------------
        this.txtAdvnQry = $("<textarea  cols='80' rows='4'></textarea>");
        this.txtAdvnValue = $("<input  type='text' style='width: 200px' />");
        this.ddlAdvnCond = $("<select><option value='-1'>--" + $.getMessage('Select') + "--</option><option value='AND'>" + $.getMessage('AND') + "</option><option value='OR'>" + $.getMessage('OR') + "</option></select>");
        this.btnAddFilter = $("<button >" + $.getMessage('AddFilter') + "</button>");
        this.btnRmveFilter = $("<button >" + $.getMessage('RemoveFilter') + "</button>");

        this.tr = $('<tr></tr>');
        //Column
        this.td = $('<td>' + $.getMessage('SelectColumn') + ':</td>');
        this.tr.append(this.td);
        this.td = $('<td></td>');
        this.td.append(this.ddlAdvnCols);
        this.tr.append(this.td);

        //Operator
        this.td = $('<td>' + $.getMessage('SelectOperator') + ':</td>');
        this.tr.append(this.td);
        this.td = $('<td></td>');
        this.td.append(this.ddlAdvnOpts);
        this.tr.append(this.td);
        this.table.append(this.tr);
        //---------------------------------------------------------
        this.tr = $('<tr></tr>');
        //Value
        this.td = $('<td>' + $.getMessage('Value') + ':</td>');
        this.tr.append(this.td);
        this.td = $('<td></td>');
        this.td.append(this.txtAdvnValue);
        this.tr.append(this.td);

        //Condition
        this.td = $('<td>' + $.getMessage('SelectCondition') + ':</td>');
        this.tr.append(this.td);
        this.td = $('<td></td>');
        this.td.append(this.ddlAdvnCond);
        this.tr.append(this.td);
        this.table.append(this.tr);
        //---------------------------------------------------------
        this.tr = $('<tr></tr>');
        //Add Filter
        this.td = $('<td></td>');
        this.td.append(this.btnAddFilter);
        this.tr.append(this.td);

        //Remove Filter
        this.td = $('<td></td>');
        this.td.append(this.btnRmveFilter);
        this.tr.append(this.td);
        this.table.append(this.tr);

        //---------------------------------------------------------
        this.tr = $('<tr></tr>');
        //Query
        this.td = $("<td colspan='4'></td>");
        this.td.append(this.txtAdvnQry);
        this.tr.append(this.td);
        this.table.append(this.tr);



        var ddlAdvnCols = this.ddlAdvnCols;
        var ddlAdvnOpts = this.ddlAdvnOpts;
        var AdvnMsg = this.AdvnMsg;
        var txtAdvnValue = this.txtAdvnValue;
        var txtAdvnQry = this.txtAdvnQry;
        var grd = this.grd;
        var ddlAdvnCond = this.ddlAdvnCond;


        this.advnName = '';
        this.IsInteger = false;
        this.IsNumber = false;
        this.IsMandatory = false;
        this.option = $("");
        this.AdvnMsg.hide();

        for (col = 0; col < this.colModel.length; col++) {
            if (this.colModel[col].hasOwnProperty('advanceSearch')) {

                if (this.colModel[col].advanceSearch) {

                    if (this.colModel[col].hasOwnProperty('advncOptions')) {
                        this.advnName = this.colModel[col].advncOptions.hasOwnProperty('name') == true ? this.colModel[col].advncOptions.name : this.colModel[col].name;
                        this.IsInteger = this.colModel[col].advncOptions.hasOwnProperty('IsInteger') == true ? this.colModel[col].advncOptions.IsInteger : false;
                        this.IsNumber = this.colModel[col].advncOptions.hasOwnProperty('IsNumber') == true ? this.colModel[col].advncOptions.IsNumber : false;
                        this.IsMandatory = this.colModel[col].advncOptions.hasOwnProperty('IsMandatory') == true ? this.colModel[col].advncOptions.IsMandatory : false;
                        this.option = $("<option value='" + this.colModel[col].name + "'  >" + this.advnName + "</option>");
                        this.option.attr('IsMandatory', this.IsMandatory);
                        this.option.attr('IsInteger', this.IsInteger);
                        this.option.attr('IsNumber', this.IsNumber);
                        this.ddlAdvnCols.append(this.option);
                    }
                }
            }
        }

        this.whereCond = '{';
        this.rules = "";
        var rules = this.rules;
        var parameters = this.options.parameters;
        this.btnAddFilter.click(function () {

            this.table = '';
            this.txtareatxt = '';
            this.isMdtory = $(ddlAdvnCols[0].options[ddlAdvnCols[0].selectedIndex]).attr('IsMandatory');
            this.isIntgr = $(ddlAdvnCols[0].options[ddlAdvnCols[0].selectedIndex]).attr('IsInteger');
            this.isnum = $(ddlAdvnCols[0].options[ddlAdvnCols[0].selectedIndex]).attr('IsNumber');


            AdvnMsg.html('');
            AdvnMsg.hide();
            if (ddlAdvnCols.val() == '-1') {
                AdvnMsg.html($.getMessage('PleaseselectaColumn'));
                AdvnMsg.show();
                return;
            }
            else if (ddlAdvnOpts.val() == '-1') {
                AdvnMsg.html($.getMessage('PleaseselectaOperator'));
                AdvnMsg.show();
                return;
            }
            else if (this.isMdtory == 'true' && txtAdvnValue.val() == '') {
                AdvnMsg.html($.getMessage('ValueisMandatoryforselectedColumn'));
                AdvnMsg.show();
                return;
            }
            else if ((this.isIntgr == 'true' && txtAdvnValue.val() != '') && isNaN(txtAdvnValue.val())) {
                AdvnMsg.html($.getMessage('PleaseenterIntegerValue'));
                AdvnMsg.show();
                return;
            }
            else {

                this.colTxt = ddlAdvnCols[0].options[ddlAdvnCols[0].selectedIndex].text;
                this.oper = ddlAdvnOpts.val();
                this.txtAdvVal = txtAdvnValue.val();
                this.advVal = escape(this.oper == ' like ' ? '%' + this.txtAdvVal + '%' : this.txtAdvVal);
                if (txtAdvnQry.val() == '') {

                    //this.txtareatxt = $.getMessage('Select') + ' * ' + $.getMessage('From') + ' ' + $.getMessage('Where') + ' ' + this.colTxt + ' ' + $.getMessage(ddlAdvnOpts.val()) + ' ' + this.txtAdvVal;
                    this.txtareatxt = this.colTxt + ' ' + $.getMessage(ddlAdvnOpts.val()) + ' ' + this.txtAdvVal;
                    rules = '{Field:\'' + ddlAdvnCols.val() + '\',Operator:\'' + ddlAdvnOpts.val() + '\',Data:\'' + $.EncryptString(this.advVal) + '\',Condition:\'\'}';
                }
                else {
                    if (ddlAdvnCond.val() == '-1') {
                        AdvnMsg.html($.getMessage('PleaseselectaCondition'));
                        AdvnMsg.show();
                        return;
                    }
                    else {
                        this.txtareatxt = txtAdvnQry.val() + ' ' + ddlAdvnCond.val() + ' ' + this.colTxt + ' ' + $.getMessage(ddlAdvnOpts.val()) + ' ' + this.txtAdvVal;
                        rules = rules + ',' + '{Field:\'' + ddlAdvnCols.val() + '\',Operator:\'' + ddlAdvnOpts.val() + '\',Data:\'' + $.EncryptString(this.advVal) + '\',Condition:\'' + ddlAdvnCond.val() + '\'}';
                    }
                }
                txtAdvnQry.attr('value', this.txtareatxt);



                this.crntUrl = main.getgrdUrl();
                this.crntCond = main.getgrdUrlCond();
                this.nxturl = '';

                var filters = "{Rules:[";
                filters = filters.concat(rules);
                filters = filters.concat("]}");
                if (this.crntUrl.indexOf('advnce=true') == '-1' && this.crntUrl.indexOf('advnce=false') == '-1') {
                    grd.setGridParam({ url: this.crntUrl + this.crntCond + 'advnce=true&Query=' + filters });
                    this.nxturl = this.crntUrl + this.crntCond + 'advnce=true&Query=' + filters;
                }
                else {
                    grd.setGridParam({ url: this.crntUrl.replace('advnce=false', 'advnce=true') + '&Query=' + filters });
                    this.nxturl = this.crntUrl.replace('advnce=false', 'advnce=true') + '&Query=' + filters
                }
                grd.trigger('reloadGrid');

                grd.setGridParam({ url: this.nxturl.replace('advnce=true', 'advnce=false').replace('&Query=' + filters, '') });



            }
        });

        this.btnRmveFilter.click(
            function () {

                txtAdvnQry.attr('value', '');
                ddlAdvnOpts.attr('value', '-1');
                ddlAdvnCols.attr('value', '-1');
                txtAdvnValue.attr('value', '');
                ddlAdvnCond.attr('value', '-1');
                grd.trigger('reloadGrid');
            });

    },
    show: function () {

        this.options.AdvDiv.dialog({ modal: true, position: 'center', width: this.options.width, height: this.options.height, title: $.getMessage('AdvanceSearch') });
    },
    getgrdUrl: function () {
        return this.options.grd.getGridParam('url');
    },
    getgrdUrlCond: function () {

        this.grdUrl = this.options.grd.getGridParam('url');
        this.HasUrlPars = false;
        this.urlCond = '';
        if (this.grdUrl.indexOf("?") != '-1') {
            this.HasUrlPars = true;
            this.urlCond = '&';
        }
        else {
            this.urlCond = '?';
        }
        return this.urlCond;
    },

});

//to take a confirmation before closing the dialog box
//mode = 1 to close the dialog
//mode = 2 to close the dialog when opened through popup windows
$.ConfirmCancel = function (divID, mode) {
    if (confirm('Are you sure you want to cancel?')) {
        if (mode == 1) {
            $('#' + divID).dialog('close');
        }
        else {
            window.close();
        }
        return true;
    }
    else {
        return false;
    }
}

$.EncryptString = function (str) {
    //str = str!="&#"
    if (str.indexOf("&#") != -1) {
        str = str.replace("&#", "%lthash%")
    }

    var index = str.indexOf("+"); //Added for QA Correction as on 10-Oct-2014 for Sales_PL_Unit_0452 by Ashok Kumar M

    while (index != -1) {

        str = str.replace("+", "%Plus%");

        index = str.indexOf("+");

    }
    return escape(encodeURI(str));
};

$.DecryptString = function (str) {
    //str = str!="&#"
    if (str.indexOf("%lthash%") != -1) {
        str = str.replace("%lthash%", "&#")
    }
    //return escape(encodeURI(str));
    return decodeURI(unescape(str));
};


//Used For Prefix and Supersession Details Display During Part Search
$.fn.GridInlinePartSearch = function (Obj) {
    var grd = $(this);
    var TxtCol = Obj.TxtCol;
    var TxtMap = Obj.TxtMap;
    var SrchCol = Obj.SrchCol;
    var url = Obj.url;
    var urlPrefix = Obj.urlPrefix;
    var urlSupersession = Obj.urlSupersession;
    var ColMap = Obj.ColMap;
    var extraGrdPars = Obj.extraGrdPars;
    var IsFieldSearch = Obj.IsFieldSearch;
    var IdField = undefined;
    var FldUrl = undefined;
    var FldSrchID = undefined;
    var SupersessionID = undefined;
    var PrefixID = undefined;
    var headerid = undefined;
    var headername = undefined;
    var events = Obj.events;
    var colModels = [];
    var colName = [];
    var FieldSearchName = undefined;
    var DefaultSortColName = undefined;

    if (IsFieldSearch != undefined && Obj.FieldSearch != undefined) {
        if (IsFieldSearch) {
            IdField = Obj.FieldSearch.IdField;
            FldUrl = Obj.FieldSearch.url;
            FldSrchID = Obj.FieldSearch.FldSrchID;
            SupersessionID = Obj.FieldSearch.SupersessionID;
            PrefixID = Obj.FieldSearch.PrefixID;
            headerid = Obj.FieldSearch.headerid == undefined ? '' : Obj.FieldSearch.headerid;
            headername = Obj.FieldSearch.headername == undefined ? '' : Obj.FieldSearch.headername;
            colModels = Obj.FieldSearch.colModels == undefined ? '' : Obj.FieldSearch.colModels;
            colName = Obj.FieldSearch.colName == undefined ? '' : Obj.FieldSearch.colName;
            FieldSearchName = Obj.FieldSearch.FieldSearchName == undefined ? '' : Obj.FieldSearch.FieldSearchName;
            DefaultSortColName = Obj.FieldSearch.DefaultSortColName == undefined ? '' : Obj.FieldSearch.DefaultSortColName;

        }
    }
    var checkDuplicate = Obj.checkDuplicate;
    var DupCol = '';
    var friendlyColName = '';
    var primCol = '';
    if (checkDuplicate) {
        DupCol = Obj.DupCol.ColName;
        friendlyColName = Obj.DupCol.friendlyColName;
        primCol = Obj.DupCol.primCol;
    }

    this.evnts = [{
        type: 'change', fn: function (e) {
            if (!IsLens) {                
                this.grd = grd;
                this.rowid = $(this).parent('td').parent('tr').attr('id');
                var rowid = this.rowid;

                this.txtBx = $(this.grd.getCell(this.rowid, TxtCol));
                this.Id = this.txtBx.attr('id');
                var Id = this.Id;

                this.txt = document.getElementById(this.Id).value;
                this.value = '';
                var txt = this.txt;
                $('#' + Id).attr('duplicate', 'false');
                $('#' + Id).attr('exists', 'false');
                if (this.txt == '') {

                    jQuery.each(ColMap, function (key, val) {
                        if (key != "IsReWork") {
                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                            $('#' + hdn).attr('value', '');
                            if (key == "Rate") {
                                $('#' + hdn).change();
                            }
                        }
                        else {
                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                            $('#' + hdn).attr('checked', $(data).attr(val));
                        }
                    });

                    $('#' + Id).attr('value', '');
                }
                else {
                    $.LoadSupersessionDetails = function (val, val1) {

                        var Url = urlSupersession + '?Parts_ID=' + val + '&PartNumber=' + val1 + (Obj.pars != undefined ? Obj.pars.call(this) : '');
                        $.ajax({
                            async: false,
                            cached: false,
                            url: Url,
                            type: 'POST',
                            datatype: 'json',
                            success: function (SupersessionData) {                                
                                if (SupersessionData.IsSupersession == true) {

                                    var PreccedingPartDetail = '';
                                    var CurrentPartDetail = '';
                                    var NewPartDetail = '';

                                    $('#' + SupersessionID)[0].innerHTML = '';
                                    var Header = ' <span class="button b-close"><span>X</span></span><table  style="width:100%;text-align: center;" class="ui-widget-header"><tr><td>' + (SupersessionData.SupersessionType == null ? '' : SupersessionData.SupersessionType) + '</td></tr></table>';
                                    $('#' + SupersessionID).append(Header);

                                    var spanSuperseesionYes = '<span style="color:red;">*</span>';
                                    var spanSuperseesionNo = '<span>&nbsp;</span>';
                                    //Modified by: Kiran Date:5-12-2013 QA-Iteration-Missed out scenarios Changes Begin
                                    var spanSuperseesionInfo = '<span style="color:red;"><img style=height:20px;width:20px; src=' + $.url('/images/Supersession.jpg') + '> </span><span>' + $.getMessage('supersessionexist') + '</span>';
                                    //Changes Ends
                                    var Detail = '<table  style="width:100%;"><tr><td style="vertical-align:top;">';
                                    if (SupersessionData.PreccedingPartDetail.length > 0) {

                                        PreccedingPartDetail = '<table cellpadding="3px" cellspacing="0px" style="border:1px solid lightgrey;">';
                                        PreccedingPartDetail = PreccedingPartDetail + '<tr><td colspan="5" style="width:100%;text-align: center;" class="ui-widget-header">' + $.getMessage('PrecedingPartDetails') + '</td></tr>';
                                        PreccedingPartDetail = PreccedingPartDetail + '<tr><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;">' + '' + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + $.getMessage('Prefix') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('PartNumber') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('Description') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:5px;">' + '' + '</th></tr>';
                                        for (var i = 0; i < SupersessionData.PreccedingPartDetail.length; i++) {

                                            PreccedingPartDetail = PreccedingPartDetail + '<tr><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;text-align:center;">' + SupersessionData.PreccedingPartDetail[i].Select + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + SupersessionData.PreccedingPartDetail[i].PartPrefix + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + SupersessionData.PreccedingPartDetail[i].PartsNumber + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + (SupersessionData.PreccedingPartDetail[i].PartsDescription == null ? '' : SupersessionData.PreccedingPartDetail[i].PartsDescription) + '</td><td style="border:1px solid lightgrey;text-align:center;padding:3px 3px 3px 3px;width:5px;">' + SupersessionData.PreccedingPartDetail[i].IsSuperseeded + '</td></tr>';
                                        }
                                        PreccedingPartDetail = PreccedingPartDetail + '</table>'

                                    }
                                    Detail = Detail + PreccedingPartDetail;
                                    Detail = Detail + '</td><td style="vertical-align:top;">'

                                    if (SupersessionData.CurrentPartDetail.length > 0) {

                                        CurrentPartDetail = '<table cellpadding="3" cellspacing="0" style="border:1px solid lightgrey;">';
                                        CurrentPartDetail = CurrentPartDetail + '<tr><td colspan="5" style="width:100%;text-align: center;" class="ui-widget-header">' + $.getMessage('CurrentPartDetails') + '</td></tr>'
                                        CurrentPartDetail = CurrentPartDetail + '<tr ><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;">' + '' + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + $.getMessage('Prefix') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('PartNumber') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('Description') + '</th></tr>'
                                        for (var i = 0; i < SupersessionData.CurrentPartDetail.length; i++) {

                                            CurrentPartDetail = CurrentPartDetail + '<tr><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;text-align:center;width:25px;">' + SupersessionData.CurrentPartDetail[i].Select + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + SupersessionData.CurrentPartDetail[i].PartPrefix + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + SupersessionData.CurrentPartDetail[i].PartsNumber + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + (SupersessionData.CurrentPartDetail[i].PartsDescription == null ? '' : SupersessionData.CurrentPartDetail[i].PartsDescription) + '</td></tr>'
                                        }
                                        CurrentPartDetail = CurrentPartDetail + '</table>'
                                    }
                                    Detail = Detail + CurrentPartDetail;
                                    Detail = Detail + '</td><td style="vertical-align:top;">'

                                    if (SupersessionData.NewPartDetail.length > 0) {

                                        NewPartDetail = '<table cellpadding="3px" cellspacing="0px" style="border:1px solid lightgrey;">';
                                        NewPartDetail = NewPartDetail + '<tr><td colspan="5" style="width:100%;text-align: center;" class="ui-widget-header">' + $.getMessage('NewPartDetails') + '</td></tr>';
                                        NewPartDetail = NewPartDetail + '<tr><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;">' + '' + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + $.getMessage('Prefix') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('PartNumber') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('Description') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:5px;">' + '' + '</th></tr>';
                                        for (var i = 0; i < SupersessionData.NewPartDetail.length; i++) {

                                            NewPartDetail = NewPartDetail + '<tr><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;text-align:center;">' + SupersessionData.NewPartDetail[i].Select + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + SupersessionData.NewPartDetail[i].PartPrefix + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + SupersessionData.NewPartDetail[i].PartsNumber + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + (SupersessionData.NewPartDetail[i].PartsDescription == null ? '' : SupersessionData.NewPartDetail[i].PartsDescription) + '</td><td style="border:1px solid lightgrey;text-align:center;padding:3px 3px 3px 3px;width:5px;">' + SupersessionData.NewPartDetail[i].IsSuperseeded + '</td></tr>';
                                        }
                                        NewPartDetail = NewPartDetail + '</table>'
                                    }

                                    Detail = Detail + NewPartDetail;
                                    Detail = Detail + '</td></tr></table>'
                                    $('#' + SupersessionID).append(Detail);

                                    var Footer = '<table style="width:100%;"><tr><td style="width:50%;text-align:left;">' + spanSuperseesionInfo + '</td><td style="width:50%;text-align:right;"><input id="BtnSupersessionPopUpCancel" value=' + $.getMessage('Cancel') + ' type="button"  class="SaveBtn" /></td></tr></table>'

                                    $('#' + SupersessionID).append(Footer);


                                    $('#' + SupersessionID).bPopup({ modalClose: false, opacity: 0.6, positionStyle: 'fixed', speed: 450, transition: 'slideDown' });

                                    $('.SupersessionPopUp').click(function () {
                                        try {
                                            if ($(this).attr('IsSuperseeded') == 1) {
                                                $.LoadSupersessionDetails($(this).attr('id'), '');
                                            }
                                            else {
                                                $('#' + SupersessionID).bPopup().close()

                                                txt = $(this).attr('PartNumber');

                                                var extra = "?Value=" + $.EncryptString(txt) + '&Key=' + $(this).attr('PartID');
                                                if (extraGrdPars != null && extraGrdPars != undefined) {

                                                    jQuery.each(extraGrdPars, function (key, val) {

                                                        switch (val) {
                                                            case 'check':
                                                                extra = extra.concat("&" + key + "=" + ($(grd.getCell(rowid, key)).attr('checked') == 'checked' ? true : false));
                                                                break;
                                                            case 'text':
                                                                extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                                break;
                                                            case 'select':
                                                                extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                                break;
                                                            default:
                                                                extra = extra.concat("&" + key + "=" + grd.getCell(rowid, key));
                                                                break;
                                                        }
                                                    });
                                                }

                                                if (checkDuplicate != undefined && checkDuplicate) {
                                                    extra = extra + "&mode=" + (grd.getCell(rowid, primCol) == '' ? "add" : "edit") + "&primID=" + grd.getCell(rowid, primCol);
                                                }                                                
                                                $.ajax({
                                                    url: url + extra + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                                                    async: false,
                                                    cached: false,
                                                    type: 'POST',
                                                    datatype: 'json',
                                                    success: function (data) {                                                        
                                                        if ($(data).attr(TxtMap) == '') {
                                                            jQuery.each(ColMap, function (key, val) {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).attr('value', '');
                                                                if (key == "Rate") {
                                                                    $('#' + hdn).change();
                                                                }
                                                            });

                                                            $('#' + Id).attr('value', txt);
                                                            document.getElementById(Id).value = "";
                                                            alert($.getMessage('Invalid') + ' ' + friendlyColName);
                                                        }
                                                        else {
                                                            jQuery.each(ColMap, function (key, val) {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).attr('value', $(data).attr(val));
                                                            });


                                                            $('#' + Id).attr('value', $(data).attr(TxtMap));

                                                            if (checkDuplicate != undefined && checkDuplicate) {
                                                                var rowIDS = grd.jqGrid('getDataIDs');
                                                                var txtValue = document.getElementById($(grd.getCell(rowid, DupCol)).attr('id')).value;
                                                                var isexist = false;
                                                                for (i = 0; i < rowIDS.length; i++) {
                                                                    var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');
                                                                    if (editmode != 'false' && rowIDS[i] != rowid) {
                                                                        var colValue = document.getElementById($(grd.getCell(rowIDS[i], DupCol)).attr('id')).value;
                                                                        if (txtValue == colValue) {
                                                                            isexist = true;
                                                                            $('#' + Id).attr('value', '');
                                                                            $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                            jQuery.each(ColMap, function (key, val) {
                                                                                if (key != "IsReWork") {
                                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                    $('#' + hdn).attr('value', '');
                                                                                    if (key == "Rate") {
                                                                                        $('#' + hdn).change();
                                                                                    }
                                                                                }
                                                                                else {
                                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                    $('#' + hdn).attr('checked', false);
                                                                                }
                                                                            });

                                                                            alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                            return;
                                                                        }
                                                                    }
                                                                }

                                                                if (data.IsExists && !isexist) {

                                                                    $('#' + Id).attr('value', '');
                                                                    $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                    jQuery.each(ColMap, function (key, val) {
                                                                        if (key != "IsReWork") {
                                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                            $('#' + hdn).attr('value', '');
                                                                            if (key == "Rate") {
                                                                                $('#' + hdn).change();
                                                                            }
                                                                        }
                                                                        else {
                                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                            $('#' + hdn).attr('checked', false);
                                                                        }
                                                                    });
                                                                    alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                }
                                                            }
                                                            if ($(data).attr('IsActive') == false) {
                                                                jQuery.each(ColMap, function (key, val) {
                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                    $('#' + hdn).attr('value', '');
                                                                    if (key == "Rate") {
                                                                        $('#' + hdn).change();
                                                                    }
                                                                });
                                                                $('#' + Id).attr('value', txt);
                                                                document.getElementById(Id).value = "";
                                                                $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                alert($.getMessage('Inactive') + ' ' + friendlyColName);
                                                                return;
                                                            }

                                                            if (events != undefined && events != null) {
                                                                jQuery.each(events, function (key, evt) {
                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                    $('#' + hdn).trigger(evt);
                                                                });
                                                            }
                                                        }
                                                    },
                                                    error: function (e, d, f) {
                                                        alert("");
                                                    }
                                                });

                                            }

                                        } catch (e) {

                                        }
                                    });

                                    $('#BtnSupersessionPopUpCancel').click(function () {
                                        try {
                                            $('#' + SupersessionID).bPopup().close();
                                        } catch (e) {

                                        }
                                    });
                                }
                                else {

                                    var extra = "?Value=" + $.EncryptString(SupersessionData.PartNumber) + '&Key=' + SupersessionData.Parts_ID;

                                    if (extraGrdPars != null && extraGrdPars != undefined) {

                                        jQuery.each(extraGrdPars, function (key, val) {

                                            switch (val) {
                                                case 'check':
                                                    extra = extra.concat("&" + key + "=" + ($(grd.getCell(rowid, key)).attr('checked') == 'checked' ? true : false));
                                                    break;
                                                case 'text':
                                                    extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                    break;
                                                case 'select':
                                                    extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                    break;
                                                default:
                                                    extra = extra.concat("&" + key + "=" + grd.getCell(rowid, key));
                                                    break;
                                            }
                                        });
                                    }

                                    if (checkDuplicate != undefined && checkDuplicate) {
                                        extra = extra + "&mode=" + (grd.getCell(rowid, primCol) == '' ? "add" : "edit") + "&primID=" + grd.getCell(rowid, primCol);
                                    }                                    
                                    $.ajax({
                                        url: url + extra + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                                        async: false,
                                        cached: false,
                                        type: 'POST',
                                        datatype: 'json',
                                        success: function (data) {                                            
                                            if ($(data).attr(TxtMap) == '') {
                                                jQuery.each(ColMap, function (key, val) {
                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                    $('#' + hdn).attr('value', '');
                                                    if (key == "Rate") {
                                                        $('#' + hdn).change();
                                                    }
                                                });

                                                $('#' + Id).attr('value', txt);
                                                document.getElementById(Id).value = "";
                                                alert($.getMessage('Invalid') + ' ' + friendlyColName);
                                            }
                                            else {
                                                //Modified By Puneeth M On 13-Jan-2015 for QA Issue
                                                if (data.Count > 1) {
                                                    document.getElementById(Id).value = data.Name;
                                                    $(document.getElementById($(grd.getCell(rowid, 'Search')).attr('id'))).trigger('mousedown');
                                                }
                                                else {
                                                    jQuery.each(ColMap, function (key, val) {

                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                        $('#' + hdn).attr('value', $(data).attr(val));
                                                    });


                                                    $('#' + Id).attr('value', $(data).attr(TxtMap));

                                                    if (checkDuplicate != undefined && checkDuplicate) {

                                                        var rowIDS = grd.jqGrid('getDataIDs');
                                                        var txtValue = document.getElementById($(grd.getCell(rowid, DupCol)).attr('id')).value;
                                                        var isexist = false;
                                                        for (i = 0; i < rowIDS.length; i++) {
                                                            var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');
                                                            if (editmode != 'false' && rowIDS[i] != rowid) {
                                                                var colValue = document.getElementById($(grd.getCell(rowIDS[i], DupCol)).attr('id')).value;
                                                                if (txtValue == colValue) {
                                                                    isexist = true;
                                                                    $('#' + Id).attr('value', '');
                                                                    $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                    jQuery.each(ColMap, function (key, val) {
                                                                        if (key != "IsReWork") {
                                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                            $('#' + hdn).attr('value', '');
                                                                            if (key == "Rate") {
                                                                                $('#' + hdn).change();
                                                                            }
                                                                        }
                                                                        else {
                                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                            $('#' + hdn).attr('checked', false);
                                                                        }
                                                                    });

                                                                    alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                    return;
                                                                }
                                                            }
                                                        }

                                                        if (data.IsExists && !isexist) {

                                                            $('#' + Id).attr('value', '');
                                                            $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                            jQuery.each(ColMap, function (key, val) {
                                                                if (key != "IsReWork") {
                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                    $('#' + hdn).attr('value', '');
                                                                    if (key == "Rate") {
                                                                        $('#' + hdn).change();
                                                                    }
                                                                }
                                                                else {
                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                    $('#' + hdn).attr('checked', false);
                                                                }
                                                            });
                                                            alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                        }
                                                        if ($(data).attr('IsActive') == false) {
                                                            jQuery.each(ColMap, function (key, val) {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).attr('value', '');
                                                                if (key == "Rate") {
                                                                    $('#' + hdn).change();
                                                                }
                                                            });
                                                            $('#' + Id).attr('value', txt);
                                                            document.getElementById(Id).value = "";
                                                            $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                            alert($.getMessage('Inactive') + ' ' + friendlyColName);
                                                            return;
                                                        }
                                                    }

                                                    if (events != undefined && events != null) {
                                                        jQuery.each(events, function (key, evt) {
                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                            $('#' + hdn).trigger(evt);
                                                        });
                                                    }
                                                }
                                            }
                                        },
                                        error: function (e, d, f) {
                                            alert("");
                                        }
                                    });
                                }

                            }
                        });

                    };                    
                    var SPartNumber = this.txt;
                    $.ajax({
                        url: urlPrefix + '?Part_Number=' + $.EncryptString(SPartNumber) + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                        type: 'POST',
                        async: false,
                        cached: false,
                        datatype: 'json',
                        success: function (PrefixData) {                            
                            if (PrefixData.IsMultiPrefixPart) {
                                var urlPrefixC = urlPrefix + '?Part_Number=' + $.EncryptString(txt) + (Obj.pars != undefined ? Obj.pars.call(this) : '')
                                $.ajax({
                                    url: urlPrefixC,
                                    async: false,
                                    cached: false,
                                    type: 'POST',
                                    datatype: 'json',
                                    success: function (PrefixDataData) {                                        
                                        if (PrefixDataData.IsMultiPrefixPart) {
                                            $('#' + PrefixID)[0].innerHTML = '';
                                            var PrefixDetail = '';

                                            if (PrefixDataData.PartDetail.length > 0) {

                                                PrefixDetail = '<span class="button b-close"><span>X</span></span><table cellpadding="3px" cellspacing="0px" style="border:1px solid lightgrey;">';
                                                PrefixDetail = PrefixDetail + '<tr><td colspan="4" style="width:100%;text-align: center;" class="ui-widget-header">' + $.getMessage('PartDetails') + '</td></tr>';
                                                PrefixDetail = PrefixDetail + '<tr><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;">' + '' + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + $.getMessage('Prefix') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('PartNumber') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('Description') + '</th></tr>';

                                                for (var i = 0; i < PrefixDataData.PartDetail.length; i++) {

                                                    PrefixDetail = PrefixDetail + '<tr><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;text-align:left;">' + PrefixDataData.PartDetail[i].Select + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;text-align:left;">' + PrefixDataData.PartDetail[i].PartPrefix + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:200px;text-align:left;">' + PrefixDataData.PartDetail[i].PartsNumber + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:200px;text-align:left;">' + PrefixDataData.PartDetail[i].PartsDescription + '</td></tr>';
                                                }
                                                PrefixDetail = PrefixDetail + '</table>'
                                            }
                                            $('#' + PrefixID).append(PrefixDetail);

                                            $('#' + PrefixID).bPopup({ modalClose: false, opacity: 0.6, positionStyle: 'fixed', speed: 450, transition: 'slideDown' });
                                            $('.PrefixPopUp').click(function () {
                                                isSelect = true;

                                                if ($(this).attr('IsSuperseeded') == 1) {
                                                    $('#' + PrefixID).bPopup().close()
                                                    $.LoadSupersessionDetails($(this).attr('PartID'), '');

                                                }
                                                else {

                                                    $('#' + PrefixID).bPopup().close()

                                                    txt = $(this).attr('PartNumber');

                                                    var extra = "?Value=" + $.EncryptString(txt) + '&Key=' + $(this).attr('PartID');

                                                    if (extraGrdPars != null && extraGrdPars != undefined) {

                                                        jQuery.each(extraGrdPars, function (key, val) {

                                                            switch (val) {
                                                                case 'check':
                                                                    extra = extra.concat("&" + key + "=" + ($(grd.getCell(rowid, key)).attr('checked') == 'checked' ? true : false));
                                                                    break;
                                                                case 'text':
                                                                    extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                                    break;
                                                                case 'select':
                                                                    extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                                    break;
                                                                default:
                                                                    extra = extra.concat("&" + key + "=" + grd.getCell(rowid, key));
                                                                    break;
                                                            }
                                                        });
                                                    }

                                                    if (checkDuplicate != undefined && checkDuplicate) {
                                                        extra = extra + "&mode=" + (grd.getCell(rowid, primCol) == '' ? "add" : "edit") + "&primID=" + grd.getCell(rowid, primCol);
                                                    }                                                    
                                                    $.ajax({
                                                        url: url + extra + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                                                        async: false,
                                                        cached: false,
                                                        type: 'POST',
                                                        datatype: 'json',
                                                        success: function (data) {                                                            
                                                            if ($(data).attr(TxtMap) == '') {
                                                                jQuery.each(ColMap, function (key, val) {
                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                    $('#' + hdn).attr('value', '');
                                                                    if (key == "Rate") {
                                                                        $('#' + hdn).change();
                                                                    }
                                                                });

                                                                $('#' + Id).attr('value', txt);
                                                                document.getElementById(Id).value = "";
                                                                alert($.getMessage('Invalid') + ' ' + friendlyColName + ' ' + $.getMessage('or').toLowerCase() + ' ' + friendlyColName + ' ' + $.getMessage('isinactive'));
                                                            }
                                                            else {
                                                                jQuery.each(ColMap, function (key, val) {
                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                    $('#' + hdn).attr('value', $(data).attr(val));
                                                                });

                                                                $('#' + Id).attr('value', $(data).attr(TxtMap));


                                                                if (checkDuplicate != undefined && checkDuplicate) {
                                                                    var rowIDS = grd.jqGrid('getDataIDs');
                                                                    var txtValue = document.getElementById($(grd.getCell(rowid, DupCol)).attr('id')).value;
                                                                    var isexist = false;
                                                                    for (i = 0; i < rowIDS.length; i++) {
                                                                        var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');
                                                                        if (editmode != 'false' && rowIDS[i] != rowid) {
                                                                            var colValue = document.getElementById($(grd.getCell(rowIDS[i], DupCol)).attr('id')).value;
                                                                            if (txtValue == colValue) {
                                                                                isexist = true;
                                                                                $('#' + Id).attr('value', '');
                                                                                $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                                jQuery.each(ColMap, function (key, val) {
                                                                                    if (key != "IsReWork") {
                                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                        $('#' + hdn).attr('value', '');
                                                                                        if (key == "Rate") {
                                                                                            $('#' + hdn).change();
                                                                                        }
                                                                                    }
                                                                                    else {
                                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                        $('#' + hdn).attr('checked', false);
                                                                                    }
                                                                                });

                                                                                alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                                return;
                                                                            }
                                                                        }
                                                                    }

                                                                    if (data.IsExists && !isexist) {

                                                                        $('#' + Id).attr('value', '');
                                                                        $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                        jQuery.each(ColMap, function (key, val) {
                                                                            if (key != "IsReWork") {
                                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                $('#' + hdn).attr('value', '');
                                                                                if (key == "Rate") {
                                                                                    $('#' + hdn).change();
                                                                                }
                                                                            }
                                                                            else {
                                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                $('#' + hdn).attr('checked', false);
                                                                            }
                                                                        });
                                                                        alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                    }
                                                                    if ($(data).attr('IsActive') == false) {
                                                                        jQuery.each(ColMap, function (key, val) {
                                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                            $('#' + hdn).attr('value', '');
                                                                            if (key == "Rate") {
                                                                                $('#' + hdn).change();
                                                                            }
                                                                        });
                                                                        $('#' + Id).attr('value', txt);
                                                                        document.getElementById(Id).value = "";
                                                                        $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                        alert($.getMessage('Inactive') + ' ' + friendlyColName);
                                                                        return;
                                                                    }
                                                                }

                                                                if (events != undefined && events != null) {
                                                                    jQuery.each(events, function (key, evt) {
                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                        $('#' + hdn).trigger(evt);
                                                                    });
                                                                }
                                                            }
                                                        },
                                                        error: function (e, d, f) {
                                                            alert("");
                                                        }
                                                    });


                                                }
                                            });
                                        }
                                        else {
                                            txt = PrefixDataData.PartNumber;
                                            var extra = "?Value=" + $.EncryptString(txt) + '&Key=' + PrefixDataData.Parts_ID;
                                            if (extraGrdPars != null && extraGrdPars != undefined) {
                                                jQuery.each(extraGrdPars, function (key, val) {
                                                    switch (val) {
                                                        case 'check':
                                                            extra = extra.concat("&" + key + "=" + ($(grd.getCell(rowid, key)).attr('checked') == 'checked' ? true : false));
                                                            break;
                                                        case 'text':
                                                            extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                            break;
                                                        case 'select':
                                                            extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                            break;
                                                        default:
                                                            extra = extra.concat("&" + key + "=" + grd.getCell(rowid, key));
                                                            break;
                                                    }
                                                });
                                            }

                                            if (checkDuplicate != undefined && checkDuplicate) {
                                                extra = extra + "&mode=" + (grd.getCell(rowid, primCol) == '' ? "add" : "edit") + "&primID=" + grd.getCell(rowid, primCol);
                                            }
                                            $.ajax({
                                                url: url + extra + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                                                type: 'POST',
                                                async: false,
                                                cached: false,
                                                datatype: 'json',
                                                success: function (data) {                                                    
                                                    if ($(data).attr(TxtMap) == '') {
                                                        jQuery.each(ColMap, function (key, val) {
                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                            $('#' + hdn).attr('value', '');
                                                            if (key == "Rate") {
                                                                $('#' + hdn).change();
                                                            }
                                                        });

                                                        $('#' + Id).attr('value', txt);
                                                        document.getElementById(Id).value = "";
                                                        alert($.getMessage('Invalid') + ' ' + friendlyColName + ' ' + $.getMessage('or').toLowerCase() + ' ' + friendlyColName + ' ' + $.getMessage('isinactive'));
                                                    }
                                                    else {
                                                        jQuery.each(ColMap, function (key, val) {
                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                            $('#' + hdn).attr('value', $(data).attr(val));
                                                        });
                                                        $('#' + Id).attr('value', $(data).attr(TxtMap));


                                                        if (checkDuplicate != undefined && checkDuplicate) {
                                                            var rowIDS = grd.jqGrid('getDataIDs');
                                                            var txtValue = document.getElementById($(grd.getCell(rowid, DupCol)).attr('id')).value;
                                                            var isexist = false;
                                                            for (i = 0; i < rowIDS.length; i++) {
                                                                var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');
                                                                if (editmode != 'false' && rowIDS[i] != rowid) {
                                                                    var colValue = document.getElementById($(grd.getCell(rowIDS[i], DupCol)).attr('id')).value;
                                                                    if (txtValue == colValue) {
                                                                        isexist = true;
                                                                        $('#' + Id).attr('value', '');
                                                                        $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                        jQuery.each(ColMap, function (key, val) {
                                                                            if (key != "IsReWork") {
                                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                $('#' + hdn).attr('value', '');
                                                                                if (key == "Rate") {
                                                                                    $('#' + hdn).change();
                                                                                }
                                                                            }
                                                                            else {
                                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                $('#' + hdn).attr('checked', false);
                                                                            }
                                                                        });

                                                                        alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                        return;
                                                                    }
                                                                }
                                                            }

                                                            if (data.IsExists && !isexist) {

                                                                $('#' + Id).attr('value', '');
                                                                $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                jQuery.each(ColMap, function (key, val) {
                                                                    if (key != "IsReWork") {
                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                        $('#' + hdn).attr('value', '');
                                                                        if (key == "Rate") {
                                                                            $('#' + hdn).change();
                                                                        }
                                                                    }
                                                                    else {
                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                        $('#' + hdn).attr('checked', false);
                                                                    }
                                                                });
                                                                alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                            }
                                                            if ($(data).attr('IsActive') == false) {
                                                                jQuery.each(ColMap, function (key, val) {
                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                    $('#' + hdn).attr('value', '');
                                                                    if (key == "Rate") {
                                                                        $('#' + hdn).change();
                                                                    }
                                                                });
                                                                $('#' + Id).attr('value', txt);
                                                                document.getElementById(Id).value = "";
                                                                $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                alert($.getMessage('Inactive') + ' ' + friendlyColName);
                                                                return;
                                                            }
                                                        }

                                                        if (events != undefined && events != null) {
                                                            jQuery.each(events, function (key, evt) {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).trigger(evt);
                                                            });
                                                        }
                                                    }
                                                },
                                                error: function (e, d, f) {
                                                    alert("");
                                                }
                                            });


                                        }
                                    }
                                });



                            }
                            else {
                                $.LoadSupersessionDetails(0, $.EncryptString(SPartNumber));

                            }
                        }
                    });
                }
            }
        }
    },
    {
        type: 'drop', fn: function () {
            return false;
        }
    }];
    jQuery.each(ColMap, function (key, val) {

        if (key != 'IsReWork') {
            grd.setColProp(key, { editable: true, edittype: 'text' });
        }
        else if (key == 'IsReWork') {
            grd.setColProp(key, { editable: true, edittype: 'checkbox' });
        }
    });

    this.fld = [{
        type: 'mousedown', fn: function (e) {

            IsLens = true;
            this.grd = grd;
            this.rowid = $(this).parent('td').parent('tr').attr('id');
            var rowid = this.rowid;

            this.txtBx = $(this.grd.getCell(this.rowid, TxtCol));
            this.Id = this.txtBx.attr('id');
            this.txtId = $(this.grd.getCell(this.rowid, IdField));
            this.txtId = this.txtId.attr('id');

            $("#" + FldSrchID).FieldSearch("Search", { ResultTextField: this.Id, ResultValueField: this.txtId, headerNames: { id: headerid, Name: headername }, ExtraParam: (Obj.pars != undefined ? Obj.pars.call(this) : '') });
            IsLens = false;


        }
    }];
    grd.setColProp(TxtCol, { editable: true, editoptions: { dataEvents: this.evnts } });

    if (IsFieldSearch != undefined) {
        if (IsFieldSearch) {

            $("#" + FldSrchID).FieldSearch({ url: FldUrl, colModels: colModels, colName: colName, FieldSearchName: FieldSearchName, DefaultSortColName: DefaultSortColName });
            grd.setColProp(SrchCol, { editable: true, edittype: 'image', editoptions: { dataEvents: this.fld, src: $.url('/Content/SearchLens.jpg') } });
        }
    }
    else {
        grd.setColProp(SrchCol, { editable: true, edittype: 'image', editoptions: { src: $.url('/Content/SearchLens.jpg') } });
    }

}

//--------------------------------------------------------------//

//---------------To calculate Round Off--------------------
$.roundNumber = function (rnum, rlength) { // Arguments: number to round, number of decimal places
    var newnumber = Math.round(rnum * Math.pow(10, rlength)) / Math.pow(10, rlength);
    return newnumber;
}

$.fn.GridInlinePurchaseProcessPartSearch = function (Obj) {
    var grd = $(this);
    var TxtCol = Obj.TxtCol;
    var TxtMap = Obj.TxtMap;
    var SrchCol = Obj.SrchCol;
    var url = Obj.url;
    var urlPrefix = Obj.urlPrefix;
    var urlSupersession = Obj.urlSupersession;
    var ColMap = Obj.ColMap;
    var extraGrdPars = Obj.extraGrdPars;
    var IsFieldSearch = Obj.IsFieldSearch;
    var IdField = undefined;
    var FldUrl = undefined;
    var FldSrchID = undefined;
    var SupersessionID = undefined;
    var PrefixID = undefined;
    var headerid = undefined;
    var headername = undefined;
    var events = Obj.events;
    var colModels = [];
    var colName = [];
    var FieldSearchName = undefined;
    var DefaultSortColName = undefined;

    if (IsFieldSearch != undefined && Obj.FieldSearch != undefined) {
        if (IsFieldSearch) {
            IdField = Obj.FieldSearch.IdField;
            FldUrl = Obj.FieldSearch.url;
            FldSrchID = Obj.FieldSearch.FldSrchID;
            SupersessionID = Obj.FieldSearch.SupersessionID;
            PrefixID = Obj.FieldSearch.PrefixID;
            headerid = Obj.FieldSearch.headerid == undefined ? '' : Obj.FieldSearch.headerid;
            headername = Obj.FieldSearch.headername == undefined ? '' : Obj.FieldSearch.headername;
            colModels = Obj.FieldSearch.colModels == undefined ? '' : Obj.FieldSearch.colModels;
            colName = Obj.FieldSearch.colName == undefined ? '' : Obj.FieldSearch.colName;
            FieldSearchName = Obj.FieldSearch.FieldSearchName == undefined ? '' : Obj.FieldSearch.FieldSearchName;
            DefaultSortColName = Obj.FieldSearch.DefaultSortColName == undefined ? '' : Obj.FieldSearch.DefaultSortColName;

        }
    }
    var checkDuplicate = Obj.checkDuplicate;
    var DupCol = '';
    var friendlyColName = '';
    var primCol = '';
    if (checkDuplicate) {
        DupCol = Obj.DupCol.ColName;
        friendlyColName = Obj.DupCol.friendlyColName;
        primCol = Obj.DupCol.primCol;
    }

    this.evnts = [{
        type: 'change', fn: function (e) {
            if (!IsLens) {

                this.grd = grd;
                this.rowid = $(this).parent('td').parent('tr').attr('id');
                var rowid = this.rowid;

                this.txtBx = $(this.grd.getCell(this.rowid, TxtCol));
                this.Id = this.txtBx.attr('id');
                var Id = this.Id;

                this.txt = document.getElementById(this.Id).value;
                this.value = '';
                var txt = this.txt;
                $('#' + Id).attr('duplicate', 'false');
                $('#' + Id).attr('exists', 'false');
                if (this.txt == '') {

                    jQuery.each(ColMap, function (key, val) {
                        if (key != "IsReWork") {
                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                            $('#' + hdn).attr('value', '');
                            if (key == "Rate") {
                                $('#' + hdn).change();
                            }
                        }
                        else {
                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                            $('#' + hdn).attr('checked', $(data).attr(val));
                        }
                    });

                    $('#' + Id).attr('value', '');
                }
                else {
                    $.LoadSupersessionDetails = function (val, val1) {
                        var Url = urlSupersession + '?Parts_ID=' + val + '&PartNumber=' + val1 + (Obj.pars != undefined ? Obj.pars.call(this) : '');
                        $.ajax({
                            async: false,
                            cached: false,
                            url: Url,
                            type: 'POST',
                            datatype: 'json',
                            success: function (SupersessionData) {

                                if (SupersessionData.IsSupersession == true) {
                                    var PreccedingPartDetail = '';
                                    var CurrentPartDetail = '';
                                    var NewPartDetail = '';

                                    $('#' + SupersessionID)[0].innerHTML = '';
                                    var Header = ' <span class="button b-close"><span>X</span></span><table  style="width:100%;text-align: center;" class="ui-widget-header"><tr><td>' + (SupersessionData.SupersessionType == null ? '' : SupersessionData.SupersessionType) + '</td></tr></table>';
                                    $('#' + SupersessionID).append(Header);

                                    var spanSuperseesionYes = '<span style="color:red;">*</span>';
                                    var spanSuperseesionNo = '<span>&nbsp;</span>';
                                    //Modified by: Kiran Date:5-12-2013 QA-Iteration-Missed out scenarios Changes Begin
                                    var spanSuperseesionInfo = '<span style="color:red;"><img style=height:20px;width:20px; src=' + $.url('/images/Supersession.jpg') + '> </span><span>' + $.getMessage('supersessionexist') + '</span>';
                                    //Changes Ends
                                    var Detail = '<table  style="width:100%;"><tr><td style="vertical-align:top;">';
                                    if (SupersessionData.PreccedingPartDetail.length > 0) {

                                        PreccedingPartDetail = '<table cellpadding="3px" cellspacing="0px" style="border:1px solid lightgrey;">';
                                        PreccedingPartDetail = PreccedingPartDetail + '<tr><td colspan="5" style="width:100%;text-align: center;" class="ui-widget-header">' + $.getMessage('PrecedingPartDetails') + '</td></tr>';
                                        PreccedingPartDetail = PreccedingPartDetail + '<tr><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;">' + '' + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + $.getMessage('Prefix') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('PartNumber') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('Description') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:5px;">' + '' + '</th></tr>';
                                        for (var i = 0; i < SupersessionData.PreccedingPartDetail.length; i++) {

                                            PreccedingPartDetail = PreccedingPartDetail + '<tr><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;text-align:center;">' + SupersessionData.PreccedingPartDetail[i].Select + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + SupersessionData.PreccedingPartDetail[i].PartPrefix + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + SupersessionData.PreccedingPartDetail[i].PartsNumber + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + (SupersessionData.PreccedingPartDetail[i].PartsDescription == null ? '' : SupersessionData.PreccedingPartDetail[i].PartsDescription) + '</td><td style="border:1px solid lightgrey;text-align:center;padding:3px 3px 3px 3px;width:5px;">' + SupersessionData.PreccedingPartDetail[i].IsSuperseeded + '</td></tr>';
                                        }
                                        PreccedingPartDetail = PreccedingPartDetail + '</table>'

                                    }
                                    Detail = Detail + PreccedingPartDetail;
                                    Detail = Detail + '</td><td style="vertical-align:top;">'

                                    if (SupersessionData.CurrentPartDetail.length > 0) {

                                        CurrentPartDetail = '<table cellpadding="3" cellspacing="0" style="border:1px solid lightgrey;">';
                                        CurrentPartDetail = CurrentPartDetail + '<tr><td colspan="5" style="width:100%;text-align: center;" class="ui-widget-header">' + $.getMessage('CurrentPartDetails') + '</td></tr>'
                                        CurrentPartDetail = CurrentPartDetail + '<tr ><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;">' + '' + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + $.getMessage('Prefix') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('PartNumber') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('Description') + '</th></tr>'
                                        for (var i = 0; i < SupersessionData.CurrentPartDetail.length; i++) {

                                            CurrentPartDetail = CurrentPartDetail + '<tr><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;text-align:center;width:25px;">' + SupersessionData.CurrentPartDetail[i].Select + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + SupersessionData.CurrentPartDetail[i].PartPrefix + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + SupersessionData.CurrentPartDetail[i].PartsNumber + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + (SupersessionData.CurrentPartDetail[i].PartsDescription == null ? '' : SupersessionData.CurrentPartDetail[i].PartsDescription) + '</td></tr>'
                                        }
                                        CurrentPartDetail = CurrentPartDetail + '</table>'
                                    }
                                    Detail = Detail + CurrentPartDetail;
                                    Detail = Detail + '</td><td style="vertical-align:top;">'

                                    if (SupersessionData.NewPartDetail.length > 0) {

                                        NewPartDetail = '<table cellpadding="3px" cellspacing="0px" style="border:1px solid lightgrey;">';
                                        NewPartDetail = NewPartDetail + '<tr><td colspan="5" style="width:100%;text-align: center;" class="ui-widget-header">' + $.getMessage('NewPartDetails') + '</td></tr>';
                                        NewPartDetail = NewPartDetail + '<tr><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;">' + '' + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + $.getMessage('Prefix') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('PartNumber') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('Description') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:5px;">' + '' + '</th></tr>';
                                        for (var i = 0; i < SupersessionData.NewPartDetail.length; i++) {

                                            NewPartDetail = NewPartDetail + '<tr><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;text-align:center;">' + SupersessionData.NewPartDetail[i].Select + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + SupersessionData.NewPartDetail[i].PartPrefix + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + SupersessionData.NewPartDetail[i].PartsNumber + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + (SupersessionData.NewPartDetail[i].PartsDescription == null ? '' : SupersessionData.NewPartDetail[i].PartsDescription) + '</td><td style="border:1px solid lightgrey;text-align:center;padding:3px 3px 3px 3px;width:5px;">' + SupersessionData.NewPartDetail[i].IsSuperseeded + '</td></tr>';
                                        }
                                        NewPartDetail = NewPartDetail + '</table>'
                                    }

                                    Detail = Detail + NewPartDetail;
                                    Detail = Detail + '</td></tr></table>'
                                    $('#' + SupersessionID).append(Detail);

                                    var Footer = '<table style="width:100%;"><tr><td style="width:50%;text-align:left;">' + spanSuperseesionInfo + '</td><td style="width:50%;text-align:right;"><input id="BtnSupersessionPopUpCancel" value=' + $.getMessage('Cancel') + ' type="button"  class="SaveBtn" /></td></tr></table>'

                                    $('#' + SupersessionID).append(Footer);


                                    $('#' + SupersessionID).bPopup({ modalClose: false, opacity: 0.6, positionStyle: 'fixed', speed: 450, transition: 'slideDown' });

                                    $('.SupersessionPopUp').click(function () {
                                        try {
                                            if ($(this).attr('IsSuperseeded') == 1) {
                                                $.LoadSupersessionDetails($(this).attr('id'), '');
                                            }
                                            else {
                                                $('#' + SupersessionID).bPopup().close()
                                                txt = $(this).attr('PartNumber');
                                                var extra = "?Value=" + $.EncryptString(txt) + '&Key=' + $(this).attr('PartID');
                                                if (extraGrdPars != null && extraGrdPars != undefined) {
                                                    jQuery.each(extraGrdPars, function (key, val) {
                                                        switch (val) {
                                                            case 'check':
                                                                extra = extra.concat("&" + key + "=" + ($(grd.getCell(rowid, key)).attr('checked') == 'checked' ? true : false));
                                                                break;
                                                            case 'text':
                                                                extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                                break;
                                                            case 'select':
                                                                extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                                break;
                                                            default:
                                                                extra = extra.concat("&" + key + "=" + grd.getCell(rowid, key));
                                                                break;
                                                        }
                                                    });
                                                }
                                                if (checkDuplicate != undefined && checkDuplicate) {
                                                    extra = extra + "&mode=" + (grd.getCell(rowid, primCol) == '' ? "add" : "edit") + "&primID=" + grd.getCell(rowid, primCol);
                                                }
                                                $.ajax({
                                                    url: url + extra + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                                                    type: 'POST',
                                                    async: false,
                                                    cached: false,
                                                    datatype: 'json',
                                                    success: function (data) {

                                                        if ($(data).attr(TxtMap) == '') {
                                                            jQuery.each(ColMap, function (key, val) {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).attr('value', '');
                                                                if (key == "Rate") {
                                                                    $('#' + hdn).change();
                                                                }
                                                            });

                                                            $('#' + Id).attr('value', txt);
                                                            document.getElementById(Id).value = "";
                                                            alert($.getMessage('Invalid') + ' ' + friendlyColName);
                                                        }
                                                        else if (data.IsActive == false) {
                                                            jQuery.each(ColMap, function (key, val) {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).attr('value', '');
                                                                if (key == "Rate") {
                                                                    $('#' + hdn).change();
                                                                }
                                                            });

                                                            $('#' + Id).attr('value', txt);
                                                            document.getElementById(Id).value = "";
                                                            alert(friendlyColName + ' ' + $.getMessage('isinactive'));
                                                        }
                                                        else {
                                                            jQuery.each(ColMap, function (key, val) {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).attr('value', $(data).attr(val));
                                                            });
                                                            $('#' + Id).attr('value', $(data).attr(TxtMap));
                                                            //Modified by: Kiran Date:5-12-2013 QA-Iteration-Missed out scenarios Changes Begin
                                                            $('#' + Id).focus();
                                                            //Changes Ends
                                                            if (checkDuplicate != undefined && checkDuplicate) {
                                                                var rowIDS = grd.jqGrid('getDataIDs');
                                                                var txtValue = document.getElementById($(grd.getCell(rowid, DupCol)).attr('id')).value;
                                                                var isexist = false;
                                                                for (i = 0; i < rowIDS.length; i++) {
                                                                    var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');
                                                                    if (editmode != 'false' && rowIDS[i] != rowid) {
                                                                        var colValue = document.getElementById($(grd.getCell(rowIDS[i], DupCol)).attr('id')).value;
                                                                        if (txtValue == colValue) {
                                                                            isexist = true;
                                                                            $('#' + Id).attr('value', '');
                                                                            $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                            jQuery.each(ColMap, function (key, val) {
                                                                                if (key != "IsReWork") {
                                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                    $('#' + hdn).attr('value', '');
                                                                                    if (key == "Rate") {
                                                                                        $('#' + hdn).change();
                                                                                    }
                                                                                }
                                                                                else {
                                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                    $('#' + hdn).attr('checked', false);
                                                                                }
                                                                            });

                                                                            alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                            return;
                                                                        }
                                                                    }
                                                                }
                                                                if (data.IsExists && !isexist) {
                                                                    $('#' + Id).attr('value', '');
                                                                    $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                    jQuery.each(ColMap, function (key, val) {
                                                                        if (key != "IsReWork") {
                                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                            $('#' + hdn).attr('value', '');
                                                                            if (key == "Rate") {
                                                                                $('#' + hdn).change();
                                                                            }
                                                                        }
                                                                        else {
                                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                            $('#' + hdn).attr('checked', false);
                                                                        }
                                                                    });
                                                                    alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                }
                                                            }
                                                            if (events != undefined && events != null) {
                                                                jQuery.each(events, function (key, evt) {
                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                    $('#' + hdn).trigger(evt);
                                                                });
                                                            }
                                                        }
                                                    },
                                                    error: function (e, d, f) {
                                                        alert("");
                                                    }
                                                });
                                            }

                                        } catch (e) { }
                                    });

                                    $('#BtnSupersessionPopUpCancel').click(function () {
                                        try {
                                            $('#' + SupersessionID).bPopup().close();
                                        } catch (e) { }
                                    });
                                }
                                else {
                                    var extra = "?Value=" + $.EncryptString(SupersessionData.PartNumber) + '&Key=' + SupersessionData.Parts_ID;
                                    if (extraGrdPars != null && extraGrdPars != undefined) {
                                        jQuery.each(extraGrdPars, function (key, val) {
                                            switch (val) {
                                                case 'check':
                                                    extra = extra.concat("&" + key + "=" + ($(grd.getCell(rowid, key)).attr('checked') == 'checked' ? true : false));
                                                    break;
                                                case 'text':
                                                    extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                    break;
                                                case 'select':
                                                    extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                    break;
                                                default:
                                                    extra = extra.concat("&" + key + "=" + grd.getCell(rowid, key));
                                                    break;
                                            }
                                        });
                                    }

                                    if (checkDuplicate != undefined && checkDuplicate) {
                                        extra = extra + "&mode=" + (grd.getCell(rowid, primCol) == '' ? "add" : "edit") + "&primID=" + grd.getCell(rowid, primCol);
                                    }
                                    $.ajax({
                                        url: url + extra + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                                        type: 'POST',
                                        async: false,
                                        cached: false,
                                        datatype: 'json',
                                        success: function (data) {

                                            if ($(data).attr(TxtMap) == '') {
                                                jQuery.each(ColMap, function (key, val) {
                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                    $('#' + hdn).attr('value', '');
                                                    if (key == "Rate") {
                                                        $('#' + hdn).change();
                                                    }
                                                });

                                                $('#' + Id).attr('value', txt);
                                                document.getElementById(Id).value = "";
                                                alert($.getMessage('Invalid') + ' ' + friendlyColName);
                                            }
                                            else if (data.IsActive == false) {
                                                jQuery.each(ColMap, function (key, val) {
                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                    $('#' + hdn).attr('value', '');
                                                    if (key == "Rate") {
                                                        $('#' + hdn).change();
                                                    }
                                                });

                                                $('#' + Id).attr('value', txt);
                                                document.getElementById(Id).value = "";
                                                alert(friendlyColName + ' ' + $.getMessage('isinactive'));
                                            }
                                            else {
                                                jQuery.each(ColMap, function (key, val) {
                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                    $('#' + hdn).attr('value', $(data).attr(val));
                                                });

                                                $('#' + Id).attr('value', $(data).attr(TxtMap));
                                                //Modified by: Kiran Date:5-12-2013 QA-Iteration-Missed out scenarios Changes Begin
                                                $('#' + Id).focus();
                                                //Changes Ends
                                                if (checkDuplicate != undefined && checkDuplicate) {
                                                    var rowIDS = grd.jqGrid('getDataIDs');
                                                    var txtValue = document.getElementById($(grd.getCell(rowid, DupCol)).attr('id')).value;
                                                    var isexist = false;
                                                    for (i = 0; i < rowIDS.length; i++) {
                                                        var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');
                                                        if (editmode != 'false' && rowIDS[i] != rowid) {
                                                            var colValue = document.getElementById($(grd.getCell(rowIDS[i], DupCol)).attr('id')).value;
                                                            if (txtValue == colValue) {
                                                                isexist = true;
                                                                $('#' + Id).attr('value', '');
                                                                $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                jQuery.each(ColMap, function (key, val) {
                                                                    if (key != "IsReWork") {
                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                        $('#' + hdn).attr('value', '');
                                                                        if (key == "Rate") {
                                                                            $('#' + hdn).change();
                                                                        }
                                                                    }
                                                                    else {
                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                        $('#' + hdn).attr('checked', false);
                                                                    }
                                                                });

                                                                alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                return;
                                                            }
                                                        }
                                                    }

                                                    if (data.IsExists && !isexist) {

                                                        $('#' + Id).attr('value', '');
                                                        $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                        jQuery.each(ColMap, function (key, val) {
                                                            if (key != "IsReWork") {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).attr('value', '');
                                                                if (key == "Rate") {
                                                                    $('#' + hdn).change();
                                                                }
                                                            }
                                                            else {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).attr('checked', false);
                                                            }
                                                        });
                                                        alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                    }
                                                }

                                                if (events != undefined && events != null) {
                                                    jQuery.each(events, function (key, evt) {
                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                        $('#' + hdn).trigger(evt);
                                                    });
                                                }
                                            }
                                        },
                                        error: function (e, d, f) {
                                            alert("");
                                        }
                                    });


                                }

                            }
                        });

                    };

                    var SPartNumber = this.txt;
                    $.ajax({
                        url: urlPrefix + '?Part_Number=' + $.EncryptString(SPartNumber) + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                        type: 'POST',
                        async: false,
                        cached: false,
                        datatype: 'json',
                        success: function (PrefixData) {

                            if (PrefixData.IsMultiPrefixPart) {

                                var urlPrefixC = urlPrefix + '?Part_Number=' + $.EncryptString(txt) + (Obj.pars != undefined ? Obj.pars.call(this) : '')

                                $.ajax({
                                    url: urlPrefixC,
                                    async: false,
                                    cached: false,
                                    type: 'POST',
                                    datatype: 'json',
                                    success: function (PrefixDataData) {
                                        if (PrefixDataData.IsMultiPrefixPart) {
                                            $('#' + PrefixID)[0].innerHTML = '';
                                            var PrefixDetail = '';

                                            if (PrefixDataData.PartDetail.length > 0) {

                                                PrefixDetail = '<span class="button b-close"><span>X</span></span><table cellpadding="3px" cellspacing="0px" style="border:1px solid lightgrey;">';
                                                PrefixDetail = PrefixDetail + '<tr><td colspan="4" style="width:100%;text-align: center;" class="ui-widget-header">' + $.getMessage('PartDetails') + '</td></tr>';
                                                PrefixDetail = PrefixDetail + '<tr><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;">' + '' + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + $.getMessage('Prefix') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('PartNumber') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('Description') + '</th></tr>';

                                                for (var i = 0; i < PrefixDataData.PartDetail.length; i++) {

                                                    PrefixDetail = PrefixDetail + '<tr><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;text-align:left;">' + PrefixDataData.PartDetail[i].Select + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;text-align:left;">' + PrefixDataData.PartDetail[i].PartPrefix + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:200px;text-align:left;">' + PrefixDataData.PartDetail[i].PartsNumber + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:200px;text-align:left;">' + PrefixDataData.PartDetail[i].PartsDescription + '</td></tr>';
                                                }
                                                PrefixDetail = PrefixDetail + '</table>'
                                            }

                                            $('#' + PrefixID).append(PrefixDetail);
                                            $('#' + PrefixID).bPopup({ modalClose: false, opacity: 0.6, positionStyle: 'fixed', speed: 450, transition: 'slideDown' });
                                            $('.PrefixPopUp').click(function () {
                                                isSelect = true;

                                                if ($(this).attr('IsSuperseeded') == 1) {
                                                    $('#' + PrefixID).bPopup().close()
                                                    $.LoadSupersessionDetails($(this).attr('PartID'), '');
                                                }
                                                else {
                                                    $('#' + PrefixID).bPopup().close()

                                                    txt = $(this).attr('PartNumber');

                                                    var extra = "?Value=" + $.EncryptString(txt) + '&Key=' + $(this).attr('PartID');

                                                    if (extraGrdPars != null && extraGrdPars != undefined) {

                                                        jQuery.each(extraGrdPars, function (key, val) {

                                                            switch (val) {
                                                                case 'check':
                                                                    extra = extra.concat("&" + key + "=" + ($(grd.getCell(rowid, key)).attr('checked') == 'checked' ? true : false));
                                                                    break;
                                                                case 'text':
                                                                    extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                                    break;
                                                                case 'select':
                                                                    extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                                    break;
                                                                default:
                                                                    extra = extra.concat("&" + key + "=" + grd.getCell(rowid, key));
                                                                    break;
                                                            }
                                                        });
                                                    }

                                                    if (checkDuplicate != undefined && checkDuplicate) {
                                                        extra = extra + "&mode=" + (grd.getCell(rowid, primCol) == '' ? "add" : "edit") + "&primID=" + grd.getCell(rowid, primCol);
                                                    }

                                                    $.ajax({
                                                        url: url + extra + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                                                        async: false,
                                                        cached: false,
                                                        type: 'POST',
                                                        datatype: 'json',
                                                        success: function (data) {
                                                            if ($(data).attr(TxtMap) == '') {
                                                                jQuery.each(ColMap, function (key, val) {
                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                    $('#' + hdn).attr('value', '');
                                                                    if (key == "Rate") {
                                                                        $('#' + hdn).change();
                                                                    }
                                                                });

                                                                $('#' + Id).attr('value', txt);
                                                                document.getElementById(Id).value = "";
                                                                alert($.getMessage('Invalid') + ' ' + friendlyColName);
                                                            }
                                                            else if (data.IsActive == false) {
                                                                jQuery.each(ColMap, function (key, val) {
                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                    $('#' + hdn).attr('value', '');
                                                                    if (key == "Rate") {
                                                                        $('#' + hdn).change();
                                                                    }
                                                                });

                                                                $('#' + Id).attr('value', txt);
                                                                document.getElementById(Id).value = "";
                                                                alert(friendlyColName + ' ' + $.getMessage('isinactive'));
                                                            }
                                                            else {
                                                                jQuery.each(ColMap, function (key, val) {
                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                    $('#' + hdn).attr('value', $(data).attr(val));
                                                                    if (key == "Rate") {
                                                                        $('#' + hdn).attr('value', Math.abs($(data).attr(val)).toFixed(2));
                                                                    }
                                                                });
                                                                $('#' + Id).attr('value', $(data).attr(TxtMap));
                                                                //Modified by: Kiran Date:5-12-2013 QA-Iteration-Missed out scenarios Changes Begin
                                                                $('#' + Id).focus();
                                                                //Changes Ends
                                                                if (checkDuplicate != undefined && checkDuplicate) {
                                                                    var rowIDS = grd.jqGrid('getDataIDs');
                                                                    var txtValue = document.getElementById($(grd.getCell(rowid, DupCol)).attr('id')).value;
                                                                    var isexist = false;
                                                                    for (i = 0; i < rowIDS.length; i++) {
                                                                        var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');
                                                                        if (editmode != 'false' && rowIDS[i] != rowid) {
                                                                            var colValue = document.getElementById($(grd.getCell(rowIDS[i], DupCol)).attr('id')).value;
                                                                            if (txtValue == colValue) {
                                                                                isexist = true;
                                                                                $('#' + Id).attr('value', '');
                                                                                $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                                jQuery.each(ColMap, function (key, val) {
                                                                                    if (key != "IsReWork") {
                                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                        $('#' + hdn).attr('value', '');
                                                                                        if (key == "Rate") {
                                                                                            $('#' + hdn).change();
                                                                                        }
                                                                                    }
                                                                                    else {
                                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                        $('#' + hdn).attr('checked', false);
                                                                                    }
                                                                                });

                                                                                alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                                return;
                                                                            }
                                                                        }
                                                                    }
                                                                    if (data.IsExists && !isexist) {

                                                                        $('#' + Id).attr('value', '');
                                                                        $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                        jQuery.each(ColMap, function (key, val) {
                                                                            if (key != "IsReWork") {
                                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                $('#' + hdn).attr('value', '');
                                                                                if (key == "Rate") {
                                                                                    $('#' + hdn).change();
                                                                                }
                                                                            }
                                                                            else {
                                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                $('#' + hdn).attr('checked', false);
                                                                            }
                                                                        });
                                                                        alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                    }
                                                                }
                                                                if (events != undefined && events != null) {
                                                                    jQuery.each(events, function (key, evt) {
                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                        $('#' + hdn).trigger(evt);
                                                                    });
                                                                }
                                                            }
                                                        },
                                                        error: function (e, d, f) {
                                                            alert("");
                                                        }
                                                    });
                                                }
                                            });
                                        }
                                        else {
                                            txt = PrefixDataData.PartNumber;
                                            var extra = "?Value=" + $.EncryptString(txt) + '&Key=' + PrefixDataData.Parts_ID;
                                            if (extraGrdPars != null && extraGrdPars != undefined) {
                                                jQuery.each(extraGrdPars, function (key, val) {
                                                    switch (val) {
                                                        case 'check':
                                                            extra = extra.concat("&" + key + "=" + ($(grd.getCell(rowid, key)).attr('checked') == 'checked' ? true : false));
                                                            break;
                                                        case 'text':
                                                            extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                            break;
                                                        case 'select':
                                                            extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                            break;
                                                        default:
                                                            extra = extra.concat("&" + key + "=" + grd.getCell(rowid, key));
                                                            break;
                                                    }
                                                });
                                            }

                                            if (checkDuplicate != undefined && checkDuplicate) {
                                                extra = extra + "&mode=" + (grd.getCell(rowid, primCol) == '' ? "add" : "edit") + "&primID=" + grd.getCell(rowid, primCol);
                                            }

                                            $.ajax({
                                                url: url + extra + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                                                type: 'POST',
                                                async: false,
                                                cached: false,
                                                datatype: 'json',
                                                success: function (data) {
                                                    if ($(data).attr(TxtMap) == '') {
                                                        jQuery.each(ColMap, function (key, val) {
                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                            $('#' + hdn).attr('value', '');
                                                            if (key == "Rate") {
                                                                $('#' + hdn).change();
                                                            }
                                                        });

                                                        $('#' + Id).attr('value', txt);
                                                        document.getElementById(Id).value = "";
                                                        alert($.getMessage('Invalid') + ' ' + friendlyColName);
                                                    }
                                                    else if (data.IsActive == false) {
                                                        jQuery.each(ColMap, function (key, val) {
                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                            $('#' + hdn).attr('value', '');
                                                            if (key == "Rate") {
                                                                $('#' + hdn).change();
                                                            }
                                                        });

                                                        $('#' + Id).attr('value', txt);
                                                        document.getElementById(Id).value = "";
                                                        alert(friendlyColName + ' ' + $.getMessage('isinactive'));
                                                    }
                                                    else {
                                                        jQuery.each(ColMap, function (key, val) {
                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                            $('#' + hdn).attr('value', $(data).attr(val));

                                                            if (key == "Rate") {
                                                                $('#' + hdn).attr('value', Math.abs($(data).attr(val)).toFixed(2));
                                                            }
                                                        });
                                                        $('#' + Id).attr('value', $(data).attr(TxtMap));
                                                        //Modified by: Kiran Date:5-12-2013 QA-Iteration-Missed out scenarios Changes Begin
                                                        $('#' + Id).focus();
                                                        //Changes Ends
                                                        if (checkDuplicate != undefined && checkDuplicate) {
                                                            var rowIDS = grd.jqGrid('getDataIDs');
                                                            var txtValue = document.getElementById($(grd.getCell(rowid, DupCol)).attr('id')).value;
                                                            var isexist = false;
                                                            for (i = 0; i < rowIDS.length; i++) {
                                                                var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');
                                                                if (editmode != 'false' && rowIDS[i] != rowid) {
                                                                    var colValue = document.getElementById($(grd.getCell(rowIDS[i], DupCol)).attr('id')).value;
                                                                    if (txtValue == colValue) {
                                                                        isexist = true;
                                                                        $('#' + Id).attr('value', '');
                                                                        $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                        jQuery.each(ColMap, function (key, val) {
                                                                            if (key != "IsReWork") {
                                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                $('#' + hdn).attr('value', '');
                                                                                if (key == "Rate") {
                                                                                    $('#' + hdn).change();
                                                                                }
                                                                            }
                                                                            else {
                                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                $('#' + hdn).attr('checked', false);
                                                                            }
                                                                        });

                                                                        alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                        return;
                                                                    }
                                                                }
                                                            }

                                                            if (data.IsExists && !isexist) {

                                                                $('#' + Id).attr('value', '');
                                                                $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                jQuery.each(ColMap, function (key, val) {
                                                                    if (key != "IsReWork") {
                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                        $('#' + hdn).attr('value', '');
                                                                        if (key == "Rate") {
                                                                            $('#' + hdn).change();
                                                                        }
                                                                    }
                                                                    else {
                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                        $('#' + hdn).attr('checked', false);
                                                                    }
                                                                });
                                                                alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                            }
                                                        }

                                                        if (events != undefined && events != null) {
                                                            jQuery.each(events, function (key, evt) {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).trigger(evt);
                                                            });
                                                        }
                                                    }
                                                },
                                                error: function (e, d, f) {
                                                    alert("");
                                                }
                                            });


                                        }
                                    }
                                });
                            }
                            else {
                                $.LoadSupersessionDetails(0, $.EncryptString(SPartNumber));
                            }
                        }
                    });
                }
            }
        }
    },
    {
        type: 'drop', fn: function () {
            return false;
        }
    }];
    jQuery.each(ColMap, function (key, val) {

        if (key != 'IsReWork') {
            grd.setColProp(key, { editable: true, edittype: 'text' });
        }
        else if (key == 'IsReWork') {
            grd.setColProp(key, { editable: true, edittype: 'checkbox' });
        }
    });

    this.fld = [{
        type: 'mousedown', fn: function (e) {

            IsLens = true;
            this.grd = grd;
            this.rowid = $(this).parent('td').parent('tr').attr('id');
            var rowid = this.rowid;

            this.txtBx = $(this.grd.getCell(this.rowid, TxtCol));
            this.Id = this.txtBx.attr('id');
            this.txtId = $(this.grd.getCell(this.rowid, IdField));
            this.txtId = this.txtId.attr('id');

            $("#" + FldSrchID).FieldSearch("Search", { ResultTextField: this.Id, ResultValueField: this.txtId, headerNames: { id: headerid, Name: headername }, ExtraParam: (Obj.pars != undefined ? Obj.pars.call(this) : '') });
            IsLens = false;


        }
    }];
    grd.setColProp(TxtCol, { editable: true, editoptions: { dataEvents: this.evnts } });
    //Modified by: Kiran Date:5-12-2013 QA-Iteration-Missed out scenarios Changes Begin
    if (IsFieldSearch != undefined) {
        if (IsFieldSearch) {

            $("#" + FldSrchID).FieldSearch({ url: FldUrl, colModels: colModels, colName: colName, FieldSearchName: FieldSearchName, DefaultSortColName: DefaultSortColName });
            grd.setColProp(SrchCol, { editable: true, edittype: 'image', editoptions: { dataEvents: this.fld, src: $.url('/Content/SearchLens.jpg'), 'tabindex': '-1' } });
        }
    }
    else {
        grd.setColProp(SrchCol, { editable: true, edittype: 'image', editoptions: { src: $.url('/Content/SearchLens.jpg'), 'tabindex': '-1' } });
    }
    //Changes Ends
}


$.fn.GridInlineStockProcessPartSearch = function (Obj) {
    var grd = $(this);
    var TxtCol = Obj.TxtCol;
    var TxtMap = Obj.TxtMap;
    var SrchCol = Obj.SrchCol;
    var url = Obj.url;
    var urlPrefix = Obj.urlPrefix;
    var urlSupersession = Obj.urlSupersession;
    var ColMap = Obj.ColMap;
    var extraGrdPars = Obj.extraGrdPars;
    var IsFieldSearch = Obj.IsFieldSearch;
    var IdField = undefined;
    var FldUrl = undefined;
    var FldSrchID = undefined;
    var SupersessionID = undefined;
    var PrefixID = undefined;
    var headerid = undefined;
    var headername = undefined;
    var events = Obj.events;
    var colModels = [];
    var colName = [];
    var FieldSearchName = undefined;
    var DefaultSortColName = undefined;

    if (IsFieldSearch != undefined && Obj.FieldSearch != undefined) {
        if (IsFieldSearch) {
            IdField = Obj.FieldSearch.IdField;
            FldUrl = Obj.FieldSearch.url;
            FldSrchID = Obj.FieldSearch.FldSrchID;
            SupersessionID = Obj.FieldSearch.SupersessionID;
            PrefixID = Obj.FieldSearch.PrefixID;
            headerid = Obj.FieldSearch.headerid == undefined ? '' : Obj.FieldSearch.headerid;
            headername = Obj.FieldSearch.headername == undefined ? '' : Obj.FieldSearch.headername;
            colModels = Obj.FieldSearch.colModels == undefined ? '' : Obj.FieldSearch.colModels;
            colName = Obj.FieldSearch.colName == undefined ? '' : Obj.FieldSearch.colName;
            FieldSearchName = Obj.FieldSearch.FieldSearchName == undefined ? '' : Obj.FieldSearch.FieldSearchName;
            DefaultSortColName = Obj.FieldSearch.DefaultSortColName == undefined ? '' : Obj.FieldSearch.DefaultSortColName;

        }
    }
    var checkDuplicate = Obj.checkDuplicate;
    var DupCol = '';
    var friendlyColName = '';
    var primCol = '';
    if (checkDuplicate) {
        DupCol = Obj.DupCol.ColName;
        friendlyColName = Obj.DupCol.friendlyColName;
        primCol = Obj.DupCol.primCol;
    }

    this.evnts = [{
        type: 'change', fn: function (e) {
            if (!IsLens) {

                this.grd = grd;
                this.rowid = $(this).parent('td').parent('tr').attr('id');
                var rowid = this.rowid;

                this.txtBx = $(this.grd.getCell(this.rowid, TxtCol));
                this.Id = this.txtBx.attr('id');
                var Id = this.Id;

                this.txt = document.getElementById(this.Id).value;
                this.value = '';
                var txt = this.txt;
                $('#' + Id).attr('duplicate', 'false');
                $('#' + Id).attr('exists', 'false');
                if (this.txt == '') {

                    jQuery.each(ColMap, function (key, val) {
                        if (key != "IsReWork") {
                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                            $('#' + hdn).attr('value', '');
                            if (key == "Rate") {
                                $('#' + hdn).change();
                            }
                        }
                        else {
                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                            $('#' + hdn).attr('checked', $(data).attr(val));
                        }
                    });

                    $('#' + Id).attr('value', '');
                }
                else {
                    $.LoadSupersessionDetails = function (val, val1) {
                        var Url = urlSupersession + '?Parts_ID=' + val + '&PartNumber=' + val1 + (Obj.pars != undefined ? Obj.pars.call(this) : '');
                        $.ajax({
                            async: false,
                            cached: false,
                            url: Url,
                            type: 'POST',
                            datatype: 'json',
                            success: function (SupersessionData) {

                                if (SupersessionData.IsSupersession == true) {
                                    var PreccedingPartDetail = '';
                                    var CurrentPartDetail = '';
                                    var NewPartDetail = '';

                                    $('#' + SupersessionID)[0].innerHTML = '';
                                    var Header = ' <span class="button b-close"><span>X</span></span><table  style="width:100%;text-align: center;" class="ui-widget-header"><tr><td>' + (SupersessionData.SupersessionType == null ? '' : SupersessionData.SupersessionType) + '</td></tr></table>';
                                    $('#' + SupersessionID).append(Header);

                                    var spanSuperseesionYes = '<span style="color:red;">*</span>';
                                    var spanSuperseesionNo = '<span>&nbsp;</span>';
                                    //Modified by: Kiran Date:5-12-2013 QA-Iteration-Missed out scenarios Changes Begin
                                    var spanSuperseesionInfo = '<span style="color:red;"><img style=height:20px;width:20px; src=' + $.url('/images/Supersession.jpg') + '> </span><span>' + $.getMessage('supersessionexist') + '</span>';
                                    //Changes Ends
                                    var Detail = '<table  style="width:100%;"><tr><td style="vertical-align:top;">';
                                    if (SupersessionData.PreccedingPartDetail.length > 0) {

                                        PreccedingPartDetail = '<table cellpadding="3px" cellspacing="0px" style="border:1px solid lightgrey;">';
                                        PreccedingPartDetail = PreccedingPartDetail + '<tr><td colspan="5" style="width:100%;text-align: center;" class="ui-widget-header">' + $.getMessage('PrecedingPartDetails') + '</td></tr>';
                                        PreccedingPartDetail = PreccedingPartDetail + '<tr><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;">' + '' + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + $.getMessage('Prefix') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('PartNumber') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('Description') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:5px;">' + '' + '</th></tr>';
                                        for (var i = 0; i < SupersessionData.PreccedingPartDetail.length; i++) {

                                            PreccedingPartDetail = PreccedingPartDetail + '<tr><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;text-align:center;">' + SupersessionData.PreccedingPartDetail[i].Select + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + SupersessionData.PreccedingPartDetail[i].PartPrefix + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + SupersessionData.PreccedingPartDetail[i].PartsNumber + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + (SupersessionData.PreccedingPartDetail[i].PartsDescription == null ? '' : SupersessionData.PreccedingPartDetail[i].PartsDescription) + '</td><td style="border:1px solid lightgrey;text-align:center;padding:3px 3px 3px 3px;width:5px;">' + SupersessionData.PreccedingPartDetail[i].IsSuperseeded + '</td></tr>';
                                        }
                                        PreccedingPartDetail = PreccedingPartDetail + '</table>'

                                    }
                                    Detail = Detail + PreccedingPartDetail;
                                    Detail = Detail + '</td><td style="vertical-align:top;">'

                                    if (SupersessionData.CurrentPartDetail.length > 0) {

                                        CurrentPartDetail = '<table cellpadding="3" cellspacing="0" style="border:1px solid lightgrey;">';
                                        CurrentPartDetail = CurrentPartDetail + '<tr><td colspan="5" style="width:100%;text-align: center;" class="ui-widget-header">' + $.getMessage('CurrentPartDetails') + '</td></tr>'
                                        CurrentPartDetail = CurrentPartDetail + '<tr ><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;">' + '' + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + $.getMessage('Prefix') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('PartNumber') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('Description') + '</th></tr>'
                                        for (var i = 0; i < SupersessionData.CurrentPartDetail.length; i++) {

                                            CurrentPartDetail = CurrentPartDetail + '<tr><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;text-align:center;width:25px;">' + SupersessionData.CurrentPartDetail[i].Select + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + SupersessionData.CurrentPartDetail[i].PartPrefix + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + SupersessionData.CurrentPartDetail[i].PartsNumber + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + (SupersessionData.CurrentPartDetail[i].PartsDescription == null ? '' : SupersessionData.CurrentPartDetail[i].PartsDescription) + '</td></tr>'
                                        }
                                        CurrentPartDetail = CurrentPartDetail + '</table>'
                                    }
                                    Detail = Detail + CurrentPartDetail;
                                    Detail = Detail + '</td><td style="vertical-align:top;">'

                                    if (SupersessionData.NewPartDetail.length > 0) {

                                        NewPartDetail = '<table cellpadding="3px" cellspacing="0px" style="border:1px solid lightgrey;">';
                                        NewPartDetail = NewPartDetail + '<tr><td colspan="5" style="width:100%;text-align: center;" class="ui-widget-header">' + $.getMessage('NewPartDetails') + '</td></tr>';
                                        NewPartDetail = NewPartDetail + '<tr><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;">' + '' + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + $.getMessage('Prefix') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('PartNumber') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('Description') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:5px;">' + '' + '</th></tr>';
                                        for (var i = 0; i < SupersessionData.NewPartDetail.length; i++) {

                                            NewPartDetail = NewPartDetail + '<tr><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;text-align:center;">' + SupersessionData.NewPartDetail[i].Select + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + SupersessionData.NewPartDetail[i].PartPrefix + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + SupersessionData.NewPartDetail[i].PartsNumber + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + (SupersessionData.NewPartDetail[i].PartsDescription == null ? '' : SupersessionData.NewPartDetail[i].PartsDescription) + '</td><td style="border:1px solid lightgrey;text-align:center;padding:3px 3px 3px 3px;width:5px;">' + SupersessionData.NewPartDetail[i].IsSuperseeded + '</td></tr>';
                                        }
                                        NewPartDetail = NewPartDetail + '</table>'
                                    }

                                    Detail = Detail + NewPartDetail;
                                    Detail = Detail + '</td></tr></table>'
                                    $('#' + SupersessionID).append(Detail);

                                    var Footer = '<table style="width:100%;"><tr><td style="width:50%;text-align:left;">' + spanSuperseesionInfo + '</td><td style="width:50%;text-align:right;"><input id="BtnSupersessionPopUpCancel" value=' + $.getMessage('Cancel') + ' type="button"  class="SaveBtn" /></td></tr></table>'

                                    $('#' + SupersessionID).append(Footer);


                                    $('#' + SupersessionID).bPopup({ modalClose: false, opacity: 0.6, positionStyle: 'fixed', speed: 450, transition: 'slideDown' });

                                    $('.SupersessionPopUp').click(function () {
                                        try {
                                            if ($(this).attr('IsSuperseeded') == 1) {
                                                $.LoadSupersessionDetails($(this).attr('id'), '');
                                            }
                                            else {
                                                $('#' + SupersessionID).bPopup().close()
                                                txt = $(this).attr('PartNumber');
                                                var extra = "?Value=" + $.EncryptString(txt) + '&Key=' + $(this).attr('PartID');
                                                if (extraGrdPars != null && extraGrdPars != undefined) {
                                                    jQuery.each(extraGrdPars, function (key, val) {
                                                        switch (val) {
                                                            case 'check':
                                                                extra = extra.concat("&" + key + "=" + ($(grd.getCell(rowid, key)).attr('checked') == 'checked' ? true : false));
                                                                break;
                                                            case 'text':
                                                                extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                                break;
                                                            case 'select':
                                                                extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                                break;
                                                            default:
                                                                extra = extra.concat("&" + key + "=" + grd.getCell(rowid, key));
                                                                break;
                                                        }
                                                    });
                                                }
                                                if (checkDuplicate != undefined && checkDuplicate) {
                                                    extra = extra + "&mode=" + (grd.getCell(rowid, primCol) == '' ? "add" : "edit") + "&primID=" + grd.getCell(rowid, primCol);
                                                }
                                                $.ajax({
                                                    url: url + extra + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                                                    type: 'POST',
                                                    async: false,
                                                    cached: false,
                                                    datatype: 'json',
                                                    success: function (data) {

                                                        if ($(data).attr(TxtMap) == '') {
                                                            jQuery.each(ColMap, function (key, val) {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).attr('value', '');
                                                                if (key == "Rate") {
                                                                    $('#' + hdn).change();
                                                                }
                                                            });

                                                            $('#' + Id).attr('value', txt);
                                                            document.getElementById(Id).value = "";
                                                            alert($.getMessage('Invalid') + ' ' + friendlyColName);
                                                        }
                                                        else if (data.IsActive == false) {
                                                            jQuery.each(ColMap, function (key, val) {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).attr('value', '');
                                                                if (key == "Rate") {
                                                                    $('#' + hdn).change();
                                                                }
                                                            });

                                                            $('#' + Id).attr('value', txt);
                                                            document.getElementById(Id).value = "";
                                                            alert(friendlyColName + ' ' + $.getMessage('isinactive'));
                                                        }
                                                        else if (Math.abs(data.rate) == 0) {
                                                            jQuery.each(ColMap, function (key, val) {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).attr('value', '');
                                                                if (key == "Rate") {
                                                                    $('#' + hdn).change();
                                                                }
                                                            });

                                                            $('#' + Id).attr('value', txt);
                                                            document.getElementById(Id).value = "";
                                                            alert($.getMessage('RateCannotbeZero'));
                                                        }
                                                        else {
                                                            jQuery.each(ColMap, function (key, val) {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).attr('value', $(data).attr(val));
                                                            });
                                                            $('#' + Id).attr('value', $(data).attr(TxtMap));
                                                            if (checkDuplicate != undefined && checkDuplicate) {
                                                                var rowIDS = grd.jqGrid('getDataIDs');
                                                                var txtValue = document.getElementById($(grd.getCell(rowid, DupCol)).attr('id')).value;
                                                                var isexist = false;
                                                                for (i = 0; i < rowIDS.length; i++) {
                                                                    var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');
                                                                    if (editmode != 'false' && rowIDS[i] != rowid) {
                                                                        var colValue = document.getElementById($(grd.getCell(rowIDS[i], DupCol)).attr('id')).value;
                                                                        if (txtValue == colValue) {
                                                                            isexist = true;
                                                                            $('#' + Id).attr('value', '');
                                                                            $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                            jQuery.each(ColMap, function (key, val) {
                                                                                if (key != "IsReWork") {
                                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                    $('#' + hdn).attr('value', '');
                                                                                    if (key == "Rate") {
                                                                                        $('#' + hdn).change();
                                                                                    }
                                                                                }
                                                                                else {
                                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                    $('#' + hdn).attr('checked', false);
                                                                                }
                                                                            });

                                                                            alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                            return;
                                                                        }
                                                                    }
                                                                }
                                                                if (data.IsExists && !isexist) {
                                                                    $('#' + Id).attr('value', '');
                                                                    $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                    jQuery.each(ColMap, function (key, val) {
                                                                        if (key != "IsReWork") {
                                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                            $('#' + hdn).attr('value', '');
                                                                            if (key == "Rate") {
                                                                                $('#' + hdn).change();
                                                                            }
                                                                        }
                                                                        else {
                                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                            $('#' + hdn).attr('checked', false);
                                                                        }
                                                                    });
                                                                    alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                }
                                                            }
                                                            if (events != undefined && events != null) {
                                                                jQuery.each(events, function (key, evt) {
                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                    $('#' + hdn).trigger(evt);
                                                                });
                                                            }
                                                        }
                                                    },
                                                    error: function (e, d, f) {
                                                        alert("");
                                                    }
                                                });
                                            }

                                        } catch (e) { }
                                    });

                                    $('#BtnSupersessionPopUpCancel').click(function () {
                                        try {
                                            $('#' + SupersessionID).bPopup().close();
                                        } catch (e) { }
                                    });
                                }
                                else {
                                    var extra = "?Value=" + $.EncryptString(SupersessionData.PartNumber) + '&Key=' + SupersessionData.Parts_ID;
                                    if (extraGrdPars != null && extraGrdPars != undefined) {
                                        jQuery.each(extraGrdPars, function (key, val) {
                                            switch (val) {
                                                case 'check':
                                                    extra = extra.concat("&" + key + "=" + ($(grd.getCell(rowid, key)).attr('checked') == 'checked' ? true : false));
                                                    break;
                                                case 'text':
                                                    extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                    break;
                                                case 'select':
                                                    extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                    break;
                                                default:
                                                    extra = extra.concat("&" + key + "=" + grd.getCell(rowid, key));
                                                    break;
                                            }
                                        });
                                    }

                                    if (checkDuplicate != undefined && checkDuplicate) {
                                        extra = extra + "&mode=" + (grd.getCell(rowid, primCol) == '' ? "add" : "edit") + "&primID=" + grd.getCell(rowid, primCol);
                                    }
                                    $.ajax({
                                        url: url + extra + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                                        type: 'POST',
                                        async: false,
                                        cached: false,
                                        datatype: 'json',
                                        success: function (data) {

                                            if ($(data).attr(TxtMap) == '') {
                                                jQuery.each(ColMap, function (key, val) {
                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                    $('#' + hdn).attr('value', '');
                                                    if (key == "Rate") {
                                                        $('#' + hdn).change();
                                                    }
                                                });

                                                $('#' + Id).attr('value', txt);
                                                document.getElementById(Id).value = "";
                                                alert($.getMessage('Invalid') + ' ' + friendlyColName);
                                            }
                                            else if (data.IsActive == false) {
                                                jQuery.each(ColMap, function (key, val) {
                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                    $('#' + hdn).attr('value', '');
                                                    if (key == "Rate") {
                                                        $('#' + hdn).change();
                                                    }
                                                });

                                                $('#' + Id).attr('value', txt);
                                                document.getElementById(Id).value = "";
                                                alert(friendlyColName + ' ' + $.getMessage('isinactive'));
                                            }
                                            else if (Math.abs(data.rate) == 0) {
                                                jQuery.each(ColMap, function (key, val) {
                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                    $('#' + hdn).attr('value', '');
                                                    if (key == "Rate") {
                                                        $('#' + hdn).change();
                                                    }
                                                });

                                                $('#' + Id).attr('value', txt);
                                                document.getElementById(Id).value = "";
                                                alert($.getMessage('RateCannotbeZero'));
                                            }
                                            else {
                                                jQuery.each(ColMap, function (key, val) {
                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                    $('#' + hdn).attr('value', $(data).attr(val));
                                                });

                                                $('#' + Id).attr('value', $(data).attr(TxtMap));
                                                if (checkDuplicate != undefined && checkDuplicate) {
                                                    var rowIDS = grd.jqGrid('getDataIDs');
                                                    var txtValue = document.getElementById($(grd.getCell(rowid, DupCol)).attr('id')).value;
                                                    var isexist = false;
                                                    for (i = 0; i < rowIDS.length; i++) {
                                                        var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');
                                                        if (editmode != 'false' && rowIDS[i] != rowid) {
                                                            var colValue = document.getElementById($(grd.getCell(rowIDS[i], DupCol)).attr('id')).value;
                                                            if (txtValue == colValue) {
                                                                isexist = true;
                                                                $('#' + Id).attr('value', '');
                                                                $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                jQuery.each(ColMap, function (key, val) {
                                                                    if (key != "IsReWork") {
                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                        $('#' + hdn).attr('value', '');
                                                                        if (key == "Rate") {
                                                                            $('#' + hdn).change();
                                                                        }
                                                                    }
                                                                    else {
                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                        $('#' + hdn).attr('checked', false);
                                                                    }
                                                                });

                                                                alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                return;
                                                            }
                                                        }
                                                    }

                                                    if (data.IsExists && !isexist) {

                                                        $('#' + Id).attr('value', '');
                                                        $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                        jQuery.each(ColMap, function (key, val) {
                                                            if (key != "IsReWork") {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).attr('value', '');
                                                                if (key == "Rate") {
                                                                    $('#' + hdn).change();
                                                                }
                                                            }
                                                            else {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).attr('checked', false);
                                                            }
                                                        });
                                                        alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                    }
                                                }

                                                if (events != undefined && events != null) {
                                                    jQuery.each(events, function (key, evt) {
                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                        $('#' + hdn).trigger(evt);
                                                    });
                                                }
                                            }
                                        },
                                        error: function (e, d, f) {
                                            alert("");
                                        }
                                    });


                                }

                            }
                        });

                    };

                    var SPartNumber = this.txt;
                    $.ajax({
                        url: urlPrefix + '?Part_Number=' + $.EncryptString(SPartNumber) + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                        type: 'POST',
                        async: false,
                        cached: false,
                        datatype: 'json',
                        success: function (PrefixData) {

                            if (PrefixData.IsMultiPrefixPart) {

                                var urlPrefixC = urlPrefix + '?Part_Number=' + $.EncryptString(txt) + (Obj.pars != undefined ? Obj.pars.call(this) : '')

                                $.ajax({
                                    url: urlPrefixC,
                                    async: false,
                                    cached: false,
                                    type: 'POST',
                                    datatype: 'json',
                                    success: function (PrefixDataData) {
                                        if (PrefixDataData.IsMultiPrefixPart) {
                                            $('#' + PrefixID)[0].innerHTML = '';
                                            var PrefixDetail = '';


                                            if (PrefixDataData.PartDetail.length > 0) {

                                                PrefixDetail = '<span class="button b-close"><span>X</span></span><table cellpadding="3px" cellspacing="0px" style="border:1px solid lightgrey;">';
                                                PrefixDetail = PrefixDetail + '<tr><td colspan="4" style="width:100%;text-align: center;" class="ui-widget-header">' + $.getMessage('PartDetails') + '</td></tr>';
                                                PrefixDetail = PrefixDetail + '<tr><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;">' + '' + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + $.getMessage('Prefix') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('PartNumber') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('Description') + '</th></tr>';

                                                for (var i = 0; i < PrefixDataData.PartDetail.length; i++) {

                                                    PrefixDetail = PrefixDetail + '<tr><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;text-align:left;">' + PrefixDataData.PartDetail[i].Select + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;text-align:left;">' + PrefixDataData.PartDetail[i].PartPrefix + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:200px;text-align:left;">' + PrefixDataData.PartDetail[i].PartsNumber + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:200px;text-align:left;">' + PrefixDataData.PartDetail[i].PartsDescription + '</td></tr>';
                                                }
                                                PrefixDetail = PrefixDetail + '</table>'
                                            }

                                            $('#' + PrefixID).append(PrefixDetail);
                                            $('#' + PrefixID).bPopup({ modalClose: false, opacity: 0.6, positionStyle: 'fixed', speed: 450, transition: 'slideDown' });
                                            $('.PrefixPopUp').click(function () {
                                                isSelect = true;

                                                if ($(this).attr('IsSuperseeded') == 1) {
                                                    $('#' + PrefixID).bPopup().close()
                                                    $.LoadSupersessionDetails($(this).attr('PartID'), '');
                                                }
                                                else {
                                                    $('#' + PrefixID).bPopup().close()

                                                    txt = $(this).attr('PartNumber');

                                                    var extra = "?Value=" + $.EncryptString(txt) + '&Key=' + $(this).attr('PartID');

                                                    if (extraGrdPars != null && extraGrdPars != undefined) {

                                                        jQuery.each(extraGrdPars, function (key, val) {

                                                            switch (val) {
                                                                case 'check':
                                                                    extra = extra.concat("&" + key + "=" + ($(grd.getCell(rowid, key)).attr('checked') == 'checked' ? true : false));
                                                                    break;
                                                                case 'text':
                                                                    extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                                    break;
                                                                case 'select':
                                                                    extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                                    break;
                                                                default:
                                                                    extra = extra.concat("&" + key + "=" + grd.getCell(rowid, key));
                                                                    break;
                                                            }
                                                        });
                                                    }

                                                    if (checkDuplicate != undefined && checkDuplicate) {
                                                        extra = extra + "&mode=" + (grd.getCell(rowid, primCol) == '' ? "add" : "edit") + "&primID=" + grd.getCell(rowid, primCol);
                                                    }

                                                    $.ajax({
                                                        url: url + extra + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                                                        async: false,
                                                        cached: false,
                                                        type: 'POST',
                                                        datatype: 'json',
                                                        success: function (data) {
                                                            if ($(data).attr(TxtMap) == '') {
                                                                jQuery.each(ColMap, function (key, val) {
                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                    $('#' + hdn).attr('value', '');
                                                                    if (key == "Rate") {
                                                                        $('#' + hdn).change();
                                                                    }
                                                                });

                                                                $('#' + Id).attr('value', txt);
                                                                document.getElementById(Id).value = "";
                                                                alert($.getMessage('Invalid') + ' ' + friendlyColName);
                                                            }
                                                            else if (data.IsActive == false) {
                                                                jQuery.each(ColMap, function (key, val) {
                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                    $('#' + hdn).attr('value', '');
                                                                    if (key == "Rate") {
                                                                        $('#' + hdn).change();
                                                                    }
                                                                });

                                                                $('#' + Id).attr('value', txt);
                                                                document.getElementById(Id).value = "";
                                                                alert(friendlyColName + ' ' + $.getMessage('isinactive'));
                                                            }
                                                            else if (Math.abs(data.rate) == 0) {
                                                                jQuery.each(ColMap, function (key, val) {
                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                    $('#' + hdn).attr('value', '');
                                                                    if (key == "Rate") {
                                                                        $('#' + hdn).change();
                                                                    }
                                                                });

                                                                $('#' + Id).attr('value', txt);
                                                                document.getElementById(Id).value = "";
                                                                alert($.getMessage('RateCannotbeZero'));
                                                            }
                                                            else {
                                                                jQuery.each(ColMap, function (key, val) {
                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                    $('#' + hdn).attr('value', $(data).attr(val));
                                                                    if (key == "Rate") {
                                                                        $('#' + hdn).attr('value', Math.abs($(data).attr(val)).toFixed(2));
                                                                    }
                                                                });
                                                                $('#' + Id).attr('value', $(data).attr(TxtMap));
                                                                if (checkDuplicate != undefined && checkDuplicate) {
                                                                    var rowIDS = grd.jqGrid('getDataIDs');
                                                                    var txtValue = document.getElementById($(grd.getCell(rowid, DupCol)).attr('id')).value;
                                                                    var isexist = false;
                                                                    for (i = 0; i < rowIDS.length; i++) {
                                                                        var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');
                                                                        if (editmode != 'false' && rowIDS[i] != rowid) {
                                                                            var colValue = document.getElementById($(grd.getCell(rowIDS[i], DupCol)).attr('id')).value;
                                                                            if (txtValue == colValue) {
                                                                                isexist = true;
                                                                                $('#' + Id).attr('value', '');
                                                                                $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                                jQuery.each(ColMap, function (key, val) {
                                                                                    if (key != "IsReWork") {
                                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                        $('#' + hdn).attr('value', '');
                                                                                        if (key == "Rate") {
                                                                                            $('#' + hdn).change();
                                                                                        }
                                                                                    }
                                                                                    else {
                                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                        $('#' + hdn).attr('checked', false);
                                                                                    }
                                                                                });

                                                                                alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                                return;
                                                                            }
                                                                        }
                                                                    }
                                                                    if (data.IsExists && !isexist) {

                                                                        $('#' + Id).attr('value', '');
                                                                        $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                        jQuery.each(ColMap, function (key, val) {
                                                                            if (key != "IsReWork") {
                                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                $('#' + hdn).attr('value', '');
                                                                                if (key == "Rate") {
                                                                                    $('#' + hdn).change();
                                                                                }
                                                                            }
                                                                            else {
                                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                $('#' + hdn).attr('checked', false);
                                                                            }
                                                                        });
                                                                        alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                    }
                                                                }
                                                                if (events != undefined && events != null) {
                                                                    jQuery.each(events, function (key, evt) {
                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                        $('#' + hdn).trigger(evt);
                                                                    });
                                                                }
                                                            }
                                                        },
                                                        error: function (e, d, f) {
                                                            alert("");
                                                        }
                                                    });
                                                }
                                            });
                                        }
                                        else {
                                            txt = PrefixDataData.PartNumber;
                                            var extra = "?Value=" + $.EncryptString(txt) + '&Key=' + PrefixDataData.Parts_ID;
                                            if (extraGrdPars != null && extraGrdPars != undefined) {
                                                jQuery.each(extraGrdPars, function (key, val) {
                                                    switch (val) {
                                                        case 'check':
                                                            extra = extra.concat("&" + key + "=" + ($(grd.getCell(rowid, key)).attr('checked') == 'checked' ? true : false));
                                                            break;
                                                        case 'text':
                                                            extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                            break;
                                                        case 'select':
                                                            extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                            break;
                                                        default:
                                                            extra = extra.concat("&" + key + "=" + grd.getCell(rowid, key));
                                                            break;
                                                    }
                                                });
                                            }

                                            if (checkDuplicate != undefined && checkDuplicate) {
                                                extra = extra + "&mode=" + (grd.getCell(rowid, primCol) == '' ? "add" : "edit") + "&primID=" + grd.getCell(rowid, primCol);
                                            }

                                            $.ajax({
                                                url: url + extra + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                                                type: 'POST',
                                                async: false,
                                                cached: false,
                                                datatype: 'json',
                                                success: function (data) {
                                                    if ($(data).attr(TxtMap) == '') {
                                                        jQuery.each(ColMap, function (key, val) {
                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                            $('#' + hdn).attr('value', '');
                                                            if (key == "Rate") {
                                                                $('#' + hdn).change();
                                                            }
                                                        });

                                                        $('#' + Id).attr('value', txt);
                                                        document.getElementById(Id).value = "";
                                                        alert($.getMessage('Invalid') + ' ' + friendlyColName);
                                                    }
                                                    else if (data.IsActive == false) {
                                                        jQuery.each(ColMap, function (key, val) {
                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                            $('#' + hdn).attr('value', '');
                                                            if (key == "Rate") {
                                                                $('#' + hdn).change();
                                                            }
                                                        });

                                                        $('#' + Id).attr('value', txt);
                                                        document.getElementById(Id).value = "";
                                                        alert(friendlyColName + ' ' + $.getMessage('isinactive'));
                                                    }
                                                    else if (Math.abs(data.rate) == 0) {
                                                        jQuery.each(ColMap, function (key, val) {
                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                            $('#' + hdn).attr('value', '');
                                                            if (key == "Rate") {
                                                                $('#' + hdn).change();
                                                            }
                                                        });

                                                        $('#' + Id).attr('value', txt);
                                                        document.getElementById(Id).value = "";
                                                        alert($.getMessage('RateCannotbeZero'));
                                                    }
                                                    else {
                                                        jQuery.each(ColMap, function (key, val) {
                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                            $('#' + hdn).attr('value', $(data).attr(val));

                                                            if (key == "Rate") {
                                                                $('#' + hdn).attr('value', Math.abs($(data).attr(val)).toFixed(2));
                                                            }
                                                        });
                                                        $('#' + Id).attr('value', $(data).attr(TxtMap));
                                                        if (checkDuplicate != undefined && checkDuplicate) {
                                                            var rowIDS = grd.jqGrid('getDataIDs');
                                                            var txtValue = document.getElementById($(grd.getCell(rowid, DupCol)).attr('id')).value;
                                                            var isexist = false;
                                                            for (i = 0; i < rowIDS.length; i++) {
                                                                var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');
                                                                if (editmode != 'false' && rowIDS[i] != rowid) {
                                                                    var colValue = document.getElementById($(grd.getCell(rowIDS[i], DupCol)).attr('id')).value;
                                                                    if (txtValue == colValue) {
                                                                        isexist = true;
                                                                        $('#' + Id).attr('value', '');
                                                                        $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                        jQuery.each(ColMap, function (key, val) {
                                                                            if (key != "IsReWork") {
                                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                $('#' + hdn).attr('value', '');
                                                                                if (key == "Rate") {
                                                                                    $('#' + hdn).change();
                                                                                }
                                                                            }
                                                                            else {
                                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                $('#' + hdn).attr('checked', false);
                                                                            }
                                                                        });

                                                                        alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                        return;
                                                                    }
                                                                }
                                                            }

                                                            if (data.IsExists && !isexist) {

                                                                $('#' + Id).attr('value', '');
                                                                $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                jQuery.each(ColMap, function (key, val) {
                                                                    if (key != "IsReWork") {
                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                        $('#' + hdn).attr('value', '');
                                                                        if (key == "Rate") {
                                                                            $('#' + hdn).change();
                                                                        }
                                                                    }
                                                                    else {
                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                        $('#' + hdn).attr('checked', false);
                                                                    }
                                                                });
                                                                alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                            }
                                                        }

                                                        if (events != undefined && events != null) {
                                                            jQuery.each(events, function (key, evt) {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).trigger(evt);
                                                            });
                                                        }
                                                    }
                                                },
                                                error: function (e, d, f) {
                                                    alert("");
                                                }
                                            });


                                        }
                                    }
                                });
                            }
                            else {
                                $.LoadSupersessionDetails(0, $.EncryptString(SPartNumber));
                            }
                        }
                    });
                }
            }
        }
    },
    {
        type: 'drop', fn: function () {
            return false;
        }
    }];
    jQuery.each(ColMap, function (key, val) {

        if (key != 'IsReWork') {
            grd.setColProp(key, { editable: true, edittype: 'text' });
        }
        else if (key == 'IsReWork') {
            grd.setColProp(key, { editable: true, edittype: 'checkbox' });
        }
    });

    this.fld = [{
        type: 'mousedown', fn: function (e) {

            IsLens = true;
            this.grd = grd;
            this.rowid = $(this).parent('td').parent('tr').attr('id');
            var rowid = this.rowid;

            this.txtBx = $(this.grd.getCell(this.rowid, TxtCol));
            this.Id = this.txtBx.attr('id');
            this.txtId = $(this.grd.getCell(this.rowid, IdField));
            this.txtId = this.txtId.attr('id');

            $("#" + FldSrchID).FieldSearch("Search", { ResultTextField: this.Id, ResultValueField: this.txtId, headerNames: { id: headerid, Name: headername }, ExtraParam: (Obj.pars != undefined ? Obj.pars.call(this) : '') });
            IsLens = false;


        }
    }];
    grd.setColProp(TxtCol, { editable: true, editoptions: { dataEvents: this.evnts } });

    if (IsFieldSearch != undefined) {
        if (IsFieldSearch) {

            $("#" + FldSrchID).FieldSearch({ url: FldUrl, colModels: colModels, colName: colName, FieldSearchName: FieldSearchName, DefaultSortColName: DefaultSortColName });
            grd.setColProp(SrchCol, { editable: true, edittype: 'image', editoptions: { dataEvents: this.fld, src: $.url('/Content/SearchLens.jpg') } });
        }
    }
    else {
        grd.setColProp(SrchCol, { editable: true, edittype: 'image', editoptions: { src: $.url('/Content/SearchLens.jpg') } });
    }
}
//------------------------Grid inline Search with Separate Inactive and Invalid Message------------------------------------------------------------------------//
$.fn.GridInlineSearchActive = function (Obj) {

    var grd = $(this);
    var TxtCol = Obj.TxtCol;
    var TxtMap = Obj.TxtMap;
    var SrchCol = Obj.SrchCol;
    var url = Obj.url;
    var ColMap = Obj.ColMap;
    var extraGrdPars = Obj.extraGrdPars;
    var IsFieldSearch = Obj.IsFieldSearch;
    var IdField = undefined;
    var FldUrl = undefined;
    var FldSrchID = undefined;
    var headerid = undefined;
    var headername = undefined;
    var events = Obj.events;
    var colModels = [];
    var colName = [];
    var FieldSearchName = undefined;
    var DefaultSortColName = undefined;

    if (IsFieldSearch != undefined && Obj.FieldSearch != undefined) {
        if (IsFieldSearch) {
            IdField = Obj.FieldSearch.IdField;
            FldUrl = Obj.FieldSearch.url;
            FldSrchID = Obj.FieldSearch.FldSrchID;
            headerid = Obj.FieldSearch.headerid == undefined ? '' : Obj.FieldSearch.headerid;
            headername = Obj.FieldSearch.headername == undefined ? '' : Obj.FieldSearch.headername;
            colModels = Obj.FieldSearch.colModels == undefined ? '' : Obj.FieldSearch.colModels;
            colName = Obj.FieldSearch.colName == undefined ? '' : Obj.FieldSearch.colName;
            FieldSearchName = Obj.FieldSearch.FieldSearchName == undefined ? '' : Obj.FieldSearch.FieldSearchName;
            DefaultSortColName = Obj.FieldSearch.DefaultSortColName == undefined ? '' : Obj.FieldSearch.DefaultSortColName;

        }
    }
    var checkDuplicate = Obj.checkDuplicate;
    var DupCol = '';
    var friendlyColName = '';
    var primCol = '';

    friendlyColName = (Obj.DupCol != undefined && Obj.DupCol != null) ? Obj.DupCol.friendlyColName : "";

    if (checkDuplicate) {
        DupCol = Obj.DupCol.ColName;
        primCol = Obj.DupCol.primCol;
    }

    this.evnts = [{
        type: 'change', fn: function (e) {
            if (!IsLens) {

                this.grd = grd;
                this.rowid = $(this).parent('td').parent('tr').attr('id');
                var rowid = this.rowid;

                this.txtBx = $(this.grd.getCell(this.rowid, TxtCol));
                this.Id = this.txtBx.attr('id');
                var Id = this.Id;

                this.txt = document.getElementById(this.Id).value;
                this.value = '';
                var txt = this.txt;
                $('#' + Id).attr('duplicate', 'false');
                $('#' + Id).attr('exists', 'false');

                if (this.txt == '') {

                    jQuery.each(ColMap, function (key, val) {
                        if (key != "IsReWork") {
                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                            $('#' + hdn).attr('value', '');
                            if (key == "Rate") {
                                $('#' + hdn).change();
                            }
                        }
                        else {
                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                            $('#' + hdn).attr('checked', $(data).attr(val));
                        }
                    });

                    $('#' + Id).attr('value', '');
                }
                else {

                    var extra = "?Key=" + $.EncryptString(txt);
                    if (extraGrdPars != null && extraGrdPars != undefined) {

                        jQuery.each(extraGrdPars, function (key, val) {

                            switch (val) {
                                case 'check':
                                    extra = extra.concat("&" + key + "=" + ($(grd.getCell(rowid, key)).attr('checked') == 'checked' ? true : false));
                                    break;
                                case 'text':
                                    extra = extra.concat("&" + key + "=" + document.getElementById($(grd.getCell(rowid, key)).attr('id')).value);
                                    break;
                                case 'select':
                                    extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                    break;
                                default:
                                    extra = extra.concat("&" + key + "=" + grd.getCell(rowid, key));
                                    break;
                            }
                        });
                    }
                    if (checkDuplicate != undefined && checkDuplicate) {
                        extra = extra + "&mode=" + (grd.getCell(rowid, primCol) == '' ? "add" : "edit") + "&primID=" + grd.getCell(rowid, primCol);
                    }

                    $.ajax({
                        url: url + extra + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                        async: false,
                        cached: false,
                        type: 'POST',
                        datatype: 'json',
                        success: function (data) {

                            if ($(data).attr(TxtMap) == '') {
                                jQuery.each(ColMap, function (key, val) {
                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                    $('#' + hdn).attr('value', '');
                                    if (key == "Rate") {
                                        $('#' + hdn).change();
                                    }
                                });

                                $('#' + Id).attr('value', txt);
                                document.getElementById(Id).value = "";
                                //Modified by: Kiran Date:27-9-2013 QA-Iteration-2 Changes Begin
                                alert($.getMessage('Invalid') + ' ' + friendlyColName.toLowerCase());
                                //Changes Ends
                            }
                            else if (data.IsActive == false) {
                                jQuery.each(ColMap, function (key, val) {
                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                    $('#' + hdn).attr('value', '');
                                    if (key == "Rate") {
                                        $('#' + hdn).change();
                                    }
                                });

                                $('#' + Id).attr('value', txt);
                                document.getElementById(Id).value = "";
                                alert(friendlyColName + ' ' + $.getMessage('isinactive'));
                            }
                            else {
                                jQuery.each(ColMap, function (key, val) {

                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                    $('#' + hdn).attr('value', $(data).attr(val));

                                    if (key == "FreeStock") {
                                        grd.setCell(rowid, 'PartNumber', '', '', { title: 'Free Stock :' + $(data).attr(val) });
                                    }
                                });

                                $('#' + Id).attr('value', $(data).attr(TxtMap));

                                if (checkDuplicate != undefined && checkDuplicate) {
                                    var rowIDS = grd.jqGrid('getDataIDs');
                                    var txtValue = document.getElementById($(grd.getCell(rowid, DupCol)).attr('id')).value;
                                    var isexist = false;
                                    for (i = 0; i < rowIDS.length; i++) {
                                        var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');
                                        if (editmode != 'false' && rowIDS[i] != rowid) {
                                            var colValue = document.getElementById($(grd.getCell(rowIDS[i], DupCol)).attr('id')).value;
                                            if (txtValue == colValue) {
                                                isexist = true;
                                                $('#' + Id).attr('value', '');
                                                $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                jQuery.each(ColMap, function (key, val) {
                                                    if (key != "IsReWork") {
                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                        $('#' + hdn).attr('value', '');
                                                        if (key == "Rate") {
                                                            $('#' + hdn).change();
                                                        }
                                                    }
                                                    else {
                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                        $('#' + hdn).attr('checked', false);
                                                    }
                                                });

                                                alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                return;
                                            }
                                        }
                                    }

                                    if (data.IsExists && !isexist) {

                                        $('#' + Id).attr('value', '');
                                        $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                        jQuery.each(ColMap, function (key, val) {
                                            if (key != "IsReWork") {
                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                $('#' + hdn).attr('value', '');
                                                if (key == "Rate") {
                                                    $('#' + hdn).change();
                                                }
                                            }
                                            else {
                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                $('#' + hdn).attr('checked', false);
                                            }
                                        });
                                        alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                    }
                                }

                                if (events != undefined && events != null) {
                                    jQuery.each(events, function (key, evt) {
                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                        $('#' + hdn).trigger(evt);
                                    });
                                }
                            }
                        },
                        error: function (e, d, f) {
                            alert("");
                        }
                    });
                }
            }
        }
    },
    {
        type: 'drop', fn: function () {
            return false;
        }
    }];
    jQuery.each(ColMap, function (key, val) {

        if (key != 'IsReWork') {
            grd.setColProp(key, { editable: true, edittype: 'text' });
        }
        else if (key == 'IsReWork') {
            grd.setColProp(key, { editable: true, edittype: 'checkbox' });
        }
    });

    this.fld = [{
        type: 'mousedown', fn: function (e) {

            IsLens = true;
            this.grd = grd;
            this.rowid = $(this).parent('td').parent('tr').attr('id');
            var rowid = this.rowid;

            this.txtBx = $(this.grd.getCell(this.rowid, TxtCol));
            this.Id = this.txtBx.attr('id');
            this.txtId = $(this.grd.getCell(this.rowid, IdField));
            this.txtId = this.txtId.attr('id');

            var extra = "";
            if (extraGrdPars != null && extraGrdPars != undefined) {
                jQuery.each(extraGrdPars, function (key, val) {
                    switch (val) {
                        case 'check':
                            extra = extra.concat("&" + key + "=" + ($(grd.getCell(rowid, key)).attr('checked') == 'checked' ? true : false));
                            break;
                        case 'text':
                            extra = extra.concat("&" + key + "=" + document.getElementById($(grd.getCell(rowid, key)).attr('id')).value);
                            break;
                        case 'select':
                            extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                            break;
                        default:
                            extra = extra.concat("&" + key + "=" + grd.getCell(rowid, key));
                            break;
                    }
                });
            }
            var extraParameters = extra + (Obj.pars != undefined ? Obj.pars.call(this) : '');
            $("#" + FldSrchID).FieldSearch("Search", { ResultTextField: this.Id, ResultValueField: this.txtId, headerNames: { id: headerid, Name: headername }, ExtraParam: extraParameters });
            IsLens = false;


        }
    }];
    grd.setColProp(TxtCol, { editable: true, editoptions: { dataEvents: this.evnts } });

    if (IsFieldSearch != undefined) {
        if (IsFieldSearch) {

            $("#" + FldSrchID).FieldSearch({ url: FldUrl, colModels: colModels, colName: colName, FieldSearchName: FieldSearchName, DefaultSortColName: DefaultSortColName });

            grd.setColProp(SrchCol, { editable: true, edittype: 'image', editoptions: { dataEvents: this.fld, src: $.url('/Content/SearchLens.jpg') } });
        }
    }
    else {
        grd.setColProp(SrchCol, { editable: true, edittype: 'image', editoptions: { src: $.url('/Content/SearchLens.jpg') } });
    }

}

//---------------For Parts Series----------------//
//Used For Prefix and Supersession Details Display During Part Search
$.fn.GridInlinePartSearchForSalesProcess = function (Obj) {
    var grd = $(this);
    var TxtCol = Obj.TxtCol;
    var TxtMap = Obj.TxtMap;
    var SrchCol = Obj.SrchCol;
    var url = Obj.url;
    var urlPrefix = Obj.urlPrefix;
    var urlSupersession = Obj.urlSupersession;
    var ColMap = Obj.ColMap;
    var extraGrdPars = Obj.extraGrdPars;
    var IsFieldSearch = Obj.IsFieldSearch;
    var IdField = undefined;
    var FldUrl = undefined;
    var FldSrchID = undefined;
    var SupersessionID = undefined;
    var PrefixID = undefined;
    var headerid = undefined;
    var headername = undefined;
    var events = Obj.events;
    var colModels = [];
    var colName = [];
    var FieldSearchName = undefined;
    var DefaultSortColName = undefined;

    if (IsFieldSearch != undefined && Obj.FieldSearch != undefined) {
        if (IsFieldSearch) {
            IdField = Obj.FieldSearch.IdField;
            FldUrl = Obj.FieldSearch.url;
            FldSrchID = Obj.FieldSearch.FldSrchID;
            SupersessionID = Obj.FieldSearch.SupersessionID;
            PrefixID = Obj.FieldSearch.PrefixID;
            headerid = Obj.FieldSearch.headerid == undefined ? '' : Obj.FieldSearch.headerid;
            headername = Obj.FieldSearch.headername == undefined ? '' : Obj.FieldSearch.headername;
            colModels = Obj.FieldSearch.colModels == undefined ? '' : Obj.FieldSearch.colModels;
            colName = Obj.FieldSearch.colName == undefined ? '' : Obj.FieldSearch.colName;
            FieldSearchName = Obj.FieldSearch.FieldSearchName == undefined ? '' : Obj.FieldSearch.FieldSearchName;
            DefaultSortColName = Obj.FieldSearch.DefaultSortColName == undefined ? '' : Obj.FieldSearch.DefaultSortColName;

        }
    }
    var checkDuplicate = Obj.checkDuplicate;
    var DupCol = '';
    var friendlyColName = '';
    var primCol = '';
    if (checkDuplicate) {
        DupCol = Obj.DupCol.ColName;
        friendlyColName = Obj.DupCol.friendlyColName;
        primCol = Obj.DupCol.primCol;
    }

    this.evnts = [{
        type: 'change', fn: function (e) {
            if (!IsLens) {
                this.grd = grd;
                this.rowid = $(this).parent('td').parent('tr').attr('id');
                var rowid = this.rowid;

                this.txtBx = $(this.grd.getCell(this.rowid, TxtCol));
                this.Id = this.txtBx.attr('id');
                var Id = this.Id;

                this.txt = document.getElementById(this.Id).value;
                this.value = '';
                var txt = this.txt;
                $('#' + Id).attr('duplicate', 'false');
                $('#' + Id).attr('exists', 'false');
                $('#' + Id).focus();
                if (this.txt == '') {

                    jQuery.each(ColMap, function (key, val) {
                        if (key != "IsReWork") {
                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                            $('#' + hdn).attr('value', '');
                            if (key == "Rate") {
                                $('#' + hdn).change();
                            }
                        }
                        else {
                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                            $('#' + hdn).attr('checked', $(data).attr(val));
                        }
                    });

                    $('#' + Id).attr('value', '');
                    var taxstrid = $(grd.getCell(rowid, 'TaxStructure')).attr('id');
                    $.ClearDropDown(taxstrid);
                }
                else {
                    $.LoadSupersessionDetails = function (val, val1) {

                        var Url = urlSupersession + '?Parts_ID=' + val + '&PartNumber=' + val1 + (Obj.pars != undefined ? Obj.pars.call(this) : '');
                        $.ajax({
                            async: false,
                            cached: false,
                            url: Url,
                            type: 'POST',
                            datatype: 'json',
                            success: function (SupersessionData) {
                                if (SupersessionData.IsSupersession == true) {

                                    var PreccedingPartDetail = '';
                                    var CurrentPartDetail = '';
                                    var NewPartDetail = '';

                                    $('#' + SupersessionID)[0].innerHTML = '';
                                    var Header = ' <span class="button b-close"><span>X</span></span><table  style="width:100%;text-align: center;" class="ui-widget-header"><tr><td>' + (SupersessionData.SupersessionType == null ? '' : SupersessionData.SupersessionType) + '</td></tr></table>';
                                    $('#' + SupersessionID).append(Header);

                                    var spanSuperseesionYes = '<span style="color:red;">*</span>';
                                    var spanSuperseesionNo = '<span>&nbsp;</span>';
                                    //Modified by: Kiran Date:5-12-2013 QA-Iteration-Missed out scenarios Changes Begin
                                    var spanSuperseesionInfo = '<span style="color:red;"><img style=height:20px;width:20px; src=' + $.url('/images/Supersession.jpg') + '> </span><span>' + $.getMessage('supersessionexist') + '</span>';
                                    //Changes Ends
                                    var Detail = '<table  style="width:100%;"><tr><td style="vertical-align:top;">';
                                    if (SupersessionData.PreccedingPartDetail.length > 0) {

                                        PreccedingPartDetail = '<table cellpadding="3px" cellspacing="0px" style="border:1px solid lightgrey;">';
                                        PreccedingPartDetail = PreccedingPartDetail + '<tr><td colspan="5" style="width:100%;text-align: center;" class="ui-widget-header">' + $.getMessage('PrecedingPartDetails') + '</td></tr>';
                                        PreccedingPartDetail = PreccedingPartDetail + '<tr><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;">' + '' + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + $.getMessage('Prefix') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('PartNumber') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('Description') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:5px;">' + '' + '</th></tr>';
                                        for (var i = 0; i < SupersessionData.PreccedingPartDetail.length; i++) {

                                            PreccedingPartDetail = PreccedingPartDetail + '<tr><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;text-align:center;">' + SupersessionData.PreccedingPartDetail[i].Select + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + SupersessionData.PreccedingPartDetail[i].PartPrefix + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + SupersessionData.PreccedingPartDetail[i].PartsNumber + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + (SupersessionData.PreccedingPartDetail[i].PartsDescription == null ? '' : SupersessionData.PreccedingPartDetail[i].PartsDescription) + '</td><td style="border:1px solid lightgrey;text-align:center;padding:3px 3px 3px 3px;width:5px;">' + SupersessionData.PreccedingPartDetail[i].IsSuperseeded + '</td></tr>';
                                        }
                                        PreccedingPartDetail = PreccedingPartDetail + '</table>'

                                    }
                                    Detail = Detail + PreccedingPartDetail;
                                    Detail = Detail + '</td><td style="vertical-align:top;">'

                                    if (SupersessionData.CurrentPartDetail.length > 0) {

                                        CurrentPartDetail = '<table cellpadding="3" cellspacing="0" style="border:1px solid lightgrey;">';
                                        CurrentPartDetail = CurrentPartDetail + '<tr><td colspan="5" style="width:100%;text-align: center;" class="ui-widget-header">' + $.getMessage('CurrentPartDetails') + '</td></tr>'
                                        CurrentPartDetail = CurrentPartDetail + '<tr ><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;">' + '' + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + $.getMessage('Prefix') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('PartNumber') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('Description') + '</th></tr>'
                                        for (var i = 0; i < SupersessionData.CurrentPartDetail.length; i++) {

                                            CurrentPartDetail = CurrentPartDetail + '<tr><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;text-align:center;width:25px;">' + SupersessionData.CurrentPartDetail[i].Select + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + SupersessionData.CurrentPartDetail[i].PartPrefix + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + SupersessionData.CurrentPartDetail[i].PartsNumber + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + (SupersessionData.CurrentPartDetail[i].PartsDescription == null ? '' : SupersessionData.CurrentPartDetail[i].PartsDescription) + '</td></tr>'
                                        }
                                        CurrentPartDetail = CurrentPartDetail + '</table>'
                                    }
                                    Detail = Detail + CurrentPartDetail;
                                    Detail = Detail + '</td><td style="vertical-align:top;">'

                                    if (SupersessionData.NewPartDetail.length > 0) {

                                        NewPartDetail = '<table cellpadding="3px" cellspacing="0px" style="border:1px solid lightgrey;">';
                                        NewPartDetail = NewPartDetail + '<tr><td colspan="5" style="width:100%;text-align: center;" class="ui-widget-header">' + $.getMessage('NewPartDetails') + '</td></tr>';
                                        NewPartDetail = NewPartDetail + '<tr><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;">' + '' + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + $.getMessage('Prefix') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('PartNumber') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('Description') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:5px;">' + '' + '</th></tr>';
                                        for (var i = 0; i < SupersessionData.NewPartDetail.length; i++) {

                                            NewPartDetail = NewPartDetail + '<tr><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;text-align:center;">' + SupersessionData.NewPartDetail[i].Select + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + SupersessionData.NewPartDetail[i].PartPrefix + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + SupersessionData.NewPartDetail[i].PartsNumber + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + (SupersessionData.NewPartDetail[i].PartsDescription == null ? '' : SupersessionData.NewPartDetail[i].PartsDescription) + '</td><td style="border:1px solid lightgrey;text-align:center;padding:3px 3px 3px 3px;width:5px;">' + SupersessionData.NewPartDetail[i].IsSuperseeded + '</td></tr>';
                                        }
                                        NewPartDetail = NewPartDetail + '</table>'
                                    }

                                    Detail = Detail + NewPartDetail;
                                    Detail = Detail + '</td></tr></table>'
                                    $('#' + SupersessionID).append(Detail);

                                    var Footer = '<table style="width:100%;"><tr><td style="width:50%;text-align:left;">' + spanSuperseesionInfo + '</td><td style="width:50%;text-align:right;"><input id="BtnSupersessionPopUpCancel" value=' + $.getMessage('Cancel') + ' type="button"  class="SaveBtn" /></td></tr></table>'

                                    $('#' + SupersessionID).append(Footer);


                                    $('#' + SupersessionID).bPopup({ modalClose: false, opacity: 0.6, positionStyle: 'fixed', speed: 450, transition: 'slideDown' });

                                    $('.SupersessionPopUp').click(function () {
                                        try {
                                            if ($(this).attr('IsSuperseeded') == 1) {
                                                $.LoadSupersessionDetails($(this).attr('id'), '');
                                            }
                                            else {
                                                $('#' + SupersessionID).bPopup().close()
                                                txt = $(this).attr('PartNumber');

                                                var extra = "?Value=" + $.EncryptString(txt) + '&Key=' + $(this).attr('PartID');
                                                if (extraGrdPars != null && extraGrdPars != undefined) {
                                                    jQuery.each(extraGrdPars, function (key, val) {

                                                        switch (val) {
                                                            case 'check':
                                                                extra = extra.concat("&" + key + "=" + ($(grd.getCell(rowid, key)).attr('checked') == 'checked' ? true : false));
                                                                break;
                                                            case 'text':
                                                                extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                                break;
                                                            case 'select':
                                                                extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                                break;
                                                            default:
                                                                extra = extra.concat("&" + key + "=" + grd.getCell(rowid, key));
                                                                break;
                                                        }
                                                    });
                                                }

                                                if (checkDuplicate != undefined && checkDuplicate) {
                                                    extra = extra + "&mode=" + (grd.getCell(rowid, primCol) == '' ? "add" : "edit") + "&primID=" + grd.getCell(rowid, primCol);
                                                }

                                                $.ajax({
                                                    url: url + extra + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                                                    async: false,
                                                    cached: false,
                                                    type: 'POST',
                                                    datatype: 'json',
                                                    success: function (data) {                                                        
                                                        //-----------
                                                        if (data.TaxStructureList != undefined) {
                                                            var taxstrid = $(grd.getCell(rowid, 'TaxStructure')).attr('id');
                                                            $.ClearDropDown(taxstrid);
                                                            $('#' + taxstrid).LoadOptions(data.TaxStructureList, 'TaxStructure_Name', 'TaxStructure_ID');
                                                            if (data.TaxStructureList.length == 1) {
                                                                $('#' + taxstrid).attr('value', data.TaxStructureList[0].TaxStructure_ID);
                                                                $('#' + taxstrid).change();
                                                            }
                                                        }
                                                        if ($(data).attr(TxtMap) == '') {
                                                            jQuery.each(ColMap, function (key, val) {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).attr('value', '');
                                                                if (key == "Rate") {
                                                                    $('#' + hdn).change();
                                                                }
                                                            });

                                                            $('#' + Id).attr('value', txt);
                                                            document.getElementById(Id).value = "";
                                                            alert($.getMessage('Invalid') + ' ' + friendlyColName);
                                                            txt = "";
                                                            $('#' + Id).attr('value', txt);
                                                        }
                                                        else if (data.IsActive == false) {
                                                            jQuery.each(ColMap, function (key, val) {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).attr('value', '');
                                                                if (key == "Rate") {
                                                                    $('#' + hdn).change();
                                                                }
                                                            });

                                                            $('#' + Id).attr('value', txt);
                                                            document.getElementById(Id).value = "";
                                                            alert($.getMessage('Inactive') + ' ' + friendlyColName);
                                                        }
                                                        else {
                                                            jQuery.each(ColMap, function (key, val) {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).attr('value', $(data).attr(val));
                                                                //if (hdn.indexOf('_Rate') > -1 ) {
                                                                //    if (Math.abs($(data).attr(val)) > 0) {
                                                                //        $('#' + hdn).attr('readonly', 'readonly');
                                                                //        $('#' + hdn).attr("onkeydown", "return false");
                                                                //        $('#' + hdn).attr('class', 'ReadOnly');
                                                                //    }
                                                                //    else {
                                                                //        $('#' + hdn).removeAttr('readonly');
                                                                //        $('#' + hdn).removeAttr("onkeydown");
                                                                //        $('#' + hdn).removeAttr('class');
                                                                //    }
                                                                //}
                                                            });

                                                            $('#' + Id).attr('value', $(data).attr(TxtMap));

                                                            if (checkDuplicate != undefined && checkDuplicate) {
                                                                var rowIDS = grd.jqGrid('getDataIDs');
                                                                var txtValue = document.getElementById($(grd.getCell(rowid, DupCol)).attr('id')).value;
                                                                var isexist = false;
                                                                for (i = 0; i < rowIDS.length; i++) {
                                                                    var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');
                                                                    if (editmode != 'false' && rowIDS[i] != rowid) {
                                                                        var colValue = document.getElementById($(grd.getCell(rowIDS[i], DupCol)).attr('id')).value;
                                                                        if (txtValue == colValue) {
                                                                            isexist = true;
                                                                            $('#' + Id).attr('value', '');
                                                                            $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                            jQuery.each(ColMap, function (key, val) {
                                                                                if (key != "IsReWork") {
                                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                    $('#' + hdn).attr('value', '');
                                                                                    if (key == "Rate") {
                                                                                        $('#' + hdn).change();
                                                                                    }
                                                                                }
                                                                                else {
                                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                    $('#' + hdn).attr('checked', false);
                                                                                }
                                                                            });

                                                                            alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                            return;
                                                                        }
                                                                    }
                                                                }

                                                                if (data.IsExists && !isexist) {

                                                                    $('#' + Id).attr('value', '');
                                                                    $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                    jQuery.each(ColMap, function (key, val) {
                                                                        if (key != "IsReWork") {
                                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                            $('#' + hdn).attr('value', '');
                                                                            if (key == "Rate") {
                                                                                $('#' + hdn).change();
                                                                            }
                                                                        }
                                                                        else {
                                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                            $('#' + hdn).attr('checked', false);
                                                                        }
                                                                    });
                                                                    alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                }
                                                            }

                                                            if (events != undefined && events != null) {
                                                                jQuery.each(events, function (key, evt) {
                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                    $('#' + hdn).trigger(evt);
                                                                });
                                                            }
                                                        }
                                                    },
                                                    error: function (e, d, f) {
                                                        alert("");
                                                    }
                                                });

                                            }

                                        } catch (e) {

                                        }
                                    });

                                    $('#BtnSupersessionPopUpCancel').click(function () {
                                        try {
                                            //$('#' + SupersessionID).dialog('close');
                                            $('#' + SupersessionID).bPopup().close();
                                        } catch (e) {

                                        }
                                    });
                                }
                                else {

                                    var extra = "?Value=" + $.EncryptString(SupersessionData.PartNumber) + '&Key=' + SupersessionData.Parts_ID;

                                    if (extraGrdPars != null && extraGrdPars != undefined) {

                                        jQuery.each(extraGrdPars, function (key, val) {

                                            switch (val) {
                                                case 'check':
                                                    extra = extra.concat("&" + key + "=" + ($(grd.getCell(rowid, key)).attr('checked') == 'checked' ? true : false));
                                                    break;
                                                case 'text':
                                                    extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                    break;
                                                case 'select':
                                                    extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                    break;
                                                default:
                                                    extra = extra.concat("&" + key + "=" + grd.getCell(rowid, key));
                                                    break;
                                            }
                                        });
                                    }

                                    if (checkDuplicate != undefined && checkDuplicate) {
                                        extra = extra + "&mode=" + (grd.getCell(rowid, primCol) == '' ? "add" : "edit") + "&primID=" + grd.getCell(rowid, primCol);
                                    }

                                    $.ajax({
                                        url: url + extra + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                                        async: false,
                                        cached: false,
                                        type: 'POST',
                                        datatype: 'json',
                                        success: function (data) {
                                            if (data.TaxStructureList != undefined) {
                                                var taxstrid = $(grd.getCell(rowid, 'TaxStructure')).attr('id');
                                                $.ClearDropDown(taxstrid);
                                                $('#' + taxstrid).LoadOptions(data.TaxStructureList, 'TaxStructure_Name', 'TaxStructure_ID');
                                                if (data.TaxStructureList.length == 1) {
                                                    $('#' + taxstrid).attr('value', data.TaxStructureList[0].TaxStructure_ID);
                                                    $('#' + taxstrid).change();
                                                }
                                            }
                                            if ($(data).attr(TxtMap) == '') {
                                                jQuery.each(ColMap, function (key, val) {
                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                    $('#' + hdn).attr('value', '');

                                                    if (key == "Rate") {
                                                        $('#' + hdn).change();
                                                    }
                                                });

                                                var taxstr = $(grd.getCell(rowid, 'TaxStructure')).attr('id');
                                                $.ClearDropDown(taxstr);

                                                $('#' + Id).attr('value', txt);
                                                document.getElementById(Id).value = "";
                                                alert($.getMessage('Invalid') + ' ' + friendlyColName);
                                                txt = "";
                                                $('#' + Id).attr('value', txt);
                                            }
                                            else if (data.IsActive == false) {
                                                jQuery.each(ColMap, function (key, val) {
                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                    $('#' + hdn).attr('value', '');
                                                    if (key == "Rate") {
                                                        $('#' + hdn).change();
                                                    }
                                                });

                                                var taxstr = $(grd.getCell(rowid, 'TaxStructure')).attr('id');
                                                $.ClearDropDown(taxstr);

                                                $('#' + Id).attr('value', txt);
                                                document.getElementById(Id).value = "";
                                                alert($.getMessage('Inactive') + ' ' + friendlyColName);
                                            }
                                            else {
                                                jQuery.each(ColMap, function (key, val) {
                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                    $('#' + hdn).attr('value', $(data).attr(val));

                                                    //if (hdn.indexOf('_Rate') > -1) {
                                                    //    if (Math.abs($(data).attr(val)) > 0) {
                                                    //        $('#' + hdn).attr('readonly', 'readonly');
                                                    //        $('#' + hdn).attr("onkeydown", "return false");
                                                    //        $('#' + hdn).attr('class', 'ReadOnly');
                                                    //    }
                                                    //    else {
                                                    //        $('#' + hdn).removeAttr('readonly');
                                                    //        $('#' + hdn).removeAttr("onkeydown");
                                                    //        $('#' + hdn).removeAttr('class');
                                                    //    }
                                                    //}
                                                });

                                                $('#' + Id).attr('value', $(data).attr(TxtMap));


                                                if (checkDuplicate != undefined && checkDuplicate) {
                                                    var rowIDS = grd.jqGrid('getDataIDs');
                                                    var txtValue = document.getElementById($(grd.getCell(rowid, DupCol)).attr('id')).value;
                                                    var isexist = false;
                                                    for (i = 0; i < rowIDS.length; i++) {
                                                        var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');
                                                        if (editmode != 'false' && rowIDS[i] != rowid) {
                                                            var colValue = document.getElementById($(grd.getCell(rowIDS[i], DupCol)).attr('id')).value;
                                                            if (txtValue == colValue) {
                                                                isexist = true;
                                                                $('#' + Id).attr('value', '');
                                                                $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                jQuery.each(ColMap, function (key, val) {
                                                                    if (key != "IsReWork") {
                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                        $('#' + hdn).attr('value', '');
                                                                        if (key == "Rate") {
                                                                            $('#' + hdn).change();
                                                                        }
                                                                    }
                                                                    else {
                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                        $('#' + hdn).attr('checked', false);
                                                                    }
                                                                });

                                                                var taxstr = $(grd.getCell(rowid, 'TaxStructure')).attr('id');
                                                                $.ClearDropDown(taxstr);

                                                                alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                return;
                                                            }
                                                        }
                                                    }

                                                    if (data.IsExists && !isexist) {

                                                        $('#' + Id).attr('value', '');
                                                        $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                        jQuery.each(ColMap, function (key, val) {
                                                            if (key != "IsReWork") {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).attr('value', '');
                                                                if (key == "Rate") {
                                                                    $('#' + hdn).change();
                                                                }
                                                            }
                                                            else {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).attr('checked', false);
                                                            }
                                                        });

                                                        var taxstr = $(grd.getCell(rowid, 'TaxStructure')).attr('id');
                                                        $.ClearDropDown(taxstr);

                                                        alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                    }
                                                }

                                                if (events != undefined && events != null) {
                                                    jQuery.each(events, function (key, evt) {
                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                        $('#' + hdn).trigger(evt);
                                                    });
                                                }
                                            }
                                        },
                                        error: function (e, d, f) {
                                            alert("");
                                        }
                                    });
                                }
                            }
                        });

                    };

                    var SPartNumber = this.txt;
                    $.ajax({
                        url: urlPrefix + '?Part_Number=' + $.EncryptString(SPartNumber) + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                        type: 'POST',
                        async: false,
                        cached: false,
                        datatype: 'json',
                        success: function (PrefixData) {
                            if (PrefixData.IsMultiPrefixPart) {

                                var urlPrefixC = urlPrefix + '?Part_Number=' + $.EncryptString(txt) + (Obj.pars != undefined ? Obj.pars.call(this) : '')

                                $.ajax({
                                    url: urlPrefixC,
                                    async: false,
                                    cached: false,
                                    type: 'POST',
                                    datatype: 'json',
                                    success: function (PrefixDataData) {
                                        if (PrefixDataData.IsMultiPrefixPart) {
                                            $('#' + PrefixID)[0].innerHTML = '';
                                            var PrefixDetail = '';

                                            if (PrefixDataData.PartDetail.length > 0) {

                                                PrefixDetail = '<span class="button b-close"><span>X</span></span><table cellpadding="3px" cellspacing="0px" style="border:1px solid lightgrey;">';
                                                PrefixDetail = PrefixDetail + '<tr><td colspan="4" style="width:100%;text-align: center;" class="ui-widget-header">' + $.getMessage('PartDetails') + '</td></tr>';
                                                PrefixDetail = PrefixDetail + '<tr><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;">' + '' + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + $.getMessage('Prefix') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('PartNumber') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('Description') + '</th></tr>';

                                                for (var i = 0; i < PrefixDataData.PartDetail.length; i++) {

                                                    PrefixDetail = PrefixDetail + '<tr><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;text-align:left;">' + PrefixDataData.PartDetail[i].Select + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;text-align:left;">' + PrefixDataData.PartDetail[i].PartPrefix + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:200px;text-align:left;">' + PrefixDataData.PartDetail[i].PartsNumber + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:200px;text-align:left;">' + PrefixDataData.PartDetail[i].PartsDescription + '</td></tr>';
                                                }
                                                PrefixDetail = PrefixDetail + '</table>'
                                            }

                                            $('#' + PrefixID).append(PrefixDetail);

                                            // $('#' + PrefixID).dialog({modal: true, position: 'center', width: '50%', title: 'Prefix'});
                                            $('#' + PrefixID).bPopup({ modalClose: false, opacity: 0.6, positionStyle: 'fixed', speed: 450, transition: 'slideDown' });
                                            $('.PrefixPopUp').click(function () {
                                                isSelect = true;
                                                if ($(this).attr('IsSuperseeded') == 1) {
                                                    $('#' + PrefixID).bPopup().close()
                                                    $.LoadSupersessionDetails($(this).attr('PartID'), '');
                                                    $('#' + Id).focus();
                                                }
                                                else {
                                                    $('#' + PrefixID).bPopup().close()
                                                    $('#' + Id).focus();
                                                    txt = $(this).attr('PartNumber');

                                                    var extra = "?Value=" + $.EncryptString(txt) + '&Key=' + $(this).attr('PartID');

                                                    if (extraGrdPars != null && extraGrdPars != undefined) {

                                                        jQuery.each(extraGrdPars, function (key, val) {

                                                            switch (val) {
                                                                case 'check':
                                                                    extra = extra.concat("&" + key + "=" + ($(grd.getCell(rowid, key)).attr('checked') == 'checked' ? true : false));
                                                                    break;
                                                                case 'text':
                                                                    extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                                    break;
                                                                case 'select':
                                                                    extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                                    break;
                                                                default:
                                                                    extra = extra.concat("&" + key + "=" + grd.getCell(rowid, key));
                                                                    break;
                                                            }
                                                        });
                                                    }

                                                    if (checkDuplicate != undefined && checkDuplicate) {
                                                        extra = extra + "&mode=" + (grd.getCell(rowid, primCol) == '' ? "add" : "edit") + "&primID=" + grd.getCell(rowid, primCol);
                                                    }

                                                    $.ajax({
                                                        url: url + extra + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                                                        async: false,
                                                        cached: false,
                                                        type: 'POST',
                                                        datatype: 'json',
                                                        success: function (data) {
                                                            if (data.TaxStructureList != undefined) {
                                                                var taxstrid = $(grd.getCell(rowid, 'TaxStructure')).attr('id');
                                                                $.ClearDropDown(taxstrid);
                                                                $('#' + taxstrid).LoadOptions(data.TaxStructureList, 'TaxStructure_Name', 'TaxStructure_ID');
                                                                if (data.TaxStructureList.length == 1) {
                                                                    $('#' + taxstrid).attr('value', data.TaxStructureList[0].TaxStructure_ID);
                                                                    $('#' + taxstrid).change();
                                                                }
                                                            }
                                                            if ($(data).attr(TxtMap) == '') {
                                                                jQuery.each(ColMap, function (key, val) {
                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                    $('#' + hdn).attr('value', '');
                                                                    if (key == "Rate") {
                                                                        $('#' + hdn).change();
                                                                    }
                                                                });

                                                                $('#' + Id).attr('value', txt);
                                                                document.getElementById(Id).value = "";

                                                                var taxstr = $(grd.getCell(rowid, 'TaxStructure')).attr('id');
                                                                $.ClearDropDown(taxstr);

                                                                alert($.getMessage('Invalid') + ' ' + friendlyColName);
                                                            }
                                                            else if (data.IsActive == false) {
                                                                jQuery.each(ColMap, function (key, val) {
                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                    $('#' + hdn).attr('value', '');
                                                                    if (key == "Rate") {
                                                                        $('#' + hdn).change();
                                                                    }
                                                                });

                                                                $('#' + Id).attr('value', txt);
                                                                document.getElementById(Id).value = "";

                                                                var taxstr = $(grd.getCell(rowid, 'TaxStructure')).attr('id');
                                                                $.ClearDropDown(taxstr);

                                                                alert($.getMessage('Inactive') + ' ' + friendlyColName);
                                                            }
                                                            else {
                                                                jQuery.each(ColMap, function (key, val) {
                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                    $('#' + hdn).attr('value', $(data).attr(val));

                                                                    //if (hdn.indexOf('_Rate') > -1) {
                                                                    //    if (Math.abs($(data).attr(val)) > 0) {
                                                                    //        $('#' + hdn).attr('readonly', 'readonly');
                                                                    //        $('#' + hdn).attr("onkeydown", "return false");
                                                                    //        $('#' + hdn).attr('class', 'ReadOnly');
                                                                    //    }
                                                                    //    else {
                                                                    //        $('#' + hdn).removeAttr('readonly');
                                                                    //        $('#' + hdn).removeAttr("onkeydown");
                                                                    //        $('#' + hdn).removeAttr('class');
                                                                    //    }
                                                                    //}
                                                                });

                                                                $('#' + Id).attr('value', $(data).attr(TxtMap));
                                                                $('#' + Id).focus();

                                                                if (checkDuplicate != undefined && checkDuplicate) {
                                                                    var rowIDS = grd.jqGrid('getDataIDs');
                                                                    var txtValue = document.getElementById($(grd.getCell(rowid, DupCol)).attr('id')).value;
                                                                    var isexist = false;
                                                                    for (i = 0; i < rowIDS.length; i++) {
                                                                        var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');
                                                                        if (editmode != 'false' && rowIDS[i] != rowid) {
                                                                            var colValue = document.getElementById($(grd.getCell(rowIDS[i], DupCol)).attr('id')).value;
                                                                            if (txtValue == colValue) {
                                                                                isexist = true;
                                                                                $('#' + Id).attr('value', '');
                                                                                $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                                jQuery.each(ColMap, function (key, val) {
                                                                                    if (key != "IsReWork") {
                                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                        $('#' + hdn).attr('value', '');
                                                                                        if (key == "Rate") {
                                                                                            $('#' + hdn).change();
                                                                                        }
                                                                                    }
                                                                                    else {
                                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                        $('#' + hdn).attr('checked', false);
                                                                                    }
                                                                                });

                                                                                var taxstr = $(grd.getCell(rowid, 'TaxStructure')).attr('id');
                                                                                $.ClearDropDown(taxstr);

                                                                                alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                                return;
                                                                            }
                                                                        }
                                                                    }

                                                                    if (data.IsExists && !isexist) {

                                                                        $('#' + Id).attr('value', '');
                                                                        $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                        jQuery.each(ColMap, function (key, val) {
                                                                            if (key != "IsReWork") {
                                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                $('#' + hdn).attr('value', '');
                                                                                if (key == "Rate") {
                                                                                    $('#' + hdn).change();
                                                                                }
                                                                            }
                                                                            else {
                                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                $('#' + hdn).attr('checked', false);
                                                                            }
                                                                        });

                                                                        var taxstr = $(grd.getCell(rowid, 'TaxStructure')).attr('id');
                                                                        $.ClearDropDown(taxstr);

                                                                        alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                    }
                                                                }

                                                                if (events != undefined && events != null) {
                                                                    jQuery.each(events, function (key, evt) {
                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                        $('#' + hdn).trigger(evt);
                                                                    });
                                                                }
                                                            }
                                                        },
                                                        error: function (e, d, f) {
                                                            alert("");
                                                        }
                                                    });


                                                }
                                            });
                                        }
                                        else {
                                            txt = PrefixDataData.PartNumber;
                                            var extra = "?Value=" + $.EncryptString(txt) + '&Key=' + PrefixDataData.Parts_ID;
                                            if (extraGrdPars != null && extraGrdPars != undefined) {
                                                jQuery.each(extraGrdPars, function (key, val) {
                                                    switch (val) {
                                                        case 'check':
                                                            extra = extra.concat("&" + key + "=" + ($(grd.getCell(rowid, key)).attr('checked') == 'checked' ? true : false));
                                                            break;
                                                        case 'text':
                                                            extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                            break;
                                                        case 'select':
                                                            extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                            break;
                                                        default:
                                                            extra = extra.concat("&" + key + "=" + grd.getCell(rowid, key));
                                                            break;
                                                    }
                                                });
                                            }

                                            if (checkDuplicate != undefined && checkDuplicate) {
                                                extra = extra + "&mode=" + (grd.getCell(rowid, primCol) == '' ? "add" : "edit") + "&primID=" + grd.getCell(rowid, primCol);
                                            }
                                            $.ajax({
                                                url: url + extra + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                                                type: 'POST',
                                                async: false,
                                                cached: false,
                                                datatype: 'json',
                                                success: function (data) {

                                                    if ($(data).attr(TxtMap) == '') {
                                                        jQuery.each(ColMap, function (key, val) {
                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                            $('#' + hdn).attr('value', '');
                                                            if (key == "Rate") {
                                                                $('#' + hdn).change();
                                                            }
                                                        });

                                                        $('#' + Id).attr('value', txt);
                                                        document.getElementById(Id).value = "";

                                                        var taxstr = $(grd.getCell(rowid, 'TaxStructure')).attr('id');
                                                        $.ClearDropDown(taxstr);

                                                        alert($.getMessage('Invalid') + ' ' + friendlyColName);
                                                    }
                                                    else if (data.IsActive == false) {
                                                        jQuery.each(ColMap, function (key, val) {
                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                            $('#' + hdn).attr('value', '');
                                                            if (key == "Rate") {
                                                                $('#' + hdn).change();
                                                            }
                                                        });

                                                        $('#' + Id).attr('value', txt);
                                                        document.getElementById(Id).value = "";

                                                        var taxstr = $(grd.getCell(rowid, 'TaxStructure')).attr('id');
                                                        $.ClearDropDown(taxstr);

                                                        alert($.getMessage('Inactive') + ' ' + friendlyColName);
                                                    }
                                                    else {
                                                        jQuery.each(ColMap, function (key, val) {
                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                            $('#' + hdn).attr('value', $(data).attr(val));
                                                            //if (hdn.indexOf('_Rate') > -1) {
                                                            //    if (Math.abs($(data).attr(val)) > 0) {
                                                            //        $('#' + hdn).attr('readonly', 'readonly');
                                                            //        $('#' + hdn).attr("onkeydown", "return false");
                                                            //        $('#' + hdn).attr('class', 'ReadOnly');
                                                            //    }
                                                            //    else {
                                                            //        $('#' + hdn).removeAttr('readonly');
                                                            //        $('#' + hdn).removeAttr("onkeydown");
                                                            //        $('#' + hdn).removeAttr('class');
                                                            //    }
                                                            //}
                                                        });
                                                        $('#' + Id).attr('value', $(data).attr(TxtMap));
                                                        $('#' + Id).focus();
                                                        if (checkDuplicate != undefined && checkDuplicate) {
                                                            var rowIDS = grd.jqGrid('getDataIDs');
                                                            var txtValue = document.getElementById($(grd.getCell(rowid, DupCol)).attr('id')).value;
                                                            var isexist = false;
                                                            for (i = 0; i < rowIDS.length; i++) {
                                                                var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');
                                                                if (editmode != 'false' && rowIDS[i] != rowid) {
                                                                    var colValue = document.getElementById($(grd.getCell(rowIDS[i], DupCol)).attr('id')).value;
                                                                    if (txtValue == colValue) {
                                                                        isexist = true;
                                                                        $('#' + Id).attr('value', '');
                                                                        $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                        jQuery.each(ColMap, function (key, val) {
                                                                            if (key != "IsReWork") {
                                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                $('#' + hdn).attr('value', '');
                                                                                if (key == "Rate") {
                                                                                    $('#' + hdn).change();
                                                                                }
                                                                            }
                                                                            else {
                                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                $('#' + hdn).attr('checked', false);
                                                                            }
                                                                        });

                                                                        var taxstr = $(grd.getCell(rowid, 'TaxStructure')).attr('id');
                                                                        $.ClearDropDown(taxstr);

                                                                        alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                        return;
                                                                    }
                                                                }
                                                            }

                                                            if (data.IsExists && !isexist) {

                                                                $('#' + Id).attr('value', '');
                                                                $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                jQuery.each(ColMap, function (key, val) {
                                                                    if (key != "IsReWork") {
                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                        $('#' + hdn).attr('value', '');
                                                                        if (key == "Rate") {
                                                                            $('#' + hdn).change();
                                                                        }
                                                                    }
                                                                    else {
                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                        $('#' + hdn).attr('checked', false);
                                                                    }
                                                                });

                                                                var taxstr = $(grd.getCell(rowid, 'TaxStructure')).attr('id');
                                                                $.ClearDropDown(taxstr);

                                                                alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                            }
                                                        }

                                                        if (events != undefined && events != null) {
                                                            jQuery.each(events, function (key, evt) {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).trigger(evt);
                                                            });
                                                        }
                                                    }
                                                },
                                                error: function (e, d, f) {
                                                    alert("");
                                                }
                                            });


                                        }
                                    }
                                });



                            }
                            else {
                                $.LoadSupersessionDetails(0, $.EncryptString(SPartNumber));
                                $('#' + Id).focus();
                            }
                        }
                    });
                }
            }
        }
    },
    {
        type: 'drop', fn: function () {
            return false;
        }
    }];
    jQuery.each(ColMap, function (key, val) {

        if (key != 'IsReWork') {
            grd.setColProp(key, { editable: true, edittype: 'text' });
        }
        else if (key == 'IsReWork') {
            grd.setColProp(key, { editable: true, edittype: 'checkbox' });
        }
    });

    this.fld = [{
        type: 'mousedown', fn: function (e) {

            IsLens = true;
            this.grd = grd;
            this.rowid = $(this).parent('td').parent('tr').attr('id');
            var rowid = this.rowid;

            this.txtBx = $(this.grd.getCell(this.rowid, TxtCol));
            this.Id = this.txtBx.attr('id');
            this.txtId = $(this.grd.getCell(this.rowid, IdField));
            this.txtId = this.txtId.attr('id');

            $("#" + FldSrchID).FieldSearch("Search", { ResultTextField: this.Id, ResultValueField: this.txtId, headerNames: { id: headerid, Name: headername }, ExtraParam: (Obj.pars != undefined ? Obj.pars.call(this) : '') });
            IsLens = false;


        }
    }];
    grd.setColProp(TxtCol, { editable: true, editoptions: { dataEvents: this.evnts } });

    if (IsFieldSearch != undefined) {
        if (IsFieldSearch) {

            $("#" + FldSrchID).FieldSearch({ url: FldUrl, colModels: colModels, colName: colName, FieldSearchName: FieldSearchName, DefaultSortColName: DefaultSortColName });
            grd.setColProp(SrchCol, { editable: true, edittype: 'image', editoptions: { dataEvents: this.fld, src: $.url('/Content/SearchLens.jpg') } });
        }
    }
    else {
        grd.setColProp(SrchCol, { editable: true, edittype: 'image', editoptions: { src: $.url('/Content/SearchLens.jpg') } });
    }

}

//--------------------------------------------------------------//

//Used For Prefix Details Display During Part Search(No Supersession)
$.fn.GridInlinePrefixPartSearch = function (Obj) {
    var grd = $(this);
    var TxtCol = Obj.TxtCol;
    var TxtMap = Obj.TxtMap;
    var SrchCol = Obj.SrchCol;
    var url = Obj.url;
    var urlPrefix = Obj.urlPrefix;
    var urlSupersession = Obj.urlSupersession;
    var ColMap = Obj.ColMap;
    var extraGrdPars = Obj.extraGrdPars;
    var IsFieldSearch = Obj.IsFieldSearch;
    var IdField = undefined;
    var FldUrl = undefined;
    var FldSrchID = undefined;
    var SupersessionID = undefined;
    var PrefixID = undefined;
    var headerid = undefined;
    var headername = undefined;
    var events = Obj.events;
    var colModels = [];
    var colName = [];
    var FieldSearchName = undefined;
    var DefaultSortColName = undefined;

    if (IsFieldSearch != undefined && Obj.FieldSearch != undefined) {
        if (IsFieldSearch) {
            IdField = Obj.FieldSearch.IdField;
            FldUrl = Obj.FieldSearch.url;
            FldSrchID = Obj.FieldSearch.FldSrchID;
            SupersessionID = Obj.FieldSearch.SupersessionID;
            PrefixID = Obj.FieldSearch.PrefixID;
            headerid = Obj.FieldSearch.headerid == undefined ? '' : Obj.FieldSearch.headerid;
            headername = Obj.FieldSearch.headername == undefined ? '' : Obj.FieldSearch.headername;
            colModels = Obj.FieldSearch.colModels == undefined ? '' : Obj.FieldSearch.colModels;
            colName = Obj.FieldSearch.colName == undefined ? '' : Obj.FieldSearch.colName;
            FieldSearchName = Obj.FieldSearch.FieldSearchName == undefined ? '' : Obj.FieldSearch.FieldSearchName;
            DefaultSortColName = Obj.FieldSearch.DefaultSortColName == undefined ? '' : Obj.FieldSearch.DefaultSortColName;

        }
    }
    var checkDuplicate = Obj.checkDuplicate;
    var DupCol = '';
    var friendlyColName = '';
    var primCol = '';
    if (checkDuplicate) {
        DupCol = Obj.DupCol.ColName;
        friendlyColName = Obj.DupCol.friendlyColName;
        primCol = Obj.DupCol.primCol;
    }

    this.evnts = [{
        type: 'change', fn: function (e) {
            if (!IsLens) {

                this.grd = grd;
                this.rowid = $(this).parent('td').parent('tr').attr('id');
                var rowid = this.rowid;

                this.txtBx = $(this.grd.getCell(this.rowid, TxtCol));
                this.Id = this.txtBx.attr('id');
                var Id = this.Id;

                this.txt = document.getElementById(this.Id).value;
                this.value = '';
                var txt = this.txt;
                $('#' + Id).attr('duplicate', 'false');
                $('#' + Id).attr('exists', 'false');
                if (this.txt == '') {

                    jQuery.each(ColMap, function (key, val) {
                        if (key != "IsReWork") {
                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                            $('#' + hdn).attr('value', '');
                            if (key == "Rate") {
                                $('#' + hdn).change();
                            }
                        }
                        else {
                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                            $('#' + hdn).attr('checked', $(data).attr(val));
                        }
                    });

                    $('#' + Id).attr('value', '');
                }
                else {

                    var SPartNumber = this.txt;
                    $.ajax({
                        url: urlPrefix + '?Part_Number=' + $.EncryptString(SPartNumber) + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                        type: 'POST',
                        async: false,
                        cached: false,
                        datatype: 'json',
                        success: function (PrefixData) {

                            if (PrefixData.IsMultiPrefixPart) {

                                var urlPrefixC = urlPrefix + '?Part_Number=' + $.EncryptString(txt) + (Obj.pars != undefined ? Obj.pars.call(this) : '')

                                $.ajax({
                                    url: urlPrefixC,
                                    async: false,
                                    cached: false,
                                    type: 'POST',
                                    datatype: 'json',
                                    success: function (PrefixDataData) {

                                        if (PrefixDataData.IsMultiPrefixPart) {
                                            $('#' + PrefixID)[0].innerHTML = '';
                                            var PrefixDetail = '';

                                            if (PrefixDataData.PartDetail.length > 0) {

                                                PrefixDetail = '<span class="button b-close"><span>X</span></span><table cellpadding="3px" cellspacing="0px" style="border:1px solid lightgrey;">';
                                                PrefixDetail = PrefixDetail + '<tr><td colspan="4" style="width:100%;text-align: center;" class="ui-widget-header">' + $.getMessage('PartDetails') + '</td></tr>';
                                                PrefixDetail = PrefixDetail + '<tr><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;">' + '' + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:50px;">' + $.getMessage('Prefix') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('PartNumber') + '</th><th style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:100px;">' + $.getMessage('Description') + '</th></tr>';

                                                for (var i = 0; i < PrefixDataData.PartDetail.length; i++) {

                                                    PrefixDetail = PrefixDetail + '<tr><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;text-align:left;">' + PrefixDataData.PartDetail[i].Select + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:25px;text-align:left;">' + PrefixDataData.PartDetail[i].PartPrefix + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:200px;text-align:left;">' + PrefixDataData.PartDetail[i].PartsNumber + '</td><td style="border:1px solid lightgrey;padding:3px 3px 3px 3px;width:200px;text-align:left;">' + PrefixDataData.PartDetail[i].PartsDescription + '</td></tr>';
                                                }
                                                PrefixDetail = PrefixDetail + '</table>'
                                            }
                                            $('#' + PrefixID).append(PrefixDetail);

                                            $('#' + PrefixID).bPopup({ modalClose: false, opacity: 0.6, positionStyle: 'fixed', speed: 450, transition: 'slideDown' });
                                            $('.PrefixPopUp').click(function () {
                                                isSelect = true;

                                                $('#' + PrefixID).bPopup().close()

                                                txt = $(this).attr('PartNumber');

                                                var extra = "?Value=" + $.EncryptString(txt) + '&Key=' + $(this).attr('PartID');

                                                if (extraGrdPars != null && extraGrdPars != undefined) {

                                                    jQuery.each(extraGrdPars, function (key, val) {

                                                        switch (val) {
                                                            case 'check':
                                                                extra = extra.concat("&" + key + "=" + ($(grd.getCell(rowid, key)).attr('checked') == 'checked' ? true : false));
                                                                break;
                                                            case 'text':
                                                                extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                                break;
                                                            case 'select':
                                                                extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                                break;
                                                            default:
                                                                extra = extra.concat("&" + key + "=" + grd.getCell(rowid, key));
                                                                break;
                                                        }
                                                    });
                                                }

                                                if (checkDuplicate != undefined && checkDuplicate) {
                                                    extra = extra + "&mode=" + (grd.getCell(rowid, primCol) == '' ? "add" : "edit") + "&primID=" + grd.getCell(rowid, primCol);
                                                }

                                                $.ajax({
                                                    url: url + extra + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                                                    async: false,
                                                    cached: false,
                                                    type: 'POST',
                                                    datatype: 'json',
                                                    success: function (data) {

                                                        if ($(data).attr(TxtMap) == '') {
                                                            jQuery.each(ColMap, function (key, val) {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).attr('value', '');
                                                                if (key == "Rate") {
                                                                    $('#' + hdn).change();
                                                                }
                                                            });

                                                            $('#' + Id).attr('value', txt);
                                                            document.getElementById(Id).value = "";
                                                            alert($.getMessage('Invalid') + ' ' + friendlyColName + ' ' + $.getMessage('or').toLowerCase() + ' ' + friendlyColName + ' ' + $.getMessage('isinactive'));
                                                        }
                                                        else {
                                                            jQuery.each(ColMap, function (key, val) {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).attr('value', $(data).attr(val));
                                                            });

                                                            $('#' + Id).attr('value', $(data).attr(TxtMap));


                                                            if (checkDuplicate != undefined && checkDuplicate) {
                                                                var rowIDS = grd.jqGrid('getDataIDs');
                                                                var txtValue = document.getElementById($(grd.getCell(rowid, DupCol)).attr('id')).value;
                                                                var isexist = false;
                                                                for (i = 0; i < rowIDS.length; i++) {
                                                                    var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');
                                                                    if (editmode != 'false' && rowIDS[i] != rowid) {
                                                                        var colValue = document.getElementById($(grd.getCell(rowIDS[i], DupCol)).attr('id')).value;
                                                                        if (txtValue == colValue) {
                                                                            isexist = true;
                                                                            $('#' + Id).attr('value', '');
                                                                            $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                            jQuery.each(ColMap, function (key, val) {
                                                                                if (key != "IsReWork") {
                                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                    $('#' + hdn).attr('value', '');
                                                                                    if (key == "Rate") {
                                                                                        $('#' + hdn).change();
                                                                                    }
                                                                                }
                                                                                else {
                                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                    $('#' + hdn).attr('checked', false);
                                                                                }
                                                                            });

                                                                            alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                            return;
                                                                        }
                                                                    }
                                                                }

                                                                if (data.IsExists && !isexist) {

                                                                    $('#' + Id).attr('value', '');
                                                                    $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                    jQuery.each(ColMap, function (key, val) {
                                                                        if (key != "IsReWork") {
                                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                            $('#' + hdn).attr('value', '');
                                                                            if (key == "Rate") {
                                                                                $('#' + hdn).change();
                                                                            }
                                                                        }
                                                                        else {
                                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                            $('#' + hdn).attr('checked', false);
                                                                        }
                                                                    });
                                                                    alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                }
                                                            }

                                                            if (events != undefined && events != null) {
                                                                jQuery.each(events, function (key, evt) {
                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                    $('#' + hdn).trigger(evt);
                                                                });
                                                            }
                                                        }
                                                    },
                                                    error: function (e, d, f) {
                                                        alert("");
                                                    }
                                                });



                                            });
                                        }
                                        else {
                                            txt = PrefixDataData.PartNumber;
                                            var extra = "?Value=" + $.EncryptString(txt) + '&Key=' + PrefixDataData.Parts_ID;
                                            if (extraGrdPars != null && extraGrdPars != undefined) {
                                                jQuery.each(extraGrdPars, function (key, val) {
                                                    switch (val) {
                                                        case 'check':
                                                            extra = extra.concat("&" + key + "=" + ($(grd.getCell(rowid, key)).attr('checked') == 'checked' ? true : false));
                                                            break;
                                                        case 'text':
                                                            extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                            break;
                                                        case 'select':
                                                            extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                            break;
                                                        default:
                                                            extra = extra.concat("&" + key + "=" + grd.getCell(rowid, key));
                                                            break;
                                                    }
                                                });
                                            }

                                            if (checkDuplicate != undefined && checkDuplicate) {
                                                extra = extra + "&mode=" + (grd.getCell(rowid, primCol) == '' ? "add" : "edit") + "&primID=" + grd.getCell(rowid, primCol);
                                            }
                                            $.ajax({
                                                url: url + extra + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                                                type: 'POST',
                                                datatype: 'json',
                                                async: false,
                                                cached: false,
                                                success: function (data) {

                                                    if ($(data).attr(TxtMap) == '') {
                                                        jQuery.each(ColMap, function (key, val) {
                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                            $('#' + hdn).attr('value', '');
                                                            if (key == "Rate") {
                                                                $('#' + hdn).change();
                                                            }
                                                        });

                                                        $('#' + Id).attr('value', txt);
                                                        document.getElementById(Id).value = "";
                                                        alert($.getMessage('Invalid') + ' ' + friendlyColName + ' ' + $.getMessage('or').toLowerCase() + ' ' + friendlyColName + ' ' + $.getMessage('isinactive'));
                                                    }
                                                    else {
                                                        jQuery.each(ColMap, function (key, val) {
                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                            $('#' + hdn).attr('value', $(data).attr(val));
                                                        });
                                                        $('#' + Id).attr('value', $(data).attr(TxtMap));


                                                        if (checkDuplicate != undefined && checkDuplicate) {
                                                            var rowIDS = grd.jqGrid('getDataIDs');
                                                            var txtValue = document.getElementById($(grd.getCell(rowid, DupCol)).attr('id')).value;
                                                            var isexist = false;
                                                            for (i = 0; i < rowIDS.length; i++) {
                                                                var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');
                                                                if (editmode != 'false' && rowIDS[i] != rowid) {
                                                                    var colValue = document.getElementById($(grd.getCell(rowIDS[i], DupCol)).attr('id')).value;
                                                                    if (txtValue == colValue) {
                                                                        isexist = true;
                                                                        $('#' + Id).attr('value', '');
                                                                        $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                        jQuery.each(ColMap, function (key, val) {
                                                                            if (key != "IsReWork") {
                                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                $('#' + hdn).attr('value', '');
                                                                                if (key == "Rate") {
                                                                                    $('#' + hdn).change();
                                                                                }
                                                                            }
                                                                            else {
                                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                                $('#' + hdn).attr('checked', false);
                                                                            }
                                                                        });

                                                                        alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                                        return;
                                                                    }
                                                                }
                                                            }

                                                            if (data.IsExists && !isexist) {

                                                                $('#' + Id).attr('value', '');
                                                                $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                                jQuery.each(ColMap, function (key, val) {
                                                                    if (key != "IsReWork") {
                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                        $('#' + hdn).attr('value', '');
                                                                        if (key == "Rate") {
                                                                            $('#' + hdn).change();
                                                                        }
                                                                    }
                                                                    else {
                                                                        var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                        $('#' + hdn).attr('checked', false);
                                                                    }
                                                                });
                                                                alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                            }
                                                        }

                                                        if (events != undefined && events != null) {
                                                            jQuery.each(events, function (key, evt) {
                                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                $('#' + hdn).trigger(evt);
                                                            });
                                                        }
                                                    }
                                                },
                                                error: function (e, d, f) {
                                                    alert("");
                                                }
                                            });


                                        }
                                    }
                                });



                            }
                            else {
                                var extra = "?Value=" + $.EncryptString(PrefixData.PartNumber) + '&Key=' + PrefixData.Parts_ID;

                                if (extraGrdPars != null && extraGrdPars != undefined) {

                                    jQuery.each(extraGrdPars, function (key, val) {

                                        switch (val) {
                                            case 'check':
                                                extra = extra.concat("&" + key + "=" + ($(grd.getCell(rowid, key)).attr('checked') == 'checked' ? true : false));
                                                break;
                                            case 'text':
                                                extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                break;
                                            case 'select':
                                                extra = extra.concat("&" + key + "=" + $(grd.getCell(rowid, key)).attr('value'));
                                                break;
                                            default:
                                                extra = extra.concat("&" + key + "=" + grd.getCell(rowid, key));
                                                break;
                                        }
                                    });
                                }

                                if (checkDuplicate != undefined && checkDuplicate) {
                                    extra = extra + "&mode=" + (grd.getCell(rowid, primCol) == '' ? "add" : "edit") + "&primID=" + grd.getCell(rowid, primCol);
                                }

                                $.ajax({
                                    url: url + extra + (Obj.pars != undefined ? Obj.pars.call(this) : ''),
                                    async: false,
                                    cached: false,
                                    type: 'POST',
                                    datatype: 'json',
                                    success: function (data) {

                                        if ($(data).attr(TxtMap) == '') {
                                            jQuery.each(ColMap, function (key, val) {
                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                $('#' + hdn).attr('value', '');
                                                if (key == "Rate") {
                                                    $('#' + hdn).change();
                                                }
                                            });

                                            $('#' + Id).attr('value', txt);
                                            document.getElementById(Id).value = "";
                                            alert($.getMessage('Invalid') + ' ' + friendlyColName);
                                        }
                                        else {
                                            jQuery.each(ColMap, function (key, val) {

                                                var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                $('#' + hdn).attr('value', $(data).attr(val));
                                            });


                                            $('#' + Id).attr('value', $(data).attr(TxtMap));

                                            if (checkDuplicate != undefined && checkDuplicate) {

                                                var rowIDS = grd.jqGrid('getDataIDs');
                                                var txtValue = document.getElementById($(grd.getCell(rowid, DupCol)).attr('id')).value;
                                                var isexist = false;
                                                for (i = 0; i < rowIDS.length; i++) {
                                                    var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');
                                                    if (editmode != 'false' && rowIDS[i] != rowid) {
                                                        var colValue = document.getElementById($(grd.getCell(rowIDS[i], DupCol)).attr('id')).value;
                                                        if (txtValue == colValue) {
                                                            isexist = true;
                                                            $('#' + Id).attr('value', '');
                                                            $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                            jQuery.each(ColMap, function (key, val) {
                                                                if (key != "IsReWork") {
                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                    $('#' + hdn).attr('value', '');
                                                                    if (key == "Rate") {
                                                                        $('#' + hdn).change();
                                                                    }
                                                                }
                                                                else {
                                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                                    $('#' + hdn).attr('checked', false);
                                                                }
                                                            });

                                                            alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                            return;
                                                        }
                                                    }
                                                }

                                                if (data.IsExists && !isexist) {

                                                    $('#' + Id).attr('value', '');
                                                    $('#' + $(grd.getCell(rowid, DupCol)).attr('id')).attr('value', '');
                                                    jQuery.each(ColMap, function (key, val) {
                                                        if (key != "IsReWork") {
                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                            $('#' + hdn).attr('value', '');
                                                            if (key == "Rate") {
                                                                $('#' + hdn).change();
                                                            }
                                                        }
                                                        else {
                                                            var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                            $('#' + hdn).attr('checked', false);
                                                        }
                                                    });
                                                    alert($.getMessage('Duplicate') + ' ' + friendlyColName);
                                                }
                                            }

                                            if (events != undefined && events != null) {
                                                jQuery.each(events, function (key, evt) {
                                                    var hdn = $(grd.getCell(rowid, key)).attr('id');
                                                    $('#' + hdn).trigger(evt);
                                                });
                                            }
                                        }
                                    },
                                    error: function (e, d, f) {
                                        alert("");
                                    }
                                });

                            }
                        }
                    });
                }
            }
        }
    },
    {
        type: 'drop', fn: function () {
            return false;
        }
    }];
    jQuery.each(ColMap, function (key, val) {

        if (key != 'IsReWork') {
            grd.setColProp(key, { editable: true, edittype: 'text' });
        }
        else if (key == 'IsReWork') {
            grd.setColProp(key, { editable: true, edittype: 'checkbox' });
        }
    });

    this.fld = [{
        type: 'mousedown', fn: function (e) {

            IsLens = true;
            this.grd = grd;
            this.rowid = $(this).parent('td').parent('tr').attr('id');
            var rowid = this.rowid;

            this.txtBx = $(this.grd.getCell(this.rowid, TxtCol));
            this.Id = this.txtBx.attr('id');
            this.txtId = $(this.grd.getCell(this.rowid, IdField));
            this.txtId = this.txtId.attr('id');

            $("#" + FldSrchID).FieldSearch("Search", { ResultTextField: this.Id, ResultValueField: this.txtId, headerNames: { id: headerid, Name: headername }, ExtraParam: (Obj.pars != undefined ? Obj.pars.call(this) : '') });
            IsLens = false;


        }
    }];
    grd.setColProp(TxtCol, { editable: true, editoptions: { dataEvents: this.evnts } });

    if (IsFieldSearch != undefined) {
        if (IsFieldSearch) {

            $("#" + FldSrchID).FieldSearch({ url: FldUrl, colModels: colModels, colName: colName, FieldSearchName: FieldSearchName, DefaultSortColName: DefaultSortColName });
            grd.setColProp(SrchCol, { editable: true, edittype: 'image', editoptions: { dataEvents: this.fld, src: $.url('/Content/SearchLens.jpg') } });
        }
    }
    else {
        grd.setColProp(SrchCol, { editable: true, edittype: 'image', editoptions: { src: $.url('/Content/SearchLens.jpg') } });
    }

}


//Added by Ravi Begin
//Coded by Shashi:) To Show & Hide JQGrid Filter Toolbar based on grid data
var DataHistory = new Array();
$.ToggleFilterToolbar = function (grid_id) {
    
    try {

        $("#gview_" + grid_id + " .ui-search-toolbar").hide()
        if ($("#" + grid_id).jqGrid("getDataIDs").length > 0) {
            $("#gview_" + grid_id + " .ui-search-toolbar").show();
            if (DataHistory.indexOf(grid_id) == -1) DataHistory.push(grid_id);
        }
        else {
            if (DataHistory.indexOf(grid_id) == -1) $("#gview_" + grid_id + " .ui-search-toolbar").hide();
            else {
                $("#gview_" + grid_id + " .ui-search-toolbar").show();

            }
        }
    }
    catch (e) { }
}
$.DestoryFilterToolbar = function (gridArray) {
    
    try {

        for (var i = 0; i < gridArray.length; i++) {
            if (DataHistory.indexOf(gridArray[i]) != -1) {
                DataHistory.splice(DataHistory.indexOf(gridArray[i]), 1);
                $("#gview_" + gridArray[i] + " .ui-search-toolbar :text").attr("value", "");
                var f = "{ \"groupOp\": \"AND\", \"rules\": [] }";
                $("#" + gridArray[i]).setGridParam({ search: false, postData: { filters: f } });
            }
        }
    }
    catch (e) { }
}
//End

//Coded by Shashi for changing grid parameter with reload
//input: 1st parameter 0-cliendside. 1-json; 2nd parameter: true for reloadgrid; 3rd parameter for gridurl
$.fn.ChangeGridParam = function (type, reload, gridurl) {
    var grd = $(this);
    grd.setGridParam({ datatype: type == '0' ? 'clientSide' : 'json', });
    if (gridurl != undefined) grd.setGridParam({ url: gridurl });
    if (reload != undefined && reload) grd.trigger('reloadGrid');
}

//Coded by Shashi for setting search property
//input: array of jqGrid column indexes
$.fn.SetSearch = function (colIndex) {
    var grd = $(this);
    for (var i = 0; i < colIndex.length; i++) {
        grd.setColProp(grd.getGridParam('colModel')[colIndex[i]].name, { searchoptions: { sopt: ['cn', 'bw'] }, search: true })
    }
}