using System.ComponentModel.DataAnnotations;

namespace PBC.UtilityService.Utilities.DTOs
{
    /// <summary>
    /// Request for generating DMS password hash
    /// </summary>
    public class GenerateDMSPasswordRequest
    {
        /// <summary>
        /// Plain text password to hash
        /// </summary>
        [Required]
        public string UserPassword { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request for checking password against hash
    /// </summary>
    public class CheckPasswordRequest
    {
        /// <summary>
        /// Hash from database
        /// </summary>
        [Required]
        public string HashedPwdFromDatabase { get; set; } = string.Empty;

        /// <summary>
        /// Plain text password entered by user
        /// </summary>
        [Required]
        public string UserEnteredPassword { get; set; } = string.Empty;
    }

    /// <summary>
    /// Generic response for BCrypt operations
    /// </summary>
    /// <typeparam name="T">Type of data returned</typeparam>
    public class BCryptResponse<T>
    {
        /// <summary>
        /// Indicates if the operation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// The result data
        /// </summary>
        public T? Data { get; set; }

        /// <summary>
        /// Error message if operation failed
        /// </summary>
        public string? ErrorMessage { get; set; }
    }
}
