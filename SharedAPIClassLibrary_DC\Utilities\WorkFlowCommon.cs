﻿using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using static SharedAPIClassLibrary_AMERP.CoreWorkFlowEscalation1Services;
using static SharedAPIClassLibrary_AMERP.HelpDeskServiceRequestServices;
using static SharedAPIClassLibrary_DC.Utilities.Common;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace SharedAPIClassLibrary_AMERP.Utilities
{
    public class WorkFlowCommon
    {
        #region chkforAdmin
        /// <summary>
        /// chkforAdmin
        /// </summary>
        /// <param name="userID"></param>
        /// <param name="WorkFlowName"></param>
        /// <param name="DBName"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static bool chkforAdmin(int userID, string WorkFlowName, string DBName, string connString, int LogException)
        {
            bool flag = false;
            try
            {

                int WorkFlowID = Common.GetWorkFlowID(WorkFlowName, DBName, connString, LogException);
                using (SqlConnection conn = new SqlConnection(connString))
                {


                    SqlCommand cmd = null;

                    try
                    {
                        string query = @"
                        SELECT COUNT(*)
                            FROM GNM_WFRoleUser AS uid
                            INNER JOIN GNM_WFRole AS role ON uid.WFRole_ID = role.WFRole_ID
                            WHERE uid.UserID = @UserID
                            AND role.WorkFlow_ID = @WorkFlowID
                            AND role.WfRole_IsAdmin = 1";
                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;

                            cmd.Parameters.AddWithValue("@UserID", userID);
                            cmd.Parameters.AddWithValue("@WorkFlowID", WorkFlowID);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            int result = (int)cmd.ExecuteScalar();

                            flag = result > 0;


                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }

            }
            catch (Exception ex)
            {
                flag = false;
                LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return flag;
        }

        #endregion
        #region LockRecord
        public static string LockRecord(string connString, int LogException, string UserCulture, int QuotationID, int userID, int CompanyID, string WorkFlowName, string DBName, int Branch_ID = 0)
        {
            string text = "";
            int workFlow_ID = 0;
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string queryWorkFlow = "SELECT WorkFlow_ID FROM GNM_WorkFlow WHERE WorkFlow_Name = @WorkFlowName";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(queryWorkFlow, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@WorkFlowName", WorkFlowName);




                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            object result = cmd.ExecuteScalar();
                            workFlow_ID = result != null ? Convert.ToInt32(result) : throw new InvalidOperationException("WorkFlow not found.");



                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }


                text = LockTransaction(connString, LogException, CompanyID, workFlow_ID, QuotationID, userID, 0, Branch_ID);
                text = CommonFunctionalities.GetResourceString(UserCulture.ToString(), text).ToString();
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return text;
        }

        #endregion
        #region LockTransaction
        public static string LockTransaction(string connString, int LogException, int companyID, int workFlowID, int transactionNumber, int userID, int transactionValue, int Branch_ID = 0)
        {
            string empty = string.Empty;
            int num = 0;
            WF_WFCase_Progress wF_WFCase_Progress = null;
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {



                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                    {
                        conn.Open();
                    }

                    try
                    {

                        using (SqlCommand command = new SqlCommand("SELECT TOP 1 WFAction_ID FROM GNM_WFAction WHERE WFAction_Name = @WFActionName", conn))
                        {
                            command.Parameters.AddWithValue("@WFActionName", "Lock");
                            object result = command.ExecuteScalar();
                            num = result != null ? Convert.ToInt32(result) : 0;
                        }
                        using (SqlCommand command = new SqlCommand(@"SELECT TOP 1 * FROM GNM_WFCase_Progress 
                                                    WHERE WorkFlow_ID = @WorkFlowID AND Transaction_ID = @TransactionID 
                                                    ORDER BY WFCaseProgress_ID DESC", conn))
                        {
                            command.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                            command.Parameters.AddWithValue("@TransactionID", transactionNumber);
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    wF_WFCase_Progress = new WF_WFCase_Progress
                                    {
                                        WFCaseProgress_ID = reader.GetInt32(reader.GetOrdinal("WFCaseProgress_ID")),
                                        WorkFlow_ID = reader.GetInt32(reader.GetOrdinal("WorkFlow_ID")),
                                        Transaction_ID = reader.GetInt32(reader.GetOrdinal("Transaction_ID")),
                                        WFSteps_ID = reader.GetInt32(reader.GetOrdinal("WFSteps_ID")),
                                        Addresse_ID = reader.IsDBNull(reader.GetOrdinal("Addresse_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Addresse_ID")),
                                        Addresse_Flag = reader.IsDBNull(reader.GetOrdinal("Addresse_Flag")) ? (byte)0 : reader.GetByte(reader.GetOrdinal("Addresse_Flag")),
                                        Received_Time = reader.GetDateTime(reader.GetOrdinal("Received_Time")),
                                        Actioned_By = reader.IsDBNull(reader.GetOrdinal("Actioned_By")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Actioned_By")),
                                        Action_Time = reader.IsDBNull(reader.GetOrdinal("Action_Time")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("Action_Time")),
                                        Action_Chosen = reader.IsDBNull(reader.GetOrdinal("Action_Chosen")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Action_Chosen")),
                                        Action_Remarks = reader.IsDBNull(reader.GetOrdinal("Action_Remarks")) ? null : reader.GetString(reader.GetOrdinal("Action_Remarks")),
                                        Locked_Ind = reader.IsDBNull(reader.GetOrdinal("Locked_Ind")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("Locked_Ind")),
                                        WFNextStep_ID = reader.IsDBNull(reader.GetOrdinal("WFNextStep_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WFNextStep_ID"))
                                    };

                                }
                            }
                        }
                        if (wF_WFCase_Progress != null)
                        {
                            if (wF_WFCase_Progress.Action_Chosen != num)
                            {
                                using (SqlCommand command = new SqlCommand(@"UPDATE GNM_WFCase_Progress 
                                                            SET Actioned_By = @UserID, 
                                                                Action_Time = @ActionTime, 
                                                                Action_Chosen = @ActionChosen 
                                                            WHERE WFCaseProgress_ID = @WFCaseProgressID", conn))
                                {
                                    command.Parameters.AddWithValue("@UserID", userID);
                                    command.Parameters.AddWithValue("@ActionTime", Branch_ID != 0 ? LocalTime(Branch_ID, DateTime.Now, connString) : DateTime.Now);
                                    command.Parameters.AddWithValue("@ActionChosen", num);
                                    command.Parameters.AddWithValue("@WFCaseProgressID", wF_WFCase_Progress.WFCaseProgress_ID);
                                    command.ExecuteNonQuery();
                                }
                                using (SqlCommand command = new SqlCommand(@"INSERT INTO GNM_WFCase_Progress 
                                                            (WorkFlow_ID, Transaction_ID, WFSteps_ID, Addresse_Flag, Addresse_ID, Received_Time, Locked_Ind) 
                                                            VALUES (@WorkFlowID, @TransactionID, @WFStepsID, @AddresseFlag, @AddresseID, @ReceivedTime, @LockedInd)", conn))
                                {
                                    command.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                                    command.Parameters.AddWithValue("@TransactionID", transactionNumber);
                                    command.Parameters.AddWithValue("@WFStepsID", wF_WFCase_Progress.WFSteps_ID);
                                    command.Parameters.AddWithValue("@AddresseFlag", 1);
                                    command.Parameters.AddWithValue("@AddresseID", userID);
                                    command.Parameters.AddWithValue("@ReceivedTime", Branch_ID != 0 ? LocalTime(Branch_ID, DateTime.Now, connString) : DateTime.Now);
                                    command.Parameters.AddWithValue("@LockedInd", true);
                                    command.ExecuteNonQuery();
                                }


                                empty = "TransactionLockedSuccessfully";
                            }
                            else
                            {
                                empty = "TransactionisalreadybeenLocked";
                            }
                        }
                        else
                        {
                            empty = "ErrorOccuredwhileLocking";
                        }

                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {

                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }





            }
            catch (Exception ex)
            {
                empty = "ErrorOccuredwhileUnLocking";
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return empty;
        }
        public static DateTime LocalTime(int Branch_ID, DateTime servertime, string strCon)
        {
            int num = 0;
            string destinationTimeZoneId = "";
            DateTime now = DateTime.Now;
            try
            {
                SqlCommand cmd = null;
                using (SqlConnection sqlConnection = new SqlConnection(strCon))
                {
                    cmd = new SqlCommand("select TimeZoneID from GNM_Branch where Branch_ID='" + Branch_ID + "'", sqlConnection);
                    sqlConnection.Open();
                    num = Convert.ToInt32(cmd.ExecuteScalar());
                    sqlConnection.Close();
                }

                try
                {
                    using (SqlConnection sqlConnection = new SqlConnection(strCon))
                    {
                        cmd = new SqlCommand("select RefMasterDetail_Name from GNM_RefMasterDetail where RefMasterDetail_ID='" + num + "'", sqlConnection);
                        sqlConnection.Open();
                        destinationTimeZoneId = cmd.ExecuteScalar().ToString();
                        sqlConnection.Close();
                    }

                    return TimeZoneInfo.ConvertTimeBySystemTimeZoneId(servertime, TimeZoneInfo.Local.Id, destinationTimeZoneId);
                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion
        //#region UnLockTransaction
        //public static string UnLockTransaction(string connString,int LogException, int jobcardID, int userID, int CompanyID, string WorkFlowName, string DBName, int Branch_ID = 0)
        //{
        //    string text = "";
        //    int workFlowID = 0;
        //    try
        //    {

        //        using (SqlConnection conn = new SqlConnection(connString))
        //        {
        //            string queryWorkFlow = "SELECT WorkFlow_ID FROM GNM_WorkFlow WHERE WorkFlow_Name = @WorkFlowName";

        //            SqlCommand cmd = null;

        //            try
        //            {
        //                using (cmd = new SqlCommand(queryWorkFlow, conn))
        //                {
        //                    cmd.CommandType = CommandType.Text;




        //                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
        //                    {
        //                        conn.Open();
        //                    }

        //                    object result = cmd.ExecuteScalar();
        //                    workFlowID = result != null ? Convert.ToInt32(result) : throw new InvalidOperationException("WorkFlow not found.");



        //                }


        //            }
        //            catch (Exception ex)
        //            {
        //                if (LogException == 1)
        //                {
        //                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
        //                }

        //            }
        //            finally
        //            {
        //                cmd.Dispose();
        //                conn.Close();
        //                conn.Dispose();
        //                SqlConnection.ClearAllPools();
        //            }

        //        }


        //        text =UnLockTransaction(CompanyID, workFlowID, jobcardID, userID, "", Branch_ID);
        //        text = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), text).ToString();
        //    }
        //    catch (Exception ex)
        //    {
        //        if (LogException == 1)
        //        {
        //            global::LogSheetExporter.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
        //        }
        //    }

        //    return text;
        //}
        //#endregion
        #region UnLockTransaction
        public static string UnLockTransaction(string connString, int LogException, int companyID, int workFlowID, int transactionNumber, int userID, string remarks, int Branch_ID = 0)
        {
            string empty = string.Empty;
            int num2 = 0;
            SqlCommand cmd = null;
            WF_WFCase_Progress wF_WFCase_Progress = new WF_WFCase_Progress();
            WF_WFCase_Progress wF_WFCase_Progress2 = new WF_WFCase_Progress();
            byte addresse_Flag = 0;

            WF_WFCase_Progress wF_WFCase_Progress3 = new WF_WFCase_Progress();
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    //string queryWorkFlow = "SELECT TOP 1 WFAction_ID FROM GNM_WFAction WHERE UPPER(WFAction_Name) = 'UNLOCK'";

                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                    {
                        conn.Open();
                    }

                    try
                    {

                        using (cmd = new SqlCommand("SELECT TOP 1 WFAction_ID FROM GNM_WFAction WHERE UPPER(WFAction_Name) = 'UNLOCK'", conn))
                        {
                            object result = cmd.ExecuteScalar();
                            num2 = result != null ? Convert.ToInt32(result) : 0;
                        }
                        using (cmd = new SqlCommand(@"SELECT TOP 1 * FROM GNM_WFCase_Progress 
                                                WHERE WorkFlow_ID = @WorkFlowID AND Transaction_ID = @TransactionID AND Addresse_Flag = 0 
                                                ORDER BY WFCaseProgress_ID DESC", conn))
                        {
                            cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                            cmd.Parameters.AddWithValue("@TransactionID", transactionNumber);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    wF_WFCase_Progress = new WF_WFCase_Progress
                                    {
                                        WFCaseProgress_ID = reader.GetInt32(reader.GetOrdinal("WFCaseProgress_ID")),
                                        WorkFlow_ID = reader.GetInt32(reader.GetOrdinal("WorkFlow_ID")),
                                        Transaction_ID = reader.GetInt32(reader.GetOrdinal("Transaction_ID")),
                                        WFSteps_ID = reader.GetInt32(reader.GetOrdinal("WFSteps_ID")),
                                        Addresse_ID = reader.IsDBNull(reader.GetOrdinal("Addresse_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Addresse_ID")),
                                        Addresse_Flag = reader.GetByte(reader.GetOrdinal("Addresse_Flag")),
                                        Received_Time = reader.GetDateTime(reader.GetOrdinal("Received_Time")),
                                        Actioned_By = reader.IsDBNull(reader.GetOrdinal("Actioned_By")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Actioned_By")),
                                        Action_Time = reader.IsDBNull(reader.GetOrdinal("Action_Time")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("Action_Time")),
                                        Action_Chosen = reader.IsDBNull(reader.GetOrdinal("Action_Chosen")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Action_Chosen")),
                                        Action_Remarks = reader.IsDBNull(reader.GetOrdinal("Action_Remarks")) ? null : reader.GetString(reader.GetOrdinal("Action_Remarks")),
                                        Locked_Ind = reader.IsDBNull(reader.GetOrdinal("Locked_Ind")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("Locked_Ind")),
                                        WFNextStep_ID = reader.IsDBNull(reader.GetOrdinal("WFNextStep_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WFNextStep_ID"))
                                    };
                                }
                            }
                        }
                        using (cmd = new SqlCommand(@"SELECT TOP 1 * FROM GNM_WFCase_Progress 
                                                WHERE WorkFlow_ID = @WorkFlowID AND Transaction_ID = @TransactionID 
                                                ORDER BY WFCaseProgress_ID DESC", conn))
                        {

                            cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                            cmd.Parameters.AddWithValue("@TransactionID", transactionNumber);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    wF_WFCase_Progress2 = new WF_WFCase_Progress
                                    {
                                        WFCaseProgress_ID = reader.GetInt32(reader.GetOrdinal("WFCaseProgress_ID")),
                                        WorkFlow_ID = reader.GetInt32(reader.GetOrdinal("WorkFlow_ID")),
                                        Transaction_ID = reader.GetInt32(reader.GetOrdinal("Transaction_ID")),
                                        WFSteps_ID = reader.GetInt32(reader.GetOrdinal("WFSteps_ID")),
                                        Addresse_ID = reader.IsDBNull(reader.GetOrdinal("Addresse_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Addresse_ID")),
                                        Addresse_Flag = reader.GetByte(reader.GetOrdinal("Addresse_Flag")),
                                        Received_Time = reader.GetDateTime(reader.GetOrdinal("Received_Time")),
                                        Actioned_By = reader.IsDBNull(reader.GetOrdinal("Actioned_By")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Actioned_By")),
                                        Action_Time = reader.IsDBNull(reader.GetOrdinal("Action_Time")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("Action_Time")),
                                        Action_Chosen = reader.IsDBNull(reader.GetOrdinal("Action_Chosen")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Action_Chosen")),
                                        Action_Remarks = reader.IsDBNull(reader.GetOrdinal("Action_Remarks")) ? null : reader.GetString(reader.GetOrdinal("Action_Remarks")),
                                        Locked_Ind = reader.IsDBNull(reader.GetOrdinal("Locked_Ind")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("Locked_Ind")),
                                        WFNextStep_ID = reader.IsDBNull(reader.GetOrdinal("WFNextStep_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WFNextStep_ID"))
                                    };
                                }
                            }
                        }
                        addresse_Flag = ((wF_WFCase_Progress == null) ? Convert.ToByte(1) : Convert.ToByte(0));
                        using (cmd = new SqlCommand(@"SELECT TOP 1 * FROM GNM_WFCase_Progress 
                                                WHERE WorkFlow_ID = @WorkFlowID AND Transaction_ID = @TransactionID AND Addresse_Flag = 1 AND Action_Chosen IS NOT NULL 
                                                ORDER BY WFCaseProgress_ID DESC", conn))
                        {
                            cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                            cmd.Parameters.AddWithValue("@TransactionID", transactionNumber);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    wF_WFCase_Progress3 = new WF_WFCase_Progress
                                    {
                                        Addresse_ID = reader.GetInt32(reader.GetOrdinal("Addresse_ID"))
                                    };
                                }
                            }
                        }

                        if (wF_WFCase_Progress2 != null)
                        {
                            if (wF_WFCase_Progress2.Action_Chosen != num2)
                            {
                                using (cmd = new SqlCommand(@"UPDATE GNM_WFCase_Progress 
                                                        SET Actioned_By = @UserID, 
                                                            Action_Time = @ActionTime, 
                                                            Action_Chosen = @ActionChosen, 
                                                            Action_Remarks = @Remarks 
                                                        WHERE WFCaseProgress_ID = @WFCaseProgressID", conn))
                                {
                                    cmd.Parameters.AddWithValue("@UserID", userID);
                                    cmd.Parameters.AddWithValue("@ActionTime", Branch_ID != 0 ? LocalTime(Branch_ID, DateTime.Now, connString) : DateTime.Now);
                                    cmd.Parameters.AddWithValue("@ActionChosen", num2);
                                    cmd.Parameters.AddWithValue("@Remarks", remarks);
                                    cmd.Parameters.AddWithValue("@WFCaseProgressID", wF_WFCase_Progress2.WFCaseProgress_ID);
                                    cmd.ExecuteNonQuery();
                                }
                                using (cmd = new SqlCommand(@"INSERT INTO GNM_WFCase_Progress 
                                                        (WorkFlow_ID, Transaction_ID, WFSteps_ID, Addresse_Flag, Addresse_ID, Received_Time) 
                                                        VALUES (@WorkFlowID, @TransactionID, @WFStepsID, @AddresseFlag, @AddresseID, @ReceivedTime)", conn))
                                {
                                    cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                                    cmd.Parameters.AddWithValue("@TransactionID", transactionNumber);
                                    cmd.Parameters.AddWithValue("@WFStepsID", wF_WFCase_Progress2.WFSteps_ID);
                                    cmd.Parameters.AddWithValue("@AddresseFlag", addresse_Flag);
                                    cmd.Parameters.AddWithValue("@AddresseID", ((wF_WFCase_Progress == null) ? wF_WFCase_Progress3.Addresse_ID : wF_WFCase_Progress.Addresse_ID));
                                    cmd.Parameters.AddWithValue("@ReceivedTime", ((Branch_ID != 0) ? LocalTime(Branch_ID, DateTime.Now, connString) : DateTime.Now));
                                    cmd.ExecuteNonQuery();
                                }


                                return "TransactionUnLockedSuccessfully";
                            }

                            return "TransactionisalreadybeenUnLocked";
                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }






                return "ErrorOccuredwhileUnLocking";
            }
            catch (Exception)
            {
                return "ErrorOccuredwhileUnLocking";
            }
        }

        #endregion
        #region
        public static string UnLockRecord(string connString, int LogException, string UserCulture, int jobcardID, int userID, int CompanyID, string WorkFlowName, string DBName, int Branch_ID = 0)
        {
            string text = "";
            int workFlowID = 0;
            try
            {

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string queryWorkFlow = "SELECT WorkFlow_ID FROM GNM_WorkFlow WHERE WorkFlow_Name = @WorkFlowName";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(queryWorkFlow, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@WorkFlowName", WorkFlowName);




                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            object result = cmd.ExecuteScalar();
                            workFlowID = result != null ? Convert.ToInt32(result) : throw new InvalidOperationException("WorkFlow not found.");



                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }

                text = UnLockTransaction(connString, LogException, CompanyID, workFlowID, jobcardID, userID, "", Branch_ID);
                text = CommonFunctionalities.GetResourceString(UserCulture.ToString(), text).ToString();
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return text;
        }

        #endregion

        #region GetWorkFlowID
        public static int GetWorkFlowID(string WorkFlowName, string DBName, string constring, int LogException)
        {
            int workflowID = 0;

            try
            {

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Query to get the WorkFlow_ID based on WorkFlowName
                    string query = @"SELECT TOP 1 WorkFlow_ID 
                             FROM GNM_WorkFlow 
                             WHERE WorkFlow_Name = @WorkFlowName";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        // Add the parameter to prevent SQL injection
                        cmd.Parameters.AddWithValue("@WorkFlowName", WorkFlowName);

                        // Execute the query and get the WorkFlow_ID
                        object result = cmd.ExecuteScalar();
                        if (result != null && result != DBNull.Value)
                        {
                            workflowID = Convert.ToInt32(result);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log exception if LogException flag is 0
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return workflowID;
        }
        #endregion


        #region CheckPreffixSuffix
        public static bool CheckPreffixSuffix(int companyID, int branchID, string objectName, string dbName, string constring)
        {
            bool flag = false;
            int objectID = GetObjectID(objectName, dbName, constring);

            try
            {
                // Construct the connection string with the provided database name (dbName)
                SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(constring)
                {
                    InitialCatalog = dbName
                };

                using (SqlConnection conn = new SqlConnection(builder.ToString()))
                {
                    conn.Open();

                    // Define the current date to use in both queries
                    DateTime currentDate = DateTime.Now;

                    // First Query: Check PrefixSuffix with specific Branch ID
                    string query1 = @"SELECT COUNT(1) 
                              FROM WF_PrefixSuffix 
                              WHERE Branch_ID = @BranchID 
                                AND Object_ID = @ObjectID 
                                AND FromDate <= @CurrentDate 
                                AND ToDate >= @CurrentDate";

                    using (SqlCommand cmd = new SqlCommand(query1, conn))
                    {
                        cmd.Parameters.AddWithValue("@BranchID", branchID);
                        cmd.Parameters.AddWithValue("@ObjectID", objectID);
                        cmd.Parameters.AddWithValue("@CurrentDate", currentDate);

                        int count = Convert.ToInt32(cmd.ExecuteScalar());
                        flag = count > 0;
                    }

                    // Second Query: Check PrefixSuffix with null Branch ID if the first check fails
                    if (!flag)
                    {
                        string query2 = @"SELECT COUNT(1) 
                                  FROM WF_PrefixSuffix 
                                  WHERE Company_ID = @CompanyID 
                                    AND Branch_ID IS NULL 
                                    AND Object_ID = @ObjectID 
                                    AND FromDate <= @CurrentDate 
                                    AND ToDate >= @CurrentDate";

                        using (SqlCommand cmd = new SqlCommand(query2, conn))
                        {
                            cmd.Parameters.AddWithValue("@CompanyID", companyID);
                            cmd.Parameters.AddWithValue("@ObjectID", objectID);
                            cmd.Parameters.AddWithValue("@CurrentDate", currentDate);

                            int count = Convert.ToInt32(cmd.ExecuteScalar());
                            flag = count > 0;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return flag;
        }
        public static int GetObjectID(string name, string dbName, string constring)
        {
            int result = 0;

            try
            {

                SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(constring)
                {
                    InitialCatalog = dbName
                };

                using (SqlConnection conn = new SqlConnection(builder.ToString()))
                {
                    conn.Open();

                    // Query to get the Object_ID based on Object_Name
                    string query = @"SELECT TOP 1 Object_ID 
                             FROM WF_Object 
                             WHERE UPPER(Object_Name) = UPPER(@ObjectName)";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@ObjectName", name);

                        // Execute the query and retrieve the result
                        object obj = cmd.ExecuteScalar();
                        if (obj != null && obj != DBNull.Value)
                        {
                            result = Convert.ToInt32(obj);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return result;
        }
        #endregion


        #region GetRolesForActions
        public static dynamic GetRolesForActions(int WFCurrentStepID, int ActionID, int TransactionID, int CompanyID, string WorkFlowName, string DBName, int UserLanguageID, string constring, int LogException)
        {
            object obj = null;
            object result = obj;
            int num = 0;
            List<WF_WFStepLink> list = null;
            object obj2 = obj;
            bool flag = true;
            List<ResultForAction> list2 = new List<ResultForAction>();
            try
            {
                num = GetWorkFlowID(WorkFlowName, DBName, constring, LogException);
                list = GetStepLinkForInsert(constring, CompanyID, num, WFCurrentStepID, ActionID);
                if (list != null && list.Count > 0)
                {
                    foreach (WF_WFStepLink item in list)
                    {
                        if (item.Addresse_Flag == 1)
                        {
                            list2.AddRange(GetListOfStaffForaRoleID(constring, CompanyID, item.Addresse_WFRole_ID.Value, 0, WFCurrentStepID, ActionID, UserLanguageID));
                            flag = false;
                        }
                        else
                        {
                            list2.AddRange(GetAssociatedRolesForaStep(constring, LogException, CompanyID, WFCurrentStepID, ActionID, num, UserLanguageID));
                            flag = true;
                        }
                    }

                    IQueryable<ResultForAction> list3 = list2.AsQueryable();
                    result = new
                    {
                        Result = (flag ? "Role" : "Individual"),
                        ResultList = (from res in list2
                                      orderby res.Name
                                      select new { res.ID, res.isVersionEnabled, res.Name }).Distinct().ToArray(),
                        CallDate = DateTime.Now.ToShortDateString(),
                        CallTime = DateTime.Now.ToShortTimeString()
                    };
                }
                else
                {
                    result = new
                    {
                        Result = "End",
                        CallDate = DateTime.Now.ToString("dd-MMM-yyyy") + ' ' + DateTime.Now.ToString("HH:mm"),
                        CallTime = DateTime.Now.ToShortTimeString()
                    };
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return result;
        }
        public static List<WF_WFStepLink> GetStepLinkForInsert(string constring, int companyID, int workFlowID, int stepID, int ActionID)
        {
            List<WF_WFStepLink> list = new List<WF_WFStepLink>();

            try
            {
                using (SqlConnection con = new SqlConnection(constring))
                {
                    con.Open();

                    // First query to get initial WF_WFStepLink records
                    string query1 = @"
                SELECT WFStepLink_ID, FrmWFSteps_ID, ToWFSteps_ID, WorkFlow_ID, Company_ID, WFAction_ID 
                FROM GNM_WFStepLink 
                WHERE FrmWFSteps_ID = @StepID AND WorkFlow_ID = @WorkFlowID AND Company_ID = @CompanyID AND WFAction_ID = @ActionID";

                    using (SqlCommand cmd1 = new SqlCommand(query1, con))
                    {
                        cmd1.Parameters.AddWithValue("@StepID", stepID);
                        cmd1.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                        cmd1.Parameters.AddWithValue("@CompanyID", companyID);
                        cmd1.Parameters.AddWithValue("@ActionID", ActionID);

                        using (SqlDataReader reader1 = cmd1.ExecuteReader())
                        {
                            while (reader1.Read())
                            {
                                WF_WFStepLink stepLink = new WF_WFStepLink
                                {
                                    WFStepLink_ID = Convert.ToInt32(reader1["WFStepLink_ID"]),
                                    FrmWFSteps_ID = Convert.ToInt32(reader1["FrmWFSteps_ID"]),
                                    ToWFSteps_ID = Convert.ToInt32(reader1["ToWFSteps_ID"]),
                                    WorkFlow_ID = Convert.ToInt32(reader1["WorkFlow_ID"]),
                                    Company_ID = Convert.ToInt32(reader1["Company_ID"]),
                                    WFAction_ID = Convert.ToInt32(reader1["WFAction_ID"])
                                };

                                list.Add(stepLink);

                                // Second query to get related WF_WFStepLink records based on ToWFSteps_ID
                                string query2 = @"
                            SELECT WFStepLink_ID, FrmWFSteps_ID, ToWFSteps_ID, WorkFlow_ID, Company_ID, WFAction_ID 
                            FROM GNM_WFStepLink 
                            WHERE FrmWFSteps_ID = @ToStepID AND WorkFlow_ID = @WorkFlowID AND Company_ID = @CompanyID";

                                using (SqlCommand cmd2 = new SqlCommand(query2, con))
                                {
                                    cmd2.Parameters.AddWithValue("@ToStepID", stepLink.ToWFSteps_ID);
                                    cmd2.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                                    cmd2.Parameters.AddWithValue("@CompanyID", companyID);

                                    using (SqlDataReader reader2 = cmd2.ExecuteReader())
                                    {
                                        while (reader2.Read())
                                        {
                                            WF_WFStepLink additionalStepLink = new WF_WFStepLink
                                            {
                                                WFStepLink_ID = Convert.ToInt32(reader2["WFStepLink_ID"]),
                                                FrmWFSteps_ID = Convert.ToInt32(reader2["FrmWFSteps_ID"]),
                                                ToWFSteps_ID = Convert.ToInt32(reader2["ToWFSteps_ID"]),
                                                WorkFlow_ID = Convert.ToInt32(reader2["WorkFlow_ID"]),
                                                Company_ID = Convert.ToInt32(reader2["Company_ID"]),
                                                WFAction_ID = Convert.ToInt32(reader2["WFAction_ID"])
                                            };
                                            list.Add(additionalStepLink);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return list;
        }


        public static List<ResultForAction> GetListOfStaffForaRoleID(string constring, int companyID, int roleID, int transactionValue, int stepID, int actionID, int UserLanguageID)
        {
            List<ResultForAction> resultList = new List<ResultForAction>();
            bool isVersionEnabled = false;
            int externalCompanyID = 0;

            try
            {

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Fetch WFRole_ExternalCompany_ID for the given roleID
                    string query1 = @"SELECT WFRole_ExternalCompany_ID 
                              FROM GNM_WFRole 
                              WHERE WFRole_ID = @RoleID";

                    using (SqlCommand cmd = new SqlCommand(query1, conn))
                    {
                        cmd.Parameters.AddWithValue("@RoleID", roleID);
                        object result = cmd.ExecuteScalar();
                        if (result != null)
                        {
                            externalCompanyID = Convert.ToInt32(result);
                        }
                    }

                    // Check if IsVersionEnabled flag is set
                    string query2 = @"SELECT ISNULL(IsVersionEnabled, 0) 
                              FROM GNM_WFStepLink 
                              WHERE Company_ID = @CompanyID 
                              AND FrmWFSteps_ID = @StepID 
                              AND WFAction_ID = @ActionID";

                    using (SqlCommand cmd = new SqlCommand(query2, conn))
                    {
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);
                        cmd.Parameters.AddWithValue("@StepID", stepID);
                        cmd.Parameters.AddWithValue("@ActionID", actionID);
                        object result = cmd.ExecuteScalar();
                        if (result != null)
                        {
                            isVersionEnabled = Convert.ToBoolean(result);
                        }
                    }

                    // Fetch user details based on UserLanguageID
                    string query3;
                    if (UserLanguageID == 0)
                    {
                        query3 = @"SELECT DISTINCT a.WFRole_ID, b.User_ID, b.User_Name, b.User_LoginID
                           FROM GNM_WFRoleUser a
                           INNER JOIN GNM_User b ON a.UserID = b.User_ID
                           WHERE a.WFRole_ID = @RoleID 
                           AND b.Company_ID = @ExtCompID 
                           AND b.User_IsActive = 1";
                    }
                    else
                    {
                        query3 = @"SELECT DISTINCT a.WFRole_ID, b.User_ID, l.User_Name, b.User_LoginID
                           FROM GNM_WFRoleUser a
                           INNER JOIN GNM_User b ON a.UserID = b.User_ID
                           INNER JOIN GNM_UserLocale l ON b.User_ID = l.User_ID
                           WHERE a.WFRole_ID = @RoleID 
                           AND b.Company_ID = @ExtCompID 
                           AND b.User_IsActive = 1 
                           AND l.Language_ID = @UserLanguageID";
                    }

                    using (SqlCommand cmd = new SqlCommand(query3, conn))
                    {
                        cmd.Parameters.AddWithValue("@RoleID", roleID);
                        cmd.Parameters.AddWithValue("@ExtCompID", externalCompanyID);

                        if (UserLanguageID != 0)
                        {
                            cmd.Parameters.AddWithValue("@UserLanguageID", UserLanguageID);
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ResultForAction resultForAction = new ResultForAction
                                {
                                    ID = reader.GetInt32(reader.GetOrdinal("User_ID")),
                                    Name = reader.GetString(reader.GetOrdinal("User_Name")),
                                    isVersionEnabled = isVersionEnabled
                                };

                                resultList.Add(resultForAction);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log exception
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return resultList;
        }

        public static List<ResultForAction> GetAssociatedRolesForaStep(string constring, int LogException, int companyID, int stepID, int actionID, int workFlowID, int UserLanguageID)
        {
            List<ResultForAction> resultList = new List<ResultForAction>();
            List<int> toWFStepsIDList = new List<int>();
            bool isVersionEnabled = false;

            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Query to check if the version is enabled
                    string versionCheckQuery = @"
                SELECT TOP 1 IsVersionEnabled 
                FROM GNM_WFStepLink 
                WHERE Company_ID = @CompanyID 
                  AND FrmWFSteps_ID = @StepID 
                  AND WFAction_ID = @ActionID";

                    using (SqlCommand versionCmd = new SqlCommand(versionCheckQuery, conn))
                    {
                        versionCmd.Parameters.AddWithValue("@CompanyID", companyID);
                        versionCmd.Parameters.AddWithValue("@StepID", stepID);
                        versionCmd.Parameters.AddWithValue("@ActionID", actionID);

                        object versionResult = versionCmd.ExecuteScalar();
                        isVersionEnabled = versionResult != null && versionResult != DBNull.Value && Convert.ToBoolean(versionResult);
                    }

                    // Fetch all `ToWFSteps_ID` related to given `FrmWFSteps_ID`, `Company_ID`, and `WorkFlow_ID`
                    string toWFStepsQuery = @"
                SELECT ToWFSteps_ID 
                FROM GNM_WFStepLink 
                WHERE FrmWFSteps_ID = @StepID 
                  AND Company_ID = @CompanyID 
                  AND WFAction_ID = @ActionID 
                  AND WorkFlow_ID = @WorkFlowID";

                    using (SqlCommand toWFStepsCmd = new SqlCommand(toWFStepsQuery, conn))
                    {
                        toWFStepsCmd.Parameters.AddWithValue("@CompanyID", companyID);
                        toWFStepsCmd.Parameters.AddWithValue("@StepID", stepID);
                        toWFStepsCmd.Parameters.AddWithValue("@ActionID", actionID);
                        toWFStepsCmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);

                        using (SqlDataReader reader = toWFStepsCmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                toWFStepsIDList.Add(reader.GetInt32(0));
                            }
                        }
                    }

                    // If UserLanguageID is 0, join with WF_WFRole table, otherwise join with WF_WFRoleLocale
                    string roleQuery = UserLanguageID == 0 ?
                        @"
                SELECT DISTINCT b.WFRole_ID AS ID, b.WFRole_Name AS Name
                FROM GNM_WFStepLink a
                INNER JOIN GNM_WFRole b ON a.Addresse_WFRole_ID = b.WFRole_ID
                WHERE a.ToWFSteps_ID IN (@ToWFStepsList) 
                  AND a.Company_ID = @CompanyID 
                  AND a.WorkFlow_ID = @WorkFlowID"
                        :
                        @"
                SELECT DISTINCT b.WFRole_ID AS ID, b.WFRole_Name AS Name
                FROM GNM_WFStepLink a
                INNER JOIN GNM_WFRoleLocale b ON a.Addresse_WFRole_ID = b.WFRole_ID
                WHERE a.ToWFSteps_ID IN (@ToWFStepsList) 
                  AND b.Language_ID = @UserLanguageID 
                  AND a.Company_ID = @CompanyID 
                  AND a.WorkFlow_ID = @WorkFlowID";

                    using (SqlCommand roleCmd = new SqlCommand(roleQuery, conn))
                    {
                        // Prepare parameter for `ToWFStepsList` as a comma-separated string
                        var toWFStepsListParam = string.Join(",", toWFStepsIDList);
                        roleCmd.Parameters.AddWithValue("@ToWFStepsList", toWFStepsListParam);
                        roleCmd.Parameters.AddWithValue("@CompanyID", companyID);
                        roleCmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);

                        if (UserLanguageID != 0)
                        {
                            roleCmd.Parameters.AddWithValue("@UserLanguageID", UserLanguageID);
                        }

                        using (SqlDataReader reader = roleCmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ResultForAction result = new ResultForAction
                                {
                                    ID = reader.GetInt32(0),
                                    Name = reader.GetString(1),
                                    isVersionEnabled = isVersionEnabled
                                };
                                resultList.Add(result);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log exception if LogException flag is 0
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return resultList;
        }
        #endregion

    }
}
