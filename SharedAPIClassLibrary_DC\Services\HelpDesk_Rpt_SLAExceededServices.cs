﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.Utilities.CoreCompanyCalenderMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class HelpDesk_Rpt_SLAExceededServices
    {
        public static string DateDetails = string.Empty;
        public static int getWorkingHours(DateTime? Calldate, DateTime ToDate, int companyID, string connString, int LogException)
        {
            CallDateDetail detailForCallDate = new CallDateDetail();
            detailForCallDate = GetDetailsForDate(Calldate, companyID, connString, LogException);
            double final = detailForCallDate.WorkHours;
            var startTime = detailForCallDate.startTime;
            var endTime = detailForCallDate.endTime;
            double STM = detailForCallDate.startTimeMinutes;
            double ETM = detailForCallDate.endTimeMinutes;
            var BreakstartTime = detailForCallDate.BreakstartTime;
            var BreakEndTime = detailForCallDate.BreakEndTime;
            double BSTM = detailForCallDate.BreakstartTimeMinutes;
            double BETM = detailForCallDate.BreakEndTimeMinute;
            double TotalBreakTime = BETM - BSTM;
            if (startTime < endTime)
            {
                return getWorkingHoursForNonNightShift(Calldate, ToDate, companyID, detailForCallDate, connString, LogException);
            }
            else
            {
                return getWorkingHoursForNightShift(Calldate, ToDate, companyID, detailForCallDate, connString, LogException);
            }
        }

        public static CallDateDetail GetDetailsForDate(DateTime? Calldate, int companyID, string connString, int LogException)
        {
            CallDateDetail detail = null;
            try
            {
                DateDetails Data = null;
                DateDetails DataForYear = null;
                List<DateDetails> DateDetailsList = new List<DateDetails>();

                if (DateDetails == null)
                {
                    Data = new DateDetails();
                    Data.Details = GetDetails(Calldate, companyID, connString, LogException);
                    Data.Year = Calldate.Value.Year;

                    DateDetailsList.Add(Data);
                    DateDetails = DateDetailsList.ToString();
                }
                else
                {
                    DateDetailsList.Clear();
                    DateDetailsList = DateDetails.Cast<DateDetails>().ToList();
                    DataForYear = DateDetailsList.Where(Y => Y.Year == Calldate.Value.Year).FirstOrDefault();
                    if (DataForYear == null)//Details not found for a given Year
                    {
                        Data = new DateDetails();
                        Data.Details = GetDetails(Calldate, companyID, connString, LogException);
                        Data.Year = Calldate.Value.Year;

                        DateDetailsList.Add(Data);
                        DateDetails = DateDetailsList.ToString();
                    }
                }

                DataForYear = DateDetailsList.Where(Y => Y.Year == Calldate.Value.Year).FirstOrDefault();
                detail = DataForYear.Details;
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return detail;
        }


        public static CallDateDetail GetDetails(DateTime? Calldate, int companyID, string connString, int LogException)
        {
            CallDateDetail detail = new CallDateDetail();
            try
            {
                string WDays = null;

                // Query to fetch the working days
                string query = @"
                                SELECT TOP 1 CompanyCalender_WorkingDays
                                FROM GNM_CompanyCalender
                                WHERE Company_ID = @CompanyID
                                  AND IsGeneralShift = 1
                                  AND CompanyCalender_Year = @Year";

                // Execute query using ADO.NET
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    SqlCommand cmd = new SqlCommand(query, conn);
                    cmd.Parameters.AddWithValue("@CompanyID", companyID);
                    cmd.Parameters.AddWithValue("@Year", Calldate.Value.Year);

                    conn.Open();
                    var result = cmd.ExecuteScalar();
                    if (result != null && result != DBNull.Value)
                    {
                        WDays = result.ToString();
                    }
                }

                List<GNM_CompanyCalender> startTimeList = new List<GNM_CompanyCalender>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string StartTimequery = "SELECT * FROM GNM_CompanyCalender";

                    using (var cmd = new SqlCommand(StartTimequery, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_CompanyCalender
                                {
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    CompanyCalender_Year = reader["CompanyCalender_Year"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CompanyCalender_Year"]),
                                    CompanyCalender_StartTime = (TimeSpan)(reader["CompanyCalender_StartTime"] == DBNull.Value ? (TimeSpan?)null : (TimeSpan)reader["CompanyCalender_StartTime"]),
                                    CompanyCalender_EndTime = (TimeSpan)(reader["CompanyCalender_EndTime"] == DBNull.Value ? (TimeSpan?)null : (TimeSpan)reader["CompanyCalender_EndTime"]),
                                    Break_StartTime = (TimeSpan)(reader["Break_StartTime"] == DBNull.Value ? (TimeSpan?)null : (TimeSpan)reader["Break_StartTime"]),
                                    Break_EndTime = (TimeSpan)(reader["Break_EndTime"] == DBNull.Value ? (TimeSpan?)null : (TimeSpan)reader["Break_EndTime"]),
                                    IsGeneralShift = (bool)(reader["IsGeneralShift"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IsGeneralShift"])),
                                };

                                startTimeList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }
                var startTime = startTimeList.Where(d => d.Company_ID == companyID && d.IsGeneralShift == true && d.CompanyCalender_Year == Calldate.Value.Year).Select(a => a.CompanyCalender_StartTime).FirstOrDefault();
                double STM = startTime.TotalMinutes;
                var endTime = startTimeList.Where(d => d.Company_ID == companyID && d.IsGeneralShift == true && d.CompanyCalender_Year == Calldate.Value.Year).Select(a => a.CompanyCalender_EndTime).FirstOrDefault();
                double ETM = (STM < endTime.TotalMinutes) ? endTime.TotalMinutes : (1440 + endTime.TotalMinutes);
                var BreakstartTime = startTimeList.Where(d => d.Company_ID == companyID && d.IsGeneralShift == true && d.CompanyCalender_Year == Calldate.Value.Year).Select(a => a.Break_StartTime).FirstOrDefault();
                double BSTM = (BreakstartTime.TotalMinutes < STM) ? (1440 + BreakstartTime.TotalMinutes) : BreakstartTime.TotalMinutes;
                var BreakEndTime = startTimeList.Where(d => d.Company_ID == companyID && d.IsGeneralShift == true && d.CompanyCalender_Year == Calldate.Value.Year).Select(a => a.Break_EndTime).FirstOrDefault();
                double BETM = (BreakEndTime.TotalMinutes < STM) ? (1440 + BreakEndTime.TotalMinutes) : BreakEndTime.TotalMinutes;
                double TotalBreakTime = BETM - BSTM;
                double WHours = (ETM - STM) - (BETM - BSTM);
                int CallDateYear = Convert.ToInt32(Calldate.Value.Year);
                int Count = 0;
                List<string> WDlist = new List<string>();
                foreach (string a in WDays.Split(','))
                {
                    Count++;
                    if (a == "1")
                    {
                        switch (Count)
                        {
                            case 1: WDlist.Add("Monday"); break;
                            case 2: WDlist.Add("Tuesday"); break;
                            case 3: WDlist.Add("Wednesday"); break;
                            case 4: WDlist.Add("Thursday"); break;
                            case 5: WDlist.Add("Friday"); break;
                            case 6: WDlist.Add("Saturday"); break;
                            case 7: WDlist.Add("Sunday"); break;
                        }
                    }
                };
                detail.startTime = startTime;
                detail.endTime = endTime;
                detail.BreakstartTime = BreakstartTime;
                detail.BreakEndTime = BreakEndTime;
                detail.startTimeMinutes = STM;
                detail.endTimeMinutes = ETM; ;
                detail.BreakstartTimeMinutes = BSTM;
                detail.BreakEndTimeMinute = BETM;
                detail.WorkDays = WDlist;
                detail.WorkHours = WHours;

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return detail;
        }

        public static int getWorkingHoursForNonNightShift(DateTime? Calldate, DateTime ToDate, int companyID, CallDateDetail detailForCallDate, string connString, int LogException)
        {
            DateTime? ActualCallDate = Calldate;
            //CallDateDetail detailForCallDate = new CallDateDetail();
            //detailForCallDate = GetDetailsForDate(Calldate, companyID);
            double final = detailForCallDate.WorkHours;
            var startTime = detailForCallDate.startTime;
            var endTime = detailForCallDate.endTime;
            double STM = detailForCallDate.startTime.TotalMinutes;
            double ETM = detailForCallDate.endTime.TotalMinutes;
            var BreakstartTime = detailForCallDate.BreakstartTime;
            var BreakEndTime = detailForCallDate.BreakEndTime;
            double BSTM = detailForCallDate.BreakstartTime.TotalMinutes;
            double BETM = detailForCallDate.BreakEndTime.TotalMinutes;
            double TotalBreakTime = BETM - BSTM;
            double timeFromCallDate = 0.0;
            double timeFromToday = 0.0;
            if (Calldate.Value.Date == ToDate.Date && Calldate.Value.TimeOfDay < endTime && ToDate.TimeOfDay < endTime && Calldate.Value.TimeOfDay > startTime)
            {
                if (ToDate.TimeOfDay < BreakstartTime)
                {
                    timeFromToday = ToDate.TimeOfDay.TotalMinutes - Calldate.Value.TimeOfDay.TotalMinutes;
                }
                else if (ToDate.TimeOfDay > BreakstartTime && ToDate.TimeOfDay < BreakEndTime)
                {
                    timeFromToday = BSTM - Calldate.Value.TimeOfDay.TotalMinutes;
                }
                else if (ToDate.TimeOfDay > BreakEndTime && Calldate.Value.TimeOfDay < BreakstartTime)
                {
                    timeFromToday = (ToDate.TimeOfDay.TotalMinutes - Calldate.Value.TimeOfDay.TotalMinutes) - (BETM - BSTM);
                }
                else if (ToDate.TimeOfDay > BreakEndTime && Calldate.Value.TimeOfDay > BreakstartTime && Calldate.Value.TimeOfDay < BreakEndTime)
                {
                    timeFromToday = (ToDate.TimeOfDay.TotalMinutes - BETM);
                }
                else if (ToDate.TimeOfDay > BreakEndTime && Calldate.Value.TimeOfDay > BreakEndTime)
                {
                    timeFromToday = (ToDate.TimeOfDay.TotalMinutes - Calldate.Value.TimeOfDay.TotalMinutes);
                }
            }
            else if (Calldate.Value.Date == ToDate.Date && Calldate.Value.TimeOfDay < endTime && ToDate.TimeOfDay > endTime && Calldate.Value.TimeOfDay > startTime)
            {
                if (Calldate.Value.TimeOfDay < BreakstartTime)
                {
                    timeFromToday = (ETM - Calldate.Value.TimeOfDay.TotalMinutes) - (BETM - BSTM);
                }
                else if (Calldate.Value.TimeOfDay > BreakstartTime && Calldate.Value.TimeOfDay < BreakEndTime)
                {
                    timeFromToday = (ETM - BETM);
                }
                else if (Calldate.Value.TimeOfDay > BreakEndTime)
                {
                    timeFromToday = ETM - Calldate.Value.TimeOfDay.TotalMinutes;
                }
            }
            else if (Calldate.Value.Date == ToDate.Date && ToDate.TimeOfDay < endTime && Calldate.Value.TimeOfDay < startTime)
            {
                if (ToDate.TimeOfDay < BreakstartTime)
                {
                    timeFromToday = ToDate.TimeOfDay.TotalMinutes - STM;
                }
                else if (ToDate.TimeOfDay > BreakstartTime && ToDate.TimeOfDay < BreakEndTime)
                {
                    timeFromToday = BSTM - STM;
                }
                else if (ToDate.TimeOfDay > BreakEndTime)
                {
                    timeFromToday = (ToDate.TimeOfDay.TotalMinutes - STM) - (BETM - BSTM);
                }
            }
            else if (Calldate.Value.Date == ToDate.Date && ToDate.TimeOfDay > endTime && Calldate.Value.TimeOfDay < startTime)
            {
                timeFromToday = final;
            }
            if (Calldate.Value.Date < ToDate.Date)
            {
                if (Calldate.Value.TimeOfDay < endTime && Calldate.Value.TimeOfDay > startTime)
                {
                    if (Calldate.Value.TimeOfDay < BreakstartTime)
                    {
                        timeFromCallDate = (ETM - Calldate.Value.TimeOfDay.TotalMinutes) - TotalBreakTime;
                    }
                    else if (Calldate.Value.TimeOfDay > BreakstartTime && Calldate.Value.TimeOfDay < BreakEndTime)
                    {
                        timeFromCallDate = ETM - BETM;
                    }
                    else if (Calldate.Value.TimeOfDay > BreakEndTime)
                    {
                        timeFromCallDate = ETM - Calldate.Value.TimeOfDay.TotalMinutes;
                    }
                    Calldate = Calldate.Value.AddDays(1);
                }
                else if (Calldate.Value.TimeOfDay < startTime)
                {
                    timeFromCallDate = final;
                    Calldate = Calldate.Value.AddDays(1);
                }
                else if (Calldate.Value.TimeOfDay > endTime)
                {
                    Calldate = Calldate.Value.AddDays(1);
                }
            }
            double TotalWorkingHours = 0.0;
            int yearChng = Calldate.Value.Year;
            CallDateDetail detail = new CallDateDetail();
            detail = GetDetailsForDate(Calldate, companyID, connString, LogException);
            while (Calldate.Value.Date < ToDate.Date)
            {
                string day = Calldate.Value.DayOfWeek.ToString();
                int year = Calldate.Value.Year;
                if (Calldate.Value.Year != yearChng)
                {
                    detail = GetDetailsForDate(Calldate, companyID, connString, LogException);
                }
                if (detail.WorkDays.Contains(day))
                {
                    TotalWorkingHours = TotalWorkingHours + Convert.ToDouble(detail.WorkHours);
                }
                Calldate = Calldate.Value.AddDays(1);
                yearChng = Calldate.Value.Year;
            }
            CallDateDetail todaysDetail = new CallDateDetail();
            todaysDetail = GetDetailsForDate(ToDate, companyID, connString, LogException);
            if (ToDate.TimeOfDay > todaysDetail.startTime && ToDate.TimeOfDay < todaysDetail.endTime && ToDate.Date != ActualCallDate.Value.Date)
            {
                if (ToDate.TimeOfDay < todaysDetail.BreakstartTime)
                {
                    timeFromToday = ToDate.TimeOfDay.TotalMinutes - todaysDetail.startTime.TotalMinutes;
                }
                else if (ToDate.TimeOfDay > todaysDetail.BreakstartTime && ToDate.TimeOfDay < todaysDetail.BreakEndTime)
                {
                    timeFromToday = todaysDetail.BreakstartTime.TotalMinutes - todaysDetail.startTime.TotalMinutes;
                }
                else if (ToDate.TimeOfDay > todaysDetail.BreakEndTime)
                {
                    timeFromToday = (ToDate.TimeOfDay.TotalMinutes - todaysDetail.startTime.TotalMinutes) - (todaysDetail.BreakEndTime.TotalMinutes - todaysDetail.BreakstartTime.TotalMinutes);
                }
            }
            if (ToDate.TimeOfDay > todaysDetail.endTime && ToDate.Date != ActualCallDate.Value.Date)
            {
                timeFromToday = todaysDetail.WorkHours;
            }

            List<GNM_CompanyCalender> CompanyCalenderList = new List<GNM_CompanyCalender>();

            using (var conn = new SqlConnection(connString))
            {
                conn.Open();

                string StartTimequery = "SELECT * FROM GNM_CompanyCalender";

                using (var cmd = new SqlCommand(StartTimequery, conn))
                {
                    using (var reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var refMasterDetailObj = new GNM_CompanyCalender
                            {
                                Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                CompanyCalender_ID = reader["CompanyCalender_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CompanyCalender_ID"]),
                                CompanyCalender_EndTime = (TimeSpan)(reader["CompanyCalender_EndTime"] == DBNull.Value ? (TimeSpan?)null : (TimeSpan)reader["CompanyCalender_EndTime"]),
                                Break_StartTime = (TimeSpan)(reader["Break_StartTime"] == DBNull.Value ? (TimeSpan?)null : (TimeSpan)reader["Break_StartTime"]),
                                Break_EndTime = (TimeSpan)(reader["Break_EndTime"] == DBNull.Value ? (TimeSpan?)null : (TimeSpan)reader["Break_EndTime"]),
                                IsGeneralShift = (bool)(reader["IsGeneralShift"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IsGeneralShift"])),
                            };

                            CompanyCalenderList.Add(refMasterDetailObj);
                        }
                    }
                }
            }

            List<GNM_CompanyCalenderHolidays> CompanyCalenderHolidaysList = new List<GNM_CompanyCalenderHolidays>();

            using (var conn = new SqlConnection(connString))
            {
                conn.Open();

                string StartTimequery = "SELECT * FROM GNM_CompanyCalenderHolidays";

                using (var cmd = new SqlCommand(StartTimequery, conn))
                {
                    using (var reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var refMasterDetailObj = new GNM_CompanyCalenderHolidays
                            {
                                CompanyCalender_ID = reader["CompanyCalender_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CompanyCalender_ID"]),
                                CompanyCalenderHoliday_Date = (DateTime)(reader["CompanyCalenderHoliday_Date"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["CompanyCalenderHoliday_Date"]))

                            };

                            CompanyCalenderHolidaysList.Add(refMasterDetailObj);
                        }
                    }
                }
            }

            var Hcount = (from a in CompanyCalenderList
                          join b in CompanyCalenderHolidaysList on a.CompanyCalender_ID equals b.CompanyCalender_ID
                          where a.Company_ID == companyID && b.CompanyCalenderHoliday_Date >= ActualCallDate && b.CompanyCalenderHoliday_Date <= ToDate
                          && a.IsGeneralShift == true
                          select new
                          {
                              b.CompanyCalenderHoliday_Date
                          }).ToList();
            double HDtime = 0.0;
            int holidayYear = 0;
            double holidayWhours = 0;
            foreach (var hday in Hcount)
            {
                CallDateDetail holidayDetail = new CallDateDetail();
                if (holidayYear == 0 || hday.CompanyCalenderHoliday_Date.Year != holidayYear)
                {
                    holidayDetail = GetDetailsForDate(hday.CompanyCalenderHoliday_Date, companyID, connString, LogException);
                }
                holidayWhours = holidayDetail.WorkHours;
                HDtime = HDtime + holidayWhours;
                holidayYear = hday.CompanyCalenderHoliday_Date.Year;
            }
            string h = (timeFromCallDate + TotalWorkingHours + timeFromToday - HDtime).ToString();
            var val = h.Split('.');
            return Convert.ToInt32(val[0]);
        }


        public static int getWorkingHoursForNightShift(DateTime? Calldate, DateTime ToDate, int companyID, CallDateDetail detailForCallDate, string connString, int LogException)
        {
            DateTime? ActualCallDate = Calldate;
            //CallDateDetail detailForCallDate = new CallDateDetail();
            //detailForCallDate = GetDetailsForDate(Calldate, companyID);
            double final = detailForCallDate.WorkHours;
            var startTime = detailForCallDate.startTime;
            var endTime = detailForCallDate.endTime;
            double STM = detailForCallDate.startTimeMinutes;
            double ETM = detailForCallDate.endTimeMinutes;
            var BreakstartTime = detailForCallDate.BreakstartTime;
            var BreakEndTime = detailForCallDate.BreakEndTime;
            double BSTM = detailForCallDate.BreakstartTimeMinutes;
            double BETM = detailForCallDate.BreakEndTimeMinute;
            double TotalBreakTime = BETM - BSTM;
            double timeFromCallDate = 0.0;
            double timeFromToday = 0.0;
            if (Calldate.Value.Date == ToDate.Date)
            {
                if (Calldate.Value.Date == ToDate.Date && (Calldate.Value.TimeOfDay.TotalMinutes + 1440) < ETM && (ToDate.TimeOfDay.TotalMinutes + 1440) < ETM)
                {
                    if ((Calldate.Value.TimeOfDay.TotalMinutes + 1440) < BSTM)
                    {
                        timeFromToday = (ToDate.TimeOfDay.TotalMinutes + 1440) - (Calldate.Value.TimeOfDay.TotalMinutes + 1440) - (BETM - BSTM);
                    }
                    else if ((Calldate.Value.TimeOfDay.TotalMinutes + 1440) > BSTM && (Calldate.Value.TimeOfDay.TotalMinutes + 1440) < BETM)
                    {
                        timeFromToday = ((ToDate.TimeOfDay.TotalMinutes + 1440) - BETM);
                    }
                    else if (Calldate.Value.TimeOfDay.TotalMinutes + 1440 > BETM)
                    {
                        timeFromToday = (ToDate.TimeOfDay.TotalMinutes + 1440) - (Calldate.Value.TimeOfDay.TotalMinutes + 1440);
                    }
                }
                else if (Calldate.Value.Date == ToDate.Date && (Calldate.Value.TimeOfDay.TotalMinutes + 1440) < ETM && (ToDate.TimeOfDay.TotalMinutes + 1440) > ETM)
                {
                    timeFromToday = ETM - (Calldate.Value.TimeOfDay.TotalMinutes + 1440);
                }
                else if (Calldate.Value.Date == ToDate.Date && (Calldate.Value.TimeOfDay.TotalMinutes) < STM && (ToDate.TimeOfDay.TotalMinutes) > STM)
                {
                    timeFromToday = ToDate.TimeOfDay.TotalMinutes - STM;
                }
                else if (Calldate.Value.Date == ToDate.Date && (Calldate.Value.TimeOfDay.TotalMinutes) > STM && (ToDate.TimeOfDay.TotalMinutes) > STM)
                {
                    timeFromToday = ToDate.TimeOfDay.TotalMinutes - Calldate.Value.TimeOfDay.TotalMinutes;
                }
            }
            if (Calldate.Value.Date < ToDate.Date)
            {
                if (Calldate.Value.TimeOfDay.TotalMinutes < ETM && (Calldate.Value.TimeOfDay.TotalMinutes) > STM)
                {
                    if (Calldate.Value.TimeOfDay.TotalMinutes < BSTM)
                    {
                        timeFromCallDate = (ETM - Calldate.Value.TimeOfDay.TotalMinutes) - TotalBreakTime;
                    }
                    else if (Calldate.Value.TimeOfDay.TotalMinutes > BSTM && Calldate.Value.TimeOfDay.TotalMinutes < BETM)
                    {
                        timeFromCallDate = ETM - BETM;
                    }
                    else if (Calldate.Value.TimeOfDay.TotalMinutes > BETM)
                    {
                        timeFromCallDate = ETM - Calldate.Value.TimeOfDay.TotalMinutes;
                    }
                    Calldate = Calldate.Value.AddDays(1);
                }
                else if (Calldate.Value.TimeOfDay.TotalMinutes < STM)
                {
                    if ((Calldate.Value.TimeOfDay.TotalMinutes + 1440) < ETM)
                    {
                        if ((Calldate.Value.TimeOfDay.TotalMinutes) + 1440 < BSTM)
                        {
                            timeFromCallDate = ETM - (Calldate.Value.TimeOfDay.TotalMinutes + 1440) - (BETM - BSTM);
                        }
                        else if ((Calldate.Value.TimeOfDay.TotalMinutes) + 1440 > BSTM && (Calldate.Value.TimeOfDay.TotalMinutes) + 1440 < BETM)
                        {
                            timeFromCallDate = ETM - (BETM);
                        }
                        else if ((Calldate.Value.TimeOfDay.TotalMinutes) + 1440 > BETM)
                        {
                            timeFromCallDate = ETM - (Calldate.Value.TimeOfDay.TotalMinutes + 1440);
                        }
                        //Calldate = Calldate.Value.AddDays(1);
                    }
                    else
                    {
                        timeFromCallDate = final;
                        Calldate = Calldate.Value.AddDays(1);
                    }
                }
                else if (Calldate.Value.TimeOfDay.TotalMinutes > ETM)
                {
                    Calldate = Calldate.Value.AddDays(1);
                }
            }
            double TotalWorkingHours = 0.0;
            int yearChng = Calldate.Value.Year;
            CallDateDetail detail = new CallDateDetail();
            detail = GetDetailsForDate(Calldate, companyID, connString, LogException);
            double temp = 0.0;
            while (Calldate.Value.Date < ToDate.Date)
            {
                string day = Calldate.Value.DayOfWeek.ToString();
                int year = Calldate.Value.Year;
                if (Calldate.Value.Year != yearChng)
                {
                    detail = GetDetailsForDate(Calldate, companyID, connString, LogException);
                }
                if (detail.WorkDays.Contains(day))
                {
                    TotalWorkingHours = TotalWorkingHours + Convert.ToDouble(detail.WorkHours);
                    temp = detail.WorkHours;
                }
                Calldate = Calldate.Value.AddDays(1);
                yearChng = Calldate.Value.Year;
            }
            CallDateDetail todaysDetail = new CallDateDetail();
            todaysDetail = GetDetailsForDate(ToDate, companyID, connString, LogException);
            if (ToDate.TimeOfDay.TotalMinutes > todaysDetail.startTimeMinutes && ToDate.TimeOfDay.TotalMinutes < todaysDetail.endTimeMinutes && ToDate.Date != ActualCallDate.Value.Date)
            {
                if (ToDate.TimeOfDay.TotalMinutes < todaysDetail.BreakstartTimeMinutes)
                {
                    timeFromToday = ToDate.TimeOfDay.TotalMinutes - todaysDetail.startTimeMinutes;
                }
                else if (ToDate.TimeOfDay.TotalMinutes > todaysDetail.BreakstartTimeMinutes && ToDate.TimeOfDay.TotalMinutes < todaysDetail.BreakEndTimeMinute)
                {
                    timeFromToday = todaysDetail.BreakstartTimeMinutes - todaysDetail.startTimeMinutes;
                }
                else if (ToDate.TimeOfDay.TotalMinutes > todaysDetail.BreakEndTimeMinute)
                {
                    timeFromToday = (ToDate.TimeOfDay.TotalMinutes - todaysDetail.startTimeMinutes) - (todaysDetail.BreakEndTimeMinute - todaysDetail.BreakstartTimeMinutes);
                }
            }
            else if ((ToDate.TimeOfDay.TotalMinutes + 1440) > todaysDetail.startTimeMinutes && (ToDate.TimeOfDay.TotalMinutes + 1440) < todaysDetail.endTimeMinutes && ToDate.Date != ActualCallDate.Value.Date)
            {
                timeFromToday = ((ToDate.TimeOfDay.TotalMinutes + 1440) - todaysDetail.startTimeMinutes) - (todaysDetail.BreakEndTimeMinute - todaysDetail.BreakstartTimeMinutes) - temp;
                DateTime Tday = ActualCallDate.Value.AddDays(1);
                if (Tday.Date == ToDate.Date)
                {
                    timeFromToday = timeFromToday - todaysDetail.WorkHours;
                }
            }

            List<GNM_CompanyCalender> CompanyCalenderList = new List<GNM_CompanyCalender>();

            using (var conn = new SqlConnection(connString))
            {
                conn.Open();

                string StartTimequery = "SELECT * FROM GNM_CompanyCalender";

                using (var cmd = new SqlCommand(StartTimequery, conn))
                {
                    using (var reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var refMasterDetailObj = new GNM_CompanyCalender
                            {
                                Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                CompanyCalender_ID = reader["CompanyCalender_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CompanyCalender_ID"]),
                                CompanyCalender_EndTime = (TimeSpan)(reader["CompanyCalender_EndTime"] == DBNull.Value ? (TimeSpan?)null : (TimeSpan)reader["CompanyCalender_EndTime"]),
                                Break_StartTime = (TimeSpan)(reader["Break_StartTime"] == DBNull.Value ? (TimeSpan?)null : (TimeSpan)reader["Break_StartTime"]),
                                Break_EndTime = (TimeSpan)(reader["Break_EndTime"] == DBNull.Value ? (TimeSpan?)null : (TimeSpan)reader["Break_EndTime"]),
                                IsGeneralShift = (bool)(reader["IsGeneralShift"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IsGeneralShift"])),
                            };

                            CompanyCalenderList.Add(refMasterDetailObj);
                        }
                    }
                }
            }

            List<GNM_CompanyCalenderHolidays> CompanyCalenderHolidaysList = new List<GNM_CompanyCalenderHolidays>();

            using (var conn = new SqlConnection(connString))
            {
                conn.Open();

                string StartTimequery = "SELECT * FROM GNM_CompanyCalenderHolidays";

                using (var cmd = new SqlCommand(StartTimequery, conn))
                {
                    using (var reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var refMasterDetailObj = new GNM_CompanyCalenderHolidays
                            {
                                CompanyCalender_ID = reader["CompanyCalender_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CompanyCalender_ID"]),
                                CompanyCalenderHoliday_Date = (DateTime)(reader["CompanyCalenderHoliday_Date"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["CompanyCalenderHoliday_Date"]))

                            };

                            CompanyCalenderHolidaysList.Add(refMasterDetailObj);
                        }
                    }
                }
            }

            var Hcount = (from a in CompanyCalenderList
                          join b in CompanyCalenderHolidaysList on a.CompanyCalender_ID equals b.CompanyCalender_ID
                          where a.Company_ID == companyID && b.CompanyCalenderHoliday_Date >= ActualCallDate && b.CompanyCalenderHoliday_Date <= ToDate
                          && a.IsGeneralShift == true
                          select new { b.CompanyCalenderHoliday_Date }).ToList();
            double HDtime = 0.0;
            int holidayYear = 0;
            double holidayWhours = 0;
            foreach (var hday in Hcount)
            {
                CallDateDetail holidayDetail = new CallDateDetail();
                if (holidayYear == 0 || hday.CompanyCalenderHoliday_Date.Year != holidayYear)
                {
                    holidayDetail = GetDetailsForDate(hday.CompanyCalenderHoliday_Date, companyID, connString, LogException);
                }
                holidayWhours = holidayDetail.WorkHours;
                HDtime = HDtime + holidayWhours;
                holidayYear = hday.CompanyCalenderHoliday_Date.Year;
            }

            string h = (timeFromCallDate + TotalWorkingHours + timeFromToday - HDtime).ToString();
            var val = h.Split('.');
            return Convert.ToInt32(val[0]);
        }


        public static int GetSLATime(decimal SLAHours)
        {
            string SLA = SLAHours.ToString();
            if (SLA.Contains('.'))
            {
                var arr = SLA.Split('.');
                int a = Convert.ToInt32(arr[0]);
                int b = a * 60;
                int c = Convert.ToInt32(arr[1]);
                return (b + c);
            }
            else
            {
                return (Convert.ToInt32(SLAHours) * 60);
            }
        }

        public static string SplitAndGet(string SLATime)
        {
            double a = Convert.ToDouble(SLATime);
            string InTimeFormat = Math.Floor(a / 60).ToString() + ":" + (Math.Floor(a % 60).ToString().Length == 1 ? "0" + Math.Floor(a % 60).ToString() : Math.Floor(a % 60).ToString());
            return InTimeFormat;
        }

        public static bool CheckPartySpecific(int Party_ID, int CallComplexity_ID, int? CallPriority_ID, int CompanyID, string connString)
        {
            List<HD_ServiceLevelAgreement> ServiceLevelAgreementList = new List<HD_ServiceLevelAgreement>();

            using (var conn = new SqlConnection(connString))
            {
                conn.Open();

                string StartTimequery = "SELECT * FROM HD_ServiceLevelAgreement";

                using (var cmd = new SqlCommand(StartTimequery, conn))
                {
                    using (var reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var refMasterDetailObj = new HD_ServiceLevelAgreement
                            {
                                Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                Party_ID = reader["Party_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Party_ID"]),
                                CallComplexity_ID = reader["CallComplexity_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CallComplexity_ID"]),
                                CallPriority_ID = reader["CallPriority_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CallPriority_ID"])
                            };

                            ServiceLevelAgreementList.Add(refMasterDetailObj);
                        }
                    }
                }
            }
            List<HD_ServiceLevelAgreement> Agreement = ServiceLevelAgreementList.Where(sr => sr.Company_ID == CompanyID && sr.Party_ID == Party_ID && sr.CallComplexity_ID == CallComplexity_ID && sr.CallPriority_ID == CallPriority_ID).ToList();
            if (Agreement.Count() != 0) return true; else return false;
        }

        #region ::: To Select:::
        /// <summary>
        /// To Select 
        /// </summary>
        public static IActionResult Select(SLAExceeded_SelectList OBJ, string connString, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters)
        {
            var jsonData = default(dynamic);
            string frmdate = OBJ.FromDate;
            string todate = OBJ.ToDate;
            string all = CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "all").ToString();
            IQueryable<CaseDetailsForSLA> IQSRdata = null;
            try
            {
                int Count = 0; int Total = 0;
                int Language_ID = Convert.ToInt32(OBJ.UserLanguageID);

                string todateFromSLAreport = string.Empty;
                string fromdateFromSLAreport = string.Empty;
                string SelectedBrachinSLAReport = string.Empty;
                string SLAType = string.Empty;
                string SLAFilterType = string.Empty;
                todateFromSLAreport = (todate == null || todate == "") ? all : todate;
                fromdateFromSLAreport = (frmdate == null || frmdate == "") ? all : frmdate;
                //IEnumerable<GNM_Branch> branchData = compEnt.GNM_Branch.Where(a => a.Branch_Active == true && a.Company_ID == Company_ID);
                //SelectedBrachinSLAReport = branch == -1 ? "All" : branchData.Where(a => a.Branch_ID == branch).Select(a => a.Branch_Name).FirstOrDefault();



                if (OBJ.UserLanguageCode.ToString() == OBJ.GeneralLanguageCode.ToString())
                {
                    if (OBJ.mode == "checked")
                    {
                        SLAType = "exceededtime";
                        SLAFilterType = "SLAExceeded";


                    }
                    else
                    {
                        SLAType = "TimeRemaining";
                        SLAFilterType = "SLAWithin";


                    }
                }
                else//Non English
                {

                    if (OBJ.mode == "checked")
                    {
                        SLAType = "exceededtime";
                        SLAFilterType = "SLAExceeded";

                    }
                    else
                    {
                        SLAType = "TimeRemaining";
                        SLAFilterType = "SLAWithin";

                    }
                }
                IQSRdata = GetSLAExceeded(OBJ.mode, Convert.ToInt32(OBJ.Type), frmdate, todate, OBJ.BranchData, OBJ.CompanyData, OBJ.BranchID, OBJ.Company_ID, OBJ.UserCulture,
            OBJ.UserLanguageID, OBJ.GeneralLanguageID, OBJ.UserLanguageCode, OBJ.GeneralLanguageCode, connString, LogException);
                Count = IQSRdata.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                IQSRdata = IQSRdata.OrderByField<CaseDetailsForSLA>(sidx, sord);
                //  Session["CaseDetailsForSLAExport"] = IQSRdata.ToList();
                jsonData = new
                {
                    total = Total,
                    page = page,
                    rows = (from a in IQSRdata.AsEnumerable()
                            select new
                            {
                                Region = a.Region,
                                CompanyName = a.CompanyName,
                                BranchName = a.BranchName,
                                CaseNumber = "<span class='ServiceRequest' key='" + a.ServiceRequestID + "' style='color:blue;text-decoration:underline;cursor:pointer'>" + a.CaseNumber + "</span>",
                                Status = a.Status,
                                FunctionGroup = a.FunctionGroup,
                                Model = a.Model,
                                SerialNumber = a.SerialNumber,
                                IssueArea = a.IssueArea,
                                IssueSubArea = a.IssueSubArea,
                                SLATime = SplitAndGet(a.SLATime.ToString())
                            }).ToList().Paginate(page, rows),
                    records = Count,
                    todateFromSLAreport,
                    fromdateFromSLAreport,
                    SelectedBrachinSLAReport,
                    SLAType,
                    SLAFilterType,
                    OBJ.mode,
                    OBJ.Type,
                    frmdate,
                    todate


                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: Select All SLA Excedded:::
        /// <summary>
        /// Select All Parts Order Cancellation
        /// </summary>
        private static IQueryable<CaseDetailsForSLA> GetSLAExceeded(string mode, int Type, string frmdate, string todate, string BranchData, string CompanyData, string BranchID, string Company_ID, string UserCulture,
            int UserLanguageID, int GeneralLanguageID, string UserLanguageCode, string GeneralLanguageCode, string connString, int LogException)
        {
            IEnumerable<CaseDetailsForSLA> SRData = null;
            //string frmdate = Request.Params["FromDate"];
            //string todate = Request.Params["ToDate"];
            if (frmdate == null)
            {
                frmdate = string.Empty;

            }
            if (todate == null)
            {
                todate = string.Empty;

            }
            Branch branchs = new Branch();
            if (BranchData != null && BranchData != "")
            {
                branchs = JObject.Parse(Common.DecryptString(Common.DecryptString(BranchData))).ToObject<Branch>(); //~Manju M
            }

            if (BranchData != null && BranchData != "")
            {

                for (int i = 0; i < branchs.Branchs.Count; i++)
                {
                    BranchID = BranchID + branchs.Branchs[i].ID + ", ";
                }
                BranchID = BranchID.Remove(BranchID.LastIndexOf(','), 1);
                //Session["HD_Tr_AverageResponseTime_Branch_ID"] = BranchID;
            }
            Company companys = new Company();
            if (CompanyData != null && CompanyData != "")
            {
                companys = JObject.Parse(Common.DecryptString(Common.DecryptString(CompanyData))).ToObject<Company>(); //~Manju M
            }

            if (BranchData != null && BranchData != "")
            {

                for (int i = 0; i < companys.Companys.Count; i++)
                {
                    Company_ID = Company_ID + companys.Companys[i].ID + ", ";
                }
                Company_ID = Company_ID.Remove(Company_ID.LastIndexOf(','), 1);
                //Session["CompanyID"] = Company_ID;

            }

            string all = CommonFunctionalities.GetResourceString(UserCulture.ToString(), "all").ToString();
            IQueryable<CaseDetailsForSLA> IQSRdata = null;
            try
            {
                int Count = 0; int Total = 0;
                int Language_ID = Convert.ToInt32(UserLanguageID);
                int generalLanguageID = Convert.ToInt32(GeneralLanguageID);
                //int Company_ID = Convert.ToInt32(Session["Company_ID"]);
                //Session["todateFromSLAreport"] = (todate == null || todate == "") ? all : todate;
                //Session["fromdateFromSLAreport"] = (frmdate == null || frmdate == "") ? all : frmdate;
                //IEnumerable<GNM_Branch> branchData = compEnt.GNM_Branch.Where(a => a.Branch_Active == true && a.Company_ID == Company_ID);
                //Session["SelectedBrachinSLAReport"] = branch == -1 ? "All" : branchData.Where(a => a.Branch_ID == branch).Select(a => a.Branch_Name).FirstOrDefault();
                List<HD_ServiceRequest> SRAll = null;
                List<HD_ServiceRequest> SRAllList = new List<HD_ServiceRequest>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM HD_ServiceRequest";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new HD_ServiceRequest
                                {
                                    Company_ID = reader["Company_ID"] != DBNull.Value ? Convert.ToInt32(reader["Company_ID"]) : 0,
                                    Branch_ID = reader["Branch_ID"] != DBNull.Value ? Convert.ToInt32(reader["Branch_ID"]) : 0,
                                    Model_ID = reader["Model_ID"] != DBNull.Value ? Convert.ToInt32(reader["Model_ID"]) : 0,
                                    Party_ID = reader["Party_ID"] != DBNull.Value ? Convert.ToInt32(reader["Party_ID"]) : 0,
                                    IssueArea_ID = reader["IssueArea_ID"] != DBNull.Value ? Convert.ToInt32(reader["IssueArea_ID"]) : 0,
                                    IssueSubArea_ID = reader["IssueSubArea_ID"] != DBNull.Value ? Convert.ToInt32(reader["IssueSubArea_ID"]) : 0,
                                    ServiceRequest_ID = reader["ServiceRequest_ID"] != DBNull.Value ? Convert.ToInt32(reader["ServiceRequest_ID"]) : 0,
                                    FunctionGroup_ID = reader["FunctionGroup_ID"] != DBNull.Value ? Convert.ToInt32(reader["FunctionGroup_ID"]) : 0,
                                    CallStatus_ID = reader["CallStatus_ID"] != DBNull.Value ? Convert.ToInt32(reader["CallStatus_ID"]) : 0,
                                    CallComplexity_ID = reader["CallComplexity_ID"] != DBNull.Value ? Convert.ToInt32(reader["CallComplexity_ID"]) : 0,
                                    CallPriority_ID = reader["CallPriority_ID"] != DBNull.Value ? Convert.ToInt32(reader["CallPriority_ID"]) : 0,
                                    ServiceRequestNumber = reader["ServiceRequestNumber"] != DBNull.Value ? reader["ServiceRequestNumber"].ToString() : null,
                                    SerialNumber = reader["SerialNumber"] != DBNull.Value ? reader["SerialNumber"].ToString() : null,
                                    CallDateAndTime = reader["CallDateAndTime"] != DBNull.Value ? Convert.ToDateTime(reader["CallDateAndTime"]) : DateTime.MinValue,
                                    CallClosureDateAndTime = reader["CallClosureDateAndTime"] != DBNull.Value ? Convert.ToDateTime(reader["CallClosureDateAndTime"]) : (DateTime?)null
                                };

                                SRAllList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }
                SRAll = SRAllList.ToList();
                if (frmdate != string.Empty && todate != string.Empty)
                {
                    DateTime FromDate = Convert.ToDateTime(frmdate);
                    DateTime ToDate = Convert.ToDateTime(todate).AddDays(1);
                    //SRAll =   (branch==-1)?(SRClient.HD_ServiceRequest.Where(a => (a.CallDateAndTime >= FromDate && a.CallDateAndTime < ToDate) && a.Company_ID == Company_ID).ToList()):(SRClient.HD_ServiceRequest.Where(a => (a.CallDateAndTime >= FromDate && a.CallDateAndTime < ToDate) && a.Company_ID == Company_ID && a.Branch_ID==branch).ToList());
                    if (BranchID == "")
                    {
                        SRAll = (from a in SRAll
                                 join b in companys.Companys on a.Company_ID equals b.ID
                                 where a.CallDateAndTime >= FromDate && a.CallDateAndTime < ToDate
                                 select a).ToList();
                    }
                    else
                    {
                        SRAll = (from a in SRAll
                                 join b in companys.Companys on a.Company_ID equals b.ID
                                 join BR in branchs.Branchs on a.Branch_ID equals BR.ID
                                 where a.CallDateAndTime >= FromDate && a.CallDateAndTime < ToDate
                                 select a).ToList();
                    }

                }
                else if (frmdate != string.Empty && todate == string.Empty)
                {
                    DateTime FromDate = Convert.ToDateTime(frmdate);
                    //SRAll = (branch == -1) ? (SRClient.HD_ServiceRequest.Where(a => a.CallDateAndTime >= FromDate && a.Company_ID == Company_ID).ToList()) : (SRClient.HD_ServiceRequest.Where(a => a.CallDateAndTime >= FromDate && a.Company_ID == Company_ID && a.Branch_ID==branch).ToList());
                    if (BranchID == "")
                    {
                        SRAll = (from a in SRAll
                                 join b in companys.Companys on a.Company_ID equals b.ID
                                 where a.CallDateAndTime >= FromDate
                                 select a).ToList();
                    }
                    else
                    {
                        SRAll = (from a in SRAll
                                 join b in companys.Companys on a.Company_ID equals b.ID
                                 join BR in branchs.Branchs on a.Branch_ID equals BR.ID
                                 where a.CallDateAndTime >= FromDate
                                 select a).ToList();
                    }
                }
                else if (frmdate == string.Empty && todate != string.Empty)
                {
                    DateTime ToDate = Convert.ToDateTime(todate).AddDays(1);
                    //SRAll = (branch == -1) ? (SRClient.HD_ServiceRequest.Where(a => a.CallDateAndTime < ToDate && a.Company_ID == Company_ID).ToList()) : (SRClient.HD_ServiceRequest.Where(a => a.CallDateAndTime < ToDate && a.Company_ID == Company_ID && a.Branch_ID==branch).ToList());
                    if (BranchID == "")
                    {
                        SRAll = (from a in SRAll
                                 join b in companys.Companys on a.Company_ID equals b.ID
                                 where a.CallDateAndTime < ToDate
                                 select a).ToList();
                    }
                    else
                    {
                        SRAll = (from a in SRAll
                                 join b in companys.Companys on a.Company_ID equals b.ID
                                 join BR in branchs.Branchs on a.Branch_ID equals BR.ID
                                 where a.CallDateAndTime < ToDate
                                 select a).ToList();
                    }
                }
                else if (frmdate == string.Empty && todate == string.Empty)
                {
                    //SRAll = (branch == -1) ? (SRClient.HD_ServiceRequest.Where(a => a.Company_ID == Company_ID).ToList()) : (SRClient.HD_ServiceRequest.Where(a => a.Company_ID == Company_ID && a.Branch_ID == branch).ToList());
                    if (BranchID == "")
                    {
                        SRAll = (from a in SRAll
                                 join b in companys.Companys on a.Company_ID equals b.ID
                                 select a).ToList();
                    }
                    else
                    {
                        SRAll = (from a in SRAll
                                 join b in companys.Companys on a.Company_ID equals b.ID
                                 join BR in branchs.Branchs on a.Branch_ID equals BR.ID
                                 select a).ToList();
                    }

                }

                List<GNM_RefMaster> refMasterDetail = new List<GNM_RefMaster>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_RefMaster";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_RefMaster
                                {
                                    RefMaster_ID = reader["RefMaster_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["RefMaster_ID"]),
                                    RefMaster_Name = reader["RefMaster_Name"] == DBNull.Value ? null : reader["RefMaster_Name"].ToString(),
                                };

                                refMasterDetail.Add(refMasterDetailObj);
                            }
                        }
                    }
                }
                List<GNM_Model> Model_Detail = new List<GNM_Model>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_Model";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_Model
                                {
                                    Model_IsActive = (bool)(reader["Model_IsActive"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["Model_IsActive"])),
                                    Model_ID = reader["Model_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Model_ID"]),
                                };

                                Model_Detail.Add(refMasterDetailObj);
                            }
                        }
                    }
                }
                List<GNM_ModelLocale> ModelLocale_Detail = new List<GNM_ModelLocale>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT ML.Language_ID, ML.Model_ID, M.Model_IsActive FROM GNM_ModelLocale ML JOIN GNM_Model M ON ML.Model_ID=M.Model_ID";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_ModelLocale
                                {
                                    Model_IsActive = (bool)(reader["Model_IsActive"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["Model_IsActive"])),
                                    Model_ID = reader["Model_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Model_ID"]),
                                    Language_ID = reader["Language_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Language_ID"]),
                                };

                                ModelLocale_Detail.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                List<GNM_RefMasterDetail> refDetail = new List<GNM_RefMasterDetail>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_RefMasterDetail";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_RefMasterDetail
                                {
                                    RefMaster_ID = reader["RefMaster_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["RefMaster_ID"]),
                                    RefMasterDetail_ID = reader["RefMasterDetail_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    RefMasterDetail_IsActive = (bool)(reader["RefMasterDetail_IsActive"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["RefMasterDetail_IsActive"])),
                                };

                                refDetail.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                List<GNM_RefMasterDetailLocale> RefMasterDetailLocaleList = new List<GNM_RefMasterDetailLocale>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT RL.RefMaster_ID, RL.RefMasterDetail_ID, RL.Language_ID, R.RefMasterDetail_IsActive FROM GNM_RefMasterDetailLocale RL JOIN GNM_RefMasterDetail R ON RL.RefMasterDetail_ID=R.RefMasterDetail_ID";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_RefMasterDetailLocale
                                {
                                    RefMaster_ID = reader["RefMaster_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["RefMaster_ID"]),
                                    RefMasterDetail_ID = reader["RefMasterDetail_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                    Language_ID = reader["Language_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Language_ID"]),
                                    RefMasterDetail_IsActive = (bool)(reader["RefMasterDetail_IsActive"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["RefMasterDetail_IsActive"])),
                                };

                                RefMasterDetailLocaleList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                List<HD_IssueSubArea> IssueSubAreaList = new List<HD_IssueSubArea>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM HD_IssueSubArea";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new HD_IssueSubArea
                                {
                                    IssueSubArea_IsActive = (bool)(reader["IssueSubArea_IsActive"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IssueSubArea_IsActive"])),
                                };

                                IssueSubAreaList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                List<HD_IssueSubAreaLocale> IssueSubAreaLocaleList = new List<HD_IssueSubAreaLocale>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT IL.Language_ID, I.IssueSubArea_IsActive FROM HD_IssueSubAreaLocale IL JOIN HD_IssueSubArea I ON IL.IssueSubArea_ID=I.IssueSubArea_ID";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new HD_IssueSubAreaLocale
                                {
                                    IssueSubArea_IsActive = (bool)(reader["IssueSubArea_IsActive"] == DBNull.Value ? (bool?)null : Convert.ToBoolean(reader["IssueSubArea_IsActive"])),
                                    Language_ID = reader["Language_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Language_ID"]),
                                };

                                IssueSubAreaLocaleList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                List<WF_WFStepStatus> WFStepStatusList = new List<WF_WFStepStatus>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_WFStepStatus";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new WF_WFStepStatus
                                {
                                    WFStepStatus_ID = reader["WFStepStatus_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["WFStepStatus_ID"]),
                                    WFStepStatus_Nm = reader["WFStepStatus_Nm"] == DBNull.Value ? null : reader["WFStepStatus_Nm"].ToString(),
                                };

                                WFStepStatusList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                List<HD_ServiceLevelAgreement> ServiceLevelAgreementList = new List<HD_ServiceLevelAgreement>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM HD_ServiceLevelAgreement";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new HD_ServiceLevelAgreement
                                {
                                    CallPriority_ID = reader["CallPriority_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CallPriority_ID"]),
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    CallComplexity_ID = reader["CallComplexity_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CallComplexity_ID"]),
                                    Party_ID = reader["Party_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Party_ID"]),
                                    ServiceLevelAgreement_Hours = (decimal)(reader["ServiceLevelAgreement_Hours"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["ServiceLevelAgreement_Hours"])),
                                };

                                ServiceLevelAgreementList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                List<GNM_Company> CompanyList = new List<GNM_Company>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_Company";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_Company
                                {
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    Company_Name = reader["Company_Name"] == DBNull.Value ? null : reader["Company_Name"].ToString(),
                                };

                                CompanyList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                List<GNM_Branch> BranchList = new List<GNM_Branch>();

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    string query = "SELECT * FROM GNM_Branch";

                    using (var cmd = new SqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new GNM_Branch
                                {
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    Branch_Name = reader["Branch_Name"] == DBNull.Value ? null : reader["Branch_Name"].ToString(),
                                };

                                BranchList.Add(refMasterDetailObj);
                            }
                        }
                    }
                }

                int FGRefid = refMasterDetail.Where(a => a.RefMaster_Name == "FUNCTIONGROUP").Select(a => a.RefMaster_ID).FirstOrDefault();
                int IAid = refMasterDetail.Where(a => a.RefMaster_Name == "ISSUEAREA").Select(a => a.RefMaster_ID).FirstOrDefault();
                List<GNM_Model> model = Model_Detail.Where(a => a.Model_IsActive == true).ToList();
                List<GNM_RefMasterDetail> IsArlist = refDetail.Where(a => a.RefMaster_ID == IAid && a.RefMasterDetail_IsActive == true).ToList();
                IsArlist = (from a in IsArlist join b in companys.Companys on a.Company_ID equals b.ID select a).ToList();

                List<GNM_RefMasterDetail> FGlist = refDetail.Where(a => a.RefMaster_ID == FGRefid && a.RefMasterDetail_IsActive == true).ToList();
                FGlist = (from a in FGlist join b in companys.Companys on a.Company_ID equals b.ID select a).ToList();
                List<HD_IssueSubArea> IsSuArList = IssueSubAreaList.Where(a => a.IssueSubArea_IsActive == true).ToList();
                int ClosedStatusID = WFStepStatusList.Where(st => st.WFStepStatus_Nm.ToUpper() == "CLOSED").Select(s => s.WFStepStatus_ID).FirstOrDefault();
                List<HD_ServiceLevelAgreement> Agreement = ServiceLevelAgreementList.ToList();
                Agreement = (from a in Agreement join b in companys.Companys on a.Company_ID equals b.ID select a).ToList();

                DateTime CurrentDateTime = DateTime.Now;

                if (UserLanguageCode.ToString() == GeneralLanguageCode.ToString())
                {
                    if (mode == "checked")
                    {

                        SRData = (from SR in SRAll
                                  join AG in Agreement on SR.CallPriority_ID equals AG.CallPriority_ID
                                  join a in SRAll on SR.ServiceRequest_ID equals a.ServiceRequest_ID
                                  join c in model on a.Model_ID equals c.Model_ID into ModelDetail
                                  from Modelfinal in ModelDetail.DefaultIfEmpty(new GNM_Model { Model_Name = "" })
                                  join d in IsArlist on a.IssueArea_ID equals d.RefMasterDetail_ID into IAList
                                  from IAfinal in IAList.DefaultIfEmpty(new GNM_RefMasterDetail { RefMasterDetail_Name = "" })
                                  join e in IsSuArList on a.IssueSubArea_ID equals e.IssueSubArea_ID into ISAList
                                  from ISAfinal in ISAList.DefaultIfEmpty(new HD_IssueSubArea { IssueSubArea_Description = "" })
                                  join f in FGlist on SR.FunctionGroup_ID equals f.RefMasterDetail_ID into FGdata
                                  from FGfinal in FGdata.DefaultIfEmpty(new GNM_RefMasterDetail { RefMasterDetail_Name = "" })
                                  join f in WFStepStatusList on a.CallStatus_ID equals f.WFStepStatus_ID
                                  where SR.CallComplexity_ID == AG.CallComplexity_ID
                                  &&
                                  ((CheckPartySpecific(SR.Party_ID, SR.CallComplexity_ID, SR.CallPriority_ID, SR.Company_ID, connString)) ? SR.Party_ID == AG.Party_ID : AG.Party_ID == null)
                                  select new CaseDetailsForSLA()
                                  {
                                      Region = CommonFunctionalities.getRegionName(Convert.ToInt32(Language_ID), Convert.ToInt32(generalLanguageID), SR.Branch_ID, connString, LogException),
                                      ServiceRequestID = a.ServiceRequest_ID,
                                      CaseNumber = a.ServiceRequestNumber,
                                      Status = f.WFStepStatus_Nm,
                                      FunctionGroup = FGfinal.RefMasterDetail_Name,
                                      Model = Modelfinal.Model_Name,
                                      SerialNumber = (a.SerialNumber == null ? string.Empty : a.SerialNumber),
                                      IssueArea = IAfinal.RefMasterDetail_Name,
                                      IssueSubArea = ISAfinal.IssueSubArea_Description,
                                      //WorkingHours = getWorkingHours(SR.CallDateAndTime, Convert.ToDateTime(((SR.CallStatus_ID != ClosedStatusID) ? (CurrentDateTime) : (SR.CallClosureDateAndTime))), Company_ID),
                                      WorkingHours = Type == 1 ? getWorkingHours(SR.CallDateAndTime, Convert.ToDateTime(((SR.CallStatus_ID != ClosedStatusID) ? (CurrentDateTime) : (SR.CallClosureDateAndTime))), SR.Company_ID, connString, LogException) : Convert.ToInt32((a.CallClosureDateAndTime == null ? CurrentDateTime : a.CallClosureDateAndTime).Value.Subtract(a.CallDateAndTime).TotalMinutes),
                                      ServiceLevelAgreement_Hours = GetSLATime(AG.ServiceLevelAgreement_Hours),
                                      Company_ID = a.Company_ID,
                                      Branch_ID = a.Branch_ID
                                  });

                        SRData = (from a in SRData
                                  where a.WorkingHours > a.ServiceLevelAgreement_Hours
                                  select new CaseDetailsForSLA()
                                  {
                                      Region = a.Region,
                                      ServiceRequestID = a.ServiceRequestID,
                                      CaseNumber = a.CaseNumber,
                                      Status = a.Status,
                                      FunctionGroup = a.FunctionGroup,
                                      Model = a.Model,
                                      SerialNumber = a.SerialNumber,
                                      IssueArea = a.IssueArea,
                                      IssueSubArea = a.IssueSubArea,
                                      SLATime = a.WorkingHours - a.ServiceLevelAgreement_Hours,
                                      CompanyName = CompanyList.Where(b => b.Company_ID == a.Company_ID).Select(j => j.Company_Name).FirstOrDefault(),
                                      BranchName = BranchList.Where(b => b.Branch_ID == a.Branch_ID).Select(j => j.Branch_Name).FirstOrDefault()
                                  });

                    }
                    else
                    {

                        SRData = (from SR in SRAll
                                  join AG in Agreement on SR.CallPriority_ID equals AG.CallPriority_ID
                                  join a in SRAll on SR.ServiceRequest_ID equals a.ServiceRequest_ID
                                  join c in model on a.Model_ID equals c.Model_ID into ModelDetail
                                  from Modelfinal in ModelDetail.DefaultIfEmpty(new GNM_Model { Model_Name = "" })
                                  join d in IsArlist on a.IssueArea_ID equals d.RefMasterDetail_ID into IAList
                                  from IAfinal in IAList.DefaultIfEmpty(new GNM_RefMasterDetail { RefMasterDetail_Name = "" })
                                  join e in IsSuArList on a.IssueSubArea_ID equals e.IssueSubArea_ID into ISAList
                                  from ISAfinal in ISAList.DefaultIfEmpty(new HD_IssueSubArea { IssueSubArea_Description = "" })
                                  join f in FGlist on SR.FunctionGroup_ID equals f.RefMasterDetail_ID into FGdata
                                  from FGfinal in FGdata.DefaultIfEmpty(new GNM_RefMasterDetail { RefMasterDetail_Name = "" })
                                  join f in WFStepStatusList on a.CallStatus_ID equals f.WFStepStatus_ID
                                  where SR.CallComplexity_ID == AG.CallComplexity_ID
                                  &&
                                  ((CheckPartySpecific(SR.Party_ID, SR.CallComplexity_ID, SR.CallPriority_ID, SR.Company_ID, connString)) ? SR.Party_ID == AG.Party_ID : AG.Party_ID == null)
                                  select new CaseDetailsForSLA()
                                  {
                                      Region = CommonFunctionalities.getRegionName(Convert.ToInt32(Language_ID), Convert.ToInt32(generalLanguageID), SR.Branch_ID, connString, LogException),
                                      ServiceRequestID = a.ServiceRequest_ID,
                                      CaseNumber = a.ServiceRequestNumber,
                                      Status = f.WFStepStatus_Nm,
                                      FunctionGroup = FGfinal.RefMasterDetail_Name,
                                      Model = Modelfinal.Model_Name,
                                      SerialNumber = (a.SerialNumber == null ? string.Empty : a.SerialNumber),
                                      IssueArea = IAfinal.RefMasterDetail_Name,
                                      IssueSubArea = ISAfinal.IssueSubArea_Description,
                                      //WorkingHours = getWorkingHours(SR.CallDateAndTime, Convert.ToDateTime(((SR.CallStatus_ID != ClosedStatusID) ? (CurrentDateTime) : (SR.CallClosureDateAndTime))), Company_ID),
                                      WorkingHours = Type == 1 ? getWorkingHours(SR.CallDateAndTime, Convert.ToDateTime(((SR.CallStatus_ID != ClosedStatusID) ? (CurrentDateTime) : (SR.CallClosureDateAndTime))), SR.Company_ID, connString, LogException) : Convert.ToInt32((a.CallClosureDateAndTime == null ? CurrentDateTime : a.CallClosureDateAndTime).Value.Subtract(a.CallDateAndTime).TotalMinutes),
                                      ServiceLevelAgreement_Hours = GetSLATime(AG.ServiceLevelAgreement_Hours),
                                      Company_ID = a.Company_ID,
                                      Branch_ID = a.Branch_ID
                                  });

                        SRData = (from a in SRData
                                  where a.WorkingHours < a.ServiceLevelAgreement_Hours
                                  select new CaseDetailsForSLA()
                                  {
                                      Region = a.Region,
                                      ServiceRequestID = a.ServiceRequestID,
                                      CaseNumber = a.CaseNumber,
                                      Status = a.Status,
                                      FunctionGroup = a.FunctionGroup,
                                      Model = a.Model,
                                      SerialNumber = a.SerialNumber,
                                      IssueArea = a.IssueArea,
                                      IssueSubArea = a.IssueSubArea,
                                      SLATime = a.ServiceLevelAgreement_Hours - a.WorkingHours,
                                      CompanyName = CompanyList.Where(b => b.Company_ID == a.Company_ID).Select(j => j.Company_Name).FirstOrDefault(),
                                      BranchName = BranchList.Where(b => b.Branch_ID == a.Branch_ID).Select(j => j.Branch_Name).FirstOrDefault()
                                  });

                    }
                }
                else//Non English
                {
                    List<GNM_RefMasterDetailLocale> FGLocalelist = RefMasterDetailLocaleList.Where(a => a.RefMaster_ID == FGRefid && a.Language_ID == Language_ID && a.RefMasterDetail_IsActive == true).ToList();
                    List<GNM_RefMasterDetailLocale> IsArLocalelist = RefMasterDetailLocaleList.Where(a => a.RefMaster_ID == IAid && a.Language_ID == Language_ID && a.RefMasterDetail_IsActive == true).ToList();
                    List<GNM_ModelLocale> ModelLocaleList = ModelLocale_Detail.Where(i => i.Language_ID == Language_ID && i.Model_IsActive == true).ToList();
                    List<HD_IssueSubAreaLocale> ISAlocaleList = IssueSubAreaLocaleList.Where(i => i.Language_ID == Language_ID && i.IssueSubArea_IsActive == true).ToList();
                    if (mode == "checked")
                    {

                        SRData = (from SR in SRAll
                                  join AG in Agreement on SR.CallPriority_ID equals AG.CallPriority_ID
                                  join a in SRAll on SR.ServiceRequest_ID equals a.ServiceRequest_ID
                                  join c in ModelLocaleList on a.Model_ID equals c.Model_ID into ModelDetail
                                  from Modelfinal in ModelDetail.DefaultIfEmpty(new GNM_ModelLocale { Model_Name = "" })
                                  join d in IsArLocalelist on a.IssueArea_ID equals d.RefMasterDetail_ID into IAList
                                  from IAfinal in IAList.DefaultIfEmpty(new GNM_RefMasterDetailLocale { RefMasterDetail_Name = "" })
                                  join e in ISAlocaleList on a.IssueSubArea_ID equals e.IssueSubArea_ID into ISAList
                                  from ISAfinal in ISAList.DefaultIfEmpty(new HD_IssueSubAreaLocale { IssueSubArea_Description = "" })
                                  join f in FGLocalelist on SR.FunctionGroup_ID equals f.RefMasterDetail_ID into FGdata
                                  from FGfinal in FGdata.DefaultIfEmpty(new GNM_RefMasterDetailLocale { RefMasterDetail_Name = "" })
                                  join f in WFStepStatusList on a.CallStatus_ID equals f.WFStepStatus_ID
                                  where SR.CallComplexity_ID == AG.CallComplexity_ID
                                  &&
                                  ((CheckPartySpecific(SR.Party_ID, SR.CallComplexity_ID, SR.CallPriority_ID, SR.Company_ID, connString)) ? SR.Party_ID == AG.Party_ID : AG.Party_ID == null)
                                  select new CaseDetailsForSLA()
                                  {
                                      Region = CommonFunctionalities.getRegionName(Convert.ToInt32(Language_ID), Convert.ToInt32(generalLanguageID), SR.Branch_ID, connString, LogException),
                                      ServiceRequestID = a.ServiceRequest_ID,
                                      CaseNumber = a.ServiceRequestNumber,
                                      Status = f.WFStepStatus_Nm,
                                      FunctionGroup = FGfinal.RefMasterDetail_Name,
                                      Model = Modelfinal.Model_Name,
                                      SerialNumber = (a.SerialNumber == null ? string.Empty : a.SerialNumber),
                                      IssueArea = IAfinal.RefMasterDetail_Name,
                                      IssueSubArea = ISAfinal.IssueSubArea_Description,
                                      //WorkingHours = getWorkingHours(SR.CallDateAndTime, Convert.ToDateTime(((SR.CallStatus_ID != ClosedStatusID) ? (CurrentDateTime) : (SR.CallClosureDateAndTime))), Company_ID),
                                      WorkingHours = Type == 1 ? getWorkingHours(SR.CallDateAndTime, Convert.ToDateTime(((SR.CallStatus_ID != ClosedStatusID) ? (CurrentDateTime) : (SR.CallClosureDateAndTime))), SR.Company_ID, connString, LogException) : Convert.ToInt32((a.CallClosureDateAndTime == null ? CurrentDateTime : a.CallClosureDateAndTime).Value.Subtract(a.CallDateAndTime).TotalMinutes),
                                      ServiceLevelAgreement_Hours = GetSLATime(AG.ServiceLevelAgreement_Hours)
                                  });

                        SRData = (from a in SRData
                                  where a.WorkingHours > a.ServiceLevelAgreement_Hours
                                  select new CaseDetailsForSLA()
                                  {
                                      Region = a.Region,
                                      ServiceRequestID = a.ServiceRequestID,
                                      CaseNumber = a.CaseNumber,
                                      Status = a.Status,
                                      FunctionGroup = a.FunctionGroup,
                                      Model = a.Model,
                                      SerialNumber = a.SerialNumber,
                                      IssueArea = a.IssueArea,
                                      IssueSubArea = a.IssueSubArea,
                                      SLATime = a.WorkingHours - a.ServiceLevelAgreement_Hours
                                  });
                    }
                    else
                    {

                        SRData = (from SR in SRAll
                                  join AG in Agreement on SR.CallPriority_ID equals AG.CallPriority_ID
                                  join a in SRAll on SR.ServiceRequest_ID equals a.ServiceRequest_ID
                                  join c in ModelLocaleList on a.Model_ID equals c.Model_ID into ModelDetail
                                  from Modelfinal in ModelDetail.DefaultIfEmpty(new GNM_ModelLocale { Model_Name = "" })
                                  join d in RefMasterDetailLocaleList on a.IssueArea_ID equals d.RefMasterDetail_ID into IAList
                                  from IAfinal in IAList.DefaultIfEmpty(new GNM_RefMasterDetailLocale { RefMasterDetail_Name = "" })
                                  join e in ISAlocaleList on a.IssueSubArea_ID equals e.IssueSubArea_ID into ISAList
                                  from ISAfinal in ISAList.DefaultIfEmpty(new HD_IssueSubAreaLocale { IssueSubArea_Description = "" })
                                  join f in FGLocalelist on SR.FunctionGroup_ID equals f.RefMasterDetail_ID into FGdata
                                  from FGfinal in FGdata.DefaultIfEmpty(new GNM_RefMasterDetailLocale { RefMasterDetail_Name = "" })
                                  join f in WFStepStatusList on a.CallStatus_ID equals f.WFStepStatus_ID
                                  where SR.CallComplexity_ID == AG.CallComplexity_ID
                                  &&
                                  ((CheckPartySpecific(SR.Party_ID, SR.CallComplexity_ID, SR.CallPriority_ID, SR.Company_ID, connString)) ? SR.Party_ID == AG.Party_ID : AG.Party_ID == null)
                                  select new CaseDetailsForSLA()
                                  {
                                      Region = CommonFunctionalities.getRegionName(Convert.ToInt32(Language_ID), Convert.ToInt32(generalLanguageID), SR.Branch_ID, connString, LogException),
                                      ServiceRequestID = a.ServiceRequest_ID,
                                      CaseNumber = a.ServiceRequestNumber,
                                      Status = f.WFStepStatus_Nm,
                                      FunctionGroup = FGfinal.RefMasterDetail_Name,
                                      Model = Modelfinal.Model_Name,
                                      SerialNumber = (a.SerialNumber == null ? string.Empty : a.SerialNumber),
                                      IssueArea = IAfinal.RefMasterDetail_Name,
                                      IssueSubArea = ISAfinal.IssueSubArea_Description,
                                      //WorkingHours = getWorkingHours(SR.CallDateAndTime, Convert.ToDateTime(((SR.CallStatus_ID != ClosedStatusID) ? (CurrentDateTime) : (SR.CallClosureDateAndTime))), Company_ID),
                                      WorkingHours = Type == 1 ? getWorkingHours(SR.CallDateAndTime, Convert.ToDateTime(((SR.CallStatus_ID != ClosedStatusID) ? (CurrentDateTime) : (SR.CallClosureDateAndTime))), SR.Company_ID, connString, LogException) : Convert.ToInt32((a.CallClosureDateAndTime == null ? CurrentDateTime : a.CallClosureDateAndTime).Value.Subtract(a.CallDateAndTime).TotalMinutes),
                                      ServiceLevelAgreement_Hours = GetSLATime(AG.ServiceLevelAgreement_Hours)
                                  });

                        SRData = (from a in SRData
                                  where a.WorkingHours < a.ServiceLevelAgreement_Hours
                                  select new CaseDetailsForSLA()
                                  {
                                      Region = a.Region,
                                      ServiceRequestID = a.ServiceRequestID,
                                      CaseNumber = a.CaseNumber,
                                      Status = a.Status,
                                      FunctionGroup = a.FunctionGroup,
                                      Model = a.Model,
                                      SerialNumber = a.SerialNumber,
                                      IssueArea = a.IssueArea,
                                      IssueSubArea = a.IssueSubArea,
                                      SLATime = a.ServiceLevelAgreement_Hours - a.WorkingHours
                                  });
                    }
                }
                IQSRdata = SRData.AsQueryable<CaseDetailsForSLA>();


            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return IQSRdata;
        }
        #endregion

        #region ::: To Export:::
        /// <summary>
        /// To Export 
        /// </summary>
        /// <returns>...</returns>
        public static async Task<object> Export(SLAExceeded_ExportList OBJ, string connString, int LogException)
        {
            DataTable Dt = new DataTable();
            List<CaseDetailsForSLA> IQCaseDetailForExport = null;

            int cnt = 0;
            try
            {
                string ExorWit = OBJ.SLAType.ToString(); ;
                string filterType = OBJ.SLAFilterType.ToString();
                string Fdate = OBJ.fromdateFromSLAreport.ToString();
                string Tdate = OBJ.todateFromSLAreport.ToString();
                string SelectedBrachinSLAReport = OBJ.SelectedBrachinSLAReport.ToString();
                string SLAFilterType = OBJ.SLAFilterType.ToString();
                string mode = OBJ.mode.ToString();
                //int branch = Convert.ToInt32(Request.Params["branch"]);
                int Type = Convert.ToInt32(OBJ.Type);
                string frmdate = string.Empty;
                string todate = string.Empty;
                frmdate = OBJ.frmdate;
                todate = OBJ.todate;
                if (frmdate == null)
                {
                    frmdate = string.Empty;
                }
                if (todate == null)
                {
                    todate = string.Empty;
                }
                IQCaseDetailForExport = GetSLAExceeded(mode, Type, frmdate, todate, OBJ.BranchData, OBJ.CompanyData, OBJ.BranchID, OBJ.Company_ID, OBJ.UserCulture,
            OBJ.UserLanguageID, OBJ.GeneralLanguageID, OBJ.UserLanguageCode, OBJ.GeneralLanguageCode, connString, LogException).ToList();
                var result = from a in IQCaseDetailForExport.AsEnumerable()
                             select new
                             {
                                 a.Region,
                                 a.CompanyName,
                                 a.BranchName,
                                 a.CaseNumber,
                                 a.Status,
                                 a.FunctionGroup,
                                 a.SerialNumber,
                                 a.Model,
                                 a.IssueArea,
                                 a.IssueSubArea,
                                 SLATime = SplitAndGet(a.SLATime.ToString())
                             };
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "Region").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "Company").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "Branch").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "casenumber").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "model").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "serialnumber").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "issuearea").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "issuesubarea").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "functiongroup").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "status").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), ExorWit).ToString());

                DataTable dtOptions = new DataTable();
                dtOptions.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "filtercriteria").ToString());
                //dtOptions.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "branch").ToString());
                dtOptions.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "fromdate").ToString());
                dtOptions.Columns.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "todate").ToString());

                dtOptions.Rows.Add(CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), filterType).ToString(), Fdate, Tdate);
                DataTable DtAlignment = new DataTable();
                DtAlignment.Columns.Add("Region");
                DtAlignment.Columns.Add("Company");
                DtAlignment.Columns.Add("Branch");
                DtAlignment.Columns.Add("Case Number");
                DtAlignment.Columns.Add("Model");
                DtAlignment.Columns.Add("Serial Number");
                DtAlignment.Columns.Add("Issue Area");
                DtAlignment.Columns.Add("Issue Sub-Area");
                DtAlignment.Columns.Add("Function Group");
                DtAlignment.Columns.Add("status");
                DtAlignment.Columns.Add(ExorWit);
                DtAlignment.Rows.Add(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                DataSet ds = new DataSet();
                cnt = IQCaseDetailForExport.Count;
                if (cnt > 0)
                {
                    for (int i = 0; i < cnt; i++)
                    {
                        Dt.Rows.Add(IQCaseDetailForExport[i].Region.ToString(), IQCaseDetailForExport[i].CompanyName.ToString(), IQCaseDetailForExport[i].BranchName.ToString(), IQCaseDetailForExport[i].CaseNumber.ToString(), IQCaseDetailForExport[i].Model.ToString(), IQCaseDetailForExport[i].SerialNumber.ToString(), IQCaseDetailForExport[i].IssueArea.ToString(), IQCaseDetailForExport[i].IssueSubArea.ToString(), IQCaseDetailForExport[i].FunctionGroup.ToString(), IQCaseDetailForExport[i].Status.ToString(), SplitAndGet(IQCaseDetailForExport[i].SLATime.ToString()));
                    }
                    ReportExportList exportList = new ReportExportList
                    {
                        Branch = OBJ.BranchID,
                        GeneralLanguageID = OBJ.GeneralLanguageID,
                        UserLanguageID = OBJ.UserLanguageID,
                        Company_ID = Convert.ToInt32(OBJ.Company_ID),
                        UserCulture = OBJ.UserCulture,
                        Options = dtOptions,
                        exprtType = OBJ.exprtType,
                        Alignment = DtAlignment,
                        FileName = CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "SLAReport").ToString(),
                        Header = CommonFunctionalities.GetResourceString(OBJ.UserCulture.ToString(), "SLAReport").ToString(),
                        dt = Dt,
                    };
                    var results = await ReportExport.Export(exportList, connString, LogException);
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1) LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return null;
        }
        #endregion
    }

    // Properties

    public class CallDateDetail
    {
        public List<string> WorkDays { get; set; }
        public TimeSpan startTime { get; set; }
        public TimeSpan endTime { get; set; }
        public TimeSpan BreakstartTime { get; set; }
        public TimeSpan BreakEndTime { get; set; }
        public double startTimeMinutes { get; set; }
        public double endTimeMinutes { get; set; }
        public double BreakstartTimeMinutes { get; set; }
        public double BreakEndTimeMinute { get; set; }
        public double WorkHours { get; set; }
    }

    public class DateDetails
    {
        public CallDateDetail Details { get; set; }
        public int Year { get; set; }
    }

    public partial class SLAExceeded_SelectList
    {
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public string UserLanguageCode { get; set; }
        public string GeneralLanguageCode { get; set; }
        public string FromDate { get; set; }
        public string ToDate { get; set; }
        public string UserCulture { get; set; }
        public string mode { get; set; }
        public string Type { get; set; }
        public string BranchData { get; set; }
        public string BranchID { get; set; }
        public string Company_ID { get; set; }
        public string CompanyData { get; set; }
        public string DateDetails { get; set; }
    }

    public class CaseDetailsForSLA
    {
        public int ServiceRequestID { get; set; }
        public string CaseNumber { get; set; }
        public string Status { get; set; }
        public string FunctionGroup { get; set; }
        public string Model { get; set; }
        public string SerialNumber { get; set; }
        public string IssueArea { get; set; }
        public string IssueSubArea { get; set; }
        public int SLATime { get; set; }
        public string CompanyName { get; set; }
        public string BranchName { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public int WorkingHours { get; set; }
        public int ServiceLevelAgreement_Hours { get; set; }
        public string Region { get; set; }
    }


    public partial class GNM_ModelLocale
    {
        public int ModelLocale_ID { get; set; }
        public int Model_ID { get; set; }
        public string Model_Name { get; set; }
        public int Language_ID { get; set; }
        public string Model_Description { get; set; }
        public bool Model_IsActive { get; set; }
        public virtual GNM_Model GNM_Model { get; set; }
    }

    public partial class GNM_Model
    {
        public int Model_ID { get; set; }
        public int ProductType_ID { get; set; }
        public int Brand_ID { get; set; }
        public string Model_Name { get; set; }
        public bool Model_IsActive { get; set; }
        public int ModifiedBy { get; set; }
        public DateTime ModifiedDate { get; set; }
        public Nullable<int> ServiceType_ID { get; set; }
        public Nullable<int> ServiceFrequency { get; set; }
        public string Model_Description { get; set; }
        public Nullable<byte> AttachmentCount { get; set; }
        public string Series { get; set; }

    }

    public partial class SLAExceeded_ExportList
    {
        public DataTable Options { get; set; }
        public DataTable dt { get; set; }
        public DataTable Alignment { get; set; }
        public string FileName { get; set; }
        public string Header { get; set; }
        public int exprtType { get; set; }
        public string UserCulture { get; set; }
        public string ExportTitle { get; set; }
        public string SLAType { get; set; }
        public string SLAFilterType { get; set; }
        public string fromdateFromSLAreport { get; set; }
        public string todateFromSLAreport { get; set; }
        public string SelectedBrachinSLAReport { get; set; }
        public string mode { get; set; }
        public string Type { get; set; }
        public string frmdate { get; set; }
        public string todate { get; set; }
        public string BranchData { get; set; }
        public string BranchID { get; set; }
        public string Company_ID { get; set; }
        public string CompanyData { get; set; }
        public string DateDetails { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public string UserLanguageCode { get; set; }
        public string GeneralLanguageCode { get; set; }
    }
}
