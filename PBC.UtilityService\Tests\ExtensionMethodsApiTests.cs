using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using PBC.UtilityService.Services;
using PBC.UtilityService.Utilities.DTOs;
using PBC.UtilityService.Utilities.Models;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace PBC.UtilityService.Tests
{
    /// <summary>
    /// Integration tests for ExtensionMethods API endpoints
    /// </summary>
    public class ExtensionMethodsApiTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public ExtensionMethodsApiTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory;
            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task OrderByField_ShouldReturnOrderedData()
        {
            // Arrange
            var request = new OrderByFieldRequest
            {
                SortField = "Name",
                SortDirection = "asc",
                Data = new object[]
                {
                    new { Name = "John", Age = 30 },
                    new { Name = "Alice", Age = 25 },
                    new { Name = "Bob", Age = 35 }
                }
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/extensionmethods/order-by-field", request);

            // Assert
            response.EnsureSuccessStatusCode();
            var result = await response.Content.ReadFromJsonAsync<ExtensionMethodsResponse<object[]>>();
            
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
            Assert.Equal(3, result.TotalCount);
        }

        [Fact]
        public async Task FilterSearch_ShouldReturnFilteredData()
        {
            // Arrange
            var request = new FilterSearchRequest
            {
                Filters = new Filters
                {
                    rules = new List<rules>
                    {
                        new rules { field = "Name", data = "John" }
                    }
                },
                Data = new object[]
                {
                    new { Name = "John", Age = 30 },
                    new { Name = "Jane", Age = 25 },
                    new { Name = "John Doe", Age = 35 }
                }
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/extensionmethods/filter-search", request);

            // Assert
            response.EnsureSuccessStatusCode();
            var result = await response.Content.ReadFromJsonAsync<ExtensionMethodsResponse<object[]>>();
            
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
        }

        [Fact]
        public async Task AdvanceSearch_ShouldReturnFilteredData()
        {
            // Arrange
            var request = new AdvanceSearchRequest
            {
                AdvanceFilter = new AdvanceFilter
                {
                    Rules = new List<Rules>
                    {
                        new Rules 
                        { 
                            Field = "Age", 
                            Data = "25", 
                            Operator = "greaterthan",
                            Condition = "AND"
                        }
                    }
                },
                Data = new object[]
                {
                    new { Name = "John", Age = 30 },
                    new { Name = "Jane", Age = 25 },
                    new { Name = "Bob", Age = 35 }
                }
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/extensionmethods/advance-search", request);

            // Assert
            response.EnsureSuccessStatusCode();
            var result = await response.Content.ReadFromJsonAsync<ExtensionMethodsResponse<object[]>>();
            
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
        }

        [Fact]
        public async Task Paginate_ShouldReturnPaginatedData()
        {
            // Arrange
            var request = new PaginateRequest
            {
                Data = new object[] { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10 },
                Page = 2,
                Rows = 3
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/extensionmethods/paginate", request);

            // Assert
            response.EnsureSuccessStatusCode();
            var result = await response.Content.ReadFromJsonAsync<PaginationResponse<object>>();
            
            Assert.NotNull(result);
            Assert.Equal(2, result.CurrentPage);
            Assert.Equal(3, result.RowsPerPage);
            Assert.Equal(10, result.TotalRecords);
            Assert.True(result.HasNextPage);
            Assert.True(result.HasPreviousPage);
        }

        [Fact]
        public async Task ConvertToType_ShouldConvertValue()
        {
            // Arrange
            var request = new ConvertToTypeRequest
            {
                Value = "123",
                TargetType = "Int32"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/extensionmethods/convert-to-type", request);

            // Assert
            response.EnsureSuccessStatusCode();
            var result = await response.Content.ReadFromJsonAsync<ExtensionMethodsResponse<object>>();
            
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
        }

        [Fact]
        public async Task DecryptString_ShouldDecryptValue()
        {
            // Arrange
            var request = new DecryptStringRequest
            {
                EncryptedString = "Hello%20World%lthash%23"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/extensionmethods/decrypt-string", request);

            // Assert
            response.EnsureSuccessStatusCode();
            var result = await response.Content.ReadFromJsonAsync<ExtensionMethodsResponse<string>>();
            
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal("Hello World&#23", result.Data);
        }

        [Fact]
        public async Task Like_ShouldReturnComparisonResult()
        {
            // Act
            var response = await _client.GetAsync("/api/extensionmethods/like?field=Hello World&value=world");

            // Assert
            response.EnsureSuccessStatusCode();
            var result = await response.Content.ReadFromJsonAsync<ExtensionMethodsResponse<bool>>();
            
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.True(result.Data);
        }

        [Fact]
        public async Task LikeInteger_ShouldReturnComparisonResult()
        {
            // Act
            var response = await _client.GetAsync("/api/extensionmethods/like-integer?field=12345&value=234");

            // Assert
            response.EnsureSuccessStatusCode();
            var result = await response.Content.ReadFromJsonAsync<ExtensionMethodsResponse<bool>>();
            
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.True(result.Data);
        }

        [Fact]
        public async Task LikeDecimal_ShouldReturnComparisonResult()
        {
            // Act
            var response = await _client.GetAsync("/api/extensionmethods/like-decimal?field=123.45&value=23.4");

            // Assert
            response.EnsureSuccessStatusCode();
            var result = await response.Content.ReadFromJsonAsync<ExtensionMethodsResponse<bool>>();
            
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.True(result.Data);
        }

        [Fact]
        public async Task LikeBool_ShouldReturnComparisonResult()
        {
            // Act
            var response = await _client.GetAsync("/api/extensionmethods/like-bool?field=true&value=true");

            // Assert
            response.EnsureSuccessStatusCode();
            var result = await response.Content.ReadFromJsonAsync<ExtensionMethodsResponse<bool>>();
            
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.True(result.Data);
        }

        [Fact]
        public void ExtensionMethodsService_ShouldBeRegistered()
        {
            // Arrange & Act
            var service = _factory.Services.GetService<IExtensionMethodsService>();

            // Assert
            Assert.NotNull(service);
            Assert.IsType<ExtensionMethodsService>(service);
        }
    }
}
