﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Reflection;
using System.Resources;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class PRM_PartsCategoryServices
    {
        int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));

        #region :::GetGlobalResourceObject   Uday Kumar J B 11-07-2024 :::
        /// <summary>
        /// To Get GlobalResourceObject Uday Kumar J B 11-07-2024 15:13
        /// </summary>
        /// 
        public static string GetGlobalResourceObject(string cultureValue, string resourceKey, Assembly assembly = null)
        {
            try
            {
                if (assembly == null)
                {
                    assembly = Assembly.GetExecutingAssembly();
                }

                string cultureIdentifier = cultureValue.Replace("Resource_", "");
                string resourceNamespace = assembly.GetName().Name + ".App_GlobalResources.";
                string resourceFileName = "resource_" + cultureIdentifier.ToLowerInvariant();
                ResourceManager resourceManager = new ResourceManager(resourceNamespace + resourceFileName, assembly);
                string resourceValue = resourceManager.GetString(resourceKey);

                return resourceValue;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error accessing resource: {ex.Message}");
                return string.Empty;
            }
        }
        #endregion


        #region ::: Select Uday Kumar J B 14-08-2024 :::
        /// <summary>
        /// To Select PartsCategory
        /// </summary>
        /// 

        public static DataTable getLandingGridData(string connString, int CompanyID, int LanguageID, int GeneralLanguageID)
        {
            DataTable dataTable = new DataTable();

            using (SqlConnection con = new SqlConnection(connString))
            {
                using (SqlCommand cmd = new SqlCommand("GetLandingGridData", con))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                    cmd.Parameters.AddWithValue("@LanguageID", LanguageID);
                    cmd.Parameters.AddWithValue("@GeneralLanguageID", Convert.ToInt32(GeneralLanguageID));

                    SqlDataAdapter da = new SqlDataAdapter(cmd);
                    con.Open();
                    da.Fill(dataTable);
                    con.Close();
                }
            }

            return dataTable;
        }


        public static IActionResult Select(string connString, SelectPRM_PartsCategoryList SelectPRM_PartsCategoryobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string AppPath = string.Empty;
            var jsonData = default(dynamic);
            try
            {
                // Retrieve the data
                DataTable dtPartsCategoryMaster = getLandingGridData(connString, SelectPRM_PartsCategoryobj.CompanyID, SelectPRM_PartsCategoryobj.LanguageID, SelectPRM_PartsCategoryobj.GeneralLanguageID);

                // Sorting logic
                DataView dv = dtPartsCategoryMaster.DefaultView;
                dv.Sort = $"{sidx} {sord}";
                DataTable sortedTable = dv.ToTable();

                // Filtering logic
                if (!string.IsNullOrEmpty(filters))
                {
                    dv.RowFilter = $"PartsCategory_Description LIKE '%{filters}%'";
                    sortedTable = dv.ToTable();
                }

                // Paging logic
                int totalRecords = sortedTable.Rows.Count;
                int totalPages = (int)Math.Ceiling((float)totalRecords / (float)rows);
                DataTable paginatedTable = sortedTable.AsEnumerable()
                    .Skip((page - 1) * rows)
                    .Take(rows)
                    .CopyToDataTable();

                // Prepare the rows for the grid
                var paginatedRows = paginatedTable.AsEnumerable()
                    .Select(row => new
                    {
                        ID = row["PartsCategory_ID"],
                        edit = "<a title='Edit' href='#' id='" + row["PartsCategory_ID"] + "' key='" + row["PartsCategory_ID"] + "' editmode='false' " + ((Convert.ToInt32(row["Company_ID"]) == SelectPRM_PartsCategoryobj.CompanyID) ? "class='PartsCategoryEdit font-icon-class'" : "class='font-icon-class'") + "><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                        delete = "<input type='checkbox' key='" + row["PartsCategory_ID"] + "' defaultchecked=''  id='chk" + row["PartsCategory_ID"] + "'  " + ((Convert.ToInt32(row["Company_ID"]) == SelectPRM_PartsCategoryobj.CompanyID) ? "class='PartsCategoryDelete'" : "class='CannotDeletePartsCategory'") + "/>",
                        PartsCategory_Description = row["PartsCategory_Description"].ToString(),
                        Company_Name = row["Company_Name"].ToString(),
                        PartsCategory_IsActive = row["PartsCategory_IsActive"].ToString(),
                        Locale = "<img key='" + row["PartsCategory_ID"] + "' src='" + AppPath + "/Content/local.png'  " + ((Convert.ToInt32(row["Company_ID"]) == SelectPRM_PartsCategoryobj.CompanyID) ? "class='PartsCategoryLocale'" : "") + " alt='Localize' width='20' height='20'  title='Localize'/>",
                        View = "<img id='" + row["PartsCategory_ID"] + "' src='" + AppPath + "/Content/plus.gif' key='" + row["PartsCategory_ID"] + "' class='ViewPartsCategoryLocale' PartsCategory='" + row["PartsCategory_Description"].ToString() + "'/>",
                    }).ToList();

                jsonData = new
                {
                    total = totalPages,
                    page,
                    rows = paginatedRows,
                    records = totalRecords,
                    filter = filters,
                    advanceFilter = advnceFilters, // Replace 'Query' with 'advnceFilters'
                    SelectPRM_PartsCategoryobj.CompanyID,
                    SelectPRM_PartsCategoryobj.LanguageID,
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: Save Uday Kumar J B 14-08-2024 :::
        /// <summary>
        /// To Insert and Update PartsCategory
        /// </summary>
        /// 
        public static IActionResult Save(string connString, SavePRM_PartsCategoryList SavePRM_PartsCategoryobj)
        {
            string Msg = string.Empty;
            JObject jObj = null;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {

                jObj = JObject.Parse(SavePRM_PartsCategoryobj.data);
                int Count = jObj["rows"].Count();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int i = 0; i < Count; i++)
                    {
                        PRM_PartsCategory SRow = jObj["rows"].ElementAt(i).ToObject<PRM_PartsCategory>();

                        if (SRow.PartsCategory_ID != 0)
                        {
                            // Update existing record
                            using (SqlCommand cmd = new SqlCommand("Up_Upd_AM_ERP_UpdatePartsCategory", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;

                                cmd.Parameters.AddWithValue("@PartsCategory_ID", SRow.PartsCategory_ID);
                                cmd.Parameters.AddWithValue("@Description", Common.DecryptString(SRow.Description));
                                cmd.Parameters.AddWithValue("@IsActive", SRow.IsActive);

                                cmd.ExecuteNonQuery();
                            }

                            //  gbl.InsertGPSDetails(Convert.ToInt32(SavePRM_PartsCategoryobj.Company_ID), Convert.ToInt32(SavePRM_PartsCategoryobj.Branch), Convert.ToInt32(SavePRM_PartsCategoryobj.User_ID), Convert.ToInt32(Common.GetObjectID("PRM_PartsCategory")), SRow.PartsCategory_ID, 0, 0, "Update", false, Convert.ToInt32(SavePRM_PartsCategoryobj.MenuID), Convert.ToDateTime(SavePRM_PartsCategoryobj.LoggedIDateTime));
                        }
                        else
                        {
                            // Insert new record
                            using (SqlCommand cmd = new SqlCommand("Up_Ins_AM_ERP_InsertPartsCategory", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;

                                cmd.Parameters.AddWithValue("@Description", Common.DecryptString(SRow.Description));
                                cmd.Parameters.AddWithValue("@IsActive", SRow.IsActive);
                                cmd.Parameters.AddWithValue("@Company_ID", Convert.ToInt32(SavePRM_PartsCategoryobj.Company_ID));
                                cmd.Parameters.AddWithValue("@Branch_ID", Convert.ToInt32(SavePRM_PartsCategoryobj.Branch));

                                // Output parameter for inserted PartsCategory_ID
                                var returnParameter = cmd.Parameters.Add("@PartsCategory_ID", SqlDbType.Int);
                                returnParameter.Direction = ParameterDirection.Output;

                                cmd.ExecuteNonQuery();

                                // Retrieve the inserted PartsCategory_ID from output parameter
                                SRow.PartsCategory_ID = (int)returnParameter.Value;
                            }

                            // gbl.InsertGPSDetails(Convert.ToInt32(SavePRM_PartsCategoryobj.Company_ID), Convert.ToInt32(SavePRM_PartsCategoryobj.Branch), Convert.ToInt32(SavePRM_PartsCategoryobj.User_ID), Convert.ToInt32(Common.GetObjectID("PRM_PartsCategory")), SRow.PartsCategory_ID, 0, 0, "Insert", false, Convert.ToInt32(SavePRM_PartsCategoryobj.MenuID), Convert.ToDateTime(SavePRM_PartsCategoryobj.LoggedIDateTime));
                        }
                    }
                }

                Msg = "Saved";
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                Msg = string.Empty;
            }
            return new JsonResult(Msg);
        }
        #endregion


        #region ::: CheckPartsCategory Uday Kumar J B 14-08-2024:::
        /// <summary>
        /// To Check PartsCategory already exists 
        /// </summary>

        public static IActionResult CheckPartsCategory(string connString, CheckPartsCategoryList CheckPartsCategoryobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            int count = 0;
            IEnumerable<ParentCompanyObject> parentCompanyDetails = null;

            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Decrypt the PartsCategoryDescription
                    string PartsCategoryDescription = Common.DecryptString(CheckPartsCategoryobj.PartsCategoryDescription);

                    // Retrieve ParentCompID using stored procedure
                    int parentCompID = 0;
                    using (SqlCommand cmd = new SqlCommand("GetParentCompanyID", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", CheckPartsCategoryobj.CompanyID);
                        parentCompID = Convert.ToInt32(cmd.ExecuteScalar());
                    }

                    if (CheckPartsCategoryobj.CompanyID != parentCompID && parentCompID != 0)
                    {
                        // Execute the recursive CTE query using the stored procedure
                        using (SqlCommand cmd = new SqlCommand("GetParentCompanyDetails", connection))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@CompanyID", CheckPartsCategoryobj.CompanyID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                List<ParentCompanyObject> tempParentCompanyDetails = new List<ParentCompanyObject>();
                                while (reader.Read())
                                {
                                    ParentCompanyObject parentCompany = new ParentCompanyObject
                                    {
                                        Company_ID = Convert.ToInt32(reader["Company_ID"]),
                                        Company_Name = reader["Company_Name"].ToString(),
                                        Company_Parent_ID = Convert.ToInt32(reader["Company_Parent_ID"])
                                    };
                                    tempParentCompanyDetails.Add(parentCompany);
                                }
                                parentCompanyDetails = tempParentCompanyDetails;
                            }
                        }

                        // Retrieve PartsCategoryRow using stored procedure
                        List<PRM_PartsCategory> partsCategoryRow = new List<PRM_PartsCategory>();
                        using (SqlCommand cmd = new SqlCommand("GetPartsCategoryByDescription", connection))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Description", PartsCategoryDescription);
                            cmd.Parameters.AddWithValue("@PartsCategoryID", CheckPartsCategoryobj.PartsCategoryID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    PRM_PartsCategory partsCategory = new PRM_PartsCategory
                                    {
                                        PartsCategory_ID = Convert.ToInt32(reader["PartsCategory_ID"]),
                                        Description = reader["Description"].ToString(),
                                        IsActive = Convert.ToBoolean(reader["IsActive"]),
                                        Company_ID = Convert.ToInt32(reader["Company_ID"]),
                                        Branch_ID = Convert.ToInt32(reader["Branch_ID"])
                                    };
                                    partsCategoryRow.Add(partsCategory);
                                }
                            }
                        }

                        // Join PartsCategoryRow with ParentCompanyDetails using LINQ to Objects
                        var partsCategoryRowList = from a in partsCategoryRow
                                                   join b in parentCompanyDetails on a.Company_ID equals b.Company_ID
                                                   select new
                                                   {
                                                       PartsCategory_ID = a.PartsCategory_ID,
                                                       Description = a.Description,
                                                       IsActive = a.IsActive,
                                                       Company_ID = a.Company_ID,
                                                       Branch_ID = a.Branch_ID
                                                   };

                        // Set count based on the result count
                        if (partsCategoryRowList.ToList().Count > 0)
                        {
                            count = 1;
                        }
                    }
                    else
                    {
                        // Retrieve PartsCategoryRow with specific Company_ID using stored procedure
                        List<PRM_PartsCategory> partsCategoryRow = new List<PRM_PartsCategory>();
                        using (SqlCommand cmd = new SqlCommand("GetPartsCategoryByCompany", connection))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Description", PartsCategoryDescription);
                            cmd.Parameters.AddWithValue("@PartsCategoryID", CheckPartsCategoryobj.PartsCategoryID);
                            cmd.Parameters.AddWithValue("@CompanyID", CheckPartsCategoryobj.CompanyID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    PRM_PartsCategory partsCategory = new PRM_PartsCategory
                                    {
                                        PartsCategory_ID = Convert.ToInt32(reader["PartsCategory_ID"]),
                                        Description = reader["Description"].ToString(),
                                        IsActive = Convert.ToBoolean(reader["IsActive"]),
                                        Company_ID = Convert.ToInt32(reader["Company_ID"]),
                                        Branch_ID = Convert.ToInt32(reader["Branch_ID"])
                                    };
                                    partsCategoryRow.Add(partsCategory);
                                }
                            }
                        }

                        // Set count based on the result count
                        if (partsCategoryRow.ToList().Count > 0)
                        {
                            count = 1;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(count);
        }
        #endregion


        #region ::: SelectParticularPartsCategory Uday Kumar J B 14-08-2024 :::
        /// <summary>
        /// To Select Particular PartsCategory
        /// </summary>
        /// 
        public static IActionResult SelectParticularPartsCategory(string connString, SelectParticularPartsCategoryList SelectParticularPartsCategoryobj)
        {
            object result = null;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int Language_ID = Convert.ToInt32(SelectParticularPartsCategoryobj.UserLanguageID);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("SelectParticularPartsCategory", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@PartsCategoryID", SelectParticularPartsCategoryobj.PartsCategoryID);
                        cmd.Parameters.AddWithValue("@LanguageID", Language_ID);

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            // Reading the first result set (PRM_PartsCategory)
                            if (reader.Read())
                            {
                                int partsCategoryID = reader.GetInt32(reader.GetOrdinal("PartsCategory_ID"));
                                string description = reader.GetString(reader.GetOrdinal("PartsCategory_Description"));
                                bool isActive = reader.GetBoolean(reader.GetOrdinal("PartsCategory_IsActive"));

                                // Move to the second result set (PRM_PartsCategoryLocale)
                                if (reader.NextResult() && reader.Read())
                                {
                                    string localeID = reader["PartsCategoryLocale_ID"].ToString();
                                    string localeDescription = reader["PartsCategoryLocale_Description"].ToString();

                                    result = new
                                    {
                                        PartsCategory_ID = partsCategoryID,
                                        PartsCategory_Description = description,
                                        PartsCategory_IsActive = isActive,
                                        PartsCategoryLocale_ID = localeID,
                                        PartsCategoryLocale_Description = localeDescription
                                    };
                                }
                                else
                                {
                                    result = new
                                    {
                                        PartsCategory_ID = partsCategoryID,
                                        PartsCategory_Description = description,
                                        PartsCategory_IsActive = isActive,
                                        PartsCategoryLocale_ID = "",
                                        PartsCategoryLocale_Description = ""
                                    };
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(result);
        }
        #endregion


        #region ::: Delete Uday Kumar J B 14-08-2024 :::
        /// <summary>
        /// To Delete PartsCategory
        /// </summary>
        /// 
        public static IActionResult Delete(string connString, DeletePRM_PartsCategoryList DeletePRM_PartsCategoryobj)
        {
            string Msg = string.Empty;
            try
            {
                var json = DeletePRM_PartsCategoryobj.key;
                JObject jObj = JObject.Parse(json);
                int count = jObj["rows"].Count();
                List<int> ids = new List<int>();

                for (int i = 0; i < count; i++)
                {
                    ids.Add((int)jObj["rows"][i]["id"]);
                }

                string idList = string.Join(",", ids);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Del_PRM_PartsCategoryLocale", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@IDList", idList);
                        cmd.ExecuteNonQuery();
                    }

                    using (SqlCommand cmd = new SqlCommand("Up_Del_PRM_PartsCategory", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@IDList", idList);
                        cmd.ExecuteNonQuery();
                    }
                }

                // gbl.InsertGPSDetails(Convert.ToInt32(DeletePRM_PartsCategoryobj.Company_ID), Convert.ToInt32(DeletePRM_PartsCategoryobj.Branch), Convert.ToInt32(DeletePRM_PartsCategoryobj.User_ID), Convert.ToInt32(Common.GetObjectID("PRM_PartsCategory")), ids.Last(), 0, 0, "Delete", false, Convert.ToInt32(DeletePRM_PartsCategoryobj.MenuID), Convert.ToDateTime(DeletePRM_PartsCategoryobj.LoggedIDateTime));
                Msg += GetGlobalResourceObject(DeletePRM_PartsCategoryobj.GeneralCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (SqlException ex)
            {
                if (ex.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    Msg += GetGlobalResourceObject(DeletePRM_PartsCategoryobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
            }

            return new JsonResult(Msg);
        }
        #endregion


        #region ::: Export Uday Kumar J B 14-08-2024 :::
        /// <summary>
        /// To Export 
        /// </summary>
        /// 
        public static IActionResult Export(string connString, ExportPRM_PartsCategoryList ExportPRM_PartsCategoryobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                DataTable DtData = new DataTable();
                DataTable DtCriteria = new DataTable();
                DataTable DtAlignment = new DataTable();

                int CompanyID = Convert.ToInt32(ExportPRM_PartsCategoryobj.CompanyID);
                int LanguageID = Convert.ToInt32(ExportPRM_PartsCategoryobj.LanguageID);
                int GeneralLanguageID = Convert.ToInt32(ExportPRM_PartsCategoryobj.GeneralLanguageID);

                using (SqlConnection con = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("GetLandingGridData", con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                        cmd.Parameters.AddWithValue("@LanguageID", LanguageID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", GeneralLanguageID);

                        SqlDataAdapter da = new SqlDataAdapter(cmd);
                        con.Open();
                        da.Fill(DtData);
                        con.Close();
                    }
                }

                if (DtData.Rows.Count > 0)
                {
                    DtAlignment.Columns.Add("Company");
                    DtAlignment.Columns.Add("Description");
                    DtAlignment.Columns.Add("Active");
                    DtAlignment.Rows.Add(0, 0, 1);

                    // ReportExport.Export(ExportPRM_PartsCategoryobj.exprtType, DtData, DtCriteria, DtAlignment, "PartsCategory", HttpContext.GetGlobalResourceObject(ExportPRM_PartsCategoryobj.GeneralCulture.ToString(), "PartsCategory").ToString());
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }
        #endregion


        #region ::: UpdateLocale Uday Kumar J B 14-08-2024 :::
        /// <summary>
        /// To Update PartsCategory Locale
        /// </summary>

        public static IActionResult UpdateLocale(string connString, UpdateLocaleListb UpdateLocaleobj)
        {
            int partsCategoryLocaleID = 0;
            var jsonData = default(dynamic);
            JObject jObj = null;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                jObj = JObject.Parse(UpdateLocaleobj.data);
                PRM_PartsCategoryLocale slRow = jObj.ToObject<PRM_PartsCategoryLocale>();
                string description = Common.DecryptString(slRow.Description);
                PRM_PartsCategoryLocale SLRow = null;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    if (slRow.PartsCategoryLocale_ID != 0)
                    {
                        using (SqlCommand cmd = new SqlCommand("UpdatePartsCategoryLocale", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@PartsCategoryLocale_ID", slRow.PartsCategoryLocale_ID);
                            cmd.Parameters.AddWithValue("@Description", description);
                            cmd.ExecuteNonQuery();
                        }
                        partsCategoryLocaleID = slRow.PartsCategoryLocale_ID;
                    }
                    else
                    {
                        using (SqlCommand cmd = new SqlCommand("InsertPartsCategoryLocale", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@PartsCategory_ID", slRow.PartsCategory_ID);
                            cmd.Parameters.AddWithValue("@Description", description);

                            SqlParameter idParam = new SqlParameter("@PartsCategoryLocale_ID", SqlDbType.Int);
                            idParam.Direction = ParameterDirection.Output;
                            cmd.Parameters.Add(idParam);

                            cmd.ExecuteNonQuery();
                            partsCategoryLocaleID = (int)idParam.Value;
                        }
                    }

                    // gbl.InsertGPSDetails(Convert.ToInt32(UpdateLocaleobj.Company_ID), Convert.ToInt32(UpdateLocaleobj.Branch), Convert.ToInt32(UpdateLocaleobj.User_ID), Convert.ToInt32(Common.GetObjectID("PRM_PartsCategory")), SLRow.PartsCategory_ID, 0, 0, "Delete", false, Convert.ToInt32(UpdateLocaleobj.MenuID), Convert.ToDateTime(UpdateLocaleobj.LoggedIDateTime));
                }

                jsonData = new
                {
                    PartsCategoryLocale_ID = partsCategoryLocaleID
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: CheckPartsCategoryLocale Uday Kumar J B 14-08-2024 :::
        /// <summary>
        /// To Check PartsCategoryLocale already exists 
        /// </summary>
        /// 
        public static IActionResult CheckPartsCategoryLocale(string connString, CheckPartsCategoryLocaleList CheckPartsCategoryLocaleobj)
        {
            int count = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                string PartsCategoryDescription = Common.DecryptString(CheckPartsCategoryLocaleobj.PartsCategoryDescription);
                int languageID = Convert.ToInt32(CheckPartsCategoryLocaleobj.UserLanguageID);

                using (SqlConnection conn = new SqlConnection("your_connection_string"))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("CheckPartsCategoryLocale", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Description", PartsCategoryDescription);
                        cmd.Parameters.AddWithValue("@PartsCategoryLocaleID", CheckPartsCategoryLocaleobj.PartsCategoryLocaleID);
                        cmd.Parameters.AddWithValue("@LanguageID", languageID);

                        SqlParameter existsParam = new SqlParameter("@Exists", SqlDbType.Bit);
                        existsParam.Direction = ParameterDirection.Output;
                        cmd.Parameters.Add(existsParam);

                        cmd.ExecuteNonQuery();

                        count = (bool)existsParam.Value ? 1 : 0;
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(count);
        }
        #endregion


        #region ::: PRM_PartsCategory List and obj classes Uday Kumar J B 14-08-2024 :::
        /// <summary>
        /// PRM_PartsCategory
        /// </summary>
        ///
        public class ExportPRM_PartsCategoryList
        {
            public int exprtType { get; set; }
            public string BranchName { get; set; }
            public int CompanyID { get; set; }
            public int LanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string GeneralCulture { get; set; }
        }
        public class CheckPartsCategoryLocaleList
        {
            public string PartsCategoryDescription { set; get; }
            public int PartsCategoryLocaleID { set; get; }
            public int UserLanguageID { set; get; }
        }
        public class UpdateLocaleListb
        {
            public string data { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedIDateTime { get; set; }
            public string GeneralCulture { get; set; }
        }
        public class DeletePRM_PartsCategoryList
        {
            public string key { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedIDateTime { get; set; }
            public string GeneralCulture { get; set; }
        }
        public class SelectParticularPartsCategoryList
        {
            public int PartsCategoryID { get; set; }
            public int UserLanguageID { get; set; }
        }
        public class CheckPartsCategoryList
        {
            public int CompanyID { get; set; }
            public string PartsCategoryDescription { get; set; }
            public int PartsCategoryID { get; set; }
        }
        public class SavePRM_PartsCategoryList
        {
            public string data { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedIDateTime { get; set; }
        }
        public class SelectPRM_PartsCategoryList
        {
            public int CompanyID { get; set; }
            public int LanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        #endregion


        #region ::: PartsCategoryMaster Uday Kumar J B 14-08-2024 :::
        /// <summary>
        /// PartsCategoryMaster
        /// </summary>
        public class PartsCategoryMaster
        {
            public int PartsCategory_ID
            {
                get;
                set;
            }
            public int Company_ID
            {
                get;
                set;
            }
            public string PartsCategory_Description
            {
                get;
                set;
            }

            public string Company_Name
            {
                get;
                set;
            }

            public string PartsCategory_IsActive
            {
                get;
                set;
            }
        }

        public class ParentCompanyObject
        {
            public int Company_ID
            {
                get;
                set;
            }

            public string Company_Name
            {
                get;
                set;
            }

            public int Company_Parent_ID
            {
                get;
                set;
            }
        }

        public partial class PRM_PartsCategory
        {
            public PRM_PartsCategory()
            {
                this.PRM_PartsCategoryDefinition = new HashSet<PRM_PartsCategoryDefinition>();
                this.PRM_PartsCategoryLocale = new HashSet<PRM_PartsCategoryLocale>();
            }

            public int PartsCategory_ID { get; set; }
            public string Description { get; set; }
            public bool IsActive { get; set; }
            public int Company_ID { get; set; }
            public int Branch_ID { get; set; }
            public string Code { get; set; }

            public virtual ICollection<PRM_PartsCategoryDefinition> PRM_PartsCategoryDefinition { get; set; }
            public virtual ICollection<PRM_PartsCategoryLocale> PRM_PartsCategoryLocale { get; set; }
        }

        public partial class PRM_PartsCategoryDefinition
        {
            public int PartsCategoryDefinition_ID { get; set; }
            public int PartsCategory_ID { get; set; }
            public Nullable<decimal> ConversionFactor { get; set; }
            public Nullable<decimal> ProfitValue { get; set; }
            public int Company_ID { get; set; }
            public int Branch_ID { get; set; }
            public bool IsActive { get; set; }
            public Nullable<decimal> MRPFactor { get; set; }

            public virtual PRM_PartsCategory PRM_PartsCategory { get; set; }
        }

        public partial class PRM_PartsCategoryLocale
        {
            public int PartsCategoryLocale_ID { get; set; }
            public int PartsCategory_ID { get; set; }
            public int Language_ID { get; set; }
            public string Description { get; set; }

            public virtual PRM_PartsCategory PRM_PartsCategory { get; set; }
        }
        #endregion

    }
}
