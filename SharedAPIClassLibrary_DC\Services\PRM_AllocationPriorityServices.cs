﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Reflection;
using System.Resources;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace SharedAPIClassLibrary_AMERP
{
    public class PRM_AllocationPriorityServices
    {
        int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));


        #region :::GetGlobalResourceObject   Uday Kumar J B 11-07-2024 :::
        /// <summary>
        /// To Get GlobalResourceObject Uday Kumar J B 11-07-2024 15:13
        /// </summary>
        /// 
        public static string GetGlobalResourceObject(string cultureValue, string resourceKey, Assembly assembly = null)
        {
            try
            {
                if (assembly == null)
                {
                    assembly = Assembly.GetExecutingAssembly();
                }

                string cultureIdentifier = cultureValue.Replace("Resource_", "");
                string resourceNamespace = assembly.GetName().Name + ".App_GlobalResources.";
                string resourceFileName = "resource_" + cultureIdentifier.ToLowerInvariant();
                ResourceManager resourceManager = new ResourceManager(resourceNamespace + resourceFileName, assembly);
                string resourceValue = resourceManager.GetString(resourceKey);

                return resourceValue;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error accessing resource: {ex.Message}");
                return string.Empty;
            }
        }
        #endregion


        #region ::: Select Uday Kumar J B 19-08-2024 :::
        // / <summary>
        // / To Select Allocation priority
        // / </summary>
        // /
        public static IActionResult Select(string connString, SelectPRM_AllocationPriorityList SelectPRM_AllocationPriorityobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            var jsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int Count = 0;
                int Total = 0;
                int Company_ID = Convert.ToInt32(SelectPRM_AllocationPriorityobj.Company_ID);
                int GeneralLanguageID = Convert.ToInt32(SelectPRM_AllocationPriorityobj.GeneralLanguageID);

                List<AllocationPriority> allocationPriorityList = new List<AllocationPriority>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("Up_Sel_AM_ERP_GetAllocationPriority", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                        cmd.Parameters.AddWithValue("@LanguageID", SelectPRM_AllocationPriorityobj.LanguageID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", GeneralLanguageID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                allocationPriorityList.Add(new AllocationPriority
                                {
                                    AllocationPriority_ID = reader.GetInt32(0),
                                    RefMasterDetail_Name = reader.GetString(1),
                                    OrderClass_Description = reader.GetString(2),
                                    AllocationPriority_Priority = reader.GetInt32(3)
                                });
                            }
                        }
                    }
                }

                var IQAllocationPriorityMaster = allocationPriorityList.AsQueryable();

                if (_search)
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    if (filtersObj.rules.Count > 0)
                        IQAllocationPriorityMaster = IQAllocationPriorityMaster.FilterSearch<AllocationPriority>(filtersObj);
                }
                if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse(advnceFilters).ToObject<AdvanceFilter>();
                    IQAllocationPriorityMaster = IQAllocationPriorityMaster.AdvanceSearch<AllocationPriority>(advnfilter);
                }

                IQAllocationPriorityMaster = IQAllocationPriorityMaster.OrderByField<AllocationPriority>(sidx, sord);

                Count = IQAllocationPriorityMaster.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;

                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                jsonData = new
                {
                    total = Total,
                    page = page,
                    rows = (from a in IQAllocationPriorityMaster.AsEnumerable()
                            select new
                            {
                                ID = a.AllocationPriority_ID,
                                OrderClass_Description = a.OrderClass_Description,
                                RefMasterDetail_Name = a.RefMasterDetail_Name,
                                AllocationPriority_Priority = a.AllocationPriority_Priority
                            }).ToList().Paginate(page, rows),
                    records = Count
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: Save  Uday Kumar J B 19-08-2024:::
        /// <summary>
        /// To Update Allocation Priority
        /// </summary>
        /// 
        public static IActionResult Save(string connString, SavePRM_AllocationPriorityList SavePRM_AllocationPriorityobj)
        {
            string Msg = string.Empty;
            JObject jObj = null;
            int Count = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                jObj = JObject.Parse(SavePRM_AllocationPriorityobj.data);
                Count = jObj["rows"].Count();
                int userID = Convert.ToInt32(SavePRM_AllocationPriorityobj.User_ID);
                int companyID = Convert.ToInt32(SavePRM_AllocationPriorityobj.Company_ID);
                int branchID = Convert.ToInt32(SavePRM_AllocationPriorityobj.Branch);
                int menuID = Convert.ToInt32(SavePRM_AllocationPriorityobj.MenuID);
                DateTime loggedInDateTime = Convert.ToDateTime(SavePRM_AllocationPriorityobj.LoggedINDateTime);
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    for (int i = 0; i < Count; i++)
                    {
                        PRM_AllocationPriority APRow = jObj["rows"].ElementAt(i).ToObject<PRM_AllocationPriority>();

                        if (APRow.AllocationPriority_ID != 0)
                        {
                            using (SqlCommand cmd = new SqlCommand("Up_Upd_AM_ERP_UpdateAllocationPriority", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@AllocationPriority_ID", APRow.AllocationPriority_ID);
                                cmd.Parameters.AddWithValue("@AllocationPriority_Priority", APRow.AllocationPriority_Priority);
                                cmd.Parameters.AddWithValue("@ModifiedBy", userID);
                                cmd.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                                cmd.ExecuteNonQuery();
                            }

                            // gbl.InsertGPSDetails(companyID, branchID, userID, Convert.ToInt32(Common.GetObjectID("PRM_AllocationPriority")), APRow.AllocationPriority_ID, 0, 0, "Insert", false, menuID, loggedInDateTime);
                        }
                    }
                }
                Msg = "Saved";
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                Msg = string.Empty;
            }
            return new JsonResult(Msg);
        }

        #endregion


        #region ::: Export  Uday Kumar J B 19-08-2024:::
        /// <summary>
        /// To Export Allocation Priority
        /// </summary>
        public static IActionResult Export(string connString, ExportPRM_AllocationPriorityList ExportPRM_AllocationPriorityobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                DataTable DtData = new DataTable();
                int Count = 0;
                int Total = 0;
                int Company_ID = Convert.ToInt32(ExportPRM_AllocationPriorityobj.Company_ID);
                int GeneralLanguageID = Convert.ToInt32(ExportPRM_AllocationPriorityobj.GeneralLanguageID);

                List<AllocationPriority> allocationPriorityList = new List<AllocationPriority>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("Up_Sel_AM_ERP_GetAllocationPriority", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                        cmd.Parameters.AddWithValue("@LanguageID", ExportPRM_AllocationPriorityobj.LanguageID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", GeneralLanguageID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                allocationPriorityList.Add(new AllocationPriority
                                {
                                    AllocationPriority_ID = reader.GetInt32(0),
                                    RefMasterDetail_Name = reader.GetString(1),
                                    OrderClass_Description = reader.GetString(2),
                                    AllocationPriority_Priority = reader.GetInt32(3)
                                });
                            }
                        }
                    }
                }

                var IQAllocationPriority = allocationPriorityList.AsQueryable();
                var AllocationPriorityArray = from a in IQAllocationPriority.AsEnumerable()
                                              select new
                                              {
                                                  a.RefMasterDetail_Name,
                                                  a.OrderClass_Description,
                                                  a.AllocationPriority_Priority,
                                              };

                DtData.Columns.Add(GetGlobalResourceObject(ExportPRM_AllocationPriorityobj.UserCulture.ToString(), "OrderType").ToString());
                DtData.Columns.Add(GetGlobalResourceObject(ExportPRM_AllocationPriorityobj.UserCulture.ToString(), "OrderClass").ToString());
                DtData.Columns.Add(GetGlobalResourceObject(ExportPRM_AllocationPriorityobj.UserCulture.ToString(), "Priority").ToString());

                Count = AllocationPriorityArray.AsEnumerable().Count();
                if (Count > 0)
                {
                    for (int i = 0; i < Count; i++)
                    {
                        DtData.Rows.Add(AllocationPriorityArray.ElementAt(i).RefMasterDetail_Name, AllocationPriorityArray.ElementAt(i).OrderClass_Description, AllocationPriorityArray.ElementAt(i).AllocationPriority_Priority);
                    }

                    DataTable DtCriteria = new DataTable();

                    DataTable DtAlignment = new DataTable();
                    DtAlignment.Columns.Add("OrderType");
                    DtAlignment.Columns.Add("OrderClass");
                    DtAlignment.Columns.Add("Priority");
                    DtAlignment.Rows.Add(0, 0, 2);
                    // ReportExport.Export(ExportPRM_AllocationPriorityobj.exprtType, DtData, DtCriteria, DtAlignment, "AllocationPriority", GetGlobalResourceObject(ExportPRM_AllocationPriorityobj.UserCulture.ToString(), "AllocationPriority").ToString());
                }

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(null);
        }
        #endregion


        #region ::: PRM_AllocationPriority List and obj classes  Uday Kumar J B 19-08-2024:::
        /// <summary>
        /// To PRM_AllocationPriority List and obj classes
        /// </summary>
        public class ExportPRM_AllocationPriorityList
        {
            public string UserCulture { get; set; }
            public int exprtType { get; set; }
            public int LanguageID { get; set; }
            public int Company_ID { get; set; }
            public int GeneralLanguageID { get; set; }
        }

        public class SavePRM_AllocationPriorityList
        {
            public string data { get; set; }
            public int User_ID { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }

        public class SelectPRM_AllocationPriorityList
        {
            public int LanguageID { get; set; }
            public int Company_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string UserCulture { get; set; }
        }
        #endregion


        #region ::: AllocationPriority Classes Uday Kumar J B 19-08-2024:::
        /// <summary>
        /// AllocationPriority
        /// </summary>
        public class AllocationPriority
        {
            public int AllocationPriority_ID
            {
                get;
                set;
            }

            public string OrderClass_Description
            {
                get;
                set;
            }
            public string RefMasterDetail_Name
            {
                get;
                set;
            }
            public int AllocationPriority_Priority
            {
                get;
                set;
            }
        }

        public partial class PRM_AllocationPriority
        {
            public int AllocationPriority_ID { get; set; }
            public int Company_ID { get; set; }
            public int OrderType_ID { get; set; }
            public int OrderClass_ID { get; set; }
            public int AllocationPriority_Priority { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }
        }
        #endregion

    }
}
