﻿@using WorkFlow.Models
@using WorkFlow.Utilities
@using System;
<!DOCTYPE html>
<html oncontextmenu="return false"  >
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width" />
    @*    <meta http-equiv="X-UA-Compatible" content="IE=8;FF=3;OtherUA=4" />*@
    @* <meta http-equiv="X-UA-Compatible" content="IE=8,chrome=IE8;FF=3;OtherUA=4">*@

    <title>@ViewBag.Title</title>
    <link href="~/Content/jquery.treeview.css" rel="stylesheet" />
@*    <link rel="stylesheet" id="Blue"  href="@string.Concat(WorkFlow.Models.WFCommon.appPath,"/Content/Themes/",Session["CompanyTheme"].ToString(),"/",Session["CompanyTheme"].ToString(),".css")" />*@
        <link rel="stylesheet" id="Blue"  href="@string.Concat(WorkFlow.Models.WFCommon.appPath,"/Content/Themes/Blue/Blue.css")" />

    <link href="~/Content/jquery.jqplot.min.css" rel="stylesheet" />

    <link href="~/Content/ui.jqgrid.css" rel="stylesheet" />
    <link href="~/Content/Styles/Basic.css" rel="stylesheet" />

    <script src="~/Scripts/JQuery/jquery-1.8.2.js"></script>
    <script src="~/Scripts/JQuery/jquery-ui-1.9.0.custom.min.js"></script>
    <script src="~/Scripts/JQGrid/jquery.jqGrid.min.js"></script>
    <script src="~/Scripts/JQGrid/grid.locale-en.js"></script>
    <script src="~/Scripts/Common/Common.js"></script>
    <script src="~/Scripts/Common/FieldSearch.js" type="text/javascript"></script>
    <script src="~/Scripts/JQuery/jquery.nestedAccordion.js"></script>
    <script src="~/Scripts/JQuery/fullcalendar.js"></script>
    <script src="~/Scripts/jquery-ui-timepicker-addon.js"></script>


    <script type="text/javascript">
        var IsLogout;
      
        $(document).ready(function () {
            $.ajaxSetup({
                cache: false
            });

            $.url = function (url) {
                var path = '@Request.ApplicationPath'
                if (path == '/')
                    return url;
                return path + url;
            }

            $('#logoimg').attr("src", $.url("/images/Quest-logo-185-67-png.png"));
            $('#home').attr("href", $.url("/CoreWFMain/CoreMainView"));
            $('#Logout').attr("href", $.url("/CoreWFLogOut/LogOutView"));

           
            var fn = function (e) {

                if (!e)
                    var e = window.event;

                var keycode = e.keyCode;
                if (e.which)
                    keycode = e.which;

                var src = e.srcElement;
                if (e.target)
                    src = e.target;

                if (116 == keycode || 115 == keycode || 117 == keycode || 118 == keycode || 123 == keycode || (83 == (e.ctrlKey && keycode)) || (115 == (e.ctrlKey && keycode))) {
                    // Firefox and other non IE browsers
                    if (e.preventDefault) {
                        e.preventDefault();
                        e.stopPropagation();
                    }
                        // Internet Explorer
                    else if (e.keyCode) {
                        e.keyCode = 0;
                        e.returnValue = false;
                        e.cancelBubble = true;
                    }

                    return false;
                }
            }



            //try {
            //    window.moveTo(0, 0);
            //    window.resizeTo(screen.availWidth, screen.availHeight);
            //}
            //catch (e) {
            //}
            document.onkeydown = fn;

           @* var a = document.getElementById('Blue');
            a.href = $.url("/Content/Themes/") + '@Session["CompanyTheme"].ToString()' + '/' + '@Session["CompanyTheme"].ToString()' + '.css';*@
           
            var Home = document.getElementById('imgHome');
            Home.src = $.url("/Content/Themes/") + '@Session["CompanyTheme"].ToString()' + '/' + 'images' + '/' + 'Home.png';
            var Logout = document.getElementById('imgLogout');
            Logout.src = $.url("/Content/Themes/") + '@Session["CompanyTheme"].ToString()' + '/' + 'images' + '/' + 'Logout.png';


            try {
                document.getElementById('LblUserName').innerHTML = '@Session["User_Name"]' + ' - ' + '@Session["Branch_Name"]';

                $('#MenuLinks').accordion({
                    expandSub: true,
                    heightStyle: "content"
                });
                //$('.tdSideMenubar').resizable({ ghost: true, maxWidth: 250 });
            }
            catch (e) {
                $(this).LogException("ready", e.description, e.name, e.number);
            }

            $("#imgLogout").click(function () {
                var log = confirm('AreyousureyouwanttoLogout?'); 
                if (log == true) {
                    return true;
                }
                else {
                    return false;
                }
            })


            $.ClearDropDown = function (id) {
                try {
                    var selDropDown = document.getElementById(id);
                    var optionDropDown = selDropDown.options;
                    for (var j = 1; j < optionDropDown.length; j++) {
                        selDropDown.remove(j);
                        j--;
                    }
                }
                catch (e) {
                }
            }
           
            $.absoluteurl = function (url) {
                var path = '@Request.ApplicationPath'
                if (path == '/')
                    return url;
                return path + url;
            }

            $.ClearDropDown('SelRecentActivities');
            $.ajax({
                url: $.url('/CoreWFMain/SelAllRecentMenuActivities'),
                success: function (response) {
                    if (response != null) {
                        var SelRecentActivities = document.getElementById('SelRecentActivities');
                        for (var i = 0; i < response.length; i++) {
                            var opt = new Option(response[i].Menu_Description, response[i].Menu_Path, false, false);
                            SelRecentActivities.add(opt);
                        }
                    }
                }
            });

            $("#imgHome").click(function () {
                $.ajax({
                    url: $.url('/CoreWFMain/CoreMainView')
                });
            });
            $("#SelRecentActivities").focus();
        });

        function ImgClick(moduleID, id) {
           
                $('#MenuLinks').accordion();
                $.post('../Layout/Menu?ModuleId=' + moduleID, function (response) {
                    $("#MenuLinks").empty().append(response);
                    $('#MenuLinks').accordion({ expandSub: true, alwaysOpen: true });
                    $(".SpanSelected A").removeClass('SpanIDSelected');
                    $(".SpanSelected A").addClass('selected');
                    $(id).addClass('SpanIDSelected');
                });
            }

        function MenuClickAction(id) {

            $.post($.url('/Layout/MenuClickAction?id=' + id));
        }

            $.fn.LogException = function (Message, desc, name, number) {
                $.post($.url("/Layout/logException?Message=" + Message + "&Desc=" + desc + "&name=" + name + "&number=" + number), '', function () { }, "Text");
            }

            $.url = function (url) {
                var path = '@Request.ApplicationPath'
                if (path == '/')
                    return url;
                return path + url;
            }

            $.HideMenu = function () {
                $("#Horizontal").hide();
                $("#td").hide();
                $("#td1").hide();
                $("#topMenu").hide();
            }

            function QuickLinks()
            {
                
                var options = document.getElementById("SelRecentActivities").options[document.getElementById("SelRecentActivities").selectedIndex].value;
                window.location = $.url(options);
            }

            var HCollapseCounter = 0;
            var VCollapseCounter = 0;
            function ClickToCollapse() {
                if (HCollapseCounter == 0) {
                    $("#td").toggle();
                    $("#td1").toggle();
                    $("#ImgCollapse").attr("src", $.url("/Images/Ic_Expand-right.png"));
                    $("#ImgCollapse").attr("title", "click to expand");
                    $("#tdrender").attr("colspan", "2");
                    HCollapseCounter = 1;
                }
                else {
                    $("#td").toggle();
                    $("#ImgCollapse").attr("src", $.url("/Images/Ic_Collapse--left.png"));
                    $("#ImgCollapse").attr("title", "click to collapse");
                    $("#tdrender").attr("colspan", "0");
                    $("#td1").toggle();
                    HCollapseCounter = 0;
                }
            }

            function ClickToCollapseTop() {
                if (VCollapseCounter == 0) {
                    $("#topMenu").toggle();
                    $("#ImgCollapseTop").attr("src", $.url("/Images/Bottom--arrow-icon.png"));
                    $("#ImgCollapseTop").attr("title", "click to expand");
                    VCollapseCounter = 1;
                }
                else {
                    $("#topMenu").toggle();
                    $("#ImgCollapseTop").attr("src", $.url("/Images/Top--arrow-icon.png"));
                    $("#ImgCollapseTop").attr("title", "click to collapse");
                    VCollapseCounter = 0;
                }
            }
    </script>
</head>
<body id="body"  >
    <table class="tblExternal" cellspacing="0" cellpadding="0">
        <tr id="topMenu">
            <td colspan="3">
                <div class="header">
                    <div class="title">
                        <div class="headerima">
                            <div class="questlogo">
                                <img width="185px" height="67px" id="logoimg" />
                            </div>
                            <div class="imgtop">
                                <table>
                                    <tr>
                                        <td style="font-size: 13px; font-family: Cambria; vertical-align: text-top;" class="RecentActivities">
                                           
                                        </td>
                                        <td style="font-size: 13px; font-family: Cambria; vertical-align: text-top;">
                                            
                                        </td>
                                        <td>
                                            <a id="home" style="text-decoration: none; border: 0px none transparent">
                                                <img id="imgHome" style="text-decoration: none; border: 0px none transparent;" alt="Home" height="30" width="50" /></a>
                                            <a id="Logout" style="text-decoration: none; border: 0px none transparent">
                                                <img id="imgLogout" style="text-decoration: none; border: 0px none transparent" alt="LogOut" height="30" width="50" /></a>
                                            <br />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="3" style="text-align:right;"><span style="vertical-align: text-top; font-size: 13px; font-family: Cambria" class="RecentActivities">@HttpContext.GetGlobalResourceObject("Resource_en", "Welcome")</span><label class="RecentActivities" id="LblUserName" style="vertical-align: text-top; font-size: 13px; font-family: Cambria"></label></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </td>
        </tr>
        <tr id="Horizontal">
            <td class="subtitle"></td>
            <td colspan="1" class="subtitle">
                <div class="subtitle" style="width: 100%">
                    <nav>
                        <img src="~/images/Ic_Collapse--left.png" id="ImgCollapse" title="click to collapse" onclick="ClickToCollapse()" />@Html.HorizontalMenu()
                    </nav>
                </div>
            </td>
            <td class="subtitle">
                <img src="~/images/Top--arrow-icon.png" id="ImgCollapseTop" title="click to collapse" onclick="ClickToCollapseTop()" />
            </td>
        </tr>
        <tr>
            <td class="tdSideMenubar MenuBackground" id="td" style="height: 800px;width:20%" valign="middle">
                <div id="MenuLinks">
                    @Html.DefaultMenuSidebar()
                </div>
            </td>
            <td style="vertical-align: top; padding: 10px 0px 0px 10px;width:80%;" id="tdrender">
                <div class="content">@RenderBody()</div>
            </td>
            <td>
               
            </td>
        </tr>
    </table>
</body>
</html>
