﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class HelpDeskServiceRequestKnowledgeBaseServices
    {
        // Variable Declaration
        public static int ArticleId = 0;
        public static StringBuilder data = null;
        public static List<int> ExpandId = new List<int>();
        public static List<int> toFilter = new List<int>();
        public static int? FilterId = -1;
        public static bool fromReload = false;
        public static string AppPath = "quest-partsassist\\EPC_UploadedFiles";
        //++++++++++++++++++++++++++++++++++++++


        #region ::: Initial Set Up:::
        /// <summary>
        /// To get initial set up
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult InitialSetup(KnowledgeBase_InitialSetupList OBJ, string connString, int LogException)
        {
            JsonResult jr = null;
            int objectid = 0;
            try
            {
                objectid = Convert.ToInt32(OBJ.ObjectID);
                jr = Common.InitialSetup(objectid, OBJ.User_ID, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jr);
        }
        #endregion

        #region ::: GetContent:::
        /// <summary>
        /// To GetContent
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult GetContent(GetContentList OBJ, string connString, int LogException)
        {
            dynamic data = null;
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    // Define the SQL query
                    string query = @"
                SELECT 
                    Content,
                    IssueArea_ID,
                    IssueSubArea_ID
                FROM 
                    HD_KnowledgeBase
                WHERE 
                    KnowledgeBase_ID = @KnowledgeBase_ID";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@KnowledgeBase_ID", OBJ.Id); // Add parameter to prevent SQL injection

                        conn.Open();
                        SqlDataReader reader = cmd.ExecuteReader();

                        // Read the result set
                        if (reader.HasRows)
                        {
                            while (reader.Read())
                            {
                                data = new
                                {
                                    Content = reader["Content"].ToString(),
                                    IssueArea_ID = reader["IssueArea_ID"],
                                    IssueSubArea_ID = reader["IssueSubArea_ID"]
                                };
                            }
                        }
                        reader.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(data);
        }

        #endregion


        #region ::: To Get All Issue Area:::
        /// <summary>
        /// To Get All Issue Area
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult GetAllArea(GetAllAreaList OBJ, string connString, int LogException)
        {
            dynamic list = null;

            try
            {
                int Company_ID = Convert.ToInt32(OBJ.Company_Id);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    // Define the stored procedure name
                    string storedProcedure = "USP_GetAllIssueArea";

                    using (SqlCommand cmd = new SqlCommand(storedProcedure, conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add the parameter for Company_ID
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);

                        conn.Open();

                        SqlDataReader reader = cmd.ExecuteReader();

                        List<dynamic> resultList = new List<dynamic>();

                        // Read the data from the reader
                        while (reader.Read())
                        {
                            resultList.Add(new
                            {
                                ID = reader["ID"],
                                Name = reader["Name"]
                            });
                        }

                        reader.Close();

                        // Assign the result list to 'list'
                        list = resultList;
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(list);
        }

        #endregion

        #region ::: CheckDuplicate Sibling & Rename:::
        /// <summary>
        /// To CheckDuplicate Sibling & Rename
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult CheckDuplicate_Sibling_Rename(CheckDuplicate_Sibling_RenameList OBJ, string connString, int LogException)
        {
            int result = 0;

            try
            {
                int CurrentId = Convert.ToInt32(OBJ.CurrentFolderId);
                int ParentId = 0;
                string Name = Common.DecryptString(Common.DecryptString(OBJ.FileName.ToString()));
                bool CallFromRename = OBJ.CallFromRename != null;

                // Get ParentId using ADO.NET
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string getParentIdQuery = "SELECT Parent_ID FROM HD_KnowledgeBase WHERE KnowledgeBase_ID = @CurrentId";

                    using (SqlCommand cmd = new SqlCommand(getParentIdQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@CurrentId", CurrentId);

                        conn.Open();
                        ParentId = Convert.ToInt32(cmd.ExecuteScalar());
                        conn.Close();
                    }
                }

                // Call the stored procedure to check for duplicates
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string storedProcedure = "USP_CheckDuplicate_Sibling_Rename";

                    using (SqlCommand cmd = new SqlCommand(storedProcedure, conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters to the stored procedure
                        cmd.Parameters.AddWithValue("@CurrentId", CurrentId);
                        cmd.Parameters.AddWithValue("@FileName", Name);
                        cmd.Parameters.AddWithValue("@ParentId", ParentId);
                        cmd.Parameters.AddWithValue("@CompanyId", OBJ.Company_Id);
                        cmd.Parameters.AddWithValue("@CallFromRename", CallFromRename ? 1 : 0);

                        conn.Open();

                        // Execute the stored procedure and get the result
                        result = Convert.ToInt32(cmd.ExecuteScalar());

                        conn.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(result);
        }

        #endregion

        #region ::: AddSibling:::
        /// <summary>
        /// To AddSibling
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult AddSibling(AddSiblingList OBJ, string connString, int LogException)
        {
            int ParentId = 0;

            try
            {
                string decryptedName = Common.DecryptString(Common.DecryptString(OBJ.Name));

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("USP_AddSibling", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters to the stored procedure
                        cmd.Parameters.AddWithValue("@Id", OBJ.Id);
                        cmd.Parameters.AddWithValue("@Name", decryptedName);
                        cmd.Parameters.AddWithValue("@CompanyId", OBJ.Company_Id);
                        cmd.Parameters.AddWithValue("@Branch", Convert.ToInt32(OBJ.Branch));
                        cmd.Parameters.AddWithValue("@MenuID", Convert.ToInt32(OBJ.MenuID));
                        cmd.Parameters.AddWithValue("@LoggedINDateTime", Convert.ToDateTime(OBJ.LoggedINDateTime));

                        // Open connection, execute the command and get the ParentId
                        conn.Open();
                        ParentId = Convert.ToInt32(cmd.ExecuteScalar());  // ExecuteScalar will return the ParentId
                        conn.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(ParentId);
        }

        #endregion

        #region ::: Check Duplicate Subfolder:::
        /// <summary>
        /// To Check Duplicate Subfolder
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult CheckDuplicateSubfolder(CheckDuplicateSubfolderList OBJ, string connString, int LogException)
        {
            int result = 0;

            try
            {
                // Get the current user and other necessary session values
                int CurrentId = Convert.ToInt32(OBJ.CurrentFolderId);
                string Name = Common.DecryptString(Common.DecryptString(OBJ.FileName.ToString()));

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("USP_CheckDuplicateSubfolder", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters to the stored procedure
                        cmd.Parameters.AddWithValue("@CurrentId", CurrentId);
                        cmd.Parameters.AddWithValue("@FileName", Name);
                        cmd.Parameters.AddWithValue("@CompanyId", OBJ.Company_Id);

                        // Open connection, execute the command, and get the result
                        conn.Open();
                        result = Convert.ToInt32(cmd.ExecuteScalar());  // ExecuteScalar will return the result (1 or 0)
                        conn.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(result);
        }

        #endregion

        #region ::: AddSubfolder:::
        /// <summary>
        /// To Add Subfolder
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult AddSubfolder(AddSubfolderList OBJ, string connString, int LogException)
        {
            int result = OBJ.ParentId;

            try
            {
                // Get the current user and other necessary session values
                string decryptedFileName = Common.DecryptString(Common.DecryptString(OBJ.Name));
                string fileType = "D";

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("USP_AddSubfolder", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters to the stored procedure
                        cmd.Parameters.AddWithValue("@FileName", decryptedFileName);
                        cmd.Parameters.AddWithValue("@FileType", fileType);
                        cmd.Parameters.AddWithValue("@ParentId", OBJ.ParentId);
                        cmd.Parameters.AddWithValue("@CompanyId", OBJ.Company_Id);

                        // Open connection and execute the command
                        conn.Open();
                        cmd.ExecuteNonQuery();  // Execute the insertion
                        conn.Close();
                    }
                }

                // Insert GPS details (you can adjust this as per your existing logic)
                //gbl.InsertGPSDetails(
                //    Convert.ToInt32(Session["Company_ID"].ToString()),
                //    Convert.ToInt32(Session["Branch"]),
                //    User.User_ID,
                //    Common.GetObjectID("HelpDeskServiceRequestKnowledgeBase"),
                //    ParentId,
                //    0,
                //    0,
                //    "Inserted SubFolder- " + decryptedFileName,
                //    false,
                //    Convert.ToInt32(Session["MenuID"]),
                //    Convert.ToDateTime(Session["LoggedINDateTime"])
                //);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(result);
        }

        #endregion


        #region ::: Rename:::
        /// <summary>
        /// To Rename
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult Rename(RenameList OBJ, string connString, int LogException)
        {
            int parentId = 0;

            try
            {
                // Get the current user and other necessary session values
                string decryptedNewName = Common.DecryptString(Common.DecryptString(OBJ.NewName));

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("USP_RenameKnowledgeBase", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters for the stored procedure
                        cmd.Parameters.AddWithValue("@KnowledgeBase_ID", OBJ.Id);
                        cmd.Parameters.AddWithValue("@NewFileName", decryptedNewName);

                        // Open connection
                        conn.Open();

                        // Execute the command and get the Parent_ID
                        object result = cmd.ExecuteScalar();
                        if (result != null)
                        {
                            parentId = Convert.ToInt32(result);
                        }

                        // Close connection
                        conn.Close();
                    }
                }

                // Insert GPS details (you can adjust this as per your existing logic)
                //gbl.InsertGPSDetails(
                //    Convert.ToInt32(Session["Company_ID"].ToString()),
                //    Convert.ToInt32(Session["Branch"]),
                //    User.User_ID,
                //    Common.GetObjectID("HelpDeskServiceRequestKnowledgeBase"),
                //    Id,
                //    0,
                //    0,
                //    "Renamed",
                //    false,
                //    Convert.ToInt32(Session["MenuID"]),
                //    Convert.ToDateTime(Session["LoggedINDateTime"])
                //);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(parentId);
        }

        #endregion

        #region ::: Delete Article:::
        /// <summary>
        /// To Delete Article
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult DeleteArticle(DeleteArticleList OBJ, string connString, int LogException)
        {
            int parentId = 0;

            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("USP_DeleteKnowledgeBaseArticle", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters for the stored procedure
                        cmd.Parameters.AddWithValue("@KnowledgeBase_ID", OBJ.Id);

                        // Open connection
                        conn.Open();

                        // Execute the command and get the Parent_ID
                        object result = cmd.ExecuteScalar();
                        if (result != null)
                        {
                            parentId = Convert.ToInt32(result);
                        }

                        // Close connection
                        conn.Close();
                    }
                }

                // Insert GPS details for deletion action
                //gbl.InsertGPSDetails(
                //    Convert.ToInt32(Session["Company_ID"].ToString()),
                //    Convert.ToInt32(Session["Branch"]),
                //    User.User_ID,
                //    Common.GetObjectID("HelpDeskServiceRequestKnowledgeBase"),
                //    Id,
                //    0,
                //    0,
                //    "Deleted Article",
                //    false,
                //    Convert.ToInt32(Session["MenuID"]),
                //    Convert.ToDateTime(Session["LoggedINDateTime"])
                //);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(parentId);
        }

        #endregion

        #region ::: Check Duplicate Article:::
        /// <summary>
        /// To Check Duplicate Article
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult CheckDuplicateArticle(CheckDuplicateArticleList OBJ, string connString, int LogException)
        {
            int result = 0;

            try
            {
                int parentId = 0;
                string name = string.Empty;
                string fileType = string.Empty;
                string operationType = OBJ.OperationType.ToString();

                if (operationType == "Add" || operationType == "Update")
                {
                    if (operationType == "Add")
                    {
                        parentId = Convert.ToInt32(OBJ.CurrentFolderId);
                        name = Common.DecryptString(Common.DecryptString(OBJ.FileName.ToString()));
                        fileType = OBJ.FileType.ToString();
                    }
                    else if (operationType == "Update")
                    {
                        int currentId = Convert.ToInt32(OBJ.CurrentId);
                        parentId = GetParentId(connString, currentId); // Helper method to get Parent_ID
                        name = Common.DecryptString(Common.DecryptString(OBJ.FileName.ToString()));
                        fileType = OBJ.FileType.ToString();
                    }

                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        using (SqlCommand cmd = new SqlCommand("USP_CheckDuplicateKnowledgeBaseArticle", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;

                            // Add parameters
                            cmd.Parameters.AddWithValue("@OperationType", operationType);
                            cmd.Parameters.AddWithValue("@Parent_ID", parentId);
                            cmd.Parameters.AddWithValue("@FileName", name);
                            cmd.Parameters.AddWithValue("@FileType", fileType);
                            cmd.Parameters.AddWithValue("@Company_ID", OBJ.Company_Id);

                            if (operationType == "Update")
                            {
                                int currentId = Convert.ToInt32(OBJ.CurrentId);
                                cmd.Parameters.AddWithValue("@CurrentId", currentId);
                            }

                            conn.Open();
                            object duplicateResult = cmd.ExecuteScalar();
                            if (duplicateResult != null)
                            {
                                result = Convert.ToInt32(duplicateResult);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(result);
        }

        private static int GetParentId(string connString, int currentId)
        {
            int parentId = 0;

            using (SqlConnection conn = new SqlConnection(connString))
            {
                using (SqlCommand cmd = new SqlCommand("SELECT Parent_ID FROM HD_KnowledgeBase WHERE KnowledgeBase_ID = @KnowledgeBase_ID", conn))
                {
                    cmd.Parameters.AddWithValue("@KnowledgeBase_ID", currentId);

                    conn.Open();
                    object result = cmd.ExecuteScalar();
                    if (result != null)
                    {
                        parentId = Convert.ToInt32(result);
                    }
                }
            }

            return parentId;
        }

        #endregion


        #region ::: Save Article:::
        /// <summary>
        /// To Save Article
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult SaveArticle(SaveArticleList OBJ, string connString, int LogException)
        {
            int newKnowledgeBaseId = 0;

            try
            {

                int parentId = Convert.ToInt32(OBJ.ParentId);
                string fileName = Common.DecryptString(Common.DecryptString(OBJ.FileName.ToString()));
                string content = Common.DecryptString(Common.DecryptString(OBJ.Content).ToString());
                int? issueAreaId = Convert.ToInt32(OBJ.ACode);
                int? issueSubAreaId = Convert.ToInt32(OBJ.SACode);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("USP_SaveKnowledgeBaseArticle", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@FileName", fileName);
                        cmd.Parameters.AddWithValue("@FileType", "A");
                        cmd.Parameters.AddWithValue("@Parent_ID", parentId);
                        cmd.Parameters.AddWithValue("@IssueArea_ID", issueAreaId == 0 ? (object)DBNull.Value : issueAreaId);
                        cmd.Parameters.AddWithValue("@IssueSubArea_ID", issueSubAreaId == 0 ? (object)DBNull.Value : issueSubAreaId);
                        cmd.Parameters.AddWithValue("@Company_ID", OBJ.Company_Id);
                        cmd.Parameters.AddWithValue("@Content", content);

                        // Output parameter to capture the newly inserted KnowledgeBase_ID
                        SqlParameter outputIdParam = new SqlParameter("@NewKnowledgeBaseID", SqlDbType.Int)
                        {
                            Direction = ParameterDirection.Output
                        };
                        cmd.Parameters.Add(outputIdParam);

                        conn.Open();
                        cmd.ExecuteNonQuery();
                        newKnowledgeBaseId = Convert.ToInt32(outputIdParam.Value);
                    }
                }

                // Store the new article ID in session
                ArticleId = newKnowledgeBaseId;

                // Log the GPS details
                //gbl.InsertGPSDetails(
                //    Convert.ToInt32(Session["Company_ID"].ToString()),
                //    Convert.ToInt32(Session["Branch"]),
                //    User.User_ID,
                //    Common.GetObjectID("HelpDeskServiceRequestKnowledgeBase"),
                //    parentId,
                //    0, 0,
                //    "Inserted Article- " + fileName,
                //    false,
                //    Convert.ToInt32(Session["MenuID"]),
                //    Convert.ToDateTime(Session["LoggedINDateTime"])
                //);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(newKnowledgeBaseId);
        }

        #endregion

        #region ::: Update Article:::
        /// <summary>
        /// To Update Article
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult UpdateArticle(UpdateArticleList OBJ, string connString, int LogException)
        {
            int parentID = 0;
            try
            {
                int id = OBJ.ParentId != 0 ? Convert.ToInt32(OBJ.ParentId) : 0;

                ArticleId = id;
                string fileName = Common.DecryptString(Common.DecryptString(OBJ.FileName.ToString()));
                string content = Common.DecryptString(Common.DecryptString(OBJ.Content).ToString());
                int? issueAreaId = Convert.ToInt32(OBJ.ACode);
                int? issueSubAreaId = Convert.ToInt32(OBJ.SACode);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("USP_UpdateKnowledgeBaseArticle", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@KnowledgeBase_ID", id);
                        cmd.Parameters.AddWithValue("@FileName", fileName);
                        cmd.Parameters.AddWithValue("@Content", content);
                        cmd.Parameters.AddWithValue("@IssueArea_ID", issueAreaId == 0 ? (object)DBNull.Value : issueAreaId);
                        cmd.Parameters.AddWithValue("@IssueSubArea_ID", issueSubAreaId == 0 ? (object)DBNull.Value : issueSubAreaId);
                        cmd.Parameters.AddWithValue("@Company_ID", OBJ.Company_Id);

                        conn.Open();
                        cmd.ExecuteNonQuery();
                    }
                }

                // Retrieve parent ID
                parentID = GetParentIdByKnowledgeBaseId(connString, id);

                // Log the GPS details
                //gbl.InsertGPSDetails(
                //    Convert.ToInt32(Session["Company_ID"].ToString()),
                //    Convert.ToInt32(Session["Branch"]),
                //    User.User_ID,
                //    Common.GetObjectID("HelpDeskServiceRequestKnowledgeBase"),
                //    id,
                //    0, 0,
                //    "Updated Article- " + fileName,
                //    false,
                //    Convert.ToInt32(Session["MenuID"]),
                //    Convert.ToDateTime(Session["LoggedINDateTime"])
                //);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(parentID);
        }

        private static int GetParentIdByKnowledgeBaseId(string connString, int knowledgeBaseId)
        {
            int parentID = 0;
            using (SqlConnection conn = new SqlConnection(connString))
            {
                string query = "SELECT Parent_ID FROM HD_KnowledgeBase WHERE KnowledgeBase_ID = @KnowledgeBase_ID";
                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    cmd.Parameters.AddWithValue("@KnowledgeBase_ID", knowledgeBaseId);
                    conn.Open();
                    var result = cmd.ExecuteScalar();
                    parentID = result != null ? Convert.ToInt32(result) : 0;
                }
            }
            return parentID;
        }

        #endregion

        #region ::: Check Attachment:::
        /// <summary>
        /// To Duplicate Check Attachment
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult CheckAttachment(CheckAttachment_List OBJ, string connString, int LogException)
        {
            string result = "No";

            try
            {
                // Extract file name if it contains backslashes
                if (OBJ.fileName.Contains("\\"))
                {
                    int lastIndex = OBJ.fileName.LastIndexOf('\\') + 1;
                    int len = OBJ.fileName.Length - lastIndex;
                    OBJ.fileName = OBJ.fileName.Substring(lastIndex, len);
                }

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "SELECT COUNT(1) FROM HD_KnowledgeBase WHERE FileName = @FileName AND Parent_ID = @Parent_ID";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        // Add parameters to avoid SQL injection
                        cmd.Parameters.AddWithValue("@FileName", OBJ.fileName);
                        cmd.Parameters.AddWithValue("@Parent_ID", OBJ.id);

                        conn.Open();

                        // Execute the query and get the result
                        int attachmentExists = (int)cmd.ExecuteScalar();

                        // Determine result based on the query result
                        result = attachmentExists > 0 ? "Yes" : "No";
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(result);
        }

        #endregion


        #region ::: LoadTree:::
        /// <summary>
        /// To LoadTree
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult LoadTree(LoadTreeList OBJ, string connString, int LogException)
        {
            string result = string.Empty;
            List<HD_KnowledgeBase> KnowledgeBaseList = new List<HD_KnowledgeBase>();

            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Get parent IDs (instead of stored procedure)
                    string KnowledgeBaseQuery = "SELECT * FROM HD_KnowledgeBase";
                    using (SqlCommand cmd = new SqlCommand(KnowledgeBaseQuery, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var refMasterDetailObj = new HD_KnowledgeBase
                                {
                                    KnowledgeBase_ID = reader["KnowledgeBase_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["KnowledgeBase_ID"]),
                                    Parent_ID = reader["Parent_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Parent_ID"]),
                                    IssueArea_ID = reader["IssueArea_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["IssueArea_ID"]),
                                    IssueSubArea_ID = reader["IssueSubArea_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["IssueSubArea_ID"]),
                                    Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                    FileName = reader["FileName"] == DBNull.Value ? null : reader["FileName"].ToString(),
                                    FileType = reader["FileType"] == DBNull.Value ? null : reader["FileType"].ToString(),
                                    Content = reader["Content"] == DBNull.Value ? null : reader["Content"].ToString(),
                                };

                                KnowledgeBaseList.Add(refMasterDetailObj);
                            }
                        }

                    }
                }
                List<HD_KnowledgeBase> dd = KnowledgeBaseList.ToList();
                if (!fromReload)
                    FilterId = OBJ.FilterID == 0 ? -1 : Convert.ToInt32(OBJ.FilterID);
                List<KBList> childList = new List<KBList>();
                List<KBList> forParent = new List<KBList>();
                List<KBList> toExpand = new List<KBList>();
                List<KBList> toFilterList = new List<KBList>();
                data = new StringBuilder();
                data.Append("<ul id='mytree' class='filetree'>");

                List<int> parentIDs = (KnowledgeBaseList.Where(x => x.Parent_ID == 0 && x.Company_ID == OBJ.Company_ID).Select(a => a.KnowledgeBase_ID).ToList());  //to get all parentIDs
                string parentQuery = string.Empty;
                if (OBJ.FileName != string.Empty && OBJ.FileName != "undefined")// updated condition by Kavitha
                {
                    if (FilterId == -1)
                    {
                        parentQuery = " WITH n as (SELECT [KnowledgeBase_ID],[FileName],[Parent_ID],[FileType] FROM dbo.HD_KnowledgeBase WHERE [KnowledgeBase_ID] IN (SELECT KnowledgeBase_ID FROM dbo.HD_KnowledgeBase WHERE FileName Like '%" + OBJ.FileName + "%' AND Company_ID=" + OBJ.Company_ID + ") UNION ALL SELECT child.[KnowledgeBase_ID], child.[FileName], child.[Parent_ID],child.[FileType] FROM dbo.HD_KnowledgeBase AS child JOIN n ON child.[KnowledgeBase_ID] = n.[Parent_ID] AND child.[Company_ID]=" + OBJ.Company_ID + ") Select DISTINCT [KnowledgeBase_ID],[FileName],[Parent_ID],[FileType] from n WHERE FileType='D' and Parent_ID=0 ";// Removed ISSUEAREA_ID is null condition by Kavitha
                    }
                    else
                    {
                        parentQuery = " WITH n as (SELECT [KnowledgeBase_ID],[FileName],[Parent_ID],[FileType] FROM dbo.HD_KnowledgeBase WHERE [KnowledgeBase_ID] IN (SELECT KnowledgeBase_ID FROM dbo.HD_KnowledgeBase WHERE FileName Like '%" + OBJ.FileName + "%'  AND IssueArea_ID = " + FilterId + " AND Company_ID=" + OBJ.Company_ID + ") UNION ALL SELECT child.[KnowledgeBase_ID], child.[FileName], child.[Parent_ID],child.[FileType] FROM dbo.HD_KnowledgeBase AS child JOIN n ON child.[KnowledgeBase_ID] = n.[Parent_ID] AND child.[Company_ID]=" + OBJ.Company_ID + ") Select DISTINCT [KnowledgeBase_ID],[FileName],[Parent_ID],[FileType] from n WHERE FileType='D' and Parent_ID=0 ";
                    }
                }
                else
                {
                    parentQuery = "SELECT [KnowledgeBase_ID],[FileName],[Parent_ID],[FileType] FROM dbo.HD_KnowledgeBase WHERE [Parent_ID] =0 AND Company_ID=" + OBJ.Company_ID;
                }
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();
                    using (SqlCommand cmd = new SqlCommand(parentQuery, connection))
                    {
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                KBList kbItem = new KBList
                                {
                                    KnowledgeBase_ID = reader.IsDBNull(0) ? 0 : reader.GetInt32(0),
                                    FileName = reader.IsDBNull(1) ? string.Empty : reader.GetString(1),
                                    Parent_ID = reader.IsDBNull(2) ? 0 : reader.GetInt32(2),
                                    FileType = reader.IsDBNull(3) ? string.Empty : reader.GetString(3),
                                };
                                forParent.Add(kbItem);
                            }
                        }
                    }
                }
                if (OBJ.OpenId != 0)
                {
                    string toGetAllParentID = ";WITH n as (SELECT [KnowledgeBase_ID],[FileName],[Parent_ID] FROM dbo.HD_KnowledgeBase WHERE [KnowledgeBase_ID] =" + OBJ.OpenId + " AND Company_ID=" + OBJ.Company_ID + " UNION ALL SELECT child.[KnowledgeBase_ID], child.[FileName], child.[Parent_ID] FROM dbo.HD_KnowledgeBase AS child JOIN n ON child.[KnowledgeBase_ID] = n.[Parent_ID] AND child.[Company_ID]=" + OBJ.Company_ID + ") Select [KnowledgeBase_ID] from n";
                    using (SqlConnection connection = new SqlConnection(connString))
                    {
                        connection.Open();
                        using (SqlCommand cmd = new SqlCommand(toGetAllParentID, connection))
                        {
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    KBList kbItem = new KBList
                                    {
                                        KnowledgeBase_ID = reader.IsDBNull(0) ? 0 : reader.GetInt32(0)
                                    };
                                    toExpand.Add(kbItem);
                                }
                            }
                        }
                    }
                    foreach (var item in toExpand)
                    {
                        ExpandId.Add(item.KnowledgeBase_ID);
                    }
                }

                //if (FilterId != -1) //to get all DIR's of filtered IssueArea_ID// commented condition by Kavitha
                //{
                string AllParentIds = string.Empty;
                if (FilterId == -1) // for General IssueArea (IssueArea_ID IS NULL)
                {
                    AllParentIds = ";WITH n as (SELECT [KnowledgeBase_ID],[FileName],[Parent_ID],[FileType] FROM dbo.HD_KnowledgeBase WHERE [KnowledgeBase_ID] IN (SELECT KnowledgeBase_ID FROM dbo.HD_KnowledgeBase WHERE Company_ID=" + OBJ.Company_ID + ") UNION ALL SELECT child.[KnowledgeBase_ID], child.[FileName], child.[Parent_ID],child.[FileType] FROM dbo.HD_KnowledgeBase AS child JOIN n ON child.[KnowledgeBase_ID] = n.[Parent_ID] AND child.[Company_ID] = " + OBJ.Company_ID + ") Select DISTINCT [KnowledgeBase_ID] from n WHERE FileType='D'";// Removed ISSUEAREA_ID is null condition by Kavitha
                    FilterId = -1;//updated null to -1
                }
                else
                {
                    AllParentIds = ";WITH n as (SELECT [KnowledgeBase_ID],[FileName],[Parent_ID],[FileType] FROM dbo.HD_KnowledgeBase WHERE [KnowledgeBase_ID] IN (SELECT KnowledgeBase_ID FROM dbo.HD_KnowledgeBase WHERE Company_ID=" + OBJ.Company_ID + " AND IssueArea_ID=" + FilterId + ") UNION ALL SELECT child.[KnowledgeBase_ID], child.[FileName], child.[Parent_ID],child.[FileType] FROM dbo.HD_KnowledgeBase AS child JOIN n ON child.[KnowledgeBase_ID] = n.[Parent_ID] AND child.[Company_ID] = " + OBJ.Company_ID + ") Select DISTINCT [KnowledgeBase_ID] from n WHERE FileType='D'";
                }
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();
                    using (SqlCommand cmd = new SqlCommand(AllParentIds, connection))
                    {
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                if (!reader.IsDBNull(0))
                                {
                                    toFilter.Add(reader.GetInt32(0));
                                }
                            }
                        }

                    }
                }
                //}//commented by Kavitha

                for (int i = 0; i < forParent.Count(); i++)     //loops for each parent element
                {
                    if (FilterId != -1)
                    {
                        if (toFilter.Contains(forParent.ElementAt(i).KnowledgeBase_ID)) //to filter root elements
                        {
                            if (ExpandId.Contains(forParent.ElementAt(i).KnowledgeBase_ID))//to make expand
                                data.Append("<li class='open'><span class='folder' id='" + forParent.ElementAt(i).KnowledgeBase_ID + "' >" + forParent.ElementAt(i).FileName + "</span>");  //appending parent node
                            else
                                data.Append("<li class='closed'><span class='folder' id='" + forParent.ElementAt(i).KnowledgeBase_ID + "' >" + forParent.ElementAt(i).FileName + "</span>");  //appending parent node
                        }
                    }
                    else
                    {
                        if (ExpandId.Contains(forParent.ElementAt(i).KnowledgeBase_ID))//to make expand
                            data.Append("<li class='open'><span class='folder' id='" + forParent.ElementAt(i).KnowledgeBase_ID + "' >" + forParent.ElementAt(i).FileName + "</span>");  //appending parent node
                        else
                            data.Append("<li class='closed'><span class='folder' id='" + forParent.ElementAt(i).KnowledgeBase_ID + "' >" + forParent.ElementAt(i).FileName + "</span>");  //appending parent node                       
                    }

                    string childQuery = string.Empty;
                    if (OBJ.FileName != string.Empty && OBJ.FileName != "undefined")//updated condition by Kavitha
                    {
                        if (FilterId != -1)
                        {
                            childQuery = " WITH n as (SELECT [KnowledgeBase_ID],[FileName],[Parent_ID],[FileType],[IssueArea_ID] FROM dbo.HD_KnowledgeBase WHERE [KnowledgeBase_ID] IN (SELECT KnowledgeBase_ID FROM dbo.HD_KnowledgeBase WHERE FileName Like '%" + OBJ.FileName + "%'  AND IssueArea_ID = " + FilterId + " AND Company_ID=" + OBJ.Company_ID + ") UNION ALL SELECT child.[KnowledgeBase_ID], child.[FileName], child.[Parent_ID],child.[FileType],child.[IssueArea_ID] FROM dbo.HD_KnowledgeBase AS child JOIN n ON child.[KnowledgeBase_ID] = n.[Parent_ID] AND child.[Company_ID]=" + OBJ.Company_ID + ") Select DISTINCT [KnowledgeBase_ID],[FileName],[Parent_ID],[FileType],[IssueArea_ID] from n WHERE [Parent_ID] =" + forParent.ElementAt(i).KnowledgeBase_ID;
                        }
                        else
                        {
                            childQuery = " WITH n as (SELECT [KnowledgeBase_ID],[FileName],[Parent_ID],[FileType],[IssueArea_ID] FROM dbo.HD_KnowledgeBase WHERE [KnowledgeBase_ID] IN (SELECT KnowledgeBase_ID FROM dbo.HD_KnowledgeBase WHERE FileName Like '%" + OBJ.FileName + "%'   AND Company_ID=" + OBJ.Company_ID + ") UNION ALL SELECT child.[KnowledgeBase_ID], child.[FileName], child.[Parent_ID],child.[FileType],child.[IssueArea_ID] FROM dbo.HD_KnowledgeBase AS child JOIN n ON child.[KnowledgeBase_ID] = n.[Parent_ID] AND child.[Company_ID]=" + OBJ.Company_ID + ") Select DISTINCT [KnowledgeBase_ID],[FileName],[Parent_ID],[FileType],[IssueArea_ID] from n WHERE [Parent_ID] =" + forParent.ElementAt(i).KnowledgeBase_ID;// Removed ISSUEAREA_ID is null condition by Kavitha
                        }
                    }
                    else
                    {
                        childQuery = "SELECT [KnowledgeBase_ID],[FileName],[Parent_ID],[FileType],[IssueArea_ID] FROM dbo.HD_KnowledgeBase WHERE [Parent_ID] =" + forParent.ElementAt(i).KnowledgeBase_ID + " AND Company_ID=" + OBJ.Company_ID;
                    }
                    using (SqlConnection connection = new SqlConnection(connString))
                    {
                        connection.Open();
                        using (SqlCommand cmd = new SqlCommand(childQuery, connection))
                        {
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    KBList kbItem = new KBList
                                    {
                                        KnowledgeBase_ID = reader.IsDBNull(0) ? 0 : reader.GetInt32(0),
                                        FileName = reader.IsDBNull(1) ? string.Empty : reader.GetString(1),
                                        Parent_ID = reader.IsDBNull(2) ? 0 : reader.GetInt32(2),
                                        IssueArea_ID = reader.IsDBNull(4) ? 0 : reader.GetInt32(4),
                                        FileType = reader.IsDBNull(3) ? string.Empty : reader.GetString(3),
                                    };
                                    childList.Add(kbItem);
                                }
                            }
                        }
                    }
                    if (childList.Count() > 0)
                    {
                        data.Append("<ul>");
                        foreach (var obj in childList)  //loops for each child
                        {
                            if (obj.FileType == "D")    //if current child is directory then append element and check for it has got any childs
                            {
                                if (FilterId == -1)
                                {
                                    if (ExpandId.Contains(obj.KnowledgeBase_ID))
                                    {
                                        data.Append("<li class='open'><span class='folder' id='" + obj.KnowledgeBase_ID + "' >" + obj.FileName + "</span>");
                                        cheeckForChildNodes(obj.KnowledgeBase_ID, OBJ.OpenId, OBJ.FileName, OBJ.Company_ID, connString, LogException);
                                    }
                                    else
                                    {
                                        data.Append("<li class='closed'><span class='folder' id='" + obj.KnowledgeBase_ID + "' >" + obj.FileName + "</span>");
                                        cheeckForChildNodes(obj.KnowledgeBase_ID, OBJ.OpenId, OBJ.FileName, OBJ.Company_ID, connString, LogException);
                                    }
                                }
                                else if (FilterId != -1 && toFilter.Contains(obj.KnowledgeBase_ID)) //to filter parent elements
                                {
                                    if (ExpandId.Contains(obj.KnowledgeBase_ID))
                                    {
                                        data.Append("<li class='open'><span class='folder' id='" + obj.KnowledgeBase_ID + "' >" + obj.FileName + "</span>");
                                        cheeckForChildNodes(obj.KnowledgeBase_ID, OBJ.OpenId, OBJ.FileName, OBJ.Company_ID, connString, LogException);
                                    }
                                    else
                                    {
                                        data.Append("<li class='closed'><span class='folder' id='" + obj.KnowledgeBase_ID + "' >" + obj.FileName + "</span>");
                                        cheeckForChildNodes(obj.KnowledgeBase_ID, OBJ.OpenId, OBJ.FileName, OBJ.Company_ID, connString, LogException);
                                    }
                                }
                            }

                            else if (obj.FileType == "A")   //if current child is file then just append it
                            {
                                if (FilterId == -1)
                                {
                                    data.Append("<li class='closed'><span class='file' id='" + obj.KnowledgeBase_ID + "' >" + obj.FileName + "</span></li>");
                                }
                                else if (FilterId != -1 && obj.IssueArea_ID == FilterId)
                                {
                                    data.Append("<li class='closed'><span class='file' id='" + obj.KnowledgeBase_ID + "' >" + obj.FileName + "</span></li>");
                                }
                            }

                            else if (obj.FileType == "F")   //if current child is attachment then just append it
                            {
                                if (FilterId == -1)
                                {
                                    data.Append("<li class='closed'> <span class='attachment' id='" + obj.KnowledgeBase_ID + "' ><a target='_blank' value='" + obj.FileName + "' href='" + AppPath + "/KB_Files/KnowledgeBase_Attachments/" + forParent.ElementAt(i).KnowledgeBase_ID + "/" + obj.FileName + "'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + obj.FileName + "</a></span></li>");
                                }
                                else if (FilterId != -1 && obj.IssueArea_ID == FilterId)
                                {
                                    data.Append("<li class='closed'> <span class='attachment' id='" + obj.KnowledgeBase_ID + "' ><a target='_blank' value='" + obj.FileName + "' href='" + AppPath + "/KB_Files/KnowledgeBase_Attachments/" + forParent.ElementAt(i).KnowledgeBase_ID + "/" + obj.FileName + "'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + obj.FileName + "</a></span></li>");
                                }
                            }

                        }
                        data.Append("</ul></li>");
                    }
                    childList.Clear();
                }
                toFilter.Clear();
                ExpandId.Clear();

                data.Append("</ul>");
                result = data.ToString();
                while (result.Contains("<ul></ul></li>"))//to remove unwanted tags from tree data
                {
                    result = result.Replace("<ul></ul></li>", "");
                }
                if (OBJ.FileName != string.Empty && OBJ.FileName != "undefined")//updated condition by KAvitha
                {
                    result = result.Replace("class='closed'", "class='open'");
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return new JsonResult(result);
        }
        #endregion



        #region ::: cheeckForChildNodes:::
        /// <summary>
        /// To cheeck For Child Nodes
        /// </summary>
        /// <returns>...</returns>
        public static void cheeckForChildNodes(int KnowledgeBase_ID, int OpenId, string FileName, int Company_ID, string connString, int LogException)
        {
            try
            {
                List<KBList> childList = new List<KBList>();
                string childQuery = string.Empty;
                if (FileName != string.Empty && FileName != "undefined")//updated condition by Kavitha
                {
                    if (FilterId != -1)
                    {
                        childQuery = " WITH n as (SELECT [KnowledgeBase_ID],[FileName],[Parent_ID],[FileType],[IssueArea_ID] FROM dbo.HD_KnowledgeBase WHERE [KnowledgeBase_ID] IN (SELECT KnowledgeBase_ID FROM dbo.HD_KnowledgeBase WHERE FileName Like '%" + FileName + "%'  AND IssueArea_ID = " + FilterId + " AND Company_ID=" + Company_ID + ") UNION ALL SELECT child.[KnowledgeBase_ID], child.[FileName], child.[Parent_ID],child.[FileType],child.[IssueArea_ID] FROM dbo.HD_KnowledgeBase AS child JOIN n ON child.[KnowledgeBase_ID] = n.[Parent_ID] AND child.[Company_ID]=" + Company_ID + ") Select DISTINCT [KnowledgeBase_ID],[FileName],[Parent_ID],[FileType],[IssueArea_ID] from n WHERE [Parent_ID] =" + KnowledgeBase_ID;
                    }
                    else
                    {
                        childQuery = " WITH n as (SELECT [KnowledgeBase_ID],[FileName],[Parent_ID],[FileType],[IssueArea_ID] FROM dbo.HD_KnowledgeBase WHERE [KnowledgeBase_ID] IN (SELECT KnowledgeBase_ID FROM dbo.HD_KnowledgeBase WHERE FileName Like '%" + FileName + "%'   AND Company_ID=" + Company_ID + ") UNION ALL SELECT child.[KnowledgeBase_ID], child.[FileName], child.[Parent_ID],child.[FileType],child.[IssueArea_ID] FROM dbo.HD_KnowledgeBase AS child JOIN n ON child.[KnowledgeBase_ID] = n.[Parent_ID] AND child.[Company_ID]=" + Company_ID + ") Select DISTINCT [KnowledgeBase_ID],[FileName],[Parent_ID],[FileType],[IssueArea_ID] from n WHERE [Parent_ID] =" + KnowledgeBase_ID;// Removed ISSUEAREA_ID is null condition by Kavitha
                    }
                }
                else
                {
                    childQuery = "SELECT [KnowledgeBase_ID],[FileName],[Parent_ID],[FileType],[IssueArea_ID] FROM dbo.HD_KnowledgeBase WHERE [Parent_ID] =" + KnowledgeBase_ID + " AND Company_ID=" + Company_ID;
                }
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand(childQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@FileName", "%" + FileName + "%");
                        cmd.Parameters.AddWithValue("@FilterId", FilterId);
                        cmd.Parameters.AddWithValue("@CompanyID", Company_ID);
                        cmd.Parameters.AddWithValue("@KnowledgeBaseID", KnowledgeBase_ID);

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                KBList kb = new KBList
                                {
                                    KnowledgeBase_ID = reader.GetInt32(reader.GetOrdinal("KnowledgeBase_ID")),
                                    FileName = reader.GetString(reader.GetOrdinal("FileName")),
                                    Parent_ID = reader.GetInt32(reader.GetOrdinal("Parent_ID")),
                                    FileType = reader.GetString(reader.GetOrdinal("FileType")),
                                    IssueArea_ID = reader.IsDBNull(reader.GetOrdinal("IssueArea_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("IssueArea_ID"))
                                };
                                childList.Add(kb);
                            }
                        }
                    }
                }
                if (childList.Count() == 0)     //if no child just end the tag
                {
                    data.Append("</li>");
                }
                else
                {
                    data.Append("<ul>");
                    foreach (var obj in childList)  //if it has got childs, loops for each child element
                    {
                        if (obj.FileType == "D")    //if child is a directory then append element and check for it has got any childs by recursive call
                        {
                            if (FilterId == -1)
                            {
                                if (ExpandId.Contains(obj.KnowledgeBase_ID))
                                {
                                    data.Append("<li class='open'><span class='folder' id='" + obj.KnowledgeBase_ID + "' >" + obj.FileName + "</span>");
                                    cheeckForChildNodes(obj.KnowledgeBase_ID, OpenId, FileName, Company_ID, connString, LogException);
                                }
                                else
                                {
                                    data.Append("<li class='closed'><span class='folder' id='" + obj.KnowledgeBase_ID + "' >" + obj.FileName + "</span>");
                                    cheeckForChildNodes(obj.KnowledgeBase_ID, OpenId, FileName, Company_ID, connString, LogException);
                                }
                            }
                            else if (FilterId != -1 && toFilter.Contains(obj.KnowledgeBase_ID)) //to filter parent elements
                            {
                                if (ExpandId.Contains(obj.KnowledgeBase_ID))
                                {
                                    data.Append("<li class='open'><span class='folder' id='" + obj.KnowledgeBase_ID + "' >" + obj.FileName + "</span>");
                                    cheeckForChildNodes(obj.KnowledgeBase_ID, OpenId, FileName, Company_ID, connString, LogException);
                                }
                                else
                                {
                                    data.Append("<li class='closed'><span class='folder' id='" + obj.KnowledgeBase_ID + "' >" + obj.FileName + "</span>");
                                    cheeckForChildNodes(obj.KnowledgeBase_ID, OpenId, FileName, Company_ID, connString, LogException);
                                }
                            }
                        }
                        else if (obj.FileType == "A")  //if current child is file then just append it
                        {
                            if (FilterId == -1)
                            {
                                data.Append("<li class='closed'><span class='file' id='" + obj.KnowledgeBase_ID + "' >" + obj.FileName + "</span></li>");
                            }
                            else if (FilterId != -1 && obj.IssueArea_ID == FilterId)
                            {
                                data.Append("<li class='closed'><span class='file' id='" + obj.KnowledgeBase_ID + "' >" + obj.FileName + "</span></li>");
                            }
                        }
                        else if (obj.FileType == "F")  //if current child is attachment then just append it
                        {
                            if (FilterId == -1)
                            {
                                data.Append("<li class='closed'> <span class='attachment' id='" + obj.KnowledgeBase_ID + "' ><a target='_blank' value='" + obj.FileName + "' href='" + AppPath + "/KB_Files/KnowledgeBase_Attachments/" + KnowledgeBase_ID + "/" + obj.FileName + "'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + obj.FileName + "</a></span></li>");
                            }
                            else if (FilterId != -1 && obj.IssueArea_ID == FilterId)
                            {
                                data.Append("<li class='closed'> <span class='attachment' id='" + obj.KnowledgeBase_ID + "' ><a target='_blank' value='" + obj.FileName + "' href='" + AppPath + "/KB_Files/KnowledgeBase_Attachments/" + KnowledgeBase_ID + "/" + obj.FileName + "'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + obj.FileName + "</a></span></li>");
                            }
                        }
                    }
                    data.Append("</ul></li>");
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion

        #region ::: ReloadTree:::
        /// <summary>
        /// To ReloadTree
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult ReloadTree(ReloadTreeList OBJ, string connString, int LogException)
        {
            LoadTreeList classData = new LoadTreeList
            {
                FilterID = OBJ.IssueAreaID,
                FileName = OBJ.FileName == null ? "" : Common.DecryptString(Common.DecryptString(OBJ.FileName)),
                OpenId = OBJ.OpenId,
                Company_ID = OBJ.Company_ID
            };
            fromReload = true;
            return LoadTree(classData, connString, LogException);
        }
        #endregion

        #region ::: Delete:::
        /// <summary>
        /// To Delete Folder
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult Delete(KnowlegdeBase_DeleteList OBJ, string connString, int LogException)
        {
            int result = -2;
            int companyID = Convert.ToInt32(OBJ.Company_ID);
            List<HD_KnowledgeBase> knowledgeBaseList = new List<HD_KnowledgeBase>();
            IEnumerable<KBList> childList = null;

            try
            {
                // Fetch KnowledgeBase entries from the database
                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Fetch all entries
                    string fetchQuery = "SELECT KnowledgeBase_ID, Parent_ID FROM HD_KnowledgeBase";
                    using (var cmd = new SqlCommand(fetchQuery, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var knowledgeBaseEntry = new HD_KnowledgeBase
                                {
                                    KnowledgeBase_ID = reader["KnowledgeBase_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["KnowledgeBase_ID"]),
                                    Parent_ID = reader["Parent_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Parent_ID"])
                                };

                                knowledgeBaseList.Add(knowledgeBaseEntry);
                            }
                        }
                    }

                    // Get the target object
                    var targetKB = knowledgeBaseList.FirstOrDefault(x => x.KnowledgeBase_ID == OBJ.Id);
                    if (targetKB != null)
                    {
                        result = targetKB.Parent_ID;

                        // Fetch all child IDs using a recursive CTE query
                        string childQuery = $@"
                    ;WITH n AS (
                        SELECT KnowledgeBase_ID, FileName, Parent_ID, Company_ID
                        FROM dbo.HD_KnowledgeBase
                        WHERE KnowledgeBase_ID = @KnowledgeBaseID AND Company_ID = @CompanyID
                        UNION ALL
                        SELECT child.KnowledgeBase_ID, child.FileName, child.Parent_ID, child.Company_ID
                        FROM dbo.HD_KnowledgeBase AS child
                        INNER JOIN n ON child.Parent_ID = n.KnowledgeBase_ID AND n.Company_ID = @CompanyID
                    )
                    SELECT KnowledgeBase_ID, FileName, Parent_ID FROM n";

                        using (var childCmd = new SqlCommand(childQuery, conn))
                        {
                            childCmd.Parameters.AddWithValue("@KnowledgeBaseID", OBJ.Id);
                            childCmd.Parameters.AddWithValue("@CompanyID", companyID);

                            using (var reader = childCmd.ExecuteReader())
                            {
                                var childListTemp = new List<KBList>();
                                while (reader.Read())
                                {
                                    childListTemp.Add(new KBList
                                    {
                                        KnowledgeBase_ID = Convert.ToInt32(reader["KnowledgeBase_ID"]),
                                        FileName = reader["FileName"].ToString(),
                                        Parent_ID = Convert.ToInt32(reader["Parent_ID"])
                                    });
                                }
                                childList = childListTemp;
                            }
                        }

                        // Process child records
                        foreach (var item in childList)
                        {
                            // Delete each record
                            string deleteQuery = "DELETE FROM HD_KnowledgeBase WHERE KnowledgeBase_ID = @KnowledgeBaseID";
                            using (var deleteCmd = new SqlCommand(deleteQuery, conn))
                            {
                                deleteCmd.Parameters.AddWithValue("@KnowledgeBaseID", item.KnowledgeBase_ID);
                                deleteCmd.ExecuteNonQuery();
                            }

                            // Delete associated files
                            string filePath = Path.Combine(AppPath, "KB_Files", "KnowledgeBase_Attachments", item.KnowledgeBase_ID.ToString());
                            DirectoryInfo dirInfo = new DirectoryInfo(filePath);
                            if (dirInfo.Exists)
                            {
                                dirInfo.Delete(true);
                            }
                            //gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"].ToString()), Convert.ToInt32(Session["Branch"]), User.User_ID, Common.GetObjectID("HelpDeskServiceRequestKnowledgeBase"), item.KnowledgeBase_ID, 0, 0, "Deleted Knowledge Base", false, Convert.ToInt32(Session["MenuID"]), Convert.ToDateTime(Session["LoggedINDateTime"]));

                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result = -2;
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(result);
        }
        #endregion

        #region ::: Delete Attachment:::
        /// <summary>
        /// To Delete Attachment
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult DeleteAttachment(KnowlegdeBase_DeleteList OBJ, string connString, int LogException)
        {
            int parentID = -1;
            string appPath = AppPath; // Ensure AppPath is properly set.
            string basePath = Path.Combine(appPath, "KB_Files", "KnowledgeBase_Attachments");

            try
            {
                HD_KnowledgeBase deleteAttachment = null;

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Fetch the specific entry by ID
                    string fetchQuery = "SELECT KnowledgeBase_ID, Parent_ID, FileName FROM HD_KnowledgeBase WHERE KnowledgeBase_ID = @KnowledgeBaseID";
                    using (var cmd = new SqlCommand(fetchQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@KnowledgeBaseID", OBJ.Id);

                        using (var reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                deleteAttachment = new HD_KnowledgeBase
                                {
                                    KnowledgeBase_ID = reader["KnowledgeBase_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["KnowledgeBase_ID"]),
                                    Parent_ID = reader["Parent_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Parent_ID"]),
                                    FileName = reader["FileName"]?.ToString()
                                };
                            }
                        }
                    }

                    if (deleteAttachment != null)
                    {
                        parentID = deleteAttachment.Parent_ID;

                        // Construct the full file path
                        string fullPath = Path.Combine(basePath, deleteAttachment.Parent_ID.ToString(), deleteAttachment.FileName);

                        // Delete the file if it exists
                        FileInfo attach = new FileInfo(fullPath);
                        if (attach.Exists)
                        {
                            attach.Delete();
                        }

                        // Delete the database record
                        string deleteQuery = "DELETE FROM HD_KnowledgeBase WHERE KnowledgeBase_ID = @KnowledgeBaseID";
                        using (var deleteCmd = new SqlCommand(deleteQuery, conn))
                        {
                            deleteCmd.Parameters.AddWithValue("@KnowledgeBaseID", deleteAttachment.KnowledgeBase_ID);
                            deleteCmd.ExecuteNonQuery();
                        }
                    }
                }
                //gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"].ToString()), Convert.ToInt32(Session["Branch"]), User.User_ID, Common.GetObjectID("HelpDeskServiceRequestKnowledgeBase"), Id, 0, 0, "Deleted Attachment", false, Convert.ToInt32(Session["MenuID"]), Convert.ToDateTime(Session["LoggedINDateTime"]));

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(
                        ex.HResult,
                        ex.GetType().FullName + ":" + ex.Message,
                        ex.TargetSite.ToString(),
                        ex.StackTrace
                    );
                }
            }

            return new JsonResult(parentID);
        }

        #endregion


        #region ::: SaveFileToServer:::
        /// <summary>
        /// To Save File To Server
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult SaveFileToServer(IFormFile postedFile, string connString, int LogException, KnowledgeBase_SaveFileToServerList OBJ)
        {
            //AppPath = Request.ApplicationPath == "/" ? "" : Request.ApplicationPath;
            string responseMessage = string.Empty;
            try
            {
                if (postedFile != null)
                {
                    int Id = Convert.ToInt32(OBJ.Id);
                    string fileName = postedFile.FileName;
                    string contentType = postedFile.ContentType;
                    var rowsAffected = -1;
                    Common common = new Common();
                    if (fileName.Contains("\\"))
                    {
                        int lastIndex = fileName.LastIndexOf('\\') + 1;
                        int len = fileName.Length - lastIndex;
                        fileName = fileName.Substring(lastIndex, len);
                    }
                    //string SMP = Server.MapPath("/HelpDesk/KB_Files/KnowledgeBase_Attachments");
                    //string SMP = Server.MapPath(AppPath + "/KB_Files/KnowledgeBase_Attachments");

                    string appPath = AppPath; // Ensure AppPath is properly set.
                    string basePath = Path.Combine(appPath, "KB_Files", "KnowledgeBase_Attachments");
                    string folderPath = Path.Combine(basePath, Id.ToString());
                    string filePath = Path.Combine(folderPath, fileName);
                    if (Directory.Exists(folderPath))
                    {
                        rowsAffected = common.UploadFileItemsForImport1(postedFile.OpenReadStream(), contentType, folderPath, fileName);
                    }
                    else
                    {
                        DirectoryInfo di = Directory.CreateDirectory(folderPath);
                        rowsAffected = common.UploadFileItemsForImport1(postedFile.OpenReadStream(), contentType, folderPath, fileName);
                    }
                    if (rowsAffected > 0)
                    {
                        // Check if a duplicate entry exists and delete it
                        using (var conn = new SqlConnection(connString))
                        {
                            conn.Open();

                            string fetchQuery = "SELECT KnowledgeBase_ID, Parent_ID, FileName FROM HD_KnowledgeBase WHERE FileName = @FileName AND Parent_ID = @ParentID";
                            HD_KnowledgeBase existingEntry = null;

                            using (var cmd = new SqlCommand(fetchQuery, conn))
                            {
                                cmd.Parameters.AddWithValue("@FileName", fileName);
                                cmd.Parameters.AddWithValue("@ParentID", Id);

                                using (var reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        existingEntry = new HD_KnowledgeBase
                                        {
                                            KnowledgeBase_ID = reader["KnowledgeBase_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["KnowledgeBase_ID"]),
                                            Parent_ID = reader["Parent_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Parent_ID"]),
                                            FileName = reader["FileName"].ToString()
                                        };
                                    }
                                }
                            }

                            if (existingEntry != null)
                            {
                                string deleteQuery = "DELETE FROM HD_KnowledgeBase WHERE KnowledgeBase_ID = @KnowledgeBaseID";
                                using (var deleteCmd = new SqlCommand(deleteQuery, conn))
                                {
                                    deleteCmd.Parameters.AddWithValue("@KnowledgeBaseID", existingEntry.KnowledgeBase_ID);
                                    deleteCmd.ExecuteNonQuery();
                                }
                            }

                            // Insert new entry for the uploaded file
                            string insertQuery = @"
                        INSERT INTO HD_KnowledgeBase 
                        (FileName, FileType, Parent_ID, IssueArea_ID, IssueSubArea_ID, Company_ID, Content) 
                        VALUES (@FileName, @FileType, @ParentID, @IssueAreaID, @IssueSubAreaID, @CompanyID, @Content)";
                            using (var insertCmd = new SqlCommand(insertQuery, conn))
                            {
                                insertCmd.Parameters.AddWithValue("@FileName", fileName);
                                insertCmd.Parameters.AddWithValue("@FileType", "F");
                                insertCmd.Parameters.AddWithValue("@ParentID", Id);
                                insertCmd.Parameters.AddWithValue("@IssueAreaID", DBNull.Value);
                                insertCmd.Parameters.AddWithValue("@IssueSubAreaID", DBNull.Value);
                                insertCmd.Parameters.AddWithValue("@CompanyID", OBJ.Company_ID);
                                insertCmd.Parameters.AddWithValue("@Content", DBNull.Value);

                                insertCmd.ExecuteNonQuery();
                            }
                        }
                    }
                }
                responseMessage = "<script>var x=window.open('','_self','','');window.opener = null;x.close();</script>";

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(
                        ex.HResult,
                        ex.GetType().FullName + ":" + ex.Message,
                        ex.TargetSite.ToString(),
                        ex.StackTrace
                    );
                }

                // Optional: Update response message with error details
                responseMessage = "An error occurred while saving the file.";
            }
            return new JsonResult(responseMessage);
        }
        #endregion
    }

    //Properties
    public class KnowledgeBase_SaveFileToServerList
    {
        public int Company_ID { get; set; }
        public int Id { get; set; }
    }
    public partial class KnowledgeBase_InitialSetupList
    {
        public int ObjectID { get; set; }
        public int User_ID { get; set; }
    }
    public partial class GetContentList
    {
        public int Id { get; set; }
    }
    public partial class GetAllAreaList
    {
        public int Company_Id { get; set; }
    }
    public partial class CheckDuplicate_Sibling_RenameList
    {
        public int Company_Id { get; set; }
        public int CurrentFolderId { get; set; }
        public string FileName { get; set; }
        public string CallFromRename { get; set; }
    }
    public partial class AddSiblingList
    {
        public int Company_Id { get; set; }
        public int Id { get; set; }
        public int Branch { get; set; }
        public int MenuID { get; set; }
        public string Name { get; set; }
        public DateTime LoggedINDateTime { get; set; }
    }
    public partial class CheckDuplicateSubfolderList
    {
        public int Company_Id { get; set; }
        public int CurrentFolderId { get; set; }
        public string FileName { get; set; }

    }
    public partial class AddSubfolderList
    {
        public int Company_Id { get; set; }
        public int ParentId { get; set; }
        public int Branch { get; set; }
        public int MenuID { get; set; }
        public string Name { get; set; }
        public DateTime LoggedINDateTime { get; set; }
    }
    public partial class RenameList
    {
        public int Company_Id { get; set; }
        public int Id { get; set; }
        public int ParentId { get; set; }
        public int Branch { get; set; }
        public int MenuID { get; set; }
        public string NewName { get; set; }
        public DateTime LoggedINDateTime { get; set; }
    }
    public partial class DeleteArticleList
    {
        public int Company_Id { get; set; }
        public int Id { get; set; }
        public int ParentId { get; set; }
        public int Branch { get; set; }
        public int MenuID { get; set; }
        public string NewName { get; set; }
        public DateTime LoggedINDateTime { get; set; }
    }
    public partial class CheckDuplicateArticleList
    {
        public int Company_Id { get; set; }
        public int CurrentId { get; set; }
        public int CurrentFolderId { get; set; }
        public string OperationType { get; set; }
        public string FileName { get; set; }
        public string FileType { get; set; }
    }
    public partial class SaveArticleList
    {
        public int Company_Id { get; set; }
        public int ParentId { get; set; }
        public int ACode { get; set; }
        public int SACode { get; set; }
        public string Content { get; set; }
        public string FileName { get; set; }
        public string FileType { get; set; }
    }
    public partial class UpdateArticleList
    {
        public int Company_Id { get; set; }
        public int ParentId { get; set; }
        public int ACode { get; set; }
        public int SACode { get; set; }
        public string Content { get; set; }
        public string FileName { get; set; }
        public string FileType { get; set; }
    }
    public partial class CheckAttachment_List
    {
        public int id { get; set; }
        public string fileName { get; set; }
    }
    public partial class LoadTreeList
    {
        public int Company_ID { get; set; }
        public int FilterID { get; set; }
        public int OpenId { get; set; }
        public string FileName { get; set; }
    }
    public partial class ReloadTreeList
    {
        public int Company_ID { get; set; }
        public int IssueAreaID { get; set; }
        public int OpenId { get; set; }
        public string FileName { get; set; }
    }
    public partial class KnowlegdeBase_DeleteList
    {
        public int Company_ID { get; set; }
        public int Id { get; set; }
        public string FileName { get; set; }
    }

    public partial class HD_KnowledgeBase
    {
        public int KnowledgeBase_ID { get; set; }
        public string FileName { get; set; }
        public int Parent_ID { get; set; }
        public string FileType { get; set; }
        public string Content { get; set; }
        public Nullable<int> IssueArea_ID { get; set; }
        public Nullable<int> IssueSubArea_ID { get; set; }
        public Nullable<int> Company_ID { get; set; }
    }
    public partial class cheeckForChildNodesList
    {
        public int Company_ID { get; set; }
        public int FilterId { get; set; }
        public int OpenId { get; set; }
        public string FileName { get; set; }
        public int KnowledgeBase_ID { get; set; }
    }

    public partial class KBList
    {
        public int KnowledgeBase_ID { get; set; }
        public string FileName { get; set; }
        public int? Parent_ID { get; set; }
        public string FileType { get; set; }
        public string Content { get; set; }
        public int? IssueArea_ID { get; set; }
        public int? IssueSubArea_ID { get; set; }
        public int Company_Id { get; set; }
    }
}
