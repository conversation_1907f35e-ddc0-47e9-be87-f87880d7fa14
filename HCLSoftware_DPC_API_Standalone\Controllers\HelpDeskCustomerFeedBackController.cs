﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.HelpDeskCustomerFeedBackServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class HelpDeskCustomerFeedBackController : ApiController
    {

        #region ::: Select Case Registration:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskCustomerFeedBack/SelectServiceRequest")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectServiceRequest([FromBody] SelectServiceRequestaList SelectServiceRequestObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskCustomerFeedBackServices.SelectServiceRequest(SelectServiceRequestObj, Conn, LogException, HelpDesk, sidx, sord, page, rows, _search, filters, advnce, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);
        }

        #endregion





        #region::: To select all Tickets and Work Order Pending for Customer Feedback Mithun:::
        /// <summary>
        /// To select all Tickets and Work Order Pending for Customer Feedback
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskCustomerFeedBack/SelectALLTicketsandWOPendingforCustomerFeedBack")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectALLTicketsandWOPendingforCustomerFeedBack([FromBody] SelectALLTicketsandWOPendingforCustomerFeedBackList SelectALLTicketsandWOPendingforCustomerFeedBackObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskCustomerFeedBackServices.SelectALLTicketsandWOPendingforCustomerFeedBack(SelectALLTicketsandWOPendingforCustomerFeedBackObj, Conn, LogException, sidx, sord, page, rows, _search, filters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SavecustomerFeedbackDetails /Mithun:::
        /// <summary>
        /// To Save Customer Feedback Details
        /// </summary>
        [Route("api/HelpDeskCustomerFeedBack/SavecustomerFeedbackDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SavecustomerFeedbackDetails([FromBody] SavecustomerFeedbackDetailsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskCustomerFeedBackServices.SavecustomerFeedbackDetails(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region::: Exprot /Mithun:::

        [Route("api/HelpDeskCustomerFeedBack/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] SelectALLTicketsandWOPendingforCustomerFeedBackList ExportObj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportObj.sidx;
            string sord = ExportObj.sord;
            string filters = ExportObj.filters;
            string Query = ExportObj.Query;

            try
            {


                Object Response = await HelpDeskCustomerFeedBackServices.Export(ExportObj, connstring, LogException, filters, Query, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }

        #endregion



    }
}
