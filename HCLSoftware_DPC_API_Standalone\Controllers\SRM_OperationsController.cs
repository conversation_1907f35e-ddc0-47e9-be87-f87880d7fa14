﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.SRM_OperationsServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;



namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class SRM_OperationsController : ApiController
    {

        #region ::: Select Uday Kumar J B 02-8-2024 :::
        /// <summary>
        /// To select All product
        /// </summary>
        /// 
        [Route("api/SRM_Operations/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] SelectOperationList SelectOperationobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = " ";


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = SRM_OperationsServices.Select(connstring, SelectOperationobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Insert Uday Kumar J B  02-08-2024:::
        /// <summary>
        /// To Insert Product
        /// </summary>
        /// 
        [Route("api/SRM_Operations/Insert")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Insert([FromBody] InsertOperationsList InsertOperationsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_OperationsServices.Insert(connString, InsertOperationsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Update Uday Kumar J B 02-08-2024 :::
        /// <summary>
        /// To Update Operation header
        /// </summary>
        /// 
        [Route("api/SRM_Operations/Update")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Update([FromBody] UpdateOperationsList UpdateOperationsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_OperationsServices.Update(connString, UpdateOperationsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Delete Uday Kumar J B 02-08-2024:::
        /// <summary>
        ///To Delete Operation
        /// </summary> 
        /// 
        [Route("api/SRM_Operations/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteOperationList DeleteOperationobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_OperationsServices.Delete(connString, DeleteOperationobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Export Uday Kumar J B 02-08-2024 :::
        /// <summary>
        /// Exporting Company Grid
        /// </summary>
        /// 
        [Route("api/SRM_Operations/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Export([FromBody] ExportOperationsList ExportOperationsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                // Response = SRM_OperationsServices.Export(connString, ExportOperationsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectOperationProductDetail Uday Kumar J B 02-08-2024 :::
        /// <summary>
        /// To Select Operation Product Detail
        /// </summary>
        /// 
        [Route("api/SRM_Operations/SelectOperationProductDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectOperationProductDetail([FromBody] SelectOperationProductDetailList SelectOperationProductDetailobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = " ";


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = SRM_OperationsServices.SelectOperationProductDetail(connstring, SelectOperationProductDetailobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectOperationBranchDetail Uday Kumar J B 02-08-2024 :::
        /// <summary>
        /// To Select Operation Branch Detail
        /// </summary>
        /// 
        [Route("api/SRM_Operations/SelectOperationBranchDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectOperationBranchDetail([FromBody] SelectOperationBranchDetailList SelectOperationBranchDetailobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = " ";


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = SRM_OperationsServices.SelectOperationBranchDetail(connstring, SelectOperationBranchDetailobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectOperationCheckListDetail Uday Kumar J B 02-08-2024 :::
        /// <summary>
        /// To Select Operation Branch Detail
        /// </summary>
        /// 
        [Route("api/SRM_Operations/SelectOperationCheckListDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectOperationCheckListDetail([FromBody] SelectOperationCheckListDetailList SelectOperationCheckListDetailobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = " ";


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = SRM_OperationsServices.SelectOperationCheckListDetail(connstring, SelectOperationCheckListDetailobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectReferenceMaster Uday Kumar J B 02-08-2024:::
        /// <summary>
        /// To get Refrence Master records for a Master
        /// </summary> 
        /// 
        [Route("api/SRM_Operations/SelectReferenceMaster")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectReferenceMaster([FromBody] SelectReferenceMasteroperationList SelectReferenceMasteroperationobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_OperationsServices.SelectReferenceMaster(connString, SelectReferenceMasteroperationobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectProductType Uday Kumar J B 02-08-2024 :::
        /// <summary>
        /// To Select Product type
        /// </summary> 
        /// 
        [Route("api/SRM_Operations/SelectProductType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectProductType([FromBody] SelectProductTypeLista SelectProductTypeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_OperationsServices.SelectProductType(connString, SelectProductTypeobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectModel Uday Kumar J B 02-08-2024:::
        /// <summary>
        /// To Select Model
        /// </summary> 
        /// 
        [Route("api/SRM_Operations/SelectModel")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectModel([FromBody] SelectModelList SelectModelobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_OperationsServices.SelectModel(connString, SelectModelobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectFunctionGroup Uday Kumar J B 02-08-2024 :::
        /// <summary>
        /// To get Function Group master
        /// </summary> 
        ///
        [Route("api/SRM_Operations/SelectFunctionGroup")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectFunctionGroup([FromBody] SelectFunctionGroupList SelectFunctionGroupobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_OperationsServices.SelectFunctionGroup(connString, SelectFunctionGroupobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveOperationProductDetail Uday Kumar J B 02-08-2024 :::
        /// <summary>
        /// To Insert and Update Product type
        /// </summary>
        /// 
        [Route("api/SRM_Operations/SaveOperationProductDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveOperationProductDetail([FromBody] SaveOperationProductDetailList SaveOperationProductDetailobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_OperationsServices.SaveOperationProductDetail(connString, SaveOperationProductDetailobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveOperationBranchDetail Uday Kumar J B 02-08-2024:::
        /// <summary>
        /// To Insert and Update Operation Branch Detail
        /// </summary>
        /// 
        [Route("api/SRM_Operations/SaveOperationBranchDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveOperationBranchDetail([FromBody] SaveOperationBranchDetailList SaveOperationBranchDetailobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_OperationsServices.SaveOperationBranchDetail(connString, SaveOperationBranchDetailobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveOperationCheckListDetail Uday Kumar J B 06-08-2024 :::
        /// <summary>
        /// To Insert and Update Operation CheckList Detail
        /// </summary>
        /// 
        [Route("api/SRM_Operations/SaveOperationCheckListDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveOperationCheckListDetail([FromBody] SaveOperationCheckListDetailList SaveOperationCheckListDetailobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_OperationsServices.SaveOperationCheckListDetail(connString, SaveOperationCheckListDetailobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeleteOperationProductDetail Uday Kumar J B 06-08-2024:::
        /// <summary>
        ///To Delete OperationProductDetail
        /// </summary>  
        /// 
        [Route("api/SRM_Operations/DeleteOperationProductDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteOperationProductDetail([FromBody] DeleteOperationProductDetailList DeleteOperationProductDetailobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_OperationsServices.DeleteOperationProductDetail(connString, DeleteOperationProductDetailobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeleteOperationBranchDetail Uday Kumar J B 06-08-2024:::
        /// <summary>
        ///To Delete OperationBranchDetail
        /// </summary>  
        /// 
        [Route("api/SRM_Operations/DeleteOperationBranchDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteOperationBranchDetail([FromBody] DeleteOperationBranchDetailList DeleteOperationBranchDetailobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_OperationsServices.DeleteOperationBranchDetail(connString, DeleteOperationBranchDetailobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeleteOperationCheckListDetail Uday Kumar J B 06-08-2024:::
        /// <summary>
        ///To Delete Operation CheckList Detail
        /// </summary> 
        /// 
        [Route("api/SRM_Operations/DeleteOperationCheckListDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteOperationCheckListDetail([FromBody] DeleteOperationCheckListDetailList DeleteOperationCheckListDetailobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_OperationsServices.DeleteOperationCheckListDetail(connString, DeleteOperationCheckListDetailobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveOperationLocale Uday Kumar J B 06-08-2024:::
        /// <summary>
        /// To Save Operation Locale
        /// </summary>
        /// 
        [Route("api/SRM_Operations/SaveOperationLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveOperationLocale([FromBody] SaveOperationLocaleList SaveOperationLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_OperationsServices.SaveOperationLocale(connString, SaveOperationLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveOperationChecklistLocale Uday Kumar J B 06-08-2024 :::
        /// <summary>
        /// To Save Operation Locale
        /// </summary>
        /// 
        [Route("api/SRM_Operations/SaveOperationChecklistLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveOperationChecklistLocale([FromBody] SaveOperationChecklistLocaleList SaveOperationChecklistLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_OperationsServices.SaveOperationChecklistLocale(connString, SaveOperationChecklistLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckOperationCode Uday Kumar J B 06-08-2024:::
        /// <summary>
        /// To Check if OperationCode already exists
        /// </summary>
        /// 
        [Route("api/SRM_Operations/CheckOperationCode")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckOperationCode([FromBody] CheckOperationCodeList CheckOperationCodeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_OperationsServices.CheckOperationCode(connString, CheckOperationCodeobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckChecklist Uday Kumar J B 06-08-2024:::
        /// <summary>
        /// To check if the CheckList Description is already used
        /// </summary>
        /// 
        [Route("api/SRM_Operations/CheckCheckListDescription")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckCheckListDescription([FromBody] CheckCheckListDescriptionList CheckCheckListDescriptionobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_OperationsServices.CheckCheckListDescription(connString, CheckCheckListDescriptionobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckChecklistLocale Uday Kumar J B 06-08-2024:::
        /// <summary>
        /// To check if the CheckList Description is already used
        /// </summary>
        /// 
        [Route("api/SRM_Operations/CheckCheckListDescriptionLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckCheckListDescriptionLocale([FromBody] CheckCheckListDescriptionLocaleList CheckCheckListDescriptionLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_OperationsServices.CheckCheckListDescriptionLocale(connString, CheckCheckListDescriptionLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckBranch Uday Kumar J B 06-08-2024:::
        /// <summary>
        /// To Check Branch
        /// </summary>
        /// 
        [Route("api/SRM_Operations/CheckBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckBranch([FromBody] CheckBranchList CheckBranchobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_OperationsServices.CheckBranch(connString, CheckBranchobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckProductDetail Uday Kumar J B 06-08-2024:::
        /// <summary>
        /// To Check Product Detail
        /// </summary>
        /// 
        [Route("api/SRM_Operations/CheckProductDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckProductDetail([FromBody] CheckProductDetailList CheckProductDetailobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = SRM_OperationsServices.CheckProductDetail(connString, CheckProductDetailobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectSRTHistory Uday Kumar J B 06-08-2024 :::
        /// <summary>
        /// To Select SRT History
        /// </summary>
        /// 
        [Route("api/SRM_Operations/SelectSRTHistory")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSRTHistory([FromBody] SelectSRTHistoryList SelectSRTHistoryobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = " ";


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = SRM_OperationsServices.SelectSRTHistory(connstring, SelectSRTHistoryobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion

    }
}