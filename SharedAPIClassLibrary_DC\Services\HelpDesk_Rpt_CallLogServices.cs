﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.CoreProductMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace SharedAPIClassLibrary_AMERP
{
    public class HelpDesk_Rpt_CallLogServices
    {
        public static List<string> CallLogBranchName = new List<string>();
        public static string CallLogFromDate = "";
        public static string CallLogToDate = "";
        public static List<SP_GENERATECALLLOG_Result> CallLog = new List<SP_GENERATECALLLOG_Result>();
        #region InitialSetUp vinay n 20/11/24
        public static IActionResult InitialSetUp(InitialSetUpList Obj, string connString, int LogException)
        {
            var jsonResult = default(dynamic);
            JsonResult jr = null;
            int objectid = 0;

            try
            {
                objectid = Convert.ToInt32(Obj.ObjectID);
                jr = Common.InitialSetup(objectid, Obj.User_ID, connString, LogException);
                // Modified by Manju P on 28-sep-2015 for TML call SR-TML-30-2015 -- Added company filter
                int? Company_ID = Convert.ToInt32(Obj.Company_ID);
                string query = "SELECT Param_value FROM GNM_CompParam WHERE UPPER(Param_Name) = 'CHANGESERIALNUMTOVCNUM' AND Company_ID = @CompanyID";
                List<SqlParameter> parameters = new List<SqlParameter>
                {
                    new SqlParameter("@CompanyID", Company_ID)
                };

                bool CHANGESERIALNUMTOVCNUM = GetValueFromDB<string>(query, parameters, connString)?.ToUpper() == "TRUE";
                // Changes end -- Manju P 
                jsonResult = new
                {
                    jr,
                    CHANGESERIALNUMTOVCNUM
                };
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return new JsonResult(jsonResult);
        }
        #endregion

        #region  vinay n ado.net common vinay n 20/11/24
        public static T GetValueFromDB<T>(string query, List<SqlParameter> parameters, string connectionString)
        {
            T result = default;

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                conn.Open();

                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    // Add parameters to the command
                    if (parameters != null && parameters.Count > 0)
                    {
                        cmd.Parameters.AddRange(parameters.ToArray());
                    }

                    // Execute the query
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        // If the result type is a collection (e.g., List<T>)
                        if (typeof(T).IsGenericType && typeof(T).GetGenericTypeDefinition() == typeof(List<>))
                        {

                            var list = Activator.CreateInstance<T>();
                            var columnPropertyMapping = typeof(T).GetGenericArguments()[0]
                                    .GetProperties()
                                    .Where(property => Enumerable.Range(0, reader.FieldCount)
                                                                 .Select(reader.GetName)
                                                                 .Contains(property.Name))
                                    .Select(property => new
                                    {
                                        Property = property,
                                        Ordinal = reader.GetOrdinal(property.Name),
                                        TargetType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType
                                    })
                                    .ToList();
                            while (reader.Read())
                            {
                                var item = Activator.CreateInstance(typeof(T).GetGenericArguments()[0]);

                                foreach (var mapping in columnPropertyMapping)
                                {
                                    var value = reader[mapping.Ordinal];
                                    if (value != DBNull.Value)
                                    {
                                        // Set property value directly using the precomputed type
                                        mapping.Property.SetValue(item, Convert.ChangeType(value, mapping.TargetType));
                                    }
                                }

                              ((IList)list).Add(item);
                            }
                            result = (T)list;
                        }
                        // If the result is a scalar type (like string, bool, int)
                        else if (typeof(T) == typeof(string))
                        {
                            if (reader.Read())
                            {
                                result = (T)Convert.ChangeType(reader[0], typeof(T));
                            }
                        }
                        else if (typeof(T) == typeof(bool))
                        {
                            if (reader.Read())
                            {
                                result = (T)Convert.ChangeType(reader[0].ToString().ToUpper() == "TRUE", typeof(T));
                            }
                        }
                        else if (typeof(T) == typeof(int))
                        {
                            if (reader.Read())
                            {
                                result = (T)Convert.ChangeType(reader[0], typeof(T));
                            }
                        }
                        // Handle cases for custom classes/complex objects
                        else if (typeof(T).IsClass && typeof(T) != typeof(string))
                        {
                            if (reader.Read())
                            {
                                result = Activator.CreateInstance<T>(); // Create an instance of the class
                                foreach (var property in typeof(T).GetProperties())
                                {
                                    // If the column exists in the reader, set the corresponding property
                                    if (reader.GetOrdinal(property.Name) >= 0)
                                    {
                                        var value = reader[property.Name];
                                        if (value != DBNull.Value)
                                        {
                                            property.SetValue(result, Convert.ChangeType(value, property.PropertyType));
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return result;
        }


        #endregion
        #region Select vinay n 20/11/24
        public static IActionResult Select(SelectHelpDesk_Rpt_CallLogList Obj, string connString, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters, bool advnce, string _Query)
        {


            var jsonResult = default(dynamic);
            try
            {
                int Count = 0;
                int Total = 0;
                string frmdate = Obj.FromDate;
                string todate = Obj.ToDate;
                int userLanguageID = Convert.ToInt32(Obj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(Obj.GeneralLanguageID);
                List<string> BranchName = new List<string>();

                int? UserCompany_ID = Convert.ToInt32(Obj.Company_ID);


                CallLogFromDate = frmdate;
                CallLogToDate = todate;
                string queryCHANGESERIALNUMTOVCNUM = @"
                SELECT Param_value
                FROM GNM_CompParam
                WHERE UPPER(Param_Name) = 'CHANGESERIALNUMTOVCNUM' AND Company_ID = @CompanyID";
                List<SqlParameter> parametersCHANGESERIALNUMTOVCNUM = new List<SqlParameter>
                {
                    new SqlParameter("@CompanyID", UserCompany_ID)
                };

                bool CHANGESERIALNUMTOVCNUM = GetValueFromDB<string>(queryCHANGESERIALNUMTOVCNUM, parametersCHANGESERIALNUMTOVCNUM, connString) == "TRUE" ? true : false;
                // Chnages end
                //Added by Venkateshwari for HelpDesk CR-5 Changes 19-Aug-2015.... Start.
                string BranchIDs = string.Empty;
                Top_Branch branchs = new Top_Branch();
                if (Obj.Branch != null && Obj.Branch != "")
                {
                    string decodedValue = Uri.UnescapeDataString(Obj.Branch);
                    branchs = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<Top_Branch>();

                    for (int i = 0; i < branchs.Branchs.Count; i++)
                    {
                        BranchIDs = BranchIDs + branchs.Branchs[i].ID + ", ";
                    }
                    BranchIDs = BranchIDs.Remove(BranchIDs.LastIndexOf(','), 1);
                }
                string Company_ID = string.Empty;
                Company companys = new Company();
                if (Obj.CompanyData != null && Obj.CompanyData != "")
                {
                    string decodedValue = Uri.UnescapeDataString(Obj.CompanyData);
                    companys = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<Company>(); //~Manju M
                }

                if (Obj.CompanyData != null && Obj.CompanyData != "")
                {

                    for (int i = 0; i < companys.Companys.Count; i++)
                    {
                        Company_ID = Company_ID + companys.Companys[i].ID + ", ";
                    }
                    Company_ID = Company_ID.Remove(Company_ID.LastIndexOf(','), 1);
                    //Session["CompanyID"] = Company_ID;

                }
                string query = "SELECT Branch_ID,Branch_Name FROM GNM_Branch";
                List<GNM_Branch> branch = GetValueFromDB<List<GNM_Branch>>(query, null, connString);


                //BranchName = Branch_ID == 0 ? string.Empty : companyClient.GNM_Branch.Where(i => i.Branch_ID == Branch_ID).Select(i => i.Branch_Name).FirstOrDefault();
                BranchName = (from a in branch
                              join b in branchs.Branchs on a.Branch_ID equals b.ID
                              select a.Branch_Name).ToList<string>();

                CallLogBranchName = BranchName;
                DateTime FDate = Convert.ToDateTime(frmdate);
                DateTime TDate = Convert.ToDateTime(todate);
                TDate = TDate.AddDays(1);
                List<SP_GENERATECALLLOG_Result> CallLogList = new List<SP_GENERATECALLLOG_Result>();
                IQueryable<SP_GENERATECALLLOG_Result> iQCallLogResultTemp = null; //Modified by Venkateshwari for HelpDesk CR-5 Changes 19-Aug-2015.
                StringBuilder Query = new StringBuilder();
                Query.Append(" SELECT M1.TRANSACTION_ID,CASE (M1.ADDRESSE_FLAG) WHEN 0 THEN T.WFROLE_NAME ELSE S.USER_NAME END AS QUERYESCALATEDTO,REPLACE(CONVERT(VARCHAR(11), ");
                Query.Append(" M1.RECEIVED_TIME, 106), ' ', '-')+' '+ CONVERT(VARCHAR(5), M1.RECEIVED_TIME, 24) AS ESCALATEDDATETIME,CC.REFMASTERDETAIL_NAME AS DEPARTMENT,");
                Query.Append(" DD.REFMASTERDETAIL_NAME AS DESIGNATION,EE.COMPANY_EMPLOYEE_NAME AS MANAGER ");
                Query.Append(" INTO #TEMPDATE FROM GNM_WFCASE_PROGRESS M1 ");
                Query.Append(" LEFT JOIN GNM_WFCASE_PROGRESS M2 ON (M1.TRANSACTION_ID = M2.TRANSACTION_ID AND M1.WFCASEPROGRESS_ID < M2.WFCASEPROGRESS_ID) ");
                Query.Append(" JOIN HD_SERVICEREQUEST H ON M1.TRANSACTION_ID=H.SERVICEREQUEST_ID AND H.COMPANY_ID in (" + Company_ID + ") AND H.BRANCH_ID IN (" + BranchIDs + ")");
                Query.Append(" LEFT OUTER JOIN GNM_USER S ON M1.ADDRESSE_ID=S.USER_ID ");
                Query.Append(" LEFT OUTER JOIN GNM_WFROLE T ON M1.ADDRESSE_ID=T.WFROLE_ID ");
                Query.Append(" JOIN GNM_COMPANYEMPLOYEE BB ON S.EMPLOYEE_ID=BB.COMPANY_EMPLOYEE_ID ");
                Query.Append(" JOIN GNM_REFMASTERDETAIL CC ON BB.COMPANY_EMPLOYEE_DEPARTMENT_ID=CC.REFMASTERDETAIL_ID");
                Query.Append(" JOIN GNM_REFMASTERDETAIL DD ON BB.COMPANY_EMPLOYEE_DESIGNATION_ID=DD.REFMASTERDETAIL_ID ");
                Query.Append(" LEFT OUTER JOIN GNM_COMPANYEMPLOYEE EE ON BB.COMPANY_EMPLOYEE_MANAGER_ID=EE.COMPANY_EMPLOYEE_ID ");
                Query.Append(" WHERE USER_ID IN (SELECT USER_ID FROM GNM_USER WHERE USER_ID=M1.ADDRESSE_ID) AND M2.WFCASEPROGRESS_ID IS NULL ");
                Query.Append(" AND H.SERVICEREQUESTDATE >= '" + FDate.ToString("dd-MMM-yyyy") + "' AND H.SERVICEREQUESTDATE < '" + TDate.ToString("dd-MMM-yyyy") + "'");
                Query.Append(" SELECT A.COMPANY_ID as Company_ID,A.BRANCH_ID as Branch_ID,CG.Company_Name as CompanyName,CB.Branch_Name as BranchName,reg.RefMasterDetail_Name as 'Region', DATEPART(YEAR,A.SERVICEREQUESTDATE) AS YEAR, DATENAME(MONTH,A.SERVICEREQUESTDATE) AS MONTH,DATEPART(WEEK,A.SERVICEREQUESTDATE) AS WEEK,");
                Query.Append(" DATENAME(DW,A.SERVICEREQUESTDATE) AS DAY, REPLACE(CONVERT(VARCHAR(11), A.SERVICEREQUESTDATE, 106), ' ', '-') AS DATE, ");
                Query.Append(" A.SERVICEREQUEST_ID AS CALLREFERENCEID, A.SERVICEREQUESTNUMBER AS 'CALLREFERENCENUMBER', A.CaseType_ID , ");
                Query.Append(" CASE(ISIMPORTEXPORT) WHEN 1 THEN 'INTERNATIONAL' ELSE 'DOMESTIC'  END AS 'BUSINESSAREA' ,");
                Query.Append(" C.REFMASTERDETAIL_NAME AS BRAND,D.PRODUCTTYPE_NAME AS 'MODELCATEGORY',E.MODEL_NAME AS MODEL,A.SERIALNUMBER AS 'CHASSISTYPE',");
                Query.Append(" A.PRODUCT_UNIQUE_NUMBER AS 'UNIQUEIDENTIFIER', A.FLEXI1 AS 'VCNUMBER',CASE (A.ISUNDERBREAKDOWN) WHEN 0 THEN 'NO' ELSE 'YES' END AS 'ISUNDERBREAKDOWN', ");
                Query.Append(" case when A.IsDealer=1 then branchdata.Branch_Name else  B.PARTY_NAME end AS REQUESTOR,case when A.IsDealer=1 then branchdata.Branch_Mobile else F.PARTYCONTACTPERSON_MOBILE end AS 'CONTACTNUMBER', case when A.IsDealer=1 then branchdata.Branch_Email else F.PARTYCONTACTPERSON_EMAIL end  AS 'CONTATCTEMAIL',");
                Query.Append(" REPLACE(CONVERT(VARCHAR(11), A.CALLDATEANDTIME, 106), ' ', '-')+' '+ CONVERT(VARCHAR(5), A.CALLDATEANDTIME, 24) AS 'RECEIVEDDATETIME',");
                Query.Append(" G.REFMASTERDETAIL_NAME AS 'CALLMODE',H.REFMASTERDETAIL_NAME AS 'QUERYTYPE',I.ISSUESUBAREA_DESCRIPTION AS 'QUERYCATEGORY',");
                Query.Append(" J.REFMASTERDETAIL_NAME AS 'GROUPNUMBER',A.CALLDESCRIPTION AS 'QUERYPROBLEM',K.REFMASTERDETAIL_NAME AS 'CALLPRIORITY',");
                Query.Append(" L.REFMASTERDETAIL_NAME AS 'CALLCOMPLEXITY',A.RESPONSETIME,M.WFSTEPSTATUS_NM AS 'STATUS',A.CLOSINGDESCRIPTION AS 'SOLUTIONPROVIDED',");
                Query.Append(" REPLACE(CONVERT(VARCHAR(11), A.SERVICEREQUESTDATE, 106), ' ', '-')+' '+ CONVERT(VARCHAR(5), A.SERVICEREQUESTDATE, 24) AS 'ACKNOWLEDGEDDATETIME',");
                Query.Append(" REPLACE(CONVERT(VARCHAR(11), A.PROMISEDCOMPLETIONDATE, 106), ' ', '-')+' '+ CONVERT(VARCHAR(5), A.PROMISEDCOMPLETIONDATE, 24) AS 'PROMISEDCOMPLETIONDATETIME',");
                Query.Append(" REPLACE(CONVERT(VARCHAR(11), A.CALLCLOSUREDATEANDTIME, 106), ' ', '-')+' '+ CONVERT(VARCHAR(5), A.CALLCLOSUREDATEANDTIME, 24) AS 'COMPLETIONDATETIME',");

                Query.Append(" A.RESOLUTIONTIME,");
                Query.Append(" DATEDIFF(MINUTE,A.CallDateAndTime, ISNULL(A.CallClosureDateAndTime,GETDATE())) AS RESOLUTIONTIME24HOURMINUTES,");

                Query.Append(" CONVERT(VARCHAR(5), A.CALLCLOSUREDATEANDTIME-A.SERVICEREQUESTDATE, 24) AS 'TOTALSOLUTIONTIME',");
                Query.Append(" CONVERT(VARCHAR(5), REPLACE(N.SERVICELEVELAGREEMENT_HOURS,'.',':')) AS 'DEFINEDKPIHOURS',");
                Query.Append(" CASE WHEN (CONVERT(INT,ISNULL(REPLACE(A.RESOLUTIONTIME,':',''),0)) - CONVERT(INT,ISNULL(REPLACE(CONVERT(VARCHAR(5), REPLACE(N.SERVICELEVELAGREEMENT_HOURS,'.',':')),':',''),0))) < 0 THEN 'YES' ELSE 'NO' END AS 'KPIMET',");
                //
                //Added By Puneeth M for TML User Request TML-2015011301 on 22-Jan-2015 (Defect Name need to be displayed instead of Root Cause Analysis in Call Log Report)
                //Query.Append(" O.REFMASTERDETAIL_NAME AS 'REASONFORNOTMEETINGKPIHOURS', A.ROOTCAUSE,A.ACTIONREMARKS AS REMARKS,";
                Query.Append(" O.REFMASTERDETAIL_NAME AS 'REASONFORNOTMEETINGKPIHOURS', T.DEFECTNAME_DESCRIPTION AS ROOTCAUSE,A.ACTIONREMARKS AS REMARKS,");
                //
                Query.Append(" (SELECT COUNT(SERVICEREQUEST_ID) FROM HD_SERVICEREQUESTPARTSLIST WHERE SERVICEREQUEST_ID=A.SERVICEREQUEST_ID) AS 'NUMBEROFPARTS',");
                Query.Append(" R.QUERYESCALATEDTO,R.ESCALATEDDATETIME,R.DEPARTMENT,R.DESIGNATION,R.MANAGER,P.SERVICEREQUESTNUMBER AS 'REPEATEDQUERY',A.FLEXI2, S.COMPANY_EMPLOYEE_NAME AS FLEXI3 ");
                Query.Append(" FROM HD_SERVICEREQUEST A ");
                Query.Append(" LEFT OUTER  JOIN GNM_PARTY B ON A.PARTY_ID=B.PARTY_ID LEFT OUTER JOIN  GNM_Branch branchdata on branchdata.Branch_ID=A.Party_ID ");
                Query.Append(" LEFT OUTER JOIN GNM_REFMASTERDETAIL C ON A.BRAND_ID=C.REFMASTERDETAIL_ID");
                Query.Append(" LEFT OUTER JOIN GNM_PRODUCTTYPE D ON A.PRODUCTTYPE_ID=D.PRODUCTTYPE_ID ");
                Query.Append(" LEFT OUTER JOIN GNM_MODEL E ON A.MODEL_ID=E.MODEL_ID ");
                Query.Append("  LEFT OUTER JOIN GNM_PARTYCONTACTPERSONDETAILS F ON A.PARTYCONTACTPERSON_ID=F.PARTYCONTACTPERSON_ID ");
                Query.Append(" JOIN GNM_REFMASTERDETAIL G ON A.CALLMODE_ID=G.REFMASTERDETAIL_ID ");
                Query.Append(" JOIN GNM_REFMASTERDETAIL H ON A.ISSUEAREA_ID=H.REFMASTERDETAIL_ID");
                Query.Append(" LEFT OUTER JOIN HD_ISSUESUBAREA I ON A.ISSUESUBAREA_ID=I.ISSUESUBAREA_ID ");
                Query.Append(" LEFT OUTER JOIN GNM_REFMASTERDETAIL J ON A.FUNCTIONGROUP_ID=J.REFMASTERDETAIL_ID ");
                Query.Append(" LEFT OUTER JOIN GNM_REFMASTERDETAIL K ON A.CALLPRIORITY_ID=K.REFMASTERDETAIL_ID ");
                Query.Append(" LEFT OUTER JOIN GNM_REFMASTERDETAIL L ON A.CALLCOMPLEXITY_ID=L.REFMASTERDETAIL_ID");
                Query.Append(" JOIN GNM_WFSTEPSTATUS M ON A.CALLSTATUS_ID=M.WFSTEPSTATUS_ID ");
                Query.Append(" LEFT OUTER JOIN HD_SERVICELEVELAGREEMENT N ON A.CALLPRIORITY_ID=N.CALLPRIORITY_ID AND A.CALLCOMPLEXITY_ID=N.CALLCOMPLEXITY_ID AND N.PARTY_ID IS NULL ");
                Query.Append(" LEFT OUTER JOIN GNM_REFMASTERDETAIL O ON A.CLOSURETYPE_ID=O.REFMASTERDETAIL_ID ");
                Query.Append(" LEFT OUTER JOIN HD_SERVICEREQUEST P ON A.PARENTISSUE_ID=P.SERVICEREQUEST_ID");
                Query.Append(" JOIN #TEMPDATE R ON A.SERVICEREQUEST_ID=R.TRANSACTION_ID");
                Query.Append(" JOIN GNM_COMPANYEMPLOYEE S ON A.CALLOWNER_ID =S.COMPANY_EMPLOYEE_ID    ");
                //Added By Puneeth M for TML User Request TML-2015011301 on 22-Jan-2015 (Defect Name need to be displayed instead of Root Cause Analysis in Call Log Report)
                Query.Append(" LEFT OUTER JOIN HD_DEFECTNAME T ON A.DEFECTNAME_ID =T.DEFECTNAME_ID JOIN GNM_COMPANY CG on CG.COMPANY_ID=A.COMPANY_ID  JOIN GNM_BRANCH CB on CB.Branch_ID=A.Branch_ID LEFT OUTER JOIN GNM_REFMASTERDETAIL Reg ON Reg.RefMasterDetail_ID=CB.Region_ID ");
                //
                Query.Append(" WHERE A.COMPANY_ID in (" + Company_ID + ") AND A.BRANCH_ID IN (" + BranchIDs + ") AND A.SERVICEREQUESTDATE >= '" + FDate.ToString("dd-MMM-yyyy") + "' AND A.SERVICEREQUESTDATE < '" + TDate.ToString("dd-MMM-yyyy") + "'");
                Query.Append(" DROP TABLE #TEMPDATE");
                CallLogList = GetValueFromDB<List<SP_GENERATECALLLOG_Result>>(Query.ToString(), null, connString);



                //Modified by Venkateshwari for HelpDesk CR-5 Changes 19-Aug-2015...... Start.
                iQCallLogResultTemp = CallLogList.AsQueryable<SP_GENERATECALLLOG_Result>();



                IQueryable<SP_GENERATECALLLOG_Result> iQCallLogResult = iQCallLogResultTemp.AsQueryable();

                //Modified by Venkateshwari for HelpDesk CR-5 Changes 19-Aug-2015...... End.
                iQCallLogResult = iQCallLogResult.OrderByField<SP_GENERATECALLLOG_Result>(sidx, sord);
                CallLog = iQCallLogResult.ToList();
                Count = iQCallLogResult.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                jsonResult = new
                {
                    total = Total,
                    page = page,
                    records = Count,
                    //data = (iQCallLogResult).ToList().Paginate(page, rows)
                    data = (from a in iQCallLogResult
                            select new
                            {
                                YEAR = a.YEAR,
                                MONTH = a.MONTH,
                                WEEK = a.WEEK,
                                DAY = a.DAY,
                                DATE = a.DATE,
                                Region = a.Region,
                                CALLREFERENCEID = a.CALLREFERENCEID,
                                CALLREFERENCENUMBER = "<span  key='" + a.CALLREFERENCEID + "' style='color:blue;text-decoration:underline;cursor:pointer'>" + a.CALLREFERENCENUMBER + "</span>",
                                BUSINESSAREA = a.BUSINESSAREA,
                                BRAND = a.BRAND,
                                MODELCATEGORY = a.MODELCATEGORY,
                                MODEL = a.MODEL,
                                CHASSISTYPE = CHANGESERIALNUMTOVCNUM == true ? a.VCNUMBER : a.CHASSISTYPE,   // Modified by Manju P on 9-sep-2015 for TML call SR-TML-30-2015    -- Added condition                         
                                UNIQUEIDENTIFIER = a.UNIQUEIDENTIFIER,
                                VCNUMBER = CHANGESERIALNUMTOVCNUM == true ? a.CHASSISTYPE : a.VCNUMBER,    // Modified by Manju P on 9-sep-2015 for TML call SR-TML-30-2015       -- Added condition                                                     
                                ISUNDERBREAKDOWN = a.ISUNDERBREAKDOWN,
                                REQUESTOR = a.REQUESTOR,
                                CONTACTNUMBER = a.CONTACTNUMBER,
                                CONTATCTEMAIL = a.CONTATCTEMAIL,
                                RECEIVEDDATETIME = a.RECEIVEDDATETIME,
                                CALLMODE = a.CALLMODE,
                                QUERYTYPE = a.QUERYTYPE,
                                QUERYCATEGORY = a.QUERYCATEGORY,
                                GROUPNUMBER = a.GROUPNUMBER,
                                QUERYPROBLEM = a.QUERYPROBLEM,
                                CALLPRIORITY = a.CALLPRIORITY,
                                CALLCOMPLEXITY = a.CALLCOMPLEXITY,
                                ACKNOWLEDGEDDATETIME = a.ACKNOWLEDGEDDATETIME,
                                // RESPONSETIME = a.RESPONSETIME,
                                PROMISEDCOMPLETIONDATETIME = a.PROMISEDCOMPLETIONDATETIME,
                                STATUS = a.STATUS,
                                SOLUTIONPROVIDED = a.SOLUTIONPROVIDED,
                                COMPLETIONDATETIME = a.COMPLETIONDATETIME,
                                RESOLUTIONTIME = a.RESOLUTIONTIME,
                                TOTALSOLUTIONTIME = a.TOTALSOLUTIONTIME,
                                DEFINEDKPIHOURS = a.DEFINEDKPIHOURS,
                                KPIMET = a.KPIMET,
                                REASONFORNOTMEETINGKPIHOURS = a.REASONFORNOTMEETINGKPIHOURS,
                                ROOTCAUSE = a.ROOTCAUSE,
                                NUMBEROFPARTS = a.NUMBEROFPARTS,
                                QUERYESCALATEDTO = a.QUERYESCALATEDTO,
                                ESCALATEDDATETIME = a.ESCALATEDDATETIME,
                                REMARKS = Common.DecryptString(a.REMARKS),
                                DEPARTMENT = a.DEPARTMENT,
                                DESIGNATION = a.DESIGNATION,
                                MANAGER = a.MANAGER,
                                REPEATEDQUERY = a.REPEATEDQUERY,
                                FLEXI2 = a.FLEXI2,
                                FLEXI3 = a.FLEXI3,
                                RESOLUTIONTIME24HOURS = a.RESOLUTIONTIME == "0:00" ? "0:00" : CommonFunctionalities.ConvertToHours(a.RESOLUTIONTIME24HOURMINUTES),
                                CompanyName = a.CompanyName,
                                BranchName = a.BranchName,
                                Company_ID = a.Company_ID,
                                Branch_ID = a.Branch_ID,
                                Type = a.CaseType_ID == 1 ? "Support" : a.CaseType_ID == 2 ? "Parts" : a.CaseType_ID == 3 ? "Service" : "Sales"


                            }).ToList().Paginate(page, rows)
                };
                //gbl.InsertGPSDetails(Convert.ToInt32(Obj.Company_ID.ToString()), Convert.ToInt32(Obj.Branch), Obj.User_ID, Common.GetObjectID("HelpDesk_Rpt_CallLog"), 0, 0, 0, "Generated-Call Log", false, Convert.ToInt32(Obj.MenuID), Convert.ToDateTime(Obj.LoggedINDateTime));
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonResult);
        }
        #endregion
        #region Export vinay n 20/11/24
        public static async Task<object> Export(ExportHelpDesk_Rpt_CallLogList Obj, string connString, int LogException)
        {
            try
            {

                int userID = Obj.User_ID;
                int branchID = Convert.ToInt32(Obj.Branch);

                string BranchName = CallLogBranchName.ToString();
                string FromDate = CallLogFromDate.ToString();
                string ToDate = CallLogToDate.ToString();
                List<SP_GENERATECALLLOG_Result> CallLogList = new List<SP_GENERATECALLLOG_Result>();
                CallLogList = ((List<SP_GENERATECALLLOG_Result>)CallLog);
                DataTable dt = new DataTable();
                int? Company_ID = Convert.ToInt32(Obj.Company_ID);
                string query = @"
                SELECT Param_value 
                FROM GNM_CompParam 
                WHERE UPPER(Param_Name) = @ParamName AND Company_ID = @CompanyID";
                List<SqlParameter> parameters = new List<SqlParameter>
                {
                    new SqlParameter("@ParamName", "CHANGESERIALNUMTOVCNUM"),
                    new SqlParameter("@CompanyID", Company_ID)
                };
                string paramValue = GetValueFromDB<string>(query, parameters, connString);
                bool CHANGESERIALNUMTOVCNUM = !string.IsNullOrEmpty(paramValue) && paramValue.ToUpper() == "TRUE";

                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Type").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Year").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "month").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Week").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Day").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Date").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Region").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Company").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Branch").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CallReferenceNumber").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "BusinessArea").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Brand").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ModelCategory").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Model").ToString());
                if (CHANGESERIALNUMTOVCNUM)
                {
                    dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "VCNumber").ToString());
                }
                else
                {
                    dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "SerialNumber").ToString());
                }
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "UniqueIdentifier").ToString());
                if (CHANGESERIALNUMTOVCNUM)
                {
                    dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "SerialNumber").ToString());
                }
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "IsUnderBreakDown").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Requestor").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ContatctNumber").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ContatctEmail").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ReceivedDateandTime").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CallMode").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "QueryType").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "QueryCategory").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "GroupNumber").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "QueryProblem").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CallPriority").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CallComplexity").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "AcknowledgedDateandTime").ToString());
                //dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ResponseTime").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "PromisedCompletionDateandTime").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Status").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "SolutionProvided").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CompletionDateandTime").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ResolutionTime").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ResolutionTime24Hours").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "TotalSolutionTime").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "DefinedKPIHours").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "KPIMet").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ReasonForNotMeetingKPI").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "RootCause").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "NumberOfParts").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "QueryEscalatedTo").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "EscalatedDateandTime").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Remarks").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Department").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Designation").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Manager").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "RepeatedQuery").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Flexi1").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CallOwner").ToString());

                DataTable dtAlignment = new DataTable();
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Type").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Year").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "month").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Week").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Day").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Date").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Region").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Company").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Branch").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CallReferenceNumber").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "BusinessArea").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Brand").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ModelCategory").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Model").ToString());
                if (CHANGESERIALNUMTOVCNUM)
                {
                    dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "VCNumber").ToString());
                }
                else
                {
                    dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "SerialNumber").ToString());
                }
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "UniqueIdentifier").ToString());
                if (CHANGESERIALNUMTOVCNUM)
                {
                    dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "SerialNumber").ToString());
                }
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "IsUnderBreakDown").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Requestor").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ContatctNumber").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ContatctEmail").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ReceivedDateandTime").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CallMode").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "QueryType").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "QueryCategory").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "GroupNumber").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "QueryProblem").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CallPriority").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CallComplexity").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "AcknowledgedDateandTime").ToString());
                //dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ResponseTime").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "PromisedCompletionDateandTime").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Status").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "SolutionProvided").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CompletionDateandTime").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ResolutionTime").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ResolutionTime24Hours").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "TotalSolutionTime").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "DefinedKPIHours").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "KPIMet").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ReasonForNotMeetingKPI").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "RootCause").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "NumberOfParts").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "QueryEscalatedTo").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "EscalatedDateandTime").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Remarks").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Department").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Designation").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Manager").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "RepeatedQuery").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Flexi1").ToString());
                dtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CallOwner").ToString());
                if (CHANGESERIALNUMTOVCNUM)
                {
                    dtAlignment.Rows.Add(0, 2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                }
                else
                {
                    dtAlignment.Rows.Add(0, 2, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                }

                DataTable DateRange = new DataTable();
                //DateRange.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Branch").ToString());
                DateRange.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "fromdate").ToString());
                DateRange.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "todate").ToString());
                DateRange.Rows.Add(FromDate, ToDate);

                if (CallLogList.Count > 0)
                {
                    for (int i = 0; i < CallLogList.Count(); i++)
                    {

                        if (CHANGESERIALNUMTOVCNUM)
                        {
                            dt.Rows.Add(CallLogList[i].CaseType_ID == 1 ? "Support" : CallLogList[i].CaseType_ID == 2 ? "Parts" : CallLogList[i].CaseType_ID == 3 ? "Service" : "Sales", CallLogList[i].YEAR, CallLogList[i].MONTH, CallLogList[i].WEEK, CallLogList[i].DAY, CallLogList[i].DATE, CallLogList[i].Region, CallLogList[i].CompanyName, CallLogList[i].BranchName, CallLogList[i].CALLREFERENCENUMBER, CallLogList[i].BUSINESSAREA, CallLogList[i].BRAND, CallLogList[i].MODELCATEGORY, CallLogList[i].MODEL, CallLogList[i].CHASSISTYPE, CallLogList[i].UNIQUEIDENTIFIER, CallLogList[i].VCNUMBER, CallLogList[i].ISUNDERBREAKDOWN, CallLogList[i].REQUESTOR, CallLogList[i].CONTACTNUMBER, CallLogList[i].CONTATCTEMAIL, Obj.exprtType == 1 ? "=concatenate(\"" + CallLogList[i].RECEIVEDDATETIME + "\")" : CallLogList[i].RECEIVEDDATETIME, CallLogList[i].CALLMODE, CallLogList[i].QUERYTYPE, CallLogList[i].QUERYCATEGORY, CallLogList[i].GROUPNUMBER, CallLogList[i].QUERYPROBLEM, CallLogList[i].CALLPRIORITY, CallLogList[i].CALLCOMPLEXITY, Obj.exprtType == 1 ? "=concatenate(\"" + CallLogList[i].ACKNOWLEDGEDDATETIME + "\")" : CallLogList[i].ACKNOWLEDGEDDATETIME, CallLogList[i].PROMISEDCOMPLETIONDATETIME, CallLogList[i].STATUS, CallLogList[i].SOLUTIONPROVIDED, CallLogList[i].COMPLETIONDATETIME, CallLogList[i].RESOLUTIONTIME, CommonFunctionalities.ConvertToHours(CallLogList[i].RESOLUTIONTIME24HOURMINUTES), CallLogList[i].TOTALSOLUTIONTIME, CallLogList[i].DEFINEDKPIHOURS, CallLogList[i].KPIMET, CallLogList[i].REASONFORNOTMEETINGKPIHOURS, CallLogList[i].ROOTCAUSE, CallLogList[i].NUMBEROFPARTS, CallLogList[i].QUERYESCALATEDTO, Obj.exprtType == 1 ? "=concatenate(\"" + CallLogList[i].ESCALATEDDATETIME + "\")" : CallLogList[i].ESCALATEDDATETIME, CallLogList[i].REMARKS, CallLogList[i].DEPARTMENT, CallLogList[i].DESIGNATION, CallLogList[i].MANAGER, CallLogList[i].REPEATEDQUERY, CallLogList[i].FLEXI2, CallLogList[i].FLEXI3);
                        }
                        else
                        {
                            dt.Rows.Add(CallLogList[i].CaseType_ID == 1 ? "Support" : CallLogList[i].CaseType_ID == 2 ? "Parts" : CallLogList[i].CaseType_ID == 3 ? "Service" : "Sales", CallLogList[i].YEAR, CallLogList[i].MONTH, CallLogList[i].WEEK, CallLogList[i].DAY, CallLogList[i].DATE, CallLogList[i].Region, CallLogList[i].CompanyName, CallLogList[i].BranchName, CallLogList[i].CALLREFERENCENUMBER, CallLogList[i].BUSINESSAREA, CallLogList[i].BRAND, CallLogList[i].MODELCATEGORY, CallLogList[i].MODEL, CallLogList[i].CHASSISTYPE, CallLogList[i].UNIQUEIDENTIFIER, CallLogList[i].ISUNDERBREAKDOWN, CallLogList[i].REQUESTOR, CallLogList[i].CONTACTNUMBER, CallLogList[i].CONTATCTEMAIL, Obj.exprtType == 1 ? "=concatenate(\"" + CallLogList[i].RECEIVEDDATETIME + "\")" : CallLogList[i].RECEIVEDDATETIME, CallLogList[i].CALLMODE, CallLogList[i].QUERYTYPE, CallLogList[i].QUERYCATEGORY, CallLogList[i].GROUPNUMBER, CallLogList[i].QUERYPROBLEM, CallLogList[i].CALLPRIORITY, CallLogList[i].CALLCOMPLEXITY, Obj.exprtType == 1 ? "=concatenate(\"" + CallLogList[i].ACKNOWLEDGEDDATETIME + "\")" : CallLogList[i].ACKNOWLEDGEDDATETIME, CallLogList[i].PROMISEDCOMPLETIONDATETIME, CallLogList[i].STATUS, CallLogList[i].SOLUTIONPROVIDED, CallLogList[i].COMPLETIONDATETIME, CallLogList[i].RESOLUTIONTIME, CommonFunctionalities.ConvertToHours(CallLogList[i].RESOLUTIONTIME24HOURMINUTES), CallLogList[i].TOTALSOLUTIONTIME, CallLogList[i].DEFINEDKPIHOURS, CallLogList[i].KPIMET, CallLogList[i].REASONFORNOTMEETINGKPIHOURS, CallLogList[i].ROOTCAUSE, CallLogList[i].NUMBEROFPARTS, CallLogList[i].QUERYESCALATEDTO, Obj.exprtType == 1 ? "=concatenate(\"" + CallLogList[i].ESCALATEDDATETIME + "\")" : CallLogList[i].ESCALATEDDATETIME, CallLogList[i].REMARKS, CallLogList[i].DEPARTMENT, CallLogList[i].DESIGNATION, CallLogList[i].MANAGER, CallLogList[i].REPEATEDQUERY, CallLogList[i].FLEXI2, CallLogList[i].FLEXI3);
                        }
                    }
                    DataSet ds = new DataSet();

                    ExportReportExportCR5List exportReport = new ExportReportExportCR5List
                    {
                        FileName = "CallLog",
                        Branch = Convert.ToString(Obj.Branch),
                        Company_ID = Obj.Company_ID,
                        UserCulture = Obj.UserCulture,
                        dt = dt, // You can populate this with actual data as needed
                        exprtType = Obj.exprtType, // Set an appropriate type for export (e.g., 1 for PDF, 2 for Excel, etc.)
                        Header = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CallLog").ToString(),
                        Options = DateRange, // Populate this with your report options
                        selection = ds, // Add selection-related data here
                        Alignment = dtAlignment // Define alignment details for table columns
                    };
                    var result = await ReportExportCR5.Export(exportReport, connString, LogException);
                    return result.Value;
                    //gbl.InsertGPSDetails(Convert.ToInt32(Obj.Company_ID.ToString()), branchID, User.User_ID, Common.GetObjectID("HelpDesk_Rpt_CallLog"), 0, 0, 0, "Call Log -Export ", false, Convert.ToInt32(Obj.MenuID), Convert.ToDateTime(Obj.LoggedINDateTime));
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return null;
        }
        #endregion
    }
    #region HelpDesk_Rpt_CallLogListsAndObjs
    public class ExportHelpDesk_Rpt_CallLogList
    {

        public int User_ID { get; set; }
        public string Branch { get; set; }
        public int Company_ID { get; set; }
        public string UserCulture { get; set; }
        public int exprtType { get; set; }
    }
    public class SelectHelpDesk_Rpt_CallLogList
    {
        public int MenuID { get; set; }
        public string FromDate { get; set; }
        public string ToDate { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int Company_ID { get; set; }
        public string Branch { get; set; }
        public string CompanyData { get; set; }
        public int User_ID { get; set; }
        public DateTime LoggedINDateTime { get; set; }
    }
    public class InitialSetUpList
    {
        public int ObjectID { get; set; }
        public int User_ID { get; set; }
        public int Company_ID { get; set; }
    }

    #endregion
    #region HelpDesk_Rpt_CallLogMasterClasses
    public class Top_Branch
    {
        public List<Top_Branchs> Branchs
        {
            get;
            set;
        }
    }
    public class Top_Branchs
    {

        public int ID
        {
            get;
            set;
        }
        public string Name
        {
            get;
            set;
        }
    }
    public class SP_GENERATECALLLOG_Result
    {
        public Nullable<int> YEAR { get; set; }
        public string MONTH { get; set; }
        public Nullable<int> WEEK { get; set; }
        public string DAY { get; set; }
        public string DATE { get; set; }
        public int CALLREFERENCEID { get; set; }
        public string CALLREFERENCENUMBER { get; set; }
        public string BUSINESSAREA { get; set; }
        public string BRAND { get; set; }
        public string MODELCATEGORY { get; set; }
        public string MODEL { get; set; }
        public string CHASSISTYPE { get; set; }
        public string UNIQUEIDENTIFIER { get; set; }
        public string VCNUMBER { get; set; }
        public string ISUNDERBREAKDOWN { get; set; }
        public string REQUESTOR { get; set; }
        public string CONTACTNUMBER { get; set; }
        public string CONTATCTEMAIL { get; set; }
        public string RECEIVEDDATETIME { get; set; }
        public string CALLMODE { get; set; }
        public string QUERYTYPE { get; set; }
        public string QUERYCATEGORY { get; set; }
        public string GROUPNUMBER { get; set; }
        public string QUERYPROBLEM { get; set; }
        public string CALLPRIORITY { get; set; }
        public string CALLCOMPLEXITY { get; set; }
        public string ACKNOWLEDGEDDATETIME { get; set; }
        public string RESPONSETIME { get; set; }
        public string PROMISEDCOMPLETIONDATETIME { get; set; }
        public string STATUS { get; set; }
        public string SOLUTIONPROVIDED { get; set; }
        public string COMPLETIONDATETIME { get; set; }
        public string RESOLUTIONTIME { get; set; }
        public string TOTALSOLUTIONTIME { get; set; }
        public string DEFINEDKPIHOURS { get; set; }
        public string KPIMET { get; set; }
        public string REASONFORNOTMEETINGKPIHOURS { get; set; }
        public string ROOTCAUSE { get; set; }
        public Nullable<int> NUMBEROFPARTS { get; set; }
        public string QUERYESCALATEDTO { get; set; }
        public string ESCALATEDDATETIME { get; set; }
        public string REMARKS { get; set; }
        public string DEPARTMENT { get; set; }
        public string DESIGNATION { get; set; }
        public string MANAGER { get; set; }
        public string REPEATEDQUERY { get; set; }
        public string FLEXI2 { get; set; }
        public string FLEXI3 { get; set; }
        public int RESOLUTIONTIME24HOURMINUTES { get; set; }
        public string CompanyName { get; set; }
        public string BranchName { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public string Region { get; set; }
        public int CaseType_ID { get; set; }

    }
    #endregion
}
