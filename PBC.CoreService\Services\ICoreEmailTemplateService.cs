﻿using Microsoft.AspNetCore.Mvc;
using PBC.Core_SharedAPIClass.Models;

namespace PBC.Core_SharedAPIClass.Services
{
    public interface ICoreEmailTemplateService
    {


        /// <summary>
        /// Check if old password matches the stored password
        /// </summary>
        /// <param name="obj">CheckOldPasswordList object</param>
        /// <param name="connString">Database connection string</param>
        /// <param name="logException">Log exception flag</param>
        /// <returns>JsonResult with count (1 if password matches, 0 if not)</returns>
        Task<IActionResult> CommonMethodForEmailandSMS(CommonMethodForEmailandSMSList obj, string connString, int logException);

    }
}
