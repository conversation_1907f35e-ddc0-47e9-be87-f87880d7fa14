﻿/*body {
    font-size: .85em;
    font-family: "Segoe UI", Verdana, Helvetica, Sans-Serif;
    color: #232323;
    background-color: #fff;
}*/
 body {
            font-size: 12px;
            font-family: Cambria;
        }

header, footer, nav, section {
    display: block;
}


/* Styles for basic forms
-----------------------------------------------------------*/
fieldset {
    border: 1px solid #ddd;
    padding: 0 1.4em 1.4em 1.4em;
    margin: 0 0 1.5em 0;
}

legend {
    font-size: 1.2em;
    font-weight: bold;
}

textarea {
    min-height: 75px;
}

.editor-label {
    margin: 1em 0 0 0;
}

.editor-field {
    margin: 0.5em 0 0 0;
}


/* Styles for validation helpers
-----------------------------------------------------------*/
.field-validation-error {
    color: #f00;
}

.field-validation-valid {
    display: none;
}

.input-validation-error {
    border: 1px solid #f00;
    background-color: #fee;
}

.validation-summary-errors {
    font-weight: bold;
    color: #f00;
}

.validation-summary-valid {
    display: none;
}




BODY { font-family: Cambria, Georgia, "Times New Roman"; margin: 0; }
DIV.header DIV.title {
font: bold 1em "Arial Narrow", "Franklin Gothic Medium", Arial;
}
DIV.header {     width: 100%;
                 height: 68px;
	            background: -webkit-gradient(linear, left top, left bottom, from(#797676), to(#444343));
	            background: -moz-linear-gradient(top,  #797676,  #444343);
                filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#797676', endColorstr='#444343');
}
DIV.header DIV.title { font-size: 1.5em; padding: 0.1em; 
                       background:url('../Images/bg.jpg');
}

.subtitle {
    /*background:url('../Images/sub1.png') 50% 50% repeat-x;*/
    /*background-color:#125182;   
    /*background-repeat:repeat-x;*/
    background: -webkit-gradient(linear, left top, left bottom, from(#BC463D), to(#852720));
	            background: -moz-linear-gradient(top,  #BC463D,  #852720);
                filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#BC463D', endColorstr='#852720');
}
.tdSideMenubar {
    background-color: white;    vertical-align: top;   border-right:1px solid gray;
     /*position:fixed;*/
     width:20%;
}
DIV.categories A {
    font: bold 1.1em "Arial Narrow","Franklin Gothic Medium",Arial;
    display: block;
    text-decoration: none;
    padding: 0;
    color: white;   
    width:100%;   
    margin: 0; padding-bottom: 0;padding-top:0;padding-left:0;padding-right:0;margin-top: 0em;margin-bottom:0em;
    margin-left:0em;    
}
DIV.categories A.selected { background-color: #666; color: White;  }
DIV.categories A:hover { background-color: #675352; }
DIV.categories A.selected:hover { background-color: #666; }

.tblExternal {
    border-bottom:1px solid gray;
    border-left:1px solid gray;
    border-right :1px solid gray;
    border-top :1px solid gray;
    width:100%;    
}
table.hovertable {
	font-family: verdana,arial,sans-serif;
	font-size:11px;	
	border-width: 1px;
	border-color: #999999;
	border-collapse: collapse;
    width:100%;
}
table.hovertable th {
	background-color:#191970;
	border-width: 1px;
	padding: 8px;
	border-style: solid;
	border-color: #a9c6c9;
    color:white;
}
table.hovertable tr {
	background-color:#FCF4D4;
}
table.hovertable td {
	border-width: 1px;
	padding: 8px;
	border-style: solid;
	border-color: #a9c6c9;
}

.header {
    font-family:Cambria;
    font-size:large;
    font-weight:bold;
}
.headerima {
    width:100%;
    height:100px;
   background:url('../Images/bg.png');
   z-index: 3;
}
.questlogo {
    padding-bottom:5px;
    float:left;
/*background:url('../Images/Quest Logo.png') no-repeat;*/
z-index:-100;
 
}
DIV.footer {
    background-color: #c0504e; color: White; 
}
DIV.footer { font-size: 2em; padding: .2em; }

 .img {
            height: 30px;
            width: 75px;
  }
 div.imgtop { margin:2px; height:30; width:30; float:right; } 
    div.imgtop  imgtop  { display:inline; margin:3px; border:1px solid #ffffff; }
    div.imgtop  a:hover imgtop    {  border:1px solid #0000ff; }

Span.SpanSelected {
   /*border:1px solid black;*/   
}
Span.SpanSelected A {
    /*font: bold 1.1em "Arial Narrow","Franklin Gothic Medium",Arial;
    text-decoration: none;
    padding: .2em;
    padding-right:.4em;
    color: white;    
    display:inline-block;
    width:170px;
    text-align:center;    
    background-image:url('../Images/BlueButton.jpg');*/

    /*height:20px;
    background-repeat:no-repeat;    
    behavior:url('ie-css3.htc');
    -moz-border-radius: 15px 15px 0 0;     
    -webkit-border-radius: 15px 15px 0 0; 
    border-top-left-radius radius:15px 15px;
    -moz-border-radius-topleft*/
    

    
 /*basic styles*/
	/*width: 170px;  height: 28px;*/  
    /*background: #deedf7 url('../Images/ui-bg_highlight-soft_100_deedf7_1x100.png') 50% 50% repeat-x; 
    color: #222222;*/
     /*background:url('../Images/ui-bg_gloss-wave_55_5c9ccc_500x100.png') 50% 50% repeat;
    color: whitesmoke;
	text-align: center;  font-size: 18px;  line-height: 25px;

  display:inline-block;*/
	/*gradient styles*/
	/*background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #2890e1), color-stop(.5, #2890e1), color-stop(.51, #2890e1), to(##2890e1));
	background: -moz-linear-gradient(top, #2890e1, #1269ab 50%, ##2890e1 51%, ##2890e1);*/  
 
	/*border styles*/
	/*-moz-border-radius: 15px;
	-webkit-border-radius: 15px;*/
    /*border-top-left-radius:10px;
    border-top-right-radius:10px;*/
	/*border-radius: 30px;*/
 
	/*-moz-box-shadow:inset 0 0 15px #000000;
	-webkit-box-shadow:inset 0 0 15px #000000;
	box-shadow:inset 0 0 10px #000000;*/
    /*float:left;*/
    width:100px;
	display: inline-block;
	zoom: 1; /* zoom and *display = ie7 hack for display:inline-block */
	vertical-align: baseline;
	margin: 0 0.5px;
	outline: none;
	cursor: pointer;
	text-align: center;
	text-decoration: none;
	font: 12px/100% Arial, Helvetica, sans-serif;
	font-weight: bold;
	padding: .5em 1em .55em;
	text-shadow: 0 1px 1px rgba(0,0,0,.3);
        -webkit-border-top-left-radius:.5em;
	-webkit-border-top-right-radius:.5em;
	-moz-border-top-left-radius:.5em;
        -moz-border-top-right-radius:.5em;
	/*border-radius: .5em;*/
	-webkit-box-shadow: 0 1px 2px rgba(0,0,0,.2);
	-moz-box-shadow: 0 1px 2px rgba(0,0,0,.2);
	box-shadow: 0 1px 2px rgba(0,0,0,.2);
    border:#7d2424;
    background:#9E4037;
    color:white;
    text-transform:uppercase;
     border: 1px solid #5c0404; 
            /*color: white;
        background: -webkit-gradient(linear, left top, left bottom, from(#0078a5), to(#00adee));
        background: -moz-linear-gradient(top, #0078a5, #00adee);
        filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0078a5', endColorstr='#00adee');*/
}



.SpanIDSelected 
{
        /*color: white;
        background: -webkit-gradient(linear, left top, left bottom, from(#0078a5), to(#00adee));
        background: -moz-linear-gradient(top, #0078a5, #00adee);
        filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0078a5', endColorstr='#00adee');*/
        background:#868585;
}





/*Span.SpanSelected A.selected { background-color: #666; color: White;  width:175px; }*/

Span.SpanSelected A:hover {
    background: #b0abab;
	/*background: -webkit-gradient(linear, left top, left bottom, from(#0095cc), to(#00678e));
	background: -moz-linear-gradient(top,  #0095cc,  #00678e);
	filter:  progid:DXImageTransform.Microsoft.gradient(startColorstr='#0095cc', endColorstr='#00678e');*/}

    Span.SpanSelected A:active 
    {
        /*color: white;
        background: -webkit-gradient(linear, left top, left bottom, from(#0078a5), to(#00adee));
        background: -moz-linear-gradient(top, #0078a5, #00adee);
        filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0078a5', endColorstr='#00adee');*/
        background:#868585;
    }

/*Span.SpanSelected A.selected:hover { -moz-box-shadow:inset 0 0 50px #000000;
	-webkit-box-shadow:inset 0 0 50px #000000;
	box-shadow:inset 0 0 50px #000000;
 }*/
.normal {
    font: bold 1.1em "Arial Narrow","Franklin Gothic Medium",Arial;
    text-decoration: none;
    padding: .2em;
    color: white;
    background-color:#c0504e;
}

ul
{
	 margin: 0; padding-bottom: 0;padding-top:0;padding-left:0;padding-right:0;margin-top: 0em;margin-bottom:0em;
     margin-left:0.5em;list-style-type:none;
}
li {    
    list-style-type:none;
    margin-top: 0.1em;
    margin-bottom:0.1em;
    margin-left:0.5em;
    display: block; 
    /*background:#d5e5fa;*/
}

/*.content {
    overflow: non
}*/


#content A{
    text-decoration: none;
    padding: .2em;
    padding-right:.4em;
    color: #222222;    
    display:inline-block;
    width:170px;
}







/*.accordion .outer {border-width:0 1px 1px; background: #fff}*/

.accordion .inner {margin-bottom:0; padding:.5em 20px 1em; position:relative; overflow:hidden}

.accordion .inner .inner {padding-bottom:0}

.accordion .h {padding-top:.3em} 

.accordion p {margin:.5em 1px 1em}

.accordion {margin:0; padding:0 0px; border-width:0 1px 1px;}

.accordion li {list-style-type:square}

.accordion li.last-child {margin-left:19px; list-style-type:disc}

h1 {font-family:Verdana; font-size:11px;font-weight:bold;}

h1, h2, h3 {margin-bottom:1em}

h2, h3, h4 a, h5 a {padding:3px 10px}

h2, h3, h4, h5 {font-size:1em}


/* --- Links ---AC0C0C */
.accordion a {
    padding-left:2em; 
    /*background-image:url('../Images/RedButton.jpg');
    background-repeat:no-repeat*/ 
    /*background-color:midnightblue;*/
    text-decoration:none;
    height:18px;
    font-size:12px;
    border: 1px solid #aaaaaa;
    color: white;
        /*-webkit-border-top-left-radius:.8em;
	-webkit-border-top-right-radius:.8em;
	-moz-border-top-left-radius:.8em;
        -moz-border-top-right-radius:.8em;*/
        -webkit-border-radius:.4em;
        -moz-border-radius:.4em;
        /*background:#898787;*/
         background:#7d7c7c;
}

.accordianChild 
{
    padding-left:1em;
    background:#d5e5fa;
}

.accordion a:hover{border:1px solid #aaaaaa; text-decoration:none; outline:0 none; background-color: #bdb7b7; color:#000000;cursor:hand;font-weight: bolder;font-weight:bold}

.accordion a:active {
    border: 1px solid #aaaaaa;
    text-decoration: none;
    background: #868585;
}


* {margin:0; padding:0}

a.trigger {padding-left:20px; background-image: url('../Images/Expand.png'); background-repeat: no-repeat; background-position: 1px 50%;  font-weight:700}

a.trigger.open {background-image: url('../Images/Collapse.png')}
.last-child a.trigger{background-image:none; font-weight:normal;background:#827f7f;}
.last-child a:hover {background:#bdb7b7;color:#000000}


.loginBox {
    /*background-image:url('../Images/login-box-backg.png');
    background-size:50px;*/
    font-family:Cambria;
    font-size:16px;
    color:white;
    text-decoration:none;
    border:none;
}
.loginHeader {
     font-family:Arial;
    font-size:25px;
    color:white;
    text-decoration:none;
    border:none;
}
.SaveBtn {
    background-color:#9E4037;
    color:white;
    width:70px;
    height:25px;
   display:inline-block;
    border-top-left-radius:5px;
    border-top-right-radius:5px;
    border-bottom-left-radius:5px;
    border-bottom-right-radius:5px;
    font-weight:bold;
text-transform:lowercase;

}
.EditBtn {
    background-color:#9E4037;
    color:white;
    width:70px;
    height:25px;
    display:inline-block;
    border-top-left-radius:5px;
    border-top-right-radius:5px;
    border-bottom-left-radius:5px;
    border-bottom-right-radius:5px;
    font-weight:bold;
text-transform:lowercase;

}
.DeleteBtn {
    background-color:#9E4037;
    color:white;
    width:70px;
    height:25px;
    display:inline-block;
    border-top-left-radius:5px;
    border-top-right-radius:5px;
    border-bottom-left-radius:5px;
    border-bottom-right-radius:5px;
    font-weight:bold;
text-transform:lowercase;

}

.RequiredFieldValidator
{
    color: red;
}