﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.HelpDesk_Tr_CaseDistributionChartServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class HelpDesk_Tr_CaseDistributionChartController : ApiController
    {


        #region ::: LoadBranchDD  Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To Load Branch
        /// </summary>
        /// 
        [Route("api/HelpDesk_Tr_CaseDistributionChart/LoadBranchDD")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadBranchDD([FromBody] HelpDesk_Tr_CaseDistributionChartLoadBranchDDList HelpDesk_Tr_CaseDistributionChartLoadBranchDDobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_CaseDistributionChartServices.LoadBranchDD(HelpDesk_Tr_CaseDistributionChartLoadBranchDDobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: load Reference Master data Uday Kumar J B 18-11-2024 :::
        /// <summary>
        /// To load master drop downs
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Tr_CaseDistributionChart/loadMasters")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult loadMasters([FromBody] HelpDesk_Tr_CaseDistributionChartloadMastersDDList HelpDesk_Tr_CaseDistributionChartloadMastersDDobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_CaseDistributionChartServices.loadMasters(HelpDesk_Tr_CaseDistributionChartloadMastersDDobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion




        #region ::: load Brand  Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To load Brand
        /// </summary>
        /// <returns>...</returns>
        ///  
        [Route("api/HelpDesk_Tr_CaseDistributionChart/SelectBrand")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectBrand([FromBody] HelpDesk_Tr_CaseDistributionChartSelectBrandList HelpDesk_Tr_CaseDistributionChartSelectBrandobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_CaseDistributionChartServices.SelectBrand(HelpDesk_Tr_CaseDistributionChartSelectBrandobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: Load Brand Uday kumar J B 18-11-2024:::
        /// <summary>
        /// To Load Brand
        /// </summary>
        /// <returns>...</returns>
        ///  
        [Route("api/HelpDesk_Tr_CaseDistributionChart/LoadBrand")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadBrand([FromBody] HelpDesk_Tr_CaseDistributionChartLoadBrandList HelpDesk_Tr_CaseDistributionChartLoadBrandobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_CaseDistributionChartServices.LoadBrand(HelpDesk_Tr_CaseDistributionChartLoadBrandobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: load Product Type Uday kumar J B 18-11-2024 :::
        /// <summary>
        /// To load Product Type
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Tr_CaseDistributionChart/ProductType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ProductType([FromBody] HelpDesk_Tr_CaseDistributionChartProductTypeList HelpDesk_Tr_CaseDistributionChartProductTypeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_CaseDistributionChartServices.ProductType(HelpDesk_Tr_CaseDistributionChartProductTypeobj, connString, LogException);
            }
            catch (Exception ex)
            {

                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: Get Party Dtails  Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To get Customer details
        /// </summary>
        /// <returns>...</returns>
        ///
        [Route("api/HelpDesk_Tr_CaseDistributionChart/GetPartyDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetPartyDetails([FromBody] HelpDesk_Tr_CaseDistributionChartGetPartyDetailsList HelpDesk_Tr_CaseDistributionChartGetPartyDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_CaseDistributionChartServices.GetPartyDetails(HelpDesk_Tr_CaseDistributionChartGetPartyDetailsobj, connString, LogException);
            }
            catch (Exception ex)
            {

                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: Get Model Dtails  Uday kumar J B 18-1-2024:::
        /// <summary>
        /// To get Customer details
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Tr_CaseDistributionChart/GetModel")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetModel([FromBody] HelpDesk_Tr_CaseDistributionChartGetModelList HelpDesk_Tr_CaseDistributionChartGetModelsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_CaseDistributionChartServices.GetModel(HelpDesk_Tr_CaseDistributionChartGetModelsobj, connString, LogException);
            }
            catch (Exception ex)
            {

                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: Select Service Request Report Day wise Uday Kumar J B 21-11-2024:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Tr_CaseDistributionChart/SelectDaywiseReport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectDaywiseReport([FromBody] HelpDesk_Tr_CaseDistributionChartSelectYearReportList HelpDesk_Tr_CaseDistributionChartSelectYearReportobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_CaseDistributionChartServices.SelectDaywiseReport(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj, connString, LogException);
            }
            catch (Exception ex)
            {

                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Select Service Request Count Year Uday Kumar J B 20-11-2024:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Tr_CaseDistributionChart/SelectYearReport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectYearReport([FromBody] HelpDesk_Tr_CaseDistributionChartSelectYearReportList HelpDesk_Tr_CaseDistributionChartSelectYearReportobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Tr_CaseDistributionChartServices.SelectYearReport(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion



        #region ::: Select Service Request Count Month wise Uday Kumar J B 20-11-2024:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Tr_CaseDistributionChart/SelectMonthReport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectMonthReport([FromBody] HelpDesk_Tr_CaseDistributionChartSelectYearReportList HelpDesk_Tr_CaseDistributionChartSelectYearReportobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Tr_CaseDistributionChartServices.SelectMonthReport(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion




        #region ::: Select Service Request Count Day wise Uday Kumar J B 21-11-2024:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Tr_CaseDistributionChart/SelectDayReport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectDayReport([FromBody] HelpDesk_Tr_CaseDistributionChartSelectYearReportList HelpDesk_Tr_CaseDistributionChartSelectYearReportobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Tr_CaseDistributionChartServices.SelectDayReport(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion




        #region ::: Get Party Detail Grid Uday Kumar J B 21-11-2024:::
        /// <summary>
        ///To select menus of respective module
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Tr_CaseDistributionChart/SelectPartyDetailGrid")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartyDetailGrid([FromBody] HelpDesk_Tr_CaseDistributionChartSelectYearReportList HelpDesk_Tr_CaseDistributionChartSelectYearReportobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Tr_CaseDistributionChartServices.SelectPartyDetailGrid(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion




        #region ::: To Export Uday Kumar J B 21-11-2024:::
        /// <summary>
        /// To Export 
        /// </summary>
        /// <returns>...</returns>
        ///  
        [Route("api/HelpDesk_Tr_CaseDistributionChart/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] HelpDesk_Tr_CaseDistributionChartSelectYearReportList HelpDesk_Tr_CaseDistributionChartSelectYearReportobj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.sidx;
            string sord = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.sord;
            string filter = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.filter;
            string advnceFilter = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.advanceFilter;

            try
            {


                Object Response = await HelpDesk_Tr_CaseDistributionChartServices.Export(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj, connstring, LogException, filter, advnceFilter, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion


    }
}