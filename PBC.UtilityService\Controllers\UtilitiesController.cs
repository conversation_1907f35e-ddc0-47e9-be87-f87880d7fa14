using Microsoft.AspNetCore.Mvc;
using PBC.UtilityService.Services;
using PBC.UtilityService.Utilities;
using PBC.UtilityService.Utilities.Models;
using PBC.UtilityService.Models;
using System.Data.SqlClient;

namespace PBC.UtilityService.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class UtilitiesController : ControllerBase
    {
        private readonly ILogger<UtilitiesController> _logger;
        private readonly IUtilitiesService _utilitiesService;

        public UtilitiesController(ILogger<UtilitiesController> logger, IUtilitiesService utilitiesService)
        {
            _logger = logger;
            _utilitiesService = utilitiesService;
        }

        /// <summary>
        /// Calculate total pages based on number of records and page size
        /// </summary>
        /// <param name="numberOfRecords">Total number of records</param>
        /// <param name="pageSize">Number of records per page</param>
        /// <returns>Total number of pages</returns>
        [HttpGet("calculate-total-pages")]
        public async Task<ActionResult<int>> CalculateTotalPages(long numberOfRecords, int pageSize)
        {
            try
            {
                if (pageSize <= 0)
                {
                    return BadRequest("Page size must be greater than 0");
                }

                var result = await _utilitiesService.CalculateTotalPagesAsync(numberOfRecords, pageSize);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating total pages");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Check if a string is a valid date
        /// </summary>
        /// <param name="date">Date string to validate</param>
        /// <returns>True if valid date, false otherwise</returns>
        [HttpGet("is-date")]
        public async Task<ActionResult<bool>> IsDate(string date)
        {
            try
            {
                var result = await _utilitiesService.IsDateAsync(date);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if string is date");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Check if an object is numeric
        /// </summary>
        /// <param name="entity">Object to check</param>
        /// <returns>True if numeric, false otherwise</returns>
        [HttpPost("is-numeric")]
        public async Task<ActionResult<bool>> IsNumeric([FromBody] object entity)
        {
            try
            {
                var result = await _utilitiesService.IsNumericAsync(entity);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if object is numeric");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Check if an object is a valid double
        /// </summary>
        /// <param name="entity">Object to check</param>
        /// <returns>True if valid double, false otherwise</returns>
        [HttpPost("is-double")]
        public async Task<ActionResult<bool>> IsDouble([FromBody] object entity)
        {
            try
            {
                var result = await _utilitiesService.IsDoubleAsync(entity);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if object is double");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Create a message list with a single message
        /// </summary>
        /// <param name="message">Message to add to list</param>
        /// <returns>List containing the message</returns>
        [HttpGet("message")]
        public async Task<ActionResult<List<string>>> Message(string message)
        {
            try
            {
                var result = await _utilitiesService.MessageAsync(message);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating message list");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Calculate MD5 hash of input string
        /// </summary>
        /// <param name="input">String to hash</param>
        /// <returns>MD5 hash as string</returns>
        [HttpGet("calculate-md5-hash")]
        public async Task<ActionResult<string>> CalculateMD5Hash(string input)
        {
            try
            {
                if (string.IsNullOrEmpty(input))
                {
                    return BadRequest("Input cannot be null or empty");
                }

                var result = await _utilitiesService.CalculateMD5HashAsync(input);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating MD5 hash");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Generate DMS password hash
        /// </summary>
        /// <param name="userPassword">Plain text password</param>
        /// <returns>Hashed password</returns>
        [HttpPost("generate-dms-password")]
        public async Task<ActionResult<string>> GenerateDMSPassword([FromBody] string userPassword)
        {
            try
            {
                if (string.IsNullOrEmpty(userPassword))
                {
                    return BadRequest("Password cannot be null or empty");
                }

                var result = await _utilitiesService.GenerateDMSPasswordAsync(userPassword);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating DMS password");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get global resource object value
        /// </summary>
        /// <param name="cultureValue">Culture value</param>
        /// <param name="resourceKey">Resource key</param>
        /// <returns>Resource value as JSON</returns>
        [HttpGet("get-global-resource-object")]
        public async Task<IActionResult> GetGlobalResourceObject(string cultureValue, string resourceKey)
        {
            try
            {
                var result = await _utilitiesService.GetGlobalResourceObjectAsync(cultureValue, resourceKey);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting global resource object");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get month name based on culture
        /// </summary>
        /// <param name="id">Month ID (1-12)</param>
        /// <param name="culture">Culture string</param>
        /// <returns>Month name in specified culture</returns>
        [HttpGet("get-month-name")]
        public async Task<ActionResult<string>> GetMonthName(int id, string culture)
        {
            try
            {
                if (id < 1 || id > 12)
                {
                    return BadRequest("Month ID must be between 1 and 12");
                }

                var result = await _utilitiesService.GetMonthNameAsync(id, culture);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting month name");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get priority name based on culture
        /// </summary>
        /// <param name="id">Priority ID (1-4)</param>
        /// <param name="culture">Culture string</param>
        /// <returns>Priority name in specified culture</returns>
        [HttpGet("get-priority")]
        public async Task<ActionResult<string>> GetPriority(byte id, string culture)
        {
            try
            {
                if (id < 1 || id > 4)
                {
                    return BadRequest("Priority ID must be between 1 and 4");
                }

                var result = await _utilitiesService.GetPriorityAsync(id, culture);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting priority");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get group queue for workflow (Role-based filtering version)
        /// </summary>
        /// <param name="request">Group queue request</param>
        /// <returns>List of indicators</returns>
        [HttpPost("get-group-queue")]
        public ActionResult<List<Indicator>> GetGroupQueue([FromBody] GroupQueueAdvancedRequest request)
        {
            try
            {
                var result = Common.GetGroupQueue(request.CompanyID, request.ConnString, request.WorkFlowID, request.UserID);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting group queue");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get all queue for workflow
        /// </summary>
        /// <param name="request">All queue request</param>
        /// <returns>List of indicators</returns>
        [HttpPost("get-all-queue")]
        public ActionResult<List<Indicator>> GetAllQueue([FromBody] AllQueueRequest request)
        {
            try
            {
                var result = Common.GetAllQueue(request.CompanyID, request.WorkFlowID, request.UserID, request.StatusID, request.BranchID, request.ConnString, request.LogException);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all queue");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Check if user can add records
        /// </summary>
        /// <param name="request">Add records check request</param>
        /// <returns>True if user can add records</returns>
        [HttpPost("check-is-add-records")]
        public ActionResult<bool> CheckIsAddRecords([FromBody] CheckAddRecordsRequest request)
        {
            try
            {
                var result = Common.chkIsAddRecords(request.ObjectID, request.WorkFlowID, request.CompanyID, request.UserID, request.ConnString, request.LogException);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking add records permission");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get value from database using generic method
        /// </summary>
        /// <param name="request">Database query request</param>
        /// <returns>Query result as JSON</returns>
        [HttpPost("get-value-from-db")]
        public IActionResult GetValueFromDB([FromBody] GetValueFromDBRequest request)
        {
            try
            {
                // Use reflection to call the generic method
                var method = typeof(Common).GetMethod("GetValueFromDB");
                var genericMethod = method.MakeGenericMethod(request.ReturnType);

                var result = genericMethod.Invoke(null, new object[]
                {
                    request.Query,
                    request.Parameters,
                    request.ConnectionString,
                    request.LogException,
                    request.NextResult,
                    request.ResultSetIndex,
                    request.IsStoredProcedure
                });

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting value from database");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Decrypt string
        /// </summary>
        /// <param name="encryptedString">String to decrypt</param>
        /// <returns>Decrypted string</returns>
        [HttpPost("decrypt-string")]
        public ActionResult<string> DecryptString([FromBody] string encryptedString)
        {
            try
            {
                var result = Common.DecryptString(encryptedString);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error decrypting string");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get AMP_SP configuration value
        /// </summary>
        /// <returns>AMP_SP value</returns>
        [HttpGet("amp-sp")]
        public ActionResult<string> GetAmpSp()
        {
            try
            {
                var result = Common.AMP_SP();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting AMP_SP");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get workflow ID by name
        /// </summary>
        /// <param name="request">Workflow ID request</param>
        /// <returns>Workflow ID</returns>
        [HttpPost("get-workflow-id")]
        public ActionResult<int> GetWorkFlowID([FromBody] GetWorkFlowIDRequest request)
        {
            try
            {
                var result = Common.GetWorkFlowID(request.WorkFlowName, request.DBName, request.ConnString, request.LogException);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting workflow ID");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get initial setup data for ACL permissions
        /// </summary>
        /// <param name="request">Initial setup request</param>
        /// <returns>Initial setup data as JSON result</returns>
        [HttpPost("initial-setup")]
        public async Task<IActionResult> GetInitialSetup([FromBody] InitialSetupRequest request)
        {
            try
            {
                var result = await _utilitiesService.GetInitialSetupAsync(
                    request.ObjectId,
                    request.UserId,
                    request.ConnectionString,
                    request.LogException);

                return Ok(result.Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting initial setup");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Validate call date and PCD (Promised Completion Date)
        /// </summary>
        /// <param name="request">Date validation request</param>
        /// <returns>Validation result (1 if PCD is before call date, 0 otherwise)</returns>
        [HttpPost("validate-calldate-pcd")]
        public async Task<IActionResult> ValidateCalldateAndPCD([FromBody] ValidateCalldateandPCDRequest request)
        {
            try
            {
                var result = await _utilitiesService.ValidateCalldateandPCDAsync(request.PCD, request.Calldate);
                return Ok(result.Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating call date and PCD");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Check bay and workshop availability
        /// </summary>
        /// <param name="request">Bay workshop availability request</param>
        /// <returns>Availability status</returns>
        [HttpPost("check-bay-workshop-availability")]
        public async Task<IActionResult> CheckBayWorkshopAvailability([FromBody] CheckBayWorkshopAvailabilityRequest request)
        {
            try
            {
                var result = await _utilitiesService.CheckBayWorkshopAvailabilityAsync(
                    request.ExpectedArrivalDate,
                    request.ExpectedDepartureDate,
                    request.IsWIPBay,
                    request.BookingMinutes,
                    request.ServiceRequest_ID,
                    request.Quotation_ID,
                    request.Branch,
                    request.ConnectionString,
                    request.LogException);

                return Ok(result.Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking bay workshop availability");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Check if auto allocation is allowed for a workflow
        /// </summary>
        /// <param name="request">Auto allocation check request</param>
        /// <returns>True if auto allocation is allowed, false otherwise</returns>
        /// <response code="200">Returns auto allocation status</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("check-auto-allocation")]
        public async Task<ActionResult<bool>> CheckAutoAllocation([FromBody] CheckAutoAllocationRequest request)
        {
            try
            {
                if (request.CompanyID <= 0 || request.WorkFlowID <= 0 || request.UserID <= 0)
                {
                    return BadRequest("CompanyID, WorkFlowID, and UserID must be greater than 0");
                }

                if (string.IsNullOrEmpty(request.ConnString))
                {
                    return BadRequest("Connection string cannot be null or empty");
                }

                var result = await _utilitiesService.CheckAutoAllocationAsync(
                    request.CompanyID,
                    request.WorkFlowID,
                    request.UserID,
                    request.ConnString,
                    request.LogException);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking auto allocation");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get auto allocation step details for a workflow
        /// </summary>
        /// <param name="request">Auto allocation step details request</param>
        /// <returns>Auto allocation step details</returns>
        /// <response code="200">Returns auto allocation step details</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("get-auto-allocation-step-details")]
        public async Task<ActionResult<object>> GetAutoAllocationStepDetails([FromBody] GetAutoAllocationStepDetailsRequest request)
        {
            try
            {
                if (request.WorkFlowID <= 0 || request.CompanyID <= 0)
                {
                    return BadRequest("WorkFlowID and CompanyID must be greater than 0");
                }

                if (string.IsNullOrEmpty(request.ConnString))
                {
                    return BadRequest("Connection string cannot be null or empty");
                }

                var result = await _utilitiesService.GetAutoAllocationStepDetailsAsync(
                    request.WorkFlowID,
                    request.CompanyID,
                    request.ConnString,
                    request.LogException);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting auto allocation step details");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Lock a record in the workflow system
        /// </summary>
        /// <param name="request">Lock record request</param>
        /// <returns>Lock result message</returns>
        /// <response code="200">Returns lock result message</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("lock-record")]
        public async Task<ActionResult<string>> LockRecord([FromBody] LockRecordRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.ConnString))
                {
                    return BadRequest("Connection string cannot be null or empty");
                }

                if (string.IsNullOrEmpty(request.WorkFlowName))
                {
                    return BadRequest("WorkFlow name cannot be null or empty");
                }

                if (request.QuotationID <= 0 || request.UserID <= 0 || request.CompanyID <= 0)
                {
                    return BadRequest("QuotationID, UserID, and CompanyID must be greater than 0");
                }

                var result = await _utilitiesService.LockRecordAsync(
                    request.ConnString,
                    request.LogException,
                    request.UserCulture,
                    request.QuotationID,
                    request.UserID,
                    request.CompanyID,
                    request.WorkFlowName,
                    request.DBName,
                    request.Branch_ID);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error locking record");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Unlock a record in the workflow system
        /// </summary>
        /// <param name="request">Unlock record request</param>
        /// <returns>Unlock result message</returns>
        /// <response code="200">Returns unlock result message</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("unlock-record")]
        public async Task<ActionResult<string>> UnLockRecord([FromBody] UnLockRecordRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.ConnString))
                {
                    return BadRequest("Connection string cannot be null or empty");
                }

                if (string.IsNullOrEmpty(request.WorkFlowName))
                {
                    return BadRequest("WorkFlow name cannot be null or empty");
                }

                if (request.JobcardID <= 0 || request.UserID <= 0 || request.CompanyID <= 0)
                {
                    return BadRequest("JobcardID, UserID, and CompanyID must be greater than 0");
                }

                var result = await _utilitiesService.UnLockRecordAsync(
                    request.ConnString,
                    request.LogException,
                    request.UserCulture,
                    request.JobcardID,
                    request.UserID,
                    request.CompanyID,
                    request.WorkFlowName,
                    request.DBName,
                    request.Branch_ID);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unlocking record");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Convert server time to local time based on branch timezone
        /// </summary>
        /// <param name="request">Local time conversion request</param>
        /// <returns>Local time based on branch timezone</returns>
        /// <response code="200">Returns local time</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("local-time-based-on-branch")]
        public async Task<ActionResult<DateTime>> LocalTimeBasedOnBranch([FromBody] LocalTimeBasedOnBranchRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.ConnString))
                {
                    return BadRequest("Connection string cannot be null or empty");
                }

                if (request.BranchID <= 0)
                {
                    return BadRequest("BranchID must be greater than 0");
                }

                var result = await _utilitiesService.LocalTimeBasedOnBranchAsync(
                    request.BranchID,
                    request.ServerTime,
                    request.ConnString);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting server time to local time");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Convert server time to local time based on user timezone
        /// </summary>
        /// <param name="request">Local time conversion request</param>
        /// <returns>Local time based on user timezone</returns>
        /// <response code="200">Returns local time</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("local-time")]
        public async Task<ActionResult<DateTime>> LocalTime([FromBody] LocalTimeRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.ConnString))
                {
                    return BadRequest("Connection string cannot be null or empty");
                }

                if (request.UserID <= 0)
                {
                    return BadRequest("UserID must be greater than 0");
                }

                var result = await _utilitiesService.LocalTimeAsync(
                    request.UserID,
                    request.ServerTime,
                    request.ConnString);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting server time to local time for user");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get the end step status name for a workflow
        /// </summary>
        /// <param name="request">End step status name request</param>
        /// <returns>End step status name</returns>
        /// <response code="200">Returns end step status name</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("get-end-step-status-name")]
        public async Task<ActionResult<string>> GetEndStepStatusName([FromBody] GetEndStepStatusNameRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.ConnString))
                {
                    return BadRequest("Connection string cannot be null or empty");
                }

                if (request.WorkflowID <= 0)
                {
                    return BadRequest("WorkflowID must be greater than 0");
                }

                var result = await _utilitiesService.GetEndStepStatusNameAsync(
                    request.WorkflowID,
                    request.ConnString,
                    request.LogException);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting end step status name");
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
