﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class HelpDesk_Tr_CaseDistributionChartServices
    {


        #region ::: global Declartation Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To Load Branch
        /// </summary>
        /// 
        public static int InProgressID = 0;
        public static int ClosedID = 0;
        public static int HoldID = 0;
        public static int CompletedID = 0;

        #endregion



        #region ::: LoadBranchDD Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To Load Branch
        /// </summary>
        /// 
        public static IActionResult LoadBranchDD(HelpDesk_Tr_CaseDistributionChartLoadBranchDDList HelpDesk_Tr_CaseDistributionChartLoadBranchDDobj, string connString, int LogException)
        {
            object jsonobj = null;
            int Language_ID = Convert.ToInt32(HelpDesk_Tr_CaseDistributionChartLoadBranchDDobj.UserLanguageID);
            int CompanyID = Convert.ToInt32(HelpDesk_Tr_CaseDistributionChartLoadBranchDDobj.Company_ID);

            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Determine which stored procedure to use
                    string spName = HelpDesk_Tr_CaseDistributionChartLoadBranchDDobj.GeneralLanguageCode.ToString() == HelpDesk_Tr_CaseDistributionChartLoadBranchDDobj.UserLanguageCode.ToString()
                        ? "SP_AMERP_HelpDesk_GetBranchesByGeneralLanguage"
                        : "SP_AMERP_HelpDesk_GetBranchesByLocalizedLanguageLoadBranchDD";

                    using (SqlCommand cmd = new SqlCommand(spName, conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                        cmd.Parameters.AddWithValue("@EmployeeID", HelpDesk_Tr_CaseDistributionChartLoadBranchDDobj.Employee_ID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            var branches = new List<object>();

                            while (reader.Read())
                            {
                                branches.Add(new
                                {
                                    ID = reader["ID"],
                                    Name = reader["Name"]
                                });
                            }

                            jsonobj = new { Branch = branches };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonobj);
        }
        #endregion



        #region ::: load Reference Master data Uday Kumar J B 18-11-2024 :::
        /// <summary>
        /// To load master drop downs
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult loadMasters(HelpDesk_Tr_CaseDistributionChartloadMastersDDList HelpDesk_Tr_CaseDistributionChartloadMastersDDobj, string connString, int LogException)
        {
            object jsonobj = null;

            try
            {
                // User details and language code setup
                int LangID = HelpDesk_Tr_CaseDistributionChartloadMastersDDobj.Language_ID;
                string GenLangCode = HelpDesk_Tr_CaseDistributionChartloadMastersDDobj.GeneralLanguageCode.ToString();
                string UserLangCode = HelpDesk_Tr_CaseDistributionChartloadMastersDDobj.UserLanguageCode.ToString();

                // Find Master ID and Company-Specific Flag
                int MasterID;
                bool IsCompSpecific;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesK_GetRefMasterDetailsByName", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@MasterName", HelpDesk_Tr_CaseDistributionChartloadMastersDDobj.MasterName);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                MasterID = Convert.ToInt32(reader["RefMaster_ID"]);
                                IsCompSpecific = Convert.ToBoolean(reader["IsCompanySpecific"]);
                            }
                            else
                            {
                                MasterID = 0;
                                IsCompSpecific = false;
                            }
                        }
                    }

                    string CompanyIDs = string.IsNullOrEmpty(HelpDesk_Tr_CaseDistributionChartloadMastersDDobj.CompanyIDs) ? "0" : HelpDesk_Tr_CaseDistributionChartloadMastersDDobj.CompanyIDs.TrimEnd(',');

                    // Fetch data using appropriate stored procedure


                    string spName = (GenLangCode == UserLangCode)
                        ? "SP_AMERP_HelpDesK_GetRefMasterDetailsByCompany"
                        : "SP_AMERP_HelpDesK_GetRefMasterDetailsByLanguage";

                    using (SqlCommand cmd = new SqlCommand(spName, conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@MasterID", MasterID);
                        if (GenLangCode != UserLangCode)
                            cmd.Parameters.AddWithValue("@LangID", LangID);
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                        cmd.Parameters.AddWithValue("@IsCompSpecific", IsCompSpecific);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            var rows = new List<object>();
                            while (reader.Read())
                            {
                                rows.Add(new
                                {
                                    ID = reader["ID"],
                                    Name = reader["Name"]
                                });
                            }

                            jsonobj = new { rows = rows.ToArray() };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonobj);
        }
        #endregion



        #region ::: load Brand Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To load Brand
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult SelectBrand(HelpDesk_Tr_CaseDistributionChartSelectBrandList HelpDesk_Tr_CaseDistributionChartSelectBrandobj, string connString, int LogException)
        {
            object jsonobj = null;
            try
            {
                // Retrieve session data
                int CompanyID = Convert.ToInt32(HelpDesk_Tr_CaseDistributionChartSelectBrandobj.Company_ID);
                int LangID = HelpDesk_Tr_CaseDistributionChartSelectBrandobj.Language_ID;
                string GenLangCode = HelpDesk_Tr_CaseDistributionChartSelectBrandobj.GeneralLanguageCode.ToString();
                string UserLangCode = HelpDesk_Tr_CaseDistributionChartSelectBrandobj.UserLanguageCode.ToString();

                string spName = (GenLangCode == UserLangCode) ? "SP_AMERP_HelpDesk_GetBrands" : "SP_AMERP_HelpDesk_GetBrandsByLanguage";

                // Set up connection and command
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand(spName, conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@RefMasterName", "BRAND");
                        if (GenLangCode != UserLangCode)
                        {
                            cmd.Parameters.AddWithValue("@LanguageID", LangID);
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            var rows = new List<object>();
                            while (reader.Read())
                            {
                                rows.Add(new
                                {
                                    ID = reader["ID"],
                                    Name = reader["Name"]
                                });
                            }

                            jsonobj = new { rows = rows.ToArray() };
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonobj);
        }
        #endregion




        #region ::: Load Brand Uday kumar J B 18-11-2024:::
        /// <summary>
        /// To Load Brand
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult LoadBrand(HelpDesk_Tr_CaseDistributionChartLoadBrandList HelpDesk_Tr_CaseDistributionChartLoadBrandobj, string connString, int LogException)
        {
            object BrandData = null;
            try
            {
                // Retrieve session data
                int LangID = HelpDesk_Tr_CaseDistributionChartLoadBrandobj.Language_ID;
                string GenLangCode = HelpDesk_Tr_CaseDistributionChartLoadBrandobj.GeneralLanguageCode.ToString();
                string UserLangCode = HelpDesk_Tr_CaseDistributionChartLoadBrandobj.UserLanguageCode.ToString();

                List<dynamic> companyBrands = new List<dynamic>();
                List<dynamic> brands = new List<dynamic>();

                // Fetch data using stored procedures
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Fetch company brands
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetCompanyBrandsLoadBrand", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyIDs", HelpDesk_Tr_CaseDistributionChartLoadBrandobj.CompanyIDs);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                companyBrands.Add(new
                                {
                                    Company_Brand_ID = reader["Company_Brand_ID"],
                                    Company_ID = reader["Company_ID"],
                                    Brand_ID = reader["Brand_ID"]
                                });
                            }
                        }
                    }

                    // Fetch active brands
                    string spName = (GenLangCode == UserLangCode) ? "SP_AMERP_HelpDesk_GetActiveBrandsLoadBrand" : "SP_AMERP_HelpDesk_GetLocalizedBrands";
                    using (SqlCommand cmd = new SqlCommand(spName, conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        if (spName == "SP_AMERP_HelpDesk_GetLocalizedBrands")
                        {
                            cmd.Parameters.AddWithValue("@LanguageID", LangID);
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                brands.Add(new
                                {
                                    ID = reader["ID"],
                                    Name = reader["Name"]
                                });
                            }
                        }
                    }
                }

                // Combine data
                if (GenLangCode == UserLangCode)
                {
                    BrandData = (from cb in companyBrands
                                 join b in brands on cb.Brand_ID equals b.ID
                                 orderby b.Name
                                 select new
                                 {
                                     ID = b.ID,
                                     Name = b.Name
                                 }).Distinct();
                }
                else
                {
                    BrandData = (from cb in companyBrands
                                 join b in brands on cb.Brand_ID equals b.ID
                                 orderby b.Name
                                 select new
                                 {
                                     ID = b.ID,
                                     Name = b.Name
                                 }).Distinct();
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(BrandData);
        }
        #endregion




        #region ::: load Product Type Uday kumar J B 18-11-2024 :::
        /// <summary>
        /// To load Product Type
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult ProductType(HelpDesk_Tr_CaseDistributionChartProductTypeList HelpDesk_Tr_CaseDistributionChartProductTypeobj, string connString, int LogException)
        {
            object jsonobj = null;

            try
            {
                // Session data
                int LangID = HelpDesk_Tr_CaseDistributionChartProductTypeobj.Language_ID;
                string GenLangCode = HelpDesk_Tr_CaseDistributionChartProductTypeobj.GeneralLanguageCode.ToString();
                string UserLangCode = HelpDesk_Tr_CaseDistributionChartProductTypeobj.UserLanguageCode.ToString();

                List<dynamic> companyBrands = new List<dynamic>();
                List<dynamic> productTypes = new List<dynamic>();
                List<dynamic> productTypeLocales = new List<dynamic>();
                List<dynamic> brandNames = new List<dynamic>();

                // Fetch data using ADO.NET
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // 1. Fetch Company Brands
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetCompanyBrandsProductType", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyIDs", HelpDesk_Tr_CaseDistributionChartProductTypeobj.CompanyIDs);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                companyBrands.Add(new
                                {
                                    Brand_ID = reader["Brand_ID"]
                                });
                            }
                        }
                    }

                    // 2. Fetch Product Types or Locales
                    string spName = (GenLangCode == UserLangCode) ? "SP_AMERP_HelpDesk_GetProductTypes" : "SP_AMERP_HelpDesk_GetProductTypeLocalesProductType";
                    using (SqlCommand cmd = new SqlCommand(spName, conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        if (spName == "SP_AMERP_HelpDesk_GetProductTypeLocalesProductType")
                            cmd.Parameters.AddWithValue("@LanguageID", LangID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                if (GenLangCode == UserLangCode)
                                {
                                    productTypes.Add(new
                                    {
                                        ProductType_ID = reader["ProductType_ID"],
                                        Brand_ID = reader["Brand_ID"],
                                        ProductType_Name = reader["ProductType_Name"]
                                    });
                                }
                                else
                                {
                                    productTypeLocales.Add(new
                                    {
                                        ProductType_ID = reader["ProductType_ID"],
                                        ProductType_Name = reader["ProductType_Name"]
                                    });
                                }
                            }
                        }
                    }

                    // 3. Fetch Brand Names or Localized Brand Names
                    spName = (GenLangCode == UserLangCode) ? "SP_AMERP_HelpDesk_GetBrandNames" : "SP_AMERP_HelpDesk_GetLocalizedBrandNames";
                    using (SqlCommand cmd = new SqlCommand(spName, conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        if (spName == "SP_AMERP_HelpDesk_GetLocalizedBrandNames")
                            cmd.Parameters.AddWithValue("@LanguageID", LangID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                brandNames.Add(new
                                {
                                    Brand_ID = reader["Brand_ID"],
                                    Brand_Name = reader["Brand_Name"]
                                });
                            }
                        }
                    }
                }

                // Combine data
                if (GenLangCode == UserLangCode)
                {
                    var productTypeList = (from pt in productTypes
                                           join cb in companyBrands on pt.Brand_ID equals cb.Brand_ID
                                           join bn in brandNames on cb.Brand_ID equals bn.Brand_ID
                                           orderby pt.ProductType_Name
                                           select new
                                           {
                                               ID = pt.ProductType_ID,
                                               Name = pt.ProductType_Name + "--" + bn.Brand_Name
                                           }).Distinct();

                    jsonobj = new { rows = productTypeList };
                }
                else
                {
                    var productTypeLocaleList = (from ptl in productTypeLocales
                                                 join cb in companyBrands on ptl.ProductType_ID equals cb.Brand_ID
                                                 join bn in brandNames on cb.Brand_ID equals bn.Brand_ID
                                                 orderby ptl.ProductType_Name
                                                 select new
                                                 {
                                                     ID = ptl.ProductType_ID,
                                                     Name = ptl.ProductType_Name + "--" + bn.Brand_Name
                                                 }).Distinct();

                    jsonobj = new { rows = productTypeLocaleList };
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonobj);
        }
        #endregion



        #region ::: Get Party Dtails Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To get Customer details
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult GetPartyDetails(HelpDesk_Tr_CaseDistributionChartGetPartyDetailsList HelpDesk_Tr_CaseDistributionChartGetPartyDetailsobj, string connString, int LogException)
        {
            object jsonResult = null;

            try
            {
                // Decrypt Party Name
                string Pname = Common.DecryptString(HelpDesk_Tr_CaseDistributionChartGetPartyDetailsobj.PartyName);

                // Session data
                int LangID = HelpDesk_Tr_CaseDistributionChartGetPartyDetailsobj.Language_ID;
                string GenLangCode = HelpDesk_Tr_CaseDistributionChartGetPartyDetailsobj.GeneralLanguageCode.ToString();
                string UserLangCode = HelpDesk_Tr_CaseDistributionChartGetPartyDetailsobj.UserLanguageCode.ToString();

                List<dynamic> parties = new List<dynamic>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    if (GenLangCode == UserLangCode)
                    {
                        // Fetch party details in general language
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetPartyByName", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@CompanyID", HelpDesk_Tr_CaseDistributionChartGetPartyDetailsobj.Company_ID);
                            cmd.Parameters.AddWithValue("@PartyName", Pname);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    parties.Add(new
                                    {
                                        Party_ID = reader["Party_ID"],
                                        Party_Name = reader["Party_Name"]
                                    });
                                }
                            }
                        }
                    }
                    else
                    {
                        // Fetch localized party details
                        using (SqlCommand cmd = new SqlCommand("sp_HelpDesk_GetPartyLocaleByName", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@CompanyID", HelpDesk_Tr_CaseDistributionChartGetPartyDetailsobj.Company_ID);
                            cmd.Parameters.AddWithValue("@PartyName", Pname);
                            cmd.Parameters.AddWithValue("@LanguageID", LangID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    parties.Add(new
                                    {
                                        Party_ID = reader["Party_ID"],
                                        Party_Name = reader["Party_Name"]
                                    });
                                }
                            }
                        }
                    }
                }

                // Generate JSON result based on the number of results
                if (parties.Count == 0)
                {
                    jsonResult = new { Result = "0" };
                }
                else if (parties.Count == 1)
                {
                    jsonResult = new
                    {
                        Result = "1",
                        PartyID = parties[0].Party_ID
                    };
                }
                else
                {
                    jsonResult = new { Result = "2" };
                }
            }
            catch (Exception ex)
            {
                jsonResult = new { Result = "0" };

                // Log exception
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonResult);
        }
        #endregion



        #region ::: Get Model Dtails Uday kumar J B 18-1-2024:::
        /// <summary>
        /// To get Customer details
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult GetModel(HelpDesk_Tr_CaseDistributionChartGetModelList HelpDesk_Tr_CaseDistributionChartGetModelsobj, string connString, int LogException)
        {
            object jsonResult = null;

            try
            {
                // Decrypt Model Name
                string ModelDec = Common.DecryptString(HelpDesk_Tr_CaseDistributionChartGetModelsobj.ModelName);

                // Session data
                int LangID = HelpDesk_Tr_CaseDistributionChartGetModelsobj.Language_ID;
                string GenLangCode = HelpDesk_Tr_CaseDistributionChartGetModelsobj.GeneralLanguageCode.ToString();
                string UserLangCode = HelpDesk_Tr_CaseDistributionChartGetModelsobj.UserLanguageCode.ToString();

                List<dynamic> models = new List<dynamic>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    if (GenLangCode == UserLangCode)
                    {
                        // Fetch general model details
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetModelByName", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@ModelName", ModelDec);
                            cmd.Parameters.AddWithValue("@CompanyID", HelpDesk_Tr_CaseDistributionChartGetModelsobj.Company_ID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    models.Add(new
                                    {
                                        ID = reader["Model_ID"],
                                        Name = reader["Model_Name"]
                                    });
                                }
                            }
                        }
                    }
                    else
                    {
                        // Fetch localized model details
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetLocalizedModelByName", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@ModelName", ModelDec);
                            cmd.Parameters.AddWithValue("@LanguageID", LangID);
                            cmd.Parameters.AddWithValue("@CompanyID", HelpDesk_Tr_CaseDistributionChartGetModelsobj.Company_ID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    models.Add(new
                                    {
                                        ID = reader["Model_ID"],
                                        Name = reader["Model_Name"]
                                    });
                                }
                            }
                        }
                    }
                }

                // Generate JSON result based on the number of results
                if (models.Count == 0)
                {
                    jsonResult = new { Result = "0" };
                }
                else
                {
                    jsonResult = new
                    {
                        Result = "1",
                        ModelID = models[0].ID
                    };
                }
            }
            catch (Exception ex)
            {
                jsonResult = new { Result = "0" };

                // Log exception
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonResult);
        }
        #endregion




        #region ::: Get Status IDs Uday kumar J B 18-11-2024:::
        /// <summary>
        /// To get Status IDs
        /// </summary>
        /// <returns>...</returns>
        /// 

        public static IActionResult GetStatusIDs(string connString, int LogException)
        {
            try
            {
                // Retrieve InProgressID
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Get InProgressID
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetStatusID_InProgress", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        InProgressID = Convert.ToInt32(cmd.ExecuteScalar());
                    }

                    // Get ClosedID using WorkFlow ID
                    //  ClosedID = HelpDeskCommon.GetEndStepStatusID(Common.GetWorkFlowID("Case Registration", ConfigurationManager.AppSettings.Get("DbName")));


                    // Get HoldID
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetStatusID_Hold", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        HoldID = Convert.ToInt32(cmd.ExecuteScalar());
                    }

                    // Get CompletedID
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetStatusID_Completed", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        CompletedID = Convert.ToInt32(cmd.ExecuteScalar());
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return null;
        }
        #endregion




        #region ::: Select Service Request Count Year Uday Kumar J B 20-11-2024:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult SelectYearReport(HelpDesk_Tr_CaseDistributionChartSelectYearReportList HelpDesk_Tr_CaseDistributionChartSelectYearReportobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonobj = default(dynamic);
            int count = 0;
            int total = 0;
            IEnumerable<ServiceRequestCount> SRCountListRecieved = null;
            IEnumerable<ServiceRequestCount> SRCountListClosed = null;
            IEnumerable<ServiceRequestCount> SRCountListStatus = null;
            List<ServiceRequestCount> FinalResultList = new List<ServiceRequestCount>();
            ServiceRequestCount FinalResult = new ServiceRequestCount();
            IQueryable<ServiceRequestCount> FinalResultSort = null;
            string GenLangCode = string.Empty;
            string UserLangCode = string.Empty;
            int TotalCount = 0;
            int TotalClosedCount = 0;
            try
            {
                GetStatusIDs(connString, LogException);
                GenLangCode = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.GeneralLanguageCode.ToString();
                UserLangCode = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserLanguageCode.ToString();
                string CompanyIDs = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.CompanyIDs.TrimEnd(new char[] { ',' });
                string BranchIDs = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.BranchIDs.TrimEnd(new char[] { ',' });
                int UserLangID = Convert.ToInt32(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserLanguageID);
                int GeneralLangID = Convert.ToInt32(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.GeneralLanguageID);
                int userid = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.User_ID;
                string frmdate = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.FromDate;
                string todate = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.ToDate;
                string FrmDate = frmdate;
                string TDate = todate;
                int Mode = Convert.ToInt32(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Mode);
                SelectionCriteria Selection = JObject.Parse(Uri.UnescapeDataString(Uri.UnescapeDataString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Data))).ToObject<SelectionCriteria>();

                foreach (var s in Selection.SelectedIDs)
                {
                    s.Name = Common.DecryptString(s.Name);
                }
                DateTime? FromDate = null;
                DateTime? ToDate = null;

                if (!string.IsNullOrEmpty(frmdate))
                {
                    FromDate = Convert.ToDateTime(frmdate);
                }

                if (!string.IsNullOrEmpty(todate))
                {
                    ToDate = Convert.ToDateTime(todate);
                }
                List<ServiceRequestCount> SRCountListRecievedMode = new List<ServiceRequestCount>();

                if (Mode == 1)//Issue Area
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts", conn);
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters to the stored procedure
                        cmd.Parameters.Add(new SqlParameter("@CompanyIDs", SqlDbType.NVarChar)).Value = CompanyIDs;
                        cmd.Parameters.Add(new SqlParameter("@BranchIDs", SqlDbType.NVarChar)).Value = BranchIDs;

                        if (FromDate.HasValue)
                            cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = FromDate.Value;
                        else
                            cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = DBNull.Value;

                        if (ToDate.HasValue)
                            cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = ToDate.Value;
                        else
                            cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = DBNull.Value;
                        cmd.Parameters.AddWithValue("@UserLangID", UserLangID);
                        cmd.Parameters.AddWithValue("@GeneralLangID", GeneralLangID);

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ServiceRequestCount srCount = new ServiceRequestCount()
                                {
                                    Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["IssueArea_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    StatusID = Convert.ToInt32(reader["CallStatus_ID"]),
                                    StatusName = reader["StatusName"].ToString(),
                                    CompanyName = reader["CompanyName"].ToString(),
                                    BranchName = reader["BranchName"].ToString(),
                                    BranchID = Convert.ToInt32(reader["Branch_ID"])
                                };

                                // After fetching the data, call CFC.getRegionName method
                                srCount.Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, srCount.BranchID);

                                SRCountListRecievedMode.Add(srCount);
                            }
                            SRCountListRecieved = SRCountListRecievedMode.AsEnumerable();
                        }
                    }

                }
                else if (Mode == 2)//Call Nature
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_CallNature", conn);
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters to the stored procedure
                        cmd.Parameters.Add(new SqlParameter("@CompanyIDs", SqlDbType.NVarChar)).Value = CompanyIDs;
                        cmd.Parameters.Add(new SqlParameter("@BranchIDs", SqlDbType.NVarChar)).Value = BranchIDs;

                        if (FromDate.HasValue)
                            cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = FromDate.Value;
                        else
                            cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = DBNull.Value;

                        if (ToDate.HasValue)
                            cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = ToDate.Value;
                        else
                            cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = DBNull.Value;
                        // Add parameters to the stored procedure
                        cmd.Parameters.AddWithValue("@UserLangID", UserLangID);
                        cmd.Parameters.AddWithValue("@GeneralLangID", GeneralLangID);

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ServiceRequestCount srCount = new ServiceRequestCount()
                                {
                                    Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["IssueArea_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    StatusID = Convert.ToInt32(reader["CallStatus_ID"]),
                                    StatusName = reader["StatusName"].ToString(),
                                    BranchName = reader["BranchName"].ToString(),
                                    BranchID = Convert.ToInt32(reader["Branch_ID"])
                                };

                                // After fetching the data, call CFC.getRegionName method
                                srCount.Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, srCount.BranchID);

                                SRCountListRecievedMode.Add(srCount);
                            }
                            SRCountListRecieved = SRCountListRecievedMode.AsEnumerable();
                        }
                    }
                }
                else if (Mode == 8)//Dealer
                {
                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_ForDealer", conn);
                            cmd.CommandType = CommandType.StoredProcedure;
                            // Add parameters to the stored procedure
                            cmd.Parameters.Add(new SqlParameter("@CompanyIDs", SqlDbType.NVarChar)).Value = CompanyIDs;
                            cmd.Parameters.Add(new SqlParameter("@BranchIDs", SqlDbType.NVarChar)).Value = BranchIDs;

                            if (FromDate.HasValue)
                                cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = FromDate.Value;
                            else
                                cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = DBNull.Value;

                            if (ToDate.HasValue)
                                cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = ToDate.Value;
                            else
                                cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = DBNull.Value;

                            // Add parameters to the stored procedure
                            cmd.Parameters.AddWithValue("@UserLangID", UserLangID);
                            cmd.Parameters.AddWithValue("@GeneralLangID", GeneralLangID);

                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount srCount = new ServiceRequestCount()
                                    {
                                        Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["IssueArea_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        StatusID = Convert.ToInt32(reader["CallStatus_ID"]),
                                        StatusName = reader["StatusName"].ToString(),
                                        CompanyName = reader["CompanyName"].ToString(),
                                        BranchName = reader["BranchName"].ToString(),
                                        BranchID = Convert.ToInt32(reader["Branch_ID"])
                                    };

                                    // After fetching the data, call CFC.getRegionName method
                                    srCount.Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, srCount.BranchID);

                                    SRCountListRecievedMode.Add(srCount);
                                }
                                SRCountListRecieved = SRCountListRecievedMode.AsEnumerable();
                            }
                        }
                    }
                    else
                    {

                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_ForDealer_WithBranchData", conn);
                            cmd.CommandType = CommandType.StoredProcedure;
                            // Add parameters to the stored procedure
                            cmd.Parameters.Add(new SqlParameter("@CompanyIDs", SqlDbType.NVarChar)).Value = CompanyIDs;
                            cmd.Parameters.Add(new SqlParameter("@BranchIDs", SqlDbType.NVarChar)).Value = BranchIDs;

                            if (FromDate.HasValue)
                                cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = FromDate.Value;
                            else
                                cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = DBNull.Value;

                            if (ToDate.HasValue)
                                cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = ToDate.Value;
                            else
                                cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = DBNull.Value;

                            // Add parameters to the stored procedure
                            cmd.Parameters.AddWithValue("@UserLangID", UserLangID);
                            cmd.Parameters.AddWithValue("@GeneralLangID", GeneralLangID);

                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount srCount = new ServiceRequestCount()
                                    {
                                        Type = reader["Type"].ToString(),
                                        Year = Convert.ToInt32(reader["Year"]),
                                        StatusID = Convert.ToInt32(reader["CallStatus_ID"]),
                                        StatusName = reader["StatusName"].ToString(),
                                        CompanyName = reader["CompanyName"].ToString(),
                                        BranchName = reader["BranchName"].ToString(),
                                        BranchID = Convert.ToInt32(reader["Branch_ID"])
                                    };

                                    // After fetching the data, call CFC.getRegionName method
                                    srCount.Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, srCount.BranchID);

                                    SRCountListRecievedMode.Add(srCount);
                                }
                                SRCountListRecieved = SRCountListRecievedMode.AsEnumerable();
                            }
                        }
                    }
                }

                else if (Mode == 3)//Party
                {
                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_ForNonDealer", conn);
                            cmd.CommandType = CommandType.StoredProcedure;
                            // Add parameters to the stored procedure
                            cmd.Parameters.Add(new SqlParameter("@CompanyIDs", SqlDbType.NVarChar)).Value = CompanyIDs;
                            cmd.Parameters.Add(new SqlParameter("@BranchIDs", SqlDbType.NVarChar)).Value = BranchIDs;

                            if (FromDate.HasValue)
                                cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = FromDate.Value;
                            else
                                cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = DBNull.Value;

                            if (ToDate.HasValue)
                                cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = ToDate.Value;
                            else
                                cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = DBNull.Value;

                            // Add parameters to the stored procedure
                            cmd.Parameters.AddWithValue("@UserLangID", UserLangID);
                            cmd.Parameters.AddWithValue("@GeneralLangID", GeneralLangID);

                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount srCount = new ServiceRequestCount()
                                    {
                                        Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["Party_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        StatusID = Convert.ToInt32(reader["CallStatus_ID"]),
                                        StatusName = reader["StatusName"].ToString(),
                                        CompanyName = reader["CompanyName"].ToString(),
                                        BranchName = reader["BranchName"].ToString(),
                                        BranchID = Convert.ToInt32(reader["Branch_ID"])
                                    };

                                    // After fetching the data, call CFC.getRegionName method
                                    srCount.Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, srCount.BranchID);

                                    SRCountListRecievedMode.Add(srCount);
                                }
                                SRCountListRecieved = SRCountListRecievedMode.AsEnumerable();
                            }
                        }
                    }
                    else
                    {
                        if (GenLangCode == UserLangCode)
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_ForNonDealer_WithParty", conn);
                                cmd.CommandType = CommandType.StoredProcedure;
                                // Add parameters to the stored procedure
                                cmd.Parameters.Add(new SqlParameter("@CompanyIDs", SqlDbType.NVarChar)).Value = CompanyIDs;
                                cmd.Parameters.Add(new SqlParameter("@BranchIDs", SqlDbType.NVarChar)).Value = BranchIDs;

                                if (FromDate.HasValue)
                                    cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = FromDate.Value;
                                else
                                    cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = DBNull.Value;

                                if (ToDate.HasValue)
                                    cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = ToDate.Value;
                                else
                                    cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = DBNull.Value;

                                // Add parameters to the stored procedure
                                cmd.Parameters.AddWithValue("@UserLangID", UserLangID);
                                cmd.Parameters.AddWithValue("@GeneralLangID", GeneralLangID);

                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount srCount = new ServiceRequestCount()
                                        {
                                            Type = reader["Type"].ToString(),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            StatusID = Convert.ToInt32(reader["CallStatus_ID"]),
                                            StatusName = reader["StatusName"].ToString(),
                                            CompanyName = reader["CompanyName"].ToString(),
                                            BranchName = reader["BranchName"].ToString(),
                                            BranchID = Convert.ToInt32(reader["Branch_ID"])
                                        };

                                        // After fetching the data, call CFC.getRegionName method
                                        srCount.Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, srCount.BranchID);

                                        SRCountListRecievedMode.Add(srCount);
                                    }
                                    SRCountListRecieved = SRCountListRecievedMode.AsEnumerable();
                                }
                            }
                        }
                        else
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_GetServiceRequestCounts_WithPartyLocale", conn);
                                cmd.CommandType = CommandType.StoredProcedure;
                                // Add parameters to the stored procedure
                                cmd.Parameters.Add(new SqlParameter("@CompanyIDs", SqlDbType.NVarChar)).Value = CompanyIDs;
                                cmd.Parameters.Add(new SqlParameter("@BranchIDs", SqlDbType.NVarChar)).Value = BranchIDs;

                                if (FromDate.HasValue)
                                    cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = FromDate.Value;
                                else
                                    cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = DBNull.Value;

                                if (ToDate.HasValue)
                                    cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = ToDate.Value;
                                else
                                    cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = DBNull.Value;
                                // Add parameters to the stored procedure
                                cmd.Parameters.AddWithValue("@UserLangID", UserLangID);
                                cmd.Parameters.AddWithValue("@GeneralLangID", GeneralLangID);

                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount srCount = new ServiceRequestCount()
                                        {
                                            Type = reader["Type"].ToString(),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            StatusID = Convert.ToInt32(reader["CallStatus_ID"]),
                                            StatusName = reader["StatusName"].ToString(),
                                            CompanyName = reader["CompanyName"].ToString(),
                                            BranchName = reader["BranchName"].ToString(),
                                            BranchID = Convert.ToInt32(reader["Branch_ID"])
                                        };

                                        // After fetching the data, call CFC.getRegionName method
                                        srCount.Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, srCount.BranchID);

                                        SRCountListRecievedMode.Add(srCount);
                                    }
                                    SRCountListRecieved = SRCountListRecievedMode.AsEnumerable();
                                }
                            }
                        }
                    }

                }

                else if (Mode == 4)//Priority
                {

                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_GetServiceRequestCounts_ByPriority", conn);
                        cmd.CommandType = CommandType.StoredProcedure;
                        // Add parameters to the stored procedure
                        cmd.Parameters.Add(new SqlParameter("@CompanyIDs", SqlDbType.NVarChar)).Value = CompanyIDs;
                        cmd.Parameters.Add(new SqlParameter("@BranchIDs", SqlDbType.NVarChar)).Value = BranchIDs;

                        if (FromDate.HasValue)
                            cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = FromDate.Value;
                        else
                            cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = DBNull.Value;

                        if (ToDate.HasValue)
                            cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = ToDate.Value;
                        else
                            cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = DBNull.Value;

                        // Add parameters to the stored procedure
                        cmd.Parameters.AddWithValue("@UserLangID", UserLangID);
                        cmd.Parameters.AddWithValue("@GeneralLangID", GeneralLangID);

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ServiceRequestCount srCount = new ServiceRequestCount()
                                {
                                    Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["CallPriority_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    StatusID = Convert.ToInt32(reader["CallStatus_ID"]),
                                    StatusName = reader["StatusName"].ToString(),
                                    CompanyName = reader["CompanyName"].ToString(),
                                    BranchName = reader["BranchName"].ToString(),
                                    BranchID = Convert.ToInt32(reader["Branch_ID"])
                                };

                                // After fetching the data, call CFC.getRegionName method
                                srCount.Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, srCount.BranchID);

                                SRCountListRecievedMode.Add(srCount);
                            }
                            SRCountListRecieved = SRCountListRecievedMode.AsEnumerable();
                        }
                    }
                }

                else if (Mode == 5)//Model
                {
                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_ByModel", conn);
                            cmd.CommandType = CommandType.StoredProcedure;
                            // Add parameters to the stored procedure
                            cmd.Parameters.Add(new SqlParameter("@CompanyIDs", SqlDbType.NVarChar)).Value = CompanyIDs;
                            cmd.Parameters.Add(new SqlParameter("@BranchIDs", SqlDbType.NVarChar)).Value = BranchIDs;

                            if (FromDate.HasValue)
                                cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = FromDate.Value;
                            else
                                cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = DBNull.Value;

                            if (ToDate.HasValue)
                                cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = ToDate.Value;
                            else
                                cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = DBNull.Value;

                            // Add parameters to the stored procedure
                            cmd.Parameters.AddWithValue("@UserLangID", UserLangID);
                            cmd.Parameters.AddWithValue("@GeneralLangID", GeneralLangID);

                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount srCount = new ServiceRequestCount()
                                    {
                                        Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["Model_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        StatusID = Convert.ToInt32(reader["CallStatus_ID"]),
                                        StatusName = reader["StatusName"].ToString(),
                                        CompanyName = reader["CompanyName"].ToString(),
                                        BranchName = reader["BranchName"].ToString(),
                                        BranchID = Convert.ToInt32(reader["Branch_ID"])
                                    };

                                    // After fetching the data, call CFC.getRegionName method
                                    srCount.Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, srCount.BranchID);

                                    SRCountListRecievedMode.Add(srCount);
                                }
                                SRCountListRecieved = SRCountListRecievedMode.AsEnumerable();
                            }
                        }
                    }
                    else
                    {


                        if (GenLangCode == UserLangCode)
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_ByModelLocal", conn);
                                cmd.CommandType = CommandType.StoredProcedure;
                                // Add parameters to the stored procedure
                                cmd.Parameters.Add(new SqlParameter("@CompanyIDs", SqlDbType.NVarChar)).Value = CompanyIDs;
                                cmd.Parameters.Add(new SqlParameter("@BranchIDs", SqlDbType.NVarChar)).Value = BranchIDs;

                                if (FromDate.HasValue)
                                    cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = FromDate.Value;
                                else
                                    cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = DBNull.Value;

                                if (ToDate.HasValue)
                                    cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = ToDate.Value;
                                else
                                    cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = DBNull.Value;

                                // Add parameters to the stored procedure
                                cmd.Parameters.AddWithValue("@UserLangID", UserLangID);
                                cmd.Parameters.AddWithValue("@GeneralLangID", GeneralLangID);

                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount srCount = new ServiceRequestCount()
                                        {
                                            Type = reader["Type"].ToString(),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            StatusID = Convert.ToInt32(reader["CallStatus_ID"]),
                                            StatusName = reader["StatusName"].ToString(),
                                            CompanyName = reader["CompanyName"].ToString(),
                                            BranchName = reader["BranchName"].ToString(),
                                            BranchID = Convert.ToInt32(reader["Branch_ID"])
                                        };

                                        // After fetching the data, call CFC.getRegionName method
                                        srCount.Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, srCount.BranchID);

                                        SRCountListRecievedMode.Add(srCount);
                                    }
                                    SRCountListRecieved = SRCountListRecievedMode.AsEnumerable();
                                }
                            }
                        }
                        else
                        {

                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_ByModelLocale", conn);
                                cmd.CommandType = CommandType.StoredProcedure;
                                // Add parameters to the stored procedure
                                cmd.Parameters.Add(new SqlParameter("@CompanyIDs", SqlDbType.NVarChar)).Value = CompanyIDs;
                                cmd.Parameters.Add(new SqlParameter("@BranchIDs", SqlDbType.NVarChar)).Value = BranchIDs;

                                if (FromDate.HasValue)
                                    cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = FromDate.Value;
                                else
                                    cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = DBNull.Value;

                                if (ToDate.HasValue)
                                    cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = ToDate.Value;
                                else
                                    cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = DBNull.Value;

                                // Add parameters to the stored procedure
                                cmd.Parameters.AddWithValue("@UserLangID", UserLangID);
                                cmd.Parameters.AddWithValue("@GeneralLangID", GeneralLangID);

                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount srCount = new ServiceRequestCount()
                                        {
                                            Type = reader["Type"].ToString(),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            StatusID = Convert.ToInt32(reader["CallStatus_ID"]),
                                            StatusName = reader["StatusName"].ToString(),
                                            CompanyName = reader["CompanyName"].ToString(),
                                            BranchName = reader["BranchName"].ToString(),
                                            BranchID = Convert.ToInt32(reader["Branch_ID"])
                                        };

                                        // After fetching the data, call CFC.getRegionName method
                                        srCount.Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, srCount.BranchID);

                                        SRCountListRecievedMode.Add(srCount);
                                    }
                                    SRCountListRecieved = SRCountListRecievedMode.AsEnumerable();
                                }
                            }
                        }
                    }

                }
                else if (Mode == 6)//Brand
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_ByBrand", conn);
                        cmd.CommandType = CommandType.StoredProcedure;
                        // Add parameters to the stored procedure
                        cmd.Parameters.Add(new SqlParameter("@CompanyIDs", SqlDbType.NVarChar)).Value = CompanyIDs;
                        cmd.Parameters.Add(new SqlParameter("@BranchIDs", SqlDbType.NVarChar)).Value = BranchIDs;

                        if (FromDate.HasValue)
                            cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = FromDate.Value;
                        else
                            cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = DBNull.Value;

                        if (ToDate.HasValue)
                            cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = ToDate.Value;
                        else
                            cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = DBNull.Value;

                        // Add parameters to the stored procedure
                        cmd.Parameters.AddWithValue("@UserLangID", UserLangID);
                        cmd.Parameters.AddWithValue("@GeneralLangID", GeneralLangID);

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ServiceRequestCount srCount = new ServiceRequestCount()
                                {
                                    Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["Brand_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    StatusID = Convert.ToInt32(reader["CallStatus_ID"]),
                                    StatusName = reader["StatusName"].ToString(),
                                    CompanyName = reader["CompanyName"].ToString(),
                                    BranchName = reader["BranchName"].ToString(),
                                    BranchID = Convert.ToInt32(reader["Branch_ID"])
                                };

                                // After fetching the data, call CFC.getRegionName method
                                srCount.Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, srCount.BranchID);

                                SRCountListRecievedMode.Add(srCount);
                            }
                            SRCountListRecieved = SRCountListRecievedMode.AsEnumerable();
                        }
                    }
                }
                else if (Mode == 7)//Product Type
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_ByProductType", conn);
                        cmd.CommandType = CommandType.StoredProcedure;
                        // Add parameters to the stored procedure
                        cmd.Parameters.Add(new SqlParameter("@CompanyIDs", SqlDbType.NVarChar)).Value = CompanyIDs;
                        cmd.Parameters.Add(new SqlParameter("@BranchIDs", SqlDbType.NVarChar)).Value = BranchIDs;

                        if (FromDate.HasValue)
                            cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = FromDate.Value;
                        else
                            cmd.Parameters.Add(new SqlParameter("@FromDate", SqlDbType.DateTime)).Value = DBNull.Value;

                        if (ToDate.HasValue)
                            cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = ToDate.Value;
                        else
                            cmd.Parameters.Add(new SqlParameter("@ToDate", SqlDbType.DateTime)).Value = DBNull.Value;

                        // Add parameters for UserLangID and GeneralLangID
                        cmd.Parameters.AddWithValue("@UserLangID", UserLangID);
                        cmd.Parameters.AddWithValue("@GeneralLangID", GeneralLangID);

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ServiceRequestCount srCount = new ServiceRequestCount()
                                {
                                    Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["ProductType_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    StatusID = Convert.ToInt32(reader["CallStatus_ID"]),
                                    StatusName = reader["StatusName"].ToString(),
                                    CompanyName = reader["CompanyName"].ToString(),
                                    BranchName = reader["BranchName"].ToString(),
                                    BranchID = Convert.ToInt32(reader["Branch_ID"])
                                };

                                // Call CFC.getRegionName method to populate the Region
                                srCount.Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, srCount.BranchID);

                                SRCountListRecievedMode.Add(srCount);
                            }
                            SRCountListRecieved = SRCountListRecievedMode.AsEnumerable();
                        }
                    }
                }

                SRCountListStatus = (from SRequest in SRCountListRecieved
                                     group SRequest by new { SRequest.Year, SRequest.BranchName } into final
                                     select new ServiceRequestCount()
                                     {
                                         // Type = final.FirstOrDefault().Type,
                                         Count = final.Count(),
                                         Year = final.FirstOrDefault().Year,
                                         StatusIDs = final.Select(a => a.StatusID).ToList(),
                                         CompanyName = final.FirstOrDefault().CompanyName,
                                         Region = final.FirstOrDefault().Region,
                                         BranchName = final.FirstOrDefault().BranchName,
                                         BranchID = final.FirstOrDefault().BranchID
                                     });


                SRCountListClosed = (from SRequest in SRCountListStatus

                                     select new ServiceRequestCount()
                                     {
                                         Year = SRequest.Year,
                                         // Type = SRequest.Type,
                                         StatusName = SRequest.StatusName,
                                         Count = (from sr in SRequest.StatusIDs
                                                  where sr == ClosedID
                                                  select sr
                                                ).Count(),
                                         CompanyName = SRequest.CompanyName,
                                         BranchName = SRequest.BranchName,
                                         Region = SRequest.Region,
                                         BranchID = SRequest.BranchID

                                     });

                FinalResultList = ((from srFinal in SRCountListStatus
                                    join closed in SRCountListClosed on new { srFinal.Year, srFinal.Type, srFinal.BranchName } equals new { closed.Year, closed.Type, closed.BranchName } into CL
                                    from FINCL in CL.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                    select new ServiceRequestCount()
                                    {
                                        ClosedCount = FINCL.Count,
                                        Count = srFinal.Count,
                                        // Type = srFinal.Type,
                                        Year = srFinal.Year,
                                        Region = srFinal.Region,
                                        CompanyName = srFinal.CompanyName,
                                        BranchName = srFinal.BranchName,
                                        BranchID = srFinal.BranchID
                                    }).ToList()).Paginate(page, rows);

                FinalResultSort = FinalResultList.AsQueryable().OrderByField<ServiceRequestCount>(sidx, sord);
                count = SRCountListStatus.Count();

                for (int i = 0; i < FinalResultList.Count; i++)
                {
                    TotalCount += FinalResultList.ElementAt(i).Count;
                    TotalClosedCount += FinalResultList.ElementAt(i).ClosedCount;
                }

                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                jsonobj = new
                {
                    TotalPages = total,
                    PageNo = page,
                    RecordCount = count,
                    TotalCount = TotalCount,
                    TotalClosedCount = TotalClosedCount,
                    rows = FinalResultSort
                };
                // gbl.InsertGPSDetails(Convert.ToInt32(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Company_ID.ToString()), Convert.ToInt32(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Branch), HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.User_ID, Common.GetObjectID("HelpDesk_Tr_CaseDistributionChart"), 0, 0, 0, "Generated- Ticket Distribution Chart", false, Convert.ToInt32(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.MenuID), Convert.ToDateTime(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.LoggedINDateTime));
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonobj);
        }

        #endregion



        #region ::: Select Service Request Count Month wise Uday Kumar J B 20-11-2024:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        /// 

        public static IActionResult SelectMonthReport(HelpDesk_Tr_CaseDistributionChartSelectYearReportList HelpDesk_Tr_CaseDistributionChartSelectYearReportobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonobj = default(dynamic);
            int count = 0;
            int total = 0;
            IEnumerable<ServiceRequestCount> SRCountList = null;

            int TotalCount = 0;
            IEnumerable<ServiceRequestCount> SRCountListClosed = null;
            IEnumerable<ServiceRequestCount> SRCountListClosedReport = null;
            IEnumerable<ServiceRequestCount> SRCountListStatus = null;
            IEnumerable<ServiceRequestCount> SRCountListStatusReport = null;
            List<ServiceRequestCount> FinalResultList = new List<ServiceRequestCount>();
            IQueryable<ServiceRequestCount> FinalResultSort = null;
            List<ServiceRequestCount> FinalResultListReport = new List<ServiceRequestCount>();
            ServiceRequestCount FinalResult = new ServiceRequestCount();
            int UserLangID = Convert.ToInt32(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserLanguageID);
            string GenLangCode = string.Empty;
            string UserLangCode = string.Empty;
            int TotalClosedCount = 0;
            try
            {
                GetStatusIDs(connString, LogException);
                int userid = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.User_ID;
                GenLangCode = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.GeneralLanguageCode.ToString();
                UserLangCode = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserLanguageCode.ToString();

                string CompanyIDs = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.CompanyIDs.TrimEnd(new char[] { ',' });
                string BranchIDs = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.BranchIDs.TrimEnd(new char[] { ',' });
                string frmdate = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.FromDate.ToString();
                string todate = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.ToDate.ToString();
                SelectionCriteria Selection = JObject.Parse(Uri.UnescapeDataString(Uri.UnescapeDataString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Data))).ToObject<SelectionCriteria>();

                foreach (var s in Selection.SelectedIDs)
                {
                    s.Name = Common.DecryptString(s.Name);
                }


                List<ServiceRequestCount> SRCountListMode = new List<ServiceRequestCount>();

                if (HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.SelectedCriteria == 1)//Issue Area
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsIssueArea", conn);
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters for the stored procedure
                        cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                        cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1)); // Add 1 day to ToDate
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                        cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Year);


                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {


                            while (reader.Read())
                            {
                                ServiceRequestCount SRCount = new ServiceRequestCount
                                {
                                    Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["IssueArea_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    Month = Convert.ToInt32(reader["Month"]),
                                    StatusID = Convert.ToInt32(reader["StatusID"]),
                                    StatusName = reader["StatusName"].ToString()
                                };

                                SRCountListMode.Add(SRCount);
                            }

                            SRCountList = SRCountListMode.AsEnumerable();
                        }
                    }

                }
                else if (HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.SelectedCriteria == 2)//Call Nature
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByCallMode", conn);
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters for the stored procedure
                        cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                        cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1)); // Add 1 day to ToDate
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                        cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Year);


                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {

                            while (reader.Read())
                            {
                                ServiceRequestCount SRCount = new ServiceRequestCount
                                {
                                    Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["CallMode_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    Month = Convert.ToInt32(reader["Month"]),
                                    StatusID = Convert.ToInt32(reader["StatusID"]),
                                    StatusName = reader["StatusName"].ToString()
                                };

                                SRCountListMode.Add(SRCount);
                            }

                            SRCountList = SRCountListMode.AsEnumerable();
                        }
                    }


                }
                else if (HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.SelectedCriteria == 8)//Dealer
                {
                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByParty", conn);
                            cmd.CommandType = CommandType.StoredProcedure;

                            // Add parameters for the stored procedure
                            cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Year);
                            cmd.Parameters.AddWithValue("@IsDealer", true); // Setting IsDealer to true as per the LINQ query
                            cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1)); // Add 1 day to ToDate
                            cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                            cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {

                                while (reader.Read())
                                {
                                    ServiceRequestCount SRCount = new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["Party_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString()
                                    };

                                    SRCountListMode.Add(SRCount);
                                }

                                SRCountList = SRCountListMode.AsEnumerable();
                            }
                        }

                    }
                    else
                    {

                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByBranch", conn);
                            cmd.CommandType = CommandType.StoredProcedure;

                            // Add parameters for the stored procedure
                            cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Year);
                            cmd.Parameters.AddWithValue("@IsDealer", false); // Setting IsDealer to false as per the LINQ query
                            cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1)); // Add 1 day to ToDate
                            cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                            cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount SRCount = new ServiceRequestCount
                                    {
                                        Type = reader["Type"].ToString(),
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString()
                                    };

                                    SRCountListMode.Add(SRCount);
                                }

                                SRCountList = SRCountListMode.AsEnumerable();
                            }
                        }



                    }
                }
                else if (HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.SelectedCriteria == 3)//Party
                {
                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsforParty", conn);
                            cmd.CommandType = CommandType.StoredProcedure;

                            // Add parameters for the stored procedure
                            cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Year);
                            cmd.Parameters.AddWithValue("@IsDealer", false); // Set IsDealer to false based on LINQ query
                            cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1)); // Add 1 day to ToDate
                            cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                            cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount SRCount = new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["Party_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString()
                                    };

                                    SRCountListMode.Add(SRCount);
                                }

                                SRCountList = SRCountListMode.AsEnumerable();
                            }
                        }

                    }
                    else
                    {
                        if (GenLangCode == UserLangCode)
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByPartyWithStatus", conn);
                                cmd.CommandType = CommandType.StoredProcedure;

                                // Add parameters for the stored procedure
                                cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Year);
                                cmd.Parameters.AddWithValue("@IsDealer", false); // Set IsDealer to false as per your LINQ query
                                cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1)); // Add 1 day to ToDate
                                cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                                cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {

                                    while (reader.Read())
                                    {
                                        ServiceRequestCount SRCount = new ServiceRequestCount
                                        {
                                            Type = reader["Type"].ToString(),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString()
                                        };

                                        SRCountListMode.Add(SRCount);
                                    }

                                    SRCountList = SRCountListMode.AsEnumerable();
                                }
                            }


                        }
                        else
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByPartyLocaleWithStatus", conn);
                                cmd.CommandType = CommandType.StoredProcedure;

                                // Add parameters for the stored procedure
                                cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Year);
                                cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1)); // Add 1 day to ToDate
                                cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                                cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                                cmd.Parameters.AddWithValue("@UserLangID", UserLangID);

                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {

                                    while (reader.Read())
                                    {
                                        ServiceRequestCount SRCount = new ServiceRequestCount
                                        {
                                            Type = reader["Type"].ToString(),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString()
                                        };

                                        SRCountListMode.Add(SRCount);
                                    }

                                    SRCountList = SRCountListMode.AsEnumerable();
                                }
                            }

                        }
                    }
                }

                else if (HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.SelectedCriteria == 4)//Priority
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("Sp_AMERP_HelpDesk_GetServiceRequestCountsByPriorityWithStatus", conn);
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters for the stored procedure
                        cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Year);
                        cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                        cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1)); // Add 1 day to ToDate
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {

                            while (reader.Read())
                            {
                                ServiceRequestCount SRCount = new ServiceRequestCount
                                {
                                    Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["CallPriority_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    Month = Convert.ToInt32(reader["Month"]),
                                    StatusID = Convert.ToInt32(reader["StatusID"]),
                                    StatusName = reader["StatusName"].ToString()
                                };

                                SRCountListMode.Add(SRCount);
                            }

                            SRCountList = SRCountListMode.AsEnumerable();
                        }
                    }


                }
                else if (HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.SelectedCriteria == 5)//Model
                {
                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            SqlCommand cmd = new SqlCommand("Sp_AMERP_HelpDesk_GetServiceRequestCountsByModelWithStatus", conn);
                            cmd.CommandType = CommandType.StoredProcedure;

                            // Add parameters for the stored procedure
                            cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Year);
                            cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1)); // Add 1 day to ToDate
                            cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                            cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {

                                while (reader.Read())
                                {
                                    ServiceRequestCount SRCount = new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["Model_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString()
                                    };

                                    SRCountListMode.Add(SRCount);
                                }

                                SRCountList = SRCountListMode.AsEnumerable();
                            }
                        }

                    }
                    else
                    {

                        if (GenLangCode == UserLangCode)
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                SqlCommand cmd = new SqlCommand("Sp_AMERP_HelpDesk_GetServiceRequestCountsByModelWithStatusLocal", conn);
                                cmd.CommandType = CommandType.StoredProcedure;

                                // Add parameters for the stored procedure
                                cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Year);
                                cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1)); // Add 1 day to ToDate
                                cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                                cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);


                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {

                                    while (reader.Read())
                                    {
                                        ServiceRequestCount SRCount = new ServiceRequestCount
                                        {
                                            Type = reader["Type"].ToString(),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString()
                                        };

                                        SRCountListMode.Add(SRCount);
                                    }

                                    SRCountList = SRCountListMode.AsEnumerable();
                                }
                            }


                        }
                        else
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                SqlCommand cmd = new SqlCommand("Sp_AMERP_HelpDesk_GetServiceRequestCountsByModelLocaleWithStatus", conn);
                                cmd.CommandType = CommandType.StoredProcedure;

                                // Add the @Year parameter to the command
                                cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Year);
                                cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1)); // Add 1 day to ToDate
                                cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                                cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                                cmd.Parameters.AddWithValue("@UserLangID", UserLangID);

                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {

                                    while (reader.Read())
                                    {
                                        ServiceRequestCount SRCount = new ServiceRequestCount
                                        {
                                            Type = reader["Type"].ToString(),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString()
                                        };

                                        SRCountListMode.Add(SRCount);
                                    }

                                    SRCountList = SRCountListMode.AsEnumerable();
                                }
                            }

                        }
                    }

                }

                else if (HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.SelectedCriteria == 6)//Brand
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("Sp_AMERP_HelpDesk_GetServiceRequestCountsByBrandWithStatus", conn);
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add the @Year parameter to the command
                        cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Year);
                        cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                        cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1)); // Add 1 day to ToDate
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {

                            while (reader.Read())
                            {
                                ServiceRequestCount SRCount = new ServiceRequestCount
                                {
                                    Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["Brand_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    Month = Convert.ToInt32(reader["Month"]),
                                    StatusID = Convert.ToInt32(reader["StatusID"]),
                                    StatusName = reader["StatusName"].ToString()
                                };

                                SRCountListMode.Add(SRCount);
                            }

                            SRCountList = SRCountListMode.AsEnumerable();
                        }
                    }


                }

                else if (HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.SelectedCriteria == 7)//Product Type
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("Sp_AMERP_HelpDesk_GetServiceRequestCountsByProductTypeWithStatus", conn);
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add the @Year parameter to the command
                        cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Year);
                        cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                        cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1)); // Add 1 day to ToDate
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {

                            while (reader.Read())
                            {
                                ServiceRequestCount SRCount = new ServiceRequestCount
                                {
                                    Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["ProductType_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    Month = Convert.ToInt32(reader["Month"]),
                                    StatusID = Convert.ToInt32(reader["StatusID"]),
                                    StatusName = reader["StatusName"].ToString()
                                };

                                SRCountListMode.Add(SRCount);
                            }

                            SRCountList = SRCountListMode.AsEnumerable();
                        }
                    }
                }


                SRCountListStatus = (from SRequest in SRCountList
                                     group SRequest by new { SRequest.Year, SRequest.Month } into final
                                     select new ServiceRequestCount()
                                     {
                                         Type = final.FirstOrDefault().Type,
                                         Count = final.Count(),
                                         Year = final.FirstOrDefault().Year,
                                         StatusIDs = final.Select(a => a.StatusID).ToList(),
                                         Month = final.FirstOrDefault().Month,
                                     });

                SRCountListStatusReport = (from SRequest in SRCountList
                                           group SRequest by new { SRequest.Year, SRequest.Type } into final
                                           where final.FirstOrDefault().Year == HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Year
                                           select new ServiceRequestCount()
                                           {
                                               Type = final.FirstOrDefault().Type,
                                               Count = final.Count(),
                                               Year = final.FirstOrDefault().Year,
                                               StatusIDs = final.Select(a => a.StatusID).ToList()
                                           });


                SRCountListClosed = (from SRequest in SRCountListStatus
                                     select new ServiceRequestCount()
                                     {
                                         Year = SRequest.Year,
                                         Type = SRequest.Type,
                                         StatusName = SRequest.StatusName,
                                         Month = SRequest.Month,
                                         Count = (from sr in SRequest.StatusIDs
                                                  where sr == ClosedID
                                                  select sr
                                                ).Count()
                                     });

                SRCountListClosedReport = (from SRequest in SRCountListStatusReport

                                           select new ServiceRequestCount()
                                           {
                                               Year = SRequest.Year,
                                               Type = SRequest.Type,
                                               StatusName = SRequest.StatusName,
                                               Count = (from sr in SRequest.StatusIDs
                                                        where sr == ClosedID
                                                        select sr
                                                      ).Count()
                                           });

                FinalResultList = ((from srFinal in SRCountListStatus
                                    join closed in SRCountListClosed on new { srFinal.Year, srFinal.Month } equals new { closed.Year, closed.Month } into CL
                                    from FINCL in CL.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                    select new ServiceRequestCount()
                                    {
                                        ClosedCount = FINCL.Count,
                                        Count = srFinal.Count,
                                        Type = srFinal.Type,
                                        Year = srFinal.Year,
                                        Month = srFinal.Month,
                                        MonthName = GetMonthName(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj, srFinal.Month),
                                    }).ToList()).Paginate(page, rows);

                FinalResultListReport = (from srFinal in SRCountListStatusReport
                                         join closed in SRCountListClosedReport on new { srFinal.Year, srFinal.Type } equals new { closed.Year, closed.Type } into CL
                                         from FINCL in CL.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                         select new ServiceRequestCount()
                                         {
                                             ClosedCount = FINCL.Count,
                                             Count = srFinal.Count,
                                             Type = srFinal.Type,
                                             Year = srFinal.Year
                                         }).ToList();

                FinalResultSort = FinalResultList.AsQueryable().OrderByField<ServiceRequestCount>(sidx, sord);
                count = SRCountListStatus.Count();

                for (int i = 0; i < FinalResultList.Count; i++)
                {
                    TotalCount += FinalResultList.ElementAt(i).Count;
                    TotalClosedCount += FinalResultList.ElementAt(i).ClosedCount;
                }

                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                jsonobj = new
                {
                    TotalPages = total,
                    PageNo = page,
                    RecordCount = count,
                    TotalCount = TotalCount,
                    TotalClosedCount = TotalClosedCount,
                    ChildMonthData = FinalResultSort,
                    ReportData = FinalResultListReport,
                    Year = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Year
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonobj);
        }

        #endregion



        #region ::: GetMonthName Uday Kumar J B 22-11-2024:::
        /// <summary>
        /// To Select Month Name based on Culture
        /// </summary>
        public static string GetMonthName(HelpDesk_Tr_CaseDistributionChartSelectYearReportList HelpDesk_Tr_CaseDistributionChartSelectYearReportobj, int ID)
        {
            string MonthName = string.Empty;
            try
            {
                switch (ID)
                {
                    case 1:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "January").ToString();
                        break;
                    case 2:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "February").ToString();
                        break;
                    case 3:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "March").ToString();
                        break;
                    case 4:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "April").ToString();
                        break;
                    case 5:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "May").ToString();
                        break;
                    case 6:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "June").ToString();
                        break;
                    case 7:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "July").ToString();
                        break;
                    case 8:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "August").ToString();
                        break;
                    case 9:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "September").ToString();
                        break;
                    case 10:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "October").ToString();
                        break;
                    case 11:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "November").ToString();
                        break;
                    case 12:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "December").ToString();
                        break;
                    default:
                        MonthName = string.Empty;
                        break;
                }
            }
            catch (Exception ex)
            {

            }
            return MonthName;
        }
        #endregion





        #region ::: Select Service Request Count Day wise Uday Kumar J B 21-11-2024:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        /// 

        public static IActionResult SelectDayReport(HelpDesk_Tr_CaseDistributionChartSelectYearReportList HelpDesk_Tr_CaseDistributionChartSelectYearReportobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonobj = default(dynamic);
            int count = 0;
            int total = 0;
            IEnumerable<ServiceRequestCount> SRCountList = null;
            int userid = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.User_ID;
            int TotalCount = 0;
            int TotalClosedCount = 0;
            IEnumerable<ServiceRequestCount> SRCountListClosedReport = null;
            IEnumerable<ServiceRequestCount> SRCountListStatusReport = null;
            List<ServiceRequestCount> FinalResultListReport = new List<ServiceRequestCount>();
            IEnumerable<ServiceRequestCount> SRCountListClosed = null;
            List<ServiceRequestCount> FinalResultList = new List<ServiceRequestCount>();
            IQueryable<ServiceRequestCount> FinalResultSort = null;
            IEnumerable<ServiceRequestCount> SRCountListStatus = null;
            string GenLangCode = string.Empty;
            string UserLangCode = string.Empty;

            try
            {
                GetStatusIDs(connString, LogException);
                GenLangCode = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.GeneralLanguageCode.ToString();
                UserLangCode = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserLanguageCode.ToString();
                string frmdate = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.FromDate.ToString();
                string todate = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.ToDate.ToString();
                int Year = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Year;
                int Month = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Month;
                string CompanyIDs = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.CompanyIDs.TrimEnd(new char[] { ',' });
                string BranchIDs = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.BranchIDs.TrimEnd(new char[] { ',' });
                int UserLangID = Convert.ToInt32(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserLanguageID);
                SelectionCriteria Selection = JObject.Parse(Uri.UnescapeDataString(Uri.UnescapeDataString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Data))).ToObject<SelectionCriteria>();
                string startDate = string.Empty;
                string endDate = string.Empty;
                string BranchName = string.Empty;
                string SelectionCriteria = string.Empty;
                foreach (var s in Selection.SelectedIDs)
                {
                    s.Name = Common.DecryptString(s.Name);
                }
                DateTime? fromDate = string.IsNullOrEmpty(frmdate) ? (DateTime?)null : Convert.ToDateTime(frmdate);
                DateTime? toDate = string.IsNullOrEmpty(todate) ? (DateTime?)null : Convert.ToDateTime(todate);

                List<ServiceRequestCount> SRCountListMode = new List<ServiceRequestCount>();

                if (HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.SelectedCriteria == 1)//Issue Area
                {
                    SelectionCriteria = "Issue Area";

                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountByIssueArea", conn);
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@Year", Year);
                        cmd.Parameters.AddWithValue("@Month", Month);
                        cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);


                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ServiceRequestCount SRcount = new ServiceRequestCount
                                {
                                    Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["IssueArea_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    Month = Convert.ToInt32(reader["Month"]),
                                    Date = reader["Date"].ToString(),
                                    StatusID = Convert.ToInt32(reader["StatusID"]),
                                    StatusName = reader["StatusName"].ToString(),
                                };

                                SRCountListMode.Add(SRcount);
                            }
                            SRCountList = SRCountListMode.AsEnumerable();
                        }
                    }


                }

                else if (HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.SelectedCriteria == 2)//Call Nature
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountByCallMode", conn);
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@Year", Year);
                        cmd.Parameters.AddWithValue("@Month", Month);
                        cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ServiceRequestCount SRcount = new ServiceRequestCount
                                {
                                    Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["CallMode_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    Month = Convert.ToInt32(reader["Month"]),
                                    Date = reader["Date"].ToString(),
                                    StatusID = Convert.ToInt32(reader["StatusID"]),
                                    StatusName = reader["StatusName"].ToString(),
                                };

                                SRCountListMode.Add(SRcount);
                            }
                            SRCountList = SRCountListMode.AsEnumerable();
                        }
                    }

                }
                else if (HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.SelectedCriteria == 8)//Dealer
                {
                    SelectionCriteria = "Dealer";

                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountByParty", conn);
                            cmd.CommandType = CommandType.StoredProcedure;

                            // Add parameters
                            cmd.Parameters.AddWithValue("@Year", Year);
                            cmd.Parameters.AddWithValue("@Month", Month);
                            cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                            cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount SRcount = new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["Party_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        Date = reader["Date"].ToString(),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString(),
                                    };

                                    SRCountListMode.Add(SRcount);
                                }
                                SRCountList = SRCountListMode.AsEnumerable();
                            }
                        }
                    }
                    else
                    {

                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountByBranch", conn);
                            cmd.CommandType = CommandType.StoredProcedure;

                            // Add parameters
                            cmd.Parameters.AddWithValue("@Year", Year);
                            cmd.Parameters.AddWithValue("@Month", Month);
                            cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                            cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                            cmd.Parameters.AddWithValue("@IsDealer", true);

                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount SRcount = new ServiceRequestCount
                                    {
                                        Type = reader["Type"].ToString(),
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        Date = reader["Date"].ToString(),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString(),
                                    };

                                    SRCountListMode.Add(SRcount);
                                }
                                SRCountList = SRCountListMode.AsEnumerable();
                            }
                        }
                    }
                }

                else if (HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.SelectedCriteria == 3)//Party
                {
                    SelectionCriteria = "Party";

                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountByPartyDay", conn);
                            cmd.CommandType = CommandType.StoredProcedure;

                            // Add parameters
                            cmd.Parameters.AddWithValue("@Year", Year);
                            cmd.Parameters.AddWithValue("@Month", Month);
                            cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                            cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                            cmd.Parameters.AddWithValue("@IsDealer", false);

                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount SRcount = new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["Party_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        Date = reader["Date"].ToString(),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString(),
                                    };

                                    SRCountListMode.Add(SRcount);
                                }
                                SRCountList = SRCountListMode.AsEnumerable();
                            }
                        }
                    }
                    else
                    {
                        if (GenLangCode == UserLangCode)
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountByPartyName", conn);
                                cmd.CommandType = CommandType.StoredProcedure;

                                // Add parameters
                                cmd.Parameters.AddWithValue("@Year", Year);
                                cmd.Parameters.AddWithValue("@Month", Month);
                                cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                                cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                                cmd.Parameters.AddWithValue("@IsDealer", false);

                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount SRcount = new ServiceRequestCount
                                        {
                                            Type = reader["Type"].ToString(),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            Date = reader["Date"].ToString(),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString(),
                                        };

                                        SRCountListMode.Add(SRcount);
                                    }
                                    SRCountList = SRCountListMode.AsEnumerable();
                                }
                            }
                        }
                        else
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountByPartyLocale", conn);
                                cmd.CommandType = CommandType.StoredProcedure;

                                // Add parameters
                                cmd.Parameters.AddWithValue("@Year", Year);
                                cmd.Parameters.AddWithValue("@Month", Month);
                                cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                                cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount SRcount = new ServiceRequestCount
                                        {
                                            Type = reader["Type"].ToString(),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            Date = reader["Date"].ToString(),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString(),
                                        };

                                        SRCountListMode.Add(SRcount);
                                    }
                                    SRCountList = SRCountListMode.AsEnumerable();
                                }
                            }
                        }
                    }

                }

                else if (HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.SelectedCriteria == 4)//Priority
                {
                    SelectionCriteria = "Priority";

                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountByCallPriority", conn);
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@Year", Year);
                        cmd.Parameters.AddWithValue("@Month", Month);
                        cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ServiceRequestCount SRcount = new ServiceRequestCount
                                {
                                    Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["CallPriority_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    Month = Convert.ToInt32(reader["Month"]),
                                    Date = reader["Date"].ToString(),
                                    StatusID = Convert.ToInt32(reader["StatusID"]),
                                    StatusName = reader["StatusName"].ToString(),
                                };

                                SRCountListMode.Add(SRcount);
                            }
                            SRCountList = SRCountListMode.AsEnumerable();
                        }
                    }
                }

                else if (HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.SelectedCriteria == 5)//Model
                {
                    SelectionCriteria = "Model";

                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountByModel", conn);
                            cmd.CommandType = CommandType.StoredProcedure;

                            // Add parameters
                            cmd.Parameters.AddWithValue("@Year", Year);
                            cmd.Parameters.AddWithValue("@Month", Month);
                            cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                            cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount SRcount = new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["Model_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        Date = reader["Date"].ToString(),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString(),
                                    };

                                    SRCountListMode.Add(SRcount);
                                }
                                SRCountList = SRCountListMode.AsEnumerable();
                            }
                        }
                    }
                    else
                    {

                        if (GenLangCode == UserLangCode)
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountByModelName", conn);
                                cmd.CommandType = CommandType.StoredProcedure;

                                // Add parameters
                                cmd.Parameters.AddWithValue("@Year", Year);
                                cmd.Parameters.AddWithValue("@Month", Month);
                                cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                                cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount SRcount = new ServiceRequestCount
                                        {
                                            Type = reader["Type"].ToString(),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            Date = reader["Date"].ToString(),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString(),
                                        };

                                        SRCountListMode.Add(SRcount);
                                    }
                                    SRCountList = SRCountListMode.AsEnumerable();
                                }
                            }
                        }
                        else
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountByModelLocale", conn);
                                cmd.CommandType = CommandType.StoredProcedure;

                                // Add parameters
                                cmd.Parameters.AddWithValue("@Year", Year);
                                cmd.Parameters.AddWithValue("@Month", Month);
                                cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                                cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                                cmd.Parameters.AddWithValue("@UserLangID", UserLangID);

                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount SRcount = new ServiceRequestCount
                                        {
                                            Type = reader["Type"].ToString(),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            Date = reader["Date"].ToString(),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString(),
                                        };

                                        SRCountListMode.Add(SRcount);
                                    }
                                    SRCountList = SRCountListMode.AsEnumerable();
                                }
                            }
                        }
                    }

                }

                else if (HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.SelectedCriteria == 6)//Brand
                {
                    SelectionCriteria = "Brand";
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountByBrand", conn);
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@Year", Year);
                        cmd.Parameters.AddWithValue("@Month", Month);
                        cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ServiceRequestCount SRcount = new ServiceRequestCount
                                {
                                    Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["Brand_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    Month = Convert.ToInt32(reader["Month"]),
                                    Date = reader["Date"].ToString(),
                                    StatusID = Convert.ToInt32(reader["StatusID"]),
                                    StatusName = reader["StatusName"].ToString(),
                                };

                                SRCountListMode.Add(SRcount);
                            }
                            SRCountList = SRCountListMode.AsEnumerable();
                        }
                    }

                }


                else if (HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.SelectedCriteria == 7)//Product Type
                {
                    SelectionCriteria = "Product Type";
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountByProductType", conn);
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@Year", Year);
                        cmd.Parameters.AddWithValue("@Month", Month);
                        cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ServiceRequestCount SRcount = new ServiceRequestCount
                                {
                                    Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["ProductType_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    Month = Convert.ToInt32(reader["Month"]),
                                    Date = reader["Date"].ToString(),
                                    StatusID = Convert.ToInt32(reader["StatusID"]),
                                    StatusName = reader["StatusName"].ToString(),
                                };

                                SRCountListMode.Add(SRcount);
                            }
                            SRCountList = SRCountListMode.AsEnumerable();
                        }
                    }

                }

                SRCountListStatus = (from SRequest in SRCountList
                                     group SRequest by new { SRequest.Year, SRequest.Month, SRequest.Date } into final
                                     select new ServiceRequestCount()
                                     {
                                         Type = final.FirstOrDefault().Type,
                                         Count = final.Count(),
                                         Year = final.FirstOrDefault().Year,
                                         StatusIDs = final.Select(a => a.StatusID).ToList(),
                                         Month = final.FirstOrDefault().Month,
                                         Date = final.FirstOrDefault().Date
                                     });

                SRCountListStatusReport = (from SRequest in SRCountList
                                           group SRequest by new { SRequest.Year, SRequest.Month, SRequest.Type } into final
                                           where final.FirstOrDefault().Year == Year && final.FirstOrDefault().Month == Month
                                           select new ServiceRequestCount()
                                           {
                                               Type = final.FirstOrDefault().Type,
                                               Count = final.Count(),
                                               Year = final.FirstOrDefault().Year,
                                               StatusIDs = final.Select(a => a.StatusID).ToList(),
                                               Month = final.FirstOrDefault().Month,
                                           });


                SRCountListClosed = (from SRequest in SRCountListStatus
                                     select new ServiceRequestCount()
                                     {
                                         Year = SRequest.Year,
                                         Type = SRequest.Type,
                                         StatusName = SRequest.StatusName,
                                         Month = SRequest.Month,
                                         Date = SRequest.Date,
                                         Count = (from sr in SRequest.StatusIDs
                                                  where sr == ClosedID
                                                  select sr
                                                ).Count()
                                     });

                SRCountListClosedReport = (from SRequest in SRCountListStatusReport

                                           select new ServiceRequestCount()
                                           {
                                               Year = SRequest.Year,
                                               Month = SRequest.Month,
                                               Type = SRequest.Type,
                                               StatusName = SRequest.StatusName,
                                               Count = (from sr in SRequest.StatusIDs
                                                        where sr == ClosedID
                                                        select sr
                                                      ).Count()
                                           });

                FinalResultList = ((from srFinal in SRCountListStatus
                                    join closed in SRCountListClosed on new { srFinal.Year, srFinal.Month, srFinal.Date } equals new { closed.Year, closed.Month, closed.Date } into CL
                                    from FINCL in CL.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                    select new ServiceRequestCount()
                                    {
                                        ClosedCount = FINCL.Count,
                                        Count = srFinal.Count,
                                        Type = srFinal.Type,
                                        Year = srFinal.Year,
                                        Month = srFinal.Month,
                                        Date = Convert.ToDateTime(srFinal.Date).ToString("dd-MMM-yyyy")
                                    }).ToList()).Paginate(page, rows);


                FinalResultListReport = (from srFinal in SRCountListStatusReport
                                         join closed in SRCountListClosedReport on new { srFinal.Year, srFinal.Month, srFinal.Type } equals new { closed.Year, closed.Month, closed.Type } into CL
                                         from FINCL in CL.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                         select new ServiceRequestCount()
                                         {
                                             ClosedCount = FINCL.Count,
                                             Count = srFinal.Count,
                                             Type = srFinal.Type,
                                             Year = srFinal.Year
                                         }).ToList();
                FinalResultSort = FinalResultList.AsQueryable().OrderByField<ServiceRequestCount>(sidx, sord);

                count = SRCountListStatus.Count();

                for (int i = 0; i < FinalResultList.Count; i++)
                {
                    TotalCount += FinalResultList.ElementAt(i).Count;
                    TotalClosedCount += FinalResultList.ElementAt(i).ClosedCount;
                }

                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                jsonobj = new
                {
                    total = total,
                    page = page,
                    records = count,
                    TotalCount = TotalCount,
                    TotalClosedCount = TotalClosedCount,
                    ChildDayData = FinalResultSort,
                    ReportData = FinalResultListReport,
                    Year = Year,
                    Month = GetMonthName(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj, Month)
                };

                //FinalResultList;

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonobj);
        }

        #endregion




        #region ::: Select Service Request Report Day wise Uday Kumar J B 21-11-2024:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        /// 

        public static IActionResult SelectDaywiseReport(HelpDesk_Tr_CaseDistributionChartSelectYearReportList HelpDesk_Tr_CaseDistributionChartSelectYearReportobj, string connString, int LogException)
        {
            var jsonobj = default(dynamic);
            IEnumerable<ServiceRequestCount> SRCountList = null;
            int userid = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.User_ID;
            IEnumerable<ServiceRequestCount> SRCountListClosedReport = null;
            IEnumerable<ServiceRequestCount> SRCountListStatusReport = null;
            IEnumerable<ServiceRequestCount> FinalResultListReport = null;
            string GenLangCode = string.Empty;
            string UserLangCode = string.Empty;

            try
            {
                GetStatusIDs(connString, LogException);
                GenLangCode = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.GeneralLanguageCode.ToString();
                UserLangCode = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserLanguageCode.ToString();
                string frmdate = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.FromDate.ToString();
                string todate = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.ToDate.ToString();
                string CompanyIDs = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.CompanyIDs.TrimEnd(new char[] { ',' });
                string BranchIDs = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.BranchIDs.TrimEnd(new char[] { ',' });
                int SelectedCriteria = Convert.ToInt32(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.SelectedCriteria);
                int Year = Convert.ToInt32(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Year);
                int Month = Convert.ToInt32(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Month);
                string day = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.day;
                int UserLangID = Convert.ToInt32(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserLanguageID);
                SelectionCriteria Selection = JObject.Parse(Uri.UnescapeDataString(Uri.UnescapeDataString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Data))).ToObject<SelectionCriteria>();
                string startDate = string.Empty;
                string endDate = string.Empty;
                string SelectionCriteria = string.Empty;
                foreach (var s in Selection.SelectedIDs)
                {
                    s.Name = Common.DecryptString(s.Name);
                }

                List<ServiceRequestCount> SRCountListMode = new List<ServiceRequestCount>();


                if (SelectedCriteria == 1)//Issue Area
                {
                    SelectionCriteria = "Issue Area";

                    using (var connection = new SqlConnection(connString))
                    {
                        connection.Open();

                        using (var command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsday", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;

                            command.Parameters.AddWithValue("@Year", Year);
                            command.Parameters.AddWithValue("@Month", Month);
                            command.Parameters.AddWithValue("@Day", Convert.ToDateTime(day));
                            command.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                            command.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                            command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                            using (var reader = command.ExecuteReader())
                            {

                                while (reader.Read())
                                {
                                    SRCountListMode.Add(new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["IssueArea_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        Date = reader["Date"].ToString(),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString()
                                    });
                                }
                                SRCountList = SRCountListMode.AsEnumerable();
                            }
                        }
                    }
                }

                else if (SelectedCriteria == 2)//Call Nature
                {
                    using (var connection = new SqlConnection(connString))
                    {
                        connection.Open();

                        using (var command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByCallModeday", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;

                            command.Parameters.AddWithValue("@Year", Year);
                            command.Parameters.AddWithValue("@Month", Month);
                            command.Parameters.AddWithValue("@Day", Convert.ToDateTime(day));
                            command.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                            command.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                            command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                            using (var reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    SRCountListMode.Add(new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["CallMode_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        Date = reader["Date"].ToString(),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString()
                                    });
                                }
                                SRCountList = SRCountListMode.AsEnumerable();
                            }
                        }
                    }


                }

                else if (SelectedCriteria == 8)//Dealer
                {
                    SelectionCriteria = "Dealer";

                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (var connection = new SqlConnection(connString))
                        {
                            connection.Open();

                            using (var command = new SqlCommand("SP_GetServiceRequestCountsByParty", connection))
                            {
                                command.CommandType = CommandType.StoredProcedure;

                                command.Parameters.AddWithValue("@Year", Year);
                                command.Parameters.AddWithValue("@Month", Month);
                                command.Parameters.AddWithValue("@Day", Convert.ToDateTime(day));
                                command.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                                command.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                                command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));


                                using (var reader = command.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        SRCountListMode.Add(new ServiceRequestCount
                                        {
                                            Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["Party_ID"].ToString())?.Name ?? "No Name",
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            Date = reader["Date"].ToString(),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString()
                                        });
                                    }
                                    SRCountList = SRCountListMode.AsEnumerable();
                                }
                            }
                        }
                    }
                    else
                    {

                        if (GenLangCode == UserLangCode)
                        {
                            using (var connection = new SqlConnection(connString))
                            {
                                connection.Open();

                                using (var command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsWithBranchday", connection))
                                {
                                    command.CommandType = CommandType.StoredProcedure;

                                    command.Parameters.AddWithValue("@Year", Year);
                                    command.Parameters.AddWithValue("@Month", Month);
                                    command.Parameters.AddWithValue("@Day", Convert.ToDateTime(day));
                                    command.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                                    command.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                                    command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                    command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                                    using (var reader = command.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            SRCountListMode.Add(new ServiceRequestCount
                                            {
                                                Type = reader["Type"].ToString(),  // Branch_Name or 'N/A'
                                                Year = Convert.ToInt32(reader["Year"]),
                                                Month = Convert.ToInt32(reader["Month"]),
                                                Date = reader["Date"].ToString(),
                                                StatusID = Convert.ToInt32(reader["StatusID"]),
                                                StatusName = reader["StatusName"].ToString()
                                            });
                                        }
                                        SRCountList = SRCountListMode.AsEnumerable();
                                    }
                                }
                            }
                        }
                    }
                }
                else if (SelectedCriteria == 3)//Party
                {
                    SelectionCriteria = "Party";

                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (var connection = new SqlConnection(connString))
                        {
                            connection.Open();

                            using (var command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsWithPartyAndStatusday", connection))
                            {
                                command.CommandType = CommandType.StoredProcedure;

                                command.Parameters.AddWithValue("@Year", Year);
                                command.Parameters.AddWithValue("@Month", Month);
                                command.Parameters.AddWithValue("@Day", Convert.ToDateTime(day));
                                command.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                                command.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                                command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                                using (var reader = command.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        SRCountListMode.Add(new ServiceRequestCount
                                        {
                                            Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["Party_ID"].ToString())?.Name ?? "No Name",  // Party Name from the selected IDs
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            Date = reader["Date"].ToString(),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString()
                                        });
                                    }
                                    SRCountList = SRCountListMode.AsEnumerable();
                                }
                            }
                        }

                    }
                    else
                    {
                        if (GenLangCode == UserLangCode)
                        {
                            using (var connection = new SqlConnection(connString))
                            {
                                connection.Open();

                                using (var command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsWithPartyBranchAndStatus", connection))
                                {
                                    command.CommandType = CommandType.StoredProcedure;

                                    command.Parameters.AddWithValue("@Year", Year);
                                    command.Parameters.AddWithValue("@Month", Month);
                                    command.Parameters.AddWithValue("@Day", Convert.ToDateTime(day));
                                    command.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                                    command.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                                    command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                    command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                                    using (var reader = command.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            SRCountListMode.Add(new ServiceRequestCount
                                            {
                                                Type = reader["Type"].ToString(),
                                                Year = Convert.ToInt32(reader["Year"]),
                                                Month = Convert.ToInt32(reader["Month"]),
                                                Date = reader["Date"].ToString(),
                                                StatusID = Convert.ToInt32(reader["StatusID"]),
                                                StatusName = reader["StatusName"].ToString()
                                            });
                                        }
                                        SRCountList = SRCountListMode.AsEnumerable();
                                    }
                                }
                            }
                        }
                        else
                        {
                            using (var connection = new SqlConnection(connString))
                            {
                                connection.Open();

                                using (var command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsWithPartyLocaleAndStatus", connection))
                                {
                                    command.CommandType = CommandType.StoredProcedure;

                                    command.Parameters.AddWithValue("@Year", Year);
                                    command.Parameters.AddWithValue("@Month", Month);
                                    command.Parameters.AddWithValue("@Day", Convert.ToDateTime(day));
                                    command.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                                    command.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                                    command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                    command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));
                                    command.Parameters.AddWithValue("@UserLangID", UserLangID);

                                    using (var reader = command.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            SRCountListMode.Add(new ServiceRequestCount
                                            {
                                                Type = reader["Type"].ToString(),
                                                Year = Convert.ToInt32(reader["Year"]),
                                                Month = Convert.ToInt32(reader["Month"]),
                                                Date = reader["Date"].ToString(),
                                                StatusID = Convert.ToInt32(reader["StatusID"]),
                                                StatusName = reader["StatusName"].ToString()
                                            });
                                        }
                                        SRCountList = SRCountListMode.AsEnumerable();
                                    }
                                }
                            }
                        }
                    }

                }

                else if (SelectedCriteria == 4)//Priority
                {
                    SelectionCriteria = "Priority";

                    using (var connection = new SqlConnection(connString))
                    {
                        connection.Open();

                        using (var command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsWithPriorityAndStatus", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;

                            command.Parameters.AddWithValue("@Year", Year);
                            command.Parameters.AddWithValue("@Month", Month);
                            command.Parameters.AddWithValue("@Day", Convert.ToDateTime(day));
                            command.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                            command.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                            command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                            using (var reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    SRCountListMode.Add(new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["CallPriority_ID"].ToString())?.Name ?? "No Name",  // Party Name from the selected IDs
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        Date = reader["Date"].ToString(),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString()
                                    });
                                }
                                SRCountList = SRCountListMode.AsEnumerable();
                            }
                        }
                    }
                }

                else if (SelectedCriteria == 5)//Model
                {
                    SelectionCriteria = "Model";

                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (var connection = new SqlConnection(connString))
                        {
                            connection.Open();

                            using (var command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsWithModelAndStatus", connection))
                            {
                                command.CommandType = CommandType.StoredProcedure;

                                command.Parameters.AddWithValue("@Year", Year);
                                command.Parameters.AddWithValue("@Month", Month);
                                command.Parameters.AddWithValue("@Day", Convert.ToDateTime(day));
                                command.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                                command.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                                command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                                using (var reader = command.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        SRCountListMode.Add(new ServiceRequestCount
                                        {
                                            Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["Model_ID"].ToString())?.Name ?? "No Name",  // Party Name from the selected IDs
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            Date = reader["Date"].ToString(),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString()
                                        });
                                    }
                                    SRCountList = SRCountListMode.AsEnumerable();
                                }
                            }
                        }

                    }
                    else
                    {


                        if (GenLangCode == UserLangCode)
                        {
                            using (var connection = new SqlConnection(connString))
                            {
                                connection.Open();

                                using (var command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsWithModelAndStatusLocal", connection))
                                {
                                    command.CommandType = CommandType.StoredProcedure;

                                    command.Parameters.AddWithValue("@Year", Year);
                                    command.Parameters.AddWithValue("@Month", Month);
                                    command.Parameters.AddWithValue("@Day", Convert.ToDateTime(day));
                                    command.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                                    command.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                                    command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                    command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                                    using (var reader = command.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            SRCountListMode.Add(new ServiceRequestCount
                                            {
                                                Type = reader["Type"].ToString(),
                                                Year = Convert.ToInt32(reader["Year"]),
                                                Month = Convert.ToInt32(reader["Month"]),
                                                Date = reader["Date"].ToString(),
                                                StatusID = Convert.ToInt32(reader["StatusID"]),
                                                StatusName = reader["StatusName"].ToString()
                                            });
                                        }
                                        SRCountList = SRCountListMode.AsEnumerable();
                                    }
                                }
                            }
                        }
                        else
                        {
                            using (var connection = new SqlConnection(connString))
                            {
                                connection.Open();

                                using (var command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsWithModelLocaleAndStatus", connection))
                                {
                                    command.CommandType = CommandType.StoredProcedure;

                                    // Add parameters to the stored procedure
                                    command.Parameters.AddWithValue("@Year", Year);
                                    command.Parameters.AddWithValue("@Month", Month);
                                    command.Parameters.AddWithValue("@Day", Convert.ToDateTime(day));
                                    command.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                                    command.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                                    command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                    command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));
                                    command.Parameters.AddWithValue("@UserLangID", UserLangID);

                                    using (var reader = command.ExecuteReader())
                                    {
                                        // Read each row from the result set
                                        while (reader.Read())
                                        {
                                            SRCountListMode.Add(new ServiceRequestCount
                                            {
                                                Type = reader["Type"].ToString(),
                                                Year = Convert.ToInt32(reader["Year"]),
                                                Month = Convert.ToInt32(reader["Month"]),
                                                Date = reader["Date"].ToString(),
                                                StatusID = Convert.ToInt32(reader["StatusID"]),
                                                StatusName = reader["StatusName"].ToString()
                                            });
                                        }
                                        SRCountList = SRCountListMode.AsEnumerable();
                                    }
                                }
                            }

                        }
                    }

                }

                else if (SelectedCriteria == 6)//Brand
                {
                    SelectionCriteria = "Brand";
                    using (var connection = new SqlConnection(connString))
                    {
                        connection.Open();

                        using (var command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsWithBrandAndStatus", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;

                            // Add parameters to the stored procedure
                            command.Parameters.AddWithValue("@Year", Year);
                            command.Parameters.AddWithValue("@Month", Month);
                            command.Parameters.AddWithValue("@Day", Convert.ToDateTime(day));
                            command.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                            command.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                            command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                            using (var reader = command.ExecuteReader())
                            {

                                // Read each row from the result set
                                while (reader.Read())
                                {
                                    SRCountListMode.Add(new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["Brand_ID"].ToString())?.Name ?? "No Name",  // Party Name from the selected IDs
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        Date = reader["Date"].ToString(),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString()
                                    });
                                }
                                SRCountList = SRCountListMode.AsEnumerable();
                            }
                        }
                    }
                }


                else if (SelectedCriteria == 7)//Product Type
                {
                    SelectionCriteria = "Product Type";

                    using (var connection = new SqlConnection(connString))
                    {
                        connection.Open();

                        using (var command = new SqlCommand("SP_GetServiceRequestCountsWithProductType", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;

                            // Add parameters to the stored procedure
                            command.Parameters.AddWithValue("@Year", Year);
                            command.Parameters.AddWithValue("@Month", Month);
                            command.Parameters.AddWithValue("@Day", Convert.ToDateTime(day));
                            command.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                            command.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                            command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                            using (var reader = command.ExecuteReader())
                            {
                                // Read each row from the result set
                                while (reader.Read())
                                {
                                    SRCountListMode.Add(new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["ProductType_ID"].ToString())?.Name ?? "No Name",  // Party Name from the selected IDs
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        Date = reader["Date"].ToString(),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString()
                                    });
                                }
                                SRCountList = SRCountListMode.AsEnumerable();
                            }
                        }
                    }


                }

                SRCountListStatusReport = (from SRequest in SRCountList
                                           group SRequest by new { SRequest.Year, SRequest.Month, SRequest.Type, SRequest.Date } into final
                                           where final.FirstOrDefault().Year == Year && final.FirstOrDefault().Month == Month && Convert.ToDateTime(final.FirstOrDefault().Date) == Convert.ToDateTime(day)
                                           select new ServiceRequestCount()
                                           {
                                               Type = final.FirstOrDefault().Type,
                                               Count = final.Count(),
                                               Year = final.FirstOrDefault().Year,
                                               Date = final.FirstOrDefault().Date,
                                               StatusIDs = final.Select(a => a.StatusID).ToList(),
                                               Month = final.FirstOrDefault().Month,
                                           });

                SRCountListClosedReport = (from SRequest in SRCountListStatusReport

                                           select new ServiceRequestCount()
                                           {
                                               Year = SRequest.Year,
                                               Month = SRequest.Month,
                                               Date = SRequest.Date,
                                               Type = SRequest.Type,
                                               StatusName = SRequest.StatusName,
                                               Count = (from sr in SRequest.StatusIDs
                                                        where sr == ClosedID
                                                        select sr
                                                      ).Count()
                                           });

                FinalResultListReport = (from srFinal in SRCountListStatusReport
                                         join closed in SRCountListClosedReport on new { srFinal.Year, srFinal.Month, srFinal.Date, srFinal.Type } equals new { closed.Year, closed.Month, closed.Date, closed.Type } into CL
                                         from FINCL in CL.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                         select new ServiceRequestCount()
                                         {
                                             ClosedCount = FINCL.Count,
                                             Count = srFinal.Count,
                                             Type = srFinal.Type,
                                             Year = srFinal.Year,
                                             Date = srFinal.Date
                                         }).ToList();


                jsonobj = new
                {

                    ReportData = FinalResultListReport,
                    Year = Year,
                    Month = Month,
                    Date = Convert.ToDateTime(day).ToString("dd-MMM-yyyy")
                };

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonobj);
        }


        #endregion



        #region ::: Get Party Detail Grid Uday Kumar J B 21-11-2024:::
        /// <summary>
        ///To select menus of respective module
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult SelectPartyDetailGrid(HelpDesk_Tr_CaseDistributionChartSelectYearReportList HelpDesk_Tr_CaseDistributionChartSelectYearReportobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonobj = default(dynamic);
            List<dynamic> arr = new List<dynamic>();
            int total = 0;
            string Pname = Common.DecryptString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.PartyName);

            try
            {
                string GenLangCode = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.GeneralLanguageCode.ToString();
                string UserLangCode = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserLanguageCode.ToString();
                DataTable dt = new DataTable();

                using (SqlConnection con = new SqlConnection(connString))
                {
                    con.Open();
                    using (SqlCommand cmd = new SqlCommand())
                    {
                        cmd.Connection = con;

                        if (GenLangCode == UserLangCode)
                        {
                            // Call GetParties stored procedure
                            cmd.CommandText = "SP_AMERP_HelpDesk_GetPartiesSelectPartyDetailGrid";
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Pname", Pname);
                            cmd.Parameters.AddWithValue("@Company_ID", HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Company_ID);

                            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                            {
                                da.Fill(dt);
                            }
                        }
                        else
                        {
                            // Call GetPartyLocales stored procedure
                            cmd.CommandText = "SP_AMERP_HelpDesk_GetPartyLocalesSelectPartyDetailGrid";
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Pname", Pname);
                            cmd.Parameters.AddWithValue("@Language_ID", HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Language_ID);
                            cmd.Parameters.AddWithValue("@Company_ID", HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Company_ID);

                            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                            {
                                da.Fill(dt);
                            }
                        }
                    }
                }

                var list = dt.AsEnumerable().Select(row => new
                {
                    Party_ID = row["Party_ID"],
                    Select = $"<label  key='{row["Party_ID"]}' class='PartySelect' style='color:blue;text-decoration:underline'>Select</label>",
                    Party_Name = row["Party_Name"],
                    Party_Location = row["Party_Location"]
                }).ToList();

                total = (int)Math.Ceiling((double)list.Count / rows);
                for (int i = 0; i < list.Count; i++)
                {
                    if (i >= (page * rows) - rows && i < (page * rows))
                    {
                        arr.Add(list[i]);
                    }
                }

                jsonobj = new
                {
                    TotalPages = total,
                    PageNo = page,
                    RecordCount = arr.Count(),
                    rows = arr.ToArray()
                };
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonobj);
        }

        #endregion





        #region ::: To Export Uday Kumar J B 21-11-2024:::
        /// <summary>
        /// To Export 
        /// </summary>
        /// <returns>...</returns>
        ///  

        public static async Task<object> Export(HelpDesk_Tr_CaseDistributionChartSelectYearReportList HelpDesk_Tr_CaseDistributionChartSelectYearReportobj, string connString, int LogException, string filter, string advnceFilter, string sidx, string sord)
        {
            int companyID = Convert.ToInt32(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Company_ID);
            int branchID = Convert.ToInt32(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Branch);
            int count = 0;
            IEnumerable<ServiceRequestCount> SRCountList = null;
            int userid = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.User_ID;
            int TotalCount = 0;
            int TotalClosedCount = 0;
            IEnumerable<ServiceRequestCount> SRCountListClosedReport = null;
            IEnumerable<ServiceRequestCount> SRCountListStatusReport = null;
            IEnumerable<ServiceRequestCount> FinalResultListReport = null;
            IEnumerable<ServiceRequestCount> SRCountListClosed = null;
            List<ServiceRequestCount> FinalResultList = new List<ServiceRequestCount>();
            IEnumerable<ServiceRequestCount> SRCountListStatus = null;
            string GenLangCode = string.Empty;
            string UserLangCode = string.Empty;

            try
            {
                GetStatusIDs(connString, LogException);
                GenLangCode = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.GeneralLanguageCode.ToString();
                UserLangCode = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserLanguageCode.ToString();
                string frmdate = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.FromDate.ToString();
                string todate = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.ToDate.ToString();
                string CompanyIDs = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.CompanyIDs.ToString();
                string BranchIDs = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.BranchIDs.ToString();
                CompanyIDs = CompanyIDs.TrimEnd(new char[] { ',' });
                BranchIDs = BranchIDs.TrimEnd(new char[] { ',' });
                int UserLangID = Convert.ToInt32(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserLanguageID);
                int GeneralLangID = Convert.ToInt32(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.GeneralLanguageID);
                string startDate = string.Empty;
                string endDate = string.Empty;
                string BranchName = string.Empty;
                string SelectionCriteria = string.Empty;
                DateTime? fromDate = string.IsNullOrEmpty(frmdate) ? (DateTime?)null : Convert.ToDateTime(frmdate);
                DateTime? toDate = string.IsNullOrEmpty(todate) ? (DateTime?)null : Convert.ToDateTime(todate);
                SelectionCriteria Selection = JObject.Parse(Uri.UnescapeDataString(Uri.UnescapeDataString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Data))).ToObject<SelectionCriteria>();
                foreach (var s in Selection.SelectedIDs)
                {
                    s.Name = Common.DecryptString(s.Name);
                }

                int SelectedCriteria = Convert.ToInt32(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.SelectedCriteria);


                List<ServiceRequestCount> serviceRequestCounts = new List<ServiceRequestCount>();

                if (SelectedCriteria == 1)//Issue Area
                {
                    SelectionCriteria = "Issue Area";

                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsExport", conn);
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs); // Ensure comma-separated IDs
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ServiceRequestCount src = new ServiceRequestCount
                                {
                                    Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["IssueArea_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    Month = Convert.ToInt32(reader["Month"]),
                                    Date = reader["Date"].ToString(),
                                    StatusID = Convert.ToInt32(reader["StatusID"]),
                                    StatusName = reader["StatusName"].ToString(),
                                    CompanyName = reader["CompanyName"].ToString(),
                                    BranchName = reader["BranchName"].ToString(),
                                    Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, Convert.ToInt32(reader["Branch_ID"])) // Call the C# method
                                };

                                serviceRequestCounts.Add(src);
                            }
                            SRCountList = serviceRequestCounts.AsEnumerable();
                        }
                    }
                }
                else if (SelectedCriteria == 8)//Dealer
                {
                    SelectionCriteria = "Dealer";

                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetDealerServiceRequestCountsExport", conn);
                            cmd.CommandType = CommandType.StoredProcedure;

                            // Add parameters
                            cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs); // Ensure comma-separated IDs
                            cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount src = new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["Party_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        Date = reader["Date"].ToString(),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString(),
                                        CompanyName = reader["CompanyName"].ToString(),
                                        BranchName = reader["BranchName"].ToString(),
                                        Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, Convert.ToInt32(reader["Branch_ID"])) // Call C# method
                                    };

                                    serviceRequestCounts.Add(src);
                                }
                                SRCountList = serviceRequestCounts.AsEnumerable();
                            }
                        }
                    }
                    else
                    {

                        if (GenLangCode == UserLangCode)
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetDealerServiceRequestCountsByBranchExport", conn);
                                cmd.CommandType = CommandType.StoredProcedure;

                                // Add parameters
                                cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs); // Ensure comma-separated IDs
                                cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount src = new ServiceRequestCount
                                        {
                                            Type = reader["Type"].ToString(),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            Date = reader["Date"].ToString(),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString(),
                                            CompanyName = reader["CompanyName"].ToString(),
                                            BranchName = reader["BranchName"].ToString(),
                                            Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, Convert.ToInt32(reader["Branch_ID"])) // Call C# method
                                        };

                                        serviceRequestCounts.Add(src);
                                    }
                                    SRCountList = serviceRequestCounts.AsEnumerable();
                                }
                            }
                        }
                    }
                }
                else if (SelectedCriteria == 3)//Party
                {
                    SelectionCriteria = "Party";

                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetNonDealerServiceRequestCountsExport", conn);
                            cmd.CommandType = CommandType.StoredProcedure;

                            // Add parameters
                            cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs); // Ensure comma-separated IDs
                            cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount src = new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["Party_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        Date = reader["Date"].ToString(),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString(),
                                        CompanyName = reader["CompanyName"].ToString(),
                                        BranchName = reader["BranchName"].ToString(),
                                        Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, Convert.ToInt32(reader["Branch_ID"])) // Call C# method
                                    };

                                    serviceRequestCounts.Add(src);
                                }
                                SRCountList = serviceRequestCounts.AsEnumerable();
                            }
                        }
                    }
                    else
                    {
                        if (GenLangCode == UserLangCode)
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByPartyExport", conn);
                                cmd.CommandType = CommandType.StoredProcedure;

                                // Add parameters
                                cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs); // Ensure comma-separated IDs
                                cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount src = new ServiceRequestCount
                                        {
                                            Type = reader["Type"].ToString(),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            Date = reader["Date"].ToString(),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString(),
                                            CompanyName = reader["CompanyName"].ToString(),
                                            BranchName = reader["BranchName"].ToString(),
                                            Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, Convert.ToInt32(reader["Branch_ID"])) // Call C# method
                                        };

                                        serviceRequestCounts.Add(src);
                                    }
                                    SRCountList = serviceRequestCounts.AsEnumerable();
                                }
                            }
                        }
                        else
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsLocalExport", conn);
                                cmd.CommandType = CommandType.StoredProcedure;

                                // Add parameters
                                cmd.Parameters.AddWithValue("@UserLangID", UserLangID);
                                cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs); // Ensure comma-separated IDs
                                cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount src = new ServiceRequestCount
                                        {
                                            Type = reader["Type"].ToString(),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            Date = reader["Date"].ToString(),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString(),
                                            CompanyName = reader["CompanyName"].ToString(),
                                            BranchName = reader["BranchName"].ToString(),
                                            Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, Convert.ToInt32(reader["Branch_ID"])) // Call C# method
                                        };

                                        serviceRequestCounts.Add(src);
                                    }
                                    SRCountList = serviceRequestCounts.AsEnumerable();
                                }
                            }
                        }
                    }

                }
                else if (SelectedCriteria == 4)//Priority
                {
                    SelectionCriteria = "Priority";

                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByPriorityExport", conn);
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs); // Ensure comma-separated IDs
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ServiceRequestCount src = new ServiceRequestCount
                                {
                                    Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["CallPriority_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    Month = Convert.ToInt32(reader["Month"]),
                                    Date = reader["Date"].ToString(),
                                    StatusID = Convert.ToInt32(reader["StatusID"]),
                                    StatusName = reader["StatusName"].ToString(),
                                    CompanyName = reader["CompanyName"].ToString(),
                                    BranchName = reader["BranchName"].ToString(),
                                    Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, Convert.ToInt32(reader["Branch_ID"])) // Call C# method
                                };

                                serviceRequestCounts.Add(src);
                            }
                            SRCountList = serviceRequestCounts.AsEnumerable();
                        }
                    }
                }
                else if (SelectedCriteria == 5)//Model
                {
                    SelectionCriteria = "Model";

                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByModelExport", conn);
                            cmd.CommandType = CommandType.StoredProcedure;

                            // Add parameters
                            cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs); // Ensure comma-separated IDs
                            cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount src = new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["Model_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        Date = reader["Date"].ToString(),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString(),
                                        CompanyName = reader["CompanyName"].ToString(),
                                        BranchName = reader["BranchName"].ToString(),
                                        Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, Convert.ToInt32(reader["Branch_ID"])) // Call C# method
                                    };

                                    serviceRequestCounts.Add(src);
                                }
                                SRCountList = serviceRequestCounts.AsEnumerable();
                            }
                        }
                    }
                    else
                    {

                        if (GenLangCode == UserLangCode)
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsForModelExport", conn);
                                cmd.CommandType = CommandType.StoredProcedure;

                                // Add parameters
                                cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs); // Ensure comma-separated IDs
                                cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount src = new ServiceRequestCount
                                        {
                                            Type = reader["Type"].ToString(),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            Date = reader["Date"].ToString(),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString(),
                                            CompanyName = reader["CompanyName"].ToString(),
                                            BranchName = reader["BranchName"].ToString(),
                                            Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, Convert.ToInt32(reader["Branch_ID"])) // Call C# method
                                        };

                                        serviceRequestCounts.Add(src);
                                    }
                                    SRCountList = serviceRequestCounts.AsEnumerable();
                                }
                            }
                        }
                        else
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByModelLocaleExport", conn);
                                cmd.CommandType = CommandType.StoredProcedure;

                                // Add parameters
                                cmd.Parameters.AddWithValue("@UserLangID", UserLangID);
                                cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                                cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs); // Ensure comma-separated IDs
                                cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount src = new ServiceRequestCount
                                        {
                                            Type = reader["Type"].ToString(),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            Date = reader["Date"].ToString(),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString(),
                                            CompanyName = reader["CompanyName"].ToString(),
                                            BranchName = reader["BranchName"].ToString(),
                                            Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, Convert.ToInt32(reader["Branch_ID"])) // Call C# method
                                        };

                                        serviceRequestCounts.Add(src);
                                    }
                                    SRCountList = serviceRequestCounts.AsEnumerable();
                                }
                            }
                        }
                    }

                }
                else if (SelectedCriteria == 6)//Brand
                {
                    SelectionCriteria = "Brand";
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByBrandExport", conn);
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs); // Ensure comma-separated IDs
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ServiceRequestCount src = new ServiceRequestCount
                                {
                                    Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["Brand_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    Month = Convert.ToInt32(reader["Month"]),
                                    Date = reader["Date"].ToString(),
                                    StatusID = Convert.ToInt32(reader["StatusID"]),
                                    StatusName = reader["StatusName"].ToString(),
                                    CompanyName = reader["CompanyName"].ToString(),
                                    BranchName = reader["BranchName"].ToString(),
                                    Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, Convert.ToInt32(reader["Branch_ID"])) // Call C# method
                                };

                                serviceRequestCounts.Add(src);
                            }
                            SRCountList = serviceRequestCounts.AsEnumerable();
                        }
                    }
                }
                else if (SelectedCriteria == 7)//Product Type
                {
                    SelectionCriteria = "Product Type";

                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByProductTypeExport", conn);
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@FromDate", (object)fromDate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ToDate", (object)toDate ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@CompanyIDs", CompanyIDs); // Ensure comma-separated IDs
                        cmd.Parameters.AddWithValue("@BranchIDs", BranchIDs);

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ServiceRequestCount src = new ServiceRequestCount
                                {
                                    Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader["ProductType_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    Month = Convert.ToInt32(reader["Month"]),
                                    Date = reader["Date"].ToString(),
                                    StatusID = Convert.ToInt32(reader["StatusID"]),
                                    StatusName = reader["StatusName"].ToString(),
                                    CompanyName = reader["CompanyName"].ToString(),
                                    BranchName = reader["BranchName"].ToString(),
                                    Region = Common.getRegionName(connString, LogException, UserLangID, GeneralLangID, Convert.ToInt32(reader["Branch_ID"])) // Call C# method
                                };

                                serviceRequestCounts.Add(src);
                            }
                            SRCountList = serviceRequestCounts.AsEnumerable();
                        }
                    }
                }

                //-------------------------------------------------------------------------------------------------------//
                SRCountListStatus = (from SRequest in SRCountList
                                     group SRequest by new { SRequest.Year, SRequest.Month, SRequest.Date, SRequest.Type } into final //Modified by Harish on 16-Dec-2014 for Version 2.0 corrections
                                     select new ServiceRequestCount()
                                     {
                                         Type = final.FirstOrDefault().Type,
                                         Count = final.Count(),
                                         Year = final.FirstOrDefault().Year,
                                         StatusIDs = final.Select(a => a.StatusID).ToList(),
                                         Month = final.FirstOrDefault().Month,
                                         Date = final.FirstOrDefault().Date,
                                         CompanyName = final.FirstOrDefault().CompanyName,
                                         BranchName = final.FirstOrDefault().BranchName,
                                         Region = final.FirstOrDefault().Region
                                     });

                SRCountListStatusReport = (from SRequest in SRCountList
                                           group SRequest by new { SRequest.Year, SRequest.Month } into final //Modified by Harish on 16-Dec-2014 for Version 2.0 corrections
                                                                                                              //where final.FirstOrDefault().Year == Year && final.FirstOrDefault().Month == Month
                                           select new ServiceRequestCount()
                                           {
                                               Type = final.FirstOrDefault().Type,
                                               Count = final.Count(),
                                               Year = final.FirstOrDefault().Year,
                                               StatusIDs = final.Select(a => a.StatusID).ToList(),
                                               Month = final.FirstOrDefault().Month,
                                           });


                SRCountListClosed = (from SRequest in SRCountListStatus
                                     select new ServiceRequestCount()
                                     {
                                         Year = SRequest.Year,
                                         Type = SRequest.Type,
                                         StatusName = SRequest.StatusName,
                                         Month = SRequest.Month,
                                         Date = SRequest.Date,
                                         Count = (from sr in SRequest.StatusIDs
                                                  where sr == ClosedID
                                                  select sr
                                                ).Count(),
                                         CompanyName = SRequest.CompanyName,
                                         BranchName = SRequest.BranchName,
                                         Region = SRequest.Region
                                     });

                SRCountListClosedReport = (from SRequest in SRCountListStatusReport

                                           select new ServiceRequestCount()
                                           {
                                               Year = SRequest.Year,
                                               Month = SRequest.Month,
                                               Type = SRequest.Type,
                                               StatusName = SRequest.StatusName,
                                               Count = (from sr in SRequest.StatusIDs
                                                        where sr == ClosedID
                                                        select sr
                                                      ).Count()
                                           });

                FinalResultList = (from srFinal in SRCountListStatus
                                   join closed in SRCountListClosed on new { srFinal.Year, srFinal.Month, srFinal.Date, srFinal.Type } equals new { closed.Year, closed.Month, closed.Date, closed.Type } into CL
                                   from FINCL in CL.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                   select new ServiceRequestCount()
                                   {
                                       ClosedCount = FINCL.Count,
                                       Count = srFinal.Count,
                                       Type = srFinal.Type,
                                       Year = srFinal.Year,
                                       Month = srFinal.Month,
                                       Date = DateTime.TryParse(srFinal.Date?.ToString(), out DateTime parsedDate)
                                              ? parsedDate.ToString("dd-MMM-yyyy")
                                              : "Invalid Date",
                                       CompanyName = srFinal.CompanyName,
                                       BranchName = srFinal.BranchName,
                                       Region = srFinal.Region
                                   }).ToList();


                FinalResultListReport = (from srFinal in SRCountListStatusReport
                                         join closed in SRCountListClosedReport on new { srFinal.Year, srFinal.Month } equals new { closed.Year, closed.Month } into CL //Modified by Harish on 16-Dec-2014 for Version 2.0 corrections
                                         from FINCL in CL.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                         select new ServiceRequestCount()
                                         {
                                             ClosedCount = FINCL.Count,
                                             Count = srFinal.Count,
                                             Type = srFinal.Type,
                                             Year = srFinal.Year
                                         }).ToList();


                count = SRCountListStatus.Count();
                DataTable dtOptions = new DataTable();
                dtOptions.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "SelectionCriteria").ToString());
                dtOptions.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "fromdate").ToString());
                dtOptions.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "todate").ToString());


                dtOptions.Rows.Add(SelectionCriteria, (HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.FromDate.ToString() != "" ? Convert.ToDateTime(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.FromDate).ToString("dd-MMM-yyyy") : ""), (HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.ToDate.ToString() != "" ? Convert.ToDateTime(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.ToDate).ToString("dd-MMM-yyyy") : ""));

                DataTable dt = new DataTable();
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "Region").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "CompanyName").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "BranchName").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "Date").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "SelectionCriteria").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "ReceivedCount").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "ClosedCount").ToString());

                DataTable DtAlignment = new DataTable();
                DtAlignment.Columns.Add("Region");
                DtAlignment.Columns.Add("CompanyName");
                DtAlignment.Columns.Add("BranchName");
                DtAlignment.Columns.Add("Date");
                DtAlignment.Columns.Add("SelectionCriteria");
                DtAlignment.Columns.Add("ReceivedCount");
                DtAlignment.Columns.Add("ClosedCount");
                DtAlignment.Rows.Add(0, 0, 0, 0, 0, 2, 2);

                DataSet ds = new DataSet();
                for (int i = 0; i < FinalResultList.Count; i++)
                {
                    TotalCount += FinalResultList[i].Count;
                    TotalClosedCount += FinalResultList[i].ClosedCount;
                    dt.Rows.Add(FinalResultList[i].Region, FinalResultList[i].CompanyName, FinalResultList[i].BranchName, FinalResultList[i].Date, FinalResultList[i].Type, FinalResultList[i].Count, FinalResultList[i].ClosedCount);
                }
                dt.Rows.Add("", "", "", "", "Total", TotalCount, TotalClosedCount);
                ReportExportList reportExportList = new ReportExportList
                {
                    Company_ID = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Company_ID, // Assuming this is available in ExportObj
                    Branch = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Branch.ToString(),
                    GeneralLanguageID = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.GeneralLanguageID,
                    UserLanguageID = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Language_ID,
                    Options = dtOptions,
                    dt = dt,
                    Alignment = DtAlignment,
                    FileName = "TicketDistributionChart", // Set a default or dynamic filename
                    Header = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "CaseDistributionChart").ToString(), // Set a default or dynamic header
                    exprtType = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                    UserCulture = HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture
                };

                var result = await ReportExport.Export(reportExportList, connString, LogException);
                return result.Value;
                // ReportExport.Export(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.exprtType, dt, dtOptions, DtAlignment, "TicketDistributionChart", CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.UserCulture.ToString(), "CaseDistributionChart").ToString());
                //  gbl.InsertGPSDetails(Convert.ToInt32(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.Company_ID.ToString()), branchID, HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.User_ID, Common.GetObjectID("HelpDesk_Tr_CaseDistributionChart"), 0, 0, 0, "Ticket Distribution Chart-Export ", false, Convert.ToInt32(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.MenuID), Convert.ToDateTime(HelpDesk_Tr_CaseDistributionChartSelectYearReportobj.LoggedINDateTime));
            }
            catch (Exception ex)
            {
                if (LogException == 1) LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return false;
        }

        #endregion




        #region :::HelpDesk_Tr_CaseDistributionChart list and obj classes Uday Kumar J B 21-11-2024:::
        /// <summary>
        /// HelpDesk_Tr_CaseDistributionChart
        /// </summary>
        /// <returns>...</returns>
        ///  

        public class HelpDesk_Tr_CaseDistributionChartSelectYearReportList
        {

            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
            public string CompanyIDs { get; set; }
            public string BranchIDs { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int User_ID { get; set; }
            public string FromDate { get; set; }
            public string ToDate { get; set; }
            public int Mode { get; set; }
            public string Data { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public int SelectedCriteria { get; set; }
            public int Year { get; set; }
            public int Month { get; set; }
            public string day { get; set; }
            public string UserCulture { get; set; }
            public string PartyName { get; set; }
            public int Language_ID { get; set; }
            public int exprtType { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filter { get; set; }
            public string advanceFilter { get; set; }

        }



        public class HelpDesk_Tr_CaseDistributionChartGetModelList
        {

            public string ModelName { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
            public int Company_ID { get; set; }
            public int Language_ID { get; set; }



        }

        public class HelpDesk_Tr_CaseDistributionChartGetPartyDetailsList
        {
            public string PartyName { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
            public int Company_ID { get; set; }
            public int Language_ID { get; set; }

        }


        public class HelpDesk_Tr_CaseDistributionChartProductTypeList
        {
            public int Language_ID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
            public string CompanyIDs { get; set; }

        }


        public class HelpDesk_Tr_CaseDistributionChartLoadBrandList
        {
            public int Language_ID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
            public string CompanyIDs { get; set; }

        }



        public class HelpDesk_Tr_CaseDistributionChartSelectBrandList
        {
            public int Language_ID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
            public int Company_ID { get; set; }

        }


        public class HelpDesk_Tr_CaseDistributionChartloadMastersDDList
        {

            public string MasterName { get; set; }
            public int isComp { get; set; }
            public string CompanyIDs { get; set; }
            public int Language_ID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
        }


        public class HelpDesk_Tr_CaseDistributionChartLoadBranchDDList
        {
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
            public int Employee_ID { get; set; }

        }
        #endregion



        #region :::HelpDesk_Tr_CaseDistributionChart  classes Uday Kumar J B 21-11-2024:::
        /// <summary>
        /// HelpDesk_Tr_CaseDistributionChart
        /// </summary>
        /// <returns>...</returns>
        ///  

        public class ServiceRequestCount
        {
            public int Year { get; set; }
            public int Month { get; set; }
            public string Type { get; set; }
            public int Count { get; set; }
            public string Date { get; set; }
            public int StatusID { get; set; }
            public List<int> StatusIDs { get; set; }
            public int StatusCount { get; set; }
            public string MonthName { get; set; }
            public string StatusName { get; set; }
            public int CompletedCount { get; set; }
            public int PendingCount { get; set; }
            public int InProgressCount { get; set; }
            public int OnHoldCount { get; set; }
            public int ClosedCount { get; set; }
            public double TimeDiff { get; set; }
            public string AvgResolution { get; set; }
            public int EscalatedCount { get; set; }
            public string CompanyName { get; set; }
            public string BranchName { get; set; }
            public int CompanyID { get; set; }
            public int BranchID { get; set; }
            public string Region { get; set; }
        }
        public class SelectionCriteria
        {
            public List<Selections> SelectedIDs
            {
                get;
                set;
            }
        }
        public class Selections
        {

            public int ID
            {
                get;
                set;
            }
            public string Name
            {
                get;
                set;
            }
        }
        #endregion

    }
}
