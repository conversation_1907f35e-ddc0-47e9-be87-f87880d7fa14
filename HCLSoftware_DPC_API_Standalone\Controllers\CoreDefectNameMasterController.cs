﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreDefectNameMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreDefectNameMasterController : ApiController
    {

        #region ::: Export Uday Kumar J B 20-08-2024:::
        /// <summary>
        /// Exporting 
        /// </summary> 
        /// 
        [Route("api/CoreDefectNameMaster/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] ExportCoreDefectNameMasterList ExportCoreDefectNameMasterobj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportCoreDefectNameMasterobj.sidx;
            string sord = ExportCoreDefectNameMasterobj.sord;
            string filter = ExportCoreDefectNameMasterobj.filter;
            string advnceFilter = ExportCoreDefectNameMasterobj.advanceFilter;

            try
            {


                object Response = await CoreDefectNameMasterServices.Export(ExportCoreDefectNameMasterobj, connstring, filter, advnceFilter, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion


        #region ::: CheckDefectNameLocale Uday Kumar J B 20-08-2024:::
        /// <summary>
        /// CheckDefectNameLocale 
        /// </summary>
        /// 
        [Route("api/CoreDefectNameMaster/CheckDefectNameLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckDefectNameLocale([FromBody] CheckDefectNameLocaleList CheckDefectNameLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreDefectNameMasterServices.CheckDefectNameLocale(connString, CheckDefectNameLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckDefectName Uday Kumar J B 20-08-2024:::
        /// <summary>
        /// To Check DefectName already exists 
        /// </summary>//Val
        /// 
        [Route("api/CoreDefectNameMaster/CheckDefectName")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckDefectName([FromBody] CheckDefectNameList CheckDefectNameobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreDefectNameMasterServices.CheckDefectName(connString, CheckDefectNameobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectReferenceMaster Uday Kumar J B 20-08-2024:::
        /// <summary>
        /// To get Refrence Master records for a Master
        /// </summary> 
        /// 
        [Route("api/CoreDefectNameMaster/SelectReferenceMaster")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectReferenceMaster([FromBody] SelectReferenceMasterCoreDefectNameMasterList SelectReferenceMasterCoreDefectNameMasterobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreDefectNameMasterServices.SelectReferenceMaster(connString, SelectReferenceMasterCoreDefectNameMasterobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Select Uday Kumar J B 20-08-2024:::
        /// <summary>
        /// To Select DefectName for a DefectGroup
        /// </summary>
        [Route("api/CoreDefectNameMaster/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] SelectCoreDefectNameMasterList SelectCoreDefectNameMasterobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            if (string.IsNullOrEmpty(sidx))
            {
                sidx = "DefectName_Description";
            }

            int rows;
            string rowsParam = HttpContext.Current.Request.Params["rows"];

            if (string.IsNullOrEmpty(rowsParam) || !int.TryParse(rowsParam, out rows) || rows == 0)
            {
                rows = 10;
            }

            int page;
            string pageParam = HttpContext.Current.Request.Params["page"];

            if (string.IsNullOrEmpty(pageParam) || !int.TryParse(pageParam, out page) || page == 0)
            {
                page = 1; // Default to the first page if the value is null, empty, or 0
            }

            string sord = HttpContext.Current.Request.Params["sord"];
            if (string.IsNullOrEmpty(sord))
            {
                sord = "asc";
            }
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreDefectNameMasterServices.Select(connString, SelectCoreDefectNameMasterobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Delete Uday Kumar J B 20-08-2024 :::
        /// <summary>
        /// To Delete Defect Name
        /// </summary>
        /// 
        [Route("api/CoreDefectNameMaster/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteCoreDefectNameMasterList DeleteCoreDefectNameMasterobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreDefectNameMasterServices.Delete(connString, DeleteCoreDefectNameMasterobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Save Uday Kumar J B 20-08-2024 :::
        /// <summary>
        /// To Insert and Update Defect Name
        /// </summary>
        /// 
        [Route("api/CoreDefectNameMaster/Save")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Save([FromBody] SaveCoreDefectNameMasterList SaveCoreDefectNameMasterobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreDefectNameMasterServices.Save(connString, SaveCoreDefectNameMasterobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectParticularDefectName Uday Kumar J B 20-08-2024 :::
        /// <summary>
        /// To Select Particular Defect Name
        /// </summary>
        /// 
        [Route("api/CoreDefectNameMaster/SelectParticularDefectName")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularDefectName([FromBody] SelectParticularDefectNameList SelectParticularDefectNameobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreDefectNameMasterServices.SelectParticularDefectName(connString, SelectParticularDefectNameobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: UpdateLocale Uday Kumar J B 20-08-2024:::
        /// <summary>
        /// UpdateLocale
        /// </summary>
        /// 
        [Route("api/CoreDefectNameMaster/UpdateLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateLocale([FromBody] UpdateLocaleCoreDefectNameMasterList UpdateLocaleCoreDefectNameMasterobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreDefectNameMasterServices.UpdateLocale(connString, UpdateLocaleCoreDefectNameMasterobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion

    }
}