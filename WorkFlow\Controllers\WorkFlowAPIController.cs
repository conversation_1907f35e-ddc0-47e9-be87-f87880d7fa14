﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.EntityClient;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Newtonsoft.Json.Linq;
using WorkFlow.Controllers.WorkFlow.Controllers;
using WorkFlow.Models;
using LS = LogSheetExporter;
using System.Data.SqlClient;
namespace WorkFlow.Controllers
{
    namespace WorkFlow.Controllers
    {
        public class WorkFlowAPIController: Controller
        {
            WorkFlowEntity client = null;
            GenEntities genEnt =null;
            public WorkFlowAPIController(string dbname)
            {
                client = new WorkFlowEntity(dbname);
                genEnt = new GenEntities(dbname);
            }
            SqlConnection con;
            SqlCommand cmd;
            SqlDataReader dr;
            string strCon = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString.ToString();

            #region ::: To Update Work Flow Field Value:::
            /// <Author>"Puneeth M"</Author>
            /// <CreatedDate>"1-2-2013"</CreatedDate>
            /// <Purpose >"To Update Work Flow Field Value"</Purpose>

            public void UpdateWorkFlowFieldValue(int CompanyID, int WorkFlowID, int WFFieldID, int TransactionID, string WorkFlowFieldValue)
            {
                try
                {
                    IEnumerable<WF_WFField> WFFieldList = client.WF_WFField.Where(i => i.WorkFlow_ID == WorkFlowID && i.WFField_ID == WFFieldID).ToList();
                    foreach (var j in WFFieldList)
                    {
                        WF_WFFieldValue WFFieldValueObj = new WF_WFFieldValue();
                        WF_WFFieldValue WFFieldValue = client.WF_WFFieldValue.Where(i => i.Company_ID == CompanyID && i.WorkFlow_ID == WorkFlowID && i.WFField_ID == j.WFField_ID && i.Transaction_ID == TransactionID).FirstOrDefault();
                        if (WFFieldValue == null)
                        {
                            WFFieldValueObj.Company_ID = CompanyID;
                            WFFieldValueObj.WorkFlow_ID = WorkFlowID;
                            WFFieldValueObj.WFField_ID = j.WFField_ID;
                            WFFieldValueObj.Transaction_ID = TransactionID;
                            WFFieldValueObj.WorkFlowFieldValue = WorkFlowFieldValue;
                            client.WF_WFFieldValue.Add(WFFieldValueObj);
                        }
                        else
                        {
                            if (WorkFlowFieldValue != WFFieldValueObj.WorkFlowFieldValue)
                            {
                                WFFieldValueObj.WorkFlowFieldValue = WorkFlowFieldValue;
                            }
                        }
                        client.SaveChanges();   
                    }
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            #endregion

            #region ::: Auto Allocate:::
            /// <Author>"Puneeth M"</Author>
            /// <CreatedDate>"1-02-2013"</CreatedDate>
            /// <Purpose >"Auto Allocatee"</Purpose>
            public WF_WFStepLink AutoAllocate(int companyID, int workFlowID, int stepID, int ActionID, int TransactionID)
            {
                IEnumerable<WF_WFStepLink> Slink = null;
                WF_WFStepLink result = null;
                IEnumerable<WF_WFStepLink> ToStepLink = null;
                WF_WFFieldValue WFFieldValue = null;
                try
                {
                    IEnumerable<WF_WFField> WFField = client.WF_WFField.Where(i => i.WorkFlow_ID == workFlowID);
                    int IsAutoAllocate = 0;
                    Slink = client.WF_WFStepLink.Where(a => a.FrmWFSteps_ID == stepID && a.WorkFlow_ID == workFlowID && a.Company_ID == companyID && a.WFAction_ID == ActionID);
                    foreach (var z in Slink)
                    {
                        int ToStep = 0;
                        ToStep = z.ToWFSteps_ID;

                        ToStepLink = client.WF_WFStepLink.Where(a => a.FrmWFSteps_ID == ToStep && a.WorkFlow_ID == workFlowID && a.Company_ID == companyID);

                        IsAutoAllocate = ToStepLink.Select(i => i.Addresse_Flag).FirstOrDefault();
                        if (IsAutoAllocate == 2)
                        {
                            foreach (var x in WFField)
                            {
                                WFFieldValue = client.WF_WFFieldValue.Where(i => i.Company_ID == companyID && i.WFField_ID == x.WFField_ID && i.Transaction_ID == TransactionID).FirstOrDefault();
                                foreach (var y in ToStepLink)
                                {
                                    //AutoCondition = y;
                                    if (y.AutoCondition != "" && y.Addresse_Flag == 2)
                                    {
                                        //result = ToStepLink.Where(r => r.AutoCondition == WFFieldValue.WorkFlowFieldValue).FirstOrDefault();                        
                                        //Commented by DK 
                                        //string query1 = "select A.* from GNM_WFStepLink A inner join GNM_WFFieldValue B on A.WFField_ID=B.WFField_ID and A.WorkFlow_ID=B.WorkFlow_ID and A.Company_ID=B.Company_ID where A.Company_ID=" + companyID + " and A.WFField_ID=" + WFFieldValue.WFField_ID + " and B.Transaction_ID=" + TransactionID + " and A.Addresse_Flag=" + 2 + " and A.FrmWFSteps_ID=" + ToStep + "  and B.WorkFlowFieldValue ='" + y.AutoCondition + "' and A.AutoCondition='" + y.AutoCondition.Replace("'", "''") + "'";

                                        //Added by DK on20-Dec-14 - Modifed Query to Check Typeof value 
                                        Double dblValue;
                                        string query1 = "";
                                        if (Double.TryParse(WFFieldValue.WorkFlowFieldValue, out dblValue))
                                        {
                                            query1 = "Select A.* from GNM_WFStepLink A inner join GNM_WFFieldValue B on A.WFField_ID=B.WFField_ID and A.WorkFlow_ID=B.WorkFlow_ID and A.Company_ID=B.Company_ID where A.Company_ID=" + companyID + " and A.WFField_ID=" + WFFieldValue.WFField_ID + " and B.Transaction_ID=" + TransactionID + " and A.Addresse_Flag=" + 2 + " and A.FrmWFSteps_ID=" + ToStep + " and Convert (decimal(12,2),B.WorkFlowFieldValue) " + y.AutoCondition + " and A.AutoCondition='" + y.AutoCondition.Replace("'", "''") + "'";
                                        }
                                        else
                                        {
                                            query1 = "Select A.* from GNM_WFStepLink A inner join GNM_WFFieldValue B on A.WFField_ID=B.WFField_ID and A.WorkFlow_ID=B.WorkFlow_ID and A.Company_ID=B.Company_ID where A.Company_ID=" + companyID + " and A.WFField_ID=" + WFFieldValue.WFField_ID + " and B.Transaction_ID=" + TransactionID + " and A.Addresse_Flag=" + 2 + " and A.FrmWFSteps_ID=" + ToStep + " and B.WorkFlowFieldValue " + y.AutoCondition + " and A.AutoCondition='" + y.AutoCondition.Replace("'", "''") + "'";
                                        }
                                        result = client.Database.SqlQuery(typeof(WF_WFStepLink), query1).Cast<WF_WFStepLink>().FirstOrDefault();
                                        if (result != null)
                                        {
                                            break;
                                        }
                                    }
                                }
                                if (result != null)
                                {
                                    break;
                                }

                            }
                        }
                        if (result != null)
                        {
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return result;
            }
            #endregion

            #region ::: GetStatusofWorkFlow :::
            /// <Author>"Kiran NR"</Author>
            /// <CreatedDate>"11-10-2012"</CreatedDate>
            /// <Purpose >"To bring the current status of the work flow for the transaction number"</Purpose>
            /// <returns></returns> 
            //public ActionResult GetStatusofWorkFlow(int transactionNumber, int workFlowID)
            //{
            //    dynamic dummy = null;
            //    var jsonResult = dummy;
            //    var arr = dummy;
            //    WF_WFSteps step = null;

            //    try
            //    {
            //        WF_WFCase_Progress gnmCaseProgress = client.WF_WFCase_Progress.OrderByDescending(a => a.WFCaseProgress_ID).Where(a => a.Transaction_ID == transactionNumber && a.WorkFlow_ID == workFlowID).FirstOrDefault();

            //        List<WF_WFSteps> stepList = client.WF_WFSteps.Where(i => i.WorkFlow_ID == workFlowID).ToList();
            //        if (gnmCaseProgress != null)
            //        {
            //            step = client.WF_WFSteps.Where(i => i.WFSteps_ID == gnmCaseProgress.WFSteps_ID).FirstOrDefault();

            //            jsonResult = "{CurrentStepID:" + step.WFSteps_ID + ",CurrentStepName:'" + step.WFStep_Name + "'}";
            //        }
            //        else
            //        {
                        //arr = from a in stepList
                        //      join c in client.WF_WFSteps on a.WFSteps_ID equals c.WFSteps_ID
                        //      join b in client.WF_WFStepType on a.WFStepType_ID equals b.WFStepType_ID
                        //      where b.WFStepType_Nm.ToUpper() == "BEGIN"
                        //      select new
                        //      {
                        //          a.WorkFlow_ID,
                        //          a.WFSteps_ID,
                        //          c.WFStep_Name

                        //      };
                        //foreach (var x in arr)
                        //{
                        //    jsonResult = "{CurrentStepID:" + x.WFSteps_ID + ",CurrentStepName:'" + x.WFStep_Name + "'}";
                        //}
            //        }
            //    }
            //    catch (Exception ex)
            //    {
            //        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            //    }

            //    return Json(jsonResult, JsonRequestBehavior.AllowGet);
            //}

            public ActionResult GetStatusofWorkFlow(int transactionNumber, int workFlowID, int UserID, int CompID,int UserLanguageID,int BrnachID=0)
            {
                dynamic dummy = null;
                var jsonResult = dummy;
                var arr = dummy;
                WF_WFSteps step = null;
                try
                {
                    WF_WFCase_Progress gnmCaseProgress = client.WF_WFCase_Progress.OrderByDescending(a => a.WFCaseProgress_ID).Where(a => a.Transaction_ID == transactionNumber && a.WorkFlow_ID == workFlowID).FirstOrDefault();

                    List<WF_WFSteps> stepList = client.WF_WFSteps.Where(i => i.WorkFlow_ID == workFlowID).ToList();
                    if (gnmCaseProgress != null)
                    {
                        if (UserLanguageID == 0)
                        {
                            step = client.WF_WFSteps.Where(i => i.WFSteps_ID == gnmCaseProgress.WFSteps_ID).FirstOrDefault();
                            jsonResult = "{CurrentStepID:" + step.WFSteps_ID + ",CurrentStepName:'" + step.WFStep_Name + "'}";
                        }
                        else {
                            WF_WFStepsLocale stepL = client.WF_WFStepsLocale.Where(i => i.WFSteps_ID == gnmCaseProgress.WFSteps_ID && i.Language_ID==UserLanguageID).FirstOrDefault();
                            jsonResult = "{CurrentStepID:" + stepL.WFSteps_ID + ",CurrentStepName:'" + stepL.WFStep_Name + "'}";
                        }
                    }
                    else
                    {
                        List<WF_WFStepLink> StepLink = client.WF_WFStepLink.Where(s => s.Company_ID == CompID && s.WorkFlow_ID == workFlowID).ToList();
                        string BranchCode = genEnt.WF_Branch.Where(a => a.Branch_ID == BrnachID).FirstOrDefault().Branch_ShortName;
                        if (UserLanguageID == 0)
                        {
                            jsonResult = (from a in StepLink
                                          join c in client.WF_WFSteps on a.FrmWFSteps_ID equals c.WFSteps_ID
                                          join b in client.WF_WFStepType on c.WFStepType_ID equals b.WFStepType_ID
                                          join d in client.WF_WFRoleUser on a.Addresse_WFRole_ID equals d.WFRole_ID
                                          where b.WFStepType_Nm.ToUpper() == "BEGIN" && d.UserID == UserID && c.BranchCode.ToUpper()==BranchCode.ToUpper()
                                          select new
                                          {
                                              c.WFSteps_ID,
                                              c.WFStep_Name

                                          }).Distinct().ToArray();
                        }
                        else {
                            jsonResult = (from a in StepLink
                                          join c in client.WF_WFSteps on a.FrmWFSteps_ID equals c.WFSteps_ID
                                          join b in client.WF_WFStepType on c.WFStepType_ID equals b.WFStepType_ID
                                          join d in client.WF_WFRoleUser on a.Addresse_WFRole_ID equals d.WFRole_ID
                                          join L in client.WF_WFStepsLocale on c.WFSteps_ID equals L.WFSteps_ID
                                          where b.WFStepType_Nm.ToUpper() == "BEGIN" && d.UserID == UserID && L.Language_ID == UserLanguageID && c.BranchCode.ToUpper() == BranchCode.ToUpper()
                                          select new
                                          {
                                              c.WFSteps_ID,
                                              L.WFStep_Name

                                          }).Distinct().ToArray();
                        }

                        // jsonResult = new { r.ToArray() };

                        //jsonResult = "Result:{[";

                        //foreach (var x in arr)
                        //{
                        //    jsonResult += "{CurrentStepID:" + x.WFSteps_ID + ",CurrentStepName:'" + x.WFStep_Name + "'}";
                        //}
                        //jsonResult += "]}";
                    }
                }
                catch (Exception e)
                {
                }

                return Json(jsonResult, JsonRequestBehavior.AllowGet);
            }

            //-----To handle Multiple Begin Step issue in Interface
            public ActionResult GetStatusofWorkFlowInterface(int transactionNumber, int workFlowID,int CompID)
            {
                dynamic dummy = null;
                var jsonResult = dummy;
                var arr = dummy;
                WF_WFSteps step = null;
                try
                {
                    WF_WFCase_Progress gnmCaseProgress = client.WF_WFCase_Progress.OrderByDescending(a => a.WFCaseProgress_ID).Where(a => a.Transaction_ID == transactionNumber && a.WorkFlow_ID == workFlowID).FirstOrDefault();

                    List<WF_WFSteps> stepList = client.WF_WFSteps.Where(i => i.WorkFlow_ID == workFlowID).ToList();
                    if (gnmCaseProgress != null)
                    {
                        step = client.WF_WFSteps.Where(i => i.WFSteps_ID == gnmCaseProgress.WFSteps_ID).FirstOrDefault();

                        jsonResult = "{CurrentStepID:" + step.WFSteps_ID + ",CurrentStepName:'" + step.WFStep_Name + "'}";
                    }
                    else
                    {
                        arr = from a in stepList                              
                              join b in client.WF_WFStepType on a.WFStepType_ID equals b.WFStepType_ID
                              where b.WFStepType_Nm.ToUpper() == "BEGIN"
                              select new
                              {                                 
                                  a.WFSteps_ID,
                                  a.WFStep_Name
                              };
                        foreach (var x in arr)
                        {
                             int WFSteps_ID = x.WFSteps_ID;
                             if (client.WF_WFStepLink.Where(s => s.FrmWFSteps_ID == WFSteps_ID && s.Company_ID == CompID).ToList().Count > 0)
                            {
                                jsonResult = "{CurrentStepID:" + x.WFSteps_ID + ",CurrentStepName:'" + x.WFStep_Name + "'}";
                            }
                        }
                    }
                }
                catch (Exception e)
                {
                }

                return Json(jsonResult, JsonRequestBehavior.AllowGet);
            }

            #endregion

            #region ::: GetMovementofWorkFlow:::
            /// <Author>"Kiran NR"</Author>
            /// <CreatedDate>"11-10-2012"</CreatedDate>
            /// <Purpose >"To bring the movement of the work flow for a transaction number"</Purpose>
            /// <returns></returns> 
            public List<MovementHistory> GetMovementofWorkFlow(int transactionNumber, int workFlowID,int UserLanguageID)
            {
                dynamic s = null;
                var MovementFlow = s;
                int Addresse = 0;
                int actionID = 0;
                string userName = string.Empty;
                string AssignTO = string.Empty;
                List<WF_WFCase_Progress> gnmCaseProgressMovement = null;
                List<MovementHistory> listMovement = new List<MovementHistory>();
                int ID = 0;

                try
                {
                    gnmCaseProgressMovement = client.WF_WFCase_Progress.Where(a => a.Transaction_ID == transactionNumber && a.WorkFlow_ID == workFlowID).ToList();
                    if (UserLanguageID == 0)
                    {
                        var historyList = from a in gnmCaseProgressMovement
                                          join b in client.WF_WorkFlow on a.WorkFlow_ID equals b.WorkFlow_ID
                                          join c in client.WF_WFSteps on a.WFSteps_ID equals c.WFSteps_ID
                                          //join d in client.WF_WFAction on a.Action_Chosen equals d.WFAction_ID
                                          select new
                                          {
                                              a.Action_Chosen,
                                              a.Action_Remarks,
                                              a.Action_Time,
                                              a.Actioned_By,
                                              a.Addresse_Flag,
                                              a.Addresse_ID,
                                              a.Received_Time,
                                              a.Transaction_ID,
                                              a.WFCaseProgress_ID,
                                              a.WFSteps_ID,
                                              a.WorkFlow_ID,
                                              b.WorkFlow_Name,
                                              c.WFStep_Name,
                                              //d.WFAction_Name
                                          };

                        foreach (var obj in historyList)
                        {
                            MovementHistory row = new MovementHistory();
                            if (obj.Action_Chosen != null)
                            {
                                row.Action_Chosen = (int)obj.Action_Chosen;
                            }
                            row.Action_Remarks = obj.Action_Remarks;
                            if (obj.Action_Time != null)
                            {
                                row.Action_Time = Convert.ToDateTime(obj.Action_Time).ToString();
                            }
                            else
                            {
                                row.Action_Time = "";
                            }
                            if (obj.Actioned_By != null)
                            {
                                row.Actioned_By = (int)obj.Actioned_By;
                            }
                            row.Addresse_ID = (int)obj.Addresse_ID;
                            row.Received_Time = (DateTime)obj.Received_Time;
                            row.Transaction_ID = obj.Transaction_ID;
                            row.WFCaseProgress_ID = obj.WFCaseProgress_ID;
                            row.WFStep_Name = obj.WFStep_Name;
                            row.WFSteps_ID = obj.WFSteps_ID;
                            row.WorkFlow_ID = obj.WorkFlow_ID;
                            row.WorkFlow_Name = obj.WorkFlow_Name;
                            row.Addresse_Flag = Convert.ToByte(obj.Addresse_Flag);
                            listMovement.Add(row);
                        }
                        for (int i = 0; i < listMovement.Count; i++)
                        {
                            Addresse = listMovement[i].Actioned_By;
                            userName = genEnt.WF_User.Where(a => a.User_ID == Addresse).Select(a => a.User_Name).FirstOrDefault();
                            listMovement[i].UserorRoleName = userName;

                            actionID = listMovement[i].Action_Chosen;
                            listMovement[i].WFAction_Name = client.WF_WFAction.Where(a => a.WFAction_ID == actionID).Select(a => a.WFAction_Name).FirstOrDefault();

                            ID = listMovement[i].Addresse_ID;
                            if (listMovement[i].Addresse_Flag == 1)
                            {
                                AssignTO = genEnt.WF_User.Where(a => a.User_ID == ID).Select(a => a.User_Name).FirstOrDefault();
                                listMovement[i].AssignTO = AssignTO;
                            }
                            else
                            {
                                WF_WFRole TO = client.WF_WFRole.Where(a => a.WFRole_ID == ID).FirstOrDefault();
                                listMovement[i].AssignTO = (TO == null) ? "" : TO.WFRole_Name;
                            }
                        }
                    }
                    else {
                        var historyList = from a in gnmCaseProgressMovement
                                          join b in client.WF_WorkFlow on a.WorkFlow_ID equals b.WorkFlow_ID
                                          join c in client.WF_WFSteps on a.WFSteps_ID equals c.WFSteps_ID
                                          join d in client.WF_WFStepsLocale on c.WFSteps_ID equals d.WFSteps_ID
                                          select new
                                          {
                                              a.Action_Chosen,
                                              a.Action_Remarks,
                                              a.Action_Time,
                                              a.Actioned_By,
                                              a.Addresse_Flag,
                                              a.Addresse_ID,
                                              a.Received_Time,
                                              a.Transaction_ID,
                                              a.WFCaseProgress_ID,
                                              a.WFSteps_ID,
                                              a.WorkFlow_ID,
                                              b.WorkFlow_Name,
                                              d.WFStep_Name,
                                              //d.WFAction_Name
                                          };

                        foreach (var obj in historyList)
                        {
                            MovementHistory row = new MovementHistory();
                            if (obj.Action_Chosen != null)
                            {
                                row.Action_Chosen = (int)obj.Action_Chosen;
                            }
                            row.Action_Remarks = obj.Action_Remarks;
                            if (obj.Action_Time != null)
                            {
                                row.Action_Time = Convert.ToDateTime(obj.Action_Time).ToString();
                            }
                            else
                            {
                                row.Action_Time = "";
                            }
                            if (obj.Actioned_By != null)
                            {
                                row.Actioned_By = (int)obj.Actioned_By;
                            }
                            row.Addresse_ID = (int)obj.Addresse_ID;
                            row.Received_Time = (DateTime)obj.Received_Time;
                            row.Transaction_ID = obj.Transaction_ID;
                            row.WFCaseProgress_ID = obj.WFCaseProgress_ID;
                            row.WFStep_Name = obj.WFStep_Name;
                            row.WFSteps_ID = obj.WFSteps_ID;
                            row.WorkFlow_ID = obj.WorkFlow_ID;
                            row.WorkFlow_Name = obj.WorkFlow_Name;
                            row.Addresse_Flag = Convert.ToByte(obj.Addresse_Flag);
                            listMovement.Add(row);
                        }
                        for (int i = 0; i < listMovement.Count; i++)
                        {
                            Addresse = listMovement[i].Actioned_By;
                            userName = genEnt.WF_UserLocale.Where(a => a.User_ID == Addresse).Select(a => a.User_Name).FirstOrDefault();
                            listMovement[i].UserorRoleName = userName;

                            actionID = listMovement[i].Action_Chosen;
                            listMovement[i].WFAction_Name = client.WF_WFActionLocale.Where(a => a.WFAction_ID == actionID).Select(a => a.WFAction_Name).FirstOrDefault();

                            ID = listMovement[i].Addresse_ID;
                            if (listMovement[i].Addresse_Flag == 1)
                            {
                                AssignTO = genEnt.WF_UserLocale.Where(a => a.User_ID == ID).Select(a => a.User_Name).FirstOrDefault();
                                listMovement[i].AssignTO = AssignTO;
                            }
                            else
                            {
                                WF_WFRoleLocale TO = client.WF_WFRoleLocale.Where(a => a.WFRole_ID == ID).FirstOrDefault();
                                listMovement[i].AssignTO = (TO == null) ? "" : TO.WFRole_Name;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return listMovement;
            }
            #endregion

            #region ::: InsertWorkFlowHistory:::
            /// <Author>"Kiran NR"</Author>
            /// <CreatedDate>"11-10-2012"</CreatedDate>
            /// <Purpose >"To insert the movement history"</Purpose>
            /// <returns></returns> 
            public void insertWorkFlowHistory(CaseProgressObjects CPDetails, SMSTemplate SMSCutomerObj, SMSTemplate SMSAssigneeObj, int Branch_ID=0)
            {
                try
                {
                    int stepTypeID = 0;
                    List<WF_WFCase_Progress> gnmCaseProgress = client.WF_WFCase_Progress.OrderByDescending(a => a.WFCaseProgress_ID).Where(a => a.Transaction_ID == CPDetails.transactionNumber && a.WorkFlow_ID == CPDetails.workFlowID).ToList();
                    if (gnmCaseProgress.Count > 0)
                    {
                        stepTypeID = client.WF_WFSteps.Where(i => i.WFSteps_ID == CPDetails.NextStepID).Select(i => i.WFStepType_ID).FirstOrDefault();

                        gnmCaseProgress[0].Action_Chosen = CPDetails.actionID;
                        gnmCaseProgress[0].Action_Remarks = CPDetails.actionRemarks;
                        gnmCaseProgress[0].Actioned_By = CPDetails.actionBy;
                        gnmCaseProgress[0].Action_Time = Branch_ID != 0 ? LocalTime(Branch_ID, DateTime.Now) : DateTime.Now;
                        gnmCaseProgress[0].WFNextStep_ID = CPDetails.NextStepID;

                        client.SaveChanges();
                    }
                    else
                    {
                        stepTypeID = client.WF_WFSteps.Where(i => i.WFSteps_ID == CPDetails.currentStepID).Select(i => i.WFStepType_ID).FirstOrDefault();
                    }


                    string stepTypeName = client.WF_WFStepType.Where(i => i.WFStepType_ID == stepTypeID).Select(i => i.WFStepType_Nm).FirstOrDefault();
                    WF_WFStepLink stepLinkrowMail = client.WF_WFStepLink.Where(i => i.Company_ID == CPDetails.CompanyID && i.WorkFlow_ID == CPDetails.workFlowID && i.FrmWFSteps_ID == CPDetails.NextStepID).FirstOrDefault();

                    WF_WFStepLink stepLinkrow = client.WF_WFStepLink.Where(i => i.Company_ID == CPDetails.CompanyID && i.WorkFlow_ID == CPDetails.workFlowID && i.FrmWFSteps_ID == CPDetails.currentStepID && i.WFAction_ID == CPDetails.actionID && i.ToWFSteps_ID==CPDetails.NextStepID).FirstOrDefault();
                    if (stepTypeName.ToLower() != "End".ToLower())
                    {
                        WF_WFCase_Progress rowObj = new WF_WFCase_Progress();
                        rowObj.WorkFlow_ID = CPDetails.workFlowID;
                        rowObj.Transaction_ID = CPDetails.transactionNumber;
                        rowObj.WFSteps_ID = CPDetails.NextStepID;
                        rowObj.Addresse_ID = CPDetails.AssignTo;
                        rowObj.Addresse_Flag = CPDetails.addresseType;
                        rowObj.Received_Time = Branch_ID != 0 ? LocalTime(Branch_ID, DateTime.Now) : DateTime.Now; //CPDetails.receivedTime;
                        client.WF_WFCase_Progress.Add(rowObj);
                        client.SaveChanges();                        
                    }

                    if (CPDetails.currentStepID != CPDetails.NextStepID)
                    {
                        if (stepTypeName.ToLower() != "Static".ToLower())
                        {
                            if (stepLinkrow != null)
                            {
                                if (stepLinkrow.IsSMSSentToAddressee)
                                {
                                    if (stepLinkrowMail != null)
                                    {
                                        if (stepLinkrowMail.Addresse_Flag == 1)
                                        {
                                            int employeeID = (int)genEnt.WF_User.Where(i => i.User_ID == CPDetails.AssignTo).Select(i => i.Employee_ID).FirstOrDefault();
                                            string mobileNumber = genEnt.WF_CompanyEmployee.Where(i => i.Company_Employee_ID == employeeID).Select(i => i.Company_Employee_MobileNumber).FirstOrDefault();

                                            if (mobileNumber != null && mobileNumber.Trim() != "" )//Added by Shashi
                                            {
                                                WF_Sms newSMSRow = new WF_Sms();
                                                newSMSRow.Sms_Text = CPDetails.smsTextAddressee;
                                                newSMSRow.Sms_Queue_Date = Branch_ID != 0 ? LocalTime(Branch_ID, DateTime.Now) : DateTime.Now;
                                                newSMSRow.Sms_Mobile_Number = mobileNumber;
                                                newSMSRow.Parameter1_value = SMSAssigneeObj.Param1;
                                                newSMSRow.Parameter2_value = SMSAssigneeObj.Param2;
                                                newSMSRow.Parameter3_value = SMSAssigneeObj.Param3;
                                                newSMSRow.Parameter4_value = SMSAssigneeObj.Param4;
                                                newSMSRow.Template_ID = SMSAssigneeObj.Template_ID;
                                                client.WF_Sms.Add(newSMSRow);
                                                client.SaveChanges();
                                            }
                                        }
                                    }
                                    //else if (stepLinkrowMail.Addresse_Flag == 0)
                                    //{
                                    //    List<ResultForAction> ResAction = GetListOfStaffForaRoleID(CPDetails.CompanyID, CPDetails.RoleID, 0, CPDetails.currentStepID, CPDetails.actionID);

                                    //    foreach (var c in ResAction)
                                    //    {
                                    //        int employeeID = (int)genEnt.WF_User.Where(i => i.User_ID == c.ID).Select(i => i.Employee_ID).FirstOrDefault();
                                    //        string mobileNumber = genEnt.WF_CompanyEmployee.Where(i => i.Company_Employee_ID == employeeID).Select(i => i.Company_Employee_MobileNumber).FirstOrDefault();

                                    //        WF_Sms newSMSRow = new WF_Sms();
                                    //        newSMSRow.Sms_Text = CPDetails.smsTextAddressee;
                                    //        newSMSRow.Sms_Queue_Date = DateTime.Now;
                                    //        newSMSRow.Sms_Mobile_Number = mobileNumber;
                                    //        client.WF_Sms.Add(newSMSRow);
                                    //        client.SaveChanges();
                                    //    }
                                    //}
                                }
                                if (stepLinkrow.IsSMSSentToCustomer)
                                {
                                    if (CPDetails.customerMobileNumber != null && CPDetails.customerMobileNumber.Trim() != "")//Added by Shashi
                                    {
                                        WF_Sms newSMSRow = new WF_Sms();
                                        newSMSRow.Sms_Text = CPDetails.smsTextCustomer;
                                        newSMSRow.Sms_Queue_Date = Branch_ID != 0 ? LocalTime(Branch_ID, DateTime.Now) : DateTime.Now;
                                        newSMSRow.Sms_Mobile_Number = CPDetails.customerMobileNumber;
                                        newSMSRow.Parameter1_value = SMSCutomerObj.Param1;
                                        newSMSRow.Parameter2_value = SMSCutomerObj.Param2;
                                        newSMSRow.Parameter3_value = SMSCutomerObj.Param3;
                                        newSMSRow.Parameter4_value = SMSCutomerObj.Param4;
                                        newSMSRow.Template_ID = SMSCutomerObj.Template_ID;
                                        client.WF_Sms.Add(newSMSRow);
                                        client.SaveChanges();
                                    }
                                }
                                if (stepLinkrow.IsEmailSentToAddresse)
                                {
                                    if (stepLinkrowMail != null)
                                    {
                                        if (stepLinkrowMail.Addresse_Flag == 1)
                                        {
                                            int employeeID = (int)genEnt.WF_User.Where(i => i.User_ID == CPDetails.AssignTo).Select(i => i.Employee_ID).FirstOrDefault();
                                            string emailID = genEnt.WF_CompanyEmployee.Where(i => i.Company_Employee_ID == employeeID).Select(i => i.Company_Employee_Email).FirstOrDefault();

                                            WF_Email newEmailRow = new WF_Email();
                                            newEmailRow.Email_To = emailID;
                                            newEmailRow.Email_Subject = CPDetails.emailSubAddressee;
                                            newEmailRow.Email_Body = CPDetails.emailBodyAddress;
                                            //added by Kavitha-start
                                            newEmailRow.Email_Bcc = CPDetails.AddresseBcc;
                                            newEmailRow.Email_cc = CPDetails.AddresseCC;
                                            newEmailRow.Email_IsError = false;
                                            newEmailRow.NoOfAttempts = 0;
                                            //added by Kavitha-end
                                            newEmailRow.Email_Queue_Date = Branch_ID != 0 ? LocalTime(Branch_ID, DateTime.Now) : DateTime.Now;
                                            client.WF_Email.Add(newEmailRow);
                                            client.SaveChanges();
                                        }
                                    }
                                    //else if (stepLinkrowMail.Addresse_Flag == 0)
                                    //{
                                    //    List<ResultForAction> ResAction = GetListOfStaffForaRoleID(CPDetails.CompanyID, CPDetails.RoleID, 0, CPDetails.currentStepID, CPDetails.actionID);

                                    //    foreach (var c in ResAction)
                                    //    {
                                    //        int employeeID = (int)genEnt.WF_User.Where(i => i.User_ID == c.ID).Select(i => i.Employee_ID).FirstOrDefault();
                                    //        string emailID = genEnt.WF_CompanyEmployee.Where(i => i.Company_Employee_ID == employeeID).Select(i => i.Company_Employee_Email).FirstOrDefault();

                                    //        WF_Email newEmailRow = new WF_Email();
                                    //        newEmailRow.Email_To = emailID;
                                    //        newEmailRow.Email_Subject = CPDetails.emailSubAddressee;
                                    //        newEmailRow.Email_Body = CPDetails.emailBodyAddress;
                                    //        newEmailRow.Email_Queue_Date = DateTime.Now;
                                    //        client.WF_Email.Add(newEmailRow);
                                    //        client.SaveChanges();
                                    //    }
                                    //}

                                }
                                if (stepLinkrow.IsEmailSentToCustomer)
                                {
                                    WF_Email newEmailRow = new WF_Email();
                                    newEmailRow.Email_To = CPDetails.customerEmailID;
                                    newEmailRow.Email_Subject = CPDetails.emailSubCustomer;
                                    newEmailRow.Email_Body = CPDetails.emailBodyCustomer;
                                    //added by Kavitha-start
                                    newEmailRow.Email_Bcc = CPDetails.CustomerBcc;
                                    newEmailRow.Email_cc = CPDetails.customerCC;
                                    newEmailRow.Email_IsError = false;
                                    newEmailRow.NoOfAttempts = 0;
                                    //added by Kavitha-end
                                    newEmailRow.Email_Queue_Date = Branch_ID != 0 ? LocalTime(Branch_ID, DateTime.Now) : DateTime.Now;
                                    client.WF_Email.Add(newEmailRow);
                                    client.SaveChanges();
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            #endregion        

            #region ::: GetActionsForTheStep:::
            /// <Author>"Kiran NR"</Author>
            /// <CreatedDate>"11-10-2012"</CreatedDate>
            /// <Purpose >"To bring the actions associated with the work flow"</Purpose>
            /// <returns></returns> 
            public ActionResult GetActionsForTheStep(int companyID, int stepID, int workFlowID, int UserLanguageID)
            {
                dynamic s = null;
                var x = s;
                try
                {
                    List<WF_WFStepLink> listStepLink = client.WF_WFStepLink.Where(a => a.FrmWFSteps_ID == stepID && a.WorkFlow_ID == workFlowID && a.Company_ID == companyID).ToList();
                    if (UserLanguageID == 0)
                    {
                        var arr = (from a in listStepLink
                                   join b in client.WF_WFAction on a.WFAction_ID equals b.WFAction_ID
                                   join c in client.WF_WFRole on a.Addresse_WFRole_ID equals c.WFRole_ID
                                   orderby b.WFAction_Name
                                   select new
                                   {
                                       b.WFAction_ID,
                                       b.WFAction_Name,
                                       a.Addresse_WFRole_ID,
                                       c.WFRole_Name,
                                       a.Addresse_Flag
                                   }).Distinct();

                        x = new { Actions = arr.ToArray() };
                    }
                    else {
                        var arr = (from a in listStepLink
                                   join b in client.WF_WFAction on a.WFAction_ID equals b.WFAction_ID
                                   join c in client.WF_WFRole on a.Addresse_WFRole_ID equals c.WFRole_ID
                                   join L in client.WF_WFActionLocale on b.WFAction_ID equals L.WFAction_ID
                                   where L.Language_ID==UserLanguageID
                                   orderby b.WFAction_Name
                                   select new
                                   {
                                       b.WFAction_ID,
                                       L.WFAction_Name,
                                       a.Addresse_WFRole_ID,
                                       c.WFRole_Name,
                                       a.Addresse_Flag
                                   }).Distinct();

                        x = new { Actions = arr.ToArray() };
                    }
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return Json(x, JsonRequestBehavior.AllowGet);
            }
            #endregion

            #region ::: GetAssociatedRolesForaStep:::
            /// <Author>"Kiran NR"</Author>
            /// <CreatedDate>"11-10-2012"</CreatedDate>
            /// <Purpose >"To bring the associated roles with the steps of the work flow "</Purpose>
            /// <returns></returns> 
            public List<ResultForAction> GetAssociatedRolesForaStep(int companyID, int stepID, int actionID, int workFlowID,int UserLanguageID)
            {
                dynamic s = null;
                var x = s;
                IEnumerable<WF_WFStepLink> listToSteps = null;
                List<WF_WFStepLink> listSteps = new List<WF_WFStepLink>();
                List<ResultForAction> resultList = new List<ResultForAction>();
                try
                {
                    listToSteps = client.WF_WFStepLink.Where(i => i.FrmWFSteps_ID == stepID && i.Company_ID == companyID && i.WFAction_ID == actionID && i.WorkFlow_ID == workFlowID);
                    bool isVersionEnabled = false;
                    isVersionEnabled = Convert.ToBoolean(client.WF_WFStepLink.Where(i => i.Company_ID == companyID && i.FrmWFSteps_ID == stepID && i.WFAction_ID == actionID).Select(i => i.IsVersionEnabled).FirstOrDefault());

                    foreach (var i in listToSteps)
                    {
                        listSteps.AddRange(client.WF_WFStepLink.Where(a => a.FrmWFSteps_ID == i.ToWFSteps_ID && a.Company_ID == companyID && a.WorkFlow_ID == workFlowID).ToList());
                    }
                    if (UserLanguageID == 0)
                    {
                        var arr = ((from a in listSteps
                                    join b in client.WF_WFRole on a.Addresse_WFRole_ID equals b.WFRole_ID
                                    select new
                                    {
                                        ID = b.WFRole_ID,
                                        Name = b.WFRole_Name
                                    }).Distinct()).ToList();

                        foreach (var a in arr)
                        {
                            ResultForAction result = new ResultForAction();
                            result.ID = a.ID;
                            result.Name = a.Name;
                            result.isVersionEnabled = isVersionEnabled;
                            resultList.Add(result);
                        }

                        x = new { Actions = arr.ToArray(), isVersionEnabled = isVersionEnabled };
                    }
                    else {
                        var arr = ((from a in listSteps
                                    join b in client.WF_WFRoleLocale on a.Addresse_WFRole_ID equals b.WFRole_ID
                                    where b.Language_ID==UserLanguageID
                                    select new
                                    {
                                        ID = b.WFRole_ID,
                                        Name = b.WFRole_Name
                                    }).Distinct()).ToList();

                        foreach (var a in arr)
                        {
                            ResultForAction result = new ResultForAction();
                            result.ID = a.ID;
                            result.Name = a.Name;
                            result.isVersionEnabled = isVersionEnabled;
                            resultList.Add(result);
                        }

                        x = new { Actions = arr.ToArray(), isVersionEnabled = isVersionEnabled };
                    }
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return Json(x, JsonRequestBehavior.AllowGet);
                return resultList;
            }
            #endregion

            #region ::: GetListOfStaffForaRoleID:::
            /// <Author>"Kiran NR"</Author>
            /// <CreatedDate>"11-10-2012"</CreatedDate>
            /// <Purpose >"To bring the users associated withe the role"</Purpose>
            /// <returns></returns> 
            public List<ResultForAction> GetListOfStaffForaRoleID(int companyID, int roleID, int transactionValue, int stepID, int actionID, int UserLanguageID)
            {
                dynamic s = null;
                var x = s;
                List<ResultForAction> resultList = new List<ResultForAction>();

                try
                {
                    //List<WF_WFRoleUser> listRoleUser = client.WF_WFRoleUser.Where(a => a.WFRole_ID == roleID).ToList();
                    //IEnumerable<WF_User> usr = genEnt.WF_User.Where(u => u.Company_ID == companyID);
                    //bool isVersionEnabled = false;
                    //isVersionEnabled = Convert.ToBoolean(client.WF_WFStepLink.Where(i => i.Company_ID == companyID && i.FrmWFSteps_ID == stepID && i.WFAction_ID == actionID).Select(i => i.IsVersionEnabled).FirstOrDefault());
                    //var arr = ((from a in listRoleUser
                    //            join b in usr on a.UserID equals b.User_ID
                    //            join c in client.WF_WFRole on a.WFRole_ID equals c.WFRole_ID
                    //            join d in client.WF_WFStepLink on c.WorkFlow_ID equals d.WorkFlow_ID
                    //            where (d.Company_ID == companyID && d.FrmWFSteps_ID == stepID && d.WFAction_ID == actionID) && b.User_IsActive == true
                    //            select new
                    //            {
                    //                a.WFRole_ID,
                    //                ID = b.User_ID,
                    //                Name = b.User_Name,
                    //                b.User_LoginID

                    //            }).Distinct()).ToList();

                    //foreach (var a in arr)
                    //{
                    //    ResultForAction result = new ResultForAction();
                    //    result.ID = a.ID;
                    //    result.Name = a.Name;
                    //    result.isVersionEnabled = isVersionEnabled;
                    //    resultList.Add(result);
                    //}


                    //---- Changing to handle External Que--
                    int ExtCompID =Convert.ToInt32(client.WF_WFRole.Where(a => a.WFRole_ID == roleID).Select(a => a.WFRole_ExternalCompany_ID).FirstOrDefault());

                    List<WF_WFRoleUser> listRoleUser = client.WF_WFRoleUser.Where(a => a.WFRole_ID == roleID).ToList();
                    IEnumerable<WF_User> usr = genEnt.WF_User.Where(u => u.Company_ID == ExtCompID && u.User_IsActive==true);
                    bool isVersionEnabled = false;
                    isVersionEnabled = Convert.ToBoolean(client.WF_WFStepLink.Where(i => i.Company_ID == companyID && i.FrmWFSteps_ID == stepID && i.WFAction_ID == actionID).Select(i => i.IsVersionEnabled).FirstOrDefault());
                    if (UserLanguageID==0)
                    {
                        var arr = ((from a in listRoleUser
                                    join b in usr on a.UserID equals b.User_ID
                                    select new
                                    {
                                        a.WFRole_ID,
                                        ID = b.User_ID,
                                        Name = b.User_Name,
                                        b.User_LoginID

                                    }).Distinct()).ToList();

                        foreach (var a in arr)
                        {
                            ResultForAction result = new ResultForAction();
                            result.ID = a.ID;
                            result.Name = a.Name;
                            result.isVersionEnabled = isVersionEnabled;
                            resultList.Add(result);
                        }
                    }
                    else {
                        var arr = ((from a in listRoleUser
                                    join b in usr on a.UserID equals b.User_ID
                                    join L in genEnt.WF_UserLocale on b.User_ID equals L.User_ID
                                    where L.Language_ID==UserLanguageID
                                    select new
                                    {
                                        a.WFRole_ID,
                                        ID = b.User_ID,
                                        Name = L.User_Name,
                                        b.User_LoginID

                                    }).Distinct()).ToList();

                        foreach (var a in arr)
                        {
                            ResultForAction result = new ResultForAction();
                            result.ID = a.ID;
                            result.Name = a.Name;
                            result.isVersionEnabled = isVersionEnabled;
                            resultList.Add(result);
                        }
                    }

                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return resultList;
            }
            #endregion

            #region ::: GetStepLink:::
            /// <Author>"Kiran NR"</Author>
            /// <CreatedDate>"11-10-2012"</CreatedDate>
            /// <Purpose >"To bring the actions associated with the work flow"</Purpose>
            /// <returns></returns> 
            public WF_WFStepLink GetStepLink(int companyID, int workFlowID, int stepID, int ActionID)
            {
                int toStep = 0;
                WF_WFStepLink StepL = null;
                try
                {
                    toStep = client.WF_WFStepLink.Where(a => a.FrmWFSteps_ID == stepID && a.WorkFlow_ID == workFlowID && a.Company_ID == companyID && a.WFAction_ID == ActionID).Select(i => i.ToWFSteps_ID).FirstOrDefault();
                    StepL = client.WF_WFStepLink.Where(a => a.FrmWFSteps_ID == toStep && a.WorkFlow_ID == workFlowID && a.Company_ID == companyID).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return StepL;
            }
            #endregion

            #region ::: Get multiple StepLink:::
            /// <Author>"Kiran NR"</Author>
            /// <CreatedDate>"11-10-2012"</CreatedDate>
            /// <Purpose >"To bring the actions associated with the work flow"</Purpose>
            /// <returns></returns> 
            public List<WF_WFStepLink> GetStepLinkForInsert(int companyID, int workFlowID, int stepID, int ActionID)
            {
                // int toStep = 0;
                IEnumerable<WF_WFStepLink> ToStepLink = null;
                List<WF_WFStepLink> StepL = new List<WF_WFStepLink>();
                try
                {
                    //toStep = client.WF_WFStepLink.Where(a => a.FrmWFSteps_ID == stepID && a.WorkFlow_ID == workFlowID && a.Company_ID == companyID && a.WFAction_ID == ActionID).Select(i => i.ToWFSteps_ID).FirstOrDefault();
                    ToStepLink = client.WF_WFStepLink.Where(a => a.FrmWFSteps_ID == stepID && a.WorkFlow_ID == workFlowID && a.Company_ID == companyID && a.WFAction_ID == ActionID);

                    foreach (var SL in ToStepLink)
                    {
                        StepL.AddRange(client.WF_WFStepLink.Where(a => a.FrmWFSteps_ID == SL.ToWFSteps_ID && a.WorkFlow_ID == workFlowID && a.Company_ID == companyID).ToList());
                    }


                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return StepL;
            }
            #endregion

            #region ::: GetNextStepType:::
            /// <Author>"Kiran NR"</Author>
            /// <CreatedDate>"11-10-2012"</CreatedDate>
            /// <Purpose >"To bring the actions associated with the work flow"</Purpose>
            /// <returns></returns> 
            public JsonResult GetNextStepType(int companyID, int workFlowID, int stepID, int ActionID)
            {
                dynamic dummy = null;
                var jsonResult = dummy;
                int ToStepType = 0;
                int ToStepStatus = 0;
                string StepTypeName = string.Empty;
                WF_WFStepLink Slink = null;
                try
                {
                    if (ActionID > 0)
                    {
                        Slink = client.WF_WFStepLink.Where(a => a.FrmWFSteps_ID == stepID && a.WorkFlow_ID == workFlowID && a.Company_ID == companyID && a.WFAction_ID == ActionID).FirstOrDefault();
                        ToStepType = client.WF_WFSteps.Where(id => id.WFSteps_ID == Slink.ToWFSteps_ID).Select(a => a.WFStepType_ID).FirstOrDefault();
                        ToStepStatus = client.WF_WFSteps.Where(id => id.WFSteps_ID == Slink.ToWFSteps_ID).Select(a => a.WFStepStatus_ID).FirstOrDefault();
                        StepTypeName = client.WF_WFStepType.Where(name => name.WFStepType_ID == ToStepType).Select(a => a.WFStepType_Nm).FirstOrDefault();

                        jsonResult = "{NextStepID:" + Slink.ToWFSteps_ID + ",ToStepStatus:'" + ToStepStatus + "', NextStepType:'" + StepTypeName + "', RoleID:'" + Slink.Addresse_WFRole_ID + "'}";
                    }
                    else
                    {
                        ToStepType = client.WF_WFSteps.Where(id => id.WFSteps_ID == stepID).Select(a => a.WFStepType_ID).FirstOrDefault();
                        ToStepStatus = client.WF_WFSteps.Where(id => id.WFSteps_ID == stepID).Select(a => a.WFStepStatus_ID).FirstOrDefault();
                        StepTypeName = client.WF_WFStepType.Where(name => name.WFStepType_ID == ToStepType).Select(a => a.WFStepType_Nm).FirstOrDefault();

                        jsonResult = "{NextStepID:" + stepID + ",ToStepStatus:'" + ToStepStatus + "', NextStepType:'" + StepTypeName + "',RoleID:'" + 0 + "'}";
                    }
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return Json(jsonResult, JsonRequestBehavior.AllowGet);
            }
            #endregion

            public List<Indicator> GetGroupQueue(int companyID, int workFlowID, int userID)
            {
                string inCondition = string.Empty;
                List<Indicator> RowInd = new List<Indicator>();
                IEnumerable<WF_WFStepLink> Stlink = client.WF_WFStepLink.Where(st => st.Company_ID == companyID);
                dynamic dummy = null;
                var list = dummy;

                try
                {
                    List<int> roleID = client.WF_WFRoleUser.OrderByDescending(i => i.WFRoleUser_ID).Where(i => i.UserID == userID).Select(i => i.WFRole_ID).ToList();
                    for (int i = 0; i < roleID.Count; i++)
                    {
                        if (i != (roleID.Count - 1))
                        {
                            inCondition += roleID[i] + ",";
                        }
                        else
                        {
                            inCondition += roleID[i];
                        }
                    }
                    string query = "select * from GNM_WFCase_Progress where (Action_Chosen =0 or Action_Chosen is null) and WorkFlow_ID='" + workFlowID + "' and Addresse_ID in (" + inCondition + ") and Addresse_Flag=0";
                    List<WF_WFCase_Progress> transactionList = client.Database.SqlQuery(typeof(WF_WFCase_Progress), query).Cast<WF_WFCase_Progress>().ToList();

                    list = (from a in transactionList
                            join b in Stlink on a.WorkFlow_ID equals b.WorkFlow_ID
                            where b.WorkFlow_ID == workFlowID
                            select new
                            {
                                a.Transaction_ID,
                            }).Distinct();

                    foreach (var a in list)
                    {
                        Indicator RInd = new Indicator();
                        RInd.TransactionID = a.Transaction_ID;
                        RInd.IndicatorType = 0;
                        RInd.IsLock = false;
                        RowInd.Add(RInd);
                    }
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return RowInd;
            }

            public List<Indicator> GetAllQueue(int companyID, int workFlowID, int userID, int StatusID, int branchID)
            {
                bool isBranchSpecific = Convert.ToBoolean(client.WF_WorkFlow.Where(i => i.WorkFlow_ID == workFlowID).Select(i => i.AllQueue_Filter_IsBranch).FirstOrDefault());
                List<Indicator> RowInd = new List<Indicator>();
                WF_WFStepStatus StepStutus = null;
                IEnumerable<WF_WFStepLink> Stlink = client.WF_WFStepLink.Where(st => st.Company_ID == companyID);
                int ID = 0;
                dynamic dummy = null;
                var list = dummy;
                int EndStepTypeID = 0;
                try
                {
                    List<int> CloseStepID;
                    EndStepTypeID = client.WF_WFStepType.Where(st => st.WFStepType_Nm.ToUpper() == "END").Select(s => s.WFStepType_ID).FirstOrDefault();
                    //if (workFlowID == 2) 
                    //{
                    //    CloseStepID = client.WF_WFSteps.Where(st => st.WFStepType_ID == EndStepTypeID && st.WorkFlow_ID == workFlowID && st.WFStep_Name.ToUpper()=="CLOSED").Select(i => i.WFSteps_ID).ToList<int>(); 
                    //}
                    //else
                    //{
                        CloseStepID = client.WF_WFSteps.Where(st => st.WFStepType_ID == EndStepTypeID && st.WorkFlow_ID == workFlowID).Select(i => i.WFSteps_ID).ToList<int>();
                    //}
                    List<int> CloseStepIDStore = new List<int>();
                    CloseStepIDStore.AddRange(CloseStepID);
                    List<WF_WFCase_Progress> transactionList = new List<WF_WFCase_Progress>();
                    IEnumerable<WF_WFSteps> Steps = null;

                    int roleID = (from user in client.WF_WFRoleUser
                                  join role in client.WF_WFRole on user.WFRole_ID equals role.WFRole_ID
                                  where role.WorkFlow_ID == workFlowID && user.UserID == userID
                                  select new
                                  {
                                      user.WFRole_ID
                                  }).ToArray().Select(w => w.WFRole_ID).FirstOrDefault();


                    if (StatusID == 0)
                    {
                        transactionList = (from a in client.WF_WFCase_Progress
                                           where (CloseStepID.Contains((a.WFNextStep_ID.HasValue ? a.WFNextStep_ID.Value : 0)) || a.Action_Chosen == null) && a.WorkFlow_ID == workFlowID
                                           select a).ToList<WF_WFCase_Progress>();
                    }
                    else
                    {
                        StepStutus = client.WF_WFStepStatus.Where(ss => ss.WFStepStatus_ID == StatusID).FirstOrDefault();
                        Steps = client.WF_WFSteps.Where(st => st.WFStepStatus_ID == StepStutus.WFStepStatus_ID && st.WorkFlow_ID == workFlowID);

                        foreach (var step in Steps)
                        {
                            int x = 0;
                            while (x < CloseStepID.Count())
                            {
                                if (step.WFSteps_ID == CloseStepID[x])
                                {
                                    ID = CloseStepID[x];
                                    transactionList.AddRange(client.WF_WFCase_Progress.Where(cp => cp.WorkFlow_ID == workFlowID && cp.WFNextStep_ID == ID).ToList());
                                    CloseStepID.RemoveAt(0);
                                    x = 0;
                                }
                                else
                                {
                                    transactionList.AddRange(client.WF_WFCase_Progress.Where(cp => cp.WFSteps_ID == step.WFSteps_ID && cp.WorkFlow_ID == workFlowID && (cp.Action_Chosen == null)).ToList());
                                    break;
                                }
                            }
                        }
                    }

                    list = (from a in transactionList
                            join b in Stlink on a.WorkFlow_ID equals b.WorkFlow_ID
                            where b.WorkFlow_ID == workFlowID
                            select new
                            {
                                a.Transaction_ID,
                                a.Actioned_By,
                                a.Addresse_ID,
                                indicator = (CloseStepIDStore.Contains((a.WFNextStep_ID.HasValue ? a.WFNextStep_ID.Value : 0)) ? 3 : (a.Addresse_ID == userID && a.Addresse_Flag == 1) ? 1 : ((a.Addresse_ID == roleID && a.Addresse_Flag == 0) ? 2 : 4))
                            }).Distinct();

                    foreach (var a in list)
                    {
                        Indicator RInd = new Indicator();
                        RInd.TransactionID = a.Transaction_ID;
                        RInd.IndicatorType = a.indicator;
                        RInd.IsLock = false;
                        RowInd.Add(RInd);
                    }
                }
                catch (Exception ex)
                {
                }
                return RowInd;
            }


            public string LockTransaction(int companyID, int workFlowID, int transactionNumber, int userID, int transactionValue, int Branch_ID=0)
            {
                string status = string.Empty;
                try
                {
                    int lockID = client.WF_WFAction.Where(i => i.WFAction_Name == "Lock").Select(i => i.WFAction_ID).FirstOrDefault();
                    WF_WFRoleUser listRoleUser = client.WF_WFRoleUser.Where(a => a.UserID == userID).FirstOrDefault();

                    WF_WFCase_Progress lastRow = client.WF_WFCase_Progress.OrderByDescending(i => i.WFCaseProgress_ID).Where(i => i.WorkFlow_ID == workFlowID && i.Transaction_ID == transactionNumber).FirstOrDefault();
                    if (lastRow != null)
                    {
                        if (lastRow.Action_Chosen != lockID)
                        {
                            lastRow.Actioned_By = userID;
                            lastRow.Action_Time = Branch_ID != 0 ? LocalTime(Branch_ID, DateTime.Now) : DateTime.Now; 
                            lastRow.Action_Chosen = lockID;
                            client.SaveChanges();

                            WF_WFCase_Progress newRow = new WF_WFCase_Progress();
                            newRow.Addresse_Flag = 1;
                            newRow.Addresse_ID = userID;
                            newRow.WFSteps_ID = lastRow.WFSteps_ID;
                            newRow.Received_Time = Branch_ID != 0 ? LocalTime(Branch_ID, DateTime.Now) : DateTime.Now;
                            newRow.WorkFlow_ID = workFlowID;
                            newRow.Transaction_ID = transactionNumber;
                            newRow.Locked_Ind = true;
                            client.WF_WFCase_Progress.Add(newRow);
                            client.SaveChanges();
                            status = "TransactionLockedSuccessfully";
                        }
                        else
                        {

                            status = "TransactionisalreadybeenLocked";
                        }
                    }
                    else
                    {
                        status = "ErrorOccuredwhileLocking";
                    }
                }
                catch (Exception ex)
                {
                    status = "ErrorOccuredwhileUnLocking";
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return status;
            }

            public string UnLockTransaction(int companyID, int workFlowID, int transactionNumber, int userID, string remarks, int Branch_ID=0)
            {
                string status = string.Empty;
                try
                {
                    int lockID = client.WF_WFAction.Where(i => i.WFAction_Name.ToUpper() == "LOCK").Select(i => i.WFAction_ID).FirstOrDefault();
                    int unLockID = client.WF_WFAction.Where(i => i.WFAction_Name.ToUpper() == "UNLOCK").Select(i => i.WFAction_ID).FirstOrDefault();
                    WF_WFCase_Progress previousRow = client.WF_WFCase_Progress.OrderByDescending(i => i.WFCaseProgress_ID).Where(i => i.WorkFlow_ID == workFlowID && i.Transaction_ID == transactionNumber && i.Addresse_Flag == 0).FirstOrDefault();
                    WF_WFCase_Progress lastRow = client.WF_WFCase_Progress.OrderByDescending(i => i.WFCaseProgress_ID).Where(i => i.WorkFlow_ID == workFlowID && i.Transaction_ID == transactionNumber).FirstOrDefault();

                    byte addFlag = (previousRow == null) ? Convert.ToByte(1) : Convert.ToByte(0);

                    WF_WFCase_Progress previousRowN = client.WF_WFCase_Progress.OrderByDescending(i => i.WFCaseProgress_ID).Where(i => i.WorkFlow_ID == workFlowID && i.Transaction_ID == transactionNumber && i.Addresse_Flag == 1 && i.Action_Chosen != null).FirstOrDefault();
               

                    if (lastRow != null)
                    {
                        if (lastRow.Action_Chosen != unLockID)
                        {
                            lastRow.Action_Chosen = unLockID;
                            lastRow.Actioned_By = userID;
                            lastRow.Action_Time = Branch_ID != 0 ? LocalTime(Branch_ID, DateTime.Now) : DateTime.Now; 
                            lastRow.Action_Remarks = remarks;
                            client.SaveChanges();

                            WF_WFCase_Progress newRow = new WF_WFCase_Progress();
                            newRow.Addresse_Flag = addFlag;
                            newRow.WorkFlow_ID = workFlowID;
                            newRow.Transaction_ID = transactionNumber;
                            newRow.WFSteps_ID = lastRow.WFSteps_ID;
                            newRow.Addresse_ID = (previousRow == null) ? previousRowN.Addresse_ID : previousRow.Addresse_ID;
                            newRow.Received_Time = Branch_ID != 0 ? LocalTime(Branch_ID, DateTime.Now) : DateTime.Now;
                            client.WF_WFCase_Progress.Add(newRow);
                            client.SaveChanges();
                            status = "TransactionUnLockedSuccessfully";
                        }
                        else
                        {
                            status = "TransactionisalreadybeenUnLocked";
                        }
                    }
                    else
                    {
                        status = "ErrorOccuredwhileUnLocking";
                    }
                }
                catch (Exception ex)
                {
                    status = "ErrorOccuredwhileUnLocking";
                }
                return status;
            }

            public bool chkIsAddRecords(int ObjectID, int WorkFlowID, int CompanyID, int UserID)
            {
                bool isAdd = true;
                try
                {
                    int count = 0;
                    IEnumerable<WF_WFStepLink> stepLink = client.WF_WFStepLink.Where(s => s.WorkFlow_ID == WorkFlowID && s.Company_ID == CompanyID);
                    count = (from a in stepLink
                             join b in client.WF_WFRoleUser on a.Addresse_WFRole_ID equals b.WFRole_ID
                             join c in client.WF_WFSteps on a.FrmWFSteps_ID equals c.WFSteps_ID
                             where b.UserID == UserID && c.WFStepType_ID == 1
                             select new
                             {
                                 a.WFStepLink_ID
                             }).Count();
                    isAdd = (count > 0) ? true : false;
                }
                catch (Exception ex)
                {
                    isAdd = false;
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return isAdd;
            }

            public List<Indicator> GetMyQueue(int companyID, int workFlowID, int userID)
            {
                dynamic dummy = null;
                var jsonResult = dummy;
                List<Indicator> RowInd = new List<Indicator>();
                try
                {
                    List<WF_WFCase_Progress> transactionList = client.WF_WFCase_Progress.Where(i => (i.Action_Chosen == null) && i.WorkFlow_ID == workFlowID && i.Addresse_ID == userID && i.Addresse_Flag == 1).ToList();
                    var list = (from a in transactionList
                                join b in client.WF_WFStepLink on a.WorkFlow_ID equals b.WorkFlow_ID
                                where (b.Company_ID == companyID)
                                select new
                                {
                                    a.Transaction_ID,
                                    a.Locked_Ind
                                }).Distinct();

                    foreach (var a in list)
                    {
                        Indicator RInd = new Indicator();
                        RInd.TransactionID = a.Transaction_ID;
                        RInd.IndicatorType = 0;
                        RInd.IsLock = Convert.ToBoolean(a.Locked_Ind);
                        RowInd.Add(RInd);
                    }
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return RowInd;
            }

            public int GetStepTypeID(int stepID, int workFlowID)
            {
                int stepTypeID = 0;
                try
                {
                    stepTypeID = client.WF_WFSteps.Where(i => i.WFSteps_ID == stepID && i.WorkFlow_ID == workFlowID).Select(i => i.WFStepType_ID).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return stepTypeID;
            }

            public List<AutoAllocateResult> getAutoAllocationresult(int WorkFlowID, int UserID, int CompanyID)
            {
                List<AutoAllocateResult> result = new List<AutoAllocateResult>();
                List<AllocationLogic> Logic = new List<AllocationLogic>();
                WF_WFStepLink stepLink = null;
                IEnumerable<WF_WFCase_Progress> FromList = null;
                IEnumerable<WF_WFCase_Progress> ToList = null;

                IEnumerable<WF_WFRoleUser> RoleUser = null;
                IEnumerable<WF_User> User = null;
                int rowcount = 1;
                int RowID = 0;
                int RoleID = 0;

                try
                {
                    User = genEnt.WF_User.Where(u => u.Company_ID == CompanyID);
                    stepLink = GetAutoAllocationStepDetails(WorkFlowID, CompanyID);

                    if (stepLink != null)
                    {
                        FromList = client.WF_WFCase_Progress.Where(cp => cp.WFSteps_ID == stepLink.FrmWFSteps_ID && (cp.Action_Chosen == null) && cp.Addresse_Flag == 1 && cp.Addresse_ID == UserID && cp.WorkFlow_ID == WorkFlowID);
                        if (FromList != null)
                        {
                            foreach (var TranList in FromList)
                            {
                                AutoAllocateResult Tr = new AutoAllocateResult();
                                Tr.TransactionID = TranList.Transaction_ID;
                                result.Add(Tr);
                            }
                            RoleID = (int)client.WF_WFStepLink.Where(sl => sl.Company_ID == CompanyID && sl.WorkFlow_ID == WorkFlowID && sl.FrmWFSteps_ID == stepLink.ToWFSteps_ID).FirstOrDefault().Addresse_WFRole_ID;
                            RoleUser = client.WF_WFRoleUser.Where(ru => ru.WFRole_ID == RoleID);
                            var RoleUserComp = (from Ru in RoleUser
                                                join u in User on Ru.UserID equals u.User_ID
                                                select new
                                                {
                                                    Ru.UserID,
                                                    Ru.WFRole_ID,
                                                    Ru.WFRoleUser_ID
                                                });


                            foreach (var user in RoleUserComp)
                            {
                                ToList = client.WF_WFCase_Progress.Where(cp => (cp.Action_Chosen == null) && cp.Addresse_Flag == 1 && cp.Addresse_ID == user.UserID && cp.WorkFlow_ID == WorkFlowID);
                                AllocationLogic AL = new AllocationLogic();
                                AL.RowNumber = rowcount;
                                AL.UserID = user.UserID;
                                AL.Count = (ToList == null) ? 0 : ToList.Count();
                                AL.ReceivedTime = (ToList == null) ? DateTime.Now : ToList.OrderBy(l => l.Received_Time).Select(r => r.Received_Time).FirstOrDefault();
                                Logic.Add(AL);
                                rowcount++;
                            }

                            for (int i = 0; i < result.Count; i++)
                            {
                                result.ElementAt(i).UserID = Logic.OrderBy(row => row.Count).ThenBy(j => j.ReceivedTime).Select(r => r.UserID).FirstOrDefault();
                                RowID = Logic.OrderBy(row => row.Count).ThenBy(j => j.ReceivedTime).Select(r => r.RowNumber).FirstOrDefault();

                                Logic.Where(rid => rid.RowNumber == RowID).FirstOrDefault().Count = Logic.Where(rid => rid.RowNumber == RowID).FirstOrDefault().Count + 1;
                                Logic.Where(rid => rid.RowNumber == RowID).FirstOrDefault().ReceivedTime = DateTime.Now;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

                return result;
            }

            public WF_WFStepLink GetAutoAllocationStepDetails(int WorkFlowID, int CompanyID)
            {
                WF_WFStepLink AutoResult = null;
                try
                {
                    AutoResult = client.WF_WFStepLink.Where(sl => sl.Company_ID == CompanyID && sl.WorkFlow_ID == WorkFlowID && sl.AutoAllocationAllowed == true).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return AutoResult;
            }

            #region ::: To check whether Auto allocation is applicable or not:::
            /// <Author>"Amith Naik"</Author>
            /// <CreatedDate>"11-10-2012"</CreatedDate>
            /// <Purpose >"To check whether Auto allocation is applicable or not"</Purpose>
            /// <returns></returns> 
            public bool CheckAutoAllocation(int companyID, int workFlowID, int UserID)
            {
                bool result = true;
                WF_WFStepLink stepLink = null;
                IEnumerable<WF_WFCase_Progress> FromList = null;
                try
                {
                    stepLink = GetAutoAllocationStepDetails(workFlowID, companyID);

                    if (stepLink != null)
                    {
                        FromList = client.WF_WFCase_Progress.Where(cp => cp.WFSteps_ID == stepLink.FrmWFSteps_ID && (cp.Action_Chosen == null) && cp.Addresse_Flag == 1 && cp.Addresse_ID == UserID && cp.WorkFlow_ID == workFlowID);
                        if (FromList.Count() == 0)
                        {
                            result = false;
                        }
                    }
                    else
                    {
                        result = false;
                    }
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return result;
            }
            #endregion
            
            public bool CheckIfVersionEnabled(int companyID, int workFlowID, int stepID, int ActionID)
            {
                bool isVersionEnabled = false;
                isVersionEnabled = Convert.ToBoolean(client.WF_WFStepLink.Where(i => i.Company_ID == companyID && i.WorkFlow_ID == workFlowID && i.FrmWFSteps_ID == stepID && i.WFAction_ID == ActionID).Select(i => i.IsVersionEnabled));
                return isVersionEnabled;
            }
            public WF_WFStepLink GetRoleOrIndividualForFirstStep(int companyID, int workflowID)
            {
                WF_WFSteps stepRow = client.WF_WFSteps.Where(a => a.WorkFlow_ID == workflowID && a.WFStepType_ID == 1).FirstOrDefault();
                WF_WFStepLink stepLinkRow = client.WF_WFStepLink.Where(a => a.WorkFlow_ID == workflowID && a.Company_ID == companyID && a.FrmWFSteps_ID == stepRow.WFSteps_ID).FirstOrDefault();
                return stepLinkRow;
            }

            public List<Indicator> GetExternalMyQueue(int companyID, int workFlowID, int userID)
            {
                dynamic dummy = null;
                var jsonResult = dummy;
                List<Indicator> RowInd = new List<Indicator>();
                try
                {
                    List<WF_WFCase_Progress> transactionList = client.WF_WFCase_Progress.Where(i => (i.Action_Chosen == null) && i.WorkFlow_ID == workFlowID && i.Addresse_ID == userID && i.Addresse_Flag == 1).ToList();
                    var list = (from a in transactionList
                                join b in client.WF_WFStepLink on a.WorkFlow_ID equals b.WorkFlow_ID
                                where (b.Company_ID != companyID)
                                select new
                                {
                                    a.Transaction_ID,
                                    a.Locked_Ind
                                }).Distinct();

                    foreach (var a in list)
                    {
                        Indicator RInd = new Indicator();
                        RInd.TransactionID = a.Transaction_ID;
                        RInd.IndicatorType = 0;
                        RInd.IsLock = Convert.ToBoolean(a.Locked_Ind);
                        RowInd.Add(RInd);
                    }
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return RowInd;
            }

            public List<Indicator> GetExternalGroupQueue(int companyID, int workFlowID, int userID)
            {
                string inCondition = string.Empty;
                List<Indicator> RowInd = new List<Indicator>();
                IEnumerable<WF_WFStepLink> Stlink = client.WF_WFStepLink.Where(st => st.Company_ID != companyID);
                dynamic dummy = null;
                var list = dummy;

                try
                {
                    List<int> roleID = client.WF_WFRoleUser.OrderByDescending(i => i.WFRoleUser_ID).Where(i => i.UserID == userID).Select(i => i.WFRole_ID).ToList();
                    for (int i = 0; i < roleID.Count; i++)
                    {
                        if (i != (roleID.Count - 1))
                        {
                            inCondition += roleID[i] + ",";
                        }
                        else
                        {
                            inCondition += roleID[i];
                        }
                    }
                    string query = "select * from GNM_WFCase_Progress where (Action_Chosen =0 or Action_Chosen is null) and WorkFlow_ID='" + workFlowID + "' and Addresse_ID in (" + inCondition + ") and Addresse_Flag=0";
                    List<WF_WFCase_Progress> transactionList = client.Database.SqlQuery(typeof(WF_WFCase_Progress), query).Cast<WF_WFCase_Progress>().ToList();

                    list = (from a in transactionList
                            join b in Stlink on a.WorkFlow_ID equals b.WorkFlow_ID
                            where b.WorkFlow_ID == workFlowID
                            select new
                            {
                                a.Transaction_ID,
                            }).Distinct();

                    foreach (var a in list)
                    {
                        Indicator RInd = new Indicator();
                        RInd.TransactionID = a.Transaction_ID;
                        RInd.IndicatorType = 0;
                        RInd.IsLock = false;
                        RowInd.Add(RInd);
                    }
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return RowInd;
            }

            public int  CheckIsInvokeParentLinkRequired(int companyID, int workFlowID, int stepID, int ActionID,int tostepid)
            {
                int ParentStepLinkID = 0;
                WF_WFStepLink StepL = null;
                StepL = client.WF_WFStepLink.Where(a => a.Company_ID == companyID && a.WorkFlow_ID == workFlowID && a.FrmWFSteps_ID == stepID && a.WFAction_ID == ActionID && a.ToWFSteps_ID == tostepid).FirstOrDefault();

                if (StepL != null)
                {
                    ParentStepLinkID = (StepL.InvokeParentWF_ID == null) ? 0 : Convert.ToInt32(StepL.InvokeParentWFLink_ID);
                }
                else
                {
                    ParentStepLinkID = 0;
                }
                return ParentStepLinkID;
            }

            public WF_WFStepLink UpdateParentLink(int ParentStepLinkID, int transactionID, int userID, string smstextadd, string smsTextCust, string custMobNo, string CustEmailID, string emailSubAdd, string emailbodyAdd, string emailbodyCust, string emailSubCust, SMSTemplate SMSCutomerObj, SMSTemplate SMSAssigneeObj)
            {
                int parentWFID = 0;
                int CompanyID = 0;
                int addresseeID = 0;
                WF_WFStepLink ParentStepL = null;
                IEnumerable<WF_WFCase_Progress> caseProg = null;
                IEnumerable<WF_WFRoleUser> WFRoleUser = null;
                CaseProgressObjects CPDetails = new CaseProgressObjects();
                try
                {
                    WFRoleUser = client.WF_WFRoleUser.Where(r => r.UserID == userID);
                    ParentStepL = client.WF_WFStepLink.Where(a => a.WFStepLink_ID == ParentStepLinkID).FirstOrDefault();

                    parentWFID = Convert.ToInt32(ParentStepL.WorkFlow_ID);
                    CompanyID = Convert.ToInt32(ParentStepL.Company_ID);                    
                    
                    caseProg = client.WF_WFCase_Progress.Where(c => c.WorkFlow_ID == parentWFID && c.Transaction_ID == transactionID);

                    //To check that current child action is already esists in parent case progress
                    if (caseProg.Where(cp => cp.Action_Chosen == ParentStepL.WFAction_ID && cp.WFSteps_ID == ParentStepL.FrmWFSteps_ID && cp.WFNextStep_ID == ParentStepL.ToWFSteps_ID).Count() == 0)
                    {
                        //Proceed only if logged in user belongs to Parent Action group
                        if (WFRoleUser.Where(r=>r.WFRole_ID== ParentStepL.Addresse_WFRole_ID).Count()>0)
                        {
                            WF_WFCase_Progress lastRow = client.WF_WFCase_Progress.OrderByDescending(i => i.WFCaseProgress_ID).Where(i => i.WorkFlow_ID == parentWFID && i.Transaction_ID == transactionID).FirstOrDefault();

                            if (lastRow != null)
                            {
                                //Proceed only if Parent Transacton is exists logged in users my queue or group queue 
                                if ((lastRow.Addresse_ID == userID && lastRow.Addresse_Flag == 1) || (lastRow.Addresse_ID == ParentStepL.Addresse_WFRole_ID && lastRow.Addresse_Flag == 0))
                                {
                                    if (ParentStepL.Addresse_Flag == 0)
                                    {
                                        addresseeID =Convert.ToInt32(ParentStepL.Addresse_WFRole_ID);
                                    }
                                    else
                                    {
                                        addresseeID = userID;
                                    }

                                    CPDetails.actionBy = userID;
                                    CPDetails.actionID = ParentStepL.WFAction_ID;
                                    CPDetails.actionRemarks = "System Action";
                                    CPDetails.actionTime = DateTime.Now;
                                    CPDetails.addresseType = ParentStepL.Addresse_Flag;
                                    CPDetails.AssignTo = addresseeID;
                                    CPDetails.CompanyID = CompanyID;
                                    CPDetails.currentStepID = ParentStepL.FrmWFSteps_ID;
                                    CPDetails.customerEmailID = CustEmailID;
                                    CPDetails.customerMobileNumber = custMobNo;
                                    CPDetails.emailBodyAddress = emailbodyAdd;
                                    CPDetails.emailBodyCustomer = emailbodyCust;
                                    CPDetails.emailSubAddressee = emailSubAdd;
                                    CPDetails.emailSubCustomer = emailSubCust;
                                    CPDetails.NextStepID = ParentStepL.ToWFSteps_ID;
                                    CPDetails.receivedTime = DateTime.Now;
                                    CPDetails.RoleID = Convert.ToInt32(ParentStepL.Addresse_WFRole_ID);
                                    CPDetails.smsTextAddressee = smstextadd;
                                    CPDetails.smsTextCustomer = smsTextCust;
                                    CPDetails.transactionNumber = transactionID;
                                    CPDetails.workFlowID = parentWFID;

                                    insertWorkFlowHistory(CPDetails, SMSCutomerObj, SMSAssigneeObj);
                                }                               
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return ParentStepL;
            }

            public bool CheckIsInvokeChildObject(int companyID, int workFlowID, int stepID, int ActionID,int tostepid)
            {
                bool result = false;

                WF_WFStepLink StepL = null;
                StepL = client.WF_WFStepLink.Where(a => a.Company_ID == companyID && a.WorkFlow_ID == workFlowID && a.FrmWFSteps_ID == stepID && a.WFAction_ID == ActionID && a.ToWFSteps_ID == tostepid).FirstOrDefault();
                result = (StepL.InvokeChildObject_ID == null) ? false : true;
                return result;
            }

            public string InvokeChildAction(int steplinkID)
            {
               
                WF_WFStepLink CurrentStepL = null;
                int objectID = 0;
                string ObjAction = string.Empty;
               
                string ObjectName = string.Empty;
                WF_WFChildActions act = null;
                try
                {
                    CurrentStepL = client.WF_WFStepLink.Where(a => a.WFStepLink_ID == steplinkID).FirstOrDefault();
                    objectID =Convert.ToInt32(CurrentStepL.InvokeChildObject_ID);
                    act = client.WF_WFChildActions.Where(i => i.ChildActions_ID == CurrentStepL.InvokeChildObjectAction).FirstOrDefault();
                    ObjAction = (act == null) ? "" : act.Actions_Name;
                    ObjectName = genEnt.WF_Object.Where(m => m.Object_ID == objectID).Select(m => m.Object_Name).FirstOrDefault();
                    ObjectName = ObjectName + "/" + ObjAction;
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

                return ObjectName;
            }


            public string InvokeChildActionSameController(int steplinkID)
            {
                WF_WFStepLink CurrentStepL = null;
                int objectID = 0;
                string ObjAction = string.Empty;
                WF_WFChildActions act = null;
                try
                {
                    CurrentStepL = client.WF_WFStepLink.Where(a => a.WFStepLink_ID == steplinkID).FirstOrDefault();
                    objectID = Convert.ToInt32(CurrentStepL.InvokeChildObject_ID);
                    act = client.WF_WFChildActions.Where(i => i.ChildActions_ID == CurrentStepL.InvokeChildObjectAction).FirstOrDefault();
                    ObjAction = (act == null) ? "" : act.Actions_Name;
                    
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

                return ObjAction;
            }


            public List<Indicator> GetTranforApproval(int companyID, int workFlowID)
            {
                List<Indicator> result = new List<Indicator>();
                IEnumerable<WF_WFSteps> Steps = null;

                int CreatedStepTypeID = client.WF_WFStepStatus.Where(s => s.WFStepStatus_Nm.ToUpper() == "CREATED").Select(S=>S.WFStepStatus_ID).FirstOrDefault();
                Steps = client.WF_WFSteps.Where(s => s.WFStepStatus_ID == CreatedStepTypeID && s.WorkFlow_ID == workFlowID);

                foreach (var s in Steps)
                {
                    IEnumerable<WF_WFCase_Progress> cp = client.WF_WFCase_Progress.Where(c => c.WorkFlow_ID == workFlowID && c.Action_Chosen==null && c.WFSteps_ID==s.WFSteps_ID);

                    foreach(var c in cp)
                    {
                        Indicator ind =new Indicator();
                        ind.TransactionID = c.Transaction_ID;
                        result.Add(ind);
                    }

                }
                return result;
            }


            public WF_WFStepLink getFirstStepForFloatDealer(int companyID, int workFlowID, int TransactionID)
            {

                WF_WFStepLink ResultLink = null;
                IEnumerable<WF_WFField> WFField = client.WF_WFField.Where(i => i.WorkFlow_ID == workFlowID);
                WF_WFFieldValue WFFieldValue = null;
               IEnumerable<WF_WFStepLink> ToStepLink = client.WF_WFStepLink.Where(a => a.Addresse_Flag==2 && a.WorkFlow_ID == workFlowID && a.Company_ID == companyID);
                foreach (var x in WFField)
                {
                    WFFieldValue = client.WF_WFFieldValue.Where(i => i.Company_ID == companyID && i.WFField_ID == x.WFField_ID && i.Transaction_ID == TransactionID).FirstOrDefault();
                    foreach (var y in ToStepLink)
                    {
                        //AutoCondition = y;
                        if (y.AutoCondition != "" && y.Addresse_Flag == 2)
                        {
                            //result = ToStepLink.Where(r => r.AutoCondition == WFFieldValue.WorkFlowFieldValue).FirstOrDefault();                        
                            string query = "select A.* from GNM_WFStepLink A inner join GNM_WFFieldValue B on A.WFField_ID=B.WFField_ID and A.WorkFlow_ID=B.WorkFlow_ID and A.Company_ID=B.Company_ID where A.Company_ID=" + companyID + " and A.WFField_ID=" + WFFieldValue.WFField_ID + " and B.Transaction_ID=" + TransactionID + " and A.Addresse_Flag=" + 2 + "   and B.WorkFlowFieldValue ='" + y.AutoCondition + "' and A.AutoCondition='" + y.AutoCondition.Replace("'", "''") + "'";
                            ResultLink = client.Database.SqlQuery(typeof(WF_WFStepLink), query).Cast<WF_WFStepLink>().FirstOrDefault();
                            if (ResultLink != null)
                            {
                                break;
                            }
                        }
                    }
                    if (ResultLink != null)
                    {
                        break;
                    }

                }

                return ResultLink;
            }
            //Added by Manju M for HelpDesk CR-5 Changes 09-Oct-2015 -- Start
            public DateTime LocalTime(int Branch_ID, DateTime servertime)
            {
                int Timezoneid = 0;
                string StandardTimeZone = "";
                DateTime localtime = DateTime.Now;

                try
                {

                    using (SqlConnection con = new SqlConnection(strCon))
                    {
                        cmd = new SqlCommand("select TimeZoneID from GNM_Branch where Branch_ID='" + Branch_ID + "'", con);
                        con.Open();
                        Timezoneid = Convert.ToInt32(cmd.ExecuteScalar());
                        con.Close();
                    }
                    try
                    {
                        using (SqlConnection con = new SqlConnection(strCon))
                        {
                            cmd = new SqlCommand("select RefMasterDetail_Name from GNM_RefMasterDetail where RefMasterDetail_ID='" + Timezoneid + "'", con);
                            con.Open();
                            StandardTimeZone = cmd.ExecuteScalar().ToString();
                            con.Close();
                        }

                        localtime = TimeZoneInfo.ConvertTimeBySystemTimeZoneId(servertime, TimeZoneInfo.Local.Id, StandardTimeZone);
                    }
                    catch (Exception ex)
                    {
                        throw ex;
                    }
                }
                catch (Exception ex)
                {
                    throw ex;
                }
                return localtime;
            }
            //Added HelpDesk CR-5 Changes 09-oct-2015 -- End
        }
        public class SMSTemplate
        {
            public int Template_ID { get; set; }
            public string Param1 { get; set; }
            public string Param2 { get; set; }
            public string Param3 { get; set; }
            public string Param4 { get; set; }
        }
        public class Indicator
        {

            public int TransactionID { get; set; }
            public int IndicatorType { get; set; }
            public bool IsLock { get; set; }
        }
        public class AutoAllocateResult
        {
            public int TransactionID { get; set; }
            public int UserID { get; set; }
        }
        public class AllocationLogic
        {
            public int RowNumber { get; set; }
            public int UserID { get; set; }
            public int Count { get; set; }
            public DateTime ReceivedTime { get; set; }
        }
        public class WorkFlowSummary
        {
            public int StatusID { get; set; }
            public string StatusName { get; set; }
            public int Count { get; set; }
            public int MaxValue { get; set; }
            public int Mode { get; set; }
            public int Company_ID { get; set; }
            public int WorkFlow_ID { get; set; }
            public int WFCaseProgress_ID { get; set; }
        }
        public class ResultForAction
        {
            public int ID { get; set; }
            public string Name { get; set; }
            public bool isVersionEnabled { get; set; }
        }
    }


    namespace FSM.Models
    {
        
        public static class WorkFlowCommon
        {
           
            public enum RoleType
            {
                MyQue = 1,
                GroupQue = 2,
                AllQue = 3,
                None = 0
            }
            static int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            public static bool chkforAdmin(int userID, string WorkFlowName,string DBName)
            {

                bool IsAdmin = false;
                try
                {
                    WorkFlowEntity WorkFlowClient = new WorkFlowEntity(DBName);
                    int WorkFlowID = GetWorkFlowID(WorkFlowName,DBName);

                    IsAdmin = WorkFlowClient.WF_WFRoleUser.Where(uid => uid.UserID == userID && uid.GNM_WFRole.WorkFlow_ID == WorkFlowID && uid.GNM_WFRole.WfRole_IsAdmin == true).Count() > 0 ? true : false;
                }
                catch (Exception ex)
                {
                    IsAdmin = false;
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return IsAdmin;

            }

            public static int GetWorkFlowID(string WorkFlowName, string DBName)
            {

                try
                {
                    WorkFlowEntity WorkFlowClient = new WorkFlowEntity(DBName);
                    int WorkFlowID = WorkFlowClient.WF_WorkFlow.Where(wf => wf.WorkFlow_Name == WorkFlowName).FirstOrDefault().WorkFlow_ID;
                    return WorkFlowID;
                }

                catch (Exception ex)
                {
                    if (LogException == 0)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                    }
                    return 0;
                }

            }

            //public static dynamic GetActions(int WFCurrentStepID, string WorkFlowName, int CompanyID,string DBName) //0--Add Mode
            //{

            //    WorkFlowAPIController API = new WorkFlowAPIController(DBName);

            //    JTokenReader jr = null;
            //    dynamic dummy = null;
            //    var jsonResult = dummy;
            //    var jsonStatus = dummy;
            //    int WorkFlowID = 0;


            //    var Actions = dummy;
            //    string CurrentStep = string.Empty;
            //    int currentstepid = 0;
            //    try
            //    {
            //        WorkFlowID = GetWorkFlowID(WorkFlowName, DBName);

            //        if (WFCurrentStepID == 0)
            //        {
            //            jsonStatus = API.GetStatusofWorkFlow(0, WorkFlowID);
            //            JObject jObject = JObject.Parse(jsonStatus.Data.ToString());
            //            jr = new JTokenReader(jObject["CurrentStepID"]);
            //            jr.Read();
            //            currentstepid = Convert.ToInt32(jr.Value);

            //            jr = new JTokenReader(jObject["CurrentStepName"]);
            //            jr.Read();
            //            CurrentStep = jr.Value.ToString();

            //            Actions = API.GetActionsForTheStep(CompanyID, currentstepid, WorkFlowID);
            //            jsonResult = new
            //            {
            //                Result = "Begin",
            //                CurrentStepID = currentstepid,
            //                CurrentStep = CurrentStep,
            //                ActionsList = Actions.Data,
            //                CallDate = DateTime.Now.ToShortDateString(),
            //                CallTime = DateTime.Now.ToShortTimeString()
            //            };
            //        }
            //        else
            //        {
            //            Actions = API.GetActionsForTheStep(CompanyID, WFCurrentStepID, WorkFlowID);

            //            jsonResult = new
            //            {
            //                Result = "Intermediate",
            //                ActionsList = Actions.Data,
            //                CallDate = DateTime.Now.ToShortDateString(),
            //                CallTime = DateTime.Now.ToShortTimeString()
            //            };
            //        }
            //    }


            //    catch (Exception ex)
            //    {
            //        if (LogException == 0)
            //        {
            //            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            //            //
            //        }

            //    }
            //    return jsonResult;
            //}

            public static dynamic GetRolesForActions(int WFCurrentStepID, int ActionID, int TransactionID, int CompanyID, string WorkFlowName, string DBName, int UserLanguageID) //0--Add Mode
            {
                WorkFlowAPIController API = new WorkFlowAPIController(DBName);
                dynamic dummy = null;
                var jsonResult = dummy;
                int WorkFlowID = 0;

                List<WF_WFStepLink> WFStepLink = null;
                var RoleorIndividual = dummy;
                bool isRole = true;
                List<ResultForAction> resultList = new List<ResultForAction>();

                try
                {
                    WorkFlowID = GetWorkFlowID(WorkFlowName, DBName);

                    WFStepLink = API.GetStepLinkForInsert(CompanyID, WorkFlowID, WFCurrentStepID, ActionID);

                    if (WFStepLink != null && WFStepLink.Count > 0)
                    {
                        foreach (var c in WFStepLink)
                        {
                            if (c.Addresse_Flag == 1)
                            {
                                resultList.AddRange(API.GetListOfStaffForaRoleID(CompanyID, (int)c.Addresse_WFRole_ID, 0, WFCurrentStepID, ActionID,UserLanguageID));
                                isRole = false;
                            }
                            else
                            {
                                resultList.AddRange(API.GetAssociatedRolesForaStep(CompanyID, WFCurrentStepID, ActionID, WorkFlowID,UserLanguageID));
                                isRole = true;
                            }
                        }


                        jsonResult = new
                        {
                            Result = (isRole) ? "Role" : "Individual",
                            ResultList = (from res in resultList
                                          orderby res.Name //added order by condition by Kavitha for Tools Issue
                                          select new
                                          {
                                              res.ID,
                                              res.isVersionEnabled,
                                              res.Name
                                          }).Distinct().ToArray(),
                            CallDate = DateTime.Now.ToShortDateString(),
                            CallTime = DateTime.Now.ToShortTimeString()
                        };

                    }
                    else
                    {
                        jsonResult = new
                        {
                            Result = "End",
                            CallDate = DateTime.Now.ToString("dd-MMM-yyyy") + ' ' + DateTime.Now.ToString("HH:mm"),
                            CallTime = DateTime.Now.ToShortTimeString()
                        };
                    }
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

                return jsonResult;
            }

            public static string LockRecord(int QuotationID, int userID, int CompanyID, string WorkFlowName,string DBName, int Branch_ID=0)
            {
                string status = "";
                try
                {
                    WorkFlowEntity WorkFlowClient = new WorkFlowEntity(DBName);
                    WorkFlowAPIController API = new WorkFlowAPIController(DBName);

                    int WorkFlowID = WorkFlowClient.WF_WorkFlow.Where(wf => wf.WorkFlow_Name == WorkFlowName).FirstOrDefault().WorkFlow_ID;
                    status = API.LockTransaction(CompanyID, WorkFlowID, QuotationID, userID, 0, Branch_ID);
                    status = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), status).ToString();
                }
                catch (Exception ex)
                {
                    if (LogException == 0)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }
                return status;
            }

            public static string UnLockRecord(int jobcardID, int userID, int CompanyID, string WorkFlowName, string DBName, int Branch_ID=0)
            {
                string status = "";
                try
                {
                    WorkFlowEntity WorkFlowClient = new WorkFlowEntity(DBName);
                    WorkFlowAPIController API = new WorkFlowAPIController(DBName);

                    int WorkFlowID = WorkFlowClient.WF_WorkFlow.Where(wf => wf.WorkFlow_Name == WorkFlowName).Select(i => i.WorkFlow_ID).FirstOrDefault();
                    status = API.UnLockTransaction(CompanyID, WorkFlowID, jobcardID, userID, "",Branch_ID);
                    status = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), status).ToString();
                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }
                return status;
            }

            public static bool CheckPreffixSuffix(int CompanyID, int BranchID, string ObjectName, string DBName)
            {
                GenEntities genEnt = new GenEntities(DBName);             
                int ObjectID = GetObjectID(ObjectName, DBName);

                bool result=genEnt.WF_PrefixSuffix.Where(a => a.Branch_ID == BranchID && a.Object_ID == ObjectID && a.FromDate <= DateTime.Now && a.ToDate >= DateTime.Now).Count() > 0 ? true : false;

                if(!result)
                {
                    result=genEnt.WF_PrefixSuffix.Where(a => a.Company_ID == CompanyID && a.Branch_ID==null && a.Object_ID == ObjectID && a.FromDate <= DateTime.Now && a.ToDate >= DateTime.Now).Count() > 0 ? true : false;
                }
                return result;
            }

            public static RoleType GetRoleType(int UserID, int TransactionID, string WorkFlowName, string DBName)
            {
                WorkFlowEntity WorkFlowClient = new WorkFlowEntity(DBName);
                int WorkFlowID = GetWorkFlowID(WorkFlowName, DBName);
                WF_WFCase_Progress gnmCP = WorkFlowClient.WF_WFCase_Progress.Where(a => a.Transaction_ID == TransactionID && a.WorkFlow_ID == WorkFlowID).OrderByDescending(a => a.WFCaseProgress_ID).FirstOrDefault();
                int EndStepTypeID = WorkFlowClient.WF_WFStepType.Where(st => st.WFStepType_Nm.ToUpper() == "END").Select(s => s.WFStepType_ID).FirstOrDefault();

                List<int> CloseStepID = WorkFlowClient.WF_WFSteps.Where(st => st.WFStepType_ID == EndStepTypeID && st.WorkFlow_ID == WorkFlowID).Select(i => i.WFSteps_ID).ToList<int>();

                if ((gnmCP.Action_Chosen == null) && gnmCP.Addresse_ID == UserID && gnmCP.Addresse_Flag == 1)
                {
                    return RoleType.MyQue;
                }
                else if ((gnmCP.Action_Chosen == null) && gnmCP.Addresse_Flag == 0 && (WorkFlowClient.WF_WFRoleUser.Where(a => a.UserID == UserID && a.WFRoleUser_ID == gnmCP.Addresse_ID).Count() > 0 ? true : false))
                {
                    return RoleType.GroupQue;
                }
                else if ((CloseStepID.Contains(gnmCP.WFNextStep_ID.HasValue ? gnmCP.WFNextStep_ID.Value : 0) || gnmCP.Action_Chosen == null))
                {
                    return RoleType.AllQue;
                }
                return RoleType.None;
            }

            public static bool CheckAddPermissions(int UserID, int CompanyID, string ObjectName, string WorkFlowName, string DBName)
            {
                bool IsADD = true;
                try
                {
                    WorkFlowAPIController API = new WorkFlowAPIController(DBName);
                    int ObjectID = GetObjectID(ObjectName, DBName);
                    int WorkFlowID = WorkFlowCommon.GetWorkFlowID(WorkFlowName, DBName);
                    IsADD = API.chkIsAddRecords(ObjectID, WorkFlowID, CompanyID, UserID);
                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }
                }
                return IsADD;
            }

            public static int GetObjectID(string name, string DBName)
            {
                GenEntities genEnt = new GenEntities(DBName);
                int ObjectID = 0;
                try
                {
                    ObjectID = genEnt.WF_Object.Where(ob => ob.Object_Name.ToUpper() == name.ToUpper()).Select(o => o.Object_ID).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return ObjectID;
            }
        }
        
    }
}
