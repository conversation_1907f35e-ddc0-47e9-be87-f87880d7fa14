﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Web;
using System.Web.Http;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class HelpDeskCustomerFeedBackQuestionsController : ApiController
    {
        #region SelectIssueSubArea vinay n 11/11/24
        /// <summary>
        /// SelectIssueSubArea
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskCustomerFeedBackQuestions/SelectIssueSubArea")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectIssueSubArea([FromBody] SelectIssueSubAreaList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskCustomerFeedBackQuestionsServices.SelectIssueSubArea(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region SelectIssueSubArea vinay n 11/11/24
        /// <summary>
        /// Save
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskCustomerFeedBackQuestions/Save")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Save([FromBody] SaveCustomerFeedBackQuestionsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskCustomerFeedBackQuestionsServices.Save(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region SelectIssueSubArea vinay n 11/11/24
        /// <summary>
        /// Save
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskCustomerFeedBackQuestions/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteCustomerFeedBackQuestionsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskCustomerFeedBackQuestionsServices.Delete(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region SelectIssueSubArea vinay n 11/11/24
        /// <summary>
        /// Save
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskCustomerFeedBackQuestions/GetIssueAreas")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetIssueAreas([FromBody] GetIssueAreasList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskCustomerFeedBackQuestionsServices.GetIssueAreas(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region SelectIssueSubArea vinay n 11/11/24
        /// <summary>
        /// Save
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskCustomerFeedBackQuestions/GetIssueAreas")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetIssueSubAreas([FromBody] GetIssueSubAreasList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskCustomerFeedBackQuestionsServices.GetIssueSubAreas(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region LoadCustomerQuestion vinay n 11/11/24
        /// <summary>
        /// LoadCustomerQuestion
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskCustomerFeedBackQuestions/LoadCustomerQuestion")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadCustomerQuestion([FromBody] GetIssueAreasList Obj)
        {
            var Response = default(dynamic);
            try
            {

                var rawBody = Request.Content.ReadAsStringAsync().Result;
                string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

                string sidx = HttpContext.Current.Request.Params["sidx"];
                int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
                int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
                string sord = HttpContext.Current.Request.Params["sord"];
                bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
                long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
                string filters = "";
                bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);

                string advnceFilters = HttpContext.Current.Request.Params["Query"];


                if (HttpContext.Current.Request.Params["filters"] == null)
                {
                    filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
                }
                else
                {
                    filters = HttpContext.Current.Request.Params["filters"];
                }
                Response = HelpDeskCustomerFeedBackQuestionsServices.LoadCustomerQuestion(Obj, connstring, LogException, sidx, sord, page, rows, _search, advnce, filters, advnceFilters);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region CheckQuestions vinay n 11/11/24
        /// <summary>
        /// CheckQuestions
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/HelpDeskCustomerFeedBackQuestions/CheckQuestions")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckQuestions([FromBody] CheckQuestionsList Obj)
        {
            var Response = default(dynamic);
            try
            {


                string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));


                Response = HelpDeskCustomerFeedBackQuestionsServices.CheckQuestions(Obj, connstring, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



    }
}