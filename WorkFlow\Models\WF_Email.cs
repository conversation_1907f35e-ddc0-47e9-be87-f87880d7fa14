//------------------------------------------------------------------------------
// <auto-generated>
//    This code was generated from a template.
//
//    Manual changes to this file may cause unexpected behavior in your application.
//    Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace WorkFlow.Models
{
    using System;
    using System.Collections.Generic;
    
    public partial class WF_Email
    {
        public int Email_ID { get; set; }
        public string Email_Subject { get; set; }
        public string Email_Body { get; set; }
        public string Email_To { get; set; }
        public string Email_cc { get; set; }
        public string Email_Bcc { get; set; }
        public Nullable<System.DateTime> Email_Queue_Date { get; set; }
        public Nullable<System.DateTime> Email_Sent_Date { get; set; }
        public bool Email_SentStatus { get; set; }
        public string Email_Attachments { get; set; }
        public Nullable<bool> Email_IsError { get; set; }
        public Nullable<byte> NoOfAttempts { get; set; }
    }
}
