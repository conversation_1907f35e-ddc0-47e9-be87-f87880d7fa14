﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreQuestionnaireLevel3MasterController : ApiController
    {

        #region SelectReferenceMaster vinay n
        /// <summary>
        /// SelectReferenceMaster
        /// </summary>
        /// <param name="SelectReferenceMasterObj"></param>
        /// <returns></returns>
        [Route("api/CoreQuestionnaireLevel3Master/SelectReferenceMaster")]
        [HttpPost]
        [JwtTokenValidationFilter]

        public IHttpActionResult SelectReferenceMaster([FromBody] SelectReferenceMasterLista SelectReferenceMasterObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel3Services.SelectReferenceMaster(SelectReferenceMasterObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region SelectQuestionnaireLevel1Master vinay n
        /// <summary>
        /// SelectQuestionnaireLevel1Master
        /// </summary>
        /// <param name="SelectQuestionnaireLevel1MasterObj"></param>
        /// <returns></returns>
        [Route("api/CoreQuestionnaireLevel3Master/SelectQuestionnaireLevel1Master")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectQuestionnaireLevel1Master([FromBody] SelectQuestionnaireLevel1MasterList SelectQuestionnaireLevel1MasterObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel3Services.SelectQuestionnaireLevel1Master(SelectQuestionnaireLevel1MasterObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region SelectQuestionnaireLevel2Master viany n
        /// <summary>
        /// SelectQuestionnaireLevel2Master
        /// </summary>
        /// <param name="SelectQuestionnaireLevel2MasterObj"></param>
        /// <returns></returns>
        [Route("api/CoreQuestionnaireLevel3Master/SelectQuestionnaireLevel2Master")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectQuestionnaireLevel2Master([FromBody] SelectQuestionnaireLevel2MasterList SelectQuestionnaireLevel2MasterObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel3Services.SelectQuestionnaireLevel2Master(SelectQuestionnaireLevel2MasterObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region Select vinay n
        /// <summary>
        /// Select
        /// </summary>
        /// <param name="SelectParticularQuestionnaireLevel3Obj"></param>
        /// <returns></returns>
        [Route("api/CoreQuestionnaireLevel3Master/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] SelectQuestionnaireLevel3List SelectParticularQuestionnaireLevel3Obj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);

            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreQuestionnaireLevel3Services.Select(SelectParticularQuestionnaireLevel3Obj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters, connstring, LogException);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion
        #region SelectParticularQuestionnaireLevel3 vinay n
        /// <summary>
        /// SelectParticularQuestionnaireLevel3
        /// </summary>
        /// <param name="SelectParticularQuestionnaireLevel3Obj"></param>
        /// <returns></returns>
        [Route("api/CoreQuestionnaireLevel3Master/SelectParticularQuestionnaireLevel3")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularQuestionnaireLevel3([FromBody] SelectParticularQuestionnaireLevel3List SelectParticularQuestionnaireLevel3Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel3Services.SelectParticularQuestionnaireLevel3(SelectParticularQuestionnaireLevel3Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region SelectParticularQuestionnaireLevel3 vinay n
        /// <summary>
        /// SelectParticularQuestionnaireLevel3
        /// </summary>
        /// <param name="SaveQuestionnaireLevel3Obj"></param>
        /// <returns></returns>
        [Route("api/CoreQuestionnaireLevel3Master/Save")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Save([FromBody] SaveQuestionnaireLevel3List SaveQuestionnaireLevel3Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel3Services.Save(SaveQuestionnaireLevel3Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region UpdateLocale vinay n
        /// <summary>
        /// UpdateLocale
        /// </summary>
        /// <param name="UpdateLocaleQuestionnaireLevel3Obj"></param>
        /// <returns></returns>
        [Route("api/CoreQuestionnaireLevel3Master/UpdateLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateLocale([FromBody] UpdateLocaleQuestionnaireLevel3List UpdateLocaleQuestionnaireLevel3Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel3Services.UpdateLocale(UpdateLocaleQuestionnaireLevel3Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region CheckQuestionLevel3 viany n
        /// <summary>
        /// CheckQuestionLevel3
        /// </summary>
        /// <param name="CheckQuestionLevel3Obj"></param>
        /// <returns></returns>
        [Route("api/CoreQuestionnaireLevel3Master/CheckQuestionLevel3")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckQuestionLevel3([FromBody] CheckQuestionLevel3List CheckQuestionLevel3Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel3Services.CheckQuestionLevel3(CheckQuestionLevel3Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region Delete vinay n
        /// <summary>
        /// Delete
        /// </summary>
        /// <param name="DeleteQuestionLevel3Obj"></param>
        /// <returns></returns>
        [Route("api/CoreQuestionnaireLevel3Master/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteQuestionLevel3List DeleteQuestionLevel3Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel3Services.Delete(DeleteQuestionLevel3Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region CheckQuestionnaireLevel3 vinay n
        [Route("api/CoreQuestionnaireLevel3Master/CheckQuestionnaireLevel3")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckQuestionnaireLevel3([FromBody] CheckQuestionnaireLevel3List CheckQuestionnaireLevel3Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreQuestionnaireLevel3Services.CheckQuestionnaireLevel3(CheckQuestionnaireLevel3Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region Export
        /// <summary>
        /// Export
        /// </summary>
        /// <param name="ExportObj"></param>
        /// <returns></returns>
        [Route("api/CoreQuestionnaireLevel3Master/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] SelectQuestionnaireLevel3List ExportObj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));





            try
            {


                Object Response = await CoreQuestionnaireLevel3Services.Export(ExportObj, connstring, LogException);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }


        }
        #endregion
    }
}
