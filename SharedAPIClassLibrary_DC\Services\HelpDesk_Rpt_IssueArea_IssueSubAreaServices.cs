﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class HelpDesk_Rpt_IssueArea_IssueSubAreaServices
    {


        #region ::: SelectBranch  Uday Kumar J B 12-11-2024:::
        /// <summary>
        /// To get the Select Branch
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult SelectBranch(HelpDesk_Rpt_IssueAreaSelectBranchList HelpDesk_Rpt_IssueAreaSelectBranchobj, string connString, int LogException)
        {
            object jsonobj = null;
            int languageID = Convert.ToInt32(HelpDesk_Rpt_IssueAreaSelectBranchobj.UserLanguageID);
            int companyID = Convert.ToInt32(HelpDesk_Rpt_IssueAreaSelectBranchobj.Company_ID);

            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Determine SP based on language settings
                    string storedProc = (HelpDesk_Rpt_IssueAreaSelectBranchobj.GeneralLanguageCode.ToString() == HelpDesk_Rpt_IssueAreaSelectBranchobj.UserLanguageCode.ToString())
                        ? "SP_AMERP_HelpDesk_SelectBranch_DefaultLanguage"
                        : "SP_AMERP_HelpDesk_SelectBranch_OtherLanguage";

                    using (SqlCommand cmd = new SqlCommand(storedProc, conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);
                        cmd.Parameters.AddWithValue("@EmployeeID", HelpDesk_Rpt_IssueAreaSelectBranchobj.Employee_ID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            var branches = new List<object>();

                            while (reader.Read())
                            {
                                branches.Add(new
                                {
                                    ID = reader["ID"],
                                    Name = reader["Name"]
                                });
                            }

                            jsonobj = new { Branch = branches };
                        }
                    }
                }


            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonobj);
        }
        #endregion


        #region :::GetModels Uday Kumar J B 12-11-2024:::
        /// <summary>
        /// GetModels.
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult GetModels(HelpDesk_Rpt_IssueAreaGetModelsList HelpDesk_Rpt_IssueAreaGetModelsobj, string connString, int LogException)
        {
            var Modeldata = default(dynamic);
            int LangID = Convert.ToInt32(HelpDesk_Rpt_IssueAreaGetModelsobj.LanguageID);
            string GenLangCode = HelpDesk_Rpt_IssueAreaGetModelsobj.GeneralLanguageCode.ToString();
            string UserLangCode = HelpDesk_Rpt_IssueAreaGetModelsobj.UserLanguageCode.ToString();
            string CompanyArray = string.IsNullOrEmpty(HelpDesk_Rpt_IssueAreaGetModelsobj.CompanyArray) ? "0" : HelpDesk_Rpt_IssueAreaGetModelsobj.CompanyArray;
            string[] CompanyIDs = CompanyArray.Split(',');
            int CompanyID = Convert.ToInt32(CompanyIDs[0]);

            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Get Company Brands
                    List<GNM_CompanyBrands> CompBrands = new List<GNM_CompanyBrands>();
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HElpDesk_GetCompanyBrands", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", CompanyID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                CompBrands.Add(new GNM_CompanyBrands
                                {
                                    Company_Brand_ID = reader.GetInt32(0),
                                    Company_ID = reader.GetInt32(1),
                                    Brand_ID = reader.GetInt32(2)
                                });
                            }
                        }
                    }

                    // Get Brand and ProductType data based on language code
                    List<object> modelList = new List<object>();

                    if (GenLangCode == UserLangCode)
                    {
                        List<GNM_RefMasterDetail> Brand = new List<GNM_RefMasterDetail>();
                        List<GNM_ProductType> ProdType = new List<GNM_ProductType>();

                        // Get Active Brands
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetActiveBrands", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    Brand.Add(new GNM_RefMasterDetail
                                    {
                                        RefMasterDetail_ID = reader.GetInt32(0),
                                        RefMasterDetail_Name = reader.GetString(1)
                                    });
                                }
                            }
                        }

                        // Get Active Product Types
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetActiveProductTypes", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ProdType.Add(new GNM_ProductType
                                    {
                                        ProductType_ID = reader.GetInt32(0),
                                        ProductType_Name = reader.GetString(1)
                                    });
                                }
                            }
                        }

                        // Get Models Data
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetModels", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@CompanyID", CompanyID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    modelList.Add(new
                                    {
                                        ID = reader.GetInt32(0),
                                        Name = reader.GetString(1),
                                        Brand = reader.GetString(2),
                                        ProductType = reader.GetString(3)
                                    });
                                }
                            }
                        }
                    }
                    else
                    {
                        List<GNM_RefMasterDetailLocale> BrandLocale = new List<GNM_RefMasterDetailLocale>();
                        List<GNM_ProductTypeLocale> ProdTypeLocale = new List<GNM_ProductTypeLocale>();

                        // Get Brand Locales
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetBrandLocales", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@LanguageID", LangID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    BrandLocale.Add(new GNM_RefMasterDetailLocale
                                    {
                                        RefMasterDetail_ID = reader.GetInt32(0),
                                        RefMasterDetail_Name = reader.GetString(1)
                                    });
                                }
                            }
                        }

                        // Get Product Type Locales
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetProductTypeLocales", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@LanguageID", LangID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ProdTypeLocale.Add(new GNM_ProductTypeLocale
                                    {
                                        ProductType_ID = reader.GetInt32(0),
                                        ProductType_Name = reader.GetString(1)
                                    });
                                }
                            }
                        }

                        // Get Models Locale Data
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetModelLocales", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                            cmd.Parameters.AddWithValue("@LanguageID", LangID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    modelList.Add(new
                                    {
                                        ID = reader.GetInt32(0),
                                        Name = reader.GetString(1),
                                        Brand = reader.GetString(2),
                                        ProductType = reader.GetString(3)
                                    });
                                }
                            }
                        }
                    }

                    Modeldata = modelList;
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(Modeldata);
        }
        #endregion



        #region :::GetIssueSubArea Uday Kumar J B 12-11-2024:::
        /// <summary>
        /// GetIssueSubArea.
        /// </summary>
        /// <returns>...</returns>
        /// 

        public static IActionResult GetIssueSubArea(HelpDesk_Rpt_IssueAreaGetGetIssueSubAreaList HelpDesk_Rpt_IssueAreaGetGetIssueSubAreaobj, string connString, int LogException)
        {
            var ISAdata = new List<object>();

            try
            {
                int Company_ID = Convert.ToInt32(HelpDesk_Rpt_IssueAreaGetGetIssueSubAreaobj.Company_ID);
                int LangID = Convert.ToInt32(HelpDesk_Rpt_IssueAreaGetGetIssueSubAreaobj.LanguageID);
                string GenLangCode = HelpDesk_Rpt_IssueAreaGetGetIssueSubAreaobj.GeneralLanguageCode.ToString();
                string UserLangCode = HelpDesk_Rpt_IssueAreaGetGetIssueSubAreaobj.UserLanguageCode.ToString();
                string IssueAreaIDs = HelpDesk_Rpt_IssueAreaGetGetIssueSubAreaobj.IssueAreaIDs;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    SqlCommand cmd;

                    // Check if the user language matches the general language
                    if (GenLangCode == UserLangCode)
                    {
                        cmd = new SqlCommand("SP_AMERP_HelpDesk_GetIssueSubArea", conn);
                    }
                    else
                    {
                        cmd = new SqlCommand("SP_AMERP_HelpDesk_GetIssueSubAreaLocale", conn);
                        cmd.Parameters.AddWithValue("@Language_ID", LangID);
                    }

                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@IssueArea_IDs", IssueAreaIDs);
                    cmd.Parameters.AddWithValue("@Company_ID", Company_ID);

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            ISAdata.Add(new
                            {
                                ID = reader["ID"],
                                Name = reader["Name"]
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(
                        ex.HResult,
                        $"{ex.GetType().FullName}: {ex.Message}",
                        ex.TargetSite.ToString(),
                        ex.StackTrace
                    );
                }
            }

            return new JsonResult(ISAdata);
        }
        #endregion



        #region :::GetIssueSubArea Uday Kumar J B 12-11-2024:::
        /// <summary>
        /// GetIssueSubArea.
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult loadStatus(HelpDesk_Rpt_IssueAreaGetloadStatusList HelpDesk_Rpt_IssueAreaGetloadStatusobj, string connString, int LogException)
        {
            var CaseStatus = new List<object>();

            try
            {
                int UserLangauge_ID = Convert.ToInt32(HelpDesk_Rpt_IssueAreaGetloadStatusobj.UserLanguageID);
                int GeneralLangauge_ID = Convert.ToInt32(HelpDesk_Rpt_IssueAreaGetloadStatusobj.GeneralLanguageID);

                using (var connection = new SqlConnection(connString))
                {
                    using (var command = new SqlCommand("SP_AMERP_HelpDesk_GetWorkFlowStatuses", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@UserLanguageID", UserLangauge_ID);
                        command.Parameters.AddWithValue("@GeneralLanguageID", GeneralLangauge_ID);

                        connection.Open();
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                CaseStatus.Add(new
                                {
                                    ID = reader.GetInt32(reader.GetOrdinal("WFStepStatus_ID")),
                                    Name = reader.GetString(reader.GetOrdinal("WFStepStatus_Nm"))
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(CaseStatus);
        }
        #endregion


        #region ::: load Reference Master data Uday Kumar J B 12-11-2024:::
        /// <summary>
        /// To load master drop downs
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult loadMasters(HelpDesk_Rpt_IssueAreaGetloadMastersList HelpDesk_Rpt_IssueAreaGetloadMastersobj, string connString, int LogException)
        {
            var jsonobj = new List<object>();

            try
            {
                int LangID = HelpDesk_Rpt_IssueAreaGetloadMastersobj.Language_ID;
                string GenLangCode = HelpDesk_Rpt_IssueAreaGetloadMastersobj.GeneralLanguageCode.ToString();
                string UserLangCode = HelpDesk_Rpt_IssueAreaGetloadMastersobj.UserLanguageCode.ToString();
                int CompanyID = string.IsNullOrEmpty(HelpDesk_Rpt_IssueAreaGetloadMastersobj.CompanyArray) ? 0 : Convert.ToInt32(HelpDesk_Rpt_IssueAreaGetloadMastersobj.CompanyArray.Split(',')[0]);

                using (var connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Retrieve RefMaster_ID and IsCompanySpecific
                    using (var command = new SqlCommand("SP_AMERP_HelpDesk_GetRefMasterByName", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@MasterName", HelpDesk_Rpt_IssueAreaGetloadMastersobj.MasterName.ToUpper());

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                int MasterID = reader.GetInt32(0);
                                bool IsCompSpecific = reader.GetBoolean(1);

                                if (GenLangCode == UserLangCode)
                                {
                                    // Direct retrieval
                                    using (var detailCommand = new SqlCommand("SP_AMERP_HelpDesk_GetRefMasterDetails", connection))
                                    {
                                        detailCommand.CommandType = CommandType.StoredProcedure;
                                        detailCommand.Parameters.AddWithValue("@MasterID", MasterID);
                                        detailCommand.Parameters.AddWithValue("@CompanyID", CompanyID);
                                        detailCommand.Parameters.AddWithValue("@IsCompSpecific", IsCompSpecific ? 1 : 0);

                                        using (var detailReader = detailCommand.ExecuteReader())
                                        {
                                            while (detailReader.Read())
                                            {
                                                jsonobj.Add(new
                                                {
                                                    ID = detailReader.GetInt32(0),
                                                    Name = detailReader.GetString(1)
                                                });
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    // Locale-specific retrieval
                                    using (var localeCommand = new SqlCommand("SP_AMERP_HelpDesk_GetRefMasterDetailsLocale", connection))
                                    {
                                        localeCommand.CommandType = CommandType.StoredProcedure;
                                        localeCommand.Parameters.AddWithValue("@MasterID", MasterID);
                                        localeCommand.Parameters.AddWithValue("@LangID", LangID);
                                        localeCommand.Parameters.AddWithValue("@CompanyID", CompanyID);
                                        localeCommand.Parameters.AddWithValue("@IsCompSpecific", IsCompSpecific ? 1 : 0);

                                        using (var localeReader = localeCommand.ExecuteReader())
                                        {
                                            while (localeReader.Read())
                                            {
                                                jsonobj.Add(new
                                                {
                                                    ID = localeReader.GetInt32(0),
                                                    Name = localeReader.GetString(1)
                                                });
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(new { rows = jsonobj });
        }
        #endregion



        #region :::LoadGrid Uday Kumar J B 13-11-2024:::
        /// <summary>
        /// LoadGrid.
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult Select(HelpDesk_Rpt_IssueAreaGetSelectList HelpDesk_Rpt_IssueAreaGetSelectobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            string IAdata = HelpDesk_Rpt_IssueAreaGetSelectobj.IAdata.ToString();
            string ISAdata = HelpDesk_Rpt_IssueAreaGetSelectobj?.ISAdata?.ToString() ?? string.Empty;
            string IANameData = HelpDesk_Rpt_IssueAreaGetSelectobj?.IANameData?.ToString() ?? string.Empty;
            string ISANameData = HelpDesk_Rpt_IssueAreaGetSelectobj?.ISANameData?.ToString() ?? string.Empty;
            int mode = Convert.ToInt32(HelpDesk_Rpt_IssueAreaGetSelectobj.mode);
            int branch = Convert.ToInt32(HelpDesk_Rpt_IssueAreaGetSelectobj.branch);
            string frmdate = HelpDesk_Rpt_IssueAreaGetSelectobj.FromDate;
            string todate = HelpDesk_Rpt_IssueAreaGetSelectobj.ToDate;
            bool ChkSelectAll = Convert.ToBoolean(HelpDesk_Rpt_IssueAreaGetSelectobj.ChkSelectAll);
            bool ChkSelectAllISA = Convert.ToBoolean(HelpDesk_Rpt_IssueAreaGetSelectobj.ChkSelectAllISA);
            IQueryable<CaseDetails> IQSRdata = default(dynamic);
            int Count = 0;
            int Total = 0;
            var jsonobj = default(dynamic);
            var jsonData = default(dynamic);
            string all = CommonFunctionalities.GetResourceString(HelpDesk_Rpt_IssueAreaGetSelectobj.UserCulture.ToString(), "all").ToString();
            string notspeciafied = CommonFunctionalities.GetResourceString(HelpDesk_Rpt_IssueAreaGetSelectobj.UserCulture.ToString(), "notspeciafied").ToString();
            int Language_ID = Convert.ToInt32(HelpDesk_Rpt_IssueAreaGetSelectobj.UserLanguageID);
            getCompanyBranchID(HelpDesk_Rpt_IssueAreaGetSelectobj, LogException);
            string IssueAreaArray = string.Empty;
            string FiterCriteriaFromIAreport = mode == 1 ? "model" : mode == 2 ? "functiongroup" : mode == 3 ? "issuearea" : "status";
            string todateFromIAreport = (todate == null || todate == "") ? "" : todate;
            string fromdateFromIAreport = (frmdate == null || frmdate == "") ? "" : frmdate;
            string IssueSuAreaArray = string.Empty;
            string CompanyID = string.Empty;
            object[] paramtrs = { CompanyID };
            if (ChkSelectAll)
            {
                IssueAreaArray = all;
            }
            else
            {
                IssueAreaArray = (IANameData == null || IANameData == "") ? "" : Common.DecryptString(IANameData.ToString()).Replace(",", " , ");
            }

            if (ChkSelectAllISA)
            {
                IssueSuAreaArray = all;
            }
            else
            {
                IssueSuAreaArray = (ISANameData == null || ISANameData == "") ? "" : Common.DecryptString(ISANameData.ToString()).Replace(",", " , ");
            }

            string SelectedBrachinCallHistoryReport = GetSelectedBranchName(connString, LogException, branch, CompanyID);
            try
            {
                IQSRdata = GetCaseHistory(HelpDesk_Rpt_IssueAreaGetSelectobj, connString, LogException);
                Count = IQSRdata.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                IQSRdata = IQSRdata.OrderByField<CaseDetails>(sidx, sord);
                jsonData = new
                {
                    total = Total,
                    page = page,
                    rows = (from a in IQSRdata.AsEnumerable()
                            select new
                            {
                                CaseNumber = "<span class='ServiceRequest' key='" + a.ServiceRequestID + "' style='color:blue;text-decoration:underline;cursor:pointer'>" + a.CaseNumber + "</span>",
                                Status = a.Status,
                                FunctionGroup = a.FunctionGroup,
                                Model = a.Model,
                                SerialNumber = a.SerialNumber,
                                IssueArea = a.IssueArea,
                                IssueSubArea = a.IssueSubArea,
                                Company_ID = a.Company_ID,
                                ServiceRequestID = a.ServiceRequestID,
                                Region = a.Region,
                                CompanyName = a.CompanyName,
                                BranchName = a.BranchName
                            }).ToList().Paginate(page, rows),
                    records = Count,
                    IssueAreaArray,
                    FiterCriteriaFromIAreport,
                    todateFromIAreport,
                    fromdateFromIAreport,
                    IssueSuAreaArray,
                    SelectedBrachinCallHistoryReport,
                    sidx,
                    sord
                };
                // gbl.InsertGPSDetails(Convert.ToInt32(HelpDesk_Rpt_IssueAreaGetSelectobj.Company_ID.ToString()), Convert.ToInt32(HelpDesk_Rpt_IssueAreaGetSelectobj.Branch_ID), HelpDesk_Rpt_IssueAreaGetSelectobj.User_ID, Common.GetObjectID("HelpDesk_Rpt_IssueArea_IssueSubArea"), 0, 0, 0, "Generated-Ticket History ", false, Convert.ToInt32(HelpDesk_Rpt_IssueAreaGetSelectobj.MenuID), Convert.ToDateTime(HelpDesk_Rpt_IssueAreaGetSelectobj.LoggedINDateTime));
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }

        private static string GetSelectedBranchName(string connString, int LogException, int branchID, string companyID)
        {
            using (SqlConnection conn = new SqlConnection(connString))
            {
                SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetBranchData", conn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@CompanyID", companyID);
                cmd.Parameters.AddWithValue("@BranchID", branchID);

                conn.Open();
                return cmd.ExecuteScalar()?.ToString();
            }
        }

        public static void getCompanyBranchID(HelpDesk_Rpt_IssueAreaGetSelectList HelpDesk_Rpt_IssueAreaGetSelectobj, int LogException)
        {
            string BranchID = string.Empty;
            string CompanyID = string.Empty;
            try
            {
                Branch branchs = new Branch();

                if (HelpDesk_Rpt_IssueAreaGetSelectobj.BranchData != null && HelpDesk_Rpt_IssueAreaGetSelectobj.BranchData != "")
                {
                    branchs = JObject.Parse(Common.DecryptString(HelpDesk_Rpt_IssueAreaGetSelectobj.BranchData)).ToObject<Branch>(); //~Manju M
                }

                if (HelpDesk_Rpt_IssueAreaGetSelectobj.BranchData != null && HelpDesk_Rpt_IssueAreaGetSelectobj.BranchData != "")
                {
                    if (branchs.Branchs.Count > 0)
                    {
                        for (int i = 0; i < branchs.Branchs.Count; i++)
                        {
                            BranchID = BranchID + branchs.Branchs[i].ID + ", ";
                        }
                        BranchID = BranchID.Remove(BranchID.LastIndexOf(','), 1);
                    }
                }
                Company companys = new Company();
                if (HelpDesk_Rpt_IssueAreaGetSelectobj.CompanyData != null && HelpDesk_Rpt_IssueAreaGetSelectobj.CompanyData != "")
                {
                    companys = JObject.Parse(Common.DecryptString(HelpDesk_Rpt_IssueAreaGetSelectobj.CompanyData)).ToObject<Company>(); //~Manju M
                }

                if (HelpDesk_Rpt_IssueAreaGetSelectobj.CompanyData != null && HelpDesk_Rpt_IssueAreaGetSelectobj.CompanyData != "")
                {
                    if (companys.Companys.Count > 0)
                    {
                        for (int i = 0; i < companys.Companys.Count; i++)
                        {
                            CompanyID = CompanyID + companys.Companys[i].ID + ", ";
                        }
                        CompanyID = CompanyID.Remove(CompanyID.LastIndexOf(','), 1);
                    }

                }
            }
            catch (Exception ex)
            {

                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
        }


        private static IQueryable<CaseDetails> GetCaseHistory(HelpDesk_Rpt_IssueAreaGetSelectList HelpDesk_Rpt_IssueAreaGetSelectobj, string connString, int LogException)
        {
            string IAdata = HelpDesk_Rpt_IssueAreaGetSelectobj?.IAdata?.ToString() ?? string.Empty;
            string ISAdata = HelpDesk_Rpt_IssueAreaGetSelectobj?.ISAdata?.ToString() ?? string.Empty;
            string IANameData = HelpDesk_Rpt_IssueAreaGetSelectobj?.IANameData?.ToString() ?? string.Empty;
            string ISANameData = HelpDesk_Rpt_IssueAreaGetSelectobj?.ISANameData?.ToString() ?? string.Empty;
            int mode = Convert.ToInt32(HelpDesk_Rpt_IssueAreaGetSelectobj.mode);
            string frmdate = HelpDesk_Rpt_IssueAreaGetSelectobj.FromDate;
            string todate = HelpDesk_Rpt_IssueAreaGetSelectobj.ToDate;
            bool ChkSelectAll = Convert.ToBoolean(HelpDesk_Rpt_IssueAreaGetSelectobj.ChkSelectAll);
            bool ChkSelectAllISA = Convert.ToBoolean(HelpDesk_Rpt_IssueAreaGetSelectobj.ChkSelectAllISA);

            IEnumerable<CaseDetails> SRdata = default(dynamic);
            IQueryable<CaseDetails> IQSRdata = default(dynamic);
            List<dynamic> arr = new List<dynamic>();
            string all = CommonFunctionalities.GetResourceString(HelpDesk_Rpt_IssueAreaGetSelectobj.UserCulture.ToString(), "all").ToString();
            string notspeciafied = CommonFunctionalities.GetResourceString(HelpDesk_Rpt_IssueAreaGetSelectobj.UserCulture.ToString(), "notspeciafied").ToString();
            int Language_ID = Convert.ToInt32(HelpDesk_Rpt_IssueAreaGetSelectobj.UserLanguageID);
            int generalLanguageID = Convert.ToInt32(HelpDesk_Rpt_IssueAreaGetSelectobj.GeneralLanguageID);
            string Company_ID = Convert.ToString(HelpDesk_Rpt_IssueAreaGetSelectobj.Company_ID);
            string Branch_ID = Convert.ToString(HelpDesk_Rpt_IssueAreaGetSelectobj.Branch_ID);
            List<HD_ServiceRequest> SRAll = default(dynamic);
            try
            {
                string QueryData = string.Empty;

                DateTime FromDate = Convert.ToDateTime(frmdate);
                DateTime ToDate = Convert.ToDateTime(todate).AddDays(1);
                object[] paramtrs = { FromDate, ToDate, Company_ID, IAdata, Company_ID, Branch_ID, ISAdata };

                var caseDetailsList = new List<CaseDetails>();

                if (HelpDesk_Rpt_IssueAreaGetSelectobj.UserLanguageCode.ToString() == HelpDesk_Rpt_IssueAreaGetSelectobj.GeneralLanguageCode.ToString())
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetCaseHistory", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;

                            cmd.Parameters.AddWithValue("@FromDate", (object)FromDate ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@ToDate", (object)ToDate ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                            cmd.Parameters.AddWithValue("@Branch_ID", Branch_ID);
                            cmd.Parameters.AddWithValue("@IAdata", (object)IAdata ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@ISAdata", (object)ISAdata ?? DBNull.Value);

                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    caseDetailsList.Add(new CaseDetails
                                    {
                                        Region = reader["Region"].ToString(),
                                        ServiceRequestID = Convert.ToInt32(reader["ServiceRequestID"]),
                                        CaseNumber = reader["CaseNumber"].ToString(),
                                        Status = reader["Status"].ToString(),
                                        FunctionGroup = reader["FunctionGroup"].ToString(),
                                        Model = reader["Model"].ToString(),
                                        SerialNumber = reader["SerialNumber"].ToString(),
                                        IssueArea = reader["IssueArea"].ToString(),
                                        IssueSubArea = reader["IssueSubArea"].ToString(),
                                        CompanyName = reader["CompanyName"].ToString(),
                                        BranchName = reader["BranchName"].ToString(),
                                        Company_ID = Convert.ToInt32(reader["Company_ID"])
                                    });
                                }
                                IQSRdata = caseDetailsList.AsQueryable<CaseDetails>();
                            }
                        }
                    }
                }
                else
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetCaseHistoryLocale", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;

                            cmd.Parameters.AddWithValue("@FromDate", (object)FromDate ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@ToDate", (object)ToDate ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                            cmd.Parameters.AddWithValue("@Branch_ID", Branch_ID);
                            cmd.Parameters.AddWithValue("@IAdata", (object)IAdata ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@ISAdata", (object)ISAdata ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@Language_ID", Language_ID);
                            cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                            conn.Open();
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var caseDetail = new CaseDetails
                                    {
                                        Region = Common.getRegionName(connString, LogException, Convert.ToInt32(Language_ID), Convert.ToInt32(generalLanguageID), Convert.ToInt32(reader["Branch_ID"])),
                                        ServiceRequestID = Convert.ToInt32(reader["ServiceRequestID"]),
                                        CaseNumber = reader["CaseNumber"]?.ToString(),
                                        Status = reader["Status"]?.ToString(),
                                        FunctionGroup = reader["FunctionGroup"]?.ToString(),
                                        Model = reader["Model"]?.ToString(),
                                        SerialNumber = reader["SerialNumber"]?.ToString(),
                                        IssueArea = reader["IssueArea"]?.ToString(),
                                        IssueSubArea = reader["IssueSubArea"]?.ToString(),
                                        Company_ID = Convert.ToInt32(reader["Company_ID"])
                                    };
                                    caseDetailsList.Add(caseDetail);
                                }
                                IQSRdata = caseDetailsList.AsQueryable<CaseDetails>();
                            }
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return IQSRdata;
        }

        #endregion




        #region ::: To Export Uday Kumar J B 13-11-2024:::
        /// <summary>
        /// To Export 
        /// </summary>
        /// <returns>...</returns>
        public static async Task<object> Export(HelpDesk_Rpt_IssueAreaGetSelectList HelpDesk_Rpt_IssueAreaGetSelectobj, string connString, int LogException, string filter, string advnceFilter, string sidx, string sord)
        {
            DataTable Dt = new DataTable();
            int userID = HelpDesk_Rpt_IssueAreaGetSelectobj.User_ID;
            int companyID = Convert.ToInt32(HelpDesk_Rpt_IssueAreaGetSelectobj.Company_ID);
            int cnt = 0;
            try
            {
                string IAArray = HelpDesk_Rpt_IssueAreaGetSelectobj.IssueAreaArray == null ? "" : Convert.ToString(HelpDesk_Rpt_IssueAreaGetSelectobj.IssueAreaArray);
                string filteredIN = HelpDesk_Rpt_IssueAreaGetSelectobj.FiterCriteriaFromIAreport == null ? "" : Convert.ToString(HelpDesk_Rpt_IssueAreaGetSelectobj.FiterCriteriaFromIAreport);
                string Tdate = HelpDesk_Rpt_IssueAreaGetSelectobj.todateFromIAreport.ToString();
                string Fdate = HelpDesk_Rpt_IssueAreaGetSelectobj.fromdateFromIAreport.ToString();
                string ISAArray = HelpDesk_Rpt_IssueAreaGetSelectobj.IssueSuAreaArray == null ? "" : Convert.ToString(HelpDesk_Rpt_IssueAreaGetSelectobj.IssueSuAreaArray);
                string SelectedBrachinCallHistoryReport = HelpDesk_Rpt_IssueAreaGetSelectobj.SelectedBrachinCallHistoryReport.ToString();
                List<CaseDetails> SRequestReport = null;
                SRequestReport = GetCaseHistory(HelpDesk_Rpt_IssueAreaGetSelectobj, connString, LogException).ToList();
                if (sidx != null && sord != null)
                {
                    SRequestReport = SRequestReport.AsQueryable().OrderByField<CaseDetails>(sidx.ToString(), sord.ToString()).ToList();
                }
                var IAcaseDetailArray = (from a in SRequestReport
                                         select new
                                         {
                                             a.Region,
                                             a.CompanyName,
                                             a.BranchName,
                                             a.CaseNumber,
                                             a.Status,
                                             a.FunctionGroup,
                                             a.Model,
                                             a.IssueArea,
                                             a.IssueSubArea,
                                             a.SerialNumber
                                         }).ToList();
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_IssueAreaGetSelectobj.UserCulture.ToString(), "Region").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_IssueAreaGetSelectobj.UserCulture.ToString(), "Company").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_IssueAreaGetSelectobj.UserCulture.ToString(), "Branch").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_IssueAreaGetSelectobj.UserCulture.ToString(), "casenumber").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_IssueAreaGetSelectobj.UserCulture.ToString(), "model").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_IssueAreaGetSelectobj.UserCulture.ToString(), "serialnumber").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_IssueAreaGetSelectobj.UserCulture.ToString(), "issuearea").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_IssueAreaGetSelectobj.UserCulture.ToString(), "issuesubarea").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_IssueAreaGetSelectobj.UserCulture.ToString(), "functiongroup").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_IssueAreaGetSelectobj.UserCulture.ToString(), "status").ToString());

                DataTable dtOptions = new DataTable();
                //dtOptions.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "branch").ToString());
                dtOptions.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_IssueAreaGetSelectobj.UserCulture.ToString(), "fromdate").ToString());
                dtOptions.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_IssueAreaGetSelectobj.UserCulture.ToString(), "todate").ToString());
                dtOptions.Rows.Add(Fdate, Tdate);

                DataTable DtAlignment = new DataTable();
                DtAlignment.Columns.Add("Region");
                DtAlignment.Columns.Add("CompanyName");
                DtAlignment.Columns.Add("BranchName");
                DtAlignment.Columns.Add("ServiceRequestNumber");
                DtAlignment.Columns.Add("Case Number");
                DtAlignment.Columns.Add("Model");
                DtAlignment.Columns.Add("Serial Number");
                DtAlignment.Columns.Add("Issue Area");
                DtAlignment.Columns.Add("Issue Sub-Area");
                DtAlignment.Columns.Add("Function Group");
                DtAlignment.Columns.Add("status");
                DtAlignment.Rows.Add(0, 0, 0, 0, 0, 0, 0, 0, 0, 0);

                DataTable dtHeader = new DataTable();
                dtHeader.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_IssueAreaGetSelectobj.UserCulture.ToString(), filteredIN).ToString());
                string IAID = IAArray.ToString() + " ";
                IAID = IAID.Remove(IAID.Length - 1);
                dtHeader.Rows.Add(IAID);

                DataTable dtIssueSubArea = new DataTable();
                dtIssueSubArea.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_IssueAreaGetSelectobj.UserCulture.ToString(), "issuesubarea").ToString());
                string ISAID = ISAArray.ToString() + " ";
                ISAID = ISAID.Remove(ISAID.Length - 1);
                dtIssueSubArea.Rows.Add(ISAID);

                DataSet ds = new DataSet();
                ds.Tables.Add(dtHeader);
                if (filteredIN == "issuearea")
                    ds.Tables.Add(dtIssueSubArea);

                cnt = IAcaseDetailArray.Count;
                if (cnt > 0)
                {
                    for (int i = 0; i < cnt; i++)
                    {
                        Dt.Rows.Add(IAcaseDetailArray[i].Region, IAcaseDetailArray[i].CompanyName, IAcaseDetailArray[i].BranchName, IAcaseDetailArray[i].CaseNumber, IAcaseDetailArray[i].Model, IAcaseDetailArray[i].SerialNumber, IAcaseDetailArray[i].IssueArea, IAcaseDetailArray[i].IssueSubArea, IAcaseDetailArray[i].FunctionGroup, IAcaseDetailArray[i].Status);
                    }
                    ExportReportExportCR5List exportReport = new ExportReportExportCR5List
                    {
                        FileName = "CaseHistory",
                        Branch = Convert.ToString(HelpDesk_Rpt_IssueAreaGetSelectobj.Branch_ID),
                        Company_ID = HelpDesk_Rpt_IssueAreaGetSelectobj.Company_ID,
                        UserCulture = HelpDesk_Rpt_IssueAreaGetSelectobj.UserCulture,
                        dt = Dt, // You can populate this with actual data as needed
                        exprtType = HelpDesk_Rpt_IssueAreaGetSelectobj.exprtType, // Set an appropriate type for export (e.g., 1 for PDF, 2 for Excel, etc.)
                        Header = CommonFunctionalities.GetResourceString(HelpDesk_Rpt_IssueAreaGetSelectobj.UserCulture.ToString(), "CaseHistoryreport").ToString(),
                        Options = dtOptions, // Populate this with your report options
                        selection = ds, // Add selection-related data here
                        Alignment = DtAlignment // Define alignment details for table columns
                    };
                    var result = await ReportExportCR5.Export(exportReport, connString, LogException);
                    return result.Value;
                    // ReportExportCR5.Export(HelpDesk_Rpt_IssueAreaGetSelectobj.exprtType, Dt, dtOptions, ds, DtAlignment, "CaseHistory", CommonFunctionalities.GetResourceString(HelpDesk_Rpt_IssueAreaGetSelectobj.UserCulture.ToString(), "CaseHistoryreport").ToString());
                    //    gbl.InsertGPSDetails(Convert.ToInt32(HelpDesk_Rpt_IssueAreaGetSelectobj.Company_ID.ToString()), HelpDesk_Rpt_IssueAreaGetSelectobj.Branch_ID, HelpDesk_Rpt_IssueAreaGetSelectobj.User_ID, Common.GetObjectID("HelpDesk_Rpt_IssueArea_IssueSubArea"), 0, 0, 0, "Ticket History-Export ", false, Convert.ToInt32(HelpDesk_Rpt_IssueAreaGetSelectobj.MenuID), Convert.ToDateTime(HelpDesk_Rpt_IssueAreaGetSelectobj.LoggedINDateTime));
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1) LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return false;
        }
        #endregion




        #region :::  HelpDesk_Rpt_IssueArea list and obj classes Uday Kumar J B 13-11-2024:::
        /// <summary>
        /// HelpDesk_Rpt_IssueArea 
        /// </summary>
        /// <returns>...</returns>
        public class HelpDesk_Rpt_IssueAreaGetSelectList
        {
            public string IAdata { get; set; }
            public string ISAdata { get; set; }
            public string IANameData { get; set; }
            public string ISANameData { get; set; }
            public int mode { get; set; }
            public int branch { get; set; }
            public string FromDate { get; set; }
            public string ToDate { get; set; }
            public string ChkSelectAll { get; set; }
            public string ChkSelectAllISA { get; set; }

            public string UserCulture { get; set; }
            public int UserLanguageID { get; set; }
            public string BranchData { get; set; }
            public string CompanyData { get; set; }
            public int Company_ID { get; set; }
            public int Branch_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public int GeneralLanguageID { get; set; }
            public string UserLanguageCode { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string IssueAreaArray { get; set; }
            public string FiterCriteriaFromIAreport { get; set; }
            public string todateFromIAreport { get; set; }
            public string fromdateFromIAreport { get; set; }
            public string IssueSuAreaArray { get; set; }
            public string SelectedBrachinCallHistoryReport { get; set; }
            public int exprtType { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filter { get; set; }
            public string advanceFilter { get; set; }


        }


        public class HelpDesk_Rpt_IssueAreaGetloadMastersList
        {
            public string MasterName { get; set; }
            public int isComp { get; set; }
            public string CompanyArray { get; set; }
            public int Language_ID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
        }
        public class HelpDesk_Rpt_IssueAreaGetloadStatusList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        public class HelpDesk_Rpt_IssueAreaGetGetIssueSubAreaList
        {
            public int Company_ID { get; set; }
            public int LanguageID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
            public string IssueAreaIDs { get; set; }
        }

        public class HelpDesk_Rpt_IssueAreaGetModelsList
        {
            public string CompanyArray { get; set; }
            public int LanguageID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
        }
        public class HelpDesk_Rpt_IssueAreaSelectBranchList
        {
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int Employee_ID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
        }

        #endregion



        #region :::  HelpDesk_Rpt_IssueArea  classes Uday Kumar J B 13-11-2024:::
        /// <summary>
        /// HelpDesk_Rpt_IssueArea 
        /// </summary>
        /// <returns>...</returns>

        public partial class GNM_CompanyBrands
        {
            public int Company_Brand_ID { get; set; }
            public int Company_ID { get; set; }
            public int Brand_ID { get; set; }
        }

        public partial class GNM_ProductType
        {
            public int ProductType_ID { get; set; }
            public int Brand_ID { get; set; }
            public string ProductType_Name { get; set; }
            public bool ProductType_IsActive { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }
        }

        public partial class GNM_ProductTypeLocale
        {
            public int ProductTypeLocale_ID { get; set; }
            public int ProductType_ID { get; set; }
            public string ProductType_Name { get; set; }
            public int Language_ID { get; set; }

        }

        public class CaseDetails
        {
            public int ServiceRequestID { get; set; }
            public string CaseNumber { get; set; }
            public string Status { get; set; }
            public string FunctionGroup { get; set; }
            public string Model { get; set; }
            public string SerialNumber { get; set; }
            public string IssueArea { get; set; }
            public string IssueSubArea { get; set; }
            public string CompanyName { get; set; }
            public string BranchName { get; set; }
            public int Company_ID { get; set; }
            public string Region { get; set; }

        }
        public class Branch
        {
            public List<Branchs> Branchs
            {
                get;
                set;
            }
        }
        public class Branchs
        {

            public int ID
            {
                get;
                set;
            }
            public string Name
            {
                get;
                set;
            }
        }


        public class Company
        {
            public List<Companys> Companys
            {
                get;
                set;
            }
        }
        public class Companys
        {

            public int ID
            {
                get;
                set;
            }
            public string Name
            {
                get;
                set;
            }
        }
        #endregion



    }
}
