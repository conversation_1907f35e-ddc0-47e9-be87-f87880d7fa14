using PBC.UtilityService.Utilities.DTOs;

namespace PBC.UtilityService.Services
{
    public interface IBCryptService
    {
        /// <summary>
        /// Generates a DMS password hash using BCrypt with AMP_SP salt
        /// </summary>
        /// <param name="request">The password generation request</param>
        /// <returns>BCrypt hashed password</returns>
        Task<BCryptResponse<string>> GenerateDMSPasswordAsync(GenerateDMSPasswordRequest request);

        /// <summary>
        /// Checks if a password matches the stored hash
        /// </summary>
        /// <param name="request">The password check request</param>
        /// <returns>True if password matches</returns>
        Task<BCryptResponse<bool>> CheckPasswordWithDBAsync(CheckPasswordRequest request);
    }
}
