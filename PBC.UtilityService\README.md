# PBC.UtilityService

A .NET 8 Web API project for utility services in the PBC microservices architecture.

## Configuration

### Ports
- **HTTP**: 5003

### Database
- **Connection String**: `Server=(localdb)\\mssqllocaldb;Database=UtilitiesSharedAPI;Trusted_Connection=true;MultipleActiveResultSets=true`

### Service URLs
- **Core Service**: http://localhost:5001
- **Helpdesk Service**: http://localhost:5002

## Features

- **Health Checks**: Available at `/health` and `/api/health`
- **Swagger Documentation**: Available at `/swagger` in development
- **CORS**: Configured to allow all origins, methods, and headers
- **HTTP Client**: Configured for inter-service communication

## Project Structure

```
PBC.UtilityService/
├── Controllers/
│   └── HealthController.cs
├── Services/
│   ├── IHealthService.cs
│   └── HealthService.cs
├── HttpClients/
├── Utilities/
├── Properties/
│   └── launchSettings.json
├── Program.cs
├── appsettings.json
├── appsettings.Development.json
└── PBC.UtilityService.csproj
```

## Running the Application

```bash
cd PBC.UtilityService
dotnet run
```

The application will be available at:
- HTTP: http://localhost:5003
- Swagger: http://localhost:5003/swagger

## Health Check Endpoints

- `GET /health` - Basic health check
- `GET /api/health` - Detailed health status
- `GET /api/health/status` - Service status information

## Dependencies

The project includes the same comprehensive set of NuGet packages as the other PBC services:
- ASP.NET Core 8.0
- Entity Framework Core
- Swagger/OpenAPI
- JWT Authentication
- AWS SDK
- Azure Storage
- Excel processing (ClosedXML, EPPlus)
- PDF processing (iTextSharp)
- Web scraping (PuppeteerSharp, AngleSharp)
- And more...
