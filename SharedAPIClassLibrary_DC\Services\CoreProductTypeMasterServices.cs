﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreProductTypeMasterServices
    {
        static string AppPath = string.Empty;
        private static JTokenReader jTR;

        #region ::: get Landing Grid Data /Mithun:::
        /// <summary>
        /// get Landing Grid Data
        /// </summary> 
        public static IQueryable<ProductTypeMaster> getLandingGridData(SelectProductTypeList1 getLandingGridDataObj, string constring, int LogException, int BrandID, int LanguageID, string GeneralCulture, string UserCulture, int GeneralLanguageID)
        {
            List<ProductTypeMaster> productTypeMasters = new List<ProductTypeMaster>();
            try
            {
                int Count = 0;

                string YesE = CommonFunctionalities.GetResourceString(GeneralCulture.ToString(), "yes").ToString();
                string NoE = CommonFunctionalities.GetResourceString(GeneralCulture.ToString(), "no").ToString();
                string YesL = CommonFunctionalities.GetResourceString(UserCulture.ToString(), "yes").ToString();
                string NoL = CommonFunctionalities.GetResourceString(UserCulture.ToString(), "no").ToString();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Fetch ProductType data
                    string productTypeProcedure = "Up_Sel_Am_Erp_SelectProductTypes";
                    List<GNM_ProductType> productTypes = new List<GNM_ProductType>();

                    using (SqlCommand command = new SqlCommand(productTypeProcedure, connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@BrandID", BrandID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                productTypes.Add(new GNM_ProductType
                                {
                                    ProductType_ID = reader.GetInt32(0),
                                    ProductType_Name = reader.GetString(1),
                                    ProductType_IsActive = reader.GetBoolean(2)
                                });
                            }
                        }
                    }

                    // Fetch ProductTypeLocale data if LanguageID is different from GeneralLanguageID
                    List<GNM_ProductTypeLocale> productTypeLocales = new List<GNM_ProductTypeLocale>();
                    if (LanguageID != Convert.ToInt32(GeneralLanguageID))
                    {
                        string productTypeLocaleProcedure = "Up_Sel_Am_Erp_SelectProductTypesLocale";

                        using (SqlCommand command = new SqlCommand(productTypeLocaleProcedure, connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@BrandID", BrandID);
                            command.Parameters.AddWithValue("@LanguageID", LanguageID);

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    productTypeLocales.Add(new GNM_ProductTypeLocale
                                    {
                                        ProductType_ID = reader.GetInt32(0),
                                        ProductType_Name = reader.GetString(1)
                                    });
                                }
                            }
                        }
                    }

                    // Combine results
                    if (LanguageID == Convert.ToInt32(GeneralLanguageID))
                    {
                        foreach (var productType in productTypes)
                        {
                            productTypeMasters.Add(new ProductTypeMaster
                            {
                                ProductType_ID = productType.ProductType_ID,
                                ProductType_Name = productType.ProductType_Name,
                                ProductType_IsActive = productType.ProductType_IsActive ? YesE : NoE
                            });
                        }
                    }
                    else
                    {
                        foreach (var productType in productTypes)
                        {
                            var locale = productTypeLocales.FirstOrDefault(l => l.ProductType_ID == productType.ProductType_ID);
                            productTypeMasters.Add(new ProductTypeMaster
                            {
                                ProductType_ID = productType.ProductType_ID,
                                ProductType_Name = locale?.ProductType_Name ?? productType.ProductType_Name,
                                ProductType_IsActive = productType.ProductType_IsActive ? YesL : NoL
                            });
                        }
                    }

                    if (productTypeMasters.Count > 0)
                    {
                        //gbl.InsertGPSDetails(
                        //    Convert.ToInt32(getLandingGridDataObj.Company_ID),
                        //    Convert.ToInt32(getLandingGridDataObj.Branch),
                        //    Convert.ToInt32(getLandingGridDataObj.User_ID),
                        //    Convert.ToInt32(Common.GetObjectID("CoreProductTypeMaster",constring)),
                        //    0, 0, 0,
                        //    GetGlobalResourceObject(Culture.ToString(), "Producttype").ToString() + " - Viewed",
                        //    false,
                        //    Convert.ToInt32(getLandingGridDataObj.MenuID),
                        //    Convert.ToDateTime(getLandingGridDataObj.LoggedINDateTime)
                        //);
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return productTypeMasters.AsQueryable();


        }


        #endregion

        #region ::: Select /Mithun:::
        /// <summary>
        /// To Select Product Types for Brand
        /// </summary>
        public static IActionResult Select(SelectProductTypeList1 SelectObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {
            var jsonData = default(dynamic);

            try
            {

                int Count = 0;
                int Total = 0;

                IQueryable<ProductTypeMaster> IQProductTypeMaster = null;
                getLandingGridDataList getLandingGridDataObj = new getLandingGridDataList();
                IQProductTypeMaster = getLandingGridData(SelectObj, constring, LogException, SelectObj.BrandID, SelectObj.LanguageID, SelectObj.GeneralCulture, SelectObj.UserCulture, SelectObj.GeneralLanguageID);

                if (_search)
                {
                    Filters filtersobj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    IQProductTypeMaster = IQProductTypeMaster.FilterSearch<ProductTypeMaster>(filtersobj);
                }
                if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse(Query).ToObject<AdvanceFilter>();
                    IQProductTypeMaster = IQProductTypeMaster.AdvanceSearch<ProductTypeMaster>(advnfilter);
                }

                IQProductTypeMaster = IQProductTypeMaster.OrderByField<ProductTypeMaster>(sidx, sord);

                Count = IQProductTypeMaster.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                // Added by Manjunatha P for QA correction 
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }
                //End
                string Lbl_Refresh = CommonFunctionalities.GetGlobalResourceObject(SelectObj.UserCulture.ToString(), "refresh").ToString();
                string Lbl_AdvanceSearch = CommonFunctionalities.GetGlobalResourceObject(SelectObj.UserCulture.ToString(), "advancesearch").ToString();
                jsonData = new
                {
                    total = Total,
                    page = page,
                    rows = (from a in IQProductTypeMaster.AsEnumerable()
                            select new
                            {
                                ID = a.ProductType_ID,
                                //edit = "<img id='" + a.ProductType_ID + "' src='" + AppPath + "/Content/edit.gif' key='" + a.ProductType_ID + "' class='EditProductType' editmode='false'/>",
                                edit = "<a title=" + CommonFunctionalities.GetGlobalResourceObject(SelectObj.UserCulture.ToString(), "view").ToString() + " href='#' id='" + a.ProductType_ID + "' src='" + AppPath + "/Content/edit.gif' key='" + a.ProductType_ID + "' class='EditProductType font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input type='checkbox' key='" + a.ProductType_ID + "' defaultchecked=''  id='chk" + a.ProductType_ID + "' class='ProductTypeDelete'/>",
                                ProductType_Name = (a.ProductType_Name),
                                ProductType_IsActive = a.ProductType_IsActive,
                                Locale = "<a key='" + a.ProductType_ID + "' src='" + AppPath + "/Content/local.png' class='ProductTypeLocale' alt='Localize' width='20' height='20'  title='Localize'><i class='fa fa-globe'></i></a>",
                                View = "<img id='" + a.ProductType_ID + "' src='" + AppPath + "/Content/plus.gif' key='" + a.ProductType_ID + "' class='ViewProductTypeLocale' ProductType='" + a.ProductType_Name + "'/>",
                            }).ToList().Paginate(page, rows),
                    records = Count,
                    filter = filters,
                    advanceFilter = Query,
                    SelectObj.BrandID,
                    SelectObj.LanguageID,
                    Lbl_AdvanceSearch,
                    Lbl_Refresh,
                };

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }
        #endregion

        #region ::: SelectReferenceMaster /Mithun:::
        /// <summary>
        /// To get Refrence Master records for a Master
        /// </summary> 
        public static IActionResult SelectReferenceMaster(SelectReferenceMasterProductTypeList SelectReferenceMasterObj, string constring, int LogException)
        {
            var Masterdata = default(dynamic);
            try
            {
                int Company_ID = Convert.ToInt32(SelectReferenceMasterObj.Company_ID);
                int Language_ID = Convert.ToInt32(SelectReferenceMasterObj.UserLanguageID);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    SqlCommand cmd = new SqlCommand("Up_Sel_AM_ERP_GetReferenceStateMasterData", connection);
                    cmd.CommandType = CommandType.StoredProcedure;

                    cmd.Parameters.AddWithValue("@RefMasterName", "BRAND");
                    cmd.Parameters.AddWithValue("@LanguageID", Language_ID);
                    cmd.Parameters.AddWithValue("@CompanyID", Company_ID);

                    SqlDataAdapter adapter = new SqlDataAdapter(cmd);
                    System.Data.DataTable dt = new System.Data.DataTable();
                    adapter.Fill(dt);

                    Masterdata = new
                    {
                        ReferenceMasterData = dt.AsEnumerable().Select(row => new
                        {
                            ID = row.Field<int>("ID"),
                            Name = row.Field<string>("Name")
                        })
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(Masterdata, JsonRequestBehavior.AllowGet);
            return new JsonResult(Masterdata);
        }

        #endregion

        #region ::: SelectParticularProductType /Mithun:::
        /// <summary>
        /// To Select Particular Product type
        /// </summary>
        public static IActionResult SelectParticularProductType(SelectParticularProductTypeList SelectParticularProductTypeObj, string constring, int LogException)
        {
            var x = default(dynamic);
            try
            {
                int Language_ID = Convert.ToInt32(SelectParticularProductTypeObj.UserLanguageID);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand("Up_Sel_Am_Erp_SelectParticularProductType", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@ProductTypeID", SelectParticularProductTypeObj.ProductTypeID);
                        command.Parameters.AddWithValue("@LanguageID", Language_ID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                x = new
                                {
                                    ProductType_ID = reader.GetInt32(reader.GetOrdinal("ProductType_ID")),
                                    ProductType_Name = reader.GetString(reader.GetOrdinal("ProductType_Name")),
                                    ProductType_IsActive = reader.GetBoolean(reader.GetOrdinal("ProductType_IsActive")),
                                    ProductType_Brand_ID = reader.GetInt32(reader.GetOrdinal("Brand_ID")),
                                    ProductTypeLocale_ID = reader.IsDBNull(reader.GetOrdinal("ProductTypeLocale_ID")) ? "" : reader.GetInt32(reader.GetOrdinal("ProductTypeLocale_ID")).ToString(),
                                    ProductTypeLocale_Name = reader.IsDBNull(reader.GetOrdinal("ProductTypeLocale_Name")) ? "" : reader.GetString(reader.GetOrdinal("ProductTypeLocale_Name")),
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(x, JsonRequestBehavior.AllowGet);
            return new JsonResult(x);
        }

        #endregion

        #region ::: Save /Mihtun:::
        /// <summary>
        /// To Insert and Update Product type
        /// </summary>
        public static IActionResult Save(SaveProductTypeList SaveObj, string constring, int LogException)
        {
            string Msg = string.Empty;
            try
            {
                JObject jObj = JObject.Parse(SaveObj.data);
                int Count = jObj["rows"].Count();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    for (int i = 0; i < Count; i++)
                    {
                        GNM_ProductType PTRow = jObj["rows"].ElementAt(i).ToObject<GNM_ProductType>();

                        using (SqlCommand command = new SqlCommand("Up_Ins_AM_ERP_SaveProductType", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.Add(new SqlParameter("@ProductType_ID", PTRow.ProductType_ID));
                            command.Parameters.Add(new SqlParameter("@ProductType_Name", Uri.UnescapeDataString(PTRow.ProductType_Name)));
                            command.Parameters.Add(new SqlParameter("@ProductType_IsActive", PTRow.ProductType_IsActive));
                            command.Parameters.Add(new SqlParameter("@Brand_ID", PTRow.Brand_ID));
                            command.Parameters.Add(new SqlParameter("@ModifiedBy", Convert.ToInt32(SaveObj.User_ID)));
                            command.Parameters.Add(new SqlParameter("@ModifiedDate", DateTime.Now));

                            var newProductTypeIDParameter = new SqlParameter("@NewProductType_ID", SqlDbType.Int)
                            {
                                Direction = ParameterDirection.Output
                            };
                            command.Parameters.Add(newProductTypeIDParameter);

                            command.ExecuteNonQuery();

                            int productTypeID = PTRow.ProductType_ID != 0 ? PTRow.ProductType_ID : (int)newProductTypeIDParameter.Value;

                            //gbl.InsertGPSDetails(
                            //    Convert.ToInt32(SaveObj.Company_ID),
                            //    Convert.ToInt32(SaveObj.Branch),
                            //    Convert.ToInt32(SaveObj.User_ID),
                            //    Convert.ToInt32(Common.GetObjectID("CoreProductTypeMaster",constring)),
                            //    productTypeID,
                            //    0, 0,
                            //    (PTRow.ProductType_ID != 0 ? "Updated " : "Inserted ") + Uri.UnescapeDataString(PTRow.ProductType_Name),
                            //    false,
                            //    Convert.ToInt32(SaveObj.MenuID),
                            //    Convert.ToDateTime(SaveObj.LoggedINDateTime)
                            //);
                        }
                    }
                }

                Msg = "Saved";
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                Msg = string.Empty;
            }
            //return Msg;
            return new JsonResult(Msg);
        }
        #endregion

        #region ::: UpdateLocale /Mithun:::
        /// <summary>
        /// To Update Product type Locale
        /// </summary>
        public static IActionResult UpdateLocale(UpdateLocaleProductTypeList UpdateLocaleObj, string constring, int LogException)
        {
            int ProductTypeLocale_ID = 0;
            var result = new { ProductTypeLocale_ID = 0 };
            try
            {
                // Parse the input JSON object
                JObject jObj = JObject.Parse(UpdateLocaleObj.data);
                var PTLRow = jObj.ToObject<GNM_ProductTypeLocale>();
                string decryptedProductTypeName = Common.DecryptString(PTLRow.ProductType_Name);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand("Up_Ins_Upd_Am_Erp_UpdateLocale", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@ProductTypeLocale_ID", (object)PTLRow.ProductTypeLocale_ID ?? DBNull.Value);
                        command.Parameters.AddWithValue("@ProductType_Name", decryptedProductTypeName);
                        command.Parameters.AddWithValue("@ProductType_ID", PTLRow.ProductType_ID);

                        // Retrieve Language_ID from session and add it as a parameter
                        int Language_ID = Convert.ToInt32(UpdateLocaleObj.UserLanguageID);
                        command.Parameters.AddWithValue("@Language_ID", Language_ID);

                        SqlParameter outputIdParam = new SqlParameter("@NewProductTypeLocale_ID", SqlDbType.Int)
                        {
                            Direction = ParameterDirection.Output
                        };
                        command.Parameters.Add(outputIdParam);

                        command.ExecuteNonQuery();
                        ProductTypeLocale_ID = (int)outputIdParam.Value;
                    }
                }

                // Insert GPS Details
                //gbl.InsertGPSDetails(
                //    Convert.ToInt32(UpdateLocaleObj.Company_ID),
                //    Convert.ToInt32(UpdateLocaleObj.Branch),
                //    Convert.ToInt32(UpdateLocaleObj.User_ID),
                //    Convert.ToInt32(Common.GetObjectID("CoreProductTypeMaster",constring)),
                //    PTLRow.ProductType_ID, 0, 0, "Update", false, Convert.ToInt32(UpdateLocaleObj.MenuID)
                //);

                result = new
                {
                    ProductTypeLocale_ID = ProductTypeLocale_ID
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(result, JsonRequestBehavior.AllowGet);
            return new JsonResult(result);
        }


        #endregion

        #region ::: Delete /Mithun:::
        /// <summary>
        /// To Delete Product type
        /// </summary>
        public static IActionResult Delete(DeleteProductTypeList DeleteObj, string constring, int LogException)
        {
            string Msg = string.Empty;
            SqlConnection connection = null;
            SqlTransaction transaction = null;
            try
            {
                // Initialize the SQL connection
                connection = new SqlConnection(constring);
                connection.Open();
                transaction = connection.BeginTransaction();

                // Parse the JSON object
                JObject jObj = JObject.Parse(DeleteObj.key);
                int Count = jObj["rows"].Count();

                int ID = 0;

                for (int i = 0; i < Count; i++)
                {
                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jTR.Read();
                    ID = Convert.ToInt32(jTR.Value);

                    // Check if the product type exists
                    SqlCommand selectCommand = new SqlCommand("SELECT * FROM GNM_ProductType WHERE ProductType_ID = @ID", connection, transaction);
                    selectCommand.Parameters.AddWithValue("@ID", ID);
                    SqlDataReader reader = selectCommand.ExecuteReader();

                    if (reader.HasRows)
                    {
                        reader.Read();
                        string productTypeName = reader["ProductType_Name"].ToString();
                        reader.Close();

                        // Check for references in other tables
                        SqlCommand referenceCheckCommand = new SqlCommand("SELECT COUNT(*) FROM SRT_WarrantyClaimHeader WHERE ProductType_ID = @ID", connection, transaction);
                        referenceCheckCommand.Parameters.AddWithValue("@ID", ID);
                        int referenceCount = (int)referenceCheckCommand.ExecuteScalar();

                        if (referenceCount > 0)
                        {
                            Msg += CommonFunctionalities.GetGlobalResourceObject(DeleteObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                        }
                        else
                        {
                            // Delete the product type locale if exists
                            SqlCommand deleteLocaleCommand = new SqlCommand("DELETE FROM GNM_ProductTypeLocale WHERE ProductType_ID = @ID", connection, transaction);
                            deleteLocaleCommand.Parameters.AddWithValue("@ID", ID);
                            deleteLocaleCommand.ExecuteNonQuery();

                            // Delete the product type
                            SqlCommand deleteCommand = new SqlCommand("DELETE FROM GNM_ProductType WHERE ProductType_ID = @ID", connection, transaction);
                            deleteCommand.Parameters.AddWithValue("@ID", ID);
                            deleteCommand.ExecuteNonQuery();

                            // Insert GPS details
                            //gbl.InsertGPSDetails(
                            //    Convert.ToInt32(DeleteObj.Company_ID),
                            //    Convert.ToInt32(DeleteObj.Branch),
                            //    Convert.ToInt32(DeleteObj.User_ID),
                            //    Convert.ToInt32(Common.GetObjectID("CoreProductTypeMaster",constring)),
                            //    ID,
                            //    0,
                            //    0,
                            //    "Delete" + productTypeName,
                            //    false,
                            //    Convert.ToInt32(DeleteObj.MenuID),
                            //    Convert.ToDateTime(DeleteObj.LoggedINDateTime)
                            //);

                            Msg += CommonFunctionalities.GetGlobalResourceObject(DeleteObj.UserCulture.ToString(), "deletedsuccessfully").ToString();
                        }
                    }
                    else
                    {
                        reader.Close();
                    }
                }

                // Commit the transaction
                transaction.Commit();
            }
            catch (Exception ex)
            {
                // Rollback transaction in case of an error
                if (transaction != null)
                {
                    transaction.Rollback();
                }

                if (ex.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    Msg += CommonFunctionalities.GetGlobalResourceObject(DeleteObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    Msg += ex.Message; // Or log the error message for further analysis
                }
            }
            finally
            {
                // Close the connection
                if (connection != null)
                {
                    connection.Close();
                }
            }
            //return Msg;
            return new JsonResult(Msg);
        }



        #endregion

        #region ::: CheckProductTypeName /Mihtun:::
        /// <summary>
        /// To Check if Product type name Already exists for the Brand
        /// </summary>
        public static IActionResult CheckProductTypeName(CheckProductTypeNameList CheckProductTypeNameObj, string constring, int LogException)
        {
            int count = 0;
            try
            {
                string ProductTypeName = Uri.UnescapeDataString(CheckProductTypeNameObj.ProductTypeName);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    using (SqlCommand cmd = new SqlCommand("UP_AM_ERP_CheckDuplicateProductType", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@BrandID", CheckProductTypeNameObj.BrandID);
                        cmd.Parameters.AddWithValue("@ProductTypeName", ProductTypeName);
                        cmd.Parameters.AddWithValue("@ProductTypeID", CheckProductTypeNameObj.ProductTypeID);

                        conn.Open();
                        count = (int)cmd.ExecuteScalar();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return count;
            return new JsonResult(count);
        }

        #endregion

        #region ::: CheckProductTypeLocaleName /Mithun:::
        /// <summary>
        /// To Check if ProductType Locale Name Already exists for the Brand
        /// </summary>

        public static IActionResult CheckProductTypeLocaleName(CheckProductTypeLocaleNameList CheckProductTypeLocaleNameObj, string constring, int LogException)
        {
            int Count = 0;
            try
            {
                // Decrypt the ProductTypeName
                string ProductTypeName = Common.DecryptString(CheckProductTypeLocaleNameObj.ProductTypeName);

                // Get the Language_ID from the session
                int Language_ID = Convert.ToInt32(CheckProductTypeLocaleNameObj.UserLanguageID);


                // Create and open the SQL connection
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Create the SQL command
                    using (SqlCommand command = new SqlCommand("Up_Chk_Am_Erp_CheckProductTypeLocale", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        command.Parameters.AddWithValue("@BrandID", CheckProductTypeLocaleNameObj.BrandID);
                        command.Parameters.AddWithValue("@ProductTypeName", ProductTypeName);
                        command.Parameters.AddWithValue("@ProductTypeLocaleID", CheckProductTypeLocaleNameObj.ProductTypeLocaleID);
                        command.Parameters.AddWithValue("@Language_ID", Language_ID);

                        // Execute the command and get the result
                        Count = (int)command.ExecuteScalar();
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the exception if logging is enabled
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Count;
            return new JsonResult(Count);
        }

        #endregion

        #region::: Export :::

        public static async Task<object> Export(SelectProductTypeList1 ExportObj, string constring, int LogException, string filters, string Query, string sidx, string sord)
        {
            try
            {
                DataTable DtData = new DataTable();
                IQueryable<ProductTypeMaster> IQProductType;

                int BrandID = Convert.ToInt32(ExportObj.BrandID);
                int LanguageID = Convert.ToInt32(ExportObj.LanguageID);

                // Fetch data as IQueryable
                IQProductType = getLandingGridData(ExportObj, constring, LogException, ExportObj.BrandID, ExportObj.LanguageID, ExportObj.GeneralCulture, ExportObj.UserCulture, ExportObj.GeneralLanguageID);

                // Apply filtering
                if (filters.ToString() != "null")
                {
                    Filters filtersobj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    if (filtersobj.rules.Count() > 0)
                    {
                        IQProductType = IQProductType.FilterSearch<ProductTypeMaster>(filtersobj);
                    }
                }

                // Apply advanced filtering
                if (Query.ToString() != "null")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Query).ToObject<AdvanceFilter>();
                    if (advnfilter.rules.Count() > 0)
                    {
                        IQProductType = IQProductType.AdvanceSearch<ProductTypeMaster>(advnfilter);
                    }
                }

                // Apply sorting
                IQProductType = IQProductType.OrderByField<ProductTypeMaster>(sidx.ToString(), sord.ToString());

                // Prepare data for export
                DtData.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "Producttype").ToString());
                DtData.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "Active").ToString());

                // Convert IQueryable to List to populate DataTable
                var ProductTypeList = IQProductType.ToList();

                foreach (var item in ProductTypeList)
                {
                    DtData.Rows.Add(item.ProductType_Name, item.ProductType_IsActive);
                }

                // Prepare criteria for the report
                if (DtData.Rows.Count > 0)
                {
                    DataTable DtCriteria = new DataTable();
                    DtCriteria.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "Brand").ToString());
                    DtCriteria.Rows.Add(Common.DecryptString(ExportObj.BrandName));

                    // Prepare alignment for the report
                    DataTable DtAlignment = new DataTable();
                    DtAlignment.Columns.Add("Producttype");
                    DtAlignment.Columns.Add("Active");
                    DtAlignment.Rows.Add(0, 1);

                    ReportExportList reportExportList = new ReportExportList
                    {
                        Company_ID = ExportObj.Company_ID, // Assuming this is available in ExportObj
                        Branch = ExportObj.Branch.ToString(),
                        GeneralLanguageID = ExportObj.GeneralLanguageID,
                        UserLanguageID = ExportObj.LanguageID,
                        Options = DtCriteria,
                        dt = DtData,
                        Alignment = DtAlignment,
                        FileName = "Asset Type", // Set a default or dynamic filename
                        Header = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "Asset Type").ToString(), // Set a default or dynamic header
                        exprtType = ExportObj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                        UserCulture = ExportObj.UserCulture
                    };


                    var Result = await ReportExport.Export(reportExportList, constring, LogException);
                    return Result.Value;


                    // Export the report
                    //ReportExport.Export(exprtType, DtData, DtCriteria, DtAlignment,
                    //    HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "ProducttypeExport").ToString(),
                    //    HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "Producttype").ToString());
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return null;
        }

        #endregion





        public class CheckProductTypeLocaleNameList
        {
            public int UserLanguageID { get; set; }
            public int BrandID { get; set; }
            public int ProductTypeLocaleID { get; set; }
            public string ProductTypeName { get; set; }
        }

        public class CheckProductTypeNameList
        {
            public int BrandID { get; set; }
            public int ProductTypeID { get; set; }
            public string ProductTypeName { get; set; }
        }
        public class DeleteProductTypeList
        {
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public string key { get; set; }
            public string UserCulture { get; set; }
            public DateTime LoggedINDateTime { get; set; }

        }
        public class UpdateLocaleProductTypeList
        {
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public string data { get; set; }

        }
        public class SaveProductTypeList
        {
            public int GeneralLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public string data { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }
        public class SelectParticularProductTypeList
        {
            public int UserLanguageID { get; set; }
            public int ProductTypeID { get; set; }
        }
        public class SelectReferenceMasterProductTypeList
        {
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
        }

        public class SelectProductTypeList1
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public int BrandID { get; set; }
            public int LanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int exprtType { get; set; }
            public string UserCulture { get; set; }
            public string GeneralCulture { get; set; }
            public string BrandName { get; set; }
            public string sord { get; set; }
            public string sidx { get; set; }
            public string filters { get; set; }
            public string Query { get; set; }
        }
        public class getLandingGridDataList
        {
            public int GeneralLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public string key { get; set; }
            public string Lang { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }
        public partial class GNM_ProductTypeLocale
        {
            public int ProductTypeLocale_ID { get; set; }
            public int ProductType_ID { get; set; }
            public string ProductType_Name { get; set; }
            public int Language_ID { get; set; }

            public virtual GNM_ProductType GNM_ProductType { get; set; }
        }
        public partial class GNM_ProductType
        {
            public GNM_ProductType()
            {
                this.GNM_ProductTypeLocale = new HashSet<GNM_ProductTypeLocale>();
            }

            public int ProductType_ID { get; set; }
            public int Brand_ID { get; set; }
            public string ProductType_Name { get; set; }
            public bool ProductType_IsActive { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }

            public virtual ICollection<GNM_ProductTypeLocale> GNM_ProductTypeLocale { get; set; }
        }
        public class ProductTypeMaster
        {
            public int ProductType_ID
            {
                get;
                set;
            }

            public string ProductType_Name
            {
                get;
                set;
            }

            public string ProductType_IsActive
            {
                get;
                set;
            }
        }
    }
}
