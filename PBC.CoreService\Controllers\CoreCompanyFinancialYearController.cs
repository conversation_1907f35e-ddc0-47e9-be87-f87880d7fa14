using Microsoft.AspNetCore.Mvc;
using PBC.CoreService.Services;
using PBC.CoreService.Utilities.DTOs;

namespace PBC.CoreService.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CoreCompanyFinancialYearController : ControllerBase
    {
        private readonly ICoreCompanyFinancialYearServices _coreCompanyFinancialYearServices;
        private readonly ILogger<CoreCompanyFinancialYearController> _logger;
        private readonly IConfiguration _configuration;

        public CoreCompanyFinancialYearController(
            ICoreCompanyFinancialYearServices coreCompanyFinancialYearServices,
            ILogger<CoreCompanyFinancialYearController> logger,
            IConfiguration configuration)
        {
            _coreCompanyFinancialYearServices = coreCompanyFinancialYearServices;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// Select all financial years of the company
        /// </summary>
        /// <param name="request">SelectCompanyFinancialYearList object</param>
        /// <param name="sidx">Sort index</param>
        /// <param name="sord">Sort order</param>
        /// <param name="page">Page number</param>
        /// <param name="rows">Rows per page</param>
        /// <returns>JsonResult with financial year data</returns>
        /// <response code="200">Returns the financial year data</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/corecompanyfinancialyear/select?sidx=Company_FinancialYear&amp;sord=asc&amp;page=1&amp;rows=10
        ///     {
        ///        "company_ID": 1,
        ///        "userLanguageID": 1,
        ///        "generalLanguageID": 1
        ///     }
        /// </remarks>
        [HttpPost("select")]
        [ProducesResponseType(typeof(JsonResult), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Select(
            [FromBody] SelectCompanyFinancialYearList request,
            [FromQuery] string sidx = "",
            [FromQuery] string sord = "asc",
            [FromQuery] int page = 1,
            [FromQuery] int rows = 10)
        {
            try
            {
                _logger.LogInformation("POST /api/corecompanyfinancialyear/select");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Get connection string from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                var result = await _coreCompanyFinancialYearServices.Select(request, connString, logException, sidx, sord, page, rows);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error selecting company financial years");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while selecting financial years");
            }
        }

        /// <summary>
        /// Get all company names
        /// </summary>
        /// <param name="request">SelectCompanyList object</param>
        /// <returns>JsonResult with company data</returns>
        /// <response code="200">Returns the company data</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/corecompanyfinancialyear/select-company
        ///     {
        ///        "company_ID": 1,
        ///        "userLanguageID": 1,
        ///        "generalLanguageID": 1
        ///     }
        /// </remarks>
        [HttpPost("select-company")]
        [ProducesResponseType(typeof(JsonResult), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SelectCompany([FromBody] SelectCompanyList request)
        {
            try
            {
                _logger.LogInformation("POST /api/corecompanyfinancialyear/select-company");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Get connection string from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                var result = await _coreCompanyFinancialYearServices.SelectCompany(request, connString, logException);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error selecting companies");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while selecting companies");
            }
        }

        /// <summary>
        /// Save financial year data
        /// </summary>
        /// <param name="request">SaveCoreCompanyFinancialList object</param>
        /// <returns>JsonResult with success status</returns>
        /// <response code="200">Financial year data saved successfully</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/corecompanyfinancialyear/save
        ///     {
        ///        "data": "{\"rows\":[{\"Company_FinancialYear_ID\":0,\"Company_ID\":1,\"Company_FinancialYear\":2024,\"Company_FinancialYear_FromDate\":\"2024-04-01T00:00:00\",\"Company_FinancialYear_ToDate\":\"2025-03-31T23:59:59\"}]}",
        ///        "company_ID": 1,
        ///        "user_ID": 123,
        ///        "menuID": 10,
        ///        "branch": 1,
        ///        "loggedINDateTime": "2025-06-17T09:20:46.694Z"
        ///     }
        /// </remarks>
        [HttpPost("save")]
        [ProducesResponseType(typeof(JsonResult), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Save([FromBody] SaveCoreCompanyFinancialList request)
        {
            try
            {
                _logger.LogInformation("POST /api/corecompanyfinancialyear/save");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Get connection string from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                var result = await _coreCompanyFinancialYearServices.Save(request, connString, logException);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving financial year data");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while saving financial year data");
            }
        }

        /// <summary>
        /// Delete financial year records
        /// </summary>
        /// <param name="request">DeleteCoreCompanyFinancialList object</param>
        /// <returns>JsonResult with deletion status message</returns>
        /// <response code="200">Returns deletion status message</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/corecompanyfinancialyear/delete
        ///     {
        ///        "key": "{\"rows\":[{\"id\":123}]}",
        ///        "lang": "en",
        ///        "userCulture": "Resource_en",
        ///        "company_ID": 1,
        ///        "user_ID": 123,
        ///        "menuID": 10,
        ///        "branch": 1,
        ///        "loggedINDateTime": "2025-06-17T09:20:46.694Z"
        ///     }
        /// </remarks>
        [HttpPost("delete")]
        [ProducesResponseType(typeof(JsonResult), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Delete([FromBody] DeleteCoreCompanyFinancialList request)
        {
            try
            {
                _logger.LogInformation("POST /api/corecompanyfinancialyear/delete");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Get connection string from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                var result = await _coreCompanyFinancialYearServices.Delete(request, connString, logException);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting financial year records");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while deleting financial year records");
            }
        }

        /// <summary>
        /// Check if financial year already exists for the company
        /// </summary>
        /// <param name="request">CheckFinancialYearList object</param>
        /// <returns>JsonResult with status (1 if exists, 0 if not)</returns>
        /// <response code="200">Returns check result (1 if exists, 0 if not)</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/corecompanyfinancialyear/check-financial-year
        ///     {
        ///        "company_ID": 1,
        ///        "financialYear": 2024,
        ///        "primaryKey": 0
        ///     }
        /// </remarks>
        [HttpPost("check-financial-year")]
        [ProducesResponseType(typeof(JsonResult), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> CheckFinancialYear([FromBody] CheckFinancialYearList request)
        {
            try
            {
                _logger.LogInformation("POST /api/corecompanyfinancialyear/check-financial-year");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Get connection string from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                var result = await _coreCompanyFinancialYearServices.CheckFinancialYear(request, connString, logException);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking financial year");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while checking financial year");
            }
        }

        /// <summary>
        /// Get all saved financial years for the company
        /// </summary>
        /// <param name="request">GetAllSavedFinancialYearsList object</param>
        /// <returns>JsonResult with financial years array</returns>
        /// <response code="200">Returns array of saved financial years</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/corecompanyfinancialyear/get-all-saved-financial-years
        ///     {
        ///        "company_ID": 1
        ///     }
        /// </remarks>
        [HttpPost("get-all-saved-financial-years")]
        [ProducesResponseType(typeof(JsonResult), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetAllSavedFinancialYears([FromBody] GetAllSavedFinancialYearsList request)
        {
            try
            {
                _logger.LogInformation("POST /api/corecompanyfinancialyear/get-all-saved-financial-years");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Get connection string from configuration
                string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;
                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                var result = await _coreCompanyFinancialYearServices.GetAllSavedFinancialYears(request, connString, logException);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all saved financial years");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while getting saved financial years");
            }
        }
    }
}
