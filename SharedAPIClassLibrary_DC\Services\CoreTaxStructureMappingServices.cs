﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreTaxStructureMappingServices
    {
        #region ::: SelAllTaxMapping :::
        /// <summary>
        /// to select all Tax Mapping
        /// </summary> 
        public static IActionResult SelAllTaxMapping(SelAllTaxMappingList SelAllTaxMappingObj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            try
            {
                int count = 0;
                int total = 0;

                int companyID = SelAllTaxMappingObj.Company_ID;
                var jsonResult = default(dynamic);
                string select = CommonFunctionalities.GetResourceString(SelAllTaxMappingObj.UserCulture.ToString(), "select").ToString();
                string ChildTaxStr = "0:----" + select + "----;";
                string ParentTaxStr = "0:----" + select + "----;";
                List<TaxStructureData> childTaxList = new List<TaxStructureData>();
                List<TaxStructureData> parentTaxList = new List<TaxStructureData>();
                List<TaxMappingData> taxMappingList = new List<TaxMappingData>();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_Select_AM_ERP_SelAllTaxMapping_TaxStructureMapping";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@companyID", companyID);
                            command.Parameters.AddWithValue("@userLanguageID", SelAllTaxMappingObj.UserLanguageID);
                            command.Parameters.AddWithValue("@generalLanguageID", SelAllTaxMappingObj.GeneralLanguageID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                // First result set: MovementTypeDefinition data
                                while (reader.Read())
                                {
                                    childTaxList.Add(new TaxStructureData
                                    {
                                        TaxStructure_ID = reader.GetInt32(0),
                                        TaxStructure_Name = reader.GetString(1)
                                    });
                                }

                                // Move to the second result set - Parent Tax Structures
                                if (reader.NextResult())
                                {
                                    while (reader.Read())
                                    {
                                        parentTaxList.Add(new TaxStructureData
                                        {
                                            TaxStructure_ID = reader.GetInt32(0),
                                            TaxStructure_Name = reader.GetString(1)
                                        });
                                    }
                                }

                                // Move to the third result set - Tax Structure Mapping
                                if (reader.NextResult())
                                {
                                    while (reader.Read())
                                    {
                                        taxMappingList.Add(new TaxMappingData
                                        {
                                            TaxStructureMappingID = reader.GetInt32(0),
                                            ParentTaxStructure = reader.GetString(1),
                                            ChildTaxStructure = reader.GetString(2)
                                        });
                                    }
                                }


                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log exception
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                    }
                    finally
                    {
                        command?.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                if (SelAllTaxMappingObj.UserLanguageID == SelAllTaxMappingObj.GeneralLanguageID)
                {



                    for (int i = 0; i < childTaxList.ToArray().Count(); i++)
                    {
                        ChildTaxStr = ChildTaxStr + childTaxList.ToArray().ElementAt(i).TaxStructure_ID + ":" + childTaxList.ToArray().ElementAt(i).TaxStructure_Name + ";";
                    }
                    ChildTaxStr = ChildTaxStr.TrimEnd(new char[] { ';' });





                    for (int i = 0; i < parentTaxList.ToArray().Count(); i++)
                    {
                        ParentTaxStr = ParentTaxStr + parentTaxList.ToArray().ElementAt(i).TaxStructure_ID + ":" + parentTaxList.ToArray().ElementAt(i).TaxStructure_Name + ";";
                    }
                    ParentTaxStr = ParentTaxStr.TrimEnd(new char[] { ';' });

                    IQueryable<TaxMappingData> iTaxMappingArray = null;

                    var arrTaxMappingList = from a in taxMappingList

                                            select new TaxMappingData()
                                            {
                                                //edit = "<img id='" + a.TaxStructureMapping_ID + "' src='" + AppPath + "/Content/Images/edit.gif' key='" + a.TaxStructureMapping_ID + "' class='editMapping' editmode='false'/>",
                                                edit = "<a title='View' href='#' id='" + a.TaxStructureMappingID + "' key='" + a.TaxStructureMappingID + "' class='editMapping font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                                delete = "<input type='checkbox' key='" + a.TaxStructureMappingID + "' id='chk" + a.TaxStructureMappingID + "' class='chkMappingDelete'/>",
                                                ChildTaxStructure = a.ChildTaxStructure,
                                                ParentTaxStructure = a.ParentTaxStructure,
                                                TaxStructureMappingID = a.TaxStructureMappingID
                                            };
                    iTaxMappingArray = arrTaxMappingList.AsQueryable<TaxMappingData>();

                    if (_search)
                    {
                        Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                        iTaxMappingArray = iTaxMappingArray.FilterSearch<TaxMappingData>(filtersObj);

                    }

                    //Modified by: Amith. Date:13-11-2013 QA- Iteration1 Consistency Issues Changes Begin 
                    iTaxMappingArray = iTaxMappingArray.OrderByField<TaxMappingData>(sidx, sord);
                    //Changes Ends
                    count = iTaxMappingArray.Count();
                    total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                    jsonResult = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        data = iTaxMappingArray.ToList().Paginate(page, rows),
                        ParentTaxStr,
                        ChildTaxStr
                    };
                }
                else
                {



                    for (int i = 0; i < childTaxList.ToArray().Count(); i++)
                    {
                        ChildTaxStr = ChildTaxStr + childTaxList.ToArray().ElementAt(i).TaxStructure_ID + ":" + childTaxList.ToArray().ElementAt(i).TaxStructure_Name + ";";
                    }
                    ChildTaxStr = ChildTaxStr.TrimEnd(new char[] { ';' });





                    for (int i = 0; i < parentTaxList.ToArray().Count(); i++)
                    {
                        ParentTaxStr = ParentTaxStr + parentTaxList.ToArray().ElementAt(i).TaxStructure_ID + ":" + parentTaxList.ToArray().ElementAt(i).TaxStructure_Name + ";";
                    }
                    ParentTaxStr = ParentTaxStr.TrimEnd(new char[] { ';' });

                    IQueryable<TaxMappingData> iTaxMappingArray = null;

                    var arrTaxMappingList = from a in taxMappingList

                                            select new TaxMappingData()
                                            {
                                                //edit = "<img id='" + a.TaxStructureMapping_ID + "' src='" + AppPath + "/Content/Images/edit.gif' key='" + a.TaxStructureMapping_ID + "' class='editMapping'/>",
                                                edit = "<a title='View' href='#' id='" + a.TaxStructureMappingID + "' key='" + a.TaxStructureMappingID + "' class='editMapping font-icon-class' ><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                                delete = "<input type='checkbox' key='" + a.TaxStructureMappingID + "' id='chk" + a.TaxStructureMappingID + "' class='chkMappingDelete'/>",
                                                ChildTaxStructure = a.ChildTaxStructure,
                                                ParentTaxStructure = a.ParentTaxStructure,
                                                TaxStructureMappingID = a.TaxStructureMappingID
                                            };
                    iTaxMappingArray = arrTaxMappingList.AsQueryable<TaxMappingData>();

                    if (_search)
                    {
                        Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                        iTaxMappingArray = iTaxMappingArray.FilterSearch<TaxMappingData>(filtersObj);

                    }
                    iTaxMappingArray = iTaxMappingArray.OrderByField<TaxMappingData>(sidx, sord);
                    //Session["TaxMappingData"] = iTaxMappingArray;
                    count = iTaxMappingArray.Count();
                    total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                    jsonResult = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        data = iTaxMappingArray.ToList().Paginate(page, rows),
                        ParentTaxStr,
                        ChildTaxStr
                    };
                }
                return new JsonResult(jsonResult);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult("Error");
            }
            return new JsonResult("Error");
        }
        #endregion
        #region ::: Save :::
        /// <summary>
        /// to Save
        /// </summary>
        public static void Save(SaveTaxMappingList SaveObj, string connString, int LogException)
        {
            try
            {
                JObject jobj = JObject.Parse(SaveObj.Data);
                int rowCount = jobj["rows"].Count();
                for (int j = 0; j < rowCount; j++)
                {
                    int CompanyID = Convert.ToInt32(SaveObj.Company_ID);
                    GNM_TaxStructureMapping taxMappingRow = jobj["rows"].ElementAt(j).ToObject<GNM_TaxStructureMapping>();
                    taxMappingRow.Company_ID = CompanyID;

                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_Save_AM_ERP_Save_TaxStructureMapping";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@TaxStructureMapping_ID", taxMappingRow.TaxStructureMapping_ID);
                                command.Parameters.AddWithValue("@ChildTaxStructure_ID", taxMappingRow.ChildTaxStructure_ID);
                                command.Parameters.AddWithValue("@ParentTaxStructure_ID", taxMappingRow.ParentTaxStructure_ID);
                                command.Parameters.AddWithValue("@CompanyID", taxMappingRow.Company_ID);


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }


                                command.ExecuteNonQuery();
                            }
                        }
                        catch (Exception ex)
                        {
                            // Log exception
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }
                        }
                        finally
                        {
                            command?.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                    if (taxMappingRow.TaxStructureMapping_ID == 0)
                    {

                        //gbl.InsertGPSDetails(Convert.ToInt32(SaveObj.Company_ID), Convert.ToInt32(SaveObj.Branch), Convert.ToInt32(SaveObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreTaxStructureMapping")), taxMappingRow.TaxStructureMapping_ID, 0, 0, "Insert", false, Convert.ToInt32(SaveObj.MenuID));
                    }
                    else
                    {


                        //gbl.InsertGPSDetails(Convert.ToInt32(SaveObj.Company_ID), Convert.ToInt32(SaveObj.Branch), Convert.ToInt32(SaveObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreTaxStructureMapping")), taxMappingRow.TaxStructureMapping_ID, 0, 0, "Update", false, Convert.ToInt32(SaveObj.MenuID));
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);


            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }

            }
        }
        #endregion
        #region ::: Delete :::
        /// <summary>
        /// to Delete tax mapping
        /// </summary>
        public static IActionResult Delete(DeleteTaxMappingList DelObj, string connString, int LogException)
        {
            string errorMsg = "";
            try
            {
                JTokenReader reader = null;
                JObject jobj = JObject.Parse(DelObj.key);
                int rowCount = jobj["rows"].Count();
                GNM_TaxStructureMapping deleteRow = null;
                int id = 0;
                for (int i = 0; i < rowCount; i++)
                {
                    reader = new JTokenReader(jobj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    reader.Read();
                    id = Convert.ToInt32(reader.Value);
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_Delete_AM_ERP_Delete_TaxStructureMapping";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@TaxStructureMapping_ID", id);


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }


                                command.ExecuteNonQuery();
                            }
                        }
                        catch (Exception ex)
                        {
                            // Log exception
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }
                        }
                        finally
                        {
                            command?.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }

                }

                //gbl.InsertGPSDetails(Convert.ToInt32(DelObj.Company_ID), Convert.ToInt32(DelObj.Branch), Convert.ToInt32(DelObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreTaxStructureMapping")), id, 0, 0, "Delete", false, Convert.ToInt32(DelObj.MenuID));
                errorMsg += CommonFunctionalities.GetResourceString(DelObj.UserCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null)
                {
                    if (ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                    {
                        errorMsg += CommonFunctionalities.GetResourceString(DelObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();//Tr_Resource.Dependencyfoundcannotdeletetherecords;
                    }
                }
                else
                {
                    errorMsg += CommonFunctionalities.GetResourceString(DelObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();//"Dependency found cannot delete the records";
                }
            }
            return new JsonResult(errorMsg);
        }
        #endregion

        public static IActionResult CheckMapping(CheckMappingList CheckMappingObj, string connString, int LogException)
        {
            int CompanyID = Convert.ToInt32(CheckMappingObj.Company_ID);
            using (SqlConnection conn = new SqlConnection(connString))
            {
                string query = "UP_CheckMapping_AM_ERP_CheckMapping_TaxStructureMapping";

                SqlCommand command = null;

                try
                {
                    using (command = new SqlCommand(query, conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@CompanyID", CompanyID);
                        command.Parameters.AddWithValue("@TaxID", CheckMappingObj.TaxID);
                        command.Parameters.AddWithValue("@pkID", CheckMappingObj.pkID);
                        command.Parameters.AddWithValue("@Mode", CheckMappingObj.mode);
                        SqlParameter resultParameter = new SqlParameter("@Result", SqlDbType.Int)
                        {
                            Direction = ParameterDirection.Output
                        };
                        command.Parameters.Add(resultParameter);


                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }


                        command.ExecuteNonQuery();
                        int result = (int)resultParameter.Value;
                        return new JsonResult(result.ToString());
                    }
                }
                catch (Exception ex)
                {
                    // Log exception
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }
                    return new JsonResult(-1);
                }
                finally
                {
                    command?.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }
            }

        }
    }
    #region TaxStructureMapping Classes
    public class TaxMappingData
    {
        public string edit { get; set; }
        public string delete { get; set; }
        public int TaxStructureMappingID { get; set; }
        public string ParentTaxStructure { get; set; }
        public string ChildTaxStructure { get; set; }
    }
    public class SelAllTaxMappingList
    {
        public int Company_ID { get; set; }
        public string UserCulture { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
    }
    public class TaxStructureData
    {
        public int TaxStructure_ID { get; set; }
        public string TaxStructure_Name { get; set; }
        public string ChildTaxStructure { get; set; }
        public string ParentTaxStructure { get; set; }
        public int TaxStructureMappingID { get; set; }
    }
    public partial class GNM_TaxStructureMapping
    {
        public int TaxStructureMapping_ID { get; set; }
        public int ParentTaxStructure_ID { get; set; }
        public int ChildTaxStructure_ID { get; set; }
        public int Company_ID { get; set; }

        public virtual GNM_TaxStructure GNM_TaxStructure { get; set; }
        public virtual GNM_TaxStructure GNM_TaxStructure1 { get; set; }
    }
    public partial class GNM_TaxStructure
    {
        public GNM_TaxStructure()
        {
            this.GNM_TaxStructureDtl = new HashSet<GNM_TaxStructureDtl>();
            this.GNM_TaxStructureLocale = new HashSet<GNM_TaxStructureLocale>();
            this.GNM_TaxSummary = new HashSet<GNM_TaxSummary>();
            this.GNM_TaxStructureMapping = new HashSet<GNM_TaxStructureMapping>();
            this.GNM_TaxStructureMapping1 = new HashSet<GNM_TaxStructureMapping>();
            this.GNM_TaxStrucutreExciseDutyDtl = new HashSet<GNM_TaxStrucutreExciseDutyDtl>();
        }

        public int TaxStructure_ID { get; set; }
        public int Company_ID { get; set; }
        public string TaxStructure_Name { get; set; }
        public bool TaxStructure_IsActive { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public Nullable<System.DateTime> FromDate { get; set; }
        public Nullable<System.DateTime> ToDate { get; set; }
        public Nullable<bool> IsServiceTax { get; set; }

        public virtual ICollection<GNM_TaxStructureDtl> GNM_TaxStructureDtl { get; set; }
        public virtual ICollection<GNM_TaxStructureLocale> GNM_TaxStructureLocale { get; set; }
        public virtual ICollection<GNM_TaxSummary> GNM_TaxSummary { get; set; }
        public virtual ICollection<GNM_TaxStructureMapping> GNM_TaxStructureMapping { get; set; }
        public virtual ICollection<GNM_TaxStructureMapping> GNM_TaxStructureMapping1 { get; set; }
        public virtual ICollection<GNM_TaxStrucutreExciseDutyDtl> GNM_TaxStrucutreExciseDutyDtl { get; set; }
    }
    public partial class GNM_TaxSummary
    {
        public int TaxSummary_ID { get; set; }
        public int ObjectID { get; set; }
        public int Transaction_ID { get; set; }
        public byte TaxSummary_Component { get; set; }
        public decimal TotalAmount { get; set; }
        public Nullable<decimal> DiscountPercentage { get; set; }
        public Nullable<decimal> DiscountAmount { get; set; }
        public Nullable<decimal> DiscountedAmount { get; set; }
        public Nullable<int> TaxStructure_ID { get; set; }
        public Nullable<decimal> TaxStrutureAmount { get; set; }
        public string TaxableOtherCharges1 { get; set; }
        public Nullable<decimal> TaxableOtherCharges1Percentage { get; set; }
        public Nullable<decimal> TaxableOtherCharges1Amount { get; set; }
        public string TaxableOtherCharges2 { get; set; }
        public Nullable<decimal> TaxableOtherCharges2Percentage { get; set; }
        public Nullable<decimal> TaxableOtherCharges2Amount { get; set; }
        public string NonTaxableOtherCharges1 { get; set; }
        public Nullable<decimal> NonTaxableothercharges1Percentage { get; set; }
        public Nullable<decimal> NonTaxableOtherCharges1Amount { get; set; }
        public string NonTaxableOtherCharges2 { get; set; }
        public Nullable<decimal> NonTaxableothercharges2Percentage { get; set; }
        public Nullable<decimal> NonTaxableOtherCharges2Amount { get; set; }
        public Nullable<decimal> TotalTaxableAmount { get; set; }

        public virtual GNM_TaxStructure GNM_TaxStructure { get; set; }
    }
    public partial class GNM_TaxStrucutreExciseDutyDtl
    {
        public int TaxStructureExciseDutyDtl_ID { get; set; }
        public int TaxStructure_ID { get; set; }
        public int ExciseDuty_ID { get; set; }
        public Nullable<bool> IsDefault { get; set; }

        public virtual GNM_TaxStructure GNM_TaxStructure { get; set; }
    }
    public partial class GNM_TaxStructureDtl
    {
        public int TaxStructureDtl_ID { get; set; }
        public int TaxStructure_ID { get; set; }
        public int TaxType_Seq { get; set; }
        public int TaxType_ID { get; set; }
        public decimal TaxType_TaxPerc { get; set; }
        public bool TaxType_IncludeBaseAmt { get; set; }
        public string TaxStructureDtl_Formula { get; set; }
        public string TaxStructureDtl_FormulaValue { get; set; }
        public string AccountCode { get; set; }
        public Nullable<bool> IsExcise { get; set; }

        public virtual GNM_TaxStructure GNM_TaxStructure { get; set; }
    }
    public partial class GNM_TaxStructureLocale
    {
        public int TaxStructureLocale_ID { get; set; }
        public int TaxStructure_ID { get; set; }
        public string TaxStructure_Name { get; set; }
        public int Language_ID { get; set; }

        public virtual GNM_TaxStructure GNM_TaxStructure { get; set; }
    }
    public class SaveTaxMappingList
    {
        public string Data { get; set; }
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int User_ID { get; set; }
        public int MenuID { get; set; }
    }
    public class DeleteTaxMappingList
    {
        public string key { get; set; }
        public int Company_ID { set; get; }
        public int Branch { set; get; }
        public int User_ID { set; get; }
        public int MenuID { set; get; }
        public string UserCulture { get; set; }

    }
    public class CheckMappingList
    {
        public int Company_ID { get; set; }
        public int TaxID { get; set; }
        public int pkID { get; set; }
        public int mode { get; set; }
    }
    #endregion
}
