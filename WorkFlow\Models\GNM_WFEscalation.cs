//------------------------------------------------------------------------------
// <auto-generated>
//    This code was generated from a template.
//
//    Manual changes to this file may cause unexpected behavior in your application.
//    Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace WorkFlow.Models
{
    using System;
    using System.Collections.Generic;
    
    public partial class GNM_WFEscalation
    {
        public GNM_WFEscalation()
        {
            this.GNM_WFEscalationHistory = new HashSet<GNM_WFEscalationHistory>();
        }
    
        public int Escalation_ID { get; set; }
        public int Company_ID { get; set; }
        public int WorkFlow_ID { get; set; }
        public int WFRole_ID { get; set; }
        public decimal Escalation_Hours { get; set; }
        public int Escalate_To_EmployeeID { get; set; }
        public bool Escalate_IsEmail { get; set; }
        public bool Escalate_IsMobile { get; set; }
        public bool CCToAssignee { get; set; }
    
        public virtual ICollection<GNM_WFEscalationHistory> GNM_WFEscalationHistory { get; set; }
    }
}
