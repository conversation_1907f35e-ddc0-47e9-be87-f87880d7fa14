using Microsoft.AspNetCore.Mvc;
using PBC.UtilityService.Services;
using PBC.UtilityService.Utilities.DTOs;
using System.ComponentModel.DataAnnotations;

namespace PBC.UtilityService.Controllers
{
    /// <summary>
    /// Controller for Help Desk Common operations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class HelpDeskCommonController : ControllerBase
    {
        private readonly IHelpDeskCommonService _helpDeskCommonService;
        private readonly ILogger<HelpDeskCommonController> _logger;

        public HelpDeskCommonController(
            IHelpDeskCommonService helpDeskCommonService,
            ILogger<HelpDeskCommonController> logger)
        {
            _helpDeskCommonService = helpDeskCommonService;
            _logger = logger;
        }

        /// <summary>
        /// Gets the end step status ID for a workflow
        /// </summary>
        /// <param name="request">End step status ID request</param>
        /// <returns>End step status ID</returns>
        /// <response code="200">Returns the end step status ID</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("end-step-status-id")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> GetEndStepStatusID([FromBody] GetEndStepStatusIDRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/helpdeskcommon/end-step-status-id - Getting end step status ID for workflow: {WorkflowId}", request.WorkflowID);
                
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _helpDeskCommonService.GetEndStepStatusIDAsync(request.WorkflowID, request.ConnectionString, request.LogException);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting end step status ID for workflow: {WorkflowId}", request.WorkflowID);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while retrieving end step status ID");
            }
        }

        /// <summary>
        /// Gets the end step status name for a workflow
        /// </summary>
        /// <param name="request">End step status name request</param>
        /// <returns>End step status name</returns>
        /// <response code="200">Returns the end step status name</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("end-step-status-name")]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<string>> GetEndStepStatusName([FromBody] GetEndStepStatusNameRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/helpdeskcommon/end-step-status-name - Getting end step status name for workflow: {WorkflowId}", request.WorkflowID);
                
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _helpDeskCommonService.GetEndStepStatusNameAsync(request.WorkflowID, request.ConnectionString, request.LogException);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting end step status name for workflow: {WorkflowId}", request.WorkflowID);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while retrieving end step status name");
            }
        }
    }
}
