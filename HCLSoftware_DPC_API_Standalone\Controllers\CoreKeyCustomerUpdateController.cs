﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreKeyCustomerUpdateServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreKeyCustomerUpdateController : ApiController
    {

        #region ::: SelectPartyMaster /Mithun :::
        /// <summary>
        /// To SelectPartyMaster
        /// </summary>
        [Route("api/CoreKeyCustomerUpdate/SelectPartyMaster")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartyMaster([FromBody] SelectPartyMasterList SelectPartyMasterObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            //bool search = (HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            //string _advnce = (HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];

            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreKeyCustomerUpdateServices.SelectPartyMaster(SelectPartyMasterObj, Conn, LogException, sidx, sord, page, rows, _search, advnce, filters, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region::: UpdateKeyCustomer /Mithun :::
        /// <summary>
        /// To UpdateKeyCustomer
        /// </summary>
        [Route("api/CoreKeyCustomerUpdate/UpdateKeyCustomer")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateKeyCustomer([FromBody] UpdateKeyCustomerList UpdateKeyCustomerObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreKeyCustomerUpdateServices.UpdateKeyCustomer(UpdateKeyCustomerObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



    }
}
