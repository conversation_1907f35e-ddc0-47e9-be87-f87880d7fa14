﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.HelpDeskTop10ModelsServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class HelpDeskTop10ModelsController : ApiController
    {


        #region ::: SelectTopModel Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To Select Top Model
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDeskTop10Models/SelectTopModel")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectTopModel([FromBody] HelpDeskTop10ModelsSelectTopModelList HelpDeskTop10ModelsSelectTopModelobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskTop10ModelsServices.SelectTopModel(HelpDeskTop10ModelsSelectTopModelobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion



        #region ::: SelectBrandAndBranch Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To Select Brand And Branch
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDeskTop10Models/SelectBrandAndBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectBrandAndBranch([FromBody] HelpDeskTop10ModelsSelectBrandAndBranchList HelpDeskTop10ModelsSelectBrandAndBranchobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTop10ModelsServices.SelectBrandAndBranch(HelpDeskTop10ModelsSelectBrandAndBranchobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Export Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To Export Top Model
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDeskTop10Models/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] HelpDeskTop10ModelsSelectTopModelList HelpDeskTop10ModelsSelectTopModelobj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HelpDeskTop10ModelsSelectTopModelobj.sidx;
            string sord = HelpDeskTop10ModelsSelectTopModelobj.sord;
            string filter = HelpDeskTop10ModelsSelectTopModelobj.filter;
            string advnceFilter = HelpDeskTop10ModelsSelectTopModelobj.advanceFilter;

            try
            {


                Object Response = await HelpDeskTop10ModelsServices.Export(HelpDeskTop10ModelsSelectTopModelobj, connstring, LogException, filter, advnceFilter, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion




    }
}