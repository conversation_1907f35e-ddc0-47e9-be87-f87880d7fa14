﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CorePrefixSuffixServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CorePrefixSuffixController : ApiController
    {

        #region ::: SelectPrefixSuffix Uday Kumar J B 13-08-2024 :::
        /// <summary>
        /// SelectPrefixSuffix
        /// </summary>   
        ///
        [Route("api/CorePrefixSuffix/SelectPrefixSuffix")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPrefixSuffix([FromBody] SelectPrefixSuffixList SelectPrefixSuffixobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"] ?? "Start_Number";
            int rows = string.IsNullOrEmpty(HttpContext.Current.Request.Params["rows"]) ? 10 : Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = string.IsNullOrEmpty(HttpContext.Current.Request.Params["page"]) ? 1 : Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"] ?? "asc";
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"] ?? "false");
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"] ?? "0");
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePrefixSuffixServices.SelectPrefixSuffix(connstring, SelectPrefixSuffixobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectPrefixSuffixByBranch Uday Kumar J B 13-08-2024:::
        /// <summary>
        /// SelectPrefixSuffix
        /// </summary>   
        /// 
        [Route("api/CorePrefixSuffix/SelectPrefixSuffixByBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPrefixSuffixByBranch([FromBody] SelectPrefixSuffixByBranchList SelectPrefixSuffixByBranchobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"] ?? "Start_Number";
            int rows = string.IsNullOrEmpty(HttpContext.Current.Request.Params["rows"]) ? 10 : Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = string.IsNullOrEmpty(HttpContext.Current.Request.Params["page"]) ? 1 : Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"] ?? "asc";
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"] ?? "false");
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"] ?? "0");
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePrefixSuffixServices.SelectPrefixSuffixByBranch(connstring, SelectPrefixSuffixByBranchobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: InsertPrefixSuffix Uday Kumar J B 13-08-2024:::
        /// <summary>
        /// InsertPrefixSuffix
        /// </summary> 
        /// 
        [Route("api/CorePrefixSuffix/InsertPrefixSuffix")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertPrefixSuffix([FromBody] InsertPrefixSuffixList InsertPrefixSuffixobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePrefixSuffixServices.InsertPrefixSuffix(connString, InsertPrefixSuffixobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeletePreficSuffix Uday Kumar J B 13-08-2024:::
        /// <summary>
        /// to Delete the Parts
        /// </summary>
        /// 
        [Route("api/CorePrefixSuffix/DeletePreficSuffix")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeletePreficSuffix([FromBody] DeletePrefixSuffixList DeletePrefixSuffixobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePrefixSuffixServices.DeletePreficSuffix(connString, DeletePrefixSuffixobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: LoadObjectDropdown Uday Kumar J B 13-08-2024:::
        /// <summary>
        /// to Load Object Dropdowns
        /// </summary>
        /// 
        [Route("api/CorePrefixSuffix/LoadObjectDropdown")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadObjectDropdown([FromBody] LoadObjectDropdownList LoadObjectDropdownobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePrefixSuffixServices.LoadObjectDropdown(connString, LoadObjectDropdownobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: ValidateFinYear Uday Kumar J B 13-08-2024:::
        /// <summary>
        /// to log Exception to text file
        /// </summary>    
        /// 
        [Route("api/CorePrefixSuffix/ValidateFinYear")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ValidateFinYear([FromBody] ValidateFinYearList ValidateFinYearobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePrefixSuffixServices.ValidateFinYear(connString, ValidateFinYearobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectDates Uday Kumar J B 13-08-2024:::
        /// <summary>
        /// to select the Dates for Comapny financial Year
        /// </summary>  
        /// 
        [Route("api/CorePrefixSuffix/SelectDates")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectDates([FromBody] SelectDatesList SelectDatesobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CorePrefixSuffixServices.SelectDates(connString, SelectDatesobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion

    }
}