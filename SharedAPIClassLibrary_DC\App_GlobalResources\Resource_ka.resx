﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="A" xml:space="preserve">
    <value>ಎ</value>
  </data>
  <data name="Abandon" xml:space="preserve">
    <value>ಪರಿತ್ಯಜಿಸು</value>
  </data>
  <data name="AbandonDelayReason" xml:space="preserve">
    <value>ಪರಿತ್ಯಜಿಸು</value>
  </data>
  <data name="Abandoned" xml:space="preserve">
    <value>ಪರಿತ್ಯಕ್ತ</value>
  </data>
  <data name="ABCAnalysisReport" xml:space="preserve">
    <value>ಎಬಿಸಿ ವಿಶ್ಲೇಷಣೆಯ ವರದಿ</value>
  </data>
  <data name="Above90Hours" xml:space="preserve">
    <value>90 ಗಂಟೆಗಳ ಮೇಲೆ</value>
  </data>
  <data name="AboveEighteenMonths" xml:space="preserve">
    <value>18 ತಿಂಗಳ ಮೇಲೆ</value>
  </data>
  <data name="AboveTwentyFourMonthQty" xml:space="preserve">
    <value>24 ತಿಂಗಳ ಪ್ರಮಾಣ ಮೇಲೆ</value>
  </data>
  <data name="AboveTwentyFourMonthValue" xml:space="preserve">
    <value>24 ತಿಂಗಳ ಮೌಲ್ಯವನ್ನು ಮೇಲೆ</value>
  </data>
  <data name="Accept" xml:space="preserve">
    <value>ಸ್ವೀಕರಿಸಿ</value>
  </data>
  <data name="Acceptablelimit" xml:space="preserve">
    <value>ಪ್ರಮಾಣ ಸ್ವೀಕಾರಾರ್ಹ ಮಿತಿಯನ್ನು ಮೀರಿ</value>
  </data>
  <data name="AcceptedQuantity" xml:space="preserve">
    <value>ಅಕ್ಸೆಪ್ಟೆಡ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="AcceptedQuantityCannotbegreaterthanOrderedQty" xml:space="preserve">
    <value>ಅಕ್ಸೆಪ್ಟೆಡ್ ಪ್ರಮಾಣ ಆದೇಶ ಪ್ರಮಾಣ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="AcceptedQuantityPO" xml:space="preserve">
    <value>ಅಕ್ಸೆಪ್ಟೆಡ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="Acceptedquantityshouldbesameasorderedquantity" xml:space="preserve">
    <value>ಪ್ರಮಾಣ ಆದೇಶ ಸ್ವೀಕರಿಸಲ್ಪಟ್ಟ ಪ್ರಮಾಣ ಒಂದೇ ಆಗಿರಬೇಕು</value>
  </data>
  <data name="AccountNumber" xml:space="preserve">
    <value>ಖಾತೆ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ACKDate" xml:space="preserve">
    <value>ಸ್ವೀಕೃತಿ ದಿನಾಂಕ</value>
  </data>
  <data name="Action" xml:space="preserve">
    <value>ಕ್ರಿಯೆ</value>
  </data>
  <data name="ActionBy" xml:space="preserve">
    <value>ಕ್ರಮ</value>
  </data>
  <data name="ActionDate" xml:space="preserve">
    <value>ಆಕ್ಷನ್ ದಿನಾಂಕ</value>
  </data>
  <data name="ActionDetails" xml:space="preserve">
    <value>ಆಕ್ಷನ್ ವಿವರಗಳು</value>
  </data>
  <data name="ActionForNextService" xml:space="preserve">
    <value>ಮುಂದೆ ಸೇವೆ ಆಕ್ಷನ್</value>
  </data>
  <data name="ActionName" xml:space="preserve">
    <value>ಆಕ್ಷನ್ ಹೆಸರು</value>
  </data>
  <data name="ActionRemarks" xml:space="preserve">
    <value>ಆಕ್ಷನ್ ರಿಮಾರ್ಕ್ಸ್</value>
  </data>
  <data name="ActionRemarksMaxlimitexceeded" xml:space="preserve">
    <value>ಮೀರಿದೆ ಆಕ್ಷನ್ ರಿಮಾರ್ಕ್ಸ್ ಗರಿಷ್ಠ ಮಿತಿಯನ್ನು</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>ಕ್ರಿಯೆಗಳು</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>ಸಕ್ರಿಯವಾಗಿದೆ?</value>
  </data>
  <data name="ActiveFrom" xml:space="preserve">
    <value>ಸಕ್ರಿಯ</value>
  </data>
  <data name="ActiveFromdatecannotbegreaterthanActiveTodate" xml:space="preserve">
    <value>ದಿನಾಂಕದಿಂದ ಸಕ್ರಿಯ ಇಲ್ಲಿಯವರೆಗೆ ಸಕ್ರಿಯ ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ActiveTo" xml:space="preserve">
    <value>ಪುಟಗಳು</value>
  </data>
  <data name="Activity" xml:space="preserve">
    <value>ಚಟುವಟಿಕೆ</value>
  </data>
  <data name="ActualAvailability" xml:space="preserve">
    <value>ನಿಜವಾದ ಲಭ್ಯತೆ%</value>
  </data>
  <data name="Actualcompletiondatecannotbelessactualstartdate" xml:space="preserve">
    <value>ನಿಜವಾದ ಅಂತಿಮ ದಿನಾಂಕ ನಿಜವಾದ ಆರಂಭ ದಿನಾಂಕ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ActualEndDate" xml:space="preserve">
    <value>ನಿಜವಾದ ಅಂತಿಮ ದಿನಾಂಕ</value>
  </data>
  <data name="ActualHours" xml:space="preserve">
    <value>ನಿಜವಾದ ಅವರ್ಸ್</value>
  </data>
  <data name="ActualStartDate" xml:space="preserve">
    <value>ನಿಜವಾದ ಆರಂಭ ದಿನಾಂಕ</value>
  </data>
  <data name="Actualstartdatecannotbelessthanplannedstartdate" xml:space="preserve">
    <value>ನಿಜವಾದ ಆರಂಭ ದಿನಾಂಕ ಯೋಜನೆ ಆರಂಭ ದಿನಾಂಕ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>ಸೇರಿಸು</value>
  </data>
  <data name="AddAction" xml:space="preserve">
    <value>ಆಕ್ಷನ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddActivity" xml:space="preserve">
    <value>ಚಟುವಟಿಕೆ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddArticle" xml:space="preserve">
    <value>ಲೇಖನ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddBranch" xml:space="preserve">
    <value>ಶಾಖೆ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddBranchTaxDetails" xml:space="preserve">
    <value>ಶಾಖೆ ತೆರಿಗೆ ವಿವರಗಳು ಸೇರಿಸಿ</value>
  </data>
  <data name="AddBrands" xml:space="preserve">
    <value>ಬ್ರಾಂಡ್ಸ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddCampaingDetails" xml:space="preserve">
    <value>ಕ್ಯಾಂಪೇನ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddCompany" xml:space="preserve">
    <value>ಸೇರಿಸಿ ಕಂಪನಿ</value>
  </data>
  <data name="AddCompanyRelation" xml:space="preserve">
    <value>ಕಂಪನಿ ನಂಟು ಸೇರಿಸಿ</value>
  </data>
  <data name="AddCompanyTaxDetails" xml:space="preserve">
    <value>ಕಂಪನಿ ತೆರಿಗೆ ವಿವರಗಳು ಸೇರಿಸಿ</value>
  </data>
  <data name="AddCompanyTerms" xml:space="preserve">
    <value>ಕಂಪನಿ ನಿಯಮಗಳು ಸೇರಿಸಿ</value>
  </data>
  <data name="addcomponentdetails" xml:space="preserve">
    <value>ಕಾಂಪೊನೆಂಟ್ ವಿವರ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddCreditNote" xml:space="preserve">
    <value>ಕ್ರೆಡಿಟ್ ಟಿಪ್ಪಣಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="addcustomer" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ಸೇರಿಸಿ</value>
  </data>
  <data name="addcustomerorderclass" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ಆರ್ಡರ್ ವರ್ಗ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddCustomerQuotation" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ಉದ್ಧರಣ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddDebitNote" xml:space="preserve">
    <value>ಡೆಬಿಟ್ ಟಿಪ್ಪಣಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddDefectPartsDisposal" xml:space="preserve">
    <value>ದೋಷ ಭಾಗಗಳು ವಿಲೇವಾರಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddEmployee" xml:space="preserve">
    <value>ಸೇರಿಸಿ ನೌಕರರ</value>
  </data>
  <data name="AddEvents" xml:space="preserve">
    <value>ಸೇರಿಸಿ ಕ್ರಿಯೆಗಳು</value>
  </data>
  <data name="AddFilter" xml:space="preserve">
    <value>ಆಯ್ಕೆ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddFloatChangeLocation" xml:space="preserve">
    <value>ಫ್ಲೋಟ್ ಸ್ಥಳ ಬದಲಾವಣೆ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddFloatInvoice" xml:space="preserve">
    <value>ಫ್ಲೋಟ್ ಸರಕುಪಟ್ಟಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddFloatIssueRequest" xml:space="preserve">
    <value>ಫ್ಲೋಟ್ ಸಂಚಿಕೆ ವಿನಂತಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddFloatScrap" xml:space="preserve">
    <value>ಫ್ಲೋಟ್ ಸ್ಕ್ರ್ಯಾಪ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="addfreestock" xml:space="preserve">
    <value>ಭಾಗಗಳು ಉಚಿತ ಸ್ಟಾಕ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddFunctionGroup" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್ ಗುಂಪು ಸೇರಿಸು</value>
  </data>
  <data name="AddGDRSettlement" xml:space="preserve">
    <value>ಜಿಡಿಆರ್ ಸೆಟ್ಲ್ಮೆಂಟ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddInternalInvoice" xml:space="preserve">
    <value>ಆಂತರಿಕ ಸರಕುಪಟ್ಟಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddInternalInvoiceReturn" xml:space="preserve">
    <value>ಆಂತರಿಕ ಸರಕುಪಟ್ಟಿ ರಿಟರ್ನ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddJobCard" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddJobCardPartsReturn" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ಭಾಗಗಳು ರಿಟರ್ನ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddKitBom" xml:space="preserve">
    <value>ಕಿಟ್ ಬೊಮ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddKitBreaking" xml:space="preserve">
    <value>ಕಿಟ್ ಬ್ರೇಕಿಂಗ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddMakingofKit" xml:space="preserve">
    <value>ಕಿಟ್ ಮೇಕಿಂಗ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddMandatoryClaim" xml:space="preserve">
    <value>ಕಡ್ಡಾಯ ಹಕ್ಕು ಸೇರಿಸಿ</value>
  </data>
  <data name="AddMaster" xml:space="preserve">
    <value>ಮಾಸ್ಟರ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="addmodel" xml:space="preserve">
    <value>ಮಾದರಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="addnewpart" xml:space="preserve">
    <value>ಹೊಸ ಭಾಗ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddNullPick" xml:space="preserve">
    <value>ನಲ್ ಪಿಕ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="addoperation" xml:space="preserve">
    <value>ಸೇರಿಸಿ ಆಪರೇಷನ್</value>
  </data>
  <data name="addoperation1" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ವಿವರಗಳು ಸೇರಿಸಿ</value>
  </data>
  <data name="AddOperationEmployeeDetails" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ನೌಕರರ ವಿವರಗಳು ಸೇರಿಸಿ</value>
  </data>
  <data name="AddPackingList" xml:space="preserve">
    <value>ಪ್ಯಾಕಿಂಗ್ ಪಟ್ಟಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddPart" xml:space="preserve">
    <value>ಭಾಗ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddPartFreeStockDetails" xml:space="preserve">
    <value>ಸೇರಿಸಿ ಭಾಗ ಉಚಿತ ಸ್ಟಾಕ್ ವಿವರಗಳು</value>
  </data>
  <data name="addpartprice" xml:space="preserve">
    <value>ಭಾಗ ಬೆಲೆ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddParts" xml:space="preserve">
    <value>ಭಾಗಗಳನ್ನು ಸೇರಿಸಿ</value>
  </data>
  <data name="AddParty" xml:space="preserve">
    <value>ಸೇರಿಸಿ ಪಕ್ಷದ</value>
  </data>
  <data name="AddPrefixSuffix" xml:space="preserve">
    <value>ಪೂರ್ವ ಪ್ರತ್ಯಯ ಸೇರಿಸಿ</value>
  </data>
  <data name="addproduct" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಸೇರಿಸಿ</value>
  </data>
  <data name="addproductdetail" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ವಿವರ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddProductSalesInvoice" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddProductSalesInvoiceCancellation" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ ರದ್ದತಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddProductTransferGRN" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಟ್ರಾನ್ಸ್ಫರ್ ಜಿ ಆರ್ ಎನ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddProductTransferNote" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಟ್ರಾನ್ಸ್ಫರ್ ಟಿಪ್ಪಣಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddProductTransferRequest" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ವರ್ಗಾವಣೆ ಮನವಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="addproducttype" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಪ್ರಕಾರ ಸೇರಿಸಿ</value>
  </data>
  <data name="addproducttypedetails" xml:space="preserve">
    <value>ಭಾಗ ಉತ್ಪನ್ನ ಪ್ರಕಾರ ವಿವರಗಳು ಸೇರಿಸಿ</value>
  </data>
  <data name="AddPurchaseGRN" xml:space="preserve">
    <value>ಖರೀದಿ ಜಿ ಆರ್ ಎನ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddPurchaseInvoice" xml:space="preserve">
    <value>ಖರೀದಿ ಸರಕುಪಟ್ಟಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddPurchaseOrder" xml:space="preserve">
    <value>ಸೇರಿಸಿ ಪರ್ಚೇಸ್ ಆರ್ಡರ್</value>
  </data>
  <data name="AddPurchaseOrderCancellation" xml:space="preserve">
    <value>ಪರ್ಚೇಸ್ ಆರ್ಡರ್ ರದ್ದತಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddReceipt" xml:space="preserve">
    <value>ರಸೀತಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddRequest" xml:space="preserve">
    <value>ವಿನಂತಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>ವಿಳಾಸ</value>
  </data>
  <data name="Address1" xml:space="preserve">
    <value>ಲೈನ್ 1 ವಿಳಾಸ</value>
  </data>
  <data name="Address2" xml:space="preserve">
    <value>ಲೈನ್ 2 ವಿಳಾಸ</value>
  </data>
  <data name="Address3" xml:space="preserve">
    <value>ಲೈನ್ 3 ವಿಳಾಸ</value>
  </data>
  <data name="AddresseFlag" xml:space="preserve">
    <value>ವಿಳಾಸ ಫ್ಲಾಗ್</value>
  </data>
  <data name="AddRole" xml:space="preserve">
    <value>ಪಾತ್ರ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddSerialNumber" xml:space="preserve">
    <value>ಕ್ರಮ ಸಂಖ್ಯೆ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddServiceAgreement" xml:space="preserve">
    <value>ಸೇವೆ ಒಪ್ಪಂದ ಸೇರಿಸಿ</value>
  </data>
  <data name="addServiceCharge" xml:space="preserve">
    <value>ಸೇವೆ ಚಾರ್ಜ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="addServiceChargeDetails" xml:space="preserve">
    <value>ಸೇವೆ ಚಾರ್ಜ್ ವಿವರಗಳು ಸೇರಿಸಿ</value>
  </data>
  <data name="addservicecharges" xml:space="preserve">
    <value>ಸೇವೆ ಚಾರ್ಜಸ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddServiceInvoice" xml:space="preserve">
    <value>ಸೇವೆ ಸರಕುಪಟ್ಟಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddServiceInvoiceReturn" xml:space="preserve">
    <value>ಸೇವೆ ಸರಕುಪಟ್ಟಿ ರಿಟರ್ನ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddServiceType" xml:space="preserve">
    <value>ಸೇವೆ ಟೈಪ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddServiceTypeHeader" xml:space="preserve">
    <value>ಸೇವೆ ಟೈಪ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddSibling" xml:space="preserve">
    <value>ಒಡಹುಟ್ಟಿದವರು ಸೇರಿಸಿ</value>
  </data>
  <data name="addsiteaddress" xml:space="preserve">
    <value>ಸೈಟ್ ವಿಳಾಸ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddSkills" xml:space="preserve">
    <value>ಸ್ಕಿಲ್ಸ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddSpecialization" xml:space="preserve">
    <value>ತಜ್ಞತೆ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddStep" xml:space="preserve">
    <value>ಹಂತ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddStepLink" xml:space="preserve">
    <value>ಹಂತ ಲಿಂಕ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddStockBlocking" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ನಿರ್ಬಂಧಿಸುವುದು ಸೇರಿಸಿ</value>
  </data>
  <data name="AddStockCheckConfirmation" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಚೆಕ್ ದೃಢೀಕರಣ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddStockCheckRequest" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಚೆಕ್ ವಿನಂತಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddStockCheckRequest1" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಚೆಕ್ ವಿನಂತಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddStockReceiptGRN" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ರಸೀತಿ ಜಿ ಆರ್ ಎನ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddStockTransferRequest" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ವರ್ಗಾವಣೆ ಮನವಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddSubfolder" xml:space="preserve">
    <value>ಉಪ ಫೋಲ್ಡರ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddSundry" xml:space="preserve">
    <value>ಸಂಡ್ರಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddSupersession" xml:space="preserve">
    <value>ಸೂಪರ್ ಅಧಿವೇಶನದಲ್ಲಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="addSupplierorderclass" xml:space="preserve">
    <value>ಸರಬರಾಜುದಾರ ಸಲುವಾಗಿ ವರ್ಗ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddTaxCode" xml:space="preserve">
    <value>ತೆರಿಗೆ ಕೋಡ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="addtaxstructure" xml:space="preserve">
    <value>ತೆರಿಗೆ ರಚನೆ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddTaxStructureDetails" xml:space="preserve">
    <value>ತೆರಿಗೆ ರಚನೆ ವಿವರಗಳು ಸೇರಿಸಿ</value>
  </data>
  <data name="addtaxtstructuredetails" xml:space="preserve">
    <value>ತೆರಿಗೆ ರಚನೆ ವಿವರಗಳು ಸೇರಿಸಿ</value>
  </data>
  <data name="AddTool" xml:space="preserve">
    <value>ಟೂಲ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddTravel" xml:space="preserve">
    <value>ಪ್ರಯಾಣ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddUser" xml:space="preserve">
    <value>ಬಳಕೆದಾರ ಸೇರಿಸಿ</value>
  </data>
  <data name="addwarrantydetails" xml:space="preserve">
    <value>ಖಾತರಿ ವಿವರಗಳು ಸೇರಿಸಿ</value>
  </data>
  <data name="AddWorkDetails" xml:space="preserve">
    <value>ಕೆಲಸದ ವಿವರಗಳು ಸೇರಿಸಿ</value>
  </data>
  <data name="Adjusted" xml:space="preserve">
    <value>ಸರಿಪಡಿಸಲಾಯಿತು</value>
  </data>
  <data name="AdjustedValue" xml:space="preserve">
    <value>ಹೊಂದಾಣಿಕೆಯ ಮೌಲ್ಯ</value>
  </data>
  <data name="advancesearch" xml:space="preserve">
    <value>ಅಡ್ವಾನ್ಸ್ ಹುಡುಕು</value>
  </data>
  <data name="Ageing" xml:space="preserve">
    <value>ಏಜಿಂಗ್</value>
  </data>
  <data name="AgeingAnalysisReport" xml:space="preserve">
    <value>ವಿಶ್ಲೇಷಣೆಯ ವರದಿ ಏಜಿಂಗ್</value>
  </data>
  <data name="AgeingHours" xml:space="preserve">
    <value>ಏಜಿಂಗ್ ಅವರ್ಸ್</value>
  </data>
  <data name="AgingAnalysisReport" xml:space="preserve">
    <value>ಏಜಿಂಗ್ ವಿಶ್ಲೇಷಣೆ ಡೆಡ್ ಸ್ಟಾಕ್ ವರದಿ</value>
  </data>
  <data name="aliaspartnumber" xml:space="preserve">
    <value>ಅಲಿಯಾಸ್ ಭಾಗ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="all" xml:space="preserve">
    <value>ಎಲ್ಲಾ</value>
  </data>
  <data name="Allocate" xml:space="preserve">
    <value>ಗೊತ್ತುಪಡಿಸು</value>
  </data>
  <data name="Allocated" xml:space="preserve">
    <value>ಹಂಚಿಕೆ</value>
  </data>
  <data name="AllocatedDetails" xml:space="preserve">
    <value>ಹಂಚಿಕೆ ವಿವರಗಳು</value>
  </data>
  <data name="AllocatedHours" xml:space="preserve">
    <value>ಹಂಚಿಕೆ ಅವರ್ಸ್</value>
  </data>
  <data name="AllocatedQty" xml:space="preserve">
    <value>ಹಂಚಿಕೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="AllocatedQuantity" xml:space="preserve">
    <value>ಹಂಚಿಕೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="AllocatedValueExVAT" xml:space="preserve">
    <value>ಹಂಚಿಕೆ ಮೌಲ್ಯ ಮಾಜಿ ವ್ಯಾಟ್</value>
  </data>
  <data name="Allocation" xml:space="preserve">
    <value>ಹಂಚಿಕೆ</value>
  </data>
  <data name="AllocationNotPossible" xml:space="preserve">
    <value>ಹಂಚಿಕೆ ಸಾಧ್ಯವಿರುವುದಿಲ್ಲ</value>
  </data>
  <data name="AllocationofParts" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಹಂಚಿಕೆ</value>
  </data>
  <data name="AllocationPriority" xml:space="preserve">
    <value>ಹಂಚಿಕೆ ಆದ್ಯತಾ</value>
  </data>
  <data name="AllocationStatus" xml:space="preserve">
    <value>ಹಂಚಿಕೆ ಸ್ಥಿತಿ</value>
  </data>
  <data name="AlloctedSuccessfully" xml:space="preserve">
    <value>ಯಶಸ್ವಿಯಾಗಿ ಹಂಚಿಕೆ</value>
  </data>
  <data name="AlloctedSuccessfully1" xml:space="preserve">
    <value>ಯಶಸ್ವಿಯಾಗಿ ಹಂಚಿಕೆ</value>
  </data>
  <data name="AlloctionFailed" xml:space="preserve">
    <value>ಹಂಚಿಕೆ ವಿಫಲವಾಗಿದೆ</value>
  </data>
  <data name="AlloctionFailed1" xml:space="preserve">
    <value>ಹಂಚಿಕೆ ವಿಫಲವಾಗಿದೆ</value>
  </data>
  <data name="AllOperationShouldBeCompleted" xml:space="preserve">
    <value>ಎಲ್ಲಾ ಕಾರ್ಯಾಚರಣೆ ಪೂರ್ಣಗೊಳ್ಳುವ</value>
  </data>
  <data name="AllQueue" xml:space="preserve">
    <value>ಎಲ್ಲಾ ಸರದಿಗೆ</value>
  </data>
  <data name="AllSundryshouldbecompleted" xml:space="preserve">
    <value>ಎಲ್ಲಾ ಬಗೆಬಗೆಯ ಪೂರ್ಣಗೊಂಡಿತು ಮಾಡಬೇಕು</value>
  </data>
  <data name="Allthepartshastobeallocatedwithserialnumber" xml:space="preserve">
    <value>ಎಲ್ಲಾ ಭಾಗಗಳು ಕ್ರಮ ಸಂಖ್ಯೆ ಹಂಚಲಾಗುವುದು ಹೊಂದಿದೆ</value>
  </data>
  <data name="alreadyakitpart" xml:space="preserve">
    <value>ಈಗಾಗಲೇ ಕಿಟ್ ಭಾಗ</value>
  </data>
  <data name="AlreadyApproved" xml:space="preserve">
    <value>ಈಗಾಗಲೇ ಅನುಮೋದನೆ</value>
  </data>
  <data name="AlreadyAssociatedPleaseSelectfromDropDown" xml:space="preserve">
    <value>ಈಗಾಗಲೇ ಸಂಬಂಧಿಸಿದ ಡ್ರಾಪ್ ಡೌನ್ ನಿಂದ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="AlreadyClosed" xml:space="preserve">
    <value>ಈಗಾಗಲೆ ಮುಚ್ಚಲಾಗಿದೆ</value>
  </data>
  <data name="alreadyexists" xml:space="preserve">
    <value>ಈಗಾಗಲೇ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="alreadyexistsforthelocation" xml:space="preserve">
    <value>ಈಗಾಗಲೇ ಸ್ಥಳ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="alreadyexistswithinactivestatus" xml:space="preserve">
    <value>ಈಗಾಗಲೇ ನಿಷ್ಕ್ರಿಯ ಸ್ಥಿತಿಯನ್ನು ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="AlreadyRejected" xml:space="preserve">
    <value>ಈಗಾಗಲೇ ತಿರಸ್ಕರಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="AlternatePartNumber" xml:space="preserve">
    <value>ಪರ್ಯಾಯ ಭಾಗ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>ವೊತ್ತ</value>
  </data>
  <data name="AmountBlank" xml:space="preserve">
    <value>ವೊತ್ತ ಖಾಲಿ</value>
  </data>
  <data name="Amountcannotbegreaterthancommittedvalue" xml:space="preserve">
    <value>ವೊತ್ತ ಬದ್ಧ ಮೌಲ್ಯ ಹೆಚ್ಚಾಗಿದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="AmountIsBeyondAcceptableLimit" xml:space="preserve">
    <value>ವೊತ್ತ ಸ್ವೀಕಾರಾರ್ಹ ಮಿತಿಯನ್ನು ಮೀರಿ</value>
  </data>
  <data name="AmountPaid" xml:space="preserve">
    <value>ಮೊತ್ತವನ್ನು</value>
  </data>
  <data name="AmountReceived" xml:space="preserve">
    <value>ಸ್ವೀಕರಿಸಲಾಗಿದೆ ವೊತ್ತ</value>
  </data>
  <data name="AmountShouldbeLessThan" xml:space="preserve">
    <value>ವೊತ್ತ ಕಡಿಮೆ ಮಾಡಬೇಕು</value>
  </data>
  <data name="AND" xml:space="preserve">
    <value>ಮತ್ತು</value>
  </data>
  <data name="Annexure" xml:space="preserve">
    <value>ಅನುಬಂಧನ</value>
  </data>
  <data name="ApprovalLimit" xml:space="preserve">
    <value>ಅನುಮೋದನೆ ಮಿತಿ</value>
  </data>
  <data name="ApprovalStatus" xml:space="preserve">
    <value>ಅನುಮೋದನೆ ಸ್ಥಿತಿ</value>
  </data>
  <data name="Approve" xml:space="preserve">
    <value>ಅನುಮೋದಿಸಿ</value>
  </data>
  <data name="Approved" xml:space="preserve">
    <value>ಅಂಗೀಕರಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="Approved1" xml:space="preserve">
    <value>ಅಂಗೀಕರಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="ApprovedAmount" xml:space="preserve">
    <value>ಅಂಗೀಕರಿಸಲಾಗಿದ ವೊತ್ತ</value>
  </data>
  <data name="ApprovedAmountCannotBeGreaterThanClaimedAmount" xml:space="preserve">
    <value>ಅಂಗೀಕರಿಸಲಾಗಿದ ವೊತ್ತ ಹಕ್ಕು ಪ್ರಮಾಣದ ಹೆಚ್ಚಾಗಿದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ApprovedAmountCannotbeZero" xml:space="preserve">
    <value>ಮಂಜೂರಾದ ಮೊತ್ತದ ಶೂನ್ಯ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ApprovedQuantity" xml:space="preserve">
    <value>ಅಂಗೀಕರಿಸಲಾಗಿದ ಪ್ರಮಾಣ</value>
  </data>
  <data name="ApprovedQuantityCannotBeGreaterThanQuantity" xml:space="preserve">
    <value>ಅಂಗೀಕರಿಸಲಾಗಿದ ಪ್ರಮಾಣ ಹಕ್ಕು ಪ್ರಮಾಣ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="April" xml:space="preserve">
    <value>ಏಪ್ರಿ</value>
  </data>
  <data name="Aresurewanttodelete" xml:space="preserve">
    <value>ಖಚಿತವಾಗಿ ಅಳಿಸಲು ಬಯಸುವಿರಾ</value>
  </data>
  <data name="Areyousurewanttocancel" xml:space="preserve">
    <value>ನೀವು ರದ್ದು ಬಯಸುತ್ತೀರೆ?</value>
  </data>
  <data name="AreYouSureWantToDel" xml:space="preserve">
    <value>ನೀವು ಖಚಿತವಾಗಿ ಅಳಿಸಲು ಬಯಸುವಿರಾ</value>
  </data>
  <data name="Areyousurewanttodelete" xml:space="preserve">
    <value>ಅಳಿಸಲು ನೀವು ಖಚಿತವೆ?</value>
  </data>
  <data name="AreyousureyouwanttoAbandontheRequest" xml:space="preserve">
    <value>ನೀವು ವಿನಂತಿಯನ್ನು ತ್ಯಜಿಸಲು ಬಯಸುತ್ತೀರೆ</value>
  </data>
  <data name="AreyousureyouwanttoLogout" xml:space="preserve">
    <value>ನೀವು ಲಾಗ್ ಔಟ್ ಖಚಿತವೆ</value>
  </data>
  <data name="AreyousureyouwanttoLogout1" xml:space="preserve">
    <value>ನೀವು ನಿರ್ಗಮಿಸಬೇಕಾಗುತ್ತದೆ ಖಚಿತವೆ</value>
  </data>
  <data name="areyousureyouwanttomovetheevent" xml:space="preserve">
    <value>ನೀವು ಈವೆಂಟ್ ಸರಿಸಲು ಬಯಸುತ್ತೀರೆ?</value>
  </data>
  <data name="ArrivedDate" xml:space="preserve">
    <value>ಆಗಮಿಸಿದ ದಿನಾಂಕ</value>
  </data>
  <data name="Article" xml:space="preserve">
    <value>ಲೇಖನ</value>
  </data>
  <data name="ArticleName" xml:space="preserve">
    <value>ಲೇಖನ ಹೆಸರು</value>
  </data>
  <data name="Assaign" xml:space="preserve">
    <value>ನಿಗದಿಪಡಿಸಿ</value>
  </data>
  <data name="Assembling" xml:space="preserve">
    <value>ಜೋಡಣೆ</value>
  </data>
  <data name="AssessableValue" xml:space="preserve">
    <value>ಅಂದಾಜು ಮೌಲ್ಯ</value>
  </data>
  <data name="AssessableValuecannotbezero" xml:space="preserve">
    <value>ಅಂದಾಜು ಮೌಲ್ಯ ಶೂನ್ಯ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Assign" xml:space="preserve">
    <value>ನಿಗದಿಪಡಿಸಿ</value>
  </data>
  <data name="AssignedTo" xml:space="preserve">
    <value>ನಿಯೋಜಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="AssignTo" xml:space="preserve">
    <value>ನಿಗದಿ</value>
  </data>
  <data name="atleastonerecordshouldbeaddedtopartsdetailgrid" xml:space="preserve">
    <value>ಕನಿಷ್ಠ ಒಂದು ದಾಖಲೆ ಭಾಗಗಳು ವಿವರ ಜಾಲಕ್ಕೆ ಸೇರಿಸಲಾಗಿದೆ ಮಾಡಬೇಕು</value>
  </data>
  <data name="atleastonerecordshouldbeaddedtosuperseedingpartpartsdetailgrid" xml:space="preserve">
    <value>ಕನಿಷ್ಠ ಒಂದು ದಾಖಲೆ ಭಾಗಗಳು ವಿವರ ಗ್ರಿಡ್ superseeding ಸೇರಿಸಲಾಗಿದೆ ಮಾಡಬೇಕು</value>
  </data>
  <data name="Atleastselectonedetail" xml:space="preserve">
    <value>ಕನಿಷ್ಠ ಒಂದು ವಿವರ ಆಯ್ಕೆ</value>
  </data>
  <data name="AttachmentDetail" xml:space="preserve">
    <value>ಬಾಂಧವ್ಯ ವಿವರ</value>
  </data>
  <data name="AttachmentDetails" xml:space="preserve">
    <value>ಬಾಂಧವ್ಯ ವಿವರಗಳು</value>
  </data>
  <data name="Attachments" xml:space="preserve">
    <value>ಲಗತ್ತುಗಳು</value>
  </data>
  <data name="AttachmentUpload" xml:space="preserve">
    <value>ಬಾಂಧವ್ಯ ಅಪ್ಲೋಡ್</value>
  </data>
  <data name="August" xml:space="preserve">
    <value>ಆಗಸ್ಟ್</value>
  </data>
  <data name="AuthorizedSignatory" xml:space="preserve">
    <value>ಅಧಿಕೃತ ಸಹಿದಾರ</value>
  </data>
  <data name="autoallocate" xml:space="preserve">
    <value>ಆಟೋ ನಿಗದಿಪಡಿಸಬೇಕಾಗುತ್ತದೆ</value>
  </data>
  <data name="AutoAllocated" xml:space="preserve">
    <value>ಆಟೋ ನಿಗದಿ</value>
  </data>
  <data name="AutoAllocationAllowed" xml:space="preserve">
    <value>ಆಟೋ ಅಲೋಕೇಶನ್ ಅನುಮತಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="AutoAllocationCondition" xml:space="preserve">
    <value>ಆಟೋ ಅಲೋಕೇಶನ್ ಕಂಡಿಶನ್</value>
  </data>
  <data name="AutoAllocationFieldName" xml:space="preserve">
    <value>ಆಟೋ ಅಲೋಕೇಶನ್ ಫೀಲ್ಡ್ ಹೆಸರು</value>
  </data>
  <data name="AutomaticPICannotBeCreatedSincePIAtDealerAlereadyExistsForTheseSerialNumbersDoYouWantToProceed" xml:space="preserve">
    <value>ಈ ಸರಣಿ ಸಂಖ್ಯೆಗಳನ್ನು ನೀವು ಮುಂದುವರಿಸಲು ಬಯಸುತ್ತೀರಾ ಫಾರ್ ಡೀಲರ್ ಪಿಐ ಈಗಾಗಲೇ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ ರಿಂದ ಸ್ವಯಂಚಾಲಿತ ಪಿಐ ರಚಿಸಲಾಗುವುದಿಲ್ಲ</value>
  </data>
  <data name="AvailabilityStock" xml:space="preserve">
    <value>ಲಭ್ಯತೆ ಸ್ಟಾಕ್</value>
  </data>
  <data name="Available" xml:space="preserve">
    <value>ಲಭ್ಯ</value>
  </data>
  <data name="AvailableSerialNumbers" xml:space="preserve">
    <value>ಲಭ್ಯವಿರುವ ಸರಣಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="AvailableStock" xml:space="preserve">
    <value>ಲಭ್ಯವಿರುವ ಸ್ಟಾಕ್</value>
  </data>
  <data name="AVE" xml:space="preserve">
    <value>AVE</value>
  </data>
  <data name="AveragePurchasePrice" xml:space="preserve">
    <value>ಸರಾಸರಿ ಖರೀದಿ ಬೆಲೆ</value>
  </data>
  <data name="AverageResolutionTime" xml:space="preserve">
    <value>ಸರಾಸರಿ ರೆಸಲ್ಯೂಶನ್ ಟೈಮ್</value>
  </data>
  <data name="averageresolutiontimeyearwise" xml:space="preserve">
    <value>ಸರಾಸರಿ ರೆಸಲ್ಯೂಶನ್ ಟೈಮ್ - ವರ್ಷವಾರು</value>
  </data>
  <data name="averageresponsetime" xml:space="preserve">
    <value>ಸರಾಸರಿ ಪ್ರತಿಕ್ರಿಯೆ ಸಮಯ</value>
  </data>
  <data name="averageresponsetimeyearwise" xml:space="preserve">
    <value>ಸರಾಸರಿ ಪ್ರತಿಕ್ರಿಯೆ ಸಮಯ-ವರ್ಷವಾರು</value>
  </data>
  <data name="AverageSellingPrice" xml:space="preserve">
    <value>ಸರಾಸರಿ ಮಾರಾಟದ ಬೆಲೆ</value>
  </data>
  <data name="AverageTime" xml:space="preserve">
    <value>ಸರಾಸರಿ ಸಮಯ</value>
  </data>
  <data name="AVEs" xml:space="preserve">
    <value>ಸಾಗರದಿಂದ</value>
  </data>
  <data name="AvgResolutionTime" xml:space="preserve">
    <value>ಆವರೇಜ್ ರೆಸಲ್ಯೂಶನ್ ಟೈಮ್</value>
  </data>
  <data name="B" xml:space="preserve">
    <value>ಬಿ</value>
  </data>
  <data name="Back" xml:space="preserve">
    <value>ಹಿಂದೆ</value>
  </data>
  <data name="BackOrder" xml:space="preserve">
    <value>ಬ್ಯಾಕ್ ಆರ್ಡರ್</value>
  </data>
  <data name="BackOrderDetails" xml:space="preserve">
    <value>ಬ್ಯಾಕ್ ಆರ್ಡರ್ ವಿವರಗಳು</value>
  </data>
  <data name="BackOrderQty" xml:space="preserve">
    <value>ಬಿ ಓ ಪ್ರಮಾಣ</value>
  </data>
  <data name="BackOrderStatus" xml:space="preserve">
    <value>ಬ್ಯಾಕ್ ಆರ್ಡರ್ ಸ್ಥಿತಿ</value>
  </data>
  <data name="BackOrderStatusOptions" xml:space="preserve">
    <value>ಬ್ಯಾಕ್ ಆರ್ಡರ್ ಸ್ಥಿತಿ ಆಯ್ಕೆಗಳು</value>
  </data>
  <data name="BackOrderValueExVAT" xml:space="preserve">
    <value>ಬ್ಯಾಕ್ ಆರ್ಡರ್ ಮೌಲ್ಯ ಮಾಜಿ ವ್ಯಾಟ್</value>
  </data>
  <data name="BacktoBackOrder" xml:space="preserve">
    <value>ಮತ್ತೆ ಆರ್ಡರ್</value>
  </data>
  <data name="Balance" xml:space="preserve">
    <value>ಬ್ಯಾಲೆನ್ಸ್</value>
  </data>
  <data name="BalanceAmount" xml:space="preserve">
    <value>ಬ್ಯಾಲೆನ್ಸ್ ವೊತ್ತ</value>
  </data>
  <data name="BankDetails" xml:space="preserve">
    <value>ಬ್ಯಾಂಕ್ ವಿವರಗಳು</value>
  </data>
  <data name="BankName" xml:space="preserve">
    <value>ಬ್ಯಾಂಕ್ ಹೆಸರು</value>
  </data>
  <data name="BarChart" xml:space="preserve">
    <value>ನಕಾಶೆ</value>
  </data>
  <data name="BEDTotal" xml:space="preserve">
    <value>ಒಟ್ಟು</value>
  </data>
  <data name="Belowarecustomerprovidedparts" xml:space="preserve">
    <value>ಕೆಳಗಿನ ಗ್ರಾಹಕರ ಒದಗಿಸಿದ ಭಾಗಗಳು</value>
  </data>
  <data name="BelowEightHours" xml:space="preserve">
    <value>8 ಕೆಳಗೆ ಅವರ್ಸ್</value>
  </data>
  <data name="BillDate" xml:space="preserve">
    <value>ಬಿಲ್ ದಿನಾಂಕ</value>
  </data>
  <data name="BillingAddress" xml:space="preserve">
    <value>ಬಿಲ್ಲಿಂಗ್ ವಿಳಾಸ</value>
  </data>
  <data name="BillingCurrency" xml:space="preserve">
    <value>ಬಿಲ್ಲಿಂಗ್ ಕರೆನ್ಸಿ</value>
  </data>
  <data name="BillingReport" xml:space="preserve">
    <value>ಬಿಲ್ ಸೆಟ್ಲ್ಮೆಂಟ್  ವರದಿ</value>
  </data>
  <data name="BillNo" xml:space="preserve">
    <value>ಬಿಲ್ ಯಾವುದೇ</value>
  </data>
  <data name="BillOfEntry" xml:space="preserve">
    <value>ಎಂಟ್ರಿ ಬಿಲ್</value>
  </data>
  <data name="BillOfEntryDate" xml:space="preserve">
    <value>ಎಂಟ್ರಿ ದಿನಾಂಕ ಬಿಲ್</value>
  </data>
  <data name="BillOfEntryDetailReport" xml:space="preserve">
    <value>ಎಂಟ್ರಿ ವಿವರ ವರದಿ ಬಿಲ್</value>
  </data>
  <data name="BillOfentryisalreadydoneforthisPINum" xml:space="preserve">
    <value>ಎಂಟ್ರಿ ಬಿಲ್ ಈಗಾಗಲೇ ಈ ಪಿಐ ಸಂಖ್ಯೆ ಮಾಡಲಾಗುತ್ತದೆ</value>
  </data>
  <data name="BillOfEntryNum" xml:space="preserve">
    <value>ಎಂಟ್ರಿ ಸಂಖ್ಯೆಯ ಬಿಲ್</value>
  </data>
  <data name="BillSettledNo" xml:space="preserve">
    <value>ಬಿಲ್ ನೆಲೆ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="BillSettlementDetails" xml:space="preserve">
    <value>ಬಿಲ್ ಸೆಟ್ಲ್ಮೆಂಟ್ ವಿವರಗಳು</value>
  </data>
  <data name="binlocation" xml:space="preserve">
    <value>ಬಿನ್ ಸ್ಥಳ</value>
  </data>
  <data name="BinLocationaddedsuccessfully" xml:space="preserve">
    <value>ಬಿನ್ ಸ್ಥಳ ಯಶಸ್ವಿಯಾಗಿ ಸೇರಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="BinLocationCannotbesameasBufferBinLocation" xml:space="preserve">
    <value>ಬಿನ್ ಸ್ಥಳ ಬಫರ್ ಬಿನ್ ಸ್ಥಳ ಅದೇ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="BinLocationDetails" xml:space="preserve">
    <value>ಬಿನ್ ಸ್ಥಳ ವಿವರಗಳು</value>
  </data>
  <data name="BinLocationnotfounddoyouwanttoadd" xml:space="preserve">
    <value>ಬಿನ್ ಸ್ಥಳ ಕಂಡುಬಂದಿಲ್ಲ, ನೀವು ಸೇರಿಸಲು ಬಯಸುವ ಇಲ್ಲ?</value>
  </data>
  <data name="BinningList" xml:space="preserve">
    <value>Binning ಪಟ್ಟಿ</value>
  </data>
  <data name="binstock" xml:space="preserve">
    <value>ಬಿನ್ ಸ್ಟಾಕ್</value>
  </data>
  <data name="BlockedBy" xml:space="preserve">
    <value>ನಿರ್ಬಂಧಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="BlockedQuantity" xml:space="preserve">
    <value>ನಿರ್ಬಂಧಿಸಿದ ಪ್ರಮಾಣ</value>
  </data>
  <data name="Blockedquantitycannotbegreaterthanrequestedquantity" xml:space="preserve">
    <value>ಪ್ರಮಾಣ ಉಚಿತ ಸ್ಟಾಕ್ ಹೆಚ್ಚು ಅಥವಾ ವಿನಂತಿಸಿದ ಪ್ರಮಾಣ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="BlockedQuantityisGreaterthenFreeStock" xml:space="preserve">
    <value>ನಿರ್ಬಂಧಿಸಿದ ಪ್ರಮಾಣ ಉಚಿತ ಸ್ಟಾಕ್ ಹೆಚ್ಚಾಗಿದೆ</value>
  </data>
  <data name="BOMDetail" xml:space="preserve">
    <value>BOM ವಿವರ</value>
  </data>
  <data name="BOMQuantity" xml:space="preserve">
    <value>BOM ಪ್ರಮಾಣ</value>
  </data>
  <data name="Bonding" xml:space="preserve">
    <value>ಬಂಧನ</value>
  </data>
  <data name="BondingDate" xml:space="preserve">
    <value>ಬಂಧನ ದಿನಾಂಕ</value>
  </data>
  <data name="BondingDetailReport" xml:space="preserve">
    <value>ಬಾಂಡಿಂಗ್ ವಿವರ ವರದಿ</value>
  </data>
  <data name="BondingDocumentType" xml:space="preserve">
    <value>ಡಾಕ್ಯುಮೆಂಟ್ ಪ್ರಕಾರ</value>
  </data>
  <data name="BondingisalreadydoneforthisPINum" xml:space="preserve">
    <value>ಬಾಂಡಿಂಗ್ ಈಗಾಗಲೇ ಈ ಪಿಐ ಸಂಖ್ಯೆ ಮಾಡಲಾಗುತ್ತದೆ</value>
  </data>
  <data name="BondingNum" xml:space="preserve">
    <value>ಬಂಧನ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="BondingNumber" xml:space="preserve">
    <value>ಬಂಧನ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="BOQuantity" xml:space="preserve">
    <value>BO ಪ್ರಮಾಣ</value>
  </data>
  <data name="Branch" xml:space="preserve">
    <value>ಶಾಖೆ</value>
  </data>
  <data name="BranchAlrearySelected" xml:space="preserve">
    <value>ಶಾಖೆ Alreary ಆಯ್ದ</value>
  </data>
  <data name="BranchAssociation" xml:space="preserve">
    <value>ಶಾಖೆ ಅಸೋಸಿಯೇಷನ್</value>
  </data>
  <data name="branchdetail" xml:space="preserve">
    <value>ಶಾಖೆ ವಿವರ</value>
  </data>
  <data name="BranchName" xml:space="preserve">
    <value>ಶಾಖೆ ಹೆಸರು</value>
  </data>
  <data name="BranchNameisalreadypresent" xml:space="preserve">
    <value>ಶಾಖೆ ಹೆಸರು ನಕಲು</value>
  </data>
  <data name="BranchSearch" xml:space="preserve">
    <value>ಶಾಖೆ ಹುಡುಕು</value>
  </data>
  <data name="BranchTaxCode" xml:space="preserve">
    <value>ಶಾಖೆ ತೆರಿಗೆ ಕೋಡ್</value>
  </data>
  <data name="BranchTaxDetails" xml:space="preserve">
    <value>ಶಾಖೆ ತೆರಿಗೆ ವಿವರಗಳು</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>ಮುದ್ರೆ</value>
  </data>
  <data name="BrandName" xml:space="preserve">
    <value>ಬ್ರಾಂಡ್ ಹೆಸರು</value>
  </data>
  <data name="BrandsAssociation" xml:space="preserve">
    <value>ಬ್ರಾಂಡ್ಸ್ ಅಸೋಸಿಯೇಷನ್</value>
  </data>
  <data name="Break" xml:space="preserve">
    <value>ಬಿರುಕು</value>
  </data>
  <data name="BreakDown" xml:space="preserve">
    <value>ಡೌನ್ ಬ್ರೇಕ್</value>
  </data>
  <data name="BreakFrom" xml:space="preserve">
    <value>ಗೆ ಬ್ರೇಕ್</value>
  </data>
  <data name="BreakFromandtotimeshouldbewithininworkinghour" xml:space="preserve">
    <value>ಸಮಯದಿಂದ ಬ್ರೇಕ್ ಮತ್ತು ಸಮಯ ಬ್ರೇಕ್ ಕೆಲಸದ ಒಳಗೆ ಇರಬೇಕು</value>
  </data>
  <data name="BreakFromTimeCannotbeGreaterThanBreakTotime" xml:space="preserve">
    <value>ಸಮಯದಿಂದ ಬ್ರೇಕ್ ಬ್ರೇಕ್ ಟೈಮ್ ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="BreakHourscannotbegreatorthanWorkingHours" xml:space="preserve">
    <value>ಬ್ರೇಕ್ ದುಡಿಮೆಯಲ್ಲಿ ಗಂಟೆಗೂ greator ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="BreakTo" xml:space="preserve">
    <value>ಬ್ರೇಕ್</value>
  </data>
  <data name="Browse" xml:space="preserve">
    <value>ಬ್ರೌಸ್</value>
  </data>
  <data name="BtnCancelCampaingHeader" xml:space="preserve">
    <value>ರದ್ದು</value>
  </data>
  <data name="bufferbinlocation" xml:space="preserve">
    <value>ಬಫರ್ ಬಿನ್ ಸ್ಥಳ</value>
  </data>
  <data name="BufferBinLocationCannotbesameasBinLocation" xml:space="preserve">
    <value>ಬಫರ್ ಬಿನ್ ಸ್ಥಳ ಬಿನ್ ಸ್ಥಳ ಅದೇ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Buyer" xml:space="preserve">
    <value>ಖರೀದಿದಾರ</value>
  </data>
  <data name="BuyingCurrency" xml:space="preserve">
    <value>ಕರೆನ್ಸಿ ಖರೀದಿ</value>
  </data>
  <data name="C" xml:space="preserve">
    <value>ಸಿ</value>
  </data>
  <data name="C0" xml:space="preserve">
    <value>C0</value>
  </data>
  <data name="C0NOP" xml:space="preserve">
    <value>C0NOP</value>
  </data>
  <data name="C1" xml:space="preserve">
    <value>ಸಿ 1</value>
  </data>
  <data name="C10" xml:space="preserve">
    <value>ಸಿ 10</value>
  </data>
  <data name="C10NOP" xml:space="preserve">
    <value>C10NOP</value>
  </data>
  <data name="C11" xml:space="preserve">
    <value>C11</value>
  </data>
  <data name="C11NOP" xml:space="preserve">
    <value>C11NOP</value>
  </data>
  <data name="C12" xml:space="preserve">
    <value>C12</value>
  </data>
  <data name="C12NOP" xml:space="preserve">
    <value>C12NOP</value>
  </data>
  <data name="C1NOP" xml:space="preserve">
    <value>C1NOP</value>
  </data>
  <data name="C2" xml:space="preserve">
    <value>C2</value>
  </data>
  <data name="C2NOP" xml:space="preserve">
    <value>C2NOP</value>
  </data>
  <data name="C3" xml:space="preserve">
    <value>C3</value>
  </data>
  <data name="C3NOP" xml:space="preserve">
    <value>C3NOP</value>
  </data>
  <data name="C4" xml:space="preserve">
    <value>C4</value>
  </data>
  <data name="C4NOP" xml:space="preserve">
    <value>C4NOP</value>
  </data>
  <data name="C5" xml:space="preserve">
    <value>C5</value>
  </data>
  <data name="C5NOP" xml:space="preserve">
    <value>C5NOP</value>
  </data>
  <data name="C6" xml:space="preserve">
    <value>ಇದು C6</value>
  </data>
  <data name="C6NOP" xml:space="preserve">
    <value>C6NOP</value>
  </data>
  <data name="C7" xml:space="preserve">
    <value>C7</value>
  </data>
  <data name="C7NOP" xml:space="preserve">
    <value>C7NOP</value>
  </data>
  <data name="C8" xml:space="preserve">
    <value>C8</value>
  </data>
  <data name="C8NOP" xml:space="preserve">
    <value>C8NOP</value>
  </data>
  <data name="C9" xml:space="preserve">
    <value>C9</value>
  </data>
  <data name="C9NOP" xml:space="preserve">
    <value>C9NOP</value>
  </data>
  <data name="calculateformula" xml:space="preserve">
    <value>ಫಾರ್ಮುಲಾ ಲೆಕ್ಕ</value>
  </data>
  <data name="Calendar" xml:space="preserve">
    <value>ಕ್ಯಾಲೆಂಡರ್</value>
  </data>
  <data name="CallBackDate" xml:space="preserve">
    <value>ದಿನಾಂಕ ಬ್ಯಾಕ್ ಕಾಲ್</value>
  </data>
  <data name="CallBackTime" xml:space="preserve">
    <value>ಬ್ಯಾಕ್ ಟೈಮ್ ಕಾಲ್</value>
  </data>
  <data name="CallClosureDate" xml:space="preserve">
    <value>ಕಾಲ್ ಸಮಾಪ್ತಿ ದಿನಾಂಕ</value>
  </data>
  <data name="CallClosureTime" xml:space="preserve">
    <value>ಸಮಾಪ್ತಿ ಸಮಯ ಕರೆ</value>
  </data>
  <data name="CallComplexity" xml:space="preserve">
    <value>ಸಂಕೀರ್ಣತೆ ಕಾಲ್</value>
  </data>
  <data name="CallDate" xml:space="preserve">
    <value>ದಿನಾಂಕ ಕಾಲ್</value>
  </data>
  <data name="CallDateCanNotBeGreaterThanCurrentDate" xml:space="preserve">
    <value>ದಿನಾಂಕ ಕಾಲ್ ಮತ್ತು ಸಮಯ ಪ್ರಸ್ತುತ ದಿನಾಂಕ ಮತ್ತು ಸಮಯವನ್ನು ಹೆಚ್ಚು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Calldatecannotbegreaterthancurrentdatetime" xml:space="preserve">
    <value>ಕಾಲ್ ದಿನಾಂಕ ಪ್ರಸ್ತುತ ದಿನಾಂಕ ಸಮಯ ಹೆಚ್ಚು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="CallDescription" xml:space="preserve">
    <value>ವಿವರಣೆ ಕಾಲ್</value>
  </data>
  <data name="CallDetails" xml:space="preserve">
    <value>ವಿವರಗಳು ಕಾಲ್</value>
  </data>
  <data name="CallHistory" xml:space="preserve">
    <value>ಇತಿಹಾಸ ಕಾಲ್</value>
  </data>
  <data name="CallMode" xml:space="preserve">
    <value>ಕಾಲ್ ಮೋಡ್</value>
  </data>
  <data name="CallNature" xml:space="preserve">
    <value>ಕಾಲ್ ಪ್ರಕೃತಿ</value>
  </data>
  <data name="CallPriority" xml:space="preserve">
    <value>ಆದ್ಯತಾ ಕಾಲ್</value>
  </data>
  <data name="CallStatus" xml:space="preserve">
    <value>ಕಾಲ್ ಸ್ಥಿತಿ</value>
  </data>
  <data name="CallTime" xml:space="preserve">
    <value>ಸಮಯ ಕರೆ</value>
  </data>
  <data name="CallType" xml:space="preserve">
    <value>ರೀತಿಯ ಕರೆ</value>
  </data>
  <data name="Campaign" xml:space="preserve">
    <value>ದಂಡಯಾತ್ರೆ</value>
  </data>
  <data name="CampaignCode" xml:space="preserve">
    <value>ಕ್ಯಾಂಪೇನ್ ಕೋಡ್</value>
  </data>
  <data name="CampaignCode1" xml:space="preserve">
    <value>ಕ್ಯಾಂಪೇನ್ ಕೋಡ್</value>
  </data>
  <data name="CampaignHeader" xml:space="preserve">
    <value>ತಲೆಹೊಡೆತ</value>
  </data>
  <data name="CampaignName" xml:space="preserve">
    <value>ಕ್ಯಾಂಪೇನ್ ಹೆಸರು</value>
  </data>
  <data name="Campaignoperationcannotbedeleted" xml:space="preserve">
    <value>ಕ್ಯಾಂಪೇನ್ ಆಪರೇಷನ್ ಅಳಿಸಲಾಗಿಲ್ಲ</value>
  </data>
  <data name="Campaignpartcannotbedeleted" xml:space="preserve">
    <value>ಕ್ಯಾಂಪೇನ್ ಭಾಗ ಅಳಿಸಲಾಗಿಲ್ಲ</value>
  </data>
  <data name="CampaingMachineDetails" xml:space="preserve">
    <value>ಯಂತ್ರ ವಿವರಗಳು Campaing</value>
  </data>
  <data name="Campaingnserialnumber" xml:space="preserve">
    <value>ಕ್ರಮಸಂಖ್ಯೆ ಪ್ರಚಾರ</value>
  </data>
  <data name="CampaingOperationCode" xml:space="preserve">
    <value>ಕಾರ್ಯಾಚರಣೆ ಕೋಡ್</value>
  </data>
  <data name="CampaingOperationDetails" xml:space="preserve">
    <value>Campaing ಆಪರೇಷನ್ ವಿವರಗಳು</value>
  </data>
  <data name="CampaingPartNumber" xml:space="preserve">
    <value>ಭಾಗ</value>
  </data>
  <data name="CampaingPartsDetails" xml:space="preserve">
    <value>Campaing ಭಾಗಗಳು ವಿವರಗಳು</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>ರದ್ದು</value>
  </data>
  <data name="CancellationIsAlreadyDoneForTheSelectedInvoiceNumber" xml:space="preserve">
    <value>ರದ್ದತಿ ಈಗಾಗಲೇ ಆಯ್ಕೆ ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ ಮಾಡಲಾಗುತ್ತದೆ</value>
  </data>
  <data name="CancellationNumber" xml:space="preserve">
    <value>ರದ್ದತಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="CancellationQuantitycannotbegreaterthan" xml:space="preserve">
    <value>ರದ್ದತಿ ಪ್ರಮಾಣ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="CancelledQuantity" xml:space="preserve">
    <value>ರದ್ದುಗೊಳಿಸಲಾಗಿದೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="CancelledQuantityCannotbegreaterthan" xml:space="preserve">
    <value>ರದ್ದುಗೊಳಿಸಲಾಗಿದೆ ಪ್ರಮಾಣ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="CancelledReason" xml:space="preserve">
    <value>ರದ್ದುಗೊಳಿಸಲಾಗಿದೆ / ಪರಿತ್ಯಕ್ತ ಕಾರಣ</value>
  </data>
  <data name="cannotclosethejobwithoutproductdetails" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ವಿವರಗಳು ಇಲ್ಲದೆ ಕೆಲಸ ಮುಚ್ಚಿ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="CannotdeleteasTaxTypeisreferenced" xml:space="preserve">
    <value>ತೆರಿಗೆ ರೀತಿಯ ಉಲ್ಲೇಖವಿದೆ ಎಂದು ಅಳಿಸಲು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="CannotDeleteinEditMode" xml:space="preserve">
    <value>ಬದಲಾಯಿಸಿ ಕ್ರಮದಲ್ಲಿ ಅಳಿಸಲು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="cannotreallocatemorethandeallocated" xml:space="preserve">
    <value>ಹಂಚಿಕೆಯು ರದ್ದತಿಯಾಗುವುದಕ್ಕೆ ಹೆಚ್ಚು ಪುನರ್ವಿಂಗಡಿಸು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Cannotreducequantityalreadyreallocated" xml:space="preserve">
    <value>ಪ್ರಮಾಣ ಕಡಿಮೆಗೊಳಿಸಲು ಸಾಧ್ಯವಿಲ್ಲ, ಈಗಾಗಲೇ ಉಪಯೊಗಕ್ಕೆ</value>
  </data>
  <data name="CannotsaveGDRasfollowingpartsareblocked" xml:space="preserve">
    <value>ಕೆಳಗಿನ ಭಾಗ (ಗಳು) ಎಂದು GDR ಉಳಿಸಲು ಸಾಧ್ಯವಾಗಿಲ್ಲ ನಿರ್ಬಂಧಿಸಲಾಗುತ್ತದೆ</value>
  </data>
  <data name="cannotselectpartfromparentcompany" xml:space="preserve">
    <value>ಪೋಷಕ ಕಂಪನಿ ಭಾಗದಲ್ಲಿ ಆಯ್ಕೆ ಮಾಡಬಹುದು</value>
  </data>
  <data name="capslockison" xml:space="preserve">
    <value>CAPS LOCK ಆನ್ ಆಗಿದೆ</value>
  </data>
  <data name="CaseDetailCannotbeblank" xml:space="preserve">
    <value>ಕೇಸ್ ವಿವರ ಖಾಲಿ ಇರಕೂಡದು</value>
  </data>
  <data name="CaseDetails" xml:space="preserve">
    <value>ಕೇಸ್ ವಿವರಗಳು</value>
  </data>
  <data name="CaseDistributionChart" xml:space="preserve">
    <value>ಕೇಸ್ ವಿತರಣೆ ಚಾರ್ಟ್</value>
  </data>
  <data name="CaseHistoryReport" xml:space="preserve">
    <value>ಕೇಸ್ ಹಿಸ್ಟರಿ</value>
  </data>
  <data name="CaseNumber" xml:space="preserve">
    <value>ಕೇಸ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="CaseNumbercannotbezero" xml:space="preserve">
    <value>ಕೇಸ್ ಸಂಖ್ಯೆ ಶೂನ್ಯವಾಗಿರುವುದಿಲ್ಲ</value>
  </data>
  <data name="CaseProgress" xml:space="preserve">
    <value>ಕೇಸ್ ಪ್ರೋಗ್ರೆಸ್</value>
  </data>
  <data name="CaseProgressHistory" xml:space="preserve">
    <value>ಕೇಸ್ ಪ್ರೋಗ್ರೆಸ್ ಇತಿಹಾಸ</value>
  </data>
  <data name="CaseRegistration" xml:space="preserve">
    <value>ಕೇಸ್ ನೋಂದಣಿ</value>
  </data>
  <data name="CaseRegistrationCount" xml:space="preserve">
    <value>ಕೇಸ್ ನೋಂದಣಿ ನ್ಯಾಷನಲ್</value>
  </data>
  <data name="caseregistrationnumber" xml:space="preserve">
    <value>ಪ್ರಕರಣ ನೋಂದಣಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="CaseSummary" xml:space="preserve">
    <value>ಕೇಸ್ ಸಾರಾಂಶ</value>
  </data>
  <data name="CaseType" xml:space="preserve">
    <value>ಕೇಸ್ ಕೌಟುಂಬಿಕತೆ</value>
  </data>
  <data name="CaseWeight" xml:space="preserve">
    <value>ಕೇಸ್ ತೂಕ</value>
  </data>
  <data name="cat" xml:space="preserve">
    <value>ಸಿಎಟಿ</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>ವರ್ಗ</value>
  </data>
  <data name="CauseofFailure" xml:space="preserve">
    <value>ವೈಫಲ್ಯ ಕಾರಣ</value>
  </data>
  <data name="CausingPartDetails" xml:space="preserve">
    <value>ಭಾಗ ವಿವರ ಉಂಟುಮಾಡುವುದು</value>
  </data>
  <data name="CausingPartWiseReportFrom" xml:space="preserve">
    <value>ಗೆ ಭಾಗ ವೈಸ್ ವರದಿ ಉಂಟುಮಾಡುವುದು</value>
  </data>
  <data name="center" xml:space="preserve">
    <value>ಕೇಂದ್ರ</value>
  </data>
  <data name="ChangePassword" xml:space="preserve">
    <value>ಪಾಸ್ವರ್ಡ್ ಬದಲಿಸಿ</value>
  </data>
  <data name="Changeswillbelostdoyouwanttoproceed" xml:space="preserve">
    <value>ನೀವು ರದ್ದು ಬಯಸುತ್ತೀರೆ?</value>
  </data>
  <data name="Changeswillbelostdoyouwanttoproceed1" xml:space="preserve">
    <value>ಬದಲಾವಣೆಗಳು ಕಾಣೆಯಾಗುತ್ತವೆ ನೀವು ಮುಂದುವರಿಸಲು ಬಯಸುತ್ತೀರಾ</value>
  </data>
  <data name="ChargeTo" xml:space="preserve">
    <value>ಚಾರ್ಜ್</value>
  </data>
  <data name="ChargeToParty" xml:space="preserve">
    <value>ಪಕ್ಷದ ಚಾರ್ಜ್</value>
  </data>
  <data name="Checklist" xml:space="preserve">
    <value>ಪರಿಶೀಲನಾಪಟ್ಟಿ</value>
  </data>
  <data name="checklistdetail" xml:space="preserve">
    <value>ಪಟ್ಟಿ ವಿವರ ಪರಿಶೀಲಿಸಿ</value>
  </data>
  <data name="ChildTaxStructure" xml:space="preserve">
    <value>ಮಕ್ಕಳ ತೆರಿಗೆ ರಚನೆ</value>
  </data>
  <data name="ChooseColumnNames" xml:space="preserve">
    <value>ಕಾಲಮ್ ಹೆಸರುಗಳು ಆರಿಸಿ</value>
  </data>
  <data name="ClaimDate" xml:space="preserve">
    <value>ಹಕ್ಕು ದಿನಾಂಕ</value>
  </data>
  <data name="ClaimedAmount" xml:space="preserve">
    <value>ಹಕ್ಕು ವೊತ್ತ</value>
  </data>
  <data name="ClaimedAmountcannotbezero" xml:space="preserve">
    <value>ಹಕ್ಕು ವೊತ್ತ ಶೂನ್ಯ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ClaimHistory" xml:space="preserve">
    <value>ಹಕ್ಕು ಇತಿಹಾಸ</value>
  </data>
  <data name="ClaimisMandatory" xml:space="preserve">
    <value>ಹಕ್ಕು ಕಡ್ಡಾಯ</value>
  </data>
  <data name="ClaimNumber" xml:space="preserve">
    <value>ಕ್ಲೇಮ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ClaimStatus" xml:space="preserve">
    <value>ಹಕ್ಕು ಸ್ಥಿತಿ</value>
  </data>
  <data name="ClearanceCharge" xml:space="preserve">
    <value>ಕ್ಲಿಯರೆನ್ಸ್ ಚಾರ್ಜ್</value>
  </data>
  <data name="clearformula" xml:space="preserve">
    <value>ತೆರವುಗೊಳಿಸಿ ಫಾರ್ಮುಲಾ</value>
  </data>
  <data name="ClearingAgent" xml:space="preserve">
    <value>ಕ್ಲಿಯರಿಂಗ್ ಏಜೆಂಟ್</value>
  </data>
  <data name="ClearingAgentisLockedDoyouwanttocontinue" xml:space="preserve">
    <value>ಏಜೆಂಟ್ ತೆರವುಗೊಳಿಸುವುದು ಲಾಕ್ ನೀವು ಮುಂದುವರಿಸಲು ಬಯಸುತ್ತೀರಾ</value>
  </data>
  <data name="ClearingAgentName" xml:space="preserve">
    <value>ಕ್ಲಿಯರಿಂಗ್ ಏಜೆಂಟ್ ಹೆಸರು</value>
  </data>
  <data name="close" xml:space="preserve">
    <value>ಮುಚ್ಚಿ</value>
  </data>
  <data name="Closed" xml:space="preserve">
    <value>ಮುಚ್ಚಲಾಗಿದೆ</value>
  </data>
  <data name="ClosedCount" xml:space="preserve">
    <value>ಮುಚ್ಚಿದ ನ್ಯಾಷನಲ್</value>
  </data>
  <data name="ClosedDate" xml:space="preserve">
    <value>ಮುಚ್ಚಿದ ದಿನಾಂಕ</value>
  </data>
  <data name="ClosingInventoryatcostprice" xml:space="preserve">
    <value>ವೆಚ್ಚ ಬೆಲೆಗೆ ಇನ್ವೆಂಟರಿ ಕ್ಲೋಸಿಂಗ್</value>
  </data>
  <data name="ClosingStock" xml:space="preserve">
    <value>ಮುಚ್ಚುವ ಸ್ಟಾಕ್</value>
  </data>
  <data name="ClosureDate" xml:space="preserve">
    <value>ಸಮಾಪ್ತಿ ದಿನಾಂಕ</value>
  </data>
  <data name="ClosureDetails" xml:space="preserve">
    <value>ಸಮಾಪ್ತಿ ವಿವರಗಳು</value>
  </data>
  <data name="closurereason" xml:space="preserve">
    <value>ಸಮಾಪ್ತಿ ಕಾರಣ</value>
  </data>
  <data name="ClosureRemarks" xml:space="preserve">
    <value>ಸಮಾಪ್ತಿ ರಿಮಾರ್ಕ್ಸ್</value>
  </data>
  <data name="ClosureType" xml:space="preserve">
    <value>ಸಮಾಪ್ತಿ ಪ್ರಕಾರ</value>
  </data>
  <data name="code" xml:space="preserve">
    <value>ಕೋಡ್</value>
  </data>
  <data name="ColumnNames" xml:space="preserve">
    <value>ಕಾಲಮ್ ಹೆಸರುಗಳು</value>
  </data>
  <data name="CombinationAlreadyExists" xml:space="preserve">
    <value>ಸಂಯೋಜನೆಯನ್ನು ಈಗಾಗಲೇ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="commissioningdate" xml:space="preserve">
    <value>ದಿನಾಂಕ ನಿಯುಕ್ತಿ</value>
  </data>
  <data name="FOCCommittedValue" xml:space="preserve">
    <value>ಉಚಿತವಾಗಿ ಬದ್ಧವಾಗಿದೆ ಮೌಲ್ಯ</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>ಕಂಪನಿ</value>
  </data>
  <data name="CompanyBrands" xml:space="preserve">
    <value>ಕಂಪನಿ ಬ್ರಾಂಡ್ಸ್</value>
  </data>
  <data name="CompanyCalender" xml:space="preserve">
    <value>ಕಂಪನಿ ಕ್ಯಾಲೆಂಡರ್</value>
  </data>
  <data name="CompanyCalender1" xml:space="preserve">
    <value>ಕಂಪನಿ ಕ್ಯಾಲೆಂಡರ್</value>
  </data>
  <data name="CompanyEmployeeName" xml:space="preserve">
    <value>ಕಂಪನಿ ನೌಕರರ ಹೆಸರು</value>
  </data>
  <data name="companyemployeesearch" xml:space="preserve">
    <value>ಕಂಪನಿ ನೌಕರರ ಹುಡುಕು</value>
  </data>
  <data name="CompanyFinancialYear" xml:space="preserve">
    <value>ಕಂಪನಿ ಹಣಕಾಸು ವರ್ಷದ</value>
  </data>
  <data name="CompanyHeader" xml:space="preserve">
    <value>ತಲೆಹೊಡೆತ</value>
  </data>
  <data name="CompanyMaster" xml:space="preserve">
    <value>ಕಂಪನಿ ಮಾಸ್ಟರ್</value>
  </data>
  <data name="CompanyName" xml:space="preserve">
    <value>ಕಂಪನಿ ಹೆಸರು</value>
  </data>
  <data name="CompanyNameisalreadypresent" xml:space="preserve">
    <value>ಕಂಪನಿ ಹೆಸರು ಈಗಾಗಲೇ ಇರುತ್ತದೆ</value>
  </data>
  <data name="CompanyRelation" xml:space="preserve">
    <value>ಕಂಪನಿ ಕಂಪನಿ ನಂಟು</value>
  </data>
  <data name="CompanyRelationships" xml:space="preserve">
    <value>ಕಂಪನಿ ಸಂಬಂಧಗಳು</value>
  </data>
  <data name="CompanySavedPleaseAssociateatleastoneBrand" xml:space="preserve">
    <value>ಕಂಪನಿ ಹೆಡರ್ ಉಳಿಸಿದ ಯಶಸ್ಸು ಸಂಪೂರ್ಣವಾಗಿ, ಕನಿಷ್ಠ ಒಂದು ಬ್ರಾಂಡ್ ಸಂಯೋಜಿಸಲು ದಯವಿಟ್ಟು, ಶಾಖೆಗೆ ಒಂದು ಶಾಖೆ, ನೌಕರ ಮತ್ತು ಸಹಾಯಕ employeee ರಚಿಸಲು ದಯವಿಟ್ಟು</value>
  </data>
  <data name="CompanySavedPleaseAssociateatleastoneBrand1" xml:space="preserve">
    <value>ಕಂಪನಿ ಹೆಡರ್ ಯಶಸ್ವಿಯಾಗಿ ಉಳಿಸಲಾಗಿದೆ, ಕನಿಷ್ಠ ಒಂದು ಬ್ರಾಂಡ್ ಸಂಯೋಜಿಸಲು ದಯವಿಟ್ಟು, ಶಾಖೆ ಒಂದು ಶಾಖೆ, ನೌಕರರ ಮತ್ತು ಸಹಾಯಕ ನೌಕರರ ರಚಿಸಿ ದಯವಿಟ್ಟು</value>
  </data>
  <data name="CompanyTaxCode" xml:space="preserve">
    <value>ಕಂಪನಿ ತೆರಿಗೆ ಕೋಡ್</value>
  </data>
  <data name="CompanyTaxDetails" xml:space="preserve">
    <value>ಕಂಪನಿ ತೆರಿಗೆ ವಿವರಗಳು</value>
  </data>
  <data name="CompanyTerms" xml:space="preserve">
    <value>ಕಂಪನಿ ನಿಯಮಗಳು</value>
  </data>
  <data name="CompanyTheme" xml:space="preserve">
    <value>ಕಂಪನಿ ಥೀಮ್</value>
  </data>
  <data name="CompanyType" xml:space="preserve">
    <value>ಕಂಪನಿ ಪ್ರಕಾರ</value>
  </data>
  <data name="CompetitorBrand" xml:space="preserve">
    <value>ಪ್ರತಿಸ್ಪರ್ಧಿ ಬ್ರ್ಯಾಂಡ್</value>
  </data>
  <data name="CompetitorDetails" xml:space="preserve">
    <value>ಪ್ರತಿಸ್ಪರ್ಧಿ ವಿವರಗಳು</value>
  </data>
  <data name="CompetitorModel" xml:space="preserve">
    <value>ಪ್ರತಿಸ್ಪರ್ಧಿ ಮಾದರಿ</value>
  </data>
  <data name="CompetitorName" xml:space="preserve">
    <value>ಪ್ರತಿಸ್ಪರ್ಧಿ ಹೆಸರು</value>
  </data>
  <data name="CompetitorPrice" xml:space="preserve">
    <value>ಪ್ರತಿಸ್ಪರ್ಧಿ ಬೆಲೆ</value>
  </data>
  <data name="CompetitorProductType" xml:space="preserve">
    <value>ಪ್ರತಿಸ್ಪರ್ಧಿ ಉತ್ಪನ್ನ ಪ್ರಕಾರ</value>
  </data>
  <data name="Completed" xml:space="preserve">
    <value>ಪೂರ್ಣಗೊಂಡಿದೆ</value>
  </data>
  <data name="CompletedCount" xml:space="preserve">
    <value>ಪೂರ್ಣಗೊಂಡಿದೆ ನ್ಯಾಷನಲ್</value>
  </data>
  <data name="CompleteEnteringDetail" xml:space="preserve">
    <value>ಕಂಪ್ಲೀಟ್ ದಾಖಲಿಸುವ ವಿವರ</value>
  </data>
  <data name="CompleteEnteringDetailParts" xml:space="preserve">
    <value>ಕಂಪ್ಲೀಟ್ ದಾಖಲಿಸುವ ಭಾಗಗಳು ವಿವರ</value>
  </data>
  <data name="CompleteEnteringDetailService" xml:space="preserve">
    <value>ಕಂಪ್ಲೀಟ್ ದಾಖಲಿಸುವ ಸೇವೆ ವಿವರ</value>
  </data>
  <data name="CompleteEnteringDetailSundry" xml:space="preserve">
    <value>ಕಂಪ್ಲೀಟ್ ಬಗೆಬಗೆಯ ವಿವರ ಪ್ರವೇಶಿಸುವ</value>
  </data>
  <data name="Complexity" xml:space="preserve">
    <value>ಸಂಕೀರ್ಣತೆ</value>
  </data>
  <data name="componentdetail" xml:space="preserve">
    <value>ಕಾಂಪೊನೆಂಟ್ ವಿವರ</value>
  </data>
  <data name="ComponentReading" xml:space="preserve">
    <value>ಕಾಂಪೊನೆಂಟ್ ಓದುವಿಕೆ</value>
  </data>
  <data name="componentreadingdetails" xml:space="preserve">
    <value>ಕಾಂಪೊನೆಂಟ್ ಓದುವಿಕೆ ವಿವರ</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>ದೃಢೀಕರಿಸಿ</value>
  </data>
  <data name="Confirmationdate" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ದೃಢೀಕರಣ ದಿನಾಂಕ</value>
  </data>
  <data name="ConfirmedBy" xml:space="preserve">
    <value>ದೃಢಪಡಿಸಿದರು</value>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>ಪಾಸ್ವರ್ಡ್ ದೃಢೀಕರಿಸಿ</value>
  </data>
  <data name="Consignee" xml:space="preserve">
    <value>ಸಾಗಿಸಿದ</value>
  </data>
  <data name="ConsigneeAddress" xml:space="preserve">
    <value>ಸಾಗಿಸಿದ ವಿಳಾಸ</value>
  </data>
  <data name="ConsigneeLocation" xml:space="preserve">
    <value>ಸಾಗಿಸಿದ ಸ್ಥಳ</value>
  </data>
  <data name="ConsumptionCode" xml:space="preserve">
    <value>ಬಳಕೆ ಕೋಡ್</value>
  </data>
  <data name="ConsumptionPattern" xml:space="preserve">
    <value>ಬಳಕೆಯ ವಿಧಾನವನ್ನು</value>
  </data>
  <data name="ConsumptionValue" xml:space="preserve">
    <value>ಬಳಕೆ ಮೌಲ್ಯ</value>
  </data>
  <data name="ContactPerson" xml:space="preserve">
    <value>ವ್ಯಕ್ತಿ ಸಂಪರ್ಕ</value>
  </data>
  <data name="ContactPersonMobileNumber" xml:space="preserve">
    <value>ವ್ಯಕ್ತಿ ಸಂಪರ್ಕ ಮೊಬೈಲ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ContactPersons" xml:space="preserve">
    <value>ಸಂಪರ್ಕಿಸಬೇಕಾದ ವ್ಯಕ್ತಿಗಳು</value>
  </data>
  <data name="ConversionFactor" xml:space="preserve">
    <value>ಪರಿವರ್ತಿಸುವುದರ</value>
  </data>
  <data name="ConvertionPercentage" xml:space="preserve">
    <value>ಪರಿವರ್ತನೆ %</value>
  </data>
  <data name="CopyRoleFrom" xml:space="preserve">
    <value>ನಕಲು ಪಾತ್ರ</value>
  </data>
  <data name="CoreDetail" xml:space="preserve">
    <value>ಕೋರ್ ವಿವರ</value>
  </data>
  <data name="CoreDetails" xml:space="preserve">
    <value>ಕೋರ್ ವಿವರಗಳು</value>
  </data>
  <data name="CoreEstimation" xml:space="preserve">
    <value>ಕೋರ್ ಎಸ್ಟಿಮೇಶನ್</value>
  </data>
  <data name="CoreGRN" xml:space="preserve">
    <value>ಕೋರ್ ಜಿ ಆರ್ ಎನ್</value>
  </data>
  <data name="CoreInspection" xml:space="preserve">
    <value>ಕೋರ್ ಇನ್ಸ್ಪೆಕ್ಷನ್</value>
  </data>
  <data name="CoreInspectionNumber" xml:space="preserve">
    <value>ಕೋರ್ ಇನ್ಸ್ಪೆಕ್ಷನ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="CorePartDescription" xml:space="preserve">
    <value>ತಿರುಳಾಗಿದೆ ವಿವರಣೆ</value>
  </data>
  <data name="CorePartDetails" xml:space="preserve">
    <value>ತಿರುಳಾಗಿದೆ ವಿವರಗಳು</value>
  </data>
  <data name="CorePartNumber" xml:space="preserve">
    <value>ಪ್ರಧಾನ ಪಾತ್ರ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="CorePartsDetail" xml:space="preserve">
    <value>ಕೋರ್ ಭಾಗಗಳಲ್ಲಿ ವಿವರ</value>
  </data>
  <data name="CoreReceipt" xml:space="preserve">
    <value>ಕೋರ್ ರಸೀತಿ</value>
  </data>
  <data name="CoreType" xml:space="preserve">
    <value>ಕೋರ್ ಪ್ರಕಾರ</value>
  </data>
  <data name="CoreValue" xml:space="preserve">
    <value>ಕೋರ್ ಮೌಲ್ಯ</value>
  </data>
  <data name="CorrectiveAction" xml:space="preserve">
    <value>ಸರಿಪಡಿಸುವ ಕ್ರಮ</value>
  </data>
  <data name="Cost" xml:space="preserve">
    <value>ಬೆಲೆ</value>
  </data>
  <data name="Cost1" xml:space="preserve">
    <value>ಬೆಲೆ</value>
  </data>
  <data name="CostCenter" xml:space="preserve">
    <value>ವೆಚ್ಚ ಸೆಂಟರ್</value>
  </data>
  <data name="CostCenterDescription" xml:space="preserve">
    <value>ವೆಚ್ಚ ಸೆಂಟರ್ ವಿವರಣೆ</value>
  </data>
  <data name="Costing" xml:space="preserve">
    <value>ಕಾಸ್ಟಿಂಗ್</value>
  </data>
  <data name="CostingDetails" xml:space="preserve">
    <value>ವಿವರಗಳು ಕಾಸ್ಟಿಂಗ್</value>
  </data>
  <data name="CostingType" xml:space="preserve">
    <value>ಟೈಪ್ ಕಾಸ್ಟಿಂಗ್</value>
  </data>
  <data name="CostPrice" xml:space="preserve">
    <value>ಮೂಲ ಬೆಲೆ</value>
  </data>
  <data name="CostType" xml:space="preserve">
    <value>ವೆಚ್ಚ ಪ್ರಕಾರ</value>
  </data>
  <data name="Count" xml:space="preserve">
    <value>ಎಣಿಕೆ</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>ದೇಶ</value>
  </data>
  <data name="CouponNumber" xml:space="preserve">
    <value>ಕೂಪನ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="CRCount" xml:space="preserve">
    <value>ಕೇಸ್ ನೋಂದಣಿ ನ್ಯಾಷನಲ್</value>
  </data>
  <data name="create" xml:space="preserve">
    <value>ರಚಿಸಿ</value>
  </data>
  <data name="Created" xml:space="preserve">
    <value>ರಚಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="CreatedBy" xml:space="preserve">
    <value>ದಾಖಲಿಸಿದವರು</value>
  </data>
  <data name="CreatedDate" xml:space="preserve">
    <value>ದಿನಾಂಕ ರಚಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="CreateInvoice" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ರಚಿಸಿ</value>
  </data>
  <data name="CreateJobCard" xml:space="preserve">
    <value>ಉದ್ಯೋಗ ಚೀಟಿ ರಚಿಸಿ</value>
  </data>
  <data name="CreateNew" xml:space="preserve">
    <value>ಹೊಸ ರಚಿಸಿ</value>
  </data>
  <data name="CreateOrder" xml:space="preserve">
    <value>ರಚಿಸಿ ಆರ್ಡರ್</value>
  </data>
  <data name="CreateQuotation" xml:space="preserve">
    <value>ಉದ್ಧರಣ ರಚಿಸಿ</value>
  </data>
  <data name="CreateServiceRequest" xml:space="preserve">
    <value>ಸೇವೆ ವಿನಂತಿಯನ್ನು ರಚಿಸಿ</value>
  </data>
  <data name="CreditDaysPassedFor" xml:space="preserve">
    <value>ಕ್ರೆಡಿಟ್ ಡೇಸ್ ಜಾರಿಗೆ</value>
  </data>
  <data name="CreditDebitNote" xml:space="preserve">
    <value>ಕ್ರೆಡಿಟ್ ಡೆಬಿಟ್ ಚೀಟಿ</value>
  </data>
  <data name="CreditENote" xml:space="preserve">
    <value>ಕ್ರೆಡಿಟ್ ಸೂಚನೆ</value>
  </data>
  <data name="CreditLimitExceededDoyouwanttocontinue" xml:space="preserve">
    <value>ಕ್ರೆಡಿಟ್ ಮಿತಿ ಮೀರಿದೆ.</value>
  </data>
  <data name="Creditlimitispassedfortheparty" xml:space="preserve">
    <value>ಕ್ರೆಡಿಟ್ ಮಿತಿಯನ್ನು ಪಕ್ಷಕ್ಕೆ ರವಾನಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="CreditNote" xml:space="preserve">
    <value>ಕ್ರೆಡಿಟ್ ಗಮನಿಸಿ</value>
  </data>
  <data name="CreditNoteAmount" xml:space="preserve">
    <value>ಕ್ರೆಡಿಟ್ ಗಮನಿಸಿ ವೊತ್ತ</value>
  </data>
  <data name="CreditNoteDetails" xml:space="preserve">
    <value>ಕ್ರೆಡಿಟ್ ಗಮನಿಸಿ ವಿವರಗಳು</value>
  </data>
  <data name="CreditNoteNumber" xml:space="preserve">
    <value>ಕ್ರೆಡಿಟ್ ಗಮನಿಸಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="CreditOrDebitNoteNumber" xml:space="preserve">
    <value>ಕ್ರೆಡಿಟ್ ಅಥವಾ ಡೆಬಿಟ್ ಗಮನಿಸಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="Credits" xml:space="preserve">
    <value>ಕ್ರೆಡಿಟ್ಸ್</value>
  </data>
  <data name="crictical" xml:space="preserve">
    <value>ಕ್ರಿಟಿಕಲ್</value>
  </data>
  <data name="Critical" xml:space="preserve">
    <value>ಕ್ರಿಟಿಕಲ್</value>
  </data>
  <data name="CSANumber" xml:space="preserve">
    <value>ಸಿ ಎಸ್ ಎ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="CSARevenueReport" xml:space="preserve">
    <value>ಸಿಎಸ್ಎ ಕಂದಾಯ ವರದಿ</value>
  </data>
  <data name="CSAWiseAndNonSalesReport" xml:space="preserve">
    <value>ಸಿಎಸ್ಎ ವೈಸ್ ಮತ್ತು ಮಾರಾಟದ ವರದಿ</value>
  </data>
  <data name="CumilativePick" xml:space="preserve">
    <value>Cumilative ಪಿಕ್</value>
  </data>
  <data name="CumulativeConsumption" xml:space="preserve">
    <value>ಸಂಚಿತ ಬಳಕೆ%</value>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>ಕರೆನ್ಸಿ</value>
  </data>
  <data name="CurrentAssigning" xml:space="preserve">
    <value>ಅಸೈನೀ</value>
  </data>
  <data name="CurrentFloatLocation" xml:space="preserve">
    <value>ಪ್ರಸ್ತುತ ಫ್ಲೋಟ್ ಸ್ಥಳ</value>
  </data>
  <data name="Currentlocationcannotbesameasnewlocation" xml:space="preserve">
    <value>ಪ್ರಸ್ತುತ ಸ್ಥಳ ಹೊಸ ಸ್ಥಳ ಅದೇ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="CurrentPartDetails" xml:space="preserve">
    <value>ಪ್ರಸ್ತುತ ಭಾಗ ವಿವರಗಳು</value>
  </data>
  <data name="CurrentReading" xml:space="preserve">
    <value>ಪ್ರಸ್ತುತ ಓದುವಿಕೆ</value>
  </data>
  <data name="CurrentSiteAddress" xml:space="preserve">
    <value>ಪ್ರಸ್ತುತ ಸೈಟ್ ವಿಳಾಸ</value>
  </data>
  <data name="CurrentStep" xml:space="preserve">
    <value>ಪ್ರಸ್ತುತ ಹಂತ</value>
  </data>
  <data name="CurrentStockQty" xml:space="preserve">
    <value>ಪ್ರಸ್ತುತ ಸ್ಟಾಕ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="CurrentStockstatement" xml:space="preserve">
    <value>ಪ್ರಸ್ತುತ ಸ್ಟಾಕ್ ಹೇಳಿಕೆ</value>
  </data>
  <data name="CurrentValue" xml:space="preserve">
    <value>ಪ್ರಸ್ತುತ ಮೌಲ್ಯ</value>
  </data>
  <data name="CurrentWAC" xml:space="preserve">
    <value>ಪ್ರಸ್ತುತ ಡಬ್ಲುಎಸಿ</value>
  </data>
  <data name="CustomDuty" xml:space="preserve">
    <value>ಕಸ್ಟಮ್ ಡ್ಯೂಟಿ</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>ಗಿರಾಕಿ</value>
  </data>
  <data name="CustomerComplaint" xml:space="preserve">
    <value>ಗ್ರಾಹಕರ ದೂರು</value>
  </data>
  <data name="CustomerContact" xml:space="preserve">
    <value>ವ್ಯಕ್ತಿ ಸಂಪರ್ಕ</value>
  </data>
  <data name="CustomerContact1" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ಸಂಪರ್ಕ</value>
  </data>
  <data name="CustomerContactPhone" xml:space="preserve">
    <value>ವ್ಯಕ್ತಿ ಸಂಪರ್ಕ ಮೊಬೈಲ್</value>
  </data>
  <data name="CustomerContactPhone1" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ಸಂಪರ್ಕ ದೂರವಾಣಿ</value>
  </data>
  <data name="customerdetails" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ವಿವರಗಳು</value>
  </data>
  <data name="customerdueservices" xml:space="preserve">
    <value>ಸೇವೆ ಕಾರಣ ಗ್ರಾಹಕ</value>
  </data>
  <data name="customerdueservices1" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ಕಾರಣ ಸೇವೆಗಳು</value>
  </data>
  <data name="CustomerisLockedDoyouwanttocontinue" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ಲಾಕ್, ನೀವು ಮುಂದುವರಿಸಲು ಬಯಸುತ್ತೀರಾ?</value>
  </data>
  <data name="Customerisnotactive" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ಸಕ್ರಿಯವಾಗಿಲ್ಲ</value>
  </data>
  <data name="CustomerLocation" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ಸ್ಥಳ</value>
  </data>
  <data name="customername" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ಹೆಸರು</value>
  </data>
  <data name="CustomerNameorBranch" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ಹೆಸರು / ಶಾಖೆ</value>
  </data>
  <data name="CustomerNotFound" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ಕಂಡುಬಂದಿಲ್ಲ</value>
  </data>
  <data name="customerorderclass" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ಆರ್ಡರ್ ವರ್ಗ</value>
  </data>
  <data name="CustomerProvided" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ಒದಗಿಸಿದ</value>
  </data>
  <data name="CustomerProvidedCannotBeGreaterThanRequestedQuantity" xml:space="preserve">
    <value>ಒದಗಿಸಿದ ಗ್ರಾಹಕ ವಿನಂತಿಸಲಾಗಿದೆ ಪ್ರಮಾಣ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="CustomerProvidedCannotBeGreaterThanRequiredQuantity" xml:space="preserve">
    <value>ಒದಗಿಸಿದ ಗ್ರಾಹಕ ಅವಶ್ಯವಿರುವ ಪ್ರಮಾಣವನ್ನು ಹೆಚ್ಚು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Customerprovidedcannotbegreaterthansumofquantityrequiredandquantityrequested" xml:space="preserve">
    <value>ಒದಗಿಸಿದ ಗ್ರಾಹಕ ಅವಶ್ಯವಿರುವ ಪ್ರಮಾಣವನ್ನು ಮೊತ್ತವು ಮತ್ತು ವಿನಂತಿಸಿದ ಪ್ರಮಾಣ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="CustomerQuotation" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ನುಡಿಮುತ್ತುಗಳು</value>
  </data>
  <data name="CustomerQuotation1" xml:space="preserve">
    <value>ಉದ್ಧರಣ</value>
  </data>
  <data name="CustomerQuotationArchived" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ಉದ್ಧರಣ ಆರ್ಕೈವ್</value>
  </data>
  <data name="CustomerQuotationDate" xml:space="preserve">
    <value>ಉಲ್ಲೇಖ ದಿನಾಂಕ</value>
  </data>
  <data name="CustomerQuotationNumber" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ಉದ್ಧರಣ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="CustomerQuotationSummary" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ಉದ್ಧರಣ ಸಾರಾಂಶ</value>
  </data>
  <data name="CustomerRating" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ರೇಟಿಂಗ್</value>
  </data>
  <data name="CustomerReferenceDate" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ರೆಫರೆನ್ಸ್ ದಿನಾಂಕ</value>
  </data>
  <data name="CustomerReferenceNumber" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ಉಲ್ಲೇಖ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="customersearch" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ಹುಡುಕು</value>
  </data>
  <data name="CustomerServiceHistory" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ಸೇವೆ ಇತಿಹಾಸ</value>
  </data>
  <data name="customerspecific" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ನಿಶ್ಚಿತ</value>
  </data>
  <data name="CustomerWithDetailsAlreadyExistsCombinationOfNameMobileandEmailShouldbeUnique" xml:space="preserve">
    <value>ಈ ವಿವರಗಳನ್ನು ಗ್ರಾಹಕ ಈಗಾಗಲೇ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ. \ ಹೆಸರು, ಮೊಬೈಲ್ ಮತ್ತು ಇಮೇಲ್ nCombination ಅನನ್ಯ ಇರಬೇಕು</value>
  </data>
  <data name="customscode" xml:space="preserve">
    <value>ಕಸ್ಟಮ್ಸ್ ಕೋಡ್</value>
  </data>
  <data name="CustomsDocReference" xml:space="preserve">
    <value>ಕಸ್ಟಮ್ಸ್ ಡಾಕ್.</value>
  </data>
  <data name="Daily" xml:space="preserve">
    <value>ಪ್ರತಿದಿನ</value>
  </data>
  <data name="DailyBillSettlementReportFrom" xml:space="preserve">
    <value> ಬಿಲ್ ಸೆಟ್ಲ್ಮೆಂಟ್ ವರದಿ</value>
  </data>
  <data name="DailyDebitNoteForClaim" xml:space="preserve">
    <value>ವಾದಕ್ಕೆ ಡೈಲಿ ಡೆಬಿಟ್ ಚೀಟಿಯನ್ನು</value>
  </data>
  <data name="DailyDebitNoteForJobCardReport" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ರಿಪೋರ್ಟ್ ದೈನಂದಿನ ಡೆಬಿಟ್ ಚೀಟಿಯನ್ನು</value>
  </data>
  <data name="DailyJobCardBillingReportFrom" xml:space="preserve">
    <value>ದೈನಂದಿನ JobCard ಬಿಲ್ಲಿಂಗ್ ವರದಿ</value>
  </data>
  <data name="DailyMonthlyReport" xml:space="preserve">
    <value>ಡೈಲಿ ಮಾಸಿಕ ವರದಿ</value>
  </data>
  <data name="DailyWarrantyCertificate" xml:space="preserve">
    <value>ಡೈಲಿ ಖಾತರಿ ಪ್ರಮಾಣಪತ್ರ</value>
  </data>
  <data name="DailyWarrantyClaimReportFrom" xml:space="preserve">
    <value>ದೈನಂದಿನ ಖಾತರಿ ಹಕ್ಕು ವರದಿ</value>
  </data>
  <data name="DamagedQuantity" xml:space="preserve">
    <value>ಹಾಳಾದ ಪ್ರಮಾಣ</value>
  </data>
  <data name="DamagedQuantitycannotbegreaterthanReceivedQuantity" xml:space="preserve">
    <value>ಹಾಳಾದ ಪ್ರಮಾಣ ಸ್ವೀಕರಿಸಿದ ಪ್ರಮಾಣ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="DamageQuantity" xml:space="preserve">
    <value>ಹಾನಿ ಪ್ರಮಾಣ</value>
  </data>
  <data name="DataSavedSuccessfully" xml:space="preserve">
    <value>ಡೇಟಾ ಯಶಸ್ವಿಯಾಗಿ ಉಳಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>ದಿನಾಂಕ</value>
  </data>
  <data name="DateandTime" xml:space="preserve">
    <value>ದಿನಾಂಕ ಮತ್ತು ಸಮಯ</value>
  </data>
  <data name="datecannotbegreaterthanCurrentdate" xml:space="preserve">
    <value>ದಿನಾಂಕ ಪ್ರಸ್ತುತ ದಿನಾಂಕ ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Datecannotbegreaterthanserviceagreementtodate" xml:space="preserve">
    <value>ಕಾರಣ ದಿನಾಂಕ ಇಲ್ಲಿಯವರೆಗೆ ಸೇವೆ ಒಪ್ಪಂದಕ್ಕೆ ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="DatecannotbeGreaterthen" xml:space="preserve">
    <value>ದಿನಾಂಕ ನಂತರ ಗ್ರೇಟರ್ ಸಾಧ್ಯವಿಲ್ಲ:</value>
  </data>
  <data name="datecannotbelessthanCurrentdate" xml:space="preserve">
    <value>ದಿನಾಂಕ ಪ್ರಸ್ತುತ ದಿನಾಂಕ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Datecannotbelessthanserviceagreementfromdate" xml:space="preserve">
    <value>ಕಾರಣ ದಿನಾಂಕ ದಿನಾಂಕದಿಂದ ಸೇವೆ ಒಪ್ಪಂದದ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="DateCannotbelessthenCurrentDate" xml:space="preserve">
    <value>ದಿನಾಂಕದಿಂದ ಪರಿಣಾಮಕಾರಿ ಪ್ರಸ್ತುತ ದಿನಾಂಕ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Datecannotbelessthenpreviousdate" xml:space="preserve">
    <value>ದಿನಾಂಕ ನಂತರ ಕಡಿಮೆ ಅಥವಾ ಹಿಂದಿನ ದಾಖಲೆಗಳ ದಿನಾಂಕ ಸಮಾನವಾಗಿರಬೇಕು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="DateOfFailure" xml:space="preserve">
    <value>ವೈಫಲ್ಯದ ದಿನಾಂಕ</value>
  </data>
  <data name="DateOfManufacture" xml:space="preserve">
    <value>ಉತ್ಪಾದನೆ ದಿನಾಂಕ</value>
  </data>
  <data name="DateOfRepair" xml:space="preserve">
    <value>ದುರಸ್ತಿ ದಿನಾಂಕ</value>
  </data>
  <data name="dateselectedmustbegreaterthenpreviouscustomer" xml:space="preserve">
    <value>ಆಯ್ಕೆ ದಿನಾಂಕ ಹಿಂದಿನ ಗ್ರಾಹಕ ಹೆಚ್ಚು ಇರಬೇಕು</value>
  </data>
  <data name="DateshouldbelessthanLastEffectivefromofsamesupplier" xml:space="preserve">
    <value>ದಿನಾಂಕ ಅದೇ ಪೂರೈಕೆದಾರ ದಿನಾಂಕದಿಂದ ಕಡಿಮೆ ಅಥವಾ ಕಳೆದ ಪರಿಣಾಮಕಾರಿ ಸಮಾನವಾಗಿರುತ್ತದೆ ಇರುವಂತಿಲ್ಲ</value>
  </data>
  <data name="DaysLeft" xml:space="preserve">
    <value>ಎಡ ಡೇಸ್</value>
  </data>
  <data name="DCType" xml:space="preserve">
    <value>ಡಿಸಿ ಪ್ರಕಾರ</value>
  </data>
  <data name="Dealer" xml:space="preserve">
    <value>ವ್ಯಾಪಾರಿ</value>
  </data>
  <data name="DealerName" xml:space="preserve">
    <value>ಡೀಲರ್ ಹೆಸರು</value>
  </data>
  <data name="DealersorCompany" xml:space="preserve">
    <value>ಡೀಲರ್ಸ್ / ಕಂಪನಿ</value>
  </data>
  <data name="DeallocatedQtycannotbegraterthanAllocatedQty" xml:space="preserve">
    <value>ಹಂಚಿಕೆಯು ರದ್ದತಿಯಾಗುವುದಕ್ಕೆ ಪ್ರಮಾಣ ಹಂಚಿಕೆ ಪ್ರಮಾಣ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="DeallocatedQuantity" xml:space="preserve">
    <value>ಹಂಚಿಕೆಯು ರದ್ದತಿಯಾಗುವುದಕ್ಕೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="DeAllocateOrdesrtoReAllocate" xml:space="preserve">
    <value>ಸಲುವಾಗಿ ಮರು ನಿಯೋಜಿಸಿ ಡಿ ನಿಯೋಜಿಸಿ</value>
  </data>
  <data name="DeallocationNumber" xml:space="preserve">
    <value>ಡಿ ಹಂಚಿಕೆ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="DeAllocationReRunAllocation" xml:space="preserve">
    <value>ಡಿ ಹಂಚಿಕೆ</value>
  </data>
  <data name="DebitENote" xml:space="preserve">
    <value>DebitNote</value>
  </data>
  <data name="DebitNote" xml:space="preserve">
    <value>ಡೆಬಿಟ್ ಚೀಟಿಯನ್ನು</value>
  </data>
  <data name="DebitNoteAmount" xml:space="preserve">
    <value>ಡೆಬಿಟ್ ಚೀಟಿಯನ್ನು ವೊತ್ತ</value>
  </data>
  <data name="DebitNoteDetails" xml:space="preserve">
    <value>ಡೆಬಿಟ್ ಚೀಟಿಯನ್ನು ವಿವರಗಳು</value>
  </data>
  <data name="DebitNoteNumber" xml:space="preserve">
    <value>ಡೆಬಿಟ್ ಚೀಟಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="DeBonding" xml:space="preserve">
    <value>ಡಿ ಬಂಧನ</value>
  </data>
  <data name="DeBondingDate" xml:space="preserve">
    <value>ದಿನಾಂಕ DeBonding</value>
  </data>
  <data name="December" xml:space="preserve">
    <value>ಡಿಸೆಂಬರ್</value>
  </data>
  <data name="Deduction" xml:space="preserve">
    <value>ವ್ಯವಕಲನ</value>
  </data>
  <data name="DeductionCannotbegreaterthanCoreValue" xml:space="preserve">
    <value>ಕಳೆಯುವುದು ಕೋರ್ ಮೌಲ್ಯ ಹೆಚ್ಚಾಗಿದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="DefaultGridSize" xml:space="preserve">
    <value>ಡೀಫಾಲ್ಟ್ ಗ್ರಿಡ್ ಗಾತ್ರ</value>
  </data>
  <data name="Defaultgridsizealreadyavailable" xml:space="preserve">
    <value>ಈಗಾಗಲೇ ಲಭ್ಯವಿರುವ ಡೀಫಾಲ್ಟ್ ಗ್ರಿಡ್ ಗಾತ್ರ</value>
  </data>
  <data name="DefaultgridSizeshouldbebetweenzeroandtwofiftyfive" xml:space="preserve">
    <value>ಡೀಫಾಲ್ಟ್ ಗ್ರಿಡ್ ಗಾತ್ರ 0 ಮತ್ತು 255 ರ ನಡುವಿನ ಇರಬೇಕು</value>
  </data>
  <data name="Defaultrecordcannotbedeleted" xml:space="preserve">
    <value>ಡೀಫಾಲ್ಟ್ ದಾಖಲೆ ಅಳಿಸಲಾಗಿದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="DefectCode" xml:space="preserve">
    <value>ದೋಷ ಕೋಡ್</value>
  </data>
  <data name="DefectGroup" xml:space="preserve">
    <value>ದೋಷ ಗುಂಪು</value>
  </data>
  <data name="DefectGroupNotActive" xml:space="preserve">
    <value>ದೋಷ ಕೋಡ್ ಸಕ್ರಿಯವಾಗಿಲ್ಲ</value>
  </data>
  <data name="DefectGroupSearch" xml:space="preserve">
    <value>ದೋಷ ಗುಂಪು ಹುಡುಕಾಟ</value>
  </data>
  <data name="DefectiveGroupName" xml:space="preserve">
    <value>ದೋಷಯುಕ್ತ ಗುಂಪು ಹೆಸರು</value>
  </data>
  <data name="DefectiveGroupWiseClaimReport" xml:space="preserve">
    <value>ದೋಷಯುಕ್ತ ಗ್ರೂಪ್ ವೈಸ್ ಹಕ್ಕು ವರದಿ</value>
  </data>
  <data name="DefectiveGroupWiseWarrantyClaimReportFrom" xml:space="preserve">
    <value>ದೋಷಪೂರಿತವಾದ ಗ್ರೂಪ್ ವೈಸ್ ಖಾತರಿ ಹಕ್ಕು ವರದಿ</value>
  </data>
  <data name="DefectivePartsDisposal" xml:space="preserve">
    <value>ದೋಷಯುಕ್ತ ಭಾಗಗಳು ವಿಲೇವಾರಿ</value>
  </data>
  <data name="DefectiveQuantity" xml:space="preserve">
    <value>ದೋಷಯುಕ್ತ ಪ್ರಮಾಣ</value>
  </data>
  <data name="DefectiveQuantityCannotbegreaterthanfailedstock" xml:space="preserve">
    <value>ದೋಷಯುಕ್ತ ಪ್ರಮಾಣ ವಿಫಲವಾಗಿದೆ ಸ್ಟಾಕ್ ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="DefectName" xml:space="preserve">
    <value>ದೋಷ ಹೆಸರು</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteAction" xml:space="preserve">
    <value>ಆಕ್ಷನ್ ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteArticle" xml:space="preserve">
    <value>ಲೇಖನ ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteAttachment" xml:space="preserve">
    <value>ಬಾಂಧವ್ಯ ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteBranch" xml:space="preserve">
    <value>ಶಾಖೆ ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteBranchTaxDetails" xml:space="preserve">
    <value>ಶಾಖೆ ತೆರಿಗೆ ವಿವರಗಳು ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteBrands" xml:space="preserve">
    <value>ಬ್ರಾಂಡ್ಸ್ ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteCompany" xml:space="preserve">
    <value>ಕಂಪನಿ ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteCompanyRelation" xml:space="preserve">
    <value>ಕಂಪನಿ ನಂಟು ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteCompanyTerms" xml:space="preserve">
    <value>ಕಂಪನಿ ನಿಯಮಗಳು ಅಳಿಸಿ</value>
  </data>
  <data name="deletedsuccessfully" xml:space="preserve">
    <value>ಯಶಸ್ವಿಯಾಗಿ ಅಳಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="DeleteEmployee" xml:space="preserve">
    <value>ನೌಕರರ ಅಳಿಸಿ</value>
  </data>
  <data name="deletefreestock" xml:space="preserve">
    <value>ಭಾಗ ಉಚಿತ ಸ್ಟಾಕ್ ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteFunctionGroup" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್ ಗುಂಪು ಅಳಿಸು</value>
  </data>
  <data name="DeleteJobCard" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteMaster" xml:space="preserve">
    <value>ಮಾಸ್ಟರ್ ಅಳಿಸಿ</value>
  </data>
  <data name="deletemodel" xml:space="preserve">
    <value>ಮಾದರಿ ಅಳಿಸಿ</value>
  </data>
  <data name="deleteoperation" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteOperationEmployeeDetails" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ನೌಕರರ ವಿವರಗಳು ಅಳಿಸಿ</value>
  </data>
  <data name="deletepart" xml:space="preserve">
    <value>ಭಾಗ ಅಳಿಸಿ</value>
  </data>
  <data name="deletepartprice" xml:space="preserve">
    <value>ಭಾಗ ಬೆಲೆ ಅಳಿಸಿ</value>
  </data>
  <data name="deletepartproducttype" xml:space="preserve">
    <value>ಭಾಗ ಉತ್ಪನ್ನ ಪ್ರಕಾರ ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteParts" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteParty" xml:space="preserve">
    <value>ಪಕ್ಷದ ಅಳಿಸಿ</value>
  </data>
  <data name="DeletePrefixSuffix" xml:space="preserve">
    <value>ಪೂರ್ವ ಪ್ರತ್ಯಯ ಅಳಿಸಿ</value>
  </data>
  <data name="deleteproduct" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಅಳಿಸಿ</value>
  </data>
  <data name="deleteproductdetail" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ವಿವರ ಅಳಿಸಿ</value>
  </data>
  <data name="deleteproducttype" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಪ್ರಕಾರ ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteQuotation" xml:space="preserve">
    <value>ನುಡಿಮುತ್ತುಗಳು ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteReport" xml:space="preserve">
    <value>ವರದಿ ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteRequest" xml:space="preserve">
    <value>ವಿನಂತಿ ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteRole" xml:space="preserve">
    <value>ಪಾತ್ರ ಅಳಿಸಿ</value>
  </data>
  <data name="deleteServiceCharge" xml:space="preserve">
    <value>ಸೇವೆ ಚಾರ್ಜ್ ಅಳಿಸಿ</value>
  </data>
  <data name="deleteServiceChargeDetails" xml:space="preserve">
    <value>ಸೇವೆ ಚಾರ್ಜ್ ವಿವರಗಳು ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteServiceType" xml:space="preserve">
    <value>ಸೇವೆ ಟೈಪ್ ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteSkills" xml:space="preserve">
    <value>ಸ್ಕಿಲ್ಸ್ ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteSpecialization" xml:space="preserve">
    <value>ತಜ್ಞತೆ ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteStep" xml:space="preserve">
    <value>ಹಂತ ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteStepLink" xml:space="preserve">
    <value>ಹಂತ ಕೊಂಡಿಅಳಿಸು</value>
  </data>
  <data name="DeleteSundry" xml:space="preserve">
    <value>ಸಂಡ್ರಿ ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteTaxCode" xml:space="preserve">
    <value>ತೆರಿಗೆ ಕೋಡ್ ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteTaxDetails" xml:space="preserve">
    <value>ತೆರಿಗೆ ವಿವರಗಳು ಅಳಿಸಿ</value>
  </data>
  <data name="deletetaxstructure" xml:space="preserve">
    <value>ತೆರಿಗೆ ರಚನೆ ಅಳಿಸಿ</value>
  </data>
  <data name="deletetaxtstructuredetails" xml:space="preserve">
    <value>ತೆರಿಗೆ ರಚನೆ ವಿವರಗಳು ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteUser" xml:space="preserve">
    <value>ಬಳಕೆದಾರನನ್ನು ಅಳಿಸಿ</value>
  </data>
  <data name="deletewarrantydetails" xml:space="preserve">
    <value>ಖಾತರಿ ವಿವರ ಅಳಿಸಿ</value>
  </data>
  <data name="DeleteWorkDetails" xml:space="preserve">
    <value>ಕೆಲಸದ ವಿವರಗಳು ಅಳಿಸಿ</value>
  </data>
  <data name="Delivered" xml:space="preserve">
    <value>ತಲುಪಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>ಡೆಲಿವರಿ ದಿನಾಂಕ</value>
  </data>
  <data name="DeliveryNote" xml:space="preserve">
    <value>ಡೆಲಿವರಿ ಗಮನಿಸಿ</value>
  </data>
  <data name="DeliveryNoteDailyReport" xml:space="preserve">
    <value>ಡೆಲಿವರಿ ಗಮನಿಸಿ ಡೈಲಿ ವರದಿ</value>
  </data>
  <data name="DeliveryNoteDate" xml:space="preserve">
    <value>ಡೆಲಿವರಿ ಗಮನಿಸಿ ದಿನಾಂಕ</value>
  </data>
  <data name="DeliveryNoteMonthlyReport" xml:space="preserve">
    <value>ಡೆಲಿವರಿ ಗಮನಿಸಿ ಮಾಸಿಕ ವರದಿ</value>
  </data>
  <data name="DeliveryNoteNum" xml:space="preserve">
    <value>ಡೆಲಿವರಿ ನೋಟ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="DeliveryNoteNumber" xml:space="preserve">
    <value>ಡೆಲಿವರಿ ನೋಟ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="DeliveryNoteReturn" xml:space="preserve">
    <value>ಡೆಲಿವರಿ ಗಮನಿಸಿ ರಿಟರ್ನ್</value>
  </data>
  <data name="DeliveryNoteReturnDailyReport" xml:space="preserve">
    <value>ಡೆಲಿವರಿ ಗಮನಿಸಿ ರಿಟರ್ನ್ ಡೈಲಿ ವರದಿ</value>
  </data>
  <data name="DeliveryNoteReturnMonthlyReport" xml:space="preserve">
    <value>ಡೆಲಿವರಿ ಗಮನಿಸಿ ರಿಟರ್ನ್ ಮಾಸಿಕ ವರದಿ</value>
  </data>
  <data name="DeliveryNoteReturnNumber" xml:space="preserve">
    <value>ಡೆಲಿವರಿ ನೋಟ್ ರಿಟರ್ನ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="Department" xml:space="preserve">
    <value>ವಿಭಾಗ</value>
  </data>
  <data name="DepartureDate" xml:space="preserve">
    <value>ನಿರ್ಗಮನದ ದಿನಾಂಕ</value>
  </data>
  <data name="Dependencyfoundcannotdeletetherecords" xml:space="preserve">
    <value>ಕಂಡು ಡಿಪೆಂಡೆನ್ಸಿ ದಾಖಲೆಗಳನ್ನು ಅಳಿಸಲು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="description" xml:space="preserve">
    <value>ವಿವರಣೆ</value>
  </data>
  <data name="Designation" xml:space="preserve">
    <value>ಹುದ್ದೆ</value>
  </data>
  <data name="DestinationColumns" xml:space="preserve">
    <value>ಗಮ್ಯಸ್ಥಾನ ಅಂಕಣ</value>
  </data>
  <data name="Detail" xml:space="preserve">
    <value>ವಿವರ</value>
  </data>
  <data name="Detailcannotbeblank" xml:space="preserve">
    <value>ವಿವರ ಖಾಲಿ ಇರಕೂಡದು</value>
  </data>
  <data name="DeviationHours" xml:space="preserve">
    <value>ಮಾರ್ಗಾಂತರ ಅವರ್ಸ್</value>
  </data>
  <data name="DeviationPercentage" xml:space="preserve">
    <value>ಮಾರ್ಗಾಂತರ%</value>
  </data>
  <data name="DeviationQuantity" xml:space="preserve">
    <value>ಮಾರ್ಗಾಂತರ ಪ್ರಮಾಣ</value>
  </data>
  <data name="DeviationStock" xml:space="preserve">
    <value>ಮಾರ್ಗಾಂತರ ಸ್ಟಾಕ್</value>
  </data>
  <data name="dimension" xml:space="preserve">
    <value>ಆಯಾಮ</value>
  </data>
  <data name="DimensionCannotBeZero" xml:space="preserve">
    <value>ಆಯಾಮ ಶೂನ್ಯವಾಗಿರುವುದಿಲ್ಲ</value>
  </data>
  <data name="Direct" xml:space="preserve">
    <value>ನೇರವಾಗಿ</value>
  </data>
  <data name="Discount" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿ</value>
  </data>
  <data name="Discount%" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿ%</value>
  </data>
  <data name="Discount11" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿ</value>
  </data>
  <data name="Discountamount" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿ ವೊತ್ತ</value>
  </data>
  <data name="DiscountAmountCanNotBeGreaterThan" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿ ವೊತ್ತ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="DiscountAmountCannotbegreaterthanEstimationAmount" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿ ವೊತ್ತ ಅಂದಾಜು ಪ್ರಮಾಣದ ಹೆಚ್ಚಾಗಿದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="DiscountAmountCannotBeGreaterThanMachineTotalAmount" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿ ವೊತ್ತ ಯಂತ್ರ ಒಟ್ಟು ಪ್ರಮಾಣದ ಹೆಚ್ಚಾಗಿದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="DiscountAmountShouldbeLessThan" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿ ವೊತ್ತ ಕಡಿಮೆ ಮಾಡಬೇಕು</value>
  </data>
  <data name="DiscountAmountShouldNotBeGreaterThan" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿ ವೊತ್ತ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಮಾಡಬಾರದು</value>
  </data>
  <data name="DiscountAmt" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿ ವೊತ್ತ</value>
  </data>
  <data name="Discountcannotbegreaterthan100" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿ ಹೆಚ್ಚು ಅಥವಾ 100 ಶೇಕಡಾವಾರು ಸಮಾನವಾಗಿರಬೇಕು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="DiscountedAmount" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿಯ ವೊತ್ತ</value>
  </data>
  <data name="DiscountedAmountPO" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿಯ ವೊತ್ತ</value>
  </data>
  <data name="DiscountedAmountSIR" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿಯ ವೊತ್ತ</value>
  </data>
  <data name="DiscountedAmt" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿಯ ವೊತ್ತ</value>
  </data>
  <data name="DiscountPer" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿ ಶೇಕಡಾವಾರು</value>
  </data>
  <data name="DiscountPercent" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿ%</value>
  </data>
  <data name="DiscountPercentage" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿ ಶೇಕಡಾವಾರು</value>
  </data>
  <data name="DiscountShouldbeLessThan" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿ ಕಡಿಮೆ ಮಾಡಬೇಕು</value>
  </data>
  <data name="DiscoutAmount" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿ ವೊತ್ತ</value>
  </data>
  <data name="DiscoutAmountPO" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿ ಆಮ್ಟ್</value>
  </data>
  <data name="DiscoutAmountSIR" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿ ವೊತ್ತ</value>
  </data>
  <data name="Dismantling" xml:space="preserve">
    <value>ಡಿಸ್ಮ್ಯಾಂಟ್ಲಿಂಗ್</value>
  </data>
  <data name="DisposalNumber" xml:space="preserve">
    <value>ವಿಲೇವಾರಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="DivideByZeroException" xml:space="preserve">
    <value>ಅಪ್ಲಿಕೇಶನ್ ದೋಷ ಸಂಭವಿಸಿದೆ</value>
  </data>
  <data name="DNActualReturnDate" xml:space="preserve">
    <value>ನಿಜವಾದ ಲಾಭ ದಿನಾಂಕ</value>
  </data>
  <data name="DNINumber" xml:space="preserve">
    <value>ಡಿ ಎನ್ ಐ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="DNMachineDetails" xml:space="preserve">
    <value>ಯಂತ್ರ ವಿವರಗಳು</value>
  </data>
  <data name="DNMobile" xml:space="preserve">
    <value>ಡಿ ಎನ್ ಮೊಬೈಲ್</value>
  </data>
  <data name="DNShippingAddress" xml:space="preserve">
    <value>ಶಿಪ್ಪಿಂಗ್ ವಿಳಾಸ</value>
  </data>
  <data name="DocketDate" xml:space="preserve">
    <value>ಡಾಕೆಟ್ ದಿನಾಂಕ</value>
  </data>
  <data name="DocketNumber" xml:space="preserve">
    <value>ಡಾಕೆಟ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="Document" xml:space="preserve">
    <value>ದಾಖಲೆ</value>
  </data>
  <data name="DocumentDetails" xml:space="preserve">
    <value>ಡಾಕ್ಯುಮೆಂಟ್ ವಿವರಗಳು</value>
  </data>
  <data name="DocumentExport" xml:space="preserve">
    <value>ಡಾಕ್ಯುಮೆಂಟ್ ರಫ್ತು</value>
  </data>
  <data name="DocumentReferenceDate" xml:space="preserve">
    <value>ರೆಫರೆನ್ಸ್ ದಿನಾಂಕ</value>
  </data>
  <data name="DocumentReferenceNumber" xml:space="preserve">
    <value>ಉಲ್ಲೇಖ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="DocumentType" xml:space="preserve">
    <value>ಡಾಕ್ಯುಮೆಂಟ್ ಪ್ರಕಾರ</value>
  </data>
  <data name="DonotEnterSpace" xml:space="preserve">
    <value>ಸ್ಪೇಸ್ ಪ್ರವೇಶಿಸುವುದಿಲ್ಲ</value>
  </data>
  <data name="downtimehours" xml:space="preserve">
    <value>ಅಲಭ್ಯತೆಯನ್ನು ಗಂಟೆಗಳ (24)</value>
  </data>
  <data name="DoyouwanttoaddthisSerialNumber" xml:space="preserve">
    <value>ನೀವು ಈ ಕ್ರಮಸಂಖ್ಯೆ ಸೇರಿಸಲು ಬಯಸುತ್ತೀರಿ</value>
  </data>
  <data name="Doyouwanttochangetheassociation" xml:space="preserve">
    <value>ನೀವು ಅಸೋಸಿಯೇಷನ್ ಬದಲಾಯಿಸಲು ಬಯಸುತ್ತೀರಿ?</value>
  </data>
  <data name="doyouwanttocontinue" xml:space="preserve">
    <value>ನೀವು ಮುಂದುವರಿಸಲು ಬಯಸುತ್ತೀರಾ</value>
  </data>
  <data name="Doyouwanttocreatenewversion" xml:space="preserve">
    <value>ನೀವು ಹೊಸ ಆವೃತ್ತಿಯನ್ನು ರಚಿಸಲು ಬಯಸುತ್ತೀರಿ?</value>
  </data>
  <data name="Doyouwanttorecreatetheinvoice" xml:space="preserve">
    <value>ನೀವು ಸರಕುಪಟ್ಟಿ ರಚಿಸಲು ಮರು ಬಯಸುವಿರಾ?</value>
  </data>
  <data name="Doyouwanttoreinvoice" xml:space="preserve">
    <value>ನೀವು reinvoice ಬಯಸುತ್ತೀರಾ</value>
  </data>
  <data name="DoYouWantToSelectCampaignService" xml:space="preserve">
    <value>ನೀವು ಪ್ರಚಾರ ಸೇವೆ ಆಯ್ಕೆ ಬಯಸುತ್ತೀರಾ</value>
  </data>
  <data name="DoYouWantToSelectCSAService" xml:space="preserve">
    <value>ನೀವು ಸಿಎಸ್ಎ ಸೇವೆ ಆಯ್ಕೆ ಬಯಸುತ್ತೀರಾ</value>
  </data>
  <data name="Dragmebelowtosee" xml:space="preserve">
    <value>ನೋಡಿ ನನ್ನನ್ನು ಎಳೆಯಿರಿ</value>
  </data>
  <data name="DueAmount" xml:space="preserve">
    <value>ಕಾರಣ ವೊತ್ತ</value>
  </data>
  <data name="DueDate" xml:space="preserve">
    <value>ಕಾರಣ ದಿನಾಂಕ</value>
  </data>
  <data name="DueDatecannotbeBlank" xml:space="preserve">
    <value>ಕಾರಣ ದಿನಾಂಕ ಖಾಲಿ ಇರಕೂಡದು</value>
  </data>
  <data name="DueRange" xml:space="preserve">
    <value>ಕಾರಣ ರೇಂಜ್</value>
  </data>
  <data name="Duplicate" xml:space="preserve">
    <value>ದ್ವಿಭಾಗದ</value>
  </data>
  <data name="DuplicateArticle" xml:space="preserve">
    <value>ಲೇಖನ ನಕಲು</value>
  </data>
  <data name="DuplicateAttachment" xml:space="preserve">
    <value>ಬಾಂಧವ್ಯ ನಕಲು</value>
  </data>
  <data name="duplicatebinlocation" xml:space="preserve">
    <value>ಬಿನ್ ಸ್ಥಳ ನಕಲು</value>
  </data>
  <data name="duplicatebranch" xml:space="preserve">
    <value>ನಕಲು ಶಾಖೆ</value>
  </data>
  <data name="DuplicateBranchName" xml:space="preserve">
    <value>ಶಾಖೆ ಹೆಸರು ಈಗಾಗಲೇ ಇರುತ್ತದೆ</value>
  </data>
  <data name="DuplicateBrand" xml:space="preserve">
    <value>ಬ್ರಾಂಡ್ ನಕಲು</value>
  </data>
  <data name="DuplicateChecklist" xml:space="preserve">
    <value>ಪರಿಶೀಲನಾಪಟ್ಟಿ ನಕಲು</value>
  </data>
  <data name="DuplicateCheckListDescriptionisnotAllowed" xml:space="preserve">
    <value>ನಕಲು ಪರಿಶೀಲನಾಪಟ್ಟಿ ವಿವರಣೆ ಅನುಮತಿಸಲಾಗುವುದಿಲ್ಲ</value>
  </data>
  <data name="DuplicateCode" xml:space="preserve">
    <value>ಕೋಡ್ ನಕಲು</value>
  </data>
  <data name="DuplicateCode1" xml:space="preserve">
    <value>ವಿವರಣೆ ನಕಲು</value>
  </data>
  <data name="DuplicateCodeandDescription" xml:space="preserve">
    <value>ಕೋಡ್ ಮತ್ತು ವಿವರಣೆ ನಕಲು</value>
  </data>
  <data name="DuplicateCodeandDescription1" xml:space="preserve">
    <value>ನಕಲು ಕೋಡ್ ಮತ್ತು ವಿವರಣೆ</value>
  </data>
  <data name="DuplicateCompanyName" xml:space="preserve">
    <value>ಕಂಪನಿ ಹೆಸರಿನ ನಕಲಿ</value>
  </data>
  <data name="duplicateconsigneeaddress" xml:space="preserve">
    <value>Consignee ವಿಳಾಸಕ್ಕೆ ನಕಲು</value>
  </data>
  <data name="duplicateconsigneelocation" xml:space="preserve">
    <value>Consignee ಸ್ಥಳ ನಕಲು</value>
  </data>
  <data name="DuplicateContactPerson" xml:space="preserve">
    <value>ಸಂಪರ್ಕ ವ್ಯಕ್ತಿ ನಕಲು</value>
  </data>
  <data name="DuplicateCustomer" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ನಕಲು</value>
  </data>
  <data name="duplicatedate" xml:space="preserve">
    <value>ದಿನಾಂಕ ನಕಲು</value>
  </data>
  <data name="duplicateDefectGroupCode" xml:space="preserve">
    <value>ದೋಷ ಗುಂಪು ಕೋಡ್ ನಕಲು</value>
  </data>
  <data name="duplicateDefectGroupcodeandName" xml:space="preserve">
    <value>ದೋಷ ಗುಂಪು ಕೋಡ್ ಮತ್ತು ಹೆಸರು ನಕಲು</value>
  </data>
  <data name="duplicateDefectGroupName" xml:space="preserve">
    <value>ದೋಷ ಗ್ರೂಪ್ ಹೆಸರು ನಕಲು</value>
  </data>
  <data name="Duplicatedefectname" xml:space="preserve">
    <value>ದೋಷ ಹೆಸರು ನಕಲು</value>
  </data>
  <data name="DuplicateDefectNameCodeAndDescription" xml:space="preserve">
    <value>ದೋಷ ಹೆಸರು ಕೋಡ್ ಮತ್ತು ವಿವರಣೆ ನಕಲು</value>
  </data>
  <data name="DuplicateDefectName_Code" xml:space="preserve">
    <value>ದೋಷ ಹೆಸರು ಕೋಡ್ ನಕಲು</value>
  </data>
  <data name="DuplicateDefectName_Description" xml:space="preserve">
    <value>ದೋಷ ಹೆಸರು ವಿವರಣೆ ನಕಲು</value>
  </data>
  <data name="DuplicateDescription" xml:space="preserve">
    <value>ವಿವರಣೆ ನಕಲು</value>
  </data>
  <data name="DuplicateEmailsFound" xml:space="preserve">
    <value>ಕಂಡು ಇಮೇಲ್ಗಳನ್ನು ನಕಲು</value>
  </data>
  <data name="DuplicateEmployee" xml:space="preserve">
    <value>ನಕಲು ನೌಕರರ</value>
  </data>
  <data name="DuplicateEnquiryStage" xml:space="preserve">
    <value>ವಿಚಾರಣೆ ಹಂತ ನಕಲು</value>
  </data>
  <data name="Duplicateentries" xml:space="preserve">
    <value>ನಮೂದುಗಳನ್ನು ನಕಲು</value>
  </data>
  <data name="Duplicateentriesof" xml:space="preserve">
    <value>ದ್ವಿಭಾಗದ</value>
  </data>
  <data name="Duplicateentriesof1" xml:space="preserve">
    <value>ನಮೂದುಗಳನ್ನು ನಕಲು</value>
  </data>
  <data name="DuplicateExpenditure" xml:space="preserve">
    <value>ವೆಚ್ಚ ಹೆಸರು ನಕಲು</value>
  </data>
  <data name="DuplicateExpenditureName" xml:space="preserve">
    <value>ವೆಚ್ಚ ಹೆಸರು ನಕಲು</value>
  </data>
  <data name="DuplicateFunctionGroup" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್ ಗ್ರೂಪ್ ನಕಲು</value>
  </data>
  <data name="duplicateholidayname" xml:space="preserve">
    <value>ಹಾಲಿಡೇ ಹೆಸರು ನಕಲು</value>
  </data>
  <data name="DuplicateInvoiceisnotallowed" xml:space="preserve">
    <value>ನಕಲು ಸರಕುಪಟ್ಟಿ ಅನುಮತಿಸಲಾಗುವುದಿಲ್ಲ</value>
  </data>
  <data name="DuplicateIssueSubAreaCodeAndDescription" xml:space="preserve">
    <value>ನಕಲು IssueSubArea-ಕೋಡ್ ಮತ್ತು ಹೆಸರು</value>
  </data>
  <data name="DuplicateIssueSubAreaDescription" xml:space="preserve">
    <value>ಉಪ ಏರಿಯಾ ಹೆಸರು ಸಮಸ್ಯೆಯನ್ನು ನಕಲು</value>
  </data>
  <data name="DuplicateIssueSubAreaShortName" xml:space="preserve">
    <value>ನಕಲು ಸಂಚಿಕೆ ಉಪ ಪ್ರದೇಶ ಕೋಡ್</value>
  </data>
  <data name="duplicateLinkName" xml:space="preserve">
    <value>ಲಿಂಕ್ ಹೆಸರು ನಕಲು</value>
  </data>
  <data name="duplicateLinkURL" xml:space="preserve">
    <value>ಲಿಂಕ್ URL ನಕಲು</value>
  </data>
  <data name="DuplicateLoginId" xml:space="preserve">
    <value>ಲಾಗಿನ್ ID ನಕಲು</value>
  </data>
  <data name="DuplicateMaster" xml:space="preserve">
    <value>ಮಾಸ್ಟರ್ ನಕಲು</value>
  </data>
  <data name="duplicatemodel" xml:space="preserve">
    <value>ಮಾದರಿ ನಕಲು</value>
  </data>
  <data name="DuplicateModule" xml:space="preserve">
    <value>ಮಾಡ್ಯೂಲ್ ನಕಲು</value>
  </data>
  <data name="duplicatemovementype" xml:space="preserve">
    <value>ಚಳವಳಿ ಪ್ರಕಾರ ನಕಲು</value>
  </data>
  <data name="DuplicateOperationCode" xml:space="preserve">
    <value>ಕಾರ್ಯಾಚರಣೆ ಕೋಡ್ ನಕಲು</value>
  </data>
  <data name="DuplicatePart" xml:space="preserve">
    <value>ಭಾಗ ನಕಲು</value>
  </data>
  <data name="Duplicatepartnumber" xml:space="preserve">
    <value>ಭಾಗ ಸಂಖ್ಯೆ ನಕಲು</value>
  </data>
  <data name="DuplicatePartsCategory" xml:space="preserve">
    <value>ಭಾಗಗಳು ವರ್ಗದಲ್ಲಿ ನಕಲು</value>
  </data>
  <data name="DuplicatePhoneNumbersFound" xml:space="preserve">
    <value>ಕಂಡು ಫೋನ್ ಸಂಖ್ಯೆಗಳು ನಕಲು</value>
  </data>
  <data name="duplicateproducttype" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಪ್ರಕಾರ ನಕಲು</value>
  </data>
  <data name="DuplicateQuestion" xml:space="preserve">
    <value>ಪ್ರಶ್ನೆ ನಕಲು</value>
  </data>
  <data name="DuplicateRole" xml:space="preserve">
    <value>ಪಾತ್ರ ನಕಲು</value>
  </data>
  <data name="duplicatesecondarysegment" xml:space="preserve">
    <value>ಮಾಧ್ಯಮಿಕ ವಿಭಾಗದಲ್ಲಿ ನಕಲು</value>
  </data>
  <data name="DuplicateSequence" xml:space="preserve">
    <value>ಅನುಕ್ರಮ ನಕಲು</value>
  </data>
  <data name="DuplicateSerialNumber" xml:space="preserve">
    <value>ಕ್ರಮ ಸಂಖ್ಯೆ ನಕಲು</value>
  </data>
  <data name="Duplicateservicecode" xml:space="preserve">
    <value>ಸೇವೆ ಕೋಡ್ ನಕಲು</value>
  </data>
  <data name="duplicateservicedate" xml:space="preserve">
    <value>ಸೇವೆ ದಿನಾಂಕ ನಕಲು</value>
  </data>
  <data name="duplicateservicetype" xml:space="preserve">
    <value>ಸೇವೆ ರೀತಿಯ ನಕಲು</value>
  </data>
  <data name="DuplicateServiceTypeisnotallowed" xml:space="preserve">
    <value>ನಕಲು ಸೇವೆ ಪ್ರಕಾರವನ್ನು ಅನುಮತಿಸಲಾಗುವುದಿಲ್ಲ</value>
  </data>
  <data name="DuplicateSibling" xml:space="preserve">
    <value>ಒಡಹುಟ್ಟಿದವರು ನಕಲು</value>
  </data>
  <data name="DuplicateSiteAddressAndLocation" xml:space="preserve">
    <value>ನಕಲು ಸೈಟ್ ವಿಳಾಸ ಮತ್ತು</value>
  </data>
  <data name="duplicatestate" xml:space="preserve">
    <value>ರಾಜ್ಯದ ನಕಲು</value>
  </data>
  <data name="duplicatestatename" xml:space="preserve">
    <value>ರಾಜ್ಯದ ಹೆಸರು ನಕಲು</value>
  </data>
  <data name="DuplicateSubfolder" xml:space="preserve">
    <value>Subfolder ನಕಲು</value>
  </data>
  <data name="DuplicateSundryDescriptionisnotAllowed" xml:space="preserve">
    <value>ಬಗೆಬಗೆಯ ಕೆಲಸ ವಿವರಣೆ ನಕಲು</value>
  </data>
  <data name="DuplicateSuppierInvoiceNumber" xml:space="preserve">
    <value>Suppier ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ ನಕಲು</value>
  </data>
  <data name="DuplicateSupplier" xml:space="preserve">
    <value>ಸರಬರಾಜುದಾರ ನಕಲು</value>
  </data>
  <data name="DuplicateTaxname" xml:space="preserve">
    <value>ತೆರಿಗೆ ಹೆಸರು ನಕಲು</value>
  </data>
  <data name="DuplicateTaxType" xml:space="preserve">
    <value>ತೆರಿಗೆ ರೀತಿಯ ನಕಲು</value>
  </data>
  <data name="DuplicateToolName" xml:space="preserve">
    <value>ಉಪಕರಣವನ್ನು ನಕಲು</value>
  </data>
  <data name="duplicatetransaction" xml:space="preserve">
    <value>ಟ್ರಾನ್ಸಾಕ್ಷನ್ ನಕಲು</value>
  </data>
  <data name="DuplicateUniqueidentifier" xml:space="preserve">
    <value>ಅಸದೃಶ ನಕಲು</value>
  </data>
  <data name="DuplicateVariant" xml:space="preserve">
    <value>ಭಿನ್ನ ನಕಲು</value>
  </data>
  <data name="DuplicateWarehouse" xml:space="preserve">
    <value>ಗೋದಾಮಿನ ನಕಲು</value>
  </data>
  <data name="duplicatewarrantytype" xml:space="preserve">
    <value>ಖಾತರಿ ರೀತಿಯ ನಕಲು</value>
  </data>
  <data name="DutyAmount" xml:space="preserve">
    <value>ಡ್ಯೂಟಿ ವೊತ್ತ</value>
  </data>
  <data name="DutyAmt" xml:space="preserve">
    <value>ಡ್ಯೂಟಿ ವೊತ್ತ</value>
  </data>
  <data name="DutyStructure" xml:space="preserve">
    <value>ಡ್ಯೂಟಿ ರಚನೆ</value>
  </data>
  <data name="edit" xml:space="preserve">
    <value>ಸಂಪಾದಿಸು</value>
  </data>
  <data name="EditAction" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಆಕ್ಷನ್</value>
  </data>
  <data name="EditCompany" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಕಂಪನಿ</value>
  </data>
  <data name="editcomponentdetails" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಕಾಂಪೊನೆಂಟ್ ವಿವರಗಳು</value>
  </data>
  <data name="EditEvents" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಕ್ರಿಯೆಗಳು</value>
  </data>
  <data name="EditFIR" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಫ್ಲೋಟ್ ಸಂಚಿಕೆ ವಿನಂತಿ</value>
  </data>
  <data name="EditFloatChangeLocation" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಫ್ಲೋಟ್ ಸ್ಥಳ ಬದಲಾವಣೆ</value>
  </data>
  <data name="EditJobCard" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಜಾಬ್ ಕಾರ್ಡ್</value>
  </data>
  <data name="editmodel" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಮಾದರಿ</value>
  </data>
  <data name="editoperation" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಆಪರೇಷನ್</value>
  </data>
  <data name="EditOperationDetails" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಆಪರೇಷನ್ ವಿವರಗಳು</value>
  </data>
  <data name="EditPackingList" xml:space="preserve">
    <value>ಬದಲಾಯಿಸಿ ಪ್ಯಾಕಿಂಗ್ ಪಟ್ಟಿ</value>
  </data>
  <data name="EditPartsMaster" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಭಾಗಗಳು ಮಾಸ್ಟರ್</value>
  </data>
  <data name="editproduct" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಉತ್ಪನ್ನ</value>
  </data>
  <data name="EditProductSalesInvoice" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಉತ್ಪನ್ನ ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ</value>
  </data>
  <data name="EditProductSalesInvoiceCancellation" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಉತ್ಪನ್ನ ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ ರದ್ದತಿ</value>
  </data>
  <data name="EditProductTransferGRN" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಉತ್ಪನ್ನ ಟ್ರಾನ್ಸ್ಫರ್ ಜಿ ಆರ್ ಎನ್</value>
  </data>
  <data name="EditProductTransferNote" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಉತ್ಪನ್ನ ಟ್ರಾನ್ಸ್ಫರ್ ಗಮನಿಸಿ</value>
  </data>
  <data name="EditProductTransferRequest" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಉತ್ಪನ್ನ ವರ್ಗಾವಣೆ ಮನವಿ</value>
  </data>
  <data name="editproducttype" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಉತ್ಪನ್ನ ಪ್ರಕಾರ</value>
  </data>
  <data name="EditPurchaseGRN" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಖರೀದಿ ಜಿ ಆರ್ ಎನ್</value>
  </data>
  <data name="EditPurchaseInvoice" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಖರೀದಿ ಸರಕುಪಟ್ಟಿ</value>
  </data>
  <data name="EditPurchaseOrder" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಪರ್ಚೇಸ್ ಆರ್ಡರ್</value>
  </data>
  <data name="EditPurchaseOrderCancellation" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಪರ್ಚೇಸ್ ಆರ್ಡರ್ ರದ್ದತಿ</value>
  </data>
  <data name="EditReport" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ವರದಿ</value>
  </data>
  <data name="editsecondarysegment" xml:space="preserve">
    <value>ಸೆಕೆಂಡರಿ ಸೆಗ್ಮೆಂಟ್ ಸಂಪಾದಿಸಿ</value>
  </data>
  <data name="editsevicecharges" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಸೇವೆ ಚಾರ್ಜಸ್</value>
  </data>
  <data name="editsiteaddress" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಸೈಟ್ ವಿಳಾಸ</value>
  </data>
  <data name="editstate" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ರಾಜ್ಯ</value>
  </data>
  <data name="EditStockBlockingUnblocking" xml:space="preserve">
    <value>ಅನಿರ್ಬಂಧಿಸುವಲ್ಲಿ ಸಂಪಾದಿಸಿ ಸ್ಟಾಕ್ ತಡೆಯುವ</value>
  </data>
  <data name="EditStockCheckConfirmation" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಸ್ಟಾಕ್ ಚೆಕ್ ದೃಢೀಕರಣ</value>
  </data>
  <data name="EditStockCheckRequest" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಸ್ಟಾಕ್ ಚೆಕ್ ವಿನಂತಿ</value>
  </data>
  <data name="EditStockReceiptGRN" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಸ್ಟಾಕ್ ರಸೀತಿ ಜಿ ಆರ್ ಎನ್</value>
  </data>
  <data name="EditStockTransferRequest" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಸ್ಟಾಕ್ ವರ್ಗಾವಣೆ ಮನವಿ</value>
  </data>
  <data name="EditSupersession" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ Supersession</value>
  </data>
  <data name="edittaxstructure" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ತೆರಿಗೆ ರಚನೆ</value>
  </data>
  <data name="editwarrantydeatils" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಖಾತರಿ ವಿವರಗಳನ್ನು</value>
  </data>
  <data name="effectivefrom" xml:space="preserve">
    <value>ಪರಿಣಾಮಕಾರಿ</value>
  </data>
  <data name="EighteenMonthQty" xml:space="preserve">
    <value>18 ತಿಂಗಳ ಪ್ರಮಾಣ</value>
  </data>
  <data name="EighteenMonthValue" xml:space="preserve">
    <value>18 ತಿಂಗಳ ಮೌಲ್ಯ</value>
  </data>
  <data name="EightHour" xml:space="preserve">
    <value>ಕಡಿಮೆ 8 ಅವರ್</value>
  </data>
  <data name="EightHour1" xml:space="preserve">
    <value>ಕಡಿಮೆ 8 ಅವರ್ಸ್</value>
  </data>
  <data name="EightHour11" xml:space="preserve">
    <value>&lt;8 ಗಂಟೆ</value>
  </data>
  <data name="EightToSixteenHours" xml:space="preserve">
    <value>8 16 ಗಂಟೆಗಳ</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>ಇಮೇಲ್</value>
  </data>
  <data name="EmailToAddresse" xml:space="preserve">
    <value>Addresse ಇಮೇಲ್</value>
  </data>
  <data name="EmailToCustomer" xml:space="preserve">
    <value>ಇಮೇಲ್ ಗ್ರಾಹಕ</value>
  </data>
  <data name="Employee" xml:space="preserve">
    <value>ನೌಕರರ</value>
  </data>
  <data name="EmployeeBranch" xml:space="preserve">
    <value>ನೌಕರ - ಶಾಖೆ</value>
  </data>
  <data name="EmployeeBranch1" xml:space="preserve">
    <value>ನೌಕರ ಶಾಖೆ</value>
  </data>
  <data name="EmployeeBranch11" xml:space="preserve">
    <value>ನೌಕರ - ಶಾಖೆ</value>
  </data>
  <data name="EmployeeDetails" xml:space="preserve">
    <value>ನೌಕರರ ವಿವರಗಳು</value>
  </data>
  <data name="EmployeeID" xml:space="preserve">
    <value>ನೌಕರರ ಕೋಡ್</value>
  </data>
  <data name="EmployeeID1" xml:space="preserve">
    <value>ಉದ್ಯೋಗಿಗಳ ID</value>
  </data>
  <data name="EmployeeIDisalreadyused" xml:space="preserve">
    <value>ನೌಕರರ ಕೋಡ್ ನಕಲು</value>
  </data>
  <data name="EmployeeisalreadyassociatedwiththeBranch" xml:space="preserve">
    <value>ನೌಕರರ ಈಗಾಗಲೇ ಶಾಖೆ ಸಂಬಂಧಿಸಿದೆ</value>
  </data>
  <data name="EmployeeisalreadyassociatedwiththeSpecialization" xml:space="preserve">
    <value>ನೌಕರರ ಈಗಾಗಲೇ ವಿಶೇಷ ಸಂಬಂಧಿಸಿದೆ</value>
  </data>
  <data name="Employeeisassociatedintraveldetailwillbedeleted" xml:space="preserve">
    <value>ನೌಕರರ ಪ್ರಯಾಣ ವಿವರ ಸಂಬಂಧಿಸಿದೆ ಅಳಿಸಲಾಗುತ್ತದೆ</value>
  </data>
  <data name="EmployeeisInActive" xml:space="preserve">
    <value>ಸಕ್ರಿಯ ನೌಕರರು</value>
  </data>
  <data name="EmployeeName" xml:space="preserve">
    <value>ನೌಕರರ</value>
  </data>
  <data name="EmployeeNameIsAlreadySelected" xml:space="preserve">
    <value>ನೌಕರರ ಹೆಸರು ಈಗಾಗಲೇ ಆಯ್ಕೆ</value>
  </data>
  <data name="EmployeeNotFound" xml:space="preserve">
    <value>ನೌಕರರ ಕಂಡುಬಂದಿಲ್ಲ</value>
  </data>
  <data name="EmployeeSearch" xml:space="preserve">
    <value>ನೌಕರರ ಹುಡುಕು</value>
  </data>
  <data name="EmployeeSkills" xml:space="preserve">
    <value>ಸ್ಕಿಲ್ಸ್</value>
  </data>
  <data name="EmployeeSpecialization" xml:space="preserve">
    <value>ವಿಶೇಷ</value>
  </data>
  <data name="EmployeeTools" xml:space="preserve">
    <value>ನೌಕರರ / ಪರಿಕರಗಳು</value>
  </data>
  <data name="EndDate" xml:space="preserve">
    <value>ಅಂತಿಮ ದಿನಾಂಕ</value>
  </data>
  <data name="EndDatecannotbelessthanStartDate" xml:space="preserve">
    <value>ಅಂತಿಮ ದಿನಾಂಕ ಆರಂಭದ ದಿನಾಂಕ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="EngineSerialNum" xml:space="preserve">
    <value>ಎಂಜಿನ್ ಕ್ರಮ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="EngineSerialnumber" xml:space="preserve">
    <value>ಎಂಜಿನ್ ಕ್ರಮ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>ಇಂಗ್ಲೀಷ್</value>
  </data>
  <data name="EnquiredQty" xml:space="preserve">
    <value>ವಿಚಾರಣೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="Enquiry" xml:space="preserve">
    <value>ವಿಚಾರಣೆ</value>
  </data>
  <data name="EnquiryCount" xml:space="preserve">
    <value>ವಿಚಾರಣೆ ನ್ಯಾಷನಲ್</value>
  </data>
  <data name="EnquiryDate" xml:space="preserve">
    <value>ವಿಚಾರಣೆ ದಿನಾಂಕ</value>
  </data>
  <data name="EnquiryNumber" xml:space="preserve">
    <value>ವಿಚಾರಣೆ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="EnquiryQty" xml:space="preserve">
    <value>ವಿಚಾರಣೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="EnquiryStage" xml:space="preserve">
    <value>ವಿಚಾರಣೆ ಹಂತ</value>
  </data>
  <data name="EnquiryStageName" xml:space="preserve">
    <value>ವಿಚಾರಣೆ ಹಂತ ಹೆಸರು</value>
  </data>
  <data name="EnquiryStatus" xml:space="preserve">
    <value>ವಿಚಾರಣೆ ಸ್ಥಿತಿ</value>
  </data>
  <data name="EnquiryToOrderConvertionRatio" xml:space="preserve">
    <value>Convertion ಅನುಪಾತ ಆರ್ಡರ್ ವಿಚಾರಣೆ</value>
  </data>
  <data name="EnquiryType" xml:space="preserve">
    <value>ವಿಚಾರಣೆ ಪ್ರಕಾರ</value>
  </data>
  <data name="Enter" xml:space="preserve">
    <value>ಯನ್ನು</value>
  </data>
  <data name="EnterCode" xml:space="preserve">
    <value>ಕೋಡ್ ಯನ್ನು</value>
  </data>
  <data name="EnterDescription" xml:space="preserve">
    <value>ವಿವರಣೆ ನಮೂದಿಸಿ</value>
  </data>
  <data name="EnteredCampaignAlreadyExist" xml:space="preserve">
    <value>ಪ್ರಚಾರ ಕೋಡ್ ನಕಲು</value>
  </data>
  <data name="EnteredNumberdoesnotbelongstocustomerorprospect" xml:space="preserve">
    <value>ಪ್ರವೇಶಿಸಿತು ಸಂಖ್ಯೆ doesnot ಗ್ರಾಹಕ ಅಥವಾ ಅದರೆಡೆಗೆ ಸೇರುತ್ತದೆ</value>
  </data>
  <data name="EnteredToolalreadyexist" xml:space="preserve">
    <value>ಪ್ರವೇಶಿಸಿತು ಉಪಕರಣವನ್ನು ಈಗಾಗಲೆ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="enterfromdate" xml:space="preserve">
    <value>ದಿನಾಂಕದಿಂದ ಯನ್ನು</value>
  </data>
  <data name="EnterJobCardNumber" xml:space="preserve">
    <value>ಉದ್ಯೋಗ ಚೀಟಿ ಸಂಖ್ಯೆ ಯನ್ನು</value>
  </data>
  <data name="EnterMasterName" xml:space="preserve">
    <value>ಮಾಸ್ಟರ್ ಹೆಸರನ್ನು ನಮೂದಿಸಿ</value>
  </data>
  <data name="enterNonTaxableothercharges" xml:space="preserve">
    <value>ಅಲ್ಲದ ತೆರಿಗೆ ಇತರ ಆರೋಪಗಳನ್ನು ಯನ್ನು</value>
  </data>
  <data name="enterNonTaxableothercharges1" xml:space="preserve">
    <value>ಅಲ್ಲದ ತೆರಿಗೆ ಇತರ ಆರೋಪಗಳನ್ನು 1 ನಮೂದಿಸಿ</value>
  </data>
  <data name="enterNonTaxableothercharges2" xml:space="preserve">
    <value>ಅಲ್ಲದ ತೆರಿಗೆ ಇತರ ಆರೋಪಗಳನ್ನು 2 ನಮೂದಿಸಿ</value>
  </data>
  <data name="EnterRequiredQuantity" xml:space="preserve">
    <value>ಅವಶ್ಯವಿರುವ ಪ್ರಮಾಣವನ್ನು ಯನ್ನು</value>
  </data>
  <data name="enterTaxableothercharges" xml:space="preserve">
    <value>ತೆರಿಗೆ ಇತರ ಆರೋಪಗಳನ್ನು ಯನ್ನು</value>
  </data>
  <data name="enterTaxableothercharges1" xml:space="preserve">
    <value>ತೆರಿಗೆ ಇತರ ಆರೋಪಗಳನ್ನು 1 ನಮೂದಿಸಿ</value>
  </data>
  <data name="enterTaxableothercharges2" xml:space="preserve">
    <value>ತೆರಿಗೆ ಇತರ ಆರೋಪಗಳನ್ನು 2 ನಮೂದಿಸಿ</value>
  </data>
  <data name="EnterTaxableOtherChargesAmount" xml:space="preserve">
    <value>ತೆರಿಗೆ ಇತರ ಆರೋಪಗಳನ್ನು ವೊತ್ತ ನಮೂದಿಸಿ</value>
  </data>
  <data name="enteryear" xml:space="preserve">
    <value>ವರ್ಷದ ಯನ್ನು</value>
  </data>
  <data name="EntryTax" xml:space="preserve">
    <value>ಪ್ರವೇಶ ತೆರಿಗೆ</value>
  </data>
  <data name="EntryTaxPercentage" xml:space="preserve">
    <value>ಪ್ರವೇಶ ತೆರಿಗೆ ಶೇಕಡಾವಾರು</value>
  </data>
  <data name="EnvironmentTypeWiseWarrantyClaimReport" xml:space="preserve">
    <value>ಪರಿಸರ ಪ್ರಕಾರ ವೈಸ್ ಖಾತರಿ ಹಕ್ಕು ವರದಿ</value>
  </data>
  <data name="EnvironmentTypeWiseWarrantyClaimReportFrom" xml:space="preserve">
    <value>ಪರಿಸರ ಪ್ರಕಾರ ವೈಸ್ ಖಾತರಿ ಹಕ್ಕು ವರದಿ</value>
  </data>
  <data name="EOQ" xml:space="preserve">
    <value>EOQ</value>
  </data>
  <data name="Equal" xml:space="preserve">
    <value>ಸಮ</value>
  </data>
  <data name="EquivalentConsignee" xml:space="preserve">
    <value>ಸಮಾನ Consignee</value>
  </data>
  <data name="EquivalentConsigneeAddressForBillingAddresIsNotDefinedInOEMSalesOrderCanNotBeCreatedDoYouWantToContinue" xml:space="preserve">
    <value>ಬಿಲ್ಲಿಂಗ್ ವಿಳಾಸ ಸಮಾನ Consignee ವಿಳಾಸ OEM ರಲ್ಲಿ ಉಲ್ಲೇಖಿಸಲ್ಪಟ್ಟಿಲ್ಲ.</value>
  </data>
  <data name="EquivalentConsigneeAddressForBillingAddressAndShippingAddresIsNotDefinedInOEMSalesOrderCanNotBeCreatedDoYouWantToContinue" xml:space="preserve">
    <value>ಬಿಲ್ಲಿಂಗ್ ವಿಳಾಸ ಮತ್ತು ಶಿಪ್ಪಿಂಗ್ addres ಸಮಾನ Consignee ವಿಳಾಸ OEM ರಲ್ಲಿ ಉಲ್ಲೇಖಿಸಲ್ಪಟ್ಟಿಲ್ಲ.</value>
  </data>
  <data name="EquivalentConsigneeAddressForShippingAddresIsNotDefinedInOEMSalesOrderCanNotBeCreatedDoYouWantToContinue" xml:space="preserve">
    <value>ಶಿಪ್ಪಿಂಗ್ ವಿಳಾಸ ಸಮಾನ Consignee ವಿಳಾಸ OEM ರಲ್ಲಿ ಉಲ್ಲೇಖಿಸಲ್ಪಟ್ಟಿಲ್ಲ.</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>ದೋಷ</value>
  </data>
  <data name="ErrorInChecking" xml:space="preserve">
    <value>ಪರೀಕ್ಷಿಸುವ ದೋಷ ..</value>
  </data>
  <data name="ErrorinPublishing" xml:space="preserve">
    <value>ಪಬ್ಲಿಷಿಂಗ್ ದೋಷ</value>
  </data>
  <data name="ErrorinUploadedPartsPleaseOpenExcel" xml:space="preserve">
    <value>ಅಪ್ಲೋಡ್ ಭಾಗಗಳಲ್ಲಿ ದೋಷ.</value>
  </data>
  <data name="ErrorOccuredwhileLocking" xml:space="preserve">
    <value>ಲಾಕ್ ದೋಷ ಸಂಭವಿಸಿದೆ</value>
  </data>
  <data name="ErrorSaving" xml:space="preserve">
    <value>ಉಳಿಸುವಲ್ಲಿ ದೋಷ</value>
  </data>
  <data name="EscalateTo" xml:space="preserve">
    <value>ವಿಸ್ತರಿಸಿತು</value>
  </data>
  <data name="EscalationHours" xml:space="preserve">
    <value>ಏರಿಕೆ ಅವರ್ಸ್</value>
  </data>
  <data name="EstimationAmount" xml:space="preserve">
    <value>ಅಂದಾಜು ವೊತ್ತ</value>
  </data>
  <data name="EstimationFieldSearch" xml:space="preserve">
    <value>ಅಂದಾಜು ಹುಡುಕು</value>
  </data>
  <data name="EstimationNumber" xml:space="preserve">
    <value>ಅಂದಾಜು #</value>
  </data>
  <data name="EstimationOEMApproval" xml:space="preserve">
    <value>ಅಂದಾಜು OEM ಅನುಮೋದನೆ</value>
  </data>
  <data name="EstimationType" xml:space="preserve">
    <value>ಅಂದಾಜು ಪ್ರಕಾರ</value>
  </data>
  <data name="EventEndDateAndTime" xml:space="preserve">
    <value>ಈವೆಂಟ್ ಅಂತಿಮ ದಿನಾಂಕ ಮತ್ತು ಸಮಯ</value>
  </data>
  <data name="EventLocation" xml:space="preserve">
    <value>ಘಟನೆ ಸ್ಥಳ</value>
  </data>
  <data name="EventName" xml:space="preserve">
    <value>ಈವೆಂಟ್ ಹೆಸರು</value>
  </data>
  <data name="Events" xml:space="preserve">
    <value>ಕ್ರಿಯೆಗಳು</value>
  </data>
  <data name="EventStartDateAndTime" xml:space="preserve">
    <value>ಈವೆಂಟ್ ಆರಂಭ ದಿನಾಂಕ ಮತ್ತು ಸಮಯ</value>
  </data>
  <data name="EventStartDateCannotbeLessthanJobCardDate" xml:space="preserve">
    <value>ಈವೆಂಟ್ ಆರಂಭ ದಿನಾಂಕ ಉದ್ಯೋಗ ಚೀಟಿ ದಿನಾಂಕ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Eventstarttimecoincideswithothereventsdoyouwanttocontinue" xml:space="preserve">
    <value>ಈವೆಂಟ್ ಪ್ರಾರಂಭದ ಸಮಯ ಇತರ ಘಟನೆಗಳು ಸೇರಿಕೊಳ್ಳುತ್ತದೆ, ನೀವು ಮುಂದುವರಿಸಲು ಬಯಸುತ್ತೀರಾ?</value>
  </data>
  <data name="EventType" xml:space="preserve">
    <value>ಈವೆಂಟ್ ಕೌಟುಂಬಿಕತೆ</value>
  </data>
  <data name="ExceededTime" xml:space="preserve">
    <value>ಮಿತಿಮೀರಿದೆ ಟೈಮ್</value>
  </data>
  <data name="Excel" xml:space="preserve">
    <value>ಮಿಂಚು</value>
  </data>
  <data name="ExcessAmount" xml:space="preserve">
    <value>ಹೆಚ್ಚುವರಿ ವೊತ್ತ</value>
  </data>
  <data name="ExcessQty" xml:space="preserve">
    <value>ಹೆಚ್ಚುವರಿ ಪ್ರಮಾಣ</value>
  </data>
  <data name="ExcessQuantity" xml:space="preserve">
    <value>ಹೆಚ್ಚುವರಿ ಪ್ರಮಾಣ</value>
  </data>
  <data name="ExchangePartDetails" xml:space="preserve">
    <value>ವಿನಿಮಯ ಭಾಗ ವಿವರಗಳು</value>
  </data>
  <data name="ExchangeRate" xml:space="preserve">
    <value>ವಿನಿಮಯ ದರ</value>
  </data>
  <data name="ExchangeRatecannotbezero" xml:space="preserve">
    <value>ವಿನಿಮಯ ದರ ಸೊನ್ನೆ ಇರುವಂತಿಲ್ಲ</value>
  </data>
  <data name="ExpectedArrivalDate" xml:space="preserve">
    <value>ನಿರೀಕ್ಷಿತ ಆಗಮಿಸಿದರೂ ದಿನಾಂಕ</value>
  </data>
  <data name="ExpectedDeliveryDate" xml:space="preserve">
    <value>ನಿರೀಕ್ಷಿತ ಡೆಲಿವರಿ ದಿನಾಂಕ</value>
  </data>
  <data name="ExpectedReturnDate" xml:space="preserve">
    <value>ನಿರೀಕ್ಷಿತ ಪ್ರತಿಫಲದ ದಿನಾಂಕ</value>
  </data>
  <data name="ExpenditureName" xml:space="preserve">
    <value>ವೆಚ್ಚದ ಹೆಸರು</value>
  </data>
  <data name="ExpenditureValueCannotBeZero" xml:space="preserve">
    <value>ವೆಚ್ಚದ ಮೌಲ್ಯ ಶೂನ್ಯ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ExpenseAmount" xml:space="preserve">
    <value>ಖರ್ಚು ವೊತ್ತ</value>
  </data>
  <data name="ExpenseDetail" xml:space="preserve">
    <value>ಖರ್ಚು ವಿವರ</value>
  </data>
  <data name="Expenses" xml:space="preserve">
    <value>ವೆಚ್ಚಗಳು</value>
  </data>
  <data name="ExpensesDetail" xml:space="preserve">
    <value>ವೆಚ್ಚಗಳು ವಿವರ</value>
  </data>
  <data name="ExpensesSummary" xml:space="preserve">
    <value>ವೆಚ್ಚಗಳು ಸಾರಾಂಶ</value>
  </data>
  <data name="ExpensesTotalAmount" xml:space="preserve">
    <value>ವೆಚ್ಚಗಳು ಒಟ್ಟು ವೊತ್ತ</value>
  </data>
  <data name="ExpensesTotalAmountIsBeyondAcceptableLimit" xml:space="preserve">
    <value>ವೆಚ್ಚಗಳು ಒಟ್ಟು ವೊತ್ತ ಸ್ವೀಕಾರಾರ್ಹ ಮಿತಿ ಮೀರಿ</value>
  </data>
  <data name="ExpenseType" xml:space="preserve">
    <value>ಖರ್ಚು ಪ್ರಕಾರ</value>
  </data>
  <data name="Export" xml:space="preserve">
    <value>ರಫ್ತು</value>
  </data>
  <data name="ExportAction" xml:space="preserve">
    <value>ರಫ್ತು ಆಕ್ಷನ್</value>
  </data>
  <data name="ExporttoDocument" xml:space="preserve">
    <value>ಡಾಕ್ಯುಮೆಂಟ್ ರಫ್ತು</value>
  </data>
  <data name="ExporttoExcel" xml:space="preserve">
    <value>ಎಕ್ಸೆಲ್ ರಫ್ತು</value>
  </data>
  <data name="FailedPartsStock" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಸ್ಟಾಕ್ ವಿಫಲವಾಗಿದೆ</value>
  </data>
  <data name="FailedtosavenewContactPerson" xml:space="preserve">
    <value>ಹೊಸ ಸಂಪರ್ಕ ವ್ಯಕ್ತಿ ಉಳಿಸಲು ವಿಫಲವಾಗಿದೆ</value>
  </data>
  <data name="FailedtosavenewParty" xml:space="preserve">
    <value>ಹೊಸ ಪಕ್ಷದ ಉಳಿಸಲು ವಿಫಲವಾಗಿದೆ</value>
  </data>
  <data name="FailedtosavenewSerialNumber" xml:space="preserve">
    <value>ಹೊಸ ಕ್ರಮಸಂಖ್ಯೆ ಉಳಿಸಲು ವಿಫಲವಾಗಿದೆ</value>
  </data>
  <data name="FailedToSaveSerial" xml:space="preserve">
    <value>ಸರಣಿ ಉಳಿಸಲು ವಿಫಲವಾಗಿದೆ</value>
  </data>
  <data name="FailureReading" xml:space="preserve">
    <value>ವೈಫಲ್ಯ ಓದುವಿಕೆ</value>
  </data>
  <data name="FailureReadingCannotBeLessThanCurrentReading" xml:space="preserve">
    <value>ವೈಫಲ್ಯ ಓದುವ ಪ್ರಸ್ತುತ ಓದುವ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Fast" xml:space="preserve">
    <value>ವೇಗವಾದ</value>
  </data>
  <data name="FastCannotbeSmallerthanorEqualtoMedium" xml:space="preserve">
    <value>ಫಾಸ್ಟ್ ಚಿಕ್ಕದಾದ ಅಥವಾ ಸಾಧಾರಣ ಸಮಾನವಾಗಿರಬೇಕು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="FAX" xml:space="preserve">
    <value>ಫ್ಯಾಕ್ಸ್</value>
  </data>
  <data name="February" xml:space="preserve">
    <value>ಫೆಬ್ರ</value>
  </data>
  <data name="Fieldshighlightedaremandatory" xml:space="preserve">
    <value>ಹೈಲೈಟ್ ಜಾಗ ಕಡ್ಡಾಯವಾಗಿ ತುಂಬಬೇಕು</value>
  </data>
  <data name="FieldsmarkedwithStararemandatory" xml:space="preserve">
    <value>* ಎಂದು ಕಾಣಿಸುವ ಜಾಗ ಕಡ್ಡಾಯವಾಗಿ ತುಂಬಬೇಕು</value>
  </data>
  <data name="FileAlreadyExistsDoYouWantToReplace" xml:space="preserve">
    <value>ಕಡತವು, ನೀವು ಬದಲಾಯಿಸಲು ಬಯಸುತ್ತೀರೆ?</value>
  </data>
  <data name="FileDescription" xml:space="preserve">
    <value>ಫೈಲ್ ವಿವರಣೆ</value>
  </data>
  <data name="FileName" xml:space="preserve">
    <value>ಫೈಲ್ ಹೆಸರು</value>
  </data>
  <data name="FileNameShouldNotExceed50Characters" xml:space="preserve">
    <value>ಫೈಲ್ ಹೆಸರು 50 ಪಾತ್ರಗಳು ಮೀರಬಾರದು</value>
  </data>
  <data name="FileSizeShouldNotExceed2MB" xml:space="preserve">
    <value>ಕಡತ ಗಾತ್ರ 2 ಎಂಬಿ ಮೀರಬಾರದು</value>
  </data>
  <data name="FileSizeShouldNotExceed2MBf" xml:space="preserve">
    <value>ಕಡತ ಗಾತ್ರ ಯಾವುದೇ 2 ಎಂಬಿ ಮೀರುವಂತಿಲ್ಲ ಬೇಕು</value>
  </data>
  <data name="FileSizeShouldNotExceed5MB" xml:space="preserve">
    <value>ಕಡತ ಗಾತ್ರ 5 ಎಂಬಿ ಮೀರಬಾರದು</value>
  </data>
  <data name="FileType" xml:space="preserve">
    <value>ಫೈಲ್ ಪ್ರಕಾರ</value>
  </data>
  <data name="FileTypeNotSupportedSupportedFormatsAre" xml:space="preserve">
    <value>ಫೈಲ್ ಬೆಂಬಲಿತವಾಗಿಲ್ಲ, ಬೆಂಬಲ ವಿಧಾನಗಳೆಂದರೆ:</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>ಶೋಧಕ</value>
  </data>
  <data name="FilterAllQueBranch" xml:space="preserve">
    <value>ಎಲ್ಲಾ ಕ್ಯು ಶಾಖೆ ನಿರ್ದಿಷ್ಟ ಫಿಲ್ಟರ್?</value>
  </data>
  <data name="FilterCriteria" xml:space="preserve">
    <value>ಆಯ್ಕೆ ಮಾನದಂಡ</value>
  </data>
  <data name="FinalAmount" xml:space="preserve">
    <value>ಫೈನಲ್ ವೊತ್ತ</value>
  </data>
  <data name="Finance" xml:space="preserve">
    <value>ಹಣಕಾಸು</value>
  </data>
  <data name="FinancedAmount" xml:space="preserve">
    <value>ಆರ್ಥಿಕ ವೊತ್ತ</value>
  </data>
  <data name="FinanceDetail" xml:space="preserve">
    <value>ಹಣಕಾಸು ವಿವರ</value>
  </data>
  <data name="FinanceDetails" xml:space="preserve">
    <value>ಹಣಕಾಸು ವಿವರಗಳು</value>
  </data>
  <data name="FinanceRemarks" xml:space="preserve">
    <value>ಹಣಕಾಸು ರಿಮಾರ್ಕ್ಸ್</value>
  </data>
  <data name="FinanceScheme" xml:space="preserve">
    <value>ಹಣಕಾಸು ಯೋಜನೆ</value>
  </data>
  <data name="FinanceTerms" xml:space="preserve">
    <value>ಹಣಕಾಸು ನಿಯಮಗಳು</value>
  </data>
  <data name="FinancialYear" xml:space="preserve">
    <value>ಹಣಕಾಸು ವರ್ಷ</value>
  </data>
  <data name="financialyearalredyselected" xml:space="preserve">
    <value>ಹಣಕಾಸು ವರ್ಷ ಈಗಾಗಲೇ ಆಯ್ಕೆ</value>
  </data>
  <data name="FinancialyearcannotbeGreaterthannextrowsfinancialyear" xml:space="preserve">
    <value>ಹಣಕಾಸು ವರ್ಷದ ಹಣಕಾಸು ವರ್ಷದ ಮುಂದಿನ ಸಾಲುಗಳನ್ನು ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Financialyearcannotbelessthanpreviousrowsfinancialyear" xml:space="preserve">
    <value>ಹಣಕಾಸು ವರ್ಷದ ಹಣಕಾಸು ವರ್ಷದ ಹಿಂದಿನ ಸಾಲುಗಳನ್ನು ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Financier" xml:space="preserve">
    <value>ಹಣಕಾಸು</value>
  </data>
  <data name="FinancierName" xml:space="preserve">
    <value>ಫೈನಾನ್ಷಿಯರ್ ಹೆಸರು</value>
  </data>
  <data name="FindRootCause" xml:space="preserve">
    <value>ಮೂಲ ಕಾರಣ ಹುಡುಕಿ</value>
  </data>
  <data name="First" xml:space="preserve">
    <value>ಪ್ರಥಮ</value>
  </data>
  <data name="Float" xml:space="preserve">
    <value>ತೇಲುವುದು</value>
  </data>
  <data name="FloatChangeStockLocation" xml:space="preserve">
    <value>ಚೇಂಜ್ ಸ್ಟಾಕ್ ಸ್ಥಳ ತೆಪ್ಪ</value>
  </data>
  <data name="FloatDeliveryNote" xml:space="preserve">
    <value>ಫ್ಲೋಟ್ ಡೆಲಿವರಿ ಗಮನಿಸಿ</value>
  </data>
  <data name="FloatDeliveryNoteDate" xml:space="preserve">
    <value>ಫ್ಲೋಟ್ ಡೆಲಿವರಿ ಗಮನಿಸಿ ದಿನಾಂಕ</value>
  </data>
  <data name="FloatDeliveryNoteNumber" xml:space="preserve">
    <value>ಫ್ಲೋಟ್ ಡೆಲಿವರಿ ನೋಟ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="FloatDeliveryNoteReturn" xml:space="preserve">
    <value>ಫ್ಲೋಟ್ ಡೆಲಿವರಿ ಗಮನಿಸಿ ರಿಟರ್ನ್</value>
  </data>
  <data name="FloatDeliveryNoteReturnNumber" xml:space="preserve">
    <value>ಡೆಲಿವರಿ ನೋಟ್ ರಿಟರ್ನ್ ಸಂಖ್ಯೆ ತೆಪ್ಪ</value>
  </data>
  <data name="FloatInvoice" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ತೆಪ್ಪ</value>
  </data>
  <data name="FloatInvoiceNumber" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ ತೆಪ್ಪ</value>
  </data>
  <data name="FloatIssueRequest" xml:space="preserve">
    <value>ಫ್ಲೋಟ್ ಸಂಚಿಕೆ ವಿನಂತಿ</value>
  </data>
  <data name="FloatIssueRequestDate" xml:space="preserve">
    <value>ಸಂಚಿಕೆ ವಿನಂತಿ ದಿನಾಂಕ ತೆಪ್ಪ</value>
  </data>
  <data name="FloatIssueRequestNumber" xml:space="preserve">
    <value>ಸಂಚಿಕೆ ವಿನಂತಿ ಸಂಖ್ಯೆ ತೆಪ್ಪ</value>
  </data>
  <data name="FloatReceiptGRN" xml:space="preserve">
    <value>ಫ್ಲೋಟ್ ರಸೀತಿ ಜಿ ಆರ್ ಎನ್</value>
  </data>
  <data name="FloatReceiptGrnNumber" xml:space="preserve">
    <value>ಫ್ಲೋಟ್ ರಸೀತಿ ಜಿ ಆರ್ ಎನ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="FloatScrap" xml:space="preserve">
    <value>ಫ್ಲೋಟ್ ಸ್ಕ್ರ್ಯಾಪ್</value>
  </data>
  <data name="FloatScrapNumber" xml:space="preserve">
    <value>ಸ್ಕ್ರ್ಯಾಪ್ ಸಂಖ್ಯೆ ತೆಪ್ಪ</value>
  </data>
  <data name="FOC" xml:space="preserve">
    <value>ಉಚಿತವಾಗಿ</value>
  </data>
  <data name="FOCDetails" xml:space="preserve">
    <value>ಎಫ್ಓಸಿ ವಿವರಗಳು</value>
  </data>
  <data name="FOCRemarks" xml:space="preserve">
    <value>ಎಫ್ಓಸಿ ರಿಮಾರ್ಕ್ಸ್</value>
  </data>
  <data name="Follow-Ups" xml:space="preserve">
    <value>ಮುಂಬರಿಕೆಗಳನ್ನೂ</value>
  </data>
  <data name="FollowingPartsareblockedcannotcreateGRN" xml:space="preserve">
    <value>ಕೆಳಗಿನ ಭಾಗ (ಗಳು) ಎಂದು ಜಿ ಆರ್ ಎನ್ ನಿರ್ಮಿಸಲು ಸಾಧ್ಯವಾಗಿಲ್ಲ ನಿರ್ಬಂಧಿಸಲಾಗುತ್ತದೆ</value>
  </data>
  <data name="For" xml:space="preserve">
    <value>ಫಾರ್</value>
  </data>
  <data name="ForfullReturnAmountShouldbezeroorfull" xml:space="preserve">
    <value>ಪೂರ್ಣ ಲಾಭ ವೊತ್ತ ಶೂನ್ಯ ಅಥವಾ ಪೂರ್ಣ ಆಗಿರಬೇಕು</value>
  </data>
  <data name="forgotpassword" xml:space="preserve">
    <value>ಪಾಸ್ವರ್ಡ್ ಮರೆತಿರಾ?</value>
  </data>
  <data name="formula" xml:space="preserve">
    <value>ಸೂತ್ರ</value>
  </data>
  <data name="FormulaCostPrice" xml:space="preserve">
    <value>ಫಾರ್ಮುಲಾ ವೆಚ್ಚ ಬೆಲೆ</value>
  </data>
  <data name="formulasummary" xml:space="preserve">
    <value>ಫಾರ್ಮುಲಾ ಸಾರಾಂಶ</value>
  </data>
  <data name="FourtyEightToNintyHours" xml:space="preserve">
    <value>48 90 ಗಂಟೆಗಳ</value>
  </data>
  <data name="FreeHours" xml:space="preserve">
    <value>ಉಚಿತ ಅವರ್ಸ್</value>
  </data>
  <data name="freestock" xml:space="preserve">
    <value>ಉಚಿತ ಸ್ಟಾಕ್</value>
  </data>
  <data name="FreeStockisnotenoughforKitBreaking" xml:space="preserve">
    <value>ಉಚಿತ ಸ್ಟಾಕ್ ಕಿಟ್ ಬ್ರೇಕಿಂಗ್ ಸಾಕಾಗುವುದಿಲ್ಲ</value>
  </data>
  <data name="Freestockisnotenoughforthepartnumber" xml:space="preserve">
    <value>ಉಚಿತ ಸ್ಟಾಕ್ ಭಾಗ ಸಂಖ್ಯೆ ಸಾಕಾಗುವುದಿಲ್ಲ</value>
  </data>
  <data name="FreeStockisupdated" xml:space="preserve">
    <value>ಉಚಿತ ಸ್ಟಾಕ್ ಇತರ ಬಳಕೆದಾರರ ಬು ಅಪ್ಡೇಟ್</value>
  </data>
  <data name="Freight" xml:space="preserve">
    <value>ಸರಕು</value>
  </data>
  <data name="FreightAmount" xml:space="preserve">
    <value>ಸರಕು ವೊತ್ತ</value>
  </data>
  <data name="FreightTerms" xml:space="preserve">
    <value>ಸರಕು ನಿಯಮಗಳು</value>
  </data>
  <data name="Friday" xml:space="preserve">
    <value>ಶುಕ್ರವಾರ</value>
  </data>
  <data name="From" xml:space="preserve">
    <value>ಗೆ</value>
  </data>
  <data name="fromdate" xml:space="preserve">
    <value>ದಿನಾಂಕದಿಂದ</value>
  </data>
  <data name="FromDateandTodatecannotbeEmpty" xml:space="preserve">
    <value>ದಿನಾಂಕದಿಂದ ಮತ್ತು ಇಲ್ಲಿಯವರೆಗೆ ಖಾಲಿ ಇರುವಂತಿಲ್ಲ</value>
  </data>
  <data name="FromDatecannotbegreaterthanToDate" xml:space="preserve">
    <value>ದಿನಾಂಕ ಇಲ್ಲಿಯವರೆಗೆ ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="FromDateCannotbegreaterThanToday" xml:space="preserve">
    <value>ದಿನಾಂಕದಿಂದ ಪ್ರಸ್ತುತ ದಿನಾಂಕ ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="fromdatecannotbegreaterthentodate" xml:space="preserve">
    <value>ದಿನಾಂಕ ಇಲ್ಲಿಯವರೆಗೆ ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="fromdatecannotbegreatorthantodate" xml:space="preserve">
    <value>ದಿನಾಂಕ ಇಲ್ಲಿಯವರೆಗೆ ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Fromdatecannotbelessthanorequaltopreviousrowtodate" xml:space="preserve">
    <value>ದಿನಾಂಕದಿಂದ ಕಡಿಮೆ ಅಥವಾ ಇಲ್ಲಿಯವರೆಗೆ ಹಿಂದಿನ ಸಾಲುಗಳ ಸಮಾನವಾಗಿರಬೇಕು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="FromDatecannotbelessthanToDate" xml:space="preserve">
    <value>ದಿನಾಂಕ ಇಲ್ಲಿಯವರೆಗೆ ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="FromDatecannotbelessthanToDate1" xml:space="preserve">
    <value>ದಿನಾಂಕ ಇಲ್ಲಿಯವರೆಗೆ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="fromdatecannotbelessthencurrentdate" xml:space="preserve">
    <value>ದಿನಾಂಕದಿಂದ ಪ್ರಸ್ತುತ ದಿನಾಂಕ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="fromdatecannotbelessthencurrentdate1" xml:space="preserve">
    <value>ದಿನಾಂಕ ಪ್ರಸ್ತುತ ದಿನಾಂಕ ನಂತರ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="fromdatemustbegreaterthanorequaltoissuedate" xml:space="preserve">
    <value>ದಿನಾಂಕದಿಂದ ದಿನಾಂಕದಂದು ವಿವಾದದ ಹೆಚ್ಚು ಅಥವಾ ಸಮನಾಗಿರಬೇಕು</value>
  </data>
  <data name="fromdatemustbelesserthentodate" xml:space="preserve">
    <value>ದಿನಾಂಕ ಇಲ್ಲಿಯವರೆಗೆ ಕಡಿಮೆ ಇರಬೇಕು</value>
  </data>
  <data name="fromdatemustbelesserthentodate1" xml:space="preserve">
    <value>ದಿನಾಂಕ ಇಲ್ಲಿಯವರೆಗೆ ನಂತರ ಕಡಿಮೆ ಇರಬೇಕು</value>
  </data>
  <data name="FromInvoiceNumber" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ ಗೆ</value>
  </data>
  <data name="FromStep" xml:space="preserve">
    <value>ಹಂತ ಗೆ</value>
  </data>
  <data name="FromTimeAndToTimeCannotBeEqual" xml:space="preserve">
    <value>ಸಮಯ ಮತ್ತು ಕಾಲಕ್ಕೆ ಸಮಾನವಾಗಿರಬೇಕು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="FromTimecannotbegreaterthanToTime" xml:space="preserve">
    <value>ಕಾಲಕಾಲಕ್ಕೆ ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="FromTimeCanNotBeGreaterToTime" xml:space="preserve">
    <value>ಕಾಲಕಾಲಕ್ಕೆ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="FromYearAndToYearAreMandatory" xml:space="preserve">
    <value>ವರ್ಷದಿಂದ ಮತ್ತು ವರ್ಷ ಕಡ್ಡಾಯವಾಗಿ</value>
  </data>
  <data name="FromYearCannotbegreaterthanToYear" xml:space="preserve">
    <value>ವರ್ಷದಿಂದ ವರ್ಷಕ್ಕೆ ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Full" xml:space="preserve">
    <value>ಪೂರ್ಣ</value>
  </data>
  <data name="FunctionGroup" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್ ಗುಂಪು</value>
  </data>
  <data name="FunctionGroupHeader" xml:space="preserve">
    <value>ಗುಂಪು ಶಿರೋಲೇಖ ಕೆಲಸ</value>
  </data>
  <data name="FunctionGroupID" xml:space="preserve">
    <value>ಗುಂಪನ್ನು ID ಕೆಲಸ</value>
  </data>
  <data name="functiongroupisnotactive" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್ ಗುಂಪು ಸಕ್ರಿಯ ಅಲ್ಲ</value>
  </data>
  <data name="FunctionGroupName" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್ ಗುಂಪು ಹೆಸರು</value>
  </data>
  <data name="FunctionGroupNative" xml:space="preserve">
    <value>ಗುಂಪು ಸ್ಥಳೀಯ ಫಂಕ್ಷನ್</value>
  </data>
  <data name="FunctionGroupOperations" xml:space="preserve">
    <value>ಗುಂಪಿನ ಕಾರ್ಯಾಚರಣೆಗಳ ಕೆಲಸ</value>
  </data>
  <data name="FunctionGroupSearch" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್ ಗುಂಪು ಹುಡುಕಾಟ</value>
  </data>
  <data name="GatePass" xml:space="preserve">
    <value>ಗೇಟ್ ಪಾಸ್</value>
  </data>
  <data name="GatePassAlreadyCreated" xml:space="preserve">
    <value>ಗೇಟ್ ಪಾಸ್ ಈಗಾಗಲೇ ದಾಖಲಿಸಿದವರು</value>
  </data>
  <data name="GatePassDate" xml:space="preserve">
    <value>ಗೇಟ್ ಪಾಸ್ ದಿನಾಂಕ</value>
  </data>
  <data name="GatePassDetail" xml:space="preserve">
    <value>ಗೇಟ್ ಪಾಸ್ ವಿವರ</value>
  </data>
  <data name="GatePassNumber" xml:space="preserve">
    <value>ಗೇಟ್ ಪಾಸ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="GDRNumber" xml:space="preserve">
    <value>ಜಿ ಡಿ ಆರ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="GDRSettlement" xml:space="preserve">
    <value>ಜಿಡಿಆರ್ ಸೆಟ್ಲ್ಮೆಂಟ್</value>
  </data>
  <data name="GDRSettlementNumber" xml:space="preserve">
    <value>ಜಿ ಡಿ ಆರ್ ಸೆಟ್ಲ್ಮೆಂಟ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="GDRSettlementReport" xml:space="preserve">
    <value>ಜಿಡಿಆರ್ ಸೆಟ್ಲ್ಮೆಂಟ್ ವರದಿ</value>
  </data>
  <data name="GDRStatus" xml:space="preserve">
    <value>ಜಿಡಿಆರ್ ಸ್ಥಿತಿ</value>
  </data>
  <data name="General" xml:space="preserve">
    <value>ಜನರಲ್</value>
  </data>
  <data name="GeneralShift" xml:space="preserve">
    <value>ಜನರಲ್ ಶಿಫ್ಟ್</value>
  </data>
  <data name="GenerateReport" xml:space="preserve">
    <value>ವರದಿ ರಚಿಸಿ</value>
  </data>
  <data name="GoodsinTransit" xml:space="preserve">
    <value>ಇನ್ ಸಾರಿಗೆ ಗೂಡ್ಸ್</value>
  </data>
  <data name="GoodsReleaseNote" xml:space="preserve">
    <value>ಗೂಡ್ಸ್ ರಿಲೀಸ್ ನೋಟ್</value>
  </data>
  <data name="GoodsReleaseNoteDate" xml:space="preserve">
    <value>ಗೂಡ್ಸ್ ರಿಲೀಸ್ ನೋಟ್ ದಿನಾಂಕ</value>
  </data>
  <data name="GoodsReleaseNoteDetailReport" xml:space="preserve">
    <value>ಗೂಡ್ಸ್ ರಿಲೀಸ್ ನೋಟ್ ವಿವರ ವರದಿ</value>
  </data>
  <data name="GoodsReleaseNoteNumber" xml:space="preserve">
    <value>ಗೂಡ್ಸ್ ರಿಲೀಸ್ ನೋಟ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="GrandTotal" xml:space="preserve">
    <value>ಗ್ರ್ಯಾಂಡ್ ಒಟ್ಟು</value>
  </data>
  <data name="GraphCategorySelection" xml:space="preserve">
    <value>ಗ್ರಾಫ್ ವರ್ಗ ಆಯ್ಕೆ</value>
  </data>
  <data name="GraphType" xml:space="preserve">
    <value>ಗ್ರಾಫ್ ಪ್ರಕಾರ</value>
  </data>
  <data name="GreaterThan" xml:space="preserve">
    <value>ಹೆಚ್ಚಿನ</value>
  </data>
  <data name="GRNDate" xml:space="preserve">
    <value>ಜಿ ಆರ್ ಎನ್ ದಿನಾಂಕ</value>
  </data>
  <data name="GRNisalreadycreatedfortheselectedinspectionnumber" xml:space="preserve">
    <value>ಜಿ ಆರ್ ಎನ್ ಈಗಾಗಲೇ ಆಯ್ಕೆ ತಪಾಸಣೆ ಸಂಖ್ಯೆ ರಚಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="GRNIsAlreadyCreatedForThisInvoiceNumber" xml:space="preserve">
    <value>ಜಿ ಆರ್ ಎನ್ ಈಗಾಗಲೇ ಈ ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ ರಚಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="GRNNumber" xml:space="preserve">
    <value>ಜಿ ಆರ್ ಎನ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="GrossAmount" xml:space="preserve">
    <value>ಒಟ್ಟು ವೊತ್ತ</value>
  </data>
  <data name="Group" xml:space="preserve">
    <value>ಗುಂಪು</value>
  </data>
  <data name="GroupQue" xml:space="preserve">
    <value>ಗುಂಪು ಸರದಿಗೆ</value>
  </data>
  <data name="GroupQueue" xml:space="preserve">
    <value>ಗುಂಪು ಸರದಿಗೆ</value>
  </data>
  <data name="Header" xml:space="preserve">
    <value>ತಲೆಹೊಡೆತ</value>
  </data>
  <data name="Heading" xml:space="preserve">
    <value>ಶಿರೋನಾಮೆ</value>
  </data>
  <data name="high" xml:space="preserve">
    <value>ಎತ್ತರದ</value>
  </data>
  <data name="HigherPriorityOrderexistsdoyouwanttocontinue" xml:space="preserve">
    <value>ಹೆಚ್ಚಿನ ಆದ್ಯತೆಯನ್ನು ಸಲುವಾಗಿ ನೀವು ಮುಂದುವರಿಸಲು ಬಯಸುತ್ತೀರಾ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ?</value>
  </data>
  <data name="HighlightedFieldsareMandatory" xml:space="preserve">
    <value>ಹೈಲೈಟ್ ಜಾಗ ಕಡ್ಡಾಯವಾಗಿ ತುಂಬಬೇಕು</value>
  </data>
  <data name="HMR" xml:space="preserve">
    <value>HMR</value>
  </data>
  <data name="Hold" xml:space="preserve">
    <value>ಹಿಡಿ</value>
  </data>
  <data name="Holidays" xml:space="preserve">
    <value>ರಜಾದಿನಗಳು</value>
  </data>
  <data name="Home" xml:space="preserve">
    <value>ಮುಖಪುಟ</value>
  </data>
  <data name="HourlyRate" xml:space="preserve">
    <value>ಗಂಟೆಯ ದರ</value>
  </data>
  <data name="Hours" xml:space="preserve">
    <value>ಅವರ್ಸ್</value>
  </data>
  <data name="ID" xml:space="preserve">
    <value>ಐಡಿ</value>
  </data>
  <data name="IgnoreAmount" xml:space="preserve">
    <value>ವೊತ್ತ ನಿರ್ಲಕ್ಷಿಸು</value>
  </data>
  <data name="IgnoreBalance" xml:space="preserve">
    <value>ಬ್ಯಾಲೆನ್ಸ್ ನಿರ್ಲಕ್ಷಿಸು</value>
  </data>
  <data name="Image" xml:space="preserve">
    <value>ಚಿತ್ರ</value>
  </data>
  <data name="Import" xml:space="preserve">
    <value>ಆಮದು</value>
  </data>
  <data name="ImportAction" xml:space="preserve">
    <value>ಆಮದು ಆಕ್ಷನ್</value>
  </data>
  <data name="ImportedSuccessfully" xml:space="preserve">
    <value>ಯಶಸ್ವಿಯಾಗಿ ಆಮದು</value>
  </data>
  <data name="ImportIntoDatabase" xml:space="preserve">
    <value>ದತ್ತಸಂಚಯದಿಂದ ಆಮದು</value>
  </data>
  <data name="ImportParts" xml:space="preserve">
    <value>ಆಮದು ಭಾಗಗಳು</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>ನಿಷ್ಕ್ರಿಯ</value>
  </data>
  <data name="InactiveBinlocation" xml:space="preserve">
    <value>ನಿಷ್ಕ್ರಿಯ ಬಿನ್ ಸ್ಥಳ</value>
  </data>
  <data name="Inactivebranch" xml:space="preserve">
    <value>ನಿಷ್ಕ್ರಿಯ ಶಾಖೆ</value>
  </data>
  <data name="InactiveClearingAgentName" xml:space="preserve">
    <value>ನಿಷ್ಕ್ರಿಯ ಕ್ಲಿಯರಿಂಗ್ ಏಜೆಂಟ್ ಹೆಸರು</value>
  </data>
  <data name="InactiveDefectCode" xml:space="preserve">
    <value>ನಿಷ್ಕ್ರಿಯ ದೋಷದ ಕೋಡ್</value>
  </data>
  <data name="InactiveEmployee" xml:space="preserve">
    <value>ನಿಷ್ಕ್ರಿಯ ನೌಕರ</value>
  </data>
  <data name="InactiveModel" xml:space="preserve">
    <value>ನಿಷ್ಕ್ರಿಯ ಮಾದರಿ</value>
  </data>
  <data name="InactivePartNumber" xml:space="preserve">
    <value>ನಿಷ್ಕ್ರಿಯ ಭಾಗ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InactiveParty" xml:space="preserve">
    <value>ನಿಷ್ಕ್ರಿಯ ಪಕ್ಷದ</value>
  </data>
  <data name="inactiveproduct" xml:space="preserve">
    <value>ಸಕ್ರಿಯ ಉತ್ಪನ್ನದಲ್ಲಿ</value>
  </data>
  <data name="InactiveSerialNumber" xml:space="preserve">
    <value>ನಿಷ್ಕ್ರಿಯ ಕ್ರಮ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InactiveSupplier" xml:space="preserve">
    <value>ನಿಷ್ಕ್ರಿಯ ಪೂರೈಕೆದಾರ</value>
  </data>
  <data name="Inactivetool" xml:space="preserve">
    <value>ನಿಷ್ಕ್ರಿಯ ಸಾಧನ</value>
  </data>
  <data name="InactiveWarehouse" xml:space="preserve">
    <value>ನಿಷ್ಕ್ರಿಯ ವೇರ್ಹೌಸ್</value>
  </data>
  <data name="IncompleteOperationsarepresent" xml:space="preserve">
    <value>ಅಪೂರ್ಣ ಕಾರ್ಯಾಚರಣೆಗಳು, ನೀವು ಉದ್ಯೋಗ ಚೀಟಿ ಮುಚ್ಚಲು ಪ್ರಸ್ತುತ ಬಯಸುತ್ತೀರಿ?</value>
  </data>
  <data name="IncorrectPassword" xml:space="preserve">
    <value>ತಪ್ಪಾದ ಪಾಸ್ವರ್ಡ್</value>
  </data>
  <data name="IncorrectPassword1" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಪಾಸ್ವರ್ಡ್</value>
  </data>
  <data name="IndexOutOfRangeException" xml:space="preserve">
    <value>ಅಪ್ಲಿಕೇಶನ್ ದೋಷ ಸಂಭವಿಸಿದೆ</value>
  </data>
  <data name="InformationCollected" xml:space="preserve">
    <value>ಕಲೆಕ್ಟೆಡ್ ಮಾಹಿತಿ</value>
  </data>
  <data name="InProgress" xml:space="preserve">
    <value>ಪ್ರೊಗ್ರೆಸ್</value>
  </data>
  <data name="InProgressCount" xml:space="preserve">
    <value>ಪ್ರೋಗ್ರೆಸ್ ಎಣಿಕೆ</value>
  </data>
  <data name="InsertedSuccessfully" xml:space="preserve">
    <value>ಯಶಸ್ವಿಯಾಗಿ ಸೇರಿಸಿದ</value>
  </data>
  <data name="InspectedBy" xml:space="preserve">
    <value>ಪರಿಶೀಲನೆ</value>
  </data>
  <data name="InspectedDate" xml:space="preserve">
    <value>ತಪಾಸಣೆ ದಿನಾಂಕ</value>
  </data>
  <data name="InspectionChecklist" xml:space="preserve">
    <value>ತಪಾಸಣೆ ಪರಿಶೀಲನಾಪಟ್ಟಿ</value>
  </data>
  <data name="InspectionDateCannotbeGreaterThanCurrentDate" xml:space="preserve">
    <value>ತಪಾಸಣೆ ದಿನಾಂಕ ಪ್ರಸ್ತುತ ದಿನಾಂಕ ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="InspectionFieldSearch" xml:space="preserve">
    <value>ಇನ್ಸ್ಪೆಕ್ಷನ್ ಫೀಲ್ಡ್ ಹುಡುಕು</value>
  </data>
  <data name="InspectionNumber" xml:space="preserve">
    <value>ಇನ್ಸ್ಪೆಕ್ಷನ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="Insurance" xml:space="preserve">
    <value>ವಿಮೆ</value>
  </data>
  <data name="InsuranceAmount" xml:space="preserve">
    <value>ವಿಮೆ ವೊತ್ತ</value>
  </data>
  <data name="InsuranceCompany" xml:space="preserve">
    <value>ವಿಮೆ ಕಂಪನಿ</value>
  </data>
  <data name="InsuranceDetails" xml:space="preserve">
    <value>ವಿಮಾ ವಿವರಗಳು</value>
  </data>
  <data name="InsuranceJob" xml:space="preserve">
    <value>ವಿಮೆ?</value>
  </data>
  <data name="InsuranceParty" xml:space="preserve">
    <value>ವಿಮೆ ಪಕ್ಷದ</value>
  </data>
  <data name="InsurancePartyisLockedDoyouwanttocontinue" xml:space="preserve">
    <value>ವಿಮೆ ಪಕ್ಷದ ಲಾಕ್ ನೀವು ಮುಂದುವರಿಸಲು ಬಯಸುತ್ತೀರಾ</value>
  </data>
  <data name="InsurancePartyisnotactive" xml:space="preserve">
    <value>ನಿಷ್ಕ್ರಿಯ ವಿಮೆ ಪಕ್ಷದ</value>
  </data>
  <data name="InsuranceRemarks" xml:space="preserve">
    <value>ವಿಮೆ ರಿಮಾರ್ಕ್ಸ್</value>
  </data>
  <data name="InsuranceResponciblity" xml:space="preserve">
    <value>ವಿಮೆ ಜವಾಬ್ದಾ?</value>
  </data>
  <data name="InsuranceResponsibility" xml:space="preserve">
    <value>ವಿಮೆ ರೆಸ್ಪಾನ್ಸಿಬಿಲಿಟಿ</value>
  </data>
  <data name="InsuredAmount" xml:space="preserve">
    <value>ವಿಮೆ ವೊತ್ತ</value>
  </data>
  <data name="InsurenceExpiryDate" xml:space="preserve">
    <value>ವಿಮೆ ಮುಕ್ತಾಯದ ದಿನಾಂಕ</value>
  </data>
  <data name="InsurenceNumber" xml:space="preserve">
    <value>ವಿಮೆ #</value>
  </data>
  <data name="InsurancePremiumAmount" xml:space="preserve">
    <value>ವಿಮೆ ಪ್ರೀಮಿಯಂ ವೊತ್ತ</value>
  </data>
  <data name="Internal" xml:space="preserve">
    <value>ಆಂತರಿಕ</value>
  </data>
  <data name="InternalInvoice" xml:space="preserve">
    <value>ಆಂತರಿಕ ಸರಕುಪಟ್ಟಿ</value>
  </data>
  <data name="InternalInvoiceDate" xml:space="preserve">
    <value>ಆಂತರಿಕ ಸರಕುಪಟ್ಟಿ ದಿನಾಂಕ</value>
  </data>
  <data name="InternalInvoiceNumber" xml:space="preserve">
    <value>ಆಂತರಿಕ ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InternalInvoiceReturn" xml:space="preserve">
    <value>ಆಂತರಿಕ ಸರಕುಪಟ್ಟಿ ರಿಟರ್ನ್</value>
  </data>
  <data name="InternalInvoiceReturnDate" xml:space="preserve">
    <value>ಆಂತರಿಕ ಸರಕುಪಟ್ಟಿ ಹಿಂದಿರುಗುವ ದಿನಾಂಕವನ್ನು</value>
  </data>
  <data name="InternalInvoiceReturnDetails" xml:space="preserve">
    <value>ಆಂತರಿಕ ಸರಕುಪಟ್ಟಿ ರಿಟರ್ನ್ ವಿವರಗಳು</value>
  </data>
  <data name="InternalInvoiceSearch" xml:space="preserve">
    <value>ಆಂತರಿಕ ಸರಕುಪಟ್ಟಿ ಹುಡುಕು</value>
  </data>
  <data name="InTransit" xml:space="preserve">
    <value>ಸಾಗಣೆಯಲ್ಲಿ</value>
  </data>
  <data name="Invalid" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ</value>
  </data>
  <data name="InvalidAmount" xml:space="preserve">
    <value>ಅಮಾನ್ಯ ಮೊತ್ತ</value>
  </data>
  <data name="InvalidBranch" xml:space="preserve">
    <value>ಅಮಾನ್ಯ ಶಾಖೆ</value>
  </data>
  <data name="InvalidBranchSelection" xml:space="preserve">
    <value>Prefixsuffix ಕಂಪನಿ ನಿರ್ದಿಷ್ಟ ರಚಿತವಾಗುತ್ತದೆ.</value>
  </data>
  <data name="InvalidCastException" xml:space="preserve">
    <value>ಅಪ್ಲಿಕೇಶನ್ ದೋಷ ಸಂಭವಿಸಿದೆ</value>
  </data>
  <data name="InvalidClaimNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಹಕ್ಕು ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidClearingAgentName" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಕ್ಲಿಯರಿಂಗ್ ಏಜೆಂಟ್ ಹೆಸರು</value>
  </data>
  <data name="InvalidCompanySelection" xml:space="preserve">
    <value>Prefixsuffix ಶಾಖೆ ನಿರ್ದಿಷ್ಟ ರಚಿತವಾಗುತ್ತದೆ.</value>
  </data>
  <data name="InvalidCompetitorName" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಪ್ರತಿಸ್ಪರ್ಧಿ ಹೆಸರು</value>
  </data>
  <data name="InvalidDate" xml:space="preserve">
    <value>ಅಮಾನ್ಯ ದಿನಾಂಕ</value>
  </data>
  <data name="InvalidDateFormat" xml:space="preserve">
    <value>ಅಮಾನ್ಯ ದಿನಾಂಕ ಸ್ವರೂಪ</value>
  </data>
  <data name="InvalidDecimal" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ದಶಮಾಂಶ</value>
  </data>
  <data name="InvalidDecimalorvalueadjustmentisblankorzero" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ದಶಮಾಂಶ ಅಥವಾ ಮೌಲ್ಯವನ್ನು ಖಾಲಿ ಅಥವಾ ಶೂನ್ಯ</value>
  </data>
  <data name="InvalidDefectCode" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ದೋಷದ ಕೋಡ್</value>
  </data>
  <data name="InvalidDeliveryNoteNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಡೆಲಿವರಿ ನೋಟ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidDeliveryNoteNumberOrReturnIsAlreadyDone" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಡೆಲಿವರಿ ಗಮನಿಸಿ ಸಂಖ್ಯೆ ಅಥವಾ ರಿಟರ್ನ್ ಈಗಾಗಲೇ ಮಾಡಲಾಗುತ್ತದೆ</value>
  </data>
  <data name="InvalidEmail" xml:space="preserve">
    <value>ಅಮಾನ್ಯ ಇಮೇಲ್</value>
  </data>
  <data name="InvalidEmployee" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ನೌಕರ</value>
  </data>
  <data name="InvalidFile" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಫೈಲ್</value>
  </data>
  <data name="InvalidFinancier" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಫೈನಾನ್ಷಿಯರ್</value>
  </data>
  <data name="InvalidFloatDeliveryNoteNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಫ್ಲೋಟ್ ವಿತರಣಾ ಟಿಪ್ಪಣಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="Invalidfunctiongroup" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಫಂಕ್ಷನ್ ಗುಂಪು</value>
  </data>
  <data name="InvalidInspectionNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ತಪಾಸಣೆ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidInsuranceCompany" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಇನ್ಶುರೆನ್ಸ್ ಕಂಪನಿ</value>
  </data>
  <data name="InvalidInsuranceParty" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ವಿಮೆ ಪಕ್ಷದ</value>
  </data>
  <data name="InvalidInternalInvoiceNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಆಂತರಿಕ ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidInvoiceNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidIssueRequestNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಸಂಚಿಕೆ ವಿನಂತಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidJobCardNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಉದ್ಯೋಗ ಚೀಟಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidkitMakingNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯ ಮಾಡುವ ಕಿಟ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidMachineTransferNoteNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಯಂತ್ರ ಟ್ರಾನ್ಸ್ಫರ್ ನೋಟ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidMobile" xml:space="preserve">
    <value>ಅಮಾನ್ಯ ಮೊಬೈಲ್</value>
  </data>
  <data name="InvalidMobile1" xml:space="preserve">
    <value>ಅಮಾನ್ಯ ಮೊಬೈಲ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidMobileNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯ ಮೊಬೈಲ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidModel" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಮಾದರಿಯನ್ನು</value>
  </data>
  <data name="invalidmodelormodelisinactive" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಮಾದರಿ ಅಥವಾ ಮಾದರಿ ನಿಷ್ಕ್ರಿಯ</value>
  </data>
  <data name="InvalidName" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಪಾರ್ಟಿ ಹೆಸರು</value>
  </data>
  <data name="InvalidOperationException" xml:space="preserve">
    <value>ಡೇಟಾಬೇಸ್ ದೋಷ ಸಂಭವಿಸಿದೆ</value>
  </data>
  <data name="InvalidOrderNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಖರೀದಿ ಆದೇಶವನ್ನು ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidorinactiveBranch" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಅಥವಾ ನಿಷ್ಕ್ರಿಯ ಶಾಖೆ</value>
  </data>
  <data name="InvalidorinactiveParty" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಅಥವಾ ನಿಷ್ಕ್ರಿಯ ಪಕ್ಷದ</value>
  </data>
  <data name="Invalidpartnumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಭಾಗ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="invalidpartnumberorpartnumberisinactive" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಭಾಗ ಸಂಖ್ಯೆ ಅಥವಾ ಭಾಗ ಸಂಖ್ಯೆ ನಿಷ್ಕ್ರಿಯ</value>
  </data>
  <data name="InvalidPartsInvoice" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಭಾಗಗಳು ಸರಕುಪಟ್ಟಿ</value>
  </data>
  <data name="InvalidPartsOrderNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಭಾಗಗಳು ಸಂಖ್ಯೆ ಆದೇಶ</value>
  </data>
  <data name="InvalidParty" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಪಾರ್ಟಿ</value>
  </data>
  <data name="InvalidPercentage" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಶೇಕಡಾವಾರು</value>
  </data>
  <data name="InvalidPhone" xml:space="preserve">
    <value>ಅಮಾನ್ಯ ಫೋನ್</value>
  </data>
  <data name="InvalidPhoneNo" xml:space="preserve">
    <value>ಅಮಾನ್ಯ ಮೊಬೈಲ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidPhoneNo1" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ದೂರವಾಣಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidProduct" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಉತ್ಪನ್ನ</value>
  </data>
  <data name="InvalidProductUniqueNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಉತ್ಪನ್ನ ಅನನ್ಯ ಗುರುತು</value>
  </data>
  <data name="InvalidProductUniqueNumber1" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಉತ್ಪನ್ನ ಅಸದೃಶ ಅಥವಾ ಉತ್ಪನ್ನ ಸಕ್ರಿಯವಾಗಿಲ್ಲ</value>
  </data>
  <data name="InvalidPurchaseInvoiceNum" xml:space="preserve">
    <value>ಅಮಾನ್ಯ ಖರೀದಿ ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidPurchaseInvoiceNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯ ಖರೀದಿ ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidPurchaseOrderNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಪರ್ಚೇಸ್ ಆರ್ಡರ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidQuantity" xml:space="preserve">
    <value>ಅಮಾನ್ಯ ಪ್ರಮಾಣ</value>
  </data>
  <data name="InvalidQuotationNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಉದ್ಧರಣಾ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidRate" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ದರ.</value>
  </data>
  <data name="InvalidReceiptNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ರಸೀತಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidRegisteredMobileNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ನೋಂದಾಯಿತ ಮೊಬೈಲ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidRequestCoreNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ವಿನಂತಿ ಕೋರ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidSalesOrderNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಮಾರಾಟದ ಆದೇಶ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="invalidselection" xml:space="preserve">
    <value>ಅಮಾನ್ಯ ಆಯ್ಕೆ</value>
  </data>
  <data name="InvalidSerialNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಸರಣಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidServiceRequestNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಸೇವೆ ವಿನಂತಿಯನ್ನು ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidStandardPackingQuantity" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಸ್ಟ್ಯಾಂಡರ್ಡ್ ಪ್ಯಾಕಿಂಗ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="invalidStockCheckConfirmationNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಸ್ಟಾಕ್ ಚೆಕ್ ದೃಢೀಕರಣ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="invalidStockCheckRequestNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಸ್ಟಾಕ್ ಚೆಕ್ ವಿನಂತಿಯನ್ನು ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidStockTransferNoteNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಸ್ಟಾಕ್ ವರ್ಗಾವಣೆ ಟಿಪ್ಪಣಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidSupplier" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಪೂರೈಕೆದಾರ</value>
  </data>
  <data name="InvalidTool" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಸಾಧನ</value>
  </data>
  <data name="InvalidTransactionNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ವ್ಯವಹಾರ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidTransporterName" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಟ್ರಾನ್ಸ್ಪೋರ್ಟರ್ ಹೆಸರು</value>
  </data>
  <data name="InValidUniqueIdentificationNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ವಿಶಿಷ್ಟ ಗುರುತಿನ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvalidValue" xml:space="preserve">
    <value>ಅಮಾನ್ಯ ಮೌಲ್ಯ</value>
  </data>
  <data name="InvalidWarehouse" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ವೇರ್ಹೌಸ್</value>
  </data>
  <data name="invalidWarrantyClaimNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಖಾತರಿ ಹಕ್ಕು ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InventoryData" xml:space="preserve">
    <value>ಇನ್ವೆಂಟರಿ ಡೇಟಾ</value>
  </data>
  <data name="Invoice" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ</value>
  </data>
  <data name="InvoiceAddress" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ವಿಳಾಸ</value>
  </data>
  <data name="InvoiceAleadyCreatedforThisPurchaseOrderNumber" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಈಗಾಗಲೇ ಈ ಪರ್ಚೇಸ್ ಆರ್ಡರ್ ಸಂಖ್ಯೆ ರಚಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="InvoiceAlreadyCreatedForThisSerialNumber" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಈಗಾಗಲೇ ಕ್ರಮ ಸಂಖ್ಯೆ ರಚಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="InvoiceAlreadyDone" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಈಗಾಗಲೇ ಮಾಡಲಾಗುತ್ತದೆ</value>
  </data>
  <data name="InvoiceAmount" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ವೊತ್ತ</value>
  </data>
  <data name="InvoiceAmountLocalCurrency" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ವೊತ್ತ ಸ್ಥಳೀಯ ಕರೆನ್ಸಿ</value>
  </data>
  <data name="InvoiceCancellation" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ರದ್ದತಿ</value>
  </data>
  <data name="InvoiceCancellationDate" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ರದ್ದು ದಿನಾಂಕ</value>
  </data>
  <data name="InvoiceCancellationNumber" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ರದ್ದತಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvoiceCost" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ವೆಚ್ಚ</value>
  </data>
  <data name="InvoiceDate" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ದಿನಾಂಕ</value>
  </data>
  <data name="InvoiceDetail" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ವಿವರ</value>
  </data>
  <data name="InvoiceDetails" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ವಿವರಗಳು</value>
  </data>
  <data name="InvoicedQuantity" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಪ್ರಮಾಣ</value>
  </data>
  <data name="InvoiceFieldSearch" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಫೀಲ್ಡ್ ಹುಡುಕು</value>
  </data>
  <data name="InvoiceIsAlreadyDoneForTheSelectedSalesOrderNumber" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಈಗಾಗಲೇ ಆಯ್ಕೆ ಮಾರಾಟದ ಆದೇಶ ಸಂಖ್ಯೆ ಮಾಡಲಾಗುತ್ತದೆ</value>
  </data>
  <data name="InvoiceMachineAmount" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಯಂತ್ರ ವೊತ್ತ</value>
  </data>
  <data name="InvoiceNumber" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="InvoiceNumberNotFound" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ ಕಂಡುಬಂದಿಲ್ಲ</value>
  </data>
  <data name="InvoiceQuantity" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಪ್ರಮಾಣ</value>
  </data>
  <data name="InvoiceQuantitySIR" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಪ್ರಮಾಣ</value>
  </data>
  <data name="InvoiceRate" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ದರ</value>
  </data>
  <data name="InvoiceReturnDate" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಹಿಂದಿರುಗುವ ದಿನಾಂಕವನ್ನು</value>
  </data>
  <data name="InvoiceReturnDetails" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ರಿಟರ್ನ್ ವಿವರಗಳು</value>
  </data>
  <data name="Invoicesearch" xml:space="preserve">
    <value>ಸೇವೆ ಸರಕುಪಟ್ಟಿ ಹುಡುಕು</value>
  </data>
  <data name="InvoiceTotalAmount" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಒಟ್ಟು ವೊತ್ತ</value>
  </data>
  <data name="Invoicetype" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಪ್ರಕಾರ</value>
  </data>
  <data name="IsActive" xml:space="preserve">
    <value>ಸಕ್ರಿಯವಾಗಿದೆ?</value>
  </data>
  <data name="IsAdmin" xml:space="preserve">
    <value>ನಿರ್ವಹಣೆ ಈಸ್</value>
  </data>
  <data name="IsAllPartsClosed" xml:space="preserve">
    <value>ಎಲ್ಲಾ ಪಾರ್ಟ್ಸ್ ಮುಚ್ಚಲಾಗಿದೆ</value>
  </data>
  <data name="isbaseamountincluded" xml:space="preserve">
    <value>ಬೇಸ್ ವೊತ್ತ ಸೇರಿಸಲಾಗಿದೆ?</value>
  </data>
  <data name="IsBillTo" xml:space="preserve">
    <value>ಬಿಲ್ ಹೊಂದಿದೆ?</value>
  </data>
  <data name="IsCancelled" xml:space="preserve">
    <value>ರದ್ದುಗೊಳಿಸಲಾಗಿದೆ ಇದೆ?</value>
  </data>
  <data name="IsChargeable" xml:space="preserve">
    <value>ಪೂರಣಮಾಡಬಲ್ಲ ಈಸ್</value>
  </data>
  <data name="IsCompanySpecific" xml:space="preserve">
    <value>ಕಂಪನಿ ನಿರ್ದಿಷ್ಟ</value>
  </data>
  <data name="iscomponent" xml:space="preserve">
    <value>ಅಂಶವಾಗಿದೆ?</value>
  </data>
  <data name="IsConsiderForDemand" xml:space="preserve">
    <value>ಬೇಡಿಕೆ ಪರಿಗಣಿಸುತ್ತಾರೆ?</value>
  </data>
  <data name="IsCSA" xml:space="preserve">
    <value>ಸಿಎಸ್ಎ ಈಸ್?</value>
  </data>
  <data name="IsCustomer" xml:space="preserve">
    <value>IsCustomer</value>
  </data>
  <data name="IsDealer" xml:space="preserve">
    <value>ಡೀಲರ್?</value>
  </data>
  <data name="IsDefault" xml:space="preserve">
    <value>ಡೀಫಾಲ್ಟ್?</value>
  </data>
  <data name="IsDefaultBinLocationRequired" xml:space="preserve">
    <value>ಡೀಫಾಲ್ಟ್ ಬಿನ್ ಸ್ಥಳ ಅಗತ್ಯವಿದೆ</value>
  </data>
  <data name="IsDefaultConsigneeRequired" xml:space="preserve">
    <value>ಡೀಫಾಲ್ಟ್ consignee ಅಗತ್ಯವಿದೆ</value>
  </data>
  <data name="IsDefaultContact" xml:space="preserve">
    <value>ಡೀಫಾಲ್ಟ್ ಸಂಪರ್ಕ ಹೊಂದಿದೆ?</value>
  </data>
  <data name="IsDefaultContactRequired" xml:space="preserve">
    <value>ಡೀಫಾಲ್ಟ್ ಸಂಪರ್ಕ ಅಗತ್ಯವಿದೆ</value>
  </data>
  <data name="IsDefaultWarehouseRequired" xml:space="preserve">
    <value>ಡೀಫಾಲ್ಟ್ ಗೋದಾಮಿನ ಅಗತ್ಯವಿದೆ</value>
  </data>
  <data name="IsEmail" xml:space="preserve">
    <value>ಇಮೇಲ್</value>
  </data>
  <data name="IsEmployee" xml:space="preserve">
    <value>ನೌಕರ</value>
  </data>
  <data name="IsExport" xml:space="preserve">
    <value>ರಫ್ತು ಆಗಿದೆ?</value>
  </data>
  <data name="IsExternal" xml:space="preserve">
    <value>ಹೊರಗಿನ</value>
  </data>
  <data name="IsFOC" xml:space="preserve">
    <value>ಎಫ್ಓಸಿ ಈಸ್</value>
  </data>
  <data name="IsFrom" xml:space="preserve">
    <value>ಈಸ್?</value>
  </data>
  <data name="IsFullReturn" xml:space="preserve">
    <value>ಸಂಪೂರ್ಣವಾಗಿ ಮರಳುವುದು</value>
  </data>
  <data name="IsGDRClosed" xml:space="preserve">
    <value>ಜಿಡಿಆರ್ ಮುಚ್ಚಲಾಗಿದೆ</value>
  </data>
  <data name="IsGeneralshift" xml:space="preserve">
    <value>ಜನರಲ್ ಶಿಫ್ಟ್?</value>
  </data>
  <data name="IsGoodCore" xml:space="preserve">
    <value>ಗುಡ್ ಕೋರ್ ಈಸ್?</value>
  </data>
  <data name="isHazardous" xml:space="preserve">
    <value>ಅಪಾಯಕಾರಿ?</value>
  </data>
  <data name="IsHeadOffice" xml:space="preserve">
    <value>ಹೆಡ್ ಆಫೀಸ್ ಈಸ್</value>
  </data>
  <data name="IsHypothecated" xml:space="preserve">
    <value>ಹೈಪೊಥಿಕೇಶನ್ ಈಸ್?</value>
  </data>
  <data name="isinactive" xml:space="preserve">
    <value>ನಿಷ್ಕ್ರಿಯ</value>
  </data>
  <data name="IsInvoiceCancelled" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ರದ್ದುಗೊಳಿಸಲಾಗಿದೆ ಇದೆ?</value>
  </data>
  <data name="IsKitPart" xml:space="preserve">
    <value>ಕಿಟ್ ಭಾಗವಾಗಿದೆ?</value>
  </data>
  <data name="IsLocal" xml:space="preserve">
    <value>ಸ್ಥಳೀಯ ಈಸ್?</value>
  </data>
  <data name="IsLock" xml:space="preserve">
    <value>IsLock</value>
  </data>
  <data name="IsMandatory" xml:space="preserve">
    <value>ಕಡ್ಡಾಯ?</value>
  </data>
  <data name="IsMandtory" xml:space="preserve">
    <value>ಕಡ್ಡಾಯ?</value>
  </data>
  <data name="IsModelSpecific" xml:space="preserve">
    <value>ಮಾದರಿ ನಿರ್ದಿಷ್ಟ?</value>
  </data>
  <data name="IsOEM" xml:space="preserve">
    <value>OEM ಈಸ್?</value>
  </data>
  <data name="IsOperationCompleted" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ಮುಗಿದ</value>
  </data>
  <data name="IsPaid" xml:space="preserve">
    <value>ಹಣ</value>
  </data>
  <data name="IsPartialReturn" xml:space="preserve">
    <value>ಭಾಗಶಃ ಮರಳುವುದು</value>
  </data>
  <data name="IsPartofKit" xml:space="preserve">
    <value>ಕಿಟ್ ಭಾಗವಾಗಿದೆ?</value>
  </data>
  <data name="IsProductValidate" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಸ್ಥಿರೀಕರಿಸಿಲಿಂಕ್ ಈಸ್?</value>
  </data>
  <data name="IsReserved" xml:space="preserve">
    <value>ಕಾಯ್ದಿರಿಸಲಾಗಿದೆ?</value>
  </data>
  <data name="IsReturnable" xml:space="preserve">
    <value>ಹಿಂದಿರುಗಿಸಬಹುದಾದ ಈಸ್</value>
  </data>
  <data name="IsReturnableBasis" xml:space="preserve">
    <value>ಹಿಂದಿರುಗಿಸಬಹುದಾದ ಆಧಾರವಾಗಿದೆ</value>
  </data>
  <data name="IsReWork" xml:space="preserve">
    <value>ಮರು ಕೆಲಸ</value>
  </data>
  <data name="IsSms" xml:space="preserve">
    <value>ಎಸ್ಎಮ್ಎಸ್ ಆಗಿದೆ</value>
  </data>
  <data name="IsStockUnBlocked" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಅನಿರ್ಬಂಧಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="IssueArea" xml:space="preserve">
    <value>ಸಂಚಿಕೆ ಪ್ರದೇಶ</value>
  </data>
  <data name="IssueCore" xml:space="preserve">
    <value>ಸಂಚಿಕೆ ಕೋರ್</value>
  </data>
  <data name="IssueCoreisalreadyCreated" xml:space="preserve">
    <value>ಸಂಚಿಕೆ ಕೋರ್ ಈಗಾಗಲೇ ರಚಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="IssueCoreNumber" xml:space="preserve">
    <value>ಸಂಚಿಕೆ ಕೋರ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="IssuedAmount" xml:space="preserve">
    <value>ನೀಡಲಾಗಿದೆ ವೊತ್ತ</value>
  </data>
  <data name="TotalIssuedvalueshouldnotbegreaterthanCommittedvalue" xml:space="preserve">
    <value>ಒಟ್ಟು ನೀಡಲಾಗಿದೆ ಮೌಲ್ಯ ಬದ್ಧವಾಗಿದೆ ಮೌಲ್ಯವನ್ನು ಹೆಚ್ಚು ಮಾಡಬಾರದು</value>
  </data>
  <data name="issueddate" xml:space="preserve">
    <value>ನೀಡಲಾಗಿದೆ ದಿನಾಂಕ</value>
  </data>
  <data name="issueddatesholudbelessthanorequaltocurrentdate" xml:space="preserve">
    <value>ನೀಡಲಾಗಿದೆ ದಿನಾಂಕ ಕಡಿಮೆ ಅಥವಾ ಪ್ರಸ್ತುತ ದಿನಾಂಕ ಸಮಾನವಾಗಿರಬೇಕು sholud</value>
  </data>
  <data name="IssueDetails" xml:space="preserve">
    <value>ಸಂಚಿಕೆ ವಿವರಗಳು</value>
  </data>
  <data name="IssuedQty" xml:space="preserve">
    <value>ನೀಡಲಾಗಿದೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="IssuedQuantity" xml:space="preserve">
    <value>ನೀಡಲಾಗಿದೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="IssuedTo" xml:space="preserve">
    <value>ನೀಡಲಾಗಿದೆ</value>
  </data>
  <data name="FOCIssuedValue" xml:space="preserve">
    <value>ಉಚಿತವಾಗಿ ನೀಡಲಾಗಿದೆ ಮೌಲ್ಯ</value>
  </data>
  <data name="IssueRequestDate" xml:space="preserve">
    <value>ಸಂಚಿಕೆ ವಿನಂತಿ ದಿನಾಂಕ ತೆಪ್ಪ</value>
  </data>
  <data name="IssueRequestNumber" xml:space="preserve">
    <value>ಸಂಚಿಕೆ ವಿನಂತಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="Issues" xml:space="preserve">
    <value>ಸಮಸ್ಯೆಗಳು</value>
  </data>
  <data name="IssueSubArea" xml:space="preserve">
    <value>ಉಪ ಏರಿಯಾ ನೀಡಿ</value>
  </data>
  <data name="IssueType" xml:space="preserve">
    <value>ಸಂಚಿಕೆ ಪ್ರಕಾರ</value>
  </data>
  <data name="IsSundryCompleted" xml:space="preserve">
    <value>ಸಂಡ್ರಿ ಮುಗಿದ</value>
  </data>
  <data name="IsUnderBreakDown" xml:space="preserve">
    <value>ಬ್ರೇಕ್ ಡೌನ್ ಹಂತದಲ್ಲಿದೆ?</value>
  </data>
  <data name="isunderwarranty" xml:space="preserve">
    <value>ವಾರಂಟಿ?</value>
  </data>
  <data name="isversionallowed" xml:space="preserve">
    <value>ಆವೃತ್ತಿ ಅವಕಾಶವಿದೆಯೇ?</value>
  </data>
  <data name="IsVersionEnabled" xml:space="preserve">
    <value>ಆವೃತ್ತಿ ಸಶಕ್ತ?</value>
  </data>
  <data name="IsWarranty" xml:space="preserve">
    <value>ಖಾತರಿ ಆಗಿದೆ?</value>
  </data>
  <data name="ItemNumber" xml:space="preserve">
    <value>ಕ್ರಮ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ItemType" xml:space="preserve">
    <value>ಐಟಂ ಪ್ರಕಾರ</value>
  </data>
  <data name="January" xml:space="preserve">
    <value>ಜನವರಿ</value>
  </data>
  <data name="Jobamendedwillcreatenewversionwanttoproceed" xml:space="preserve">
    <value>ಜಾಬ್ ತಿದ್ದುಪಡಿ, ಹೊಸ ಆವೃತ್ತಿ ರಚಿಸುತ್ತದೆ ಮುಂದುವರೆಯಲು ಬಯಸುವ?</value>
  </data>
  <data name="JobCard" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್</value>
  </data>
  <data name="JobCardAbandonReason" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ಕಾರಣ ತ್ಯಜಿಸು</value>
  </data>
  <data name="JobCardActivity" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ಚಟುವಟಿಕೆ</value>
  </data>
  <data name="JobCardActivityDetail" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ಚಟುವಟಿಕೆ ವಿವರ</value>
  </data>
  <data name="JobcardActivityStatus" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ಚಟುವಟಿಕೆ ಸ್ಥಿತಿ</value>
  </data>
  <data name="JobcardArchived" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ಆರ್ಕೈವ್</value>
  </data>
  <data name="JobCardBillingReport" xml:space="preserve">
    <value>ಉದ್ಯೋಗ ಚೀಟಿ ಬಿಲ್ಲಿಂಗ್ ವರದಿ</value>
  </data>
  <data name="JobCardClosureDate" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ಸಮಾಪ್ತಿ ದಿನಾಂಕ</value>
  </data>
  <data name="JobCardCushionHours" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ಕುಷನ್ ಅವರ್ಸ್</value>
  </data>
  <data name="JobCardDate" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ದಿನಾಂಕ</value>
  </data>
  <data name="JobCardDelayReason" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ವಿಳಂಬ ಕಾರಣ</value>
  </data>
  <data name="JobCardFieldSearch" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ಫೀಲ್ಡ್ ಹುಡುಕು</value>
  </data>
  <data name="JobCardformsg" xml:space="preserve">
    <value>ಉದ್ಯೋಗ ಚೀಟಿ</value>
  </data>
  <data name="JobCardHOApproval" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ಹೋ ಅನುಮೋದನೆ</value>
  </data>
  <data name="JobCardisalreadyCreated" xml:space="preserve">
    <value>ಉದ್ಯೋಗ ಚೀಟಿ ಈಗಾಗಲೇ ರಚಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="JobCardisalreadyCreatedForCSA" xml:space="preserve">
    <value>ಅಳಿಸಲಾಗಿಲ್ಲ ಉದ್ಯೋಗ ಚೀಟಿ ಈಗಾಗಲೇ ರಚಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="JobCardisalreadyCreatedForCSAedit" xml:space="preserve">
    <value>ಸಂಪಾದಿತವಾಗಿಲ್ಲ ಉದ್ಯೋಗ ಚೀಟಿ ಈಗಾಗಲೇ ರಚಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="JobCardisalreadycreatedforthisServiceRequestNumber" xml:space="preserve">
    <value>ಉದ್ಯೋಗ ಚೀಟಿ ಈಗಾಗಲೇ ಈ ಸೇವೆ ವಿನಂತಿಯನ್ನು ಸಂಖ್ಯೆ ರಚಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="JobCardIsAlreadyDoneforQuotation" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ಈಗಾಗಲೇ ಈ ಉದ್ಧರಣಾ ಸಂಖ್ಯೆ ಮಾಡಲಾಗುತ್ತದೆ</value>
  </data>
  <data name="JobCardisOpenfortheselectedProductdoyouwanttocontinue" xml:space="preserve">
    <value>ಉದ್ಯೋಗ ಚೀಟಿ ಆಯ್ಕೆ ಉತ್ಪನ್ನ ತೆರೆದುಕೊಳ್ಳುತ್ತದೆ ನೀವು ಮುಂದುವರಿಸಲು ಬಯಸುತ್ತೀರಾ?</value>
  </data>
  <data name="JobCardNumber" xml:space="preserve">
    <value>ಉದ್ಯೋಗ ಚೀಟಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="JobCardNumber1" xml:space="preserve">
    <value>ಉದ್ಯೋಗ ಚೀಟಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="JobCardNumberNotExists" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ jobcard ಸಂಖ್ಯೆ ದಾಖಲಿಸಿದರೆ</value>
  </data>
  <data name="JobcardNumbernotfound" xml:space="preserve">
    <value>ಉದ್ಯೋಗ ಚೀಟಿ ಸಂಖ್ಯೆ ಕಂಡುಬಂದಿಲ್ಲ</value>
  </data>
  <data name="JobCardNumberSearch" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ಸಂಖ್ಯೆ ಹುಡುಕಾಟದ</value>
  </data>
  <data name="JobCardOEMApproval" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ಅನುಮೋದನೆ</value>
  </data>
  <data name="JobCardOpenDate" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ತೆರೆಯಿರಿ ದಿನಾಂಕ</value>
  </data>
  <data name="JobCardPartsReceipt" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ಭಾಗಗಳು ರಸೀತಿ</value>
  </data>
  <data name="JobCardPartsReturn" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ಭಾಗಗಳು ರಿಟರ್ನ್</value>
  </data>
  <data name="JobCardPendingCount" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ಬಾಕಿ ನ್ಯಾಷನಲ್</value>
  </data>
  <data name="JobCardReworkReport" xml:space="preserve">
    <value>Job Card ಮರು ವರದಿ</value>
  </data>
  <data name="JobCardSearch" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ಹುಡುಕು</value>
  </data>
  <data name="JobCardStatus" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ಸ್ಥಿತಿ</value>
  </data>
  <data name="JobCardSummary" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ಸಾರಾಂಶ</value>
  </data>
  <data name="JobCardVersion" xml:space="preserve">
    <value>ರೂಪಾಂತರ</value>
  </data>
  <data name="JobCardWIPCount" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ವಿಪ್ ನ್ಯಾಷನಲ್</value>
  </data>
  <data name="JobDescription" xml:space="preserve">
    <value>ಜಾಬ್ ವಿವರಣೆ</value>
  </data>
  <data name="JobDetails" xml:space="preserve">
    <value>ಜಾಬ್ ವಿವರಗಳು</value>
  </data>
  <data name="JobEndDate" xml:space="preserve">
    <value>ಜಾಬ್ ಎಂಡ್ ದಿನಾಂಕ</value>
  </data>
  <data name="JobPriority" xml:space="preserve">
    <value>ಜಾಬ್ ಆದ್ಯತಾ</value>
  </data>
  <data name="JobSiteAddress" xml:space="preserve">
    <value>ಜಾಬ್ ಸೈಟ್ ವಿಳಾಸ</value>
  </data>
  <data name="JobStartDate" xml:space="preserve">
    <value>ಉದ್ಯೋಗ ಪ್ರಾರಂಭ ದಿನಾಂಕ</value>
  </data>
  <data name="JobType" xml:space="preserve">
    <value>ಜಾಬ್ ಕೌಟುಂಬಿಕತೆ</value>
  </data>
  <data name="JoinedTables" xml:space="preserve">
    <value>Joined ಟೇಬಲ್ಸ್</value>
  </data>
  <data name="JoinWith" xml:space="preserve">
    <value>ಸೇರಲು</value>
  </data>
  <data name="July" xml:space="preserve">
    <value>ಜುಲೈ</value>
  </data>
  <data name="June" xml:space="preserve">
    <value>ಜೂನ್</value>
  </data>
  <data name="KitBom" xml:space="preserve">
    <value>ಕಿಟ್ ಬೊಮ್</value>
  </data>
  <data name="KitBreaking" xml:space="preserve">
    <value>ಕಿಟ್ ಬ್ರೇಕಿಂಗ್</value>
  </data>
  <data name="KitBreakingDate" xml:space="preserve">
    <value>ಕಿಟ್ ಬ್ರೇಕಿಂಗ್ ದಿನಾಂಕ</value>
  </data>
  <data name="KitBreakingNumber" xml:space="preserve">
    <value>ಕಿಟ್ ಬ್ರೇಕಿಂಗ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="KitMaking" xml:space="preserve">
    <value>ಕಿಟ್ ಮೇಕಿಂಗ್</value>
  </data>
  <data name="KitMakingDate" xml:space="preserve">
    <value>ತಯಾರಿಸುವ ಕಿಟ್ ದಿನಾಂಕ</value>
  </data>
  <data name="KitMakingNumber" xml:space="preserve">
    <value>ತಯಾರಿಸುವ ಕಿಟ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="KitMakingNumer" xml:space="preserve">
    <value>ತಯಾರಿಸುವ ಕಿಟ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="KitPartDescription" xml:space="preserve">
    <value>ಕಿಟ್ ಭಾಗ ವಿವರಣೆ</value>
  </data>
  <data name="KitPartFreeStock" xml:space="preserve">
    <value>ಕಿಟ್ ಭಾಗ ಉಚಿತ ಸ್ಟಾಕ್</value>
  </data>
  <data name="KitPartNumbe" xml:space="preserve">
    <value>ಕಿಟ್ ಭಾಗ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="KitPartNumber" xml:space="preserve">
    <value>ಕಿಟ್ ಭಾಗ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="KnowledgeBase" xml:space="preserve">
    <value>ಜ್ಞಾನ</value>
  </data>
  <data name="Labour" xml:space="preserve">
    <value>ಲೇಬರ್</value>
  </data>
  <data name="LabourAmount" xml:space="preserve">
    <value>ಲೇಬರ್ ವೊತ್ತ</value>
  </data>
  <data name="LabourApproved" xml:space="preserve">
    <value>ಲೇಬರ್ ಅನುಮೋದನೆ</value>
  </data>
  <data name="LabourCost" xml:space="preserve">
    <value>ಲೇಬರ್ ವೆಚ್ಚ</value>
  </data>
  <data name="LandingCost" xml:space="preserve">
    <value>ಲ್ಯಾಂಡಿಂಗ್ ವೆಚ್ಚ</value>
  </data>
  <data name="LandingCostRate" xml:space="preserve">
    <value>ಲ್ಯಾಂಡಿಂಗ್ ವೆಚ್ಚ ದರ</value>
  </data>
  <data name="LandingPage" xml:space="preserve">
    <value>ಲ್ಯಾಂಡಿಂಗ್ ಪುಟ</value>
  </data>
  <data name="Landline" xml:space="preserve">
    <value>ಸ್ಥಿರ ದೂರವಾಣಿ</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>ಭಾಷೆ</value>
  </data>
  <data name="LanguageName" xml:space="preserve">
    <value>ಭಾಷೆ</value>
  </data>
  <data name="Last" xml:space="preserve">
    <value>ಕೊನೆಯ</value>
  </data>
  <data name="LastInvoicedDate" xml:space="preserve">
    <value>ಕೊನೆಯ ಸರಕುಪಟ್ಟಿ ದಿನಾಂಕ</value>
  </data>
  <data name="LastStockDate" xml:space="preserve">
    <value>ಕೊನೆಯ ಸ್ಟಾಕ್ ದಿನಾಂಕ</value>
  </data>
  <data name="LastStockUpdatedDate" xml:space="preserve">
    <value>ಕೊನೆಯ ಸ್ಟಾಕ್ ದಿನಾಂಕ Updated</value>
  </data>
  <data name="LC" xml:space="preserve">
    <value>ಹಣಭರವಸೆಯ ಪತ್ರ</value>
  </data>
  <data name="LCAmount" xml:space="preserve">
    <value>ಕ್ರೆಡಿಟ್ ವೊತ್ತ ಪತ್ರ</value>
  </data>
  <data name="LCAmountShort" xml:space="preserve">
    <value>ಎಲ್ಸಿ ವೊತ್ತ</value>
  </data>
  <data name="LCAmtCanNotBeGreaterThanMachineTotalAmt" xml:space="preserve">
    <value>ಎಲ್ಸಿ ವೊತ್ತ ಯಂತ್ರ ಒಟ್ಟು ವೊತ್ತ ಹೆಚ್ಚಾಗಿದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="LCAmtCanNotBeZero" xml:space="preserve">
    <value>ಎಲ್ಸಿ ವೊತ್ತ ಶೂನ್ಯವಾಗಿರುವುದಿಲ್ಲ</value>
  </data>
  <data name="LCDate" xml:space="preserve">
    <value>ಕ್ರೆಡಿಟ್ ದಿನಾಂಕ ಪತ್ರ</value>
  </data>
  <data name="LCDateShort" xml:space="preserve">
    <value>ಎಲ್ಸಿ ದಿನಾಂಕ</value>
  </data>
  <data name="LCDetails" xml:space="preserve">
    <value>ಕ್ರೆಡಿಟ್ ವಿವರಗಳು ಪತ್ರ</value>
  </data>
  <data name="LCExpiryDate" xml:space="preserve">
    <value>ಕ್ರೆಡಿಟ್ ಅಂತ್ಯ ದಿನಾಂಕ ಪತ್ರ</value>
  </data>
  <data name="LCExpiryDateShort" xml:space="preserve">
    <value>ಎಲ್ಸಿ ಅಂತ್ಯ ದಿನಾಂಕ</value>
  </data>
  <data name="LCNum" xml:space="preserve">
    <value>ಎಲ್ ಸಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="LCNumber" xml:space="preserve">
    <value>ಎಲ್ ಸಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="LCRemarks" xml:space="preserve">
    <value>ಕ್ರೆಡಿಟ್ ರಿಮಾರ್ಕ್ಸ್ ಪತ್ರ</value>
  </data>
  <data name="leadtime" xml:space="preserve">
    <value>ಉತ್ಪಾದನಾಕಾಲ</value>
  </data>
  <data name="LeadTimeInDays" xml:space="preserve">
    <value>ಡೇಸ್ ಟೈಮ್ ಲೀಡ್</value>
  </data>
  <data name="LeadTimeTracker" xml:space="preserve">
    <value>ಮುನ್ನಡೆ ಸಮಯ ಟ್ರ್ಯಾಕರ್</value>
  </data>
  <data name="LessThan" xml:space="preserve">
    <value>ಕಡಿಮೆ</value>
  </data>
  <data name="Like" xml:space="preserve">
    <value>ಲೈಕ್</value>
  </data>
  <data name="LineFullyCompletedDate" xml:space="preserve">
    <value>ಲೈನ್ ಸಂಪೂರ್ಣವಾಗಿ ದಿನಾಂಕ ಮುಗಿದಿದೆ</value>
  </data>
  <data name="Link" xml:space="preserve">
    <value>ಕೊಂಡಿ</value>
  </data>
  <data name="LinkIsInactive" xml:space="preserve">
    <value>ಲಿಂಕ್ ನಿಷ್ಕ್ರಿಯ</value>
  </data>
  <data name="LinkName" xml:space="preserve">
    <value>ಲಿಂಕ್ ಹೆಸರು</value>
  </data>
  <data name="Links" xml:space="preserve">
    <value>ಲಿಂಕ್ಸ್</value>
  </data>
  <data name="LinkURL" xml:space="preserve">
    <value>ಲಿಂಕ್ URL</value>
  </data>
  <data name="ListPrice" xml:space="preserve">
    <value>ಪಟ್ಟಿಬೆಲೆ</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>ಲೋಡ್</value>
  </data>
  <data name="LoadingOrUnloadingAmount" xml:space="preserve">
    <value>ಲೋಡ್ / ಇಳಿಸುವಿಕೆಯೊಂದಿಗೆ ವೊತ್ತ</value>
  </data>
  <data name="Local" xml:space="preserve">
    <value>ಸ್ಥಳೀಯ</value>
  </data>
  <data name="Locale" xml:space="preserve">
    <value>ಪ್ರದೇಶ</value>
  </data>
  <data name="LocaleDetails" xml:space="preserve">
    <value>ಲೊಕೇಲ್ ವಿವರಗಳು</value>
  </data>
  <data name="LocaledetailsarenotavaliableforBrand" xml:space="preserve">
    <value>ಲೊಕೇಲ್ ವಿವರಗಳು ಬ್ರ್ಯಾಂಡ್ ಲಭ್ಯವಿದೆ ಇಲ್ಲ</value>
  </data>
  <data name="LocaledetailsarenotavaliableforProductModel" xml:space="preserve">
    <value>ಲೊಕೇಲ್ ವಿವರಗಳು ಉತ್ಪನ್ನ ಮಾದರಿ ಲಭ್ಯವಿದೆ ಇಲ್ಲ</value>
  </data>
  <data name="LocaledetailsarenotavaliableforProductType" xml:space="preserve">
    <value>ಲೊಕೇಲ್ ವಿವರಗಳು ಉತ್ಪನ್ನ ಮಾದರಿ ಲಭ್ಯವಿದೆ ಇಲ್ಲ</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>ಸ್ಥಳ</value>
  </data>
  <data name="LocationTransfer" xml:space="preserve">
    <value>ಸ್ಥಳ ಟ್ರಾನ್ಸ್ಫರ್</value>
  </data>
  <data name="Lock" xml:space="preserve">
    <value>ಕರುಳು</value>
  </data>
  <data name="locked" xml:space="preserve">
    <value>ಬಂಧಿಸಿದ</value>
  </data>
  <data name="lockedby" xml:space="preserve">
    <value>ಲಾಕ್</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>ಲಾಗಿನ್</value>
  </data>
  <data name="LoginID" xml:space="preserve">
    <value>ಲಾಗಿನ್ ID</value>
  </data>
  <data name="LogoName" xml:space="preserve">
    <value>ಲೋಗೋ ಹೆಸರು</value>
  </data>
  <data name="LogOut" xml:space="preserve">
    <value>ಲಾಗ್ ಔಟ್</value>
  </data>
  <data name="LostCount" xml:space="preserve">
    <value>ಲಾಸ್ಟ್ ನ್ಯಾಷನಲ್</value>
  </data>
  <data name="LostQty" xml:space="preserve">
    <value>ಪ್ರಮಾಣ ಲಾಸ್ಟ್</value>
  </data>
  <data name="LostReasons" xml:space="preserve">
    <value>ಲಾಸ್ಟ್ ಕಾರಣಗಳು</value>
  </data>
  <data name="LostSales" xml:space="preserve">
    <value>ಕಳೆದುಕೊಂಡ ಮಾರಾಟದ</value>
  </data>
  <data name="LostSalesAnalysisReport" xml:space="preserve">
    <value>ಮಾರಾಟದ ವಿಶ್ಲೇಷಣೆಯ ವರದಿ ಲಾಸ್ಟ್</value>
  </data>
  <data name="low" xml:space="preserve">
    <value>ಕಡಿಮೆ</value>
  </data>
  <data name="LRDate" xml:space="preserve">
    <value>ಎಲ್ಆರ್ ದಿನಾಂಕ</value>
  </data>
  <data name="LRNumber" xml:space="preserve">
    <value>ಎಲ್ಆರ್ #</value>
  </data>
  <data name="Machine" xml:space="preserve">
    <value>ಯಂತ್ರ</value>
  </data>
  <data name="MachineCost" xml:space="preserve">
    <value>ಯಂತ್ರ ವೆಚ್ಚ</value>
  </data>
  <data name="MachineDetailCanNotBeBlank" xml:space="preserve">
    <value>ಯಂತ್ರ ವಿವರ ಖಾಲಿ ಇರಕೂಡದು</value>
  </data>
  <data name="MachineDetails" xml:space="preserve">
    <value>ಯಂತ್ರ ವಿವರಗಳು</value>
  </data>
  <data name="Machinedetailsaremandatory" xml:space="preserve">
    <value>ಯಂತ್ರ ವಿವರಗಳು ಕಡ್ಡಾಯವಾಗಿ</value>
  </data>
  <data name="MachineDetailsFieldSearch" xml:space="preserve">
    <value>ಯಂತ್ರ ವಿವರಗಳು ಫೀಲ್ಡ್ ಹುಡುಕು</value>
  </data>
  <data name="MachineStatus" xml:space="preserve">
    <value>ಯಂತ್ರ ಸ್ಥಿತಿ</value>
  </data>
  <data name="Machinetotalamount" xml:space="preserve">
    <value>ಯಂತ್ರ ಒಟ್ಟು ವೊತ್ತ</value>
  </data>
  <data name="MachineTotalAmountIsBeyondAcceptableLimit" xml:space="preserve">
    <value>ಯಂತ್ರ ಒಟ್ಟು ವೊತ್ತ ಸ್ವೀಕಾರಾರ್ಹ ಮಿತಿ ಮೀರಿ</value>
  </data>
  <data name="MachineTransferNote" xml:space="preserve">
    <value>ಯಂತ್ರ ಟ್ರಾನ್ಸ್ಫರ್ ಗಮನಿಸಿ</value>
  </data>
  <data name="MachineTransferNoteDate" xml:space="preserve">
    <value>ಯಂತ್ರ ಟ್ರಾನ್ಸ್ಫರ್ ಗಮನಿಸಿ ದಿನಾಂಕ</value>
  </data>
  <data name="MachineTransferNoteNum" xml:space="preserve">
    <value>ಯಂತ್ರ ಟ್ರಾನ್ಸ್ಫರ್ ನೋಟ್ ನಮ್</value>
  </data>
  <data name="MachineTransferRequest" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ವರ್ಗಾವಣೆ ಮನವಿ</value>
  </data>
  <data name="Manager" xml:space="preserve">
    <value>ವ್ಯವಸ್ಥಾಪಕ</value>
  </data>
  <data name="Mandatory" xml:space="preserve">
    <value>ಆಜ್ಞಾಪಕ</value>
  </data>
  <data name="MandatoryClaim" xml:space="preserve">
    <value>ಕಡ್ಡಾಯ ಹಕ್ಕು</value>
  </data>
  <data name="MandatoryClaimOEMApproval" xml:space="preserve">
    <value>ಕಡ್ಡಾಯ ಹಕ್ಕು ಅನುಮೋದನೆ</value>
  </data>
  <data name="MandatoryServiceDetails" xml:space="preserve">
    <value>ಕಡ್ಡಾಯ ಸೇವೆ ವಿವರಗಳು</value>
  </data>
  <data name="mandatoryservices" xml:space="preserve">
    <value>ಕಡ್ಡಾಯ ಸೇವೆಗಳು</value>
  </data>
  <data name="manual" xml:space="preserve">
    <value>ಮ್ಯಾನುಯಲ್</value>
  </data>
  <data name="Manufacturer" xml:space="preserve">
    <value>ತಯಾರಕ</value>
  </data>
  <data name="MapColumns" xml:space="preserve">
    <value>ನಕ್ಷೆ ಅಂಕಣ</value>
  </data>
  <data name="MappedColumns" xml:space="preserve">
    <value>ಮ್ಯಾಪ್ ಅಂಕಣ</value>
  </data>
  <data name="March" xml:space="preserve">
    <value>ಹಾಳುಮಾಡು</value>
  </data>
  <data name="MasterExists" xml:space="preserve">
    <value>ಮಾಸ್ಟರ್ ಈಗಾಗಲೇ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="MasterID" xml:space="preserve">
    <value>ಮಾಸ್ಟರ್ ID ಯನ್ನು</value>
  </data>
  <data name="MasterName" xml:space="preserve">
    <value>ಮಾಸ್ಟರ್ ಹೆಸರು</value>
  </data>
  <data name="MaxSundryAmount" xml:space="preserve">
    <value>ಮ್ಯಾಕ್ಸ್ ಸಂಡ್ರಿ ಮೌಲ್ಯ</value>
  </data>
  <data name="MaxTravelExpense" xml:space="preserve">
    <value>ಮ್ಯಾಕ್ಸ್ ಪ್ರಯಾಣ ವೆಚ್ಚ</value>
  </data>
  <data name="May" xml:space="preserve">
    <value>ಮೇ ತಿಂಗಳು</value>
  </data>
  <data name="MeanTimeReport" xml:space="preserve">
    <value>ಟೈಮ್ ವರದಿ ಮೀನ್</value>
  </data>
  <data name="MeanTimeToRepairReport" xml:space="preserve">
    <value>ವರದಿ ದುರಸ್ತಿ ಮಾಡಲು ಸಮಯ ಮೀನ್</value>
  </data>
  <data name="medium" xml:space="preserve">
    <value>ಮಧ್ಯಮ</value>
  </data>
  <data name="Medium1" xml:space="preserve">
    <value>ಮಧ್ಯಮ</value>
  </data>
  <data name="MediumCannotbegreaterthanorEqualtoFast" xml:space="preserve">
    <value>ಸಾಧಾರಣ ಹೆಚ್ಚಿನ ಅಥವಾ ಫಾಸ್ಟ್ ಗೆ ಸಮಾನವಾಗಿರಬೇಕು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="MediumCannotbeSmallerthanorEqualtoSlow" xml:space="preserve">
    <value>ಸಾಧಾರಣ ಚಿಕ್ಕದಾದ ಅಥವಾ ನಿಧಾನಗೊಳಿಸಲು ಸಮಾನವಾಗಿರಬೇಕು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="MenuDetail" xml:space="preserve">
    <value>ಮೆನು ವಿವರಗಳು</value>
  </data>
  <data name="MenuDetails" xml:space="preserve">
    <value>ಮೆನು ವಿವರಗಳು</value>
  </data>
  <data name="MenuName" xml:space="preserve">
    <value>ಮೆನು ಹೆಸರು</value>
  </data>
  <data name="MenuNamecannotbeblank" xml:space="preserve">
    <value>ಮೆನು ಹೆಸರು ಖಾಲಿ ಇರುವಂತಿಲ್ಲ</value>
  </data>
  <data name="MenuPath" xml:space="preserve">
    <value>ಮೆನು ಪಾತ್</value>
  </data>
  <data name="MinimumOrderQty" xml:space="preserve">
    <value>ಕನಿಷ್ಠ ಆರ್ಡರ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="Mobile" xml:space="preserve">
    <value>ಚರ</value>
  </data>
  <data name="MobileNumber" xml:space="preserve">
    <value>ಮೊಬೈಲ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="Mode" xml:space="preserve">
    <value>ಮೋಡ್</value>
  </data>
  <data name="model" xml:space="preserve">
    <value>ಮಾದರಿ</value>
  </data>
  <data name="modelenglish" xml:space="preserve">
    <value>ಮಾದರಿ ಇಂಗ್ಲೀಷ್</value>
  </data>
  <data name="ModelFieldSearch" xml:space="preserve">
    <value>ಮಾದರಿ ಫೀಲ್ಡ್ ಹುಡುಕು</value>
  </data>
  <data name="modelisinactive" xml:space="preserve">
    <value>ನಿಷ್ಕ್ರಿಯ ಮಾದರಿ</value>
  </data>
  <data name="modellocale" xml:space="preserve">
    <value>ಮಾದರಿ ಲೊಕೇಲ್</value>
  </data>
  <data name="modelmaster" xml:space="preserve">
    <value>ಮಾದರಿ ಮಾಸ್ಟರ್</value>
  </data>
  <data name="modelname" xml:space="preserve">
    <value>ಮಾದರಿ ಹೆಸರು</value>
  </data>
  <data name="modelnamealreadyexists" xml:space="preserve">
    <value>ಮಾದರಿ ಹೆಸರು ಈಗಾಗಲೇ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="modelnotfound" xml:space="preserve">
    <value>ಮಾದರಿ ಕಂಡುಬಂದಿಲ್ಲ</value>
  </data>
  <data name="ModelSearch" xml:space="preserve">
    <value>ಮಾದರಿ ಹುಡುಕು</value>
  </data>
  <data name="ModelWiseSalesOrderWithValue" xml:space="preserve">
    <value>ಮೌಲ್ಯ ಮಾದರಿ ವೈಸ್ ಮಾರಾಟದ ಆರ್ಡರ್</value>
  </data>
  <data name="ModelWiseTransactionDetails" xml:space="preserve">
    <value>ಮಾದರಿ ವೈಸ್ ವ್ಯವಹಾರದ ವಿವರಗಳು</value>
  </data>
  <data name="ModeOfShipment" xml:space="preserve">
    <value>ಸಾಗಾಣಿಕೆಯ ಮೋಡ್</value>
  </data>
  <data name="ModeOfTransport" xml:space="preserve">
    <value>ಸಾರಿಗೆ ಕ್ರಮದಲ್ಲಿ</value>
  </data>
  <data name="ModifiedBy" xml:space="preserve">
    <value>ಬದಲಾಯಿಸಲಾಗಿತ್ತು</value>
  </data>
  <data name="ModifiedDate" xml:space="preserve">
    <value>ದಿನಾಂಕ ಮಾರ್ಪಡಿಸಿದೆ</value>
  </data>
  <data name="Module" xml:space="preserve">
    <value>ಮಾಡ್ಯೂಲ್</value>
  </data>
  <data name="ModuleName" xml:space="preserve">
    <value>ಮಾಡ್ಯೂಲ್ ಹೆಸರು</value>
  </data>
  <data name="ModuleNameCannotbeblank" xml:space="preserve">
    <value>ಮಾಡ್ಯೂಲ್ ಹೆಸರು ಖಾಲಿ ಇರುವಂತಿಲ್ಲ</value>
  </data>
  <data name="Monday" xml:space="preserve">
    <value>ಸೋಮವಾರ</value>
  </data>
  <data name="month" xml:space="preserve">
    <value>ತಿಂಗಳ</value>
  </data>
  <data name="Month-Year" xml:space="preserve">
    <value>ತಿಂಗಳ ವರ್ಷದ</value>
  </data>
  <data name="Monthly" xml:space="preserve">
    <value>ಮಾಸಿಕ</value>
  </data>
  <data name="MonthRollingPickQuantity" xml:space="preserve">
    <value>12 ತಿಂಗಳ ರೋಲಿಂಗ್ ಪ್ರಮಾಣ ಆರಿಸಿ</value>
  </data>
  <data name="MonthWiseEnquiryToOrderConvertionPercentageGraphForTheYear" xml:space="preserve">
    <value>ವರ್ಷದ Convertion ಶೇಕಡಾವಾರು ಗ್ರಾಫ್ ಆದೇಶ ತಿಂಗಳ ಬುದ್ಧಿವಂತ ವಿಚಾರಣೆ</value>
  </data>
  <data name="MonthWiseSalesAnalysisReport" xml:space="preserve">
    <value>ತಿಂಗಳ ವೈಸ್ ಮಾರಾಟದ ವಿಶ್ಲೇಷಣೆಯ ವರದಿ</value>
  </data>
  <data name="monthwisesalesordercountgraphfortheyear" xml:space="preserve">
    <value>ತಿಂಗಳ ವೈಸ್ ಮಾರಾಟದ ಆರ್ಡರ್ ವರ್ಷದ ಗ್ರಾಫ್ ಕೌಂಟ್</value>
  </data>
  <data name="movement" xml:space="preserve">
    <value>ಚಳವಳಿ</value>
  </data>
  <data name="MovementType" xml:space="preserve">
    <value>ಚಳವಳಿ ಪ್ರಕಾರ</value>
  </data>
  <data name="MovementTypeDefinition" xml:space="preserve">
    <value>ಚಳವಳಿ ಕೌಟುಂಬಿಕತೆ ವ್ಯಾಖ್ಯಾನ</value>
  </data>
  <data name="movementtypedetails" xml:space="preserve">
    <value>ಚಳವಳಿ ಟೈಪ್ ವಿವರಗಳು</value>
  </data>
  <data name="MRPFactor" xml:space="preserve">
    <value>MRP ಫ್ಯಾಕ್ಟರ್</value>
  </data>
  <data name="MRPLabel" xml:space="preserve">
    <value>MRP ಲೇಬಲ್</value>
  </data>
  <data name="MRPPrice" xml:space="preserve">
    <value>MRP ಬೆಲೆ</value>
  </data>
  <data name="MRPReport" xml:space="preserve">
    <value>MRP ವರದಿ</value>
  </data>
  <data name="MTBF" xml:space="preserve">
    <value>ವೈಫಲ್ಯ ನಡುವಿನ ಮೀನ್ ಟೈಮ್</value>
  </data>
  <data name="MTTR" xml:space="preserve">
    <value>MTTR</value>
  </data>
  <data name="MyQue" xml:space="preserve">
    <value>ನನ್ನ ಸರದಿಗೆ</value>
  </data>
  <data name="MyQueue" xml:space="preserve">
    <value>ನನ್ನ ಸರದಿಗೆ</value>
  </data>
  <data name="MyTasksList" xml:space="preserve">
    <value>ನನ್ನ TasksList</value>
  </data>
  <data name="NA" xml:space="preserve">
    <value>ಎನ್ಎ</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>ಹೆಸರು</value>
  </data>
  <data name="NameShouldNotBeBlank" xml:space="preserve">
    <value>ಹೆಸರು ಖಾಲಿ ಇರುವಂತಿಲ್ಲ</value>
  </data>
  <data name="neagtivestockadjustmentisnotpossible" xml:space="preserve">
    <value>ನಕಾರಾತ್ಮಕ ಸ್ಟಾಕ್ ಹೊಂದಾಣಿಕೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="NetAmount" xml:space="preserve">
    <value>ನೆಟ್ ವೊತ್ತ</value>
  </data>
  <data name="netamt" xml:space="preserve">
    <value>ನೆಟ್ ಆಮ್ಟ್</value>
  </data>
  <data name="NetPartsWeight" xml:space="preserve">
    <value>ನೆಟ್ ಭಾಗಗಳು ತೂಕ</value>
  </data>
  <data name="NetSaleValue" xml:space="preserve">
    <value>ನಿವ್ವಳ ಮಾರಾಟ ಮೌಲ್ಯ</value>
  </data>
  <data name="NewAllocatedQty" xml:space="preserve">
    <value>ಹೊಸ ಹಂಚಿಕೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="NewAllocatedQuantity" xml:space="preserve">
    <value>ಹೊಸ ಹಂಚಿಕೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="NewFloatLocation" xml:space="preserve">
    <value>ಹೊಸ ಫ್ಲೋಟ್ ಸ್ಥಳ</value>
  </data>
  <data name="NewPartDetails" xml:space="preserve">
    <value>ಹೊಸ ಭಾಗ ವಿವರಗಳು</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>ಹೊಸ ಪಾಸ್ವರ್ಡ್</value>
  </data>
  <data name="newpasswordandConfirmpasswordarenotmatching" xml:space="preserve">
    <value>ಹೊಸ ಪಾಸ್ವರ್ಡ್ ಮತ್ತು ಪಾಸ್ವರ್ಡ್ ದೃಢೀಕರಿಸಿ ಹೊಂದಾಣಿಕೆ ಇಲ್ಲ</value>
  </data>
  <data name="NewReading" xml:space="preserve">
    <value>ಹೊಸ ಓದುವಿಕೆ</value>
  </data>
  <data name="NewReadingCanNotBeLesserThanOldReading" xml:space="preserve">
    <value>ಹೊಸ ಓದುವ ಹಳೆಯ ಓದುವ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="NewReport" xml:space="preserve">
    <value>ಹೊಸ ವರದಿ</value>
  </data>
  <data name="Next" xml:space="preserve">
    <value>ಮುಂದೆ</value>
  </data>
  <data name="NextServiceType" xml:space="preserve">
    <value>ಮುಂದೆ ಸೇವೆ ಪ್ರಕಾರ</value>
  </data>
  <data name="NintyHour" xml:space="preserve">
    <value>ಹೆಚ್ಚು 90 ಅವರ್</value>
  </data>
  <data name="NintyHour1" xml:space="preserve">
    <value>ಹೆಚ್ಚು 90 ಅವರ್ಸ್</value>
  </data>
  <data name="NintyHour11" xml:space="preserve">
    <value>&gt; 90 ಅವರ್</value>
  </data>
  <data name="no" xml:space="preserve">
    <value>ಇಲ್ಲ</value>
  </data>
  <data name="NoAmount" xml:space="preserve">
    <value>ಇಲ್ಲ ವೊತ್ತ</value>
  </data>
  <data name="NoBreakdowns" xml:space="preserve">
    <value>ಯಾವುದೇ ವಿಭಜನೆ</value>
  </data>
  <data name="NoChangesMade" xml:space="preserve">
    <value>ಯಾವುದೇ ಬದಲಾವಣೆಗಳನ್ನು</value>
  </data>
  <data name="NochangesmadetoSave" xml:space="preserve">
    <value>ಉಳಿಸಲು ಯಾವುದೇ ಬದಲಾವಣೆಗಳನ್ನು</value>
  </data>
  <data name="NoDataFound" xml:space="preserve">
    <value>ಯಾವುದೇ ಡೇಟಾ ಕಂಡುಬಂದಿಲ್ಲ</value>
  </data>
  <data name="Noeditpermissionforproductmaster" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಮಾಸ್ಟರ್ ಯಾವುದೇ ಸಂಪಾದಿಸಿ ಅನುಮತಿ</value>
  </data>
  <data name="NoInternalInvoices" xml:space="preserve">
    <value>ಯಾವುದೇ ಆಂತರಿಕ ಇನ್ವಾಯ್ಸ್</value>
  </data>
  <data name="NoInvoice" xml:space="preserve">
    <value>ಮರಳಲು ಸರಕುಪಟ್ಟಿ</value>
  </data>
  <data name="NoJobCardforScrapParts" xml:space="preserve">
    <value>ಸ್ಕ್ರ್ಯಾಪ್ ಭಾಗಗಳು ಯಾವುದೇ ಉದ್ಯೋಗ ಚೀಟಿ</value>
  </data>
  <data name="NoJobCards" xml:space="preserve">
    <value>ಯಾವುದೇ claimable ಕೆಲಸ ಕಾರ್ಡುಗಳು</value>
  </data>
  <data name="NoJobCardsforInternalInvoice" xml:space="preserve">
    <value>ಆಂತರಿಕ ಸರಕುಪಟ್ಟಿ ಯಾವುದೇ ಕೆಲಸ ಕಾರ್ಡುಗಳು</value>
  </data>
  <data name="NoJobCardsForInvoice" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಯಾವುದೇ ಕೆಲಸ ಕಾರ್ಡುಗಳು</value>
  </data>
  <data name="NoJobCardsForJobCardPartsReturn" xml:space="preserve">
    <value>ಯಾವುದೇ ಕೆಲಸ ಕಾರ್ಡುಗಳು</value>
  </data>
  <data name="NoJobCardsforSalvageParts" xml:space="preserve">
    <value>ರಕ್ಷಣೆ ಭಾಗಗಳು ಯಾವುದೇ ಉದ್ಯೋಗ ಚೀಟಿ</value>
  </data>
  <data name="NoJobCardsforWarrantyClaim" xml:space="preserve">
    <value>ಖಾತರಿ ಹಕ್ಕು ಯಾವುದೇ ಕೆಲಸ ಕಾರ್ಡುಗಳು</value>
  </data>
  <data name="NomatchingrecordfoundDoyouwanttoadd" xml:space="preserve">
    <value>ಪಕ್ಷದ ನಿಷ್ಕ್ರಿಯ ಅಥವಾ ಕಂಡುಬಂದಿಲ್ಲ ಯಾವುದೇ ಹೊಂದಾಣಿಕೆಯ ದಾಖಲೆ, ನೀವು ಈ ಪಕ್ಷದ ಸೇರಿಸಲು ಬಯಸುತ್ತೀರಿ</value>
  </data>
  <data name="NomatchingrecordfoundDoyouwanttoadd1" xml:space="preserve">
    <value>ಯಾವುದೇ ಹೊಂದಾಣಿಕೆಗೆ ದಾಖಲೆ, ನೀವು ಈ ಪಕ್ಷದ ಸೇರಿಸಲು ಬಯಸುತ್ತೀರಿ</value>
  </data>
  <data name="Noneofthepartswereuploaded" xml:space="preserve">
    <value>ಎಕ್ಸೆಲ್ ಭಾಗಗಳು ಯಾವುದೇ ಅಪ್ಲೋಡ್ ಮಾಡಲಾಯಿತು</value>
  </data>
  <data name="NonReturnable" xml:space="preserve">
    <value>ಹಿಂದಿರುಗಿಸಬೇಕಾಗಿಲ್ಲದ</value>
  </data>
  <data name="NonSales" xml:space="preserve">
    <value>ಮಾಂಸಾಹಾರಿ ಮಾರಾಟದ</value>
  </data>
  <data name="NonSalesQuantity" xml:space="preserve">
    <value>ಮಾಂಸಾಹಾರಿ ಮಾರಾಟದ ಪ್ರಮಾಣ</value>
  </data>
  <data name="NonTaxable" xml:space="preserve">
    <value>ಮಾಂಸಾಹಾರಿ ತೆರಿಗೆ</value>
  </data>
  <data name="Nontaxable1" xml:space="preserve">
    <value>ಅಲ್ಲದ ತೆರಿಗೆ 1</value>
  </data>
  <data name="Nontaxable2" xml:space="preserve">
    <value>ಅಲ್ಲದ ತೆರಿಗೆ 2</value>
  </data>
  <data name="NonTaxableothercharges" xml:space="preserve">
    <value>ಮಾಂಸಾಹಾರಿ ತೆರಿಗೆ ಇತರೆ ಚಾರ್ಜಸ್</value>
  </data>
  <data name="NonTaxableothercharges1" xml:space="preserve">
    <value>ಮಾಂಸಾಹಾರಿ ತೆರಿಗೆ ಇತರ ಆರೋಪಗಳನ್ನು 1</value>
  </data>
  <data name="NonTaxableothercharges1Amount" xml:space="preserve">
    <value>ಮಾಂಸಾಹಾರಿ ತೆರಿಗೆ ಇತರ ಆರೋಪಗಳನ್ನು 1 ವೊತ್ತ</value>
  </data>
  <data name="NonTaxableothercharges2" xml:space="preserve">
    <value>ಮಾಂಸಾಹಾರಿ ತೆರಿಗೆ ಇತರ ಆರೋಪಗಳನ್ನು 2</value>
  </data>
  <data name="NonTaxableothercharges2Amount" xml:space="preserve">
    <value>ಮಾಂಸಾಹಾರಿ ತೆರಿಗೆ ಇತರ ಆರೋಪಗಳನ್ನು 2 ವೊತ್ತ</value>
  </data>
  <data name="NonTaxableotherchargesAmount" xml:space="preserve">
    <value>ಮಾಂಸಾಹಾರಿ ತೆರಿಗೆ ಇತರೆ ಚಾರ್ಜಸ್ ವೊತ್ತ</value>
  </data>
  <data name="NonTaxableotherchargesamountcannotbegreaterthan" xml:space="preserve">
    <value>ಮಾಂಸಾಹಾರಿ ತೆರಿಗೆ ಇತರ ಆರೋಪಗಳನ್ನು ವೊತ್ತ ಹೆಚ್ಚಾಗಿದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="NonTaxableotherchargesamountcannotpartiallyreturn" xml:space="preserve">
    <value>ಮಾಂಸಾಹಾರಿ ತೆರಿಗೆ ಇತರ ಆರೋಪಗಳನ್ನು ವೊತ್ತ ಭಾಗಶಃ ಮರಳಲು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="NoofInvoices" xml:space="preserve">
    <value>ಇನ್ವಾಯ್ಸ್ ಯಾವುದೇ</value>
  </data>
  <data name="NoOfInvoicesInTransit" xml:space="preserve">
    <value>ಇನ್ ಸಾರಿಗೆ ನಂ ಇನ್ವಾಯ್ಸಸ್</value>
  </data>
  <data name="Noofnewcontractssecuredinthemonth" xml:space="preserve">
    <value>ತಿಂಗಳಲ್ಲಿ ಪಡೆದುಕೊಂಡನು ಹೊಸ ಒಪ್ಪಂದಗಳು ಸಂಖ್ಯೆ</value>
  </data>
  <data name="NoOfPicksPerYear" xml:space="preserve">
    <value>ವರ್ಷಕ್ಕೆ ಪಿಕ್ಸ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="NoOfSRCompleted" xml:space="preserve">
    <value>ಪೂರ್ಣಗೊಂಡಿದೆ ಪ್ರಕರಣದ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="NoOfSRCompleted1" xml:space="preserve">
    <value>ಸೇವೆ ವಿನಂತಿ ಸಂಖ್ಯೆ ಪೂರ್ಣಗೊಂಡಿದೆ</value>
  </data>
  <data name="NoOfSRRecieved" xml:space="preserve">
    <value>ಕೇಸ್ ಸ್ವೀಕರಿಸಿಲ್ಲ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="NoOfSRRecieved1" xml:space="preserve">
    <value>ಸೇವೆ ಸಂಖ್ಯೆ ಸ್ವೀಕರಿಸಿಲ್ಲ ವಿನಂತಿ</value>
  </data>
  <data name="Nopartsavailableinselectedbinlocation" xml:space="preserve">
    <value>ಆಯ್ಕೆ ಬಿನ್ ಸ್ಥಳ ಲಭ್ಯವಿದೆ ಯಾವುದೇ ಭಾಗಗಳು</value>
  </data>
  <data name="Nopendinginvoicesfoundforthissupplier" xml:space="preserve">
    <value>ಈ ಪೂರೈಕೆದಾರ ಗಾಗಿ ಯಾವುದೇ ಬಾಕಿ ಇನ್ವಾಯ್ಸ್</value>
  </data>
  <data name="Noproductisassociatedwithselectedcustomer" xml:space="preserve">
    <value>ಯಾವುದೇ ಉತ್ಪನ್ನ ಆಯ್ಕೆ ಗ್ರಾಹಕ ಸಂಬಂಧಿಸಿದೆ</value>
  </data>
  <data name="Noproductisassociatedwithselectedparty" xml:space="preserve">
    <value>ಯಾವುದೇ ಉತ್ಪನ್ನ ಆಯ್ಕೆ ಪಕ್ಷದ ಸಂಬಂಧಿಸಿದೆ</value>
  </data>
  <data name="NoProductIsAssociatedWithThisSupplier" xml:space="preserve">
    <value>ಯಾವುದೇ ಉತ್ಪನ್ನ ಈ ಸರಬರಾಜುದಾರ ಸಂಬಂಧಿಸಿದೆ</value>
  </data>
  <data name="NoReceiptsforInspection" xml:space="preserve">
    <value>ತಪಾಸಣೆ ಯಾವುದೇ ರಸೀದಿಗಳನ್ನು</value>
  </data>
  <data name="NoRecords" xml:space="preserve">
    <value>ಯಾವುದೇ ದಾಖಲೆಗಳು</value>
  </data>
  <data name="Norecordstoview" xml:space="preserve">
    <value>ವೀಕ್ಷಿಸಲು ಯಾವುದೇ ದಾಖಲೆಗಳು ಇಲ್ಲ</value>
  </data>
  <data name="NoRequestCoreforIssueCore" xml:space="preserve">
    <value>ಸಮಸ್ಯೆಯನ್ನು ಕೋರ್ ಯಾವುದೇ ವಿನಂತಿಯನ್ನು ಕೋರ್</value>
  </data>
  <data name="NoServiceHistoryRecords" xml:space="preserve">
    <value>ಯಾವುದೇ ಸೇವೆ ಇತಿಹಾಸ ದಾಖಲೆಗಳು</value>
  </data>
  <data name="Notabletocancelthejobcardsincereturneddateisblankingatepass" xml:space="preserve">
    <value>ಉದ್ಯೋಗ ಚೀಟಿ ರದ್ದು ಸಾಧ್ಯವಿಲ್ಲ, ಏಕೆಂದರೆ ಮರಳಿದರು ದಿನಾಂಕ ಗೇಟ್ ಪಾಸ್ ಖಾಲಿ</value>
  </data>
  <data name="Notabletoclosethejobcardsincereturneddateisblankingatepass" xml:space="preserve">
    <value>ಉದ್ಯೋಗ ಚೀಟಿ ಮುಚ್ಚಲು ಸಾಧ್ಯವಿಲ್ಲ, ಏಕೆಂದರೆ ಮರಳಿದರು ದಿನಾಂಕ ಗೇಟ್ ಪಾಸ್ ಖಾಲಿ</value>
  </data>
  <data name="NotAllowedtoEditSinceGRNisCreatedforthisSerialNumber" xml:space="preserve">
    <value>ಜಿ ಆರ್ ಎನ್ ಕ್ರಮ ಸಂಖ್ಯೆ ರಚಿಸಲಾಗಿದೆ ರಿಂದ, ಸಂಪಾದಿಸಲು ಅವಕಾಶ</value>
  </data>
  <data name="Notallowedtoeditthisevent" xml:space="preserve">
    <value>ಈ ಘಟನೆ ಸಂಪಾದಿಸಲು ಅವಕಾಶ</value>
  </data>
  <data name="NotApplicable" xml:space="preserve">
    <value>ಅನ್ವಯಿಸುವುದಿಲ್ಲ</value>
  </data>
  <data name="NotApproved" xml:space="preserve">
    <value>ಅನುಮೋದನೆ ಇಲ್ಲ</value>
  </data>
  <data name="NoteDate" xml:space="preserve">
    <value>ಗಮನಿಸಿ ದಿನಾಂಕ</value>
  </data>
  <data name="NoteNumber" xml:space="preserve">
    <value>ನೋಟ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="NoteOrInvoiceNumber" xml:space="preserve">
    <value>ನೋಟ್ ಅಥವಾ ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="NotEqual" xml:space="preserve">
    <value>ಸಮ ಅಲ್ಲ</value>
  </data>
  <data name="NotEqual1" xml:space="preserve">
    <value>NotEqual</value>
  </data>
  <data name="NotesDetails" xml:space="preserve">
    <value>ಟಿಪ್ಪಣಿಗಳು ವಿವರಗಳು</value>
  </data>
  <data name="NotFound" xml:space="preserve">
    <value>ಕಂಡುಬಂದಿಲ್ಲ</value>
  </data>
  <data name="notspeciafied" xml:space="preserve">
    <value>ನಿರ್ದಿಷ್ಟಪಡಿಸಿಲ್ಲ</value>
  </data>
  <data name="NotUnderWarranty" xml:space="preserve">
    <value>ಮಾಡಿರುವುದಿಲ್ಲ ವಾರಂಟಿ</value>
  </data>
  <data name="November" xml:space="preserve">
    <value>ನವೆಂಬರ್</value>
  </data>
  <data name="NullPick" xml:space="preserve">
    <value>ನಲ್ ಪಿಕ್</value>
  </data>
  <data name="NullReferenceException" xml:space="preserve">
    <value>ಅಪ್ಲಿಕೇಶನ್ ದೋಷ ಸಂಭವಿಸಿದೆ</value>
  </data>
  <data name="number" xml:space="preserve">
    <value>ಸಂಖ್ಯೆ</value>
  </data>
  <data name="NumberOfCasesBasedonIssueArea" xml:space="preserve">
    <value>IssueArea ಆಧರಿಸಿ ಪ್ರಕರಣಗಳ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="NumberOfCounts" xml:space="preserve">
    <value>ಎಣಿಕೆಗಳು ಸಂಖ್ಯೆ</value>
  </data>
  <data name="NumberofDays" xml:space="preserve">
    <value>ಡೇಸ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="NumberofDayscannotbezero" xml:space="preserve">
    <value>ದಿನಗಳ ಸಂಖ್ಯೆ ಶೂನ್ಯ ಅಥವಾ ಖಾಲಿ ಇರಕೂಡದು</value>
  </data>
  <data name="NumberofKitsMade" xml:space="preserve">
    <value>ಕಿಟ್ ಗಳನ್ನು ಸಂಖ್ಯೆ</value>
  </data>
  <data name="NumberofKitsToBreak" xml:space="preserve">
    <value>ಬ್ರೇಕ್ ಕಿಟ್ಗಳ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="NumberofKitstobreakcannotbezero" xml:space="preserve">
    <value>ಮುರಿಯಲು ಕಿಟ್ಗಳ ಸಂಖ್ಯೆ ಶೂನ್ಯವಾಗಿರುವುದಿಲ್ಲ</value>
  </data>
  <data name="NumberofKitstoMake" xml:space="preserve">
    <value>ಮೇಕ್ ಕಿಟ್ಗಳ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="numberofkitstomakemustbegreaterthanzero" xml:space="preserve">
    <value>ಮಾಡಲು ಕಿಟ್ಗಳ ಸಂಖ್ಯೆ ಹೆಚ್ಚಾಗಿದೆ ಶೂನ್ಯ ಇರಬೇಕು</value>
  </data>
  <data name="NumberOfSalesOrders" xml:space="preserve">
    <value>ಮಾರಾಟದ ಆದೇಶ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="NumberofShipment" xml:space="preserve">
    <value>ಸಾಗಾಣಿಕೆಯ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ObjectDescription" xml:space="preserve">
    <value>ವಸ್ತು ವಿವರಣೆ</value>
  </data>
  <data name="ObjectDescriptioncannotbeblank" xml:space="preserve">
    <value>ವಸ್ತು ವಿವರಣೆ ಖಾಲಿ ಇರುವಂತಿಲ್ಲ</value>
  </data>
  <data name="ObjectMaster" xml:space="preserve">
    <value>ಉದ್ದೇಶ</value>
  </data>
  <data name="ObjectMaster1" xml:space="preserve">
    <value>ವಸ್ತು ಮಾಸ್ಟರ್</value>
  </data>
  <data name="ObjectName" xml:space="preserve">
    <value>ಆಬ್ಜೆಕ್ಟ್ ಹೆಸರು</value>
  </data>
  <data name="ObjectNamecannotbeblank" xml:space="preserve">
    <value>ವಸ್ತು ಹೆಸರು ಖಾಲಿ ಇರುವಂತಿಲ್ಲ</value>
  </data>
  <data name="ObjectssavedSuccessfully" xml:space="preserve">
    <value>ಆಬ್ಜೆಕ್ಟ್ಸ್ ಯಶಸ್ವಿಯಾಗಿ ಉಳಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="ObsolescenceReport" xml:space="preserve">
    <value>ಕ್ಷಯ ವರದಿ</value>
  </data>
  <data name="October" xml:space="preserve">
    <value>ಅಕ್ಟೋಬರ್</value>
  </data>
  <data name="of" xml:space="preserve">
    <value>ಆಫ್</value>
  </data>
  <data name="OldBinLocation" xml:space="preserve">
    <value>ಓಲ್ಡ್ ಬಿನ್ ಸ್ಥಳ</value>
  </data>
  <data name="OldBufferBinLocation" xml:space="preserve">
    <value>ಓಲ್ಡ್ ಬಫರ್ ಬಿನ್ ಸ್ಥಳ</value>
  </data>
  <data name="OldPassword" xml:space="preserve">
    <value>ಹಳೆಯ ಪಾಸ್ವರ್ಡ್</value>
  </data>
  <data name="OldReading" xml:space="preserve">
    <value>ಓಲ್ಡ್ ಓದುವಿಕೆ</value>
  </data>
  <data name="OldReadingCannotBeSameAsNewReading" xml:space="preserve">
    <value>ಓಲ್ಡ್ ಓದುವ ಹೊಸ ಓದುವ ಅದೇ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="OnDate" xml:space="preserve">
    <value>ದಿನಾಂಕ ರಂದು</value>
  </data>
  <data name="OnHoldCount" xml:space="preserve">
    <value>ಹಿಡಿತವನ್ನು ಎಣಿಕೆ</value>
  </data>
  <data name="onlyactivecustomerdeatilscanbeedited" xml:space="preserve">
    <value>ಕೇವಲ ಸಕ್ರಿಯ ಗ್ರಾಹಕ ವಿವರಗಳನ್ನು ಎಡಿಟ್ ಮಾಡಬಹುದು</value>
  </data>
  <data name="onlyactivewarrantycanbeedited" xml:space="preserve">
    <value>ಕೇವಲ ಸಕ್ರಿಯ ಖಾತರಿ ಎಡಿಟ್ ಮಾಡಬಹುದು</value>
  </data>
  <data name="open" xml:space="preserve">
    <value>ತೆರೆದ</value>
  </data>
  <data name="OpeningStock" xml:space="preserve">
    <value>ತೆರೆಯುವ ಸ್ಟಾಕ್</value>
  </data>
  <data name="OpenReport" xml:space="preserve">
    <value>ಓಪನ್ ವರದಿ</value>
  </data>
  <data name="operation" xml:space="preserve">
    <value>ಕಾರ್ಯಾಚರಣೆ</value>
  </data>
  <data name="OperationandEmployeeisalreadyassociated" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ಮತ್ತು ನೌಕರ ಈಗಾಗಲೇ ಸಂಬಂಧಿಸಿದೆ</value>
  </data>
  <data name="OperationCode" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ಕೋಡ್</value>
  </data>
  <data name="Operationcodealreadyexists" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ಕೋಡ್ ಈಗಾಗಲೇ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="OperationCodeAlreadySelected" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ಕೋಡ್ aready ಆಯ್ಕೆ</value>
  </data>
  <data name="operationcodedup" xml:space="preserve">
    <value>ಕಾರ್ಯಾಚರಣೆ ಕೋಡ್</value>
  </data>
  <data name="OperationCodeNotFound" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ಕೋಡ್ ಕಂಡುಬಂದಿಲ್ಲ</value>
  </data>
  <data name="operationdes" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ವಿವರಣೆ</value>
  </data>
  <data name="OperationDescription" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ವಿವರಣೆ</value>
  </data>
  <data name="OperationDetail" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ವಿವರ</value>
  </data>
  <data name="OperationDetailisMandatory" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ವಿವರ ಕಡ್ಡಾಯ</value>
  </data>
  <data name="OperationDetails" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ವಿವರ</value>
  </data>
  <data name="OperationDeviationReport" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ಮಾರ್ಗಾಂತರ ವರದಿ</value>
  </data>
  <data name="OperationEmployeeDetails" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ನೌಕರರ ವಿವರಗಳು</value>
  </data>
  <data name="OperationEndDate" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ಅಂತಿಮ ದಿನಾಂಕ</value>
  </data>
  <data name="OperationEndDateCannotbelessthanoperationStartDate" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ಅಂತಿಮ ದಿನಾಂಕವನ್ನು ಕಾರ್ಯಾಚರಣೆ ಆರಂಭ ದಿನಾಂಕ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="operationenglish" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ಇಂಗ್ಲೀಷ್</value>
  </data>
  <data name="OperationFieldSearch" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ಫೀಲ್ಡ್ ಹುಡುಕು</value>
  </data>
  <data name="operationheader" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ಶಿರೋಲೇಖ</value>
  </data>
  <data name="OperationHours" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ಅವರ್ಸ್</value>
  </data>
  <data name="Operationisalreadyassociatedwithanotheremployee" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ಈಗಾಗಲೇ ಮತ್ತೊಂದು ನೌಕರ ಸಂಬಂಧಿಸಿದೆ</value>
  </data>
  <data name="operationlocale" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ಲೊಕೇಲ್</value>
  </data>
  <data name="operationmaster" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ಮಾಸ್ಟರ್</value>
  </data>
  <data name="Operationpartdetailwillbecleared" xml:space="preserve">
    <value>ಆಪರೇಷನ್ / ಭಾಗ ವಿವರ ತೆರವುಗೊಳಿಸಲಾಗುವುದು</value>
  </data>
  <data name="Operationquantitycannotbezero" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ಪ್ರಮಾಣ ಶೂನ್ಯವಾಗಿರುವುದಿಲ್ಲ</value>
  </data>
  <data name="OperationStartDate" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ಪ್ರಾರಂಭ ದಿನಾಂಕ</value>
  </data>
  <data name="OperationStartdatecannotbelessthanJobcarddate" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ಆರಂಭ ದಿನಾಂಕ ಉದ್ಯೋಗ ಚೀಟಿ ದಿನಾಂಕ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="operationtime" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ಟೈಮ್</value>
  </data>
  <data name="OperationTotalAmount" xml:space="preserve">
    <value>ಆಪರೇಷನ್ ಒಟ್ಟು ವೊತ್ತ</value>
  </data>
  <data name="Operator" xml:space="preserve">
    <value>ಆಪರೇಟರ್</value>
  </data>
  <data name="OR" xml:space="preserve">
    <value>ಅಥವಾ</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>ಆರ್ಡರ್</value>
  </data>
  <data name="OrderAmount" xml:space="preserve">
    <value>ಆರ್ಡರ್ ವೊತ್ತ</value>
  </data>
  <data name="OrderCancellationNumber" xml:space="preserve">
    <value>ಆರ್ಡರ್ ರದ್ದತಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="OrderClass" xml:space="preserve">
    <value>ಆರ್ಡರ್ ವರ್ಗ</value>
  </data>
  <data name="OrderClassAlreadyAssociatedtoWarehouse" xml:space="preserve">
    <value>ಆರ್ಡರ್ ವರ್ಗ ಈಗಾಗಲೇ ಮಳಿಗೆಗೆ ಸಂಬಂಧಿಸಿದ</value>
  </data>
  <data name="OrderClassServiceType" xml:space="preserve">
    <value>ಆರ್ಡರ್ ವರ್ಗ ಸೇವೆ ಪ್ರಕಾರ</value>
  </data>
  <data name="OrderClassServiceTypeLocale" xml:space="preserve">
    <value>ಆರ್ಡರ್ ವರ್ಗ ಸೇವೆ ಟೈಪ್ ಲೊಕೇಲ್</value>
  </data>
  <data name="OrderCount" xml:space="preserve">
    <value>ಆರ್ಡರ್ ನ್ಯಾಷನಲ್</value>
  </data>
  <data name="OrderDate" xml:space="preserve">
    <value>ಆದೇಶ ದಿನಾಂಕ</value>
  </data>
  <data name="OrderDetail" xml:space="preserve">
    <value>ಆರ್ಡರ್ ವಿವರಗಳು</value>
  </data>
  <data name="OrderedQuantity" xml:space="preserve">
    <value>ಆದೇಶ ಪ್ರಮಾಣ</value>
  </data>
  <data name="OrderNum" xml:space="preserve">
    <value>ಆದೇಶ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="OrderNumber" xml:space="preserve">
    <value>ಆದೇಶ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="OrderorStockTransferRequest" xml:space="preserve">
    <value>ಪರ್ಚೇಸ್ ಆರ್ಡರ್ / ಸ್ಟಾಕ್ ಟ್ರಾನ್ಸ್ಫರ್</value>
  </data>
  <data name="OrderorStockTransferRequestNumber" xml:space="preserve">
    <value>ಆರ್ಡರ್ ಅಥವಾ ಸ್ಟಾಕ್ ವರ್ಗಾವಣೆ ಮನವಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="Orderqtyshouldalwaysbegreaterthanminordqty" xml:space="preserve">
    <value>ಆರ್ಡರ್ ಪ್ರಮಾಣ ಯಾವಾಗಲೂ ಹೆಚ್ಚು ಅಥವಾ ಕನಿಷ್ಠ ಸಲುವಾಗಿ ಪ್ರಮಾಣ ಸಮನಾಗಿರುತ್ತದೆ</value>
  </data>
  <data name="OrderqtyshouldbeinmultipleofstandardpackingQty" xml:space="preserve">
    <value>ಆರ್ಡರ್ ಪ್ರಮಾಣ ಸ್ಟ್ಯಾಂಡರ್ಡ್ ಪ್ಯಾಕಿಂಗ್ ಪ್ರಮಾಣ ಅನೇಕ ಇರಬೇಕು</value>
  </data>
  <data name="OrderQuantity" xml:space="preserve">
    <value>ಆರ್ಡರ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="Orders" xml:space="preserve">
    <value>ಆರ್ಡರ್ಸ್</value>
  </data>
  <data name="OrderStatus" xml:space="preserve">
    <value>ಆರ್ಡರ್ ಸ್ಥಿತಿ</value>
  </data>
  <data name="OrderType" xml:space="preserve">
    <value>ಆರ್ಡರ್ ಪ್ರಕಾರ</value>
  </data>
  <data name="OriginalPartDescription" xml:space="preserve">
    <value>ಮೂಲ ಭಾಗ ವಿವರಣೆ</value>
  </data>
  <data name="OriginalPartDetails" xml:space="preserve">
    <value>ಮೂಲ ಭಾಗ ವಿವರಗಳು</value>
  </data>
  <data name="OriginalPartNumber" xml:space="preserve">
    <value>ಮೂಲ ಭಾಗ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="OTCReport" xml:space="preserve">
    <value>OTCReport</value>
  </data>
  <data name="OtherAmount" xml:space="preserve">
    <value>ಇತರೆ ವೊತ್ತ</value>
  </data>
  <data name="OtherCharge" xml:space="preserve">
    <value>ಇತರೆ ಚಾರ್ಜ್</value>
  </data>
  <data name="OtherCharges" xml:space="preserve">
    <value>ಇತರೆ ಚಾರ್ಜಸ್</value>
  </data>
  <data name="OtherDetail" xml:space="preserve">
    <value>ಇತರೆ ವಿವರ</value>
  </data>
  <data name="OtherDetails" xml:space="preserve">
    <value>ಇತರೆ ವಿವರಗಳು</value>
  </data>
  <data name="OtherExpenses" xml:space="preserve">
    <value>ಇತರೆ ಖರ್ಚುಗಳು</value>
  </data>
  <data name="Others" xml:space="preserve">
    <value>ಇತರೆ</value>
  </data>
  <data name="OutOfMemoryException" xml:space="preserve">
    <value>ಅಪ್ಲಿಕೇಶನ್ ದೋಷ ಸಂಭವಿಸಿದೆ</value>
  </data>
  <data name="OutstandingAmount" xml:space="preserve">
    <value>ಮಹೋನ್ನತ ವೊತ್ತ</value>
  </data>
  <data name="Overdue30to60days" xml:space="preserve">
    <value>30 ರಿಂದ 60 ದಿನಗಳಲ್ಲಿ ಬಾಕಿ ಮಿತಿಮೀರಿದ</value>
  </data>
  <data name="Overduefrom90days" xml:space="preserve">
    <value>ಹೆಚ್ಚು thsn 90 ದಿನಗಳಿಂದ ಬಾಕಿ ಮಿತಿಮೀರಿದ</value>
  </data>
  <data name="Overduelessthan30days" xml:space="preserve">
    <value>ಮಿತಿಮೀರಿದ &lt;30 ದಿನಗಳ</value>
  </data>
  <data name="OverStockExRMBVal" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಎಕ್ಸ್ RMB ವಾಲೆ ಪ್ರತಿ</value>
  </data>
  <data name="OverStockVal" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಮೌಲ್ಯವನ್ನು</value>
  </data>
  <data name="Owner" xml:space="preserve">
    <value>ಒಡೆಯ</value>
  </data>
  <data name="OwnerName" xml:space="preserve">
    <value>ಮಾಲೀಕ ಹೆಸರು</value>
  </data>
  <data name="Packed" xml:space="preserve">
    <value>ಪ್ಯಾಕ್</value>
  </data>
  <data name="PackedBy" xml:space="preserve">
    <value>ಪ್ಯಾಕ್</value>
  </data>
  <data name="PackingList" xml:space="preserve">
    <value>ಪ್ಯಾಕಿಂಗ್ ಪಟ್ಟಿ</value>
  </data>
  <data name="PackingListIsAlreadyCreatedForThisInvoiceNumber" xml:space="preserve">
    <value>ಪಟ್ಟಿ ಪ್ಯಾಕಿಂಗ್ ಈಗಾಗಲೇ ಈ ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ ರಚಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="PackingListNumber" xml:space="preserve">
    <value>ಪ್ಯಾಕಿಂಗ್ ಪಟ್ಟಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="PackingType" xml:space="preserve">
    <value>ಭಿನ್ನ</value>
  </data>
  <data name="Page" xml:space="preserve">
    <value>ಪುಟ</value>
  </data>
  <data name="ParentCaseNumber" xml:space="preserve">
    <value>ಪೋಷಕ ಕೇಸ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ParentCompany" xml:space="preserve">
    <value>ಪೋಷಕ ಕಂಪನಿ</value>
  </data>
  <data name="parentcompanyoperationcannotbedeleted" xml:space="preserve">
    <value>ಪೋಷಕ ಕಂಪನಿ ಕಾರ್ಯಾಚರಣೆ ಅಳಿಸಲಾಗಿಲ್ಲ</value>
  </data>
  <data name="parentcompanyoperationcannotbeedited" xml:space="preserve">
    <value>ಪೋಷಕ ಕಂಪನಿ ಕಾರ್ಯಾಚರಣೆ ಸಂಪಾದಿತವಾಗಿಲ್ಲ</value>
  </data>
  <data name="ParentCompanyServiceChargeCannotBeDeleted" xml:space="preserve">
    <value>ಪೋಷಕ ಕಂಪನಿ ಸೇವಾ ಶುಲ್ಕ ಅಳಿಸಲಾಗಿಲ್ಲ</value>
  </data>
  <data name="ParentCompanyServiceChargeCannotBeEdited" xml:space="preserve">
    <value>ಪೋಷಕ ಕಂಪನಿ ಸೇವಾ ಶುಲ್ಕ ಸಂಪಾದಿತವಾಗಿಲ್ಲ</value>
  </data>
  <data name="ParentCompanyServiceTypeCannotBeDeleted" xml:space="preserve">
    <value>ಪೋಷಕ ಕಂಪನಿ ಸೇವೆ ಮಾದರಿ ಅಳಿಸಲಾಗಿಲ್ಲ</value>
  </data>
  <data name="ParentEnquiryNumber" xml:space="preserve">
    <value>ಪೋಷಕ ವಿಚಾರಣೆ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ParentMenu" xml:space="preserve">
    <value>ಪೋಷಕ ಮೆನು</value>
  </data>
  <data name="ParentpartcannotbechildPart" xml:space="preserve">
    <value>ಪೋಷಕ ಭಾಗ ಮಗು ಭಾಗ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ParentTaxStructure" xml:space="preserve">
    <value>ಪೋಷಕ ತೆರಿಗೆ ರಚನೆ</value>
  </data>
  <data name="Part" xml:space="preserve">
    <value>ಭಾಗ</value>
  </data>
  <data name="PartAlreadyAdded" xml:space="preserve">
    <value>ಭಾಗ ಈಗಾಗಲೇ ಸೇರಿಸಲಾಗಿದೆ.</value>
  </data>
  <data name="PartalreadyexistsinDealercompany" xml:space="preserve">
    <value>ಭಾಗ ಈಗಾಗಲೇ ಡೀಲರ್ ಕಂಪನಿ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="PartalreadyexistsinParentcompany" xml:space="preserve">
    <value>ಭಾಗ ಈಗಾಗಲೇ ಪೋಷಕ ಕಂಪನಿ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="partcategory" xml:space="preserve">
    <value>ಭಾಗ ವರ್ಗ</value>
  </data>
  <data name="partdescription" xml:space="preserve">
    <value>ಭಾಗ ವಿವರಣೆ</value>
  </data>
  <data name="PartDetails" xml:space="preserve">
    <value>ಭಾಗ ವಿವರಗಳು</value>
  </data>
  <data name="Partdetailsnotpresentfortheselectedwarehouse" xml:space="preserve">
    <value>ಭಾಗ ವಿವರಗಳು ಆಯ್ಕೆ ಗೋದಾಮಿನ ಪ್ರೆಸೆಂಟ್</value>
  </data>
  <data name="partfunctiongroup" xml:space="preserve">
    <value>ಭಾಗ ಫಂಕ್ಷನ್ ಗುಂಪು</value>
  </data>
  <data name="Partial" xml:space="preserve">
    <value>ಭಾಗಶಃ</value>
  </data>
  <data name="Partisalreadyused" xml:space="preserve">
    <value>ಭಾಗ ಈಗಾಗಲೇ ಬಳಸಲಾಗುತ್ತದೆ</value>
  </data>
  <data name="partisnotactive" xml:space="preserve">
    <value>ಭಾಗ ಸಕ್ರಿಯವಾಗಿಲ್ಲ</value>
  </data>
  <data name="Partisnotassociatedwiththeselectedsupplier" xml:space="preserve">
    <value>ಭಾಗ ಆಯ್ಕೆ ಸರಬರಾಜು ಸಂಬಂಧ ಇಲ್ಲ</value>
  </data>
  <data name="Partisnotassociatedwiththeselectedwarehouse" xml:space="preserve">
    <value>ಭಾಗ ಆಯ್ಕೆ ಗೋದಾಮಿನ ಸಂಬಂಧವಿಲ್ಲ</value>
  </data>
  <data name="PartList" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಪಟ್ಟಿ</value>
  </data>
  <data name="PartListDetails" xml:space="preserve">
    <value>PartList ವಿವರಗಳು</value>
  </data>
  <data name="Partner" xml:space="preserve">
    <value>ಜತೆಗಾರ</value>
  </data>
  <data name="PartnerName" xml:space="preserve">
    <value>ಜತೆಗಾರ</value>
  </data>
  <data name="partnum" xml:space="preserve">
    <value>ಭಾಗ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="partnumber" xml:space="preserve">
    <value>ಭಾಗ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="partnumberalreadyexists" xml:space="preserve">
    <value>ಭಾಗ ಸಂಖ್ಯೆ ಈಗಾಗಲೇ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="PartNumberalreadyselected" xml:space="preserve">
    <value>ಭಾಗ ಸಂಖ್ಯೆ ಈಗಾಗಲೇ ಆಯ್ಕೆ</value>
  </data>
  <data name="PartnumberexitsinPartsDetailgrid" xml:space="preserve">
    <value>ಭಾಗ ಸಂಖ್ಯೆ ಭಾಗಗಳು ವಿವರ ಗ್ರಿಡ್ ನಿರ್ಗಮಿಸುತ್ತದೆ</value>
  </data>
  <data name="PartnumberexitsinSuperseedingPartsDetailgrid" xml:space="preserve">
    <value>ಭಾಗ ಸಂಖ್ಯೆ ಸೂಪರ್ ಬಿತ್ತನೆಯ ಭಾಗಗಳು ವಿವರ ಗ್ರಿಡ್ ನಿರ್ಗಮಿಸುತ್ತದೆ</value>
  </data>
  <data name="PartNumberisBlank" xml:space="preserve">
    <value>ಭಾಗ ಸಂಖ್ಯೆ ಖಾಲಿ.</value>
  </data>
  <data name="PartnumberisSuperseded" xml:space="preserve">
    <value>ಭಾಗ ಸಂಖ್ಯೆ ಹೊರಗಿಡಲಾಗಿತ್ತು</value>
  </data>
  <data name="Partnumberissuperseedingwithanotherpart" xml:space="preserve">
    <value>ಭಾಗ ಸಂಖ್ಯೆ ಇನ್ನೊಂದು ಭಾಗವು ಸೂಪರ್ ಬಿತ್ತನೆಯ ಹೊಂದಿದೆ</value>
  </data>
  <data name="Partnumberissupersessionwithanotherpart" xml:space="preserve">
    <value>ಭಾಗ ಸಂಖ್ಯೆ ಇನ್ನೊಂದು ಭಾಗವು supersession ಆಗಿದೆ</value>
  </data>
  <data name="PartNumbernotassociatedtotheselectedproductdetails" xml:space="preserve">
    <value>ಭಾಗ ಸಂಖ್ಯೆ ಆಯ್ದ ಉತ್ಪನ್ನದ ವಿವರಗಳನ್ನು ಸಂಬಂಧವಿಲ್ಲ.</value>
  </data>
  <data name="PartNumbernotfound" xml:space="preserve">
    <value>ಭಾಗ ಸಂಖ್ಯೆ ಕಂಡುಬಂದಿಲ್ಲ</value>
  </data>
  <data name="partnumberr" xml:space="preserve">
    <value>ಭಾಗ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="partnumbersearch" xml:space="preserve">
    <value>ಭಾಗ ಸಂಖ್ಯೆ ಹುಡುಕು</value>
  </data>
  <data name="Partprefix" xml:space="preserve">
    <value>ಭಾಗ ಪೂರ್ವಪ್ರತ್ಯಯ</value>
  </data>
  <data name="PartPrefixisBlank" xml:space="preserve">
    <value>PartPrefix ಖಾಲಿ</value>
  </data>
  <data name="partprice" xml:space="preserve">
    <value>ಭಾಗ ಬೆಲೆ</value>
  </data>
  <data name="partpricepdetails" xml:space="preserve">
    <value>ಭಾಗ ಬೆಲೆ ವಿವರಗಳು</value>
  </data>
  <data name="partproducttypedetails" xml:space="preserve">
    <value>ಭಾಗಗಳು ಉತ್ಪನ್ನ ವಿವರಗಳು</value>
  </data>
  <data name="partproducttypedetails1" xml:space="preserve">
    <value>ಭಾಗಗಳು ಉತ್ಪನ್ನ ಪ್ರಕಾರ ವಿವರಗಳು</value>
  </data>
  <data name="partproducttypedetails11" xml:space="preserve">
    <value>ಭಾಗ ಉತ್ಪನ್ನ ಪ್ರಕಾರ ವಿವರಗಳು</value>
  </data>
  <data name="Parts" xml:space="preserve">
    <value>ಭಾಗಗಳು</value>
  </data>
  <data name="PartsAlreadyReturnedforselectedjobcard" xml:space="preserve">
    <value>ಭಾಗಗಳಲ್ಲಿ ಈಗಾಗಲೇ ಆಯ್ಕೆ ಉದ್ಯೋಗ ಚೀಟಿ ಮರಳಿದರು</value>
  </data>
  <data name="PartsAmount" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ವೊತ್ತ</value>
  </data>
  <data name="PartsApproved" xml:space="preserve">
    <value>ಅನುಮೋದಿತ ಭಾಗಗಳು</value>
  </data>
  <data name="Partsareblockedforstockcheckconfirmation" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಸ್ಟಾಕ್ ಚೆಕ್ ದೃಢೀಕರಣ ನಿರ್ಬಂಧಿಸಲಾಗುತ್ತದೆ</value>
  </data>
  <data name="PartsareblockedofstockCheckrequestacceptancecannotbedone" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಸ್ಟಾಕ್ ಚೆಕ್ ವಿನಂತಿಯನ್ನು ನಿರ್ಬಂಧಿಸಲಾಗಿದೆ, ಸ್ವೀಕಾರ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="PartsCategory" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ವರ್ಗ</value>
  </data>
  <data name="PartsCategoryDefinition" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ವರ್ಗ ವ್ಯಾಖ್ಯಾನ</value>
  </data>
  <data name="PartsCategoryisinactive" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ವರ್ಗ ನಿಷ್ಕ್ರಿಯ</value>
  </data>
  <data name="PartsCategoryNotFound" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ವರ್ಗ ಕಂಡುಬಂದಿಲ್ಲ</value>
  </data>
  <data name="PartsCost" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ವೆಚ್ಚ</value>
  </data>
  <data name="PartsCost1" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ವೆಚ್ಚ</value>
  </data>
  <data name="PartsCreditLimit" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಕ್ರೆಡಿಟ್ ಮಿತಿಯನ್ನು</value>
  </data>
  <data name="PartsDetail" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ವಿವರ</value>
  </data>
  <data name="PartsDetailCannotbeblank" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ವಿವರ ಖಾಲಿ ಇರಕೂಡದು</value>
  </data>
  <data name="PartsDetails" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ವಿವರಗಳು</value>
  </data>
  <data name="PartsDetailsCannotbeEmpty" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ವಿವರಗಳು ಖಾಲಿ ಇರುವಂತಿಲ್ಲ</value>
  </data>
  <data name="PartsDisposalisalreadydoneforselectedclaimnumber" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ವಿಲೇವಾರಿ ಈಗಾಗಲೇ ಆಯ್ಕೆ ಹಕ್ಕು ಸಂಖ್ಯೆ ಮಾಡಲಾಗುತ್ತದೆ</value>
  </data>
  <data name="partsenglish" xml:space="preserve">
    <value>ಭಾಗಗಳು</value>
  </data>
  <data name="partsenglish1" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಇಂಗ್ಲೀಷ್</value>
  </data>
  <data name="PartsFieldSearch" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಫೀಲ್ಡ್ ಹುಡುಕು</value>
  </data>
  <data name="partsfreestockdetails" xml:space="preserve">
    <value>ಭಾಗಗಳು ಉಚಿತ ಸ್ಟಾಕ್ ವಿವರಗಳು</value>
  </data>
  <data name="PartsInvoiceReturn" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಸರಕುಪಟ್ಟಿ ರಿಟರ್ನ್</value>
  </data>
  <data name="PartsList" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಪಟ್ಟಿ</value>
  </data>
  <data name="partslocale" xml:space="preserve">
    <value>ಭಾಗಗಳು</value>
  </data>
  <data name="partslocale1" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಲೊಕೇಲ್</value>
  </data>
  <data name="PartsLookUp" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ನೋಡಿ</value>
  </data>
  <data name="partsmaster" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಮಾಸ್ಟರ್</value>
  </data>
  <data name="partsmasterlocale" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಮಾಸ್ಟರ್</value>
  </data>
  <data name="partsmasterlocale1" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಮಾಸ್ಟರ್ ಲೊಕೇಲ್</value>
  </data>
  <data name="Partsnotavailable" xml:space="preserve">
    <value>ಭಾಗಗಳು ಲಭ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="PartsNumberdoesnothaveanyAllocatedOrders" xml:space="preserve">
    <value>ಭಾಗಗಳು ಸಂಖ್ಯೆ ಯಾವುದೇ ಹಂಚಿಕೆ ಆದೇಶಗಳನ್ನು ಹೊಂದಿಲ್ಲ</value>
  </data>
  <data name="PartsNumberdoesnothaveanyBackOrders" xml:space="preserve">
    <value>ಭಾಗಗಳು ಸಂಖ್ಯೆ ಯಾವುದೇ ಮತ್ತೆ ಆದೇಶಗಳನ್ನು ಹೊಂದಿಲ್ಲ</value>
  </data>
  <data name="PartsOrder" xml:space="preserve">
    <value>ಭಾಗಗಳು ಆದೇಶ</value>
  </data>
  <data name="PartsOrderAcceptance" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಅಂಗೀಕಾರ ಆರ್ಡರ್</value>
  </data>
  <data name="PartsOrderCancellation" xml:space="preserve">
    <value>ಭಾಗಗಳು ಆದೇಶ ರದ್ದತಿ</value>
  </data>
  <data name="PartsOrderCancellationNumber" xml:space="preserve">
    <value>ಭಾಗಗಳ ಆದೇಶ ರದ್ದತಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="PartsOrdercannotbecreatedbecauseofthelocalpartsdoyouwanttosavepurchaseorder" xml:space="preserve">
    <value>ಭಾಗಗಳು ಆದೇಶ ಏಕೆಂದರೆ ಸ್ಥಳೀಯ ಭಾಗಗಳು ರಚಿಸಲಾಗುವುದಿಲ್ಲ ನೀವು ಖರೀದಿ ಆದೇಶವನ್ನು ಉಳಿಸಲು ಬಯಸುವಿರಾ?</value>
  </data>
  <data name="PartsOrderDate" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ದಿನಾಂಕ ಸಲುವಾಗಿ</value>
  </data>
  <data name="PartsOrderisinbackorder" xml:space="preserve">
    <value>ಭಾಗಗಳು ಆದೇಶ ಸಲುವಾಗಿ ಆಗಿದೆ</value>
  </data>
  <data name="PartsOrderNumber" xml:space="preserve">
    <value>ಭಾಗಗಳ ಆದೇಶ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="PartsOrderSummary" xml:space="preserve">
    <value>ಭಾಗಗಳು ಆದೇಶ ಸಾರಾಂಶ</value>
  </data>
  <data name="PartsOrderType" xml:space="preserve">
    <value>ಆರ್ಡರ್ ಪ್ರಕಾರ</value>
  </data>
  <data name="PartsPlanningPurchaseProposal" xml:space="preserve">
    <value>ಖರೀದಿ ಪ್ರಸ್ತಾಪ ಯೋಜನೆ ಭಾಗಗಳು</value>
  </data>
  <data name="partspmasterheader" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಮಾಸ್ಟರ್</value>
  </data>
  <data name="partspmasterheader1" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಮಾಸ್ಟರ್ ಶಿರೋಲೇಖ</value>
  </data>
  <data name="partspricedetails" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಬೆಲೆ ವಿವರಗಳು</value>
  </data>
  <data name="partsproducttypelocale" xml:space="preserve">
    <value>ಭಾಗಗಳು ಉತ್ಪನ್ನ ಪ್ರಕಾರ</value>
  </data>
  <data name="partsproducttypelocale1" xml:space="preserve">
    <value>ಭಾಗಗಳು ಉತ್ಪನ್ನ ಪ್ರಕಾರ ಲೊಕೇಲ್</value>
  </data>
  <data name="Partsquantitycannotbezero" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಪ್ರಮಾಣ ಶೂನ್ಯವಾಗಿರುವುದಿಲ್ಲ</value>
  </data>
  <data name="PartsQuotation" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ನುಡಿಮುತ್ತುಗಳು</value>
  </data>
  <data name="PartsQuotationArchived" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಉದ್ಧರಣ ಆರ್ಕೈವ್</value>
  </data>
  <data name="PartsReplacedCost" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಬದಲಾಯಿಸಲ್ಪಟ್ಟಿದೆ ವೆಚ್ಚ</value>
  </data>
  <data name="PartsStatus" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಸ್ಥಿತಿ</value>
  </data>
  <data name="partsstockdetails" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಸ್ಟಾಕ್ ವಿವರಗಳು</value>
  </data>
  <data name="Partsstockstatementreport" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಸ್ಟಾಕ್ ಹೇಳಿಕೆ ರಿಪೋರ್ಟ್</value>
  </data>
  <data name="PartsSuppliedByCustomer" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ಪೂರೈಕೆ ಭಾಗಗಳು</value>
  </data>
  <data name="partsSupplierdetails" xml:space="preserve">
    <value>ಬಿಡಿಭಾಗಗಳ ಸರಬರಾಜುದಾರ ವಿವರಗಳು</value>
  </data>
  <data name="PartsTemplate" xml:space="preserve">
    <value>ಭಾಗಗಳು ಟೆಂಪ್ಲೇಟು</value>
  </data>
  <data name="PartsTotalAmount" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಒಟ್ಟು ವೊತ್ತ</value>
  </data>
  <data name="PartsUpload" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಅಪ್ಲೋಡ್</value>
  </data>
  <data name="PartWiseFailureReport" xml:space="preserve">
    <value>ಭಾಗ ವೈಸ್ ವೈಫಲ್ಯ ವರದಿ</value>
  </data>
  <data name="PartWiseFailureReportFrom" xml:space="preserve">
    <value>ಗೆ ಭಾಗ ವೈಸ್ ವೈಫಲ್ಯ ವರದಿ</value>
  </data>
  <data name="Party" xml:space="preserve">
    <value>ಪಕ್ಷ</value>
  </data>
  <data name="PartyDetails" xml:space="preserve">
    <value>ಪಕ್ಷದ ವಿವರ</value>
  </data>
  <data name="PartydoesnotexistsinOEMPartsOrderCannotbecreateddoyouwanttocontinue" xml:space="preserve">
    <value>ಪಕ್ಷದ OEM ಭಾಗಗಳನ್ನು ಸಲುವಾಗಿ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ ರಚಿಸಲಾಗುವುದಿಲ್ಲ ನೀವು ಮುಂದುವರಿಸಲು ಬಯಸುತ್ತೀರಾ?</value>
  </data>
  <data name="PartyDoesNotExistsInOEMSalesOrderCanNotBeCreatedDoYouWantToContinue" xml:space="preserve">
    <value>ಪಕ್ಷದ OEM ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ ಮಾಡುವುದಿಲ್ಲ.</value>
  </data>
  <data name="PartyFieldSearch" xml:space="preserve">
    <value>ಪಕ್ಷದ ಫೀಲ್ಡ್ ಹುಡುಕು</value>
  </data>
  <data name="PartyFielSearch" xml:space="preserve">
    <value>ಪಕ್ಷದ ಫೀಲ್ಡ್ ಹುಡುಕು</value>
  </data>
  <data name="Partyislocked" xml:space="preserve">
    <value>ಪಕ್ಷದ ಲಾಕ್</value>
  </data>
  <data name="PartyisLockedDoyouwanttocontinue" xml:space="preserve">
    <value>ಪಕ್ಷದ ನೀವು ಮುಂದುವರಿಸಲು ಬಯಸುತ್ತೀರಾ, ಲಾಕ್?</value>
  </data>
  <data name="PartyLocation" xml:space="preserve">
    <value>ಪಕ್ಷದ ಸ್ಥಳ</value>
  </data>
  <data name="PartyMobile" xml:space="preserve">
    <value>ಪಕ್ಷದ ಮೊಬೈಲ್</value>
  </data>
  <data name="PartyName" xml:space="preserve">
    <value>ಪಕ್ಷದ ಹೆಸರು</value>
  </data>
  <data name="partynamesearch" xml:space="preserve">
    <value>ಪಕ್ಷದ ಹೆಸರು ಹುಡುಕು</value>
  </data>
  <data name="PartyNotFound" xml:space="preserve">
    <value>ಪಕ್ಷಕ್ಕೆ ಕಂಡುಬಂದಿಲ್ಲ</value>
  </data>
  <data name="PartyOrBranch" xml:space="preserve">
    <value>ಪಕ್ಷದ / ಶಾಖೆ</value>
  </data>
  <data name="PartyOrderref" xml:space="preserve">
    <value>ಪಕ್ಷದ ರೆಫರೆನ್ಸ್</value>
  </data>
  <data name="PartyPhone" xml:space="preserve">
    <value>ಪಕ್ಷದ ಫೋನ್</value>
  </data>
  <data name="PartySchedule" xml:space="preserve">
    <value>ಪಕ್ಷದ ವೇಳಾಪಟ್ಟಿ</value>
  </data>
  <data name="PartySearch" xml:space="preserve">
    <value>ಪಕ್ಷದ ಹುಡುಕು</value>
  </data>
  <data name="PartyType" xml:space="preserve">
    <value>ಪಕ್ಷದ ಪ್ರಕಾರ</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>ಪಾಸ್ವರ್ಡ್</value>
  </data>
  <data name="Passwordandconfirmpasswordshouldmatch" xml:space="preserve">
    <value>ಪಾಸ್ವರ್ಡ್ ಅನ್ನು ದೃಢೀಕರಿಸಿ ನೀಡಿದ ಪಾಸ್ವರ್ಡ್ ಹೊಂದಿಕೆಯಾಗುವುದಿಲ್ಲ</value>
  </data>
  <data name="Passwordandconfirmpasswordshouldmatch1" xml:space="preserve">
    <value>ಪಾಸ್ವರ್ಡ್ ಮತ್ತು ಪಾಸ್ವರ್ಡ್ ಅನ್ನು ಖಚಿತಪಡಿಸು ಹೊಂದಾಣಿಕೆ ಮಾಡಬೇಕು</value>
  </data>
  <data name="Passwordandconfirmpasswordshouldmatch11" xml:space="preserve">
    <value>ಪಾಸ್ವರ್ಡ್ ಅನ್ನು ದೃಢೀಕರಿಸಿ ನೀಡಿದ ಪಾಸ್ವರ್ಡ್ ಹೊಂದಿಕೆಯಾಗುವುದಿಲ್ಲ</value>
  </data>
  <data name="Payment" xml:space="preserve">
    <value>ಪಾವತಿ</value>
  </data>
  <data name="PaymentDetails" xml:space="preserve">
    <value>ಪಾವತಿ ವಿವರಗಳು</value>
  </data>
  <data name="PaymentDueDate" xml:space="preserve">
    <value>ಪಾವತಿ ಕಾರಣ ದಿನಾಂಕ</value>
  </data>
  <data name="PaymentDueDateCannotbelessthanInvoicedate" xml:space="preserve">
    <value>ಪಾವತಿ ಕಾರಣ ದಿನಾಂಕ ಸರಕುಪಟ್ಟಿ ದಿನಾಂಕ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="PaymentMode" xml:space="preserve">
    <value>ಪಾವತಿ ಮೋಡ್</value>
  </data>
  <data name="PaymentTerms" xml:space="preserve">
    <value>ಪಾವತಿ ನಿಯಮಗಳು</value>
  </data>
  <data name="PCD" xml:space="preserve">
    <value>ಪ್ರಾಮಿಸ್ಡ್ ಪೂರ್ಣಗೊಂಡ ದಿನಾಂಕ</value>
  </data>
  <data name="PCDShouldBeGreaterThanCallDate" xml:space="preserve">
    <value>ಪ್ರಾಮಿಸ್ಡ್ ಪೂರ್ಣಗೊಂಡ ದಿನಾಂಕ ಕರೆ ದಿನಾಂಕ ಹೆಚ್ಚಿರಬೇಕು</value>
  </data>
  <data name="PDF" xml:space="preserve">
    <value>ಪಿಡಿಎಫ್</value>
  </data>
  <data name="Pending" xml:space="preserve">
    <value>ಬಾಕಿ</value>
  </data>
  <data name="PendingAmount" xml:space="preserve">
    <value>ಬಾಕಿ ವೊತ್ತ</value>
  </data>
  <data name="PendingEnquires" xml:space="preserve">
    <value>ಬಾಕಿ ವಿಚಾರಣೆ</value>
  </data>
  <data name="PendingForApproval" xml:space="preserve">
    <value>ಅನುಮೋದನೆ ಬಾಕಿ</value>
  </data>
  <data name="PendingforHOApproval" xml:space="preserve">
    <value>ಹೋ ಅನುಮೋದನೆಗೆ ಬಾಕಿ</value>
  </data>
  <data name="PendingforOEMApproval" xml:space="preserve">
    <value>OEM ಅನುಮೋದನೆಗೆ ಬಾಕಿ</value>
  </data>
  <data name="PendingOrdersForDispatch" xml:space="preserve">
    <value>ಡಿಸ್ಪ್ಯಾಚ್ ಬಾಕಿ ಆದೇಶಗಳನ್ನು</value>
  </data>
  <data name="PendingOrdersForInvoice" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಬಾಕಿ ಆದೇಶಗಳನ್ನು</value>
  </data>
  <data name="PendingOrdersForInvoiceorDispatch" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ / ಡಿಸ್ಪ್ಯಾಚ್ ಬಾಕಿ ಆದೇಶಗಳನ್ನು</value>
  </data>
  <data name="PendingPartsOrder" xml:space="preserve">
    <value>ಬಾಕಿ ಭಾಗಗಳು ಆದೇಶ</value>
  </data>
  <data name="PendingPIForBOE" xml:space="preserve">
    <value>ಎಂಟ್ರಿ ಬಿಲ್ ಬಾಕಿಯಿದೆ ಖರೀದಿ ಸರಕುಪಟ್ಟಿ</value>
  </data>
  <data name="PendingPIForGoodsReleaseNote" xml:space="preserve">
    <value>ಗೂಡ್ಸ್ ಬಿಡುಗಡೆ ಗಮನಿಸಿ ಬಾಕಿ ಖರೀದಿ ಸರಕುಪಟ್ಟಿ</value>
  </data>
  <data name="PendingPIForPGRN" xml:space="preserve">
    <value>ಖರೀದಿ ಜಿ ಆರ್ ಎನ್ ವರದಿ ನೆನೆಗುದಿಗೆ ಖರೀದಿ ಸರಕುಪಟ್ಟಿ</value>
  </data>
  <data name="PendingProductTransferNoteForProductTransferGRN" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಟ್ರಾನ್ಸ್ಫರ್ ಜಿ ಆರ್ ಎನ್ ನೆನೆಗುದಿಗೆ ಉತ್ಪನ್ನ ಟ್ರಾನ್ಸ್ಫರ್ ಗಮನಿಸಿ</value>
  </data>
  <data name="PendingProductTransferRequestForProductTransferNote" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಟ್ರಾನ್ಸ್ಫರ್ ಗಮನಿಸಿ ಬಾಕಿ ಉತ್ಪನ್ನ ವರ್ಗಾವಣೆ ಮನವಿ</value>
  </data>
  <data name="PendingPurchaseOrder" xml:space="preserve">
    <value>ಬಾಕಿ ಪರ್ಚೇಸ್ ಆರ್ಡರ್</value>
  </data>
  <data name="PendingPurchaseOrderQuantity" xml:space="preserve">
    <value>ಬಾಕಿ ಖರೀದಿ ಆದೇಶದ ಪ್ರಮಾಣವನ್ನು</value>
  </data>
  <data name="PendingPurchaseOrdersForInvoiceReport" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ವರದಿ ನೆನೆಗುದಿಗೆ ಖರೀದಿ ಆದೇಶಗಳನ್ನು</value>
  </data>
  <data name="PendingSalesOrderQuantity" xml:space="preserve">
    <value>ಬಾಕಿ ಮಾರಾಟ ಆರ್ಡರ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="PendingSalesQuotationsReport" xml:space="preserve">
    <value>ಬಾಕಿ ಮಾರಾಟ ಉಲ್ಲೇಖಗಳು ವರದಿಮಾಡಿ</value>
  </data>
  <data name="PercAvail" xml:space="preserve">
    <value>PERC ಸಹಾಯಮಾಡು</value>
  </data>
  <data name="Percentage" xml:space="preserve">
    <value>ಶೇಕಡಾವಾರು</value>
  </data>
  <data name="PercentageDeviation" xml:space="preserve">
    <value>ಶೇಕಡಾವಾರು ಮಾರ್ಗಾಂತರ</value>
  </data>
  <data name="PeriodCannotbeGreaterthan12Months" xml:space="preserve">
    <value>ಅವಧಿ ಗ್ರೇಟರ್ 12 ತಿಂಗಳಿಗಿಂತ ಹೆಚ್ಚು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="PeriodInMonth" xml:space="preserve">
    <value>ತಿಂಗಳಲ್ಲಿ ಅವಧಿ</value>
  </data>
  <data name="Personal" xml:space="preserve">
    <value>ವೈಯಕ್ತಿಕ</value>
  </data>
  <data name="PersonalCalender" xml:space="preserve">
    <value>ವೈಯಕ್ತಿಕ ಕ್ಯಾಲೆಂಡರ್</value>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>ದೂರವಾಣಿ</value>
  </data>
  <data name="PhoneNo" xml:space="preserve">
    <value>ದೂರವಾಣಿ</value>
  </data>
  <data name="PhysicalStock" xml:space="preserve">
    <value>ಶಾರೀರಿಕ ಸ್ಟಾಕ್</value>
  </data>
  <data name="PickedBy" xml:space="preserve">
    <value>ಮೂಲಕ ಆಯ್ಕೆ</value>
  </data>
  <data name="PickedQty" xml:space="preserve">
    <value>ಆಯ್ಕೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="PickedqtyCannotbegreaterthanAllocatedQty" xml:space="preserve">
    <value>ಆಯ್ಕೆ ಪ್ರಮಾಣ ಹಂಚಿಕೆ ಪ್ರಮಾಣ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="PickedQuantity" xml:space="preserve">
    <value>ಆಯ್ಕೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="PickListConfirmation" xml:space="preserve">
    <value>ಪಟ್ಟಿ ದೃಢೀಕರಣ ಆರಿಸಿ</value>
  </data>
  <data name="PickListConfirmationDate" xml:space="preserve">
    <value>ಪಟ್ಟಿ ದೃಢೀಕರಣ ದಿನಾಂಕ ಆರಿಸಿ</value>
  </data>
  <data name="PickListConfirmationNumber" xml:space="preserve">
    <value>ಪಟ್ಟಿ ದೃಢೀಕರಣ ಸಂಖ್ಯೆ ಆರಿಸಿ</value>
  </data>
  <data name="PickListNumber" xml:space="preserve">
    <value>ಪಟ್ಟಿ ಸಂಖ್ಯೆ ಆರಿಸಿ</value>
  </data>
  <data name="PieChart" xml:space="preserve">
    <value>ಪೈ ಚಾರ್ಟ್</value>
  </data>
  <data name="PKGMonth" xml:space="preserve">
    <value>PKG ತಿಂಗಳ</value>
  </data>
  <data name="PlannedCompletionDate" xml:space="preserve">
    <value>ಯೋಜಿತ ಪೂರ್ಣಗೊಂಡ ದಿನಾಂಕ</value>
  </data>
  <data name="Plannedcompletiondatecannotbelessplannedstartdate" xml:space="preserve">
    <value>ಯೋಜಿತ ಪೂರ್ಣಗೊಂಡ ದಿನಾಂಕ ಕಡಿಮೆ ಆರಂಭದ ದಿನಾಂಕ ಯೋಜನೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="PlannedCompletionDatecannotbelessthanStartDate" xml:space="preserve">
    <value>ಯೋಜಿತ ಪೂರ್ಣಗೊಂಡ ದಿನಾಂಕ ಆರಂಭದ ದಿನಾಂಕ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="PlannedStartDate" xml:space="preserve">
    <value>ಯೋಜಿತ ಪ್ರಾರಂಭ ದಿನಾಂಕ</value>
  </data>
  <data name="Pleaseaddatleastoneoperationdetail" xml:space="preserve">
    <value>ಕನಿಷ್ಠ ಒಂದು ಕಾರ್ಯಾಚರಣೆ ವಿವರ ಸೇರಿಸಲು ದಯವಿಟ್ಟು</value>
  </data>
  <data name="pleasecompleteenteringdetails" xml:space="preserve">
    <value>ವಿವರಗಳು ಪ್ರವೇಶಿಸುವ ಪೂರ್ಣಗೊಳಿಸಿ</value>
  </data>
  <data name="PleaseDeallocateReallocatesomequantity" xml:space="preserve">
    <value>ದಯವಿಟ್ಟು ಡಿ ನಿಯೋಜಿಸಿ ಮತ್ತು ಕೆಲವು ಪ್ರಮಾಣದಲ್ಲಿ ಮರು ನಿಯೋಜಿಸಿ</value>
  </data>
  <data name="Pleasedeallocatesomequantity" xml:space="preserve">
    <value>ಕೆಲವು ಪ್ರಮಾಣ deallocate ದಯವಿಟ್ಟು</value>
  </data>
  <data name="Pleasedefineworkingdays" xml:space="preserve">
    <value>ಕೆಲಸ ದಿನಗಳ ವ್ಯಾಖ್ಯಾನಿಸಲು ದಯವಿಟ್ಟು</value>
  </data>
  <data name="PleaseEnterArticleContent" xml:space="preserve">
    <value>ಲೇಖನ ವಿಷಯವನ್ನು ನಮೂದಿಸಿ</value>
  </data>
  <data name="PleaseEnterArticleName" xml:space="preserve">
    <value>ಲೇಖನ ಹೆಸರನ್ನು ನಮೂದಿಸಿ</value>
  </data>
  <data name="PleaseenteratleastonePartDetails" xml:space="preserve">
    <value>ಕನಿಷ್ಠ ಒಂದು ಭಾಗ ವಿವರಗಳು ನಮೂದಿಸಿ</value>
  </data>
  <data name="PleaseEnterDateAs" xml:space="preserve">
    <value>ದಿನಾಂಕ ಯನ್ನು ದಯವಿಟ್ಟು</value>
  </data>
  <data name="Pleaseentereffectivefromdate" xml:space="preserve">
    <value>ದಿನಾಂಕದಿಂದ ಪರಿಣಾಮಕಾರಿ ನಮೂದಿಸಿ</value>
  </data>
  <data name="PleaseenterIntegerValue" xml:space="preserve">
    <value>ಪೂರ್ಣಾಂಕ ಮೌಲ್ಯ ನಮೂದಿಸಿ</value>
  </data>
  <data name="PleaseenterIntegerValue1" xml:space="preserve">
    <value>ಪೂರ್ಣಾಂಕ ಮೌಲ್ಯ ನಮೂದಿಸಿ</value>
  </data>
  <data name="PleaseEnterLoginID" xml:space="preserve">
    <value>ಲಾಗಿನ್ ID ಅನ್ನು ನಮೂದಿಸಿ</value>
  </data>
  <data name="Pleaseenteronlyintegernumber" xml:space="preserve">
    <value>ಕೇವಲ ಪೂರ್ಣಾಂಕ ಸಂಖ್ಯೆ ನಮೂದಿಸಿ</value>
  </data>
  <data name="PleaseEnterPartPrefix" xml:space="preserve">
    <value>ಭಾಗ ಪೂರ್ವಪ್ರತ್ಯಯ ನಮೂದಿಸಿ</value>
  </data>
  <data name="PleaseenterPartyName" xml:space="preserve">
    <value>ಪಕ್ಷದ ಹೆಸರು ನಮೂದಿಸಿ</value>
  </data>
  <data name="PleaseEnterPassword" xml:space="preserve">
    <value>ದಯವಿಟ್ಟು ಪಾಸ್ವರ್ಡ್ ಅನ್ನು ನಮೂದಿಸಿ</value>
  </data>
  <data name="Pleaseenterquantity" xml:space="preserve">
    <value>ಪ್ರಮಾಣ ನಮೂದಿಸಿ</value>
  </data>
  <data name="PleaseenterReportHeader" xml:space="preserve">
    <value>ವರದಿ ಹೆಡರ್ ನಮೂದಿಸಿ</value>
  </data>
  <data name="PleaseenterReportName" xml:space="preserve">
    <value>ವರದಿ ಹೆಸರು ನಮೂದಿಸಿ</value>
  </data>
  <data name="Pleaseenterserailnumber" xml:space="preserve">
    <value>ಸರಣಿ ಸಂಖ್ಯೆ ನಮೂದಿಸಿ</value>
  </data>
  <data name="PleaseEnterSerialNumber" xml:space="preserve">
    <value>ಕ್ರಮ ಸಂಖ್ಯೆ ಯನ್ನು ದಯವಿಟ್ಟು</value>
  </data>
  <data name="PleaseEnterSiblingName" xml:space="preserve">
    <value>ಸಹೋದರ ಹೆಸರು ನಮೂದಿಸಿ</value>
  </data>
  <data name="PleaseEnterSubfolderName" xml:space="preserve">
    <value>Subfolder ಹೆಸರು ನಮೂದಿಸಿ</value>
  </data>
  <data name="Pleaseentersupplier" xml:space="preserve">
    <value>ಪೂರೈಕೆದಾರ ನಮೂದಿಸಿ</value>
  </data>
  <data name="PleaseEnterTaxableOtherCharges" xml:space="preserve">
    <value>ತೆರಿಗೆ ಇತರೆ ಚಾರ್ಜಸ್ ಯನ್ನು ದಯವಿಟ್ಟು</value>
  </data>
  <data name="Pleaseenteruserdetails" xml:space="preserve">
    <value>ಬಳಕೆದಾರ ವಿವರಗಳನ್ನು ನಮೂದಿಸಿ</value>
  </data>
  <data name="pleaseentervalidmodel" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಮಾದರಿಯನ್ನು</value>
  </data>
  <data name="Pleaseentervalue" xml:space="preserve">
    <value>ಮೌಲ್ಯ ನಮೂದಿಸಿ</value>
  </data>
  <data name="Pleaseentervalue1" xml:space="preserve">
    <value>ಮೌಲ್ಯ 1 ನಮೂದಿಸಿ</value>
  </data>
  <data name="PleaseprovideMenuName" xml:space="preserve">
    <value>ಮೆನು ಹೆಸರನ್ನು ಒದಗಿಸಿ</value>
  </data>
  <data name="PleaseprovideModuleName" xml:space="preserve">
    <value>ಘಟಕ ಹೆಸರನ್ನು ಒದಗಿಸಿ</value>
  </data>
  <data name="Pleaseprovidepassword" xml:space="preserve">
    <value>ಪಾಸ್ವರ್ಡ್ ಒದಗಿಸಿ</value>
  </data>
  <data name="PleaseProvideTheProperBreakTime" xml:space="preserve">
    <value>ಸರಿಯಾದ ವಿರಾಮದ ಸಮಯದಲ್ಲಿ ಒದಗಿಸಿ</value>
  </data>
  <data name="PleaseProvideTheProperWorkingTime" xml:space="preserve">
    <value>ಸರಿಯಾದ ಕಾರ್ಮಿಕ ಸಮಯ ಒದಗಿಸಿ</value>
  </data>
  <data name="pleasesavetheOperationdata" xml:space="preserve">
    <value>ಕಾರ್ಯಾಚರಣೆ ವಿವರಗಳು ಉಳಿಸಲು ದಯವಿಟ್ಟು</value>
  </data>
  <data name="pleasesavethepartsdata" xml:space="preserve">
    <value>ಭಾಗಗಳು ವಿವರಗಳು ಉಳಿಸಲು ದಯವಿಟ್ಟು</value>
  </data>
  <data name="pleasesavetheServicedata" xml:space="preserve">
    <value>ಸೇವೆ ವಿವರಗಳು ಉಳಿಸಲು ದಯವಿಟ್ಟು</value>
  </data>
  <data name="pleasesavethesundrydata" xml:space="preserve">
    <value>ಬಗೆಬಗೆಯ ವಿವರಗಳು ಉಳಿಸಲು ದಯವಿಟ್ಟು</value>
  </data>
  <data name="PleaseselectaColumn" xml:space="preserve">
    <value>ಒಂದು ಅಂಕಣ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="PleaseselectaCondition" xml:space="preserve">
    <value>ಒಂದು ಕಂಡಿಶನ್ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="Pleaseselectafiletoupload" xml:space="preserve">
    <value>ಅಪ್ಲೋಡ್ ಮಾಡಲು ಫೈಲ್ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="PleaseselectaOperator" xml:space="preserve">
    <value>ಒಂದು ಆಪರೇಟರ್ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="PleaseSelectBinLocation" xml:space="preserve">
    <value>ಬಿನ್ ಸ್ಥಳ ಆಯ್ಕೆಮಾಡಿ</value>
  </data>
  <data name="PleaseSelectBranch" xml:space="preserve">
    <value>ಶಾಖೆ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="PleaseselectBrand" xml:space="preserve">
    <value>ಬ್ರ್ಯಾಂಡ್ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="PleaseselectBrandandProductType" xml:space="preserve">
    <value>ಗುರುತು ಮತ್ತು ಉತ್ಪನ್ನ ಪ್ರಕಾರವನ್ನು ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="Pleaseselectcompany" xml:space="preserve">
    <value>ಕಂಪನಿ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="Pleaseselectcondition" xml:space="preserve">
    <value>ಪರಿಸ್ಥಿತಿ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="PleaseSelectCorePart" xml:space="preserve">
    <value>ತಿರುಳಾಗಿದೆ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="PleaseSelectDocumentType" xml:space="preserve">
    <value>ದಾಖಲೆ ಪ್ರಕಾರ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="PleaseselectFile" xml:space="preserve">
    <value>ಫೈಲ್ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="PleaseSelectGraphCategory" xml:space="preserve">
    <value>ಗ್ರಾಫ್ ವರ್ಗ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="PleaseSelectGraphType" xml:space="preserve">
    <value>ಗ್ರಾಫ್ ಪ್ರಕಾರವನ್ನು ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="PleaseselectIssueArea" xml:space="preserve">
    <value>ಸಂಚಿಕೆ ಪ್ರದೇಶ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="pleaseselectModel" xml:space="preserve">
    <value>ಮಾದರಿ ಆಯ್ಕೆಮಾಡಿ</value>
  </data>
  <data name="PleaseselectmodelandSerialNumber" xml:space="preserve">
    <value>ಆಯ್ದ ಮಾದರಿ ಮತ್ತು ಸರಣಿ ಸಂಖ್ಯೆ ಸಂತೋಷಪಡಿಸಿ</value>
  </data>
  <data name="PleaseselectmodelandSerialNumber1" xml:space="preserve">
    <value>ಸಂತೋಷ ಚುನಾಯಿತ ಮಾದರಿ ಮತ್ತು ಕ್ರಮ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="PleaseselectmodelandSerialNumber11" xml:space="preserve">
    <value>ಆಯ್ದ ಮಾದರಿ ಮತ್ತು ಸರಣಿ ಸಂಖ್ಯೆ ಸಂತೋಷಪಡಿಸಿ</value>
  </data>
  <data name="Pleaseselectoperator" xml:space="preserve">
    <value>ಆಯೋಜಕರು ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="Pleaseselectorderclass" xml:space="preserve">
    <value>ಸಲುವಾಗಿ ವರ್ಗ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="Pleaseselectordertype" xml:space="preserve">
    <value>ಸಲುವಾಗಿ ಪ್ರಕಾರವನ್ನು ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="pleaseselectpartnumber" xml:space="preserve">
    <value>ಭಾಗ ಸಂಖ್ಯೆ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="Pleaseselectpartsorder" xml:space="preserve">
    <value>ಭಾಗಗಳು ಆದೇಶ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="PleaseSelectParty" xml:space="preserve">
    <value>ಪಕ್ಷದ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="Pleaseselectpicklist" xml:space="preserve">
    <value>ಆಯ್ಕೆ ಪಟ್ಟಿ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="PleaseSelectProduct" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="PleaseSelectProductType" xml:space="preserve">
    <value>SelectProductType ದಯವಿಟ್ಟು</value>
  </data>
  <data name="Pleaseselectpurchaseinvoice" xml:space="preserve">
    <value>ಖರೀದಿ ಸರಕುಪಟ್ಟಿ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="Pleaseselectpurchaseorder" xml:space="preserve">
    <value>ಖರೀದಿ ಆದೇಶವನ್ನು ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="PleaseselectQuestionnaireLevel1" xml:space="preserve">
    <value>ಪ್ರಶ್ನಾವಳಿಗಳ Level1 ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="PleaseselectQuestionnaireLevel2" xml:space="preserve">
    <value>ಪ್ರಶ್ನಾವಳಿಗಳ Level2 ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="Pleaseselectrecordstodelete" xml:space="preserve">
    <value>ಅಳಿಸಲು ದಾಖಲೆಗಳನ್ನು ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="Pleaseselectrequestingbranch" xml:space="preserve">
    <value>ಮನವಿ ಶಾಖೆ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="Pleaseselectserialnumber" xml:space="preserve">
    <value>ಕ್ರಮಸಂಖ್ಯೆ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="PleaseselectServicerequestnumber" xml:space="preserve">
    <value>ಸೇವೆ ವಿನಂತಿಯನ್ನು ಸಂಖ್ಯೆ ಆಯ್ಕೆಮಾಡಿ</value>
  </data>
  <data name="Pleaseselectsupplier" xml:space="preserve">
    <value>ಪೂರೈಕೆದಾರ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="PleaseSelectTaxType" xml:space="preserve">
    <value>TaxType ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="PleaseselecttheColumnName" xml:space="preserve">
    <value>ಕಾಲಮ್ ಹೆಸರು ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="Pleaseselectthecolumnstomap" xml:space="preserve">
    <value>ನಕ್ಷೆ ಕಾಲಮ್ಗಳನ್ನು ಆಯ್ಕೆಮಾಡಿ</value>
  </data>
  <data name="Pleaseselecttheoperator" xml:space="preserve">
    <value>ಆಯೋಜಕರು ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="Pleaseselectthesupplier" xml:space="preserve">
    <value>ಪೂರೈಕೆದಾರ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="PleaseselecttheTableName" xml:space="preserve">
    <value>ಟೇಬಲ್ ಹೆಸರು ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="Pleaseselectthewarehouse" xml:space="preserve">
    <value>ಸಾಮಾನು ಮನೆ ಆಯ್ಕೆಮಾಡಿ</value>
  </data>
  <data name="Pleaseselecttypeofpurchase" xml:space="preserve">
    <value>ಖರೀದಿ ಮಾದರಿ ಆಯ್ಕೆಮಾಡಿ</value>
  </data>
  <data name="PleaseselectUserstosave" xml:space="preserve">
    <value>ಉಳಿಸಲು ಬಳಕೆದಾರರು ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="PleaseSelectWareHouse" xml:space="preserve">
    <value>ಗೋದಾಮಿನ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="PlsEnterLCNumber" xml:space="preserve">
    <value>ಎಲ್ ಸಿ ಸಂಖ್ಯೆ ನಮೂದಿಸಿ</value>
  </data>
  <data name="PlsEnterTaxableOtherCharges" xml:space="preserve">
    <value>Pls ತೆರಿಗೆ ಇತರ ಆರೋಪಗಳನ್ನು ನಮೂದಿಸಿ</value>
  </data>
  <data name="PlsPurchaseInvoiceNumber" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ ಖರೀದಿಸಿ ದಯವಿಟ್ಟು</value>
  </data>
  <data name="PlsSelectExpenditureName" xml:space="preserve">
    <value>ವೆಚ್ಚ ಹೆಸರು ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="PlsSelectReasonsForLossOfSale" xml:space="preserve">
    <value>Pls ಮಾರಾಟ ನಷ್ಟ ಕಾರಣಗಳು ಆಯ್ಕೆ</value>
  </data>
  <data name="POAmount" xml:space="preserve">
    <value>ಪಿಒ ಪ್ರಮಾಣ</value>
  </data>
  <data name="POCancellation" xml:space="preserve">
    <value>ಆರ್ಡರ್ ರದ್ದತಿ ಖರೀದಿಸಿ</value>
  </data>
  <data name="POCancellationNumber" xml:space="preserve">
    <value>ಪಿಒ ರದ್ದತಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="PolicyDate" xml:space="preserve">
    <value>ನೀತಿ ದಿನಾಂಕ</value>
  </data>
  <data name="PolicyNumber" xml:space="preserve">
    <value>ನೀತಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="POQuantity" xml:space="preserve">
    <value>ಪಿಒ ಪ್ರಮಾಣ</value>
  </data>
  <data name="POStatus" xml:space="preserve">
    <value>ಪಿಒ ಸ್ಥಿತಿ</value>
  </data>
  <data name="PrecedingPartDetails" xml:space="preserve">
    <value>ಭಾಗ ವಿವರಗಳು ಹಿಂದಿನ</value>
  </data>
  <data name="PreffixSuffixDoesntExists" xml:space="preserve">
    <value>ಪೂರ್ವಪ್ರತ್ಯಯ ಪ್ರತ್ಯಯ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="PreffixSuffixDoesntExistsForGDR" xml:space="preserve">
    <value>Preffix ಪ್ರತ್ಯಯ doesnot GDR ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="PreffixSuffixDoesntExistsForPartsorder" xml:space="preserve">
    <value>ಭಾಗಗಳು ಆದೇಶಕ್ಕೆ Preffix ಪ್ರತ್ಯಯ doesnot ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="PreffixSuffixDoesntExistsforPartsOrderinoem" xml:space="preserve">
    <value>ಭಾಗಗಳು OEM ನಲ್ಲಿ ಆದೇಶಕ್ಕೆ Preffix ಪ್ರತ್ಯಯ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="prefix" xml:space="preserve">
    <value>ಮೊದಲೇ ಜೋಡಿಸು</value>
  </data>
  <data name="prefixsuffix" xml:space="preserve">
    <value>ಪೂರ್ವಪ್ರತ್ಯಯ ಪ್ರತ್ಯಯ</value>
  </data>
  <data name="prefixsuffix1" xml:space="preserve">
    <value>PrefixSuffix</value>
  </data>
  <data name="prefixsuffix11" xml:space="preserve">
    <value>ಪೂರ್ವಪ್ರತ್ಯಯ ಪ್ರತ್ಯಯ</value>
  </data>
  <data name="Previous" xml:space="preserve">
    <value>ಹಿಂದಿನ</value>
  </data>
  <data name="PreviousdateCannotbeempty" xml:space="preserve">
    <value>ಹಿಂದಿನ ದಿನಾಂಕ ಖಾಲಿ ಇರುವಂತಿಲ್ಲ</value>
  </data>
  <data name="PreviousServiceAgreementwillbeInactivewouldyouliketoProceed" xml:space="preserve">
    <value>ಹಿಂದಿನ ಸೇವೆ ಒಪ್ಪಂದದ ನೀವು ಮುಂದುವರೆಯಲು ನಿಷ್ಕ್ರಿಯ ಬಯಸುತ್ತೀರಿ ಮಾಡಲಾಗುತ್ತದೆ</value>
  </data>
  <data name="PreviousStockstatement" xml:space="preserve">
    <value>ಹಿಂದಿನ ಸ್ಟಾಕ್ ಹೇಳಿಕೆ</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>ಬೆಲೆ</value>
  </data>
  <data name="PricecannotbeBlankorZero" xml:space="preserve">
    <value>ಬೆಲೆ ಖಾಲಿ ಅಥವಾ ಶೂನ್ಯ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="PriceList" xml:space="preserve">
    <value>ಮಾರಾಟ ಬೆಲೆ</value>
  </data>
  <data name="PrimaryDealer" xml:space="preserve">
    <value>ಪ್ರಾಥಮಿಕ ಹಾಕುವವನು</value>
  </data>
  <data name="PrimarySegment" xml:space="preserve">
    <value>ಪ್ರಾಥಮಿಕ ಸೆಗ್ಮೆಂಟ್</value>
  </data>
  <data name="Print" xml:space="preserve">
    <value>ಮುದ್ರಣ</value>
  </data>
  <data name="PrintAction" xml:space="preserve">
    <value>ಪ್ರಿಂಟ್ ಆಕ್ಷನ್</value>
  </data>
  <data name="PrintFinancier" xml:space="preserve">
    <value>ಪ್ರಿಂಟ್ ಫೈನಾನ್ಷಿಯರ್</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>ಆದ್ಯತೆ</value>
  </data>
  <data name="PriorityShouldBeBetweenzeroandtwofiftyfive" xml:space="preserve">
    <value>ಆದ್ಯತಾ 0 ಮತ್ತು 255 ರ ನಡುವಿನ ಇರಬೇಕು</value>
  </data>
  <data name="Probability" xml:space="preserve">
    <value>ಸಂಭವನೀಯತೆ</value>
  </data>
  <data name="product" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ</value>
  </data>
  <data name="ProductAssociation" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಅಸೋಸಿಯೇಷನ್</value>
  </data>
  <data name="productdetail" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ವಿವರ</value>
  </data>
  <data name="ProductDetailCannotbeblank" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ವಿವರ ಖಾಲಿ ಇರಕೂಡದು</value>
  </data>
  <data name="ProductDetailReport" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ವಿವರ ವರದಿ</value>
  </data>
  <data name="productdetails" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ವಿವರಗಳು</value>
  </data>
  <data name="productid" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ID</value>
  </data>
  <data name="ProductisinactiveorScrap" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಸಕ್ರಿಯ ಅಥವಾ ಸ್ಕ್ರ್ಯಾಪ್ ಆಗಿದೆ</value>
  </data>
  <data name="Productisnotasscociatedwithanycustomer" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಪ್ರಸ್ತುತ ಯಾವುದೇ ಗ್ರಾಹಕ asscociated ಇಲ್ಲ</value>
  </data>
  <data name="ProductLocation" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಸ್ಥಳ</value>
  </data>
  <data name="ProductPurchaseGRN" xml:space="preserve">
    <value>ಉತ್ಪನ್ನದ ಖರೀದಿಯ ಜಿ ಆರ್ ಎನ್</value>
  </data>
  <data name="ProductPurchaseInvoice" xml:space="preserve">
    <value>ಉತ್ಪನ್ನದ ಖರೀದಿಯ ಸರಕುಪಟ್ಟಿ</value>
  </data>
  <data name="ProductPurchaseOrder" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಪರ್ಚೇಸ್ ಆರ್ಡರ್</value>
  </data>
  <data name="ProductReading" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಓದುವಿಕೆ</value>
  </data>
  <data name="ProductSalesInvoice" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ</value>
  </data>
  <data name="ProductSalesInvoiceCancellationDate" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ ರದ್ದು ದಿನಾಂಕ</value>
  </data>
  <data name="ProductSalesInvoiceCancellationNumber" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ ರದ್ದತಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ProductSalesInvoiceDate" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ ದಿನಾಂಕ</value>
  </data>
  <data name="ProductSalesInvoiceNumber" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ProductSalesOrder" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಮಾರಾಟದ ಆರ್ಡರ್</value>
  </data>
  <data name="ProductSalesOrderAllocation" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಮಾರಾಟದ ಆರ್ಡರ್ ಹಂಚಿಕ</value>
  </data>
  <data name="ProductSalesOrderAmmendment" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಮಾರಾಟದ ಆರ್ಡರ್ ತಿದ್ದುಪಡಿ</value>
  </data>
  <data name="ProductSalesOrderArchive" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಮಾರಾಟದ ಆರ್ಡರ್ ಆರ್ಕೈವ್</value>
  </data>
  <data name="ProductSalesOrderDate" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಮಾರಾಟದ ಆದೇಶ ದಿನಾಂಕ</value>
  </data>
  <data name="ProductSalesOrderNumber" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಮಾರಾಟದ ಆದೇಶ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ProductSalesOrderStatus" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಮಾರಾಟದ ಆರ್ಡರ್ ಸ್ಥಿತಿ</value>
  </data>
  <data name="ProductSchedule" xml:space="preserve">
    <value>ಪ್ರೊಡಕ್ಟ್ ಶೆಡ್ಯುಲ್</value>
  </data>
  <data name="ProductServiceHistory" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಸೇವೆ ಇತಿಹಾಸ</value>
  </data>
  <data name="ProductStockStatement" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಸ್ಟಾಕ್ ಹೇಳಿಕೆ</value>
  </data>
  <data name="ProductTotalAmount" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಒಟ್ಟು ಪ್ರಮಾಣ</value>
  </data>
  <data name="ProductTransferGRN" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಟ್ರಾನ್ಸ್ಫರ್ ಜಿ ಆರ್ ಎನ್</value>
  </data>
  <data name="ProductTransferGRNDate" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಟ್ರಾನ್ಸ್ಫರ್ ಜಿ ಆರ್ ಎನ್ ದಿನಾಂಕ</value>
  </data>
  <data name="ProductTransferGRNDetailReport" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಟ್ರಾನ್ಸ್ಫರ್ ಜಿ ಆರ್ ಎನ್ ವಿವರ ವರದಿ</value>
  </data>
  <data name="ProductTransferGRNNumber" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಟ್ರಾನ್ಸ್ಫರ್ ಜಿ ಆರ್ ಎನ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ProductTransferNote" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಟ್ರಾನ್ಸ್ಫರ್ ಗಮನಿಸಿ</value>
  </data>
  <data name="ProductTransferNoteDate" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಟ್ರಾನ್ಸ್ಫರ್ ಗಮನಿಸಿ ದಿನಾಂಕ</value>
  </data>
  <data name="ProductTransferNoteDetailReport" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಟ್ರಾನ್ಸ್ಫರ್ ಗಮನಿಸಿ ವಿವರ ವರದಿ</value>
  </data>
  <data name="ProductTransferNoteNumber" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಟ್ರಾನ್ಸ್ಫರ್ ನೋಟ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ProductTransferRequestdate" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ವರ್ಗಾವಣೆ ಮನವಿ ದಿನಾಂಕ</value>
  </data>
  <data name="ProductTransferRequestDetailReport" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ವರ್ಗಾವಣೆ ಮನವಿ ವಿವರ ವರದಿ</value>
  </data>
  <data name="ProductTransferRequestNumber" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ವರ್ಗಾವಣೆ ಮನವಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="Producttype" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಪ್ರಕಾರ</value>
  </data>
  <data name="ProductType1" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಪ್ರಕಾರ</value>
  </data>
  <data name="producttypeenglish" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಪ್ರಕಾರ ಇಂಗ್ಲೀಷ್</value>
  </data>
  <data name="producttypelocale" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಪ್ರಕಾರ ಲೊಕೇಲ್</value>
  </data>
  <data name="producttypemaster" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಪ್ರಕಾರ ಮಾಸ್ಟರ್</value>
  </data>
  <data name="producttypename" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಪ್ರಕಾರ ಹೆಸರು</value>
  </data>
  <data name="producttypenamealreadyexists" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಪ್ರಕಾರ ಹೆಸರು ಈಗಾಗಲೇ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="ProductUnderWarranty" xml:space="preserve">
    <value>ವಾರಂಟಿ ಉತ್ಪನ್ನ</value>
  </data>
  <data name="ProductUniqueNo" xml:space="preserve">
    <value>ಅಸದೃಶ</value>
  </data>
  <data name="ProductVariantAndAllocationDetails" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಭಿನ್ನ ಮತ್ತು ಹಂಚಿಕೆ ವಿವರಗಳು</value>
  </data>
  <data name="ProductVariantDetails" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಭಿನ್ನ ವಿವರಗಳು</value>
  </data>
  <data name="ProfitOrLoss" xml:space="preserve">
    <value>ಲಾಭ ಅಥವಾ ನಷ್ಟ</value>
  </data>
  <data name="ProfitValue" xml:space="preserve">
    <value>ಲಾಭ ಮೌಲ್ಯ</value>
  </data>
  <data name="PromisedReturnDate" xml:space="preserve">
    <value>ಪ್ರಾಮಿಸ್ಡ್ ಹಿಂದಿರುಗುವ ದಿನಾಂಕವನ್ನು</value>
  </data>
  <data name="PromisedReturnDateCannotbelessThanCurrentDate" xml:space="preserve">
    <value>ಪ್ರಾಮಿಸ್ಡ್ ಹಿಂದಿರುಗುವ ದಿನಾಂಕವನ್ನು ಪ್ರಸ್ತುತ ದಿನಾಂಕ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Prospect" xml:space="preserve">
    <value>ಪ್ರಾಸ್ಪೆಕ್ಟ್</value>
  </data>
  <data name="Publish" xml:space="preserve">
    <value>ಬಹಿರಂಗಪಡಿಸು</value>
  </data>
  <data name="PublishedSuccessfully" xml:space="preserve">
    <value>ಯಶಸ್ವಿಯಾಗಿ ಪ್ರಕಟಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="Purchase" xml:space="preserve">
    <value>ಖರೀದಿ</value>
  </data>
  <data name="PurchaseDetails" xml:space="preserve">
    <value>ಖರೀದಿ ವಿವರಗಳು</value>
  </data>
  <data name="PurchaseDetails1" xml:space="preserve">
    <value>ಖರೀದಿ ವಿವರಗಳು</value>
  </data>
  <data name="PurchaseGRN" xml:space="preserve">
    <value>ಜಿ ಆರ್ ಎನ್ ಖರೀದಿಸಿ</value>
  </data>
  <data name="PurchaseGRNDailyReport" xml:space="preserve">
    <value>ಜಿ ಆರ್ ಎನ್ ಡೈಲಿ ವರದಿ ಖರೀದಿಸಿ</value>
  </data>
  <data name="PurchaseGRNDate" xml:space="preserve">
    <value>ಖರೀದಿ ಜಿ ಆರ್ ಎನ್ ದಿನಾಂಕ</value>
  </data>
  <data name="PurchaseGRNDetailReport" xml:space="preserve">
    <value>ಖರೀದಿ ಜಿ ಆರ್ ಎನ್ ವಿವರ ವರದಿ</value>
  </data>
  <data name="PurchaseGRNMonthlyReport" xml:space="preserve">
    <value>ಜಿ ಆರ್ ಎನ್ ಮಾಸಿಕ ವರದಿ ಖರೀದಿಸಿ</value>
  </data>
  <data name="purchaseGRNnumber" xml:space="preserve">
    <value>ಜಿ ಆರ್ ಎನ್ ಸಂಖ್ಯೆ ಖರೀದಿಸಲು</value>
  </data>
  <data name="PurchaseInvoice" xml:space="preserve">
    <value>ಖರೀದಿ ಸರಕುಪಟ್ಟಿ</value>
  </data>
  <data name="PurchaseInvoiceBinningList" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ Binning ಪಟ್ಟಿ ಖರೀದಿಸಿ</value>
  </data>
  <data name="PurchaseInvoiceDailyReport" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಡೈಲಿ ವರದಿ ಖರೀದಿಸಿ</value>
  </data>
  <data name="PurchaseInvoiceDate" xml:space="preserve">
    <value>ಖರೀದಿ ಸರಕುಪಟ್ಟಿ ದಿನಾಂಕ</value>
  </data>
  <data name="PurchaseInvoiceDetailReport" xml:space="preserve">
    <value>ಖರೀದಿ ಸರಕುಪಟ್ಟಿ ವಿವರ ವರದಿ</value>
  </data>
  <data name="PurchaseInvoiceMonthlyReport" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಮಾಸಿಕ ವರದಿ ಖರೀದಿಸಿ</value>
  </data>
  <data name="PurchaseInvoiceNum" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ </value>
  </data>
  <data name="PurchaseInvoiceNumber" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ </value>
  </data>
  <data name="PurchaseInvoiceNumberFieldSearch" xml:space="preserve">
    <value>ಖರೀದಿ ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ ಫೀಲ್ಡ್ ಹುಡುಕು</value>
  </data>
  <data name="PurchaseInvoiceTaxDetail" xml:space="preserve">
    <value>ಖರೀದಿ ಸರಕುಪಟ್ಟಿ ತೆರಿಗೆ ವಿವರ</value>
  </data>
  <data name="PurchaseOrder" xml:space="preserve">
    <value>ಪರ್ಚೇಸ್ ಆರ್ಡರ್</value>
  </data>
  <data name="PurchaseOrderDailyReport" xml:space="preserve">
    <value>ಆರ್ಡರ್ ಡೈಲಿ ವರದಿ ಖರೀದಿಸಿ</value>
  </data>
  <data name="PurchaseOrderDate" xml:space="preserve">
    <value>ಪರ್ಚೇಸ್ ಆರ್ಡರ್ ದಿನಾಂಕ</value>
  </data>
  <data name="PurchaseOrderDetailReport" xml:space="preserve">
    <value>ಪರ್ಚೇಸ್ ಆರ್ಡರ್ ವಿವರ ವರದಿ</value>
  </data>
  <data name="PurchaseOrderMonthlyReport" xml:space="preserve">
    <value>ಆರ್ಡರ್ ಮಾಸಿಕ ವರದಿ ಖರೀದಿಸಿ</value>
  </data>
  <data name="PurchaseOrderNumber" xml:space="preserve">
    <value>ಆದೇಶ ಸಂಖ್ಯೆ ಖರೀದಿಸಿ</value>
  </data>
  <data name="PurchaseOrderNumberFieldSearch" xml:space="preserve">
    <value>ಪರ್ಚೇಸ್ ಆರ್ಡರ್ ಸಂಖ್ಯೆ ಫೀಲ್ಡ್ ಹುಡುಕು</value>
  </data>
  <data name="PurchaseOrderStatus" xml:space="preserve">
    <value>ಖರೀದಿ ಆದೇಶ ಸ್ಥಿತಿಗತಿಯ</value>
  </data>
  <data name="PurchaseOrderSummary" xml:space="preserve">
    <value>ಪರ್ಚೇಸ್ ಆರ್ಡರ್ ಸಾರಾಂಶ</value>
  </data>
  <data name="PurchasePriceVarianceReport" xml:space="preserve">
    <value>ಖರೀದಿ ಬೆಲೆ ವೈಷಮ್ಯವನ್ನು ವರದಿ</value>
  </data>
  <data name="PurchaseReferenceDate" xml:space="preserve">
    <value>ರೆಫರೆನ್ಸ್ ದಿನಾಂಕ</value>
  </data>
  <data name="PurchaseRefNumber" xml:space="preserve">
    <value>ಉಲ್ಲೇಖ ಸಂಖ್ಯೆ ಖರೀದಿಸಿ</value>
  </data>
  <data name="PurchaseType" xml:space="preserve">
    <value>ಖರೀದಿ ಪ್ರಕಾರ</value>
  </data>
  <data name="QRCode" xml:space="preserve">
    <value>QR ಕೋಡ್</value>
  </data>
  <data name="Qty" xml:space="preserve">
    <value>ಪ್ರಮಾಣ</value>
  </data>
  <data name="QtyofBOM" xml:space="preserve">
    <value>BOM ಪ್ರಮಾಣ</value>
  </data>
  <data name="QtyRequired" xml:space="preserve">
    <value>ಪ್ರಮಾಣ ಅಗತ್ಯ</value>
  </data>
  <data name="QTYtobeordered" xml:space="preserve">
    <value>ಆದೇಶ ಪ್ರಮಾಣ</value>
  </data>
  <data name="QualityChecklist" xml:space="preserve">
    <value>ಗುಣಮಟ್ಟ ಪರಿಶೀಲನಾಪಟ್ಟಿ</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>ಪ್ರಮಾಣ</value>
  </data>
  <data name="Quantitycannotbeblank" xml:space="preserve">
    <value>ಪ್ರಮಾಣ ಖಾಲಿ ಇರಕೂಡದು</value>
  </data>
  <data name="Quantitycannotbegreaterthan" xml:space="preserve">
    <value>ಪ್ರಮಾಣ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Quantitycannotbegreaterthanfreestock" xml:space="preserve">
    <value>ಪ್ರಮಾಣ ಉಚಿತ ಸ್ಟಾಕ್ ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Quantitycannotbegreaterthanreturnableqty" xml:space="preserve">
    <value>ಪ್ರಮಾಣ ವಾಪಸಾತಿ ಪ್ರಮಾಣ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="QuantityCanNotBeLessThanAllocatedQuantity" xml:space="preserve">
    <value>ಪ್ರಮಾಣ ನಿಗದಿ ಪ್ರಮಾಣ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="QuantityCanNotBeLessThanWonQuantity" xml:space="preserve">
    <value>ಪ್ರಮಾಣ ವೋನ್ ಪ್ರಮಾಣ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="QuantityCannotbeZero" xml:space="preserve">
    <value>ಪ್ರಮಾಣ ಶೂನ್ಯವಾಗಿರುವುದಿಲ್ಲ</value>
  </data>
  <data name="QuantityCannotbeZeroorBlank" xml:space="preserve">
    <value>ಪ್ರಮಾಣ 0 ಅಥವಾ ಖಾಲಿ ಇರಕೂಡದು</value>
  </data>
  <data name="QuantityDetails" xml:space="preserve">
    <value>ಪ್ರಮಾಣ ವಿವರಗಳು</value>
  </data>
  <data name="QuantityInStock" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="QuantityIssued" xml:space="preserve">
    <value>ನೀಡಲಾಗಿದೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="QuantityReceived" xml:space="preserve">
    <value>ಸ್ವೀಕರಿಸಲಾಗಿದೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="QuantityReleased" xml:space="preserve">
    <value>ಬಿಡುಗಡೆಯಾಗಿದೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="QuantityRequested" xml:space="preserve">
    <value>ಪ್ರಮಾಣ ವಿನಂತಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="QuantityRequired" xml:space="preserve">
    <value>ಪ್ರಮಾಣ ಅಗತ್ಯ</value>
  </data>
  <data name="QuantityRequiredCannotbelessthanQuantityRequested" xml:space="preserve">
    <value>ಅವಶ್ಯವಿರುವ ಪ್ರಮಾಣವನ್ನು ವಿನಂತಿಸಿದ ಪ್ರಮಾಣ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="QuantityReturned" xml:space="preserve">
    <value>ಪ್ರಮಾಣ ರಿಟರ್ನ್ಡ್</value>
  </data>
  <data name="QuantityToAllocate" xml:space="preserve">
    <value>ನಿಗದಿಪಡಿಸಬೇಕಾಗುತ್ತದೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="QuantitytoReturn" xml:space="preserve">
    <value>ಹಿಂತಿರುಗಿ ಪ್ರಮಾಣ</value>
  </data>
  <data name="QuantityUsed" xml:space="preserve">
    <value>ಉಪಯೋಗಿಸಿದ ಪ್ರಮಾಣ</value>
  </data>
  <data name="QuantityUsedisMandatoryinPartsDetail" xml:space="preserve">
    <value>ಉಪಯೋಗಿಸಿದ ಪ್ರಮಾಣ ಭಾಗಗಳು ವಿವರ ಕಡ್ಡಾಯ</value>
  </data>
  <data name="QuestInformaticsPrivateLimited" xml:space="preserve">
    <value>ಕ್ವೆಸ್ಟ್ ಇನ್ಫರ್ಮ್ಯಾಟಿಕ್ಸ್ ಪ್ರೈವೇಟ್ ಲಿಮಿಟೆಡ್</value>
  </data>
  <data name="Questionaries" xml:space="preserve">
    <value>ಪ್ರಶ್ನಾವಳಿಗಳು</value>
  </data>
  <data name="QuestionLevel1" xml:space="preserve">
    <value>ಪ್ರಶ್ನೆ Level1</value>
  </data>
  <data name="QuestionLevel2" xml:space="preserve">
    <value>ಪ್ರಶ್ನೆ Level2</value>
  </data>
  <data name="QuestionLevel3" xml:space="preserve">
    <value>ಪ್ರಶ್ನೆ Level3</value>
  </data>
  <data name="QuestionnaireLevel1" xml:space="preserve">
    <value>ಪ್ರಶ್ನಾವಳಿಗಳ Level1</value>
  </data>
  <data name="QuestionnaireLevel2" xml:space="preserve">
    <value>ಪ್ರಶ್ನಾವಳಿಗಳ Level2</value>
  </data>
  <data name="QuestionnaireLevel3" xml:space="preserve">
    <value>ಪ್ರಶ್ನಾವಳಿಗಳ Level3</value>
  </data>
  <data name="Queue" xml:space="preserve">
    <value>ಕ್ಯೂ</value>
  </data>
  <data name="Quicklinks" xml:space="preserve">
    <value>ತ್ವರಿತ ಲಿಂಕ್ಸ್</value>
  </data>
  <data name="Quotation" xml:space="preserve">
    <value>ಉದ್ಧರಣ</value>
  </data>
  <data name="QuotationAmount" xml:space="preserve">
    <value>ನುಡಿಮುತ್ತುಗಳು ಪ್ರಮಾಣ</value>
  </data>
  <data name="QuotationDate" xml:space="preserve">
    <value>ಉಲ್ಲೇಖ ದಿನಾಂಕ</value>
  </data>
  <data name="quotationdatewisesearch" xml:space="preserve">
    <value>ನುಡಿಮುತ್ತುಗಳು DateWise ಹುಡುಕು</value>
  </data>
  <data name="QuotationDetail" xml:space="preserve">
    <value>ನುಡಿಮುತ್ತುಗಳು ವಿವರ</value>
  </data>
  <data name="QuotationFinalAmount" xml:space="preserve">
    <value>ನುಡಿಮುತ್ತುಗಳು ಫೈನಲ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="Quotationisalreadycreated" xml:space="preserve">
    <value>ನುಡಿಮುತ್ತುಗಳು ಈಗಾಗಲೇ ರಚಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="QuotationisClosed" xml:space="preserve">
    <value>ನುಡಿಮುತ್ತುಗಳು ಮುಚ್ಚಲಾಗಿದೆ</value>
  </data>
  <data name="QuotationiscreatedforthisServiceRequestNumber" xml:space="preserve">
    <value>ನುಡಿಮುತ್ತುಗಳು ಈ ಸೇವೆ ವಿನಂತಿಯನ್ನು ರಚಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="QuotationNumber" xml:space="preserve">
    <value>ನುಡಿಮುತ್ತುಗಳು ಸಂಖ್ಯೆ</value>
  </data>
  <data name="QuotationPriority" xml:space="preserve">
    <value>ನುಡಿಮುತ್ತುಗಳು ಆದ್ಯತಾ</value>
  </data>
  <data name="QuotationQty" xml:space="preserve">
    <value>ನುಡಿಮುತ್ತುಗಳು ಪ್ರಮಾಣ</value>
  </data>
  <data name="QuotationValidity" xml:space="preserve">
    <value>ನುಡಿಮುತ್ತುಗಳು ವಾಯಿದೆ</value>
  </data>
  <data name="RangecannotbeBlank" xml:space="preserve">
    <value>ರೇಂಜ್ ಖಾಲಿ ಇರಕೂಡದು</value>
  </data>
  <data name="Rate" xml:space="preserve">
    <value>ದರ</value>
  </data>
  <data name="RateCannotbeZero" xml:space="preserve">
    <value>ದರ ಸೊನ್ನೆ ಇರುವಂತಿಲ್ಲ</value>
  </data>
  <data name="Rating" xml:space="preserve">
    <value>ಬೆಲೆ - ಕಟ್ಟುವುದು</value>
  </data>
  <data name="Ratingshouldbebetween1and10" xml:space="preserve">
    <value>ರೇಟಿಂಗ್ 1 ಮತ್ತು 10 ನಡುವೆ ಇರಬೇಕು</value>
  </data>
  <data name="RatingshouldBetween1and10" xml:space="preserve">
    <value>ರೇಟಿಂಗ್ ಮಾಡಬೇಕಾದುದು 1 ರಿಂದ 10</value>
  </data>
  <data name="Ratio" xml:space="preserve">
    <value>ಅನುಪಾತ%</value>
  </data>
  <data name="Read" xml:space="preserve">
    <value>ಓದು</value>
  </data>
  <data name="ReadAction" xml:space="preserve">
    <value>ಆಕ್ಷನ್ ಓದಿ</value>
  </data>
  <data name="reading" xml:space="preserve">
    <value>ಓದುವಿಕೆ</value>
  </data>
  <data name="ReadingDetails" xml:space="preserve">
    <value>ವಿವರ ಓದುವಿಕೆ</value>
  </data>
  <data name="ReadingEnteredCannotBeLessThanPreviousReading" xml:space="preserve">
    <value>ಪ್ರವೇಶಿಸಿತು ಓದುವಿಕೆ ಹಿಂದಿನ ಓದುವ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="readinglimit" xml:space="preserve">
    <value>ಮಿತಿ ಓದುವಿಕೆ</value>
  </data>
  <data name="readinglog" xml:space="preserve">
    <value>ಲಾಗ್ ಓದುವಿಕೆ</value>
  </data>
  <data name="ReadingShouldbegreaterthanFailureReading" xml:space="preserve">
    <value>ಓದುವಿಕೆ ವೈಫಲ್ಯ ಓದುವಿಕೆ ಹೆಚ್ಚಿರಬೇಕು</value>
  </data>
  <data name="ReadingShouldbegreaterthanPreviousReading" xml:space="preserve">
    <value>ಓದುವಿಕೆ ಹಿಂದಿನ ಓದುವ ಹೆಚ್ಚಿರಬೇಕು</value>
  </data>
  <data name="realizationreport" xml:space="preserve">
    <value>ಸಮರ್ಪಕ ವರದಿ</value>
  </data>
  <data name="ReallocateallDeallocatedQuanity" xml:space="preserve">
    <value>ಎಲ್ಲಾ ಡಿ ಹಂಚಿಕೆ quanity ಮರು ನಿಯೋಜಿಸಿ</value>
  </data>
  <data name="ReallocatedQty" xml:space="preserve">
    <value>ಉಪಯೊಗಕ್ಕೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="ReallocatedQtyshouldnotbegreaterthanBackorderQty" xml:space="preserve">
    <value>ಉಪಯೊಗಕ್ಕೆ ಪ್ರಮಾಣ backorder ಪ್ರಮಾಣ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಮಾಡಬಾರದು</value>
  </data>
  <data name="ReAllocatedQuantityAndAcceptedquantityisnotmatching" xml:space="preserve">
    <value>ಹಂಚಿಕೆ ಪ್ರಮಾಣ ಮತ್ತು ಸ್ವೀಕರಿಸಿದ ಪ್ರಮಾಣ ಹೊಂದಾಣಿಕೆ ಇಲ್ಲ ರೀ</value>
  </data>
  <data name="ReAllocatedQuantitycannotbegreaterthanAcceptedQty" xml:space="preserve">
    <value>ಹಂಚಿಕೆ ಪ್ರಮಾಣ ಮರು ಸ್ವೀಕರಿಸಿದ ಪ್ರಮಾಣ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ReAllocatedQuantitycannotbegreaterthanAcceptedquantity" xml:space="preserve">
    <value>ಹಂಚಿಕೆ ಪ್ರಮಾಣ ಮರು ಸ್ವೀಕರಿಸಿದ ಪ್ರಮಾಣ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ReAllocatedQuantitycannotbegreaterthanAllocatedQty" xml:space="preserve">
    <value>ಹಂಚಿಕೆ ಪ್ರಮಾಣ ಮರು ಹಂಚಿಕೆ ಪ್ರಮಾಣ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ReAllocatedQuantitycannotbegreaterthanfreestock" xml:space="preserve">
    <value>ಹಂಚಿಕೆ ಪ್ರಮಾಣ ಮರು ಉಚಿತ ಸ್ಟಾಕ್ ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ReAllocation" xml:space="preserve">
    <value>ಮರು ನಿಗದಿ</value>
  </data>
  <data name="ReallocationNum" xml:space="preserve">
    <value>ಪುನರ್ವಿಂಗಡಣೆ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="Reason" xml:space="preserve">
    <value>ಕಾರಣ</value>
  </data>
  <data name="reasonforinactive" xml:space="preserve">
    <value>ಸಕ್ರಿಯವಲ್ಲದ ಕಾರಣ</value>
  </data>
  <data name="ReasonforReturn" xml:space="preserve">
    <value>ರಿಟರ್ನ್ ಕಾರಣ</value>
  </data>
  <data name="ReasonsForLossOfSale" xml:space="preserve">
    <value>ಮಾರಾಟ ನಷ್ಟ ಕಾರಣಗಳು</value>
  </data>
  <data name="ReBuild" xml:space="preserve">
    <value>ಮರು ನಿರ್ಮಾಣ</value>
  </data>
  <data name="Receipt" xml:space="preserve">
    <value>ರಸೀತಿ</value>
  </data>
  <data name="ReceiptAlreadyCreated" xml:space="preserve">
    <value>ರಸೀತಿ ಈಗಾಗಲೇ ದಾಖಲಿಸಿದವರು</value>
  </data>
  <data name="ReceiptAlreadyCreatedForInvoiceAmount" xml:space="preserve">
    <value>ರಸೀತಿ ಈಗಾಗಲೇ ಸರಕುಪಟ್ಟಿ ಪ್ರಮಾಣವನ್ನು ದಾಖಲಿಸಿದವರು</value>
  </data>
  <data name="ReceiptDate" xml:space="preserve">
    <value>ರಸೀತಿ ದಿನಾಂಕ</value>
  </data>
  <data name="ReceiptisdoneforthisInvoiceNumber" xml:space="preserve">
    <value>ರಸೀತಿ ಈ ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ ಮಾಡಲಾಗುತ್ತದೆ</value>
  </data>
  <data name="ReceiptMode" xml:space="preserve">
    <value>ರಸೀತಿ ಮೋಡ್</value>
  </data>
  <data name="ReceiptNumber" xml:space="preserve">
    <value>ರಸೀತಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ReceiptRefDate" xml:space="preserve">
    <value>ರಸೀತಿ ಉಲ್ಲೇಖ ದಿನಾಂಕ</value>
  </data>
  <data name="ReceiptRefNumber" xml:space="preserve">
    <value>ರಸೀತಿ ಉಲ್ಲೇಖ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ReceiptSearch" xml:space="preserve">
    <value>ರಸೀತಿ ಹುಡುಕು</value>
  </data>
  <data name="ReceiptType" xml:space="preserve">
    <value>ರಸೀತಿ ಪ್ರಕಾರ</value>
  </data>
  <data name="ReceiveAmount" xml:space="preserve">
    <value>ಪ್ರಮಾಣ ಸ್ವೀಕರಿಸಿ</value>
  </data>
  <data name="ReceivedAmount" xml:space="preserve">
    <value>ಸ್ವೀಕರಿಸಲಾಗಿದೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="ReceivedAmountcannotbegreaterthanAmountReceived" xml:space="preserve">
    <value>ಸ್ವೀಕರಿಸಿ ಪಡೆದಿದ್ದೇನೆ ಪ್ರಮಾಣದ ಮತ್ತು ಕ್ರೆಡಿಟ್ ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ReceivedAmountCannotBeLessThanOldReceivedAmount" xml:space="preserve">
    <value>ಸ್ವೀಕರಿಸಲಾಗಿದೆ ಪ್ರಮಾಣ ಓಲ್ಡ್ ಪಡೆದಿದ್ದೇನೆ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ReceivedBy" xml:space="preserve">
    <value>ಪಡೆದ</value>
  </data>
  <data name="ReceivedCount" xml:space="preserve">
    <value>ಸ್ವೀಕರಿಸಲಾಗಿದೆ ನ್ಯಾಷನಲ್</value>
  </data>
  <data name="ReceivedDate" xml:space="preserve">
    <value>ಸ್ವೀಕರಿಸಲಾಗಿದೆ ದಿನಾಂಕ</value>
  </data>
  <data name="ReceivedQuantity" xml:space="preserve">
    <value>ಸ್ವೀಕರಿಸಲಾಗಿದೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="ReceivedQuantitycannotbegreaterthanRequestedQuantity" xml:space="preserve">
    <value>ಸ್ವೀಕರಿಸಲಾಗಿದೆ ಪ್ರಮಾಣ ವಿನಂತಿಸಿದ ಪ್ರಮಾಣ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="RecentActivityLinks" xml:space="preserve">
    <value>ಇತ್ತೀಚಿನ ಚಟುವಟಿಕೆ ಲಿಂಕ್ಸ್</value>
  </data>
  <data name="RecieptNumber" xml:space="preserve">
    <value>ರಸೀತಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="RecievedAmount" xml:space="preserve">
    <value>ಸ್ವೀಕರಿಸಲಾಗಿದೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="RecievedAmountCannotbeGreaterThanFinancedAmount" xml:space="preserve">
    <value>ಸ್ವೀಕರಿಸಿಲ್ಲ ಪ್ರಮಾಣದ ಆರ್ಥಿಕ ಪ್ರಮಾಣದ ಹೆಚ್ಚಾಗಿದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="RecievedAmountcannotbegreaterthanInvoiceAmount" xml:space="preserve">
    <value>ಪಡೆದ ಮೊತ್ತಕ್ಕಿಂತ - ಪ್ರಮಾಣದ ಸರಕುಪಟ್ಟಿ ಪ್ರಮಾಣವನ್ನು ಹೆಚ್ಚು ಸಾಧ್ಯವಿಲ್ಲ ಸ್ವೀಕರಿಸಿ</value>
  </data>
  <data name="RecievedTime" xml:space="preserve">
    <value>ಸ್ವೀಕರಿಸಲಾಗಿದೆ ಟೈಮ್</value>
  </data>
  <data name="RecievedTime1" xml:space="preserve">
    <value>ಸ್ವೀಕರಿಸಿಲ್ಲ ಟೈಮ್</value>
  </data>
  <data name="RecieviedCount" xml:space="preserve">
    <value>ಸ್ವೀಕರಿಸಲಾಗಿದೆ ನ್ಯಾಷನಲ್</value>
  </data>
  <data name="RecieviedCount1" xml:space="preserve">
    <value>Receivied ನ್ಯಾಷನಲ್</value>
  </data>
  <data name="ReClaim" xml:space="preserve">
    <value>ಮರು ಹಕ್ಕು</value>
  </data>
  <data name="RecordhasbeenalteredfortheselectedtransactionPleaserequery" xml:space="preserve">
    <value>ರೆಕಾರ್ಡ್ ಆಯ್ಕೆ ವ್ಯವಹಾರಕ್ಕೆ, ಬದಲಾಯಿಸಿತು.</value>
  </data>
  <data name="Recordsavedsuccessfully" xml:space="preserve">
    <value>ರೆಕಾರ್ಡ್ ಯಶಸ್ವಿಯಾಗಿ ಉಳಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="ReferecneDateCannotbelessthenCurrentDate" xml:space="preserve">
    <value>Referecne ದಿನಾಂಕ CurrentDate ನಂತರ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ReferenceDate" xml:space="preserve">
    <value>ರೆಫರೆನ್ಸ್ ದಿನಾಂಕ</value>
  </data>
  <data name="ReferenceDetail" xml:space="preserve">
    <value>ರೆಫರೆನ್ಸ್ ವಿವರ</value>
  </data>
  <data name="ReferenceMasters" xml:space="preserve">
    <value>ರೆಫರೆನ್ಸ್ ಮಾಸ್ಟರ್ಸ್</value>
  </data>
  <data name="ReferenceNumber" xml:space="preserve">
    <value>ಉಲ್ಲೇಖ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ReferenceTables" xml:space="preserve">
    <value>ರೆಫರೆನ್ಸ್ ಟೇಬಲ್ಸ್</value>
  </data>
  <data name="refresh" xml:space="preserve">
    <value>ರಿಫ್ರೆಶ್</value>
  </data>
  <data name="Region" xml:space="preserve">
    <value>ಪ್ರದೇಶ</value>
  </data>
  <data name="Register" xml:space="preserve">
    <value>ನೋಂದಾಯಿಸು</value>
  </data>
  <data name="Registered" xml:space="preserve">
    <value>ನೋಂದಾಯಿತ</value>
  </data>
  <data name="RegisteredMobile" xml:space="preserve">
    <value>ನೋಂದಾಯಿತ ಮೊಬೈಲ್</value>
  </data>
  <data name="Rejected" xml:space="preserve">
    <value>ತಿರಸ್ಕರಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="Rejected1" xml:space="preserve">
    <value>ತಿರಸ್ಕರಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="RelatedIssue" xml:space="preserve">
    <value>ಸಂಬಂಧಿತ ಸಂಚಿಕೆ</value>
  </data>
  <data name="relogin" xml:space="preserve">
    <value>ಮರು ಲಾಗಿನ್</value>
  </data>
  <data name="ReMan" xml:space="preserve">
    <value>ಮರು ಮ್ಯಾನ್</value>
  </data>
  <data name="ReManInternalInvoice" xml:space="preserve">
    <value>ಮರು ಮ್ಯಾನ್ ಆಂತರಿಕ ಸರಕುಪಟ್ಟಿ</value>
  </data>
  <data name="ReManInternalInvoiceNumber" xml:space="preserve">
    <value>ಪುನಃ ನೇಮಕಮಾಡು ಆಂತರಿಕ ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ReManInvoice" xml:space="preserve">
    <value>ಮರು ಮ್ಯಾನ್ ಸರಕುಪಟ್ಟಿ</value>
  </data>
  <data name="ReManInvoiceNumber" xml:space="preserve">
    <value>ಪುನಃ ನೇಮಕಮಾಡು ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ReManPartDescription" xml:space="preserve">
    <value>ಪುನಃ ನೇಮಕಮಾಡು ಭಾಗ ವಿವರಣೆ</value>
  </data>
  <data name="ReManPartDetails" xml:space="preserve">
    <value>ಮರು ಮ್ಯಾನ್ ಭಾಗ ವಿವರಗಳು</value>
  </data>
  <data name="ReManPartNumber" xml:space="preserve">
    <value>ಪುನಃ ನೇಮಕಮಾಡು ಭಾಗ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ReManpartsDetails" xml:space="preserve">
    <value>ಮರು ಮ್ಯಾನ್ ಭಾಗಗಳು ವಿವರ</value>
  </data>
  <data name="Remarks" xml:space="preserve">
    <value>ರಿಮಾರ್ಕ್ಸ್</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>ತೆಗೆದುಹಾಕುವುದು</value>
  </data>
  <data name="RemoveFilter" xml:space="preserve">
    <value>ಫಿಲ್ಟರ್ ತೆಗೆದುಹಾಕಿ</value>
  </data>
  <data name="Rename" xml:space="preserve">
    <value>ಹೊಸ ಹೆಸರಿಡು</value>
  </data>
  <data name="reopen" xml:space="preserve">
    <value>ಮತ್ತೆತೆರೆಯಿರಿ</value>
  </data>
  <data name="ReOrder" xml:space="preserve">
    <value>ಪುನಃ ಆದೇಶ</value>
  </data>
  <data name="ReOrderLevel" xml:space="preserve">
    <value>ಪುನಃ ಆದೇಶ ಮಟ್ಟ</value>
  </data>
  <data name="ReOrderLevelQty" xml:space="preserve">
    <value>ಪುನಃ ಆದೇಶ ಮಟ್ಟ ಪ್ರಮಾಣ</value>
  </data>
  <data name="ReOrderPoint" xml:space="preserve">
    <value>ಪುನಃ ಆದೇಶ ಪಾಯಿಂಟ್</value>
  </data>
  <data name="repairhours" xml:space="preserve">
    <value>ದುರಸ್ತಿ ಗಂಟೆಗಳ (8)</value>
  </data>
  <data name="RepairReading" xml:space="preserve">
    <value>ದುರಸ್ತಿ ಓದುವಿಕೆ</value>
  </data>
  <data name="Repairreadingcannotbelessthanfailurereading" xml:space="preserve">
    <value>ದುರಸ್ತಿ ಓದುವ ವೈಫಲ್ಯ ಓದುವ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Replace" xml:space="preserve">
    <value>ಬದಲಾಯಿಸಿ</value>
  </data>
  <data name="ReplacingCode" xml:space="preserve">
    <value>ಕೋಡ್ನಿಂದ ಸ್ಥಳಾಂತರಿಸಿದ</value>
  </data>
  <data name="ReportType" xml:space="preserve">
    <value>ವರದಿ ಪ್ರಕಾರ</value>
  </data>
  <data name="ReportWizard" xml:space="preserve">
    <value>ವರದಿ ವಿಝಾರ್ಡ್</value>
  </data>
  <data name="ReqDate" xml:space="preserve">
    <value>Req ಅನ್ನು ದಿನಾಂಕ</value>
  </data>
  <data name="ReqNumber" xml:space="preserve">
    <value>REQ</value>
  </data>
  <data name="RequestCore" xml:space="preserve">
    <value>ವಿನಂತಿ ಕೋರ್</value>
  </data>
  <data name="RequestCoreDate" xml:space="preserve">
    <value>ವಿನಂತಿ ಕೋರ್ ದಿನಾಂಕ</value>
  </data>
  <data name="RequestCoreIsSuccessfull" xml:space="preserve">
    <value>ವಿನಂತಿ ಕೋರ್ ಯಶಸ್ವಿಯಾಗಿ ಆಗಿದೆ</value>
  </data>
  <data name="RequestCoreNumber" xml:space="preserve">
    <value>ಕೋರ್ ಸಂಖ್ಯೆ ವಿನಂತಿ</value>
  </data>
  <data name="RequestCoreSearch" xml:space="preserve">
    <value>ವಿನಂತಿ ಕೋರ್ ಹುಡುಕು</value>
  </data>
  <data name="Requestdate" xml:space="preserve">
    <value>ವಿನಂತಿ ದಿನಾಂಕ</value>
  </data>
  <data name="RequestDescription" xml:space="preserve">
    <value>ವಿನಂತಿ ವಿವರಣೆ</value>
  </data>
  <data name="RequestedQuantity" xml:space="preserve">
    <value>ವಿನಂತಿಸಲಾಗಿದೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="RequestforApproval" xml:space="preserve">
    <value>ಅನುಮೋದನೆಗೆ ವಿನಂತಿ</value>
  </data>
  <data name="RequestForCore" xml:space="preserve">
    <value>ಕೋರ್ ವಿನಂತಿಯನ್ನು</value>
  </data>
  <data name="RequestForOEMApproval" xml:space="preserve">
    <value>OEM ಅನುಮೋದನೆ ವಿನಂತಿ</value>
  </data>
  <data name="RequestForParts" xml:space="preserve">
    <value>ಭಾಗಗಳು ವಿನಂತಿ</value>
  </data>
  <data name="Requestforpartsisdonecannotdelete" xml:space="preserve">
    <value>ಭಾಗಗಳು ವಿನಂತಿ ಮಾಡಲಾಗುತ್ತದೆ, ಅಳಿಸಲು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="RequestForPartsSuccessfull" xml:space="preserve">
    <value>ಭಾಗಗಳು ವಿನಂತಿ ಯಶಸ್ವಿಯಾಗಿ ಆಗಿದೆ</value>
  </data>
  <data name="RequestingBranch" xml:space="preserve">
    <value>ಮನವಿ ಶಾಖೆ</value>
  </data>
  <data name="RequestingBranchName" xml:space="preserve">
    <value>ಮನವಿ ಶಾಖೆ ಹೆಸರು</value>
  </data>
  <data name="RequestingBranchWareHouse" xml:space="preserve">
    <value>ಶಾಖೆ ವೇರ್ ಹೌಸ್ ಮನವಿ</value>
  </data>
  <data name="RequestingCompany" xml:space="preserve">
    <value>ಮನವಿ ಕಂಪನಿ</value>
  </data>
  <data name="RequestingCompany1" xml:space="preserve">
    <value>ಮನವಿ ಕಂಪನಿ</value>
  </data>
  <data name="RequestingWarehouse" xml:space="preserve">
    <value>ಮನವಿ ವೇರ್ಹೌಸ್</value>
  </data>
  <data name="RequestNumber" xml:space="preserve">
    <value>ವಿನಂತಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="RequestSuccessfull" xml:space="preserve">
    <value>ವಿನಂತಿ ಯಶಸ್ವಿಯಾಗಿ</value>
  </data>
  <data name="RequiredQuantity" xml:space="preserve">
    <value>ಅವಶ್ಯವಿರುವ ಪ್ರಮಾಣವನ್ನು</value>
  </data>
  <data name="RequiredQuantityCannotbegreaterthanQuantity" xml:space="preserve">
    <value>ಅವಶ್ಯವಿರುವ ಪ್ರಮಾಣವನ್ನು ಪ್ರಮಾಣ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="RequiredQuantityCannotbeZero" xml:space="preserve">
    <value>ಅವಶ್ಯವಿರುವ ಪ್ರಮಾಣವನ್ನು ಶೂನ್ಯ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="RequiredQuantityisGreaterthenFreeStock" xml:space="preserve">
    <value>ಅವಶ್ಯವಿರುವ ಪ್ರಮಾಣವನ್ನು ಉಚಿತ ಸ್ಟಾಕ್ ಹೆಚ್ಚಾಗಿದೆ</value>
  </data>
  <data name="RerunAllocationDetails" xml:space="preserve">
    <value>ಮರುಪ್ರದರ್ಶನ - ಹಂಚಿಕೆಯ ವಿವರಗಳು</value>
  </data>
  <data name="Reservation" xml:space="preserve">
    <value>ಮೀಸಲು</value>
  </data>
  <data name="ReservedQty" xml:space="preserve">
    <value>ರಿಸರ್ವ್ಡ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>ಮತ್ತೆ ಜೋಡಿಸು</value>
  </data>
  <data name="resolutiontime" xml:space="preserve">
    <value>ರೆಸಲ್ಯೂಶನ್ ಟೈಮ್</value>
  </data>
  <data name="ResolutionTimeOfClosedCases" xml:space="preserve">
    <value>ಮುಚ್ಚಿದ ಪ್ರಕರಣಗಳು ರೆಸಲ್ಯೂಶನ್ ಟೈಮ್</value>
  </data>
  <data name="Resolutiontimewithtimeslots" xml:space="preserve">
    <value>ಸಮಯ ಸ್ಥಾನಗಳ ರೆಸಲ್ಯೂಶನ್ ಟೈಮ್</value>
  </data>
  <data name="ResourceDetail" xml:space="preserve">
    <value>ಸಂಪನ್ಮೂಲ ವಿವರ</value>
  </data>
  <data name="ResourceTimeSheet" xml:space="preserve">
    <value>ಸಂಪನ್ಮೂಲ ಟೈಮ್ ಶೀಟ್</value>
  </data>
  <data name="ResourceType" xml:space="preserve">
    <value>ಸಂಪನ್ಮೂಲ ಪ್ರಕಾರ</value>
  </data>
  <data name="ResourceUtilization" xml:space="preserve">
    <value>ಸಂಪನ್ಮೂಲ ಬಳಕೆ</value>
  </data>
  <data name="ResourceUtilizationReport" xml:space="preserve">
    <value>ಸಂಪನ್ಮೂಲ ಬಳಕೆ ರಿಪೋರ್ಟ್</value>
  </data>
  <data name="responsetime" xml:space="preserve">
    <value>ರೆಸ್ಪಾನ್ಸ್ ಟೈಮ್</value>
  </data>
  <data name="Returnable" xml:space="preserve">
    <value>ವಾಪಸಾತಿ</value>
  </data>
  <data name="ReturnAmount" xml:space="preserve">
    <value>ರಿಟರ್ನ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="ReturnAmountCannotbeZero" xml:space="preserve">
    <value>ರಿಟರ್ನ್ ಪ್ರಮಾಣದ ಶೂನ್ಯ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="returnbypartysearch" xml:space="preserve">
    <value>ಆದಾಯಕ್ಕೆ ಪಕ್ಷದ ಹುಡುಕು</value>
  </data>
  <data name="ReturnDate" xml:space="preserve">
    <value>ರಿಟರ್ನ್ ದಿನಾಂಕ</value>
  </data>
  <data name="ReturnedBy" xml:space="preserve">
    <value>ಮರಳುವುದು</value>
  </data>
  <data name="ReturnedByParty" xml:space="preserve">
    <value>ಪಕ್ಷದ ಮೂಲಕ ಮರಳಿದರು</value>
  </data>
  <data name="ReturnedDate" xml:space="preserve">
    <value>ಮರಳಿದರು ದಿನಾಂಕ</value>
  </data>
  <data name="ReturnedDateCannotbegreaterThanCurrentDate" xml:space="preserve">
    <value>ಮರಳಿದರು ದಿನಾಂಕ ಪ್ರಸ್ತುತ ದಿನಾಂಕ ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ReturnedDateCannotbelessThanPromisedReturnDate" xml:space="preserve">
    <value>ಮರಳಿದರು ದಿನಾಂಕ ಭರವಸೆ ಹಿಂದಿರುಗುವ ದಿನಾಂಕವನ್ನು ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ReturnedQuantity" xml:space="preserve">
    <value>ಮರಳಿದರು ಪ್ರಮಾಣ</value>
  </data>
  <data name="ReturnedQuantitySIR" xml:space="preserve">
    <value>ಮರಳಿದರು ಪ್ರಮಾಣ</value>
  </data>
  <data name="ReturnedTo" xml:space="preserve">
    <value>ಮರಳಿದರು</value>
  </data>
  <data name="ReturningBranch" xml:space="preserve">
    <value>ಹಿಂದಿರುಗುತ್ತಿದ್ದೀರಾ ಶಾಖೆ</value>
  </data>
  <data name="ReturningCompany" xml:space="preserve">
    <value>ಹಿಂದಿರುಗುತ್ತಿದ್ದೀರಾ ಕಂಪನಿ</value>
  </data>
  <data name="ReturnInternalInvoice" xml:space="preserve">
    <value>ಆಂತರಿಕ ಸರಕುಪಟ್ಟಿ ಹಿಂತಿರುಗಿ</value>
  </data>
  <data name="ReturnInvoice" xml:space="preserve">
    <value>ರಿಟರ್ನ್ ಸರಕುಪಟ್ಟಿ</value>
  </data>
  <data name="ReturnNumber" xml:space="preserve">
    <value>ರಿಟರ್ನ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ReturnParts" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಹಿಂತಿರುಗಿ</value>
  </data>
  <data name="ReturnQuantity" xml:space="preserve">
    <value>ರಿಟರ್ನ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="returnquantitycannotbegreaterthaninvoicedquantity" xml:space="preserve">
    <value>ರಿಟರ್ನ್ ಪ್ರಮಾಣ ಸರಕುಪಟ್ಟಿ ಪ್ರಮಾಣ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ReturnQuantityCannotBeGreaterThanIssuedQuantity" xml:space="preserve">
    <value>ರಿಟರ್ನ್ ಪ್ರಮಾಣ ಬಿಡುಗಡೆ ಪ್ರಮಾಣದ ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ReturnQuantityCannotBeGreaterThanQuantity" xml:space="preserve">
    <value>ರಿಟರ್ನ್ ಪ್ರಮಾಣ ಪ್ರಮಾಣ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ReturnQuantitySIR" xml:space="preserve">
    <value>ರಿಟರ್ನ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="Returns" xml:space="preserve">
    <value>ರಿಟರ್ನ್ಸ್</value>
  </data>
  <data name="ReturnTax" xml:space="preserve">
    <value>ಆದಾಯಘೋಷಣೆ</value>
  </data>
  <data name="ReturnTo" xml:space="preserve">
    <value>ಹಿಂತಿರುಗಿ</value>
  </data>
  <data name="ReturnType" xml:space="preserve">
    <value>ಪ್ರಕಾರ ಹಿಂತಿರುಗಿ</value>
  </data>
  <data name="Reusable" xml:space="preserve">
    <value>ಪುನಃ ಬಳಸಬಹುದಾದ</value>
  </data>
  <data name="RevenuecannotbeBlankorzero" xml:space="preserve">
    <value>ಕಂದಾಯ ಖಾಲಿ ಇರಕೂಡದು</value>
  </data>
  <data name="RevenueGenerated" xml:space="preserve">
    <value>ಆದಾಯ</value>
  </data>
  <data name="revenuemorethen" xml:space="preserve">
    <value>ಕಂದಾಯ ಹೆಚ್ಚು ನಂತರ</value>
  </data>
  <data name="ReworkReportFrom" xml:space="preserve">
    <value>ವರದಿಯ  ಜಾಬ್ ಕಾರ್ಡ್  ಮರು</value>
  </data>
  <data name="Role" xml:space="preserve">
    <value>ರೋಲ್</value>
  </data>
  <data name="RoleDefinition" xml:space="preserve">
    <value>ಪಾತ್ರ ವ್ಯಾಖ್ಯಾನ</value>
  </data>
  <data name="RoleName" xml:space="preserve">
    <value>ಪಾತ್ರ ಹೆಸರು</value>
  </data>
  <data name="RoleNameCannotbeblank" xml:space="preserve">
    <value>ಪಾತ್ರ ಹೆಸರು ಖಾಲಿ ಇರುವಂತಿಲ್ಲ</value>
  </data>
  <data name="RoleObject" xml:space="preserve">
    <value>ಪಾತ್ರ ವಸ್ತು</value>
  </data>
  <data name="Roles" xml:space="preserve">
    <value>ಪಾತ್ರಗಳು</value>
  </data>
  <data name="RootCause" xml:space="preserve">
    <value>ಮೂಲ ಕಾರಣ</value>
  </data>
  <data name="RootCauseAnalysis" xml:space="preserve">
    <value>ಮೂಲ ಕಾರಣ ವಿಶ್ಲೇಷಣೆ</value>
  </data>
  <data name="RoundOff" xml:space="preserve">
    <value>ಆಫ್ ಸುತ್ತ</value>
  </data>
  <data name="Sales" xml:space="preserve">
    <value>ಮಾರಾಟದ</value>
  </data>
  <data name="SalesAmount" xml:space="preserve">
    <value>ಮಾರಾಟದ ಪ್ರಮಾಣ</value>
  </data>
  <data name="SalesCreditLimit" xml:space="preserve">
    <value>ಮಾರಾಟದ ಕ್ರೆಡಿಟ್ ಮಿತಿಯನ್ನು</value>
  </data>
  <data name="SalesDCissuesatcostprice" xml:space="preserve">
    <value>ವೆಚ್ಚ ಬೆಲೆಗೆ ಮಾರಾಟದ ಡಿಸಿ ತೊಂದರೆಗಳು</value>
  </data>
  <data name="SalesEnquiry" xml:space="preserve">
    <value>ಮಾರಾಟದ ವಿಚಾರಣೆ</value>
  </data>
  <data name="SalesHistory" xml:space="preserve">
    <value>ಮಾರಾಟದ ಇತಿಹಾಸ</value>
  </data>
  <data name="SalesInvoice" xml:space="preserve">
    <value>ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ</value>
  </data>
  <data name="SalesInvoiceDailyReport" xml:space="preserve">
    <value>ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ ಡೈಲಿ ವರದಿ</value>
  </data>
  <data name="SalesInvoiceDate" xml:space="preserve">
    <value>ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ ದಿನಾಂಕ</value>
  </data>
  <data name="SalesInvoiceDetailReport" xml:space="preserve">
    <value>ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ ವಿವರ ವರದಿ</value>
  </data>
  <data name="SalesInvoiceMonthlyReport" xml:space="preserve">
    <value>ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ ಮಾಸಿಕ ವರದಿ</value>
  </data>
  <data name="SalesInvoiceNumber" xml:space="preserve">
    <value>ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="SalesinvoiceReport" xml:space="preserve">
    <value>ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ ವರದಿ</value>
  </data>
  <data name="SalesInvoiceReturn" xml:space="preserve">
    <value>ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ ರಿಟರ್ನ್</value>
  </data>
  <data name="SalesInvoiceReturnDailyReport" xml:space="preserve">
    <value>ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ ರಿಟರ್ನ್ ಡೈಲಿ ವರದಿ</value>
  </data>
  <data name="SalesInvoiceReturnDate" xml:space="preserve">
    <value>ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ ಹಿಂದಿರುಗುವ ದಿನಾಂಕವನ್ನು</value>
  </data>
  <data name="SalesInvoiceReturnMonthlyReport" xml:space="preserve">
    <value>ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ ರಿಟರ್ನ್ ಮಾಸಿಕ ವರದಿ</value>
  </data>
  <data name="SalesInvoiceReturnNumber" xml:space="preserve">
    <value>ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ ರಿಟರ್ನ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="SalesNonSales" xml:space="preserve">
    <value>ಮಾರಾಟದ / ​​ನಾನ್ ಮಾರಾಟದ</value>
  </data>
  <data name="SalesOrder" xml:space="preserve">
    <value>ಮಾರಾಟದ ಆರ್ಡರ್</value>
  </data>
  <data name="SalesOrderAmmendment" xml:space="preserve">
    <value>ಮಾರಾಟದ ಆರ್ಡರ್ ammendment</value>
  </data>
  <data name="SalesOrderArchive" xml:space="preserve">
    <value>ಮಾರಾಟದ ಆರ್ಡರ್ ಆರ್ಕೈವ್</value>
  </data>
  <data name="SalesOrderCanNotBeCreatedBecauseOfTheLocalSalesDoYouWantToSavePurchaseOrder" xml:space="preserve">
    <value>ಮಾರಾಟದ ಸಲುವಾಗಿ ಏಕೆಂದರೆ ಸ್ಥಳೀಯ ಮಾರಾಟ ರಚಿಸಲಾಗುವುದಿಲ್ಲ.</value>
  </data>
  <data name="SalesOrderCount" xml:space="preserve">
    <value>ಮಾರಾಟದ ಆರ್ಡರ್ ನ್ಯಾಷನಲ್</value>
  </data>
  <data name="SalesOrderDailyreport" xml:space="preserve">
    <value>ಮಾರಾಟದ ಆರ್ಡರ್ ಡೈಲಿ ವರದಿ</value>
  </data>
  <data name="SalesOrderDate" xml:space="preserve">
    <value>ಮಾರಾಟದ ಆದೇಶ ದಿನಾಂಕ</value>
  </data>
  <data name="SalesOrderDetailReport" xml:space="preserve">
    <value>ಮಾರಾಟದ ಆರ್ಡರ್ ವಿವರ ವರದಿ</value>
  </data>
  <data name="SalesOrderMonthlyReport" xml:space="preserve">
    <value>ಮಾರಾಟದ ಆರ್ಡರ್ ಮಾಸಿಕ ವರದಿ</value>
  </data>
  <data name="SalesOrderNumber" xml:space="preserve">
    <value>ಮಾರಾಟದ ಆದೇಶ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="SalesOrderPendingForApproval" xml:space="preserve">
    <value>ಅನುಮೋದನೆಗೆ ಮಾರಾಟದ ಆರ್ಡರ್ ಬಾಕಿ</value>
  </data>
  <data name="SalesOrderValue" xml:space="preserve">
    <value>ಮಾರಾಟದ ಆರ್ಡರ್ ಮೌಲ್ಯ</value>
  </data>
  <data name="SalesOrderVersionDetails" xml:space="preserve">
    <value>ಮಾರಾಟದ ಆರ್ಡರ್ ಆವೃತ್ತಿ ವಿವರಗಳು</value>
  </data>
  <data name="SalesOutStandingAmount" xml:space="preserve">
    <value>ಮಾರಾಟದ ಬಾಕಿ ಮೊತ್ತದ</value>
  </data>
  <data name="SalesPendingEnquiries" xml:space="preserve">
    <value>ಮಾರಾಟದ ಬಾಕಿ ಸಂಬಂಧ ತನಿಖೆಗಾಗಿ</value>
  </data>
  <data name="SalesQuantity" xml:space="preserve">
    <value>ಮಾರಾಟದ ಪ್ರಮಾಣ</value>
  </data>
  <data name="SalesQuotation" xml:space="preserve">
    <value>ಮಾರಾಟದ ನುಡಿಮುತ್ತುಗಳು</value>
  </data>
  <data name="SalesQuotationConversionRatioDetails" xml:space="preserve">
    <value>ಮಾರಾಟದ ಉದ್ಧರಣ ಪರಿವರ್ತನೆ ಅನುಪಾತವು ವಿವರಗಳು</value>
  </data>
  <data name="SalesQuotationDetailReport" xml:space="preserve">
    <value>ಮಾರಾಟದ ಉದ್ಧರಣ ವಿವರ ವರದಿ</value>
  </data>
  <data name="SalesQuotationVersionDetails" xml:space="preserve">
    <value>ಮಾರಾಟದ ಉದ್ಧರಣ ಆವೃತ್ತಿ ವಿವರಗಳು</value>
  </data>
  <data name="SalesRepresentative" xml:space="preserve">
    <value>ಮಾರಾಟ ಪ್ರತಿನಿಧಿ</value>
  </data>
  <data name="SalesSuccessRatio" xml:space="preserve">
    <value>ಮಾರಾಟದ ಯಶಸ್ಸು ಅನುಪಾತ</value>
  </data>
  <data name="SalesType" xml:space="preserve">
    <value>ಮಾರಾಟದ ಪ್ರಕಾರ</value>
  </data>
  <data name="Salvage" xml:space="preserve">
    <value>ಕಚ್ಚಾವಸ್ತು</value>
  </data>
  <data name="SalvageNumber" xml:space="preserve">
    <value>ರಕ್ಷಣೆ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="SalvagePartNumber" xml:space="preserve">
    <value>ಭಾಗ ಸಂಖ್ಯೆ ಕಚ್ಚಾವಸ್ತು</value>
  </data>
  <data name="SalvagePartPrefix" xml:space="preserve">
    <value>ಭಾಗ ಪೂರ್ವಪ್ರತ್ಯಯ ಕಚ್ಚಾವಸ್ತು</value>
  </data>
  <data name="SalvageParts" xml:space="preserve">
    <value>ರಕ್ಷಣೆ ಭಾಗಗಳು</value>
  </data>
  <data name="SalvagePartsN" xml:space="preserve">
    <value>SalvageParts</value>
  </data>
  <data name="Saturday" xml:space="preserve">
    <value>ಶನಿವಾರ</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>ಉಳಿಸಿ</value>
  </data>
  <data name="SaveAction" xml:space="preserve">
    <value>ಆಕ್ಷನ್ ಉಳಿಸಿ</value>
  </data>
  <data name="SavedSuccessfull" xml:space="preserve">
    <value>ಯಶಸ್ವಿಯಾಗಿ ಉಳಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="SavedSuccessfully" xml:space="preserve">
    <value>ಯಶಸ್ವಿಯಾಗಿ ಉಳಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="SaveFormatandGenerateReport" xml:space="preserve">
    <value>ಫಾರ್ಮ್ಯಾಟ್ ಉಳಿಸಿ ಮತ್ತು ರಿಪೋರ್ಟ್ ರಚಿಸಿ</value>
  </data>
  <data name="savefreestockdetails" xml:space="preserve">
    <value>ಭಾಗಗಳು ಉಚಿತ ಸ್ಟಾಕ್ ವಿವರಗಳು ಉಳಿಸಿ</value>
  </data>
  <data name="saveheader" xml:space="preserve">
    <value>ಹೆಡರ್ ಉಳಿಸಿ</value>
  </data>
  <data name="savepartprice" xml:space="preserve">
    <value>ಭಾಗ ಬೆಲೆ ವಿವರಗಳು ಉಳಿಸಿ</value>
  </data>
  <data name="SavePrefixSuffix" xml:space="preserve">
    <value>ಪೂರ್ವಪ್ರತ್ಯಯ ಪ್ರತ್ಯಯ ಉಳಿಸಿ</value>
  </data>
  <data name="saveproductdetail" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ವಿವರ ಉಳಿಸಿ</value>
  </data>
  <data name="saveproducttype" xml:space="preserve">
    <value>ಉಳಿಸಿ ಭಾಗ ಉತ್ಪನ್ನ ಪ್ರಕಾರ</value>
  </data>
  <data name="SaveRole" xml:space="preserve">
    <value>ಪಾತ್ರ ಉಳಿಸಿ</value>
  </data>
  <data name="SaveStep" xml:space="preserve">
    <value>ಹಂತ ಉಳಿಸಿ</value>
  </data>
  <data name="SaveStepLink" xml:space="preserve">
    <value>ಹಂತ ಲಿಂಕ್ ಉಳಿಸಿ</value>
  </data>
  <data name="SaveSuccesfull" xml:space="preserve">
    <value>ಯಶಸ್ಸಿಗೆ ಉಳಿಸಿ</value>
  </data>
  <data name="SaveSuccessfull" xml:space="preserve">
    <value>ಯಶಸ್ವಿಯಾಗಿ ಉಳಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="savetaxstructure" xml:space="preserve">
    <value>ತೆರಿಗೆ ರಚನೆ ಉಳಿಸಿ</value>
  </data>
  <data name="SaveUser" xml:space="preserve">
    <value>ಬಳಕೆದಾರ ಉಳಿಸಿ</value>
  </data>
  <data name="ScheduleType" xml:space="preserve">
    <value>ವೇಳಾಪಟ್ಟಿ ಪ್ರಕಾರ</value>
  </data>
  <data name="Scrap" xml:space="preserve">
    <value>ಚಿಂದಿ</value>
  </data>
  <data name="ScrapDate" xml:space="preserve">
    <value>ದಿನಾಂಕ</value>
  </data>
  <data name="ScrapDetails" xml:space="preserve">
    <value>ಸ್ಕ್ರ್ಯಾಪ್ ವಿವರಗಳು</value>
  </data>
  <data name="ScrapNumber" xml:space="preserve">
    <value>ಸ್ಕ್ರ್ಯಾಪ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ScrapParts" xml:space="preserve">
    <value>ಸ್ಕ್ರ್ಯಾಪ್ ಭಾಗಗಳು</value>
  </data>
  <data name="ScrapPartsN" xml:space="preserve">
    <value>ScrapParts</value>
  </data>
  <data name="ScrappedBy" xml:space="preserve">
    <value>ನಿಷ್ಕ್ರಿಯವಾಗಲ್ಪಟ್ಟವು</value>
  </data>
  <data name="ScrapQuantity" xml:space="preserve">
    <value>ಸ್ಕ್ರ್ಯಾಪ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="SecondaryDealer" xml:space="preserve">
    <value>ಸೆಕೆಂಡರಿ ಡೀಲರ್</value>
  </data>
  <data name="SecondarySegment" xml:space="preserve">
    <value>ಸೆಕೆಂಡರಿ ಸೆಗ್ಮೆಂಟ್</value>
  </data>
  <data name="SecondarySegmentdescription" xml:space="preserve">
    <value>ಸೆಕೆಂಡರಿ ಸೆಗ್ಮೆಂಟ್ ವಿವರಣೆ</value>
  </data>
  <data name="secondarysegmentenglish" xml:space="preserve">
    <value>ಸೆಕೆಂಡರಿ ಸೆಗ್ಮೆಂಟ್ ಇಂಗ್ಲೀಷ್</value>
  </data>
  <data name="secondarysegmentlocale" xml:space="preserve">
    <value>ಸೆಕೆಂಡರಿ ಸೆಗ್ಮೆಂಟ್ ಲೊಕೇಲ್</value>
  </data>
  <data name="SegmentDetail" xml:space="preserve">
    <value>ಸೆಗ್ಮೆಂಟ್ ವಿವರ</value>
  </data>
  <data name="select" xml:space="preserve">
    <value>ಆರಿಸು</value>
  </data>
  <data name="SelectAll" xml:space="preserve">
    <value>ಎಲ್ಲಾ ಆಯ್ಕೆಮಾಡಿ</value>
  </data>
  <data name="SelectatleastoneWorkingDay" xml:space="preserve">
    <value>ಕನಿಷ್ಠ ಒಂದು ದಿನ ಕೆಲಸ ಮಾಡಿ</value>
  </data>
  <data name="selectbrand" xml:space="preserve">
    <value>ಆಯ್ಕೆ ಬ್ರಾಂಡ್</value>
  </data>
  <data name="SelectColumn" xml:space="preserve">
    <value>ಅಂಕಣ ಆಯ್ಕೆ</value>
  </data>
  <data name="selectcompany" xml:space="preserve">
    <value>ಆಯ್ಕೆ ಕಂಪನಿ</value>
  </data>
  <data name="SelectCondition" xml:space="preserve">
    <value>ಕಂಡಿಶನ್ ಆಯ್ಕೆ</value>
  </data>
  <data name="SelectDDl" xml:space="preserve">
    <value>--------- ಆಯ್ಕೆ ---------</value>
  </data>
  <data name="SelectDDl1" xml:space="preserve">
    <value>---- ಆಯ್ಕೆ ----</value>
  </data>
  <data name="SelectedFileisnotanExcelFile" xml:space="preserve">
    <value>ಆಯ್ದ ಕಡತ ಒಂದು ಎಕ್ಸೆಲ್ ಕಡತವನ್ನು ಅಲ್ಲ</value>
  </data>
  <data name="SelectedInvoiceNumberisAlreadyReturned" xml:space="preserve">
    <value>ಆಂತರಿಕ ಸರಕುಪಟ್ಟಿ ಈಗಾಗಲೇ ಮರಳಿ</value>
  </data>
  <data name="SelectedPartsDoesnothaveanywarehouseassociated" xml:space="preserve">
    <value>ಆಯ್ದ ಭಾಗಗಳು ಸಂಬಂಧಿಸಿದ ಯಾವುದೇ ಗೋದಾಮಿನ ಹೊಂದಿಲ್ಲ</value>
  </data>
  <data name="SelectedYearOfThisHolidayIsNot" xml:space="preserve">
    <value>ಈ ರಜಾ ಆಯ್ದ ವರ್ಷದ ಅಲ್ಲ</value>
  </data>
  <data name="SelectGeneralShift" xml:space="preserve">
    <value>ಜನರಲ್ ಶಿಫ್ಟ್ ಆಯ್ಕೆ</value>
  </data>
  <data name="SelectionCriteria" xml:space="preserve">
    <value>ಆಯ್ಕೆ ಮಾನದಂಡ</value>
  </data>
  <data name="SelectIssueArea" xml:space="preserve">
    <value>IssueArea ಆಯ್ಕೆ</value>
  </data>
  <data name="SelectItemType" xml:space="preserve">
    <value>ಐಟಂ ಕೌಟುಂಬಿಕತೆ ಆಯ್ಕೆ</value>
  </data>
  <data name="SelectkitPartNumber" xml:space="preserve">
    <value>ಆಯ್ಕೆ ಕಿಟ್ ಭಾಗ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="SelectModel" xml:space="preserve">
    <value>ಆಯ್ಕೆ ಮಾದರಿ</value>
  </data>
  <data name="SelectModelandRequest" xml:space="preserve">
    <value>ಆಯ್ಕೆ ಮಾದರಿ ಮತ್ತು ವಿನಂತಿ</value>
  </data>
  <data name="SelectOperator" xml:space="preserve">
    <value>ಆಪರೇಟರ್ ಆಯ್ಕೆ</value>
  </data>
  <data name="SelectPartNumber" xml:space="preserve">
    <value>ಆಯ್ಕೆ ಭಾಗ</value>
  </data>
  <data name="SelectPartyType" xml:space="preserve">
    <value>ಪಕ್ಷದ ಆಯ್ಕೆಮಾಡಿ</value>
  </data>
  <data name="selectproducttype" xml:space="preserve">
    <value>ಆಯ್ಕೆ ಉತ್ಪನ್ನ ಪ್ರಕಾರ</value>
  </data>
  <data name="SelectRecordstoDelete" xml:space="preserve">
    <value>ಅಳಿಸಿ ರೆಕಾರ್ಡ್ಸ್ ಆಯ್ಕೆ</value>
  </data>
  <data name="SelectReport" xml:space="preserve">
    <value>ವರದಿ ಮಾಡಿ</value>
  </data>
  <data name="SelectReportFromPreviouslyStoredFormats" xml:space="preserve">
    <value>ಹಿಂದೆ ಸಂಗ್ರಹಿಸಿದ ಶೈಲಿಗಳಿಗೆ ವರದಿ ಮಾಡಿ</value>
  </data>
  <data name="SelectReportType" xml:space="preserve">
    <value>ಆಯ್ಕೆ ವರದಿ ಪ್ರಕಾರ</value>
  </data>
  <data name="SelectReqNumber" xml:space="preserve">
    <value>ಆಯ್ಕೆ ವಿನಂತಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="SelectServiceRequest" xml:space="preserve">
    <value>ಸೇವೆ ವಿನಂತಿ ಮಾಡಿ</value>
  </data>
  <data name="selectshift" xml:space="preserve">
    <value>ಆಯ್ಕೆ ಶಿಫ್ಟ್</value>
  </data>
  <data name="SelectTableName" xml:space="preserve">
    <value>ಟೇಬಲ್ ಹೆಸರು ಆಯ್ಕೆ</value>
  </data>
  <data name="SelectWarrantyClaimNumber" xml:space="preserve">
    <value>ಆಯ್ಕೆ ಖಾತರಿ ಹಕ್ಕು ಸಂಖ್ಯೆ</value>
  </data>
  <data name="SelectYear" xml:space="preserve">
    <value>ವರ್ಷದ ಆಯ್ಕೆ</value>
  </data>
  <data name="Seller" xml:space="preserve">
    <value>ಮಾರಾಟಗಾರ</value>
  </data>
  <data name="SellingPrice" xml:space="preserve">
    <value>ಮಾರಾಟ ಬೆಲೆ</value>
  </data>
  <data name="SellingPriceDetails" xml:space="preserve">
    <value>ಬೆಲೆ ವಿವರಗಳು ಮಾರಾಟ</value>
  </data>
  <data name="September" xml:space="preserve">
    <value>ಸೆಪ್ಟೆ</value>
  </data>
  <data name="sequenceno" xml:space="preserve">
    <value>ಸೀಕ್ವೆನ್ಸ್ ಯಾವುದೇ</value>
  </data>
  <data name="Serial" xml:space="preserve">
    <value>ಧಾರಾವಾಹಿ</value>
  </data>
  <data name="SeriallNumberSearch" xml:space="preserve">
    <value>ಕ್ರಮ ಸಂಖ್ಯೆ ಹುಡುಕು</value>
  </data>
  <data name="serialnumber" xml:space="preserve">
    <value>ಅನುಕ್ರಮ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="SerialNumberAllocation" xml:space="preserve">
    <value>ಕ್ರಮ ಸಂಖ್ಯೆ ಹಂಚಿಕೆ</value>
  </data>
  <data name="SerialNumberAlreadyExistsForTheSelectedPartNumber" xml:space="preserve">
    <value>ಕ್ರಮ ಸಂಖ್ಯೆ ಈಗಾಗಲೇ ಆಯ್ಕೆ ಭಾಗ ಸಂಖ್ಯೆ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="serialnumberalreadyexistsforthismodel" xml:space="preserve">
    <value>ಕ್ರಮಸಂಖ್ಯೆ ಈಗಾಗಲೇ ಈ ಮಾದರಿಗೆ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="SerialNumberDetails" xml:space="preserve">
    <value>ಕ್ರಮ ಸಂಖ್ಯೆ ವಿವರಗಳು</value>
  </data>
  <data name="SerialNumberFieldSearch" xml:space="preserve">
    <value>ಕ್ರಮ ಸಂಖ್ಯೆ ಫೀಲ್ಡ್ ಹುಡುಕು</value>
  </data>
  <data name="SerialNumberisalreadyavailable" xml:space="preserve">
    <value>ಕ್ರಮಸಂಖ್ಯೆ ನೀವು ಮುಂದುವರಿಸಲು ಬಯಸುತ್ತೀರಾ, ಈಗಾಗಲೇ ಲಭ್ಯವಿದೆ?</value>
  </data>
  <data name="SerialNumberisalreadyscrapped" xml:space="preserve">
    <value>ಕ್ರಮಸಂಖ್ಯೆ ಈಗಾಗಲೇ ಕೈಬಿಟ್ಟಿತು ಇದೆ</value>
  </data>
  <data name="SerialNumberisalreadysold" xml:space="preserve">
    <value>ಕ್ರಮಸಂಖ್ಯೆ ಈಗಾಗಲೇ ಮಾರಾಟ</value>
  </data>
  <data name="SerialNumberIsInActive" xml:space="preserve">
    <value>ಕ್ರಮ ಸಂಖ್ಯೆ ನಿಷ್ಕ್ರಿಯ</value>
  </data>
  <data name="SerialNumbernotfoundfortheselectedmodel" xml:space="preserve">
    <value>ಕ್ರಮಸಂಖ್ಯೆ ಆಯ್ಕೆ ಮಾದರಿಗೆ ಕಂಡುಬಂದಿಲ್ಲ</value>
  </data>
  <data name="SerialNumberr" xml:space="preserve">
    <value>ಅನುಕ್ರಮ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="SerialStatus" xml:space="preserve">
    <value>ಸೀರಿಯಲ್ ಸ್ಥಿತಿ</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>ಸೇವೆ</value>
  </data>
  <data name="ServiceAgreementDetails" xml:space="preserve">
    <value>ಸೇವೆ ಒಪ್ಪಂದ ವಿವರ</value>
  </data>
  <data name="ServiceAgreementNumber" xml:space="preserve">
    <value>ಸೇವೆ ಒಪ್ಪಂದ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ServiceAgreementServiceHistory" xml:space="preserve">
    <value>ಸೇವೆ ಒಪ್ಪಂದ ಸೇವೆ ಇತಿಹಾಸ</value>
  </data>
  <data name="ServiceAgreementType" xml:space="preserve">
    <value>ಸೇವೆ ಒಪ್ಪಂದ ಪ್ರಕಾರ</value>
  </data>
  <data name="ServiceAgreementValue" xml:space="preserve">
    <value>ಸೇವೆ ಒಪ್ಪಂದ ಮೌಲ್ಯ</value>
  </data>
  <data name="ServiceAmount" xml:space="preserve">
    <value>ಸೇವೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="ServiceCharge" xml:space="preserve">
    <value>ಸೇವಾ ಶುಲ್ಕ</value>
  </data>
  <data name="ServiceChargecannotbezero" xml:space="preserve">
    <value>ಸೇವೆ ಚಾರ್ಜ್ ಶೂನ್ಯ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ServiceChargeCode" xml:space="preserve">
    <value>ಸೇವೆ ಚಾರ್ಜ್ ಕೋಡ್</value>
  </data>
  <data name="servicechargecodenotfound" xml:space="preserve">
    <value>ಸೇವೆ ಚಾರ್ಜ್ ಕೋಡ್ ಕಂಡುಬಂದಿಲ್ಲ</value>
  </data>
  <data name="Servicechargecodes" xml:space="preserve">
    <value>ಸೇವಾ ಶುಲ್ಕ ಕೋಡ್</value>
  </data>
  <data name="ServiceChargeDetail" xml:space="preserve">
    <value>ಸೇವೆ ಚಾರ್ಜ್ ವಿವರ</value>
  </data>
  <data name="ServiceChargeFieldSearch" xml:space="preserve">
    <value>ಸೇವೆ ಚಾರ್ಜ್ ಫೀಲ್ಡ್ ಹುಡುಕು</value>
  </data>
  <data name="ServiceChargeIsMandatory" xml:space="preserve">
    <value>ಸೇವೆ ಚಾರ್ಜ್ ವಿವರ ಕಡ್ಡಾಯ</value>
  </data>
  <data name="ServiceChargeRs" xml:space="preserve">
    <value>ServiceCharge (ರೂ)</value>
  </data>
  <data name="servicecharges" xml:space="preserve">
    <value>ಸೇವೆ ಚಾರ್ಜಸ್</value>
  </data>
  <data name="servicechargesdetail" xml:space="preserve">
    <value>ಸೇವೆ ವಿವರ ಚಾರ್ಜಸ್</value>
  </data>
  <data name="ServiceChargesDetails" xml:space="preserve">
    <value>ಸೇವೆ ವಿವರಗಳು ಚಾರ್ಜಸ್</value>
  </data>
  <data name="servicechargesenglish" xml:space="preserve">
    <value>ಸೇವೆ ಚಾರ್ಜಸ್</value>
  </data>
  <data name="servicechargesenglish1" xml:space="preserve">
    <value>ಸೇವೆ ಇಂಗ್ಲೀಷ್ ಚಾರ್ಜಸ್</value>
  </data>
  <data name="servicechargesheader" xml:space="preserve">
    <value>ಸೇವೆ ಚಾರ್ಜಸ್</value>
  </data>
  <data name="servicechargesheader1" xml:space="preserve">
    <value>ಸೇವೆ ಶಿರೋಲೇಖ ಚಾರ್ಜಸ್</value>
  </data>
  <data name="servicechargeslocale" xml:space="preserve">
    <value>ಸೇವೆ ಚಾರ್ಜಸ್</value>
  </data>
  <data name="servicechargeslocale1" xml:space="preserve">
    <value>ಸೇವೆ ಚಾರ್ಜಸ್ ಲೊಕೇಲ್</value>
  </data>
  <data name="servicechargesmaster" xml:space="preserve">
    <value>ಸೇವೆ ಚಾರ್ಜಸ್ ಮಾಸ್ಟರ್</value>
  </data>
  <data name="ServiceChargesTotalAmount" xml:space="preserve">
    <value>ಸೇವೆ ಒಟ್ಟು ಪ್ರಮಾಣ ಚಾರ್ಜಸ್</value>
  </data>
  <data name="ServiceCode" xml:space="preserve">
    <value>ಸೇವೆ ಕೋಡ್</value>
  </data>
  <data name="servicecodealreadyexists" xml:space="preserve">
    <value>ಸೇವೆ ಕೋಡ್ ಈಗಾಗಲೇ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="ServiceCompany" xml:space="preserve">
    <value>ಸೇವೆ ಕಂಪನಿ</value>
  </data>
  <data name="ServiceCreditLimit" xml:space="preserve">
    <value>ಸೇವೆ ಕ್ರೆಡಿಟ್ ಮಿತಿಯನ್ನು</value>
  </data>
  <data name="servicedate" xml:space="preserve">
    <value>ಸೇವೆ ದಿನಾಂಕ</value>
  </data>
  <data name="servicedatecannotbelessthancurrentdate" xml:space="preserve">
    <value>ಸೇವೆ ದಿನಾಂಕ ಪ್ರಸ್ತುತ ದಿನಾಂಕ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ServiceDetail" xml:space="preserve">
    <value>ಸೇವೆ ವಿವರ</value>
  </data>
  <data name="serviceduedays" xml:space="preserve">
    <value>ಸೇವೆ ಕಾರಣ ಡೇಸ್</value>
  </data>
  <data name="Serviceduedayscannotbezero" xml:space="preserve">
    <value>ಸೇವೆ ಕಾರಣ ದಿನಗಳ ಶೂನ್ಯವಾಗಿರುವುದಿಲ್ಲ</value>
  </data>
  <data name="serviceduehours" xml:space="preserve">
    <value>ಸೇವೆ ಕಾರಣ ಅವರ್ಸ್</value>
  </data>
  <data name="Serviceduehourscannotbezero" xml:space="preserve">
    <value>ಸೇವೆ ಕಾರಣ ಗಂಟೆಗಳ ಶೂನ್ಯ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ServiceFrequency" xml:space="preserve">
    <value>ಸೇವೆ ಫ್ರೀಕ್ವೆನ್ಸಿ</value>
  </data>
  <data name="Servicefrequencycannotbezero" xml:space="preserve">
    <value>ಸೇವೆ ಆವರ್ತನ ಶೂನ್ಯ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="servicehistory" xml:space="preserve">
    <value>ಸೇವೆ ಇತಿಹಾಸ</value>
  </data>
  <data name="ServiceInvoice" xml:space="preserve">
    <value>ಸೇವೆ ಸರಕುಪಟ್ಟಿ</value>
  </data>
  <data name="ServiceInvoiceNumber" xml:space="preserve">
    <value>ಸೇವೆ ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ServiceInvoiceReturn" xml:space="preserve">
    <value>ಸೇವೆ ಸರಕುಪಟ್ಟಿ ರಿಟರ್ನ್</value>
  </data>
  <data name="ServiceLevelAgreement" xml:space="preserve">
    <value>ಸೇವೆ ಮಟ್ಟದ ಒಪ್ಪಂದ</value>
  </data>
  <data name="ServicePriority" xml:space="preserve">
    <value>ಸೇವೆ ಆದ್ಯತಾ</value>
  </data>
  <data name="ServiceQuotation" xml:space="preserve">
    <value>ಸೇವೆ ನುಡಿಮುತ್ತುಗಳು</value>
  </data>
  <data name="ServiceQuotationArchived" xml:space="preserve">
    <value>ಸೇವೆ ಉದ್ಧರಣ ಆರ್ಕೈವ್</value>
  </data>
  <data name="ServiceQuotationNumber" xml:space="preserve">
    <value>ಸೇವೆ ಉದ್ಧರಣ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ServiceRequest" xml:space="preserve">
    <value>ಸೇವೆ ವಿನಂತಿ</value>
  </data>
  <data name="ServiceRequestAbandoned" xml:space="preserve">
    <value>ಸೇವೆ ವಿನಂತಿ ಪರಿತ್ಯಕ್ತ</value>
  </data>
  <data name="ServiceRequestCount" xml:space="preserve">
    <value>ಸೇವೆ ವಿನಂತಿ ನ್ಯಾಷನಲ್</value>
  </data>
  <data name="ServiceRequestDate" xml:space="preserve">
    <value>ಸೇವೆ ವಿನಂತಿ ದಿನಾಂಕ</value>
  </data>
  <data name="ServiceRequestDistributionChart" xml:space="preserve">
    <value>ಸೇವೆ ವಿನಂತಿ ವಿತರಣೆ ಚಾರ್ಟ್</value>
  </data>
  <data name="ServicerequestFieldSearch" xml:space="preserve">
    <value>ಸೇವೆ ವಿನಂತಿಯನ್ನು ಫೀಲ್ಡ್ ಹುಡುಕು</value>
  </data>
  <data name="ServiceRequestIsClosed" xml:space="preserve">
    <value>ಸೇವೆ ವಿನಂತಿಯನ್ನು ಮುಚ್ಚಲಾಗಿದೆ</value>
  </data>
  <data name="ServiceRequestNumber" xml:space="preserve">
    <value>ಸೇವೆ ವಿನಂತಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ServiceRequestNumbernotfound" xml:space="preserve">
    <value>ಸೇವೆ ವಿನಂತಿಯನ್ನು ಸಂಖ್ಯೆ ಕಂಡುಬಂದಿಲ್ಲ</value>
  </data>
  <data name="ServiceRequestSummary" xml:space="preserve">
    <value>ಸೇವೆ ವಿನಂತಿ ಸಾರಾಂಶ</value>
  </data>
  <data name="serviceschdule" xml:space="preserve">
    <value>ಸೇವೆ ವೇಳಾಪಟ್ಟಿ</value>
  </data>
  <data name="ServiceSchedule" xml:space="preserve">
    <value>ಸೇವೆ ವೇಳಾಪಟ್ಟಿ</value>
  </data>
  <data name="ServiceType" xml:space="preserve">
    <value>ಸೇವೆ ಪ್ರಕಾರ</value>
  </data>
  <data name="ServiceTypeName" xml:space="preserve">
    <value>ಸೇವೆ ಕೌಟುಂಬಿಕತೆ ಹೆಸರು</value>
  </data>
  <data name="ServiceType_Active" xml:space="preserve">
    <value>ಸಕ್ರಿಯವಾಗಿದೆ?</value>
  </data>
  <data name="SettlementNumber" xml:space="preserve">
    <value>ಸೆಟ್ಲ್ಮೆಂಟ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="SettlementType" xml:space="preserve">
    <value>ಸೆಟ್ಲ್ಮೆಂಟ್ ಪ್ರಕಾರ</value>
  </data>
  <data name="Sevtotwelvemonths" xml:space="preserve">
    <value>7-12 ತಿಂಗಳುಗಳು</value>
  </data>
  <data name="Shift" xml:space="preserve">
    <value>ವರ್ಗಾಯಿಸು</value>
  </data>
  <data name="ShippedQuantity" xml:space="preserve">
    <value>ಸಾಗಿಸಲಾಯಿತು ಪ್ರಮಾಣ</value>
  </data>
  <data name="Shipping" xml:space="preserve">
    <value>ಹಡಗು ರವಾನೆ</value>
  </data>
  <data name="ShippingAddress" xml:space="preserve">
    <value>ಶಿಪ್ಪಿಂಗ್ ವಿಳಾಸ</value>
  </data>
  <data name="ShippingDetails" xml:space="preserve">
    <value>ಶಿಪ್ಪಿಂಗ್ ವಿವರಗಳು</value>
  </data>
  <data name="Shippingdetailsaremandatory" xml:space="preserve">
    <value>ಶಿಪ್ಪಿಂಗ್ ವಿವರಗಳು ಕಡ್ಡಾಯವಾಗಿ</value>
  </data>
  <data name="ShippingRemarks" xml:space="preserve">
    <value>ಶಿಪ್ಪಿಂಗ್ ರಿಮಾರ್ಕ್ಸ್</value>
  </data>
  <data name="ShippingTerms" xml:space="preserve">
    <value>ಶಿಪ್ಪಿಂಗ್ ನಿಯಮಗಳು</value>
  </data>
  <data name="ShortAmount" xml:space="preserve">
    <value>ಸಣ್ಣ ಪ್ರಮಾಣ</value>
  </data>
  <data name="ShortName" xml:space="preserve">
    <value>ಸಣ್ಣ ಹೆಸರು</value>
  </data>
  <data name="ShortQty" xml:space="preserve">
    <value>ಸಣ್ಣ ಪ್ರಮಾಣ</value>
  </data>
  <data name="ShortQuantity" xml:space="preserve">
    <value>ಸಣ್ಣ ಪ್ರಮಾಣ</value>
  </data>
  <data name="siteaddress" xml:space="preserve">
    <value>ಸೈಟ್ ವಿಳಾಸ</value>
  </data>
  <data name="siteaddressdetails" xml:space="preserve">
    <value>ಸೈಟ್ ವಿಳಾಸ ವಿವರ</value>
  </data>
  <data name="SixMonthQty" xml:space="preserve">
    <value>6 ತಿಂಗಳ ಪ್ರಮಾಣ</value>
  </data>
  <data name="SixMonthValue" xml:space="preserve">
    <value>6 ತಿಂಗಳ ಮೌಲ್ಯ</value>
  </data>
  <data name="SixteenToTwentyFourHours" xml:space="preserve">
    <value>16 ರಿಂದ 24 ಗಂಟೆಗಳ</value>
  </data>
  <data name="skill" xml:space="preserve">
    <value>ನೈಪುಣ್ಯ</value>
  </data>
  <data name="Skillisalreadyassociatedwiththeemployee" xml:space="preserve">
    <value>ನೈಪುಣ್ಯ ಈಗಾಗಲೇ ನೌಕರ ಸಂಬಂಧಿಸಿದೆ</value>
  </data>
  <data name="skilllevel" xml:space="preserve">
    <value>ಕೌಶಲ್ಯತೆಯ ಮಟ್ಟ</value>
  </data>
  <data name="skilllevelshouldbebetween1to10" xml:space="preserve">
    <value>ಕೌಶಲ್ಯ ಮಟ್ಟ 1 ಗೆ 10 ಆಗಿರಬೇಕು</value>
  </data>
  <data name="Skillset" xml:space="preserve">
    <value>ಕೌಶಲ್ಯ ಸೆಟ್</value>
  </data>
  <data name="SLAExceeded" xml:space="preserve">
    <value>ಶ್ರೀಲಂಕಾ ಮಿತಿಮೀರಿದೆ</value>
  </data>
  <data name="SLAHours" xml:space="preserve">
    <value>ಶ್ರೀಲಂಕಾ ಅವರ್ಸ್</value>
  </data>
  <data name="SLAReport" xml:space="preserve">
    <value>ಶ್ರೀಲಂಕಾ ವರದಿ</value>
  </data>
  <data name="SLAWithin" xml:space="preserve">
    <value>ಶ್ರೀಲಂಕಾ ಒಳಗೆ</value>
  </data>
  <data name="slno" xml:space="preserve">
    <value>ಯಾವುದೇ ಕ್ರ</value>
  </data>
  <data name="Slow" xml:space="preserve">
    <value>ನಿಧಾನದ</value>
  </data>
  <data name="SlowCannotbegreaterthanorEqualtoMedium" xml:space="preserve">
    <value>ಸ್ಲೋ ಹೆಚ್ಚು ಅಥವಾ ಸಾಧಾರಣ ಸಮಾನವಾಗಿರಬೇಕು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="SlowCannotbeSmallerthanorEqualtoUnMoved" xml:space="preserve">
    <value>ಸ್ಲೋ ಚಿಕ್ಕದಾದ ಅಥವಾ ಅಚಲ ಗೆ ಸಮಾನವಾಗಿರಬೇಕು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="sms" xml:space="preserve">
    <value>ಎಸ್ಎಂಎಸ್</value>
  </data>
  <data name="SMSToAddressee" xml:space="preserve">
    <value>ವಿಳಾಸದಾರ ಸಂಚಿಕೆ</value>
  </data>
  <data name="SMSToCustomer" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ಸಂಚಿಕೆ</value>
  </data>
  <data name="SMTPMailBox" xml:space="preserve">
    <value>SMTP ಮೈಲ್ ಬಾಕ್ಸ್</value>
  </data>
  <data name="SMTPPassword" xml:space="preserve">
    <value>ನಿಮ್ಮ SMTP ಪಾಸ್ವರ್ಡ್</value>
  </data>
  <data name="SMTPServerName" xml:space="preserve">
    <value>SMTP ಸರ್ವರ್ ಹೆಸರು</value>
  </data>
  <data name="SMTPUserName" xml:space="preserve">
    <value>SMTP ಬಳಕೆದಾರ ಹೆಸರು</value>
  </data>
  <data name="Sn" xml:space="preserve">
    <value>ಅನುಕ್ರಮ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="Sold" xml:space="preserve">
    <value>ಮಾರಾಟ</value>
  </data>
  <data name="SortOrder" xml:space="preserve">
    <value>ವಿಂಗಡಣಾ ಕ್ರಮ</value>
  </data>
  <data name="SortOrdercannotbeblank" xml:space="preserve">
    <value>ವಿಂಗಡಿಸಿ ಸಲುವಾಗಿ ಖಾಲಿ ಇರಕೂಡದು</value>
  </data>
  <data name="sortordercannotbeblankforMenu" xml:space="preserve">
    <value>ವಿಂಗಡಿಸಿ ಸಲುವಾಗಿ ಮೆನು ಖಾಲಿ ಇರಕೂಡದು</value>
  </data>
  <data name="SortOrderCannotbegreaterthan" xml:space="preserve">
    <value>ವಿಂಗಡಿಸಿ ಸಲುವಾಗಿ ಹೆಚ್ಚಿನ 255 ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Source" xml:space="preserve">
    <value>ಮೂಲ</value>
  </data>
  <data name="SourceColumns" xml:space="preserve">
    <value>ಮೂಲ ಕಾಲಮ್ಗಳು</value>
  </data>
  <data name="Specialization" xml:space="preserve">
    <value>ವಿಶೇಷ</value>
  </data>
  <data name="SpecializationMaster" xml:space="preserve">
    <value>ತಜ್ಞತೆ ಮಾಸ್ಟರ್</value>
  </data>
  <data name="splitquantitycannotbezero" xml:space="preserve">
    <value>ಒಡೆದ ಪ್ರಮಾಣ ಶೂನ್ಯ ಅಥವಾ ಖಾಲಿ ಇರಕೂಡದು</value>
  </data>
  <data name="SqlException" xml:space="preserve">
    <value>ಡೇಟಾಬೇಸ್ ದೋಷ ಸಂಭವಿಸಿದೆ</value>
  </data>
  <data name="SRBasedonCalltype" xml:space="preserve">
    <value>ಸೇವೆ ವಿನಂತಿ ಕಾಲ್ ಆಧರಿತವಾಗಿ</value>
  </data>
  <data name="SRCount" xml:space="preserve">
    <value>ಸೇವೆ ವಿನಂತಿಗಳು ಕೌಂಟ್</value>
  </data>
  <data name="SRNotFound" xml:space="preserve">
    <value>ಸೇವೆ ವಿನಂತಿಯನ್ನು ಕಂಡುಬಂದಿಲ್ಲ</value>
  </data>
  <data name="ss" xml:space="preserve">
    <value>ಎಸ್ಎಸ್</value>
  </data>
  <data name="StandardHours" xml:space="preserve">
    <value>ಸ್ಟ್ಯಾಂಡರ್ಡ್ ಅವರ್ಸ್</value>
  </data>
  <data name="standardtime" xml:space="preserve">
    <value>ಸ್ಟ್ಯಾಂಡರ್ಡ್ ಟೈಮ್</value>
  </data>
  <data name="StartDate" xml:space="preserve">
    <value>ಪ್ರಾರಂಭ ದಿನಾಂಕ</value>
  </data>
  <data name="StartdatecannotbegreaterthanEnddate" xml:space="preserve">
    <value>ಪ್ರಾರಂಭ ದಿನಾಂಕ ಅಂತಿಮ ದಿನಾಂಕವನ್ನು ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="startnumber" xml:space="preserve">
    <value>ಸಂಖ್ಯೆ ಪ್ರಾರಂಭಿಸಿ</value>
  </data>
  <data name="startnumbercannotbenullorzero" xml:space="preserve">
    <value>ಪ್ರಾರಂಭಿಸಿ ಸಂಖ್ಯೆ ಶೂನ್ಯ ಅಥವಾ ಶೂನ್ಯ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="State" xml:space="preserve">
    <value>ರಾಜ್ಯ</value>
  </data>
  <data name="stateenglish" xml:space="preserve">
    <value>ರಾಜ್ಯ ಇಂಗ್ಲೀಷ್</value>
  </data>
  <data name="statelocale" xml:space="preserve">
    <value>ರಾಜ್ಯ ಲೊಕೇಲ್</value>
  </data>
  <data name="status" xml:space="preserve">
    <value>ಅಂತಸ್ತು</value>
  </data>
  <data name="StatusHistory" xml:space="preserve">
    <value>ಸ್ಥಿತಿ ಇತಿಹಾಸ</value>
  </data>
  <data name="StdPackingQty" xml:space="preserve">
    <value>ಸ್ಟ್ಯಾಂಡರ್ಡ್ ಪ್ಯಾಕಿಂಗ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="StepLink" xml:space="preserve">
    <value>ಹಂತ ಲಿಂಕ್</value>
  </data>
  <data name="StepName" xml:space="preserve">
    <value>ಹಂತ ಹೆಸರು</value>
  </data>
  <data name="Steps" xml:space="preserve">
    <value>ಕ್ರಮಗಳು</value>
  </data>
  <data name="StepStatus" xml:space="preserve">
    <value>ಹಂತ ಸ್ಥಿತಿ</value>
  </data>
  <data name="StepType" xml:space="preserve">
    <value>ಹಂತ ಪ್ರಕಾರ</value>
  </data>
  <data name="Stock" xml:space="preserve">
    <value>ಸ್ಟಾಕ್</value>
  </data>
  <data name="StockAdjustment" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಹೊಂದಾಣಿಕೆ</value>
  </data>
  <data name="StockAdjustmentNo" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಹೊಂದಾಣಿಕೆ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="StockBlock" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ನಿರ್ಬಂಧಿಸುವುದು</value>
  </data>
  <data name="StockBlocking" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಅನಿರ್ಬಂಧಿಸಲಾಗುತ್ತಿದೆ ನಿರ್ಬಂಧಿಸುವುದು</value>
  </data>
  <data name="StockBlockingDailyReport" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಡೈಲಿ ವರದಿ ನಿರ್ಬಂಧಿಸುವುದು</value>
  </data>
  <data name="StockBlockingMonthlyReport" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಮಾಸಿಕ ವರದಿ ನಿರ್ಬಂಧಿಸುವುದು</value>
  </data>
  <data name="StockBlockingNumber" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ನಿರ್ಬಂಧಿಸುವುದು ಸಂಖ್ಯೆ</value>
  </data>
  <data name="StockBlockingNumberAlreadyExists" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ನಿರ್ಬಂಧಿಸುವುದು ಈಗಾಗಲೇ ಈ ಸೇವೆ ವಿನಂತಿ ಸಂಖ್ಯೆ ರಚಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="StockBlockingNumberAlreadyExistsforQuotationNumber" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ತಡೆಯುವ ಸಂಖ್ಯೆ ಈಗಾಗಲೇ ಉದ್ಧರಣಾ ಸಂಖ್ಯೆ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="StockBlockingNumberAlreadyExistsforServiceRequestNumber" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ತಡೆಯುವ ಸಂಖ್ಯೆ ಈಗಾಗಲೇ ಸೇವೆ ವಿನಂತಿಯನ್ನು ಸಂಖ್ಯೆ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="StockCheckConfirmation" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಚೆಕ್ ದೃಢೀಕರಣ</value>
  </data>
  <data name="StockCheckConfirmationDate" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಚೆಕ್ ದೃಢೀಕರಣ ದಿನಾಂಕ</value>
  </data>
  <data name="StockCheckConfirmationNo" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಚೆಕ್ ದೃಢೀಕರಣ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="StockCheckConfirmationnumber" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಚೆಕ್ ದೃಢೀಕರಣ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="StockCheckConfirmationnumberL" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಚೆಕ್ ದೃಢೀಕರಣ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="StockCheckConfirmationSearch" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಚೆಕ್ ದೃಢೀಕರಣ ಹುಡುಕು</value>
  </data>
  <data name="StockCheckRequest" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಚೆಕ್ ವಿನಂತಿ</value>
  </data>
  <data name="StockCheckRequestDate" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಚೆಕ್ ವಿನಂತಿ ದಿನಾಂಕ</value>
  </data>
  <data name="StockCheckRequestNum" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಚೆಕ್ ವಿನಂತಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="StockCheckRequestNumber" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಚೆಕ್ ವಿನಂತಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="StockDetailsNotAvailableforthepart" xml:space="preserve">
    <value>ಭಾಗ ಸ್ಟಾಕ್ ವಿವರಗಳು ಲಭ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="StockGreaterThanZero" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಗ್ರೇಟರ್ ದ್ಯಾನ್ ಜೀರೊ</value>
  </data>
  <data name="StockLedger" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಲೆಡ್ಜರ್</value>
  </data>
  <data name="StockLocation" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಸ್ಥಳ</value>
  </data>
  <data name="StockNotFound" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಬಿನ್ ಸ್ಥಳ ದೊರೆಯಲಿಲ್ಲ</value>
  </data>
  <data name="StockOnOrderValueExVAT" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಆರ್ಡರ್ ಮೌಲ್ಯ ಮಾಜಿ ವ್ಯಾಟ್</value>
  </data>
  <data name="StockReceiptGRN" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ರಸೀತಿ ಜಿ ಆರ್ ಎನ್</value>
  </data>
  <data name="StockReceiptGRNDailyReport" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ರಸೀತಿ ಜಿ ಆರ್ ಎನ್ ಡೈಲಿ ವರದಿ</value>
  </data>
  <data name="StockReceiptGRNMonthlyReport" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ರಸೀತಿ ಜಿ ಆರ್ ಎನ್ ಮಾಸಿಕ ವರದಿ</value>
  </data>
  <data name="StockReceiptGRNNumber" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ರಸೀತಿ ಜಿ ಆರ್ ಎನ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="StockTransferNote" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಟ್ರಾನ್ಸ್ಫರ್ ಗಮನಿಸಿ</value>
  </data>
  <data name="StockTransferNoteDailyReport" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಟ್ರಾನ್ಸ್ಫರ್ ಗಮನಿಸಿ ಡೈಲಿ ವರದಿ</value>
  </data>
  <data name="StockTransferNoteDate" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಟ್ರಾನ್ಸ್ಫರ್ ಗಮನಿಸಿ ದಿನಾಂಕ</value>
  </data>
  <data name="StockTransferNoteMonthlyReport" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಟ್ರಾನ್ಸ್ಫರ್ ಗಮನಿಸಿ ಮಾಸಿಕ ವರದಿ</value>
  </data>
  <data name="StockTransferNoteNumber" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಟ್ರಾನ್ಸ್ಫರ್ ಗಮನಿಸಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="StockTransferReceipt" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ವರ್ಗಾವಣೆ / ರಸೀತಿ</value>
  </data>
  <data name="StockTransferRequest" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ವರ್ಗಾವಣೆ ಮನವಿ</value>
  </data>
  <data name="StockTransferRequestNumber" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ವರ್ಗಾವಣೆ ಮನವಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="StockUnBlocking" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಅನ್ ನಿರ್ಬಂಧಿಸುವುದು</value>
  </data>
  <data name="StockUnBlockingDailyReport" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಅನಿರ್ಬಂಧಿಸಲಾಗುತ್ತಿದೆ ಡೈಲಿ ವರದಿ</value>
  </data>
  <data name="StockUnBlockingMonthlyReport" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಅನಿರ್ಬಂಧಿಸಲಾಗುತ್ತಿದೆ ಮಾಸಿಕ ವರದಿ</value>
  </data>
  <data name="StockUnBlockingNumber" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಅನಿರ್ಬಂಧಿಸಲಾಗುತ್ತಿದೆ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="StockUpdatedByOtherUser" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಇತರ ಬಳಕೆದಾರರ ಮೂಲಕ ಅಪ್ಡೇಟ್ಗೊಳಿಸಲಾಗಿದೆ ಇದೆ.</value>
  </data>
  <data name="StockUsedInKits" xml:space="preserve">
    <value>ಕಿಟ್ಗಳು ಬಳಸುವ ಸ್ಟಾಕ್</value>
  </data>
  <data name="StockValue" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಮೌಲ್ಯ</value>
  </data>
  <data name="StockZeroTransactedParts" xml:space="preserve">
    <value>ಸ್ಟಾಕ್ ಶೂನ್ಯ ವ್ಯವಹಾರವಾದ ಭಾಗಗಳು</value>
  </data>
  <data name="String1" xml:space="preserve">
    <value>ಉತ್ಪಾದನೆ ದಿನಾಂಕ</value>
  </data>
  <data name="String3" xml:space="preserve">
    <value>ಬಂಧನ</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>ಯಶಸ್ಸು</value>
  </data>
  <data name="SuccessfullyReAllocated" xml:space="preserve">
    <value>ಯಶಸ್ವಿಯಾಗಿ ಮರು ಹಂಚಿಕೆ</value>
  </data>
  <data name="SuccessRatio" xml:space="preserve">
    <value>ಯಶಸ್ಸು ಅನುಪಾತ</value>
  </data>
  <data name="suffix" xml:space="preserve">
    <value>ಪ್ರತ್ಯಯ</value>
  </data>
  <data name="SuffixalreadySelected" xml:space="preserve">
    <value>ಪ್ರತ್ಯಯ ಈಗಾಗಲೇ ಆಯ್ಕೆ</value>
  </data>
  <data name="Summary" xml:space="preserve">
    <value>ಸಂಗ್ರಹವಾದ</value>
  </data>
  <data name="sumofsplittedquantitycannotbegreaterthanpickedquantity" xml:space="preserve">
    <value>Splitted ಪ್ರಮಾಣ ಮೊತ್ತ ಆಯ್ಕೆ ಪ್ರಮಾಣ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Sunday" xml:space="preserve">
    <value>ಭಾನುವಾರ</value>
  </data>
  <data name="Sundry" xml:space="preserve">
    <value>ಬಗೆಬಗೆಯ</value>
  </data>
  <data name="SundryAmount" xml:space="preserve">
    <value>ಬಗೆಬಗೆಯ ಪ್ರಮಾಣ</value>
  </data>
  <data name="SundryAmountcannotexceedCampaignSundryAmount" xml:space="preserve">
    <value>ಬಗೆಬಗೆಯ ಪ್ರಮಾಣದ ಪ್ರಚಾರ ಬಗೆಬಗೆಯ ಪ್ರಮಾಣವನ್ನು ಮೀರಬಾರದು</value>
  </data>
  <data name="SundryApproved" xml:space="preserve">
    <value>ಬಗೆಬಗೆಯ ಅನುಮೋದನೆ</value>
  </data>
  <data name="SundryCost" xml:space="preserve">
    <value>ಬಗೆಬಗೆಯ ವೆಚ್ಚ</value>
  </data>
  <data name="SundryDetail" xml:space="preserve">
    <value>ಬಗೆಬಗೆಯ ವಿವರ</value>
  </data>
  <data name="SundryDetails" xml:space="preserve">
    <value>ಬಗೆಬಗೆಯ ವಿವರಗಳು</value>
  </data>
  <data name="SundryJobDescription" xml:space="preserve">
    <value>ಬಗೆಬಗೆಯ ಜಾಬ್ ವಿವರಣೆ</value>
  </data>
  <data name="SundryTotalAmount" xml:space="preserve">
    <value>ಬಗೆಬಗೆಯ ಒಟ್ಟು ಪ್ರಮಾಣ</value>
  </data>
  <data name="SuperseedingPartsDetail" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ವಿವರ Superseeding</value>
  </data>
  <data name="Supersession" xml:space="preserve">
    <value>Supersession</value>
  </data>
  <data name="SupersessionDetail" xml:space="preserve">
    <value>Supersession ವಿವರ</value>
  </data>
  <data name="SupersessionDetails" xml:space="preserve">
    <value>Supersession ವಿವರಗಳು</value>
  </data>
  <data name="supersessionexist" xml:space="preserve">
    <value>Supersession ಅಸ್ತಿತ್ವದಲ್ಲಿವೆ</value>
  </data>
  <data name="SuperSessionExistsforaboveparts" xml:space="preserve">
    <value>ಸೂಪರ್ ಅಧಿವೇಶನ ಮೇಲೆ ಭಾಗಗಳು ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ.</value>
  </data>
  <data name="SupersessionType" xml:space="preserve">
    <value>Supersession ಪ್ರಕಾರ</value>
  </data>
  <data name="SupersessionTypenotinProperformat" xml:space="preserve">
    <value>Supersession ರೀತಿಯ ಸರಿಯಾದ ರೂಪದಲ್ಲಿ</value>
  </data>
  <data name="Supplier" xml:space="preserve">
    <value>ಸರಬರಾಜುದಾರ</value>
  </data>
  <data name="SupplierDetails" xml:space="preserve">
    <value>ಪೂರೈಕೆದಾರರ ವಿವರಗಳು</value>
  </data>
  <data name="SupplierFieldSearch" xml:space="preserve">
    <value>ಸರಬರಾಜುದಾರ ಫೀಲ್ಡ್ ಹುಡುಕು</value>
  </data>
  <data name="SupplierInvoiceDate" xml:space="preserve">
    <value>ಸರಬರಾಜುದಾರ ಸರಕುಪಟ್ಟಿ ದಿನಾಂಕ</value>
  </data>
  <data name="SupplierInvoiceNumber" xml:space="preserve">
    <value>ಸರಬರಾಜು ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="SupplierisLockedDoyouwanttocontinue" xml:space="preserve">
    <value>ಸರಬರಾಜುದಾರ ಲಾಕ್, ನೀವು ಮುಂದುವರಿಸಲು ಬಯಸುತ್ತೀರಾ</value>
  </data>
  <data name="supplierisnotactive" xml:space="preserve">
    <value>ಸರಬರಾಜುದಾರ ಸಕ್ರಿಯವಾಗಿಲ್ಲ</value>
  </data>
  <data name="Supplierisnotassociatedtoparts" xml:space="preserve">
    <value>ಸರಬರಾಜುದಾರ ಭಾಗಗಳಿಗೆ ಸಂಬಂಧ ಇಲ್ಲ</value>
  </data>
  <data name="SupplierName" xml:space="preserve">
    <value>ಸರಬರಾಜುದಾರ ಹೆಸರು</value>
  </data>
  <data name="SupplierNotAssociatedWithThisModel" xml:space="preserve">
    <value>ಸರಬರಾಜುದಾರ ಈ ಮಾದರಿ ಸಂಬಂಧವಿಲ್ಲ</value>
  </data>
  <data name="Suppliernotfound" xml:space="preserve">
    <value>ಸರಬರಾಜುದಾರ ಕಂಡುಬಂದಿಲ್ಲ</value>
  </data>
  <data name="supplierorderclass" xml:space="preserve">
    <value>ಸರಬರಾಜುದಾರ ಆರ್ಡರ್ ವರ್ಗ</value>
  </data>
  <data name="SupplierPartNumber" xml:space="preserve">
    <value>ಸರಬರಾಜು ಭಾಗ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="SupplierPrice" xml:space="preserve">
    <value>ಸರಬರಾಜುದಾರ ಬೆಲೆ</value>
  </data>
  <data name="SupplierPriceDetails" xml:space="preserve">
    <value>ಸರಬರಾಜುದಾರ ಬೆಲೆ ವಿವರಗಳು</value>
  </data>
  <data name="SupplierReferenceDate" xml:space="preserve">
    <value>ಸರಬರಾಜುದಾರ ರೆಫರೆನ್ಸ್ ದಿನಾಂಕ</value>
  </data>
  <data name="SupplierReferenceNumber" xml:space="preserve">
    <value>ಸರಬರಾಜು ಉಲ್ಲೇಖ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="Supplierspecific" xml:space="preserve">
    <value>ಸರಬರಾಜುದಾರ ನಿರ್ದಿಷ್ಟ</value>
  </data>
  <data name="SupplierWiseExcessorShortPartsReceived" xml:space="preserve">
    <value>ಸ್ವೀಕರಿಸಲಾಗಿದೆ ಸರಬರಾಜುದಾರ ವೈಸ್ ಆಧಿಕ್ಯ ಅಥವಾ ಸಣ್ಣ ಭಾಗಗಳು</value>
  </data>
  <data name="SupplierWisePurchaseReport" xml:space="preserve">
    <value>ಪೂರೈಕೆದಾರ ವೈಸ್ ಖರೀದಿ ವರದಿ</value>
  </data>
  <data name="SupplyingBranch" xml:space="preserve">
    <value>ಸರಬರಾಜು ಶಾಖೆ</value>
  </data>
  <data name="SupplyingBranchDetails" xml:space="preserve">
    <value>ಶಾಖೆ ವಿವರಗಳು ಪೂರೈಕೆ</value>
  </data>
  <data name="SupplyingBranchWareHouse" xml:space="preserve">
    <value>ಶಾಖೆ ವೇರ್ ಮನೆ ಸರಬರಾಜು</value>
  </data>
  <data name="SupplyingWarehouse" xml:space="preserve">
    <value>ವೇರ್ಹೌಸ್ ಪೂರೈಕೆ</value>
  </data>
  <data name="Support" xml:space="preserve">
    <value>ಬೆಂಬಲ</value>
  </data>
  <data name="SystemGeneratedMachineCost" xml:space="preserve">
    <value>ಸಿಸ್ಟಮ್ ರಚಿಸಲಾಗಿದೆ ಯಂತ್ರ ವೆಚ್ಚ</value>
  </data>
  <data name="SystemStock" xml:space="preserve">
    <value>ವ್ಯವಸ್ಥೆಯನ್ನು ಸ್ಟಾಕ್</value>
  </data>
  <data name="TableName" xml:space="preserve">
    <value>ಟೇಬಲ್ ಹೆಸರು</value>
  </data>
  <data name="Tax" xml:space="preserve">
    <value>ತೆರಿಗೆ</value>
  </data>
  <data name="Taxable" xml:space="preserve">
    <value>ಕರಾರ್ಹ</value>
  </data>
  <data name="Taxable1" xml:space="preserve">
    <value>ತೆರಿಗೆ 1</value>
  </data>
  <data name="Taxable2" xml:space="preserve">
    <value>ತೆರಿಗೆ 2</value>
  </data>
  <data name="TaxableAmount" xml:space="preserve">
    <value>ತೆರಿಗೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="Taxableothercharges" xml:space="preserve">
    <value>ತೆರಿಗೆ ಇತರೆ ಚಾರ್ಜಸ್</value>
  </data>
  <data name="Taxableothercharges1" xml:space="preserve">
    <value>ತೆರಿಗೆ ಇತರೆ ಚಾರ್ಜಸ್ 1</value>
  </data>
  <data name="Taxableothercharges1Amount" xml:space="preserve">
    <value>ತೆರಿಗೆ ಇತರೆ ಚಾರ್ಜಸ್ 1 ಪ್ರಮಾಣ</value>
  </data>
  <data name="Taxableothercharges2" xml:space="preserve">
    <value>ತೆರಿಗೆ ಇತರೆ ಚಾರ್ಜಸ್ 2</value>
  </data>
  <data name="Taxableothercharges2Amount" xml:space="preserve">
    <value>ತೆರಿಗೆ ಇತರೆ ಚಾರ್ಜಸ್ 2 ಪ್ರಮಾಣ</value>
  </data>
  <data name="TaxableotherchargesAmount" xml:space="preserve">
    <value>ತೆರಿಗೆ ಇತರೆ ಚಾರ್ಜಸ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="Taxableotherchargesamountcannotbegreaterthan" xml:space="preserve">
    <value>ತೆರಿಗೆ ಇತರ ಆರೋಪಗಳನ್ನು ಪ್ರಮಾಣದ ಹೆಚ್ಚಾಗಿದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Taxableotherchargesamountcannotpartiallyreturn" xml:space="preserve">
    <value>ತೆರಿಗೆ ಇತರ ಆರೋಪಗಳನ್ನು ಪ್ರಮಾಣದ ಭಾಗಶಃ ಮರಳಲು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Taxamount" xml:space="preserve">
    <value>ತೆರಿಗೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="Taxamount1" xml:space="preserve">
    <value>ತೆರಿಗೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TaxAmt" xml:space="preserve">
    <value>ತೆರಿಗೆ ಆಮ್ಟ್</value>
  </data>
  <data name="TaxCode" xml:space="preserve">
    <value>ತೆರಿಗೆ ಕೋಡ್</value>
  </data>
  <data name="TaxCode1" xml:space="preserve">
    <value>ತೆರಿಗೆ ಕೋಡ್</value>
  </data>
  <data name="TaxCode11" xml:space="preserve">
    <value>ತೆರಿಗೆ ಕೋಡ್</value>
  </data>
  <data name="TaxCodeName" xml:space="preserve">
    <value>ತೆರಿಗೆ ಕೋಡ್ ಹೆಸರು</value>
  </data>
  <data name="TaxDetail" xml:space="preserve">
    <value>ತೆರಿಗೆ ವಿವರ</value>
  </data>
  <data name="TaxDetails" xml:space="preserve">
    <value>ತೆರಿಗೆ ವಿವರಗಳು</value>
  </data>
  <data name="TaxFormula" xml:space="preserve">
    <value>ತೆರಿಗೆ ಫಾರ್ಮುಲಾ</value>
  </data>
  <data name="Taxnamealreadyexists" xml:space="preserve">
    <value>ತೆರಿಗೆ ಹೆಸರು ಈಗಾಗಲೇ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="TaxonTaxableOtherCharges" xml:space="preserve">
    <value>ತೆರಿಗೆ ರಚನೆ ಇತರೆ ಚಾರ್ಜಸ್</value>
  </data>
  <data name="TaxonTaxableOtherChargesAmount" xml:space="preserve">
    <value>ತೆರಿಗೆ ಇತರೆ ಆರೋಪದ ಮೇಲೆ ತೆರಿಗೆ</value>
  </data>
  <data name="taxpercentage" xml:space="preserve">
    <value>ತೆರಿಗೆ ಶೇಕಡಾವಾರು</value>
  </data>
  <data name="TaxStructure" xml:space="preserve">
    <value>ತೆರಿಗೆ ರಚನೆ</value>
  </data>
  <data name="taxstructuredetail" xml:space="preserve">
    <value>ತೆರಿಗೆ ರಚನೆ ವಿವರ</value>
  </data>
  <data name="taxstructuredetails" xml:space="preserve">
    <value>ತೆರಿಗೆ ರಚನೆ ವಿವರಗಳು</value>
  </data>
  <data name="taxstructureenglish" xml:space="preserve">
    <value>ತೆರಿಗೆ ರಚನೆ</value>
  </data>
  <data name="taxstructureenglish1" xml:space="preserve">
    <value>ತೆರಿಗೆ ರಚನೆ ಇಂಗ್ಲೀಷ್</value>
  </data>
  <data name="taxstructureheader" xml:space="preserve">
    <value>ತೆರಿಗೆ ರಚನೆ ಶಿರೋಲೇಖ</value>
  </data>
  <data name="TaxStructureisalreadyassociated" xml:space="preserve">
    <value>ತೆರಿಗೆ ರಚನೆ ಈಗಾಗಲೇ ಸಂಬಂಧಿಸಿದೆ</value>
  </data>
  <data name="taxstructurelocale" xml:space="preserve">
    <value>ತೆರಿಗೆ ರಚನೆ</value>
  </data>
  <data name="taxstructurelocale1" xml:space="preserve">
    <value>ತೆರಿಗೆ ರಚನೆ ಲೊಕೇಲ್</value>
  </data>
  <data name="TaxStructureMapping" xml:space="preserve">
    <value>ತೆರಿಗೆ ಸ್ಟ್ರಕ್ಚರ್ ಮ್ಯಾಪಿಂಗ್</value>
  </data>
  <data name="taxstructurename" xml:space="preserve">
    <value>ತೆರಿಗೆ ರಚನೆ ಹೆಸರು</value>
  </data>
  <data name="taxtype" xml:space="preserve">
    <value>ತೆರಿಗೆ ಪ್ರಕಾರ</value>
  </data>
  <data name="taxtypealreadyselected" xml:space="preserve">
    <value>ತೆರಿಗೆ ಪ್ರಕಾರ ಈಗಾಗಲೇ ಆಯ್ಕೆ</value>
  </data>
  <data name="taxtypealrearyselected" xml:space="preserve">
    <value>ತೆರಿಗೆ ರೀತಿಯ ಈಗಾಗಲೇ ಆಯ್ಕೆ</value>
  </data>
  <data name="taxtypeisreferencedinformulacannotdelete" xml:space="preserve">
    <value>ತೆರಿಗೆ ರೀತಿಯ ಅಳಿಸಲು ಸಾಧ್ಯವಿಲ್ಲ ಸೂತ್ರ ಉಲ್ಲೇಖಿಸಲಾಗುತ್ತದೆ</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>ತಂಡ</value>
  </data>
  <data name="Terms" xml:space="preserve">
    <value>ನಿಯಮಗಳು</value>
  </data>
  <data name="TermsandCondition" xml:space="preserve">
    <value>ನಿಯಮಗಳು ಮತ್ತು ಕಂಡಿಶನ್</value>
  </data>
  <data name="TermsAndConditions" xml:space="preserve">
    <value>ನಿಯಮಗಳು ಮತ್ತು ನಿಯಮಗಳು</value>
  </data>
  <data name="TermsConditions" xml:space="preserve">
    <value>ನಿಯಮಗಳು</value>
  </data>
  <data name="Thebrandhasalreadybeenselected" xml:space="preserve">
    <value>ಬ್ರ್ಯಾಂಡ್ ಈಗಾಗಲೇ ಆಯ್ಕೆ ಮಾಡಲಾಗಿದೆ</value>
  </data>
  <data name="TheCompanyDealerhasalreadybeenassociated" xml:space="preserve">
    <value>ಕಂಪನಿ / ಡೀಲರ್ ಈಗಾಗಲೇ ಸಂಬಂಧ</value>
  </data>
  <data name="themodelandserialnumberalreadyexists" xml:space="preserve">
    <value>ಮಾದರಿ ಮತ್ತು ಸರಣಿ ಸಂಖ್ಯೆ ಈಗಾಗಲೇ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="ThereisNoQuantityForReturn" xml:space="preserve">
    <value>ಮರಳಲು ಯಾವುದೇ ಪ್ರಮಾಣ ಇಲ್ಲ</value>
  </data>
  <data name="ThereisNoQuantityToReturn" xml:space="preserve">
    <value>ಮರಳಲು ಯಾವುದೇ ಪ್ರಮಾಣ ಇಲ್ಲ</value>
  </data>
  <data name="thesecondarysegmentalreadyexists" xml:space="preserve">
    <value>ಸೆಕೆಂಡರಿ ಸೆಗ್ಮೆಂಟ್ ಈಗಾಗಲೇ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="thestatealreadyexists" xml:space="preserve">
    <value>ರಾಜ್ಯ ಈಗಾಗಲೇ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="ThetaxStructurehasalreadybeenselected" xml:space="preserve">
    <value>ತೆರಿಗೆ ರಚನೆ ಈಗಾಗಲೇ ಆಯ್ಕೆ ಮಾಡಲಾಗಿದೆ</value>
  </data>
  <data name="ThirttoeighteenMonths" xml:space="preserve">
    <value>13-18 ತಿಂಗಳುಗಳು</value>
  </data>
  <data name="ThirtySixToFourtyEightHours" xml:space="preserve">
    <value>36 ರಿಂದ 48 ಗಂಟೆಗಳ</value>
  </data>
  <data name="ThisCaseIsClosed" xml:space="preserve">
    <value>ಈ ಘಟನೆಯು ಮುಚ್ಚಲ್ಪಟ್ಟಿತು</value>
  </data>
  <data name="Thisisfromcoretypemastercannotbedeleted" xml:space="preserve">
    <value>ಈ ಕೋರ್ ರೀತಿಯ ಅಳಿಸಲಾಗಿದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ThisLoginIDisalreadyexists" xml:space="preserve">
    <value>ಈ ಲಾಗಿನ್ ID ಈಗಾಗಲೇ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ ಇದೆ</value>
  </data>
  <data name="ThisModuleisalreadyexists" xml:space="preserve">
    <value>ಈ ಘಟಕವು ಈಗಾಗಲೇ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ ಇದೆ</value>
  </data>
  <data name="ThisRoleisalreadyexists" xml:space="preserve">
    <value>ಈ ಪಾತ್ರವನ್ನು ಈಗಾಗಲೇ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ ಆಗಿದೆ</value>
  </data>
  <data name="Thisroleisalreadyselectedfortheuser" xml:space="preserve">
    <value>ಈ ಪಾತ್ರವನ್ನು ಈಗಾಗಲೇ ಬಳಕೆದಾರ ಆಯ್ಕೆ ಇದೆ</value>
  </data>
  <data name="ThisSerialNumberisalreadyassociatedwiththecustomer" xml:space="preserve">
    <value>ಕ್ರಮ ಸಂಖ್ಯೆ ಈಗಾಗಲೇ ಗ್ರಾಹಕ ಸಂಬಂಧಿಸಿದೆ</value>
  </data>
  <data name="Thursday" xml:space="preserve">
    <value>ಗುರುವಾರ</value>
  </data>
  <data name="TimeRemaining" xml:space="preserve">
    <value>ಉಳಿದಸಮಯ</value>
  </data>
  <data name="TimeWiseDistributionOfPendingCases" xml:space="preserve">
    <value>ಬಾಕಿ ಇರುವ ಪ್ರಕರಣಗಳ ಸಮಯದಲ್ಲಿ ವೈಸ್ ವಿತರಣೆ</value>
  </data>
  <data name="To" xml:space="preserve">
    <value>ಗೆ</value>
  </data>
  <data name="TobeorderedExVatRMB" xml:space="preserve">
    <value>OrderedExVatRMB ಬಿ</value>
  </data>
  <data name="todate" xml:space="preserve">
    <value>ದಿನಾಂಕ</value>
  </data>
  <data name="ToDatecannotbegreaterthanCurrentDate" xml:space="preserve">
    <value>ದಿನಾಂಕ ಪ್ರಸ್ತುತ ದಿನಾಂಕ ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ToDateCannotbegreaterThanToday" xml:space="preserve">
    <value>ದಿನಾಂಕ ಪ್ರಸ್ತುತ ದಿನಾಂಕ ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ToDatecannotbelessthanCurrentDate" xml:space="preserve">
    <value>ದಿನಾಂಕ ಪ್ರಸ್ತುತ ದಿನಾಂಕ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="todatecannotbelessthenfromdatedate" xml:space="preserve">
    <value>ಇಲ್ಲಿಯವರೆಗೆ ದಿನಾಂಕದಿಂದ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="todatemustbegreaterthanorequaltofromdate" xml:space="preserve">
    <value>ದಿನಾಂಕ ಹೆಚ್ಚಿನ ಅಥವಾ ದಿನಾಂಕ ಗೆ ಸಮಾನವಾಗಿರಬೇಕು</value>
  </data>
  <data name="ToInvoiceNumber" xml:space="preserve">
    <value>ಸಂಖ್ಯೆ ಸರಕುಪಟ್ಟಿ</value>
  </data>
  <data name="ToOEM" xml:space="preserve">
    <value>OEM ಗೆ</value>
  </data>
  <data name="Tools" xml:space="preserve">
    <value>ಪರಿಕರಗಳು</value>
  </data>
  <data name="ToolsMaster" xml:space="preserve">
    <value>ಪರಿಕರಗಳು ಮಾಸ್ಟರ್</value>
  </data>
  <data name="ToolsName" xml:space="preserve">
    <value>ಪರಿಕರಗಳು ಹೆಸರು</value>
  </data>
  <data name="ToolsSearch" xml:space="preserve">
    <value>ಪರಿಕರಗಳು ಫೀಲ್ಡ್ ಹುಡುಕು</value>
  </data>
  <data name="ToolsValue" xml:space="preserve">
    <value>ಪರಿಕರಗಳು ಮೌಲ್ಯ</value>
  </data>
  <data name="Top10Customers" xml:space="preserve">
    <value>ಟಾಪ್ 10 ಗ್ರಾಹಕರು</value>
  </data>
  <data name="Top10Model" xml:space="preserve">
    <value>ಟಾಪ್ 10 ಮಾದರಿಗಳು</value>
  </data>
  <data name="Top10Parts" xml:space="preserve">
    <value>ಟಾಪ್ 10 ಭಾಗಗಳು</value>
  </data>
  <data name="Top10SalesRepresentative" xml:space="preserve">
    <value>ಟಾಪ್ 10 ಮಾರಾಟ ಪ್ರತಿನಿಧಿ</value>
  </data>
  <data name="ToParty" xml:space="preserve">
    <value>ಪಕ್ಷಕ್ಕೆ</value>
  </data>
  <data name="TopModel" xml:space="preserve">
    <value>ಟಾಪ್ 10 ಮಾದರಿಗಳು</value>
  </data>
  <data name="TopModelwithMaximumCase" xml:space="preserve">
    <value>ಗರಿಷ್ಠ ಕೇಸ್ ಟಾಪ್ ಮಾಡೆಲ್ಸ್</value>
  </data>
  <data name="TopModelwithMaximumSR" xml:space="preserve">
    <value>ಗರಿಷ್ಠ ಕೇಸ್ ಟಾಪ್ 10 ಮಾದರಿಗಳು</value>
  </data>
  <data name="TopModelwithMaximumSR1" xml:space="preserve">
    <value>ಗರಿಷ್ಠ ಸೇವೆ ವಿನಂತಿ ಟಾಪ್ 10 ಮಾದರಿಗಳು</value>
  </data>
  <data name="TopSalesRepresentative" xml:space="preserve">
    <value>ಟಾಪ್ SalesRepresentative</value>
  </data>
  <data name="ToptenPartsCategory" xml:space="preserve">
    <value>ಟಾಪ್ 10 ಭಾಗಗಳು ವರ್ಗ</value>
  </data>
  <data name="TopTenSalesPerformance" xml:space="preserve">
    <value>ಟಾಪ್ 10 ಮಾರಾಟದ ಪ್ರದರ್ಶನ</value>
  </data>
  <data name="ToStep" xml:space="preserve">
    <value>ಹಂತಕ್ಕೆ</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>ಒಟ್ಟು</value>
  </data>
  <data name="TotalAccessableAmount" xml:space="preserve">
    <value>ಒಟ್ಟು Assessable ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalAllocatedHours" xml:space="preserve">
    <value>ಒಟ್ಟು ನಿಗದಿ ಅವರ್ಸ್</value>
  </data>
  <data name="TotalAmount" xml:space="preserve">
    <value>ಒಟ್ಟು ಪ್ರಮಾಣ</value>
  </data>
  <data name="Totalamountcannotbezero" xml:space="preserve">
    <value>ಒಟ್ಟು ಪ್ರಮಾಣವನ್ನು ಶೂನ್ಯ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Totalamountcannotbezeroorblank" xml:space="preserve">
    <value>ಒಟ್ಟು ಪ್ರಮಾಣವನ್ನು ಶೂನ್ಯ ಅಥವಾ ಖಾಲಿ ಇರಕೂಡದು</value>
  </data>
  <data name="TotalAmountDue" xml:space="preserve">
    <value>ಕಾರಣ ಒಟ್ಟು ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalAmountIgnored" xml:space="preserve">
    <value>ನಿರ್ಲಕ್ಷಿಸಲಾಗಿದೆ ಒಟ್ಟು ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalAmountisbeyondacceptablelimit" xml:space="preserve">
    <value>ಒಟ್ಟು ಪ್ರಮಾಣದ ಸ್ವೀಕಾರಾರ್ಹ ಮಿತಿಯನ್ನು ಮೀರಿ</value>
  </data>
  <data name="TotalAmountReceived" xml:space="preserve">
    <value>ಸ್ವೀಕರಿಸಲಾಗಿದೆ ಒಟ್ಟು ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalAmountReturned" xml:space="preserve">
    <value>ಒಟ್ಟು ಪ್ರಮಾಣ ರಿಟರ್ನ್ಡ್</value>
  </data>
  <data name="TotalAmt" xml:space="preserve">
    <value>ಒಟ್ಟು ಆಮ್ಟ್</value>
  </data>
  <data name="TotalApproved" xml:space="preserve">
    <value>ಒಟ್ಟು ಅನುಮೋದನೆ</value>
  </data>
  <data name="TotalAVGWeightedAverageCost" xml:space="preserve">
    <value>ಒಟ್ಟು ಸರಾಸರಿ ಡಬ್ಲುಎಸಿ</value>
  </data>
  <data name="TotalBillOfEntryAmount" xml:space="preserve">
    <value>ಎಂಟ್ರಿ ಪ್ರಮಾಣವನ್ನು ಒಟ್ಟು ಬಿಲ್</value>
  </data>
  <data name="TotalBillOfEntryAmountIsBeyondAcceptableLimit" xml:space="preserve">
    <value>ಒಟ್ಟು BillOfEntry ಪ್ರಮಾಣ ಸ್ವೀಕಾರಾರ್ಹ ಮಿತಿ ಮೀರಿ</value>
  </data>
  <data name="TotalBOEAmount" xml:space="preserve">
    <value>ಎಂಟ್ರಿ ಪ್ರಮಾಣವನ್ನು ಒಟ್ಟು ಬಿಲ್</value>
  </data>
  <data name="TotalBondingAmount" xml:space="preserve">
    <value>ಒಟ್ಟು ಬಾಂಡಿಂಗ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalBreakDowns" xml:space="preserve">
    <value>ಒಟ್ಟು ಕುಸಿತಗಳು</value>
  </data>
  <data name="TotalCancellationAmountShouldbeGreaterthanzero" xml:space="preserve">
    <value>ಒಟ್ಟು ರದ್ದು ಪ್ರಮಾಣದ ಹೆಚ್ಚಾಗಿದೆ ಶೂನ್ಯ ಇರಬೇಕು</value>
  </data>
  <data name="TotalCaseWeightisbeyondacceptablelimit" xml:space="preserve">
    <value>ಒಟ್ಟು ತೂಕ ಸ್ವೀಕಾರಾರ್ಹ ಮಿತಿಯನ್ನು ಮೀರಿ</value>
  </data>
  <data name="TotalClaimed" xml:space="preserve">
    <value>ಹಕ್ಕು ಪಡೆದ ಒಟ್ಟು</value>
  </data>
  <data name="TotalClaimedAmount" xml:space="preserve">
    <value>ಒಟ್ಟು ಹಕ್ಕುಪಡೆದಿರುವುದು ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalDeallocatedQty" xml:space="preserve">
    <value>ಒಟ್ಟು ಹಂಚಿಕೆಯು ರದ್ದತಿಯಾಗುವುದಕ್ಕೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalDeduction" xml:space="preserve">
    <value>ಒಟ್ಟು ಕಳೆಯುವುದು</value>
  </data>
  <data name="TotalDutyAmount" xml:space="preserve">
    <value>ಒಟ್ಟು ಡ್ಯೂಟಿ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalExpenseAmount" xml:space="preserve">
    <value>ಒಟ್ಟು ಖರ್ಚು ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalExpenses" xml:space="preserve">
    <value>ಒಟ್ಟು ವೆಚ್ಚಗಳು</value>
  </data>
  <data name="TotalFullFilledOrderLines" xml:space="preserve">
    <value>ಒಟ್ಟು ಪೂರ್ಣ ತುಂಬಿದ ಆರ್ಡರ್ ಲೈನ್ಸ್</value>
  </data>
  <data name="TotalGoodsReleaseNoteAmount" xml:space="preserve">
    <value>ಒಟ್ಟು ಗೂಡ್ಸ್ ರಿಲೀಸ್ ನೋಟ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalGRNAmount" xml:space="preserve">
    <value>ಒಟ್ಟು ಜಿ ಆರ್ ಎನ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalGRNAmountShouldbeGreaterthanzero" xml:space="preserve">
    <value>ಒಟ್ಟು ಜಿ ಆರ್ ಎನ್ ಪ್ರಮಾಣದ ಹೆಚ್ಚಾಗಿದೆ ಶೂನ್ಯ ಇರಬೇಕು</value>
  </data>
  <data name="TotalHMR" xml:space="preserve">
    <value>ಒಟ್ಟು HMR</value>
  </data>
  <data name="TotalHours" xml:space="preserve">
    <value>ಒಟ್ಟು ಅವರ್ಸ್</value>
  </data>
  <data name="TotalInvoiceAmount" xml:space="preserve">
    <value>ಒಟ್ಟು ಸರಕುಪಟ್ಟಿ ಪ್ರಮಾಣ</value>
  </data>
  <data name="Totalinvoiceamountcannotbezero" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಒಟ್ಟು ಪ್ರಮಾಣವನ್ನು ಶೂನ್ಯ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="TotalInvoiceCancellationAmount" xml:space="preserve">
    <value>ಒಟ್ಟು ಸರಕುಪಟ್ಟಿ ರದ್ದತಿ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalInvoiceCancellationamountcannotbezero" xml:space="preserve">
    <value>ಒಟ್ಟು ಸರಕುಪಟ್ಟಿ ರದ್ದತಿ ಪ್ರಮಾಣದ ಶೂನ್ಯ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="TotalLandingCost" xml:space="preserve">
    <value>ಒಟ್ಟು ಲ್ಯಾಂಡಿಂಗ್ ವೆಚ್ಚ</value>
  </data>
  <data name="TotalMachineTransferRequestamountcannotbezero" xml:space="preserve">
    <value>ಒಟ್ಟು ಉತ್ಪನ್ನ ವರ್ಗಾವಣೆ ಮನವಿ ಪ್ರಮಾಣದ ಶೂನ್ಯ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="TotalNumberoforderlines" xml:space="preserve">
    <value>ಆರ್ಡರ್ ಲೈನ್ಸ್ ಒಟ್ಟು ಸಂಖ್ಯೆ</value>
  </data>
  <data name="TotalOn" xml:space="preserve">
    <value>ಒಟ್ಟು ರಂದು</value>
  </data>
  <data name="TotalOrderValue" xml:space="preserve">
    <value>ಒಟ್ಟು ಆರ್ಡರ್ ಮೌಲ್ಯ</value>
  </data>
  <data name="TotalPartsAmount" xml:space="preserve">
    <value>ಒಟ್ಟು ಭಾಗಗಳು ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalPIAMount" xml:space="preserve">
    <value>ಒಟ್ಟು ಪಿಐ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalPIAMountInLocalCurrency" xml:space="preserve">
    <value>ಸ್ಥಳೀಯ ಕರೆನ್ಸಿಯಲ್ಲಿ ಒಟ್ಟು ಪಿಐ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalPickedQuantity" xml:space="preserve">
    <value>ಒಟ್ಟು ಆಯ್ಕೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalProductSalesInvoiceAmount" xml:space="preserve">
    <value>ಒಟ್ಟು ಉತ್ಪನ್ನ ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalProductSalesInvoiceCancellationAmount" xml:space="preserve">
    <value>ಒಟ್ಟು ಉತ್ಪನ್ನ ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ ರದ್ದತಿ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalProductTransferGRNAmount" xml:space="preserve">
    <value>ಒಟ್ಟು ಉತ್ಪನ್ನ ಟ್ರಾನ್ಸ್ಫರ್ ಜಿ ಆರ್ ಎನ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalProductTransferNoteAmountCannotBeZero" xml:space="preserve">
    <value>ಒಟ್ಟು ಉತ್ಪನ್ನ ಟ್ರಾನ್ಸ್ಫರ್ ಗಮನಿಸಿ ಪ್ರಮಾಣದ ಶೂನ್ಯ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="TotalPurchaseGRNAmount" xml:space="preserve">
    <value>ಒಟ್ಟು ಖರೀದಿಸಿ ಜಿ ಆರ್ ಎನ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalPurchaseGRNAmountCanNotBeZero" xml:space="preserve">
    <value>ಒಟ್ಟು ಖರೀದಿಸಿ ಜಿ ಆರ್ ಎನ್ ಪ್ರಮಾಣ ಶೂನ್ಯವಾಗಿರುವುದಿಲ್ಲ</value>
  </data>
  <data name="TotalPurchaseInvoiceAmountCanNotBeZero" xml:space="preserve">
    <value>ಒಟ್ಟು ಖರೀದಿ ಸರಕುಪಟ್ಟಿ ಪ್ರಮಾಣ ಶೂನ್ಯವಾಗಿರುವುದಿಲ್ಲ</value>
  </data>
  <data name="Totalpurchaseorderamount" xml:space="preserve">
    <value>ಒಟ್ಟು ಪರ್ಚೇಸ್ ಆರ್ಡರ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalPurchaseOrderAmountcannotbezero" xml:space="preserve">
    <value>ಒಟ್ಟು ಖರೀದಿ ಆದೇಶವನ್ನು ಪ್ರಮಾಣದ ಶೂನ್ಯ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="TotalQuantity" xml:space="preserve">
    <value>ಒಟ್ಟು ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalQuotationAmount" xml:space="preserve">
    <value>ಒಟ್ಟು ಉದ್ಧರಣ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalReallocatedQty" xml:space="preserve">
    <value>ಒಟ್ಟು ಉಪಯೊಗಕ್ಕೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalRetailPrice" xml:space="preserve">
    <value>ಒಟ್ಟು ಚಿಲ್ಲರೆ ಬೆಲೆ</value>
  </data>
  <data name="TotalSalesInvoiceAmount" xml:space="preserve">
    <value>ಒಟ್ಟು ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalSalesOrderAmount" xml:space="preserve">
    <value>ಒಟ್ಟು ಮಾರಾಟದ ಆರ್ಡರ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalServiceChargesAmount" xml:space="preserve">
    <value>ಒಟ್ಟು ಸೇವೆ ಚಾರ್ಜಸ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="totalStock" xml:space="preserve">
    <value>ಒಟ್ಟು ಸ್ಟಾಕ್</value>
  </data>
  <data name="TotalStockTransferRequestamountcannotbezero" xml:space="preserve">
    <value>ಒಟ್ಟು ಸ್ಟಾಕ್ ವರ್ಗಾವಣೆ ವಿನಂತಿಯನ್ನು ಪ್ರಮಾಣದ ಶೂನ್ಯ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="TotalSundryAmount" xml:space="preserve">
    <value>ಒಟ್ಟು ಸಂಡ್ರಿ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalTaxableAmount" xml:space="preserve">
    <value>ಒಟ್ಟು ತೆರಿಗೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TotalTaxableAmountBlank" xml:space="preserve">
    <value>ಒಟ್ಟು ತೆರಿಗೆ ಪ್ರಮಾಣವನ್ನು ಖಾಲಿ</value>
  </data>
  <data name="TotalTime" xml:space="preserve">
    <value>ಒಟ್ಟು ಟೈಮ್</value>
  </data>
  <data name="TotalValue" xml:space="preserve">
    <value>ಒಟ್ಟು ಮೌಲ್ಯ</value>
  </data>
  <data name="TotalWeight" xml:space="preserve">
    <value>ಒಟ್ಟು ತೂಕ</value>
  </data>
  <data name="TotalWorkingHours" xml:space="preserve">
    <value>ಒಟ್ಟು ವರ್ಕಿಂಗ್ ಅವರ್ಸ್</value>
  </data>
  <data name="Track" xml:space="preserve">
    <value>ಟ್ರ್ಯಾಕ್</value>
  </data>
  <data name="TransactionisalreadybeenLocked" xml:space="preserve">
    <value>ಟ್ರಾನ್ಸಾಕ್ಷನ್ ಈಗಾಗಲೇ ಲಾಕ್ ಮಾಡಲಾಗಿದೆ</value>
  </data>
  <data name="TransactionisalreadybeenUnLocked" xml:space="preserve">
    <value>ಟ್ರಾನ್ಸಾಕ್ಷನ್ ಈಗಾಗಲೇ ಅನ್ಲಾಕ್ ಮಾಡಲಾಗಿದೆ</value>
  </data>
  <data name="TransactionLockedSuccessfully" xml:space="preserve">
    <value>ಟ್ರಾನ್ಸಾಕ್ಷನ್ ಯಶಸ್ವಿಯಾಗಿ ಲಾಕ್</value>
  </data>
  <data name="TransactionNumber" xml:space="preserve">
    <value>ಟ್ರಾನ್ಸಾಕ್ಷನ್ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="TransactionOrderClass" xml:space="preserve">
    <value>ಟ್ರಾನ್ಸಾಕ್ಷನ್ ಆರ್ಡರ್ ವರ್ಗ</value>
  </data>
  <data name="TransactionOrderClassLocale" xml:space="preserve">
    <value>ಟ್ರಾನ್ಸಾಕ್ಷನ್ ಆರ್ಡರ್ ವರ್ಗ ಲೊಕೇಲ್</value>
  </data>
  <data name="TransactionQuantity" xml:space="preserve">
    <value>ಟ್ರಾನ್ಸಾಕ್ಷನ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="Transactions" xml:space="preserve">
    <value>ಟ್ರಾನ್ಸಾಕ್ಷನ್ಸ್</value>
  </data>
  <data name="TransactionType" xml:space="preserve">
    <value>ವ್ಯವಹಾರ ಕೌಟುಂಬಿಕತೆ</value>
  </data>
  <data name="TransactionUnLockedSuccessfully" xml:space="preserve">
    <value>ಟ್ರಾನ್ಸಾಕ್ಷನ್ ಯಶಸ್ವಿಯಾಗಿ ಅನ್ಲಾಕ್</value>
  </data>
  <data name="TransferQuantity" xml:space="preserve">
    <value>ವರ್ಗಾವಣೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TransferredBy" xml:space="preserve">
    <value>ವರ್ಗಾವಣೆ</value>
  </data>
  <data name="TransportationDetails" xml:space="preserve">
    <value>ಸಾರಿಗೆ ವಿವರಗಳು</value>
  </data>
  <data name="TransportationRemarks" xml:space="preserve">
    <value>ಸಾರಿಗೆ ರಿಮಾರ್ಕ್ಸ್</value>
  </data>
  <data name="TransportationResponsibility" xml:space="preserve">
    <value>ಸಾರಿಗೆ ರೆಸ್ಪಾನ್ಸಿಬಿಲಿಟಿ</value>
  </data>
  <data name="TransportationResponsiblity" xml:space="preserve">
    <value>ಸಾರಿಗೆ ಜವಾಬ್ದಾರಿ</value>
  </data>
  <data name="Transporter" xml:space="preserve">
    <value>ಟ್ರಾನ್ಸ್ಪೋರ್ಟರ್</value>
  </data>
  <data name="TransportMode" xml:space="preserve">
    <value>ಸಾರಿಗೆ ಮೋಡ್</value>
  </data>
  <data name="TravelDetail" xml:space="preserve">
    <value>ಪ್ರಯಾಣ ವಿವರ</value>
  </data>
  <data name="TravelDetail1" xml:space="preserve">
    <value>ಪ್ರಯಾಣ ವಿವರ</value>
  </data>
  <data name="TravelExpensecannotexceedCampaignExpenseAmount" xml:space="preserve">
    <value>ಪ್ರಯಾಣ ವೆಚ್ಚ ಪ್ರಚಾರ ವೆಚ್ಚದಲ್ಲಿ ಪ್ರಮಾಣವನ್ನು ಮೀರಬಾರದು</value>
  </data>
  <data name="Tuesday" xml:space="preserve">
    <value>ಮಂಗಳವಾರ</value>
  </data>
  <data name="TwelveMonthQty" xml:space="preserve">
    <value>12 ತಿಂಗಳ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TwelveMonthValue" xml:space="preserve">
    <value>12 ತಿಂಗಳ ಮೌಲ್ಯ</value>
  </data>
  <data name="TwentyFourMonthQty" xml:space="preserve">
    <value>24 ತಿಂಗಳ ಪ್ರಮಾಣ</value>
  </data>
  <data name="TwentyFourMonthValue" xml:space="preserve">
    <value>24 ತಿಂಗಳ ಮೌಲ್ಯ</value>
  </data>
  <data name="TwentyFourToThirtySixHours" xml:space="preserve">
    <value>24 36 ಗಂಟೆಗಳ</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>ಪ್ರಕಾರ</value>
  </data>
  <data name="TypeofPurchase" xml:space="preserve">
    <value>ಖರೀದಿ ಮಾದರಿ</value>
  </data>
  <data name="UnAllocatedSalesOrders" xml:space="preserve">
    <value>ನಿಯೋಜಿಸದೆ ಇರುವ ಮಾರಾಟದ ಆರ್ಡರ್ಸ್</value>
  </data>
  <data name="UnBlockaStock" xml:space="preserve">
    <value>ಒಂದು ಸ್ಟಾಕ್ ಅನಿರ್ಬಂಧಿಸಲು ದಯವಿಟ್ಟು</value>
  </data>
  <data name="UnderWarranty" xml:space="preserve">
    <value>ವಾರಂಟಿ</value>
  </data>
  <data name="UniqueIdentifier" xml:space="preserve">
    <value>ಅಸದೃಶ</value>
  </data>
  <data name="uniqueidentifieralreadyexists" xml:space="preserve">
    <value>ಅನನ್ಯ ಗುರುತು ಈಗಾಗಲೇ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ</value>
  </data>
  <data name="UnitCost" xml:space="preserve">
    <value>ಏಕಾಂಶ ಬೆಲೆ</value>
  </data>
  <data name="UnitMRP" xml:space="preserve">
    <value>ಘಟಕ MRP ಆರ್ಎಸ್</value>
  </data>
  <data name="unitofmeasurement" xml:space="preserve">
    <value>ಮಾಪನ ಘಟಕ</value>
  </data>
  <data name="UnitPrice" xml:space="preserve">
    <value>ಘಟಕದ ಬೆಲೆ</value>
  </data>
  <data name="UnitWeight" xml:space="preserve">
    <value>ಘಟಕ ತೂಕ</value>
  </data>
  <data name="UnLock" xml:space="preserve">
    <value>ಅನ್ಲಾಕ್</value>
  </data>
  <data name="UnMoved" xml:space="preserve">
    <value>ಅಚಲ</value>
  </data>
  <data name="UnMovedCannotbeGreaterthanorEqualtoSlow" xml:space="preserve">
    <value>ಅಚಲ ಹೆಚ್ಚು ಅಥವಾ ನಿಧಾನಗೊಳಿಸಲು ಸಮಾನವಾಗಿರಬೇಕು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="UnRegisteredServiceRequest" xml:space="preserve">
    <value>ನೋಂದಾಯಿಸದ ಸೇವೆ ಮನವಿ</value>
  </data>
  <data name="UnRegisteredServiceRequest1" xml:space="preserve">
    <value>ಪರಿಶೀಲನೆ ಸರದಿಗೆ</value>
  </data>
  <data name="uom" xml:space="preserve">
    <value>UOM</value>
  </data>
  <data name="UpdateArticle" xml:space="preserve">
    <value>ಅಪ್ಡೇಟ್ ಲೇಖನ</value>
  </data>
  <data name="UpdateGDR" xml:space="preserve">
    <value>ಜಿಡಿಆರ್ ನವೀಕರಿಸಿ</value>
  </data>
  <data name="UpdateWeightAverageCost" xml:space="preserve">
    <value>ಸಮತೋಲನದ ಸರಾಸರಿ ವೆಚ್ಚ ನವೀಕರಿಸಿ</value>
  </data>
  <data name="UploadAllPartsFromExcel" xml:space="preserve">
    <value>ಎಕ್ಸೆಲ್ ಎಲ್ಲಾ ಭಾಗಗಳ ಅಪ್ಲೋಡ್</value>
  </data>
  <data name="UploadBy" xml:space="preserve">
    <value>ಅಪ್ಲೋಡ್</value>
  </data>
  <data name="UploadDate" xml:space="preserve">
    <value>ಅಪ್ಲೋಡ್ ದಿನಾಂಕ</value>
  </data>
  <data name="Uploadedsuccesfully" xml:space="preserve">
    <value>ಯಶಸ್ವಿಯಾಗಿ ಅಪ್ಲೋಡ್</value>
  </data>
  <data name="uploadedwithsomeerrorcheckerrorexcel" xml:space="preserve">
    <value>ಕೆಲವು ದೋಷ ಚೆಕ್ ದೋಷ ಎಕ್ಸೆಲ್ ಅಪ್ಲೋಡ್</value>
  </data>
  <data name="UploadFile" xml:space="preserve">
    <value>ಫೈಲ್ ಅಪ್ಲೋಡ್</value>
  </data>
  <data name="UploadParts" xml:space="preserve">
    <value>ರಚಿಸಿ</value>
  </data>
  <data name="usageenvironment" xml:space="preserve">
    <value>ಬಳಕೆ ಪರಿಸರ</value>
  </data>
  <data name="UsedQunatityCannotBeGreaterThanIssuedQuantity" xml:space="preserve">
    <value>ಬಳಸಲಾದ ಪ್ರಮಾಣದಲ್ಲಿ ಬಿಡುಗಡೆ ಪ್ರಮಾಣದ ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="UsedQunatityCannotBeGreaterThanIssuedQuantityMinusReturned" xml:space="preserve">
    <value>ಬಳಸಲಾದ ಪ್ರಮಾಣದಲ್ಲಿ ಬಿಡುಗಡೆ ಪ್ರಮಾಣದ ಹೆಚ್ಚು ಸಾಧ್ಯವಿಲ್ಲ - ಮರಳಿದರು ಪ್ರಮಾಣ</value>
  </data>
  <data name="UsedQunatityCannotBeGreaterThanReceivedQuantityMinusReturned" xml:space="preserve">
    <value>ಉಪಯೋಗಿಸಿದ ಪ್ರಮಾಣ ಸ್ವೀಕರಿಸಿದ ಹೆಚ್ಚಿನ ಸಾಧ್ಯವಿಲ್ಲ - ಮರಳಿದರು ಪ್ರಮಾಣ</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>ಬಳಕೆದಾರ</value>
  </data>
  <data name="UserDataSavedSuccessfully" xml:space="preserve">
    <value>ಬಳಕೆದಾರ ಡೇಟಾ ಯಶಸ್ವಿಯಾಗಿ ಉಳಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="UserDetail" xml:space="preserve">
    <value>ಬಳಕೆದಾರ ವಿವರ</value>
  </data>
  <data name="UserDetails" xml:space="preserve">
    <value>ಬಳಕೆದಾರ ವಿವರಗಳು</value>
  </data>
  <data name="Userdonthaveaccesstopartymaster" xml:space="preserve">
    <value>ಬಳಕೆದಾರ ಪಕ್ಷದ ಮಾಸ್ಟರ್ ಪ್ರವೇಶವನ್ನು ಹೊಂದಿವೆ ಡೋಂಟ್</value>
  </data>
  <data name="Userdonthaveaccesstoproductmaster" xml:space="preserve">
    <value>ಬಳಕೆದಾರ ಉತ್ಪನ್ನ ಮಾಸ್ಟರ್ ಪ್ರವೇಶವನ್ನು ಹೊಂದಿವೆ ಡೋಂಟ್</value>
  </data>
  <data name="Userdonthaveeditaccess" xml:space="preserve">
    <value>ಬಳಕೆದಾರ ಎಡಿಟ್ ಪ್ರವೇಶ ಹೊಂದಿವೆ ಡೋಂಟ್</value>
  </data>
  <data name="Userdonthaveeditaccesstoproductmaster" xml:space="preserve">
    <value>ಬಳಕೆದಾರ ಉತ್ಪನ್ನ ಮಾಸ್ಟರ್ ಸಂಪಾದನೆಯ ಪ್ರವೇಶವನ್ನು ಹೊಂದಿವೆ ಡೋಂಟ್</value>
  </data>
  <data name="Userislocked" xml:space="preserve">
    <value>ಬಳಕೆದಾರ ಲಾಕ್</value>
  </data>
  <data name="Userisnotactive" xml:space="preserve">
    <value>ಬಳಕೆದಾರ ಸಕ್ರಿಯವಾಗಿಲ್ಲ</value>
  </data>
  <data name="UserName" xml:space="preserve">
    <value>ಬಳಕೆದಾರ ಹೆಸರು</value>
  </data>
  <data name="UserNameOrPasswordYouEnteredIsIncorrectPleaseTryAgain" xml:space="preserve">
    <value>ಬಳಕೆದಾರ ಹೆಸರು ಅಥವಾ ನೀವು ನಮೂದಿಸಿದ ಪಾಸ್ವರ್ಡ್ ತಪ್ಪಾಗಿದೆ.</value>
  </data>
  <data name="UserRoleDetails" xml:space="preserve">
    <value>ಬಳಕೆದಾರ ಪಾತ್ರ ವಿವರಗಳು</value>
  </data>
  <data name="UserRoles" xml:space="preserve">
    <value>ಬಳಕೆದಾರ ಪಾತ್ರಗಳು</value>
  </data>
  <data name="UserType" xml:space="preserve">
    <value>ಬಳಕೆದಾರನ ಪ್ರಕಾರ</value>
  </data>
  <data name="UtilizationPercentage" xml:space="preserve">
    <value>ಬಳಕೆ%</value>
  </data>
  <data name="ValadityPeriodInMonths" xml:space="preserve">
    <value>ತಿಂಗಳಲ್ಲಿ ವಾಯಿದೆ ಅವಧಿ</value>
  </data>
  <data name="Value" xml:space="preserve">
    <value>ಮೌಲ್ಯ</value>
  </data>
  <data name="Value1" xml:space="preserve">
    <value>ಮೌಲ್ಯ 1</value>
  </data>
  <data name="ValueAdjustment" xml:space="preserve">
    <value>ಮೌಲ್ಯ ಹೊಂದಾಣಿಕೆ</value>
  </data>
  <data name="valueadjustmentisblankorzero" xml:space="preserve">
    <value>ಮೌಲ್ಯ ಹೊಂದಾಣಿಕೆ ಖಾಲಿ ಅಥವಾ ಶೂನ್ಯ</value>
  </data>
  <data name="ValueinLocalCurrency" xml:space="preserve">
    <value>ಸ್ಥಳೀಯ ಕರೆನ್ಸಿಯಲ್ಲಿ ಮೌಲ್ಯ</value>
  </data>
  <data name="Valueinlocalcurrencycannotbezero" xml:space="preserve">
    <value>ಸ್ಥಳೀಯ ಕರೆನ್ಸಿಯಲ್ಲಿ ಮೌಲ್ಯ ಶೂನ್ಯ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ValueisMandatoryforselectedColumn" xml:space="preserve">
    <value>ಮೌಲ್ಯ ಆಯ್ಕೆ ಅಂಕಣ ಕಡ್ಡಾಯವಾಗಿ</value>
  </data>
  <data name="ValueofStock" xml:space="preserve">
    <value>ಷೇರುಗಳ ಮೌಲ್ಯವನ್ನು</value>
  </data>
  <data name="VAR" xml:space="preserve">
    <value>VAR</value>
  </data>
  <data name="VarianceinRate" xml:space="preserve">
    <value>ದರ ಭಿನ್ನಾಭಿಪ್ರಾಯ</value>
  </data>
  <data name="Variant" xml:space="preserve">
    <value>ಭಿನ್ನ</value>
  </data>
  <data name="VariantDetails" xml:space="preserve">
    <value>ಭಿನ್ನ ವಿವರಗಳು</value>
  </data>
  <data name="VariantName" xml:space="preserve">
    <value>ಭಿನ್ನ ಹೆಸರು</value>
  </data>
  <data name="VARs" xml:space="preserve">
    <value>VAR ಗಳು</value>
  </data>
  <data name="Ver" xml:space="preserve">
    <value>ಆವೃತ್ತಿ</value>
  </data>
  <data name="VerificationQueue" xml:space="preserve">
    <value>ಪರಿಶೀಲನೆ ಸರದಿಗೆ</value>
  </data>
  <data name="Version" xml:space="preserve">
    <value>ಆವೃತ್ತಿ</value>
  </data>
  <data name="VersionDate" xml:space="preserve">
    <value>ಆವೃತ್ತಿ ದಿನಾಂಕ</value>
  </data>
  <data name="VersionNumber" xml:space="preserve">
    <value>ಆವೃತ್ತಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="VersionNumberandDate" xml:space="preserve">
    <value>ಆವೃತ್ತಿ ಸಂಖ್ಯೆ ಮತ್ತು ದಿನಾಂಕ</value>
  </data>
  <data name="VersionwillbeCreatedContinue" xml:space="preserve">
    <value>ಆವೃತ್ತಿ ನಿರ್ಮಿಸಲಾಗುತ್ತದೆ.</value>
  </data>
  <data name="view" xml:space="preserve">
    <value>ನೋಟ</value>
  </data>
  <data name="ViewallRecords" xml:space="preserve">
    <value>ಎಲ್ಲಾ ರೆಕಾರ್ಡ್ಸ್ ವೀಕ್ಷಿಸಿ</value>
  </data>
  <data name="ViewArticle" xml:space="preserve">
    <value>ವೀಕ್ಷಿಸು ಲೇಖನ</value>
  </data>
  <data name="ViewJobCard" xml:space="preserve">
    <value>ವೀಕ್ಷಿಸು ಜಾಬ್ ಕಾರ್ಡ್</value>
  </data>
  <data name="ViewPartsMaster" xml:space="preserve">
    <value>ವೀಕ್ಷಿಸು ಭಾಗಗಳು ಮಾಸ್ಟರ್</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>ವೇರ್ ಹೌಸ್</value>
  </data>
  <data name="WareHouseFieldSearch" xml:space="preserve">
    <value>ವೇರ್ಹೌಸ್ ಫೀಲ್ಡ್ ಹುಡುಕು</value>
  </data>
  <data name="WareHouseName" xml:space="preserve">
    <value>ಗೋದಾಮಿನ ಹೆಸರು</value>
  </data>
  <data name="WarehouseOrderClassAssociation" xml:space="preserve">
    <value>ಮಳಿಗೆ</value>
  </data>
  <data name="WarehouseType" xml:space="preserve">
    <value>ವೇರ್ಹೌಸ್ ಪ್ರಕಾರ</value>
  </data>
  <data name="Warranty" xml:space="preserve">
    <value>ಅಧಿಕಾರ</value>
  </data>
  <data name="WarrantyCertificateFrom" xml:space="preserve">
    <value>ಖಾತರಿ ಪ್ರಮಾಣಪತ್ರ</value>
  </data>
  <data name="WarrantyClaim" xml:space="preserve">
    <value>ಖಾತರಿ ಹಕ್ಕು</value>
  </data>
  <data name="WarrantyClaimAlreadyDone" xml:space="preserve">
    <value>ಖಾತರಿ ಈಗಾಗಲೇ ಹಕ್ಕು ದಾಖಲಿಸಿದವರು</value>
  </data>
  <data name="WarrantyClaimCausingPartWiseReport" xml:space="preserve">
    <value>ಭಾಗ ವೈಸ್ ವರದಿ ಉಂಟುಮಾಡುವುದು ಖಾತರಿ ಹಕ್ಕು</value>
  </data>
  <data name="WarrantyClaimDate" xml:space="preserve">
    <value>ದಿನಾಂಕ</value>
  </data>
  <data name="WarrantyClaimID" xml:space="preserve">
    <value>ಖಾತರಿ ಹಕ್ಕು ID</value>
  </data>
  <data name="WarrantyClaimKPIReport" xml:space="preserve">
    <value>ಖಾತರಿ ಹಕ್ಕು KPI ವರದಿ</value>
  </data>
  <data name="WarrantyClaimKPIReportFrom" xml:space="preserve">
    <value>ಖಾತರಿ ಹಕ್ಕು KPI ವರದಿ</value>
  </data>
  <data name="WarrantyClaimNumber" xml:space="preserve">
    <value>ಖಾತರಿ ಹಕ್ಕು ಸಂಖ್ಯೆ</value>
  </data>
  <data name="WarrantyClaimOEMApproval" xml:space="preserve">
    <value>ಖಾತರಿ ಹಕ್ಕು ಅನುಮೋದನೆ</value>
  </data>
  <data name="WarrantyClaimRe-SettledCost" xml:space="preserve">
    <value>ಖಾತರಿ ಹಕ್ಕು ಮರು ನೆಲೆಸಿದರು ವೆಚ್ಚ</value>
  </data>
  <data name="WarrantyClaimSettledCost" xml:space="preserve">
    <value>ಖಾತರಿ ಕ್ಲೈಮ್ ವೆಚ್ಚ</value>
  </data>
  <data name="WarrantyDate" xml:space="preserve">
    <value>ಖಾತರಿ ದಿನಾಂಕ</value>
  </data>
  <data name="WarrantyDescription" xml:space="preserve">
    <value>ಖಾತರಿ ವಿವರಣೆ</value>
  </data>
  <data name="warrantydetails" xml:space="preserve">
    <value>ಖಾತರಿ ವಿವರ</value>
  </data>
  <data name="WarrantyHMR" xml:space="preserve">
    <value>ಖಾತರಿ HMR</value>
  </data>
  <data name="WarrantyPeriod" xml:space="preserve">
    <value>ಖಾತರಿ ಅವಧಿಯ</value>
  </data>
  <data name="WarrantyType" xml:space="preserve">
    <value>ಖಾತರಿ ಪ್ರಕಾರ</value>
  </data>
  <data name="Website" xml:space="preserve">
    <value>ವೆಬ್ಸೈಟ್</value>
  </data>
  <data name="Wednesday" xml:space="preserve">
    <value>ಬುಧವಾರ</value>
  </data>
  <data name="weight" xml:space="preserve">
    <value>ತೂಕ</value>
  </data>
  <data name="WeightCannotBeZero" xml:space="preserve">
    <value>ತೂಕ ಶೂನ್ಯ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="WeightedAverageCost" xml:space="preserve">
    <value>ಸಮತೋಲನದ ಸರಾಸರಿ ವೆಚ್ಚ</value>
  </data>
  <data name="WeightedAverageCostisZero" xml:space="preserve">
    <value>ಸಮತೋಲನದ ಸರಾಸರಿ ವೆಚ್ಚ ಶೂನ್ಯ</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>ಸ್ವಾಗತ:</value>
  </data>
  <data name="Where" xml:space="preserve">
    <value>ಎಲ್ಲಿ</value>
  </data>
  <data name="WonQty" xml:space="preserve">
    <value>ಗೆಲುವು ಪ್ರಮಾಣ</value>
  </data>
  <data name="WorkFlow" xml:space="preserve">
    <value>ಕೆಲಸ ಹರಿವು</value>
  </data>
  <data name="WorkFlowID" xml:space="preserve">
    <value>ಫ್ಲೋ ID ಯನ್ನು ಕೆಲಸ</value>
  </data>
  <data name="WorkFlowName" xml:space="preserve">
    <value>ಫ್ಲೋ ಹೆಸರು ಕೆಲಸ</value>
  </data>
  <data name="WorkFlowSteps" xml:space="preserve">
    <value>ಫ್ಲೋ ಕ್ರಮಗಳು ಕೆಲಸ</value>
  </data>
  <data name="WorkingDays" xml:space="preserve">
    <value>ಕೆಲಸ ದಿನಗಳ</value>
  </data>
  <data name="WorkingTime" xml:space="preserve">
    <value>ಕೆಲಸ ಸಮಯ</value>
  </data>
  <data name="Year" xml:space="preserve">
    <value>ವರ್ಷ</value>
  </data>
  <data name="Yearshouldbebetween2000and2999" xml:space="preserve">
    <value>ವರ್ಷ 2000 ಮತ್ತು 2999 ನಡುವೆ ಇರಬೇಕು</value>
  </data>
  <data name="YearWiseEnquiryToOrderConvertionPercentageGraphForLast6Years" xml:space="preserve">
    <value>ವರ್ಷವಾರು ವಿಚಾರಣೆ ಕಳೆದ 6 ವರ್ಷಗಳಿಂದ Convertion ಶೇಕಡಾವಾರು ಗ್ರಾಫ್ ಆದೇಶ</value>
  </data>
  <data name="yearwisesalesordercountgraphforlast6years" xml:space="preserve">
    <value>ವರ್ಷವಾರು ಮಾರಾಟದ ಆರ್ಡರ್ ಕಳೆದ 6 ವರ್ಷಗಳಿಂದ ಗ್ರಾಫ್ ಕೌಂಟ್</value>
  </data>
  <data name="yes" xml:space="preserve">
    <value>ಹೌದು</value>
  </data>
  <data name="YouChangedTheEmailDetailsWillBeLostDoYouWantToProceed" xml:space="preserve">
    <value>ನೀವು ಇಮೇಲ್ ಬದಲಾಗಿದೆ.</value>
  </data>
  <data name="YouChangedTheLocationDetailsWillBeLostDoYouWantToProceed" xml:space="preserve">
    <value>ನೀವು ಸ್ಥಳ ಬದಲಾಗಿದೆ.</value>
  </data>
  <data name="YouChangedTheMobileDetailsWillBeLostDoYouWantToProceed" xml:space="preserve">
    <value>ನೀವು ಮೊಬೈಲ್ ಬದಲಾಗಿದೆ.</value>
  </data>
  <data name="YouChangedTheNameDetailsWillBeLostDoYouWantToProceed" xml:space="preserve">
    <value>ನೀವು ಹೆಸರು ಬದಲಾಗಿದೆ.</value>
  </data>
  <data name="Youdonothaveaddpermission" xml:space="preserve">
    <value>ನೀವು ಸೇರಿಸಲು ಅನುಮತಿ ಇಲ್ಲ</value>
  </data>
  <data name="Youdonothaveeditpermission" xml:space="preserve">
    <value>ನೀವು ಬದಲಾಯಿಸಿ ಅನುಮತಿ ಇಲ್ಲ</value>
  </data>
  <data name="youdonthaveaccesspermissiontoallocationpriority" xml:space="preserve">
    <value>ನೀವು ಅಲೋಕೇಶನ್ ಆದ್ಯತಾ ಪ್ರವೇಶ ಅನುಮತಿ ಇಲ್ಲ</value>
  </data>
  <data name="YouDontHaveAccessRights" xml:space="preserve">
    <value>ನೀವು ಪ್ರವೇಶ ಹಕ್ಕುಗಳನ್ನು ಹೊಂದಿವೆ ಡೋಂಟ್</value>
  </data>
  <data name="youdonthavepermissiontoaddrecordstobinlocationmaster" xml:space="preserve">
    <value>ನೀವು ಬಿನ್ ಸ್ಥಳ ಮಾಸ್ಟರ್ ದಾಖಲೆಗಳನ್ನು ಸೇರಿಸಲು ಅನುಮತಿ ಇಲ್ಲ</value>
  </data>
  <data name="YouhavebeenLoggedoutsuccessfully" xml:space="preserve">
    <value>ನೀವು ಯಶಸ್ವಿಯಾಗಿ ಔಟ್ ಮಾಡಿದ್ದೀರಿ</value>
  </data>
  <data name="ZerotoSixMonths" xml:space="preserve">
    <value>0-6 ತಿಂಗಳು</value>
  </data>
  <data name="ZipCode" xml:space="preserve">
    <value>ಜಿಪ್ ಕೋಡ್</value>
  </data>
  <data name="ActiveQtyShouldBeLessThanOrEqualToQuantity" xml:space="preserve">
    <value>ಸಕ್ರಿಯ ಪ್ರಮಾಣ ಕಡಿಮೆ ಅಥವಾ ಪ್ರಮಾಣಕ್ಕೆ ಸಮ ಇರಬೇಕು</value>
  </data>
  <data name="ActiveQuantityCanNotBeLessThanAllocatedQuantity" xml:space="preserve">
    <value>ಸಕ್ರಿಯ ಪ್ರಮಾಣ ನಿಗದಿ ಪ್ರಮಾಣ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ActiveQtyCanNotBeBlank" xml:space="preserve">
    <value>ಸಕ್ರಿಯ ಪ್ರಮಾಣ ಖಾಲಿ ಇರಕೂಡದು</value>
  </data>
  <data name="RateCannotbeZeroOrBlank" xml:space="preserve">
    <value>KA Rate cannot be zero or blank</value>
  </data>
  <data name="FinancedAmountCanNotBeGreaterThanTotalSalesOrderAmount" xml:space="preserve">
    <value>ಆರ್ಥಿಕ ವೊತ್ತ ಮಾರಾಟ ಸಲುವಾಗಿ ಪ್ರಮಾಣದ ಹೆಚ್ಚಾಗಿದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="FinancedAmountCanNotBeZero" xml:space="preserve">
    <value>ಆರ್ಥಿಕ ವೊತ್ತ ಶೂನ್ಯಕ್ಕೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="LCAmountCanNotBeGreaterThanTotalSalesOrderAmount" xml:space="preserve">
    <value>ಎಲ್ಸಿ ವೊತ್ತ ಒಟ್ಟು ಮಾರಾಟ ಸಲುವಾಗಿ ವೊತ್ತ ಹೆಚ್ಚಾಗಿದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="LCAmountCanNotBeZero" xml:space="preserve">
    <value>ಎಲ್ಸಿ ವೊತ್ತ ಶೂನ್ಯವಾಗಿರುವುದಿಲ್ಲ</value>
  </data>
  <data name="DuplicateExpenses" xml:space="preserve">
    <value>ವೆಚ್ಚಗಳು ನಕಲು</value>
  </data>
  <data name="PleaseSelectPurchaseInvoiceNumber" xml:space="preserve">
    <value>ಖರೀದಿ ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ ಆಯ್ಕೆಮಾಡಿ</value>
  </data>
  <data name="TotalLandingCostIsBeyondAcceptableLimit" xml:space="preserve">
    <value>ಒಟ್ಟು ಲ್ಯಾಂಡಿಂಗ್ ವೆಚ್ಚ ಸ್ವೀಕಾರಾರ್ಹ ಮಿತಿಯನ್ನು ಮೀರಿದೆ</value>
  </data>
  <data name="PurchaseHistory" xml:space="preserve">
    <value>ಖರೀದಿ ಇತಿಹಾಸ</value>
  </data>
  <data name="String2" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿ ಪ್ರಮಾಣದ ಹೆಚ್ಚಾಗಿದೆ ಇರುವಂತಿಲ್ಲ</value>
  </data>
  <data name="Discountamountshouldnotbegreaterthantotaldamount" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿ ವೊತ್ತ ಒಟ್ಟು ಪ್ರಮಾಣದ ಹೆಚ್ಚಾಗಿದೆ ಇರುವಂತಿಲ್ಲ</value>
  </data>
  <data name="DuplicateRecords" xml:space="preserve">
    <value>ರೆಕಾರ್ಡ್ಸ್ ನಕಲು</value>
  </data>
  <data name="InvalidProductSalesOrderNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಉತ್ಪನ್ನ ಮಾರಾಟದ ಆದೇಶ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ExchangeRatecannotbeblank" xml:space="preserve">
    <value>ವಿನಿಮಯ ದರ ಖಾಲಿ ಇರಕೂಡದು</value>
  </data>
  <data name="DuplicateCostingType" xml:space="preserve">
    <value>ಮಾದರಿ ಕಾಸ್ಟಿಂಗ್ ನಕಲು</value>
  </data>
  <data name="Issuedquantitycannotbegreaterthancommittedquantity" xml:space="preserve">
    <value>ನೀಡಲಾಗಿದೆ ಪ್ರಮಾಣ ಬದ್ಧ ಪ್ರಮಾಣ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Amountcannotbezero" xml:space="preserve">
    <value>ವೊತ್ತ ಶೂನ್ಯವಾಗಿರುವುದಿಲ್ಲ</value>
  </data>
  <data name="DeliveryNoteNumberFieldSearch" xml:space="preserve">
    <value>ಡೆಲಿವರಿ ಗಮನಿಸಿ ಸಂಖ್ಯೆ ಫೀಲ್ಡ್ ಹುಡುಕು</value>
  </data>
  <data name="ProductSalesInvoiceNumberFieldSearch" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಮಾರಾಟದ ಸರಕುಪಟ್ಟಿ ಸಂಖ್ಯೆ ಫೀಲ್ಡ್ ಹುಡುಕು</value>
  </data>
  <data name="ProductTransferNoteNumberFieldSearch" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಟ್ರಾನ್ಸ್ಫರ್ ಗಮನಿಸಿ ಸಂಖ್ಯೆ ಫೀಲ್ಡ್ ಹುಡುಕು</value>
  </data>
  <data name="CostingTotalIsBeyondAcceptableLimit" xml:space="preserve">
    <value>ಒಟ್ಟು ಕಾಸ್ಟಿಂಗ್ ಸ್ವೀಕಾರಾರ್ಹ ಮಿತಿ ಮೀರಿ</value>
  </data>
  <data name="InsurancePremiumAmountCannotbeGreaterThanInsuredAmount" xml:space="preserve">
    <value>ವಿಮೆ ಪ್ರೀಮಿಯಂ ವೊತ್ತ ವಿಮೆಯನ್ನು ಪಡೆದ ವೊತ್ತ ಅಧಿಕವಾಗಿರುತ್ತದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="InsurancePremiumAmountCanNotBeZero" xml:space="preserve">
    <value>ವಿಮಾ ಪ್ರೀಮಿಯಂ ಮೊತ್ತ ಶೂನ್ಯ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="InsuredAmountCanNotBeGreaterThanTotalSalesOrderAmount" xml:space="preserve">
    <value>ವಿಮೆ ವೊತ್ತ ಹೆಚ್ಚಿನ ಒಟ್ಟು ಮಾರಾಟದ ಆರ್ಡರ್ ವೊತ್ತ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="InsuredAmountCanNotBeZero" xml:space="preserve">
    <value>ವಿಮೆ ವೊತ್ತ ಶೂನ್ಯವಾಗಿರುವುದಿಲ್ಲ</value>
  </data>
  <data name="InactiveTransporterName" xml:space="preserve">
    <value>ನಿಷ್ಕ್ರಿಯ ಟ್ರಾನ್ಸ್ಪೋರ್ಟರ್ ಹೆಸರು</value>
  </data>
  <data name="PleaseEnterActiveQuantity" xml:space="preserve">
    <value>ಸಕ್ರಿಯ ಪ್ರಮಾಣ ನಮೂದಿಸಿ</value>
  </data>
  <data name="AddBillOfEntry" xml:space="preserve">
    <value>ಎಂಟ್ರಿ ಬಿಲ್ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddBonding" xml:space="preserve">
    <value>ಬಂಧನ ಸೇರಿಸಿ</value>
  </data>
  <data name="AddDeliveryNote" xml:space="preserve">
    <value>ಡೆಲಿವರಿ ಟಿಪ್ಪಣಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="editBillOfEntry" xml:space="preserve">
    <value>ಎಂಟ್ರಿ ಬಿಲ್ ಬದಲಾಯಿಸಿ</value>
  </data>
  <data name="editBonding" xml:space="preserve">
    <value>ಬದಲಾಯಿಸಿ ಬಂಧನ</value>
  </data>
  <data name="editDeliveryNote" xml:space="preserve">
    <value>ಬದಲಾಯಿಸಿ ಡೆಲಿವರಿ ಗಮನಿಸಿ</value>
  </data>
  <data name="ActiveQuantity" xml:space="preserve">
    <value>ಸಕ್ರಿಯ ಪ್ರಮಾಣ</value>
  </data>
  <data name="Reserve" xml:space="preserve">
    <value>ಮೀಸಲು</value>
  </data>
  <data name="WonQuantity" xml:space="preserve">
    <value>ಗೆಲುವು ಪ್ರಮಾಣ</value>
  </data>
  <data name="InvalidProductTransferRequestNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಉತ್ಪನ್ನ ವರ್ಗಾವಣೆ ಮನವಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ProductTransferNoteIsAlreadyCreatedForThisProductTransferRequestNumber" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ಟ್ರಾನ್ಸ್ಫರ್ ಗಮನಿಸಿ ಈಗಾಗಲೇ ಈ ಉತ್ಪನ್ನ ವರ್ಗಾವಣೆ ವಿನಂತಿಯನ್ನು ಸಂಖ್ಯೆ ರಚಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="ProducttransferGRNisalreadycreatedforthisproducttransfernotenumber" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ವರ್ಗಾವಣೆ grn ಈಗಾಗಲೇ ಈ ಉತ್ಪನ್ನ ವರ್ಗಾವಣೆ ಟಿಪ್ಪಣಿ ಸಂಖ್ಯೆ ರಚಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="Invalidproducttransfernotenumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಉತ್ಪನ್ನ ವರ್ಗಾವಣೆ ಟಿಪ್ಪಣಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="SalesOrderAllocation" xml:space="preserve">
    <value>ಮಾರಾಟದ ಆರ್ಡರ್ ಹಂಚಿಕ</value>
  </data>
  <data name="Supplyingbranchwarehousecannotbesameasrequestingbranchwarehouse" xml:space="preserve">
    <value>ಸರಬರಾಜು ಶಾಖೆಯ ಮಳಿಗೆ ಮತ್ತು ಮನವಿ ಶಾಖೆಯ ಮಳಿಗೆ ಒಂದೇ ಆಗಿರಲು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="TotalSalesOrderAmountInLocalCurrency" xml:space="preserve">
    <value>ಒಟ್ಟು ಮಾರಾಟ ದರ ಸ್ಥಳೀಯ ಕರೆನ್ಸಿಯಲ್ಲಿ</value>
  </data>
  <data name="CommissioningDetails" xml:space="preserve">
    <value>ನಿಯುಕ್ತಿ ವಿವರಗಳು</value>
  </data>
  <data name="ExpectedCommissioningDate" xml:space="preserve">
    <value>ನಿರೀಕ್ಷಿತ ನಿಯುಕ್ತಿ ದಿನಾಂಕ</value>
  </data>
  <data name="ServicingDealer" xml:space="preserve">
    <value>ಸರ್ವಿಸಿಂಗ್ ಡೀಲರ್</value>
  </data>
  <data name="ServicingDealerBranch" xml:space="preserve">
    <value>ಸರ್ವಿಸಿಂಗ್ ಡೀಲರ್ ಶಾಖೆ</value>
  </data>
  <data name="CommissionDate" xml:space="preserve">
    <value>ಆಯೋಗ ದಿನಾಂಕ</value>
  </data>
  <data name="WarrantyEndDate" xml:space="preserve">
    <value>ಖಾತರಿ ಅಂತಿಮ ದಿನಾಂಕ</value>
  </data>
  <data name="SalesQuotationArchive" xml:space="preserve">
    <value>ಮಾರಾಟದ ಉದ್ಧರಣ ಆರ್ಕೈವ್</value>
  </data>
  <data name="SpnGITstock" xml:space="preserve">
    <value>ಜಿಐಟಿ ಸ್ಟಾಕ್</value>
  </data>
  <data name="SpnOutofWH" xml:space="preserve">
    <value>ವೇರ್ಹೌಸ್ ಔಟ್</value>
  </data>
  <data name="SpnUnderClearence" xml:space="preserve">
    <value>ಕ್ಲಿಯರೆನ್ಸ್ ಅಂಡರ್</value>
  </data>
  <data name="NewLandingCost" xml:space="preserve">
    <value>ನೂತನ ಲ್ಯಾಂಡಿಂಗ್ ವೆಚ್ಚ</value>
  </data>
  <data name="OldLandingCost" xml:space="preserve">
    <value>ಪ್ರಾಚೀನ ಲ್ಯಾಂಡಿಂಗ್ ವೆಚ್ಚ</value>
  </data>
  <data name="ProductCostUpdation" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ವೆಚ್ಚ ತಿದ್ದುಪಡಿ</value>
  </data>
  <data name="ProductCostUpdationDate" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ವೆಚ್ಚ ತಿದ್ದುಪಡಿ ದಿನಾಂಕ</value>
  </data>
  <data name="ProductCostUpdationNumber" xml:space="preserve">
    <value>ಉತ್ಪನ್ನ ವೆಚ್ಚ ತಿದ್ದುಪಡಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="UpdatedBy" xml:space="preserve">
    <value>ಇವರಿಂದ ನವೀಕರಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="FOCNumber" xml:space="preserve">
    <value>ವೆಚ್ಚ ಸಂಖ್ಯೆಯ ಉಚಿತ</value>
  </data>
  <data name="SalesOrderAmount" xml:space="preserve">
    <value>ಮಾರಾಟದ ಆರ್ಡರ್ ಮೊತ್ತ</value>
  </data>
  <data name="FreeOfCost" xml:space="preserve">
    <value>ಉಚಿತವಾಗಿ</value>
  </data>
  <data name="FOCCommitted" xml:space="preserve">
    <value>ಉಚಿತವಾಗಿ ಕಮಿಟೆಡ್</value>
  </data>
  <data name="FOCIssued" xml:space="preserve">
    <value>ಉಚಿತವಾಗಿ ನೀಡಲಾಗಿದೆ</value>
  </data>
  <data name="CancelledBy" xml:space="preserve">
    <value>ಮೂಲಕ ರದ್ದುಗೊಳಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="POCDate" xml:space="preserve">
    <value>ಖರೀದಿ ಆದೇಶವನ್ನು ರದ್ದು ದಿನಾಂಕ</value>
  </data>
  <data name="POCNumber" xml:space="preserve">
    <value>ಖರೀದಿ ಆದೇಶವನ್ನು ರದ್ದು</value>
  </data>
  <data name="PurchaseOrderAmount" xml:space="preserve">
    <value>ಪರ್ಚೇಸ್ ಆರ್ಡರ್ ಪ್ರಮಾಣ</value>
  </data>
  <data name="CancellationReason" xml:space="preserve">
    <value>ರದ್ದತಿ ಕಾರಣ</value>
  </data>
  <data name="PurchaseOrderCancellation" xml:space="preserve">
    <value>ಪರ್ಚೇಸ್ ಆರ್ಡರ್ ರದ್ದು</value>
  </data>
  <data name="TotalPurchaseOrderValue" xml:space="preserve">
    <value>ಒಟ್ಟು ಪರ್ಚೇಸ್ ಆರ್ಡರ್ ಮೌಲ್ಯ</value>
  </data>
  <data name="NewLandingCostCannotBeZero" xml:space="preserve">
    <value>ಹೊಸ ಲ್ಯಾಂಡಿಂಗ್ ವೆಚ್ಚ ಶೂನ್ಯ ಇರುವಂತಿಲ್ಲ</value>
  </data>
  <data name="NewLandingCostisbeyondacceptablelimit" xml:space="preserve">
    <value>ಹೊಸ ಲ್ಯಾಂಡಿಂಗ್ ವೆಚ್ಚ ಸ್ವೀಕಾರಾರ್ಹ ಮಿತಿಯನ್ನು ಮೀರಿದ್ದಾಗಿದೆ</value>
  </data>
  <data name="InactiveSupplierName" xml:space="preserve">
    <value>ನಿಷ್ಕ್ರಿಯ ಸರಬರಾಜುದಾರ ಹೆಸರು</value>
  </data>
  <data name="InvalidSupplierName" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಸರಬರಾಜುದಾರ ಹೆಸರು</value>
  </data>
  <data name="AddProductCostUpdation" xml:space="preserve">
    <value>ಯಂತ್ರ ವೆಚ್ಚ ತಿದ್ದುಪಡಿ ಸೇರಿಸಿ</value>
  </data>
  <data name="EditProductCostUpdation" xml:space="preserve">
    <value>ಸಂಪಾದಿಸಿ ಯಂತ್ರ ವೆಚ್ಚ ತಿದ್ದುಪಡಿ</value>
  </data>
  <data name="SalesOrderCancellation" xml:space="preserve">
    <value>ಮಾರಾಟದ ಆರ್ಡರ್ ರದ್ದತಿ</value>
  </data>
  <data name="SalesOrderCancellationNumber" xml:space="preserve">
    <value>ಮಾರಾಟದ ಆರ್ಡರ್ ರದ್ದತಿ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="NewLandingCostCannotbeSameAsOldLandingCost" xml:space="preserve">
    <value>ಹೊಸ ಲ್ಯಾಂಡಿಂಗ್ ವೆಚ್ಚ ಓಲ್ಡ್ ಲ್ಯಾಂಡಿಂಗ್ ವೆಚ್ಚ ಒಂದೇ ಆಗಿರುವಂತಿಲ್ಲ</value>
  </data>
  <data name="FOCDueValue" xml:space="preserve">
    <value>ಉಚಿತವಾಗಿ ಕಾರಣ ಪ್ರಮಾಣದ</value>
  </data>
  <data name="AverageLandingCost" xml:space="preserve">
    <value>ಸರಾಸರಿ ಲ್ಯಾಂಡಿಂಗ್ ವೆಚ್ಚ</value>
  </data>
  <data name="TransactionDetail" xml:space="preserve">
    <value>ಟ್ರಾನ್ಸಾಕ್ಷನ್ ವಿವರ</value>
  </data>
  <data name="FOCDate" xml:space="preserve">
    <value>ಉಚಿತ ವೆಚ್ಚ ದಿನಾಂಕ</value>
  </data>
  <data name="IssuedDetails" xml:space="preserve">
    <value>ನೀಡಿದ ವಿವರಗಳು</value>
  </data>
  <data name="InactiveServiceDealer" xml:space="preserve">
    <value>ನಿಷ್ಕ್ರಿಯ ಸೇವೆ ಡೀಲರ್</value>
  </data>
  <data name="InvalidServiceDealer" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ಸೇವೆ ಡೀಲರ್</value>
  </data>
  <data name="Taxdetailsarenotavailableinsupplier" xml:space="preserve">
    <value>ಪೂರೈಕೆದಾರ ತೆರಿಗೆ ವಿವರಗಳು ಲಭ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="FOCDetailsaremandatory" xml:space="preserve">
    <value>ಎಫ್ಓಸಿ ವಿವರಗಳು ಕಡ್ಡಾಯವಾಗಿದೆ</value>
  </data>
  <data name="InvalidCustomer" xml:space="preserve">
    <value>ಅಮಾನ್ಯ ಗ್ರಾಹಕ</value>
  </data>
  <data name="RatedetailsarenotmatchingwithSupplierRatedoyouwanttocontinue" xml:space="preserve">
    <value>ದರ ವಿವರಗಳು ಸರಬರಾಜುದಾರ ದರ ಹೊಂದಾಣಿಕೆ ಇಲ್ಲ, ನೀವು ಮುಂದುವರಿಸಲು ಬಯಸುತ್ತೀರಾ?</value>
  </data>
  <data name="InactiveCustomer" xml:space="preserve">
    <value>ನಿಷ್ಕ್ರಿಯ ಗ್ರಾಹಕ</value>
  </data>
  <data name="InvalidEnquiryNumber" xml:space="preserve">
    <value>ಅಮಾನ್ಯವಾದ ವಿಚಾರಣೆ ಸಂಖ್ಯೆ</value>
  </data>
  <data name="ServiceDealerisLockedDoyouwanttocontinue" xml:space="preserve">
    <value>ಸರ್ವಿಸಿಂಗ್ ಡೀಲರ್ ಲಾಕ್ ಆಗಿದೆ. ನೀವು ಮುಂದುವರಿಸಲು ಬಯಸುತ್ತೀರಾ?</value>
  </data>
  <data name="TotalTransferGRNAmountCannotBeZero" xml:space="preserve">
    <value>ಒಟ್ಟು ವರ್ಗಾವಣೆ ಜಿ ಆರ್ ಎನ್ ಮೊತ್ತ ಶೂನ್ಯ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="NonPurchaseGRN" xml:space="preserve">
    <value>ನಾನ್ ಖರೀದಿ ಜಿ ಆರ್ ಎನ್</value>
  </data>
  <data name="NonPurchaseGRNDate" xml:space="preserve">
    <value>ನಾನ್ ಖರೀದಿ ಜಿ ಆರ್ ಎನ್ ದಿನಾಂಕ</value>
  </data>
  <data name="NonPurchaseGRNNumber" xml:space="preserve">
    <value>ನಾನ್ ಖರೀದಿ ಜಿ ಆರ್ ಎನ್ #</value>
  </data>
  <data name="QuantityCannotBeGreaterThanActiveQuantity" xml:space="preserve">
    <value>ಪ್ರಮಾಣ ಸಕ್ರಿಯ ಪ್ರಮಾಣ ಗಿಂತ ಹೆಚ್ಚಾಗಿರುವುದು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="QuantityCannotBeLesserThanAllocatedQuantity" xml:space="preserve">
    <value>ಪ್ರಮಾಣ ನಿಗದಿ ಮಾಡಿದ ಪ್ರಮಾಣ ಗಿಂತ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Servicerequestcannotbeclosedduetopendingfollowupdetails" xml:space="preserve">
    <value>ಸೇವೆ ವಿನಂತಿಯನ್ನು ಕಾರಣ ವಿವರಗಳು ಅನುಸರಿಸಲು ಬಾಕಿ ಮುಚ್ಚಲಾಗಿದೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="AddNonPurchaseGRN" xml:space="preserve">
    <value>ಸೇರಿಸಿ ನಾನ್-ಖರೀದಿ ಜಿ ಆರ್ ಎನ್</value>
  </data>
  <data name="EditNonPurchaseGRN" xml:space="preserve">
    <value>ಬದಲಾಯಿಸಿ ನಾನ್-ಖರೀದಿ ಜಿ ಆರ್ ಎನ್</value>
  </data>
  <data name="DuplicateDealer" xml:space="preserve">
    <value>ನಕಲು ಡೀಲರ್</value>
  </data>
  <data name="FollowUpDetails" xml:space="preserve">
    <value>ಅನುಚರ ವಿವರಗಳು</value>
  </data>
  <data name="Followupinvitedetails" xml:space="preserve">
    <value>ಅನುಚರ ಆಹ್ವಾನ ವಿವರಗಳು   </value>
  </data>
  <data name="FollowUps" xml:space="preserve">
    <value>ಅಪ್ ಅನುಸರಸಿ</value>
  </data>
  <data name="Totalsalesorderamountcannotbezero" xml:space="preserve">
    <value>ಒಟ್ಟು ಮಾರಾಟ ಆದೇಶ ಮೊತ್ತ ಶೂನ್ಯ ಇರುವಂತಿಲ್ಲ</value>
  </data>
  <data name="serialnumberalreadyexistinparentcompanydouwanttochangetheownership" xml:space="preserve">
    <value>ಸೀರಿಯಲ್ # ಈಗಾಗಲೇ ಪೋಷಕ ಕಂಪನಿ ಅಸ್ತಿತ್ವದಲ್ಲಿದೆ. ನೀವು ಮಾಲೀಕತ್ವವನ್ನು ಬದಲಾಯಿಸಲು ಬಯಸುತ್ತೀರಿ?</value>
  </data>
  <data name="Serialnumberisallocatedcannotbeused" xml:space="preserve">
    <value>ಸೀರಿಯಲ್ # ಹಂಚಿಕೆ ಇದೆ, ಬಳಸಲು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Serialnumberisunderclearencecannotbeused" xml:space="preserve">
    <value>ಸೀರಿಯಲ್ # ತೆರವು ಅಡಿಯಲ್ಲಿ, ಬಳಸಲು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="SerialnumberisunderGITcannotbeused" xml:space="preserve">
    <value>ಸೀರಿಯಲ್ # ಸಾರಿಗೆ ಹಂತದಲ್ಲಿದೆ, ಬಳಸಲು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="CostofSale" xml:space="preserve">
    <value>ಮಾರಾಟಕ್ಕೆ ವೆಚ್ಚ</value>
  </data>
  <data name="ExpenditureAmount" xml:space="preserve">
    <value>ವೆಚ್ಚದ ಪ್ರಮಾಣ</value>
  </data>
  <data name="NetProfitAmount" xml:space="preserve">
    <value>ನಿವ್ವಳ ಲಾಭ ಪ್ರಮಾಣ</value>
  </data>
  <data name="ProfitAmount" xml:space="preserve">
    <value>ಲಾಭ ಪ್ರಮಾಣ</value>
  </data>
  <data name="ProfitandLossReport" xml:space="preserve">
    <value>ಲಾಭ ಮತ್ತು ನಷ್ಟ ವರದಿ</value>
  </data>
  <data name="ModelRateIsNotDefinedInSupplierCanNotCreateAutomaticSalesOrderDoYouWantToContinue" xml:space="preserve">
    <value>ಮಾದರಿ ದರ ಸರಬರಾಜುದಾರ ವ್ಯಾಖ್ಯಾನಿಸಲಾಗಿದೆ ಅಲ್ಲ, ಸ್ವಯಂಚಾಲಿತ ಮಾರಾಟದ ಆರ್ಡರ್ ರಚಿಸಲು ಸಾಧ್ಯವಿಲ್ಲ. ನೀವು ಮುಂದುವರಿಸಲು ಬಯಸುತ್ತೀರಾ?</value>
  </data>
  <data name="PleaseselectSalesOrderNumber" xml:space="preserve">
    <value>ಮಾರಾಟದ ಆದೇಶ ಸಂಖ್ಯೆ ಆಯ್ಕೆಮಾಡಿ</value>
  </data>
  <data name="PurchaseReOredrLevelReport" xml:space="preserve">
    <value>ಖಾತರಿ ಹಕ್ಕು ವರದಿ</value>
  </data>
  <data name="TaxdeductedReport" xml:space="preserve">
    <value>ತೆರಿಗೆ ಟೈಪ್ ವರದಿ</value>
  </data>
  <data name="AMCExpiry" xml:space="preserve">
    <value>ಎಎಂಸಿ ಅಂತ್ಯ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="ApprovedLaborAmt" xml:space="preserve">
    <value>Approved ಕಾರ್ಮಿಕ ಆಮ್ಟ್ (ರೂ.)</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="ApprovedPartsAmt" xml:space="preserve">
    <value>Approved ಭಾಗಗಳು ಆಮ್ಟ್ (ರೂ.)</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="ApprovedSundryAmt" xml:space="preserve">
    <value>Approved ಸಂಡ್ರಿ ಆಮ್ಟ್ (ರೂ.)</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="ApprovedTotalAmt" xml:space="preserve">
    <value>Approved ಒಟ್ಟು ಆಮ್ಟ್ (ರೂ.)</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="CampaignReport" xml:space="preserve">
    <value>ಕ್ಯಾಂಪೇನ್ ವರದಿ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="CampaignReportFrom" xml:space="preserve">
    <value>ಪ್ರಚಾರ ವರದಿ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="CausingPart" xml:space="preserve">
    <value>ಕಾರಣವಾಗುತ್ತದೆ ಭಾಗ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="CausingPartWise" xml:space="preserve">
    <value>ಕಾರಣವಾಗುತ್ತದೆ ಭಾಗ ವೈಸ್</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="CausingPartWiseWarrantyClaimReportFrom" xml:space="preserve">
    <value>ಗೆ ಭಾಗ ವೈಸ್ ಖಾತರಿ ಹಕ್ಕು ವರದಿ ಉಂಟುಮಾಡುವುದು</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="ClaimDtg" xml:space="preserve">
    <value>ಜಿಲ್ಲೆ ಹಕ್ಕು (ಗ್ರಾಂ)</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="ClaimedLaborAmt" xml:space="preserve">
    <value>ಹಕ್ಕು ಕಾರ್ಮಿಕ ಆಮ್ಟ್ (ರೂ.)</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="ClaimedPartsAmt" xml:space="preserve">
    <value>ಹಕ್ಕು ಭಾಗಗಳು ಆಮ್ಟ್ (ರೂ.)</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="ClaimedSundryAmt" xml:space="preserve">
    <value>ಹಕ್ಕು ಸಂಡ್ರಿ ಆಮ್ಟ್ (ರೂ.)</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="ClaimedTotalAmt" xml:space="preserve">
    <value>ಹಕ್ಕು ಒಟ್ಟು ಆಮ್ಟ್ (ರೂ.)</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="ClaimName" xml:space="preserve">
    <value>ಕ್ಲೇಮ್ ಹೆಸರು</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="ClaimTypeWise" xml:space="preserve">
    <value>ಪ್ರಕಾರ ವೈಸ್ ಹಕ್ಕು</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="CustomerType" xml:space="preserve">
    <value>ಗ್ರಾಹಕ ಪ್ರಕಾರ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="DateAnalysisreportFrom" xml:space="preserve">
    <value>ಗೆ ದಿನಾಂಕ ವಿಶ್ಲೇಷಣೆ ಆಡಿಟ್ ವರದಿ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="DealerWiseCostAnalysis" xml:space="preserve">
    <value>ಡೀಲರ್ ವೈಸ್ ವೆಚ್ಚ ವಿಶ್ಲೇಷಣೆ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="DealerWiseCostAnalysisReportFrom" xml:space="preserve">
    <value>ಡೀಲರ್ ವೈಸ್ ವಿಶ್ಲೇಷಣೆ ರಿಪೋರ್ಟ್ ಕಾಸ್ಟ್</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="DealerWiseJobCard" xml:space="preserve">
    <value>ಡೀಲರ್ ವೈಸ್ ಜಾಬ್ ಕಾರ್ಡ್</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="DealerWiseJobCardReportFrom" xml:space="preserve">
    <value>ಡೀಲರ್ ವೈಸ್ ಜಾಬ್ ಕಾರ್ಡ್ ವರದಿಯನ್ನು</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="DefectCodeWise" xml:space="preserve">
    <value>ದೋಷ ಕೋಡ್ ವೈಸ್</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="DefectivePartsDisposalReportFrom" xml:space="preserve">
    <value>ಗೆ ದೋಷಯುಕ್ತ ಭಾಗಗಳು ವಿಲೇವಾರಿ ವರದಿ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="Diffcba" xml:space="preserve">
    <value>ವ್ಯತ್ಯಾಸಗಳ ಸಿ = ಬಿ ಒಂದು</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="Difffed" xml:space="preserve">
    <value>ವ್ಯತ್ಯಾಸಗಳ F = ಇ ಡಿ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="Diffhge" xml:space="preserve">
    <value>ವ್ಯತ್ಯಾಸಗಳ H = ಜಿ ಇ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="Diffigd" xml:space="preserve">
    <value>ವ್ಯತ್ಯಾಸಗಳ (Cl ಹೆಚ್ಚು ಡಿಟಿ-ಡೋರ್) ನಾನು = G-d</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="Diffjga" xml:space="preserve">
    <value>ವ್ಯತ್ಯಾಸಗಳ (Cl ಹೆಚ್ಚು ಡಿಟಿ-DOF) ಜೆ = ಜಿ ಒಂದು</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="DOFa" xml:space="preserve">
    <value>DOF (ಒಂದು)</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="DORLastrepaird" xml:space="preserve">
    <value>ಜೆಸಿ ಕ್ಲೋಸಿಂಗ್ ಜಿಲ್ಲೆ (ಇ)</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="EnviornmentName" xml:space="preserve">
    <value>ಪರಿಸರ ಹೆಸರು</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="EnvironmentType" xml:space="preserve">
    <value>ಪರಿಸರ ಪ್ರಕಾರ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="EnvironmentTypeWise" xml:space="preserve">
    <value>ಪರಿಸರ ಪ್ರಕಾರ ವೈಸ್</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="FailedPartWise" xml:space="preserve">
    <value>ವಿಫಲವಾಗಿದೆ ಭಾಗ ವೈಸ್</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="FailureHours" xml:space="preserve">
    <value>Failue ಅವರ್ಸ್</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="ForCustkda" xml:space="preserve">
    <value>Cust (ಡೋರ್-DOF) ಕೆ = ಡಿ ಎ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="FunctionName" xml:space="preserve">
    <value>ಫಂಕ್ಷನ್ ಹೆಸರು</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="GatePassReportDailyFrom" xml:space="preserve">
    <value>ಗೇಟ್ ಪಾಸ್ ವರದಿ ಪ್ರತಿದಿನ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="HOR" xml:space="preserve">
    <value>HOR</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="InternalInvoiceReportFrom" xml:space="preserve">
    <value>ಆಂತರಿಕ ಸರಕುಪಟ್ಟಿ ವರದಿ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="InternalInvoiceReturnReportFrom" xml:space="preserve">
    <value>ಆಂತರಿಕ ಸರಕುಪಟ್ಟಿ ರಿಟರ್ನ್ ವರದಿ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="JCClosingDte" xml:space="preserve">
    <value>ಜೆಸಿ ಕ್ಲೋಸಿಂಗ್ ಜಿಲ್ಲೆ (ಇ)</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="JCOpeningDtb" xml:space="preserve">
    <value>ಜೆಸಿ ತೆರೆಯುತ್ತಿದೆ ಜಿಲ್ಲೆ (ಬಿ)</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="JobCardCompleteDate" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ಕಂಪ್ಲೀಟ್ ದಿನಾಂಕ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="JobCardPartsReturnFrom" xml:space="preserve">
    <value>ಜಾಬ್ ಕಾರ್ಡ್ ಭಾಗಗಳು ಹಿಂದಿರುಗಿ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="JobCardReportFrom" xml:space="preserve">
    <value>ಕೆಲಸ ಕಾರ್ಡ್ ರಿಪೋರ್ಟ್</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="MachineLocation" xml:space="preserve">
    <value>ಯಂತ್ರ ಸ್ಥಳ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="MachineWarrantyExpiry" xml:space="preserve">
    <value>ಯಂತ್ರ ಖಾತರಿ ಅಂತ್ಯ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="MachineWarrantyExpiryReport" xml:space="preserve">
    <value>ಯಂತ್ರ ಖಾತರಿ ಮುಗಿಯುವ ವರದಿ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="PartNoisinactive" xml:space="preserve">
    <value>ಭಾಗ ಸಂಖ್ಯೆ ಸಕ್ರಿಯ ಹೊಂದಿದೆ.</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="PartsValue" xml:space="preserve">
    <value>ಪಾರ್ಟ್ಸ್ ಮೌಲ್ಯ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="PromissedReturnDate" xml:space="preserve">
    <value>Promissed ಹಿಂದಿರುಗುವ ದಿನಾಂಕವನ್ನು</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="ReportPreview" xml:space="preserve">
    <value>ವರದಿ ಮುನ್ನೋಟ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="ResourceName" xml:space="preserve">
    <value>ಸಂಪನ್ಮೂಲ ಹೆಸರು</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="ServiceAuditReportDate" xml:space="preserve">
    <value>ಸೇವೆ ಆಡಿಟ್ ವರದಿ ದಿನಾಂಕ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="ServiceInvoiceDate" xml:space="preserve">
    <value>ಸೇವೆ ಸರಕುಪಟ್ಟಿ ದಿನಾಂಕ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="ServiceInvoiceReportFrom" xml:space="preserve">
    <value>ಸೇವೆ ಸರಕುಪಟ್ಟಿ ವರದಿಯನ್ನು</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="ServiceInvoiceReturnDate" xml:space="preserve">
    <value>ಸೇವೆ ಸರಕುಪಟ್ಟಿ ಹಿಂದಿರುಗುವ ದಿನಾಂಕವನ್ನು</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="ServiceInvoiceReturnNumber" xml:space="preserve">
    <value>ಸಂಖ್ಯೆ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="ServiceInvoiceReturnReportFrom" xml:space="preserve">
    <value>ಸೇವೆ ಸರಕುಪಟ್ಟಿ ರಿಟರ್ನ್ ವರದಿಯನ್ನು</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="WarrantyClaimReportFrom" xml:space="preserve">
    <value>ಖಾತರಿ ಹಕ್ಕು ವರದಿ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="WarrantyClaimTypewiseReport" xml:space="preserve">
    <value>ಖಾತರಿ ಹಕ್ಕು ಪ್ರಕಾರ ಬುದ್ಧಿವಂತ ವರದಿ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="WarrantyClaimTypewiseReportFrom" xml:space="preserve">
    <value>ಖಾತರಿ ಹಕ್ಕು ಪ್ರಕಾರ ಬುದ್ಧಿವಂತ ವರದಿ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="WarrantyExpiry" xml:space="preserve">
    <value>ಖಾತರಿ ಅಂತ್ಯ</value>
    <comment>Added for Reports 29 May 14</comment>
  </data>
  <data name="Discountamountcannotbeequaltototalamount" xml:space="preserve">
    <value>ರಿಯಾಯಿತಿ ಮೊತ್ತವನ್ನು ಒಟ್ಟು ಮೊತ್ತಕ್ಕೆ ಸಮಾನವಾಗಿರಬೇಕು ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="ServiceEngineerWise" xml:space="preserve">
    <value>ಸೇವೆ ಎಂಜಿನಿಯರ್ ವೈಸ್ JC</value>
  </data>
  <data name="ServiceEngineerWiseReport" xml:space="preserve">
    <value>ಸೇವೆ ಎಂಜಿನಿಯರ್ ವೈಸ್ JC ವರದಿ</value>
  </data>
  <data name="ServiceEngineerWiseReportFrom" xml:space="preserve">
    <value>ಸೇವೆ ಎಂಜಿನಿಯರ್ ವೈಸ್  JC ವರದಿಯನ್ನು</value>
  </data>
  <data name="ExpectedDeliveryDatecannotbelessthanpurchaseorderdate" xml:space="preserve">
    <value>ನಿರೀಕ್ಷಿತ ಡೆಲಿವರಿ ದಿನಾಂಕ ಖರೀದಿ ಆದೇಶದ ದಿನಾಂಕ ಕಡಿಮೆ ಸಾಧ್ಯವಿಲ್ಲ</value>
  </data>
  <data name="Bill" xml:space="preserve">
    <value>ಬಿಲ್ </value>
  </data>
  <data name="MachineAMCExpiry" xml:space="preserve">
    <value>ಯಂತ್ರ ಎಎಂಸಿ ಅಂತ್ಯ</value>
  </data>
  <data name="MachineWarrantyAMCExpiry" xml:space="preserve">
    <value>ಯಂತ್ರ ಖಾತರಿ / ಎಎಂಸಿ ಅಂತ್ಯ ವರದಿ</value>
  </data>
  <data name="Stockturnoverratio" xml:space="preserve">
    <value>ಅನುಪಾತ ಮೇಲೆ ಸ್ಟಾಕ್ ಟರ್ನ್</value>
  </data>
  <data name="DetailReport" xml:space="preserve">
    <value>ವಿವರ ವರದಿ</value>
  </data>
  <data name="ItemWiseReceiptsOrIssuesReport" xml:space="preserve">
    <value>ಐಟಂ ವೈಸ್ ರಸೀದಿಗಳನ್ನು ಅಥವಾ ಇಷ್ಯೂಸ್ ರಿಪೋರ್ಟ್</value>
  </data>
  <data name="SummaryReport" xml:space="preserve">
    <value>ಸಾರಾಂಶ ವರದಿ</value>
  </data>
  <data name="IssueQuantity" xml:space="preserve">
    <value>ಸಂಚಿಕೆ ಪ್ರಮಾಣ</value>
  </data>
  <data name="PleaseSelectAtleastOneTransactionToGenerateReport" xml:space="preserve">
    <value>ವರದಿ ಸೃಷ್ಟಿಸಲು ಕನಿಷ್ಠ ಒಂದು ಟ್ರಾನ್ಸಾಕ್ಷನ್ ಆಯ್ಕೆ ಮಾಡಿ</value>
  </data>
  <data name="ReceiptQuantity" xml:space="preserve">
    <value>ರಸೀತಿ ಪ್ರಮಾಣ</value>
  </data>
  <data name="Receipts" xml:space="preserve">
    <value>ರಸೀದಿಗಳು</value>
  </data>
  <data name="TransactionDate" xml:space="preserve">
    <value>ವಹಿವಾಟಿನ ದಿನಾಂಕ</value>
  </data>
  <data name="TransactionName" xml:space="preserve">
    <value>ಟ್ರಾನ್ಸಾಕ್ಷನ್ ಹೆಸರು</value>
  </data>
  <data name="OpeningBalance" xml:space="preserve">
    <value>ಆರಂಭಿಕ ಬ್ಯಾಲೆನ್ಸ್</value>
  </data>
  <data name="Particulars" xml:space="preserve">
    <value>ವಿವರಗಳು</value>
  </data>
  <data name="CancelledQty" xml:space="preserve">
    <value>ರದ್ದುಗೊಳಿಸಲಾಗಿದೆ ಪ್ರಮಾಣ (Cq)</value>
  </data>
  <data name="InvoicedQty" xml:space="preserve">
    <value>ನೆಟ್ ಆದೇಶ</value>
  </data>
  <data name="NetOrderedQty" xml:space="preserve">
    <value>ನೆಟ್ ಆದೇಶ ಪ್ರಮಾಣ (ಎನ್ ಕ್ಯೂ = ಓ ಕ್ಯೂ - ಸಿ ಕ್ಯೂ)</value>
  </data>
  <data name="OrderQtyRpt" xml:space="preserve">
    <value>ಆರ್ಡರ್ ಪ್ರಮಾಣ (OQ)</value>
  </data>
  <data name="PendingQty" xml:space="preserve">
    <value>ಗೆಲುವು ಪ್ರಮಾಣ</value>
  </data>
  <data name="GITQuantity" xml:space="preserve">
    <value>ಗೆಲುವು ಪ್ರಮಾಣ</value>
  </data>
  <data name="BackOrdCancelQty" xml:space="preserve">
    <value>ಬ್ಯಾಕ್ ಆರ್ಡರ್ ಪ್ರಮಾಣ ರದ್ದು</value>
  </data>
  <data name="BackOrderStatusReport" xml:space="preserve">
    <value>ಬ್ಯಾಕ್ ಆರ್ಡರ್ ಪ್ರಮಾಣ ವರದಿ</value>
  </data>
  <data name="GITStock" xml:space="preserve">
    <value>ಜಿಐಟಿ ಸ್ಟಾಕ್</value>
  </data>
  <data name="InvoiceQty" xml:space="preserve">
    <value>ಸರಕುಪಟ್ಟಿ ಪ್ರಮಾಣ</value>
  </data>
  <data name="OrderQty" xml:space="preserve">
    <value>ಆರ್ಡರ್ ಪ್ರಮಾಣ (OQ)</value>
  </data>
  <data name="Transaction" xml:space="preserve">
    <value>ಟ್ರಾನ್ಸಾಕ್ಷನ್</value>
  </data>
  <data name="ContractDetails" xml:space="preserve">
    <value>Ka Contract Details</value>
  </data>
  <data name="ABCValuesTotalshouldbe100" xml:space="preserve">
    <value>ka A, B and C values total should be 100</value>
  </data>
  <data name="RejectedQuantity" xml:space="preserve">
    <value>KA Rejected Quantity</value>
  </data>
  <data name="EmailTemplateDetailLocale" xml:space="preserve">
    <value>KA_Email Template Detail Locale</value>
  </data>
  <data name="partnumORnostock" xml:space="preserve">
    <value>ka part # / not in stoc</value>
  </data>
  <data name="TimeZone" xml:space="preserve">
    <value>Time Zone</value>
  </data>
  <data name="DeviationAdjusted" xml:space="preserve">
    <value>ವಿಚಲನ ಸರಿಹೊಂದಿಸಲಾಗಿದೆ</value>
  </data>
  <data name="ListOfParts" xml:space="preserve">
    <value>ka_List of Parts has Split Quantity (Customer Paid or Warranty Claim or Internal Invoice)</value>
  </data>
  <data name="MnualQuantity" xml:space="preserve">
    <value>ka_Part has Split Quantity, Kinldy do Manual Quantity Split Update in Work Order, Used Quantity will be Updated as 0(zero)</value>
  </data>
  <data name="PartsHasSplitQuantity" xml:space="preserve">
    <value>ka_Parts has Split Quanity</value>
  </data>
  <data name="ServiceInvoiceQty" xml:space="preserve">
    <value>ka_Service Invoice Qty</value>
  </data>
  <data name="InternalInvoiceQty" xml:space="preserve">
    <value>ka_Internal Invoice Qty</value>
  </data>
  <data name="WarrantyClaimQty" xml:space="preserve">
    <value>ka_Warranty Claim Qty</value>
  </data>
  <data name="JobCardPartsReturn1" xml:space="preserve">
    <value>ka_WorkOrderPartsReturn</value>
  </data>
  <data name="MnualQty1" xml:space="preserve">
    <value>ka_Kinldy do Manual Quantity Split Update in Work Order, Used Quantity will be Updated as 0(zero)</value>
  </data>
  <data name="WorkOrderDate" xml:space="preserve">
    <value>ka_WorkOrderDate</value>
  </data>
</root>