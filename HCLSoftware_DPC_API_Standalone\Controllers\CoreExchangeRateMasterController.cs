﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreExchangeRateMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreExchangeRateMasterController : ApiController
    {
        #region ::: Load ExchangeMaster Landing Grid /Mihtun:::
        /// <summary>
        /// To Load Service type Landing Grid
        /// </summary>  
        [Route("api/CoreExchangeRateMaster/SelectAllExchangeRate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectAllExchangeRate([FromBody] SelectAllExchangeRateList SelectAllExchangeRateObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreExchangeRateMasterServices.SelectAllExchangeRate(SelectAllExchangeRateObj, Conn, LogException, sidx, sord, page, rows, _search, advnce, filters, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: Save /Mithun:::
        /// <summary>
        /// To Insert and Update State
        /// </summary>
        [Route("api/CoreExchangeRateMaster/Insert")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Insert([FromBody] InsertExchangeRateList InsertObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreExchangeRateMasterServices.Insert(InsertObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CheckDuplicateRecord /Mithun:::
        /// <summary>
        /// To Check State already exists 
        /// </summary>
        [Route("api/CoreExchangeRateMaster/CheckDuplicateRecord")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckDuplicateRecord([FromBody] CheckDuplicateRecordList CheckDuplicateRecordObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreExchangeRateMasterServices.CheckDuplicateRecord(CheckDuplicateRecordObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region::: Exprot /Mithun:::

        [Route("api/CoreExchangeRateMaster/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] SelectAllExchangeRateList ExportObj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportObj.sidx;
            string sord = ExportObj.sord;
            string filters = ExportObj.filters;
            string Query = ExportObj.Query;

            try
            {


                Object Response = await CoreExchangeRateMasterServices.Export(ExportObj, connstring, LogException, filters, Query, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }

        #endregion

    }
}
