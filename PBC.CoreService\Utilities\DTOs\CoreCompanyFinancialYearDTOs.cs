using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;

namespace PBC.CoreService.Utilities.DTOs
{
    #region Request DTOs
    /// <summary>
    /// Request object for selecting company financial years
    /// </summary>
    public class SelectCompanyFinancialYearList
    {
        /// <summary>
        /// Company ID to filter financial years
        /// </summary>
        [Required]
        public int Company_ID { get; set; }

        /// <summary>
        /// User language ID for localization
        /// </summary>
        public int UserLanguageID { get; set; }

        /// <summary>
        /// General language ID for localization
        /// </summary>
        public int GeneralLanguageID { get; set; }
    }

    /// <summary>
    /// Request object for selecting companies
    /// </summary>
    public class SelectCompanyList
    {
        /// <summary>
        /// Company ID filter
        /// </summary>
        [Required]
        public int Company_ID { get; set; }

        /// <summary>
        /// User language ID for localization
        /// </summary>
        public int UserLanguageID { get; set; }

        /// <summary>
        /// General language ID for localization
        /// </summary>
        public int GeneralLanguageID { get; set; }
    }

    /// <summary>
    /// Request object for saving company financial year data
    /// </summary>
    public class SaveCoreCompanyFinancialList
    {
        /// <summary>
        /// JSON string containing financial year data
        /// </summary>
        [Required]
        [Description("JSON string with financial year rows data")]
        public string Data { get; set; } = string.Empty;

        /// <summary>
        /// Company ID
        /// </summary>
        [Required]
        public int Company_ID { get; set; }

        /// <summary>
        /// User ID performing the operation
        /// </summary>
        [Required]
        public int User_ID { get; set; }

        /// <summary>
        /// Menu ID for audit purposes
        /// </summary>
        [Required]
        public int MenuID { get; set; }

        /// <summary>
        /// Branch ID
        /// </summary>
        [Required]
        public int Branch { get; set; }

        /// <summary>
        /// Timestamp when user logged in
        /// </summary>
        [Required]
        public DateTime LoggedINDateTime { get; set; }
    }

    /// <summary>
    /// Request object for deleting company financial years
    /// </summary>
    public class DeleteCoreCompanyFinancialList
    {
        /// <summary>
        /// JSON string containing IDs to delete
        /// </summary>
        [Required]
        [Description("JSON string with rows containing IDs to delete")]
        public string key { get; set; } = string.Empty;

        /// <summary>
        /// Language code
        /// </summary>
        public string Lang { get; set; } = string.Empty;

        /// <summary>
        /// User culture for localization
        /// </summary>
        public string UserCulture { get; set; } = string.Empty;

        /// <summary>
        /// Company ID
        /// </summary>
        [Required]
        public int Company_ID { get; set; }

        /// <summary>
        /// User ID performing the operation
        /// </summary>
        [Required]
        public int User_ID { get; set; }

        /// <summary>
        /// Menu ID for audit purposes
        /// </summary>
        [Required]
        public int MenuID { get; set; }

        /// <summary>
        /// Branch ID
        /// </summary>
        [Required]
        public int Branch { get; set; }

        /// <summary>
        /// Timestamp when user logged in
        /// </summary>
        [Required]
        public DateTime LoggedINDateTime { get; set; }
    }

    /// <summary>
    /// Request object for checking if financial year exists
    /// </summary>
    public class CheckFinancialYearList
    {
        /// <summary>
        /// Company ID
        /// </summary>
        [Required]
        public int Company_ID { get; set; }

        /// <summary>
        /// Financial year to check
        /// </summary>
        [Required]
        public int financialYear { get; set; }

        /// <summary>
        /// Primary key for update scenarios
        /// </summary>
        public int primaryKey { get; set; }
    }

    /// <summary>
    /// Request object for getting all saved financial years
    /// </summary>
    public class GetAllSavedFinancialYearsList
    {
        /// <summary>
        /// Company ID to filter financial years
        /// </summary>
        [Required]
        public int Company_ID { get; set; }
    }
    #endregion

    #region Entity DTOs
    /// <summary>
    /// Company financial year entity
    /// </summary>
    public class GNM_CompanyFinancialYear
    {
        public int Company_FinancialYear_ID { get; set; }
        public int? Company_ID { get; set; }
        public int Company_FinancialYear { get; set; }
        public DateTime Company_FinancialYear_FromDate { get; set; }
        public DateTime Company_FinancialYear_ToDate { get; set; }
        public virtual GNM_Company? GNM_Company { get; set; }
    }



    /// <summary>
    /// Object entity for prefix/suffix operations
    /// </summary>
    public class GNM_Object
    {
        public int Object_ID { get; set; }
        public string Object_Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response data for company financial year grid
    /// </summary>
    public class CompanyFinancialYearData
    {
        public string edit { get; set; } = string.Empty;
        public string delete { get; set; } = string.Empty;
        public int Company_FinancialYear_ID { get; set; }
        public int Company_FinancialYear { get; set; }
        public string Company_FinancialYear_FromDate { get; set; } = string.Empty;
        public string Company_FinancialYear_ToDate { get; set; } = string.Empty;
    }
    #endregion

    #region HTTP Client DTOs for PBC.UtilityService calls
    /// <summary>
    /// Request for logging to PBC.UtilityService
    /// </summary>
    public class LogRequest
    {
        public int ExId { get; set; }
        public string ExMessage { get; set; } = string.Empty;
        public string ExDetails { get; set; } = string.Empty;
        public string ExStackTrace { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request for getting resource string from PBC.UtilityService
    /// </summary>
    public class ResourceStringRequest
    {
        public string CultureValue { get; set; } = string.Empty;
        public string ResourceKey { get; set; } = string.Empty;
    }

    /// <summary>
    /// Generic response from PBC.UtilityService
    /// </summary>
    /// <typeparam name="T">Type of data returned</typeparam>
    public class UtilityServiceResponse<T>
    {
        public bool Success { get; set; }
        public T? Data { get; set; }
        public string? ErrorMessage { get; set; }
    }
    #endregion
}
