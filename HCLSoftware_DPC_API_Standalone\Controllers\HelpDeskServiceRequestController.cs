﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.HelpDeskServiceRequestServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;
namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class HelpDeskServiceRequestController : ApiController
    {

        //checked
        #region ::: load Masters /Mithun:::
        /// <summary>
        /// To load master drop downs
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskServiceRequest/loadMasters")]
        [HttpPost]
        [JwtTokenValidationFilter]

        public IHttpActionResult loadMasters([FromBody] loadMastersList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.loadMasters(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        // checked
        #region ::: load IssueSubArea /Mithun:::
        /// <summary>
        /// To load IssueSubArea drop down
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskServiceRequest/loadIssueSubArea")]
        [HttpPost]
        [JwtTokenValidationFilter]

        public IHttpActionResult loadIssueSubArea([FromBody] loadIssueSubAreaList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.loadIssueSubArea(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        //checked
        #region ::: Get Party Dtails based on PartyName:::
        /// <summary>
        /// To get Customer details
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskServiceRequest/GetPartyDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetPartyDetails([FromBody] GetPartyDetailsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.GetPartyDetails(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        // checked
        #region ::: Get Party Detail Grid Based on PartyName:::
        /// <summary>
        ///To select menus of respective module
        /// </summary>
        /// <returns>...</returns>

        [Route("api/HelpDeskServiceRequest/SelectPartyDetailGrid")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartyDetailGrid([FromBody] SelectPartyDetailGridEList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskServiceRequestServices.SelectPartyDetailGrid(Obj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);
        }
        #endregion


        //checked
        #region ::: Get Party Details by ID:::
        /// <summary>
        /// To get Customer details
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskServiceRequest/GetPartyDetailsbyID")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetPartyDetailsbyID([FromBody] GetPartyDetailsbyIDEList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.GetPartyDetailsbyID(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Get Product Warranty on change of serial number:::
        /// <summary>
        /// To get product details
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskServiceRequest/getProductUniqueNumber")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult getProductUniqueNumber([FromBody] getProductUniqueNumberFList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.getProductUniqueNumber(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion

        #region ::: Get Product Warranty on change of serial number:::
        /// <summary>
        /// To get product details
        /// </summary>
        /// <returns>...</returns>

        [Route("api/HelpDeskServiceRequest/getProductWarranty")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult getProductWarranty([FromBody] getProductWarrantyList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.getProductWarranty(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Get Product Warranty on change of reading:::
        /// <summary>
        /// To get product warranty
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskServiceRequest/ValidateReading")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ValidateReading([FromBody] ValidateReadingList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.ValidateReading(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: ValidateSerial Number::::
        /// <summary>
        /// ValidateSerial Number
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskServiceRequest/ValidateSerialNumber")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ValidateSerialNumber([FromBody] ValidateSerialNumberList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.ValidateSerialNumber(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        //checked
        #region ::: Get Brand Product Type based on ModelID:::
        /// <summary>
        /// To  Get Brand Product Type
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskServiceRequest/getBrandProductType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult getBrandProductType([FromBody] getBrandProductTypecList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.getBrandProductType(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: GetSerialNumberForModelforDealer :::
        /// <summary>
        /// GetSerialNumberForModelforDealer
        /// </summary>
        [Route("api/HelpDeskServiceRequest/GetSerialNumberForModelforDealer")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetSerialNumberForModelforDealer([FromBody] GetSerialNumberForModelforDealerList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.GetSerialNumberForModelforDealer(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: Get Customer Details by Email :::
        /// <summary>
        /// To get CustomerDetails
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskServiceRequest/getCustomerDetailsByEmail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult getCustomerDetailsByEmail([FromBody] getCustomerDetailsByEmailCList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.getCustomerDetailsByEmail(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: Party Contact Person Details Save:::
        /// <summary>
        /// Party Contact Person Details
        /// </summary> 
        [Route("api/HelpDeskServiceRequest/ContactPersonMasterSave")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ContactPersonMasterSave([FromBody] ContactPersonMasterSaveList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.ContactPersonMasterSave(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: Get Product Details based on PartyID:::
        /// <summary>
        /// To get product details
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskServiceRequest/GetAllProductDetailsForDealer")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetAllProductDetailsForDealer([FromBody] GetAllProductDetailsForDealerList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.GetAllProductDetailsForDealer(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: Add Model :::
        /// <summary>
        /// Add model
        /// </summary>
        /// <param name="BrandID"></param>
        /// <param name="ProductTypeID"></param>
        /// <param name="ModelName"></param>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/AddModel")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult AddModel([FromBody] AddModelList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.AddModel(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: PartyMasterSave:::
        /// <summary>
        /// to save the Party Master
        /// </summary>   
        [Route("api/HelpDeskServiceRequest/PartyMasterSave")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PartyMasterSave([FromBody] HelpDeskPartyMasterSaveList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.PartyMasterSave(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        // checked
        #region ::: Get Actions:::
        /// <summary>
        /// To get Actions
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskServiceRequest/GetActions")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetActions([FromBody] GetActionsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.GetActions(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::Get Roles For Actions:::
        /// <summary>
        /// To get Roles For Actions
        /// </summary>
        /// <returns>...</returns>

        [Route("api/HelpDeskServiceRequest/GetRolesForActions")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetRolesForActions([FromBody] GetRolesForActionsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.GetRolesForActions(Obj, Conn, LogException, HelpDesk);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: Get work flow case progress [GetMovementofWorkFlow()]:::
        /// <summary>
        /// To get work flow case progress
        /// </summary>
        /// <returns>...</returns>

        [Route("api/HelpDeskServiceRequest/GetMovementofWorkFlow")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetMovementofWorkFlow([FromBody] GetMovementofWorkFlowList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskServiceRequestServices.GetMovementofWorkFlow(Obj, Conn, LogException, HelpDesk, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);
        }

        #endregion




        #region ::: Get work flow case progress [GetMovementofWorkFlow()]:::
        /// <summary>
        /// To get work flow case progress
        /// </summary>
        /// <returns>...</returns>

        [Route("api/HelpDeskServiceRequest/GetMovementofWorkFlowforAll")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetMovementofWorkFlowforAll([FromBody] GetMovementofWorkFlowforAllList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskServiceRequestServices.GetMovementofWorkFlowforAll(Obj, Conn, LogException, HelpDesk, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);
        }

        #endregion


        // checked
        #region :::SelectNotesDetails:::
        /// <summary>
        /// SelectNotesDetails
        /// </summary>
        /// <returns>...</returns>

        [Route("api/HelpDeskServiceRequest/SelectNotesDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectNotesDetails([FromBody] SelectNotesDetailsList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskServiceRequestServices.SelectNotesDetails(Obj, Conn, LogException, sidx, sord, page, rows, _search, filters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);
        }

        #endregion

        #region ::: To Check Permissions:::
        /// <summary>
        /// To unlock the record
        /// </summary>
        /// <returns>...</returns>

        [Route("api/HelpDeskServiceRequest/CheckAddPermissions")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckAddPermissions([FromBody] CheckAddPermissionsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.CheckAddPermissions(Obj, Conn, LogException, HelpDesk);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: Save Notes Details :::
        /// <summary>
        /// To Insert and Update Notes Details
        /// </summary>

        [Route("api/HelpDeskServiceRequest/SaveNotesDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveNotesDetails([FromBody] SaveNotesDetailsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.SaveNotesDetails(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::SelectRelatedIssues:::
        /// <summary>
        /// SelectRelatedIssues
        /// </summary>
        /// <returns>...</returns>

        [Route("api/HelpDeskServiceRequest/SelectRelatedIssues")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectRelatedIssues([FromBody] SelectRelatedIssuesList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskServiceRequestServices.SelectRelatedIssues(Obj, Conn, LogException, HelpDesk, sidx, sord, page, rows, _search, filters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);
        }

        #endregion


        #region ::: Delete Notes :::
        /// <summary>
        /// To Delete Notes
        /// </summary>

        [Route("api/HelpDeskServiceRequest/DeleteNotes")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteNotes([FromBody] HelpDeskDeleteNotesList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.DeleteNotes(Obj, Conn);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        // done
        #region ::: Sel HD Serv Request PartsDetails:::
        /// <summary>
        /// To Select HD Serv Request PartsDetails
        /// </summary>
        /// <returns>...</returns>

        [Route("api/HelpDeskServiceRequest/SelHDPartsDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelHDPartsDetails([FromBody] SelHDPartsDetailsList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskServiceRequestServices.SelHDPartsDetails(Obj, Conn, LogException, sidx, sord, page, rows, _search, filters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);
        }

        #endregion



        // done
        #region ::: Sel HD Serv Request Product Details:::
        /// <summary>
        /// To Select HD Serv Request Product Details
        /// </summary>
        /// <returns>...</returns>

        [Route("api/HelpDeskServiceRequest/SelHDProductDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelHDProductDetails([FromBody] SelHDProductDetailsList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskServiceRequestServices.SelHDProductDetails(Obj, Conn, LogException, sidx, sord, page, rows, _search, filters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);
        }
        #endregion


        // checked
        #region ::: Sel HD Serv Request Product Details:::
        /// <summary>
        /// To Select HD Serv Request Product Details
        /// </summary>
        /// <returns>...</returns>

        [Route("api/HelpDeskServiceRequest/SelTMLPartsList")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelTMLPartsList([FromBody] SelTMLPartsListList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskServiceRequestServices.SelTMLPartsList(Obj, Conn, LogException, sidx, sord, page, rows, _search, filters, advnce, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: ExportPartsList :::
        [Route("api/HelpDeskServiceRequest/ExportPartsList")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> ExportPartsList([FromBody] SelHDPartsDetailsList ExportObj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportObj.sidx;

            string sord = ExportObj.sord;

            string filters = ExportObj.filter;


            string advnceFilters = ExportObj.advanceFilter;



            try
            {


                object Response = await HelpDeskServiceRequestServices.ExportPartsList(ExportObj, connstring, LogException, filters, advnceFilters, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion

        #region ::: ExportPartsList :::
        [Route("api/HelpDeskServiceRequest/ExportTMLPartsList")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> ExportTMLPartsList([FromBody] SelTMLPartsListList ExportObj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportObj.sidx;

            string sord = ExportObj.sord;

            string filters = ExportObj.filter;


            string advnceFilters = ExportObj.advanceFilter;



            try
            {


                object Response = await HelpDeskServiceRequestServices.ExportTMLPartsList(ExportObj, connstring, LogException, filters, advnceFilters, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion


        #region ::: ExportProductDetails :::
        [Route("api/HelpDeskServiceRequest/ExportProductDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> ExportProductDetails([FromBody] ExportProductDetailsList ExportObj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportObj.sidx;

            string sord = ExportObj.sord;

            string filters = ExportObj.filter;


            string advnceFilters = ExportObj.advanceFilter;



            try
            {


                object Response = await HelpDeskServiceRequestServices.ExportProductDetails(ExportObj, connstring, LogException, filters, advnceFilters, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion



        #region ::: SavePartsList ::::
        /// <summary>
        /// SavePartsList
        /// <returns></returns>

        [Route("api/HelpDeskServiceRequest/SavePartsList")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SavePartsList([FromBody] HelpDeskSavePartsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.SavePartsList(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: SaveProductDetails ::::
        /// <summary>
        /// SaveProductDetails
        /// </summary>
        /// <returns></returns>

        [Route("api/HelpDeskServiceRequest/SaveProductDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveProductDetails([FromBody] SaveProductDetailsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.SaveProductDetails(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Delete Parts List :::
        /// <summary>
        /// To Delete PartsList
        /// </summary>

        [Route("api/HelpDeskServiceRequest/DeletePartsList")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeletePartsList([FromBody] HelpDeskDeletePartsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.DeletePartsList(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: Delete FollowUps :::
        /// <summary>
        /// To Delete TML PartsList
        /// </summary>
        [Route("api/HelpDeskServiceRequest/DeleteTMLPartsList")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteTMLPartsList([FromBody] HelpDeskDeleteTMLPartsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.DeleteTMLPartsList(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: SelectQuestionarieDropDowns :::
        /// <summary>
        /// SelectQuestionarieDropDowns
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/SelectQuestionarieDropDowns")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectQuestionarieDropDowns([FromBody] SelectQuestionarieDropDownsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.SelectQuestionarieDropDowns(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        //checked
        #region :::GetScheduledDropins :::
        /// <summary>
        /// GetScheduledDropins
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/GetScheduledDropins")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetScheduledDropins([FromBody] GetScheduledDropinsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.GetScheduledDropins(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        // checked
        #region :::SelectAllDropdownData :::
        /// <summary>
        /// SelectAllDropdownData
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/SelectAllDropdownData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectAllDropdownData([FromBody] SelectAllDropdownDataList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.SelectAllDropdownData(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region :::SelectFieldSearchName :::
        /// <summary>
        /// SelectFieldSearchName
        /// </summary>
        /// <returns></returns>

        [Route("api/HelpDeskServiceRequest/SelectFieldSearchName")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectFieldSearchName([FromBody] SelectFieldSearchNameList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskServiceRequestServices.SelectFieldSearchName(Obj, Conn, LogException, sidx, sord, page, rows, _search, filters, advnce, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);
        }
        #endregion



        #region :::SelFollowUpParty :::
        /// <summary>
        /// SelFollowUpParty
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/SelFollowUpParty")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelFollowUpParty([FromBody] SelFollowUpPartyList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.SelFollowUpParty(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region :::GetFollowUpEmployeeCustomer :::
        /// <summary>
        /// GetFollowUpEmployeeCustomer
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/GetFollowUpEmployeeCustomer")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetFollowUpEmployeeCustomer([FromBody] GetFollowUpEmployeeCustomerList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.GetFollowUpEmployeeCustomer(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion





        //checked
        #region :::SelHDFollowUpDetails :::
        /// <summary>
        /// SelHDFollowUpDetails
        /// </summary>
        /// <returns></returns>

        [Route("api/HelpDeskServiceRequest/SelHDFollowUpDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelHDFollowUpDetails([FromBody] SelHDFollowUpDetailsList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskServiceRequestServices.SelHDFollowUpDetails(Obj, Conn, LogException, sidx, sord, page, rows, _search, filters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);
        }
        #endregion





        #region ::: SelHDFollowUpInviteDetails:::
        /// <summary>
        /// SelHDFollowUpInviteDetails
        /// <returns></returns>

        [Route("api/HelpDeskServiceRequest/SelHDFollowUpInviteDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelHDFollowUpInviteDetails([FromBody] SelHDFollowUpInviteDetailsList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskServiceRequestServices.SelHDFollowUpInviteDetails(Obj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);
        }
        #endregion

        #region ::: ExportFollowUpDetails :::
        [Route("api/HelpDeskServiceRequest/ExportFollowUpDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> ExportFollowUpDetails([FromBody] SelHDFollowUpDetailsList ExportObj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportObj.sidx;

            string sord = ExportObj.sord;

            string filters = ExportObj.filter;


            string advnceFilters = ExportObj.advanceFilter;

            try
            {

                object Response = await HelpDeskServiceRequestServices.ExportFollowUpDetails(ExportObj, connstring, LogException, filters, advnceFilters, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion







        #region :::DeleteFollowUps :::
        /// <summary>
        /// DeleteFollowUps
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/DeleteFollowUps")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteFollowUps([FromBody] DeleteFollowUpsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.DeleteFollowUps(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion




        #region :::DeleteInvite :::
        /// <summary>
        /// DeleteInvite
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/DeleteInvite")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteInvite([FromBody] DeleteInviteList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.DeleteInvite(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion




        #region ::: GetContactPersonDetails :::
        /// <summary>
        /// GetContactPersonDetails
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/GetContactPersonDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetContactPersonDetails([FromBody] GetContactPersonDetailsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.GetContactPersonDetails(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        // checked
        #region ::: SelectMultiplePartPrefix :::
        /// <summary>
        /// SelectMultiplePartPrefix
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/SelectMultiplePartPrefix")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectMultiplePartPrefix([FromBody] SelectMultiplePartPrefixList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.SelectMultiplePartPrefix(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: CheckRequestPart :::
        /// <summary>
        /// CheckRequestPart
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/CheckRequestPart")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckRequestPart([FromBody] CheckRequestPartList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.CheckRequestPart(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        // checked
        #region ::: SelectFieldSearchPart :::
        /// <summary>
        /// SelectFieldSearchPart
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/SelectFieldSearchPart")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectFieldSearchPart([FromBody] SelectFieldSearchPartList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskServiceRequestServices.SelectFieldSearchPart(Obj, Conn, LogException, sidx, sord, page, rows, _search, filters, advnce, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SearchFieldSearchTMLPartNumber :::
        /// <summary>
        /// SearchFieldSearchTMLPartNumber
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/SearchFieldSearchTMLPartNumber")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SearchFieldSearchTMLPartNumber([FromBody] SearchFieldSearchTMLPartNumberList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskServiceRequestServices.SearchFieldSearchTMLPartNumber(Obj, Conn, LogException, sidx, sord, page, rows, _search, filters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);
        }
        #endregion



        #region :::CheckDuplicatetInviteList :::
        /// <summary>
        /// CheckDuplicateInvite
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/CheckDuplicatetInvite")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckDuplicatetInvite([FromBody] CheckDuplicatetInviteList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.CheckDuplicatetInvite(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::GetAvailableQuantity :::
        /// <summary>
        /// GetAvailableQuantity
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="constring"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/GetAvailableQuantity")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetAvailableQuantity([FromBody] GetAvailableQuantityList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.GetAvailableQuantity(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: GetPackingTypeSellingPriceTotal :::
        /// <summary>
        /// GetPackingTypeSellingPriceTotal
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/GetPackingTypeSellingPriceTotal")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetPackingTypeSellingPriceTotal([FromBody] GetPackingTypeSellingPriceTotalList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.GetPackingTypeSellingPriceTotal(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: GetSalesRepresentative_EnquiryStage :::
        /// <summary>
        /// GetSalesRepresentative_EnquiryStage
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/GetSalesRepresentative_EnquiryStage")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetSalesRepresentative_EnquiryStage([FromBody] GetSalesRepresentative_EnquiryStageList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.GetSalesRepresentative_EnquiryStage(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: GetSecondarySegment :::
        /// <summary>
        /// GetSecondarySegment
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/GetSecondarySegment")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetSecondarySegment([FromBody] GetSecondarySegmentList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.GetSecondarySegment(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion




        #region ::: GetPrimarySegment_LostSalesReasons :::
        /// <summary>
        /// GetPrimarySegment_LostSalesReasons
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/GetPrimarySegment_LostSalesReasons")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetPrimarySegment_LostSalesReasons([FromBody] GetPrimarySegment_LostSalesReasonsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.GetPrimarySegment_LostSalesReasons(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectFieldSearchCompetitor :::
        /// <summary>
        /// SelectFieldSearchCompetitor
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/SelectFieldSearchCompetitor")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectFieldSearchCompetitor([FromBody] SelectFieldSearchCompetitorList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskServiceRequestServices.SelectFieldSearchCompetitor(Obj, Conn, LogException, sidx, sord, page, rows, _search, filters, advnce, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);
        }
        #endregion



        #region ::: GetCompetitorNameDetails :::
        /// <summary>
        /// GetCompetitorNameDetails
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/GetCompetitorNameDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetCompetitorNameDetails([FromBody] GetCompetitorNameDetailsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.GetCompetitorNameDetails(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion




        #region ::: SelectFieldSearchCompetitorModel :::
        /// <summary>
        /// SelectFieldSearchCompetitorModel
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/SelectFieldSearchCompetitorModel")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectFieldSearchCompetitorModel([FromBody] SelectFieldSearchCompetitorModelList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskServiceRequestServices.SelectFieldSearchCompetitorModel(Obj, Conn, LogException, sidx, sord, page, rows, _search, filters, advnce, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);
        }
        #endregion




        // not found

        #region ::: GetModelDetails :::
        /// <summary>
        /// GetModelDetails
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/GetModelDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetModelDetails([FromBody] GetModelDetailsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.GetModelDetails(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion




        #region ::: GetCompetitorModelDetails :::
        /// <summary>
        /// GetCompetitorModelDetails
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/GetCompetitorModelDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetCompetitorModelDetails([FromBody] GetCompetitorModelDetailsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.GetCompetitorModelDetails(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: CheckDuplicatetModel :::
        /// <summary>
        /// CheckDuplicatetModel
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/CheckDuplicatetModel")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckDuplicatetModel([FromBody] CheckDuplicatetModelList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.CheckDuplicatetModel(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion




        #region ::: DeleteProducts :::
        /// <summary>
        /// DeleteProducts
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/DeleteProducts")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteProducts([FromBody] HD_DeleteProductsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.DeleteProducts(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion






        #region ::: SelectFieldSearchSerialNumber :::
        /// <summary>
        /// SelectFieldSearchSerialNumber
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/SelectFieldSearchSerialNumber")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectFieldSearchSerialNumber([FromBody] SelectFieldSearchSerialNumberList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskServiceRequestServices.SelectFieldSearchSerialNumber(Obj, Conn, LogException, sidx, sord, page, rows, _search, filters, advnce, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);
        }
        #endregion





        #region ::: GetAllocationDetails :::
        /// <summary>
        /// GetAllocationDetails
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/GetAllocationDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetAllocationDetails([FromBody] GetAllocationDetailsList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.GetAllocationDetails(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion





        #region ::: GetProductDetailsOnSerial :::
        /// <summary>
        /// GetProductDetailsOnSerial
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/GetProductDetailsOnSerial")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetProductDetailsOnSerial([FromBody] GetProductDetailsOnSerialList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.GetProductDetailsOnSerial(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: Get Product Details based on PartyID:::
        /// <summary>
        /// To get product details
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskServiceRequest/GetProductDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetProductDetails([FromBody] GetProductDetailsFList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.GetProductDetails(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: DeleteAttachment:::
        /// <summary>
        /// DeleteAttachment
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskServiceRequest/DeleteAttachment")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteAttachment([FromBody] DeleteAttachmentList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.DeleteAttachment(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion



        #region ::: CheckAttachment:::
        /// <summary>
        /// CheckAttachment
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskServiceRequest/CheckAttachment")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckAttachment([FromBody] CheckAttachmentList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.CheckAttachment(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion



        #region ::: UpdateDesc:::
        /// <summary>
        /// UpdateDesc
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskServiceRequest/UpdateDesc")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateDesc([FromBody] UpdateDescList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.UpdateDesc(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion




        #region ::: GetEndStep :::
        /// <summary>
        /// GetEndStep
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/GetEndStep")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetEndStep([FromBody] GetEndStepList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.GetEndStep(Obj, Conn, LogException, HelpDesk);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        // checked
        #region ::: LoadCustomerQuestion:::
        /// <summary>
        ////to Load Customer Question
        /// </summary> 
        [Route("api/HelpDeskServiceRequest/LoadCustomerQuestion")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadCustomerQuestion([FromBody] LoadCustomerQuestionList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskServiceRequestServices.LoadCustomerQuestion(Obj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);
        }
        #endregion


        // not found
        #region ::: LoadCustomerQuestionForRequest :::
        /// <summary>
        /// LoadCustomerQuestionForRequest
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/LoadCustomerQuestionForRequest")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadCustomerQuestionForRequest([FromBody] LoadCustomerQuestionForRequestList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskServiceRequestServices.LoadCustomerQuestionForRequest(Obj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);
        }
        #endregion



        #region ::: LoadPartsDropdown :::
        /// <summary>
        /// LoadPartsDropdown
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/LoadPartsDropdown")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadPartsDropdown([FromBody] HD_LoadPartsDropdownList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.LoadPartsDropdown(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: LoadContractorContactList :::
        /// <summary>
        /// LoadContractorContactList
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/LoadContractorContactList")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadContractorContactList([FromBody] HD_LoadContractorContactList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.LoadContractorContactList(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: GetProductIDOnModelBrandProductType :::
        /// <summary>
        /// GetProductIDOnModelBrandProductType
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/GetProductIDOnModelBrandProductType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetProductIDOnModelBrandProductType([FromBody] GetProductIDOnModelBrandProductTypeList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.GetProductIDOnModelBrandProductType(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: GetDepartmentandNameData:::
        /// <summary>
        /// GetDepartmentandNameData
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskServiceRequest/GetDepartmentandNameData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetDepartmentandNameData([FromBody] GetDepartmentandNameDataList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.GetDepartmentandNameData(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Sel Case Registration Navigation:::
        /// <summary>
        /// To Select Case Registration record for Navigation
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskServiceRequest/SelServiceRequestNavigation")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelServiceRequestNavigation([FromBody] SelServiceRequestNavigationList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.SelServiceRequestNavigation(Obj, Conn, LogException, HelpDesk);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Insert:::
        /// <summary>
        /// Insert
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskServiceRequest/Insert")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Insert([FromBody] ServiceRequest_SelectList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.Insert(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Edit:::
        /// <summary>
        /// Edit
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskServiceRequest/Edit")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Edit([FromBody] ServiceRequest_SelectList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.Edit(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: InsertAttachments_PartLists_Notes:::
        /// <summary>
        /// InsertAttachments_PartLists_Notes
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskServiceRequest/InsertAttachments_PartLists_Notes")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertAttachments_PartLists_Notes([FromBody] InsertAttachmentsPartsListsNotesList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.InsertAttachments_PartLists_Notes(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region :::SelectDefectGroup:::
        /// <summary>
        /// SelectDefectGroup
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskServiceRequest/SelectDefectGroup")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectDefectGroup([FromBody] HelpDeskSelectDefectGroupList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.SelectDefectGroup(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion



        #region :::SelectDefectName:::
        /// <summary>
        /// SelectDefectName
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskServiceRequest/SelectDefectName")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectDefectName([FromBody] SelectDefectNameList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.SelectDefectName(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion




        #region ::: SelectAttachmentDetails:::
        /// <summary>
        /// SelectAttachmentDetails
        /// </summary>
        /// <returns>...</returns>

        [Route("api/HelpDeskServiceRequest/SelectAttachmentDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectAttachmentDetails([FromBody] SelectAttachmentDetailsList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskServiceRequestServices.SelectAttachmentDetails(Obj, Conn, LogException, sidx, sord, page, rows, _search, filters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);
        }

        #endregion



        #region ::: Get SerialNumber for selected Party and Model:::
        /// <summary>
        /// To  Get  SerialNumber for selected Party and Model
        /// </summary>
        /// <returns>...</returns>

        [Route("api/HelpDeskServiceRequest/getSerialNumberForModel")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult getSerialNumberForModel([FromBody] getSerialNumberForModelFList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.getSerialNumberForModel(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion





        #region ::: Product Master Save:::
        /// <summary>
        /// To save the Product Master
        /// </summary>  
        [Route("api/HelpDeskServiceRequest/ProductMasterSave")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ProductMasterSave([FromBody] ProductMasterSaveList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.ProductMasterSave(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Product Master Save after confirmation:::
        /// <summary>
        /// To save the Product Master
        /// </summary>      

        [Route("api/HelpDeskServiceRequest/ProductMasterSaveConfirm")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ProductMasterSaveConfirm([FromBody] ProductMasterSaveConfirmFList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.ProductMasterSaveConfirm(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectRootCause:::
        /// <summary>
        /// SelectRootCause
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskServiceRequest/SelectRootCause")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectRootCause([FromBody] SelectRootCauseList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskServiceRequestServices.SelectRootCause(Obj, Conn, LogException, HelpDesk, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);
        }

        #endregion

        #region ::: SelectModel :::
        /// <summary>
        /// SelectModel
        /// </summary>
        /// <returns></returns>

        [Route("api/HelpDeskServiceRequest/SelectModel")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectModel([FromBody] HD_SelectModelList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = HelpDeskServiceRequestServices.SelectModel(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: SelectFieldSearchModel :::
        /// <summary>
        /// SelectFieldSearchModel
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequest/SelectFieldSearchModel")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectFieldSearchModel([FromBody] SelectFieldSearchModelList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            string HelpDesk = ConfigurationManager.AppSettings.Get("DbName");
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskServiceRequestServices.SelectFieldSearchModel(Obj, Conn, LogException, sidx, sord, page, rows, _search, filters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);
        }

        #endregion








    }
}
