﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using static SharedAPIClassLibrary_DC.Utilities.Common;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreChangePasswordServices
    {
        #region CheckPasswordWithDB vinay n 26/9/24
        /// <summary>
        /// CheckPasswordWithDB
        /// </summary>
        /// <param name="hashedPwdFromDatabase"></param>
        /// <param name="userEnteredPassword"></param>
        /// <returns></returns>
        private static bool CheckPasswordWithDB(string hashedPwdFromDatabase, string userEnteredPassword)
        {
            return BCrypt.CheckPassword(userEnteredPassword + Common.AMP_SP(), hashedPwdFromDatabase);
        }
        #endregion
        #region ::: CheckOldPassword vinay n 26/9/24:::
        /// <summary>
        /// To CheckOldPassword
        /// </summary>
        public static IActionResult CheckOldPassword(CheckOldPasswordList Obj, string connString, int LogException)
        {
            int Count = 0;
            try
            {


                List<GNM_User> UserRow = null;
                int User_ID = Convert.ToInt32(Obj.User_ID);
                string hashedPwdFromDatabase = String.Empty;
                string OldPassword = Common.DecryptString(Obj.OldPassword);
                // OldPassword = Util.CalculateMD5Hash(OldPassword);
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_Get_AM_ERP_CheckOldPassword_ChangePassword";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;

                            command.Parameters.AddWithValue("@User_ID", User_ID);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            hashedPwdFromDatabase = command.ExecuteScalar()?.ToString();
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log exception
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                    }
                    finally
                    {
                        command?.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }


                bool PasswrdCheck = CheckPasswordWithDB(hashedPwdFromDatabase, OldPassword);
                if (PasswrdCheck)
                {
                    Count = 1;
                }

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Count);
        }

        #endregion
        #region GenerateDMSPassword vinay n 26/9/24
        /// <summary>
        /// GenerateDMSPassword
        /// </summary>
        /// <param name="userPassword"></param>
        /// <returns></returns>
        private static string GenerateDMSPassword(string userPassword)
        {
            string AMP_SP = Common.AMP_SP();
            string pwdToHash = userPassword + AMP_SP; //  is our PARTSASSIST hard-coded salt
            string hashToStoreInDatabase = BCrypt.HashPassword(pwdToHash, BCrypt.GenerateSalt());
            return hashToStoreInDatabase;
        }
        #endregion
        #region ::: ChangePassword :::
        /// <summary>
        /// To Change Password
        /// </summary>
        public static void ChangePassword(ChangePasswordList Obj, string connString, int LogException)
        {
            try
            {


                int User_ID = Convert.ToInt32(Obj.User_ID);
                var jObj = JObject.Parse(Obj.data);

                var jTR = new JTokenReader(jObj["OldPassword"]);
                jTR.Read();
                //string OldPassword = Util.CalculateMD5Hash(Common.DecryptString(jTR.Value.ToString()));

                jTR = new JTokenReader(jObj["NewPassword"]);
                jTR.Read();
                //string NewPassword = Util.CalculateMD5Hash(Common.DecryptString(jTR.Value.ToString()));   //Commented by DK - Old logic/Algorithm 26-Apr-22

                //START - IMPLEMENT and Uncomment When Ready to TEST //DK --Commneted on 1-Feb-22
                string NewPassword = GenerateDMSPassword(Common.DecryptString(jTR.Value.ToString())); //Added by DK New Algorthim used for Passrd as Dicussed with Volvo IT team and Approved by them          


                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_ChangePassword_AM_ERP_ChangePassword_ChangePassword";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;

                            command.Parameters.AddWithValue("@User_ID", User_ID);
                            command.Parameters.AddWithValue("@NewPassword", NewPassword);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            command.ExecuteNonQuery();
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log exception
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                    }
                    finally
                    {
                        command?.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                //gbl.InsertGPSDetails(Convert.ToInt32(Obj.Company_ID.ToString()), Convert.ToInt32(Obj.Branch), User_ID, Common.GetObjectID("CoreChangePassword"), UpdateRow.User_ID, 0, 0, "Changed Password", false, Convert.ToInt32(Obj.MenuID), Convert.ToDateTime(Obj.LoggedINDateTime));
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
        }
        #endregion

    }
    #region ChangePasswordListsAndObjs vinay n 26/9/24
    public class CheckOldPasswordList
    {
        public int User_ID { get; set; }
        public string OldPassword { get; set; }

    }
    public class ChangePasswordList
    {
        public int User_ID { get; set; }
        public string data { get; set; }
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int MenuID { get; set; }
        public DateTime LoggedINDateTime { get; set; }
    }
    #endregion

}
