using Microsoft.AspNetCore.Mvc;
using PBC.CoreService.Utilities.DTOs;
using System.Text;
using System.Text.Json;
using System.Data;
using System.Data.SqlClient;
using System.Net;
using Newtonsoft.Json.Linq;

namespace PBC.CoreService.Services
{
    public class CoreCompanyFinancialYearServices : ICoreCompanyFinancialYearServices
    {
        private readonly ILogger<CoreCompanyFinancialYearServices> _logger;
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;

        public CoreCompanyFinancialYearServices(
            ILogger<CoreCompanyFinancialYearServices> logger,
            HttpClient httpClient,
            IConfiguration configuration)
        {
            _logger = logger;
            _httpClient = httpClient;
            _configuration = configuration;
        }

        #region ::: Select /Mithun:::
        /// <summary>
        /// to select all the Financial Year of the company
        /// </summary> 
        public async Task<IActionResult> Select(SelectCompanyFinancialYearList selectObj, string connString, int logException, string sidx, string sord, int page, int rows)
        {
            var x = default(dynamic);
            try
            {
                int companyID = Convert.ToInt32(selectObj.Company_ID);

                // Connect to your database
                using (var connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Create command for calling stored procedure
                    using (var command = new SqlCommand("Up_Sel_Am_Erp_SelectCompanyFinancialYear", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        command.Parameters.AddWithValue("@CompanyID", companyID);

                        // Execute command and retrieve results
                        using (var reader = command.ExecuteReader())
                        {
                            // Process result set for data
                            var financialYearList = new List<CompanyFinancialYearData>();
                            while (reader.Read())
                            {
                                var financialYear = new CompanyFinancialYearData
                                {
                                    Company_FinancialYear_ID = (int)reader["Company_FinancialYear_ID"],
                                    delete = "<input type='checkbox' key='" + reader["Company_FinancialYear_ID"] + "' id='chk" + reader["Company_FinancialYear_ID"] + "' class='chkFinancialYearDelete' editmode='false'/>",
                                    Company_FinancialYear = int.Parse(reader["Company_FinancialYear"].ToString()),
                                    Company_FinancialYear_FromDate = ((DateTime)reader["Company_FinancialYear_FromDate"]).ToString("dd-MMM-yyyy"),
                                    Company_FinancialYear_ToDate = ((DateTime)reader["Company_FinancialYear_ToDate"]).ToString("dd-MMM-yyyy")
                                };
                                financialYearList.Add(financialYear);
                            }

                            reader.NextResult(); // Move to the next result set for total count
                            if (reader.Read())
                            {
                                int totalRecords = (int)reader["TotalRecords"];
                                int totalPages = rows > 0 ? (int)Math.Ceiling((double)totalRecords / rows) : 0;

                                // Prepare response object
                                x = new
                                {
                                    total = totalPages,
                                    page = page,
                                    records = totalRecords,
                                    data = financialYearList.Skip((page - 1) * rows).Take(rows)
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle exceptions - Use HTTP call to PBC.UtilityService for logging
                if (logException == 1)
                {
                    await LogToUtilityServiceAsync(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
                }
            }

            // Return JSON response
            //return Json(x, JsonRequestBehavior.AllowGet);
            return new JsonResult(x);
        }


        #endregion

        #region ::: SelectCompany Mithun:::
        /// <summary>
        /// to get all the Company Names 
        /// </summary> 

        public async Task<IActionResult> SelectCompany(SelectCompanyList selectCompanyObj, string connString, int logException)
        {
            var regionArray = new List<dynamic>();
            try
            {
                int companyID = Convert.ToInt32(selectCompanyObj.Company_ID);
                int userLanguageID = Convert.ToInt32(selectCompanyObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(selectCompanyObj.GeneralLanguageID);

                using (var connection = new SqlConnection(connString))
                {
                    connection.Open();

                    SqlCommand command = new SqlCommand("Up_Sel_Am_Erp_SelectCompany", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.AddWithValue("@Company_ID", companyID);
                    command.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                    command.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var item = new
                            {
                                Company_ID = reader.GetInt32(0),
                                Company_Name = reader.GetString(1)
                            };
                            regionArray.Add(item);
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                await LogToUtilityServiceAsync(wex.HResult, wex.Status.ToString(), wex.TargetSite?.ToString() ?? "", wex.StackTrace ?? "");
            }
            catch (Exception ex)
            {
                if (logException == 1)
                {
                    await LogToUtilityServiceAsync(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
                }
            }

            //return Json(regionArray, JsonRequestBehavior.AllowGet);
            return new JsonResult(regionArray);
        }

        #endregion

        #region ::: Save /Mithun:::
        /// <summary>
        /// to save the data
        /// </summary>
        public async Task<IActionResult> Save(SaveCoreCompanyFinancialList saveObj, string connString, int logException)
        {
            try
            {
                string jsonData = saveObj.Data;
                JObject jobj = JObject.Parse(jsonData);
                int Company_ID = Convert.ToInt32(saveObj.Company_ID);
                int User_ID = Convert.ToInt32(saveObj.User_ID);

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Fetch GNM_Object records using stored procedure
                    SqlCommand objectCommand = new SqlCommand("Up_Sel_Am_Erp_SelectFinancialYearObjects", connection);
                    objectCommand.CommandType = CommandType.StoredProcedure;

                    List<GNM_Object> ObjectList = new List<GNM_Object>();

                    using (SqlDataReader objectReader = objectCommand.ExecuteReader())
                    {
                        while (objectReader.Read())
                        {
                            GNM_Object obj = new GNM_Object();
                            obj.Object_ID = Convert.ToInt32(objectReader["Object_ID"]);
                            obj.Object_Description = objectReader["Object_Description"].ToString();
                            // Map other properties as needed
                            ObjectList.Add(obj);
                        }
                    }

                    foreach (var ObjectItemList in ObjectList)
                    {
                        foreach (var row in jobj["rows"])
                        {
                            GNM_CompanyFinancialYear detailRow = row.ToObject<GNM_CompanyFinancialYear>();

                            if (detailRow.Company_FinancialYear_ID != 0)
                            {
                                // Update existing record using stored procedure
                                SqlCommand updateCommand = new SqlCommand("Up_Upd_Am_Erp_UpdateCompanyFinancialYear", connection);
                                updateCommand.CommandType = CommandType.StoredProcedure;
                                updateCommand.Parameters.AddWithValue("@Company_FinancialYear_ID", detailRow.Company_FinancialYear_ID);
                                updateCommand.Parameters.AddWithValue("@Company_ID", detailRow.Company_ID);
                                updateCommand.Parameters.AddWithValue("@Company_FinancialYear", detailRow.Company_FinancialYear);
                                updateCommand.Parameters.AddWithValue("@Company_FinancialYear_FromDate", detailRow.Company_FinancialYear_FromDate);
                                updateCommand.Parameters.AddWithValue("@Company_FinancialYear_ToDate", detailRow.Company_FinancialYear_ToDate);

                                updateCommand.ExecuteNonQuery();

                                // Log update
                                // gbl.InsertGPSDetails(Company_ID, Convert.ToInt32(SaveObj.Branch), User_ID, Common.GetObjectID("CoreCompanyFinancialYear",constring), detailRow.Company_FinancialYear_ID, 0, 0, "Updated " + detailRow.Company_FinancialYear, false, Convert.ToInt32(SaveObj.MenuID), Convert.ToDateTime(SaveObj.LoggedINDateTime));

                            }
                            else
                            {
                                // Insert new record using stored procedure
                                SqlCommand insertCommand = new SqlCommand("Up_Ins_Am_Erp_InsertCompanyFinancialYear", connection);
                                insertCommand.CommandType = CommandType.StoredProcedure;
                                insertCommand.Parameters.AddWithValue("@Company_ID", detailRow.Company_ID);
                                insertCommand.Parameters.AddWithValue("@Company_FinancialYear", detailRow.Company_FinancialYear);
                                insertCommand.Parameters.AddWithValue("@Company_FinancialYear_FromDate", detailRow.Company_FinancialYear_FromDate);
                                insertCommand.Parameters.AddWithValue("@Company_FinancialYear_ToDate", detailRow.Company_FinancialYear_ToDate);

                                SqlParameter newFinancialYearIDParam = new SqlParameter("@NewFinancialYearID", SqlDbType.Int);
                                newFinancialYearIDParam.Direction = ParameterDirection.Output;
                                insertCommand.Parameters.Add(newFinancialYearIDParam);

                                insertCommand.ExecuteNonQuery();

                                int newFinancialYearID = Convert.ToInt32(newFinancialYearIDParam.Value);

                                // Log insert
                                //   gbl.InsertGPSDetails(Company_ID, Convert.ToInt32(SaveObj.Branch), User_ID, Common.GetObjectID("CoreCompanyFinancialYear",constring), newFinancialYearID, 0, 0, "Inserted " + detailRow.Company_FinancialYear, false, Convert.ToInt32(SaveObj.MenuID), Convert.ToDateTime(SaveObj.LoggedINDateTime));

                            }

                            // Handle PrefixSuffixList logic using stored procedure for select query
                            // Call stored procedure to fetch PrefixSuffix records
                            SqlCommand selectCommand = new SqlCommand("Up_Sel_Am_Erp_SelectFinacialYearPrefixSuffix", connection);
                            selectCommand.CommandType = CommandType.StoredProcedure;
                            selectCommand.Parameters.AddWithValue("@Company_ID", Company_ID);
                            selectCommand.Parameters.AddWithValue("@Object_ID", ObjectItemList.Object_ID);
                            selectCommand.Parameters.AddWithValue("@FinancialYear", detailRow.Company_FinancialYear - 1);

                            using (SqlDataReader reader = selectCommand.ExecuteReader())
                            {
                                if (!reader.HasRows)
                                {
                                    // Insert new PrefixSuffix record using stored procedure
                                    SqlCommand insertPrefixSuffixCommand = new SqlCommand("Up_Ins_Am_Erp_InsertPrefixsSuffixs", connection);
                                    insertPrefixSuffixCommand.CommandType = CommandType.StoredProcedure;
                                    insertPrefixSuffixCommand.Parameters.AddWithValue("@Company_ID", Company_ID);
                                    insertPrefixSuffixCommand.Parameters.AddWithValue("@Object_ID", ObjectItemList.Object_ID);
                                    insertPrefixSuffixCommand.Parameters.AddWithValue("@Start_Number", 1);
                                    insertPrefixSuffixCommand.Parameters.AddWithValue("@Prefix", "");
                                    insertPrefixSuffixCommand.Parameters.AddWithValue("@Suffix", "");
                                    insertPrefixSuffixCommand.Parameters.AddWithValue("@FromDate", detailRow.Company_FinancialYear_FromDate);
                                    insertPrefixSuffixCommand.Parameters.AddWithValue("@ToDate", Convert.ToDateTime(detailRow.Company_FinancialYear_ToDate.ToString().Split(' ')[0] + " 23:59:59.000"));
                                    insertPrefixSuffixCommand.Parameters.AddWithValue("@FinancialYear", detailRow.Company_FinancialYear);
                                    insertPrefixSuffixCommand.Parameters.AddWithValue("@Company_FinancialYear_ID", detailRow.Company_FinancialYear_ID);
                                    insertPrefixSuffixCommand.Parameters.AddWithValue("@ModifiedBY", User_ID);
                                    insertPrefixSuffixCommand.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                                    insertPrefixSuffixCommand.ExecuteNonQuery();

                                }
                                else
                                {
                                    // Update existing PrefixSuffix record using stored procedure
                                    while (reader.Read())
                                    {
                                        SqlCommand updatePrefixSuffixCommand = new SqlCommand("Up_Upd_Am_Erp_UpdatePrefixsSuffixs", connection);
                                        updatePrefixSuffixCommand.CommandType = CommandType.StoredProcedure;
                                        updatePrefixSuffixCommand.Parameters.AddWithValue("@PrefixSuffix_ID", reader["PrefixSuffix_ID"]);
                                        updatePrefixSuffixCommand.Parameters.AddWithValue("@Branch_ID", reader["Branch_ID"] == DBNull.Value ? DBNull.Value : (object)reader["Branch_ID"]);
                                        updatePrefixSuffixCommand.Parameters.AddWithValue("@Start_Number", reader["Start_Number"]);
                                        updatePrefixSuffixCommand.Parameters.AddWithValue("@Prefix", reader["Prefix"]);
                                        updatePrefixSuffixCommand.Parameters.AddWithValue("@Suffix", reader["Suffix"]);
                                        updatePrefixSuffixCommand.Parameters.AddWithValue("@ToDate", Convert.ToDateTime(detailRow.Company_FinancialYear_ToDate.ToString().Split(' ')[0] + " 23:59:59.000"));
                                        updatePrefixSuffixCommand.Parameters.AddWithValue("@ModifiedBY", User_ID);
                                        updatePrefixSuffixCommand.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                                        updatePrefixSuffixCommand.ExecuteNonQuery();

                                    }
                                }
                            }
                        }

                    }
                    return new JsonResult(new { success = true });
                }

            }

            catch (Exception ex)
            {
                if (logException == 1)
                {
                    await LogToUtilityServiceAsync(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
                }
                return new JsonResult(new { success = false });
            }
        }


        #endregion

        #region ::: Delete Mithun:::
        /// <summary>
        /// to Delete the details
        /// </summary>

        public async Task<IActionResult> Delete(DeleteCoreCompanyFinancialList deleteObj, string connString, int logException)
        {
            string errorMsg = "";
            int value = 0;
            try
            {
                JObject jobj = JObject.Parse(deleteObj.key);
                int rowCount = jobj["rows"].Count();
                int id = 0;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    SqlCommand cmd = new SqlCommand();
                    cmd.Connection = conn;

                    for (int i = 0; i < rowCount; i++)
                    {
                        JToken idToken = jobj["rows"].ElementAt(i)["id"];
                        id = idToken.Value<int>();

                        // Delete command
                        cmd.CommandText = "DELETE FROM GNM_CompanyFinancialYear WHERE Company_FinancialYear_ID = @id";
                        cmd.Parameters.Clear();
                        cmd.Parameters.AddWithValue("@id", id);
                        cmd.ExecuteNonQuery();
                    }
                }

                errorMsg += await GetResourceStringAsync(deleteObj.UserCulture.ToString(), "deletedsuccessfully");

                // Logging the deletion
                //    gbl.InsertGPSDetails(Convert.ToInt32(DeleteObj.Company_ID), Convert.ToInt32(DeleteObj.Branch), Convert.ToInt32(DeleteObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreCompanyFinancialYear",constring)), id, 0, 0, "Deleted " + value, false, Convert.ToInt32(DeleteObj.MenuID), Convert.ToDateTime(DeleteObj.LoggedINDateTime));
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    errorMsg += await GetResourceStringAsync(deleteObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords");
                }
                else
                {
                    errorMsg += await GetResourceStringAsync(deleteObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords");
                }
            }
            //return errorMsg;
            return new JsonResult(errorMsg);
        }

        #endregion

        #region ::: CheckFinancialYear /Mithun:::
        /// <summary>
        /// to check if the financial year is already selected for the company
        /// </summary>
        public async Task<IActionResult> CheckFinancialYear(CheckFinancialYearList checkFinancialYearObj, string connString, int logException)
        {
            int status = 0;
            try
            {
                int companyID = Convert.ToInt32(checkFinancialYearObj.Company_ID);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("Up_Chk_Am_Erp_CheckCompanyFinancialYear", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Input parameters
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);
                        cmd.Parameters.AddWithValue("@FinancialYear", checkFinancialYearObj.financialYear);
                        cmd.Parameters.AddWithValue("@PrimaryKey", checkFinancialYearObj.primaryKey);

                        // Execute the command
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                // Read the status value from the result set
                                status = reader.GetInt32(0);
                            }
                        }
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                // Log SQL exceptions with more details
                if (logException == 1)
                {
                    await LogToUtilityServiceAsync(sqlEx.HResult, sqlEx.Message, sqlEx.TargetSite?.ToString() ?? "", sqlEx.StackTrace ?? "");
                }
                // Handle SQL exception
            }
            catch (Exception ex)
            {
                // Log general exceptions with more details
                if (logException == 1)
                {
                    await LogToUtilityServiceAsync(ex.HResult, ex.GetType().FullName + ": " + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
                }
                // Handle other exceptions
            }

            return new JsonResult(status);
        }





        #endregion

        #region ::: GetAllSavedFinancialYears /Mithun:::
        /// <summary>
        /// to check if the financial year is already selected for the company
        /// </summary>

        public async Task<IActionResult> GetAllSavedFinancialYears(GetAllSavedFinancialYearsList getAllSavedFinancialYearsObj, string connString, int logException)
        {
            var jsonResult = default(dynamic);
            try
            {
                int companyID = Convert.ToInt32(getAllSavedFinancialYearsObj.Company_ID);

                // Step 1: ADO.NET to execute stored procedure
                List<string> financialYears = new List<string>();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetAllSavedFinancialYears", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@CompanyID", companyID);

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            string financialYear = reader["Company_FinancialYear"].ToString();
                            financialYears.Add(financialYear);
                        }
                    }
                }

                // Step 2: LINQ to construct JSON result
                var x = financialYears.Select(f => new { Company_FinancialYear = f });

                jsonResult = new
                {
                    financialYear = x.ToArray()
                };
            }
            catch (WebException wex)
            {
                await LogToUtilityServiceAsync(wex.HResult, wex.Status.ToString(), wex.TargetSite?.ToString() ?? "", wex.StackTrace ?? "");
                // Handle web exception
            }
            catch (Exception ex)
            {
                if (logException == 1)
                {
                    await LogToUtilityServiceAsync(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
                    // Handle other exceptions
                }
            }

            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);
        }

        #endregion

        #region Helper Methods for HTTP Calls to PBC.UtilityService

        /// <summary>
        /// Helper method to log exceptions via HTTP call to PBC.UtilityService
        /// </summary>
        private async Task LogToUtilityServiceAsync(int exId, string exMessage, string exDetails, string exStackTrace)
        {
            try
            {
                var logRequest = new LogRequest
                {
                    ExId = exId,
                    ExMessage = exMessage,
                    ExDetails = exDetails,
                    ExStackTrace = exStackTrace
                };

                var json = JsonSerializer.Serialize(logRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // Get PBC.UtilityService URL from configuration
                var utilityServiceUrl = _configuration["PBC.UtilityService:BaseUrl"] ?? "http://localhost:7003";
                var response = await _httpClient.PostAsync($"{utilityServiceUrl}/api/logsheetexporter/log", content);

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogWarning("Failed to log to utility service. Status: {StatusCode}", response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service for logging");
            }
        }

        /// <summary>
        /// Helper method to get resource string via HTTP call to PBC.UtilityService
        /// </summary>
        private async Task<string> GetResourceStringAsync(string cultureValue, string resourceKey)
        {
            try
            {
                var resourceRequest = new ResourceStringRequest
                {
                    CultureValue = cultureValue,
                    ResourceKey = resourceKey
                };

                var json = JsonSerializer.Serialize(resourceRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // Get PBC.UtilityService URL from configuration
                var utilityServiceUrl = _configuration["PBC.UtilityService:BaseUrl"] ?? "http://localhost:7003";
                var response = await _httpClient.PostAsync($"{utilityServiceUrl}/api/commonfunctionalities/resource-string", content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    return responseContent.Trim('"'); // Remove JSON quotes
                }
                else
                {
                    _logger.LogWarning("Failed to get resource string from utility service. Status: {StatusCode}", response.StatusCode);
                    return resourceKey; // Fallback to resource key
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service for resource string");
                return resourceKey; // Fallback to resource key
            }
        }

        #endregion
    }
}
