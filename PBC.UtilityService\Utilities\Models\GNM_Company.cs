using System;
using System.Collections.Generic;

namespace PBC.UtilityService.Utilities.Models
{
    public partial class GNM_Company
    {
        public GNM_Company()
        {
            this.GNM_Branch = new HashSet<GNM_Branch>();
        }

        public int Company_ID { get; set; }
        public string Company_Name { get; set; }
        public string Company_ShortName { get; set; }
        public string Company_Address { get; set; }
        public string Company_ZipCode { get; set; }
        public int Country_ID { get; set; }
        public int State_ID { get; set; }
        public string Company_Phone { get; set; }
        public string Company_Fax { get; set; }
        public string Company_Email { get; set; }
        public string Company_Website { get; set; }
        public string Company_Type { get; set; }
        public bool Company_Active { get; set; }
        public string Company_Mobile { get; set; }
        public string Company_Logo { get; set; }
        public string Company_TaxNumber { get; set; }
        public string Company_RegistrationNumber { get; set; }
        public DateTime? Company_EstablishedDate { get; set; }
        public string Company_Description { get; set; }
        public int? TimeZoneID { get; set; }
        public string Company_Currency { get; set; }
        public string Company_Language { get; set; }
        public bool? Company_IsHeadOffice { get; set; }
        public int? Parent_Company_ID { get; set; }
        public DateTime? Created_Date { get; set; }
        public int? Created_By { get; set; }
        public DateTime? Modified_Date { get; set; }
        public int? Modified_By { get; set; }

        public virtual ICollection<GNM_Branch> GNM_Branch { get; set; }
    }
}
