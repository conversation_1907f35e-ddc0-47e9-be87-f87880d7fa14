﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace SharedAPIClassLibrary_AMERP
{
    public class CoreDefectGroupMasterServices
    {

        #region :::SelectReferenceMaster /Mithun:::
        /// <summary>
        /// To select Reference Master
        /// </summary>
        /// <param name="SelectReferenceMasterObj"></param>
        /// <param name="constring"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult SelectReferenceMaster(SelectDefectGroupReferenceMasterList SelectDefectGroupReferenceMasterObj, string constring, int LogException)
        {
            var Masterdata = default(dynamic);
            try
            {
                int CompanyID = Convert.ToInt32(SelectDefectGroupReferenceMasterObj.Company_ID);
                int Language_ID = Convert.ToInt32(SelectDefectGroupReferenceMasterObj.UserLanguageID);

                string userLanguageCode = SelectDefectGroupReferenceMasterObj.UserLanguageCode.ToString();
                string generalLanguageCode = SelectDefectGroupReferenceMasterObj.GeneralLanguageCode.ToString();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Retrieve GNM_RefMasterDetail data using SP
                    List<GNM_RefMasterDetail> refMasterDetails = new List<GNM_RefMasterDetail>();
                    using (SqlCommand cmd = new SqlCommand("UP_SEL_AMERP_GetGNMRefMasterDetail", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", CompanyID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                refMasterDetails.Add(new GNM_RefMasterDetail
                                {
                                    RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                    RefMasterDetail_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name")),
                                    RefMasterDetail_IsActive = reader.GetBoolean(reader.GetOrdinal("RefMasterDetail_IsActive")),
                                    GNM_RefMaster = new GNM_RefMaster
                                    {
                                        RefMaster_Name = reader.GetString(reader.GetOrdinal("RefMaster_Name")),
                                        IsCompanySpecific = reader.GetBoolean(reader.GetOrdinal("IsCompanySpecific"))
                                    },
                                    Company_ID = reader.IsDBNull(reader.GetOrdinal("Company_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Company_ID"))
                                });
                            }
                        }
                    }

                    if (userLanguageCode == generalLanguageCode)
                    {
                        Masterdata = new
                        {
                            ReferenceMasterData = from Ref in refMasterDetails
                                                  orderby Ref.RefMasterDetail_Name
                                                  select new
                                                  {
                                                      ID = Ref.RefMasterDetail_ID,
                                                      Name = Ref.RefMasterDetail_Name
                                                  }
                        };
                    }
                    else
                    {
                        // Retrieve GNM_RefMasterDetailLocale data using SP
                        List<GNM_RefMasterDetailLocale> refMasterDetailLocales = new List<GNM_RefMasterDetailLocale>();
                        using (SqlCommand cmd = new SqlCommand("UP_SEL_AMERP_GetGNMRefMasterDetailLocale", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Language_ID", Language_ID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    refMasterDetailLocales.Add(new GNM_RefMasterDetailLocale
                                    {
                                        RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                        RefMasterDetail_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name")),
                                        Language_ID = reader.GetInt32(reader.GetOrdinal("Language_ID")),
                                        GNM_RefMasterDetail = new GNM_RefMasterDetail
                                        {
                                            RefMasterDetail_IsActive = reader.GetBoolean(reader.GetOrdinal("RefMasterDetail_IsActive"))
                                        },
                                        GNM_RefMaster = new GNM_RefMaster
                                        {
                                            RefMaster_Name = reader.GetString(reader.GetOrdinal("RefMaster_Name"))
                                        }
                                    });
                                }
                            }
                        }

                        Masterdata = new
                        {
                            ReferenceMasterData = from Ref in refMasterDetails
                                                  join RefL in refMasterDetailLocales on Ref.RefMasterDetail_ID equals RefL.RefMasterDetail_ID
                                                  orderby RefL.RefMasterDetail_Name
                                                  select new
                                                  {
                                                      ID = RefL.RefMasterDetail_ID,
                                                      Name = RefL.RefMasterDetail_Name
                                                  }
                        };
                    }
                }

                return new JsonResult(Masterdata);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                return new JsonResult(null);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(null);
            }
        }

        #endregion

        #region :::Loading Defect Group English Grid /Mithun:::
        /// <summary>
        /// Loading Defect Group English Grid
        /// </summary>
        public static IActionResult Select(SelectDefectGroupList SelectObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters, string Query, bool advnce)
        {
            string AppPath = string.Empty;
            try
            {
                int Count = 0;
                int Total = 0;
                int CompanyID = Convert.ToInt32(SelectObj.Company_ID);
                string YesE = CommonFunctionalities.GetResourceString(SelectObj?.GeneralCulture?.ToString(), "yes")?.ToString() ?? string.Empty;

                string NoE = CommonFunctionalities.GetResourceString(SelectObj?.GeneralCulture?.ToString(), "No")?.ToString() ?? string.Empty;

                string YesL = CommonFunctionalities.GetResourceString(SelectObj?.UserCulture?.ToString(), "yes")?.ToString() ?? string.Empty;

                string NoL = CommonFunctionalities.GetResourceString(SelectObj?.UserCulture?.ToString(), "No")?.ToString() ?? string.Empty;


                List<DefectGroupMaster> defectGroupMasterList = new List<DefectGroupMaster>();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Call the first stored procedure
                    using (SqlCommand cmd = new SqlCommand("UP_SEL_GetDefectGroups", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@IssueAreaID", SelectObj.IssueAreaID);
                        cmd.Parameters.AddWithValue("@CompanyID", CompanyID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var defectGroup = new DefectGroupMaster()
                                {
                                    DefectGroup_ID = reader.GetInt32(reader.GetOrdinal("DefectGroup_ID")),
                                    DefectGroup_ShortName = reader.GetString(reader.GetOrdinal("DefectGroup_ShortName")),
                                    DefectGroupDescription = reader.GetString(reader.GetOrdinal("DefectGroupDescription")),
                                    DefectGroup_IsActive = reader.GetBoolean(reader.GetOrdinal("DefectGroup_IsActive")) ? YesE : NoE
                                };
                                defectGroupMasterList.Add(defectGroup);
                            }
                        }
                    }

                    // Call the second stored procedure if the LanguageID is different from the GeneralLanguageID
                    if (SelectObj.LanguageID != Convert.ToInt32(SelectObj.GeneralLanguageID))
                    {
                        using (SqlCommand cmd = new SqlCommand("UP_SEL_GetLocalizedDefectGroups", connection))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@IssueAreaID", SelectObj.IssueAreaID);
                            cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                            cmd.Parameters.AddWithValue("@LanguageID", SelectObj.LanguageID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var defectGroup = new DefectGroupMaster()
                                    {
                                        DefectGroup_ID = reader.GetInt32(reader.GetOrdinal("DefectGroup_ID")),
                                        DefectGroup_ShortName = reader.GetString(reader.GetOrdinal("DefectGroup_ShortName")),
                                        DefectGroupDescription = reader.GetString(reader.GetOrdinal("DefectGroupDescription")),
                                        DefectGroup_IsActive = reader.GetBoolean(reader.GetOrdinal("DefectGroup_IsActive")) ? YesL : NoL
                                    };
                                    defectGroupMasterList.Add(defectGroup);
                                }
                            }
                        }
                    }
                }

                IQueryable<DefectGroupMaster> IQDefectGroupMaster = defectGroupMasterList.AsQueryable();
                Filters appliedFilters = null;
                AdvanceFilter appliedAdvanceFilters = null;

                // Apply filtering if _search is true
                if (_search)
                {
                    // Double decryption process
                    string decryptedFiltersOnce = Common.DecryptString(filters);
                    string decryptedFiltersTwice = Common.DecryptString(decryptedFiltersOnce);

                    appliedFilters = JObject.Parse(decryptedFiltersTwice).ToObject<Filters>();

                    if (appliedFilters.rules.Count() > 0)
                    {
                        IQDefectGroupMaster = IQDefectGroupMaster.FilterSearch<DefectGroupMaster>(appliedFilters);
                    }
                }

                // Apply advanced filtering if advnce is true
                if (advnce)
                {
                    appliedAdvanceFilters = JObject.Parse(Query).ToObject<AdvanceFilter>();
                    IQDefectGroupMaster = IQDefectGroupMaster.AdvanceSearch<DefectGroupMaster>(appliedAdvanceFilters);
                }

                // Apply sorting and paging
                IQDefectGroupMaster = IQDefectGroupMaster.OrderByField<DefectGroupMaster>(sidx, sord);


                // Calculate total records and pages for pagination
                Count = IQDefectGroupMaster.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                // Prepare JSON data for jqGrid
                var jsonData = new
                {
                    total = Total,
                    page = page,
                    data = (from a in IQDefectGroupMaster.AsEnumerable()
                            select new
                            {
                                ID = a.DefectGroup_ID,
                                edit = "<a title='View' href='#' id='" + a.DefectGroup_ID + "' key='" + a.DefectGroup_ID + "' class='DefectGroupEdit font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input type='checkbox' key='" + a.DefectGroup_ID + "' defaultchecked='' id='chk" + a.DefectGroup_ID + "' class='DefectGroupDelete'/>",
                                DefectGroup_ShortName = (a.DefectGroup_ShortName),
                                DefectGroupDescription = (a.DefectGroupDescription),
                                DefectGroup_IsActive = a.DefectGroup_IsActive,
                                Locale = "<a key='" + a.DefectGroup_ID + "' src='" + AppPath + "/Content/local.png' class='DefectGroupLocale' alt='Localize' width='20' height='20' title='Localize'><i class='fa fa-globe'></i></a>",
                                View = "<a title='View' href='#' id='" + a.DefectGroup_ID + "' key='" + a.DefectGroup_ID + "' class='ViewDefectGroupLocale font-icon-class'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                            }).ToList().Paginate(page, rows),
                    records = Count,
                    filter = filters,
                    advanceFilter = Query
                };

                //return Json(jsonData, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonData);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region :::SelectParticularDefectGroup /Mithun:::
        /// <summary>
        /// To Select Particular DefectGroup
        /// </summary>
        /// <param name="DefectGroupID"></param>
        /// <returns></returns>
        public static IActionResult SelectParticularDefectGroup(SelectParticularDefectGroupList SelectParticularDefectGroupObj, string constring, int LogException)
        {
            var x = new object();

            try
            {
                int Language_ID = Convert.ToInt32(SelectParticularDefectGroupObj.UserLanguageID);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Call the stored procedure USP_GetDefectGroupLocale
                    SqlCommand cmdDefectGroupLocale = new SqlCommand("USP_GetDefectGroupLocale", conn);
                    cmdDefectGroupLocale.CommandType = CommandType.StoredProcedure;
                    cmdDefectGroupLocale.Parameters.AddWithValue("@DefectGroupID", SelectParticularDefectGroupObj.DefectGroupID);
                    cmdDefectGroupLocale.Parameters.AddWithValue("@LanguageID", Language_ID);

                    SqlDataReader readerDefectGroupLocale = cmdDefectGroupLocale.ExecuteReader();

                    if (readerDefectGroupLocale.Read())
                    {
                        int defectGroupID = readerDefectGroupLocale.GetInt32(0);
                        string defectGroupShortName = readerDefectGroupLocale.GetString(1);
                        string defectGroupDescription = readerDefectGroupLocale.IsDBNull(2) ? null : readerDefectGroupLocale.GetString(2);
                        bool defectGroupIsActive = readerDefectGroupLocale.GetBoolean(3);
                        int issueAreaID = readerDefectGroupLocale.GetInt32(4);
                        string defectGroupLocaleID = readerDefectGroupLocale.GetInt32(5).ToString();
                        string defectGroupLocaleName = readerDefectGroupLocale.IsDBNull(6) ? "" : readerDefectGroupLocale.GetString(6);
                        string defectGroupLocaleShortName = readerDefectGroupLocale.IsDBNull(7) ? "" : readerDefectGroupLocale.GetString(7);

                        x = new
                        {
                            DefectGroup_ID = defectGroupID,
                            DefectGroup_ShortName = defectGroupShortName,
                            DefectGroupDescription = defectGroupDescription,
                            DefectGroup_IsActive = defectGroupIsActive,
                            IssueArea_ID = issueAreaID,
                            DefectGroupLocaleID = defectGroupLocaleID,
                            DefectGroupLocaleName = defectGroupLocaleName,
                            DefectGroupLocaleShortName = defectGroupLocaleShortName,
                        };
                    }
                    else
                    {
                        x = new
                        {
                            DefectGroup_ID = SelectParticularDefectGroupObj.DefectGroupID,
                            DefectGroup_ShortName = "",
                            DefectGroupDescription = "",
                            DefectGroup_IsActive = false,
                            IssueArea_ID = 0,
                            DefectGroupLocaleID = "",
                            DefectGroupLocaleName = "",
                            DefectGroupLocaleShortName = "",
                        };
                    }

                    readerDefectGroupLocale.Close();

                    conn.Close();
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }

            //return Json(x, JsonRequestBehavior.AllowGet);
            return new JsonResult(x);
        }


        #endregion

        #region :::Save /Mithun:::
        /// <summary>
        /// To save
        /// </summary>
        /// <returns></returns>
        public static IActionResult Save(SaveDefectGroupList SaveDefectGroupObj, string constring, int LogException)
        {
            string Msg = string.Empty;
            try
            {
                //GNM_User UserDetails = (GNM_User)Session["UserDetails"];
                //  GNM_User UserDetails = SaveDefectGroupObj.UserDetails.FirstOrDefault();
                JObject jObj = JObject.Parse(SaveDefectGroupObj.data);
                int Count = jObj["rows"].Count();
                int CompanyID = Convert.ToInt32(SaveDefectGroupObj.Company_ID);
                DateTime LoginDatetime = Convert.ToDateTime(SaveDefectGroupObj.LoggedINDateTime);

                // Construct XML data representing multiple defect groups
                StringBuilder xmlDataBuilder = new StringBuilder();
                xmlDataBuilder.Append("<DefectGroups>");
                for (int i = 0; i < Count; i++)
                {
                    JObject defectGroup = jObj["rows"][i].ToObject<JObject>();
                    int IssueArea_ID = Convert.ToInt32(defectGroup["IssueArea_ID"]);
                    int DefectGroup_ID = defectGroup["DefectGroup_ID"] != null ? Convert.ToInt32(defectGroup["DefectGroup_ID"]) : 0;
                    string DefectGroup_ShortName = Common.DecryptString(defectGroup["DefectGroup_ShortName"].ToString());
                    string DefectGroupDescription = Common.DecryptString(defectGroup["DefectGroupDescription"].ToString());
                    bool DefectGroup_IsActive = Convert.ToBoolean(defectGroup["DefectGroup_IsActive"].ToString());

                    xmlDataBuilder.Append("<DefectGroup>");
                    xmlDataBuilder.AppendFormat("<IssueArea_ID>{0}</IssueArea_ID>", IssueArea_ID);
                    xmlDataBuilder.AppendFormat("<DefectGroup_ID>{0}</DefectGroup_ID>", DefectGroup_ID);
                    xmlDataBuilder.AppendFormat("<DefectGroup_ShortName>{0}</DefectGroup_ShortName>", DefectGroup_ShortName);
                    xmlDataBuilder.AppendFormat("<DefectGroupDescription>{0}</DefectGroupDescription>", DefectGroupDescription);
                    xmlDataBuilder.AppendFormat("<DefectGroup_IsActive>{0}</DefectGroup_IsActive>", DefectGroup_IsActive);
                    xmlDataBuilder.AppendFormat("<Company_ID>{0}</Company_ID>", CompanyID);
                    xmlDataBuilder.Append("</DefectGroup>");
                }
                xmlDataBuilder.Append("</DefectGroups>");
                string xmlData = xmlDataBuilder.ToString();

                // Call the stored procedure UpdateDefectGroupXML with XML data

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand("USP_SaveDefectGroupXML", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@DefectGroupData", xmlData);
                        command.ExecuteNonQuery();
                    }
                }

                Msg = "Saved";
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                Msg = string.Empty;
            }
            //return Msg;
            return new JsonResult(Msg);
        }

        #endregion

        #region :::UpdateLocale /Mithun:::
        /// <summary>
        /// To Update Local
        /// </summary>
        /// <param name="UpdateLocaleDefectGroupObj"></param>
        /// <param name="constring"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult UpdateLocale(UpdateLocaleDefectGroupList UpdateLocaleDefectGroupObj, string constring, int LogException)
        {
            int DefectGroupLocaleID = 0;
            var x = default(dynamic);
            try
            {
                HD_DefectGroupLocale DLRow = null;
                JObject jObj = JObject.Parse(UpdateLocaleDefectGroupObj.data);
                DLRow = jObj.ToObject<HD_DefectGroupLocale>();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    if (DLRow.DefectGroupLocale_ID != 0)
                    {
                        // Update existing record
                        string updateQuery = @"
                    UPDATE HD_DefectGroupLocale 
                    SET 
                        DefectGroup_ShortName = @DefectGroup_ShortName,
                        DefectGroupDescription = @DefectGroupDescription
                    WHERE 
                        DefectGroupLocale_ID = @DefectGroupLocale_ID";

                        using (SqlCommand cmd = new SqlCommand(updateQuery, connection))
                        {
                            cmd.Parameters.AddWithValue("@DefectGroup_ShortName", Common.DecryptString(DLRow.DefectGroup_ShortName));
                            cmd.Parameters.AddWithValue("@DefectGroupDescription", Common.DecryptString(DLRow.DefectGroupDescription));
                            cmd.Parameters.AddWithValue("@DefectGroupLocale_ID", DLRow.DefectGroupLocale_ID);

                            cmd.ExecuteNonQuery();
                        }

                        DefectGroupLocaleID = DLRow.DefectGroupLocale_ID;

                        //gbl.InsertGPSDetails(
                        //    Convert.ToInt32(UpdateLocaleDefectGroupObj.Company_ID),
                        //    Convert.ToInt32(UpdateLocaleDefectGroupObj.Branch),
                        //    Convert.ToInt32(UpdateLocaleDefectGroupObj.User_ID),
                        //    Convert.ToInt32(Common.GetObjectID("CoreDefectGroupMaster",constring)),
                        //    DLRow.DefectGroup_ID, 0, 0, "Update", false,
                        //    Convert.ToInt32(UpdateLocaleDefectGroupObj.MenuID));
                    }
                    else
                    {
                        // Insert new record
                        string insertQuery = @"
                    INSERT INTO HD_DefectGroupLocale (DefectGroup_ShortName, DefectGroupDescription, DefectGroup_ID) 
                    VALUES (@DefectGroup_ShortName, @DefectGroupDescription, @DefectGroup_ID);
                    SELECT SCOPE_IDENTITY();";

                        using (SqlCommand cmd = new SqlCommand(insertQuery, connection))
                        {
                            cmd.Parameters.AddWithValue("@DefectGroup_ShortName", Common.DecryptString(DLRow.DefectGroup_ShortName));
                            cmd.Parameters.AddWithValue("@DefectGroupDescription", Common.DecryptString(DLRow.DefectGroupDescription));
                            cmd.Parameters.AddWithValue("@DefectGroup_ID", DLRow.DefectGroup_ID);

                            DefectGroupLocaleID = Convert.ToInt32(cmd.ExecuteScalar());
                        }
                        //gbl.InsertGPSDetails(
                        //   Convert.ToInt32(UpdateLocaleDefectGroupObj.Company_ID),
                        //   Convert.ToInt32(UpdateLocaleDefectGroupObj.Branch),
                        //   Convert.ToInt32(UpdateLocaleDefectGroupObj.User_ID),
                        //   Convert.ToInt32(Common.GetObjectID("CoreDefectGroupMaster", constring)),
                        //   DLRow.DefectGroup_ID, 0, 0, "Insert", false,
                        //   Convert.ToInt32(UpdateLocaleDefectGroupObj.MenuID));
                    }
                }

                x = new
                {
                    DefectGroupLocaleID = DefectGroupLocaleID
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(x, JsonRequestBehavior.AllowGet);
            return new JsonResult(x);
        }


        #endregion

        #region :::Delete /Mithun:::
        /// <summary>
        /// Delete
        /// </summary>
        /// <returns></returns>
        public static IActionResult Delete(DeleteDefectGroupList DeleteDefectGroupObj, string constring, int LogException)
        {
            string Msg = string.Empty;
            int Count = 0;
            JTokenReader jTR = null;
            try
            {
                JObject jObj = JObject.Parse(DeleteDefectGroupObj.key);
                Count = jObj["rows"].Count();
                int CompanyID = Convert.ToInt32(DeleteDefectGroupObj.Company_ID);
                int ID = 0;


                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    SqlTransaction transaction = connection.BeginTransaction();

                    try
                    {
                        for (int i = 0; i < Count; i++)
                        {
                            jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                            jTR.Read();
                            ID = Convert.ToInt32(jTR.Value);

                            // Delete from HD_DefectGroup
                            string deleteDefectGroupQuery = "DELETE FROM HD_DefectGroup WHERE DefectGroup_ID = @DefectGroup_ID AND Company_ID = @Company_ID";
                            using (SqlCommand command = new SqlCommand(deleteDefectGroupQuery, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@DefectGroup_ID", ID);
                                command.Parameters.AddWithValue("@Company_ID", CompanyID);
                                command.ExecuteNonQuery();
                            }

                            // Delete from HD_DefectGroupLocale
                            string deleteDefectGroupLocaleQuery = "DELETE FROM HD_DefectGroupLocale WHERE DefectGroup_ID = @DefectGroup_ID";
                            using (SqlCommand command = new SqlCommand(deleteDefectGroupLocaleQuery, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@DefectGroup_ID", ID);
                                command.ExecuteNonQuery();
                            }

                            // gbl.InsertGPSDetails(Convert.ToInt32(DeleteDefectGroupObj.Company_ID), Convert.ToInt32(DeleteDefectGroupObj.Branch), Convert.ToInt32(DeleteDefectGroupObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreDefectGroupMaster",constring)), ID, 0, 0, "Delete", false, Convert.ToInt32(DeleteDefectGroupObj.MenuID));
                        }

                        transaction.Commit();
                        Msg += CommonFunctionalities.GetResourceString(DeleteDefectGroupObj.GeneralCulture.ToString(), "deletedsuccessfully").ToString();
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        if (ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                        {
                            Msg += CommonFunctionalities.GetResourceString(DeleteDefectGroupObj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                        }
                        else
                        {
                            throw; // Rethrow the exception if it's not the expected one
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle other exceptions here
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Msg;
            return new JsonResult(Msg);
        }

        #endregion

        #region :::CheckDefectGroup /Mithun:::
        /// <summary>
        /// To Check Defect Group
        /// </summary>
        /// <param name="IssueAreaID"></param>
        /// <param name="DefectGroupName"></param>
        /// <param name="DefectGroupID"></param>
        /// <returns></returns>
        public static IActionResult CheckDefectGroup(CheckDefectGroupList CheckDefectGroupObj, string constring, int LogException)
        {
            int Count = 0;
            int CompanyID = Convert.ToInt32(CheckDefectGroupObj.Company_ID);
            try
            {
                string DefectGroupName = Uri.UnescapeDataString(CheckDefectGroupObj.DefectGroupName);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    using (SqlCommand cmd = new SqlCommand("UP_AMERP_CheckDefectGroupCount", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@DefectGroupID", CheckDefectGroupObj.DefectGroupID);
                        cmd.Parameters.AddWithValue("@IssueAreaID", CheckDefectGroupObj.IssueAreaID);
                        cmd.Parameters.AddWithValue("@DefectGroupName", DefectGroupName);
                        cmd.Parameters.AddWithValue("@CompanyID", CompanyID);

                        Count = (int)cmd.ExecuteScalar();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };

            }
            //return Count;
            return new JsonResult(Count);
        }

        #endregion

        #region :::CheckDefectGroupCode /Mithun:::
        /// <summary>
        /// To Check Defect Group Code
        /// </summary>
        /// <param name="IssueAreaID"></param>
        /// <param name="DefectGroupShortName"></param>
        /// <param name="DefectGroupID"></param>
        /// <returns></returns>
        public static IActionResult CheckDefectGroupCode(CheckDefectGroupCodeList CheckDefectGroupCodeObj, string constring, int LogException)
        {
            int Count = 0;
            int CompanyID = Convert.ToInt32(CheckDefectGroupCodeObj.Company_ID);
            try
            {
                string DefectGroupShortName = Uri.UnescapeDataString(CheckDefectGroupCodeObj.DefectGroupShortName);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    using (SqlCommand cmd = new SqlCommand("UP_AMERP_CheckDefectGroupCode", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@DefectGroupID", CheckDefectGroupCodeObj.DefectGroupID);
                        cmd.Parameters.AddWithValue("@IssueAreaID", CheckDefectGroupCodeObj.IssueAreaID);
                        cmd.Parameters.AddWithValue("@DefectGroupShortName", DefectGroupShortName);
                        cmd.Parameters.AddWithValue("@CompanyID", CompanyID);

                        Count = (int)cmd.ExecuteScalar();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            //return Count;
            return new JsonResult(Count);
        }

        #endregion


        #region :::CheckDefectGroupNameLocale /Mithun:::
        /// <summary>
        /// To Check Defect Group Name Locale
        /// </summary>
        /// <param name="CheckDefectGroupNameLocaleObj"></param>
        /// <param name="constring"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult CheckDefectGroupNameLocale(CheckDefectGroupNameLocaleList CheckDefectGroupNameLocaleObj, string constring, int LogException)
        {
            int Count = 0;
            try
            {
                string DefectGroup = Common.DecryptString(CheckDefectGroupNameLocaleObj.DefectGroupName);
                string Code = Common.DecryptString(CheckDefectGroupNameLocaleObj.DefectGroupCode);
                int LanguageID = Convert.ToInt32(CheckDefectGroupNameLocaleObj.UserLanguageID);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Check for DefectGroup Code
                    using (SqlCommand cmd = new SqlCommand("UP_AMERP_CheckDefectGroupCodeLocale", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@IssueAreaID", CheckDefectGroupNameLocaleObj.IssueAreaID);
                        cmd.Parameters.AddWithValue("@Code", Code);
                        cmd.Parameters.AddWithValue("@DefectGroupLocaleID", CheckDefectGroupNameLocaleObj.DefectGroupLocaleID);
                        cmd.Parameters.AddWithValue("@LanguageID", LanguageID);

                        int countCode = (int)cmd.ExecuteScalar();
                        if (countCode > 0)
                        {
                            Count = 1;
                        }
                    }

                    if (Count == 0)
                    {
                        // Check for DefectGroup Name
                        using (SqlCommand cmd = new SqlCommand("UP_AMERP_CheckDefectGroupName", connection))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@IssueAreaID", CheckDefectGroupNameLocaleObj.IssueAreaID);
                            cmd.Parameters.AddWithValue("@DefectGroup", DefectGroup);
                            cmd.Parameters.AddWithValue("@DefectGroupLocaleID", CheckDefectGroupNameLocaleObj.DefectGroupLocaleID);
                            cmd.Parameters.AddWithValue("@LanguageID", LanguageID);

                            int countName = (int)cmd.ExecuteScalar();
                            if (countName > 0)
                            {
                                Count = 2;
                            }
                        }
                    }

                    if (Count == 0)
                    {
                        // Check for both DefectGroup Code and Name
                        using (SqlCommand cmd = new SqlCommand("UP_AMERP_CheckDefectGroupCodeAndName", connection))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@IssueAreaID", CheckDefectGroupNameLocaleObj.IssueAreaID);
                            cmd.Parameters.AddWithValue("@Code", Code);
                            cmd.Parameters.AddWithValue("@DefectGroup", DefectGroup);
                            cmd.Parameters.AddWithValue("@DefectGroupLocaleID", CheckDefectGroupNameLocaleObj.DefectGroupLocaleID);
                            cmd.Parameters.AddWithValue("@LanguageID", LanguageID);

                            int countBoth = (int)cmd.ExecuteScalar();
                            if (countBoth > 0)
                            {
                                Count = 3;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(Count);
        }

        #endregion


        #region::: Data for Export:::

        private static IQueryable<DefectGroupMaster> GetDefectGroupMaster(SelectDefectGroupList ExportObj, string constring)
        {
            List<DefectGroupMaster> defectGroupMasterList = new List<DefectGroupMaster>();
            string YesE = CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture?.ToString(), "yes")?.ToString() ?? string.Empty;

            string NoE = CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture?.ToString(), "No")?.ToString() ?? string.Empty;

            string YesL = CommonFunctionalities.GetResourceString(ExportObj.UserCulture?.ToString(), "yes")?.ToString() ?? string.Empty;

            string NoL = CommonFunctionalities.GetResourceString(ExportObj.UserCulture?.ToString(), "no")?.ToString() ?? string.Empty;
            using (SqlConnection connection = new SqlConnection(constring))
            {
                connection.Open();

                // Call the first stored procedure
                using (SqlCommand cmd = new SqlCommand("UP_SEL_GetDefectGroups", connection))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@IssueAreaID", ExportObj.IssueAreaID);
                    cmd.Parameters.AddWithValue("@CompanyID", ExportObj.Company_ID);

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var defectGroup = new DefectGroupMaster()
                            {
                                DefectGroup_ID = reader.GetInt32(reader.GetOrdinal("DefectGroup_ID")),
                                DefectGroup_ShortName = reader.GetString(reader.GetOrdinal("DefectGroup_ShortName")),
                                DefectGroupDescription = reader.GetString(reader.GetOrdinal("DefectGroupDescription")),
                                DefectGroup_IsActive = reader.GetBoolean(reader.GetOrdinal("DefectGroup_IsActive")) ? YesE : NoE
                            };
                            defectGroupMasterList.Add(defectGroup);
                        }
                    }
                }

                // Call the second stored procedure if needed
                if (ExportObj.LanguageID != ExportObj.GeneralLanguageID)
                {
                    using (SqlCommand cmd = new SqlCommand("UP_SEL_GetLocalizedDefectGroups", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@IssueAreaID", ExportObj.IssueAreaID);
                        cmd.Parameters.AddWithValue("@CompanyID", ExportObj.Company_ID);
                        cmd.Parameters.AddWithValue("@LanguageID", ExportObj.LanguageID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var defectGroup = new DefectGroupMaster()
                                {
                                    DefectGroup_ID = reader.GetInt32(reader.GetOrdinal("DefectGroup_ID")),
                                    DefectGroup_ShortName = reader.GetString(reader.GetOrdinal("DefectGroup_ShortName")),
                                    DefectGroupDescription = reader.GetString(reader.GetOrdinal("DefectGroupDescription")),
                                    DefectGroup_IsActive = reader.GetBoolean(reader.GetOrdinal("DefectGroup_IsActive")) ? YesL : NoL
                                };
                                defectGroupMasterList.Add(defectGroup);
                            }
                        }
                    }
                }
            }

            return defectGroupMasterList.AsQueryable();
        }

        #endregion


        #region::: Export /Mithun:::

        public static async Task<object> Export(SelectDefectGroupList ExportObj, string constring, int LogException, string filters, string Query, string sidx, string sord)
        {
            DataTable Dt = new DataTable();
            DataTable DtAlignment = new DataTable();
            int Count = 0;
            Filters appliedFilters = null;
            try
            {
                // Retrieve the data
                IQueryable<DefectGroupMaster> IQDefectGroupMaster = GetDefectGroupMaster(ExportObj, constring);

                // Apply filters if _search is true
                if (filters != "null" && filters != "undefined")
                {
                    string decryptedFiltersOnce = Common.DecryptString(filters);
                    string decryptedFiltersTwice = Common.DecryptString(decryptedFiltersOnce);

                    appliedFilters = JObject.Parse(decryptedFiltersTwice).ToObject<Filters>();

                    if (appliedFilters.rules.Count() > 0)
                    {
                        IQDefectGroupMaster = IQDefectGroupMaster.FilterSearch<DefectGroupMaster>(appliedFilters);
                    }
                }

                // Apply advanced filtering if advnce is true
                else if (Query != null && Query != "undefined")
                {
                    AdvanceFilter appliedAdvanceFilters = JObject.Parse(Query).ToObject<AdvanceFilter>();
                    IQDefectGroupMaster = IQDefectGroupMaster.AdvanceSearch<DefectGroupMaster>(appliedAdvanceFilters);
                }

                // Apply sorting and paging
                IQDefectGroupMaster = IQDefectGroupMaster.OrderByField<DefectGroupMaster>(sidx, sord);

                // Set up DataTable columns for export
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "code").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "DefectGroup").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "Active").ToString());

                DtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "code").ToString());
                DtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "DefectGroup").ToString());
                DtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "Active").ToString());
                DtAlignment.Rows.Add(0, 0, 0);

                // Get the count of filtered and sorted data
                Count = IQDefectGroupMaster.Count();
                if (Count > 0)
                {
                    // Populate the DataTable for export
                    for (int i = 0; i < Count; i++)
                    {
                        var defectGroup = IQDefectGroupMaster.ElementAt(i);
                        Dt.Rows.Add(defectGroup.DefectGroup_ShortName, defectGroup.DefectGroupDescription, defectGroup.DefectGroup_IsActive);
                    }

                    DataTable DtCriteria = new DataTable();

                    DtAlignment.Columns.Add("code");
                    DtAlignment.Columns.Add("DefectGroup");
                    DtAlignment.Columns.Add("Active");
                    DtAlignment.Rows.Add(0, 0, 1);

                    DtCriteria.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "IssueArea").ToString());
                    DtCriteria.Rows.Add(Common.DecryptString(ExportObj.IssueAreaDescription));
                    ReportExportList reportExportList = new ReportExportList
                    {
                        Company_ID = ExportObj.Company_ID, // Assuming this is available in ExportObj
                        Branch = ExportObj.Branch_ID.ToString(),
                        GeneralLanguageID = ExportObj.GeneralLanguageID,
                        UserLanguageID = ExportObj.LanguageID,
                        Options = DtCriteria,
                        dt = Dt,
                        Alignment = DtAlignment,
                        FileName = "DefectGroup", // Set a default or dynamic filename
                        Header = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "DefectGroup").ToString(), // Set a default or dynamic header
                        exprtType = ExportObj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                        UserCulture = ExportObj.UserCulture
                    };




                    // Set up additional DataTable for issue area description
                    DataTable Dt1 = new DataTable();
                    Dt1.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "IssueArea").ToString());
                    Dt1.Rows.Add(Common.DecryptString(ExportObj.IssueAreaDescription));

                    // Export the data
                    // ReportExport.Export(exprtType, Dt, Dt1, DtAlignment, "DefectGroup", CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "DefectGroup").ToString());


                    var result = await ReportExport.Export(reportExportList, constring, LogException);
                    return result.Value;
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return null;
        }

        #endregion
    }


    public class SelectDefectGroupReferenceMasterList
    {
        public int Company_ID { get; set; }
        public int UserLanguageID { get; set; }
        public string UserLanguageCode { get; set; }
        public string GeneralLanguageCode { get; set; }
    }
    public class CheckDefectGroupNameLocaleList
    {
        public int UserLanguageID { get; set; }
        public int IssueAreaID { get; set; }
        public int DefectGroupLocaleID { get; set; }
        public string DefectGroupName { get; set; }
        public string DefectGroupCode { get; set; }
    }
    public class CheckDefectGroupCodeList
    {
        public int Company_ID { get; set; }
        public int IssueAreaID { get; set; }
        public int DefectGroupID { get; set; }
        public string DefectGroupShortName { get; set; }
    }
    public class CheckDefectGroupList
    {
        public int Company_ID { get; set; }
        public int IssueAreaID { get; set; }
        public int DefectGroupID { get; set; }
        public string DefectGroupName { get; set; }
    }
    public class DeleteDefectGroupList
    {
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public int LanguageID { get; set; }
        public int MenuID { get; set; }
        public int Branch { get; set; }
        public string GeneralCulture { get; set; }
        public string key { get; set; }
    }
    public class UpdateLocaleDefectGroupList
    {
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public int LanguageID { get; set; }
        public int MenuID { get; set; }
        public int Branch { get; set; }
        public string data { get; set; }
    }
    public class SaveDefectGroupList
    {
        public string data { get; set; }
        public int Company_ID { get; set; }
        public DateTime LoggedINDateTime { get; set; }
        public List<GNM_User> UserDetails { get; set; }
    }
    public class SelectParticularDefectGroupList
    {
        public int UserLanguageID { get; set; }
        public int DefectGroupID { get; set; }
    }
    public class SelectDefectGroupList
    {
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public string Branch { get; set; }
        public int IssueAreaID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int LanguageID { get; set; }
        public int exprtType { get; set; }
        public string GeneralCulture { get; set; }
        public string UserCulture { get; set; }
        public string sidx { get; set; }
        public string sord { get; set; }
        public string IssueAreaDescription { get; set; }
        public string filters { get; set; }
        public string Query { get; set; }
        public bool _search { get; set; }
        public bool advnce { get; set; }
    }

    public class DefectGroupMaster
    {
        public int DefectGroup_ID
        {
            get;
            set;
        }

        public string DefectGroup_ShortName
        {
            get;
            set;
        }

        public string DefectGroupDescription
        {
            get;
            set;
        }

        public string DefectGroup_IsActive
        {
            get;
            set;
        }

        public int Company_ID
        {
            get;
            set;
        }
    }


    public partial class GNM_User
    {
        public GNM_User()
        {
            this.GNM_ATTACHMENTDETAIL = new HashSet<GNM_ATTACHMENTDETAIL>();
            this.GNM_GPSLOG = new HashSet<GNM_GPSLOG>();
            this.GNM_MODELATTACHMENTDETAIL = new HashSet<GNM_MODELATTACHMENTDETAIL>();
            this.GNM_REF_ATTACHMENTDETAIL = new HashSet<GNM_REF_ATTACHMENTDETAIL>();
            this.GNM_UserCompany = new HashSet<GNM_UserCompany>();
            this.GNM_UserLocale = new HashSet<GNM_UserLocale>();
            this.GNM_UserRole = new HashSet<GNM_UserRole>();
            this.GNM_WALKAROUNDATTACHMENTDETAIL = new HashSet<GNM_WALKAROUNDATTACHMENTDETAIL>();
            this.GNM_TAMSAdjustmentLog = new HashSet<GNM_TAMSAdjustmentLog>();
            this.GNM_RegilarizeWindowCloseLog = new HashSet<GNM_RegilarizeWindowCloseLog>();
            this.GNM_DashboardConfiguration = new HashSet<GNM_DashboardConfiguration>();
        }

        public int User_ID { get; set; }
        public string User_Name { get; set; }
        public string User_LoginID { get; set; }
        public string User_Password { get; set; }
        public bool User_IsActive { get; set; }
        public bool User_Locked { get; set; }
        public Nullable<int> User_LoginCount { get; set; }
        public Nullable<int> User_FailedCount { get; set; }
        public int Company_ID { get; set; }
        public int Language_ID { get; set; }
        public byte User_Type_ID { get; set; }
        public Nullable<int> Employee_ID { get; set; }
        public Nullable<int> Partner_ID { get; set; }
        public string LandingPage { get; set; }
        public string User_IPAddress { get; set; }
        public Nullable<int> WareHouse_ID { get; set; }
        public string ReleaseVersionPopup { get; set; }

        public virtual ICollection<GNM_ATTACHMENTDETAIL> GNM_ATTACHMENTDETAIL { get; set; }
        public virtual ICollection<GNM_GPSLOG> GNM_GPSLOG { get; set; }
        public virtual ICollection<GNM_MODELATTACHMENTDETAIL> GNM_MODELATTACHMENTDETAIL { get; set; }
        public virtual ICollection<GNM_REF_ATTACHMENTDETAIL> GNM_REF_ATTACHMENTDETAIL { get; set; }
        public virtual ICollection<GNM_UserCompany> GNM_UserCompany { get; set; }
        public virtual ICollection<GNM_UserLocale> GNM_UserLocale { get; set; }
        public virtual ICollection<GNM_UserRole> GNM_UserRole { get; set; }
        public virtual ICollection<GNM_WALKAROUNDATTACHMENTDETAIL> GNM_WALKAROUNDATTACHMENTDETAIL { get; set; }
        public virtual ICollection<GNM_TAMSAdjustmentLog> GNM_TAMSAdjustmentLog { get; set; }
        public virtual ICollection<GNM_RegilarizeWindowCloseLog> GNM_RegilarizeWindowCloseLog { get; set; }
        public virtual ICollection<GNM_DashboardConfiguration> GNM_DashboardConfiguration { get; set; }
    }
    public partial class GNM_ATTACHMENTDETAIL
    {
        public int ATTACHMENTDETAIL_ID { get; set; }
        public string FILENAME { get; set; }
        public string FILEDESCRIPTION { get; set; }
        public int UPLOADBY { get; set; }
        public System.DateTime UPLOADDATE { get; set; }
        public string REMARKS { get; set; }
        public int COMPANY_ID { get; set; }
        public int OBJECT_ID { get; set; }
        public int TRANSACTION_ID { get; set; }
        public Nullable<int> DETAIL_ID { get; set; }
        public string TABLE_NAME { get; set; }

        public virtual GNM_Object GNM_Object { get; set; }
        public virtual GNM_User GNM_User { get; set; }
    }

    public partial class GNM_GPSLOG
    {
        public int GPSLOG_ID { get; set; }
        public int OBJECT_ID { get; set; }
        public int RECORD_ID { get; set; }
        public double LATITUDE { get; set; }
        public double LONGITUDE { get; set; }
        public System.DateTime MODIFIEDDATE { get; set; }
        public int USER_ID { get; set; }
        public int COMPANY_ID { get; set; }
        public int BRANCH_ID { get; set; }
        public string ActionName { get; set; }
        public Nullable<bool> IsFromMobile { get; set; }
        public Nullable<System.DateTime> LOGINDATETIME { get; set; }
        public Nullable<System.DateTime> LOGOUTDATETIME { get; set; }
        public Nullable<int> Menu_ID { get; set; }
        public string HostName { get; set; }
        public string AddressFamily { get; set; }
        public string isTrustedHost { get; set; }
        public string AddressList { get; set; }
        public string LocalIPAddress { get; set; }

        public virtual GNM_Object GNM_Object { get; set; }
        public virtual GNM_User GNM_User { get; set; }
    }

    public partial class GNM_MODELATTACHMENTDETAIL
    {
        public int MODELATTACHMENTDETAIL_ID { get; set; }
        public int MODEL_ID { get; set; }
        public string FILENAME { get; set; }
        public string FILEDESCRIPTION { get; set; }
        public int UPLOADBY { get; set; }
        public System.DateTime UPLOADDATE { get; set; }
        public int OBJECT_ID { get; set; }
        public bool AUTODISPLAY { get; set; }
        public bool PRINT { get; set; }

        public virtual GNM_Object GNM_Object { get; set; }
        public virtual GNM_User GNM_User { get; set; }
    }

    public partial class GNM_REF_ATTACHMENTDETAIL
    {
        public int REFATTACHMENTDETAIL_ID { get; set; }
        public string FILENAME { get; set; }
        public string FILEDESCRIPTION { get; set; }
        public int UPLOADBY { get; set; }
        public System.DateTime UPLOADDATE { get; set; }
        public string REMARKS { get; set; }
        public int COMPANY_ID { get; set; }
        public int OBJECT_ID { get; set; }
        public int TRANSACTION_ID { get; set; }
        public Nullable<int> DETAIL_ID { get; set; }
        public string TABLE_NAME { get; set; }
        public Nullable<bool> ISACTIVE { get; set; }

        public virtual GNM_Object GNM_Object { get; set; }
        public virtual GNM_User GNM_User { get; set; }
    }

    public partial class GNM_UserCompany
    {
        public int UserCompanyDet_ID { get; set; }
        public int User_ID { get; set; }
        public int Company_ID { get; set; }

        public virtual GNM_User GNM_User { get; set; }
    }
    public partial class GNM_UserLocale
    {
        public int User_Locale_ID { get; set; }
        public int User_ID { get; set; }
        public int Language_ID { get; set; }
        public string User_Name { get; set; }

        public virtual GNM_User GNM_User { get; set; }
    }
    public partial class GNM_UserRole
    {
        public int UserRole_ID { get; set; }
        public int User_ID { get; set; }
        public int Role_ID { get; set; }

        public virtual GNM_Role GNM_Role { get; set; }
        public virtual GNM_User GNM_User { get; set; }
    }
    public partial class GNM_WALKAROUNDATTACHMENTDETAIL
    {
        public int ATTACHMENTDETAIL_ID { get; set; }
        public string FILENAME { get; set; }
        public string FILEDESCRIPTION { get; set; }
        public int UPLOADBY { get; set; }
        public System.DateTime UPLOADDATE { get; set; }
        public string REMARKS { get; set; }
        public int COMPANY_ID { get; set; }
        public int OBJECT_ID { get; set; }
        public int TRANSACTION_ID { get; set; }
        public Nullable<int> DETAIL_ID { get; set; }
        public string TABLE_NAME { get; set; }
        public string TYPE { get; set; }

        public virtual GNM_Object GNM_Object { get; set; }
        public virtual GNM_User GNM_User { get; set; }
    }

    public partial class GNM_TAMSAdjustmentLog
    {
        public int TAMSAdjustmentLog_ID { get; set; }
        public int Employee_ID { get; set; }
        public int ActivityType { get; set; }
        public System.DateTime Date { get; set; }
        public Nullable<System.DateTime> ActualStartDatetime { get; set; }
        public Nullable<System.DateTime> ActualEndDatetime { get; set; }
        public System.DateTime AdjustedStartDatetime { get; set; }
        public System.DateTime AdjustedEndDatetime { get; set; }
        public int AdjustedBy_ID { get; set; }
        public System.DateTime AdjustedDate { get; set; }
        public string LaborCode { get; set; }

        public virtual GNM_User GNM_User { get; set; }
    }
    public partial class GNM_RegilarizeWindowCloseLog
    {
        public int WindowCloseLog_ID { get; set; }
        public System.DateTime WindowClosDateTime { get; set; }
        public int USER_ID { get; set; }
        public int Employee_ID { get; set; }
        public int BRANCH_ID { get; set; }
        public Nullable<System.DateTime> FromDateTime { get; set; }
        public Nullable<System.DateTime> ToDateTime { get; set; }
        public int Shift_ID { get; set; }

        public virtual GNM_User GNM_User { get; set; }
    }
    public partial class GNM_DashboardConfiguration
    {
        public int DashboardConfiguration_ID { get; set; }
        public Nullable<int> User_ID { get; set; }
        public Nullable<int> Object_ID { get; set; }
        public Nullable<bool> IsVisible { get; set; }
        public Nullable<bool> IsExpand { get; set; }

        public virtual GNM_Object GNM_Object { get; set; }
        public virtual GNM_User GNM_User { get; set; }
    }
    public partial class GNM_Object
    {
        public GNM_Object()
        {
            this.GNM_RoleObject = new HashSet<GNM_RoleObject>();
            this.GNM_PrefixSuffix = new HashSet<GNM_PrefixSuffix>();
            this.GNM_GPSLOG = new HashSet<GNM_GPSLOG>();
            this.GNM_ATTACHMENTDETAIL = new HashSet<GNM_ATTACHMENTDETAIL>();
            this.GNM_MODELATTACHMENTDETAIL = new HashSet<GNM_MODELATTACHMENTDETAIL>();
            this.GNM_REF_ATTACHMENTDETAIL = new HashSet<GNM_REF_ATTACHMENTDETAIL>();
            this.GNM_WALKAROUNDATTACHMENTDETAIL = new HashSet<GNM_WALKAROUNDATTACHMENTDETAIL>();
            this.GNM_DashboardConfiguration = new HashSet<GNM_DashboardConfiguration>();
        }

        public int Object_ID { get; set; }
        public string Object_Name { get; set; }
        public string Read_Action { get; set; }
        public string Create_Action { get; set; }
        public string Update_Action { get; set; }
        public string Delete_Action { get; set; }
        public string Export_Action { get; set; }
        public string Print_Action { get; set; }
        public bool Object_IsActive { get; set; }
        public string Object_Description { get; set; }
        public string Import_Action { get; set; }
        public string Object_Type { get; set; }

        public virtual ICollection<GNM_RoleObject> GNM_RoleObject { get; set; }
        public virtual ICollection<GNM_PrefixSuffix> GNM_PrefixSuffix { get; set; }
        public virtual ICollection<GNM_GPSLOG> GNM_GPSLOG { get; set; }
        public virtual ICollection<GNM_ATTACHMENTDETAIL> GNM_ATTACHMENTDETAIL { get; set; }
        public virtual ICollection<GNM_MODELATTACHMENTDETAIL> GNM_MODELATTACHMENTDETAIL { get; set; }
        public virtual ICollection<GNM_REF_ATTACHMENTDETAIL> GNM_REF_ATTACHMENTDETAIL { get; set; }
        public virtual ICollection<GNM_WALKAROUNDATTACHMENTDETAIL> GNM_WALKAROUNDATTACHMENTDETAIL { get; set; }
        public virtual ICollection<GNM_DashboardConfiguration> GNM_DashboardConfiguration { get; set; }
    }

    public partial class GNM_RoleObject
    {
        public int RoleObject_ID { get; set; }
        public int Role_ID { get; set; }
        public int Object_ID { get; set; }
        public bool RoleObject_Create { get; set; }
        public bool RoleObject_Read { get; set; }
        public bool RoleObject_Update { get; set; }
        public bool RoleObject_Delete { get; set; }
        public bool RoleObject_Print { get; set; }
        public bool RoleObject_Export { get; set; }
        public bool RoleObject_Import { get; set; }

        public virtual GNM_Role GNM_Role { get; set; }
        public virtual GNM_Object GNM_Object { get; set; }
    }
    public partial class GNM_PrefixSuffix
    {
        public int PrefixSuffix_ID { get; set; }
        public int Company_ID { get; set; }
        public Nullable<int> Branch_ID { get; set; }
        public int Object_ID { get; set; }
        public int Start_Number { get; set; }
        public string Prefix { get; set; }
        public string Suffix { get; set; }
        public System.DateTime FromDate { get; set; }
        public System.DateTime ToDate { get; set; }
        public int ModifiedBY { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public Nullable<int> FinancialYear { get; set; }
        public Nullable<int> Company_FinancialYear_ID { get; set; }

        public virtual GNM_Object GNM_Object { get; set; }
    }

    public partial class GNM_Role
    {
        public GNM_Role()
        {
            this.GNM_RoleObject = new HashSet<GNM_RoleObject>();
            this.GNM_UserRole = new HashSet<GNM_UserRole>();
        }

        public int Role_ID { get; set; }
        public int Company_ID { get; set; }
        public string Role_Name { get; set; }

        public virtual ICollection<GNM_RoleObject> GNM_RoleObject { get; set; }
        public virtual ICollection<GNM_UserRole> GNM_UserRole { get; set; }
    }


    public partial class GNM_RefMaster
    {
        public GNM_RefMaster()
        {
            this.GNM_RefMasterDetail = new HashSet<GNM_RefMasterDetail>();
            this.GNM_RefMasterDetailLocale = new HashSet<GNM_RefMasterDetailLocale>();
        }

        public int RefMaster_ID { get; set; }
        public string RefMaster_Name { get; set; }
        public Nullable<bool> IsCompanySpecific { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public bool IsSystemMaster { get; set; }

        public virtual ICollection<GNM_RefMasterDetail> GNM_RefMasterDetail { get; set; }
        public virtual ICollection<GNM_RefMasterDetailLocale> GNM_RefMasterDetailLocale { get; set; }
    }

    public partial class GNM_RefMasterDetailLocale
    {
        public int RefMasterDetailLocale_ID { get; set; }
        public int RefMasterDetail_ID { get; set; }
        public int RefMaster_ID { get; set; }
        public string RefMasterDetail_Short_Name { get; set; }
        public string RefMasterDetail_Name { get; set; }
        public int Language_ID { get; set; }
        public bool RefMasterDetail_IsActive { get; set; }

        public virtual GNM_RefMaster GNM_RefMaster { get; set; }
        public virtual GNM_RefMasterDetail GNM_RefMasterDetail { get; set; }
    }

    public partial class HD_DefectGroupLocale
    {
        public int DefectGroupLocale_ID { get; set; }
        public int DefectGroup_ID { get; set; }
        public string DefectGroup_ShortName { get; set; }
        public string DefectGroupDescription { get; set; }
        public int Language_ID { get; set; }

        public virtual HD_DefectGroup HD_DefectGroup { get; set; }
    }
    public partial class HD_DefectGroup
    {
        public HD_DefectGroup()
        {
            this.HD_DefectGroupLocale = new HashSet<HD_DefectGroupLocale>();
        }

        public int DefectGroup_ID { get; set; }
        public int IssueArea_ID { get; set; }
        public string DefectGroup_ShortName { get; set; }
        public string DefectGroupDescription { get; set; }
        public bool DefectGroup_IsActive { get; set; }
        public int Company_ID { get; set; }

        public virtual ICollection<HD_DefectGroupLocale> HD_DefectGroupLocale { get; set; }
    }
}
