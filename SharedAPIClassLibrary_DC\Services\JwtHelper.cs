﻿using Microsoft.IdentityModel.Tokens;
using System;
using System.Configuration;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace SharedAPIClassLibrary_AMERP.Services
{
    /// <summary>
    /// DK - 09-FEB-2025 - use this for Sharing with SAP or any other systems for API Endpoints access - JWT token Jen
    /// </summary>
    public class JwtHelper
    {
        private static readonly string SecretKey = ConfigurationManager.AppSettings["DCAPIKEY"]; // Secure storage recommended
        private static readonly string Issuer = ConfigurationManager.AppSettings["DCIssuerKEY"];
        private static readonly string Audience = ConfigurationManager.AppSettings["DCParty"];
        private static readonly int TokenExpiryMinutes = Convert.ToInt32(ConfigurationManager.AppSettings["DCAPITimeOut"]);

        /// <summary>
        /// Generates a secure JWT token after validating user credentials.
        /// </summary>
        /// <param name="username">User's unique identifier</param>
        /// <returns>JWT Token</returns>
        public static string GenerateJwtToken(string username)
        {
            if (string.IsNullOrEmpty(username))
                throw new ArgumentException("Username cannot be null or empty");

            // Encode the secret key properly
            var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(SecretKey));
            var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);

            var claims = new[]
            {
                new Claim(JwtRegisteredClaimNames.Sub, username), // Subject
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()), // Unique Token ID
                new Claim(JwtRegisteredClaimNames.Iat, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64), // Issued At
                new Claim(JwtRegisteredClaimNames.Exp, DateTimeOffset.UtcNow.AddMinutes(TokenExpiryMinutes).ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64), // Expiration
                new Claim(JwtRegisteredClaimNames.Iss, Issuer), // Issuer
                new Claim(JwtRegisteredClaimNames.Aud, Audience) // Audience
            };

            var token = new JwtSecurityToken(
                issuer: Issuer,
                audience: Audience,
                claims: claims,
                expires: DateTime.UtcNow.AddMinutes(TokenExpiryMinutes),
                signingCredentials: credentials
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }
    }
}
