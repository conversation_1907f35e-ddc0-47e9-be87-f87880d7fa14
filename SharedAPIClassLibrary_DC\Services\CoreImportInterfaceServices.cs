﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using OfficeOpenXml;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreImportInterfaceServices
    {
        public static string templateDirectory = "Template";
        public static string BucketFilePath = "quest-partsassist\\EPC_UploadedFiles";
        public static DataTable ActualData = null;
        public static DataTable UploadedColumnNames = null;
        public static List<Attachements> AttachmentData = null;

        #region ::: FillTableNames Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// FillTableNames 
        /// </summary>
        public static IActionResult FillTableNames(string connString)
        {
            var jsonResult = default(dynamic);
            SqlConnection con = null;
            SqlCommand cmd = null;
            SqlDataReader dr = null;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                con = new SqlConnection(connString);
                con.Open(); // Open the connection

                // Use the stored procedure instead of inline SQL
                cmd = new SqlCommand("SP_ImportInterfaceGetTableNames", con);
                cmd.CommandType = CommandType.StoredProcedure; // Specify that it's a stored procedure
                dr = cmd.ExecuteReader();
                DataTable dtTableNames = new DataTable();
                dtTableNames.Load(dr);

                List<TableNames> getDataList = new List<TableNames>();
                for (int i = 0; i < dtTableNames.Rows.Count; i++)
                {
                    getDataList.Add(new TableNames()
                    {
                        tableID = dtTableNames.Rows[i]["tableID"].ToString(),  // Access by column name
                        tableName = dtTableNames.Rows[i]["tableName"].ToString()
                    });
                }

                // Convert the list to the required format
                jsonResult = from a in getDataList
                             select new
                             {
                                 a.tableID,
                                 a.tableName
                             };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            finally
            {
                // Close resources
                if (dr != null) dr.Close(); // Close the reader if it is open
                if (con != null) con.Close(); // Close the connection if it is open
            }

            return new JsonResult(jsonResult);
        }
        #endregion


        #region ::: FillColumnNames Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// FillColumnNames 
        /// </summary>
        public static IActionResult FillColumnNames(string connString, FillColumnNamesList FillColumnNamesobj)
        {
            var jsonResult = default(dynamic);
            SqlConnection con = null;
            SqlCommand cmd = null;
            SqlDataReader dr = null;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                con = new SqlConnection(connString);
                con.Open(); // Open the connection

                // Use the stored procedure instead of inline SQL
                cmd = new SqlCommand("SP_ImportInterfaceGetColumnNames", con);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@TableValue", FillColumnNamesobj.tableValue); // Pass the tableValue parameter

                dr = cmd.ExecuteReader();
                DataTable dtColumnNames = new DataTable();
                dtColumnNames.Load(dr);

                // Store the DataTable in session
                // Storing dtColumnNames
                ColumnDataStorage.DtColumnNames = dtColumnNames;

                List<ColumnNames> getDataList = new List<ColumnNames>();
                for (int i = 0; i < dtColumnNames.Rows.Count; i++)
                {
                    getDataList.Add(new ColumnNames()
                    {
                        columnID = dtColumnNames.Rows[i]["CName"].ToString(),
                        columnName = dtColumnNames.Rows[i]["columnName"].ToString()
                    });
                }

                // Clear and update the dtColumnMapping DataTable
                DataTable dtColumnMapping = ColumnDataStorage.DtColumnMapping;
                dtColumnMapping.Rows.Clear();
                ColumnDataStorage.DtColumnMapping = dtColumnMapping;

                // Convert the list to the required format
                jsonResult = from a in getDataList
                             select new
                             {
                                 a.columnID,
                                 a.columnName
                             };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            finally
            {
                // Close resources
                if (dr != null) dr.Close();
                if (con != null) con.Close();
            }

            return new JsonResult(jsonResult);
        }
        #endregion


        #region ::: ImportInterface Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// ImportInterface 
        /// </summary>
        [HttpPost]
        public static IActionResult ImportInterface(IFormFile postedFile)
        {
            DataTable actualData = null;
            DataTable uploadedColumnNames = null;
            var jsonResult = default(dynamic);
            Common common = new Common();
            int jData = 0;
            if (postedFile != null && postedFile.Length > 0)
            {
                string fileName = postedFile.FileName;
                string contentType = postedFile.ContentType;
                string fileExtension = Path.GetExtension(fileName);
                //string filePath = Path.Combine("C:\\LogSheetExport\\Upload", fileName); // Ensure unique path with filename
                string filePath = Path.Combine(BucketFilePath, fileName); // Ensure unique path with filename
                string filePathNew = BucketFilePath;
                try
                {
                    // Save the uploaded file
                    //using (var stream = new FileStream(filePath, FileMode.Create))
                    //{
                    //    postedFile.CopyTo(stream);
                    //}

                    var RowsAffected = common.UploadFileItemsForImport1((Stream)postedFile, contentType, filePathNew, fileName);

                    // Handle Excel files using EPPlus
                    if (fileExtension.Equals(".xls", StringComparison.OrdinalIgnoreCase) ||
                        fileExtension.Equals(".xlsx", StringComparison.OrdinalIgnoreCase))
                    {
                        using (var package = new ExcelPackage(new FileInfo(filePath)))
                        {
                            var worksheet = package.Workbook.Worksheets[0]; // Get the first worksheet
                            actualData = new DataTable();

                            // Load data into DataTable
                            bool hasHeader = true; // Assuming the first row has headers
                            foreach (var firstRowCell in worksheet.Cells[1, 1, 1, worksheet.Dimension.End.Column])
                            {
                                actualData.Columns.Add(hasHeader ? firstRowCell.Text : $"Column {firstRowCell.Start.Column}");
                            }

                            // Start from row 2 since row 1 contains headers
                            for (var rowNum = 2; rowNum <= worksheet.Dimension.End.Row; rowNum++)
                            {
                                var row = worksheet.Cells[rowNum, 1, rowNum, worksheet.Dimension.End.Column];
                                var newRow = actualData.NewRow();

                                foreach (var cell in row)
                                {
                                    newRow[cell.Start.Column - 1] = cell.Text; // Fill the data
                                }

                                actualData.Rows.Add(newRow);
                            }
                            ActualData = actualData;
                            uploadedColumnNames = new DataTable();
                            uploadedColumnNames.Columns.Add("ColumnNames");
                            uploadedColumnNames.Columns.Add("ColumnDataType");

                            foreach (DataColumn column in actualData.Columns)
                            {
                                uploadedColumnNames.Rows.Add(column.ColumnName, column.DataType.ToString());
                            }
                            UploadedColumnNames = uploadedColumnNames;
                            jData = 1;
                        }
                    }
                    else if (fileExtension.Equals(".csv", StringComparison.OrdinalIgnoreCase))
                    {
                        using (var reader = new StreamReader(filePath))
                        {
                            string str = reader.ReadLine();
                            string[] columns = str.Split(',');

                            // Prepare source column names data
                            DataTable dtSrcColumnName = new DataTable();
                            dtSrcColumnName.Columns.Add("SourceColumnName");

                            foreach (string column in columns)
                            {
                                dtSrcColumnName.Rows.Add(column);
                            }
                            UploadedColumnNames = dtSrcColumnName;

                            actualData = new DataTable();
                            foreach (DataRow row in dtSrcColumnName.Rows)
                            {
                                actualData.Columns.Add(row[0].ToString());
                            }

                            string fullData = reader.ReadToEnd();
                            string[] fullDataArray = fullData.Split(new[] { '\n' }, StringSplitOptions.RemoveEmptyEntries);

                            foreach (string line in fullDataArray)
                            {
                                if (!string.IsNullOrWhiteSpace(line))
                                {
                                    string[] splitLine = line.Split(',');
                                    DataRow dr = actualData.NewRow();
                                    for (int j = 0; j < splitLine.Length; j++)
                                    {
                                        dr[j] = splitLine[j].Replace('"', ' ').Trim();
                                    }
                                    actualData.Rows.Add(dr);
                                }
                            }
                            ActualData = actualData;
                            jData = 1;
                        }
                    }

                    // Delete the file after processing
                    System.IO.File.Delete(filePath);
                }
                catch (Exception ex)
                {
                    // Log exception if necessary
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ": " + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    return new StatusCodeResult(StatusCodes.Status500InternalServerError);
                }
            }
            else
            {
                return new BadRequestObjectResult("No file uploaded.");
            }
            jsonResult = new
            {
                jData
            };
            //return ViewResult("~/Views/Core/CoreImportInterfaceView.cshtml");
            return new JsonResult(jsonResult);
        }

        public static string Delimit(string str)
        {
            string x = str;
            x = x.Replace(",", "   ");
            int firstIndex = x.IndexOf('"');
            int lastIndex = x.LastIndexOf('"');
            int[] x1 = new int[lastIndex - firstIndex];
            if (firstIndex != -1)
            {
                int counter = firstIndex + 1;
                for (int i = 0; i < (lastIndex - firstIndex); i++)
                {
                    x1[i] = x.IndexOf('"', counter, (lastIndex - counter));
                    counter++;
                }
            }
            DataTable dt = new DataTable();
            dt.Columns.Add("Col1");
            dt.Rows.Add(firstIndex.ToString());
            for (int i = 0; i < x1.Length; i++)
            {
                if (dt.Rows.Count == 0)
                {
                    dt.Rows.Add(x1[i].ToString());
                }
                else
                {
                    int counter = 0;
                    for (int j = 0; j < dt.Rows.Count; j++)
                    {
                        if (x1[i].ToString() == dt.Rows[j][0].ToString())
                        {
                            counter += 1;
                        }
                    }
                    if (counter == 0)
                    {
                        dt.Rows.Add(x1[i].ToString());
                    }
                }
            }
            dt.Rows.Add(lastIndex.ToString());
            for (int j = 0; j < dt.Rows.Count; j++)
            {
                if (Convert.ToInt32(dt.Rows[j][0]) == -1)
                {
                    dt.Rows.RemoveAt(j);
                    dt.AcceptChanges();
                    j--;
                }
            }
            int start = -1;
            int iCounter = 0;
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                if (i == 0)
                {
                }
                else if (i % 2 != 0)
                {
                    start = Convert.ToInt32(dt.Rows[i][0]) + iCounter + 1;
                    x = x.Insert(start, ",");
                    iCounter++;
                }
            }
            return x;
        }
        #endregion


        #region ::: MapColumns Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// MapColumns 
        /// </summary>
        public static IActionResult MapColumns(string connString, MapColumnsList MapColumnsobj)
        {
            string message = "success";
            int counter = 0;
            DataTable dtColumnMapping = ColumnDataStorage.DtColumnMapping;
            if (dtColumnMapping.Rows.Count > 0)
            {
                for (int i = 0; i < dtColumnMapping.Rows.Count; i++)
                {
                    if ((dtColumnMapping.Rows[i][0].ToString() == MapColumnsobj.sourceColumnValue) && (dtColumnMapping.Rows[i][1].ToString() == MapColumnsobj.destinationColumnValue))
                    {
                        counter = 2;
                        goto label;
                    }
                    else if (dtColumnMapping.Rows[i][1].ToString() == MapColumnsobj.destinationColumnValue)
                    {
                        counter = 1;
                        goto label;
                    }
                }
            label: if (counter == 0)
                {
                    dtColumnMapping.Rows.Add(MapColumnsobj.sourceColumnValue, MapColumnsobj.destinationColumnValue, false);
                }
                else if (counter == 1)
                {
                    message = "Selected Destination column is already Mapped with other Source Column";
                }
                else if (counter == 2)
                {
                    message = "Selected columns are already Mapped";
                }
            }
            else
            {
                dtColumnMapping.Rows.Add(MapColumnsobj.sourceColumnValue, MapColumnsobj.destinationColumnValue, false);
            }
            ColumnDataStorage.DtColumnMapping = dtColumnMapping;

            return new JsonResult(message);
        }
        #endregion


        #region ::: MappedData Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// MappedData 
        /// </summary>
        public static IActionResult MappedData(string connString, MappedDataList MappedDataobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            string AppPath = string.Empty;
            DataTable dtColumnMapping = ColumnDataStorage.DtColumnMapping;
            List<MapList> listMap = new List<MapList>();
            if (dtColumnMapping != null && dtColumnMapping.Rows.Count > 0)
            {
                for (int i = 0; i < dtColumnMapping.Rows.Count; i++)
                {
                    MapList obj = new MapList();
                    obj.DestinationColumnName = dtColumnMapping.Rows[i][1].ToString();
                    obj.SourceColumnName = dtColumnMapping.Rows[i][0].ToString();
                    obj.RowID = i + 1;
                    obj.IsUnique = Convert.ToBoolean(dtColumnMapping.Rows[i][2]);
                    listMap.Add(obj);
                }
            }
            var x = from a in listMap
                    select new
                    {
                        a.SourceColumnName,
                        a.DestinationColumnName,
                        delete = "<img src='" + AppPath + "/Content/images/ic_Delete_Grid.gif' key='" + a.RowID + "' class='deleteMapping'/>",
                        Unique = "<input type='checkbox' key='" + a.RowID + "' class='chkUnique'" + ((a.IsUnique) ? "checked='true'" : "") + " />"
                    };

            List<dynamic> arr = new List<dynamic>();
            for (int i = 0; i < x.ToList().Count; i++)
            {
                if ((i >= (page * rows) - rows) && (i < (page * rows) + rows))
                {
                    arr.Add(x.ToList()[i]);
                }
            }
            int count = listMap.Count;
            int total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
            var y = new
            {
                total = total,
                page = page,
                records = count,
                data = arr.ToArray()
            };
            return new JsonResult(y);
        }
        #endregion


        #region ::: DeleteMapping Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// DeleteMapping 
        /// </summary>
        public static IActionResult DeleteMapping(string connString, DeleteMappingList DeleteMappingobj)
        {
            DataTable dtColumnMapping;
            dtColumnMapping = ColumnDataStorage.DtColumnMapping;
            dtColumnMapping.Rows.RemoveAt((DeleteMappingobj.rowID - 1));
            dtColumnMapping.AcceptChanges();
            ColumnDataStorage.DtColumnMapping = dtColumnMapping;
            return new JsonResult(false);
        }
        #endregion


        #region ::: SetUniqueKey Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// SetUniqueKey 
        /// </summary>
        public static IActionResult SetUniqueKey(string connString, SetUniqueKeyList SetUniqueKeyobj)
        {
            DataTable dtColumnMapping;
            dtColumnMapping = ColumnDataStorage.DtColumnMapping;
            dtColumnMapping.Rows[SetUniqueKeyobj.rowID - 1]["IsUnique"] = SetUniqueKeyobj.isUnique;
            dtColumnMapping.AcceptChanges();
            ColumnDataStorage.DtColumnMapping = dtColumnMapping;
            return new JsonResult(false);
        }
        #endregion


        #region ::: ImportToDataBase Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// ImportToDataBase 
        /// </summary>
        public static IActionResult ImportToDataBase(string connString, ImportToDataBaseList ImportToDataBaseobj)
        {
            string message = "";
            var jsonResult = default(dynamic);
            DataTable dtColumnMapping;
            SqlConnection con = null;
            SqlCommand cmd = null;
            SqlDataReader dr = null;
            DataTable dtActualdata = ColumnDataStorage.ActualData;
            DataTable dtError = new DataTable();
            if (dtActualdata != null)
            {
                for (int i = 0; i < dtActualdata.Columns.Count; i++)
                {
                    dtError.Columns.Add(dtActualdata.Columns[i].ColumnName);
                }
            }

            dtError.Columns.Add("ErrorRemarks");
            try
            {
                bool hasUniqueKey = false;
                string uniqueKey = "";
                string uniqueValue = "";
                string[] uniqueKeys = null;
                string[] uniqueValues = null;
                int isEveryThingsFine = 0;

                if (ColumnDataStorage.DtColumnMapping != null)
                {
                    dtColumnMapping = ColumnDataStorage.DtColumnMapping;
                    if (dtColumnMapping.Rows.Count > 0)
                    {
                        for (int i = 0; i < dtColumnMapping.Rows.Count; i++)
                        {
                            if (Convert.ToBoolean(dtColumnMapping.Rows[i]["IsUnique"]))
                            {
                                uniqueKey += dtColumnMapping.Rows[i]["DestinationColumn"].ToString() + ",";
                                uniqueValue += dtColumnMapping.Rows[i]["SourceColumn"].ToString() + ",";
                                hasUniqueKey = true;
                                isEveryThingsFine += 1;
                            }
                        }
                        if (isEveryThingsFine != dtColumnMapping.Rows.Count)
                        {
                            if (!hasUniqueKey)
                            {
                                foreach (DataRow row in dtActualdata.Rows)
                                {
                                    string columns = string.Join(",", dtColumnMapping.AsEnumerable().Select(x => x["DestinationColumn"].ToString()));
                                    string values = string.Join(",", dtColumnMapping.AsEnumerable().Select(x => "'" + row[x["SourceColumn"].ToString()].ToString() + "'"));

                                    // Call Insert SP
                                    cmd = new SqlCommand("SP_ImportInterfaceInsertData", con);
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.Parameters.AddWithValue("@tableName", ImportToDataBaseobj.tableName);
                                    cmd.Parameters.AddWithValue("@columns", columns);
                                    cmd.Parameters.AddWithValue("@values", values);

                                    try
                                    {
                                        cmd.ExecuteNonQuery();
                                    }
                                    catch (Exception ex)
                                    {
                                        DataRow drErrorRow = dtError.NewRow();
                                        for (int am = 0; am < row.ItemArray.Count(); am++)
                                        {
                                            drErrorRow[am] = row[am];
                                        }
                                        drErrorRow["ErrorRemarks"] = ex.Message;
                                        dtError.Rows.Add(drErrorRow);
                                    }
                                }
                            }
                            else
                            {
                                // Logic for checking and updating records
                                uniqueKeys = uniqueKey.TrimEnd(',').Split(',');
                                uniqueValues = uniqueValue.TrimEnd(',').Split(',');

                                foreach (DataRow row in dtActualdata.Rows)
                                {
                                    string whereClause = string.Join(" AND ", uniqueKeys.Select((k, i) => k + " = '" + row[uniqueValues[i]].ToString() + "'"));

                                    // Check if record exists
                                    cmd = new SqlCommand("SP_ImportInterfaceCheckExistingRecord", con);
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.Parameters.AddWithValue("@tableName", ImportToDataBaseobj.tableName);
                                    cmd.Parameters.AddWithValue("@whereClause", whereClause);

                                    int recordCount = (int)cmd.ExecuteScalar();
                                    if (recordCount > 0)
                                    {
                                        // Update existing record
                                        string setClause = string.Join(",", dtColumnMapping.AsEnumerable()
                                            .Where(x => !uniqueKeys.Contains(x["DestinationColumn"].ToString()))
                                            .Select(x => x["DestinationColumn"].ToString() + " = '" + row[x["SourceColumn"].ToString()].ToString() + "'"));

                                        cmd = new SqlCommand("SP_ImportInterfaceUpdateData", con);
                                        cmd.CommandType = CommandType.StoredProcedure;
                                        cmd.Parameters.AddWithValue("@tableName", ImportToDataBaseobj.tableName);
                                        cmd.Parameters.AddWithValue("@setColumns", setClause);
                                        cmd.Parameters.AddWithValue("@whereClause", whereClause);

                                        try
                                        {
                                            cmd.ExecuteNonQuery();
                                        }
                                        catch (Exception ex)
                                        {
                                            DataRow drErrorRow = dtError.NewRow();
                                            for (int am = 0; am < row.ItemArray.Count(); am++)
                                            {
                                                drErrorRow[am] = row[am];
                                            }
                                            drErrorRow["ErrorRemarks"] = ex.Message;
                                            dtError.Rows.Add(drErrorRow);
                                        }
                                    }
                                    else
                                    {
                                        // Insert new record
                                        string columns = string.Join(",", dtColumnMapping.AsEnumerable().Select(x => x["DestinationColumn"].ToString()));
                                        string values = string.Join(",", dtColumnMapping.AsEnumerable().Select(x => "'" + row[x["SourceColumn"].ToString()].ToString() + "'"));

                                        cmd = new SqlCommand("InsertData", con);
                                        cmd.CommandType = CommandType.StoredProcedure;
                                        cmd.Parameters.AddWithValue("@tableName", ImportToDataBaseobj.tableName);
                                        cmd.Parameters.AddWithValue("@columns", columns);
                                        cmd.Parameters.AddWithValue("@values", values);

                                        try
                                        {
                                            cmd.ExecuteNonQuery();
                                        }
                                        catch (Exception ex)
                                        {
                                            DataRow drErrorRow = dtError.NewRow();
                                            for (int am = 0; am < row.ItemArray.Count(); am++)
                                            {
                                                drErrorRow[am] = row[am];
                                            }
                                            drErrorRow["ErrorRemarks"] = ex.Message;
                                            dtError.Rows.Add(drErrorRow);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                message = ex.Message;
            }

            jsonResult = new
            {
                message = message,
                hasErrors = dtError.Rows.Count > 0
            };

            return new JsonResult(jsonResult);
        }
        #endregion


        #region ::: GetDatas Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// GetDatas 
        /// </summary>
        public static IActionResult GetDatas(string connString, GetDataList GetDataobj)
        {
            var jsonResult = default(dynamic);
            DataTable dtSrcColumnName = new DataTable();
            if (ColumnDataStorage.UploadedColumnNames != null)
            {
                dtSrcColumnName = ColumnDataStorage.UploadedColumnNames;
            }
            StringBuilder sb = new StringBuilder();
            List<GetData> getDataList = new List<GetData>();
            for (int i = 0; i < dtSrcColumnName.Rows.Count; i++)
            {
                getDataList.Add(new GetData()
                {
                    columnID = dtSrcColumnName.Rows[i][0].ToString(),
                    columnName = dtSrcColumnName.Rows[i][0].ToString()
                });
            }
            jsonResult = from a in getDataList
                         select new
                         {
                             a.columnID,
                             a.columnName
                         };

            return new JsonResult(jsonResult);
        }
        #endregion


        #region ::: FillStoredFormat Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// FillStoredFormat 
        /// </summary>
        public static IActionResult FillStoredFormat(string connString, FillStoredFormatList FillStoredFormatobj)
        {
            var jsonResult = default(dynamic);
            SqlConnection con = null;
            SqlCommand cmd = null;
            SqlDataReader dr = null;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                con = new SqlConnection(connString);
                con.Open(); // Open the connection

                // Use the stored procedure instead of inline SQL
                cmd = new SqlCommand("SP_ImportInterfaceGetStoredFormats", con);
                cmd.CommandType = CommandType.StoredProcedure; // Specify that it's a stored procedure

                dr = cmd.ExecuteReader();
                DataTable dtFormatNames = new DataTable();
                dtFormatNames.Load(dr);

                List<TableNames> getDataList = new List<TableNames>();
                for (int i = 0; i < dtFormatNames.Rows.Count; i++)
                {
                    getDataList.Add(new TableNames()
                    {
                        tableID = dtFormatNames.Rows[i][0].ToString(),
                        tableName = dtFormatNames.Rows[i][0].ToString()
                    });
                }

                con.Close();

                // Convert the list to the required format
                jsonResult = from a in getDataList
                             select new
                             {
                                 a.tableID,
                                 a.tableName
                             };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            finally
            {
                // Close resources
                if (dr != null) dr.Close();
                if (con != null) con.Close();
            }

            return new JsonResult(jsonResult);
        }
        #endregion


        #region ::: CheckFormat Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// CheckFormat 
        /// </summary>
        public static IActionResult CheckFormat(string connString, CheckFormatList CheckFormatobj)
        {
            int returnValue = 0;
            var jsonResult = default(dynamic);
            SqlConnection con = null;
            SqlCommand cmd = null;
            SqlDataReader dr = null;

            try
            {
                con = new SqlConnection(connString);
                con.Open(); // Open the connection

                // Step 1: Call stored procedure to get distinct column names
                cmd = new SqlCommand("SP_ImportInterfaceGetFormatExcelColumnNames", con);
                cmd.CommandType = CommandType.StoredProcedure; // Specify it's a stored procedure
                cmd.Parameters.AddWithValue("@FormatName", CheckFormatobj.value); // Pass the value parameter

                dr = cmd.ExecuteReader();
                DataTable dtFormatNames = new DataTable();
                dtFormatNames.Load(dr);

                // Step 2: Compare session data if available
                if (ColumnDataStorage.UploadedColumnNames != null)
                {
                    DataTable dtSrcColumnName = ColumnDataStorage.UploadedColumnNames;
                    int counter = 0;

                    for (int i = 0; i < dtFormatNames.Rows.Count; i++)
                    {
                        bool check = false;
                        for (int j = 0; j < dtSrcColumnName.Rows.Count; j++)
                        {
                            if (dtFormatNames.Rows[i][0].ToString() == dtSrcColumnName.Rows[j][0].ToString())
                            {
                                check = true;
                                break;
                            }
                        }
                        if (check)
                        {
                            counter += 1;
                        }
                    }

                    // Step 3: If all match, load column mapping
                    if (counter == dtFormatNames.Rows.Count)
                    {
                        DataTable dtColumnMapping = ColumnDataStorage.DtColumnMapping;
                        dtColumnMapping.Rows.Clear(); // Clear any existing mappings

                        // Step 4: Call stored procedure to get format details
                        cmd = new SqlCommand("SP_ImportInterfaceGetFormatDetails", con);
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@FormatName", CheckFormatobj.value);

                        dr = cmd.ExecuteReader();
                        DataTable dt = new DataTable();
                        dt.Load(dr);
                        returnValue = 1;

                        string tableName = "";
                        for (int k = 0; k < dt.Rows.Count; k++)
                        {
                            tableName = dt.Rows[0][3].ToString(); // Table name
                            dtColumnMapping.Rows.Add(dt.Rows[k][0].ToString(), dt.Rows[k][1].ToString(), Convert.ToBoolean(dt.Rows[k][2]));
                        }

                        // Step 5: Update session with new mapping and prepare JSON result
                        ColumnDataStorage.DtColumnMapping = dtColumnMapping;

                        jsonResult = new
                        {
                            returnValue = returnValue,
                            tableName = tableName
                        };
                    }
                    else
                    {
                        returnValue = 0;
                        jsonResult = new
                        {
                            returnValue = returnValue,
                            tableName = ""
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                jsonResult = new
                {
                    returnValue = returnValue,
                    tableName = ""
                };
            }
            finally
            {
                if (dr != null) dr.Close();
                if (con != null) con.Close();
            }

            return new JsonResult(jsonResult);
        }
        #endregion


        #region ::: SaveFormat Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// SaveFormat 
        /// </summary>
        public static IActionResult SaveFormat(string connString, SaveFormatList SaveFormatobj)
        {
            int status = 0;
            SqlConnection con = null;
            SqlCommand cmd = null;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {

                con = new SqlConnection(connString);
                con.Open(); // Open the connection

                // Get the column mapping from the session
                DataTable dtColumnMapping = ColumnDataStorage.DtColumnMapping;

                // Check if there are rows to save
                if (dtColumnMapping != null && dtColumnMapping.Rows.Count > 0)
                {
                    for (int i = 0; i < dtColumnMapping.Rows.Count; i++)
                    {
                        // Create the command to call the stored procedure
                        cmd = new SqlCommand("SP_ImportInterfaceSaveImportInterfaceFormat", con);
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters to the command
                        cmd.Parameters.AddWithValue("@TableName", SaveFormatobj.tableName);
                        cmd.Parameters.AddWithValue("@ExcelColumnName", dtColumnMapping.Rows[i][0].ToString());
                        cmd.Parameters.AddWithValue("@TableColumnName", dtColumnMapping.Rows[i][1].ToString());
                        cmd.Parameters.AddWithValue("@IsUnique", Convert.ToBoolean(dtColumnMapping.Rows[i][2]));
                        cmd.Parameters.AddWithValue("@FormatName", SaveFormatobj.formatName);

                        // Execute the command
                        cmd.ExecuteNonQuery();
                    }

                    // Set status to indicate success
                    status = 1;
                }
            }
            catch (Exception ex)
            {
                // Log or handle the exception if needed
                status = 0; // Set status to 0 in case of an error
            }
            finally
            {
                // Close the connection
                if (con != null)
                {
                    con.Close();
                }
            }

            // Return the status
            return new JsonResult(status);
        }
        #endregion


        #region ::: FillFormattedColumnNames Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// FillFormattedColumnNames 
        /// </summary>
        public static IActionResult FillFormattedColumnNames(string connString, FillFormattedColumnNamesList FillFormattedColumnNamesobj)
        {
            var jsonResult = default(dynamic);
            SqlConnection con = null;
            SqlCommand cmd = null;
            SqlDataReader dr = null;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                con = new SqlConnection(connString);
                con.Open(); // Open the connection

                // Use the stored procedure instead of inline SQL
                cmd = new SqlCommand("SP_ImportInterfaceGetFormattedColumnNames", con);
                cmd.CommandType = CommandType.StoredProcedure; // Specify that it's a stored procedure
                cmd.Parameters.AddWithValue("@TableValue", FillFormattedColumnNamesobj.tableValue); // Pass the tableValue parameter

                dr = cmd.ExecuteReader();
                DataTable dtColumnNames = new DataTable();
                dtColumnNames.Load(dr);

                // Store the DataTable in session
                ColumnDataStorage.DtColumnNames = dtColumnNames;

                List<ColumnNames> getDataList = new List<ColumnNames>();
                for (int i = 0; i < dtColumnNames.Rows.Count; i++)
                {
                    getDataList.Add(new ColumnNames()
                    {
                        columnID = dtColumnNames.Rows[i]["CName"].ToString(),
                        columnName = dtColumnNames.Rows[i]["columnName"].ToString()
                    });
                }

                con.Close();

                // Convert the list to the required format
                jsonResult = from a in getDataList
                             select new
                             {
                                 a.columnID,
                                 a.columnName
                             };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            finally
            {
                // Close resources
                if (dr != null) dr.Close();
                if (con != null) con.Close();
            }

            return new JsonResult(jsonResult);
        }
        #endregion


        #region :::  CoreImportInterface list and obj classes Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// CoreImportInterface 
        /// </summary>
        /// 
        public static class ColumnDataStorage
        {
            public static DataTable DtColumnNames { get; set; }
            public static DataTable DtColumnMapping { get; set; }
            public static DataTable ActualData { get; set; }
            public static DataTable UploadedColumnNames { get; set; }

            static ColumnDataStorage()
            {
                DtColumnNames = new DataTable();
                DtColumnMapping = new DataTable();
                ActualData = new DataTable();
                UploadedColumnNames = new DataTable();
            }
        }

        public class FillFormattedColumnNamesList
        {
            public int tableValue { get; set; }
        }

        public class SaveFormatList
        {
            public string formatName { get; set; }
            public string tableName { get; set; }
        }
        public class CheckFormatList
        {
            public string value { get; set; }
        }

        public class FillStoredFormatList
        {

        }
        public class GetDataList
        {

        }

        public class ImportToDataBaseList
        {
            public string tableName { get; set; }
        }
        public class SetUniqueKeyList
        {
            public int rowID { get; set; }
            public bool isUnique { get; set; }
        }
        public class DeleteMappingList
        {
            public int rowID { get; set; }
        }
        public class MappedDataList
        {

        }

        public class MapColumnsList
        {
            public string destinationColumnValue { get; set; }
            public string sourceColumnValue { get; set; }
        }

        public class FillColumnNamesList
        {
            public int tableValue { get; set; }
        }
        #endregion


        #region :::  CoreImportInterface  classes Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// CoreImportInterface 
        /// </summary>

        public class MapList
        {
            private int rowID;
            public int RowID
            {
                get { return rowID; }
                set { rowID = value; }
            }
            private string sourceColumnName;
            public string SourceColumnName
            {
                get { return sourceColumnName; }
                set { sourceColumnName = value; }
            }
            private string destinationColumnName;
            public string DestinationColumnName
            {
                get { return destinationColumnName; }
                set { destinationColumnName = value; }
            }
            private bool isUnique;
            public bool IsUnique
            {
                get { return isUnique; }
                set { isUnique = value; }
            }
        }
        public class GetData
        {
            public string columnID { get; set; }
            public string columnName { get; set; }
        }
        public class ColumnNames
        {
            public string columnID { get; set; }
            public string columnName { get; set; }
        }
        public class TableNames
        {
            public string tableID { get; set; }
            public string tableName { get; set; }
        }
        #endregion

    }
}
