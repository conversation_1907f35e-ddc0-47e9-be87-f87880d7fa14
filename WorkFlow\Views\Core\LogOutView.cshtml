﻿<!DOCTYPE html>
<html>
<head>
    <title></title>
    <script src="~/Scripts/JQuery/jquery-1.8.2.js"></script>
    <script src="~/Scripts/JQuery/jquery-ui-1.9.0.custom.min.js"></script>
    <script type="text/javascript">

        $(document).ready(function () {
            $.url = function (url) {
                var path = '@Request.ApplicationPath'
                if (path == '/')
                    return url;
                return path + url;
            }
            $('#btnRelogin').click(function () {
                window.open($.url('/CoreWFLogin/CoreLoginView'), '_blank', ' height=' + screen.availHeight + ' , width=' + screen.availWidth + ' , resizable=yes , scrollbars=yes , top=0 , left=0 , menubar=0 , status=0 , titlebar=0,location=0', '');
                window.parent.IsLogout = true;
                window.parent.close();
            });
            $('#btnClose').click(function () {
                window.parent.IsLogout = true;
                window.parent.close();
            });
            document.onhelp = function () { return (false); }
            window.onhelp = function () { return (false); }
        });
    </script>
</head>
<body style="background-image: url('../Images/blue_full-d.png'); background-repeat: no-repeat; background-size: 100%">
    <div style="text-align: center">
        <br />
        <br />
        <br />
        <br />
        <br />
        <br />
        <br />
        <br />
        <br />
        <br />
        <br />
        <br />
        <br />
        <br />
        <br />
        <br />
        <br />
        <br />
        <br />
        <br />
        <strong><span style="font-size: 24pt; color: white">@HttpContext.GetGlobalResourceObject("Resource_en", "YouhavebeenLoggedoutsuccessfully").ToString()<br />
            <br />
        </span></strong>
        <input id="btnClose" type="button" value="@HttpContext.GetGlobalResourceObject("Resource_en", "close").ToString()" />
        <input id="btnRelogin" type="button" value="@HttpContext.GetGlobalResourceObject("Resource_en", "relogin").ToString()" />
    </div>
</body>
<script type="text/javascript">
    //Function to disable general browser features
    document.onkeydown = function (e) {
        evt = window.event;

        if (e != null && e != undefined) {
            evt = e;
        }
        

        if (evt.keyCode == 112 || evt.keyCode == 113 || evt.keyCode == 114 || evt.keyCode == 115
            || evt.keyCode == 116 || evt.keyCode == 117 || evt.keyCode == 118 || evt.keyCode == 119
            || evt.keyCode == 120 || evt.keyCode == 122 || evt.keyCode == 123) {
            evt.keyCode = 0;
            return false;
        }

        if (evt.keyCode == 8 && evt.srcElement.id.indexOf("txt") == -1) {
            evt.keyCode = 0;
            return false;

        }
        //ctrl + r
        if (evt.ctrlKey && evt.keyCode == 82) {
            evt.keyCode = 0;
            return false;
        }
        //ctrl + n
        if (evt.ctrlKey && evt.keyCode == 78) {
            evt.keyCode = 0;
            return false;
        }
        //ctrl + p
        if (evt.ctrlKey && evt.keyCode == 80) {
            evt.keyCode = 0;
            return false;
        }
        //ctrl + b
        if (evt.ctrlKey && evt.keyCode == 66) {
            evt.keyCode = 0;
            return false;
        }
        //ctrl + h
        if (evt.ctrlKey && evt.keyCode == 72) {
            evt.keyCode = 0;
            return false;
        }
        //ctrl + 5
        if (evt.ctrlKey && evt.keyCode == 53) {
            evt.keyCode = 0;
            return false;
        }
        //ctrl + l
        if (evt.ctrlKey && evt.keyCode == 76) {
            evt.keyCode = 0;
            return false;
        }
        //ctrl + i
        if (evt.ctrlKey && evt.keyCode == 73) {
            evt.keyCode = 0;
            return false;
        }
        //ctrl + e
        if (evt.ctrlKey && evt.keyCode == 69) {
            evt.keyCode = 0;
            return false;
        }
        //ctrl + s
        if (evt.ctrlKey && evt.keyCode == 83) {
            evt.keyCode = 0;
            return false;
        }
        //ctrl + w
        if (evt.ctrlKey && evt.keyCode == 87) {
            evt.keyCode = 0;
            return false;
        }
        //ctrl + o
        if (evt.ctrlKey && evt.keyCode == 79) {
            evt.keyCode = 0;
            return false;
        }
        //Shift + enter
        if (evt.shiftKey && evt.keyCode == 13) {
            evt.keyCode = 0;
            return false;
        }
        //Alt + Left arrow
        if (evt.altKey && evt.keyCode == 37) {

            evt.keyCode = 0;
            return false;
        }
        //Alt + right arrow
        if (evt.altKey && evt.keyCode == 39) {

            evt.keyCode = 0;
            return false;
        }
        //Alt + Up arrow
        if (evt.altKey && evt.keyCode == 38) {

            evt.keyCode = 0;
            return false;
        }
        //Alt + down arrow         
        if (evt.altKey && evt.keyCode == 40) {

            evt.keyCode = 0;
            return false;
        }

    }
</script>
</html>
