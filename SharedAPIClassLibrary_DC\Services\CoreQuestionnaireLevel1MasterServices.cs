﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace SharedAPIClassLibrary_AMERP
{
    public class CoreQuestionnaireLevel1MasterServices
    {
        static string AppPath = string.Empty;

        #region ::: SelectReferenceMaster /Mithun:::
        /// <summary>
        /// To get Refrence Master records for a Master
        /// </summary> 
        public static IActionResult SelectReferenceMaster(SelectReferenceMasterQuestionnaireLevel1List SelectReferenceMasterObj, string constring, int LogException)
        {
            {
                var Masterdata = new object();
                try
                {
                    int CompanyID = Convert.ToInt32(SelectReferenceMasterObj.Company_ID);
                    int Language_ID = Convert.ToInt32(SelectReferenceMasterObj.UserLanguageID);
                    string UserLanguageCode = SelectReferenceMasterObj.UserLanguageCode.ToString();
                    string GeneralLanguageCode = SelectReferenceMasterObj.GeneralLanguageCode.ToString();

                    using (SqlConnection conn = new SqlConnection(constring))
                    {
                        conn.Open();

                        using (SqlCommand cmd = new SqlCommand("USP_GetQuestionnaryLevel1", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@UserLang", UserLanguageCode);
                            cmd.Parameters.AddWithValue("@GenLang", GeneralLanguageCode);
                            cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                            cmd.Parameters.AddWithValue("@Language_ID", Language_ID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                List<dynamic> referenceMasterData = new List<dynamic>();
                                int? defaultId = null;

                                // Read the first result set (list of names)
                                while (reader.Read())
                                {
                                    referenceMasterData.Add(new
                                    {
                                        RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("ID")),
                                        RefMasterDetail_Name = reader.GetString(reader.GetOrdinal("Name"))
                                    });
                                }

                                // Move to the second result set (default value)
                                if (reader.NextResult() && reader.Read())
                                {
                                    defaultId = reader.GetInt32(reader.GetOrdinal("IsDefault"));
                                }

                                Masterdata = new
                                {
                                    ReferenceMasterData = referenceMasterData.OrderBy(r => r.RefMasterDetail_Name).ToList(),
                                    Isdefault = defaultId
                                };
                            }
                        }
                    }

                    //return Json(Masterdata, JsonRequestBehavior.AllowGet);
                    return new JsonResult(Masterdata);
                }
                catch (WebException wex)
                {
                    LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                    //return RedirectToAction("Error");
                    return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }
                    //return RedirectToAction("Error");
                    return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };

                }
            }
        }
        #endregion

        #region ::: Select /Mithun:::
        /// <summary>
        /// To Select Questions for IssueSubArea
        /// </summary>
        public static IActionResult Select(SelectCoreQuestionnaireLevel1List CoreQuestionnaireLevel1Obj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {

            try
            {
                int Count = 0;
                int Total = 0;
                int CompanyID = Convert.ToInt32(CoreQuestionnaireLevel1Obj.Company_ID);

                List<QuestionnaireLevel1> IEQuestionnaireLevel1Array = new List<QuestionnaireLevel1>();



                string YesE = CommonFunctionalities.GetResourceString(CoreQuestionnaireLevel1Obj.GeneralCulture.ToString(), "yes").ToString();
                string NoE = CommonFunctionalities.GetResourceString(CoreQuestionnaireLevel1Obj.GeneralCulture.ToString(), "no").ToString();
                string YesL = CommonFunctionalities.GetResourceString(CoreQuestionnaireLevel1Obj.UserCulture.ToString(), "yes").ToString();
                string NoL = CommonFunctionalities.GetResourceString(CoreQuestionnaireLevel1Obj.UserCulture.ToString(), "no").ToString();


                var jsonData = default(dynamic);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    string query = "UP_Select_AM_ERP_QuestionnaireLevel1";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@IssueAreaID", CoreQuestionnaireLevel1Obj.IssueAreaID);
                            command.Parameters.AddWithValue("@CompanyID", CompanyID);
                            command.Parameters.AddWithValue("@LanguageID", CoreQuestionnaireLevel1Obj.LanguageID);
                            command.Parameters.AddWithValue("@GeneralLanguageID", Convert.ToInt32(CoreQuestionnaireLevel1Obj.GeneralLanguageID));
                            command.Parameters.AddWithValue("@YesE", YesE);
                            command.Parameters.AddWithValue("@NoE", NoE);
                            command.Parameters.AddWithValue("@YesL", YesL);
                            command.Parameters.AddWithValue("@NoL", NoL);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    QuestionnaireLevel1 ql = new QuestionnaireLevel1
                                    {
                                        QuestionaryLevel1_ID = reader.GetInt32(reader.GetOrdinal("QuestionaryLevel1_ID")),
                                        QuestionLevel1 = reader.GetString(reader.GetOrdinal("QuestionLevel1")),
                                        QuestionLevel1_IsActive = reader.GetString(reader.GetOrdinal("QuestionLevel1_IsActive"))
                                    };
                                    IEQuestionnaireLevel1Array.Add(ql);
                                }



                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                IQueryable<QuestionnaireLevel1> IQQuestionnaireLevel1 = IEQuestionnaireLevel1Array.AsQueryable();

                IQQuestionnaireLevel1 = IEQuestionnaireLevel1Array.AsQueryable<QuestionnaireLevel1>();

                if (_search)
                {
                    string decodedValue = Uri.UnescapeDataString(filters);
                    Filters filtersObj = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<Filters>();
                    if (filtersObj.rules.Count() > 0)
                        IQQuestionnaireLevel1 = IQQuestionnaireLevel1.FilterSearch<QuestionnaireLevel1>(filtersObj);
                }
                if (advnce)
                {
                    string decodedValue = Uri.UnescapeDataString(Query);
                    AdvanceFilter advnfilter = JObject.Parse((decodedValue)).ToObject<AdvanceFilter>();
                    IQQuestionnaireLevel1 = IQQuestionnaireLevel1.AdvanceSearch<QuestionnaireLevel1>(advnfilter);
                }

                IQQuestionnaireLevel1 = IQQuestionnaireLevel1.OrderByField<QuestionnaireLevel1>(sidx, sord);



                Count = IQQuestionnaireLevel1.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                //Added by Ravi on 05-Jan-2014 for HelpDesk QA Corrections Begin
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }
                //---End
                jsonData = new
                {
                    total = Total,
                    page = page,
                    data = (from a in IQQuestionnaireLevel1.AsEnumerable()
                            select new
                            {
                                ID = a.QuestionaryLevel1_ID,
                                //edit = "<img id='" + a.QuestionaryLevel1_ID + "' src='" + AppPath + "/Content/edit.gif' key='" + a.QuestionaryLevel1_ID + "' class='QuestionnaireLevel1Edit' editmode='false'/>",
                                edit = "<a title='Edit' href='#' id='" + a.QuestionaryLevel1_ID + "' key='" + a.QuestionaryLevel1_ID + "' class='QuestionnaireLevel1Edit' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input type='checkbox' key='" + a.QuestionaryLevel1_ID + "' defaultchecked=''  id='chk" + a.QuestionaryLevel1_ID + "' class='QuestionnaireLevel1Delete'/>",
                                QuestionLevel1 = (a.QuestionLevel1),
                                QuestionLevel1_IsActive = a.QuestionLevel1_IsActive,
                                Locale = "<img key='" + a.QuestionaryLevel1_ID + "' src='" + AppPath + "/Content/local.png' class='QuestionnaireLevel1Locale' alt='Localize' width='20' height='20'  title='Localize'/>",
                                View = "<img id='" + a.QuestionaryLevel1_ID + "' src='" + AppPath + "/Content/plus.gif' key='" + a.QuestionaryLevel1_ID + "' class='ViewQuestionnaireLevel1Locale'/>",
                            }).ToList().Paginate(page, rows),
                    records = Count
                };

                //return Json(jsonData, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonData);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
                //return RedirectToAction("Error");
            }
        }
        #endregion

        #region ::: SelectParticularQuestionnaireLevel1 /Mithun:::
        /// <summary>
        /// SelectParticularQuestionnaireLevel1
        /// </summary>
        public static IActionResult SelectParticularQuestionnaireLevel1(SelectParticularQuestionnaireLevel1List SelectParticularQuestionnaireLevel1Obj, string constring, int LogException)
        {
            var x = default(dynamic);
            try
            {
                int Language_ID = Convert.ToInt32(SelectParticularQuestionnaireLevel1Obj.UserLanguageID);
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    string query = "UP_Select_AM_ERP_SelectParticularQuestionnaireLevel1";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@QuestionnaireLevel1ID", SelectParticularQuestionnaireLevel1Obj.QuestionnaireLevel1ID);
                            command.Parameters.AddWithValue("@Language_ID", Language_ID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    x = new
                                    {
                                        QuestionaryLevel1_ID = reader.IsDBNull(reader.GetOrdinal("QuestionaryLevel1_ID"))
       ? default(int)
       : reader.GetInt32(reader.GetOrdinal("QuestionaryLevel1_ID")),

                                        QuestionLevel1 = reader.IsDBNull(reader.GetOrdinal("QuestionLevel1"))
       ? string.Empty
       : reader.GetString(reader.GetOrdinal("QuestionLevel1")),

                                        QuestionLevel1_IsActive = reader.IsDBNull(reader.GetOrdinal("QuestionLevel1_IsActive"))
       ? default(bool)
       : reader.GetBoolean(reader.GetOrdinal("QuestionLevel1_IsActive")),

                                        IssueArea_ID = reader.IsDBNull(reader.GetOrdinal("IssueArea_ID"))
       ? default(int)
       : reader.GetInt32(reader.GetOrdinal("IssueArea_ID")),

                                        IssueSubArea_ID = reader.IsDBNull(reader.GetOrdinal("IssueSubArea_ID"))
       ? default(int)
       : reader.GetInt32(reader.GetOrdinal("IssueSubArea_ID")),

                                        QuestionnaireLevel1LocaleID = reader.IsDBNull(reader.GetOrdinal("QuestionnaireLevel1LocaleID"))
       ? default(int)
       : reader.GetInt32(reader.GetOrdinal("QuestionnaireLevel1LocaleID")),

                                        QuestionnaireLevel1NameLocale = reader.IsDBNull(reader.GetOrdinal("QuestionnaireLevel1NameLocale"))
       ? string.Empty
       : reader.GetString(reader.GetOrdinal("QuestionnaireLevel1NameLocale"))
                                    };


                                }



                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                //return Json(x, JsonRequestBehavior.AllowGet);
                return new JsonResult(x);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }
        #endregion

        #region ::: Delete /Mithun:::
        /// <summary>
        /// To Delete Questionnaire Level1 Question
        /// </summary>
        public static IActionResult Delete(DeleteCoreQuestionnaireLevel1List DeleteObj, string constring, int LogException)
        {
            string ErrorMsg = string.Empty;

            try
            {
                // Parse the JSON request
                JObject jObj = JObject.Parse(DeleteObj.key);
                int Count = jObj["rows"].Count();
                //GNM_User User = (GNM_User)Session["UserDetails"];


                int ID = 0;

                for (int i = 0; i < Count; i++)
                {
                    // Extract the ID
                    JTokenReader jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jTR.Read();
                    ID = Convert.ToInt32(jTR.Value);

                    using (SqlConnection conn = new SqlConnection(constring))
                    {
                        string query = "USP_DeleteQuestionaryLevel1";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@QuestionaryLevel1_ID", ID);

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                command.ExecuteScalar();



                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }
                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }

                    // Insert GPS details
                    //   gbl.InsertGPSDetails(Convert.ToInt32(DeleteObj.Company_ID.ToString()), Convert.ToInt32(DeleteObj.Branch), DeleteObj.User_ID, Common.GetObjectID("CoreQuestionnaireLevel1Master",constring), ID, 0, 0, "Deleted " + ID, false, Convert.ToInt32(DeleteObj.MenuID), Convert.ToDateTime(DeleteObj.LoggedINDateTime));
                }

                ErrorMsg += CommonFunctionalities.GetResourceString(DeleteObj.UserCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.InnerException != null && ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    ErrorMsg += CommonFunctionalities.GetResourceString(DeleteObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    ErrorMsg += ex.Message;
                }
            }
            //return ErrorMsg;
            return new JsonResult(ErrorMsg);
        }
        #endregion

        #region ::: UpdateLocale /Mithun:::
        /// <summary>
        /// UpdateLocale
        /// </summary>
        public static IActionResult UpdateLocale(UpdateLocaleCoreQuestionnaireLevel1List UpdateLocaleObj, string constring, int LogException)
        {
            int QuestionnaireLevel1LocaleID = 0;
            var x = default(dynamic);
            try
            {
                HD_QuestionnaireLevel1Locale QLRow = null;
                HD_QuestionnaireLevel1Locale AddQlRow = null;

                JObject jObj = JObject.Parse(UpdateLocaleObj.data);


                QLRow = jObj.ToObject<HD_QuestionnaireLevel1Locale>();


                using (SqlConnection conn = new SqlConnection(constring))
                {
                    string query = "UP_UpdateOrInsert_AM_ERP_QuestionnaireLevel1Locale";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@QuestionLevel1_ID", QLRow.QuestionLevel1_ID);
                            command.Parameters.AddWithValue("@QuestionnaireLevel1Locale_ID", QLRow.QuestionnaireLevel1Locale_ID);
                            command.Parameters.AddWithValue("@QuestionLevel1", QLRow.QuestionLevel1);
                            command.Parameters.AddWithValue("@LanguageID", QLRow.Language_ID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    QuestionnaireLevel1LocaleID = Convert.ToInt32(reader["QuestionnaireLevel1Locale_ID"]);
                                }



                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                //   gbl.InsertGPSDetails(Convert.ToInt32(UpdateLocaleObj.Company_ID), Convert.ToInt32(UpdateLocaleObj.Branch), Convert.ToInt32(UpdateLocaleObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreQuestionnaireLevel1Master",constring)), QLRow.QuestionLevel1_ID, 0, 0, "Insert", false, Convert.ToInt32(UpdateLocaleObj.MenuID));

                x = new
                {
                    QuestionnaireLevel1LocaleID = QuestionnaireLevel1LocaleID
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(x, JsonRequestBehavior.AllowGet);
            return new JsonResult(x);
        }
        #endregion

        #region ::: CheckQuestionnaireLevel1Locale /Mithun:::
        /// <summary>
        /// CheckQuestionnaireLevel1Locale
        /// </summary>
        public static IActionResult CheckQuestionnaireLevel1Locale(CheckQuestionnaireLevel1LocaleList CheckQuestionnaireLevel1LocaleObj, string constring, int LogException)
        {
            int count = 0;
            try
            {
                string decryptedQuestionLevel1 = Common.DecryptString(CheckQuestionnaireLevel1LocaleObj.QuestionLevel1);
                int language_ID = Convert.ToInt32(CheckQuestionnaireLevel1LocaleObj.UserLanguageID);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    string query = "Up_Chk_Am_Erp_CheckQuestionnaireLevel1Locale";

                    using (SqlCommand command = new SqlCommand(query, conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@IssueAreaID", CheckQuestionnaireLevel1LocaleObj.IssueAreaID);
                        command.Parameters.AddWithValue("@QuestionnaireLevel1LocaleID", CheckQuestionnaireLevel1LocaleObj.QuestionnaireLevel1LocaleID);
                        command.Parameters.AddWithValue("@QuestionLevel1", decryptedQuestionLevel1);
                        command.Parameters.AddWithValue("@LanguageID", language_ID);

                        SqlParameter returnValue = new SqlParameter();
                        returnValue.Direction = ParameterDirection.ReturnValue;
                        command.Parameters.Add(returnValue);

                        command.ExecuteNonQuery();
                        count = (int)returnValue.Value;
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return count;
            return new JsonResult(count);
        }
        #endregion

        #region ::: Save /Mithun:::
        /// <summary>
        /// To Insert and Update State
        /// </summary>
        public static IActionResult Save(CoreQuestionnaireLevel1SaveList SaveObj, string constring, int LogException)
        {
            string ErrorMsg = string.Empty;
            try
            {
                //GNM_User UserDetails = (GNM_User)Session["UserDetails"];

                int CompanyID = Convert.ToInt32(SaveObj.Company_ID);
                //GNM_User User = (GNM_User)Session["UserDetails"];

                JObject jObj = JObject.Parse(SaveObj.data);
                int Count = jObj["rows"].Count();
                bool tableUpdated = false;

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    for (int i = 0; i < Count; i++)
                    {
                        JObject row = jObj["rows"].ElementAt(i).ToObject<JObject>();
                        int QuestionaryLevel1_ID = row["QuestionaryLevel1_ID"].ToObject<int>();
                        string QuestionLevel1 = Common.DecryptString(row["QuestionLevel1"].ToString());
                        int IssueArea_ID = row["IssueArea_ID"].ToObject<int>();
                        bool QuestionLevel1_IsActive = row["QuestionLevel1_IsActive"].ToObject<bool>();

                        string query = "USP_SaveQuestionaryLevel1";

                        using (SqlCommand command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@QuestionaryLevel1_ID", QuestionaryLevel1_ID);
                            command.Parameters.AddWithValue("@QuestionLevel1", QuestionLevel1);
                            command.Parameters.AddWithValue("@IssueArea_ID", IssueArea_ID);
                            command.Parameters.AddWithValue("@QuestionLevel1_IsActive", QuestionLevel1_IsActive);
                            command.Parameters.AddWithValue("@CompanyID", CompanyID);

                            SqlParameter isUpdatedParam = new SqlParameter("@IsUpdated", SqlDbType.Bit)
                            {
                                Direction = ParameterDirection.Output
                            };
                            command.Parameters.Add(isUpdatedParam);

                            command.ExecuteNonQuery();
                            bool isUpdated = (bool)isUpdatedParam.Value;

                            if (isUpdated)
                            {
                                //   gbl.InsertGPSDetails(Convert.ToInt32(SaveObj.Company_ID.ToString()), Convert.ToInt32(SaveObj.Branch), SaveObj.User_ID, Common.GetObjectID("CoreQuestionnaireLevel1Master",constring), QuestionaryLevel1_ID, 0, 0, "Updated " + QuestionLevel1, false, Convert.ToInt32(SaveObj.MenuID), Convert.ToDateTime(SaveObj.LoggedINDateTime));
                            }
                            else
                            {
                                //   gbl.InsertGPSDetails(Convert.ToInt32(SaveObj.Company_ID.ToString()), Convert.ToInt32(SaveObj.Branch), SaveObj.User_ID, Common.GetObjectID("CoreQuestionnaireLevel1Master",constring), QuestionaryLevel1_ID, 0, 0, "Inserted " + QuestionLevel1, false, Convert.ToInt32(SaveObj.MenuID), Convert.ToDateTime(SaveObj.LoggedINDateTime));
                            }

                            tableUpdated = true;
                        }
                    }
                }

                ErrorMsg = tableUpdated ? "Saved" : "UnSaved";
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                ErrorMsg = string.Empty;

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                ErrorMsg = string.Empty;
            }
            //return ErrorMsg;
            return new JsonResult(ErrorMsg);
        }
        #endregion

        #region ::: CheckQuestion /Mithun:::
        /// <summary>
        /// To Check Question in Question Level1 with Issue Sub AreaID already exists 
        /// </summary>
        public static IActionResult CheckQuestionnaireLevel1(CheckQuestionnaireLevel1List CheckQuestionnaireLevel1Obj, string constring, int LogException)
        {
            int count = 0;
            int companyID = Convert.ToInt32(CheckQuestionnaireLevel1Obj.Company_ID);
            string question = CheckQuestionnaireLevel1Obj.question ?? string.Empty;
            using (SqlConnection conn = new SqlConnection(constring))
            {
                string query = "USP_CheckQuestionnaireLevel1";

                SqlCommand command = null;

                try
                {
                    using (command = new SqlCommand(query, conn))
                    {
                        //SqlParameter[] parameters = new SqlParameter[]
                        //{
                        //    new SqlParameter("@Question", question),
                        //    new SqlParameter("@IssueAreaID", CheckQuestionnaireLevel1Obj.issueAreaID),
                        //    new SqlParameter("@Q1ID", CheckQuestionnaireLevel1Obj.q1ID),
                        //    new SqlParameter("@CompanyID", companyID),
                        //    new SqlParameter("@Exists", SqlDbType.Bit) { Direction = ParameterDirection.Output }
                        //};
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@Question", question);
                        command.Parameters.AddWithValue("@IssueAreaID", CheckQuestionnaireLevel1Obj.issueAreaID);
                        command.Parameters.AddWithValue("@Q1ID", CheckQuestionnaireLevel1Obj.q1ID);
                        command.Parameters.AddWithValue("@CompanyID", companyID);
                        command.Parameters.Add("@Exists", SqlDbType.Bit).Direction = ParameterDirection.Output;


                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }
                        command.ExecuteNonQuery();
                        bool exists = Convert.ToBoolean(command.Parameters["@Exists"].Value);

                        count = exists ? 1 : 0;


                    }
                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }
                    //RedirectToAction("Error");
                    return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
                }

                finally
                {
                    command.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }
                //return count;
                return new JsonResult(count);

            }
        }

        #endregion



        #region ::: Export :::
        /// <summary>
        /// Exporting 
        /// </summary>
        /// //CoreQuestionnaireLevel1Master/Export?exprtType=1&IssueSubAreaId=7&IssueAreaName= IssueSubArea5
        public static List<QuestionnaireLevel1> SelectList(SelectCoreQuestionnaireLevel1List CoreQuestionnaireLevel1Obj, string constring, int LogException)
        {

            try
            {
                int Count = 0;
                int Total = 0;
                int CompanyID = Convert.ToInt32(CoreQuestionnaireLevel1Obj.Company_ID);

                List<QuestionnaireLevel1> IEQuestionnaireLevel1Array = new List<QuestionnaireLevel1>();



                string YesE = CommonFunctionalities.GetResourceString(CoreQuestionnaireLevel1Obj.GeneralCulture.ToString(), "yes").ToString();
                string NoE = CommonFunctionalities.GetResourceString(CoreQuestionnaireLevel1Obj.GeneralCulture.ToString(), "no").ToString();
                string YesL = CommonFunctionalities.GetResourceString(CoreQuestionnaireLevel1Obj.UserCulture.ToString(), "yes").ToString();
                string NoL = CommonFunctionalities.GetResourceString(CoreQuestionnaireLevel1Obj.UserCulture.ToString(), "no").ToString();


                var jsonData = default(dynamic);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    string query = "UP_Select_AM_ERP_QuestionnaireLevel1";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@IssueAreaID", CoreQuestionnaireLevel1Obj.IssueAreaID);
                            command.Parameters.AddWithValue("@CompanyID", CompanyID);
                            command.Parameters.AddWithValue("@LanguageID", CoreQuestionnaireLevel1Obj.LanguageID);
                            command.Parameters.AddWithValue("@GeneralLanguageID", Convert.ToInt32(CoreQuestionnaireLevel1Obj.GeneralLanguageID));
                            command.Parameters.AddWithValue("@YesE", YesE);
                            command.Parameters.AddWithValue("@NoE", NoE);
                            command.Parameters.AddWithValue("@YesL", YesL);
                            command.Parameters.AddWithValue("@NoL", NoL);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    QuestionnaireLevel1 ql = new QuestionnaireLevel1
                                    {
                                        QuestionaryLevel1_ID = reader.GetInt32(reader.GetOrdinal("QuestionaryLevel1_ID")),
                                        QuestionLevel1 = reader.GetString(reader.GetOrdinal("QuestionLevel1")),
                                        QuestionLevel1_IsActive = reader.GetString(reader.GetOrdinal("QuestionLevel1_IsActive"))
                                    };
                                    IEQuestionnaireLevel1Array.Add(ql);
                                }



                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                IQueryable<QuestionnaireLevel1> IQQuestionnaireLevel1 = IEQuestionnaireLevel1Array.AsQueryable();

                IQQuestionnaireLevel1 = IEQuestionnaireLevel1Array.AsQueryable<QuestionnaireLevel1>();


                IQQuestionnaireLevel1 = IQQuestionnaireLevel1.OrderByField<QuestionnaireLevel1>(CoreQuestionnaireLevel1Obj.sidx, CoreQuestionnaireLevel1Obj.sord);




                //---End

                //return Json(jsonData, JsonRequestBehavior.AllowGet);
                return IQQuestionnaireLevel1.ToList();
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return null;
                //return RedirectToAction("Error");
            }
        }
        public static async Task<object> Export(SelectCoreQuestionnaireLevel1List ExportObj, string connString, int LogException)
        {
            IQueryable<QuestionnaireLevel1> IQQuestionnaireLevel1 = null;
            int Count = 0;

            DataTable Dt = new DataTable();
            try
            {

                List<QuestionnaireLevel1> jsonResult = (List<QuestionnaireLevel1>)SelectList(ExportObj, connString, LogException);
                IQQuestionnaireLevel1 = jsonResult.AsQueryable();
                if (ExportObj.filter != "null" && ExportObj.filter != "undefined")
                {
                    Filters filters = JObject.Parse(Common.DecryptString(ExportObj.filter)).ToObject<Filters>();
                    if (filters.rules.Count > 0)
                        IQQuestionnaireLevel1 = IQQuestionnaireLevel1.FilterSearch<QuestionnaireLevel1>(filters);
                }

                if (ExportObj.advanceFilter != "null")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Common.DecryptString(ExportObj.advanceFilter)).ToObject<AdvanceFilter>();
                    IQQuestionnaireLevel1 = IQQuestionnaireLevel1.AdvanceSearch<QuestionnaireLevel1>(advnfilter);
                }
                //var QuestionnaireLevel1Array = from a in IQQuestionnaireLevel1.AsEnumerable()
                //                               select new
                //                               {
                //                                   a.QuestionLevel1,
                //                                   a.QuestionLevel1_IsActive
                //                               };

                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "QuestionnaireLevel1").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "Active").ToString());

                DataTable DtAlignment = new DataTable();
                DtAlignment.Columns.Add("Questionnaire Level1");
                DtAlignment.Columns.Add("Is Active?");

                DtAlignment.Rows.Add(0, 0);

                Count = IQQuestionnaireLevel1.AsEnumerable().Count();
                if (Count > 0)
                {
                    for (int i = 0; i < Count; i++)
                    {
                        Dt.Rows.Add(IQQuestionnaireLevel1.ElementAt(i).QuestionLevel1, IQQuestionnaireLevel1.ElementAt(i).QuestionLevel1_IsActive);
                    }

                    DataTable Dt1 = new DataTable();
                    Dt1.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "IssueArea").ToString());
                    Dt1.Rows.Add(Common.DecryptString(ExportObj.IssueAreaName));
                    ReportExportList reportExportList = new ReportExportList
                    {
                        Company_ID = ExportObj.Company_ID, // Assuming this is available in ExportObj
                        Branch = ExportObj.Branch_ID.ToString(),
                        GeneralLanguageID = ExportObj.LanguageID,
                        UserLanguageID = ExportObj.UserLanguageID,
                        Options = Dt1,
                        dt = Dt,
                        Alignment = DtAlignment,
                        FileName = "QuestionnaireLevel1", // Set a default or dynamic filename
                        Header = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "QuestionnaireLevel1").ToString(), // Set a default or dynamic header
                        exprtType = ExportObj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                        UserCulture = ExportObj.UserCulture
                    };
                    //ReportExport.Export(ExportObj.exprtType, Dt, Dt1, DtAlignment, "QuestionnaireLevel1", GetResourceString(ExportObj.UserCulture.ToString(), "QuestionnaireLevel1").ToString());
                    var result = await ReportExport.Export(reportExportList, connString, LogException);
                    return result.Value;
                    //gbl.InsertGPSDetails(Convert.ToInt32(ExportObj.Company_ID.ToString()), Convert.ToInt32(ExportObj.Branch), ExportObj.User_ID, Common.GetObjectID("CoreQuestionnaireLevel1Master"), 0, 0, 0, "Questionnaire Lvevel1-Export ", false, Convert.ToInt32(ExportObj.MenuID), Convert.ToDateTime(ExportObj.LoggedINDateTime));
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return null;
        }
        #endregion



    }
    public class CheckQuestionnaireLevel1List
    {
        public int Company_ID { get; set; }
        public int issueAreaID { get; set; }
        public int q1ID { get; set; }
        public string question { get; set; }
    }
    public class CoreQuestionnaireLevel1SaveList
    {
        public int Branch { get; set; }
        public int User_ID { get; set; }
        public int UserLanguageID { get; set; }
        public int Company_ID { get; set; }
        public int MenuID { get; set; }
        public string data { get; set; }


        public DateTime LoggedINDateTime { get; set; }

    }
    public class CheckQuestionnaireLevel1LocaleList
    {
        public int IssueAreaID { get; set; }
        public int UserLanguageID { get; set; }
        public int QuestionnaireLevel1LocaleID { get; set; }
        public string QuestionLevel1 { get; set; }
    }
    public class UpdateLocaleCoreQuestionnaireLevel1List
    {
        public int Branch { get; set; }
        public int User_ID { get; set; }
        public int UserLanguageID { get; set; }
        public int Company_ID { get; set; }
        public int MenuID { get; set; }
        public string data { get; set; }
        public DateTime LoggedINDateTime { get; set; }
    }






    public class DeleteCoreQuestionnaireLevel1List
    {
        public int Branch { get; set; }
        public int User_ID { get; set; }
        public int UserLanguageID { get; set; }
        public int Company_ID { get; set; }
        public int MenuID { get; set; }
        public string key { get; set; }
        public string UserCulture { get; set; }
        public DateTime LoggedINDateTime { get; set; }

    }

    public class SelectParticularQuestionnaireLevel1List
    {
        public int UserLanguageID { get; set; }
        public int QuestionnaireLevel1ID { get; set; }

    }
    public class SelectCoreQuestionnaireLevel1List
    {
        public int Company_ID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int IssueAreaID { get; set; }
        public int LanguageID { get; set; }
        public string GeneralCulture { get; set; }
        public string UserCulture { get; set; }
        public string IssueAreaName { get; set; }
        public string sidx { get; set; }
        public string sord { get; set; }
        public int exprtType { get; set; }
        public string Branch { get; set; }
        public int User_ID { get; set; }
        public int MenuID { get; set; }
        public DateTime LoggedINDateTime { get; set; }
        public string filter { get; set; }
        public string advanceFilter { get; set; }
        public int Branch_ID { get; set; }
        public int UserLanguageID { get; set; }

    }

    public class SelectReferenceMasterQuestionnaireLevel1List
    {
        public int Company_ID { get; set; }
        public int UserLanguageID { get; set; }
        public string UserLanguageCode { get; set; }
        public string GeneralLanguageCode { get; set; }
    }
    public class QuestionnaireLevel1
    {
        public int QuestionaryLevel1_ID
        {
            get;
            set;
        }

        public int IssueArea_ID
        {
            get;
            set;
        }
        public int IssueSubArea_ID
        {
            get;
            set;
        }

        public string QuestionLevel1
        {
            get;
            set;
        }

        public string QuestionLevel1_IsActive
        {
            get;
            set;
        }
    }
}
