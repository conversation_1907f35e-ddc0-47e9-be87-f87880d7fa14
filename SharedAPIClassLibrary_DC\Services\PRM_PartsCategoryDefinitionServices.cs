﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Reflection;
using System.Resources;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.PRM_PartsCategoryServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace SharedAPIClassLibrary_AMERP
{
    public class PRM_PartsCategoryDefinitionServices
    {

        int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));


        #region :::GetGlobalResourceObject   Uday Kumar J B 11-07-2024 :::
        /// <summary>
        /// To Get GlobalResourceObject Uday Kumar J B 11-07-2024 15:13
        /// </summary>
        /// 
        public static string GetGlobalResourceObject(string cultureValue, string resourceKey, Assembly assembly = null)
        {
            try
            {
                if (assembly == null)
                {
                    assembly = Assembly.GetExecutingAssembly();
                }

                string cultureIdentifier = cultureValue.Replace("Resource_", "");
                string resourceNamespace = assembly.GetName().Name + ".App_GlobalResources.";
                string resourceFileName = "resource_" + cultureIdentifier.ToLowerInvariant();
                ResourceManager resourceManager = new ResourceManager(resourceNamespace + resourceFileName, assembly);
                string resourceValue = resourceManager.GetString(resourceKey);

                return resourceValue;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error accessing resource: {ex.Message}");
                return string.Empty;
            }
        }
        #endregion


        #region ::: Select Uday Kumar J B 20-08-2024 :::
        /// <summary>
        /// To Select PartsCategoryDefinition
        /// </summary>
        /// 
        public static IQueryable<PartsCategoryDefinitionMaster> getLandingGridData(string connString, SelectPRM_PartsCategoryDefinitionList SelectPRM_PartsCategoryDefinitionobj)
        {
            IQueryable<PartsCategoryDefinitionMaster> IQPartsCategoryDefinitionMaster = null;
            List<PartsCategoryDefinitionMaster> partsCategoryDefinitionMasters = new List<PartsCategoryDefinitionMaster>();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int Language_ID = Convert.ToInt32(SelectPRM_PartsCategoryDefinitionobj.UserLanguageID);
                int Company_ID = Convert.ToInt32(SelectPRM_PartsCategoryDefinitionobj.Company_ID);
                int Branch_ID = Convert.ToInt32(SelectPRM_PartsCategoryDefinitionobj.Branch);
                int ParentCompID = GetParentCompanyID(Company_ID, connString);  // Fetch ParentCompID using ADO.NET

                string Yes = GetGlobalResourceObject(SelectPRM_PartsCategoryDefinitionobj.UserCulture.ToString(), "Yes").ToString();
                string No = GetGlobalResourceObject(SelectPRM_PartsCategoryDefinitionobj.UserCulture.ToString(), "No").ToString();

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand("Up_Get_AM_ERP_GetLandingGridData", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@Company_ID", Company_ID);
                        command.Parameters.AddWithValue("@Branch_ID", Branch_ID);
                        command.Parameters.AddWithValue("@Yes", Yes);
                        command.Parameters.AddWithValue("@No", No);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                PartsCategoryDefinitionMaster item = new PartsCategoryDefinitionMaster
                                {
                                    PartsCategoryDefinition_ID = reader.GetInt32(reader.GetOrdinal("PartsCategoryDefinition_ID")),
                                    PartsCategory_Description = reader.GetString(reader.GetOrdinal("PartsCategory_Description")),
                                    PartsCategory_ConversionFactor = reader.GetDecimal(reader.GetOrdinal("ConversionFactor")),
                                    PartsCategory_ProfitValue = reader.GetDecimal(reader.GetOrdinal("ProfitValue")),
                                    PartsCategory_MRPFactor = reader.GetDecimal(reader.GetOrdinal("MRPFactor")),
                                    PartsCategory_IsActive = reader.GetString(reader.GetOrdinal("PartsCategory_IsActive"))
                                };

                                partsCategoryDefinitionMasters.Add(item);
                            }
                        }
                    }
                }

                IQPartsCategoryDefinitionMaster = partsCategoryDefinitionMasters.AsQueryable();
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return IQPartsCategoryDefinitionMaster;
        }

        private static int GetParentCompanyID(int companyID, string connString)
        {
            using (SqlConnection connection = new SqlConnection(connString))
            {
                using (SqlCommand command = new SqlCommand("GetCompany_Parent_IDSelect", connection))
                {
                    command.Parameters.AddWithValue("@Company_ID", companyID);
                    connection.Open();
                    return (int)command.ExecuteScalar();
                }
            }
        }



        public static IActionResult Select(string connString, SelectPRM_PartsCategoryDefinitionList SelectPRM_PartsCategoryDefinitionobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            var jsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int Count = 0;
                int Total = 0;
                int LanguageID = Convert.ToInt32(SelectPRM_PartsCategoryDefinitionobj.UserLanguageID);
                int CompanyID = Convert.ToInt32(SelectPRM_PartsCategoryDefinitionobj.Company_ID);
                int BranchID = Convert.ToInt32(SelectPRM_PartsCategoryDefinitionobj.Branch);
                int ParentCompID = GetParentCompanyIDselect(CompanyID, connString);

                string Select = GetGlobalResourceObject(SelectPRM_PartsCategoryDefinitionobj.UserCulture.ToString(), "select").ToString();
                string Description = "-1:--" + Select + "--;";

                DataTable dtCategory = GetDataTable("GetPartsCategory", connString, new SqlParameter("@CompanyID", CompanyID), new SqlParameter("@ParentCompID", ParentCompID));
                DataTable dtPartsCategoryDefinition = GetDataTable("GetPartsCategoryDefinition", connString, new SqlParameter("@CompanyID", CompanyID), new SqlParameter("@BranchID", BranchID));
                DataTable dtPartsCategoryLocale = GetDataTable("GetPartsCategoryLocale", connString, new SqlParameter("@CompanyID", CompanyID), new SqlParameter("@LanguageID", LanguageID));

                if (LanguageID == Convert.ToInt32(SelectPRM_PartsCategoryDefinitionobj.GeneralLanguageID))
                {
                    foreach (DataRow row in dtCategory.Rows)
                    {
                        Description += row["PartsCategory_ID"] + ":" + row["Description"] + ";";
                    }
                }
                else
                {
                    foreach (DataRow row in dtPartsCategoryLocale.Rows)
                    {
                        Description += row["PartsCategory_ID"] + ":" + row["Description"] + ";";
                    }
                }

                Description = Description.TrimEnd(';');

                var partsCategoryDefinitionList = dtPartsCategoryDefinition.AsEnumerable().Select(row => new PartsCategoryDefinitionMaster
                {
                    PartsCategoryDefinition_ID = Convert.ToInt32(row["PartsCategoryDefinition_ID"]),
                    PartsCategory_Description = dtCategory.AsEnumerable().FirstOrDefault(catRow => catRow.Field<int>("PartsCategory_ID") == row.Field<int>("PartsCategory_ID"))["Description"].ToString(),
                    PartsCategory_ConversionFactor = row.Field<decimal?>("ConversionFactor") ?? 0.00m,
                    PartsCategory_ProfitValue = row.Field<decimal?>("ProfitValue") ?? 0.00m,
                    PartsCategory_MRPFactor = row.Field<decimal?>("MRPFactor") ?? 0.00m,
                    PartsCategory_IsActive = row.Field<bool>("IsActive") ? "Yes" : "No"
                }).AsQueryable();

                if (_search)
                {
                    Filters searchFilters = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    if (searchFilters.rules.Count > 0)
                    {
                        partsCategoryDefinitionList = partsCategoryDefinitionList.FilterSearch<PartsCategoryDefinitionMaster>(searchFilters);
                    }
                }

                if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse(Common.DecryptString(advnceFilters)).ToObject<AdvanceFilter>();
                    partsCategoryDefinitionList = partsCategoryDefinitionList.AdvanceSearch<PartsCategoryDefinitionMaster>(advnfilter);
                }

                partsCategoryDefinitionList = partsCategoryDefinitionList.OrderByField<PartsCategoryDefinitionMaster>(sidx, sord);

                Count = partsCategoryDefinitionList.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;

                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                jsonData = new
                {
                    total = Total,
                    page = page,
                    rows = partsCategoryDefinitionList.Skip((page - 1) * rows).Take(rows).Select(a => new
                    {
                        ID = a.PartsCategoryDefinition_ID,
                        edit = "<a title='Edit' href='#' id='" + a.PartsCategoryDefinition_ID + "' key='" + a.PartsCategoryDefinition_ID + "' editmode='false' class='PartsCategoryEdit font-icon-class'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                        delete = "<input type='checkbox' key='" + a.PartsCategoryDefinition_ID + "' defaultchecked='' id='chk" + a.PartsCategoryDefinition_ID + "' class='PartsCategoryDelete'/>",
                        PartsCategory_Description = a.PartsCategory_Description,
                        PartsCategory_ConversionFactor = a.PartsCategory_ConversionFactor.ToString("0.000"),
                        PartsCategory_ProfitValue = a.PartsCategory_ProfitValue.ToString("0.00"),
                        PartsCategory_MRPFactor = a.PartsCategory_MRPFactor.ToString("0.000"),
                        PartsCategory_IsActive = a.PartsCategory_IsActive
                    }).ToList(),
                    records = Count,
                    Description,
                    filter = filters,
                    advanceFilter = advnceFilters,
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }

        private static DataTable GetDataTable(string storedProcedure, string connString, params SqlParameter[] parameters)
        {
            using (var connection = new SqlConnection(connString))
            {
                using (var command = new SqlCommand(storedProcedure, connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    if (parameters != null)
                    {
                        command.Parameters.AddRange(parameters);
                    }

                    using (var adapter = new SqlDataAdapter(command))
                    {
                        var dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        return dataTable;
                    }
                }
            }
        }

        private static int GetParentCompanyIDselect(int companyID, string connString)
        {
            using (var connection = new SqlConnection(connString))
            {
                using (var command = new SqlCommand("GetCompany_Parent_ID", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@CompanyID", companyID);
                    connection.Open();
                    return (int)command.ExecuteScalar();
                }
            }
        }
        #endregion


        #region ::: Save Uday Kumar J B 20-08-2024:::
        /// <summary>
        /// To Insert and Update PartsCategoryDefinition
        /// </summary>
        /// 
        public static IActionResult Save(string connString, SavePRM_PartsCategoryDefinitionList SavePRM_PartsCategoryDefinitionobj)
        {
            string Msg = string.Empty;
            SqlConnection connection = null;
            SqlTransaction transaction = null;
            JObject jObj = null;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JTokenReader jr = null;
                jObj = JObject.Parse(SavePRM_PartsCategoryDefinitionobj.data);
                int Count = jObj["rows"].Count();

                // Initialize connection
                connection = new SqlConnection(connString);
                connection.Open();
                transaction = connection.BeginTransaction();

                for (int i = 0; i < Count; i++)
                {
                    PRM_PartsCategoryDefinition SRow = jObj["rows"].ElementAt(i).ToObject<PRM_PartsCategoryDefinition>();
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Description"]);
                    jr.Read();
                    int PartsCategory_ID = Convert.ToInt32(jr.Value.ToString());

                    if (SRow.PartsCategoryDefinition_ID != 0)
                    {
                        // Update existing record
                        using (SqlCommand command = new SqlCommand("UpdatePRM_PartsCategoryDefinition", connection, transaction))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@PartsCategoryDefinition_ID", SRow.PartsCategoryDefinition_ID);
                            command.Parameters.AddWithValue("@PartsCategory_ID", PartsCategory_ID);
                            command.Parameters.AddWithValue("@ConversionFactor", SRow.ConversionFactor);
                            command.Parameters.AddWithValue("@ProfitValue", SRow.ProfitValue);
                            command.Parameters.AddWithValue("@MRPFactor", SRow.MRPFactor);
                            command.Parameters.AddWithValue("@IsActive", SRow.IsActive);
                            command.ExecuteNonQuery();
                        }
                        //  gbl.InsertGPSDetails(Convert.ToInt32(SavePRM_PartsCategoryDefinitionobj.Company_ID), Convert.ToInt32(SavePRM_PartsCategoryDefinitionobj.Branch), Convert.ToInt32(SavePRM_PartsCategoryDefinitionobj.User_ID), Convert.ToInt32(Common.GetObjectID("PRM_PartsCategoryDefinition")), SRow.PartsCategoryDefinition_ID, 0, 0, "Update", false, Convert.ToInt32(SavePRM_PartsCategoryDefinitionobj.MenuID), Convert.ToDateTime(SavePRM_PartsCategoryDefinitionobj.LoggedIDateTime));
                    }
                    else
                    {
                        // Insert new record
                        using (SqlCommand command = new SqlCommand("InsertPRM_PartsCategoryDefinition", connection, transaction))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@Company_ID", Convert.ToInt32(SavePRM_PartsCategoryDefinitionobj.Company_ID));
                            command.Parameters.AddWithValue("@Branch_ID", Convert.ToInt32(SavePRM_PartsCategoryDefinitionobj.Branch));
                            command.Parameters.AddWithValue("@PartsCategory_ID", PartsCategory_ID);
                            command.Parameters.AddWithValue("@ConversionFactor", SRow.ConversionFactor);
                            command.Parameters.AddWithValue("@ProfitValue", SRow.ProfitValue);
                            command.Parameters.AddWithValue("@MRPFactor", SRow.MRPFactor);
                            command.Parameters.AddWithValue("@IsActive", SRow.IsActive);
                            int newID = Convert.ToInt32(command.ExecuteScalar());
                            SRow.PartsCategoryDefinition_ID = newID;
                        }
                        //  gbl.InsertGPSDetails(Convert.ToInt32(SavePRM_PartsCategoryDefinitionobj.Company_ID), Convert.ToInt32(SavePRM_PartsCategoryDefinitionobj.Branch), Convert.ToInt32(SavePRM_PartsCategoryDefinitionobj.User_ID), Convert.ToInt32(Common.GetObjectID("PRM_PartsCategoryDefinition")), SRow.PartsCategoryDefinition_ID, 0, 0, "Insert", false, Convert.ToInt32(SavePRM_PartsCategoryDefinitionobj.MenuID), Convert.ToDateTime(SavePRM_PartsCategoryDefinitionobj.LoggedIDateTime));
                    }
                }
                transaction.Commit();
                Msg = "Saved";
            }
            catch (Exception ex)
            {
                if (transaction != null)
                {
                    transaction.Rollback();
                }
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                Msg = string.Empty;
            }
            finally
            {
                if (connection != null && connection.State == ConnectionState.Open)
                {
                    connection.Close();
                }
            }
            return new JsonResult(Msg);
        }

        #endregion


        #region ::: CheckPartsCategory Uday Kumar J B 20-08-2024:::
        /// <summary>
        /// To Check PartsCategoryDefinition already exists 
        /// </summary>
        /// 
        public static IActionResult CheckPartsCategory(string connString, CheckPartsCategoryPRM_PartsCategoryDefinitionList CheckPartsCategoryPRM_PartsCategoryDefinitionobj)
        {
            int Count = 0;
            SqlConnection connection = null;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int Company_ID = Convert.ToInt32(CheckPartsCategoryPRM_PartsCategoryDefinitionobj.Company_ID);
                int Branch_ID = Convert.ToInt32(CheckPartsCategoryPRM_PartsCategoryDefinitionobj.Branch);

                // Initialize connection
                connection = new SqlConnection(connString);
                connection.Open();

                using (SqlCommand command = new SqlCommand("Up_Check_AM_ERP_CheckPartsCategory", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@Company_ID", Company_ID);
                    command.Parameters.AddWithValue("@Branch_ID", Branch_ID);
                    command.Parameters.AddWithValue("@PartsCategory_ID", CheckPartsCategoryPRM_PartsCategoryDefinitionobj.PartsCategory_ID);
                    command.Parameters.AddWithValue("@PartsCategoryDefinition_ID", CheckPartsCategoryPRM_PartsCategoryDefinitionobj.PartsCategoryDefinition_ID);

                    Count = Convert.ToInt32(command.ExecuteScalar());
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            finally
            {
                if (connection != null && connection.State == ConnectionState.Open)
                {
                    connection.Close();
                }
            }
            return new JsonResult(Count);
        }

        #endregion


        #region ::: Delete Uday Kumar J B 20-08-2024 :::
        /// <summary>
        /// To Delete PartsCategoryDefinition
        /// </summary>
        /// 
        public static IActionResult Delete(string connString, DeletePRM_PartsCategoryDefinitionList DeletePRM_PartsCategoryDefinitionobj)
        {
            string Msg = string.Empty;
            SqlConnection connection = null;
            SqlTransaction transaction = null;
            JObject jObj = null;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                jObj = JObject.Parse(DeletePRM_PartsCategoryDefinitionobj.key);
                int Count = jObj["rows"].Count();
                int ID = 0;

                connection = new SqlConnection(connString);
                connection.Open();
                transaction = connection.BeginTransaction();

                for (int i = 0; i < Count; i++)
                {
                    JTokenReader jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jTR.Read();
                    ID = Convert.ToInt32(jTR.Value);

                    using (SqlCommand command = new SqlCommand("usp_DeletePartsCategoryDefinition", connection, transaction))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@PartsCategoryDefinition_ID", ID);
                        command.ExecuteNonQuery();
                    }
                }

                transaction.Commit();

                // gbl.InsertGPSDetails(Convert.ToInt32(DeletePRM_PartsCategoryDefinitionobj.Company_ID), Convert.ToInt32(DeletePRM_PartsCategoryDefinitionobj.Branch), Convert.ToInt32(DeletePRM_PartsCategoryDefinitionobj.User_ID), Convert.ToInt32(Common.GetObjectID("PRM_PartsCategoryDefinition")), ID, 0, 0, "Delete", false, Convert.ToInt32(DeletePRM_PartsCategoryDefinitionobj.MenuID), Convert.ToDateTime(DeletePRM_PartsCategoryDefinitionobj.LoggedIDateTime));
                Msg += GetGlobalResourceObject(DeletePRM_PartsCategoryDefinitionobj.UserCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (Exception ex)
            {
                if (transaction != null)
                {
                    transaction.Rollback();
                }

                if (ex.InnerException != null && ex.InnerException.InnerException != null && ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    Msg += GetGlobalResourceObject(DeletePRM_PartsCategoryDefinitionobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }
                }
            }
            finally
            {
                if (connection != null && connection.State == ConnectionState.Open)
                {
                    connection.Close();
                }
            }
            return new JsonResult(Msg);
        }

        #endregion


        #region ::: Export Uday Kumar J B 20-08-2024 :::
        /// <summary>
        /// To Export 
        /// </summary>
        public static IActionResult Export(string connString, SelectPRM_PartsCategoryDefinitionList SelectPRM_PartsCategoryDefinitionobj, string filter, string advanceFilter, string sidx, string sord)
        {
            int count = 0;
            int logException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                IQueryable<PartsCategoryDefinitionMaster> iQPartsCategoryDefinitionMaster = null;
                DataTable dtData = new DataTable();
                iQPartsCategoryDefinitionMaster = getLandingGridData(connString, SelectPRM_PartsCategoryDefinitionobj);

                if (!string.IsNullOrEmpty(filter) && filter != "null")
                {
                    Filters parsedFilters = JObject.Parse(Common.DecryptString(filter)).ToObject<Filters>();
                    if (parsedFilters.rules.Count() > 0)
                    {
                        iQPartsCategoryDefinitionMaster = iQPartsCategoryDefinitionMaster.FilterSearch<PartsCategoryDefinitionMaster>(parsedFilters);
                    }
                }

                if (!string.IsNullOrEmpty(advanceFilter) && advanceFilter != "null")
                {
                    AdvanceFilter advnFilter = JObject.Parse(Common.DecryptString(advanceFilter)).ToObject<AdvanceFilter>();
                    if (advnFilter.rules.Count() > 0)
                    {
                        iQPartsCategoryDefinitionMaster = iQPartsCategoryDefinitionMaster.AdvanceSearch<PartsCategoryDefinitionMaster>(advnFilter);
                    }
                }

                iQPartsCategoryDefinitionMaster = iQPartsCategoryDefinitionMaster.OrderByField<PartsCategoryDefinitionMaster>(sidx.ToString(), sord.ToString());

                var partsCategoryArray = from a in iQPartsCategoryDefinitionMaster.AsEnumerable()
                                         select new
                                         {
                                             a.PartsCategory_Description,
                                             a.PartsCategory_ConversionFactor,
                                             a.PartsCategory_ProfitValue,
                                             a.PartsCategory_MRPFactor,
                                             a.PartsCategory_IsActive
                                         };

                dtData.Columns.Add(GetGlobalResourceObject(SelectPRM_PartsCategoryDefinitionobj.UserCulture.ToString(), "PartsCategory").ToString());
                dtData.Columns.Add(GetGlobalResourceObject(SelectPRM_PartsCategoryDefinitionobj.UserCulture.ToString(), "ConversionFactor").ToString());
                dtData.Columns.Add(GetGlobalResourceObject(SelectPRM_PartsCategoryDefinitionobj.UserCulture.ToString(), "ProfitValue").ToString());
                dtData.Columns.Add(GetGlobalResourceObject(SelectPRM_PartsCategoryDefinitionobj.UserCulture.ToString(), "MRPFactor").ToString());
                dtData.Columns.Add(GetGlobalResourceObject(SelectPRM_PartsCategoryDefinitionobj.UserCulture.ToString(), "Active").ToString());

                count = partsCategoryArray.Count();
                if (count > 0)
                {
                    for (int i = 0; i < count; i++)
                    {
                        dtData.Rows.Add(partsCategoryArray.ElementAt(i).PartsCategory_Description, partsCategoryArray.ElementAt(i).PartsCategory_ConversionFactor, partsCategoryArray.ElementAt(i).PartsCategory_ProfitValue, partsCategoryArray.ElementAt(i).PartsCategory_MRPFactor, partsCategoryArray.ElementAt(i).PartsCategory_IsActive);
                    }

                    DataTable dtCriteria = new DataTable();
                    DataTable dtAlignment = new DataTable();
                    dtAlignment.Columns.Add("PartsCategory");
                    dtAlignment.Columns.Add("ConversionFactor");
                    dtAlignment.Columns.Add("ProfitValue");
                    dtAlignment.Columns.Add("MRPFactor");
                    dtAlignment.Columns.Add("Active");
                    dtAlignment.Rows.Add(0, 2, 2, 2, 1);

                    // ReportExport.Export(SelectPRM_PartsCategoryDefinitionobj.exprtType, dtData, dtCriteria, dtAlignment, "PartsCategoryDefinition", GetGlobalResourceObject(SelectPRM_PartsCategoryDefinitionobj.UserCulture.ToString(), "PartsCategoryDefinition").ToString());
                }
            }
            catch (Exception ex)
            {
                if (logException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(null);
        }
        #endregion


        #region ::: PRM_PartsCategoryDefinition list and obj classes Uday Kumar J B 20-08-2024 :::
        /// <summary>
        /// To PRM_PartsCategoryDefinition 
        /// </summary>
        public class ExportPRM_PartsCategoryDefinitionList
        {
            public int exprtType { get; set; }
            public string UserCulture { get; set; }
        }
        public class DeletePRM_PartsCategoryDefinitionList
        {
            public string key { get; set; }
            public string UserCulture { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedIDateTime { get; set; }
        }

        public class CheckPartsCategoryPRM_PartsCategoryDefinitionList
        {
            public int PartsCategory_ID { get; set; }
            public int PartsCategoryDefinition_ID { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
        }
        public class SavePRM_PartsCategoryDefinitionList
        {
            public string data { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedIDateTime { get; set; }
        }

        public class SelectPRM_PartsCategoryDefinitionList
        {
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public string UserCulture { get; set; }
            public string GeneralLanguageID { get; set; }
            public int exprtType { get; set; }
        }

        #endregion


        #region ::: PartsCategoryDefinitionMaster Classes Uday Kumar J B 20-08-2024 :::
        /// <summary>
        /// PartsCategoryDefinitionMaster
        /// </summary>
        public class PartsCategoryDefinitionMaster
        {
            public int PartsCategoryDefinition_ID
            {
                get;
                set;
            }
            public int PartsCategory_ID
            {
                get;
                set;
            }

            public string PartsCategory_Description
            {
                get;
                set;
            }

            public decimal PartsCategory_ConversionFactor
            {
                get;
                set;
            }

            public decimal PartsCategory_ProfitValue
            {
                get;
                set;
            }

            public decimal PartsCategory_MRPFactor
            {
                get;
                set;
            }

            public string PartsCategory_IsActive
            {
                get;
                set;
            }
        }
        #endregion

    }
}
