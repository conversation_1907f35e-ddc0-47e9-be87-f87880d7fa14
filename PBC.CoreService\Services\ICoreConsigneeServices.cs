using Microsoft.AspNetCore.Mvc;
using PBC.CoreService.Utilities.DTOs;

namespace PBC.CoreService.Services
{
    public interface ICoreConsigneeServices
    {
        Task<IActionResult> LoadBranchDropdown(LoadBranchDropdownList LoadBranchDropdownObj, string constring, int LogException);
        Task<IActionResult> Select(string connString, GetAllconsigneeList SelectConsigneeObj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advanceFilter);
        Task<IQueryable<ConsigneeMaster>> GetAllconsignee(GetAllconsigneeList GetAllconsigneeObj, string connstring, int LogException);
        Task<IActionResult> Save(SaveList SaveObj, string connString, int LogException);
        Task<IActionResult> CheckConsignee(CheckConsigneeList CheckConsigneeObj, string connString, int LogException);
        Task<IActionResult> CheckConsigneeAddress(CheckConsigneeAddressList CheckConsigneeAddressObj, string connString, int LogException);
        Task<IActionResult> SelectParticularConsignee(SelectParticularConsigneeList SelectParticularConsigneeObj, string connString, int LogException);
        Task<IActionResult> Delete(DeleteList DeleteObj, string connString, int LogException);
        Task<ActionResult> UpdateLocale(UpdateLocaleList UpdateLocaleObj, string connString, int LogException);
        Task<IActionResult> CheckConsigneeLocale(CheckConsigneeLocaleList CheckConsigneeLocaleObj, string connString, int LogException);
        Task<IActionResult> CheckConsigneeAddressLocale(CheckConsigneeAddressLocaleList CheckConsigneeAddressLocaleObj, string connString, int LogException);
        Task<IActionResult> CheckWareHouse(CheckWareHouseList CheckWareHouseObj, string connString, int LogException);
        Task<object> Export(GetAllconsigneeList ExportObj, string connString, int LogException, string filter, string advanceFilter, string sidx, string sord);
    }
}