﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CorePartsRatecontractImportServices
    {

        public static int? ErrorCode = null;
        public static string ErrorCountval = string.Empty;
        public static string templateDirectory = "Template";
        public static string BucketFilePath = "quest-partsassist\\EPC_UploadedFiles";
        public static DataTable ActualData = null;
        public static DataTable UploadedColumnNames = null;
        public static List<Attachements> AttachmentData = null;



        #region ::: ImportPartsRatecontractTemplate :::
        /// <summary>
        /// ImportPartsRatecontractTemplate
        /// </summary>
        public static HttpResponseMessage ImportPartsRatecontractTemplate(int LogException)
        {
            HttpResponseMessage response;
            try
            {
                // Define the file path and check if the file exists
                string filePath = Path.Combine(templateDirectory, "PartsRateContractTemplate.xls");
                if (!File.Exists(filePath))
                {
                    response = new HttpResponseMessage(HttpStatusCode.NotFound)
                    {
                        Content = new StringContent("Template file not found.")
                    };
                    return response;
                }

                // Read the file as byte array
                byte[] fileBytes = File.ReadAllBytes(filePath);

                // Create response message
                response = new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new ByteArrayContent(fileBytes)
                };
                response.Content.Headers.ContentDisposition = new System.Net.Http.Headers.ContentDispositionHeaderValue("attachment")
                {
                    FileName = "PartsRateContractTemplate.xls"
                };
                response.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                // gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"].ToString()), Convert.ToInt32(Session["Branch"]), User.User_ID, Common.GetObjectID("CoreUploadPriceList"), 0, 0, 0, "Downloaded Price List Template", false, Convert.ToInt32(Session["MenuID"]), Convert.ToDateTime(Session["LoggedINDateTime"]));
            }
            catch (Exception ex)
            {
                // Log exception if LogException flag is set
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, $"{ex.GetType().FullName}: {ex.Message}", ex.TargetSite.ToString(), ex.StackTrace);
                }

                // Return an error response in case of failure
                response = new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("An error occurred while generating the template.")
                };
            }
            return response;
        }

        #endregion



        #region ::: UploadFile Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// UploadFile
        /// </summary>
        public static HttpResponseMessage UploadFile(IFormFile partsPrice, string connString, int companyId, DateTime effectiveFrom, DateTime effectiveTo, int userId, int menuId, int branchId, string userCulture, DateTime loggedInDateTime, int Currency_ID)
        {
            DataTable dt = null;

            try
            {
                // Check file extension
                string fileExtension = Path.GetExtension(partsPrice.FileName);
                if (fileExtension == ".xls" || fileExtension == ".xlsx" || fileExtension == ".csv")
                {
                    // Read the Excel file into a DataTable
                    dt = Common.ExcelReader((Stream)partsPrice, partsPrice.FileName);

                    // Validate file columns
                    if (ValidateColumns(dt))
                    {
                        // Validate and process the DataTable content
                        var partClass = ValidateParts(dt, effectiveFrom, effectiveTo, connString, companyId, Currency_ID);
                        if (partClass.PartsList.Count > 0)
                        {
                            SaveCustomerPartsRateContract(partClass.PartsList, connString, companyId, userId, branchId);
                        }

                        // Check for errors and return error file if needed
                        if (partClass.HasError)
                        {
                            return CreateErrorFileResponse(partClass.ErrorString);
                        }

                        // Calculate error counts
                        int errorcount = Convert.ToInt32(dt.Rows.Count) - partClass.PartsList.Count;
                        ErrorCountval = errorcount + "$" + dt.Rows.Count;
                        ErrorCode = partClass.HasError && partClass.PartsList.Count > 0 ? 1 : !partClass.HasError && partClass.PartsList.Count > 0 ? 2 : 0;
                        //gbl.InsertGPSDetails(Convert.ToInt32(companyId.ToString()), Convert.ToInt32(branchId), userId, Common.GetObjectID("CoreUploadPriceList"), 0, 0, 0, "Uploaded Price List", false, Convert.ToInt32(menuId), Convert.ToDateTime(loggedInDateTime));

                        // Log upload activity
                    }
                    else
                    {
                        ErrorCode = 4; // Column validation failed
                    }
                }
                else
                {
                    ErrorCode = 3; // Invalid file type
                }

                // Return success response with error code information
                return new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new StringContent($"{{\"ErrorCode\": {ErrorCode}}}", System.Text.Encoding.UTF8, "application/json")
                };
            }
            catch (Exception ex)
            {
                // Log exception
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                return new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("An error occurred while processing your request.")
                };
            }
        }

        private static bool ValidateColumns(DataTable dt)
        {
            // Validate DataTable columns
            return dt.Columns[0].Caption.Trim().ToUpper() == "CUSTOMER#" &&
                   dt.Columns[1].Caption.Trim().ToUpper() == "CUSTOMER NAME" &&
                   dt.Columns[2].Caption.Trim().ToUpper() == "PREFIX" &&
                   dt.Columns[3].Caption.Trim().ToUpper() == "PARTNUMBER" &&
                   dt.Columns[4].Caption.Trim().ToUpper() == "PART DESCRIPTION" &&
                   dt.Columns[5].Caption.Trim().ToUpper() == "SCALE" &&
                   dt.Columns[6].Caption.Trim().ToUpper() == "PRICE$" &&
                   dt.Columns[7].Caption.Trim().ToUpper() == "CURRENCY";
        }

        private static HttpResponseMessage CreateErrorFileResponse(string errorContent)
        {
            var response = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(errorContent)
            };
            response.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/excel");
            response.Content.Headers.ContentDisposition = new System.Net.Http.Headers.ContentDispositionHeaderValue("attachment")
            {
                FileName = "ErrorFile.xls"
            };
            return response;
        }
        #endregion


        #region ::: ValidateParts Uday Kumar J B 21-08-2024:::
        /// <summary>
        /// ValidateParts
        /// </summary>
        /// 
        public static EncapPartsRateContractClass ValidateParts(DataTable dt, DateTime effectiveFrom, DateTime effectiveTo, string connString, int Company_ID, int CurrencyID)
        {
            List<PartRatecontract> partsList = new List<PartRatecontract>();
            StringBuilder sb = new StringBuilder();
            sb.Append("<table border='1'><thead><tr style='font-weight:bold'><td>Customer#</td><td>Customer Name</td><td>Prefix</td><td>PartNumber</td><td>Description</td><td>Scale</td><td>Price$</td><td>Currency</td><td>Remarks</td></tr></thead>");
            bool IsError = false; bool IsErrorinFile = false;

            using (SqlConnection conn = new SqlConnection(connString))
            {
                conn.Open();
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    IsError = false;
                    string Remarks = string.Empty;
                    PartRatecontract rowObj = new PartRatecontract();
                    if (dt.Rows[i][0] == null || dt.Rows[i][0].ToString() == "")
                    {
                        IsError = true; IsErrorinFile = true;
                        Remarks += "CustomerCodeCannotBeBlank<br/>";
                    }
                    if (dt.Rows[i][2] == null || dt.Rows[i][2].ToString() == "")
                    {
                        IsError = true; IsErrorinFile = true;
                        Remarks += "PartPrefixisBlank<br/>";
                    }
                    if (dt.Rows[i][3] == null || dt.Rows[i][3].ToString() == "")
                    {
                        IsError = true; IsErrorinFile = true;
                        Remarks += "PartNumberisBlank<br/>";
                    }
                    if (dt.Rows[i][6] == null || dt.Rows[i][6].ToString() == "")
                    {
                        IsError = true; IsErrorinFile = true;
                        Remarks += "RateCannotbeZeroOrBlank<br/>";
                    }

                    if (!IsError)
                    {
                        using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_ValidatePartsGetPartyByCode", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@PartyCode", dt.Rows[i][0].ToString());
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (!reader.HasRows)
                                {
                                    IsError = true; IsErrorinFile = true;
                                    Remarks += "InvalidCustomerNumber<br/>";
                                }
                            }
                        }
                    }

                    if (!IsError)
                    {
                        using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_ValidatePartsGetPartyByName", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@PartyName", dt.Rows[i][1].ToString());
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (!reader.HasRows)
                                {
                                    IsError = true; IsErrorinFile = true;
                                    Remarks += "InvalidCustomer<br/>";
                                }
                            }
                        }
                    }

                    if (!IsError)
                    {
                        using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_ValidatePartsGetCompanyParentID", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@CompanyID", Convert.ToInt32(Company_ID));
                            int parentCompID = (int)cmd.ExecuteScalar();

                            cmd.CommandText = "Up_Sel_Am_Erp_ValidatePartsGetPartByPrefixAndNumber";
                            cmd.Parameters.Clear();
                            cmd.Parameters.AddWithValue("@PartPrefix", dt.Rows[i][2].ToString());
                            cmd.Parameters.AddWithValue("@PartNumber", dt.Rows[i][3].ToString());
                            cmd.Parameters.AddWithValue("@CompanyID", Convert.ToInt32(Company_ID));
                            cmd.Parameters.AddWithValue("@ParentCompanyID", parentCompID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (!reader.HasRows)
                                {
                                    IsError = true; IsErrorinFile = true;
                                    Remarks += "Invalidpartnumber<br/>";
                                }
                            }
                        }
                    }

                    if (!IsError)
                    {
                        using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_ValidatePartsGetCurrencyID", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@CurrencyCode", dt.Rows[i][7].ToString());
                            if ((int?)cmd.ExecuteScalar() == null)
                            {
                                IsError = true; IsErrorinFile = true;
                                Remarks += "InvalidCurrencycode<br/>";
                            }
                        }
                    }

                    if (!IsError)
                    {
                        using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_ValidatePartsGetPartsPriceDetails", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@PartID", dt.Rows[i][3].ToString());
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.HasRows)
                                {
                                    reader.Read();
                                    int? Currency_ID = reader["Currency_ID"] as int?;
                                    if (Currency_ID.HasValue)
                                    {
                                        using (SqlCommand currencyCmd = new SqlCommand("Up_Sel_Am_Erp_RefMasterDetail_NameCorePartsRatecontractImport", conn))
                                        {
                                            currencyCmd.Parameters.AddWithValue("@Currency_ID", Currency_ID.Value);
                                            string Currency = currencyCmd.ExecuteScalar().ToString();
                                            IsError = true; IsErrorinFile = true;
                                            Remarks += Currency + " NotSetForthispart<br/>";
                                        }
                                    }
                                }
                            }
                        }
                    }

                    if (!IsError)
                    {
                        if (Convert.ToInt32(dt.Rows[i][6]) == 0)
                        {
                            IsError = true; IsErrorinFile = true;
                            Remarks += "RateCannotbeZero<br/>";
                        }
                        else if (!IsNumber(dt.Rows[i][6]))
                        {
                            IsError = true; IsErrorinFile = true;
                            Remarks += "InvalidRate<br/>";
                        }
                    }

                    if (!IsError)
                    {
                        if (!CheckIFPartExists(dt.Rows[i][2].ToString(), dt.Rows[i][3].ToString(), connString, Company_ID))
                        {
                            IsError = true; IsErrorinFile = true;
                            Remarks += "PartNumbernotfound<br/>";
                        }
                        else
                        {
                            rowObj = SelectPart(dt.Rows[i][2].ToString(), dt.Rows[i][3].ToString(), connString, Company_ID);
                            if (rowObj == null)
                            {
                                IsError = true; IsErrorinFile = true;
                                Remarks += "PartNumbernotfound<br/>";
                            }
                            else
                            {
                                rowObj.effectiveFrom = effectiveFrom;
                                rowObj.effectiveTo = effectiveTo;
                            }

                            if (dt.AsEnumerable().Where(a => a.Field<string>("PartyCode") == dt.Rows[i][0].ToString() && a.Field<string>("PartPrefix") == dt.Rows[i][2].ToString() && a.Field<string>("PartNumber") == dt.Rows[i][3].ToString()).ToList().Count > 1)
                            {
                                IsError = true; IsErrorinFile = true;
                                Remarks += "Duplicatepartnumber<br/>";
                            }
                        }
                    }

                    if (!IsError)
                    {
                        using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_ValidatePartsGetPartyPartsRateContract", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@PartyID", dt.Rows[i][0].ToString());
                            cmd.Parameters.AddWithValue("@PartID", dt.Rows[i][3].ToString());
                            cmd.Parameters.AddWithValue("@EffectiveFrom", effectiveFrom);
                            cmd.Parameters.AddWithValue("@EffectiveTo", effectiveTo);
                            cmd.Parameters.AddWithValue("@CurrencyID", Convert.ToInt32(CurrencyID));
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.HasRows)
                                {
                                    IsError = true; IsErrorinFile = true;
                                    Remarks += "ThiscontractalreadyexistsforgivenPartNumberandPartyCode<br/>";
                                }
                            }
                        }
                    }

                    if (IsError)
                    {
                        sb.Append("<tr><td>" + dt.Rows[i][0] + "</td><td>" + dt.Rows[i][1] + "</td><td>" + dt.Rows[i][2] + "</td><td>" + dt.Rows[i][3] + "</td><td>" + dt.Rows[i][4] + "</td><td>" + dt.Rows[i][5] + "</td><td>" + dt.Rows[i][6] + "</td><td>" + dt.Rows[i][7] + "</td><td>" + Remarks + "</td></tr>");
                    }
                    else
                    {
                        partsList.Add(rowObj);
                    }
                }
            }

            if (IsErrorinFile)
            {
                //sb.Append("</table>");
                //string str = sb.ToString();
                //HttpContext.Current.Session["PARTSRATECONTRACT_IMPORT_ERROR"] = str;
                //HttpContext.Current.Session["PARTSRATECONTRACT_LIST"] = null;

                sb.Append("</table>");
                EncapPartsRateContractClass result = new EncapPartsRateContractClass();
                result.HasError = IsErrorinFile;
                result.PartsList = partsList;
                result.ErrorString = sb.ToString();
            }
            else
            {
                //HttpContext.Current.Session["PARTSRATECONTRACT_LIST"] = partsList;
                //HttpContext.Current.Session["PARTSRATECONTRACT_IMPORT_ERROR"] = null;

                EncapPartsRateContractClass result = new EncapPartsRateContractClass();
                result.HasError = IsErrorinFile;
                result.PartsList = partsList;
                result.ErrorString = sb.ToString();
            }

            return new EncapPartsRateContractClass
            {
                PartsRatecontractList = partsList,
                Error = IsErrorinFile
            };
        }

        #endregion


        #region ::: IsNumber Uday Kumar J B 21-08-2024:::
        /// <summary>
        /// IsNumber
        /// </summary>
        /// 
        private static bool IsNumber(object number)
        {
            try
            {
                Convert.ToDecimal(number);
                return true;
            }
            catch (FormatException fx)
            {
                return false;
            }
        }
        #endregion


        #region ::: CheckIFPartExists Uday Kumar J B 21-08-2024:::
        /// <summary>
        /// CheckIFPartExists
        /// </summary>
        /// 

        public static bool CheckIFPartExists(string partPrefix, string partNumber, string connString, int Company_ID)
        {
            bool isvalid = false;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                Company_ID = Convert.ToInt32(Company_ID);
                int parentCompID = 0;

                using (var connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Retrieve parentCompID using stored procedure
                    using (var command = new SqlCommand("Up_Chk_Am_Erp_CheckIFPartExists", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@Company_ID", Company_ID);
                        SqlParameter existsParam = new SqlParameter("@Exists", SqlDbType.Bit);
                        existsParam.Direction = ParameterDirection.Output;
                        command.Parameters.Add(existsParam);

                        command.ExecuteNonQuery();

                        // Retrieve output parameter value
                        int existsValue = Convert.ToInt32(command.Parameters["@Exists"].Value);
                        isvalid = (existsValue == 1);
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, $"{ex.GetType().FullName}:{ex.Message}", ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return isvalid;
        }

        #endregion


        #region ::: SelectPart Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// SelectPart
        /// </summary>
        /// 
        public static PartRatecontract SelectPart(string partPrefix, string partNumber, string connString, int Company_ID)
        {
            PartRatecontract partUploadRow = null;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                Company_ID = Convert.ToInt32(Company_ID);

                using (var connection = new SqlConnection(connString))
                {
                    connection.Open();

                    using (var command = new SqlCommand("SelectPart", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@Company_ID", Company_ID);
                        command.Parameters.AddWithValue("@PartPrefix", partPrefix.ToLower());
                        command.Parameters.AddWithValue("@PartNumber", partNumber.ToLower());

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                partUploadRow = new PartRatecontract();
                                partUploadRow.partID = Convert.ToInt32(reader["partID"]);
                                partUploadRow.PartPrefix = reader["PartPrefix"].ToString();
                                partUploadRow.PartNumber = reader["PartNumber"].ToString();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, $"{ex.GetType().FullName}:{ex.Message}", ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return partUploadRow;
        }

        #endregion


        #region ::: SaveCustomerPartsRateContract Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// SaveCustomerPartsRateContract
        /// </summary>
        /// 
        public static void SaveCustomerPartsRateContract(List<PartRatecontract> partsList, string connString, int Company_ID, int User_ID, int Branch)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                Company_ID = Convert.ToInt32(Company_ID);
                int BranchID = Convert.ToInt32(Branch.ToString());

                using (var connection = new SqlConnection(connString))
                {
                    connection.Open();

                    foreach (var part in partsList)
                    {
                        if (part.partID != 0 && part.PartyID != 0 && part.Price != 0)
                        {
                            using (var command = new SqlCommand("Up_Save_Am_Erp_SaveCustomerPartsRateContract", connection))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@Company_ID", Company_ID);
                                command.Parameters.AddWithValue("@User_ID", User_ID);
                                command.Parameters.AddWithValue("@BranchID", BranchID);
                                command.Parameters.AddWithValue("@Part_ID", part.partID);
                                command.Parameters.AddWithValue("@Party_ID", part.PartyID);
                                command.Parameters.AddWithValue("@Effective_FromDate", part.effectiveFrom);
                                command.Parameters.AddWithValue("@Effective_ToDate", part.effectiveTo);
                                command.Parameters.AddWithValue("@Quantity", part.Quantity > 0 ? part.Quantity : (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Rate", part.Price);
                                command.Parameters.AddWithValue("@Currency_ID", part.currencyID);

                                command.ExecuteNonQuery();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, $"{ex.GetType().FullName}:{ex.Message}", ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
        }

        //public string CheckForError()
        //{
        //    string ErrorCoundVal = Session["ErrorCountval"].ToString();
        //    return (Session["ErrorCode"] + "$" + ErrorCoundVal).ToString();
        //}
        #endregion


        #region ::: Get Party Parts Rate Contract Detail Grid Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///To select menus of respective module
        /// </summary>
        /// <returns>...</returns>

        public static List<PartRatecontract> GetPartsRateContractGridData(string connString, SelectPartsRateContractGridList SelectPartsRateContractGridobj, string sidx, int rows, int page, string sord)
        {
            List<PartRatecontract> list = new List<PartRatecontract>();
            string Effective_FromDate = "";
            string Effective_ToDate = "";
            string Lbl_View = CommonFunctionalities.GetResourceString(SelectPartsRateContractGridobj.UserCulture.ToString(), "view").ToString();

            using (SqlConnection conn = new SqlConnection(connString))
            {
                SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_SelectPartsRateContractGrid", conn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@Party_ID", SelectPartsRateContractGridobj.Party_ID);
                cmd.Parameters.AddWithValue("@Effective_FromDate", Convert.ToDateTime(Effective_FromDate));
                cmd.Parameters.AddWithValue("@Effective_ToDate", Convert.ToDateTime(Effective_ToDate));
                cmd.Parameters.AddWithValue("@PageIndex", page);
                cmd.Parameters.AddWithValue("@PageSize", rows);
                cmd.Parameters.AddWithValue("@SortColumn", sidx);
                cmd.Parameters.AddWithValue("@SortOrder", sord);

                conn.Open();
                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var rateContract = new PartRatecontract
                        {
                            edit = "<a title=" + Lbl_View + " href='#' key='" + reader["PartyPartsRateContract_ID"] + "' class='editPartyRateContract font-icon-class'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                            partID = Convert.ToInt32(reader["Part_ID"]),
                            PartyID = Convert.ToInt32(reader["Party_ID"]),
                            Quantity = Convert.ToInt32(reader["Quantity"]),
                            Ratestr = Convert.ToDecimal(reader["Rate"]).ToString("0.00"),
                            Effective_FromDate = Convert.ToDateTime(reader["Effective_FromDate"]).ToString("dd-MMM-yyyy"),
                            Effective_ToDate = Convert.ToDateTime(reader["Effective_ToDate"]).ToString("dd-MMM-yyyy"),
                            currencyID = Convert.ToInt32(reader["Currency_ID"]),
                            UploadedBy = reader["UploadedBy"].ToString(),
                            UploadedDate = Convert.ToDateTime(reader["UploadedDate"]).ToString("dd-MMM-yyyy"),
                            PartyName = reader["PartyName"].ToString(),
                            Currency = reader["Currency"].ToString(),
                            Parts_PartsNumber = reader["Parts_PartsNumber"].ToString(),
                            PartsDescription = reader["PartsDescription"].ToString(),
                            PartPrefix = reader["PartPrefix"].ToString()
                        };
                        list.Add(rateContract);
                    }
                }
            }

            return list;
        }

        public static IActionResult SelectPartsRateContractGrid(string connString, SelectPartsRateContractGridList SelectPartsRateContractGridobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            dynamic Dummy = null;
            var jsonobj = Dummy;
            int total = 0;

            try
            {
                var list = GetPartsRateContractGridData(connString, SelectPartsRateContractGridobj, sidx, rows, page, sord);

                IQueryable<PartRatecontract> queryableList = list.AsQueryable().OrderByField(sidx, sord);

                if (_search)
                {
                    // Parse and decrypt the filters
                    Filters filtersObj = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(filters))).ToObject<Filters>();

                    // Ensure filtersObj is not null and its filter collection has more than 0 items
                    if (filtersObj != null && filtersObj.rules.Count > 0)
                    {
                        // Perform the FilterSearch operation
                        queryableList = queryableList.FilterSearch(filtersObj);
                    }
                }
                else if (advnce && !string.IsNullOrEmpty(advnceFilters))
                {
                    AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<AdvanceFilter>();
                    queryableList = queryableList.AdvanceSearch(advnfilter);
                    page = 1;
                }
                int count = queryableList.Count();
                total = rows > 0 ? (int)Math.Ceiling((double)count / rows) : 0;

                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(list.Count) / Convert.ToDouble(rows))) : 0;
                jsonobj = new
                {
                    TotalPages = total,
                    PageNo = page,
                    RecordCount = count,
                    rows = queryableList
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonobj);
        }


        #endregion


        #region ::: Get Party Parts Rate Contract Detail Grid Uday KumarJ B 21-08-2024:::
        /// <summary>
        ///To select menus of respective module
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult SelectPartyPartsRateContractGrid(string connString, SelectPartyPartsRateContractGridList SelectPartyPartsRateContractGridobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            dynamic jsonobj = null;
            int total = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var list = new List<object>();
            try
            {
                using (var connection = new SqlConnection(connString))
                {
                    connection.Open();

                    using (var command = new SqlCommand("SelectPartyPartsRateContractGrid", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@Party_ID", SelectPartyPartsRateContractGridobj.Party_ID);
                        command.Parameters.AddWithValue("@PageIndex", page);
                        command.Parameters.AddWithValue("@PageSize", rows);

                        using (var reader = command.ExecuteReader())
                        {


                            while (reader.Read())
                            {
                                string Lbl_View = null;
                                var item = new
                                {
                                    edit = $"<a title='{Lbl_View}' href='#' key='{reader["Party_ID"]}' class='editPartyRateContract font-icon-class'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                    UploadedDate = reader["UploadedDate"] != DBNull.Value ? Convert.ToDateTime(reader["UploadedDate"]).ToString("dd-MMM-yyyy") : "",
                                    Party_ID = reader["Party_ID"],
                                    Effective_FromDate = Convert.ToDateTime(reader["Effective_FromDate"]).ToString("dd-MMM-yyyy"),
                                    Effective_ToDate = Convert.ToDateTime(reader["Effective_ToDate"]).ToString("dd-MMM-yyyy"),
                                    UploadedBy = reader["UploadedBy"],
                                    UploadedByName = reader["UploadedByName"]
                                };
                                list.Add(item);
                            }
                            IQueryable<PartRatecontractClass> PartyContractListList = (IQueryable<PartRatecontractClass>)list.AsQueryable().OrderByField(sidx, sord);
                            if (_search)
                            {
                                // Parse and decrypt the filters
                                Filters filtersObj = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(filters))).ToObject<Filters>();

                                // Ensure filtersObj is not null and its filter collection has more than 0 items
                                if (filtersObj != null && filtersObj.rules.Count > 0)
                                {
                                    // Perform the FilterSearch operation
                                    PartyContractListList = PartyContractListList.FilterSearch(filtersObj);
                                }
                            }
                            else if (advnce && !string.IsNullOrEmpty(advnceFilters))
                            {
                                AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<AdvanceFilter>();
                                PartyContractListList = PartyContractListList.AdvanceSearch(advnfilter);
                                page = 1;
                            }
                            int count = PartyContractListList.Count();
                            total = rows > 0 ? (int)Math.Ceiling((double)count / rows) : 0;

                            if (count < (rows * page) && count != 0)
                            {
                                page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                            }


                            jsonobj = new
                            {
                                TotalPages = total,
                                PageNo = page,
                                RecordCount = count,
                                rows = PartyContractListList
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log your exception here
                LS.LogSheetExporter.LogToTextFile(ex.HResult, $"{ex.GetType().FullName}:{ex.Message}", ex.TargetSite.ToString(), ex.StackTrace);
                // Handle exception or redirect to error page as needed
            }

            return new JsonResult(jsonobj);
        }

        #endregion


        #region :::Export Uday Kumar J B 21-08-2024  Not Useded:::
        public static IActionResult Export(string connString, SelectPartsRateContractGridList SelectPartsRateContractGridobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {

            int userID = SelectPartsRateContractGridobj.User_ID;
            int companyID = Convert.ToInt32(SelectPartsRateContractGridobj.Company_ID);
            int branchID = Convert.ToInt32(SelectPartsRateContractGridobj.Branch);
            DataTable Dt = new DataTable();
            DataTable DtData = new DataTable();
            int Count = 0;
            int type = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));

            try
            {
                List<PartRatecontract> wf = GetPartsRateContractGridDataExport(connString, SelectPartsRateContractGridobj, sidx, rows, page, sord);
                Count = wf.Count();
                if (Count > 0)
                {
                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(SelectPartsRateContractGridobj.UserCulture.ToString(), "Customer").ToString());
                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(SelectPartsRateContractGridobj.UserCulture.ToString(), "Partprefix").ToString());
                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(SelectPartsRateContractGridobj.UserCulture.ToString(), "partnumber").ToString());
                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(SelectPartsRateContractGridobj.UserCulture.ToString(), "partdescription").ToString());
                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(SelectPartsRateContractGridobj.UserCulture.ToString(), "rate").ToString());
                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(SelectPartsRateContractGridobj.UserCulture.ToString(), "Quantity").ToString());
                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(SelectPartsRateContractGridobj.UserCulture.ToString(), "effectivefrom").ToString());
                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(SelectPartsRateContractGridobj.UserCulture.ToString(), "EffectiveTo").ToString());
                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(SelectPartsRateContractGridobj.UserCulture.ToString(), "Currency").ToString());
                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(SelectPartsRateContractGridobj.UserCulture.ToString(), "UploadedBy").ToString());
                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(SelectPartsRateContractGridobj.UserCulture.ToString(), "UploadedDate").ToString());

                    DataTable DtAlignment = new DataTable();
                    DtAlignment.Columns.Add("Customer");
                    DtAlignment.Columns.Add("Partprefix");
                    DtAlignment.Columns.Add("partnumber");
                    DtAlignment.Columns.Add("partdescription");
                    DtAlignment.Columns.Add("rate");
                    DtAlignment.Columns.Add("Quantity");
                    DtAlignment.Columns.Add("effectivefrom");
                    DtAlignment.Columns.Add("EffectiveTo");
                    DtAlignment.Columns.Add("Currency");
                    DtAlignment.Columns.Add("UploadedBy");
                    DtAlignment.Columns.Add("UploadedDate");

                    DtAlignment.Rows.Add(0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0);

                    for (int i = 0; i < Count; i++)
                    {
                        DtData.Rows.Add(
                            wf[i].PartyName,
                            wf[i].PartPrefix,
                            wf[i].Parts_PartsNumber,
                            wf[i].PartsDescription,
                            Convert.ToDecimal(wf[i].Ratestr).ToString("0.00"),
                            wf[i].Quantity,
                            (Convert.ToDateTime(wf[i].Effective_FromDate).ToString() == "1/1/1900 12:00:00 AM" || wf[i].Effective_FromDate == null) ? "" : Convert.ToDateTime(wf[i].Effective_FromDate).ToString("dd-MMM-yyyy"),
                            (Convert.ToDateTime(wf[i].Effective_ToDate).ToString() == "1/1/1900 12:00:00 AM" || wf[i].Effective_ToDate == null) ? "" : Convert.ToDateTime(wf[i].Effective_ToDate).ToString("dd-MMM-yyyy"),
                            wf[i].Currency,
                            wf[i].UploadedBy,
                            wf[i].UploadedDate
                        );
                    }

                    // Uncomment and use the actual implementation of DocumentExport.Export as needed
                    // DocumentExport.Export(SelectPartsRateContractGridobj.exprtType, DtData, DtAlignment, GetGlobalResourceObject(SelectPartsRateContractGridobj.UserCulture.ToString(), "CustomerRateContract").ToString(), GetGlobalResourceObject(SelectPartsRateContractGridobj.UserCulture.ToString(), "CustomerRateContract").ToString());

                    // Uncomment and use the actual implementation of gbl.InsertGPSDetails as needed
                    // gbl.InsertGPSDetails(Convert.ToInt32(SelectPartsRateContractGridobj.Company_ID.ToString()), Convert.ToInt32(SelectPartsRateContractGridobj.Branch), User.User_ID, Common.GetObjectID("PartsRateContractImport"), 0, 0, 0, GetGlobalResourceObject(SelectPartsRateContractGridobj.UserCulture.ToString(), "UploadPartsRateContract").ToString(), false, Convert.ToInt32(SelectPartsRateContractGridobj.MenuID), Convert.ToDateTime(SelectPartsRateContractGridobj.LoggedINDateTime));
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(null);
        }

        public static List<PartRatecontract> GetPartsRateContractGridDataExport(string connString, SelectPartsRateContractGridList SelectPartsRateContractGridobj, string sidx, int rows, int page, string sord)
        {
            List<PartRatecontract> list = new List<PartRatecontract>();
            string Effective_FromDate = "";
            string Effective_ToDate = "";
            string Lbl_View = CommonFunctionalities.GetResourceString(SelectPartsRateContractGridobj.UserCulture.ToString(), "view").ToString();

            using (SqlConnection conn = new SqlConnection(connString))
            {
                SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_SelectPartsRateContractGrid", conn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@Party_ID", SelectPartsRateContractGridobj.Party_ID);
                cmd.Parameters.AddWithValue("@Effective_FromDate", Convert.ToDateTime(Effective_FromDate));
                cmd.Parameters.AddWithValue("@Effective_ToDate", Convert.ToDateTime(Effective_ToDate));
                cmd.Parameters.AddWithValue("@PageIndex", page);
                cmd.Parameters.AddWithValue("@PageSize", rows);
                cmd.Parameters.AddWithValue("@SortColumn", sidx);
                cmd.Parameters.AddWithValue("@SortOrder", sord);

                conn.Open();
                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var rateContract = new PartRatecontract
                        {
                            partID = Convert.ToInt32(reader["Part_ID"]),
                            PartyID = Convert.ToInt32(reader["Party_ID"]),
                            Quantity = Convert.ToInt32(reader["Quantity"]),
                            Ratestr = Convert.ToDecimal(reader["Rate"]).ToString("0.00"),
                            Effective_FromDate = Convert.ToDateTime(reader["Effective_FromDate"]).ToString("dd-MMM-yyyy"),
                            Effective_ToDate = Convert.ToDateTime(reader["Effective_ToDate"]).ToString("dd-MMM-yyyy"),
                            currencyID = Convert.ToInt32(reader["Currency_ID"]),
                            UploadedBy = reader["UploadedBy"].ToString(),
                            UploadedDate = Convert.ToDateTime(reader["UploadedDate"]).ToString("dd-MMM-yyyy"),
                            PartyName = reader["PartyName"].ToString(),
                            Currency = reader["Currency"].ToString(),
                            Parts_PartsNumber = reader["Parts_PartsNumber"].ToString(),
                            PartsDescription = reader["PartsDescription"].ToString(),
                            PartPrefix = reader["PartPrefix"].ToString()
                        };
                        list.Add(rateContract);
                    }
                }
            }

            return list;
        }


        #endregion


        #region :::RateContractDetailsDatatable Uday Kumar J B 21-08-2024:::

        public static List<PartRatecontract> RateContractDetailsDatatable(string connString, int UploadedBy, string UploadedDate)
        {
            List<PartRatecontract> rateContracts = new List<PartRatecontract>();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetRateContractDetails", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@UploadedBy", UploadedBy);
                    cmd.Parameters.AddWithValue("@UploadedDate", Convert.ToDateTime(UploadedDate).ToString("yyyy-MM-dd"));

                    conn.Open();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var rateContract = new PartRatecontract
                            {
                                PartyName = reader.HasColumn("PartyName") && !reader.IsDBNull(reader.GetOrdinal("PartyName")) ? reader["PartyName"].ToString() : null,
                                PartPrefix = reader.HasColumn("PartPrefix") && !reader.IsDBNull(reader.GetOrdinal("PartPrefix")) ? reader["PartPrefix"].ToString() : null,
                                Parts_PartsNumber = reader.HasColumn("Parts_PartsNumber") && !reader.IsDBNull(reader.GetOrdinal("Parts_PartsNumber")) ? reader["Parts_PartsNumber"].ToString() : null,
                                PartsDescription = reader.HasColumn("PartsDescription") && !reader.IsDBNull(reader.GetOrdinal("PartsDescription")) ? reader["PartsDescription"].ToString() : null,
                                Rate = reader.HasColumn("Rate") && !reader.IsDBNull(reader.GetOrdinal("Rate")) ? (decimal?)reader["Rate"] : null,
                                Quantity = reader.HasColumn("Quantity") && !reader.IsDBNull(reader.GetOrdinal("Quantity")) ? (decimal?)reader["Quantity"] : null, // Changed to decimal
                                effectiveFrom = reader.HasColumn("effectiveFrom") && !reader.IsDBNull(reader.GetOrdinal("effectiveFrom")) ? (DateTime?)reader["effectiveFrom"] : null, // Nullable DateTime
                                effectiveTo = reader.HasColumn("effectiveTo") && !reader.IsDBNull(reader.GetOrdinal("effectiveTo")) ? (DateTime?)reader["effectiveTo"] : null, // Nullable DateTime
                                Currency = reader.HasColumn("Currency") && !reader.IsDBNull(reader.GetOrdinal("Currency")) ? reader["Currency"].ToString() : null,
                                UploadedBy = reader.HasColumn("UploadedBy") && !reader.IsDBNull(reader.GetOrdinal("UploadedBy")) ? reader["UploadedBy"].ToString() : null,
                                UploadedDateStr = reader.HasColumn("UploadedDateStr") && !reader.IsDBNull(reader.GetOrdinal("UploadedDateStr")) ? reader["UploadedDateStr"].ToString() : null
                            };
                            rateContracts.Add(rateContract);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return rateContracts;
        }

        #endregion


        #region :::RateContractExport Uday Kumar J B 21-08-2024:::
        public static async Task<object> RateContractExport(RateContractExportList RateContractExportobj, string connString)
        {
            //GNM_User User = RateContractExportobj.UserDetails.FirstOrDefault();
            int userID = RateContractExportobj.User_ID;
            int companyID = Convert.ToInt32(RateContractExportobj.Company_ID);
            int branchID = Convert.ToInt32(RateContractExportobj.Branch);
            DataTable Dt = new DataTable();
            DataTable DtData = new DataTable();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int Count = 0;
                List<PartRatecontract> wf = RateContractDetailsDatatable(connString, RateContractExportobj.UploadedBy, RateContractExportobj.UploadedDate);
                Count = wf.Count();

                if (Count > 0)
                {

                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(RateContractExportobj.UserCulture.ToString(), "Customer").ToString());
                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(RateContractExportobj.UserCulture.ToString(), "Partprefix").ToString());
                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(RateContractExportobj.UserCulture.ToString(), "partnumber").ToString());
                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(RateContractExportobj.UserCulture.ToString(), "partdescription").ToString());
                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(RateContractExportobj.UserCulture.ToString(), "rate").ToString());
                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(RateContractExportobj.UserCulture.ToString(), "Quantity").ToString());
                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(RateContractExportobj.UserCulture.ToString(), "effectivefrom").ToString());
                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(RateContractExportobj.UserCulture.ToString(), "EffectiveTo").ToString());
                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(RateContractExportobj.UserCulture.ToString(), "Currency").ToString());
                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(RateContractExportobj.UserCulture.ToString(), "UploadedBy").ToString());
                    DtData.Columns.Add(CommonFunctionalities.GetResourceString(RateContractExportobj.UserCulture.ToString(), "UploadedDate").ToString());
                    DataTable DtAlignment = new DataTable();
                    DtAlignment.Columns.Add("Customer");
                    DtAlignment.Columns.Add("Partprefix");
                    DtAlignment.Columns.Add("partnumber");
                    DtAlignment.Columns.Add("partdescription");
                    DtAlignment.Columns.Add("rate");
                    DtAlignment.Columns.Add("Quantity");
                    DtAlignment.Columns.Add("effectivefrom");
                    DtAlignment.Columns.Add("EffectiveTo");
                    DtAlignment.Columns.Add("Currency");
                    DtAlignment.Columns.Add("UploadedBy");
                    DtAlignment.Columns.Add("UploadedDate");

                    DtAlignment.Rows.Add(0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0);
                    for (int i = 0; i < Count; i++)
                    {
                        DtData.Rows.Add(
                        wf[i].PartyName,
                        wf[i].PartPrefix,
                        wf[i].Parts_PartsNumber,
                        wf[i].PartsDescription,
                        Convert.ToDecimal(wf[i].Rate).ToString("0.00"),
                        wf[i].Quantity,

                        (Convert.ToDateTime(wf[i].effectiveFrom).ToString() == "1/1/1900 12:00:00 AM" || wf[i].effectiveFrom == null) ? "" : Convert.ToDateTime(wf[i].effectiveFrom).ToString("dd-MMM-yyyy"),
                        (Convert.ToDateTime(wf[i].effectiveTo).ToString() == "1/1/1900 12:00:00 AM" || wf[i].effectiveTo == null) ? "" : Convert.ToDateTime(wf[i].effectiveTo).ToString("dd-MMM-yyyy"),
                        wf[i].Currency,
                        wf[i].UploadedBy,

                        (Convert.ToDateTime(wf[i].UploadedDateStr).ToString() == "1/1/1900 12:00:00 AM" || wf[i].UploadedDateStr == null) ? "" : Convert.ToDateTime(wf[i].UploadedDateStr).ToString("dd-MMM-yyyy")
                        );
                    }
                    ExportList reportExportList = new ExportList
                    {
                        Company_ID = RateContractExportobj.Company_ID, // Assuming this is available in ExportObj
                        Branch = RateContractExportobj.Branch,
                        dt1 = DtAlignment,


                        dt = DtData,

                        FileName = "CustomerRateContract", // Set a default or dynamic filename
                        Header = CommonFunctionalities.GetResourceString(RateContractExportobj.UserCulture.ToString(), "CustomerRateContract").ToString(), // Set a default or dynamic header
                        exprtType = 1, // Assuming export type as 1 for Excel, adjust as needed
                        UserCulture = RateContractExportobj.UserCulture
                    };
                    var result = await DocumentExport.Export(reportExportList, connString, LogException);
                    return result.Value;
                    // DocumentExport.Export(1, DtData, DtAlignment, GetGlobalResourceObject(RateContractExportobj.UserCulture.ToString(), "CustomerRateContract").ToString(), GetGlobalResourceObject(RateContractExportobj.UserCulture.ToString(), "CustomerRateContract").ToString());
                    // gbl.InsertGPSDetails(Convert.ToInt32(RateContractExportobj.Company_ID.ToString()), Convert.ToInt32(RateContractExportobj.Branch), User.User_ID, Common.GetObjectID("PartsRateContractImport"), 0, 0, 0, GetGlobalResourceObject(RateContractExportobj.UserCulture.ToString(), "UploadPartsRateContract").ToString(), false, Convert.ToInt32(RateContractExportobj.MenuID), Convert.ToDateTime(RateContractExportobj.LoggedINDateTime));
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return null;
        }
        #endregion


        #region ::: Landing Grid Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// Get Landing Grid  data
        /// </summary>
        /// 
        public static IActionResult GetLandingData(string connString, GetLandingDataList GetLandingDataobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var JsonData = default(dynamic);
            try
            {
                // GNM_User User = GetLandingDataobj.UserDetails.FirstOrDefault();
                List<PartRatecontractClassGrid> result = new List<PartRatecontractClassGrid>();
                string Lbl_View = CommonFunctionalities.GetGlobalResourceObject(GetLandingDataobj.UserCulture.ToString(), "view").ToString();

                int userLanguageID = Convert.ToInt32(GetLandingDataobj.UserLanguageID);
                int companyId = Convert.ToInt32(GetLandingDataobj.Company_ID);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetLandingData", conn);
                    cmd.CommandType = CommandType.StoredProcedure;

                    conn.Open();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var grid = new PartRatecontractClassGrid
                            {
                                UploadedBy = reader["UploadedBy"] != DBNull.Value ? (int)reader["UploadedBy"] : 0,
                                UploadCount = reader["UploadCount"] != DBNull.Value ? (int)reader["UploadCount"] : 0,
                                UploadedDate = reader["UploadedDate"] != DBNull.Value ? (DateTime?)reader["UploadedDate"] : null,
                                UploadedByName = reader["UploadedByName"] != DBNull.Value ? reader["UploadedByName"].ToString() : string.Empty
                            };
                            result.Add(grid);
                        }
                    }
                }
                IQueryable<PartRatecontractClassGrid> queryableResult = result.AsQueryable();

                if (_search)
                {
                    // Parse and decrypt the filters
                    Filters filtersObj = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(filters))).ToObject<Filters>();

                    // Ensure filtersObj is not null and its filter collection has more than 0 items
                    if (filtersObj != null && filtersObj.rules.Count > 0)
                    {
                        // Perform the FilterSearch operation
                        queryableResult = queryableResult.FilterSearch(filtersObj);
                    }
                }
                else if (advnce && !string.IsNullOrEmpty(advnceFilters))
                {
                    AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<AdvanceFilter>();

                    if (advnfilter != null && advnfilter.rules.Count > 0)
                    {
                        queryableResult = queryableResult.AdvanceSearch(advnfilter);
                        page = 1;
                    }
                }


                //result = result.OrderByField(sidx, sord).ToList();
                int count = queryableResult.Count();
                int total = rows > 0 ? (int)Math.Ceiling((double)count / rows) : 0;

                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                var paginatedResult = result.Skip((page - 1) * rows).Take(rows).ToList();

                JsonData = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = (from a in paginatedResult
                            select new
                            {
                                edit = $"<a title={Lbl_View} href='#' key='{a.UploadedBy}' class='ExportPartyRateContract font-icon-class'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                UploadedByName = a.UploadedByName,
                                UploadCount = a.UploadCount,
                                UploadedDate = a.UploadedDate?.ToString("dd-MMM-yyyy") ?? ""
                            }).ToList()
                };

                // gbl.InsertGPSDetails(companyId, Convert.ToInt32(GetLandingDataobj.Branch), User.User_ID, Common.GetObjectID("TAMSUSPayableSummaryReport"), 0, 0, 0, "Generated TAMS US Payable Summary Report", false, Convert.ToInt32(GetLandingDataobj.MenuID), Convert.ToDateTime(GetLandingDataobj.LoggedINDateTime));
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(JsonData);
        }

        #endregion


        #region ::: CheckAccess Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// CheckAccess
        /// </summary>
        ///
        public static IActionResult CheckAccess(string connString, CheckAccessList CheckAccessobj)
        {
            int UserID = Convert.ToInt32(CheckAccessobj.User_ID);
            List<RoleaccessClass> PartyRateContractExport = new List<RoleaccessClass>();
            var json = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    SqlCommand cmd = new SqlCommand("Up_Chk_Am_Erp_CheckAccess", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@UserID", UserID);

                    conn.Open();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var roleAccess = new RoleaccessClass
                            {
                                Object_ID = reader["Object_ID"] != DBNull.Value ? (int)reader["Object_ID"] : 0,
                                Object_Name = reader["Object_Name"] != DBNull.Value ? reader["Object_Name"].ToString() : string.Empty,
                                Object_Description = reader["Object_Description"] != DBNull.Value ? reader["Object_Description"].ToString() : string.Empty,
                                RoleObject_Export = reader["RoleObject_Export"] != DBNull.Value ? (int)reader["RoleObject_Export"] : 0
                            };
                            PartyRateContractExport.Add(roleAccess);
                        }
                    }
                }

                json = new
                {
                    PartyRateContractExport,
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(json);
        }

        #endregion


        #region ::: CorePartsRatecontractImport list and obj classes Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// CorePartsRatecontractImport
        /// </summary>
        ///
        public class CheckAccessList
        {
            public int User_ID { get; set; }
        }
        public class GetLandingDataList
        {
            public List<GNM_User> UserDetails { get; set; }
            public string UserCulture { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int Branch { get; set; }
            public int UserLanguageID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }
        public class RateContractExportList
        {
            public List<GNM_User> UserDetails { get; set; }
            public int Company_ID { get; set; }
            public int exprtType { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public int UploadedBy { get; set; }
            public string UploadedDate { get; set; }
            public string UserCulture { get; set; }
        }
        public class SelectPartyPartsRateContractGridList
        {
            public int Party_ID { get; set; }

        }

        public class SelectPartsRateContractGridList
        {
            public int Party_ID { get; set; }
            public string Effective_FromDate { get; set; }
            public string Effective_ToDate { get; set; }
            public string UserCulture { get; set; }
            public List<GNM_User> UserDetails { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public int exprtType { get; set; }
        }

        public class UploadFileCorePartsRatecontractImportList
        {
            public string PartsPrice { get; set; }
            public List<GNM_User> UserDetails { get; set; }
            public DateTime EffectiveFrom { get; set; }
            public DateTime EffectiveTo { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int userID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public int Currency_ID { get; set; }
        }

        public class ImportPartsRatecontractTemplateList
        {
            public List<GNM_User> UserDetails { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }
        #endregion


        #region ::: CorePartsRatecontractImport classes Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// CorePartsRatecontractImport
        /// </summary>
        ///

        public class RoleaccessClass
        {

            public int RoleObject_ID { get; set; }
            public int Role_ID { get; set; }
            public int Object_ID { get; set; }
            public bool RoleObject_Create { get; set; }
            public bool RoleObject_Read { get; set; }
            public bool RoleObject_Update { get; set; }
            public bool RoleObject_Delete { get; set; }
            public bool RoleObject_Print { get; set; }
            public int RoleObject_Export { get; set; }
            public bool RoleObject_Import { get; set; }
            public string Object_Name { get; set; }
            public string Read_Action { get; set; }
            public string Create_Action { get; set; }
            public string Update_Action { get; set; }
            public string Delete_Action { get; set; }
            public string Export_Action { get; set; }
            public string Print_Action { get; set; }
            public bool Object_IsActive { get; set; }
            public string Object_Description { get; set; }
            public string Import_Action { get; set; }
            public string Object_Type { get; set; }
        }
        public class PartRatecontract
        {
            public int partID { get; set; }
            public int PartyID { get; set; }
            public int? currencyID { get; set; }
            public decimal? Quantity { get; set; }
            public decimal Price { get; set; }
            public DateTime? effectiveFrom { get; set; }
            public DateTime? effectiveTo { get; set; }
            public string PartPrefix { get; set; }
            public string PartNumber { get; set; }
            public int companyID { get; set; }
            public bool HasError { get; set; }
            public decimal ListPrice { get; set; }
            public string edit { get; set; }
            public string PartyName { get; set; }
            public string UploadedBy { get; set; }
            public string UploadedDate { get; set; }
            public string Currency { get; set; }
            public string Parts_PartsNumber { get; set; }
            public string PartsDescription { get; set; }
            public string Effective_FromDate { get; set; }
            public string Effective_ToDate { get; set; }
            public decimal? Rate { get; set; }
            public string Ratestr { get; set; }
            public string UploadedDateStr { get; set; }
        }
        public class PartRatecontractClass
        {
            public DateTime? UploadedDate { get; set; }
            public int Party_ID { get; set; }
            public decimal Quantity { get; set; }
            public decimal Rate { get; set; }
            public DateTime Effective_FromDate { get; set; }
            public DateTime Effective_ToDate { get; set; }
            public string PartyName { get; set; }
            public int? UploadedBy { get; set; }
            public int UploadCount { get; set; }
            public string UploadedByName { get; set; }

        }
        public class PartRatecontractClassGrid
        {
            public DateTime? UploadedDate { get; set; }
            public int? UploadedBy { get; set; }
            public int? UploadCount { get; set; }
            public string UploadedByName { get; set; }


        }
        public class EncapPartsRateContractClass
        {
            public List<PartRatecontract> PartsList { get; set; }
            public bool HasError { get; set; }
            public string ErrorString { get; set; }
            public bool Error { get; internal set; }
            public List<PartRatecontract> PartsRatecontractList { get; internal set; }
        }
        #endregion


    }

    #region ::: HasColumn  Uday Kumar J B 21-08-2024 :::
    /// <summary>
    /// HasColumn
    /// </summary>
    ///
    public static class SqlDataReaderExtensions
    {
        public static bool HasColumn(this SqlDataReader reader, string columnName)
        {
            for (int i = 0; i < reader.FieldCount; i++)
            {
                if (reader.GetName(i).Equals(columnName, StringComparison.OrdinalIgnoreCase))
                    return true;
            }
            return false;
        }
    }
    #endregion

}
