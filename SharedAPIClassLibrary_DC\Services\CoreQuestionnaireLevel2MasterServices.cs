﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace SharedAPIClassLibrary_AMERP
{
    public class CoreQuestionnaireLevel2MasterServices
    {
        static string AppPath = string.Empty;
        private static JTokenReader jTR;
        #region ::: SelectQuestionnaireLevel1 /Mithun:::
        /// <summary>
        /// To get QustionnaireLevel1 records for a IssueArea
        /// </summary>


        public static IActionResult SelectQuestionLevel1(SelectQuestionLevel1List SelectQuestionLevel1Obj, string constring, int LogException)
        {
            var Masterdata = new object();
            try
            {
                int CompanyID = Convert.ToInt32(SelectQuestionLevel1Obj.Company_ID);
                int GeneralLanguageID = Convert.ToInt32(SelectQuestionLevel1Obj.GeneralLanguageID);

                // Establish SQL connection
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    using (SqlCommand cmd = new SqlCommand("UP_Select_AM_ERP_QuestionnaireLevel2", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@IssueAreaID", SelectQuestionLevel1Obj.IssueAreaID);
                        cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                        cmd.Parameters.AddWithValue("@LanguageID", SelectQuestionLevel1Obj.LanguageID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", GeneralLanguageID);

                        conn.Open();

                        // Execute the stored procedure and read the results
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            var resultList = new List<object>();
                            while (reader.Read())
                            {
                                resultList.Add(new
                                {
                                    ID = reader.GetInt32(reader.GetOrdinal("ID")),
                                    Name = reader.GetString(reader.GetOrdinal("Name"))
                                });
                            }

                            Masterdata = new
                            {
                                Data = resultList
                            };
                        }
                    }
                }

                // Return data as JSON
                //return Json(Masterdata, JsonRequestBehavior.AllowGet);
                return new JsonResult(Masterdata);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }
        #endregion

        #region ::: CheckQuestionnaireLevel2Locale /Mithun:::
        /// <summary>
        /// CheckQuestionnaireLevel2Locale
        /// </summary> 
        public static IActionResult CheckQuestionnaireLevel2Locale(CheckQuestionnaireLevel2LocaleList CheckQuestionnaireLevel2LocaleObj, string constring, int LogException)
        {
            int Count = 0;
            try
            {
                string Q2 = Common.DecryptString(CheckQuestionnaireLevel2LocaleObj.QuestionLevel2);
                int Language_ID = Convert.ToInt32(CheckQuestionnaireLevel2LocaleObj.UserLanguageID);
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    string query = "UP_Check_AM_ERP_QuestionnaireLevel2Locale";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@QuestionLevel2LocaleID", CheckQuestionnaireLevel2LocaleObj.QuestionLevel2LocaleID);
                            command.Parameters.AddWithValue("@Q2", Q2);
                            command.Parameters.AddWithValue("@QuestionLevelID", CheckQuestionnaireLevel2LocaleObj.QuestionLevelID);
                            command.Parameters.AddWithValue("@Language_ID", Language_ID);
                            command.Parameters.AddWithValue("@IssueAreaID", CheckQuestionnaireLevel2LocaleObj.IssueAreaID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            Count = (int)command.ExecuteScalar();
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                if (Count > 0)
                {
                    Count = 1;
                }

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Count;
            return new JsonResult(Count);
        }
        #endregion

        #region ::: SelectReferenceMaster /Mithun:::
        /// <summary>
        /// To get Refrence Master records for a Master
        /// </summary> 
        public static IActionResult SelectReferenceMaster(SelectReferenceMasterCoreQuestionnaireLevel2List SelectReferenceMasterObj, string constring, int LogException)
        {
            var Masterdata = default(dynamic);
            List<QuestionnaireLevelData> masterDataList = new List<QuestionnaireLevelData>();
            int isDefault = 0;
            try
            {
                int CompanyID = Convert.ToInt32(SelectReferenceMasterObj.Company_ID);
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    string query = "UP_Select_AM_ERP_QLevel2SelectReferenceMaster";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@ReferenceMasterName", SelectReferenceMasterObj.ReferenceMasterName);
                            command.Parameters.AddWithValue("@CompanyID", CompanyID);
                            command.Parameters.AddWithValue("@GeneralLanguageID", Convert.ToInt32(SelectReferenceMasterObj.GeneralLanguageID));
                            command.Parameters.AddWithValue("@LanguageID", SelectReferenceMasterObj.LanguageID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var Data = new QuestionnaireLevelData
                                    {
                                        ID = reader.GetInt32(reader.GetOrdinal("ID")),
                                        Name = reader.IsDBNull(reader.GetOrdinal("Name")) ? null : reader.GetString(reader.GetOrdinal("Name"))
                                    };
                                    masterDataList.Add(Data);
                                }

                                if (reader.NextResult() && reader.Read())
                                {
                                    isDefault = reader.GetInt32(reader.GetOrdinal("Isdefault"));
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                Masterdata = new
                {
                    ReferenceMasterData = masterDataList,
                    Isdefault = isDefault
                };

                //return Json(Masterdata, JsonRequestBehavior.AllowGet);
                return new JsonResult(Masterdata);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }
        #endregion

        #region ::: Load QuestionnaireLevel2Grid /Mithun:::
        /// <summary>
        /// Loading QuestionnaireLevel2 Grid
        /// </summary> 
        public static IActionResult Select(SelectCoreQuestionnaireLevel2List SelectObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {

            try
            {
                int Count = 0;
                int Total = 0;
                int CompanyID = Convert.ToInt32(SelectObj.Company_ID);

                IQueryable<QuestionnaireLevel2> IQQuestionnaireLevel2 = null;

                var results = new List<QuestionnaireLevel2>();
                string YesE = CommonFunctionalities.GetResourceString(SelectObj.GeneralCulture.ToString(), "yes").ToString();
                string NoE = CommonFunctionalities.GetResourceString(SelectObj.GeneralCulture.ToString(), "no").ToString();
                string YesL = CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "yes").ToString();
                string NoL = CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "no").ToString();


                var jsonData = default(dynamic);
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    string query = "UP_Select_AM_ERP_SelectQuestionnaireLevel2";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.Add(new SqlParameter("@QuestionnaireLevel1ID", SelectObj.QuestionnaireLevel1ID));
                            command.Parameters.Add(new SqlParameter("@CompanyID", CompanyID));
                            command.Parameters.Add(new SqlParameter("@GeneralLanguageID", Convert.ToInt32(SelectObj.GeneralLanguageID)));
                            command.Parameters.Add(new SqlParameter("@LanguageID", SelectObj.LanguageID));
                            command.Parameters.Add(new SqlParameter("@YesE", YesE));
                            command.Parameters.Add(new SqlParameter("@NoE", NoE));
                            command.Parameters.Add(new SqlParameter("@YesL", YesL));
                            command.Parameters.Add(new SqlParameter("@NoL", NoL));

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    results.Add(new QuestionnaireLevel2
                                    {
                                        QuestionaryLevel2_ID = reader.IsDBNull(0) ? 0 : reader.GetInt32(0),
                                        QuestionLevel2 = reader.IsDBNull(1) ? string.Empty : reader.GetString(1),
                                        QuestionLevel2_IsActive = reader.IsDBNull(2) ? string.Empty : reader.GetString(2)
                                    });
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }



                IQQuestionnaireLevel2 = results.AsQueryable<QuestionnaireLevel2>();

                if (_search)
                {
                    string decodedValue = Uri.UnescapeDataString(filters);
                    Filters filtersObj = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<Filters>();
                    if (filtersObj.rules.Count() > 0)
                        IQQuestionnaireLevel2 = IQQuestionnaireLevel2.FilterSearch<QuestionnaireLevel2>(filtersObj);
                }
                if (advnce)
                {
                    string decodedValue = Uri.UnescapeDataString(Query);
                    AdvanceFilter advnfilter = JObject.Parse(decodedValue).ToObject<AdvanceFilter>();
                    IQQuestionnaireLevel2 = IQQuestionnaireLevel2.AdvanceSearch<QuestionnaireLevel2>(advnfilter);
                }

                IQQuestionnaireLevel2 = IQQuestionnaireLevel2.OrderByField<QuestionnaireLevel2>(sidx, sord);



                Count = IQQuestionnaireLevel2.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                //Added by Ravi on 05-Jan-2014 for HelpDesk QA Corrections Begin
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }
                //---End
                jsonData = new
                {
                    total = Total,
                    page = page,
                    data = (from a in IQQuestionnaireLevel2.AsEnumerable()
                            select new
                            {
                                ID = a.QuestionaryLevel2_ID,
                                //edit = "<img id='" + a.QuestionaryLevel2_ID + "' src='" + AppPath + "/Content/edit.gif' key='" + a.QuestionaryLevel2_ID + "' class='QuestionnaireLevel2Edit' editmode='false'/>",
                                edit = "<a title='Edit' href='#' id='" + a.QuestionaryLevel2_ID + "'  key='" + a.QuestionaryLevel2_ID + "' class='QuestionnaireLevel2Edit' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input type='checkbox' key='" + a.QuestionaryLevel2_ID + "' defaultchecked=''  id='chk" + a.QuestionaryLevel2_ID + "' class='QuestionnaireLevel2Delete'/>",
                                QuestionLevel2 = (a.QuestionLevel2),
                                QuestionLevel2_IsActive = a.QuestionLevel2_IsActive,
                                Locale = "<img key='" + a.QuestionaryLevel2_ID + "' src='" + AppPath + "/Content/local.png' class='QuestionnaireLevel2Locale' alt='Localize' width='20' height='20'  title='Localize'/>",
                                View = "<img id='" + a.QuestionaryLevel2_ID + "' src='" + AppPath + "/Content/plus.gif' key='" + a.QuestionaryLevel2_ID + "' class='ViewQuestionnaireLevel2Locale'/>",
                            }).ToList().Paginate(page, rows),
                    records = Count
                };

                //return Json(jsonData, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonData);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }
        #endregion

        #region ::: SelectParticularQuestionnaireLevel2 /Mithun:::
        /// <summary>
        /// SelectParticularQuestionnaireLevel2
        /// </summary>
        public static IActionResult SelectParticularQuestionnaireLevel2(SelectParticularQuestionnaireLevel2List SelectParticularQuestionnaireLevel2Obj, string constring, int LogException)
        {
            var x = default(dynamic);
            try
            {
                int Language_ID = Convert.ToInt32(SelectParticularQuestionnaireLevel2Obj.UserLanguageID);
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    string query = "UP_Select_AM_ERP_SelectParticularQuestionnaireLevel2";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@QuestionnaireLevel2ID", SelectParticularQuestionnaireLevel2Obj.QuestionnaireLevel2ID);
                            command.Parameters.AddWithValue("@Language_ID", Language_ID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    x = new
                                    {
                                        QuestionaryLevel2_ID = reader.GetInt32(reader.GetOrdinal("QuestionaryLevel2_ID")),
                                        QuestionLevel2 = reader.IsDBNull(reader.GetOrdinal("QuestionLevel2")) ? string.Empty : reader.GetString(reader.GetOrdinal("QuestionLevel2")),
                                        QuestionLevel2_IsActive = reader.GetBoolean(reader.GetOrdinal("QuestionLevel2_IsActive")),
                                        QuestionaryLevel1_ID = reader.GetInt32(reader.GetOrdinal("QuestionaryLevel1_ID")),
                                        QuestionnaireLevel2Locale_ID = reader.IsDBNull(reader.GetOrdinal("QuestionnaireLevel2Locale_ID")) ? string.Empty : reader.GetInt32(reader.GetOrdinal("QuestionnaireLevel2Locale_ID")).ToString(),
                                        QuestionLevel2LocaleName = reader.IsDBNull(reader.GetOrdinal("QuestionLevel2LocaleName")) ? string.Empty : reader.GetString(reader.GetOrdinal("QuestionLevel2LocaleName"))
                                    };
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                //return Json(x, JsonRequestBehavior.AllowGet);
                return new JsonResult(x);
            }

            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }
        #endregion

        #region ::: Save QuestionnareLevel2 /Mithun:::
        /// <summary>
        /// SaveQuestionnaireLevel2
        /// </summary>
        public static IActionResult Save(SaveQuestionnareLevel2List SaveObj, string constring, int LogException)
        {
            string Msg = string.Empty;
            SqlConnection conn = new SqlConnection(constring);
            SqlCommand cmd = null;

            try
            {
                conn.Open();
                //GNM_User UserDetails = (GNM_User)Session["UserDetails"];

                JObject jObj = JObject.Parse(SaveObj.data);

                int CompanyID = Convert.ToInt32(SaveObj.Company_ID);
                int Count = jObj["rows"].Count();

                for (int i = 0; i < Count; i++)
                {
                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["QuestionaryLevel1_ID"]);
                    jTR.Read();
                    int QuestionaryLevel1_ID = Convert.ToInt32(jTR.Value.ToString());

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["QuestionaryLevel2_ID"]);
                    jTR.Read();
                    int QuestionaryLevel2_ID = Convert.ToInt32(jTR.Value.ToString() == "" ? "0" : jTR.Value.ToString());

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["QuestionLevel2"]);
                    jTR.Read();
                    string QuestionLevel2 = Common.DecryptString(jTR.Value.ToString());

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["QuestionLevel2_IsActive"]);
                    jTR.Read();
                    bool QuestionLevel2_IsActive = Convert.ToBoolean(jTR.Value.ToString());

                    jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["IssueArea_ID"]);
                    jTR.Read();
                    int IssueArea_ID = Convert.ToInt32(jTR.Value.ToString());

                    // Determine if it's an insert or update
                    if (QuestionaryLevel2_ID > 0)
                    {
                        cmd = new SqlCommand("UP_UPD_AM_ERP_SaveQuestionaryLevel2", conn);
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@QuestionaryLevel2_ID", QuestionaryLevel2_ID);
                        cmd.Parameters.AddWithValue("@QuestionLevel2", QuestionLevel2);
                        cmd.Parameters.AddWithValue("@QuestionLevel2_IsActive", QuestionLevel2_IsActive);
                    }
                    else
                    {
                        cmd = new SqlCommand("UP_INS_AM_ERP_SaveQuestionaryLevel2", conn);
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@QuestionLevel2", QuestionLevel2);
                        cmd.Parameters.AddWithValue("@QuestionaryLevel1_ID", QuestionaryLevel1_ID);
                        cmd.Parameters.AddWithValue("@QuestionLevel2_IsActive", QuestionLevel2_IsActive);
                        cmd.Parameters.AddWithValue("@Company_ID", CompanyID);
                        cmd.Parameters.AddWithValue("@IssueArea_ID", IssueArea_ID);
                    }

                    cmd.ExecuteNonQuery();

                    // Assuming gbl.InsertGPSDetails is another method that logs the action
                    string actionDescription = (QuestionaryLevel2_ID > 0) ? "Updated " + QuestionLevel2 : "Inserted " + QuestionLevel2;
                    //   gbl.InsertGPSDetails(Convert.ToInt32(SaveObj.Company_ID.ToString()), Convert.ToInt32(SaveObj.Branch), SaveObj.User_ID, Common.GetObjectID("CoreQuestionnaireLevel2Master",constring), QuestionaryLevel2_ID, 0, 0, actionDescription, false, Convert.ToInt32(SaveObj.MenuID), Convert.ToDateTime(SaveObj.LoggedINDateTime));
                }

                Msg = "Saved";
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                Msg = string.Empty;
            }
            finally
            {
                if (conn.State == ConnectionState.Open)
                    conn.Close();
                if (cmd != null)
                    cmd.Dispose();
            }

            //return Msg;
            return new JsonResult(Msg);
        }
        #endregion

        #region ::: UpdateLocale /Mithun:::
        /// <summary>
        /// UpdateLocale
        /// </summary> 
        public static IActionResult UpdateLocale(UpdateLocaleCoreQuestionnaireLevel2List UpdateLocaleObj, string constring, int LogException)
        {
            int Q2LocaleID = 0;
            var x = default(dynamic);
            try
            {
                HD_QuestionnaireLevel2Locale QLRow = null;
                HD_QuestionnaireLevel2Locale QLLRow = null;
                JObject jObj = JObject.Parse(UpdateLocaleObj.data);

                QLRow = jObj.ToObject<HD_QuestionnaireLevel2Locale>();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    string query = "UP_UpdateOrInsert_AM_ERP_QuestionnaireLevel2Locale";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@QuestionLevel2_ID", QLRow.QuestionLevel2_ID);
                            command.Parameters.AddWithValue("@QuestionnaireLevel2Locale_ID", QLRow.QuestionnaireLevel2Locale_ID);
                            command.Parameters.AddWithValue("@QuestionLevel2", QLRow.QuestionLevel2);
                            command.Parameters.AddWithValue("@LanguageID", QLRow.Language_ID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    Q2LocaleID = reader.GetInt32(reader.GetOrdinal("QuestionnaireLevel2Locale_ID"));
                                }


                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Q2LocaleID;
            return new JsonResult(Q2LocaleID);

        }
        #endregion

        #region ::: Delete QuestionnaireLevel2 /Mithun:::
        /// <summary>
        /// Delete
        /// </summary> 
        public static IActionResult Delete(DeleteQuestionnaireLevel2List DeleteObj, string constring, int LogException)
        {
            string Msg = string.Empty;

            try
            {
                JToken jObj = JObject.Parse(DeleteObj.key);
                int Count = jObj["rows"].Count();
                //GNM_User User = (GNM_User)Session["UserDetails"];

                int CompanyID = Convert.ToInt32(DeleteObj.Company_ID);
                int ID = 0;

                for (int i = 0; i < Count; i++)
                {
                    JToken idToken = jObj["rows"].ElementAt(i).ToObject<JObject>()["id"];
                    ID = Convert.ToInt32(idToken.Value<string>());

                    using (SqlConnection conn = new SqlConnection(constring))
                    {
                        string query = "UP_DEL_AM_ERP_DeleteQuestionaryLevel2AndLocale";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@QuestionaryLevel2_ID", ID);

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                command.ExecuteScalar();


                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }
                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                    //  gbl.InsertGPSDetails(Convert.ToInt32(DeleteObj.Company_ID.ToString()), Convert.ToInt32(DeleteObj.Branch), DeleteObj.User_ID, Common.GetObjectID("CoreQuestionnaireLevel2Master",constring), ID, 0, 0, "Deleted " + ID + "", false, Convert.ToInt32(DeleteObj.MenuID), Convert.ToDateTime(DeleteObj.LoggedINDateTime));
                }

                Msg += CommonFunctionalities.GetResourceString(DeleteObj.UserCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (Exception ex)
            {
                if (ex.InnerException?.InnerException?.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint") ?? false)
                {
                    Msg += CommonFunctionalities.GetResourceString(DeleteObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    Msg += ex.Message;
                }
            }
            //return Msg;
            return new JsonResult(Msg);
        }

        #endregion

        #region ::: CheckQuestionnaireLevel2 /Mithun:::
        /// <summary>
        /// CheckQuestionnaireLevel2
        /// </summary> 
        public static IActionResult CheckQuestionnaireLevel2(CheckQuestionnaireLevel2List CheckQuestionnaireLevel2Obj, string constring, int LogException)
        {
            int count = 0;
            int companyID = Convert.ToInt32(CheckQuestionnaireLevel2Obj.Company_ID);
            using (SqlConnection conn = new SqlConnection(constring))
            {
                string query = "UP_Chk_AM_ERP_CheckQuestionnaireLevel2";
                SqlCommand command = null;
                count = 0; // Initialize count variable

                try
                {
                    using (command = new SqlCommand(query, conn))
                    {
                        SqlParameter[] parameters = new SqlParameter[]
                        {
                new SqlParameter("@QuestionaryLevel1_ID", CheckQuestionnaireLevel2Obj.QuestionnaireLevel1ID),
                new SqlParameter("@QuestionLevel2", CheckQuestionnaireLevel2Obj.QuestionLevel2),
                new SqlParameter("@CompanyID", companyID),
                new SqlParameter("@QuestionnaireLevel2ID", CheckQuestionnaireLevel2Obj.QuestionnaireLevel2ID),
                new SqlParameter("@Exists", SqlDbType.Bit) { Direction = ParameterDirection.Output }
                        };

                        command.Parameters.AddRange(parameters);
                        command.CommandType = CommandType.StoredProcedure;

                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }

                        command.ExecuteNonQuery();

                        bool exists = Convert.ToBoolean(command.Parameters["@Exists"].Value);

                        if (exists)
                        {
                            count = 1;
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }
                    return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
                }
                finally
                {
                    command.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }

                return new JsonResult(count);
            }

        }
        #endregion



        #region ::: Export :::
        /// <summary>
        /// Export
        /// </summary> 
        public static List<QuestionnaireLevel2> SelectList(SelectCoreQuestionnaireLevel2List SelectObj, string constring, int LogException)
        {

            try
            {
                int Count = 0;
                int Total = 0;
                int CompanyID = Convert.ToInt32(SelectObj.Company_ID);

                IQueryable<QuestionnaireLevel2> IQQuestionnaireLevel2 = null;

                var results = new List<QuestionnaireLevel2>();
                string YesE = CommonFunctionalities.GetResourceString(SelectObj.GeneralCulture.ToString(), "yes").ToString();
                string NoE = CommonFunctionalities.GetResourceString(SelectObj.GeneralCulture.ToString(), "no").ToString();
                string YesL = CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "yes").ToString();
                string NoL = CommonFunctionalities.GetResourceString(SelectObj.UserCulture.ToString(), "no").ToString();


                var jsonData = default(dynamic);
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    string query = "UP_Select_AM_ERP_SelectQuestionnaireLevel2";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.Add(new SqlParameter("@QuestionnaireLevel1ID", SelectObj.QuestionnaireLevel1ID));
                            command.Parameters.Add(new SqlParameter("@CompanyID", CompanyID));
                            command.Parameters.Add(new SqlParameter("@GeneralLanguageID", Convert.ToInt32(SelectObj.GeneralLanguageID)));
                            command.Parameters.Add(new SqlParameter("@LanguageID", SelectObj.LanguageID));
                            command.Parameters.Add(new SqlParameter("@YesE", YesE));
                            command.Parameters.Add(new SqlParameter("@NoE", NoE));
                            command.Parameters.Add(new SqlParameter("@YesL", YesL));
                            command.Parameters.Add(new SqlParameter("@NoL", NoL));

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    results.Add(new QuestionnaireLevel2
                                    {
                                        QuestionaryLevel2_ID = reader.IsDBNull(0) ? 0 : reader.GetInt32(0),
                                        QuestionLevel2 = reader.IsDBNull(1) ? string.Empty : reader.GetString(1),
                                        QuestionLevel2_IsActive = reader.IsDBNull(2) ? string.Empty : reader.GetString(2)
                                    });
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }



                IQQuestionnaireLevel2 = results.AsQueryable<QuestionnaireLevel2>();

                if (SelectObj.filters.ToString() != "null" && SelectObj.filters.ToString() != "undefined")
                {
                    string decodedValue = Uri.UnescapeDataString(SelectObj.filters);
                    Filters filtersObj = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<Filters>();
                    if (filtersObj.rules.Count() > 0)
                        IQQuestionnaireLevel2 = IQQuestionnaireLevel2.FilterSearch<QuestionnaireLevel2>(filtersObj);
                }
                if (SelectObj.Query.ToString() != "null")
                {
                    string decodedValue = Uri.UnescapeDataString(SelectObj.Query);
                    AdvanceFilter advnfilter = JObject.Parse(decodedValue).ToObject<AdvanceFilter>();
                    IQQuestionnaireLevel2 = IQQuestionnaireLevel2.AdvanceSearch<QuestionnaireLevel2>(advnfilter);
                }

                IQQuestionnaireLevel2 = IQQuestionnaireLevel2.OrderByField<QuestionnaireLevel2>(SelectObj.sidx, SelectObj.sord);




                //return Json(jsonData, JsonRequestBehavior.AllowGet);
                return IQQuestionnaireLevel2.ToList();
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return null;
            }
        }
        public static async Task<object> Export(SelectCoreQuestionnaireLevel2List ExportObj, string connString, int LogException)
        {
            IQueryable<QuestionnaireLevel2> IQQuestionnaireLevel2 = null;
            int Count = 0;
            DataTable Dt = new DataTable();
            try
            {

                List<QuestionnaireLevel2> results = ((List<QuestionnaireLevel2>)SelectList(ExportObj, connString, LogException));
                IQQuestionnaireLevel2 = results.AsQueryable();
                //var QuestionnaireLevel2Array = from a in IQQuestionnaireLevel2.AsEnumerable()
                //                               select new
                //                               {
                //                                   a.QuestionLevel2,
                //                                   a.QuestionLevel2_IsActive
                //                               };

                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "QuestionnaireLevel2").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "Active").ToString());

                DataTable DtAlignment = new DataTable();
                DtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "QuestionnaireLevel2").ToString());
                DtAlignment.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "active").ToString());
                DtAlignment.Rows.Add(0, 0);

                Count = IQQuestionnaireLevel2.AsEnumerable().Count();
                if (Count > 0)
                {
                    for (int i = 0; i < Count; i++)
                    {
                        Dt.Rows.Add(IQQuestionnaireLevel2.ElementAt(i).QuestionLevel2, IQQuestionnaireLevel2.ElementAt(i).QuestionLevel2_IsActive);
                    }

                    DataTable Dt1 = new DataTable();
                    Dt1.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "IssueArea").ToString());
                    Dt1.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "QuestionnaireLevel1").ToString());
                    Dt1.Rows.Add(Common.DecryptString(ExportObj.IssueArea), Common.DecryptString(ExportObj.QuestionnaireLevel1Description));
                    //ReportExport.Export(ExportObj.exprtType, Dt, Dt1, DtAlignment, "QuestionnaireLevel2", CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "QuestionnaireLevel2").ToString());
                    ReportExportList reportExportList = new ReportExportList
                    {
                        Company_ID = ExportObj.Company_ID, // Assuming this is available in ExportObj
                        Branch = ExportObj.Branch_ID.ToString(),
                        GeneralLanguageID = ExportObj.LanguageID,
                        UserLanguageID = ExportObj.UserLanguageID,
                        Options = Dt1,
                        dt = Dt,
                        Alignment = DtAlignment,
                        FileName = "QuestionnaireLevel2", // Set a default or dynamic filename
                        Header = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "QuestionnaireLevel2").ToString(), // Set a default or dynamic header
                        exprtType = ExportObj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                        UserCulture = ExportObj.UserCulture
                    };

                    var result = await ReportExport.Export(reportExportList, connString, LogException);
                    return result.Value;
                    //gbl.InsertGPSDetails(Convert.ToInt32(ExportObj.Company_ID.ToString()), Convert.ToInt32(ExportObj.Branch), ExportObj.User_ID, Common.GetObjectID("CoreQuestionnaireLevel2Master"), 0, 0, 0, "Questionnaire Level2-Export", false, Convert.ToInt32(ExportObj.MenuID), Convert.ToDateTime(ExportObj.LoggedINDateTime));
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return null;
        }
        #endregion








    }
    public class CheckQuestionnaireLevel2List
    {
        public int QuestionnaireLevel1ID { get; set; }
        public int Company_ID { get; set; }
        public string QuestionLevel2 { get; set; }
        public int QuestionnaireLevel2ID { get; set; }
    }
    public class DeleteQuestionnaireLevel2List
    {
        public int Branch { get; set; }
        public int User_ID { get; set; }
        public int UserLanguageID { get; set; }
        public int Company_ID { get; set; }
        public int MenuID { get; set; }
        public string key { get; set; }
        public string UserCulture { get; set; }
        public DateTime LoggedINDateTime { get; set; }

    }
    public class UpdateLocaleCoreQuestionnaireLevel2List
    {
        public string data { get; set; }
    }
    public class SaveQuestionnareLevel2List
    {
        public int Branch { get; set; }
        public int User_ID { get; set; }
        public int UserLanguageID { get; set; }
        public int Company_ID { get; set; }
        public int MenuID { get; set; }
        public string data { get; set; }

        public DateTime LoggedINDateTime { get; set; }

    }
    public class SelectParticularQuestionnaireLevel2List
    {
        public int LanguageID { get; set; }
        public int UserLanguageID { get; set; }
        public int QuestionnaireLevel2ID { get; set; }
    }
    public class SelectCoreQuestionnaireLevel2List
    {
        public int GeneralLanguageID { get; set; }
        public int Company_ID { get; set; }
        public int LanguageID { get; set; }
        public int QuestionnaireLevel1ID { get; set; }
        public string GeneralCulture { get; set; }
        public string UserCulture { get; set; }
        public string IssueArea { get; set; }
        public string QuestionnaireLevel1Description { get; set; }
        public int exprtType { get; set; }
        public string Branch { get; set; }
        public int User_ID { get; set; }
        public int MenuID { get; set; }
        public DateTime LoggedINDateTime { set; get; }

        public string filters { get; set; }

        public string Query { get; set; }
        public string sidx { get; set; }
        public string sord { get; set; }
        public int Branch_ID { get; set; }
        public int UserLanguageID { get; set; }
    }
    public class SelectReferenceMasterCoreQuestionnaireLevel2List
    {
        public int GeneralLanguageID { get; set; }
        public int Company_ID { get; set; }
        public int LanguageID { get; set; }
        public string ReferenceMasterName { get; set; }
    }

    public class CheckQuestionnaireLevel2LocaleList
    {
        public int UserLanguageID { get; set; }
        public int QuestionLevelID { get; set; }
        public int QuestionLevel2LocaleID { get; set; }
        public int IssueAreaID { get; set; }
        public string QuestionLevel2 { get; set; }
    }
    public class SelectQuestionLevel1List
    {
        public int Company_ID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int IssueAreaID { get; set; }
        public int LanguageID { get; set; }

    }
    public class QuestionnaireLevel2
    {
        public int QuestionaryLevel2_ID
        {
            get;
            set;
        }

        public int QuestionaryLevel1_ID
        {
            get;
            set;
        }

        public string QuestionLevel2
        {
            get;
            set;
        }

        public string QuestionLevel2_IsActive
        {
            get;
            set;
        }
    }
}
