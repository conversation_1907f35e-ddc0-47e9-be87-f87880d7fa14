﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace SharedAPIClassLibrary_AMERP
{
    public class HelpDesk_Tr_CaseRegistrationCountServices
    {

        #region ::: global Declartation Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To Load Branch
        /// </summary>
        /// 
        public static string GenLangCode = string.Empty;
        public static string UserLangCode = string.Empty;
        public static int TotalRecievedCount = 0;
        public static int TotalCompletedCount = 0;
        public static int TotalInProgressCount = 0;
        public static int TotalOnHoldCount = 0;
        public static int TotalClosedCount = 0;
        public static int InProgressID = 0;
        public static int ClosedID = 0;
        public static int HoldID = 0;
        public static int CompletedID = 0;
        public static int EscalatedID = 0;
        public static int TotalEscalatedcount = 0;
        public static bool FilterPartyBasedonCompany = true;

        #endregion



        #region ::: Get Party Detail Grid Uday kumar J B 18-11-2024:::
        /// <summary>
        ///To select menus of respective module
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult SelectPartyDetailGrid(HelpDesk_Tr_CaseRegistrationCountSelectPartyDetailGridList HelpDesk_Tr_CaseRegistrationCountSelectPartyDetailGridobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonobj = default(dynamic);
            try
            {
                // Decrypt Party Name
                string Pname = Common.DecryptString(HelpDesk_Tr_CaseRegistrationCountSelectPartyDetailGridobj.PartyName);
                string GenLangCode = HelpDesk_Tr_CaseRegistrationCountSelectPartyDetailGridobj.GeneralLanguageCode.ToString();
                string UserLangCode = HelpDesk_Tr_CaseRegistrationCountSelectPartyDetailGridobj.UserLanguageCode.ToString();

                DataTable resultTable = new DataTable();
                int totalRecords = 0;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    if (GenLangCode == UserLangCode)
                    {
                        // Call SP to Get General Parties
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetGeneralParties", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@PartyName", Pname);
                            cmd.Parameters.AddWithValue("@CompanyID", HelpDesk_Tr_CaseRegistrationCountSelectPartyDetailGridobj.Company_ID);
                            cmd.Parameters.AddWithValue("@PartyType1", 1);
                            cmd.Parameters.AddWithValue("@PartyType2", 2);

                            SqlDataAdapter adapter = new SqlDataAdapter(cmd);
                            adapter.Fill(resultTable);
                        }
                    }
                    else
                    {
                        // Call SP to Get Party Locales
                        DataTable partyLocaleTable = new DataTable();
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetPartyLocales", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@PartyName", Pname);
                            cmd.Parameters.AddWithValue("@LanguageID", HelpDesk_Tr_CaseRegistrationCountSelectPartyDetailGridobj.Language_ID);

                            SqlDataAdapter adapter = new SqlDataAdapter(cmd);
                            adapter.Fill(partyLocaleTable);
                        }

                        // Call SP to Get Parties for Locale
                        DataTable partyTable = new DataTable();
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetPartiesByLocale", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@CompanyID", HelpDesk_Tr_CaseRegistrationCountSelectPartyDetailGridobj.Company_ID);

                            SqlDataAdapter adapter = new SqlDataAdapter(cmd);
                            adapter.Fill(partyTable);
                        }

                        // Join partyLocaleTable and partyTable manually
                        var joinedTable = from DataRow loc in partyLocaleTable.Rows
                                          join DataRow party in partyTable.Rows
                                          on loc["Party_ID"].ToString() equals party["Party_ID"].ToString()
                                          select new
                                          {
                                              Party_ID = loc["Party_ID"],
                                              Select = $"<label key='{loc["Party_ID"]}' class='PartySelect' style='color:blue;text-decoration:underline'>Select</label>",
                                              Party_Name = loc["Party_Name"],
                                              Party_Location = party["Party_Location"]
                                          };

                        resultTable = LINQToDataTable(joinedTable.ToList());
                    }
                }

                // Pagination logic
                totalRecords = resultTable.Rows.Count;
                int totalPages = (int)Math.Ceiling((double)totalRecords / rows);

                var pagedData = resultTable.AsEnumerable()
                                           .Skip((page - 1) * rows)
                                           .Take(rows)
                                           .Select(row => new
                                           {
                                               Party_ID = row["Party_ID"],
                                               Select = row["Select"],
                                               Party_Name = row["Party_Name"],
                                               Party_Location = row["Party_Location"]
                                           });

                jsonobj = new
                {
                    TotalPages = totalPages,
                    PageNo = page,
                    RecordCount = pagedData.Count(),
                    rows = pagedData.ToArray()
                };
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonobj);
        }

        // Utility method to convert LINQ result to DataTable
        private static DataTable LINQToDataTable<T>(IEnumerable<T> varlist)
        {
            DataTable dt = new DataTable();
            var props = typeof(T).GetProperties();
            foreach (var prop in props)
            {
                dt.Columns.Add(prop.Name, prop.PropertyType);
            }

            foreach (var item in varlist)
            {
                DataRow dr = dt.NewRow();
                foreach (var prop in props)
                {
                    dr[prop.Name] = prop.GetValue(item, null);
                }
                dt.Rows.Add(dr);
            }

            return dt;
        }
        #endregion



        #region ::: Get Model Detail Grid Uday Kumar J B 18-11-2024:::
        /// <summary>
        ///To select menus of respective module
        /// </summary>
        /// <returns>...</returns>
        /// 

        public static IActionResult SelectModelDetailGrid(HelpDesk_Tr_CaseRegistrationCountSelectModelDetailGridList HelpDesk_Tr_CaseRegistrationCountSelectModelDetailGridobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonobj = new object();
            var models = new List<dynamic>();
            int total = 0;

            string Mname = Common.DecryptString(HelpDesk_Tr_CaseRegistrationCountSelectModelDetailGridobj.ModelName);

            try
            {
                string GenLangCode = HelpDesk_Tr_CaseRegistrationCountSelectModelDetailGridobj.GeneralLanguageCode.ToString();
                string UserLangCode = HelpDesk_Tr_CaseRegistrationCountSelectModelDetailGridobj.UserLanguageCode.ToString();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    if (GenLangCode == UserLangCode)
                    {
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetModelsByLanguageCode", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@ModelName", Mname);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    models.Add(new
                                    {
                                        Model_ID = reader["Model_ID"],
                                        Select = $"<label key='{reader["Model_ID"]}' class='ModelSelect' style='color:blue;text-decoration:underline'>Select</label>",
                                        Model_Name = reader["Model_Name"]
                                    });
                                }
                            }
                        }
                    }
                    else
                    {
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetLocalizedModels", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@ModelName", Mname);
                            cmd.Parameters.AddWithValue("@LanguageID", HelpDesk_Tr_CaseRegistrationCountSelectModelDetailGridobj.Language_ID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    models.Add(new
                                    {
                                        Model_ID = reader["Model_ID"],
                                        Select = $"<label key='{reader["Model_ID"]}' class='ModelSelect' style='color:blue;text-decoration:underline'>Select</label>",
                                        Model_Name = reader["Model_Name"]
                                    });
                                }
                            }
                        }
                    }
                }

                // Pagination
                total = (int)Math.Ceiling((double)models.Count / rows);
                var pagedModels = models.Skip((page - 1) * rows).Take(rows);

                jsonobj = new
                {
                    TotalPages = total,
                    PageNo = page,
                    RecordCount = pagedModels.Count(),
                    rows = pagedModels.ToArray()
                };
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return new JsonResult(jsonobj);
        }
        #endregion



        #region ::: load Reference Master data Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To load master drop downs
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult loadMasters(HelpDesk_Tr_CaseRegistrationCountloadMastersList HelpDesk_Tr_CaseRegistrationCountloadMastersobj, string connString, int LogException)
        {
            var jsonobj = default(dynamic);
            try
            {
                int LangID = HelpDesk_Tr_CaseRegistrationCountloadMastersobj.Language_ID;
                string GenLangCode = HelpDesk_Tr_CaseRegistrationCountloadMastersobj.GeneralLanguageCode.ToString();
                string UserLangCode = HelpDesk_Tr_CaseRegistrationCountloadMastersobj.UserLanguageCode.ToString();

                string CompanyArray = (HelpDesk_Tr_CaseRegistrationCountloadMastersobj.CompanyArray == "") ? "0" : HelpDesk_Tr_CaseRegistrationCountloadMastersobj.CompanyArray;
                string[] CompanyIDs = CompanyArray.Split(',');
                int CompanyID = Convert.ToInt32(CompanyIDs[0]);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetLocalizedModels", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@MasterName", HelpDesk_Tr_CaseRegistrationCountloadMastersobj.MasterName.ToUpper());
                        cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                        cmd.Parameters.AddWithValue("@LanguageID", LangID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageCode", GenLangCode);
                        cmd.Parameters.AddWithValue("@UserLanguageCode", UserLangCode);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.HasRows)
                            {
                                var rows = new List<object>();
                                while (reader.Read())
                                {
                                    rows.Add(new
                                    {
                                        ID = reader["ID"],
                                        Name = reader["Name"]
                                    });
                                }

                                jsonobj = new { rows = rows.ToArray() };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonobj);
        }
        #endregion


        #region ::: load Product Type Uday kumar J B 18-11-2024:::
        /// <summary>
        /// To load Product Type
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult ProductType(HelpDesk_Tr_CaseRegistrationCountProductTypeList HelpDesk_Tr_CaseRegistrationCountProductTypesobj, string connString, int LogException)
        {
            var jsonobj = default(dynamic);
            try
            {
                int LangID = HelpDesk_Tr_CaseRegistrationCountProductTypesobj.Language_ID;

                string GenLangCode = HelpDesk_Tr_CaseRegistrationCountProductTypesobj.GeneralLanguageCode.ToString();
                string UserLangCode = HelpDesk_Tr_CaseRegistrationCountProductTypesobj.UserLanguageCode.ToString();

                Top_Company companys = new Top_Company();
                string Company_ID = string.Empty;

                if (HelpDesk_Tr_CaseRegistrationCountProductTypesobj.Company != null && HelpDesk_Tr_CaseRegistrationCountProductTypesobj.Company != "")
                {
                    companys = JObject.Parse(Common.DecryptString(HelpDesk_Tr_CaseRegistrationCountProductTypesobj.Company)).ToObject<Top_Company>();
                    for (int i = 0; i < companys.Companies.Count; i++)
                    {
                        Company_ID += companys.Companies[i].ID + ",";
                    }
                    Company_ID = Company_ID.TrimEnd(',');
                }

                DataTable resultTable = new DataTable();
                foreach (var companyID in Company_ID.Split(','))
                {
                    if (!string.IsNullOrWhiteSpace(companyID))
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            using (SqlCommand cmd = new SqlCommand(
                                GenLangCode == UserLangCode ? "Sp_AMERP_HelpDesk_GetProductTypeGeneralLang" : "SP_AMERP_HelpDesk_GetProductTypeUserLang", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@CompanyID", Convert.ToInt32(companyID));

                                if (GenLangCode != UserLangCode)
                                {
                                    cmd.Parameters.AddWithValue("@LangID", LangID);
                                }

                                SqlDataAdapter adapter = new SqlDataAdapter(cmd);
                                adapter.Fill(resultTable);
                            }
                        }
                    }
                }

                if (GenLangCode == UserLangCode)
                {
                    jsonobj = new
                    {
                        rows = resultTable.AsEnumerable()
                            .OrderBy(row => row["ProductType_Name"])
                            .Select(row => new
                            {
                                ID = row["ProductType_ID"],
                                Name = row["ProductType_Name"]
                            })
                            .Distinct()
                            .ToArray()
                    };
                }
                else
                {
                    jsonobj = new
                    {
                        rows = resultTable.AsEnumerable()
                            .OrderBy(row => row["ProductType_Name"])
                            .Select(row => new
                            {
                                ID = row["ProductType_ID"],
                                Name = row["ProductType_Name"]
                            })
                            .Distinct()
                            .ToArray()
                    };
                }


            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonobj);
        }
        #endregion



        #region ::: Get Party Dtails Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To get Customer details
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult GetPartyDetails(HelpDesk_Tr_CaseRegistrationCountGetPartyDetailsList HelpDesk_Tr_CaseRegistrationCountGetPartyDetailssobj, string connString, int LogException)
        {
            var jsonResult = default(dynamic);
            string Pname = Common.DecryptString(HelpDesk_Tr_CaseRegistrationCountGetPartyDetailssobj.PartyName);
            try
            {
                int LangID = HelpDesk_Tr_CaseRegistrationCountGetPartyDetailssobj.Language_ID;
                string GenLangCode = HelpDesk_Tr_CaseRegistrationCountGetPartyDetailssobj.GeneralLanguageCode.ToString();
                string UserLangCode = HelpDesk_Tr_CaseRegistrationCountGetPartyDetailssobj.UserLanguageCode.ToString();
                int CompanyId = 0;

                if (HelpDesk_Tr_CaseRegistrationCountGetPartyDetailssobj.Company != null && HelpDesk_Tr_CaseRegistrationCountGetPartyDetailssobj.Company != "")
                {
                    CompanyId = Convert.ToInt32(HelpDesk_Tr_CaseRegistrationCountGetPartyDetailssobj.Company.ToString().Split(',')[0]);
                }

                bool FilterPartyBasedonCompany = false;

                // Fetch FilterPartyBasedonCompany using ADO.NET
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetFilterPartyBasedonCompany", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyId", CompanyId);

                        object result = cmd.ExecuteScalar();
                        FilterPartyBasedonCompany = result != null && result.ToString().ToUpper() == "TRUE";
                    }
                }

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetPartyDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@PartyName", Pname);
                        cmd.Parameters.AddWithValue("@CompanyId", CompanyId);
                        cmd.Parameters.AddWithValue("@LanguageId", LangID);
                        cmd.Parameters.AddWithValue("@FilterByCompany", FilterPartyBasedonCompany);
                        cmd.Parameters.AddWithValue("@GenLangCode", GenLangCode);
                        cmd.Parameters.AddWithValue("@UserLangCode", UserLangCode);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (!reader.HasRows)
                            {
                                jsonResult = new { Result = "0" };
                            }
                            else
                            {
                                var parties = new List<dynamic>();
                                while (reader.Read())
                                {
                                    parties.Add(new
                                    {
                                        PartyID = reader["Party_ID"],
                                        PartyName = reader["Party_Name"]
                                    });
                                }

                                if (parties.Count == 1)
                                {
                                    jsonResult = new
                                    {
                                        Result = "1",
                                        PartyID = parties.First().PartyID
                                    };
                                }
                                else if (parties.Count > 1)
                                {
                                    jsonResult = new { Result = "2" };
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                jsonResult = new { Result = "0" };
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonResult);
        }
        #endregion




        #region ::: Get Model Dtails Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To get Customer details
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult GetModel(HelpDesk_Tr_CaseRegistrationCountGetModelList HelpDesk_Tr_CaseRegistrationCountGetModelsobj, string connString, int LogException)
        {
            var jsonResult = default(dynamic);
            string ModelDec = Common.DecryptString(HelpDesk_Tr_CaseRegistrationCountGetModelsobj.ModelName);
            int CompanyId = 0;

            if (HelpDesk_Tr_CaseRegistrationCountGetModelsobj.Company != null && HelpDesk_Tr_CaseRegistrationCountGetModelsobj.Company != "")
            {
                CompanyId = Convert.ToInt32(HelpDesk_Tr_CaseRegistrationCountGetModelsobj.Company.ToString().Split(',')[0]);
            }

            try
            {
                int LangID = HelpDesk_Tr_CaseRegistrationCountGetModelsobj.Language_ID;
                string GenLangCode = HelpDesk_Tr_CaseRegistrationCountGetModelsobj.GeneralLanguageCode.ToString();
                string UserLangCode = HelpDesk_Tr_CaseRegistrationCountGetModelsobj.UserLanguageCode.ToString();

                List<dynamic> res = new List<dynamic>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    if (GenLangCode == UserLangCode)
                    {
                        // Fetch Models matching the criteria
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetModelDetailsByLanguageMatch", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@ModelName", ModelDec);
                            cmd.Parameters.AddWithValue("@CompanyId", CompanyId);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    res.Add(new
                                    {
                                        ID = reader["Model_ID"],
                                        Name = reader["Model_Name"]
                                    });
                                }
                            }
                        }
                    }
                    else
                    {
                        // Fetch Models with language mismatch
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetModelDetailsByLanguageMismatch", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@ModelName", ModelDec);
                            cmd.Parameters.AddWithValue("@CompanyId", CompanyId);
                            cmd.Parameters.AddWithValue("@LanguageId", LangID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    res.Add(new
                                    {
                                        ID = reader["Model_ID"],
                                        Name = reader["Model_Name"]
                                    });
                                }
                            }
                        }
                    }
                }

                // Process the results
                if (res.Count == 0)
                {
                    jsonResult = new { Result = "0" };
                }
                else if (res.Count == 1)
                {
                    jsonResult = new
                    {
                        Result = "1",
                        ModelID = res.First().ID
                    };
                }
                else if (res.Count > 1)
                {
                    jsonResult = new { Result = "2" };
                }
            }
            catch (Exception ex)
            {
                jsonResult = new { Result = "0" };
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonResult);
        }
        #endregion



        #region ::: Get Status IDs Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To get Status IDs
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult GetStatusIDs(string connString, int LogException)
        {
            int InProgressID = 0;
            int ClosedID = 0;
            int HoldID = 0;
            int CompletedID = 0;
            int EscalatedID = 0;
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetAllStatusIDs", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                string statusName = reader["WFStepStatus_Nm"].ToString().ToUpper();
                                int statusID = Convert.ToInt32(reader["WFStepStatus_ID"]);

                                switch (statusName)
                                {
                                    case "InProgress":
                                        InProgressID = statusID;
                                        break;
                                    case "Closed":
                                        ClosedID = statusID;
                                        break;
                                    case "Hold":
                                        HoldID = statusID;
                                        break;
                                    case "Completed":
                                        CompletedID = statusID;
                                        break;
                                    case "Escalated":
                                        EscalatedID = statusID;
                                        break;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return null;
        }
        #endregion



        #region ::: Load Brand Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To Load Brand
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static IActionResult LoadBrand(HelpDesk_Tr_CaseRegistrationCountLoadBrandList HelpDesk_Tr_CaseRegistrationCountLoadBrandsobj, string connString, int LogException)
        {
            var BrandData = new List<object>();
            try
            {
                int LangID = HelpDesk_Tr_CaseRegistrationCountLoadBrandsobj.Language_ID;

                string GenLangCode = HelpDesk_Tr_CaseRegistrationCountLoadBrandsobj.GeneralLanguageCode.ToString();
                string UserLangCode = HelpDesk_Tr_CaseRegistrationCountLoadBrandsobj.UserLanguageCode.ToString();

                Top_Company companys = new Top_Company();
                string Company_ID = string.Empty;

                if (HelpDesk_Tr_CaseRegistrationCountLoadBrandsobj.Company != null && HelpDesk_Tr_CaseRegistrationCountLoadBrandsobj.Company != "")
                {
                    companys = JObject.Parse(Common.DecryptString(HelpDesk_Tr_CaseRegistrationCountLoadBrandsobj.Company)).ToObject<Top_Company>();
                    Company_ID = string.Join(",", companys.Companies.Select(c => c.ID));
                }

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand(GenLangCode == UserLangCode ? "SP_AMERP_HelpDesk_LoadBrand_DefaultLanguage" : "SP_AMERP_HelpDesk_LoadBrand_Localized", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyIDs", Company_ID);
                        if (GenLangCode != UserLangCode)
                        {
                            cmd.Parameters.AddWithValue("@LanguageID", LangID);
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                BrandData.Add(new
                                {
                                    ID = reader["RefMasterDetail_ID"],
                                    Name = reader["RefMasterDetail_Name"]
                                });
                            }
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(BrandData);
        }
        #endregion




        #region ::: Select Service Request Count Year Uday Kumar J B 22-11-2024:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        //-- Modified by Venkateshwari for HelpDesk CR-5 Changes 18-Aug-2015 -- Start


        public static IActionResult SelectYearReport(HelpDesk_Tr_CaseRegistrationCountSelectYearReportList HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonobj = default(dynamic);
            int count = 0;
            int total = 0;
            IEnumerable<ServiceRequestCount> SRCountListRecieved = null;
            IEnumerable<ServiceRequestCount> SRCountListCompleted = null;
            IEnumerable<ServiceRequestCount> SRCountListInProgress = null;
            IEnumerable<ServiceRequestCount> SRCountListOnHold = null;
            IEnumerable<ServiceRequestCount> SRCountListClosed = null;
            IEnumerable<ServiceRequestCount> SRCountListEscalated = null;//Added by Harish on 8-Oct-2014
            IEnumerable<ServiceRequestCount> SRCountListStatus = null;
            List<ServiceRequestCount> FinalResultList = new List<ServiceRequestCount>();
            IQueryable<ServiceRequestCount> FinalResultSort = null;
            ServiceRequestCount FinalResult = new ServiceRequestCount();
            string GenLangCode = string.Empty;
            string UserLangCode = string.Empty;
            GetStatusIDs(connString, LogException);
            try
            {
                GenLangCode = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.GeneralLanguageCode.ToString();
                UserLangCode = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserLanguageCode.ToString();
                int userid = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.User_ID;
                string frmdate = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.FromDate.ToString();
                string todate = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.ToDate.ToString();
                string Stype = Common.DecryptString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Type);
                int Mode = Convert.ToInt32(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.SelectedCriteria);
                SelectionCriteria Selection = JObject.Parse(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Data).ToObject<SelectionCriteria>();

                string CompanyIds = string.Empty;
                Top_Company companies = new Top_Company();
                if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Company != null && HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Company != "")
                {
                    companies = JObject.Parse(Common.DecryptString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Company)).ToObject<Top_Company>();
                }

                string BranchIds = string.Empty;
                RTS_Branch branchs = new RTS_Branch();
                if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Branch != null && HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Branch != "")
                {
                    branchs = JObject.Parse(Common.DecryptString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Branch)).ToObject<RTS_Branch>();
                }


                foreach (var s in Selection.SelectedIDs)
                {
                    s.Name = Common.DecryptString(s.Name);
                }

                List<ServiceRequestCount> serviceRequestCounts = new List<ServiceRequestCount>();

                if (Mode == 1)//Call type
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByIssueAreaCRC", conn);
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@CompanyJSON", companies);
                        cmd.Parameters.AddWithValue("@BranchJSON", branchs);
                        cmd.Parameters.AddWithValue("@FrmDate", string.IsNullOrEmpty(frmdate) ? DBNull.Value : (object)DateTime.Parse(frmdate));
                        cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? DBNull.Value : (object)DateTime.Parse(todate));

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ServiceRequestCount requestCount = new ServiceRequestCount
                                {
                                    Type = Selection.SelectedIDs.Where(s => s.Name == Stype).FirstOrDefault(s => s.ID.ToString() == reader["IssueArea_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    StatusID = Convert.ToInt32(reader["StatusID"]),
                                    StatusName = reader["StatusName"].ToString()
                                };

                                serviceRequestCounts.Add(requestCount);
                            }
                            SRCountListRecieved = serviceRequestCounts.AsEnumerable();
                        }
                    }

                }

                else if (Mode == 2)//Call Nature
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByCallComplexityCRC", conn);
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@CompanyJSON", companies);
                        cmd.Parameters.AddWithValue("@BranchJSON", branchs);
                        cmd.Parameters.AddWithValue("@FrmDate", string.IsNullOrEmpty(frmdate) ? DBNull.Value : (object)DateTime.Parse(frmdate));
                        cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? DBNull.Value : (object)DateTime.Parse(todate));

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ServiceRequestCount requestCount = new ServiceRequestCount
                                {
                                    Type = Selection.SelectedIDs.Where(s => s.Name == Stype).FirstOrDefault(s => s.ID.ToString() == reader["CallComplexity_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    StatusID = Convert.ToInt32(reader["StatusID"]),
                                    StatusName = reader["StatusName"].ToString()
                                };

                                serviceRequestCounts.Add(requestCount);
                            }
                            SRCountListRecieved = serviceRequestCounts.AsEnumerable();
                        }
                    }
                }

                else if (Mode == 3)//Party
                {
                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByPartyCR", conn);
                            cmd.CommandType = CommandType.StoredProcedure;

                            // Add parameters
                            cmd.Parameters.AddWithValue("@CompanyJSON", companies);
                            cmd.Parameters.AddWithValue("@BranchJSON", branchs);
                            cmd.Parameters.AddWithValue("@FrmDate", string.IsNullOrEmpty(frmdate) ? DBNull.Value : (object)DateTime.Parse(frmdate));
                            cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? DBNull.Value : (object)DateTime.Parse(todate));

                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount requestCount = new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.Where(s => s.Name == Stype).FirstOrDefault(s => s.ID.ToString() == reader["Party_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString()
                                    };

                                    serviceRequestCounts.Add(requestCount);
                                }
                                SRCountListRecieved = serviceRequestCounts.AsEnumerable();
                            }
                        }
                    }
                    else
                    {

                        if (GenLangCode == UserLangCode)
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByPartyNameCRC", conn);
                                cmd.CommandType = CommandType.StoredProcedure;

                                // Add parameters
                                cmd.Parameters.AddWithValue("@CompanyJSON", companies);
                                cmd.Parameters.AddWithValue("@BranchJSON", branchs);
                                cmd.Parameters.AddWithValue("@FrmDate", string.IsNullOrEmpty(frmdate) ? DBNull.Value : (object)DateTime.Parse(frmdate));
                                cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? DBNull.Value : (object)DateTime.Parse(todate));

                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount requestCount = new ServiceRequestCount
                                        {
                                            Type = reader["Type"].ToString() == Stype ? reader["Type"].ToString() : "No",
                                            Year = Convert.ToInt32(reader["Year"]),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString()
                                        };

                                        serviceRequestCounts.Add(requestCount);
                                    }
                                    SRCountListRecieved = serviceRequestCounts.AsEnumerable();
                                }
                            }
                        }
                        else
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByPartyNameAndLanguageCRC", conn);
                                cmd.CommandType = CommandType.StoredProcedure;

                                // Add parameters
                                cmd.Parameters.AddWithValue("@CompanyJSON", companies);
                                cmd.Parameters.AddWithValue("@BranchJSON", branchs);
                                cmd.Parameters.AddWithValue("@UserLanguageID", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Language_ID);
                                cmd.Parameters.AddWithValue("@FrmDate", string.IsNullOrEmpty(frmdate) ? DBNull.Value : (object)DateTime.Parse(frmdate));
                                cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? DBNull.Value : (object)DateTime.Parse(todate));

                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount requestCount = new ServiceRequestCount
                                        {
                                            Type = reader["Type"].ToString() == Stype ? reader["Type"].ToString() : "No",
                                            Year = Convert.ToInt32(reader["Year"]),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString()
                                        };

                                        serviceRequestCounts.Add(requestCount);
                                    }
                                    SRCountListRecieved = serviceRequestCounts.AsEnumerable();
                                }
                            }
                        }
                    }

                }

                else if (Mode == 4)//Priority
                {

                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByPriorityCRC", conn);
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@CompanyJSON", companies);
                        cmd.Parameters.AddWithValue("@BranchJSON", branchs);
                        cmd.Parameters.AddWithValue("@FrmDate", string.IsNullOrEmpty(frmdate) ? DBNull.Value : (object)DateTime.Parse(frmdate));
                        cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? DBNull.Value : (object)DateTime.Parse(todate));

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ServiceRequestCount requestCount = new ServiceRequestCount
                                {
                                    Type = Selection.SelectedIDs.Where(s => s.Name == Stype).FirstOrDefault(s => s.ID.ToString() == reader["CallPriority_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    StatusID = Convert.ToInt32(reader["StatusID"]),
                                    StatusName = reader["StatusName"].ToString()
                                };

                                serviceRequestCounts.Add(requestCount);
                            }
                            SRCountListRecieved = serviceRequestCounts.AsEnumerable();
                        }
                    }

                }

                else if (Mode == 5)//Model
                {
                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByModelCRC", conn);
                            cmd.CommandType = CommandType.StoredProcedure;

                            // Add parameters
                            cmd.Parameters.AddWithValue("@CompanyJSON", companies);
                            cmd.Parameters.AddWithValue("@BranchJSON", branchs);
                            cmd.Parameters.AddWithValue("@FrmDate", string.IsNullOrEmpty(frmdate) ? DBNull.Value : (object)DateTime.Parse(frmdate));
                            cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? DBNull.Value : (object)DateTime.Parse(todate));

                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount requestCount = new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.Where(s => s.Name == Stype).FirstOrDefault(s => s.ID.ToString() == reader["Model_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString()
                                    };

                                    serviceRequestCounts.Add(requestCount);
                                }
                                SRCountListRecieved = serviceRequestCounts.AsEnumerable();
                            }
                        }
                    }
                    else
                    {

                        if (GenLangCode == UserLangCode)
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByModelNameCRC", conn);
                                cmd.CommandType = CommandType.StoredProcedure;

                                // Add parameters
                                cmd.Parameters.AddWithValue("@CompanyJSON", companies);
                                cmd.Parameters.AddWithValue("@BranchJSON", branchs);
                                cmd.Parameters.AddWithValue("@FrmDate", string.IsNullOrEmpty(frmdate) ? DBNull.Value : (object)DateTime.Parse(frmdate));
                                cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? DBNull.Value : (object)DateTime.Parse(todate));

                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount requestCount = new ServiceRequestCount
                                        {
                                            Type = reader["Type"].ToString(),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString()
                                        };

                                        serviceRequestCounts.Add(requestCount);
                                    }
                                    SRCountListRecieved = serviceRequestCounts.AsEnumerable();
                                }
                            }
                        }
                        else
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByModelLocaleCRC", conn);
                                cmd.CommandType = CommandType.StoredProcedure;

                                // Add parameters
                                cmd.Parameters.AddWithValue("@CompanyJSON", companies);
                                cmd.Parameters.AddWithValue("@BranchJSON", branchs);
                                cmd.Parameters.AddWithValue("@Language_ID", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Language_ID);
                                cmd.Parameters.AddWithValue("@FrmDate", string.IsNullOrEmpty(frmdate) ? DBNull.Value : (object)DateTime.Parse(frmdate));
                                cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? DBNull.Value : (object)DateTime.Parse(todate));

                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount requestCount = new ServiceRequestCount
                                        {
                                            Type = reader["Type"].ToString() == Stype ? reader["Type"].ToString() : "No",
                                            Year = Convert.ToInt32(reader["Year"]),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString()
                                        };

                                        serviceRequestCounts.Add(requestCount);
                                    }
                                    SRCountListRecieved = serviceRequestCounts.AsEnumerable();
                                }
                            }
                        }
                    }

                }
                else if (Mode == 6)//Brand
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByBrandCRC", conn);
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@CompanyJSON", companies);
                        cmd.Parameters.AddWithValue("@BranchJSON", branchs);
                        cmd.Parameters.AddWithValue("@FrmDate", string.IsNullOrEmpty(frmdate) ? DBNull.Value : (object)DateTime.Parse(frmdate));
                        cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? DBNull.Value : (object)DateTime.Parse(todate));

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ServiceRequestCount requestCount = new ServiceRequestCount
                                {
                                    Type = Selection.SelectedIDs.Where(s => s.Name == Stype).FirstOrDefault(s => s.ID.ToString() == reader["Brand_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    StatusID = Convert.ToInt32(reader["StatusID"]),
                                    StatusName = reader["StatusName"].ToString()
                                };

                                serviceRequestCounts.Add(requestCount);
                            }
                            SRCountListRecieved = serviceRequestCounts.AsEnumerable();
                        }
                    }
                }
                else if (Mode == 7)//Product Type
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByProductTypeCRC", conn);
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@CompanyJSON", companies);
                        cmd.Parameters.AddWithValue("@BranchJSON", branchs);
                        cmd.Parameters.AddWithValue("@FrmDate", string.IsNullOrEmpty(frmdate) ? DBNull.Value : (object)DateTime.Parse(frmdate));
                        cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? DBNull.Value : (object)DateTime.Parse(todate));

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ServiceRequestCount requestCount = new ServiceRequestCount
                                {
                                    Type = Selection.SelectedIDs.Where(s => s.Name == Stype).FirstOrDefault(s => s.ID.ToString() == reader["ProductType_ID"].ToString())?.Name ?? "No Name",
                                    Year = Convert.ToInt32(reader["Year"]),
                                    StatusID = Convert.ToInt32(reader["StatusID"]),
                                    StatusName = reader["StatusName"].ToString()
                                };

                                serviceRequestCounts.Add(requestCount);
                            }
                            SRCountListRecieved = serviceRequestCounts.AsEnumerable();
                        }
                    }

                }
                else if (Mode == 8)
                {
                    if (Stype == "Domestic")
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByPartyCRC", conn);
                            cmd.CommandType = CommandType.StoredProcedure;

                            // Add parameters
                            cmd.Parameters.AddWithValue("@CompanyJSON", companies);
                            cmd.Parameters.AddWithValue("@BranchJSON", branchs);
                            cmd.Parameters.AddWithValue("@FrmDate", string.IsNullOrEmpty(frmdate) ? DBNull.Value : (object)DateTime.Parse(frmdate));
                            cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? DBNull.Value : (object)DateTime.Parse(todate));

                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount requestCount = new ServiceRequestCount
                                    {
                                        Type = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "Domestic").ToString(),
                                        Year = Convert.ToInt32(reader["Year"]),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString()
                                    };

                                    serviceRequestCounts.Add(requestCount);
                                }
                                SRCountListRecieved = serviceRequestCounts.AsEnumerable();
                            }
                        }
                    }
                    else
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByInternationalPartyCRC", conn);
                            cmd.CommandType = CommandType.StoredProcedure;

                            // Add parameters
                            cmd.Parameters.AddWithValue("@CompanyJSON", companies);
                            cmd.Parameters.AddWithValue("@BranchJSON", branchs);
                            cmd.Parameters.AddWithValue("@FrmDate", string.IsNullOrEmpty(frmdate) ? DBNull.Value : (object)DateTime.Parse(frmdate));
                            cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? DBNull.Value : (object)DateTime.Parse(todate));

                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount requestCount = new ServiceRequestCount
                                    {
                                        Type = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "International").ToString(),
                                        Year = Convert.ToInt32(reader["Year"]),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString()
                                    };

                                    serviceRequestCounts.Add(requestCount);
                                }
                                SRCountListRecieved = serviceRequestCounts.AsEnumerable();
                            }
                        }
                    }
                }

                SRCountListStatus = (from SRequest in SRCountListRecieved
                                     group SRequest by new { SRequest.Year, SRequest.Type } into final
                                     select new ServiceRequestCount()
                                     {
                                         Type = final.FirstOrDefault().Type,
                                         Count = final.Count(),
                                         Year = final.FirstOrDefault().Year,
                                         StatusIDs = final.Select(a => a.StatusID).ToList()
                                     });


                SRCountListCompleted = (from SRequest in SRCountListStatus

                                        select new ServiceRequestCount()
                                        {
                                            Year = SRequest.Year,
                                            Type = SRequest.Type,
                                            StatusName = SRequest.StatusName,
                                            Count = (from sr in SRequest.StatusIDs
                                                     where sr == CompletedID
                                                     select sr
                                                   ).Count()

                                        });



                SRCountListOnHold = (from SRequest in SRCountListStatus

                                     select new ServiceRequestCount()
                                     {
                                         Year = SRequest.Year,
                                         Type = SRequest.Type,
                                         StatusName = SRequest.StatusName,
                                         Count = (from sr in SRequest.StatusIDs
                                                  where sr == HoldID
                                                  select sr
                                                ).Count()

                                     });



                SRCountListInProgress = (from SRequest in SRCountListStatus

                                         select new ServiceRequestCount()
                                         {
                                             Year = SRequest.Year,
                                             Type = SRequest.Type,
                                             StatusName = SRequest.StatusName,
                                             Count = (from sr in SRequest.StatusIDs
                                                      where sr == InProgressID
                                                      select sr
                                                    ).Count()

                                         });


                SRCountListClosed = (from SRequest in SRCountListStatus

                                     select new ServiceRequestCount()
                                     {
                                         Year = SRequest.Year,
                                         Type = SRequest.Type,
                                         StatusName = SRequest.StatusName,
                                         Count = (from sr in SRequest.StatusIDs
                                                  where sr == ClosedID
                                                  select sr
                                                ).Count()

                                     });
                SRCountListEscalated = (from SRequest in SRCountListStatus
                                        select new ServiceRequestCount()
                                        {
                                            Year = SRequest.Year,
                                            Type = SRequest.Type,
                                            StatusName = SRequest.StatusName,
                                            Count = (from sr in SRequest.StatusIDs
                                                     where sr == EscalatedID
                                                     select sr
                                                   ).Count()
                                        });



                FinalResultList = (from srFinal in SRCountListStatus
                                   join Completed in SRCountListCompleted on new { srFinal.Year, srFinal.Type } equals new { Completed.Year, Completed.Type } into FC
                                   from FinComp in FC.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                   join hold in SRCountListOnHold on new { srFinal.Year, srFinal.Type } equals new { hold.Year, hold.Type } into HO
                                   from FinHO in HO.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                   join Progress in SRCountListInProgress on new { srFinal.Year, srFinal.Type } equals new { Progress.Year, Progress.Type } into IP
                                   from FINIP in IP.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                   join closed in SRCountListClosed on new { srFinal.Year, srFinal.Type } equals new { closed.Year, closed.Type } into CL
                                   from FINCL in CL.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                       //Added by Harish on 8-Oct-2014
                                   join escalated in SRCountListEscalated on new { srFinal.Year, srFinal.Type } equals new { escalated.Year, escalated.Type } into ES
                                   from FINES in ES.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                   select new ServiceRequestCount()
                                   {
                                       ClosedCount = FINCL.Count,
                                       CompletedCount = FinComp.Count,
                                       Count = srFinal.Count,
                                       InProgressCount = FINIP.Count,
                                       OnHoldCount = FinHO.Count,
                                       EscalatedCount = FINES.Count,//Added by Harish on 8-Oct-2014
                                       Type = srFinal.Type,
                                       Year = srFinal.Year
                                   }).ToList();

                FinalResultSort = FinalResultList.AsQueryable().OrderByField<ServiceRequestCount>(sidx, sord);

                count = SRCountListStatus.Count();

                for (int i = 0; i < FinalResultList.Count; i++)
                {
                    TotalRecievedCount += FinalResultList.ElementAt(i).Count;
                    TotalCompletedCount += FinalResultList.ElementAt(i).CompletedCount;
                    TotalInProgressCount += FinalResultList.ElementAt(i).InProgressCount;
                    TotalOnHoldCount += FinalResultList.ElementAt(i).OnHoldCount;
                    TotalClosedCount += FinalResultList.ElementAt(i).ClosedCount;
                    TotalEscalatedcount += FinalResultList.ElementAt(i).EscalatedCount;//Added by Harish on 8-Oct-2014
                }

                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                jsonobj = new
                {
                    TotalPages = total,
                    PageNo = page,
                    RecordCount = count,
                    TotalRecievedCount = TotalRecievedCount,
                    TotalCompletedCount = TotalCompletedCount,
                    TotalInProgressCount = TotalInProgressCount,
                    TotalOnHoldCount = TotalOnHoldCount,
                    TotalClosedCount = TotalClosedCount,
                    TotalEscalatedcount = TotalEscalatedcount,//Added by Harish on 8-Oct-2014
                    YearTypeData = FinalResultSort.ToList().Paginate(page, rows)
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonobj);
        }
        #endregion




        #region ::: Select Service Request Count Month wise Uday Kumar J B 22-11-2024:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        //-- Modified by Venkateshwari for HelpDesk CR-5 Changes 18-Aug-2015 -- Start
        //

        public static IActionResult SelectMonthReport(HelpDesk_Tr_CaseRegistrationCountSelectYearReportList HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonobj = default(dynamic);
            int count = 0;
            int total = 0;
            IEnumerable<ServiceRequestCount> SRCountList = null;
            int userid = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.User_ID;
            IEnumerable<ServiceRequestCount> SRCountListCompleted = null;
            IEnumerable<ServiceRequestCount> SRCountListInProgress = null;
            IEnumerable<ServiceRequestCount> SRCountListOnHold = null;
            IEnumerable<ServiceRequestCount> SRCountListClosed = null;
            IEnumerable<ServiceRequestCount> SRCountListEscalated = null;
            IEnumerable<ServiceRequestCount> SRCountListStatus = null;
            List<ServiceRequestCount> FinalResultList = new List<ServiceRequestCount>();
            IQueryable<ServiceRequestCount> FinalResultSort = null;
            ServiceRequestCount FinalResult = new ServiceRequestCount();
            string GenLangCode = string.Empty;
            string UserLangCode = string.Empty;
            GetStatusIDs(connString, LogException);
            try
            {
                GenLangCode = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.GeneralLanguageCode.ToString();
                UserLangCode = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserLanguageCode.ToString();
                string frmdate = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.FromDate.ToString();//Request.Params["FromDate"];
                string todate = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.ToDate.ToString();//Request.Params["ToDate"];
                SelectionCriteria Selection = JObject.Parse(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Data).ToObject<SelectionCriteria>();
                string Stype = Common.DecryptString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Type);
                string CompanyIds = string.Empty;
                Top_Company companies = new Top_Company();
                if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Company != null && HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Company != "")
                {
                    companies = JObject.Parse(Common.DecryptString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Company)).ToObject<Top_Company>();

                    for (int i = 0; i < companies.Companies.Count; i++)
                    {
                        CompanyIds = CompanyIds + companies.Companies[i].ID + ", ";
                    }
                    CompanyIds = CompanyIds.Remove(CompanyIds.LastIndexOf(','), 1);
                }

                string BranchIds = string.Empty;
                RTS_Branch branchs = new RTS_Branch();
                if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Branch != null && HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Branch != "")
                {
                    branchs = JObject.Parse(Common.DecryptString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Branch)).ToObject<RTS_Branch>();

                    for (int i = 0; i < branchs.Branchs.Count; i++)
                    {
                        BranchIds = BranchIds + branchs.Branchs[i].ID + ", ";
                    }
                    BranchIds = BranchIds.Remove(BranchIds.LastIndexOf(','), 1);
                }

                var strCompanyIds = CompanyIds.Split(',');
                var strBranchIds = BranchIds.Split(',');

                IEnumerable<ServiceRequestCount> FinalResultListTemp = null;
                IEnumerable<ServiceRequestCount> SRCountListStatusTemp = null;
                int Mode = Convert.ToInt32(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Mode);


                foreach (var s in Selection.SelectedIDs)
                {
                    s.Name = Common.DecryptString(s.Name);
                }

                List<ServiceRequestCount> serviceRequestCounts = new List<ServiceRequestCount>();

                if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.SelectedCriteria == 1)//Call type
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsSelectMonthReport", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                            cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                            cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));
                            cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);


                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount SRCount = new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.Where(s => s.Name == Stype).FirstOrDefault(s => s.ID.ToString() == reader["IssueArea_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString(),
                                    };
                                    serviceRequestCounts.Add(SRCount);
                                }
                                SRCountList = serviceRequestCounts.AsEnumerable();
                            }
                        }
                    }
                }
                else if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.SelectedCriteria == 2)//Call Nature
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_ByComplexitySelectMonthReport", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                            cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                            cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));
                            cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);


                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount SRCount = new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.Where(s => s.Name == Stype).FirstOrDefault(s => s.ID.ToString() == reader["CallComplexity_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString(),
                                    };
                                    serviceRequestCounts.Add(SRCount);
                                }
                                SRCountList = serviceRequestCounts.AsEnumerable();
                            }
                        }
                    }
                }
                else if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.SelectedCriteria == 3)//Party
                {
                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_ByPartySelectMonthReport", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                                cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                                cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));
                                cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);


                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount SRCount = new ServiceRequestCount
                                        {
                                            Type = Selection.SelectedIDs.Where(s => s.Name == Stype).FirstOrDefault(s => s.ID.ToString() == reader["Party_ID"].ToString())?.Name ?? "No Name",
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString(),
                                        };
                                        serviceRequestCounts.Add(SRCount);
                                    }
                                    SRCountList = serviceRequestCounts.AsEnumerable();
                                }
                            }
                        }
                    }
                    else
                    {

                        if (GenLangCode == UserLangCode)
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_ByPartyDetailsSelectMonthReport", conn))
                                {
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                                    cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                                    cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                    cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));
                                    cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);


                                    conn.Open();

                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            ServiceRequestCount SRCount = new ServiceRequestCount
                                            {
                                                Type = Stype == reader["Type"]?.ToString() ? reader["Type"]?.ToString() : "No Name",
                                                Year = Convert.ToInt32(reader["Year"]),
                                                Month = Convert.ToInt32(reader["Month"]),
                                                StatusID = Convert.ToInt32(reader["StatusID"]),
                                                StatusName = reader["StatusName"].ToString(),
                                            };
                                            serviceRequestCounts.Add(SRCount);
                                        }
                                        SRCountList = serviceRequestCounts.AsEnumerable();
                                    }
                                }
                            }
                        }
                        else
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_ByPartyLocaleSelectMonthReport", conn))
                                {
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                                    cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                                    cmd.Parameters.AddWithValue("@LanguageID", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Language_ID);
                                    cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                    cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));
                                    cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);


                                    conn.Open();

                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            ServiceRequestCount SRCount = new ServiceRequestCount
                                            {
                                                Type = Stype == reader["Type"]?.ToString() ? reader["Type"]?.ToString() : "No Name",
                                                Year = Convert.ToInt32(reader["Year"]),
                                                Month = Convert.ToInt32(reader["Month"]),
                                                StatusID = Convert.ToInt32(reader["StatusID"]),
                                                StatusName = reader["StatusName"].ToString(),
                                            };
                                            serviceRequestCounts.Add(SRCount);
                                        }
                                        SRCountList = serviceRequestCounts.AsEnumerable();
                                    }
                                }
                            }
                        }
                    }
                }

                else if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.SelectedCriteria == 4)//Priority
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_ByCallPrioritySelectMonthReport", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                            cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                            cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));
                            cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);


                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount SRCount = new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.Where(s => s.Name == Stype).FirstOrDefault(s => s.ID.ToString() == reader["CallPriority_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString(),
                                    };
                                    serviceRequestCounts.Add(SRCount);
                                }
                                SRCountList = serviceRequestCounts.AsEnumerable();
                            }
                        }
                    }
                }
                else if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.SelectedCriteria == 5)//Model
                {
                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_ByModelSelectMonthReport", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                                cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                                cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));
                                cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);


                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount SRCount = new ServiceRequestCount
                                        {
                                            Type = Selection.SelectedIDs.Where(s => s.Name == Stype).FirstOrDefault(s => s.ID.ToString() == reader["Model_ID"].ToString())?.Name ?? "No Name",
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString(),
                                        };
                                        serviceRequestCounts.Add(SRCount);
                                    }
                                    SRCountList = serviceRequestCounts.AsEnumerable();
                                }
                            }
                        }
                    }
                    else
                    {
                        if (GenLangCode == UserLangCode)
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_ByModelNameSelectMonthReport", conn))
                                {
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                                    cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                                    cmd.Parameters.AddWithValue("@LanguageID", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Language_ID);
                                    cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                    cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));
                                    cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);


                                    conn.Open();

                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            ServiceRequestCount SRCount = new ServiceRequestCount
                                            {
                                                Type = Stype == reader["Type"]?.ToString() ? reader["Type"]?.ToString() : "No Name",
                                                Year = Convert.ToInt32(reader["Year"]),
                                                Month = Convert.ToInt32(reader["Month"]),
                                                StatusID = Convert.ToInt32(reader["StatusID"]),
                                                StatusName = reader["StatusName"].ToString(),
                                            };
                                            serviceRequestCounts.Add(SRCount);
                                        }
                                        SRCountList = serviceRequestCounts.AsEnumerable();
                                    }
                                }
                            }
                        }
                        else
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_ByModelLocaleNameSelectMonthReport", conn))
                                {
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                                    cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                                    cmd.Parameters.AddWithValue("@LanguageID", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Language_ID);
                                    cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                    cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));
                                    cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);


                                    conn.Open();

                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            ServiceRequestCount SRCount = new ServiceRequestCount
                                            {
                                                Type = Stype == reader["Type"]?.ToString() ? reader["Type"]?.ToString() : "No Name",
                                                Year = Convert.ToInt32(reader["Year"]),
                                                Month = Convert.ToInt32(reader["Month"]),
                                                StatusID = Convert.ToInt32(reader["StatusID"]),
                                                StatusName = reader["StatusName"].ToString(),
                                            };
                                            serviceRequestCounts.Add(SRCount);
                                        }
                                        SRCountList = serviceRequestCounts.AsEnumerable();
                                    }
                                }
                            }
                        }
                    }

                }

                else if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.SelectedCriteria == 6)//Brand
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_ByBrandNameSelectMonthReport", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                            cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                            cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));
                            cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);


                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount SRCount = new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.Where(s => s.Name == Stype).FirstOrDefault(s => s.ID.ToString() == reader["Brand_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString(),
                                    };
                                    serviceRequestCounts.Add(SRCount);
                                }
                                SRCountList = serviceRequestCounts.AsEnumerable();
                            }
                        }
                    }
                }

                else if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.SelectedCriteria == 7)//Product Type
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_ByProductTypeSelectMonthReport", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                            cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                            cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));
                            cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);


                            conn.Open();

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount SRCount = new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.Where(s => s.Name == Stype).FirstOrDefault(s => s.ID.ToString() == reader["ProductType_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString(),
                                    };
                                    serviceRequestCounts.Add(SRCount);
                                }
                                SRCountList = serviceRequestCounts.AsEnumerable();
                            }
                        }
                    }
                }
                else if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.SelectedCriteria == 8)
                {
                    if (Stype == "Domestic")
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_ByPartyTypeSelectMonthReport", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                                cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                                cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));
                                cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);


                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount SRCount = new ServiceRequestCount
                                        {
                                            Type = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "Domestic").ToString(),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString(),
                                        };
                                        serviceRequestCounts.Add(SRCount);
                                    }
                                    SRCountList = serviceRequestCounts.AsEnumerable();
                                }
                            }
                        }
                    }
                    else
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCounts_ByInternationalPartyTypeSelectMonthReport", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                                cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                                cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));
                                cmd.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);


                                conn.Open();

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount SRCount = new ServiceRequestCount
                                        {
                                            Type = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "International").ToString(),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString(),
                                        };
                                        serviceRequestCounts.Add(SRCount);
                                    }
                                    SRCountList = serviceRequestCounts.AsEnumerable();
                                }
                            }
                        }
                    }
                }
                SRCountListStatus = (from SRequest in SRCountList
                                     group SRequest by new { SRequest.Year, SRequest.Month, SRequest.Type } into final
                                     select new ServiceRequestCount()
                                     {
                                         Type = final.FirstOrDefault().Type,
                                         Count = final.Count(),
                                         Year = final.FirstOrDefault().Year,
                                         StatusIDs = final.Select(a => a.StatusID).ToList(),
                                         Month = final.FirstOrDefault().Month,
                                     });


                SRCountListCompleted = (from SRequest in SRCountListStatus

                                        select new ServiceRequestCount()
                                        {
                                            Year = SRequest.Year,
                                            Type = SRequest.Type,
                                            StatusName = SRequest.StatusName,
                                            Month = SRequest.Month,
                                            Count = (from sr in SRequest.StatusIDs
                                                     where sr == CompletedID
                                                     select sr
                                                   ).Count()

                                        });

                SRCountListOnHold = (from SRequest in SRCountListStatus

                                     select new ServiceRequestCount()
                                     {
                                         Year = SRequest.Year,
                                         Type = SRequest.Type,
                                         StatusName = SRequest.StatusName,
                                         Month = SRequest.Month,
                                         Count = (from sr in SRequest.StatusIDs
                                                  where sr == HoldID
                                                  select sr
                                                ).Count()

                                     });

                SRCountListInProgress = (from SRequest in SRCountListStatus

                                         select new ServiceRequestCount()
                                         {
                                             Year = SRequest.Year,
                                             Type = SRequest.Type,
                                             StatusName = SRequest.StatusName,
                                             Month = SRequest.Month,
                                             Count = (from sr in SRequest.StatusIDs
                                                      where sr == InProgressID
                                                      select sr
                                                    ).Count()

                                         });

                SRCountListClosed = (from SRequest in SRCountListStatus
                                     select new ServiceRequestCount()
                                     {
                                         Year = SRequest.Year,
                                         Type = SRequest.Type,
                                         StatusName = SRequest.StatusName,
                                         Month = SRequest.Month,
                                         Count = (from sr in SRequest.StatusIDs
                                                  where sr == ClosedID
                                                  select sr
                                                ).Count()
                                     });
                SRCountListEscalated = (from SRequest in SRCountListStatus
                                        select new ServiceRequestCount()
                                        {
                                            Year = SRequest.Year,
                                            Type = SRequest.Type,
                                            StatusName = SRequest.StatusName,
                                            Month = SRequest.Month,
                                            Count = (from sr in SRequest.StatusIDs
                                                     where sr == EscalatedID
                                                     select sr
                                                   ).Count()
                                        });

                FinalResultList = (from srFinal in SRCountListStatus
                                   join Completed in SRCountListCompleted on new { srFinal.Year, srFinal.Type, srFinal.Month } equals new { Completed.Year, Completed.Type, Completed.Month } into FC
                                   from FinComp in FC.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                   join hold in SRCountListOnHold on new { srFinal.Year, srFinal.Type, srFinal.Month } equals new { hold.Year, hold.Type, hold.Month } into HO
                                   from FinHO in HO.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                   join Progress in SRCountListInProgress on new { srFinal.Year, srFinal.Type, srFinal.Month } equals new { Progress.Year, Progress.Type, Progress.Month } into IP
                                   from FINIP in IP.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                   join closed in SRCountListClosed on new { srFinal.Year, srFinal.Type, srFinal.Month } equals new { closed.Year, closed.Type, closed.Month } into CL
                                   from FINCL in CL.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                       //Added by Harish on 8-Oct-2014
                                   join escalated in SRCountListEscalated on new { srFinal.Year, srFinal.Type, srFinal.Month } equals new { escalated.Year, escalated.Type, escalated.Month } into ES
                                   from FINES in ES.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                   select new ServiceRequestCount()
                                   {
                                       ClosedCount = FINCL.Count,
                                       CompletedCount = FinComp.Count,
                                       Count = srFinal.Count,
                                       InProgressCount = FINIP.Count,
                                       OnHoldCount = FinHO.Count,
                                       EscalatedCount = FINES.Count,//Added by Harish on 8-Oct-2014
                                       Type = srFinal.Type,
                                       MonthName = GetMonthName(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj, srFinal.Month),
                                       Month = srFinal.Month,
                                       Year = srFinal.Year
                                   }).ToList();

                FinalResultSort = FinalResultList.AsQueryable().OrderByField<ServiceRequestCount>(sidx, sord);
                count = SRCountListStatus.Count();


                for (int i = 0; i < FinalResultList.Count; i++)
                {
                    TotalRecievedCount += FinalResultList.ElementAt(i).Count;
                    TotalCompletedCount += FinalResultList.ElementAt(i).CompletedCount;
                    TotalInProgressCount += FinalResultList.ElementAt(i).InProgressCount;
                    TotalOnHoldCount += FinalResultList.ElementAt(i).OnHoldCount;
                    TotalClosedCount += FinalResultList.ElementAt(i).ClosedCount;
                    TotalEscalatedcount += FinalResultList.ElementAt(i).EscalatedCount;//Added by Harish on 8-Oct-2014
                }

                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                jsonobj = new
                {
                    TotalPages = total,
                    page = page,
                    records = count,
                    TotalRecievedCount = TotalRecievedCount,
                    TotalCompletedCount = TotalCompletedCount,
                    TotalInProgressCount = TotalInProgressCount,
                    TotalOnHoldCount = TotalOnHoldCount,
                    TotalClosedCount = TotalClosedCount,
                    TotalEscalatedcount = TotalEscalatedcount,//Added by Harish on 8-Oct-2014
                    MonthTypeData = FinalResultSort.ToList().Paginate(page, rows)
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonobj);
        }
        #endregion



        #region ::: GetMonthName Uday Kumar J B 22-11-2024:::
        /// <summary>
        /// To Select Month Name based on Culture
        /// </summary>
        public static string GetMonthName(HelpDesk_Tr_CaseRegistrationCountSelectYearReportList HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj, int ID)
        {
            string MonthName = string.Empty;
            try
            {
                switch (ID)
                {
                    case 1:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "January").ToString();
                        break;
                    case 2:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "February").ToString();
                        break;
                    case 3:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "March").ToString();
                        break;
                    case 4:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "April").ToString();
                        break;
                    case 5:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "May").ToString();
                        break;
                    case 6:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "June").ToString();
                        break;
                    case 7:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "July").ToString();
                        break;
                    case 8:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "August").ToString();
                        break;
                    case 9:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "September").ToString();
                        break;
                    case 10:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "October").ToString();
                        break;
                    case 11:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "November").ToString();
                        break;
                    case 12:
                        MonthName = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "December").ToString();
                        break;
                    default:
                        MonthName = string.Empty;
                        break;
                }
            }
            catch (Exception ex)
            {

            }
            return MonthName;
        }
        #endregion


        #region ::: Select Service Request Count Day wise Uday Kumar J B 22-11-2024:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        //-- Modified by Venkateshwari for HelpDesk CR-5 Changes 18-Aug-2015 -- Start

        public static IActionResult SelectDayReport(HelpDesk_Tr_CaseRegistrationCountSelectYearReportList HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonobj = default(dynamic);
            int count = 0;
            int total = 0;
            IEnumerable<ServiceRequestCount> SRCountList = null;
            int userid = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.User_ID;
            IEnumerable<ServiceRequestCount> SRCountListCompleted = null;
            IEnumerable<ServiceRequestCount> SRCountListInProgress = null;
            IEnumerable<ServiceRequestCount> SRCountListOnHold = null;
            IEnumerable<ServiceRequestCount> SRCountListClosed = null;
            IEnumerable<ServiceRequestCount> SRCountListEscalated = null;//Added by Harish on 8-Oct-2014
            List<ServiceRequestCount> FinalResultList = new List<ServiceRequestCount>();
            IQueryable<ServiceRequestCount> FinalResultSort = null;
            IEnumerable<ServiceRequestCount> SRCountListStatus = null;
            string GenLangCode = string.Empty;
            string UserLangCode = string.Empty;
            GetStatusIDs(connString, LogException);
            try
            {
                GenLangCode = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.GeneralLanguageCode.ToString();
                UserLangCode = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserLanguageCode.ToString();
                string frmdate = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.FromDate.ToString();
                string todate = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.ToDate.ToString();
                string Stype = Common.DecryptString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Type);
                string startDate = string.Empty;
                string endDate = string.Empty;
                string BranchName = string.Empty;
                string SelectionCriteria = string.Empty;
                SelectionCriteria Selection = JObject.Parse(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Data).ToObject<SelectionCriteria>();

                string CompanyIds = string.Empty;
                Top_Company companies = new Top_Company();
                if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Company != null && HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Company != "")
                {
                    companies = JObject.Parse(Common.DecryptString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Company)).ToObject<Top_Company>();

                    for (int i = 0; i < companies.Companies.Count; i++)
                    {
                        CompanyIds = CompanyIds + companies.Companies[i].ID + ", ";
                    }
                    CompanyIds = CompanyIds.Remove(CompanyIds.LastIndexOf(','), 1);
                }

                string BranchIds = string.Empty;
                Top_Branch branchs = new Top_Branch();
                if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Branch != null && HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Branch != "")
                {
                    branchs = JObject.Parse(Common.DecryptString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Branch)).ToObject<Top_Branch>();

                    for (int i = 0; i < branchs.Branchs.Count; i++)
                    {
                        BranchIds = BranchIds + branchs.Branchs[i].ID + ", ";
                    }
                    BranchIds = BranchIds.Remove(BranchIds.LastIndexOf(','), 1);
                }

                var strCompanyIds = CompanyIds.Split(',');
                var strBranchIds = BranchIds.Split(',');

                foreach (var s in Selection.SelectedIDs)
                {
                    s.Name = Common.DecryptString(s.Name);
                }

                List<ServiceRequestCount> serviceRequestCounts = new List<ServiceRequestCount>();

                if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.SelectedCriteria == 1)//Call type
                {
                    SelectionCriteria = "Issue Area";

                    using (SqlConnection connection = new SqlConnection(connString))
                    {
                        using (SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsSelectDayReport", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;

                            // Add parameters
                            command.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);
                            command.Parameters.AddWithValue("@Month", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Month);
                            command.Parameters.AddWithValue("@CompanyIDs", CompanyIds);
                            command.Parameters.AddWithValue("@BranchIDs", BranchIds);
                            command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : DateTime.Parse(frmdate));
                            command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : DateTime.Parse(todate).AddDays(1));


                            connection.Open();
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount SRcount = new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.Where(s => s.Name == Stype).FirstOrDefault(s => s.ID.ToString() == reader["IssueArea_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        Date = reader["Date"].ToString(),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString()
                                    };
                                    serviceRequestCounts.Add(SRcount);
                                }
                                SRCountList = serviceRequestCounts.AsEnumerable();
                            }
                        }
                    }
                }

                else if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.SelectedCriteria == 2)//Call Nature
                {
                    SelectionCriteria = "Call Complexity";
                    using (SqlConnection connection = new SqlConnection(connString))
                    {
                        using (SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByComplexitySelectDayReport", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;

                            // Add parameters
                            command.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);
                            command.Parameters.AddWithValue("@Month", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Month);
                            command.Parameters.AddWithValue("@CompanyIDs", CompanyIds);
                            command.Parameters.AddWithValue("@BranchIDs", BranchIds);
                            command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : DateTime.Parse(frmdate));
                            command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : DateTime.Parse(todate).AddDays(1));


                            connection.Open();
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount SRcount = new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.Where(s => s.Name == Stype).FirstOrDefault(s => s.ID.ToString() == reader["CallComplexity_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        Date = reader["Date"].ToString(),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString()
                                    };
                                    serviceRequestCounts.Add(SRcount);
                                }
                                SRCountList = serviceRequestCounts.AsEnumerable();
                            }
                        }
                    }
                }

                else if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.SelectedCriteria == 3)//Party
                {
                    SelectionCriteria = "Party";

                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (SqlConnection connection = new SqlConnection(connString))
                        {
                            using (SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByPartySelectDayReport", connection))
                            {
                                command.CommandType = CommandType.StoredProcedure;

                                // Add parameters
                                command.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);
                                command.Parameters.AddWithValue("@Month", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Month);
                                command.Parameters.AddWithValue("@CompanyIDs", CompanyIds);
                                command.Parameters.AddWithValue("@BranchIDs", BranchIds);
                                command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : DateTime.Parse(frmdate));
                                command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : DateTime.Parse(todate).AddDays(1));


                                connection.Open();
                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount SRcount = new ServiceRequestCount
                                        {
                                            Type = Selection.SelectedIDs.Where(s => s.Name == Stype).FirstOrDefault(s => s.ID.ToString() == reader["Party_ID"].ToString())?.Name ?? "No Name",
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            Date = reader["Date"].ToString(),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString()
                                        };
                                        serviceRequestCounts.Add(SRcount);
                                    }
                                    SRCountList = serviceRequestCounts.AsEnumerable();
                                }
                            }
                        }
                    }
                    else
                    {
                        if (GenLangCode == UserLangCode)
                        {
                            using (SqlConnection connection = new SqlConnection(connString))
                            {
                                using (SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByPartyDetailsSelectDayReport", connection))
                                {
                                    command.CommandType = CommandType.StoredProcedure;

                                    // Add parameters
                                    command.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);
                                    command.Parameters.AddWithValue("@Month", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Month);
                                    command.Parameters.AddWithValue("@CompanyIDs", CompanyIds);
                                    command.Parameters.AddWithValue("@BranchIDs", BranchIds);
                                    command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : DateTime.Parse(frmdate));
                                    command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : DateTime.Parse(todate).AddDays(1));


                                    connection.Open();
                                    using (SqlDataReader reader = command.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            ServiceRequestCount SRcount = new ServiceRequestCount
                                            {
                                                Type = Stype == reader["Type"]?.ToString() ? reader["Type"]?.ToString() : "No Name",
                                                Year = Convert.ToInt32(reader["Year"]),
                                                Month = Convert.ToInt32(reader["Month"]),
                                                Date = reader["Date"].ToString(),
                                                StatusID = Convert.ToInt32(reader["StatusID"]),
                                                StatusName = reader["StatusName"].ToString()
                                            };
                                            serviceRequestCounts.Add(SRcount);
                                        }
                                        SRCountList = serviceRequestCounts.AsEnumerable();
                                    }
                                }
                            }
                        }
                        else
                        {
                            using (SqlConnection connection = new SqlConnection(connString))
                            {
                                using (SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByLocaleSelectDayReport", connection))
                                {
                                    command.CommandType = CommandType.StoredProcedure;

                                    // Add parameters
                                    command.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);
                                    command.Parameters.AddWithValue("@Month", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Month);
                                    command.Parameters.AddWithValue("@CompanyIDs", CompanyIds);
                                    command.Parameters.AddWithValue("@BranchIDs", BranchIds);
                                    command.Parameters.AddWithValue("@LanguageID", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Language_ID);
                                    command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : DateTime.Parse(frmdate));
                                    command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : DateTime.Parse(todate).AddDays(1));


                                    connection.Open();
                                    using (SqlDataReader reader = command.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            ServiceRequestCount SRcount = new ServiceRequestCount
                                            {
                                                Type = Stype == reader["Type"]?.ToString() ? reader["Type"]?.ToString() : "No Name",
                                                Year = Convert.ToInt32(reader["Year"]),
                                                Month = Convert.ToInt32(reader["Month"]),
                                                Date = reader["Date"].ToString(),
                                                StatusID = Convert.ToInt32(reader["StatusID"]),
                                                StatusName = reader["StatusName"].ToString()
                                            };
                                            serviceRequestCounts.Add(SRcount);
                                        }
                                        SRCountList = serviceRequestCounts.AsEnumerable();
                                    }
                                }
                            }
                        }
                    }

                }

                else if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.SelectedCriteria == 4)//Priority
                {
                    SelectionCriteria = "Priority";

                    using (SqlConnection connection = new SqlConnection(connString))
                    {
                        using (SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByPrioritySelectDayReport", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;

                            // Add parameters
                            command.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);
                            command.Parameters.AddWithValue("@Month", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Month);
                            command.Parameters.AddWithValue("@CompanyIDs", CompanyIds);
                            command.Parameters.AddWithValue("@BranchIDs", BranchIds);
                            command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : DateTime.Parse(frmdate));
                            command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : DateTime.Parse(todate).AddDays(1));


                            connection.Open();
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount SRcount = new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.Where(s => s.Name == Stype).FirstOrDefault(s => s.ID.ToString() == reader["CallPriority_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        Date = reader["Date"].ToString(),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString()
                                    };
                                    serviceRequestCounts.Add(SRcount);
                                }
                                SRCountList = serviceRequestCounts.AsEnumerable();
                            }
                        }
                    }
                }

                else if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.SelectedCriteria == 5)//Model
                {
                    SelectionCriteria = "Model";

                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (SqlConnection connection = new SqlConnection(connString))
                        {
                            using (SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByModelSelectDayReport", connection))
                            {
                                command.CommandType = CommandType.StoredProcedure;

                                // Add parameters
                                command.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);
                                command.Parameters.AddWithValue("@Month", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Month);
                                command.Parameters.AddWithValue("@CompanyIDs", CompanyIds);
                                command.Parameters.AddWithValue("@BranchIDs", BranchIds);
                                command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : DateTime.Parse(frmdate));
                                command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : DateTime.Parse(todate).AddDays(1));


                                connection.Open();
                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount SRcount = new ServiceRequestCount
                                        {
                                            Type = Selection.SelectedIDs.Where(s => s.Name == Stype).FirstOrDefault(s => s.ID.ToString() == reader["Model_ID"].ToString())?.Name ?? "No Name",
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            Date = reader["Date"].ToString(),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString()
                                        };
                                        serviceRequestCounts.Add(SRcount);
                                    }
                                    SRCountList = serviceRequestCounts.AsEnumerable();
                                }
                            }
                        }
                    }
                    else
                    {

                        if (GenLangCode == UserLangCode)
                        {
                            using (SqlConnection connection = new SqlConnection(connString))
                            {
                                using (SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByModelLocalSelectDayReport", connection))
                                {
                                    command.CommandType = CommandType.StoredProcedure;

                                    // Add parameters
                                    command.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);
                                    command.Parameters.AddWithValue("@Month", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Month);
                                    command.Parameters.AddWithValue("@CompanyIDs", CompanyIds);
                                    command.Parameters.AddWithValue("@BranchIDs", BranchIds);
                                    command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : DateTime.Parse(frmdate));
                                    command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : DateTime.Parse(todate).AddDays(1));


                                    connection.Open();
                                    using (SqlDataReader reader = command.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            ServiceRequestCount SRcount = new ServiceRequestCount
                                            {
                                                Type = Selection.SelectedIDs.Where(s => s.Name == Stype).FirstOrDefault(s => s.ID.ToString() == reader["Model_ID"].ToString())?.Name ?? "No Name",
                                                Year = Convert.ToInt32(reader["Year"]),
                                                Month = Convert.ToInt32(reader["Month"]),
                                                Date = reader["Date"].ToString(),
                                                StatusID = Convert.ToInt32(reader["StatusID"]),
                                                StatusName = reader["StatusName"].ToString()
                                            };
                                            serviceRequestCounts.Add(SRcount);
                                        }
                                        SRCountList = serviceRequestCounts.AsEnumerable();
                                    }
                                }
                            }
                        }
                        else
                        {
                            using (SqlConnection connection = new SqlConnection(connString))
                            {
                                using (SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByModelLocaleSelectDayReport", connection))
                                {
                                    command.CommandType = CommandType.StoredProcedure;

                                    // Add parameters
                                    command.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);
                                    command.Parameters.AddWithValue("@Month", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Month);
                                    command.Parameters.AddWithValue("@CompanyIDs", CompanyIds);
                                    command.Parameters.AddWithValue("@BranchIDs", BranchIds);
                                    command.Parameters.AddWithValue("@LanguageID", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Language_ID);
                                    command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : DateTime.Parse(frmdate));
                                    command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : DateTime.Parse(todate).AddDays(1));


                                    connection.Open();
                                    using (SqlDataReader reader = command.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            ServiceRequestCount SRcount = new ServiceRequestCount
                                            {
                                                Type = Stype == reader["Type"]?.ToString() ? reader["Type"]?.ToString() : "No Name",
                                                Year = Convert.ToInt32(reader["Year"]),
                                                Month = Convert.ToInt32(reader["Month"]),
                                                Date = reader["Date"].ToString(),
                                                StatusID = Convert.ToInt32(reader["StatusID"]),
                                                StatusName = reader["StatusName"].ToString()
                                            };
                                            serviceRequestCounts.Add(SRcount);
                                        }
                                        SRCountList = serviceRequestCounts.AsEnumerable();
                                    }
                                }
                            }
                        }
                    }

                }

                else if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.SelectedCriteria == 6)//Brand
                {
                    SelectionCriteria = "Brand";
                    using (SqlConnection connection = new SqlConnection(connString))
                    {
                        using (SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByBrandSelectDayReport", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;

                            // Add parameters
                            command.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);
                            command.Parameters.AddWithValue("@Month", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Month);
                            command.Parameters.AddWithValue("@CompanyIDs", CompanyIds);
                            command.Parameters.AddWithValue("@BranchIDs", BranchIds);
                            command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : DateTime.Parse(frmdate));
                            command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : DateTime.Parse(todate).AddDays(1));


                            connection.Open();
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount SRcount = new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.Where(s => s.Name == Stype).FirstOrDefault(s => s.ID.ToString() == reader["Model_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        Date = reader["Date"].ToString(),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString()
                                    };
                                    serviceRequestCounts.Add(SRcount);
                                }
                                SRCountList = serviceRequestCounts.AsEnumerable();
                            }
                        }
                    }
                }
                else if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.SelectedCriteria == 7)//Product Type
                {
                    SelectionCriteria = "Product Type";

                    using (SqlConnection connection = new SqlConnection(connString))
                    {
                        using (SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByProductTypeSelectDayReport", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;

                            // Add parameters
                            command.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);
                            command.Parameters.AddWithValue("@Month", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Month);
                            command.Parameters.AddWithValue("@CompanyIDs", CompanyIds);
                            command.Parameters.AddWithValue("@BranchIDs", BranchIds);
                            command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : DateTime.Parse(frmdate));
                            command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : DateTime.Parse(todate).AddDays(1));


                            connection.Open();
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ServiceRequestCount SRcount = new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.Where(s => s.Name == Stype).FirstOrDefault(s => s.ID.ToString() == reader["ProductType_ID"].ToString())?.Name ?? "No Name",
                                        Year = Convert.ToInt32(reader["Year"]),
                                        Month = Convert.ToInt32(reader["Month"]),
                                        Date = reader["Date"].ToString(),
                                        StatusID = Convert.ToInt32(reader["StatusID"]),
                                        StatusName = reader["StatusName"].ToString()
                                    };
                                    serviceRequestCounts.Add(SRcount);
                                }
                                SRCountList = serviceRequestCounts.AsEnumerable();
                            }
                        }
                    }
                }
                else if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.SelectedCriteria == 8)
                {

                    if (Stype == "Domestic")
                    {
                        using (SqlConnection connection = new SqlConnection(connString))
                        {
                            using (SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByParty1SelectDayReport", connection))
                            {
                                command.CommandType = CommandType.StoredProcedure;

                                // Add parameters
                                command.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);
                                command.Parameters.AddWithValue("@Month", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Month);
                                command.Parameters.AddWithValue("@CompanyIDs", CompanyIds);
                                command.Parameters.AddWithValue("@BranchIDs", BranchIds);
                                command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : DateTime.Parse(frmdate));
                                command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : DateTime.Parse(todate).AddDays(1));


                                connection.Open();
                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount SRcount = new ServiceRequestCount
                                        {
                                            Type = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "Domestic").ToString(),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            Date = reader["Date"].ToString(),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString()
                                        };
                                        serviceRequestCounts.Add(SRcount);
                                    }
                                    SRCountList = serviceRequestCounts.AsEnumerable();
                                }
                            }
                        }
                    }
                    else
                    {
                        using (SqlConnection connection = new SqlConnection(connString))
                        {
                            using (SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByInternationalPartySelectDayReport", connection))
                            {
                                command.CommandType = CommandType.StoredProcedure;

                                // Add parameters
                                command.Parameters.AddWithValue("@Year", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Year);
                                command.Parameters.AddWithValue("@Month", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Month);
                                command.Parameters.AddWithValue("@CompanyIDs", CompanyIds);
                                command.Parameters.AddWithValue("@BranchIDs", BranchIds);
                                command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : DateTime.Parse(frmdate));
                                command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : DateTime.Parse(todate).AddDays(1));


                                connection.Open();
                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        ServiceRequestCount SRcount = new ServiceRequestCount
                                        {
                                            Type = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "International").ToString(),
                                            Year = Convert.ToInt32(reader["Year"]),
                                            Month = Convert.ToInt32(reader["Month"]),
                                            Date = reader["Date"].ToString(),
                                            StatusID = Convert.ToInt32(reader["StatusID"]),
                                            StatusName = reader["StatusName"].ToString()
                                        };
                                        serviceRequestCounts.Add(SRcount);
                                    }
                                    SRCountList = serviceRequestCounts.AsEnumerable();
                                }
                            }
                        }
                    }
                }

                SRCountListStatus = (from SRequest in SRCountList
                                     group SRequest by new { SRequest.Year, SRequest.Month, SRequest.Date, SRequest.Type } into final
                                     select new ServiceRequestCount()
                                     {
                                         Type = final.FirstOrDefault().Type,
                                         Count = final.Count(),
                                         Year = final.FirstOrDefault().Year,
                                         StatusIDs = final.Select(a => a.StatusID).ToList(),
                                         Month = final.FirstOrDefault().Month,
                                         Date = final.FirstOrDefault().Date
                                     });


                SRCountListCompleted = (from SRequest in SRCountListStatus

                                        select new ServiceRequestCount()
                                        {
                                            Year = SRequest.Year,
                                            Type = SRequest.Type,
                                            StatusName = SRequest.StatusName,
                                            Month = SRequest.Month,
                                            Date = SRequest.Date,
                                            Count = (from sr in SRequest.StatusIDs
                                                     where sr == CompletedID
                                                     select sr
                                                   ).Count()

                                        });

                SRCountListOnHold = (from SRequest in SRCountListStatus

                                     select new ServiceRequestCount()
                                     {
                                         Year = SRequest.Year,
                                         Type = SRequest.Type,
                                         StatusName = SRequest.StatusName,
                                         Month = SRequest.Month,
                                         Date = SRequest.Date,
                                         Count = (from sr in SRequest.StatusIDs
                                                  where sr == HoldID
                                                  select sr
                                                ).Count()

                                     });

                SRCountListInProgress = (from SRequest in SRCountListStatus

                                         select new ServiceRequestCount()
                                         {
                                             Year = SRequest.Year,
                                             Type = SRequest.Type,
                                             StatusName = SRequest.StatusName,
                                             Month = SRequest.Month,
                                             Date = SRequest.Date,
                                             Count = (from sr in SRequest.StatusIDs
                                                      where sr == InProgressID
                                                      select sr
                                                    ).Count()

                                         });

                SRCountListClosed = (from SRequest in SRCountListStatus
                                     select new ServiceRequestCount()
                                     {
                                         Year = SRequest.Year,
                                         Type = SRequest.Type,
                                         StatusName = SRequest.StatusName,
                                         Month = SRequest.Month,
                                         Date = SRequest.Date,
                                         Count = (from sr in SRequest.StatusIDs
                                                  where sr == ClosedID
                                                  select sr
                                                ).Count()
                                     });
                //Added by Harish on 8-Oct-2014
                SRCountListEscalated = (from SRequest in SRCountListStatus
                                        select new ServiceRequestCount()
                                        {
                                            Year = SRequest.Year,
                                            Type = SRequest.Type,
                                            StatusName = SRequest.StatusName,
                                            Month = SRequest.Month,
                                            Date = SRequest.Date,
                                            Count = (from sr in SRequest.StatusIDs
                                                     where sr == EscalatedID
                                                     select sr
                                                   ).Count()
                                        });

                FinalResultList = (from srFinal in SRCountListStatus
                                   join Completed in SRCountListCompleted on new { srFinal.Year, srFinal.Type, srFinal.Month, srFinal.Date } equals new { Completed.Year, Completed.Type, Completed.Month, Completed.Date } into FC
                                   from FinComp in FC.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                   join hold in SRCountListOnHold on new { srFinal.Year, srFinal.Type, srFinal.Month, srFinal.Date } equals new { hold.Year, hold.Type, hold.Month, hold.Date } into HO
                                   from FinHO in HO.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                   join Progress in SRCountListInProgress on new { srFinal.Year, srFinal.Type, srFinal.Month, srFinal.Date } equals new { Progress.Year, Progress.Type, Progress.Month, Progress.Date } into IP
                                   from FINIP in IP.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                   join closed in SRCountListClosed on new { srFinal.Year, srFinal.Type, srFinal.Month, srFinal.Date } equals new { closed.Year, closed.Type, closed.Month, closed.Date } into CL
                                   from FINCL in CL.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                       //Added by Harish on 8-Oct-2014
                                   join escalated in SRCountListEscalated on new { srFinal.Year, srFinal.Type, srFinal.Month, srFinal.Date } equals new { escalated.Year, escalated.Type, escalated.Month, escalated.Date } into ES
                                   from FINES in ES.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                   select new ServiceRequestCount()
                                   {
                                       ClosedCount = FINCL.Count,
                                       CompletedCount = FinComp.Count,
                                       Count = srFinal.Count,
                                       InProgressCount = FINIP.Count,
                                       OnHoldCount = FinHO.Count,
                                       EscalatedCount = FINES.Count,//Added by Harish on 8-Oct-2014
                                       Type = srFinal.Type,
                                       Year = srFinal.Year,
                                       Month = srFinal.Month,
                                       Date = Convert.ToDateTime(srFinal.Date).ToString("dd-MMM-yyyy"),
                                   }).ToList();


                FinalResultSort = FinalResultList.AsQueryable().OrderByField<ServiceRequestCount>(sidx, sord);

                count = SRCountListStatus.Count();

                for (int i = 0; i < FinalResultList.Count; i++)
                {
                    TotalRecievedCount += FinalResultList.ElementAt(i).Count;
                    TotalCompletedCount += FinalResultList.ElementAt(i).CompletedCount;
                    TotalInProgressCount += FinalResultList.ElementAt(i).InProgressCount;
                    TotalOnHoldCount += FinalResultList.ElementAt(i).OnHoldCount;
                    TotalClosedCount += FinalResultList.ElementAt(i).ClosedCount;
                    TotalEscalatedcount += FinalResultList.ElementAt(i).EscalatedCount;//Added by Harish on 8-Oct-2014
                }

                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                jsonobj = new
                {
                    total = total,
                    page = page,
                    records = count,
                    TotalRecievedCount = TotalRecievedCount,
                    TotalCompletedCount = TotalCompletedCount,
                    TotalInProgressCount = TotalInProgressCount,
                    TotalOnHoldCount = TotalOnHoldCount,
                    TotalClosedCount = TotalClosedCount,
                    TotalEscalatedcount = TotalEscalatedcount,//Added by Harish on 8-Oct-2014
                    DayTypeData = FinalResultSort.ToList().Paginate(page, rows)
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonobj);
        }
        #endregion




        #region ::: To Export Uday Kumar J B 22-11-2024:::
        /// <summary>
        /// To Export 
        /// </summary>
        /// <returns>...</returns>
        ///  

        public static async Task<object> Export(HelpDesk_Tr_CaseRegistrationCountSelectYearReportList HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj, string connString, int LogException, string filter, string advnceFilter, string sidx, string sord)
        {
            IEnumerable<ServiceRequestCount> SRCountList = null;
            int userid = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.User_ID;
            IEnumerable<ServiceRequestCount> SRCountListCompleted = null;
            IEnumerable<ServiceRequestCount> SRCountListRecieved = null;
            IEnumerable<ServiceRequestCount> SRCountListInProgress = null;
            IEnumerable<ServiceRequestCount> SRCountListOnHold = null;
            IEnumerable<ServiceRequestCount> SRCountListClosed = null;
            IEnumerable<ServiceRequestCount> SRCountListEscalated = null;//Added by Harish on 8-Oct-2014
            List<ServiceRequestCount> FinalResultList = new List<ServiceRequestCount>();
            IQueryable<ServiceRequestCount> FinalResultSort = null;
            IEnumerable<ServiceRequestCount> SRCountListStatus = null;
            string GenLangCode = string.Empty;
            int SelectedCriteria = Convert.ToInt32(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.SelectedCriteria);
            string UserLangCode = string.Empty;
            GetStatusIDs(connString, LogException);
            try
            {
                GenLangCode = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.GeneralLanguageCode.ToString();
                UserLangCode = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserLanguageCode.ToString();
                int userLanguageID = Convert.ToInt32(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.GeneralLanguageID);
                string frmdate = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.FromDate.ToString();
                string todate = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.ToDate.ToString();
                string startDate = string.Empty;
                string endDate = string.Empty;
                string BranchName = string.Empty;
                string SelectionCriteria = string.Empty;
                SelectionCriteria Selection = JObject.Parse(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Data).ToObject<SelectionCriteria>();
                foreach (var s in Selection.SelectedIDs)
                {
                    s.Name = Common.DecryptString(s.Name);
                }

                string CompanyIds = string.Empty;
                string CompanyNames = string.Empty;
                string SelectedCompanyNames = string.Empty;
                Top_Company companies = new Top_Company();
                if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Company != null && HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Company != "")
                {
                    companies = JObject.Parse(Common.DecryptString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Company)).ToObject<Top_Company>();

                    for (int i = 0; i < companies.Companies.Count; i++)
                    {
                        CompanyIds = CompanyIds + companies.Companies[i].ID + ", ";
                        CompanyNames = CompanyNames + companies.Companies[i].Name + ", ";
                    }
                    CompanyIds = CompanyIds.Remove(CompanyIds.LastIndexOf(','), 1);
                    CompanyNames = CompanyNames.Remove(CompanyNames.LastIndexOf(','), 1);
                }

                string BranchIds = string.Empty;
                string BranchNames = string.Empty;
                string SelectedBranchNames = string.Empty;
                Top_Branch branchs = new Top_Branch();
                if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Branch != null && HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Branch != "")
                {
                    branchs = JObject.Parse(Common.DecryptString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Branch)).ToObject<Top_Branch>();

                    for (int i = 0; i < branchs.Branchs.Count; i++)
                    {
                        BranchIds = BranchIds + branchs.Branchs[i].ID + ", ";
                        BranchNames = BranchNames + branchs.Branchs[i].Name + ", ";
                    }
                    BranchIds = BranchIds.Remove(BranchIds.LastIndexOf(','), 1);
                    BranchNames = BranchNames.Remove(BranchNames.LastIndexOf(','), 1);
                }

                List<ServiceRequestCount> serviceRequestCounts = new List<ServiceRequestCount>();

                if (SelectedCriteria == 1)//Call type
                {
                    SelectionCriteria = "Issue Area";

                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsCaseRegistrationExport", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;

                            cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                            cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                            cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                            conn.Open();
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    serviceRequestCounts.Add(new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader.GetString(reader.GetOrdinal("IssueArea_ID")).ToString())?.Name ?? "No Name",
                                        Year = reader.GetInt32(reader.GetOrdinal("Year")),
                                        Month = reader.GetInt32(reader.GetOrdinal("Month")),
                                        Date = reader.GetString(reader.GetOrdinal("Date")),
                                        StatusID = reader.GetInt32(reader.GetOrdinal("StatusID")),
                                        StatusName = reader.GetString(reader.GetOrdinal("StatusName")),
                                        CompanyID = reader.GetInt32(reader.GetOrdinal("CompanyID")),
                                        BranchID = reader.GetInt32(reader.GetOrdinal("BranchID")),
                                        CompanyName = reader.GetString(reader.GetOrdinal("CompanyName")),
                                        BranchName = reader.GetString(reader.GetOrdinal("BranchName"))
                                    });
                                }
                                SRCountList = serviceRequestCounts.AsEnumerable();
                            }
                        }
                    }
                }
                else if (SelectedCriteria == 2)//Call Nature
                {
                    SelectionCriteria = "Complexity";
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountCallComplexityCaseRegistrationExport", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;

                            cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                            cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                            cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                            conn.Open();
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    serviceRequestCounts.Add(new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader.GetString(reader.GetOrdinal("CallComplexity_ID")).ToString())?.Name ?? "No Name",
                                        Year = reader.GetInt32(reader.GetOrdinal("Year")),
                                        Month = reader.GetInt32(reader.GetOrdinal("Month")),
                                        Date = reader.GetString(reader.GetOrdinal("Date")),
                                        StatusID = reader.GetInt32(reader.GetOrdinal("StatusID")),
                                        StatusName = reader.GetString(reader.GetOrdinal("StatusName")),
                                        CompanyID = reader.GetInt32(reader.GetOrdinal("CompanyID")),
                                        BranchID = reader.GetInt32(reader.GetOrdinal("BranchID")),
                                        CompanyName = reader.GetString(reader.GetOrdinal("CompanyName")),
                                        BranchName = reader.GetString(reader.GetOrdinal("BranchName"))
                                    });
                                }
                                SRCountList = serviceRequestCounts.AsEnumerable();
                            }
                        }
                    }
                }
                else if (SelectedCriteria == 3)//Party
                {
                    SelectionCriteria = "Party";

                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountByPartyCaseRegistrationExport", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;

                                cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                                cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                                cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                                conn.Open();
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        serviceRequestCounts.Add(new ServiceRequestCount
                                        {
                                            Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader.GetString(reader.GetOrdinal("Party_ID")).ToString())?.Name ?? "No Name",
                                            Year = reader.GetInt32(reader.GetOrdinal("Year")),
                                            Month = reader.GetInt32(reader.GetOrdinal("Month")),
                                            Date = reader.GetString(reader.GetOrdinal("Date")),
                                            StatusID = reader.GetInt32(reader.GetOrdinal("StatusID")),
                                            StatusName = reader.GetString(reader.GetOrdinal("StatusName")),
                                            CompanyID = reader.GetInt32(reader.GetOrdinal("CompanyID")),
                                            BranchID = reader.GetInt32(reader.GetOrdinal("BranchID")),
                                            CompanyName = reader.GetString(reader.GetOrdinal("CompanyName")),
                                            BranchName = reader.GetString(reader.GetOrdinal("BranchName"))
                                        });
                                    }
                                    SRCountList = serviceRequestCounts.AsEnumerable();
                                }
                            }
                        }
                    }
                    else
                    {
                        if (GenLangCode == UserLangCode)
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountWithPartyCaseRegistrationExport", conn))
                                {
                                    cmd.CommandType = CommandType.StoredProcedure;

                                    cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                                    cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                                    cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                    cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                                    conn.Open();
                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            serviceRequestCounts.Add(new ServiceRequestCount
                                            {
                                                Type = reader.GetString(reader.GetOrdinal("Type")),
                                                Year = reader.GetInt32(reader.GetOrdinal("Year")),
                                                Month = reader.GetInt32(reader.GetOrdinal("Month")),
                                                Date = reader.GetString(reader.GetOrdinal("Date")),
                                                StatusID = reader.GetInt32(reader.GetOrdinal("StatusID")),
                                                StatusName = reader.GetString(reader.GetOrdinal("StatusName")),
                                                CompanyID = reader.GetInt32(reader.GetOrdinal("CompanyID")),
                                                BranchID = reader.GetInt32(reader.GetOrdinal("BranchID")),
                                                CompanyName = reader.GetString(reader.GetOrdinal("CompanyName")),
                                                BranchName = reader.GetString(reader.GetOrdinal("BranchName"))
                                            });
                                        }
                                        SRCountList = serviceRequestCounts.AsEnumerable();
                                    }
                                }
                            }
                        }
                        else
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountWithPartyLocaleCaseRegistrationExport", conn))
                                {
                                    cmd.CommandType = CommandType.StoredProcedure;

                                    cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                                    cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                                    cmd.Parameters.AddWithValue("@LanguageID", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Language_ID);
                                    cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                    cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                                    conn.Open();
                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            serviceRequestCounts.Add(new ServiceRequestCount
                                            {
                                                Type = reader.GetString(reader.GetOrdinal("Type")),
                                                Year = reader.GetInt32(reader.GetOrdinal("Year")),
                                                Month = reader.GetInt32(reader.GetOrdinal("Month")),
                                                Date = reader.GetString(reader.GetOrdinal("Date")),
                                                StatusID = reader.GetInt32(reader.GetOrdinal("StatusID")),
                                                StatusName = reader.GetString(reader.GetOrdinal("StatusName")),
                                                CompanyID = reader.GetInt32(reader.GetOrdinal("CompanyID")),
                                                BranchID = reader.GetInt32(reader.GetOrdinal("BranchID")),
                                                CompanyName = reader.GetString(reader.GetOrdinal("CompanyName")),
                                                BranchName = reader.GetString(reader.GetOrdinal("BranchName"))
                                            });
                                        }
                                        SRCountList = serviceRequestCounts.AsEnumerable();
                                    }
                                }
                            }
                        }
                    }

                }
                else if (SelectedCriteria == 4)//Priority
                {
                    SelectionCriteria = "Priority";

                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountByPriorityCaseRegistrationExport", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;

                            cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                            cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                            cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                            conn.Open();
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    serviceRequestCounts.Add(new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader.GetString(reader.GetOrdinal("CallPriority_ID")).ToString())?.Name ?? "No Name",
                                        Year = reader.GetInt32(reader.GetOrdinal("Year")),
                                        Month = reader.GetInt32(reader.GetOrdinal("Month")),
                                        Date = reader.GetString(reader.GetOrdinal("Date")),
                                        StatusID = reader.GetInt32(reader.GetOrdinal("StatusID")),
                                        StatusName = reader.GetString(reader.GetOrdinal("StatusName")),
                                        CompanyID = reader.GetInt32(reader.GetOrdinal("CompanyID")),
                                        BranchID = reader.GetInt32(reader.GetOrdinal("BranchID")),
                                        CompanyName = reader.GetString(reader.GetOrdinal("CompanyName")),
                                        BranchName = reader.GetString(reader.GetOrdinal("BranchName"))
                                    });
                                }
                                SRCountList = serviceRequestCounts.AsEnumerable();
                            }
                        }
                    }
                }
                else if (SelectedCriteria == 5)//Model
                {
                    SelectionCriteria = "Model";

                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountByModelCaseRegistrationExport", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;

                                cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                                cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                                cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                                conn.Open();
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        serviceRequestCounts.Add(new ServiceRequestCount
                                        {
                                            Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader.GetString(reader.GetOrdinal("Model_ID")).ToString())?.Name ?? "No Name",
                                            Year = reader.GetInt32(reader.GetOrdinal("Year")),
                                            Month = reader.GetInt32(reader.GetOrdinal("Month")),
                                            Date = reader.GetString(reader.GetOrdinal("Date")),
                                            StatusID = reader.GetInt32(reader.GetOrdinal("StatusID")),
                                            StatusName = reader.GetString(reader.GetOrdinal("StatusName")),
                                            CompanyID = reader.GetInt32(reader.GetOrdinal("CompanyID")),
                                            BranchID = reader.GetInt32(reader.GetOrdinal("BranchID")),
                                            CompanyName = reader.GetString(reader.GetOrdinal("CompanyName")),
                                            BranchName = reader.GetString(reader.GetOrdinal("BranchName"))
                                        });
                                    }
                                    SRCountList = serviceRequestCounts.AsEnumerable();
                                }
                            }
                        }
                    }
                    else
                    {

                        if (GenLangCode == UserLangCode)
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountByModelDetailsCaseRegistrationExport", conn))
                                {
                                    cmd.CommandType = CommandType.StoredProcedure;

                                    cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                                    cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                                    cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                    cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                                    conn.Open();
                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            serviceRequestCounts.Add(new ServiceRequestCount
                                            {
                                                Type = reader.GetString(reader.GetOrdinal("Type")),
                                                Year = reader.GetInt32(reader.GetOrdinal("Year")),
                                                Month = reader.GetInt32(reader.GetOrdinal("Month")),
                                                Date = reader.GetString(reader.GetOrdinal("Date")),
                                                StatusID = reader.GetInt32(reader.GetOrdinal("StatusID")),
                                                StatusName = reader.GetString(reader.GetOrdinal("StatusName")),
                                                CompanyID = reader.GetInt32(reader.GetOrdinal("CompanyID")),
                                                BranchID = reader.GetInt32(reader.GetOrdinal("BranchID")),
                                                CompanyName = reader.GetString(reader.GetOrdinal("CompanyName")),
                                                BranchName = reader.GetString(reader.GetOrdinal("BranchName"))
                                            });
                                        }
                                        SRCountList = serviceRequestCounts.AsEnumerable();
                                    }
                                }
                            }
                        }
                        else
                        {
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountByModelLocaleDetailsCaseRegistrationExport", conn))
                                {
                                    cmd.CommandType = CommandType.StoredProcedure;

                                    cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                                    cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                                    cmd.Parameters.AddWithValue("@Language_ID", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Language_ID);
                                    cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                    cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                                    conn.Open();
                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            serviceRequestCounts.Add(new ServiceRequestCount
                                            {
                                                Type = reader.GetString(reader.GetOrdinal("Type")),
                                                Year = reader.GetInt32(reader.GetOrdinal("Year")),
                                                Month = reader.GetInt32(reader.GetOrdinal("Month")),
                                                Date = reader.GetString(reader.GetOrdinal("Date")),
                                                StatusID = reader.GetInt32(reader.GetOrdinal("StatusID")),
                                                StatusName = reader.GetString(reader.GetOrdinal("StatusName")),
                                                CompanyID = reader.GetInt32(reader.GetOrdinal("CompanyID")),
                                                BranchID = reader.GetInt32(reader.GetOrdinal("BranchID")),
                                                CompanyName = reader.GetString(reader.GetOrdinal("CompanyName")),
                                                BranchName = reader.GetString(reader.GetOrdinal("BranchName"))
                                            });
                                        }
                                        SRCountList = serviceRequestCounts.AsEnumerable();
                                    }
                                }
                            }
                        }
                    }
                }
                else if (SelectedCriteria == 6)//Brand
                {
                    SelectionCriteria = "Brand";
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountByBrandDetailsCaseRegistrationExport", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;

                            cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                            cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                            cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                            conn.Open();
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    serviceRequestCounts.Add(new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader.GetString(reader.GetOrdinal("Brand_ID")).ToString())?.Name ?? "No Name",
                                        Year = reader.GetInt32(reader.GetOrdinal("Year")),
                                        Month = reader.GetInt32(reader.GetOrdinal("Month")),
                                        Date = reader.GetString(reader.GetOrdinal("Date")),
                                        StatusID = reader.GetInt32(reader.GetOrdinal("StatusID")),
                                        StatusName = reader.GetString(reader.GetOrdinal("StatusName")),
                                        CompanyID = reader.GetInt32(reader.GetOrdinal("CompanyID")),
                                        BranchID = reader.GetInt32(reader.GetOrdinal("BranchID")),
                                        CompanyName = reader.GetString(reader.GetOrdinal("CompanyName")),
                                        BranchName = reader.GetString(reader.GetOrdinal("BranchName"))
                                    });
                                }
                                SRCountList = serviceRequestCounts.AsEnumerable();
                            }
                        }
                    }
                }
                else if (SelectedCriteria == 7)//Product Type
                {
                    SelectionCriteria = "Product Type";

                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountByProductTypeCaseRegistrationExport", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;

                            cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                            cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                            cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                            conn.Open();
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    serviceRequestCounts.Add(new ServiceRequestCount
                                    {
                                        Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == reader.GetString(reader.GetOrdinal("ProductType_ID")).ToString())?.Name ?? "No Name",
                                        Year = reader.GetInt32(reader.GetOrdinal("Year")),
                                        Month = reader.GetInt32(reader.GetOrdinal("Month")),
                                        Date = reader.GetString(reader.GetOrdinal("Date")),
                                        StatusID = reader.GetInt32(reader.GetOrdinal("StatusID")),
                                        StatusName = reader.GetString(reader.GetOrdinal("StatusName")),
                                        CompanyID = reader.GetInt32(reader.GetOrdinal("CompanyID")),
                                        BranchID = reader.GetInt32(reader.GetOrdinal("BranchID")),
                                        CompanyName = reader.GetString(reader.GetOrdinal("CompanyName")),
                                        BranchName = reader.GetString(reader.GetOrdinal("BranchName"))
                                    });
                                }
                                SRCountList = serviceRequestCounts.AsEnumerable();
                            }
                        }
                    }
                }
                else if (SelectedCriteria == 8)//Party Type
                {
                    SelectionCriteria = "Business Area";
                    if (Selection.SelectedIDs.Count == 2)
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountByParty1CaseRegistrationExport", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;

                                cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                                cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                                cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                                conn.Open();
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        serviceRequestCounts.Add(new ServiceRequestCount
                                        {
                                            Type = reader.GetBoolean(reader.GetOrdinal("IsImportExport")) ? CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "International").ToString() : CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "Domestic").ToString(),
                                            Year = reader.GetInt32(reader.GetOrdinal("Year")),
                                            Month = reader.GetInt32(reader.GetOrdinal("Month")),
                                            Date = reader.GetString(reader.GetOrdinal("Date")),
                                            StatusID = reader.GetInt32(reader.GetOrdinal("StatusID")),
                                            StatusName = reader.GetString(reader.GetOrdinal("StatusName")),
                                            CompanyID = reader.GetInt32(reader.GetOrdinal("CompanyID")),
                                            BranchID = reader.GetInt32(reader.GetOrdinal("BranchID")),
                                            CompanyName = reader.GetString(reader.GetOrdinal("CompanyName")),
                                            BranchName = reader.GetString(reader.GetOrdinal("BranchName"))
                                        });
                                    }
                                    SRCountList = serviceRequestCounts.AsEnumerable();
                                }
                            }
                        }
                    }
                    else if (Selection.SelectedIDs[0].ID == 1)
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountByPartyDomesticCaseRegistrationExport", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;

                                cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                                cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                                cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                                conn.Open();
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        serviceRequestCounts.Add(new ServiceRequestCount
                                        {
                                            Type = CommonFunctionalities.GetGlobalResourceObject(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "Domestic").ToString(),
                                            Year = reader.GetInt32(reader.GetOrdinal("Year")),
                                            Month = reader.GetInt32(reader.GetOrdinal("Month")),
                                            Date = reader.GetString(reader.GetOrdinal("Date")),
                                            StatusID = reader.GetInt32(reader.GetOrdinal("StatusID")),
                                            StatusName = reader.GetString(reader.GetOrdinal("StatusName")),
                                            CompanyID = reader.GetInt32(reader.GetOrdinal("CompanyID")),
                                            BranchID = reader.GetInt32(reader.GetOrdinal("BranchID")),
                                            CompanyName = reader.GetString(reader.GetOrdinal("CompanyName")),
                                            BranchName = reader.GetString(reader.GetOrdinal("BranchName"))
                                        });
                                    }
                                    SRCountList = serviceRequestCounts.AsEnumerable();
                                }
                            }
                        }
                    }
                    else
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountByPartyInternationalCaseRegistrationExport", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;

                                cmd.Parameters.AddWithValue("@CompanyIds", CompanyIds);
                                cmd.Parameters.AddWithValue("@BranchIds", BranchIds);
                                cmd.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                cmd.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate));

                                conn.Open();
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        serviceRequestCounts.Add(new ServiceRequestCount
                                        {
                                            Type = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "International").ToString(),
                                            Year = reader.GetInt32(reader.GetOrdinal("Year")),
                                            Month = reader.GetInt32(reader.GetOrdinal("Month")),
                                            Date = reader.GetString(reader.GetOrdinal("Date")),
                                            StatusID = reader.GetInt32(reader.GetOrdinal("StatusID")),
                                            StatusName = reader.GetString(reader.GetOrdinal("StatusName")),
                                            CompanyID = reader.GetInt32(reader.GetOrdinal("CompanyID")),
                                            BranchID = reader.GetInt32(reader.GetOrdinal("BranchID")),
                                            CompanyName = reader.GetString(reader.GetOrdinal("CompanyName")),
                                            BranchName = reader.GetString(reader.GetOrdinal("BranchName"))
                                        });
                                    }
                                    SRCountList = serviceRequestCounts.AsEnumerable();
                                }
                            }
                        }
                    }

                }

                SRCountListStatus = (from SRequest in SRCountList
                                     group SRequest by new { SRequest.CompanyID, SRequest.BranchID, SRequest.Year, SRequest.Month, SRequest.Date, SRequest.Type } into final
                                     select new ServiceRequestCount()
                                     {

                                         Type = final.FirstOrDefault().Type,
                                         Count = final.Count(),
                                         Year = final.FirstOrDefault().Year,
                                         StatusIDs = final.Select(a => a.StatusID).ToList(),
                                         Month = final.FirstOrDefault().Month,
                                         Date = final.FirstOrDefault().Date,
                                         CompanyName = final.FirstOrDefault().CompanyName.ToString(),
                                         BranchName = final.FirstOrDefault().BranchName.ToString(),
                                         CompanyID = final.FirstOrDefault().CompanyID,
                                         BranchID = final.FirstOrDefault().BranchID
                                     });


                SRCountListCompleted = (from SRequest in SRCountListStatus

                                        select new ServiceRequestCount()
                                        {
                                            Year = SRequest.Year,
                                            Type = SRequest.Type,
                                            StatusName = SRequest.StatusName,
                                            Month = SRequest.Month,
                                            Date = SRequest.Date,
                                            Count = (from sr in SRequest.StatusIDs
                                                     where sr == CompletedID
                                                     select sr
                                                   ).Count(),
                                            CompanyName = SRequest.CompanyName.ToString(),
                                            BranchName = SRequest.BranchName.ToString(),
                                            CompanyID = SRequest.CompanyID,
                                            BranchID = SRequest.BranchID

                                        });

                SRCountListOnHold = (from SRequest in SRCountListStatus

                                     select new ServiceRequestCount()
                                     {
                                         Year = SRequest.Year,
                                         Type = SRequest.Type,
                                         StatusName = SRequest.StatusName,
                                         Month = SRequest.Month,
                                         Date = SRequest.Date,
                                         Count = (from sr in SRequest.StatusIDs
                                                  where sr == HoldID
                                                  select sr
                                                ).Count(),
                                         CompanyName = SRequest.CompanyName.ToString(),
                                         BranchName = SRequest.BranchName.ToString(),
                                         CompanyID = SRequest.CompanyID,
                                         BranchID = SRequest.BranchID

                                     });

                SRCountListInProgress = (from SRequest in SRCountListStatus

                                         select new ServiceRequestCount()
                                         {
                                             Year = SRequest.Year,
                                             Type = SRequest.Type,
                                             StatusName = SRequest.StatusName,
                                             Month = SRequest.Month,
                                             Date = SRequest.Date,
                                             Count = (from sr in SRequest.StatusIDs
                                                      where sr == InProgressID
                                                      select sr
                                                    ).Count(),
                                             CompanyName = SRequest.CompanyName.ToString(),
                                             BranchName = SRequest.BranchName.ToString(),
                                             CompanyID = SRequest.CompanyID,
                                             BranchID = SRequest.BranchID

                                         });

                SRCountListClosed = (from SRequest in SRCountListStatus
                                     select new ServiceRequestCount()
                                     {
                                         Year = SRequest.Year,
                                         Type = SRequest.Type,
                                         StatusName = SRequest.StatusName,
                                         Month = SRequest.Month,
                                         Date = SRequest.Date,
                                         Count = (from sr in SRequest.StatusIDs
                                                  where sr == ClosedID
                                                  select sr
                                                ).Count(),
                                         CompanyName = SRequest.CompanyName.ToString(),
                                         BranchName = SRequest.BranchName.ToString(),
                                         CompanyID = SRequest.CompanyID,
                                         BranchID = SRequest.BranchID
                                     });

                SRCountListEscalated = (from SRequest in SRCountListStatus
                                        select new ServiceRequestCount()
                                        {
                                            Year = SRequest.Year,
                                            Type = SRequest.Type,
                                            StatusName = SRequest.StatusName,
                                            Month = SRequest.Month,
                                            Date = SRequest.Date,
                                            Count = (from sr in SRequest.StatusIDs
                                                     where sr == EscalatedID
                                                     select sr
                                                   ).Count(),
                                            CompanyName = SRequest.CompanyName.ToString(),
                                            BranchName = SRequest.BranchName.ToString(),
                                            CompanyID = SRequest.CompanyID,
                                            BranchID = SRequest.BranchID
                                        });

                FinalResultList = (from srFinal in SRCountListStatus
                                       //join Completed in SRCountListCompleted on new { srFinal.Year, srFinal.Type, srFinal.Month, srFinal.Date } equals new { Completed.Year, Completed.Type, Completed.Month, Completed.Date } into FC
                                   join Completed in SRCountListCompleted on new { srFinal.CompanyID, srFinal.BranchID, srFinal.Year, srFinal.Month, srFinal.Date, srFinal.Type } equals new { Completed.CompanyID, Completed.BranchID, Completed.Year, Completed.Month, Completed.Date, Completed.Type } into FC
                                   from FinComp in FC.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                   join hold in SRCountListOnHold on new { srFinal.CompanyID, srFinal.BranchID, srFinal.Year, srFinal.Month, srFinal.Date, srFinal.Type } equals new { hold.CompanyID, hold.BranchID, hold.Year, hold.Month, hold.Date, hold.Type } into HO
                                   from FinHO in HO.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                   join Progress in SRCountListInProgress on new { srFinal.CompanyID, srFinal.BranchID, srFinal.Year, srFinal.Month, srFinal.Date, srFinal.Type } equals new { Progress.CompanyID, Progress.BranchID, Progress.Year, Progress.Month, Progress.Date, Progress.Type } into IP
                                   from FINIP in IP.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                   join closed in SRCountListClosed on new { srFinal.CompanyID, srFinal.BranchID, srFinal.Year, srFinal.Month, srFinal.Date, srFinal.Type } equals new { closed.CompanyID, closed.BranchID, closed.Year, closed.Month, closed.Date, closed.Type } into CL
                                   from FINCL in CL.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                   join escalated in SRCountListEscalated on new { srFinal.CompanyID, srFinal.BranchID, srFinal.Year, srFinal.Month, srFinal.Date, srFinal.Type } equals new { escalated.CompanyID, escalated.BranchID, escalated.Year, escalated.Month, escalated.Date, escalated.Type } into ES
                                   from FINES in ES.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                   select new ServiceRequestCount()
                                   {
                                       Region = Common.getRegionName(connString, LogException, userLanguageID, generalLanguageID, srFinal.BranchID),
                                       CompanyName = srFinal.CompanyName.ToString(),
                                       BranchName = srFinal.BranchName.ToString(),
                                       ClosedCount = FINCL.Count,
                                       CompletedCount = FinComp.Count,
                                       Count = srFinal.Count,
                                       InProgressCount = FINIP.Count,
                                       OnHoldCount = FinHO.Count,
                                       EscalatedCount = FINES.Count,
                                       Type = srFinal.Type,
                                       Year = srFinal.Year,
                                       Month = srFinal.Month,
                                       Date = Convert.ToDateTime(srFinal.Date).ToString("dd-MMM-yyyy"),

                                   }).ToList();

                DataTable dtOptions = new DataTable();
                dtOptions.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "SelectionCriteria").ToString());
                dtOptions.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "fromdate").ToString());
                dtOptions.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "todate").ToString());

                dtOptions.Rows.Add(SelectionCriteria, (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.FromDate.ToString() != "" ? Convert.ToDateTime(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.FromDate.ToString()).ToString("dd-MMM-yyyy") : ""), (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.ToDate.ToString() != "" ? Convert.ToDateTime(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.ToDate.ToString()).ToString("dd-MMM-yyyy") : ""));

                DataTable dt = new DataTable();
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "Region").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "Date").ToString());
                dt.Columns.Add(SelectionCriteria);

                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "Company").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "Branch").ToString());

                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "ReceivedCount").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "InProgressCount").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "OnHoldCount").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "CompletedCount").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "ClosedCount").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "Escalatedcount").ToString());

                DataTable DtAlignment = new DataTable();
                DtAlignment.Columns.Add("Region");
                DtAlignment.Columns.Add("Date");
                DtAlignment.Columns.Add(SelectionCriteria);
                DtAlignment.Columns.Add("CompanyName");
                DtAlignment.Columns.Add("BranchName");
                DtAlignment.Columns.Add("ReceivedCount");
                DtAlignment.Columns.Add("InProgressCount");
                DtAlignment.Columns.Add("OnHoldCount");
                DtAlignment.Columns.Add("CompletedCount");
                DtAlignment.Columns.Add("ClosedCount");
                DtAlignment.Columns.Add("Escalatedcount");
                DtAlignment.Rows.Add(0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2);
                int Count = FinalResultList.Count;
                DataSet ds = new DataSet();
                string[] strCompanyNamesArr = SelectedCompanyNames.Split(',');
                string[] strBranchNamesArr = SelectedBranchNames.Split(',');

                for (int i = 0; i < Count; i++)
                {
                    TotalRecievedCount += FinalResultList[i].Count;
                    TotalCompletedCount += FinalResultList[i].CompletedCount;
                    TotalInProgressCount += FinalResultList[i].InProgressCount;
                    TotalOnHoldCount += FinalResultList[i].OnHoldCount;
                    TotalClosedCount += FinalResultList[i].ClosedCount;
                    TotalEscalatedcount += FinalResultList[i].EscalatedCount;
                    dt.Rows.Add(FinalResultList[i].Region, FinalResultList[i].Date, FinalResultList[i].Type, FinalResultList[i].CompanyName, FinalResultList[i].BranchName, FinalResultList[i].Count, FinalResultList[i].InProgressCount, FinalResultList[i].OnHoldCount, FinalResultList[i].CompletedCount, FinalResultList[i].ClosedCount, FinalResultList[i].EscalatedCount);
                }

                dt.Rows.Add("", "", "", "", "Total", TotalRecievedCount, TotalInProgressCount, TotalOnHoldCount, TotalCompletedCount, TotalClosedCount, TotalEscalatedcount);
                ExportReportExportCR5List exportReport = new ExportReportExportCR5List
                {
                    FileName = "CaseRegistrationCount",
                    Branch = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Branch,
                    Company_ID = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Company_ID,
                    UserCulture = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture,
                    dt = dt, // You can populate this with actual data as needed
                    exprtType = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.exprtType, // Set an appropriate type for export (e.g., 1 for PDF, 2 for Excel, etc.)
                    Header = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "CaseRegistrationCount").ToString(),
                    Options = dtOptions, // Populate this with your report options
                    selection = ds, // Add selection-related data here
                    Alignment = DtAlignment // Define alignment details for table columns
                };
                var result = await ReportExportCR5.Export(exportReport, connString, LogException);
                return result.Value;
                //  ReportExportCR5.Export(exprtType, dt, dtOptions, ds, DtAlignment, "CaseRegistrationCount", HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "CaseRegistrationCount").ToString());
                //  gbl.InsertGPSDetails(Convert.ToInt32(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Company_ID.ToString()), Convert.ToInt32(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Branch), HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.User_ID, Common.GetObjectID("HelpDesk_Tr_CaseRegistrationCount"), 0, 0, 0, "Ticket Registration Count-Export", false, Convert.ToInt32(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.MenuID), Convert.ToDateTime(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.LoggedINDateTime));
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }
            }
            return false;
        }
        #endregion





        #region ::: Select Service Request Count Type Uday Kumar J B 22-11-2024:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        //-- Modified by Venkateshwari for HelpDesk CR-5 Changes 18-Aug-2015 -- Start

        public static IActionResult SelectTypeReport(HelpDesk_Tr_CaseRegistrationCountSelectYearReportList HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonobj = default(dynamic);
            int count = 0;
            int total = 0;
            IEnumerable<ServiceRequestCount> SRCountListRecieved = null;
            IEnumerable<ServiceRequestCount> SRCountListCompleted = null;
            IEnumerable<ServiceRequestCount> SRCountListInProgress = null;
            IEnumerable<ServiceRequestCount> SRCountListOnHold = null;
            IEnumerable<ServiceRequestCount> SRCountListClosed = null;
            IEnumerable<ServiceRequestCount> SRCountListEscalated = null;//Added by Harish on 8-Oct-2014
            IEnumerable<ServiceRequestCount> SRCountListStatus = null;
            List<ServiceRequestCount> FinalResultList = new List<ServiceRequestCount>();
            ServiceRequestCount FinalResult = new ServiceRequestCount();
            string GenLangCode = string.Empty;
            string UserLangCode = string.Empty;

            GetStatusIDs(connString, LogException);
            try
            {
                GenLangCode = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.GeneralLanguageCode.ToString();
                UserLangCode = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserLanguageCode.ToString();
                int userid = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.User_ID;
                string frmdate = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.FromDate;
                string todate = HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.ToDate;
                string CompanyIds = string.Empty;
                Top_Company companies = new Top_Company();
                if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Company != null && HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Company != "")
                {
                    companies = JObject.Parse(Common.DecryptString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Company)).ToObject<Top_Company>();

                    for (int i = 0; i < companies.Companies.Count; i++)
                    {
                        CompanyIds = CompanyIds + companies.Companies[i].ID + ", ";
                    }
                    CompanyIds = CompanyIds.Remove(CompanyIds.LastIndexOf(','), 1);
                }

                string BranchIds = string.Empty;
                RTS_Branch branchs = new RTS_Branch();
                if (HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Branch != null && HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Branch != "")
                {
                    branchs = JObject.Parse(Common.DecryptString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Branch)).ToObject<RTS_Branch>();

                    for (int i = 0; i < branchs.Branchs.Count; i++)
                    {
                        BranchIds = BranchIds + branchs.Branchs[i].ID + ", ";
                    }
                    BranchIds = BranchIds.Remove(BranchIds.LastIndexOf(','), 1);
                }

                var strCompanyIds = CompanyIds.Split(',');
                var strBranchIds = BranchIds.Split(',');

                int Mode = Convert.ToInt32(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Mode);
                SelectionCriteria Selection = JObject.Parse(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Data).ToObject<SelectionCriteria>();

                foreach (var s in Selection.SelectedIDs)
                {
                    s.Name = Common.DecryptString(s.Name);
                }


                if (Mode == 1)//Call type
                {
                    using (SqlConnection connection = new SqlConnection(connString))
                    {
                        connection.Open();
                        SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsSelectTypeReport", connection);
                        command.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        command.Parameters.AddWithValue("@CompanyIds", CompanyIds.TrimEnd(','));
                        command.Parameters.AddWithValue("@BranchIds", BranchIds.TrimEnd(','));
                        command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                        command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1));

                        SqlDataAdapter adapter = new SqlDataAdapter(command);
                        DataTable resultTable = new DataTable();
                        adapter.Fill(resultTable);

                        // Convert the result table to a list
                        SRCountListRecieved = resultTable.AsEnumerable().Select(row => new ServiceRequestCount
                        {
                            Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == row["IssueArea_ID"].ToString())?.Name ?? "No Name",
                            Year = Convert.ToInt32(row["Year"]),
                            StatusID = Convert.ToInt32(row["StatusID"]),
                            StatusName = row["StatusName"].ToString()
                        }).ToList();
                    }


                }

                else if (Mode == 2)//Call Nature
                {
                    using (SqlConnection connection = new SqlConnection(connString))
                    {
                        connection.Open();
                        SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByComplexitySelectTypeReport", connection);
                        command.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        command.Parameters.AddWithValue("@CompanyIds", CompanyIds.TrimEnd(','));
                        command.Parameters.AddWithValue("@BranchIds", BranchIds.TrimEnd(','));
                        command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                        command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1));

                        SqlDataAdapter adapter = new SqlDataAdapter(command);
                        DataTable resultTable = new DataTable();
                        adapter.Fill(resultTable);

                        // Convert the result table to a list
                        SRCountListRecieved = resultTable.AsEnumerable().Select(row => new ServiceRequestCount
                        {
                            Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == row["CallComplexity_ID"].ToString())?.Name ?? "No Name",
                            Year = Convert.ToInt32(row["Year"]),
                            StatusID = Convert.ToInt32(row["StatusID"]),
                            StatusName = row["StatusName"].ToString()
                        }).ToList();
                    }
                }

                else if (Mode == 3)//Party
                {
                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (SqlConnection connection = new SqlConnection(connString))
                        {
                            connection.Open();
                            SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsByPartySelectTypeReport", connection);
                            command.CommandType = CommandType.StoredProcedure;

                            // Add parameters
                            command.Parameters.AddWithValue("@CompanyIds", CompanyIds.TrimEnd(','));
                            command.Parameters.AddWithValue("@BranchIds", BranchIds.TrimEnd(','));
                            command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1));

                            SqlDataAdapter adapter = new SqlDataAdapter(command);
                            DataTable resultTable = new DataTable();
                            adapter.Fill(resultTable);

                            // Convert the result table to a list
                            SRCountListRecieved = resultTable.AsEnumerable().Select(row => new ServiceRequestCount
                            {
                                Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == row["Party_ID"].ToString())?.Name ?? "No Name",
                                Year = Convert.ToInt32(row["Year"]),
                                StatusID = Convert.ToInt32(row["StatusID"]),
                                StatusName = row["StatusName"].ToString()
                            }).ToList();
                        }
                    }
                    else
                    {
                        if (GenLangCode == UserLangCode)
                        {
                            using (SqlConnection connection = new SqlConnection(connString))
                            {
                                connection.Open();
                                SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsWithPartyDetailsSelectTypeReport", connection);
                                command.CommandType = CommandType.StoredProcedure;

                                // Add parameters
                                command.Parameters.AddWithValue("@CompanyIds", CompanyIds.TrimEnd(','));
                                command.Parameters.AddWithValue("@BranchIds", BranchIds.TrimEnd(','));
                                command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1));

                                SqlDataAdapter adapter = new SqlDataAdapter(command);
                                DataTable resultTable = new DataTable();
                                adapter.Fill(resultTable);

                                // Convert the result table to a list
                                SRCountListRecieved = resultTable.AsEnumerable().Select(row => new ServiceRequestCount
                                {
                                    Type = row["Type"].ToString(),
                                    Year = Convert.ToInt32(row["Year"]),
                                    StatusID = Convert.ToInt32(row["StatusID"]),
                                    StatusName = row["StatusName"].ToString()
                                }).ToList();
                            }
                        }
                        else
                        {
                            using (SqlConnection connection = new SqlConnection(connString))
                            {
                                connection.Open();
                                SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsWithPartyLocaleSelectTypeReport", connection);
                                command.CommandType = CommandType.StoredProcedure;

                                // Add parameters
                                command.Parameters.AddWithValue("@CompanyIds", CompanyIds.TrimEnd(','));
                                command.Parameters.AddWithValue("@BranchIds", BranchIds.TrimEnd(','));
                                command.Parameters.AddWithValue("@LanguageID", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Language_ID);
                                command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1));

                                SqlDataAdapter adapter = new SqlDataAdapter(command);
                                DataTable resultTable = new DataTable();
                                adapter.Fill(resultTable);

                                // Convert the result table to a list
                                SRCountListRecieved = resultTable.AsEnumerable().Select(row => new ServiceRequestCount
                                {
                                    Type = row["Type"].ToString(),
                                    Year = Convert.ToInt32(row["Year"]),
                                    StatusID = Convert.ToInt32(row["StatusID"]),
                                    StatusName = row["StatusName"].ToString()
                                }).ToList();
                            }
                        }
                    }

                }

                else if (Mode == 4)//Priority
                {

                    using (SqlConnection connection = new SqlConnection(connString))
                    {
                        connection.Open();
                        SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsWithCallPrioritySelectTypeReport", connection);
                        command.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        command.Parameters.AddWithValue("@CompanyIds", CompanyIds.TrimEnd(','));
                        command.Parameters.AddWithValue("@BranchIds", BranchIds.TrimEnd(','));
                        command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                        command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1));

                        SqlDataAdapter adapter = new SqlDataAdapter(command);
                        DataTable resultTable = new DataTable();
                        adapter.Fill(resultTable);

                        // Convert the result table to a list
                        SRCountListRecieved = resultTable.AsEnumerable().Select(row => new ServiceRequestCount
                        {
                            Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == row["CallPriority_ID"].ToString())?.Name ?? "No Name",
                            Year = Convert.ToInt32(row["Year"]),
                            StatusID = Convert.ToInt32(row["StatusID"]),
                            StatusName = row["StatusName"].ToString()
                        }).ToList();
                    }
                }

                else if (Mode == 5)//Model
                {
                    if (Selection.SelectedIDs[0].ID != 0)
                    {
                        using (SqlConnection connection = new SqlConnection(connString))
                        {
                            connection.Open();
                            SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsWithModelSelectTypeReport", connection);
                            command.CommandType = CommandType.StoredProcedure;

                            // Add parameters
                            command.Parameters.AddWithValue("@CompanyIds", CompanyIds.TrimEnd(','));
                            command.Parameters.AddWithValue("@BranchIds", BranchIds.TrimEnd(','));
                            command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1));

                            SqlDataAdapter adapter = new SqlDataAdapter(command);
                            DataTable resultTable = new DataTable();
                            adapter.Fill(resultTable);

                            // Convert the result table to a list
                            SRCountListRecieved = resultTable.AsEnumerable().Select(row => new ServiceRequestCount
                            {
                                Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == row["Model_ID"].ToString())?.Name ?? "No Name",
                                Year = Convert.ToInt32(row["Year"]),
                                StatusID = Convert.ToInt32(row["StatusID"]),
                                StatusName = row["StatusName"].ToString()
                            }).ToList();
                        }
                    }
                    else
                    {
                        if (GenLangCode == UserLangCode)
                        {
                            using (SqlConnection connection = new SqlConnection(connString))
                            {
                                connection.Open();
                                SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsWithModel1SelectTypeReport", connection);
                                command.CommandType = CommandType.StoredProcedure;

                                // Add parameters
                                command.Parameters.AddWithValue("@CompanyIds", CompanyIds.TrimEnd(','));
                                command.Parameters.AddWithValue("@BranchIds", BranchIds.TrimEnd(','));
                                command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1));

                                SqlDataAdapter adapter = new SqlDataAdapter(command);
                                DataTable resultTable = new DataTable();
                                adapter.Fill(resultTable);

                                // Convert the result table to a list
                                SRCountListRecieved = resultTable.AsEnumerable().Select(row => new ServiceRequestCount
                                {
                                    Type = row["Type"].ToString(),  // Model_Name from GNM_Model
                                    Year = Convert.ToInt32(row["Year"]),
                                    StatusID = Convert.ToInt32(row["StatusID"]),
                                    StatusName = row["StatusName"].ToString()
                                }).ToList();
                            }
                        }
                        else
                        {
                            using (SqlConnection connection = new SqlConnection(connString))
                            {
                                connection.Open();
                                SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsWithModelLocaleSelectTypeReport", connection);
                                command.CommandType = CommandType.StoredProcedure;

                                // Add parameters
                                command.Parameters.AddWithValue("@CompanyIds", CompanyIds.TrimEnd(','));
                                command.Parameters.AddWithValue("@BranchIds", BranchIds.TrimEnd(','));
                                command.Parameters.AddWithValue("@LanguageID", HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Language_ID);
                                command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                                command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1));

                                SqlDataAdapter adapter = new SqlDataAdapter(command);
                                DataTable resultTable = new DataTable();
                                adapter.Fill(resultTable);

                                // Convert the result table to a list
                                SRCountListRecieved = resultTable.AsEnumerable().Select(row => new ServiceRequestCount
                                {
                                    Type = row["Type"].ToString(),  // Model_Name from GNM_ModelLocale
                                    Year = Convert.ToInt32(row["Year"]),
                                    StatusID = Convert.ToInt32(row["StatusID"]),
                                    StatusName = row["StatusName"].ToString()
                                }).ToList();
                            }
                        }
                    }

                }
                else if (Mode == 6)//Brand
                {
                    using (SqlConnection connection = new SqlConnection(connString))
                    {
                        connection.Open();
                        SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsWithBrandSelectTypeReport", connection);
                        command.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        command.Parameters.AddWithValue("@CompanyIds", CompanyIds.TrimEnd(','));
                        command.Parameters.AddWithValue("@BranchIds", BranchIds.TrimEnd(','));
                        command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                        command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1));

                        SqlDataAdapter adapter = new SqlDataAdapter(command);
                        DataTable resultTable = new DataTable();
                        adapter.Fill(resultTable);

                        // Convert the result table to a list
                        SRCountListRecieved = resultTable.AsEnumerable().Select(row => new ServiceRequestCount
                        {
                            Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == row["Brand_ID"].ToString())?.Name ?? "No Name",
                            Year = Convert.ToInt32(row["Year"]),
                            StatusID = Convert.ToInt32(row["StatusID"]),
                            StatusName = row["StatusName"].ToString()
                        }).ToList();
                    }
                }
                else if (Mode == 7)//Product Type
                {
                    using (SqlConnection connection = new SqlConnection(connString))
                    {
                        connection.Open();
                        SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsWithProductTypeSelectTypeReport", connection);
                        command.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        command.Parameters.AddWithValue("@CompanyIds", CompanyIds.TrimEnd(','));
                        command.Parameters.AddWithValue("@BranchIds", BranchIds.TrimEnd(','));
                        command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                        command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1));

                        SqlDataAdapter adapter = new SqlDataAdapter(command);
                        DataTable resultTable = new DataTable();
                        adapter.Fill(resultTable);

                        // Convert the result table to a list
                        SRCountListRecieved = resultTable.AsEnumerable().Select(row => new ServiceRequestCount
                        {
                            Type = Selection.SelectedIDs.FirstOrDefault(s => s.ID.ToString() == row["ProductType_ID"].ToString())?.Name ?? "No Name",
                            Year = Convert.ToInt32(row["Year"]),
                            StatusID = Convert.ToInt32(row["StatusID"]),
                            StatusName = row["StatusName"].ToString()
                        }).ToList();
                    }
                }
                else if (Mode == 8)
                {
                    if (Selection.SelectedIDs.Count == 2)
                    {
                        using (SqlConnection connection = new SqlConnection(connString))
                        {
                            connection.Open();
                            SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetServiceRequestCountsWithPartySelectTypeReport", connection);
                            command.CommandType = CommandType.StoredProcedure;

                            // Add parameters
                            command.Parameters.AddWithValue("@CompanyIds", CompanyIds.TrimEnd(','));
                            command.Parameters.AddWithValue("@BranchIds", BranchIds.TrimEnd(','));
                            command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1));

                            SqlDataAdapter adapter = new SqlDataAdapter(command);
                            DataTable resultTable = new DataTable();
                            adapter.Fill(resultTable);

                            // Convert the result table to a list
                            SRCountListRecieved = resultTable.AsEnumerable().Select(row => new ServiceRequestCount
                            {
                                Type = Convert.ToBoolean(row["IsImportExport"]) ? CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "Domestic").ToString() : CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "International").ToString(),
                                Year = Convert.ToInt32(row["Year"]),
                                StatusID = Convert.ToInt32(row["StatusID"]),
                                StatusName = row["StatusName"].ToString()
                            }).ToList();
                        }
                    }
                    else if (Selection.SelectedIDs[0].ID == 1)
                    {
                        using (SqlConnection connection = new SqlConnection(connString))
                        {
                            connection.Open();
                            SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetDomesticServiceRequestCountsSelectTypeReport", connection);
                            command.CommandType = CommandType.StoredProcedure;

                            // Add parameters
                            command.Parameters.AddWithValue("@CompanyIds", CompanyIds.TrimEnd(','));
                            command.Parameters.AddWithValue("@BranchIds", BranchIds.TrimEnd(','));
                            command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1));

                            SqlDataAdapter adapter = new SqlDataAdapter(command);
                            DataTable resultTable = new DataTable();
                            adapter.Fill(resultTable);

                            // Convert the result table to a list
                            SRCountListRecieved = resultTable.AsEnumerable().Select(row => new ServiceRequestCount()
                            {
                                Type = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "Domestic").ToString(),
                                Year = Convert.ToInt32(row["Year"]),
                                StatusID = Convert.ToInt32(row["StatusID"]),
                                StatusName = row["StatusName"].ToString()
                            }).ToList();
                        }
                    }
                    else
                    {
                        using (SqlConnection connection = new SqlConnection(connString))
                        {
                            connection.Open();
                            SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetInternationalServiceRequestCountsSelectTypeReport", connection);
                            command.CommandType = CommandType.StoredProcedure;

                            // Add parameters
                            command.Parameters.AddWithValue("@CompanyIds", CompanyIds.TrimEnd(','));
                            command.Parameters.AddWithValue("@BranchIds", BranchIds.TrimEnd(','));
                            command.Parameters.AddWithValue("@FromDate", string.IsNullOrEmpty(frmdate) ? (object)DBNull.Value : Convert.ToDateTime(frmdate));
                            command.Parameters.AddWithValue("@ToDate", string.IsNullOrEmpty(todate) ? (object)DBNull.Value : Convert.ToDateTime(todate).AddDays(1));

                            SqlDataAdapter adapter = new SqlDataAdapter(command);
                            DataTable resultTable = new DataTable();
                            adapter.Fill(resultTable);

                            // Convert the result table to a list
                            SRCountListRecieved = resultTable.AsEnumerable().Select(row => new ServiceRequestCount()
                            {
                                Type = CommonFunctionalities.GetResourceString(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.UserCulture.ToString(), "International").ToString(),
                                Year = Convert.ToInt32(row["Year"]),
                                StatusID = Convert.ToInt32(row["StatusID"]),
                                StatusName = row["StatusName"].ToString()
                            }).ToList();
                        }
                    }
                }


                SRCountListStatus = (from SRequest in SRCountListRecieved
                                     group SRequest by new { SRequest.Type } into final
                                     select new ServiceRequestCount()
                                     {
                                         Type = final.FirstOrDefault().Type,
                                         Count = final.Count(),
                                         //Year = final.FirstOrDefault().Year,
                                         StatusIDs = final.Select(a => a.StatusID).ToList()
                                     });



                SRCountListCompleted = (from SRequest in SRCountListStatus

                                        select new ServiceRequestCount()
                                        {
                                            //Year = SRequest.Year,
                                            Type = SRequest.Type,
                                            StatusName = SRequest.StatusName,
                                            Count = (from sr in SRequest.StatusIDs
                                                     where sr == CompletedID
                                                     select sr
                                                   ).Count()

                                        });



                SRCountListOnHold = (from SRequest in SRCountListStatus

                                     select new ServiceRequestCount()
                                     {
                                         //Year = SRequest.Year,
                                         Type = SRequest.Type,
                                         StatusName = SRequest.StatusName,
                                         Count = (from sr in SRequest.StatusIDs
                                                  where sr == HoldID
                                                  select sr
                                                ).Count()

                                     });



                SRCountListInProgress = (from SRequest in SRCountListStatus

                                         select new ServiceRequestCount()
                                         {
                                             //Year = SRequest.Year,
                                             Type = SRequest.Type,
                                             StatusName = SRequest.StatusName,
                                             Count = (from sr in SRequest.StatusIDs
                                                      where sr == InProgressID
                                                      select sr
                                                    ).Count()

                                         });


                SRCountListClosed = (from SRequest in SRCountListStatus

                                     select new ServiceRequestCount()
                                     {
                                         //Year = SRequest.Year,
                                         Type = SRequest.Type,
                                         StatusName = SRequest.StatusName,
                                         Count = (from sr in SRequest.StatusIDs
                                                  where sr == ClosedID
                                                  select sr
                                                ).Count()

                                     });
                //Added by Harish on 8-Oct-2014
                SRCountListEscalated = (from SRequest in SRCountListStatus

                                        select new ServiceRequestCount()
                                        {
                                            //Year = SRequest.Year,
                                            Type = SRequest.Type,
                                            StatusName = SRequest.StatusName,
                                            Count = (from sr in SRequest.StatusIDs
                                                     where sr == EscalatedID
                                                     select sr
                                                   ).Count()
                                        });


                FinalResultList = (from srFinal in SRCountListStatus
                                   join Completed in SRCountListCompleted on new { srFinal.Type } equals new { Completed.Type } into FC
                                   from FinComp in FC.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                   join hold in SRCountListOnHold on new { srFinal.Type } equals new { hold.Type } into HO
                                   from FinHO in HO.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                   join Progress in SRCountListInProgress on new { srFinal.Type } equals new { Progress.Type } into IP
                                   from FINIP in IP.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                   join closed in SRCountListClosed on new { srFinal.Type } equals new { closed.Type } into CL
                                   from FINCL in CL.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                       //Added by Harish on 8-Oct-2014
                                   join escalated in SRCountListEscalated on new { srFinal.Type } equals new { escalated.Type } into ES
                                   from FINES in ES.DefaultIfEmpty(new ServiceRequestCount { Count = 0 })
                                   select new ServiceRequestCount()
                                   {
                                       ClosedCount = FINCL.Count,
                                       CompletedCount = FinComp.Count,
                                       Count = srFinal.Count,
                                       InProgressCount = FINIP.Count,
                                       OnHoldCount = FinHO.Count,
                                       EscalatedCount = FINES.Count,//Added by Harish on 8-Oct-2014
                                       Type = srFinal.Type,
                                       //Year = srFinal.Year
                                   }).ToList();

                count = SRCountListStatus.Count();

                for (int i = 0; i < FinalResultList.Count; i++)
                {
                    int index = i;
                    int j = index++;

                    while (j < FinalResultList.Count)
                    {
                        if ((i != j) && (FinalResultList[i].Type == FinalResultList[j].Type))
                        {
                            FinalResultList[i].Count += FinalResultList[j].Count;
                            FinalResultList[i].CompletedCount += FinalResultList[j].CompletedCount;
                            FinalResultList[i].InProgressCount += FinalResultList[j].InProgressCount;
                            FinalResultList[i].OnHoldCount += FinalResultList[j].OnHoldCount;
                            FinalResultList[i].ClosedCount += FinalResultList[j].ClosedCount;
                            FinalResultList[i].EscalatedCount += FinalResultList[j].EscalatedCount;
                            FinalResultList.RemoveAt(j);
                        }
                        else { j++; }
                    }
                }

                for (int i = 0; i < FinalResultList.Count; i++)
                {
                    TotalRecievedCount += FinalResultList.ElementAt(i).Count;
                    TotalCompletedCount += FinalResultList.ElementAt(i).CompletedCount;
                    TotalInProgressCount += FinalResultList.ElementAt(i).InProgressCount;
                    TotalOnHoldCount += FinalResultList.ElementAt(i).OnHoldCount;
                    TotalClosedCount += FinalResultList.ElementAt(i).ClosedCount;
                    TotalEscalatedcount += FinalResultList.ElementAt(i).EscalatedCount;//Added by Harish on 8-Oct-2014
                }

                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                jsonobj = new
                {
                    TotalPages = total,
                    PageNo = page,
                    RecordCount = count,
                    TotalRecievedCount = TotalRecievedCount,
                    TotalCompletedCount = TotalCompletedCount,
                    TotalInProgressCount = TotalInProgressCount,
                    TotalOnHoldCount = TotalOnHoldCount,
                    TotalClosedCount = TotalClosedCount,
                    TotalEscalatedcount = TotalEscalatedcount,//Added by Harish on 8-Oct-2014
                    rows = FinalResultList
                };
                //  gbl.InsertGPSDetails(Convert.ToInt32(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Company_ID.ToString()), Convert.ToInt32(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.Branch), HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.User_ID, Common.GetObjectID("HelpDesk_Tr_CaseRegistrationCount"), 0, 0, 0, " Generated Ticket Registration Count Report", false, Convert.ToInt32(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.MenuID), Convert.ToDateTime(HelpDesk_Tr_CaseRegistrationCountSelectYearReportobj.LoggedINDateTime));
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }                //  return RedirectToAction("Error");
            }
            return new JsonResult(jsonobj);
        }
        #endregion








        #region ::: HelpDesk_Tr_CaseRegistrationCountSelectYearReport list and obj classes Uday Kumar J B 22-11-2024:::
        /// <summary>
        /// HelpDesk_Tr_CaseRegistrationCountSelectYearReport
        /// </summary>
        /// <returns>...</returns>
        //


        public class HelpDesk_Tr_CaseRegistrationCountSelectYearReportList
        {
            public string GeneralLanguageCode { get; set; }

            public string UserLanguageCode { get; set; }
            public int User_ID { get; set; }
            public string FromDate { get; set; }
            public string ToDate { get; set; }
            public string Type { get; set; }
            public int SelectedCriteria { get; set; }
            public string Data { get; set; }
            public string Company { get; set; }
            public string Branch { get; set; }
            public int Language_ID { get; set; }
            public string UserCulture { get; set; }
            public int Mode { get; set; }
            public int Year { get; set; }
            public int Month { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int exprtType { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filter { get; set; }
            public string advanceFilter { get; set; }


        }

        public class HelpDesk_Tr_CaseRegistrationCountLoadBrandList
        {

            public int Language_ID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
            public string Company { get; set; }


        }



        public class HelpDesk_Tr_CaseRegistrationCountGetModelList
        {
            public string ModelName { get; set; }
            public int Language_ID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
            public string Company { get; set; }

        }


        public class HelpDesk_Tr_CaseRegistrationCountGetPartyDetailsList
        {
            public string PartyName { get; set; }
            public int Language_ID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
            public string Company { get; set; }


        }






        public class HelpDesk_Tr_CaseRegistrationCountProductTypeList
        {
            public int Language_ID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
            public string Company { get; set; }

        }

        public class HelpDesk_Tr_CaseRegistrationCountloadMastersList
        {
            public string CompanyArray { get; set; }
            public string MasterName { get; set; }
            public int Company_ID { get; set; }
            public int Language_ID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }

        }

        public class HelpDesk_Tr_CaseRegistrationCountSelectModelDetailGridList
        {
            public string ModelName { get; set; }
            public int Company_ID { get; set; }
            public int Language_ID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }

        }


        public class HelpDesk_Tr_CaseRegistrationCountSelectPartyDetailGridList
        {
            public string PartyName { get; set; }
            public int Company_ID { get; set; }
            public int Language_ID { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }

        }

        #endregion


        #region ::: HelpDesk_Tr_CaseRegistrationCountSelectYearReport  classes Uday Kumar J B 22-11-2024:::
        /// <summary>
        /// HelpDesk_Tr_CaseRegistrationCountSelectYearReport
        /// </summary>
        /// <returns>...</returns>
        //
        public class Top_Branch
        {
            public List<Top_Branchs> Branchs
            {
                get;
                set;
            }
        }
        public class Top_Branchs
        {

            public int ID
            {
                get;
                set;
            }
            public string Name
            {
                get;
                set;
            }
        }
        public class Top_Company
        {
            public List<Top_Companies> Companies
            {
                get;
                set;
            }
        }
        public class Top_Companies
        {

            public int ID
            {
                get;
                set;
            }
            public string Name
            {
                get;
                set;
            }
        }

        public class ServiceRequestCount
        {
            public int Year { get; set; }
            public int Month { get; set; }
            public string Type { get; set; }
            public int Count { get; set; }
            public string Date { get; set; }
            public int StatusID { get; set; }
            public List<int> StatusIDs { get; set; }
            public int StatusCount { get; set; }
            public string MonthName { get; set; }
            public string StatusName { get; set; }
            public int CompletedCount { get; set; }
            public int PendingCount { get; set; }
            public int InProgressCount { get; set; }
            public int OnHoldCount { get; set; }
            public int ClosedCount { get; set; }
            public double TimeDiff { get; set; }
            public string AvgResolution { get; set; }
            public int EscalatedCount { get; set; }
            public string CompanyName { get; set; }
            public string BranchName { get; set; }
            public int CompanyID { get; set; }
            public int BranchID { get; set; }
            public string Region { get; set; }
        }


        public class SelectionCriteria
        {
            public List<Selections> SelectedIDs
            {
                get;
                set;
            }
        }
        public class Selections
        {

            public int ID
            {
                get;
                set;
            }
            public string Name
            {
                get;
                set;
            }
        }

        public class RTS_Branch
        {
            public List<RTS_Branchs> Branchs
            {
                get;
                set;
            }
        }
        public class RTS_Branchs
        {

            public int ID
            {
                get;
                set;
            }
            public string Name
            {
                get;
                set;
            }
        }
        #endregion



    }
}
