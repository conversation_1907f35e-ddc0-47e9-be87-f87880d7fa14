﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreLogsheetReportServices
    {

        #region ::: SelReportData Uday Kumar J B 28-09-2024 :::
        /// <summary>
        /// To select All Log Report Data
        /// </summary>
        ///
        public static IActionResult SelReportData(string connString, SelReportDataList SelReportDataobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            var jsonData = default(dynamic);
            var ReportData = default(dynamic);
            int Count = 0;
            int Total = 0;
            int CompanyID = Convert.ToInt32(SelReportDataobj.Company_ID);
            IEnumerable<logsheetreportclass> Result = null;
            string Filterlogsheetfromuser = "";
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));

            try
            {
                string branchID = SelReportDataobj.branchID.TrimEnd(new char[] { ',' });
                string companyID = SelReportDataobj.companyID.TrimEnd(new char[] { ',' });

                int loggedBranchID = Convert.ToInt32(SelReportDataobj.Branch);
                List<logsheetreportclass> ps = new List<logsheetreportclass>();
                string role = GetFilterLogsheetFromUser(CompanyID, connString); // Fetch role filter based on CompanyID

                if (!string.IsNullOrEmpty(role))
                {
                    Filterlogsheetfromuser = role;
                }

                // Prepare ADO.NET command to call the stored procedure
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("SP_ReportLogSheetSelReportData", conn)) // Change to correct SP name
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);
                        cmd.Parameters.AddWithValue("@BranchID", branchID);
                        cmd.Parameters.AddWithValue("@FromDate", Convert.ToDateTime(SelReportDataobj.Fromdate));
                        cmd.Parameters.AddWithValue("@EndDate", Convert.ToDateTime(SelReportDataobj.EndDate));
                        cmd.Parameters.AddWithValue("@FilterLogsheetFromUser", Filterlogsheetfromuser);

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                logsheetreportclass report = new logsheetreportclass
                                {
                                    GPSLOG_ID = Convert.ToInt32(reader["GPSLOG_ID"]),
                                    region = reader["region"].ToString(),
                                    Company_Name = reader["Company_Name"].ToString(),
                                    Branch_Name = reader["Branch_Name"].ToString(),
                                    User_LoginID = reader["User_LoginID"].ToString(),
                                    User_Name = reader["User_Name"].ToString(),
                                    LOGINDATETIME = reader["LOGINDATETIME"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["LOGINDATETIME"]),
                                    LOGOUTDATETIME = reader["LOGOUTDATETIME"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["LOGOUTDATETIME"]),
                                    Module_Description = reader["Module_Description"].ToString(),
                                    Menu_Description = reader["Menu_Description"].ToString(),
                                    ActionName = reader["ActionName"].ToString(),
                                    MODIFIEDDATE = Convert.ToDateTime(reader["MODIFIEDDATE"])
                                };
                                report.MODIFIEDDATEstring = report.MODIFIEDDATE.ToString("dd-MMM-yyyy hh:mm tt");
                                report.LOGOUTDATETIMEstring = report.LOGOUTDATETIME?.ToString("dd-MMM-yyyy hh:mm tt") ?? "";
                                report.LOGINDATETIMEstring = report.LOGINDATETIME?.ToString("dd-MMM-yyyy hh:mm tt") ?? "";
                                ps.Add(report);
                            }
                        }
                    }
                }

                // Process the result like the LINQ method
                Result = from a in ps
                         select new logsheetreportclass()
                         {
                             GPSLOG_ID = a.GPSLOG_ID,
                             region = a.region,
                             Company_Name = a.Company_Name,
                             Branch_Name = a.Branch_Name,
                             User_LoginID = a.User_LoginID,
                             User_Name = a.User_Name,
                             LOGINDATETIME = a.LOGINDATETIME,
                             LOGOUTDATETIME = a.LOGOUTDATETIME,
                             Module_Description = a.Module_Description,
                             Menu_Description = a.Menu_Description,
                             ActionName = a.ActionName,
                             MODIFIEDDATE = a.MODIFIEDDATE,
                             MODIFIEDDATEstring = a.MODIFIEDDATEstring,
                             LOGOUTDATETIMEstring = a.LOGOUTDATETIMEstring,
                             LOGINDATETIMEstring = a.LOGINDATETIMEstring
                         };

                IQueryable<logsheetreportclass> IQResultReport = Result.AsQueryable();

                if (_search)
                {
                    // Parse and decrypt the filters
                    Filters filtersObj = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(filters))).ToObject<Filters>();

                    // Ensure filtersObj is not null and its filter collection has more than 0 items
                    if (filtersObj != null && filtersObj.rules.Count > 0)
                    {
                        // Perform the FilterSearch operation
                        IQResultReport = IQResultReport.FilterSearch<logsheetreportclass>(filtersObj);
                    }
                }
                else if (advnce && !string.IsNullOrEmpty(advnceFilters))
                {
                    AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<AdvanceFilter>();
                    IQResultReport = IQResultReport.AdvanceSearch(advnfilter);
                    page = 1;
                }

                IQResultReport = IQResultReport.OrderByField<logsheetreportclass>(sidx, sord);
                Count = IQResultReport.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                jsonData = new
                {
                    total = Total,
                    page = page,
                    records = Count,
                    data = IQResultReport.ToList().Paginate(page, rows),
                    filter = filters,
                    IQResultReport
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }

        // Utility method to get filter logsheet role value
        private static string GetFilterLogsheetFromUser(int CompanyID, String connString)
        {
            string filterLogsheetFromUser = string.Empty;

            using (SqlConnection conn = new SqlConnection(connString))
            {
                using (SqlCommand cmd = new SqlCommand("SP_LogSheetReportGetFilterLogsheetReportfromUser", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@CompanyID", CompanyID);

                    conn.Open();
                    object result = cmd.ExecuteScalar(); // Since we are fetching a single value, ExecuteScalar is appropriate
                    if (result != null && result != DBNull.Value)
                    {
                        filterLogsheetFromUser = result.ToString();
                    }
                }
            }

            return filterLogsheetFromUser;
        }
        #endregion


        #region ::: To Export  Uday Kumar J B 28-09-2024:::
        /// <summary>
        /// To Export 
        /// </summary>
        /// <returns>...</returns>
        public static async Task<object> Export(ReportDataExportList ReportDataExportobj, string connString, string filter, string advnceFilter, string sidx, string sord)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                var Logsheetdata = default(dynamic);
                int userID = ReportDataExportobj.User_ID;
                DataTable dt = new DataTable();
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ReportDataExportobj.UserCulture.ToString(), "Region").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ReportDataExportobj.UserCulture.ToString(), "CompanyName").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ReportDataExportobj.UserCulture.ToString(), "BranchName").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ReportDataExportobj.UserCulture.ToString(), "LoginID").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ReportDataExportobj.UserCulture.ToString(), "UserName").ToString());
                dt.Columns.Add("Login Date");
                dt.Columns.Add("Logout Date");
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ReportDataExportobj.UserCulture.ToString(), "modulename").ToString());
                dt.Columns.Add("Master/Transaction/Report");
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ReportDataExportobj.UserCulture.ToString(), "actionname").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ReportDataExportobj.UserCulture.ToString(), "actiondate").ToString());



                DataTable dtAlignment = new DataTable();
                dtAlignment.Columns.Add("Region");
                dtAlignment.Columns.Add("CompanyName");
                dtAlignment.Columns.Add("BranchName");
                dtAlignment.Columns.Add("LoginID");
                dtAlignment.Columns.Add("UserName");
                dtAlignment.Columns.Add("Login Date");
                dtAlignment.Columns.Add("Logout Date");
                dtAlignment.Columns.Add("modulename");
                dtAlignment.Columns.Add("Master/Transaction/Report");
                dtAlignment.Columns.Add("actionname");
                dtAlignment.Columns.Add("actiondate");


                dtAlignment.Rows.Add(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);

                Logsheetdata = SelReportDataExport(connString, "GPSLOG_ID", "asc", 1, 100000, Convert.ToString(ReportDataExportobj.branchID), Convert.ToString(ReportDataExportobj.companyID), Convert.ToString(ReportDataExportobj.FromDate), Convert.ToString(ReportDataExportobj.EndDate), ReportDataExportobj.Company_ID, ReportDataExportobj.Branch, advnceFilter, filter);

                var logsheetDataObject = Logsheetdata.Value; // This should be the actual object holding your data

                // Assuming the structure contains "data", extract it as a List<logsheetreportclass>
                List<logsheetreportclass> iLogSheetArray = logsheetDataObject.data as List<logsheetreportclass>;
                int count = iLogSheetArray.Count();

                for (int i = 0; i < count; i++)
                {
                    dt.Rows.Add(iLogSheetArray.ElementAt(i).region,//1
                        iLogSheetArray.ElementAt(i).Company_Name,//2
                        iLogSheetArray.ElementAt(i).Branch_Name,//3
                        iLogSheetArray.ElementAt(i).User_LoginID,//4
                        iLogSheetArray.ElementAt(i).User_Name,//5
                        iLogSheetArray.ElementAt(i).LOGINDATETIMEstring,//6
                        iLogSheetArray.ElementAt(i).LOGOUTDATETIMEstring,//7
                        iLogSheetArray.ElementAt(i).Module_Description,//8                        
                        iLogSheetArray.ElementAt(i).Menu_Description,//9
                        iLogSheetArray.ElementAt(i).ActionName,//10
                        iLogSheetArray.ElementAt(i).MODIFIEDDATEstring);//11

                }
                ExportList reportExportList = new ExportList
                {
                    Company_ID = ReportDataExportobj.Company_ID, // Assuming this is available in ExportObj
                    Branch = ReportDataExportobj.Branch,
                    dt1 = dtAlignment,


                    dt = dt,

                    FileName = "LogSheetReport", // Set a default or dynamic filename
                    Header = CommonFunctionalities.GetResourceString(ReportDataExportobj.UserCulture.ToString(), "Logsheetreport").ToString(), // Set a default or dynamic header
                    exprtType = ReportDataExportobj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                    UserCulture = ReportDataExportobj.UserCulture
                };
                var result = await DocumentExport.Export(reportExportList, connString, LogException);
                return result.Value;
                // DocumentExport.Export(ReportDataExportobj.exprtType, dt, dtAlignment, "LogSheetReport", CommonFunctionalities.GetGlobalResourceObject(ReportDataExportobj.UserCulture.ToString(), "Logsheetreport").ToString());
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return null;
        }

        public static ActionResult SelReportDataExport(string connString, string sidx, string sord, int page, int rows, string branchID, string companyID, string Fromdate, string EndDate, int Company_ID, int Branch, string advnceFilters, string filter)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            object jsonData = null; // Avoid dynamic typing to keep structure clear
            List<logsheetreportclass> ps = new List<logsheetreportclass>();
            try
            {
                branchID = branchID.TrimEnd(new char[] { ',' });
                companyID = companyID.TrimEnd(new char[] { ',' });
                int CompanyID = Convert.ToInt32(Company_ID);
                string Filterlogsheetfromuser = GetFilterLogsheetFromUser(CompanyID, connString); // Fetch role filter based on CompanyID

                // ADO.NET command to call the stored procedure
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("SP_ReportLogSheetSelReportData", conn)) // Change to correct SP name
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);
                        cmd.Parameters.AddWithValue("@BranchID", branchID);
                        cmd.Parameters.AddWithValue("@FromDate", Convert.ToDateTime(Fromdate));
                        cmd.Parameters.AddWithValue("@EndDate", Convert.ToDateTime(EndDate));
                        cmd.Parameters.AddWithValue("@FilterLogsheetFromUser", Filterlogsheetfromuser);

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var report = new logsheetreportclass
                                {
                                    GPSLOG_ID = Convert.ToInt32(reader["GPSLOG_ID"]),
                                    region = reader["region"].ToString(),
                                    Company_Name = reader["Company_Name"].ToString(),
                                    Branch_Name = reader["Branch_Name"].ToString(),
                                    User_LoginID = reader["User_LoginID"].ToString(),
                                    User_Name = reader["User_Name"].ToString(),
                                    LOGINDATETIME = reader["LOGINDATETIME"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["LOGINDATETIME"]),
                                    LOGOUTDATETIME = reader["LOGOUTDATETIME"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["LOGOUTDATETIME"]),
                                    Module_Description = reader["Module_Description"].ToString(),
                                    Menu_Description = reader["Menu_Description"].ToString(),
                                    ActionName = reader["ActionName"].ToString(),
                                    MODIFIEDDATE = Convert.ToDateTime(reader["MODIFIEDDATE"])
                                };
                                report.MODIFIEDDATEstring = report.MODIFIEDDATE.ToString("dd-MMM-yyyy hh:mm tt");
                                report.LOGOUTDATETIMEstring = report.LOGOUTDATETIME?.ToString("dd-MMM-yyyy hh:mm tt") ?? "";
                                report.LOGINDATETIMEstring = report.LOGINDATETIME?.ToString("dd-MMM-yyyy hh:mm tt") ?? "";
                                ps.Add(report);
                            }
                        }
                    }
                }

                // LINQ-like filtering and ordering
                var Result = ps.AsQueryable();

                // Apply filter if present
                if (!string.IsNullOrEmpty(filter) && filter != "null" && filter != "undefined")
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filter)).ToObject<Filters>();
                    if (filtersObj.rules.Count > 0)
                    {
                        Result = Result.FilterSearch(filtersObj); // Custom filter logic
                    }
                }

                // Apply advanced filters if present
                if (!string.IsNullOrEmpty(advnceFilters) && advnceFilters != "null")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Common.DecryptString(advnceFilters)).ToObject<AdvanceFilter>();
                    Result = Result.AdvanceSearch(advnfilter); // Custom advanced filter logic
                }

                // Order and Paginate
                Result = Result.OrderByField<logsheetreportclass>(sidx, sord);
                int Count = Result.Count();
                int Total = rows > 0 ? (int)Math.Ceiling((double)Count / rows) : 0;
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                var paginatedData = Result.ToList().Paginate(page, rows);

                // Build the jsonData object
                jsonData = new
                {
                    total = Total,
                    page = page,
                    records = Count,
                    data = paginatedData,
                    filter = filter
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ": " + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { error = ex.Message }); // Return error in JSON format
            }

            return new JsonResult(jsonData);
        }


        #endregion


        #region ::: Log Sheet Report list and obj classes  Uday Kumar J B 28-09-2024:::
        /// <summary>
        /// To list and obj classes
        /// </summary>
        /// <returns>...</returns>
        public class ReportDataExportList
        {
            public int exprtType { get; set; }
            public int User_ID { get; set; }
            public string UserCulture { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filter { get; set; }
            public string advanceFilter { get; set; }
            public string branchID { get; set; }
            public string companyID { get; set; }
            public DateTime EndDate { get; set; }
            public DateTime FromDate { get; set; }
        }

        public class SelReportDataList
        {
            public int Company_ID { get; set; }
            public string branchID { get; set; }
            public string companyID { get; set; }
            public int Branch { get; set; }
            public DateTime Fromdate { get; set; }
            public DateTime EndDate { get; set; }

        }
        #endregion


        #region ::: Log Sheet Report classes  Uday Kumar J B 28-09-2024:::
        /// <summary>
        /// To  classes
        /// </summary>
        /// <returns>...</returns>
        public class logsheetreportclass
        {

            public string region { get; set; }
            public string Company_Name { get; set; }
            public string Branch_Name { get; set; }
            public string User_LoginID { get; set; }
            public string User_Name { get; set; }
            public DateTime? LOGINDATETIME { get; set; }
            public DateTime? LOGOUTDATETIME { get; set; }
            public string Module_Description { get; set; }
            public string Menu_Description { get; set; }
            public string ActionName { get; set; }
            public DateTime MODIFIEDDATE { get; set; }
            public string MODIFIEDDATEstring { get; set; }
            public string LOGINDATETIMEstring { get; set; }
            public string LOGOUTDATETIMEstring { get; set; }
            public int GPSLOG_ID { get; set; }
        }
        #endregion

    }
}
