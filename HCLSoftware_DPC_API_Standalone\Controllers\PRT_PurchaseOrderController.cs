﻿using SharedAPIClassLibrary_AMERP;
using SharedAPIClassLibrary_AMERP.Services;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using LS = SharedAPIClassLibrary_AMERP.Utilities;
namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class PRT_PurchaseOrderController : ApiController
    {
        #region PurchaseOrderInterface /Vinay N
        /// <summary>
        /// PurchaseOrderInterface
        /// </summary>
        /// <param name="PurchaseOrderInterfaceObj"></param>
        /// <returns></returns>
        [Route("api/PRT_PurchaseOrder/PurchaseOrderInterface")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PurchaseOrderInterface([FromBody] PurchaseOrderInterfaceList PurchaseOrderInterfaceObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["YANMAR"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = PRT_PurchaseOrderServices.PurchaseOrderInterface(PurchaseOrderInterfaceObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion
    }
}