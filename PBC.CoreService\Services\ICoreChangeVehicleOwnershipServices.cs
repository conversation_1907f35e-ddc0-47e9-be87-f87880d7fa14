using Microsoft.AspNetCore.Mvc;
using PBC.CoreService.Utilities.DTOs;

namespace PBC.CoreService.Services
{
    public interface ICoreChangeVehicleOwnershipServices
    {
        /// <summary>
        /// To select All product
        /// </summary>
        Task<IActionResult> SelectAsync(string connString, SelectCoreChangeVehicleOwnershipList SelectCoreChangeVehicleOwnershipobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters);

        /// <summary>
        /// To Save Product Customer Details
        /// </summary>
        Task<IActionResult> SaveProductCustomerDetailsAsync(string connString, SaveProductCustomerDetailsCoreChangeVehicleOwnershipList SaveProductCustomerDetailsCoreChangeVehicleOwnershipobj);

        /// <summary>
        /// To Select Field Search SerialNumber
        /// </summary>
        Task<IActionResult> SelectFieldSearchSerialNumberAsync(string connString, SelectFieldSearchSerialNumberList SelectFieldSearchSerialNumberobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters);

        /// <summary>
        /// Method To Get Products For Serial
        /// </summary>
        Task<IActionResult> GetProductForSerialAsync(string connString, GetProductForSerialList GetProductForSerialobj);

        /// <summary>
        /// To get Customer details
        /// </summary>
        Task<IActionResult> GetPartyDetailsAsync(string connString, GetPartyDetailsCoreChangeVehicleOwnershipList GetPartyDetailsCoreChangeVehicleOwnershipobj);

        /// <summary>
        /// To select menus of respective module
        /// </summary>
        Task<IActionResult> SelectPartyDetailGridAsync(string connString, SelectPartyDetailGridCList SelectPartyDetailGridobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters);

        /// <summary>
        /// To get Customer Search 
        /// </summary>
        Task<IActionResult> SelectFieldSearchPartyAsync(string connString, SelectFieldSearchPartyList SelectFieldSearchPartyobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters);

        /// <summary>
        /// To get Customer details by ID
        /// </summary>
        Task<IActionResult> GetPartyDetailsbyIDAsync(string connString, GetPartyDetailsbyIDaList GetPartyDetailsbyIDobj);
    }
}
