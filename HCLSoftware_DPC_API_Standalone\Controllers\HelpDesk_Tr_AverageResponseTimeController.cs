﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.HelpDesk_Tr_AverageResponseTimeServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class HelpDesk_Tr_AverageResponseTimeController : ApiController
    {


        #region ::: Select Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To select Year wise Details
        /// </summary> 
        [Route("api/HelpDesk_Tr_AverageResponseTime/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] HelpDesk_Tr_AverageResponseTimeSelectList HelpDesk_Tr_AverageResponseTimeSelectobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Tr_AverageResponseTimeServices.Select(HelpDesk_Tr_AverageResponseTimeSelectobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectMonthWise Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To Select Month Wise Avearage Response Time
        /// </summary>
        [Route("api/HelpDesk_Tr_AverageResponseTime/SelectMonthWise")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectMonthWise([FromBody] HelpDesk_Tr_AverageResponseTimeSelectList HelpDesk_Tr_AverageResponseTimeSelectobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Tr_AverageResponseTimeServices.SelectMonthWise(HelpDesk_Tr_AverageResponseTimeSelectobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectDateWise Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To Select Date Wise
        /// </summary>
        [Route("api/HelpDesk_Tr_AverageResponseTime/SelectDateWise")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectDateWise([FromBody] HelpDesk_Tr_AverageResponseTimeSelectList HelpDesk_Tr_AverageResponseTimeSelectobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Tr_AverageResponseTimeServices.SelectDateWise(HelpDesk_Tr_AverageResponseTimeSelectobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion



        #region ::: SelectDateWise Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To Select Date Wise
        /// </summary>
        [Route("api/HelpDesk_Tr_AverageResponseTime/SelectDateDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectDateDetails([FromBody] HelpDesk_Tr_AverageResponseSelectDateDetailsList HelpDesk_Tr_AverageResponseSelectDateDetailsobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Tr_AverageResponseTimeServices.SelectDateDetails(HelpDesk_Tr_AverageResponseSelectDateDetailsobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion



        #region ::: SelectCustomer Uday Kumar J B 15-11-2024 :::
        /// <summary>
        /// To get Customers
        /// </summary> 
        ///  
        [Route("api/HelpDesk_Tr_AverageResponseTime/SelectCustomer")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCustomer([FromBody] HelpDesk_Tr_AverageResponseSelectCustomerList HelpDesk_Tr_AverageResponseSelectCustomerobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_AverageResponseTimeServices.SelectCustomer(HelpDesk_Tr_AverageResponseSelectCustomerobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectDealer Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To get Dealer 
        /// </summary> 
        /// 
        [Route("api/HelpDesk_Tr_AverageResponseTime/SelectDealer")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectDealer([FromBody] HelpDesk_Tr_AverageResponseSelectCustomerList HelpDesk_Tr_AverageResponseSelectCustomerobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_AverageResponseTimeServices.SelectDealer(HelpDesk_Tr_AverageResponseSelectCustomerobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectModel Uday Kumar J B 15-11-2024 :::
        /// <summary>
        /// To Select Model
        /// </summary> 
        /// 
        [Route("api/HelpDesk_Tr_AverageResponseTime/SelectModel")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectModel([FromBody] HelpDesk_Tr_AverageResponseSelectCustomerList HelpDesk_Tr_AverageResponseSelectCustomerobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_AverageResponseTimeServices.SelectModel(HelpDesk_Tr_AverageResponseSelectCustomerobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectIssueArea Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To get Refrence Master records for a Master
        /// </summary> 
        ///  
        [Route("api/HelpDesk_Tr_AverageResponseTime/SelectIssueArea")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectIssueArea([FromBody] HelpDesk_Tr_AverageResponseSelectCustomerList HelpDesk_Tr_AverageResponseSelectCustomerobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_AverageResponseTimeServices.SelectIssueArea(HelpDesk_Tr_AverageResponseSelectCustomerobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectCallProirity Uday Kumar J B 15-11-2024:::
        /// <summary>
        /// To Select CallPriority
        /// </summary> 
        /// 
        [Route("api/HelpDesk_Tr_AverageResponseTime/SelectCallPriority")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCallPriority([FromBody] HelpDesk_Tr_AverageResponseSelectCustomerList HelpDesk_Tr_AverageResponseSelectCustomerobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_AverageResponseTimeServices.SelectCallPriority(HelpDesk_Tr_AverageResponseSelectCustomerobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: Export Uday Kumar J B  15-11-2024:::
        /// <summary>
        /// Exporting Company Grid
        /// </summary>
        /// 
        [Route("api/HelpDesk_Tr_AverageResponseTime/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] HelpDesk_Tr_AverageResponseTimeExportList HelpDesk_Tr_AverageResponseTimeExportobj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HelpDesk_Tr_AverageResponseTimeExportobj.sidx;
            string sord = HelpDesk_Tr_AverageResponseTimeExportobj.sord;
            string filter = HelpDesk_Tr_AverageResponseTimeExportobj.filter;
            string advnceFilter = HelpDesk_Tr_AverageResponseTimeExportobj.advanceFilter;

            try
            {


                Object Response = await HelpDesk_Tr_AverageResponseTimeServices.Export(HelpDesk_Tr_AverageResponseTimeExportobj, connstring, LogException, filter, advnceFilter, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion





    }
}