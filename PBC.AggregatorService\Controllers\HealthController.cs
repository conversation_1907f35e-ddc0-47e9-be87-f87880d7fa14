using Microsoft.AspNetCore.Mvc;
using PBC.AggregatorService.Services;

namespace PBC.AggregatorService.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class HealthController : ControllerBase
    {
        private readonly IHealthService _healthService;

        public HealthController(IHealthService healthService)
        {
            _healthService = healthService;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var healthStatus = await _healthService.CheckHealthAsync();
            return Ok(healthStatus);
        }

        [HttpGet("status")]
        public IActionResult GetStatus()
        {
            return Ok(new
            {
                Service = "PBC.AggregatorService",
                Status = "Healthy",
                Timestamp = DateTime.UtcNow,
                Version = "1.0.0"
            });
        }
    }
}
