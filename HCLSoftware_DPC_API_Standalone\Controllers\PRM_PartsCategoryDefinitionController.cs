﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.PRM_PartsCategoryDefinitionServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class PRM_PartsCategoryDefinitionController : ApiController
    {

        #region ::: Select Uday Kumar J B 20-08-2024 :::
        /// <summary>
        /// To Select PartsCategoryDefinition
        /// </summary>
        /// 
        [Route("api/PRM_PartsCategoryDefinition/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] SelectPRM_PartsCategoryDefinitionList SelectPRM_PartsCategoryDefinitionobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = " ";


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = PRM_PartsCategoryDefinitionServices.Select(connString, SelectPRM_PartsCategoryDefinitionobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Save Uday Kumar J B 20-08-2024:::
        /// <summary>
        /// To Insert and Update PartsCategoryDefinition
        /// </summary>
        ///
        [Route("api/PRM_PartsCategoryDefinition/Save")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Save([FromBody] SavePRM_PartsCategoryDefinitionList SavePRM_PartsCategoryDefinitionobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = PRM_PartsCategoryDefinitionServices.Save(connString, SavePRM_PartsCategoryDefinitionobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckPartsCategory Uday Kumar J B 20-08-2024:::
        /// <summary>
        /// To Check PartsCategoryDefinition already exists 
        /// </summary>
        /// 
        [Route("api/PRM_PartsCategoryDefinition/CheckPartsCategory")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckPartsCategory([FromBody] CheckPartsCategoryPRM_PartsCategoryDefinitionList CheckPartsCategoryPRM_PartsCategoryDefinitionobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = PRM_PartsCategoryDefinitionServices.CheckPartsCategory(connString, CheckPartsCategoryPRM_PartsCategoryDefinitionobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Delete Uday Kumar J B 20-08-2024 :::
        /// <summary>
        /// To Delete PartsCategoryDefinition
        /// </summary>
        ///  
        [Route("api/PRM_PartsCategoryDefinition/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeletePRM_PartsCategoryDefinitionList DeletePRM_PartsCategoryDefinitionobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = PRM_PartsCategoryDefinitionServices.Delete(connString, DeletePRM_PartsCategoryDefinitionobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Export Uday Kumar J B 20-08-2024 :::
        /// <summary>
        /// To Export 
        /// </summary> 
        [Route("api/PRM_PartsCategoryDefinition/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Export([FromBody] SelectPRM_PartsCategoryDefinitionList SelectPRM_PartsCategoryDefinitionobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = " ";


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = PRM_PartsCategoryDefinitionServices.Export(connString, SelectPRM_PartsCategoryDefinitionobj, filters, advnceFilters, filters, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion

    }
}