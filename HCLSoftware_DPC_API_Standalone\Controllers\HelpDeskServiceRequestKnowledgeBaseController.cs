﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Internal;
using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class HelpDeskServiceRequestKnowledgeBaseController : ApiController
    {
        #region InitialSetup
        /// <summary>
        /// InitialSetup
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequestKnowledgeBase/InitialSetup")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InitialSetup([FromBody] KnowledgeBase_InitialSetupList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskServiceRequestKnowledgeBaseServices.InitialSetup(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion

        #region GetContent
        /// <summary>
        /// GetContent
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequestKnowledgeBase/GetContent")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetContent([FromBody] GetContentList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskServiceRequestKnowledgeBaseServices.GetContent(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetAllArea
        /// <summary>
        /// GetAllArea
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequestKnowledgeBase/GetAllArea")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetAllArea([FromBody] GetAllAreaList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskServiceRequestKnowledgeBaseServices.GetAllArea(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region CheckDuplicate_Sibling_Rename
        /// <summary>
        /// CheckDuplicate_Sibling_Rename
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequestKnowledgeBase/CheckDuplicate_Sibling_Rename")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckDuplicate_Sibling_Rename([FromBody] CheckDuplicate_Sibling_RenameList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskServiceRequestKnowledgeBaseServices.CheckDuplicate_Sibling_Rename(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion

        #region AddSibling
        /// <summary>
        /// AddSibling
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequestKnowledgeBase/AddSibling")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult AddSibling([FromBody] AddSiblingList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskServiceRequestKnowledgeBaseServices.AddSibling(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region CheckDuplicateSubfolder
        /// <summary>
        /// CheckDuplicateSubfolder
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequestKnowledgeBase/CheckDuplicateSubfolder")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckDuplicateSubfolder([FromBody] CheckDuplicateSubfolderList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskServiceRequestKnowledgeBaseServices.CheckDuplicateSubfolder(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region AddSubfolder
        /// <summary>
        /// AddSubfolder
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequestKnowledgeBase/AddSubfolder")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult AddSubfolder([FromBody] AddSubfolderList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskServiceRequestKnowledgeBaseServices.AddSubfolder(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region Rename
        /// <summary>
        /// Rename
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequestKnowledgeBase/Rename")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Rename([FromBody] RenameList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskServiceRequestKnowledgeBaseServices.Rename(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region DeleteArticle
        /// <summary>
        /// DeleteArticle
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequestKnowledgeBase/DeleteArticle")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteArticle([FromBody] DeleteArticleList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskServiceRequestKnowledgeBaseServices.DeleteArticle(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region CheckDuplicateArticle
        /// <summary>
        /// CheckDuplicateArticle
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequestKnowledgeBase/CheckDuplicateArticle")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckDuplicateArticle([FromBody] CheckDuplicateArticleList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskServiceRequestKnowledgeBaseServices.CheckDuplicateArticle(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region UpdateArticle
        /// <summary>
        /// UpdateArticle
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequestKnowledgeBase/UpdateArticle")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateArticle([FromBody] UpdateArticleList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskServiceRequestKnowledgeBaseServices.UpdateArticle(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region SaveArticle
        /// <summary>
        /// SaveArticle
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequestKnowledgeBase/SaveArticle")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveArticle([FromBody] SaveArticleList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskServiceRequestKnowledgeBaseServices.SaveArticle(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region CheckAttachment
        /// <summary>
        /// CheckAttachment
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequestKnowledgeBase/CheckAttachment")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckAttachment([FromBody] CheckAttachment_List Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskServiceRequestKnowledgeBaseServices.CheckAttachment(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region LoadTree
        /// <summary>
        /// LoadTree
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequestKnowledgeBase/LoadTree")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadTree([FromBody] LoadTreeList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskServiceRequestKnowledgeBaseServices.LoadTree(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ReloadTree
        /// <summary>
        /// ReloadTree
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequestKnowledgeBase/ReloadTree")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ReloadTree([FromBody] ReloadTreeList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskServiceRequestKnowledgeBaseServices.ReloadTree(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion

        #region Delete
        /// <summary>
        /// Delete
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequestKnowledgeBase/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] KnowlegdeBase_DeleteList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskServiceRequestKnowledgeBaseServices.Delete(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion

        #region DeleteAttachment
        /// <summary>
        /// DeleteAttachment
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskServiceRequestKnowledgeBase/DeleteAttachment")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteAttachment([FromBody] KnowlegdeBase_DeleteList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskServiceRequestKnowledgeBaseServices.DeleteAttachment(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: To SaveFileToServer:::
        /// <summary>
        /// To SaveFileToServer
        /// </summary>
        [Route("api/HelpDeskServiceRequestKnowledgeBase/SaveFileToServer")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> SaveFileToServer(KnowledgeBase_SaveFileToServerList OBJ)
        {
            try
            {
                var provider = new MultipartMemoryStreamProvider();

                // Read the multipart form data into the provider
                await Request.Content.ReadAsMultipartAsync(provider);

                // Retrieve form data
                string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                IFormFile postedFile = null;


                foreach (var content in provider.Contents)
                {
                    var name = content.Headers.ContentDisposition.Name.Trim('"');

                    // Check if content is a form field or a file
                    if (content.Headers.ContentDisposition.FileName == null)
                    {
                        // Read form field data
                        var value = await content.ReadAsStringAsync();

                        switch (name)
                        {
                            case "Id":
                                OBJ.Id = Convert.ToInt32(value);
                                break;
                            case "Company_ID":
                                OBJ.Company_ID = Convert.ToInt32(value);
                                break;
                            default:
                                // Handle other form fields if necessary
                                break;
                        }
                    }
                    else
                    {
                        // Check if the content is the file we want
                        if (name == "formFile")
                        {
                            // Read file content
                            var stream = await content.ReadAsStreamAsync();
                            postedFile = new FormFile(stream, 0, stream.Length, content.Headers.ContentDisposition.Name.Trim('"'), content.Headers.ContentDisposition.FileName.Trim('"'));
                        }
                    }
                }

                // Call your service method to process the data
                var Response = default(dynamic);
                Response = HelpDeskServiceRequestKnowledgeBaseServices.SaveFileToServer(postedFile, connString, LogException, OBJ);

                return Ok(Response.Value);
            }
            catch (Exception ex)
            {
                // Log or handle exceptions
                return InternalServerError(ex);
            }
        }
        #endregion

    }
}