//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resource_ja {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource_ja() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.Resource_ja", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 放棄する.
        /// </summary>
        internal static string Abandon {
            get {
                return ResourceManager.GetString("Abandon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 放棄する.
        /// </summary>
        internal static string AbandonDelayReason {
            get {
                return ResourceManager.GetString("AbandonDelayReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 放棄された.
        /// </summary>
        internal static string Abandoned {
            get {
                return ResourceManager.GetString("Abandoned", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 受け入れる.
        /// </summary>
        internal static string Accept {
            get {
                return ResourceManager.GetString("Accept", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 口座番号.
        /// </summary>
        internal static string AccountNumber {
            get {
                return ResourceManager.GetString("AccountNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アクション.
        /// </summary>
        internal static string Action {
            get {
                return ResourceManager.GetString("Action", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to によるアクション.
        /// </summary>
        internal static string ActionBy {
            get {
                return ResourceManager.GetString("ActionBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アクション日付.
        /// </summary>
        internal static string ActionDate {
            get {
                return ResourceManager.GetString("ActionDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 次のサービスのための行動.
        /// </summary>
        internal static string ActionForNextService {
            get {
                return ResourceManager.GetString("ActionForNextService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アクション名.
        /// </summary>
        internal static string ActionName {
            get {
                return ResourceManager.GetString("ActionName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アクション備考.
        /// </summary>
        internal static string ActionRemarks {
            get {
                return ResourceManager.GetString("ActionRemarks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アクション備考最大制限を超えました.
        /// </summary>
        internal static string ActionRemarksMaxlimitexceeded {
            get {
                return ResourceManager.GetString("ActionRemarksMaxlimitexceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アクション.
        /// </summary>
        internal static string Actions {
            get {
                return ResourceManager.GetString("Actions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アクティブですか？.
        /// </summary>
        internal static string Active {
            get {
                return ResourceManager.GetString("Active", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to からアクティブ.
        /// </summary>
        internal static string ActiveFrom {
            get {
                return ResourceManager.GetString("ActiveFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 日付からActiveは、現在までに、アクティブより大きくすることはできません.
        /// </summary>
        internal static string ActiveFromdatecannotbegreaterthanActiveTodate {
            get {
                return ResourceManager.GetString("ActiveFromdatecannotbegreaterthanActiveTodate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to するために、Active.
        /// </summary>
        internal static string ActiveTo {
            get {
                return ResourceManager.GetString("ActiveTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 実働時間.
        /// </summary>
        internal static string ActualHours {
            get {
                return ResourceManager.GetString("ActualHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 加える.
        /// </summary>
        internal static string Add {
            get {
                return ResourceManager.GetString("Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アクションの追加.
        /// </summary>
        internal static string AddAction {
            get {
                return ResourceManager.GetString("AddAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ブランチの追加.
        /// </summary>
        internal static string AddBranch {
            get {
                return ResourceManager.GetString("AddBranch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 支店税の詳細を追加.
        /// </summary>
        internal static string AddBranchTaxDetails {
            get {
                return ResourceManager.GetString("AddBranchTaxDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ブランドを追加.
        /// </summary>
        internal static string AddBrands {
            get {
                return ResourceManager.GetString("AddBrands", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 会社を追加.
        /// </summary>
        internal static string AddCompany {
            get {
                return ResourceManager.GetString("AddCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 会社関係を追加.
        /// </summary>
        internal static string AddCompanyRelation {
            get {
                return ResourceManager.GetString("AddCompanyRelation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 法人税の詳細を追加.
        /// </summary>
        internal static string AddCompanyTaxDetails {
            get {
                return ResourceManager.GetString("AddCompanyTaxDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 会社の規約を追加.
        /// </summary>
        internal static string AddCompanyTerms {
            get {
                return ResourceManager.GetString("AddCompanyTerms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to コンポーネントの詳細を追加.
        /// </summary>
        internal static string addcomponentdetails {
            get {
                return ResourceManager.GetString("addcomponentdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 顧客を追加.
        /// </summary>
        internal static string addcustomer {
            get {
                return ResourceManager.GetString("addcustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to カスタマー見積を追加.
        /// </summary>
        internal static string AddCustomerQuotation {
            get {
                return ResourceManager.GetString("AddCustomerQuotation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 従業員を追加.
        /// </summary>
        internal static string AddEmployee {
            get {
                return ResourceManager.GetString("AddEmployee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to イベントを追加.
        /// </summary>
        internal static string AddEvents {
            get {
                return ResourceManager.GetString("AddEvents", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フィルタの追加.
        /// </summary>
        internal static string AddFilter {
            get {
                return ResourceManager.GetString("AddFilter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツのフリー素材を追加.
        /// </summary>
        internal static string addfreestock {
            get {
                return ResourceManager.GetString("addfreestock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ファンクショングループを追加.
        /// </summary>
        internal static string AddFunctionGroup {
            get {
                return ResourceManager.GetString("AddFunctionGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ジョブ&amp;#183;カードを追加.
        /// </summary>
        internal static string AddJobCard {
            get {
                return ResourceManager.GetString("AddJobCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to マスターを追加.
        /// </summary>
        internal static string AddMaster {
            get {
                return ResourceManager.GetString("AddMaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to モデルを追加.
        /// </summary>
        internal static string addmodel {
            get {
                return ResourceManager.GetString("addmodel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 新しいパーツを追加する.
        /// </summary>
        internal static string addnewpart {
            get {
                return ResourceManager.GetString("addnewpart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 追加操作.
        /// </summary>
        internal static string addoperation {
            get {
                return ResourceManager.GetString("addoperation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作従業員の詳細を追加.
        /// </summary>
        internal static string AddOperationEmployeeDetails {
            get {
                return ResourceManager.GetString("AddOperationEmployeeDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツを追加する.
        /// </summary>
        internal static string AddPart {
            get {
                return ResourceManager.GetString("AddPart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツの価格を追加.
        /// </summary>
        internal static string addpartprice {
            get {
                return ResourceManager.GetString("addpartprice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツの追加.
        /// </summary>
        internal static string AddParts {
            get {
                return ResourceManager.GetString("AddParts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーティーを追加.
        /// </summary>
        internal static string AddParty {
            get {
                return ResourceManager.GetString("AddParty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to プリフィックスサフィックスを追加.
        /// </summary>
        internal static string AddPrefixSuffix {
            get {
                return ResourceManager.GetString("AddPrefixSuffix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 製品の追加.
        /// </summary>
        internal static string addproduct {
            get {
                return ResourceManager.GetString("addproduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 製品の詳細を追加.
        /// </summary>
        internal static string addproductdetail {
            get {
                return ResourceManager.GetString("addproductdetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 製品タイプを追加.
        /// </summary>
        internal static string addproducttype {
            get {
                return ResourceManager.GetString("addproducttype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツ製品タイプの詳細を追加.
        /// </summary>
        internal static string addproducttypedetails {
            get {
                return ResourceManager.GetString("addproducttypedetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to リクエストの追加.
        /// </summary>
        internal static string AddRequest {
            get {
                return ResourceManager.GetString("AddRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アドレス.
        /// </summary>
        internal static string Address {
            get {
                return ResourceManager.GetString("Address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 住所1行目.
        /// </summary>
        internal static string Address1 {
            get {
                return ResourceManager.GetString("Address1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 住所2行目.
        /// </summary>
        internal static string Address2 {
            get {
                return ResourceManager.GetString("Address2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 住所3.
        /// </summary>
        internal static string Address3 {
            get {
                return ResourceManager.GetString("Address3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Addresse旗.
        /// </summary>
        internal static string AddresseFlag {
            get {
                return ResourceManager.GetString("AddresseFlag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ロールの追加.
        /// </summary>
        internal static string AddRole {
            get {
                return ResourceManager.GetString("AddRole", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービスチャージを追加.
        /// </summary>
        internal static string addServiceCharge {
            get {
                return ResourceManager.GetString("addServiceCharge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービス料の詳細を追加.
        /// </summary>
        internal static string addServiceChargeDetails {
            get {
                return ResourceManager.GetString("addServiceChargeDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービス料を追加.
        /// </summary>
        internal static string addservicecharges {
            get {
                return ResourceManager.GetString("addservicecharges", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービスタイプを追加.
        /// </summary>
        internal static string AddServiceType {
            get {
                return ResourceManager.GetString("AddServiceType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サイトのアドレスを追加.
        /// </summary>
        internal static string addsiteaddress {
            get {
                return ResourceManager.GetString("addsiteaddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to スキルを追加.
        /// </summary>
        internal static string AddSkills {
            get {
                return ResourceManager.GetString("AddSkills", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to スペシャライゼーションを追加.
        /// </summary>
        internal static string AddSpecialization {
            get {
                return ResourceManager.GetString("AddSpecialization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ステップの追加.
        /// </summary>
        internal static string AddStep {
            get {
                return ResourceManager.GetString("AddStep", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ステップのリンクの追加.
        /// </summary>
        internal static string AddStepLink {
            get {
                return ResourceManager.GetString("AddStepLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 雑貨追加.
        /// </summary>
        internal static string AddSundry {
            get {
                return ResourceManager.GetString("AddSundry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 税コードを追加.
        /// </summary>
        internal static string AddTaxCode {
            get {
                return ResourceManager.GetString("AddTaxCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 租税構造を追加.
        /// </summary>
        internal static string addtaxstructure {
            get {
                return ResourceManager.GetString("addtaxstructure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 租税構造の詳細を追加.
        /// </summary>
        internal static string AddTaxStructureDetails {
            get {
                return ResourceManager.GetString("AddTaxStructureDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 租税構造の詳細を追加.
        /// </summary>
        internal static string addtaxtstructuredetails {
            get {
                return ResourceManager.GetString("addtaxtstructuredetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ユーザーの追加.
        /// </summary>
        internal static string AddUser {
            get {
                return ResourceManager.GetString("AddUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 保証の詳細を追加.
        /// </summary>
        internal static string addwarrantydetails {
            get {
                return ResourceManager.GetString("addwarrantydetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 仕事の詳細を追加.
        /// </summary>
        internal static string AddWorkDetails {
            get {
                return ResourceManager.GetString("AddWorkDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アドバンス検索.
        /// </summary>
        internal static string advancesearch {
            get {
                return ResourceManager.GetString("advancesearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to すべて.
        /// </summary>
        internal static string all {
            get {
                return ResourceManager.GetString("all", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 割り当てる.
        /// </summary>
        internal static string Allocate {
            get {
                return ResourceManager.GetString("Allocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 割り当てられた時間.
        /// </summary>
        internal static string AllocatedHours {
            get {
                return ResourceManager.GetString("AllocatedHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 割り当てはできません.
        /// </summary>
        internal static string AllocationNotPossible {
            get {
                return ResourceManager.GetString("AllocationNotPossible", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 首尾よくAllocted.
        /// </summary>
        internal static string AlloctedSuccessfully {
            get {
                return ResourceManager.GetString("AlloctedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alloctionに失敗しました.
        /// </summary>
        internal static string AlloctionFailed {
            get {
                return ResourceManager.GetString("AlloctionFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to すべてのキュー.
        /// </summary>
        internal static string AllQueue {
            get {
                return ResourceManager.GetString("AllQueue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 既に関連付けられたドロップダウンから選択してください.
        /// </summary>
        internal static string AlreadyAssociatedPleaseSelectfromDropDown {
            get {
                return ResourceManager.GetString("AlreadyAssociatedPleaseSelectfromDropDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 郵便番号.
        /// </summary>
        internal static string alreadyexists {
            get {
                return ResourceManager.GetString("alreadyexists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja already exists for the location.
        /// </summary>
        internal static string alreadyexistsforthelocation {
            get {
                return ResourceManager.GetString("alreadyexistsforthelocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 量.
        /// </summary>
        internal static string Amount {
            get {
                return ResourceManager.GetString("Amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 金額は空白です.
        /// </summary>
        internal static string AmountBlank {
            get {
                return ResourceManager.GetString("AmountBlank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 量が許容限度を超えている.
        /// </summary>
        internal static string AmountIsBeyondAcceptableLimit {
            get {
                return ResourceManager.GetString("AmountIsBeyondAcceptableLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 量がより小さくなければなりません.
        /// </summary>
        internal static string AmountShouldbeLessThan {
            get {
                return ResourceManager.GetString("AmountShouldbeLessThan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フローステップを働かせる.
        /// </summary>
        internal static string AND {
            get {
                return ResourceManager.GetString("AND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 承認制限.
        /// </summary>
        internal static string ApprovalLimit {
            get {
                return ResourceManager.GetString("ApprovalLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 4月.
        /// </summary>
        internal static string April {
            get {
                return ResourceManager.GetString("April", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 必ず削除したい.
        /// </summary>
        internal static string Aresurewanttodelete {
            get {
                return ResourceManager.GetString("Aresurewanttodelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to あなたがキャンセルしてもよろしいです.
        /// </summary>
        internal static string Areyousurewanttocancel {
            get {
                return ResourceManager.GetString("Areyousurewanttocancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to あなたは確信して削除したいのですか？.
        /// </summary>
        internal static string Areyousurewanttodelete {
            get {
                return ResourceManager.GetString("Areyousurewanttodelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to あなたが要求を放棄してもよろしいです.
        /// </summary>
        internal static string AreyousureyouwanttoAbandontheRequest {
            get {
                return ResourceManager.GetString("AreyousureyouwanttoAbandontheRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Are you sure you want to Log out.
        /// </summary>
        internal static string AreyousureyouwanttoLogout {
            get {
                return ResourceManager.GetString("AreyousureyouwanttoLogout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to は、イベントを移動してもよろしいですか？.
        /// </summary>
        internal static string areyousureyouwanttomovetheevent {
            get {
                return ResourceManager.GetString("areyousureyouwanttomovetheevent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 郵便番号.
        /// </summary>
        internal static string Assign {
            get {
                return ResourceManager.GetString("Assign", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to に割り当てられている.
        /// </summary>
        internal static string AssignedTo {
            get {
                return ResourceManager.GetString("AssignedTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to に割り当てる.
        /// </summary>
        internal static string AssignTo {
            get {
                return ResourceManager.GetString("AssignTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 少なくともいずれかを選択します。詳細.
        /// </summary>
        internal static string Atleastselectonedetail {
            get {
                return ResourceManager.GetString("Atleastselectonedetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 8月.
        /// </summary>
        internal static string August {
            get {
                return ResourceManager.GetString("August", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 署名権者.
        /// </summary>
        internal static string AuthorizedSignatory {
            get {
                return ResourceManager.GetString("AuthorizedSignatory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 自動割り当て.
        /// </summary>
        internal static string autoallocate {
            get {
                return ResourceManager.GetString("autoallocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 自動割り当て可.
        /// </summary>
        internal static string AutoAllocationAllowed {
            get {
                return ResourceManager.GetString("AutoAllocationAllowed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 平均解決時間.
        /// </summary>
        internal static string AverageResolutionTime {
            get {
                return ResourceManager.GetString("AverageResolutionTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 平均解決時間 - ワイズ年.
        /// </summary>
        internal static string averageresolutiontimeyearwise {
            get {
                return ResourceManager.GetString("averageresolutiontimeyearwise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 平均応答時間.
        /// </summary>
        internal static string averageresponsetime {
            get {
                return ResourceManager.GetString("averageresponsetime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 平均レスポンスタイムイヤーワイズ.
        /// </summary>
        internal static string averageresponsetimeyearwise {
            get {
                return ResourceManager.GetString("averageresponsetimeyearwise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 平均時間.
        /// </summary>
        internal static string AverageTime {
            get {
                return ResourceManager.GetString("AverageTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 平均解決時間.
        /// </summary>
        internal static string AvgResolutionTime {
            get {
                return ResourceManager.GetString("AvgResolutionTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 銀行名.
        /// </summary>
        internal static string BankName {
            get {
                return ResourceManager.GetString("BankName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 棒グラフ.
        /// </summary>
        internal static string BarChart {
            get {
                return ResourceManager.GetString("BarChart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ブランチ.
        /// </summary>
        internal static string Branch {
            get {
                return ResourceManager.GetString("Branch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ブランチalrearyは、選択した.
        /// </summary>
        internal static string BranchAlrearySelected {
            get {
                return ResourceManager.GetString("BranchAlrearySelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 支部協会.
        /// </summary>
        internal static string BranchAssociation {
            get {
                return ResourceManager.GetString("BranchAssociation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 枝の詳細.
        /// </summary>
        internal static string branchdetail {
            get {
                return ResourceManager.GetString("branchdetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 支店名.
        /// </summary>
        internal static string BranchName {
            get {
                return ResourceManager.GetString("BranchName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 支店名は既に存在している.
        /// </summary>
        internal static string BranchNameisalreadypresent {
            get {
                return ResourceManager.GetString("BranchNameisalreadypresent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 支店税コード.
        /// </summary>
        internal static string BranchTaxCode {
            get {
                return ResourceManager.GetString("BranchTaxCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 支店税の詳細.
        /// </summary>
        internal static string BranchTaxDetails {
            get {
                return ResourceManager.GetString("BranchTaxDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ブランド.
        /// </summary>
        internal static string Brand {
            get {
                return ResourceManager.GetString("Brand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ブランド名.
        /// </summary>
        internal static string BrandName {
            get {
                return ResourceManager.GetString("BrandName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ブランド協会.
        /// </summary>
        internal static string BrandsAssociation {
            get {
                return ResourceManager.GetString("BrandsAssociation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ブラウズ.
        /// </summary>
        internal static string Browse {
            get {
                return ResourceManager.GetString("Browse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 数式を計算.
        /// </summary>
        internal static string calculateformula {
            get {
                return ResourceManager.GetString("calculateformula", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to カレンダー.
        /// </summary>
        internal static string Calendar {
            get {
                return ResourceManager.GetString("Calendar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 戻る日を呼び出す.
        /// </summary>
        internal static string CallBackDate {
            get {
                return ResourceManager.GetString("CallBackDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 時間をコールバック.
        /// </summary>
        internal static string CallBackTime {
            get {
                return ResourceManager.GetString("CallBackTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 閉鎖日を呼び出す.
        /// </summary>
        internal static string CallClosureDate {
            get {
                return ResourceManager.GetString("CallClosureDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 閉鎖時間を呼び出す.
        /// </summary>
        internal static string CallClosureTime {
            get {
                return ResourceManager.GetString("CallClosureTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 日付を呼び出す.
        /// </summary>
        internal static string CallDate {
            get {
                return ResourceManager.GetString("CallDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CallDateは、現在の日付より大きくすることはできません.
        /// </summary>
        internal static string CallDateCanNotBeGreaterThanCurrentDate {
            get {
                return ResourceManager.GetString("CallDateCanNotBeGreaterThanCurrentDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 説明を呼び出&amp;#8203;&amp;#8203;す.
        /// </summary>
        internal static string CallDescription {
            get {
                return ResourceManager.GetString("CallDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to コー&amp;#8203;&amp;#8203;ル詳細.
        /// </summary>
        internal static string CallDetails {
            get {
                return ResourceManager.GetString("CallDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to モードを呼び出す.
        /// </summary>
        internal static string CallMode {
            get {
                return ResourceManager.GetString("CallMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 自然を呼び出す.
        /// </summary>
        internal static string CallNature {
            get {
                return ResourceManager.GetString("CallNature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ステータスを呼び出す.
        /// </summary>
        internal static string CallStatus {
            get {
                return ResourceManager.GetString("CallStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 時間を呼び出す.
        /// </summary>
        internal static string CallTime {
            get {
                return ResourceManager.GetString("CallTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to コー&amp;#8203;&amp;#8203;ルタイプ.
        /// </summary>
        internal static string CallType {
            get {
                return ResourceManager.GetString("CallType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to キャンセル.
        /// </summary>
        internal static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Cannot close the job without product details.
        /// </summary>
        internal static string cannotclosethejobwithoutproductdetails {
            get {
                return ResourceManager.GetString("cannotclosethejobwithoutproductdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot delete as Tax Type is referenced.
        /// </summary>
        internal static string CannotdeleteasTaxTypeisreferenced {
            get {
                return ResourceManager.GetString("CannotdeleteasTaxTypeisreferenced", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 編集モードで削除することはできません.
        /// </summary>
        internal static string CannotDeleteinEditMode {
            get {
                return ResourceManager.GetString("CannotDeleteinEditMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja caps lock is on.
        /// </summary>
        internal static string capslockison {
            get {
                return ResourceManager.GetString("capslockison", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ケースの進捗状況.
        /// </summary>
        internal static string CaseProgress {
            get {
                return ResourceManager.GetString("CaseProgress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ケースの進捗履歴.
        /// </summary>
        internal static string CaseProgressHistory {
            get {
                return ResourceManager.GetString("CaseProgressHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 失敗の原因.
        /// </summary>
        internal static string CauseofFailure {
            get {
                return ResourceManager.GetString("CauseofFailure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 作業フロ便番号.
        /// </summary>
        internal static string CCToAssignee {
            get {
                return ResourceManager.GetString("CCToAssignee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Change Password.
        /// </summary>
        internal static string ChangePassword {
            get {
                return ResourceManager.GetString("ChangePassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 変更内容は失われます続行しますか.
        /// </summary>
        internal static string Changeswillbelostdoyouwanttoproceed {
            get {
                return ResourceManager.GetString("Changeswillbelostdoyouwanttoproceed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 列名を選択してください.
        /// </summary>
        internal static string ChooseColumnNames {
            get {
                return ResourceManager.GetString("ChooseColumnNames", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to クリアフォーミュラ.
        /// </summary>
        internal static string clearformula {
            get {
                return ResourceManager.GetString("clearformula", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 閉じる.
        /// </summary>
        internal static string close {
            get {
                return ResourceManager.GetString("close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 閉店.
        /// </summary>
        internal static string Closed {
            get {
                return ResourceManager.GetString("Closed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to クローズドカウント.
        /// </summary>
        internal static string ClosedCount {
            get {
                return ResourceManager.GetString("ClosedCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 閉鎖の詳細.
        /// </summary>
        internal static string ClosureDetails {
            get {
                return ResourceManager.GetString("ClosureDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 閉鎖の理由.
        /// </summary>
        internal static string closurereason {
            get {
                return ResourceManager.GetString("closurereason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 閉鎖型.
        /// </summary>
        internal static string ClosureType {
            get {
                return ResourceManager.GetString("ClosureType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to コード.
        /// </summary>
        internal static string code {
            get {
                return ResourceManager.GetString("code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 列名.
        /// </summary>
        internal static string ColumnNames {
            get {
                return ResourceManager.GetString("ColumnNames", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 日付のコミッショニング.
        /// </summary>
        internal static string commissioningdate {
            get {
                return ResourceManager.GetString("commissioningdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 会社.
        /// </summary>
        internal static string Company {
            get {
                return ResourceManager.GetString("Company", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 企業ブランド.
        /// </summary>
        internal static string CompanyBrands {
            get {
                return ResourceManager.GetString("CompanyBrands", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Company Calender.
        /// </summary>
        internal static string CompanyCalender {
            get {
                return ResourceManager.GetString("CompanyCalender", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Company Financial Year.
        /// </summary>
        internal static string CompanyFinancialYear {
            get {
                return ResourceManager.GetString("CompanyFinancialYear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ヘッダ.
        /// </summary>
        internal static string CompanyHeader {
            get {
                return ResourceManager.GetString("CompanyHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 会社マスター.
        /// </summary>
        internal static string CompanyMaster {
            get {
                return ResourceManager.GetString("CompanyMaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 会社名.
        /// </summary>
        internal static string CompanyName {
            get {
                return ResourceManager.GetString("CompanyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 会社名は既に存在している.
        /// </summary>
        internal static string CompanyNameisalreadypresent {
            get {
                return ResourceManager.GetString("CompanyNameisalreadypresent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 会社 - 会社関係.
        /// </summary>
        internal static string CompanyRelation {
            get {
                return ResourceManager.GetString("CompanyRelation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 会社との関係.
        /// </summary>
        internal static string CompanyRelationships {
            get {
                return ResourceManager.GetString("CompanyRelationships", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 会社ヘッダが完全に成功を保存し、少なくとも準の1つのブランドをしてください.
        /// </summary>
        internal static string CompanySavedPleaseAssociateatleastoneBrand {
            get {
                return ResourceManager.GetString("CompanySavedPleaseAssociateatleastoneBrand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 会社税コード.
        /// </summary>
        internal static string CompanyTaxCode {
            get {
                return ResourceManager.GetString("CompanyTaxCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 法人税の詳細.
        /// </summary>
        internal static string CompanyTaxDetails {
            get {
                return ResourceManager.GetString("CompanyTaxDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 会社の規約.
        /// </summary>
        internal static string CompanyTerms {
            get {
                return ResourceManager.GetString("CompanyTerms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 会社のテーマ.
        /// </summary>
        internal static string CompanyTheme {
            get {
                return ResourceManager.GetString("CompanyTheme", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 会社の種類.
        /// </summary>
        internal static string CompanyType {
            get {
                return ResourceManager.GetString("CompanyType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 完成した.
        /// </summary>
        internal static string Completed {
            get {
                return ResourceManager.GetString("Completed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 完成したカウント.
        /// </summary>
        internal static string CompletedCount {
            get {
                return ResourceManager.GetString("CompletedCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 詳細を入力し完了.
        /// </summary>
        internal static string CompleteEnteringDetail {
            get {
                return ResourceManager.GetString("CompleteEnteringDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 部品の詳細を入力し完了.
        /// </summary>
        internal static string CompleteEnteringDetailParts {
            get {
                return ResourceManager.GetString("CompleteEnteringDetailParts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービスの詳細を入力し完了.
        /// </summary>
        internal static string CompleteEnteringDetailService {
            get {
                return ResourceManager.GetString("CompleteEnteringDetailService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 諸口詳細を入力完了.
        /// </summary>
        internal static string CompleteEnteringDetailSundry {
            get {
                return ResourceManager.GetString("CompleteEnteringDetailSundry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to コンポーネントの詳細.
        /// </summary>
        internal static string componentdetails {
            get {
                return ResourceManager.GetString("componentdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Confirm Password.
        /// </summary>
        internal static string ConfirmPassword {
            get {
                return ResourceManager.GetString("ConfirmPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to コンタクトパーソン.
        /// </summary>
        internal static string ContactPerson {
            get {
                return ResourceManager.GetString("ContactPerson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to コンタクトパーソン.
        /// </summary>
        internal static string ContactPersons {
            get {
                return ResourceManager.GetString("ContactPersons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to コピー元の役割.
        /// </summary>
        internal static string CopyRoleFrom {
            get {
                return ResourceManager.GetString("CopyRoleFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 是正処置.
        /// </summary>
        internal static string CorrectiveAction {
            get {
                return ResourceManager.GetString("CorrectiveAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to カウント.
        /// </summary>
        internal static string Count {
            get {
                return ResourceManager.GetString("Count", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to カントリー.
        /// </summary>
        internal static string Country {
            get {
                return ResourceManager.GetString("Country", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 作成.
        /// </summary>
        internal static string Created {
            get {
                return ResourceManager.GetString("Created", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to JobCardを作成.
        /// </summary>
        internal static string CreateJobCard {
            get {
                return ResourceManager.GetString("CreateJobCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 新規作成.
        /// </summary>
        internal static string CreateNew {
            get {
                return ResourceManager.GetString("CreateNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 見積書を作成します。.
        /// </summary>
        internal static string CreateQuotation {
            get {
                return ResourceManager.GetString("CreateQuotation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービスリクエストを作成する.
        /// </summary>
        internal static string CreateServiceRequest {
            get {
                return ResourceManager.GetString("CreateServiceRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Crictical.
        /// </summary>
        internal static string crictical {
            get {
                return ResourceManager.GetString("crictical", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to クリティカル.
        /// </summary>
        internal static string Critical {
            get {
                return ResourceManager.GetString("Critical", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 通貨.
        /// </summary>
        internal static string Currency {
            get {
                return ResourceManager.GetString("Currency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 電流ステップ.
        /// </summary>
        internal static string CurrentStep {
            get {
                return ResourceManager.GetString("CurrentStep", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 顧客.
        /// </summary>
        internal static string Customer {
            get {
                return ResourceManager.GetString("Customer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 顧客の苦情.
        /// </summary>
        internal static string CustomerComplaint {
            get {
                return ResourceManager.GetString("CustomerComplaint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 顧客との接触.
        /// </summary>
        internal static string CustomerContact {
            get {
                return ResourceManager.GetString("CustomerContact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to お客様の連絡先電話番号.
        /// </summary>
        internal static string CustomerContactPhone {
            get {
                return ResourceManager.GetString("CustomerContactPhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to お客様の詳細.
        /// </summary>
        internal static string customerdetails {
            get {
                return ResourceManager.GetString("customerdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 顧客によるサービス.
        /// </summary>
        internal static string customerdueservices {
            get {
                return ResourceManager.GetString("customerdueservices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 顧客がロックされ、続行してもよろしいでしょうか？.
        /// </summary>
        internal static string CustomerisLockedDoyouwanttocontinue {
            get {
                return ResourceManager.GetString("CustomerisLockedDoyouwanttocontinue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 顧客はアクティブではありません.
        /// </summary>
        internal static string Customerisnotactive {
            get {
                return ResourceManager.GetString("Customerisnotactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 顧客の場所.
        /// </summary>
        internal static string CustomerLocation {
            get {
                return ResourceManager.GetString("CustomerLocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 顧客名.
        /// </summary>
        internal static string customername {
            get {
                return ResourceManager.GetString("customername", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 顧客が見つかりません.
        /// </summary>
        internal static string CustomerNotFound {
            get {
                return ResourceManager.GetString("CustomerNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 顧客の見積.
        /// </summary>
        internal static string CustomerQuotation {
            get {
                return ResourceManager.GetString("CustomerQuotation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Customer Quotation Archived.
        /// </summary>
        internal static string CustomerQuotationArchived {
            get {
                return ResourceManager.GetString("CustomerQuotationArchived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 引用日.
        /// </summary>
        internal static string CustomerQuotationDate {
            get {
                return ResourceManager.GetString("CustomerQuotationDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 引用数.
        /// </summary>
        internal static string CustomerQuotationNumber {
            get {
                return ResourceManager.GetString("CustomerQuotationNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 顧客見積概要.
        /// </summary>
        internal static string CustomerQuotationSummary {
            get {
                return ResourceManager.GetString("CustomerQuotationSummary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to お客様からの評価.
        /// </summary>
        internal static string CustomerRating {
            get {
                return ResourceManager.GetString("CustomerRating", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 顧客の検索.
        /// </summary>
        internal static string customersearch {
            get {
                return ResourceManager.GetString("customersearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to カスタマーサービス履歴.
        /// </summary>
        internal static string CustomerServiceHistory {
            get {
                return ResourceManager.GetString("CustomerServiceHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to データが正常に保存されました.
        /// </summary>
        internal static string DataSavedSuccessfully {
            get {
                return ResourceManager.GetString("DataSavedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 日付.
        /// </summary>
        internal static string Date {
            get {
                return ResourceManager.GetString("Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 日から有効では少ないし、現在の日付にすることはできません.
        /// </summary>
        internal static string DateCannotbelessthenCurrentDate {
            get {
                return ResourceManager.GetString("DateCannotbelessthenCurrentDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 日付は少ないし、前のレコードの日付にすることはできません.
        /// </summary>
        internal static string Datecannotbelessthenpreviousdate {
            get {
                return ResourceManager.GetString("Datecannotbelessthenpreviousdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 選択した日付が前のカスタマーその後大きくなければなりません。.
        /// </summary>
        internal static string dateselectedmustbegreaterthenpreviouscustomer {
            get {
                return ResourceManager.GetString("dateselectedmustbegreaterthenpreviouscustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 余日.
        /// </summary>
        internal static string DaysLeft {
            get {
                return ResourceManager.GetString("DaysLeft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ディーラー.
        /// </summary>
        internal static string Dealer {
            get {
                return ResourceManager.GetString("Dealer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ディーラー/会社.
        /// </summary>
        internal static string DealersorCompany {
            get {
                return ResourceManager.GetString("DealersorCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 12月.
        /// </summary>
        internal static string December {
            get {
                return ResourceManager.GetString("December", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to デフォルトのグリッドサイズ.
        /// </summary>
        internal static string DefaultGridSize {
            get {
                return ResourceManager.GetString("DefaultGridSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Default grid size already available.
        /// </summary>
        internal static string Defaultgridsizealreadyavailable {
            get {
                return ResourceManager.GetString("Defaultgridsizealreadyavailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to デフォルトのグリッドサイズは、0&amp;#12316;255の間&amp;#8203;&amp;#8203;でなければなりません.
        /// </summary>
        internal static string DefaultgridSizeshouldbebetweenzeroandtwofiftyfive {
            get {
                return ResourceManager.GetString("DefaultgridSizeshouldbebetweenzeroandtwofiftyfive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 削除.
        /// </summary>
        internal static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アクションを削除する.
        /// </summary>
        internal static string DeleteAction {
            get {
                return ResourceManager.GetString("DeleteAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ブランチを削除.
        /// </summary>
        internal static string DeleteBranch {
            get {
                return ResourceManager.GetString("DeleteBranch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 支店税の詳細を削除.
        /// </summary>
        internal static string DeleteBranchTaxDetails {
            get {
                return ResourceManager.GetString("DeleteBranchTaxDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ブランドを削除.
        /// </summary>
        internal static string DeleteBrands {
            get {
                return ResourceManager.GetString("DeleteBrands", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 会社を削除.
        /// </summary>
        internal static string DeleteCompany {
            get {
                return ResourceManager.GetString("DeleteCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 会社との関係を削除.
        /// </summary>
        internal static string DeleteCompanyRelation {
            get {
                return ResourceManager.GetString("DeleteCompanyRelation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 会社の規約を削除.
        /// </summary>
        internal static string DeleteCompanyTerms {
            get {
                return ResourceManager.GetString("DeleteCompanyTerms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 正常に削除さ.
        /// </summary>
        internal static string deletedsuccessfully {
            get {
                return ResourceManager.GetString("deletedsuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 従業員を削除.
        /// </summary>
        internal static string DeleteEmployee {
            get {
                return ResourceManager.GetString("DeleteEmployee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パートフリーを削除.
        /// </summary>
        internal static string deletefreestock {
            get {
                return ResourceManager.GetString("deletefreestock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ファンクショングループを削除する.
        /// </summary>
        internal static string DeleteFunctionGroup {
            get {
                return ResourceManager.GetString("DeleteFunctionGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ジョブ&amp;#183;カードを削除.
        /// </summary>
        internal static string DeleteJobCard {
            get {
                return ResourceManager.GetString("DeleteJobCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to マスターを削除.
        /// </summary>
        internal static string DeleteMaster {
            get {
                return ResourceManager.GetString("DeleteMaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to モデルを削除.
        /// </summary>
        internal static string deletemodel {
            get {
                return ResourceManager.GetString("deletemodel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 削除操作.
        /// </summary>
        internal static string deleteoperation {
            get {
                return ResourceManager.GetString("deleteoperation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作従業員の詳細を削除.
        /// </summary>
        internal static string DeleteOperationEmployeeDetails {
            get {
                return ResourceManager.GetString("DeleteOperationEmployeeDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツを削除.
        /// </summary>
        internal static string deletepart {
            get {
                return ResourceManager.GetString("deletepart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツの価格を削除.
        /// </summary>
        internal static string deletepartprice {
            get {
                return ResourceManager.GetString("deletepartprice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 部品、製品の種類を削除.
        /// </summary>
        internal static string deletepartproducttype {
            get {
                return ResourceManager.GetString("deletepartproducttype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツを削除する.
        /// </summary>
        internal static string DeleteParts {
            get {
                return ResourceManager.GetString("DeleteParts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーティーを削除.
        /// </summary>
        internal static string DeleteParty {
            get {
                return ResourceManager.GetString("DeleteParty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to プリフィックスサフィックスを削除.
        /// </summary>
        internal static string DeletePrefixSuffix {
            get {
                return ResourceManager.GetString("DeletePrefixSuffix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 製品を削除.
        /// </summary>
        internal static string deleteproduct {
            get {
                return ResourceManager.GetString("deleteproduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 製品の詳細を削除.
        /// </summary>
        internal static string deleteproductdetail {
            get {
                return ResourceManager.GetString("deleteproductdetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 製品タイプを削除.
        /// </summary>
        internal static string deleteproducttype {
            get {
                return ResourceManager.GetString("deleteproducttype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 見積を削除.
        /// </summary>
        internal static string DeleteQuotation {
            get {
                return ResourceManager.GetString("DeleteQuotation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to リクエストを削除.
        /// </summary>
        internal static string DeleteRequest {
            get {
                return ResourceManager.GetString("DeleteRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 役割の削除.
        /// </summary>
        internal static string DeleteRole {
            get {
                return ResourceManager.GetString("DeleteRole", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービスチャージを削除.
        /// </summary>
        internal static string deleteServiceCharge {
            get {
                return ResourceManager.GetString("deleteServiceCharge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービス料の詳細を削除.
        /// </summary>
        internal static string deleteServiceChargeDetails {
            get {
                return ResourceManager.GetString("deleteServiceChargeDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービスの種類を削除.
        /// </summary>
        internal static string DeleteServiceType {
            get {
                return ResourceManager.GetString("DeleteServiceType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to スキルを削除.
        /// </summary>
        internal static string DeleteSkills {
            get {
                return ResourceManager.GetString("DeleteSkills", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to スペシャライゼーションを削除.
        /// </summary>
        internal static string DeleteSpecialization {
            get {
                return ResourceManager.GetString("DeleteSpecialization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ステップを削除.
        /// </summary>
        internal static string DeleteStep {
            get {
                return ResourceManager.GetString("DeleteStep", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ステップのリンクの削除.
        /// </summary>
        internal static string DeleteStepLink {
            get {
                return ResourceManager.GetString("DeleteStepLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 雑貨削除.
        /// </summary>
        internal static string DeleteSundry {
            get {
                return ResourceManager.GetString("DeleteSundry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 税コードを削除.
        /// </summary>
        internal static string DeleteTaxCode {
            get {
                return ResourceManager.GetString("DeleteTaxCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 税の詳細を削除.
        /// </summary>
        internal static string DeleteTaxDetails {
            get {
                return ResourceManager.GetString("DeleteTaxDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 租税構造を削除.
        /// </summary>
        internal static string deletetaxstructure {
            get {
                return ResourceManager.GetString("deletetaxstructure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 租税構造の詳細を削除.
        /// </summary>
        internal static string deletetaxtstructuredetails {
            get {
                return ResourceManager.GetString("deletetaxtstructuredetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ユーザーの削除.
        /// </summary>
        internal static string DeleteUser {
            get {
                return ResourceManager.GetString("DeleteUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 保証内容を削除.
        /// </summary>
        internal static string deletewarrantydetails {
            get {
                return ResourceManager.GetString("deletewarrantydetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 作業内容を削除.
        /// </summary>
        internal static string DeleteWorkDetails {
            get {
                return ResourceManager.GetString("DeleteWorkDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 納期.
        /// </summary>
        internal static string DeliveryDate {
            get {
                return ResourceManager.GetString("DeliveryDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 部門.
        /// </summary>
        internal static string Department {
            get {
                return ResourceManager.GetString("Department", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 依存関係は、レコードを削除することはできませんが見つかりました.
        /// </summary>
        internal static string Dependencyfoundcannotdeletetherecords {
            get {
                return ResourceManager.GetString("Dependencyfoundcannotdeletetherecords", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 説明.
        /// </summary>
        internal static string description {
            get {
                return ResourceManager.GetString("description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 指定.
        /// </summary>
        internal static string Designation {
            get {
                return ResourceManager.GetString("Designation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 変換先列.
        /// </summary>
        internal static string DestinationColumns {
            get {
                return ResourceManager.GetString("DestinationColumns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 詳細.
        /// </summary>
        internal static string Detail {
            get {
                return ResourceManager.GetString("Detail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 偏差時間.
        /// </summary>
        internal static string DeviationHours {
            get {
                return ResourceManager.GetString("DeviationHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 偏差％.
        /// </summary>
        internal static string DeviationPercentage {
            get {
                return ResourceManager.GetString("DeviationPercentage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 割引.
        /// </summary>
        internal static string Discount {
            get {
                return ResourceManager.GetString("Discount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 割引額.
        /// </summary>
        internal static string Discountamount {
            get {
                return ResourceManager.GetString("Discountamount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 割引額.
        /// </summary>
        internal static string DiscountedAmount {
            get {
                return ResourceManager.GetString("DiscountedAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 割引率.
        /// </summary>
        internal static string DiscountPercentage {
            get {
                return ResourceManager.GetString("DiscountPercentage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 割引は未満でなければなりません.
        /// </summary>
        internal static string DiscountShouldbeLessThan {
            get {
                return ResourceManager.GetString("DiscountShouldbeLessThan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アプリケーションエラーが発生しました.
        /// </summary>
        internal static string DivideByZeroException {
            get {
                return ResourceManager.GetString("DivideByZeroException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 郵便番号.
        /// </summary>
        internal static string DocumentExport {
            get {
                return ResourceManager.GetString("DocumentExport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to スペースを入力しないでください.
        /// </summary>
        internal static string DonotEnterSpace {
            get {
                return ResourceManager.GetString("DonotEnterSpace", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to このシリアル番号を追加しますか.
        /// </summary>
        internal static string DoyouwanttoaddthisSerialNumber {
            get {
                return ResourceManager.GetString("DoyouwanttoaddthisSerialNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 関連付けを変更したいですか？.
        /// </summary>
        internal static string Doyouwanttochangetheassociation {
            get {
                return ResourceManager.GetString("Doyouwanttochangetheassociation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 新しいバージョンを作成したいですか？.
        /// </summary>
        internal static string Doyouwanttocreatenewversion {
            get {
                return ResourceManager.GetString("Doyouwanttocreatenewversion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 期日.
        /// </summary>
        internal static string DueDate {
            get {
                return ResourceManager.GetString("DueDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 期日は空白にすることはできません.
        /// </summary>
        internal static string DueDatecannotbeBlank {
            get {
                return ResourceManager.GetString("DueDatecannotbeBlank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to による範囲.
        /// </summary>
        internal static string DueRange {
            get {
                return ResourceManager.GetString("DueRange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Duplicate.
        /// </summary>
        internal static string Duplicate {
            get {
                return ResourceManager.GetString("Duplicate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 支店を複製.
        /// </summary>
        internal static string duplicatebranch {
            get {
                return ResourceManager.GetString("duplicatebranch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Duplicate Branch Name.
        /// </summary>
        internal static string DuplicateBranchName {
            get {
                return ResourceManager.GetString("DuplicateBranchName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Duplicate Brand.
        /// </summary>
        internal static string DuplicateBrand {
            get {
                return ResourceManager.GetString("DuplicateBrand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Code.
        /// </summary>
        internal static string DuplicateCode {
            get {
                return ResourceManager.GetString("DuplicateCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Description.
        /// </summary>
        internal static string DuplicateCode1 {
            get {
                return ResourceManager.GetString("DuplicateCode1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Code and Description.
        /// </summary>
        internal static string DuplicateCodeandDescription {
            get {
                return ResourceManager.GetString("DuplicateCodeandDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Duplicate Company Name.
        /// </summary>
        internal static string DuplicateCompanyName {
            get {
                return ResourceManager.GetString("DuplicateCompanyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Duplicate date.
        /// </summary>
        internal static string duplicatedate {
            get {
                return ResourceManager.GetString("duplicatedate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 説明を複製.
        /// </summary>
        internal static string DuplicateDescription {
            get {
                return ResourceManager.GetString("DuplicateDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 見つかった電子メールを複製.
        /// </summary>
        internal static string DuplicateEmailsFound {
            get {
                return ResourceManager.GetString("DuplicateEmailsFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Employee.
        /// </summary>
        internal static string DuplicateEmployee {
            get {
                return ResourceManager.GetString("DuplicateEmployee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to エントリが重複している.
        /// </summary>
        internal static string Duplicateentries {
            get {
                return ResourceManager.GetString("Duplicateentries", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フローステップを働かせる.
        /// </summary>
        internal static string Duplicateentriesof {
            get {
                return ResourceManager.GetString("Duplicateentriesof", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Duplicate Function Group.
        /// </summary>
        internal static string DuplicateFunctionGroup {
            get {
                return ResourceManager.GetString("DuplicateFunctionGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Duplicate Login Id.
        /// </summary>
        internal static string DuplicateLoginId {
            get {
                return ResourceManager.GetString("DuplicateLoginId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Duplicate Master.
        /// </summary>
        internal static string DuplicateMaster {
            get {
                return ResourceManager.GetString("DuplicateMaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to モデルを複製.
        /// </summary>
        internal static string duplicatemodel {
            get {
                return ResourceManager.GetString("duplicatemodel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Duplicate Module.
        /// </summary>
        internal static string DuplicateModule {
            get {
                return ResourceManager.GetString("DuplicateModule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ja Duplicate Operation Code.
        /// </summary>
        internal static string DuplicateOperationCode {
            get {
                return ResourceManager.GetString("DuplicateOperationCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Duplicate part number.
        /// </summary>
        internal static string Duplicatepartnumber {
            get {
                return ResourceManager.GetString("Duplicatepartnumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 見つかった電話番号を重複.
        /// </summary>
        internal static string DuplicatePhoneNumbersFound {
            get {
                return ResourceManager.GetString("DuplicatePhoneNumbersFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 製品タイプを複製.
        /// </summary>
        internal static string duplicateproducttype {
            get {
                return ResourceManager.GetString("duplicateproducttype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Duplicate Role.
        /// </summary>
        internal static string DuplicateRole {
            get {
                return ResourceManager.GetString("DuplicateRole", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to セカンダリセグメントを複製.
        /// </summary>
        internal static string duplicatesecondarysegment {
            get {
                return ResourceManager.GetString("duplicatesecondarysegment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Duplicate service code.
        /// </summary>
        internal static string Duplicateservicecode {
            get {
                return ResourceManager.GetString("Duplicateservicecode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービスの日付を複製.
        /// </summary>
        internal static string duplicateservicedate {
            get {
                return ResourceManager.GetString("duplicateservicedate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービスタイプを複製.
        /// </summary>
        internal static string duplicateservicetype {
            get {
                return ResourceManager.GetString("duplicateservicetype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 重複したサービスタイプは許可されていません.
        /// </summary>
        internal static string DuplicateServiceTypeisnotallowed {
            get {
                return ResourceManager.GetString("DuplicateServiceTypeisnotallowed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja duplicate state.
        /// </summary>
        internal static string duplicatestate {
            get {
                return ResourceManager.GetString("duplicatestate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 状態名が重複して.
        /// </summary>
        internal static string duplicatestatename {
            get {
                return ResourceManager.GetString("duplicatestatename", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 諸口記述を複製することはできません.
        /// </summary>
        internal static string DuplicateSundryDescriptionisnotAllowed {
            get {
                return ResourceManager.GetString("DuplicateSundryDescriptionisnotAllowed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Duplicate Tax name.
        /// </summary>
        internal static string DuplicateTaxname {
            get {
                return ResourceManager.GetString("DuplicateTaxname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Duplicate Tax Type.
        /// </summary>
        internal static string DuplicateTaxType {
            get {
                return ResourceManager.GetString("DuplicateTaxType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Duplicate Unique identifier.
        /// </summary>
        internal static string DuplicateUniqueidentifier {
            get {
                return ResourceManager.GetString("DuplicateUniqueidentifier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 編集.
        /// </summary>
        internal static string edit {
            get {
                return ResourceManager.GetString("edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アクションの編集.
        /// </summary>
        internal static string EditAction {
            get {
                return ResourceManager.GetString("EditAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 当社の編集.
        /// </summary>
        internal static string EditCompany {
            get {
                return ResourceManager.GetString("EditCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to コンポーネントの詳細を編集.
        /// </summary>
        internal static string editcomponentdetails {
            get {
                return ResourceManager.GetString("editcomponentdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to イベントを編集.
        /// </summary>
        internal static string EditEvents {
            get {
                return ResourceManager.GetString("EditEvents", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ジョブ&amp;#183;カードを編集.
        /// </summary>
        internal static string EditJobCard {
            get {
                return ResourceManager.GetString("EditJobCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to モデルを編集.
        /// </summary>
        internal static string editmodel {
            get {
                return ResourceManager.GetString("editmodel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作を編集.
        /// </summary>
        internal static string editoperation {
            get {
                return ResourceManager.GetString("editoperation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Operation Details.
        /// </summary>
        internal static string EditOperationDetails {
            get {
                return ResourceManager.GetString("EditOperationDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to エディットパーツマスター.
        /// </summary>
        internal static string EditPartsMaster {
            get {
                return ResourceManager.GetString("EditPartsMaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 商品を編集.
        /// </summary>
        internal static string editproduct {
            get {
                return ResourceManager.GetString("editproduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 製品タイプを編集.
        /// </summary>
        internal static string editproducttype {
            get {
                return ResourceManager.GetString("editproducttype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to セカンダリセグメントを編集.
        /// </summary>
        internal static string editsecondarysegment {
            get {
                return ResourceManager.GetString("editsecondarysegment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービス料を編集.
        /// </summary>
        internal static string editsevicecharges {
            get {
                return ResourceManager.GetString("editsevicecharges", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サイトのアドレスを編集する.
        /// </summary>
        internal static string editsiteaddress {
            get {
                return ResourceManager.GetString("editsiteaddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 編集状態.
        /// </summary>
        internal static string editstate {
            get {
                return ResourceManager.GetString("editstate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 租税構造を編集.
        /// </summary>
        internal static string edittaxstructure {
            get {
                return ResourceManager.GetString("edittaxstructure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 保証Deatilsを編集.
        /// </summary>
        internal static string editwarrantydeatils {
            get {
                return ResourceManager.GetString("editwarrantydeatils", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to より効果的な.
        /// </summary>
        internal static string effectivefrom {
            get {
                return ResourceManager.GetString("effectivefrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &amp;lt;8時間.
        /// </summary>
        internal static string EightHour {
            get {
                return ResourceManager.GetString("EightHour", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 8&amp;#12316;16時間.
        /// </summary>
        internal static string EightToSixteenHours {
            get {
                return ResourceManager.GetString("EightToSixteenHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to メール.
        /// </summary>
        internal static string Email {
            get {
                return ResourceManager.GetString("Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Addresseへのメール.
        /// </summary>
        internal static string EmailToAddresse {
            get {
                return ResourceManager.GetString("EmailToAddresse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 顧客へのメール.
        /// </summary>
        internal static string EmailToCustomer {
            get {
                return ResourceManager.GetString("EmailToCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 従業員.
        /// </summary>
        internal static string Employee {
            get {
                return ResourceManager.GetString("Employee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 従業員 - ブランチ.
        /// </summary>
        internal static string EmployeeBranch {
            get {
                return ResourceManager.GetString("EmployeeBranch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 従業員の詳細.
        /// </summary>
        internal static string EmployeeDetails {
            get {
                return ResourceManager.GetString("EmployeeDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 従業員コード.
        /// </summary>
        internal static string EmployeeID {
            get {
                return ResourceManager.GetString("EmployeeID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 従業員コードはすでに使用されています.
        /// </summary>
        internal static string EmployeeIDisalreadyused {
            get {
                return ResourceManager.GetString("EmployeeIDisalreadyused", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 従業員が、既に枝に関連付けられている.
        /// </summary>
        internal static string EmployeeisalreadyassociatedwiththeBranch {
            get {
                return ResourceManager.GetString("EmployeeisalreadyassociatedwiththeBranch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 従業員はすでに分業に関連付けられている.
        /// </summary>
        internal static string EmployeeisalreadyassociatedwiththeSpecialization {
            get {
                return ResourceManager.GetString("EmployeeisalreadyassociatedwiththeSpecialization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 従業員.
        /// </summary>
        internal static string EmployeeName {
            get {
                return ResourceManager.GetString("EmployeeName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 従業員が見つからない.
        /// </summary>
        internal static string EmployeeNotFound {
            get {
                return ResourceManager.GetString("EmployeeNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to スキル.
        /// </summary>
        internal static string EmployeeSkills {
            get {
                return ResourceManager.GetString("EmployeeSkills", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 分業.
        /// </summary>
        internal static string EmployeeSpecialization {
            get {
                return ResourceManager.GetString("EmployeeSpecialization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 終了日は開始日より小さくすることはできません.
        /// </summary>
        internal static string EndDatecannotbelessthanStartDate {
            get {
                return ResourceManager.GetString("EndDatecannotbelessthanStartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 英語.
        /// </summary>
        internal static string English {
            get {
                return ResourceManager.GetString("English", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 問い合わせ日.
        /// </summary>
        internal static string EnquiryDate {
            get {
                return ResourceManager.GetString("EnquiryDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to お問い合わせ番号.
        /// </summary>
        internal static string EnquiryNumber {
            get {
                return ResourceManager.GetString("EnquiryNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to コー&amp;#8203;&amp;#8203;ドを入力してください.
        /// </summary>
        internal static string EnterCode {
            get {
                return ResourceManager.GetString("EnterCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 説明を入力します.
        /// </summary>
        internal static string EnterDescription {
            get {
                return ResourceManager.GetString("EnterDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 入力した番号のdoesnot顧客や見込み客に属し.
        /// </summary>
        internal static string EnteredNumberdoesnotbelongstocustomerorprospect {
            get {
                return ResourceManager.GetString("EnteredNumberdoesnotbelongstocustomerorprospect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 日付から入力.
        /// </summary>
        internal static string enterfromdate {
            get {
                return ResourceManager.GetString("enterfromdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to マスター名を入力してください.
        /// </summary>
        internal static string EnterMasterName {
            get {
                return ResourceManager.GetString("EnterMasterName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 非課税その他の費用は、1を入力します.
        /// </summary>
        internal static string enterNonTaxableothercharges1 {
            get {
                return ResourceManager.GetString("enterNonTaxableothercharges1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 非課税その他の費用は、2を入力します.
        /// </summary>
        internal static string enterNonTaxableothercharges2 {
            get {
                return ResourceManager.GetString("enterNonTaxableothercharges2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 課税対象となるその他の費用は、1を入力します.
        /// </summary>
        internal static string enterTaxableothercharges1 {
            get {
                return ResourceManager.GetString("enterTaxableothercharges1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 課税対象となるその他の費用は、2を入力します.
        /// </summary>
        internal static string enterTaxableothercharges2 {
            get {
                return ResourceManager.GetString("enterTaxableothercharges2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja enter year.
        /// </summary>
        internal static string enteryear {
            get {
                return ResourceManager.GetString("enteryear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 入国税の割合.
        /// </summary>
        internal static string EntryTaxPercentage {
            get {
                return ResourceManager.GetString("EntryTaxPercentage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フローステップを働かせる.
        /// </summary>
        internal static string Equal {
            get {
                return ResourceManager.GetString("Equal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to エラー.
        /// </summary>
        internal static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アップロードされた部分でエラーが発生しました。.
        /// </summary>
        internal static string ErrorinUploadedPartsPleaseOpenExcel {
            get {
                return ResourceManager.GetString("ErrorinUploadedPartsPleaseOpenExcel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ロック中にエラーが発生しました.
        /// </summary>
        internal static string ErrorOccuredwhileLocking {
            get {
                return ResourceManager.GetString("ErrorOccuredwhileLocking", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 保存エラー.
        /// </summary>
        internal static string ErrorSaving {
            get {
                return ResourceManager.GetString("ErrorSaving", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to イベント名.
        /// </summary>
        internal static string EventName {
            get {
                return ResourceManager.GetString("EventName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to イベント.
        /// </summary>
        internal static string Events {
            get {
                return ResourceManager.GetString("Events", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to JA Event start date cannot be less than Job card date.
        /// </summary>
        internal static string EventStartDateCannotbeLessthanJobCardDate {
            get {
                return ResourceManager.GetString("EventStartDateCannotbeLessthanJobCardDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Event start time coincides with other events, do you want to continue?.
        /// </summary>
        internal static string Eventstarttimecoincideswithothereventsdoyouwanttocontinue {
            get {
                return ResourceManager.GetString("Eventstarttimecoincideswithothereventsdoyouwanttocontinue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to バージョン.
        /// </summary>
        internal static string Excel {
            get {
                return ResourceManager.GetString("Excel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to エクスポート.
        /// </summary>
        internal static string Export {
            get {
                return ResourceManager.GetString("Export", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EXPORTアクション.
        /// </summary>
        internal static string ExportAction {
            get {
                return ResourceManager.GetString("ExportAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Documentにエクスポート.
        /// </summary>
        internal static string ExporttoDocument {
            get {
                return ResourceManager.GetString("ExporttoDocument", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 新しい連絡先の担当者の保存に失敗しました.
        /// </summary>
        internal static string FailedtosavenewContactPerson {
            get {
                return ResourceManager.GetString("FailedtosavenewContactPerson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 新党の保存に失敗しました.
        /// </summary>
        internal static string FailedtosavenewParty {
            get {
                return ResourceManager.GetString("FailedtosavenewParty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 新しいシリアル番号を保存できませんでした.
        /// </summary>
        internal static string FailedtosavenewSerialNumber {
            get {
                return ResourceManager.GetString("FailedtosavenewSerialNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to シリアルの保存に失敗しました.
        /// </summary>
        internal static string FailedToSaveSerial {
            get {
                return ResourceManager.GetString("FailedToSaveSerial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FAX.
        /// </summary>
        internal static string FAX {
            get {
                return ResourceManager.GetString("FAX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 2月.
        /// </summary>
        internal static string February {
            get {
                return ResourceManager.GetString("February", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 強調表示されたフィールドは必須です.
        /// </summary>
        internal static string Fieldshighlightedaremandatory {
            get {
                return ResourceManager.GetString("Fieldshighlightedaremandatory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to *の欄は必須項目です.
        /// </summary>
        internal static string FieldsmarkedwithStararemandatory {
            get {
                return ResourceManager.GetString("FieldsmarkedwithStararemandatory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フィルタリング.
        /// </summary>
        internal static string Filter {
            get {
                return ResourceManager.GetString("Filter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フローステップを働かせる ?.
        /// </summary>
        internal static string FilterAllQueBranch {
            get {
                return ResourceManager.GetString("FilterAllQueBranch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フィルタ条件.
        /// </summary>
        internal static string FilterCriteria {
            get {
                return ResourceManager.GetString("FilterCriteria", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ユーザー名.
        /// </summary>
        internal static string FinancialYear {
            get {
                return ResourceManager.GetString("FinancialYear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 接尾辞は、すでに選択.
        /// </summary>
        internal static string financialyearalredyselected {
            get {
                return ResourceManager.GetString("financialyearalredyselected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to JA Financial year cannot be greater than next rows financial year.
        /// </summary>
        internal static string FinancialyearcannotbeGreaterthannextrowsfinancialyear {
            get {
                return ResourceManager.GetString("FinancialyearcannotbeGreaterthannextrowsfinancialyear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to JA Financial year cannot be less than previous rows financial year.
        /// </summary>
        internal static string Financialyearcannotbelessthanpreviousrowsfinancialyear {
            get {
                return ResourceManager.GetString("Financialyearcannotbelessthanpreviousrowsfinancialyear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to のために.
        /// </summary>
        internal static string For {
            get {
                return ResourceManager.GetString("For", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パスワードをお忘れですか？.
        /// </summary>
        internal static string forgotpassword {
            get {
                return ResourceManager.GetString("forgotpassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 式.
        /// </summary>
        internal static string formula {
            get {
                return ResourceManager.GetString("formula", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 式の概要.
        /// </summary>
        internal static string formulasummary {
            get {
                return ResourceManager.GetString("formulasummary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 48&amp;#12316;90時間.
        /// </summary>
        internal static string FourtyEightToNintyHours {
            get {
                return ResourceManager.GetString("FourtyEightToNintyHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 自由時間.
        /// </summary>
        internal static string FreeHours {
            get {
                return ResourceManager.GetString("FreeHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フリー.
        /// </summary>
        internal static string freestock {
            get {
                return ResourceManager.GetString("freestock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Friday.
        /// </summary>
        internal static string Friday {
            get {
                return ResourceManager.GetString("Friday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to はい.
        /// </summary>
        internal static string From {
            get {
                return ResourceManager.GetString("From", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 日付から.
        /// </summary>
        internal static string fromdate {
            get {
                return ResourceManager.GetString("fromdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 日付からと日付に空にすることはできません.
        /// </summary>
        internal static string FromDateandTodatecannotbeEmpty {
            get {
                return ResourceManager.GetString("FromDateandTodatecannotbeEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 日付から現在までのより大きくすることはできません.
        /// </summary>
        internal static string FromDatecannotbegreaterthanToDate {
            get {
                return ResourceManager.GetString("FromDatecannotbegreaterthanToDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FROMDATEは、ToDateその後も大きくすることはできません.
        /// </summary>
        internal static string fromdatecannotbegreaterthentodate {
            get {
                return ResourceManager.GetString("fromdatecannotbegreaterthentodate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 日付から今日までよりgreatorすることはできません.
        /// </summary>
        internal static string fromdatecannotbegreatorthantodate {
            get {
                return ResourceManager.GetString("fromdatecannotbegreatorthantodate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to JA From date cannot be less than or equal to previous rows to date.
        /// </summary>
        internal static string Fromdatecannotbelessthanorequaltopreviousrowtodate {
            get {
                return ResourceManager.GetString("Fromdatecannotbelessthanorequaltopreviousrowtodate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 日付から現在までのより小さくすることはできません.
        /// </summary>
        internal static string FromDatecannotbelessthanToDate {
            get {
                return ResourceManager.GetString("FromDatecannotbelessthanToDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 日付から少ないし、現在の日付にすることはできません.
        /// </summary>
        internal static string fromdatecannotbelessthencurrentdate {
            get {
                return ResourceManager.GetString("fromdatecannotbelessthencurrentdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 日付から以上発行日に等しくなければなりません.
        /// </summary>
        internal static string fromdatemustbegreaterthanorequaltoissuedate {
            get {
                return ResourceManager.GetString("fromdatemustbegreaterthanorequaltoissuedate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 日から日まで次に低いでなければなりません.
        /// </summary>
        internal static string fromdatemustbelesserthentodate {
            get {
                return ResourceManager.GetString("fromdatemustbelesserthentodate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ステップから.
        /// </summary>
        internal static string FromStep {
            get {
                return ResourceManager.GetString("FromStep", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja From Time cannot be greater than To Time.
        /// </summary>
        internal static string FromTimecannotbegreaterthanToTime {
            get {
                return ResourceManager.GetString("FromTimecannotbegreaterthanToTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 関数群.
        /// </summary>
        internal static string FunctionGroup {
            get {
                return ResourceManager.GetString("FunctionGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ファンクショングループヘッダー.
        /// </summary>
        internal static string FunctionGroupHeader {
            get {
                return ResourceManager.GetString("FunctionGroupHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 機能グループID.
        /// </summary>
        internal static string FunctionGroupID {
            get {
                return ResourceManager.GetString("FunctionGroupID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 関数グループ名.
        /// </summary>
        internal static string FunctionGroupName {
            get {
                return ResourceManager.GetString("FunctionGroupName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 機能グループのネイティブ.
        /// </summary>
        internal static string FunctionGroupNative {
            get {
                return ResourceManager.GetString("FunctionGroupNative", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 機能グループの操作.
        /// </summary>
        internal static string FunctionGroupOperations {
            get {
                return ResourceManager.GetString("FunctionGroupOperations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to レポートの生成.
        /// </summary>
        internal static string GenerateReport {
            get {
                return ResourceManager.GetString("GenerateReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to グラフカテゴリ選択.
        /// </summary>
        internal static string GraphCategorySelection {
            get {
                return ResourceManager.GetString("GraphCategorySelection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to グラフの種類.
        /// </summary>
        internal static string GraphType {
            get {
                return ResourceManager.GetString("GraphType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フローステップを働かせる.
        /// </summary>
        internal static string GreaterThan {
            get {
                return ResourceManager.GetString("GreaterThan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 作業フロー名.
        /// </summary>
        internal static string GroupQue {
            get {
                return ResourceManager.GetString("GroupQue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to グループのキュー.
        /// </summary>
        internal static string GroupQueue {
            get {
                return ResourceManager.GetString("GroupQueue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ヘッダ.
        /// </summary>
        internal static string Header {
            get {
                return ResourceManager.GetString("Header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 見出し.
        /// </summary>
        internal static string Heading {
            get {
                return ResourceManager.GetString("Heading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ハイ.
        /// </summary>
        internal static string high {
            get {
                return ResourceManager.GetString("high", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 強調表示されたフィールドは必須です.
        /// </summary>
        internal static string HighlightedFieldsareMandatory {
            get {
                return ResourceManager.GetString("HighlightedFieldsareMandatory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HMR.
        /// </summary>
        internal static string HMR {
            get {
                return ResourceManager.GetString("HMR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ホールド.
        /// </summary>
        internal static string Hold {
            get {
                return ResourceManager.GetString("Hold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Holidays.
        /// </summary>
        internal static string Holidays {
            get {
                return ResourceManager.GetString("Holidays", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 番号.
        /// </summary>
        internal static string ID {
            get {
                return ResourceManager.GetString("ID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to インポート.
        /// </summary>
        internal static string Import {
            get {
                return ResourceManager.GetString("Import", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to インポートアクション.
        /// </summary>
        internal static string ImportAction {
            get {
                return ResourceManager.GetString("ImportAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 正常にインポート.
        /// </summary>
        internal static string ImportedSuccessfully {
            get {
                return ResourceManager.GetString("ImportedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to データベースにインポート.
        /// </summary>
        internal static string ImportIntoDatabase {
            get {
                return ResourceManager.GetString("ImportIntoDatabase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 輸入パーツ.
        /// </summary>
        internal static string ImportParts {
            get {
                return ResourceManager.GetString("ImportParts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja In Active Product.
        /// </summary>
        internal static string inactiveproduct {
            get {
                return ResourceManager.GetString("inactiveproduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Incomplete operations are present, do you want to close the Job card?.
        /// </summary>
        internal static string IncompleteOperationsarepresent {
            get {
                return ResourceManager.GetString("IncompleteOperationsarepresent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Incorrect Password.
        /// </summary>
        internal static string IncorrectPassword {
            get {
                return ResourceManager.GetString("IncorrectPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アプリケーションエラーが発生しました.
        /// </summary>
        internal static string IndexOutOfRangeException {
            get {
                return ResourceManager.GetString("IndexOutOfRangeException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 進行中.
        /// </summary>
        internal static string InProgress {
            get {
                return ResourceManager.GetString("InProgress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to プログレスカウントに.
        /// </summary>
        internal static string InProgressCount {
            get {
                return ResourceManager.GetString("InProgressCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 正常に挿入.
        /// </summary>
        internal static string InsertedSuccessfully {
            get {
                return ResourceManager.GetString("InsertedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 内部.
        /// </summary>
        internal static string Internal {
            get {
                return ResourceManager.GetString("Internal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Invalid.
        /// </summary>
        internal static string Invalid {
            get {
                return ResourceManager.GetString("Invalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to JA Prefixsuffix is created as company specific. Cannot change to Branch. To change Delete the Existing record and create Branch Specific Prefix Suffix.
        /// </summary>
        internal static string InvalidBranchSelection {
            get {
                return ResourceManager.GetString("InvalidBranchSelection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アプリケーションエラーが発生しました.
        /// </summary>
        internal static string InvalidCastException {
            get {
                return ResourceManager.GetString("InvalidCastException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to JA Prefixsuffix is created as Branch specific. Cannot change to Company. To change Delete the Existing record and create Company Specific Prefix Suffix.
        /// </summary>
        internal static string InvalidCompanySelection {
            get {
                return ResourceManager.GetString("InvalidCompanySelection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 無効な日付.
        /// </summary>
        internal static string InvalidDate {
            get {
                return ResourceManager.GetString("InvalidDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Invalid Decimal.
        /// </summary>
        internal static string InvalidDecimal {
            get {
                return ResourceManager.GetString("InvalidDecimal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 無効なメール.
        /// </summary>
        internal static string InvalidEmail {
            get {
                return ResourceManager.GetString("InvalidEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 無効なファイル.
        /// </summary>
        internal static string InvalidFile {
            get {
                return ResourceManager.GetString("InvalidFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 郵便番号.
        /// </summary>
        internal static string InvalidMobile {
            get {
                return ResourceManager.GetString("InvalidMobile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 無効なモデル.
        /// </summary>
        internal static string InvalidModel {
            get {
                return ResourceManager.GetString("InvalidModel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 無効なモデルまたはモデルは非アクティブです.
        /// </summary>
        internal static string invalidmodelormodelisinactive {
            get {
                return ResourceManager.GetString("invalidmodelormodelisinactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 無効なパーティ名.
        /// </summary>
        internal static string InvalidName {
            get {
                return ResourceManager.GetString("InvalidName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to データベースエラーが発生しました.
        /// </summary>
        internal static string InvalidOperationException {
            get {
                return ResourceManager.GetString("InvalidOperationException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 無効な部品番号または部品番号が非アクティブである.
        /// </summary>
        internal static string invalidpartnumberorpartnumberisinactive {
            get {
                return ResourceManager.GetString("invalidpartnumberorpartnumberisinactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Phone.
        /// </summary>
        internal static string InvalidPhone {
            get {
                return ResourceManager.GetString("InvalidPhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to なし電話無効.
        /// </summary>
        internal static string InvalidPhoneNo {
            get {
                return ResourceManager.GetString("InvalidPhoneNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 無効な製品.
        /// </summary>
        internal static string InvalidProduct {
            get {
                return ResourceManager.GetString("InvalidProduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 無効なプロダクト一意識別子.
        /// </summary>
        internal static string InvalidProductUniqueNumber {
            get {
                return ResourceManager.GetString("InvalidProductUniqueNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Invalid Quantity..
        /// </summary>
        internal static string InvalidQuantity {
            get {
                return ResourceManager.GetString("InvalidQuantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Invalid Rate..
        /// </summary>
        internal static string InvalidRate {
            get {
                return ResourceManager.GetString("InvalidRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 無効な登録された携帯電話番号.
        /// </summary>
        internal static string InvalidRegisteredMobileNumber {
            get {
                return ResourceManager.GetString("InvalidRegisteredMobileNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 無効な選択.
        /// </summary>
        internal static string invalidselection {
            get {
                return ResourceManager.GetString("invalidselection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Invalid serial number.
        /// </summary>
        internal static string InvalidSerialNumber {
            get {
                return ResourceManager.GetString("InvalidSerialNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 無効なサービス要求番号.
        /// </summary>
        internal static string InvalidServiceRequestNumber {
            get {
                return ResourceManager.GetString("InvalidServiceRequestNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 無効な一意の識別番号.
        /// </summary>
        internal static string InValidUniqueIdentificationNumber {
            get {
                return ResourceManager.GetString("InValidUniqueIdentificationNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アクティブですか？.
        /// </summary>
        internal static string IsActive {
            get {
                return ResourceManager.GetString("IsActive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Adminです.
        /// </summary>
        internal static string IsAdmin {
            get {
                return ResourceManager.GetString("IsAdmin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 基準額は含まれていますか？.
        /// </summary>
        internal static string isbaseamountincluded {
            get {
                return ResourceManager.GetString("isbaseamountincluded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 当社は固有のものです.
        /// </summary>
        internal static string IsCompanySpecific {
            get {
                return ResourceManager.GetString("IsCompanySpecific", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 構成要素である.
        /// </summary>
        internal static string iscomponent {
            get {
                return ResourceManager.GetString("iscomponent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IsCustomer.
        /// </summary>
        internal static string IsCustomer {
            get {
                return ResourceManager.GetString("IsCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to デフォルトの連絡先ですか？.
        /// </summary>
        internal static string IsDefaultContact {
            get {
                return ResourceManager.GetString("IsDefaultContact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 外部にある.
        /// </summary>
        internal static string IsExternal {
            get {
                return ResourceManager.GetString("IsExternal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 危険です.
        /// </summary>
        internal static string isHazardous {
            get {
                return ResourceManager.GetString("isHazardous", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 本社です.
        /// </summary>
        internal static string IsHeadOffice {
            get {
                return ResourceManager.GetString("IsHeadOffice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja is in active.
        /// </summary>
        internal static string isinactive {
            get {
                return ResourceManager.GetString("isinactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作が完了する.
        /// </summary>
        internal static string IsOperationCompleted {
            get {
                return ResourceManager.GetString("IsOperationCompleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 発行日.
        /// </summary>
        internal static string issueddate {
            get {
                return ResourceManager.GetString("issueddate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 発行日付sholud未満または現在の日付に等し&amp;#8203;&amp;#8203;いこと.
        /// </summary>
        internal static string issueddatesholudbelessthanorequaltocurrentdate {
            get {
                return ResourceManager.GetString("issueddatesholudbelessthanorequaltocurrentdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ブレークダウンの下にある？.
        /// </summary>
        internal static string IsUnderBreakDown {
            get {
                return ResourceManager.GetString("IsUnderBreakDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 保証期間内である？.
        /// </summary>
        internal static string isunderwarranty {
            get {
                return ResourceManager.GetString("isunderwarranty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ペットバージョンは何ですか？.
        /// </summary>
        internal static string isversionallowed {
            get {
                return ResourceManager.GetString("isversionallowed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to バージョンが有効になっていますか？.
        /// </summary>
        internal static string IsVersionEnabled {
            get {
                return ResourceManager.GetString("IsVersionEnabled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1月.
        /// </summary>
        internal static string January {
            get {
                return ResourceManager.GetString("January", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ジョブが改正され、続行するか、新しいバージョンを作成するのでしょうか？.
        /// </summary>
        internal static string Jobamendedwillcreatenewversionwanttoproceed {
            get {
                return ResourceManager.GetString("Jobamendedwillcreatenewversionwanttoproceed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ジョブ&amp;#183;カード.
        /// </summary>
        internal static string JobCard {
            get {
                return ResourceManager.GetString("JobCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ジョブカードは理由を断念.
        /// </summary>
        internal static string JobCardAbandonReason {
            get {
                return ResourceManager.GetString("JobCardAbandonReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アーカイブされたジョブ&amp;#183;カード.
        /// </summary>
        internal static string JobcardArchived {
            get {
                return ResourceManager.GetString("JobcardArchived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ジョブカード閉鎖日.
        /// </summary>
        internal static string JobCardClosureDate {
            get {
                return ResourceManager.GetString("JobCardClosureDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ジョブカードクッション時間.
        /// </summary>
        internal static string JobCardCushionHours {
            get {
                return ResourceManager.GetString("JobCardCushionHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ジョブカード日.
        /// </summary>
        internal static string JobCardDate {
            get {
                return ResourceManager.GetString("JobCardDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ジョブカード遅延理由.
        /// </summary>
        internal static string JobCardDelayReason {
            get {
                return ResourceManager.GetString("JobCardDelayReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ジョブカードフィールド検索.
        /// </summary>
        internal static string JobCardFieldSearch {
            get {
                return ResourceManager.GetString("JobCardFieldSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ジョブ&amp;#183;カードは、すでにこのサービスリクエスト番号のために作成され.
        /// </summary>
        internal static string JobCardisalreadycreatedforthisServiceRequestNumber {
            get {
                return ResourceManager.GetString("JobCardisalreadycreatedforthisServiceRequestNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ジョブカード番号.
        /// </summary>
        internal static string JobCardNumber {
            get {
                return ResourceManager.GetString("JobCardNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ジョブ&amp;#183;カード番号が見つかりません.
        /// </summary>
        internal static string JobcardNumbernotfound {
            get {
                return ResourceManager.GetString("JobcardNumbernotfound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ジョブカード番号検索.
        /// </summary>
        internal static string JobCardNumberSearch {
            get {
                return ResourceManager.GetString("JobCardNumberSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ジョブカードはカウントを保留.
        /// </summary>
        internal static string JobCardPendingCount {
            get {
                return ResourceManager.GetString("JobCardPendingCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ジョブ&amp;#183;カードのステータス.
        /// </summary>
        internal static string JobCardStatus {
            get {
                return ResourceManager.GetString("JobCardStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ジョブ&amp;#183;カードの概要.
        /// </summary>
        internal static string JobCardSummary {
            get {
                return ResourceManager.GetString("JobCardSummary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to バージョン.
        /// </summary>
        internal static string JobCardVersion {
            get {
                return ResourceManager.GetString("JobCardVersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 仕事カードWIPカウント.
        /// </summary>
        internal static string JobCardWIPCount {
            get {
                return ResourceManager.GetString("JobCardWIPCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 職務記述書.
        /// </summary>
        internal static string JobDescription {
            get {
                return ResourceManager.GetString("JobDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ジョブの終了日.
        /// </summary>
        internal static string JobEndDate {
            get {
                return ResourceManager.GetString("JobEndDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ジョブ優先順位.
        /// </summary>
        internal static string JobPriority {
            get {
                return ResourceManager.GetString("JobPriority", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 求人サイトのアドレス.
        /// </summary>
        internal static string JobSiteAddress {
            get {
                return ResourceManager.GetString("JobSiteAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 仕事開始日.
        /// </summary>
        internal static string JobStartDate {
            get {
                return ResourceManager.GetString("JobStartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 結合テーブル.
        /// </summary>
        internal static string JoinedTables {
            get {
                return ResourceManager.GetString("JoinedTables", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to と結合.
        /// </summary>
        internal static string JoinWith {
            get {
                return ResourceManager.GetString("JoinWith", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 7月.
        /// </summary>
        internal static string July {
            get {
                return ResourceManager.GetString("July", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 6月.
        /// </summary>
        internal static string June {
            get {
                return ResourceManager.GetString("June", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 固定電話.
        /// </summary>
        internal static string Landline {
            get {
                return ResourceManager.GetString("Landline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 言語.
        /// </summary>
        internal static string Language {
            get {
                return ResourceManager.GetString("Language", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 言語.
        /// </summary>
        internal static string LanguageName {
            get {
                return ResourceManager.GetString("LanguageName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フローステップを働かせる.
        /// </summary>
        internal static string LessThan {
            get {
                return ResourceManager.GetString("LessThan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フローステップを働かせる.
        /// </summary>
        internal static string Like {
            get {
                return ResourceManager.GetString("Like", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 郵便番号.
        /// </summary>
        internal static string Loading {
            get {
                return ResourceManager.GetString("Loading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ローカル.
        /// </summary>
        internal static string Local {
            get {
                return ResourceManager.GetString("Local", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ロケール.
        /// </summary>
        internal static string Locale {
            get {
                return ResourceManager.GetString("Locale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ロケールの詳細.
        /// </summary>
        internal static string LocaleDetails {
            get {
                return ResourceManager.GetString("LocaleDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ロケールの詳細は、ブランドのために利用可能なではありません.
        /// </summary>
        internal static string LocaledetailsarenotavaliableforBrand {
            get {
                return ResourceManager.GetString("LocaledetailsarenotavaliableforBrand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ロケールの詳細は、製品モデルのために利用可能なではありません.
        /// </summary>
        internal static string LocaledetailsarenotavaliableforProductModel {
            get {
                return ResourceManager.GetString("LocaledetailsarenotavaliableforProductModel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ロケールの詳細は、製品の種類のために利用可能なではありません.
        /// </summary>
        internal static string LocaledetailsarenotavaliableforProductType {
            get {
                return ResourceManager.GetString("LocaledetailsarenotavaliableforProductType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 場所.
        /// </summary>
        internal static string Location {
            get {
                return ResourceManager.GetString("Location", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 作業の流れ.
        /// </summary>
        internal static string Lock {
            get {
                return ResourceManager.GetString("Lock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ロック.
        /// </summary>
        internal static string locked {
            get {
                return ResourceManager.GetString("locked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to によってロック.
        /// </summary>
        internal static string lockedby {
            get {
                return ResourceManager.GetString("lockedby", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ログイン.
        /// </summary>
        internal static string Login {
            get {
                return ResourceManager.GetString("Login", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ログインID.
        /// </summary>
        internal static string LoginID {
            get {
                return ResourceManager.GetString("LoginID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ロゴ名.
        /// </summary>
        internal static string LogoName {
            get {
                return ResourceManager.GetString("LogoName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ロー.
        /// </summary>
        internal static string low {
            get {
                return ResourceManager.GetString("low", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to マネージャー.
        /// </summary>
        internal static string Manager {
            get {
                return ResourceManager.GetString("Manager", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 必須サービス.
        /// </summary>
        internal static string mandatoryservices {
            get {
                return ResourceManager.GetString("mandatoryservices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to メーカー.
        /// </summary>
        internal static string Manufacturer {
            get {
                return ResourceManager.GetString("Manufacturer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to マップコラム.
        /// </summary>
        internal static string MapColumns {
            get {
                return ResourceManager.GetString("MapColumns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to マップされた列.
        /// </summary>
        internal static string MappedColumns {
            get {
                return ResourceManager.GetString("MappedColumns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 3月.
        /// </summary>
        internal static string March {
            get {
                return ResourceManager.GetString("March", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to マスターは既に存在しています.
        /// </summary>
        internal static string MasterExists {
            get {
                return ResourceManager.GetString("MasterExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to マスタID.
        /// </summary>
        internal static string MasterID {
            get {
                return ResourceManager.GetString("MasterID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to マスターの名前.
        /// </summary>
        internal static string MasterName {
            get {
                return ResourceManager.GetString("MasterName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 5月.
        /// </summary>
        internal static string May {
            get {
                return ResourceManager.GetString("May", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 培地.
        /// </summary>
        internal static string medium {
            get {
                return ResourceManager.GetString("medium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to メニューの詳細.
        /// </summary>
        internal static string MenuDetail {
            get {
                return ResourceManager.GetString("MenuDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to メニューの詳細.
        /// </summary>
        internal static string MenuDetails {
            get {
                return ResourceManager.GetString("MenuDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to メニュー名.
        /// </summary>
        internal static string MenuName {
            get {
                return ResourceManager.GetString("MenuName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to メニュー名は空白にすることはできません.
        /// </summary>
        internal static string MenuNamecannotbeblank {
            get {
                return ResourceManager.GetString("MenuNamecannotbeblank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to メニューパス.
        /// </summary>
        internal static string MenuPath {
            get {
                return ResourceManager.GetString("MenuPath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 携帯電話.
        /// </summary>
        internal static string Mobile {
            get {
                return ResourceManager.GetString("Mobile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 携帯電話番号.
        /// </summary>
        internal static string MobileNumber {
            get {
                return ResourceManager.GetString("MobileNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to モデル.
        /// </summary>
        internal static string model {
            get {
                return ResourceManager.GetString("model", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to モデル英語.
        /// </summary>
        internal static string modelenglish {
            get {
                return ResourceManager.GetString("modelenglish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Model Field Search.
        /// </summary>
        internal static string ModelFieldSearch {
            get {
                return ResourceManager.GetString("ModelFieldSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to モデルは非アクティブです.
        /// </summary>
        internal static string modelisinactive {
            get {
                return ResourceManager.GetString("modelisinactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to モデルロケール.
        /// </summary>
        internal static string modellocale {
            get {
                return ResourceManager.GetString("modellocale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to モデルマスター.
        /// </summary>
        internal static string modelmaster {
            get {
                return ResourceManager.GetString("modelmaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to モデル名.
        /// </summary>
        internal static string modelname {
            get {
                return ResourceManager.GetString("modelname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to モデル名は既に存在しています.
        /// </summary>
        internal static string modelnamealreadyexists {
            get {
                return ResourceManager.GetString("modelnamealreadyexists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to モデルが見つかりません.
        /// </summary>
        internal static string modelnotfound {
            get {
                return ResourceManager.GetString("modelnotfound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to モデル検索.
        /// </summary>
        internal static string ModelSearch {
            get {
                return ResourceManager.GetString("ModelSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to モジュール.
        /// </summary>
        internal static string Module {
            get {
                return ResourceManager.GetString("Module", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to モジュール名.
        /// </summary>
        internal static string ModuleName {
            get {
                return ResourceManager.GetString("ModuleName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to モジュール名は空白にすることはできません.
        /// </summary>
        internal static string ModuleNameCannotbeblank {
            get {
                return ResourceManager.GetString("ModuleNameCannotbeblank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Monday.
        /// </summary>
        internal static string Monday {
            get {
                return ResourceManager.GetString("Monday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 月.
        /// </summary>
        internal static string month {
            get {
                return ResourceManager.GetString("month", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 保証日.
        /// </summary>
        internal static string MyQue {
            get {
                return ResourceManager.GetString("MyQue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 私のキュー.
        /// </summary>
        internal static string MyQueue {
            get {
                return ResourceManager.GetString("MyQueue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 名前.
        /// </summary>
        internal static string Name {
            get {
                return ResourceManager.GetString("Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja New Password.
        /// </summary>
        internal static string NewPassword {
            get {
                return ResourceManager.GetString("NewPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja New Password and Confirm Password are not matching.
        /// </summary>
        internal static string newpasswordandConfirmpasswordarenotmatching {
            get {
                return ResourceManager.GetString("newpasswordandConfirmpasswordarenotmatching", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &amp;gt; 90時間対応.
        /// </summary>
        internal static string NintyHour {
            get {
                return ResourceManager.GetString("NintyHour", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ノー.
        /// </summary>
        internal static string no {
            get {
                return ResourceManager.GetString("no", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 作ら変更はありません.
        /// </summary>
        internal static string NoChangesMade {
            get {
                return ResourceManager.GetString("NoChangesMade", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 保存しよ変更はありません.
        /// </summary>
        internal static string NochangesmadetoSave {
            get {
                return ResourceManager.GetString("NochangesmadetoSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja No Edit permission for Product Master.
        /// </summary>
        internal static string Noeditpermissionforproductmaster {
            get {
                return ResourceManager.GetString("Noeditpermissionforproductmaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 当事者が非アクティブであるか、または一致するレコードが見つからないと、このパーティを追加しますか.
        /// </summary>
        internal static string NomatchingrecordfoundDoyouwanttoadd {
            get {
                return ResourceManager.GetString("NomatchingrecordfoundDoyouwanttoadd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 課税対象となる非.
        /// </summary>
        internal static string NonTaxable {
            get {
                return ResourceManager.GetString("NonTaxable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 非課税その他の費用1.
        /// </summary>
        internal static string NonTaxableothercharges1 {
            get {
                return ResourceManager.GetString("NonTaxableothercharges1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 非課税対象額その他の費用1.
        /// </summary>
        internal static string NonTaxableothercharges1Amount {
            get {
                return ResourceManager.GetString("NonTaxableothercharges1Amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 非課税その他の費用2.
        /// </summary>
        internal static string NonTaxableothercharges2 {
            get {
                return ResourceManager.GetString("NonTaxableothercharges2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 非課税対象額その他の費用2.
        /// </summary>
        internal static string NonTaxableothercharges2Amount {
            get {
                return ResourceManager.GetString("NonTaxableothercharges2Amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 完成したサービス要求の数.
        /// </summary>
        internal static string NoOfSRCompleted {
            get {
                return ResourceManager.GetString("NoOfSRCompleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービスリクエストの数が受け取っ.
        /// </summary>
        internal static string NoOfSRRecieved {
            get {
                return ResourceManager.GetString("NoOfSRRecieved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to いいえ製品は、選択した顧客に関連付けられていません.
        /// </summary>
        internal static string Noproductisassociatedwithselectedcustomer {
            get {
                return ResourceManager.GetString("Noproductisassociatedwithselectedcustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フローステップを働かせる.
        /// </summary>
        internal static string Norecordstoview {
            get {
                return ResourceManager.GetString("Norecordstoview", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フローステップを働かせる.
        /// </summary>
        internal static string NotEqual {
            get {
                return ResourceManager.GetString("NotEqual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Not Found.
        /// </summary>
        internal static string NotFound {
            get {
                return ResourceManager.GetString("NotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 11月.
        /// </summary>
        internal static string November {
            get {
                return ResourceManager.GetString("November", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アプリケーションエラーが発生しました.
        /// </summary>
        internal static string NullReferenceException {
            get {
                return ResourceManager.GetString("NullReferenceException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 数.
        /// </summary>
        internal static string number {
            get {
                return ResourceManager.GetString("number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to オブジェクトの説明.
        /// </summary>
        internal static string ObjectDescription {
            get {
                return ResourceManager.GetString("ObjectDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to オブジェクトの説明は空白にすることはできません.
        /// </summary>
        internal static string ObjectDescriptioncannotbeblank {
            get {
                return ResourceManager.GetString("ObjectDescriptioncannotbeblank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to オブジェクトマスター.
        /// </summary>
        internal static string ObjectMaster {
            get {
                return ResourceManager.GetString("ObjectMaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to オブジェクト名.
        /// </summary>
        internal static string ObjectName {
            get {
                return ResourceManager.GetString("ObjectName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to オブジェクト名は空白にすることはできません.
        /// </summary>
        internal static string ObjectNamecannotbeblank {
            get {
                return ResourceManager.GetString("ObjectNamecannotbeblank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to オブジェクトが正常に保存され.
        /// </summary>
        internal static string ObjectssavedSuccessfully {
            get {
                return ResourceManager.GetString("ObjectssavedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 10月.
        /// </summary>
        internal static string October {
            get {
                return ResourceManager.GetString("October", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 年.
        /// </summary>
        internal static string of {
            get {
                return ResourceManager.GetString("of", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Old Password.
        /// </summary>
        internal static string OldPassword {
            get {
                return ResourceManager.GetString("OldPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OnHoldカウント.
        /// </summary>
        internal static string OnHoldCount {
            get {
                return ResourceManager.GetString("OnHoldCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 唯一のアクティブな顧客deatilsは編集することができます.
        /// </summary>
        internal static string onlyactivecustomerdeatilscanbeedited {
            get {
                return ResourceManager.GetString("onlyactivecustomerdeatilscanbeedited", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 唯一のアクティブな保証を編集することができます.
        /// </summary>
        internal static string onlyactivewarrantycanbeedited {
            get {
                return ResourceManager.GetString("onlyactivewarrantycanbeedited", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 開く.
        /// </summary>
        internal static string open {
            get {
                return ResourceManager.GetString("open", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to レポートを開く.
        /// </summary>
        internal static string OpenReport {
            get {
                return ResourceManager.GetString("OpenReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作.
        /// </summary>
        internal static string operation {
            get {
                return ResourceManager.GetString("operation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作および従業員がすでに関連付けられて.
        /// </summary>
        internal static string OperationandEmployeeisalreadyassociated {
            get {
                return ResourceManager.GetString("OperationandEmployeeisalreadyassociated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to オペレーションコード.
        /// </summary>
        internal static string OperationCode {
            get {
                return ResourceManager.GetString("OperationCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to オペレーションコードは既に存在しています.
        /// </summary>
        internal static string Operationcodealreadyexists {
            get {
                return ResourceManager.GetString("Operationcodealreadyexists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作コードareadyは、選択した.
        /// </summary>
        internal static string OperationCodeAlreadySelected {
            get {
                return ResourceManager.GetString("OperationCodeAlreadySelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to オペレーションコードが見つかりません.
        /// </summary>
        internal static string OperationCodeNotFound {
            get {
                return ResourceManager.GetString("OperationCodeNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作説明.
        /// </summary>
        internal static string OperationDescription {
            get {
                return ResourceManager.GetString("OperationDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作の詳細.
        /// </summary>
        internal static string OperationDetails {
            get {
                return ResourceManager.GetString("OperationDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作偏差レポート.
        /// </summary>
        internal static string OperationDeviationReport {
            get {
                return ResourceManager.GetString("OperationDeviationReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作員詳細.
        /// </summary>
        internal static string OperationEmployeeDetails {
            get {
                return ResourceManager.GetString("OperationEmployeeDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 運転終了日.
        /// </summary>
        internal static string OperationEndDate {
            get {
                return ResourceManager.GetString("OperationEndDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作の終了日は運転開始日よりも小さくすることはできません.
        /// </summary>
        internal static string OperationEndDateCannotbelessthanoperationStartDate {
            get {
                return ResourceManager.GetString("OperationEndDateCannotbelessthanoperationStartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作英語.
        /// </summary>
        internal static string operationenglish {
            get {
                return ResourceManager.GetString("operationenglish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to オペレーションフィールド検索.
        /// </summary>
        internal static string OperationFieldSearch {
            get {
                return ResourceManager.GetString("OperationFieldSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作ヘッダー.
        /// </summary>
        internal static string operationheader {
            get {
                return ResourceManager.GetString("operationheader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 運転時間.
        /// </summary>
        internal static string OperationHours {
            get {
                return ResourceManager.GetString("OperationHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作は、すでに別の従業員に関連付けられている.
        /// </summary>
        internal static string Operationisalreadyassociatedwithanotheremployee {
            get {
                return ResourceManager.GetString("Operationisalreadyassociatedwithanotheremployee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作のロケール.
        /// </summary>
        internal static string operationlocale {
            get {
                return ResourceManager.GetString("operationlocale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作マスター.
        /// </summary>
        internal static string operationmaster {
            get {
                return ResourceManager.GetString("operationmaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 運転開始日.
        /// </summary>
        internal static string OperationStartDate {
            get {
                return ResourceManager.GetString("OperationStartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Operation start date cannot be less than job card date.
        /// </summary>
        internal static string OperationStartdatecannotbelessthanJobcarddate {
            get {
                return ResourceManager.GetString("OperationStartdatecannotbelessthanJobcarddate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to オペレータ.
        /// </summary>
        internal static string Operator {
            get {
                return ResourceManager.GetString("Operator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フローステップを働かせる.
        /// </summary>
        internal static string OR {
            get {
                return ResourceManager.GetString("OR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to その他の詳細.
        /// </summary>
        internal static string OtherDetail {
            get {
                return ResourceManager.GetString("OtherDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to その他の詳細.
        /// </summary>
        internal static string OtherDetails {
            get {
                return ResourceManager.GetString("OtherDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 保証日.
        /// </summary>
        internal static string Others {
            get {
                return ResourceManager.GetString("Others", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アプリケーションエラーが発生しました.
        /// </summary>
        internal static string OutOfMemoryException {
            get {
                return ResourceManager.GetString("OutOfMemoryException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to はい.
        /// </summary>
        internal static string Page {
            get {
                return ResourceManager.GetString("Page", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 親会社.
        /// </summary>
        internal static string ParentCompany {
            get {
                return ResourceManager.GetString("ParentCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 親会社の操作を削除することはできません.
        /// </summary>
        internal static string parentcompanyoperationcannotbedeleted {
            get {
                return ResourceManager.GetString("parentcompanyoperationcannotbedeleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 親会社の操作を編集することはできません.
        /// </summary>
        internal static string parentcompanyoperationcannotbeedited {
            get {
                return ResourceManager.GetString("parentcompanyoperationcannotbeedited", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 親メニュー.
        /// </summary>
        internal static string ParentMenu {
            get {
                return ResourceManager.GetString("ParentMenu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Part Already Added..
        /// </summary>
        internal static string PartAlreadyAdded {
            get {
                return ResourceManager.GetString("PartAlreadyAdded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツカテゴリ.
        /// </summary>
        internal static string partcategory {
            get {
                return ResourceManager.GetString("partcategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 部品記述.
        /// </summary>
        internal static string partdescription {
            get {
                return ResourceManager.GetString("partdescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツの機能グループ.
        /// </summary>
        internal static string partfunctiongroup {
            get {
                return ResourceManager.GetString("partfunctiongroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パートナー.
        /// </summary>
        internal static string Partner {
            get {
                return ResourceManager.GetString("Partner", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パートナー.
        /// </summary>
        internal static string PartnerName {
            get {
                return ResourceManager.GetString("PartnerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 部品番号.
        /// </summary>
        internal static string partnumber {
            get {
                return ResourceManager.GetString("partnumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 部品番号がすでに存在している.
        /// </summary>
        internal static string partnumberalreadyexists {
            get {
                return ResourceManager.GetString("partnumberalreadyexists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Part Number already selected.
        /// </summary>
        internal static string PartNumberalreadyselected {
            get {
                return ResourceManager.GetString("PartNumberalreadyselected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Part Number is blank..
        /// </summary>
        internal static string PartNumberisBlank {
            get {
                return ResourceManager.GetString("PartNumberisBlank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Part Number not associated to the selected product details..
        /// </summary>
        internal static string PartNumbernotassociatedtotheselectedproductdetails {
            get {
                return ResourceManager.GetString("PartNumbernotassociatedtotheselectedproductdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 部品番号が見つかりません.
        /// </summary>
        internal static string PartNumbernotfound {
            get {
                return ResourceManager.GetString("PartNumbernotfound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 品番検索.
        /// </summary>
        internal static string partnumbersearch {
            get {
                return ResourceManager.GetString("partnumbersearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツ価格.
        /// </summary>
        internal static string partprice {
            get {
                return ResourceManager.GetString("partprice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツの価格詳細.
        /// </summary>
        internal static string partpricepdetails {
            get {
                return ResourceManager.GetString("partpricepdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 一部商品の種類の詳細.
        /// </summary>
        internal static string partproducttypedetails {
            get {
                return ResourceManager.GetString("partproducttypedetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Parts.
        /// </summary>
        internal static string Parts {
            get {
                return ResourceManager.GetString("Parts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 部品詳細.
        /// </summary>
        internal static string PartsDetail {
            get {
                return ResourceManager.GetString("PartsDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツの詳細.
        /// </summary>
        internal static string PartsDetails {
            get {
                return ResourceManager.GetString("PartsDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツの英語.
        /// </summary>
        internal static string partsenglish {
            get {
                return ResourceManager.GetString("partsenglish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツフィールド検索.
        /// </summary>
        internal static string PartsFieldSearch {
            get {
                return ResourceManager.GetString("PartsFieldSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツフリーフォト詳細.
        /// </summary>
        internal static string partsfreestockdetails {
            get {
                return ResourceManager.GetString("partsfreestockdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 部品ロケール.
        /// </summary>
        internal static string partslocale {
            get {
                return ResourceManager.GetString("partslocale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツマスター.
        /// </summary>
        internal static string partsmaster {
            get {
                return ResourceManager.GetString("partsmaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツマスターロケール.
        /// </summary>
        internal static string partsmasterlocale {
            get {
                return ResourceManager.GetString("partsmasterlocale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツマスターヘッダー.
        /// </summary>
        internal static string partspmasterheader {
            get {
                return ResourceManager.GetString("partspmasterheader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 部品価格詳細.
        /// </summary>
        internal static string partspricedetails {
            get {
                return ResourceManager.GetString("partspricedetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツ製品タイプのロケール.
        /// </summary>
        internal static string partsproducttypelocale {
            get {
                return ResourceManager.GetString("partsproducttypelocale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツテンプレート.
        /// </summary>
        internal static string PartsTemplate {
            get {
                return ResourceManager.GetString("PartsTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 部品合計金額.
        /// </summary>
        internal static string PartsTotalAmount {
            get {
                return ResourceManager.GetString("PartsTotalAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーティー.
        /// </summary>
        internal static string Party {
            get {
                return ResourceManager.GetString("Party", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーティーの詳細.
        /// </summary>
        internal static string PartyDetails {
            get {
                return ResourceManager.GetString("PartyDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja  Party Field Search.
        /// </summary>
        internal static string PartyFielSearch {
            get {
                return ResourceManager.GetString("PartyFielSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーティーの場所.
        /// </summary>
        internal static string PartyLocation {
            get {
                return ResourceManager.GetString("PartyLocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーティーモバイル.
        /// </summary>
        internal static string PartyMobile {
            get {
                return ResourceManager.GetString("PartyMobile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーティ名.
        /// </summary>
        internal static string PartyName {
            get {
                return ResourceManager.GetString("PartyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーティーが見つかりません.
        /// </summary>
        internal static string PartyNotFound {
            get {
                return ResourceManager.GetString("PartyNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーティ電話.
        /// </summary>
        internal static string PartyPhone {
            get {
                return ResourceManager.GetString("PartyPhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Party Schedule.
        /// </summary>
        internal static string PartySchedule {
            get {
                return ResourceManager.GetString("PartySchedule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーティー検索.
        /// </summary>
        internal static string PartySearch {
            get {
                return ResourceManager.GetString("PartySearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーティーの種類.
        /// </summary>
        internal static string PartyType {
            get {
                return ResourceManager.GetString("PartyType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パスワード.
        /// </summary>
        internal static string Password {
            get {
                return ResourceManager.GetString("Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パスワードを確認すると、指定されたパスワードと一致していません.
        /// </summary>
        internal static string Passwordandconfirmpasswordshouldmatch {
            get {
                return ResourceManager.GetString("Passwordandconfirmpasswordshouldmatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 支払条件.
        /// </summary>
        internal static string PaymentTerms {
            get {
                return ResourceManager.GetString("PaymentTerms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to バージョン.
        /// </summary>
        internal static string PDF {
            get {
                return ResourceManager.GetString("PDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ペンディング.
        /// </summary>
        internal static string Pending {
            get {
                return ResourceManager.GetString("Pending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーセント偏差.
        /// </summary>
        internal static string PercentageDeviation {
            get {
                return ResourceManager.GetString("PercentageDeviation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 郵便番号.
        /// </summary>
        internal static string Personal {
            get {
                return ResourceManager.GetString("Personal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Personal Calendar.
        /// </summary>
        internal static string PersonalCalender {
            get {
                return ResourceManager.GetString("PersonalCalender", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 電話.
        /// </summary>
        internal static string Phone {
            get {
                return ResourceManager.GetString("Phone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 電話.
        /// </summary>
        internal static string PhoneNo {
            get {
                return ResourceManager.GetString("PhoneNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 円グラフ.
        /// </summary>
        internal static string PieChart {
            get {
                return ResourceManager.GetString("PieChart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 計画完了日.
        /// </summary>
        internal static string PlannedCompletionDate {
            get {
                return ResourceManager.GetString("PlannedCompletionDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 計画完了日が開始日より小さくすることはできません.
        /// </summary>
        internal static string PlannedCompletionDatecannotbelessthanStartDate {
            get {
                return ResourceManager.GetString("PlannedCompletionDatecannotbelessthanStartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 計画開始日.
        /// </summary>
        internal static string PlannedStartDate {
            get {
                return ResourceManager.GetString("PlannedStartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Please add atleast one operation detail.
        /// </summary>
        internal static string Pleaseaddatleastoneoperationdetail {
            get {
                return ResourceManager.GetString("Pleaseaddatleastoneoperationdetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 詳細を入力し完了してください.
        /// </summary>
        internal static string pleasecompleteenteringdetails {
            get {
                return ResourceManager.GetString("pleasecompleteenteringdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Please Enter Date As.
        /// </summary>
        internal static string PleaseEnterDateAs {
            get {
                return ResourceManager.GetString("PleaseEnterDateAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フローステップを働かせる.
        /// </summary>
        internal static string PleaseenterIntegerValue {
            get {
                return ResourceManager.GetString("PleaseenterIntegerValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ログインIDを入力してください.
        /// </summary>
        internal static string PleaseEnterLoginID {
            get {
                return ResourceManager.GetString("PleaseEnterLoginID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーティ名を入力してください.
        /// </summary>
        internal static string PleaseenterPartyName {
            get {
                return ResourceManager.GetString("PleaseenterPartyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パスワードを入力してください.
        /// </summary>
        internal static string PleaseEnterPassword {
            get {
                return ResourceManager.GetString("PleaseEnterPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to レポートヘッダーを入力してください.
        /// </summary>
        internal static string PleaseenterReportHeader {
            get {
                return ResourceManager.GetString("PleaseenterReportHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to レポート名を入力してください.
        /// </summary>
        internal static string PleaseenterReportName {
            get {
                return ResourceManager.GetString("PleaseenterReportName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Please enter serail number.
        /// </summary>
        internal static string Pleaseenterserailnumber {
            get {
                return ResourceManager.GetString("Pleaseenterserailnumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ユーザーの詳細を入力してください.
        /// </summary>
        internal static string Pleaseenteruserdetails {
            get {
                return ResourceManager.GetString("Pleaseenteruserdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 有効なモデルを入力してください.
        /// </summary>
        internal static string pleaseentervalidmodel {
            get {
                return ResourceManager.GetString("pleaseentervalidmodel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 値を入力してください.
        /// </summary>
        internal static string Pleaseentervalue {
            get {
                return ResourceManager.GetString("Pleaseentervalue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to value1を入力してください.
        /// </summary>
        internal static string Pleaseentervalue1 {
            get {
                return ResourceManager.GetString("Pleaseentervalue1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to メニュー名を入力してください.
        /// </summary>
        internal static string PleaseprovideMenuName {
            get {
                return ResourceManager.GetString("PleaseprovideMenuName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to モジュール名を指定してください.
        /// </summary>
        internal static string PleaseprovideModuleName {
            get {
                return ResourceManager.GetString("PleaseprovideModuleName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パスワードを入力してください.
        /// </summary>
        internal static string Pleaseprovidepassword {
            get {
                return ResourceManager.GetString("Pleaseprovidepassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作の詳細を保存してください.
        /// </summary>
        internal static string pleasesavetheOperationdata {
            get {
                return ResourceManager.GetString("pleasesavetheOperationdata", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 部品の詳細を保存してください.
        /// </summary>
        internal static string pleasesavethepartsdata {
            get {
                return ResourceManager.GetString("pleasesavethepartsdata", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービスの詳細を保存してください.
        /// </summary>
        internal static string pleasesavetheServicedata {
            get {
                return ResourceManager.GetString("pleasesavetheServicedata", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 諸口詳細を保存してください.
        /// </summary>
        internal static string pleasesavethesundrydata {
            get {
                return ResourceManager.GetString("pleasesavethesundrydata", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フローステップを働かせる.
        /// </summary>
        internal static string PleaseselectaColumn {
            get {
                return ResourceManager.GetString("PleaseselectaColumn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フローステップを働かせる.
        /// </summary>
        internal static string PleaseselectaCondition {
            get {
                return ResourceManager.GetString("PleaseselectaCondition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アップロードするファイルを選択してください.
        /// </summary>
        internal static string Pleaseselectafiletoupload {
            get {
                return ResourceManager.GetString("Pleaseselectafiletoupload", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フローステップを働かせる.
        /// </summary>
        internal static string PleaseselectaOperator {
            get {
                return ResourceManager.GetString("PleaseselectaOperator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 支店を選択してください.
        /// </summary>
        internal static string PleaseSelectBranch {
            get {
                return ResourceManager.GetString("PleaseSelectBranch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ブランドを選択してください.
        /// </summary>
        internal static string PleaseselectBrand {
            get {
                return ResourceManager.GetString("PleaseselectBrand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 会社を選択してください.
        /// </summary>
        internal static string Pleaseselectcompany {
            get {
                return ResourceManager.GetString("Pleaseselectcompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 条件を選択してください.
        /// </summary>
        internal static string Pleaseselectcondition {
            get {
                return ResourceManager.GetString("Pleaseselectcondition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ファイルを選択してください.
        /// </summary>
        internal static string PleaseselectFile {
            get {
                return ResourceManager.GetString("PleaseselectFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to グラフのカテゴリを選択してください.
        /// </summary>
        internal static string PleaseSelectGraphCategory {
            get {
                return ResourceManager.GetString("PleaseSelectGraphCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Please Select Graph Type.
        /// </summary>
        internal static string PleaseSelectGraphType {
            get {
                return ResourceManager.GetString("PleaseSelectGraphType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to モデルを選択してください.
        /// </summary>
        internal static string pleaseselectModel {
            get {
                return ResourceManager.GetString("pleaseselectModel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to セレクトモデルとシリアル番号を喜ばす.
        /// </summary>
        internal static string PleaseselectmodelandSerialNumber {
            get {
                return ResourceManager.GetString("PleaseselectmodelandSerialNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 演算子を選択してください.
        /// </summary>
        internal static string Pleaseselectoperator {
            get {
                return ResourceManager.GetString("Pleaseselectoperator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 削除するレコードを選択してください.
        /// </summary>
        internal static string Pleaseselectrecordstodelete {
            get {
                return ResourceManager.GetString("Pleaseselectrecordstodelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to JA Please select service request number.
        /// </summary>
        internal static string PleaseselectServicerequestnumber {
            get {
                return ResourceManager.GetString("PleaseselectServicerequestnumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 列名を選択してください.
        /// </summary>
        internal static string PleaseselecttheColumnName {
            get {
                return ResourceManager.GetString("PleaseselecttheColumnName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to マップする列を選択してください.
        /// </summary>
        internal static string Pleaseselectthecolumnstomap {
            get {
                return ResourceManager.GetString("Pleaseselectthecolumnstomap", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 演算子を選択してください.
        /// </summary>
        internal static string Pleaseselecttheoperator {
            get {
                return ResourceManager.GetString("Pleaseselecttheoperator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to テーブル名を選択してください.
        /// </summary>
        internal static string PleaseselecttheTableName {
            get {
                return ResourceManager.GetString("PleaseselecttheTableName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 保存するようにユーザーを選択してください.
        /// </summary>
        internal static string PleaseselectUserstosave {
            get {
                return ResourceManager.GetString("PleaseselectUserstosave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Preffixサフィックスが存在しません.
        /// </summary>
        internal static string PreffixSuffixDoesntExists {
            get {
                return ResourceManager.GetString("PreffixSuffixDoesntExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 接頭辞.
        /// </summary>
        internal static string prefix {
            get {
                return ResourceManager.GetString("prefix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 接頭語接尾辞.
        /// </summary>
        internal static string prefixsuffix {
            get {
                return ResourceManager.GetString("prefixsuffix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 前の日は、空にすることはできません.
        /// </summary>
        internal static string PreviousdateCannotbeempty {
            get {
                return ResourceManager.GetString("PreviousdateCannotbeempty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 価格は空白またはゼロにすることはできません.
        /// </summary>
        internal static string PricecannotbeBlankorZero {
            get {
                return ResourceManager.GetString("PricecannotbeBlankorZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to プライマリセグメント.
        /// </summary>
        internal static string PrimarySegment {
            get {
                return ResourceManager.GetString("PrimarySegment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 印刷.
        /// </summary>
        internal static string Print {
            get {
                return ResourceManager.GetString("Print", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アクションを印刷.
        /// </summary>
        internal static string PrintAction {
            get {
                return ResourceManager.GetString("PrintAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 優先順位.
        /// </summary>
        internal static string Priority {
            get {
                return ResourceManager.GetString("Priority", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 優先度は、0から255の間&amp;#8203;&amp;#8203;でなければなりません.
        /// </summary>
        internal static string PriorityShouldBeBetweenzeroandtwofiftyfive {
            get {
                return ResourceManager.GetString("PriorityShouldBeBetweenzeroandtwofiftyfive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 製品.
        /// </summary>
        internal static string product {
            get {
                return ResourceManager.GetString("product", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 製品協会.
        /// </summary>
        internal static string ProductAssociation {
            get {
                return ResourceManager.GetString("ProductAssociation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 製品の詳細.
        /// </summary>
        internal static string productdetail {
            get {
                return ResourceManager.GetString("productdetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 商品の詳細.
        /// </summary>
        internal static string productdetails {
            get {
                return ResourceManager.GetString("productdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to プロダクトID.
        /// </summary>
        internal static string productid {
            get {
                return ResourceManager.GetString("productid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 製品は現在どの顧客とasscociatedされていません.
        /// </summary>
        internal static string Productisnotasscociatedwithanycustomer {
            get {
                return ResourceManager.GetString("Productisnotasscociatedwithanycustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 製品の場所.
        /// </summary>
        internal static string ProductLocation {
            get {
                return ResourceManager.GetString("ProductLocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 製品のリーディング.
        /// </summary>
        internal static string ProductReading {
            get {
                return ResourceManager.GetString("ProductReading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Product Schedule.
        /// </summary>
        internal static string ProductSchedule {
            get {
                return ResourceManager.GetString("ProductSchedule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 製品サービスの歴史.
        /// </summary>
        internal static string ProductServiceHistory {
            get {
                return ResourceManager.GetString("ProductServiceHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 製品タイプ.
        /// </summary>
        internal static string Producttype {
            get {
                return ResourceManager.GetString("Producttype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 製品タイプ英語.
        /// </summary>
        internal static string producttypeenglish {
            get {
                return ResourceManager.GetString("producttypeenglish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 製品タイプのロケール.
        /// </summary>
        internal static string producttypelocale {
            get {
                return ResourceManager.GetString("producttypelocale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 製品タイプのマスター.
        /// </summary>
        internal static string producttypemaster {
            get {
                return ResourceManager.GetString("producttypemaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 製品形名.
        /// </summary>
        internal static string producttypename {
            get {
                return ResourceManager.GetString("producttypename", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 製品型名はすでに存在している.
        /// </summary>
        internal static string producttypenamealreadyexists {
            get {
                return ResourceManager.GetString("producttypenamealreadyexists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ユニークな識別子.
        /// </summary>
        internal static string ProductUniqueNo {
            get {
                return ResourceManager.GetString("ProductUniqueNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to プロスペクト.
        /// </summary>
        internal static string Prospect {
            get {
                return ResourceManager.GetString("Prospect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 量.
        /// </summary>
        internal static string Quantity {
            get {
                return ResourceManager.GetString("Quantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity Cannot be Zero.
        /// </summary>
        internal static string QuantityCannotbeZero {
            get {
                return ResourceManager.GetString("QuantityCannotbeZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quest Informatics Private Limited.
        /// </summary>
        internal static string QuestInformaticsPrivateLimited {
            get {
                return ResourceManager.GetString("QuestInformaticsPrivateLimited", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Queue.
        /// </summary>
        internal static string Queue {
            get {
                return ResourceManager.GetString("Queue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Quick Links.
        /// </summary>
        internal static string Quicklinks {
            get {
                return ResourceManager.GetString("Quicklinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 引用.
        /// </summary>
        internal static string Quotation {
            get {
                return ResourceManager.GetString("Quotation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 引用額.
        /// </summary>
        internal static string QuotationAmount {
            get {
                return ResourceManager.GetString("QuotationAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 引用の詳細.
        /// </summary>
        internal static string QuotationDetail {
            get {
                return ResourceManager.GetString("QuotationDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 引用数.
        /// </summary>
        internal static string QuotationNumber {
            get {
                return ResourceManager.GetString("QuotationNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 引用優先.
        /// </summary>
        internal static string QuotationPriority {
            get {
                return ResourceManager.GetString("QuotationPriority", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 範囲は空白にすることはできません.
        /// </summary>
        internal static string RangecannotbeBlank {
            get {
                return ResourceManager.GetString("RangecannotbeBlank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 率.
        /// </summary>
        internal static string Rate {
            get {
                return ResourceManager.GetString("Rate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rate Cannot be Zero.
        /// </summary>
        internal static string RateCannotbeZero {
            get {
                return ResourceManager.GetString("RateCannotbeZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 評価.
        /// </summary>
        internal static string Rating {
            get {
                return ResourceManager.GetString("Rating", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 評価は1から10の間でなければなりません.
        /// </summary>
        internal static string Ratingshouldbebetween1and10 {
            get {
                return ResourceManager.GetString("Ratingshouldbebetween1and10", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rating should between 1 and 10.
        /// </summary>
        internal static string RatingshouldBetween1and10 {
            get {
                return ResourceManager.GetString("RatingshouldBetween1and10", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 読む.
        /// </summary>
        internal static string Read {
            get {
                return ResourceManager.GetString("Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アクションを読む.
        /// </summary>
        internal static string ReadAction {
            get {
                return ResourceManager.GetString("ReadAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 読書.
        /// </summary>
        internal static string reading {
            get {
                return ResourceManager.GetString("reading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to リミットを読んで.
        /// </summary>
        internal static string readinglimit {
            get {
                return ResourceManager.GetString("readinglimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ログを読んで.
        /// </summary>
        internal static string readinglog {
            get {
                return ResourceManager.GetString("readinglog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 実現レポート.
        /// </summary>
        internal static string realizationreport {
            get {
                return ResourceManager.GetString("realizationreport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アクティブでない理由.
        /// </summary>
        internal static string reasonforinactive {
            get {
                return ResourceManager.GetString("reasonforinactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Received Count.
        /// </summary>
        internal static string ReceivedCount {
            get {
                return ResourceManager.GetString("ReceivedCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 最近の活動リンク.
        /// </summary>
        internal static string RecentActivityLinks {
            get {
                return ResourceManager.GetString("RecentActivityLinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 受け取っ時間.
        /// </summary>
        internal static string RecievedTime {
            get {
                return ResourceManager.GetString("RecievedTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recieviedカウント.
        /// </summary>
        internal static string RecieviedCount {
            get {
                return ResourceManager.GetString("RecieviedCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to レコードが正常に保存され.
        /// </summary>
        internal static string Recordsavedsuccessfully {
            get {
                return ResourceManager.GetString("Recordsavedsuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 参照の詳細.
        /// </summary>
        internal static string ReferenceDetail {
            get {
                return ResourceManager.GetString("ReferenceDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to リファレンス&amp;#183;マスターズ.
        /// </summary>
        internal static string ReferenceMasters {
            get {
                return ResourceManager.GetString("ReferenceMasters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 参照テーブル.
        /// </summary>
        internal static string ReferenceTables {
            get {
                return ResourceManager.GetString("ReferenceTables", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to リフレッシュ.
        /// </summary>
        internal static string refresh {
            get {
                return ResourceManager.GetString("refresh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 地域.
        /// </summary>
        internal static string Region {
            get {
                return ResourceManager.GetString("Region", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 登録された.
        /// </summary>
        internal static string Registered {
            get {
                return ResourceManager.GetString("Registered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 登録された携帯.
        /// </summary>
        internal static string RegisteredMobile {
            get {
                return ResourceManager.GetString("RegisteredMobile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Re-Login.
        /// </summary>
        internal static string relogin {
            get {
                return ResourceManager.GetString("relogin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 備考.
        /// </summary>
        internal static string Remarks {
            get {
                return ResourceManager.GetString("Remarks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フィルタの削除.
        /// </summary>
        internal static string RemoveFilter {
            get {
                return ResourceManager.GetString("RemoveFilter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to レポートウィザード.
        /// </summary>
        internal static string ReportWizard {
            get {
                return ResourceManager.GetString("ReportWizard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to REQ日.
        /// </summary>
        internal static string ReqDate {
            get {
                return ResourceManager.GetString("ReqDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to REQ数.
        /// </summary>
        internal static string ReqNumber {
            get {
                return ResourceManager.GetString("ReqNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to リクエスト説明.
        /// </summary>
        internal static string RequestDescription {
            get {
                return ResourceManager.GetString("RequestDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 郵便番号.
        /// </summary>
        internal static string RequestNumber {
            get {
                return ResourceManager.GetString("RequestNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Reset.
        /// </summary>
        internal static string Reset {
            get {
                return ResourceManager.GetString("Reset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 時間分解能.
        /// </summary>
        internal static string resolutiontime {
            get {
                return ResourceManager.GetString("resolutiontime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 時間スロットに解決時間.
        /// </summary>
        internal static string Resolutiontimewithtimeslots {
            get {
                return ResourceManager.GetString("Resolutiontimewithtimeslots", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to リソース使用率レポート.
        /// </summary>
        internal static string ResourceUtilizationReport {
            get {
                return ResourceManager.GetString("ResourceUtilizationReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 応答時間.
        /// </summary>
        internal static string responsetime {
            get {
                return ResourceManager.GetString("responsetime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 収入は空白にすることはできません.
        /// </summary>
        internal static string RevenuecannotbeBlankorzero {
            get {
                return ResourceManager.GetString("RevenuecannotbeBlankorzero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 発生した収益.
        /// </summary>
        internal static string RevenueGenerated {
            get {
                return ResourceManager.GetString("RevenueGenerated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to もっとして収入.
        /// </summary>
        internal static string revenuemorethen {
            get {
                return ResourceManager.GetString("revenuemorethen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 役割の定義.
        /// </summary>
        internal static string RoleDefinition {
            get {
                return ResourceManager.GetString("RoleDefinition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 役割名.
        /// </summary>
        internal static string RoleName {
            get {
                return ResourceManager.GetString("RoleName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 役割名は空白にすることはできません.
        /// </summary>
        internal static string RoleNameCannotbeblank {
            get {
                return ResourceManager.GetString("RoleNameCannotbeblank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Roleオブジェクト.
        /// </summary>
        internal static string RoleObject {
            get {
                return ResourceManager.GetString("RoleObject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 役割.
        /// </summary>
        internal static string Roles {
            get {
                return ResourceManager.GetString("Roles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 丸める.
        /// </summary>
        internal static string RoundOff {
            get {
                return ResourceManager.GetString("RoundOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Saturday.
        /// </summary>
        internal static string Saturday {
            get {
                return ResourceManager.GetString("Saturday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 保存.
        /// </summary>
        internal static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to アクションを保存.
        /// </summary>
        internal static string SaveAction {
            get {
                return ResourceManager.GetString("SaveAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 正常に保存されました.
        /// </summary>
        internal static string SavedSuccessfully {
            get {
                return ResourceManager.GetString("SavedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フォーマットを保存して、レポートの生成.
        /// </summary>
        internal static string SaveFormatandGenerateReport {
            get {
                return ResourceManager.GetString("SaveFormatandGenerateReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツのフリー素材の詳細を保存.
        /// </summary>
        internal static string savefreestockdetails {
            get {
                return ResourceManager.GetString("savefreestockdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ヘッダを保存.
        /// </summary>
        internal static string saveheader {
            get {
                return ResourceManager.GetString("saveheader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツの価格詳細を保存.
        /// </summary>
        internal static string savepartprice {
            get {
                return ResourceManager.GetString("savepartprice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to プリフィックスサフィックスを保存.
        /// </summary>
        internal static string SavePrefixSuffix {
            get {
                return ResourceManager.GetString("SavePrefixSuffix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 製品の詳細を保存.
        /// </summary>
        internal static string saveproductdetail {
            get {
                return ResourceManager.GetString("saveproductdetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツ製品タイプを保存.
        /// </summary>
        internal static string saveproducttype {
            get {
                return ResourceManager.GetString("saveproducttype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Roleを.
        /// </summary>
        internal static string SaveRole {
            get {
                return ResourceManager.GetString("SaveRole", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ステップを保存.
        /// </summary>
        internal static string SaveStep {
            get {
                return ResourceManager.GetString("SaveStep", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ステップリンクを名前を付けて保存.
        /// </summary>
        internal static string SaveStepLink {
            get {
                return ResourceManager.GetString("SaveStepLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 正常に保存されました.
        /// </summary>
        internal static string SaveSuccessfull {
            get {
                return ResourceManager.GetString("SaveSuccessfull", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 租税構造を保存.
        /// </summary>
        internal static string savetaxstructure {
            get {
                return ResourceManager.GetString("savetaxstructure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ユーザーを保存.
        /// </summary>
        internal static string SaveUser {
            get {
                return ResourceManager.GetString("SaveUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Schedule Type.
        /// </summary>
        internal static string ScheduleType {
            get {
                return ResourceManager.GetString("ScheduleType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to セカンダリセグメント.
        /// </summary>
        internal static string SecondarySegment {
            get {
                return ResourceManager.GetString("SecondarySegment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to セカンダリセグメントの説明.
        /// </summary>
        internal static string SecondarySegmentdescription {
            get {
                return ResourceManager.GetString("SecondarySegmentdescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to セカンダリセグメント英語.
        /// </summary>
        internal static string secondarysegmentenglish {
            get {
                return ResourceManager.GetString("secondarysegmentenglish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to セカンダリセグメントのロケール.
        /// </summary>
        internal static string secondarysegmentlocale {
            get {
                return ResourceManager.GetString("secondarysegmentlocale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to セグメント詳細.
        /// </summary>
        internal static string SegmentDetail {
            get {
                return ResourceManager.GetString("SegmentDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 選択する.
        /// </summary>
        internal static string select {
            get {
                return ResourceManager.GetString("select", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [すべて]を選択します.
        /// </summary>
        internal static string SelectAll {
            get {
                return ResourceManager.GetString("SelectAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ブランドを選択してください.
        /// </summary>
        internal static string selectbrand {
            get {
                return ResourceManager.GetString("selectbrand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フローステップを働かせる.
        /// </summary>
        internal static string SelectColumn {
            get {
                return ResourceManager.GetString("SelectColumn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja select company.
        /// </summary>
        internal static string selectcompany {
            get {
                return ResourceManager.GetString("selectcompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フローステップを働かせる.
        /// </summary>
        internal static string SelectCondition {
            get {
                return ResourceManager.GetString("SelectCondition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ---------選択---------.
        /// </summary>
        internal static string SelectDDl {
            get {
                return ResourceManager.GetString("SelectDDl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 選択したファイルはエクセルファイルではありません.
        /// </summary>
        internal static string SelectedFileisnotanExcelFile {
            get {
                return ResourceManager.GetString("SelectedFileisnotanExcelFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 選択基準.
        /// </summary>
        internal static string SelectionCriteria {
            get {
                return ResourceManager.GetString("SelectionCriteria", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to モデルを選択.
        /// </summary>
        internal static string SelectModel {
            get {
                return ResourceManager.GetString("SelectModel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to モデルを選択し、要求.
        /// </summary>
        internal static string SelectModelandRequest {
            get {
                return ResourceManager.GetString("SelectModelandRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フローステップを働かせる.
        /// </summary>
        internal static string SelectOperator {
            get {
                return ResourceManager.GetString("SelectOperator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーティーの種類を選択.
        /// </summary>
        internal static string SelectPartyType {
            get {
                return ResourceManager.GetString("SelectPartyType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 製品タイプを選択します.
        /// </summary>
        internal static string selectproducttype {
            get {
                return ResourceManager.GetString("selectproducttype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 削除するレコードを選択します.
        /// </summary>
        internal static string SelectRecordstoDelete {
            get {
                return ResourceManager.GetString("SelectRecordstoDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 以前に保存されたフォーマットから[レポート]を選択します.
        /// </summary>
        internal static string SelectReportFromPreviouslyStoredFormats {
            get {
                return ResourceManager.GetString("SelectReportFromPreviouslyStoredFormats", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 要求番号を選択.
        /// </summary>
        internal static string SelectReqNumber {
            get {
                return ResourceManager.GetString("SelectReqNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービスリクエストを選択.
        /// </summary>
        internal static string SelectServiceRequest {
            get {
                return ResourceManager.GetString("SelectServiceRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja select shift.
        /// </summary>
        internal static string selectshift {
            get {
                return ResourceManager.GetString("selectshift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to テーブル名を選択してください.
        /// </summary>
        internal static string SelectTableName {
            get {
                return ResourceManager.GetString("SelectTableName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 9月.
        /// </summary>
        internal static string September {
            get {
                return ResourceManager.GetString("September", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to シーケンスなし.
        /// </summary>
        internal static string sequenceno {
            get {
                return ResourceManager.GetString("sequenceno", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to シリアル.
        /// </summary>
        internal static string Serial {
            get {
                return ResourceManager.GetString("Serial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to シリアルナンバー.
        /// </summary>
        internal static string serialnumber {
            get {
                return ResourceManager.GetString("serialnumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to シリアル番号は、このモデルのために既に存在している.
        /// </summary>
        internal static string serialnumberalreadyexistsforthismodel {
            get {
                return ResourceManager.GetString("serialnumberalreadyexistsforthismodel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to シリアル番号のフィールド検索.
        /// </summary>
        internal static string SerialNumberFieldSearch {
            get {
                return ResourceManager.GetString("SerialNumberFieldSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to シリアル番号は、選択されたモデルが見つかりません.
        /// </summary>
        internal static string SerialNumbernotfoundfortheselectedmodel {
            get {
                return ResourceManager.GetString("SerialNumbernotfoundfortheselectedmodel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Service.
        /// </summary>
        internal static string Service {
            get {
                return ResourceManager.GetString("Service", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービスチャージコード.
        /// </summary>
        internal static string ServiceChargeCode {
            get {
                return ResourceManager.GetString("ServiceChargeCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービス料コードが見つかりません.
        /// </summary>
        internal static string servicechargecodenotfound {
            get {
                return ResourceManager.GetString("servicechargecodenotfound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービス料の詳細.
        /// </summary>
        internal static string ServiceChargeDetail {
            get {
                return ResourceManager.GetString("ServiceChargeDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービス料フィールドの検索.
        /// </summary>
        internal static string ServiceChargeFieldSearch {
            get {
                return ResourceManager.GetString("ServiceChargeFieldSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービス料金.
        /// </summary>
        internal static string servicecharges {
            get {
                return ResourceManager.GetString("servicecharges", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービス料金の詳細.
        /// </summary>
        internal static string servicechargesdetail {
            get {
                return ResourceManager.GetString("servicechargesdetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービス料金の詳細.
        /// </summary>
        internal static string ServiceChargesDetails {
            get {
                return ResourceManager.GetString("ServiceChargesDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 英語サービス料金.
        /// </summary>
        internal static string servicechargesenglish {
            get {
                return ResourceManager.GetString("servicechargesenglish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービス料ヘッダー.
        /// </summary>
        internal static string servicechargesheader {
            get {
                return ResourceManager.GetString("servicechargesheader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービス料金のロケール.
        /// </summary>
        internal static string servicechargeslocale {
            get {
                return ResourceManager.GetString("servicechargeslocale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービス料マスター.
        /// </summary>
        internal static string servicechargesmaster {
            get {
                return ResourceManager.GetString("servicechargesmaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービスは、合計金額を充電.
        /// </summary>
        internal static string ServiceChargesTotalAmount {
            get {
                return ResourceManager.GetString("ServiceChargesTotalAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Code.
        /// </summary>
        internal static string ServiceCode {
            get {
                return ResourceManager.GetString("ServiceCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービスコードは既に存在しています.
        /// </summary>
        internal static string servicecodealreadyexists {
            get {
                return ResourceManager.GetString("servicecodealreadyexists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービス日.
        /// </summary>
        internal static string servicedate {
            get {
                return ResourceManager.GetString("servicedate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービスは、現在の日付より小さくすることはできません.
        /// </summary>
        internal static string servicedatecannotbelessthancurrentdate {
            get {
                return ResourceManager.GetString("servicedatecannotbelessthancurrentdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービスの詳細.
        /// </summary>
        internal static string ServiceDetails {
            get {
                return ResourceManager.GetString("ServiceDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービス履歴.
        /// </summary>
        internal static string servicehistory {
            get {
                return ResourceManager.GetString("servicehistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービス&amp;#183;プライオリティ.
        /// </summary>
        internal static string ServicePriority {
            get {
                return ResourceManager.GetString("ServicePriority", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 引用数.
        /// </summary>
        internal static string ServiceQuotationNumber {
            get {
                return ResourceManager.GetString("ServiceQuotationNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービス要求.
        /// </summary>
        internal static string ServiceRequest {
            get {
                return ResourceManager.GetString("ServiceRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービス&amp;#183;リクエストは、放棄された.
        /// </summary>
        internal static string ServiceRequestAbandoned {
            get {
                return ResourceManager.GetString("ServiceRequestAbandoned", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービス&amp;#183;リクエスト&amp;#183;カウント.
        /// </summary>
        internal static string ServiceRequestCount {
            get {
                return ResourceManager.GetString("ServiceRequestCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービス依頼日.
        /// </summary>
        internal static string ServiceRequestDate {
            get {
                return ResourceManager.GetString("ServiceRequestDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービスリクエストの分布図.
        /// </summary>
        internal static string ServiceRequestDistributionChart {
            get {
                return ResourceManager.GetString("ServiceRequestDistributionChart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービス&amp;#183;リクエスト&amp;#183;フィールドの検索.
        /// </summary>
        internal static string ServicerequestFieldSearch {
            get {
                return ResourceManager.GetString("ServicerequestFieldSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービス要求番号.
        /// </summary>
        internal static string ServiceRequestNumber {
            get {
                return ResourceManager.GetString("ServiceRequestNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービスリクエスト番号が見つかりません.
        /// </summary>
        internal static string ServiceRequestNumbernotfound {
            get {
                return ResourceManager.GetString("ServiceRequestNumbernotfound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービスリクエストの概要.
        /// </summary>
        internal static string ServiceRequestSummary {
            get {
                return ResourceManager.GetString("ServiceRequestSummary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービスSchdule.
        /// </summary>
        internal static string serviceschdule {
            get {
                return ResourceManager.GetString("serviceschdule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービススケジュール.
        /// </summary>
        internal static string ServiceSchedule {
            get {
                return ResourceManager.GetString("ServiceSchedule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービスの種類.
        /// </summary>
        internal static string ServiceType {
            get {
                return ResourceManager.GetString("ServiceType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービスタイプ名.
        /// </summary>
        internal static string ServiceTypeName {
            get {
                return ResourceManager.GetString("ServiceTypeName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ja Shift.
        /// </summary>
        internal static string Shift {
            get {
                return ResourceManager.GetString("Shift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 省略名.
        /// </summary>
        internal static string ShortName {
            get {
                return ResourceManager.GetString("ShortName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サイトアドレス.
        /// </summary>
        internal static string siteaddress {
            get {
                return ResourceManager.GetString("siteaddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サイトのアドレスの詳細.
        /// </summary>
        internal static string siteaddressdetails {
            get {
                return ResourceManager.GetString("siteaddressdetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 16&amp;#12316;24時間.
        /// </summary>
        internal static string SixteenToTwentyFourHours {
            get {
                return ResourceManager.GetString("SixteenToTwentyFourHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to スキル.
        /// </summary>
        internal static string skill {
            get {
                return ResourceManager.GetString("skill", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to スキルはすでに従業員に関連付けられている.
        /// </summary>
        internal static string Skillisalreadyassociatedwiththeemployee {
            get {
                return ResourceManager.GetString("Skillisalreadyassociatedwiththeemployee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 能力水準.
        /// </summary>
        internal static string skilllevel {
            get {
                return ResourceManager.GetString("skilllevel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to スキルレベルが10から1の間でなければなりません.
        /// </summary>
        internal static string skilllevelshouldbebetween1to10 {
            get {
                return ResourceManager.GetString("skilllevelshouldbebetween1to10", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to スキルセット.
        /// </summary>
        internal static string Skillset {
            get {
                return ResourceManager.GetString("Skillset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SLなし.
        /// </summary>
        internal static string slno {
            get {
                return ResourceManager.GetString("slno", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to お届け先へのSMS.
        /// </summary>
        internal static string SMSToAddressee {
            get {
                return ResourceManager.GetString("SMSToAddressee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 顧客へのSMS.
        /// </summary>
        internal static string SMSToCustomer {
            get {
                return ResourceManager.GetString("SMSToCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SMTPメールボックス.
        /// </summary>
        internal static string SMTPMailBox {
            get {
                return ResourceManager.GetString("SMTPMailBox", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SMTPパスワード.
        /// </summary>
        internal static string SMTPPassword {
            get {
                return ResourceManager.GetString("SMTPPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SMTPサーバー名.
        /// </summary>
        internal static string SMTPServerName {
            get {
                return ResourceManager.GetString("SMTPServerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SMTPユーザー名.
        /// </summary>
        internal static string SMTPUserName {
            get {
                return ResourceManager.GetString("SMTPUserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Snの.
        /// </summary>
        internal static string Sn {
            get {
                return ResourceManager.GetString("Sn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ソート順序.
        /// </summary>
        internal static string SortOrder {
            get {
                return ResourceManager.GetString("SortOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ソート順は空白にすることはできません.
        /// </summary>
        internal static string SortOrdercannotbeblank {
            get {
                return ResourceManager.GetString("SortOrdercannotbeblank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ソート順序は、メニューの空白にすることはできません.
        /// </summary>
        internal static string sortordercannotbeblankforMenu {
            get {
                return ResourceManager.GetString("sortordercannotbeblankforMenu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ソート順は、255を超えることはできません.
        /// </summary>
        internal static string SortOrderCannotbegreaterthan {
            get {
                return ResourceManager.GetString("SortOrderCannotbegreaterthan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ソース列.
        /// </summary>
        internal static string SourceColumns {
            get {
                return ResourceManager.GetString("SourceColumns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 分業.
        /// </summary>
        internal static string Specialization {
            get {
                return ResourceManager.GetString("Specialization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Specializationのマスター.
        /// </summary>
        internal static string SpecializationMaster {
            get {
                return ResourceManager.GetString("SpecializationMaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to データベースエラーが発生しました.
        /// </summary>
        internal static string SqlException {
            get {
                return ResourceManager.GetString("SqlException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to JA Service Request Based on Call type.
        /// </summary>
        internal static string SRBasedonCalltype {
            get {
                return ResourceManager.GetString("SRBasedonCalltype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービスリクエストカウント.
        /// </summary>
        internal static string SRCount {
            get {
                return ResourceManager.GetString("SRCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サービス要求が見つかりません.
        /// </summary>
        internal static string SRNotFound {
            get {
                return ResourceManager.GetString("SRNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 標準時間.
        /// </summary>
        internal static string StandardHours {
            get {
                return ResourceManager.GetString("StandardHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 標準時.
        /// </summary>
        internal static string standardtime {
            get {
                return ResourceManager.GetString("standardtime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 番号を開始.
        /// </summary>
        internal static string startnumber {
            get {
                return ResourceManager.GetString("startnumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to スタート番号がNULLまたはゼロにすることはできません.
        /// </summary>
        internal static string startnumbercannotbenullorzero {
            get {
                return ResourceManager.GetString("startnumbercannotbenullorzero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 状態.
        /// </summary>
        internal static string State {
            get {
                return ResourceManager.GetString("State", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 状態英語.
        /// </summary>
        internal static string stateenglish {
            get {
                return ResourceManager.GetString("stateenglish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 状態ロケール.
        /// </summary>
        internal static string statelocale {
            get {
                return ResourceManager.GetString("statelocale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ステータス.
        /// </summary>
        internal static string status {
            get {
                return ResourceManager.GetString("status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ステップリンク.
        /// </summary>
        internal static string StepLink {
            get {
                return ResourceManager.GetString("StepLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ステップ名.
        /// </summary>
        internal static string StepName {
            get {
                return ResourceManager.GetString("StepName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 手順.
        /// </summary>
        internal static string Steps {
            get {
                return ResourceManager.GetString("Steps", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ステップステータス.
        /// </summary>
        internal static string StepStatus {
            get {
                return ResourceManager.GetString("StepStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ステップタイプ.
        /// </summary>
        internal static string StepType {
            get {
                return ResourceManager.GetString("StepType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 成功.
        /// </summary>
        internal static string Success {
            get {
                return ResourceManager.GetString("Success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to サフィックス.
        /// </summary>
        internal static string suffix {
            get {
                return ResourceManager.GetString("suffix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 接尾辞は、すでに選択.
        /// </summary>
        internal static string SuffixalreadySelected {
            get {
                return ResourceManager.GetString("SuffixalreadySelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 要約.
        /// </summary>
        internal static string Summary {
            get {
                return ResourceManager.GetString("Summary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 雑詳細.
        /// </summary>
        internal static string SundryDetail {
            get {
                return ResourceManager.GetString("SundryDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 雑詳細.
        /// </summary>
        internal static string SundryDetails {
            get {
                return ResourceManager.GetString("SundryDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 諸口仕事内容.
        /// </summary>
        internal static string SundryJobDescription {
            get {
                return ResourceManager.GetString("SundryJobDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 雑貨合計金額.
        /// </summary>
        internal static string SundryTotalAmount {
            get {
                return ResourceManager.GetString("SundryTotalAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to テーブル名.
        /// </summary>
        internal static string TableName {
            get {
                return ResourceManager.GetString("TableName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 税.
        /// </summary>
        internal static string Tax {
            get {
                return ResourceManager.GetString("Tax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 課税.
        /// </summary>
        internal static string Taxable {
            get {
                return ResourceManager.GetString("Taxable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 課税対象となるその他の費用1.
        /// </summary>
        internal static string Taxableothercharges1 {
            get {
                return ResourceManager.GetString("Taxableothercharges1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 課税その他費用1金額.
        /// </summary>
        internal static string Taxableothercharges1Amount {
            get {
                return ResourceManager.GetString("Taxableothercharges1Amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 課税対象となるその他の費用2.
        /// </summary>
        internal static string Taxableothercharges2 {
            get {
                return ResourceManager.GetString("Taxableothercharges2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 課税その他費用2量.
        /// </summary>
        internal static string Taxableothercharges2Amount {
            get {
                return ResourceManager.GetString("Taxableothercharges2Amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aamount税.
        /// </summary>
        internal static string Taxamount {
            get {
                return ResourceManager.GetString("Taxamount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 税法.
        /// </summary>
        internal static string TaxCode {
            get {
                return ResourceManager.GetString("TaxCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 税コード名.
        /// </summary>
        internal static string TaxCodeName {
            get {
                return ResourceManager.GetString("TaxCodeName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Detail.
        /// </summary>
        internal static string TaxDetail {
            get {
                return ResourceManager.GetString("TaxDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 税の詳細.
        /// </summary>
        internal static string TaxDetails {
            get {
                return ResourceManager.GetString("TaxDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 税務名は既に存在しています.
        /// </summary>
        internal static string Taxnamealreadyexists {
            get {
                return ResourceManager.GetString("Taxnamealreadyexists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 税の割合.
        /// </summary>
        internal static string taxpercentage {
            get {
                return ResourceManager.GetString("taxpercentage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 租税構造.
        /// </summary>
        internal static string TaxStructure {
            get {
                return ResourceManager.GetString("TaxStructure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 租税構造の詳細.
        /// </summary>
        internal static string taxstructuredetail {
            get {
                return ResourceManager.GetString("taxstructuredetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 租税構造の詳細.
        /// </summary>
        internal static string taxstructuredetails {
            get {
                return ResourceManager.GetString("taxstructuredetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 税構造英語.
        /// </summary>
        internal static string taxstructureenglish {
            get {
                return ResourceManager.GetString("taxstructureenglish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 租税構造ヘッダー.
        /// </summary>
        internal static string taxstructureheader {
            get {
                return ResourceManager.GetString("taxstructureheader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 租税構造のロケール.
        /// </summary>
        internal static string taxstructurelocale {
            get {
                return ResourceManager.GetString("taxstructurelocale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 税構造名.
        /// </summary>
        internal static string taxstructurename {
            get {
                return ResourceManager.GetString("taxstructurename", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 税の種類.
        /// </summary>
        internal static string taxtype {
            get {
                return ResourceManager.GetString("taxtype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 税タイプがすでに選択.
        /// </summary>
        internal static string taxtypealreadyselected {
            get {
                return ResourceManager.GetString("taxtypealreadyselected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 税タイプがすでに選択.
        /// </summary>
        internal static string taxtypealrearyselected {
            get {
                return ResourceManager.GetString("taxtypealrearyselected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 税の種類は次の式で参照されて削除することはできません.
        /// </summary>
        internal static string taxtypeisreferencedinformulacannotdelete {
            get {
                return ResourceManager.GetString("taxtypeisreferencedinformulacannotdelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to はい.
        /// </summary>
        internal static string Team {
            get {
                return ResourceManager.GetString("Team", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 条件.
        /// </summary>
        internal static string Terms {
            get {
                return ResourceManager.GetString("Terms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 利用規約.
        /// </summary>
        internal static string TermsAndConditions {
            get {
                return ResourceManager.GetString("TermsAndConditions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 条件.
        /// </summary>
        internal static string TermsConditions {
            get {
                return ResourceManager.GetString("TermsConditions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ブランドは既に選択されている.
        /// </summary>
        internal static string Thebrandhasalreadybeenselected {
            get {
                return ResourceManager.GetString("Thebrandhasalreadybeenselected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 会社/販売代理店は、すでに関連付けられている.
        /// </summary>
        internal static string TheCompanyDealerhasalreadybeenassociated {
            get {
                return ResourceManager.GetString("TheCompanyDealerhasalreadybeenassociated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to セカンダリセグメントがすでに存在します.
        /// </summary>
        internal static string thesecondarysegmentalreadyexists {
            get {
                return ResourceManager.GetString("thesecondarysegmentalreadyexists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 状態はすでに存在しています.
        /// </summary>
        internal static string thestatealreadyexists {
            get {
                return ResourceManager.GetString("thestatealreadyexists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 租税構造が既に選択されています.
        /// </summary>
        internal static string ThetaxStructurehasalreadybeenselected {
            get {
                return ResourceManager.GetString("ThetaxStructurehasalreadybeenselected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 36&amp;#12316;48時間.
        /// </summary>
        internal static string ThirtySixToFourtyEightHours {
            get {
                return ResourceManager.GetString("ThirtySixToFourtyEightHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to このログインIDは既に存在している.
        /// </summary>
        internal static string ThisLoginIDisalreadyexists {
            get {
                return ResourceManager.GetString("ThisLoginIDisalreadyexists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to このモジュールは、すでに存在している.
        /// </summary>
        internal static string ThisModuleisalreadyexists {
            get {
                return ResourceManager.GetString("ThisModuleisalreadyexists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to この役割は、すでに存在している.
        /// </summary>
        internal static string ThisRoleisalreadyexists {
            get {
                return ResourceManager.GetString("ThisRoleisalreadyexists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to このロールは、ユーザーにすでに選択されている.
        /// </summary>
        internal static string Thisroleisalreadyselectedfortheuser {
            get {
                return ResourceManager.GetString("Thisroleisalreadyselectedfortheuser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to このシリアル番号は、既に顧客に関連付けられている.
        /// </summary>
        internal static string ThisSerialNumberisalreadyassociatedwiththecustomer {
            get {
                return ResourceManager.GetString("ThisSerialNumberisalreadyassociatedwiththecustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Thursday `.
        /// </summary>
        internal static string Thursday {
            get {
                return ResourceManager.GetString("Thursday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to へ.
        /// </summary>
        internal static string To {
            get {
                return ResourceManager.GetString("To", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 日.
        /// </summary>
        internal static string todate {
            get {
                return ResourceManager.GetString("todate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 日付を現在の日付より大きくすることはできません.
        /// </summary>
        internal static string ToDatecannotbegreaterthanCurrentDate {
            get {
                return ResourceManager.GetString("ToDatecannotbegreaterthanCurrentDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 日付現在の日付より小さくすることはできません.
        /// </summary>
        internal static string ToDatecannotbelessthanCurrentDate {
            get {
                return ResourceManager.GetString("ToDatecannotbelessthanCurrentDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 日付に日付から少ないし、することはできません.
        /// </summary>
        internal static string todatecannotbelessthenfromdatedate {
            get {
                return ResourceManager.GetString("todatecannotbelessthenfromdatedate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 日付に日付からより大きいか等しくなければなりません.
        /// </summary>
        internal static string todatemustbegreaterthanorequaltofromdate {
            get {
                return ResourceManager.GetString("todatemustbegreaterthanorequaltofromdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 郵便番号.
        /// </summary>
        internal static string TopModel {
            get {
                return ResourceManager.GetString("TopModel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Top 10 Models with Maximum Service Request.
        /// </summary>
        internal static string TopModelwithMaximumSR {
            get {
                return ResourceManager.GetString("TopModelwithMaximumSR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ステップに.
        /// </summary>
        internal static string ToStep {
            get {
                return ResourceManager.GetString("ToStep", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 合計.
        /// </summary>
        internal static string Total {
            get {
                return ResourceManager.GetString("Total", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 割り当てられた総時間.
        /// </summary>
        internal static string TotalAllocatedHours {
            get {
                return ResourceManager.GetString("TotalAllocatedHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 総額.
        /// </summary>
        internal static string TotalAmount {
            get {
                return ResourceManager.GetString("TotalAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 合計オン.
        /// </summary>
        internal static string TotalOn {
            get {
                return ResourceManager.GetString("TotalOn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 合計見積金額.
        /// </summary>
        internal static string TotalQuotationAmount {
            get {
                return ResourceManager.GetString("TotalQuotationAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 合計課税対象額.
        /// </summary>
        internal static string TotalTaxableAmount {
            get {
                return ResourceManager.GetString("TotalTaxableAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 合計課税額は空白です.
        /// </summary>
        internal static string TotalTaxableAmountBlank {
            get {
                return ResourceManager.GetString("TotalTaxableAmountBlank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 総労働時間.
        /// </summary>
        internal static string TotalWorkingHours {
            get {
                return ResourceManager.GetString("TotalWorkingHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to トランザクションが既にロックされている.
        /// </summary>
        internal static string TransactionisalreadybeenLocked {
            get {
                return ResourceManager.GetString("TransactionisalreadybeenLocked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to トランザクションのロックは既に解除されています.
        /// </summary>
        internal static string TransactionisalreadybeenUnLocked {
            get {
                return ResourceManager.GetString("TransactionisalreadybeenUnLocked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to トランザクションが正常にロックされました.
        /// </summary>
        internal static string TransactionLockedSuccessfully {
            get {
                return ResourceManager.GetString("TransactionLockedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to トランザクションが正常にロック解除.
        /// </summary>
        internal static string TransactionUnLockedSuccessfully {
            get {
                return ResourceManager.GetString("TransactionUnLockedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Tuesday.
        /// </summary>
        internal static string Tuesday {
            get {
                return ResourceManager.GetString("Tuesday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 24&amp;#12316;36時間.
        /// </summary>
        internal static string TwentyFourToThirtySixHours {
            get {
                return ResourceManager.GetString("TwentyFourToThirtySixHours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to タイプ.
        /// </summary>
        internal static string Type {
            get {
                return ResourceManager.GetString("Type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 一意の識別子はすでに存在しています.
        /// </summary>
        internal static string uniqueidentifieralreadyexists {
            get {
                return ResourceManager.GetString("uniqueidentifieralreadyexists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 測定単位.
        /// </summary>
        internal static string unitofmeasurement {
            get {
                return ResourceManager.GetString("unitofmeasurement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to はい.
        /// </summary>
        internal static string UnLock {
            get {
                return ResourceManager.GetString("UnLock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Verification Queue.
        /// </summary>
        internal static string UnRegisteredServiceRequest {
            get {
                return ResourceManager.GetString("UnRegisteredServiceRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to タイプ.
        /// </summary>
        internal static string uom {
            get {
                return ResourceManager.GetString("uom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ファイルをアップロード.
        /// </summary>
        internal static string UploadFile {
            get {
                return ResourceManager.GetString("UploadFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to パーツをアップロード.
        /// </summary>
        internal static string UploadParts {
            get {
                return ResourceManager.GetString("UploadParts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 使用環境.
        /// </summary>
        internal static string usageenvironment {
            get {
                return ResourceManager.GetString("usageenvironment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ユーザー.
        /// </summary>
        internal static string User {
            get {
                return ResourceManager.GetString("User", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ユーザーデータの保存に成功しました.
        /// </summary>
        internal static string UserDataSavedSuccessfully {
            get {
                return ResourceManager.GetString("UserDataSavedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ユーザーの詳細.
        /// </summary>
        internal static string UserDetail {
            get {
                return ResourceManager.GetString("UserDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ユーザーの詳細.
        /// </summary>
        internal static string UserDetails {
            get {
                return ResourceManager.GetString("UserDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja User dont have access to Party Master.
        /// </summary>
        internal static string Userdonthaveaccesstopartymaster {
            get {
                return ResourceManager.GetString("Userdonthaveaccesstopartymaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User dont have access to Product Master.
        /// </summary>
        internal static string Userdonthaveaccesstoproductmaster {
            get {
                return ResourceManager.GetString("Userdonthaveaccesstoproductmaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja User dont have Edit Access.
        /// </summary>
        internal static string Userdonthaveeditaccess {
            get {
                return ResourceManager.GetString("Userdonthaveeditaccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User dont have Edit access to Product Master.
        /// </summary>
        internal static string Userdonthaveeditaccesstoproductmaster {
            get {
                return ResourceManager.GetString("Userdonthaveeditaccesstoproductmaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ja User is locked.
        /// </summary>
        internal static string Userislocked {
            get {
                return ResourceManager.GetString("Userislocked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ja User is not active.
        /// </summary>
        internal static string Userisnotactive {
            get {
                return ResourceManager.GetString("Userisnotactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ユーザー名.
        /// </summary>
        internal static string UserName {
            get {
                return ResourceManager.GetString("UserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to あなたが入力したユーザー名またはパスワードが正しくありません。.
        /// </summary>
        internal static string UserNameOrPasswordYouEnteredIsIncorrectPleaseTryAgain {
            get {
                return ResourceManager.GetString("UserNameOrPasswordYouEnteredIsIncorrectPleaseTryAgain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ユーザロールの詳細.
        /// </summary>
        internal static string UserRoleDetails {
            get {
                return ResourceManager.GetString("UserRoleDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ユーザーの役割.
        /// </summary>
        internal static string UserRoles {
            get {
                return ResourceManager.GetString("UserRoles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ユーザータイプ.
        /// </summary>
        internal static string UserType {
            get {
                return ResourceManager.GetString("UserType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 率％.
        /// </summary>
        internal static string UtilizationPercentage {
            get {
                return ResourceManager.GetString("UtilizationPercentage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 値.
        /// </summary>
        internal static string Value {
            get {
                return ResourceManager.GetString("Value", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フローステップを働かせる.
        /// </summary>
        internal static string ValueisMandatoryforselectedColumn {
            get {
                return ResourceManager.GetString("ValueisMandatoryforselectedColumn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to バージョン.
        /// </summary>
        internal static string Ver {
            get {
                return ResourceManager.GetString("Ver", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 検証キュー.
        /// </summary>
        internal static string VerificationQueue {
            get {
                return ResourceManager.GetString("VerificationQueue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to バージョン.
        /// </summary>
        internal static string Version {
            get {
                return ResourceManager.GetString("Version", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Version Date.
        /// </summary>
        internal static string VersionDate {
            get {
                return ResourceManager.GetString("VersionDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Version Number.
        /// </summary>
        internal static string VersionNumber {
            get {
                return ResourceManager.GetString("VersionNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Version Number and Date.
        /// </summary>
        internal static string VersionNumberandDate {
            get {
                return ResourceManager.GetString("VersionNumberandDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 表示.
        /// </summary>
        internal static string view {
            get {
                return ResourceManager.GetString("view", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ジョブ&amp;#183;カードを見る.
        /// </summary>
        internal static string ViewJobCard {
            get {
                return ResourceManager.GetString("ViewJobCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ビューパーツマスター.
        /// </summary>
        internal static string ViewPartsMaster {
            get {
                return ResourceManager.GetString("ViewPartsMaster", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 保証日.
        /// </summary>
        internal static string WarrantyDate {
            get {
                return ResourceManager.GetString("WarrantyDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 保証詳細.
        /// </summary>
        internal static string warrantydetails {
            get {
                return ResourceManager.GetString("warrantydetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ウェブサイト.
        /// </summary>
        internal static string Website {
            get {
                return ResourceManager.GetString("Website", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Wednesday.
        /// </summary>
        internal static string Wednesday {
            get {
                return ResourceManager.GetString("Wednesday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ようこそ：.
        /// </summary>
        internal static string Welcome {
            get {
                return ResourceManager.GetString("Welcome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to はい.
        /// </summary>
        internal static string Where {
            get {
                return ResourceManager.GetString("Where", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 作業の流れ.
        /// </summary>
        internal static string WorkFlow {
            get {
                return ResourceManager.GetString("WorkFlow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フローIDを働かせる.
        /// </summary>
        internal static string WorkFlowID {
            get {
                return ResourceManager.GetString("WorkFlowID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 作業フロー名.
        /// </summary>
        internal static string WorkFlowName {
            get {
                return ResourceManager.GetString("WorkFlowName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to フローステップを働かせる.
        /// </summary>
        internal static string WorkFlowSteps {
            get {
                return ResourceManager.GetString("WorkFlowSteps", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Working Days.
        /// </summary>
        internal static string WorkingDays {
            get {
                return ResourceManager.GetString("WorkingDays", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Working Time.
        /// </summary>
        internal static string WorkingTime {
            get {
                return ResourceManager.GetString("WorkingTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 年.
        /// </summary>
        internal static string Year {
            get {
                return ResourceManager.GetString("Year", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja Year should be between 2000 and 2999.
        /// </summary>
        internal static string Yearshouldbebetween2000and2999 {
            get {
                return ResourceManager.GetString("Yearshouldbebetween2000and2999", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to はい.
        /// </summary>
        internal static string yes {
            get {
                return ResourceManager.GetString("yes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja You do not have edit permission.
        /// </summary>
        internal static string Youdonothaveeditpermission {
            get {
                return ResourceManager.GetString("Youdonothaveeditpermission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ja You have been Logged out successfully.
        /// </summary>
        internal static string YouhavebeenLoggedoutsuccessfully {
            get {
                return ResourceManager.GetString("YouhavebeenLoggedoutsuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 郵便番号.
        /// </summary>
        internal static string ZipCode {
            get {
                return ResourceManager.GetString("ZipCode", resourceCulture);
            }
        }
    }
}
