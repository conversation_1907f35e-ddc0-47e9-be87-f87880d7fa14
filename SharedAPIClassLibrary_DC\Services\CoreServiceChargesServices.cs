﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using WorkFlow.Models;
//using LS = SharedAPIClassLibrary_AMERP.Utilities;
using LS = LogSheetExporter;
namespace SharedAPIClassLibrary_AMERP
{
    public class CoreServiceChargesServices
    {

        #region ::: SelectServiceCharges Uday Kumar J B 16-07-2024:::
        /// <summary>
        /// Method to Load English Grid 
        /// </summary> 
        /// 

        public static IActionResult SelectServiceCharges(string connString, SelectServiceChargesList SelectServiceChargesobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int count = 0;
            int total = 0;
            string AppPath = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                //var userDetail = (GNM_User)SelectServiceChargesobj.UserDetails.FirstOrDefault();
                List<GNM_ServiceCharges> serviceChargesLists = new List<GNM_ServiceCharges>();

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // FilterServiceChargeBasedonCompany
                    bool FilterServiceChargeBasedonCompany = true;
                    using (SqlCommand command = new SqlCommand("GetFilterServiceChargeBasedonCompany", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@CompanyID", SelectServiceChargesobj.Company_ID);
                        FilterServiceChargeBasedonCompany = command.ExecuteScalar().ToString().ToUpper() == "TRUE";
                    }

                    using (SqlCommand command = new SqlCommand("sp_GetServiceCharges", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@UserId", SelectServiceChargesobj.User_ID);
                        command.Parameters.AddWithValue("@CompanyId", SelectServiceChargesobj.Company_ID);
                        command.Parameters.AddWithValue("@FilterServiceChargeBasedonCompany", FilterServiceChargeBasedonCompany);
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                serviceChargesLists.Add(MapToServiceCharge(reader));
                            }
                        }
                    }

                    SelectServiceChargesobj.ServiceCharge = serviceChargesLists;
                    var serviceChargesList = serviceChargesLists.AsQueryable().OrderByField(sidx, sord);
                    if (_search)
                    {
                        // Parse and decrypt the filters
                        Filters filtersObj = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(filters))).ToObject<Filters>();

                        // Ensure filtersObj is not null and its filter collection has more than 0 items
                        if (filtersObj != null && filtersObj.rules.Count > 0)
                        {
                            // Perform the FilterSearch operation
                            serviceChargesList = serviceChargesList.FilterSearch(filtersObj);
                        }
                    }
                    else if (advnce && !string.IsNullOrEmpty(advnceFilters))
                    {
                        AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<AdvanceFilter>();
                        serviceChargesList = serviceChargesList.AdvanceSearch(advnfilter);
                        page = 1;
                    }
                    count = serviceChargesList.Count();
                    total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                    if (count < (rows * page) && count != 0)
                    {
                        page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                    }

                    string Title = CommonFunctionalities.GetGlobalResourceObject(SelectServiceChargesobj.UserCulture.ToString(), "view").ToString();
                    var arrServiceCharge = serviceChargesList
                        .Select(a => new
                        {
                            edit = "<a title=" + Title + " href='#' id='" + a.ServiceCharge_ID + "' key='" + a.ServiceCharge_ID + "' class='EditServiceCharges font-icon-class'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                            delete = "<input type='checkbox' key='" + a.ServiceCharge_ID + "' defaultchecked='' id='chk" + a.ServiceCharge_ID + "' class='ServiceChargesDelete'/>",
                            ID = a.ServiceCharge_ID,
                            ServiceCharge_Code = a.ServiceCharge_Code,
                            ServiceCharge_Description = a.ServiceCharge_Description,
                            ServiceCharge_Amount = a.ServiceCharge_Amount.ToString(),
                            ServiceCharge_Amountstring = a.ServiceCharge_Amount.ToString(),
                            ServiceCharge_IsActive = a.ServiceCharge_IsActive ? "Yes" : "No",
                            Locale = "<a key='" + a.ServiceCharge_ID + "' src='" + AppPath + "/Content/local.png' class='ServiceChargesLocale' alt='Localize' width='20' height='20' title='Localize'><i class='fa fa-globe'></i></a>"
                        });


                    var jsonData = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        data = arrServiceCharge.ToList().Paginate(page, rows)
                    };
                    return new JsonResult(jsonData);
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(false);
            }
        }

        private static GNM_ServiceCharges MapToServiceCharge(SqlDataReader reader)
        {
            return new GNM_ServiceCharges
            {
                ServiceCharge_ID = reader.GetInt32(reader.GetOrdinal("ServiceCharge_ID")),
                ServiceCharge_Code = reader.GetString(reader.GetOrdinal("ServiceCharge_Code")),
                ServiceCharge_Description = reader.GetString(reader.GetOrdinal("ServiceCharge_Description")),
                ServiceCharge_Amount = reader.GetDecimal(reader.GetOrdinal("ServiceCharge_Amount")),
                ServiceCharge_IsActive = reader.GetBoolean(reader.GetOrdinal("ServiceCharge_IsActive")),
                Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID"))
            };
        }

        #endregion


        #region ::: Load Grid Locale Core Service Charges  Uday Kumar J B 15-07-2024 :::
        /// <summary>
        /// Method to Load Locale Grid of Core Service Charges
        /// </summary>  
        /// 

        public static IActionResult LoadGridLocale(string connString, LoadGridLocaleCoreServiceChargesList LoadGridLocaleCoreServiceChargesobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int count = 0;
            int total = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                //GNM_User User = LoadGridLocaleCoreServiceChargesobj.UserDetails.FirstOrDefault();
                List<object> arrServiceCharge = new List<object>();

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // FilterServiceChargeBasedonCompany
                    bool FilterServiceChargeBasedonCompany = true;
                    using (SqlCommand command = new SqlCommand("GetFilterServiceChargeBasedonCompany", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@CompanyID", LoadGridLocaleCoreServiceChargesobj.Company_ID);
                        FilterServiceChargeBasedonCompany = command.ExecuteScalar().ToString().ToUpper() == "TRUE";
                    }

                    using (SqlCommand command = new SqlCommand("sp_LoadGridLocale", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@UserId", LoadGridLocaleCoreServiceChargesobj.User_ID);
                        command.Parameters.AddWithValue("@CompanyId", LoadGridLocaleCoreServiceChargesobj.Company_ID);
                        command.Parameters.AddWithValue("@LanguageId", LoadGridLocaleCoreServiceChargesobj.Language_ID);
                        command.Parameters.AddWithValue("@FilterServiceChargeBasedonCompany", FilterServiceChargeBasedonCompany ? 1 : 0);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var viewIcon = reader.GetString(reader.GetOrdinal("ViewIcon"));
                                var serviceChargeId = reader.GetInt32(reader.GetOrdinal("ID"));
                                var serviceChargeCode = reader.GetString(reader.GetOrdinal("ServiceCharge_Code"));
                                var serviceChargeDescription = reader.GetString(reader.GetOrdinal("ServiceCharge_Description"));
                                var serviceChargeAmount = reader.GetString(reader.GetOrdinal("ServiceCharge_Amount"));
                                var serviceChargeIsActive = reader.GetString(reader.GetOrdinal("ServiceCharge_IsActive"));

                                var item = new
                                {
                                    view = $"<img id='{serviceChargeId}' src='{viewIcon}' key='{serviceChargeId}' class='ViewServiceChargesLocale'/>",
                                    ID = serviceChargeId,
                                    ServiceCharge_Code = serviceChargeCode,
                                    ServiceCharge_Description = serviceChargeDescription,
                                    ServiceCharge_Amount = serviceChargeAmount,
                                    ServiceCharge_IsActive = serviceChargeIsActive
                                };

                                arrServiceCharge.Add(item);
                            }
                        }
                    }
                    var arrServiceCharges = arrServiceCharge.AsQueryable();
                    if (_search)
                    {
                        // Parse and decrypt the filters
                        Filters filtersObj = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(filters))).ToObject<Filters>();

                        // Ensure filtersObj is not null and its filter collection has more than 0 items
                        if (filtersObj != null && filtersObj.rules.Count > 0)
                        {
                            // Perform the FilterSearch operation
                            arrServiceCharges = arrServiceCharges.FilterSearch(filtersObj);
                        }
                    }
                    else if (advnce && !string.IsNullOrEmpty(advnceFilters))
                    {
                        AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<AdvanceFilter>();
                        arrServiceCharges = arrServiceCharges.AdvanceSearch(advnfilter);
                        page = 1;
                    }
                    count = arrServiceCharges.Count();
                    total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                    if (count < (rows * page) && count != 0)
                    {
                        page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                    }

                    count = arrServiceCharge.Count();
                    total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                    if (count < rows * page && count != 0)
                    {
                        page = count / rows + (count % rows == 0 ? 0 : 1);
                    }
                }

                var jsonData = new
                {
                    total,
                    page,
                    records = count,
                    data = arrServiceCharge.Skip((page - 1) * rows).Take(rows).ToList()
                };

                return new JsonResult(jsonData);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                   LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(null);
            }
        }
        #endregion


        #region ::: InsertServiceChargeHeader   Uday Kumar J B 15-07-2024 :::
        /// <summary>
        /// Method to Insert Service Charges 
        /// </summary>  
        /// 
        public static IActionResult InsertServiceChargeHeader(string connString, InsertServiceChargeHeaderList InsertServiceChargeHeaderobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var ServiceChargeHeaderData = new { ServiceCharge_ID = 0 }; // Initialize with default value

            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Parse JSON parameters
                    JObject jObj = JObject.Parse(InsertServiceChargeHeaderobj.key);
                    int ServiceChargeID = jObj["ServiceChargeID"].ToObject<int>();
                    string Code = Regex.Replace(Uri.UnescapeDataString(jObj["Code"].ToString()), @"[^\w]", "");
                    string Desc = Regex.Replace(Uri.UnescapeDataString(jObj["Desc"].ToString()), @"[^\w]", "");
                    bool Active = jObj["Active"].ToString() == "checked";
                    decimal Amount = string.IsNullOrEmpty(jObj["Amount"].ToString()) ? 0.00m : Convert.ToDecimal(jObj["Amount"]);

                    // Execute stored procedure
                    using (SqlCommand command = new SqlCommand("sp_InsertOrUpdateServiceCharge", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@ServiceChargeID", ServiceChargeID);
                        command.Parameters.AddWithValue("@Code", Code);
                        command.Parameters.AddWithValue("@Desc", Desc);
                        command.Parameters.AddWithValue("@Active", Active);
                        command.Parameters.AddWithValue("@Amount", Amount);
                        command.Parameters.AddWithValue("@CompanyID", InsertServiceChargeHeaderobj.Company_ID);
                        command.Parameters.AddWithValue("@UserID", InsertServiceChargeHeaderobj.User_ID);

                        // Use an OUTPUT parameter to capture the newly inserted or updated ServiceCharge_ID
                        SqlParameter outputParameter = new SqlParameter("@ServiceCharge_ID_Output", SqlDbType.Int)
                        {
                            Direction = ParameterDirection.Output
                        };
                        command.Parameters.Add(outputParameter);

                        command.ExecuteNonQuery();

                        // Get the output value after execution
                        int insertedID = (int)outputParameter.Value;
                        ServiceChargeHeaderData = new { ServiceCharge_ID = insertedID }; // Correctly initialize anonymous type
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(ServiceChargeHeaderData);
        }
        #endregion


        #region ::: InsertServiceChargeHeaderLocale  Uday Kumar J B 15-07-2024 :::
        /// <summary>
        ///  Method to Insert Locale Service Charges
        /// </summary>  
        /// 


        public static IActionResult InsertServiceChargeHeaderLocale(string connString, InsertServiceChargeHeaderLocaleList InsertServiceChargeHeaderLocaleobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var ServiceChargeHeaderLocale = new { ServiceCharge_ID = 0 };
            try
            {
                JObject jObj = JObject.Parse(InsertServiceChargeHeaderLocaleobj.key);
                int ServiceChargeID = jObj["ServiceChargeID"].ToObject<int>();
                string Desc = jObj["Desc"].ToString();

                // GNM_User User = InsertServiceChargeHeaderLocaleobj.UserDetails.FirstOrDefault();
                int LanguageID = Convert.ToInt32(InsertServiceChargeHeaderLocaleobj.UserLanguageID);

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Execute stored procedure
                    using (SqlCommand command = new SqlCommand("sp_InsertOrUpdateServiceChargeLocale", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@ServiceChargeID", ServiceChargeID);
                        command.Parameters.AddWithValue("@Desc", Desc);
                        command.Parameters.AddWithValue("@LanguageID", LanguageID);
                        SqlParameter outputParameter = new SqlParameter("@ServiceCharge_ID_Output", SqlDbType.Int)
                        {
                            Direction = ParameterDirection.Output
                        };
                        command.Parameters.Add(outputParameter);
                        command.ExecuteNonQuery();

                        int insertedID = (int)outputParameter.Value;
                        ServiceChargeHeaderLocale = new { ServiceCharge_ID = insertedID };
                    }
                }

                // Log insertion/update
                CommonFunctionalities.InsertGPSDetails(Convert.ToInt32(InsertServiceChargeHeaderLocaleobj.Company_ID), Convert.ToInt32(InsertServiceChargeHeaderLocaleobj.Branch), Convert.ToInt32(InsertServiceChargeHeaderLocaleobj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreServiceChargesMaster")), ServiceChargeID, 0, 0, "Update", false, Convert.ToInt32(InsertServiceChargeHeaderLocaleobj.MenuID));
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(ServiceChargeHeaderLocale);
        }
        #endregion


        #region ::: SelectSingleServiceCharge Uday Kumar J B 15-07-2024 :::
        /// <summary>
        /// To select the Single Service Charge
        /// </summary>  
        /// 

        public static IActionResult SelectSingleServiceCharge(string connString, SelectSingleServiceChargeList SelectSingleServiceChargeobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var Service = new
            {
                Service_ID = 0,
                Service_Code = "",
                Service_Desc = "",
                Service_Amount = "",
                Sevice_IsActive = false
            };

            int branchID = Convert.ToInt32(SelectSingleServiceChargeobj.Branch);
            //GNM_User User = SelectSingleServiceChargeobj.UserDetails.FirstOrDefault();

            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Execute stored procedure
                    using (SqlCommand command = new SqlCommand("sp_SelectSingleServiceCharge", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@ServiceChargeID", SelectSingleServiceChargeobj.id);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                Service = new
                                {
                                    Service_ID = Convert.ToInt32(reader["ServiceCharge_ID"]),
                                    Service_Code = reader["ServiceCharge_Code"].ToString(),
                                    Service_Desc = reader["ServiceCharge_Description"].ToString(),
                                    Service_Amount = reader["ServiceCharge_Amount"].ToString(),
                                    Sevice_IsActive = Convert.ToBoolean(reader["ServiceCharge_IsActive"])
                                };

                                // Log view action
                                CommonFunctionalities.InsertGPSDetails(
                                    Convert.ToInt32(SelectSingleServiceChargeobj.Company_ID.ToString()),
                                    branchID,
                                    SelectSingleServiceChargeobj.User_ID,
                                    Common.GetObjectID("CoreServiceChargesMaster"),
                                    SelectSingleServiceChargeobj.id,
                                    0,
                                    0,
                                    "Viewed " + reader["ServiceCharge_Code"].ToString(),
                                    false,
                                    Convert.ToInt32(SelectSingleServiceChargeobj.MenuID),
                                    Convert.ToDateTime(SelectSingleServiceChargeobj.LoggedINDateTime),
                                    null);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            // Wrap the service object in an array to match the JavaScript expectation
            var response = new[] { Service };
            return new JsonResult(Uri.EscapeDataString(Newtonsoft.Json.JsonConvert.SerializeObject(response)));
        }
        #endregion


        #region ::: SelectSingleServiceChargeLocale Uday Kumar J B 16-07-2024 :::
        /// <summary>
        ///  To select the Single Locale Service Charge
        /// </summary> 
        /// 

        public static IActionResult SelectSingleServiceChargeLocale(string connString, SelectSingleServiceChargeLocaleList SelectSingleServiceChargeLocaleobj)
        {
            var Service = new List<object>(); // Adjust the type as per your requirement
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int LangID = Convert.ToInt32(SelectSingleServiceChargeLocaleobj.UserLanguageID);

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Execute stored procedure to retrieve service charge details
                    using (SqlCommand command = new SqlCommand("sp_SelectSingleServiceChargeLocale", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@ServiceChargeID", SelectSingleServiceChargeLocaleobj.id);
                        command.Parameters.AddWithValue("@LangID", LangID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (reader.HasRows)
                            {
                                while (reader.Read())
                                {
                                    var serviceObject = new
                                    {
                                        Service_ID = Convert.ToInt32(reader["ServiceCharge_ID"]),
                                        Service_Code = reader["ServiceCharge_Code"].ToString(),
                                        Service_Desc = reader["ServiceCharge_Description"].ToString(),
                                        Service_Amount = reader["ServiceCharge_Amount"].ToString(),
                                        Sevice_IsActive = Convert.ToBoolean(reader["ServiceCharge_IsActive"]),
                                        ServiceChargeLocale_ID = Convert.ToInt32(reader["ServiceChargeLocale_ID"])
                                    };

                                    Service.Add(serviceObject);
                                }
                            }
                            else
                            {
                                // Handle case where no rows are returned
                                var defaultServiceObject = new
                                {
                                    Service_ID = 0,
                                    Service_Code = "",
                                    Service_Desc = "",
                                    Service_Amount = "0.00",
                                    Sevice_IsActive = false,
                                    ServiceChargeLocale_ID = 0
                                };

                                Service.Add(defaultServiceObject);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(Newtonsoft.Json.JsonConvert.SerializeObject(Service));
        }

        #endregion


        #region ::: SelectServiceChargesDetail  Uday Kumar J B 16-07-2024 :::
        /// <summary>
        /// To Select the Service Charge Details
        /// </summary>   
        /// 

        public static IActionResult SelectServiceChargesDetail(string connString, SelectServiceChargesDetailList SelectServiceChargesDetailobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int count = 0;
            int total = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var ServiceChargesDet = default(dynamic);
            List<GNMServiceChargeDetails> serviceChargeDetailsList = new List<GNMServiceChargeDetails>();
            string Branch = "-1:--Select--; ";
            string Brands = "-1:--Select--; ";
            string Models = "-1:--Select--";
            string ProductType = "-1:--Select--";

            try
            {
                int Company_ID = Convert.ToInt32(SelectServiceChargesDetailobj.Company_ID);

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    using (SqlCommand branchCommand = new SqlCommand("sp_GetBranchesSelectServiceChargesDetail", connection))
                    {
                        branchCommand.CommandType = CommandType.StoredProcedure;
                        branchCommand.Parameters.AddWithValue("@CompanyID", Company_ID);

                        using (SqlDataReader branchReader = branchCommand.ExecuteReader())
                        {
                            while (branchReader.Read())
                            {
                                Branch += branchReader["Branch_ID"].ToString() + ":";
                                Branch += branchReader["Branch_Name"] != DBNull.Value ? branchReader["Branch_Name"].ToString() : string.Empty;
                                Branch += "; ";
                            }
                        }
                    }
                    Branch = Branch.TrimEnd(new char[] { ';', ' ' });


                    // Get Service Charges Detail
                    using (SqlCommand serviceChargesCommand = new SqlCommand("sp_GetServiceChargesDetail", connection))
                    {
                        serviceChargesCommand.CommandType = CommandType.StoredProcedure;
                        serviceChargesCommand.Parameters.AddWithValue("@ServiceChargeID", SelectServiceChargesDetailobj.id);

                        using (SqlDataReader reader = serviceChargesCommand.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                serviceChargeDetailsList.Add(new GNMServiceChargeDetails
                                {
                                    ID = reader["ServiceChargeDetail_ID"] != DBNull.Value ? Convert.ToInt32(reader["ServiceChargeDetail_ID"]) : 0,
                                    Branch = reader["Branch_Name"] != DBNull.Value ? reader["Branch_Name"].ToString() : string.Empty,
                                    BrandID = reader["Brand_ID"] != DBNull.Value ? Convert.ToInt32(reader["Brand_ID"]) : 0,
                                    Brand = reader["Brand_Name"] != DBNull.Value ? reader["Brand_Name"].ToString() : string.Empty,
                                    ModelID = reader["Model_ID"] != DBNull.Value ? Convert.ToInt32(reader["Model_ID"]) : 0,
                                    Model = reader["Model_Name"] != DBNull.Value ? reader["Model_Name"].ToString() : string.Empty,
                                    ProductTypeID = reader["ProductType_ID"] != DBNull.Value ? Convert.ToInt32(reader["ProductType_ID"]) : 0,
                                    ProductType = reader["ProductType_Name"] != DBNull.Value ? reader["ProductType_Name"].ToString() : string.Empty,
                                    ServiceCharge_Amount = reader["ServiceCharge_Amount"] != DBNull.Value ? Convert.ToDecimal(reader["ServiceCharge_Amount"]).ToString("0.00") : "0.00"
                                });
                            }
                        }
                    }

                    // Get Company Brands
                    using (SqlCommand brandCommand = new SqlCommand("sp_GetCompanyBrands", connection))
                    {
                        brandCommand.CommandType = CommandType.StoredProcedure;
                        brandCommand.Parameters.AddWithValue("@CompanyID", Company_ID);

                        using (SqlDataReader brandReader = brandCommand.ExecuteReader())
                        {
                            while (brandReader.Read())
                            {
                                Brands += brandReader["RefMasterDetail_ID"].ToString() + ":";
                                Brands += brandReader["RefMasterDetail_Name"] != DBNull.Value ? brandReader["RefMasterDetail_Name"].ToString() : string.Empty;
                                Brands += "; ";
                            }
                        }
                    }
                    Brands = Brands.TrimEnd(new char[] { ';', ' ' });

                    // Get Product Type
                    using (SqlCommand productTypeCommand = new SqlCommand("sp_GetProductTypeServiceChargesDetail", connection))
                    {
                        productTypeCommand.CommandType = CommandType.StoredProcedure;

                        using (SqlDataReader productTypeReader = productTypeCommand.ExecuteReader())
                        {
                            while (productTypeReader.Read())
                            {
                                ProductType += productTypeReader["ProductType_ID"].ToString() + ":";
                                ProductType += productTypeReader["ProductType_Name"] != DBNull.Value ? productTypeReader["ProductType_Name"].ToString() : string.Empty;
                                ProductType += "; ";
                            }
                        }
                    }
                    ProductType = ProductType.TrimEnd(new char[] { ';', ' ' });

                    // Get Models
                    using (SqlCommand modelCommand = new SqlCommand("sp_GetModel", connection))
                    {
                        modelCommand.CommandType = CommandType.StoredProcedure;

                        using (SqlDataReader modelReader = modelCommand.ExecuteReader())
                        {
                            while (modelReader.Read())
                            {
                                Models += modelReader["Model_ID"].ToString() + ":";
                                Models += modelReader["Model_Name"] != DBNull.Value ? modelReader["Model_Name"].ToString() : string.Empty;
                                Models += "; ";
                            }
                        }
                    }
                    Models = Models.TrimEnd(new char[] { ';', ' ' });

                    // Process Service Charge Details
                    var ServiceChargeDetailsArr = serviceChargeDetailsList.AsQueryable().OrderByField(sidx, sord);
                    if (_search)
                    {
                        // Parse and decrypt the filters
                        Filters filtersObj = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(filters))).ToObject<Filters>();

                        // Ensure filtersObj is not null and its filter collection has more than 0 items
                        if (filtersObj != null && filtersObj.rules.Count > 0)
                        {
                            // Perform the FilterSearch operation
                            ServiceChargeDetailsArr = ServiceChargeDetailsArr.FilterSearch(filtersObj);
                        }
                    }
                    else if (advnce && !string.IsNullOrEmpty(advnceFilters))
                    {
                        AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<AdvanceFilter>();
                        ServiceChargeDetailsArr = ServiceChargeDetailsArr.AdvanceSearch(advnfilter);
                        page = 1;
                    }
                    count = ServiceChargeDetailsArr.Count();
                    total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                    // Resource strings
                    string Lbl_Refresh = CommonFunctionalities.GetGlobalResourceObject(SelectServiceChargesDetailobj.UserCulture.ToString(), "refresh").ToString();
                    string editResourceValue = CommonFunctionalities.GetGlobalResourceObject(SelectServiceChargesDetailobj.UserCulture.ToString(), "edit").ToString();

                    // Final object to be returned as JSON
                    ServiceChargesDet = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        rows = (from a in ServiceChargeDetailsArr
                                select new
                                {
                                    edit = $"<a title=\"{editResourceValue}\" href='#' id='{a.ID}' key='{a.ID}' class='EditServiceChargesDetails font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                    delete = $"<input type='checkbox' key='{a.ID}' defaultchecked='' id='chk{a.ID}' class='DelServiceChargesDetails'/>",
                                    a.ID,
                                    a.Branch,
                                    a.BrandID,
                                    a.Brand,
                                    a.ModelID,
                                    a.Model,
                                    a.ProductTypeID,
                                    a.ProductType,
                                    a.ServiceCharge_Amount
                                }).ToList(),
                        Branch,
                        Brands,
                        ProductType,
                        Models,
                        Lbl_Refresh
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(ServiceChargesDet);
        }
        #endregion


        #region ::: SelectServiceChargesDetailLocale Uday Kumar J B 16-07-2024:::
        /// <summary>
        /// To Select the Service Charge Details
        /// </summary> 
        /// 

        public static IActionResult SelectServiceChargesDetailLocale(string connString, SelectServiceChargesDetailLocaleList SelectServiceChargesDetailLocaleobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int count = 0;
            int total = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            object ServiceChargesDet = null;
            string Branch = "-1:--Select--;";
            string Brands = "-1:--Select--;";
            string Models = "-1:--Select--";
            string ProductType = "-1:--Select--";

            try
            {
                int Company_ID = Convert.ToInt32(SelectServiceChargesDetailLocaleobj.Company_ID);

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    List<GNMServiceChargeDetails> serviceChargeDetailsList = new List<GNMServiceChargeDetails>();

                    // Get Service Charges Detail
                    using (SqlCommand command = new SqlCommand("sp_GetServiceChargesDetailLocale", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@ServiceChargeID", SelectServiceChargesDetailLocaleobj.id);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                GNMServiceChargeDetails detail = new GNMServiceChargeDetails
                                {
                                    ID = reader["ServiceChargeDetail_ID"] != DBNull.Value ? Convert.ToInt32(reader["ServiceChargeDetail_ID"]) : 0,
                                    Branch = reader["Branch_Name"] != DBNull.Value ? reader["Branch_Name"].ToString() : string.Empty,
                                    Brand = reader["Brand_Name"] != DBNull.Value ? reader["Brand_Name"].ToString() : string.Empty,
                                    Model = reader["Model_Name"] != DBNull.Value ? reader["Model_Name"].ToString() : string.Empty,
                                    ProductType = reader["ProductType_Name"] != DBNull.Value ? reader["ProductType_Name"].ToString() : string.Empty,
                                    ServiceCharge_Amount = reader["ServiceCharge_Amount"] != DBNull.Value ? Convert.ToDecimal(reader["ServiceCharge_Amount"]).ToString("0.00") : "0.00"
                                };
                                serviceChargeDetailsList.Add(detail);
                            }
                        }
                    }

                    // Get Company Brands
                    using (SqlCommand command = new SqlCommand("sp_GetCompanyBrandsLocale", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@CompanyID", Company_ID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                Brands += reader["RefMasterDetail_ID"].ToString() + ":" + reader["RefMasterDetail_Name"].ToString() + ";";
                            }
                        }
                    }
                    Brands = Brands.TrimEnd(new char[] { ';' });

                    // Get Product Type
                    using (SqlCommand command = new SqlCommand("sp_GetProductTypeLocaleServiceChargesDetailLocale", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ProductType += reader["ProductType_ID"].ToString() + ":" + reader["ProductType_Name"].ToString() + ";";
                            }
                        }
                    }
                    ProductType = ProductType.TrimEnd(new char[] { ';' });

                    // Get Models
                    using (SqlCommand command = new SqlCommand("sp_GetModelsLocale", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                Models += reader["Model_ID"].ToString() + ":" + reader["Model_Name"].ToString() + ";";
                            }
                        }
                    }
                    Models = Models.TrimEnd(new char[] { ';' });

                    // Get Branches
                    using (SqlCommand command = new SqlCommand("sp_GetBranchesLocale", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                Branch += reader["Branch_ID"].ToString() + ":" + reader["Branch_Name"].ToString() + ";";
                            }
                        }
                    }
                    Branch = Branch.TrimEnd(new char[] { ';' });

                    // Prepare data for return
                    var serviceChargesList = serviceChargeDetailsList.AsQueryable().OrderByField(sidx, sord);
                    if (_search)
                    {
                        // Parse and decrypt the filters
                        Filters filtersObj = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(filters))).ToObject<Filters>();

                        // Ensure filtersObj is not null and its filter collection has more than 0 items
                        if (filtersObj != null && filtersObj.rules.Count > 0)
                        {
                            // Perform the FilterSearch operation
                            serviceChargesList = serviceChargesList.FilterSearch(filtersObj);
                        }
                    }
                    else if (advnce && !string.IsNullOrEmpty(advnceFilters))
                    {
                        AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<AdvanceFilter>();
                        serviceChargesList = serviceChargesList.AdvanceSearch(advnfilter);
                        page = 1;
                    }
                    count = serviceChargesList.Count();
                    total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                    if (count < (rows * page) && count != 0)
                    {
                        page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                    }

                    var editcluture = CommonFunctionalities.GetGlobalResourceObject(SelectServiceChargesDetailLocaleobj.UserCulture.ToString(), "edit");
                    ServiceChargesDet = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        rows = serviceChargesList.Select(a => new
                        {
                            edit = $"<a title='{editcluture}' href='#' id='{a.ID}' key='{a.ID}' class='EditServiceChargesDetails font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                            delete = $"<input type='checkbox' key='{a.ID}' defaultchecked='' id='chk{a.ID}' class='DelServiceChargesDetails'/>",
                            a.ID,
                            a.Branch,
                            a.BrandID,
                            a.Brand,
                            a.Model,
                            a.ModelID,
                            a.ProductType,
                            a.ProductTypeID,
                            a.ServiceCharge_Amount
                        }).ToList(),
                        Branch,
                        Brands,
                        ProductType,
                        Models
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, $"{ex.GetType().FullName}:{ex.Message}", ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(ServiceChargesDet);
        }
        #endregion


        #region ::: LoadBrandDropdown Uday Kumar J B 16-07-2024:::
        /// <summary>
        ////to Select Brand Details Based on the login Company
        /// </summary> 
        /// 

        public static IActionResult LoadBrandDropdown(string connString, LoadBrandDropdownList LoadBrandDropdownobj)
        {
            var jsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                GNM_User User = LoadBrandDropdownobj.UserDetails.FirstOrDefault();
                int companyID = User.Company_ID;

                // Initialize list for storing brand data
                List<object> brandArr = new List<object>();

                // Connect to the database using ADO.NET
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Create command for executing stored procedure
                    using (SqlCommand command = new SqlCommand("sp_LoadBrandDropdown", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@CompanyID", companyID);

                        // Execute the command and read data
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var brand = new
                                {
                                    ID = Convert.ToInt32(reader["ID"]),
                                    Name = reader["Name"].ToString()
                                };
                                brandArr.Add(brand);
                            }
                        }
                    }
                }

                // Prepare JSON response
                jsonData = new
                {
                    BrandArr = brandArr
                };
            }
            catch (Exception ex)
            {
                // Handle exceptions
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: LoadMachineTypeDropdown Uday Kumar J B 16-07-2024:::
        /// <summary>
        ////to Select ProductType Based on the BrandID 
        /// </summary> 
        /// 

        public static IActionResult LoadMachineTypeDropdown(string connString, LoadMachineTypeDropdownList LoadMachineTypeDropdownobj)
        {
            var jsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // Initialize list for storing product type data
                List<object> productTypeArr = new List<object>();

                // Connect to the database using ADO.NET
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Create command for executing stored procedure
                    using (SqlCommand command = new SqlCommand("sp_LoadMachineTypeDropdown", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@BrandID", LoadMachineTypeDropdownobj.BrandID);

                        // Execute the command and read data
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var productType = new
                                {
                                    ID = Convert.ToInt32(reader["ID"]),
                                    Name = reader["Name"].ToString()
                                };
                                productTypeArr.Add(productType);
                            }
                        }
                    }
                }

                // Prepare JSON response
                jsonData = new
                {
                    ProductType = productTypeArr
                };
            }
            catch (Exception ex)
            {
                // Handle exceptions
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }
        #endregion


        #region::: LoadModelDropdown Uday Kumar J B 16-07-2024:::
        /// <summary>
        ////to Select Model BAsed on the product Type
        /// </summary> 
        /// 

        public static IActionResult LoadModelDropdown(string connString, LoadModelDropdownList LoadModelDropdownobj)
        {
            var jsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // Initialize list for storing model data
                List<object> modelArr = new List<object>();

                // Connect to the database using ADO.NET
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Create command for executing stored procedure
                    using (SqlCommand command = new SqlCommand("sp_LoadModelDropdown", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@ProductTypeID", LoadModelDropdownobj.MtypeID);

                        // Execute the command and read data
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var model = new
                                {
                                    ID = Convert.ToInt32(reader["ID"]),
                                    Name = reader["Name"].ToString()
                                };
                                modelArr.Add(model);
                            }
                        }
                    }
                }

                // Prepare JSON response
                jsonData = new
                {
                    Model = modelArr
                };
            }
            catch (Exception ex)
            {
                // Handle exceptions
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: InsertServiceChargeDetails Uday Kumar J B 16-07-2024:::
        /// <summary>
        /// Method to insert Service Charge Details
        /// </summary> 
        /// 

        public static IActionResult InsertServiceChargeDetails(string connString, InsertServiceChargeDetailsList InsertServiceChargeDetailsobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var InsertServiceChargeDetails = new { ServiceCharge_ID = 0 };
            try
            {
                JObject jObj = JObject.Parse(InsertServiceChargeDetailsobj.key);
                JArray rows = (JArray)jObj["rows"]; // Retrieve the array of rows

                foreach (JToken row in rows)
                {
                    // Read values from each row using direct casting to JObject
                    string ID = row["ID"].ToString();
                    int ServiceCharge_ID = Convert.ToInt32(row["ServiceCharge_ID"]);
                    int Branch = Convert.ToInt32(row["Branch"]);
                    int? Brand_ID = Convert.ToInt32(row["Brand"]) == -1 ? null : (int?)Convert.ToInt32(row["Brand"]);
                    int? ProductType_ID = Convert.ToInt32(row["ProductType"]) == -1 ? null : (int?)Convert.ToInt32(row["ProductType"]);
                    int? Model_ID = Convert.ToInt32(row["Model"]) == -1 ? null : (int?)Convert.ToInt32(row["Model"]);
                    decimal Amount = Convert.ToDecimal(row["Amount"]);

                    // Determine if it's an insert or update
                    if (string.IsNullOrEmpty(ID))
                    {
                        // Insert new service charge detail
                        using (SqlConnection connection = new SqlConnection(connString))
                        {
                            connection.Open();

                            using (SqlCommand command = new SqlCommand("sp_InsertServiceChargeDetail", connection))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@ServiceCharge_ID", ServiceCharge_ID);
                                command.Parameters.AddWithValue("@Branch_ID", Branch);
                                command.Parameters.AddWithValue("@Brand_ID", Brand_ID ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@ProductType_ID", ProductType_ID ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Model_ID", Model_ID ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@ServiceCharge_Amount", Amount);

                                SqlParameter outputParameter = new SqlParameter("@ServiceCharge_ID_Output", SqlDbType.Int)
                                {
                                    Direction = ParameterDirection.Output
                                };
                                command.Parameters.Add(outputParameter);
                                command.ExecuteNonQuery();

                                int insertedID = (int)outputParameter.Value;
                                InsertServiceChargeDetails = new { ServiceCharge_ID = insertedID };
                            }
                        }
                    }
                    else
                    {
                        // Update existing service charge detail
                        int ServiceID = Convert.ToInt32(ID);

                        using (SqlConnection connection = new SqlConnection(connString))
                        {
                            connection.Open();

                            using (SqlCommand command = new SqlCommand("sp_UpdateServiceChargeDetail", connection))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@ServiceChargeDetail_ID", ServiceID);
                                command.Parameters.AddWithValue("@Branch_ID", Branch);
                                command.Parameters.AddWithValue("@Brand_ID", Brand_ID ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@ProductType_ID", ProductType_ID ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@Model_ID", Model_ID ?? (object)DBNull.Value);
                                command.Parameters.AddWithValue("@ServiceCharge_Amount", Amount);

                                SqlParameter outputParameter = new SqlParameter("@ServiceCharge_ID_Output", SqlDbType.Int)
                                {
                                    Direction = ParameterDirection.Output
                                };
                                command.Parameters.Add(outputParameter);
                                command.ExecuteNonQuery();

                                int insertedID = (int)outputParameter.Value;
                                InsertServiceChargeDetails = new { ServiceCharge_ID = insertedID };
                            }
                        }
                    }

                    // Perform additional operations if needed
                    CommonFunctionalities.InsertGPSDetails(Convert.ToInt32(InsertServiceChargeDetailsobj.Company_ID), Convert.ToInt32(InsertServiceChargeDetailsobj.Branch), Convert.ToInt32(InsertServiceChargeDetailsobj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreServiceChargesMaster")), ServiceCharge_ID, 0, 0, "Update", false, Convert.ToInt32(InsertServiceChargeDetailsobj.MenuID));
                }
            }
            catch (Exception ex)
            {
                // Handle exceptions
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(InsertServiceChargeDetails);
        }
        #endregion


        #region ::: DeleteServiceCharge Uday Kumar J B 16-07-2024 :::
        /// <summary>
        /// to Delete the Service Charge
        /// </summary>
        /// 
        public static IActionResult DeleteServiceCharge(string connString, DeleteServiceChargeList DeleteServiceChargeobj)
        {
            string errorMsg = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jobj = JObject.Parse(DeleteServiceChargeobj.key);
                JArray rows = (JArray)jobj["rows"]; // Retrieve the array of rows

                foreach (JToken row in rows)
                {
                    int id = Convert.ToInt32(row["id"]);

                    using (SqlConnection connection = new SqlConnection(connString))
                    {
                        connection.Open();

                        // Check if any dependencies exist before deletion
                        using (SqlCommand checkDependencyCommand = new SqlCommand("GNM_CheckServiceChargeDependency", connection))
                        {
                            checkDependencyCommand.CommandType = CommandType.StoredProcedure;
                            checkDependencyCommand.Parameters.AddWithValue("@ServiceCharge_ID", id);
                            int dependencyCount = (int)checkDependencyCommand.ExecuteScalar();

                            if (dependencyCount > 0)
                            {
                                // Dependency found, cannot delete
                                errorMsg += $"Service charge with ID {id} has dependencies and cannot be deleted.<br/>";
                                continue; // Skip to next iteration
                            }
                        }

                        // Delete from GNM_ServiceCharges
                        using (SqlCommand deleteServiceChargeCommand = new SqlCommand("GNM_DeleteServiceCharge", connection))
                        {
                            deleteServiceChargeCommand.CommandType = CommandType.StoredProcedure;
                            deleteServiceChargeCommand.Parameters.AddWithValue("@ServiceCharge_ID", id);
                            deleteServiceChargeCommand.ExecuteNonQuery();

                            // Log deletion
                            CommonFunctionalities.InsertGPSDetails(Convert.ToInt32(DeleteServiceChargeobj.Company_ID), Convert.ToInt32(DeleteServiceChargeobj.Branch), Convert.ToInt32(DeleteServiceChargeobj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreServiceChargesMaster")), id, 0, 0, "Delete", false, Convert.ToInt32(DeleteServiceChargeobj.MenuID));
                        }
                    }
                }

                if (string.IsNullOrEmpty(errorMsg))
                {
                    errorMsg = CommonFunctionalities.GetResourceString(DeleteServiceChargeobj.UserCulture.ToString(), "deletedsuccessfully").ToString();
                }
            }
            catch (SqlException sqlEx) // Catch SQL exceptions separately
            {
                if (sqlEx.Number == 547) // SQL error number for foreign key constraint violation
                {
                    errorMsg = CommonFunctionalities.GetResourceString(DeleteServiceChargeobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    errorMsg = "An error occurred while deleting service charges."; // Generic error message for other SQL exceptions
                }

                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(sqlEx.HResult, sqlEx.GetType().FullName + ":" + sqlEx.Message, sqlEx.TargetSite.ToString(), sqlEx.StackTrace);
                }
            }
            catch (Exception ex) // Catch all other exceptions
            {
                errorMsg = "An error occurred while deleting service charges."; // Generic error message
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(errorMsg);
        }

        #endregion


        #region ::: DelServiceChargesDetails  Uday Kumar J B 16-07-2024 :::
        /// <summary>
        /// to Delete the Service Charges Details
        /// </summary>
        /// 
        public static IActionResult DelServiceChargesDetails(string connString, DelServiceChargesDetailsList DelServiceChargesDetailsobj)
        {
            string errorMsg = "";
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jobj = JObject.Parse(DelServiceChargesDetailsobj.key);
                JArray rows = (JArray)jobj["rows"]; // Retrieve the array of rows

                foreach (JToken row in rows)
                {
                    int id = Convert.ToInt32(row["id"]);

                    using (SqlConnection connection = new SqlConnection(connString))
                    {
                        connection.Open();

                        // Check if any dependencies exist before deletion
                        SqlCommand checkDependencyCommand = new SqlCommand("sp_CheckDependency", connection);
                        checkDependencyCommand.CommandType = CommandType.StoredProcedure;
                        checkDependencyCommand.Parameters.AddWithValue("@ServiceChargeDetail_ID", id);
                        int dependencyCount = (int)checkDependencyCommand.ExecuteScalar();

                        if (dependencyCount > 0)
                        {
                            // Dependency found, cannot delete
                            errorMsg = CommonFunctionalities.GetGlobalResourceObject(DelServiceChargesDetailsobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                            continue; // Skip to next iteration
                        }

                        // Delete from GNM_ServiceChargesDetail using stored procedure
                        SqlCommand deleteServiceChargeDetailCommand = new SqlCommand("sp_DelServiceChargesDetails", connection);
                        deleteServiceChargeDetailCommand.CommandType = CommandType.StoredProcedure;
                        deleteServiceChargeDetailCommand.Parameters.AddWithValue("@ServiceChargeDetail_ID", id);
                        deleteServiceChargeDetailCommand.ExecuteNonQuery();

                        // Log deletion
                        CommonFunctionalities.InsertGPSDetails(Convert.ToInt32(DelServiceChargesDetailsobj.Company_ID), Convert.ToInt32(DelServiceChargesDetailsobj.Branch), Convert.ToInt32(DelServiceChargesDetailsobj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreServiceChargesMaster")), id, 0, 0, "Delete", false, Convert.ToInt32(DelServiceChargesDetailsobj.MenuID));
                    }
                }

                if (string.IsNullOrEmpty(errorMsg))
                {
                    errorMsg = CommonFunctionalities.GetGlobalResourceObject(DelServiceChargesDetailsobj.UserCulture.ToString(), "deletedsuccessfully").ToString();
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(errorMsg);
        }

        #endregion


        #region ::: ServiceChargeExport Uday Kumar J B 16-07-2024 Pending :::
        /// <summary>
        /// Exporting Service Charges Grid
        /// </summary>
        /// 
        public static async Task<object> ServiceChargeExport(ServiceChargeExportList ServiceChargeExportobj, string connString, string filters, string advnceFilters, string sidx, string sord)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            int userID = ServiceChargeExportobj.User_ID;

            try
            {
                List<GNM_ServiceCharges> serviceChargesLists = new List<GNM_ServiceCharges>();

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // FilterServiceChargeBasedonCompany
                    bool FilterServiceChargeBasedonCompany = true;
                    using (SqlCommand command = new SqlCommand("GetFilterServiceChargeBasedonCompany", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@CompanyID", ServiceChargeExportobj.Company_ID);
                        FilterServiceChargeBasedonCompany = command.ExecuteScalar().ToString().ToUpper() == "TRUE";
                    }

                    using (SqlCommand command = new SqlCommand("sp_GetServiceCharges", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@UserId", ServiceChargeExportobj.User_ID);
                        command.Parameters.AddWithValue("@CompanyId", ServiceChargeExportobj.Company_ID);
                        command.Parameters.AddWithValue("@FilterServiceChargeBasedonCompany", FilterServiceChargeBasedonCompany);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var serviceCharge = new GNM_ServiceCharges
                                {
                                    ServiceCharge_ID = reader.GetInt32(reader.GetOrdinal("ServiceCharge_ID")),
                                    ServiceCharge_Code = reader.GetString(reader.GetOrdinal("ServiceCharge_Code")),
                                    ServiceCharge_Description = reader.GetString(reader.GetOrdinal("ServiceCharge_Description")),
                                    ServiceCharge_Amount = Convert.ToDecimal(reader.GetOrdinal("ServiceCharge_Amount")),
                                    ServiceCharge_IsActive = reader.GetBoolean(reader.GetOrdinal("ServiceCharge_IsActive"))
                                };

                                serviceChargesLists.Add(serviceCharge);
                            }
                        }
                    }

                    if (ServiceChargeExportobj.Language_ID == ServiceChargeExportobj.GeneralLanguage_ID)
                    {
                        using (SqlCommand command = new SqlCommand("sp_LoadGridLocale", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@UserId", ServiceChargeExportobj.User_ID);
                            command.Parameters.AddWithValue("@CompanyId", ServiceChargeExportobj.Company_ID);
                            command.Parameters.AddWithValue("@LanguageId", ServiceChargeExportobj.Language_ID);
                            command.Parameters.AddWithValue("@FilterServiceChargeBasedonCompany", FilterServiceChargeBasedonCompany ? 1 : 0);

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var serviceCharge = new GNM_ServiceCharges
                                    {
                                        ServiceCharge_ID = reader.GetInt32(reader.GetOrdinal("ServiceCharge_ID")),
                                        ServiceCharge_Code = reader.GetString(reader.GetOrdinal("ServiceCharge_Code")),
                                        ServiceCharge_Description = reader.GetString(reader.GetOrdinal("ServiceCharge_Description")),
                                        ServiceCharge_Amount = Convert.ToDecimal(reader.GetOrdinal("ServiceCharge_Amount")),
                                        ServiceCharge_IsActive = reader.GetBoolean(reader.GetOrdinal("ServiceCharge_IsActive"))
                                    };

                                    serviceChargesLists.Add(serviceCharge);
                                }
                            }
                        }
                    }

                    var arrServiceCharges = serviceChargesLists.AsQueryable().OrderByField(sidx, sord);

                    // Apply standard filters if present
                    if (!string.IsNullOrEmpty(filters) && filters != "null" && filters != "undefined")
                    {
                        Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                        if (filtersObj.rules.Count > 0)
                            arrServiceCharges = arrServiceCharges.FilterSearch(filtersObj);
                    }

                    // Apply advanced filters if present
                    if (!string.IsNullOrEmpty(advnceFilters) && advnceFilters != "null")
                    {
                        AdvanceFilter advnfilter = JObject.Parse(Common.DecryptString(advnceFilters)).ToObject<AdvanceFilter>();
                        arrServiceCharges = arrServiceCharges.AdvanceSearch(advnfilter);
                    }
                    // Create DataTable and add columns
                    DataTable dt = new DataTable();
                    dt.Columns.Add(CommonFunctionalities.GetResourceString(ServiceChargeExportobj.GeneralCulture.ToString(), "code").ToString());
                    dt.Columns.Add(CommonFunctionalities.GetResourceString(ServiceChargeExportobj.GeneralCulture.ToString(), "description").ToString());
                    dt.Columns.Add(CommonFunctionalities.GetResourceString(ServiceChargeExportobj.GeneralCulture.ToString(), "Amount").ToString());
                    dt.Columns.Add(CommonFunctionalities.GetResourceString(ServiceChargeExportobj.GeneralCulture.ToString(), "Active").ToString());

                    // Populate DataTable with data from arrServiceCharge
                    foreach (var serviceCharge in arrServiceCharges)
                    {
                        dt.Rows.Add(
                            serviceCharge.ServiceCharge_Code.ToString(),
                            serviceCharge.ServiceCharge_Description.ToString(),
                            serviceCharge.ServiceCharge_Amount,
                            serviceCharge.ServiceCharge_IsActive ? "Yes" : "No"
                        );
                    }

                    // Document Export
                    DataTable dtAlignment = new DataTable();
                    dtAlignment.Columns.Add("code");
                    dtAlignment.Columns.Add("description");
                    dtAlignment.Columns.Add("Amount");
                    dtAlignment.Columns.Add("Active");
                    dtAlignment.Rows.Add(0, 0, 2, 1);

                    ExportList reportExportList = new ExportList
                    {
                        Company_ID = ServiceChargeExportobj.Company_ID, // Assuming this is available in ExportObj
                        Branch = ServiceChargeExportobj.Branch,
                        dt1 = dtAlignment,


                        dt = dt,

                        FileName = "LaborCharges", // Set a default or dynamic filename
                        Header = CommonFunctionalities.GetResourceString(ServiceChargeExportobj.UserCulture.ToString(), "servicechargesenglish").ToString(), // Set a default or dynamic header
                        exprtType = ServiceChargeExportobj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                        UserCulture = ServiceChargeExportobj.UserCulture
                    };
                    //return DocumentExport.Export(reportExportList, connString, LogException);
                    var result = await DocumentExport.Export(reportExportList, connString, LogException);
                    return result.Value;
                    // DocumentExport.Export(ServiceChargeExportobj.exprtType, dt, dtAlignment, "LaborCharges", 
                    // HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "servicechargesenglish").ToString());

                    // Logging
                    //CommonFunctionalities.InsertGPSDetails(
                    //    Convert.ToInt32(ServiceChargeExportobj.Company_ID.ToString()),
                    //    Convert.ToInt32(ServiceChargeExportobj.Branch),
                    //    ServiceChargeExportobj.User_ID,
                    //    Common.GetObjectID("CoreServiceChargesMaster"),
                    //    0,
                    //    0,
                    //    0,
                    //    "ServiceCharges-Export ",
                    //    false,
                    //    Convert.ToInt32(ServiceChargeExportobj.MenuID),
                    //    Convert.ToDateTime(ServiceChargeExportobj.LoggedINDateTime)
                    //);
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return null;
        }
        #endregion


        #region ::: ValidateServiceCode Uday Kumar J B 16-07-2024:::
        /// <summary>
        /// To Validate PartNumber for duplicate 
        /// </summary>
        /// 

        public static IActionResult ValidateServiceCode(string connString, ValidateServiceCodeList ValidateServiceCodeobj)
        {
            var ServiceCore = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string Sc = Common.DecryptString(ValidateServiceCodeobj.Scode);
            try
            {
                bool FilterServiceChargeBasedonCompany = true;

                // Fetching FilterServiceChargeBasedonCompany value using ADO.NET
                string filterParamValue = string.Empty;
                using (SqlConnection con = new SqlConnection(connString))
                {
                    SqlCommand paramCmd = new SqlCommand("GetFilterServiceChargeBasedonCompany", con);
                    paramCmd.CommandType = CommandType.StoredProcedure;
                    paramCmd.Parameters.AddWithValue("@CompanyID", ValidateServiceCodeobj.Company_ID);
                    con.Open();
                    filterParamValue = (string)paramCmd.ExecuteScalar();
                }
                FilterServiceChargeBasedonCompany = filterParamValue.ToUpper() == "TRUE" ? true : false;

                List<GNM_ServiceCharges> liServiceCCharges = new List<GNM_ServiceCharges>();

                // Fetching service charges based on conditions using ADO.NET
                using (SqlConnection con = new SqlConnection(connString))
                {
                    SqlCommand cmd = new SqlCommand("GetServiceCharges", con);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@ServiceCode", Sc);
                    cmd.Parameters.AddWithValue("@CompanyID", FilterServiceChargeBasedonCompany ? ValidateServiceCodeobj.Company_ID : (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@Key", ValidateServiceCodeobj.key);
                    con.Open();
                    SqlDataReader reader = cmd.ExecuteReader();
                    while (reader.Read())
                    {
                        GNM_ServiceCharges charge = new GNM_ServiceCharges();
                        charge.ServiceCharge_ID = (int)reader["ServiceCharge_ID"];
                        charge.ServiceCharge_Code = reader["ServiceCharge_Code"].ToString();
                        charge.Company_ID = (int)reader["Company_ID"];
                        // Populate other properties as needed
                        liServiceCCharges.Add(charge);
                    }
                }

                // Validating service code existence
                if (liServiceCCharges.Count == 0)
                {
                    ServiceCore = new { IsValid = true };
                }
                else
                {
                    ServiceCore = new { IsValid = false };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Uri.EscapeDataString(Newtonsoft.Json.JsonConvert.SerializeObject(ServiceCore)));
        }

        #endregion


        #region ::: ServiceChargeProdExits Uday Kumar J B 16-07-2024:::
        /// <summary>
        /// To check the Duplicate rows in details Grid
        /// </summary>
        /// 

        public static IActionResult ServiceChargeProdExits(string connString, ServiceChargeProdExitsList ServiceChargeProdExitsobj)
        {
            JTokenReader jr = null;
            var jsonResult = default(dynamic);
            int? j = null;
            bool ServiceChargesDet = false;
            int ServiceID = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jObj = JObject.Parse(ServiceChargeProdExitsobj.key);
                int ServiceChargescount = jObj["rows"].Count();
                List<int> rowIndex = new List<int>();

                // Establishing database connection
                using (SqlConnection con = new SqlConnection(connString))
                {
                    con.Open();

                    for (int i = 0; i < ServiceChargescount; i++)
                    {
                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["ID"]);
                        jr.Read();
                        string ID = jr.Value.ToString();
                        if (!string.IsNullOrEmpty(ID))
                            ServiceID = Convert.ToInt32(ID);

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["ServiceCharge_ID"]);
                        jr.Read();
                        int ServiceCharge_ID = Convert.ToInt32(jr.Value.ToString());

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Branch"]);
                        jr.Read();
                        int Branch = Convert.ToInt32(jr.Value.ToString());

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Brand"]);
                        jr.Read();
                        int? Brand_ID = Convert.ToInt32(jr.Value.ToString()) == -1 ? j : Convert.ToInt32(jr.Value.ToString());

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["ProductType"]);
                        jr.Read();
                        int? ProductType_ID = Convert.ToInt32(jr.Value.ToString()) == -1 ? j : Convert.ToInt32(jr.Value.ToString());

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Model"]);
                        jr.Read();
                        int? Model_ID = Convert.ToInt32(jr.Value.ToString()) == -1 ? j : Convert.ToInt32(jr.Value.ToString());

                        // Using SqlCommand to call stored procedure
                        SqlCommand cmd = new SqlCommand("CheckServiceChargeDetailExists", con);
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@ServiceChargeID", ServiceCharge_ID);
                        cmd.Parameters.AddWithValue("@BranchID", Branch);
                        cmd.Parameters.AddWithValue("@BrandID", Brand_ID ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ProductTypeID", ProductType_ID ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ModelID", Model_ID ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ServiceID", ServiceID);

                        int count = (int)cmd.ExecuteScalar();
                        if (count > 0)
                        {
                            rowIndex.Add(i);
                        }
                    }
                }

                // Creating JSON result based on rowIndex
                jsonResult = new
                {
                    ServiceChargesDet = rowIndex.Count > 0,
                    rowsIndex = rowIndex.ToArray()
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonResult);
        }
        #endregion


        #region ::: SelectProductType Uday Kumar J B 16-07-2024:::
        /// <summary>
        /// To select ProductType
        /// </summary>   
        /// 
        public static IActionResult SelectProductType(string connString, SelectProductTypeLista SelectProductTypeobj)
        {
            var jsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // Establishing database connection
                using (SqlConnection con = new SqlConnection(connString))
                {
                    con.Open();

                    // Using SqlCommand to call stored procedure
                    SqlCommand cmd = new SqlCommand("GetProductTypesByBrandID", con);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@BrandID", SelectProductTypeobj.id);

                    // Using SqlDataAdapter to fill DataTable
                    DataTable dt = new DataTable();
                    using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                    {
                        da.Fill(dt);
                    }

                    // Creating JSON data from DataTable
                    jsonData = new
                    {
                        ProductType = from DataRow row in dt.Rows
                                      orderby row["Name"].ToString()
                                      select new
                                      {
                                          ID = Convert.ToInt32(row["ID"]),
                                          Name = row["Name"].ToString()
                                      }
                    };
                }

                return new JsonResult(jsonData);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(null);
            }
        }
        #endregion


        #region ::: SelectModel Uday Kumar J B 16-07-2024:::
        /// <summary>
        /// To select Model
        /// </summary>
        /// 

        public static IActionResult SelectModel(string connString, SelectModelList SelectModelobj)
        {
            var jsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // Establishing database connection
                using (SqlConnection con = new SqlConnection(connString))
                {
                    con.Open();

                    // Using SqlCommand to call stored procedure
                    SqlCommand cmd = new SqlCommand("GetModelsByProductTypeID", con);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@ProductTypeID", SelectModelobj.id);

                    // Using SqlDataAdapter to fill DataTable
                    DataTable dt = new DataTable();
                    using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                    {
                        da.Fill(dt);
                    }

                    // Creating JSON data from DataTable
                    jsonData = new
                    {
                        Model = from DataRow row in dt.Rows
                                orderby row["Name"].ToString()
                                select new
                                {
                                    ID = Convert.ToInt32(row["ID"]),
                                    Name = row["Name"].ToString()
                                }
                    };
                }

                return new JsonResult(jsonData);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(null);
            }
        }
        #endregion


        #region :::CoreServiceChargesMaster List and Obj class Uday Kumar J B 16-07-2024:::
        /// <summary>
        /// To select Model
        /// </summary>
        /// 

        public class SelectServiceChargesList
        {

            public List<GNM_User> UserDetails { get; set; }

            public List<GNM_ServiceCharges> ServiceCharge { get; set; }
            public string UserCulture { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
        }

        public class ServiceChargeExportList
        {
            public string GeneralCulture { get; set; }
            public int exprtType { get; set; }

            public int Branch { get; set; }

            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public int User_ID { get; set; }
            public int Language_ID { get; set; }
            public int GeneralLanguage_ID { get; set; }
            public string UserCulture { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filter { get; set; }
            public string advanceFilter { get; set; }

        }

        public class SelectModelList
        {
            public int id { get; set; }
        }

        public class SelectProductTypeLista
        {
            public int id { get; set; }
        }

        public class ServiceChargeProdExitsList
        {
            public string key { get; set; }
        }
        public class ValidateServiceCodeList
        {
            public string Scode { get; set; }
            public int Company_ID { get; set; }
            public int key { get; set; }
            public List<GNM_User> UserDetails { get; set; }


        }

        public class DelServiceChargesDetailsList
        {
            public string key { get; set; }
            public int Company_ID { get; set; }

            public int Branch { get; set; }

            public int User_ID { get; set; }

            public int MenuID { get; set; }
            public string UserCulture { get; set; }

        }

        public class DeleteServiceChargeList
        {
            public string key { get; set; }
            public int Company_ID { get; set; }

            public int Branch { get; set; }

            public int User_ID { get; set; }

            public int MenuID { get; set; }
            public string UserCulture { get; set; }

        }

        public class InsertServiceChargeDetailsList
        {
            public int Company_ID { get; set; }

            public int Branch { get; set; }

            public int User_ID { get; set; }

            public int MenuID { get; set; }

            public string key { get; set; }

        }

        public class LoadModelDropdownList
        {
            public int MtypeID { get; set; }

        }


        public class LoadMachineTypeDropdownList
        {
            public int BrandID { get; set; }

        }

        public class LoadBrandDropdownList
        {
            public List<GNM_User> UserDetails { get; set; }

        }

        public class SelectServiceChargesDetailLocaleList
        {
            public int id { get; set; }

            public int Company_ID { get; set; }

            public string UserCulture { get; set; }

        }


        public class SelectServiceChargesDetailList
        {
            public int id { get; set; }

            public int Company_ID { get; set; }
            public List<GNM_User> UserDetails { get; set; }

            public string UserCulture { get; set; }

        }

        public class SelectSingleServiceChargeLocaleList
        {
            public int UserLanguageID { get; set; }
            public int id { get; set; }

        }

        public class SelectSingleServiceChargeList
        {
            public int Branch { get; set; }
            public List<GNM_User> UserDetails { get; set; }

            public int id { get; set; }

            public string Company_ID { get; set; }
            public int MenuID { get; set; }
            public int User_ID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }

        public class InsertServiceChargeHeaderLocaleList
        {
            public string key { get; set; }

            public List<GNM_User> UserDetails { get; set; }

            public int UserLanguageID { get; set; }

            public string Company_ID { get; set; }
            public int Branch { get; set; }

            public int User_ID { get; set; }

            public int MenuID { get; set; }

        }

        public class LoadGridLocaleCoreServiceChargesList
        {
            public List<GNM_User> UserDetails { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int Language_ID { get; set; }

        }

        public class InsertServiceChargeHeaderList
        {
            public string key { get; set; }

            public List<GNM_User> UserDetails { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
        }
        #endregion


        #region :::CoreServiceChargesMaster class Uday Kumar J B 16-07-2024:::
        /// <summary>
        /// To select Model
        /// </summary>
        /// 
        public class GNMServiceChargeDetails
        {
            public int ID { set; get; }
            public string Branch { set; get; }
            public int BrandID { set; get; }
            public string Brand { set; get; }
            public int ModelID { set; get; }
            public string Model { set; get; }
            public int ProductTypeID { set; get; }
            public string ProductType { set; get; }
            public string ServiceCharge_Amount { set; get; }
        }
        public partial class GNM_ServiceCharges
        {
            public GNM_ServiceCharges()
            {
                this.GNM_ServiceChargesDetail = new HashSet<GNM_ServiceChargesDetail>();
                this.GNM_ServiceChargesLocale = new HashSet<GNM_ServiceChargesLocale>();
            }

            public int ServiceCharge_ID { get; set; }
            public int Company_ID { get; set; }
            public string ServiceCharge_Description { get; set; }
            public string ServiceCharge_Code { get; set; }
            public bool ServiceCharge_IsActive { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }
            public decimal ServiceCharge_Amount { get; set; }

            public virtual ICollection<GNM_ServiceChargesDetail> GNM_ServiceChargesDetail { get; set; }
            public virtual ICollection<GNM_ServiceChargesLocale> GNM_ServiceChargesLocale { get; set; }
        }

        public partial class GNM_ServiceChargesDetail
        {
            public int ServiceChargeDetail_ID { get; set; }
            public int ServiceCharge_ID { get; set; }
            public int Branch_ID { get; set; }
            public Nullable<int> Brand_ID { get; set; }
            public Nullable<int> ProductType_ID { get; set; }
            public Nullable<int> Model_ID { get; set; }
            public decimal ServiceCharge_Amount { get; set; }

            public virtual GNM_ServiceCharges GNM_ServiceCharges { get; set; }
        }
        public partial class GNM_ServiceChargesLocale
        {
            public int ServiceChargeLocale_ID { get; set; }
            public int ServiceCharge_ID { get; set; }
            public string ServiceCharge_Description { get; set; }
            public int Language_ID { get; set; }

            public virtual GNM_ServiceCharges GNM_ServiceCharges { get; set; }
        }
        #endregion

    }
}
