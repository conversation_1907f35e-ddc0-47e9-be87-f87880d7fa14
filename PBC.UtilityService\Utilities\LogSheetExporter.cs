using System;
using System.IO;
using System.Text;

namespace PBC.UtilityService.Utilities
{
    public class LogSheetExporter
    {
        private static string _LogFileName = "C:\\HCLGIT_SW\\AMERP-Microservice\\PBC.UtilityService\\Logs\\ErrorLog.txt";

        public static void LogToTextFile(int ExId, string ExMessage, string ExDetails, string ExStackTrace)
        {
            try
            {
                // Ensure the directory exists
                string logDirectory = Path.GetDirectoryName(_LogFileName);
                if (!Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory);
                }

                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.AppendFormat("Exception Date:{0}", DateTime.Now);
                stringBuilder.AppendFormat("{0}Exception From:", Environment.NewLine);
                stringBuilder.AppendFormat("{0}{1}: {2}", Environment.NewLine, ExMessage, ExId);
                stringBuilder.AppendFormat("{0}{0}Exception Information{0}{1}", Environment.NewLine, ExDetails);
                stringBuilder.AppendFormat("{0}{0}Stack Trace{0}{1}", Environment.NewLine, ExStackTrace);
                
                using (StreamWriter streamWriter = File.AppendText(_LogFileName))
                {
                    streamWriter.WriteLine(stringBuilder.ToString());
                    streamWriter.WriteLine("----------------------------------");
                }
            }
            catch (Exception ex)
            {
                // If logging fails, write to console as fallback
                Console.WriteLine($"Failed to write to log file: {ex.Message}");
                Console.WriteLine($"Original error - ID: {ExId}, Message: {ExMessage}");
            }
        }
    }
}
