//------------------------------------------------------------------------------
// <auto-generated>
//    This code was generated from a template.
//
//    Manual changes to this file may cause unexpected behavior in your application.
//    Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace WorkFlow.Models
{
    using System;
    using System.Collections.Generic;
    
    public partial class WF_WFStepLink
    {
        public int WFStepLink_ID { get; set; }
        public int WorkFlow_ID { get; set; }
        public int Company_ID { get; set; }
        public int FrmWFSteps_ID { get; set; }
        public int WFAction_ID { get; set; }
        public int ToWFSteps_ID { get; set; }
        public Nullable<int> Addresse_WFRole_ID { get; set; }
        public byte Addresse_Flag { get; set; }
        public bool IsSMSSentToCustomer { get; set; }
        public bool IsEmailSentToCustomer { get; set; }
        public bool IsSMSSentToAddressee { get; set; }
        public bool IsEmailSentToAddresse { get; set; }
        public bool AutoAllocationAllowed { get; set; }
        public bool IsVersionEnabled { get; set; }
        public Nullable<int> InvokeParentWF_ID { get; set; }
        public Nullable<int> InvokeParentWFLink_ID { get; set; }
        public Nullable<int> InvokeChildObject_ID { get; set; }
        public Nullable<int> InvokeChildObjectAction { get; set; }
        public Nullable<int> WFField_ID { get; set; }
        public string AutoCondition { get; set; }
    
        public virtual WF_WFAction GNM_WFAction { get; set; }
        public virtual WF_WFRole GNM_WFRole { get; set; }
        public virtual WF_WFSteps GNM_WFSteps { get; set; }
        public virtual WF_WFSteps GNM_WFSteps1 { get; set; }
        public virtual WF_WorkFlow GNM_WorkFlow { get; set; }
    }
}
