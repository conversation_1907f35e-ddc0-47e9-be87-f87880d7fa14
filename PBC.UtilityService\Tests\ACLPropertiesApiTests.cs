using Microsoft.AspNetCore.Mvc.Testing;
using System.Text;
using System.Text.Json;
using PBC.UtilityService.Utilities.DTOs;
using Xunit;

namespace PBC.UtilityService.Tests
{
    public class ACLPropertiesApiTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public ACLPropertiesApiTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory;
            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task GetAllACLProperties_ReturnsEmptyList_WhenNoDataExists()
        {
            // Act
            var response = await _client.GetAsync("/api/aclproperties");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var aclProperties = JsonSerializer.Deserialize<List<ACLPropertiesDto>>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            Assert.NotNull(aclProperties);
            Assert.Empty(aclProperties);
        }

        [Fact]
        public async Task CreateACLProperties_ReturnsCreatedACLProperties_WhenValidDataProvided()
        {
            // Arrange
            var createRequest = new CreateACLPropertiesRequest
            {
                Object_ID = 1001,
                RoleObject_Create = 1,
                RoleObject_Read = 1,
                RoleObject_Update = 1,
                RoleObject_Delete = 0,
                RoleObject_Print = 1,
                RoleObject_Export = 1,
                RoleObject_Import = 0
            };

            var json = JsonSerializer.Serialize(createRequest);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PostAsync("/api/aclproperties", content);

            // Assert
            response.EnsureSuccessStatusCode();
            var responseContent = await response.Content.ReadAsStringAsync();
            var createdACLProperties = JsonSerializer.Deserialize<ACLPropertiesDto>(responseContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            Assert.NotNull(createdACLProperties);
            Assert.Equal(1001, createdACLProperties.Object_ID);
            Assert.Equal(1, createdACLProperties.RoleObject_Create);
            Assert.Equal(1, createdACLProperties.RoleObject_Read);
            Assert.Equal(1, createdACLProperties.RoleObject_Update);
            Assert.Equal(0, createdACLProperties.RoleObject_Delete);
            Assert.Equal(1, createdACLProperties.RoleObject_Print);
            Assert.Equal(1, createdACLProperties.RoleObject_Export);
            Assert.Equal(0, createdACLProperties.RoleObject_Import);
        }

        [Fact]
        public async Task GetACLPropertiesByObjectId_ReturnsACLProperties_WhenExists()
        {
            // Arrange - First create an ACL Properties entry
            var createRequest = new CreateACLPropertiesRequest
            {
                Object_ID = 1002,
                RoleObject_Create = 1,
                RoleObject_Read = 1,
                RoleObject_Update = 0,
                RoleObject_Delete = 0,
                RoleObject_Print = 1,
                RoleObject_Export = 0,
                RoleObject_Import = 1
            };

            var json = JsonSerializer.Serialize(createRequest);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            await _client.PostAsync("/api/aclproperties", content);

            // Act
            var response = await _client.GetAsync("/api/aclproperties/1002");

            // Assert
            response.EnsureSuccessStatusCode();
            var responseContent = await response.Content.ReadAsStringAsync();
            var aclProperties = JsonSerializer.Deserialize<ACLPropertiesDto>(responseContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            Assert.NotNull(aclProperties);
            Assert.Equal(1002, aclProperties.Object_ID);
            Assert.Equal(1, aclProperties.RoleObject_Create);
            Assert.Equal(1, aclProperties.RoleObject_Read);
            Assert.Equal(0, aclProperties.RoleObject_Update);
            Assert.Equal(0, aclProperties.RoleObject_Delete);
            Assert.Equal(1, aclProperties.RoleObject_Print);
            Assert.Equal(0, aclProperties.RoleObject_Export);
            Assert.Equal(1, aclProperties.RoleObject_Import);
        }

        [Fact]
        public async Task GetACLPropertiesByObjectId_ReturnsNotFound_WhenDoesNotExist()
        {
            // Act
            var response = await _client.GetAsync("/api/aclproperties/9999");

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.NotFound, response.StatusCode);
        }

        [Fact]
        public async Task UpdateACLProperties_ReturnsUpdatedACLProperties_WhenExists()
        {
            // Arrange - First create an ACL Properties entry
            var createRequest = new CreateACLPropertiesRequest
            {
                Object_ID = 1003,
                RoleObject_Create = 0,
                RoleObject_Read = 1,
                RoleObject_Update = 0,
                RoleObject_Delete = 0,
                RoleObject_Print = 0,
                RoleObject_Export = 0,
                RoleObject_Import = 0
            };

            var createJson = JsonSerializer.Serialize(createRequest);
            var createContent = new StringContent(createJson, Encoding.UTF8, "application/json");
            await _client.PostAsync("/api/aclproperties", createContent);

            // Prepare update request
            var updateRequest = new UpdateACLPropertiesRequest
            {
                RoleObject_Create = 1,
                RoleObject_Delete = 1,
                RoleObject_Import = 1
            };

            var updateJson = JsonSerializer.Serialize(updateRequest);
            var updateContent = new StringContent(updateJson, Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PutAsync("/api/aclproperties/1003", updateContent);

            // Assert
            response.EnsureSuccessStatusCode();
            var responseContent = await response.Content.ReadAsStringAsync();
            var updatedACLProperties = JsonSerializer.Deserialize<ACLPropertiesDto>(responseContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            Assert.NotNull(updatedACLProperties);
            Assert.Equal(1003, updatedACLProperties.Object_ID);
            Assert.Equal(1, updatedACLProperties.RoleObject_Create); // Updated
            Assert.Equal(1, updatedACLProperties.RoleObject_Read); // Unchanged
            Assert.Equal(0, updatedACLProperties.RoleObject_Update); // Unchanged
            Assert.Equal(1, updatedACLProperties.RoleObject_Delete); // Updated
            Assert.Equal(0, updatedACLProperties.RoleObject_Print); // Unchanged
            Assert.Equal(0, updatedACLProperties.RoleObject_Export); // Unchanged
            Assert.Equal(1, updatedACLProperties.RoleObject_Import); // Updated
        }

        [Fact]
        public async Task DeleteACLProperties_ReturnsNoContent_WhenExists()
        {
            // Arrange - First create an ACL Properties entry
            var createRequest = new CreateACLPropertiesRequest
            {
                Object_ID = 1004,
                RoleObject_Create = 1,
                RoleObject_Read = 1,
                RoleObject_Update = 1,
                RoleObject_Delete = 1,
                RoleObject_Print = 1,
                RoleObject_Export = 1,
                RoleObject_Import = 1
            };

            var json = JsonSerializer.Serialize(createRequest);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            await _client.PostAsync("/api/aclproperties", content);

            // Act
            var response = await _client.DeleteAsync("/api/aclproperties/1004");

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.NoContent, response.StatusCode);

            // Verify deletion
            var getResponse = await _client.GetAsync("/api/aclproperties/1004");
            Assert.Equal(System.Net.HttpStatusCode.NotFound, getResponse.StatusCode);
        }

        [Fact]
        public async Task ExistsACLProperties_ReturnsTrue_WhenExists()
        {
            // Arrange - First create an ACL Properties entry
            var createRequest = new CreateACLPropertiesRequest
            {
                Object_ID = 1005,
                RoleObject_Create = 1,
                RoleObject_Read = 1,
                RoleObject_Update = 1,
                RoleObject_Delete = 1,
                RoleObject_Print = 1,
                RoleObject_Export = 1,
                RoleObject_Import = 1
            };

            var json = JsonSerializer.Serialize(createRequest);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            await _client.PostAsync("/api/aclproperties", content);

            // Act
            var response = await _client.GetAsync("/api/aclproperties/1005/exists");

            // Assert
            response.EnsureSuccessStatusCode();
            var responseContent = await response.Content.ReadAsStringAsync();
            var exists = JsonSerializer.Deserialize<bool>(responseContent);

            Assert.True(exists);
        }

        [Fact]
        public async Task ExistsACLProperties_ReturnsFalse_WhenDoesNotExist()
        {
            // Act
            var response = await _client.GetAsync("/api/aclproperties/9999/exists");

            // Assert
            response.EnsureSuccessStatusCode();
            var responseContent = await response.Content.ReadAsStringAsync();
            var exists = JsonSerializer.Deserialize<bool>(responseContent);

            Assert.False(exists);
        }
    }
}
