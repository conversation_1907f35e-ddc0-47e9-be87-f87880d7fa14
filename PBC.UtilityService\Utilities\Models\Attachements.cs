using System;

namespace PBC.UtilityService.Utilities.Models
{
    public class Attachements
    {
        public int ATTACHMENTDETAIL_ID { get; set; }
        public string FILE_NAME { get; set; }
        public string FILEDESCRIPTION { get; set; }
        public int UPLOADBY { get; set; }
        public DateTime UPLOADDATE { get; set; }
        public string Remarks { get; set; }
        public int COMPANY_ID { get; set; }
        public int OBJECTID { get; set; }
        public int TransactionID { get; set; }
        public int DETAIL_ID { get; set; }
        public string Tablename { get; set; }
        public string AttachmentIDS { get; set; }
        public string UPLOADDATESORT { get; set; }
        public bool IsActive { get; set; }
        public string UserName { get; set; }
        public int Upload { get; set; }
    }
}
