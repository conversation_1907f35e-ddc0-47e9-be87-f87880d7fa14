﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.Utilities.CoreCompanyCalenderMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreRosterMasterServices
    {

        #region ::: Select Roster /Mithun:::
        /// <summary>
        /// SelectPrefixSuffix
        /// </summary> 
        public static IActionResult Select(SelectRosterList SelectObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {
            int count = 0;
            int total = 0;
            var x = default(dynamic);
            int Company_ID = Convert.ToInt32(SelectObj.Company_ID);
            List<Roster> rosters = new List<Roster>();
            List<GNM_CompanyEmployee> employees = new List<GNM_CompanyEmployee>();
            List<GNM_RefMasterDetail> shifts = new List<GNM_RefMasterDetail>();
            IQueryable<Roster> IQRoster = null;
            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetAllRosterGridData", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            // Fetch Roster Data
                            while (reader.Read())
                            {
                                rosters.Add(new Roster
                                {
                                    Roster_ID = reader.GetInt32(reader.GetOrdinal("ROSTER_ID")),
                                    Employee_ID = reader.GetInt32(reader.GetOrdinal("EMPLOYEE_ID")),
                                    Shift_ID = reader.GetInt32(reader.GetOrdinal("SHIFT_ID")),
                                    FromDateStr = reader.GetDateTime(reader.GetOrdinal("FROM_DATE")),
                                    FromDate = reader.GetDateTime(reader.GetOrdinal("FROM_DATE")).ToString("dd-MMM-yyyy"),
                                    ToDateStr = reader.IsDBNull(reader.GetOrdinal("TO_DATE")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("TO_DATE")),
                                    ToDate = reader.IsDBNull(reader.GetOrdinal("TO_DATE")) ? string.Empty : reader.GetDateTime(reader.GetOrdinal("TO_DATE")).ToString("dd-MMM-yyyy"),
                                    Employee = reader.GetString(reader.GetOrdinal("Employee")),
                                    Shift = reader.GetString(reader.GetOrdinal("Shift"))
                                });
                            }
                            IQRoster = rosters.AsQueryable<Roster>();

                            if (_search)
                            {
                                Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                                IQRoster = IQRoster.FilterSearch<Roster>(filtersObj);
                            }
                            else if (advnce)
                            {
                                AdvanceFilter advnfilter = JObject.Parse(Query).ToObject<AdvanceFilter>();
                                if (advnfilter.rules.Count() > 0)
                                {
                                    IQRoster = IQRoster.AdvanceSearch<Roster>(advnfilter);
                                }
                            }


                            // Move to next result set (Active Employees)
                            reader.NextResult();

                            while (reader.Read())
                            {
                                employees.Add(new GNM_CompanyEmployee
                                {
                                    Company_Employee_ID = reader.GetInt32(reader.GetOrdinal("Company_Employee_ID")),
                                    Employee_ID = reader.GetString(reader.GetOrdinal("Employee_ID")),
                                    Company_Employee_Name = reader.GetString(reader.GetOrdinal("Company_Employee_Name"))
                                });
                            }

                            // Move to next result set (Active Shifts)
                            reader.NextResult();

                            while (reader.Read())
                            {
                                shifts.Add(new GNM_RefMasterDetail
                                {
                                    RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                    RefMasterDetail_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"))
                                });
                            }
                        }
                    }
                }

                string JsonEmployeeData = "-1:--" + CommonFunctionalities.GetResourceString(SelectObj.GeneralCulture.ToString(), "select").ToString() + "--;";
                foreach (var emp in employees)
                {
                    JsonEmployeeData += emp.Company_Employee_ID + ":" + emp.Employee_ID + " - " + emp.Company_Employee_Name + ";";
                }
                JsonEmployeeData = JsonEmployeeData.TrimEnd(';');

                string JsonShiftData = "-1:--" + CommonFunctionalities.GetResourceString(SelectObj.GeneralCulture.ToString(), "select").ToString() + "--;";
                foreach (var shift in shifts)
                {
                    JsonShiftData += shift.RefMasterDetail_ID + ":" + shift.RefMasterDetail_Name + ";";
                }
                JsonShiftData = JsonShiftData.TrimEnd(';');



                // Sorting 
                IQRoster = IQRoster.OrderByField(sidx, sord);
                // Session["RosterExport"] = IQRoster;
                count = IQRoster.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }
                var arr = from a in IQRoster
                          select new
                          {
                              Roster_ID = a.Roster_ID,
                              Employee = a.Employee,
                              Shift = a.Shift,
                              FromDate = Convert.ToDateTime(a.FromDate).ToString("dd-MMM-yyyy"),
                              ToDate = string.IsNullOrEmpty(a.ToDate) ? " " : Convert.ToDateTime(a.ToDate).ToString("dd-MMM-yyyy"),
                              Employee_ID = a.Employee_ID,
                              Shift_ID = a.Shift_ID,
                              ToDateStr = a.ToDateStr,
                              edit = "<a title='Edit' href='#' style='font-size: 13px;' id='" + a.Roster_ID + "' class='EditRoster' key='" + a.Roster_ID + "' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                              delete = "<input type='checkbox' key='" + a.Roster_ID + "' defaultchecked=''  id='chk" + a.Roster_ID + "' class='RosterDelete'/>",
                          };
                x = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = arr.ToList().Paginate(page, rows),
                    // filter = Request.Params["filters"],
                    // advanceFilter = Request.Params["Query"],
                    JsonEmployeeData,
                    JsonShiftData
                };
                //return Json(x, JsonRequestBehavior.AllowGet);
                return new JsonResult(x);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: InsertRoster /Mithun:::
        /// <summary>
        /// InsertRoster
        /// </summary>   
        public static IActionResult Insert(InsertRosterList InsertObj, string constring, int LogException)
        {
            dynamic dummy = null;
            var jsonResult = dummy;
            //GNM_User User = (GNM_User)Session["UserDetails"];
            int CompanyID = InsertObj.Company_ID;
            try
            {
                JObject jObj = JObject.Parse(InsertObj.data);
                int Rostercount = jObj["rows"].Count();
                for (int i = 0; i < Rostercount; i++)
                {
                    int Roster_ID = jObj["rows"].ElementAt(i)["ROSTER_ID"].ToObject<int>();
                    int Employee_ID = jObj["rows"].ElementAt(i)["EMPLOYEE_ID"].ToObject<int>();
                    int Shift_ID = jObj["rows"].ElementAt(i)["SHIFT_ID"].ToObject<int>();
                    DateTime FromDate = jObj["rows"].ElementAt(i)["FROM_DATE"].ToObject<DateTime>();
                    string ToDate = jObj["rows"].ElementAt(i)["TO_DATE"].ToString();

                    using (SqlConnection conn = new SqlConnection(constring))
                    {
                        using (SqlCommand cmd = new SqlCommand("Up_Ins_Upd_Am_Erp_SaveRosterDetails", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@ROSTER_ID", Roster_ID == 0 ? (object)DBNull.Value : Roster_ID);
                            cmd.Parameters.AddWithValue("@EMPLOYEE_ID", Employee_ID);
                            cmd.Parameters.AddWithValue("@SHIFT_ID", Shift_ID);
                            cmd.Parameters.AddWithValue("@FROM_DATE", FromDate);
                            cmd.Parameters.AddWithValue("@TO_DATE", string.IsNullOrEmpty(ToDate) ? (object)DBNull.Value : Convert.ToDateTime(ToDate));
                            cmd.Parameters.AddWithValue("@COMPANY_ID", CompanyID);
                            cmd.Parameters.Add("@BRANCH_ID", SqlDbType.Int).Direction = ParameterDirection.Output;

                            conn.Open();
                            cmd.ExecuteNonQuery();

                            int BranchID = (int)cmd.Parameters["@BRANCH_ID"].Value;
                            //    gbl.InsertGPSDetails(CompanyID, BranchID, userID, Common.GetObjectID("CoreRosterMaster",constring), Roster_ID, 0, 0, "Insert", false, Convert.ToInt32(InsertObj.MenuID));
                        }
                    }
                }
                return new JsonResult(new { success = true });
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { success = false });
            }
        }



        #endregion

        #region ::: DeleteRoster /Mithun:::
        /// <summary>
        /// to Delete the Parts
        /// </summary>
        public static IActionResult Delete(DeleteRosterList DeleteObj, string constring, int LogException)
        {
            string errorMsg = "";
            try
            {
                JObject jobj = JObject.Parse(DeleteObj.key);
                int rowCount = jobj["rows"].Count();
                int id = 0;

                for (int i = 0; i < rowCount; i++)
                {
                    id = Convert.ToInt32(jobj["rows"].ElementAt(i)["id"].ToString());

                    using (SqlConnection conn = new SqlConnection(constring))
                    {
                        string query = "DELETE FROM GNM_ROSTER WHERE ROSTER_ID = @ROSTER_ID";
                        SqlCommand cmd = new SqlCommand(query, conn);
                        cmd.Parameters.AddWithValue("@ROSTER_ID", id);

                        conn.Open();
                        try
                        {
                            cmd.ExecuteNonQuery();
                        }
                        catch (SqlException ex)
                        {
                            if (ex.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                            {
                                errorMsg = CommonFunctionalities.GetResourceString(DeleteObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                                break;
                            }
                            else
                            {
                                throw;
                            }
                        }
                    }
                }

                if (string.IsNullOrEmpty(errorMsg))
                {
                    //gbl.InsertGPSDetails(
                    //    Convert.ToInt32(DeleteObj.Company_ID),
                    //    Convert.ToInt32(DeleteObj.Branch),
                    //    Convert.ToInt32(DeleteObj.User_ID),
                    //    Convert.ToInt32(Common.GetObjectID("CoreRosterMaster",constring)),
                    //    id, 0, 0, "Delete", false, Convert.ToInt32(DeleteObj.MenuID)
                    //);

                    errorMsg = CommonFunctionalities.GetResourceString(DeleteObj.UserCulture.ToString(), "deletedsuccessfully").ToString();
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return errorMsg;
            return new JsonResult(errorMsg);
        }


        #endregion

        #region ::: Check Valid Date Format /Mithun:::
        public static IActionResult CheckValidDateFormat(CheckValidDateFormatRosterList CheckValidDateFormatObj)
        {
            var Result = "";
            var jsonresult = default(dynamic);
            try
            {
                DateTime temp = Convert.ToDateTime(CheckValidDateFormatObj.dateformat);
                Result = "Success";
            }
            catch (Exception e)
            {
                Result = "Fail";
            }
            jsonresult = new
            {
                Result = Result
            };
            //return Json(jsonresult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonresult);
        }
        #endregion

        #region ::: CheckCustomerToDate /Mithun:::
        /// <summary>
        /// To Check Customer From Date Validations
        /// </summary>
        public static IActionResult CheckEmployeeShiftToDate(CheckEmployeeShiftToDateList CheckEmployeeShiftToDateObj, string constring, int LogException)
        {
            int Count = 0;
            try
            {
                DateTime FDate = Convert.ToDateTime(CheckEmployeeShiftToDateObj.ShiftToDate);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    using (SqlCommand cmd = new SqlCommand("Up_Chk_Am_Erp_CheckEmployeeShiftToDate", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@ShiftToDate", CheckEmployeeShiftToDateObj.ShiftToDate);
                        cmd.Parameters.AddWithValue("@ShiftFromDate", CheckEmployeeShiftToDateObj.ShiftFromDate);
                        cmd.Parameters.AddWithValue("@EmployeeID", CheckEmployeeShiftToDateObj.EmployeeID);
                        cmd.Parameters.AddWithValue("@Roster_ID", CheckEmployeeShiftToDateObj.Roster_ID);

                        SqlParameter outputParam = new SqlParameter("@Count", SqlDbType.Int);
                        outputParam.Direction = ParameterDirection.Output;
                        cmd.Parameters.Add(outputParam);

                        conn.Open();
                        cmd.ExecuteNonQuery();

                        Count = (int)outputParam.Value;
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Count;
            return new JsonResult(Count);
        }

        #endregion

        #region ::: CheckCustomerFromDate /Mithun:::
        /// <summary>
        /// To Check Customer From Date Validations
        /// </summary>
        public static IActionResult CheckEmployeeShiftFromDate(CheckEmployeeShiftFromDateList CheckEmployeeShiftFromDateObj, string constring, int LogException)
        {
            int Count = 0;
            try
            {
                DateTime FDate = Convert.ToDateTime(CheckEmployeeShiftFromDateObj.ShiftToDate);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    using (SqlCommand cmd = new SqlCommand("Up_Chk_Am_Erp_CheckEmployeeShiftFromDate", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@ShiftToDate", CheckEmployeeShiftFromDateObj.ShiftToDate);
                        cmd.Parameters.AddWithValue("@ShiftFromDate", CheckEmployeeShiftFromDateObj.ShiftFromDate);
                        cmd.Parameters.AddWithValue("@EmployeeID", CheckEmployeeShiftFromDateObj.EmployeeID);
                        cmd.Parameters.AddWithValue("@Roster_ID", CheckEmployeeShiftFromDateObj.Roster_ID);

                        SqlParameter outputParam = new SqlParameter("@Count", SqlDbType.Int);
                        outputParam.Direction = ParameterDirection.Output;
                        cmd.Parameters.Add(outputParam);

                        conn.Open();
                        cmd.ExecuteNonQuery();

                        Count = (int)outputParam.Value;
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Count;
            return new JsonResult(Count);
        }


        #endregion


        #region::: Data for Export  :::

        public static IQueryable<Roster> GetRosterData(SelectRosterList ExportObj, string constring)
        {
            List<Roster> rosters = new List<Roster>();


            using (SqlConnection conn = new SqlConnection(constring))
            {
                conn.Open();

                using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetAllRosterGridData", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@Company_ID", ExportObj.Company_ID);

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        // Fetch roster data
                        while (reader.Read())
                        {
                            rosters.Add(new Roster
                            {
                                Roster_ID = reader.GetInt32(reader.GetOrdinal("ROSTER_ID")),
                                Employee_ID = reader.GetInt32(reader.GetOrdinal("EMPLOYEE_ID")),
                                Shift_ID = reader.GetInt32(reader.GetOrdinal("SHIFT_ID")),
                                FromDateStr = reader.GetDateTime(reader.GetOrdinal("FROM_DATE")),
                                FromDate = reader.GetDateTime(reader.GetOrdinal("FROM_DATE")).ToString("dd-MMM-yyyy"),
                                ToDateStr = reader.IsDBNull(reader.GetOrdinal("TO_DATE")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("TO_DATE")),
                                ToDate = reader.IsDBNull(reader.GetOrdinal("TO_DATE")) ? string.Empty : reader.GetDateTime(reader.GetOrdinal("TO_DATE")).ToString("dd-MMM-yyyy"),
                                Employee = reader.GetString(reader.GetOrdinal("Employee")),
                                Shift = reader.GetString(reader.GetOrdinal("Shift"))
                            });
                        }
                    }
                }
            }

            // Return as IQueryable
            return rosters.AsQueryable<Roster>();
        }


        #endregion


        #region ::: Export :::

        public static async Task<object> Export(SelectRosterList ExportObj, string conString, int LogException, string filters, string Query, string sidx, string sord)
        {
            DataTable dt = new DataTable();
            try
            {
                // Fetching roster data from the database
                IQueryable<Roster> arrRoster = GetRosterData(ExportObj, conString);

                if (filters != "null" && filters != "undefined")
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    arrRoster = arrRoster.FilterSearch<Roster>(filtersObj);
                }
                else if (Query != "null" && Query != "undefined")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Query).ToObject<AdvanceFilter>();
                    if (advnfilter.rules.Count() > 0)
                    {
                        arrRoster = arrRoster.AdvanceSearch<Roster>(advnfilter);
                    }
                }
                arrRoster = arrRoster.OrderByField(sidx, sord);

                // Adding the required columns to the DataTable
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "Employee").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "Shift").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "FromDate").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "ToDate").ToString());

                // Loop through the roster data and populate the DataTable
                foreach (var roster in arrRoster)
                {
                    dt.Rows.Add(roster.Employee, roster.Shift, roster.FromDate, roster.ToDate);
                }

                // Creating a DataTable for alignment
                DataTable dtAlignment = new DataTable();
                dtAlignment.Columns.Add("Employee");
                dtAlignment.Columns.Add("Shift");
                dtAlignment.Columns.Add("FromDate");
                dtAlignment.Columns.Add("ToDate");
                dtAlignment.Rows.Add(0, 0, 0, 0);

                ExportList reportExportList = new ExportList
                {
                    Company_ID = ExportObj.Company_ID, // Assuming this is available in ExportObj
                    Branch = ExportObj.Branch,
                    dt1 = dt,


                    dt = dt,

                    FileName = "MovementTypeDefinition", // Set a default or dynamic filename
                    Header = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "MovementTypeDefinition").ToString(), // Set a default or dynamic header
                    exprtType = ExportObj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                    UserCulture = ExportObj.UserCulture
                };


                var Result = await DocumentExport.Export(reportExportList, conString, LogException);
                return Result.Value;


                // Exporting the data
                // DocumentExport.Export(exprtType, dt, dtAlignment, "Roster", CommonFunctionalities.GetResourceString(Session["GeneralCulture"].ToString(), "Roster").ToString());
            }
            catch (Exception ex)
            {
                // Handle and log the exception if necessary
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return null;
        }

        #endregion








        public class CheckValidDateFormatRosterList
        {
            public string dateformat { get; set; }
        }
        public class CheckEmployeeShiftFromDateList
        {
            public string ShiftToDate { get; set; }
            public string ShiftFromDate { get; set; }
            public int EmployeeID { get; set; }
            public int Roster_ID { get; set; }
        }

        public class CheckEmployeeShiftToDateList
        {
            public string ShiftToDate { get; set; }
            public string ShiftFromDate { get; set; }
            public int EmployeeID { get; set; }
            public int Roster_ID { get; set; }
        }

        public class DeleteRosterList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public string key { get; set; }
            public string UserCulture { get; set; }
        }


        public class InsertRosterList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public string data { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }


        public class SelectRosterList
        {
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int exprtType { get; set; }
            public string GeneralCulture { get; set; }
            public string UserCulture { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filters { get; set; }
            public string Query { get; set; }
        }

        public class Roster
        {
            public int Roster_ID { get; set; }
            public int Employee_ID { get; set; }
            public int Shift_ID { get; set; }
            public string Employee { get; set; }
            public string Shift { get; set; }
            public string FromDate { get; set; }
            public DateTime FromDateStr { get; set; }
            public string ToDate { get; set; }
            public DateTime? ToDateStr { get; set; }
        }
    }
}
