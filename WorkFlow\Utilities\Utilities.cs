﻿
using WorkFlow.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Threading;
using System.Resources;
using System.Globalization;
using System.Text;
using System.Security.Cryptography;
using System.Net;

namespace WorkFlow.Utilities
{
    public class Utilities
    {
        #region
        /// <summary>
        /// Calculate Total Pages
        /// </summary>
        /// <param name="numberOfRecords"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public static int CalculateTotalPages(long numberOfRecords, Int32 pageSize)
        {
            long result;
            int totalPages;

            Math.DivRem(numberOfRecords, pageSize, out result);

            if (result > 0)
                totalPages = (int)((numberOfRecords / pageSize)) + 1;
            else
                totalPages = (int)(numberOfRecords / pageSize);

            return totalPages;
        }
         #endregion

        #region
        /// <summary>
        /// Check if date is a valid format
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        /// 
        public static Boolean IsDate(string date)
        {
            DateTime dateTime;
            return DateTime.TryParse(date, out dateTime);
        }
         #endregion

        #region
        /// <summary>
        /// IsNumeric
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public static Boolean IsNumeric(object entity)
        {
            if (entity == null) return false;

            int result;
            return int.TryParse(entity.ToString(), out result);
        }
         #endregion

        #region
        /// <summary>
        /// IsDouble
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public static Boolean IsDouble(object entity)
        {
            if (entity == null) return false;

            string e = entity.ToString();

            // Loop through all instances of the string 'text'.
            int count = 0;
            int i = 0;
            while ((i = e.IndexOf(".", i)) != -1)
            {
                i += ".".Length;
                count++;
            }
            if (count > 1) return false;

            e = e.Replace(".", "");

            int result;
            return int.TryParse(e, out result);
        }
         #endregion

        #region
        public static List<String> Message(string message)
        {
            List<String> returnMessage = new List<String>();
            returnMessage.Add(message);
            return returnMessage;
        }
         #endregion     

        #region 

        public string CalculateMD5Hash(string input)
        {
            // step 1, calculate MD5 hash from input
            MD5 md5 = System.Security.Cryptography.MD5.Create();
            byte[] inputBytes = System.Text.Encoding.ASCII.GetBytes(input);
            byte[] hash = md5.ComputeHash(inputBytes);

            // step 2, convert byte array to hex string
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < hash.Length; i++)
            {
                sb.Append(hash[i].ToString("x2"));
            }
            return sb.ToString();
        }
        #endregion

        #region ::: GetMonthName:::
        /// <summary>
        /// To Select Month Name based on Culture
        /// </summary>
        public static string GetMonthName(int ID)
        {
            string MonthName = string.Empty;
            try
            {
                switch (ID)
                {
                    case 1:
                        MonthName = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "January").ToString();
                        break;
                    case 2:
                        MonthName = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "February").ToString();
                        break;
                    case 3:
                        MonthName = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "March").ToString();
                        break;
                    case 4:
                        MonthName = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "April").ToString();
                        break;
                    case 5:
                        MonthName = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "May").ToString();
                        break;
                    case 6:
                        MonthName = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "June").ToString();
                        break;
                    case 7:
                        MonthName = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "July").ToString();
                        break;
                    case 8:
                        MonthName = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "August").ToString();
                        break;
                    case 9:
                        MonthName = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "September").ToString();
                        break;
                    case 10:
                        MonthName = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "October").ToString();
                        break;
                    case 11:
                        MonthName = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "November").ToString();
                        break;
                    case 12:
                        MonthName = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "December").ToString();
                        break;
                    default:
                        MonthName = string.Empty;
                        break;
                }
            }
            catch (Exception ex)
            {
                
            }
            return MonthName;
        }
        #endregion

        #region ::: GetPariority:::
        /// <summary>
        /// To Select Pariority based on Culture
        /// </summary>
        public static string GetPariority(byte ID)
        {
            string Priority = string.Empty;

            try
            {
                switch (ID)
                {
                    case 1:
                        Priority = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "low").ToString();
                        break;
                    case 2:
                        Priority = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "medium").ToString();
                        break;
                    case 3:
                        Priority = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "high").ToString();
                        break;
                    case 4:
                        Priority = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "critical").ToString();
                        break;
                    default:
                        Priority = string.Empty;
                        break;
                }
            }
            catch (Exception ex)
            {
                
            }
            return Priority;
        }
        #endregion
     
    }
}