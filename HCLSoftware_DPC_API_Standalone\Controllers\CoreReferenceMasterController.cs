﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreReferenceMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreReferenceMasterController : ApiController
    {
        #region ::: Landing Grid /Mithun:::
        /// <summary>
        ///  Landing Grid
        /// </summary>
        /// <returns></returns>
        [Route("api/CoreReferenceMaster/SelectReferenceMaster")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectReferenceMaster([FromBody] SelectCoreReferenceMasterList SelectReferenceMasterObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreReferenceMasterServices.SelectReferenceMaster(SelectReferenceMasterObj, Conn, LogException, sidx, sord, page, rows, _search, advnce, filters, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: SaveMasterDetail /Mithun:::
        /// <summary>
        /// Save Master Detail
        /// </summary>
        /// <returns></returns>
        [Route("api/CoreReferenceMaster/SaveMasterDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveMasterDetail([FromBody] SaveMasterDetailList SaveMasterDetailObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreReferenceMasterServices.SaveMasterDetail(SaveMasterDetailObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Detail Grid /Mithun:::
        /// <summary>
        /// Detail Grid
        /// </summary>
        /// <returns></returns>
        [Route("api/CoreReferenceMaster/MasterDetailData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult MasterDetailData([FromBody] MasterDetailDataList MasterDetailDataObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreReferenceMasterServices.MasterDetailData(MasterDetailDataObj, Conn, LogException, sidx, sord, page, rows, _search, advnce, filters, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: MasterDetailLocaleData /Mithun:::
        /// <summary>
        /// Master Detail Locale Data
        /// </summary>
        /// <returns></returns>
        [Route("api/CoreReferenceMaster/MasterDetailLocaleData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult MasterDetailLocaleData([FromBody] MasterDetailLocaleDataList MasterDetailLocaleDataObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string Query = (HttpContext.Current.Request.Params["Query"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreReferenceMasterServices.MasterDetailLocaleData(MasterDetailLocaleDataObj, Conn, LogException, sidx, sord, page, rows, _search, advnce, filters, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: MasterDetailLocale /Mithun:::
        /// <summary>
        /// MasterDetailLocale
        /// </summary>
        /// <returns></returns>
        [Route("api/CoreReferenceMaster/MasterDetailLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult MasterDetailLocale([FromBody] MasterDetailLocaleList MasterDetailLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreReferenceMasterServices.MasterDetailLocale(MasterDetailLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: LocaleUpdate /Mithun:::
        /// <summary>
        /// Locale Update
        /// </summary>
        [Route("api/CoreReferenceMaster/LocaleUpdate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LocaleUpdate([FromBody] LocaleUpdateList LocaleUpdateObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreReferenceMasterServices.LocaleUpdate(LocaleUpdateObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: DeleteReferenceMasterDetail /Mithun:::
        /// <summary>
        /// Delete Reference MasterDetail
        /// </summary>
        /// <returns></returns>
        [Route("api/CoreReferenceMaster/DeleteReferenceMasterDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteReferenceMasterDetail([FromBody] DeleteReferenceMasterDetailList DeleteReferenceMasterDetailObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreReferenceMasterServices.DeleteReferenceMasterDetail(DeleteReferenceMasterDetailObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Delete Reference Master /Mithun:::
        /// <summary>
        ///  Delete Reference Master
        /// </summary>
        /// <returns></returns>
        [Route("api/CoreReferenceMaster/DeleteReferenceMaster")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteReferenceMaster([FromBody] DeleteReferenceMasterList DeleteReferenceMasterObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreReferenceMasterServices.DeleteReferenceMaster(DeleteReferenceMasterObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion

        #region ::: LocaleDelete /Mithun :::
        /// <summary>
        /// LocaleDelete
        /// </summary>
        /// <param name="id"></param>
        [Route("api/CoreReferenceMaster/LocaleDelete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LocaleDelete([FromBody] LocaleDeleteList LocaleDeleteObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreReferenceMasterServices.LocaleDelete(LocaleDeleteObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Check Master Exists /Mithun :::
        /// <summary>
        /// Check Master Exists
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        [Route("api/CoreReferenceMaster/CheckMasterExists")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckMasterExists([FromBody] CheckMasterExistsList CheckMasterExistsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreReferenceMasterServices.CheckMasterExists(CheckMasterExistsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::  Check If Description Exists /Mithun:::
        /// <summary>
        ///  Check If Description Exists
        /// </summary>
        /// <returns></returns>
        [Route("api/CoreReferenceMaster/CheckDescriptionExists")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckDescriptionExists([FromBody] CheckDescriptionExistsList CheckDescriptionExistsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreReferenceMasterServices.CheckDescriptionExists(CheckDescriptionExistsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CheckLocaleExists /Mithun:::
        /// <summary>
        /// CheckLocaleExists
        /// </summary>
        [Route("api/CoreReferenceMaster/CheckLocaleExists")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckLocaleExists([FromBody] CheckLocaleExistsList CheckLocaleExistsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreReferenceMasterServices.CheckLocaleExists(CheckLocaleExistsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion




        #region::: Export /Mithun :::

        [Route("api/CoreReferenceMaster/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] SelectCoreReferenceMasterList ExportObj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportObj.sidx;
            string sord = ExportObj.sord;
            string filters = ExportObj.filters;
            string Query = ExportObj.Query;
            bool _search = ExportObj._search;
            try
            {


                Object Response = await CoreReferenceMasterServices.Export(ExportObj, connstring, LogException, filters, Query, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }

        #endregion





    }
}
