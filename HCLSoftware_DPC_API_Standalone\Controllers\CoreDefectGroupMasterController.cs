﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreDefectGroupMasterController : ApiController
    {
        #region::: Select Reference Master /Mithun:::
        /// <summary>
        /// To select Reference Master
        /// </summary>
        /// <param name="SelectReferenceMasterObj"></param>
        /// <returns></returns>
        [Route("api/CoreDefectGroupMaster/SelectReferenceMaster")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectReferenceMaster([FromBody] SelectDefectGroupReferenceMasterList SelectDefectGroupReferenceMasterObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreDefectGroupMasterServices.SelectReferenceMaster(SelectDefectGroupReferenceMasterObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region :::Loading Defect Group English Grid /Mithun:::
        /// <summary>
        /// Loading Defect Group English Grid
        /// </summary>
        [Route("api/CoreDefectGroupMaster/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] SelectDefectGroupList SelectObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreDefectGroupMasterServices.Select(SelectObj, Conn, LogException, sidx, sord, page, rows, _search, filters, Query, advnce);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
            return Ok(Response.Value);
        }
        #endregion

        #region :::SelectParticularDefectGroup /Mithun:::
        /// <summary>
        /// To Select Particular DefectGroup
        /// </summary>
        [Route("api/CoreDefectGroupMaster/SelectParticularDefectGroup")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularDefectGroup([FromBody] SelectParticularDefectGroupList SelectParticularDefectGroupObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreDefectGroupMasterServices.SelectParticularDefectGroup(SelectParticularDefectGroupObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region :::Save /Mithun:::
        /// <summary>
        /// To save
        /// </summary>
        [Route("api/CoreDefectGroupMaster/Save")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Save([FromBody] SaveDefectGroupList SaveDefectGroupObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreDefectGroupMasterServices.Save(SaveDefectGroupObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region :::UpdateLocale /Mithun:::
        /// <summary>
        /// To Update Local
        /// </summary>

        [Route("api/CoreDefectGroupMaster/UpdateLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateLocale([FromBody] UpdateLocaleDefectGroupList UpdateLocaleDefectGroupObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreDefectGroupMasterServices.UpdateLocale(UpdateLocaleDefectGroupObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region :::Delete /Mithun:::
        /// <summary>
        /// Delete
        /// </summary>
        [Route("api/CoreDefectGroupMaster/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteDefectGroupList DeleteDefectGroupObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreDefectGroupMasterServices.Delete(DeleteDefectGroupObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region :::CheckDefectGroup /Mithun:::
        /// <summary>
        /// To Check Defect Group
        /// </summary>
        [Route("api/CoreDefectGroupMaster/CheckDefectGroup")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckDefectGroup([FromBody] CheckDefectGroupList CheckDefectGroupObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreDefectGroupMasterServices.CheckDefectGroup(CheckDefectGroupObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region :::CheckDefectGroupCode /Mithun:::
        /// <summary>
        /// To Check Defect Group Code
        /// </summary>
        [Route("api/CoreDefectGroupMaster/CheckDefectGroupCode")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckDefectGroupCode([FromBody] CheckDefectGroupCodeList CheckDefectGroupCodeObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreDefectGroupMasterServices.CheckDefectGroupCode(CheckDefectGroupCodeObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region :::CheckDefectGroupNameLocale /Mithun:::
        /// <summary>
        /// To Check Defect Group Name Locale
        /// </summary>
        [Route("api/CoreDefectGroupMaster/CheckDefectGroupNameLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckDefectGroupNameLocale([FromBody] CheckDefectGroupNameLocaleList CheckDefectGroupNameLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreDefectGroupMasterServices.CheckDefectGroupNameLocale(CheckDefectGroupNameLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region::: Exprot /Mithun:::

        [Route("api/CoreDefectGroupMaster/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] SelectDefectGroupList ExportObj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportObj.sidx;
            string sord = ExportObj.sord;
            string filters = ExportObj.filters;
            string Query = ExportObj.Query;

            try
            {


                object Response = await CoreDefectGroupMasterServices.Export(ExportObj, connstring, LogException, filters, Query, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }

        #endregion
    }

}