﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="3.5.0.2" targetFramework="net472" />
  <package id="AWSSDK.Core" version="3.7.302.3" targetFramework="net472" />
  <package id="AWSSDK.S3" version="3.7.305.19" targetFramework="net472" />
  <package id="Azure.Core" version="1.24.0" targetFramework="net472" />
  <package id="Azure.Identity" version="1.6.0" targetFramework="net472" />
  <package id="Azure.Storage.Blobs" version="12.10.0" targetFramework="net472" />
  <package id="Azure.Storage.Common" version="12.9.0" targetFramework="net472" />
  <package id="bootstrap" version="3.4.1" targetFramework="net472" />
  <package id="BouncyCastle" version="1.8.9" targetFramework="net472" />
  <package id="ClosedXML" version="0.102.1" targetFramework="net472" />
  <package id="DocumentFormat.OpenXml" version="2.16.0" targetFramework="net472" />
  <package id="EPPlus" version="7.4.1" targetFramework="net472" />
  <package id="EPPlus.Interfaces" version="6.1.1" targetFramework="net472" />
  <package id="EPPlus.System.Drawing" version="6.1.1" targetFramework="net472" />
  <package id="ExcelNumberFormat" version="1.1.0" targetFramework="net472" />
  <package id="FastMember" version="1.5.0" targetFramework="net472" />
  <package id="Irony.NetCore" version="1.0.11" targetFramework="net472" />
  <package id="iTextSharp" version="5.5.13.3" targetFramework="net472" />
  <package id="jQuery" version="3.4.1" targetFramework="net472" />
  <package id="Microsoft.AspNet.Cors" version="5.3.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.Mvc" version="5.3.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.Razor" version="3.3.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi" version="5.3.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Client" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.3.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Cors" version="5.3.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.HelpPage" version="5.3.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.3.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebPages" version="3.3.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Antiforgery" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Authentication.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Authentication.Core" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Authorization" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Authorization.Policy" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Cors" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Cryptography.Internal" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.DataProtection" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.DataProtection.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Diagnostics.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Hosting.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Hosting.Server.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Html.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Http" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Http.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Http.Extensions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Http.Features" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.JsonPatch" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Localization" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Mvc" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Mvc.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Mvc.Analyzers" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Mvc.ApiExplorer" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Mvc.Core" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Mvc.Cors" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Mvc.DataAnnotations" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Mvc.Formatters.Json" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Mvc.Localization" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Mvc.Razor" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Mvc.Razor.Extensions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Mvc.RazorPages" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Mvc.TagHelpers" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Mvc.ViewFeatures" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Razor" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Razor.Design" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Razor.Language" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Razor.Runtime" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.ResponseCaching.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Routing" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Routing.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.WebUtilities" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.CodeAnalysis.Analyzers" version="1.1.0" targetFramework="net472" />
  <package id="Microsoft.CodeAnalysis.Common" version="2.8.0" targetFramework="net472" />
  <package id="Microsoft.CodeAnalysis.CSharp" version="2.8.0" targetFramework="net472" />
  <package id="Microsoft.CodeAnalysis.Razor" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="2.0.1" targetFramework="net472" />
  <package id="Microsoft.CSharp" version="4.5.0" targetFramework="net472" />
  <package id="Microsoft.Data.SqlClient" version="5.0.0" targetFramework="net472" />
  <package id="Microsoft.Data.SqlClient.SNI" version="5.0.0" targetFramework="net472" />
  <package id="Microsoft.DiaSymReader.Native" version="1.7.0" targetFramework="net472" />
  <package id="Microsoft.DotNet.PlatformAbstractions" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Caching.Abstractions" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Caching.Memory" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyModel" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.FileProviders.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.FileProviders.Composite" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.FileSystemGlobbing" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Hosting.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Localization" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Localization.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.ObjectPool" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Options" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Primitives" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.WebEncoders" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Identity.Client" version="4.45.0" targetFramework="net472" />
  <package id="Microsoft.Identity.Client.Extensions.Msal" version="2.19.3" targetFramework="net472" />
  <package id="Microsoft.IdentityModel.Abstractions" version="7.0.3" targetFramework="net472" />
  <package id="Microsoft.IdentityModel.JsonWebTokens" version="7.0.3" targetFramework="net472" />
  <package id="Microsoft.IdentityModel.Logging" version="7.0.3" targetFramework="net472" />
  <package id="Microsoft.IdentityModel.Protocols" version="6.21.0" targetFramework="net472" />
  <package id="Microsoft.IdentityModel.Protocols.OpenIdConnect" version="6.21.0" targetFramework="net472" />
  <package id="Microsoft.IdentityModel.Tokens" version="7.0.3" targetFramework="net472" />
  <package id="Microsoft.IO.RecyclableMemoryStream" version="3.0.1" targetFramework="net472" />
  <package id="Microsoft.Net.Http.Headers" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Owin" version="4.2.2" targetFramework="net472" />
  <package id="Microsoft.Owin.Security" version="4.2.2" targetFramework="net472" />
  <package id="Microsoft.Owin.Security.Jwt" version="4.2.2" targetFramework="net472" />
  <package id="Microsoft.Owin.Security.OAuth" version="4.2.2" targetFramework="net472" />
  <package id="Microsoft.Web.Infrastructure" version="1.0.0.0" targetFramework="net472" />
  <package id="Microsoft.Win32.Registry" version="4.5.0" targetFramework="net472" />
  <package id="Modernizr" version="2.8.3" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net472" />
  <package id="Newtonsoft.Json.Bson" version="1.0.2" targetFramework="net472" />
  <package id="Owin" version="1.0" targetFramework="net472" />
  <package id="PuppeteerSharp" version="20.0.2" targetFramework="net472" />
  <package id="SixLabors.Fonts" version="1.0.0" targetFramework="net472" />
  <package id="System.AppContext" version="4.3.0" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.Collections" version="4.3.0" targetFramework="net472" />
  <package id="System.Collections.Concurrent" version="4.3.0" targetFramework="net472" />
  <package id="System.Collections.Immutable" version="1.3.1" targetFramework="net472" />
  <package id="System.ComponentModel.Annotations" version="5.0.0" targetFramework="net472" />
  <package id="System.Configuration.ConfigurationManager" version="5.0.0" targetFramework="net472" />
  <package id="System.Console" version="4.3.0" targetFramework="net472" />
  <package id="System.Data.SqlClient" version="4.8.6" targetFramework="net472" />
  <package id="System.Diagnostics.Debug" version="4.3.0" targetFramework="net472" />
  <package id="System.Diagnostics.DiagnosticSource" version="8.0.0" targetFramework="net472" />
  <package id="System.Diagnostics.FileVersionInfo" version="4.3.0" targetFramework="net472" />
  <package id="System.Diagnostics.StackTrace" version="4.3.0" targetFramework="net472" />
  <package id="System.Diagnostics.Tools" version="4.3.0" targetFramework="net472" />
  <package id="System.Dynamic.Runtime" version="4.3.0" targetFramework="net472" />
  <package id="System.Globalization" version="4.3.0" targetFramework="net472" />
  <package id="System.IdentityModel.Tokens.Jwt" version="7.0.3" targetFramework="net472" />
  <package id="System.IO" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.Compression" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.FileSystem" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.FileSystem.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.Packaging" version="6.0.0" targetFramework="net472" />
  <package id="System.Linq" version="4.3.0" targetFramework="net472" />
  <package id="System.Linq.Expressions" version="4.3.0" targetFramework="net472" />
  <package id="System.Memory" version="4.5.5" targetFramework="net472" />
  <package id="System.Memory.Data" version="1.0.2" targetFramework="net472" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Reflection" version="4.3.0" targetFramework="net472" />
  <package id="System.Reflection.Metadata" version="1.4.2" targetFramework="net472" />
  <package id="System.Resources.ResourceManager" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net472" />
  <package id="System.Runtime.Extensions" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime.InteropServices" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime.Numerics" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.AccessControl" version="5.0.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.1" targetFramework="net472" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.ProtectedData" version="4.7.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Xml" version="4.5.0" targetFramework="net472" />
  <package id="System.Security.Permissions" version="5.0.0" targetFramework="net472" />
  <package id="System.Security.Principal.Windows" version="5.0.0" targetFramework="net472" />
  <package id="System.Text.Encoding" version="4.3.0" targetFramework="net472" />
  <package id="System.Text.Encoding.CodePages" version="4.3.0" targetFramework="net472" />
  <package id="System.Text.Encoding.Extensions" version="4.3.0" targetFramework="net472" />
  <package id="System.Text.Encodings.Web" version="8.0.0" targetFramework="net472" />
  <package id="System.Text.Json" version="8.0.4" targetFramework="net472" />
  <package id="System.Text.RegularExpressions" version="4.3.1" targetFramework="net472" />
  <package id="System.Threading" version="4.3.0" targetFramework="net472" />
  <package id="System.Threading.Tasks" version="4.3.0" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
  <package id="System.Threading.Tasks.Parallel" version="4.3.0" targetFramework="net472" />
  <package id="System.Threading.Thread" version="4.3.0" targetFramework="net472" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net472" />
  <package id="System.Xml.ReaderWriter" version="4.3.0" targetFramework="net472" />
  <package id="System.Xml.XDocument" version="4.3.0" targetFramework="net472" />
  <package id="System.Xml.XmlDocument" version="4.3.0" targetFramework="net472" />
  <package id="System.Xml.XPath" version="4.3.0" targetFramework="net472" />
  <package id="System.Xml.XPath.XDocument" version="4.3.0" targetFramework="net472" />
  <package id="WebGrease" version="1.6.0" targetFramework="net472" />
  <package id="XLParser" version="1.5.2" targetFramework="net472" />
</packages>