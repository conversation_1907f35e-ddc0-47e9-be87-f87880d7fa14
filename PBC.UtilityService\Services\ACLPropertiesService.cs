using PBC.UtilityService.Utilities.Models;
using PBC.UtilityService.Utilities.DTOs;

namespace PBC.UtilityService.Services
{
    /// <summary>
    /// Service implementation for ACL Properties operations
    /// </summary>
    public class ACLPropertiesService : IACLPropertiesService
    {
        private readonly ILogger<ACLPropertiesService> _logger;
        
        // In-memory storage for demonstration - replace with actual data persistence layer
        private static readonly List<ACLProperties> _aclPropertiesStore = new();
        private static readonly object _lock = new();

        public ACLPropertiesService(ILogger<ACLPropertiesService> logger)
        {
            _logger = logger;
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<ACLPropertiesDto>> GetAllAsync()
        {
            _logger.LogInformation("Getting all ACL Properties");
            
            await Task.Delay(1); // Simulate async operation
            
            lock (_lock)
            {
                return _aclPropertiesStore.Select(MapToDto).ToList();
            }
        }

        /// <inheritdoc/>
        public async Task<ACLPropertiesDto?> GetByObjectIdAsync(int objectId)
        {
            _logger.LogInformation("Getting ACL Properties for Object ID: {ObjectId}", objectId);
            
            await Task.Delay(1); // Simulate async operation
            
            lock (_lock)
            {
                var aclProperties = _aclPropertiesStore.FirstOrDefault(x => x.Object_ID == objectId);
                return aclProperties != null ? MapToDto(aclProperties) : null;
            }
        }

        /// <inheritdoc/>
        public async Task<ACLPropertiesDto> CreateAsync(CreateACLPropertiesRequest request)
        {
            _logger.LogInformation("Creating ACL Properties for Object ID: {ObjectId}", request.Object_ID);
            
            await Task.Delay(1); // Simulate async operation
            
            var aclProperties = new ACLProperties
            {
                Object_ID = request.Object_ID,
                RoleObject_Create = request.RoleObject_Create,
                RoleObject_Read = request.RoleObject_Read,
                RoleObject_Update = request.RoleObject_Update,
                RoleObject_Delete = request.RoleObject_Delete,
                RoleObject_Print = request.RoleObject_Print,
                RoleObject_Export = request.RoleObject_Export,
                RoleObject_Import = request.RoleObject_Import
            };

            lock (_lock)
            {
                // Check if already exists
                if (_aclPropertiesStore.Any(x => x.Object_ID == request.Object_ID))
                {
                    throw new InvalidOperationException($"ACL Properties for Object ID {request.Object_ID} already exists");
                }
                
                _aclPropertiesStore.Add(aclProperties);
            }

            _logger.LogInformation("Successfully created ACL Properties for Object ID: {ObjectId}", request.Object_ID);
            return MapToDto(aclProperties);
        }

        /// <inheritdoc/>
        public async Task<ACLPropertiesDto?> UpdateAsync(int objectId, UpdateACLPropertiesRequest request)
        {
            _logger.LogInformation("Updating ACL Properties for Object ID: {ObjectId}", objectId);
            
            await Task.Delay(1); // Simulate async operation
            
            lock (_lock)
            {
                var aclProperties = _aclPropertiesStore.FirstOrDefault(x => x.Object_ID == objectId);
                if (aclProperties == null)
                {
                    _logger.LogWarning("ACL Properties not found for Object ID: {ObjectId}", objectId);
                    return null;
                }

                // Update only provided values
                if (request.RoleObject_Create.HasValue)
                    aclProperties.RoleObject_Create = request.RoleObject_Create.Value;
                if (request.RoleObject_Read.HasValue)
                    aclProperties.RoleObject_Read = request.RoleObject_Read.Value;
                if (request.RoleObject_Update.HasValue)
                    aclProperties.RoleObject_Update = request.RoleObject_Update.Value;
                if (request.RoleObject_Delete.HasValue)
                    aclProperties.RoleObject_Delete = request.RoleObject_Delete.Value;
                if (request.RoleObject_Print.HasValue)
                    aclProperties.RoleObject_Print = request.RoleObject_Print.Value;
                if (request.RoleObject_Export.HasValue)
                    aclProperties.RoleObject_Export = request.RoleObject_Export.Value;
                if (request.RoleObject_Import.HasValue)
                    aclProperties.RoleObject_Import = request.RoleObject_Import.Value;

                _logger.LogInformation("Successfully updated ACL Properties for Object ID: {ObjectId}", objectId);
                return MapToDto(aclProperties);
            }
        }

        /// <inheritdoc/>
        public async Task<bool> DeleteAsync(int objectId)
        {
            _logger.LogInformation("Deleting ACL Properties for Object ID: {ObjectId}", objectId);
            
            await Task.Delay(1); // Simulate async operation
            
            lock (_lock)
            {
                var aclProperties = _aclPropertiesStore.FirstOrDefault(x => x.Object_ID == objectId);
                if (aclProperties == null)
                {
                    _logger.LogWarning("ACL Properties not found for Object ID: {ObjectId}", objectId);
                    return false;
                }

                _aclPropertiesStore.Remove(aclProperties);
                _logger.LogInformation("Successfully deleted ACL Properties for Object ID: {ObjectId}", objectId);
                return true;
            }
        }

        /// <inheritdoc/>
        public async Task<bool> ExistsAsync(int objectId)
        {
            _logger.LogInformation("Checking if ACL Properties exists for Object ID: {ObjectId}", objectId);
            
            await Task.Delay(1); // Simulate async operation
            
            lock (_lock)
            {
                return _aclPropertiesStore.Any(x => x.Object_ID == objectId);
            }
        }

        /// <summary>
        /// Maps ACLProperties model to DTO
        /// </summary>
        private static ACLPropertiesDto MapToDto(ACLProperties aclProperties)
        {
            return new ACLPropertiesDto
            {
                Object_ID = aclProperties.Object_ID,
                RoleObject_Create = aclProperties.RoleObject_Create,
                RoleObject_Read = aclProperties.RoleObject_Read,
                RoleObject_Update = aclProperties.RoleObject_Update,
                RoleObject_Delete = aclProperties.RoleObject_Delete,
                RoleObject_Print = aclProperties.RoleObject_Print,
                RoleObject_Export = aclProperties.RoleObject_Export,
                RoleObject_Import = aclProperties.RoleObject_Import
            };
        }
    }
}
