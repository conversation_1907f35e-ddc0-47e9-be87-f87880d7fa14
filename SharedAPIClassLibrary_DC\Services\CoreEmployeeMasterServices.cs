﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.CoreProductMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreEmployeeMasterServices
    {
        public static string BucketFilePath = "quest-partsassist\\EPC_UploadedFiles";

        #region ::: SelectAllEmployeeDetails Uday Kumar J B 27-08-2024:::
        /// <summary>
        /// to select all the employees of the company
        /// </summary> 
        /// 

        public static IActionResult SelectAllEmployeeDetails(string connString, SelectAllEmployeeDetailsList SelectAllEmployeeDetailsobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int count = 0;
                int total = 0;
                string AppPath = string.Empty;
                // GNM_User User = SelectAllEmployeeDetailsobj.UserDetails.FirstOrDefault();
                int companyID = SelectAllEmployeeDetailsobj.Company_ID;
                int userLanguageID = Convert.ToInt32(SelectAllEmployeeDetailsobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectAllEmployeeDetailsobj.GeneralLanguageID);

                List<EmployeeData> employeeDataList = new List<EmployeeData>();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    SqlCommand cmd = new SqlCommand("Up_SEL_GetEmployeeDetails", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@CompanyID", companyID);
                    cmd.Parameters.AddWithValue("@LanguageID", SelectAllEmployeeDetailsobj.langaugeID);
                    cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                    conn.Open();
                    SqlDataReader reader = cmd.ExecuteReader();
                    while (reader.Read())
                    {
                        EmployeeData employee = new EmployeeData
                        {
                            Company_Employee_ID = reader["Company_Employee_ID"] != DBNull.Value ? (int)reader["Company_Employee_ID"] : 0,
                            EmployeeCode = reader["Employee_ID"] != DBNull.Value ? (string)reader["Employee_ID"] : string.Empty,
                            Company_Employee_Name = reader["Company_Employee_Name"] != DBNull.Value ? (string)reader["Company_Employee_Name"] : string.Empty,
                            Company_Employee_MobileNumber = reader["Company_Employee_MobileNumber"] != DBNull.Value ? (string)reader["Company_Employee_MobileNumber"] : string.Empty,
                            Company_Employee_Email = reader["Company_Employee_Email"] != DBNull.Value ? (string)reader["Company_Employee_Email"] : string.Empty,
                            Department = reader["Department"] != DBNull.Value ? (string)reader["Department"] : string.Empty,
                            Designation = reader["Designation"] != DBNull.Value ? (string)reader["Designation"] : string.Empty,
                            IsActive = reader["Company_Employee_Active"] != DBNull.Value ? ((bool)reader["Company_Employee_Active"] ? "Yes" : "No") : "No",
                            Manager = reader["Manager"] != DBNull.Value ? (string)reader["Manager"] : string.Empty,
                            Country = reader["Country"] != DBNull.Value ? (string)reader["Country"] : string.Empty,
                            State = reader["State"] != DBNull.Value ? (string)reader["State"] : string.Empty,
                            RegionName = reader["RegionName"] != DBNull.Value ? (string)reader["RegionName"] : string.Empty,
                            Address = reader["Company_Employee_Address"] != DBNull.Value ? (string)reader["Company_Employee_Address"] : string.Empty,
                            Location = reader["Company_Employee_Location"] != DBNull.Value ? (string)reader["Company_Employee_Location"] : string.Empty,
                            Zipcode = reader["Company_Employee_ZipCode"] != DBNull.Value ? (string)reader["Company_Employee_ZipCode"] : string.Empty,
                            Landline = reader["Company_Employee_Landline_Number"] != DBNull.Value ? (string)reader["Company_Employee_Landline_Number"] : string.Empty,
                            ActiveFromstring = reader["Company_Employee_ActiveFrom"] != DBNull.Value ? Convert.ToDateTime(reader["Company_Employee_ActiveFrom"]).ToString("dd-MMM-yyyy") : string.Empty,
                            AtiveTostring = reader["Company_Employee_ValidateUpTo"] != DBNull.Value ? Convert.ToDateTime(reader["Company_Employee_ValidateUpTo"]).ToString("dd-MMM-yyyy") : string.Empty,
                            HourlyRate = reader["HourlyRate"] != DBNull.Value ? (decimal)reader["HourlyRate"] : 0,
                            IsEligibleForOT = reader["IsEligibleForOT"] != DBNull.Value ? (bool)reader["IsEligibleForOT"] : false,
                            ExemptionHours = reader["ExemptionHours"] != DBNull.Value ? (byte)reader["ExemptionHours"] : (byte)0
                        };
                        employeeDataList.Add(employee);
                    }
                }

                // Sorting 
                var sortedEmployeeDataList = employeeDataList.AsQueryable().OrderByField(sidx, sord);

                // FilterToolBar Search
                // FilterToolBar Search
                if (_search)
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    sortedEmployeeDataList = sortedEmployeeDataList.FilterSearch(filtersObj);
                }
                // Advance Search
                else if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<AdvanceFilter>();
                    sortedEmployeeDataList = sortedEmployeeDataList.AdvanceSearch(advnfilter);
                    page = 1;
                }

                count = sortedEmployeeDataList.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                var EmpData = sortedEmployeeDataList.Skip((page - 1) * rows).Take(rows).Select(a => new
                {
                    edit = $"<a title='View' href='#' id='{a.Company_Employee_ID}' key='{a.Company_Employee_ID}' class='editEmployee font-icon-class'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                    delete = $"<input type='checkbox' key='{a.Company_Employee_ID}' id='chk{a.Company_Employee_ID}' class='chkCompanyEmployeeDelete'/>",
                    a.EmployeeCode,
                    a.Company_Employee_ID,
                    a.Company_Employee_Name,
                    a.Company_Employee_MobileNumber,
                    a.Company_Employee_Email,
                    a.Department,
                    a.Designation,
                    a.IsActive,
                    Local = (userLanguageID != generalLanguageID) ? $"<a key='{a.Company_Employee_ID}' src='{AppPath}/Content/Images/local.png' class='employeeLocale' alt='Localize' width='20' height='20'  title='Localize'><i class='fa fa-globe'></i></a>" : "",
                    a.Manager,
                    a.Country,
                    a.State,
                    a.RegionName,
                    a.Address,
                    a.Location,
                    a.Zipcode,
                    a.Landline,
                    a.ActiveFromstring,
                    a.AtiveTostring,
                    a.HourlyRate,
                    a.IsEligibleForOT,
                    a.ExemptionHours
                }).ToList();

                var x = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = EmpData
                };

                return new JsonResult(x);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }

        #endregion


        #region ::: EmployeeExport Uday Kumar J B 04-10-2024:::
        /// <summary>
        /// Exporting Branch Grid
        /// </summary>
        public static async Task<object> EmployeeExport(EmployeeExportList EmployeeExportobj, string connString, string filters, string advnceFilters, string sidx, string sord)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int companyID = EmployeeExportobj.Company_ID;
                int branchID = Convert.ToInt32(EmployeeExportobj.Branch);
                DataTable dt = new DataTable();

                dt.Columns.Add(CommonFunctionalities.GetResourceString(EmployeeExportobj.GeneralCulture.ToString(), "EmployeeID").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(EmployeeExportobj.GeneralCulture.ToString(), "Name").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(EmployeeExportobj.GeneralCulture.ToString(), "Mobile").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(EmployeeExportobj.GeneralCulture.ToString(), "Email").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(EmployeeExportobj.GeneralCulture.ToString(), "Department").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(EmployeeExportobj.GeneralCulture.ToString(), "Designation").ToString());
                //triveni--
                dt.Columns.Add(CommonFunctionalities.GetResourceString(EmployeeExportobj.GeneralCulture.ToString(), "Manager").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(EmployeeExportobj.GeneralCulture.ToString(), "Country").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(EmployeeExportobj.GeneralCulture.ToString(), "State").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(EmployeeExportobj.GeneralCulture.ToString(), "Region").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(EmployeeExportobj.GeneralCulture.ToString(), "Address").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(EmployeeExportobj.GeneralCulture.ToString(), "Location").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(EmployeeExportobj.GeneralCulture.ToString(), "ZipCode").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(EmployeeExportobj.GeneralCulture.ToString(), "Landline").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(EmployeeExportobj.GeneralCulture.ToString(), "ActiveFrom").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(EmployeeExportobj.GeneralCulture.ToString(), "ActiveTo").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(EmployeeExportobj.GeneralCulture.ToString(), "HourlyRate").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(EmployeeExportobj.GeneralCulture.ToString(), "IsActive").ToString());
                //end

                DataTable dt1 = new DataTable();
                dt1.Columns.Add("EmployeeID");
                dt1.Columns.Add("Name");
                dt1.Columns.Add("Mobile");
                dt1.Columns.Add("Email");
                dt1.Columns.Add("Department");
                dt1.Columns.Add("Designation");
                //triveni
                dt1.Columns.Add("Manager");
                dt1.Columns.Add("Country");
                dt1.Columns.Add("State");
                dt1.Columns.Add("Region");
                dt1.Columns.Add("Address");
                dt1.Columns.Add("Location");
                dt1.Columns.Add("ZipCode");
                dt1.Columns.Add("Landline");
                dt1.Columns.Add("ActiveFrom");
                dt1.Columns.Add("ActiveTo");
                dt1.Columns.Add("HourlyRate");
                dt1.Columns.Add("IsActive");
                //end
                dt1.Rows.Add(0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                List<EmployeeData> iEmployeeArray = new List<EmployeeData>();
                int userLanguageID = Convert.ToInt32(EmployeeExportobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(EmployeeExportobj.GeneralLanguageID);

                List<EmployeeData> employeeDataList = new List<EmployeeData>();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    SqlCommand cmd = new SqlCommand("Up_SEL_GetEmployeeDetails", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@CompanyID", companyID);
                    cmd.Parameters.AddWithValue("@LanguageID", EmployeeExportobj.langaugeID);
                    cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                    conn.Open();
                    SqlDataReader reader = cmd.ExecuteReader();
                    while (reader.Read())
                    {
                        EmployeeData employee = new EmployeeData
                        {
                            Company_Employee_ID = reader["Company_Employee_ID"] != DBNull.Value ? (int)reader["Company_Employee_ID"] : 0,
                            EmployeeCode = reader["Employee_ID"] != DBNull.Value ? (string)reader["Employee_ID"] : string.Empty,
                            Company_Employee_Name = reader["Company_Employee_Name"] != DBNull.Value ? (string)reader["Company_Employee_Name"] : string.Empty,
                            Company_Employee_MobileNumber = reader["Company_Employee_MobileNumber"] != DBNull.Value ? (string)reader["Company_Employee_MobileNumber"] : string.Empty,
                            Company_Employee_Email = reader["Company_Employee_Email"] != DBNull.Value ? (string)reader["Company_Employee_Email"] : string.Empty,
                            Department = reader["Department"] != DBNull.Value ? (string)reader["Department"] : string.Empty,
                            Designation = reader["Designation"] != DBNull.Value ? (string)reader["Designation"] : string.Empty,
                            IsActive = reader["Company_Employee_Active"] != DBNull.Value ? ((bool)reader["Company_Employee_Active"] ? "Yes" : "No") : "No",
                            Manager = reader["Manager"] != DBNull.Value ? (string)reader["Manager"] : string.Empty,
                            Country = reader["Country"] != DBNull.Value ? (string)reader["Country"] : string.Empty,
                            State = reader["State"] != DBNull.Value ? (string)reader["State"] : string.Empty,
                            RegionName = reader["RegionName"] != DBNull.Value ? (string)reader["RegionName"] : string.Empty,
                            Address = reader["Company_Employee_Address"] != DBNull.Value ? (string)reader["Company_Employee_Address"] : string.Empty,
                            Location = reader["Company_Employee_Location"] != DBNull.Value ? (string)reader["Company_Employee_Location"] : string.Empty,
                            Zipcode = reader["Company_Employee_ZipCode"] != DBNull.Value ? (string)reader["Company_Employee_ZipCode"] : string.Empty,
                            Landline = reader["Company_Employee_Landline_Number"] != DBNull.Value ? (string)reader["Company_Employee_Landline_Number"] : string.Empty,
                            ActiveFromstring = reader["Company_Employee_ActiveFrom"] != DBNull.Value ? Convert.ToDateTime(reader["Company_Employee_ActiveFrom"]).ToString("dd-MMM-yyyy") : string.Empty,
                            AtiveTostring = reader["Company_Employee_ValidateUpTo"] != DBNull.Value ? Convert.ToDateTime(reader["Company_Employee_ValidateUpTo"]).ToString("dd-MMM-yyyy") : string.Empty,
                            HourlyRate = reader["HourlyRate"] != DBNull.Value ? (decimal)reader["HourlyRate"] : 0,
                            IsEligibleForOT = reader["IsEligibleForOT"] != DBNull.Value ? (bool)reader["IsEligibleForOT"] : false,
                            ExemptionHours = reader["ExemptionHours"] != DBNull.Value ? (byte)reader["ExemptionHours"] : (byte)0
                        };
                        employeeDataList.Add(employee);
                    }
                }

                // Sorting 
                var sortedEmployeeDataList = employeeDataList.AsQueryable().OrderByField(sidx, sord);
                // Apply standard filters if present
                if (!string.IsNullOrEmpty(filters) && filters != "null" && filters != "undefined")
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    if (filtersObj.rules.Count > 0)
                        sortedEmployeeDataList = sortedEmployeeDataList.FilterSearch(filtersObj);
                }


                // Apply advanced filters if present
                if (!string.IsNullOrEmpty(advnceFilters) && advnceFilters != "null")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Common.DecryptString(advnceFilters)).ToObject<AdvanceFilter>();
                    sortedEmployeeDataList = sortedEmployeeDataList.AdvanceSearch(advnfilter);
                }
                iEmployeeArray = sortedEmployeeDataList.ToList();
                int cnt = iEmployeeArray.AsEnumerable().Count();
                for (int i = 0; i < cnt; i++)
                {
                    dt.Rows.Add(iEmployeeArray.ElementAt(i).EmployeeCode, iEmployeeArray.ElementAt(i).Company_Employee_Name, iEmployeeArray.ElementAt(i).Company_Employee_MobileNumber, iEmployeeArray.ElementAt(i).Company_Employee_Email, iEmployeeArray.ElementAt(i).Department, iEmployeeArray.ElementAt(i).Designation, iEmployeeArray.ElementAt(i).Manager, iEmployeeArray.ElementAt(i).Country, iEmployeeArray.ElementAt(i).State, iEmployeeArray.ElementAt(i).RegionName, iEmployeeArray.ElementAt(i).Address, iEmployeeArray.ElementAt(i).Location, iEmployeeArray.ElementAt(i).Zipcode, iEmployeeArray.ElementAt(i).Landline, iEmployeeArray.ElementAt(i).ActiveFromstring, iEmployeeArray.ElementAt(i).AtiveTostring, iEmployeeArray.ElementAt(i).HourlyRate, iEmployeeArray.ElementAt(i).IsActive);
                    //dt.Rows.Add(iEmployeeArray.ElementAt(i).EmployeeCode, iEmployeeArray.ElementAt(i).Company_Employee_Name, iEmployeeArray.ElementAt(i).Company_Employee_MobileNumber, iEmployeeArray.ElementAt(i).Company_Employee_Email, iEmployeeArray.ElementAt(i).Department, iEmployeeArray.ElementAt(i).Designation, iEmployeeArray.ElementAt(i).Manager, iEmployeeArray.ElementAt(i).Country, iEmployeeArray.ElementAt(i).State, iEmployeeArray.ElementAt(i).RegionName, iEmployeeArray.ElementAt(i).Address, iEmployeeArray.ElementAt(i).Location, iEmployeeArray.ElementAt(i).Zipcode,iEmployeeArray.ElementAt(i).Landline, iEmployeeArray.ElementAt(i).ActiveFromstring, iEmployeeArray.ElementAt(i).AtiveTostring, iEmployeeArray.ElementAt(i).HourlyRate, iEmployeeArray.ElementAt(i).IsActive);
                }
                ExportList reportExportList = new ExportList
                {
                    Company_ID = EmployeeExportobj.Company_ID, // Assuming this is available in ExportObj
                    Branch = EmployeeExportobj.Branch,
                    dt1 = dt1,


                    dt = dt,

                    FileName = "Employee", // Set a default or dynamic filename
                    Header = CommonFunctionalities.GetResourceString(EmployeeExportobj.UserCulture.ToString(), "Employee").ToString(), // Set a default or dynamic header
                    exprtType = EmployeeExportobj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                    UserCulture = EmployeeExportobj.UserCulture
                };
                //  DocumentExport.Export(exprtType, dt, dt1, "Employee", HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "Employee").ToString());
                var res = await DocumentExport.Export(reportExportList, connString, LogException);
                return res.Value;
                // gbl.InsertGPSDetails(Convert.ToInt32(EmployeeExportobj.Company_ID.ToString()), branchID, EmployeeExportobj.User_ID, Common.GetObjectID("CoreEmployeeMaster"), 0, 0, 0, "Employee - Export ", false, Convert.ToInt32(EmployeeExportobj.MenuID), Convert.ToDateTime(EmployeeExportobj.LoggedINDateTime));
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }

            }
            return null;
        }
        #endregion


        #region ::: SelEmployeelocale Uday Kumar J B 27-08-2024:::
        /// <summary>
        /// to select the employees locale detail of the company
        /// </summary> 
        /// 

        public static IActionResult SelEmployeelocale(string connString, SelEmployeelocaleList SelEmployeelocaleobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int userLanguageID = Convert.ToInt32(SelEmployeelocaleobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelEmployeelocaleobj.GeneralLanguageID);
                DataTable employeeGeneralTable = new DataTable();
                DataTable employeeLocaleTable = new DataTable();
                DataTable managerTable = new DataTable();

                // Get employee general details
                using (SqlConnection con = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("sp_GetCompanyEmployeeByID", con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyEmployeeID", SelEmployeelocaleobj.id);
                        SqlDataAdapter da = new SqlDataAdapter(cmd);
                        da.Fill(employeeGeneralTable);
                    }

                    // Get employee locale details
                    using (SqlCommand cmd = new SqlCommand("sp_GetCompanyEmployeeLocaleByIDAndLanguage", con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyEmployeeID", SelEmployeelocaleobj.id);
                        cmd.Parameters.AddWithValue("@LanguageID", userLanguageID);
                        SqlDataAdapter da = new SqlDataAdapter(cmd);
                        da.Fill(employeeLocaleTable);
                    }

                    // Get manager details if exists
                    if (employeeGeneralTable.Rows.Count > 0 && employeeGeneralTable.Rows[0]["Company_Employee_Manager_ID"] != DBNull.Value)
                    {
                        int managerId = Convert.ToInt32(employeeGeneralTable.Rows[0]["Company_Employee_Manager_ID"]);
                        using (SqlCommand cmd = new SqlCommand("sp_GetManagerByID", con))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@ManagerID", managerId);
                            SqlDataAdapter da = new SqlDataAdapter(cmd);
                            da.Fill(managerTable);
                        }
                    }
                }

                dynamic s = null;
                var x = s;
                DataRow employeeGeneralRow = employeeGeneralTable.Rows.Count > 0 ? employeeGeneralTable.Rows[0] : null;
                DataRow employeeLocaleRow = employeeLocaleTable.Rows.Count > 0 ? employeeLocaleTable.Rows[0] : null;
                DataRow managerRow = managerTable.Rows.Count > 0 ? managerTable.Rows[0] : null;

                if (employeeGeneralRow != null)
                {
                    x = new
                    {
                        EmpID = employeeGeneralRow["Company_Employee_ID"],
                        EmpGName = employeeGeneralRow["Company_Employee_Name"],
                        EmpGAddress1 = employeeGeneralRow["Company_Employee_Address"],
                        EmpGLocation = employeeGeneralRow["Company_Employee_Location"],
                        EmpCodeG = employeeGeneralRow["Employee_ID"],
                        EmpMobileG = employeeGeneralRow["Company_Employee_MobileNumber"],
                        EmpLandLineG = employeeGeneralRow["Company_Employee_Landline_Number"],
                        EmpZipCodeG = employeeGeneralRow["Company_Employee_ZipCode"],
                        EmpActiveFromG = employeeGeneralRow["Company_Employee_ActiveFrom"] != DBNull.Value ? Convert.ToDateTime(employeeGeneralRow["Company_Employee_ActiveFrom"]).ToString("dd-MMM-yyyy") : "",
                        EmpActiveToG = employeeGeneralRow["Company_Employee_ValidateUpTo"] != DBNull.Value ? Convert.ToDateTime(employeeGeneralRow["Company_Employee_ValidateUpTo"]).ToString("dd-MMM-yyyy") : "",
                        EmpIsActive = employeeGeneralRow["Company_Employee_Active"],
                        EmpEmailG = employeeGeneralRow["Company_Employee_Email"],
                        EmpHourlyRateG = employeeGeneralRow["HourlyRate"],
                        EmpCountryG = GetRefMasterDetailNamea((int?)employeeGeneralRow["Country_ID"], connString),
                        EmpDepartmentG = GetRefMasterDetailNamea((int?)employeeGeneralRow["Company_Employee_Department_ID"], connString),
                        EmpDesignationG = GetRefMasterDetailNamea((int?)employeeGeneralRow["Company_Employee_Designation_ID"], connString),
                        EmpStateG = GetStateNamea((int?)employeeGeneralRow["State_ID"], connString),
                        EmpManagerG = managerRow != null ? managerRow["Company_Employee_Name"].ToString() : "",
                        EmpRegionNameG = GetRefMasterDetailNamea((int?)employeeGeneralRow["Region_ID"], connString),
                        EmpLID = employeeLocaleRow != null ? employeeLocaleRow["Company_Employee_Locale_ID"] : "",
                        EmpLName = employeeLocaleRow != null ? employeeLocaleRow["Company_Employee_Name"] : "",
                        EmpLAddress1 = employeeLocaleRow != null ? employeeLocaleRow["Company_Employee_Address"] : "",
                        EmpLLocation = employeeLocaleRow != null ? employeeLocaleRow["Company_Employee_Location"] : "",
                        IsEligibleForOT = employeeGeneralRow["IsEligibleForOT"],
                        ExemptionHours = employeeGeneralRow["ExemptionHours"]
                    };
                }
                return new JsonResult(x);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(null);
        }

        // Helper methods for fetching related data
        private static string GetRefMasterDetailNamea(int? refMasterDetailId, string connString)
        {
            if (refMasterDetailId == null) return "";
            using (SqlConnection con = new SqlConnection(connString))
            {
                using (SqlCommand cmd = new SqlCommand("sp_GetRefMasterDetailName", con))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@RefMasterDetailID", refMasterDetailId);
                    con.Open();
                    return cmd.ExecuteScalar()?.ToString() ?? "";
                }
            }
        }

        private static string GetStateNamea(int? stateId, string connString)
        {
            if (stateId == null) return "";
            using (SqlConnection con = new SqlConnection(connString))
            {
                using (SqlCommand cmd = new SqlCommand("sp_GetStateName", con))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@StateID", stateId);
                    con.Open();
                    return cmd.ExecuteScalar()?.ToString() ?? "";
                }
            }
        }



        #endregion


        #region ::: SelectSpecificEmployeeDetails Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// to select the specific employee detail of the company for editing
        /// </summary> 
        /// 

        public static IActionResult SelectSpecificEmployeeDetails(string connString, SelectSpecificEmployeeDetailsList SelectSpecificEmployeeDetailsobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // GNM_User User = SelectSpecificEmployeeDetailsobj.UserDetails.FirstOrDefault();
                int branchID = Convert.ToInt32(SelectSpecificEmployeeDetailsobj.Branch);
                int userLanguageID = Convert.ToInt32(SelectSpecificEmployeeDetailsobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectSpecificEmployeeDetailsobj.GeneralLanguageID);

                var employeeDetails = GetEmployeeDetails(SelectSpecificEmployeeDetailsobj.id, connString);
                var managerDetails = employeeDetails.Company_Employee_Manager_ID != null ? GetManagerDetails(employeeDetails.Company_Employee_Manager_ID, connString) : null;
                var selectedCountryName = GetRefMasterDetailName(employeeDetails.Country_ID, connString);
                var selectedDepartmentName = GetRefMasterDetailName(employeeDetails.Company_Employee_Department_ID, connString);
                var selectedDesignationName = GetRefMasterDetailName(employeeDetails.Company_Employee_Designation_ID, connString);
                var selectedStateName = GetStateName(employeeDetails.State_ID, connString);
                var employeeLocaleDetails = GetEmployeeLocaleDetails(SelectSpecificEmployeeDetailsobj.id, userLanguageID, connString);

                var regionName = GetRefMasterDetailName(employeeDetails.Region_ID, connString);

                var result = new
                {
                    employeeDetails.Company_Employee_ID,
                    employeeDetails.Employee_ID,
                    Company_Employee_Name = SelectSpecificEmployeeDetailsobj.languageID == generalLanguageID ? employeeDetails.Company_Employee_Name : employeeLocaleDetails.Company_Employee_Name,
                    employeeDetails.Company_ID,
                    employeeDetails.Country_ID,
                    employeeDetails.State_ID,
                    employeeDetails.Company_Employee_MobileNumber,
                    employeeDetails.Company_Employee_Landline_Number,
                    employeeDetails.Company_Employee_ZipCode,
                    Company_Employee_ActiveFrom = employeeDetails.Company_Employee_ActiveFrom != null ? Convert.ToDateTime(employeeDetails.Company_Employee_ActiveFrom).ToString("dd-MMM-yyyy") : "",
                    Company_Employee_ValidateUpTo = employeeDetails.Company_Employee_ValidateUpTo != null ? Convert.ToDateTime(employeeDetails.Company_Employee_ValidateUpTo).ToString("dd-MMM-yyyy") : "",
                    employeeDetails.Company_Employee_Active,
                    employeeDetails.Company_Employee_Manager_ID,
                    Company_Employee_Address = SelectSpecificEmployeeDetailsobj.languageID == generalLanguageID ? employeeDetails.Company_Employee_Address : employeeLocaleDetails.Company_Employee_Address,
                    Company_Employee_Location = SelectSpecificEmployeeDetailsobj.languageID == generalLanguageID ? employeeDetails.Company_Employee_Location : employeeLocaleDetails.Company_Employee_Location,
                    employeeDetails.Company_Employee_Email,
                    employeeDetails.HourlyRate,
                    employeeDetails.Company_Employee_Department_ID,
                    employeeDetails.Company_Employee_Designation_ID,
                    employeeDetails.PHOTONAME,
                    Selected_CountryName = selectedCountryName,
                    Selected_DepartmentName = selectedDepartmentName,
                    Selected_DesignationName = selectedDesignationName,
                    Selected_StateName = selectedStateName,
                    Selected_ManagerName = managerDetails != null ? managerDetails.Company_Employee_Name : "",
                    Region_ID = employeeDetails.Region_ID ?? 0,
                    Region_Name = regionName,
                    employeeDetails.IsEligibleForOT,
                    employeeDetails.IsEligibleForIncentive,
                    employeeDetails.IsConsideredForPayroll,
                    employeeDetails.ExemptionHours
                };

                //  gbl.InsertGPSDetails(Convert.ToInt32(SelectSpecificEmployeeDetailsobj.Company_ID.ToString()), branchID, SelectSpecificEmployeeDetailsobj.User_ID, Common.GetObjectID("CoreEmployeeMaster"), result.Company_Employee_ID, 0, 0, "Viewed " + result.Employee_ID + "", false, Convert.ToInt32(SelectSpecificEmployeeDetailsobj.MenuID), Convert.ToDateTime(SelectSpecificEmployeeDetailsobj.LoggedINDateTime));

                return new JsonResult(result);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }

        private static dynamic GetEmployeeDetails(int employeeID, string connString)
        {
            using (SqlConnection con = new SqlConnection(connString))
            {
                using (SqlCommand cmd = new SqlCommand("sp_GetEmployeeDetailsByID", con))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@EmployeeID", employeeID);
                    con.Open();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new
                            {
                                Company_Employee_ID = reader["Company_Employee_ID"],
                                Employee_ID = reader["Employee_ID"],
                                Company_Employee_Name = reader["Company_Employee_Name"],
                                Company_ID = reader["Company_ID"],
                                Country_ID = reader["Country_ID"],
                                State_ID = reader["State_ID"],
                                Company_Employee_MobileNumber = reader["Company_Employee_MobileNumber"],
                                Company_Employee_Landline_Number = reader["Company_Employee_Landline_Number"],
                                Company_Employee_ZipCode = reader["Company_Employee_ZipCode"],
                                Company_Employee_ActiveFrom = reader["Company_Employee_ActiveFrom"] as DateTime?,
                                Company_Employee_ValidateUpTo = reader["Company_Employee_ValidateUpTo"] as DateTime?,
                                Company_Employee_Active = reader["Company_Employee_Active"],
                                Company_Employee_Manager_ID = reader["Company_Employee_Manager_ID"] as int?,
                                Company_Employee_Address = reader["Company_Employee_Address"],
                                Company_Employee_Location = reader["Company_Employee_Location"],
                                Company_Employee_Email = reader["Company_Employee_Email"],
                                HourlyRate = reader["HourlyRate"],
                                Company_Employee_Department_ID = reader["Company_Employee_Department_ID"],
                                Company_Employee_Designation_ID = reader["Company_Employee_Designation_ID"],
                                PHOTONAME = reader["PHOTONAME"],
                                IsEligibleForOT = reader["IsEligibleForOT"],
                                IsEligibleForIncentive = reader["IsEligibleForIncentive"],
                                IsConsideredForPayroll = reader["IsConsideredForPayroll"],
                                ExemptionHours = reader["ExemptionHours"],
                                Region_ID = reader["Region_ID"] as int?
                            };
                        }
                    }
                }
            }
            return null;
        }

        private static dynamic GetManagerDetails(int? managerID, string connString)
        {
            if (managerID == null) return null;
            using (SqlConnection con = new SqlConnection(connString))
            {
                using (SqlCommand cmd = new SqlCommand("sp_GetManagerDetailsByID", con))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@ManagerID", managerID);
                    con.Open();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new
                            {
                                Company_Employee_ID = reader["Company_Employee_ID"],
                                Company_Employee_Name = reader["Company_Employee_Name"]
                            };
                        }
                    }
                }
            }
            return null;
        }

        private static string GetRefMasterDetailName(int? refMasterDetailID, string connString)
        {
            if (refMasterDetailID == null) return "";
            using (SqlConnection con = new SqlConnection(connString))
            {
                using (SqlCommand cmd = new SqlCommand("sp_GetRefMasterDetailNameByID", con))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@RefMasterDetailID", refMasterDetailID);
                    con.Open();
                    return cmd.ExecuteScalar()?.ToString() ?? "";
                }
            }
        }

        private static string GetStateName(int? stateID, string connString)
        {
            if (stateID == null) return "";
            using (SqlConnection con = new SqlConnection(connString))
            {
                using (SqlCommand cmd = new SqlCommand("sp_GetStateNameByID", con))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@StateID", stateID);
                    con.Open();
                    return cmd.ExecuteScalar()?.ToString() ?? "";
                }
            }
        }

        private static dynamic GetEmployeeLocaleDetails(int employeeID, int languageID, string connString)
        {
            using (SqlConnection con = new SqlConnection(connString))
            {
                using (SqlCommand cmd = new SqlCommand("sp_GetEmployeeLocaleDetails", con))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@EmployeeID", employeeID);
                    cmd.Parameters.AddWithValue("@LanguageID", languageID);
                    con.Open();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new
                            {
                                Company_Employee_Locale_ID = reader["Company_Employee_Locale_ID"],
                                Company_Employee_Name = reader["Company_Employee_Name"],
                                Company_Employee_Address = reader["Company_Employee_Address"],
                                Company_Employee_Location = reader["Company_Employee_Location"]
                            };
                        }
                    }
                }
            }
            return null;
        }


        #endregion


        #region ::: InsertEmployeeDetails Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// to insert the employee details
        /// </summary> 
        /// 

        public static IActionResult InsertEmployeeDetails(string connString, InsertEmployeeDetailsList InsertEmployeeDetailsobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int branchID = Convert.ToInt32(InsertEmployeeDetailsobj.Branch);
                int modifiedBy = Convert.ToInt32(InsertEmployeeDetailsobj.User_Employee_ID);
                JObject jobj = JObject.Parse(InsertEmployeeDetailsobj.EmployeeData);
                // GNM_User user = InsertEmployeeDetailsobj.UserDetails.FirstOrDefault();
                int companyID = InsertEmployeeDetailsobj.Company_ID;
                int userID = InsertEmployeeDetailsobj.User_ID;

                // Extract values from JSON
                string name = Common.DecryptString(jobj["Name"].ToString());
                string isActive = jobj["isActive"].ToString();
                string employeeID = Uri.UnescapeDataString(jobj["employeeID"].ToString());
                string mobileNo = Uri.UnescapeDataString(jobj["mobileNo"].ToString());
                string landLineNo = Uri.UnescapeDataString(jobj["landLineNo"].ToString());
                string zipCode = Common.DecryptString(jobj["zipCode"].ToString());
                string activeFrom = jobj["activeFrom"].ToString();
                string activeTo = jobj["activeTo"].ToString();
                string address1 = Uri.UnescapeDataString(Common.DecryptString(jobj["address1"].ToString()));
                string location = Common.DecryptString(jobj["location"].ToString());
                string email = Common.DecryptString(jobj["email"].ToString());
                string hourlyrate = jobj["hourlyrate"].ToString();
                string countryID = jobj["countryID"].ToString();
                string stateID = jobj["stateID"].ToString();
                string deptID = jobj["deptID"].ToString();
                string desgnID = jobj["desgnID"].ToString();
                string mgrID = jobj["mgrID"].ToString();
                int? regionID = Convert.ToInt32(jobj["Region_ID"].ToString());
                bool isEligibleForOT = Convert.ToBoolean(jobj["IsEligibleForOT"].ToString());
                bool isEligibleForIncentive = Convert.ToBoolean(jobj["IsEligibleForIncentive"].ToString());
                bool isConsideredForPayroll = Convert.ToBoolean(jobj["IsConsideredForPayroll"].ToString());
                int exemptionHours = Convert.ToByte(jobj["ExemptionHours"].ToString());
                string image = Uri.UnescapeDataString(jobj["image"].ToString());

                // Handle image path adjustments
                if (image.Contains("\\"))
                {
                    int lastIndex = image.LastIndexOf('\\') + 1;
                    image = image.Substring(lastIndex);
                }

                int companyEmployeeID;

                // Insert Employee Details using ADO.NET
                using (SqlConnection con = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("SP_InsertEmployeeDetails", con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_Employee_Active", (isActive == "checked"));
                        cmd.Parameters.AddWithValue("@Company_Employee_ActiveFrom", string.IsNullOrEmpty(activeFrom) ? (object)DBNull.Value : Convert.ToDateTime(activeFrom));
                        cmd.Parameters.AddWithValue("@Company_Employee_Address", address1);
                        cmd.Parameters.AddWithValue("@Company_Employee_Department_ID", Convert.ToInt32(deptID));
                        cmd.Parameters.AddWithValue("@Company_Employee_Designation_ID", Convert.ToInt32(desgnID));
                        cmd.Parameters.AddWithValue("@Company_Employee_Email", email);
                        cmd.Parameters.AddWithValue("@HourlyRate", string.IsNullOrEmpty(hourlyrate) ? (object)DBNull.Value : Convert.ToDecimal(hourlyrate));
                        cmd.Parameters.AddWithValue("@Company_Employee_Landline_Number", landLineNo);
                        cmd.Parameters.AddWithValue("@Company_Employee_Location", location);
                        cmd.Parameters.AddWithValue("@Company_Employee_Manager_ID", string.IsNullOrEmpty(mgrID) ? (object)DBNull.Value : Convert.ToInt32(mgrID));
                        cmd.Parameters.AddWithValue("@Company_Employee_MobileNumber", mobileNo);
                        cmd.Parameters.AddWithValue("@Company_Employee_Name", name);
                        cmd.Parameters.AddWithValue("@Company_Employee_ValidateUpTo", string.IsNullOrEmpty(activeTo) ? (object)DBNull.Value : Convert.ToDateTime(activeTo));
                        cmd.Parameters.AddWithValue("@Company_Employee_ZipCode", zipCode);
                        cmd.Parameters.AddWithValue("@Company_ID", companyID);
                        cmd.Parameters.AddWithValue("@Country_ID", Convert.ToInt32(countryID));
                        cmd.Parameters.AddWithValue("@Employee_ID", employeeID);
                        cmd.Parameters.AddWithValue("@State_ID", Convert.ToInt32(stateID));
                        cmd.Parameters.AddWithValue("@ModifiedBy", userID);
                        cmd.Parameters.AddWithValue("@Region_ID", regionID == 0 ? (object)DBNull.Value : regionID);
                        cmd.Parameters.AddWithValue("@IsEligibleForOT", isEligibleForOT);
                        cmd.Parameters.AddWithValue("@IsEligibleForIncentive", isEligibleForIncentive);
                        cmd.Parameters.AddWithValue("@IsConsideredForPayroll", isConsideredForPayroll);
                        cmd.Parameters.AddWithValue("@ExemptionHours", Convert.ToByte(exemptionHours));
                        cmd.Parameters.AddWithValue("@PHOTONAME", image);

                        SqlParameter outputIDParam = new SqlParameter("@Company_Employee_ID", SqlDbType.Int)
                        {
                            Direction = ParameterDirection.Output
                        };
                        cmd.Parameters.Add(outputIDParam);

                        con.Open();
                        cmd.ExecuteNonQuery();
                        companyEmployeeID = (int)outputIDParam.Value;
                    }
                }

                // Move file from temp to final location
                string smp = BucketFilePath;

                // Construct the destination path using companyEmployeeID and image
                string dstPath = Path.Combine(smp, $"{companyEmployeeID}-{image}");

                // Check if the image is not null or empty
                if (!string.IsNullOrEmpty(image))
                {
                    // Construct the source path based on the image
                    string tempDir = Path.Combine(smp, $"Temp_{image}");
                    string srcPath = Path.Combine(tempDir, image);

                    // Check if the source file exists before moving
                    if (File.Exists(srcPath))
                    {
                        // Move the file from temp to the final destination
                        File.Move(srcPath, dstPath);
                    }
                }

                //if (!string.IsNullOrEmpty(image))
                //{
                //    string SMP = Server.MapPath(AppPath + "/EmployeeProfile");
                //    string srcPath = SMP + ConfigurationManager.AppSettings.Get("AttachmentPath").ToString() + "Temp_0" + "-" + image;
                //    string dstPath = SMP + ConfigurationManager.AppSettings.Get("AttachmentPath").ToString() + companyEmployeeID + "-" + image;

                //    if (!System.IO.File.Exists(dstPath))
                //    {
                //        System.IO.File.Move(srcPath, dstPath);
                //    }
                //}

                // Check and log incentive eligibility change
                if (isEligibleForIncentive)
                {
                    using (SqlConnection con = new SqlConnection(connString))
                    {
                        using (SqlCommand cmd = new SqlCommand("SP_InsertEmployeeETOLogDetails", con))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Employee_ID", companyEmployeeID);
                            cmd.Parameters.AddWithValue("@ModifiedBy", userID);
                            cmd.Parameters.AddWithValue("@IsAdded", isEligibleForIncentive);
                            cmd.Parameters.AddWithValue("@BranchID", branchID);
                            cmd.Parameters.AddWithValue("@LogDate", CommonFunctionalities.LocalTimes(branchID, DateTime.Now));
                            con.Open();
                            cmd.ExecuteNonQuery();
                        }
                    }
                }

                // GPS Details insertion (assuming gbl.InsertGPSDetails is already an ADO.NET method)
                var result = new
                {
                    companyEmployeeID,
                    employeeID
                };
                //  gbl.InsertGPSDetails(companyID, branchID, userID, Common.GetObjectID("CoreEmployeeMaster"), companyEmployeeID, 0, 0, "Inserted " + result.employeeID + "", false, Convert.ToInt32(InsertEmployeeDetailsobj.MenuID), Convert.ToDateTime(InsertEmployeeDetailsobj.LoggedINDateTime.ToString()), null);

                return new JsonResult(result);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(null);
        }

        #endregion


        #region ::: UpdateEmployeeDetails Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// to update the employee details
        /// </summary> 
        public static IActionResult UpdateEmployeeDetails(string connString, UpdateEmployeeDetailsList UpdateEmployeeDetailsobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int branchID = Convert.ToInt32(UpdateEmployeeDetailsobj.Branch);
                int companyID = Convert.ToInt32(UpdateEmployeeDetailsobj.Company_ID);
                int modifiedBy = Convert.ToInt32(UpdateEmployeeDetailsobj.User_Employee_ID);

                JObject jobj = JObject.Parse(UpdateEmployeeDetailsobj.EmployeeData);

                int primaryKey = Convert.ToInt32(jobj["primaryKey"].ToString());
                //  GNM_User user = UpdateEmployeeDetailsobj.UserDetails.FirstOrDefault();
                int userID = UpdateEmployeeDetailsobj.User_ID;

                string name = Common.DecryptString(jobj["Name"].ToString());
                string isActive = jobj["isActive"].ToString();
                string employeeID = Uri.UnescapeDataString(jobj["employeeID"].ToString());
                string mobileNo = jobj["mobileNo"].ToString();
                string landLineNo = jobj["landLineNo"].ToString();
                string zipCode = Common.DecryptString(jobj["zipCode"].ToString());
                string activeFrom = jobj["activeFrom"].ToString();
                string activeTo = jobj["activeTo"].ToString();
                string address1 = Uri.UnescapeDataString(Common.DecryptString(jobj["address1"].ToString()));
                string location = Common.DecryptString(jobj["location"].ToString());
                string email = Common.DecryptString(jobj["email"].ToString());
                string hourlyrate = jobj["hourlyrate"].ToString();
                string countryID = jobj["countryID"].ToString();
                string stateID = jobj["stateID"].ToString();
                string deptID = jobj["deptID"].ToString();
                string desgnID = jobj["desgnID"].ToString();
                string mgrID = jobj["mgrID"].ToString();
                int? regionID = string.IsNullOrEmpty(jobj["Region_ID"].ToString()) ? (int?)null : Convert.ToInt32(jobj["Region_ID"]);
                bool isEligibleForOT = Convert.ToBoolean(jobj["IsEligibleForOT"].ToString());
                bool isEligibleForIncentive = Convert.ToBoolean(jobj["IsEligibleForIncentive"].ToString());
                bool isConsideredForPayroll = Convert.ToBoolean(jobj["IsConsideredForPayroll"].ToString());
                int exemptionHours = Convert.ToInt32(jobj["ExemptionHours"].ToString());

                using (SqlConnection con = new SqlConnection(connString))
                {
                    con.Open();

                    // Log incentive eligibility change if necessary
                    if (IsEligibleForIncentiveChanged(primaryKey, isEligibleForIncentive, con))
                    {
                        using (SqlCommand cmd = new SqlCommand("SP_InsertEmployeeETOLogDetails", con))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Employee_ID", primaryKey);
                            cmd.Parameters.AddWithValue("@ModifiedBy", userID);
                            cmd.Parameters.AddWithValue("@IsAdded", isEligibleForIncentive);
                            // cmd.Parameters.AddWithValue("@LogDate", Common.LocalTime(branchID, DateTime.Now));
                            cmd.ExecuteNonQuery();
                        }
                    }

                    // Update Employee Details
                    using (SqlCommand cmd = new SqlCommand("SP_UpdateEmployeeDetails", con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@PrimaryKey", primaryKey);
                        cmd.Parameters.AddWithValue("@Name", name);
                        cmd.Parameters.AddWithValue("@IsActive", isActive == "checked");
                        cmd.Parameters.AddWithValue("@EmployeeID", employeeID);
                        cmd.Parameters.AddWithValue("@MobileNo", mobileNo);
                        cmd.Parameters.AddWithValue("@LandLineNo", landLineNo);
                        cmd.Parameters.AddWithValue("@ZipCode", zipCode);
                        cmd.Parameters.AddWithValue("@ActiveFrom", string.IsNullOrEmpty(activeFrom) ? (object)DBNull.Value : Convert.ToDateTime(activeFrom));
                        cmd.Parameters.AddWithValue("@ActiveTo", string.IsNullOrEmpty(activeTo) ? (object)DBNull.Value : Convert.ToDateTime(activeTo));
                        cmd.Parameters.AddWithValue("@Address1", address1);
                        cmd.Parameters.AddWithValue("@Location", location);
                        cmd.Parameters.AddWithValue("@Email", email);
                        cmd.Parameters.AddWithValue("@HourlyRate", string.IsNullOrEmpty(hourlyrate) ? (object)DBNull.Value : Convert.ToDecimal(hourlyrate));
                        cmd.Parameters.AddWithValue("@CountryID", Convert.ToInt32(countryID));
                        cmd.Parameters.AddWithValue("@StateID", Convert.ToInt32(stateID));
                        cmd.Parameters.AddWithValue("@DeptID", Convert.ToInt32(deptID));
                        cmd.Parameters.AddWithValue("@DesgnID", Convert.ToInt32(desgnID));
                        cmd.Parameters.AddWithValue("@MgrID", string.IsNullOrEmpty(mgrID) ? (object)DBNull.Value : Convert.ToInt32(mgrID));
                        cmd.Parameters.AddWithValue("@RegionID", regionID.HasValue ? regionID.Value : (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@IsEligibleForOT", isEligibleForOT);
                        cmd.Parameters.AddWithValue("@IsEligibleForIncentive", isEligibleForIncentive);
                        cmd.Parameters.AddWithValue("@IsConsideredForPayroll", isConsideredForPayroll);
                        cmd.Parameters.AddWithValue("@ExemptionHours", exemptionHours);
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);
                        cmd.Parameters.AddWithValue("@ModifiedBy", userID);
                        cmd.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                        cmd.ExecuteNonQuery();
                    }
                }

                // GPS Details insertion
                //  gbl.InsertGPSDetails(companyID, branchID, userID, Common.GetObjectID("CoreEmployeeMaster"), primaryKey, 0, 0, "Updated " + employeeID + "", false, Convert.ToInt32(UpdateEmployeeDetailsobj.MenuID), Convert.ToDateTime(UpdateEmployeeDetailsobj.LoggedINDateTime.ToString()), null);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return new JsonResult(false);
        }

        private static bool IsEligibleForIncentiveChanged(int primaryKey, bool newEligibility, SqlConnection con)
        {
            using (SqlCommand cmd = new SqlCommand("SP_GetEmployeeEligibilityForIncentive", con))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@PrimaryKey", primaryKey);

                var result = cmd.ExecuteScalar();
                bool currentEligibility = result != DBNull.Value && Convert.ToBoolean(result);
                return currentEligibility != newEligibility;
            }
        }
        #endregion


        #region ::: InsertEmployeeLocale Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// to insert the Employee locale details
        /// </summary>
        /// 

        public static IActionResult InsertEmployeeLocale(string connString, InsertEmployeeLocaleList InsertEmployeeLocaleobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int userLanguageID = Convert.ToInt32(InsertEmployeeLocaleobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(InsertEmployeeLocaleobj.GeneralLanguageID);
                JObject jobj = JObject.Parse(InsertEmployeeLocaleobj.employeeLocaleData);

                int EmpID = Convert.ToInt32(jobj["EmpID"].ToString());
                string EmpName = Common.DecryptString(jobj["EmpName"].ToString());
                string EmpAddress1 = Uri.UnescapeDataString(Common.DecryptString(jobj["EmpAddress1"].ToString()));
                string Location = Common.DecryptString(jobj["Location"].ToString());
                int insertedLocaleID = 0;

                using (SqlConnection con = new SqlConnection(connString))
                {
                    con.Open();

                    using (SqlCommand cmd = new SqlCommand("SP_InsertEmployeeLocale", con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_Employee_ID", EmpID);
                        cmd.Parameters.AddWithValue("@Company_Employee_Name", EmpName);
                        cmd.Parameters.AddWithValue("@Company_Employee_Address", EmpAddress1);
                        cmd.Parameters.AddWithValue("@Company_Employee_Location", Location);
                        cmd.Parameters.AddWithValue("@Language_ID", userLanguageID);

                        SqlParameter outputIdParam = new SqlParameter("@Company_Employee_Locale_ID", SqlDbType.Int)
                        {
                            Direction = ParameterDirection.Output
                        };
                        cmd.Parameters.Add(outputIdParam);

                        cmd.ExecuteNonQuery();

                        insertedLocaleID = (int)outputIdParam.Value;
                    }
                }

                // GPS Details insertion
                // gbl.InsertGPSDetails(Convert.ToInt32(InsertEmployeeLocaleobj.Company_ID), Convert.ToInt32(InsertEmployeeLocaleobj.Branch), Convert.ToInt32(InsertEmployeeLocaleobj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreEmployeeMaster")), insertedLocaleID, 0, 0, "Update", false, Convert.ToInt32(InsertEmployeeLocaleobj.MenuID));

                var x = new
                {
                    Company_Employee_Locale_ID = insertedLocaleID
                };

                return new JsonResult(x);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return new JsonResult(false);
        }
        #endregion


        #region ::: UpdateEmployeeLocale  Uday Kumar J B 27-08-2024:::
        /// <summary>
        /// to update the Employee locale details
        /// </summary>
        /// 

        public static IActionResult UpdateEmployeeLocale(string connString, UpdateEmployeeLocaleList UpdateEmployeeLocaleobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jobj = JObject.Parse(UpdateEmployeeLocaleobj.employeeLocaleData);

                int EmpID = Convert.ToInt32(jobj["EmpID"].ToString());
                int EmpLocaleID = Convert.ToInt32(jobj["EmpLocaleID"].ToString());
                string EmpName = Common.DecryptString(jobj["EmpName"].ToString());
                string EmpAddress1 = Uri.UnescapeDataString(Common.DecryptString(jobj["EmpAddress1"].ToString()));
                string Location = Common.DecryptString(jobj["Location"].ToString());

                using (SqlConnection con = new SqlConnection(connString))
                {
                    con.Open();

                    using (SqlCommand cmd = new SqlCommand("SP_UpdateEmployeeLocale", con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_Employee_Locale_ID", EmpLocaleID);
                        cmd.Parameters.AddWithValue("@Company_Employee_ID", EmpID);
                        cmd.Parameters.AddWithValue("@Company_Employee_Name", EmpName);
                        cmd.Parameters.AddWithValue("@Company_Employee_Address", EmpAddress1);
                        cmd.Parameters.AddWithValue("@Company_Employee_Location", Location);

                        cmd.ExecuteNonQuery();
                    }
                }

                // GPS Details insertion
                //  gbl.InsertGPSDetails(Convert.ToInt32(UpdateEmployeeLocaleobj.Company_ID), Convert.ToInt32(UpdateEmployeeLocaleobj.Branch), Convert.ToInt32(UpdateEmployeeLocaleobj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreEmployeeMaster")), EmpID, 0, 0, "Update", false, Convert.ToInt32(UpdateEmployeeLocaleobj.MenuID));
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return new JsonResult(false);
        }
        #endregion


        #region ::: DeleteCompanyEmployee Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// to Delete the Company Employee details
        /// </summary>
        /// 

        public static IActionResult DeleteCompanyEmployee(string connString, DeleteCompanyEmployeeList DeleteCompanyEmployeeobj)
        {
            string errorMsg = "";
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                //GNM_User User = DeleteCompanyEmployeeobj.UserDetails.FirstOrDefault();
                int userID = DeleteCompanyEmployeeobj.User_ID;
                int companyID = Convert.ToInt32(DeleteCompanyEmployeeobj.Company_ID);
                int branchID = Convert.ToInt32(DeleteCompanyEmployeeobj.Branch);

                JObject jobj = JObject.Parse(DeleteCompanyEmployeeobj.key);
                int rowCount = jobj["rows"].Count();

                using (SqlConnection con = new SqlConnection(connString))
                {
                    con.Open();
                    SqlTransaction transaction = con.BeginTransaction();

                    try
                    {
                        for (int i = 0; i < rowCount; i++)
                        {
                            int id = Convert.ToInt32(jobj["rows"].ElementAt(i)["id"].ToString());

                            using (SqlCommand cmd = new SqlCommand("SP_DeleteCompanyEmployee", con, transaction))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@Company_Employee_ID", id);

                                cmd.ExecuteNonQuery();
                            }

                            // gbl.InsertGPSDetails(companyID, branchID, userID, Common.GetObjectID("CoreEmployeeMaster"), id, 0, 0, "Deleted", false, Convert.ToInt32(DeleteCompanyEmployeeobj.MenuID), Convert.ToDateTime(DeleteCompanyEmployeeobj.LoggedINDateTime.ToString()), null);
                        }

                        transaction.Commit();
                        errorMsg += CommonFunctionalities.GetResourceString(DeleteCompanyEmployeeobj.UserCulture.ToString(), "deletedsuccessfully").ToString();
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();

                        if (ex.InnerException != null && ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                        {
                            errorMsg += CommonFunctionalities.GetResourceString(DeleteCompanyEmployeeobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                        }
                        else
                        {
                            errorMsg += CommonFunctionalities.GetResourceString(DeleteCompanyEmployeeobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(errorMsg);
        }
        #endregion


        #region ::: CheckEmployeeSkills Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// to check if the employee is already associated with the skills
        /// </summary>
        /// 

        public static IActionResult CheckEmployeeSkills(string connString, CheckEmployeeSkillsList CheckEmployeeSkillsobj)
        {
            int status = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection con = new SqlConnection(connString))
                {
                    con.Open();

                    using (SqlCommand cmd = new SqlCommand("SP_CheckEmployeeSkills", con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Skillset_ID", CheckEmployeeSkillsobj.skillID);
                        cmd.Parameters.AddWithValue("@Employee_ID", CheckEmployeeSkillsobj.employeeID);
                        cmd.Parameters.AddWithValue("@Employee_Skillset_ID", CheckEmployeeSkillsobj.primaryKey);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                int employeeSkillsetID = reader.GetInt32(reader.GetOrdinal("Employee_Skillset_ID"));
                                if (employeeSkillsetID != CheckEmployeeSkillsobj.primaryKey)
                                {
                                    status = 1;
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(status);
        }
        #endregion


        #region ::: SelAllEmployeeSkillset Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// To select all the skills of the particular employee
        /// </summary>
        /// 

        public static IActionResult SelAllEmployeeSkillset(string connString, SelAllEmployeeSkillsetList SelAllEmployeeSkillsetobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int userLanguageID = Convert.ToInt32(SelAllEmployeeSkillsetobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelAllEmployeeSkillsetobj.GeneralLanguageID);
                int count = 0;
                int total = 0;

                List<GNM_CompanyEmployeeSkillset> employeeList = new List<GNM_CompanyEmployeeSkillset>();
                List<GNM_RefMaster> refMaster = new List<GNM_RefMaster>();
                List<GNM_RefMasterDetail> refDetail = new List<GNM_RefMasterDetail>();
                List<GNM_RefMasterDetailLocale> refDetailLocale = new List<GNM_RefMasterDetailLocale>();

                using (SqlConnection con = new SqlConnection(connString))
                {
                    con.Open();

                    // Fetch employee skillset data using stored procedure
                    using (SqlCommand cmd = new SqlCommand("SelAllEmployeeSkillset", con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@EmployeeID", SelAllEmployeeSkillsetobj.employeeID);
                        cmd.Parameters.AddWithValue("@Sidx", sidx);
                        cmd.Parameters.AddWithValue("@Sord", sord);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                employeeList.Add(new GNM_CompanyEmployeeSkillset
                                {
                                    Employee_Skillset_ID = Convert.ToInt32(reader["Employee_Skillset_ID"]),
                                    CompnayEmployee_ID = Convert.ToInt32(reader["CompnayEmployee_ID"]),
                                    Skillset_ID = Convert.ToInt32(reader["Skillset_ID"]),
                                    Employee_Skillset_Rating = Convert.ToInt32(reader["Employee_Skillset_Rating"]),
                                    Level_ID = Convert.ToInt32(reader["Level_ID"])
                                });
                            }
                        }
                    }

                    // Fetch RefMaster data using stored procedure
                    using (SqlCommand cmd = new SqlCommand("GetAllRefMaster", con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                refMaster.Add(new GNM_RefMaster
                                {
                                    RefMaster_ID = Convert.ToInt32(reader["RefMaster_ID"]),
                                    RefMaster_Name = reader["RefMaster_Name"].ToString()
                                });
                            }
                        }
                    }

                    // Fetch RefMasterDetail data using stored procedure
                    using (SqlCommand cmd = new SqlCommand("GetAllRefMasterDetail", con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                refDetail.Add(new GNM_RefMasterDetail
                                {
                                    RefMasterDetail_ID = Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                    RefMaster_ID = Convert.ToInt32(reader["RefMaster_ID"]),
                                    RefMasterDetail_Name = reader["RefMasterDetail_Name"].ToString(),
                                    RefMasterDetail_IsActive = Convert.ToBoolean(reader["RefMasterDetail_IsActive"])
                                });
                            }
                        }
                    }

                    // Fetch RefMasterDetailLocale data using stored procedure
                    using (SqlCommand cmd = new SqlCommand("GetAllRefMasterDetailLocale", con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                refDetailLocale.Add(new GNM_RefMasterDetailLocale
                                {
                                    RefMasterDetail_ID = Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                    RefMasterDetail_Name = reader["RefMasterDetail_Name"].ToString(),
                                    Language_ID = Convert.ToInt32(reader["Language_ID"])
                                });
                            }
                        }
                    }
                }

                count = employeeList.Count;
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                // Create skill names string
                var skillArray = from a in refMaster
                                 join b in refDetail on a.RefMaster_ID equals b.RefMaster_ID
                                 where a.RefMaster_Name == "SKILLS" && b.RefMasterDetail_IsActive == true
                                 orderby b.RefMasterDetail_Name
                                 select new
                                 {
                                     b.RefMasterDetail_ID,
                                     b.RefMasterDetail_Name
                                 };

                string skillNames = "0:--" + CommonFunctionalities.GetResourceString(SelAllEmployeeSkillsetobj.UserCulture.ToString(), "SelectDDl").ToString() + "--;";
                foreach (var taxObj in skillArray)
                {
                    skillNames += taxObj.RefMasterDetail_ID + ":" + taxObj.RefMasterDetail_Name.Replace(";", ":") + ";";
                }
                skillNames = skillNames.TrimEnd(new char[] { ';' });

                dynamic s = null;

                if (SelAllEmployeeSkillsetobj.languageID == generalLanguageID)
                {
                    var arrSkillsList = from a in employeeList
                                        join b in refDetail on a.Skillset_ID equals b.RefMasterDetail_ID
                                        select new EmployeeSkillData()
                                        {
                                            edit = "<a title=" + CommonFunctionalities.GetGlobalResourceObject(SelAllEmployeeSkillsetobj.UserCulture.ToString(), "edit").ToString() + " href='#' id='" + a.Employee_Skillset_ID + "'key='" + a.Employee_Skillset_ID + "' class='editEmployeeSkills font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                            delete = "<input type='checkbox' key='" + a.Employee_Skillset_ID + "' id='chk" + a.Employee_Skillset_ID + "' class='chkEmployeeSkillDelete'/>",
                                            Employee_Skillset_ID = a.Employee_Skillset_ID,
                                            Skillset = b.RefMasterDetail_Name,
                                            Level_ID = refDetail.Where(k => k.RefMasterDetail_ID == a.Level_ID).FirstOrDefault().RefMasterDetail_Name,
                                            Employee_Skillset_Rating = a.Employee_Skillset_Rating
                                        };

                    var arrSkillsListIQ = arrSkillsList.AsQueryable<EmployeeSkillData>();

                    // FilterToolBar Search
                    if (_search)
                    {
                        Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                        arrSkillsListIQ = arrSkillsListIQ.FilterSearch<EmployeeSkillData>(filtersObj);
                    }

                    var arr = arrSkillsListIQ.Skip((page - 1) * rows).Take(rows).ToList();

                    s = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        data = arr.ToArray(),
                        skillNames
                    };
                }
                else
                {
                    var arrLocaleSkillsList = from a in employeeList
                                              join c in refDetail on a.Skillset_ID equals c.RefMasterDetail_ID
                                              join b in refDetailLocale on a.Skillset_ID equals b.RefMasterDetail_ID into tempLoc
                                              from d in tempLoc.DefaultIfEmpty(new GNM_RefMasterDetailLocale() { RefMasterDetail_ID = 0, RefMasterDetail_Name = "", Language_ID = userLanguageID })
                                              join l in refDetail on a.Skillset_ID equals l.RefMasterDetail_ID
                                              join l1 in refDetailLocale on a.Level_ID equals l1.RefMasterDetail_ID into levelLoc
                                              from l2 in levelLoc.DefaultIfEmpty(new GNM_RefMasterDetailLocale() { RefMasterDetail_ID = 0, RefMasterDetail_Name = "", Language_ID = userLanguageID })
                                              where d.Language_ID == userLanguageID
                                              select new EmployeeSkillData()
                                              {
                                                  Skillset = d.RefMasterDetail_Name,
                                                  Employee_Skillset_Rating = a.Employee_Skillset_Rating,
                                                  Level_ID = l2.RefMasterDetail_Name
                                              };

                    var arrSkillsListIQ = arrLocaleSkillsList.AsQueryable<EmployeeSkillData>();

                    // FilterToolBar Search
                    if (_search)
                    {
                        Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                        arrSkillsListIQ = arrSkillsListIQ.FilterSearch<EmployeeSkillData>(filtersObj);
                    }

                    var arr = arrSkillsListIQ.Skip((page - 1) * rows).Take(rows).ToList();

                    s = new
                    {
                        total = total,
                        page = page,
                        records = arrSkillsListIQ.Count(),
                        data = arr.ToArray()
                    };
                }

                return new JsonResult(s);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }


        #endregion


        #region ::: SelEmployeeTrainingDetails Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// to select all the skills of the particular employee
        /// </summary>
        /// 

        public static IActionResult SelEmployeeTrainingDetails(string connString, SelEmployeeTrainingDetailsList SelEmployeeTrainingDetailsobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            string AppPath = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (var connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Get Employee Training Details
                    var employeeTrainingDetailList = new List<GNM_EmployeeTrainingDetails>();
                    using (var command = new SqlCommand("GetEmployeeTrainingDetails", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@EmployeeID", SelEmployeeTrainingDetailsobj.employeeID);
                        command.Parameters.AddWithValue("@SortColumn", sidx);
                        command.Parameters.AddWithValue("@SortOrder", sord);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                employeeTrainingDetailList.Add(new GNM_EmployeeTrainingDetails
                                {
                                    TrainingDetails_ID = Convert.ToInt32(reader["TrainingDetails_ID"]),
                                    Employee_ID = Convert.ToInt32(reader["Employee_ID"]),
                                    Brand_ID = reader["Brand_ID"] as int?,
                                    ProductType_ID = reader["ProductType_ID"] as int?,
                                    TrainingName = reader["TrainingName"] as string,
                                    Level_ID = reader["Level_ID"] as int?,
                                    FromDate = reader["FromDate"] as DateTime?,
                                    ToDate = reader["ToDate"] as DateTime?,
                                    Evaluation = reader["Evaluation"] as string,
                                    Faculty = reader["Faculty"] as string,
                                    Status = reader["Status"] as int?
                                });
                            }
                        }
                    }

                    // Get RefMaster Details for "BRAND", "LEVEL", "EMPLOYEETRAININGSTATUS"
                    var BrandArray = new List<GNM_RefMasterDetail>();
                    var LevelArray = new List<GNM_RefMasterDetail>();
                    var StatusArray = new List<GNM_RefMasterDetail>();
                    var ProductTypeArray = new List<GNM_ProductType>();

                    foreach (var refMasterName in new[] { "BRAND", "LEVEL", "EMPLOYEETRAININGSTATUS" })
                    {
                        using (var command = new SqlCommand("GetRefMasterDetailsByName", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@RefMasterName", refMasterName);
                            using (var reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var detail = new GNM_RefMasterDetail
                                    {
                                        RefMasterDetail_ID = Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                        RefMasterDetail_Name = reader["RefMasterDetail_Name"] as string
                                    };

                                    if (refMasterName == "BRAND")
                                        BrandArray.Add(detail);
                                    else if (refMasterName == "LEVEL")
                                        LevelArray.Add(detail);
                                    else if (refMasterName == "EMPLOYEETRAININGSTATUS")
                                        StatusArray.Add(detail);
                                }
                            }
                        }
                    }

                    // Get Product Types
                    using (var command = new SqlCommand("GetProductType", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ProductTypeArray.Add(new GNM_ProductType
                                {
                                    ProductType_ID = Convert.ToInt32(reader["ProductType_ID"]),
                                    ProductType_Name = reader["ProductType_Name"] as string,
                                });
                            }
                        }
                    }

                    // Construct dropdown value strings
                    string ConstructDropdownString(List<GNM_RefMasterDetail> items)
                    {
                        string dropdownString = $"0:--{CommonFunctionalities.GetResourceString(SelEmployeeTrainingDetailsobj.UserCulture.ToString(), "SelectDDl").ToString()}--;";
                        foreach (var item in items)
                        {
                            dropdownString += $"{item.RefMasterDetail_ID}:{item.RefMasterDetail_Name.Replace(";", ":")};";
                        }
                        return dropdownString.TrimEnd(';');
                    }

                    string BrandNames = ConstructDropdownString(BrandArray);
                    string LevelNames = ConstructDropdownString(LevelArray);
                    string StatusNames = ConstructDropdownString(StatusArray);
                    string ProductTypeNames = ConstructProductTypeDropdownString(ProductTypeArray);

                    // New method for Product Type dropdown
                    string ConstructProductTypeDropdownString(List<GNM_ProductType> items)
                    {
                        string dropdownString = $"0:--{CommonFunctionalities.GetResourceString(SelEmployeeTrainingDetailsobj.UserCulture.ToString(), "SelectDDl").ToString()}--;";
                        foreach (var item in items)
                        {
                            dropdownString += $"{item.ProductType_ID}:{item.ProductType_Name.Replace(";", ":")};";
                        }
                        return dropdownString.TrimEnd(';');
                    }

                    var arr = employeeTrainingDetailList.Skip((page - 1) * rows).Take(rows)
                        .Select(detail => new
                        {
                            edit = $"<a title='{CommonFunctionalities.GetGlobalResourceObject(SelEmployeeTrainingDetailsobj.UserCulture.ToString(), "edit").ToString()}' href='#' id='{detail.TrainingDetails_ID}' key='{detail.TrainingDetails_ID}' class='editEmployeeTrainingSkills font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                            Employee_ID = detail.Employee_ID,
                            delete = $"<input type='checkbox' key='{detail.TrainingDetails_ID}' id='chk{detail.TrainingDetails_ID}' class='chkEmployeeTrainingSkillDelete'/>",
                            TrainingDetails_ID = detail.TrainingDetails_ID,
                            Brand_ID = detail.Brand_ID == null ? "" : BrandArray.FirstOrDefault(b => b.RefMasterDetail_ID == detail.Brand_ID)?.RefMasterDetail_Name,
                            ProductType_ID = detail.ProductType_ID == null ? "" : ProductTypeArray.FirstOrDefault(p => p.ProductType_ID == detail.ProductType_ID)?.ProductType_Name,
                            TrainingName = detail.TrainingName ?? "",
                            Level_ID = detail.Level_ID == null ? "" : LevelArray.FirstOrDefault(l => l.RefMasterDetail_ID == detail.Level_ID)?.RefMasterDetail_Name,
                            FromDate = detail.FromDate?.ToString("dd-MMM-yyyy") ?? "",
                            ToDate = detail.ToDate?.ToString("dd-MMM-yyyy") ?? "",
                            Evaluation = detail.Evaluation ?? "",
                            Faculty = detail.Faculty ?? "",
                            Status = detail.Status == null || detail.Status == 0 ? "" : StatusArray.FirstOrDefault(s => s.RefMasterDetail_ID == detail.Status)?.RefMasterDetail_Name,
                            Local = $"<a key='{detail.TrainingDetails_ID}' id='{detail.TrainingDetails_ID}' src='{AppPath}/Content/local.png' class='TrainingLocale' width='20' height='20' alt='Localize' title='Localize'><i class='fa fa-globe'></i></a>",
                            productTypeID = detail.ProductType_ID,
                            BrandID = detail.Brand_ID
                        }).ToList();

                    var sortedemployeeTrainingDetailList = arr.AsQueryable().OrderByField(sidx, sord);

                    if (_search)
                    {
                        Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                        sortedemployeeTrainingDetailList = sortedemployeeTrainingDetailList.FilterSearch(filtersObj);
                    }

                    // Construct response data
                    int count = sortedemployeeTrainingDetailList.Count();
                    int total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                    // Construct response object
                    var response = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        data = sortedemployeeTrainingDetailList,
                        BrandValues = BrandNames,
                        ProductTypeValues = ProductTypeNames,
                        LevelValues = LevelNames,
                        StatusValues = StatusNames
                    };

                    return new JsonResult(response);
                }
            }
            catch (Exception ex)
            {
                // Log the exception
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(null);
            }
        }




        #endregion


        #region ::: CheckETODetails Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// CheckETODetails
        /// </summary>
        /// 
        public static IActionResult CheckETODetails(string connString, CheckETODetailsList CheckETODetailsobj)
        {
            int Count = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("CheckETODetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters for the stored procedure
                        cmd.Parameters.AddWithValue("@Year", CheckETODetailsobj.Year);
                        cmd.Parameters.AddWithValue("@EmployeeID", CheckETODetailsobj.employeeID);

                        // Open the connection
                        conn.Open();

                        // Execute the command and retrieve the count
                        Count = (int)cmd.ExecuteScalar();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(Count);
        }


        #endregion


        #region ::: CheckETODetailsWithBankCode Uday kumar J B 27-08-2024:::
        /// <summary>
        /// CheckETODetailsWithBankCode
        /// </summary>
        /// 

        public static IActionResult CheckETODetailsWithBankCode(string connString, CheckETODetailsWithBankCodelist CheckETODetailsWithBankCodeobj)
        {
            int Count = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("usp_CheckETODetailsWithBankCode", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@Year", CheckETODetailsWithBankCodeobj.Year);
                        cmd.Parameters.AddWithValue("@EmployeeID", CheckETODetailsWithBankCodeobj.employeeID);
                        cmd.Parameters.AddWithValue("@BankCodeID", CheckETODetailsWithBankCodeobj.BankCodeID);

                        conn.Open();

                        object result = cmd.ExecuteScalar();
                        if (result != null)
                        {
                            Count = Convert.ToInt32(result);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(Count);
        }
        #endregion


        #region ::: SelEmployeeETODetails Uday Kumar J B 27-08-2024:::
        /// <summary>
        /// to select all the skills of the particular employee
        /// </summary>
        /// 

        public static IActionResult SelEmployeeETODetails(string connString, SelEmployeeETODetailsList SelEmployeeETODetailsobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    SqlCommand cmd;
                    SqlDataAdapter da;

                    // Get Financial Year List
                    cmd = new SqlCommand("GetFinancialYearList", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    da = new SqlDataAdapter(cmd);
                    DataTable dtYearList = new DataTable();
                    da.Fill(dtYearList);

                    // Get Bank Code List
                    cmd = new SqlCommand("GetBankCodeList", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    da = new SqlDataAdapter(cmd);
                    DataTable dtBankCodeList = new DataTable();
                    da.Fill(dtBankCodeList);

                    // Get Employee ETO Details
                    cmd = new SqlCommand("GetEmployeeETODetails", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@EmployeeID", SelEmployeeETODetailsobj.employeeID);
                    da = new SqlDataAdapter(cmd);
                    DataTable dtEmployeeETOList = new DataTable();
                    da.Fill(dtEmployeeETOList);

                    // Check Nethris Default
                    cmd = new SqlCommand("CheckNethrisDefault", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@EmployeeID", SelEmployeeETODetailsobj.employeeID);
                    bool NethrisDefault = (int)cmd.ExecuteScalar() == 1;

                    // Prepare Year and Bank Code strings
                    string Year = "0:--" + CommonFunctionalities.GetResourceString(SelEmployeeETODetailsobj.UserCulture.ToString(), "select").ToString() + "--;";
                    foreach (DataRow row in dtYearList.Rows)
                    {
                        Year += row["Company_FinancialYear"].ToString() + ":" + row["Company_FinancialYear"].ToString().Replace(";", ":") + ";";
                    }
                    Year = Year.TrimEnd(';');

                    string BankCode = "0:--" + CommonFunctionalities.GetResourceString(SelEmployeeETODetailsobj.UserCulture.ToString(), "select").ToString() + "--;";
                    foreach (DataRow row in dtBankCodeList.Rows)
                    {
                        BankCode += row["RefMasterDetail_ID"].ToString() + ":" + row["RefMasterDetail_Name"].ToString().Replace(";", ":") + ";";
                    }
                    BankCode = BankCode.TrimEnd(';');

                    // Process Employee ETO List
                    var arrETOList = dtEmployeeETOList.AsEnumerable().Select(row => new EmployeeETO
                    {
                        edit = (row.IsNull("UsedHours") || Convert.ToDecimal(row["UsedHours"]) == 0)
                            ? "<a title='" + CommonFunctionalities.GetGlobalResourceObject(SelEmployeeETODetailsobj.UserCulture.ToString(), "edit").ToString() +
                              "' href='#' id='" + row["EmployeeETO_ID"] + "' key='" + row["EmployeeETO_ID"] +
                              "' class='editEmployeeETO font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>"
                            : "",
                        delete = (row.IsNull("UsedHours") || Convert.ToDecimal(row["UsedHours"]) == 0)
                            ? "<input type='checkbox' key='" + row["EmployeeETO_ID"] + "' id='chk" + row["EmployeeETO_ID"] + "' class='chkEmployeeETODelete'/>"
                            : "",
                        ID = Convert.ToInt32(row["EmployeeETO_ID"]),
                        Employee_ID = Convert.ToInt32(row["Employee_ID"]),
                        Year = Convert.ToInt32(row["Year"]),
                        BankCodeCategory = row.IsNull("BankCode_ID") ? "" : dtBankCodeList.AsEnumerable()
                            .Where(x => x.Field<int>("RefMasterDetail_ID") == Convert.ToInt32(row["BankCode_ID"]))
                            .Select(x => x.Field<string>("RefMasterDetail_Name")).FirstOrDefault(),
                        HoursAvailable = row.IsNull("AvailableHours") ? "0:00" : SplitAndGet(row["AvailableHours"].ToString()),
                        UsedHours = row.IsNull("UsedHours") ? "0:00" : SplitAndGet(row["UsedHours"].ToString()),
                        PendingHours = row.IsNull("AvailableHours") || row.IsNull("UsedHours")
                            ? "0:00"
                            : SplitAndGet((Convert.ToDecimal(row["AvailableHours"]) - Convert.ToDecimal(row["UsedHours"])).ToString()),
                        CarryoverHours = row.IsNull("CarryoverHours") ? "0:00" : SplitAndGet(row["CarryoverHours"].ToString()),
                        UsedCarryoverHours = row.IsNull("UsedCarryoverHours") ? "0:00" : SplitAndGet(row["UsedCarryoverHours"].ToString()),
                        TotalUsedHours = row.IsNull("UsedCarryoverHours") || row.IsNull("UsedHours")
                            ? "0:00"
                            : SplitAndGet((Convert.ToDecimal(row["UsedCarryoverHours"]) + Convert.ToDecimal(row["UsedHours"])).ToString())
                    }).ToList();

                    var sortedETOList = arrETOList.AsQueryable().OrderByField(sidx, sord);

                    // Filter if necessary
                    if (_search)
                    {
                        Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                        sortedETOList = sortedETOList.FilterSearch<EmployeeETO>(filtersObj);
                    }

                    var result = new
                    {
                        total = 0, // Modify this to reflect total pages if necessary
                        page = page,
                        records = arrETOList.Count,
                        data = sortedETOList,
                        Year,
                        BankCode,
                        NethrisDefault
                    };

                    return new JsonResult(result);
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }

        #endregion


        #region ::: SplitAndGet Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// SplitAndGet
        /// </summary>
        public static string SplitAndGet(string SLATime)
        {
            double a = Convert.ToDouble(SLATime);
            string InTimeFormat = (Math.Floor(a / 60).ToString().Length == 1 ? ("0" + Math.Floor(a / 60).ToString()) : Math.Floor(a / 60).ToString()) + ":" + (Math.Floor(a % 60).ToString().Length == 1 ? "0" + Math.Floor(a % 60).ToString() : Math.Floor(a % 60).ToString());
            return InTimeFormat;
        }
        #endregion


        #region ::: SelIncentiveLogDetails Uday Kumar J B 27-08-2024:::
        /// <summary>
        /// to select all the skills of the particular employee
        /// </summary>
        /// 

        public static IActionResult SelIncentiveLogDetails(string connString, SelIncentiveLogDetailsList SelIncentiveLogDetailsobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int userLanguageID = Convert.ToInt32(SelIncentiveLogDetailsobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelIncentiveLogDetailsobj.GeneralLanguageID);

                List<dynamic> employeeETOList = new List<dynamic>();
                int count = 0;
                int total = 0;
                dynamic x = null;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Fetch Employee ETO Log Details
                    using (SqlCommand cmd = new SqlCommand("usp_GetEmployeeETOLogDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@EmployeeID", SelIncentiveLogDetailsobj.employeeID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                // Fetch ModifiedBy User Name using a separate command
                                string modifiedBy = "";
                                using (SqlCommand userCmd = new SqlCommand("usp_GetUserNameByID", conn))
                                {
                                    userCmd.CommandType = CommandType.StoredProcedure;
                                    userCmd.Parameters.AddWithValue("@UserID", Convert.ToInt32(reader["ModifiedBy"]));
                                    modifiedBy = userCmd.ExecuteScalar()?.ToString() ?? "";
                                }

                                // Populate the list
                                employeeETOList.Add(new
                                {
                                    edit = $"<a title='{CommonFunctionalities.GetResourceString(SelIncentiveLogDetailsobj.UserCulture.ToString(), "edit").ToString()}' href='#' id='{reader["ID"]}' key='{reader["ID"]}' class='editEmployeeETOLog font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                    delete = $"<input type='checkbox' key='{reader["ID"]}' id='chk{reader["ID"]}' class='chkEmployeeETOLogDelete'/>",
                                    ID = Convert.ToInt32(reader["ID"]),
                                    IsAdded = Convert.ToBoolean(reader["IsAdded"]) ? CommonFunctionalities.GetResourceString(SelIncentiveLogDetailsobj.UserCulture.ToString(), "yes").ToString() : CommonFunctionalities.GetResourceString(SelIncentiveLogDetailsobj.UserCulture.ToString(), "no").ToString(),
                                    Employee_ID = Convert.ToInt32(reader["Employee_ID"]),
                                    ModifiedBy = modifiedBy,
                                    ModifiedDate = reader["ModifiedDate"] != DBNull.Value ? Convert.ToDateTime(reader["ModifiedDate"]).ToString("dd-MMM-yyyy hh:mm tt") : "",
                                    Remarks = Convert.ToBoolean(reader["IsAdded"]) ? CommonFunctionalities.GetResourceString(SelIncentiveLogDetailsobj.UserCulture.ToString(), "Added").ToString() : CommonFunctionalities.GetResourceString(SelIncentiveLogDetailsobj.UserCulture.ToString(), "Removed").ToString()
                                });
                            }
                        }
                    }

                    // Sorting using the updated OrderByField method for dynamic objects
                    var sortedemployeeETOList = employeeETOList.AsQueryable();

                    if (_search)
                    {
                        Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                        sortedemployeeETOList = sortedemployeeETOList.FilterSearch(filtersObj);
                    }

                    // Pagination
                    count = sortedemployeeETOList.Count();
                    total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                    employeeETOList = sortedemployeeETOList.Skip((page - 1) * rows).Take(rows).ToList();

                    x = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        rows = employeeETOList
                    };
                }

                return new JsonResult(x);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }

        #endregion


        #region ::: SelectSpecificEmployeeTrainingLocaleDetails Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// SelectSpecificEmployeeTrainingLocaleDetails
        /// </summary>
        /// 
        public static IActionResult SelectSpecificEmployeeTrainingLocaleDetails(string connString, SelectSpecificEmployeeTrainingLocaleDetailsList SelectSpecificEmployeeTrainingLocaleDetailsobj)
        {
            var x = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int Language_ID = Convert.ToInt32(SelectSpecificEmployeeTrainingLocaleDetailsobj.UserLanguageID);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Fetch English Row (GNM_EmployeeTrainingDetails)
                    GNM_EmployeeTrainingDetails englishRow = null;
                    using (SqlCommand cmd = new SqlCommand("usp_GetEmployeeTrainingDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@TrainingDetails_ID", SelectSpecificEmployeeTrainingLocaleDetailsobj.id);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                englishRow = new GNM_EmployeeTrainingDetails
                                {
                                    TrainingDetails_ID = Convert.ToInt32(reader["TrainingDetails_ID"]),
                                    TrainingName = reader["TrainingName"].ToString(),
                                    Evaluation = reader["Evaluation"].ToString(),
                                    Faculty = reader["Faculty"].ToString(),
                                    Brand_ID = reader["Brand_ID"] as int?,
                                    ProductType_ID = reader["ProductType_ID"] as int?,
                                    Level_ID = reader["Level_ID"] as int?,
                                    FromDate = reader["FromDate"] as DateTime?,
                                    ToDate = reader["ToDate"] as DateTime?,
                                    Status = Convert.ToInt32(reader["Status"])
                                };
                            }
                        }
                    }

                    // Fetch Locale Row (GNM_EmployeeTrainingLocaleDetails)
                    GNM_EmployeeTrainingLocaleDetails localeRow = null;
                    using (SqlCommand cmd = new SqlCommand("usp_GetEmployeeTrainingLocaleDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Training_ID", SelectSpecificEmployeeTrainingLocaleDetailsobj.id);
                        cmd.Parameters.AddWithValue("@Language_ID", Language_ID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                localeRow = new GNM_EmployeeTrainingLocaleDetails
                                {
                                    Training_ID = Convert.ToInt32(reader["Training_ID"]),
                                    TrainingLocale_Name = reader["TrainingLocale_Name"].ToString(),
                                    TrainingLocale_Evaluation = reader["TrainingLocale_Evaluation"].ToString(),
                                    TrainingLocale_Faculty = reader["TrainingLocale_Faculty"].ToString()
                                };
                            }
                        }
                    }

                    // Fetch additional details (Brand Name, Product Type Name, Level Name, Status Name)
                    string brandName = string.Empty, productTypeName = string.Empty, levelName = string.Empty, statusName = string.Empty;

                    if (englishRow.Brand_ID.HasValue)
                    {
                        using (SqlCommand cmd = new SqlCommand("usp_GetRefMasterDetailName", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@RefMasterDetail_ID", englishRow.Brand_ID.Value);
                            brandName = cmd.ExecuteScalar()?.ToString() ?? string.Empty;
                        }
                    }

                    if (englishRow.ProductType_ID.HasValue)
                    {
                        using (SqlCommand cmd = new SqlCommand("usp_GetProductTypeName", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@ProductType_ID", englishRow.ProductType_ID.Value);
                            productTypeName = cmd.ExecuteScalar()?.ToString() ?? string.Empty;
                        }
                    }

                    if (englishRow.Level_ID.HasValue)
                    {
                        using (SqlCommand cmd = new SqlCommand("usp_GetRefMasterDetailName", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@RefMasterDetail_ID", englishRow.Level_ID.Value);
                            levelName = cmd.ExecuteScalar()?.ToString() ?? string.Empty;
                        }
                    }

                    using (SqlCommand cmd = new SqlCommand("usp_GetRefMasterDetailName", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@RefMasterDetail_ID", englishRow.Status);
                        statusName = cmd.ExecuteScalar()?.ToString() ?? string.Empty;
                    }

                    // Construct the dynamic object `x`
                    if (localeRow != null)
                    {
                        x = new
                        {
                            EmpTrainingNameG = englishRow.TrainingName,
                            EmpTrainingDetail_IDG = englishRow.TrainingDetails_ID,
                            EmpTrainingEvaluationG = englishRow.Evaluation,
                            EmpTrainingFacultyG = englishRow.Faculty,

                            EmpTrainingLocale_ID = localeRow.Training_ID,
                            EmpTrainingNameN = localeRow.TrainingLocale_Name,
                            EmpTrainingEvaluationN = localeRow.TrainingLocale_Evaluation,
                            EmpTrainingFacultyN = localeRow.TrainingLocale_Faculty,

                            EmpTrainingBrandNameG = brandName,
                            EmpTrainingProductType_NameG = productTypeName,
                            EmpTrainingLevel_NameG = levelName,
                            EmpTrainingFromDateG = englishRow.FromDate?.ToString("dd-MMM-yyyy") ?? "",
                            EmpTrainingToDateG = englishRow.ToDate?.ToString("dd-MMM-yyyy") ?? "",
                            EmpTrainingStatusG = statusName
                        };
                    }
                    else
                    {
                        x = new
                        {
                            EmpTrainingNameG = englishRow.TrainingName,
                            EmpTrainingDetail_IDG = englishRow.TrainingDetails_ID,
                            EmpTrainingEvaluationG = englishRow.Evaluation,
                            EmpTrainingFacultyG = englishRow.Faculty,

                            EmpTrainingBrandNameG = brandName,
                            EmpTrainingProductType_NameG = productTypeName,
                            EmpTrainingLevel_NameG = levelName,
                            EmpTrainingFromDateG = englishRow.FromDate?.ToString("dd-MMM-yyyy") ?? "",
                            EmpTrainingToDateG = englishRow.ToDate?.ToString("dd-MMM-yyyy") ?? "",
                            EmpTrainingStatusG = statusName,

                            EmpTrainingLocale_ID = "",
                            EmpTrainingNameN = "",
                            EmpTrainingEvaluationN = "",
                            EmpTrainingFacultyN = ""
                        };
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                // return RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                // return RedirectToAction("Error");
            }

            return new JsonResult(x);
        }

        #endregion


        #region ::: SaveEmpTrainingLocaleContactdetails Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// SaveEmpTrainingLocaleContactdetails
        /// </summary>
        /// 

        public static IActionResult SaveEmpTrainingLocaleContactdetails(string connString, SaveEmpTrainingLocaleContactdetailsList SaveEmpTrainingLocaleContactdetailsobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // Parse and decrypt incoming data
                JObject jobj = JObject.Parse(SaveEmpTrainingLocaleContactdetailsobj.LocaleData);
                GNM_EmployeeTrainingLocaleDetails rowObj = jobj.ToObject<GNM_EmployeeTrainingLocaleDetails>();
                rowObj.TrainingLocale_Name = Common.DecryptString(rowObj.TrainingLocale_Name);
                rowObj.TrainingLocale_Evaluation = Common.DecryptString(rowObj.TrainingLocale_Evaluation);
                rowObj.TrainingLocale_Faculty = Common.DecryptString(rowObj.TrainingLocale_Faculty);

                int result = 0;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand())
                    {
                        cmd.Connection = conn;
                        if (rowObj.TrainingLocaleDetails_ID == 0)
                        {
                            // Insert new record
                            cmd.CommandText = "usp_InsertEmployeeTrainingLocaleDetails";
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Training_ID", rowObj.Training_ID);
                            cmd.Parameters.AddWithValue("@Language_ID", rowObj.Language_ID);
                            cmd.Parameters.AddWithValue("@TrainingLocale_Name", rowObj.TrainingLocale_Name);
                            cmd.Parameters.AddWithValue("@TrainingLocale_Evaluation", rowObj.TrainingLocale_Evaluation);
                            cmd.Parameters.AddWithValue("@TrainingLocale_Faculty", rowObj.TrainingLocale_Faculty);
                            result = Convert.ToInt32(cmd.ExecuteScalar());
                        }
                        else
                        {
                            int adjustedID = rowObj.TrainingLocaleDetails_ID - 1;
                            // Update existing record
                            cmd.CommandText = "usp_UpdateEmployeeTrainingLocaleDetails";
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@TrainingLocaleDetails_ID", adjustedID);
                            cmd.Parameters.AddWithValue("@TrainingLocale_Name", rowObj.TrainingLocale_Name);
                            cmd.Parameters.AddWithValue("@TrainingLocale_Evaluation", rowObj.TrainingLocale_Evaluation);
                            cmd.Parameters.AddWithValue("@TrainingLocale_Faculty", rowObj.TrainingLocale_Faculty);
                            cmd.ExecuteNonQuery();
                            result = rowObj.TrainingLocaleDetails_ID;
                        }
                    }
                }
                return new JsonResult(result);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(0);
        }

        #endregion


        #region ::: Insert Training details Uday Kumar J B 27-08-2024:::
        /// <summary>
        /// To Save Training Details
        /// </summary>
        /// 

        public static IActionResult SaveTrainingDetails(string connString, SaveTrainingDetailsList SaveTrainingDetailsobj)
        {
            string message = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int Company_ID = Convert.ToInt32(SaveTrainingDetailsobj.Company_ID);
                bool isDuplicate = false;
                //int recordCount = 0;
                JObject jObj = JObject.Parse(SaveTrainingDetailsobj.data);
                int count = jObj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int i = 0; i < count; i++)
                    {
                        GNM_EmployeeTrainingDetails mRow = jObj["rows"].ElementAt(i).ToObject<GNM_EmployeeTrainingDetails>();

                        // Check for duplicates
                        using (SqlCommand cmd = new SqlCommand("usp_CheckDuplicateEmployeeTrainingDetails", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Brand_ID", mRow.Brand_ID == -1 ? (object)DBNull.Value : mRow.Brand_ID);
                            cmd.Parameters.AddWithValue("@ProductType_ID", mRow.ProductType_ID == -1 ? (object)DBNull.Value : mRow.ProductType_ID);
                            cmd.Parameters.AddWithValue("@TrainingName", mRow.TrainingName);
                            cmd.Parameters.AddWithValue("@Level_ID", mRow.Level_ID == 0 ? (object)DBNull.Value : mRow.Level_ID);
                            cmd.Parameters.AddWithValue("@FromDate", mRow.FromDate);
                            cmd.Parameters.AddWithValue("@ToDate", mRow.ToDate);
                            cmd.Parameters.AddWithValue("@Evaluation", mRow.Evaluation);
                            cmd.Parameters.AddWithValue("@Faculty", mRow.Faculty);
                            cmd.Parameters.AddWithValue("@Status", mRow.Status == -1 ? (object)DBNull.Value : mRow.Status);

                            int recordCount = (int)cmd.ExecuteScalar();

                            if (recordCount == 0)
                            {
                                using (SqlCommand cmdInsertOrUpdate = new SqlCommand("usp_InsertOrUpdateEmployeeTrainingDetails", conn))
                                {
                                    cmdInsertOrUpdate.CommandType = CommandType.StoredProcedure;
                                    cmdInsertOrUpdate.Parameters.AddWithValue("@TrainingDetails_ID", mRow.TrainingDetails_ID);
                                    cmdInsertOrUpdate.Parameters.AddWithValue("@Employee_ID", mRow.Employee_ID);
                                    cmdInsertOrUpdate.Parameters.AddWithValue("@Brand_ID", mRow.Brand_ID == -1 ? (object)DBNull.Value : mRow.Brand_ID);
                                    cmdInsertOrUpdate.Parameters.AddWithValue("@ProductType_ID", mRow.ProductType_ID == -1 ? (object)DBNull.Value : mRow.ProductType_ID);
                                    cmdInsertOrUpdate.Parameters.AddWithValue("@TrainingName", mRow.TrainingName);
                                    cmdInsertOrUpdate.Parameters.AddWithValue("@Level_ID", mRow.Level_ID == 0 ? (object)DBNull.Value : mRow.Level_ID);
                                    cmdInsertOrUpdate.Parameters.AddWithValue("@FromDate", mRow.FromDate);
                                    cmdInsertOrUpdate.Parameters.AddWithValue("@ToDate", mRow.ToDate);
                                    cmdInsertOrUpdate.Parameters.AddWithValue("@Evaluation", mRow.Evaluation);
                                    cmdInsertOrUpdate.Parameters.AddWithValue("@Faculty", mRow.Faculty);
                                    cmdInsertOrUpdate.Parameters.AddWithValue("@Status", mRow.Status == -1 ? (object)DBNull.Value : mRow.Status);
                                    cmdInsertOrUpdate.ExecuteNonQuery();
                                }
                            }
                            else
                            {
                                if (mRow.TrainingDetails_ID != 0)
                                {
                                    using (SqlCommand cmdUpdate = new SqlCommand("usp_InsertOrUpdateEmployeeTrainingDetails", conn))
                                    {
                                        cmdUpdate.CommandType = CommandType.StoredProcedure;
                                        cmdUpdate.Parameters.AddWithValue("@TrainingDetails_ID", mRow.TrainingDetails_ID);
                                        cmdUpdate.Parameters.AddWithValue("@Employee_ID", mRow.Employee_ID);
                                        cmdUpdate.Parameters.AddWithValue("@Brand_ID", mRow.Brand_ID == -1 ? (object)DBNull.Value : mRow.Brand_ID);
                                        cmdUpdate.Parameters.AddWithValue("@ProductType_ID", mRow.ProductType_ID == -1 ? (object)DBNull.Value : mRow.ProductType_ID);
                                        cmdUpdate.Parameters.AddWithValue("@TrainingName", mRow.TrainingName);
                                        cmdUpdate.Parameters.AddWithValue("@Level_ID", mRow.Level_ID == 0 ? (object)DBNull.Value : mRow.Level_ID);
                                        cmdUpdate.Parameters.AddWithValue("@FromDate", mRow.FromDate);
                                        cmdUpdate.Parameters.AddWithValue("@ToDate", mRow.ToDate);
                                        cmdUpdate.Parameters.AddWithValue("@Evaluation", mRow.Evaluation);
                                        cmdUpdate.Parameters.AddWithValue("@Faculty", mRow.Faculty);
                                        cmdUpdate.Parameters.AddWithValue("@Status", mRow.Status == -1 ? (object)DBNull.Value : mRow.Status);
                                        cmdUpdate.ExecuteNonQuery();
                                    }
                                }
                            }
                            if (recordCount == 0 && mRow.TrainingDetails_ID == 0)
                            {
                                isDuplicate = true;
                            }
                        }


                    }
                    message = "Saved";
                    if (isDuplicate)
                    {
                        message = "Duplicate";
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                message = string.Empty;
            }
            return new JsonResult(message);
        }

        #endregion


        #region ::: Insert Employee ETO details Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// To Save Training Details
        /// </summary>
        /// 

        public static IActionResult SaveEmployeeETODetails(string connString, SaveEmployeeETODetailsList SaveEmployeeETODetailsobj)
        {
            string message = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int Company_ID = Convert.ToInt32(SaveEmployeeETODetailsobj.Company_ID);
                JObject jObj = JObject.Parse(SaveEmployeeETODetailsobj.data);
                int count = jObj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int i = 0; i < count; i++)
                    {
                        var row = jObj["rows"].ElementAt(i);
                        int? AvailableHours = row["AvailableHours"].ToString().Contains(":")
                            ? Convert.ToInt32(row["AvailableHours"].ToString().Split(':')[0])
                            : Convert.ToInt32(row["AvailableHours"].ToString());
                        int? Year = Convert.ToInt32(row["Year"].ToString());
                        int? BankCode_ID = Convert.ToInt32(row["BankCode_ID"].ToString());
                        int EmployeeETO_ID = Convert.ToInt32(row["EmployeeETO_ID"].ToString());
                        int Employee_ID = Convert.ToInt32(row["Employee_ID"].ToString());

                        int recordCount = 0;

                        // Check for duplicates
                        using (SqlCommand cmdCheckDuplicate = new SqlCommand("usp_CheckDuplicateEmployeeETODetails", conn))
                        {
                            cmdCheckDuplicate.CommandType = CommandType.StoredProcedure;
                            cmdCheckDuplicate.Parameters.AddWithValue("@Year", Year);
                            cmdCheckDuplicate.Parameters.AddWithValue("@Employee_ID", Employee_ID);
                            cmdCheckDuplicate.Parameters.AddWithValue("@BankCode_ID", BankCode_ID == -1 ? (object)DBNull.Value : BankCode_ID);

                            recordCount = (int)cmdCheckDuplicate.ExecuteScalar();
                        }

                        if (recordCount <= 1)
                        {
                            using (SqlCommand cmdInsertOrUpdate = new SqlCommand("usp_InsertOrUpdateEmployeeETODetails", conn))
                            {
                                cmdInsertOrUpdate.CommandType = CommandType.StoredProcedure;
                                cmdInsertOrUpdate.Parameters.AddWithValue("@EmployeeETO_ID", EmployeeETO_ID);
                                cmdInsertOrUpdate.Parameters.AddWithValue("@Employee_ID", Employee_ID);
                                cmdInsertOrUpdate.Parameters.AddWithValue("@Year", Year == -1 ? (object)DBNull.Value : Year);
                                cmdInsertOrUpdate.Parameters.AddWithValue("@BankCode_ID", BankCode_ID == -1 ? (object)DBNull.Value : BankCode_ID);
                                cmdInsertOrUpdate.Parameters.AddWithValue("@AvailableHours", AvailableHours * 60);
                                cmdInsertOrUpdate.Parameters.AddWithValue("@PendingHours", AvailableHours * 60);
                                cmdInsertOrUpdate.ExecuteNonQuery();
                            }

                            if (recordCount == 0)
                            {
                                message = "Saved";
                            }
                        }
                        else
                        {
                            message = "Duplicate";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                message = string.Empty;
            }
            return new JsonResult(message);
        }

        #endregion


        #region ::: SaveSkills Uday Kumar J B 27-08-2024:::
        /// <summary>
        /// to update the skills of the particular employee 
        /// </summary>
        /// 

        public static IActionResult SaveSkills(string connString, SaveSkillsList SaveSkillsobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jobj = JObject.Parse(SaveSkillsobj.Data);
                int rowCount = jobj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int j = 0; j < rowCount; j++)
                    {
                        var row = jobj["rows"].ElementAt(j);
                        string rating = row["rating"].ToString();
                        string skillset = row["skillset"].ToString();
                        string employeeID = row["employeeID"].ToString();
                        string levelID = row["Level_ID"].ToString();
                        string employeeSkillID = row["employeeSkillID"].ToString();

                        if (!string.IsNullOrEmpty(employeeSkillID))
                        {
                            // Update existing record
                            using (SqlCommand cmdUpdate = new SqlCommand("usp_UpdateCompanyEmployeeSkillset", conn))
                            {
                                cmdUpdate.CommandType = CommandType.StoredProcedure;
                                cmdUpdate.Parameters.AddWithValue("@Employee_Skillset_ID", Convert.ToInt32(employeeSkillID));
                                cmdUpdate.Parameters.AddWithValue("@Employee_Skillset_Rating", Convert.ToInt32(rating));
                                cmdUpdate.Parameters.AddWithValue("@Skillset_ID", Convert.ToInt32(skillset));
                                cmdUpdate.Parameters.AddWithValue("@CompnayEmployee_ID", Convert.ToInt32(employeeID));
                                cmdUpdate.Parameters.AddWithValue("@Level_ID", Convert.ToInt32(levelID));
                                cmdUpdate.ExecuteNonQuery();
                            }
                        }
                        else
                        {
                            // Insert new record
                            using (SqlCommand cmdInsert = new SqlCommand("usp_InsertCompanyEmployeeSkillset", conn))
                            {
                                cmdInsert.CommandType = CommandType.StoredProcedure;
                                cmdInsert.Parameters.AddWithValue("@Employee_Skillset_Rating", Convert.ToInt32(rating));
                                cmdInsert.Parameters.AddWithValue("@Skillset_ID", Convert.ToInt32(skillset));
                                cmdInsert.Parameters.AddWithValue("@CompnayEmployee_ID", Convert.ToInt32(employeeID));
                                cmdInsert.Parameters.AddWithValue("@Level_ID", Convert.ToInt32(levelID));
                                cmdInsert.ExecuteNonQuery();
                            }
                        }

                        // Log details
                        //gbl.InsertGPSDetails(
                        //    Convert.ToInt32(SaveSkillsobj.Company_ID),
                        //    Convert.ToInt32(SaveSkillsobj.Branch),
                        //    Convert.ToInt32(SaveSkillsobj.User_ID),
                        //    Convert.ToInt32(Common.GetObjectID("CoreEmployeeMaster")),
                        //    Convert.ToInt32(employeeID),
                        //    0,
                        //    0,
                        //    "Update",
                        //    false,
                        //    Convert.ToInt32(SaveSkillsobj.MenuID)
                        //);
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }


        #endregion


        #region ::: DeleteSkills Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// to Delete the Employee Skillset details
        /// </summary>
        /// 

        public static IActionResult DeleteSkills(string connString, DeleteSkillsList DeleteSkillsobj)
        {
            string errorMsg = "";
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jobj = JObject.Parse(DeleteSkillsobj.key);
                int rowCount = jobj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int i = 0; i < rowCount; i++)
                    {
                        int id = Convert.ToInt32(jobj["rows"].ElementAt(i)["id"].ToString());

                        using (SqlCommand cmd = new SqlCommand("usp_DeleteCompanyEmployeeSkillset", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Employee_Skillset_ID", id);

                            cmd.ExecuteNonQuery();
                        }

                        // Log details after successful deletion
                        // Note: You'll need to get the employeeID to log it
                        // Assuming you need the last processed employeeSkillID to log
                        //gbl.InsertGPSDetails(
                        //    Convert.ToInt32(DeleteSkillsobj.Company_ID),
                        //    Convert.ToInt32(DeleteSkillsobj.Branch),
                        //    Convert.ToInt32(DeleteSkillsobj.User_ID),
                        //    Convert.ToInt32(Common.GetObjectID("CoreEmployeeMaster")),
                        //    id, // Change this to the correct employeeID if needed
                        //    0,
                        //    0,
                        //    "Delete",
                        //    false,
                        //    Convert.ToInt32(DeleteSkillsobj.MenuID)
                        //);
                    }
                }

                errorMsg += CommonFunctionalities.GetResourceString(DeleteSkillsobj.UserCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (SqlException sqlEx)
            {
                // Check for foreign key constraint violations
                if (sqlEx.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    errorMsg += CommonFunctionalities.GetResourceString(DeleteSkillsobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    errorMsg += CommonFunctionalities.GetResourceString(DeleteSkillsobj.UserCulture.ToString(), "erroroccurred").ToString();
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                errorMsg += CommonFunctionalities.GetResourceString(DeleteSkillsobj.UserCulture.ToString(), "erroroccurred").ToString();
            }
            return new JsonResult(errorMsg);
        }


        #endregion


        #region ::: EmployeeExport need to do:::
        /// <summary>
        /// Exporting Branch Grid
        /// </summary>
        //public void EmployeeExport(int exprtType)
        //{
        //    try
        //    {
        //        GNM_User User = (GNM_User)Session["UserDetails"];
        //        int companyID = User.Company_ID;
        //        int branchID = Convert.ToInt32(Session["Branch"]);
        //        DataTable dt = new DataTable();

        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "EmployeeID").ToString());
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "Name").ToString());
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "Mobile").ToString());
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "Email").ToString());
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "Department").ToString());
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "Designation").ToString());
        //        //triveni--
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "Manager").ToString());
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "Country").ToString());
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "State").ToString());
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "Region").ToString());
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "Address").ToString());
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "Location").ToString());
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "ZipCode").ToString());
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "Landline").ToString());
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "ActiveFrom").ToString());
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "ActiveTo").ToString());
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "HourlyRate").ToString());
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "IsActive").ToString());
        //        //end

        //        DataTable dt1 = new DataTable();
        //        dt1.Columns.Add("EmployeeID");
        //        dt1.Columns.Add("Name");
        //        dt1.Columns.Add("Mobile");
        //        dt1.Columns.Add("Email");
        //        dt1.Columns.Add("Department");
        //        dt1.Columns.Add("Designation");
        //        //triveni
        //        dt1.Columns.Add("Manager");
        //        dt1.Columns.Add("Country");
        //        dt1.Columns.Add("State");
        //        dt1.Columns.Add("Region");
        //        dt1.Columns.Add("Address");
        //        dt1.Columns.Add("Location");
        //        dt1.Columns.Add("ZipCode");
        //        dt1.Columns.Add("Landline");
        //        dt1.Columns.Add("ActiveFrom");
        //        dt1.Columns.Add("ActiveTo");
        //        dt1.Columns.Add("HourlyRate");
        //        dt1.Columns.Add("IsActive");
        //        //end
        //        dt1.Rows.Add(0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
        //        List<EmployeeData> iEmployeeArray = new List<EmployeeData>();
        //        IQueryable<EmployeeData> QEmployeeArray = (IQueryable<EmployeeData>)Session["EmployeeData"];
        //        iEmployeeArray = QEmployeeArray.ToList();
        //        int cnt = iEmployeeArray.AsEnumerable().Count();
        //        for (int i = 0; i < cnt; i++)
        //        {
        //            dt.Rows.Add(iEmployeeArray.ElementAt(i).EmployeeCode, Server.HtmlDecode(iEmployeeArray.ElementAt(i).Company_Employee_Name), iEmployeeArray.ElementAt(i).Company_Employee_MobileNumber, Server.HtmlDecode(iEmployeeArray.ElementAt(i).Company_Employee_Email), Server.HtmlDecode(iEmployeeArray.ElementAt(i).Department), Server.HtmlDecode(iEmployeeArray.ElementAt(i).Designation), Server.HtmlDecode(iEmployeeArray.ElementAt(i).Manager), Server.HtmlDecode(iEmployeeArray.ElementAt(i).Country), Server.HtmlDecode(iEmployeeArray.ElementAt(i).State), Server.HtmlDecode(iEmployeeArray.ElementAt(i).RegionName), Server.HtmlDecode(iEmployeeArray.ElementAt(i).Address), iEmployeeArray.ElementAt(i).Location, Server.HtmlDecode(iEmployeeArray.ElementAt(i).Zipcode), Server.HtmlDecode(iEmployeeArray.ElementAt(i).Landline), Server.HtmlDecode(iEmployeeArray.ElementAt(i).ActiveFromstring), Server.HtmlDecode(iEmployeeArray.ElementAt(i).AtiveTostring), iEmployeeArray.ElementAt(i).HourlyRate, Server.HtmlDecode(iEmployeeArray.ElementAt(i).IsActive));
        //            //dt.Rows.Add(iEmployeeArray.ElementAt(i).EmployeeCode, iEmployeeArray.ElementAt(i).Company_Employee_Name, iEmployeeArray.ElementAt(i).Company_Employee_MobileNumber, iEmployeeArray.ElementAt(i).Company_Employee_Email, iEmployeeArray.ElementAt(i).Department, iEmployeeArray.ElementAt(i).Designation, iEmployeeArray.ElementAt(i).Manager, iEmployeeArray.ElementAt(i).Country, iEmployeeArray.ElementAt(i).State, iEmployeeArray.ElementAt(i).RegionName, iEmployeeArray.ElementAt(i).Address, iEmployeeArray.ElementAt(i).Location, iEmployeeArray.ElementAt(i).Zipcode,iEmployeeArray.ElementAt(i).Landline, iEmployeeArray.ElementAt(i).ActiveFromstring, iEmployeeArray.ElementAt(i).AtiveTostring, iEmployeeArray.ElementAt(i).HourlyRate, iEmployeeArray.ElementAt(i).IsActive);
        //        }
        //        DocumentExport.Export(exprtType, dt, dt1, "Employee", HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "Employee").ToString());
        //        gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"].ToString()), branchID, User.User_ID, Common.GetObjectID("CoreEmployeeMaster"), 0, 0, 0, "Employee - Export ", false, Convert.ToInt32(Session["MenuID"]), Convert.ToDateTime(Session["LoggedINDateTime"]));
        //    }
        //    catch (WebException wex)
        //    {
        //        LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

        //        RedirectToAction("Error");
        //    }
        //    catch (Exception ex)
        //    {
        //        if (LogException == 1)
        //        {
        //            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

        //        }
        //        RedirectToAction("Error");
        //    }
        //}
        #endregion


        #region ::: SelAllEmployeeBranches  Uday Kumar J B 27-08-2024:::
        /// <summary>
        /// to select all the branches associated with the Employee
        /// </summary>
        /// 

        public static IActionResult SelAllEmployeeBranches(string connString, SelAllEmployeeBranchesList SelAllEmployeeBranchesobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // Initialize the variables
                int count = 0;
                int total = 0;

                // Retrieve employee branch list using stored procedure
                DataTable dtEmployeeBranches = new DataTable();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("SelEmployeeBranches", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@EmployeeID", SelAllEmployeeBranchesobj.employeeID);
                        cmd.Parameters.AddWithValue("@LanguageID", SelAllEmployeeBranchesobj.languageID);

                        SqlDataAdapter da = new SqlDataAdapter(cmd);
                        da.Fill(dtEmployeeBranches);
                    }
                }

                count = dtEmployeeBranches.Rows.Count;
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                // Retrieve the company ID
                int companyID;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("SelEmployeeCompanyID", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@EmployeeID", SelAllEmployeeBranchesobj.employeeID);
                        conn.Open();
                        companyID = Convert.ToInt32(cmd.ExecuteScalar());
                    }
                }

                // Retrieve branch details
                DataTable dtBranches = new DataTable();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("SelBranchDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);
                        SqlDataAdapter da = new SqlDataAdapter(cmd);
                        da.Fill(dtBranches);
                    }
                }

                // Prepare branch names for selection dropdown
                string branchNames = "-1:--Select--;";
                foreach (DataRow row in dtBranches.Rows)
                {
                    branchNames += row["Branch_ID"] + ":" + row["Branch_Name"].ToString().Replace(";", ":") + ";";
                }
                branchNames = branchNames.TrimEnd(new char[] { ';' });

                // Prepare employee branch data
                List<EmployeeBranchData> employeeBranchDataList = new List<EmployeeBranchData>();
                foreach (DataRow row in dtEmployeeBranches.Rows)
                {
                    int branchID = Convert.ToInt32(row["Branch_ID"]);

                    // Retrieve additional branch details
                    string stateName = string.Empty, regionName = string.Empty, countryName = string.Empty;
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        using (SqlCommand cmd = new SqlCommand("SelAdditionalBranchDetails", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@BranchID", branchID);
                            cmd.Parameters.AddWithValue("@LanguageID", SelAllEmployeeBranchesobj.languageID);
                            conn.Open();
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    stateName = reader["State_Name"].ToString();
                                    regionName = reader["Region_Name"].ToString();
                                    countryName = reader["Country_Name"].ToString();
                                }
                            }
                        }
                    }

                    EmployeeBranchData data = new EmployeeBranchData
                    {
                        edit = "<a title=" + CommonFunctionalities.GetResourceString(SelAllEmployeeBranchesobj.UserCulture.ToString(), "edit").ToString() + " href='#' id='" + row["EmployeeBranch_ID"] + "'key='" + row["EmployeeBranch_ID"] + "' class='editEmployeeBranch font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                        delete = "<input type='checkbox' key='" + row["EmployeeBranch_ID"] + "' id='chk" + row["EmployeeBranch_ID"] + "' class='chkEmployeeBranchDelete'/>",
                        EmployeeBranch_ID = Convert.ToInt32(row["EmployeeBranch_ID"]),
                        Branch = row["Branch_Name"].ToString(),
                        State_Name = stateName,
                        Region_Name = regionName,
                        Country_Name = countryName,
                        IsDefault = Convert.ToBoolean(row["IsDefault"]) ? "Yes" : "No",
                        BranchID = branchID
                    };

                    employeeBranchDataList.Add(data);
                }

                // Sorting
                var sortedemployeeBranchDataList = employeeBranchDataList.AsQueryable().OrderByField(sidx, sord);

                // search
                if (_search)
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    sortedemployeeBranchDataList = sortedemployeeBranchDataList.FilterSearch(filtersObj);
                }

                // Sorting and Paging
                count = sortedemployeeBranchDataList.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                var paginatedList = sortedemployeeBranchDataList.Skip((page - 1) * rows).Take(rows).ToList();

                // Prepare the result
                var result = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = sortedemployeeBranchDataList,
                    BranchNames = branchNames
                };

                return new JsonResult(result);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }


        #endregion


        #region ::: SaveEmployeeBranch  Uday Kumar J B 27-08-2024:::
        /// <summary>
        /// to save Employee Branch Association
        /// </summary>
        /// 

        public static IActionResult SaveEmployeeBranch(string connString, SaveEmployeeBranchList SaveEmployeeBranchobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JTokenReader reader = null;
                JObject jobj = JObject.Parse(SaveEmployeeBranchobj.Data);

                int rowCount = jobj["rows"].Count();

                for (int j = 0; j < rowCount; j++)
                {
                    reader = new JTokenReader(jobj["rows"].ElementAt(j).ToObject<JObject>()["employeeBranchID"]);
                    reader.Read();
                    string employeeBranchIDValue = reader.Value.ToString();

                    reader = new JTokenReader(jobj["employeeID"]);
                    reader.Read();
                    int employeeID = Convert.ToInt32(reader.Value);

                    reader = new JTokenReader(jobj["rows"].ElementAt(j).ToObject<JObject>()["branchID"]);
                    reader.Read();
                    int branchID = Convert.ToInt32(reader.Value);

                    reader = new JTokenReader(jobj["rows"].ElementAt(j).ToObject<JObject>()["IsDefault"]);
                    reader.Read();
                    bool isDefault = Convert.ToBoolean(reader.Value);

                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        conn.Open();
                        if (!string.IsNullOrEmpty(employeeBranchIDValue))
                        {
                            // Update existing record
                            int employeeBranchID = Convert.ToInt32(employeeBranchIDValue);

                            using (SqlCommand cmd = new SqlCommand("UpdateEmployeeBranch", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@EmployeeBranchID", employeeBranchID);
                                cmd.Parameters.AddWithValue("@BranchID", branchID);
                                cmd.Parameters.AddWithValue("@CompanyEmployeeID", employeeID);
                                cmd.Parameters.AddWithValue("@IsDefault", isDefault);

                                cmd.ExecuteNonQuery();
                            }
                        }
                        else
                        {
                            // Check if the branch already exists
                            bool branchExists = false;

                            using (SqlCommand checkCmd = new SqlCommand("CheckEmployeeBranchExists", conn))
                            {
                                checkCmd.CommandType = CommandType.StoredProcedure;
                                checkCmd.Parameters.AddWithValue("@BranchID", branchID);
                                checkCmd.Parameters.AddWithValue("@CompanyEmployeeID", employeeID);

                                branchExists = (int)checkCmd.ExecuteScalar() > 0;
                            }

                            if (!branchExists)
                            {
                                // Insert new record
                                using (SqlCommand cmd = new SqlCommand("InsertEmployeeBranch", conn))
                                {
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.Parameters.AddWithValue("@BranchID", branchID);
                                    cmd.Parameters.AddWithValue("@CompanyEmployeeID", employeeID);
                                    cmd.Parameters.AddWithValue("@IsDefault", isDefault);

                                    cmd.ExecuteNonQuery();
                                }
                            }
                        }

                        // Log GPS Details
                        //gbl.InsertGPSDetails(Convert.ToInt32(SaveEmployeeBranchobj.Company_ID),
                        //    Convert.ToInt32(SaveEmployeeBranchobj.Branch),
                        //    Convert.ToInt32(SaveEmployeeBranchobj.User_ID),
                        //    Convert.ToInt32(Common.GetObjectID("CoreEmployeeMaster")),
                        //    employeeID, 0, 0, "Update", false, Convert.ToInt32(SaveEmployeeBranchobj.MenuID));
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }
            }
            return new JsonResult(false);
        }


        #endregion


        #region ::: SaveEmployeeBranchEditMode Uday Kumar J B 27-08-2024:::
        /// <summary>
        /// SaveEmployeeBranchEditMode
        /// </summary>
        /// 

        public static IActionResult SaveEmployeeBranchEditMode(string connString, SaveEmployeeBranchEditModeList SaveEmployeeBranchEditModeobj)
        {
            dynamic s = null;
            var jsonresult = s;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JTokenReader reader = null;
                JObject jobj = JObject.Parse(SaveEmployeeBranchEditModeobj.Data);

                int rowCount = jobj["rows"].Count();
                int CountryID = 0;
                int StateID = 0;
                int? RegionID = 0;
                bool IsDefault = false;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Loop through each row in the data
                    for (int j = 0; j < rowCount; j++)
                    {
                        reader = new JTokenReader(jobj["rows"].ElementAt(j).ToObject<JObject>()["employeeBranchID"]);
                        reader.Read();
                        int employeeBranchID = Convert.ToInt32(reader.Value);

                        reader = new JTokenReader(jobj["employeeID"]);
                        reader.Read();
                        int employeeID = Convert.ToInt32(reader.Value);

                        reader = new JTokenReader(jobj["rows"].ElementAt(j).ToObject<JObject>()["IsDefault"]);
                        reader.Read();
                        IsDefault = Convert.ToBoolean(reader.Value);

                        reader = new JTokenReader(jobj["rows"].ElementAt(j).ToObject<JObject>()["branchID"]);
                        reader.Read();
                        int branchID = Convert.ToInt32(reader.Value);

                        // Update the IsDefault flag in GNM_EmployeeBranch table
                        using (SqlCommand updateBranchCmd = new SqlCommand("UpdateEmployeeBranchIsDefault", conn))
                        {
                            updateBranchCmd.CommandType = CommandType.StoredProcedure;
                            updateBranchCmd.Parameters.AddWithValue("@EmployeeBranchID", employeeBranchID);
                            updateBranchCmd.Parameters.AddWithValue("@IsDefault", IsDefault);

                            updateBranchCmd.ExecuteNonQuery();
                        }

                        if (IsDefault)
                        {
                            // Get branch data
                            GNM_Branch branchData = GetBranchData(branchID, conn); // Custom method to fetch branch data

                            // Update CompanyEmployee details
                            using (SqlCommand updateCompanyCmd = new SqlCommand("UpdateCompanyEmployeeDetails", conn))
                            {
                                updateCompanyCmd.CommandType = CommandType.StoredProcedure;
                                updateCompanyCmd.Parameters.AddWithValue("@EmployeeID", employeeID);
                                updateCompanyCmd.Parameters.AddWithValue("@CountryID", branchData.Country_ID);
                                updateCompanyCmd.Parameters.AddWithValue("@StateID", branchData.State_ID);
                                updateCompanyCmd.Parameters.AddWithValue("@RegionID", branchData.Region_ID);

                                updateCompanyCmd.ExecuteNonQuery();
                            }

                            CountryID = branchData.Country_ID;
                            StateID = branchData.State_ID;
                            RegionID = branchData.Region_ID;
                        }
                    }

                    // Get CountryList
                    List<dynamic> CountryList = new List<dynamic>();
                    using (SqlCommand getCountryListCmd = new SqlCommand("GetCountryList", conn))
                    {
                        getCountryListCmd.CommandType = CommandType.StoredProcedure;
                        using (SqlDataReader readerCountry = getCountryListCmd.ExecuteReader())
                        {
                            while (readerCountry.Read())
                            {
                                CountryList.Add(new { ID = readerCountry["ID"], Name = readerCountry["Name"] });
                            }
                        }
                    }

                    // Get StateList
                    List<dynamic> StateList = new List<dynamic>();
                    using (SqlCommand getStateListCmd = new SqlCommand("GetStateList", conn))
                    {
                        getStateListCmd.CommandType = CommandType.StoredProcedure;
                        using (SqlDataReader readerState = getStateListCmd.ExecuteReader())
                        {
                            while (readerState.Read())
                            {
                                StateList.Add(new { ID = readerState["ID"], Name = readerState["Name"] });
                            }
                        }
                    }

                    // Get RegionName
                    string RegionName = string.Empty;
                    using (SqlCommand getRegionNameCmd = new SqlCommand("GetRegionNameByID", conn))
                    {
                        getRegionNameCmd.CommandType = CommandType.StoredProcedure;
                        getRegionNameCmd.Parameters.AddWithValue("@RegionID", RegionID == null ? 0 : RegionID);

                        RegionName = (string)getRegionNameCmd.ExecuteScalar();
                    }

                    // Prepare JSON result
                    jsonresult = new
                    {
                        CountryID,
                        StateID,
                        RegionID,
                        RegionName,
                        CountryList,
                        StateList
                    };
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonresult);
        }

        private static GNM_Branch GetBranchData(int branchID, SqlConnection conn)
        {
            // Method to retrieve branch data using a stored procedure
            GNM_Branch branchData = null;

            using (SqlCommand cmd = new SqlCommand("GetBranchDataByID", conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@BranchID", branchID);

                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        branchData = new GNM_Branch
                        {
                            Branch_ID = (int)reader["Branch_ID"],
                            Country_ID = (int)reader["Country_ID"],
                            State_ID = (int)reader["State_ID"],
                            Region_ID = reader["Region_ID"] as int?
                        };
                    }
                }
            }

            return branchData;
        }



        #endregion


        #region ::: DeleteEmployeeBranch Uday Kumar J B 27-08-2024  :::
        /// <summary>
        /// to Delete the Employee Branch details
        /// </summary>
        /// 

        public static IActionResult DeleteEmployeeBranch(string connString, DeleteEmployeeBranchList DeleteEmployeeBranchobj)
        {
            string errorMsg = "";
            dynamic s = null;
            var jsonresult = s;
            List<dynamic> CountryList = new List<dynamic>();
            List<dynamic> StateList = new List<dynamic>();
            int CountryID = 0;
            int StateID = 0;
            int? RegionID = 0;
            bool IsDefault = false;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            using (SqlConnection conn = new SqlConnection(connString))
            {
                conn.Open();

                // Fetch CountryList using ADO.NET
                using (SqlCommand cmdCountry = new SqlCommand("usp_GetCountries", conn))
                {
                    using (SqlDataReader reader = cmdCountry.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            CountryList.Add(new
                            {
                                ID = reader["RefMasterDetail_ID"],
                                Name = reader["RefMasterDetail_Name"].ToString()
                            });
                        }
                    }
                }

                // Fetch StateList using ADO.NET
                using (SqlCommand cmdState = new SqlCommand("usp_GetStates", conn))
                {
                    using (SqlDataReader reader = cmdState.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            StateList.Add(new
                            {
                                ID = reader["State_ID"],
                                Name = reader["State_Name"].ToString()
                            });
                        }
                    }
                }

                try
                {
                    JObject jobj = JObject.Parse(DeleteEmployeeBranchobj.key);
                    JObject jobjdata = JObject.Parse(DeleteEmployeeBranchobj.empBranchData);
                    int rowCount = jobj["rows"].Count();
                    int rowCountEmp = jobjdata["rows"].Count();
                    int CountDependent = 0;

                    // Delete Employee Branches
                    for (int i = 0; i < rowCount; i++)
                    {
                        int id = jobj["rows"].ElementAt(i)["id"].Value<int>();

                        using (SqlCommand cmdDeleteBranch = new SqlCommand("usp_DeleteEmployeeBranch", conn))
                        {
                            cmdDeleteBranch.CommandType = CommandType.StoredProcedure;
                            cmdDeleteBranch.Parameters.AddWithValue("@EmployeeBranchID", id);

                            try
                            {
                                cmdDeleteBranch.ExecuteNonQuery();
                            }
                            catch (SqlException ex)
                            {
                                if (ex.Number == 547) // Foreign key constraint error
                                {
                                    errorMsg += CommonFunctionalities.GetResourceString(DeleteEmployeeBranchobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                                }
                                else
                                {
                                    throw;
                                }
                            }
                        }
                    }

                    // Process Employee Data
                    for (int j = 0; j < rowCountEmp; j++)
                    {
                        int employeeBranchID = jobjdata["rows"].ElementAt(j)["employeeBranchID"].Value<int>();
                        int employeeID = jobjdata["employeeID"].Value<int>();
                        IsDefault = jobjdata["rows"].ElementAt(j)["IsDefault"].Value<bool>();
                        int branchID = jobjdata["rows"].ElementAt(j)["branchID"].Value<int>();

                        using (SqlCommand cmdUpdateBranch = new SqlCommand("usp_UpdateEmployeeBranch", conn))
                        {
                            cmdUpdateBranch.CommandType = CommandType.StoredProcedure;
                            cmdUpdateBranch.Parameters.AddWithValue("@EmployeeBranchID", employeeBranchID);
                            cmdUpdateBranch.Parameters.AddWithValue("@IsDefault", IsDefault);
                            cmdUpdateBranch.ExecuteNonQuery();
                        }

                        if (IsDefault)
                        {
                            // Fetch Branch data using ADO.NET
                            int branchCountryID = 0;
                            int branchStateID = 0;
                            int? branchRegionID = null;

                            using (SqlCommand cmdBranch = new SqlCommand("usp_GetBranchDetailsDeleteEmployeeBranchs", conn))
                            {
                                cmdBranch.CommandType = CommandType.StoredProcedure;
                                cmdBranch.Parameters.AddWithValue("@BranchID", branchID);
                                using (SqlDataReader reader = cmdBranch.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        branchCountryID = Convert.ToInt32(reader["Country_ID"]);
                                        branchStateID = Convert.ToInt32(reader["State_ID"]);
                                        branchRegionID = reader["Region_ID"] as int?;
                                    }
                                }
                            }

                            using (SqlCommand cmdUpdateEmployee = new SqlCommand("usp_UpdateCompanyEmployeeDeleteEmployeeBranch", conn))
                            {
                                cmdUpdateEmployee.CommandType = CommandType.StoredProcedure;
                                cmdUpdateEmployee.Parameters.AddWithValue("@CompanyEmployeeID", employeeID);
                                cmdUpdateEmployee.Parameters.AddWithValue("@CountryID", branchCountryID);
                                cmdUpdateEmployee.Parameters.AddWithValue("@StateID", branchStateID);
                                cmdUpdateEmployee.Parameters.AddWithValue("@RegionID", branchRegionID ?? (object)DBNull.Value);
                                cmdUpdateEmployee.ExecuteNonQuery();

                                CountryID = branchCountryID;
                                StateID = branchStateID;
                                RegionID = branchRegionID;
                            }
                        }

                        using (SqlCommand cmdCheckTimesheet = new SqlCommand("usp_CheckDailyTimeSheet", conn))
                        {
                            cmdCheckTimesheet.CommandType = CommandType.StoredProcedure;
                            cmdCheckTimesheet.Parameters.AddWithValue("@EmployeeID", employeeID);
                            cmdCheckTimesheet.Parameters.AddWithValue("@BranchID", branchID);
                            SqlParameter countParam = new SqlParameter("@CountDependent", SqlDbType.Int) { Direction = ParameterDirection.Output };
                            cmdCheckTimesheet.Parameters.Add(countParam);
                            cmdCheckTimesheet.ExecuteNonQuery();

                            CountDependent = (int)countParam.Value;
                        }
                    }

                    if (CountDependent == 0)
                    {
                        // Assuming `gbl.InsertGPSDetails` is defined elsewhere
                        //  gbl.InsertGPSDetails(Convert.ToInt32(DeleteEmployeeBranchobj.Company_ID), Convert.ToInt32(DeleteEmployeeBranchobj.Branch), Convert.ToInt32(DeleteEmployeeBranchobj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreEmployeeMaster")), 0, 0, 0, "Delete", false, Convert.ToInt32(DeleteEmployeeBranchobj.MenuID));
                        errorMsg += CommonFunctionalities.GetResourceString(DeleteEmployeeBranchobj.UserCulture.ToString(), "deletedsuccessfully").ToString();
                    }
                    else
                    {
                        errorMsg += CommonFunctionalities.GetResourceString(DeleteEmployeeBranchobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                    }
                }
                catch (Exception ex)
                {
                    if (ex.InnerException != null && ex.InnerException.InnerException != null && ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                    {
                        errorMsg += CommonFunctionalities.GetResourceString(DeleteEmployeeBranchobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                    }
                    else
                    {
                        errorMsg += CommonFunctionalities.GetResourceString(DeleteEmployeeBranchobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                    }
                }

                // Fetch RegionName using ADO.NET
                string RegionName = null;
                if (RegionID != null)
                {
                    using (SqlCommand cmdRegionName = new SqlCommand("usp_GetRegionName", conn))
                    {
                        cmdRegionName.CommandType = CommandType.StoredProcedure;
                        cmdRegionName.Parameters.AddWithValue("@RegionID", RegionID ?? (object)DBNull.Value);
                        using (SqlDataReader reader = cmdRegionName.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                RegionName = reader["RefMasterDetail_Name"].ToString();
                            }
                        }
                    }
                }

                //jsonresult = new
                //{
                //    errorMsg,
                //    CountryID,
                //    StateID,
                //    RegionID,
                //    RegionName,
                //    CountryList,
                //    StateList
                //};
            }

            return new JsonResult(errorMsg);
        }
        #endregion


        #region ::: CheckEmployeeBranch Uday Kumar J B 27-08-2024:::
        /// <summary>
        /// to check if the employee is already associated with the branch
        /// </summary>
        /// 

        public static IActionResult CheckEmployeeBranch(string connString, CheckEmployeeBranchList CheckEmployeeBranchobj)
        {
            int status = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("CheckEmployeeBranch", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        cmd.Parameters.AddWithValue("@BranchID", CheckEmployeeBranchobj.branchID);
                        cmd.Parameters.AddWithValue("@EmployeeID", CheckEmployeeBranchobj.employeeID);
                        cmd.Parameters.AddWithValue("@PrimaryKey", CheckEmployeeBranchobj.primaryKey);

                        // Execute the stored procedure and get the return status
                        status = (int)cmd.ExecuteScalar();
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(status);
        }


        #endregion


        #region ::: SelectState Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// to get all the states for the selected country
        /// </summary> 
        /// 

        public static IActionResult SelectState(string connString, SelectStateList SelectStateobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            int userLanguageID = Convert.ToInt32(SelectStateobj.UserLanguageID);
            int generalLanguageID = Convert.ToInt32(SelectStateobj.GeneralLanguageID);
            dynamic regionArray = null;
            DataTable dt = new DataTable();

            using (SqlConnection conn = new SqlConnection(connString))
            {
                conn.Open();

                if (SelectStateobj.languageID == generalLanguageID)
                {
                    using (SqlCommand cmd = new SqlCommand("GetStatesByCountry", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CountryID", SelectStateobj.countryID);

                        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        {
                            da.Fill(dt);
                        }
                    }
                }
                else
                {
                    using (SqlCommand cmd = new SqlCommand("GetStatesByCountryAndLanguage", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CountryID", SelectStateobj.countryID);
                        cmd.Parameters.AddWithValue("@LanguageID", userLanguageID);

                        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                        {
                            da.Fill(dt);
                        }
                    }
                }

                regionArray = dt.AsEnumerable().Select(row => new
                {
                    State_id = row.Field<int>("State_ID"),
                    State_Name = row.Field<string>("State_Name")
                }).ToList();
            }

            return new JsonResult(regionArray);
        }


        #endregion


        #region ::: CheckEmployeeID Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// to check if the Name is already used
        /// </summary>
        /// 

        public static IActionResult CheckEmployeeID(string connString, CheckEmployeeIDList CheckEmployeeIDobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            int status = 0;
            try
            {
                string employeeID = Common.DecryptString(CheckEmployeeIDobj.employeeID);
                //GNM_User User = CheckEmployeeIDobj.UserDetails.FirstOrDefault();
                int companyID = CheckEmployeeIDobj.Company_ID;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("CheckEmployeeID_SP", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@EmployeeID", employeeID);
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);
                        cmd.Parameters.AddWithValue("@PrimaryKey", CheckEmployeeIDobj.primaryKey);

                        status = Convert.ToInt32(cmd.ExecuteScalar());
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(status);
        }


        #endregion


        #region ::: SelectAllEmployeeMastersData Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// SelectAllEmployeeMastersData
        /// </summary>
        /// 

        public static IActionResult SelectAllEmployeeMastersData(string connString, SelectAllEmployeeMastersDataList SelectAllEmployeeMastersDataobj)
        {
            var countryArray = default(dynamic);
            var stateArray = default(dynamic);
            var departmentArray = default(dynamic);
            var designationArray = default(dynamic);
            var managerArray = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int userLanguageID = Convert.ToInt32(SelectAllEmployeeMastersDataobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectAllEmployeeMastersDataobj.GeneralLanguageID);
                //GNM_User User = SelectAllEmployeeMastersDataobj.UserDetails.FirstOrDefault();
                int companyID = SelectAllEmployeeMastersDataobj.Company_ID;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Get Countries
                    using (SqlCommand cmd = new SqlCommand("GetCountries", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@LanguageID", SelectAllEmployeeMastersDataobj.languageID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            var countryList = new List<dynamic>();
                            while (reader.Read())
                            {
                                countryList.Add(new
                                {
                                    RefMasterDetail_ID = reader["RefMasterDetail_ID"],
                                    RefMasterDetail_Name = reader["RefMasterDetail_Name"]
                                });
                            }
                            countryArray = countryList;
                        }
                    }

                    // Get States
                    using (SqlCommand cmd = new SqlCommand("GetStates", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CountryID", SelectAllEmployeeMastersDataobj.countryID);
                        cmd.Parameters.AddWithValue("@LanguageID", SelectAllEmployeeMastersDataobj.languageID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            var stateList = new List<dynamic>();
                            while (reader.Read())
                            {
                                stateList.Add(new
                                {
                                    State_id = reader["State_ID"],
                                    State_Name = reader["State_Name"]
                                });
                            }
                            stateArray = stateList;
                        }
                    }

                    // Get Departments
                    using (SqlCommand cmd = new SqlCommand("GetDepartments", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@LanguageID", SelectAllEmployeeMastersDataobj.languageID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            var departmentList = new List<dynamic>();
                            while (reader.Read())
                            {
                                departmentList.Add(new
                                {
                                    RefMasterDetail_ID = reader["RefMasterDetail_ID"],
                                    RefMasterDetail_Name = reader["RefMasterDetail_Name"]
                                });
                            }
                            departmentArray = departmentList;
                        }
                    }

                    // Get Designations
                    using (SqlCommand cmd = new SqlCommand("GetDesignations", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@LanguageID", SelectAllEmployeeMastersDataobj.languageID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            var designationList = new List<dynamic>();
                            while (reader.Read())
                            {
                                designationList.Add(new
                                {
                                    RefMasterDetail_ID = reader["RefMasterDetail_ID"],
                                    RefMasterDetail_Name = reader["RefMasterDetail_Name"]
                                });
                            }
                            designationArray = designationList;
                        }
                    }

                    // Get Managers
                    using (SqlCommand cmd = new SqlCommand("GetManagers", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@LanguageID", SelectAllEmployeeMastersDataobj.languageID);
                        cmd.Parameters.AddWithValue("@UserLanguageID", userLanguageID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            var managerList = new List<dynamic>();
                            while (reader.Read())
                            {
                                managerList.Add(new
                                {
                                    Company_Employee_ID = reader["Company_Employee_ID"],
                                    Company_Employee_Name = reader["Company_Employee_Name"]
                                });
                            }
                            managerArray = managerList;
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            var jsonResult = new
            {
                countryArray = countryArray,
                stateArray = stateArray,
                departmentArray = departmentArray,
                designationArray = designationArray,
                managerArray = managerArray
            };
            return new JsonResult(jsonResult);
        }


        #endregion


        #region ::: DeleteTrainingDetails Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// to Delete the Employee Skillset details
        /// </summary>
        /// 

        public static IActionResult DeleteTrainingDetails(string connString, DeleteTrainingDetailsList DeleteTrainingDetailsobj)
        {
            string errorMsg = "";
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jobj = JObject.Parse(DeleteTrainingDetailsobj.key);
                int rowCount = jobj["rows"].Count();
                var ids = new List<int>();

                for (int i = 0; i < rowCount; i++)
                {
                    int id = jobj["rows"].ElementAt(i)["id"].Value<int>();
                    ids.Add(id);
                }

                string idsParameter = string.Join(",", ids);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("DeleteTrainingDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@TrainingDetails_IDs", idsParameter);

                        cmd.ExecuteNonQuery();
                    }
                }

                // Log GPS Details
                //gbl.InsertGPSDetails(
                //    Convert.ToInt32(DeleteTrainingDetailsobj.Company_ID),
                //    Convert.ToInt32(DeleteTrainingDetailsobj.Branch),
                //    Convert.ToInt32(DeleteTrainingDetailsobj.User_ID),
                //    Convert.ToInt32(Common.GetObjectID("CoreEmployeeMaster")),
                //    ids.Last(), // Assuming the last ID is used for GPS logging
                //    0, 0, "Delete", false, Convert.ToInt32(DeleteTrainingDetailsobj.MenuID)
                //);

                errorMsg += CommonFunctionalities.GetResourceString(DeleteTrainingDetailsobj.UserCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (SqlException sqlEx)
            {
                if (sqlEx.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    errorMsg += CommonFunctionalities.GetResourceString(DeleteTrainingDetailsobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    errorMsg += CommonFunctionalities.GetResourceString(DeleteTrainingDetailsobj.UserCulture.ToString(), "erroroccurred").ToString();
                }
            }
            catch (Exception ex)
            {
                errorMsg += CommonFunctionalities.GetResourceString(DeleteTrainingDetailsobj.UserCulture.ToString(), "erroroccurred").ToString();
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return new JsonResult(errorMsg);
        }

        #endregion


        #region ::: DeleteETODetails Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// to Delete the Employee Skillset details
        /// </summary>
        /// 

        public static IActionResult DeleteETODetails(string connString, DeleteETODetailsList DeleteETODetailsobj)
        {
            string errorMsg = "";
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jobj = JObject.Parse(DeleteETODetailsobj.key);
                int rowCount = jobj["rows"].Count();
                var ids = new List<int>();

                for (int i = 0; i < rowCount; i++)
                {
                    int id = jobj["rows"].ElementAt(i)["id"].Value<int>();
                    ids.Add(id);
                }

                string idsParameter = string.Join(",", ids);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("DeleteETODetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@EmployeeETO_IDs", idsParameter);

                        cmd.ExecuteNonQuery();
                    }
                }

                // Log GPS Details
                //gbl.InsertGPSDetails(
                //    Convert.ToInt32(DeleteETODetailsobj.Company_ID),
                //    Convert.ToInt32(DeleteETODetailsobj.Branch),
                //    Convert.ToInt32(DeleteETODetailsobj.User_ID),
                //    Convert.ToInt32(Common.GetObjectID("CoreEmployeeMaster")),
                //    ids.Last(), // Assuming the last ID is used for GPS logging
                //    0, 0, "Delete", false, Convert.ToInt32(DeleteETODetailsobj.MenuID)
                //);
                errorMsg += CommonFunctionalities.GetResourceString(DeleteETODetailsobj.UserCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (SqlException sqlEx)
            {
                if (sqlEx.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    errorMsg += CommonFunctionalities.GetResourceString(DeleteETODetailsobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    errorMsg += CommonFunctionalities.GetResourceString(DeleteETODetailsobj.UserCulture.ToString(), "erroroccurred").ToString();
                }
            }
            catch (Exception ex)
            {
                errorMsg += CommonFunctionalities.GetResourceString(DeleteETODetailsobj.UserCulture.ToString(), "erroroccurred").ToString();
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return new JsonResult(errorMsg);
        }


        #endregion


        #region ::: SelectRegionForReport Uday Kumar J B 28-08-2024 :::
        /// <summary>
        ////to Select Region and its child
        /// </summary> 
        /// 

        public static IActionResult SelectRegionForReport(string connString, SelectRegionForReportList SelectRegionForReportobj)
        {
            int User_ID = Convert.ToInt32(SelectRegionForReportobj.User_ID);
            var jsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("GetRegionForReport", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@OEM", SelectRegionForReportobj.OEM);
                        cmd.Parameters.AddWithValue("@User_ID", User_ID);

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            var regionList = new List<APINameID>();
                            while (reader.Read())
                            {
                                regionList.Add(new APINameID
                                {
                                    ID = reader.GetInt32(reader.GetOrdinal("ID")),
                                    Name = reader.GetString(reader.GetOrdinal("Name"))
                                });
                            }

                            jsonData = new
                            {
                                Region = regionList.OrderBy(a => a.Name).Select(a => new
                                {
                                    ID = a.ID,
                                    Name = a.Name
                                })
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }


        #endregion


        #region ::: SelectCompany Uday Kumar J B 28-08-2024 :::
        /// <summary>
        ////to Select Company and its child
        /// </summary> 
        /// 

        public static IActionResult SelectCompanyForReport(string connString, SelectCompanyForReportList SelectCompanyForReportobj)
        {
            int UserLang = Convert.ToInt32(SelectCompanyForReportobj.UserLanguageID);
            int GeneralLang = Convert.ToInt32(SelectCompanyForReportobj.GeneralLanguageID);
            int Company_ID = Convert.ToInt32(SelectCompanyForReportobj.Company_ID);
            var jsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // Trim any trailing commas from RegionID
                string RegionID = SelectCompanyForReportobj.RegionID.TrimEnd(new char[] { ',' });

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("GetCompanyForReport", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@RegionID", RegionID);
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                        cmd.Parameters.AddWithValue("@UserLang", UserLang);
                        cmd.Parameters.AddWithValue("@GeneralLang", GeneralLang);

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            var companyList = new List<APINameID>();
                            while (reader.Read())
                            {
                                companyList.Add(new APINameID
                                {
                                    ID = reader.GetInt32(reader.GetOrdinal("ID")),
                                    Name = reader.GetString(reader.GetOrdinal("Name"))
                                });
                            }

                            jsonData = new
                            {
                                Company = companyList.OrderBy(a => a.Name).Select(a => new
                                {
                                    ID = a.ID,
                                    Name = a.Name
                                })
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }


        #endregion


        #region ::: SelectBranchForReport Uday Kumar J B 28-08-2024 :::
        /// <summary>
        ////to Select Branch for Reports
        /// </summary> 
        /// 

        public static IActionResult SelectBranchForReport(string connString, SelectBranchForReportList SelectBranchForReportobj)
        {
            var jsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int userLanguageID = Convert.ToInt32(SelectBranchForReportobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectBranchForReportobj.GeneralLanguageID);
                int companyID = Convert.ToInt32(SelectBranchForReportobj.Company_ID);

                // Trim any trailing commas from CompanyID and RegionID
                string CompanyID = SelectBranchForReportobj.CompanyID.TrimEnd(new char[] { ',' });
                string RegionID = SelectBranchForReportobj.RegionID.TrimEnd(new char[] { ',' });

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("GetBranchForReport", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                        cmd.Parameters.AddWithValue("@RegionID", RegionID);
                        cmd.Parameters.AddWithValue("@UserLang", userLanguageID);
                        cmd.Parameters.AddWithValue("@GeneralLang", generalLanguageID);

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            var branchList = new List<APINameID>();
                            while (reader.Read())
                            {
                                branchList.Add(new APINameID
                                {
                                    ID = reader.GetInt32(reader.GetOrdinal("ID")),
                                    Name = reader.GetString(reader.GetOrdinal("Name"))
                                });
                            }

                            jsonData = new
                            {
                                branchValues = branchList.OrderBy(a => a.Name).Select(a => new
                                {
                                    Branch_ID = a.ID,
                                    Branch_Name = a.Name
                                })
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }


        #endregion


        #region ::: SelectRegionForReport Uday Kumar J B 28-08-2024 I think not Used :::
        /// <summary>
        ////to Select Warehouse for Reports
        /// </summary> 
        /// 

        public static IActionResult SelectWarehouseForReport(string connString, SelectWarehouseForReportList SelectWarehouseForReportobj)
        {
            var jsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int userLanguageID = Convert.ToInt32(SelectWarehouseForReportobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectWarehouseForReportobj.GeneralLanguageID);

                // Trim any trailing commas from BranchID and CompanyID
                string BranchID = SelectWarehouseForReportobj.BranchID.TrimEnd(new char[] { ',' });
                string CompanyID = SelectWarehouseForReportobj.CompanyID.TrimEnd(new char[] { ',' });
                CompanyID = string.IsNullOrEmpty(CompanyID) ? "0" : CompanyID;
                BranchID = string.IsNullOrEmpty(BranchID) ? "0" : BranchID;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("GetWarehouseForReport", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@BranchID", BranchID);
                        cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                        cmd.Parameters.AddWithValue("@UserLang", userLanguageID);
                        cmd.Parameters.AddWithValue("@GeneralLang", generalLanguageID);

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            var warehouseList = new List<APINameID>();
                            while (reader.Read())
                            {
                                warehouseList.Add(new APINameID
                                {
                                    ID = reader.GetInt32(reader.GetOrdinal("ID")),
                                    Name = reader.GetString(reader.GetOrdinal("Name"))
                                });
                            }

                            jsonData = new
                            {
                                branchWH = warehouseList.OrderBy(a => a.Name).Select(a => new
                                {
                                    WareHouse_ID = a.ID,
                                    WareHouseName = a.Name
                                })
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }


        #endregion


        #region ::: SaveFileToServer Uday Kumar J B 28-08-2024 need to do :::
        /// <summary>
        /// SaveFileToServer
        /// </summary>
        //public static IActionResult SaveFileToServer(string connString, SaveFileToServerList SaveFileToServerobj)
        //{
        //    string AppPath = string.Empty;
        //    int Count = 0;
        //    int EmployeeID = Convert.ToInt32(SaveFileToServerobj.EmployeeID);

        //    if (SaveFileToServerobj.postedFile != null && EmployeeID == 0)
        //    {
        //        string fileName = SaveFileToServerobj.postedFile.FileName;

        //        if (fileName.Contains("\\"))
        //        {
        //            int lastIndex = fileName.LastIndexOf('\\') + 1;
        //            int len = fileName.Length - lastIndex;
        //            fileName = fileName.Substring(lastIndex, len);
        //        }

        //        string SMP = Server.MapPath(AppPath + "/EmployeeProfile");
        //        string Path = SMP + "/" + "Temp_0";
        //        string fullPath = SMP + ConfigurationManager.AppSettings.Get("AttachmentPath").ToString() + "Temp_0" + "-" + fileName;
        //        //string Path = SMP + "/";
        //        //string fullPath = SMP + "/" + "/" + fileName;
        //        if (System.IO.File.Exists(fullPath))
        //        {
        //            SaveFileToServerobj.postedFile.SaveAs(fullPath);
        //            //Session["TempPhoto"] = fileName;

        //        }
        //        else
        //        {
        //            DirectoryInfo di = Directory.CreateDirectory(Path);
        //            postedFile.SaveAs(fullPath);
        //            //Session["TempPhoto"] = fileName;

        //        }
        //    }
        //    else
        //    {
        //        string fileName = SaveFileToServerobj.postedFile.FileName;

        //        if (fileName.Contains("\\"))
        //        {
        //            int lastIndex = fileName.LastIndexOf('\\') + 1;
        //            int len = fileName.Length - lastIndex;
        //            fileName = fileName.Substring(lastIndex, len);
        //        }

        //        string SMP = Server.MapPath(AppPath + "/EmployeeProfile");
        //        string Path = SMP + "/" + EmployeeID;
        //        string fullPath = SMP + "/" + EmployeeID + "-" + fileName;
        //        //Session["TempPhoto"] = fileName;
        //        if (System.IO.File.Exists(fullPath))
        //        {
        //            //DirectoryInfo di = Directory.CreateDirectory(Path);
        //            //di.Delete(true);
        //            //Thread.Sleep(2000);
        //            //if (!Directory.Exists(Path))
        //            //{
        //            //DirectoryInfo di1 = Directory.CreateDirectory(Path);
        //            postedFile.SaveAs(fullPath);
        //            // Session["TempPhoto"] = fileName;
        //            GNM_CompanyEmployee dsProfileUpdate = new GNM_CompanyEmployee();
        //            dsProfileUpdate.PHOTONAME = fileName;
        //            dsProfileUpdate.Company_Employee_ID = EmployeeID;
        //            Count = UserProfileUpdatePhoto(connString,dsProfileUpdate, Convert.ToInt32(SaveFileToServerobj.User_ID));

        //            // }
        //        }
        //        else
        //        {
        //            DirectoryInfo di = Directory.CreateDirectory(Path);
        //            SaveFileToServerobj.postedFile.SaveAs(fullPath);
        //            //Session["TempPhoto"] = fileName;
        //            GNM_CompanyEmployee dsProfileUpdate = new GNM_CompanyEmployee();
        //            dsProfileUpdate.PHOTONAME = fileName;
        //            dsProfileUpdate.Company_Employee_ID = EmployeeID;
        //            Count = UserProfileUpdatePhoto(connString,dsProfileUpdate, Convert.ToInt32(SaveFileToServerobj.User_ID));
        //        }
        //    }
        //    //Response.Write("<script>var x=window.open('','_self','','');window.opener = null;x.close();</script>");
        //    Response.Write("<script>var x=window.open('','_self','','');window.opener = null;x.close();</script>");
        //    return new JsonResult(null);
        //}
        #endregion


        #region ::: UserProfileUpdatePhoto Uday Kumar J B 28-08-2024  need to check:::
        /// <summary>
        /// UserProfileUpdatePhoto
        /// </summary>
        /// 
        public static int UserProfileUpdatePhoto(string connString, GNM_CompanyEmployee dsobj, int userProfileid)
        {
            int count = 0;
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("UpdateEmployeePhoto", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_Employee_ID", dsobj.Company_Employee_ID);
                        cmd.Parameters.AddWithValue("@PhotoName", dsobj.PHOTONAME);
                        cmd.Parameters.AddWithValue("@ModifiedBy", userProfileid);

                        conn.Open();
                        count = cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return count;
        }
        #endregion


        #region ::: SelAllEducationalQualification Uday Kumar J B 28-08-2024 :::
        /// <summary>
        /// to select all the skills of the particular employee
        /// </summary>
        /// 

        public static IActionResult SelEmployeeQualification(string connString, SelEmployeeQualificationList SelEmployeeQualificationobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int userLanguageID = Convert.ToInt32(SelEmployeeQualificationobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelEmployeeQualificationobj.GeneralLanguageID);
                int count = 0;
                int total = 0;

                // List to hold the qualification data
                List<QualificationData> qualificationDataList = new List<QualificationData>();

                // Call stored procedure to get Employee Qualification List
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    SqlCommand cmd = new SqlCommand("SelEmployeeQualificationList", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@EmployeeID", SelEmployeeQualificationobj.employeeID);

                    conn.Open();
                    SqlDataReader reader = cmd.ExecuteReader();
                    while (reader.Read())
                    {
                        int qualificationId = Convert.ToInt32(reader["EMPLOYEEQUALIFICATION_ID"]);

                        qualificationDataList.Add(new QualificationData
                        {
                            // Edit and delete actions
                            edit = $"<a title='Edit' href='#' key='{qualificationId}' class='editEmployeeQualification font-icon-class'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                            delete = $"<input type='checkbox' key='{qualificationId}' id='chk{qualificationId}' class='chkEmployeeQualificationDelete'/>",
                            EMPLOYEEQUALIFICATION_ID = qualificationId,
                            INSTITUTENAME = reader["INSTITUTENAME"].ToString(),
                            PASSPERCENTAGE = reader["PASSPERCENTAGE"].ToString(),
                            YOP = Convert.ToInt32(reader["YOP"]),
                            QUALIFICATION = reader["QUALIFICATION"].ToString(),
                            COURSE = reader["COURSE"].ToString(),
                            Company_Employee_ID = Convert.ToInt32(reader["Company_Employee_ID"]),

                        });
                    }
                }

                count = qualificationDataList.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                // Apply sorting (replace the LINQ OrderByField method)
                var sortedList = qualificationDataList.AsQueryable().OrderByField(sidx, sord);

                if (_search)
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    sortedList = sortedList.FilterSearch(filtersObj);
                }
                // Pagination
                var paginatedList = sortedList.Skip((page - 1) * rows).Take(rows).ToList();

                var result = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = paginatedList,
                };

                return new JsonResult(result);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(null);
        }


        #endregion


        #region ::: DeleteQualification Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// to Delete the Employee Skillset details
        /// </summary>
        /// 


        public static IActionResult DeleteQualification(string connString, DeleteQualificationList DeleteQualificationobj)
        {
            string errorMsg = "";
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jobj = JObject.Parse(DeleteQualificationobj.key);
                int rowCount = jobj["rows"].Count();
                int id = 0;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int i = 0; i < rowCount; i++)
                    {
                        id = Convert.ToInt32(jobj["rows"].ElementAt(i)["id"]);

                        using (SqlCommand cmd = new SqlCommand("DeleteEmployeeQualification", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@EMPLOYEEQUALIFICATION_ID", id);

                            try
                            {
                                cmd.ExecuteNonQuery();
                            }
                            catch (SqlException ex)
                            {
                                if (ex.Number == 50001) // Custom error code from the stored procedure
                                {
                                    errorMsg += CommonFunctionalities.GetResourceString(DeleteQualificationobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                                }
                                else
                                {
                                    throw;
                                }
                            }
                        }
                    }
                }

                //gbl.InsertGPSDetails(
                //    Convert.ToInt32(DeleteQualificationobj.Company_ID),
                //    Convert.ToInt32(DeleteQualificationobj.Branch),
                //    Convert.ToInt32(DeleteQualificationobj.User_ID),
                //    Convert.ToInt32(Common.GetObjectID("CoreEmployeeMaster")),
                //    id, 0, 0, "Delete", false, Convert.ToInt32(DeleteQualificationobj.MenuID)
                //);

                errorMsg += CommonFunctionalities.GetResourceString(DeleteQualificationobj.GeneralCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.InnerException?.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint") == true)
                {
                    errorMsg += CommonFunctionalities.GetResourceString(DeleteQualificationobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    errorMsg += CommonFunctionalities.GetResourceString(DeleteQualificationobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
            }

            return new JsonResult(errorMsg);
        }


        #endregion


        #region ::: SaveQualification Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// to update the skills of the particular employee 
        /// </summary>
        /// 

        public static IActionResult SaveQualification(string connString, SaveQualificationList SaveQualificationobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jobj = JObject.Parse(SaveQualificationobj.Data);
                int rowCount = jobj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int j = 0; j < rowCount; j++)
                    {
                        string InstituteName = Common.DecryptString(jobj["rows"].ElementAt(j)["InstituteName"].ToString());
                        string QualificationName = Common.DecryptString(jobj["rows"].ElementAt(j)["QualificationName"].ToString());
                        string COURSE = Common.DecryptString(jobj["rows"].ElementAt(j)["COURSE"].ToString());
                        string PASSPERCENTAGE = Common.DecryptString(jobj["rows"].ElementAt(j)["PASSPERCENTAGE"].ToString());
                        int employeeID = Convert.ToInt32(jobj["rows"].ElementAt(j)["employeeID"].ToString());
                        string employeeQualificationID = jobj["rows"].ElementAt(j)["employeeQualiifcationID"].ToString();
                        int YOP = Convert.ToInt32(jobj["rows"].ElementAt(j)["YOP"].ToString());

                        if (!string.IsNullOrEmpty(employeeQualificationID))
                        {
                            int empQualID = Convert.ToInt32(employeeQualificationID);

                            using (SqlCommand cmd = new SqlCommand("UpdateEmployeeQualification", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@EMPLOYEEQUALIFICATION_ID", empQualID);
                                cmd.Parameters.AddWithValue("@QUALIFICATION", QualificationName);
                                cmd.Parameters.AddWithValue("@Company_Employee_ID", employeeID);
                                cmd.Parameters.AddWithValue("@INSTITUTENAME", InstituteName);
                                cmd.Parameters.AddWithValue("@COURSE", COURSE);
                                cmd.Parameters.AddWithValue("@YOP", YOP);
                                cmd.Parameters.AddWithValue("@PASSPERCENTAGE", PASSPERCENTAGE);

                                cmd.ExecuteNonQuery();
                            }
                        }
                        else
                        {
                            using (SqlCommand cmd = new SqlCommand("InsertEmployeeQualification", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@QUALIFICATION", QualificationName);
                                cmd.Parameters.AddWithValue("@Company_Employee_ID", employeeID);
                                cmd.Parameters.AddWithValue("@INSTITUTENAME", InstituteName);
                                cmd.Parameters.AddWithValue("@COURSE", COURSE);
                                cmd.Parameters.AddWithValue("@YOP", YOP);
                                cmd.Parameters.AddWithValue("@PASSPERCENTAGE", PASSPERCENTAGE);

                                SqlParameter newIDParam = new SqlParameter("@NewEmployeeQualificationID", SqlDbType.Int);
                                newIDParam.Direction = ParameterDirection.Output;
                                cmd.Parameters.Add(newIDParam);

                                cmd.ExecuteNonQuery();
                                int newEmployeeQualificationID = (int)newIDParam.Value;
                            }
                        }

                        //gbl.InsertGPSDetails(
                        //    Convert.ToInt32(SaveQualificationobj.Company_ID),
                        //    Convert.ToInt32(SaveQualificationobj.Branch),
                        //    Convert.ToInt32(SaveQualificationobj.User_ID),
                        //    Convert.ToInt32(Common.GetObjectID("CoreEmployeeMaster")),
                        //    employeeID, 0, 0, "Update", false, Convert.ToInt32(SaveQualificationobj.MenuID)
                        //);
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }


        #endregion


        #region ::: CheckEmployeeQualification Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// CheckEmployeeQualification
        /// </summary>
        /// 

        public static IActionResult CheckEmployeeQualification(string connString, CheckEmployeeQualificationList CheckEmployeeQualificationobj)
        {
            int status = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("CheckEmployeeQualification", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Qualification", CheckEmployeeQualificationobj.Qualification);
                        cmd.Parameters.AddWithValue("@Company_Employee_ID", CheckEmployeeQualificationobj.employeeID);
                        cmd.Parameters.AddWithValue("@PrimaryKey", CheckEmployeeQualificationobj.primaryKey);

                        object result = cmd.ExecuteScalar();

                        if (result != null && Convert.ToInt32(result) == 1)
                        {
                            status = 1;
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return new JsonResult(status);
        }


        #endregion


        #region ::: SelWorkingExperience Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// to select all the skills of the particular employee
        /// </summary>
        /// 


        public static IActionResult SelWorkingExperience(string connString, SelWorkingExperienceList SelWorkingExperienceobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int userLanguageID = Convert.ToInt32(SelWorkingExperienceobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelWorkingExperienceobj.GeneralLanguageID);

                List<GNM_CompanyEmployeeEXPERIENCE> employeeList = new List<GNM_CompanyEmployeeEXPERIENCE>();
                List<GNM_RefMasterDetail> departmentList = new List<GNM_RefMasterDetail>();
                List<GNM_RefMasterDetail> designationList = new List<GNM_RefMasterDetail>();

                int count = 0;
                int total = 0;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Fetch Employee Experience using SP
                    using (SqlCommand cmd = new SqlCommand("USP_GetEmployeeExperience", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@EmployeeID", SelWorkingExperienceobj.employeeID);
                        cmd.Parameters.AddWithValue("@SortColumn", sidx);
                        cmd.Parameters.AddWithValue("@SortOrder", sord);

                        SqlDataReader reader = cmd.ExecuteReader();
                        while (reader.Read())
                        {
                            employeeList.Add(new GNM_CompanyEmployeeEXPERIENCE
                            {
                                GNM_CompanyEmployeeWORKING_ID = Convert.ToInt32(reader["GNM_CompanyEmployeeWORKING_ID"]),
                                Company_Employee_ID = Convert.ToInt32(reader["Company_Employee_ID"]),
                                COMPANY_NAME = reader["COMPANY_NAME"].ToString(),
                                EXPERIENCE = Convert.ToInt32(reader["EXPERIENCE"]),
                                Department_ID = Convert.ToInt32(reader["Department_ID"]),
                                DESIGNATION_ID = Convert.ToInt32(reader["DESIGNATION_ID"])
                            });
                        }
                        count = employeeList.Count;
                    }

                    // Fetch Department Values using SP
                    using (SqlCommand cmd = new SqlCommand("USP_GetDepartments", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        SqlDataReader reader = cmd.ExecuteReader();
                        while (reader.Read())
                        {
                            departmentList.Add(new GNM_RefMasterDetail
                            {
                                RefMasterDetail_ID = Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                RefMasterDetail_Name = reader["RefMasterDetail_Name"].ToString()
                            });
                        }
                    }

                    // Fetch Designation Values using SP
                    using (SqlCommand cmd = new SqlCommand("USP_GetDesignations", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        SqlDataReader reader = cmd.ExecuteReader();
                        while (reader.Read())
                        {
                            designationList.Add(new GNM_RefMasterDetail
                            {
                                RefMasterDetail_ID = Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                RefMasterDetail_Name = reader["RefMasterDetail_Name"].ToString()
                            });
                        }
                    }

                    // Calculate total pages
                    total = rows > 0 ? Convert.ToInt32(Math.Ceiling((double)count / rows)) : 0;
                }

                // Create the department and designation value strings
                string deptmentValues = "-1:--Select--;";
                foreach (var dept in departmentList)
                {
                    deptmentValues += dept.RefMasterDetail_ID + ":" + dept.RefMasterDetail_Name.Replace(";", ":") + ";";
                }
                deptmentValues = deptmentValues.TrimEnd(';');

                string designationValues = "-1:--Select--;";
                foreach (var designation in designationList)
                {
                    designationValues += designation.RefMasterDetail_ID + ":" + designation.RefMasterDetail_Name.Replace(";", ":") + ";";
                }
                designationValues = designationValues.TrimEnd(';');

                if (SelWorkingExperienceobj.languageID == generalLanguageID)
                {
                    var arrSkillsList = from a in employeeList
                                        join dept in departmentList on a.Department_ID equals dept.RefMasterDetail_ID
                                        join desg in designationList on a.DESIGNATION_ID equals desg.RefMasterDetail_ID
                                        select new
                                        {
                                            edit = $"<a title='Edit' href='#' key='{a.GNM_CompanyEmployeeWORKING_ID}' class='editEmployeeExperiance font-icon-class'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                            delete = $"<input type='checkbox' key='{a.GNM_CompanyEmployeeWORKING_ID}' id='chk{a.GNM_CompanyEmployeeWORKING_ID}' class='chkEmployeeExperianceDelete'/>",
                                            a.GNM_CompanyEmployeeWORKING_ID,
                                            a.Company_Employee_ID,
                                            a.COMPANY_NAME,
                                            a.EXPERIENCE,
                                            DEPARTMENT = dept.RefMasterDetail_Name,
                                            DESIGNATION = desg.RefMasterDetail_Name,
                                        };


                    var sortedSelWorkingExperienceList = arrSkillsList.AsQueryable().OrderByField(sidx, sord);

                    if (_search)
                    {
                        Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                        sortedSelWorkingExperienceList = sortedSelWorkingExperienceList.FilterSearch(filtersObj);
                    }
                    var result = new
                    {
                        total,
                        page,
                        records = count,
                        data = sortedSelWorkingExperienceList,
                        deptmentValues,
                        DesignationValues = designationValues
                    };

                    return new JsonResult(result);
                }


            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }

        #endregion


        #region ::: DeleteExperiance Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// to Delete the Employee Skillset details
        /// </summary>
        /// 

        public static IActionResult DeleteExperience(string connString, DeleteExperienceList DeleteExperienceobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string errorMsg = "";
            try
            {
                JObject jobj = JObject.Parse(DeleteExperienceobj.key);
                int rowCount = jobj["rows"].Count();
                int id = 0;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int i = 0; i < rowCount; i++)
                    {
                        id = Convert.ToInt32(jobj["rows"].ElementAt(i)["id"].ToString());

                        using (SqlCommand cmd = new SqlCommand("DeleteExperience", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@ExperienceID", id);

                            object result = cmd.ExecuteScalar();
                            if (result != null)
                            {
                                string resultMessage = result.ToString();
                                if (resultMessage != "Success")
                                {
                                    errorMsg += resultMessage;
                                    break; // If there's an error, stop the process
                                }
                            }
                        }
                    }
                }

                if (string.IsNullOrEmpty(errorMsg))
                {
                    //  gbl.InsertGPSDetails(Convert.ToInt32(DeleteExperienceobj.Company_ID), Convert.ToInt32(DeleteExperienceobj.Branch), Convert.ToInt32(DeleteExperienceobj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreEmployeeMaster")), id, 0, 0, "Delete", false, Convert.ToInt32(DeleteExperienceobj.MenuID));
                    errorMsg += CommonFunctionalities.GetResourceString(DeleteExperienceobj.GeneralCulture.ToString(), "deletedsuccessfully").ToString();
                }
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    errorMsg += CommonFunctionalities.GetResourceString(DeleteExperienceobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    errorMsg += CommonFunctionalities.GetResourceString(DeleteExperienceobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
            }

            return new JsonResult(errorMsg);
        }


        #endregion


        #region ::: SaveExperiance Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// to update the skills of the particular employee 
        /// </summary>
        /// 

        public static IActionResult SaveExperience(string connString, SaveExperienceList SaveExperienceobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jobj = JObject.Parse(SaveExperienceobj.Data);
                int rowCount = jobj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int j = 0; j < rowCount; j++)
                    {
                        int employeeID = Convert.ToInt32(jobj["rows"].ElementAt(j)["employeeID"].ToString());
                        int departmentID = Convert.ToInt32(jobj["rows"].ElementAt(j)["Department_ID"].ToString());
                        string companyName = jobj["rows"].ElementAt(j)["COMPANY_NAME"].ToString();
                        int designationID = Convert.ToInt32(jobj["rows"].ElementAt(j)["DESIGNATION_ID"].ToString());
                        int experience = Convert.ToInt32(jobj["rows"].ElementAt(j)["EXPERIENCE"].ToString());
                        string employeeExperienceID;
                        var row = jobj["rows"].ElementAtOrDefault(j);
                        if (row != null && row["employeeExperienceID"] != null)
                        {
                            employeeExperienceID = row["employeeExperienceID"].ToString();
                            if (string.IsNullOrWhiteSpace(employeeExperienceID))
                            {
                                employeeExperienceID = null;
                            }
                        }
                        else
                        {

                            employeeExperienceID = null;
                        }

                        if (!string.IsNullOrEmpty(employeeExperienceID))
                        {
                            int experienceID = Convert.ToInt32(employeeExperienceID);

                            using (SqlCommand cmd = new SqlCommand("UpdateExperience", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@GNM_CompanyEmployeeWORKING_ID", experienceID);
                                cmd.Parameters.AddWithValue("@Company_Employee_ID", employeeID);
                                cmd.Parameters.AddWithValue("@Department_ID", departmentID);
                                cmd.Parameters.AddWithValue("@COMPANY_NAME", companyName);
                                cmd.Parameters.AddWithValue("@DESIGNATION_ID", designationID);
                                cmd.Parameters.AddWithValue("@EXPERIENCE", experience);

                                cmd.ExecuteNonQuery();
                            }
                        }
                        else
                        {
                            using (SqlCommand cmd = new SqlCommand("InsertExperience", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@Company_Employee_ID", employeeID);
                                cmd.Parameters.AddWithValue("@Department_ID", departmentID);
                                cmd.Parameters.AddWithValue("@COMPANY_NAME", companyName);
                                cmd.Parameters.AddWithValue("@DESIGNATION_ID", designationID);
                                cmd.Parameters.AddWithValue("@EXPERIENCE", experience);

                                cmd.ExecuteScalar(); // Retrieve the new experience ID if needed
                            }
                        }

                        //gbl.InsertGPSDetails(
                        //    Convert.ToInt32(SaveExperienceobj.Company_ID),
                        //    Convert.ToInt32(SaveExperienceobj.Branch),
                        //    Convert.ToInt32(SaveExperienceobj.User_ID),
                        //    Convert.ToInt32(Common.GetObjectID("CoreEmployeeMaster")),
                        //    employeeID,
                        //    0,
                        //    0,
                        //    "Update",
                        //    false,
                        //    Convert.ToInt32(SaveExperienceobj.MenuID)
                        //);
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }


        #endregion


        #region ::: Select Shift Type Uday Kumar J B 28-08-2024:::
        /// <summary>
        ////to Select Branch for Reports
        /// </summary> 
        /// 

        public static IActionResult SelectShiftType(string connString, SelectShiftTypeList SelectShiftTypeobj)
        {
            dynamic jsonData = null;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int userLanguageID = Convert.ToInt32(SelectShiftTypeobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectShiftTypeobj.GeneralLanguageID);
                int companyID = Convert.ToInt32(SelectShiftTypeobj.Company_ID);
                var refMasterList = new List<APINameID>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Get RefMasterID
                    int refMasterID;
                    using (SqlCommand cmd = new SqlCommand("GetRefMasterIDSelectShiftType", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        refMasterID = Convert.ToInt32(cmd.ExecuteScalar());
                    }

                    // Get Branch_ID for the Employee
                    int branchID;
                    using (SqlCommand cmd = new SqlCommand("GetBranchIDForEmployee", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Employee_ID", SelectShiftTypeobj.Employee_ID);
                        branchID = Convert.ToInt32(cmd.ExecuteScalar());
                    }

                    if (branchID > 0)
                    {
                        string payrollType;
                        using (SqlCommand cmd = new SqlCommand("GetPayrollFileType_IDSelectShiftType", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Branch_ID", branchID);
                            payrollType = cmd.ExecuteScalar().ToString();
                        }

                        string query = "";
                        if (payrollType == "SAP" || payrollType == "Factory")
                        {
                            query = "GetShiftTypesForSAPFactory";
                        }
                        else if (payrollType == "NETHRIS")
                        {
                            query = "GetShiftTypesForNethris";
                        }
                        else
                        {
                            throw new InvalidOperationException("Invalid Payroll Type");
                        }

                        using (SqlCommand cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Branch_ID", branchID);
                            cmd.Parameters.AddWithValue("@Employee_ID", SelectShiftTypeobj.Employee_ID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    refMasterList.Add(new APINameID
                                    {
                                        ID = reader.GetInt32(reader.GetOrdinal("ID")),
                                        Name = reader.GetString(reader.GetOrdinal("Name"))
                                    });
                                }
                            }
                        }

                        if (SelectShiftTypeobj.UserLanguageCode.ToString() == SelectShiftTypeobj.GeneralLanguageCode.ToString())
                        {
                            jsonData = new
                            {
                                Data = refMasterList.OrderBy(r => r.Name).Select(r => new
                                {
                                    r.ID,
                                    r.Name
                                })
                            };
                        }
                        else
                        {
                            List<GNM_RefMasterDetailLocale> refMasterDetailLocale;
                            using (SqlCommand cmd = new SqlCommand("GetRefMasterDetailSelectShiftType", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@RefMasterID", refMasterID);
                                cmd.Parameters.AddWithValue("@Language_ID", userLanguageID);

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    refMasterDetailLocale = new List<GNM_RefMasterDetailLocale>();
                                    while (reader.Read())
                                    {
                                        refMasterDetailLocale.Add(new GNM_RefMasterDetailLocale
                                        {
                                            RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                            RefMasterDetail_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"))
                                        });
                                    }
                                }
                            }

                            jsonData = new
                            {
                                Data = from refItem in refMasterList
                                       join locale in refMasterDetailLocale on refItem.ID equals locale.RefMasterDetail_ID
                                       orderby locale.RefMasterDetail_Name
                                       select new
                                       {
                                           ID = locale.RefMasterDetail_ID,
                                           Name = locale.RefMasterDetail_Name
                                       }
                            };
                        }
                    }
                    else
                    {
                        jsonData = new
                        {
                            Data = new List<object>()
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }

        #endregion


        #region ::: Select Shitf Days Uday Kumar J B 28-08-2024 :::
        /// <summary>
        ////to Select Branch for Reports
        /// </summary> 
        /// 

        public static IActionResult SelectShiftDays(string connString, SelectShiftDaysList SelectShiftDaysobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var jsonData = default(dynamic);
            string cleanedShiftType = SelectShiftDaysobj.ShiftType.Replace(",", "");
            try
            {
                int userLanguageID = Convert.ToInt32(SelectShiftDaysobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectShiftDaysobj.GeneralLanguageID);
                int Company_ID = Convert.ToInt32(SelectShiftDaysobj.Company_ID);

                int Branch_ID = 0;
                int RefMasterID = 0;
                string PayRolltype = "";

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Get Branch_ID
                    using (SqlCommand cmd = new SqlCommand("GetBranch_IDSelectShiftDays", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Employee_ID", SelectShiftDaysobj.Employee_ID);
                        Branch_ID = Convert.ToInt32(cmd.ExecuteScalar());
                    }

                    // Get RefMasterID
                    using (SqlCommand cmd = new SqlCommand("GetRefMasterIDSelectShiftDays", conn))
                    {
                        RefMasterID = Convert.ToInt32(cmd.ExecuteScalar());
                    }

                    // Get PayRolltype
                    using (SqlCommand cmd = new SqlCommand("GetPayRolltypeSelectShiftDays", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Branch_ID", Branch_ID);
                        PayRolltype = cmd.ExecuteScalar()?.ToString();
                    }

                    // Fetch shift days based on payroll type
                    List<APINameID> RefMasterList = new List<APINameID>();
                    string storedProcedure = "";

                    switch (PayRolltype)
                    {
                        case "SAP":
                            storedProcedure = "GetShiftDaysSAP";
                            break;
                        case "NETHRIS":
                            storedProcedure = "GetShiftDaysNethris";
                            break;
                        case "Factory":
                            storedProcedure = "GetShiftDaysFactory";
                            break;
                    }

                    if (!string.IsNullOrEmpty(storedProcedure))
                    {
                        using (SqlCommand cmd = new SqlCommand(storedProcedure, conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@ShiftType", cleanedShiftType);
                            cmd.Parameters.AddWithValue("@BranchID", Branch_ID);
                            cmd.Parameters.AddWithValue("@EmployeeID", SelectShiftDaysobj.Employee_ID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    RefMasterList.Add(new APINameID
                                    {
                                        ID = Convert.ToInt32(reader["ID"]),
                                        Name = reader["Name"].ToString()
                                    });
                                }
                            }
                        }
                    }

                    if (SelectShiftDaysobj.UserLanguageCode.ToString() == SelectShiftDaysobj.GeneralLanguageCode.ToString())
                    {
                        jsonData = new
                        {
                            Data = from Ref in RefMasterList
                                   orderby Ref.Name
                                   select new
                                   {
                                       ID = Ref.ID,
                                       Name = Ref.Name
                                   },
                        };
                    }
                    else
                    {
                        using (SqlCommand cmd = new SqlCommand("GetRefMasterDetailSelectShiftDays", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@RefMasterID", RefMasterID);
                            cmd.Parameters.AddWithValue("@Language_ID", userLanguageID);

                            List<APINameID> RefMasterDetailLocale = new List<APINameID>();
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    RefMasterDetailLocale.Add(new APINameID
                                    {
                                        ID = Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                        Name = reader["RefMasterDetail_Name"].ToString()
                                    });
                                }
                            }

                            jsonData = new
                            {
                                Data = from Ref in RefMasterList
                                       join RefL in RefMasterDetailLocale on Ref.ID equals RefL.ID
                                       orderby RefL.Name
                                       select new
                                       {
                                           ID = RefL.ID,
                                           Name = RefL.Name
                                       },
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }


        #endregion


        #region ::: Select Shitf  Uday Kumar J B 28-08-2024:::
        /// <summary>
        ////to Select Branch for Reports
        /// </summary> 
        /// 

        public static IActionResult SelectShift(string connString, SelectShiftList SelectShiftobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var jsonData = default(dynamic);
            string cleanedShiftDays = SelectShiftobj.ShiftDays.TrimEnd(',');
            string cleanedShiftType = SelectShiftobj.ShiftType.TrimEnd(',');
            try
            {
                int userLanguageID = Convert.ToInt32(SelectShiftobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectShiftobj.GeneralLanguageID);
                int Company_ID = Convert.ToInt32(SelectShiftobj.Company_ID);

                int Branch_ID;
                string PayRolltype;
                List<APINameID> RefMasterList = new List<APINameID>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Get Branch_ID
                    using (SqlCommand cmd = new SqlCommand("GetBranchIDForEmployee", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Employee_ID", SelectShiftobj.Employee_ID);
                        Branch_ID = Convert.ToInt32(cmd.ExecuteScalar());
                    }

                    // Get PayRolltype
                    using (SqlCommand cmd = new SqlCommand("GetPayRollTypeForBranch", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Branch_ID", Branch_ID);
                        PayRolltype = cmd.ExecuteScalar().ToString();
                    }

                    // Get RefMasterList based on PayRolltype
                    using (SqlCommand cmd = new SqlCommand("GetShiftRefMasterDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@ShiftDays", cleanedShiftDays);
                        cmd.Parameters.AddWithValue("@ShiftType", cleanedShiftType);
                        cmd.Parameters.AddWithValue("@Branch_ID", Branch_ID);
                        cmd.Parameters.AddWithValue("@Employee_ID", SelectShiftobj.Employee_ID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                RefMasterList.Add(new APINameID
                                {
                                    ID = Convert.ToInt32(reader["ID"]),
                                    Name = reader["Name"].ToString()
                                });
                            }
                        }
                    }

                    // If not the same language, get locale data
                    if (userLanguageID != generalLanguageID)
                    {
                        int RefMasterID;
                        List<GNM_RefMasterDetailLocale> RefMasterDetailLocale = new List<GNM_RefMasterDetailLocale>();

                        using (SqlCommand cmd = new SqlCommand("GetRefMasterID", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@RefMaster_Name", "SHIFT");
                            RefMasterID = Convert.ToInt32(cmd.ExecuteScalar());
                        }

                        using (SqlCommand cmd = new SqlCommand("GetRefMasterDetailLocale", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@RefMasterID", RefMasterID);
                            cmd.Parameters.AddWithValue("@LanguageID", userLanguageID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    RefMasterDetailLocale.Add(new GNM_RefMasterDetailLocale
                                    {
                                        RefMasterDetail_ID = Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                        RefMasterDetail_Name = reader["RefMasterDetail_Name"].ToString()
                                    });
                                }
                            }
                        }

                        jsonData = new
                        {
                            Data = from Ref in RefMasterList
                                   join RefL in RefMasterDetailLocale on Ref.ID equals RefL.RefMasterDetail_ID
                                   orderby RefL.RefMasterDetail_Name
                                   select new
                                   {
                                       ID = RefL.RefMasterDetail_ID,
                                       Name = RefL.RefMasterDetail_Name
                                   }
                        };
                    }
                    else
                    {
                        jsonData = new
                        {
                            Data = from Ref in RefMasterList
                                   orderby Ref.Name
                                   select new
                                   {
                                       ID = Ref.ID,
                                       Name = Ref.Name
                                   }
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }


        #endregion


        #region :::  Insert TAMS Schedule Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// To Insert TAMS Schedule
        /// </summary>
        /// 


        public static IActionResult SaveTAMSSchedule(string connString, SaveTAMSScheduleList SaveTAMSScheduleobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var jsonData = default(dynamic);
            int Count = 0;
            try
            {
                string ShiftTypeID = SaveTAMSScheduleobj.ShiftTypeID.TrimEnd(new char[] { ',' });
                string ShiftDaysID = SaveTAMSScheduleobj.ShiftDaysID.TrimEnd(new char[] { ',' });
                string Shift = SaveTAMSScheduleobj.Shift.TrimEnd(new char[] { ',' });

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Step 1: Check if there are existing rosters after the effective date for the employee
                    using (SqlCommand cmd = new SqlCommand("usp_GetRosterCount", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@EffectiveFromDate", SaveTAMSScheduleobj.EffectiveFromDate);
                        cmd.Parameters.AddWithValue("@EmployeeID", SaveTAMSScheduleobj.EmployeeID);
                        int RosterCount = Convert.ToInt32(cmd.ExecuteScalar());

                        if (RosterCount == 0)
                        {
                            // Step 2: Update the TO_DATE of the last roster for the employee
                            using (SqlCommand updateCmd = new SqlCommand("usp_UpdateRosterToDate", conn))
                            {
                                updateCmd.CommandType = CommandType.StoredProcedure;
                                updateCmd.Parameters.AddWithValue("@EffectiveFromDate", SaveTAMSScheduleobj.EffectiveFromDate);
                                updateCmd.Parameters.AddWithValue("@EmployeeID", SaveTAMSScheduleobj.EmployeeID);
                                updateCmd.ExecuteNonQuery();
                            }

                            // Step 3: Get Branch_ID for the employee
                            int Branch_ID;
                            using (SqlCommand branchCmd = new SqlCommand("usp_GetBranchIDByEmployeeID", conn))
                            {
                                branchCmd.CommandType = CommandType.StoredProcedure;
                                branchCmd.Parameters.AddWithValue("@EmployeeID", SaveTAMSScheduleobj.EmployeeID);
                                Branch_ID = Convert.ToInt32(branchCmd.ExecuteScalar());
                            }

                            // Step 4: Insert new roster
                            using (SqlCommand insertCmd = new SqlCommand("usp_InsertRoster", conn))
                            {
                                insertCmd.CommandType = CommandType.StoredProcedure;
                                insertCmd.Parameters.AddWithValue("@EmployeeID", SaveTAMSScheduleobj.EmployeeID);
                                insertCmd.Parameters.AddWithValue("@ShiftID", Convert.ToInt32(Shift));
                                insertCmd.Parameters.AddWithValue("@FromDate", SaveTAMSScheduleobj.EffectiveFromDate);
                                insertCmd.Parameters.AddWithValue("@ShiftTypeID", Convert.ToInt32(ShiftTypeID));
                                insertCmd.Parameters.AddWithValue("@ShiftDays", Convert.ToInt32(ShiftDaysID));
                                insertCmd.Parameters.AddWithValue("@BranchID", Branch_ID);
                                insertCmd.ExecuteNonQuery();
                            }
                        }
                        else
                        {
                            Count = 1;
                        }
                    }

                    jsonData = new
                    {
                        Count = Count
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }


        #endregion


        #region :::  Insert TAMS Schedule Effective From Uday Kumar J B 28-08-2024 I think This method not used check:::
        /// <summary>
        /// To Insert TAMS Schedule
        /// </summary>
        /// 

        public static IActionResult SaveTAMSScheduleEffectiveFrom(string connString, SaveTAMSScheduleEffectiveFromList SaveTAMSScheduleEffectiveFromobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var jsonData = default(dynamic);
            int Count = 0;
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Step 1: Check if there are existing rosters after the effective date for the employee (excluding the specified RosterID)
                    using (SqlCommand cmd = new SqlCommand("usp_GetRosterCountByEffectiveFromDate", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@EffectiveFromDate", SaveTAMSScheduleEffectiveFromobj.EffectiveFromDate);
                        cmd.Parameters.AddWithValue("@EmployeeID", SaveTAMSScheduleEffectiveFromobj.employeeID);
                        cmd.Parameters.AddWithValue("@RosterID", SaveTAMSScheduleEffectiveFromobj.RosterID);
                        int RosterCount = Convert.ToInt32(cmd.ExecuteScalar());

                        if (RosterCount == 0)
                        {
                            // Step 2: Update the FROM_DATE for the specified roster
                            using (SqlCommand updateCmd = new SqlCommand("usp_UpdateRosterFromDate", conn))
                            {
                                updateCmd.CommandType = CommandType.StoredProcedure;
                                updateCmd.Parameters.AddWithValue("@EffectiveFromDate", SaveTAMSScheduleEffectiveFromobj.EffectiveFromDate);
                                updateCmd.Parameters.AddWithValue("@EmployeeID", SaveTAMSScheduleEffectiveFromobj.employeeID);
                                updateCmd.Parameters.AddWithValue("@RosterID", SaveTAMSScheduleEffectiveFromobj.RosterID);
                                updateCmd.ExecuteNonQuery();
                            }
                        }
                        else
                        {
                            Count = 1;
                        }
                    }

                    jsonData = new
                    {
                        Count = Count
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }


        #endregion


        #region ::: SelEmployeeTAMSSchedule Uday Kumar J B 28-08-2024 :::
        /// <summary>
        /// to select all the branches associated with the Employee
        /// </summary>
        /// 

        public static IActionResult SelEmployeeTAMSSchedule(string connString, SelEmployeeTAMSScheduleList SelEmployeeTAMSScheduleobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int userLanguageID = Convert.ToInt32(SelEmployeeTAMSScheduleobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelEmployeeTAMSScheduleobj.GeneralLanguageID);
                int count = 0;
                int total = 0;

                List<GNM_ROSTER> RosterMasterList = new List<GNM_ROSTER>();
                List<TAMSScheduleData> arrSkillsList = new List<TAMSScheduleData>();

                // Fetch roster data using ADO.NET
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("usp_GetRosterByEmployeeID", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@EmployeeID", SelEmployeeTAMSScheduleobj.employeeID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                RosterMasterList.Add(new GNM_ROSTER
                                {
                                    ROSTER_ID = reader.IsDBNull(reader.GetOrdinal("ROSTER_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("ROSTER_ID")),
                                    EMPLOYEE_ID = reader.IsDBNull(reader.GetOrdinal("EMPLOYEE_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("EMPLOYEE_ID")),
                                    SHIFT_ID = reader.IsDBNull(reader.GetOrdinal("SHIFT_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("SHIFT_ID")),
                                    ShiftDays = reader.IsDBNull(reader.GetOrdinal("ShiftDays")) ? 0 : reader.GetInt32(reader.GetOrdinal("ShiftDays")),
                                    ShiftType_ID = reader.IsDBNull(reader.GetOrdinal("ShiftType_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("ShiftType_ID")),
                                    FROM_DATE = reader.IsDBNull(reader.GetOrdinal("FROM_DATE")) ? DateTime.MinValue : reader.GetDateTime(reader.GetOrdinal("FROM_DATE")),
                                    TO_DATE = reader.IsDBNull(reader.GetOrdinal("TO_DATE")) ? DateTime.MinValue : reader.GetDateTime(reader.GetOrdinal("TO_DATE")),
                                });
                            }
                        }
                    }

                    count = RosterMasterList.Count();
                    total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                    foreach (var roster in RosterMasterList)
                    {
                        string shiftName = "", shiftDaysName = "", shiftTypeName = "";

                        // Fetch shift name
                        using (SqlCommand cmd = new SqlCommand("usp_GetRefMasterDetail", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@RefMasterDetailID", roster.SHIFT_ID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    shiftName = reader["RefMasterDetail_Name"] != DBNull.Value ? reader["RefMasterDetail_Name"].ToString() : "";
                                }
                            }
                        }

                        // Fetch shift days name
                        using (SqlCommand cmd = new SqlCommand("usp_GetRefMasterDetail", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@RefMasterDetailID", roster.ShiftDays);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    shiftDaysName = reader["RefMasterDetail_Name"] != DBNull.Value ? reader["RefMasterDetail_Name"].ToString() : "";
                                }
                            }
                        }

                        // Fetch shift type name
                        using (SqlCommand cmd = new SqlCommand("usp_GetRefMasterDetail", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@RefMasterDetailID", roster.ShiftType_ID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    shiftTypeName = reader["RefMasterDetail_Name"] != DBNull.Value ? reader["RefMasterDetail_Name"].ToString() : "";
                                }
                            }
                        }

                        // If languageID is not equal to generalLanguageID, fetch locale names instead
                        if (SelEmployeeTAMSScheduleobj.languageID != generalLanguageID)
                        {
                            using (SqlCommand cmd = new SqlCommand("usp_GetRefMasterDetailLocale", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@RefMasterDetailID", roster.SHIFT_ID);

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        shiftName = reader["RefMasterDetail_Name"] != DBNull.Value ? reader["RefMasterDetail_Name"].ToString() : "";
                                    }
                                }
                            }

                            using (SqlCommand cmd = new SqlCommand("usp_GetRefMasterDetailLocale", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@RefMasterDetailID", roster.ShiftDays);

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        shiftDaysName = reader["RefMasterDetail_Name"] != DBNull.Value ? reader["RefMasterDetail_Name"].ToString() : "";
                                    }
                                }
                            }

                            using (SqlCommand cmd = new SqlCommand("usp_GetRefMasterDetailLocale", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@RefMasterDetailID", roster.ShiftType_ID);

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        shiftTypeName = reader["RefMasterDetail_Name"] != DBNull.Value ? reader["RefMasterDetail_Name"].ToString() : "";
                                    }
                                }
                            }
                        }

                        // Populate the TAMSScheduleData object
                        arrSkillsList.Add(new TAMSScheduleData()
                        {
                            edit = "<a title=" + CommonFunctionalities.GetGlobalResourceObject(SelEmployeeTAMSScheduleobj.UserCulture.ToString(), "edit").ToString() + " href='#' id='" + roster.ROSTER_ID + "'key='" + roster.ROSTER_ID + "' class='editTAMSSchedule font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                            delete = "<input type='checkbox' key='" + roster.ROSTER_ID + "' id='chk" + roster.ROSTER_ID + "' class='chkEmployeeBranchDelete'/>",
                            Roster_ID = roster.ROSTER_ID,
                            ShiftType = shiftTypeName,
                            ShiftDays = shiftDaysName,
                            Shift = shiftName,
                            EffectiveFromDate = Convert.ToDateTime(roster.FROM_DATE).ToString("dd-MMM-yyyy") == "01-Jan-0001" ? "" : Convert.ToDateTime(roster.FROM_DATE).ToString("dd-MMM-yyyy"),
                            EffectiveToDate = Convert.ToDateTime(roster.TO_DATE).ToString("dd-MMM-yyyy") == "01-Jan-0001" ? "" : Convert.ToDateTime(roster.TO_DATE).ToString("dd-MMM-yyyy"),
                            EffectiveFromDateStr = Convert.ToDateTime(roster.FROM_DATE),
                            EffectiveToDateStr = Convert.ToDateTime(roster.TO_DATE),
                            Employee_ID = roster.EMPLOYEE_ID
                        });
                    }
                }

                // Convert to IQueryable for further filtering
                var arrSkillsListIQ = arrSkillsList.AsQueryable();

                // FilterToolBar Search
                if (_search)
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    arrSkillsListIQ = arrSkillsListIQ.FilterSearch<TAMSScheduleData>(filtersObj);
                }

                var result = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = arrSkillsListIQ.ToArray(),
                };

                return new JsonResult(result);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }


        #endregion


        #region ::: SelectAssociatedBranch Uday Kumar J B 28-08-2024:::
        /// <summary>
        ////to Select Branch for Downlines Association
        /// </summary> 
        /// 

        public static IActionResult SelectAssociatedBranch(string connString, SelectAssociatedBranchList SelectAssociatedBranchobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var jsonData = default(dynamic);
            try
            {
                int userLanguageID = Convert.ToInt32(SelectAssociatedBranchobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectAssociatedBranchobj.GeneralLanguageID);
                int Company_ID = Convert.ToInt32(SelectAssociatedBranchobj.Company_ID);

                List<APINameID> branchList = new List<APINameID>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("usp_GetAssociatedBranches", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@EmployeeID", SelectAssociatedBranchobj.EmployeeID);
                        cmd.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                branchList.Add(new APINameID
                                {
                                    ID = reader.GetInt32(0),
                                    Name = reader.GetString(1)
                                });
                            }
                        }
                    }
                }

                if (userLanguageID == generalLanguageID)
                {
                    jsonData = new
                    {
                        branchValues = from a in branchList
                                       orderby a.Name
                                       select new
                                       {
                                           Branch_ID = a.ID,
                                           Branch_Name = a.Name
                                       }
                    };
                }
                else
                {
                    jsonData = new
                    {
                        branchValues = from a in branchList
                                       orderby a.Name
                                       select new
                                       {
                                           Branch_ID = a.ID,
                                           Branch_Name = a.Name
                                       }
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }

        #endregion


        #region ::: Select Employee Uday Kumar J B 28-08-2024 :::
        /// <summary>
        ////to Select Branch for Reports
        /// </summary> 
        /// 

        public static IActionResult SelectEmployee(string connString, SelectEmployeeList SelectEmployeeobj)
        {
            // Initialize jsonData with an empty list of dynamic objects
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var jsonData = new
            {
                EmployeeValues = new List<dynamic>()
            };

            var MasterName = SelectEmployeeobj.starts_with == null ? "" : SelectEmployeeobj.starts_with.Trim();
            try
            {
                int userLanguageID = Convert.ToInt32(SelectEmployeeobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectEmployeeobj.GeneralLanguageID);
                int Company_ID = Convert.ToInt32(SelectEmployeeobj.Company_ID);

                List<APINameID> EmployeeList = new List<APINameID>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("usp_GetEmployees", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@DesignationID", SelectEmployeeobj.DesignationID.TrimEnd(','));
                        cmd.Parameters.AddWithValue("@BranchID", SelectEmployeeobj.BranchID.TrimEnd(','));
                        cmd.Parameters.AddWithValue("@EmployeeID", SelectEmployeeobj.EmployeeID);
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                        cmd.Parameters.AddWithValue("@MasterName", MasterName);
                        cmd.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                EmployeeList.Add(new APINameID
                                {
                                    ID = reader.GetInt32(0),
                                    Name = reader.GetString(1)
                                });
                            }
                        }
                    }
                }

                var employeeValues = from a in EmployeeList
                                     orderby a.Name
                                     select new
                                     {
                                         ID = a.ID,
                                         Name = a.Name
                                     };

                // Assign the properly typed employeeValues to jsonData
                jsonData = new
                {
                    EmployeeValues = employeeValues.ToList<dynamic>()
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }




        #endregion


        #region ::: SaveEmployeeDownLines Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// to save Employee Branch Association
        /// </summary>
        /// 

        public static IActionResult SaveEmployeeDownLines(string connString, SaveEmployeeDownLinesList SaveEmployeeDownLinesobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                if (!string.IsNullOrWhiteSpace(SaveEmployeeDownLinesobj.EmployeeID))
                {
                    string EmployeeID = SaveEmployeeDownLinesobj.EmployeeID.TrimEnd(',');

                    List<GNM_EmployeeBranch> EmployeeList = new List<GNM_EmployeeBranch>();

                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        conn.Open();

                        // Fetch EmployeeBranch records
                        using (SqlCommand cmd = new SqlCommand("usp_GetEmployeeBranches", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@EmployeeID", EmployeeID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    EmployeeList.Add(new GNM_EmployeeBranch
                                    {
                                        CompanyEmployee_ID = reader.GetInt32(reader.GetOrdinal("CompanyEmployee_ID")),
                                        Branch_ID = reader.GetInt32(reader.GetOrdinal("Branch_ID"))
                                    });
                                }
                            }
                        }

                        foreach (var employee in EmployeeList)
                        {
                            int employee_ID = employee.CompanyEmployee_ID;

                            // Check if the employee downline record already exists
                            GNM_EmployeedownLines existingRecord = null;
                            using (SqlCommand checkCmd = new SqlCommand("usp_CheckEmployeedownLines", conn))
                            {
                                checkCmd.CommandType = CommandType.StoredProcedure;
                                checkCmd.Parameters.AddWithValue("@CompanyEmployeeID", employee_ID);
                                checkCmd.Parameters.AddWithValue("@ManagerEmployeeID", SaveEmployeeDownLinesobj.ManagerEmployeeID);

                                using (SqlDataReader reader = checkCmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        existingRecord = new GNM_EmployeedownLines
                                        {
                                            CompanyEmployee_ID = reader.GetInt32(reader.GetOrdinal("CompanyEmployee_ID")),
                                            ManagerEmployee_ID = reader.GetInt32(reader.GetOrdinal("ManagerEmployee_ID")),
                                            Branch_ID = reader.GetInt32(reader.GetOrdinal("Branch_ID"))
                                        };
                                    }
                                }
                            }

                            if (existingRecord != null)
                            {
                                // Update the existing record
                                using (SqlCommand updateCmd = new SqlCommand("usp_UpdateEmployeedownLines", conn))
                                {
                                    updateCmd.CommandType = CommandType.StoredProcedure;
                                    updateCmd.Parameters.AddWithValue("@BranchID", employee.Branch_ID);
                                    updateCmd.Parameters.AddWithValue("@CompanyEmployeeID", employee_ID);
                                    updateCmd.Parameters.AddWithValue("@ManagerEmployeeID", SaveEmployeeDownLinesobj.ManagerEmployeeID);
                                    updateCmd.ExecuteNonQuery();
                                }
                            }
                            else
                            {
                                // Insert a new record
                                using (SqlCommand insertCmd = new SqlCommand("usp_InsertEmployeedownLines", conn))
                                {
                                    insertCmd.CommandType = CommandType.StoredProcedure;
                                    insertCmd.Parameters.AddWithValue("@BranchID", employee.Branch_ID);
                                    insertCmd.Parameters.AddWithValue("@CompanyEmployeeID", employee_ID);
                                    insertCmd.Parameters.AddWithValue("@ManagerEmployeeID", SaveEmployeeDownLinesobj.ManagerEmployeeID);
                                    insertCmd.ExecuteNonQuery();
                                }
                            }
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }


        #endregion


        #region ::: SelAllEmployeeDownLines Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// to select all the down lines associated with the Employee
        /// </summary>
        /// 

        public static IActionResult SelAllEmployeeDownLines(string connString, SelAllEmployeeDownLinesList SelAllEmployeeDownLinesobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int userLanguageID = Convert.ToInt32(SelAllEmployeeDownLinesobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelAllEmployeeDownLinesobj.GeneralLanguageID);
                int count = 0;
                int total = 0;
                List<EmployeedownLinesData> employeedownlineList = new List<EmployeedownLinesData>();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    SqlCommand cmd = new SqlCommand(
                        SelAllEmployeeDownLinesobj.languageID == generalLanguageID ? "SelAllEmployeeDownLines" : "SelAllEmployeeDownLinesByLanguage",
                        conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@ManagerEmployeeID", SelAllEmployeeDownLinesobj.employeeID);

                    if (SelAllEmployeeDownLinesobj.languageID != generalLanguageID)
                    {
                        cmd.Parameters.AddWithValue("@LanguageID", SelAllEmployeeDownLinesobj.languageID);
                    }

                    conn.Open();
                    SqlDataReader reader = cmd.ExecuteReader();
                    while (reader.Read())
                    {
                        employeedownlineList.Add(new EmployeedownLinesData
                        {
                            EmployeeDownLine_ID = Convert.ToInt32(reader["EmployeeDownLine_ID"]),
                            Company_Employee_Name = reader["Company_Employee_Name"].ToString(),
                            Branch_Name = reader["Branch_Name"].ToString(),
                            ReportingTo = reader["ReportingTo"].ToString(),
                            Department = reader["Department"].ToString(),
                            Designation = reader["Designation"].ToString(),
                            delete = $"<input type='checkbox' key='{reader["EmployeeDownLine_ID"]}' id='chk{reader["EmployeeDownLine_ID"]}' class='chkEmployeedownLineDelete'/>"
                        });
                    }
                }

                count = employeedownlineList.Count;
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                // Sorting and Paging
                var sortedList = employeedownlineList.AsQueryable().OrderByField(sidx, sord);

                if (_search)
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    sortedList = sortedList.FilterSearch(filtersObj);
                }

                var paginatedList = sortedList.Skip((page - 1) * rows).Take(rows).ToList();

                var result = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = paginatedList
                };

                return new JsonResult(result);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }


        #endregion


        #region ::: DeleteDownLineDetails Uday Kumar J B 28-08-2024 :::
        /// <summary>
        /// to Delete the Employee DownLine details
        /// </summary>
        /// 

        public static IActionResult DeleteDownLineDetails(string connString, DeleteDownLineDetailsList DeleteDownLineDetailsobj)
        {
            string errorMsg = "";
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jobj = JObject.Parse(DeleteDownLineDetailsobj.key);
                int rowCount = jobj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int i = 0; i < rowCount; i++)
                    {
                        int id = jobj["rows"].ElementAt(i)["id"].Value<int>();

                        using (SqlCommand cmd = new SqlCommand("usp_DeleteDownLineDetails", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@EmployeeDownLineID", id);

                            try
                            {
                                cmd.ExecuteNonQuery();
                            }
                            catch (SqlException ex)
                            {
                                // Handle specific SQL exceptions
                                if (ex.Number == 547) // Foreign key constraint violation
                                {
                                    errorMsg += CommonFunctionalities.GetResourceString(DeleteDownLineDetailsobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                                }
                                else
                                {
                                    throw;
                                }
                            }
                        }
                    }
                }

                // Assuming `gbl.InsertGPSDetails` and `Common.GetObjectID` are defined elsewhere
                // gbl.InsertGPSDetails(Convert.ToInt32(DeleteDownLineDetailsobj.Company_ID), Convert.ToInt32(DeleteDownLineDetailsobj.Branch), Convert.ToInt32(DeleteDownLineDetailsobj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreEmployeeMaster")), 0, 0, 0, "Delete", false, Convert.ToInt32(DeleteDownLineDetailsobj.MenuID));

                errorMsg += CommonFunctionalities.GetResourceString(DeleteDownLineDetailsobj.UserCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (Exception ex)
            {
                // Log exception
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                errorMsg += CommonFunctionalities.GetResourceString(DeleteDownLineDetailsobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
            }
            return new JsonResult(errorMsg);
        }


        #endregion


        #region ::: Core Employee Master List and obj Classes Uday Kumar J B 28-08-2024 :::
        /// <summary>
        /// Core Employee Master List and obj Classes
        /// </summary>
        /// 

        public class EmployeeExportList
        {
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public string GeneralCulture { get; set; }
            public string UserCulture { get; set; }
            public int exprtType { get; set; }
            public int MenuID { get; set; }
            public int User_ID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filter { get; set; }
            public string advanceFilter { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int langaugeID { get; set; }
        }
        public class SelectAllEmployeeDetailsList
        {
            public List<GNM_User> UserDetails { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int langaugeID { get; set; }
        }

        public class SelEmployeelocaleList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int id { get; set; }
        }

        public class SelectSpecificEmployeeDetailsList
        {
            public List<GNM_User> UserDetails { get; set; }
            public int User_ID { get; set; }
            public int Branch { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int languageID { get; set; }
            public int id { get; set; }
            public int Company_ID { get; set; }

        }

        public class InsertEmployeeDetailsList
        {
            public int Branch { get; set; }
            public int User_Employee_ID { get; set; }
            public string EmployeeData { get; set; }
            public List<GNM_User> UserDetails { get; set; }
            public int MenuID { get; set; }
            public int User_ID { get; set; }
            public int Company_ID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }

        public class UpdateEmployeeDetailsList
        {
            public int Branch { get; set; }
            public int Company_ID { get; set; }
            public int User_Employee_ID { get; set; }
            public string EmployeeData { get; set; }
            public int User_ID { get; set; }
            public List<GNM_User> UserDetails { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }

        public class InsertEmployeeLocaleList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string employeeLocaleData { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }

        }

        public class UpdateEmployeeLocaleList
        {
            public string employeeLocaleData { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }


        public class DeleteCompanyEmployeeList
        {
            public List<GNM_User> UserDetails { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public string key { get; set; }
            public int MenuID { get; set; }
            public int User_ID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string UserCulture { get; set; }
        }

        public class CheckEmployeeSkillsList
        {
            public int skillID { get; set; }
            public int employeeID { get; set; }
            public int primaryKey { get; set; }
        }

        public class SelAllEmployeeSkillsetList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int employeeID { get; set; }
            public string UserCulture { get; set; }
            public int languageID { get; set; }
        }

        public class SelEmployeeTrainingDetailsList
        {
            public int employeeID { get; set; }
            public string UserCulture { get; set; }
        }
        public class CheckETODetailsList
        {
            public int Year { get; set; }
            public int employeeID { get; set; }
        }

        public class CheckETODetailsWithBankCodelist
        {
            public int Year { get; set; }
            public int employeeID { get; set; }
            public int BankCodeID { get; set; }
        }

        public class SelEmployeeETODetailsList
        {
            public int employeeID { get; set; }
            public string UserCulture { get; set; }
        }
        public class SelIncentiveLogDetailsList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int employeeID { get; set; }
            public string UserCulture { get; set; }
        }

        public class SelectSpecificEmployeeTrainingLocaleDetailsList
        {
            public int UserLanguageID { get; set; }
            public int id { get; set; }
        }
        public class SaveEmpTrainingLocaleContactdetailsList
        {
            public string LocaleData { get; set; }
        }

        public class SaveTrainingDetailsList
        {
            public int Company_ID { get; set; }
            public string data { get; set; }
        }

        public class SaveEmployeeETODetailsList
        {
            public int Company_ID { get; set; }
            public string data { get; set; }
        }

        public class SaveSkillsList
        {
            public string Data { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }

        public class DeleteSkillsList
        {
            public string key { get; set; }
            public string UserCulture { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }
        public class SelAllEmployeeBranchesList
        {
            public int employeeID { get; set; }
            public int languageID { get; set; }
            public string UserCulture { get; set; }
        }

        public class SaveEmployeeBranchList
        {
            public string Data { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }

        public class SaveEmployeeBranchEditModeList
        {
            public string Data { get; set; }
        }
        public class DeleteEmployeeBranchList
        {
            public string key { get; set; }
            public string empBranchData { get; set; }
            public string UserCulture { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }
        public class CheckEmployeeBranchList
        {
            public int branchID { get; set; }
            public int employeeID { get; set; }
            public int primaryKey { get; set; }
        }
        public class SelectStateList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int languageID { get; set; }
            public int countryID { get; set; }
        }
        public class CheckEmployeeIDList
        {
            public string employeeID { get; set; }
            public List<GNM_User> UserDetails { get; set; }
            public int primaryKey { get; set; }
            public int Company_ID { get; set; }
        }

        public class SelectAllEmployeeMastersDataList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public List<GNM_User> UserDetails { get; set; }
            public int languageID { get; set; }
            public int countryID { get; set; }
            public int Company_ID { get; set; }
        }

        public class DeleteTrainingDetailsList
        {
            public string key { get; set; }
            public string UserCulture { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }
        public class DeleteETODetailsList
        {
            public string key { get; set; }
            public string UserCulture { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }
        public class SelectRegionForReportList
        {
            public int User_ID { get; set; }
            public bool OEM { get; set; }
        }
        public class SelectCompanyForReportList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Company_ID { get; set; }
            public string RegionID { get; set; }
        }
        public class SelectBranchForReportList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Company_ID { get; set; }
            public string CompanyID { get; set; }
            public string RegionID { get; set; }
        }

        public class SelectWarehouseForReportList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string BranchID { get; set; }
            public string CompanyID { get; set; }
        }
        public class SelEmployeeQualificationList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int employeeID { get; set; }
        }
        public class DeleteQualificationList
        {
            public string key { get; set; }
            public string GeneralCulture { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }
        public class SaveQualificationList
        {
            public string Data { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }
        public class CheckEmployeeQualificationList
        {
            public string Qualification { get; set; }
            public int employeeID { get; set; }
            public int primaryKey { get; set; }
        }

        public class SelWorkingExperienceList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int employeeID { get; set; }
            public int languageID { get; set; }
        }
        public class DeleteExperienceList
        {
            public string key { get; set; }
            public string GeneralCulture { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }

        public class SaveExperienceList
        {
            public string Data { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }
        public class SelectShiftTypeList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int Employee_ID { get; set; }
            public string UserLanguageCode { get; set; }
            public string GeneralLanguageCode { get; set; }
        }
        public class SelectShiftDaysList
        {
            public string ShiftType { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int Employee_ID { get; set; }
            public string UserLanguageCode { get; set; }
            public string GeneralLanguageCode { get; set; }
        }
        public class SelectShiftList
        {
            public string ShiftDays { get; set; }
            public string ShiftType { get; set; }
            public int Employee_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Company_ID { get; set; }
        }
        public class SaveTAMSScheduleList
        {
            public string ShiftTypeID { get; set; }
            public string ShiftDaysID { get; set; }
            public string Shift { get; set; }
            public int EmployeeID { get; set; }
            public DateTime EffectiveFromDate { get; set; }
        }
        public class SaveTAMSScheduleEffectiveFromList
        {
            public int employeeID { get; set; }
            public DateTime EffectiveFromDate { get; set; }
            public int RosterID { get; set; }
        }
        public class SelEmployeeTAMSScheduleList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int employeeID { get; set; }
            public int languageID { get; set; }
            public string UserCulture { get; set; }
        }
        public class SelectAssociatedBranchList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int EmployeeID { get; set; }
        }
        public class SelectEmployeeList
        {
            public string DesignationID { get; set; }
            public string BranchID { get; set; }
            public int EmployeeID { get; set; }
            public string starts_with { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Company_ID { get; set; }
        }
        public class SaveEmployeeDownLinesList
        {
            public string EmployeeID { get; set; }
            public int ManagerEmployeeID { get; set; }
        }
        public class SelAllEmployeeDownLinesList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int employeeID { get; set; }
            public int languageID { get; set; }
        }

        public class DeleteDownLineDetailsList
        {
            public string key { get; set; }
            public string UserCulture { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }
        public class SaveFileToServerList
        {
            public int EmployeeID { get; set; }
            public string postedFile { get; set; }
            public int User_ID { get; set; }
        }
        #endregion


        #region ::: Core Employee Master  Classes Uday Kumar J B 28-08-2024 :::
        /// <summary>
        /// Core Employee Master  Classes
        /// </summary>
        /// 
        public class EmployeeData
        {
            public string IsActive { get; set; }
            public string edit { get; set; }
            public string delete { get; set; }
            public string view { get; set; }
            public int Company_Employee_ID { get; set; }
            public string Company_Employee_Name { get; set; }
            public string EmployeeCode { get; set; }
            public string Company_Employee_MobileNumber { get; set; }
            public string Company_Employee_Email { get; set; }
            public string Department { get; set; }
            public string Designation { get; set; }
            public string Local { get; set; }
            public string Manager { get; set; }
            public string Country { get; set; }
            public string State { get; set; }
            public string Address { get; set; }
            public string Location { get; set; }
            public string Zipcode { get; set; }
            public string Landline { get; set; }
            public string ActiveFromstring { get; set; }
            public string AtiveTostring { get; set; }
            public decimal? HourlyRate { get; set; }
            public string RegionName { get; set; }
            public byte ExemptionHours { get; set; }
            public bool IsEligibleForOT { get; set; }
        }
        public class BranchData
        {
            public string view { get; set; }
            public string edit { get; set; }
            public string delete { get; set; }
            public int Branch_ID { get; set; }
            public string Branch_Name { get; set; }
            public string Branch_Phone { get; set; }
            public string Branch_Location { get; set; }
            public string Branch_Email { get; set; }
            public string Branch_HeadOffice { get; set; }
            public string Branch_External { get; set; }
            public string Branch_Active { get; set; }
            public string Local { get; set; }
            public string IsOverTimeDWM { get; set; }
        }

        public class EmployeeSkillData
        {
            public string view { get; set; }
            public string edit { get; set; }
            public string delete { get; set; }
            public int Employee_Skillset_ID { get; set; }
            public int CompnayEmployee_ID { get; set; }
            public int Skillset_ID { get; set; }
            public string Skillset { get; set; }
            public int Employee_Skillset_Rating { get; set; }
            public string Level_ID { get; set; }
        }

        public partial class GNM_ProductType
        {
            public GNM_ProductType()
            {
                this.GNM_ProductTypeLocale = new HashSet<GNM_ProductTypeLocale>();
            }

            public int ProductType_ID { get; set; }
            public int Brand_ID { get; set; }
            public string ProductType_Name { get; set; }
            public bool ProductType_IsActive { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }

            public virtual ICollection<GNM_ProductTypeLocale> GNM_ProductTypeLocale { get; set; }
        }

        public partial class GNM_ProductTypeLocale
        {
            public int ProductTypeLocale_ID { get; set; }
            public int ProductType_ID { get; set; }
            public string ProductType_Name { get; set; }
            public int Language_ID { get; set; }

            public virtual GNM_ProductType GNM_ProductType { get; set; }
        }

        public class EmployeeETO
        {
            public string view { get; set; }
            public string edit { get; set; }
            public string delete { get; set; }
            public int ID { get; set; }
            public string HoursAvailable { get; set; }
            public string UsedHours { get; set; }
            public string PendingHours { get; set; }
            public int Year { get; set; }
            public int Employee_ID { get; set; }
            public string ModifiedBy { get; set; }
            public string ModifiedDate { get; set; }
            public string IsAdded { get; set; }
            public string Remarks { get; set; }
            public string CarryoverHours { get; set; }
            public string UsedCarryoverHours { get; set; }
            public string TotalUsedHours { get; set; }
            public string BankCodeCategory { get; set; }
            public string Validity { get; set; }
        }
        public class EmployeeBranchData
        {
            public string view { get; set; }
            public string edit { get; set; }
            public string delete { get; set; }
            public int EmployeeBranch_ID { get; set; }
            public string Branch { get; set; }
            public string State_Name { get; set; }
            public string Region_Name { get; set; }
            public string Country_Name { get; set; }
            public string Company_Name { get; set; }
            public string IsDefault { get; set; }
            public int BranchID { get; set; }
        }

        public class APINameValue
        {
            public string Name { get; set; }
            public dynamic Value { get; set; }
        }
        public class APINameID
        {
            public int ID { get; set; }
            public string Name { get; set; }
        }
        public class QualificationData
        {
            public int EMPLOYEEQUALIFICATION_ID { get; set; }
            public int Company_Employee_ID { get; set; }
            public string INSTITUTENAME { get; set; }
            public string PASSPERCENTAGE { get; set; }
            public int? YOP { get; set; }
            public string QUALIFICATION { get; set; }
            public string COURSE { get; set; }
            public string edit { get; set; }
            public string delete { get; set; }
        }
        public partial class GNM_ROSTER
        {
            public int ROSTER_ID { get; set; }
            public int EMPLOYEE_ID { get; set; }
            public int SHIFT_ID { get; set; }
            public System.DateTime FROM_DATE { get; set; }
            public Nullable<System.DateTime> TO_DATE { get; set; }
            public Nullable<int> ShiftType_ID { get; set; }
            public Nullable<int> ShiftDays { get; set; }
            public Nullable<int> Branch_ID { get; set; }
        }
        public class TAMSScheduleData
        {
            public string view { get; set; }
            public string edit { get; set; }
            public string delete { get; set; }
            public int Roster_ID { get; set; }
            public string Branch { get; set; }
            public string ShiftType { get; set; }
            public string ShiftDays { get; set; }
            public string Shift { get; set; }
            public string EffectiveFromDate { get; set; }
            public string EffectiveToDate { get; set; }
            public int BranchID { get; set; }
            public DateTime EffectiveFromDateStr { get; set; }
            public DateTime EffectiveToDateStr { get; set; }
            public int Employee_ID { get; set; }
        }
        public class EmployeedownLinesData
        {
            public string delete { get; set; }
            public int EmployeeDownLine_ID { get; set; }
            public string Branch_Name { get; set; }
            public string Company_Employee_Name { get; set; }
            public string ReportingTo { get; set; }
            public string Department { get; set; }
            public string Designation { get; set; }
        }
        #endregion

    }
}
