﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreMovementTypeServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreMovementTypeController : ApiController
    {


        #region ::: Select Uday Kumar J B 14-08-2024:::
        /// <summary>
        /// To select the All parts 
        /// </summary>    
        /// 
        [Route("api/CoreMovementType/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] SelectCoreMovementTypeList SelectCoreMovementTypeLandobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreMovementTypeServices.Select(connString, SelectCoreMovementTypeLandobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Insert Uday Kumar J B 14-08-2024:::
        /// <summary>
        /// Method to insert the Parts Master Header Table
        /// </summary>   
        /// 
        [Route("api/CoreMovementType/Insert")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Insert([FromBody] InsertCoreMovementTypeList InsertCoreMovementTypeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreMovementTypeServices.Insert(connString, InsertCoreMovementTypeobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Delete Uday Kumar J B 14-08-2024:::
        /// <summary>
        /// to Delete the Parts
        /// </summary>
        /// 
        [Route("api/CoreMovementType/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteCoreMovementTypeList DeleteCoreMovementTypeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreMovementTypeServices.Delete(connString, DeleteCoreMovementTypeobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Export Uday Kumar J B 14-08-2024:::
        /// <summary>
        /// Exporting Parts Grid
        /// </summary>
        /// 
        [Route("api/CoreMovementType/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] ExportCoreMovementTypeList ExportCoreMovementTypeobj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportCoreMovementTypeobj.sidx;
            string sord = ExportCoreMovementTypeobj.sord;
            string filter = ExportCoreMovementTypeobj.filter;
            string advnceFilter = ExportCoreMovementTypeobj.advanceFilter;

            try
            {


                object Response = await CoreMovementTypeServices.Export(ExportCoreMovementTypeobj, connstring, filter, advnceFilter, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion


        #region ::: InsertLocale Uday Kumar J B 14-08-2024:::
        /// <summary>
        /// To select the All parts 
        /// </summary> 
        /// 
        [Route("api/CoreMovementType/InsertLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertLocale([FromBody] InsertLocaleCoreMovementTypeList InsertLocaleCoreMovementTypeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreMovementTypeServices.InsertLocale(connString, InsertLocaleCoreMovementTypeobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectSingleLocale Uday Kumar J B 14-08-2024 :::
        /// <summary>
        /// Select Single Locale
        /// </summary>
        /// 
        [Route("api/CoreMovementType/SelectSingleLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSingleLocale([FromBody] SelectSingleLocaleList SelectSingleLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreMovementTypeServices.SelectSingleLocale(connString, SelectSingleLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectMovemenTypeLocale Uday Kumar J B 14-08-2024:::
        /// <summary>
        /// To select the All parts 
        /// </summary>  
        /// 
        [Route("api/CoreMovementType/SelectMovemenTypeLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectMovemenTypeLocale([FromBody] SelectMovemenTypeLocaleList SelectMovemenTypeLocaleobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = " ";


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreMovementTypeServices.SelectMovemenTypeLocale(connString, SelectMovemenTypeLocaleobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckMovementType Uday Kumar J B 14-08-2024 :::
        /// <summary>
        /// To Check if Movement type name Already exists /// 
        /// </summary>
        /// 
        [Route("api/CoreMovementType/CheckMovementType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckMovementType([FromBody] CheckMovementTypeList CheckMovementTypeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreMovementTypeServices.CheckMovementType(connString, CheckMovementTypeobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckMovementTypeLocale Uday Kumar J B 14-08-2024:::
        /// <summary>
        /// To Check MovementTypeLocale already exists 
        /// </summary>
        /// 
        [Route("api/CoreMovementType/CheckMovementTypeLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckMovementTypeLocale([FromBody] CheckMovementTypeLocaleList CheckMovementTypeLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreMovementTypeServices.CheckMovementTypeLocale(connString, CheckMovementTypeLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion

    }
}