﻿using AMMSCore.Models;

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Org.BouncyCastle.Asn1.Cmp;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Data.SqlTypes;
using System.Drawing.Text;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Cryptography.Pkcs;
using System.Threading.Tasks;
using System.Web;
using System.Xml;
using System.Xml.Linq;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.Utilities.CoreCompanyCalenderMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;
namespace SharedAPIClassLibrary_AMERP
{
    public class CoreCompanyMasterServices
    {
        static string AppPath = string.Empty;

        #region:::SelectAllCompanies /Mithun:::
        /// <summary>
        /// to get all the Company Pages
        /// </summary>
        /// <returns></returns>
        public static IActionResult SelectAllCompanies(SelectAllCompaniesList SelectAllCompaniesObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {
            try
            {
                int count = 0;
                int total = 0;
                List<CompanyData> companyList = new List<CompanyData>();

                int userLanguageID = Convert.ToInt32(SelectAllCompaniesObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectAllCompaniesObj.GeneralLanguageID);
                string manufacturer = CommonFunctionalities.GetResourceString(SelectAllCompaniesObj.GeneralCulture.ToString(), "Manufacturer").ToString();
                string dealer = CommonFunctionalities.GetResourceString(SelectAllCompaniesObj.GeneralCulture.ToString(), "Dealer").ToString();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Get the total count of companies
                    using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM GNM_Company", conn))
                    {
                        count = (int)cmd.ExecuteScalar();
                    }

                    total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                    // Use the stored procedure to fetch company data
                    string query = "UP_SEL_AMERP_SelectAllCompanies";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                companyList.Add(new CompanyData
                                {
                                    edit = $"<a title='View' href='#' id='{reader.GetInt32(reader.GetOrdinal("Company_ID"))}' key='{reader.GetInt32(reader.GetOrdinal("Company_ID"))}' class='editCompanyMaster font-icon-class'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                    delete = $"<input type='checkbox' key='{reader.GetInt32(reader.GetOrdinal("Company_ID"))}' defaultchecked='' id='chk{reader.GetInt32(reader.GetOrdinal("Company_ID"))}' class='chkCompanyHeaderDelete'/>",
                                    Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                    Company_Name = reader.IsDBNull(reader.GetOrdinal("CompanyName")) ? string.Empty : reader.GetString(reader.GetOrdinal("CompanyName")),
                                    Type = reader.IsDBNull(reader.GetOrdinal("Company_Type")) ? dealer : (reader.GetString(reader.GetOrdinal("Company_Type")) == "M" ? manufacturer : dealer),
                                    Local = userLanguageID != generalLanguageID ? $"<img key='{reader.GetInt32(reader.GetOrdinal("Company_ID"))}' src='{AppPath}/Content/Images/local.png' class='companyLocale' alt='Localize' width='20' height='20' title='Localize'/>" : "",
                                    ShortName = reader.IsDBNull(reader.GetOrdinal("Company_ShortName")) ? string.Empty : reader.GetString(reader.GetOrdinal("Company_ShortName")),
                                    ParentCompany = reader.IsDBNull(reader.GetOrdinal("Company_Parent_ID")) ? string.Empty : GetParentCompanyName(Convert.ToInt32(reader["Company_Parent_ID"]), constring),
                                    LogoName = reader.IsDBNull(reader.GetOrdinal("Company_LogoName")) ? string.Empty : reader.GetString(reader.GetOrdinal("Company_LogoName")),
                                    Address = reader.IsDBNull(reader.GetOrdinal("Company_Address")) ? string.Empty : reader.GetString(reader.GetOrdinal("Company_Address")),
                                    Currency = reader.IsDBNull(reader.GetOrdinal("CurrencyName")) ? string.Empty : reader.GetString(reader.GetOrdinal("CurrencyName")),
                                    Remarks = reader.IsDBNull(reader.GetOrdinal("Remarks")) ? string.Empty : reader.GetString(reader.GetOrdinal("Remarks")),
                                    DefaultgridSize = reader.IsDBNull(reader.GetOrdinal("DefaultGridSize")) ? (byte)0 : reader.GetByte(reader.GetOrdinal("DefaultGridSize")),
                                    JobcardCushionhours = reader.IsDBNull(reader.GetOrdinal("JobCardCushionHours")) ? 0 : reader.GetDecimal(reader.GetOrdinal("JobCardCushionHours")),
                                    Isactive = reader.GetBoolean(reader.GetOrdinal("Company_Active")) ? "Yes" : "No",
                                    CompanyTheme = reader.IsDBNull(reader.GetOrdinal("ThemeName")) ? string.Empty : reader.GetString(reader.GetOrdinal("ThemeName"))
                                });
                            }
                        }
                    }
                }

                var x = default(dynamic);

                // Apply Filters, Advanced Search, and Sorting as before
                IQueryable<CompanyData> iCompanyArray = companyList.AsQueryable();

                // FilterToolbar Search
                if (_search)
                {
                    string urlDecodedFilters = HttpUtility.UrlDecode(filters); // URL decoding
                    string decryptedFilters = Common.DecryptString(urlDecodedFilters); // Decrypt the 'filters' string if necessary
                    JObject jsonFilters = JObject.Parse(decryptedFilters); // Parse JSON

                    Filters filtersobj = jsonFilters.ToObject<Filters>();
                    iCompanyArray = iCompanyArray.FilterSearch(filtersobj);
                }
                else if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse(Query).ToObject<AdvanceFilter>();
                    iCompanyArray = iCompanyArray.AdvanceSearch(advnfilter);
                    page = 1;
                }

                // Sorting
                iCompanyArray = iCompanyArray.OrderByField(sidx, sord);

                count = iCompanyArray.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                x = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = iCompanyArray.ToList().Paginate(page, rows)
                };

                return new JsonResult(x);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
        }

        private static string GetParentCompanyName(int parentId, string constring)
        {
            if (parentId == 0) return string.Empty;  // Return empty string if no parent

            using (SqlConnection conn = new SqlConnection(constring))
            {
                conn.Open();
                using (SqlCommand cmd = new SqlCommand("SELECT Company_Name FROM GNM_Company WHERE Company_ID = @ParentId", conn))
                {
                    cmd.Parameters.AddWithValue("@ParentId", parentId);
                    object result = cmd.ExecuteScalar();
                    return result != null ? result.ToString() : string.Empty;  // Return empty string if no result
                }
            }
        }


        #endregion

        #region ::: SelectParticularCompany /Mithun:::
        /// <summary>
        /// to get details of the Particular company
        /// </summary>  
        public static IActionResult SelectParticularCompany(SelectParticularCompanyList SelectParticularCompanyObj, string constring, int LogException)
        {
            try
            {
                dynamic x = null;
                int userLanguageID = Convert.ToInt32(SelectParticularCompanyObj.UserLanguageID);
                //GNM_User User = (GNM_User)Session["UserDetails"];
                //GNM_User User = SelectParticularCompanyObj.UserDetails.FirstOrDefault();
                int generalLanguageID = Convert.ToInt32(SelectParticularCompanyObj.GeneralLanguageID);

                // Assuming connectionString is defined somewhere in your application


                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    SqlCommand command = new SqlCommand("Up_Sel_Am_Erp_SelectParticularCompanyDeatails", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    // Add parameters to the stored procedure
                    command.Parameters.AddWithValue("@companyID", SelectParticularCompanyObj.id);
                    command.Parameters.AddWithValue("@languageID", SelectParticularCompanyObj.languageID);
                    command.Parameters.AddWithValue("@generalLanguageID", generalLanguageID);
                    command.Parameters.AddWithValue("@userLanguageID", userLanguageID);

                    SqlDataReader reader = command.ExecuteReader();
                    if (reader.Read())
                    {
                        x = new
                        {
                            Company_ID = reader["Company_ID"],
                            Name = reader["Name"],
                            ShortName = reader["ShortName"],
                            Currency = reader["Currency"],
                            CompanyType = reader["CompanyType"],
                            Active = reader["Active"],
                            LogoName = reader["LogoName"],
                            Address1 = reader["Address"],
                            ParentCompany = reader["ParentCompany"],
                            Remarks = reader["Remarks"],
                            DefaultGridSize = reader["DefaultGridSize"],
                            JobCardCushionHours = reader["JobCardCushionHours"],
                            CompanyTheme_ID = reader["CompanyTheme"],
                            CompanyFont = reader["CompanyFont"],
                            OrderingCost = reader["OrderingCost"],
                            InventoryCarryingFactoy_Percentage = reader["InventoryCarryingFactoy_Percentage"]
                        };
                    }
                    reader.Close();
                }

                //return Json(x, JsonRequestBehavior.AllowGet);
                return new JsonResult(x);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }


        #endregion

        #region ::: SelectCompanyLocaleDetails /Mithun:::
        /// <summary>
        /// to get details of the Particular company Locale details
        /// </summary>  
        public static IActionResult SelectCompanyLocaleDetails(SelectCompanyLocaleDetailsList SelectCompanyLocaleDetailsObj, string constring, int LogException)
        {
            try
            {
                int userLanguageID = Convert.ToInt32(SelectCompanyLocaleDetailsObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectCompanyLocaleDetailsObj.GeneralLanguageID);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_SelectCompanyLocaleDetails", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", SelectCompanyLocaleDetailsObj.id);
                        cmd.Parameters.AddWithValue("@Language_ID", userLanguageID);

                        SqlDataReader reader = cmd.ExecuteReader();
                        dynamic x = null;

                        if (reader.Read())
                        {
                            x = new
                            {
                                CompanyID = reader["CompanyID"],
                                CompanyGName = reader["CompanyGName"],
                                CompanyGSName = reader["CompanyGSName"],
                                CompanyGAddress = reader["CompanyGAddress"],
                                CompanyLName = reader["CompanyLName"],
                                CompanyLSName = reader["CompanyLSName"],
                                CompanyLAddress1 = reader["CompanyLAddress1"],
                                CompanyLID = reader["CompanyLID"],
                                CompanyLanguageID = reader["CompanyLanguageID"]
                            };
                        }
                        else
                        {
                            x = new
                            {
                                CompanyID = SelectCompanyLocaleDetailsObj.id,
                                CompanyGName = "",
                                CompanyGSName = "",
                                CompanyGAddress = "",
                                CompanyLName = "",
                                CompanyLSName = "",
                                CompanyLAddress1 = "",
                                CompanyLID = "",
                                CompanyLanguageID = ""
                            };
                        }

                        reader.Close();
                        //return Json(x, JsonRequestBehavior.AllowGet);
                        return new JsonResult(x);
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: SelectCountry /Mithun:::
        /// <summary>
        /// to get all countries
        /// </summary>  
        public static IActionResult SelectCountry(SelectCountryList SelectCountryObj, string constring, int LogException)
        {
            int userLanguageID = Convert.ToInt32(SelectCountryObj.UserLanguageID);
            int generalLanguageID = Convert.ToInt32(SelectCountryObj.GeneralLanguageID);
            int CompanyID = Convert.ToInt32(SelectCountryObj.Company_ID);


            List<dynamic> regionArray = new List<dynamic>();

            using (var connection = new SqlConnection(constring))
            {
                connection.Open();
                using (var command = new SqlCommand("Up_Sel_Am_Erp_SelectCountryForBranch", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@LanguageID", SelectCountryObj.languageID);
                    command.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                    command.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);
                    command.Parameters.AddWithValue("@CompanyID", CompanyID);

                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var country = new
                            {
                                RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                RefMasterDetail_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"))
                            };
                            regionArray.Add(country);
                        }
                    }
                }
            }

            //return Json(regionArray, JsonRequestBehavior.AllowGet);
            return new JsonResult(regionArray);
        }

        #endregion

        #region ::: Select Currency /Mithun:::
        /// <summary>
        /// to Select Currency
        /// </summary>
        public static IActionResult SelectCurrency(SelectCurrencyList SelectCurrencyObj, string constring, int LogException)
        {
            var currencyArray = new List<dynamic>();

            try
            {
                int userLanguageID = Convert.ToInt32(SelectCurrencyObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectCurrencyObj.GeneralLanguageID);
                int companyID = Convert.ToInt32(SelectCurrencyObj.Company_ID);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetCurrencyDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                currencyArray.Add(new
                                {
                                    RefMasterDetail_ID = reader["RefMasterDetail_ID"],
                                    RefMasterDetail_Name = reader["RefMasterDetail_Name"]
                                });
                            }
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }

            //return Json(currencyArray, JsonRequestBehavior.AllowGet);
            return new JsonResult(currencyArray);
        }

        #endregion

        #region ::: SelectState /Mithun:::
        /// <summary>
        /// to get all the states for the selected country
        /// </summary> 
        public static IActionResult SelectState(SelectStateList SelectStateObj, string constring, int LogException)
        {
            int userLanguageID = Convert.ToInt32(SelectStateObj.UserLanguageID);
            int generalLanguageID = Convert.ToInt32(SelectStateObj.GeneralLanguageID);

            try
            {
                List<dynamic> regionArray = new List<dynamic>();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetStatesDropDownCountrySelected", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CountryID", SelectStateObj.countryID);
                        cmd.Parameters.AddWithValue("@LanguageID", SelectStateObj.languageID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var state = new
                                {
                                    State_id = reader["State_id"],
                                    State_Name = reader["State_Name"]
                                };
                                regionArray.Add(state);
                            }
                        }
                    }
                }

                //return Json(regionArray, JsonRequestBehavior.AllowGet);
                return new JsonResult(regionArray);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: Update Company header /Mithun:::
        /// <summary>
        /// to update the Company Header
        /// </summary> 
        public static IActionResult Update(UpdateCoreCompanyList UpdateObj, string constring, int LogException)
        {

            try
            {
                int parentCompayID = 0;
                //GNM_User User = (GNM_User)Session["UserDetails"];
                // GNM_User User = UpdateObj.UserDetails.FirstOrDefault();
                int userID = UpdateObj.User_ID;

                JObject jObj = JObject.Parse(UpdateObj.data);
                int companyID = Convert.ToInt32(jObj["CompanyID"]);
                parentCompayID = jObj["parentCompayID"] != null && jObj["parentCompayID"].ToString() != "" ? Convert.ToInt32(jObj["parentCompayID"]) : 0;
                string remarks = Uri.UnescapeDataString(jObj["remarks"].ToString());
                string companyName = Uri.UnescapeDataString(jObj["CompanyName"].ToString());
                string shortCompanyName = Uri.UnescapeDataString(jObj["ShortCompanyName"].ToString());
                string Address1 = Uri.UnescapeDataString(jObj["Address1"].ToString());
                string LogoName = Uri.UnescapeDataString(jObj["LogoName"].ToString());
                string Currency = jObj["Currency"].ToString();
                string Mode = jObj["Mode"].ToString();
                string chkActive = jObj["chkActive"].ToString();
                string type = jObj["type"].ToString();
                byte defaultGridSize = jObj["defaultGridSize"].ToString() != "" ? Convert.ToByte(jObj["defaultGridSize"]) : Convert.ToByte(0);
                decimal cushionHours = jObj["cushionHours"].ToString() != "" ? Convert.ToDecimal(jObj["cushionHours"]) : 0.00M;
                int CompanyThemeID = Convert.ToInt32(jObj["CompanyThemeID"]);
                string CompanyFont = jObj["CompanyFont"].ToString();
                decimal InventoryCarryingFactor = jObj["InventoryCarryingFactor"].ToString() != "" ? Convert.ToDecimal(jObj["InventoryCarryingFactor"]) : 0.00M;
                int OrderingCostID = Convert.ToInt32(jObj["OrderingCostID"]);

                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();
                    using (var command = new SqlCommand("Up_Upd_Am_Erp_UpdateCompanyDeatails", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.AddWithValue("@CompanyID", companyID);
                        command.Parameters.AddWithValue("@ParentCompayID", parentCompayID);
                        command.Parameters.AddWithValue("@Remarks", remarks);
                        command.Parameters.AddWithValue("@CompanyName", companyName);
                        command.Parameters.AddWithValue("@ShortCompanyName", shortCompanyName);
                        command.Parameters.AddWithValue("@Address1", Address1);
                        command.Parameters.AddWithValue("@LogoName", LogoName);
                        command.Parameters.AddWithValue("@Currency", Convert.ToInt32(Currency));
                        command.Parameters.AddWithValue("@Mode", Mode);
                        command.Parameters.AddWithValue("@ChkActive", chkActive == "checked");
                        command.Parameters.AddWithValue("@Type", type);
                        command.Parameters.AddWithValue("@DefaultGridSize", defaultGridSize);
                        command.Parameters.AddWithValue("@CushionHours", cushionHours);
                        command.Parameters.AddWithValue("@CompanyThemeID", CompanyThemeID);
                        command.Parameters.AddWithValue("@CompanyFont", CompanyFont.ToLower().Contains("select") ? (object)DBNull.Value : CompanyFont);
                        command.Parameters.AddWithValue("@InventoryCarryingFactor", InventoryCarryingFactor);
                        command.Parameters.AddWithValue("@OrderingCostID", OrderingCostID);
                        command.Parameters.AddWithValue("@UserID", userID);
                        command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                        command.ExecuteNonQuery();
                    }
                }

                //gbl.InsertGPSDetails(
                //    Convert.ToInt32(UpdateObj.Company_ID),
                //    Convert.ToInt32(UpdateObj.Branch),
                //    Convert.ToInt32(UpdateObj.User_ID),
                //    Convert.ToInt32(Common.GetObjectID("CoreCompanyMaster",constring)),
                //    companyID,
                //    0,
                //    0,
                //    "Updated",
                //    false,
                //    Convert.ToInt32(UpdateObj.MenuID)
                //);
                return new JsonResult(true);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: Insert Company header /Mithun:::
        /// <summary>
        /// to Insert the Company Header
        /// </summary> 
        public static IActionResult Insert(InsertCoreCompanyList InsertObj, string constring, int LogException)
        {
            try
            {
                //GNM_User User = (GNM_User)Session["UserDetails"];
                // GNM_User User = InsertObj.UserDetails.FirstOrDefault();
                int userID = InsertObj.User_ID;

                JObject jObj = JObject.Parse(InsertObj.data);

                string companyName = Uri.UnescapeDataString(jObj["CompanyName"].ToString());
                string shortCompanyName = Uri.UnescapeDataString(jObj["ShortCompanyName"].ToString());
                string address1 = Uri.UnescapeDataString(jObj["Address1"].ToString());
                string logoName = Uri.UnescapeDataString(jObj["LogoName"].ToString());
                int parentCompayID = jObj["parentCompayID"].ToString() != "" ? Convert.ToInt32(jObj["parentCompayID"]) : 0;
                string remarks = Uri.UnescapeDataString(jObj["remarks"].ToString());
                int currency = Convert.ToInt32(jObj["Currency"]);
                string mode = jObj["Mode"].ToString();
                bool chkActive = jObj["chkActive"].ToString() == "checked";
                string type = jObj["type"].ToString();
                byte defaultGridSize = jObj["defaultGridSize"].ToString() != "" ? Convert.ToByte(jObj["defaultGridSize"]) : Convert.ToByte(0);
                decimal inventoryCarryingFactor = jObj["InventoryCarryingFactor"].ToString() != "" ? Convert.ToDecimal(jObj["InventoryCarryingFactor"]) : Convert.ToDecimal(0.00);
                int companyThemeID = Convert.ToInt32(jObj["CompanyThemeID"]);
                string companyFont = jObj["CompanyFont"].ToString();
                decimal cushionHours = jObj["cushionHours"].ToString() != "" ? Convert.ToDecimal(jObj["cushionHours"]) : 0.00M;
                int orderingCostID = Convert.ToInt32(jObj["OrderingCostID"]);

                int companyID = 0;

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Ins_Am_Erp_InsertcompanyHeaderDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyName", companyName);
                        cmd.Parameters.AddWithValue("@ShortCompanyName", shortCompanyName);
                        cmd.Parameters.AddWithValue("@Currency", currency);
                        cmd.Parameters.AddWithValue("@Address1", address1);
                        cmd.Parameters.AddWithValue("@Type", type);
                        cmd.Parameters.AddWithValue("@LogoName", logoName);
                        cmd.Parameters.AddWithValue("@ChkActive", chkActive);
                        cmd.Parameters.AddWithValue("@Remarks", remarks);
                        cmd.Parameters.AddWithValue("@ParentCompayID", parentCompayID);
                        cmd.Parameters.AddWithValue("@DefaultGridSize", defaultGridSize);
                        cmd.Parameters.AddWithValue("@JobCardCushionHours", cushionHours);
                        cmd.Parameters.AddWithValue("@ModifiedBy", userID);
                        cmd.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                        cmd.Parameters.AddWithValue("@CompanyThemeID", companyThemeID);
                        cmd.Parameters.AddWithValue("@InventoryCarryingFactor", inventoryCarryingFactor);
                        cmd.Parameters.AddWithValue("@OrderingCostID", orderingCostID);
                        cmd.Parameters.AddWithValue("@CompanyFont", companyFont.ToLower().Contains("select") ? (object)DBNull.Value : companyFont);

                        SqlParameter outputIdParam = new SqlParameter("@CompanyID", SqlDbType.Int)
                        {
                            Direction = ParameterDirection.Output
                        };
                        cmd.Parameters.Add(outputIdParam);

                        cmd.ExecuteNonQuery();
                        companyID = (int)outputIdParam.Value;
                    }
                }

                //  gbl.InsertGPSDetails(Convert.ToInt32(InsertObj.Company_ID), Convert.ToInt32(InsertObj.Branch), Convert.ToInt32(InsertObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreCompanyMaster",constring)), companyID, 0, 0, "Inserted " + companyName, false, Convert.ToInt32(InsertObj.MenuID), Convert.ToDateTime(InsertObj.LoggedINDateTime));

                var result = new
                {
                    Company_ID = companyID,
                    Success = true
                };

                //return Json(result, JsonRequestBehavior.AllowGet);
                return new JsonResult(result);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                return new JsonResult(new { Success = false, Message = "A network error occurred. Please try again later." });
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

                return new JsonResult(new { Success = false, Message = "An error occurred while processing your request. Please try again later." });
            }
        }

        #endregion

        #region ::: SelectAllBranchForACompany /Mithun:::
        /// <summary>
        /// to select all the branches of the company
        /// </summary> 
        public static IActionResult SelectAllBranchForACompany(SelectAllBranchForACompanyList SelectAllBranchForACompanyObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {
            try
            {
                int userLanguageID = Convert.ToInt32(SelectAllBranchForACompanyObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectAllBranchForACompanyObj.GeneralLanguageID);

                // List to hold BranchData objects
                IQueryable<BranchData> branchDataList = null;

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Construct SQL command to call the appropriate SP based on languageID
                    SqlCommand command = connection.CreateCommand();
                    command.CommandType = CommandType.StoredProcedure;
                    if (SelectAllBranchForACompanyObj.languageID == generalLanguageID)
                    {
                        command.CommandText = "Up_Sel_Am_Erp_SelectAllBranchForACompanyForEnglishLanguage";
                        command.Parameters.AddWithValue("@CompanyID", SelectAllBranchForACompanyObj.companyID);
                    }
                    else
                    {
                        command.CommandText = "Up_Sel_Am_Erp_SelectAllBranchForACompanyForLocalLanguage";
                        command.Parameters.AddWithValue("@CompanyID", SelectAllBranchForACompanyObj.companyID);
                        command.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                    }

                    // Execute the SQL command and retrieve data into branchDataList
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        List<BranchData> tempList = new List<BranchData>();
                        while (reader.Read())
                        {
                            BranchData branchData = new BranchData();
                            branchData.Branch_ID = Convert.ToInt32(reader["Branch_ID"]);
                            branchData.edit = "<a title='View' href='#' id='" + branchData.Branch_ID + "' key='" + branchData.Branch_ID + "' class='editBranchMaster font-icon-class'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>";
                            branchData.delete = "<input type='checkbox' key='" + branchData.Branch_ID + "' id='chk" + branchData.Branch_ID + "' class='chkBranchHeaderDelete'/>";
                            branchData.Branch_Name = Convert.ToString(reader["Branch_Name"]);
                            branchData.Branch_Phone = Convert.ToString(reader["Branch_Phone"]);
                            branchData.Branch_Location = Convert.ToString(reader["Branch_Location"]);
                            branchData.Branch_Email = Convert.ToString(reader["Branch_Email"]);
                            branchData.Branch_HeadOffice = Convert.ToBoolean(reader["Branch_HeadOffice"]) ? "Yes" : "No";
                            branchData.Branch_External = Convert.ToBoolean(reader["Branch_External"]) ? "Yes" : "No";
                            branchData.Branch_Active = Convert.ToBoolean(reader["Branch_Active"]) ? "Yes" : "No";
                            branchData.Local = (userLanguageID != generalLanguageID) ? "<img key='" + branchData.Branch_ID + "' src='" + AppPath + "/Content/Images/local.png' class='branchLocale' alt='Localize' width='20' height='20'  title='Localize'/>" : "";


                            // Add branchData to tempList
                            tempList.Add(branchData);
                        }
                        // Assign tempList to branchDataList
                        branchDataList = tempList.AsQueryable();
                    }

                    // Check if search/filter parameters are provided

                    if (_search)
                    {
                        // First decryption to get the encrypted filters string
                        string firstDecryption = Common.DecryptString(filters);

                        // Now decrypt the decrypted result (second decryption) if necessary
                        string secondDecryption = Common.DecryptString(firstDecryption);

                        // Deserialize the second decrypted string into a Filters object
                        Filters filtersobj = JObject.Parse(secondDecryption).ToObject<Filters>();

                        // Apply the filter to the branch data list
                        branchDataList = branchDataList.FilterSearch<BranchData>(filtersobj);
                    }

                    else if (advnce)
                    {
                        AdvanceFilter advnfilter = JObject.Parse(Query).ToObject<AdvanceFilter>();
                        branchDataList = branchDataList.AdvanceSearch<BranchData>(advnfilter);
                        page = 1; // Reset page number to 1 after applying advanced search
                    }

                    // Paginate the data
                    int startIndex = (page - 1) * rows;
                    List<BranchData> paginatedData = branchDataList.Skip(startIndex).Take(rows).ToList();

                    // Prepare JSON result object
                    var result = new
                    {
                        total = 1, // Example total pages
                        page = page,
                        records = branchDataList.Count(),
                        data = paginatedData,
                        filter = filters,
                        advanceFilter = Query,
                    };

                    //return Json(result, JsonRequestBehavior.AllowGet);
                    return new JsonResult(result);
                }
            }
            catch (Exception ex)
            {
                // Log the exception or handle as needed
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: SelectLocaleBranch /Mithun:::
        /// <summary>
        /// to select all the branch locale details
        /// </summary> 
        public static IActionResult SelectLocaleBranch(SelectLocaleBranchList SelectLocaleBranchObj, string constring, int LogException)
        {
            try
            {
                int userLanguageID = Convert.ToInt32(SelectLocaleBranchObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectLocaleBranchObj.GeneralLanguageID);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_SelectBranchLocaleDetails", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Branch_ID", SelectLocaleBranchObj.id);
                        cmd.Parameters.AddWithValue("@Language_ID", userLanguageID);

                        SqlDataReader reader = cmd.ExecuteReader();
                        dynamic x = null;

                        if (reader.Read())
                        {
                            x = new
                            {
                                Branch_id = reader["Branch_id"],
                                BranchGName = reader["BranchGName"],
                                BranchGShortName = reader["BranchGShortName"],
                                BranchGAddress1 = reader["BranchGAddress1"],
                                BranchGLocation = reader["BranchGLocation"],
                                BranchLocaleID = reader["BranchLocaleID"],
                                BranchLName = reader["BranchLName"],
                                BranchLShortName = reader["BranchLShortName"],
                                BranchLAddress1 = reader["BranchLAddress1"],
                                BranchLLocation = reader["BranchLLocation"]
                            };
                        }
                        else
                        {
                            x = new
                            {
                                Branch_id = SelectLocaleBranchObj.id,
                                BranchGName = "",
                                BranchGShortName = "",
                                BranchGAddress1 = "",
                                BranchGLocation = "",
                                BranchLocaleID = "",
                                BranchLName = "",
                                BranchLShortName = "",
                                BranchLAddress1 = "",
                                BranchLLocation = ""
                            };
                        }

                        reader.Close();
                        //return Json(x, JsonRequestBehavior.AllowGet);
                        return new JsonResult(x);
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: InsertBranchHeader /Mithun:::
        /// <summary>
        /// to insert branch header
        /// </summary> 
        public static IActionResult InsertBranchHeader(InsertBranchHeaderList InsertBranchHeaderObj, string constring, int LogException)
        {
            try
            {
                string branchData = InsertBranchHeaderObj.branchData;
                JObject jObj = JObject.Parse(branchData);
                var headerColumns = new Dictionary<string, bool>
                {

                    { "branchName", true },           // Maps to Branch_Name
                    { "branchShortName", true },      // Maps to Branch_ShortName
                    { "zipCode", true },             // Maps to Branch_ZipCode (Optional field)
                    { "countryID", true },            // Maps to Country_ID
                    { "stateID", true },              // Maps to State_ID
                    { "branchActive", false },         // Maps to Branch_Active
                    { "isHeadOffice", true },         // Maps to Branch_HeadOffice
                    { "address1", true },             // Maps to Branch_Address
                    { "location", true },            // Maps to Branch_Location (Optional field)
                    { "email", true },                // Maps to Branch_Email
                    { "mobileNumber", true },        // Maps to Branch_Mobile (Optional field)
                    { "Currency_ID", true },          // Maps to Currency_ID
                    { "Region_ID", true },            // Maps to GST Code
                    //{ "IsOverTimeDWM", true }         // Maps to Status
                };
                List<string> invalidColumns;
                bool isHeaderValid = Common.ValidateAndLog(jObj, headerColumns, out invalidColumns);
                if (!isHeaderValid)
                {

                    return new JsonResult(new { Response = "Header Validation Failed", Message = "Header contains invalid or missing fields." });
                }


                string branchName = Uri.UnescapeDataString(jObj["branchName"]?.ToString() ?? string.Empty);
                string branchShortName = Uri.UnescapeDataString(jObj["branchShortName"]?.ToString() ?? string.Empty);
                //bool branchActive = bool.TryParse(jObj["branchActive"]?.ToString(), out bool tempBranchActive) ? tempBranchActive : false;
                bool branchActive = false;
                string zipCode = Uri.UnescapeDataString(jObj["zipCode"]?.ToString() ?? string.Empty);
                int countryID = int.TryParse(jObj["countryID"]?.ToString(), out int tempCountryID) ? tempCountryID : 0;
                int stateID = int.TryParse(jObj["stateID"]?.ToString(), out int tempStateID) ? tempStateID : 0;
                string phone = Uri.UnescapeDataString(jObj["phone"]?.ToString() ?? string.Empty);
                string fax = Uri.UnescapeDataString(jObj["fax"]?.ToString() ?? string.Empty);
                bool isHeadOffice = bool.TryParse(jObj["isHeadOffice"]?.ToString(), out bool tempIsHeadOffice) ? tempIsHeadOffice : false;
                int companyID = int.TryParse(jObj["companyID"]?.ToString(), out int tempCompanyID) ? tempCompanyID : 0;
                string address1 = Uri.UnescapeDataString(jObj["address1"]?.ToString() ?? string.Empty);
                string location = Uri.UnescapeDataString(jObj["location"]?.ToString() ?? string.Empty);
                string email = Uri.UnescapeDataString(jObj["email"]?.ToString() ?? string.Empty);
                bool isExternal = bool.TryParse(jObj["isExternal"]?.ToString(), out bool tempIsExternal) ? tempIsExternal : false;
                string mobileNumber = Uri.UnescapeDataString(jObj["mobileNumber"]?.ToString() ?? string.Empty);
                int timeZoneID = int.TryParse(jObj["timeZoneID"]?.ToString(), out int tempTimeZoneID) ? tempTimeZoneID : 0;
                int regionID = int.TryParse(jObj["Region_ID"]?.ToString(), out int tempRegionID) ? tempRegionID : 0;
                int currencyID = int.TryParse(jObj["Currency_ID"]?.ToString(), out int tempCurrencyID) ? tempCurrencyID : 0;
                byte isOverTimeDWM = byte.TryParse(jObj["IsOverTimeDWM"]?.ToString(), out byte tempIsOverTimeDWM) ? tempIsOverTimeDWM : (byte)0;
                int payrollFileTypeID = int.TryParse(jObj["PayrollFileType_ID"]?.ToString(), out int tempPayrollFileTypeID) ? tempPayrollFileTypeID : 0;

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_ins_Am_Erp_insertBranchHeaderDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@BranchName", branchName);
                        cmd.Parameters.AddWithValue("@BranchShortName", branchShortName);
                        cmd.Parameters.AddWithValue("@BranchActive", branchActive);
                        cmd.Parameters.AddWithValue("@ZipCode", zipCode);
                        cmd.Parameters.AddWithValue("@CountryID", countryID);
                        cmd.Parameters.AddWithValue("@StateID", stateID);
                        cmd.Parameters.AddWithValue("@Phone", phone);
                        cmd.Parameters.AddWithValue("@Fax", fax);
                        cmd.Parameters.AddWithValue("@IsHeadOffice", isHeadOffice);
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);
                        cmd.Parameters.AddWithValue("@Address1", address1);
                        cmd.Parameters.AddWithValue("@Location", location);
                        cmd.Parameters.AddWithValue("@Email", email);
                        cmd.Parameters.AddWithValue("@IsExternal", isExternal);
                        cmd.Parameters.AddWithValue("@MobileNumber", mobileNumber);
                        cmd.Parameters.AddWithValue("@TimeZoneID", timeZoneID);
                        cmd.Parameters.AddWithValue("@RegionID", regionID);
                        cmd.Parameters.AddWithValue("@CurrencyID", currencyID);
                        cmd.Parameters.AddWithValue("@IsOverTimeDWM", isOverTimeDWM);
                        cmd.Parameters.AddWithValue("@PayrollFileTypeID", payrollFileTypeID);

                        SqlParameter insertedBranchIDParam = new SqlParameter("@InsertedBranchID", SqlDbType.Int)
                        {
                            Direction = ParameterDirection.Output
                        };
                        cmd.Parameters.Add(insertedBranchIDParam);

                        cmd.ExecuteNonQuery();

                        int insertedBranchID = (int)insertedBranchIDParam.Value;

                        var result = new { Branch_ID = insertedBranchID };
                        //return Json(result, JsonRequestBehavior.AllowGet);
                        return new JsonResult(result);
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion
      

        #region ::: SelectParticularBranch /Mithun:::
        /// <summary>
        /// to select the details of particular branch for editing
        /// </summary> 
        public static IActionResult SelectParticularBranch(SelectParticularBranchList SelectParticularBranchObj, string constring, int LogException)
        {
            try
            {
                // Retrieve UserLanguageID and GeneralLanguageID from session
                int userLanguageID, generalLanguageID;
                if (SelectParticularBranchObj.UserLanguageID != null)
                    userLanguageID = Convert.ToInt32(SelectParticularBranchObj.UserLanguageID);
                else
                    throw new Exception("UserLanguageID session variable is null or invalid");

                if (SelectParticularBranchObj.GeneralLanguageID != null)
                    generalLanguageID = Convert.ToInt32(SelectParticularBranchObj.GeneralLanguageID);
                else
                    throw new Exception("GeneralLanguageID session variable is null or invalid");

                // Connection string for your database
                // string connectionString = "your_connection_string_here";

                dynamic x = null;

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Create a command object for the stored procedure
                    using (SqlCommand command = new SqlCommand("Up_Sel_Am_Erp_SelectParticularBranch", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // Add parameters required by the stored procedure
                        command.Parameters.AddWithValue("@BranchID", SelectParticularBranchObj.id);
                        command.Parameters.AddWithValue("@LanguageID", SelectParticularBranchObj.languageID);
                        command.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);
                        command.Parameters.AddWithValue("@UserLanguageID", userLanguageID);

                        // Execute the command and read the results
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                x = new
                                {
                                    Company_id = reader["Company_ID"],
                                    Branch_Name = reader["Branch_Name"] == DBNull.Value ? string.Empty : reader["Branch_Name"],
                                    Branch_ShortName = reader["Branch_ShortName"] == DBNull.Value ? string.Empty : reader["Branch_ShortName"],
                                    Branch_ZipCode = reader["Branch_ZipCode"] == DBNull.Value ? string.Empty : reader["Branch_ZipCode"],
                                    Country_id = reader["Country_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Country_ID"]),
                                    State_id = reader["State_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["State_ID"]),
                                    Branch_Phone = reader["Branch_Phone"] == DBNull.Value ? string.Empty : reader["Branch_Phone"],
                                    Branch_Fax = reader["Branch_Fax"] == DBNull.Value ? string.Empty : reader["Branch_Fax"],
                                    Branch_HeadOffice = reader["Branch_HeadOffice"] == DBNull.Value ? false : Convert.ToBoolean(reader["Branch_HeadOffice"]),
                                    Branch_Active = reader["Branch_Active"] == DBNull.Value ? false : Convert.ToBoolean(reader["Branch_Active"]),
                                    Branch_Address1 = reader["Branch_Address1"] == DBNull.Value ? string.Empty : reader["Branch_Address1"],
                                    Branch_Location = reader["Branch_Location"] == DBNull.Value ? string.Empty : reader["Branch_Location"],
                                    Branch_Email = reader["Branch_Email"] == DBNull.Value ? string.Empty : reader["Branch_Email"],
                                    BranchID = reader["Branch_ID"],
                                    Branch_External = reader["Branch_External"] == DBNull.Value ? false : Convert.ToBoolean(reader["Branch_External"]),
                                    Branch_Mobile = reader["Branch_Mobile"] == DBNull.Value ? string.Empty : reader["Branch_Mobile"],
                                    //RefMasterDetail_ID = reader["RefMasterDetail_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                    Region_ID = reader["Region_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Region_ID"]),
                                    Region_Name = reader["Region_Name"] == DBNull.Value ? string.Empty : reader["Region_Name"],
                                    Currency_ID = reader["Currency_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Currency_ID"]),
                                    IsOverTimeDWM = reader["IsOverTimeDWM"] == DBNull.Value ? false : Convert.ToBoolean(reader["IsOverTimeDWM"]),
                                    PayrollFileType_ID = reader["PayrollFileType_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["PayrollFileType_ID"])
                                };
                            }
                        }
                    }
                }

                //return Json(x, JsonRequestBehavior.AllowGet);
                return new JsonResult(x);
            }
            catch (Exception ex)
            {
                // Handle exceptions appropriately, log if needed
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: UpdateBranchHeader /Mithun:::
        /// <summary>
        /// to update the branch header
        /// </summary> 
        public static IActionResult UpdateBranchHeader(UpdateBranchHeaderList UpdateBranchHeaderObj, string constring, int LogException)
        {
            try
            {
                JObject jObj = JObject.Parse(UpdateBranchHeaderObj.branchData);

                int branchPK = Convert.ToInt32(jObj["branchID"].ToString());
                string branchName = Uri.UnescapeDataString(jObj["branchName"].ToString());
                string branchShortName = Uri.UnescapeDataString(jObj["branchShortName"].ToString());
                bool branchActive = jObj["branchActive"].ToString() == "checked";
                string zipCode = Uri.UnescapeDataString(jObj["zipCode"].ToString());
                int countryID = Convert.ToInt32(jObj["countryID"].ToString());
                int stateID = Convert.ToInt32(jObj["stateID"].ToString());
                string phone = Uri.UnescapeDataString(jObj["phone"].ToString());
                string fax = Uri.UnescapeDataString(jObj["fax"].ToString());
                bool isHeadOffice = jObj["isHeadOffice"].ToString() == "checked";
                int companyID = Convert.ToInt32(jObj["companyID"].ToString());
                string address1 = Uri.UnescapeDataString(jObj["address1"].ToString());
                string location = Uri.UnescapeDataString(jObj["location"].ToString());
                string email = Uri.UnescapeDataString(jObj["email"].ToString());
                bool isExternal = jObj["isExternal"].ToString() == "checked";
                string mobileNumber = Uri.UnescapeDataString(jObj["mobileNumber"].ToString());
                int timeZoneID = Convert.ToInt32(jObj["timeZoneID"].ToString());
                int? regionID = string.IsNullOrEmpty(jObj["Region_ID"].ToString()) ? (int?)null : Convert.ToInt32(jObj["Region_ID"].ToString());
                int? currencyID = string.IsNullOrEmpty(jObj["Currency_ID"].ToString()) ? (int?)null : Convert.ToInt32(jObj["Currency_ID"].ToString());
                byte? isOverTimeDWM = string.IsNullOrEmpty(jObj["IsOverTimeDWM"].ToString()) ? (byte?)null : Convert.ToByte(jObj["IsOverTimeDWM"].ToString());
                int? payrollFileTypeID = string.IsNullOrEmpty(jObj["PayrollFileType_ID"].ToString()) ? (int?)null : Convert.ToInt32(jObj["PayrollFileType_ID"].ToString());

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    using (SqlCommand cmd = new SqlCommand("Up_Upd_Am_Erp_UpdateBranchHeader", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@BranchID", branchPK);
                        cmd.Parameters.AddWithValue("@BranchName", branchName);
                        cmd.Parameters.AddWithValue("@BranchShortName", branchShortName);
                        cmd.Parameters.AddWithValue("@BranchActive", branchActive);
                        cmd.Parameters.AddWithValue("@ZipCode", zipCode);
                        cmd.Parameters.AddWithValue("@CountryID", countryID);
                        cmd.Parameters.AddWithValue("@StateID", stateID);
                        cmd.Parameters.AddWithValue("@Phone", phone);
                        cmd.Parameters.AddWithValue("@Fax", fax);
                        cmd.Parameters.AddWithValue("@IsHeadOffice", isHeadOffice);
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);
                        cmd.Parameters.AddWithValue("@Address1", address1);
                        cmd.Parameters.AddWithValue("@Location", location);
                        cmd.Parameters.AddWithValue("@Email", email);
                        cmd.Parameters.AddWithValue("@IsExternal", isExternal);
                        cmd.Parameters.AddWithValue("@MobileNumber", mobileNumber);
                        cmd.Parameters.AddWithValue("@TimeZoneID", timeZoneID);
                        cmd.Parameters.AddWithValue("@Region_ID", (object)regionID ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Currency_ID", (object)currencyID ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@IsOverTimeDWM", (object)isOverTimeDWM ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@PayrollFileType_ID", (object)payrollFileTypeID ?? DBNull.Value);

                        conn.Open();
                        cmd.ExecuteNonQuery();

                        //  gbl.InsertGPSDetails(Convert.ToInt32(UpdateBranchHeaderObj.Company_ID), Convert.ToInt32(UpdateBranchHeaderObj.Branch), Convert.ToInt32(UpdateBranchHeaderObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreCompanyMaster",constring)), companyID, 0, 0, "Updated " + branchName, false, Convert.ToInt32(UpdateBranchHeaderObj.MenuID), Convert.ToDateTime(UpdateBranchHeaderObj.LoggedINDateTime));
                    }
                    return new JsonResult(true);
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: SelectBranchTaxDetails /Mithun:::
        /// <summary>
        /// to select all the branch tax details
        /// </summary> 
        public static IActionResult SelectBranchTaxDetails(SelectBranchTaxDetailsList SelectBranchTaxDetailsObj, string constring, int LogException, string sidx, string sord, int page, int rows)
        {
            try
            {
                //GNM_User User = (GNM_User)Session["UserDetails"];
                //  GNM_User User = SelectBranchTaxDetailsObj.UserDetails.FirstOrDefault();
                int companyID = SelectBranchTaxDetailsObj.Company_ID;
                int userLanguageID = Convert.ToInt32(SelectBranchTaxDetailsObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectBranchTaxDetailsObj.GeneralLanguageID);
                int count = 0;
                int total = 0;
                List<dynamic> branchList = new List<dynamic>();
                dynamic s = null;
                var x = s;

                // Fetch branch tax details
                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();

                    using (var command = new SqlCommand("sp_SelectBranchTaxDetails", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@BranchID", SelectBranchTaxDetailsObj.branchID);
                        command.Parameters.AddWithValue("@Sidx", sidx);
                        command.Parameters.AddWithValue("@Sord", sord);
                        command.Parameters.AddWithValue("@Page", page);
                        command.Parameters.AddWithValue("@Rows", rows);
                        command.Parameters.AddWithValue("@LanguageID", SelectBranchTaxDetailsObj.languageID);
                        command.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                        command.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                branchList.Add(new
                                {
                                    BranchTaxStructure_ID = reader["BranchTaxStructure_ID"],
                                    TaxStructure_ID = reader["TaxStructure_ID"]
                                });
                            }
                        }
                    }

                    // Fetch tax array details
                    List<dynamic> taxArray = new List<dynamic>();
                    using (var command = new SqlCommand("sp_SelectTaxArray", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@CurrentCompanyID", companyID);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                taxArray.Add(new
                                {
                                    Tax_Structure_Header_id = reader["Tax_Structure_Header_id"],
                                    TaxStructure_Name = reader["TaxStructure_Name"]
                                });
                            }
                        }
                    }

                    string branchTaxNames = "-1:--Select--;";
                    foreach (var taxObj in taxArray)
                    {
                        branchTaxNames += taxObj.Tax_Structure_Header_id + ":" + taxObj.TaxStructure_Name.Replace(";", ":") + ";";
                    }
                    branchTaxNames = branchTaxNames.TrimEnd(';');

                    if (SelectBranchTaxDetailsObj.languageID == generalLanguageID)
                    {
                        var branchTaxDetailsList = new List<dynamic>();
                        using (var command = new SqlCommand("sp_SelectBranchTaxDetailsByLanguage", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@BranchID", SelectBranchTaxDetailsObj.branchID);
                            command.Parameters.AddWithValue("@Sidx", sidx);
                            command.Parameters.AddWithValue("@Sord", sord);
                            command.Parameters.AddWithValue("@Page", page);
                            command.Parameters.AddWithValue("@Rows", rows);
                            command.Parameters.AddWithValue("@LanguageID", SelectBranchTaxDetailsObj.languageID);
                            command.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                            command.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                            using (var reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    branchTaxDetailsList.Add(new
                                    {
                                        BranchTaxStructure_ID = reader["BranchTaxStructure_ID"],
                                        edit = $"<a title='Edit' href='#' key='{reader["BranchTaxStructure_ID"]}' id='{reader["BranchTaxStructure_ID"]}' class='editBranchMasterTax font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                        delete = $"<input type='checkbox' key='{reader["BranchTaxStructure_ID"]}' id='chkBranchTax{reader["BranchTaxStructure_ID"]}' class='chkBranchTaxDelete'/>",
                                        Tax_Structure = reader["TaxStructure_Name"] != DBNull.Value ? reader["TaxStructure_Name"].ToString() : string.Empty
                                    });
                                }

                            }
                        }

                        List<dynamic> arr = new List<dynamic>();
                        for (int i = 0; i < branchTaxDetailsList.Count; i++)
                        {
                            if ((i >= (page * rows) - rows) && (i < (page * rows) + rows))
                            {
                                arr.Add(branchTaxDetailsList[i]);
                            }
                        }

                        x = new
                        {
                            total = total,
                            page = page,
                            records = count,
                            data = arr.ToArray(),
                            branchTaxNames
                        };
                    }
                    else
                    {
                        var branchLocaleTaxDetailsList = new List<dynamic>();
                        using (var command = new SqlCommand("sp_SelectBranchTaxDetailsByLanguage", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@BranchID", SelectBranchTaxDetailsObj.branchID);
                            command.Parameters.AddWithValue("@Sidx", sidx);
                            command.Parameters.AddWithValue("@Sord", sord);
                            command.Parameters.AddWithValue("@Page", page);
                            command.Parameters.AddWithValue("@Rows", rows);
                            command.Parameters.AddWithValue("@LanguageID", userLanguageID);
                            command.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                            command.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                            using (var reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    branchLocaleTaxDetailsList.Add(new
                                    {
                                        BranchTax_id = reader["BranchTaxStructure_ID"],
                                        Tax_Structure = reader["TaxStructure_Name"]
                                    });
                                }
                            }
                        }

                        List<dynamic> arr = new List<dynamic>();
                        for (int i = 0; i < branchLocaleTaxDetailsList.Count; i++)
                        {
                            if ((i >= (page * rows) - rows) && (i < (page * rows) + rows))
                            {
                                arr.Add(branchLocaleTaxDetailsList[i]);
                            }
                        }

                        x = new
                        {
                            total = total,
                            page = page,
                            records = branchLocaleTaxDetailsList.Count,
                            data = arr.ToArray()
                        };
                    }
                }

                //return Json(x, JsonRequestBehavior.AllowGet);
                return new JsonResult(x);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: SaveBranchTaxDetail /Mithun:::
        /// <summary>
        /// to update branch tax 
        /// </summary> 
        public static IActionResult SaveBranchTaxDetail(SaveBranchTaxDetailList SaveBranchTaxDetailObj, string constring, int LogException)
        {
            int CompanyID = Convert.ToInt32(SaveBranchTaxDetailObj.Company_ID);
            try
            {
                // Parse JSON to XML (for XML parameter)
                JObject jObj = JObject.Parse(SaveBranchTaxDetailObj.branchTaxData);
                XDocument xmlDoc = JsonConvert.DeserializeXNode(jObj.ToString(), "branchTaxData");

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Create SqlCommand for stored procedure
                    SqlCommand cmd = new SqlCommand("Up_Ins_Udp_Am_Erp_InsOrUpdBranchTaxStructure", conn);
                    cmd.CommandType = CommandType.StoredProcedure;

                    // Add XML parameter
                    SqlParameter param = cmd.Parameters.AddWithValue("@BranchTaxData", xmlDoc.ToString());
                    param.SqlDbType = SqlDbType.Xml;

                    // Execute the stored procedure
                    cmd.ExecuteNonQuery();
                }
                //   gbl.InsertGPSDetails(Convert.ToInt32(SaveBranchTaxDetailObj.Company_ID), Convert.ToInt32(SaveBranchTaxDetailObj.Branch), Convert.ToInt32(SaveBranchTaxDetailObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreCompanyMaster",constring)), CompanyID, 0, 0, "Update", false, Convert.ToInt32(SaveBranchTaxDetailObj.MenuID));
                // Optionally, handle success or redirection
                //RedirectToAction("Success");
                return new JsonResult(new { Success = "Success" }) { StatusCode = 200 };
            }
            catch (Exception ex)
            {
                // Handle exceptions
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        //doubt
        #region ::: SelectAllCompanyRelationships /Mithun:::
        /// <summary>
        /// to select all Relationships of company with others 
        /// </summary> 
        public static IActionResult SelectAllCompanyRelationships(SelectAllCompanyRelationshipsList SelectAllCompanyRelationshipsObj, string constring, int LogException, string sidx, string sord, int page, int rows)
        {
            try
            {
                int count = 0;
                int total = 0;
                List<GNM_Company_Company_Relation> companyList = new List<GNM_Company_Company_Relation>();
                //AMMSCore.Utilities.CommonFunctionalitiesController CFC = new CommonFunctionalitiesController();
                int userLanguageID = Convert.ToInt32(SelectAllCompanyRelationshipsObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectAllCompanyRelationshipsObj.GeneralLanguageID);
                string companyType = string.Empty;

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    // Get Company_Type
                    string companyTypeQuery = "SELECT Company_Type FROM GNM_Company WHERE Company_ID = @CompanyID";
                    using (SqlCommand cmd = new SqlCommand(companyTypeQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@CompanyID", SelectAllCompanyRelationshipsObj.companyID);
                        companyType = (string)cmd.ExecuteScalar();
                    }

                    // Query to get company relationships based on companyType
                    string companyRelationQuery = string.Empty;
                    if (companyType == "M")
                    {
                        companyRelationQuery = "SELECT * FROM GNM_Company_Company_Relation WHERE ManufacturerCompany_ID = @CompanyID ORDER BY " + (sidx == "Dealer_name" ? "ManufacturerCompany_ID" : sidx) + " " + sord;
                    }
                    else if (companyType == "D")
                    {
                        companyRelationQuery = "SELECT * FROM GNM_Company_Company_Relation WHERE DealerCompany_ID = @CompanyID ORDER BY " + (sidx == "Dealer_name" ? "DealerCompany_ID" : sidx) + " " + sord;
                    }

                    if (!string.IsNullOrEmpty(companyRelationQuery))
                    {
                        using (SqlCommand cmd = new SqlCommand(companyRelationQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@CompanyID", SelectAllCompanyRelationshipsObj.companyID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    companyList.Add(new GNM_Company_Company_Relation
                                    {
                                        Company_Relationship_ID = reader.GetInt32(reader.GetOrdinal("Company_Relationship_ID")),
                                        ManufacturerCompany_ID = reader.GetInt32(reader.GetOrdinal("ManufacturerCompany_ID")),
                                        DealerCompany_ID = reader.GetInt32(reader.GetOrdinal("DealerCompany_ID"))
                                        // Add other properties as needed, using reader.Get<Type>(reader.GetOrdinal("Column_Name"))
                                    });
                                }
                            }
                        }

                        count = companyList.Count();
                        total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                        dynamic s = null;
                        var x = s;

                        IEnumerable<GNM_Company> gnmCompany = CommonFunctionalities.LoadCompany(constring, LogException, companyType);

                        string companyDealerNames = "-1:--Select--;";
                        foreach (var company in gnmCompany)
                        {
                            companyDealerNames += company.Company_ID + ":" + company.Company_Name.Replace(";", ":") + ";";
                        }
                        companyDealerNames = companyDealerNames.TrimEnd(';');

                        if (SelectAllCompanyRelationshipsObj.languageID == generalLanguageID)
                        {
                            var arrCompany = from a in companyList
                                             join b in gnmCompany on a.DealerCompany_ID equals b.Company_ID
                                             select new
                                             {
                                                 edit = "<a title='Edit' href='#' key='" + a.Company_Relationship_ID + "' id='" + a.Company_Relationship_ID + "' class='editComRel font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                                 delete = "<input type='checkbox' key='" + a.Company_Relationship_ID + "' id='chk" + a.Company_Relationship_ID + "' class='chkCompanyRelationDelete'/>",
                                                 Company_Relation_ID = a.Company_Relationship_ID,
                                                 Dealer_name = (b.Company_Name)
                                             };
                            var arr = arrCompany.Skip((page - 1) * rows).Take(rows).ToList();
                            x = new
                            {
                                total = total,
                                page = page,
                                records = count,
                                data = arr.ToArray(),
                                companyDealerNames
                            };
                        }
                        else
                        {
                            var arrLocaleCompany = new List<dynamic>();
                            string localeQuery = @"
                        SELECT a.Company_Relationship_ID, c.Company_Name
                        FROM GNM_Company_Company_Relation a
                        JOIN GNM_Company b ON a.DealerCompany_ID = b.Company_ID
                        JOIN GNM_CompanyLocale c ON a.DealerCompany_ID = c.Company_ID
                        WHERE c.Language_ID = @UserLanguageID AND a.ManufacturerCompany_ID = @CompanyID";

                            using (SqlCommand cmd = new SqlCommand(localeQuery, conn))
                            {
                                cmd.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                                cmd.Parameters.AddWithValue("@CompanyID", SelectAllCompanyRelationshipsObj.companyID);
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        arrLocaleCompany.Add(new
                                        {
                                            Company_Relation_ID = reader.GetInt32(reader.GetOrdinal("Company_Relationship_ID")),
                                            Dealer_name = reader.GetString(reader.GetOrdinal("Company_Name"))
                                        });
                                    }
                                }
                            }

                            var arr = arrLocaleCompany.Skip((page - 1) * rows).Take(rows).ToList();
                            x = new
                            {
                                total = total,
                                page = page,
                                records = arrLocaleCompany.Count(),
                                data = arr.ToArray()
                            };
                        }
                        //return Json(x, JsonRequestBehavior.AllowGet);
                        return new JsonResult(x);
                    }
                }
                return new JsonResult(new { data = new List<object>() });
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: SaveRelation /Mithun:::
        /// <summary>
        /// to update company relation details
        /// </summary> 
        public static IActionResult SaveRelation(SaveRelationList SaveRelationObj, string constring, int LogException)
        {
            try
            {
                JTokenReader jsonReader = null;
                JObject jObj = JObject.Parse(SaveRelationObj.relationData);

                jsonReader = new JTokenReader(jObj["currentCompanyID"]);
                jsonReader.Read();
                string currentCompanyID = jsonReader.Value.ToString();
                int currentCompnyPK = Convert.ToInt32(currentCompanyID);

                string companyType = string.Empty;

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Get company type
                    string companyTypeQuery = "SELECT Company_Type FROM GNM_Company WHERE Company_ID = @CompanyID";
                    using (SqlCommand cmd = new SqlCommand(companyTypeQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@CompanyID", currentCompnyPK);
                        companyType = (string)cmd.ExecuteScalar();
                    }

                    int rowcount = jObj["rows"].Count();
                    for (int i = 0; i < rowcount; i++)
                    {
                        jsonReader = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["selectedCompanyID"]);
                        jsonReader.Read();
                        string selectedCompanyID = jsonReader.Value.ToString();
                        int selectedCompanyPK = Convert.ToInt32(selectedCompanyID);

                        jsonReader = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["RelationID"]);
                        jsonReader.Read();
                        string relationID = jsonReader.Value.ToString();

                        if (!string.IsNullOrEmpty(relationID))
                        {
                            int relationPK = Convert.ToInt32(relationID);
                            // Update existing relationship
                            string updateQuery = "UPDATE GNM_Company_Company_Relation SET " +
                                                 (companyType == "M" ? "DealerCompany_ID = @SelectedCompanyID" : "ManufacturerCompany_ID = @SelectedCompanyID") +
                                                 ", " +
                                                 (companyType == "M" ? "ManufacturerCompany_ID = @CurrentCompanyID" : "DealerCompany_ID = @CurrentCompanyID") +
                                                 " WHERE Company_Relationship_ID = @RelationID";

                            using (SqlCommand cmd = new SqlCommand(updateQuery, conn))
                            {
                                cmd.Parameters.AddWithValue("@SelectedCompanyID", selectedCompanyPK);
                                cmd.Parameters.AddWithValue("@CurrentCompanyID", currentCompnyPK);
                                cmd.Parameters.AddWithValue("@RelationID", relationPK);
                                cmd.ExecuteNonQuery();
                            }
                        }
                        else
                        {
                            // Insert new relationship
                            string insertQuery = "INSERT INTO GNM_Company_Company_Relation (" +
                                                 (companyType == "M" ? "DealerCompany_ID, ManufacturerCompany_ID" : "ManufacturerCompany_ID, DealerCompany_ID") +
                                                 ") VALUES (@SelectedCompanyID, @CurrentCompanyID)";

                            using (SqlCommand cmd = new SqlCommand(insertQuery, conn))
                            {
                                cmd.Parameters.AddWithValue("@SelectedCompanyID", selectedCompanyPK);
                                cmd.Parameters.AddWithValue("@CurrentCompanyID", currentCompnyPK);
                                cmd.ExecuteNonQuery();
                            }
                        }
                    }
                }

                return new JsonResult(new { Success = "Relations saved successfully" });
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: SelectAllBrandsForaCompany /Mithun:::
        /// <summary>
        /// to select all the brands associated with the company
        /// </summary> 
        public static IActionResult SelectAllBrandsForaCompany(SelectAllBrandsForaCompanyList SelectAllBrandsForaCompanyObj, string constring, int LogException, string sidx, string sord, int page, int rows)
        {
            try
            {
                int userLanguageID = Convert.ToInt32(SelectAllBrandsForaCompanyObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectAllBrandsForaCompanyObj.GeneralLanguageID);

                //  string connectionString = ConfigurationManager.ConnectionStrings["YourConnectionString"].ConnectionString;
                List<dynamic> arr = new List<dynamic>();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Fetch the list of brands for the company
                    SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetCompanyBrandsForDropDown", connection);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@CompanyID", SelectAllBrandsForaCompanyObj.companyID);
                    cmd.Parameters.AddWithValue("@sidx", sidx);
                    cmd.Parameters.AddWithValue("@sord", sord);

                    List<GNM_CompanyBrands> branchList = new List<GNM_CompanyBrands>();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            branchList.Add(new GNM_CompanyBrands
                            {
                                Company_Brand_ID = reader.GetInt32(reader.GetOrdinal("Company_Brand_ID")),
                                Brand_ID = reader.GetInt32(reader.GetOrdinal("Brand_ID")),
                                Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                // Other properties as needed
                            });
                        }
                    }

                    int count = branchList.Count;
                    int total = rows > 0 ? Convert.ToInt32(Math.Ceiling((double)count / rows)) : 0;

                    // Fetch brand names
                    cmd = new SqlCommand("Up_Sel_Am_Erp_SelectBrands", connection);
                    cmd.CommandType = CommandType.StoredProcedure;

                    List<dynamic> brandArray = new List<dynamic>();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            brandArray.Add(new
                            {
                                RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                RefMasterDetail_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"))
                            });
                        }
                    }

                    string companyBrandNames = "-1:--Select--;";
                    foreach (var brandObj in brandArray)
                    {
                        companyBrandNames += brandObj.RefMasterDetail_ID + ":" + brandObj.RefMasterDetail_Name.Replace(";", ":") + ";";
                    }
                    companyBrandNames = companyBrandNames.TrimEnd(';');

                    if (SelectAllBrandsForaCompanyObj.languageID == generalLanguageID)
                    {
                        // Fetch brands without localization
                        var arrBranchList = from a in branchList
                                            join b in brandArray on a.Brand_ID equals b.RefMasterDetail_ID
                                            select new
                                            {
                                                edit = "<a title='Edit' href='#' key='" + a.Company_Brand_ID + "' id='" + a.Company_Brand_ID + "' class='editComBrand font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                                delete = "<input type='checkbox' key='" + a.Company_Brand_ID + "' id='chk" + a.Company_Brand_ID + "' class='chkCompanyBrandsDelete'/>",
                                                Company_Brand_id = a.Company_Brand_ID,
                                                Brand_name = (b.RefMasterDetail_Name)
                                            };
                        for (int i = 0; i < arrBranchList.ToList().Count; i++)
                        {
                            if ((i >= (page * rows) - rows) && (i < (page * rows) + rows))
                            {
                                arr.Add(arrBranchList.ToList()[i]);
                            }
                        }
                    }
                    else
                    {
                        // Fetch localized brand names
                        cmd = new SqlCommand("Up_Sel_Am_Erp_SelectLocalBrands", connection);
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", SelectAllBrandsForaCompanyObj.companyID);
                        cmd.Parameters.AddWithValue("@LanguageID", userLanguageID);

                        List<dynamic> arrLocaleBranchList = new List<dynamic>();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                arrLocaleBranchList.Add(new
                                {
                                    Company_Brand_id = reader.GetInt32(reader.GetOrdinal("Company_Brand_ID")),
                                    Brand_name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"))
                                });
                            }
                        }

                        for (int i = 0; i < arrLocaleBranchList.ToList().Count; i++)
                        {
                            if ((i >= (page * rows) - rows) && (i < (page * rows) + rows))
                            {
                                arr.Add(arrLocaleBranchList.ToList()[i]);
                            }
                        }
                    }

                    var x = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        data = arr.ToArray(),
                        companyBrandNames
                    };

                    //return Json(x, JsonRequestBehavior.AllowGet);
                    return new JsonResult(x);
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
                //return RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }
        #endregion

        #region ::: SaveBrandDetails /Mithun:::
        /// <summary>
        /// to update brand association with the company
        /// </summary> 
        public static IActionResult SaveBrandDetails(SaveBrandDetailsList SaveBrandDetailsObj, string constring, int LogException)
        {
            try
            {

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    JObject jobj = JObject.Parse(SaveBrandDetailsObj.brandData);
                    string CompanyID = jobj["CompanyID"].ToString();

                    // Construct XML parameter
                    string brandDetailsXML = "<BrandDetails>";
                    int rowCount = jobj["rows"].Count();
                    for (int i = 0; i < rowCount; i++)
                    {
                        string BrandID = jobj["rows"][i]["BrandID"].ToString();
                        string CompanyBrandID = jobj["rows"][i]["CompanyBrandID"].ToString();

                        brandDetailsXML += "<Row>";
                        brandDetailsXML += "<BrandID>" + BrandID + "</BrandID>";
                        if (!string.IsNullOrEmpty(CompanyBrandID))
                            brandDetailsXML += "<CompanyBrandID>" + CompanyBrandID + "</CompanyBrandID>";
                        brandDetailsXML += "</Row>";
                    }
                    brandDetailsXML += "</BrandDetails>";

                    // Execute the stored procedure
                    SqlCommand cmd = new SqlCommand("Up_ins_Upd_Am_Erp_SaveBradDetails", connection);
                    cmd.CommandType = CommandType.StoredProcedure;

                    SqlParameter parameterCompanyID = new SqlParameter("@CompanyID", SqlDbType.Int);
                    parameterCompanyID.Value = Convert.ToInt32(CompanyID);
                    cmd.Parameters.Add(parameterCompanyID);

                    SqlParameter parameterBrandDetailsXML = new SqlParameter("@BrandDetailsXML", SqlDbType.Xml);
                    parameterBrandDetailsXML.Value = new SqlXml(new XmlTextReader(new StringReader(brandDetailsXML)));
                    cmd.Parameters.Add(parameterBrandDetailsXML);

                    cmd.ExecuteNonQuery();
                }

                // Log success or other actions if needed
                return new JsonResult(new { Success = "Saved successfully" });
            }
            catch (WebException wex)
            {
                // Handle WebException
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                // Handle other exceptions
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }


        #endregion

        #region ::: SelectAllEmployeeDetails /Mithun:::
        /// <summary>
        /// to select all the employees of the company
        /// </summary> 
        public static IActionResult SelectAllEmployeeDetails(SelectAllEmployeeDetailsList SelectAllEmployeeDetailsObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {
            try
            {
                int userLanguageID = Convert.ToInt32(SelectAllEmployeeDetailsObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectAllEmployeeDetailsObj.GeneralLanguageID);

                List<EmployeeData> employeeList = new List<EmployeeData>();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    SqlCommand command = connection.CreateCommand();
                    command.CommandType = CommandType.StoredProcedure;

                    if (SelectAllEmployeeDetailsObj.langaugeID == generalLanguageID)
                    {
                        command.CommandText = "Up_Sel_Am_Erp_SelectAllEmployeeForEnglishLanguage";
                        command.Parameters.AddWithValue("@CompanyID", SelectAllEmployeeDetailsObj.companyID);
                    }
                    else
                    {
                        command.CommandText = "Up_Sel_Am_Erp_SelectAllEmployeeForLocalLanguage";
                        command.Parameters.AddWithValue("@CompanyID", SelectAllEmployeeDetailsObj.companyID);
                        command.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                    }

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            EmployeeData employeeData = new EmployeeData();
                            employeeData.Company_Employee_ID = Convert.ToInt32(reader["Company_Employee_ID"]);
                            employeeData.edit = "<a title='View' href='#' id='" + employeeData.Company_Employee_ID + "' key='" + employeeData.Company_Employee_ID + "' class='editEmployee font-icon-class'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>";
                            employeeData.delete = "<input type='checkbox' key='" + employeeData.Company_Employee_ID + "' id='chk" + employeeData.Company_Employee_ID + "' class='chkCompanyEmployeeDelete'/>";
                            employeeData.EmployeeCode = reader["Employee_ID"].ToString();
                            employeeData.Company_Employee_Name = reader["Company_Employee_Name"].ToString();
                            employeeData.Company_Employee_MobileNumber = reader["Company_Employee_MobileNumber"].ToString();
                            employeeData.Company_Employee_Email = reader["Company_Employee_Email"].ToString();
                            employeeData.Department = reader["Department"].ToString();
                            employeeData.Designation = reader["Designation"].ToString();
                            employeeData.view = "<img id='" + employeeData.Company_Employee_ID + "' src='" + AppPath + "/Content/Images/Plus.gif' key='" + employeeData.Company_Employee_ID + "' class='viewEmployee'/>";
                            employeeData.Local = (userLanguageID != generalLanguageID) ? "<img key='" + employeeData.Company_Employee_ID + "' src='" + AppPath + "/Content/Images/local.png' class='employeeLocale' alt='Localize' width='20' height='20'  title='Localize'/>" : "";
                            employeeList.Add(employeeData);
                        }
                    }
                }

                // Convert the list to IQueryable for filtering and sorting
                IQueryable<EmployeeData> EmpList = employeeList.AsQueryable();

                if (_search)
                {
                    string firstDecryption = Common.DecryptString(filters);

                    // Now decrypt the decrypted result (second decryption) if necessary
                    string secondDecryption = Common.DecryptString(firstDecryption);

                    // Deserialize the second decrypted string into a Filters object
                    Filters filtersobj = JObject.Parse(secondDecryption).ToObject<Filters>();
                    EmpList = EmpList.FilterSearch(filtersobj);
                }
                else if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse(Query).ToObject<AdvanceFilter>();
                    EmpList = EmpList.AdvanceSearch(advnfilter);
                    page = 1; // Reset page number to 1 after applying advanced search
                }

                // Apply sorting
                EmpList = EmpList.OrderByField(sidx, sord);

                // Calculate total pages and records
                int count = EmpList.Count();
                int total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                // Paginate the result
                var paginatedData = EmpList.Skip((page - 1) * rows).Take(rows).ToList();

                var result = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = paginatedData,
                    filter = filters,
                    advanceFilter = Query,
                };

                //return Json(result, JsonRequestBehavior.AllowGet);
                return new JsonResult(result);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }
        #endregion

        #region ::: SelEmployeelocale /Mithun:::
        /// <summary>
        /// to select the employees locale detail of the company
        /// </summary> 
        public static IActionResult SelEmployeelocale(SelEmployeelocaleList SelEmployeelocaleObj, string constring, int LogException)
        {
            try
            {
                int userLanguageID = Convert.ToInt32(SelEmployeelocaleObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelEmployeelocaleObj.GeneralLanguageID);

                // Define output variables for the stored procedure
                int EmpLID = -1; // Default value
                string EmpLName = "";
                string EmpLAddress1 = "";
                string EmpLLocation = "";

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_SelectEmployeeLocaleDetails", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Input parameters
                        cmd.Parameters.AddWithValue("@EmpID", SelEmployeelocaleObj.id);
                        cmd.Parameters.AddWithValue("@Language_ID", userLanguageID);

                        // Output parameters
                        cmd.Parameters.Add("@EmpLID", SqlDbType.Int).Direction = ParameterDirection.Output;
                        cmd.Parameters.Add("@EmpLName", SqlDbType.NVarChar, -1).Direction = ParameterDirection.Output;
                        cmd.Parameters.Add("@EmpLAddress1", SqlDbType.NVarChar, -1).Direction = ParameterDirection.Output;
                        cmd.Parameters.Add("@EmpLLocation", SqlDbType.NVarChar, -1).Direction = ParameterDirection.Output;

                        cmd.ExecuteNonQuery();

                        // Retrieve output parameter values
                        EmpLID = Convert.ToInt32(cmd.Parameters["@EmpLID"].Value);
                        EmpLName = cmd.Parameters["@EmpLName"].Value.ToString();
                        EmpLAddress1 = cmd.Parameters["@EmpLAddress1"].Value.ToString();
                        EmpLLocation = cmd.Parameters["@EmpLLocation"].Value.ToString();
                    }
                }

                // Prepare JSON response based on retrieved values
                var x = new
                {
                    EmpID = SelEmployeelocaleObj.id,
                    EmpGName = "",
                    EmpGAddress1 = "",
                    EmpGLocation = "",

                    EmpLID = EmpLID == -1 ? "" : EmpLID.ToString(),
                    EmpLName = EmpLName,
                    EmpLAddress1 = EmpLAddress1,
                    EmpLLocation = EmpLLocation
                };

                //return Json(x, JsonRequestBehavior.AllowGet);
                return new JsonResult(x);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: SelectSpecificEmployeeDetails /Mithun:::
        /// <summary>
        /// to select the specific employee detail of the company for editing
        /// </summary> 
        public static IActionResult SelectSpecificEmployeeDetails(SelectSpecificEmployeeDetailsList SelectSpecificEmployeeDetailsObj, string constring, int LogException)
        {
            try
            {
                int userLanguageID = Convert.ToInt32(SelectSpecificEmployeeDetailsObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectSpecificEmployeeDetailsObj.GeneralLanguageID);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    SqlCommand cmd;

                    if (SelectSpecificEmployeeDetailsObj.languageID == generalLanguageID)
                    {
                        cmd = new SqlCommand("Up_Sel_Am_Erp_GetEmployeeDetailsGeneralLanguage", connection);
                        cmd.Parameters.AddWithValue("@id", SelectSpecificEmployeeDetailsObj.id);
                    }
                    else
                    {
                        cmd = new SqlCommand("Up_Sel_Am_Erp_GetEmployeeDetailsLocalLanguage", connection);
                        cmd.Parameters.AddWithValue("@id", SelectSpecificEmployeeDetailsObj.id);
                        cmd.Parameters.AddWithValue("@languageID", userLanguageID);
                    }

                    cmd.CommandType = CommandType.StoredProcedure;

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            var result = new
                            {
                                Company_Employee_ID = reader.GetInt32(reader.GetOrdinal("Company_Employee_ID")),
                                Employee_ID = reader.GetString(reader.GetOrdinal("Employee_ID")),
                                Company_Employee_Name = reader.GetString(reader.GetOrdinal("Company_Employee_Name")),
                                Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                Country_ID = reader.GetInt32(reader.GetOrdinal("Country_ID")),
                                State_ID = reader.GetInt32(reader.GetOrdinal("State_ID")),
                                Company_Employee_MobileNumber = reader.GetString(reader.GetOrdinal("Company_Employee_MobileNumber")),
                                Company_Employee_Landline_Number = reader.GetString(reader.GetOrdinal("Company_Employee_Landline_Number")),
                                Company_Employee_ZipCode = reader.GetString(reader.GetOrdinal("Company_Employee_ZipCode")),
                                Company_Employee_ActiveFrom = reader.IsDBNull(reader.GetOrdinal("Company_Employee_ActiveFrom")) ? "" : Convert.ToDateTime(reader["Company_Employee_ActiveFrom"]).ToString("dd-MMM-yyyy"),
                                Company_Employee_ValidateUpTo = reader.IsDBNull(reader.GetOrdinal("Company_Employee_ValidateUpTo")) ? "" : Convert.ToDateTime(reader["Company_Employee_ValidateUpTo"]).ToString("dd-MMM-yyyy"),
                                Company_Employee_Active = reader.GetBoolean(reader.GetOrdinal("Company_Employee_Active")),
                                Company_Employee_Manager_ID = reader.GetInt32(reader.GetOrdinal("Company_Employee_Manager_ID")),
                                Company_Employee_Address = reader.GetString(reader.GetOrdinal("Company_Employee_Address")),
                                Company_Employee_Location = reader.GetString(reader.GetOrdinal("Company_Employee_Location")),
                                Company_Employee_Email = reader.GetString(reader.GetOrdinal("Company_Employee_Email")),
                                Company_Employee_Department_ID = reader.GetInt32(reader.GetOrdinal("Company_Employee_Department_ID")),
                                Company_Employee_Designation_ID = reader.GetInt32(reader.GetOrdinal("Company_Employee_Designation_ID")),
                                Region_ID = reader.IsDBNull(reader.GetOrdinal("Region_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Region_ID")),
                                Region_Name = reader.GetString(reader.GetOrdinal("Region_Name"))
                            };

                            //return Json(result, JsonRequestBehavior.AllowGet);
                            return new JsonResult(result);
                        }
                        else
                        {
                            return new JsonResult(new { Error = "No data found" }) { StatusCode = 404 };
                        }
                    }
                }

                // return HttpNotFound();
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: InsertEmployeeDetails /Mithun:::
        /// <summary>
        /// to insert the employee details
        /// </summary> 

        public static IActionResult InsertEmployeeDetails(InsertEmployeeDetailsList InsertEmployeeDetailsObj, string constring, int LogException)
        {
            try
            {
                //GNM_User User = (GNM_User)Session["UserDetails"];
                //GNM_User User = InsertEmployeeDetailsObj.UserDetails.FirstOrDefault();
                int userID = InsertEmployeeDetailsObj.User_ID;

                JObject jobj = JObject.Parse(InsertEmployeeDetailsObj.EmployeeData);

                string companyID = jobj["companyID"].ToString();
                string Name = Uri.UnescapeDataString(jobj["Name"].ToString());
                bool isActive = jobj["isActive"].ToString() == "checked";
                string employeeID = Uri.UnescapeDataString(jobj["employeeID"].ToString());
                string mobileNo = Uri.UnescapeDataString(jobj["mobileNo"].ToString());
                string landLineNo = Uri.UnescapeDataString(jobj["landLineNo"].ToString());
                string zipCode = Common.DecryptString(jobj["zipCode"].ToString());
                DateTime? activeFrom = string.IsNullOrEmpty(jobj["activeFrom"].ToString()) ? (DateTime?)null : Convert.ToDateTime(jobj["activeFrom"].ToString());
                DateTime? activeTo = string.IsNullOrEmpty(jobj["activeTo"].ToString()) ? (DateTime?)null : Convert.ToDateTime(jobj["activeTo"].ToString());
                string address1 = Uri.UnescapeDataString(jobj["address1"].ToString());
                string location = Uri.UnescapeDataString(jobj["location"].ToString());
                string email = Uri.UnescapeDataString(jobj["email"].ToString());
                string countryID = jobj["countryID"].ToString();
                string stateID = jobj["stateID"].ToString();
                string deptID = jobj["deptID"].ToString();
                string desgnID = jobj["desgnID"].ToString();
                string mgrID = jobj["mgrID"].ToString();
                int? regionID = string.IsNullOrEmpty(jobj["Region_ID"].ToString()) ? (int?)null : Convert.ToInt32(jobj["Region_ID"].ToString());
                DateTime localTime = DateTime.Now; // Assuming localTime is defined as current time

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    using (SqlCommand cmd = new SqlCommand("Up_Ins_Am_Erp_InsertEmployeeDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@CompanyID", Convert.ToInt32(companyID));
                        cmd.Parameters.AddWithValue("@Name", Name);
                        cmd.Parameters.AddWithValue("@IsActive", isActive);
                        cmd.Parameters.AddWithValue("@EmployeeID", employeeID);
                        cmd.Parameters.AddWithValue("@MobileNo", mobileNo);
                        cmd.Parameters.AddWithValue("@LandLineNo", landLineNo);
                        cmd.Parameters.AddWithValue("@ZipCode", zipCode);
                        cmd.Parameters.AddWithValue("@ActiveFrom", (object)activeFrom ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ActiveTo", (object)activeTo ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Address1", address1);
                        cmd.Parameters.AddWithValue("@Location", location);
                        cmd.Parameters.AddWithValue("@Email", email);
                        cmd.Parameters.AddWithValue("@CountryID", Convert.ToInt32(countryID));
                        cmd.Parameters.AddWithValue("@StateID", Convert.ToInt32(stateID));
                        cmd.Parameters.AddWithValue("@DeptID", Convert.ToInt32(deptID));
                        cmd.Parameters.AddWithValue("@DesgnID", Convert.ToInt32(desgnID));
                        cmd.Parameters.AddWithValue("@MgrID", string.IsNullOrEmpty(mgrID) ? (object)DBNull.Value : Convert.ToInt32(mgrID));
                        cmd.Parameters.AddWithValue("@Region_ID", (object)regionID ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ModifiedDate", localTime);
                        cmd.Parameters.AddWithValue("@ModifiedBy", userID);

                        SqlParameter outputIdParam = new SqlParameter("@Company_Employee_ID", SqlDbType.Int)
                        {
                            Direction = ParameterDirection.Output
                        };
                        cmd.Parameters.Add(outputIdParam);

                        conn.Open();
                        cmd.ExecuteNonQuery();

                        int addedEmployeeId = (int)outputIdParam.Value;

                        //   gbl.InsertGPSDetails(Convert.ToInt32(InsertEmployeeDetailsObj.Company_ID), Convert.ToInt32(InsertEmployeeDetailsObj.Branch), Convert.ToInt32(InsertEmployeeDetailsObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreCompanyMaster",constring)), Convert.ToInt32(companyID), 0, 0, "Update", false, Convert.ToInt32(InsertEmployeeDetailsObj.MenuID));

                        var result = new
                        {
                            Company_Employee_ID = addedEmployeeId
                        };

                        //return Json(result, JsonRequestBehavior.AllowGet);
                        return new JsonResult(result);
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: UpdateEmployeeDetails /Mithun:::
        /// <summary>
        /// to update the employee details
        /// </summary> 
        public static IActionResult UpdateEmployeeDetails(UpdateEmployeeDetailsList UpdateEmployeeDetailsObj, string constring, int LogException)
        {
            try
            {
                //GNM_User User = (GNM_User)Session["UserDetails"];
                GNM_User User = UpdateEmployeeDetailsObj.UserDetails.FirstOrDefault();
                int userID = User.User_ID;

                JObject jobj = JObject.Parse(UpdateEmployeeDetailsObj.EmployeeData);

                int primaryKey = Convert.ToInt32(jobj["primaryKey"].ToString());
                string companyID = jobj["companyID"].ToString();
                string Name = Uri.UnescapeDataString(jobj["Name"].ToString());
                bool isActive = jobj["isActive"].ToString() == "checked";
                string employeeID = Uri.UnescapeDataString(jobj["employeeID"].ToString());
                string mobileNo = Uri.UnescapeDataString(jobj["mobileNo"].ToString());
                string landLineNo = Uri.UnescapeDataString(jobj["landLineNo"].ToString());
                string zipCode = Common.DecryptString(jobj["zipCode"].ToString());
                DateTime? activeFrom = string.IsNullOrEmpty(jobj["activeFrom"].ToString()) ? (DateTime?)null : Convert.ToDateTime(jobj["activeFrom"].ToString());
                DateTime? activeTo = string.IsNullOrEmpty(jobj["activeTo"].ToString()) ? (DateTime?)null : Convert.ToDateTime(jobj["activeTo"].ToString());
                string address1 = Uri.UnescapeDataString(jobj["address1"].ToString());
                string location = Uri.UnescapeDataString(jobj["location"].ToString());
                string email = Uri.UnescapeDataString(jobj["email"].ToString());
                string countryID = jobj["countryID"].ToString();
                string stateID = jobj["stateID"].ToString();
                string deptID = jobj["deptID"].ToString();
                string desgnID = jobj["desgnID"].ToString();
                string mgrID = jobj["mgrID"].ToString();
                int? regionID = string.IsNullOrEmpty(jobj["Region_ID"].ToString()) ? (int?)null : Convert.ToInt32(jobj["Region_ID"].ToString());
                DateTime localTime = DateTime.Now; // Assuming localTime is defined as current time

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    using (SqlCommand cmd = new SqlCommand("Up_Upd_Am_Erp_UpdateEmployeeDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@PrimaryKey", primaryKey);
                        cmd.Parameters.AddWithValue("@CompanyID", Convert.ToInt32(companyID));
                        cmd.Parameters.AddWithValue("@Name", Name);
                        cmd.Parameters.AddWithValue("@IsActive", isActive);
                        cmd.Parameters.AddWithValue("@EmployeeID", employeeID);
                        cmd.Parameters.AddWithValue("@MobileNo", mobileNo);
                        cmd.Parameters.AddWithValue("@LandLineNo", landLineNo);
                        cmd.Parameters.AddWithValue("@ZipCode", zipCode);
                        cmd.Parameters.AddWithValue("@ActiveFrom", (object)activeFrom ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ActiveTo", (object)activeTo ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Address1", address1);
                        cmd.Parameters.AddWithValue("@Location", location);
                        cmd.Parameters.AddWithValue("@Email", email);
                        cmd.Parameters.AddWithValue("@CountryID", Convert.ToInt32(countryID));
                        cmd.Parameters.AddWithValue("@StateID", Convert.ToInt32(stateID));
                        cmd.Parameters.AddWithValue("@DeptID", Convert.ToInt32(deptID));
                        cmd.Parameters.AddWithValue("@DesgnID", Convert.ToInt32(desgnID));
                        cmd.Parameters.AddWithValue("@MgrID", string.IsNullOrEmpty(mgrID) ? (object)DBNull.Value : Convert.ToInt32(mgrID));
                        cmd.Parameters.AddWithValue("@Region_ID", (object)regionID ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ModifiedDate", localTime);
                        cmd.Parameters.AddWithValue("@ModifiedBy", userID);

                        conn.Open();
                        cmd.ExecuteNonQuery();

                        //     gbl.InsertGPSDetails(Convert.ToInt32(UpdateEmployeeDetailsObj.Company_ID), Convert.ToInt32(UpdateEmployeeDetailsObj.Branch), Convert.ToInt32(UpdateEmployeeDetailsObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreCompanyMaster",constring)), Convert.ToInt32(companyID), 0, 0, "Update", false, Convert.ToInt32(UpdateEmployeeDetailsObj.MenuID));

                        return new JsonResult(new { Message = "Updated" }) { StatusCode = 200 };

                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }


        #endregion

        #region ::: InsertCompanyLocale /Mithun:::
        /// <summary>
        /// to insert the company locale details
        /// </summary>
        public static IActionResult InsertCompanyLocale(InsertCompanyLocaleList InsertCompanyLocaleObj, string constring, int LogException)
        {
            try
            {
                int userLanguageID = Convert.ToInt32(InsertCompanyLocaleObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(InsertCompanyLocaleObj.GeneralLanguageID);
                JObject jobj = JObject.Parse(InsertCompanyLocaleObj.companyData);

                string CompanyName = Common.DecryptString(jobj["CompanyName"].ToString());
                string ShortName = Common.DecryptString(jobj["ShortName"].ToString());
                string Addr1 = Common.DecryptString(jobj["Addr1"].ToString());
                int companyID = Convert.ToInt32(jobj["companyID"].ToString());

                // Define your connection string (better to store it in web.config)


                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Call the stored procedure
                    using (SqlCommand command = new SqlCommand("Up_Ins_Am_Erp_InsertCompanyLocale", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.AddWithValue("@Company_Address", Addr1);
                        command.Parameters.AddWithValue("@Company_ID", companyID);
                        command.Parameters.AddWithValue("@Company_Name", CompanyName);
                        command.Parameters.AddWithValue("@Company_ShortName", ShortName);
                        command.Parameters.AddWithValue("@Language_ID", userLanguageID);

                        // Output parameter
                        SqlParameter outputIdParam = new SqlParameter("@Company_Locale_ID", SqlDbType.Int)
                        {
                            Direction = ParameterDirection.Output
                        };
                        command.Parameters.Add(outputIdParam);

                        // Execute the command
                        command.ExecuteNonQuery();

                        int insertedRowID = (int)outputIdParam.Value;

                        // Insert GPS Details
                        //gbl.InsertGPSDetails(
                        //    Convert.ToInt32(InsertCompanyLocaleObj.Company_ID),
                        //    Convert.ToInt32(InsertCompanyLocaleObj.Branch),
                        //    Convert.ToInt32(InsertCompanyLocaleObj.User_ID),
                        //    Convert.ToInt32(Common.GetObjectID("CoreCompanyMaster",constring)),
                        //    companyID, 0, 0, "Update", false,
                        //    Convert.ToInt32(InsertCompanyLocaleObj.MenuID)
                        //);

                        var result = new
                        {
                            Company_Locale_ID = insertedRowID
                        };

                        //return Json(result, JsonRequestBehavior.AllowGet);
                        return new JsonResult(result);
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }


        #endregion

        #region ::: UpdateCompanyLocale /Mithun:::
        /// <summary>
        /// to update the company locale details
        /// </summary>
        public static IActionResult UpdateCompanyLocale(UpdateCompanyLocaleList UpdateCompanyLocaleObj, string constring, int LogException)
        {
            try
            {
                int userLanguageID = Convert.ToInt32(UpdateCompanyLocaleObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(UpdateCompanyLocaleObj.GeneralLanguageID);
                JObject jobj = JObject.Parse(UpdateCompanyLocaleObj.companyData);

                int primaryKey = Convert.ToInt32(jobj["CompanyLocalePK"].ToString());
                string CompanyName = Common.DecryptString(jobj["CompanyName"].ToString());
                string ShortName = Common.DecryptString(jobj["ShortName"].ToString());
                string Addr1 = Common.DecryptString(jobj["Addr1"].ToString());
                int companyID = Convert.ToInt32(jobj["companyID"].ToString());



                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Call the stored procedure
                    using (SqlCommand command = new SqlCommand("Up_Upd_Am_Erp_UpdateCompanyLocale", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.AddWithValue("@Company_Locale_ID", primaryKey);
                        command.Parameters.AddWithValue("@Company_Address", Addr1);
                        command.Parameters.AddWithValue("@Company_ID", companyID);
                        command.Parameters.AddWithValue("@Company_Name", CompanyName);
                        command.Parameters.AddWithValue("@Company_ShortName", ShortName);
                        command.Parameters.AddWithValue("@Language_ID", userLanguageID);

                        // Execute the command
                        command.ExecuteNonQuery();

                        // Insert GPS Details
                        //gbl.InsertGPSDetails(
                        //    Convert.ToInt32(UpdateCompanyLocaleObj.Company_ID),
                        //    Convert.ToInt32(UpdateCompanyLocaleObj.Branch),
                        //    Convert.ToInt32(UpdateCompanyLocaleObj.User_ID),
                        //    Convert.ToInt32(Common.GetObjectID("CoreCompanyMaster",constring)),
                        //    companyID, 0, 0, "Update", false,
                        //    Convert.ToInt32(UpdateCompanyLocaleObj.MenuID)
                        //);
                        return new JsonResult(new { Message = "Updated" }) { StatusCode = 200 };
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: InsertBranchLocale /Mithun:::
        /// <summary>
        /// to insert the branch locale details
        /// </summary>
        public static IActionResult InsertBranchLocale(InsertBranchLocaleList InsertBranchLocaleObj, string constring, int LogException)
        {
            try
            {
                int userLanguageID = Convert.ToInt32(InsertBranchLocaleObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(InsertBranchLocaleObj.GeneralLanguageID);

                JObject jobj = JObject.Parse(InsertBranchLocaleObj.BranchLocaleData);

                int branchID = Convert.ToInt32(jobj["branchID"].ToString());
                string Name = Common.DecryptString(jobj["Name"].ToString());
                string shortName = Common.DecryptString(jobj["shortName"].ToString());
                string address1 = Common.DecryptString(jobj["address1"].ToString());
                string location = Common.DecryptString(jobj["location"].ToString());

                int branchLocaleID;
                int companyID;



                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Call the stored procedure to insert branch locale and get company ID
                    using (SqlCommand cmd = new SqlCommand("Up_Ins_Am_Erp_InsertBranchLocale", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@Branch_Address", address1);
                        cmd.Parameters.AddWithValue("@Branch_ID", branchID);
                        cmd.Parameters.AddWithValue("@Branch_Location", location);
                        cmd.Parameters.AddWithValue("@Branch_Name", Name);
                        cmd.Parameters.AddWithValue("@Branch_ShortName", shortName);
                        cmd.Parameters.AddWithValue("@Language_ID", userLanguageID);

                        SqlParameter outputBranchLocaleIdParam = new SqlParameter("@Branch_Locale_ID", SqlDbType.Int)
                        {
                            Direction = ParameterDirection.Output
                        };
                        cmd.Parameters.Add(outputBranchLocaleIdParam);

                        SqlParameter outputCompanyIdParam = new SqlParameter("@Company_ID", SqlDbType.Int)
                        {
                            Direction = ParameterDirection.Output
                        };
                        cmd.Parameters.Add(outputCompanyIdParam);

                        cmd.ExecuteNonQuery();

                        branchLocaleID = (int)outputBranchLocaleIdParam.Value;
                        companyID = (int)outputCompanyIdParam.Value;
                    }

                    // Insert GPS details
                    //gbl.InsertGPSDetails(
                    //    Convert.ToInt32(InsertBranchLocaleObj.Company_ID),
                    //    Convert.ToInt32(InsertBranchLocaleObj.Branch),
                    //    Convert.ToInt32(InsertBranchLocaleObj.User_ID),
                    //    Convert.ToInt32(Common.GetObjectID("CoreCompanyMaster",constring)),
                    //    companyID,
                    //    0,
                    //    0,
                    //    "Update",
                    //    false,
                    //    Convert.ToInt32(InsertBranchLocaleObj.MenuID)
                    //);

                    var result = new
                    {
                        Branch_Locale_ID = branchLocaleID
                    };

                    //return Json(result, JsonRequestBehavior.AllowGet);
                    return new JsonResult(result);
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: UpdateBranchLocale /Mithun:::
        /// <summary>
        /// to update the branch locale details
        /// </summary>
        public static IActionResult UpdateBranchLocale(UpdateBranchLocaleList UpdateBranchLocaleObj, string constring, int LogException)
        {
            try
            {
                int userLanguageID = Convert.ToInt32(UpdateBranchLocaleObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(UpdateBranchLocaleObj.GeneralLanguageID);
                JObject jobj = JObject.Parse(UpdateBranchLocaleObj.BranchLocaleData);

                int branchID = Convert.ToInt32(jobj["branchID"].ToString());
                int branchLocaleID = Convert.ToInt32(jobj["branchLocaleID"].ToString());
                string Name = Common.DecryptString(jobj["Name"].ToString());
                string shortName = Common.DecryptString(jobj["shortName"].ToString());
                string address1 = Common.DecryptString(jobj["address1"].ToString());
                string location = Common.DecryptString(jobj["location"].ToString());

                int companyID;
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Call the stored procedure to update branch locale and get company ID
                    using (SqlCommand cmd = new SqlCommand("Up_Udp_Am_Erp_UpdateBranchLocale", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@Branch_Locale_ID", branchLocaleID);
                        cmd.Parameters.AddWithValue("@Branch_Address", address1);
                        cmd.Parameters.AddWithValue("@Branch_ID", branchID);
                        cmd.Parameters.AddWithValue("@Branch_Location", location);
                        cmd.Parameters.AddWithValue("@Branch_Name", Name);
                        cmd.Parameters.AddWithValue("@Branch_ShortName", shortName);
                        cmd.Parameters.AddWithValue("@Language_ID", userLanguageID);

                        SqlParameter outputCompanyIdParam = new SqlParameter("@Company_ID", SqlDbType.Int)
                        {
                            Direction = ParameterDirection.Output
                        };
                        cmd.Parameters.Add(outputCompanyIdParam);

                        cmd.ExecuteNonQuery();

                        companyID = (int)outputCompanyIdParam.Value;
                    }

                    // Insert GPS details
                    //gbl.InsertGPSDetails(
                    //    Convert.ToInt32(UpdateBranchLocaleObj.Company_ID),
                    //    Convert.ToInt32(UpdateBranchLocaleObj.Branch),
                    //    Convert.ToInt32(UpdateBranchLocaleObj.User_ID),
                    //    Convert.ToInt32(Common.GetObjectID("CoreCompanyMaster",constring)),
                    //    companyID,
                    //    0,
                    //    0,
                    //    "Update",
                    //    false,
                    //    Convert.ToInt32(UpdateBranchLocaleObj.MenuID)
                    //);
                    return new JsonResult(new { Message = "Updated" }) { StatusCode = 200 };
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
                //RedirectToAction("Error");
            }
        }

        #endregion

        #region ::: InsertEmployeeLocale /Mithun:::
        /// <summary>
        /// to insert the Employee locale details
        /// </summary>
        public static IActionResult InsertEmployeeLocale(InsertEmployeeLocaleList InsertEmployeeLocaleObj, string constring, int LogException)
        {
            try
            {
                int userLanguageID = Convert.ToInt32(InsertEmployeeLocaleObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(InsertEmployeeLocaleObj.GeneralLanguageID);

                JObject jobj = JObject.Parse(InsertEmployeeLocaleObj.employeeLocaleData);

                int EmpID = Convert.ToInt32(jobj["EmpID"].ToString());
                string EmpName = Common.DecryptString(jobj["EmpName"].ToString());
                string EmpAddress1 = Common.DecryptString(jobj["EmpAddress1"].ToString());
                string Location = Common.DecryptString(jobj["Location"].ToString());

                int employeeLocaleID;


                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Call the stored procedure to insert employee locale
                    using (SqlCommand cmd = new SqlCommand("Up_Ins_Am_Erp_InsertEmployeeLocaleDetails", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@EmpID", EmpID);
                        cmd.Parameters.AddWithValue("@EmpName", EmpName);
                        cmd.Parameters.AddWithValue("@EmpAddress1", EmpAddress1);
                        cmd.Parameters.AddWithValue("@Location", Location);
                        cmd.Parameters.AddWithValue("@Language_ID", userLanguageID);

                        SqlParameter outputEmployeeLocaleIdParam = new SqlParameter("@Employee_Locale_ID", SqlDbType.Int)
                        {
                            Direction = ParameterDirection.Output
                        };
                        cmd.Parameters.Add(outputEmployeeLocaleIdParam);

                        cmd.ExecuteNonQuery();

                        employeeLocaleID = (int)outputEmployeeLocaleIdParam.Value;
                    }

                    // Insert GPS details
                    //gbl.InsertGPSDetails(
                    //    Convert.ToInt32(InsertEmployeeLocaleObj.Company_ID),
                    //    Convert.ToInt32(InsertEmployeeLocaleObj.Branch),
                    //    Convert.ToInt32(InsertEmployeeLocaleObj.User_ID),
                    //    Convert.ToInt32(Common.GetObjectID("CoreCompanyMaster",constring)),
                    //    Convert.ToInt32(InsertEmployeeLocaleObj.Company_ID),
                    //    0,
                    //    0,
                    //    "Update",
                    //    false,
                    //    Convert.ToInt32(InsertEmployeeLocaleObj.MenuID)
                    //);

                    var result = new
                    {
                        Company_Employee_Locale_ID = employeeLocaleID
                    };

                    //return Json(result, JsonRequestBehavior.AllowGet);
                    return new JsonResult(result);
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: UpdateEmployeeLocale /Mithun:::
        /// <summary>
        /// to update the Employee locale details
        /// </summary>
        public static IActionResult UpdateEmployeeLocale(UpdateEmployeeLocaleList UpdateEmployeeLocaleObj, string constring, int LogException)
        {
            try
            {
                JObject jobj = JObject.Parse(UpdateEmployeeLocaleObj.employeeLocaleData);

                int EmpID = Convert.ToInt32(jobj["EmpID"].ToString());
                int EmpLocaleID = Convert.ToInt32(jobj["EmpLocaleID"].ToString());
                string EmpName = Common.DecryptString(jobj["EmpName"].ToString());
                string EmpAddress1 = Common.DecryptString(jobj["EmpAddress1"].ToString());
                string Location = Common.DecryptString(jobj["Location"].ToString());

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    using (SqlCommand cmd = new SqlCommand("Up_Upd_Am_Erp_UpdateEmployeeLocaleDetails", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@EmpLocaleID", EmpLocaleID);
                        cmd.Parameters.AddWithValue("@EmpID", EmpID);
                        cmd.Parameters.AddWithValue("@EmpName", EmpName);
                        cmd.Parameters.AddWithValue("@EmpAddress1", EmpAddress1);
                        cmd.Parameters.AddWithValue("@Location", Location);

                        cmd.ExecuteNonQuery();
                    }
                }

                //gbl.InsertGPSDetails(
                //    Convert.ToInt32(InsertEmployeeLocaleObj.Company_ID),
                //    Convert.ToInt32(InsertEmployeeLocaleObj.Branch),
                //    Convert.ToInt32(InsertEmployeeLocaleObj.User_ID),
                //    Convert.ToInt32(Common.GetObjectID("CoreCompanyMaster",constring)),
                //    Convert.ToInt32(InsertEmployeeLocaleObj.Company_ID),
                //    0,
                //    0,
                //    "Update",
                //    false,
                //    Convert.ToInt32(InsertEmployeeLocaleObj.MenuID)
                //);
                return new JsonResult(new { Message = "Updated" }) { StatusCode = 200 };

            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: DeleteCompany /Mithun:::
        /// <summary>
        /// to Delete the Company details
        /// </summary>
        public static IActionResult DeleteCompany(DeleteCompanyList DeleteCompanyObj, string constring, int LogException)
        {
            string errorMsg = "";
            var Culture = "Resource_" + DeleteCompanyObj.Lang;
            try
            {

                // Parse the request parameters
                JObject jobj = JObject.Parse(DeleteCompanyObj.key);
                int rowCount = jobj["rows"].Count();
                int id = 0;

                // Create a SQL connection
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    SqlTransaction transaction = connection.BeginTransaction();

                    try
                    {
                        // Iterate over each row to be deleted
                        for (int i = 0; i < rowCount; i++)
                        {
                            // Extract the company ID from the request
                            id = jobj["rows"].ElementAt(i).ToObject<JObject>()["id"].Value<int>();

                            // Construct SQL command to delete company and related locale records
                            string deleteCompanyQuery = "DELETE FROM GNM_Company WHERE Company_ID = @CompanyID";
                            SqlCommand deleteCompanyCommand = new SqlCommand(deleteCompanyQuery, connection, transaction);
                            deleteCompanyCommand.Parameters.AddWithValue("@CompanyID", id);
                            deleteCompanyCommand.ExecuteNonQuery();

                            string deleteLocaleQuery = "DELETE FROM GNM_CompanyLocale WHERE Company_ID = @CompanyID";
                            SqlCommand deleteLocaleCommand = new SqlCommand(deleteLocaleQuery, connection, transaction);
                            deleteLocaleCommand.Parameters.AddWithValue("@CompanyID", id);
                            deleteLocaleCommand.ExecuteNonQuery();
                        }

                        // Commit the transaction
                        transaction.Commit();

                        // Log the deletion
                        //  gbl.InsertGPSDetails(Convert.ToInt32(DeleteCompanyObj.Company_ID), Convert.ToInt32(DeleteCompanyObj.Branch), Convert.ToInt32(DeleteCompanyObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreCompanyMaster",constring)), id, 0, 0, "Delete", false, Convert.ToInt32(DeleteCompanyObj.MenuID));

                        // Success message
                        errorMsg += CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "deletedsuccessfully").ToString();
                    }
                    catch (Exception ex)
                    {
                        // Rollback the transaction on error
                        transaction.Rollback();

                        // Handle specific exceptions
                        if (ex.InnerException != null && ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                        {
                            errorMsg += "Dependency found cannot delete the records";
                        }
                        else
                        {
                            errorMsg += CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle general exceptions
                errorMsg += ex.Message;
            }
            //return errorMsg;
            return new JsonResult(errorMsg);
        }

        #endregion

        #region ::: DeleteBranch /Mithun:::
        /// <summary>
        /// to Delete the Branch details
        /// </summary>
        public static IActionResult DeleteBranch(DeleteBranchList DeleteBranchObj, string constring, int LogException)
        {
            string errorMsg = "";
            using (SqlConnection conn = new SqlConnection(constring))
            {
                try
                {
                    string jsonData = DeleteBranchObj.key;
                    if (string.IsNullOrEmpty(jsonData))
                    {
                        errorMsg = CommonFunctionalities.GetGlobalResourceObject(DeleteBranchObj.UserCulture.ToString(), "nodataprovided").ToString();
                        //return errorMsg;
                        return new JsonResult(errorMsg);
                    }

                    JObject jobj = JObject.Parse(jsonData);
                    int rowCount = jobj["rows"].Count();
                    int companyId = 0;

                    conn.Open();
                    using (SqlTransaction transaction = conn.BeginTransaction())
                    {
                        try
                        {
                            for (int i = 0; i < rowCount; i++)
                            {
                                int id = (int)jobj["rows"][i]["id"];

                                // Get the Company_ID for logging before deleting
                                if (i == 0) // Assuming all rows have the same Branch_ID
                                {
                                    using (SqlCommand getCompanyIdCmd = new SqlCommand("SELECT Company_ID FROM GNM_Branch WHERE Branch_ID = @Id", conn, transaction))
                                    {
                                        getCompanyIdCmd.Parameters.AddWithValue("@Id", id);
                                        using (SqlDataReader reader = getCompanyIdCmd.ExecuteReader())
                                        {
                                            if (reader.Read())
                                            {
                                                companyId = reader.GetInt32(0);
                                            }
                                        }
                                    }
                                }

                                // Delete the related row from GNM_BranchLocale
                                using (SqlCommand deleteLocaleCmd = new SqlCommand("DELETE FROM GNM_BranchLocale WHERE Branch_ID = @Id", conn, transaction))
                                {
                                    deleteLocaleCmd.Parameters.AddWithValue("@Id", id);
                                    deleteLocaleCmd.ExecuteNonQuery();
                                }

                                // Delete the row from GNM_Branch
                                using (SqlCommand deleteCmd = new SqlCommand("DELETE FROM GNM_Branch WHERE Branch_ID = @Id", conn, transaction))
                                {
                                    deleteCmd.Parameters.AddWithValue("@Id", id);
                                    deleteCmd.ExecuteNonQuery();
                                }
                            }

                            transaction.Commit();

                            // Log success or perform additional actions after database operation
                            //gbl.InsertGPSDetails(
                            //    Convert.ToInt32(DeleteBranchObj.Company_ID),
                            //    Convert.ToInt32(DeleteBranchObj.Branch),
                            //    Convert.ToInt32(DeleteBranchObj.User_ID),
                            //    Convert.ToInt32(Common.GetObjectID("CoreCompanyMaster",constring)),
                            //    companyId,
                            //    0,
                            //    0,
                            //    "Delete",
                            //    false,
                            //    Convert.ToInt32(DeleteBranchObj.MenuID)
                            //);

                            errorMsg = CommonFunctionalities.GetGlobalResourceObject(DeleteBranchObj.UserCulture.ToString(), "deletedsuccessfully").ToString();
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            throw ex; // Rethrow exception to handle it in the outer catch block
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (ex.InnerException != null && ex.InnerException.InnerException != null && ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                    {
                        errorMsg = CommonFunctionalities.GetGlobalResourceObject(DeleteBranchObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                    }
                    else
                    {
                        errorMsg = CommonFunctionalities.GetGlobalResourceObject(DeleteBranchObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                    }
                }
            }

            //return errorMsg;
            return new JsonResult(errorMsg);
        }

        #endregion

        #region ::: DeleteBranchTaxDetails /Mithun:::
        /// <summary>
        /// to Delete the Branch tax details
        /// </summary>
        public static IActionResult DeleteBranchTaxDetails(DeleteBranchTaxDetailsList DeleteBranchTaxDetailsObj, string constring, int LogException)
        {
            string errorMsg = "";
            int CompanyID = Convert.ToInt32(DeleteBranchTaxDetailsObj.Company_ID);

            try
            {
                string jsonData = DeleteBranchTaxDetailsObj.key;
                if (string.IsNullOrEmpty(jsonData))
                {
                    errorMsg += "No JSON data provided.";
                    //return errorMsg;
                    return new JsonResult(errorMsg);
                }

                // Parse JSON data
                JObject json = JObject.Parse(jsonData);
                JArray rowsArray = (JArray)json["rows"];

                // Using block for SqlConnection and SqlTransaction
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    using (SqlTransaction transaction = conn.BeginTransaction())
                    {
                        try
                        {
                            foreach (JObject row in rowsArray)
                            {
                                int id = Convert.ToInt32(row["id"].ToString());

                                // Using block for SqlCommand
                                using (SqlCommand deleteCommand = new SqlCommand())
                                {
                                    deleteCommand.Connection = conn;
                                    deleteCommand.Transaction = transaction;
                                    deleteCommand.CommandText = "DELETE FROM GNM_BranchTaxStructure WHERE BranchTaxStructure_ID = @id";
                                    deleteCommand.Parameters.AddWithValue("@id", id);
                                    deleteCommand.ExecuteNonQuery();
                                }
                            }

                            transaction.Commit();
                            //  gbl.InsertGPSDetails(Convert.ToInt32(DeleteBranchTaxDetailsObj.Company_ID), Convert.ToInt32(DeleteBranchTaxDetailsObj.Branch), Convert.ToInt32(DeleteBranchTaxDetailsObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreCompanyMaster",constring)), CompanyID, 0, 0, "Update", false, Convert.ToInt32(DeleteBranchTaxDetailsObj.MenuID));

                            errorMsg += CommonFunctionalities.GetGlobalResourceObject(DeleteBranchTaxDetailsObj.UserCulture.ToString(), "deletedsuccessfully").ToString();
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            errorMsg += ex.Message; // Handle specific error messages accordingly
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                errorMsg += ex.Message; // Handle specific error messages accordingly
            }

            //return errorMsg;
            return new JsonResult(errorMsg);
        }





        #endregion

        #region ::: DeleteCompanyRelation /Mithun:::
        /// <summary>
        /// to Delete the Company Relation details
        /// </summary>
        public static IActionResult DeleteCompanyRelation(DeleteCompanyRelationList DeleteCompanyRelationObj, string constring, int LOgException)
        {
            string errorMsg = "";
            var Culture = "Resource_" + DeleteCompanyRelationObj.Lang;

            try
            {
                JObject jobj = JObject.Parse(DeleteCompanyRelationObj.key);
                int rowCount = jobj["rows"].Count();
                int id = 0;

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    for (int i = 0; i < rowCount; i++)
                    {
                        id = jobj["rows"].ElementAt(i)["id"].ToObject<int>();

                        using (SqlCommand command = new SqlCommand("DELETE FROM GNM_Company_Company_Relation WHERE Company_Relationship_ID = @Company_Relationship_ID", connection))
                        {
                            command.Parameters.AddWithValue("@Company_Relationship_ID", id);

                            try
                            {
                                command.ExecuteNonQuery();
                            }
                            catch (SqlException sqlEx)
                            {
                                if (sqlEx.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                                {
                                    errorMsg += CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                                }
                                else
                                {
                                    throw;
                                }
                            }
                        }
                    }
                }

                if (string.IsNullOrEmpty(errorMsg))
                {
                    errorMsg += CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "deletedsuccessfully").ToString();
                }
            }
            catch (Exception ex)
            {
                if (ex is SqlException && ex.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    errorMsg += CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    errorMsg += ex.Message; // Log or handle other exceptions as needed
                }
            }

            //return errorMsg;
            return new JsonResult(errorMsg);
        }

        #endregion

        #region ::: DeleteCompanyBrands /Mithun:::
        /// <summary>
        /// to Delete the Company brand details
        /// </summary>
        public static IActionResult DeleteCompanyBrands(DeleteCompanyBrandsList DeleteCompanyBrandsObj, string constring, int LogException)
        {
            string errorMsg = "";
            var Culture = "Resource_" + DeleteCompanyBrandsObj.Lang;
            try
            {
                JObject jobj = JObject.Parse(DeleteCompanyBrandsObj.key);
                int rowCount = jobj["rows"].Count();
                int id = 0;

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    for (int i = 0; i < rowCount; i++)
                    {
                        id = Convert.ToInt32(jobj["rows"][i]["id"]);

                        // Construct and execute DELETE query
                        string deleteQuery = "DELETE FROM GNM_CompanyBrands WHERE Company_Brand_ID = @Company_Brand_ID";
                        SqlCommand cmd = new SqlCommand(deleteQuery, connection);
                        cmd.Parameters.AddWithValue("@Company_Brand_ID", id);
                        cmd.ExecuteNonQuery();

                        // Log deletion details
                        //  gbl.InsertGPSDetails(Convert.ToInt32(DeleteCompanyBrandsObj.Company_ID), Convert.ToInt32(DeleteCompanyBrandsObj.Branch), Convert.ToInt32(DeleteCompanyBrandsObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreCompanyMaster",constring)), id, 0, 0, "Delete", false, Convert.ToInt32(DeleteCompanyBrandsObj.MenuID));
                    }
                }

                errorMsg += CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    errorMsg += CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    errorMsg += CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
            }
            //return errorMsg;
            return new JsonResult(errorMsg);
        }

        #endregion

        #region ::: DeleteCompanyEmployee /Mithun:::
        /// <summary>
        /// to Delete the Company Employee details
        /// </summary>
        public static IActionResult DeleteCompanyEmployee(DeleteCompanyEmployeeList DeleteCompanyEmployeeObj, string constring, int LogException)
        {
            string errorMsg = "";
            try
            {
                string jsonData = DeleteCompanyEmployeeObj.key;
                JObject jobj = JObject.Parse(jsonData);
                int rowCount = jobj["rows"].Count();
                int companyId = Convert.ToInt32(DeleteCompanyEmployeeObj.Company_ID);
                int branchId = Convert.ToInt32(DeleteCompanyEmployeeObj.Branch);
                int userId = Convert.ToInt32(DeleteCompanyEmployeeObj.User_ID);
                int menuId = Convert.ToInt32(DeleteCompanyEmployeeObj.MenuID);
                int coreCompanyMasterId = Convert.ToInt32(Common.GetObjectID("CoreCompanyMaster"));

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    SqlTransaction transaction = conn.BeginTransaction();

                    try
                    {
                        for (int i = 0; i < rowCount; i++)
                        {
                            int id = (int)jobj["rows"][i]["id"];

                            // Delete from GNM_CompanyEmployeeLocale
                            string deleteLocaleQuery = "DELETE FROM GNM_CompanyEmployeeLocale WHERE Company_Employee_ID = @Company_Employee_ID";
                            using (SqlCommand cmd = new SqlCommand(deleteLocaleQuery, conn, transaction))
                            {
                                cmd.Parameters.AddWithValue("@Company_Employee_ID", id);
                                cmd.ExecuteNonQuery();
                            }

                            // Delete from GNM_CompanyEmployee
                            string deleteEmployeeQuery = "DELETE FROM GNM_CompanyEmployee WHERE Company_Employee_ID = @Company_Employee_ID";
                            using (SqlCommand cmd = new SqlCommand(deleteEmployeeQuery, conn, transaction))
                            {
                                cmd.Parameters.AddWithValue("@Company_Employee_ID", id);
                                cmd.ExecuteNonQuery();
                            }
                        }

                        // Commit transaction
                        transaction.Commit();

                        // Insert GPS details
                        //gbl.InsertGPSDetails(
                        //    companyId,
                        //    branchId,
                        //    userId,
                        //    coreCompanyMasterId,
                        //    companyId,
                        //    0,
                        //    0,
                        //    "Delete",
                        //    false,
                        //    menuId
                        //);

                        errorMsg += CommonFunctionalities.GetGlobalResourceObject(DeleteCompanyEmployeeObj.UserCulture.ToString(), "deletedsuccessfully").ToString();
                    }
                    catch (SqlException sqlEx)
                    {
                        transaction.Rollback();

                        if (sqlEx.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                        {
                            errorMsg += CommonFunctionalities.GetGlobalResourceObject(DeleteCompanyEmployeeObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                        }
                        else
                        {
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                errorMsg += CommonFunctionalities.GetGlobalResourceObject(DeleteCompanyEmployeeObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
            }

            //return errorMsg;
            return new JsonResult(errorMsg);
        }

        #endregion

        #region ::: CheckBranchTaxDetails /Mithun:::
        /// <summary>
        /// to check if the Tax detail has already been selected for the branch
        /// </summary>
        public static IActionResult CheckBranchTaxDetails(CheckBranchTaxDetailsList CheckBranchTaxDetailsObj, string constring, int LogException)
        {
            int status = 0;

            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Check_Branch_Tax_Details", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Parameters
                        cmd.Parameters.AddWithValue("@TaxStructureID", CheckBranchTaxDetailsObj.taxStructureID);
                        cmd.Parameters.AddWithValue("@BranchID", CheckBranchTaxDetailsObj.branchID);
                        cmd.Parameters.AddWithValue("@PrimaryKey", CheckBranchTaxDetailsObj.primaryKey);

                        // Output parameter for status
                        SqlParameter statusParam = new SqlParameter("@Status", SqlDbType.Int);
                        statusParam.Direction = ParameterDirection.Output;
                        cmd.Parameters.Add(statusParam);

                        cmd.ExecuteNonQuery();

                        // Retrieve status value from output parameter
                        status = (int)statusParam.Value;
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            //return status;
            return new JsonResult(status);
        }

        #endregion

        #region ::: CheckCompanyBrandAssociation /Mithun:::
        /// <summary>
        /// to check if the brand is already associated with the company
        /// </summary>
        public static IActionResult CheckCompanyBrandAssociation(CheckCompanyBrandAssociationList CheckCompanyBrandAssociationObj, string constring, int LogException)
        {
            int status = 0;
            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    string query = "SELECT Company_Brand_ID FROM GNM_CompanyBrands WHERE Company_ID = @CompanyID AND Brand_ID = @BrandID";
                    SqlCommand cmd = new SqlCommand(query, conn);
                    cmd.Parameters.AddWithValue("@CompanyID", CheckCompanyBrandAssociationObj.companyID);
                    cmd.Parameters.AddWithValue("@BrandID", CheckCompanyBrandAssociationObj.brandID);

                    conn.Open();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            int companyBrandID = (int)reader["Company_Brand_ID"];
                            if (companyBrandID != CheckCompanyBrandAssociationObj.primaryKey)
                            {
                                status = 1;
                                break; // Exit loop once condition is met
                            }
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                // Handle web exception
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    // Handle other exceptions
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            //return status;
            return new JsonResult(status);
        }

        #endregion

        #region ::: CheckCompanyRelations /Mithun:::
        /// <summary>
        /// to check if the Company is already associated with the selected company
        /// </summary>
        public static IActionResult CheckCompanyRelations(CheckCompanyRelationsList CheckCompanyRelationsObj, string constring, int LogException)
        {
            int status = 0;
            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    SqlCommand command = new SqlCommand("Up_Chk_Am_Erp_CheckCompanyRelations", connection);
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@CompanyID", CheckCompanyRelationsObj.companyID);
                    command.Parameters.AddWithValue("@SelectedCompanyID", CheckCompanyRelationsObj.selectedCompanyID);
                    command.Parameters.AddWithValue("@PrimaryKey", CheckCompanyRelationsObj.primaryKey);

                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();
                    if (reader.HasRows)
                    {
                        status = 1; // If rows are returned, set status to 1
                    }
                    reader.Close();
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }

            //return status;
            return new JsonResult(status);
        }

        #endregion

        #region ::: CheckEmployeeSkills /Mithun:::
        /// <summary>
        /// to check if the employee is already associated with the skills
        /// </summary>

        public static IActionResult CheckEmployeeSkills(CheckEmployeeSkillsList CheckEmployeeSkillsObj, string constring, int LogException)
        {
            int status = 0;

            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand("Up_Chk_Am_Erp_CheckEmployeeSkillSet", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // Parameters for the stored procedure
                        command.Parameters.AddWithValue("@SkillID", CheckEmployeeSkillsObj.skillID);
                        command.Parameters.AddWithValue("@EmployeeID", CheckEmployeeSkillsObj.employeeID);
                        command.Parameters.AddWithValue("@PrimaryKey", CheckEmployeeSkillsObj.primaryKey);

                        // Output parameter for status
                        SqlParameter statusParameter = new SqlParameter("@Status", SqlDbType.Int);
                        statusParameter.Direction = ParameterDirection.Output;
                        command.Parameters.Add(statusParameter);

                        command.ExecuteNonQuery();

                        // Retrieve output parameter value
                        status = Convert.ToInt32(statusParameter.Value);
                    }
                }
            }
            catch (SqlException sqlEx)
            {

                LS.LogSheetExporter.LogToTextFile(sqlEx.HResult, sqlEx.Message, sqlEx.TargetSite.ToString(), sqlEx.StackTrace);
            }
            catch (Exception ex)
            {

                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return status;
            return new JsonResult(status);
        }

        #endregion

        #region ::: SelAllEmployeeSkillset /Mithun:::
        /// <summary>
        /// to select all the skills of the particular employee
        /// </summary>
        public static IActionResult SelAllEmployeeSkillset(SelAllEmployeeSkillsetList SelAllEmployeeSkillsetObj, string constring, int LogException, string sidx, string sord, int page, int rows)
        {
            try
            {
                int userLanguageID = Convert.ToInt32(SelAllEmployeeSkillsetObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelAllEmployeeSkillsetObj.GeneralLanguageID);

                //  var connectionString = ConfigurationManager.ConnectionStrings["YourConnectionString"].ConnectionString;
                var skillsetData = new List<dynamic>();
                var skillNamesList = new List<dynamic>();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_SelectSkillSet", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@EmployeeID", SelAllEmployeeSkillsetObj.employeeID);
                        cmd.Parameters.AddWithValue("@LanguageID", userLanguageID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);
                        cmd.Parameters.AddWithValue("@Sidx", sidx);
                        cmd.Parameters.AddWithValue("@Sord", sord);
                        cmd.Parameters.AddWithValue("@Page", page);
                        cmd.Parameters.AddWithValue("@Rows", rows);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                skillsetData.Add(new
                                {
                                    Employee_Skillset_ID = reader["Employee_Skillset_ID"],
                                    edit = $"<a title='Edit' href='#' key='{reader["Employee_Skillset_ID"]}' id='{reader["Employee_Skillset_ID"]}' class='editEmployeeSkills font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                    delete = $"<input type='checkbox' key='{reader["Employee_Skillset_ID"]}' id='chk{reader["Employee_Skillset_ID"]}' class='chkEmployeeSkillDelete'/>",
                                    Skillset = (reader["Skillset"].ToString()),
                                    Employee_Skillset_Rating = reader["Employee_Skillset_Rating"]
                                });
                            }

                            if (reader.NextResult())
                            {
                                while (reader.Read())
                                {
                                    skillNamesList.Add(new
                                    {
                                        RefMasterDetail_ID = reader["RefMasterDetail_ID"],
                                        RefMasterDetail_Name = reader["RefMasterDetail_Name"]
                                    });
                                }
                            }
                        }
                    }
                }

                var skillNames = "-1:--Select--;";
                foreach (var skill in skillNamesList)
                {
                    skillNames += $"{skill.RefMasterDetail_ID}:{skill.RefMasterDetail_Name.Replace(";", ":")};";
                }
                skillNames = skillNames.TrimEnd(';');

                var jsonResult = new
                {
                    total = (int)Math.Ceiling((double)skillsetData.Count / rows),
                    page = page,
                    records = skillsetData.Count,
                    data = skillsetData.Skip((page - 1) * rows).Take(rows).ToArray(),
                    skillNames
                };

                //return Json(jsonResult, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonResult);
            }
            catch (SqlException sqlEx)
            {
                LS.LogSheetExporter.LogToTextFile(sqlEx.HResult, sqlEx.Message, sqlEx.TargetSite.ToString(), sqlEx.StackTrace);
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: SaveSkills /Mithun:::
        /// <summary>
        /// to update the skills of the particular employee 
        /// </summary>
        public static IActionResult SaveSkills(SaveSkillsList SaveSkillsObj, string constring, int LogException)
        {
            try
            {
                JObject jobj = JObject.Parse(SaveSkillsObj.Data);
                JArray rows = (JArray)jobj["rows"];
                int rowCount = rows.Count;

                // Helper method to parse string to int?
                Func<string, int?> ParseNullableInt = (str) =>
                {
                    if (string.IsNullOrWhiteSpace(str))
                    {
                        return null;
                    }

                    if (int.TryParse(str, out int result))
                    {
                        return result;
                    }

                    return null; // or throw an exception, depending on how you want to handle invalid data
                };

                // Construct the XML data
                XDocument xmlData = new XDocument(
                    new XElement("Skills",
                        from row in rows
                        select new XElement("Skill",
                            new XElement("EmployeeSkillID", ParseNullableInt(row["employeeSkillID"].ToString())),
                            new XElement("EmployeeID", int.Parse(row["employeeID"].ToString())),
                            new XElement("SkillsetID", int.Parse(row["skillset"].ToString())),
                            new XElement("Rating", int.Parse(row["rating"].ToString()))
                        )
                    )
                );

                // Execute the stored procedure
                //   string connectionString = ConfigurationManager.ConnectionStrings["YourConnectionString"].ConnectionString;
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Ins_upd_Am_Erp_SaveSkills", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add(new SqlParameter("@SkillsData", SqlDbType.Xml)
                        {
                            Value = new SqlXml(xmlData.CreateReader())
                        });

                        cmd.ExecuteNonQuery();
                    }
                }
                return new JsonResult(new { Message = "Saved" }) { StatusCode = 200 };

            }
            catch (SqlException sqlEx)
            {
                LS.LogSheetExporter.LogToTextFile(sqlEx.HResult, sqlEx.Message, sqlEx.TargetSite.ToString(), sqlEx.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: DeleteSkills /Mithun:::
        /// <summary>
        /// to Delete the Employee Skillset details
        /// </summary>
        public static IActionResult DeleteSkills(DeleteSkillsList DeleteSkillsObj, string constring, int LogException)
        {
            string errorMsg = "";
            var Culture = "Resource_" + DeleteSkillsObj.Lang;
            try
            {
                // Assuming Request.Params["key"] contains your JSON data as string
                JObject jobj = JObject.Parse(DeleteSkillsObj.key);
                int rowCount = jobj["rows"].Count();
                int id = 0;

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    using (SqlTransaction transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            for (int i = 0; i < rowCount; i++)
                            {
                                id = Convert.ToInt32(jobj["rows"][i]["id"]);

                                // Delete operation using ADO.NET
                                string deleteCommandText = "DELETE FROM GNM_CompanyEmployeeSkillset WHERE Employee_Skillset_ID = @Id";
                                using (SqlCommand command = new SqlCommand(deleteCommandText, connection, transaction))
                                {
                                    command.Parameters.AddWithValue("@Id", id);
                                    command.ExecuteNonQuery();
                                }
                            }

                            transaction.Commit();
                            errorMsg += CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "deletedsuccessfully").ToString();
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();

                            if (ex.InnerException != null && ex.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                            {
                                errorMsg += CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                            }
                            else
                            {
                                errorMsg += CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {

                errorMsg = ex.Message;
            }

            //return errorMsg;
            return new JsonResult(errorMsg);
        }



        #endregion

        #region ::: SelAllBranchTaxCode /Mithun:::
        /// <summary>
        /// to select all the branch Tax Codes
        /// </summary>
        public static IActionResult SelAllBranchTaxCode(SelAllBranchTaxCodeList SelAllBranchTaxCodeObj, string constring, int LogException, string sidx, string sord, int page, int rows)
        {
            try
            {
                List<dynamic> arr = new List<dynamic>();

                // Connect to the database
                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Execute stored procedure to fetch all branch tax codes for the given branchID
                    using (var command = new SqlCommand("Up_Sel_Am_Erp_SelAllBranchTaxCodeDetails", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@BranchID", SelAllBranchTaxCodeObj.branchID);

                        // Execute reader
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                arr.Add(new
                                {
                                    BranchTaxCode_ID = reader["BranchTaxCode_ID"],
                                    edit = $"<a title='Edit' href='#' key='{reader["BranchTaxCode_ID"]}' id='{reader["BranchTaxCode_ID"]}' class='editBranchTaxCode font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                    delete = $"<input type='checkbox' key='{reader["BranchTaxCode_ID"]}' id='chk{reader["BranchTaxCode_ID"]}' class='chkBranchTaxCodeDelete'/>",
                                    BranchTaxCodeName = (reader["BranchTaxCodeName"].ToString()),
                                    BranchTaxCode = (reader["BranchTaxCode"].ToString())
                                });
                            }
                        }
                    }
                }

                // Handle sorting and pagination in-memory
                if (!string.IsNullOrEmpty(sidx) && !string.IsNullOrEmpty(sord))
                {
                    // Sorting logic
                    if (sord.ToUpper() == "ASC")
                        arr = arr.OrderBy(a => a.BranchTaxCodeName).ToList();
                    else
                        arr = arr.OrderByDescending(a => a.BranchTaxCodeName).ToList();
                }

                // Pagination logic
                int totalCount = arr.Count;
                int startIndex = (page - 1) * rows;
                arr = arr.Skip(startIndex).Take(rows).ToList();

                // Prepare response object
                var x = new
                {
                    total = totalCount, // Total count for pagination
                    page = page,
                    records = totalCount,
                    data = arr.ToArray()
                };

                //return Json(x, JsonRequestBehavior.AllowGet);
                return new JsonResult(x);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: SaveBranchTaxCode /Mithun:::
        /// <summary>
        /// to update Branch tax code
        /// </summary>
        public static IActionResult SaveBranchTaxCode(SaveBranchTaxCodeList SaveBranchTaxCodeObj, string constring, int LogException)
        {
            int companyId = Convert.ToInt32(SaveBranchTaxCodeObj.Company_ID);
            try
            {
                string jsonData = SaveBranchTaxCodeObj.Data;
                if (string.IsNullOrEmpty(jsonData))
                {
                    //RedirectToAction("Error");
                    //return;
                    return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
                }

                // Parse JSON data into JObject
                JObject jobj = JObject.Parse(jsonData);

                // Prepare XML data from rows array
                XDocument xmlData = new XDocument(
                    new XElement("rows",
                        jobj["rows"].Select(row =>
                            new XElement("row",
                                new XElement("branchTaxCodeId", row["branchTaxCodeId"] != null ? row["branchTaxCodeId"].ToString() : "0"),
                                new XElement("branchID", jobj["branchID"] != null ? jobj["branchID"].ToString() : "0"),
                                new XElement("taxCodeName", row["taxCodeName"] != null ? row["taxCodeName"].ToString() : ""),
                                new XElement("taxCode", row["taxCode"] != null ? row["taxCode"].ToString() : "")
                            )
                        )
                    )
                );

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Execute stored procedure with XML parameter
                    SqlCommand cmd = new SqlCommand("Up_Ins_Upd_Am_Erp_SaveBranchTaxCode", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    SqlParameter param = new SqlParameter("@XmlData", SqlDbType.Xml);
                    param.Value = xmlData.ToString();
                    cmd.Parameters.Add(param);
                    cmd.ExecuteNonQuery();
                }

                // Log success or perform additional actions after database operation


                //gbl.InsertGPSDetails(
                //    companyId,
                //    Convert.ToInt32(SaveBranchTaxCodeObj.Branch),
                //    Convert.ToInt32(SaveBranchTaxCodeObj.User_ID),
                //    Convert.ToInt32(Common.GetObjectID("CoreCompanyMaster",constring)),
                //    companyId,
                //    0,
                //    0,
                //    "Update",
                //    false,
                //    Convert.ToInt32(SaveBranchTaxCodeObj.MenuID)
                //);
                return new JsonResult(new { Message = "data Saved" }) { StatusCode = 200 };
            }
            catch (WebException wex)
            {
                // Log specific WebException details
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    // Log general exceptions if needed
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }




        #endregion

        #region ::: DeleteBranchTaxCodes /Mithun:::
        /// <summary>
        /// to Delete the Branch Tax Codes
        /// </summary>

        public static IActionResult DeleteBranchTaxCodes(DeleteBranchTaxCodesList DeleteBranchTaxCodesObj, string constring, int LogException)
        {
            string errorMsg = "";
            var Culture = "Resource_" + DeleteBranchTaxCodesObj.Lang;
            try
            {
                string jsonData = DeleteBranchTaxCodesObj.key;
                if (string.IsNullOrEmpty(jsonData))
                {
                    errorMsg = CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "nodataprovided").ToString();
                    //return errorMsg;
                    return new JsonResult(errorMsg);
                }

                JObject jobj = JObject.Parse(jsonData);
                int rowCount = jobj["rows"].Count();
                int companyId = 0;

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlTransaction transaction = conn.BeginTransaction())
                    {
                        try
                        {
                            for (int i = 0; i < rowCount; i++)
                            {
                                int id = (int)jobj["rows"][i]["id"];

                                // Delete the row
                                using (SqlCommand deleteCmd = new SqlCommand("DELETE FROM GNM_BranchTaxCodes WHERE BranchTaxCode_ID = @Id", conn, transaction))
                                {
                                    deleteCmd.Parameters.AddWithValue("@Id", id);
                                    deleteCmd.ExecuteNonQuery();
                                }

                                // Get CompanyID for logging
                                if (i == 0) // Assuming all rows have the same Branch_ID
                                {
                                    using (SqlCommand getCompanyIdCmd = new SqlCommand("SELECT Company_ID FROM GNM_Branch WHERE Branch_ID = (SELECT Branch_ID FROM GNM_BranchTaxCodes WHERE BranchTaxCode_ID = @Id)", conn, transaction))
                                    {
                                        getCompanyIdCmd.Parameters.AddWithValue("@Id", id);
                                        using (SqlDataReader reader = getCompanyIdCmd.ExecuteReader())
                                        {
                                            if (reader.Read())
                                            {
                                                companyId = reader.GetInt32(0);
                                            }
                                        }
                                    }
                                }
                            }

                            transaction.Commit();

                            // Log success or perform additional actions after database operation
                            //gbl.InsertGPSDetails(
                            //    Convert.ToInt32(DeleteBranchTaxCodesObj.Company_ID),
                            //    Convert.ToInt32(DeleteBranchTaxCodesObj.Branch),
                            //    Convert.ToInt32(DeleteBranchTaxCodesObj.User_ID),
                            //    Convert.ToInt32(Common.GetObjectID("CoreCompanyMaster",constring)),
                            //    companyId,
                            //    0,
                            //    0,
                            //    "Delete",
                            //    false,
                            //    Convert.ToInt32(DeleteBranchTaxCodesObj.MenuID)
                            //);

                            errorMsg = CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "deletedsuccessfully").ToString();
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            throw ex; // Rethrow exception to handle it in the outer catch block
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.InnerException != null && ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    errorMsg = CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    errorMsg = CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
            }

            //return errorMsg;
            return new JsonResult(errorMsg);
        }


        #endregion

        #region ::: SelAllEmployeeBranches /not working :::
        /// <summary>
        /// to select all the branches associated with the Employee
        /// </summary>
        public static IActionResult SelAllEmployeeBranches(SelAllEmployeeBranchesList SelAllEmployeeBranchesObj, string constring, int LogException, string sidx, string sord, int page, int rows)
        {
            try
            {
                // CommonFunctionalitiesController CFC = new CommonFunctionalitiesController();
                int userLanguageID = Convert.ToInt32(SelAllEmployeeBranchesObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelAllEmployeeBranchesObj.GeneralLanguageID);
                int count = 0;
                int total = 0;
                List<GNM_EmployeeBranch> employeeBranchList = new List<GNM_EmployeeBranch>();
                int companyID = 0;


                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Count total employee branches
                    string countQuery = "SELECT COUNT(*) FROM GNM_EmployeeBranch WHERE CompanyEmployee_ID = @EmployeeID";
                    using (SqlCommand cmd = new SqlCommand(countQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@EmployeeID", SelAllEmployeeBranchesObj.employeeID);
                        count = (int)cmd.ExecuteScalar();
                    }

                    // Calculate total pages
                    total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                    // Query for employee branches
                    string query = $"SELECT * FROM GNM_EmployeeBranch WHERE CompanyEmployee_ID = @EmployeeID ORDER BY {(sidx == "Branch" ? "Branch_ID" : sidx)} {sord}";
                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@EmployeeID", SelAllEmployeeBranchesObj.employeeID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                GNM_EmployeeBranch employeeBranch = new GNM_EmployeeBranch
                                {
                                    EmployeeBranch_ID = (int)reader["EmployeeBranch_ID"],
                                    CompanyEmployee_ID = (int)reader["CompanyEmployee_ID"],
                                    Branch_ID = (int)reader["Branch_ID"]
                                };
                                employeeBranchList.Add(employeeBranch);
                            }
                        }
                    }

                    // Get company ID
                    string companyQuery = "SELECT Company_ID FROM GNM_CompanyEmployee WHERE Company_Employee_ID = @EmployeeID";
                    using (SqlCommand cmd = new SqlCommand(companyQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@EmployeeID", SelAllEmployeeBranchesObj.employeeID);
                        companyID = (int)cmd.ExecuteScalar();
                    }
                }

                // Load branch names
                var branchArray = CommonFunctionalities.LoadBranch(constring, LogException, true, companyID);
                string branchNames = "-1:--Select--;";

                foreach (var branchObj in branchArray)
                {
                    branchNames += branchObj.Branch_ID + ":" + branchObj.Branch_Name.Replace(";", ":") + ";";
                }
                branchNames = branchNames.TrimEnd(';');

                // Prepare data based on language
                dynamic x = null;
                if (SelAllEmployeeBranchesObj.languageID == generalLanguageID)
                {
                    var arrSkillsList = from a in employeeBranchList
                                        join b in CommonFunctionalities.LoadBranch(constring, LogException, true, companyID) on a.Branch_ID equals b.Branch_ID
                                        select new
                                        {
                                            edit = "<a title='Edit' href='#' key='" + a.EmployeeBranch_ID + "' id='" + a.EmployeeBranch_ID + "' class='editEmployeeBranch font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                            delete = "<input type='checkbox' key='" + a.EmployeeBranch_ID + "' id='chk" + a.EmployeeBranch_ID + "' class='chkEmployeeBranchDelete'/>",
                                            a.EmployeeBranch_ID,
                                            Branch = (b.Branch_Name)
                                        };

                    List<dynamic> arr = new List<dynamic>();
                    for (int i = (page * rows) - rows; i < (page * rows) && i < arrSkillsList.Count(); i++)
                    {
                        arr.Add(arrSkillsList.ToList()[i]);
                    }

                    x = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        data = arr.ToArray(),
                        BranchNames = branchNames
                    };
                }
                else
                {
                    var arrLocaleSkillsList = from a in employeeBranchList
                                              join b in CommonFunctionalities.LoadBranchLocale(constring, LogException, true, companyID, userLanguageID) on a.Branch_ID equals b.Branch_ID
                                              select new
                                              {
                                                  a.EmployeeBranch_ID,
                                                  Branch = b.Branch_Name
                                              };

                    List<dynamic> arr = new List<dynamic>();
                    for (int i = (page * rows) - rows; i < (page * rows) && i < arrLocaleSkillsList.Count(); i++)
                    {
                        arr.Add(arrLocaleSkillsList.ToList()[i]);
                    }

                    x = new
                    {
                        total = total,
                        page = page,
                        records = arrLocaleSkillsList.Count(),
                        data = arr.ToArray()
                    };
                }

                //return Json(x, JsonRequestBehavior.AllowGet);
                return new JsonResult(x);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: SaveEmployeeBranch /Mithun:::
        /// <summary>
        /// to save Employee Branch Association
        /// </summary>
        public static IActionResult SaveEmployeeBranch(SaveEmployeeBranchList SaveEmployeeBranchObj, string constring, int LogException)
        {
            int companyId = Convert.ToInt32(SaveEmployeeBranchObj.Company_ID);
            try
            {
                string jsonData = SaveEmployeeBranchObj.Data;
                if (string.IsNullOrEmpty(jsonData))
                {
                    //RedirectToAction("Error");
                    //return;
                    return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
                }

                // Parse JSON data into JObject
                JObject jobj = JObject.Parse(jsonData);

                // Prepare XML data from rows array
                XDocument xmlData = new XDocument(
                    new XElement("rows",
                        jobj["rows"].Select(row =>
                            new XElement("row",
                                new XAttribute("employeeBranchID", row["employeeBranchID"]?.ToString() ?? "0"),
                                new XElement("CompanyEmployee_ID", jobj["employeeID"]?.ToString() ?? "0"),
                                new XElement("Branch_ID", row["branchID"]?.ToString() ?? "0")
                            )
                        )
                    )
                );

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Execute stored procedure with XML parameter
                    SqlCommand cmd = new SqlCommand("Up_Ins_Upd_Am_Erp_SaveEmployeeBranch", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    SqlParameter param = new SqlParameter("@XmlData", SqlDbType.Xml);
                    param.Value = xmlData.ToString();
                    cmd.Parameters.Add(param);
                    cmd.ExecuteNonQuery();
                }

                // Log success or perform additional actions after database operation
                //gbl.InsertGPSDetails(
                //    companyId,
                //    Convert.ToInt32(SaveEmployeeBranchObj.Branch),
                //    Convert.ToInt32(SaveEmployeeBranchObj.User_ID),
                //    Convert.ToInt32(Common.GetObjectID("CoreCompanyMaster",constring)),
                //    companyId,
                //    0,
                //    0,
                //    "Update",
                //    false,
                //    Convert.ToInt32(SaveEmployeeBranchObj.MenuID)
                //);
                return new JsonResult(new { Message = "data Saved" }) { StatusCode = 200 };
            }
            catch (WebException wex)
            {
                // Log specific WebException details
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    // Log general exceptions if needed
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
        }
        #endregion

        #region ::: DeleteEmployeeBranch /Mithun:::
        /// <summary>
        /// to Delete the Employee Branch details
        /// </summary>
        public static IActionResult DeleteEmployeeBranch(DeleteEmployeeBranchList DeleteEmployeeBranchObj, string constring, int LogException)
        {
            string errorMsg = "";
            var Culture = "Resource_" + DeleteEmployeeBranchObj.Lang;
            try
            {
                JObject jobj = JObject.Parse(DeleteEmployeeBranchObj.key);
                int rowCount = jobj["rows"].Count();
                int id = 0;

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open(); // Connection will be opened here

                    for (int i = 0; i < rowCount; i++)
                    {
                        id = jobj["rows"][i]["id"].ToObject<int>();

                        using (SqlCommand command = new SqlCommand())
                        {
                            command.Connection = connection;
                            command.CommandText = "DELETE FROM GNM_EmployeeBranch WHERE EmployeeBranch_ID = @id";
                            command.Parameters.AddWithValue("@id", id);
                            command.ExecuteNonQuery();
                        }
                    }


                }

                errorMsg += CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (SqlException ex)
            {
                if (ex.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    errorMsg += CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    errorMsg += ex.Message;
                }
            }
            catch (Exception ex)
            {
                errorMsg += ex.Message;
            }

            //return errorMsg;
            return new JsonResult(errorMsg);
        }


        #endregion

        #region ::: CheckEmployeeBranch /Mithun:::
        /// <summary>
        /// to check if the employee is already associated with the branch
        /// </summary>
        public static IActionResult CheckEmployeeBranch(CheckEmployeeBranchList CheckEmployeeBranchObj, string constring, int LogException)
        {
            int status = 0;

            try
            {

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand("Up_Chk_Am_Erp_CheckEmployeeBranch", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // Parameters for the stored procedure
                        command.Parameters.AddWithValue("@BranchID", CheckEmployeeBranchObj.branchID);
                        command.Parameters.AddWithValue("@EmployeeID", CheckEmployeeBranchObj.employeeID);
                        command.Parameters.AddWithValue("@PrimaryKey", CheckEmployeeBranchObj.primaryKey);

                        // Output parameter for status
                        SqlParameter statusParameter = new SqlParameter("@Status", SqlDbType.Int);
                        statusParameter.Direction = ParameterDirection.Output;
                        command.Parameters.Add(statusParameter);

                        command.ExecuteNonQuery();

                        // Retrieve output parameter value
                        status = Convert.ToInt32(statusParameter.Value);
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                // Handle SQL exceptions
                LS.LogSheetExporter.LogToTextFile(sqlEx.HResult, sqlEx.Message, sqlEx.TargetSite.ToString(), sqlEx.StackTrace);
            }
            catch (Exception ex)
            {
                // Handle other exceptions
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return status;
            return new JsonResult(status);
        }

        #endregion

        #region ::: CheckCompanyName /Mithun:::
        /// <summary>
        /// to check if the Name is already used
        /// </summary>
        public static IActionResult CheckCompanyName(CheckCompanyNameList CheckCompanyNameObj, string constring, int LogException)
        {
            int status = 0;
            try
            {
                string companyName = Common.DecryptString(CheckCompanyNameObj.companyName);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    using (SqlCommand cmd = new SqlCommand("Up_Chk_Am_Erp_CheckCompanyName", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@CompanyName", companyName);
                        cmd.Parameters.AddWithValue("@PrimaryKey", CheckCompanyNameObj.primaryKey);

                        SqlParameter statusParam = new SqlParameter("@Status", SqlDbType.Int);
                        statusParam.Direction = ParameterDirection.Output;
                        cmd.Parameters.Add(statusParam);

                        conn.Open();
                        cmd.ExecuteNonQuery();

                        status = (int)statusParam.Value;
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            //return status;
            return new JsonResult(status);
        }

        #endregion

        #region ::: CheckBranchName /Mihtun:::
        /// <summary>
        /// to check if the Name is already used
        /// </summary>
        public static IActionResult CheckBranchName(CheckBranchNameList CheckBranchNameObj, string constring, int LogException)
        {
            int status = 0;

            try
            {
                string branchName = Common.DecryptString(CheckBranchNameObj.branchName);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Chk_Am_Erp_CheckBranchName", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@BranchName", branchName);
                        cmd.Parameters.AddWithValue("@CompanyID", CheckBranchNameObj.companyID);
                        cmd.Parameters.AddWithValue("@PrimaryKey", CheckBranchNameObj.primaryKey);

                        object result = cmd.ExecuteScalar();
                        if (result != null && Convert.ToInt32(result) > 0)
                        {
                            status = 1;
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }

            //return status;
            return new JsonResult(status);
        }


        #endregion

        #region ::: GetBranchCount /Mithun:::
        /// <summary>
        /// to check if the Name is already used
        /// </summary>

        public static IActionResult GetBranchCount(GetBranchCountList GetBranchCountObj, string constring, int LogException)
        {
            int status = 0;

            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("Up_Count_Am_Erp_GetCompanyBranchCount", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", GetBranchCountObj.companyID);

                        object result = cmd.ExecuteScalar();
                        if (result != null && Convert.ToInt32(result) > 0)
                        {
                            status = 1;
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }

            //return status;
            return new JsonResult(status);
        }


        #endregion

        #region ::: CheckEmployeeID /Mithun:::
        /// <summary>
        /// to check if the Name is already used
        /// </summary>

        public static IActionResult CheckEmployeeID(CheckEmployeeIDList CheckEmployeeIDObj, string constring, int LogException)
        {
            int status = 0;
            try
            {
                string employeeID = Common.DecryptString(CheckEmployeeIDObj.employeeID);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    using (SqlCommand cmd = new SqlCommand("Up_Chk_Am_Erp_CheckEmployeeID", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@EmployeeID", employeeID);
                        cmd.Parameters.AddWithValue("@CompanyID", CheckEmployeeIDObj.companyID);
                        cmd.Parameters.AddWithValue("@PrimaryKey", CheckEmployeeIDObj.primaryKey);

                        SqlParameter statusParam = new SqlParameter("@Status", SqlDbType.Int);
                        statusParam.Direction = ParameterDirection.Output;
                        cmd.Parameters.Add(statusParam);

                        conn.Open();
                        cmd.ExecuteNonQuery();

                        status = (int)statusParam.Value;
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            //return status;
            return new JsonResult(status);
        }

        #endregion

        #region ::: CheckLocaleCompanyName /Mithun:::
        /// <summary>
        /// to check if the Name is already used
        /// </summary>
        public static IActionResult CheckLocaleCompanyName(CheckLocaleCompanyNameList CheckLocaleCompanyNameObj, string contring, int LogException)
        {
            int status = 0;
            try
            {
                string companyName = Common.DecryptString(CheckLocaleCompanyNameObj.companyName);
                int userLanguageID = Convert.ToInt32(CheckLocaleCompanyNameObj.UserLanguageID);

                using (SqlConnection conn = new SqlConnection(contring))
                {
                    using (SqlCommand cmd = new SqlCommand("Up_Chk_Am_Erp_CheckLocaleCompanyName", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyName", companyName);
                        cmd.Parameters.AddWithValue("@PrimaryKey", CheckLocaleCompanyNameObj.primaryKey);
                        cmd.Parameters.AddWithValue("@UserLanguageID", userLanguageID);

                        conn.Open();
                        object result = cmd.ExecuteScalar();

                        if (result != null)
                        {
                            status = 1;
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            //return status;
            return new JsonResult(status);
        }
        #endregion

        #region ::: CheckLocaleBranchName :::
        /// <summary>
        /// to check if the Name is already used
        /// </summary>
        public static IActionResult CheckLocaleBranchName(CheckLocaleBranchNameList CheckLocaleBranchNameObj, string constring, int LogException)
        {
            int status = 0;
            try
            {
                string branchName = Common.DecryptString(CheckLocaleBranchNameObj.branchName);
                int userLanguageID = Convert.ToInt32(CheckLocaleBranchNameObj.UserLanguageID);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand("Up_Chk_Am_Erp_CheckLocaleBranchName", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.AddWithValue("@BranchName", branchName);
                        command.Parameters.AddWithValue("@CompanyID", CheckLocaleBranchNameObj.companyID);
                        command.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                        command.Parameters.AddWithValue("@PrimaryKey", CheckLocaleBranchNameObj.primaryKey);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                status = reader.GetInt32(reader.GetOrdinal("Status"));
                            }
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            //return status;
            return new JsonResult(status);
        }


        #endregion

        #region ::: SelectAllEmployeeMastersData /Mithun:::
        /// <summary>
        /// to select masters for drop down
        /// </summary>
        public static IActionResult SelectAllEmployeeMastersData(SelectAllEmployeeMastersDataList SelectAllEmployeeMastersDataObj, string constring, int LogException)
        {
            var countryArray = new List<dynamic>();
            var stateArray = new List<dynamic>();
            var departmentArray = new List<dynamic>();
            var designationArray = new List<dynamic>();
            var managerArray = new List<dynamic>();

            try
            {
                int userLanguageID = Convert.ToInt32(SelectAllEmployeeMastersDataObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectAllEmployeeMastersDataObj.GeneralLanguageID);


                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetAllEmployeeMastersData", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@LanguageID", userLanguageID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);
                        cmd.Parameters.AddWithValue("@CountryID", SelectAllEmployeeMastersDataObj.countryID);
                        cmd.Parameters.AddWithValue("@CompanyID", SelectAllEmployeeMastersDataObj.companyID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            do
                            {
                                while (reader.Read())
                                {
                                    string resultType = reader.GetString(0);

                                    switch (resultType)
                                    {
                                        case "COUNTRY":
                                            countryArray.Add(new
                                            {
                                                RefMasterDetail_ID = reader.GetInt32(1),
                                                RefMasterDetail_Name = reader.GetString(2)
                                            });
                                            break;
                                        case "STATE":
                                            stateArray.Add(new
                                            {
                                                State_id = reader.GetInt32(1),
                                                State_Name = reader.GetString(2)
                                            });
                                            break;
                                        case "DEPARTMENT":
                                            departmentArray.Add(new
                                            {
                                                RefMasterDetail_ID = reader.GetInt32(1),
                                                RefMasterDetail_Name = reader.GetString(2)
                                            });
                                            break;
                                        case "DESIGNATION":
                                            designationArray.Add(new
                                            {
                                                RefMasterDetail_ID = reader.GetInt32(1),
                                                RefMasterDetail_Name = reader.GetString(2)
                                            });
                                            break;
                                        case "MANAGER":
                                            managerArray.Add(new
                                            {
                                                Company_Employee_ID = reader.GetInt32(1),
                                                Company_Employee_Name = reader.GetString(2)
                                            });
                                            break;
                                    }
                                }
                            } while (reader.NextResult());
                        }
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                LS.LogSheetExporter.LogToTextFile(sqlEx.HResult, sqlEx.Message, sqlEx.TargetSite.ToString(), sqlEx.StackTrace);
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }

            var jsonResult = new
            {
                countryArray = countryArray,
                stateArray = stateArray,
                departmentArray = departmentArray,
                designationArray = designationArray,
                managerArray = managerArray
            };

            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);
        }


        #endregion

        #region ::: SelectOnlyCompanyMastersData /Mihtun:::
        /// <summary>
        /// to select masters for drop down
        /// </summary>
        public static IActionResult SelectOnlyCompanyMastersData(SelectOnlyCompanyMastersDataList SelectOnlyCompanyMastersDataObj, string constring, int LogException)
        {
            var themeArray = default(dynamic);
            var currencyArray = default(dynamic);
            var parentCompanyArray = default(dynamic);
            var FontArray = default(dynamic);
            var CostingArray = default(dynamic);

            try
            {
                int CompanyID = Convert.ToInt32(SelectOnlyCompanyMastersDataObj.Company_ID);
                int userLanguageID = Convert.ToInt32(SelectOnlyCompanyMastersDataObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectOnlyCompanyMastersDataObj.GeneralLanguageID);
                //GNM_User User = (GNM_User)Session["UserDetails"];
                // GNM_User User = SelectOnlyCompanyMastersDataObj.UserDetails.FirstOrDefault();
                //int companyID = SelectOnlyCompanyMastersDataObj.Company_ID;

                List<string> FontCollections = new List<string>();
                InstalledFontCollection fonts = new InstalledFontCollection();

                InstalledFontCollection ifc = new InstalledFontCollection();
                System.Drawing.FontFamily[] ffs = ifc.Families;

                for (int i = 0; i < fonts.Families.Length; i++)
                {
                    FontCollections.Add(fonts.Families[i].Name);
                }

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    if (SelectOnlyCompanyMastersDataObj.languageID == generalLanguageID)
                    {
                        // Retrieve themeArray
                        themeArray = GetThemeArray(conn, "COMPANYTHEME", CompanyID, userLanguageID, generalLanguageID);

                        // Retrieve currencyArray
                        currencyArray = GetCurrencyArray(conn, "CURRENCY", CompanyID, userLanguageID, generalLanguageID);

                        // Retrieve parentCompanyArray
                        parentCompanyArray = GetParentCompanyArray(conn);

                        // Retrieve FontArray
                        FontArray = fonts.Families.Select(a => new { a.Name }).ToList();

                        // Retrieve CostingArray
                        CostingArray = GetCostingArray(conn, "OrderingCostMaster", CompanyID, userLanguageID, generalLanguageID);
                    }
                    else
                    {
                        // Retrieve themeArray
                        themeArray = GetThemeArray(conn, "COMPANYTHEME", CompanyID, userLanguageID, generalLanguageID, false);

                        // Retrieve currencyArray
                        currencyArray = GetCurrencyArray(conn, "CURRENCY", CompanyID, userLanguageID, generalLanguageID, false);

                        // Retrieve parentCompanyArray
                        parentCompanyArray = GetParentCompanyLocaleArray(conn, userLanguageID);

                        // Retrieve FontArray
                        FontArray = fonts.Families.Select(a => new { a.Name }).ToList();

                        // Retrieve CostingArray
                        CostingArray = GetCostingArray(conn, "OrderingCostMaster", CompanyID, userLanguageID, generalLanguageID, false);
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }

            var jsonResult = new
            {
                currencyArray = currencyArray,
                themeArray = themeArray,
                parentCompanyArray = parentCompanyArray,
                FontArray,
                CostingArray
            };
            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);
        }

        private static dynamic GetThemeArray(SqlConnection conn, string refMasterName, int companyID, int userLanguageID, int generalLanguageID, bool isGeneral = true)
        {
            string query = isGeneral
                ? "SELECT b.RefMasterDetail_ID, b.RefMasterDetail_Name FROM GNM_RefMaster a INNER JOIN GNM_RefMasterDetail b ON a.RefMaster_ID = b.RefMaster_ID WHERE a.RefMaster_Name = @RefMasterName AND b.RefMasterDetail_IsActive = 1 ORDER BY b.RefMasterDetail_Name"
                : "SELECT b.RefMasterDetail_ID, b.RefMasterDetail_Name FROM GNM_RefMaster a INNER JOIN GNM_RefMasterDetailLocale b ON a.RefMaster_ID = b.RefMaster_ID WHERE a.RefMaster_Name = @RefMasterName AND b.Language_ID = @UserLanguageID ORDER BY b.RefMasterDetail_Name";

            using (SqlCommand cmd = new SqlCommand(query, conn))
            {
                cmd.Parameters.AddWithValue("@RefMasterName", refMasterName);
                cmd.Parameters.AddWithValue("@UserLanguageID", userLanguageID);

                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    var result = new List<dynamic>();
                    while (reader.Read())
                    {
                        result.Add(new
                        {
                            RefMasterDetail_ID = reader.GetInt32(0),
                            RefMasterDetail_Name = reader.GetString(1)
                        });
                    }
                    return result;
                }
            }
        }

        private static dynamic GetCurrencyArray(SqlConnection conn, string refMasterName, int companyID, int userLanguageID, int generalLanguageID, bool isGeneral = true)
        {
            string query = isGeneral
    ? "SELECT b.RefMasterDetail_ID, b.RefMasterDetail_Name FROM GNM_RefMaster a INNER JOIN GNM_RefMasterDetail b ON a.RefMaster_ID = b.RefMaster_ID WHERE a.RefMaster_Name = @RefMasterName AND b.RefMasterDetail_IsActive = 1 AND (a.IsCompanySpecific = 0 OR b.Company_ID = @CompanyID) ORDER BY b.RefMasterDetail_Name"
    : "SELECT b.RefMasterDetail_ID, b.RefMasterDetail_Name FROM GNM_RefMaster a INNER JOIN GNM_RefMasterDetail c ON a.RefMaster_ID = c.RefMaster_ID INNER JOIN GNM_RefMasterDetailLocale b ON a.RefMaster_ID = b.RefMaster_ID WHERE a.RefMaster_Name = @RefMasterName AND b.Language_ID = @UserLanguageID AND c.RefMasterDetail_IsActive = 1 AND (a.IsCompanySpecific = 0 OR c.Company_ID = @CompanyID) ORDER BY b.RefMasterDetail_Name";

            using (SqlCommand cmd = new SqlCommand(query, conn))
            {
                cmd.Parameters.AddWithValue("@RefMasterName", refMasterName);
                cmd.Parameters.AddWithValue("@CompanyID", companyID);
                cmd.Parameters.AddWithValue("@UserLanguageID", userLanguageID);

                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    var result = new List<dynamic>();
                    while (reader.Read())
                    {
                        result.Add(new
                        {
                            RefMasterDetail_ID = reader.GetInt32(0),
                            RefMasterDetail_Name = reader.GetString(1)
                        });
                    }
                    return result;
                }
            }
        }

        private static dynamic GetParentCompanyArray(SqlConnection conn)
        {
            string query = "SELECT Company_ID, Company_Name FROM GNM_Company WHERE Company_Active = 1 ORDER BY Company_Name";

            using (SqlCommand cmd = new SqlCommand(query, conn))
            {
                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    var result = new List<dynamic>();
                    while (reader.Read())
                    {
                        result.Add(new
                        {
                            Company_ID = reader.GetInt32(0),
                            Company_Name = reader.GetString(1)
                        });
                    }
                    return result;
                }
            }
        }

        private static dynamic GetParentCompanyLocaleArray(SqlConnection conn, int userLanguageID)
        {
            string query = "SELECT Company_ID, Company_Name FROM GNM_CompanyLocale WHERE Language_ID = @UserLanguageID ORDER BY Company_Name";

            using (SqlCommand cmd = new SqlCommand(query, conn))
            {
                cmd.Parameters.AddWithValue("@UserLanguageID", userLanguageID);

                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    var result = new List<dynamic>();
                    while (reader.Read())
                    {
                        result.Add(new
                        {
                            Company_ID = reader.GetInt32(0),
                            Company_Name = reader.GetString(1)
                        });
                    }
                    return result;
                }
            }
        }

        private static dynamic GetCostingArray(SqlConnection conn, string refMasterName, int companyID, int userLanguageID, int generalLanguageID, bool isGeneral = true)
        {
            string query = isGeneral
                ? "SELECT b.RefMasterDetail_ID, b.RefMasterDetail_Name FROM GNM_RefMaster a INNER JOIN GNM_RefMasterDetail b ON a.RefMaster_ID = b.RefMaster_ID WHERE a.RefMaster_Name = @RefMasterName AND b.RefMasterDetail_IsActive = 1 ORDER BY b.RefMasterDetail_Name"
                : "SELECT b.RefMasterDetail_ID, b.RefMasterDetail_Name FROM GNM_RefMaster a INNER JOIN GNM_RefMasterDetailLocale b ON a.RefMaster_ID = b.RefMaster_ID WHERE a.RefMaster_Name = @RefMasterName AND b.Language_ID = @UserLanguageID ORDER BY b.RefMasterDetail_Name";

            using (SqlCommand cmd = new SqlCommand(query, conn))
            {
                cmd.Parameters.AddWithValue("@RefMasterName", refMasterName);
                cmd.Parameters.AddWithValue("@UserLanguageID", userLanguageID);

                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    var result = new List<dynamic>();
                    while (reader.Read())
                    {
                        result.Add(new
                        {
                            RefMasterDetail_ID = reader.GetInt32(0),
                            RefMasterDetail_Name = reader.GetString(1)
                        });
                    }
                    return result;
                }
            }
        }

        #endregion

        #region ::: SelectBranchMastersData /Mithun:::
        /// <summary>
        /// to select masters for drop down
        /// </summary>
        public static IActionResult SelectBranchMastersData(SelectBranchMastersDataList SelectBranchMastersDataObj, string constring, int LogException)
        {
            var countryArray = new List<dynamic>();
            var stateArray = new List<dynamic>();
            var timeZoneArray = new List<dynamic>();
            var currencyBrArray = new List<dynamic>();
            var payrollFileTypeArray = new List<dynamic>();

            try
            {
                int companyID = Convert.ToInt32(SelectBranchMastersDataObj.Company_ID);
                int userLanguageID = Convert.ToInt32(SelectBranchMastersDataObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectBranchMastersDataObj.GeneralLanguageID);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Country array
                    using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_SelectBranchMastersDataGetCountries", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@IsActive", true);
                        cmd.Parameters.AddWithValue("@LanguageID", userLanguageID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                countryArray.Add(new
                                {
                                    RefMasterDetail_ID = reader["RefMasterDetail_ID"],
                                    RefMasterDetail_Name = reader["RefMasterDetail_Name"]
                                });
                            }
                        }
                    }

                    // State array
                    using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_SelectBranchMastersDataGetStates", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CountryID", SelectBranchMastersDataObj.countryID);
                        cmd.Parameters.AddWithValue("@IsActive", true);
                        cmd.Parameters.AddWithValue("@LanguageID", userLanguageID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                stateArray.Add(new
                                {
                                    State_id = reader["State_ID"],
                                    State_Name = reader["State_Name"]
                                });
                            }
                        }
                    }

                    // Time zone array
                    using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_SelectBranchMastersDataGetTimeZones", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@IsActive", true);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                timeZoneArray.Add(new
                                {
                                    RefMasterDetail_ID = reader["RefMasterDetail_ID"],
                                    RefMasterDetail_Name = reader["RefMasterDetail_Name"]
                                });
                            }
                        }
                    }

                    // Currency array
                    using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_SelectBranchMastersDataGetCurrencies", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);
                        cmd.Parameters.AddWithValue("@IsActive", true);
                        cmd.Parameters.AddWithValue("@LanguageID", userLanguageID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                currencyBrArray.Add(new
                                {
                                    RefMasterDetail_ID = reader["RefMasterDetail_ID"],
                                    RefMasterDetail_Name = reader["RefMasterDetail_Name"]
                                });
                            }
                        }
                    }

                    // Payroll file type array
                    using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_SelectBranchMastersDataGetPayRollFileTypes", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@IsActive", true);
                        cmd.Parameters.AddWithValue("@LanguageID", userLanguageID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                payrollFileTypeArray.Add(new
                                {
                                    RefMasterDetail_ID = reader["RefMasterDetail_ID"],
                                    RefMasterDetail_Name = reader["RefMasterDetail_Name"]
                                });
                            }
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }

            var jsonResult = new
            {
                countryArray = countryArray,
                stateArray = stateArray,
                timeZoneArray = timeZoneArray,
                currencyBrArray = currencyBrArray,
                PayrollFileTypeArray = payrollFileTypeArray
            };

            System.Diagnostics.Debug.WriteLine(JsonConvert.SerializeObject(jsonResult));

            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);
        }

        #endregion

        #region ::: CheckBranchTaxDetails /Mihtun:::
        /// <summary>
        /// to check if the Tax detail has already been selected for the branch
        /// </summary>
        public static IActionResult CheckOrderingCostofParent(CheckOrderingCostofParentList CheckOrderingCostofParentObj, string constring, int LogException)
        {
            int status = 0;

            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Define the SQL query to get the OrderingCost
                    string query = "SELECT OrderingCost FROM GNM_Company WHERE Company_ID = @Parent_ID";

                    using (SqlCommand cmd = new SqlCommand(query, connection))
                    {
                        cmd.Parameters.AddWithValue("@Parent_ID", CheckOrderingCostofParentObj.Parent_ID);

                        int? OrderingCostID = cmd.ExecuteScalar() as int?;

                        if (OrderingCostID != CheckOrderingCostofParentObj.SelOrderCost_ID)
                        {
                            status = 1;
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                // Handle other exceptions here if needed
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }

            //return status;
            return new JsonResult(status);
        }

        #endregion

        #region ::: Select release notes info /Mithun:::
        /// <summary>
        ///  to Select release notes info
        /// </summary>
        public static IActionResult SelectReleaseNoteInformation(SelectReleaseNoteInformationList SelectReleaseNoteInformationObj, string constring, int LogException, string sidx, string sord, int page, int rows)
        {
            var jsonResult = default(dynamic);
            int userLanguageID = Convert.ToInt32(SelectReleaseNoteInformationObj.UserLanguageID);
            int generalLanguageID = Convert.ToInt32(SelectReleaseNoteInformationObj.GeneralLanguageID);
            int BranchID = Convert.ToInt32(SelectReleaseNoteInformationObj.Branch);

            try
            {
                List<ReleaseNoteInformation> releaseNotes = new List<ReleaseNoteInformation>();

                // Database connection
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Command to execute stored procedure
                    SqlCommand command = new SqlCommand("Up_Sel_Am_Erp_SelectReleaseNoteInformation", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    // Parameters for the stored procedure
                    command.Parameters.AddWithValue("@Sidx", sidx);
                    command.Parameters.AddWithValue("@Sord", sord);
                    command.Parameters.AddWithValue("@Page", page);
                    command.Parameters.AddWithValue("@Rows", rows);
                    command.Parameters.AddWithValue("@BranchID", BranchID);
                    command.Parameters.AddWithValue("@AppPathString", SelectReleaseNoteInformationObj.AppPathString.ToString());

                    // Execute the command
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            ReleaseNoteInformation releaseNote = new ReleaseNoteInformation
                            {
                                ReleaseNoteInfo_ID = reader.GetInt32(0),
                                ReleaseVersion = reader.GetString(1),
                                ReleaseNoteFileName = reader.GetString(2),
                                ReleaseDateStr = reader.IsDBNull(3) ? string.Empty : reader.GetString(3),
                                ReleaseDate = reader.IsDBNull(4) ? DateTime.MinValue : reader.GetDateTime(4)
                            };
                            releaseNotes.Add(releaseNote);
                        }
                    }
                }

                // Construct JSON result
                int totalRecords = releaseNotes.Count;
                int totalPages = rows > 0 ? (int)Math.Ceiling((double)totalRecords / rows) : 0;

                jsonResult = new
                {
                    total = totalPages,
                    page = page,
                    records = totalRecords,
                    data = releaseNotes
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);
        }

        #endregion

        #region ::: GetNextReleaseInfo /Mithun:::
        /// <summary>
        /// GetNextReleaseInfo
        /// </summary>
        public static IActionResult GetNextReleaseInfo(GetNextReleaseInfoList GetNextReleaseInfoObj, string constring, int LogException)
        {
            var jsonResult = default(dynamic);
            int CompanyID = Convert.ToInt32(GetNextReleaseInfoObj.Company_ID);
            int BranchID = Convert.ToInt32(GetNextReleaseInfoObj.Branch);

            try
            {
                string FromTime = "";
                string ToTime = "";
                bool IsVisible = false;
                DateTime? ReleaseDate = null;
                DateTime? ActualDate = null;
                string ReleaseInfo = null;

                // Using ADO.NET to execute the stored procedure
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand("Up_Get_Am_Erp_GetNextReleaseInformation", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.AddWithValue("@CompanyID", CompanyID);
                        command.Parameters.AddWithValue("@ParamName", "NextReleaseInformation");

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                ReleaseInfo = reader["ReleaseInfo"].ToString();
                            }
                        }
                    }
                }

                if (!string.IsNullOrEmpty(ReleaseInfo))
                {
                    int Year = Convert.ToDateTime(ReleaseInfo.Split(' ')[0]).Year;
                    int Month = Convert.ToDateTime(ReleaseInfo.Split(' ')[0]).Month;
                    int Day = Convert.ToDateTime(ReleaseInfo.Split(' ')[0]).Day;
                    var date1 = new DateTime(Year, Month, Day);

                    TimeSpan StartTime = TimeSpan.Parse(Convert.ToDateTime(ReleaseInfo.Split(' ')[2] + " " + ReleaseInfo.Split(' ')[3]).ToString("HH:mm:ss"));
                    var ReleaseStartTime = Convert.ToDateTime(date1.ToString().Split(' ')[0] + " " + StartTime);

                    TimeSpan EndTime = TimeSpan.Parse(Convert.ToDateTime(ReleaseInfo.Split(' ')[5] + " " + ReleaseInfo.Split(' ')[6]).ToString("HH:mm:ss"));
                    var ReleaseEndTime = Convert.ToDateTime(date1.ToString().Split(' ')[0] + " " + EndTime);
                    DateTime EndDate = Common.LocalTime(BranchID, Convert.ToDateTime(ReleaseEndTime), constring);

                    ReleaseDate = SubtractBusinessDays(Common.LocalTime(BranchID, Convert.ToDateTime(ReleaseStartTime), constring), 3);
                    ActualDate = Common.LocalTime(BranchID, Convert.ToDateTime(ReleaseStartTime), constring);
                    FromTime = ActualDate.Value.ToString("hh:mm tt");
                    ToTime = EndDate.ToString("hh:mm tt");
                    var CurrentDate = Common.LocalTime(BranchID, DateTime.Now, constring);
                    if (ReleaseDate <= CurrentDate && ActualDate >= CurrentDate)
                    {
                        IsVisible = true;
                    }
                }

                jsonResult = new
                {
                    FromTime,
                    ToTime,
                    ReleaseDate = ReleaseDate.HasValue ? Convert.ToDateTime(ReleaseDate).ToString("dd-MMM-yyyy") : "",
                    ReleaseInfo,
                    ActualDate = ActualDate.HasValue ? Convert.ToDateTime(ActualDate).ToString("dd-MMM-yyyy") : "",
                    IsVisible
                };
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
                //RedirectToAction("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
                //RedirectToAction("Error");
            }

            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);
        }




        #endregion

        #region :::SelectMonthEndLog /Mithun:::
        /// <summary>
        /// SelectMonthEndLog
        /// </summary>
        /// <returns></returns>
        public static IActionResult SelectMonthEndLog(SelectMonthEndLogList SelectMonthEndLogObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters)
        {
            var jsonResult = default(dynamic);
            int userLanguageID = Convert.ToInt32(SelectMonthEndLogObj.UserLanguageID);
            int generalLanguageID = Convert.ToInt32(SelectMonthEndLogObj.GeneralLanguageID);
            int BranchID = Convert.ToInt32(SelectMonthEndLogObj.Branch);

            try
            {
                IEnumerable<MonthEndLog> IEReleaseNoteList = null;
                IQueryable<MonthEndLog> IQReleaseNoteList = null;
                List<MonthEndLog> ReleaseNoteList = new List<MonthEndLog>();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    string query = "SELECT MonthEndReportLog_ID, MonthName, MonthEnd_From_Date, MonthEnd_To_Date, Year, StartedBy_Nm, StartedDateTime, CompletedBy_Nm, CompletedDateTime, Remarks FROM GNM_MonthEndReportLog";
                    SqlCommand cmd = new SqlCommand(query, conn);
                    SqlDataReader reader = cmd.ExecuteReader();

                    while (reader.Read())
                    {
                        ReleaseNoteList.Add(new MonthEndLog
                        {
                            MonthEndReportLog_ID = reader.GetInt32(reader.GetOrdinal("MonthEndReportLog_ID")),
                            Month = reader.GetString(reader.GetOrdinal("MonthName")),
                            MonthEnd_From_Date = reader.GetDateTime(reader.GetOrdinal("MonthEnd_From_Date")),
                            MonthEnd_To_Date = reader.GetDateTime(reader.GetOrdinal("MonthEnd_To_Date")),
                            Year = reader.GetInt32(reader.GetOrdinal("Year")),
                            StartedBy_Nm = reader.GetString(reader.GetOrdinal("StartedBy_Nm")),
                            StartedDateTime = reader.GetDateTime(reader.GetOrdinal("StartedDateTime")),
                            CompletedBy_Nm = reader.GetString(reader.GetOrdinal("CompletedBy_Nm")),
                            CompletedDateTime = reader.IsDBNull(reader.GetOrdinal("CompletedDateTime")) ? Convert.ToDateTime("1900-01-01") : reader.GetDateTime(reader.GetOrdinal("CompletedDateTime")),
                            Remarks = reader.GetString(reader.GetOrdinal("Remarks"))
                        });
                    }
                }

                IEReleaseNoteList = ReleaseNoteList;
                IQReleaseNoteList = IEReleaseNoteList.AsQueryable();

                //if (Request.Params["_search"] == "true")
                //{
                //    Filters filters = JObject.Parse(Common.DecryptString(Request.Params["filters"])).ToObject<Filters>();
                //    if (filters.rules.Count > 0)
                //        IQReleaseNoteList = IQReleaseNoteList.FilterSearch<MonthEndLog>(filters);
                //}
                //else if (Request.Params["advnce"] == "true")
                //{
                //    AdvanceFilter advnfilter = JObject.Parse(Request.Params["Query"]).ToObject<AdvanceFilter>();
                //    IQReleaseNoteList = IQReleaseNoteList.AdvanceSearch<MonthEndLog>(advnfilter);
                //}

                IQReleaseNoteList = IQReleaseNoteList.OrderByField<MonthEndLog>(sidx, sord);
                int count = IQReleaseNoteList.Count();
                int total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                jsonResult = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = (from a in IQReleaseNoteList
                            select new
                            {
                                MonthEndReportLog_ID = a.MonthEndReportLog_ID,
                                Month = a.Month,
                                MonthEnd_From_Date = a.MonthEnd_From_Date.ToString("dd-MMM-yyyy hh:mm tt"),
                                MonthEnd_To_Date = a.MonthEnd_To_Date.ToString("dd-MMM-yyyy hh:mm tt"),
                                Year = a.Year,
                                StartedBy_Nm = a.StartedBy_Nm,
                                StartedDateTime = a.StartedDateTime.ToString("dd-MMM-yyyy hh:mm tt"),
                                CompletedBy_Nm = a.CompletedBy_Nm,
                                CompletedDateTime = a.CompletedDateTime != Convert.ToDateTime("1900-01-01") ? Convert.ToDateTime(a.CompletedDateTime).ToString("dd-MMM-yyyy hh:mm tt") : "",
                                Remarks = a.Remarks
                            }).ToList().Paginate(page, rows)
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);
        }


        #endregion

        public static DateTime AddBusinessDays(DateTime current, int nDays)
        {
            var sign = Math.Sign(nDays);
            var Unsign = Math.Abs(nDays);
            for (int i = 0; i < Unsign; i++)
            {
                do
                {
                    current = current.AddDays(sign);
                }
                while (current.DayOfWeek == DayOfWeek.Saturday || current.DayOfWeek == DayOfWeek.Sunday);
            }
            return current;
        }
        public static DateTime SubtractBusinessDays(DateTime current, int days)
        {
            return AddBusinessDays(current, -days);
        }




        #region ::: data for Export :::

        public IQueryable<CompanyData> GetCompanyData(string constring, int userLanguageID, int generalLanguageID)
        {
            List<CompanyData> companyList = new List<CompanyData>();

            using (SqlConnection conn = new SqlConnection(constring))
            {
                conn.Open();
                string query = @"SELECT a.*, b.RefMasterDetail_Name AS CurrencyName, th.RefMasterDetail_Name AS ThemeName
                         FROM GNM_Company a
                         LEFT JOIN GNM_RefMasterDetail b ON a.Currency_ID = b.RefMasterDetail_ID
                         LEFT JOIN GNM_RefMasterDetail th ON a.CompanyTheme_ID = th.RefMasterDetail_ID";

                using (SqlCommand cmd = new SqlCommand(query, conn))
                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        companyList.Add(new CompanyData
                        {
                            Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                            Company_Name = reader.IsDBNull(reader.GetOrdinal("Company_Name")) ? string.Empty : reader.GetString(reader.GetOrdinal("Company_Name")),
                            Type = reader.IsDBNull(reader.GetOrdinal("Company_Type")) ? "Dealer" : (reader.GetString(reader.GetOrdinal("Company_Type")) == "M" ? "Manufacturer" : "Dealer"),
                            ShortName = reader.IsDBNull(reader.GetOrdinal("Company_ShortName")) ? string.Empty : reader.GetString(reader.GetOrdinal("Company_ShortName")),
                            ParentCompany = reader.IsDBNull(reader.GetOrdinal("Company_Parent_ID")) ? string.Empty : GetParentCompanyName(Convert.ToInt32(reader["Company_Parent_ID"]), constring),
                            LogoName = reader.IsDBNull(reader.GetOrdinal("Company_LogoName")) ? string.Empty : reader.GetString(reader.GetOrdinal("Company_LogoName")),
                            Address = reader.IsDBNull(reader.GetOrdinal("Company_Address")) ? string.Empty : reader.GetString(reader.GetOrdinal("Company_Address")),
                            Currency = reader.IsDBNull(reader.GetOrdinal("CurrencyName")) ? string.Empty : reader.GetString(reader.GetOrdinal("CurrencyName")),
                            Remarks = reader.IsDBNull(reader.GetOrdinal("Remarks")) ? string.Empty : reader.GetString(reader.GetOrdinal("Remarks")),
                            DefaultgridSize = reader.IsDBNull(reader.GetOrdinal("DefaultGridSize")) ? (byte)0 : reader.GetByte(reader.GetOrdinal("DefaultGridSize")),
                            JobcardCushionhours = reader.IsDBNull(reader.GetOrdinal("JobCardCushionHours")) ? 0 : reader.GetDecimal(reader.GetOrdinal("JobCardCushionHours")),
                            Isactive = reader.GetBoolean(reader.GetOrdinal("Company_Active")) ? "Yes" : "No",
                            CompanyTheme = reader.IsDBNull(reader.GetOrdinal("ThemeName")) ? string.Empty : reader.GetString(reader.GetOrdinal("ThemeName"))
                        });
                    }
                }
            }
            return companyList.AsQueryable();
        }

        #endregion


        #region ::: CompanyExport :::

        public static async Task<object> CompanyExport(SelectAllCompaniesList Obj, string constring, int LogException, string filters, string Query, string sidx, string sord)
        {
            try
            {
                // Create DataTable for exporting data with localized column headers
                DataTable dt = new DataTable();
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.GeneralCulture.ToString(), "Name").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.GeneralCulture.ToString(), "CompanyType").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ShortName").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "ParentCompany").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "LogoName").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Address").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Currency").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Remarks").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "JobCardCushionHours").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "IsActive").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CompanyTheme").ToString());

                // Create a DataTable with the same structure to hold default values
                DataTable dt1 = new DataTable();
                dt1.Columns.Add("Name");
                dt1.Columns.Add("CompanyType");
                dt1.Columns.Add("ShortName");
                dt1.Columns.Add("ParentCompany");
                dt1.Columns.Add("LogoName");
                dt1.Columns.Add("Address");
                dt1.Columns.Add("Currency");
                dt1.Columns.Add("Remarks");
                dt1.Columns.Add("JobCardCushionHours");
                dt1.Columns.Add("IsActive");
                dt1.Columns.Add("CompanyTheme");

                // Add a row of default values (can be modified as needed)
                dt1.Rows.Add(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);

                // Prepare your ADO.NET query or stored procedure call
                string query = "UP_SEL_AMERP_SelectAllCompanies";

                // Initialize a list to store the company data
                List<CompanyData> companyList = new List<CompanyData>();

                // Use ADO.NET to execute the query and fetch data from the database
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    SqlCommand cmd = new SqlCommand(query, conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@UserLanguageID", Obj.UserLanguageID);
                    cmd.Parameters.AddWithValue("@GeneralLanguageID", Obj.GeneralLanguageID);
                    conn.Open();

                    // Use SqlDataReader to fetch the data
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            CompanyData company = new CompanyData
                            {
                                Company_Name = reader["Company_Name"].ToString(),
                                Type = reader["Company_Type"].ToString(),
                                ShortName = reader["Company_ShortName"].ToString(),
                                ParentCompany = reader.IsDBNull(reader.GetOrdinal("Company_Parent_ID")) ? string.Empty : GetParentCompanyName(Convert.ToInt32(reader["Company_Parent_ID"]), constring),
                                LogoName = reader["Company_LogoName"].ToString(),
                                Address = reader["Company_Address"].ToString(),
                                Currency = reader["CurrencyName"].ToString(),
                                Remarks = reader["Remarks"].ToString(),
                                JobcardCushionhours = reader.IsDBNull(reader.GetOrdinal("JobCardCushionHours")) ? 0 : reader.GetDecimal(reader.GetOrdinal("JobCardCushionHours")),
                                Isactive = reader["Company_Active"].ToString(),
                                CompanyTheme = reader.IsDBNull(reader.GetOrdinal("ThemeName")) ? string.Empty : reader.GetString(reader.GetOrdinal("ThemeName"))
                            };
                            companyList.Add(company);
                        }
                    }
                }

                IQueryable<CompanyData> IQ = companyList.AsQueryable();
                // If _search is true, apply filters
                if (Obj.filters != "null" && Obj.filters != "undefined")
                {
                    string urlDecodedFilters = HttpUtility.UrlDecode(filters); // URL decoding
                    string decryptedFilters = Common.DecryptString(urlDecodedFilters); // Decrypt the 'filters' string if necessary
                    JObject jsonFilters = JObject.Parse(decryptedFilters); // Parse JSON

                    Filters filtersobj = jsonFilters.ToObject<Filters>();
                    IQ = IQ.FilterSearch(filtersobj); // Apply search filters
                }
                // If advanced filter (advnce) is true, apply advanced search
                else if (Obj.Query != "null" && Obj.Query != "undefined")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Query).ToObject<AdvanceFilter>();
                    IQ = IQ.AdvanceSearch(advnfilter); // Apply advanced filters
                }

                IQ = IQ.OrderByField(sidx, sord);

                // Add filtered company data to DataTable
                foreach (var company in IQ)
                {
                    dt.Rows.Add(
                        (company.Company_Name),
                        company.Type,
                        company.ShortName,
                        company.ParentCompany,
                        company.LogoName,
                        company.Address,
                        company.Currency,
                        company.Remarks,
                        company.JobcardCushionhours,
                        company.Isactive,
                        company.CompanyTheme
                    );
                }

                ExportList reportExportList = new ExportList

                {

                    Company_ID = Obj.Company_ID, // Assuming this is available in ExportObj

                    Branch = Obj.Branch,

                    dt1 = dt1,


                    dt = dt,

                    FileName = "Company", // Set a default or dynamic filename

                    Header = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Company").ToString(), // Set a default or dynamic header

                    exprtType = Obj.exprtType, // Assuming export type as 1 for Excel, adjust as needed

                    UserCulture = Obj.UserCulture

                };

                var result = await DocumentExport.Export(reportExportList, constring, LogException);
                return result.Value;
                // Export the DataTable as per the specified export type
                //DocumentExport.Export(exprtType, dt, dt1, "Company", CommonFunctionalities.GetResourceString(Obj.GeneralCulture.ToString(), "Company").ToString());
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return null;
        }

        #endregion

        #region ::: Branch Data :::

        public static IQueryable<BranchData> GetBranchDataForCompany(SelectAllBranchForACompanyList Obj, string constring)
        {
            List<BranchData> branchDataList = new List<BranchData>();

            using (SqlConnection connection = new SqlConnection(constring))
            {
                connection.Open();

                SqlCommand command = connection.CreateCommand();
                command.CommandType = CommandType.StoredProcedure;

                if (Obj.UserLanguageID == Obj.GeneralLanguageID)
                {
                    command.CommandText = "Up_Sel_Am_Erp_SelectAllBranchForACompanyForEnglishLanguage";
                    command.Parameters.AddWithValue("@CompanyID", Obj.companyID);
                }
                else
                {
                    command.CommandText = "Up_Sel_Am_Erp_SelectAllBranchForACompanyForLocalLanguage";
                    command.Parameters.AddWithValue("@CompanyID", Obj.companyID);
                    command.Parameters.AddWithValue("@UserLanguageID", Obj.UserLanguageID);
                }

                using (SqlDataReader reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        BranchData branchData = new BranchData
                        {
                            Branch_ID = Convert.ToInt32(reader["Branch_ID"]),
                            Branch_Name = Convert.ToString(reader["Branch_Name"]),
                            Branch_Phone = Convert.ToString(reader["Branch_Phone"]),
                            Branch_Location = Convert.ToString(reader["Branch_Location"]),
                            Branch_Email = Convert.ToString(reader["Branch_Email"]),
                            Branch_HeadOffice = Convert.ToBoolean(reader["Branch_HeadOffice"]) ? "Yes" : "No",
                            Branch_External = Convert.ToBoolean(reader["Branch_External"]) ? "Yes" : "No",
                            Branch_Active = Convert.ToBoolean(reader["Branch_Active"]) ? "Yes" : "No",
                        };

                        branchDataList.Add(branchData);
                    }
                }
            }

            return branchDataList.AsQueryable();
        }

        #endregion


        #region ::: Branch Export :::

        public static async Task<object> BranchExport(SelectAllBranchForACompanyList Obj, string constring, int LogException, string filters, string Query, string sidx, string sord)
        {
            try
            {
                DataTable dt = new DataTable();
                dt.Columns.Add("Branch Name");
                dt.Columns.Add("Phone");
                dt.Columns.Add("Location");
                dt.Columns.Add("Email");
                dt.Columns.Add("IsHeadOffice");
                dt.Columns.Add("IsExternal");
                dt.Columns.Add("IsActive");

                DataTable dt1 = new DataTable();
                dt1.Columns.Add("Branch Name");
                dt1.Columns.Add("Phone");
                dt1.Columns.Add("Location");
                dt1.Columns.Add("Email");
                dt1.Columns.Add("IsHeadOffice");
                dt1.Columns.Add("IsExternal");
                dt1.Columns.Add("IsActive");
                dt1.Rows.Add(0, 2, 0, 0, 0, 0, 0);

                // Use the refactored method to get branch data
                IQueryable<BranchData> branchDataList = GetBranchDataForCompany(Obj, constring);

                if (filters != "null" && filters != "undefined")
                {
                    // First decryption to get the encrypted filters string
                    string firstDecryption = Common.DecryptString(filters);

                    // Now decrypt the decrypted result (second decryption) if necessary
                    string secondDecryption = Common.DecryptString(firstDecryption);

                    // Deserialize the second decrypted string into a Filters object
                    Filters filtersobj = JObject.Parse(secondDecryption).ToObject<Filters>();

                    // Apply the filter to the branch data list
                    branchDataList = branchDataList.FilterSearch<BranchData>(filtersobj);
                }


                branchDataList = branchDataList.OrderByField(sidx, sord);

                foreach (var branch in branchDataList)
                {
                    dt.Rows.Add(
                        branch.Branch_Name,
                        branch.Branch_Phone,
                        branch.Branch_Location,
                        branch.Branch_Email,
                        branch.Branch_HeadOffice,
                        branch.Branch_External,
                        branch.Branch_Active
                    );
                }

                ExportList reportExportList = new ExportList
                {
                    Company_ID = Obj.companyID, // Assuming this is available in ExportObj
                    Branch = Obj.Branch,
                    dt1 = dt1,


                    dt = dt,

                    FileName = "Branch", // Set a default or dynamic filename
                    Header = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Branch").ToString(), // Set a default or dynamic header
                    exprtType = Obj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                    UserCulture = Obj.UserCulture
                };

                var result = await DocumentExport.Export(reportExportList, constring, LogException);
                return result.Value;
                // DocumentExport.Export(exportType, dt, dt1, "Branch", "Branch");

            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return null;
        }


        #endregion



        #region ::: Employee Data :::

        public static IQueryable<EmployeeData> GetEmployeeData(SelectAllEmployeeDetailsList SelectAllEmployeeDetailsObj, string constring)
        {
            List<EmployeeData> employeeList = new List<EmployeeData>();

            using (SqlConnection connection = new SqlConnection(constring))
            {
                connection.Open();
                SqlCommand command = connection.CreateCommand();
                command.CommandType = CommandType.StoredProcedure;

                if (SelectAllEmployeeDetailsObj.UserLanguageID == SelectAllEmployeeDetailsObj.GeneralLanguageID)
                {
                    command.CommandText = "Up_Sel_Am_Erp_SelectAllEmployeeForEnglishLanguage";
                    command.Parameters.AddWithValue("@CompanyID", SelectAllEmployeeDetailsObj.companyID);
                }
                else
                {
                    command.CommandText = "Up_Sel_Am_Erp_SelectAllEmployeeForLocalLanguage";
                    command.Parameters.AddWithValue("@CompanyID", SelectAllEmployeeDetailsObj.companyID);
                    command.Parameters.AddWithValue("@UserLanguageID", SelectAllEmployeeDetailsObj.UserLanguageID);
                }

                using (SqlDataReader reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        EmployeeData employeeData = new EmployeeData();
                        employeeData.Company_Employee_ID = Convert.ToInt32(reader["Company_Employee_ID"]);
                        employeeData.EmployeeCode = reader["Employee_ID"].ToString();
                        employeeData.Company_Employee_Name = reader["Company_Employee_Name"].ToString();
                        employeeData.Company_Employee_MobileNumber = reader["Company_Employee_MobileNumber"].ToString();
                        employeeData.Company_Employee_Email = reader["Company_Employee_Email"].ToString();
                        employeeData.Department = reader["Department"].ToString();
                        employeeData.Designation = reader["Designation"].ToString();
                        employeeList.Add(employeeData);
                    }
                }
            }

            // Convert the list to IQueryable for filtering and sorting
            return employeeList.AsQueryable();
        }


        #endregion

        #region::: EmployeeExport :::
        public static async Task<object> EmployeeExport(SelectAllEmployeeDetailsList Obj, string constring, int LogException, string filters, string Query, string sidx, string sord)
        {
            try
            {
                // Initialize the DataTable with the column headers
                DataTable dt = new DataTable();
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.GeneralCulture.ToString(), "EmployeeID").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.GeneralCulture.ToString(), "Name").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.GeneralCulture.ToString(), "Mobile").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.GeneralCulture.ToString(), "Email").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.GeneralCulture.ToString(), "Department").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.GeneralCulture.ToString(), "Designation").ToString());

                // Define another DataTable for extra information
                DataTable dt1 = new DataTable();
                dt1.Columns.Add("EmployeeID");
                dt1.Columns.Add("Name");
                dt1.Columns.Add("Mobile");
                dt1.Columns.Add("Email");
                dt1.Columns.Add("Department");
                dt1.Columns.Add("Designation");
                dt1.Rows.Add(0, 0, 2, 0, 0, 0);  // Add placeholder row for specific requirement

                // Use the GetEmployeeData method to fetch data
                IQueryable<EmployeeData> iEmployeeArray = GetEmployeeData(Obj, constring);

                if (filters != "null" && filters != "undefined")
                {
                    Filters filtersobj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    iEmployeeArray = iEmployeeArray.FilterSearch(filtersobj);
                }

                iEmployeeArray = iEmployeeArray.OrderByField(sidx, sord);

                // Populate the DataTable with employee data
                foreach (var employee in iEmployeeArray)
                {
                    dt.Rows.Add(
                        employee.EmployeeCode,
                        (employee.Company_Employee_Name),
                        employee.Company_Employee_MobileNumber,
                        (employee.Company_Employee_Email),
                        (employee.Department),
                        (employee.Designation)
                    );
                }

                ExportList reportExportList = new ExportList
                {
                    Company_ID = Obj.companyID, // Assuming this is available in ExportObj
                    Branch = Obj.Branch,
                    dt1 = dt1,


                    dt = dt,

                    FileName = "Employee", // Set a default or dynamic filename
                    Header = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Employee").ToString(), // Set a default or dynamic header
                    exprtType = Obj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                    UserCulture = Obj.UserCulture
                };

                var result = await DocumentExport.Export(reportExportList, constring, LogException);
                return result.Value;

                // Export the document using the populated DataTable
                //DocumentExport.Export(exprtType, dt, dt1, "Employee", HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "Employee").ToString());
            }
            catch (WebException wex)
            {
                // Log the exception
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return null;
        }

        #endregion
        public class DealerMasterDetails
        {
            public List<DealerDetails> DealerDetails { get; set; }

            public DealerMasterDetails()
            {
                DealerDetails = new List<DealerDetails>();
            }
        }
        public class DealerDetails
        {
            public string Dealer_ShortName { get; set; }
            public string Dealer_Name { get; set; }
            public string Dealer_ZipCode { get; set; }
            public string State_Name { get; set; }
            public string Dealer_Phone { get; set; }
            public string Dealer_Fax { get; set; }
            public string Country_Short_Name { get; set; }
            public string Dealer_Address { get; set; }
            public string Dealer_Location { get; set; }
            public string Dealer_Email { get; set; }
            public string Dealer_Mobile { get; set; }
            public bool Dealer_External { get; set; } = true;
            public string TimeZone_ShortName { get; set; }
            public string Region_ShortName { get; set; }
            public string Currency_ShortName { get; set; }

            // Dealer Parent
            public string Parent_DealerShortname { get; set; }
            public string Remarks { get; set; }
            public string BranchTaxCode { get; set; }
            public string BranchTaxCodeName { get; set; }

            private string _languageCode = "En";

            public string Language_Code
            {
                get => string.IsNullOrWhiteSpace(_languageCode) ? "En" : _languageCode;
                set => _languageCode = string.IsNullOrWhiteSpace(value) ? "En" : value;
            }

            public List<PartyContactPersonDetails> PartyContactPersonDetails { get; set; } = new List<PartyContactPersonDetails>();
        }

        public class PartyContactPersonDetails
        {
            public string PartyContactPerson_Name { get; set; }
            public string PartyContactPerson_Email { get; set; }
            public string PartyContactPerson_Department { get; set; }
            public string PartyContactPerson_Mobile { get; set; }
            public string PartyContactPerson_Phone { get; set; }
            public string Remarks { get; set; }
            public string PartyContactPerson_DOB { get; set; }
           
        }




        public class MonthEndLog
        {
            public int MonthEndReportLog_ID { get; set; }
            public int Year { get; set; }
            public string Month { get; set; }
            public string StartedBy_Nm { get; set; }
            public DateTime MonthEnd_From_Date { get; set; }
            public DateTime MonthEnd_To_Date { get; set; }
            public DateTime StartedDateTime { get; set; }
            public DateTime CompletedDateTime { get; set; }
            public string CompletedDateTimestr { get; set; }
            public string CompletedBy_Nm { get; set; }
            public string Remarks { get; set; }
        }

        public class GetNextReleaseInfoList
        {
            public int Company_ID { get; set; }
            public int Branch { get; set; }

        }
        public class SelectMonthEndLogList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Branch { get; set; }

        }
        public class SelectReleaseNoteInformationList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Branch { get; set; }
            public string AppPathString { get; set; }

        }
        public class ReleaseNoteInformation
        {
            public int ReleaseNoteInfo_ID { get; set; }
            public string ReleaseNoteFileName { get; set; }
            public string ReleaseVersion { get; set; }
            public DateTime? ReleaseDate { get; set; }
            public string ReleaseDateStr { get; set; }
            public string IsActive { get; set; }
        }
        public class CheckOrderingCostofParentList
        {
            public int Parent_ID { get; set; }
            public int SelOrderCost_ID { get; set; }
        }
        public class SelectBranchMastersDataList
        {
            public int Company_ID { get; set; }
            public int languageID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int countryID { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public class SelectOnlyCompanyMastersDataList
        {
            public int Company_ID { get; set; }
            public int languageID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        public class RefMasterDetail
        {
            public int RefMasterDetail_ID { get; set; }
            public string RefMasterDetail_Name { get; set; }
        }

        public class ParentCompany
        {
            public int Company_ID { get; set; }
            public string Company_Name { get; set; }
        }


        public class SelectAllEmployeeMastersDataList
        {
            public int languageID { get; set; }
            public int countryID { get; set; }
            public int companyID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        public class CheckLocaleBranchNameList
        {
            public string branchName { get; set; }
            public int companyID { get; set; }
            public int UserLanguageID { get; set; }
            public int primaryKey { get; set; }
        }
        public class CheckLocaleCompanyNameList
        {
            public string companyName { get; set; }
            public int UserLanguageID { get; set; }
            public int primaryKey { get; set; }

        }
        public class CheckEmployeeIDList
        {
            public string employeeID { get; set; }
            public int companyID { get; set; }
            public int primaryKey { get; set; }
        }
        public class GetBranchCountList
        {
            public int companyID { get; set; }

        }
        public class CheckBranchNameList
        {
            public string branchName { get; set; }
            public int companyID { get; set; }
            public int primaryKey { get; set; }
        }
        public class CheckCompanyNameList
        {
            public string companyName { get; set; }
            public int primaryKey { get; set; }
        }
        public class CheckEmployeeBranchList
        {
            public int employeeID { get; set; }
            public int branchID { get; set; }
            public int primaryKey { get; set; }
        }

        public class DeleteEmployeeBranchList
        {
            public string key { get; set; }
            public string Lang { get; set; }
        }
        public class SaveEmployeeBranchList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string Data { get; set; }
        }
        public class SelAllEmployeeBranchesList
        {
            public int employeeID { get; set; }
            public int languageID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        public class DeleteBranchTaxCodesList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string key { get; set; }
            public string Lang { get; set; }
        }
        public class SaveBranchTaxCodeList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string Data { get; set; }
        }
        public class SelAllBranchTaxCodeList
        {
            public int branchID { get; set; }
        }
        public class DeleteSkillsList
        {
            public string key { get; set; }
            public string Lang { get; set; }
        }
        public class SaveSkillsList
        {
            public string Data { get; set; }
        }
        public class SelAllEmployeeSkillsetList
        {
            public int employeeID { get; set; }
            public int languageID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        public class CheckEmployeeSkillsList
        {
            public int skillID { get; set; }
            public int employeeID { get; set; }
            public int primaryKey { get; set; }
        }
        public class CheckCompanyRelationsList
        {
            public int companyID { get; set; }
            public int selectedCompanyID { get; set; }
            public int primaryKey { get; set; }
        }
        public class CheckCompanyBrandAssociationList
        {
            public int companyID { get; set; }
            public int brandID { get; set; }
            public int primaryKey { get; set; }
        }
        public class CheckBranchTaxDetailsList
        {
            public int taxStructureID { get; set; }
            public int branchID { get; set; }
            public int primaryKey { get; set; }
        }
        public class DeleteCompanyEmployeeList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string key { get; set; }
            public string UserCulture { get; set; }
        }
        public class DeleteCompanyBrandsList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string key { get; set; }
            public string Lang { get; set; }
        }
        public class DeleteCompanyRelationList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string key { get; set; }
            public string Lang { get; set; }
        }
        public class DeleteBranchTaxDetailsList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string key { get; set; }
            public string UserCulture { get; set; }
        }
        public class DeleteBranchList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string key { get; set; }
            public string UserCulture { get; set; }
        }
        public class DeleteCompanyList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string key { get; set; }
            public string Lang { get; set; }
        }
        public class UpdateEmployeeLocaleList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string employeeLocaleData { get; set; }
        }
        public class InsertEmployeeLocaleList
        {
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string employeeLocaleData { get; set; }
        }
        public class UpdateBranchLocaleList
        {
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string BranchLocaleData { get; set; }
        }
        public class InsertBranchLocaleList
        {
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string BranchLocaleData { get; set; }
        }
        public class UpdateCompanyLocaleList
        {
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string companyData { get; set; }
        }

        public class InsertCompanyLocaleList
        {
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string companyData { get; set; }
        }
        public class UpdateEmployeeDetailsList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string EmployeeData { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public class InsertEmployeeDetailsList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string EmployeeData { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public class SelectSpecificEmployeeDetailsList
        {
            public int id { get; set; }
            public int languageID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        public class SelEmployeelocaleList
        {
            public int id { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        public class SelectAllEmployeeDetailsList
        {
            public int companyID { get; set; }
            public int Branch { get; set; }
            public int exprtType { get; set; }
            public int langaugeID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string GeneralCulture { get; set; }
            public string UserCulture { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string Query { get; set; }
            public string filters { get; set; }
        }
        public class EmployeeData
        {
            public string IsActive { get; set; }
            public string edit { get; set; }
            public string delete { get; set; }
            public string view { get; set; }
            public int Company_Employee_ID { get; set; }
            public string Company_Employee_Name { get; set; }
            public string EmployeeCode { get; set; }
            public string Company_Employee_MobileNumber { get; set; }
            public string Company_Employee_Email { get; set; }
            public string Department { get; set; }
            public string Designation { get; set; }
            public string Local { get; set; }
            public string Manager { get; set; }
            public string Country { get; set; }
            public string State { get; set; }
            public string Address { get; set; }
            public string Location { get; set; }
            public string Zipcode { get; set; }
            public string Landline { get; set; }
            public string ActiveFromstring { get; set; }
            public string AtiveTostring { get; set; }
            public decimal? HourlyRate { get; set; }
            public string RegionName { get; set; }
            public byte ExemptionHours { get; set; }
            public bool IsEligibleForOT { get; set; }
        }
        public class SaveBrandDetailsList
        {
            public string brandData { get; set; }
        }
        public class SelectAllBrandsForaCompanyList
        {
            public int companyID { get; set; }
            public int languageID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        public class SaveRelationList
        {
            public string relationData { get; set; }
        }

        public class SelectAllCompanyRelationshipsList
        {
            public int companyID { get; set; }
            public int languageID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }

        public class SaveBranchTaxDetailList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string branchTaxData { get; set; }
        }
        public class SelectBranchTaxDetailsList
        {
            public int Company_ID { get; set; }
            public int branchID { get; set; }
            public int languageID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public class UpdateBranchHeaderList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string branchData { get; set; }
        }
        public class SelectParticularBranchList
        {
            public int id { get; set; }
            public int languageID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        public class InsertBranchHeaderList
        {
            public string branchData { get; set; }
        }
        public class SelectLocaleBranchList
        {
            public int id { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        public class SelectAllBranchForACompanyList
        {
            public int companyID { get; set; }
            public int Branch { get; set; }
            public int exportType { get; set; }
            public int languageID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int exprtType { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string Query { get; set; }
            public string filters { get; set; }
            public string UserCulture { get; set; }
            public string GeneralCulture { get; set; }
        }

        public class InsertCoreCompanyList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string data { get; set; }
        }
        public class UpdateCoreCompanyList
        {
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public DateTime LoggedIDateTime { get; set; }
            public string data { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public class SelectStateList
        {
            public int Company_ID { get; set; }
            public int countryID { get; set; }
            public int languageID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        public class SelectCurrencyList
        {
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        public class SelectCountryList
        {
            public int languageID { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        public class SelectCompanyLocaleDetailsList
        {
            public int id { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        public class SelectParticularCompanyList
        {
            public int id { get; set; }
            public int languageID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public class SelectAllCompaniesList
        {
            public int languageID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int exprtType { get; set; }
            public string GeneralCulture { get; set; }
            public string UserCulture { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filters { get; set; }
            public string Query { get; set; }
        }
        public class CompanyData
        {
            public string view { get; set; }
            public string edit { get; set; }
            public string delete { get; set; }
            public int Company_ID { get; set; }
            public string Company_Name { get; set; }
            public string Type { get; set; }
            public string Local { get; set; }
            public string ShortName { get; set; }
            public string ParentCompany { get; set; }
            public string LogoName { get; set; }
            public string Address { get; set; }
            public string Currency { get; set; }
            public string Remarks { get; set; }
            public byte DefaultgridSize { get; set; }
            public decimal? JobcardCushionhours { get; set; }
            public string Isactive { get; set; }
            public string CompanyTheme { get; set; }

        }



    }
}
