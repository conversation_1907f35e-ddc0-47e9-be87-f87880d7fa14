﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Abandon" xml:space="preserve">
    <value>CH Abandon</value>
  </data>
  <data name="AbandonDelayReason" xml:space="preserve">
    <value>CH Abandon &amp; Delay Reason</value>
  </data>
  <data name="Abandoned" xml:space="preserve">
    <value>CH Abandoned</value>
  </data>
  <data name="Accept" xml:space="preserve">
    <value>CH Accept</value>
  </data>
  <data name="AccountNumber" xml:space="preserve">
    <value>CH Account Number</value>
  </data>
  <data name="Action" xml:space="preserve">
    <value>CH Action</value>
  </data>
  <data name="ActionBy" xml:space="preserve">
    <value>CH Action By</value>
  </data>
  <data name="ActionDate" xml:space="preserve">
    <value>CH Action Date</value>
  </data>
  <data name="ActionForNextService" xml:space="preserve">
    <value>CH Action For Next Service</value>
  </data>
  <data name="ActionName" xml:space="preserve">
    <value>CH Action Name</value>
  </data>
  <data name="ActionRemarks" xml:space="preserve">
    <value>CH Action Remarks</value>
  </data>
  <data name="ActionRemarksMaxlimitexceeded" xml:space="preserve">
    <value>CH Action remarks max limit exceeded</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>CH Actions</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>CH Is Active?</value>
  </data>
  <data name="ActiveFrom" xml:space="preserve">
    <value>CH Active From</value>
  </data>
  <data name="ActiveFromdatecannotbegreaterthanActiveTodate" xml:space="preserve">
    <value>CH Active from date cannot be greater than active to date</value>
  </data>
  <data name="ActiveTo" xml:space="preserve">
    <value>CH Active To</value>
  </data>
  <data name="ActualHours" xml:space="preserve">
    <value>CH Actual Hours</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>CH Add</value>
  </data>
  <data name="AddAction" xml:space="preserve">
    <value>CH Add Action</value>
  </data>
  <data name="AddBranch" xml:space="preserve">
    <value>CH Add Branch</value>
  </data>
  <data name="AddBranchTaxDetails" xml:space="preserve">
    <value>CH Add Branch Tax Details</value>
  </data>
  <data name="AddBrands" xml:space="preserve">
    <value>CH Add Brands</value>
  </data>
  <data name="AddCompany" xml:space="preserve">
    <value>CH Add Company</value>
  </data>
  <data name="AddCompanyRelation" xml:space="preserve">
    <value>CH Add Company Relation</value>
  </data>
  <data name="AddCompanyTaxDetails" xml:space="preserve">
    <value>CH Add Company Tax Details</value>
  </data>
  <data name="AddCompanyTerms" xml:space="preserve">
    <value>CH Add Company Terms</value>
  </data>
  <data name="addcomponentdetails" xml:space="preserve">
    <value>CH Add Component Details</value>
  </data>
  <data name="addcustomer" xml:space="preserve">
    <value>CH Add Customer</value>
  </data>
  <data name="AddCustomerQuotation" xml:space="preserve">
    <value>CH Add Customer Quotation</value>
  </data>
  <data name="AddEmployee" xml:space="preserve">
    <value>CH Add Employee</value>
  </data>
  <data name="AddEvents" xml:space="preserve">
    <value>CH Add Events</value>
  </data>
  <data name="AddFilter" xml:space="preserve">
    <value>CH Add Filter</value>
  </data>
  <data name="addfreestock" xml:space="preserve">
    <value>CH Add Parts Free Stock</value>
  </data>
  <data name="AddFunctionGroup" xml:space="preserve">
    <value>CH Add Function Group</value>
  </data>
  <data name="AddJobCard" xml:space="preserve">
    <value>CH Add Job Card</value>
  </data>
  <data name="AddMaster" xml:space="preserve">
    <value>CH Add Master</value>
  </data>
  <data name="addmodel" xml:space="preserve">
    <value>CH Add Model</value>
  </data>
  <data name="addnewpart" xml:space="preserve">
    <value>CH Add New Part</value>
  </data>
  <data name="addoperation" xml:space="preserve">
    <value>CH Add Operation</value>
  </data>
  <data name="AddOperationEmployeeDetails" xml:space="preserve">
    <value>CH Add Operation Employee Details</value>
  </data>
  <data name="AddPart" xml:space="preserve">
    <value>CH Add Part</value>
  </data>
  <data name="addpartprice" xml:space="preserve">
    <value>CH Add Part Price</value>
  </data>
  <data name="AddParts" xml:space="preserve">
    <value>CH Add Parts</value>
  </data>
  <data name="AddParty" xml:space="preserve">
    <value>CH Add Party</value>
  </data>
  <data name="AddPrefixSuffix" xml:space="preserve">
    <value>CH Add Prefix Suffix</value>
  </data>
  <data name="addproduct" xml:space="preserve">
    <value>CH Add Product</value>
  </data>
  <data name="addproductdetail" xml:space="preserve">
    <value>CH Add Product Detail</value>
  </data>
  <data name="addproducttype" xml:space="preserve">
    <value>CH Add Product Type</value>
  </data>
  <data name="addproducttypedetails" xml:space="preserve">
    <value>CH Add Part Product Type Details</value>
  </data>
  <data name="AddRequest" xml:space="preserve">
    <value>CH Add Request</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>CH Address</value>
  </data>
  <data name="Address1" xml:space="preserve">
    <value>CH Address Line 1</value>
  </data>
  <data name="Address2" xml:space="preserve">
    <value>CH Address Line 2</value>
  </data>
  <data name="Address3" xml:space="preserve">
    <value>CH Address Line 3</value>
  </data>
  <data name="AddresseFlag" xml:space="preserve">
    <value>CH Addresse Flag</value>
  </data>
  <data name="AddRole" xml:space="preserve">
    <value>CH Add Role</value>
  </data>
  <data name="addServiceCharge" xml:space="preserve">
    <value>CH Add Service Charge</value>
  </data>
  <data name="addServiceChargeDetails" xml:space="preserve">
    <value>CH Add Service Charge Details</value>
  </data>
  <data name="addservicecharges" xml:space="preserve">
    <value>CH Add Service Charges</value>
  </data>
  <data name="AddServiceType" xml:space="preserve">
    <value>CH Add Service Type</value>
  </data>
  <data name="addsiteaddress" xml:space="preserve">
    <value>CH Add Site Address</value>
  </data>
  <data name="AddSkills" xml:space="preserve">
    <value>CH Add Skills</value>
  </data>
  <data name="AddSpecialization" xml:space="preserve">
    <value>CH Add Specialization</value>
  </data>
  <data name="AddStep" xml:space="preserve">
    <value>CH Add Step</value>
  </data>
  <data name="AddStepLink" xml:space="preserve">
    <value>CH Add Step Link</value>
  </data>
  <data name="AddSundry" xml:space="preserve">
    <value>CH Add Sundry</value>
  </data>
  <data name="AddTaxCode" xml:space="preserve">
    <value>CH Add Tax Code</value>
  </data>
  <data name="addtaxstructure" xml:space="preserve">
    <value>CH Add tax Structure</value>
  </data>
  <data name="AddTaxStructureDetails" xml:space="preserve">
    <value>CH Add Tax Structure Details</value>
  </data>
  <data name="addtaxtstructuredetails" xml:space="preserve">
    <value>CH Add Tax Structure Details</value>
  </data>
  <data name="AddUser" xml:space="preserve">
    <value>CH Add User</value>
  </data>
  <data name="addwarrantydetails" xml:space="preserve">
    <value>CH Add Warranty Details</value>
  </data>
  <data name="AddWorkDetails" xml:space="preserve">
    <value>CH Add Work Details</value>
  </data>
  <data name="advancesearch" xml:space="preserve">
    <value>CH Advance Search</value>
  </data>
  <data name="all" xml:space="preserve">
    <value>CH All</value>
  </data>
  <data name="Allocate" xml:space="preserve">
    <value>CH Allocate</value>
  </data>
  <data name="AllocatedHours" xml:space="preserve">
    <value>CH Allocated Hours</value>
  </data>
  <data name="AllocationNotPossible" xml:space="preserve">
    <value>CH Allocation not possible</value>
  </data>
  <data name="AlloctedSuccessfully" xml:space="preserve">
    <value>CH Allocted successfully</value>
  </data>
  <data name="AlloctionFailed" xml:space="preserve">
    <value>CH Alloction failed</value>
  </data>
  <data name="AllQueue" xml:space="preserve">
    <value>CH All Queue</value>
  </data>
  <data name="AlreadyAssociatedPleaseSelectfromDropDown" xml:space="preserve">
    <value>CH Already Associated Please Select from Drop Down</value>
  </data>
  <data name="alreadyexists" xml:space="preserve">
    <value>CH already exists</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>CH Amount</value>
  </data>
  <data name="AmountBlank" xml:space="preserve">
    <value>CH Amount is  blank</value>
  </data>
  <data name="AmountIsBeyondAcceptableLimit" xml:space="preserve">
    <value>CH Amount Is Beyond Acceptable Limit</value>
  </data>
  <data name="AmountShouldbeLessThan" xml:space="preserve">
    <value>CH Amount should be less than</value>
  </data>
  <data name="AND" xml:space="preserve">
    <value>CH AND</value>
  </data>
  <data name="ApprovalLimit" xml:space="preserve">
    <value>CH Approval Limit</value>
  </data>
  <data name="April" xml:space="preserve">
    <value>CH Apr</value>
  </data>
  <data name="Aresurewanttodelete" xml:space="preserve">
    <value>CH Are sure want to delete</value>
  </data>
  <data name="Areyousurewanttocancel" xml:space="preserve">
    <value>CH Are you sure you want to cancel?</value>
  </data>
  <data name="Areyousurewanttodelete" xml:space="preserve">
    <value>CH Are you sure want to delete ?</value>
  </data>
  <data name="AreyousureyouwanttoAbandontheRequest" xml:space="preserve">
    <value>CH Are you sure you want to abandon the request</value>
  </data>
  <data name="areyousureyouwanttomovetheevent" xml:space="preserve">
    <value>CH Are you sure you want to move the event?</value>
  </data>
  <data name="Assign" xml:space="preserve">
    <value>CH Assign</value>
  </data>
  <data name="AssignedTo" xml:space="preserve">
    <value>CH Assigned To</value>
  </data>
  <data name="AssignTo" xml:space="preserve">
    <value>CH Assign To</value>
  </data>
  <data name="Atleastselectonedetail" xml:space="preserve">
    <value>CH Atleast select one detail</value>
  </data>
  <data name="August" xml:space="preserve">
    <value>CH Aug</value>
  </data>
  <data name="AuthorizedSignatory" xml:space="preserve">
    <value>CH Authorized Signatory</value>
  </data>
  <data name="autoallocate" xml:space="preserve">
    <value>CH Auto Allocate</value>
  </data>
  <data name="AutoAllocationAllowed" xml:space="preserve">
    <value>CH Auto Allocation Allowed</value>
  </data>
  <data name="AverageResolutionTime" xml:space="preserve">
    <value>CH Average Resolution Time</value>
  </data>
  <data name="averageresolutiontimeyearwise" xml:space="preserve">
    <value>CH Average Resolution Time - Year Wise</value>
  </data>
  <data name="averageresponsetime" xml:space="preserve">
    <value>CH Average Response Time</value>
  </data>
  <data name="averageresponsetimeyearwise" xml:space="preserve">
    <value>CH Average Response Time -Year Wise</value>
  </data>
  <data name="AverageTime" xml:space="preserve">
    <value>CH Average Time</value>
  </data>
  <data name="AvgResolutionTime" xml:space="preserve">
    <value>CH Avg Resolution Time</value>
  </data>
  <data name="BankName" xml:space="preserve">
    <value>CH Bank Name</value>
  </data>
  <data name="BarChart" xml:space="preserve">
    <value>CH Bar Chart</value>
  </data>
  <data name="Branch" xml:space="preserve">
    <value>CH Branch</value>
  </data>
  <data name="BranchAlrearySelected" xml:space="preserve">
    <value>CH Branch alreary selected</value>
  </data>
  <data name="BranchAssociation" xml:space="preserve">
    <value>CH Branch Association</value>
  </data>
  <data name="branchdetail" xml:space="preserve">
    <value>CH Branch Detail</value>
  </data>
  <data name="BranchName" xml:space="preserve">
    <value>CH Branch Name</value>
  </data>
  <data name="BranchNameisalreadypresent" xml:space="preserve">
    <value>CH Branch name is already present</value>
  </data>
  <data name="BranchTaxCode" xml:space="preserve">
    <value>CH Branch Tax Code</value>
  </data>
  <data name="BranchTaxDetails" xml:space="preserve">
    <value>CH Branch Tax Details</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>CH Brand</value>
  </data>
  <data name="BrandName" xml:space="preserve">
    <value>CH Brand Name</value>
  </data>
  <data name="BrandsAssociation" xml:space="preserve">
    <value>CH Brands Association</value>
  </data>
  <data name="Break" xml:space="preserve">
    <value>CH Break</value>
  </data>
  <data name="BreakHourscannotbegreatorthanWorkingHours" xml:space="preserve">
    <value>CH Break Hours cannot be greator than Working Hours</value>
  </data>
  <data name="Browse" xml:space="preserve">
    <value>CH Browse</value>
  </data>
  <data name="calculateformula" xml:space="preserve">
    <value>CH Calculate Formula</value>
  </data>
  <data name="Calendar" xml:space="preserve">
    <value>CH Calendar</value>
  </data>
  <data name="CallBackDate" xml:space="preserve">
    <value>CH Call Back Date</value>
  </data>
  <data name="CallBackTime" xml:space="preserve">
    <value>CH Call Back Time</value>
  </data>
  <data name="CallClosureDate" xml:space="preserve">
    <value>CH Call Closure Date</value>
  </data>
  <data name="CallClosureTime" xml:space="preserve">
    <value>CH Call Closure Time</value>
  </data>
  <data name="CallDate" xml:space="preserve">
    <value>CH Call Date</value>
  </data>
  <data name="CallDateCanNotBeGreaterThanCurrentDate" xml:space="preserve">
    <value>CH CallDate Cannot be greater than current date</value>
  </data>
  <data name="CallDescription" xml:space="preserve">
    <value>CH Call Description</value>
  </data>
  <data name="CallDetails" xml:space="preserve">
    <value>CH Call Details</value>
  </data>
  <data name="CallMode" xml:space="preserve">
    <value>CH Call Mode</value>
  </data>
  <data name="CallNature" xml:space="preserve">
    <value>CH Call Nature</value>
  </data>
  <data name="CallStatus" xml:space="preserve">
    <value>CH Call Status</value>
  </data>
  <data name="CallTime" xml:space="preserve">
    <value>CH Call Time</value>
  </data>
  <data name="CallType" xml:space="preserve">
    <value>CH Call Type</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>CH Cancel</value>
  </data>
  <data name="CannotdeleteasTaxTypeisreferenced" xml:space="preserve">
    <value>CH Cannot delete as Tax Type is referenced</value>
  </data>
  <data name="CannotDeleteinEditMode" xml:space="preserve">
    <value>CH Cannot delete in edit mode</value>
  </data>
  <data name="CaseProgress" xml:space="preserve">
    <value>CH Case Progress</value>
  </data>
  <data name="CaseProgressHistory" xml:space="preserve">
    <value>CH Case Progress History</value>
  </data>
  <data name="CauseofFailure" xml:space="preserve">
    <value>CH Cause of Failure</value>
  </data>
  <data name="Changeswillbelostdoyouwanttoproceed" xml:space="preserve">
    <value>CH Changes will be lost do you want to proceed</value>
  </data>
  <data name="ChooseColumnNames" xml:space="preserve">
    <value>CH Choose column names</value>
  </data>
  <data name="clearformula" xml:space="preserve">
    <value>CH Clear Formula</value>
  </data>
  <data name="close" xml:space="preserve">
    <value>CH Close</value>
  </data>
  <data name="Closed" xml:space="preserve">
    <value>CH Closed</value>
  </data>
  <data name="ClosedCount" xml:space="preserve">
    <value>CH Closed Count</value>
  </data>
  <data name="ClosureDetails" xml:space="preserve">
    <value>CH Closure Details</value>
  </data>
  <data name="closurereason" xml:space="preserve">
    <value>CH Closure Reason</value>
  </data>
  <data name="ClosureType" xml:space="preserve">
    <value>CH Closure Type</value>
  </data>
  <data name="code" xml:space="preserve">
    <value>CH Code</value>
  </data>
  <data name="ColumnNames" xml:space="preserve">
    <value>CH Column Names</value>
  </data>
  <data name="commissioningdate" xml:space="preserve">
    <value>CH Commissioning Date</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>CH Company</value>
  </data>
  <data name="CompanyBrands" xml:space="preserve">
    <value>CH Company Brands</value>
  </data>
  <data name="CompanyCalender" xml:space="preserve">
    <value>CH Company Calender</value>
  </data>
  <data name="CompanyFinancialYear" xml:space="preserve">
    <value>CH Company Financial Year</value>
  </data>
  <data name="CompanyHeader" xml:space="preserve">
    <value>CH Header</value>
  </data>
  <data name="CompanyMaster" xml:space="preserve">
    <value>CH Company Master</value>
  </data>
  <data name="CompanyName" xml:space="preserve">
    <value>CH Company Name</value>
  </data>
  <data name="CompanyNameisalreadypresent" xml:space="preserve">
    <value>CH Company name is already present</value>
  </data>
  <data name="CompanyRelation" xml:space="preserve">
    <value>CH Company-Company Relation</value>
  </data>
  <data name="CompanyRelationships" xml:space="preserve">
    <value>CH Company Relationships</value>
  </data>
  <data name="CompanySavedPleaseAssociateatleastoneBrand" xml:space="preserve">
    <value>CH Company header saved success fully, Please associate atleast one Brand</value>
  </data>
  <data name="CompanyTaxCode" xml:space="preserve">
    <value>CH Company Tax Code</value>
  </data>
  <data name="CompanyTaxDetails" xml:space="preserve">
    <value>CH Company Tax Details</value>
  </data>
  <data name="CompanyTerms" xml:space="preserve">
    <value>CH Company Terms</value>
  </data>
  <data name="CompanyTheme" xml:space="preserve">
    <value>CH Company Theme</value>
  </data>
  <data name="CompanyType" xml:space="preserve">
    <value>CH Company Type</value>
  </data>
  <data name="Completed" xml:space="preserve">
    <value>CH Completed</value>
  </data>
  <data name="CompletedCount" xml:space="preserve">
    <value>CH Completed Count</value>
  </data>
  <data name="CompleteEnteringDetail" xml:space="preserve">
    <value>CH Complete entering detail</value>
  </data>
  <data name="CompleteEnteringDetailParts" xml:space="preserve">
    <value>CH Complete entering parts detail</value>
  </data>
  <data name="CompleteEnteringDetailService" xml:space="preserve">
    <value>CH Complete entering service detail</value>
  </data>
  <data name="CompleteEnteringDetailSundry" xml:space="preserve">
    <value>CH Complete entering sundry detail</value>
  </data>
  <data name="componentdetails" xml:space="preserve">
    <value>CH Component Details</value>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>CH Confirm Password</value>
  </data>
  <data name="ContactPerson" xml:space="preserve">
    <value>CH Contact Person</value>
  </data>
  <data name="ContactPersons" xml:space="preserve">
    <value>CH Contact Persons</value>
  </data>
  <data name="Copyright2012QuestInformaticsPrivateLimited" xml:space="preserve">
    <value>CH Copyright @ 2012 Quest Informatics Private Limited</value>
  </data>
  <data name="CopyRoleFrom" xml:space="preserve">
    <value>CH Copy Role From</value>
  </data>
  <data name="CorrectiveAction" xml:space="preserve">
    <value>CH Corrective Action</value>
  </data>
  <data name="Count" xml:space="preserve">
    <value>CH Count</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>CH Country</value>
  </data>
  <data name="Created" xml:space="preserve">
    <value>CH Created</value>
  </data>
  <data name="CreateJobCard" xml:space="preserve">
    <value>CH Create JobCard</value>
  </data>
  <data name="CreateNew" xml:space="preserve">
    <value>CH Create New</value>
  </data>
  <data name="CreateQuotation" xml:space="preserve">
    <value>CH Create Quotation</value>
  </data>
  <data name="CreateServiceRequest" xml:space="preserve">
    <value>CH Create Service Request</value>
  </data>
  <data name="crictical" xml:space="preserve">
    <value>CH Crictical</value>
  </data>
  <data name="Critical" xml:space="preserve">
    <value>CH Critical</value>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>CH Currency</value>
  </data>
  <data name="CurrentStep" xml:space="preserve">
    <value>CH Current Step</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>CH Customer</value>
  </data>
  <data name="CustomerComplaint" xml:space="preserve">
    <value>CH Customer Complaint</value>
  </data>
  <data name="CustomerContact" xml:space="preserve">
    <value>CH Customer Contact</value>
  </data>
  <data name="CustomerContactPhone" xml:space="preserve">
    <value>CH Customer Contact Phone</value>
  </data>
  <data name="customerdetails" xml:space="preserve">
    <value>CH Customer Details</value>
  </data>
  <data name="customerdueservices" xml:space="preserve">
    <value>CH Customer Due Services</value>
  </data>
  <data name="CustomerisLockedDoyouwanttocontinue" xml:space="preserve">
    <value>CH Customer is locked, do you want to continue?</value>
  </data>
  <data name="Customerisnotactive" xml:space="preserve">
    <value>CH Customer is not active</value>
  </data>
  <data name="CustomerLocation" xml:space="preserve">
    <value>CH Customer Location</value>
  </data>
  <data name="customername" xml:space="preserve">
    <value>CH Customer Name</value>
  </data>
  <data name="CustomerNotFound" xml:space="preserve">
    <value>CH Customer not found</value>
  </data>
  <data name="CustomerQuotation" xml:space="preserve">
    <value>CH Customer Quotation</value>
  </data>
  <data name="CustomerQuotationDate" xml:space="preserve">
    <value>CH Quotation Date</value>
  </data>
  <data name="CustomerQuotationNumber" xml:space="preserve">
    <value>CH Quotation Number</value>
  </data>
  <data name="CustomerQuotationSummary" xml:space="preserve">
    <value>CH Customer Quotation Summary</value>
  </data>
  <data name="CustomerRating" xml:space="preserve">
    <value>CH Customer Rating</value>
  </data>
  <data name="customersearch" xml:space="preserve">
    <value>CH Customer Search</value>
  </data>
  <data name="CustomerServiceHistory" xml:space="preserve">
    <value>CH Customer Service History</value>
  </data>
  <data name="DataSavedSuccessfully" xml:space="preserve">
    <value>CH Data saved successfully</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>CH Date</value>
  </data>
  <data name="DateCannotbelessthenCurrentDate" xml:space="preserve">
    <value>CH Effective from date cannot be less than current date</value>
  </data>
  <data name="Datecannotbelessthenpreviousdate" xml:space="preserve">
    <value>CH Date cannot be less than the date of previous records</value>
  </data>
  <data name="dateselectedmustbegreaterthenpreviouscustomer" xml:space="preserve">
    <value>CH Date Selected must be greater than previous Customer</value>
  </data>
  <data name="DaysLeft" xml:space="preserve">
    <value>CH Days Left</value>
  </data>
  <data name="Dealer" xml:space="preserve">
    <value>CH Dealer</value>
  </data>
  <data name="DealersorCompany" xml:space="preserve">
    <value>CH Dealers/Company</value>
  </data>
  <data name="December" xml:space="preserve">
    <value>CH Dec</value>
  </data>
  <data name="DefaultGridSize" xml:space="preserve">
    <value>CH Default Grid Size</value>
  </data>
  <data name="Defaultgridsizealreadyavailable" xml:space="preserve">
    <value>CH Default grid size already available</value>
  </data>
  <data name="DefaultgridSizeshouldbebetweenzeroandtwofiftyfive" xml:space="preserve">
    <value>CH Default grid size should be between 0 and 255</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>CH Delete</value>
  </data>
  <data name="DeleteAction" xml:space="preserve">
    <value>CH Delete Action</value>
  </data>
  <data name="DeleteBranch" xml:space="preserve">
    <value>CH Delete Branch</value>
  </data>
  <data name="DeleteBranchTaxDetails" xml:space="preserve">
    <value>CH Delete Branch Tax Details</value>
  </data>
  <data name="DeleteBrands" xml:space="preserve">
    <value>CH Delete Brands</value>
  </data>
  <data name="DeleteCompany" xml:space="preserve">
    <value>CH Delete Company</value>
  </data>
  <data name="DeleteCompanyRelation" xml:space="preserve">
    <value>CH Delete Company Relation</value>
  </data>
  <data name="DeleteCompanyTerms" xml:space="preserve">
    <value>CH Delete Company Terms</value>
  </data>
  <data name="deletedsuccessfully" xml:space="preserve">
    <value>CH Deleted Successfully</value>
  </data>
  <data name="DeleteEmployee" xml:space="preserve">
    <value>CH Delete Employee</value>
  </data>
  <data name="deletefreestock" xml:space="preserve">
    <value>CH Delete Part Free Stock</value>
  </data>
  <data name="DeleteFunctionGroup" xml:space="preserve">
    <value>CH Delete Function Group</value>
  </data>
  <data name="DeleteJobCard" xml:space="preserve">
    <value>CH Delete Job Card</value>
  </data>
  <data name="DeleteMaster" xml:space="preserve">
    <value>CH Delete Master</value>
  </data>
  <data name="deletemodel" xml:space="preserve">
    <value>CH Delete Model</value>
  </data>
  <data name="deleteoperation" xml:space="preserve">
    <value>CH Delete Operation</value>
  </data>
  <data name="DeleteOperationEmployeeDetails" xml:space="preserve">
    <value>CH Delete Operation Employee Details</value>
  </data>
  <data name="deletepart" xml:space="preserve">
    <value>CH Delete Part</value>
  </data>
  <data name="deletepartprice" xml:space="preserve">
    <value>CH Delete Part Price</value>
  </data>
  <data name="deletepartproducttype" xml:space="preserve">
    <value>CH Delete Part product Type</value>
  </data>
  <data name="DeleteParts" xml:space="preserve">
    <value>CH Delete Parts</value>
  </data>
  <data name="DeleteParty" xml:space="preserve">
    <value>CH Delete Party</value>
  </data>
  <data name="DeletePrefixSuffix" xml:space="preserve">
    <value>CH Delete Prefix Suffix</value>
  </data>
  <data name="deleteproduct" xml:space="preserve">
    <value>CH Delete Product</value>
  </data>
  <data name="deleteproductdetail" xml:space="preserve">
    <value>CH Delete Product Detail</value>
  </data>
  <data name="deleteproducttype" xml:space="preserve">
    <value>CH Delete Product Type</value>
  </data>
  <data name="DeleteQuotation" xml:space="preserve">
    <value>CH Delete Quotation</value>
  </data>
  <data name="DeleteRequest" xml:space="preserve">
    <value>CH Delete Request</value>
  </data>
  <data name="DeleteRole" xml:space="preserve">
    <value>CH Delete Role</value>
  </data>
  <data name="deleteServiceCharge" xml:space="preserve">
    <value>CH Delete Service Charge</value>
  </data>
  <data name="deleteServiceChargeDetails" xml:space="preserve">
    <value>CH Delete Service Charge Details</value>
  </data>
  <data name="DeleteServiceType" xml:space="preserve">
    <value>CH Delete Service Type</value>
  </data>
  <data name="DeleteSkills" xml:space="preserve">
    <value>CH Delete Skills</value>
  </data>
  <data name="DeleteSpecialization" xml:space="preserve">
    <value>CH Delete Specialization</value>
  </data>
  <data name="DeleteStep" xml:space="preserve">
    <value>CH Delete Step</value>
  </data>
  <data name="DeleteStepLink" xml:space="preserve">
    <value>CH Delete Step Link</value>
  </data>
  <data name="DeleteSundry" xml:space="preserve">
    <value>CH Delete Sundry</value>
  </data>
  <data name="DeleteTaxCode" xml:space="preserve">
    <value>CH Delete Tax Code</value>
  </data>
  <data name="DeleteTaxDetails" xml:space="preserve">
    <value>CH Delete Tax Details</value>
  </data>
  <data name="deletetaxstructure" xml:space="preserve">
    <value>CH Delete Tax Structure</value>
  </data>
  <data name="deletetaxtstructuredetails" xml:space="preserve">
    <value>CH Delete Tax Structure Details</value>
  </data>
  <data name="DeleteUser" xml:space="preserve">
    <value>CH Delete User</value>
  </data>
  <data name="deletewarrantydetails" xml:space="preserve">
    <value>CH Delete Warranty Details</value>
  </data>
  <data name="DeleteWorkDetails" xml:space="preserve">
    <value>CH Delete Work Details</value>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>CH Delivery Date</value>
  </data>
  <data name="Department" xml:space="preserve">
    <value>CH Department</value>
  </data>
  <data name="Dependencyfoundcannotdeletetherecords" xml:space="preserve">
    <value>CH Dependency found cannot delete the records</value>
  </data>
  <data name="description" xml:space="preserve">
    <value>CH Description</value>
  </data>
  <data name="Designation" xml:space="preserve">
    <value>CH Designation</value>
  </data>
  <data name="DestinationColumns" xml:space="preserve">
    <value>CH Destination Columns</value>
  </data>
  <data name="Detail" xml:space="preserve">
    <value>CH Detail</value>
  </data>
  <data name="DeviationHours" xml:space="preserve">
    <value>CH Deviation Hours</value>
  </data>
  <data name="DeviationPercentage" xml:space="preserve">
    <value>CH Deviation%</value>
  </data>
  <data name="Discount" xml:space="preserve">
    <value>CH Discount</value>
  </data>
  <data name="Discountamount" xml:space="preserve">
    <value>CH Discount Amount</value>
  </data>
  <data name="DiscountedAmount" xml:space="preserve">
    <value>CH Discounted Amount</value>
  </data>
  <data name="DiscountPercentage" xml:space="preserve">
    <value>CH Discount Percentage</value>
  </data>
  <data name="DiscountShouldbeLessThan" xml:space="preserve">
    <value>CH Discount should be less than</value>
  </data>
  <data name="DivideByZeroException" xml:space="preserve">
    <value>CH Application error occured</value>
  </data>
  <data name="DocumentExport" xml:space="preserve">
    <value>CH DocumentExport</value>
  </data>
  <data name="DonotEnterSpace" xml:space="preserve">
    <value>CH Do not enter space</value>
  </data>
  <data name="DoyouwanttoaddthisSerialNumber" xml:space="preserve">
    <value>CH Do you want to add this serial number</value>
  </data>
  <data name="Doyouwanttochangetheassociation" xml:space="preserve">
    <value>CH Do you want to change the association?</value>
  </data>
  <data name="Doyouwanttocreatenewversion" xml:space="preserve">
    <value>CH Do you want to create New Version?</value>
  </data>
  <data name="DueDate" xml:space="preserve">
    <value>CH Due Date</value>
  </data>
  <data name="DueDatecannotbeBlank" xml:space="preserve">
    <value>CH Due date cannot be blank</value>
  </data>
  <data name="DueRange" xml:space="preserve">
    <value>CH Due Range</value>
  </data>
  <data name="duplicatebranch" xml:space="preserve">
    <value>CH Duplicate Branch</value>
  </data>
  <data name="duplicatedate" xml:space="preserve">
    <value>CH Duplicate Date</value>
  </data>
  <data name="DuplicateDescription" xml:space="preserve">
    <value>CH Duplicate description</value>
  </data>
  <data name="DuplicateEmailsFound" xml:space="preserve">
    <value>CH Duplicate emails found</value>
  </data>
  <data name="Duplicateentries" xml:space="preserve">
    <value>CH Duplicate entries</value>
  </data>
  <data name="Duplicateentriesof" xml:space="preserve">
    <value>CH Duplicate entries of</value>
  </data>
  <data name="duplicatemodel" xml:space="preserve">
    <value>CH Duplicate model</value>
  </data>
  <data name="DuplicatePhoneNumbersFound" xml:space="preserve">
    <value>CH Duplicate phone numbers found</value>
  </data>
  <data name="duplicateproducttype" xml:space="preserve">
    <value>CH Duplicate product type</value>
  </data>
  <data name="duplicatesecondarysegment" xml:space="preserve">
    <value>CH Duplicate secondary segment</value>
  </data>
  <data name="duplicateservicedate" xml:space="preserve">
    <value>CH Duplicate service date</value>
  </data>
  <data name="duplicateservicetype" xml:space="preserve">
    <value>CH Duplicate service type</value>
  </data>
  <data name="DuplicateServiceTypeisnotallowed" xml:space="preserve">
    <value>CH Duplicate service type is not allowed</value>
  </data>
  <data name="duplicatestate" xml:space="preserve">
    <value>CH Duplicate state</value>
  </data>
  <data name="DuplicateSundryDescriptionisnotAllowed" xml:space="preserve">
    <value>CH Duplicate sundry description is not allowed</value>
  </data>
  <data name="edit" xml:space="preserve">
    <value>CH Edit</value>
  </data>
  <data name="EditAction" xml:space="preserve">
    <value>CH Edit Action</value>
  </data>
  <data name="EditCompany" xml:space="preserve">
    <value>CH Edit Company</value>
  </data>
  <data name="editcomponentdetails" xml:space="preserve">
    <value>CH Edit Component Details</value>
  </data>
  <data name="EditEvents" xml:space="preserve">
    <value>CH Edit Events</value>
  </data>
  <data name="EditJobCard" xml:space="preserve">
    <value>CH Edit Job Card</value>
  </data>
  <data name="editmodel" xml:space="preserve">
    <value>CH Edit Model</value>
  </data>
  <data name="editoperation" xml:space="preserve">
    <value>CH Edit Operation</value>
  </data>
  <data name="EditPartsMaster" xml:space="preserve">
    <value>CH Edit Parts Master</value>
  </data>
  <data name="editproduct" xml:space="preserve">
    <value>CH Edit Product</value>
  </data>
  <data name="editproducttype" xml:space="preserve">
    <value>CH Edit Product Type</value>
  </data>
  <data name="editsecondarysegment" xml:space="preserve">
    <value>CH Edit Secondary Segment</value>
  </data>
  <data name="editsevicecharges" xml:space="preserve">
    <value>CH Edit Service Charges</value>
  </data>
  <data name="editsiteaddress" xml:space="preserve">
    <value>CH Edit Site Address</value>
  </data>
  <data name="editstate" xml:space="preserve">
    <value>CH Edit State</value>
  </data>
  <data name="edittaxstructure" xml:space="preserve">
    <value>CH Edit Tax Structure</value>
  </data>
  <data name="editwarrantydeatils" xml:space="preserve">
    <value>CH Edit Warranty Deatils</value>
  </data>
  <data name="effectivefrom" xml:space="preserve">
    <value>CH Effective From</value>
  </data>
  <data name="EightHour" xml:space="preserve">
    <value>CH Less Than 8 Hour</value>
  </data>
  <data name="EightToSixteenHours" xml:space="preserve">
    <value>CH 8 To 16 Hours</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>CH Email</value>
  </data>
  <data name="EmailToAddresse" xml:space="preserve">
    <value>CH Email To Addresse</value>
  </data>
  <data name="EmailToCustomer" xml:space="preserve">
    <value>CH Email To Customer</value>
  </data>
  <data name="Employee" xml:space="preserve">
    <value>CH Employee</value>
  </data>
  <data name="EmployeeBranch" xml:space="preserve">
    <value>CH Employee - Branch</value>
  </data>
  <data name="EmployeeDetails" xml:space="preserve">
    <value>CH Employee Details</value>
  </data>
  <data name="EmployeeID" xml:space="preserve">
    <value>CH Employee Code</value>
  </data>
  <data name="EmployeeIDisalreadyused" xml:space="preserve">
    <value>CH Employee code is already used</value>
  </data>
  <data name="EmployeeisalreadyassociatedwiththeBranch" xml:space="preserve">
    <value>CH Employee is already associated with the branch</value>
  </data>
  <data name="EmployeeisalreadyassociatedwiththeSpecialization" xml:space="preserve">
    <value>CH Employee is already associated with the specialization</value>
  </data>
  <data name="EmployeeName" xml:space="preserve">
    <value>CH Employee</value>
  </data>
  <data name="EmployeeNotFound" xml:space="preserve">
    <value>CH Employee not found</value>
  </data>
  <data name="EmployeeSkills" xml:space="preserve">
    <value>CH Skills</value>
  </data>
  <data name="EmployeeSpecialization" xml:space="preserve">
    <value>CH Specialization</value>
  </data>
  <data name="EndDatecannotbelessthanStartDate" xml:space="preserve">
    <value>CH End date cannot be less than start date</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>CH English</value>
  </data>
  <data name="EnquiryDate" xml:space="preserve">
    <value>CH Enquiry Date</value>
  </data>
  <data name="EnquiryNumber" xml:space="preserve">
    <value>CH Enquiry Number</value>
  </data>
  <data name="EnterCode" xml:space="preserve">
    <value>CH Enter Code</value>
  </data>
  <data name="EnterDescription" xml:space="preserve">
    <value>CH Enter Description</value>
  </data>
  <data name="EnteredNumberdoesnotbelongstocustomerorprospect" xml:space="preserve">
    <value>CH Entered number doesnot belongs to customer or prospect</value>
  </data>
  <data name="enterfromdate" xml:space="preserve">
    <value>CH Enter From Date</value>
  </data>
  <data name="EnterMasterName" xml:space="preserve">
    <value>CH Enter master name</value>
  </data>
  <data name="enterNonTaxableothercharges1" xml:space="preserve">
    <value>CH Enter non taxable other charges 1</value>
  </data>
  <data name="enterNonTaxableothercharges2" xml:space="preserve">
    <value>CH Enter non taxable other charges 2</value>
  </data>
  <data name="enterTaxableothercharges1" xml:space="preserve">
    <value>CH Enter taxable other charges 1</value>
  </data>
  <data name="enterTaxableothercharges2" xml:space="preserve">
    <value>CH Enter taxable other charges 2</value>
  </data>
  <data name="enteryear" xml:space="preserve">
    <value>CH Enter Year</value>
  </data>
  <data name="EntryTaxPercentage" xml:space="preserve">
    <value>CH Entry Tax Percentage</value>
  </data>
  <data name="Equal" xml:space="preserve">
    <value>CH Equal</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>CH Error</value>
  </data>
  <data name="ErrorinUploadedPartsPleaseOpenExcel" xml:space="preserve">
    <value>CH Error in uploaded parts. Please open excel</value>
  </data>
  <data name="ErrorOccuredwhileLocking" xml:space="preserve">
    <value>CH Error occured while locking</value>
  </data>
  <data name="ErrorSaving" xml:space="preserve">
    <value>CH Error Saving</value>
  </data>
  <data name="EventName" xml:space="preserve">
    <value>CH Event Name</value>
  </data>
  <data name="Events" xml:space="preserve">
    <value>CH Events</value>
  </data>
  <data name="Excel" xml:space="preserve">
    <value>CH Excel</value>
  </data>
  <data name="Export" xml:space="preserve">
    <value>CH Export</value>
  </data>
  <data name="ExportAction" xml:space="preserve">
    <value>CH Export Action</value>
  </data>
  <data name="ExporttoDocument" xml:space="preserve">
    <value>CH Export to Document</value>
  </data>
  <data name="FailedtosavenewContactPerson" xml:space="preserve">
    <value>CH Failed to save new contact person</value>
  </data>
  <data name="FailedtosavenewParty" xml:space="preserve">
    <value>CH Failed to save new party</value>
  </data>
  <data name="FailedtosavenewSerialNumber" xml:space="preserve">
    <value>CH Failed to save new serial number</value>
  </data>
  <data name="FailedToSaveSerial" xml:space="preserve">
    <value>CH Failed to save serial</value>
  </data>
  <data name="FAX" xml:space="preserve">
    <value>CH FAX</value>
  </data>
  <data name="February" xml:space="preserve">
    <value>CH Feb</value>
  </data>
  <data name="Fieldshighlightedaremandatory" xml:space="preserve">
    <value>CH Fields highlighted are mandatory</value>
  </data>
  <data name="FieldsmarkedwithStararemandatory" xml:space="preserve">
    <value>CH Fields marked with * are mandatory</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>CH Filter</value>
  </data>
  <data name="FilterAllQueBranch" xml:space="preserve">
    <value>CH Filter All Que Branch Specific ?</value>
  </data>
  <data name="FilterCriteria" xml:space="preserve">
    <value>CH Filter Criteria</value>
  </data>
  <data name="FinancialYear" xml:space="preserve">
    <value>CH Financial Year</value>
  </data>
  <data name="financialyearalredyselected" xml:space="preserve">
    <value>CH Financial year already selected</value>
  </data>
  <data name="FinancialyearcannotbeGreaterthannextrowsfinancialyear" xml:space="preserve">
    <value>CH Financial year cannot be greater than next rows financial year</value>
  </data>
  <data name="Financialyearcannotbelessthanpreviousrowsfinancialyear" xml:space="preserve">
    <value>CH Financial year cannot be less than previous rows financial year</value>
  </data>
  <data name="For" xml:space="preserve">
    <value>CH For</value>
  </data>
  <data name="forgotpassword" xml:space="preserve">
    <value>CH Forgot Password?</value>
  </data>
  <data name="formula" xml:space="preserve">
    <value>CH Formula</value>
  </data>
  <data name="formulasummary" xml:space="preserve">
    <value>CH Formula Summary</value>
  </data>
  <data name="FourtyEightToNintyHours" xml:space="preserve">
    <value>CH 48 To 90 Hours</value>
  </data>
  <data name="FreeHours" xml:space="preserve">
    <value>CH Free Hours</value>
  </data>
  <data name="freestock" xml:space="preserve">
    <value>CH Free Stock</value>
  </data>
  <data name="Friday" xml:space="preserve">
    <value>CH Friday</value>
  </data>
  <data name="From" xml:space="preserve">
    <value>CH From</value>
  </data>
  <data name="fromdate" xml:space="preserve">
    <value>CH From Date</value>
  </data>
  <data name="FromDateandTodatecannotbeEmpty" xml:space="preserve">
    <value>CH FromDate and ToDate cannot be empty</value>
  </data>
  <data name="FromDatecannotbegreaterthanToDate" xml:space="preserve">
    <value>CH From Date cannot be greater than To Date</value>
  </data>
  <data name="fromdatecannotbegreaterthentodate" xml:space="preserve">
    <value>CH FromDate Cannot be greater than Todate</value>
  </data>
  <data name="fromdatecannotbegreatorthantodate" xml:space="preserve">
    <value>CH FromDate cannot be greater than ToDate</value>
  </data>
  <data name="Fromdatecannotbelessthanorequaltopreviousrowtodate" xml:space="preserve">
    <value>CH From date cannot be less than or equal to previous rows to date</value>
  </data>
  <data name="FromDatecannotbelessthanToDate" xml:space="preserve">
    <value>CH FromDate cannot be less than ToDate</value>
  </data>
  <data name="fromdatecannotbelessthencurrentdate" xml:space="preserve">
    <value>CH FromDate cannot be less than current date</value>
  </data>
  <data name="fromdatemustbegreaterthanorequaltoissuedate" xml:space="preserve">
    <value>CH FromDate must be greater than or equal to Issue date</value>
  </data>
  <data name="fromdatemustbelesserthentodate" xml:space="preserve">
    <value>CH FromDate must be lesser than ToDate</value>
  </data>
  <data name="FromStep" xml:space="preserve">
    <value>CH From Step</value>
  </data>
  <data name="FromTimecannotbegreaterthanToTime" xml:space="preserve">
    <value>CH From Time cannot be greater than To Time</value>
  </data>
  <data name="FunctionGroup" xml:space="preserve">
    <value>CH Function Group</value>
  </data>
  <data name="FunctionGroupHeader" xml:space="preserve">
    <value>CH Function Group Header</value>
  </data>
  <data name="FunctionGroupID" xml:space="preserve">
    <value>CH Function Group ID</value>
  </data>
  <data name="FunctionGroupName" xml:space="preserve">
    <value>CH Function Group Name</value>
  </data>
  <data name="FunctionGroupNative" xml:space="preserve">
    <value>CH Function Group Native</value>
  </data>
  <data name="FunctionGroupOperations" xml:space="preserve">
    <value>CH Function Group Operations</value>
  </data>
  <data name="GenerateReport" xml:space="preserve">
    <value>CH Generate Report</value>
  </data>
  <data name="GraphCategorySelection" xml:space="preserve">
    <value>CH Graph Category Selection</value>
  </data>
  <data name="GraphType" xml:space="preserve">
    <value>CH Graph Type</value>
  </data>
  <data name="GreaterThan" xml:space="preserve">
    <value>CH Greater Than</value>
  </data>
  <data name="GroupQue" xml:space="preserve">
    <value>CH Group Queue</value>
  </data>
  <data name="GroupQueue" xml:space="preserve">
    <value>CH Group Queue</value>
  </data>
  <data name="Header" xml:space="preserve">
    <value>CH Header</value>
  </data>
  <data name="Heading" xml:space="preserve">
    <value>CH Heading</value>
  </data>
  <data name="high" xml:space="preserve">
    <value>CH High</value>
  </data>
  <data name="HighlightedFieldsareMandatory" xml:space="preserve">
    <value>CH Highlighted fields are mandatory</value>
  </data>
  <data name="HMR" xml:space="preserve">
    <value>CH HMR</value>
  </data>
  <data name="Hold" xml:space="preserve">
    <value>CH Hold</value>
  </data>
  <data name="Holidays" xml:space="preserve">
    <value>CH Holidays</value>
  </data>
  <data name="ID" xml:space="preserve">
    <value>CH ID</value>
  </data>
  <data name="Import" xml:space="preserve">
    <value>CH Import</value>
  </data>
  <data name="ImportAction" xml:space="preserve">
    <value>CH Import Action</value>
  </data>
  <data name="ImportedSuccessfully" xml:space="preserve">
    <value>CH Imported successfully</value>
  </data>
  <data name="ImportIntoDatabase" xml:space="preserve">
    <value>CH Import Into Database</value>
  </data>
  <data name="ImportParts" xml:space="preserve">
    <value>CH Import Parts</value>
  </data>
  <data name="IndexOutOfRangeException" xml:space="preserve">
    <value>CH Application error occured</value>
  </data>
  <data name="InProgress" xml:space="preserve">
    <value>CH In Progress</value>
  </data>
  <data name="InProgressCount" xml:space="preserve">
    <value>CH In Progress Count</value>
  </data>
  <data name="InsertedSuccessfully" xml:space="preserve">
    <value>CH Inserted successfully</value>
  </data>
  <data name="Internal" xml:space="preserve">
    <value>CH Internal</value>
  </data>
  <data name="Invalid" xml:space="preserve">
    <value>CH Invalid</value>
  </data>
  <data name="InvalidBranchSelection" xml:space="preserve">
    <value>CH Prefixsuffix is created as company specific. Cannot change to Branch.</value>
  </data>
  <data name="InvalidCastException" xml:space="preserve">
    <value>CH Application error occured</value>
  </data>
  <data name="InvalidCompanySelection" xml:space="preserve">
    <value>CH Prefixsuffix is created as Branch specific. Cannot change to Company. </value>
  </data>
  <data name="InvalidDate" xml:space="preserve">
    <value>CH Invalid date</value>
  </data>
  <data name="InvalidEmail" xml:space="preserve">
    <value>CH Invalid email</value>
  </data>
  <data name="InvalidFile" xml:space="preserve">
    <value>CH Invalid file</value>
  </data>
  <data name="InvalidMobile" xml:space="preserve">
    <value>CH Invalid Mobile</value>
  </data>
  <data name="InvalidModel" xml:space="preserve">
    <value>CH Invalid model</value>
  </data>
  <data name="invalidmodelormodelisinactive" xml:space="preserve">
    <value>CH Invalid model or model is inactive</value>
  </data>
  <data name="InvalidName" xml:space="preserve">
    <value>CH Invalid party name</value>
  </data>
  <data name="InvalidOperationException" xml:space="preserve">
    <value>CH Database error occured</value>
  </data>
  <data name="invalidpartnumberorpartnumberisinactive" xml:space="preserve">
    <value>CH Invalid part number or part number is inactive</value>
  </data>
  <data name="InvalidPhone" xml:space="preserve">
    <value>CH Invalid Phone</value>
  </data>
  <data name="InvalidPhoneNo" xml:space="preserve">
    <value>CH Invalid Mobile No</value>
  </data>
  <data name="InvalidProduct" xml:space="preserve">
    <value>CH Invalid product</value>
  </data>
  <data name="InvalidProductUniqueNumber" xml:space="preserve">
    <value>CH Invalid Product Unique Identifier or Product is not active</value>
  </data>
  <data name="InvalidRegisteredMobileNumber" xml:space="preserve">
    <value>CH Invalid registered mobile number</value>
  </data>
  <data name="invalidselection" xml:space="preserve">
    <value>CH Invalid selection</value>
  </data>
  <data name="InvalidServiceRequestNumber" xml:space="preserve">
    <value>CH Invalid Service Request Number</value>
  </data>
  <data name="InValidUniqueIdentificationNumber" xml:space="preserve">
    <value>CH Invalid unique identification number</value>
  </data>
  <data name="IsActive" xml:space="preserve">
    <value>CH Is Active?</value>
  </data>
  <data name="IsAdmin" xml:space="preserve">
    <value>CH Is Admin</value>
  </data>
  <data name="isbaseamountincluded" xml:space="preserve">
    <value>CH Is Base Amount Included?</value>
  </data>
  <data name="IsCompanySpecific" xml:space="preserve">
    <value>CH Is Company Specific</value>
  </data>
  <data name="iscomponent" xml:space="preserve">
    <value>CH Is Component</value>
  </data>
  <data name="IsCustomer" xml:space="preserve">
    <value>CH IsCustomer</value>
  </data>
  <data name="IsDefaultContact" xml:space="preserve">
    <value>CH Is Default Contact ?</value>
  </data>
  <data name="IsExternal" xml:space="preserve">
    <value>CH Is External</value>
  </data>
  <data name="isHazardous" xml:space="preserve">
    <value>CH Is Hazardous</value>
  </data>
  <data name="IsHeadOffice" xml:space="preserve">
    <value>CH Is Head Office</value>
  </data>
  <data name="isinactive" xml:space="preserve">
    <value>CH is Inactive</value>
  </data>
  <data name="IsOperationCompleted" xml:space="preserve">
    <value>CH Is Operation Completed</value>
  </data>
  <data name="issueddate" xml:space="preserve">
    <value>CH Issued Date</value>
  </data>
  <data name="issueddatesholudbelessthanorequaltocurrentdate" xml:space="preserve">
    <value>CH Issued date sholud be less than or equal to current date</value>
  </data>
  <data name="IsUnderBreakDown" xml:space="preserve">
    <value>CH Is Under Break Down?</value>
  </data>
  <data name="isunderwarranty" xml:space="preserve">
    <value>CH Is Under Warranty?</value>
  </data>
  <data name="isversionallowed" xml:space="preserve">
    <value>CH Is Version Allowed?</value>
  </data>
  <data name="IsVersionEnabled" xml:space="preserve">
    <value>CH Is Version Enabled ?</value>
  </data>
  <data name="January" xml:space="preserve">
    <value>CH Jan</value>
  </data>
  <data name="Jobamendedwillcreatenewversionwanttoproceed" xml:space="preserve">
    <value>CH Job amended, will create new version want to proceed?</value>
  </data>
  <data name="JobCard" xml:space="preserve">
    <value>CH Job Card</value>
  </data>
  <data name="JobCardAbandonReason" xml:space="preserve">
    <value>CH Job Card Abandon Reason</value>
  </data>
  <data name="JobcardArchived" xml:space="preserve">
    <value>CH Job card Archived</value>
  </data>
  <data name="JobCardClosureDate" xml:space="preserve">
    <value>CH Job Card Closure Date</value>
  </data>
  <data name="JobCardCushionHours" xml:space="preserve">
    <value>CH Job Card Cushion Hours</value>
  </data>
  <data name="JobCardDate" xml:space="preserve">
    <value>CH Job Card Date</value>
  </data>
  <data name="JobCardDelayReason" xml:space="preserve">
    <value>CH Job Card Delay Reason</value>
  </data>
  <data name="JobCardFieldSearch" xml:space="preserve">
    <value>CH Job Card Field Search</value>
  </data>
  <data name="JobCardisalreadycreatedforthisServiceRequestNumber" xml:space="preserve">
    <value>CH Job card is already created for this service request number</value>
  </data>
  <data name="JobCardNumber" xml:space="preserve">
    <value>CH Job Card Number</value>
  </data>
  <data name="JobcardNumbernotfound" xml:space="preserve">
    <value>CH Job card number not found</value>
  </data>
  <data name="JobCardNumberSearch" xml:space="preserve">
    <value>CH Job Card Number Search</value>
  </data>
  <data name="JobCardPendingCount" xml:space="preserve">
    <value>CH Job Card Pending Count</value>
  </data>
  <data name="JobCardStatus" xml:space="preserve">
    <value>CH Job Card Status</value>
  </data>
  <data name="JobCardSummary" xml:space="preserve">
    <value>CH Job Card Summary</value>
  </data>
  <data name="JobCardVersion" xml:space="preserve">
    <value>CH Version</value>
  </data>
  <data name="JobCardWIPCount" xml:space="preserve">
    <value>CH Job Card WIP Count</value>
  </data>
  <data name="JobDescription" xml:space="preserve">
    <value>CH Job Description</value>
  </data>
  <data name="JobEndDate" xml:space="preserve">
    <value>CH Job End Date</value>
  </data>
  <data name="JobPriority" xml:space="preserve">
    <value>CH Job Priority</value>
  </data>
  <data name="JobSiteAddress" xml:space="preserve">
    <value>CH Job Site Address</value>
  </data>
  <data name="JobStartDate" xml:space="preserve">
    <value>CH Job Start Date</value>
  </data>
  <data name="JoinedTables" xml:space="preserve">
    <value>CH Joined Tables</value>
  </data>
  <data name="JoinWith" xml:space="preserve">
    <value>CH  Join With</value>
  </data>
  <data name="July" xml:space="preserve">
    <value>CH Jul</value>
  </data>
  <data name="June" xml:space="preserve">
    <value>CH Jun</value>
  </data>
  <data name="Landline" xml:space="preserve">
    <value>CH Landline</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>CH Language</value>
  </data>
  <data name="LanguageName" xml:space="preserve">
    <value>CH Language</value>
  </data>
  <data name="LessThan" xml:space="preserve">
    <value>CH Less Than</value>
  </data>
  <data name="Like" xml:space="preserve">
    <value>CH Like</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>CH Loading</value>
  </data>
  <data name="Local" xml:space="preserve">
    <value>CH Local</value>
  </data>
  <data name="Locale" xml:space="preserve">
    <value>CH Locale</value>
  </data>
  <data name="LocaleDetails" xml:space="preserve">
    <value>CH Locale Details</value>
  </data>
  <data name="LocaledetailsarenotavaliableforBrand" xml:space="preserve">
    <value>CH Locale details are not avaliable for brand</value>
  </data>
  <data name="LocaledetailsarenotavaliableforProductModel" xml:space="preserve">
    <value>CH Locale details are not avaliable for product model</value>
  </data>
  <data name="LocaledetailsarenotavaliableforProductType" xml:space="preserve">
    <value>CH Locale details are not avaliable for product type</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>CH Location</value>
  </data>
  <data name="Lock" xml:space="preserve">
    <value>CH Lock</value>
  </data>
  <data name="locked" xml:space="preserve">
    <value>CH Locked</value>
  </data>
  <data name="lockedby" xml:space="preserve">
    <value>CH Locked By</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>CH Login</value>
  </data>
  <data name="LoginID" xml:space="preserve">
    <value>CH Login ID</value>
  </data>
  <data name="LogoName" xml:space="preserve">
    <value>CH Logo Name</value>
  </data>
  <data name="low" xml:space="preserve">
    <value>CH Low</value>
  </data>
  <data name="Manager" xml:space="preserve">
    <value>CH Manager</value>
  </data>
  <data name="mandatoryservices" xml:space="preserve">
    <value>CH Mandatory Services</value>
  </data>
  <data name="Manufacturer" xml:space="preserve">
    <value>CH Manufacturer</value>
  </data>
  <data name="MapColumns" xml:space="preserve">
    <value>CH Map Columns</value>
  </data>
  <data name="MappedColumns" xml:space="preserve">
    <value>CH Mapped Columns</value>
  </data>
  <data name="March" xml:space="preserve">
    <value>CH Mar</value>
  </data>
  <data name="MasterExists" xml:space="preserve">
    <value>CH Master already exists</value>
  </data>
  <data name="MasterID" xml:space="preserve">
    <value>CH Master ID</value>
  </data>
  <data name="MasterName" xml:space="preserve">
    <value>CH Master Name</value>
  </data>
  <data name="May" xml:space="preserve">
    <value>CH May</value>
  </data>
  <data name="medium" xml:space="preserve">
    <value>CH Medium</value>
  </data>
  <data name="MenuDetail" xml:space="preserve">
    <value>CH Menu Details</value>
  </data>
  <data name="MenuDetails" xml:space="preserve">
    <value>CH Menu Details</value>
  </data>
  <data name="MenuName" xml:space="preserve">
    <value>CH Menu Name</value>
  </data>
  <data name="MenuNamecannotbeblank" xml:space="preserve">
    <value>CH Menu name can not be blank</value>
  </data>
  <data name="MenuPath" xml:space="preserve">
    <value>CH Menu Path</value>
  </data>
  <data name="Mobile" xml:space="preserve">
    <value>CH Mobile</value>
  </data>
  <data name="MobileNumber" xml:space="preserve">
    <value>CH Mobile Number</value>
  </data>
  <data name="model" xml:space="preserve">
    <value>CH Model</value>
  </data>
  <data name="modelenglish" xml:space="preserve">
    <value>CH Model English</value>
  </data>
  <data name="ModelFieldSearch" xml:space="preserve">
    <value>CH Model Field Search</value>
  </data>
  <data name="modelisinactive" xml:space="preserve">
    <value>CH Model is inactive</value>
  </data>
  <data name="modellocale" xml:space="preserve">
    <value>CH Model Locale</value>
  </data>
  <data name="modelmaster" xml:space="preserve">
    <value>CH Model Master</value>
  </data>
  <data name="modelname" xml:space="preserve">
    <value>CH Model Name</value>
  </data>
  <data name="modelnamealreadyexists" xml:space="preserve">
    <value>CH Model name already exists</value>
  </data>
  <data name="modelnotfound" xml:space="preserve">
    <value>CH Model not found</value>
  </data>
  <data name="ModelSearch" xml:space="preserve">
    <value>CH Model Search</value>
  </data>
  <data name="Module" xml:space="preserve">
    <value>CH Module</value>
  </data>
  <data name="ModuleName" xml:space="preserve">
    <value>CH Module Name</value>
  </data>
  <data name="ModuleNameCannotbeblank" xml:space="preserve">
    <value>CH Module name cannot be blank</value>
  </data>
  <data name="Monday" xml:space="preserve">
    <value>CH Monday</value>
  </data>
  <data name="month" xml:space="preserve">
    <value>CH Month</value>
  </data>
  <data name="MyQue" xml:space="preserve">
    <value>CH My Queue</value>
  </data>
  <data name="MyQueue" xml:space="preserve">
    <value>CH My Queue</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>CH Name</value>
  </data>
  <data name="NintyHour" xml:space="preserve">
    <value>CH More Than 90 Hour</value>
  </data>
  <data name="no" xml:space="preserve">
    <value>CH No</value>
  </data>
  <data name="NoChangesMade" xml:space="preserve">
    <value>CH No changes made</value>
  </data>
  <data name="NochangesmadetoSave" xml:space="preserve">
    <value>CH No changes made to save</value>
  </data>
  <data name="NomatchingrecordfoundDoyouwanttoadd" xml:space="preserve">
    <value>CH Party is InActive or No matching record found, Do you want to add this party</value>
  </data>
  <data name="NonTaxable" xml:space="preserve">
    <value>CH Non Taxable</value>
  </data>
  <data name="NonTaxableothercharges1" xml:space="preserve">
    <value>CH Non Taxable other charges 1</value>
  </data>
  <data name="NonTaxableothercharges1Amount" xml:space="preserve">
    <value>CH Non Taxable other charges 1 Amount</value>
  </data>
  <data name="NonTaxableothercharges2" xml:space="preserve">
    <value>CH Non Taxable other charges 2</value>
  </data>
  <data name="NonTaxableothercharges2Amount" xml:space="preserve">
    <value>CH Non Taxable other charges 2 Amount</value>
  </data>
  <data name="NoOfSRCompleted" xml:space="preserve">
    <value>CH Number Of Service Request Completed</value>
  </data>
  <data name="NoOfSRRecieved" xml:space="preserve">
    <value>CH Number Of Service Request Recieved</value>
  </data>
  <data name="Noproductisassociatedwithselectedcustomer" xml:space="preserve">
    <value>CH No product is associated with selected customer</value>
  </data>
  <data name="Norecordstoview" xml:space="preserve">
    <value>CH Norecordstoview</value>
  </data>
  <data name="NotEqual" xml:space="preserve">
    <value>CH Not Equal</value>
  </data>
  <data name="NotFound" xml:space="preserve">
    <value>CH Not Found</value>
  </data>
  <data name="November" xml:space="preserve">
    <value>CH Nov</value>
  </data>
  <data name="NullReferenceException" xml:space="preserve">
    <value>CH Application error occured</value>
  </data>
  <data name="number" xml:space="preserve">
    <value>CH Number</value>
  </data>
  <data name="ObjectDescription" xml:space="preserve">
    <value>CH Object Description</value>
  </data>
  <data name="ObjectDescriptioncannotbeblank" xml:space="preserve">
    <value>CH Object description cannot be blank</value>
  </data>
  <data name="ObjectMaster" xml:space="preserve">
    <value>CH Object</value>
  </data>
  <data name="ObjectName" xml:space="preserve">
    <value>CH Object Name</value>
  </data>
  <data name="ObjectNamecannotbeblank" xml:space="preserve">
    <value>CH Object name cannot be blank</value>
  </data>
  <data name="ObjectssavedSuccessfully" xml:space="preserve">
    <value>CH Objects saved successfully</value>
  </data>
  <data name="October" xml:space="preserve">
    <value>CH Oct</value>
  </data>
  <data name="of" xml:space="preserve">
    <value>CH of</value>
  </data>
  <data name="OnHoldCount" xml:space="preserve">
    <value>CH OnHold Count</value>
  </data>
  <data name="onlyactivecustomerdeatilscanbeedited" xml:space="preserve">
    <value>CH Only active customer deatils can be edited</value>
  </data>
  <data name="onlyactivewarrantycanbeedited" xml:space="preserve">
    <value>CH Only active warranty can be edited</value>
  </data>
  <data name="open" xml:space="preserve">
    <value>CH Open</value>
  </data>
  <data name="OpenReport" xml:space="preserve">
    <value>CH Open Report</value>
  </data>
  <data name="operation" xml:space="preserve">
    <value>CH Operation</value>
  </data>
  <data name="OperationandEmployeeisalreadyassociated" xml:space="preserve">
    <value>CH Operation and employee is already associated</value>
  </data>
  <data name="OperationCode" xml:space="preserve">
    <value>CH Operation Code</value>
  </data>
  <data name="Operationcodealreadyexists" xml:space="preserve">
    <value>CH Operation code already exists</value>
  </data>
  <data name="OperationCodeAlreadySelected" xml:space="preserve">
    <value>CH Operation code aready selected</value>
  </data>
  <data name="OperationCodeNotFound" xml:space="preserve">
    <value>CH Operation code not found</value>
  </data>
  <data name="OperationDescription" xml:space="preserve">
    <value>CH Operation Description</value>
  </data>
  <data name="OperationDetails" xml:space="preserve">
    <value>CH Operation Details</value>
  </data>
  <data name="OperationDeviationReport" xml:space="preserve">
    <value>CH Operation Deviation Report</value>
  </data>
  <data name="OperationEmployeeDetails" xml:space="preserve">
    <value>CH Operation Employee Details</value>
  </data>
  <data name="OperationEndDate" xml:space="preserve">
    <value>CH Operation End Date</value>
  </data>
  <data name="OperationEndDateCannotbelessthanoperationStartDate" xml:space="preserve">
    <value>CH Operation end date cannot be less than operation start date</value>
  </data>
  <data name="operationenglish" xml:space="preserve">
    <value>CH Operation English</value>
  </data>
  <data name="OperationFieldSearch" xml:space="preserve">
    <value>CH Operation Field Search</value>
  </data>
  <data name="operationheader" xml:space="preserve">
    <value>CH Operation Header</value>
  </data>
  <data name="OperationHours" xml:space="preserve">
    <value>CH Operation Hours</value>
  </data>
  <data name="Operationisalreadyassociatedwithanotheremployee" xml:space="preserve">
    <value>CH Operation is already associated with another employee</value>
  </data>
  <data name="operationlocale" xml:space="preserve">
    <value>CH Operation Locale</value>
  </data>
  <data name="operationmaster" xml:space="preserve">
    <value>CH Operation Master</value>
  </data>
  <data name="OperationStartDate" xml:space="preserve">
    <value>CH Operation Start Date</value>
  </data>
  <data name="Operator" xml:space="preserve">
    <value>CH Operator</value>
  </data>
  <data name="OR" xml:space="preserve">
    <value>CH OR</value>
  </data>
  <data name="OtherDetail" xml:space="preserve">
    <value>CH Other Detail</value>
  </data>
  <data name="OtherDetails" xml:space="preserve">
    <value>CH Other Details</value>
  </data>
  <data name="Others" xml:space="preserve">
    <value>CH Others</value>
  </data>
  <data name="OutOfMemoryException" xml:space="preserve">
    <value>CH Application error occured</value>
  </data>
  <data name="ParentCompany" xml:space="preserve">
    <value>CH Parent Company</value>
  </data>
  <data name="parentcompanyoperationcannotbedeleted" xml:space="preserve">
    <value>CH Parent company operation cannot be deleted</value>
  </data>
  <data name="parentcompanyoperationcannotbeedited" xml:space="preserve">
    <value>CH Parent company operation cannot be edited</value>
  </data>
  <data name="ParentMenu" xml:space="preserve">
    <value>CH Parent Menu</value>
  </data>
  <data name="partcategory" xml:space="preserve">
    <value>CH Part Category</value>
  </data>
  <data name="partdescription" xml:space="preserve">
    <value>CH Part Description</value>
  </data>
  <data name="partfunctiongroup" xml:space="preserve">
    <value>CH Part Function Group</value>
  </data>
  <data name="Partner" xml:space="preserve">
    <value>CH Partner</value>
  </data>
  <data name="PartnerName" xml:space="preserve">
    <value>CH Partner</value>
  </data>
  <data name="partnumber" xml:space="preserve">
    <value>CH Part Number</value>
  </data>
  <data name="partnumberalreadyexists" xml:space="preserve">
    <value>CH Part number already exists</value>
  </data>
  <data name="PartNumbernotfound" xml:space="preserve">
    <value>CH Part number not found</value>
  </data>
  <data name="partnumbersearch" xml:space="preserve">
    <value>CH Part Number Search</value>
  </data>
  <data name="partprice" xml:space="preserve">
    <value>CH Part Price</value>
  </data>
  <data name="partpricepdetails" xml:space="preserve">
    <value>CH Part Price Details</value>
  </data>
  <data name="partproducttypedetails" xml:space="preserve">
    <value>CH Parts Product Type Details</value>
  </data>
  <data name="Parts" xml:space="preserve">
    <value>CH Parts</value>
  </data>
  <data name="PartsDetail" xml:space="preserve">
    <value>CH Parts Detail</value>
  </data>
  <data name="PartsDetails" xml:space="preserve">
    <value>CH Parts Details</value>
  </data>
  <data name="partsenglish" xml:space="preserve">
    <value>CH Parts </value>
  </data>
  <data name="PartsFieldSearch" xml:space="preserve">
    <value>CH Parts Field Search</value>
  </data>
  <data name="partsfreestockdetails" xml:space="preserve">
    <value>CH Parts Free Stock details</value>
  </data>
  <data name="partslocale" xml:space="preserve">
    <value>CH Parts </value>
  </data>
  <data name="partsmaster" xml:space="preserve">
    <value>CH Parts Master</value>
  </data>
  <data name="partsmasterlocale" xml:space="preserve">
    <value>CH Parts Master </value>
  </data>
  <data name="partspmasterheader" xml:space="preserve">
    <value>CH Parts Master </value>
  </data>
  <data name="partspricedetails" xml:space="preserve">
    <value>CH Parts Price Details</value>
  </data>
  <data name="partsproducttypelocale" xml:space="preserve">
    <value>CH Parts Product Type </value>
  </data>
  <data name="PartsTemplate" xml:space="preserve">
    <value>CH Parts Template</value>
  </data>
  <data name="PartsTotalAmount" xml:space="preserve">
    <value>CH Parts Total Amount</value>
  </data>
  <data name="Party" xml:space="preserve">
    <value>CH Party</value>
  </data>
  <data name="Partyalreadyexistsforthelocation" xml:space="preserve">
    <value>CH Party already exists for the location</value>
  </data>
  <data name="PartyDetails" xml:space="preserve">
    <value>CH Party Details</value>
  </data>
  <data name="PartyFielSearch" xml:space="preserve">
    <value>CH Party Field Search</value>
  </data>
  <data name="PartyLocation" xml:space="preserve">
    <value>CH Party Location</value>
  </data>
  <data name="PartyMobile" xml:space="preserve">
    <value>CH Party Mobile</value>
  </data>
  <data name="PartyName" xml:space="preserve">
    <value>CH Party Name</value>
  </data>
  <data name="PartyNotFound" xml:space="preserve">
    <value>CH Party Not Found</value>
  </data>
  <data name="PartyPhone" xml:space="preserve">
    <value>CH Party Phone</value>
  </data>
  <data name="PartySearch" xml:space="preserve">
    <value>CH Party Search</value>
  </data>
  <data name="PartyType" xml:space="preserve">
    <value>CH Party Type</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>CH Password</value>
  </data>
  <data name="Passwordandconfirmpasswordshouldmatch" xml:space="preserve">
    <value>CH Confirm password does not match with password given</value>
  </data>
  <data name="PaymentTerms" xml:space="preserve">
    <value>CH Payment Terms</value>
  </data>
  <data name="PDF" xml:space="preserve">
    <value>CH PDF</value>
  </data>
  <data name="Pending" xml:space="preserve">
    <value>CH Pending</value>
  </data>
  <data name="PercentageDeviation" xml:space="preserve">
    <value>CH Percentage Deviation</value>
  </data>
  <data name="Personal" xml:space="preserve">
    <value>CH Personal</value>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>CH Phone</value>
  </data>
  <data name="PhoneNo" xml:space="preserve">
    <value>CH Phone</value>
  </data>
  <data name="PieChart" xml:space="preserve">
    <value>CH Pie Chart</value>
  </data>
  <data name="PlannedCompletionDate" xml:space="preserve">
    <value>CH Planned Completion Date</value>
  </data>
  <data name="PlannedCompletionDatecannotbelessthanStartDate" xml:space="preserve">
    <value>CH Planned completion date cannot be less than start date</value>
  </data>
  <data name="PlannedStartDate" xml:space="preserve">
    <value>CH Planned Start Date</value>
  </data>
  <data name="pleasecompleteenteringdetails" xml:space="preserve">
    <value>CH Please complete entering details</value>
  </data>
  <data name="PleaseenterIntegerValue" xml:space="preserve">
    <value>CH PleaseenterIntegerValue</value>
  </data>
  <data name="PleaseEnterLoginID" xml:space="preserve">
    <value>CH Please enter login ID</value>
  </data>
  <data name="PleaseenterPartyName" xml:space="preserve">
    <value>CH Please enter party name</value>
  </data>
  <data name="PleaseEnterPassword" xml:space="preserve">
    <value>CH Please enter password</value>
  </data>
  <data name="PleaseenterReportHeader" xml:space="preserve">
    <value>CH Please enter report header</value>
  </data>
  <data name="PleaseenterReportName" xml:space="preserve">
    <value>CH Please enter report name</value>
  </data>
  <data name="Pleaseenteruserdetails" xml:space="preserve">
    <value>CH Please enter user details</value>
  </data>
  <data name="pleaseentervalidmodel" xml:space="preserve">
    <value>CH Please enter valid model</value>
  </data>
  <data name="Pleaseentervalue" xml:space="preserve">
    <value>CH Please enter value</value>
  </data>
  <data name="Pleaseentervalue1" xml:space="preserve">
    <value>CH Please enter value1</value>
  </data>
  <data name="PleaseprovideMenuName" xml:space="preserve">
    <value>CH Please provide menu name</value>
  </data>
  <data name="PleaseprovideModuleName" xml:space="preserve">
    <value>CH Please provide module name</value>
  </data>
  <data name="Pleaseprovidepassword" xml:space="preserve">
    <value>CH Please provide password</value>
  </data>
  <data name="pleasesavetheOperationdata" xml:space="preserve">
    <value>CH Please save the operation details</value>
  </data>
  <data name="pleasesavethepartsdata" xml:space="preserve">
    <value>CH Please save the parts details</value>
  </data>
  <data name="pleasesavetheServicedata" xml:space="preserve">
    <value>CH Please save the service details</value>
  </data>
  <data name="pleasesavethesundrydata" xml:space="preserve">
    <value>CH Please save the sundry details</value>
  </data>
  <data name="PleaseselectaColumn" xml:space="preserve">
    <value>CH Please select a Column</value>
  </data>
  <data name="PleaseselectaCondition" xml:space="preserve">
    <value>CH Please select a Condition</value>
  </data>
  <data name="Pleaseselectafiletoupload" xml:space="preserve">
    <value>CH Please select a file to upload</value>
  </data>
  <data name="PleaseselectaOperator" xml:space="preserve">
    <value>CH Please select a Operator</value>
  </data>
  <data name="PleaseSelectBranch" xml:space="preserve">
    <value>CH Please select branch</value>
  </data>
  <data name="PleaseselectBrand" xml:space="preserve">
    <value>CH Please select brand</value>
  </data>
  <data name="Pleaseselectcompany" xml:space="preserve">
    <value>CH Please select company</value>
  </data>
  <data name="Pleaseselectcondition" xml:space="preserve">
    <value>CH Please select condition</value>
  </data>
  <data name="PleaseselectFile" xml:space="preserve">
    <value>CH Please select file</value>
  </data>
  <data name="PleaseSelectGraphCategory" xml:space="preserve">
    <value>CH Please Select Graph Category</value>
  </data>
  <data name="PleaseSelectGraphType" xml:space="preserve">
    <value>CH PleaseSelectGraphType</value>
  </data>
  <data name="pleaseselectModel" xml:space="preserve">
    <value>CH Please select model</value>
  </data>
  <data name="PleaseselectmodelandSerialNumber" xml:space="preserve">
    <value>CH Pleases select model and serial number</value>
  </data>
  <data name="Pleaseselectoperator" xml:space="preserve">
    <value>CH Please select operator</value>
  </data>
  <data name="Pleaseselectrecordstodelete" xml:space="preserve">
    <value>CH Please select records to delete</value>
  </data>
  <data name="PleaseselecttheColumnName" xml:space="preserve">
    <value>CH Please select the column name</value>
  </data>
  <data name="Pleaseselectthecolumnstomap" xml:space="preserve">
    <value>CH Please select the columns to map</value>
  </data>
  <data name="Pleaseselecttheoperator" xml:space="preserve">
    <value>CH Please select the operator</value>
  </data>
  <data name="PleaseselecttheTableName" xml:space="preserve">
    <value>CH Please select the table name</value>
  </data>
  <data name="PleaseselectUserstosave" xml:space="preserve">
    <value>CH Please select users to save</value>
  </data>
  <data name="PreffixSuffixDoesntExists" xml:space="preserve">
    <value>CH Preffix Suffix Doesnt Exists</value>
  </data>
  <data name="prefix" xml:space="preserve">
    <value>CH Prefix</value>
  </data>
  <data name="prefixsuffix" xml:space="preserve">
    <value>CH Prefix Suffix</value>
  </data>
  <data name="PreviousdateCannotbeempty" xml:space="preserve">
    <value>CH Previous date cannot be empty</value>
  </data>
  <data name="PricecannotbeBlankorZero" xml:space="preserve">
    <value>CH Price cannot be blank or zero</value>
  </data>
  <data name="PrimarySegment" xml:space="preserve">
    <value>CH Primary Segment</value>
  </data>
  <data name="Print" xml:space="preserve">
    <value>CH Print</value>
  </data>
  <data name="PrintAction" xml:space="preserve">
    <value>CH Print Action</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>CH Priority</value>
  </data>
  <data name="PriorityShouldBeBetweenzeroandtwofiftyfive" xml:space="preserve">
    <value>CH Priority should be between 0 and 255</value>
  </data>
  <data name="product" xml:space="preserve">
    <value>CH Product</value>
  </data>
  <data name="ProductAssociation" xml:space="preserve">
    <value>CH Product Association</value>
  </data>
  <data name="productdetail" xml:space="preserve">
    <value>CH Product Detail</value>
  </data>
  <data name="productdetails" xml:space="preserve">
    <value>CH Product Details</value>
  </data>
  <data name="productid" xml:space="preserve">
    <value>CH Product ID</value>
  </data>
  <data name="Productisnotasscociatedwithanycustomer" xml:space="preserve">
    <value>CH Product is currently not asscociated with any customer</value>
  </data>
  <data name="ProductLocation" xml:space="preserve">
    <value>CH Product Location</value>
  </data>
  <data name="ProductReading" xml:space="preserve">
    <value>CH Product Reading</value>
  </data>
  <data name="ProductServiceHistory" xml:space="preserve">
    <value>CH Product Service History</value>
  </data>
  <data name="Producttype" xml:space="preserve">
    <value>CH Product Type</value>
  </data>
  <data name="producttypeenglish" xml:space="preserve">
    <value>CH Product Type English</value>
  </data>
  <data name="producttypelocale" xml:space="preserve">
    <value>CH Product Type Locale</value>
  </data>
  <data name="producttypemaster" xml:space="preserve">
    <value>CH Product Type Master</value>
  </data>
  <data name="producttypename" xml:space="preserve">
    <value>CH Product Type Name</value>
  </data>
  <data name="producttypenamealreadyexists" xml:space="preserve">
    <value>CH Product type name already exists</value>
  </data>
  <data name="ProductUniqueNo" xml:space="preserve">
    <value>CH Unique Identifier</value>
  </data>
  <data name="Prospect" xml:space="preserve">
    <value>CH Prospect</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>CH Quantity</value>
  </data>
  <data name="QuantityCannotbeZero" xml:space="preserve">
    <value>CH Quantity Cannot be Zero</value>
  </data>
  <data name="QuestInformaticsPrivateLimited" xml:space="preserve">
    <value>CH Quest Informatics Private Limited</value>
  </data>
  <data name="Quotation" xml:space="preserve">
    <value>CH Quotation</value>
  </data>
  <data name="QuotationAmount" xml:space="preserve">
    <value>CH Quotation Amount</value>
  </data>
  <data name="QuotationDetail" xml:space="preserve">
    <value>CH Quotation Detail</value>
  </data>
  <data name="QuotationNumber" xml:space="preserve">
    <value>CH Quotation Number</value>
  </data>
  <data name="QuotationPriority" xml:space="preserve">
    <value>CH Quotation Priority</value>
  </data>
  <data name="RangecannotbeBlank" xml:space="preserve">
    <value>CH Range cannot be blank</value>
  </data>
  <data name="Rate" xml:space="preserve">
    <value>CH Rate</value>
  </data>
  <data name="RateCannotbeZero" xml:space="preserve">
    <value>CH Rate Cannot be Zero</value>
  </data>
  <data name="Rating" xml:space="preserve">
    <value>CH Rating</value>
  </data>
  <data name="Ratingshouldbebetween1and10" xml:space="preserve">
    <value>CH Rating should be between 1 and 10</value>
  </data>
  <data name="RatingshouldBetween1and10" xml:space="preserve">
    <value>CH Rating should between 1 and 10</value>
  </data>
  <data name="Read" xml:space="preserve">
    <value>CH Read</value>
  </data>
  <data name="ReadAction" xml:space="preserve">
    <value>CH Read Action</value>
  </data>
  <data name="reading" xml:space="preserve">
    <value>CH Reading</value>
  </data>
  <data name="readinglimit" xml:space="preserve">
    <value>CH Reading Limit</value>
  </data>
  <data name="readinglog" xml:space="preserve">
    <value>CH Reading Log</value>
  </data>
  <data name="realizationreport" xml:space="preserve">
    <value>CH Realization Report</value>
  </data>
  <data name="reasonforinactive" xml:space="preserve">
    <value>CH Reason for Inactive</value>
  </data>
  <data name="ReceivedCount" xml:space="preserve">
    <value>CH Received Count</value>
  </data>
  <data name="RecentActivityLinks" xml:space="preserve">
    <value>CH Recent Activity Links</value>
  </data>
  <data name="RecievedTime" xml:space="preserve">
    <value>CH Recieved Time</value>
  </data>
  <data name="RecieviedCount" xml:space="preserve">
    <value>CH Recievied Count</value>
  </data>
  <data name="Recordsavedsuccessfully" xml:space="preserve">
    <value>CH Record saved successfully</value>
  </data>
  <data name="ReferenceDetail" xml:space="preserve">
    <value>CH Reference Detail</value>
  </data>
  <data name="ReferenceMasters" xml:space="preserve">
    <value>CH Reference Masters</value>
  </data>
  <data name="ReferenceTables" xml:space="preserve">
    <value>CH Reference Tables</value>
  </data>
  <data name="refresh" xml:space="preserve">
    <value>CH Refresh</value>
  </data>
  <data name="Region" xml:space="preserve">
    <value>CH Region</value>
  </data>
  <data name="Registered" xml:space="preserve">
    <value>CH Registered</value>
  </data>
  <data name="RegisteredMobile" xml:space="preserve">
    <value>CH Registered Mobile</value>
  </data>
  <data name="relogin" xml:space="preserve">
    <value>CH Re-Login</value>
  </data>
  <data name="Remarks" xml:space="preserve">
    <value>CH Remarks</value>
  </data>
  <data name="RemoveFilter" xml:space="preserve">
    <value>CH Remove Filter</value>
  </data>
  <data name="ReportWizard" xml:space="preserve">
    <value>CH Report Wizard</value>
  </data>
  <data name="ReqDate" xml:space="preserve">
    <value>CH Req Date</value>
  </data>
  <data name="ReqNumber" xml:space="preserve">
    <value>CH Req Number</value>
  </data>
  <data name="RequestDescription" xml:space="preserve">
    <value>CH Request Description</value>
  </data>
  <data name="RequestNumber" xml:space="preserve">
    <value>CH Request Number</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>CH Reset</value>
  </data>
  <data name="resolutiontime" xml:space="preserve">
    <value>CH Resolution Time</value>
  </data>
  <data name="Resolutiontimewithtimeslots" xml:space="preserve">
    <value>CH Resolution Time With Time Slots</value>
  </data>
  <data name="ResourceUtilizationReport" xml:space="preserve">
    <value>CH Resource Utilization Report</value>
  </data>
  <data name="responsetime" xml:space="preserve">
    <value>CH Response Time</value>
  </data>
  <data name="RevenuecannotbeBlankorzero" xml:space="preserve">
    <value>CH Revenue cannot be blank</value>
  </data>
  <data name="RevenueGenerated" xml:space="preserve">
    <value>CH Revenue Generated</value>
  </data>
  <data name="revenuemorethen" xml:space="preserve">
    <value>CH Revenue More Then</value>
  </data>
  <data name="RoleDefinition" xml:space="preserve">
    <value>CH Role Definition</value>
  </data>
  <data name="RoleName" xml:space="preserve">
    <value>CH Role Name</value>
  </data>
  <data name="RoleNameCannotbeblank" xml:space="preserve">
    <value>CH Role name cannot be blank</value>
  </data>
  <data name="RoleObject" xml:space="preserve">
    <value>CH Role Object</value>
  </data>
  <data name="Roles" xml:space="preserve">
    <value>CH Roles</value>
  </data>
  <data name="RoundOff" xml:space="preserve">
    <value>CH Round Off</value>
  </data>
  <data name="Saturday" xml:space="preserve">
    <value>CH Saturday</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>CH Save</value>
  </data>
  <data name="SaveAction" xml:space="preserve">
    <value>CH Save Action</value>
  </data>
  <data name="SavedSuccessfully" xml:space="preserve">
    <value>CH Saved Successfully</value>
  </data>
  <data name="SaveFormatandGenerateReport" xml:space="preserve">
    <value>CH Save Format and Generate Report</value>
  </data>
  <data name="savefreestockdetails" xml:space="preserve">
    <value>CH Save Parts Free Stock Details</value>
  </data>
  <data name="saveheader" xml:space="preserve">
    <value>CH Save Header</value>
  </data>
  <data name="savepartprice" xml:space="preserve">
    <value>CH Save Part Price Details</value>
  </data>
  <data name="SavePrefixSuffix" xml:space="preserve">
    <value>CH Save Prefix Suffix</value>
  </data>
  <data name="saveproductdetail" xml:space="preserve">
    <value>CH Save Product Detail</value>
  </data>
  <data name="saveproducttype" xml:space="preserve">
    <value>CH Save Part Product Type</value>
  </data>
  <data name="SaveRole" xml:space="preserve">
    <value>CH Save Role</value>
  </data>
  <data name="SaveStep" xml:space="preserve">
    <value>CH Save Step</value>
  </data>
  <data name="SaveStepLink" xml:space="preserve">
    <value>CH Save Step Link</value>
  </data>
  <data name="SaveSuccessfull" xml:space="preserve">
    <value>CH Saved Successfully</value>
  </data>
  <data name="savetaxstructure" xml:space="preserve">
    <value>CH Save Tax Structure</value>
  </data>
  <data name="SaveUser" xml:space="preserve">
    <value>CH Save User</value>
  </data>
  <data name="SecondarySegment" xml:space="preserve">
    <value>CH Secondary Segment</value>
  </data>
  <data name="SecondarySegmentdescription" xml:space="preserve">
    <value>CH Secondary Segment Description</value>
  </data>
  <data name="secondarysegmentenglish" xml:space="preserve">
    <value>CH Secondary Segment English</value>
  </data>
  <data name="secondarysegmentlocale" xml:space="preserve">
    <value>CH Secondary Segment Locale</value>
  </data>
  <data name="SegmentDetail" xml:space="preserve">
    <value>CH Segment Detail</value>
  </data>
  <data name="select" xml:space="preserve">
    <value>CH Select</value>
  </data>
  <data name="SelectAll" xml:space="preserve">
    <value>CH Select All</value>
  </data>
  <data name="selectbrand" xml:space="preserve">
    <value>CH Select Brand</value>
  </data>
  <data name="SelectColumn" xml:space="preserve">
    <value>CH Select Column</value>
  </data>
  <data name="selectcompany" xml:space="preserve">
    <value>CH Select Company</value>
  </data>
  <data name="SelectCondition" xml:space="preserve">
    <value>CH Select Condition</value>
  </data>
  <data name="SelectDDl" xml:space="preserve">
    <value>CH ---------Select---------</value>
  </data>
  <data name="SelectedFileisnotanExcelFile" xml:space="preserve">
    <value>CH Selected file is not an excel file</value>
  </data>
  <data name="SelectionCriteria" xml:space="preserve">
    <value>CH Selection Criteria</value>
  </data>
  <data name="SelectModel" xml:space="preserve">
    <value>CH Select Model</value>
  </data>
  <data name="SelectModelandRequest" xml:space="preserve">
    <value>CH Select Model and Request</value>
  </data>
  <data name="SelectOperator" xml:space="preserve">
    <value>CH Select Operator</value>
  </data>
  <data name="SelectPartyType" xml:space="preserve">
    <value>CH Select Party Type</value>
  </data>
  <data name="selectproducttype" xml:space="preserve">
    <value>CH Select Product Type</value>
  </data>
  <data name="SelectRecordstoDelete" xml:space="preserve">
    <value>CH Select Records to Delete</value>
  </data>
  <data name="SelectReportFromPreviouslyStoredFormats" xml:space="preserve">
    <value>CH Select Report From Previously Stored Formats</value>
  </data>
  <data name="SelectReqNumber" xml:space="preserve">
    <value>CH Select Request Number</value>
  </data>
  <data name="SelectServiceRequest" xml:space="preserve">
    <value>CH Select Service Request</value>
  </data>
  <data name="selectshift" xml:space="preserve">
    <value>CH Select Shift</value>
  </data>
  <data name="SelectTableName" xml:space="preserve">
    <value>CH Select Table Name</value>
  </data>
  <data name="September" xml:space="preserve">
    <value>CH Sep</value>
  </data>
  <data name="sequenceno" xml:space="preserve">
    <value>CH Sequence No</value>
  </data>
  <data name="Serial" xml:space="preserve">
    <value>CH Serial</value>
  </data>
  <data name="serialnumber" xml:space="preserve">
    <value>CH Serial Number</value>
  </data>
  <data name="serialnumberalreadyexistsforthismodel" xml:space="preserve">
    <value>CH Serial number already exists for this model</value>
  </data>
  <data name="SerialNumberFieldSearch" xml:space="preserve">
    <value>CH Serial Number Field Search</value>
  </data>
  <data name="SerialNumbernotfoundfortheselectedmodel" xml:space="preserve">
    <value>CH Serial number not found for the selected model</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>CH Service</value>
  </data>
  <data name="ServiceChargeCode" xml:space="preserve">
    <value>CH Service Charge Code</value>
  </data>
  <data name="servicechargecodenotfound" xml:space="preserve">
    <value>CH Service charge code not found</value>
  </data>
  <data name="ServiceChargeDetail" xml:space="preserve">
    <value>CH Service Charge Detail</value>
  </data>
  <data name="ServiceChargeFieldSearch" xml:space="preserve">
    <value>CH Service Charge Field Search</value>
  </data>
  <data name="servicecharges" xml:space="preserve">
    <value>CH Service Charges</value>
  </data>
  <data name="servicechargesdetail" xml:space="preserve">
    <value>CH Service Charges Detail</value>
  </data>
  <data name="ServiceChargesDetails" xml:space="preserve">
    <value>CH Service Charges Details</value>
  </data>
  <data name="servicechargesenglish" xml:space="preserve">
    <value>CH Service Charges</value>
  </data>
  <data name="servicechargesheader" xml:space="preserve">
    <value>CH Service Charges </value>
  </data>
  <data name="servicechargeslocale" xml:space="preserve">
    <value>CH Service Charges </value>
  </data>
  <data name="servicechargesmaster" xml:space="preserve">
    <value>CH Service Charges Master</value>
  </data>
  <data name="ServiceChargesTotalAmount" xml:space="preserve">
    <value>CH Service Charges Total Amount</value>
  </data>
  <data name="servicecodealreadyexists" xml:space="preserve">
    <value>CH Service code already exists</value>
  </data>
  <data name="servicedate" xml:space="preserve">
    <value>CH Service Date</value>
  </data>
  <data name="servicedatecannotbelessthancurrentdate" xml:space="preserve">
    <value>CH Service date cannot be less than Current date</value>
  </data>
  <data name="ServiceDetails" xml:space="preserve">
    <value>CH Service Details</value>
  </data>
  <data name="servicehistory" xml:space="preserve">
    <value>CH Service History</value>
  </data>
  <data name="ServicePriority" xml:space="preserve">
    <value>CH Service Priority</value>
  </data>
  <data name="ServiceQuotationNumber" xml:space="preserve">
    <value>CH Quotation Number</value>
  </data>
  <data name="ServiceRequest" xml:space="preserve">
    <value>CH Service Request</value>
  </data>
  <data name="ServiceRequestAbandoned" xml:space="preserve">
    <value>CH Service Request Abandoned</value>
  </data>
  <data name="ServiceRequestCount" xml:space="preserve">
    <value>CH Service Request Count</value>
  </data>
  <data name="ServiceRequestDate" xml:space="preserve">
    <value>CH Service Request Date</value>
  </data>
  <data name="ServiceRequestDistributionChart" xml:space="preserve">
    <value>CH Service Request Distribution Chart</value>
  </data>
  <data name="ServicerequestFieldSearch" xml:space="preserve">
    <value>CH Service request Field Search</value>
  </data>
  <data name="ServiceRequestNumber" xml:space="preserve">
    <value>CH Service Request Number</value>
  </data>
  <data name="ServiceRequestNumbernotfound" xml:space="preserve">
    <value>CH Service request number not found</value>
  </data>
  <data name="ServiceRequestSummary" xml:space="preserve">
    <value>CH Service Request Summary</value>
  </data>
  <data name="ServiceSchedule" xml:space="preserve">
    <value>CH Service Schedule</value>
  </data>
  <data name="ServiceType" xml:space="preserve">
    <value>CH Service Type</value>
  </data>
  <data name="ServiceTypeName" xml:space="preserve">
    <value>CH Service Type Name</value>
  </data>
  <data name="Shift" xml:space="preserve">
    <value>CH Shift</value>
  </data>
  <data name="ShortName" xml:space="preserve">
    <value>CH Short Name</value>
  </data>
  <data name="siteaddress" xml:space="preserve">
    <value>CH Site Address</value>
  </data>
  <data name="siteaddressdetails" xml:space="preserve">
    <value>CH Site Address Details</value>
  </data>
  <data name="SixteenToTwentyFourHours" xml:space="preserve">
    <value>CH 16 To 24 Hours</value>
  </data>
  <data name="skill" xml:space="preserve">
    <value>CH Skill</value>
  </data>
  <data name="Skillisalreadyassociatedwiththeemployee" xml:space="preserve">
    <value>CH Skill is already associated with the employee</value>
  </data>
  <data name="skilllevel" xml:space="preserve">
    <value>CH Skill Level</value>
  </data>
  <data name="skilllevelshouldbebetween1to10" xml:space="preserve">
    <value>CH Skill level should be between 1 to 10</value>
  </data>
  <data name="Skillset" xml:space="preserve">
    <value>CH Skill Set</value>
  </data>
  <data name="slno" xml:space="preserve">
    <value>CH Sl No</value>
  </data>
  <data name="SMSToAddressee" xml:space="preserve">
    <value>CH SMS To Addressee</value>
  </data>
  <data name="SMSToCustomer" xml:space="preserve">
    <value>CH SMS To Customer</value>
  </data>
  <data name="SMTPMailBox" xml:space="preserve">
    <value>CH SMTP Mail Box</value>
  </data>
  <data name="SMTPPassword" xml:space="preserve">
    <value>CH SMTP Password</value>
  </data>
  <data name="SMTPServerName" xml:space="preserve">
    <value>CH SMTP Server Name</value>
  </data>
  <data name="SMTPUserName" xml:space="preserve">
    <value>CH SMTP User Name</value>
  </data>
  <data name="Sn" xml:space="preserve">
    <value>CH Sn</value>
  </data>
  <data name="SortOrder" xml:space="preserve">
    <value>CH Sort Order</value>
  </data>
  <data name="SortOrdercannotbeblank" xml:space="preserve">
    <value>CH Sort order can not be blank</value>
  </data>
  <data name="sortordercannotbeblankforMenu" xml:space="preserve">
    <value>CH Sort order can not be blank for menu</value>
  </data>
  <data name="SortOrderCannotbegreaterthan" xml:space="preserve">
    <value>CH Sort order cannot be greater than 255</value>
  </data>
  <data name="SourceColumns" xml:space="preserve">
    <value>CH Source Columns</value>
  </data>
  <data name="Specialization" xml:space="preserve">
    <value>CH Specialization</value>
  </data>
  <data name="SpecializationMaster" xml:space="preserve">
    <value>CH Specialization Master</value>
  </data>
  <data name="SqlException" xml:space="preserve">
    <value>CH Database error occured</value>
  </data>
  <data name="SRCount" xml:space="preserve">
    <value>CH Service Requests Count</value>
  </data>
  <data name="SRNotFound" xml:space="preserve">
    <value>CH Service request not found</value>
  </data>
  <data name="StandardHours" xml:space="preserve">
    <value>CH Standard Hours</value>
  </data>
  <data name="standardtime" xml:space="preserve">
    <value>CH Standard Time</value>
  </data>
  <data name="startnumber" xml:space="preserve">
    <value>CH Start Number</value>
  </data>
  <data name="startnumbercannotbenullorzero" xml:space="preserve">
    <value>CH Start number cannot be null or zero</value>
  </data>
  <data name="State" xml:space="preserve">
    <value>CH State</value>
  </data>
  <data name="stateenglish" xml:space="preserve">
    <value>CH State English</value>
  </data>
  <data name="statelocale" xml:space="preserve">
    <value>CH State Locale</value>
  </data>
  <data name="status" xml:space="preserve">
    <value>CH Status</value>
  </data>
  <data name="StepLink" xml:space="preserve">
    <value>CH Step Link</value>
  </data>
  <data name="StepName" xml:space="preserve">
    <value>CH Step Name</value>
  </data>
  <data name="Steps" xml:space="preserve">
    <value>CH Steps</value>
  </data>
  <data name="StepStatus" xml:space="preserve">
    <value>CH Step Status</value>
  </data>
  <data name="StepType" xml:space="preserve">
    <value>CH Step Type</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>CH Success</value>
  </data>
  <data name="suffix" xml:space="preserve">
    <value>CH Suffix</value>
  </data>
  <data name="SuffixalreadySelected" xml:space="preserve">
    <value>CH Suffix already Selected</value>
  </data>
  <data name="Summary" xml:space="preserve">
    <value>CH Summary</value>
  </data>
  <data name="Sunday" xml:space="preserve">
    <value>CH Sunday</value>
  </data>
  <data name="SundryDetail" xml:space="preserve">
    <value>CH Sundry Detail</value>
  </data>
  <data name="SundryDetails" xml:space="preserve">
    <value>CH Sundry Details</value>
  </data>
  <data name="SundryJobDescription" xml:space="preserve">
    <value>CH Sundry Job Description</value>
  </data>
  <data name="SundryTotalAmount" xml:space="preserve">
    <value>CH Sundry Total Amount</value>
  </data>
  <data name="TableName" xml:space="preserve">
    <value>CH Table Name</value>
  </data>
  <data name="Tax" xml:space="preserve">
    <value>CH Tax</value>
  </data>
  <data name="Taxable" xml:space="preserve">
    <value>CH Taxable</value>
  </data>
  <data name="Taxableothercharges1" xml:space="preserve">
    <value>CH Taxable Other Charges 1</value>
  </data>
  <data name="Taxableothercharges1Amount" xml:space="preserve">
    <value>CH Taxable Other Charges 1 Amount</value>
  </data>
  <data name="Taxableothercharges2" xml:space="preserve">
    <value>CH Taxable Other Charges 2</value>
  </data>
  <data name="Taxableothercharges2Amount" xml:space="preserve">
    <value>CH Taxable Other Charges 2 Amount</value>
  </data>
  <data name="Taxamount" xml:space="preserve">
    <value>CH Tax Amount</value>
  </data>
  <data name="TaxCode" xml:space="preserve">
    <value>CH Tax Code</value>
  </data>
  <data name="TaxCodeName" xml:space="preserve">
    <value>CH Tax Code Name</value>
  </data>
  <data name="TaxDetail" xml:space="preserve">
    <value>CH Tax Detail</value>
  </data>
  <data name="TaxDetails" xml:space="preserve">
    <value>CH Tax Details</value>
  </data>
  <data name="Taxnamealreadyexists" xml:space="preserve">
    <value>CH Tax name already exists</value>
  </data>
  <data name="taxpercentage" xml:space="preserve">
    <value>CH Tax percentage</value>
  </data>
  <data name="TaxStructure" xml:space="preserve">
    <value>CH Tax Structure</value>
  </data>
  <data name="taxstructuredetail" xml:space="preserve">
    <value>CH Tax Structure Detail</value>
  </data>
  <data name="taxstructuredetails" xml:space="preserve">
    <value>CH Tax Structure Details</value>
  </data>
  <data name="taxstructureenglish" xml:space="preserve">
    <value>CH Tax Structure </value>
  </data>
  <data name="taxstructureheader" xml:space="preserve">
    <value>CH Tax Structure Header</value>
  </data>
  <data name="taxstructurelocale" xml:space="preserve">
    <value>CH Tax Structure </value>
  </data>
  <data name="taxstructurename" xml:space="preserve">
    <value>CH Tax Structure Name</value>
  </data>
  <data name="taxtype" xml:space="preserve">
    <value>CH Tax Type</value>
  </data>
  <data name="taxtypealreadyselected" xml:space="preserve">
    <value>CH Tax type already selected</value>
  </data>
  <data name="taxtypealrearyselected" xml:space="preserve">
    <value>CH Tax type already selected</value>
  </data>
  <data name="taxtypeisreferencedinformulacannotdelete" xml:space="preserve">
    <value>CH Tax type is referenced in formula cannot delete</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>CH Team</value>
  </data>
  <data name="Terms" xml:space="preserve">
    <value>CH Terms</value>
  </data>
  <data name="TermsAndConditions" xml:space="preserve">
    <value>CH Terms And Conditions</value>
  </data>
  <data name="TermsConditions" xml:space="preserve">
    <value>CH Terms &amp; Conditions</value>
  </data>
  <data name="Thebrandhasalreadybeenselected" xml:space="preserve">
    <value>CH The brand has already been selected</value>
  </data>
  <data name="TheCompanyDealerhasalreadybeenassociated" xml:space="preserve">
    <value>CH The company/dealer has already been associated</value>
  </data>
  <data name="thesecondarysegmentalreadyexists" xml:space="preserve">
    <value>CH The secondary segment already exists</value>
  </data>
  <data name="thestatealreadyexists" xml:space="preserve">
    <value>CH The state already exists</value>
  </data>
  <data name="ThetaxStructurehasalreadybeenselected" xml:space="preserve">
    <value>CH The tax structure has already been selected </value>
  </data>
  <data name="ThirtySixToFourtyEightHours" xml:space="preserve">
    <value>CH 36 To 48 Hours</value>
  </data>
  <data name="ThisLoginIDisalreadyexists" xml:space="preserve">
    <value>CH This Login Id is already exists</value>
  </data>
  <data name="ThisModuleisalreadyexists" xml:space="preserve">
    <value>CH This module is already exists</value>
  </data>
  <data name="ThisRoleisalreadyexists" xml:space="preserve">
    <value>CH This role is already exists</value>
  </data>
  <data name="Thisroleisalreadyselectedfortheuser" xml:space="preserve">
    <value>CH This role is already selected for the user</value>
  </data>
  <data name="ThisSerialNumberisalreadyassociatedwiththecustomer" xml:space="preserve">
    <value>CH This serial number is already associated with the customer</value>
  </data>
  <data name="Thursday" xml:space="preserve">
    <value>CH Thursday</value>
  </data>
  <data name="To" xml:space="preserve">
    <value>CH To</value>
  </data>
  <data name="todate" xml:space="preserve">
    <value>CH To Date</value>
  </data>
  <data name="ToDatecannotbegreaterthanCurrentDate" xml:space="preserve">
    <value>CH To Date cannot be greater than Current Date</value>
  </data>
  <data name="ToDatecannotbelessthanCurrentDate" xml:space="preserve">
    <value>CH To Date cannot be less than Current Date</value>
  </data>
  <data name="todatecannotbelessthenfromdatedate" xml:space="preserve">
    <value>CH To date cannot be less then from date</value>
  </data>
  <data name="todatemustbegreaterthanorequaltofromdate" xml:space="preserve">
    <value>CH To date must be greater than or equal to From date</value>
  </data>
  <data name="TopModel" xml:space="preserve">
    <value>CH Top Model</value>
  </data>
  <data name="ToStep" xml:space="preserve">
    <value>CH To Step</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>CH Total</value>
  </data>
  <data name="TotalAllocatedHours" xml:space="preserve">
    <value>CH Total Allocated Hours</value>
  </data>
  <data name="TotalAmount" xml:space="preserve">
    <value>CH Total Amount</value>
  </data>
  <data name="TotalOn" xml:space="preserve">
    <value>CH Total On</value>
  </data>
  <data name="TotalQuotationAmount" xml:space="preserve">
    <value>CH Total Quotation Amount</value>
  </data>
  <data name="TotalTaxableAmount" xml:space="preserve">
    <value>CH Total Taxable Amount</value>
  </data>
  <data name="TotalTaxableAmountBlank" xml:space="preserve">
    <value>CH Total taxable amount is blank</value>
  </data>
  <data name="TotalWorkingHours" xml:space="preserve">
    <value>CH Total Working Hours</value>
  </data>
  <data name="TransactionisalreadybeenLocked" xml:space="preserve">
    <value>CH Transaction has already been locked</value>
  </data>
  <data name="TransactionisalreadybeenUnLocked" xml:space="preserve">
    <value>CH Transaction has already been unlocked</value>
  </data>
  <data name="TransactionLockedSuccessfully" xml:space="preserve">
    <value>CH Transaction locked successfully</value>
  </data>
  <data name="TransactionUnLockedSuccessfully" xml:space="preserve">
    <value>CH Transaction unlocked successfully</value>
  </data>
  <data name="Tuesday" xml:space="preserve">
    <value>CH Tuesday</value>
  </data>
  <data name="TwentyFourToThirtySixHours" xml:space="preserve">
    <value>CH 24 To 36 Hours</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>CH Type</value>
  </data>
  <data name="uniqueidentifieralreadyexists" xml:space="preserve">
    <value>CH Unique identifier already exists</value>
  </data>
  <data name="unitofmeasurement" xml:space="preserve">
    <value>CH Unit Of Measurement</value>
  </data>
  <data name="UnLock" xml:space="preserve">
    <value>CH UnLock</value>
  </data>
  <data name="UnRegisteredServiceRequest" xml:space="preserve">
    <value>CH UnRegisteredServiceRequest</value>
  </data>
  <data name="uom" xml:space="preserve">
    <value>CH UOM</value>
  </data>
  <data name="UploadFile" xml:space="preserve">
    <value>CH Upload File</value>
  </data>
  <data name="UploadParts" xml:space="preserve">
    <value>CH Upload Parts</value>
  </data>
  <data name="usageenvironment" xml:space="preserve">
    <value>CH Usage Environment</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>CH User</value>
  </data>
  <data name="UserDataSavedSuccessfully" xml:space="preserve">
    <value>CH User data saved successfully</value>
  </data>
  <data name="UserDetail" xml:space="preserve">
    <value>CH User Detail</value>
  </data>
  <data name="UserDetails" xml:space="preserve">
    <value>CH User Details</value>
  </data>
  <data name="Userdonthaveaccesstopartymaster" xml:space="preserve">
    <value>CH User dont have access to Party Master</value>
  </data>
  <data name="Userdonthaveaccesstoproductmaster" xml:space="preserve">
    <value>CH User dont have access to Product Master</value>
  </data>
  <data name="Userdonthaveeditaccess" xml:space="preserve">
    <value>CH User dont have Edit Access</value>
  </data>
  <data name="Userdonthaveeditaccesstoproductmaster" xml:space="preserve">
    <value>CH User dont have Edit access to Product Master</value>
  </data>
  <data name="Userislocked" xml:space="preserve">
    <value>CH User is locked</value>
  </data>
  <data name="Userisnotactive" xml:space="preserve">
    <value>CH User is not active</value>
  </data>
  <data name="UserName" xml:space="preserve">
    <value>CH User Name</value>
  </data>
  <data name="UserNameOrPasswordYouEnteredIsIncorrectPleaseTryAgain" xml:space="preserve">
    <value>CH User name or password you entered is incorrect. Please try again..</value>
  </data>
  <data name="UserRoleDetails" xml:space="preserve">
    <value>CH User-Role Details</value>
  </data>
  <data name="UserRoles" xml:space="preserve">
    <value>CH User Roles</value>
  </data>
  <data name="UserType" xml:space="preserve">
    <value>CH User Type</value>
  </data>
  <data name="UtilizationPercentage" xml:space="preserve">
    <value>CH Utilization %</value>
  </data>
  <data name="Value" xml:space="preserve">
    <value>CH Value</value>
  </data>
  <data name="ValueisMandatoryforselectedColumn" xml:space="preserve">
    <value>CH Value is Mandatory for selected Column</value>
  </data>
  <data name="Ver" xml:space="preserve">
    <value>CH Ver</value>
  </data>
  <data name="VerificationQueue" xml:space="preserve">
    <value>CH Verification Queue</value>
  </data>
  <data name="Version" xml:space="preserve">
    <value>CH Version</value>
  </data>
  <data name="VersionDate" xml:space="preserve">
    <value>CH Version Date</value>
  </data>
  <data name="VersionNumber" xml:space="preserve">
    <value>CH Version Number</value>
  </data>
  <data name="VersionNumberandDate" xml:space="preserve">
    <value>CH Version Number and Date</value>
  </data>
  <data name="view" xml:space="preserve">
    <value>CH View</value>
  </data>
  <data name="ViewJobCard" xml:space="preserve">
    <value>CH View Job Card</value>
  </data>
  <data name="ViewPartsMaster" xml:space="preserve">
    <value>CH View Parts Master</value>
  </data>
  <data name="WarrantyDate" xml:space="preserve">
    <value>CH Warranty Date</value>
  </data>
  <data name="warrantydetails" xml:space="preserve">
    <value>CH Warranty Details</value>
  </data>
  <data name="Website" xml:space="preserve">
    <value>CH Website</value>
  </data>
  <data name="Wednesday" xml:space="preserve">
    <value>CH Wednesday</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>CH Welcome :</value>
  </data>
  <data name="Where" xml:space="preserve">
    <value>CH Where</value>
  </data>
  <data name="WorkFlow" xml:space="preserve">
    <value>CH Work Flow</value>
  </data>
  <data name="WorkFlowID" xml:space="preserve">
    <value>CH Work Flow ID</value>
  </data>
  <data name="WorkFlowName" xml:space="preserve">
    <value>CH Work Flow Name</value>
  </data>
  <data name="WorkFlowSteps" xml:space="preserve">
    <value>CH Work Flow Steps</value>
  </data>
  <data name="WorkingDays" xml:space="preserve">
    <value>CH Working Days</value>
  </data>
  <data name="WorkingTime" xml:space="preserve">
    <value>CH Working Time</value>
  </data>
  <data name="Year" xml:space="preserve">
    <value>CH Year</value>
  </data>
  <data name="Yearshouldbebetween2000and2999" xml:space="preserve">
    <value>CH Year should be between 2000 and 2999</value>
  </data>
  <data name="yes" xml:space="preserve">
    <value>CH Yes</value>
  </data>
  <data name="Youdonothaveeditpermission" xml:space="preserve">
    <value>CH You do not have edit permission</value>
  </data>
  <data name="YouhavebeenLoggedoutsuccessfully" xml:space="preserve">
    <value>CH You have been Logged out successfully</value>
  </data>
  <data name="ZipCode" xml:space="preserve">
    <value>CH Zip Code</value>
  </data>
  <data name="inactiveproduct" xml:space="preserve">
    <value>CH In Active Product</value>
  </data>
</root>