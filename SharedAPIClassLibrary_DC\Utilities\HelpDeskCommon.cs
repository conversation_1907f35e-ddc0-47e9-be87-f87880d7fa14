﻿
using System;
using System.Collections.Generic;

using System.Data;
using System.Data.SqlClient;

using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace SharedAPIClassLibrary_AMERP.Utilities
{
    public class HelpDeskCommon
    {
        public static int GetEndStepStatusID(int Workflow_ID, string connString, int LogException)
        {
            int EndStepStatus_ID = 0;

            List<Dictionary<string, object>> results = new List<Dictionary<string, object>>();

            string Query = "SELECT C.* FROM GNM_WFSteps A JOIN GNM_WFStepType B ON A.WFStepType_ID=B.WFStepType_ID AND B.WFStepType_Nm='END' JOIN GNM_WFStepStatus C ON A.WFStepStatus_ID=C.WFStepStatus_ID WHERE A.WorkFlow_ID=" + Workflow_ID;
            using (SqlConnection conn = new SqlConnection(connString))
            {


                SqlCommand cmd = null;

                try
                {
                    using (cmd = new SqlCommand(Query, conn))
                    {
                        cmd.CommandType = CommandType.Text;




                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {

                            while (reader.Read())
                            {
                                Dictionary<string, object> row = new Dictionary<string, object>();
                                for (int i = 0; i < reader.FieldCount; i++)
                                {
                                    row[reader.GetName(i)] = reader.GetValue(i);
                                }
                                results.Add(row);
                            }

                        }




                    }


                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }

            }

            if (results.Count > 0 && results[0].ContainsKey("WFStepStatus_ID"))
            {
                EndStepStatus_ID = Convert.ToInt32(results[0]["WFStepStatus_ID"]);
            }
            return EndStepStatus_ID;
        }
        public static string GetEndStepStatusName(int Workflow_ID, string connString, int LogException)
        {
            string EndStepStatusName = "";


            string Query = "SELECT C.* FROM GNM_WFSteps A JOIN GNM_WFStepType B ON A.WFStepType_ID=B.WFStepType_ID AND B.WFStepType_Nm='END' JOIN GNM_WFStepStatus C ON A.WFStepStatus_ID=C.WFStepStatus_ID WHERE A.WorkFlow_ID=" + Workflow_ID;
            using (SqlConnection conn = new SqlConnection(connString))
            {


                SqlCommand cmd = null;

                try
                {
                    using (cmd = new SqlCommand(Query, conn))
                    {
                        cmd.CommandType = CommandType.Text;




                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {

                            if (reader.HasRows)
                            {

                                reader.Read();

                                EndStepStatusName = reader["WFStepStatus_Nm"].ToString();
                            }

                        }




                    }


                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }

            }


            return EndStepStatusName;
        }
    }
}
