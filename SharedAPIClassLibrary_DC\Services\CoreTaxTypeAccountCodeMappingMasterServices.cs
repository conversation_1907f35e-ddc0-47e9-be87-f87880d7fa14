﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;



namespace SharedAPIClassLibrary_AMERP
{
    public class CoreTaxTypeAccountCodeMappingMasterServices
    {
        public static string AppPath = null;
        #region ::: SelAllTaxMapping Vinay 26/9/24 :::
        /// <summary>
        /// to select all Tax Mapping
        /// </summary> 
        public static IActionResult SelAllTaxMapping(SelAllTaxTypeAccountCodeMappingList Obj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {

            try
            {
                int count = 0;
                int total = 0;
                var jsonResult = default(dynamic);
                var taxTypeListObj = new List<TaxType>();

                var taxTypeAccountCodeMappingList = new List<TaxTypeAccountCodeMappingData>();
                string select = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "select").ToString();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_SELECT_AM_ERP_SelAllTaxMapping_TaxTypeAccountCodeMapping";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;

                            command.Parameters.AddWithValue("@UserLanguageID", Convert.ToInt32(Obj.UserLanguageID));
                            command.Parameters.AddWithValue("@GeneralLanguageID", Obj.GeneralLanguageID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                // First result set: MovementTypeDefinition data

                                while (reader.Read())
                                {
                                    var taxType = new TaxType
                                    {
                                        RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                        RefMasterDetail_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"))
                                    };
                                    taxTypeListObj.Add(taxType);
                                }

                                if (reader.NextResult())
                                {
                                    while (reader.Read())
                                    {
                                        var mapping = new TaxTypeAccountCodeMappingData
                                        {
                                            TaxTypeAccountCodeMappingID = reader.GetInt32(reader.GetOrdinal("TaxTypeAccountCodeMappingID")),
                                            TaxTypeID = reader.GetInt32(reader.GetOrdinal("TaxTypeID")),
                                            TaxType = reader.GetString(reader.GetOrdinal("TaxType")),
                                            AccountCode = reader.GetString(reader.GetOrdinal("AccountCode"))

                                        };
                                        taxTypeAccountCodeMappingList.Add(mapping);
                                    }

                                    // Do something with taxTypeAccountCodeMappingList (e.g., return or process further)
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log exception
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                    }
                    finally
                    {
                        command?.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                if (Obj.UserLanguageID == Obj.GeneralLanguageID)
                {
                    var taxTypeList = from a in taxTypeListObj

                                      select new
                                      {
                                          a.RefMasterDetail_ID,
                                          a.RefMasterDetail_Name
                                      };

                    string TaxTypes = "0:----" + select + "----;";
                    for (int i = 0; i < taxTypeList.ToArray().Count(); i++)
                    {
                        TaxTypes = TaxTypes + taxTypeList.ToArray().ElementAt(i).RefMasterDetail_ID + ":" + taxTypeList.ToArray().ElementAt(i).RefMasterDetail_Name + ";";
                    }
                    TaxTypes = TaxTypes.TrimEnd(new char[] { ';' });

                    IQueryable<TaxTypeAccountCodeMappingData> iTaxMappingArray = null;
                    IEnumerable<TaxTypeAccountCodeMappingData> arrTaxMappingList = from a in taxTypeAccountCodeMappingList

                                                                                   select new TaxTypeAccountCodeMappingData()
                                                                                   {
                                                                                       edit = "<img id='" + a.TaxTypeAccountCodeMappingID + "' src='" + AppPath + "/Content/Images/edit.gif' key='" + a.TaxTypeAccountCodeMappingID + "' class='editTaxTypeAccountCodeMapping' editmode='false'/>",
                                                                                       delete = "<input type='checkbox' key='" + a.TaxTypeAccountCodeMappingID + "' id='chk" + a.TaxTypeAccountCodeMappingID + "' class='chkMappingDelete'/>",
                                                                                       TaxTypeID = a.TaxTypeID,
                                                                                       TaxType = a.TaxType,
                                                                                       AccountCode = a.AccountCode,
                                                                                       TaxTypeAccountCodeMappingID = a.TaxTypeAccountCodeMappingID
                                                                                   };
                    iTaxMappingArray = arrTaxMappingList.AsQueryable<TaxTypeAccountCodeMappingData>();

                    if (_search)
                    {
                        string decodedValue = Uri.UnescapeDataString(filters);
                        Filters filtersObj = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<Filters>();
                        iTaxMappingArray = iTaxMappingArray.FilterSearch<TaxTypeAccountCodeMappingData>(filtersObj);

                    }

                    iTaxMappingArray = iTaxMappingArray.OrderByField<TaxTypeAccountCodeMappingData>(sidx, sord);
                    //Session["TaxTypeAccountCodeMappingData"] = iTaxMappingArray;
                    count = iTaxMappingArray.Count();
                    total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                    jsonResult = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        data = iTaxMappingArray.ToList().Paginate(page, rows),
                        TaxTypes
                    };
                }
                else
                {
                    var taxTypeList = from a in taxTypeListObj

                                      select new
                                      {
                                          a.RefMasterDetail_ID,
                                          a.RefMasterDetail_Name
                                      };

                    string TaxTypes = "0:----" + select + "----;";
                    for (int i = 0; i < taxTypeList.ToArray().Count(); i++)
                    {
                        TaxTypes = TaxTypes + taxTypeList.ToArray().ElementAt(i).RefMasterDetail_ID + ":" + taxTypeList.ToArray().ElementAt(i).RefMasterDetail_Name + ";";
                    }
                    TaxTypes = TaxTypes.TrimEnd(new char[] { ';' });

                    IQueryable<TaxTypeAccountCodeMappingData> iTaxMappingArray = null;
                    IEnumerable<TaxTypeAccountCodeMappingData> arrTaxMappingList = from a in taxTypeAccountCodeMappingList

                                                                                   select new TaxTypeAccountCodeMappingData()
                                                                                   {
                                                                                       edit = "<img id='" + a.TaxTypeAccountCodeMappingID + "' src='" + AppPath + "/Content/Images/edit.gif' key='" + a.TaxTypeAccountCodeMappingID + "' class='editTaxTypeAccountCodeMapping'/>",
                                                                                       delete = "<input type='checkbox' key='" + a.TaxTypeAccountCodeMappingID + "' id='chk" + a.TaxTypeAccountCodeMappingID + "' class='chkMappingDelete'/>",
                                                                                       TaxTypeID = a.TaxTypeID,
                                                                                       TaxType = a.RefMasterDetail_Name,
                                                                                       AccountCode = a.AccountCode,
                                                                                       TaxTypeAccountCodeMappingID = a.TaxTypeAccountCodeMappingID
                                                                                   };
                    iTaxMappingArray = arrTaxMappingList.AsQueryable<TaxTypeAccountCodeMappingData>();

                    if (_search)
                    {
                        string decodedValue = Uri.UnescapeDataString(filters);
                        Filters filtersObj = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<Filters>();
                        iTaxMappingArray = iTaxMappingArray.FilterSearch<TaxTypeAccountCodeMappingData>(filtersObj);

                    }
                    iTaxMappingArray = iTaxMappingArray.OrderByField<TaxTypeAccountCodeMappingData>(sidx, sord);
                    //Session["TaxTypeAccountCodeMappingData"] = iTaxMappingArray;
                    count = iTaxMappingArray.Count();
                    total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                    jsonResult = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        data = iTaxMappingArray.ToList().Paginate(page, rows),
                        TaxTypes
                    };
                }
                return new JsonResult(jsonResult);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                return new JsonResult("Error");
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult("Error");
            }
        }
        #endregion
        #region ::: Save vinay n 26/9/24:::
        /// <summary>
        /// to Save
        /// </summary>
        public static void Save(SaveTaxTypeAccountCodeMappingList Obj, string connString, int LogException)
        {
            try
            {
                JObject jobj = JObject.Parse(Obj.Data);
                int rowCount = jobj["rows"].Count();
                for (int j = 0; j < rowCount; j++)
                {
                    GNM_TaxTypeAccountCodeMapping taxMappingRow = jobj["rows"].ElementAt(j).ToObject<GNM_TaxTypeAccountCodeMapping>();
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_Save_AM_ERP_Save_TaxTypeAccountCodeMapping";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;

                                command.Parameters.AddWithValue("@TaxTypeAccountCodeMapping_ID", taxMappingRow.TaxTypeAccountCodeMapping_ID);
                                command.Parameters.AddWithValue("@TaxType_ID", taxMappingRow.TaxType_ID);
                                command.Parameters.AddWithValue("@AccountCode", Common.DecryptString(taxMappingRow.AccountCode));


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                command.ExecuteScalar();
                            }
                        }
                        catch (Exception ex)
                        {
                            // Log exception
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }
                        }
                        finally
                        {
                            command?.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                    //if (taxMappingRow.TaxTypeAccountCodeMapping_ID == 0)
                    //{
                    //    taxMappingRow.AccountCode = Common.DecryptString(taxMappingRow.AccountCode);
                    //    taxClient.GNM_TaxTypeAccountCodeMapping.Add(taxMappingRow);
                    //    taxClient.SaveChanges();
                    //}
                    //else
                    //{
                    //    int pk = taxMappingRow.TaxTypeAccountCodeMapping_ID;
                    //    GNM_TaxTypeAccountCodeMapping editRow = taxClient.GNM_TaxTypeAccountCodeMapping.Where(a => a.TaxTypeAccountCodeMapping_ID == pk).FirstOrDefault();
                    //    editRow.TaxType_ID = taxMappingRow.TaxType_ID;
                    //    editRow.AccountCode = Common.DecryptString(taxMappingRow.AccountCode);
                    //    taxClient.SaveChanges();
                    //}
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);


            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }

            }
        }
        #endregion
        #region ::: Delete vinay 26/9/24:::
        /// <summary>
        /// to Delete tax mapping
        /// </summary>
        public static IActionResult Delete(DeleteTaxTypeAccountCodeMappingList Obj, string connString, int LogException)
        {
            {
                string errorMsg = "";
                try
                {
                    JTokenReader reader = null;
                    JObject jobj = JObject.Parse(Obj.key);
                    int rowCount = jobj["rows"].Count();
                    GNM_TaxTypeAccountCodeMapping deleteRow = null;
                    int id = 0;
                    for (int i = 0; i < rowCount; i++)
                    {
                        reader = new JTokenReader(jobj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                        reader.Read();
                        id = Convert.ToInt32(reader.Value);
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            string query = "UP_Delete_AM_ERP_Delete_TaxTypeAccountCodeMapping";

                            SqlCommand command = null;

                            try
                            {
                                using (command = new SqlCommand(query, conn))
                                {
                                    command.CommandType = CommandType.StoredProcedure;

                                    command.Parameters.AddWithValue("@TaxTypeAccountCodeMapping_ID", id);


                                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                    {
                                        conn.Open();
                                    }

                                    command.ExecuteScalar();
                                }
                            }
                            catch (Exception ex)
                            {
                                // Log exception
                                if (LogException == 1)
                                {
                                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                }
                            }
                            finally
                            {
                                command?.Dispose();
                                conn.Close();
                                conn.Dispose();
                                SqlConnection.ClearAllPools();
                            }
                        }
                    }


                    errorMsg += CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "deletedsuccessfully").ToString();
                }
                catch (Exception ex)
                {
                    if (ex.InnerException != null)
                    {
                        if (ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                        {
                            errorMsg += CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();//Tr_Resource.Dependencyfoundcannotdeletetherecords;
                        }
                    }
                    else
                    {
                        errorMsg += CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();//"Dependency found cannot delete the records";
                    }
                }
                return new JsonResult(errorMsg);
            }
        }
        #endregion
        #region CheckMapping vinay n 26/9/24
        /// <summary>
        /// CheckMapping
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult CheckMapping(CheckMappingTaxTypeAccountCodeMappingList Obj, string connString, int LogException)
        {
            int result = 0;
            using (SqlConnection conn = new SqlConnection(connString))
            {
                string query = "UP_Chk_AM_ERP_CheckMapping_TaxTypeAccountCodeMapping";

                SqlCommand command = null;

                try
                {
                    using (command = new SqlCommand(query, conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.AddWithValue("@TaxType_ID", Obj.TaxTypeID);
                        command.Parameters.AddWithValue("@AccountCode", Obj.AccountCode);
                        command.Parameters.AddWithValue("@pkID", Obj.pkID);


                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }

                        result = (int)command.ExecuteScalar();
                    }
                }
                catch (Exception ex)
                {
                    // Log exception
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }
                }
                finally
                {
                    command?.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }
                return new JsonResult(result);
            }

        }
        #endregion
        #region ::: Export :::
        /// <summary>
        /// To Export 
        /// </summary>
        public static async Task<object> Export(SelAllTaxTypeAccountCodeMappingList Obj, string connString, int LogException)
        {
            try
            {
                IQueryable<TaxTypeAccountCodeMappingData> IQMappingMaster = null;
                DataTable DtData = new DataTable();
                IQMappingMaster = ((IQueryable<TaxTypeAccountCodeMappingData>)SelAllTaxMappingList(Obj, connString, LogException));
                var Array = from a in IQMappingMaster.AsEnumerable()
                            select new
                            {
                                a.TaxType,
                                a.AccountCode
                            };

                DtData.Columns.Add(CommonFunctionalities.GetResourceString(Obj.GeneralCulture.ToString(), "TaxType").ToString());
                DtData.Columns.Add(CommonFunctionalities.GetResourceString(Obj.GeneralCulture.ToString(), "AccountCode").ToString());

                int Count = Array.AsEnumerable().Count();
                if (Count > 0)
                {
                    for (int i = 0; i < Count; i++)
                    {
                        DtData.Rows.Add(Array.ElementAt(i).TaxType, Array.ElementAt(i).AccountCode);
                    }

                    DataTable DtCriteria = new DataTable();

                    DataTable DtAlignment = new DataTable();
                    DtAlignment.Columns.Add("TaxType");
                    DtAlignment.Columns.Add("AccountCode");
                    DtAlignment.Rows.Add(0, 0);
                    ReportExportList reportExportList = new ReportExportList
                    {
                        Company_ID = Obj.Company_ID, // Assuming this is available in ExportObj
                        Branch = Obj.Branch_ID.ToString(),
                        GeneralLanguageID = Obj.LanguageID,
                        UserLanguageID = Obj.UserLanguageID,
                        Options = DtCriteria,
                        dt = DtData,
                        Alignment = DtAlignment,
                        FileName = "TaxTypeAccountCodeMapping", // Set a default or dynamic filename
                        Header = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "TaxTypeAccountCodeMapping").ToString(), // Set a default or dynamic header
                        exprtType = Obj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                        UserCulture = Obj.UserCulture
                    };

                    //return ReportExport.Export(reportExportList, connString, LogException);

                    var result = await ReportExport.Export(reportExportList, connString, LogException);
                    return result.Value;


                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return null;
        }
        #endregion
        #region
        public static IQueryable<TaxTypeAccountCodeMappingData> SelAllTaxMappingList(SelAllTaxTypeAccountCodeMappingList Obj, string connString, int LogException)
        {

            try
            {
                int count = 0;
                int total = 0;
                var jsonResult = default(dynamic);
                var taxTypeListObj = new List<TaxType>();
                IQueryable<TaxTypeAccountCodeMappingData> iTaxMappingArray = null;

                var taxTypeAccountCodeMappingList = new List<TaxTypeAccountCodeMappingData>();
                string select = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "select").ToString();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_SELECT_AM_ERP_SelAllTaxMapping_TaxTypeAccountCodeMapping";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;

                            command.Parameters.AddWithValue("@UserLanguageID", Convert.ToInt32(Obj.UserLanguageID));
                            command.Parameters.AddWithValue("@GeneralLanguageID", Obj.GeneralLanguageID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                // First result set: MovementTypeDefinition data

                                while (reader.Read())
                                {
                                    var taxType = new TaxType
                                    {
                                        RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                        RefMasterDetail_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"))
                                    };
                                    taxTypeListObj.Add(taxType);
                                }

                                if (reader.NextResult())
                                {
                                    while (reader.Read())
                                    {
                                        var mapping = new TaxTypeAccountCodeMappingData
                                        {
                                            TaxTypeAccountCodeMappingID = reader.GetInt32(reader.GetOrdinal("TaxTypeAccountCodeMappingID")),
                                            TaxTypeID = reader.GetInt32(reader.GetOrdinal("TaxTypeID")),
                                            TaxType = reader.GetString(reader.GetOrdinal("TaxType")),
                                            AccountCode = reader.GetString(reader.GetOrdinal("AccountCode"))

                                        };
                                        taxTypeAccountCodeMappingList.Add(mapping);
                                    }

                                    // Do something with taxTypeAccountCodeMappingList (e.g., return or process further)
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log exception
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                    }
                    finally
                    {
                        command?.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                if (Obj.UserLanguageID == Obj.GeneralLanguageID)
                {
                    var taxTypeList = from a in taxTypeListObj

                                      select new
                                      {
                                          a.RefMasterDetail_ID,
                                          a.RefMasterDetail_Name
                                      };

                    string TaxTypes = "0:----" + select + "----;";
                    for (int i = 0; i < taxTypeList.ToArray().Count(); i++)
                    {
                        TaxTypes = TaxTypes + taxTypeList.ToArray().ElementAt(i).RefMasterDetail_ID + ":" + taxTypeList.ToArray().ElementAt(i).RefMasterDetail_Name + ";";
                    }
                    TaxTypes = TaxTypes.TrimEnd(new char[] { ';' });


                    IEnumerable<TaxTypeAccountCodeMappingData> arrTaxMappingList = from a in taxTypeAccountCodeMappingList

                                                                                   select new TaxTypeAccountCodeMappingData()
                                                                                   {
                                                                                       edit = "<img id='" + a.TaxTypeAccountCodeMappingID + "' src='" + AppPath + "/Content/Images/edit.gif' key='" + a.TaxTypeAccountCodeMappingID + "' class='editTaxTypeAccountCodeMapping' editmode='false'/>",
                                                                                       delete = "<input type='checkbox' key='" + a.TaxTypeAccountCodeMappingID + "' id='chk" + a.TaxTypeAccountCodeMappingID + "' class='chkMappingDelete'/>",
                                                                                       TaxTypeID = a.TaxTypeID,
                                                                                       TaxType = a.TaxType,
                                                                                       AccountCode = a.AccountCode,
                                                                                       TaxTypeAccountCodeMappingID = a.TaxTypeAccountCodeMappingID
                                                                                   };
                    iTaxMappingArray = arrTaxMappingList.AsQueryable<TaxTypeAccountCodeMappingData>();

                    if (Obj.filters != "null" && Obj.filters != "undefined")
                    {
                        string decodedValue = Uri.UnescapeDataString(Obj.filters);
                        Filters filtersObj = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<Filters>();
                        iTaxMappingArray = iTaxMappingArray.FilterSearch<TaxTypeAccountCodeMappingData>(filtersObj);

                    }

                    iTaxMappingArray = iTaxMappingArray.OrderByField<TaxTypeAccountCodeMappingData>(Obj.sidx, Obj.sord);
                    //Session["TaxTypeAccountCodeMappingData"] = iTaxMappingArray;



                }
                else
                {
                    var taxTypeList = from a in taxTypeListObj

                                      select new
                                      {
                                          a.RefMasterDetail_ID,
                                          a.RefMasterDetail_Name
                                      };

                    string TaxTypes = "0:----" + select + "----;";
                    for (int i = 0; i < taxTypeList.ToArray().Count(); i++)
                    {
                        TaxTypes = TaxTypes + taxTypeList.ToArray().ElementAt(i).RefMasterDetail_ID + ":" + taxTypeList.ToArray().ElementAt(i).RefMasterDetail_Name + ";";
                    }
                    TaxTypes = TaxTypes.TrimEnd(new char[] { ';' });


                    IEnumerable<TaxTypeAccountCodeMappingData> arrTaxMappingList = from a in taxTypeAccountCodeMappingList

                                                                                   select new TaxTypeAccountCodeMappingData()
                                                                                   {
                                                                                       edit = "<img id='" + a.TaxTypeAccountCodeMappingID + "' src='" + AppPath + "/Content/Images/edit.gif' key='" + a.TaxTypeAccountCodeMappingID + "' class='editTaxTypeAccountCodeMapping'/>",
                                                                                       delete = "<input type='checkbox' key='" + a.TaxTypeAccountCodeMappingID + "' id='chk" + a.TaxTypeAccountCodeMappingID + "' class='chkMappingDelete'/>",
                                                                                       TaxTypeID = a.TaxTypeID,
                                                                                       TaxType = a.RefMasterDetail_Name,
                                                                                       AccountCode = a.AccountCode,
                                                                                       TaxTypeAccountCodeMappingID = a.TaxTypeAccountCodeMappingID
                                                                                   };
                    iTaxMappingArray = arrTaxMappingList.AsQueryable<TaxTypeAccountCodeMappingData>();

                    if (Obj.filters != "null" && Obj.filters != "undefined")
                    {
                        string decodedValue = Uri.UnescapeDataString(Obj.filters);
                        Filters filtersObj = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<Filters>();
                        iTaxMappingArray = iTaxMappingArray.FilterSearch<TaxTypeAccountCodeMappingData>(filtersObj);

                    }
                    iTaxMappingArray = iTaxMappingArray.OrderByField<TaxTypeAccountCodeMappingData>(Obj.sidx, Obj.sord);
                    //Session["TaxTypeAccountCodeMappingData"] = iTaxMappingArray;


                }
                return iTaxMappingArray;
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                return null;
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return null;
            }
        }
        #endregion
    }
    #region CoreTaxTypeAccountCodeMappingMasterListsAndObjects Vinay 26/9/24
    public class SelAllTaxTypeAccountCodeMappingList
    {
        public string UserCulture { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }

        //export
        public string GeneralCulture { get; set; }
        public int exprtType { get; set; }
        public string filters { get; set; }
        public string sidx { get; set; }
        public string sord { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public int LanguageID { get; set; }
    }
    public class SaveTaxTypeAccountCodeMappingList
    {
        public string Data { get; set; }
    }
    public class DeleteTaxTypeAccountCodeMappingList
    {
        public string key { get; set; }
        public string UserCulture { get; set; }
    }
    public class CheckMappingTaxTypeAccountCodeMappingList
    {
        public int TaxTypeID { get; set; }
        public string AccountCode { get; set; }
        public int pkID { get; set; }
    }
    #endregion

    #region CoreTaxTypeAccountCodeMappingMasterClasses Vinay n 26/9/24
    /// <summary>
    /// CoreTaxTypeAccountCodeMappingMasterClasses
    /// </summary>
    public class TaxTypeAccountCodeMappingData
    {
        public string edit { get; set; }
        public string delete { get; set; }
        public int TaxTypeAccountCodeMappingID { get; set; }
        public int TaxTypeID { get; set; }
        public string TaxType { get; set; }
        public string AccountCode { get; set; }
        public string RefMasterDetail_Name { get; set; }
    }
    public class TaxType
    {
        public int RefMasterDetail_ID { get; set; }
        public string RefMasterDetail_Name { get; set; }
    }
    public partial class GNM_TaxTypeAccountCodeMapping
    {
        public int TaxTypeAccountCodeMapping_ID { get; set; }
        public int TaxType_ID { get; set; }
        public string AccountCode { get; set; }
    }

    #endregion
}
