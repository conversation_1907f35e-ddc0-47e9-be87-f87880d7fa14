﻿using SharedAPIClassLibrary_AMERP;
using SharedAPIClassLibrary_AMERP.Services;
using System;
using System.Configuration;
using System.Web;
using System.Web.Http;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CorePartsMasterController : ApiController
    {

        #region ::: SelectParts:::
        /// <summary>
        /// To select the All parts 
        /// </summary>    
        [Route("api/CorePartsMaster/SelectParts")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParts([FromBody] SelectPartsList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";

            string Query = HttpContext.Current.Request.Params["Query"];

            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }


            try
            {

                Response = CorePartsMasterServices.SelectParts(Obj, Conn, LogException, sidx, sord, page, rows, _search, filters, advnce, Query);
            }
            catch (Exception ex)
            {
                //  ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaCatalougeBOMListLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);

        }
        #endregion

        #region::: Insert Parts Price /Mithun:::
        /// <summary>
        /// TO insert Parts Price
        /// </summary>
        /// <param name="InsertPartsPriceObj"></param>
        /// <returns></returns>
        [Route("api/CorePartsMaster/InsertPartsPrice")]
        [HttpPost]
        //[JwtTokenValidationFilter]
        public IHttpActionResult InsertPartsPrice([FromBody] InsertPartsPriceList InsertPartsPriceObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.InsertPartsPrice(Conn, InsertPartsPriceObj, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region::: Select Parts Product /Mithun:::
        /// <summary>
        /// Select parts product
        /// </summary>
        /// <param name="SelectPartsProductObj"></param>    
        /// <param name="id"></param>
        /// <returns></returns>
        [Route("api/CorePartsMaster/SelectPartsProduct")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartsProduct([FromBody] SelectPartsProductList SelectPartsProductObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }


            try
            {

                Response = CorePartsMasterServices.SelectPartsProduct(SelectPartsProductObj, sidx, rows, page, sord, _search, nd, filters, Conn, LogException);
            }
            catch (Exception ex)
            {
                //  ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaCatalougeBOMListLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);

        }
        #endregion

        #region::: Get Parts Product /Mithun:::
        /// <summary>
        /// Insert Parts Products
        /// </summary>
        /// <param name="InsertPartsProductObj"></param>
        /// <returns></returns>
        [Route("api/CorePartsMaster/InsertPartsProduct")]
        [HttpPost]
       // [JwtTokenValidationFilter]
        public IHttpActionResult InsertPartsProduct([FromBody] InsertPartsProductList InsertPartsProductObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.InsertPartsProduct(InsertPartsProductObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return (Response.Value);
        }

        #endregion

        #region::: DeletePartProductDetail /Mihtun:::
        /// <summary>
        /// Delete Part Product Detail
        /// </summary>
        /// <param name="Delete Part Product Detail"></param>
        /// <returns></returns>
        [Route("api/CorePartsMaster/DeletePartProductDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeletePartProductDetail([FromBody] DeletePartProductDetailList DeletePartProductDetailObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.DeletePartProductDetail(DeletePartProductDetailObj, Conn);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return (Response.Value);
        }

        #endregion

        #region::: SelectPartsFreestock /Mithun:::
        /// <summary>
        /// Select Parts Freestock
        /// </summary>
        /// <param name="SelectPartsFreestockObj"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [Route("api/CorePartsMaster/SelectPartsFreestock")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartsFreeStock([FromBody] SelectPartsFreeStockList SelectPartsFreestockObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }


            try
            {

                Response = CorePartsMasterServices.SelectPartsFreeStock(SelectPartsFreestockObj, sidx, sord, page, rows, filters, Conn, LogException);
            }
            catch (Exception ex)
            {
                //  ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaCatalougeBOMListLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);

        }
        #endregion

        #region::: Load Bin LocationArray /Mithun:::
        /// <summary>
        /// Load Bin Location Array
        /// </summary>
        /// <param name="LoadBinLocationArrayObj"></param>
        /// <returns></returns>
        [Route("api/CorePartsMaster/LoadBinLocationArray")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadBinLocationArray([FromBody] LoadBinLocationArrayList LoadBinLocationArrayObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.LoadBinLocationArray(LoadBinLocationArrayObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region::: LoadBinLocationArray /Mithun:::
        /// <summary>
        /// Load BinLocation Array
        /// </summary>
        /// <param name="LoadBinLocationArrayObj"></param>
        /// <returns></returns>
        [Route("api/CorePartsMaster/ValidateBinLocation")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ValidateBinLocation([FromBody] ValidateBinLocationList ValidateBinLocationObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.ValidateBinLocation(Conn, ValidateBinLocationObj, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region::: Delete Part Free Stock /Mithun:::
        /// <summary>
        /// Delete Parts Fress Stock
        /// </summary>
        /// <param name="ValidateBinLocationObj"></param>
        /// <returns></returns>
        [Route("api/CorePartsMaster/DeletePartFreeStock")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeletePartFreeStock([FromBody] DeletePartFreeStockList DeletePartFreeStockObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.DeletePartFreeStock(DeletePartFreeStockObj, Conn);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion

        #region:::Insert Supplier Details /Mithun:::
        /// <summary>
        /// Insert Supplier Details
        /// </summary>
        /// <param name="InsertSupplierDetailsObj"></param>
        /// <returns></returns>
        [Route("api/CorePartsMaster/InsertSupplierDetails")]
        [HttpPost]
        //[JwtTokenValidationFilter]
        public void InsertSupplierDetails([FromBody] InsertSupplierDetailsList InsertSupplierDetailsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                 CorePartsMasterServices.InsertSupplierDetails(InsertSupplierDetailsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            
        }

        #endregion

        #region::: Delete Part Supplier Details /Mithun:::
        /// <summary>
        /// Delete Part Supplier Details
        /// </summary>
        /// <param name="DeletePartSupplierDetailsObj"></param>
        /// <returns></returns>
        [Route("api/CorePartsMaster/DeletePartSupplierDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeletePartSupplierDetails([FromBody] DeletePartSupplierDetailsList DeletePartSupplierDetailsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.DeletePartSupplierDetails(DeletePartSupplierDetailsObj, Conn);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region::: Validate Part Number /Mithun:::
        /// <summary>
        ///   to Validate Part Number
        /// </summary>
        /// <param name="DeletePartSupplierDetailsObj"></param>
        /// <returns></returns>
        [Route("api/CorePartsMaster/ValidatePartNumber")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ValidatePartNumber([FromBody] ValidatePartNumberList ValidatePartNumberObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.ValidatePartNumber(ValidatePartNumberObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region::: get Previous Date /Mithun:::
        /// <summary>
        ///   to get Previous Date
        /// </summary>
        /// <param name="getPreviousDateObj"></param>
        /// <returns></returns>
        [Route("api/CorePartsMaster/getPreviousDate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult getPreviousDate([FromBody] getPreviousDateList getPreviousDateObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.getPreviousDate(getPreviousDateObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region::: PartProdExits /Mithun:::
        /// <summary>
        /// To check Part Prod Exits
        /// </summary>
        /// <param name="PartProdExitsObj"></param>
        /// <returns></returns>
        [Route("api/CorePartsMaster/PartProdExits")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PartProdExits([FromBody] PartProdExitsList PartProdExitsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.PartProdExits(PartProdExitsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion

        #region ::: MakeLastRowEditable /Mithun:::
        /// <summary>
        ///  To Make Last Row Editable
        /// </summary>   

        [Route("api/CorePartsMaster/MakeLastRowEditable")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult MakeLastRowEditable([FromBody] MakeLastRowEditableList MakeLastRowEditableObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.MakeLastRowEditable(MakeLastRowEditableObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion

        #region ::: SelectProductType /Mithun:::
        /// <summary>
        /// To select ProductType
        /// </summary>  

        [Route("api/CorePartsMaster/SelectProductType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectProductType([FromBody] SelectProductTypeList SelectProductTypeObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.SelectProductType(SelectProductTypeObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region::: Validate FunctionGroup /Mithun:::
        /// <summary>
        ///   to Validate FunctionGroup
        /// </summary>
        /// <param name="ValidatePartNumberObj"></param>
        /// <returns></returns>
        [Route("api/CorePartsMaster/ValidateFunctionGroup")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ValidateFunctionGroup([FromBody] ValidateFunctionGroupList ValidateFunctionGroupObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.ValidateFunctionGroup(ValidateFunctionGroupObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region::: Validate SupplierName /Mithun:::
        /// <summary>
        ///   to Validate SupplierName
        /// </summary>
        /// <param name="ValidateSupplierNameObj"></param>
        /// <returns></returns>
        [Route("api/CorePartsMaster/ValidateSupplierName")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ValidateSupplierName([FromBody] ValidateSupplierNameList ValidateSupplierNameObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.ValidateSupplierName(ValidateSupplierNameObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: validateModel /Mithun:::
        /// <summary>
        /// To Validate PartNumber for duplicate 
        /// </summary>
        [Route("api/CorePartsMaster/validateModel")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult validateModel([FromBody] validateModelList validateModelObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.validateModel(validateModelObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion

        #region ::: SelectPartStockDropDowns /Mithun:::
        /// <summary>
        /// to Load Parts English Dropdowns
        /// </summary>
        [Route("api/CorePartsMaster/SelectPartStockDropDowns")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartStockDropDowns([FromBody] SelectPartStockDropDownsList SelectPartStockDropDownsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.SelectPartStockDropDowns(SelectPartStockDropDownsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectWareHouseByBranch /Mithun:::
        /// <summary>
        /// to Load Parts English Dropdowns
        /// </summary>
        [Route("api/CorePartsMaster/SelectWareHouseByBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectWareHouseByBranch([FromBody] SelectWareHouseByBranchList SelectWareHouseByBranchObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.SelectWareHouseByBranch(SelectWareHouseByBranchObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Binlocation By wareHouse /Mithun:::
        /// <summary>
        /// to Load Parts English Dropdowns
        /// </summary>
        [Route("api/CorePartsMaster/SelectBinlocationByWareHouse")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectBinlocationByWareHouse([FromBody] SelectBinlocationByWareHouseList SelectBinlocationByWareHouseObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.SelectBinlocationByWareHouse(SelectBinlocationByWareHouseObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: InsertPartsSockDetails /Mithun:::
        /// <summary>
        /// Method to insert the Parts Master Header Table
        /// </summary>   

        [Route("api/CorePartsMaster/InsertPartsSockDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertPartsSockDetails([FromBody] InsertPartsSockDetailsList InsertPartsSockDetailsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.InsertPartsSockDetails(InsertPartsSockDetailsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Validate Branch /Mithun:::
        /// <summary>
        /// To check the Duplicate rows in details Grid
        /// </summary>    
        [Route("api/CorePartsMaster/ValidateBranchWareHouseDuplicate")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ValidateBranchWareHouseDuplicate([FromBody] ValidateBranchWareHouseDuplicateList ValidateBranchWareHouseDuplicateObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.ValidateBranchWareHouseDuplicate(ValidateBranchWareHouseDuplicateObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region::: Sales Non Sales YearReport Count /Mithun:::
        /// <summary>
        /// Sales Non Sales YearReport Count
        /// </summary>
        /// <returns></returns>
        [Route("api/CorePartsMaster/SalesNonSalesYearReportCount")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SalesNonSalesYearReportCount([FromBody] SalesNonSalesYearReportCountList SalesNonSalesYearReportCountObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.SalesNonSalesYearReportCount(SalesNonSalesYearReportCountObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select Parts Consumption Year /Mithun:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>

        [Route("api/CorePartsMaster/SelectSalesNonSalesYearReport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSalesNonSalesYearReport([FromBody] SelectSalesNonSalesYearReportList SelectSalesNonSalesYearReportObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartsMasterServices.SelectSalesNonSalesYearReport(SelectSalesNonSalesYearReportObj, Conn, LogException, sidx, sord, page, rows, filters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }

        #endregion


        #region ::: Select Parts Consumption Count Month /Mihtun:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CorePartsMaster/SelectSalesNonSalesMonth")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSalesNonSalesMonth([FromBody] SelectSalesNonSalesMonthList SelectSalesNonSalesMonthObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartsMasterServices.SelectSalesNonSalesMonth(SelectSalesNonSalesMonthObj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion


        #region ::: Select Parts Consumption Count Day /Mithun:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CorePartsMaster/SelectSalesNonSalesDay")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSalesNonSalesDay([FromBody] SelectSalesNonSalesDayList SelectSalesNonSalesDayObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartsMasterServices.SelectSalesNonSalesDay(SelectSalesNonSalesDayObj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }

        #endregion

        #region ::: Select Parts transaction Wise:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CorePartsMaster/SelectSalesNonSalesTransaction")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSalesNonSalesTransaction([FromBody] SelectSalesNonSalesTransactionList SelectSalesNonSalesTransactionObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartsMasterServices.SelectSalesNonSalesTransaction(SelectSalesNonSalesTransactionObj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: SelAllPendingPurchaseOrders /Mithun:::
        /// <summary>
        /// To display all the records
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CorePartsMaster/SelAllPendingPurchaseOrders")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelAllPendingPurchaseOrders([FromBody] SelAllPendingPurchaseOrdersList SelAllPendingPurchaseOrdersObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartsMasterServices.SelAllPendingPurchaseOrders(SelAllPendingPurchaseOrdersObj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: SelAllPendingPurchaseInvoice /Mithun:::
        /// <summary>
        /// To display all the records
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CorePartsMaster/SelAllPendingPurchaseInvoice")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelAllPendingPurchaseInvoice([FromBody] SelAllPendingPurchaseInvoiceList SelAllPendingPurchaseInvoiceObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartsMasterServices.SelAllPendingPurchaseInvoice(SelAllPendingPurchaseInvoiceObj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion
        // not changed in front hand
        #region ::: Select Field Search Salvage Parts /Mithun:::
        /// <summary>
        /// To Select Field Search Salvage Parts
        /// </summary>
        [Route("api/CorePartsMaster/SelectFieldSearchSalvageParts")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectFieldSearchSalvageParts([FromBody] SelectFieldSearchSalvagePartsList SelectFieldSearchSalvagePartsObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartsMasterServices.SelectFieldSearchSalvageParts(SelectFieldSearchSalvagePartsObj, Conn, LogException, sidx, sord, page, rows, _search, filters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region::: Select Part Stock Serial Number DetailsCount /Mithun:::
        /// <summary>
        /// Select Part Stock Serial Number DetailsCount
        /// </summary>
        [Route("api/CorePartsMaster/SelectPartStockSerialNumberDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartStockSerialNumberDetailsCount([FromBody] SelectPartStockSerialNumberDetailsCountList SelectPartStockSerialNumberDetailsCountObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.SelectPartStockSerialNumberDetailsCount(SelectPartStockSerialNumberDetailsCountObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region:::Select Part Stock Serial NumberDetails  /Mithun:::
        /// <summary>
        /// to Select Part Stock Serial NumberDetails
        /// </summary>
        [Route("api/CorePartsMaster/SelectPartStockSerialNumberDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartStockSerialNumberDetails([FromBody] SelectPartStockSerialNumberDetailsList SelectPartStockSerialNumberDetailsObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartsMasterServices.SelectPartStockSerialNumberDetails(SelectPartStockSerialNumberDetailsObj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: To Get GetObjectID /Mithun:::
        /// <summary>
        /// To GetObjectID the record
        /// </summary>
        /// <returns>...</returns>
        [Route("api/CorePartsMaster/GetObjectID")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetObjectID([FromBody] GetObjectIDList GetObjectIDObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.GetObjectID(GetObjectIDObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region :::Get ProductID /Mithun:::
        /// <summary>
        /// to get Product ID
        /// </summary>
        [Route("api/CorePartsMaster/GetProductID")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetProductID([FromBody] GetProductIDList GetProductIDObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.GetProductID(GetProductIDObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: InsertCompetitorPrice /Mithun:::
        /// <summary>
        /// Method to insert the Parts price details 
        /// </summary>   
        [Route("api/CorePartsMaster/InsertCompetitorPrice")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertCompetitorPrice([FromBody] InsertCompetitorPriceList InsertCompetitorPriceObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.InsertCompetitorPrice(InsertCompetitorPriceObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeleteCompetitorPriceDetail /Mithun:::
        /// <summary>
        ///to delete Part product
        /// </summary>    
        [Route("api/CorePartsMaster/DeleteCompetitorPriceDetail")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteCompetitorPriceDetail([FromBody] DeleteCompetitorPriceDetailList DeleteCompetitorPriceDetailObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.DeleteCompetitorPriceDetail(DeleteCompetitorPriceDetailObj, Conn);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectPartsPrice /Mithun:::
        /// <summary>
        ////to Select part price Details
        /// </summary>  
        [Route("api/CorePartsMaster/SelectCompetitorPartsPrice")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCompetitorPartsPrice([FromBody] SelectCompetitorPartsPriceList SelectCompetitorPartsPriceObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartsMasterServices.SelectCompetitorPartsPrice(SelectCompetitorPartsPriceObj, Conn, LogException, sidx, sord, page, rows, _search, filters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion


        #region ::: LoadPartsDropdown /Mithun:::
        /// <summary>
        /// to Load Parts English Dropdowns
        /// </summary>
        [Route("api/CorePartsMaster/LoadPartsDropdown")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadPartsDropdown([FromBody] LoadPartsDropdownList LoadPartsDropdownObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.LoadPartsDropdown(LoadPartsDropdownObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: LoadPartsDropdownLocale /Mithun:::
        /// <summary>
        /// to Load Parts Locale Dropdowns
        /// </summary>
        /// 
        [Route("api/CorePartsMaster/LoadPartsDropdownLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadPartsDropdownLocale([FromBody] LoadPartsDropdownLocaleList LoadPartsDropdownLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.LoadPartsDropdownLocale(LoadPartsDropdownLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }


        #endregion

        #region ::: SelectPartsPrice /Mithun:::
        /// <summary>
        ////to Select part price Details
        /// </summary>   
        [Route("api/CorePartsMaster/SelectPartsPrice")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartsPrice([FromBody] SelectPartsPriceList SelectPartsPriceObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartsMasterServices.SelectPartsPrice(SelectPartsPriceObj, Conn, LogException, sidx, sord, page, rows, _search, filters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion




        #region ::: SelectPartsPriceLocale /Mithun:::
        /// <summary>
        ////to Select part price Details
        /// </summary>   

        [Route("api/CorePartsMaster/SelectPartsPriceLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectPartsPriceLocale([FromBody] SelectPartsPriceLocaleList SelectPartsPriceLocaleObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartsMasterServices.SelectPartsPriceLocale(SelectPartsPriceLocaleObj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion


        #region ::: DeletePart /Mithun:::
        /// <summary>
        /// to Delete the Parts
        /// </summary>
        [Route("api/CorePartsMaster/DeletePart")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeletePart([FromBody] DeletePartList DeletePartObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.DeletePart(DeletePartObj, Conn);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectParticularPartproduct /Mithun:::
        /// <summary>
        /// To select the Particular part product
        /// </summary>  
        [Route("api/CorePartsMaster/SelectParticularPart")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularPart([FromBody] SelectParticularPartList SelectParticularPartObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.SelectParticularPart(SelectParticularPartObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Insert Part:::
        /// <summary>
        /// Method to insert the Parts Master Header Table
        /// </summary>   
        [Route("api/CorePartsMaster/Insert")]
        [HttpPost]
        //[JwtTokenValidationFilter]
        public IHttpActionResult Insert([FromBody] PartInserList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.Insert(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion














        #region ::: Update Part:::
        /// <summary>
        /// Method to update the Parts Master Header Table
        /// </summary>   
        [Route("api/CorePartsMaster/Update")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Update([FromBody] UpdateInserList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.Update(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelAllAttachmentList:::
        /// <summary>
        /// To display all the records
        /// </summary>
        /// <returns>...</returns> 
        [Route("api/CorePartsMaster/SelAllAttachmentList")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelAllAttachmentList([FromBody] AllAttachmentList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CorePartsMasterServices.SelAllAttachmentList(Obj, Conn, LogException, sidx, sord, page, rows, _search, filters);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion



        #region ::: LoadAttachmentDropDown ::: 
        [Route("api/CorePartsMaster/LoadAttachmentDropDown")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadAttachmentDropDown([FromBody] LoadAttachmentDropDownList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CorePartsMasterServices.LoadAttachmentDropDown(Obj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion


      




    }
}