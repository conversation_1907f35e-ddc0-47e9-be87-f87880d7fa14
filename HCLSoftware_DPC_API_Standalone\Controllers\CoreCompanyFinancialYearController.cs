﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreCompanyFinancialYearServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreCompanyFinancialYearController : ApiController
    {
        #region ::: Select /Mithun:::
        /// <summary>
        /// to select all the Financial Year of the company
        /// </summary> 
        [Route("api/CoreCompanyFinancialYear/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] SelectCompanyFinancialYearList SelectObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreCompanyFinancialYearServices.Select(SelectObj, Conn, LogException, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: SelectCompany Mithun:::
        /// <summary>
        /// to get all the Company Names 
        /// </summary> 
        [Route("api/CoreCompanyFinancialYear/SelectCompany")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCompany([FromBody] SelectCompanyList SelectCompanyObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyFinancialYearServices.SelectCompany(SelectCompanyObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Save /Mithun:::
        /// <summary>
        /// to save the data
        /// </summary>
        [Route("api/CoreCompanyFinancialYear/Save")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Save([FromBody] SaveCoreCompanyFinancialList SaveObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyFinancialYearServices.Save(SaveObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Delete Mithun:::
        /// <summary>
        /// to Delete the details
        /// </summary>
        [Route("api/CoreCompanyFinancialYear/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteCoreCompanyFinancialList DeleteObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyFinancialYearServices.Delete(DeleteObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CheckFinancialYear /Mithun:::
        /// <summary>
        /// to check if the financial year is already selected for the company
        /// </summary>
        [Route("api/CoreCompanyFinancialYear/CheckFinancialYear")]
        [HttpPost]
        [AllowAnonymous]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckFinancialYear([FromBody] CheckFinancialYearList CheckFinancialYearObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyFinancialYearServices.CheckFinancialYear(CheckFinancialYearObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: GetAllSavedFinancialYears /Mithun:::
        /// <summary>
        /// to check if the financial year is already selected for the company
        /// </summary>
        [Route("api/CoreCompanyFinancialYear/GetAllSavedFinancialYears")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetAllSavedFinancialYears([FromBody] GetAllSavedFinancialYearsList GetAllSavedFinancialYearsObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreCompanyFinancialYearServices.GetAllSavedFinancialYears(GetAllSavedFinancialYearsObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

    }
}
