﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.HelpDesk_Tr_CaseSummaryServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class HelpDesk_Tr_CaseSummaryController : ApiController
    {


        #region ::: SelectStatusAndBranch Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To Select Status And Branch
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Tr_CaseSummary/SelectStatusAndBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectStatusAndBranch([FromBody] HelpDesk_Tr_CaseSummarySelectStatusAndBranchList HelpDesk_Tr_CaseSummarySelectStatusAndBranchobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Tr_CaseSummaryServices.SelectStatusAndBranch(HelpDesk_Tr_CaseSummarySelectStatusAndBranchobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: SelectTopModel Uday Kumar J B 18-11-2024:::
        /// <summary>
        /// To Select Top Model
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Tr_CaseSummary/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] HelpDesk_Tr_CaseSummarySelectList HelpDesk_Tr_CaseSummarySelectobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Tr_CaseSummaryServices.Select(HelpDesk_Tr_CaseSummarySelectobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion



        #region ::: Export Uday Kumar J B 20-08-2024:::
        /// <summary>
        /// Exporting 
        /// </summary> 
        /// 
        [Route("api/HelpDesk_Tr_CaseSummary/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] HelpDesk_Tr_CaseSummarySelectList HelpDesk_Tr_CaseSummarySelectobj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HelpDesk_Tr_CaseSummarySelectobj.sidx;
            string sord = HelpDesk_Tr_CaseSummarySelectobj.sord;
            string filter = HelpDesk_Tr_CaseSummarySelectobj.filter;
            string advnceFilter = HelpDesk_Tr_CaseSummarySelectobj.advanceFilter;

            try
            {


                Object Response = await HelpDesk_Tr_CaseSummaryServices.Export(HelpDesk_Tr_CaseSummarySelectobj, connstring, LogException, filter, advnceFilter, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion






    }
}