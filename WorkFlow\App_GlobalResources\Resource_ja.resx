﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Abandon" xml:space="preserve">
    <value>放棄する</value>
  </data>
  <data name="AbandonDelayReason" xml:space="preserve">
    <value>放棄する</value>
  </data>
  <data name="Abandoned" xml:space="preserve">
    <value>放棄された</value>
  </data>
  <data name="Accept" xml:space="preserve">
    <value>受け入れる</value>
  </data>
  <data name="AccountNumber" xml:space="preserve">
    <value>口座番号</value>
  </data>
  <data name="Action" xml:space="preserve">
    <value>アクション</value>
  </data>
  <data name="ActionBy" xml:space="preserve">
    <value>によるアクション</value>
  </data>
  <data name="ActionDate" xml:space="preserve">
    <value>アクション日付</value>
  </data>
  <data name="ActionForNextService" xml:space="preserve">
    <value>次のサービスのための行動</value>
  </data>
  <data name="ActionName" xml:space="preserve">
    <value>アクション名</value>
  </data>
  <data name="ActionRemarks" xml:space="preserve">
    <value>アクション備考</value>
  </data>
  <data name="ActionRemarksMaxlimitexceeded" xml:space="preserve">
    <value>アクション備考最大制限を超えました</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>アクション</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>アクティブですか？</value>
  </data>
  <data name="ActiveFrom" xml:space="preserve">
    <value>からアクティブ</value>
  </data>
  <data name="ActiveFromdatecannotbegreaterthanActiveTodate" xml:space="preserve">
    <value>日付からActiveは、現在までに、アクティブより大きくすることはできません</value>
  </data>
  <data name="ActiveTo" xml:space="preserve">
    <value>するために、Active</value>
  </data>
  <data name="ActualHours" xml:space="preserve">
    <value>実働時間</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>加える</value>
  </data>
  <data name="AddAction" xml:space="preserve">
    <value>アクションの追加</value>
  </data>
  <data name="AddBranch" xml:space="preserve">
    <value>ブランチの追加</value>
  </data>
  <data name="AddBranchTaxDetails" xml:space="preserve">
    <value>支店税の詳細を追加</value>
  </data>
  <data name="AddBrands" xml:space="preserve">
    <value>ブランドを追加</value>
  </data>
  <data name="AddCompany" xml:space="preserve">
    <value>会社を追加</value>
  </data>
  <data name="AddCompanyRelation" xml:space="preserve">
    <value>会社関係を追加</value>
  </data>
  <data name="AddCompanyTaxDetails" xml:space="preserve">
    <value>法人税の詳細を追加</value>
  </data>
  <data name="AddCompanyTerms" xml:space="preserve">
    <value>会社の規約を追加</value>
  </data>
  <data name="addcomponentdetails" xml:space="preserve">
    <value>コンポーネントの詳細を追加</value>
  </data>
  <data name="addcustomer" xml:space="preserve">
    <value>顧客を追加</value>
  </data>
  <data name="AddCustomerQuotation" xml:space="preserve">
    <value>カスタマー見積を追加</value>
  </data>
  <data name="AddEmployee" xml:space="preserve">
    <value>従業員を追加</value>
  </data>
  <data name="AddEvents" xml:space="preserve">
    <value>イベントを追加</value>
  </data>
  <data name="AddFilter" xml:space="preserve">
    <value>フィルタの追加</value>
  </data>
  <data name="addfreestock" xml:space="preserve">
    <value>パーツのフリー素材を追加</value>
  </data>
  <data name="AddFunctionGroup" xml:space="preserve">
    <value>ファンクショングループを追加</value>
  </data>
  <data name="AddJobCard" xml:space="preserve">
    <value>ジョブ&amp;#183;カードを追加</value>
  </data>
  <data name="AddMaster" xml:space="preserve">
    <value>マスターを追加</value>
  </data>
  <data name="addmodel" xml:space="preserve">
    <value>モデルを追加</value>
  </data>
  <data name="addnewpart" xml:space="preserve">
    <value>新しいパーツを追加する</value>
  </data>
  <data name="addoperation" xml:space="preserve">
    <value>追加操作</value>
  </data>
  <data name="AddOperationEmployeeDetails" xml:space="preserve">
    <value>操作従業員の詳細を追加</value>
  </data>
  <data name="AddPart" xml:space="preserve">
    <value>パーツを追加する</value>
  </data>
  <data name="addpartprice" xml:space="preserve">
    <value>パーツの価格を追加</value>
  </data>
  <data name="AddParts" xml:space="preserve">
    <value>パーツの追加</value>
  </data>
  <data name="AddParty" xml:space="preserve">
    <value>パーティーを追加</value>
  </data>
  <data name="AddPrefixSuffix" xml:space="preserve">
    <value>プリフィックスサフィックスを追加</value>
  </data>
  <data name="addproduct" xml:space="preserve">
    <value>製品の追加</value>
  </data>
  <data name="addproductdetail" xml:space="preserve">
    <value>製品の詳細を追加</value>
  </data>
  <data name="addproducttype" xml:space="preserve">
    <value>製品タイプを追加</value>
  </data>
  <data name="addproducttypedetails" xml:space="preserve">
    <value>パーツ製品タイプの詳細を追加</value>
  </data>
  <data name="AddRequest" xml:space="preserve">
    <value>リクエストの追加</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>アドレス</value>
  </data>
  <data name="Address1" xml:space="preserve">
    <value>住所1行目</value>
  </data>
  <data name="Address2" xml:space="preserve">
    <value>住所2行目</value>
  </data>
  <data name="Address3" xml:space="preserve">
    <value>住所3</value>
  </data>
  <data name="AddresseFlag" xml:space="preserve">
    <value>Addresse旗</value>
  </data>
  <data name="AddRole" xml:space="preserve">
    <value>ロールの追加</value>
  </data>
  <data name="addServiceCharge" xml:space="preserve">
    <value>サービスチャージを追加</value>
  </data>
  <data name="addServiceChargeDetails" xml:space="preserve">
    <value>サービス料の詳細を追加</value>
  </data>
  <data name="addservicecharges" xml:space="preserve">
    <value>サービス料を追加</value>
  </data>
  <data name="AddServiceType" xml:space="preserve">
    <value>サービスタイプを追加</value>
  </data>
  <data name="addsiteaddress" xml:space="preserve">
    <value>サイトのアドレスを追加</value>
  </data>
  <data name="AddSkills" xml:space="preserve">
    <value>スキルを追加</value>
  </data>
  <data name="AddSpecialization" xml:space="preserve">
    <value>スペシャライゼーションを追加</value>
  </data>
  <data name="AddStep" xml:space="preserve">
    <value>ステップの追加</value>
  </data>
  <data name="AddStepLink" xml:space="preserve">
    <value>ステップのリンクの追加</value>
  </data>
  <data name="AddSundry" xml:space="preserve">
    <value>雑貨追加</value>
  </data>
  <data name="AddTaxCode" xml:space="preserve">
    <value>税コードを追加</value>
  </data>
  <data name="addtaxstructure" xml:space="preserve">
    <value>租税構造を追加</value>
  </data>
  <data name="AddTaxStructureDetails" xml:space="preserve">
    <value>租税構造の詳細を追加</value>
  </data>
  <data name="addtaxtstructuredetails" xml:space="preserve">
    <value>租税構造の詳細を追加</value>
  </data>
  <data name="AddUser" xml:space="preserve">
    <value>ユーザーの追加</value>
  </data>
  <data name="addwarrantydetails" xml:space="preserve">
    <value>保証の詳細を追加</value>
  </data>
  <data name="AddWorkDetails" xml:space="preserve">
    <value>仕事の詳細を追加</value>
  </data>
  <data name="advancesearch" xml:space="preserve">
    <value>アドバンス検索</value>
  </data>
  <data name="all" xml:space="preserve">
    <value>すべて</value>
  </data>
  <data name="Allocate" xml:space="preserve">
    <value>割り当てる</value>
  </data>
  <data name="AllocatedHours" xml:space="preserve">
    <value>割り当てられた時間</value>
  </data>
  <data name="AllocationNotPossible" xml:space="preserve">
    <value>割り当てはできません</value>
  </data>
  <data name="AlloctedSuccessfully" xml:space="preserve">
    <value>首尾よくAllocted</value>
  </data>
  <data name="AlloctionFailed" xml:space="preserve">
    <value>Alloctionに失敗しました</value>
  </data>
  <data name="AllQueue" xml:space="preserve">
    <value>すべてのキュー</value>
  </data>
  <data name="AlreadyAssociatedPleaseSelectfromDropDown" xml:space="preserve">
    <value>既に関連付けられたドロップダウンから選択してください</value>
  </data>
  <data name="alreadyexists" xml:space="preserve">
    <value>郵便番号</value>
    <comment>Message</comment>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>量</value>
  </data>
  <data name="AmountBlank" xml:space="preserve">
    <value>金額は空白です</value>
  </data>
  <data name="AmountIsBeyondAcceptableLimit" xml:space="preserve">
    <value>量が許容限度を超えている</value>
  </data>
  <data name="AmountShouldbeLessThan" xml:space="preserve">
    <value>量がより小さくなければなりません</value>
  </data>
  <data name="AND" xml:space="preserve">
    <value>フローステップを働かせる</value>
    <comment>Label</comment>
  </data>
  <data name="ApprovalLimit" xml:space="preserve">
    <value>承認制限</value>
  </data>
  <data name="April" xml:space="preserve">
    <value>4月</value>
  </data>
  <data name="Aresurewanttodelete" xml:space="preserve">
    <value>必ず削除したい</value>
  </data>
  <data name="Areyousurewanttocancel" xml:space="preserve">
    <value>あなたがキャンセルしてもよろしいです</value>
  </data>
  <data name="Areyousurewanttodelete" xml:space="preserve">
    <value>あなたは確信して削除したいのですか？</value>
  </data>
  <data name="AreyousureyouwanttoAbandontheRequest" xml:space="preserve">
    <value>あなたが要求を放棄してもよろしいです</value>
  </data>
  <data name="areyousureyouwanttomovetheevent" xml:space="preserve">
    <value>は、イベントを移動してもよろしいですか？</value>
  </data>
  <data name="Assign" xml:space="preserve">
    <value>郵便番号</value>
    <comment>Label</comment>
  </data>
  <data name="AssignedTo" xml:space="preserve">
    <value>に割り当てられている</value>
  </data>
  <data name="AssignTo" xml:space="preserve">
    <value>に割り当てる</value>
  </data>
  <data name="Atleastselectonedetail" xml:space="preserve">
    <value>少なくともいずれかを選択します。詳細</value>
  </data>
  <data name="August" xml:space="preserve">
    <value>8月</value>
  </data>
  <data name="AuthorizedSignatory" xml:space="preserve">
    <value>署名権者</value>
  </data>
  <data name="autoallocate" xml:space="preserve">
    <value>自動割り当て</value>
  </data>
  <data name="AutoAllocationAllowed" xml:space="preserve">
    <value>自動割り当て可</value>
  </data>
  <data name="AverageResolutionTime" xml:space="preserve">
    <value>平均解決時間</value>
  </data>
  <data name="averageresolutiontimeyearwise" xml:space="preserve">
    <value>平均解決時間 - ワイズ年</value>
  </data>
  <data name="averageresponsetime" xml:space="preserve">
    <value>平均応答時間</value>
  </data>
  <data name="averageresponsetimeyearwise" xml:space="preserve">
    <value>平均レスポンスタイムイヤーワイズ</value>
  </data>
  <data name="AverageTime" xml:space="preserve">
    <value>平均時間</value>
  </data>
  <data name="AvgResolutionTime" xml:space="preserve">
    <value>平均解決時間</value>
  </data>
  <data name="BankName" xml:space="preserve">
    <value>銀行名</value>
  </data>
  <data name="BarChart" xml:space="preserve">
    <value>棒グラフ</value>
  </data>
  <data name="Branch" xml:space="preserve">
    <value>ブランチ</value>
  </data>
  <data name="BranchAlrearySelected" xml:space="preserve">
    <value>ブランチalrearyは、選択した</value>
  </data>
  <data name="BranchAssociation" xml:space="preserve">
    <value>支部協会</value>
  </data>
  <data name="branchdetail" xml:space="preserve">
    <value>枝の詳細</value>
  </data>
  <data name="BranchName" xml:space="preserve">
    <value>支店名</value>
  </data>
  <data name="BranchNameisalreadypresent" xml:space="preserve">
    <value>支店名は既に存在している</value>
  </data>
  <data name="BranchTaxCode" xml:space="preserve">
    <value>支店税コード</value>
  </data>
  <data name="BranchTaxDetails" xml:space="preserve">
    <value>支店税の詳細</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>ブランド</value>
  </data>
  <data name="BrandName" xml:space="preserve">
    <value>ブランド名</value>
  </data>
  <data name="BrandsAssociation" xml:space="preserve">
    <value>ブランド協会</value>
  </data>
  <data name="Browse" xml:space="preserve">
    <value>ブラウズ</value>
  </data>
  <data name="calculateformula" xml:space="preserve">
    <value>数式を計算</value>
  </data>
  <data name="Calendar" xml:space="preserve">
    <value>カレンダー</value>
  </data>
  <data name="CallBackDate" xml:space="preserve">
    <value>戻る日を呼び出す</value>
  </data>
  <data name="CallBackTime" xml:space="preserve">
    <value>時間をコールバック</value>
  </data>
  <data name="CallClosureDate" xml:space="preserve">
    <value>閉鎖日を呼び出す</value>
  </data>
  <data name="CallClosureTime" xml:space="preserve">
    <value>閉鎖時間を呼び出す</value>
  </data>
  <data name="CallDate" xml:space="preserve">
    <value>日付を呼び出す</value>
  </data>
  <data name="CallDateCanNotBeGreaterThanCurrentDate" xml:space="preserve">
    <value>CallDateは、現在の日付より大きくすることはできません</value>
  </data>
  <data name="CallDescription" xml:space="preserve">
    <value>説明を呼び出&amp;#8203;&amp;#8203;す</value>
  </data>
  <data name="CallDetails" xml:space="preserve">
    <value>コー&amp;#8203;&amp;#8203;ル詳細</value>
  </data>
  <data name="CallMode" xml:space="preserve">
    <value>モードを呼び出す</value>
  </data>
  <data name="CallNature" xml:space="preserve">
    <value>自然を呼び出す</value>
  </data>
  <data name="CallStatus" xml:space="preserve">
    <value>ステータスを呼び出す</value>
  </data>
  <data name="CallTime" xml:space="preserve">
    <value>時間を呼び出す</value>
  </data>
  <data name="CallType" xml:space="preserve">
    <value>コー&amp;#8203;&amp;#8203;ルタイプ</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>キャンセル</value>
  </data>
  <data name="CannotdeleteasTaxTypeisreferenced" xml:space="preserve">
    <value>Cannot delete as Tax Type is referenced</value>
  </data>
  <data name="CannotDeleteinEditMode" xml:space="preserve">
    <value>編集モードで削除することはできません</value>
  </data>
  <data name="CaseProgress" xml:space="preserve">
    <value>ケースの進捗状況</value>
  </data>
  <data name="CaseProgressHistory" xml:space="preserve">
    <value>ケースの進捗履歴</value>
  </data>
  <data name="CauseofFailure" xml:space="preserve">
    <value>失敗の原因</value>
  </data>
  <data name="Changeswillbelostdoyouwanttoproceed" xml:space="preserve">
    <value>変更内容は失われます続行しますか</value>
  </data>
  <data name="ChooseColumnNames" xml:space="preserve">
    <value>列名を選択してください</value>
  </data>
  <data name="clearformula" xml:space="preserve">
    <value>クリアフォーミュラ</value>
  </data>
  <data name="close" xml:space="preserve">
    <value>閉じる</value>
  </data>
  <data name="Closed" xml:space="preserve">
    <value>閉店</value>
  </data>
  <data name="ClosedCount" xml:space="preserve">
    <value>クローズドカウント</value>
  </data>
  <data name="ClosureDetails" xml:space="preserve">
    <value>閉鎖の詳細</value>
  </data>
  <data name="closurereason" xml:space="preserve">
    <value>閉鎖の理由</value>
  </data>
  <data name="ClosureType" xml:space="preserve">
    <value>閉鎖型</value>
  </data>
  <data name="code" xml:space="preserve">
    <value>コード</value>
  </data>
  <data name="ColumnNames" xml:space="preserve">
    <value>列名</value>
  </data>
  <data name="commissioningdate" xml:space="preserve">
    <value>日付のコミッショニング</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>会社</value>
  </data>
  <data name="CompanyBrands" xml:space="preserve">
    <value>企業ブランド</value>
  </data>
  <data name="CompanyCalender" xml:space="preserve">
    <value>Ja Company Calender</value>
  </data>
  <data name="CompanyFinancialYear" xml:space="preserve">
    <value>Ja Company Financial Year</value>
    <comment>Label</comment>
  </data>
  <data name="CompanyHeader" xml:space="preserve">
    <value>ヘッダ</value>
  </data>
  <data name="CompanyMaster" xml:space="preserve">
    <value>会社マスター</value>
  </data>
  <data name="CompanyName" xml:space="preserve">
    <value>会社名</value>
  </data>
  <data name="CompanyNameisalreadypresent" xml:space="preserve">
    <value>会社名は既に存在している</value>
  </data>
  <data name="CompanyRelation" xml:space="preserve">
    <value>会社 - 会社関係</value>
  </data>
  <data name="CompanyRelationships" xml:space="preserve">
    <value>会社との関係</value>
  </data>
  <data name="CompanySavedPleaseAssociateatleastoneBrand" xml:space="preserve">
    <value>会社ヘッダが完全に成功を保存し、少なくとも準の1つのブランドをしてください</value>
  </data>
  <data name="CompanyTaxCode" xml:space="preserve">
    <value>会社税コード</value>
  </data>
  <data name="CompanyTaxDetails" xml:space="preserve">
    <value>法人税の詳細</value>
  </data>
  <data name="CompanyTerms" xml:space="preserve">
    <value>会社の規約</value>
  </data>
  <data name="CompanyTheme" xml:space="preserve">
    <value>会社のテーマ</value>
  </data>
  <data name="CompanyType" xml:space="preserve">
    <value>会社の種類</value>
  </data>
  <data name="Completed" xml:space="preserve">
    <value>完成した</value>
  </data>
  <data name="CompletedCount" xml:space="preserve">
    <value>完成したカウント</value>
  </data>
  <data name="CompleteEnteringDetail" xml:space="preserve">
    <value>詳細を入力し完了</value>
  </data>
  <data name="CompleteEnteringDetailParts" xml:space="preserve">
    <value>部品の詳細を入力し完了</value>
  </data>
  <data name="CompleteEnteringDetailService" xml:space="preserve">
    <value>サービスの詳細を入力し完了</value>
  </data>
  <data name="CompleteEnteringDetailSundry" xml:space="preserve">
    <value>諸口詳細を入力完了</value>
  </data>
  <data name="componentdetails" xml:space="preserve">
    <value>コンポーネントの詳細</value>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>Ja Confirm Password</value>
  </data>
  <data name="ContactPerson" xml:space="preserve">
    <value>コンタクトパーソン</value>
  </data>
  <data name="ContactPersons" xml:space="preserve">
    <value>コンタクトパーソン</value>
  </data>
  <data name="CopyRoleFrom" xml:space="preserve">
    <value>コピー元の役割</value>
  </data>
  <data name="CorrectiveAction" xml:space="preserve">
    <value>是正処置</value>
  </data>
  <data name="Count" xml:space="preserve">
    <value>カウント</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>カントリー</value>
  </data>
  <data name="Created" xml:space="preserve">
    <value>作成</value>
  </data>
  <data name="CreateJobCard" xml:space="preserve">
    <value>JobCardを作成</value>
  </data>
  <data name="CreateNew" xml:space="preserve">
    <value>新規作成</value>
  </data>
  <data name="CreateQuotation" xml:space="preserve">
    <value>見積書を作成します。</value>
  </data>
  <data name="CreateServiceRequest" xml:space="preserve">
    <value>サービスリクエストを作成する</value>
  </data>
  <data name="crictical" xml:space="preserve">
    <value>Crictical</value>
  </data>
  <data name="Critical" xml:space="preserve">
    <value>クリティカル</value>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>通貨</value>
  </data>
  <data name="CurrentStep" xml:space="preserve">
    <value>電流ステップ</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>顧客</value>
  </data>
  <data name="CustomerComplaint" xml:space="preserve">
    <value>顧客の苦情</value>
  </data>
  <data name="CustomerContact" xml:space="preserve">
    <value>顧客との接触</value>
  </data>
  <data name="CustomerContactPhone" xml:space="preserve">
    <value>お客様の連絡先電話番号</value>
  </data>
  <data name="customerdetails" xml:space="preserve">
    <value>お客様の詳細</value>
  </data>
  <data name="customerdueservices" xml:space="preserve">
    <value>顧客によるサービス</value>
  </data>
  <data name="CustomerisLockedDoyouwanttocontinue" xml:space="preserve">
    <value>顧客がロックされ、続行してもよろしいでしょうか？</value>
  </data>
  <data name="Customerisnotactive" xml:space="preserve">
    <value>顧客はアクティブではありません</value>
  </data>
  <data name="CustomerLocation" xml:space="preserve">
    <value>顧客の場所</value>
  </data>
  <data name="customername" xml:space="preserve">
    <value>顧客名</value>
  </data>
  <data name="CustomerNotFound" xml:space="preserve">
    <value>顧客が見つかりません</value>
  </data>
  <data name="CustomerQuotation" xml:space="preserve">
    <value>顧客の見積</value>
  </data>
  <data name="CustomerQuotationDate" xml:space="preserve">
    <value>引用日</value>
  </data>
  <data name="CustomerQuotationNumber" xml:space="preserve">
    <value>引用数</value>
  </data>
  <data name="CustomerQuotationSummary" xml:space="preserve">
    <value>顧客見積概要</value>
  </data>
  <data name="CustomerRating" xml:space="preserve">
    <value>お客様からの評価</value>
  </data>
  <data name="customersearch" xml:space="preserve">
    <value>顧客の検索</value>
  </data>
  <data name="CustomerServiceHistory" xml:space="preserve">
    <value>カスタマーサービス履歴</value>
  </data>
  <data name="DataSavedSuccessfully" xml:space="preserve">
    <value>データが正常に保存されました</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>日付</value>
  </data>
  <data name="DateCannotbelessthenCurrentDate" xml:space="preserve">
    <value>日から有効では少ないし、現在の日付にすることはできません</value>
  </data>
  <data name="Datecannotbelessthenpreviousdate" xml:space="preserve">
    <value>日付は少ないし、前のレコードの日付にすることはできません</value>
  </data>
  <data name="dateselectedmustbegreaterthenpreviouscustomer" xml:space="preserve">
    <value>選択した日付が前のカスタマーその後大きくなければなりません。</value>
  </data>
  <data name="DaysLeft" xml:space="preserve">
    <value>余日</value>
  </data>
  <data name="Dealer" xml:space="preserve">
    <value>ディーラー</value>
  </data>
  <data name="DealersorCompany" xml:space="preserve">
    <value>ディーラー/会社</value>
  </data>
  <data name="December" xml:space="preserve">
    <value>12月</value>
  </data>
  <data name="DefaultGridSize" xml:space="preserve">
    <value>デフォルトのグリッドサイズ</value>
  </data>
  <data name="Defaultgridsizealreadyavailable" xml:space="preserve">
    <value>Ja Default grid size already available</value>
    <comment>Message</comment>
  </data>
  <data name="DefaultgridSizeshouldbebetweenzeroandtwofiftyfive" xml:space="preserve">
    <value>デフォルトのグリッドサイズは、0&amp;#12316;255の間&amp;#8203;&amp;#8203;でなければなりません</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>削除</value>
  </data>
  <data name="DeleteAction" xml:space="preserve">
    <value>アクションを削除する</value>
  </data>
  <data name="DeleteBranch" xml:space="preserve">
    <value>ブランチを削除</value>
  </data>
  <data name="DeleteBranchTaxDetails" xml:space="preserve">
    <value>支店税の詳細を削除</value>
  </data>
  <data name="DeleteBrands" xml:space="preserve">
    <value>ブランドを削除</value>
  </data>
  <data name="DeleteCompany" xml:space="preserve">
    <value>会社を削除</value>
  </data>
  <data name="DeleteCompanyRelation" xml:space="preserve">
    <value>会社との関係を削除</value>
  </data>
  <data name="DeleteCompanyTerms" xml:space="preserve">
    <value>会社の規約を削除</value>
  </data>
  <data name="deletedsuccessfully" xml:space="preserve">
    <value>正常に削除さ</value>
  </data>
  <data name="DeleteEmployee" xml:space="preserve">
    <value>従業員を削除</value>
  </data>
  <data name="deletefreestock" xml:space="preserve">
    <value>パートフリーを削除</value>
  </data>
  <data name="DeleteFunctionGroup" xml:space="preserve">
    <value>ファンクショングループを削除する</value>
  </data>
  <data name="DeleteJobCard" xml:space="preserve">
    <value>ジョブ&amp;#183;カードを削除</value>
  </data>
  <data name="DeleteMaster" xml:space="preserve">
    <value>マスターを削除</value>
  </data>
  <data name="deletemodel" xml:space="preserve">
    <value>モデルを削除</value>
  </data>
  <data name="deleteoperation" xml:space="preserve">
    <value>削除操作</value>
  </data>
  <data name="DeleteOperationEmployeeDetails" xml:space="preserve">
    <value>操作従業員の詳細を削除</value>
  </data>
  <data name="deletepart" xml:space="preserve">
    <value>パーツを削除</value>
  </data>
  <data name="deletepartprice" xml:space="preserve">
    <value>パーツの価格を削除</value>
  </data>
  <data name="deletepartproducttype" xml:space="preserve">
    <value>部品、製品の種類を削除</value>
  </data>
  <data name="DeleteParts" xml:space="preserve">
    <value>パーツを削除する</value>
  </data>
  <data name="DeleteParty" xml:space="preserve">
    <value>パーティーを削除</value>
  </data>
  <data name="DeletePrefixSuffix" xml:space="preserve">
    <value>プリフィックスサフィックスを削除</value>
  </data>
  <data name="deleteproduct" xml:space="preserve">
    <value>製品を削除</value>
  </data>
  <data name="deleteproductdetail" xml:space="preserve">
    <value>製品の詳細を削除</value>
  </data>
  <data name="deleteproducttype" xml:space="preserve">
    <value>製品タイプを削除</value>
  </data>
  <data name="DeleteQuotation" xml:space="preserve">
    <value>見積を削除</value>
  </data>
  <data name="DeleteRequest" xml:space="preserve">
    <value>リクエストを削除</value>
  </data>
  <data name="DeleteRole" xml:space="preserve">
    <value>役割の削除</value>
  </data>
  <data name="deleteServiceCharge" xml:space="preserve">
    <value>サービスチャージを削除</value>
  </data>
  <data name="deleteServiceChargeDetails" xml:space="preserve">
    <value>サービス料の詳細を削除</value>
  </data>
  <data name="DeleteServiceType" xml:space="preserve">
    <value>サービスの種類を削除</value>
  </data>
  <data name="DeleteSkills" xml:space="preserve">
    <value>スキルを削除</value>
  </data>
  <data name="DeleteSpecialization" xml:space="preserve">
    <value>スペシャライゼーションを削除</value>
  </data>
  <data name="DeleteStep" xml:space="preserve">
    <value>ステップを削除</value>
  </data>
  <data name="DeleteStepLink" xml:space="preserve">
    <value>ステップのリンクの削除</value>
  </data>
  <data name="DeleteSundry" xml:space="preserve">
    <value>雑貨削除</value>
  </data>
  <data name="DeleteTaxCode" xml:space="preserve">
    <value>税コードを削除</value>
  </data>
  <data name="DeleteTaxDetails" xml:space="preserve">
    <value>税の詳細を削除</value>
  </data>
  <data name="deletetaxstructure" xml:space="preserve">
    <value>租税構造を削除</value>
  </data>
  <data name="deletetaxtstructuredetails" xml:space="preserve">
    <value>租税構造の詳細を削除</value>
  </data>
  <data name="DeleteUser" xml:space="preserve">
    <value>ユーザーの削除</value>
  </data>
  <data name="deletewarrantydetails" xml:space="preserve">
    <value>保証内容を削除</value>
  </data>
  <data name="DeleteWorkDetails" xml:space="preserve">
    <value>作業内容を削除</value>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>納期</value>
  </data>
  <data name="Department" xml:space="preserve">
    <value>部門</value>
  </data>
  <data name="Dependencyfoundcannotdeletetherecords" xml:space="preserve">
    <value>依存関係は、レコードを削除することはできませんが見つかりました</value>
  </data>
  <data name="description" xml:space="preserve">
    <value>説明</value>
  </data>
  <data name="Designation" xml:space="preserve">
    <value>指定</value>
  </data>
  <data name="DestinationColumns" xml:space="preserve">
    <value>変換先列</value>
  </data>
  <data name="Detail" xml:space="preserve">
    <value>詳細</value>
  </data>
  <data name="DeviationHours" xml:space="preserve">
    <value>偏差時間</value>
  </data>
  <data name="DeviationPercentage" xml:space="preserve">
    <value>偏差％</value>
  </data>
  <data name="Discount" xml:space="preserve">
    <value>割引</value>
  </data>
  <data name="Discountamount" xml:space="preserve">
    <value>割引額</value>
  </data>
  <data name="DiscountedAmount" xml:space="preserve">
    <value>割引額</value>
  </data>
  <data name="DiscountPercentage" xml:space="preserve">
    <value>割引率</value>
  </data>
  <data name="DiscountShouldbeLessThan" xml:space="preserve">
    <value>割引は未満でなければなりません</value>
  </data>
  <data name="DivideByZeroException" xml:space="preserve">
    <value>アプリケーションエラーが発生しました</value>
  </data>
  <data name="DocumentExport" xml:space="preserve">
    <value>郵便番号</value>
    <comment>Label</comment>
  </data>
  <data name="DonotEnterSpace" xml:space="preserve">
    <value>スペースを入力しないでください</value>
  </data>
  <data name="DoyouwanttoaddthisSerialNumber" xml:space="preserve">
    <value>このシリアル番号を追加しますか</value>
  </data>
  <data name="Doyouwanttochangetheassociation" xml:space="preserve">
    <value>関連付けを変更したいですか？</value>
  </data>
  <data name="Doyouwanttocreatenewversion" xml:space="preserve">
    <value>新しいバージョンを作成したいですか？</value>
  </data>
  <data name="DueDate" xml:space="preserve">
    <value>期日</value>
  </data>
  <data name="DueDatecannotbeBlank" xml:space="preserve">
    <value>期日は空白にすることはできません</value>
  </data>
  <data name="DueRange" xml:space="preserve">
    <value>による範囲</value>
  </data>
  <data name="Duplicate" xml:space="preserve">
    <value>Ja Duplicate</value>
  </data>
  <data name="duplicatebranch" xml:space="preserve">
    <value>支店を複製</value>
  </data>
  <data name="DuplicateBranchName" xml:space="preserve">
    <value>Ja Duplicate Branch Name</value>
  </data>
  <data name="DuplicateBrand" xml:space="preserve">
    <value>Ja Duplicate Brand</value>
  </data>
  <data name="DuplicateCode" xml:space="preserve">
    <value>Duplicate Code</value>
    <comment>Message</comment>
  </data>
  <data name="DuplicateCode1" xml:space="preserve">
    <value>Duplicate Description</value>
    <comment>Message</comment>
  </data>
  <data name="DuplicateCodeandDescription" xml:space="preserve">
    <value>Duplicate Code and Description</value>
    <comment>Message</comment>
  </data>
  <data name="DuplicateCompanyName" xml:space="preserve">
    <value>Ja Duplicate Company Name</value>
  </data>
  <data name="duplicatedate" xml:space="preserve">
    <value>Ja Duplicate date</value>
  </data>
  <data name="DuplicateDescription" xml:space="preserve">
    <value>説明を複製</value>
  </data>
  <data name="DuplicateEmailsFound" xml:space="preserve">
    <value>見つかった電子メールを複製</value>
  </data>
  <data name="Duplicateentries" xml:space="preserve">
    <value>エントリが重複している</value>
  </data>
  <data name="Duplicateentriesof" xml:space="preserve">
    <value>フローステップを働かせる</value>
    <comment>Message</comment>
  </data>
  <data name="DuplicateLoginId" xml:space="preserve">
    <value>Ja Duplicate Login Id</value>
  </data>
  <data name="DuplicateMaster" xml:space="preserve">
    <value>Ja Duplicate Master</value>
  </data>
  <data name="duplicatemodel" xml:space="preserve">
    <value>モデルを複製</value>
  </data>
  <data name="DuplicateModule" xml:space="preserve">
    <value>Ja Duplicate Module</value>
  </data>
  <data name="DuplicateOperationCode" xml:space="preserve">
    <value>ja Duplicate Operation Code</value>
  </data>
  <data name="Duplicatepartnumber" xml:space="preserve">
    <value>Ja Duplicate part number</value>
  </data>
  <data name="DuplicatePhoneNumbersFound" xml:space="preserve">
    <value>見つかった電話番号を重複</value>
  </data>
  <data name="duplicateproducttype" xml:space="preserve">
    <value>製品タイプを複製</value>
  </data>
  <data name="DuplicateRole" xml:space="preserve">
    <value>Ja Duplicate Role</value>
  </data>
  <data name="duplicatesecondarysegment" xml:space="preserve">
    <value>セカンダリセグメントを複製</value>
  </data>
  <data name="Duplicateservicecode" xml:space="preserve">
    <value>Ja Duplicate service code</value>
  </data>
  <data name="duplicateservicedate" xml:space="preserve">
    <value>サービスの日付を複製</value>
  </data>
  <data name="duplicateservicetype" xml:space="preserve">
    <value>サービスタイプを複製</value>
  </data>
  <data name="DuplicateServiceTypeisnotallowed" xml:space="preserve">
    <value>重複したサービスタイプは許可されていません</value>
  </data>
  <data name="duplicatestate" xml:space="preserve">
    <value>Ja duplicate state</value>
  </data>
  <data name="duplicatestatename" xml:space="preserve">
    <value>状態名が重複して</value>
  </data>
  <data name="DuplicateSundryDescriptionisnotAllowed" xml:space="preserve">
    <value>諸口記述を複製することはできません</value>
  </data>
  <data name="DuplicateTaxname" xml:space="preserve">
    <value>Ja Duplicate Tax name</value>
  </data>
  <data name="DuplicateTaxType" xml:space="preserve">
    <value>Ja Duplicate Tax Type</value>
  </data>
  <data name="DuplicateUniqueidentifier" xml:space="preserve">
    <value>Ja Duplicate Unique identifier</value>
  </data>
  <data name="edit" xml:space="preserve">
    <value>編集</value>
  </data>
  <data name="EditAction" xml:space="preserve">
    <value>アクションの編集</value>
  </data>
  <data name="EditCompany" xml:space="preserve">
    <value>当社の編集</value>
  </data>
  <data name="editcomponentdetails" xml:space="preserve">
    <value>コンポーネントの詳細を編集</value>
  </data>
  <data name="EditEvents" xml:space="preserve">
    <value>イベントを編集</value>
  </data>
  <data name="EditJobCard" xml:space="preserve">
    <value>ジョブ&amp;#183;カードを編集</value>
  </data>
  <data name="editmodel" xml:space="preserve">
    <value>モデルを編集</value>
  </data>
  <data name="editoperation" xml:space="preserve">
    <value>操作を編集</value>
  </data>
  <data name="EditPartsMaster" xml:space="preserve">
    <value>エディットパーツマスター</value>
  </data>
  <data name="editproduct" xml:space="preserve">
    <value>商品を編集</value>
  </data>
  <data name="editproducttype" xml:space="preserve">
    <value>製品タイプを編集</value>
  </data>
  <data name="editsecondarysegment" xml:space="preserve">
    <value>セカンダリセグメントを編集</value>
  </data>
  <data name="editsevicecharges" xml:space="preserve">
    <value>サービス料を編集</value>
  </data>
  <data name="editsiteaddress" xml:space="preserve">
    <value>サイトのアドレスを編集する</value>
  </data>
  <data name="editstate" xml:space="preserve">
    <value>編集状態</value>
  </data>
  <data name="edittaxstructure" xml:space="preserve">
    <value>租税構造を編集</value>
  </data>
  <data name="editwarrantydeatils" xml:space="preserve">
    <value>保証Deatilsを編集</value>
  </data>
  <data name="effectivefrom" xml:space="preserve">
    <value>より効果的な</value>
  </data>
  <data name="EightHour" xml:space="preserve">
    <value>&amp;lt;8時間</value>
  </data>
  <data name="EightToSixteenHours" xml:space="preserve">
    <value>8&amp;#12316;16時間</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>メール</value>
  </data>
  <data name="EmailToAddresse" xml:space="preserve">
    <value>Addresseへのメール</value>
  </data>
  <data name="EmailToCustomer" xml:space="preserve">
    <value>顧客へのメール</value>
  </data>
  <data name="Employee" xml:space="preserve">
    <value>従業員</value>
  </data>
  <data name="EmployeeBranch" xml:space="preserve">
    <value>従業員 - ブランチ</value>
  </data>
  <data name="EmployeeDetails" xml:space="preserve">
    <value>従業員の詳細</value>
  </data>
  <data name="EmployeeID" xml:space="preserve">
    <value>従業員コード</value>
  </data>
  <data name="EmployeeIDisalreadyused" xml:space="preserve">
    <value>従業員コードはすでに使用されています</value>
  </data>
  <data name="EmployeeisalreadyassociatedwiththeBranch" xml:space="preserve">
    <value>従業員が、既に枝に関連付けられている</value>
  </data>
  <data name="EmployeeisalreadyassociatedwiththeSpecialization" xml:space="preserve">
    <value>従業員はすでに分業に関連付けられている</value>
  </data>
  <data name="EmployeeName" xml:space="preserve">
    <value>従業員</value>
  </data>
  <data name="EmployeeNotFound" xml:space="preserve">
    <value>従業員が見つからない</value>
  </data>
  <data name="EmployeeSkills" xml:space="preserve">
    <value>スキル</value>
  </data>
  <data name="EmployeeSpecialization" xml:space="preserve">
    <value>分業</value>
  </data>
  <data name="EndDatecannotbelessthanStartDate" xml:space="preserve">
    <value>終了日は開始日より小さくすることはできません</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>英語</value>
  </data>
  <data name="EnquiryDate" xml:space="preserve">
    <value>問い合わせ日</value>
  </data>
  <data name="EnquiryNumber" xml:space="preserve">
    <value>お問い合わせ番号</value>
  </data>
  <data name="EnterCode" xml:space="preserve">
    <value>コー&amp;#8203;&amp;#8203;ドを入力してください</value>
  </data>
  <data name="EnterDescription" xml:space="preserve">
    <value>説明を入力します</value>
  </data>
  <data name="EnteredNumberdoesnotbelongstocustomerorprospect" xml:space="preserve">
    <value>入力した番号のdoesnot顧客や見込み客に属し</value>
  </data>
  <data name="enterfromdate" xml:space="preserve">
    <value>日付から入力</value>
  </data>
  <data name="EnterMasterName" xml:space="preserve">
    <value>マスター名を入力してください</value>
  </data>
  <data name="enterNonTaxableothercharges1" xml:space="preserve">
    <value>非課税その他の費用は、1を入力します</value>
  </data>
  <data name="enterNonTaxableothercharges2" xml:space="preserve">
    <value>非課税その他の費用は、2を入力します</value>
  </data>
  <data name="enterTaxableothercharges1" xml:space="preserve">
    <value>課税対象となるその他の費用は、1を入力します</value>
  </data>
  <data name="enterTaxableothercharges2" xml:space="preserve">
    <value>課税対象となるその他の費用は、2を入力します</value>
  </data>
  <data name="enteryear" xml:space="preserve">
    <value>Ja enter year</value>
  </data>
  <data name="EntryTaxPercentage" xml:space="preserve">
    <value>入国税の割合</value>
  </data>
  <data name="Equal" xml:space="preserve">
    <value>フローステップを働かせる</value>
    <comment>Label</comment>
  </data>
  <data name="Error" xml:space="preserve">
    <value>エラー</value>
  </data>
  <data name="ErrorinUploadedPartsPleaseOpenExcel" xml:space="preserve">
    <value>アップロードされた部分でエラーが発生しました。</value>
  </data>
  <data name="ErrorOccuredwhileLocking" xml:space="preserve">
    <value>ロック中にエラーが発生しました</value>
  </data>
  <data name="ErrorSaving" xml:space="preserve">
    <value>保存エラー</value>
  </data>
  <data name="EventName" xml:space="preserve">
    <value>イベント名</value>
  </data>
  <data name="Events" xml:space="preserve">
    <value>イベント</value>
  </data>
  <data name="Excel" xml:space="preserve">
    <value>バージョン</value>
    <comment>Label</comment>
  </data>
  <data name="Export" xml:space="preserve">
    <value>エクスポート</value>
  </data>
  <data name="ExportAction" xml:space="preserve">
    <value>EXPORTアクション</value>
  </data>
  <data name="ExporttoDocument" xml:space="preserve">
    <value>Documentにエクスポート</value>
  </data>
  <data name="FailedtosavenewContactPerson" xml:space="preserve">
    <value>新しい連絡先の担当者の保存に失敗しました</value>
  </data>
  <data name="FailedtosavenewParty" xml:space="preserve">
    <value>新党の保存に失敗しました</value>
  </data>
  <data name="FailedtosavenewSerialNumber" xml:space="preserve">
    <value>新しいシリアル番号を保存できませんでした</value>
  </data>
  <data name="FailedToSaveSerial" xml:space="preserve">
    <value>シリアルの保存に失敗しました</value>
  </data>
  <data name="FAX" xml:space="preserve">
    <value>FAX</value>
  </data>
  <data name="February" xml:space="preserve">
    <value>2月</value>
  </data>
  <data name="Fieldshighlightedaremandatory" xml:space="preserve">
    <value>強調表示されたフィールドは必須です</value>
  </data>
  <data name="FieldsmarkedwithStararemandatory" xml:space="preserve">
    <value>*の欄は必須項目です</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>フィルタリング</value>
  </data>
  <data name="FilterAllQueBranch" xml:space="preserve">
    <value>フローステップを働かせる ?</value>
    <comment>Label</comment>
  </data>
  <data name="FilterCriteria" xml:space="preserve">
    <value>フィルタ条件</value>
  </data>
  <data name="FinancialYear" xml:space="preserve">
    <value>ユーザー名</value>
  </data>
  <data name="financialyearalredyselected" xml:space="preserve">
    <value>接尾辞は、すでに選択</value>
  </data>
  <data name="FinancialyearcannotbeGreaterthannextrowsfinancialyear" xml:space="preserve">
    <value>JA Financial year cannot be greater than next rows financial year</value>
    <comment>Message</comment>
  </data>
  <data name="Financialyearcannotbelessthanpreviousrowsfinancialyear" xml:space="preserve">
    <value>JA Financial year cannot be less than previous rows financial year</value>
    <comment>Message</comment>
  </data>
  <data name="For" xml:space="preserve">
    <value>のために</value>
  </data>
  <data name="forgotpassword" xml:space="preserve">
    <value>パスワードをお忘れですか？</value>
  </data>
  <data name="formula" xml:space="preserve">
    <value>式</value>
  </data>
  <data name="formulasummary" xml:space="preserve">
    <value>式の概要</value>
  </data>
  <data name="FourtyEightToNintyHours" xml:space="preserve">
    <value>48&amp;#12316;90時間</value>
  </data>
  <data name="FreeHours" xml:space="preserve">
    <value>自由時間</value>
  </data>
  <data name="freestock" xml:space="preserve">
    <value>フリー</value>
  </data>
  <data name="Friday" xml:space="preserve">
    <value>Ja Friday</value>
  </data>
  <data name="From" xml:space="preserve">
    <value>はい</value>
    <comment>Label</comment>
  </data>
  <data name="fromdate" xml:space="preserve">
    <value>日付から</value>
  </data>
  <data name="FromDateandTodatecannotbeEmpty" xml:space="preserve">
    <value>日付からと日付に空にすることはできません</value>
  </data>
  <data name="FromDatecannotbegreaterthanToDate" xml:space="preserve">
    <value>日付から現在までのより大きくすることはできません</value>
  </data>
  <data name="fromdatecannotbegreaterthentodate" xml:space="preserve">
    <value>FROMDATEは、ToDateその後も大きくすることはできません</value>
  </data>
  <data name="fromdatecannotbegreatorthantodate" xml:space="preserve">
    <value>日付から今日までよりgreatorすることはできません</value>
  </data>
  <data name="Fromdatecannotbelessthanorequaltopreviousrowtodate" xml:space="preserve">
    <value>JA From date cannot be less than or equal to previous rows to date</value>
    <comment>Message</comment>
  </data>
  <data name="FromDatecannotbelessthanToDate" xml:space="preserve">
    <value>日付から現在までのより小さくすることはできません</value>
  </data>
  <data name="fromdatecannotbelessthencurrentdate" xml:space="preserve">
    <value>日付から少ないし、現在の日付にすることはできません</value>
  </data>
  <data name="fromdatemustbegreaterthanorequaltoissuedate" xml:space="preserve">
    <value>日付から以上発行日に等しくなければなりません</value>
  </data>
  <data name="fromdatemustbelesserthentodate" xml:space="preserve">
    <value>日から日まで次に低いでなければなりません</value>
  </data>
  <data name="FromStep" xml:space="preserve">
    <value>ステップから</value>
  </data>
  <data name="FromTimecannotbegreaterthanToTime" xml:space="preserve">
    <value>Ja From Time cannot be greater than To Time</value>
  </data>
  <data name="FunctionGroup" xml:space="preserve">
    <value>関数群</value>
  </data>
  <data name="FunctionGroupHeader" xml:space="preserve">
    <value>ファンクショングループヘッダー</value>
  </data>
  <data name="FunctionGroupID" xml:space="preserve">
    <value>機能グループID</value>
  </data>
  <data name="FunctionGroupName" xml:space="preserve">
    <value>関数グループ名</value>
  </data>
  <data name="FunctionGroupNative" xml:space="preserve">
    <value>機能グループのネイティブ</value>
  </data>
  <data name="FunctionGroupOperations" xml:space="preserve">
    <value>機能グループの操作</value>
  </data>
  <data name="GenerateReport" xml:space="preserve">
    <value>レポートの生成</value>
  </data>
  <data name="GraphCategorySelection" xml:space="preserve">
    <value>グラフカテゴリ選択</value>
  </data>
  <data name="GraphType" xml:space="preserve">
    <value>グラフの種類</value>
  </data>
  <data name="GreaterThan" xml:space="preserve">
    <value>フローステップを働かせる</value>
    <comment>Label</comment>
  </data>
  <data name="GroupQue" xml:space="preserve">
    <value>作業フロー名</value>
    <comment>Label</comment>
  </data>
  <data name="GroupQueue" xml:space="preserve">
    <value>グループのキュー</value>
  </data>
  <data name="Header" xml:space="preserve">
    <value>ヘッダ</value>
  </data>
  <data name="Heading" xml:space="preserve">
    <value>見出し</value>
  </data>
  <data name="high" xml:space="preserve">
    <value>ハイ</value>
  </data>
  <data name="HighlightedFieldsareMandatory" xml:space="preserve">
    <value>強調表示されたフィールドは必須です</value>
  </data>
  <data name="HMR" xml:space="preserve">
    <value>HMR</value>
  </data>
  <data name="Hold" xml:space="preserve">
    <value>ホールド</value>
  </data>
  <data name="Holidays" xml:space="preserve">
    <value>Ja Holidays</value>
  </data>
  <data name="ID" xml:space="preserve">
    <value>番号</value>
  </data>
  <data name="Import" xml:space="preserve">
    <value>インポート</value>
  </data>
  <data name="ImportAction" xml:space="preserve">
    <value>インポートアクション</value>
  </data>
  <data name="ImportedSuccessfully" xml:space="preserve">
    <value>正常にインポート</value>
  </data>
  <data name="ImportIntoDatabase" xml:space="preserve">
    <value>データベースにインポート</value>
  </data>
  <data name="ImportParts" xml:space="preserve">
    <value>輸入パーツ</value>
  </data>
  <data name="IndexOutOfRangeException" xml:space="preserve">
    <value>アプリケーションエラーが発生しました</value>
  </data>
  <data name="InProgress" xml:space="preserve">
    <value>進行中</value>
  </data>
  <data name="InProgressCount" xml:space="preserve">
    <value>プログレスカウントに</value>
  </data>
  <data name="InsertedSuccessfully" xml:space="preserve">
    <value>正常に挿入</value>
  </data>
  <data name="Internal" xml:space="preserve">
    <value>内部</value>
  </data>
  <data name="Invalid" xml:space="preserve">
    <value>Ja Invalid</value>
  </data>
  <data name="InvalidBranchSelection" xml:space="preserve">
    <value>JA Prefixsuffix is created as company specific. Cannot change to Branch. To change Delete the Existing record and create Branch Specific Prefix Suffix</value>
  </data>
  <data name="InvalidCastException" xml:space="preserve">
    <value>アプリケーションエラーが発生しました</value>
  </data>
  <data name="InvalidCompanySelection" xml:space="preserve">
    <value>JA Prefixsuffix is created as Branch specific. Cannot change to Company. To change Delete the Existing record and create Company Specific Prefix Suffix</value>
  </data>
  <data name="InvalidDate" xml:space="preserve">
    <value>無効な日付</value>
  </data>
  <data name="InvalidEmail" xml:space="preserve">
    <value>無効なメール</value>
  </data>
  <data name="InvalidFile" xml:space="preserve">
    <value>無効なファイル</value>
  </data>
  <data name="InvalidMobile" xml:space="preserve">
    <value>郵便番号</value>
    <comment>Message</comment>
  </data>
  <data name="InvalidModel" xml:space="preserve">
    <value>無効なモデル</value>
  </data>
  <data name="invalidmodelormodelisinactive" xml:space="preserve">
    <value>無効なモデルまたはモデルは非アクティブです</value>
  </data>
  <data name="InvalidName" xml:space="preserve">
    <value>無効なパーティ名</value>
  </data>
  <data name="InvalidOperationException" xml:space="preserve">
    <value>データベースエラーが発生しました</value>
  </data>
  <data name="invalidpartnumberorpartnumberisinactive" xml:space="preserve">
    <value>無効な部品番号または部品番号が非アクティブである</value>
  </data>
  <data name="InvalidPhone" xml:space="preserve">
    <value>Invalid Phone</value>
  </data>
  <data name="InvalidPhoneNo" xml:space="preserve">
    <value>なし電話無効</value>
  </data>
  <data name="InvalidProduct" xml:space="preserve">
    <value>無効な製品</value>
  </data>
  <data name="InvalidProductUniqueNumber" xml:space="preserve">
    <value>無効なプロダクト一意識別子</value>
  </data>
  <data name="InvalidRegisteredMobileNumber" xml:space="preserve">
    <value>無効な登録された携帯電話番号</value>
  </data>
  <data name="invalidselection" xml:space="preserve">
    <value>無効な選択</value>
  </data>
  <data name="InvalidServiceRequestNumber" xml:space="preserve">
    <value>無効なサービス要求番号</value>
  </data>
  <data name="InValidUniqueIdentificationNumber" xml:space="preserve">
    <value>無効な一意の識別番号</value>
  </data>
  <data name="IsActive" xml:space="preserve">
    <value>アクティブですか？</value>
  </data>
  <data name="IsAdmin" xml:space="preserve">
    <value>Adminです</value>
  </data>
  <data name="isbaseamountincluded" xml:space="preserve">
    <value>基準額は含まれていますか？</value>
  </data>
  <data name="IsCompanySpecific" xml:space="preserve">
    <value>当社は固有のものです</value>
  </data>
  <data name="iscomponent" xml:space="preserve">
    <value>構成要素である</value>
  </data>
  <data name="IsCustomer" xml:space="preserve">
    <value>IsCustomer</value>
  </data>
  <data name="IsDefaultContact" xml:space="preserve">
    <value>デフォルトの連絡先ですか？</value>
  </data>
  <data name="IsExternal" xml:space="preserve">
    <value>外部にある</value>
  </data>
  <data name="isHazardous" xml:space="preserve">
    <value>危険です</value>
  </data>
  <data name="IsHeadOffice" xml:space="preserve">
    <value>本社です</value>
  </data>
  <data name="isinactive" xml:space="preserve">
    <value>Ja is in active</value>
  </data>
  <data name="IsOperationCompleted" xml:space="preserve">
    <value>操作が完了する</value>
  </data>
  <data name="issueddate" xml:space="preserve">
    <value>発行日</value>
  </data>
  <data name="issueddatesholudbelessthanorequaltocurrentdate" xml:space="preserve">
    <value>発行日付sholud未満または現在の日付に等し&amp;#8203;&amp;#8203;いこと</value>
  </data>
  <data name="IsUnderBreakDown" xml:space="preserve">
    <value>ブレークダウンの下にある？</value>
  </data>
  <data name="isunderwarranty" xml:space="preserve">
    <value>保証期間内である？</value>
  </data>
  <data name="isversionallowed" xml:space="preserve">
    <value>ペットバージョンは何ですか？</value>
  </data>
  <data name="IsVersionEnabled" xml:space="preserve">
    <value>バージョンが有効になっていますか？</value>
  </data>
  <data name="January" xml:space="preserve">
    <value>1月</value>
  </data>
  <data name="Jobamendedwillcreatenewversionwanttoproceed" xml:space="preserve">
    <value>ジョブが改正され、続行するか、新しいバージョンを作成するのでしょうか？</value>
  </data>
  <data name="JobCard" xml:space="preserve">
    <value>ジョブ&amp;#183;カード</value>
  </data>
  <data name="JobCardAbandonReason" xml:space="preserve">
    <value>ジョブカードは理由を断念</value>
  </data>
  <data name="JobcardArchived" xml:space="preserve">
    <value>アーカイブされたジョブ&amp;#183;カード</value>
  </data>
  <data name="JobCardClosureDate" xml:space="preserve">
    <value>ジョブカード閉鎖日</value>
  </data>
  <data name="JobCardCushionHours" xml:space="preserve">
    <value>ジョブカードクッション時間</value>
  </data>
  <data name="JobCardDate" xml:space="preserve">
    <value>ジョブカード日</value>
  </data>
  <data name="JobCardDelayReason" xml:space="preserve">
    <value>ジョブカード遅延理由</value>
  </data>
  <data name="JobCardFieldSearch" xml:space="preserve">
    <value>ジョブカードフィールド検索</value>
  </data>
  <data name="JobCardisalreadycreatedforthisServiceRequestNumber" xml:space="preserve">
    <value>ジョブ&amp;#183;カードは、すでにこのサービスリクエスト番号のために作成され</value>
  </data>
  <data name="JobCardNumber" xml:space="preserve">
    <value>ジョブカード番号</value>
  </data>
  <data name="JobcardNumbernotfound" xml:space="preserve">
    <value>ジョブ&amp;#183;カード番号が見つかりません</value>
  </data>
  <data name="JobCardNumberSearch" xml:space="preserve">
    <value>ジョブカード番号検索</value>
  </data>
  <data name="JobCardPendingCount" xml:space="preserve">
    <value>ジョブカードはカウントを保留</value>
  </data>
  <data name="JobCardStatus" xml:space="preserve">
    <value>ジョブ&amp;#183;カードのステータス</value>
  </data>
  <data name="JobCardSummary" xml:space="preserve">
    <value>ジョブ&amp;#183;カードの概要</value>
  </data>
  <data name="JobCardVersion" xml:space="preserve">
    <value>バージョン</value>
  </data>
  <data name="JobCardWIPCount" xml:space="preserve">
    <value>仕事カードWIPカウント</value>
  </data>
  <data name="JobDescription" xml:space="preserve">
    <value>職務記述書</value>
  </data>
  <data name="JobEndDate" xml:space="preserve">
    <value>ジョブの終了日</value>
  </data>
  <data name="JobPriority" xml:space="preserve">
    <value>ジョブ優先順位</value>
  </data>
  <data name="JobSiteAddress" xml:space="preserve">
    <value>求人サイトのアドレス</value>
  </data>
  <data name="JobStartDate" xml:space="preserve">
    <value>仕事開始日</value>
  </data>
  <data name="JoinedTables" xml:space="preserve">
    <value>結合テーブル</value>
  </data>
  <data name="JoinWith" xml:space="preserve">
    <value>と結合</value>
  </data>
  <data name="July" xml:space="preserve">
    <value>7月</value>
  </data>
  <data name="June" xml:space="preserve">
    <value>6月</value>
  </data>
  <data name="Landline" xml:space="preserve">
    <value>固定電話</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>言語</value>
  </data>
  <data name="LanguageName" xml:space="preserve">
    <value>言語</value>
  </data>
  <data name="LessThan" xml:space="preserve">
    <value>フローステップを働かせる</value>
    <comment>Label</comment>
  </data>
  <data name="Like" xml:space="preserve">
    <value>フローステップを働かせる</value>
    <comment>Label</comment>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>郵便番号</value>
    <comment>Label</comment>
  </data>
  <data name="Local" xml:space="preserve">
    <value>ローカル</value>
  </data>
  <data name="Locale" xml:space="preserve">
    <value>ロケール</value>
  </data>
  <data name="LocaleDetails" xml:space="preserve">
    <value>ロケールの詳細</value>
  </data>
  <data name="LocaledetailsarenotavaliableforBrand" xml:space="preserve">
    <value>ロケールの詳細は、ブランドのために利用可能なではありません</value>
  </data>
  <data name="LocaledetailsarenotavaliableforProductModel" xml:space="preserve">
    <value>ロケールの詳細は、製品モデルのために利用可能なではありません</value>
  </data>
  <data name="LocaledetailsarenotavaliableforProductType" xml:space="preserve">
    <value>ロケールの詳細は、製品の種類のために利用可能なではありません</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>場所</value>
  </data>
  <data name="Lock" xml:space="preserve">
    <value>作業の流れ</value>
    <comment>Label</comment>
  </data>
  <data name="locked" xml:space="preserve">
    <value>ロック</value>
  </data>
  <data name="lockedby" xml:space="preserve">
    <value>によってロック</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>ログイン</value>
  </data>
  <data name="LoginID" xml:space="preserve">
    <value>ログインID</value>
  </data>
  <data name="LogoName" xml:space="preserve">
    <value>ロゴ名</value>
  </data>
  <data name="low" xml:space="preserve">
    <value>ロー</value>
  </data>
  <data name="Manager" xml:space="preserve">
    <value>マネージャー</value>
  </data>
  <data name="mandatoryservices" xml:space="preserve">
    <value>必須サービス</value>
  </data>
  <data name="Manufacturer" xml:space="preserve">
    <value>メーカー</value>
  </data>
  <data name="MapColumns" xml:space="preserve">
    <value>マップコラム</value>
  </data>
  <data name="MappedColumns" xml:space="preserve">
    <value>マップされた列</value>
  </data>
  <data name="March" xml:space="preserve">
    <value>3月</value>
  </data>
  <data name="MasterExists" xml:space="preserve">
    <value>マスターは既に存在しています</value>
  </data>
  <data name="MasterID" xml:space="preserve">
    <value>マスタID</value>
  </data>
  <data name="MasterName" xml:space="preserve">
    <value>マスターの名前</value>
  </data>
  <data name="May" xml:space="preserve">
    <value>5月</value>
  </data>
  <data name="medium" xml:space="preserve">
    <value>培地</value>
  </data>
  <data name="MenuDetail" xml:space="preserve">
    <value>メニューの詳細</value>
  </data>
  <data name="MenuDetails" xml:space="preserve">
    <value>メニューの詳細</value>
  </data>
  <data name="MenuName" xml:space="preserve">
    <value>メニュー名</value>
  </data>
  <data name="MenuNamecannotbeblank" xml:space="preserve">
    <value>メニュー名は空白にすることはできません</value>
  </data>
  <data name="MenuPath" xml:space="preserve">
    <value>メニューパス</value>
  </data>
  <data name="Mobile" xml:space="preserve">
    <value>携帯電話</value>
  </data>
  <data name="MobileNumber" xml:space="preserve">
    <value>携帯電話番号</value>
  </data>
  <data name="model" xml:space="preserve">
    <value>モデル</value>
  </data>
  <data name="modelenglish" xml:space="preserve">
    <value>モデル英語</value>
  </data>
  <data name="ModelFieldSearch" xml:space="preserve">
    <value>Ja Model Field Search</value>
  </data>
  <data name="modelisinactive" xml:space="preserve">
    <value>モデルは非アクティブです</value>
  </data>
  <data name="modellocale" xml:space="preserve">
    <value>モデルロケール</value>
  </data>
  <data name="modelmaster" xml:space="preserve">
    <value>モデルマスター</value>
  </data>
  <data name="modelname" xml:space="preserve">
    <value>モデル名</value>
  </data>
  <data name="modelnamealreadyexists" xml:space="preserve">
    <value>モデル名は既に存在しています</value>
  </data>
  <data name="modelnotfound" xml:space="preserve">
    <value>モデルが見つかりません</value>
  </data>
  <data name="ModelSearch" xml:space="preserve">
    <value>モデル検索</value>
  </data>
  <data name="Module" xml:space="preserve">
    <value>モジュール</value>
  </data>
  <data name="ModuleName" xml:space="preserve">
    <value>モジュール名</value>
  </data>
  <data name="ModuleNameCannotbeblank" xml:space="preserve">
    <value>モジュール名は空白にすることはできません</value>
  </data>
  <data name="Monday" xml:space="preserve">
    <value>Ja Monday</value>
  </data>
  <data name="month" xml:space="preserve">
    <value>月</value>
  </data>
  <data name="MyQue" xml:space="preserve">
    <value>保証日</value>
    <comment>Label</comment>
  </data>
  <data name="MyQueue" xml:space="preserve">
    <value>私のキュー</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>名前</value>
  </data>
  <data name="NintyHour" xml:space="preserve">
    <value>&amp;gt; 90時間対応</value>
  </data>
  <data name="no" xml:space="preserve">
    <value>ノー</value>
  </data>
  <data name="NoChangesMade" xml:space="preserve">
    <value>作ら変更はありません</value>
  </data>
  <data name="NochangesmadetoSave" xml:space="preserve">
    <value>保存しよ変更はありません</value>
  </data>
  <data name="NomatchingrecordfoundDoyouwanttoadd" xml:space="preserve">
    <value>当事者が非アクティブであるか、または一致するレコードが見つからないと、このパーティを追加しますか</value>
  </data>
  <data name="NonTaxable" xml:space="preserve">
    <value>課税対象となる非</value>
  </data>
  <data name="NonTaxableothercharges1" xml:space="preserve">
    <value>非課税その他の費用1</value>
  </data>
  <data name="NonTaxableothercharges1Amount" xml:space="preserve">
    <value>非課税対象額その他の費用1</value>
  </data>
  <data name="NonTaxableothercharges2" xml:space="preserve">
    <value>非課税その他の費用2</value>
  </data>
  <data name="NonTaxableothercharges2Amount" xml:space="preserve">
    <value>非課税対象額その他の費用2</value>
  </data>
  <data name="NoOfSRCompleted" xml:space="preserve">
    <value>完成したサービス要求の数</value>
  </data>
  <data name="NoOfSRRecieved" xml:space="preserve">
    <value>サービスリクエストの数が受け取っ</value>
  </data>
  <data name="Noproductisassociatedwithselectedcustomer" xml:space="preserve">
    <value>いいえ製品は、選択した顧客に関連付けられていません</value>
  </data>
  <data name="Norecordstoview" xml:space="preserve">
    <value>フローステップを働かせる</value>
    <comment>Label</comment>
  </data>
  <data name="NotEqual" xml:space="preserve">
    <value>フローステップを働かせる</value>
    <comment>Label</comment>
  </data>
  <data name="NotFound" xml:space="preserve">
    <value>Ja Not Found</value>
  </data>
  <data name="November" xml:space="preserve">
    <value>11月</value>
  </data>
  <data name="NullReferenceException" xml:space="preserve">
    <value>アプリケーションエラーが発生しました</value>
  </data>
  <data name="number" xml:space="preserve">
    <value>数</value>
  </data>
  <data name="ObjectDescription" xml:space="preserve">
    <value>オブジェクトの説明</value>
  </data>
  <data name="ObjectDescriptioncannotbeblank" xml:space="preserve">
    <value>オブジェクトの説明は空白にすることはできません</value>
  </data>
  <data name="ObjectMaster" xml:space="preserve">
    <value>オブジェクトマスター</value>
  </data>
  <data name="ObjectName" xml:space="preserve">
    <value>オブジェクト名</value>
  </data>
  <data name="ObjectNamecannotbeblank" xml:space="preserve">
    <value>オブジェクト名は空白にすることはできません</value>
  </data>
  <data name="ObjectssavedSuccessfully" xml:space="preserve">
    <value>オブジェクトが正常に保存され</value>
  </data>
  <data name="October" xml:space="preserve">
    <value>10月</value>
  </data>
  <data name="of" xml:space="preserve">
    <value>年</value>
    <comment>Label</comment>
  </data>
  <data name="OnHoldCount" xml:space="preserve">
    <value>OnHoldカウント</value>
  </data>
  <data name="onlyactivecustomerdeatilscanbeedited" xml:space="preserve">
    <value>唯一のアクティブな顧客deatilsは編集することができます</value>
  </data>
  <data name="onlyactivewarrantycanbeedited" xml:space="preserve">
    <value>唯一のアクティブな保証を編集することができます</value>
  </data>
  <data name="open" xml:space="preserve">
    <value>開く</value>
  </data>
  <data name="OpenReport" xml:space="preserve">
    <value>レポートを開く</value>
  </data>
  <data name="operation" xml:space="preserve">
    <value>操作</value>
  </data>
  <data name="OperationandEmployeeisalreadyassociated" xml:space="preserve">
    <value>操作および従業員がすでに関連付けられて</value>
  </data>
  <data name="OperationCode" xml:space="preserve">
    <value>オペレーションコード</value>
  </data>
  <data name="Operationcodealreadyexists" xml:space="preserve">
    <value>オペレーションコードは既に存在しています</value>
  </data>
  <data name="OperationCodeAlreadySelected" xml:space="preserve">
    <value>操作コードareadyは、選択した</value>
  </data>
  <data name="OperationCodeNotFound" xml:space="preserve">
    <value>オペレーションコードが見つかりません</value>
  </data>
  <data name="OperationDescription" xml:space="preserve">
    <value>操作説明</value>
  </data>
  <data name="OperationDetails" xml:space="preserve">
    <value>操作の詳細</value>
  </data>
  <data name="OperationDeviationReport" xml:space="preserve">
    <value>操作偏差レポート</value>
  </data>
  <data name="OperationEmployeeDetails" xml:space="preserve">
    <value>操作員詳細</value>
  </data>
  <data name="OperationEndDate" xml:space="preserve">
    <value>運転終了日</value>
  </data>
  <data name="OperationEndDateCannotbelessthanoperationStartDate" xml:space="preserve">
    <value>操作の終了日は運転開始日よりも小さくすることはできません</value>
  </data>
  <data name="operationenglish" xml:space="preserve">
    <value>操作英語</value>
  </data>
  <data name="OperationFieldSearch" xml:space="preserve">
    <value>オペレーションフィールド検索</value>
  </data>
  <data name="operationheader" xml:space="preserve">
    <value>操作ヘッダー</value>
  </data>
  <data name="OperationHours" xml:space="preserve">
    <value>運転時間</value>
  </data>
  <data name="Operationisalreadyassociatedwithanotheremployee" xml:space="preserve">
    <value>操作は、すでに別の従業員に関連付けられている</value>
  </data>
  <data name="operationlocale" xml:space="preserve">
    <value>操作のロケール</value>
  </data>
  <data name="operationmaster" xml:space="preserve">
    <value>操作マスター</value>
  </data>
  <data name="OperationStartDate" xml:space="preserve">
    <value>運転開始日</value>
  </data>
  <data name="Operator" xml:space="preserve">
    <value>オペレータ</value>
  </data>
  <data name="OR" xml:space="preserve">
    <value>フローステップを働かせる</value>
    <comment>Label</comment>
  </data>
  <data name="OtherDetail" xml:space="preserve">
    <value>その他の詳細</value>
  </data>
  <data name="OtherDetails" xml:space="preserve">
    <value>その他の詳細</value>
  </data>
  <data name="Others" xml:space="preserve">
    <value>保証日</value>
    <comment>Label</comment>
  </data>
  <data name="OutOfMemoryException" xml:space="preserve">
    <value>アプリケーションエラーが発生しました</value>
  </data>
  <data name="Page" xml:space="preserve">
    <value>はい</value>
  </data>
  <data name="ParentCompany" xml:space="preserve">
    <value>親会社</value>
  </data>
  <data name="parentcompanyoperationcannotbedeleted" xml:space="preserve">
    <value>親会社の操作を削除することはできません</value>
  </data>
  <data name="parentcompanyoperationcannotbeedited" xml:space="preserve">
    <value>親会社の操作を編集することはできません</value>
  </data>
  <data name="ParentMenu" xml:space="preserve">
    <value>親メニュー</value>
  </data>
  <data name="partcategory" xml:space="preserve">
    <value>パーツカテゴリ</value>
  </data>
  <data name="partdescription" xml:space="preserve">
    <value>部品記述</value>
  </data>
  <data name="partfunctiongroup" xml:space="preserve">
    <value>パーツの機能グループ</value>
  </data>
  <data name="Partner" xml:space="preserve">
    <value>パートナー</value>
  </data>
  <data name="PartnerName" xml:space="preserve">
    <value>パートナー</value>
  </data>
  <data name="partnumber" xml:space="preserve">
    <value>部品番号</value>
  </data>
  <data name="partnumberalreadyexists" xml:space="preserve">
    <value>部品番号がすでに存在している</value>
  </data>
  <data name="PartNumbernotfound" xml:space="preserve">
    <value>部品番号が見つかりません</value>
  </data>
  <data name="partnumbersearch" xml:space="preserve">
    <value>品番検索</value>
  </data>
  <data name="partprice" xml:space="preserve">
    <value>パーツ価格</value>
  </data>
  <data name="partpricepdetails" xml:space="preserve">
    <value>パーツの価格詳細</value>
  </data>
  <data name="partproducttypedetails" xml:space="preserve">
    <value>一部商品の種類の詳細</value>
  </data>
  <data name="Parts" xml:space="preserve">
    <value>Ja Parts</value>
  </data>
  <data name="PartsDetail" xml:space="preserve">
    <value>部品詳細</value>
  </data>
  <data name="PartsDetails" xml:space="preserve">
    <value>パーツの詳細</value>
  </data>
  <data name="partsenglish" xml:space="preserve">
    <value>パーツの英語</value>
  </data>
  <data name="PartsFieldSearch" xml:space="preserve">
    <value>パーツフィールド検索</value>
  </data>
  <data name="partsfreestockdetails" xml:space="preserve">
    <value>パーツフリーフォト詳細</value>
  </data>
  <data name="partslocale" xml:space="preserve">
    <value>部品ロケール</value>
  </data>
  <data name="partsmaster" xml:space="preserve">
    <value>パーツマスター</value>
  </data>
  <data name="partsmasterlocale" xml:space="preserve">
    <value>パーツマスターロケール</value>
  </data>
  <data name="partspmasterheader" xml:space="preserve">
    <value>パーツマスターヘッダー</value>
  </data>
  <data name="partspricedetails" xml:space="preserve">
    <value>部品価格詳細</value>
  </data>
  <data name="partsproducttypelocale" xml:space="preserve">
    <value>パーツ製品タイプのロケール</value>
  </data>
  <data name="PartsTemplate" xml:space="preserve">
    <value>パーツテンプレート</value>
  </data>
  <data name="PartsTotalAmount" xml:space="preserve">
    <value>部品合計金額</value>
  </data>
  <data name="Party" xml:space="preserve">
    <value>パーティー</value>
  </data>
  <data name="alreadyexistsforthelocation" xml:space="preserve">
    <value>Ja already exists for the location</value>
  </data>
  <data name="PartyDetails" xml:space="preserve">
    <value>パーティーの詳細</value>
  </data>
  <data name="PartyFielSearch" xml:space="preserve">
    <value>Ja  Party Field Search</value>
  </data>
  <data name="PartyLocation" xml:space="preserve">
    <value>パーティーの場所</value>
  </data>
  <data name="PartyMobile" xml:space="preserve">
    <value>パーティーモバイル</value>
  </data>
  <data name="PartyName" xml:space="preserve">
    <value>パーティ名</value>
  </data>
  <data name="PartyNotFound" xml:space="preserve">
    <value>パーティーが見つかりません</value>
  </data>
  <data name="PartyPhone" xml:space="preserve">
    <value>パーティ電話</value>
  </data>
  <data name="PartySearch" xml:space="preserve">
    <value>パーティー検索</value>
  </data>
  <data name="PartyType" xml:space="preserve">
    <value>パーティーの種類</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>パスワード</value>
  </data>
  <data name="Passwordandconfirmpasswordshouldmatch" xml:space="preserve">
    <value>パスワードを確認すると、指定されたパスワードと一致していません</value>
  </data>
  <data name="PaymentTerms" xml:space="preserve">
    <value>支払条件</value>
  </data>
  <data name="PDF" xml:space="preserve">
    <value>バージョン</value>
    <comment>Label</comment>
  </data>
  <data name="Pending" xml:space="preserve">
    <value>ペンディング</value>
  </data>
  <data name="PercentageDeviation" xml:space="preserve">
    <value>パーセント偏差</value>
  </data>
  <data name="Personal" xml:space="preserve">
    <value>郵便番号</value>
    <comment>Label</comment>
  </data>
  <data name="Phone" xml:space="preserve">
    <value>電話</value>
  </data>
  <data name="PhoneNo" xml:space="preserve">
    <value>電話</value>
  </data>
  <data name="PieChart" xml:space="preserve">
    <value>円グラフ</value>
  </data>
  <data name="PlannedCompletionDate" xml:space="preserve">
    <value>計画完了日</value>
  </data>
  <data name="PlannedCompletionDatecannotbelessthanStartDate" xml:space="preserve">
    <value>計画完了日が開始日より小さくすることはできません</value>
  </data>
  <data name="PlannedStartDate" xml:space="preserve">
    <value>計画開始日</value>
  </data>
  <data name="pleasecompleteenteringdetails" xml:space="preserve">
    <value>詳細を入力し完了してください</value>
  </data>
  <data name="PleaseenterIntegerValue" xml:space="preserve">
    <value>フローステップを働かせる</value>
    <comment>Label</comment>
  </data>
  <data name="PleaseEnterLoginID" xml:space="preserve">
    <value>ログインIDを入力してください</value>
  </data>
  <data name="PleaseenterPartyName" xml:space="preserve">
    <value>パーティ名を入力してください</value>
  </data>
  <data name="PleaseEnterPassword" xml:space="preserve">
    <value>パスワードを入力してください</value>
  </data>
  <data name="PleaseenterReportHeader" xml:space="preserve">
    <value>レポートヘッダーを入力してください</value>
  </data>
  <data name="PleaseenterReportName" xml:space="preserve">
    <value>レポート名を入力してください</value>
  </data>
  <data name="Pleaseenteruserdetails" xml:space="preserve">
    <value>ユーザーの詳細を入力してください</value>
  </data>
  <data name="pleaseentervalidmodel" xml:space="preserve">
    <value>有効なモデルを入力してください</value>
  </data>
  <data name="Pleaseentervalue" xml:space="preserve">
    <value>値を入力してください</value>
  </data>
  <data name="Pleaseentervalue1" xml:space="preserve">
    <value>value1を入力してください</value>
  </data>
  <data name="PleaseprovideMenuName" xml:space="preserve">
    <value>メニュー名を入力してください</value>
  </data>
  <data name="PleaseprovideModuleName" xml:space="preserve">
    <value>モジュール名を指定してください</value>
  </data>
  <data name="Pleaseprovidepassword" xml:space="preserve">
    <value>パスワードを入力してください</value>
  </data>
  <data name="pleasesavetheOperationdata" xml:space="preserve">
    <value>操作の詳細を保存してください</value>
  </data>
  <data name="pleasesavethepartsdata" xml:space="preserve">
    <value>部品の詳細を保存してください</value>
  </data>
  <data name="pleasesavetheServicedata" xml:space="preserve">
    <value>サービスの詳細を保存してください</value>
  </data>
  <data name="pleasesavethesundrydata" xml:space="preserve">
    <value>諸口詳細を保存してください</value>
  </data>
  <data name="PleaseselectaColumn" xml:space="preserve">
    <value>フローステップを働かせる</value>
    <comment>Label</comment>
  </data>
  <data name="PleaseselectaCondition" xml:space="preserve">
    <value>フローステップを働かせる</value>
    <comment>Label</comment>
  </data>
  <data name="Pleaseselectafiletoupload" xml:space="preserve">
    <value>アップロードするファイルを選択してください</value>
  </data>
  <data name="PleaseselectaOperator" xml:space="preserve">
    <value>フローステップを働かせる</value>
    <comment>Label</comment>
  </data>
  <data name="PleaseSelectBranch" xml:space="preserve">
    <value>支店を選択してください</value>
  </data>
  <data name="PleaseselectBrand" xml:space="preserve">
    <value>ブランドを選択してください</value>
  </data>
  <data name="Pleaseselectcompany" xml:space="preserve">
    <value>会社を選択してください</value>
  </data>
  <data name="Pleaseselectcondition" xml:space="preserve">
    <value>条件を選択してください</value>
  </data>
  <data name="PleaseselectFile" xml:space="preserve">
    <value>ファイルを選択してください</value>
  </data>
  <data name="PleaseSelectGraphCategory" xml:space="preserve">
    <value>グラフのカテゴリを選択してください</value>
  </data>
  <data name="PleaseSelectGraphType" xml:space="preserve">
    <value>Ja Please Select Graph Type</value>
  </data>
  <data name="pleaseselectModel" xml:space="preserve">
    <value>モデルを選択してください</value>
  </data>
  <data name="PleaseselectmodelandSerialNumber" xml:space="preserve">
    <value>セレクトモデルとシリアル番号を喜ばす</value>
  </data>
  <data name="Pleaseselectoperator" xml:space="preserve">
    <value>演算子を選択してください</value>
  </data>
  <data name="Pleaseselectrecordstodelete" xml:space="preserve">
    <value>削除するレコードを選択してください</value>
  </data>
  <data name="PleaseselecttheColumnName" xml:space="preserve">
    <value>列名を選択してください</value>
  </data>
  <data name="Pleaseselectthecolumnstomap" xml:space="preserve">
    <value>マップする列を選択してください</value>
  </data>
  <data name="Pleaseselecttheoperator" xml:space="preserve">
    <value>演算子を選択してください</value>
  </data>
  <data name="PleaseselecttheTableName" xml:space="preserve">
    <value>テーブル名を選択してください</value>
  </data>
  <data name="PleaseselectUserstosave" xml:space="preserve">
    <value>保存するようにユーザーを選択してください</value>
  </data>
  <data name="PreffixSuffixDoesntExists" xml:space="preserve">
    <value>Preffixサフィックスが存在しません</value>
  </data>
  <data name="prefix" xml:space="preserve">
    <value>接頭辞</value>
  </data>
  <data name="prefixsuffix" xml:space="preserve">
    <value>接頭語接尾辞</value>
  </data>
  <data name="PreviousdateCannotbeempty" xml:space="preserve">
    <value>前の日は、空にすることはできません</value>
  </data>
  <data name="PricecannotbeBlankorZero" xml:space="preserve">
    <value>価格は空白またはゼロにすることはできません</value>
  </data>
  <data name="PrimarySegment" xml:space="preserve">
    <value>プライマリセグメント</value>
  </data>
  <data name="Print" xml:space="preserve">
    <value>印刷</value>
  </data>
  <data name="PrintAction" xml:space="preserve">
    <value>アクションを印刷</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>優先順位</value>
  </data>
  <data name="PriorityShouldBeBetweenzeroandtwofiftyfive" xml:space="preserve">
    <value>優先度は、0から255の間&amp;#8203;&amp;#8203;でなければなりません</value>
  </data>
  <data name="product" xml:space="preserve">
    <value>製品</value>
  </data>
  <data name="ProductAssociation" xml:space="preserve">
    <value>製品協会</value>
  </data>
  <data name="productdetail" xml:space="preserve">
    <value>製品の詳細</value>
  </data>
  <data name="productdetails" xml:space="preserve">
    <value>商品の詳細</value>
  </data>
  <data name="productid" xml:space="preserve">
    <value>プロダクトID</value>
  </data>
  <data name="Productisnotasscociatedwithanycustomer" xml:space="preserve">
    <value>製品は現在どの顧客とasscociatedされていません</value>
  </data>
  <data name="ProductLocation" xml:space="preserve">
    <value>製品の場所</value>
  </data>
  <data name="ProductReading" xml:space="preserve">
    <value>製品のリーディング</value>
  </data>
  <data name="ProductServiceHistory" xml:space="preserve">
    <value>製品サービスの歴史</value>
  </data>
  <data name="Producttype" xml:space="preserve">
    <value>製品タイプ</value>
  </data>
  <data name="producttypeenglish" xml:space="preserve">
    <value>製品タイプ英語</value>
  </data>
  <data name="producttypelocale" xml:space="preserve">
    <value>製品タイプのロケール</value>
  </data>
  <data name="producttypemaster" xml:space="preserve">
    <value>製品タイプのマスター</value>
  </data>
  <data name="producttypename" xml:space="preserve">
    <value>製品形名</value>
  </data>
  <data name="producttypenamealreadyexists" xml:space="preserve">
    <value>製品型名はすでに存在している</value>
  </data>
  <data name="ProductUniqueNo" xml:space="preserve">
    <value>ユニークな識別子</value>
  </data>
  <data name="Prospect" xml:space="preserve">
    <value>プロスペクト</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>量</value>
  </data>
  <data name="QuantityCannotbeZero" xml:space="preserve">
    <value>Quantity Cannot be Zero</value>
    <comment>Message</comment>
  </data>
  <data name="QuestInformaticsPrivateLimited" xml:space="preserve">
    <value>Quest Informatics Private Limited</value>
  </data>
  <data name="Quotation" xml:space="preserve">
    <value>引用</value>
  </data>
  <data name="QuotationAmount" xml:space="preserve">
    <value>引用額</value>
  </data>
  <data name="QuotationDetail" xml:space="preserve">
    <value>引用の詳細</value>
  </data>
  <data name="QuotationNumber" xml:space="preserve">
    <value>引用数</value>
  </data>
  <data name="QuotationPriority" xml:space="preserve">
    <value>引用優先</value>
  </data>
  <data name="RangecannotbeBlank" xml:space="preserve">
    <value>範囲は空白にすることはできません</value>
  </data>
  <data name="Rate" xml:space="preserve">
    <value>率</value>
  </data>
  <data name="RateCannotbeZero" xml:space="preserve">
    <value>Rate Cannot be Zero</value>
    <comment>Message</comment>
  </data>
  <data name="Rating" xml:space="preserve">
    <value>評価</value>
  </data>
  <data name="Ratingshouldbebetween1and10" xml:space="preserve">
    <value>評価は1から10の間でなければなりません</value>
  </data>
  <data name="RatingshouldBetween1and10" xml:space="preserve">
    <value>Rating should between 1 and 10</value>
  </data>
  <data name="Read" xml:space="preserve">
    <value>読む</value>
  </data>
  <data name="ReadAction" xml:space="preserve">
    <value>アクションを読む</value>
  </data>
  <data name="reading" xml:space="preserve">
    <value>読書</value>
  </data>
  <data name="readinglimit" xml:space="preserve">
    <value>リミットを読んで</value>
  </data>
  <data name="readinglog" xml:space="preserve">
    <value>ログを読んで</value>
  </data>
  <data name="realizationreport" xml:space="preserve">
    <value>実現レポート</value>
  </data>
  <data name="reasonforinactive" xml:space="preserve">
    <value>アクティブでない理由</value>
  </data>
  <data name="ReceivedCount" xml:space="preserve">
    <value>Ja Received Count</value>
  </data>
  <data name="RecentActivityLinks" xml:space="preserve">
    <value>最近の活動リンク</value>
  </data>
  <data name="RecievedTime" xml:space="preserve">
    <value>受け取っ時間</value>
  </data>
  <data name="RecieviedCount" xml:space="preserve">
    <value>Recieviedカウント</value>
  </data>
  <data name="Recordsavedsuccessfully" xml:space="preserve">
    <value>レコードが正常に保存され</value>
  </data>
  <data name="ReferenceDetail" xml:space="preserve">
    <value>参照の詳細</value>
  </data>
  <data name="ReferenceMasters" xml:space="preserve">
    <value>リファレンス&amp;#183;マスターズ</value>
  </data>
  <data name="ReferenceTables" xml:space="preserve">
    <value>参照テーブル</value>
  </data>
  <data name="refresh" xml:space="preserve">
    <value>リフレッシュ</value>
  </data>
  <data name="Region" xml:space="preserve">
    <value>地域</value>
  </data>
  <data name="Registered" xml:space="preserve">
    <value>登録された</value>
  </data>
  <data name="RegisteredMobile" xml:space="preserve">
    <value>登録された携帯</value>
  </data>
  <data name="relogin" xml:space="preserve">
    <value>Re-Login</value>
  </data>
  <data name="Remarks" xml:space="preserve">
    <value>備考</value>
  </data>
  <data name="RemoveFilter" xml:space="preserve">
    <value>フィルタの削除</value>
  </data>
  <data name="ReportWizard" xml:space="preserve">
    <value>レポートウィザード</value>
  </data>
  <data name="ReqDate" xml:space="preserve">
    <value>REQ日</value>
  </data>
  <data name="ReqNumber" xml:space="preserve">
    <value>REQ数</value>
  </data>
  <data name="RequestDescription" xml:space="preserve">
    <value>リクエスト説明</value>
  </data>
  <data name="RequestNumber" xml:space="preserve">
    <value>郵便番号</value>
    <comment>Label</comment>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Ja Reset</value>
  </data>
  <data name="resolutiontime" xml:space="preserve">
    <value>時間分解能</value>
  </data>
  <data name="Resolutiontimewithtimeslots" xml:space="preserve">
    <value>時間スロットに解決時間</value>
  </data>
  <data name="ResourceUtilizationReport" xml:space="preserve">
    <value>リソース使用率レポート</value>
  </data>
  <data name="responsetime" xml:space="preserve">
    <value>応答時間</value>
  </data>
  <data name="RevenuecannotbeBlankorzero" xml:space="preserve">
    <value>収入は空白にすることはできません</value>
  </data>
  <data name="RevenueGenerated" xml:space="preserve">
    <value>発生した収益</value>
  </data>
  <data name="revenuemorethen" xml:space="preserve">
    <value>もっとして収入</value>
  </data>
  <data name="RoleDefinition" xml:space="preserve">
    <value>役割の定義</value>
  </data>
  <data name="RoleName" xml:space="preserve">
    <value>役割名</value>
  </data>
  <data name="RoleNameCannotbeblank" xml:space="preserve">
    <value>役割名は空白にすることはできません</value>
  </data>
  <data name="RoleObject" xml:space="preserve">
    <value>Roleオブジェクト</value>
  </data>
  <data name="Roles" xml:space="preserve">
    <value>役割</value>
  </data>
  <data name="RoundOff" xml:space="preserve">
    <value>丸める</value>
  </data>
  <data name="Saturday" xml:space="preserve">
    <value>Ja Saturday</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="SaveAction" xml:space="preserve">
    <value>アクションを保存</value>
  </data>
  <data name="SavedSuccessfully" xml:space="preserve">
    <value>正常に保存されました</value>
  </data>
  <data name="SaveFormatandGenerateReport" xml:space="preserve">
    <value>フォーマットを保存して、レポートの生成</value>
  </data>
  <data name="savefreestockdetails" xml:space="preserve">
    <value>パーツのフリー素材の詳細を保存</value>
  </data>
  <data name="saveheader" xml:space="preserve">
    <value>ヘッダを保存</value>
  </data>
  <data name="savepartprice" xml:space="preserve">
    <value>パーツの価格詳細を保存</value>
  </data>
  <data name="SavePrefixSuffix" xml:space="preserve">
    <value>プリフィックスサフィックスを保存</value>
  </data>
  <data name="saveproductdetail" xml:space="preserve">
    <value>製品の詳細を保存</value>
  </data>
  <data name="saveproducttype" xml:space="preserve">
    <value>パーツ製品タイプを保存</value>
  </data>
  <data name="SaveRole" xml:space="preserve">
    <value>Save Roleを</value>
  </data>
  <data name="SaveStep" xml:space="preserve">
    <value>ステップを保存</value>
  </data>
  <data name="SaveStepLink" xml:space="preserve">
    <value>ステップリンクを名前を付けて保存</value>
  </data>
  <data name="SaveSuccessfull" xml:space="preserve">
    <value>正常に保存されました</value>
  </data>
  <data name="savetaxstructure" xml:space="preserve">
    <value>租税構造を保存</value>
  </data>
  <data name="SaveUser" xml:space="preserve">
    <value>ユーザーを保存</value>
  </data>
  <data name="SecondarySegment" xml:space="preserve">
    <value>セカンダリセグメント</value>
  </data>
  <data name="SecondarySegmentdescription" xml:space="preserve">
    <value>セカンダリセグメントの説明</value>
  </data>
  <data name="secondarysegmentenglish" xml:space="preserve">
    <value>セカンダリセグメント英語</value>
  </data>
  <data name="secondarysegmentlocale" xml:space="preserve">
    <value>セカンダリセグメントのロケール</value>
  </data>
  <data name="SegmentDetail" xml:space="preserve">
    <value>セグメント詳細</value>
  </data>
  <data name="select" xml:space="preserve">
    <value>選択する</value>
  </data>
  <data name="SelectAll" xml:space="preserve">
    <value>[すべて]を選択します</value>
  </data>
  <data name="selectbrand" xml:space="preserve">
    <value>ブランドを選択してください</value>
  </data>
  <data name="SelectColumn" xml:space="preserve">
    <value>フローステップを働かせる</value>
    <comment>Label</comment>
  </data>
  <data name="selectcompany" xml:space="preserve">
    <value>Ja select company</value>
  </data>
  <data name="SelectCondition" xml:space="preserve">
    <value>フローステップを働かせる</value>
    <comment>Label</comment>
  </data>
  <data name="SelectDDl" xml:space="preserve">
    <value>---------選択---------</value>
  </data>
  <data name="SelectedFileisnotanExcelFile" xml:space="preserve">
    <value>選択したファイルはエクセルファイルではありません</value>
  </data>
  <data name="SelectionCriteria" xml:space="preserve">
    <value>選択基準</value>
  </data>
  <data name="SelectModel" xml:space="preserve">
    <value>モデルを選択</value>
  </data>
  <data name="SelectModelandRequest" xml:space="preserve">
    <value>モデルを選択し、要求</value>
  </data>
  <data name="SelectOperator" xml:space="preserve">
    <value>フローステップを働かせる</value>
    <comment>Label</comment>
  </data>
  <data name="SelectPartyType" xml:space="preserve">
    <value>パーティーの種類を選択</value>
  </data>
  <data name="selectproducttype" xml:space="preserve">
    <value>製品タイプを選択します</value>
  </data>
  <data name="SelectRecordstoDelete" xml:space="preserve">
    <value>削除するレコードを選択します</value>
  </data>
  <data name="SelectReportFromPreviouslyStoredFormats" xml:space="preserve">
    <value>以前に保存されたフォーマットから[レポート]を選択します</value>
  </data>
  <data name="SelectReqNumber" xml:space="preserve">
    <value>要求番号を選択</value>
  </data>
  <data name="SelectServiceRequest" xml:space="preserve">
    <value>サービスリクエストを選択</value>
  </data>
  <data name="selectshift" xml:space="preserve">
    <value>Ja select shift</value>
  </data>
  <data name="SelectTableName" xml:space="preserve">
    <value>テーブル名を選択してください</value>
  </data>
  <data name="September" xml:space="preserve">
    <value>9月</value>
  </data>
  <data name="sequenceno" xml:space="preserve">
    <value>シーケンスなし</value>
  </data>
  <data name="Serial" xml:space="preserve">
    <value>シリアル</value>
  </data>
  <data name="serialnumber" xml:space="preserve">
    <value>シリアルナンバー</value>
  </data>
  <data name="serialnumberalreadyexistsforthismodel" xml:space="preserve">
    <value>シリアル番号は、このモデルのために既に存在している</value>
  </data>
  <data name="SerialNumberFieldSearch" xml:space="preserve">
    <value>シリアル番号のフィールド検索</value>
  </data>
  <data name="SerialNumbernotfoundfortheselectedmodel" xml:space="preserve">
    <value>シリアル番号は、選択されたモデルが見つかりません</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>Ja Service</value>
  </data>
  <data name="ServiceChargeCode" xml:space="preserve">
    <value>サービスチャージコード</value>
  </data>
  <data name="servicechargecodenotfound" xml:space="preserve">
    <value>サービス料コードが見つかりません</value>
  </data>
  <data name="ServiceChargeDetail" xml:space="preserve">
    <value>サービス料の詳細</value>
  </data>
  <data name="ServiceChargeFieldSearch" xml:space="preserve">
    <value>サービス料フィールドの検索</value>
  </data>
  <data name="servicecharges" xml:space="preserve">
    <value>サービス料金</value>
  </data>
  <data name="servicechargesdetail" xml:space="preserve">
    <value>サービス料金の詳細</value>
  </data>
  <data name="ServiceChargesDetails" xml:space="preserve">
    <value>サービス料金の詳細</value>
  </data>
  <data name="servicechargesenglish" xml:space="preserve">
    <value>英語サービス料金</value>
  </data>
  <data name="servicechargesheader" xml:space="preserve">
    <value>サービス料ヘッダー</value>
  </data>
  <data name="servicechargeslocale" xml:space="preserve">
    <value>サービス料金のロケール</value>
  </data>
  <data name="servicechargesmaster" xml:space="preserve">
    <value>サービス料マスター</value>
  </data>
  <data name="ServiceChargesTotalAmount" xml:space="preserve">
    <value>サービスは、合計金額を充電</value>
  </data>
  <data name="ServiceCode" xml:space="preserve">
    <value>Service Code</value>
    <comment>Label</comment>
  </data>
  <data name="servicecodealreadyexists" xml:space="preserve">
    <value>サービスコードは既に存在しています</value>
  </data>
  <data name="servicedate" xml:space="preserve">
    <value>サービス日</value>
  </data>
  <data name="servicedatecannotbelessthancurrentdate" xml:space="preserve">
    <value>サービスは、現在の日付より小さくすることはできません</value>
  </data>
  <data name="ServiceDetails" xml:space="preserve">
    <value>サービスの詳細</value>
  </data>
  <data name="servicehistory" xml:space="preserve">
    <value>サービス履歴</value>
  </data>
  <data name="ServicePriority" xml:space="preserve">
    <value>サービス&amp;#183;プライオリティ</value>
  </data>
  <data name="ServiceQuotationNumber" xml:space="preserve">
    <value>引用数</value>
  </data>
  <data name="ServiceRequest" xml:space="preserve">
    <value>サービス要求</value>
  </data>
  <data name="ServiceRequestAbandoned" xml:space="preserve">
    <value>サービス&amp;#183;リクエストは、放棄された</value>
  </data>
  <data name="ServiceRequestCount" xml:space="preserve">
    <value>サービス&amp;#183;リクエスト&amp;#183;カウント</value>
  </data>
  <data name="ServiceRequestDate" xml:space="preserve">
    <value>サービス依頼日</value>
  </data>
  <data name="ServiceRequestDistributionChart" xml:space="preserve">
    <value>サービスリクエストの分布図</value>
  </data>
  <data name="ServicerequestFieldSearch" xml:space="preserve">
    <value>サービス&amp;#183;リクエスト&amp;#183;フィールドの検索</value>
  </data>
  <data name="ServiceRequestNumber" xml:space="preserve">
    <value>サービス要求番号</value>
  </data>
  <data name="ServiceRequestNumbernotfound" xml:space="preserve">
    <value>サービスリクエスト番号が見つかりません</value>
  </data>
  <data name="ServiceRequestSummary" xml:space="preserve">
    <value>サービスリクエストの概要</value>
  </data>
  <data name="serviceschdule" xml:space="preserve">
    <value>サービスSchdule</value>
  </data>
  <data name="ServiceSchedule" xml:space="preserve">
    <value>サービススケジュール</value>
  </data>
  <data name="ServiceType" xml:space="preserve">
    <value>サービスの種類</value>
  </data>
  <data name="ServiceTypeName" xml:space="preserve">
    <value>サービスタイプ名</value>
  </data>
  <data name="Shift" xml:space="preserve">
    <value>ja Shift</value>
  </data>
  <data name="ShortName" xml:space="preserve">
    <value>省略名</value>
  </data>
  <data name="siteaddress" xml:space="preserve">
    <value>サイトアドレス</value>
  </data>
  <data name="siteaddressdetails" xml:space="preserve">
    <value>サイトのアドレスの詳細</value>
  </data>
  <data name="SixteenToTwentyFourHours" xml:space="preserve">
    <value>16&amp;#12316;24時間</value>
  </data>
  <data name="skill" xml:space="preserve">
    <value>スキル</value>
  </data>
  <data name="Skillisalreadyassociatedwiththeemployee" xml:space="preserve">
    <value>スキルはすでに従業員に関連付けられている</value>
  </data>
  <data name="skilllevel" xml:space="preserve">
    <value>能力水準</value>
  </data>
  <data name="skilllevelshouldbebetween1to10" xml:space="preserve">
    <value>スキルレベルが10から1の間でなければなりません</value>
  </data>
  <data name="Skillset" xml:space="preserve">
    <value>スキルセット</value>
  </data>
  <data name="slno" xml:space="preserve">
    <value>SLなし</value>
  </data>
  <data name="SMSToAddressee" xml:space="preserve">
    <value>お届け先へのSMS</value>
  </data>
  <data name="SMSToCustomer" xml:space="preserve">
    <value>顧客へのSMS</value>
  </data>
  <data name="SMTPMailBox" xml:space="preserve">
    <value>SMTPメールボックス</value>
  </data>
  <data name="SMTPPassword" xml:space="preserve">
    <value>SMTPパスワード</value>
  </data>
  <data name="SMTPServerName" xml:space="preserve">
    <value>SMTPサーバー名</value>
  </data>
  <data name="SMTPUserName" xml:space="preserve">
    <value>SMTPユーザー名</value>
  </data>
  <data name="Sn" xml:space="preserve">
    <value>Snの</value>
  </data>
  <data name="SortOrder" xml:space="preserve">
    <value>ソート順序</value>
  </data>
  <data name="SortOrdercannotbeblank" xml:space="preserve">
    <value>ソート順は空白にすることはできません</value>
  </data>
  <data name="sortordercannotbeblankforMenu" xml:space="preserve">
    <value>ソート順序は、メニューの空白にすることはできません</value>
  </data>
  <data name="SortOrderCannotbegreaterthan" xml:space="preserve">
    <value>ソート順は、255を超えることはできません</value>
  </data>
  <data name="SourceColumns" xml:space="preserve">
    <value>ソース列</value>
  </data>
  <data name="Specialization" xml:space="preserve">
    <value>分業</value>
  </data>
  <data name="SpecializationMaster" xml:space="preserve">
    <value>Specializationのマスター</value>
  </data>
  <data name="SqlException" xml:space="preserve">
    <value>データベースエラーが発生しました</value>
  </data>
  <data name="SRCount" xml:space="preserve">
    <value>サービスリクエストカウント</value>
  </data>
  <data name="SRNotFound" xml:space="preserve">
    <value>サービス要求が見つかりません</value>
  </data>
  <data name="StandardHours" xml:space="preserve">
    <value>標準時間</value>
  </data>
  <data name="standardtime" xml:space="preserve">
    <value>標準時</value>
  </data>
  <data name="startnumber" xml:space="preserve">
    <value>番号を開始</value>
  </data>
  <data name="startnumbercannotbenullorzero" xml:space="preserve">
    <value>スタート番号がNULLまたはゼロにすることはできません</value>
  </data>
  <data name="State" xml:space="preserve">
    <value>状態</value>
  </data>
  <data name="stateenglish" xml:space="preserve">
    <value>状態英語</value>
  </data>
  <data name="statelocale" xml:space="preserve">
    <value>状態ロケール</value>
  </data>
  <data name="status" xml:space="preserve">
    <value>ステータス</value>
  </data>
  <data name="StepLink" xml:space="preserve">
    <value>ステップリンク</value>
  </data>
  <data name="StepName" xml:space="preserve">
    <value>ステップ名</value>
  </data>
  <data name="Steps" xml:space="preserve">
    <value>手順</value>
  </data>
  <data name="StepStatus" xml:space="preserve">
    <value>ステップステータス</value>
  </data>
  <data name="StepType" xml:space="preserve">
    <value>ステップタイプ</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>成功</value>
  </data>
  <data name="suffix" xml:space="preserve">
    <value>サフィックス</value>
  </data>
  <data name="SuffixalreadySelected" xml:space="preserve">
    <value>接尾辞は、すでに選択</value>
  </data>
  <data name="Summary" xml:space="preserve">
    <value>要約</value>
  </data>
  <data name="SundryDetail" xml:space="preserve">
    <value>雑詳細</value>
  </data>
  <data name="SundryDetails" xml:space="preserve">
    <value>雑詳細</value>
  </data>
  <data name="SundryJobDescription" xml:space="preserve">
    <value>諸口仕事内容</value>
  </data>
  <data name="SundryTotalAmount" xml:space="preserve">
    <value>雑貨合計金額</value>
  </data>
  <data name="TableName" xml:space="preserve">
    <value>テーブル名</value>
  </data>
  <data name="Tax" xml:space="preserve">
    <value>税</value>
  </data>
  <data name="Taxable" xml:space="preserve">
    <value>課税</value>
  </data>
  <data name="Taxableothercharges1" xml:space="preserve">
    <value>課税対象となるその他の費用1</value>
  </data>
  <data name="Taxableothercharges1Amount" xml:space="preserve">
    <value>課税その他費用1金額</value>
  </data>
  <data name="Taxableothercharges2" xml:space="preserve">
    <value>課税対象となるその他の費用2</value>
  </data>
  <data name="Taxableothercharges2Amount" xml:space="preserve">
    <value>課税その他費用2量</value>
  </data>
  <data name="Taxamount" xml:space="preserve">
    <value>Aamount税</value>
  </data>
  <data name="TaxCode" xml:space="preserve">
    <value>税法</value>
  </data>
  <data name="TaxCodeName" xml:space="preserve">
    <value>税コード名</value>
  </data>
  <data name="TaxDetail" xml:space="preserve">
    <value>Tax Detail</value>
    <comment>Label</comment>
  </data>
  <data name="TaxDetails" xml:space="preserve">
    <value>税の詳細</value>
  </data>
  <data name="Taxnamealreadyexists" xml:space="preserve">
    <value>税務名は既に存在しています</value>
  </data>
  <data name="taxpercentage" xml:space="preserve">
    <value>税の割合</value>
  </data>
  <data name="TaxStructure" xml:space="preserve">
    <value>租税構造</value>
  </data>
  <data name="taxstructuredetail" xml:space="preserve">
    <value>租税構造の詳細</value>
  </data>
  <data name="taxstructuredetails" xml:space="preserve">
    <value>租税構造の詳細</value>
  </data>
  <data name="taxstructureenglish" xml:space="preserve">
    <value>税構造英語</value>
  </data>
  <data name="taxstructureheader" xml:space="preserve">
    <value>租税構造ヘッダー</value>
  </data>
  <data name="taxstructurelocale" xml:space="preserve">
    <value>租税構造のロケール</value>
  </data>
  <data name="taxstructurename" xml:space="preserve">
    <value>税構造名</value>
  </data>
  <data name="taxtype" xml:space="preserve">
    <value>税の種類</value>
  </data>
  <data name="taxtypealreadyselected" xml:space="preserve">
    <value>税タイプがすでに選択</value>
  </data>
  <data name="taxtypealrearyselected" xml:space="preserve">
    <value>税タイプがすでに選択</value>
  </data>
  <data name="taxtypeisreferencedinformulacannotdelete" xml:space="preserve">
    <value>税の種類は次の式で参照されて削除することはできません</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>はい</value>
    <comment>Label</comment>
  </data>
  <data name="Terms" xml:space="preserve">
    <value>条件</value>
  </data>
  <data name="TermsAndConditions" xml:space="preserve">
    <value>利用規約</value>
  </data>
  <data name="TermsConditions" xml:space="preserve">
    <value>条件</value>
  </data>
  <data name="Thebrandhasalreadybeenselected" xml:space="preserve">
    <value>ブランドは既に選択されている</value>
  </data>
  <data name="TheCompanyDealerhasalreadybeenassociated" xml:space="preserve">
    <value>会社/販売代理店は、すでに関連付けられている</value>
  </data>
  <data name="thesecondarysegmentalreadyexists" xml:space="preserve">
    <value>セカンダリセグメントがすでに存在します</value>
  </data>
  <data name="thestatealreadyexists" xml:space="preserve">
    <value>状態はすでに存在しています</value>
  </data>
  <data name="ThetaxStructurehasalreadybeenselected" xml:space="preserve">
    <value>租税構造が既に選択されています</value>
  </data>
  <data name="ThirtySixToFourtyEightHours" xml:space="preserve">
    <value>36&amp;#12316;48時間</value>
  </data>
  <data name="ThisLoginIDisalreadyexists" xml:space="preserve">
    <value>このログインIDは既に存在している</value>
  </data>
  <data name="ThisModuleisalreadyexists" xml:space="preserve">
    <value>このモジュールは、すでに存在している</value>
  </data>
  <data name="ThisRoleisalreadyexists" xml:space="preserve">
    <value>この役割は、すでに存在している</value>
  </data>
  <data name="Thisroleisalreadyselectedfortheuser" xml:space="preserve">
    <value>このロールは、ユーザーにすでに選択されている</value>
  </data>
  <data name="ThisSerialNumberisalreadyassociatedwiththecustomer" xml:space="preserve">
    <value>このシリアル番号は、既に顧客に関連付けられている</value>
  </data>
  <data name="Thursday" xml:space="preserve">
    <value>Ja Thursday `</value>
  </data>
  <data name="To" xml:space="preserve">
    <value>へ</value>
  </data>
  <data name="todate" xml:space="preserve">
    <value>日</value>
  </data>
  <data name="ToDatecannotbegreaterthanCurrentDate" xml:space="preserve">
    <value>日付を現在の日付より大きくすることはできません</value>
  </data>
  <data name="ToDatecannotbelessthanCurrentDate" xml:space="preserve">
    <value>日付現在の日付より小さくすることはできません</value>
  </data>
  <data name="todatecannotbelessthenfromdatedate" xml:space="preserve">
    <value>日付に日付から少ないし、することはできません</value>
  </data>
  <data name="todatemustbegreaterthanorequaltofromdate" xml:space="preserve">
    <value>日付に日付からより大きいか等しくなければなりません</value>
  </data>
  <data name="TopModel" xml:space="preserve">
    <value>郵便番号</value>
    <comment>Label</comment>
  </data>
  <data name="ToStep" xml:space="preserve">
    <value>ステップに</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>合計</value>
  </data>
  <data name="TotalAllocatedHours" xml:space="preserve">
    <value>割り当てられた総時間</value>
  </data>
  <data name="TotalAmount" xml:space="preserve">
    <value>総額</value>
  </data>
  <data name="TotalOn" xml:space="preserve">
    <value>合計オン</value>
  </data>
  <data name="TotalQuotationAmount" xml:space="preserve">
    <value>合計見積金額</value>
  </data>
  <data name="TotalTaxableAmount" xml:space="preserve">
    <value>合計課税対象額</value>
  </data>
  <data name="TotalTaxableAmountBlank" xml:space="preserve">
    <value>合計課税額は空白です</value>
  </data>
  <data name="TotalWorkingHours" xml:space="preserve">
    <value>総労働時間</value>
  </data>
  <data name="TransactionisalreadybeenLocked" xml:space="preserve">
    <value>トランザクションが既にロックされている</value>
  </data>
  <data name="TransactionisalreadybeenUnLocked" xml:space="preserve">
    <value>トランザクションのロックは既に解除されています</value>
  </data>
  <data name="TransactionLockedSuccessfully" xml:space="preserve">
    <value>トランザクションが正常にロックされました</value>
  </data>
  <data name="TransactionUnLockedSuccessfully" xml:space="preserve">
    <value>トランザクションが正常にロック解除</value>
  </data>
  <data name="Tuesday" xml:space="preserve">
    <value>Ja Tuesday</value>
  </data>
  <data name="TwentyFourToThirtySixHours" xml:space="preserve">
    <value>24&amp;#12316;36時間</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>タイプ</value>
  </data>
  <data name="uniqueidentifieralreadyexists" xml:space="preserve">
    <value>一意の識別子はすでに存在しています</value>
  </data>
  <data name="unitofmeasurement" xml:space="preserve">
    <value>測定単位</value>
  </data>
  <data name="UnLock" xml:space="preserve">
    <value>はい</value>
    <comment>Label</comment>
  </data>
  <data name="UnRegisteredServiceRequest" xml:space="preserve">
    <value>Ja Verification Queue</value>
  </data>
  <data name="uom" xml:space="preserve">
    <value>タイプ</value>
  </data>
  <data name="UploadFile" xml:space="preserve">
    <value>ファイルをアップロード</value>
  </data>
  <data name="UploadParts" xml:space="preserve">
    <value>パーツをアップロード</value>
  </data>
  <data name="usageenvironment" xml:space="preserve">
    <value>使用環境</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>ユーザー</value>
  </data>
  <data name="UserDataSavedSuccessfully" xml:space="preserve">
    <value>ユーザーデータの保存に成功しました</value>
  </data>
  <data name="UserDetail" xml:space="preserve">
    <value>ユーザーの詳細</value>
  </data>
  <data name="UserDetails" xml:space="preserve">
    <value>ユーザーの詳細</value>
  </data>
  <data name="Userdonthaveaccesstopartymaster" xml:space="preserve">
    <value>Ja User dont have access to Party Master</value>
  </data>
  <data name="Userdonthaveaccesstoproductmaster" xml:space="preserve">
    <value>User dont have access to Product Master</value>
  </data>
  <data name="Userdonthaveeditaccess" xml:space="preserve">
    <value>Ja User dont have Edit Access</value>
  </data>
  <data name="Userdonthaveeditaccesstoproductmaster" xml:space="preserve">
    <value>User dont have Edit access to Product Master</value>
  </data>
  <data name="Userislocked" xml:space="preserve">
    <value>ja User is locked</value>
  </data>
  <data name="Userisnotactive" xml:space="preserve">
    <value>ja User is not active</value>
  </data>
  <data name="UserName" xml:space="preserve">
    <value>ユーザー名</value>
  </data>
  <data name="UserNameOrPasswordYouEnteredIsIncorrectPleaseTryAgain" xml:space="preserve">
    <value>あなたが入力したユーザー名またはパスワードが正しくありません。</value>
  </data>
  <data name="UserRoleDetails" xml:space="preserve">
    <value>ユーザロールの詳細</value>
  </data>
  <data name="UserRoles" xml:space="preserve">
    <value>ユーザーの役割</value>
  </data>
  <data name="UserType" xml:space="preserve">
    <value>ユーザータイプ</value>
  </data>
  <data name="UtilizationPercentage" xml:space="preserve">
    <value>率％</value>
  </data>
  <data name="Value" xml:space="preserve">
    <value>値</value>
  </data>
  <data name="ValueisMandatoryforselectedColumn" xml:space="preserve">
    <value>フローステップを働かせる</value>
    <comment>Label</comment>
  </data>
  <data name="Ver" xml:space="preserve">
    <value>バージョン</value>
  </data>
  <data name="VerificationQueue" xml:space="preserve">
    <value>検証キュー</value>
  </data>
  <data name="Version" xml:space="preserve">
    <value>バージョン</value>
  </data>
  <data name="VersionDate" xml:space="preserve">
    <value>Ja Version Date</value>
  </data>
  <data name="VersionNumber" xml:space="preserve">
    <value>Ja Version Number</value>
  </data>
  <data name="VersionNumberandDate" xml:space="preserve">
    <value>Ja Version Number and Date</value>
  </data>
  <data name="view" xml:space="preserve">
    <value>表示</value>
  </data>
  <data name="ViewJobCard" xml:space="preserve">
    <value>ジョブ&amp;#183;カードを見る</value>
  </data>
  <data name="ViewPartsMaster" xml:space="preserve">
    <value>ビューパーツマスター</value>
  </data>
  <data name="WarrantyDate" xml:space="preserve">
    <value>保証日</value>
  </data>
  <data name="warrantydetails" xml:space="preserve">
    <value>保証詳細</value>
  </data>
  <data name="Website" xml:space="preserve">
    <value>ウェブサイト</value>
  </data>
  <data name="Wednesday" xml:space="preserve">
    <value>Ja Wednesday</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>ようこそ：</value>
  </data>
  <data name="Where" xml:space="preserve">
    <value>はい</value>
    <comment>Label</comment>
  </data>
  <data name="WorkFlow" xml:space="preserve">
    <value>作業の流れ</value>
  </data>
  <data name="WorkFlowID" xml:space="preserve">
    <value>フローIDを働かせる</value>
  </data>
  <data name="WorkFlowName" xml:space="preserve">
    <value>作業フロー名</value>
  </data>
  <data name="WorkFlowSteps" xml:space="preserve">
    <value>フローステップを働かせる</value>
  </data>
  <data name="WorkingDays" xml:space="preserve">
    <value>Ja Working Days</value>
  </data>
  <data name="WorkingTime" xml:space="preserve">
    <value>Ja Working Time</value>
  </data>
  <data name="Year" xml:space="preserve">
    <value>年</value>
  </data>
  <data name="Yearshouldbebetween2000and2999" xml:space="preserve">
    <value>Ja Year should be between 2000 and 2999</value>
  </data>
  <data name="yes" xml:space="preserve">
    <value>はい</value>
  </data>
  <data name="Youdonothaveeditpermission" xml:space="preserve">
    <value>Ja You do not have edit permission</value>
  </data>
  <data name="YouhavebeenLoggedoutsuccessfully" xml:space="preserve">
    <value>Ja You have been Logged out successfully</value>
  </data>
  <data name="ZipCode" xml:space="preserve">
    <value>郵便番号</value>
  </data>
  <data name="Queue" xml:space="preserve">
    <value>Ja Queue</value>
  </data>
  <data name="DuplicateEmployee" xml:space="preserve">
    <value>Duplicate Employee</value>
    <comment>Message</comment>
  </data>
  <data name="DuplicateFunctionGroup" xml:space="preserve">
    <value>Ja Duplicate Function Group</value>
  </data>
  <data name="Pleaseenterserailnumber" xml:space="preserve">
    <value>Ja Please enter serail number</value>
    <comment>Message</comment>
  </data>
  <data name="EditOperationDetails" xml:space="preserve">
    <value>Edit Operation Details</value>
    <comment>Message</comment>
  </data>
  <data name="TopModelwithMaximumSR" xml:space="preserve">
    <value>Ja Top 10 Models with Maximum Service Request</value>
  </data>
  <data name="SRBasedonCalltype" xml:space="preserve">
    <value>JA Service Request Based on Call type</value>
  </data>
  <data name="CustomerQuotationArchived" xml:space="preserve">
    <value>Ja Customer Quotation Archived</value>
  </data>
  <data name="AreyousureyouwanttoLogout" xml:space="preserve">
    <value>Ja Are you sure you want to Log out</value>
  </data>
  <data name="PleaseselectServicerequestnumber" xml:space="preserve">
    <value>JA Please select service request number</value>
    <comment>Message</comment>
  </data>
  <data name="inactiveproduct" xml:space="preserve">
    <value>Ja In Active Product</value>
  </data>
  <data name="Noeditpermissionforproductmaster" xml:space="preserve">
    <value>Ja No Edit permission for Product Master</value>
  </data>
  <data name="EventStartDateCannotbeLessthanJobCardDate" xml:space="preserve">
    <value>JA Event start date cannot be less than Job card date</value>
    <comment>Message</comment>
  </data>
  <data name="cannotclosethejobwithoutproductdetails" xml:space="preserve">
    <value>Ja Cannot close the job without product details</value>
    <comment>Message</comment>
  </data>
  <data name="ChangePassword" xml:space="preserve">
    <value>Ja Change Password</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>Ja New Password</value>
  </data>
  <data name="OldPassword" xml:space="preserve">
    <value>Ja Old Password</value>
  </data>
  <data name="IncompleteOperationsarepresent" xml:space="preserve">
    <value>Ja Incomplete operations are present, do you want to close the Job card?</value>
    <comment>Message</comment>
  </data>
  <data name="IncorrectPassword" xml:space="preserve">
    <value>Ja Incorrect Password</value>
  </data>
  <data name="newpasswordandConfirmpasswordarenotmatching" xml:space="preserve">
    <value>Ja New Password and Confirm Password are not matching</value>
  </data>
  <data name="OperationStartdatecannotbelessthanJobcarddate" xml:space="preserve">
    <value>Ja Operation start date cannot be less than job card date</value>
    <comment>Message</comment>
  </data>
  <data name="Eventstarttimecoincideswithothereventsdoyouwanttocontinue" xml:space="preserve">
    <value>Ja Event start time coincides with other events, do you want to continue?</value>
    <comment>Message</comment>
  </data>
  <data name="Pleaseaddatleastoneoperationdetail" xml:space="preserve">
    <value>Ja Please add atleast one operation detail</value>
    <comment>Message</comment>
  </data>
  <data name="InvalidSerialNumber" xml:space="preserve">
    <value>Ja Invalid serial number</value>
    <comment>Message</comment>
  </data>
  <data name="InvalidDecimal" xml:space="preserve">
    <value>Ja Invalid Decimal</value>
    <comment>Message</comment>
  </data>
  <data name="PleaseEnterDateAs" xml:space="preserve">
    <value>Ja Please Enter Date As</value>
  </data>
  <data name="PartySchedule" xml:space="preserve">
    <value>Ja Party Schedule</value>
  </data>
  <data name="ProductSchedule" xml:space="preserve">
    <value>Ja Product Schedule</value>
  </data>
  <data name="ScheduleType" xml:space="preserve">
    <value>Ja Schedule Type</value>
  </data>
  <data name="InvalidQuantity" xml:space="preserve">
    <value>Ja Invalid Quantity.</value>
    <comment>Message</comment>
  </data>
  <data name="InvalidRate" xml:space="preserve">
    <value>Ja Invalid Rate.</value>
    <comment>Message</comment>
  </data>
  <data name="PartAlreadyAdded" xml:space="preserve">
    <value>Ja Part Already Added.</value>
    <comment>Message</comment>
  </data>
  <data name="PartNumberalreadyselected" xml:space="preserve">
    <value>Ja Part Number already selected</value>
    <comment>Message</comment>
  </data>
  <data name="PartNumberisBlank" xml:space="preserve">
    <value>Ja Part Number is blank.</value>
    <comment>Message</comment>
  </data>
  <data name="PartNumbernotassociatedtotheselectedproductdetails" xml:space="preserve">
    <value>Ja Part Number not associated to the selected product details.</value>
    <comment>Message</comment>
  </data>
  <data name="Quicklinks" xml:space="preserve">
    <value>Ja Quick Links</value>
  </data>
  <data name="PersonalCalender" xml:space="preserve">
    <value>Ja Personal Calendar</value>
    <comment>Label</comment>
  </data>
  <data name="capslockison" xml:space="preserve">
    <value>Ja caps lock is on</value>
  </data>
  <data name="CCToAssignee" xml:space="preserve">
    <value>作業フロ便番号</value>
  </data>
</root>