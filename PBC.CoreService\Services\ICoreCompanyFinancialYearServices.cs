using Microsoft.AspNetCore.Mvc;
using PBC.CoreService.Utilities.DTOs;

namespace PBC.CoreService.Services
{
    public interface ICoreCompanyFinancialYearServices
    {
        /// <summary>
        /// Select all financial years of the company
        /// </summary>
        /// <param name="selectObj">SelectCompanyFinancialYearList object</param>
        /// <param name="connString">Database connection string</param>
        /// <param name="logException">Log exception flag</param>
        /// <param name="sidx">Sort index</param>
        /// <param name="sord">Sort order</param>
        /// <param name="page">Page number</param>
        /// <param name="rows">Rows per page</param>
        /// <returns>JsonResult with financial year data</returns>
        Task<IActionResult> Select(SelectCompanyFinancialYearList selectObj, string connString, int logException, string sidx, string sord, int page, int rows);

        /// <summary>
        /// Get all company names
        /// </summary>
        /// <param name="selectCompanyObj">SelectCompanyList object</param>
        /// <param name="connString">Database connection string</param>
        /// <param name="logException">Log exception flag</param>
        /// <returns>JsonResult with company data</returns>
        Task<IActionResult> SelectCompany(SelectCompanyList selectCompanyObj, string connString, int logException);

        /// <summary>
        /// Save financial year data
        /// </summary>
        /// <param name="saveObj">SaveCoreCompanyFinancialList object</param>
        /// <param name="connString">Database connection string</param>
        /// <param name="logException">Log exception flag</param>
        /// <returns>JsonResult with success status</returns>
        Task<IActionResult> Save(SaveCoreCompanyFinancialList saveObj, string connString, int logException);

        /// <summary>
        /// Delete financial year records
        /// </summary>
        /// <param name="deleteObj">DeleteCoreCompanyFinancialList object</param>
        /// <param name="connString">Database connection string</param>
        /// <param name="logException">Log exception flag</param>
        /// <returns>JsonResult with deletion status</returns>
        Task<IActionResult> Delete(DeleteCoreCompanyFinancialList deleteObj, string connString, int logException);

        /// <summary>
        /// Check if financial year already exists for the company
        /// </summary>
        /// <param name="checkFinancialYearObj">CheckFinancialYearList object</param>
        /// <param name="connString">Database connection string</param>
        /// <param name="logException">Log exception flag</param>
        /// <returns>JsonResult with status (1 if exists, 0 if not)</returns>
        Task<IActionResult> CheckFinancialYear(CheckFinancialYearList checkFinancialYearObj, string connString, int logException);

        /// <summary>
        /// Get all saved financial years for the company
        /// </summary>
        /// <param name="getAllSavedFinancialYearsObj">GetAllSavedFinancialYearsList object</param>
        /// <param name="connString">Database connection string</param>
        /// <param name="logException">Log exception flag</param>
        /// <returns>JsonResult with financial years array</returns>
        Task<IActionResult> GetAllSavedFinancialYears(GetAllSavedFinancialYearsList getAllSavedFinancialYearsObj, string connString, int logException);
    }
}
