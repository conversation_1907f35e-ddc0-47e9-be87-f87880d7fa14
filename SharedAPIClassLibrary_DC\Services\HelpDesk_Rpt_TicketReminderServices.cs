﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace SharedAPIClassLibrary_AMERP
{
    public class HelpDesk_Rpt_TicketReminderServices
    {



        #region ::: LoadMachineTypeDropdown Uday Kumar J B 14-11-2024:::
        /// <summary>
        ////to Select Machine types based on the Brand
        /// </summary> 
        /// 

        public static IActionResult LoadProductType(HelpDesk_Rpt_TicketReminderLoadProductTypeList HelpDesk_Rpt_TicketReminderLoadProductTypeobj, string connString, int LogException)
        {
            int UserLang = Convert.ToInt32(HelpDesk_Rpt_TicketReminderLoadProductTypeobj.UserLanguageID);
            int GeneralLang = Convert.ToInt32(HelpDesk_Rpt_TicketReminderLoadProductTypeobj.GeneralLanguageID);
            var jsonData = default(dynamic);

            try
            {
                string BrandID = HelpDesk_Rpt_TicketReminderLoadProductTypeobj.BrandID.TrimEnd(new char[] { ',' });

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetProductTypesByBrand", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@BrandID", BrandID);
                        cmd.Parameters.AddWithValue("@UserLang", UserLang);
                        cmd.Parameters.AddWithValue("@GeneralLang", GeneralLang);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            var ProductTypeList = new List<object>();

                            while (reader.Read())
                            {
                                ProductTypeList.Add(new
                                {
                                    ID = reader["ID"],
                                    Name = reader["Name"]
                                });
                            }

                            jsonData = new
                            {
                                ProductType = ProductTypeList
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }
        #endregion



        #region ::: LoadModel Uday Kumar J B 14-11-2024:::
        /// <summary>
        ////to Select model based on the product type
        /// </summary> 
        /// 

        public static IActionResult LoadModel(HelpDesk_Rpt_TicketReminderLoadModelList HelpDesk_Rpt_TicketReminderLoadModelobj, string connString, int LogException)
        {
            int UserLang = Convert.ToInt32(HelpDesk_Rpt_TicketReminderLoadModelobj.UserLanguageID);
            int GeneralLang = Convert.ToInt32(HelpDesk_Rpt_TicketReminderLoadModelobj.GeneralLanguageID);
            var jsonData = default(dynamic);

            try
            {
                string MtypeID = HelpDesk_Rpt_TicketReminderLoadModelobj.MtypeID.TrimEnd(new char[] { ',' });

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetModelsByProductType", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@MtypeID", MtypeID);
                        cmd.Parameters.AddWithValue("@UserLang", UserLang);
                        cmd.Parameters.AddWithValue("@GeneralLang", GeneralLang);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            var ModelList = new List<object>();

                            while (reader.Read())
                            {
                                ModelList.Add(new
                                {
                                    ID = reader["ID"],
                                    Name = reader["Name"]
                                });
                            }

                            jsonData = new
                            {
                                Model = ModelList
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }
        #endregion



        #region ::: SelectBrand Uday Kumar J B 14-11-2024:::
        /// <summary>
        /// To select Brand
        /// </summary>  
        /// 
        public static IActionResult SelectBrandTypes(HelpDesk_Rpt_TicketReminderLoadModelList HelpDesk_Rpt_TicketReminderLoadModelobj, string connString, int LogException)
        {
            int UserLang = Convert.ToInt32(HelpDesk_Rpt_TicketReminderLoadModelobj.UserLanguageID);
            int GeneralLang = Convert.ToInt32(HelpDesk_Rpt_TicketReminderLoadModelobj.GeneralLanguageID);
            var jsonData = default(dynamic);
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetBrandTypesByCompanyAndLanguage", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", HelpDesk_Rpt_TicketReminderLoadModelobj.Company_ID);
                        cmd.Parameters.AddWithValue("@UserLang", UserLang);
                        cmd.Parameters.AddWithValue("@GeneralLang", GeneralLang);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            var BrandList = new List<object>();

                            while (reader.Read())
                            {
                                BrandList.Add(new
                                {
                                    ID = reader["ID"],
                                    Name = reader["Name"]
                                });
                            }

                            jsonData = new
                            {
                                Brand = BrandList
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: GetCustomerDueService Uday Kumar J B 14-11-2024:::
        /// <summary>
        /// GetCustomerDueService
        /// </summary> 
        /// 

        public static IQueryable<TicketReminder> GetCustomerDueService(HelpDesk_Rpt_TicketReminderSelectList HelpDesk_Rpt_TicketReminderSelectobj, string connString, int LogException, DateTime Fromdate, DateTime Todate, string BrandArray, string ProductTypeArray, string ModelArray, string CompanyIDs, string BranchIDs)
        {
            List<TicketReminder> reminderServices = new List<TicketReminder>();
            try
            {
                Fromdate = Convert.ToDateTime(Fromdate);
                Todate = Todate.AddDays(1);

                BrandArray = Common.DecryptString(BrandArray).TrimEnd(',');
                ProductTypeArray = Common.DecryptString(ProductTypeArray).TrimEnd(',');
                ModelArray = Common.DecryptString(ModelArray).TrimEnd(',');
                CompanyIDs = string.IsNullOrWhiteSpace(CompanyIDs) ? "0" : CompanyIDs.TrimEnd(',');
                BranchIDs = BranchIDs?.Trim().TrimEnd(',');

                int BranchID = Convert.ToInt32(HelpDesk_Rpt_TicketReminderSelectobj.Branch);
                int companyID = HelpDesk_Rpt_TicketReminderSelectobj.Company_ID;
                int UserLang = Convert.ToInt32(HelpDesk_Rpt_TicketReminderSelectobj.UserLanguageID);
                int GeneralLang = Convert.ToInt32(HelpDesk_Rpt_TicketReminderSelectobj.GeneralLanguageID);
                string EndStepStatusIDs = "";
                IQueryable<TicketReminder> iQServices = null;
                List<wfsteps> prodservice = new List<wfsteps>();

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Fetch End Step Status IDs
                    using (SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetWFStepStatusCustomerDue", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@WorkFlow_ID", 1);
                        command.Parameters.AddWithValue("@WFStepType_Nm", "END");

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                wfsteps step = new wfsteps
                                {
                                    WFStepStatus_ID = reader.GetInt32(reader.GetOrdinal("WFStepStatus_ID"))
                                };
                                prodservice.Add(step);
                            }
                        }
                    }

                    EndStepStatusIDs = string.Join(",", prodservice.Select(p => p.WFStepStatus_ID));

                    string procedureName = UserLang == GeneralLang
                        ? "SP_AMERP_HelpDesk_GetTicketReminderData"
                        : "SP_AMERP_HelpDesk_GetTicketRemindersLocal";

                    // Fetch Ticket Reminders
                    using (SqlCommand command = new SqlCommand(procedureName, connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // Add parameters to the command
                        command.Parameters.AddWithValue("@CompanyIDs", CompanyIDs);
                        command.Parameters.AddWithValue("@BranchIDs", BranchIDs);
                        command.Parameters.AddWithValue("@BrandArray", BrandArray);
                        command.Parameters.AddWithValue("@ModelArray", ModelArray);
                        command.Parameters.AddWithValue("@ProductTypeArray", ProductTypeArray);
                        command.Parameters.AddWithValue("@EndStepStatusIDs", EndStepStatusIDs);
                        command.Parameters.AddWithValue("@FromDate", Fromdate);
                        command.Parameters.AddWithValue("@ToDate", Todate);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var ticketReminder = new TicketReminder
                                {
                                    RegionName = Common.getRegionNamebyCompany(connString, LogException, UserLang, GeneralLang, Convert.ToInt32(reader["Company_ID"])),
                                    CompanyName = GetCompanyName(connString, Convert.ToInt32(reader["Company_ID"]), HelpDesk_Rpt_TicketReminderSelectobj.Language_ID),
                                    BranchName = GetBranchName(connString, Convert.ToInt32(reader["Branch_ID"]), HelpDesk_Rpt_TicketReminderSelectobj.Language_ID),
                                    TicketNumber = reader["TicketNumber"].ToString(),
                                    BrandName = reader["BrandName"].ToString(),
                                    ProductTypeName = reader["ProductTypeName"].ToString(),
                                    ModelName = reader["ModelName"].ToString(),
                                    SerialNumber = reader["SerialNumber"].ToString(),
                                    CustomerName = reader["CustomerName"].ToString(),
                                    CustomerContact = reader["CustomerContact"].ToString(),
                                    CustomerContactPhone = reader["CustomerContactPhone"].ToString(),
                                    CustomerContactEmail = reader["CustomerContactEmail"].ToString(),
                                    CallDescription = reader["CallDescription"].ToString(),
                                    AssignedTo = reader["AssignedTo"].ToString(),
                                    FollowupDescription = reader["FollowupDescription"].ToString(),
                                    StartDate = Convert.ToDateTime(reader["StartDate"]).ToString("dd-MMM-yyyy"),
                                    EndDate = reader["EndDate"] != DBNull.Value ? Convert.ToDateTime(reader["EndDate"]).ToString("dd-MMM-yyyy") : "",
                                    Mode = reader["Mode"].ToString(),
                                    Status = reader["Status"].ToString(),
                                    Remarks = reader["Remarks"].ToString(),
                                    SendReminder = "<input type='checkbox' class='chkSendReminder' id='" + reader["Party_ID"] + "' key='" + reader["FollowUpDetail_ID"] + "' />",
                                    Party_ID = Convert.ToInt32(reader["Party_ID"]),
                                    FollowUpDetail_ID = Convert.ToInt32(reader["FollowUpDetail_ID"])
                                };

                                reminderServices.Add(ticketReminder);
                            }
                        }
                    }
                }
                iQServices = reminderServices.AsQueryable<TicketReminder>();
                return iQServices;
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return null;
            }
        }

        private static string GetCompanyName(string connString, int companyId, int? languageId)
        {
            string companyName = null;

            using (SqlConnection connection = new SqlConnection(connString))
            {
                using (SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetCompanyName", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@Company_ID", companyId);
                    if (languageId.HasValue)
                        command.Parameters.AddWithValue("@Language_ID", languageId.Value);
                    else
                        command.Parameters.AddWithValue("@Language_ID", DBNull.Value);

                    connection.Open();
                    companyName = command.ExecuteScalar() as string;
                }
            }

            return companyName;
        }

        private static string GetBranchName(string connString, int branchId, int? languageId)
        {
            string branchName = null;

            using (SqlConnection connection = new SqlConnection(connString))
            {
                using (SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetBranchName", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@Branch_ID", branchId);
                    if (languageId.HasValue)
                        command.Parameters.AddWithValue("@Language_ID", languageId.Value);
                    else
                        command.Parameters.AddWithValue("@Language_ID", DBNull.Value);

                    connection.Open();
                    branchName = command.ExecuteScalar() as string;
                }
            }

            return branchName;
        }
        #endregion


        #region ::: SelectCustomerDueService Uday Kumar J B 14-11-2024:::
        /// <summary>
        /// to get the logged in users permission
        /// </summary>   
        public static IActionResult Select(HelpDesk_Rpt_TicketReminderSelectList HelpDesk_Rpt_TicketReminderSelectobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)//int rows,
        {
            int count = 0;
            //int total = 0;
            var CustomerDue = default(dynamic);
            IQueryable<TicketReminder> iQServices = null;

            try
            {
                iQServices = GetCustomerDueService(HelpDesk_Rpt_TicketReminderSelectobj, connString, LogException, HelpDesk_Rpt_TicketReminderSelectobj.Fromdate, HelpDesk_Rpt_TicketReminderSelectobj.Todate, HelpDesk_Rpt_TicketReminderSelectobj.BrandArray, HelpDesk_Rpt_TicketReminderSelectobj.ProductTypeArray, HelpDesk_Rpt_TicketReminderSelectobj.ModelArray, HelpDesk_Rpt_TicketReminderSelectobj.CompanyIDs, HelpDesk_Rpt_TicketReminderSelectobj.BranchIDs);
                if (_search)
                {
                    Filters filterobj = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(filters))).ToObject<Filters>();
                    if (filterobj.rules.Count() > 0)
                    {
                        iQServices = iQServices.FilterSearch<TicketReminder>(filterobj);
                    }
                }
                iQServices = iQServices.OrderByField<TicketReminder>(sidx, sord);
                count = iQServices.Count();
                //total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                //if (count < (rows * page) && count != 0)
                //{
                //    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                //}

                CustomerDue = new
                {
                    total = count,
                    page = page,
                    records = count,
                    data = iQServices.ToList(),//.Paginate(page, rows),                                        
                    filter = filters
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(CustomerDue);
        }
        #endregion



        #region ::: Service Due Export Uday Kumar J B 14-11-2024:::
        /// <summary>
        /// Exporting Parts Grid
        /// </summary>
        public static async Task<object> Export(HelpDesk_Rpt_TicketReminderSelectList HelpDesk_Rpt_TicketReminderSelectobj, string connString, int LogException, string filter, string advnceFilter, string sidx, string sord)
        {
            string ScheduleTypeName = string.Empty;
            string All = string.Empty;
            try
            {
                All = CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "All").ToString();
                DateTime Fdate = HelpDesk_Rpt_TicketReminderSelectobj.Fromdate;
                DateTime Tdate = HelpDesk_Rpt_TicketReminderSelectobj.Todate;

                string BrandArray = Common.DecryptString(HelpDesk_Rpt_TicketReminderSelectobj.BrandArray);
                string ProductTypeArray = Common.DecryptString(HelpDesk_Rpt_TicketReminderSelectobj.ProductTypeArray);
                string ModelArray = Common.DecryptString(HelpDesk_Rpt_TicketReminderSelectobj.ModelArray);
                string CompanyIDs = Convert.ToString(HelpDesk_Rpt_TicketReminderSelectobj.CompanyIDs);
                string BranchIDs = Convert.ToString(HelpDesk_Rpt_TicketReminderSelectobj.BranchIDs);
                string BrandArrayNum = Common.DecryptString(HelpDesk_Rpt_TicketReminderSelectobj.BrandArrayNum);
                string ProductTypeArrayNum = Common.DecryptString(HelpDesk_Rpt_TicketReminderSelectobj.ProductTypeArrayNum);
                string ModelArrayNum = Common.DecryptString(HelpDesk_Rpt_TicketReminderSelectobj.ModelArrayNum);

                List<TicketReminder> wf = new List<TicketReminder>();
                IQueryable<TicketReminder> Result = GetCustomerDueService(HelpDesk_Rpt_TicketReminderSelectobj, connString, LogException, Fdate, Tdate, BrandArrayNum, ProductTypeArrayNum, ModelArrayNum, CompanyIDs, BranchIDs);


                if (!string.IsNullOrEmpty(filter) && filter != "null" && filter != "undefined")
                {
                    Filters filterobj = JObject.Parse(Common.DecryptString(Uri.EscapeDataString(filter))).ToObject<Filters>();
                    if (filterobj.rules.Any())
                    {
                        Result = Result.FilterSearch<TicketReminder>(filterobj);
                    }
                }

                Result = Result.OrderByField<TicketReminder>(sidx.ToString(), sord.ToString());

                wf = Result.ToList();
                int cnt = wf.Count();

                DataTable data = new DataTable();
                data.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "Region").ToString());
                data.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "CompanyName").ToString());
                data.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "BranchName").ToString());
                data.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "TicketNumber").ToString());
                data.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "Brand").ToString());
                data.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "Producttype").ToString());
                data.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "model").ToString());
                data.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "serialnumber").ToString());
                data.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "customername").ToString());
                data.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "ContactPerson").ToString());
                data.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "CustomerContactPhone").ToString());
                data.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "ContactPersonEmail").ToString());
                data.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "CallDescription").ToString());
                data.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "AssignedTo").ToString());
                data.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "FollowupDescription").ToString());
                data.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "StartDate").ToString());
                data.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "EndDate").ToString());
                data.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "Mode").ToString());
                data.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "status").ToString());
                data.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "Remarks").ToString());

                DataTable dataAlignment = new DataTable();
                dataAlignment.Columns.Add("Region");
                dataAlignment.Columns.Add("CompanyName");
                dataAlignment.Columns.Add("BranchName");
                dataAlignment.Columns.Add("TicketNumber");
                dataAlignment.Columns.Add("Brand");
                dataAlignment.Columns.Add("producttypename");
                dataAlignment.Columns.Add("model");
                dataAlignment.Columns.Add("serialnumber");
                dataAlignment.Columns.Add("customername");
                dataAlignment.Columns.Add("ContactPerson");
                dataAlignment.Columns.Add("CustomerContactPhone");
                dataAlignment.Columns.Add("ContactPersonEmail");
                dataAlignment.Columns.Add("CallDescription");
                dataAlignment.Columns.Add("AssignedTo");
                dataAlignment.Columns.Add("FollowupDescription");
                dataAlignment.Columns.Add("StartDate");
                dataAlignment.Columns.Add("EndDate");
                dataAlignment.Columns.Add("Mode");
                dataAlignment.Columns.Add("status");
                dataAlignment.Columns.Add("Remarks");
                dataAlignment.Rows.Add(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0);

                for (int i = 0; i < cnt; i++)
                {
                    data.Rows.Add(wf.ElementAt(i).RegionName, wf.ElementAt(i).CompanyName, wf.ElementAt(i).BranchName, wf.ElementAt(i).TicketNumber, wf.ElementAt(i).BrandName, wf.ElementAt(i).ProductTypeName, wf.ElementAt(i).ModelName, wf.ElementAt(i).SerialNumber, wf.ElementAt(i).CustomerName, wf.ElementAt(i).CustomerContact, wf.ElementAt(i).CustomerContactPhone, wf.ElementAt(i).CustomerContactEmail, wf.ElementAt(i).CallDescription, wf.ElementAt(i).AssignedTo, wf.ElementAt(i).FollowupDescription, wf.ElementAt(i).StartDate, wf.ElementAt(i).EndDate, wf.ElementAt(i).Mode, wf.ElementAt(i).Status, wf.ElementAt(i).Remarks);
                }
                DataTable DateRange = new DataTable();
                DateRange.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "fromdate").ToString());
                DateRange.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "todate").ToString());
                DateRange.Rows.Add(Fdate, Tdate);

                DataSet MultipleSelection = new DataSet();

                DataTable Brand = new DataTable();
                DataTable ProductType = new DataTable();
                DataTable Model = new DataTable();

                Brand.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "Brand").ToString());
                ProductType.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "Producttype").ToString());
                Model.Columns.Add(CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "model").ToString());

                Brand.Rows.Add(BrandArray.TrimEnd(new char[] { ',', ' ' }) == "" ? All : BrandArray.TrimEnd(new char[] { ',', ' ' }));
                ProductType.Rows.Add(ProductTypeArray.TrimEnd(new char[] { ',', ' ' }) == "" ? All : ProductTypeArray.TrimEnd(new char[] { ',', ' ' }));
                Model.Rows.Add(ModelArray.TrimEnd(new char[] { ',', ' ' }) == "" ? All : ModelArray.TrimEnd(new char[] { ',', ' ' }));

                MultipleSelection.Tables.Add(Brand);
                MultipleSelection.Tables.Add(ProductType);
                MultipleSelection.Tables.Add(Model);
                ExportReportExport1List exportReport1 = new ExportReportExport1List
                {
                    FileName = "ResponseTimeReport",
                    Branch = Convert.ToString(HelpDesk_Rpt_TicketReminderSelectobj.Branch),
                    Company_ID = HelpDesk_Rpt_TicketReminderSelectobj.Company_ID,
                    UserCulture = HelpDesk_Rpt_TicketReminderSelectobj.UserCulture,
                    dt = data, // You can populate this with actual data as needed
                    exprtType = HelpDesk_Rpt_TicketReminderSelectobj.exprtType, // Set an appropriate type for export (e.g., 1 for PDF, 2 for Excel, etc.)
                    Header = CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "ResponseTimeReport").ToString(),
                    Options = DateRange, // Populate this with your report options
                    selection = MultipleSelection, // Add selection-related data here
                    Alignment = dataAlignment // Define alignment details for table columns
                };
                var result = await ReportExport1.Export(exportReport1, connString, LogException);
                return result.Value;

                //  ReportExport1.Export(HelpDesk_Rpt_TicketReminderSelectobj.exprtType, data, DateRange, MultipleSelection, dataAlignment, "TicketReminder", CommonFunctionalities.GetResourceString(HelpDesk_Rpt_TicketReminderSelectobj.UserCulture.ToString(), "TicketReminder").ToString());
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return null;
        }
        #endregion


        #region ::: SendReminder Uday Kumar J B 14-11-2024:::
        /// <summary>
        /// Send Reminder with email
        /// </summary> 
        public static IActionResult SendReminder(HelpDesk_Rpt_TicketReminderSendReminderlList HelpDesk_Rpt_TicketReminderSendReminderlobj, string connString, int LogException)
        {
            string emailStatus = "Email has been sent to contact person";
            try
            {
                int companyID = HelpDesk_Rpt_TicketReminderSendReminderlobj.Company_ID;
                string Languagecode = "";

                if (HelpDesk_Rpt_TicketReminderSendReminderlobj.UserLanguageCode.ToString() != HelpDesk_Rpt_TicketReminderSendReminderlobj.GeneralLanguageCode.ToString())
                {
                    Languagecode = HelpDesk_Rpt_TicketReminderSendReminderlobj.UserLanguageCode.ToString();
                }

                JObject jobj = JObject.Parse(HelpDesk_Rpt_TicketReminderSendReminderlobj.Data.ToString());
                int count = jobj["rows"].Count();

                // Convert Party_ID and FollowUpDetail_ID to integer-compatible format in the JSON string
                foreach (var row in jobj["rows"])
                {
                    if (row["Party_ID"] != null && row["Party_ID"].Type == JTokenType.String)
                    {
                        row["Party_ID"] = int.Parse(row["Party_ID"].ToString());
                    }

                    if (row["FollowUpDetail_ID"] != null && row["FollowUpDetail_ID"].Type == JTokenType.String)
                    {
                        row["FollowUpDetail_ID"] = int.Parse(row["FollowUpDetail_ID"].ToString());
                    }
                }

                // Deserialize the corrected JSON string to the List<TicketReminder>
                List<TicketReminder> slist = JsonSerializer.Deserialize<List<TicketReminder>>(jobj["rows"].ToString());

                // Process Followupdata
                JObject jobjcnt = JObject.Parse(HelpDesk_Rpt_TicketReminderSendReminderlobj.Followupdata.ToString());
                int countcnt = jobjcnt["rows"].Count();


                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    for (int i = 0; i < slist.Count; i++)
                    {
                        // Step 1: Get ContactPersonDetails for each TicketReminder using the sp_GetContactPersonDetails procedure
                        List<ContactPersonDetails> slistcntdtls = GetContactPersonDetails(connection, slist[i].Party_ID, slist[i].FollowUpDetail_ID);

                        if (slistcntdtls.Count > 0)
                        {
                            for (int j = 0; j < slistcntdtls.Count; j++)
                            {
                                // Prepare email content using EmailTemplateController
                                StringBuilder[] ETRes = CommonMethodForEmailandSMS(connString, LogException,
                                    "Ticket Reminder", companyID, Languagecode, 0,
                                    slist[i].RegionName, slist[i].CompanyName, slist[i].BranchName,
                                    slist[i].TicketNumber, slist[i].BrandName, slist[i].ProductTypeName,
                                    slist[i].ModelName, slist[i].SerialNumber, slist[i].CustomerName,
                                    slistcntdtls[j].ContactPersonName, slistcntdtls[j].ContactPersonMobile,
                                    slistcntdtls[j].ContactPersonEmail, slist[i].CallDescription,
                                    slist[i].AssignedTo, slist[i].FollowupDescription, slist[i].StartDate,
                                    slist[i].EndDate, slist[i].Mode, slist[i].Status, slist[i].Remarks);

                                string emailSubject = ETRes[0].ToString();
                                string emailBody = ETRes[1].ToString();
                                string emailTO = slistcntdtls[j].ContactPersonEmail;

                                if (!string.IsNullOrEmpty(emailTO))
                                {
                                    // Step 2: Insert email into GNM_Email using the sp_InsertEmail procedure
                                    InsertEmail(connection, emailSubject, emailBody, emailTO);
                                }
                            }
                        }
                        else
                        {
                            // Prepare email content using EmailTemplateController
                            StringBuilder[] ETRes = CommonMethodForEmailandSMS(connString, LogException,
                                "Ticket Reminder", companyID, Languagecode, 0,
                                slist[i].RegionName, slist[i].CompanyName, slist[i].BranchName,
                                slist[i].TicketNumber, slist[i].BrandName, slist[i].ProductTypeName,
                                slist[i].ModelName, slist[i].SerialNumber, slist[i].CustomerName,
                                slist[i].CustomerContact, slist[i].CustomerContactPhone,
                                slist[i].CustomerContactEmail, slist[i].CallDescription,
                                slist[i].AssignedTo, slist[i].FollowupDescription, slist[i].StartDate,
                                slist[i].EndDate, slist[i].Mode, slist[i].Status, slist[i].Remarks);

                            string emailSubject = ETRes[0].ToString();
                            string emailBody = ETRes[1].ToString();
                            string emailTO = slist[i].CustomerContactEmail;

                            if (!string.IsNullOrEmpty(emailTO))
                            {
                                // Step 2: Insert email into GNM_Email using the sp_InsertEmail procedure
                                InsertEmail(connection, emailSubject, emailBody, emailTO);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(emailStatus);
        }

        private static List<ContactPersonDetails> GetContactPersonDetails(SqlConnection connection, int partyID, int followUpID)
        {
            List<ContactPersonDetails> contactPersonDetails = new List<ContactPersonDetails>();

            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetContactPersonDetails", connection))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@PartyID", partyID);
                cmd.Parameters.AddWithValue("@FollowUpID", followUpID);

                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        contactPersonDetails.Add(new ContactPersonDetails
                        {
                            ContactPersonName = reader["PartyContactPerson_Name"].ToString(),
                            ContactPersonMobile = reader["PartyContactPerson_Mobile"].ToString(),
                            ContactPersonEmail = reader["PartyContactPerson_Email"].ToString()
                        });
                    }
                }
            }

            return contactPersonDetails;
        }

        private static void InsertEmail(SqlConnection connection, string emailSubject, string emailBody, string emailTo)
        {
            using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_InsertEmail", connection))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@EmailSubject", emailSubject);
                cmd.Parameters.AddWithValue("@EmailBody", emailBody);
                cmd.Parameters.AddWithValue("@EmailTo", emailTo);
                cmd.Parameters.AddWithValue("@EmailQueueDate", DateTime.Now);

                cmd.ExecuteNonQuery();
            }
        }
        #endregion


        #region :::  CommonMethodForEmailandSMS Uday Kumar J B 13-08-2024:::
        /// <summary>
        /// Purpose : To fetch Email Subject, Boday & SMS
        /// </summary> 
        /// 

        public static StringBuilder[] CommonMethodForEmailandSMS(string connString, int LogException, string TemplateCode, int CompanyId, string LanguageCode, int BranchId = 0, string p1 = "", string p2 = "", string p3 = "", string p4 = "", string p5 = "", string p6 = "", string p7 = "", string p8 = "", string p9 = "", string p10 = "", string p11 = "", string p12 = "", string p13 = "", string p14 = "", string p15 = "", string p16 = "", string p17 = "", string p18 = "", string p19 = "", string p20 = "")
        {
            StringBuilder[] Result = new StringBuilder[5];

            try
            {
                using (var connection = new SqlConnection(connString))
                {
                    connection.Open();

                    using (var command = new SqlCommand("Up_Sel_Am_Erp_SelectEmailTemplateGmail", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@TemplateCode", TemplateCode);
                        command.Parameters.AddWithValue("@CompanyId", CompanyId);
                        command.Parameters.AddWithValue("@LanguageCode", LanguageCode);
                        command.Parameters.AddWithValue("@BranchId", BranchId);
                        command.Parameters.AddWithValue("@Param1", p1);
                        command.Parameters.AddWithValue("@Param2", p2);
                        command.Parameters.AddWithValue("@Param3", p3);
                        command.Parameters.AddWithValue("@Param4", p4);
                        command.Parameters.AddWithValue("@Param5", p5);
                        command.Parameters.AddWithValue("@Param6", p6);
                        command.Parameters.AddWithValue("@Param7", p7);
                        command.Parameters.AddWithValue("@Param8", p8);
                        command.Parameters.AddWithValue("@Param9", p9);
                        command.Parameters.AddWithValue("@Param10", p10);
                        command.Parameters.AddWithValue("@Param11", p11);
                        command.Parameters.AddWithValue("@Param12", p12);
                        command.Parameters.AddWithValue("@Param13", p13);
                        command.Parameters.AddWithValue("@Param14", p14);
                        command.Parameters.AddWithValue("@Param15", p15);
                        command.Parameters.AddWithValue("@Param16", p16);
                        command.Parameters.AddWithValue("@Param17", p17);
                        command.Parameters.AddWithValue("@Param18", p18);
                        command.Parameters.AddWithValue("@Param19", p19);
                        command.Parameters.AddWithValue("@Param20", p20);


                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                Result[0] = new StringBuilder(reader["Subject"].ToString());
                                Result[1] = new StringBuilder(reader["Body"].ToString());
                                Result[2] = new StringBuilder(reader["SMS"].ToString());
                                Result[3] = new StringBuilder(reader["BCC"].ToString());
                                Result[4] = new StringBuilder(reader["CC"].ToString());
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                throw ex;
            }

            return Result;
        }
        #endregion


        #region ::: getPartyContactPersonDetails Uday Kumar J B 14-11-2024:::
        /// <summary>
        /// get Party Contact Person Details
        /// </summary>
        public static IActionResult getPartyContactPersonDetails(HelpDesk_Rpt_TicketRemindergetPartyContactPersonDetailslList HelpDesk_Rpt_TicketRemindergetPartyContactPersonDetailslobj, string connString, int LogException, bool _search, string filters, string Query, bool advnce, string sidx, string sord, int page, int rows)
        {
            var jsonres = default(dynamic);
            try
            {
                int BranchID = Convert.ToInt32(HelpDesk_Rpt_TicketRemindergetPartyContactPersonDetailslobj.Branch);
                int UserLang = Convert.ToInt32(HelpDesk_Rpt_TicketRemindergetPartyContactPersonDetailslobj.UserLanguageID);
                int GeneralLang = Convert.ToInt32(HelpDesk_Rpt_TicketRemindergetPartyContactPersonDetailslobj.GeneralLanguageID);
                int partyid = Convert.ToInt32(HelpDesk_Rpt_TicketRemindergetPartyContactPersonDetailslobj.PartyID);
                int FollowUpID = Convert.ToInt32(HelpDesk_Rpt_TicketRemindergetPartyContactPersonDetailslobj.FollowUpID);

                // Fetch contact person details for the given party
                List<GNM_PartyContactPersonDetails> prtcntdetails = new List<GNM_PartyContactPersonDetails>();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetPartyContactPersonDetails", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@PartyID", partyid);

                    conn.Open();
                    SqlDataReader reader = cmd.ExecuteReader();
                    while (reader.Read())
                    {
                        prtcntdetails.Add(new GNM_PartyContactPersonDetails
                        {
                            Party_ID = Convert.ToInt32(reader["Party_ID"]),
                            PartyContactPerson_Name = reader["PartyContactPerson_Name"].ToString(),
                            PartyContactPerson_Email = reader["PartyContactPerson_Email"].ToString(),
                            PartyContactPerson_Mobile = reader["PartyContactPerson_Mobile"].ToString(),
                            Party_IsDefaultContact = Convert.ToBoolean(reader["Party_IsDefaultContact"])
                        });
                    }
                    reader.Close();
                }

                // Now fetch party name based on the user language
                string partyName = string.Empty;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetPartyNameByLanguage", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@PartyID", partyid);
                    cmd.Parameters.AddWithValue("@LanguageID", UserLang);
                    cmd.Parameters.AddWithValue("@GeneralLanguageID", GeneralLang);

                    conn.Open();
                    SqlDataReader reader = cmd.ExecuteReader();
                    if (reader.Read())
                    {
                        partyName = reader["Party_Name"].ToString();
                    }
                    reader.Close();
                }

                // Prepare the JSON result similar to LINQ
                jsonres = new
                {
                    total = prtcntdetails.Count,
                    rows = prtcntdetails.Select(a => new
                    {
                        CustomerName = partyName,
                        ContactPersonName = a.PartyContactPerson_Name,
                        ContactPersonEmail = a.PartyContactPerson_Email,
                        ContactPersonMobile = a.PartyContactPerson_Mobile,
                        ChkContactPerson = (a.Party_IsDefaultContact) ? "<input type='checkbox' checked='checked' class='SelectContactPerson' />" : "<input type='checkbox' class='SelectContactPerson' />",
                        Party_ID = a.Party_ID,
                        FollowUpID = FollowUpID
                    }).ToList(),
                    records = prtcntdetails.Count
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonres);
        }
        #endregion



        #region ::: HelpDesk_Rpt_TicketReminder List and obj classes Uday Kumar J B 14-11-2024:::
        /// <summary>
        /// HelpDesk_Rpt_TicketReminder
        /// </summary>
        public class HelpDesk_Rpt_TicketRemindergetPartyContactPersonDetailslList
        {
            public int Branch { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int PartyID { get; set; }
            public int FollowUpID { get; set; }

        }

        public class HelpDesk_Rpt_TicketReminderSendReminderlList
        {

            public int Company_ID { get; set; }
            public string UserLanguageCode { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string Data { get; set; }
            public string Followupdata { get; set; }


        }


        public class HelpDesk_Rpt_TicketReminderSelectList
        {
            public DateTime Fromdate { get; set; }
            public DateTime Todate { get; set; }
            public string BrandArray { get; set; }
            public string ProductTypeArray { get; set; }
            public string ModelArray { get; set; }
            public string CompanyIDs { get; set; }
            public string BranchIDs { get; set; }
            public int Branch { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Language_ID { get; set; }
            public string UserCulture { get; set; }
            public int exprtType { get; set; }
            public string BrandArrayNum { get; set; }
            public string ProductTypeArrayNum { get; set; }
            public string ModelArrayNum { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filter { get; set; }
            public string advanceFilter { get; set; }
        }

        public class HelpDesk_Rpt_TicketReminderLoadModelList
        {

            public string MtypeID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Company_ID { get; set; }

        }


        public class HelpDesk_Rpt_TicketReminderLoadProductTypeList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string BrandID { get; set; }
        }
        #endregion



        #region :::TicketReminder Uday kumar J B 14-11-2024 :::
        /// <summary>
        ///  Public class to declare the properties
        /// </summary>   
        public class TicketReminder
        {
            public int Company_ID { get; set; }
            public int Branch_ID { get; set; }
            public int Product_ID { get; set; }
            public int Party_ID { get; set; }
            public int FollowUpDetail_ID { get; set; }
            public string TicketNumber { get; set; }
            public string BranchName { get; set; }
            public string PartyName { get; set; }
            public string CustomerName { get; set; }
            public string CustomerContact { get; set; }
            public string CustomerContactPhone { get; set; }
            public string CustomerContactEmail { get; set; }
            public string ProductTypeName { get; set; }
            public string ModelName { get; set; }
            public string BrandName { get; set; }
            public string SerialNumber { get; set; }
            public string CompanyIDs { get; set; }
            public string CompanyName { get; set; }
            public string RegionName { get; set; }
            public string SendReminder { get; set; }
            public string CallDescription { get; set; }
            public string AssignedTo { get; set; }
            public string FollowupDescription { get; set; }
            public string StartDate { get; set; }
            public string EndDate { get; set; }
            public string Mode { get; set; }
            public string Status { get; set; }
            public string Remarks { get; set; }
            public List<ContactPersonDetails> CntDetails { get; set; }
        }
        public class wfsteps
        {
            public int WFStepStatus_ID { get; set; }
        }
        public class ContactPersonDetails
        {
            public string CustomerName { get; set; }
            public string ContactPersonName { get; set; }
            public string ContactPersonEmail { get; set; }
            public string ContactPersonMobile { get; set; }
            public int PartyID { get; set; }
            public int FollowUpID { get; set; }
        }
        #endregion


    }
}
