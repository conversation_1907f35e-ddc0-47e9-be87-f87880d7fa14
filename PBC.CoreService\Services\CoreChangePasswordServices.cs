using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Mime;
using System.Text;
using System.Text.Json;
using PBC.CoreService.Utilities.DTOs;

namespace PBC.CoreService.Services
{
    public class CoreChangePasswordServices : ICoreChangePasswordServices
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<CoreChangePasswordServices> _logger;
        private readonly IConfiguration _configuration;

        public CoreChangePasswordServices(HttpClient httpClient, ILogger<CoreChangePasswordServices> logger, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _logger = logger;
            _configuration = configuration;
        }

        #region CheckPasswordWithDB
        /// <summary>
        /// CheckPasswordWithDB using PBC.UtilityService
        /// </summary>
        /// <param name="hashedPwdFromDatabase"></param>
        /// <param name="userEnteredPassword"></param>
        /// <returns></returns>
        private async Task<bool> CheckPasswordWithDB(string hashedPwdFromDatabase, string userEnteredPassword)
        {
            try
            {
                var request = new
                {
                    HashedPwdFromDatabase = hashedPwdFromDatabase,
                    UserEnteredPassword = userEnteredPassword
                };

                var json = System.Text.Json.JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, MediaTypeNames.Application.Json);

                string utilityServiceUrl = _configuration["ServiceUrls:UtilitiesService"] ?? "http://localhost:5003";

                // Set timeout for HTTP request
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
                var response = await _httpClient.PostAsync($"{utilityServiceUrl}/api/bcrypt/check-password", content, cts.Token);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = System.Text.Json.JsonSerializer.Deserialize<UtilityServiceBCryptResponse<bool>>(responseContent, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                    return result?.Success == true && result.Data == true;
                }

                return false;
            }
            catch (HttpRequestException ex)
            {
                _logger.LogWarning(ex, "PBC.UtilityService is not available. Please start the service on {UtilityServiceUrl}", _configuration["ServiceUrls:UtilitiesService"]);
                return false;
            }
            catch (TaskCanceledException ex)
            {
                _logger.LogWarning(ex, "Timeout calling PBC.UtilityService. Service may be slow or unavailable.");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking password with database");
                return false;
            }
        }
        #endregion

        #region DecryptString Helper
        /// <summary>
        /// Decrypt string using PBC.UtilityService
        /// </summary>
        /// <param name="encryptedString"></param>
        /// <returns></returns>
        private async Task<string> DecryptStringAsync(string encryptedString)
        {
            try
            {
                var request = new
                {
                    EncryptedString = encryptedString
                };

                var json = System.Text.Json.JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, MediaTypeNames.Application.Json);

                string utilityServiceUrl = _configuration["ServiceUrls:UtilitiesService"] ?? "http://localhost:5003";

                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
                var response = await _httpClient.PostAsync($"{utilityServiceUrl}/api/extensionmethods/decrypt-string", content, cts.Token);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = System.Text.Json.JsonSerializer.Deserialize<UtilityServiceExtensionMethodsResponse<string>>(responseContent, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                    return result?.Success == true ? result.Data ?? string.Empty : string.Empty;
                }

                return string.Empty;
            }
            catch (HttpRequestException ex)
            {
                _logger.LogWarning(ex, "PBC.UtilityService is not available for decryption. Please start the service on {UtilityServiceUrl}", _configuration["ServiceUrls:UtilitiesService"]);
                return string.Empty;
            }
            catch (TaskCanceledException ex)
            {
                _logger.LogWarning(ex, "Timeout calling PBC.UtilityService for decryption.");
                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error decrypting string");
                return string.Empty;
            }
        }
        #endregion

        #region GenerateDMSPassword Helper
        /// <summary>
        /// Generate DMS password using PBC.UtilityService
        /// </summary>
        /// <param name="userPassword"></param>
        /// <returns></returns>
        private async Task<string> GenerateDMSPassword(string userPassword)
        {
            try
            {
                var request = new
                {
                    UserPassword = userPassword
                };

                var json = System.Text.Json.JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, MediaTypeNames.Application.Json);

                string utilityServiceUrl = _configuration["ServiceUrls:UtilitiesService"] ?? "http://localhost:5003";

                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
                var response = await _httpClient.PostAsync($"{utilityServiceUrl}/api/bcrypt/generate-dms-password", content, cts.Token);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = System.Text.Json.JsonSerializer.Deserialize<UtilityServiceBCryptResponse<string>>(responseContent, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                    return result?.Success == true ? result.Data ?? string.Empty : string.Empty;
                }

                return string.Empty;
            }
            catch (HttpRequestException ex)
            {
                _logger.LogWarning(ex, "PBC.UtilityService is not available for password generation. Please start the service on {UtilityServiceUrl}", _configuration["ServiceUrls:UtilitiesService"]);
                return string.Empty;
            }
            catch (TaskCanceledException ex)
            {
                _logger.LogWarning(ex, "Timeout calling PBC.UtilityService for password generation.");
                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating DMS password");
                return string.Empty;
            }
        }
        #endregion

        #region ::: CheckOldPassword :::
        /// <summary>
        /// To CheckOldPassword
        /// </summary>
        public async Task<IActionResult> CheckOldPassword(CheckOldPasswordList Obj, string connString, int LogException)
        {
            int Count = 0;
            try
            {
                List<GNM_User> UserRow = null;
                int User_ID = Convert.ToInt32(Obj.User_ID);
                string hashedPwdFromDatabase = String.Empty;

                // Decrypt password using PBC.UtilityService
                string OldPassword = await DecryptStringAsync(Obj.OldPassword);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_Get_AM_ERP_CheckOldPassword_ChangePassword";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@User_ID", User_ID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            hashedPwdFromDatabase = command.ExecuteScalar()?.ToString();
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log exception using PBC.UtilityService logging if needed
                        if (LogException == 1)
                        {
                            _logger.LogError(ex, "Error executing CheckOldPassword stored procedure");
                        }
                    }
                    finally
                    {
                        command?.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                bool PasswrdCheck = await CheckPasswordWithDB(hashedPwdFromDatabase, OldPassword);
                if (PasswrdCheck)
                {
                    Count = 1;
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    _logger.LogError(ex, "Error in CheckOldPassword method");
                }
            }
            return new JsonResult(Count);
        }
        #endregion

        #region ::: ChangePassword :::
        /// <summary>
        /// To Change Password
        /// </summary>
        public async Task ChangePassword(ChangePasswordList Obj, string connString, int LogException)
        {
            try
            {
                int User_ID = Convert.ToInt32(Obj.User_ID);

                var jObj = JObject.Parse(Obj.data);

                var jTR = new JTokenReader(jObj["OldPassword"]);
                jTR.Read();

                jTR = new JTokenReader(jObj["NewPassword"]);
                jTR.Read();

                // Decrypt and generate new password using PBC.UtilityService
                string decryptedNewPassword = await DecryptStringAsync(jTR.Value.ToString());
                string NewPassword = await GenerateDMSPassword(decryptedNewPassword);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_ChangePassword_AM_ERP_ChangePassword_ChangePassword";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;

                            command.Parameters.AddWithValue("@User_ID", User_ID);
                            command.Parameters.AddWithValue("@NewPassword", NewPassword);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            command.ExecuteNonQuery();
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log exception
                        if (LogException == 1)
                        {
                            _logger.LogError(ex, "Error executing ChangePassword stored procedure");
                        }
                    }
                    finally
                    {
                        command?.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                // Note: GPS details insertion commented out as it requires additional migration
                // gbl.InsertGPSDetails(Convert.ToInt32(Obj.Company_ID.ToString()), Convert.ToInt32(Obj.Branch), User_ID, Common.GetObjectID("CoreChangePassword"), UpdateRow.User_ID, 0, 0, "Changed Password", false, Convert.ToInt32(Obj.MenuID), Convert.ToDateTime(Obj.LoggedINDateTime));
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    _logger.LogError(ex, "Error in ChangePassword method");
                }
            }
        }
        #endregion
    }
}
