
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Resources;
using System.Globalization;
using System.Text;
using System.Security.Cryptography;
using System.Net;

namespace PBC.WorkflowService.Utilities
{
    public class Utilities
    {
        #region
        /// <summary>
        /// Calculate Total Pages
        /// </summary>
        /// <param name="numberOfRecords"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public static int CalculateTotalPages(long numberOfRecords, Int32 pageSize)
        {
            long result;
            int totalPages;

            Math.DivRem(numberOfRecords, pageSize, out result);

            if (result > 0)
                totalPages = (int)((numberOfRecords / pageSize)) + 1;
            else
                totalPages = (int)(numberOfRecords / pageSize);

            return totalPages;
        }
         #endregion

        #region
        /// <summary>
        /// Check if date is a valid format
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        /// 
        public static Boolean IsDate(string date)
        {
            DateTime dateTime;
            return DateTime.TryParse(date, out dateTime);
        }
         #endregion

        #region
        /// <summary>
        /// IsNumeric
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public static Boolean IsNumeric(object entity)
        {
            if (entity == null) return false;

            int result;
            return int.TryParse(entity.ToString(), out result);
        }
         #endregion

        #region
        /// <summary>
        /// IsDouble
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public static Boolean IsDouble(object entity)
        {
            if (entity == null) return false;

            string e = entity.ToString();

            // Loop through all instances of the string 'text'.
            int count = 0;
            int i = 0;
            while ((i = e.IndexOf(".", i)) != -1)
            {
                i += ".".Length;
                count++;
            }
            if (count > 1) return false;

            e = e.Replace(".", "");

            int result;
            return int.TryParse(e, out result);
        }
         #endregion

        #region
        public static List<String> Message(string message)
        {
            List<String> returnMessage = new List<String>();
            returnMessage.Add(message);
            return returnMessage;
        }
         #endregion     

        #region 

        public string CalculateMD5Hash(string input)
        {
            // step 1, calculate MD5 hash from input
            MD5 md5 = System.Security.Cryptography.MD5.Create();
            byte[] inputBytes = System.Text.Encoding.ASCII.GetBytes(input);
            byte[] hash = md5.ComputeHash(inputBytes);

            // step 2, convert byte array to hex string
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < hash.Length; i++)
            {
                sb.Append(hash[i].ToString("x2"));
            }
            return sb.ToString();
        }
        #endregion

        #region ::: GetMonthName:::
        /// <summary>
        /// To Select Month Name based on Culture
        /// </summary>
        public static string GetMonthName(int ID)
        {
            string MonthName = string.Empty;
            try
            {
                // Use CultureInfo for month names instead of HttpContext
                var culture = CultureInfo.CurrentCulture;
                switch (ID)
                {
                    case 1:
                        // MonthName = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "January").ToString();
                        MonthName = culture.DateTimeFormat.GetMonthName(1);
                        break;
                    case 2:
                        // MonthName = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "February").ToString();
                        MonthName = culture.DateTimeFormat.GetMonthName(2);
                        break;
                    case 3:
                        // MonthName = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "March").ToString();
                        MonthName = culture.DateTimeFormat.GetMonthName(3);
                        break;
                    case 4:
                        // MonthName = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "April").ToString();
                        MonthName = culture.DateTimeFormat.GetMonthName(4);
                        break;
                    case 5:
                        // MonthName = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "May").ToString();
                        MonthName = culture.DateTimeFormat.GetMonthName(5);
                        break;
                    case 6:
                        // MonthName = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "June").ToString();
                        MonthName = culture.DateTimeFormat.GetMonthName(6);
                        break;
                    case 7:
                        // MonthName = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "July").ToString();
                        MonthName = culture.DateTimeFormat.GetMonthName(7);
                        break;
                    case 8:
                        // MonthName = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "August").ToString();
                        MonthName = culture.DateTimeFormat.GetMonthName(8);
                        break;
                    case 9:
                        MonthName = culture.DateTimeFormat.GetMonthName(9);
                        break;
                    case 10:
                        // MonthName = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "October").ToString();
                        MonthName = culture.DateTimeFormat.GetMonthName(10);
                        break;
                    case 11:
                        // MonthName = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "November").ToString();
                        MonthName = culture.DateTimeFormat.GetMonthName(11);
                        break;
                    case 12:
                        // MonthName = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "December").ToString();
                        MonthName = culture.DateTimeFormat.GetMonthName(12);
                        break;
                    default:
                        MonthName = string.Empty;
                        break;
                }
            }
            catch (Exception ex)
            {
                // Log exception if needed
            }
            return MonthName;
        }
        #endregion

        #region ::: GetPariority:::
        /// <summary>
        /// To Select Priority based on Culture
        /// </summary>
        public static string GetPriority(byte ID)
        {
            string Priority = string.Empty;

            try
            {
                // Use hardcoded values for now, can be enhanced with localization later
                switch (ID)
                {
                    case 1:
                        // Priority = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "low").ToString();
                        Priority = "Low";
                        break;
                    case 2:
                        // Priority = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "medium").ToString();
                        Priority = "Medium";
                        break;
                    case 3:
                        // Priority = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "high").ToString();
                        Priority = "High";
                        break;
                    case 4:
                        // Priority = HttpContext.GetGlobalResourceObject(HttpContext.Current.Session["UserCulture"].ToString(), "critical").ToString();
                        Priority = "Critical";
                        break;
                    default:
                        // Priority = string.Empty;
                        Priority = string.Empty;
                        break;
                }
            }
            catch (Exception ex)
            {
                // Log exception if needed
            }
            return Priority;
        }
        #endregion
     
    }
}