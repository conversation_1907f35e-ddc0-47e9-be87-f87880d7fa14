using PBC.UtilityService.Utilities.Models;
using PBC.UtilityService.Utilities.DTOs;
using System.Text;

namespace PBC.UtilityService.Services
{
    /// <summary>
    /// Service implementation for AdvanceFilter operations
    /// </summary>
    public class AdvanceFilterService : IAdvanceFilterService
    {
        private readonly ILogger<AdvanceFilterService> _logger;
        
        // In-memory storage for demonstration - replace with actual data persistence layer
        private static readonly List<AdvanceFilter> _filtersStore = new();
        private static readonly object _lock = new();
        private static int _nextId = 1;

        public AdvanceFilterService(ILogger<AdvanceFilterService> logger)
        {
            _logger = logger;
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<AdvanceFilterDto>> GetAllAsync()
        {
            _logger.LogInformation("Getting all advance filters");
            
            await Task.Delay(1); // Simulate async operation
            
            lock (_lock)
            {
                return _filtersStore.Select(MapToDto).ToList();
            }
        }

        /// <inheritdoc/>
        public async Task<AdvanceFilterDto?> GetByIdAsync(int id)
        {
            _logger.LogInformation("Getting advance filter for ID: {Id}", id);
            
            await Task.Delay(1); // Simulate async operation
            
            lock (_lock)
            {
                var filter = _filtersStore.FirstOrDefault(x => x.Id == id);
                return filter != null ? MapToDto(filter) : null;
            }
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<AdvanceFilterDto>> GetByNameAsync(string name)
        {
            _logger.LogInformation("Getting advance filters by name: {Name}", name);
            
            await Task.Delay(1); // Simulate async operation
            
            lock (_lock)
            {
                var filters = _filtersStore
                    .Where(x => x.Name != null && x.Name.Contains(name, StringComparison.OrdinalIgnoreCase))
                    .Select(MapToDto)
                    .ToList();
                
                return filters;
            }
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<AdvanceFilterDto>> GetActiveFiltersAsync()
        {
            _logger.LogInformation("Getting active advance filters");
            
            await Task.Delay(1); // Simulate async operation
            
            lock (_lock)
            {
                return _filtersStore
                    .Where(x => x.IsActive)
                    .Select(MapToDto)
                    .ToList();
            }
        }

        /// <inheritdoc/>
        public async Task<AdvanceFilterDto> CreateAsync(CreateAdvanceFilterRequest request)
        {
            _logger.LogInformation("Creating advance filter with name: {Name}", request.Name);
            
            await Task.Delay(1); // Simulate async operation
            
            var filter = new AdvanceFilter
            {
                Id = _nextId++,
                Name = request.Name,
                Description = request.Description,
                LogicalOperator = request.LogicalOperator,
                IsActive = request.IsActive,
                CreatedDate = DateTime.UtcNow,
                Rules = request.Rules.Select((rule, index) => new Rules
                {
                    Id = index + 1,
                    Field = rule.Field,
                    Data = rule.Data,
                    Operator = rule.Operator,
                    Condition = rule.Condition,
                    Order = rule.Order,
                    AdvanceFilterId = _nextId - 1
                }).ToList()
            };

            lock (_lock)
            {
                _filtersStore.Add(filter);
            }

            _logger.LogInformation("Successfully created advance filter with ID: {Id}", filter.Id);
            return MapToDto(filter);
        }

        /// <inheritdoc/>
        public async Task<AdvanceFilterDto?> UpdateAsync(int id, UpdateAdvanceFilterRequest request)
        {
            _logger.LogInformation("Updating advance filter for ID: {Id}", id);
            
            await Task.Delay(1); // Simulate async operation
            
            lock (_lock)
            {
                var filter = _filtersStore.FirstOrDefault(x => x.Id == id);
                if (filter == null)
                {
                    _logger.LogWarning("Advance filter not found for ID: {Id}", id);
                    return null;
                }

                // Update only provided values
                if (request.Name != null)
                    filter.Name = request.Name;
                if (request.Description != null)
                    filter.Description = request.Description;
                if (request.LogicalOperator != null)
                    filter.LogicalOperator = request.LogicalOperator;
                if (request.IsActive.HasValue)
                    filter.IsActive = request.IsActive.Value;
                
                filter.ModifiedDate = DateTime.UtcNow;

                // Update rules if provided
                if (request.Rules != null)
                {
                    filter.Rules = request.Rules.Select((rule, index) => new Rules
                    {
                        Id = index + 1,
                        Field = rule.Field,
                        Data = rule.Data,
                        Operator = rule.Operator,
                        Condition = rule.Condition,
                        Order = rule.Order,
                        AdvanceFilterId = id
                    }).ToList();
                }

                _logger.LogInformation("Successfully updated advance filter for ID: {Id}", id);
                return MapToDto(filter);
            }
        }

        /// <inheritdoc/>
        public async Task<bool> DeleteAsync(int id)
        {
            _logger.LogInformation("Deleting advance filter for ID: {Id}", id);
            
            await Task.Delay(1); // Simulate async operation
            
            lock (_lock)
            {
                var filter = _filtersStore.FirstOrDefault(x => x.Id == id);
                if (filter == null)
                {
                    _logger.LogWarning("Advance filter not found for ID: {Id}", id);
                    return false;
                }

                _filtersStore.Remove(filter);
                _logger.LogInformation("Successfully deleted advance filter for ID: {Id}", id);
                return true;
            }
        }

        /// <inheritdoc/>
        public async Task<bool> ExistsAsync(int id)
        {
            _logger.LogInformation("Checking if advance filter exists for ID: {Id}", id);
            
            await Task.Delay(1); // Simulate async operation
            
            lock (_lock)
            {
                return _filtersStore.Any(x => x.Id == id);
            }
        }

        /// <inheritdoc/>
        public async Task<FilterValidationResponse> ValidateAndGenerateSqlAsync(int id)
        {
            _logger.LogInformation("Validating and generating SQL for filter ID: {Id}", id);
            
            var filter = await GetByIdAsync(id);
            if (filter == null)
            {
                return new FilterValidationResponse
                {
                    IsValid = false,
                    ValidationErrors = new List<string> { $"Filter with ID {id} not found" }
                };
            }

            return await ValidateAndGenerateSqlInternalAsync(filter.Rules, filter.LogicalOperator ?? "AND");
        }

        /// <inheritdoc/>
        public async Task<FilterValidationResponse> ValidateAndGenerateSqlAsync(CreateAdvanceFilterRequest request)
        {
            _logger.LogInformation("Validating and generating SQL for filter request");
            
            var rules = request.Rules.Select(r => new RulesDto
            {
                Field = r.Field,
                Data = r.Data,
                Operator = r.Operator,
                Condition = r.Condition,
                Order = r.Order
            }).ToList();

            return await ValidateAndGenerateSqlInternalAsync(rules, request.LogicalOperator ?? "AND");
        }

        /// <inheritdoc/>
        public async Task<AdvanceFilterDto?> DuplicateAsync(int id, string newName)
        {
            _logger.LogInformation("Duplicating advance filter ID: {Id} with new name: {NewName}", id, newName);
            
            var originalFilter = await GetByIdAsync(id);
            if (originalFilter == null)
            {
                _logger.LogWarning("Original advance filter not found for ID: {Id}", id);
                return null;
            }

            var duplicateRequest = new CreateAdvanceFilterRequest
            {
                Name = newName,
                Description = $"Copy of {originalFilter.Name}",
                LogicalOperator = originalFilter.LogicalOperator,
                IsActive = originalFilter.IsActive,
                Rules = originalFilter.Rules.Select(r => new CreateRulesRequest
                {
                    Field = r.Field,
                    Data = r.Data,
                    Operator = r.Operator,
                    Condition = r.Condition,
                    Order = r.Order
                }).ToList()
            };

            return await CreateAsync(duplicateRequest);
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<RulesDto>> GetRulesByFilterIdAsync(int filterId)
        {
            _logger.LogInformation("Getting rules for filter ID: {FilterId}", filterId);
            
            var filter = await GetByIdAsync(filterId);
            return filter?.Rules ?? new List<RulesDto>();
        }

        /// <inheritdoc/>
        public async Task<AdvanceFilterDto?> AddRuleAsync(int filterId, CreateRulesRequest rule)
        {
            _logger.LogInformation("Adding rule to filter ID: {FilterId}", filterId);
            
            await Task.Delay(1); // Simulate async operation
            
            lock (_lock)
            {
                var filter = _filtersStore.FirstOrDefault(x => x.Id == filterId);
                if (filter == null)
                {
                    _logger.LogWarning("Advance filter not found for ID: {FilterId}", filterId);
                    return null;
                }

                var newRule = new Rules
                {
                    Id = filter.Rules.Count + 1,
                    Field = rule.Field,
                    Data = rule.Data,
                    Operator = rule.Operator,
                    Condition = rule.Condition,
                    Order = rule.Order,
                    AdvanceFilterId = filterId
                };

                filter.Rules.Add(newRule);
                filter.ModifiedDate = DateTime.UtcNow;

                _logger.LogInformation("Successfully added rule to filter ID: {FilterId}", filterId);
                return MapToDto(filter);
            }
        }

        /// <inheritdoc/>
        public async Task<AdvanceFilterDto?> RemoveRuleAsync(int filterId, int ruleId)
        {
            _logger.LogInformation("Removing rule ID: {RuleId} from filter ID: {FilterId}", ruleId, filterId);
            
            await Task.Delay(1); // Simulate async operation
            
            lock (_lock)
            {
                var filter = _filtersStore.FirstOrDefault(x => x.Id == filterId);
                if (filter == null)
                {
                    _logger.LogWarning("Advance filter not found for ID: {FilterId}", filterId);
                    return null;
                }

                var rule = filter.Rules.FirstOrDefault(r => r.Id == ruleId);
                if (rule == null)
                {
                    _logger.LogWarning("Rule not found for ID: {RuleId} in filter ID: {FilterId}", ruleId, filterId);
                    return null;
                }

                filter.Rules.Remove(rule);
                filter.ModifiedDate = DateTime.UtcNow;

                _logger.LogInformation("Successfully removed rule ID: {RuleId} from filter ID: {FilterId}", ruleId, filterId);
                return MapToDto(filter);
            }
        }

        /// <summary>
        /// Internal method to validate rules and generate SQL
        /// </summary>
        private async Task<FilterValidationResponse> ValidateAndGenerateSqlInternalAsync(IEnumerable<RulesDto> rules, string logicalOperator)
        {
            await Task.Delay(1); // Simulate async operation
            
            var response = new FilterValidationResponse();
            var sqlBuilder = new StringBuilder();
            var parameters = new Dictionary<string, object>();
            var paramCounter = 1;

            var validOperators = new[] { "=", "!=", "<>", "<", ">", "<=", ">=", "LIKE", "NOT LIKE", "IN", "NOT IN", "IS NULL", "IS NOT NULL" };
            var validConditions = new[] { "AND", "OR" };

            foreach (var rule in rules.OrderBy(r => r.Order))
            {
                // Validate field
                if (string.IsNullOrWhiteSpace(rule.Field))
                {
                    response.ValidationErrors.Add($"Field is required for rule at order {rule.Order}");
                    continue;
                }

                // Validate operator
                if (!validOperators.Contains(rule.Operator?.ToUpper()))
                {
                    response.ValidationErrors.Add($"Invalid operator '{rule.Operator}' for field '{rule.Field}'");
                    continue;
                }

                // Build SQL clause
                if (sqlBuilder.Length > 0 && !string.IsNullOrWhiteSpace(rule.Condition))
                {
                    if (validConditions.Contains(rule.Condition.ToUpper()))
                    {
                        sqlBuilder.Append($" {rule.Condition.ToUpper()} ");
                    }
                    else
                    {
                        sqlBuilder.Append($" {logicalOperator} ");
                    }
                }

                var paramName = $"@param{paramCounter++}";
                
                switch (rule.Operator?.ToUpper())
                {
                    case "IS NULL":
                    case "IS NOT NULL":
                        sqlBuilder.Append($"[{rule.Field}] {rule.Operator.ToUpper()}");
                        break;
                    case "IN":
                    case "NOT IN":
                        sqlBuilder.Append($"[{rule.Field}] {rule.Operator.ToUpper()} ({paramName})");
                        parameters[paramName] = rule.Data ?? "";
                        break;
                    default:
                        sqlBuilder.Append($"[{rule.Field}] {rule.Operator} {paramName}");
                        parameters[paramName] = rule.Data ?? "";
                        break;
                }
            }

            response.IsValid = !response.ValidationErrors.Any();
            if (response.IsValid && sqlBuilder.Length > 0)
            {
                response.GeneratedSql = $"WHERE {sqlBuilder}";
                response.Parameters = parameters;
            }

            return response;
        }

        /// <summary>
        /// Maps AdvanceFilter model to DTO
        /// </summary>
        private static AdvanceFilterDto MapToDto(AdvanceFilter filter)
        {
            return new AdvanceFilterDto
            {
                Id = filter.Id,
                Name = filter.Name,
                Description = filter.Description,
                LogicalOperator = filter.LogicalOperator,
                IsActive = filter.IsActive,
                CreatedDate = filter.CreatedDate,
                ModifiedDate = filter.ModifiedDate,
                Rules = filter.Rules.Select(r => new RulesDto
                {
                    Id = r.Id,
                    Field = r.Field,
                    Data = r.Data,
                    Operator = r.Operator,
                    Condition = r.Condition,
                    Order = r.Order
                }).ToList()
            };
        }
    }
}
