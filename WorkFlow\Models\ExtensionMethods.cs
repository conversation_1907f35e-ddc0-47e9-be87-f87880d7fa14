﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Web;
using System.Web.WebPages;
using System.Reflection;
using WorkFlow.Models;
namespace WorkFlow.Models
{
    //Coded By Muralidhara H M :-P
    public static class ExtensionMethods
    {
        public static IQueryable<T> OrderByField<T>(this IQueryable<T> q, string SortField, string sord)
        {
            MethodCallExpression mce = null;
            if (SortField != string.Empty && sord != string.Empty)
            {
                var param = Expression.Parameter(typeof(T), "p");
                var prop = Expression.Property(param, SortField);

                var exp = Expression.Lambda(prop, param);

                string method = (sord == "asc" ? "OrderBy" : "OrderByDescending");

                Type[] types = new Type[] { q.ElementType, exp.Body.Type };

                mce = Expression.Call(typeof(Queryable), method, types, q.Expression, exp);
                IQueryable<T> qu = q.Provider.CreateQuery<T>(mce);
            }
            return q.Provider.CreateQuery<T>(mce);

        }
        //public static IQueryable<T> FilterSearch<T>(this IQueryable<T> q, Filters filters)
        //{
        //    ParameterExpression par = Expression.Parameter(typeof(T), string.Empty);
        //    MemberExpression field = null;
        //    ConstantExpression constantValue = null;
        //    string Property = string.Empty;
        //    int count = filters.rules.Count;
        //    BinaryExpression binaryExpression = null;
        //    for (int i = 0; i < count; i++)
        //    {
        //        field = Expression.PropertyOrField(par, filters.rules.ElementAt(i).field);
        //        constantValue = Expression.Constant(filters.rules.ElementAt(i).data.ConvertToType(field.Type));

        //        if (binaryExpression == null)
        //        {
        //            binaryExpression = Expression.Equal(field, constantValue);
        //        }
        //        else
        //        {
        //            binaryExpression = Expression.And(binaryExpression, Expression.Equal(field, constantValue));
        //        }
        //    }

        //    LambdaExpression lamda = Expression.Lambda(binaryExpression, par);

        //    string method = "Where";

        //    Type[] types = new Type[] { q.ElementType };

        //    var mce = Expression.Call(typeof(Queryable), method, types, q.Expression, lamda);
        //    return q.Provider.CreateQuery<T>(mce);


        //}

        public static IQueryable<T> FilterSearch<T>(this IQueryable<T> q, Filters filters)
        {
            ParameterExpression par = Expression.Parameter(typeof(T), string.Empty);
            MemberExpression field = null;
            ConstantExpression constantValue = null;
            string Property = string.Empty;
            int count = filters.rules.Count;
            BinaryExpression binaryExpression = null;
            for (int i = 0; i < count; i++)
            {
              
                field = Expression.PropertyOrField(par, filters.rules.ElementAt(i).field);

                constantValue = Expression.Constant(filters.rules.ElementAt(i).data.ConvertToType(field.Type));

                if (binaryExpression == null)
                {

                    binaryExpression = ExtensionMethods.LikeSearch(field, constantValue);
                }
                else
                {
                    var bin = ExtensionMethods.LikeSearch(field, constantValue);

                    binaryExpression = Expression.And(binaryExpression, bin);
                }
            }

            var lambda = Expression.Lambda<Func<T, bool>>(binaryExpression, par);
            return q.Where(lambda.Compile()).AsQueryable();


        }


        //public static IQueryable<T> AdvanceSearch<T>(this IQueryable<T> q, AdvanceFilter advnfilters)
        //{
        //    ParameterExpression par = Expression.Parameter(typeof(T), string.Empty);
        //    MemberExpression field = null;
        //    ConstantExpression constantValue = null;

        //    string Property = string.Empty;
        //    int count = advnfilters.rules.Count;
        //    BinaryExpression binaryExpression = null;
        //    BinaryExpression binaryExpressionNxt = null;
        //    string Operator = string.Empty;
        //    string Condition = string.Empty;
        //    object obj = null;
        //    for (int i = 0; i < count; i++)
        //    {
        //        field = Expression.PropertyOrField(par, advnfilters.rules.ElementAt(i).Field);
  
        //        constantValue = Expression.Constant(advnfilters.rules.ElementAt(i).Data.ConvertToType( field.Type));
        //        Condition = advnfilters.rules.ElementAt(i).Condition;
        //        Operator = advnfilters.rules.ElementAt(i).Operator;

        //        switch (Operator)
        //        {
        //            case "equals":
        //                binaryExpressionNxt = Expression.Equal(field, constantValue);
        //                break;
        //            case "notequals":
        //                binaryExpressionNxt = Expression.NotEqual(field, constantValue);
        //                break;
        //            case "lessthan":
        //                binaryExpressionNxt = Expression.LessThan(field, constantValue);
        //                break;
        //            case "greaterthan":
        //                binaryExpressionNxt = Expression.GreaterThan(field, constantValue);
        //                break;
        //            case "like":
        //                break;
        //            default:
        //                break;
        //        }

        //            switch (Condition)
        //            {
        //                case "AND":
        //                    binaryExpression = Expression.And(binaryExpression, binaryExpressionNxt);
        //                    break;
        //                case "OR":
        //                    binaryExpression = Expression.Or(binaryExpression, binaryExpressionNxt);
        //                    break;
        //                default:
        //                    binaryExpression = binaryExpressionNxt;
        //                    break;
        //            }

        //    }

        //    LambdaExpression lamda = Expression.Lambda(binaryExpression, par);

        //    string method = "Where";

        //    Type[] types = new Type[] { q.ElementType };

        //    var mce = Expression.Call(typeof(Queryable), method, types, q.Expression, lamda);
        //    return q.Provider.CreateQuery<T>(mce);


        //}
        public static IQueryable<T> AdvanceSearch<T>(this IQueryable<T> q, AdvanceFilter advnfilters)
        {
            ParameterExpression par = Expression.Parameter(typeof(T), string.Empty);
            MemberExpression field = null;
            ConstantExpression constantValue = null;

            string Property = string.Empty;
            int count = advnfilters.rules.Count;
            BinaryExpression binaryExpression = null;
            BinaryExpression binaryExpressionNxt = null;
            string Operator = string.Empty;
            string Condition = string.Empty;
            object obj = null;
            for (int i = 0; i < count; i++)
            {
                field = Expression.PropertyOrField(par, advnfilters.rules.ElementAt(i).Field);

                constantValue = Expression.Constant(WFCommon.DecryptString(advnfilters.rules.ElementAt(i).Data).ConvertToType(field.Type));
                Condition = advnfilters.rules.ElementAt(i).Condition;
                Operator = advnfilters.rules.ElementAt(i).Operator;

                switch (Operator.ToLower())
                {
                    case "equals":
                        ConstantExpression type1 = Expression.Constant(Convert.ToByte(1), typeof(byte));
                        binaryExpressionNxt = ExtensionMethods.EqualSearch(field, constantValue, type1);
                        break;
                    case "equal":
                        ConstantExpression type1N = Expression.Constant(Convert.ToByte(1), typeof(byte));
                        binaryExpressionNxt = ExtensionMethods.EqualSearch(field, constantValue, type1N);
                        break;
                    case "notequal":
                        ConstantExpression type2 = Expression.Constant(Convert.ToByte(2), typeof(byte));
                        binaryExpressionNxt = ExtensionMethods.EqualSearch(field, constantValue, type2);
                        break;
                    case "lessthan":
                        binaryExpressionNxt = Expression.LessThan(field, constantValue);
                        break;
                    case "greaterthan":
                        binaryExpressionNxt = Expression.GreaterThan(field, constantValue);
                        break;
                    case "like":
                        binaryExpressionNxt = ExtensionMethods.LikeSearch(field, constantValue);
                        break;
                    default:
                        break;
                }

                switch (Condition)
                {
                    case "AND":
                        binaryExpression = Expression.And(binaryExpression, binaryExpressionNxt);
                        break;
                    case "OR":
                        binaryExpression = Expression.Or(binaryExpression, binaryExpressionNxt);
                        break;
                    default:
                        binaryExpression = binaryExpressionNxt;
                        break;
                }

            }

            var lambda = Expression.Lambda<Func<T, bool>>(binaryExpression, par);
            return q.Where(lambda.Compile()).AsQueryable();

        }
        public static List<T> Paginate<T>(this List<T> q,int page,int rows)
        {
            int startIndex = ((page * rows) - rows);
            int count = q.Count();
            int length = (count < startIndex?count:((count - startIndex) < rows ? q.Count() - startIndex : rows));

            if (startIndex <= count)
            {
                return q.GetRange(startIndex, length);
            }
            else
            {
                return q.GetRange(0, 0);
            }
        }
        public static object ConvertToType(this object objct, Type type)
        {
            if (type == typeof(int))
            {
                objct = Convert.ToInt32(objct);
            }
            else if (type == typeof(decimal))
            {
                objct = Convert.ToDecimal(objct);
            }
            else if (type == typeof(DateTime))
            {
                objct = Convert.ToString(objct);
            }
            else if (type == typeof(string))
            {
                objct = objct.ToString().ToLower();
            }
            else if (type == typeof(bool) )
            {
                objct = objct.ToString().ToLower()=="yes"?true:false;
            }
            else if ( type.Name == "Nullable`1" )
            {

                objct = objct.ToString().ToLower() == "yes" ? true : (objct.ToString().ToLower() == "no" ? false : objct);
                
            }
 
            return objct;
        }
        public static bool Like(string field,string value)
        {
            return field==null?false:field.ToLower().Contains(value.ToLower());
        }
        public static bool LikeInteger(int field, int value)
        {
            return  field==null?false: field.ToString().Contains(value.ToString());
        }
        public static bool LikeDecimal(decimal field, decimal value)
        {
            return field == null ? false : field.ToString().Contains(value.ToString());
        }
        public static bool LikeNullableBool(Nullable<bool> field, bool value)
        {
            return field==value;
        }
        public static bool LikeBool(bool field, bool value)
        {
            return field == value;
        }
        public static bool LikeDate(DateTime field, string value)
        {
            return field == null ? false : field.ToString("dd-MMM-yyyy").ToLower().Contains(value.ToLower());
        }
        public static BinaryExpression LikeSearch(Expression field,Expression value)
        {
  
            BinaryExpression binary = null;
            MethodCallExpression mce = null;
            if (value.Type == typeof(int))
            {
                mce = Expression.Call(typeof(ExtensionMethods).GetMethod("LikeInteger"), field, value);
            }
            else if (value.Type == typeof(decimal))
            {
                mce = Expression.Call(typeof(ExtensionMethods).GetMethod("LikeDecimal"), field, value);
            }
            else if (value.Type == typeof(string) && field.Type == typeof(DateTime))
            {
                mce = Expression.Call(typeof(ExtensionMethods).GetMethod("LikeDate"), field, value);
            }
            else if (value.Type == typeof(string))
            {
                mce = Expression.Call(typeof(ExtensionMethods).GetMethod("Like"), field, value);
            }
            else if ( value.Type.Name=="Nullable`1")
            {
               
                mce = Expression.Call(typeof(ExtensionMethods).GetMethod("LikeNullableBool"),field,value);
            }
            else if (value.Type == typeof(bool) )
            {

                mce = Expression.Call(typeof(ExtensionMethods).GetMethod("LikeBool"), field, value);
            }
       
            binary = Expression.Or(mce, mce); ;
            return binary;
        }
        public static BinaryExpression EqualSearch(Expression field, Expression value, ConstantExpression type)
        {
            BinaryExpression binary = null;
            MethodCallExpression mce = null;
            if (value.Type == typeof(int))
            {
                mce = Expression.Call(typeof(ExtensionMethods).GetMethod("EqualInteger"), field, value, type);
            }
            else if (value.Type == typeof(decimal))
            {
                mce = Expression.Call(typeof(ExtensionMethods).GetMethod("EqualDecimal"), field, value, type);
            }
            else if (value.Type == typeof(string))
            {
                mce = Expression.Call(typeof(ExtensionMethods).GetMethod("EqualString"), field, value, type);
            }
            else if (value.Type.Name == "Nullable`1")
            {

                mce = Expression.Call(typeof(ExtensionMethods).GetMethod("EqualNullableBool"), field, value, type);
            }
            else if (value.Type == typeof(bool))
            {

                mce = Expression.Call(typeof(ExtensionMethods).GetMethod("EqualBool"), field, value, type);
            }
            binary = Expression.Or(mce, mce); ;
            return binary;
        }
        public static bool EqualString(string field, string value, byte type)
        {
            if(type==1)
            {
            return field == null ? false : field.ToLower()==value.ToLower();
            }
            else
            {
                  return field == null ? false : field.ToLower()!=value.ToLower();
            }
        }
        public static bool EqualInteger(int field, int value,byte type)
        {
            if(type==1)
            {
            return field == null ? false : field.ToString()==value.ToString();
            }
            else
            {
                   return field == null ? false : field.ToString()!=value.ToString();
            }
        }
        public static bool EqualDecimal(decimal field, decimal value,byte type)
        {
             if(type==1)
            {
            return field == null ? false : field.ToString() == value.ToString();
             }
            else
             {
                  return field == null ? false : field.ToString() != value.ToString();
             }
        }
        public static bool EqualNullableBool(Nullable<bool> field, bool value,byte type)
        {
              if(type==1)
            {
            return field == value;
              }
            else
              {
                   return field != value;
              }
        }
        public static bool EqualBool(bool field, bool value,byte type)
        {
            if (type == 1)
            {
                return field == value;
            }
            else
            {
                return field != value;
            }
        }
    }
    public class Filters
    {
        public ICollection<rules> rules { get; set; }
    }
    public class rules
    {
        public string field { get; set; }
        public string data { get; set; }
    }
    public class AdvanceFilter
    {
        public ICollection<Rules> rules { get; set; }
    }
    public class Rules
    {
        public string Field { get; set; }
        public string Data { get; set; }
        public string Operator { get; set; }
        public string Condition { get; set; }
    }
   
   
}