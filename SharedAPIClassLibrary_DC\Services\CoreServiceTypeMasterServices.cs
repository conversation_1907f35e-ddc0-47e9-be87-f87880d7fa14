﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace SharedAPIClassLibrary_AMERP
{
    public class CoreServiceTypeMasterServices
    {
        static string AppPath = string.Empty;

        #region ::: Select Particular ServiceType in edit mode /Mithun:::
        /// <summary>
        /// To Select Particular Service Type in edit mode
        /// </summary> 
        public static IActionResult SelectParticularServiceTypeID(SelectParticularServiceTypeIDList SelectParticularServiceTypeIDObj, string constring, int LogException)
        {
            try
            {
                //GNM_User User = (GNM_User)Session["UserDetails"];
                // GNM_User User = SelectParticularServiceTypeIDObj.UserDetails.FirstOrDefault();
                int companyID = SelectParticularServiceTypeIDObj.Company_ID;
                int branchID = Convert.ToInt32(SelectParticularServiceTypeIDObj.Branch);
                var JsonResult = default(dynamic);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    string query = "SELECT ServiceType_ID, ServiceType_Name, ServiceType_Active, IsMandatoryService, ServiceDueHours, ServiceDueDays, IsWarrantyClaimable, IsDemandDrive, IsInsuranceJob, IsCommissioning, Company_ID FROM GNM_ServiceType WHERE ServiceType_ID = @ServiceType_ID";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@ServiceType_ID", SelectParticularServiceTypeIDObj.serviceTypeID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                int dbCompanyID = reader.GetInt32(reader.GetOrdinal("Company_ID"));
                                bool Edit = dbCompanyID == companyID;

                                JsonResult = new
                                {
                                    ServiceType_ID = reader.GetInt32(reader.GetOrdinal("ServiceType_ID")),
                                    ServiceType_Name = reader.GetString(reader.GetOrdinal("ServiceType_Name")),
                                    ServiceType_Active = reader.GetBoolean(reader.GetOrdinal("ServiceType_Active")),
                                    IsMandatoryService = reader.GetBoolean(reader.GetOrdinal("IsMandatoryService")),
                                    ServiceDueHours = reader.GetInt32(reader.GetOrdinal("ServiceDueHours")),
                                    ServiceDueDays = reader.GetInt32(reader.GetOrdinal("ServiceDueDays")),
                                    IsWarrantyClaimable = reader.GetBoolean(reader.GetOrdinal("IsWarrantyClaimable")),
                                    IsDemandDrive = reader.GetBoolean(reader.GetOrdinal("IsDemandDrive")),
                                    IsInsuranceJob = reader.GetBoolean(reader.GetOrdinal("IsInsuranceJob")),
                                    IsCommissioning = reader.GetBoolean(reader.GetOrdinal("IsCommissioning")),
                                    Edit
                                };
                            }
                        }
                    }
                }

                //gbl.InsertGPSDetails(
                //    Convert.ToInt32(SelectParticularServiceTypeIDObj.Company_ID.ToString()),
                //    branchID,
                //    User.User_ID,
                //    Common.GetObjectID("CoreServiceTypeMaster",constring),
                //    serviceTypeID,
                //    0,
                //    0,
                //    "Viewed " + ((dynamic)JsonResult).ServiceType_Name,
                //    false,
                //    Convert.ToInt32(SelectParticularServiceTypeIDObj.MenuID),
                //    Convert.ToDateTime(SelectParticularServiceTypeIDObj.LoggedINDateTime)
                //);

                //return Json(JsonResult, JsonRequestBehavior.AllowGet);
                return new JsonResult(JsonResult);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
        }


        #endregion

        #region ::: Load Service Type Landing Grid /Mithun:::
        /// <summary>
        /// To Load Service type Landing Grid
        /// </summary>   
        public static IActionResult Select(SelectCoreServicesList SelectObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filter, string query)
        {
            try
            {
                int count = 0;
                int total = 0;
                //GNM_User User = (GNM_User)Session["UserDetails"];
                //   GNM_User User = SelectObj.UserDetails.FirstOrDefault();
                int companyID = SelectObj.Company_ID;

                IEnumerable<GNM_ServiceType> liService = null;
                IQueryable<filterservicetype> iQService = null;

                //string YesE = HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "Yes").ToString();
                //string NoE = HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "No").ToString();

                //IEnumerable<ParentCompanyObject> ParentCompanyDetails = null;
                //string Query = ";WITH ParentComapany([Company_ID],[Company_Name],[Company_Parent_ID]) as (SELECT [Company_ID],[Company_Name],[Company_Parent_ID] FROM dbo.GNM_Company WHERE [Company_ID] = " + companyID + " UNION ALL SELECT child.[Company_ID], child.[Company_Name], child.[Company_Parent_ID] FROM dbo.GNM_Company AS child JOIN ParentComapany ON child.[Company_ID] = ParentComapany.[Company_Parent_ID])SELECT [Company_ID],[Company_Name],[Company_Parent_ID] FROM ParentComapany;";
                //ParentCompanyDetails = companyClient.Database.SqlQuery(typeof(ParentCompanyObject), Query).Cast<ParentCompanyObject>();

                ////liService = serviceTypeClient.GNM_ServiceType.Where(a => a.Company_ID == companyID);
                //liService = serviceTypeClient.GNM_ServiceType.ToList();

                //var arrParts = from a in liService
                //               join b in ParentCompanyDetails on a.Company_ID equals b.Company_ID
                //               select new filterservicetype
                //               {
                //                   ServiceType_ID = a.ServiceType_ID,
                //                   ServiceType_Name = a.ServiceType_Name,
                //                   IsMandatoryService = a.IsMandatoryService == true ? YesE : NoE,
                //                   IsWarrantyClaimable = a.IsWarrantyClaimable == true ? YesE : NoE,
                //                   IsInsuranceJob = a.IsInsuranceJob == true ? YesE : NoE,
                //                   ServiceDueHours = Convert.ToInt32(a.ServiceDueHours),
                //                   ServiceDueDays = Convert.ToInt32(a.ServiceDueDays),
                //                   IsDemandDrive = a.IsDemandDrive == true ? YesE : NoE,
                //                   ServiceType_Active = a.ServiceType_Active == true ? YesE : NoE,
                //                   ServiceDueHoursSort = a.ServiceDueHours.ToString(),
                //                   ServiceDueDaysSort = a.ServiceDueDays.ToString(),
                //                   Company_ID = a.Company_ID
                //               };
                ////iQService = arrParts.AsQueryable<filterservicetype>();

                getLandingGridDataList getLandingGridDataObj = new getLandingGridDataList();

                iQService = getLandingGridData(constring, LogException, companyID, SelectObj.GeneralCulture).AsQueryable<filterservicetype>();


                //FilterToolBar Search

                //if (_search)
                //{
                //    Filters filters = JObject.Parse(Common.DecryptString(filter)).ToObject<Filters>();
                //    iQService = iQService.FilterSearch<filterservicetype>(filters);

                //}
                ////Advance Search
                //else if (advnce)
                //{
                //    AdvanceFilter advnfilter = JObject.Parse(Common.DecryptString(query)).ToObject<AdvanceFilter>();
                //    iQService = iQService.AdvanceSearch<filterservicetype>(advnfilter);
                //}

                //Sorting 
                if (sidx == "ServiceDueHoursSort")
                {
                    iQService = iQService.OrderByField<filterservicetype>("ServiceDueHours", sord);
                }
                else if (sidx == "ServiceDueDaysSort")
                {
                    iQService = iQService.OrderByField<filterservicetype>("ServiceDueDays", sord);
                }
                else
                {
                    iQService = iQService.OrderByField<filterservicetype>(sidx, sord);
                }
                //Session["ServiceTypeExport"] = iQService;
                count = iQService.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                if (count < (rows * page) && count != 0)//1st iteration QA correction
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                var serviceTypeArray = (from a in iQService.AsEnumerable()
                                        select new
                                        {
                                            //edit = "<img id='" + a.ServiceType_ID + "' ServiceTypeName='" + a.ServiceType_Name + "' src='" + AppPath + "/Content/edit.gif' key='" + a.ServiceType_ID + "' class='editServiceTypeMaster' editmode='false'/>",

                                            edit = "<a title='View' href='#' style='font-size: 13px;' id='" + a.ServiceType_ID + "' ServiceTypeName='" + a.ServiceType_Name + "' key='" + a.ServiceType_ID + "' class='editServiceTypeMaster' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",

                                            delete = "<input type='checkbox' key='" + a.ServiceType_ID + "' defaultchecked=''  id='chk" + a.ServiceType_ID + "' class='" + (a.Company_ID == companyID ? "chkServiceTypeDelete" : "chkServiceTypeParentDelete") + "'/>",
                                            a.ServiceType_ID,
                                            ServiceType_Name = a.ServiceType_Name,
                                            IsMandatoryService = a.IsMandatoryService,
                                            IsWarrantyClaimable = a.IsWarrantyClaimable,
                                            IsInsuranceJob = a.IsInsuranceJob,
                                            ServiceDueHours = a.ServiceDueHours == 0 ? "" : a.ServiceDueHours.ToString(),
                                            ServiceDueDays = a.ServiceDueDays == 0 ? "" : a.ServiceDueDays.ToString(),
                                            IsDemandDrive = a.IsDemandDrive,
                                            ServiceType_Active = a.ServiceType_Active,
                                            ServiceDueHoursSort = a.ServiceDueHoursSort,
                                            ServiceDueDaysSort = a.ServiceDueDaysSort,
                                            Local = "<a key='" + a.ServiceType_ID + "' src='" + AppPath + "/Content/local.png' class='ServiceTypeMasterLocale' alt='Localize' width='20' height='20'  title='Localize' IsAddOrEditable='" + (a.Company_ID == companyID ? "1" : "0") + "'><i class='fa fa-globe'></i></a>",
                                        }).ToList().Paginate(page, rows);
                var x = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = serviceTypeArray,//serviceTypeArray.ToList().Paginate(page, rows)
                    //filter = Request.Params["filters"],
                    //advanceFilter = Request.Params["Query"],
                };
                //return Json(x, JsonRequestBehavior.AllowGet);
                return new JsonResult(x);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
        }
        #endregion

        #region::: GetLandingGridData /Mithun:::

        public static IQueryable<filterservicetype> getLandingGridData(string constring, int LogException, int Company_ID, string GeneralCulture)
        {
            List<filterservicetype> iQService = new List<filterservicetype>();
            try
            {
                //GNM_User User = (GNM_User)Session["UserDetails"];
                // GNM_User User = getLandingGridDataObj.UserDetails.FirstOrDefault();
                int companyID = Company_ID;

                List<GNM_ServiceType> liService = new List<GNM_ServiceType>();
                List<ParentCompanyObject> ParentCompanyDetails = new List<ParentCompanyObject>();

                string YesE = CommonFunctionalities.GetResourceString(GeneralCulture.ToString(), "yes").ToString();
                string NoE = CommonFunctionalities.GetResourceString(GeneralCulture.ToString(), "no").ToString();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Retrieve ParentCompanyDetails
                    using (SqlCommand cmd = new SqlCommand("UP_AMERP_GetParentCompanyDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@companyID", companyID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            // Check if the reader has rows
                            if (!reader.HasRows)
                            {
                                // Handle the case when no rows are returned if necessary
                                return iQService.AsQueryable();
                            }

                            while (reader.Read())
                            {
                                ParentCompanyDetails.Add(new ParentCompanyObject
                                {
                                    Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                    Company_Name = reader.GetString(reader.GetOrdinal("Company_Name")),
                                    Company_Parent_ID = reader.IsDBNull(reader.GetOrdinal("Company_Parent_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Company_Parent_ID"))

                                });
                            }
                        }
                    }

                    // Retrieve GNM_ServiceType data
                    using (SqlCommand cmd = new SqlCommand("UP_AMERP_GetServiceTypes", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                liService.Add(new GNM_ServiceType
                                {
                                    ServiceType_ID = reader.GetInt32(reader.GetOrdinal("ServiceType_ID")),
                                    ServiceType_Name = reader.GetString(reader.GetOrdinal("ServiceType_Name")),
                                    IsMandatoryService = reader.IsDBNull(reader.GetOrdinal("IsMandatoryService")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsMandatoryService")),
                                    IsWarrantyClaimable = reader.IsDBNull(reader.GetOrdinal("IsWarrantyClaimable")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsWarrantyClaimable")),
                                    IsInsuranceJob = reader.IsDBNull(reader.GetOrdinal("IsInsuranceJob")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsInsuranceJob")),
                                    ServiceDueHours = reader.IsDBNull(reader.GetOrdinal("ServiceDueHours")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ServiceDueHours")),
                                    ServiceDueDays = reader.IsDBNull(reader.GetOrdinal("ServiceDueDays")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ServiceDueDays")),
                                    IsDemandDrive = reader.IsDBNull(reader.GetOrdinal("IsDemandDrive")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsDemandDrive")),
                                    ServiceType_Active = reader.GetBoolean(reader.GetOrdinal("ServiceType_Active")),
                                    Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID"))
                                });
                            }
                        }
                    }
                }

                // Join data and project into filterservicetype
                var queryResult = from a in liService
                                  join b in ParentCompanyDetails on a.Company_ID equals b.Company_ID
                                  select new filterservicetype
                                  {
                                      ServiceType_ID = a.ServiceType_ID,
                                      ServiceType_Name = a.ServiceType_Name,
                                      IsMandatoryService = a.IsMandatoryService.HasValue ? (a.IsMandatoryService.Value ? YesE : NoE) : NoE,
                                      IsWarrantyClaimable = a.IsWarrantyClaimable.HasValue ? (a.IsWarrantyClaimable.Value ? YesE : NoE) : NoE,
                                      IsInsuranceJob = a.IsInsuranceJob.HasValue ? (a.IsInsuranceJob.Value ? YesE : NoE) : NoE,
                                      ServiceDueHours = (int)(a.ServiceDueHours.HasValue ? a.ServiceDueHours.Value : 0),
                                      ServiceDueDays = (int)(a.ServiceDueDays.HasValue ? a.ServiceDueDays.Value : 0),
                                      IsDemandDrive = a.IsDemandDrive.HasValue ? (a.IsDemandDrive.Value ? YesE : NoE) : NoE,
                                      ServiceType_Active = a.ServiceType_Active.ToString(),
                                      ServiceDueHoursSort = a.ServiceDueHours.HasValue ? a.ServiceDueHours.ToString() : "",
                                      ServiceDueDaysSort = a.ServiceDueDays.HasValue ? a.ServiceDueDays.ToString() : "",
                                      Company_ID = a.Company_ID
                                  };
                iQService = queryResult.ToList();

            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return iQService.AsQueryable();
        }


        #endregion

        #region ::: Insert /Mithun:::
        /// <summary>
        /// ServiceType Insert
        /// </summary>   
        public static IActionResult Insert(InsertCoreServiceTypeList InsertObj, string constring, int LogException)
        {
            int? nullable = null;
            int newServiceTypeID = 0;
            try
            {
                JObject jobj = JObject.Parse(InsertObj.Data);
                //GNM_User User = (GNM_User)Session["UserDetails"];
                // GNM_User User = InsertObj.UserDetails.FirstOrDefault();
                int companyID = InsertObj.Company_ID;

                string serviceTypeName = Common.DecryptString(jobj["ServiceType_Name"].ToString());
                bool isMandatory = jobj["ServiceType_IsMandatoryService"].ToString() == "checked";
                bool isWarrantyClaimable = jobj["ServiceType_IsWarrantyClaimable"].ToString() == "checked";
                bool isInsuranceJob = jobj["ServiceType_InsuranceJob"].ToString() == "checked";
                bool isCommissioning = jobj["IsCommissioning"].ToString() == "checked";
                int serviceDueHours = (int)(string.IsNullOrEmpty(jobj["ServiceType_ServiceDueHours"].ToString()) ? (int?)null : Convert.ToInt32(jobj["ServiceType_ServiceDueHours"]));
                int serviceDueDays = (int)(string.IsNullOrEmpty(jobj["ServiceType_ServiceDueDays"].ToString()) ? (int?)null : Convert.ToInt32(jobj["ServiceType_ServiceDueDays"]));
                bool isDemandDrive = jobj["ServiceType_IsDemandDrive"].ToString() == "true";
                bool isActive = jobj["ServiceType_Active"].ToString() == "checked";

                string serviceTypeCode = jobj["ServiceType_Code"]?.ToString();
                int? serviceTypePriority = string.IsNullOrEmpty(jobj["ServiceType_Priority"]?.ToString()) ? (int?)null : Convert.ToInt32(jobj["ServiceType_Priority"]);
                bool? isNotificationEligible = jobj["IsNotificationEligible"]?.ToString() == "checked";
                bool? chargeToTypeInvoice = jobj["ChargeToTypeInvoice"]?.ToString() == "checked";
                bool? chargeToTypeInternalInvoice = jobj["ChargeToTypeInternalInvoice"]?.ToString() == "checked";
                bool? chargeToTypeWarranty = jobj["ChargeToTypeWarranty"]?.ToString() == "checked";
                bool? isUnderStandardWarranty = jobj["IsUnderStandardWarranty"]?.ToString() == "checked";
                bool? isUnderExtendedWarranty = jobj["IsUnderExtendedWarranty"]?.ToString() == "checked";
                bool? isNotUnderStandardandExtendedWarranty = jobj["IsNotUnderStandardandExtendedWarranty"]?.ToString() == "checked";
                bool? isInvoice = jobj["IsInvoice"]?.ToString() == "checked";
                bool? isInternalInvoice = jobj["IsInternalInvoice"]?.ToString() == "checked";
                int? prevostNotifT = string.IsNullOrEmpty(jobj["PrevostNotifT"]?.ToString()) ? (int?)null : Convert.ToInt32(jobj["PrevostNotifT"]);
                int? novaNotifT = string.IsNullOrEmpty(jobj["NovaNotifT"]?.ToString()) ? (int?)null : Convert.ToInt32(jobj["NovaNotifT"]);
                int? systemCondition = string.IsNullOrEmpty(jobj["SystemCondition"]?.ToString()) ? (int?)null : Convert.ToInt32(jobj["SystemCondition"]);
                bool? isStandardText = jobj["IsStandardText"]?.ToString() == "checked";

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Use SqlCommand to call the stored procedure InsertGNM_ServiceType
                    using (SqlCommand cmd = new SqlCommand("UP_INS_AMERP_InsertGNM_ServiceType", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", companyID);
                        cmd.Parameters.AddWithValue("@ServiceType_Name", serviceTypeName);
                        cmd.Parameters.AddWithValue("@ServiceType_Active", isActive);
                        cmd.Parameters.AddWithValue("@IsMandatoryService", isMandatory);
                        cmd.Parameters.AddWithValue("@ServiceDueHours", (object)serviceDueHours ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ServiceDueDays", (object)serviceDueDays ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@IsWarrantyClaimable", isWarrantyClaimable);
                        cmd.Parameters.AddWithValue("@IsDemandDrive", isDemandDrive);
                        cmd.Parameters.AddWithValue("@IsInsuranceJob", isInsuranceJob);
                        cmd.Parameters.AddWithValue("@IsCommissioning", isCommissioning);
                        cmd.Parameters.AddWithValue("@ServiceType_Code", (object)serviceTypeCode ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ServiceType_Priority", (object)serviceTypePriority ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@IsNotificationEligible", (object)isNotificationEligible ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ChargeToTypeInvoice", (object)chargeToTypeInvoice ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ChargeToTypeInternalInvoice", (object)chargeToTypeInternalInvoice ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ChargeToTypeWarranty", (object)chargeToTypeWarranty ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@IsUnderStandardWarranty", (object)isUnderStandardWarranty ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@IsUnderExtendedWarranty", (object)isUnderExtendedWarranty ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@IsNotUnderStandardandExtendedWarranty", (object)isNotUnderStandardandExtendedWarranty ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@IsInvoice", (object)isInvoice ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@IsInternalInvoice", (object)isInternalInvoice ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@PrevostNotifT", (object)prevostNotifT ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@NovaNotifT", (object)novaNotifT ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@SystemCondition", (object)systemCondition ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@IsStandardText", (object)isStandardText ?? DBNull.Value);

                        // Execute the InsertGNM_ServiceType stored procedure and get the newly inserted ServiceType_ID
                        newServiceTypeID = Convert.ToInt32(cmd.ExecuteScalar());
                    }
                    // Insert into GNM_ServiceTypeOperationDetail
                    JObject jOperation = JObject.Parse(InsertObj.OperationDetailData);
                    int rowCount = jOperation["rows"].Count();
                    for (int i = 0; i < rowCount; i++)
                    {
                        int operationID = Convert.ToInt32(jOperation["rows"][i]["Operation_ID"]);

                        // Call the InsertGNM_ServiceTypeOperationDetail stored procedure
                        using (SqlCommand cmd = new SqlCommand("UP_INS_AMERP_InsertGNM_ServiceTypeOperationDetail", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@ServiceType_ID", newServiceTypeID);
                            cmd.Parameters.AddWithValue("@Operation_ID", operationID);

                            // Execute the stored procedure InsertGNM_ServiceTypeOperationDetail
                            cmd.ExecuteNonQuery();
                        }
                    }

                    // Insert GPS details
                    //gbl.InsertGPSDetails(
                    //    Convert.ToInt32(InsertObj.Company_ID),
                    //    Convert.ToInt32(InsertObj.Branch),
                    //    Convert.ToInt32(InsertObj.User_ID),
                    //    Convert.ToInt32(Common.GetObjectID("CoreServiceTypeMaster",constring)),
                    //    newServiceTypeID,
                    //    0,
                    //    0,
                    //    "Inserted " + serviceTypeName,
                    //    false,
                    //    Convert.ToInt32(InsertObj.MenuID),
                    //    Convert.ToDateTime(InsertObj.LoggedINDateTime)
                    //);
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
            //return newServiceTypeID;
            return new JsonResult(newServiceTypeID);
        }

        #endregion

        #region ::: Update /Mithun:::
        /// <summary>
        /// ServiceType Update
        /// </summary>   
        public static IActionResult Update(UpdateCoreServiceList UpdateObj, string constring, int LogException)
        {
            string msg;
            try
            {
                JObject jobj = JObject.Parse(UpdateObj.data);
                //GNM_User User = (GNM_User)Session["UserDetails"];
                //  GNM_User User = UpdateObj.UserDetails.FirstOrDefault();

                int companyID = UpdateObj.Company_ID;

                int ServiceType_ID = (int)jobj["ServiceType_ID"];
                string serviceTypeName = jobj["ServiceType_Name"].ToString();
                string IsMandatory = jobj["ServiceType_IsMandatoryService"].ToString();
                string IsWarranty = jobj["ServiceType_IsWarrantyClaimable"].ToString();
                string InsuranceJob = jobj["ServiceType_InsuranceJob"].ToString();
                string ServiceDueHours = jobj["ServiceType_ServiceDueHours"].ToString();
                string ServiceDueDays = jobj["ServiceType_ServiceDueDays"].ToString();
                string DemandDrive = jobj["ServiceType_IsDemandDrive"].ToString();
                string Active = jobj["ServiceType_Active"].ToString();
                string IsCommissioning = jobj["IsCommissioning"].ToString();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    SqlCommand command = new SqlCommand("UP_UPD_AMERP_UpdateServiceType", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.AddWithValue("@Company_ID", companyID);
                    command.Parameters.AddWithValue("@ServiceType_ID", ServiceType_ID);
                    command.Parameters.AddWithValue("@ServiceType_Name", Common.DecryptString(serviceTypeName));
                    command.Parameters.AddWithValue("@IsMandatoryService", IsMandatory == "checked" ? true : false);
                    command.Parameters.AddWithValue("@IsWarrantyClaimable", IsWarranty == "checked" ? true : false);
                    command.Parameters.AddWithValue("@IsInsuranceJob", InsuranceJob == "checked" ? true : false);
                    command.Parameters.AddWithValue("@ServiceDueHours", string.IsNullOrEmpty(ServiceDueHours) ? (object)DBNull.Value : Convert.ToInt32(ServiceDueHours));
                    command.Parameters.AddWithValue("@ServiceDueDays", string.IsNullOrEmpty(ServiceDueDays) ? (object)DBNull.Value : Convert.ToInt32(ServiceDueDays));
                    command.Parameters.AddWithValue("@IsDemandDrive", DemandDrive == "checked" ? true : false);
                    command.Parameters.AddWithValue("@ServiceType_Active", Active == "checked" ? true : false);
                    command.Parameters.AddWithValue("@IsCommissioning", IsCommissioning == "checked" ? true : false);

                    command.ExecuteNonQuery();

                }


                DateTime LoginDatetime = Convert.ToDateTime(UpdateObj.LoggedINDateTime);
                return new JsonResult(msg = "Updated");
                //gbl.InsertGPSDetails(Convert.ToInt32(UpdateObj.Company_ID), Convert.ToInt32(UpdateObj.Branch), Convert.ToInt32(UpdateObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreServiceTypeMaster",constring)), ServiceType_ID, 0, 0, "Updated " + serviceTypeName, false, Convert.ToInt32(UpdateObj.MenuID), LoginDatetime, null);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
        }


        #endregion

        #region ::: Delete /Mithun:::
        /// <summary>
        /// ServiceType Delete
        /// </summary>   
        public static IActionResult Delete(DeleteCoreServiceList DeleteObj, string constring, int LofException)
        {
            string errorMsg = "";
            try
            {
                JTokenReader reader = null;
                JObject jobj = JObject.Parse(DeleteObj.key);
                int rowCount = jobj["rows"].Count();
                int id = 0;

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    SqlTransaction transaction = conn.BeginTransaction();

                    try
                    {
                        for (int i = 0; i < rowCount; i++)
                        {
                            reader = new JTokenReader(jobj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                            reader.Read();
                            id = Convert.ToInt32(reader.Value);

                            using (SqlCommand cmdCheckDependency = new SqlCommand("SELECT COUNT(*) FROM FSMCore_SRT_JobCardHeader WHERE ServiceType_ID = @ServiceType_ID", conn, transaction))
                            {
                                cmdCheckDependency.Parameters.AddWithValue("@ServiceType_ID", id);
                                int dependencyCount = (int)cmdCheckDependency.ExecuteScalar();

                                if (dependencyCount == 0)
                                {
                                    using (SqlCommand cmdDeleteServiceType = new SqlCommand("DeleteServiceType", conn, transaction))
                                    {
                                        cmdDeleteServiceType.CommandType = CommandType.StoredProcedure;
                                        cmdDeleteServiceType.Parameters.AddWithValue("@ServiceType_ID", id);
                                        cmdDeleteServiceType.ExecuteNonQuery();
                                    }

                                    using (SqlCommand cmdDeleteServiceTypeLocale = new SqlCommand("DeleteServiceTypeLocale", conn, transaction))
                                    {
                                        cmdDeleteServiceTypeLocale.CommandType = CommandType.StoredProcedure;
                                        cmdDeleteServiceTypeLocale.Parameters.AddWithValue("@ServiceType_ID", id);
                                        cmdDeleteServiceTypeLocale.ExecuteNonQuery();
                                    }
                                }
                                else
                                {
                                    errorMsg += CommonFunctionalities.GetResourceString(DeleteObj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                                    transaction.Rollback();
                                    return new JsonResult(errorMsg);
                                }
                            }
                        }

                        transaction.Commit();
                        //gbl.InsertGPSDetails(Convert.ToInt32(DeleteObj.Company_ID), Convert.ToInt32(DeleteObj.Branch), Convert.ToInt32(DeleteObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreServiceTypeMaster",constring)), id, 0, 0, "Delete" + "", false, Convert.ToInt32(DeleteObj.MenuID), Convert.ToDateTime(DeleteObj.LoggedINDateTime));
                        errorMsg += CommonFunctionalities.GetResourceString(DeleteObj.GeneralCulture.ToString(), "deletedsuccessfully").ToString();
                    }
                    catch (SqlException sqlEx)
                    {
                        transaction.Rollback();
                        if (sqlEx.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                        {
                            errorMsg += CommonFunctionalities.GetResourceString(DeleteObj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                errorMsg += ex.Message;
            }
            //return errorMsg;
            return new JsonResult(errorMsg);
        }

        #endregion

        #region ::: Select ServiceType Locale /Mithun :::
        /// <summary>
        /// to Load Service Type Locale
        /// </summary>   
        public static IActionResult SelServiceTypeLocale(SelServiceTypeLocaleList SelServiceTypeLocaleObj, string constring, int LogException)
        {
            try
            {
                int userLanguageID = Convert.ToInt32(SelServiceTypeLocaleObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelServiceTypeLocaleObj.GeneralLanguageID);

                dynamic tempJsonRow = null;

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand("UP_SEL_AMERP_GetServiceTypeLocale", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.AddWithValue("@ServiceTypeID", SelServiceTypeLocaleObj.id);
                        command.Parameters.AddWithValue("@UserLanguageID", userLanguageID);

                        SqlDataReader reader = command.ExecuteReader();

                        if (reader.Read())
                        {
                            tempJsonRow = new
                            {
                                id = reader["ServiceType_ID"],
                                serviceTypeGName = reader["ServiceType_Name"],
                                ServiceType_Active = (bool)reader["ServiceType_Active"],
                                IsMandatoryService = (bool)reader["IsMandatoryService"],
                                IsDemandDrive = (bool)reader["IsDemandDrive"],
                                IsInsuranceJob = (bool)reader["IsInsuranceJob"],
                                IsWarrantyClaimable = (bool)reader["IsWarrantyClaimable"],
                                IsCommissioning = (bool)reader["IsCommissioning"],
                                ServiceDueHoursG = Convert.ToInt32(reader["ServiceDueHours"]),
                                ServiceDueDaysG = Convert.ToInt32(reader["ServiceDueDays"]),
                                serviceTypeLName = reader["ServiceTypeLName"] != DBNull.Value ? (string)reader["ServiceTypeLName"] : string.Empty,
                                serviceTypeLID = reader["ServiceTypeLID"] != DBNull.Value ? (int)reader["ServiceTypeLID"] : 0
                            };
                        }

                        reader.Close();
                    }
                }

                //return Json(tempJsonRow, JsonRequestBehavior.AllowGet);
                return new JsonResult(tempJsonRow);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: Select Service Type Native /Mithun:::
        /// <summary>
        /// to Select ServiceType Native
        /// </summary>  
        public static IActionResult SelServiceTypeNative(SelServiceTypeNativeList SelServiceTypeNativeObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters)
        {
            int count = 0;
            int total = 0;
            try
            {
                //GNM_User User = (GNM_User)Session["UserDetails"];
                // GNM_User User = SelServiceTypeNativeObj.UserDetails.FirstOrDefault();
                int companyID = SelServiceTypeNativeObj.Company_ID;
                int LangID = SelServiceTypeNativeObj.Language_ID;

                string YesL = CommonFunctionalities.GetResourceString(SelServiceTypeNativeObj.UserCulture.ToString(), "Yes").ToString();
                string NoL = CommonFunctionalities.GetResourceString(SelServiceTypeNativeObj.UserCulture.ToString(), "No").ToString();

                List<filterservicetype> serviceTypes = new List<filterservicetype>();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    string query = @"
                SELECT st.ServiceType_ID, stl.ServiceType_Name, st.IsMandatoryService, st.IsWarrantyClaimable,
                       st.IsInsuranceJob, st.ServiceDueHours, st.ServiceDueDays, st.IsDemandDrive, st.ServiceType_Active
                FROM GNM_ServiceType st
                JOIN GNM_ServiceTypeLocale stl ON st.ServiceType_ID = stl.ServiceType_ID
                WHERE st.Company_ID = @CompanyID AND stl.Language_ID = @LangID";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);
                        cmd.Parameters.AddWithValue("@LangID", LangID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                serviceTypes.Add(new filterservicetype
                                {
                                    ServiceType_ID = reader.GetInt32(0),
                                    ServiceType_Name = reader.GetString(1),
                                    IsMandatoryService = reader.GetBoolean(2) ? YesL : NoL,
                                    IsWarrantyClaimable = reader.GetBoolean(3) ? YesL : NoL,
                                    IsInsuranceJob = reader.GetBoolean(4) ? YesL : NoL,
                                    ServiceDueHours = reader.GetInt32(5),
                                    ServiceDueDays = reader.GetInt32(6),
                                    IsDemandDrive = reader.GetBoolean(7) ? YesL : NoL,
                                    ServiceType_Active = reader.GetBoolean(8) ? YesL : NoL
                                });
                            }
                        }
                    }
                }

                IQueryable<filterservicetype> iQService = serviceTypes.AsQueryable();

                // FilterToolBar Search
                //if (Request.Params["_search"] == "true")
                //{
                //    Filters filters = JObject.Parse(Request.Params["filters"]).ToObject<Filters>();
                //    iQService = iQService.FilterSearch<filterservicetype>(filters);
                //}

                iQService = iQService.OrderByField<filterservicetype>(sidx, sord);

                count = iQService.ToList().Count;
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                List<dynamic> arr = new List<dynamic>();
                for (int i = 0; i < iQService.ToList().Count; i++)
                {
                    if ((i >= (page * rows) - rows) && (i < (page * rows) + rows))
                    {
                        arr.Add(iQService.ToList()[i]);
                    }
                }

                dynamic s = null;
                var x = s;
                x = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = arr.ToArray()
                };
                //return Json(x, JsonRequestBehavior.AllowGet);
                return new JsonResult(x);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
        }
        #endregion

        #region ::: Insert Service Type Locale /Mithun:::
        /// <summary>
        /// to Insert ServiceType Locale
        /// </summary> 
        public static IActionResult InsertServiceTypeLocale(InsertServiceTypeLocaleList InsertServiceTypeLocaleObj, string constring, int LogException)
        {
            try
            {
                // Retrieve userLanguageID from Session
                int userLanguageID = Convert.ToInt32(InsertServiceTypeLocaleObj.UserLanguageID);

                // Parse JSON data
                JObject jobj = JObject.Parse(InsertServiceTypeLocaleObj.Data);
                int serviceTypeGID = Convert.ToInt32(jobj["serviceTypeGID"]);
                string serviceTypeLName = jobj["serviceTypeLName"].ToString();

                // Decrypt serviceTypeLName if necessary (assuming Common.DecryptString does this)
                serviceTypeLName = Common.DecryptString(serviceTypeLName);

                // Create ADO.NET connection and command objects
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Define SqlCommand for stored procedure
                    SqlCommand command = new SqlCommand("UP_INS_AMERP_InsertServiceTypeLocale", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    // Add parameters to the command
                    command.Parameters.AddWithValue("@ServiceType_ID", serviceTypeGID);
                    command.Parameters.AddWithValue("@ServiceType_Name", serviceTypeLName);
                    command.Parameters.AddWithValue("@Language_ID", userLanguageID);

                    // Execute the command
                    int rowsAffected = command.ExecuteNonQuery();
                    return new JsonResult(new { rowsAffected });
                    // Handle logging or other operations as needed
                    // gbl.InsertGPSDetails(Convert.ToInt32(InsertServiceTypeLocaleObj.Company_ID), Convert.ToInt32(InsertServiceTypeLocaleObj.Branch), Convert.ToInt32(InsertServiceTypeLocaleObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreServiceTypeMaster",constring)), serviceTypeGID, 0, 0, "Update", false, Convert.ToInt32(InsertServiceTypeLocaleObj.MenuID));
                }
            }
            catch (WebException wex)
            {
                // Handle WebException
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                // Handle other exceptions
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
        }


        #endregion

        #region ::: Update Service Type Locale /Mithun:::
        /// <summary>
        /// to Update ServiceType Locale
        /// </summary> 
        public static IActionResult UpdateServiceTypeLocale(UpdateServiceTypeLocaleList UpdateServiceTypeLocaleObj, string constring, int LogException)
        {
            try
            {
                // Retrieve userLanguageID from Session
                int userLanguageID = Convert.ToInt32(UpdateServiceTypeLocaleObj.UserLanguageID);

                // Parse JSON data
                JObject jobj = JObject.Parse(UpdateServiceTypeLocaleObj.Data);
                int serviceTypeLID = Convert.ToInt32(jobj["serviceTypeLID"]);
                int serviceTypeGID = Convert.ToInt32(jobj["serviceTypeGID"]);
                string serviceTypeLName = jobj["serviceTypeLName"].ToString();

                // Decrypt serviceTypeLName if necessary (assuming Common.DecryptString does this)
                serviceTypeLName = Common.DecryptString(serviceTypeLName);

                // Create ADO.NET connection and command objects
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Define SqlCommand for stored procedure
                    SqlCommand command = new SqlCommand("UP_UPD_AMERP_UpdateServiceTypeLocale", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    // Add parameters to the command
                    command.Parameters.AddWithValue("@ServiceTypeLocale_ID", serviceTypeLID);
                    command.Parameters.AddWithValue("@ServiceType_ID", serviceTypeGID);
                    command.Parameters.AddWithValue("@ServiceType_Name", serviceTypeLName);
                    command.Parameters.AddWithValue("@Language_ID", userLanguageID);

                    // Execute the command
                    int rowsAffected = command.ExecuteNonQuery();
                    return new JsonResult(rowsAffected);
                    // Handle logging or other operations as needed
                    //  gbl.InsertGPSDetails(Convert.ToInt32(UpdateServiceTypeLocaleObj.Company_ID), Convert.ToInt32(UpdateServiceTypeLocaleObj.Branch), Convert.ToInt32(UpdateServiceTypeLocaleObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreServiceTypeMaster",constring)), serviceTypeGID, 0, 0, "Update", false, Convert.ToInt32(UpdateServiceTypeLocaleObj.MenuID));
                }
            }
            catch (WebException wex)
            {
                // Handle WebException
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                // Handle other exceptions
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
        }


        #endregion

        #region ::: Check Duplicate ServiceType /Mithun:::
        /// <summary>
        /// to check if the Service type is already exists or not
        /// </summary>
        public static IActionResult CheckServiceType(CheckServiceTypeList CheckServiceTypeObj, string constring, int LogException)
        {
            int status = 0;
            string value = Common.DecryptString(CheckServiceTypeObj.value);
            try
            {
                int Company_ID = Convert.ToInt32(CheckServiceTypeObj.Company_ID);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    List<ParentCompanyObject> parentCompanyDetails = new List<ParentCompanyObject>();
                    List<GNM_ServiceType> serviceTypes = new List<GNM_ServiceType>();

                    // Call the stored procedure to get parent company details
                    using (SqlCommand cmd = new SqlCommand("UP_AMERP_GetParentCompanyDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                parentCompanyDetails.Add(new ParentCompanyObject
                                {
                                    Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                    Company_Name = reader.GetString(reader.GetOrdinal("Company_Name")),
                                    Company_Parent_ID = (int)(reader.IsDBNull(reader.GetOrdinal("Company_Parent_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Company_Parent_ID")))
                                });
                            }
                        }
                    }

                    // Call the stored procedure to get service types
                    using (SqlCommand cmd = new SqlCommand("UP_AMERP_GetServiceTypes", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                serviceTypes.Add(new GNM_ServiceType
                                {
                                    ServiceType_ID = reader.GetInt32(0),
                                    ServiceType_Name = reader.GetString(1),
                                    Company_ID = reader.GetInt32(2)
                                });
                            }
                        }
                    }

                    var serviceTypeArray = from a in serviceTypes
                                           join b in parentCompanyDetails on a.Company_ID equals b.Company_ID
                                           where a.ServiceType_ID != CheckServiceTypeObj.primaryKey && a.ServiceType_Name.ToLower() == value.ToLower()
                                           select new { a.ServiceType_Name };

                    if (serviceTypeArray.Any())
                    {
                        status = 1;
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
            //return status;
            return new JsonResult(status);
        }

        #endregion

        #region ::: SelectServiceTypeOperationDetails /Mithun:::
        /// <summary>
        /// to Select ServiceType Operati on Details
        /// </summary>
        /// <returns></returns>

        public static IActionResult SelectServiceTypeOperationDetails(SelectServiceTypeOperationDetailsList SelectServiceTypeOperationDetailsObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters)
        {
            var JsonResult = default(dynamic);
            try
            {
                int Count = 0, Total = 0;
                int Company_ID = Convert.ToInt32(SelectServiceTypeOperationDetailsObj.Company_ID);
                int userLanguageID = Convert.ToInt32(SelectServiceTypeOperationDetailsObj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectServiceTypeOperationDetailsObj.GeneralLanguageID);

                string Query = "";
                List<ServiceTypeOperationDetails> ServiceTypeOperationDetailsList = new List<ServiceTypeOperationDetails>();

                if (userLanguageID == generalLanguageID)
                {
                    Query = "SELECT A.ServiceTypeOperationDetail_ID, A.Operation_ID, C.Operation_Code AS 'OperationCode', C.Operation_Description AS 'OperationDescription' " +
                            "FROM GNM_ServiceTypeOperationDetail A " +
                            "JOIN GNM_ServiceType B ON A.ServiceType_ID = B.ServiceType_ID " +
                            "JOIN SRM_Operation C ON A.Operation_ID = C.Operation_ID " +
                            "WHERE B.Company_ID = @Company_ID AND A.ServiceType_ID = @ServiceType_ID";
                }
                else
                {
                    Query = "SELECT A.ServiceTypeOperationDetail_ID, A.Operation_ID, C.Operation_Code AS 'OperationCode', CL.Operation_Description AS 'OperationDescription' " +
                            "FROM GNM_ServiceTypeOperationDetail A " +
                            "JOIN GNM_ServiceType B ON A.ServiceType_ID = B.ServiceType_ID " +
                            "JOIN SRM_Operation C ON A.Operation_ID = C.Operation_ID " +
                            "JOIN select CL ON C.Operation_ID = CL.Operation_ID AND CL.Language_ID = @userLanguageID " +
                            "WHERE B.Company_ID = @Company_ID AND A.ServiceType_ID = @ServiceType_ID";
                }

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    using (SqlCommand cmd = new SqlCommand(Query, conn))
                    {
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                        cmd.Parameters.AddWithValue("@ServiceType_ID", SelectServiceTypeOperationDetailsObj.ServiceType_ID);
                        if (userLanguageID != generalLanguageID)
                        {
                            cmd.Parameters.AddWithValue("@userLanguageID", userLanguageID);
                        }

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ServiceTypeOperationDetailsList.Add(new ServiceTypeOperationDetails
                                {
                                    ServiceTypeOperationDetail_ID = reader.GetInt32(0),
                                    Operation_ID = reader.GetInt32(1),
                                    OperationCode = reader.GetString(2),
                                    OperationDescription = reader.GetString(3)
                                });
                            }
                        }
                    }
                }

                IQueryable<ServiceTypeOperationDetails> ServiceTypeOperationDetailsIQ = ServiceTypeOperationDetailsList.AsQueryable();

                //if (Request.Params["_search"] == "true")
                //{
                //    Filters filters = JObject.Parse(Common.DecryptString(Request.Params["filters"])).ToObject<Filters>();
                //    ServiceTypeOperationDetailsIQ = ServiceTypeOperationDetailsIQ.FilterSearch<ServiceTypeOperationDetails>(filters);
                //}

                ServiceTypeOperationDetailsIQ = ServiceTypeOperationDetailsIQ.OrderByField(sidx, sord);

                Count = ServiceTypeOperationDetailsIQ.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;

                JsonResult = new
                {
                    total = Total,
                    page = page,
                    data = (from a in ServiceTypeOperationDetailsIQ
                            select new
                            {
                                edit = "<a title='Edit' href='#' style='font-size: 13px;' id='" + a.ServiceTypeOperationDetail_ID + "' key='" + a.ServiceTypeOperationDetail_ID + "' class='ServiceTypeOperationDetailEdit' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input type='checkbox' key='" + a.ServiceTypeOperationDetail_ID + "' defaultchecked='' id='chk" + a.ServiceTypeOperationDetail_ID + "' class='ServiceTypeOperationDetailDelete'/>",
                                ServiceTypeOperationDetail_ID = a.ServiceTypeOperationDetail_ID,
                                Operation_ID = a.Operation_ID,
                                OperationCode = a.OperationCode,
                                OperationDescription = a.OperationDescription,
                                Local = "<a key='" + a.ServiceTypeOperationDetail_ID + "' id='" + a.ServiceTypeOperationDetail_ID + "' src='" + AppPath + "/Content/local.png' class='ServiceTypeOperationLocale' width='20' height='20' alt='Localize' title='Localize'><i class='fa fa-globe'></i></a>"
                            }).ToList(),
                    records = Count
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(JsonResult, JsonRequestBehavior.AllowGet);
            return JsonResult(JsonResult);
        }
        #endregion

        #region ::: ChangeOperationCode /Mithun:::
        /// <summary>
        /// TO Change OperationCode
        /// </summary>
        /// <param name="Key"></param>
        /// <returns></returns>

        public static IActionResult ChangeOperationCode(ChangeOperationCodeList ChangeOperationCodeObj, string constring, int LogException)
        {
            try
            {
                var extraParams = System.Web.HttpUtility.ParseQueryString(ChangeOperationCodeObj.extra);

                // Decrypt the 'Key' parameter
                string Key = Common.DecryptString(extraParams["Key"]);

                // Get 'mode' and handle logic based on it
                string mode = extraParams["mode"];

                // Convert 'primID' to nullable int only if mode is "edit"
                int? primID = mode == "edit" ? (int?)Convert.ToInt32(extraParams["primID"]) : null;

                // Convert 'ID' to integer
                int id = Convert.ToInt32(extraParams["ID"]);
                var jsonResponse = default(dynamic);
                int LangID = Convert.ToInt32(ChangeOperationCodeObj.LanguageID);
                int Company_ID = Convert.ToInt32(ChangeOperationCodeObj.Company_ID);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("UP_SEL_AMERP_GetOperationDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Key", Key);
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                        cmd.Parameters.AddWithValue("@ServiceType_ID", id);
                        cmd.Parameters.AddWithValue("@Mode", mode);
                        cmd.Parameters.AddWithValue("@PrmID", primID.HasValue ? (object)primID.Value : DBNull.Value);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                jsonResponse = new
                                {
                                    Operation_ID = reader["Operation_ID"] != DBNull.Value ? (int)reader["Operation_ID"] : (int?)null,
                                    Operation_Code = reader["Operation_Code"].ToString(),
                                    IsExists = (bool)reader["IsExists"],
                                    Operation_Description = reader["Operation_Description"].ToString()
                                };
                            }
                            else
                            {
                                jsonResponse = new
                                {
                                    Operation_ID = "",
                                    Operation_Code = "",
                                    IsExists = false,
                                    Operation_Description = ""
                                };
                            }
                        }
                    }
                }
                //return Json(jsonResponse, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonResponse);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region::: SaveServiceTypeOperationDetails /Mithun:::
        /// <summary>
        /// to Save ServiceType Operaton Details
        /// </summary>
        /// <returns></returns>

        public static IActionResult SaveServiceTypeOperationDetails(SaveServiceTypeOperationDetailsList SaveServiceTypeOperationDetailsObj, string constring, int LogException)
        {
            string Message = string.Empty;
            SqlConnection connection = new SqlConnection(constring);
            SqlCommand command = new SqlCommand();
            SqlTransaction transaction = null;

            try
            {
                connection.Open();
                transaction = connection.BeginTransaction();
                command.Connection = connection;
                command.Transaction = transaction;

                JObject JOperation = JObject.Parse(SaveServiceTypeOperationDetailsObj.data);
                int Count = JOperation["rows"].Count();

                for (int i = 0; i < Count; i++)
                {
                    GNM_ServiceTypeOperationDetail OperationDetail = JOperation["rows"].ElementAt(i).ToObject<GNM_ServiceTypeOperationDetail>();

                    // Set the stored procedure name
                    command.CommandText = "UP_INS_UPD_AMERP_GNM_ServiceTypeOperationDetail";
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.Clear();

                    // Add parameters
                    command.Parameters.AddWithValue("@ServiceTypeOperationDetail_ID", OperationDetail.ServiceTypeOperationDetail_ID);
                    command.Parameters.AddWithValue("@Operation_ID", OperationDetail.Operation_ID);
                    command.Parameters.AddWithValue("@ServiceType_ID", OperationDetail.ServiceType_ID);

                    // Execute the stored procedure
                    command.ExecuteNonQuery();

                    // Logging or additional operations
                    //  gbl.InsertGPSDetails(Convert.ToInt32(SaveServiceTypeOperationDetailsObj.Company_ID), Convert.ToInt32(SaveServiceTypeOperationDetailsObj.Branch), Convert.ToInt32(SaveServiceTypeOperationDetailsObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreServiceTypeMaster",constring)), OperationDetail.ServiceType_ID, 0, 0, "Update", false, Convert.ToInt32(SaveServiceTypeOperationDetailsObj.MenuID));
                }

                transaction.Commit();
                Message = "Saved";
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    // Logging exception
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

                // Rollback transaction if there's an exception
                if (transaction != null)
                    transaction.Rollback();

                Message = string.Empty;
            }
            finally
            {
                // Close connection in finally block
                if (connection.State == ConnectionState.Open)
                    connection.Close();
            }

            //return Message;
            return new JsonResult(Message);
        }

        #endregion


        #region::: DeleteServiceTypeOperationDetails /Mithun:::
        /// <summary>
        /// to Delete ServiceType Operation Details
        /// </summary>
        /// <returns></returns>

        public static IActionResult DeleteServiceTypeOperationDetails(DeleteServiceTypeOperationDetailsList DeleteServiceTypeOperationDetailsObj, string constring, int LogException)
        {
            string Msg = string.Empty;
            try
            {
                JObject jObj = JObject.Parse(DeleteServiceTypeOperationDetailsObj.key);
                int Count = jObj["rows"].Count();
                int ID = 0;
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    using (SqlCommand cmd = conn.CreateCommand())
                    {
                        SqlTransaction transaction = conn.BeginTransaction();
                        cmd.Transaction = transaction;

                        try
                        {
                            for (int i = 0; i < Count; i++)
                            {
                                ID = Convert.ToInt32(jObj["rows"].ElementAt(i)["id"].ToString());
                                cmd.CommandText = "DELETE FROM GNM_ServiceTypeOperationDetail WHERE ServiceTypeOperationDetail_ID = @ID";
                                cmd.Parameters.Clear();
                                cmd.Parameters.AddWithValue("@ID", ID);
                                cmd.ExecuteNonQuery();
                            }

                            transaction.Commit();
                            Msg += CommonFunctionalities.GetResourceString(DeleteServiceTypeOperationDetailsObj.UserCulture.ToString(), "deletedsuccessfully");
                        }
                        catch (SqlException sqlEx)
                        {
                            transaction.Rollback();
                            if (sqlEx.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                            {
                                Msg += CommonFunctionalities.GetResourceString(DeleteServiceTypeOperationDetailsObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Msg += ex.Message;
            }
            //return Msg;
            return new JsonResult(Msg);
        }


        #endregion


        #region ::: Export :::

        public static async Task<object> Export(SelectCoreServicesList ExportObj, string constring, int LogException, string filters, string Query, string sidx, string sord)
        {
            try
            {
                // Create DataTable for service types
                DataTable DtServiceType = new DataTable();
                DtServiceType.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "ServiceTypeName").ToString());
                DtServiceType.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "IsMandatory").ToString());
                DtServiceType.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "IsWarranty").ToString());
                DtServiceType.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "InsuranceJob").ToString());
                DtServiceType.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "serviceduehours").ToString());
                DtServiceType.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "serviceduedays").ToString());
                DtServiceType.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "IsConsiderForDemand").ToString());
                DtServiceType.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "ServiceType_Active").ToString());

                // Create DataTable for alignment
                DataTable DtAlignment = new DataTable();
                DtAlignment.Columns.Add("ServiceTypeName");
                DtAlignment.Columns.Add("IsMandatory");
                DtAlignment.Columns.Add("IsWarranty");
                DtAlignment.Columns.Add("IsInsuranceJob");
                DtAlignment.Columns.Add("Service Due Hours");
                DtAlignment.Columns.Add("Service Due Days");
                DtAlignment.Columns.Add("IsDemandDrive");
                DtAlignment.Columns.Add("ServiceType_Active");
                DtAlignment.Rows.Add(0, 0, 0, 0, 2, 2, 0, 0);

                // Fetch data using getLandingGridData()
                IQueryable<filterservicetype> IQServiceTypeArray = getLandingGridData(constring, LogException, ExportObj.Company_ID, ExportObj.GeneralCulture);

                // Apply filters if present
                if (filters != null && filters.ToString() != "null")
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    if (filtersObj.rules.Count() > 0)
                    {
                        IQServiceTypeArray = IQServiceTypeArray.FilterSearch<filterservicetype>(filtersObj);
                    }
                }
                else if (Query != null && Query.ToString() != "null")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Query).ToObject<AdvanceFilter>();
                    if (advnfilter.rules.Count() > 0)
                    {
                        IQServiceTypeArray = IQServiceTypeArray.AdvanceSearch<filterservicetype>(advnfilter);
                    }
                }

                // Apply ordering
                IQServiceTypeArray = IQServiceTypeArray.OrderByField<filterservicetype>(ExportObj.sidx.ToString(), ExportObj.sord.ToString());

                // Populate DataTable with the filtered data
                int Count = IQServiceTypeArray.Count();
                for (int i = 0; i < Count; i++)
                {
                    var serviceType = IQServiceTypeArray.ElementAt(i);
                    DtServiceType.Rows.Add(
                        serviceType.ServiceType_Name,
                        serviceType.IsMandatoryService,
                        serviceType.IsWarrantyClaimable,
                        serviceType.IsInsuranceJob,
                        serviceType.ServiceDueHours == 0 ? string.Empty : serviceType.ServiceDueHours.ToString(),
                        serviceType.ServiceDueDays == 0 ? string.Empty : serviceType.ServiceDueDays.ToString(),
                        serviceType.IsDemandDrive,
                        serviceType.ServiceType_Active
                    );
                }

                ExportList reportExportList = new ExportList
                {
                    Company_ID = ExportObj.Company_ID, // Assuming this is available in ExportObj
                    Branch = ExportObj.Branch,
                    dt1 = DtServiceType,


                    dt = DtServiceType,

                    FileName = "MovementTypeDefinition", // Set a default or dynamic filename
                    Header = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "MovementTypeDefinition").ToString(), // Set a default or dynamic header
                    exprtType = ExportObj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                    UserCulture = ExportObj.UserCulture
                };

                var result = await DocumentExport.Export(reportExportList, constring, LogException);
                return result.Value;

                //return DocumentExport.Export(reportExportList, constring, LogException);
                // Export the data
                //DocumentExport.Export(exprtType, DtServiceType, DtAlignment, "ServiceType",  CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "ServiceType").ToString());
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return null;
        }

        #endregion




        public class DeleteServiceTypeOperationDetailsList
        {
            public string key { get; set; }
            public string UserCulture { get; set; }
        }
        public class SaveServiceTypeOperationDetailsList
        {
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string data { get; set; }
        }
        public class ChangeOperationCodeList
        {
            public int Company_ID { get; set; }
            public int LanguageID { get; set; }
            public string extra { get; set; }
        }
        public class SelectServiceTypeOperationDetailsList
        {
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int ServiceType_ID { get; set; }
        }
        public class ServiceTypeOperationDetails
        {
            public int ServiceTypeOperationDetail_ID { get; set; }
            public int Operation_ID { get; set; }
            public string OperationCode { get; set; }
            public string OperationDescription { get; set; }
        }
        public class CheckServiceTypeList
        {
            public int Company_ID { get; set; }
            public int primaryKey { get; set; }
            public string value { get; set; }
        }
        public class UpdateServiceTypeLocaleList
        {
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string Data { get; set; }
            public string Lang { get; set; }
        }
        public class InsertServiceTypeLocaleList
        {
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string Data { get; set; }
        }
        public class SelServiceTypeNativeList
        {
            public string UserCulture { get; set; }
            public int Language_ID { get; set; }
            public int Company_ID { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public class SelServiceTypeLocaleList
        {
            public int id { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        public class DeleteCoreServiceList
        {
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int serviceTypeID { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string Data { get; set; }
            public string GeneralCulture { get; set; }
            public string key { get; set; }
        }
        public class UpdateCoreServiceList
        {
            public List<GNM_User> UserDetails { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string data { get; set; }
        }
        public class InsertCoreServiceTypeList
        {
            public List<GNM_User> UserDetails { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string Data { get; set; }
            public string OperationDetailData { get; set; }
        }
        public class SelectCoreServicesList
        {
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int exprtType { get; set; }
            public string GeneralCulture { get; set; }
            public string UserCulture { get; set; }
            public string filters { get; set; }
            public string Query { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public class getLandingGridDataList
        {
            public List<GNM_User> UserDetails { get; set; }
        }
        public class filterservicetype
        {
            public int ServiceType_ID { get; set; }
            public string ServiceType_Name { get; set; }
            public string IsMandatoryService { get; set; }
            public string IsWarrantyClaimable { get; set; }

            public int ServiceDueHours { get; set; }
            public int ServiceDueDays { get; set; }
            public string IsDemandDrive { get; set; }

            public string ServiceType_Active { get; set; }
            public string IsInsuranceJob { get; set; }
            public string ServiceDueHoursSort { get; set; }
            public string ServiceDueDaysSort { get; set; }
            public int Company_ID { get; set; }
        }
        public partial class GNM_ServiceTypeOperationDetail
        {
            public int ServiceTypeOperationDetail_ID { get; set; }
            public int ServiceType_ID { get; set; }
            public int Operation_ID { get; set; }

            public virtual GNM_ServiceType GNM_ServiceType { get; set; }
            public virtual CoreSRM_Operation SRM_Operation { get; set; }
        }
        public partial class CoreSRM_OperationBranchDetail
        {
            public int OperationBranchDetail_ID { get; set; }
            public int Operation_ID { get; set; }
            public int Branch_ID { get; set; }

            public virtual CoreSRM_Operation SRM_Operation { get; set; }
        }
        public partial class CoreSRM_OperationCheckListLocaleDetail
        {
            public int OperationCheckListLocaleDetail_ID { get; set; }
            public int OperationCheckListDetail_ID { get; set; }
            public string CheckListLocaleDescription { get; set; }
            public int Language_ID { get; set; }

            public virtual CoreSRM_OperationCheckListDetail SRM_OperationCheckListDetail { get; set; }
        }
        public partial class CoreSRM_OperationCheckListDetail
        {
            public CoreSRM_OperationCheckListDetail()
            {
                this.SRM_OperationCheckListLocaleDetail = new HashSet<CoreSRM_OperationCheckListLocaleDetail>();
            }

            public int OperationCheckListDetail_ID { get; set; }
            public int Operation_ID { get; set; }
            public string CheckListDescription { get; set; }
            public Nullable<bool> IsMandatory { get; set; }
            public Nullable<bool> IsSpecialTools { get; set; }
            public Nullable<bool> IsSafetyMeasures { get; set; }

            public virtual CoreSRM_Operation SRM_Operation { get; set; }
            public virtual ICollection<CoreSRM_OperationCheckListLocaleDetail> SRM_OperationCheckListLocaleDetail { get; set; }
        }
        public partial class CoreSRM_OperationLocale
        {
            public int OperationLocale_ID { get; set; }
            public int Operation_ID { get; set; }
            public string Operation_Description { get; set; }
            public int Language_ID { get; set; }

            public virtual CoreSRM_Operation SRM_Operation { get; set; }
        }
        public partial class CoreSRM_OperationProductDetail
        {
            public int OperationProductDetail_ID { get; set; }
            public int Operation_ID { get; set; }
            public int Brand_ID { get; set; }
            public Nullable<int> ProductType_ID { get; set; }
            public Nullable<int> Model_ID { get; set; }

            public virtual CoreSRM_Operation SRM_Operation { get; set; }
        }
        public partial class CoreSRM_Operation
        {
            public CoreSRM_Operation()
            {
                this.GNM_ServiceTypeOperationDetail = new HashSet<GNM_ServiceTypeOperationDetail>();
                this.SRM_OperationBranchDetail = new HashSet<CoreSRM_OperationBranchDetail>();
                this.SRM_OperationCheckListDetail = new HashSet<CoreSRM_OperationCheckListDetail>();
                this.SRM_OperationLocale = new HashSet<CoreSRM_OperationLocale>();
                this.SRM_OperationProductDetail = new HashSet<CoreSRM_OperationProductDetail>();
            }

            public int Operation_ID { get; set; }
            public int Company_ID { get; set; }
            public string Operation_Description { get; set; }
            public string Operation_Code { get; set; }
            public Nullable<int> FunctionGroup_ID { get; set; }
            public Nullable<int> Skill_ID { get; set; }
            public Nullable<byte> Operation_SkillLevel { get; set; }
            public Nullable<decimal> Operation_StandardTime { get; set; }
            public Nullable<decimal> Operation_Time { get; set; }
            public bool Operation_IsActive { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }

            public virtual ICollection<GNM_ServiceTypeOperationDetail> GNM_ServiceTypeOperationDetail { get; set; }
            public virtual ICollection<CoreSRM_OperationBranchDetail> SRM_OperationBranchDetail { get; set; }
            public virtual ICollection<CoreSRM_OperationCheckListDetail> SRM_OperationCheckListDetail { get; set; }
            public virtual ICollection<CoreSRM_OperationLocale> SRM_OperationLocale { get; set; }
            public virtual ICollection<CoreSRM_OperationProductDetail> SRM_OperationProductDetail { get; set; }
        }

        public partial class GNM_ServiceTypeLocale
        {
            public int ServiceTypeLocale_ID { get; set; }
            public int ServiceType_ID { get; set; }
            public string ServiceType_Name { get; set; }
            public int Language_ID { get; set; }

            public virtual GNM_ServiceType GNM_ServiceType { get; set; }
        }
        public partial class GNM_ServiceType
        {
            public GNM_ServiceType()
            {
                this.GNM_ServiceTypeLocale = new HashSet<GNM_ServiceTypeLocale>();
                this.GNM_ServiceTypeOperationDetail = new HashSet<GNM_ServiceTypeOperationDetail>();
            }

            public int ServiceType_ID { get; set; }
            public int Company_ID { get; set; }
            public string ServiceType_Name { get; set; }
            public bool ServiceType_Active { get; set; }
            public Nullable<bool> IsMandatoryService { get; set; }
            public Nullable<int> ServiceDueHours { get; set; }
            public Nullable<int> ServiceDueDays { get; set; }
            public Nullable<bool> IsWarrantyClaimable { get; set; }
            public Nullable<bool> IsDemandDrive { get; set; }
            public Nullable<bool> IsInsuranceJob { get; set; }
            public string ServiceType_Code { get; set; }
            public Nullable<byte> ServiceType_Priority { get; set; }
            public Nullable<bool> IsCommissioning { get; set; }
            public Nullable<bool> IsNotificationEligible { get; set; }
            public Nullable<bool> ChargeToTypeInvoice { get; set; }
            public Nullable<bool> ChargeToTypeInternalInvoice { get; set; }
            public Nullable<bool> ChargeToTypeWarranty { get; set; }
            public Nullable<bool> IsUnderStandardWarranty { get; set; }
            public Nullable<bool> IsUnderExtendedWarranty { get; set; }
            public Nullable<bool> IsNotUnderStandardandExtendedWarranty { get; set; }
            public Nullable<bool> IsInvoice { get; set; }
            public Nullable<bool> IsInternalInvoice { get; set; }
            public string PrevostNotifT { get; set; }
            public string NovaNotifT { get; set; }
            public string SystemCondition { get; set; }
            public Nullable<bool> IsStandardText { get; set; }

            public virtual ICollection<GNM_ServiceTypeLocale> GNM_ServiceTypeLocale { get; set; }
            public virtual ICollection<GNM_ServiceTypeOperationDetail> GNM_ServiceTypeOperationDetail { get; set; }
        }

        public class SelectParticularServiceTypeIDList
        {
            public List<GNM_User> UserDetails { get; set; }
            public int Branch { get; set; }
            public int serviceTypeID { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }


    }
}
