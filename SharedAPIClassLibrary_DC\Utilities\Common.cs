﻿using Amazon;
using Amazon.Runtime;
using Amazon.S3;
using Amazon.S3.Model;
using Azure.Storage;
using Azure.Storage.Blobs;
using Azure.Storage.Sas;
using ClosedXML.Excel;
using DocumentFormat.OpenXml.Drawing.Diagrams;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using OfficeOpenXml;
using SharedAPIClassLibrary_AMERP;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Reflection;
using System.Security.Cryptography;
using System.Security.Cryptography.Pkcs;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.CoreWorkFlowEscalation1Services;
using static SharedAPIClassLibrary_AMERP.Services.AMMSCoreServices._PrevostDashboardServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_DC.Utilities
{
    public class Common
    {
        private static SqlCommand sqlComm;
        private static SqlConnection conn;
        public static string isCreated = "Is created";
        public static string isApproved = "Is approved";
        public static string isRejected = "Is rejected";
        public static string isAssigned = "Is assigned to you, Please take necessary action";
        public static string isMoved = "Next action has been taken";

        //string AWSAccessKey = ConfigurationManager.AppSettings.Get("AWSAccessKey");
        //string AWSSecretKey = ConfigurationManager.AppSettings.Get("AWSSecretKey");
        //string S3BucketName = ConfigurationManager.AppSettings.Get("S3BucketName");
        //static IAmazonS3 AmazoneS3client;

        static IAmazonS3 AmazoneS3client;

        static string AWSAccessKey = ConfigurationManager.AppSettings.Get("AWSAccessKey");

        static string AWSSecretKey = ConfigurationManager.AppSettings.Get("AWSSecretKey");

        static string S3BucketName = ConfigurationManager.AppSettings.Get("S3BucketName");
        static List<ACLProperties> ACLobjList = new List<ACLProperties>();

        #region :::: Generate Connection String ::::
        /// <summary>        
        /// DK - 07-Nov-202 Get Object ID
        /// </summary>
        /// <param name="as per below method sign"></param>
        /// <returns></returns>
        //public int GetObjectID(string ObjectName, int BrandCode, int UserID, string Conn)
        //{
        //    int Object_ID = 0;
        //    try
        //    {
        //        SqlConnectionStringBuilder builder = null;
        //        //if (BrandCode == 1)
        //        //{
        //        //    builder = new SqlConnectionStringBuilder(Conn);
        //        //}
        //        //else
        //        //{
        //        //    builder = new SqlConnectionStringBuilder(Conn);
        //        //}
        //        using (SqlConnection SQLcon = new SqlConnection(Conn))
        //        {
        //            if (SQLcon.State == ConnectionState.Closed || SQLcon.State == ConnectionState.Broken) { SQLcon.Open(); }
        //            DataTable dt = new DataTable();
        //            sqlComm = new SqlCommand("SELECT Object_ID FROM MA_Object WHERE Object_Name=@ObjectName", SQLcon);
        //            sqlComm.Parameters.AddWithValue("@ObjectName", ObjectName);
        //            dt.Load(sqlComm.ExecuteReader());
        //            if (dt.Rows.Count > 0)
        //            {
        //                Object_ID = Convert.ToInt32(dt.Rows[0].ItemArray[0].ToString());
        //            }
        //        }
        //    }
        //    catch (Exception e)
        //    {
        //        var Request = new System.Net.Http.HttpRequestMessage();
        //        //ExceptionLogger.ErrorLog(e, Request, Convert.ToInt32(UserID),"");
        //    }
        //    return Object_ID;
        //}


        //Image Path From S3
        public static string GetAWSObjectURL1(string FileName, string AWSBucketPrifix)
        {
            //FileName = "BonusImg.png";
            string AWSObjectURL = string.Empty;
            string bucketname1 = string.Empty;
            string Prefix1 = string.Empty;
            if (S3BucketName.Contains("/") == true)
            {
                bucketname1 = (S3BucketName.Split('/')[0] + "/");
                Prefix1 = (S3BucketName.Split('/')[1]) + "/" + AWSBucketPrifix;
            }
            else
            {
                bucketname1 = (S3BucketName + "/");
            }
            if (!string.IsNullOrEmpty(FileName))
            {
                try
                {
                    var awsCredentials = new BasicAWSCredentials(AWSAccessKey, AWSSecretKey);
                    using (AmazoneS3client = new AmazonS3Client(awsCredentials, RegionEndpoint.APSouth1))
                    {

                        try
                        {
                            ListObjectsRequest request = new ListObjectsRequest
                            {
                                BucketName = bucketname1,
                                Prefix = Prefix1
                                //Key = FileName
                            };
                            //Task<GetObjectResponse> response1 = AmazoneS3client.GetObjectAsync(getObjectRequest);
                            //Stream ThisStrean = Stream.Null;
                            Task<ListObjectsResponse> response1 = AmazoneS3client.ListObjectsAsync(request);
                            List<S3Object> FilesInBucket = response1.Result.S3Objects.ToList();
                            S3Object FileInfo = FilesInBucket.Where(e => e.Key == Prefix1 + '/' + FileName).Select(e => e).FirstOrDefault();
                            if (FileInfo != null)
                            {
                                GetPreSignedUrlRequest getPreSignedUrlRequest = new GetPreSignedUrlRequest()
                                {
                                    BucketName = bucketname1 + Prefix1,
                                    Key = FileName,
                                    Expires = DateTime.Now.AddMinutes(20)
                                };

                                AWSObjectURL = AmazoneS3client.GetPreSignedURL(getPreSignedUrlRequest);
                            }
                            else
                            {
                                GetPreSignedUrlRequest getPreSignedUrlRequest = new GetPreSignedUrlRequest()
                                {
                                    BucketName = bucketname1 + Prefix1,
                                    Key = FileName,
                                    Expires = DateTime.Now.AddMinutes(20)
                                };
                                AWSObjectURL = AmazoneS3client.GetPreSignedURL(getPreSignedUrlRequest);
                            }
                        }
                        catch (AmazonS3Exception ex)
                        {
                            //write_time.WriteLoggedInOutTimeToFile("1", DateTime.Now.TimeOfDay.ToString(), DateTime.Now.TimeOfDay.ToString(), HostingEnvironment.ContentRootPath, ControllerContext.ActionDescriptor.ControllerName, ControllerContext.ActionDescriptor.ActionName, ex.Source);

                        }
                    }
                }
                catch (AmazonS3Exception ex)
                {
                    // write_time.WriteLoggedInOutTimeToFile("1", DateTime.Now.TimeOfDay.ToString(), DateTime.Now.TimeOfDay.ToString(), HostingEnvironment.ContentRootPath, ControllerContext.ActionDescriptor.ControllerName, ControllerContext.ActionDescriptor.ActionName, ex.Source);

                }

            }
            return AWSObjectURL;
        }
        public bool DeleteAWSObject1(string FileName, string AWSBucketPrifix)
        {
            string bucketname1 = string.Empty;
            string Prefix1 = string.Empty;
            bool deletionSuccessful = false;

            //if (S3BucketName.Contains("/") == true)
            //{
            //    bucketname1 = (S3BucketName.Split('/')[0] + "/");
            //    Prefix1 = (S3BucketName.Split('/')[1]) + "/" + AWSBucketPrifix;
            //}
            //else
            //{
            //    bucketname1 = (S3BucketName + "/");
            //}

            try
            {
                using (AmazonS3Client AmazoneS3client = new AmazonS3Client(AWSAccessKey, AWSSecretKey, RegionEndpoint.APSouth1))
                {
                    try
                    {
                        DeleteObjectRequest deleteObjectRequest = new DeleteObjectRequest
                        {
                            BucketName = AWSBucketPrifix,
                            Key = FileName
                        };

                        Task<DeleteObjectResponse> response = AmazoneS3client.DeleteObjectAsync(deleteObjectRequest);
                        deletionSuccessful = Convert.ToBoolean((response.Result.HttpStatusCode >= HttpStatusCode.OK) && (response.Result.HttpStatusCode < HttpStatusCode.MultipleChoices));
                        // You can check the response for success or handle exceptions if needed.
                    }
                    catch (AmazonS3Exception ex)
                    {
                        // Handle exception, log, or throw as needed.
                    }
                }
            }
            catch (AmazonS3Exception ex)
            {
                // Handle exception, log, or throw as needed.
            }
            return deletionSuccessful;
        }
        public bool DeleteAWSObjectFolder1(string AWSBucketPrifix)
        {
            string bucketname1 = string.Empty;
            string Prefix1 = string.Empty;
            bool deletionSuccessful = false;

            try
            {
                using (AmazonS3Client AmazoneS3client = new AmazonS3Client(AWSAccessKey, AWSSecretKey, RegionEndpoint.APSouth1))
                {
                    try
                    {
                        DeleteObjectRequest deleteObjectRequest = new DeleteObjectRequest
                        {
                            BucketName = AWSBucketPrifix,
                            Key = AWSBucketPrifix
                        };

                        Task<DeleteObjectResponse> response = AmazoneS3client.DeleteObjectAsync(deleteObjectRequest);
                        deletionSuccessful = Convert.ToBoolean((response.Result.HttpStatusCode >= HttpStatusCode.OK) && (response.Result.HttpStatusCode < HttpStatusCode.MultipleChoices));
                        // You can check the response for success or handle exceptions if needed.
                    }
                    catch (AmazonS3Exception ex)
                    {
                        // Handle exception, log, or throw as needed.
                    }
                }
            }
            catch (AmazonS3Exception ex)
            {
                // Handle exception, log, or throw as needed.
            }
            return deletionSuccessful;
        }
        public bool CopyAWSObject1(string sourceFileName, string sourcePrefix, string destinationFileName, string destinationPrefix)
        {
            string sourceBucketName = string.Empty;
            string destinationBucketName = string.Empty;
            bool copySuccessful = false;
            //if (S3BucketName.Contains("/") == true)
            //{
            //    sourceBucketName = (S3BucketName.Split('/')[0] + "/");
            //    destinationBucketName = (S3BucketName.Split('/')[1]) + "/";
            //}
            //else
            //{
            //    sourceBucketName = (S3BucketName + "/");
            //    destinationBucketName = sourceBucketName; // You can change this if copying between different buckets.
            //}

            try
            {
                using (AmazonS3Client AmazoneS3client = new AmazonS3Client(AWSAccessKey, AWSSecretKey, RegionEndpoint.APSouth1))
                {
                    try
                    {
                        CopyObjectRequest copyObjectRequest = new CopyObjectRequest
                        {
                            SourceBucket = sourcePrefix,
                            SourceKey = sourceFileName,
                            DestinationBucket = destinationPrefix,
                            DestinationKey = destinationFileName
                        };

                        Task<CopyObjectResponse> response = AmazoneS3client.CopyObjectAsync(copyObjectRequest);
                        copySuccessful = Convert.ToBoolean((response.Result.HttpStatusCode >= HttpStatusCode.OK) && (response.Result.HttpStatusCode < HttpStatusCode.MultipleChoices));
                        // You can check the response for success or handle exceptions if needed.
                    }
                    catch (AmazonS3Exception ex)
                    {
                        // Handle exception, log, or throw as needed.
                    }
                }
            }
            catch (AmazonS3Exception ex)
            {
                // Handle exception, log, or throw as needed.
            }
            return copySuccessful;
        }
        //public int UploadFileItemsForImport1(HttpPostedFileBase formFile, string AWSPrefix, string FileName)
        //{
        //    //var httpRequest = HttpContext.Current.Request;
        //    int Rowsaffected = 0;


        //    //var formFile = httpRequest.Files["formFile"];

        //    var postedFile = formFile;
        //    //var postedFile = httpRequest.Files[0];
        //    string postedFileName = FileName;
        //    //if (postedFileName.Contains(".xlsx"))
        //    //{
        //    //    postedFileName = postedFileName.Split(new[] { ".xlsx" }, StringSplitOptions.None)[0] + DateTime.Today.ToString("dd-MM-yyyy") + ".xlsx";
        //    //    postedFileName = postedFileName.Replace(" ", "_");
        //    //    postedFileName = postedFileName.Replace("(", "");
        //    //    postedFileName = postedFileName.Replace(")", "_");
        //    //}


        //    string bucketName = AWSPrefix;

        //    //if (S3BucketName.Contains("/"))
        //    //{
        //    //    bucketName = S3BucketName.Split('/')[0] + "/";
        //    //    prefix = S3BucketName.Split('/')[1];
        //    //}
        //    //else
        //    //{
        //    //    bucketName = S3BucketName + "/";
        //    //}

        //    using (var amazonS3client = new Amazon.S3.AmazonS3Client(AWSAccessKey, AWSSecretKey, Amazon.RegionEndpoint.APSouth1))
        //    {
        //        try
        //        {
        //            var putObjectRequest = new PutObjectRequest
        //            {
        //                BucketName = bucketName,
        //                ContentType = postedFile.ContentType,
        //                Key = postedFileName,
        //                InputStream = postedFile.InputStream
        //            };

        //            var putObjectResponse = amazonS3client.PutObject(putObjectRequest);
        //            amazonS3client.PutObject(putObjectRequest);

        //            if (putObjectResponse.HttpStatusCode != HttpStatusCode.OK)
        //            {
        //                Rowsaffected = 0;
        //            }
        //            else
        //            {
        //                Rowsaffected = 1;
        //            }
        //        }
        //        catch (AmazonS3Exception ex)
        //        {

        //        }
        //        catch (Exception ex)
        //        {

        //        }
        //        return Rowsaffected;
        //    }


        //}


        public int UploadFileItemsForImport1(Stream fileStream, string contentType, string awsPrefix, string fileName)
        {
            int rowsAffected = 0;

            using (var amazonS3Client = new AmazonS3Client(AWSAccessKey, AWSSecretKey, RegionEndpoint.APSouth1))
            {
                try
                {
                    var putObjectRequest = new PutObjectRequest
                    {
                        BucketName = awsPrefix,
                        ContentType = contentType,
                        Key = fileName,
                        InputStream = fileStream
                    };

                    var putObjectResponse = amazonS3Client.PutObjectAsync(putObjectRequest).Result;

                    rowsAffected = putObjectResponse.HttpStatusCode == HttpStatusCode.OK ? 1 : 0;
                }
                catch (AmazonS3Exception ex)
                {
                    // Handle S3-specific exceptions
                }
                catch (Exception ex)
                {
                    // Handle general exceptions
                }
            }

            return rowsAffected;
        }


        public async Task<int> UploadFileItemsForImport(IFormFile formFile, string AWSPrefix, string FileName)
        {
            int rowsAffected = 0;

            // Use the uploaded file
            var postedFile = formFile;
            string postedFileName = FileName;

            // Set the AWS S3 bucket name
            string bucketName = AWSPrefix;
            var config = new AmazonS3Config
            {
                RegionEndpoint = Amazon.RegionEndpoint.APSouth1,
                LogResponse = true,
                LogMetrics = true,
            };
            // Initialize AWS credentials and client
            var awsCredentials = new BasicAWSCredentials(AWSAccessKey, AWSSecretKey);
            using (var amazonS3client = new AmazonS3Client(awsCredentials, Amazon.RegionEndpoint.APSouth1))
            {
                try
                {
                    // Create a request to upload the file to S3
                    var putObjectRequest = new PutObjectRequest
                    {
                        BucketName = bucketName,
                        ContentType = postedFile.ContentType,
                        Key = postedFileName,
                        InputStream = postedFile.OpenReadStream() // Use OpenReadStream with IFormFile
                    };

                    // Send the request to upload the file (asynchronously)
                    var putObjectResponse = await amazonS3client.PutObjectAsync(putObjectRequest);

                    // Check if the upload was successful
                    if (putObjectResponse.HttpStatusCode == HttpStatusCode.OK)
                    {
                        rowsAffected = 1; // Upload successful
                    }
                    else
                    {
                        rowsAffected = 0;
                        Console.WriteLine("Upload failed with status code: " + putObjectResponse.HttpStatusCode);
                    }
                }
                catch (AmazonS3Exception ex)
                {
                    // Handle S3-specific exceptions here
                    Console.WriteLine($"S3 Error: {ex.Message} - {ex.StatusCode}");
                }
                catch (Exception ex)
                {
                    // Handle general exceptions here
                    Console.WriteLine($"General error: {ex.Message}");
                }
            }

            return rowsAffected;
        }



        public bool IsFileExistOrNot1(string FileName, string AWSBucketPrifix)
        {
            //FileName = "BonusImg.png";
            bool AWSObjectURL = false;
            string bucketname1 = string.Empty;
            string Prefix1 = FileName;
            if (S3BucketName.Contains("/") == true)
            {
                bucketname1 = (S3BucketName.Split('/')[0] + "/");
                Prefix1 = (S3BucketName.Split('/')[1]) + "/" + AWSBucketPrifix;
            }
            else
            {
                bucketname1 = (S3BucketName + "/");
            }
            if (!string.IsNullOrEmpty(FileName))
            {
                try
                {

                    using (AmazoneS3client = new AmazonS3Client(AWSAccessKey, AWSSecretKey, RegionEndpoint.APSouth1))
                    {

                        try
                        {
                            ListObjectsRequest request = new ListObjectsRequest
                            {
                                BucketName = bucketname1,
                                Prefix = Prefix1
                                // Key = FileName
                            };
                            //Task<GetObjectResponse> response1 = AmazoneS3client.GetObjectAsync(getObjectRequest);
                            //Stream ThisStrean = Stream.Null;
                            Task<ListObjectsResponse> response1 = AmazoneS3client.ListObjectsAsync(request);
                            List<S3Object> FilesInBucket = response1.Result.S3Objects.ToList();
                            S3Object FileInfo = null;
                            if (FileName != "")
                            {
                                FileInfo = FilesInBucket.Where(e => e.Key == Prefix1 + '/' + FileName).Select(e => e).FirstOrDefault();
                            }
                            else
                            {
                                FileInfo = FilesInBucket.Where(e => e.Key == Prefix1).Select(e => e).FirstOrDefault();
                            }
                            if (FileInfo != null)
                            {
                                AWSObjectURL = true;
                            }
                            else
                            {
                                AWSObjectURL = false;
                            }
                        }
                        catch (AmazonS3Exception ex)
                        {
                            //write_time.WriteLoggedInOutTimeToFile("1", DateTime.Now.TimeOfDay.ToString(), DateTime.Now.TimeOfDay.ToString(), HostingEnvironment.ContentRootPath, ControllerContext.ActionDescriptor.ControllerName, ControllerContext.ActionDescriptor.ActionName, ex.Source);

                        }
                    }
                }
                catch (AmazonS3Exception ex)
                {
                    // write_time.WriteLoggedInOutTimeToFile("1", DateTime.Now.TimeOfDay.ToString(), DateTime.Now.TimeOfDay.ToString(), HostingEnvironment.ContentRootPath, ControllerContext.ActionDescriptor.ControllerName, ControllerContext.ActionDescriptor.ActionName, ex.Source);

                }

            }
            return AWSObjectURL;
        }
        public static DataTable ExcelReader(Stream fileStream, string fileName)
        {
            string fileExtension = Path.GetExtension(fileName).ToLower();
            DataTable dt = new DataTable();

            if (fileExtension == ".xls" || fileExtension == ".xlsx")
            {
                // Use EPPlus for Excel reading without needing a temporary file
                using (var package = new ExcelPackage(fileStream))
                {
                    ExcelWorksheet worksheet = package.Workbook.Worksheets[0];

                    // Add columns from the first row (header)
                    for (int col = 1; col <= worksheet.Dimension.End.Column; col++)
                    {
                        dt.Columns.Add(worksheet.Cells[1, col].Text);
                    }

                    // Add rows from the worksheet
                    for (int row = 2; row <= worksheet.Dimension.End.Row; row++)
                    {
                        DataRow dr = dt.NewRow();
                        for (int col = 1; col <= worksheet.Dimension.End.Column; col++)
                        {
                            dr[col - 1] = worksheet.Cells[row, col].Text;
                        }
                        dt.Rows.Add(dr);
                    }
                }
            }
            else if (fileExtension == ".csv")
            {
                // Handle CSV parsing
                using (StreamReader reader = new StreamReader(fileStream))
                {
                    string headerLine = reader.ReadLine();
                    if (headerLine != null)
                    {
                        string[] columns = headerLine.Split(',');

                        // Add columns to the DataTable
                        foreach (var column in columns)
                        {
                            dt.Columns.Add(column.Trim());
                        }

                        string line;
                        while ((line = reader.ReadLine()) != null)
                        {
                            string[] values = line.Split(',');
                            DataRow row = dt.NewRow();
                            for (int i = 0; i < values.Length; i++)
                            {
                                row[i] = values[i].Trim().Replace('"', ' ');
                            }
                            dt.Rows.Add(row);
                        }
                    }
                }
            }
            else
            {
                throw new NotSupportedException("File format not supported. Only .xls, .xlsx, and .csv files are supported.");
            }

            return dt;
        }
        public static int GetObjectID(string name)
        {
            int objectID = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string connectionString = ConfigurationManager.ConnectionStrings["YANMAR"].ConnectionString;
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand("GetObjectID_SP", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Add the input parameter for the stored procedure
                        cmd.Parameters.AddWithValue("@Name", name);

                        conn.Open();

                        // Execute the stored procedure and retrieve the Object_ID
                        objectID = (int)(cmd.ExecuteScalar() ?? 0);
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return objectID;
        }
        #endregion


        #region :::BuildACLObject:::
        public static List<ACLProperties> BuildACLObject(GetWorkOrderDetailsList Obj, string ConnectionString, int LogException)
        {
            List<ACLProperties> ACLobjList = new List<ACLProperties>();
            string query = string.Empty;
            try
            {
                int UserID = Obj.User_ID;
                query = "select object_id,SUM(cast(RoleObject_Create as int)) RoleObject_Create ,SUM(cast(RoleObject_Read as int)) RoleObject_Read,sum(cast(roleobject_update as int)) RoleObject_Update, SUM(cast(roleobject_delete as int)) RoleObject_Delete, SUM(cast(roleobject_print as int)) RoleObject_Print, SUM(cast(roleobject_export as int)) RoleObject_Export ,SUM(cast(roleobject_import as int)) RoleObject_Import from GNM_roleobject where Role_ID in (select role_id from gnm_userrole where USER_ID=" + UserID + ") group by OBJECT_ID";
                using (SqlConnection conn = new SqlConnection(ConnectionString))
                {


                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.Text;


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = command.ExecuteReader())
                            {

                                while (reader.Read())
                                {
                                    ACLProperties aclObj = new ACLProperties
                                    {
                                        Object_ID = reader.GetInt32(reader.GetOrdinal("object_id")),
                                        RoleObject_Create = reader.IsDBNull(reader.GetOrdinal("RoleObject_Create")) ? 0 : reader.GetInt32(reader.GetOrdinal("RoleObject_Create")),
                                        RoleObject_Read = reader.IsDBNull(reader.GetOrdinal("RoleObject_Read")) ? 0 : reader.GetInt32(reader.GetOrdinal("RoleObject_Read")),
                                        RoleObject_Update = reader.IsDBNull(reader.GetOrdinal("RoleObject_Update")) ? 0 : reader.GetInt32(reader.GetOrdinal("RoleObject_Update")),
                                        RoleObject_Delete = reader.IsDBNull(reader.GetOrdinal("RoleObject_Delete")) ? 0 : reader.GetInt32(reader.GetOrdinal("RoleObject_Delete")),
                                        RoleObject_Print = reader.IsDBNull(reader.GetOrdinal("RoleObject_Print")) ? 0 : reader.GetInt32(reader.GetOrdinal("RoleObject_Print")),
                                        RoleObject_Export = reader.IsDBNull(reader.GetOrdinal("RoleObject_Export")) ? 0 : reader.GetInt32(reader.GetOrdinal("RoleObject_Export")),
                                        RoleObject_Import = reader.IsDBNull(reader.GetOrdinal("RoleObject_Import")) ? 0 : reader.GetInt32(reader.GetOrdinal("RoleObject_Import"))
                                    };

                                    ACLobjList.Add(aclObj); // Add to the list
                                }

                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log exception
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                    }
                    finally
                    {
                        command?.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                return ACLobjList;
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return ACLobjList;
        }
        #endregion


        #region  GetMyQueue
        public static List<Indicator> GetMyQueue(int companyID, int workFlowID, int userID, string connString, int LogException)
        {
            object obj = null;
            object obj2 = obj;
            List<Indicator> list = new List<Indicator>();
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {


                    SqlCommand command = null;
                    string query = @"
               SELECT DISTINCT a.Transaction_ID, a.Locked_Ind
               FROM WF_WFCase_Progress a
               INNER JOIN WF_WFStepLink b ON a.WorkFlow_ID = b.WorkFlow_ID
               WHERE a.Action_Chosen IS NULL
               AND a.WorkFlow_ID = @WorkFlowID
               AND a.Addresse_ID = @UserID
               AND a.Addresse_Flag = 1
               AND b.Company_ID = @CompanyID";


                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.Text;
                            command.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                            command.Parameters.AddWithValue("@UserID", userID);
                            command.Parameters.AddWithValue("@CompanyID", companyID);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = command.ExecuteReader())
                            {

                                while (reader.Read())
                                {
                                    Indicator indicator = new Indicator();
                                    indicator.TransactionID = reader.GetInt32(0); // Transaction_ID
                                    indicator.IndicatorType = 0;
                                    indicator.IsLock = Convert.ToBoolean(reader.GetInt32(1)); // Locked_Ind
                                    list.Add(indicator);
                                }

                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log exception
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                    }
                    finally
                    {
                        command?.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                foreach (var item in list)
                {
                    Indicator indicator = new Indicator();
                    indicator.TransactionID = item.TransactionID;
                    indicator.IndicatorType = 0;
                    indicator.IsLock = Convert.ToBoolean(item.IsLock);
                    list.Add(indicator);
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return list;
        }
        #endregion



        #region  GetMyQueue
        public static bool AddAction(int ObjID)
        {
            bool IsAdd = false;
            try
            {

                if (ACLobjList != null)
                {
                    // Check if the list contains the specific object and validate the role
                    var aclObj = ACLobjList.FirstOrDefault(obj => obj.Object_ID == ObjID);
                    if (aclObj != null && aclObj.RoleObject_Create > 0)
                    {
                        IsAdd = true;
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle any exceptions here, e.g., log or rethrow
                Console.WriteLine($"Error: {ex.Message}");
            }

            return IsAdd;
        }
        #endregion


        #region GetWorkFlowID
        /// <summary>
        /// GetWorkFlowID
        /// </summary>
        /// <param name="WorkFlowName"></param>
        /// <param name="DBName"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static int GetWorkFlowID(string WorkFlowName, string DBName, string connString, int LogException)
        {
            int workflowID = 0;
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {


                    SqlCommand command = null;
                    string query = "SELECT WorkFlow_ID FROM GNM_WorkFlow WHERE WorkFlow_Name = @WorkFlowName";


                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.Text;
                            command.Parameters.AddWithValue("@WorkFlowName", WorkFlowName);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = command.ExecuteReader())
                            {

                                if (reader.Read()) // Check if any row is returned
                                {
                                    workflowID = reader.IsDBNull(0) ? 0 : reader.GetInt32(0); // Read the WorkFlow_ID
                                }

                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log exception
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                    }
                    finally
                    {
                        command?.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                return workflowID;
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

                return 0;
            }
        }
        #endregion


        #region ::: To Check User belongs to Add Role for all transactions:::
        /// <summary>
        /// To Check User belongs to Add Role for all transactions
        /// </summary>
        /// <returns>...</returns>
        public static bool CheckAddPermissionsofTransactions(string ObjName, string WFName, int Company_ID, int User_ID, string connString, int LogException)
        {
            int ObjectID = 0;
            int WorkFlowID = 0;
            int CompanyID = 0;
            var jr = default(dynamic);
            bool IsTranAdd = false;
            string DBName = ConfigurationManager.AppSettings.Get("DbName");
            try
            {
                CompanyID = Convert.ToInt32(Company_ID);

                User_ID = Convert.ToInt32(User_ID);

                ObjectID = GetObjectID(ObjName);
                WorkFlowID = GetWorkFlowID(WFName, DBName, connString, LogException);
                IsTranAdd = chkIsAddRecords(ObjectID, WorkFlowID, CompanyID, User_ID, connString, LogException);

                //jr = new
                //{
                //    IsTranAdd = IsTranAdd,
                //};
                return IsTranAdd;

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jr, JsonRequestBehavior.AllowGet);
            return IsTranAdd;
        }
        #endregion

        #region 
        public static bool chkIsAddRecords(int ObjectID, int WorkFlowID, int CompanyID, int UserID, string connString, int LogException)
        {
            bool flag = true;
            try
            {
                int num = 0;
                IEnumerable<WF_WFStepLink> outer = null;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = @"
         SELECT COUNT(a.WFStepLink_ID) 
         FROM WF_WFStepLink a
         INNER JOIN WF_WFRoleUser b ON a.Addresse_WFRole_ID = b.WFRole_ID
         INNER JOIN WF_WFSteps c ON a.FrmWFSteps_ID = c.WFSteps_ID
         WHERE a.WorkFlow_ID = @WorkFlowID
         AND a.Company_ID = @CompanyID
         AND b.UserID = @UserID
         AND c.WFStepType_ID = 1";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.Text;
                            command.Parameters.AddWithValue("@WorkFlowID", WorkFlowID);
                            command.Parameters.AddWithValue("@CompanyID", CompanyID);
                            command.Parameters.AddWithValue("@UserID", UserID);



                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            num = Convert.ToInt32(command.ExecuteScalar());
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log exception
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                    }
                    finally
                    {
                        command?.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                flag = num > 0;
            }
            catch (Exception ex)
            {
                flag = false;
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return flag;
        }
        #endregion



        public static List<Indicator> GetGroupQueue(int companyID, string Connstring, int workFlowID, int userID)
        {
            string text = string.Empty;
            List<Indicator> list = new List<Indicator>();
            object obj = null;
            dynamic val = obj;

            try
            {
                // Using ADO.NET to fetch WFRole_IDs for the given userID
                List<int> list2 = new List<int>();
                using (SqlConnection connection = new SqlConnection(Connstring))
                {
                    connection.Open();
                    using (SqlCommand cmd = new SqlCommand("SELECT WFRole_ID FROM WF_WFRoleUser WHERE UserID = @userID ORDER BY WFRoleUser_ID DESC", connection))
                    {
                        cmd.Parameters.AddWithValue("@userID", userID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                list2.Add(reader.GetInt32(0)); // Add WFRole_ID to list2
                            }
                        }
                    }
                }

                // Create comma-separated string of WFRole_IDs
                for (int j = 0; j < list2.Count; j++)
                {
                    text = (j == list2.Count - 1) ? (text + list2[j]) : (text + list2[j] + ",");
                }

                // ADO.NET SQL to retrieve data from GNM_WFCase_Progress
                List<WF_WFCase_Progress> outer = new List<WF_WFCase_Progress>();
                string sql = "SELECT * FROM GNM_WFCase_Progress WHERE (Action_Chosen = 0 OR Action_Chosen IS NULL) AND WorkFlow_ID = @workFlowID AND Addresse_ID IN (" + text + ") AND Addresse_Flag = 0";
                using (SqlConnection connection = new SqlConnection(ConfigurationManager.ConnectionStrings["YourConnectionString"].ConnectionString))
                {
                    connection.Open();
                    using (SqlCommand cmd = new SqlCommand(sql, connection))
                    {
                        cmd.Parameters.AddWithValue("@workFlowID", workFlowID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                WF_WFCase_Progress progress = new WF_WFCase_Progress
                                {
                                    Transaction_ID = reader["Transaction_ID"] != DBNull.Value ? (int)reader["Transaction_ID"] : 0,
                                    WorkFlow_ID = reader["WorkFlow_ID"] != DBNull.Value ? (int)reader["WorkFlow_ID"] : 0,
                                    // Add other necessary properties
                                };
                                outer.Add(progress);
                            }
                        }
                    }
                }

                // ADO.NET SQL to retrieve data from WF_WFStepLink and join with the previous result
                using (SqlConnection connection = new SqlConnection(Connstring))
                {
                    connection.Open();
                    string innerSql = "SELECT WorkFlow_ID FROM WF_WFStepLink WHERE Company_ID = @companyID AND WorkFlow_ID = @workFlowID";
                    using (SqlCommand cmd = new SqlCommand(innerSql, connection))
                    {
                        cmd.Parameters.AddWithValue("@companyID", companyID);
                        cmd.Parameters.AddWithValue("@workFlowID", workFlowID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                int innerWorkFlowID = reader.GetInt32(0);

                                var result = (from a in outer
                                              where a.WorkFlow_ID == innerWorkFlowID
                                              select new { a.Transaction_ID }).Distinct();

                                foreach (var item in result)
                                {
                                    Indicator indicator = new Indicator
                                    {
                                        TransactionID = item.Transaction_ID,
                                        IndicatorType = 0,
                                        IsLock = false
                                    };
                                    list.Add(indicator);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return list;
        }


        #region ::: getRegionName Uday Kumar J B 14-11-2024:::
        /// <summary>
        /// To get Region Name
        /// </summary>
        /// <returns>...</returns>
        public static string getRegionName(string connString, int LogException, int userLanguageID, int generalLanguageID, int? Branch_ID)
        {
            string RegionName = "";
            int? RegionID = null;

            try
            {
                // Establish connection
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Step 1: Get RegionID from Branch ID
                    using (SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetBranchRegionID", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@Branch_ID", Branch_ID.HasValue ? (object)Branch_ID.Value : DBNull.Value);

                        var regionIdResult = command.ExecuteScalar();
                        if (regionIdResult != null && regionIdResult != DBNull.Value)
                        {
                            RegionID = Convert.ToInt32(regionIdResult);
                        }
                    }

                    // Step 2: Get RegionName based on RegionID and Language
                    if (userLanguageID == generalLanguageID)
                    {
                        using (SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetRegionNameById", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@RegionID", RegionID.HasValue ? (object)RegionID.Value : 0);

                            var regionNameResult = command.ExecuteScalar();
                            if (regionNameResult != null && regionNameResult != DBNull.Value)
                            {
                                RegionName = regionNameResult.ToString();
                            }
                        }
                    }
                    else
                    {
                        using (SqlCommand command = new SqlCommand("SP_AMERP_HelpDesk_GetRegionNameByIdAndLanguage", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@RegionID", RegionID.HasValue ? (object)RegionID.Value : 0);
                            command.Parameters.AddWithValue("@LanguageID", userLanguageID);

                            var regionNameResult = command.ExecuteScalar();
                            if (regionNameResult != null && regionNameResult != DBNull.Value)
                            {
                                RegionName = regionNameResult.ToString();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return RegionName;
        }
        #endregion


        public static string getRegionNamebyCompany(string connString, int LogException, int userLanguageID, int generalLanguageID, int? Company_ID)
        {
            string regionName = "";
            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Step 1: Get RegionID for the given Company_ID
                    int? regionID = null;
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetRegionIDByCompanyCommon", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID ?? (object)DBNull.Value);

                        SqlParameter regionIDParam = new SqlParameter("@Region_ID", SqlDbType.Int) { Direction = ParameterDirection.Output };
                        cmd.Parameters.Add(regionIDParam);

                        cmd.ExecuteNonQuery();
                        regionID = regionIDParam.Value != DBNull.Value ? (int?)regionIDParam.Value : null;
                    }

                    // Step 2: Get RegionName based on RegionID and Language_ID
                    using (SqlCommand cmd = new SqlCommand("SP_AMERP_HelpDesk_GetRegionNameByRegionIDAndLanguageCommon", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Region_ID", regionID ?? 0);
                        cmd.Parameters.AddWithValue("@Language_ID", userLanguageID == generalLanguageID ? generalLanguageID : userLanguageID);

                        SqlParameter regionNameParam = new SqlParameter("@RegionName", SqlDbType.NVarChar, 100) { Direction = ParameterDirection.Output };
                        cmd.Parameters.Add(regionNameParam);

                        cmd.ExecuteNonQuery();
                        regionName = regionNameParam.Value as string ?? "";
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return regionName;
        }

        public static string GenerateDMSPassword(string userPassword)
        {
            string AMP_SP = Common.AMP_SP();
            string pwdToHash = userPassword + AMP_SP; //  is our PARTSASSIST hard-coded salt
            string hashToStoreInDatabase = BCrypt.HashPassword(pwdToHash, BCrypt.GenerateSalt());
            return hashToStoreInDatabase;
        }
        public static string AMP_SP()
        {
            string AMP_SP = ConfigurationManager.AppSettings.Get("AMP_SP");
            return AMP_SP;
        }


        public static bool CheckPreffixSuffix(string constring,int LogException,int CompanyID, int BranchID, string ObjectName)
        {
            //LS.LogSheetExporter.LogToTextFile(Convert.ToInt32(1), "DK188", "", "DK33-SUCC Call");
            // Assuming you have a method to get ObjectID from ObjectName
            int ObjectID = GetObjectID(ObjectName);

            bool result = false;

            using (SqlConnection conn = new SqlConnection(constring))
            {
                conn.Open();

                // Query to check for branch-specific prefix/suffix
                string queryBranch = @"
                SELECT COUNT(*) 
                FROM GNM_PrefixSuffix 
                WHERE Branch_ID = @BranchID 
                  AND Object_ID = @ObjectID 
                  AND FromDate <= GETDATE() 
                  AND ToDate >= GETDATE()";

                using (SqlCommand cmd = new SqlCommand(queryBranch, conn))
                {
                    cmd.Parameters.AddWithValue("@BranchID", BranchID);
                    cmd.Parameters.AddWithValue("@ObjectID", ObjectID);

                    int count = (int)cmd.ExecuteScalar();
                    result = count > 0;
                }

                // If no match for branch-specific, check company-level prefix/suffix
                if (!result)
                {
                    string queryCompany = @"
                SELECT COUNT(*) 
                FROM GNM_PrefixSuffix 
                WHERE Company_ID = @CompanyID 
                  AND Branch_ID IS NULL 
                  AND Object_ID = @ObjectID 
                  AND FromDate <= GETDATE() 
                  AND ToDate >= GETDATE()";

                    using (SqlCommand cmd = new SqlCommand(queryCompany, conn))
                    {
                        cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                        cmd.Parameters.AddWithValue("@ObjectID", ObjectID);

                        int count = (int)cmd.ExecuteScalar();
                        result = count > 0;
                    }
                }
            }
            //LS.LogSheetExporter.LogToTextFile(Convert.ToInt32(1), Convert.ToString(result), "", "DK66SUCC Call");
            return result;
        }





        public class WF_WFCase_Progress
        {
            public int WFCaseProgress_ID { get; set; }

            public int WorkFlow_ID { get; set; }

            public int Transaction_ID { get; set; }

            public int WFSteps_ID { get; set; }

            public int? Addresse_ID { get; set; }

            public byte Addresse_Flag { get; set; }

            public DateTime Received_Time { get; set; }

            public int? Actioned_By { get; set; }

            public DateTime? Action_Time { get; set; }

            public int? Action_Chosen { get; set; }

            public string Action_Remarks { get; set; }

            public bool? Locked_Ind { get; set; }

            public int? WFNextStep_ID { get; set; }
        }









        public int GetVinShortNumber(string GetShortCode, int DCType, int UserId, string constring)
        {
            string ConnectionString = constring;
            int ShortCode = 0;
            try
            {
                using (SqlConnection ConnSql = new SqlConnection(ConnectionString))
                {
                    string Query1 = ("SELECT top 1 cast(VinshortNumber as int) FROM MA_Vehicle WHERE VinshortCode = @GetShortCode");
                    SqlDataReader DataReader = null;
                    //string Query = ("SELECT Part_Assembly_No, Part_Assembly_Description FROM MA_Part_Assembly WHERE Part_Assembly_ID = " + Part_Assembly_ID);
                    using (SqlCommand sqlComm = new SqlCommand(Query1, ConnSql))
                    {
                        sqlComm.Parameters.AddWithValue("@GetShortCode", GetShortCode);
                        if (ConnSql.State == ConnectionState.Closed)
                        {
                            ConnSql.Open();
                        }
                        DataReader = sqlComm.ExecuteReader();
                        if (DataReader.HasRows)
                        {
                            while (DataReader.Read())
                            {
                                ShortCode = Convert.ToInt32(DataReader[0].ToString());
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                var Request = new System.Net.Http.HttpRequestMessage();
                //ExceptionLogger.ErrorLog(e, Request, Convert.ToInt32(UserId), "");
            }
            return ShortCode;
        }

        public class BCrypt
        {
            private const int GENSALT_DEFAULT_LOG2_ROUNDS = 10;

            private const int BCRYPT_SALT_LEN = 16;

            private const int BLOWFISH_NUM_ROUNDS = 16;

            private static readonly uint[] p_orig = new uint[18]
            {
        608135816u, 2242054355u, 320440878u, 57701188u, 2752067618u, 698298832u, 137296536u, 3964562569u, 1160258022u, 953160567u,
        3193202383u, 887688300u, 3232508343u, 3380367581u, 1065670069u, 3041331479u, 2450970073u, 2306472731u
            };

            private static readonly uint[] s_orig = new uint[1024]
            {
        3509652390u, 2564797868u, 805139163u, 3491422135u, 3101798381u, 1780907670u, 3128725573u, 4046225305u, 614570311u, 3012652279u,
        134345442u, 2240740374u, 1667834072u, 1901547113u, 2757295779u, 4103290238u, 227898511u, 1921955416u, 1904987480u, 2182433518u,
        2069144605u, 3260701109u, 2620446009u, 720527379u, 3318853667u, 677414384u, 3393288472u, 3101374703u, 2390351024u, 1614419982u,
        1822297739u, 2954791486u, 3608508353u, 3174124327u, 2024746970u, 1432378464u, 3864339955u, 2857741204u, 1464375394u, 1676153920u,
        1439316330u, 715854006u, 3033291828u, 289532110u, 2706671279u, 2087905683u, 3018724369u, 1668267050u, 732546397u, 1947742710u,
        3462151702u, 2609353502u, 2950085171u, 1814351708u, 2050118529u, 680887927u, 999245976u, 1800124847u, 3300911131u, 1713906067u,
        1641548236u, 4213287313u, 1216130144u, 1575780402u, 4018429277u, 3917837745u, 3693486850u, 3949271944u, 596196993u, 3549867205u,
        258830323u, 2213823033u, 772490370u, 2760122372u, 1774776394u, 2652871518u, 566650946u, 4142492826u, 1728879713u, 2882767088u,
        1783734482u, 3629395816u, 2517608232u, 2874225571u, 1861159788u, 326777828u, 3124490320u, 2130389656u, 2716951837u, 967770486u,
        1724537150u, 2185432712u, 2364442137u, 1164943284u, 2105845187u, 998989502u, 3765401048u, 2244026483u, 1075463327u, 1455516326u,
        1322494562u, 910128902u, 469688178u, 1117454909u, 936433444u, 3490320968u, 3675253459u, 1240580251u, 122909385u, 2157517691u,
        634681816u, 4142456567u, 3825094682u, 3061402683u, 2540495037u, 79693498u, 3249098678u, 1084186820u, 1583128258u, 426386531u,
        1761308591u, 1047286709u, 322548459u, 995290223u, 1845252383u, 2603652396u, 3431023940u, 2942221577u, 3202600964u, 3727903485u,
        1712269319u, 422464435u, 3234572375u, 1170764815u, 3523960633u, 3117677531u, 1434042557u, 442511882u, 3600875718u, 1076654713u,
        1738483198u, 4213154764u, 2393238008u, 3677496056u, 1014306527u, 4251020053u, 793779912u, 2902807211u, 842905082u, 4246964064u,
        1395751752u, 1040244610u, 2656851899u, 3396308128u, 445077038u, 3742853595u, 3577915638u, 679411651u, 2892444358u, 2354009459u,
        1767581616u, 3150600392u, 3791627101u, 3102740896u, 284835224u, 4246832056u, 1258075500u, 768725851u, 2589189241u, 3069724005u,
        3532540348u, 1274779536u, 3789419226u, 2764799539u, 1660621633u, 3471099624u, 4011903706u, 913787905u, 3497959166u, 737222580u,
        2514213453u, 2928710040u, 3937242737u, 1804850592u, 3499020752u, 2949064160u, 2386320175u, 2390070455u, 2415321851u, 4061277028u,
        2290661394u, 2416832540u, 1336762016u, 1754252060u, 3520065937u, 3014181293u, 791618072u, 3188594551u, 3933548030u, 2332172193u,
        3852520463u, 3043980520u, 413987798u, 3465142937u, 3030929376u, 4245938359u, 2093235073u, 3534596313u, 375366246u, 2157278981u,
        2479649556u, 555357303u, 3870105701u, 2008414854u, 3344188149u, 4221384143u, 3956125452u, 2067696032u, 3594591187u, 2921233993u,
        2428461u, 544322398u, 577241275u, 1471733935u, 610547355u, 4027169054u, 1432588573u, 1507829418u, 2025931657u, 3646575487u,
        545086370u, 48609733u, 2200306550u, 1653985193u, 298326376u, 1316178497u, 3007786442u, 2064951626u, 458293330u, 2589141269u,
        3591329599u, 3164325604u, 727753846u, 2179363840u, 146436021u, 1461446943u, 4069977195u, 705550613u, 3059967265u, 3887724982u,
        4281599278u, 3313849956u, 1404054877u, 2845806497u, 146425753u, 1854211946u, 1266315497u, 3048417604u, 3681880366u, 3289982499u,
        2909710000u, 1235738493u, 2632868024u, 2414719590u, 3970600049u, 1771706367u, 1449415276u, 3266420449u, 422970021u, 1963543593u,
        2690192192u, 3826793022u, 1062508698u, 1531092325u, 1804592342u, 2583117782u, 2714934279u, 4024971509u, 1294809318u, 4028980673u,
        1289560198u, 2221992742u, 1669523910u, 35572830u, 157838143u, 1052438473u, 1016535060u, 1802137761u, 1753167236u, 1386275462u,
        3080475397u, 2857371447u, 1040679964u, 2145300060u, 2390574316u, 1461121720u, 2956646967u, 4031777805u, 4028374788u, 33600511u,
        2920084762u, 1018524850u, 629373528u, 3691585981u, 3515945977u, 2091462646u, 2486323059u, 586499841u, 988145025u, 935516892u,
        3367335476u, 2599673255u, 2839830854u, 265290510u, 3972581182u, 2759138881u, 3795373465u, 1005194799u, 847297441u, 406762289u,
        1314163512u, 1332590856u, 1866599683u, 4127851711u, 750260880u, 613907577u, 1450815602u, 3165620655u, 3734664991u, 3650291728u,
        3012275730u, 3704569646u, 1427272223u, 778793252u, 1343938022u, 2676280711u, 2052605720u, 1946737175u, 3164576444u, 3914038668u,
        3967478842u, 3682934266u, 1661551462u, 3294938066u, 4011595847u, 840292616u, 3712170807u, 616741398u, 312560963u, 711312465u,
        1351876610u, 322626781u, 1910503582u, 271666773u, 2175563734u, 1594956187u, 70604529u, 3617834859u, 1007753275u, 1495573769u,
        4069517037u, 2549218298u, 2663038764u, 504708206u, 2263041392u, 3941167025u, 2249088522u, 1514023603u, 1998579484u, 1312622330u,
        694541497u, 2582060303u, 2151582166u, 1382467621u, 776784248u, 2618340202u, 3323268794u, 2497899128u, 2784771155u, 503983604u,
        4076293799u, 907881277u, 423175695u, 432175456u, 1378068232u, 4145222326u, 3954048622u, 3938656102u, 3820766613u, 2793130115u,
        2977904593u, 26017576u, 3274890735u, 3194772133u, 1700274565u, 1756076034u, 4006520079u, 3677328699u, 720338349u, 1533947780u,
        354530856u, 688349552u, 3973924725u, 1637815568u, 332179504u, 3949051286u, 53804574u, 2852348879u, 3044236432u, 1282449977u,
        3583942155u, 3416972820u, 4006381244u, 1617046695u, 2628476075u, 3002303598u, 1686838959u, 431878346u, 2686675385u, 1700445008u,
        1080580658u, 1009431731u, 832498133u, 3223435511u, 2605976345u, 2271191193u, 2516031870u, 1648197032u, 4164389018u, 2548247927u,
        300782431u, 375919233u, 238389289u, 3353747414u, 2531188641u, 2019080857u, 1475708069u, 455242339u, 2609103871u, 448939670u,
        3451063019u, 1395535956u, 2413381860u, 1841049896u, 1491858159u, 885456874u, 4264095073u, 4001119347u, 1565136089u, 3898914787u,
        1108368660u, 540939232u, 1173283510u, 2745871338u, 3681308437u, 4207628240u, 3343053890u, 4016749493u, 1699691293u, 1103962373u,
        3625875870u, 2256883143u, 3830138730u, 1031889488u, 3479347698u, 1535977030u, 4236805024u, 3251091107u, 2132092099u, 1774941330u,
        1199868427u, 1452454533u, 157007616u, 2904115357u, 342012276u, 595725824u, 1480756522u, 206960106u, 497939518u, 591360097u,
        863170706u, 2375253569u, 3596610801u, 1814182875u, 2094937945u, 3421402208u, 1082520231u, 3463918190u, 2785509508u, 435703966u,
        3908032597u, 1641649973u, 2842273706u, 3305899714u, 1510255612u, 2148256476u, 2655287854u, 3276092548u, 4258621189u, 236887753u,
        3681803219u, 274041037u, 1734335097u, 3815195456u, 3317970021u, 1899903192u, 1026095262u, 4050517792u, 356393447u, 2410691914u,
        3873677099u, 3682840055u, 3913112168u, 2491498743u, 4132185628u, 2489919796u, 1091903735u, 1979897079u, 3170134830u, 3567386728u,
        3557303409u, 857797738u, 1136121015u, 1342202287u, 507115054u, 2535736646u, 337727348u, 3213592640u, 1301675037u, 2528481711u,
        1895095763u, 1721773893u, 3216771564u, 62756741u, 2142006736u, 835421444u, 2531993523u, 1442658625u, 3659876326u, 2882144922u,
        676362277u, 1392781812u, 170690266u, 3921047035u, 1759253602u, 3611846912u, 1745797284u, 664899054u, 1329594018u, 3901205900u,
        3045908486u, 2062866102u, 2865634940u, 3543621612u, 3464012697u, 1080764994u, 553557557u, 3656615353u, 3996768171u, 991055499u,
        499776247u, 1265440854u, 648242737u, 3940784050u, 980351604u, 3713745714u, 1749149687u, 3396870395u, 4211799374u, 3640570775u,
        1161844396u, 3125318951u, 1431517754u, 545492359u, 4268468663u, 3499529547u, 1437099964u, 2702547544u, 3433638243u, 2581715763u,
        2787789398u, 1060185593u, 1593081372u, 2418618748u, 4260947970u, 69676912u, 2159744348u, 86519011u, 2512459080u, 3838209314u,
        1220612927u, 3339683548u, 133810670u, 1090789135u, 1078426020u, 1569222167u, 845107691u, 3583754449u, 4072456591u, 1091646820u,
        628848692u, 1613405280u, 3757631651u, 526609435u, 236106946u, 48312990u, 2942717905u, 3402727701u, 1797494240u, 859738849u,
        992217954u, 4005476642u, 2243076622u, 3870952857u, 3732016268u, 765654824u, 3490871365u, 2511836413u, 1685915746u, 3888969200u,
        1414112111u, 2273134842u, 3281911079u, 4080962846u, 172450625u, 2569994100u, 980381355u, 4109958455u, 2819808352u, 2716589560u,
        2568741196u, 3681446669u, 3329971472u, 1835478071u, 660984891u, 3704678404u, 4045999559u, 3422617507u, 3040415634u, 1762651403u,
        1719377915u, 3470491036u, 2693910283u, 3642056355u, 3138596744u, 1364962596u, 2073328063u, 1983633131u, 926494387u, 3423689081u,
        2150032023u, 4096667949u, 1749200295u, 3328846651u, 309677260u, 2016342300u, 1779581495u, 3079819751u, 111262694u, 1274766160u,
        443224088u, 298511866u, 1025883608u, 3806446537u, 1145181785u, 168956806u, 3641502830u, 3584813610u, 1689216846u, 3666258015u,
        3200248200u, 1692713982u, 2646376535u, 4042768518u, 1618508792u, 1610833997u, 3523052358u, 4130873264u, 2001055236u, 3610705100u,
        2202168115u, 4028541809u, 2961195399u, 1006657119u, 2006996926u, 3186142756u, 1430667929u, 3210227297u, 1314452623u, 4074634658u,
        4101304120u, 2273951170u, 1399257539u, 3367210612u, 3027628629u, 1190975929u, 2062231137u, 2333990788u, 2221543033u, 2438960610u,
        1181637006u, 548689776u, 2362791313u, 3372408396u, 3104550113u, 3145860560u, 296247880u, 1970579870u, 3078560182u, 3769228297u,
        1714227617u, 3291629107u, 3898220290u, 166772364u, 1251581989u, 493813264u, 448347421u, 195405023u, 2709975567u, 677966185u,
        3703036547u, 1463355134u, 2715995803u, 1338867538u, 1343315457u, 2802222074u, 2684532164u, 233230375u, 2599980071u, 2000651841u,
        3277868038u, 1638401717u, 4028070440u, 3237316320u, 6314154u, 819756386u, 300326615u, 590932579u, 1405279636u, 3267499572u,
        3150704214u, 2428286686u, 3959192993u, 3461946742u, 1862657033u, 1266418056u, 963775037u, 2089974820u, 2263052895u, 1917689273u,
        448879540u, 3550394620u, 3981727096u, 150775221u, 3627908307u, 1303187396u, 508620638u, 2975983352u, 2726630617u, 1817252668u,
        1876281319u, 1457606340u, 908771278u, 3720792119u, 3617206836u, 2455994898u, 1729034894u, 1080033504u, 976866871u, 3556439503u,
        2881648439u, 1522871579u, 1555064734u, 1336096578u, 3548522304u, 2579274686u, 3574697629u, 3205460757u, 3593280638u, 3338716283u,
        3079412587u, 564236357u, 2993598910u, 1781952180u, 1464380207u, 3163844217u, 3332601554u, 1699332808u, 1393555694u, 1183702653u,
        3581086237u, 1288719814u, 691649499u, 2847557200u, 2895455976u, 3193889540u, 2717570544u, 1781354906u, 1676643554u, 2592534050u,
        3230253752u, 1126444790u, 2770207658u, 2633158820u, 2210423226u, 2615765581u, 2414155088u, 3127139286u, 673620729u, 2805611233u,
        1269405062u, 4015350505u, 3341807571u, 4149409754u, 1057255273u, 2012875353u, 2162469141u, 2276492801u, 2601117357u, 993977747u,
        3918593370u, 2654263191u, 753973209u, 36408145u, 2530585658u, 25011837u, 3520020182u, 2088578344u, 530523599u, 2918365339u,
        1524020338u, 1518925132u, 3760827505u, 3759777254u, 1202760957u, 3985898139u, 3906192525u, 674977740u, 4174734889u, 2031300136u,
        2019492241u, 3983892565u, 4153806404u, 3822280332u, 352677332u, 2297720250u, 60907813u, 90501309u, 3286998549u, 1016092578u,
        2535922412u, 2839152426u, 457141659u, 509813237u, 4120667899u, 652014361u, 1966332200u, 2975202805u, 55981186u, 2327461051u,
        676427537u, 3255491064u, 2882294119u, 3433927263u, 1307055953u, 942726286u, 933058658u, 2468411793u, 3933900994u, 4215176142u,
        1361170020u, 2001714738u, 2830558078u, 3274259782u, 1222529897u, 1679025792u, 2729314320u, 3714953764u, 1770335741u, 151462246u,
        3013232138u, 1682292957u, 1483529935u, 471910574u, 1539241949u, 458788160u, 3436315007u, 1807016891u, 3718408830u, 978976581u,
        1043663428u, 3165965781u, 1927990952u, 4200891579u, 2372276910u, 3208408903u, 3533431907u, 1412390302u, 2931980059u, 4132332400u,
        1947078029u, 3881505623u, 4168226417u, 2941484381u, 1077988104u, 1320477388u, 886195818u, 18198404u, 3786409000u, 2509781533u,
        112762804u, 3463356488u, 1866414978u, 891333506u, 18488651u, 661792760u, 1628790961u, 3885187036u, 3141171499u, 876946877u,
        2693282273u, 1372485963u, 791857591u, 2686433993u, 3759982718u, 3167212022u, 3472953795u, 2716379847u, 445679433u, 3561995674u,
        3504004811u, 3574258232u, 54117162u, 3331405415u, 2381918588u, 3769707343u, 4154350007u, 1140177722u, 4074052095u, 668550556u,
        3214352940u, 367459370u, 261225585u, 2610173221u, 4209349473u, 3468074219u, 3265815641u, 314222801u, 3066103646u, 3808782860u,
        282218597u, 3406013506u, 3773591054u, 379116347u, 1285071038u, 846784868u, 2669647154u, 3771962079u, 3550491691u, 2305946142u,
        453669953u, 1268987020u, 3317592352u, 3279303384u, 3744833421u, 2610507566u, 3859509063u, 266596637u, 3847019092u, 517658769u,
        3462560207u, 3443424879u, 370717030u, 4247526661u, 2224018117u, 4143653529u, 4112773975u, 2788324899u, 2477274417u, 1456262402u,
        2901442914u, 1517677493u, 1846949527u, 2295493580u, 3734397586u, 2176403920u, 1280348187u, 1908823572u, 3871786941u, 846861322u,
        1172426758u, 3287448474u, 3383383037u, 1655181056u, 3139813346u, 901632758u, 1897031941u, 2986607138u, 3066810236u, 3447102507u,
        1393639104u, 373351379u, 950779232u, 625454576u, 3124240540u, 4148612726u, 2007998917u, 544563296u, 2244738638u, 2330496472u,
        2058025392u, 1291430526u, 424198748u, 50039436u, 29584100u, 3605783033u, 2429876329u, 2791104160u, 1057563949u, 3255363231u,
        3075367218u, 3463963227u, 1469046755u, 985887462u
            };

            private static readonly uint[] bf_crypt_ciphertext = new uint[6] { 1332899944u, 1700884034u, 1701343084u, 1684370003u, 1668446532u, 1869963892u };

            private static readonly char[] base64_code = new char[64]
            {
        '.', '/', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H',
        'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R',
        'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'a', 'b',
        'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l',
        'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v',
        'w', 'x', 'y', 'z', '0', '1', '2', '3', '4', '5',
        '6', '7', '8', '9'
            };

            private static readonly int[] index_64 = new int[128]
            {
        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
        -1, -1, -1, -1, -1, -1, 0, 1, 54, 55,
        56, 57, 58, 59, 60, 61, 62, 63, -1, -1,
        -1, -1, -1, -1, -1, 2, 3, 4, 5, 6,
        7, 8, 9, 10, 11, 12, 13, 14, 15, 16,
        17, 18, 19, 20, 21, 22, 23, 24, 25, 26,
        27, -1, -1, -1, -1, -1, -1, 28, 29, 30,
        31, 32, 33, 34, 35, 36, 37, 38, 39, 40,
        41, 42, 43, 44, 45, 46, 47, 48, 49, 50,
        51, 52, 53, -1, -1, -1, -1, -1
            };

            private uint[] p;

            private uint[] s;

            private static string EncodeBase64(byte[] d, int length)
            {
                if (length <= 0 || length > d.Length)
                {
                    throw new ArgumentOutOfRangeException("length", length, null);
                }

                StringBuilder stringBuilder = new StringBuilder(length * 2);
                int num = 0;
                while (num < length)
                {
                    int num2 = d[num++] & 0xFF;
                    stringBuilder.Append(base64_code[(num2 >> 2) & 0x3F]);
                    num2 = (num2 & 3) << 4;
                    if (num >= length)
                    {
                        stringBuilder.Append(base64_code[num2 & 0x3F]);
                        break;
                    }

                    int num3 = d[num++] & 0xFF;
                    num2 |= (num3 >> 4) & 0xF;
                    stringBuilder.Append(base64_code[num2 & 0x3F]);
                    num2 = (num3 & 0xF) << 2;
                    if (num >= length)
                    {
                        stringBuilder.Append(base64_code[num2 & 0x3F]);
                        break;
                    }

                    num3 = d[num++] & 0xFF;
                    num2 |= (num3 >> 6) & 3;
                    stringBuilder.Append(base64_code[num2 & 0x3F]);
                    stringBuilder.Append(base64_code[num3 & 0x3F]);
                }

                return stringBuilder.ToString();
            }

            private static int Char64(char c)
            {
                return (c < '\0' || c > index_64.Length) ? (-1) : index_64[(uint)c];
            }

            private static byte[] DecodeBase64(string s, int maximumLength)
            {
                List<byte> list = new List<byte>(Math.Min(maximumLength, s.Length));
                if (maximumLength <= 0)
                {
                    throw new ArgumentOutOfRangeException("maximumLength", maximumLength, null);
                }

                int num = 0;
                int length = s.Length;
                int num2 = 0;
                while (num < length - 1 && num2 < maximumLength)
                {
                    int num3 = Char64(s[num++]);
                    int num4 = Char64(s[num++]);
                    if (num3 == -1 || num4 == -1)
                    {
                        break;
                    }

                    list.Add((byte)((num3 << 2) | ((num4 & 0x30) >> 4)));
                    if (++num2 >= maximumLength || num >= s.Length)
                    {
                        break;
                    }

                    int num5 = Char64(s[num++]);
                    if (num5 == -1)
                    {
                        break;
                    }

                    list.Add((byte)(((num4 & 0xF) << 4) | ((num5 & 0x3C) >> 2)));
                    if (++num2 >= maximumLength || num >= s.Length)
                    {
                        break;
                    }

                    int num6 = Char64(s[num++]);
                    list.Add((byte)(((num5 & 3) << 6) | num6));
                    num2++;
                }

                return list.ToArray();
            }

            private void Encipher(uint[] block, int offset)
            {
                uint num = block[offset];
                uint num2 = block[offset + 1];
                num ^= p[0];
                uint num3 = 0u;
                while (num3 <= 14)
                {
                    uint num4 = s[(num >> 24) & 0xFF];
                    num4 += s[0x100 | ((num >> 16) & 0xFF)];
                    num4 ^= s[0x200 | ((num >> 8) & 0xFF)];
                    num4 += s[0x300 | (num & 0xFF)];
                    num2 ^= num4 ^ p[++num3];
                    num4 = s[(num2 >> 24) & 0xFF];
                    num4 += s[0x100 | ((num2 >> 16) & 0xFF)];
                    num4 ^= s[0x200 | ((num2 >> 8) & 0xFF)];
                    num4 += s[0x300 | (num2 & 0xFF)];
                    num ^= num4 ^ p[++num3];
                }

                block[offset] = num2 ^ p[17];
                block[offset + 1] = num;
            }

            private static uint StreamToWord(byte[] data, ref int offset)
            {
                uint num = 0u;
                for (int i = 0; i < 4; i++)
                {
                    num = (num << 8) | data[offset];
                    offset = (offset + 1) % data.Length;
                }

                return num;
            }

            private void InitKey()
            {
                p = new uint[p_orig.Length];
                p_orig.CopyTo(p, 0);
                s = new uint[s_orig.Length];
                s_orig.CopyTo(s, 0);
            }

            private void Key(byte[] key)
            {
                uint[] array = new uint[2];
                int num = p.Length;
                int num2 = s.Length;
                int offset = 0;
                for (int i = 0; i < num; i++)
                {
                    p[i] ^= StreamToWord(key, ref offset);
                }

                for (int j = 0; j < num; j += 2)
                {
                    Encipher(array, 0);
                    p[j] = array[0];
                    p[j + 1] = array[1];
                }

                for (int k = 0; k < num2; k += 2)
                {
                    Encipher(array, 0);
                    s[k] = array[0];
                    s[k + 1] = array[1];
                }
            }

            private void EksKey(byte[] data, byte[] key)
            {
                uint[] array = new uint[2];
                int num = p.Length;
                int num2 = s.Length;
                int offset = 0;
                for (int i = 0; i < num; i++)
                {
                    p[i] ^= StreamToWord(key, ref offset);
                }

                int offset2 = 0;
                for (int j = 0; j < num; j += 2)
                {
                    array[0] ^= StreamToWord(data, ref offset2);
                    array[1] ^= StreamToWord(data, ref offset2);
                    Encipher(array, 0);
                    p[j] = array[0];
                    p[j + 1] = array[1];
                }

                for (int k = 0; k < num2; k += 2)
                {
                    array[0] ^= StreamToWord(data, ref offset2);
                    array[1] ^= StreamToWord(data, ref offset2);
                    Encipher(array, 0);
                    s[k] = array[0];
                    s[k + 1] = array[1];
                }
            }

            private byte[] CryptRaw(byte[] password, byte[] salt, int logRounds)
            {
                uint[] array = new uint[bf_crypt_ciphertext.Length];
                bf_crypt_ciphertext.CopyTo(array, 0);
                int num = array.Length;
                if (logRounds < 4 || logRounds > 31)
                {
                    throw new ArgumentOutOfRangeException("logRounds", logRounds, null);
                }

                int num2 = 1 << logRounds;
                if (salt.Length != 16)
                {
                    throw new ArgumentException("Invalid salt length.", "salt");
                }

                InitKey();
                EksKey(salt, password);
                for (int i = 0; i < num2; i++)
                {
                    Key(password);
                    Key(salt);
                }

                for (int j = 0; j < 64; j++)
                {
                    for (int k = 0; k < num >> 1; k++)
                    {
                        Encipher(array, k << 1);
                    }
                }

                byte[] array2 = new byte[num * 4];
                int l = 0;
                int num3 = 0;
                for (; l < num; l++)
                {
                    array2[num3++] = (byte)((array[l] >> 24) & 0xFFu);
                    array2[num3++] = (byte)((array[l] >> 16) & 0xFFu);
                    array2[num3++] = (byte)((array[l] >> 8) & 0xFFu);
                    array2[num3++] = (byte)(array[l] & 0xFFu);
                }

                return array2;
            }

            public static string HashPassword(string password, string salt)
            {
                if (password == null)
                {
                    throw new ArgumentNullException("password");
                }

                if (salt == null)
                {
                    throw new ArgumentNullException("salt");
                }

                char c = '\0';
                if (salt[0] != '$' || salt[1] != '2')
                {
                    throw new ArgumentException("Invalid salt version");
                }

                int num;
                if (salt[1] != '$')
                {
                    c = salt[2];
                    if (c != 'a' || salt[3] != '$')
                    {
                        throw new ArgumentException("Invalid salt revision");
                    }

                    num = 4;
                }
                else
                {
                    num = 3;
                }

                if (salt[num + 2] > '$')
                {
                    throw new ArgumentException("Missing salt rounds");
                }

                int num2 = int.Parse(salt.Substring(num, 2), NumberFormatInfo.InvariantInfo);
                byte[] bytes = Encoding.UTF8.GetBytes(password + ((c >= 'a') ? "\0" : string.Empty));
                byte[] array = DecodeBase64(salt.Substring(num + 3, 22), 16);
                BCrypt bCrypt = new BCrypt();
                byte[] d = bCrypt.CryptRaw(bytes, array, num2);
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.Append("$2");
                if (c >= 'a')
                {
                    stringBuilder.Append(c);
                }

                stringBuilder.Append('$');
                if (num2 < 10)
                {
                    stringBuilder.Append('0');
                }

                stringBuilder.Append(num2);
                stringBuilder.Append('$');
                stringBuilder.Append(EncodeBase64(array, array.Length));
                stringBuilder.Append(EncodeBase64(d, bf_crypt_ciphertext.Length * 4 - 1));
                return stringBuilder.ToString();
            }

            public static string GenerateSalt(int logRounds)
            {
                byte[] array = new byte[16];
                RandomNumberGenerator.Create().GetBytes(array);
                StringBuilder stringBuilder = new StringBuilder(array.Length * 2 + 8);
                stringBuilder.Append("$2a$");
                if (logRounds < 10)
                {
                    stringBuilder.Append('0');
                }

                stringBuilder.Append(logRounds);
                stringBuilder.Append('$');
                stringBuilder.Append(EncodeBase64(array, array.Length));
                return stringBuilder.ToString();
            }

            public static string GenerateSalt()
            {
                return GenerateSalt(10);
            }

            public static bool CheckPassword(string plaintext, string hashed)
            {
                return StringComparer.Ordinal.Compare(hashed, HashPassword(plaintext, hashed)) == 0;
            }
        }

        public static string getoperator(string operatorString)
        {
            switch (operatorString.ToUpper())
            {
                case "EQ":
                    return "=";
                case "NE":
                    return "<>";
                case "LIKE":
                    return "LIKE";
                case "GT":
                    return ">";
                case "LT":
                    return "<";
                case "GE":
                    return ">=";
                case "LE":
                    return "<=";
                default:
                    throw new ArgumentException("Invalid operator: " + operatorString);
            }
        }


        //private string azureStorageConnectionString = "DefaultEndpointsProtocol=https;AccountName=codepipeline;AccountKey=****************************************************************************************EndpointSuffix=core.windows.net"; // Set your Azure Storage connection string here
        //Azure Blob
        private string azureStorageConnectionString = "DefaultEndpointsProtocol=https;AccountName=codepipeline;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"; // Set your Azure Storage connection string here

        //string azureStorageConnectionString2 = "DefaultEndpointsProtocol = https;AccountName=codepipeline;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net";

        //public static DataTable ReadExcelFromBlobStorage(string blobUrl)
        //{
        //    var dataTable = new DataTable();
        //    try
        //    {
        //        using (var httpClient = new HttpClient())
        //        {
        //            using (var response = httpClient.GetAsync(blobUrl).Result)
        //            {
        //                using (var stream = response.Content.ReadAsStreamAsync().Result)
        //                {
        //                    using (var workbook = new XLWorkbook(stream))
        //                    {
        //                        var worksheet = workbook.Worksheet(1);

        //                        // Add columns to the DataTable using the column headers from the first row of the Excel file
        //                        var firstRow = worksheet.FirstRowUsed();
        //                        foreach (var cell in firstRow.Cells())
        //                        {
        //                            dataTable.Columns.Add(cell.Value.ToString());
        //                        }

        //                        // Add rows to the DataTable
        //                        foreach (var row in worksheet.RowsUsed().Skip(1))
        //                        {
        //                            var dataRow = dataTable.NewRow();
        //                            for (int i = 0; i < dataTable.Columns.Count; i++)
        //                            {
        //                                // Check if the cell is empty and set it to DBNull.Value if it is
        //                                var cellValue = row.Cell(i + 1).Value;
        //                                dataRow[i] = string.IsNullOrWhiteSpace(cellValue.ToString()) ? DBNull.Value : (object)cellValue;
        //                            }
        //                            dataTable.Rows.Add(dataRow);
        //                        }
        //                    }
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        // Handle other exceptions.
        //        Console.WriteLine("Exception: " + ex.Message);
        //        var Request = new System.Net.Http.HttpRequestMessage();
        //        ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(0), "");
        //    }
        //    return dataTable;
        //}

        //public static DataTable ExcelReader_SAAS(IFormFile parts)
        //{

        //    string fileName = parts.FileName;
        //    string FileExtention = Path.GetExtension(parts.FileName);
        //    DataTable dt = null;
        //    string fullPath = string.Empty;
        //    Common CommObj = new Common();
        //    fullPath = CommObj.GetAWSObjectURL(fileName, "ImportAssemblyParts");


        //    dt = ReadExcelFromBlobStorage(fullPath);

        //    return dt;
        //}

        //public int UploadFileItems(IFormFile formFile, string containerNamePrefix)
        //{
        //    int Rowsaffected = 0;
        //    string azureStorageConnectionString1 = "DefaultEndpointsProtocol=https;AccountName=codepipeline;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net";
        //    try
        //    {

        //        var blobServiceClient = new BlobServiceClient(azureStorageConnectionString1);
        //        var blobContainerClient = blobServiceClient.GetBlobContainerClient("quest-partsassist");

        //        containerNamePrefix = "EPC_UploadedFiles/" + containerNamePrefix;


        //        var fileName = Path.GetFileName(formFile.FileName);
        //        var blobClient = blobContainerClient.GetBlobClient(containerNamePrefix + "/" + fileName);

        //        // Upload the IFormFile stream directly to the blob
        //        using (Stream fileStream = formFile.OpenReadStream())
        //        {
        //            blobClient.Upload(fileStream, true);
        //        }

        //        Rowsaffected = 1;
        //    }
        //    catch (Exception ex)
        //    {
        //        // Handle Azure Storage exception
        //        // Log or handle the exception as needed
        //    }

        //    return Rowsaffected;
        //}


        public string GetAWSObjectURL(string fileName, string containerNamePrefix)
        {
            string fullFileName = fileName;
            string fileName1 = Path.GetFileNameWithoutExtension(fullFileName); // Extract filename without extension
            string fileExtension = Path.GetExtension(fullFileName).ToLower(); // Extract file extension and convert to lowercase
            fileName = fileName1 + fileExtension;

            string azureObjectURL = string.Empty;
            string containerName = "quest-partsassist";
            string prefix = string.Empty;
            containerNamePrefix = "EPC_UploadedFiles/" + containerNamePrefix + "/";
            string azureStorageConnectionString1 = "DefaultEndpointsProtocol=https;AccountName=codepipeline;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net";

            if (!string.IsNullOrEmpty(fileName))
            {
                try
                {
                    var blobServiceClient = new BlobServiceClient(azureStorageConnectionString1);
                    var blobContainerClient = blobServiceClient.GetBlobContainerClient(containerName);
                    var blobClient = blobContainerClient.GetBlobClient(containerNamePrefix + fileName);

                    if (blobClient.Exists())
                    {
                        var blobSasBuilder = new BlobSasBuilder
                        {
                            BlobContainerName = containerName,
                            BlobName = containerNamePrefix + fileName,
                            Resource = "b",
                            StartsOn = DateTimeOffset.UtcNow,
                            ExpiresOn = DateTimeOffset.UtcNow.AddMinutes(20)
                        };
                        blobSasBuilder.SetPermissions(BlobSasPermissions.Read);

                        var sasToken = blobSasBuilder.ToSasQueryParameters(new StorageSharedKeyCredential("codepipeline", "****************************************************************************************")).ToString();

                        azureObjectURL = blobClient.Uri + "?" + sasToken;
                    }
                    else
                    {
                        // Handle case when the blob is not found
                    }
                }
                catch (Exception ex)
                {
                    // Handle Azure Storage exception
                }
            }

            return azureObjectURL;
        }







        #region ::: FUNCTION TO ADD USER LOG:::
        /// <summary>
        /// DK - FUNCTION TO ADD TO USER LOG
        /// </summary> 
        public void InSertToUserLog(UserLog UserLogData, string constring, DateTime usertimezone)
        {
            string AID = string.Empty;
            int AddStatus = 0;
            TimeZoneInfo user_timezone = TimeZoneInfo.FindSystemTimeZoneById(usertimezone.ToString());
            DateTime user_datatime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, user_timezone);
            try
            {
                // using (SqlConnection conn = new SqlConnection(HttpContext.Current.Session["DBCS"].ToString()))
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    SqlCommand sqlComm = new SqlCommand("[dbo].[Up_Ins_UserLog]", conn);
                    sqlComm.Parameters.AddWithValue("@UserLog_Menu_ID", UserLogData.Menu_ID);
                    sqlComm.Parameters.AddWithValue("@UserLog_Record_ID", UserLogData.Record_ID);
                    sqlComm.Parameters.AddWithValue("@UserLog_ActionType", UserLogData.ActionType);
                    sqlComm.Parameters.AddWithValue("@UserLog_User_ID", UserLogData.User_ID);
                    sqlComm.Parameters.AddWithValue("@UserLog_User_TimeZone", user_datatime);
                    sqlComm.Parameters.Add("@AuditLog_ID", SqlDbType.Int).Direction = ParameterDirection.Output;
                    sqlComm.CommandType = CommandType.StoredProcedure;
                    if (conn.State == ConnectionState.Closed)
                    {
                        conn.Open();
                    }
                    AddStatus = sqlComm.ExecuteNonQuery();
                    sqlComm.Parameters["@AuditLog_ID"].Value.ToString();
                }
            }
            catch (Exception e)
            {

            }
        }
        #endregion

        public string errorlog(string msg, string trace)
        {
            var filename = "ErrorAPILog.txt";
            var firstpath = @"C:\LogSheetExport";
            if (!Directory.Exists(firstpath))
            {
                Directory.CreateDirectory(firstpath);
            }

            string strPath = Path.Combine(firstpath, filename);
            using (StreamWriter sw = File.AppendText(strPath))
            {
                //sw.WriteLine("=============Error Logging ===========");
                sw.WriteLine("===========Start============= " + DateTime.Now);
                sw.WriteLine("Error Message: " + msg + DateTime.Now);
                sw.WriteLine("Stack Trace: " + trace);
                sw.WriteLine("===========End============= " + DateTime.Now);
            }
            return " ";
        }

        public static string DecryptString(string str)
        {
            str = str.Replace("+", "%Plus%");
            str = Uri.UnescapeDataString(str).Replace("%lthash%", "&#");
            return str.Replace("%Plus%", "+");

        }

        #region:::FrenchDateConvert::
        /// <summary>
        ///DK-07-Nov-2023 FrenchDateConvert
        /// </summary> 
        public string FrenchDateConvert(string input, string lng)
        {
            string Result = string.Empty;
            string MainDate = input == null || Convert.ToDateTime(input).ToString("dd-MMM-yyyy") == "01-Jan-1900" || Convert.ToDateTime(input).ToString("dd-MMM-yyyy") == "01-Jan-0001" ? "" : Convert.ToDateTime(input.ToString()).ToString("dd-MMM-yyyy");
            //MainDate = input == null || Convert.ToDateTime(input).ToString("dd-MMM-yyyy") == "01-Jan-1900" || Convert.ToDateTime(input).ToString("dd-MMM-yyyy") == "01-Jan-0001" ? "" : Convert.ToDateTime(input.ToString()).ToString("dd-MMM-yyyy");

            if (lng.ToLower() == "fr" || lng.ToLower() == "en")
            {
                #region:::Only for Date COlumn MonthSwitch
                string frnchvalue = string.Empty;
                frnchvalue = MainDate.Trim();//10-fev-2017

                string getmnthval = Regex.Replace(frnchvalue, "[^a-zA-Z]", "");
                string MonthSwitch = string.Empty;
                string Query_jun = string.Empty;
                string Query_jul = string.Empty;
                string jui_jun = string.Empty;
                string jui_jul = string.Empty;
                if (lng.ToLower() == "fr" || lng.ToLower() == "en")
                {

                    switch (getmnthval.ToLower())
                    {
                        case "jan":
                            frnchvalue = (frnchvalue.ToString().Replace(("Jan"), ("Ja")));
                            break;
                        case "feb":
                            frnchvalue = (frnchvalue.ToString().Replace(("Feb"), ("Fe")));
                            break;
                        case "mar":
                            frnchvalue = (frnchvalue.ToString().Replace(("Mar"), ("Mr")));
                            break;
                        case "apr":
                            frnchvalue = (frnchvalue.ToString().Replace(("Apr"), ("Al")));
                            break;
                        case "may":
                            frnchvalue = (frnchvalue.ToString().Replace(("May"), ("Ma")));
                            break;
                        case "jun":
                            frnchvalue = (frnchvalue.ToString().Replace(("Jun"), ("Jn")));
                            break;
                        case "jul":
                            frnchvalue = (frnchvalue.ToString().Replace(("Jul"), ("Jl")));
                            break;
                        case "aug":
                            frnchvalue = (frnchvalue.ToString().Replace(("Aug"), ("Au")));
                            break;
                        case "sep":
                            frnchvalue = (frnchvalue.ToString().Replace(("Sep"), ("Se")));
                            break;
                        case "oct":
                            frnchvalue = (frnchvalue.ToString().Replace(("Oct"), ("Oc")));
                            break;
                        case "nov":
                            frnchvalue = (frnchvalue.ToString().Replace(("Nov"), ("No")));
                            break;
                        case "dec":
                            frnchvalue = (frnchvalue.ToString().Replace(("Dec"), ("De")));
                            break;
                        default:
                            break;
                    }
                }
                #endregion
                Result = frnchvalue;
            }
            else
            {
                Result = MainDate;
            }
            return Result;
        }
        #endregion

        public string FrenchDateConvertIncludingTime(string input, string lng)
        {
            string Result = string.Empty;
            string MainDate = input == null || Convert.ToDateTime(input).ToString("dd-MMM-yyyy hh:mm:ss") == "01-Jan-1900 00:00 00" || Convert.ToDateTime(input).ToString("dd-MMM-yyyy hh:mm:ss") == "01-Jan-0001 00:00 00" || Convert.ToDateTime(input).ToString("dd-MMM-yyyy hh:mm:ss") == "01-Jan-0001 12:00 00" || Convert.ToDateTime(input).ToString("dd-MMM-yyyy hh:mm:ss") == "01-Jan-1900 12:00 00" ? "" : Convert.ToDateTime(input.ToString()).ToString("dd-MMM-yyyy hh:mm:ss");
            //MainDate = input == null || Convert.ToDateTime(input).ToString("dd-MMM-yyyy") == "01-Jan-1900" || Convert.ToDateTime(input).ToString("dd-MMM-yyyy") == "01-Jan-0001" ? "" : Convert.ToDateTime(input.ToString()).ToString("dd-MMM-yyyy");

            if (lng.ToLower() == "fr" || lng.ToLower() == "en")
            {
                #region:::Only for Date COlumn MonthSwitch
                string frnchvalue = string.Empty;
                frnchvalue = MainDate.Trim();//10-fev-2017

                string getmnthval = Regex.Replace(frnchvalue, "[^a-zA-Z]", "");
                string MonthSwitch = string.Empty;
                string Query_jun = string.Empty;
                string Query_jul = string.Empty;
                string jui_jun = string.Empty;
                string jui_jul = string.Empty;
                if (lng.ToLower() == "fr" || lng.ToLower() == "en")
                {

                    switch (getmnthval.ToLower())
                    {
                        case "jan":
                            frnchvalue = (frnchvalue.ToString().Replace(("Jan"), ("Ja")));
                            break;
                        case "feb":
                            frnchvalue = (frnchvalue.ToString().Replace(("Feb"), ("Fe")));
                            break;
                        case "mar":
                            frnchvalue = (frnchvalue.ToString().Replace(("Mar"), ("Mr")));
                            break;
                        case "apr":
                            frnchvalue = (frnchvalue.ToString().Replace(("Apr"), ("Al")));
                            break;
                        case "may":
                            frnchvalue = (frnchvalue.ToString().Replace(("May"), ("Ma")));
                            break;
                        case "jun":
                            frnchvalue = (frnchvalue.ToString().Replace(("Jun"), ("Jn")));
                            break;
                        case "jul":
                            frnchvalue = (frnchvalue.ToString().Replace(("Jul"), ("Jl")));
                            break;
                        case "aug":
                            frnchvalue = (frnchvalue.ToString().Replace(("Aug"), ("Au")));
                            break;
                        case "sep":
                            frnchvalue = (frnchvalue.ToString().Replace(("Sep"), ("Se")));
                            break;
                        case "oct":
                            frnchvalue = (frnchvalue.ToString().Replace(("Oct"), ("Oc")));
                            break;
                        case "nov":
                            frnchvalue = (frnchvalue.ToString().Replace(("Nov"), ("No")));
                            break;
                        case "dec":
                            frnchvalue = (frnchvalue.ToString().Replace(("Dec"), ("De")));
                            break;
                        default:
                            break;
                    }
                }
                #endregion
                Result = frnchvalue;
            }
            else
            {
                Result = MainDate;
            }
            return Result;
        }


        #region:::FrenchDateConvertIncludingTimewithoutsec:::
        /// <summary>
        ///Siddesh FrenchDateConvertIncludingTimewithoutsec
        /// </summary> 
        public string FrenchDateConvertIncludingTimewithoutsec(string input, string lng)
        {
            string Result = string.Empty;
            string MainDate = input == null || Convert.ToDateTime(input).ToString("dd-MMM-yyyy HH:mm") == "01-Jan-1900 00:00" || Convert.ToDateTime(input).ToString("dd-MMM-yyyy HH:mm") == "01-Jan-0001 00:00" || Convert.ToDateTime(input).ToString("dd-MMM-yyyy HH:mm") == "01-Jan-0001 12:00" || Convert.ToDateTime(input).ToString("dd-MMM-yyyy HH:mm") == "01-Jan-1900 12:00" ? "" : Convert.ToDateTime(input.ToString()).ToString("dd-MMM-yyyy HH:mm");
            //MainDate = input == null || Convert.ToDateTime(input).ToString("dd-MMM-yyyy") == "01-Jan-1900" || Convert.ToDateTime(input).ToString("dd-MMM-yyyy") == "01-Jan-0001" ? "" : Convert.ToDateTime(input.ToString()).ToString("dd-MMM-yyyy");

            if (lng.ToLower() == "fr" || lng.ToLower() == "en")
            {
                #region:::Only for Date COlumn MonthSwitch
                string frnchvalue = string.Empty;
                frnchvalue = MainDate.Trim();//10-fev-2017

                string getmnthval = Regex.Replace(frnchvalue, "[^a-zA-Z]", "");
                string MonthSwitch = string.Empty;
                string Query_jun = string.Empty;
                string Query_jul = string.Empty;
                string jui_jun = string.Empty;
                string jui_jul = string.Empty;
                if (lng.ToLower() == "fr" || lng.ToLower() == "en")
                {

                    switch (getmnthval.ToLower())
                    {
                        case "jan":
                            frnchvalue = (frnchvalue.ToString().Replace(("Jan"), ("Ja")));
                            break;
                        case "feb":
                            frnchvalue = (frnchvalue.ToString().Replace(("Feb"), ("Fe")));
                            break;
                        case "mar":
                            frnchvalue = (frnchvalue.ToString().Replace(("Mar"), ("Mr")));
                            break;
                        case "apr":
                            frnchvalue = (frnchvalue.ToString().Replace(("Apr"), ("Al")));
                            break;
                        case "may":
                            frnchvalue = (frnchvalue.ToString().Replace(("May"), ("Ma")));
                            break;
                        case "jun":
                            frnchvalue = (frnchvalue.ToString().Replace(("Jun"), ("Jn")));
                            break;
                        case "jul":
                            frnchvalue = (frnchvalue.ToString().Replace(("Jul"), ("Jl")));
                            break;
                        case "aug":
                            frnchvalue = (frnchvalue.ToString().Replace(("Aug"), ("Au")));
                            break;
                        case "sep":
                            frnchvalue = (frnchvalue.ToString().Replace(("Sep"), ("Se")));
                            break;
                        case "oct":
                            frnchvalue = (frnchvalue.ToString().Replace(("Oct"), ("Oc")));
                            break;
                        case "nov":
                            frnchvalue = (frnchvalue.ToString().Replace(("Nov"), ("No")));
                            break;
                        case "dec":
                            frnchvalue = (frnchvalue.ToString().Replace(("Dec"), ("De")));
                            break;
                        default:
                            break;
                    }
                }
                #endregion
                Result = frnchvalue;
            }
            else
            {
                Result = MainDate;
            }
            return Result;
        }
        #endregion

        #region :::: GetMenuID ::::
        /// <summary>
        /// DK - handling get menu ID
        /// Date:07-NOV-2023
        /// </summary>
        /// <param name="As per below method sign"></param>
        /// <returns></returns>
        public int GetMenuID(string MenuName, int DCType, int User_ID, string constring)
        {
            int Menu_ID = 0;
            string ConnectionString = constring;
            try
            {
                using (SqlConnection SQLcon = new SqlConnection(ConnectionString))
                {
                    if (SQLcon.State == ConnectionState.Closed || SQLcon.State == ConnectionState.Broken) { SQLcon.Open(); }
                    DataTable dt = new DataTable();
                    sqlComm = new SqlCommand("SELECT Object_ID FROM MA_Object WHERE Object_Name=@MenuName", SQLcon);
                    sqlComm.Parameters.AddWithValue("@MenuName", MenuName);
                    dt.Load(sqlComm.ExecuteReader());
                    if (dt.Rows.Count > 0)
                    {
                        Menu_ID = Convert.ToInt32(dt.Rows[0].ItemArray[0].ToString());
                    }
                }
            }
            catch (Exception ex)
            {

            }
            return Menu_ID;
        }
        #endregion


        public static List<AvailableCustomerPartInfo> GetCustomePartInfoForChecking(string Connection, string Value, int Object_ID, int Userid, string Langauge_Code)
        {
            List<AvailableCustomerPartInfo> CustInfo = new List<AvailableCustomerPartInfo>();
            try
            {

                Common Commo = new Common();
                int UserID = Convert.ToInt32(Userid);
                DataSet ds = new DataSet("PartData");
                string ConnString = Connection;
                using (SqlConnection conn = new SqlConnection(ConnString))
                {
                    SqlCommand sqlComm = new SqlCommand("Up_Sel_UserCustomerInfoCustNameWithGUIConfig", conn);  //Up_Sel_Within_OrderPartSearch  //Up_Sel_Within_OrderPartSearchWithParentAssy
                    sqlComm.Parameters.AddWithValue("@User_ID", UserID);
                    sqlComm.Parameters.AddWithValue("@Display", Value);
                    sqlComm.Parameters.AddWithValue("@Object_ID", Object_ID);
                    sqlComm.CommandType = CommandType.StoredProcedure;
                    SqlDataAdapter da = new SqlDataAdapter();
                    if (conn.State == ConnectionState.Closed)
                    {
                        conn.Open();
                    }
                    DataTable dt = new DataTable();
                    dt.Load(sqlComm.ExecuteReader());
                    if (Langauge_Code.ToUpper() == "EN")
                    {
                        if (Value != "")
                        {
                            CustInfo = (from dr in dt.AsEnumerable()
                                        select new AvailableCustomerPartInfo
                                        {

                                            Customer_ID = dr.Field<int>("Customer_ID"),
                                            Customer_Name = dr.Field<string>("Customer_Name"),
                                            Customer_ShortCode = dr.Field<string>("Customer_ShortCode"),
                                            Customer_Code = dr.Field<string>("Customer_Code"),
                                            Customer_GUIConfig = dr.Field<string>("Customer_GUIConfig"),
                                            DisplayColName = dr.Field<string>("Display_Column_Name"),
                                            OriginalColName = dr.Field<string>("Original_Column_Name"),
                                            Align = dr.Field<string>("Align"),
                                            IsVisble = dr.Field<bool>("Is_Visiable"),
                                            SortNumber = dr.Field<Nullable<int>>("Sequence_No").ToString() == "" ? "20" : dr.Field<int>("Sequence_No").ToString(),
                                            Width = dr.Field<int>("Width")
                                        }).ToList();
                        }
                        else
                        {
                            CustInfo = (from dr in dt.AsEnumerable()
                                        select new AvailableCustomerPartInfo
                                        {

                                            Customer_ID = dr.Field<int>("Customer_ID"),
                                            Customer_Name = dr.Field<string>("Customer_Name"),
                                            Customer_ShortCode = dr.Field<string>("Customer_ShortCode"),
                                            Customer_Code = dr.Field<string>("Customer_Code"),
                                            Customer_GUIConfig = dr.Field<string>("Customer_GUIConfig"),

                                        }).ToList();
                        }

                    }
                    else
                    {
                        if (Value != "")
                        {
                            CustInfo = (from dr in dt.AsEnumerable()
                                        select new AvailableCustomerPartInfo
                                        {

                                            Customer_ID = dr.Field<int>("Customer_ID"),
                                            Customer_Name = dr.Field<string>("Customer_Name"),
                                            Customer_ShortCode = dr.Field<string>("Customer_FrenchShortCode"),
                                            Customer_Code = dr.Field<string>("Customer_Code"),
                                            Customer_GUIConfig = dr.Field<string>("Customer_GUIConfig"),
                                            DisplayColName = dr.Field<string>("Display_Column_Name"),
                                            OriginalColName = dr.Field<string>("Original_Column_Name"),
                                            Align = dr.Field<string>("Align"),
                                            IsVisble = dr.Field<bool>("Is_Visiable"),
                                            SortNumber = dr.Field<Nullable<int>>("Sequence_No").ToString() == "" ? "20" : dr.Field<int>("Sequence_No").ToString(),
                                            Width = dr.Field<int>("Width")
                                        }).ToList();
                        }
                        else
                        {
                            CustInfo = (from dr in dt.AsEnumerable()
                                        select new AvailableCustomerPartInfo
                                        {

                                            Customer_ID = dr.Field<int>("Customer_ID"),
                                            Customer_Name = dr.Field<string>("Customer_Name"),
                                            Customer_ShortCode = dr.Field<string>("Customer_FrenchShortCode"),
                                            Customer_Code = dr.Field<string>("Customer_Code"),
                                            Customer_GUIConfig = dr.Field<string>("Customer_GUIConfig"),

                                        }).ToList();
                        }

                    }


                }
            }
            catch (Exception e)
            {

            }
            finally
            {

                SqlConnection.ClearAllPools();
            }
            var jsonreader = new
            {
                CustomerPartInfoE = CustInfo
            };
            return CustInfo;
        }

        public bool DeleteAWSObjectFolder(string AWSBucketPrifix)
        {
            string bucketname1 = string.Empty;
            string Prefix1 = string.Empty;
            bool deletionSuccessful = false;
            try
            {
                //using (AmazonS3Client AmazoneS3client = new AmazonS3Client(AWSAccessKey, AWSSecretKey, RegionEndpoint.APSouth1))
                //{
                //    try
                //    {
                //        DeleteObjectRequest deleteObjectRequest = new DeleteObjectRequest
                //        {
                //            BucketName = AWSBucketPrifix,
                //            Key = AWSBucketPrifix
                //        };

                //        DeleteObjectResponse response = AmazoneS3client.DeleteObjectAsync(deleteObjectRequest);
                //        // If the deletion request succeeds without throwing an exception, consider it successful
                //        deletionSuccessful = true;
                //    }
                //    catch (AmazonS3Exception ex)
                //    {
                //        // Handle S3-related exception, log, or throw as needed.
                //    }
                //}
            }
            catch (AmazonS3Exception ex)
            {
                // Handle S3-related exception, log, or throw as needed.
            }
            return deletionSuccessful;
        }


        #region :::: GetActionTypeID ::::
        /// <summary>
        /// DK - GetActionTypeID
        /// Date:07-NOV-2023
        /// </summary>
        /// <param name="As per below method sign"></param>
        /// <returns></returns>
        public int GetActionTypeID(string ActionType, int DCType, int User_ID, string connstring)
        {
            int Menu_ID = 0;
            try
            {
                string ConnectionString = connstring;

                using (SqlConnection SQLcon = new SqlConnection(ConnectionString))
                {
                    if (SQLcon.State == ConnectionState.Closed || SQLcon.State == ConnectionState.Broken) { SQLcon.Open(); }
                    DataTable dt = new DataTable();
                    sqlComm = new SqlCommand("SELECT ActionType_ID FROM MA_ActionType WHERE ActionType=@ActionType", SQLcon);
                    sqlComm.Parameters.AddWithValue("@ActionType", ActionType);
                    dt.Load(sqlComm.ExecuteReader());
                    if (dt.Rows.Count > 0)
                    {
                        Menu_ID = Convert.ToInt32(dt.Rows[0].ItemArray[0].ToString());
                    }
                }
            }
            catch (Exception ex)
            {

            }
            return Menu_ID;
        }
        #endregion



        public bool CopyAWSObject(string sourceFileName, string sourcePrefix, string destinationFileName, string destinationPrefix)
        {
            string sourceBucketName = sourcePrefix; // Assuming sourcePrefix contains the bucket name
            string destinationBucketName = destinationPrefix; // Assuming destinationPrefix contains the bucket name
            bool copySuccessful = false;

            try
            {
                using (var s3Client = new AmazonS3Client(AWSAccessKey, AWSSecretKey, Amazon.RegionEndpoint.APSouth1))
                {
                    var copyObjectRequest = new CopyObjectRequest
                    {
                        SourceBucket = sourceBucketName,
                        SourceKey = sourceFileName,
                        DestinationBucket = destinationBucketName,
                        DestinationKey = destinationFileName
                    };

                    try
                    {
                        var response = s3Client.CopyObjectAsync(copyObjectRequest);
                        //copySuccessful = response.HttpStatusCode == System.Net.HttpStatusCode.OK;
                    }
                    catch (AmazonS3Exception ex)
                    {
                        // Handle exception, log, or throw as needed.
                        Console.WriteLine("Amazon S3 Exception: " + ex.Message);
                    }
                    catch (Exception ex)
                    {
                        // Handle other exceptions.
                        Console.WriteLine("Exception: " + ex.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle exception, log, or throw as needed.
                Console.WriteLine("Exception: " + ex.Message);
            }

            return copySuccessful;
        }
        public int UploadFileItems(IFormFile formFile, string containerNamePrefix)
        {
            int Rowsaffected = 0;
            string azureStorageConnectionString1 = "DefaultEndpointsProtocol=https;AccountName=codepipeline;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net";
            try
            {

                var blobServiceClient = new BlobServiceClient(azureStorageConnectionString1);
                var blobContainerClient = blobServiceClient.GetBlobContainerClient("quest-partsassist");

                containerNamePrefix = "EPC_UploadedFiles/" + containerNamePrefix;


                var fileName = Path.GetFileName(formFile.FileName);
                var blobClient = blobContainerClient.GetBlobClient(containerNamePrefix + "/" + fileName);

                // Upload the IFormFile stream directly to the blob
                using (Stream fileStream = formFile.OpenReadStream())
                {
                    blobClient.Upload(fileStream, true);
                }

                Rowsaffected = 1;
            }
            catch (Exception ex)
            {
                // Handle Azure Storage exception
                // Log or handle the exception as needed
            }

            return Rowsaffected;
        }
        public bool IsFileExistOrNot(string fileName, string containerNamePrefix)
        {
            bool azureObject = false;
            string azureObjectURL = string.Empty;
            string containerName = "quest-partsassist";
            string prefix = string.Empty;
            containerNamePrefix = "EPC_UploadedFiles/" + containerNamePrefix + "/";

            if (!string.IsNullOrEmpty(fileName))
            {
                try
                {
                    var blobServiceClient = new BlobServiceClient(azureStorageConnectionString);
                    var blobContainerClient = blobServiceClient.GetBlobContainerClient(containerName);
                    var blobClient = blobContainerClient.GetBlobClient(containerNamePrefix + fileName);

                    if (blobClient.Exists())
                    {
                        var blobSasBuilder = new BlobSasBuilder
                        {
                            BlobContainerName = containerName,
                            BlobName = containerNamePrefix + fileName,
                            Resource = "b",
                            StartsOn = DateTimeOffset.UtcNow,
                            ExpiresOn = DateTimeOffset.UtcNow.AddMinutes(20)
                        };
                        blobSasBuilder.SetPermissions(BlobSasPermissions.Read);

                        var sasToken = blobSasBuilder.ToSasQueryParameters(new StorageSharedKeyCredential("codepipeline", "****************************************************************************************")).ToString();

                        azureObjectURL = blobClient.Uri + "?" + sasToken;
                        azureObject = true;
                    }
                    else
                    {
                        fileName = "NoImage.PNG";
                        var blobSasBuilder = new BlobSasBuilder
                        {
                            BlobContainerName = containerName,
                            BlobName = containerNamePrefix + fileName,
                            Resource = "b",
                            StartsOn = DateTimeOffset.UtcNow,
                            ExpiresOn = DateTimeOffset.UtcNow.AddMinutes(20)
                        };
                        blobSasBuilder.SetPermissions(BlobSasPermissions.Read);

                        var sasToken = blobSasBuilder.ToSasQueryParameters(new StorageSharedKeyCredential("codepipeline", "****************************************************************************************")).ToString();

                        azureObjectURL = blobClient.Uri + "?" + sasToken;
                        azureObject = false;
                    }
                }
                catch (Exception ex)
                {
                    // Handle Azure Storage exception
                }
            }

            return azureObject;
        }


        //private string azureStorageConnectionString = "DefaultEndpointsProtocol=https;AccountName=codepipeline;AccountKey=****************************************************************************************EndpointSuffix=core.windows.net"; // Set your Azure Storage connection string here
        //Azure Blob
        string azureStorageConnectionString2 = "DefaultEndpointsProtocol = https;AccountName=codepipeline;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net";

        public static DataTable ReadExcelFromBlobStorage(string blobUrl)
        {
            var dataTable = new DataTable();
            try
            {
                using (var httpClient = new HttpClient())
                {
                    using (var response = httpClient.GetAsync(blobUrl).Result)
                    {
                        using (var stream = response.Content.ReadAsStreamAsync().Result)
                        {
                            using (var workbook = new XLWorkbook(stream))
                            {
                                var worksheet = workbook.Worksheet(1);

                                // Add columns to the DataTable using the column headers from the first row of the Excel file
                                var firstRow = worksheet.FirstRowUsed();
                                foreach (var cell in firstRow.Cells())
                                {
                                    dataTable.Columns.Add(cell.Value.ToString());
                                }

                                // Add rows to the DataTable
                                foreach (var row in worksheet.RowsUsed().Skip(1))
                                {
                                    var dataRow = dataTable.NewRow();
                                    for (int i = 0; i < dataTable.Columns.Count; i++)
                                    {
                                        // Check if the cell is empty and set it to DBNull.Value if it is
                                        var cellValue = row.Cell(i + 1).Value;
                                        dataRow[i] = string.IsNullOrWhiteSpace(cellValue.ToString()) ? DBNull.Value : (object)cellValue;
                                    }
                                    dataTable.Rows.Add(dataRow);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle other exceptions.
                Console.WriteLine("Exception: " + ex.Message);
                var Request = new System.Net.Http.HttpRequestMessage();
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(0), "");
            }
            return dataTable;
        }



        public static DataTable ExcelReader_SAAS(IFormFile parts, string containerNamePrefix)
        {

            string fileName = parts.FileName;
            string FileExtention = Path.GetExtension(parts.FileName);
            DataTable dt = null;
            string fullPath = string.Empty;
            Common CommObj = new Common();
            int PartsUploadStatus = CommObj.UploadFileItemsForImport1((Stream)parts, parts.ContentType, containerNamePrefix, fileName);
            fullPath = CommObj.GetAWSObjectURL(fileName, containerNamePrefix);


            dt = ReadExcelFromBlobStorage(fullPath);

            return dt;
        }

        //public int UploadFileItems(IFormFile formFile, string containerNamePrefix)
        //{
        //    int Rowsaffected = 0;
        //    string azureStorageConnectionString1 = "DefaultEndpointsProtocol=https;AccountName=codepipeline;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net";
        //    try
        //    {

        //        var blobServiceClient = new BlobServiceClient(azureStorageConnectionString1);
        //        var blobContainerClient = blobServiceClient.GetBlobContainerClient("quest-partsassist");

        //        containerNamePrefix = "EPC_UploadedFiles/" + containerNamePrefix;


        //        var fileName = Path.GetFileName(formFile.FileName);
        //        var blobClient = blobContainerClient.GetBlobClient(containerNamePrefix + "/" + fileName);

        //        // Upload the IFormFile stream directly to the blob
        //        using (Stream fileStream = formFile.OpenReadStream())
        //        {
        //            blobClient.Upload(fileStream, true);
        //        }

        //        Rowsaffected = 1;
        //    }
        //    catch (Exception ex)
        //    {
        //        // Handle Azure Storage exception
        //        // Log or handle the exception as needed
        //    }

        //    return Rowsaffected;
        //}

        //public int UploadFileItemsForImport1(IFormFile formFile, string containerNamePrefix, string FileName)
        //{
        //    int Rowsaffected = 0;

        //    try
        //    {

        //        var blobServiceClient = new BlobServiceClient(azureStorageConnectionString);
        //        var blobContainerClient = blobServiceClient.GetBlobContainerClient("quest-partsassist");
        //        containerNamePrefix = "EPC_UploadedFiles/" + containerNamePrefix;


        //        var fileName = FileName;
        //        var blobClient = blobContainerClient.GetBlobClient(containerNamePrefix + "/" + fileName);

        //        // Upload the IFormFile stream directly to the blob
        //        using (Stream fileStream = formFile.OpenReadStream())
        //        {
        //            blobClient.Upload(fileStream, true);
        //        }

        //        Rowsaffected = 1;
        //    }
        //    catch (Exception ex)
        //    {
        //        // Handle Azure Storage exception
        //        // Log or handle the exception as needed
        //    }

        //    return Rowsaffected;
        //}

        public int UploadFileItemsForImport_ErrorLog(string filePath, string containerNamePrefix, string fileName)
        {
            int rowsAffected = 0;
            string azureStorageConnectionString1 = "DefaultEndpointsProtocol=https;AccountName=codepipeline;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net";
            try
            {
                var blobServiceClient = new BlobServiceClient(azureStorageConnectionString1);
                var blobContainerClient = blobServiceClient.GetBlobContainerClient("quest-partsassist");
                containerNamePrefix = "EPC_UploadedFiles/" + containerNamePrefix;

                var blobClient = blobContainerClient.GetBlobClient(containerNamePrefix + "/" + fileName);

                // Upload the file to the blob storage
                using (var fileStream = File.OpenRead(filePath))
                {
                    blobClient.Upload(fileStream, true);
                }

                rowsAffected = 1;
            }
            catch (Exception ex)
            {
                // Handle Azure Storage exception
                // Log or handle the exception as needed
                Console.WriteLine("Exception: " + ex.Message);
            }

            return rowsAffected;
        }




        public bool DeleteAWSObject(string fileName, string containerNamePrefix)
        {
            bool deletionSuccessful = false;
            string fullFileName = fileName;
            string fileName1 = Path.GetFileNameWithoutExtension(fullFileName); // Extract filename without extension
            string fileExtension = Path.GetExtension(fullFileName).ToLower(); // Extract file extension and convert to lowercase
            fileName = fileName1 + fileExtension;

            string azureObjectURL = string.Empty;
            string containerName = "quest-partsassist";
            string prefix = string.Empty;
            containerNamePrefix = "EPC_UploadedFiles/" + containerNamePrefix + "/";
            string azureStorageConnectionString1 = "DefaultEndpointsProtocol=https;AccountName=codepipeline;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net";

            try
            {
                var blobServiceClient = new BlobServiceClient(azureStorageConnectionString1);
                var blobContainerClient = blobServiceClient.GetBlobContainerClient(containerName);
                var blobClient = blobContainerClient.GetBlobClient(containerNamePrefix + fileName);

                var deleteResponse = blobClient.Delete();
                deletionSuccessful = true;


            }
            catch (Exception ex)
            {
                // Handle Azure Storage exception, log, or throw as needed.
            }

            return deletionSuccessful;
        }
        #region :::: Generate Connection String ::::
        /// <summary>        
        /// DK - 07-Nov-202 Get Object ID
        /// </summary>
        /// <param name="as per below method sign"></param>
        /// <returns></returns>
        public int GetAssemblyIDOnAssemblyNo(string Part_Assembly_No, string MFG_Code, string Conn, int UserID)
        {
            int Part_Assembly_ID = 0;
            try
            {
                SqlConnectionStringBuilder builder = null;
                builder = new SqlConnectionStringBuilder(Conn);
                using (SqlConnection SQLcon = new SqlConnection(builder.ConnectionString))
                {
                    if (SQLcon.State == ConnectionState.Closed || SQLcon.State == ConnectionState.Broken) { SQLcon.Open(); }
                    DataTable dt = new DataTable();
                    sqlComm = new SqlCommand("SELECT Part_Assembly_ID FROM MA_Part_Assembly WHERE Part_Assembly_No=@Part_Assembly_No", SQLcon);
                    sqlComm.Parameters.AddWithValue("@Part_Assembly_No", Part_Assembly_No);
                    sqlComm.Parameters.AddWithValue("@MFG_Code", GetMFGCodeIDonMFGCode(MFG_Code, Conn, UserID));
                    dt.Load(sqlComm.ExecuteReader());
                    if (dt.Rows.Count > 0)
                    {
                        Part_Assembly_ID = Convert.ToInt32(dt.Rows[0].ItemArray[0].ToString());
                    }
                }
            }
            catch (Exception e)
            {
                var Request = new System.Net.Http.HttpRequestMessage();
                //ExceptionLogger.ErrorLog(e, Request, Convert.ToInt32(UserID), "");
            }
            return Part_Assembly_ID;
        }
        #endregion
        #region :::: Generate Connection String ::::
        /// <summary>        
        /// DK - 07-Nov-202 Get Object ID
        /// </summary>
        /// <param name="as per below method sign"></param>
        /// <returns></returns>
        public int GetMFGCodeIDonMFGCode(string MFGCode, string Conn, int UserID)
        {
            int MFG_Code_ID = 0;
            try
            {
                SqlConnectionStringBuilder builder = null;
                builder = new SqlConnectionStringBuilder(Conn);
                using (SqlConnection SQLcon = new SqlConnection(builder.ConnectionString))
                {
                    if (SQLcon.State == ConnectionState.Closed || SQLcon.State == ConnectionState.Broken) { SQLcon.Open(); }
                    DataTable dt = new DataTable();
                    sqlComm = new SqlCommand("SELECT MFG_Code_IsActive FROM MA_MFG_Code WHERE MFG_Code=@MFG_Code", SQLcon);
                    sqlComm.Parameters.AddWithValue("@MFG_Code", MFGCode);
                    dt.Load(sqlComm.ExecuteReader());
                    if (dt.Rows.Count > 0)
                    {
                        MFG_Code_ID = Convert.ToInt32(dt.Rows[0].ItemArray[0].ToString());
                    }
                }
            }
            catch (Exception e)
            {
                var Request = new System.Net.Http.HttpRequestMessage();
                //ExceptionLogger.ErrorLog(e, Request, Convert.ToInt32(UserID), "");
            }
            return MFG_Code_ID;
        }
        #endregion
        #region :::: Get Vehicle ID on VIN or VIN Short Code ::::
        /// <summary>        
        /// DK - 07-Nov-202 Get Object ID
        /// </summary>
        /// <param name="as per below method sign"></param>
        /// <returns></returns>
        public int GetVehicleID(string VIN, string VINShortCode, string Conn, int UserID)
        {
            int Vehicle_ID = 0;
            try
            {
                SqlConnectionStringBuilder builder = null;
                builder = new SqlConnectionStringBuilder(Conn);
                using (SqlConnection SQLcon = new SqlConnection(builder.ConnectionString))
                {
                    if (SQLcon.State == ConnectionState.Closed || SQLcon.State == ConnectionState.Broken) { SQLcon.Open(); }
                    DataTable dt = new DataTable();
                    if (VIN != "")
                    {
                        sqlComm = new SqlCommand("SELECT Vehicle_ID FROM MA_Vehicle WHERE Vehicle_VIN=@Vehicle_VIN AND Vehicle_Is_Active=1", SQLcon);
                        sqlComm.Parameters.AddWithValue("@Vehicle_VIN", VIN);
                    }
                    else if (VINShortCode != "")
                    {
                        sqlComm = new SqlCommand("SELECT Vehicle_ID FROM MA_Vehicle WHERE VinshortCode=@VinshortCode AND Vehicle_Is_Active=1", SQLcon);
                        sqlComm.Parameters.AddWithValue("@VinshortCode", VINShortCode);
                    }

                    dt.Load(sqlComm.ExecuteReader());
                    if (dt.Rows.Count > 0)
                    {
                        Vehicle_ID = Convert.ToInt32(dt.Rows[0].ItemArray[0].ToString());
                    }
                }
            }
            catch (Exception e)
            {
                var Request = new System.Net.Http.HttpRequestMessage();
                //ExceptionLogger.ErrorLog(e, Request, Convert.ToInt32(UserID), "");
            }
            return Vehicle_ID;
        }
        #endregion
        #region :::: Get Vehicle ID on VIN or VIN Short Code ::::
        /// <summary>        
        /// DK - 07-Nov-202 Get Object ID
        /// </summary>
        /// <param name="as per below method sign"></param>
        /// <returns></returns>
        public int GetCartIDforCartName(string CartName, string Conn, int UserID)
        {
            int Cart_ID = 0;
            try
            {
                SqlConnectionStringBuilder builder = null;
                builder = new SqlConnectionStringBuilder(Conn);
                using (SqlConnection SQLcon = new SqlConnection(builder.ConnectionString))
                {
                    if (SQLcon.State == ConnectionState.Closed || SQLcon.State == ConnectionState.Broken) { SQLcon.Open(); }
                    DataTable dt = new DataTable();

                    sqlComm = new SqlCommand("SELECT ShoppingCart_ID FROM TR_ShoppingCart WHERE ShoppingCart_Name=@ShoppingCart_Name AND ShoppingCart_User_ID=@ShoppingCart_User_ID", SQLcon);
                    sqlComm.Parameters.AddWithValue("@ShoppingCart_Name", CartName);
                    sqlComm.Parameters.AddWithValue("@ShoppingCart_User_ID", UserID);
                    dt.Load(sqlComm.ExecuteReader());
                    if (dt.Rows.Count > 0)
                    {
                        Cart_ID = Convert.ToInt32(dt.Rows[0].ItemArray[0].ToString());
                    }
                }
            }
            catch (Exception e)
            {
                var Request = new System.Net.Http.HttpRequestMessage();
                //ExceptionLogger.ErrorLog(e, Request, Convert.ToInt32(UserID), "");
            }
            return Cart_ID;
        }
        #endregion
        #region :::: Get Vehicle ID on VIN or VIN Short Code ::::
        /// <summary>        
        /// DK - 07-Nov-202 Get Object ID
        /// </summary>
        /// <param name="as per below method sign"></param>
        /// <returns></returns>
        public int GetUserID(string connstring, string Email, string User_Login_ID, string User_Name)
        {
            int User_ID = 0;
            try
            {
                SqlConnectionStringBuilder builder = null;
                builder = new SqlConnectionStringBuilder(connstring);
                using (SqlConnection SQLcon = new SqlConnection(builder.ConnectionString))
                {
                    if (SQLcon.State == ConnectionState.Closed || SQLcon.State == ConnectionState.Broken) { SQLcon.Open(); }
                    DataTable dt = new DataTable();
                    if (Email != "")
                    {
                        sqlComm = new SqlCommand("SELECT User_ID FROM MA_User WHERE User_Email=@Email", SQLcon);
                        sqlComm.Parameters.AddWithValue("@Email", Email);
                    }
                    else if (User_Login_ID != "")
                    {
                        sqlComm = new SqlCommand("SELECT User_ID FROM MA_User WHERE User_Login_ID=@User_Login_ID", SQLcon);
                        sqlComm.Parameters.AddWithValue("@User_Login_ID", User_Login_ID);

                    }
                    else if (User_Name != "")
                    {
                        sqlComm = new SqlCommand("SELECT User_ID FROM MA_User WHERE (ISNULL(U.User_FirstName,'') +' ' +IsNULL(U.User_MiddleName,'') + ' '+ ISNULL(U.User_LastName,''))=@User_Name", SQLcon);
                        sqlComm.Parameters.AddWithValue("@User_Name", User_Name);
                    }
                    dt.Load(sqlComm.ExecuteReader());
                    if (dt.Rows.Count > 0)
                    {
                        User_ID = Convert.ToInt32(dt.Rows[0].ItemArray[0].ToString());
                    }
                }
            }
            catch (Exception e)
            {
                var Request = new System.Net.Http.HttpRequestMessage();
                //ExceptionLogger.ErrorLog(e, Request, Convert.ToInt32(User_ID), connstring);
            }
            return User_ID;
        }
        #endregion


        public int UploadFileItemsForImport_ErrorLog1(Stream fileStream, string containerNamePrefix, string fileName)
        {
            int rowsAffected = 0;
            try
            {
                var blobServiceClient = new BlobServiceClient(azureStorageConnectionString);
                var blobContainerClient = blobServiceClient.GetBlobContainerClient("quest-partsassist");
                containerNamePrefix = "EPC_UploadedFiles/" + containerNamePrefix;

                var blobClient = blobContainerClient.GetBlobClient(containerNamePrefix + "/" + fileName);

                // Upload the stream to the blob storage
                blobClient.Upload(fileStream, true);

                rowsAffected = 1;
            }
            catch (Exception ex)
            {
                // Handle Azure Storage exception
                // Log or handle the exception as needed
                Console.WriteLine("Exception: " + ex.Message);
            }

            return rowsAffected;
        }

        #region:: LocalTime:::
        /// <summary>
        // Added by DK  08-NOV-2023 to get local time zone info
        /// </summary> 
        public static DateTime LocalTime(int UserID, DateTime servertime, string connstring)
        {
            DateTime localtime = DateTime.Now;
            string StandardTimeZone = "";
            try
            {

                //using (conn = new SqlConnection(ConfigurationManager.ConnectionStrings["EPC"].ConnectionString))
                using (conn = new SqlConnection(connstring))
                {
                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken) { conn.Open(); }
                    DataTable dt = new DataTable();
                    sqlComm = new SqlCommand("SELECT TimeZone FROM MA_User WHERE User_ID='" + UserID + "'", conn);
                    dt.Load(sqlComm.ExecuteReader());
                    if (dt.Rows.Count > 0)
                    {
                        StandardTimeZone = (dt.Rows[0].ItemArray[0].ToString());
                        StandardTimeZone = StandardTimeZone == null ? "India Standard Time" : StandardTimeZone;
                        localtime = TimeZoneInfo.ConvertTimeBySystemTimeZoneId(servertime, TimeZoneInfo.Local.Id, StandardTimeZone);
                    }
                }
            }

            catch (Exception ex)
            {
                var Request = new System.Net.Http.HttpRequestMessage();
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(UserID), connstring);
            }
            return localtime;
        }
        #endregion


        #region GetAllQueue
        public static List<Indicator> GetAllQueue(int companyID, int workFlowID, int userID, int StatusID, int branchID, string connString, int LogException)
        {
            bool flag = false;
            List<int> CloseStepID = new List<int>();
            using (SqlConnection conn = new SqlConnection(connString))
            {
                string query = "SELECT TOP 1 AllQueue_Filter_IsBranch FROM GNM_WorkFlow WHERE WorkFlow_ID = @workFlowID";

                SqlCommand cmd = null;

                try
                {
                    using (cmd = new SqlCommand(query, conn))
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.Parameters.AddWithValue("@workFlowID", workFlowID);




                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }

                        var result = cmd.ExecuteScalar();
                        if (result != DBNull.Value)
                        {
                            flag = Convert.ToBoolean(result);
                        }



                    }


                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }

            }
            List<Indicator> list = new List<Indicator>();
            WF_WFStepStatus StepStutus = null;
            List<WF_WFStepLink> WFStepLinksList = new List<WF_WFStepLink>();
            IEnumerable<WF_WFStepLink> inner = null;
            using (SqlConnection conn = new SqlConnection(connString))
            {
                string query = @"SELECT * 
                     FROM GNM_WFStepLink 
                     WHERE Company_ID = @companyID";

                SqlCommand cmd = null;

                try
                {
                    using (cmd = new SqlCommand(query, conn))
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.Parameters.AddWithValue("@companyID", companyID);



                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                WF_WFStepLink stepLink = new WF_WFStepLink
                                {
                                    WFStepLink_ID = reader.GetInt32(reader.GetOrdinal("WFStepLink_ID")),
                                    WorkFlow_ID = reader.GetInt32(reader.GetOrdinal("WorkFlow_ID")),
                                    Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                    FrmWFSteps_ID = reader.GetInt32(reader.GetOrdinal("FrmWFSteps_ID")),
                                    WFAction_ID = reader.GetInt32(reader.GetOrdinal("WFAction_ID")),
                                    ToWFSteps_ID = reader.GetInt32(reader.GetOrdinal("ToWFSteps_ID")),
                                    Addresse_WFRole_ID = reader.GetInt32(reader.GetOrdinal("Addresse_WFRole_ID")),
                                    Addresse_Flag = reader.GetByte(reader.GetOrdinal("Addresse_Flag")),
                                    IsSMSSentToCustomer = reader.GetBoolean(reader.GetOrdinal("IsSMSSentToCustomer")),
                                    IsEmailSentToCustomer = reader.GetBoolean(reader.GetOrdinal("IsEmailSentToCustomer")),
                                    IsSMSSentToAddressee = reader.GetBoolean(reader.GetOrdinal("IsSMSSentToAddressee")),
                                    IsEmailSentToAddresse = reader.GetBoolean(reader.GetOrdinal("IsEmailSentToAddresse")),
                                    AutoAllocationAllowed = reader.GetBoolean(reader.GetOrdinal("AutoAllocationAllowed")),
                                    IsVersionEnabled = reader.GetBoolean(reader.GetOrdinal("IsVersionEnabled")),
                                    InvokeParentWF_ID = reader.IsDBNull(reader.GetOrdinal("InvokeParentWF_ID")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("InvokeParentWF_ID")),
                                    InvokeParentWFLink_ID = reader.IsDBNull(reader.GetOrdinal("InvokeParentWFLink_ID")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("InvokeParentWFLink_ID")),
                                    InvokeChildObject_ID = reader.IsDBNull(reader.GetOrdinal("InvokeChildObject_ID")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("InvokeChildObject_ID")),
                                    InvokeChildObjectAction = reader.IsDBNull(reader.GetOrdinal("InvokeChildObjectAction")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("InvokeChildObjectAction")),

                                    WFField_ID = reader.IsDBNull(reader.GetOrdinal("WFField_ID")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("WFField_ID")),
                                    AutoCondition = reader.IsDBNull(reader.GetOrdinal("AutoCondition")) ? null : reader.GetString(reader.GetOrdinal("AutoCondition"))
                                };

                                WFStepLinksList.Add(stepLink);
                            }
                            inner = WFStepLinksList.AsEnumerable();
                        }

                    }


                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }

            }
            int ID = 0;
            object obj = null;
            dynamic val = obj;
            int EndStepTypeID = 0;

            try
            {

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string endStepTypeQuery = "SELECT TOP 1 WFStepType_ID FROM GNM_WFStepType WHERE UPPER(WFStepType_Nm) = 'END'";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(endStepTypeQuery, conn))
                        {
                            cmd.CommandType = CommandType.Text;




                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            var result = cmd.ExecuteScalar();
                            if (result != DBNull.Value)
                            {
                                EndStepTypeID = int.Parse(result.ToString());
                            }



                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string closeStepIDQuery = @"
                    SELECT WFSteps_ID 
                    FROM GNM_WFSteps 
                    WHERE WFStepType_ID = @EndStepTypeID AND WorkFlow_ID = @WorkFlowID";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(closeStepIDQuery, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@EndStepTypeID", EndStepTypeID);
                            cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);



                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {

                                    int WFSteps_ID = reader.GetInt32(reader.GetOrdinal("WFSteps_ID"));
                                    CloseStepID.Add(WFSteps_ID);
                                }
                            }



                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }


                List<int> CloseStepIDStore = new List<int>();
                CloseStepIDStore.AddRange(CloseStepID);
                List<WF_WFCase_Progress> list2 = new List<WF_WFCase_Progress>();
                IEnumerable<WF_WFSteps> enumerable = null;

                int roleID = 0;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string roleIDQuery = @"
                    SELECT TOP 1 a.WFRole_ID
                    FROM GNM_WFRoleUser a
                    JOIN GNM_WFRole role ON a.WFRole_ID = a.WFRole_ID
                    WHERE role.WorkFlow_ID = @WorkFlowID AND a.UserID = @UserID";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(roleIDQuery, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                            cmd.Parameters.AddWithValue("@UserID", userID);



                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {

                                    roleID = reader.GetInt32(reader.GetOrdinal("WFRole_ID"));
                                }
                            }



                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }
                if (StatusID == 0)
                {
                    list2 = new List<WF_WFCase_Progress>();
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = @"SELECT WFCaseProgress_ID, WorkFlow_ID, Transaction_ID, WFSteps_ID, Addresse_ID, Addresse_Flag, 
                       Received_Time, Actioned_By, Action_Time, Action_Chosen, Action_Remarks, Locked_Ind, WFNextStep_ID
                FROM GNM_WFCase_Progress
                WHERE (WFNextStep_ID IN (@CloseStepIDs) OR Action_Chosen IS NULL) AND WorkFlow_ID = @WorkFlowID";

                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(query, conn))
                            {
                                cmd.CommandType = CommandType.Text;
                                cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                                string closeStepIDList = string.Join(",", CloseStepID);
                                cmd.CommandText = cmd.CommandText.Replace("@CloseStepIDs", closeStepIDList);


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        WF_WFCase_Progress item = new WF_WFCase_Progress
                                        {
                                            WFCaseProgress_ID = reader.GetInt32(reader.GetOrdinal("WFCaseProgress_ID")),
                                            WorkFlow_ID = reader.GetInt32(reader.GetOrdinal("WorkFlow_ID")),
                                            Transaction_ID = reader.IsDBNull(reader.GetOrdinal("Transaction_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Transaction_ID")),

                                            WFSteps_ID = reader.GetInt32(reader.GetOrdinal("WFSteps_ID")),
                                            Addresse_ID = reader.IsDBNull(reader.GetOrdinal("Addresse_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Addresse_ID")),
                                            Addresse_Flag = (byte)(reader.IsDBNull(reader.GetOrdinal("Addresse_Flag")) ? 0 : reader.GetByte(reader.GetOrdinal("Addresse_Flag"))),
                                            Received_Time = reader.GetDateTime(reader.GetOrdinal("Received_Time")),
                                            Actioned_By = reader.IsDBNull(reader.GetOrdinal("Actioned_By")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Actioned_By")),
                                            Action_Time = reader.IsDBNull(reader.GetOrdinal("Action_Time")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("Action_Time")),
                                            Action_Chosen = reader.IsDBNull(reader.GetOrdinal("Action_Chosen")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Action_Chosen")),
                                            Action_Remarks = reader.IsDBNull(reader.GetOrdinal("Action_Remarks")) ? null : reader.GetString(reader.GetOrdinal("Action_Remarks")),
                                            Locked_Ind = reader.IsDBNull(reader.GetOrdinal("Locked_Ind")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("Locked_Ind")),
                                            WFNextStep_ID = reader.IsDBNull(reader.GetOrdinal("WFNextStep_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WFNextStep_ID"))
                                        };
                                        list2.Add(item);
                                    }
                                }



                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }

                    }
                }
                else
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string queryStatus = @"SELECT TOP (1) [WFStepStatus_ID], [WFStepStatus_Nm], [StepStatusCode] 
                      FROM [dbo].[GNM_WFStepStatus] 
                      WHERE [WFStepStatus_ID] = @StatusID";

                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(queryStatus, conn))
                            {
                                cmd.CommandType = CommandType.Text;
                                cmd.Parameters.AddWithValue("@StatusID", StatusID);


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        StepStutus = new WF_WFStepStatus
                                        {
                                            WFStepStatus_ID = reader.GetInt32(reader.GetOrdinal("WFStepStatus_ID")),
                                            WFStepStatus_Nm = reader.GetString(reader.GetOrdinal("WFStepStatus_Nm")),
                                            StepStatusCode = reader.GetString(reader.GetOrdinal("StepStatusCode"))
                                        };
                                    }
                                }



                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }

                    }
                    List<WF_WFSteps> queryStepsRes = new List<WF_WFSteps>();
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string querySteps = @"SELECT [WFSteps_ID], [WorkFlow_ID], [WFStep_Name], [WFStepType_ID], [WFStepStatus_ID], [WFStep_IsActive], [BranchCode] 
                         FROM [dbo].[GNM_WFSteps] 
                         WHERE [WFStepStatus_ID] = @WFStepStatus_ID 
                         AND [WorkFlow_ID] = @WorkFlowID";

                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(querySteps, conn))
                            {
                                cmd.CommandType = CommandType.Text;
                                cmd.Parameters.AddWithValue("@WFStepStatus_ID", StepStutus.WFStepStatus_ID);
                                cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        WF_WFSteps stepItem = new WF_WFSteps
                                        {
                                            WFSteps_ID = reader.GetInt32(reader.GetOrdinal("WFSteps_ID")),
                                            WorkFlow_ID = reader.GetInt32(reader.GetOrdinal("WorkFlow_ID")),
                                            WFStep_Name = reader.GetString(reader.GetOrdinal("WFStep_Name")),
                                            WFStepType_ID = reader.GetInt32(reader.GetOrdinal("WFStepType_ID")),
                                            WFStepStatus_ID = reader.GetInt32(reader.GetOrdinal("WFStepStatus_ID")),
                                            WFStep_IsActive = reader.GetBoolean(reader.GetOrdinal("WFStep_IsActive")),
                                            BranchCode = reader.IsDBNull(reader.GetOrdinal("BranchCode")) ? null : reader.GetString(reader.GetOrdinal("BranchCode"))
                                        };
                                        queryStepsRes.Add(stepItem);
                                    }
                                    enumerable = queryStepsRes.AsEnumerable();
                                }



                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }

                    }


                    WF_WFSteps step;
                    foreach (WF_WFSteps item in enumerable)
                    {
                        step = item;
                        int num = 0;
                        while (num < CloseStepID.Count())
                        {
                            if (step.WFSteps_ID == CloseStepID[num])
                            {
                                ID = CloseStepID[num];
                                using (SqlConnection conn = new SqlConnection(connString))
                                {
                                    string queryProgress = @"SELECT [WFCaseProgress_ID],
                                        [WorkFlow_ID],
                                        [Transaction_ID],
                                        [WFSteps_ID],
                                        [Addresse_ID],
                                        [Addresse_Flag],
                                        [Received_Time],
                                        [Actioned_By],
                                        [Action_Time],
                                        [Action_Chosen],
                                        [Action_Remarks],
                                        [Locked_Ind],
                                        [WFNextStep_ID]
                                 FROM [dbo].[GNM_WFCase_Progress]
                                 WHERE [WorkFlow_ID] = @WorkFlowID AND [WFNextStep_ID] = @WFNextStepID";

                                    SqlCommand cmd = null;

                                    try
                                    {
                                        using (cmd = new SqlCommand(queryProgress, conn))
                                        {
                                            cmd.CommandType = CommandType.Text;
                                            cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                                            cmd.Parameters.AddWithValue("@WFNextStepID", ID);


                                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                            {
                                                conn.Open();
                                            }

                                            using (SqlDataReader reader = cmd.ExecuteReader())
                                            {
                                                while (reader.Read())
                                                {
                                                    WF_WFCase_Progress progressItem = new WF_WFCase_Progress
                                                    {
                                                        WFCaseProgress_ID = reader.GetInt32(reader.GetOrdinal("WFCaseProgress_ID")),
                                                        WorkFlow_ID = reader.GetInt32(reader.GetOrdinal("WorkFlow_ID")),
                                                        Transaction_ID = reader.GetInt32(reader.GetOrdinal("Transaction_ID")),
                                                        WFSteps_ID = reader.GetInt32(reader.GetOrdinal("WFSteps_ID")),
                                                        Addresse_ID = reader.IsDBNull(reader.GetOrdinal("Addresse_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Addresse_ID")),
                                                        Addresse_Flag = reader.IsDBNull(reader.GetOrdinal("Addresse_Flag")) ? (byte)0 : reader.GetByte(reader.GetOrdinal("Addresse_Flag")),
                                                        Received_Time = reader.GetDateTime(reader.GetOrdinal("Received_Time")),
                                                        Actioned_By = reader.IsDBNull(reader.GetOrdinal("Actioned_By")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Actioned_By")),
                                                        Action_Time = reader.IsDBNull(reader.GetOrdinal("Action_Time")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("Action_Time")),
                                                        Action_Chosen = reader.IsDBNull(reader.GetOrdinal("Action_Chosen")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Action_Chosen")),
                                                        Action_Remarks = reader.IsDBNull(reader.GetOrdinal("Action_Remarks")) ? null : reader.GetString(reader.GetOrdinal("Action_Remarks")),
                                                        Locked_Ind = reader.IsDBNull(reader.GetOrdinal("Locked_Ind")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("Locked_Ind")),
                                                        WFNextStep_ID = reader.IsDBNull(reader.GetOrdinal("WFNextStep_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WFNextStep_ID"))
                                                    };
                                                    list2.AddRange(new[] { progressItem });
                                                }

                                            }



                                        }


                                    }
                                    catch (Exception ex)
                                    {
                                        if (LogException == 1)
                                        {
                                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                        }

                                    }
                                    finally
                                    {
                                        cmd.Dispose();
                                        conn.Close();
                                        conn.Dispose();
                                        SqlConnection.ClearAllPools();
                                    }

                                }

                                CloseStepID.RemoveAt(0);
                                num = 0;
                                continue;
                            }
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                string query = @"
                                SELECT [WFCaseProgress_ID], [WorkFlow_ID], [Transaction_ID], [WFSteps_ID],
                                       [Addresse_ID], [Addresse_Flag], [Received_Time], [Actioned_By], 
                                       [Action_Time], [Action_Chosen], [Action_Remarks], [Locked_Ind], [WFNextStep_ID]
                                FROM [GNM_WFCase_Progress]
                                WHERE [WFSteps_ID] = @WFSteps_ID
                                  AND [WorkFlow_ID] = @WorkFlow_ID
                                  AND [Action_Chosen] IS NULL;";

                                SqlCommand cmd = null;

                                try
                                {
                                    using (cmd = new SqlCommand(query, conn))
                                    {
                                        cmd.CommandType = CommandType.Text;
                                        cmd.Parameters.AddWithValue("@WFSteps_ID", step.WFSteps_ID);
                                        cmd.Parameters.AddWithValue("@WorkFlow_ID", workFlowID);


                                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                        {
                                            conn.Open();
                                        }

                                        using (SqlDataReader reader = cmd.ExecuteReader())
                                        {
                                            while (reader.Read())
                                            {

                                                WF_WFCase_Progress progressItem = new WF_WFCase_Progress
                                                {
                                                    WFCaseProgress_ID = reader.GetInt32(reader.GetOrdinal("WFCaseProgress_ID")),
                                                    WorkFlow_ID = reader.GetInt32(reader.GetOrdinal("WorkFlow_ID")),
                                                    Transaction_ID = reader.GetInt32(reader.GetOrdinal("Transaction_ID")),
                                                    WFSteps_ID = reader.GetInt32(reader.GetOrdinal("WFSteps_ID")),
                                                    Addresse_ID = reader.IsDBNull(reader.GetOrdinal("Addresse_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Addresse_ID")),
                                                    Addresse_Flag = reader.IsDBNull(reader.GetOrdinal("Addresse_Flag")) ? (byte)0 : reader.GetByte(reader.GetOrdinal("Addresse_Flag")),
                                                    Received_Time = reader.GetDateTime(reader.GetOrdinal("Received_Time")),
                                                    Actioned_By = reader.IsDBNull(reader.GetOrdinal("Actioned_By")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Actioned_By")),
                                                    Action_Time = reader.IsDBNull(reader.GetOrdinal("Action_Time")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("Action_Time")),
                                                    Action_Chosen = reader.IsDBNull(reader.GetOrdinal("Action_Chosen")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Action_Chosen")),
                                                    Action_Remarks = reader.IsDBNull(reader.GetOrdinal("Action_Remarks")) ? null : reader.GetString(reader.GetOrdinal("Action_Remarks")),
                                                    Locked_Ind = reader.IsDBNull(reader.GetOrdinal("Locked_Ind")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("Locked_Ind")),
                                                    WFNextStep_ID = reader.IsDBNull(reader.GetOrdinal("WFNextStep_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WFNextStep_ID"))
                                                };
                                                list2.AddRange(new[] { progressItem });
                                            }
                                            enumerable = queryStepsRes.AsEnumerable();
                                        }



                                    }


                                }
                                catch (Exception ex)
                                {
                                    if (LogException == 1)
                                    {
                                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                    }

                                }
                                finally
                                {
                                    cmd.Dispose();
                                    conn.Close();
                                    conn.Dispose();
                                    SqlConnection.ClearAllPools();
                                }

                            }

                            break;
                        }
                    }
                }

                val = (from a in list2
                       join b in inner on a.WorkFlow_ID equals b.WorkFlow_ID
                       where b.WorkFlow_ID == workFlowID
                       select new { a, b }).Select(joined =>
                       {
                           int transaction_ID = joined.a.Transaction_ID;
                           int? actioned_By = joined.a.Actioned_By;
                           int? addresse_ID = joined.a.Addresse_ID;
                           int indicator2;
                           if (!CloseStepIDStore.Contains(joined.a.WFNextStep_ID.HasValue ? joined.a.WFNextStep_ID.Value : 0))
                           {
                               int? addresse_ID2 = joined.a.Addresse_ID;
                               int num2 = userID;
                               if (addresse_ID2.GetValueOrDefault() != num2 || !addresse_ID2.HasValue || joined.a.Addresse_Flag != 1)
                               {
                                   addresse_ID2 = joined.a.Addresse_ID;
                                   num2 = roleID;
                                   indicator2 = ((addresse_ID2.GetValueOrDefault() == num2 && addresse_ID2.HasValue && joined.a.Addresse_Flag == 0) ? 2 : 4);
                               }
                               else
                               {
                                   indicator2 = 1;
                               }
                           }
                           else
                           {
                               indicator2 = 3;
                           }

                           return new
                           {
                               Transaction_ID = transaction_ID,
                               Actioned_By = actioned_By,
                               Addresse_ID = addresse_ID,
                               indicator = indicator2
                           };
                       }).Distinct();
                foreach (dynamic item2 in val)
                {
                    Indicator indicator = new Indicator();
                    indicator.TransactionID = item2.Transaction_ID;
                    indicator.IndicatorType = item2.indicator;
                    indicator.IsLock = false;
                    list.Add(indicator);
                }
            }
            catch (Exception)
            {
            }

            return list;
        }

        #endregion



        #region ::: CheckAutoAllocation Vinay  14-11-2024:::
        /// <summary>
        /// CheckAutoAllocation
        /// </summary>
        /// <returns>...</returns>
        public static bool CheckAutoAllocation(int companyID, int workFlowID, int UserID, string connString, int LogException)
        {
            bool result = true;
            WF_WFStepLink stepLink = null;
            IEnumerable<WF_WFCase_Progress> enumerable = null;
            List<WF_WFCase_Progress> progressList = new List<WF_WFCase_Progress>();
            try
            {
                stepLink = GetAutoAllocationStepDetails(workFlowID, companyID, connString, LogException);
                if (stepLink != null)
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = @"SELECT *
                     FROM GNM_WFCase_Progress
                    WHERE WFSteps_ID = @FrmWFStepsID 
                    AND Action_Chosen IS NULL 
                    AND Addresse_Flag = 1 
                    AND Addresse_ID = @UserID 
                    AND WorkFlow_ID = @WorkFlowID";

                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(query, conn))
                            {
                                cmd.CommandType = CommandType.Text;
                                cmd.Parameters.AddWithValue("@FrmWFStepsID", stepLink.FrmWFSteps_ID);
                                cmd.Parameters.AddWithValue("@UserID", (int?)UserID);
                                cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);



                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        WF_WFCase_Progress progress = new WF_WFCase_Progress()
                                        {
                                            WFCaseProgress_ID = reader.GetInt32(reader.GetOrdinal("WFCaseProgress_ID")),
                                            WorkFlow_ID = reader.GetInt32(reader.GetOrdinal("WorkFlow_ID")),
                                            Transaction_ID = reader.GetInt32(reader.GetOrdinal("Transaction_ID")),
                                            WFSteps_ID = reader.GetInt32(reader.GetOrdinal("WFSteps_ID")),
                                            Addresse_ID = reader.IsDBNull(reader.GetOrdinal("Addresse_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Addresse_ID")),
                                            Addresse_Flag = reader.GetByte(reader.GetOrdinal("Addresse_Flag")),
                                            Received_Time = reader.GetDateTime(reader.GetOrdinal("Received_Time")),
                                            Actioned_By = reader.IsDBNull(reader.GetOrdinal("Actioned_By")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Actioned_By")),
                                            Action_Time = reader.IsDBNull(reader.GetOrdinal("Action_Time")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("Action_Time")),
                                            Action_Chosen = reader.IsDBNull(reader.GetOrdinal("Action_Chosen")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Action_Chosen")),
                                            Action_Remarks = reader.IsDBNull(reader.GetOrdinal("Action_Remarks")) ? null : reader.GetString(reader.GetOrdinal("Action_Remarks")),
                                            Locked_Ind = reader.IsDBNull(reader.GetOrdinal("Locked_Ind")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("Locked_Ind")),
                                            WFNextStep_ID = reader.IsDBNull(reader.GetOrdinal("WFNextStep_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WFNextStep_ID"))
                                        };
                                        progressList.Add(progress);
                                    }
                                    enumerable = progressList.AsEnumerable();
                                }



                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }

                    }

                    if (enumerable.Count() == 0)
                    {
                        result = false;
                    }
                }
                else
                {
                    result = false;
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return result;
        }
        public static WF_WFStepLink GetAutoAllocationStepDetails(int WorkFlowID, int CompanyID, string connString, int LogException)
        {
            WF_WFStepLink result = null;
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = @"SELECT *
                        
                    FROM GNM_WFStepLink
                    WHERE Company_ID = @CompanyID 
                    AND WorkFlow_ID = @WorkFlowID 
                    AND AutoAllocationAllowed = 1";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                            cmd.Parameters.AddWithValue("@WorkFlowID", WorkFlowID);



                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    result = new WF_WFStepLink()
                                    {
                                        WFStepLink_ID = reader.GetInt32(reader.GetOrdinal("WFStepLink_ID")),
                                        WorkFlow_ID = reader.GetInt32(reader.GetOrdinal("WorkFlow_ID")),
                                        Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                        FrmWFSteps_ID = reader.GetInt32(reader.GetOrdinal("FrmWFSteps_ID")),
                                        WFAction_ID = reader.GetInt32(reader.GetOrdinal("WFAction_ID")),
                                        ToWFSteps_ID = reader.GetInt32(reader.GetOrdinal("ToWFSteps_ID")),
                                        Addresse_WFRole_ID = reader.IsDBNull(reader.GetOrdinal("Addresse_WFRole_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Addresse_WFRole_ID")),
                                        Addresse_Flag = reader.GetByte(reader.GetOrdinal("Addresse_Flag")),
                                        IsSMSSentToCustomer = reader.GetBoolean(reader.GetOrdinal("IsSMSSentToCustomer")),
                                        IsEmailSentToCustomer = reader.GetBoolean(reader.GetOrdinal("IsEmailSentToCustomer")),
                                        IsSMSSentToAddressee = reader.GetBoolean(reader.GetOrdinal("IsSMSSentToAddressee")),
                                        IsEmailSentToAddresse = reader.GetBoolean(reader.GetOrdinal("IsEmailSentToAddresse")),
                                        AutoAllocationAllowed = reader.GetBoolean(reader.GetOrdinal("AutoAllocationAllowed")),
                                        IsVersionEnabled = reader.GetBoolean(reader.GetOrdinal("IsVersionEnabled")),
                                        InvokeParentWF_ID = reader.IsDBNull(reader.GetOrdinal("InvokeParentWF_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("InvokeParentWF_ID")),
                                        InvokeParentWFLink_ID = reader.IsDBNull(reader.GetOrdinal("InvokeParentWFLink_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("InvokeParentWFLink_ID")),
                                        InvokeChildObject_ID = reader.IsDBNull(reader.GetOrdinal("InvokeChildObject_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("InvokeChildObject_ID")),
                                        InvokeChildObjectAction = reader.IsDBNull(reader.GetOrdinal("InvokeChildObjectAction")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("InvokeChildObjectAction")),
                                        WFField_ID = reader.IsDBNull(reader.GetOrdinal("WFField_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WFField_ID")),
                                        AutoCondition = reader.IsDBNull(reader.GetOrdinal("AutoCondition")) ? null : reader.GetString(reader.GetOrdinal("AutoCondition"))
                                    };
                                }
                            }



                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return result;
        }
        #endregion


        #region:::InitialSetup:::
        public static JsonResult InitialSetup(int ObjID, int User_ID, string connString, int LogException)
        {
            string query = string.Empty;
            List<ACLProperties> ACLobjList = new List<ACLProperties>();

            int UserID = 0;


            GNM_Object gobj = null;
            try
            {
                UserID = Convert.ToInt32(User_ID);

                try
                {


                    query = "select object_id,SUM(cast(RoleObject_Create as int)) RoleObject_Create ,SUM(cast(RoleObject_Read as int)) RoleObject_Read,sum(cast(roleobject_update as int)) RoleObject_Update, SUM(cast(roleobject_delete as int)) RoleObject_Delete, SUM(cast(roleobject_print as int)) RoleObject_Print, SUM(cast(roleobject_export as int)) RoleObject_Export ,SUM(cast(roleobject_import as int)) RoleObject_Import from GNM_roleobject where Role_ID in (select role_id from gnm_userrole where USER_ID=" + UserID + ") group by OBJECT_ID";
                    using (SqlConnection conn = new SqlConnection(connString))
                    {


                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(query, conn))
                            {
                                cmd.CommandType = CommandType.Text;



                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.HasRows) // Check if there are any rows in the result set
                                    {
                                        while (reader.Read())
                                        {
                                            ACLProperties aCLProperties = new ACLProperties
                                            {
                                                Object_ID = int.Parse(reader["Object_ID"].ToString()),
                                                RoleObject_Create = Convert.ToInt32(reader["RoleObject_Create"]),
                                                RoleObject_Read = Convert.ToInt32(reader["RoleObject_Read"]),
                                                RoleObject_Update = Convert.ToInt32(reader["RoleObject_Update"]),
                                                RoleObject_Delete = Convert.ToInt32(reader["RoleObject_Delete"]),
                                                RoleObject_Print = Convert.ToInt32(reader["RoleObject_Print"]),
                                                RoleObject_Export = Convert.ToInt32(reader["RoleObject_Export"]),
                                                RoleObject_Import = Convert.ToInt32(reader["RoleObject_Import"])
                                            };

                                            ACLobjList.Add(aCLProperties);
                                        }
                                    }
                                }




                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }


                }
                catch (Exception ex)
                {

                }



                using (SqlConnection conn = new SqlConnection(connString))
                {

                    string Query = @"SELECT 
                            [Object_ID],
                            [Object_Name],
                            [Read_Action],
                            [Create_Action],
                            [Update_Action],
                            [Delete_Action],
                            [Export_Action],
                            [Print_Action],
                            [Object_IsActive],
                            [Object_Description],
                            [Import_Action],
                            [Object_Type]
                         FROM [HCLSoftware_AMP_MicroService_GUI].[dbo].[GNM_Object]
                         WHERE [Object_ID] = @ObjID";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(Query, conn))
                        {
                            command.CommandType = CommandType.Text;
                            command.Parameters.AddWithValue("@ObjID", ObjID);



                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    gobj = new GNM_Object
                                    {
                                        Object_ID = Convert.ToInt32(reader["Object_ID"]),
                                        Object_Name = reader["Object_Name"].ToString(),
                                        Read_Action = reader["Read_Action"].ToString(),
                                        Create_Action = reader["Create_Action"].ToString(),
                                        Update_Action = reader["Update_Action"].ToString(),
                                        Delete_Action = reader["Delete_Action"].ToString(),
                                        Export_Action = reader["Export_Action"].ToString(),
                                        Print_Action = reader["Print_Action"].ToString(),
                                        Object_IsActive = Convert.ToBoolean(reader["Object_IsActive"]),
                                        Object_Description = reader["Object_Description"].ToString(),
                                        Import_Action = reader["Import_Action"].ToString(),
                                        Object_Type = reader["Object_Type"].ToString()
                                    };
                                }

                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                var aclData = new
                {
                    IsRead = (ACLobjList.Where(obj => obj.Object_ID == ObjID).First().RoleObject_Read > 0) ? true : false,
                    IsAdd = (ACLobjList.Where(obj => obj.Object_ID == ObjID).First().RoleObject_Create > 0) ? true : false,
                    IsEdit = (ACLobjList.Where(obj => obj.Object_ID == ObjID).First().RoleObject_Update > 0) ? true : false,
                    IsDelete = (ACLobjList.Where(obj => obj.Object_ID == ObjID).First().RoleObject_Delete > 0) ? true : false,
                    IsPrint = (ACLobjList.Where(obj => obj.Object_ID == ObjID).First().RoleObject_Print > 0) ? true : false,
                    IsExport = (ACLobjList.Where(obj => obj.Object_ID == ObjID).First().RoleObject_Export > 0) ? true : false,
                    IsImport = (ACLobjList.Where(obj => obj.Object_ID == ObjID).First().RoleObject_Import > 0) ? true : false,


                    SelectAction = "/" + gobj.Object_Name + "/" + gobj.Read_Action,
                    AddAction = "/" + gobj.Object_Name + "/" + gobj.Create_Action,
                    EditAction = "/" + gobj.Object_Name + "/" + gobj.Update_Action,
                    DeleteAction = "/" + gobj.Object_Name + "/" + gobj.Delete_Action,
                    PrintAction = "/" + gobj.Object_Name + "/" + gobj.Print_Action,
                    ExportAction = "/" + gobj.Object_Name + "/" + gobj.Export_Action,
                    ImportAction = "/" + gobj.Object_Name + "/" + gobj.Import_Action
                };
                return new JsonResult(aclData);
            }
            catch (Exception ex)
            {
                return null;
            }


        }
        #endregion
       
        public static void UpdateAPIDashboard(string apiCategory, string status,string connString)
        {
            string query = "EXEC Update_API_Dashboard @API_Category, @Status";

            using (SqlConnection conn = new SqlConnection(connString))
            using (SqlCommand cmd = new SqlCommand(query, conn))
            {
                cmd.Parameters.AddWithValue("@API_Category", apiCategory);
                cmd.Parameters.AddWithValue("@Status", status);

                conn.Open();
                cmd.ExecuteNonQuery();
            }
        }
        public static bool ValidateNegativeValueGreaterThanZero(JObject row, Dictionary<string, bool> columns, out List<string> invalidColumns)
        {
            bool isValid = true; // Assume all are valid initially
            invalidColumns = new List<string>();

            foreach (var column in columns)
            {
                string columnName = column.Key;
                bool IsNeedCheckNegativeValue = column.Value;

                var columnValue = row[columnName]?.ToString();

                if (IsNeedCheckNegativeValue)
                {
                    // If not mandatory, only validate if present and not empty
                    if (!string.IsNullOrWhiteSpace(columnValue))
                    {
                        if (!decimal.TryParse(columnValue, out decimal numericValue))
                        {
                            invalidColumns.Add($"{columnName} (invalid number)");
                            isValid = false;
                        }
                        else if (numericValue <= 0)
                        {
                            invalidColumns.Add($"{columnName} (must be greater than 0)");
                            isValid = false;
                        }
                    }
                }
            }

            return isValid;
        }
        public static bool ValidateNegativeValueGreaterThanorEqualZero(JObject row, Dictionary<string, bool> columns, out List<string> invalidColumns)
        {
            bool isValid = true; // Assume all are valid initially
            invalidColumns = new List<string>();

            foreach (var column in columns)
            {
                string columnName = column.Key;
                bool IsNeedCheckNegativeValue = column.Value;

                var columnValue = row[columnName]?.ToString();

                if(IsNeedCheckNegativeValue)
                {
                    // If not mandatory, only validate if present and not empty
                    if (!string.IsNullOrWhiteSpace(columnValue))
                    {
                        if (!decimal.TryParse(columnValue, out decimal numericValue))
                        {
                            invalidColumns.Add($"{columnName} (invalid number)");
                            isValid = false;
                        }
                        else if (numericValue < 0)
                        {
                            invalidColumns.Add($"{columnName} (must be zero or a positive number)");
                            isValid = false;
                        }
                    }
                }
            }

            return isValid;
        }
        public static ErrorMessage ValidatePriceData(JObject row, Dictionary<string, bool> columns, out List<string> invalidColumns)
        {
            StringBuilder ErrorMessage1 = new StringBuilder();
            ErrorMessage errodata = new ErrorMessage();
            errodata.IsValid = true;
            string Message =string.Empty; // Assume all are valid initially
            invalidColumns = new List<string>();
            foreach (var column in columns)
            {
                string columnName = column.Key;
                bool isMandatory = column.Value;

                // Validate mandatory columns
               
                    var columnValue = row[columnName]?.ToString();

                    if (string.IsNullOrWhiteSpace(columnValue) ||Convert.ToDecimal(row[columnName]?.ToString()) <= 0)
                    {
                        ErrorMessage1.Append(" " + columnName + " cannot be empty or zero ");
                        invalidColumns.Add(columnName);
                    //LS.LogSheetExporter.LogToTextFile(0,
                    //    $"Validation Error: {columnName} is mandatory but missing or invalid.",
                    //    nameof(ValidateAndLog),
                    //    $"Row data: {row.ToString()}");

                    // Mark as invalid
                    errodata.IsValid = false;
                    }

                

            }
            if (!errodata.IsValid)
            {
                errodata.Message = ErrorMessage1.ToString();

            }
            return errodata;
        }
        public static bool ValidateAndLog(JObject row, Dictionary<string, bool> columns, out List<string> invalidColumns)
        {
            bool isValid = true; // Assume all are valid initially
            invalidColumns = new List<string>();
            foreach (var column in columns)
            {
                string columnName = column.Key;
                bool isMandatory = column.Value;

                // Validate mandatory columns
                if (isMandatory)
                {
                    var columnValue = row[columnName]?.ToString();

                    if (string.IsNullOrWhiteSpace(columnValue))
                    {
                        invalidColumns.Add(columnName);
                        //LS.LogSheetExporter.LogToTextFile(0,
                        //    $"Validation Error: {columnName} is mandatory but missing or invalid.",
                        //    nameof(ValidateAndLog),
                        //    $"Row data: {row.ToString()}");

                        // Mark as invalid
                        isValid = false;
                    }

                }

            }

            return isValid;
        }
        #region:: LocalTime Vinay :::
        /// <summary>

        public static DateTime LocalTimeBasedOnBranch(int BranchID, DateTime servertime, string connstring)
        {
            DateTime localtime = DateTime.Now;
            string StandardTimeZone = "India Standard Time";
            try
            {

                //using (conn = new SqlConnection(ConfigurationManager.ConnectionStrings["EPC"].ConnectionString))
                using (conn = new SqlConnection(connstring))
                {
                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken) { conn.Open(); }
                    DataTable dt = new DataTable();
                    sqlComm = new SqlCommand("SELECT TimeZoneID FROM GNM_Branch WHERE Branch_ID = @BranchID", conn);
                    sqlComm.Parameters.AddWithValue("@BranchID", BranchID);
                    int? Timezoneid = sqlComm.ExecuteScalar() as int?;
                    if (Timezoneid.HasValue)
                    {
                        SqlCommand cmdTimeZone = new SqlCommand("SELECT RefMasterDetail_Name FROM GNM_RefMasterDetail WHERE RefMasterDetail_ID = @TimezoneID", conn);
                        cmdTimeZone.Parameters.AddWithValue("@TimezoneID", Timezoneid.Value);

                        object result = cmdTimeZone.ExecuteScalar();
                        if (result != DBNull.Value && result != null)
                        {
                            StandardTimeZone = result.ToString();
                        }
                    }
                    StandardTimeZone = string.IsNullOrEmpty(StandardTimeZone) ? "India Standard Time" : StandardTimeZone;
                    localtime = TimeZoneInfo.ConvertTimeBySystemTimeZoneId(servertime, TimeZoneInfo.Local.Id, StandardTimeZone);
                }
            }

            catch (Exception ex)
            {
                var Request = new System.Net.Http.HttpRequestMessage();
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(UserID), connstring);
            }
            return localtime;
        }
        #endregion

        public static T GetValueFromDB<T>(string query, List<SqlParameter> parameters, string connectionString,int LogException, bool nextResult = false, int resultSetIndex = 0, bool isStoredProcedure = false)
        {
            T result = default;

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                conn.Open();

                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    cmd.CommandType = isStoredProcedure ? CommandType.StoredProcedure : CommandType.Text;
                    // Add parameters to the command
                    if (parameters != null && parameters.Count > 0)
                    {
                        cmd.Parameters.AddRange(parameters.ToArray());
                    }

                    // Execute the query
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        int currentResultSetIndex = 0;

                        // Iterate through the result sets
                        do
                        {
                            // Skip result sets until the target result set is reached
                            if (nextResult && currentResultSetIndex != resultSetIndex)
                            {
                                currentResultSetIndex++;
                                continue;
                            }

                            // If the result type is a collection (e.g., List<T>)
                            if (typeof(T).IsGenericType && typeof(T).GetGenericTypeDefinition() == typeof(List<>))
                            {
                                var list = Activator.CreateInstance<T>();
                                var columnPropertyMapping = typeof(T).GetGenericArguments()[0]
                                    .GetProperties()
                                    .Where(property => Enumerable.Range(0, reader.FieldCount)
                                                                 .Select(reader.GetName)
                                                                 .Contains(property.Name))
                                    .Select(property => new
                                    {
                                        Property = property,
                                        Ordinal = reader.GetOrdinal(property.Name),
                                        TargetType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType
                                    })
                                    .ToList();

                                while (reader.Read())
                                {
                                    var item = Activator.CreateInstance(typeof(T).GetGenericArguments()[0]);

                                    foreach (var mapping in columnPropertyMapping)
                                    {
                                        var value = reader[mapping.Ordinal];
                                        if (value != DBNull.Value)
                                        {
                                            // Set property value directly using the precomputed type
                                            mapping.Property.SetValue(item, Convert.ChangeType(value, mapping.TargetType));
                                        }
                                    }

                                    // Add item to list
                                    ((IList)list).Add(item);
                                }

                                result = (T)list;
                            }
                            // If the result is a scalar type (like string, bool, int)
                            else if (typeof(T) == typeof(string))
                            {
                                if (reader.Read())
                                {
                                    result = (T)Convert.ChangeType(reader[0], typeof(T));
                                }
                            }
                            else if (typeof(T) == typeof(bool))
                            {
                                if (reader.Read())
                                {
                                    result = (T)Convert.ChangeType(reader[0].ToString().ToUpper() == "TRUE", typeof(T));
                                }
                            }
                            else if (typeof(T) == typeof(int))
                            {
                                if (reader.Read())
                                {
                                    result = (T)Convert.ChangeType(reader[0], typeof(T));
                                }
                            }
                            // Handle cases for custom classes/complex objects
                            else if (typeof(T).IsClass && typeof(T) != typeof(string))
                            {
                                if (reader.Read())
                                {
                                    result = Activator.CreateInstance<T>(); // Create an instance of the class
                                    var columnPropertyMapping = typeof(T)
                                        .GetProperties()
                                        .Where(property => Enumerable.Range(0, reader.FieldCount)
                                                                     .Select(reader.GetName)
                                                                     .Contains(property.Name))
                                        .Select(property => new
                                        {
                                            Property = property,
                                            Ordinal = reader.GetOrdinal(property.Name),
                                            TargetType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType
                                        })
                                        .ToList();

                                    foreach (var mapping in columnPropertyMapping)
                                    {
                                        var value = reader[mapping.Ordinal];
                                        if (value != DBNull.Value)
                                        {
                                            // Set property value directly using the precomputed type
                                            mapping.Property.SetValue(result, Convert.ChangeType(value, mapping.TargetType));
                                        }
                                    }
                                }
                            }

                            // Increment the result set index after processing
                            currentResultSetIndex++;
                        }
                        while (reader.NextResult() && currentResultSetIndex <= resultSetIndex); // Loop until desired result set is found
                    }
                }
            }

            return result;
        }

        public static bool CheckPreffixSuffix(int CompanyID, int BranchID, string ObjectName,string connString,int LogException)
        {
          
            int ObjectID = GetObjectID(ObjectName);
            bool result = false;
            using (SqlConnection conn = new SqlConnection(connString))
            {
                string query1 = @"
                SELECT COUNT(*) 
                FROM GNM_PrefixSuffix 
                WHERE Branch_ID = @BranchID 
                  AND Object_ID = @ObjectID 
                  AND FromDate <= @CurrentDate 
                  AND ToDate >= @CurrentDate";


                SqlCommand command = null;

                try
                {
                    using (command = new SqlCommand(query1, conn))
                    {
                        command.CommandType = CommandType.Text;
                        command.Parameters.AddWithValue("@BranchID", BranchID);
                        command.Parameters.AddWithValue("@ObjectID", ObjectID);
                        command.Parameters.AddWithValue("@CurrentDate", DateTime.Now);

                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }
                        int Count = (int)command.ExecuteScalar();
                        result = Count > 0;
                    }
                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }
                finally
                {
                    
                }
            }
          

            if (!result)
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query2 = @"
                    SELECT COUNT(*) 
                    FROM GNM_PrefixSuffix 
                    WHERE Company_ID = @CompanyID 
                      AND Branch_ID IS NULL 
                      AND Object_ID = @ObjectID 
                      AND FromDate <= @CurrentDate 
                      AND ToDate >= @CurrentDate";


                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query2, conn))
                        {
                            command.CommandType = CommandType.Text;
                            command.Parameters.AddWithValue("@CompanyID", CompanyID);
                            command.Parameters.AddWithValue("@ObjectID", ObjectID);
                            command.Parameters.AddWithValue("@CurrentDate", DateTime.Now);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            int count = (int)command.ExecuteScalar();
                            result = count > 0;
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        
                    }
                }
                
            }
            return result;
        }

        public static int LogRequest(string endpoint, string requestBody, string clientIP, string category, string connString, int LogException)
        {
            int logId = 0;
            string query = @"
        INSERT INTO SAPDMS_API_Request_Log (API_Endpoint, RequestBody, ClientIP, Status,Category)
        OUTPUT INSERTED.LogID
        VALUES (@API_Endpoint, @RequestBody, @ClientIP, 'Pending',@Category)";

            using (SqlConnection conn = new SqlConnection(connString))
            using (SqlCommand cmd = new SqlCommand(query, conn))
            {
                cmd.Parameters.AddWithValue("@API_Endpoint", endpoint);
                cmd.Parameters.AddWithValue("@RequestBody", requestBody);
                cmd.Parameters.AddWithValue("@ClientIP", clientIP);
                cmd.Parameters.AddWithValue("@Category", category ?? (object)DBNull.Value);

                conn.Open();
                logId = (int)cmd.ExecuteScalar();
            }
            return logId;
        }

        public static void LogInsertStatus(int logId, string Message, bool isSuccess,string connString,string apiCategory)
        {
            string query = @"
            INSERT INTO SAPDMS_API_Status_Log (TYPE, LogID, Message)
            VALUES (@TYPE, @LogID, @Message)";

            using (SqlConnection conn = new SqlConnection(connString))
            using (SqlCommand cmd = new SqlCommand(query, conn))
            {
                cmd.Parameters.AddWithValue("@TYPE", isSuccess ? 1 : 0);
                cmd.Parameters.AddWithValue("@LogID", logId);
                cmd.Parameters.AddWithValue("@Message", Message);

                conn.Open();
                cmd.ExecuteNonQuery();
            }

            Common.UpdateAPIDashboard(apiCategory, isSuccess? "Success":"Error", connString);
        }
        public static void sendEmail(string connectionString,int LogException)
        {
          

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                try
                {
                    conn.Open();

                    string query = @"INSERT INTO [dbo].[GNM_Email]
                                ([Email_Subject], [Email_Body], [Email_To], [Email_cc], [Email_Bcc], 
                                 [Email_Queue_Date], [Email_Sent_Date], [Email_SentStatus], 
                                 [Email_Attachments], [Email_IsError], [NoOfAttempts]) 
                                VALUES 
                                (@Email_Subject, @Email_Body, @Email_To, @Email_cc, @Email_Bcc, 
                                 @Email_Queue_Date, @Email_Sent_Date, @Email_SentStatus, 
                                 @Email_Attachments, @Email_IsError, @NoOfAttempts)";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        // Add parameters
                        cmd.Parameters.AddWithValue("@Email_Subject", "Test Email Subject");
                        cmd.Parameters.AddWithValue("@Email_Body", "This is a test email body.");
                        cmd.Parameters.AddWithValue("@Email_To", "<EMAIL>");
                        cmd.Parameters.AddWithValue("@Email_cc", "<EMAIL>");
                        cmd.Parameters.AddWithValue("@Email_Bcc", "<EMAIL>");
                        cmd.Parameters.AddWithValue("@Email_Queue_Date", DateTime.Now);
                        cmd.Parameters.AddWithValue("@Email_Sent_Date", DBNull.Value); // Not sent yet
                        cmd.Parameters.AddWithValue("@Email_SentStatus", false);
                        cmd.Parameters.AddWithValue("@Email_Attachments", "attachment1.pdf");
                        cmd.Parameters.AddWithValue("@Email_IsError", false);
                        cmd.Parameters.AddWithValue("@NoOfAttempts", 0);

                        // Execute query
                        int rowsAffected = cmd.ExecuteNonQuery();

                        if (rowsAffected > 0)
                        {
                            
                        }
                        else
                        {
                            
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }
                }
            }

        }

        public static void InsertEmailLog(SqlConnection conn, string subject, string body, string recipient,string ccRecipient,SqlTransaction tran )
        {

            string emailInsertQuery = @"
            INSERT INTO [dbo].[GNM_Email] 
            ([Email_Subject], [Email_Body], [Email_To], [Email_cc], [Email_Queue_Date], [Email_SentStatus], [Email_IsError], [NoOfAttempts]) 
            VALUES 
            (@Email_Subject, @Email_Body, @Email_To, @Email_Cc, GETDATE(), 0, 0, 0)";
            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
            {
                conn.Open();
            }

            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
            {
                conn.Open();
            }

            using (SqlCommand cmdEmail = new SqlCommand(emailInsertQuery, conn, tran))
            {
                cmdEmail.Parameters.AddWithValue("@Email_Subject", subject);
                cmdEmail.Parameters.AddWithValue("@Email_Body", body);
                cmdEmail.Parameters.AddWithValue("@Email_To", recipient);
                cmdEmail.Parameters.AddWithValue("@Email_Cc", ccRecipient);
                cmdEmail.CommandType = CommandType.Text;
                //tran.Commit();
                int count = cmdEmail.ExecuteNonQuery();
               

            }
        }
        public static void InsertEmailLogInvalidJson(SqlConnection conn, string subject, string body, string recipient, string ccRecipient)
        {

            string emailInsertQuery = @"
            INSERT INTO [dbo].[GNM_Email] 
            ([Email_Subject], [Email_Body], [Email_To], [Email_cc], [Email_Queue_Date], [Email_SentStatus], [Email_IsError], [NoOfAttempts]) 
            VALUES 
            (@Email_Subject, @Email_Body, @Email_To, @Email_Cc, GETDATE(), 0, 0, 0)";
            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
            {
                conn.Open();
            }
            using (SqlCommand cmdEmail = new SqlCommand(emailInsertQuery, conn))
            {
                cmdEmail.Parameters.AddWithValue("@Email_Subject", subject);
                cmdEmail.Parameters.AddWithValue("@Email_Body", body);
                cmdEmail.Parameters.AddWithValue("@Email_To", recipient);
                cmdEmail.Parameters.AddWithValue("@Email_Cc", ccRecipient);
                cmdEmail.CommandType = CommandType.Text;
                cmdEmail.ExecuteNonQuery();

            }
        }

        public static string GetEmailTemplate(string partsHeader,string Parts_PartPrefix,string Parts_PartsNumber,string errType, string apiUrl, bool isSuccess,string emailSubject=null)
        {


            //SucessMail


            string emailTemplate = "";
            string emailTemplateSuccess = "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <style>\r\n        body {\r\n            font-family: Cambria, serif;\r\n            font-size: 12pt;\r\n            color: black;\r\n        }\r\n        .header {\r\n            font-size: 14pt;\r\n            font-weight: bold;\r\n            color: green;\r\n        }\r\n        table {\r\n            border-collapse: collapse;\r\n            width: 100%;\r\n            margin-top: 10px;\r\n        }\r\n        th, td {\r\n            border: 2px solid black;\r\n            padding: 5px;\r\n            text-align: left;\r\n        }\r\n        th {\r\n            background: rgb(217, 217, 217);\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <span>Dear Sir/Madam,</span><br><br>\r\n    <span class=\"header\">{{PARTS_HEADER}}-API Import Successful</span><br><br>\r\n    {{Details}}. Details are as follows:<br><br>\r\n    \r\n    <table>\r\n        <tbody>\r\n            <tr>\r\n                <td><b>Part Prefix:</b></td>\r\n                <td>{{PART_PREFIX}}</td>\r\n            </tr>\r\n            <tr>\r\n                <td><b>Part Number:</b></td>\r\n                <td>{{PART_NUMBER}}</td>\r\n            </tr>\r\n            <tr>\r\n                <td><b>API Request URL:</b></td>\r\n                <td>{{API_URL}}</td>\r\n            </tr>\r\n            <tr>\r\n                <td><b>Date & Time:</b></td>\r\n                <td>{{DATETIME}}</td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n    \r\n    <br>\r\n    <span>This is a system-generated email. Please do not reply.</span><br><br>\r\n    <span>Thanking You,<br>Admin</span>\r\n</body>\r\n</html>\r\n";
            string emailTemplateError = "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <style>\r\n        body {\r\n            font-family: Cambria, serif;\r\n            font-size: 12pt;\r\n            color: black;\r\n        }\r\n        .header {\r\n            font-size: 14pt;\r\n            font-weight: bold;\r\n            color: red;\r\n        }\r\n        table {\r\n            border-collapse: collapse;\r\n            width: 100%;\r\n            margin-top: 10px;\r\n        }\r\n        th, td {\r\n            border: 2px solid black;\r\n            padding: 5px;\r\n            text-align: left;\r\n        }\r\n        th {\r\n            background: rgb(217, 217, 217);\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <span>Dear Sir/Madam,</span><br><br>\r\n    <span class=\"header\">{{PARTS_HEADER}}- API Error Notification</span><br><br>\r\n    An error occurred while processing an API request from SAP. Details are as follows:<br><br>\r\n    \r\n    <table>\r\n        <tbody>\r\n            <tr>\r\n                <td><b>Part Prefix:</b></td>\r\n                <td>{{PART_PREFIX}}</td>\r\n            </tr>\r\n            <tr>\r\n                <td><b>Part Number:</b></td>\r\n                <td>{{PART_NUMBER}}</td>\r\n            </tr>\r\n            <tr>\r\n                <td><b>Error Type:</b></td>\r\n                <td>{{ERROR_TYPE}}</td>\r\n            </tr>\r\n            <tr>\r\n                <td><b>Error Description:</b></td>\r\n                <td>{{ERROR_DESCRIPTION}}</td>\r\n            </tr>\r\n            <tr>\r\n                <td><b>API Request URL:</b></td>\r\n                <td>{{API_URL}}</td>\r\n            </tr>\r\n            <tr>\r\n                <td><b>Date & Time:</b></td>\r\n                <td>{{DATETIME}}</td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n    \r\n    <br>\r\n    <span>This is a system-generated email. Please do not reply.</span><br><br>\r\n    <span>Thanking You,<br>Admin</span>\r\n</body>\r\n</html>\r\n";
            if (isSuccess)
            {
                emailTemplate = emailTemplateSuccess;
                emailTemplate = emailTemplate.Replace("{{Details}}", emailSubject);
                emailTemplate = emailTemplate.Replace("{{PARTS_HEADER}}", partsHeader);
                emailTemplate = emailTemplate.Replace("{{PART_PREFIX}}", Parts_PartPrefix);
                emailTemplate = emailTemplate.Replace("{{PART_NUMBER}}", Parts_PartsNumber);
                emailTemplate = emailTemplate.Replace("{{API_URL}}", apiUrl);
                emailTemplate = emailTemplate.Replace("{{DATETIME}}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            }
            else
            {
                emailTemplate = emailTemplateError;
                emailTemplate = emailTemplate.Replace("{{PARTS_HEADER}}", partsHeader);
                emailTemplate = emailTemplate.Replace("{{PART_PREFIX}}", Parts_PartPrefix);
                emailTemplate = emailTemplate.Replace("{{PART_NUMBER}}", Parts_PartsNumber);
                emailTemplate = emailTemplate.Replace("{{ERROR_TYPE}}", errType);
                emailTemplate = emailTemplate.Replace("{{ERROR_DESCRIPTION}}", emailSubject);
                emailTemplate = emailTemplate.Replace("{{API_URL}}", apiUrl);
                emailTemplate = emailTemplate.Replace("{{DATETIME}}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            }
          

            return emailTemplate;
        }
        public static string GetEmailTemplatePurchaseInvoice(string partsHeader, string errType, string apiUrl, bool isSuccess, string emailSubject = null)
        {


            //SucessMail


            string emailTemplate = "";
            string emailTemplateSuccess = "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <style>\r\n        body {\r\n            font-family: Cambria, serif;\r\n            font-size: 12pt;\r\n            color: black;\r\n        }\r\n        .header {\r\n            font-size: 14pt;\r\n            font-weight: bold;\r\n            color: green;\r\n        }\r\n        table {\r\n            border-collapse: collapse;\r\n            width: 100%;\r\n            margin-top: 10px;\r\n        }\r\n        th, td {\r\n            border: 2px solid black;\r\n            padding: 5px;\r\n            text-align: left;\r\n        }\r\n        th {\r\n            background: rgb(217, 217, 217);\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <span>Dear Sir/Madam,</span><br><br>\r\n    <span class=\"header\">{{PARTS_HEADER}}-API Import Successful</span><br><br>\r\n    {{Details}}. Details are as follows:<br><br>\r\n    \r\n    <table>\r\n        <tbody>       <tr>\r\n                <td><b>API Request URL:</b></td>\r\n                <td>{{API_URL}}</td>\r\n            </tr>\r\n            <tr>\r\n                <td><b>Date & Time:</b></td>\r\n                <td>{{DATETIME}}</td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n    \r\n    <br>\r\n    <span>This is a system-generated email. Please do not reply.</span><br><br>\r\n    <span>Thanking You,<br>Admin</span>\r\n</body>\r\n</html>\r\n";
            string emailTemplateError = "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <style>\r\n        body {\r\n            font-family: Cambria, serif;\r\n            font-size: 12pt;\r\n            color: black;\r\n        }\r\n        .header {\r\n            font-size: 14pt;\r\n            font-weight: bold;\r\n            color: red;\r\n        }\r\n        table {\r\n            border-collapse: collapse;\r\n            width: 100%;\r\n            margin-top: 10px;\r\n        }\r\n        th, td {\r\n            border: 2px solid black;\r\n            padding: 5px;\r\n            text-align: left;\r\n        }\r\n        th {\r\n            background: rgb(217, 217, 217);\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <span>Dear Sir/Madam,</span><br><br>\r\n    <span class=\"header\">{{PARTS_HEADER}}- API Error Notification</span><br><br>\r\n    An error occurred while processing an API request from SAP. Details are as follows:<br><br>\r\n    \r\n    <table>\r\n        <tbody>\r\n            <tr>            <td><b>Error Type:</b></td>\r\n                <td>{{ERROR_TYPE}}</td>\r\n            </tr>\r\n            <tr>\r\n                <td><b>Error Description:</b></td>\r\n                <td>{{ERROR_DESCRIPTION}}</td>\r\n            </tr>\r\n            <tr>\r\n                <td><b>API Request URL:</b></td>\r\n                <td>{{API_URL}}</td>\r\n            </tr>\r\n            <tr>\r\n                <td><b>Date & Time:</b></td>\r\n                <td>{{DATETIME}}</td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n    \r\n    <br>\r\n    <span>This is a system-generated email. Please do not reply.</span><br><br>\r\n    <span>Thanking You,<br>Admin</span>\r\n</body>\r\n</html>\r\n";
            if (isSuccess)
            {
                emailTemplate = emailTemplateSuccess;
                emailTemplate = emailTemplate.Replace("{{Details}}", emailSubject);
                emailTemplate = emailTemplate.Replace("{{PARTS_HEADER}}", partsHeader);
               
                emailTemplate = emailTemplate.Replace("{{API_URL}}", apiUrl);
                emailTemplate = emailTemplate.Replace("{{DATETIME}}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            }
            else
            {
                emailTemplate = emailTemplateError;
                emailTemplate = emailTemplate.Replace("{{PARTS_HEADER}}", partsHeader);
               
                emailTemplate = emailTemplate.Replace("{{ERROR_TYPE}}", errType);
                emailTemplate = emailTemplate.Replace("{{ERROR_DESCRIPTION}}", emailSubject);
                emailTemplate = emailTemplate.Replace("{{API_URL}}", apiUrl);
                emailTemplate = emailTemplate.Replace("{{DATETIME}}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            }


            return emailTemplate;
        }
        public static string GetEmailTemplatePurchaseOrderCancellation(string partsHeader, string errType, string apiUrl, bool isSuccess, string PONumber,string emailSubject = null)
        {


            //SucessMail


            string emailTemplate = "";
            string emailTemplateSuccess = "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <style>\r\n        body {\r\n            font-family: Cambria, serif;\r\n            font-size: 12pt;\r\n            color: black;\r\n        }\r\n        .header {\r\n            font-size: 14pt;\r\n            font-weight: bold;\r\n            color: green;\r\n        }\r\n        table {\r\n            border-collapse: collapse;\r\n            width: 100%;\r\n            margin-top: 10px;\r\n        }\r\n        th, td {\r\n            border: 2px solid black;\r\n            padding: 5px;\r\n            text-align: left;\r\n        }\r\n        th {\r\n            background: rgb(217, 217, 217);\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <span>Dear Sir/Madam,</span><br><br>\r\n    <span class=\"header\">{{PARTS_HEADER}}-API Import Successful</span><br><br>\r\n    {{Details}}. Details are as follows:<br><br>\r\n    \r\n    <table>\r\n        <tbody>   <tr>\r\n                <td><b>Purchase Order Number:</b></td>\r\n                <td>{{Purchase_Order_Number}}</td>\r\n            </tr>\r\n      <tr>\r\n                <td><b>API Request URL:</b></td>\r\n                <td>{{API_URL}}</td>\r\n            </tr>\r\n            <tr>\r\n                <td><b>Date & Time:</b></td>\r\n                <td>{{DATETIME}}</td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n    \r\n    <br>\r\n    <span>This is a system-generated email. Please do not reply.</span><br><br>\r\n    <span>Thanking You,<br>Admin</span>\r\n</body>\r\n</html>\r\n";
            string emailTemplateError = "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <style>\r\n        body {\r\n            font-family: Cambria, serif;\r\n            font-size: 12pt;\r\n            color: black;\r\n        }\r\n        .header {\r\n            font-size: 14pt;\r\n            font-weight: bold;\r\n            color: red;\r\n        }\r\n        table {\r\n            border-collapse: collapse;\r\n            width: 100%;\r\n            margin-top: 10px;\r\n        }\r\n        th, td {\r\n            border: 2px solid black;\r\n            padding: 5px;\r\n            text-align: left;\r\n        }\r\n        th {\r\n            background: rgb(217, 217, 217);\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <span>Dear Sir/Madam,</span><br><br>\r\n    <span class=\"header\">{{PARTS_HEADER}}- API Error Notification</span><br><br>\r\n    An error occurred while processing an API request from SAP. Details are as follows:<br><br>\r\n    \r\n    <table>\r\n        <tbody>\r\n    <tr>\r\n                <td><b>Purchase Order Number:</b></td>\r\n                <td>{{Purchase_Order_Number}}</td>\r\n            </tr>\r\n         <tr>            <td><b>Error Type:</b></td>\r\n                <td>{{ERROR_TYPE}}</td>\r\n            </tr>\r\n            <tr>\r\n                <td><b>Error Description:</b></td>\r\n                <td>{{ERROR_DESCRIPTION}}</td>\r\n            </tr>\r\n            <tr>\r\n                <td><b>API Request URL:</b></td>\r\n                <td>{{API_URL}}</td>\r\n            </tr>\r\n            <tr>\r\n                <td><b>Date & Time:</b></td>\r\n                <td>{{DATETIME}}</td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n    \r\n    <br>\r\n    <span>This is a system-generated email. Please do not reply.</span><br><br>\r\n    <span>Thanking You,<br>Admin</span>\r\n</body>\r\n</html>\r\n";
            if (isSuccess)
            {
                
                emailTemplate = emailTemplateSuccess;
                emailTemplate = emailTemplate.Replace("{{Purchase_Order_Number}}", PONumber);
                emailTemplate = emailTemplate.Replace("{{Details}}", emailSubject);
                emailTemplate = emailTemplate.Replace("{{PARTS_HEADER}}", partsHeader);

                emailTemplate = emailTemplate.Replace("{{API_URL}}", apiUrl);
                emailTemplate = emailTemplate.Replace("{{DATETIME}}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            }
            else
            {
                emailTemplate = emailTemplateError;
                emailTemplate = emailTemplate.Replace("{{PARTS_HEADER}}", partsHeader);

                emailTemplate = emailTemplate.Replace("{{ERROR_TYPE}}", errType);
                emailTemplate = emailTemplate.Replace("{{ERROR_DESCRIPTION}}", emailSubject);
                emailTemplate = emailTemplate.Replace("{{API_URL}}", apiUrl);
                emailTemplate = emailTemplate.Replace("{{DATETIME}}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            }


            return emailTemplate;
        }
        public static string GetResponse(string sURL)
        {
            string sResponse = string.Empty;
            try
            {
                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(sURL);
                request.MaximumAutomaticRedirections = 4;
                request.Credentials = CredentialCache.DefaultCredentials;
                HttpWebResponse response = (HttpWebResponse)request.GetResponse();
                Stream receiveStream = response.GetResponseStream();
                StreamReader readStream = new StreamReader(receiveStream, Encoding.UTF8);
                sResponse = readStream.ReadToEnd();
                response.Close();
                readStream.Close();
            }
            catch (Exception ex)
            {
                //if (LogException == 1)
                //{
                //    LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                //}
            }
            return sResponse;
        }
        public static void SendSMS(SMSData SMSHeader)
        {
            string sUserID = ConfigurationManager.AppSettings.Get("WebUserName").ToString();
            string sPwd = ConfigurationManager.AppSettings.Get("WebPassword").ToString();
            string senderID = ConfigurationManager.AppSettings.Get("SenderID").ToString();
            string EntityID = ConfigurationManager.AppSettings.Get("EntityID").ToString();
            string workingKey = ConfigurationManager.AppSettings.Get("WorkingKey").ToString();
            string ConnectionString = ConfigurationManager.ConnectionStrings["YANMAR"].ConnectionString.ToString();
            SqlConnection Sqlcon = new SqlConnection(ConnectionString);
            try
            {
                string url = "http://alerts.jiffysms.com/api/v3/index.php?method=sms&api_key=Ada80004d4daf58439f04aef8158fbb19&to=" + SMSHeader.MobileNumber + "&sender=" + senderID + "&message=" + SMSHeader.Message + "&entity_id=" + EntityID;
                string sResponse = Common.GetResponse(url);
                if (sResponse != "")
                {
                    //using (Sqlcon = new SqlConnection(ConnectionString))
                    //{
                    Sqlcon.Open();
                    SqlCommand sqlCmd1 = new SqlCommand();
                    sqlCmd1.CommandType = CommandType.Text;
                    string query = "DECLARE @SMSint int =(select top 1 Sms_ID From GNM_Sms where Sms_Mobile_Number=" + SMSHeader.MobileNumber + " and isnull(Sms_SentStatus,0)=0 and Template_ID=" + SMSHeader.TemplateID + "  and cast(SMS_Queue_Date as date)=cast(GETDATE() as date) order by SMS_ID desc)";
                    query += "   update GNM_Sms set Sms_Sent_Date = getdate(), Sms_SentStatus = 1 where Sms_ID = @SMSint";
                    sqlCmd1 = new SqlCommand(query, Sqlcon);
                    sqlCmd1.ExecuteNonQuery();
                    //}
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.InnerException.ToString(), ex.TargetSite.ToString(), ex.StackTrace);

            }
            finally
            {
                Sqlcon.Close();
                Sqlcon.Dispose();
                SqlConnection.ClearPool(Sqlcon);
                SqlConnection.ClearAllPools();
            }
        }
        public static string GetEmailBodyAddressee(string TransactionName, string TransactionNumber, string HeadLine,string Branch_Name,string Company_Name)
        {
            StringBuilder result = new StringBuilder();
            try
            {
               
                string ApplicationLink = ConfigurationManager.AppSettings.Get("WebsiteLink");
                string BranchName = Branch_Name.ToString();
                string CompanyName = Company_Name.ToString();
                result.Append("<br/>");
                result.Append("<br/>");
                result.Append("Branch Name : " + BranchName);
                result.Append("<br/>");
                result.Append("Transaction Name :  " + TransactionName + "");
                result.Append("<br/>");
                result.Append("Transaction # :  " + TransactionNumber + "");
                result.Append("<br/>");
                result.Append("Transaction Date :  " + DateTime.Now.ToString("dd-MMM-yyyy"));
                result.Append("<br/>");
                result.Append("<br/>");
                result.Append(HeadLine);
                result.Append("<br/>");
                result.Append("<br/>");
                result.Append("Please click the link to login  ");
                result.Append("<a href='" + ApplicationLink + "'>AfterMarket ERP</a> ");
                result.Append("<br/>");
                result.Append("<br/>");
                result.Append("This is an auto mail, please do not reply.");
                result.Append("<br/>");
                result.Append("<br/>");
                result.Append("Thanks You");
                result.Append("<br/>");
                result.Append("Admin");
                result.Append("<br/>");
                result.Append(BranchName);
                result.Append("<br/>");
                result.Append(CompanyName);
                result.Append("<br/>");
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return result.ToString();
        }
        public static string GetEmailBodyCustomer(string TransactionName, string TransactionNumber, string HeadLine, string Branch_Name, string Company_Name)
        {
            StringBuilder result = new StringBuilder();
            try
            {
               
                string ApplicationLink = ConfigurationManager.AppSettings.Get("WebsiteLink");
                string BranchName = Branch_Name.ToString();
                string CompanyName =Company_Name.ToString();

                result.Append("<br/>");
                result.Append("<br/>");
                result.Append("Branch Name : " + BranchName);
                result.Append("<br/>");
                result.Append("Transaction Name :  " + TransactionName + "");
                result.Append("<br/>");
                result.Append("Transaction # :  " + TransactionNumber + "");
                result.Append("<br/>");
                result.Append("Transaction Date :  " + DateTime.Now.ToString("dd-MMM-yyyy"));
                result.Append("<br/>");
                result.Append("<br/>");
                result.Append(HeadLine);
                //result.Append("<br/>");
                //result.Append("<br/>");
                //result.Append("Please click the link to login  ");
                //result.Append("<a href='" + ApplicationLink + "'>AfterMarket ERP</a> ");
                result.Append("<br/>");
                result.Append("<br/>");
                result.Append("This is an auto mail, please do not reply.");
                result.Append("<br/>");
                result.Append("<br/>");
                result.Append("Thanks You");
                result.Append("<br/>");
                result.Append("Admin");
                result.Append("<br/>");
                result.Append(BranchName);
                result.Append("<br/>");
                result.Append(CompanyName);
                result.Append("<br/>");
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return result.ToString();
        }
    }
    public class SMSData
    {
        public string Message { get; set; }
        public string MobileNumber { get; set; }
        public int TemplateID { get; set; }
    }
    public class ErrorMessage
    {
        public string Message { get; set; }
        public bool IsValid { get; set; }
    }
}
