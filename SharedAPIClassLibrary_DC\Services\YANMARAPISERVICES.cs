﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Text;
using SharedAPIClassLibrary_DC.Utilities;
using static SharedAPIClassLibrary_AMERP.CoreCompanyMasterServices;
using SharedAPIClassLibrary_AMERP.Utilities;
using System.Linq;
using LS = LogSheetExporter;
using System.Threading.Tasks;
using System.Net.Http;
using System.Net;
using DocumentFormat.OpenXml.Wordprocessing;
using Org.BouncyCastle.Cms;
using System.Security.Cryptography.Pkcs;
using Amazon.S3.Model;
using SharedAPIClassLibrary_DC;
using Amazon.Runtime.Internal.Transform;
using System.Xml.Linq;
using System.Globalization;
using System.Transactions;

namespace SharedAPIClassLibrary_AMERP.Services
{
    public class YANMARAPISERVICES
    {
        #region PARTS MASTER
        /// <summary>
        /// VINAY 
        /// Modifyed by DK 
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="constring"></param>
        /// <param name="LogException"></param>
        /// <param name="clientIP"></param>
        /// <param name="fullUrl"></param>
        /// <param name="Recipient"></param>
        /// <param name="ccRecipient"></param>
        /// <returns></returns>
        public static IActionResult PartsMasterInsert(PartsMasterList Obj, string constring, int LogException, string clientIP, string fullUrl, string Recipient, string ccRecipient)
        {
            string partHeader = "Parts Master";
            string apiEndpoint = fullUrl;
            string requestBody = JsonConvert.SerializeObject(Obj);
            int? movementTypeID = 0;
            int? partsCategoryID = 0;
            int? functionGroupID = 0;
            int? unitOfMeasurementID = 0;
            int? userID = 0;
            int? companyID = 0;
            int? exciseDutyID = 0;
            int? salvagePartID = 0;
            int? partTypeID = 0;
            int? partsDisposalID = 0;
            var x = default(dynamic);
            int NParts_ID = 0;
            int? BinLocation_ID = 0;
            int logID = 0;
            bool somePartsNotInserted = false;
            int? CurrencyID = 0;
            int? Supplier_ID = 0;
            int? Part_ID = 0;
            int? Branch_ID = 0;
            int? WarehouseID = 0;
            bool invalidJson = false;
            bool isUpdated = false;
            string apiCategory = "PartsMaster";
            logID = Common.LogRequest(apiEndpoint, requestBody, clientIP, apiCategory, constring, LogException);

            using (SqlConnection conn = new SqlConnection(constring))
            {
                foreach (var part in Obj.PartsDetails)
                {
                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                    {
                        conn.Open();
                    }
                    SqlTransaction tran = conn.BeginTransaction();

                    bool isRowValid = true;
                    bool IsNegativeValidGreaterThanZero = true;
                    bool IsNegativeValidGreaterThanorEqualZero = true;
                    bool IsUnitOfMeasurementValid = true;
                    var partDetail = part;
                    JObject row = JObject.FromObject(new
                    {
                        Parts_PartPrefix = partDetail.Parts_PartPrefix,
                        Parts_PartsNumber = partDetail.Parts_PartsNumber,
                        Parts_PartsDescription = partDetail.Parts_PartsDescription,
                        Parts_AliasPartPrefix = partDetail.Parts_AliasPartPrefix ?? (object)null,
                        Parts_AliasPartNumber = partDetail.Parts_AliasPartNumber ?? (object)null,
                        MovementType_Description = partDetail.MovementType_Description,
                        Parts_Weight = partDetail.Parts_Weight,
                        Parts_Dimensions = partDetail.Parts_Dimensions ?? (object)null,
                        PartsCategory_Description = partDetail.PartsCategory_Description,
                        FunctionGroup_Name = partDetail.FunctionGroup_Name ?? (object)null,
                        UnitOfMeasurement_Short_Name = partDetail.UnitOfMeasurement_Short_Name,
                        Parts_IsActive = partDetail.Parts_IsActive,
                        Parts_IsHazardousGood = partDetail.Parts_IsHazardousGood,
                        User_LoginID = Obj.User_LoginID,
                        ModifiedDate = partDetail.ModifiedDate,
                        BRANCH_SHORTNAME = Obj.BRANCH_SHORTNAME,
                        Parts_IsLocal = partDetail.Parts_IsLocal,
                        Parts_IsComponent = partDetail.Parts_IsComponent,
                        IsKitPart = partDetail.IsKitPart,
                        ExciseDuty_SHORTNAME = partDetail.ExciseDuty_SHORTNAME,
                        SalvagePart_Parts_PartPrefix = partDetail.SalvagePart_Parts_PartPrefix,
                        SalvagePart_Parts_PartsNumber = partDetail.SalvagePart_Parts_PartsNumber,
                        PartType_SHORTNAME = partDetail.PartType_SHORTNAME,
                        PartsDisposal_SHORTNAME = partDetail.PartsDisposal_SHORTNAME,
                        Currency_Short_Name = partDetail.Currency_Short_Name,
                        PartyCode = partDetail.PartyCode,
                        StandardPackageQuantity = partDetail.StandardPackageQuantity,
                        SAPEffectiveDate = partDetail.SAPEffectiveDate,
                        BlockForSales = partDetail.BlockforSales,
                        BlockForPurchase = partDetail.IsPurchasable,
                        ReorderLevel = partDetail.ReorderLevel,
                        ReorderLevelQty = partDetail.ReorderLevelQty
                    });

                    var columns = new Dictionary<string, bool>
                        {
                            { "Parts_PartPrefix", true },
                            { "Parts_PartsNumber", true },
                            { "Parts_PartsDescription", true },
                            { "Parts_AliasPartPrefix", false },
                            { "Parts_AliasPartNumber", true },
                            { "MovementType_Description", true },
                            { "Parts_Weight", false },
                            { "Parts_Dimensions", false },
                            { "PartsCategory_Description", true },
                            { "FunctionGroup_Name", false },
                            { "UnitOfMeasurement_Short_Name", true },
                            { "Parts_IsActive", true },
                            { "Parts_IsHazardousGood", false },
                            { "User_LoginID", true },
                            { "ModifiedDate", false },
                            { "BRANCH_SHORTNAME", true },
                            { "Parts_IsLocal", false },
                            { "Parts_IsComponent", false },
                            { "IsKitPart", false },
                            { "ExciseDuty_SHORTNAME", true },
                            { "SalvagePart_Parts_PartPrefix", false },
                            { "SalvagePart_Parts_PartsNumber", false },
                            { "PartType_SHORTNAME", false },
                            { "PartsDisposal_SHORTNAME", false },
                            {"Currency_Short_Name",true },
                            {"PartyCode",true },
                            {"StandardPackageQuantity",false },
                            {"SAPEffectiveDate",true },
                            {"BlockForSales",false },
                            {"BlockForPurchase",false },
                        };
                    List<string> invalidColumns;
                    isRowValid = Common.ValidateAndLog(row, columns, out invalidColumns);
                    if (!isRowValid)
                    {
                        string invalidColumnsMessage = string.Join(", ", invalidColumns);
                        somePartsNotInserted = true;
                        Common.LogInsertStatus(logID,$"Part with prefix {part.Parts_PartPrefix}, Number {part.Parts_PartsNumber} has null columns. {invalidColumnsMessage}",false, constring, apiCategory);


                        string emailSubject = $"Part with prefix {part.Parts_PartPrefix}, number {part.Parts_PartsNumber} insertion failed.";

                        string emailBody = $"Prefix: {part.Parts_PartPrefix}\n" +
                                      $"Part Number: {part.Parts_PartsNumber}\n\n" +
                                      $"Missing columns: {invalidColumnsMessage}\n\n";

                        string emailTemplate = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, false, emailBody);

                        Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);

                        continue; // Skip to the next part

                    }   // Covered
                    var NonNegativeColumnGreaterThanZero = new Dictionary<string, bool>
                    {   { "Parts_Weight", true },{ "StandardPackageQuantity", true },
                    };
                    IsNegativeValidGreaterThanZero = Common.ValidateNegativeValueGreaterThanZero(row, NonNegativeColumnGreaterThanZero, out invalidColumns);
                    if (!IsNegativeValidGreaterThanZero)
                    {
                        string invalidColumnsMessage = string.Join(", ", invalidColumns);
                        somePartsNotInserted = true;

                        Common.LogInsertStatus(logID, $"Part with prefix {part.Parts_PartPrefix}, Number {part.Parts_PartsNumber} has invalid numeric columns. {invalidColumnsMessage}", false, constring, apiCategory);
                        string emailSubject = $"Part with prefix {part.Parts_PartPrefix}, number {part.Parts_PartsNumber} failed validation (numeric fields).";
                        string emailBody = $"Prefix: {part.Parts_PartPrefix}\n" + $"Part Number: {part.Parts_PartsNumber}\n\n" + $"Invalid numeric columns (must be > 0): {invalidColumnsMessage}\n\n";
                        string emailTemplate = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation (Non-Negative Check)", fullUrl, false, emailBody);
                        Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);

                        continue;
                    }
                    var NonNegativeColumnGreaterThanorEqualZero = new Dictionary<string, bool>
                    {  { "ReorderLevel", true },{ "ReorderLevelQty", true },
                    };

                    IsNegativeValidGreaterThanorEqualZero = Common.ValidateNegativeValueGreaterThanorEqualZero(row, NonNegativeColumnGreaterThanorEqualZero, out invalidColumns);
                    if (!IsNegativeValidGreaterThanorEqualZero)
                    {
                        string invalidColumnsMessage = string.Join(", ", invalidColumns);
                        somePartsNotInserted = true;

                        Common.LogInsertStatus(logID, $"Part with prefix {part.Parts_PartPrefix}, Number {part.Parts_PartsNumber} has invalid numeric columns. {invalidColumnsMessage}", false, constring, apiCategory);
                        string emailSubject = $"Part with prefix {part.Parts_PartPrefix}, number {part.Parts_PartsNumber} failed validation (numeric fields).";
                        string emailBody = $"Prefix: {part.Parts_PartPrefix}\n" + $"Part Number: {part.Parts_PartsNumber}\n\n" + $"Invalid numeric columns (must be > 0): {invalidColumnsMessage}\n\n";
                        string emailTemplate = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation (Non-Negative Check)", fullUrl, false, emailBody);
                        Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);

                        continue;
                    }
                    
                    using (SqlCommand cmd = new SqlCommand("UP_AMERP_PARTS_MASTER_INSERT_INITIALIZE", conn, tran))
                    {
                        cmd.CommandType = CommandType.StoredProcedure; // Add this line!

                        cmd.Parameters.AddWithValue("@MovementType_Description", part.MovementType_Description ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@PartsCategory_Description", part.PartsCategory_Description ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@FunctionGroup_Name", part.FunctionGroup_Name ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@UnitOfMeasurement_Short_Name", part.UnitOfMeasurement_Short_Name ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@User_LognID", Obj.User_LoginID);
                        cmd.Parameters.AddWithValue("@Branch_ShortName", Obj.BRANCH_SHORTNAME ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ExciseDuty_Short_Name", part.ExciseDuty_SHORTNAME ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@PartType_Short_Name", part.PartType_SHORTNAME ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@SalvagePart_Prefix", part.SalvagePart_Parts_PartPrefix ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@SalvagePartsNumber", part.SalvagePart_Parts_PartsNumber ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@PartsDisposal_Short_Name", part.PartsDisposal_SHORTNAME ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Currency_Short_Name", part.Currency_Short_Name ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Parts_PartPrefix", part.Parts_PartPrefix ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@PartyCode", part.PartyCode ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Parts_PartsNumber", part.Parts_PartsNumber ?? (object)DBNull.Value);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.HasRows)
                            {
                                while (reader.Read())
                                {
                                    movementTypeID = reader.IsDBNull(reader.GetOrdinal("MovementType_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("MovementType_ID"));
                                    partsCategoryID = reader.IsDBNull(reader.GetOrdinal("PartsCategory_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("PartsCategory_ID"));
                                    functionGroupID = reader.IsDBNull(reader.GetOrdinal("FunctionGroup_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("FunctionGroup_ID"));
                                    unitOfMeasurementID = reader.IsDBNull(reader.GetOrdinal("UnitOfMeasurement_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("UnitOfMeasurement_ID"));
                                    userID = reader.IsDBNull(reader.GetOrdinal("User_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("User_ID"));
                                    companyID = reader.IsDBNull(reader.GetOrdinal("Company_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Company_ID"));
                                    exciseDutyID = reader.IsDBNull(reader.GetOrdinal("ExciseDuty_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("ExciseDuty_ID"));
                                    salvagePartID = reader.IsDBNull(reader.GetOrdinal("SalvagePart_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("SalvagePart_ID"));
                                    partTypeID = reader.IsDBNull(reader.GetOrdinal("PartType_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("PartType_ID"));
                                    partsDisposalID = reader.IsDBNull(reader.GetOrdinal("PartsDisposal_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("PartsDisposal_ID"));
                                    CurrencyID = reader.IsDBNull(reader.GetOrdinal("CurrencyID")) ? 0 : reader.GetInt32(reader.GetOrdinal("CurrencyID"));
                                    Supplier_ID = reader.IsDBNull(reader.GetOrdinal("Supplier_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Supplier_ID"));
                                    Part_ID = reader.IsDBNull(reader.GetOrdinal("Part_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Part_ID"));
                                    Branch_ID = reader.IsDBNull(reader.GetOrdinal("Branch_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Branch_ID"));
                                    WarehouseID = reader.IsDBNull(reader.GetOrdinal("WarehouseID")) ? 0 : reader.GetInt32(reader.GetOrdinal("WarehouseID"));
                                    BinLocation_ID = reader.IsDBNull(reader.GetOrdinal("BinLocation_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("BinLocation_ID"));
                                }
                            }
                        }
                    }

                    JObject row2 = JObject.FromObject(new
                    {

                        MovementType_ID = movementTypeID == 0 ? (object)null : movementTypeID,
                        PartsCategory_ID = partsCategoryID == 0 ? (object)null : partsCategoryID,
                        FunctionGroup_ID = functionGroupID == 0 ? (object)null : functionGroupID,
                        UnitOfMeasurement_ID = unitOfMeasurementID == 0 ? (object)null : unitOfMeasurementID,
                        User_ID = userID == 0 ? (object)null : userID,
                        Company_ID = companyID == 0 ? (object)null : companyID,
                        ExciseDuty_ID = exciseDutyID == 0 ? (object)null : exciseDutyID,
                        SalvagePart_ID = salvagePartID == 0 ? (object)null : salvagePartID,
                        PartType_ID = partTypeID == 0 ? (object)null : partTypeID,
                        PartsDisposal_ID = partsDisposalID == 0 ? (object)null : partsDisposalID,
                        CurrencyID = CurrencyID == 0 ? (object)null : CurrencyID,
                        Supplier_ID = Supplier_ID == 0 ? (object)null : Supplier_ID,
                        Part_ID = Part_ID == 0 ? (object)null : Part_ID,
                    });
                    var col = new Dictionary<string, bool>
                        {

                            { "MovementType_ID", true },
                            { "PartsCategory_ID", true },
                            { "FunctionGroup_ID", false },
                            { "UnitOfMeasurement_ID", true },
                            { "ExciseDuty_ID", true },
                            { "SalvagePart_ID", false },
                            { "PartType_ID", false },
                            { "PartsDisposal_ID", false },
                            { "CurrencyID", true },
                            { "Supplier_ID", true },
                            { "Part_ID", false },
                        };
                    List<string> invalidColumns2;
                    isRowValid = Common.ValidateAndLog(row2, col, out invalidColumns2);
                    if (!isRowValid)
                    {
                        string invalidColumnsMessage = string.Join(", ", invalidColumns2);
                        somePartsNotInserted = true;
                        Common.LogInsertStatus(logID,
                         $"Part with prefix '{part.Parts_PartPrefix}', Number '{part.Parts_PartsNumber}' has invalid input columns: {invalidColumnsMessage}.",
                         false, constring, apiCategory);
                        string emailSubject = $"Part with prefix {part.Parts_PartPrefix}, number {part.Parts_PartsNumber} insertion failed.";

                        string emailBody = $"Prefix: {part.Parts_PartPrefix}\n" +
                                      $"Part Number: {part.Parts_PartsNumber}\n\n" +
                                      $"Invalid columns: {invalidColumnsMessage}\n\n";

                        string emailTemplate = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, false, emailBody);

                        Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                        continue; // Skip to the next part

                    }

                    try
                    {
                        // JR = InsertPart(companyID, userID, Obj.data, constring, LogException);
                        if (Part_ID != 0)
                        {
                            SqlCommand sqlCmd = null;
                            string strSql = "UPDATE GNM_Parts SET Parts_PartsDescription = @PartsDescription, " +
                            "UnitOfMeasurement_ID = @UnitOfMeasurementID, " +
                            "Parts_Dimensions = @PartsDimensions, " +
                            "Parts_Weight = @PartsWeight, " +
                            "Parts_IsActive = @PartsIsActive, " +
                            "ExciseDuty_ID = @ExciseDutyID, " +
                            "ModifiedDate = @ModifiedDate," + "Parts_IsLocal = @PartsIsLocal," + "BlockForPurchase=@BlockForPurchase," + "Parts_IsHazardousGood=@PartsIsHazardousGood," + "Parts_IsComponent=@PartsIsComponent," + "IsKitPart=@IsKitPart," + "Parts_AliasPartNumber = @PartsAliasPartNumber " +
                            "WHERE Parts_PartsNumber = @PartsNumber AND Parts_PartPrefix = 'OEM'";

                            sqlCmd = new SqlCommand(strSql, conn, tran);
                            sqlCmd.CommandType = CommandType.Text;

                            // Add parameters safely
                            sqlCmd.Parameters.AddWithValue("@PartsDescription", part.Parts_PartsDescription);
                            sqlCmd.Parameters.AddWithValue("@UnitOfMeasurementID", unitOfMeasurementID);
                            sqlCmd.Parameters.AddWithValue("@PartsDimensions", part.Parts_Dimensions);
                            sqlCmd.Parameters.AddWithValue("@PartsWeight", part.Parts_Weight);
                            sqlCmd.Parameters.AddWithValue("@PartsIsActive", part.Parts_IsActive ? 1 : 0);
                            sqlCmd.Parameters.AddWithValue("@ExciseDutyID", exciseDutyID);
                            sqlCmd.Parameters.AddWithValue("@ModifiedDate", DateTime.Now); // Correct way to pass datetime
                            sqlCmd.Parameters.AddWithValue("@PartsNumber", part.Parts_PartsNumber);
                            sqlCmd.Parameters.AddWithValue("@PartsAliasPartNumber", part.Parts_AliasPartNumber);
                            sqlCmd.Parameters.AddWithValue("@PartsIsLocal", part.Parts_IsLocal);
                            sqlCmd.Parameters.AddWithValue("@BlockForPurchase", part.IsPurchasable?1:0);
                            sqlCmd.Parameters.AddWithValue("@PartsIsHazardousGood", part.Parts_IsHazardousGood ? 1 : 0);
                            sqlCmd.Parameters.AddWithValue("@PartsIsComponent", part.Parts_IsComponent ? 1 : 0);
                            sqlCmd.Parameters.AddWithValue("@IsKitPart", part.IsKitPart ? 1 : 0);

                            sqlCmd.ExecuteNonQuery();
                            Common.LogInsertStatus(logID,
                                        $"Part with part prefix '{part.Parts_PartPrefix}' and part number '{part.Parts_PartsNumber}' successfully updated.",
                                        true, constring, apiCategory);

                            string emailSubject = $"Part with prefix {part.Parts_PartPrefix} and number {part.Parts_PartsNumber} successfully updated.";


                            string emailTemplate = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, true, emailSubject);

                            Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);

                            isUpdated = true;

                            string strSqlSup = "select * from GNM_PartsSupplierDetail where Parts_ID=" + Part_ID + " and Supplier_ID=" + Supplier_ID + "";
                            sqlCmd = new SqlCommand();
                            DataTable DtPartSupplierDetail = new DataTable();
                            sqlCmd.CommandType = CommandType.Text;
                            sqlCmd.Transaction = tran;
                            sqlCmd.CommandText = strSqlSup;
                            sqlCmd.CommandTimeout = 0;
                            sqlCmd.Connection = conn;
                            SqlDataAdapter DaPartsSupplierDetail = new SqlDataAdapter(sqlCmd);
                            DaPartsSupplierDetail.Fill(DtPartSupplierDetail);

                            if (DtPartSupplierDetail.Rows.Count == 0)
                            {
                                decimal StandardPackingQuantity = part.StandardPackageQuantity == null ? 1 : part.StandardPackageQuantity;
                                string insertsupplierPrice = "INSERT INTO GNM_PartsSupplierDetail VALUES(" + Part_ID + "," + Supplier_ID + "," + StandardPackingQuantity + ",0.00,'" + part.SAPEffectiveDate + "',NULL," + CurrencyID + ",NULL,''," + companyID + ",'',NULL,0,0.00,NULL,NULL,NULL,NULL)";
                                SqlCommand sqlCmdinsertsupplierPrice = new SqlCommand();
                                sqlCmdinsertsupplierPrice.CommandType = CommandType.Text;
                                sqlCmdinsertsupplierPrice.Transaction = tran;
                                sqlCmdinsertsupplierPrice.CommandText = insertsupplierPrice;
                                sqlCmdinsertsupplierPrice.CommandTimeout = 0;
                                sqlCmdinsertsupplierPrice.Connection = conn;
                                sqlCmdinsertsupplierPrice.ExecuteNonQuery();
                                Common.LogInsertStatus(logID,
                                        $"Part Supplier Detail  with part prefix '{part.Parts_PartPrefix}' and part number '{part.Parts_PartsNumber}' successfully inserted.",
                                        true, constring, apiCategory);
                                string emailSubject2 = $"Part Supplier Detail with prefix {part.Parts_PartPrefix} and number {part.Parts_PartsNumber} successfully inserted.";

                                string emailTemplate2 = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, true, emailSubject2);

                                Common.InsertEmailLog(conn, emailSubject2, emailTemplate2, Recipient, ccRecipient, tran);

                            }
                            else
                            {
                                decimal StandardPackingQuantity = part.StandardPackageQuantity == null ? 1 : part.StandardPackageQuantity;
                                strSql = "update GNM_PartsSupplierDetail set StandardPackingQuantity=" + StandardPackingQuantity + " where Parts_ID=" + Part_ID + " and Supplier_ID=" + Supplier_ID + " ";
                                sqlCmd = new SqlCommand();
                                sqlCmd.CommandType = CommandType.Text;
                                sqlCmd.Transaction = tran;
                                sqlCmd.CommandText = strSql;
                                sqlCmd.CommandTimeout = 0;
                                sqlCmd.Connection = conn;
                                sqlCmd.ExecuteNonQuery();
                                Common.LogInsertStatus(logID,
                                       $"Part Supplier Detail with part prefix '{part.Parts_PartPrefix}' and part number '{part.Parts_PartsNumber}' successfully updated.",
                                       true, constring, apiCategory);

                                string emailSubject2 = $"Part Supplier Detail with prefix {part.Parts_PartPrefix} and number {part.Parts_PartsNumber} successfully updated.";

                                string emailTemplate2 = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, true, emailSubject2);

                                Common.InsertEmailLog(conn, emailSubject2, emailTemplate2, Recipient, ccRecipient, tran);

                            }



                            string queryReorderDetails = "SELECT ReOrderLevel, ReOrderLevelQuantity, MinOrderQty " +
                                  "FROM GNM_PartsStockDetail " +
                                  "WHERE Parts_ID = @PartID";

                            SqlCommand sqlCommand = new SqlCommand(queryReorderDetails, conn);
                            sqlCommand.CommandType = CommandType.Text;
                            sqlCommand.Transaction = tran;
                            sqlCommand.CommandTimeout = 0;

                            // Add parameter to prevent SQL injection
                            sqlCommand.Parameters.AddWithValue("@PartID", Part_ID);

                            SqlDataAdapter dataAdapter = new SqlDataAdapter(sqlCommand);
                            DataTable dtReorderDetails = new DataTable();
                            dataAdapter.Fill(dtReorderDetails);
                            if (dtReorderDetails.Rows.Count > 0)
                            {
                                string updateQuery = "UPDATE GNM_PartsStockDetail " +
                                   "SET ReOrderLevel = @ReOrderLevel, " +
                                   "ReOrderLevelQuantity = @ReOrderLevelQuantity, " +
                                   "MinOrderQty = @MinOrderQty, " +
                                   "LastStockUpdatedDate =@LastStockUpdatedDate " +
                                   "WHERE Parts_ID = @PartID";

                                SqlCommand updateCommand = new SqlCommand(updateQuery, conn);
                                updateCommand.CommandType = CommandType.Text;
                                updateCommand.Transaction = tran;
                                updateCommand.Parameters.AddWithValue("@PartID", Part_ID);
                                updateCommand.Parameters.AddWithValue("@ReOrderLevel", part.ReorderLevel);
                                updateCommand.Parameters.AddWithValue("@ReOrderLevelQuantity", part.ReorderLevelQty);
                                updateCommand.Parameters.AddWithValue("@MinOrderQty", part.MinimumOrderQty);
                                updateCommand.Parameters.AddWithValue("@LastStockUpdatedDate", DateTime.UtcNow);

                                updateCommand.ExecuteNonQuery();
                                Common.LogInsertStatus(logID,
                                      $"Part Stock Detail  with part prefix '{part.Parts_PartPrefix}' and part number '{part.Parts_PartsNumber}' successfully inserted.",
                                      true, constring, apiCategory);
                                string emailSubject2 = $"Part Stock Detail with prefix {part.Parts_PartPrefix} and number {part.Parts_PartsNumber} successfully inserted.";

                                string emailTemplate2 = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, true, emailSubject2);

                                Common.InsertEmailLog(conn, emailSubject2, emailTemplate2, Recipient, ccRecipient, tran);
                            }
                            else
                            {
                                string insertQuery = @"
                                IF NOT EXISTS (
                                    SELECT 1 FROM GNM_PartsStockDetail 
                                    WHERE Parts_ID = @PartID AND BinLocation_ID = @BinLocationID
                                )
                                BEGIN
                                    INSERT INTO GNM_PartsStockDetail 
                                    (Parts_ID, Branch_ID, Company_ID, WareHouse_ID, 
                                    ReOrderLevel, ReOrderLevelQuantity, MinOrderQty, BinLocation_ID, BinlocationBuffer_ID, 
                                    LastStockUpdatedDate, FirstDemandDate, LastDemandDate, 
                                    FirstIssuedDate) 
                                    VALUES 
                                    (@PartID, @BranchID, @CompanyID, @WareHouseID, 
                                    @ReOrderLevel, @ReOrderLevelQuantity, @MinOrderQty, @BinLocationID, @BinlocationBufferID, 
                                    GETDATE(), NULL, NULL, NULL)
                                END";

                                SqlCommand insCommand = new SqlCommand(insertQuery, conn);
                                insCommand.CommandType = CommandType.Text;
                                insCommand.Transaction = tran;

                                // Adding parameters
                                insCommand.Parameters.AddWithValue("@PartID", Part_ID);
                                insCommand.Parameters.AddWithValue("@BranchID", Branch_ID);
                                insCommand.Parameters.AddWithValue("@CompanyID", companyID);
                                insCommand.Parameters.AddWithValue("@WareHouseID", WarehouseID);
                                insCommand.Parameters.AddWithValue("@ReOrderLevel", part.ReorderLevel);
                                insCommand.Parameters.AddWithValue("@ReOrderLevelQuantity", part.ReorderLevelQty);
                                insCommand.Parameters.AddWithValue("@MinOrderQty", part.MinimumOrderQty);
                                insCommand.Parameters.AddWithValue("@BinLocationID", Convert.ToInt32(BinLocation_ID));
                                insCommand.Parameters.AddWithValue("@BinlocationBufferID", Convert.ToInt32(BinLocation_ID));

                                // Execute Insert
                                sqlCommand.ExecuteNonQuery();
                                Common.LogInsertStatus(logID,
                                      $"Part Stock Detail  with part prefix '{part.Parts_PartPrefix}' and part number '{part.Parts_PartsNumber}' successfully updated.",
                                      true, constring, apiCategory);
                                string emailSubject2 = $"Part Stock Detail with prefix {part.Parts_PartPrefix} and number {part.Parts_PartsNumber} successfully updated.";

                                string emailTemplate2 = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, true, emailSubject2);

                                Common.InsertEmailLog(conn, emailSubject2, emailTemplate2, Recipient, ccRecipient, tran);
                            }
                        }
                        else
                        {
                            string xmlInput = $@"
                            <Parts>
                                <Part>
                                    <PartName>{(string.IsNullOrEmpty(part.Parts_PartsNumber) ? (object)DBNull.Value : Uri.UnescapeDataString(part.Parts_PartsNumber))}</PartName>
                                    <UOM>{(unitOfMeasurementID != 0 ? Convert.ToInt32(unitOfMeasurementID).ToString() : DBNull.Value.ToString())}</UOM>
                                    <PartsCategory>{(partsCategoryID != 0 ? Convert.ToInt32(partsCategoryID).ToString() : DBNull.Value.ToString())}</PartsCategory>
                                    <PartsFunctionGroup>{(functionGroupID != 0 ? Convert.ToInt32(functionGroupID).ToString() : DBNull.Value.ToString())}</PartsFunctionGroup>
                                    <chkIsActive>{(part.Parts_IsActive ? "1" : "0")}</chkIsActive>
                                    <chkIsComponent>{(part.Parts_IsComponent ? "1" : "0")}</chkIsComponent>
                                    <chkIsHazardous>{(part.Parts_IsHazardousGood ? "1" : "0")}</chkIsHazardous>
                                    <PartsDescription>{(string.IsNullOrEmpty(part.Parts_PartsDescription) ? (object)DBNull.Value : Uri.UnescapeDataString(part.Parts_PartsDescription))}</PartsDescription>
                                    <IsLocal>{(part.Parts_IsLocal ? "1" : "0")}</IsLocal>
                                    <PartPrefix>{(string.IsNullOrEmpty(part.Parts_PartPrefix) || part.Parts_PartPrefix == "undefined" ? (object)DBNull.Value : part.Parts_PartPrefix)}</PartPrefix>
                                    <AliasPartPrefix>{(string.IsNullOrEmpty(part.Parts_AliasPartPrefix) || part.Parts_AliasPartPrefix == "undefined" ? (object)DBNull.Value : part.Parts_AliasPartPrefix)}</AliasPartPrefix>
                                    <AliasPartName>{(string.IsNullOrEmpty(part.Parts_AliasPartNumber) || part.Parts_AliasPartNumber == "undefined" ? (object)DBNull.Value : part.Parts_AliasPartNumber)}</AliasPartName>
                                    <MovementType>{(movementTypeID != 0 ? movementTypeID.ToString() : DBNull.Value.ToString())}</MovementType>
                                    <Weight>{(part.Parts_Weight != 0 ? part.Parts_Weight.ToString("F2") : DBNull.Value.ToString())}</Weight>
                                    <Dimension>{(string.IsNullOrEmpty(part.Parts_Dimensions) ? (object)DBNull.Value : part.Parts_Dimensions)}</Dimension>
                                    <CustomsCode>{(string.IsNullOrEmpty(null) ? (object)DBNull.Value : null)}</CustomsCode>
                                    <ExciseDuty>{(exciseDutyID != null && exciseDutyID != 0 ? exciseDutyID.ToString() : DBNull.Value.ToString())}</ExciseDuty>
                                    <Company_ID>{(companyID != 0 ? companyID.ToString() : DBNull.Value.ToString())}</Company_ID>
                                    <User_ID>{userID}</User_ID>
                                    <ModifiedDate>{DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss")}</ModifiedDate>
                                    <SalvagePart_ID>{(salvagePartID != 0 ? salvagePartID.ToString() : DBNull.Value.ToString())}</SalvagePart_ID>
                                    <PartType>{(partTypeID != 0 ? partTypeID.ToString() : DBNull.Value.ToString())}</PartType>
                                    <PartsDisposal_ID>{(partsDisposalID != 0 ? partsDisposalID.ToString() : DBNull.Value.ToString())}</PartsDisposal_ID>
                                    <IsKitPart>{(part.IsKitPart ? "1" : "0")}</IsKitPart>
                                    <BlockForSales>{(part.BlockforSales ? "1" : "0")}</BlockForSales>
                                    <BlockForPurchase>{(part.IsPurchasable ? "1" : "0")}</BlockForPurchase>
                                </Part>
                            </Parts>";
                            SqlCommand cmd = new SqlCommand("UP_INS_AMERP_InsertGNMPart1", conn, tran);
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@PartsXML", xmlInput);
                            NParts_ID = Convert.ToInt32(cmd.ExecuteScalar());
                            if (NParts_ID == 0)
                            {
                                Common.LogInsertStatus(logID,
                                 "Error in insertion of part with prefix " + part.Parts_PartPrefix + ", Number " + part.Parts_PartsNumber + ". Row insertion failed.",
                                 false, constring, apiCategory);
                                somePartsNotInserted = true;
                                string emailSubject2 = $"Error in insertion of part with prefix {part.Parts_PartPrefix} and number {part.Parts_PartsNumber}. Row insertion failed.";
                                string emailBody2 = $"We encountered an error while attempting to insert the part with the following details into the system:\n\n" +
                                                   $"Prefix: {part.Parts_PartPrefix}\n" +
                                                   $"Part Number: {part.Parts_PartsNumber}\n\n" +
                                                   $"Unfortunately, the row insertion failed due to an issue. Please check the provided details or contact support for further assistance.\n\n" +
                                                   $"If you have any further questions or need assistance, feel free to contact us.\n\n";
                                string emailTemplate = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, false, emailBody2);
                                Common.InsertEmailLog(conn, emailSubject2, emailTemplate, Recipient, ccRecipient, tran);

                                tran.Rollback();
                                continue;
                            }
                            else
                            {
                                Common.LogInsertStatus(logID,
                                       $"Part with part prefix '{part.Parts_PartPrefix}' and part number '{part.Parts_PartsNumber}' successfully inserted.",
                                       true, constring, apiCategory);

                                string emailSubject = $"Part with prefix {part.Parts_PartPrefix} and number {part.Parts_PartsNumber} successfully inserted.";

                                string emailTemplate2 = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, true, emailSubject);

                                Common.InsertEmailLog(conn, emailSubject, emailTemplate2, Recipient, ccRecipient, tran);
                                isUpdated = false;
                                decimal StandardPackingQuantity = part.StandardPackageQuantity == null ? 1 : part.StandardPackageQuantity;
                                string insertsupplierPrice = "INSERT INTO GNM_PartsSupplierDetail VALUES(" + NParts_ID + "," + Supplier_ID + "," + StandardPackingQuantity + ",0.00,'" + part.SAPEffectiveDate + "',NULL," + CurrencyID + ",NULL,''," + companyID + ",'',NULL,0,0.00,NULL,NULL,NULL,NULL)";

                                SqlCommand sqlCmdinsertsupplierPrice = new SqlCommand();
                                sqlCmdinsertsupplierPrice.CommandType = CommandType.Text;
                                sqlCmdinsertsupplierPrice.Transaction = tran;
                                sqlCmdinsertsupplierPrice.CommandText = insertsupplierPrice;
                                sqlCmdinsertsupplierPrice.CommandTimeout = 0;
                                sqlCmdinsertsupplierPrice.Connection = conn;
                                sqlCmdinsertsupplierPrice.ExecuteNonQuery();
                                Common.LogInsertStatus(logID,
                                       $"Part Supplier Detail with part prefix '{part.Parts_PartPrefix}' and part number '{part.Parts_PartsNumber}' successfully inserted.",
                                       true, constring, apiCategory);

                                string emailSubject2 = $"Part Supplier Detail with prefix {part.Parts_PartPrefix} and number {part.Parts_PartsNumber} successfully inserted.";


                                string emailTemplate = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, true, emailSubject2);
                                Common.InsertEmailLog(conn, emailSubject2, emailTemplate, Recipient, ccRecipient, tran);
                            }

                            string queryReorderDetails = "SELECT ReOrderLevel, ReOrderLevelQuantity, MinOrderQty " +
                                  "FROM GNM_PartsStockDetail " +
                                  "WHERE Parts_ID = @PartID";

                            SqlCommand sqlCommand = new SqlCommand(queryReorderDetails, conn);
                            sqlCommand.CommandType = CommandType.Text;
                            sqlCommand.Transaction = tran;
                            sqlCommand.CommandTimeout = 0;

                            // Add parameter to prevent SQL injection
                            sqlCommand.Parameters.AddWithValue("@PartID", NParts_ID);

                            SqlDataAdapter dataAdapter = new SqlDataAdapter(sqlCommand);
                            DataTable dtReorderDetails = new DataTable();
                            dataAdapter.Fill(dtReorderDetails);

                            if (dtReorderDetails.Rows.Count > 0)
                            {
                                string updateQuery = "UPDATE GNM_PartsStockDetail " +
                                   "SET ReOrderLevel = @ReOrderLevel, " +
                                   "ReOrderLevelQuantity = @ReOrderLevelQuantity, " +
                                   "MinOrderQty = @MinOrderQty ," +
                                   "LastStockUpdatedDate =@LastStockUpdatedDate " +
                                   "WHERE Parts_ID = @PartID";

                                SqlCommand updateCommand = new SqlCommand(updateQuery, conn);
                                updateCommand.CommandType = CommandType.Text;
                                updateCommand.Transaction = tran;
                                updateCommand.Parameters.AddWithValue("@PartID", NParts_ID);
                                updateCommand.Parameters.AddWithValue("@ReOrderLevel", part.ReorderLevel);
                                updateCommand.Parameters.AddWithValue("@ReOrderLevelQuantity", part.ReorderLevelQty);
                                updateCommand.Parameters.AddWithValue("@MinOrderQty", part.MinimumOrderQty);
                                updateCommand.Parameters.AddWithValue("@LastStockUpdatedDate", DateTime.UtcNow);

                                updateCommand.ExecuteNonQuery();
                                Common.LogInsertStatus(logID,
                                      $"Part Stock Detail  with part prefix '{part.Parts_PartPrefix}' and part number '{part.Parts_PartsNumber}' successfully inserted.",
                                      true, constring, apiCategory);
                                string emailSubject2 = $"Part Stock Detail with prefix {part.Parts_PartPrefix} and number {part.Parts_PartsNumber} successfully inserted.";

                                string emailTemplate2 = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, true, emailSubject2);

                                Common.InsertEmailLog(conn, emailSubject2, emailTemplate2, Recipient, ccRecipient, tran);
                            }
                            else
                            {
                                string insertQuery = @"
                                IF NOT EXISTS (
                                    SELECT 1 FROM GNM_PartsStockDetail 
                                    WHERE Parts_ID = @PartID AND BinLocation_ID = @BinLocationID
                                )
                                BEGIN
                                    INSERT INTO GNM_PartsStockDetail 
                                    (Parts_ID, Branch_ID, Company_ID, WareHouse_ID, 
                                    ReOrderLevel, ReOrderLevelQuantity, MinOrderQty, BinLocation_ID, BinlocationBuffer_ID, 
                                    LastStockUpdatedDate, FirstDemandDate, LastDemandDate, 
                                    FirstIssuedDate) 
                                    VALUES 
                                    (@PartID, @BranchID, @CompanyID, @WareHouseID, 
                                    @ReOrderLevel, @ReOrderLevelQuantity, @MinOrderQty, @BinLocationID, @BinlocationBufferID, 
                                    GETDATE(), NULL, NULL, NULL)
                                END";

                                SqlCommand insCommand = new SqlCommand(insertQuery, conn);
                                insCommand.CommandType = CommandType.Text;
                                insCommand.Transaction = tran;
                                // Adding parameters
                                insCommand.Parameters.AddWithValue("@PartID", NParts_ID);
                                insCommand.Parameters.AddWithValue("@BranchID", Branch_ID);
                                insCommand.Parameters.AddWithValue("@CompanyID", companyID);
                                insCommand.Parameters.AddWithValue("@WareHouseID", WarehouseID);
                                insCommand.Parameters.AddWithValue("@ReOrderLevel", part.ReorderLevel);
                                insCommand.Parameters.AddWithValue("@ReOrderLevelQuantity", part.ReorderLevelQty);
                                insCommand.Parameters.AddWithValue("@MinOrderQty", part.MinimumOrderQty);
                                insCommand.Parameters.AddWithValue("@BinLocationID", Convert.ToInt32(BinLocation_ID));
                                insCommand.Parameters.AddWithValue("@BinlocationBufferID", Convert.ToInt32(BinLocation_ID));

                                // Execute Insert
                                insCommand.ExecuteNonQuery();
                                Common.LogInsertStatus(logID,
                                      $"Part Stock Detail  with part prefix '{part.Parts_PartPrefix}' and part number '{part.Parts_PartsNumber}' successfully updated.",
                                      true, constring, apiCategory);
                                string emailSubject2 = $"Part Stock Detail with prefix {part.Parts_PartPrefix} and number {part.Parts_PartsNumber} successfully updated.";

                                string emailTemplate2 = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, true, emailSubject2);

                                Common.InsertEmailLog(conn, emailSubject2, emailTemplate2, Recipient, ccRecipient, tran);
                            }

                        }

                        tran.Commit();

                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                        somePartsNotInserted = true;
                        tran.Rollback();
                        string errorMessage = $"Error in insertion of part with prefix {part.Parts_PartPrefix}, Number {part.Parts_PartsNumber}. " +
                          $"Exception: {ex.Message}. StackTrace: {ex.StackTrace}";
                        Common.LogInsertStatus(logID,
                                    errorMessage,
                                    false, constring, apiCategory);
                        string emailSubject2 = $"Error in insertion of part with prefix {part.Parts_PartPrefix} and number {part.Parts_PartsNumber}. Row insertion failed.";
                        string emailBody2 = $"We encountered an error while attempting to insert the part with the following details into the system:\n\n" +
                       $"Prefix: {part.Parts_PartPrefix}\n" +
                       $"Part Number: {part.Parts_PartsNumber}\n\n" +
                       $"Error Message: {ex.Message}\n" +
                       $"Stack Trace: {ex.StackTrace}\n\n" +
                       $"Unfortunately, the row insertion failed due to an issue. Please check the provided details or contact support for further assistance.\n\n" +
                       $"If you have any further questions or need assistance, feel free to contact us.\n\n";

                        string emailTemplate = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, false, emailBody2);
                        Common.InsertEmailLog(conn, emailSubject2, emailTemplate, Recipient, ccRecipient, tran);
                        x = new
                        {
                            Message = "Error in Insertion!"
                        };
                    }

                }
                if (Obj.PartsDetails.Count == 0)
                {
                    invalidJson = true;
                    string errorMessage = $"The provided JSON is invalid or contains no data. The insertion operation has failed.";
                    Common.LogInsertStatus(logID,
                        errorMessage,
                        false, constring, apiCategory);

                    string emailSubject2 = $"Error: Insertion failed due to invalid or empty JSON.";
                    string emailBody2 = $"We encountered an issue while attempting to process the provided JSON. The data appears to be either invalid or empty.\n\n" +
                                        $"Unfortunately, the row insertion failed because no valid data was found in the provided JSON. Please verify the data and try again.\n\n" +
                                        $"If you have any further questions or need assistance, feel free to contact us.\n\n";
                    string emailTemplate = Common.GetEmailTemplate(partHeader, "", "", "Exception", fullUrl, false, emailBody2);
                    Common.InsertEmailLogInvalidJson(conn, emailSubject2, emailTemplate, Recipient, ccRecipient);
                }

            }

            using (SqlConnection conn = new SqlConnection(constring))
            {
                try
                {
                    conn.Open();
                    string updateQuery = @"
                    UPDATE SAPDMS_API_Request_Log
                    SET Status = @Status, ResponseMessage = @ResponseMessage
                    WHERE LogID = @LogID";

                    using (SqlCommand cmd = new SqlCommand(updateQuery, conn))
                    {
                        if (invalidJson)
                        {
                            cmd.Parameters.AddWithValue("@Status", "Error");
                            cmd.Parameters.AddWithValue("@ResponseMessage", "JSON IS INVALID .");
                            x = new
                            {
                                Message = "JSON IS INVALID."
                            };
                        }
                        else if (somePartsNotInserted)
                        {
                            cmd.Parameters.AddWithValue("@Status", "Error");
                            cmd.Parameters.AddWithValue("@ResponseMessage", "SOME PARTS ARE INVALID .");
                            x = new
                            {
                                Message = "SOME PARTS DETAILS ARE INVALID ."
                            };
                        }
                        else
                        {
                            cmd.Parameters.AddWithValue("@Status", "Success");
                            cmd.Parameters.AddWithValue("@ResponseMessage", "Inserted Successfully!");
                            if (isUpdated)
                            {
                                x = new
                                {
                                    Message = "Updated Successfully!"
                                };
                            }
                            else
                            {
                                x = new
                                {
                                    Message = "Inserted Successfully!"
                                };
                            }

                        }

                        cmd.Parameters.AddWithValue("@LogID", logID);
                        cmd.ExecuteNonQuery();
                    }
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(x);
        }
        #endregion
     
        #region::: PARTS PRICE:::
        /// <summary>
        /// Insert Parts Price
        /// // Modify by DK - 23-March-2025
        /// </summary>
        /// <param name="Constring"></param>
        /// <param name="InsertPartsPriceObj"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult InsertPartsMasterPriceAndSupplierDetails(string Constring, PartPriceDetailList InsertPartsPriceObj, int LogException, string clientIP, string fullUrl, string Recipient, string ccRecipient)
        {
            string partHeader = "Parts Price Master";
            string apiEndpoint = fullUrl;
            string requestBody = JsonConvert.SerializeObject(InsertPartsPriceObj);
            string apiCategory = "PartsPriceAndSupplier";
            int companyID = 0;
            int partID = 0;
            int currencyID = 0;
            int supplierID = 0;
            decimal listPrice = 0;
            decimal SupplierListPrice = 0;
            decimal MRP = 0;
            DateTime EffectiveFrom;
            DateTime SuppEffectiveFrom;
            int? CustomerWarranty = null;
            var Details = default(dynamic);
            bool Part_IsActive = false;
            int logID = 0;
            bool somePartsNotInserted = false;
            bool PartorSupplierinserted = false;
            bool invalidJson = false;
            bool isUpdated = false;bool PriceDatanotset = false;
            logID = Common.LogRequest(apiEndpoint, requestBody, clientIP, apiCategory, Constring, LogException);
            ErrorMessage error = new ErrorMessage();

            using (SqlConnection conn = new SqlConnection(Constring))
            {
                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                {
                    conn.Open();
                }

                try
                {
                    foreach (var part in InsertPartsPriceObj.PartPriceDetails)
                    {
                        using (var transaction = conn.BeginTransaction())
                        {
                            try
                            {

                                var partDetail = part;
                                JObject row = JObject.FromObject(new
                                {
                                    Parts_PartPrefix = partDetail.Parts_PartPrefix,
                                    Parts_PartsNumber = partDetail.Parts_PartsNumber,
                                    PartsPriceDetail_ListPrice = partDetail.PartsPriceDetail_ListPrice,
                                    PartsPriceDetail_MRP = partDetail.PartsPriceDetail_MRP,
                                    PartsPriceDetail_EffectiveFrom = partDetail.PartsPriceDetail_EffectiveFrom,
                                    BRANCH_SHORTNAME = partDetail.BRANCH_SHORTNAME,
                                    CURRENCY_SHORTNAME = partDetail.CURRENCY_SHORTNAME,
                                    CustomerWarranty = partDetail.CustomerWarranty,
                                    Party_Code = partDetail.Party_Code,
                                    StandardPackingQuantity = partDetail.StandardPackingQuantity,
                                    SupplierPartNumber = partDetail.SupplierPartNumber,
                                    SupplierPartPrefix = partDetail.SupplierPartPrefix,
                                    SupplierPrice = partDetail.SupplierPrice,
                                    CostPrice = partDetail.CostPrice,
                                    SupplierYTAPrice = partDetail.SupplierYTAPrice,
                                    SupplierYEAPrice = partDetail.SupplierYEAPrice,
                                    PartsSupplierDetail_EffectiveFrom = partDetail.PartsSupplierDetail_EffectiveFrom
                                });

                                var columns = new Dictionary<string, bool>
                                {
                                    { "Parts_PartPrefix", true },
                                    { "Parts_PartsNumber", true },
                                    { "PartsPriceDetail_ListPrice", true },
                                    { "PartsPriceDetail_MRP", true },
                                    { "PartsPriceDetail_EffectiveFrom", true },
                                     { "PartsSupplierDetail_EffectiveFrom", true },
                                    { "BRANCH_SHORTNAME", true },
                                    { "CURRENCY_SHORTNAME", true },
                                    { "CustomerWarranty", false },
                                    { "Party_Code", true },
                                    { "StandardPackingQuantity", false },
                                    { "SupplierPartNumber", false },
                                    { "SupplierPartPrefix", false },
                                    { "SupplierPrice", false },
                                    { "CostPrice", false },
                                    { "SupplierYTAPrice", true },
                                    { "SupplierYEAPrice", true }
                                };
                                var pricecolumns = new Dictionary<string, bool>
                                {
                                   
                                    { "PartsPriceDetail_ListPrice", true },
                                    { "PartsPriceDetail_MRP", true },
                                
                                    
                                    { "SupplierPrice", true },
                                    { "CostPrice", true },
                                    { "SupplierYTAPrice", true },
                                    { "SupplierYEAPrice", true }
                                };
                                List<string> invalidColumns;
                                bool isRowValid = Common.ValidateAndLog(row, columns, out invalidColumns);
                                 error = Common.ValidatePriceData(row, pricecolumns, out invalidColumns);
                                if (!isRowValid)
                                {
                                    string invalidColumnsMessage = string.Join(", ", invalidColumns);
                                    somePartsNotInserted = true;
                                    Common.LogInsertStatus(logID,
                                   $"Part with prefix {part.Parts_PartPrefix}, Number {part.Parts_PartsNumber} has null columns. {invalidColumnsMessage}",
                                   false, Constring, apiCategory);

                                   string emailSubject = $"Part with prefix {part.Parts_PartPrefix}, number {part.Parts_PartsNumber} insertion failed.";
                                   string emailBody = $"Prefix: {part.Parts_PartPrefix}\n" +
                                       $"Part Number: {part.Parts_PartsNumber}\n\n" +
                                       $"Missing columns: {invalidColumnsMessage}\n\n";

                                    string emailTemplate = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, false, emailBody);

                                    Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, transaction);

                                    continue;
                                }
                                if (!error.IsValid && isRowValid)
                                {
                                    PriceDatanotset = true;

                                    Common.LogInsertStatus(logID, error.Message, false, Constring, apiCategory);
                                    string emailSubject = $"Part with prefix {part.Parts_PartPrefix}, number {part.Parts_PartsNumber} insertion failed.";
                                    string emailBody = error.Message;

                                    string emailTemplate = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, false, emailBody);
                                    Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, transaction);
                                    transaction.Commit();
                                    continue;
                                }
                                listPrice = part.PartsPriceDetail_ListPrice;
                                MRP = part.PartsPriceDetail_MRP;
                                EffectiveFrom = part.PartsPriceDetail_EffectiveFrom;
                                SuppEffectiveFrom = part.PartsSupplierDetail_EffectiveFrom;
                                CustomerWarranty = part.CustomerWarranty;
                                SupplierListPrice = part.SupplierPrice;
                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                using (SqlCommand cmd = new SqlCommand("UP_AMERP_PARTS_PRICE_AND_SUPPLIER_MASTER_INSERT_INITIALIZE", conn, transaction))
                                {
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.Parameters.AddWithValue("@Branch_ShortName", partDetail.BRANCH_SHORTNAME);
                                    cmd.Parameters.AddWithValue("@Part_Prefix", part.Parts_PartPrefix);
                                    cmd.Parameters.AddWithValue("@PartsNumber", part.Parts_PartsNumber);
                                    cmd.Parameters.AddWithValue("@RefMasterDetail_Short_Name", part.CURRENCY_SHORTNAME);
                                    cmd.Parameters.AddWithValue("@PartyCode", part.Party_Code);

                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            companyID = reader["Company_ID"] != DBNull.Value ? Convert.ToInt32(reader["Company_ID"]) : 0;
                                            partID = reader["Part_ID"] != DBNull.Value ? Convert.ToInt32(reader["Part_ID"]) : 0;
                                            currencyID = reader["Currency_ID"] != DBNull.Value ? Convert.ToInt32(reader["Currency_ID"]) : 0;
                                            supplierID = reader["Supplier_ID"] != DBNull.Value ? Convert.ToInt32(reader["Supplier_ID"]) : 0;
                                            Part_IsActive = Convert.ToBoolean(reader["Part_IsActive"]);                                           
                                        }
                                    }
                                }
                                JObject row2 = JObject.FromObject(new
                                {
                                    companyID = companyID == 0 ? (object)null : companyID,
                                    partID = partID == 0 ? (object)null : partID,
                                    currencyID = currencyID == 0 ? (object)null : currencyID,
                                    supplierID = supplierID == 0 ? (object)null : supplierID,
                                });
                                var col = new Dictionary<string, bool>
                        {

                            { "companyID", true },
                            { "partID", true },
                            { "currencyID", true },
                            { "supplierID", true },
                            { "Part_IsActive", false }

                        };
                                List<string> invalidColumns2;
                                isRowValid = Common.ValidateAndLog(row2, col, out invalidColumns2);
                                if (!isRowValid)
                                {
                                    string invalidColumnsMessage = string.Join(", ", invalidColumns2);
                                    somePartsNotInserted = true;
                                    Common.LogInsertStatus(logID,
                                     $"Part with prefix '{part.Parts_PartPrefix}', Number '{part.Parts_PartsNumber}' has invalid input columns: {invalidColumnsMessage}.",
                                     false, Constring, apiCategory);
                                    string emailSubject = $"Part with prefix {part.Parts_PartPrefix}, number {part.Parts_PartsNumber} insertion failed.";

                                    string emailBody = $"Prefix: {part.Parts_PartPrefix}\n" +
                                       $"Part Number: {part.Parts_PartsNumber}\n\n" +
                                       $"Invalid columns: {invalidColumnsMessage}\n\n";

                                    string emailTemplate = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, false, emailBody);
                                    Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, transaction);
                                    continue; // Skip to the next part

                                }
                                string strSqlpartsdetail = "SELECT TOP(1) PartsPriceDetail_ID, Parts_ID, PartsPriceDetail_EffectiveFrom,PartsPriceDetail_ListPrice,PartsPriceDetail_MRP FROM GNM_PartsPriceDetail WHERE Parts_ID=" + partID + " order by PartsPriceDetail_EffectiveFrom desc";
                                string strSqlsupplierdetail = "SELECT TOP(1) PartsSupplierDetail_ID,Parts_ID, Effectivefrom,SupplierPrice, SupplierYTAPrice ,SupplierYEAPrice FROM GNM_PartsSupplierDetail WHERE Parts_ID=" + partID + " order by Effectivefrom desc";
                                SqlCommand sqlCmdparts = new SqlCommand();
                                SqlCommand sqlCmdsupplier = new SqlCommand();
                                sqlCmdparts.Transaction = transaction;
                                sqlCmdsupplier.Transaction = transaction;
                                DataTable DtParteffectivedateparts = new DataTable();
                                DataTable DtParteffectivedatesupplier = new DataTable();
                                sqlCmdparts.CommandType = CommandType.Text;
                                sqlCmdsupplier.CommandType = CommandType.Text;
                                sqlCmdparts.CommandText = strSqlpartsdetail;
                                sqlCmdsupplier.CommandText = strSqlsupplierdetail;
                                sqlCmdparts.Connection = conn;
                                sqlCmdsupplier.Connection = conn;
                                sqlCmdparts.CommandTimeout = 0;
                                sqlCmdsupplier.CommandTimeout = 0;
                                SqlDataAdapter DaPartsprice = new SqlDataAdapter(sqlCmdparts);
                                DaPartsprice.Fill(DtParteffectivedateparts);

                                SqlDataAdapter Dasupplier = new SqlDataAdapter(sqlCmdsupplier);
                                Dasupplier.Fill(DtParteffectivedatesupplier);
                                if (DtParteffectivedateparts.Rows.Count > 0)
                                {
                                    string PartsPriceDetailID = DtParteffectivedateparts.Rows[0].ItemArray[0].ToString();
                                    string PartsID = DtParteffectivedateparts.Rows[0].ItemArray[1].ToString();
                                    DateTime PartsPriceEffectiveFromDate = Convert.ToDateTime(DtParteffectivedateparts.Rows[0].ItemArray[2]);
                                    string PartsListPrice = DtParteffectivedateparts.Rows[0].ItemArray[3].ToString();
                                    string PartsPriceMRP = DtParteffectivedateparts.Rows[0].ItemArray[4].ToString();
                                    if (EffectiveFrom == PartsPriceEffectiveFromDate.Date)
                                    {
                                        using (SqlCommand cmd = new SqlCommand("UP_UPD_AMERP_UpdatePartsPriceDetail", conn, transaction))
                                        {
                                            cmd.CommandType = CommandType.StoredProcedure;
                                            cmd.Parameters.AddWithValue("@PartsPriceDetail_ID", PartsPriceDetailID);
                                            cmd.Parameters.AddWithValue("@ListPrice", listPrice);
                                            cmd.Parameters.AddWithValue("@FCP", DBNull.Value);
                                            cmd.Parameters.AddWithValue("@MRP", MRP);
                                            cmd.Parameters.AddWithValue("@EffectiveFrom", EffectiveFrom);
                                            cmd.Parameters.AddWithValue("@CustomerWarranty", (object)CustomerWarranty ?? DBNull.Value);
                                            cmd.Parameters.AddWithValue("@Currency_ID", currencyID);

                                            cmd.ExecuteNonQuery();
                                        }
                                        // Log the successful insertion of part price with given part prefix and part number
                                        Common.LogInsertStatus(logID,
                                            $"Part price with part prefix '{part.Parts_PartPrefix}' and part number '{part.Parts_PartsNumber}' successfully updated.",
                                            true, Constring, apiCategory);


                                        string emailSubject = $"Part price with prefix {part.Parts_PartPrefix} and number {part.Parts_PartsNumber} successfully updated.";

                                        string emailTemplate = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, true, emailSubject);
                                        Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, transaction);
                                        isUpdated = true;

                                    }
                                    else if (EffectiveFrom > PartsPriceEffectiveFromDate.Date)
                                    {
                                        if (listPrice == Convert.ToDecimal(PartsListPrice) && MRP == Convert.ToDecimal(PartsPriceMRP))//No change in price detail because list price and MRP price are same
                                        {
                                            Common.LogInsertStatus(logID,
                                        $"No change in Price Details for Part with prefix '{part.Parts_PartPrefix}', Number '{part.Parts_PartsNumber}'.",
                                        false, Constring, apiCategory);
                                            somePartsNotInserted = true;
                                            PartorSupplierinserted = true;
                                            string emailSubject = $"No change in Price Details for Part with prefix {part.Parts_PartPrefix}, number {part.Parts_PartsNumber}.";
                                            string emailBody = $"There has been no change in the price details for the part with the following information:\n\n" +
                                                               $"Prefix: {part.Parts_PartPrefix}\n" +
                                                               $"Part Number: {part.Parts_PartsNumber}\n\n" +
                                                               $"If you have any further questions or need assistance, feel free to contact us.\n\n";
                                            string emailTemplate = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, false, emailBody);
                                            Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, transaction);
                                        }
                                        else
                                        {
                                            PartorSupplierinserted = false;
                                            using (SqlCommand cmd = new SqlCommand("UP_INS_AMERP_InsertPartsPriceDetail", conn, transaction))
                                            {
                                                cmd.CommandType = CommandType.StoredProcedure;
                                                cmd.Parameters.AddWithValue("@Parts_ID", partID);
                                                cmd.Parameters.AddWithValue("@ListPrice", listPrice);
                                                cmd.Parameters.AddWithValue("@FCP", null);
                                                cmd.Parameters.AddWithValue("@MRP", MRP);
                                                cmd.Parameters.AddWithValue("@EffectiveFrom", EffectiveFrom);
                                                cmd.Parameters.AddWithValue("@CustomerWarranty", (object)CustomerWarranty ?? DBNull.Value);
                                                cmd.Parameters.AddWithValue("@Company_ID", companyID);
                                                cmd.Parameters.AddWithValue("@Currency_ID", currencyID);

                                                cmd.ExecuteNonQuery();
                                            }
                                            Common.LogInsertStatus(logID,
                                           $"Part price with part prefix '{part.Parts_PartPrefix}' and part number '{part.Parts_PartsNumber}' successfully Inserted.",
                                           true, Constring, apiCategory);
                                            string emailSubject = $"Part price with prefix {part.Parts_PartPrefix} and number {part.Parts_PartsNumber} successfully inserted.";
                                            string emailTemplate = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, true, emailSubject);

                                            Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, transaction);
                                            isUpdated = false;
                                        }
                                    }
                                    else if (EffectiveFrom < PartsPriceEffectiveFromDate.Date)
                                    {
                                        Common.LogInsertStatus(logID,
                                        $"SAP  Date {EffectiveFrom} is less than DMS Parts Price Effective From Date {PartsPriceEffectiveFromDate.Date}.",
                                        false, Constring, apiCategory);
                                        somePartsNotInserted = true;
                                        PartorSupplierinserted = true;

                                        string emailSubject = $"SAP Date {EffectiveFrom} is less than DMS Parts Price Effective From Date {PartsPriceEffectiveFromDate.Date}.";
                                        string emailBody = $"The SAP Date for the part is earlier than the DMS Parts Price Effective From Date.\n\n" +
                                                           $"SAP Date: {EffectiveFrom}\n" +
                                                           $"DMS Parts Price Effective From Date: {PartsPriceEffectiveFromDate.Date}\n\n" +
                                                           $"Please verify the dates and ensure that the price effective date is correct. If the issue persists, feel free to contact us for assistance.\n\n";
                                        string emailTemplate = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, false, emailBody);
                                        Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, transaction);


                                    }

                                }
                                else
                                {
                                    PartorSupplierinserted = false;
                                    using (SqlCommand cmd = new SqlCommand("UP_INS_AMERP_InsertPartsPriceDetail", conn, transaction))
                                    {
                                        cmd.CommandType = CommandType.StoredProcedure;
                                        cmd.Parameters.AddWithValue("@Parts_ID", partID);
                                        cmd.Parameters.AddWithValue("@ListPrice", listPrice);
                                        cmd.Parameters.AddWithValue("@FCP", null);
                                        cmd.Parameters.AddWithValue("@MRP", MRP);
                                        cmd.Parameters.AddWithValue("@EffectiveFrom", EffectiveFrom);
                                        cmd.Parameters.AddWithValue("@CustomerWarranty", (object)CustomerWarranty ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@Company_ID", companyID);
                                        cmd.Parameters.AddWithValue("@Currency_ID", currencyID);

                                        cmd.ExecuteNonQuery();
                                    }
                                    Common.LogInsertStatus(logID,
                                           $"Part price with part prefix '{part.Parts_PartPrefix}' and part number '{part.Parts_PartsNumber}' successfully inserted.",
                                           true, Constring, apiCategory);
                                    string emailSubject = $"Part price with prefix {part.Parts_PartPrefix} and number {part.Parts_PartsNumber} successfully inserted.";
                                    string emailTemplate = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, true, emailSubject);

                                    Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, transaction);
                                    isUpdated = false;
                                }

                                if (DtParteffectivedatesupplier.Rows.Count > 0)
                                {
                                    string PartsSupplierPriceDetailID = DtParteffectivedatesupplier.Rows[0].ItemArray[0].ToString();
                                    string Parts_ID = DtParteffectivedatesupplier.Rows[0].ItemArray[1].ToString();
                                    DateTime SupplierPriceEffectiveFromDate = Convert.ToDateTime(DtParteffectivedatesupplier.Rows[0].ItemArray[2]);
                                    string PartsSupplierPrice = DtParteffectivedatesupplier.Rows[0].ItemArray[3].ToString();
                                    string PartsSupplierPriceYTA = DtParteffectivedatesupplier.Rows[0].ItemArray[4].ToString();
                                    string PartsSupplierPriceYEA = DtParteffectivedatesupplier.Rows[0].ItemArray[5].ToString();

                                    if (SuppEffectiveFrom == SupplierPriceEffectiveFromDate.Date)
                                    {


                                        using (SqlCommand command = new SqlCommand("UP_UPD_AMERP_UpdatePartsSupplierDetail", conn, transaction))
                                        {
                                            command.CommandType = CommandType.StoredProcedure;
                                            command.Parameters.AddWithValue("@PartsSupplierDetail_ID", PartsSupplierPriceDetailID);
                                            command.Parameters.AddWithValue("@Supplier_ID", supplierID);
                                            command.Parameters.AddWithValue("@SupplierPartPrefix", part.SupplierPartPrefix);
                                            command.Parameters.AddWithValue("@SupplierPartNumber", part.SupplierPartNumber);
                                            command.Parameters.AddWithValue("@Currency_ID", currencyID);
                                            command.Parameters.AddWithValue("@StandardPackingQuantity", Convert.ToDecimal(part.StandardPackingQuantity));

                                            command.Parameters.AddWithValue("@SupplierPrice", Convert.ToDecimal(part.SupplierPrice));
                                            command.Parameters.AddWithValue("@CostPrice", Convert.ToDecimal(part.CostPrice));
                                            command.Parameters.AddWithValue("@Effectivefrom", SuppEffectiveFrom);
                                            command.Parameters.AddWithValue("@IsWarrantyIntimation", DBNull.Value);
                                            command.Parameters.AddWithValue("@ManufacturerWarranty",0);
                                            command.Parameters.AddWithValue("@SupplierYTAPrice", Convert.ToDecimal(part.SupplierYTAPrice));
                                            command.Parameters.AddWithValue("@SupplierYEAPrice", Convert.ToDecimal(part.SupplierYEAPrice));


                                            command.ExecuteNonQuery();
                                        }
                                        Common.LogInsertStatus(logID,
                                         $"Part Supplier with part prefix '{part.Parts_PartPrefix}' and part number '{part.Parts_PartsNumber}' successfully updated.",
                                         true, Constring, apiCategory);
                                        string emailSubject = $"Part Supplier with prefix {part.Parts_PartPrefix} and number {part.Parts_PartsNumber} successfully updated.";
                                        string emailTemplate = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, true, emailSubject);

                                        Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, transaction);

                                    }
                                    else if (SuppEffectiveFrom > SupplierPriceEffectiveFromDate.Date)
                                    {
                                        if (SupplierListPrice == Convert.ToDecimal(PartsSupplierPrice) && part.SupplierYTAPrice == Convert.ToDecimal(PartsSupplierPriceYTA) && part.SupplierYEAPrice == Convert.ToDecimal(PartsSupplierPriceYEA))//No change in price detail because supplier price is same
                                        {
                                            Common.LogInsertStatus(logID,
                                       $"No change in Supplier Details for Part with prefix '{part.Parts_PartPrefix}', Number '{part.Parts_PartsNumber}'.",
                                       false, Constring, apiCategory);
                                            somePartsNotInserted = true;
                                            PartorSupplierinserted = true;
                                            string emailSubject = $"No change in Supplier Details for Part with prefix {part.Parts_PartPrefix}, number {part.Parts_PartsNumber}.";
                                            string emailBody = $"There has been no change in the supplier details for the part with the following information:\n\n" +
                                                               $"Prefix: {part.Parts_PartPrefix}\n" +
                                                               $"Part Number: {part.Parts_PartsNumber}\n\n" +
                                                               $"If you have any further questions or need assistance, feel free to contact us.\n\n";
                                            string emailTemplate = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, false, emailBody);
                                            Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, transaction);


                                        }
                                        else
                                        {
                                            PartorSupplierinserted = false;
                                            using (SqlCommand command = new SqlCommand("UP_INS_AMERP_InsertPartsSupplierDetail", conn, transaction))
                                            {
                                                command.CommandType = CommandType.StoredProcedure;
                                                command.Parameters.AddWithValue("@Parts_ID", partID);
                                                command.Parameters.AddWithValue("@Supplier_ID", supplierID);
                                                command.Parameters.AddWithValue("@StandardPackingQuantity", Convert.ToDecimal(part.StandardPackingQuantity));
                                                command.Parameters.AddWithValue("@SupplierPrice", Convert.ToDecimal(part.SupplierPrice));
                                                command.Parameters.AddWithValue("@Effectivefrom", SuppEffectiveFrom);
                                                command.Parameters.AddWithValue("@CostPrice", Convert.ToDecimal(part.CostPrice));
                                                command.Parameters.AddWithValue("@Currency_ID", currencyID);
                                                command.Parameters.AddWithValue("@LastInvoicedDate", null ?? (object)DBNull.Value);
                                                command.Parameters.AddWithValue("@SupplierPartNumber", part.SupplierPartNumber);
                                                command.Parameters.AddWithValue("@Company_ID", companyID);
                                                command.Parameters.AddWithValue("@SupplierPartPrefix", Common.DecryptString(part.SupplierPartPrefix));
                                                command.Parameters.AddWithValue("@ManufacturerWarranty", null ?? (object)0);
                                                command.Parameters.AddWithValue("@IsWarrantyIntimation", DBNull.Value);

                                                command.Parameters.AddWithValue("@SupplierYTAPrice", part.SupplierYTAPrice);
                                                command.Parameters.AddWithValue("@SupplierYEAPrice", part.SupplierYEAPrice);
                                                command.ExecuteNonQuery();
                                            }
                                            Common.LogInsertStatus(logID,
                                         $"Part Supplier with part prefix '{part.Parts_PartPrefix}' and part number '{part.Parts_PartsNumber}' successfully inserted.",
                                         true, Constring, apiCategory);
                                            string emailSubject = $"Part Supplier with prefix {part.Parts_PartPrefix} and number {part.Parts_PartsNumber} successfully inserted.";
                                            string emailTemplate = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, true, emailSubject);

                                            Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, transaction);
                                        }
                                    }
                                    else if (SuppEffectiveFrom < SupplierPriceEffectiveFromDate.Date)
                                    {
                                        Common.LogInsertStatus(logID,
                                      $"SAP  Date {SuppEffectiveFrom} is less than DMS  Parts Supplier Effective From Date {SupplierPriceEffectiveFromDate.Date}.",
                                      false, Constring, apiCategory);
                                        somePartsNotInserted = true;
                                        PartorSupplierinserted = true;
                                        string emailSubject = $"SAP Date {SuppEffectiveFrom} is less than DMS Parts Supplier Effective From Date {SupplierPriceEffectiveFromDate.Date}.";
                                        string emailBody = $"The SAP Date for the part is earlier than the DMS Parts Supplier Effective From Date.\n\n" +
                                                           $"SAP Date: {SuppEffectiveFrom}\n" +
                                                           $"DMS Parts Supplier Effective From Date: {SupplierPriceEffectiveFromDate.Date}\n\n" +
                                                           $"Please verify the dates and ensure that the supplier price effective date is correct. If the issue persists, feel free to contact us for assistance.\n\n";
                                        string emailTemplate = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, false, emailBody);
                                        Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, transaction);
                                    }

                                }
                                else
                                {

                                    PartorSupplierinserted = false;
                                    using (SqlCommand command = new SqlCommand("UP_INS_AMERP_InsertPartsSupplierDetail", conn, transaction))
                                    {
                                        command.CommandType = CommandType.StoredProcedure;
                                        command.Parameters.AddWithValue("@Parts_ID", partID);
                                        command.Parameters.AddWithValue("@Supplier_ID", supplierID);
                                        command.Parameters.AddWithValue("@StandardPackingQuantity", Convert.ToDecimal(part.StandardPackingQuantity));
                                        command.Parameters.AddWithValue("@SupplierPrice", Convert.ToDecimal(part.SupplierPrice));
                                        command.Parameters.AddWithValue("@Effectivefrom", SuppEffectiveFrom);
                                        command.Parameters.AddWithValue("@CostPrice", Convert.ToDecimal(part.CostPrice));
                                        command.Parameters.AddWithValue("@Currency_ID", currencyID);
                                        command.Parameters.AddWithValue("@LastInvoicedDate", null ?? (object)DBNull.Value);
                                        command.Parameters.AddWithValue("@SupplierPartNumber", part.SupplierPartNumber);
                                        command.Parameters.AddWithValue("@Company_ID", companyID);
                                        command.Parameters.AddWithValue("@SupplierPartPrefix", Common.DecryptString(part.SupplierPartPrefix));
                                        command.Parameters.AddWithValue("@ManufacturerWarranty", null ?? (object)0);
                                        command.Parameters.AddWithValue("@IsWarrantyIntimation", DBNull.Value);

                                        command.Parameters.AddWithValue("@SupplierYTAPrice", part.SupplierYTAPrice);
                                        command.Parameters.AddWithValue("@SupplierYEAPrice", part.SupplierYEAPrice);
                                        command.ExecuteNonQuery();
                                    }
                                    Common.LogInsertStatus(logID,
                                       $"Part Supplier with part prefix '{part.Parts_PartPrefix}' and part number '{part.Parts_PartsNumber}' successfully inserted.",
                                       true, Constring, apiCategory);
                                    string emailSubject = $"Part Supplier with prefix {part.Parts_PartPrefix} and number {part.Parts_PartsNumber} successfully inserted.";
                                    string emailTemplate = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Validation", fullUrl, true, emailSubject);

                                    Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, transaction);
                                }

                                transaction.Commit();
                            }
                            catch (Exception ex)
                            {
                                transaction.Rollback();
                                somePartsNotInserted = true;
                                string errorMessage = $"Error in insertion of part with prefix {part.Parts_PartPrefix}, Number {part.Parts_PartsNumber}. " +
                          $"Exception: {ex.Message}. StackTrace: {ex.StackTrace}";
                                Common.LogInsertStatus(logID,
                                    errorMessage,
                                    false, Constring, apiCategory);
                                string emailSubject2 = $"Error in insertion of part with prefix {part.Parts_PartPrefix} and number {part.Parts_PartsNumber}. Row insertion failed.";
                                string emailBody2 = $"We encountered an error while attempting to insert the part with the following details into the system:\n\n" +
                         $"Prefix: {part.Parts_PartPrefix}\n" +
                         $"Part Number: {part.Parts_PartsNumber}\n\n" +
                         $"Error Message: {ex.Message}\n" +
                         $"Stack Trace: {ex.StackTrace}\n\n" +
                         $"Unfortunately, the row insertion failed due to an issue. Please check the provided details or contact support for further assistance.\n\n" +
                         $"If you have any further questions or need assistance, feel free to contact us.\n\n";
                                string emailTemplate = Common.GetEmailTemplate(partHeader, part.Parts_PartPrefix, part.Parts_PartsNumber, "Exception", fullUrl, false, emailBody2);
                                Common.InsertEmailLog(conn, emailSubject2, emailTemplate, Recipient, ccRecipient, transaction);
                            }
                        }
                    }
                    if (InsertPartsPriceObj.PartPriceDetails.Count == 0)
                    {
                        invalidJson = true;
                        string errorMessage = $"The provided JSON is invalid or contains no data. The insertion operation has failed.";
                        Common.LogInsertStatus(logID,
                            errorMessage,
                            false, Constring, apiCategory);

                        string emailSubject2 = $"Error: Insertion failed due to invalid or empty JSON.";
                        string emailBody2 = $"We encountered an issue while attempting to process the provided JSON. The data appears to be either invalid or empty.\n\n" +
                                            $"Unfortunately, the row insertion failed because no valid data was found in the provided JSON. Please verify the data and try again.\n\n" +
                                            $"If you have any further questions or need assistance, feel free to contact us.\n\n";
                        string emailTemplate = Common.GetEmailTemplate(partHeader, "", "", "Exception", fullUrl, false, emailBody2);
                        Common.InsertEmailLogInvalidJson(conn, emailSubject2, emailTemplate, Recipient, ccRecipient);
                    }


                }
                catch (Exception ex)
                {

                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            using (SqlConnection conn = new SqlConnection(Constring))
            {
                try
                {
                    conn.Open();
                    string updateQuery = @"
                    UPDATE SAPDMS_API_Request_Log
                    SET Status = @Status, ResponseMessage = @ResponseMessage
                    WHERE LogID = @LogID";

                    using (SqlCommand cmd = new SqlCommand(updateQuery, conn))
                    {
                        if (invalidJson)
                        {
                            cmd.Parameters.AddWithValue("@Status", "Error");
                            cmd.Parameters.AddWithValue("@ResponseMessage", "JSON IS INVALID .");
                            Details = new
                            {
                                Message = "JSON IS INVALID."
                            };
                        }
                        else if (somePartsNotInserted && PartorSupplierinserted)
                        {
                            cmd.Parameters.AddWithValue("@Status", "Error");
                            cmd.Parameters.AddWithValue("@ResponseMessage", "SOME PARTS SUPPLIER AND/OR PRICE DETAILS ARE INVALID .");
                            Details = new
                            {
                                Message = "SOME PARTS SUPPLIER AND/OR PRICE DETAILS ARE INVALID."
                            };
                        }
                        else if (PriceDatanotset)
                        {
                            cmd.Parameters.AddWithValue("@Status", "Error");
                            cmd.Parameters.AddWithValue("@ResponseMessage", error.Message);
                            Details = new
                            {
                                Message = error.Message
                            };
                        }
                      
                        else
                        {
                            cmd.Parameters.AddWithValue("@Status", "Success");
                            cmd.Parameters.AddWithValue("@ResponseMessage", "Inserted Successfully!");
                            if (isUpdated)
                            {
                                Details = new
                                {
                                    Message = "Updated Successfully!"
                                };

                            }
                            else
                            {
                                Details = new
                                {
                                    Message = "Inserted Successfully!"
                                };
                            }
                        }

                        cmd.Parameters.AddWithValue("@LogID", logID);
                        cmd.ExecuteNonQuery();
                    }
                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Details);
        }

        #endregion
        #region::: DEALER MASTER:::
        /// <summary>
        /// VINAY
        /// Modified by DK
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <param name="clientIP"></param>
        /// <param name="fullUrl"></param>
        /// <param name="Recipient"></param>
        /// <param name="ccRecipient"></param>
        /// <returns></returns>
        public static IActionResult InsertDealerMaster(DealerMasterDetails Obj, string connString, int LogException, string clientIP, string fullUrl, string Recipient, string ccRecipient)
        {

            string companyThemeShortNme = "CH2";
            string user = "Admin";
            var detail = default(dynamic);
            string partHeader = "DealerMaster";
            string apiCategory = "DealerMaster";
            string apiEndpoint = fullUrl;
            int parentDealerID = 0;
            int compThemeID = 0;
            int userID = 0;
            int OrderingCost_ID = 0;
            int insertedCompanyId = 0;
            int LangID = 0;
            int extCompanyId = 0;
            bool somePartsNotInserted = false;
            string ErrorMsgContent = "";
            var messages = new List<object>();
            string requestBody = JsonConvert.SerializeObject(Obj);
            int logID = Common.LogRequest(apiEndpoint, requestBody, clientIP, apiCategory, connString, LogException);
            bool isUnique = true;
            using (SqlConnection conn = new SqlConnection(connString))
            {
                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                {
                    conn.Open();
                }
                SqlTransaction tran = conn.BeginTransaction();
                foreach (var obj in Obj.DealerDetails)
                {
                    try
                    {
                        // Proceed with the insertion if no duplicates were found
                        var headerColumns = new Dictionary<string, bool>
                          {
                              { "Parent_DealerShortname", false },
                              { "Remarks", false },
                              { "Dealer_ShortName", true },
                              { "Dealer_Name", true },
                              { "Dealer_ZipCode", false },
                              { "Country_Short_Name", true },
                              { "State_Name", true },
                              { "Dealer_Address", true },
                              { "Dealer_Location", false },
                              { "Dealer_Email", true },
                              { "Dealer_Mobile", false },
                              { "Currency_ShortName", true },
                              { "TimeZone_ShortName", true },
                              { "Region_ShortName", false },
                              { "BranchTaxCodeName", true },
                              { "BranchTaxCode", true },
                              { "Language_Code", false }
                          };

                        JObject Row = JObject.FromObject(new
                        {
                            TimeZone_ShortName = string.IsNullOrEmpty(obj.TimeZone_ShortName) ? null : obj.TimeZone_ShortName,
                            Parent_DealerShortname = string.IsNullOrEmpty(obj.Parent_DealerShortname) ? null : obj.Parent_DealerShortname,
                            Remarks = string.IsNullOrEmpty(obj.Remarks) ? null : obj.Remarks,
                            Dealer_ShortName = string.IsNullOrEmpty(obj.Dealer_ShortName) ? null : obj.Dealer_ShortName,
                            Dealer_Name = string.IsNullOrEmpty(obj.Dealer_Name) ? null : obj.Dealer_Name,
                            Dealer_ZipCode = string.IsNullOrEmpty(obj.Dealer_ZipCode) ? null : obj.Dealer_ZipCode,
                            State_Name = string.IsNullOrEmpty(obj.State_Name) ? null : obj.State_Name,
                            Country_Short_Name = string.IsNullOrEmpty(obj.Country_Short_Name) ? null : obj.Country_Short_Name,
                            Dealer_Address = string.IsNullOrEmpty(obj.Dealer_Address) ? null : obj.Dealer_Address,
                            Dealer_Location = string.IsNullOrEmpty(obj.Dealer_Location) ? null : obj.Dealer_Location,
                            Dealer_Email = string.IsNullOrEmpty(obj.Dealer_Email) ? null : obj.Dealer_Email,
                            Dealer_Mobile = string.IsNullOrEmpty(obj.Dealer_Mobile) ? null : obj.Dealer_Mobile,
                            Currency_ShortName = string.IsNullOrEmpty(obj.Currency_ShortName) ? null : obj.Currency_ShortName,                            
                            BranchTaxCodeName = string.IsNullOrEmpty(obj.BranchTaxCodeName) ? null : obj.BranchTaxCodeName,
                            BranchTaxCode = string.IsNullOrEmpty(obj.BranchTaxCode) ? null : obj.BranchTaxCode,
                            Language_Code = string.IsNullOrEmpty(obj.Language_Code) ? null : obj.Language_Code
                        });

                        List<string> invalidColumns;
                        bool isRowNull = Common.ValidateAndLog(Row, headerColumns, out invalidColumns);
                        if (isRowNull)
                        {
                            int countryId = 0;
                            int stateId = 0;
                            int timeZoneId = 0;
                            int regionId = 0;
                            int currencyId = 0;
                            int payrollFileTypeId = 0;

                            using (SqlCommand cmd = new SqlCommand("UP_AMERP_BRANCH_INSERT_INITIALIZE", conn, tran))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@Country_Short_Name", obj.Country_Short_Name);
                                cmd.Parameters.AddWithValue("@State_Name", obj.State_Name);
                                cmd.Parameters.AddWithValue("@TimeZone_ShortName", obj.TimeZone_ShortName);
                                cmd.Parameters.AddWithValue("@Region_ShortName", obj.Region_ShortName);
                                cmd.Parameters.AddWithValue("@Currency_ShortName", obj.Currency_ShortName);
                                cmd.Parameters.AddWithValue("@PayrollFileType_ShortName", DBNull.Value);
                                cmd.Parameters.AddWithValue("@ParentCompany_ShortName", obj.Parent_DealerShortname);
                                cmd.Parameters.AddWithValue("@CompanyTheme_ShortName", companyThemeShortNme);
                                cmd.Parameters.AddWithValue("@USER_NAME", user);
                                cmd.Parameters.AddWithValue("@OrderingCost", "SP");
                                cmd.Parameters.AddWithValue("@LangCode", obj.Language_Code);

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.HasRows)
                                    {
                                        while (reader.Read())
                                        {
                                            countryId = reader.IsDBNull(0) ? 0 : reader.GetInt32(0);
                                            stateId = reader.IsDBNull(1) ? 0 : reader.GetInt32(1);
                                            timeZoneId = reader.IsDBNull(2) ? 0 : reader.GetInt32(2);
                                            regionId = reader.IsDBNull(3) ? 0 : reader.GetInt32(3);
                                            currencyId = reader.IsDBNull(4) ? 0 : reader.GetInt32(4);
                                            payrollFileTypeId = reader.IsDBNull(5) ? 0 : reader.GetInt32(5);
                                            parentDealerID = reader.IsDBNull(6) ? 0 : reader.GetInt32(6);
                                            compThemeID = reader.IsDBNull(7) ? 0 : reader.GetInt32(7);
                                            userID = reader.IsDBNull(8) ? 0 : reader.GetInt32(8);
                                            OrderingCost_ID = reader.IsDBNull(9) ? 0 : reader.GetInt32(9);
                                            LangID = reader.IsDBNull(10) ? 0 : reader.GetInt32(10);                                            
                                        }
                                    }
                                }
                                var headerColumns2 = new Dictionary<string, bool>
                                      {
                                          { "countryId", true },
                                          { "stateId", true },
                                          { "regionId", true },
                                          { "currencyId", true },
                                          { "timeZoneId", true },
                                        {"LangID",true }

                                      };
                                JObject insertRow = JObject.FromObject(new
                                {
                                    countryId = countryId == 0 ? (int?)null : countryId,
                                    stateId = stateId == 0 ? (int?)null : stateId,
                                    regionId = regionId == 0 ? (int?)null : regionId,
                                    currencyId = currencyId == 0 ? (int?)null : currencyId,
                                    timeZoneId = timeZoneId == 0 ? (int?)null : timeZoneId,
                                    LangID = LangID == 0 ? (int?)null : LangID
                                });

                                List<string> invalidColumns2;
                                bool isRowValid = Common.ValidateAndLog(insertRow, headerColumns2, out invalidColumns2);
                                if (isRowValid)
                                {
                                    bool isDetRowValid = false;
                                    bool isContactPersonValid = true;
                                    foreach (var contactObj in obj.PartyContactPersonDetails)
                                    {
                                        var detColumns = new Dictionary<string, bool>
                                              {
                                                  { "PartyContactPerson_Name", false },
                                                  { "PartyContactPerson_Mobile", false }


                                              };
                                        JObject detRow = JObject.FromObject(new
                                        {
                                            PartyContactPerson_Name = string.IsNullOrEmpty(contactObj.PartyContactPerson_Name) ? null : contactObj.PartyContactPerson_Name,
                                            PartyContactPerson_Mobile = string.IsNullOrEmpty(contactObj.PartyContactPerson_Mobile) ? null : contactObj.PartyContactPerson_Mobile,


                                        });
                                        List<string> invalidDetColumns;
                                        isDetRowValid = Common.ValidateAndLog(detRow, detColumns, out invalidDetColumns);
                                        if (isDetRowValid == false)
                                        {
                                            string invalidColumnsMessage = string.Join(", ", invalidDetColumns);
                                            somePartsNotInserted = true;
                                            Common.LogInsertStatus(logID,
                                            $"The provided Dealer  with Party Contact Person Name: {contactObj.PartyContactPerson_Name}  has null columns: {invalidColumnsMessage}",
                                            false, connString, apiCategory);
                                            string emailSubject = $"The provided Dealer  with Party Contact Person Name: {contactObj.PartyContactPerson_Name}  Update failed";
                                            string emailBody = $"The provided Dealer  with Party Contact Person Name: {contactObj.PartyContactPerson_Name}  Update failed" +
                                            $"Missing columns: {invalidColumnsMessage}\n\n";

                                            string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, false, emailBody);
                                            Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                                            detail = new
                                            {
                                                Message = $"The provided Dealer  with Party Contact Person Name: {contactObj.PartyContactPerson_Name}  Update failed" +
                                            $"Missing columns: {invalidColumnsMessage}\n\n"
                                            };
                                            messages.Add(detail);
                                            isContactPersonValid = false;
                                            break;
                                        }

                                    }
                                    if (isContactPersonValid == true)
                                    {
                                        string checkDuplicateQuery = @"
                                                SELECT Company_ID 
                                                FROM GNM_Company 
                                                WHERE Company_ShortName = @Dealer_ShortName 
                                                AND Company_Name = @Dealer_Name;";

                                        using (SqlCommand cmdCheckDuplicate = new SqlCommand(checkDuplicateQuery, conn, tran))
                                        {
                                            cmdCheckDuplicate.Parameters.Add(new SqlParameter("@Dealer_ShortName", SqlDbType.NVarChar, 255) { Value = obj.Dealer_ShortName });
                                            cmdCheckDuplicate.Parameters.Add(new SqlParameter("@Dealer_Name", SqlDbType.NVarChar, 255) { Value = obj.Dealer_Name });

                                            object result = cmdCheckDuplicate.ExecuteScalar();

                                            if (result != null)
                                            {
                                                isUnique = false;
                                                extCompanyId = Convert.ToInt32(result); // Retrieve Company_ID
                                            }
                                            else
                                            {
                                                isUnique = true;

                                            }
                                        }
                                        int rowsAffected = 0;
                                        if (isUnique)
                                        {
                                            string insertCompanyQuery = @"
                                                    INSERT INTO GNM_Company 
                                                    (
                                                        Company_Name, Company_ShortName, Currency_ID, Company_Address, 
                                                        Company_Type, Company_Active, Company_LogoName, Company_Parent_ID, 
                                                        Remarks, DefaultGridSize, JobCardCushionHours, ModifiedBy, 
                                                        ModifiedDate, CompanyTheme_ID, InventoryCarryingFactoy_Percentage, 
                                                        OrderingCost, CompanyFont
                                                    ) 
                                                    VALUES 
                                                    (
                                                        @Company_Name, @Company_ShortName, @Currency_ID, @Company_Address, 
                                                        @Company_Type, @Company_Active, @Company_LogoName, @Company_Parent_ID, 
                                                        @Remarks, @DefaultGridSize, @JobCardCushionHours, @ModifiedBy, 
                                                        @ModifiedDate, @CompanyTheme_ID, @InventoryCarryingFactoy_Percentage, 
                                                        @OrderingCost, @CompanyFont
                                                    );
                                                    SELECT SCOPE_IDENTITY();";

                                            using (SqlCommand cmdInsert = new SqlCommand(insertCompanyQuery, conn, tran))
                                            {
                                                cmdInsert.CommandType = CommandType.Text;
                                                cmdInsert.Parameters.AddWithValue("@Company_Name", obj.Dealer_Name);
                                                cmdInsert.Parameters.AddWithValue("@Company_ShortName", obj.Dealer_ShortName);
                                                cmdInsert.Parameters.AddWithValue("@Currency_ID", Convert.ToInt32(currencyId));
                                                cmdInsert.Parameters.AddWithValue("@Company_Address", obj.Dealer_Address);
                                                cmdInsert.Parameters.AddWithValue("@Company_Type", "D");  // Corrected string value
                                                cmdInsert.Parameters.AddWithValue("@Company_Active", false);
                                                cmdInsert.Parameters.AddWithValue("@Company_LogoName", "YANMAR");
                                                cmdInsert.Parameters.AddWithValue("@Company_Parent_ID", parentDealerID);
                                                cmdInsert.Parameters.AddWithValue("@Remarks", obj.Remarks);
                                                cmdInsert.Parameters.AddWithValue("@DefaultGridSize", 30);
                                                cmdInsert.Parameters.AddWithValue("@JobCardCushionHours", Convert.ToDecimal("0.00"));  // Corrected decimal conversion
                                                cmdInsert.Parameters.AddWithValue("@ModifiedBy", userID);
                                                cmdInsert.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                                                cmdInsert.Parameters.AddWithValue("@CompanyTheme_ID", compThemeID);
                                                cmdInsert.Parameters.AddWithValue("@InventoryCarryingFactoy_Percentage", 1);
                                                cmdInsert.Parameters.AddWithValue("@OrderingCost", DBNull.Value);  // Ensure database column allows NULL
                                                cmdInsert.Parameters.AddWithValue("@CompanyFont", "Times New Roman");
                                                cmdInsert.Parameters.AddWithValue("@Company_ID", extCompanyId);  // The ID of the company to update
                                                extCompanyId = Convert.ToInt32(cmdInsert.ExecuteScalar());
                                            }
                                        }
                                        else
                                        {
                                            string updateCompanyQuery = @"
                                                UPDATE GNM_Company 
                                                SET 
                                                    Company_Name = @Company_Name,
                                                    Company_ShortName = @Company_ShortName,
                                                    Currency_ID = @Currency_ID,
                                                    Company_Address = @Company_Address,
                                                    Company_Type = @Company_Type,
                                                    Company_Active = @Company_Active,
                                                    Company_LogoName = @Company_LogoName,
                                                    Company_Parent_ID = @Company_Parent_ID,
                                                    Remarks = @Remarks,
                                                    DefaultGridSize = @DefaultGridSize,
                                                    JobCardCushionHours = @JobCardCushionHours,
                                                    ModifiedBy = @ModifiedBy,
                                                    ModifiedDate = @ModifiedDate,
                                                    CompanyTheme_ID = @CompanyTheme_ID,
                                                    InventoryCarryingFactoy_Percentage = @InventoryCarryingFactoy_Percentage,
                                                    OrderingCost = @OrderingCost,
                                                    CompanyFont = @CompanyFont
                                                WHERE 
                                                    Company_ID = @Company_ID;";

                                            using (SqlCommand cmdUpdate = new SqlCommand(updateCompanyQuery, conn, tran))
                                            {
                                                cmdUpdate.CommandType = CommandType.Text;
                                                cmdUpdate.Parameters.AddWithValue("@Company_Name", obj.Dealer_Name);
                                                cmdUpdate.Parameters.AddWithValue("@Company_ShortName", obj.Dealer_ShortName);
                                                cmdUpdate.Parameters.AddWithValue("@Currency_ID", Convert.ToInt32(currencyId));
                                                cmdUpdate.Parameters.AddWithValue("@Company_Address", obj.Dealer_Address);
                                                cmdUpdate.Parameters.AddWithValue("@Company_Type", 'D');
                                                cmdUpdate.Parameters.AddWithValue("@Company_Active", false);
                                                cmdUpdate.Parameters.AddWithValue("@Company_LogoName", "YANMAR");
                                                cmdUpdate.Parameters.AddWithValue("@Company_Parent_ID", parentDealerID);
                                                cmdUpdate.Parameters.AddWithValue("@Remarks", obj.Remarks);
                                                cmdUpdate.Parameters.AddWithValue("@DefaultGridSize", 30);
                                                cmdUpdate.Parameters.AddWithValue("@JobCardCushionHours", "0.00");
                                                cmdUpdate.Parameters.AddWithValue("@ModifiedBy", userID);
                                                cmdUpdate.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                                                cmdUpdate.Parameters.AddWithValue("@CompanyTheme_ID", compThemeID);
                                                cmdUpdate.Parameters.AddWithValue("@InventoryCarryingFactoy_Percentage", 1);
                                                cmdUpdate.Parameters.AddWithValue("@OrderingCost", DBNull.Value);
                                                cmdUpdate.Parameters.AddWithValue("@CompanyFont", "Times New Roman");
                                                cmdUpdate.Parameters.AddWithValue("@Company_ID", extCompanyId); // The ID of the company to update

                                                rowsAffected = cmdUpdate.ExecuteNonQuery();
                                            }
                                        }

                                        if (extCompanyId > 0)
                                        {
                                            int insertedBranchId = 0;
                                            string checkBranchQuery = @"
                                                SELECT Branch_ID FROM GNM_Branch 
                                                WHERE  Branch_Name = @Branch_Name AND Branch_ShortName=@Branch_ShortName";
                                            using (SqlCommand checkCmd = new SqlCommand(checkBranchQuery, conn, tran))
                                            {                                               
                                                checkCmd.Parameters.AddWithValue("@Branch_Name", obj.Dealer_Name ?? (object)DBNull.Value);
                                                checkCmd.Parameters.AddWithValue("@Branch_ShortName", obj.Dealer_ShortName ?? (object)DBNull.Value);
                                                var existingBranchId = checkCmd.ExecuteScalar();
                                                if (existingBranchId != null) // Record Exists, Update
                                                {
                                                    insertedBranchId = Convert.ToInt32(existingBranchId);
                                                    string updateBranchQuery = @"
                                                            UPDATE GNM_Branch 
                                                            SET 
                                                                Branch_ShortName = @Branch_ShortName,
                                                                Branch_ZipCode = @Branch_ZipCode,
                                                                Country_ID = @Country_ID,
                                                                State_ID = @State_ID,
                                                                Branch_Phone = @Branch_Phone,
                                                                Branch_Fax = @Branch_Fax,
                                                                Branch_HeadOffice = @Branch_HeadOffice,
                                                                Branch_Active = @Branch_Active,
                                                                Branch_Address = @Branch_Address,
                                                                Branch_Location = @Branch_Location,
                                                                Branch_Email = @Branch_Email,
                                                                Branch_External = @Branch_External,
                                                                Branch_Mobile = @Branch_Mobile,
                                                                TimeZoneID = @TimeZoneID,
                                                                Region_ID = @Region_ID,
                                                                Currency_ID = @Currency_ID,
                                                                IsOverTimeDWM = @IsOverTimeDWM,
                                                                PayrollFileType_ID = @PayrollFileType_ID
                                                            WHERE Branch_ID = @Branch_ID";
                                                    using (SqlCommand updateCmd = new SqlCommand(updateBranchQuery, conn, tran))
                                                    {
                                                        updateCmd.Parameters.AddWithValue("@Branch_ID", insertedBranchId);
                                                        updateCmd.Parameters.AddWithValue("@Branch_ShortName", obj.Dealer_ShortName ?? (object)DBNull.Value);
                                                        updateCmd.Parameters.AddWithValue("@Branch_ZipCode", obj.Dealer_ZipCode ?? (object)DBNull.Value);
                                                        updateCmd.Parameters.AddWithValue("@Country_ID", Convert.ToInt32(countryId));
                                                        updateCmd.Parameters.AddWithValue("@State_ID", Convert.ToInt32(stateId));
                                                        updateCmd.Parameters.AddWithValue("@Branch_Phone", obj.Dealer_Phone ?? (object)DBNull.Value);
                                                        updateCmd.Parameters.AddWithValue("@Branch_Fax", obj.Dealer_Fax ?? (object)DBNull.Value);
                                                        updateCmd.Parameters.AddWithValue("@Branch_HeadOffice", true);
                                                        updateCmd.Parameters.AddWithValue("@Branch_Active", false);
                                                        updateCmd.Parameters.AddWithValue("@Branch_Address", obj.Dealer_Address ?? (object)DBNull.Value);
                                                        updateCmd.Parameters.AddWithValue("@Branch_Location", obj.Dealer_Location ?? (object)DBNull.Value);
                                                        updateCmd.Parameters.AddWithValue("@Branch_Email", obj.Dealer_Email ?? (object)DBNull.Value);
                                                        updateCmd.Parameters.AddWithValue("@Branch_External", false);
                                                        updateCmd.Parameters.AddWithValue("@Branch_Mobile", obj.Dealer_Mobile ?? (object)DBNull.Value);
                                                        updateCmd.Parameters.AddWithValue("@TimeZoneID", Convert.ToInt32(timeZoneId));
                                                        updateCmd.Parameters.AddWithValue("@Region_ID", regionId);
                                                        updateCmd.Parameters.AddWithValue("@Currency_ID", currencyId);
                                                        updateCmd.Parameters.AddWithValue("@IsOverTimeDWM", false);
                                                        updateCmd.Parameters.AddWithValue("@PayrollFileType_ID", DBNull.Value);

                                                        updateCmd.ExecuteNonQuery();
                                                    }
                                                }
                                                else
                                                {
                                                    string insertBranchQuery = @"
                                                        INSERT INTO GNM_Branch 
                                                        (Company_ID, Branch_Name, Branch_ShortName, Branch_ZipCode, Country_ID, State_ID, 
                                                        Branch_Phone, Branch_Fax, Branch_HeadOffice, Branch_Active, Branch_Address, Branch_Location, 
                                                        Branch_Email, Branch_External, Branch_Mobile, TimeZoneID, Region_ID, Currency_ID, IsOverTimeDWM, PayrollFileType_ID) 
                                                        VALUES 
                                                        (@Company_ID, @Branch_Name, @Branch_ShortName, @Branch_ZipCode, @Country_ID, @State_ID, 
                                                        @Branch_Phone, @Branch_Fax, @Branch_HeadOffice, @Branch_Active, @Branch_Address, @Branch_Location, 
                                                        @Branch_Email, @Branch_External, @Branch_Mobile, @TimeZoneID, @Region_ID, @Currency_ID, @IsOverTimeDWM, @PayrollFileType_ID);
                                                        SELECT SCOPE_IDENTITY();";

                                                    using (SqlCommand insertCmd = new SqlCommand(insertBranchQuery, conn, tran))
                                                    {
                                                        insertCmd.Parameters.AddWithValue("@Company_ID", Convert.ToInt32(extCompanyId));
                                                        insertCmd.Parameters.AddWithValue("@Branch_Name", obj.Dealer_Name ?? (object)DBNull.Value);
                                                        insertCmd.Parameters.AddWithValue("@Branch_ShortName", obj.Dealer_ShortName ?? (object)DBNull.Value);
                                                        insertCmd.Parameters.AddWithValue("@Branch_ZipCode", obj.Dealer_ZipCode ?? (object)DBNull.Value);
                                                        insertCmd.Parameters.AddWithValue("@Country_ID", Convert.ToInt32(countryId));
                                                        insertCmd.Parameters.AddWithValue("@State_ID", Convert.ToInt32(stateId));
                                                        insertCmd.Parameters.AddWithValue("@Branch_Phone", obj.Dealer_Phone ?? (object)DBNull.Value);
                                                        insertCmd.Parameters.AddWithValue("@Branch_Fax", obj.Dealer_Fax ?? (object)DBNull.Value);
                                                        insertCmd.Parameters.AddWithValue("@Branch_HeadOffice", true);
                                                        insertCmd.Parameters.AddWithValue("@Branch_Active", false);
                                                        insertCmd.Parameters.AddWithValue("@Branch_Address", obj.Dealer_Address ?? (object)DBNull.Value);
                                                        insertCmd.Parameters.AddWithValue("@Branch_Location", obj.Dealer_Location ?? (object)DBNull.Value);
                                                        insertCmd.Parameters.AddWithValue("@Branch_Email", obj.Dealer_Email ?? (object)DBNull.Value);
                                                        insertCmd.Parameters.AddWithValue("@Branch_External", false);
                                                        insertCmd.Parameters.AddWithValue("@Branch_Mobile", obj.Dealer_Mobile ?? (object)DBNull.Value);
                                                        insertCmd.Parameters.AddWithValue("@TimeZoneID", Convert.ToInt32(timeZoneId));
                                                        insertCmd.Parameters.AddWithValue("@Region_ID", regionId);
                                                        insertCmd.Parameters.AddWithValue("@Currency_ID", currencyId);
                                                        insertCmd.Parameters.AddWithValue("@IsOverTimeDWM", false);
                                                        insertCmd.Parameters.AddWithValue("@PayrollFileType_ID", DBNull.Value);

                                                        insertedBranchId = Convert.ToInt32(insertCmd.ExecuteScalar());
                                                    }
                                                }

                                            }
                                            if (insertedBranchId > 0)
                                            {
                                                using (SqlCommand cmd3 = new SqlCommand(@"
                                                        IF EXISTS (SELECT 1 FROM GNM_BranchTaxCodes WHERE BranchTaxCode = @BranchTaxCode AND Branch_ID = @Branch_ID)
                                                        BEGIN
                                                            UPDATE GNM_BranchTaxCodes 
                                                            SET BranchTaxCodeName = @BranchTaxCodeName 
                                                            WHERE BranchTaxCode = @BranchTaxCode AND Branch_ID = @Branch_ID
                                                        END
                                                        ELSE
                                                        BEGIN
                                                            INSERT INTO GNM_BranchTaxCodes (BranchTaxCodeName, BranchTaxCode, Branch_ID)
                                                            VALUES (@BranchTaxCodeName, @BranchTaxCode, @Branch_ID);
                                                        END", conn, tran))
                                                {
                                                    cmd3.Parameters.AddWithValue("@BranchTaxCodeName", obj.BranchTaxCodeName ?? (object)DBNull.Value);
                                                    cmd3.Parameters.AddWithValue("@BranchTaxCode", obj.BranchTaxCode ?? (object)DBNull.Value);
                                                    cmd3.Parameters.AddWithValue("@Branch_ID", insertedBranchId);
                                                    cmd3.ExecuteNonQuery();
                                                }

                                                // Upsert for GNM_Party
                                                string upsertPartyQuery = @"
                                                    DECLARE @InsertedID INT;

                                                        IF EXISTS (SELECT 1 FROM GNM_Party WHERE Party_Code = @Party_Code AND Party_Name=@Party_Name)
                                                        BEGIN
                                                            UPDATE GNM_Party 
                                                            SET Party_IsActive = @Party_IsActive,
                                                                Party_IsLocked = @Party_IsLocked,
                                                                Party_Name = @Party_Name,
                                                                Party_Location = @Party_Location,
                                                                Party_Email = @Party_Email,
                                                                Party_Phone = @Party_Phone,
                                                                Party_Fax = @Party_Fax,
                                                                Party_PaymentTerms = @Party_PaymentTerms,
                                                                PartyType = @PartyType,
                                                                Party_Mobile = @Party_Mobile,
                                                                ModifiedBY = @ModifiedBY,
                                                                ModifiedDate = @ModifiedDate,
                                                                Country_ID = @Country_ID,
                                                                State_ID = @State_ID,
                                                                Company_ID = @Company_ID,
                                                                Relationship_Branch_ID = @Relationship_Branch_ID,
                                                                IsOEM = @IsOEM,
                                                                IsDealer = @IsDealer,
                                                                Relationship_Company_ID = @Relationship_Company_ID,
                                                                PartsCreditLimit = @PartsCreditLimit,
                                                                SalesCreditLimit = @SalesCreditLimit,
                                                                Currency_ID = @Currency_ID,
                                                                IsImportExport = @IsImportExport,
                                                                ServiceCreditLimit = @ServiceCreditLimit,
                                                                PaymentDueDays = @PaymentDueDays,
                                                                PartsOutStandingCredit = @PartsOutStandingCredit,
                                                                ServiceOutStandingCredit = @ServiceOutStandingCredit,
                                                                RemanOutStandingCredit = @RemanOutStandingCredit,
                                                                PartyAdvanceAmount = @PartyAdvanceAmount,
                                                                SalesOutStandingCredit = @SalesOutStandingCredit,
                                                                CustomerType_ID = @CustomerType_ID,
                                                                IsKeyCustomer = @IsKeyCustomer,
                                                                Region_ID = @Region_ID,
                                                                SupplierHasInterface = @SupplierHasInterface,
                                                                CustomerType = @CustomerType,
                                                                CustomerLanguageID = @CustomerLanguageID,
                                                                IsPONumberMandatory = @IsPONumberMandatory,
                                                                ServiceCreditLimitinUSD = @ServiceCreditLimitinUSD,
                                                                ServiceOutStandingCreditinUSD = @ServiceOutStandingCreditinUSD,
                                                                CAD_ExchangeRate = @CAD_ExchangeRate,
                                                                US_ExchangeRate = @US_ExchangeRate,
                                                                CreditExceededMailSent = @CreditExceededMailSent,
                                                                IsInternalCustomer = @IsInternalCustomer,
                                                                Variance_Percentage = @Variance_Percentage,
                                                                Variance_Value = @Variance_Value
                                                            WHERE Party_Code = @Party_Code AND Party_Name=@Party_Name;

                                                            -- Get the existing Party_ID
                                                            SELECT @InsertedID = Party_ID FROM GNM_Party WHERE Party_Code = @Party_Code AND Party_Name=@Party_Name;
                                                        END
                                                        ELSE
                                                        BEGIN
                                                            INSERT INTO GNM_Party 
                                                            (
                                                                Party_IsActive, Party_IsLocked, Party_Name, Party_Location, 
                                                                Party_Email, Party_Phone, Party_Fax, Party_PaymentTerms, 
                                                                PartyType, Party_Mobile, ModifiedBY, ModifiedDate, 
                                                                Country_ID, State_ID, Company_ID, Relationship_Branch_ID, 
                                                                IsOEM, IsDealer, Relationship_Company_ID, PartsCreditLimit, 
                                                                SalesCreditLimit, Currency_ID, IsImportExport, ServiceCreditLimit, 
                                                                PaymentDueDays, PartsOutStandingCredit, ServiceOutStandingCredit, 
                                                                RemanOutStandingCredit, PartyAdvanceAmount, SalesOutStandingCredit, 
                                                                CustomerType_ID, IsKeyCustomer, Region_ID, Party_Code, 
                                                                SupplierHasInterface, CustomerType, CustomerLanguageID, 
                                                                IsPONumberMandatory, ServiceCreditLimitinUSD, ServiceOutStandingCreditinUSD, 
                                                                CAD_ExchangeRate, US_ExchangeRate, CreditExceededMailSent, 
                                                                IsInternalCustomer, Variance_Percentage, Variance_Value
                                                            ) 
                                                            VALUES 
                                                            (
                                                                @Party_IsActive, @Party_IsLocked, @Party_Name, @Party_Location, 
                                                                @Party_Email, @Party_Phone, @Party_Fax, @Party_PaymentTerms, 
                                                                @PartyType, @Party_Mobile, @ModifiedBY, @ModifiedDate, 
                                                                @Country_ID, @State_ID, @Company_ID, @Relationship_Branch_ID, 
                                                                @IsOEM, @IsDealer, @Relationship_Company_ID, @PartsCreditLimit, 
                                                                @SalesCreditLimit, @Currency_ID, @IsImportExport, @ServiceCreditLimit, 
                                                                @PaymentDueDays, @PartsOutStandingCredit, @ServiceOutStandingCredit, 
                                                                @RemanOutStandingCredit, @PartyAdvanceAmount, @SalesOutStandingCredit, 
                                                                @CustomerType_ID, @IsKeyCustomer, @Region_ID, @Party_Code, 
                                                                @SupplierHasInterface, @CustomerType, @CustomerLanguageID, 
                                                                @IsPONumberMandatory, @ServiceCreditLimitinUSD, @ServiceOutStandingCreditinUSD, 
                                                                @CAD_ExchangeRate, @US_ExchangeRate, @CreditExceededMailSent, 
                                                                @IsInternalCustomer, @Variance_Percentage, @Variance_Value
                                                            );

                                                            -- Get the newly inserted Party_ID
                                                            SET @InsertedID = SCOPE_IDENTITY();
                                                        END

                                                        -- Return the Party_ID
                                                        SELECT @InsertedID;
                                                        ";


                                                using (SqlCommand insPartyCmd = new SqlCommand(upsertPartyQuery, conn, tran))
                                                {
                                                    insPartyCmd.Parameters.AddWithValue("@Party_IsActive", false);
                                                    insPartyCmd.Parameters.AddWithValue("@Party_IsLocked", false);
                                                    insPartyCmd.Parameters.AddWithValue("@Party_Name", obj.Dealer_Name ?? (object)DBNull.Value);
                                                    insPartyCmd.Parameters.AddWithValue("@Party_Location", obj.Dealer_Location ?? (object)DBNull.Value);
                                                    insPartyCmd.Parameters.AddWithValue("@Party_Email", obj.Dealer_Email ?? (object)DBNull.Value);
                                                    insPartyCmd.Parameters.AddWithValue("@Party_Phone", obj.Dealer_Phone ?? (object)DBNull.Value);
                                                    insPartyCmd.Parameters.AddWithValue("@Party_Fax", obj.Dealer_Fax ?? (object)DBNull.Value);
                                                    insPartyCmd.Parameters.AddWithValue("@Party_PaymentTerms", string.IsNullOrEmpty("") ? (object)DBNull.Value : "");
                                                    insPartyCmd.Parameters.AddWithValue("@PartyType", 99);
                                                    insPartyCmd.Parameters.AddWithValue("@Party_Mobile", obj.Dealer_Mobile ?? (object)DBNull.Value);
                                                    insPartyCmd.Parameters.AddWithValue("@ModifiedBY", userID);
                                                    insPartyCmd.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                                                    insPartyCmd.Parameters.AddWithValue("@Country_ID", countryId > 0 ? countryId : (object)DBNull.Value);
                                                    insPartyCmd.Parameters.AddWithValue("@State_ID", stateId > 0 ? stateId : (object)DBNull.Value);
                                                    insPartyCmd.Parameters.AddWithValue("@Company_ID", extCompanyId > 0 ? extCompanyId : (object)DBNull.Value);
                                                    insPartyCmd.Parameters.AddWithValue("@Relationship_Branch_ID", insertedBranchId > 0 ? insertedBranchId : (object)DBNull.Value);
                                                    insPartyCmd.Parameters.AddWithValue("@IsOEM", false);
                                                    insPartyCmd.Parameters.AddWithValue("@IsDealer", true);
                                                    insPartyCmd.Parameters.AddWithValue("@Relationship_Company_ID", (object)DBNull.Value);
                                                    insPartyCmd.Parameters.AddWithValue("@PartsCreditLimit", 0);
                                                    insPartyCmd.Parameters.AddWithValue("@SalesCreditLimit", 0);
                                                    insPartyCmd.Parameters.AddWithValue("@Currency_ID", currencyId > 0 ? currencyId : (object)DBNull.Value);
                                                    insPartyCmd.Parameters.AddWithValue("@IsImportExport", false);
                                                    insPartyCmd.Parameters.AddWithValue("@ServiceCreditLimit", 0);
                                                    insPartyCmd.Parameters.AddWithValue("@PaymentDueDays", 0);
                                                    insPartyCmd.Parameters.AddWithValue("@PartsOutStandingCredit", 0);
                                                    insPartyCmd.Parameters.AddWithValue("@ServiceOutStandingCredit", 0);
                                                    insPartyCmd.Parameters.AddWithValue("@RemanOutStandingCredit", 0);
                                                    insPartyCmd.Parameters.AddWithValue("@PartyAdvanceAmount", 0);
                                                    insPartyCmd.Parameters.AddWithValue("@SalesOutStandingCredit", 0);
                                                    insPartyCmd.Parameters.AddWithValue("@CustomerType_ID", (object)DBNull.Value);
                                                    insPartyCmd.Parameters.AddWithValue("@IsKeyCustomer", false);
                                                    insPartyCmd.Parameters.AddWithValue("@Region_ID", regionId > 0 ? regionId : (object)DBNull.Value);
                                                    insPartyCmd.Parameters.AddWithValue("@Party_Code", obj.Dealer_ShortName ?? (object)DBNull.Value);
                                                    insPartyCmd.Parameters.AddWithValue("@SupplierHasInterface", true);
                                                    insPartyCmd.Parameters.AddWithValue("@CustomerType", (object)DBNull.Value);
                                                    insPartyCmd.Parameters.AddWithValue("@CustomerLanguageID", LangID);
                                                    insPartyCmd.Parameters.AddWithValue("@IsPONumberMandatory", false);
                                                    insPartyCmd.Parameters.AddWithValue("@ServiceCreditLimitinUSD", 0);
                                                    insPartyCmd.Parameters.AddWithValue("@ServiceOutStandingCreditinUSD", 0);
                                                    insPartyCmd.Parameters.AddWithValue("@CAD_ExchangeRate", 0);
                                                    insPartyCmd.Parameters.AddWithValue("@US_ExchangeRate", 0);
                                                    insPartyCmd.Parameters.AddWithValue("@CreditExceededMailSent", false);
                                                    insPartyCmd.Parameters.AddWithValue("@IsInternalCustomer", false);
                                                    insPartyCmd.Parameters.AddWithValue("@Variance_Percentage", 0);
                                                    insPartyCmd.Parameters.AddWithValue("@Variance_Value", 0);

                                                    var insertedPartyId = insPartyCmd.ExecuteScalar();

                                                    if (insertedPartyId != null)
                                                    {
                                                        string upsertAddressQuery = @"
                                                                IF EXISTS (SELECT 1 FROM GNM_PartyAddress WHERE Party_ID = @Party_ID AND PartyAddress_Location = @PartyAddress_Location)
                                                                BEGIN
                                                                    UPDATE GNM_PartyAddress
                                                                    SET PartyAddress_Address = @PartyAddress_Address,
                                                                        PartyAddress_Active = @PartyAddress_Active,
                                                                        PartyAddress_ZIP = @PartyAddress_ZIP
                                                                    WHERE Party_ID = @Party_ID AND PartyAddress_Location = @PartyAddress_Location;
                                                                END
                                                                ELSE
                                                                BEGIN
                                                                    INSERT INTO GNM_PartyAddress 
                                                                    (Party_ID, PartyAddress_Location, PartyAddress_Address, PartyAddress_LeadTimeInDays, 
                                                                    PartyAddress_CountryID, PartyAddress_StateID, PartyAddress_Active, PartyAddress_ZIP, 
                                                                    IsDefault, EquivalentConsignee_ID, Region_ID) 
                                                                    VALUES 
                                                                    (@Party_ID, @PartyAddress_Location, @PartyAddress_Address, @PartyAddress_LeadTimeInDays, 
                                                                    @PartyAddress_CountryID, @PartyAddress_StateID, @PartyAddress_Active, @PartyAddress_ZIP, 
                                                                    @IsDefault, @EquivalentConsignee_ID, @Region_ID);
                                                                END";

                                                        using (SqlCommand insaAddCmd = new SqlCommand(upsertAddressQuery, conn, tran))
                                                        {
                                                            insaAddCmd.Parameters.AddWithValue("@Party_ID", insertedPartyId);
                                                            insaAddCmd.Parameters.AddWithValue("@PartyAddress_Location", obj.Dealer_Location);
                                                            insaAddCmd.Parameters.AddWithValue("@PartyAddress_Address", obj.Dealer_Address);
                                                            insaAddCmd.Parameters.AddWithValue("@PartyAddress_LeadTimeInDays", 1);
                                                            insaAddCmd.Parameters.AddWithValue("@PartyAddress_CountryID", countryId);
                                                            insaAddCmd.Parameters.AddWithValue("@PartyAddress_StateID", stateId);
                                                            insaAddCmd.Parameters.AddWithValue("@PartyAddress_Active", true);
                                                            insaAddCmd.Parameters.AddWithValue("@PartyAddress_ZIP", obj.Dealer_ZipCode);
                                                            insaAddCmd.Parameters.AddWithValue("@IsDefault", true);
                                                            insaAddCmd.Parameters.AddWithValue("@EquivalentConsignee_ID", DBNull.Value);
                                                            insaAddCmd.Parameters.AddWithValue("@Region_ID", regionId);
                                                            insaAddCmd.ExecuteNonQuery();
                                                        }

                                                        string checkExistingContactQuery = @"
                                                            IF EXISTS (
                                                                SELECT 1 FROM GNM_PartyContactPersonDetails 
                                                                WHERE Party_ID = @Party_ID AND PartyContactPerson_Email = @PartyContactPerson_Email AND PartyContactPerson_Name=@PartyContactPerson_Name
                                                            )
                                                            BEGIN
                                                                UPDATE GNM_PartyContactPersonDetails
                                                                SET 
                                                                    PartyContactPerson_Name = @PartyContactPerson_Name,
                                                                    PartyContactPerson_Department = @PartyContactPerson_Department,
                                                                    PartyContactPerson_Mobile = @PartyContactPerson_Mobile,
                                                                    PartyContactPerson_Phone = @PartyContactPerson_Phone,
                                                                    Party_IsDefaultContact = @Party_IsDefaultContact,
                                                                    PartyContactPerson_IsActive = @PartyContactPerson_IsActive,
                                                                    PartyContactPerson_Remarks = @PartyContactPerson_Remarks,
                                                                    Language_ID = @Language_ID,
                                                                    PartyContactPerson_DOB = @PartyContactPerson_DOB
                                                                WHERE Party_ID = @Party_ID AND PartyContactPerson_Email = @PartyContactPerson_Email AND  PartyContactPerson_Name=@PartyContactPerson_Name;
                                                            END
                                                            ELSE
                                                            BEGIN
                                                                EXEC InsertXMLPartyContactPersonDetails @XmlData;
                                                            END";
                                                        using (SqlCommand checkCmd = new SqlCommand(checkExistingContactQuery, conn, tran))
                                                        {
                                                            foreach (var contact in obj.PartyContactPersonDetails)
                                                            {
                                                                checkCmd.Parameters.Clear();
                                                                checkCmd.Parameters.AddWithValue("@Party_ID", insertedPartyId);
                                                                checkCmd.Parameters.AddWithValue("@PartyContactPerson_Email", contact.PartyContactPerson_Email ?? (object)DBNull.Value);

                                                                // Assign values or fallbacks
                                                                //checkCmd.Parameters.AddWithValue("@PartyContactPerson_Name", contact.PartyContactPerson_Name ?? obj.Dealer_Name ?? "N/A");
                                                                checkCmd.Parameters.AddWithValue("@PartyContactPerson_Name", string.IsNullOrEmpty(contact.PartyContactPerson_Name) ? (string.IsNullOrEmpty(obj.Dealer_Name) ? "N/A" : obj.Dealer_Name) : contact.PartyContactPerson_Name);
                                                                checkCmd.Parameters.AddWithValue("@PartyContactPerson_Department", contact.PartyContactPerson_Department ?? "N/A");
                                                                //checkCmd.Parameters.AddWithValue("@PartyContactPerson_Mobile", contact.PartyContactPerson_Mobile ?? obj.Dealer_Mobile ?? "0000000000");
                                                                checkCmd.Parameters.AddWithValue("@PartyContactPerson_Mobile", string.IsNullOrEmpty(contact.PartyContactPerson_Mobile) ? (string.IsNullOrEmpty(obj.Dealer_Mobile) ? "0000000000" : obj.Dealer_Mobile) : contact.PartyContactPerson_Mobile);
                                                                //checkCmd.Parameters.AddWithValue("@PartyContactPerson_Phone", contact.PartyContactPerson_Phone ?? obj.Dealer_Phone ?? "0000000000");
                                                                checkCmd.Parameters.AddWithValue("@PartyContactPerson_Phone", string.IsNullOrEmpty(contact.PartyContactPerson_Phone) ? (string.IsNullOrEmpty(obj.Dealer_Phone) ? "0000000000" : obj.Dealer_Phone) : contact.PartyContactPerson_Phone);
                                                                checkCmd.Parameters.AddWithValue("@Party_IsDefaultContact", false);
                                                                checkCmd.Parameters.AddWithValue("@PartyContactPerson_IsActive", false);
                                                                checkCmd.Parameters.AddWithValue("@PartyContactPerson_Remarks", contact.Remarks ?? "");
                                                                checkCmd.Parameters.AddWithValue("@Language_ID", LangID > 0 ? LangID : 1);
                                                                checkCmd.Parameters.AddWithValue("@PartyContactPerson_DOB",
                                                                    !string.IsNullOrEmpty(contact.PartyContactPerson_DOB) && DateTime.TryParse(contact.PartyContactPerson_DOB, out DateTime dob)
                                                                    ? (object)dob
                                                                    : DBNull.Value);

                                                                var contactDetailsXml = new XElement("Contacts",
                                                                    new XElement("Contact",
                                                                        new XElement("Party_ID", insertedPartyId),
                                                                        new XElement("PartyContactPerson_Name", string.IsNullOrEmpty(contact.PartyContactPerson_Name) ? (string.IsNullOrEmpty(obj.Dealer_Name) ? "N/A" : obj.Dealer_Name) : contact.PartyContactPerson_Name),
                                                                        new XElement("PartyContactPerson_Email", contact.PartyContactPerson_Email ?? (object)DBNull.Value),
                                                                        new XElement("PartyContactPerson_Department", contact.PartyContactPerson_Department ?? "N/A"),
                                                                        new XElement("PartyContactPerson_Mobile", string.IsNullOrEmpty(contact.PartyContactPerson_Mobile) ? (string.IsNullOrEmpty(obj.Dealer_Mobile) ? "0000000000" : obj.Dealer_Mobile) : contact.PartyContactPerson_Mobile),
                                                                        new XElement("PartyContactPerson_Phone", string.IsNullOrEmpty(contact.PartyContactPerson_Phone) ? (string.IsNullOrEmpty(obj.Dealer_Phone) ? "0000000000" : obj.Dealer_Phone) : contact.PartyContactPerson_Phone),
                                                                        new XElement("Party_IsDefaultContact", false),
                                                                        new XElement("PartyContactPerson_IsActive", false),
                                                                        new XElement("PartyContactPerson_Remarks", contact.Remarks ?? ""),
                                                                        new XElement("Language_ID", LangID),
                                                                        new XElement("PartyContactPerson_DOB",
                                                                            !string.IsNullOrEmpty(contact.PartyContactPerson_DOB) && DateTime.TryParse(contact.PartyContactPerson_DOB, out DateTime dobXml)
                                                                                ? (object)dobXml
                                                                                : DBNull.Value)
                                                                    )
                                                                );
                                                                checkCmd.Parameters.AddWithValue("@XmlData", contactDetailsXml.ToString());

                                                                checkCmd.ExecuteNonQuery();
                                                            }
                                                        }
                                                    }
                                                }
                                                //Inserting to Consignee address based on branch
                                                using (SqlCommand checkCmd = new SqlCommand(@"
                                                SELECT TOP 1 Consignee_ID 
                                                FROM GNM_Consignee 
                                                WHERE Branch_ID = @Branch_ID AND ConsigneeAddress = @ConsigneeAddress", conn, tran))
                                                {
                                                    checkCmd.Parameters.AddWithValue("@Branch_ID", insertedBranchId);
                                                    checkCmd.Parameters.AddWithValue("@ConsigneeAddress", obj.Dealer_Address ?? (object)DBNull.Value);

                                                    var existingId = checkCmd.ExecuteScalar();
                                                    if (existingId == null)
                                                    {
                                                        // Step 2: Insert if not exists
                                                        using (SqlCommand insertCmd = new SqlCommand(@"
                                                        INSERT INTO GNM_Consignee 
                                                            (Company_ID, Branch_ID, ConsigneeLocation, ConsigneeAddress, WareHouse_ID, IsActive, State_ID)
                                                        VALUES 
                                                            (@Company_ID, @Branch_ID, @ConsigneeLocation, @ConsigneeAddress, 1, 1, @State_ID);", conn, tran))
                                                        {
                                                            insertCmd.Parameters.AddWithValue("@Company_ID", extCompanyId);
                                                            insertCmd.Parameters.AddWithValue("@Branch_ID", insertedBranchId);
                                                            insertCmd.Parameters.AddWithValue("@ConsigneeLocation", obj.Dealer_Location ?? (object)DBNull.Value);
                                                            insertCmd.Parameters.AddWithValue("@ConsigneeAddress", obj.Dealer_Address ?? (object)DBNull.Value);
                                                            insertCmd.Parameters.AddWithValue("@State_ID", stateId);

                                                            insertCmd.ExecuteNonQuery();
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        if (isUnique)
                                        {
                                            Common.LogInsertStatus(logID,
                                                 $"The provided Dealer Master with Dealer Name: {obj.Dealer_Name} and Dealer ShortName: {obj.Dealer_ShortName} has been successfully inserted.",
                                                 true, connString, apiCategory);

                                            string emailSubjecthead = $"The provided Dealer Master with Dealer Name: {obj.Dealer_Name} and Dealer ShortName: {obj.Dealer_ShortName} has been successfully inserted.";
                                            string emailTemplatehead = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, true, emailSubjecthead);

                                            Common.InsertEmailLog(conn, emailSubjecthead, emailTemplatehead, Recipient, ccRecipient, tran);
                                            detail = new
                                            {
                                                Message = $"The provided Dealer Master with Dealer Name: {obj.Dealer_Name} and Dealer ShortName: {obj.Dealer_ShortName} insertion successful."
                                            };
                                            messages.Add(detail);
                                        }
                                        else
                                        {
                                            Common.LogInsertStatus(logID,
                                                 $"The provided Dealer Master with Dealer Name: {obj.Dealer_Name} and Dealer ShortName: {obj.Dealer_ShortName} has been successfully updated.",
                                                 true, connString, apiCategory);

                                            string emailSubjecthead = $"The provided Dealer Master with Dealer Name: {obj.Dealer_Name} and Dealer ShortName: {obj.Dealer_ShortName} has been successfully updated.";
                                            string emailTemplatehead = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, true, emailSubjecthead);

                                            Common.InsertEmailLog(conn, emailSubjecthead, emailTemplatehead, Recipient, ccRecipient, tran);
                                            detail = new
                                            {
                                                Message = $"The provided Dealer Master with Dealer Name: {obj.Dealer_Name} and Dealer ShortName: {obj.Dealer_ShortName} updation successful."
                                            };
                                            messages.Add(detail);
                                        }
                                        
                                    }
                                    
                                }
                                else
                                {
                                    string invalidColumnsMessage = string.Join(", ", invalidColumns2);
                                    somePartsNotInserted = true;
                                    Common.LogInsertStatus(logID,
                                    $"The provided Dealer Master with Dealer Name: {obj.Dealer_Name} and Dealer ShortName: {obj.Dealer_ShortName} has invalid columns: {invalidColumnsMessage}",
                                    false, connString, apiCategory);
                                    string emailSubject = $"The provided Dealer Master with Dealer Name: {obj.Dealer_Name} and Dealer ShortName: {obj.Dealer_ShortName} Insert/Update failed.";
                                    string emailBody = $"The Updation/Insertion of the Dealer Master with Dealer Name: {obj.Dealer_Name} and Dealer ShortName: {obj.Dealer_ShortName} has failed. " +
                                    $"Invalid columns: {invalidColumnsMessage}\n\n";

                                    string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, false, emailBody);
                                    Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                                    detail = new
                                    {
                                        Message = $"The provided Dealer Master with Dealer Name: {obj.Dealer_Name} and Dealer ShortName: {obj.Dealer_ShortName} has invalid columns: {invalidColumnsMessage}"
                                    };
                                    messages.Add(detail);
                                }
                            }
                            

                        }
                        else
                        {
                            string invalidColumnsMessage = string.Join(", ", invalidColumns);
                            somePartsNotInserted = true;
                            Common.LogInsertStatus(logID,
                            $"The provided Dealer Master with Dealer Name: {obj.Dealer_Name} and Dealer ShortName: {obj.Dealer_ShortName} has missing columns: {invalidColumnsMessage}",
                            false, connString, apiCategory);
                            string emailSubject = $"The provided Dealer Master with Dealer Name: {obj.Dealer_Name} and Dealer ShortName: {obj.Dealer_ShortName} Insert/Update failed.";
                            string emailBody = $"The insertion/updation of the Dealer Master with Dealer Name: {obj.Dealer_Name} and Dealer ShortName: {obj.Dealer_ShortName} has failed. " +
                            $"Missing columns: {invalidColumnsMessage}\n\n";

                            string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, false, emailBody);
                            Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                            detail = new
                            {
                                Message = $"The provided Dealer Master with Dealer Name: {obj.Dealer_Name} and Dealer ShortName: {obj.Dealer_ShortName} has missing columns: {invalidColumnsMessage}"
                            };
                            messages.Add(detail);
                        }

                    }
                    catch (Exception ex)
                    {
                        tran.Rollback();
                        somePartsNotInserted = true;

                        // Log the error with missing columns and exception details
                        string errorMessage = $"The provided Dealer Master with Dealer Name: {obj.Dealer_Name} and Dealer ShortName: {obj.Dealer_ShortName} insertion/updation failed. Exception: {ex.Message}";

                        Common.LogInsertStatus(logID, errorMessage, false, connString, apiCategory);

                        // Prepare email with details of the error
                        string emailSubject = $"The provided Dealer Master with Dealer Name: {obj.Dealer_Name} and Dealer ShortName: {obj.Dealer_ShortName} Insertion/Updation failed.";
                        string emailBody = $"The insertion of the Dealer Master with Dealer Name: {obj.Dealer_Name} and Dealer ShortName: {obj.Dealer_ShortName} has failed. " +

                                           $"Error Details: {ex.Message}\n\n" +
                                           $"Stack Trace: {ex.StackTrace}";

                        string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Exception", fullUrl, false, emailBody);
                        Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                        detail = new
                        {
                            Message = $"The provided Dealer Master with Dealer Name: {obj.Dealer_Name} and Dealer ShortName: {obj.Dealer_ShortName} insertion/updation failed."
                        };
                        messages.Add(detail);
                        throw;
                    }
                }
                tran.Commit();
            }

            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    if (connection.State == ConnectionState.Closed || connection.State == ConnectionState.Broken)
                    {
                        connection.Open();
                    }
                    string updateQuery = @"
                    UPDATE SAPDMS_API_Request_Log
                    SET Status = @Status, ResponseMessage = @ResponseMessage
                    WHERE LogID = @LogID";

                    using (SqlCommand cmd = new SqlCommand(updateQuery, connection))
                    {
                        if (somePartsNotInserted)
                        {
                            cmd.Parameters.AddWithValue("@Status", "Error");
                            cmd.Parameters.AddWithValue("@ResponseMessage", "Some/all dealer master(s) is/are invalid.");
                        }
                        else
                        {
                            cmd.Parameters.AddWithValue("@Status", "Success");
                            cmd.Parameters.AddWithValue("@ResponseMessage", "Inserted Successfully!");
                            if (isUnique)
                            {
                                detail = new
                                {
                                    Message = "Inserted Successfully!"
                                };
                            }
                            else
                            {
                                detail = new
                                {
                                    Message = "Updated Successfully!"
                                };
                            }
                            
                        }
                        cmd.Parameters.AddWithValue("@LogID", logID);
                        cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            if (somePartsNotInserted)
            {
                return new JsonResult(messages);
            }
            else
            {
                return new JsonResult(detail);
            }

        }
        #endregion
        #region PO CANCELLATION
        public static IActionResult PurchaseOrderCancellationInterface(PurchaseOrderCancellationInterfaceList Obj, string connString, int LogException, string fullUrl, string clientIP, string Recipient, string ccRecipient)
        {
            var x = default(dynamic);
            bool somePartsNotInserted = false;
            string apiCategory = "PurchaseOrderCancellation";
            string partHeader = "PurchaseOrderCancellation";
            int logID = 0;
            string apiEndpoint = fullUrl;
            string requestBody = JsonConvert.SerializeObject(Obj);
            logID = Common.LogRequest(apiEndpoint, requestBody, clientIP, apiCategory, connString, LogException);
            int? purchaseOrderID = 0;
            int? partsOrderID = 0;
            int? companyID = 0;
            int? branchID = 0;
            int? modifiedBy = 0;
            int insertedId = 0;
            bool isRowDetValid = true;
            bool someDetPartsNotInserted = false;
            int? WareHouse_ID = 0;
            bool isJsonValid = true;
            PRT_POCancellation headerRow = new PRT_POCancellation();
            using (SqlConnection conn = new SqlConnection(connString))
            {
                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                {
                    conn.Open();
                }
                bool isRowValid = true;
                SqlTransaction tran = conn.BeginTransaction();
                try
                {
                    var columns = new Dictionary<string, bool>
                {
                    { "PurchaseOrderNumber", true },
                    { "PartsOrderNumber", false },
                    { "OrderCancellation_Type", false },
                    { "ReasonForCancellation", true },
                    { "Branch_ShortName", true },
                    { "User_LoginID", true },
                    { "ModifiedDate", true },
                    { "PartsOrderCancellationDate", true }
                };

                    JObject row = JObject.FromObject(new
                    {
                        PurchaseOrderNumber = Obj.PurchaseOrderNumber,
                        PartsOrderNumber = Obj.PartsOrderNumber,
                        OrderCancellation_Type = Obj.PurchaseOrderNumber,
                        ReasonForCancellation = Obj.PurchaseOrderNumber,
                        Branch_ShortName = Obj.PurchaseOrderNumber,
                        User_LoginID = Obj.PurchaseOrderNumber,
                        ModifiedDate = Obj.PurchaseOrderNumber,
                        PartsOrderCancellationDate = Obj.PurchaseOrderNumber,
                    });
                    List<string> invalidColumns;
                    isRowValid = Common.ValidateAndLog(row, columns, out invalidColumns);
                    if (!isRowValid)
                    {
                        string invalidColumnsMessage = string.Join(", ", invalidColumns);
                        somePartsNotInserted = true;
                        Common.LogInsertStatus(logID,
                       $"The provided Purchase Order Cancellation header with PurchaseOrderNumber: {Obj.PurchaseOrderNumber} has null columns {invalidColumnsMessage}",
                       false, connString, apiCategory);
                        string emailSubject = $"The provided Purchase Order Cancellation header with PurchaseOrderNumber: {Obj.PurchaseOrderNumber} Insertion failed.";
                        string emailBody = $"The insertion of the Purchase Order Cancellation header with PurchaseOrderNumber: {Obj.PurchaseOrderNumber} has failed. " +
                        $"Missing columns: {invalidColumnsMessage}\n\n";

                        string emailTemplate = Common.GetEmailTemplatePurchaseOrderCancellation(partHeader, "Validation", fullUrl, false, Obj.PurchaseOrderNumber, emailBody);
                        Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                    }
                    else
                    {
                        using (SqlCommand cmd = new SqlCommand("UP_AMERP_PURCHASE_ORDER_CANCELLATION_INSERT_INITIALIZE_1", conn, tran))
                        {
                            cmd.CommandType = CommandType.StoredProcedure; // Add this line!
                            cmd.Parameters.AddWithValue("@PurchaseOrderNumber", Obj.PurchaseOrderNumber);
                            cmd.Parameters.AddWithValue("@PartsOrderNumber", string.IsNullOrEmpty(Obj.PartsOrderNumber) ? "0" : Obj.PartsOrderNumber);
                            cmd.Parameters.AddWithValue("@Branch_ShortName", Obj.BranchShortName);
                            cmd.Parameters.AddWithValue("@User_LoginID", Obj.UserLoginID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.HasRows)
                                {
                                    while (reader.Read())
                                    {
                                        purchaseOrderID = reader.IsDBNull(reader.GetOrdinal("PurchaseOrder_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("PurchaseOrder_ID"));
                                        partsOrderID = reader.IsDBNull(reader.GetOrdinal("PartsOrder_ID"))
                                        ? (int?)null
                                        : reader.GetInt32(reader.GetOrdinal("PartsOrder_ID")) == 0
                                          ? (int?)null
                                          : reader.GetInt32(reader.GetOrdinal("PartsOrder_ID"));

                                        companyID = reader.IsDBNull(reader.GetOrdinal("Company_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Company_ID"));
                                        branchID = reader.IsDBNull(reader.GetOrdinal("Branch_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Branch_ID"));
                                        modifiedBy = reader.IsDBNull(reader.GetOrdinal("ModifiedBy")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ModifiedBy"));
                                        WareHouse_ID = reader.IsDBNull(reader.GetOrdinal("WareHouse_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WareHouse_ID"));
                                   }
                                }
                            }
                        }
                        var col = new Dictionary<string, bool>
                        {
                            { "purchaseOrderID", true },
                            { "partsOrderID", false },
                            { "companyID", true },
                            { "branchID", true },
                            { "modifiedBy", true },
                            {"WareHouse_ID",true }
                        };

                        JObject Row = JObject.FromObject(new
                        {
                            purchaseOrderID = purchaseOrderID == 0 ? (int?)null : purchaseOrderID,
                            partsOrderID = partsOrderID == 0 ? (int?)null : partsOrderID,
                            companyID = companyID == 0 ? (int?)null : companyID,
                            branchID = branchID == 0 ? (int?)null : branchID,
                            modifiedBy = modifiedBy == 0 ? (int?)null : modifiedBy,
                            WareHouse_ID = WareHouse_ID == 0 ? (int?)null : WareHouse_ID
                        });

                        List<string> invalidColumns2;
                        isRowValid = Common.ValidateAndLog(row, columns, out invalidColumns2);
                        if (!isRowValid)
                        {
                           string invalidColumnsMessage = string.Join(", ", invalidColumns2);
                           somePartsNotInserted = true;
                           Common.LogInsertStatus(logID,
                           $"The provided Purchase Order Cancellation header with PurchaseOrderNumber: {Obj.PurchaseOrderNumber} has invalid columns {invalidColumnsMessage}",
                           false, connString, apiCategory);
                            string emailSubject = $"The provided Purchase Order Cancellation header with PurchaseOrderNumber: {Obj.PurchaseOrderNumber} Insertion failed.";
                            string emailBody = $"The insertion of the Purchase Order Cancellation header with PurchaseOrderNumber: {Obj.PurchaseOrderNumber} has failed. " +
                            $"Invalid columns: {invalidColumnsMessage}\n\n";
                            string emailTemplate = Common.GetEmailTemplatePurchaseOrderCancellation(partHeader, "Validation", fullUrl, false, Obj.PurchaseOrderNumber, emailBody);
                            Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                        }
                        else
                        {
                            headerRow.Branch_ID = branchID;
                            headerRow.Company_ID = companyID;
                            headerRow.ModifiedBy = modifiedBy;
                            headerRow.ModifiedDate = Obj.ModifiedDate;
                            headerRow.OrderCancellation_Type = Obj.OrderCancellationType;
                            headerRow.PartsOrder_ID = partsOrderID;
                            headerRow.PartsOrderCancellationDate = Obj.PartsOrderCancellationDate;
                            headerRow.PurchaseOrder_ID = purchaseOrderID;
                            headerRow.ReasonForCancellation = Obj.ReasonForCancellation;
                            headerRow.POCancellationNumber = "";

                            if (Common.CheckPreffixSuffix(connString, LogException, companyID ?? 0, branchID ?? 0, "PRT_PurchaseOrderCancellation"))
                            {
                                string insertHeaderQuery = @"
                                DECLARE @InsertedIds TABLE (OrderCancellation_ID INT);
                                INSERT INTO PRT_POCancellation 
                               (Branch_ID, Company_ID, ModifiedBy, ModifiedDate,
                                OrderCancellation_Type, PartsOrder_ID, PartsOrderCancellationDate, 
                                PurchaseOrder_ID, ReasonForCancellation, TotalOrderCancellationAmount, 
                                POCancellationNumber,FinancialYear) 
                                OUTPUT INSERTED.OrderCancellation_ID INTO @InsertedIds
                                VALUES 
                               (@Branch_ID, @Company_ID, @ModifiedBy, @ModifiedDate,
                                @OrderCancellation_Type, @PartsOrder_ID, @PartsOrderCancellationDate, 
                                @PurchaseOrder_ID, @ReasonForCancellation, @TotalOrderCancellationAmount, 
                                @POCancellationNumber,@FinancialYear);
                                SELECT OrderCancellation_ID FROM @InsertedIds;";
                                using (SqlCommand cmd = new SqlCommand(insertHeaderQuery, conn, tran))
                                {
                                    cmd.CommandType = CommandType.Text; // Add this line!
                                    cmd.Parameters.AddWithValue("@Branch_ID", headerRow.Branch_ID);
                                    cmd.Parameters.AddWithValue("@Company_ID", headerRow.Company_ID);
                                    cmd.Parameters.AddWithValue("@ModifiedBy", headerRow.ModifiedBy);
                                    cmd.Parameters.AddWithValue("@ModifiedDate", headerRow.ModifiedDate);
                                    cmd.Parameters.AddWithValue("@OrderCancellation_Type", headerRow.OrderCancellation_Type);
                                    cmd.Parameters.AddWithValue("@PartsOrder_ID", headerRow.PartsOrder_ID ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@PartsOrderCancellationDate", headerRow.PartsOrderCancellationDate);
                                    cmd.Parameters.AddWithValue("@PurchaseOrder_ID", headerRow.PurchaseOrder_ID);
                                    cmd.Parameters.AddWithValue("@ReasonForCancellation", headerRow.ReasonForCancellation);
                                    cmd.Parameters.AddWithValue("@TotalOrderCancellationAmount", "0.0");
                                    cmd.Parameters.AddWithValue("@POCancellationNumber", headerRow.POCancellationNumber); // Empty string
                                    cmd.Parameters.AddWithValue("@FinancialYear", ""); // Empty string

                                    insertedId = (int)cmd.ExecuteScalar();
                                }

                                if (insertedId > 0)
                                {
                                    var columnsDet = new Dictionary<string, bool>
                                    {
                                        { "Parts_PartPrefix", true },
                                        { "Parts_PartsNumber", true },
                                        { "PickedQuantity", true },
                                        { "CancelledQuantity", true },
                                    };
                                    List<bool> inserted = new List<bool>();
                                    int headCount = 0;
                                    foreach (var purDet in Obj.PartsOrderDetails)
                                    {
                                        int? partsID = 0;
                                        int? partsOrderPartsDetailID = 0;

                                        decimal rate = 0;
                                        decimal invoicedQty = 0;
                                        decimal orderedQuantity = 0;
                                        decimal CancelledQty = 0;
                                        decimal ApprovedQty = 0;
                                        string PartsOrderNumber = "";
                                        decimal amount = 0;
                                        int? isValid = 0;
                                        JObject rowDet = JObject.FromObject(new
                                        {
                                            Parts_PartPrefix = purDet.PartsPartPrefix,
                                            Parts_PartsNumber = purDet.PartsPartsNumber,
                                            PickedQuantity = purDet.PickedQuantity,
                                            CancelledQuantity = purDet.CancelledQuantity,
                                        });

                                        List<string> invalidDetColumns;
                                        isRowDetValid = Common.ValidateAndLog(row, columns, out invalidDetColumns);
                                        if (!isRowDetValid)
                                        {
                                           string invalidColumnsMessage = string.Join(", ", invalidDetColumns);
                                           someDetPartsNotInserted = true;
                                           Common.LogInsertStatus(logID,
                                           $"The provided Purchase Order Cancellation Detailer with PartPrefix: {purDet.PartsPartPrefix} and  PartsNumber :{purDet.PartsPartsNumber} has null columns {invalidColumnsMessage}",
                                           false, connString, apiCategory);
                                           string emailSubject = $"The provided Purchase Order Cancellation Detailer with PartPrefix: {purDet.PartsPartPrefix} and  PartsNumber :{purDet.PartsPartsNumber} Insertion failed.";
                                           string emailBody = $"The insertion of the Purchase Order Cancellation Detailer with PartPrefix: {purDet.PartsPartPrefix} and  PartsNumber :{purDet.PartsPartsNumber} has failed. " +
                                           $"Missing columns: {invalidColumnsMessage}\n\n";

                                           string emailTemplate = Common.GetEmailTemplate(partHeader, purDet.PartsPartPrefix, purDet.PartsPartsNumber, "Validation", fullUrl, false, emailBody);
                                           Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                                           inserted.Add(false);
                                           continue;
                                        }
                                        else
                                        {
                                            using (SqlCommand cmd = new SqlCommand("UP_AMERP_PURCHASE_ORDER_CANCELLATION_DETAILER_INSERT_INITIALIZE_1", conn, tran))
                                            {
                                                cmd.CommandType = CommandType.StoredProcedure; // Add this line!
                                                cmd.Parameters.AddWithValue("@PartsPartPrefix", purDet.PartsPartPrefix);
                                                cmd.Parameters.AddWithValue("@PartsPartsNumber", purDet.PartsPartsNumber);
                                                cmd.Parameters.AddWithValue("@PurchaseOrder_ID", purchaseOrderID);

                                                using (SqlDataReader reader = cmd.ExecuteReader())
                                                {
                                                    if (reader.HasRows)
                                                    {
                                                        while (reader.Read())
                                                        {
                                                            partsID = reader.IsDBNull(0) ? 0 : reader.GetInt32(0);
                                                            partsOrderPartsDetailID = reader.IsDBNull(1) ? (int?)null : reader.GetInt32(1);
                                                            rate = reader.IsDBNull(2) ? 0.0m : reader.GetDecimal(2);
                                                            invoicedQty = reader.IsDBNull(3) ? 0.0m : reader.GetDecimal(3);
                                                            orderedQuantity = reader.IsDBNull(4) ? 0.0m : reader.GetDecimal(4);
                                                            ApprovedQty = reader.IsDBNull(5) ? 0.0m : reader.GetDecimal(5);
                                                            CancelledQty = reader.IsDBNull(6) ? 0.0m : reader.GetDecimal(6);
                                                            PartsOrderNumber = reader.IsDBNull(7) ? "" : reader.GetString(7);
                                                            isValid = reader.IsDBNull(8) ? 0 : reader.GetInt32(8);
                                                        }
                                                    }
                                                }
                                                amount = rate * purDet.CancelledQuantity;
                                                int? partsOrderPartsDetID = partsOrderPartsDetailID == 0 ? (int?)null : partsOrderPartsDetailID;

                                                var columnsDetIDs = new Dictionary<string, bool>
                                                {
                                                    { "partsID", true },
                                                    { "partsOrderPartsDetailID", false },
                                                    { "rate", true },
                                                    { "invoicedQty", false },
                                                    { "orderedQuantity", true },
                                                    { "ApprovedQty", true },
                                                    { "CancelledQty", false },
                                                    { "PartsOrderNumber", false }
                                                };

                                                JObject rowDetIDs = new JObject
                                                {
                                                    { "partsID", partsID == 0 ? (int?)null : partsID },
                                                    { "partsOrderPartsDetailID", partsOrderPartsDetailID == null ? (int?)null : partsOrderPartsDetailID },
                                                    { "rate", rate == 0.0m ? (decimal?)null : rate },
                                                    { "invoicedQty", invoicedQty == 0.0m ? (decimal?)null : invoicedQty },
                                                    { "orderedQuantity", orderedQuantity == 0.0m ? (decimal?)null : orderedQuantity },
                                                    { "approvedQty", ApprovedQty == 0.0m ? (decimal?)null : ApprovedQty },
                                                    { "cancelledQty", CancelledQty == 0.0m ? (decimal?)null : CancelledQty },
                                                    { "partsOrderNumber", string.IsNullOrEmpty(PartsOrderNumber) ? null : PartsOrderNumber }
                                                };

                                                List<string> invalidDetColumns2;
                                                isRowDetValid = Common.ValidateAndLog(row, columns, out invalidDetColumns2);
                                                if (!isRowDetValid)
                                                {
                                                    string invalidColumnsMessage = string.Join(", ", invalidDetColumns2);
                                                    someDetPartsNotInserted = true;
                                                    Common.LogInsertStatus(logID,
                                                   $"The provided Purchase Order Cancellation Detailer with PartPrefix: {purDet.PartsPartPrefix} and  PartsNumber :{purDet.PartsPartsNumber} has invalid columns {invalidColumnsMessage}",
                                                   false, connString, apiCategory);
                                                    string emailSubject = $"The provided Purchase Order Cancellation Detailer with PartPrefix: {purDet.PartsPartPrefix} and  PartsNumber :{purDet.PartsPartsNumber} Insertion failed.";
                                                    string emailBody = $"The insertion of the Purchase Order Cancellation Detailer with PartPrefix: {purDet.PartsPartPrefix} and  PartsNumber :{purDet.PartsPartsNumber} has failed. " +
                                                    $"Invalid columns: {invalidColumnsMessage}\n\n";

                                                    string emailTemplate = Common.GetEmailTemplate(partHeader, purDet.PartsPartPrefix, purDet.PartsPartsNumber, "Validation", fullUrl, false, emailBody);
                                                    Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                                                    inserted.Add(false);
                                                    continue;
                                                }
                                                else if (isValid == 0)
                                                {
                                                    someDetPartsNotInserted = true;
                                                    Common.LogInsertStatus(logID,
                                                      $"The provided Purchase Order Cancellation Detailer with PartPrefix: {purDet.PartsPartPrefix} and PartsNumber: {purDet.PartsPartsNumber} has failed. No purchase order exists to cancel, or the order has already been canceled.",
                                                      false, connString, apiCategory);

                                                    string emailSubject = $"The provided Purchase Order Cancellation Detailer with PartPrefix: {purDet.PartsPartPrefix} and PartsNumber: {purDet.PartsPartsNumber} Cancellation Failed.";
                                                    string emailBody = $"The cancellation of the Purchase Order Detailer with PartPrefix: {purDet.PartsPartPrefix} and PartsNumber: {purDet.PartsPartsNumber} has failed. No purchase order exists to cancel, or the order has already been canceled.\n\n";

                                                    string emailTemplate = Common.GetEmailTemplate(partHeader, purDet.PartsPartPrefix, purDet.PartsPartsNumber, "Validation", fullUrl, false, emailBody);
                                                    Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                                                    inserted.Add(false);
                                                    continue;
                                                }
                                                else if (CancelledQty < purDet.CancelledQuantity)
                                                {
                                                    someDetPartsNotInserted = true;
                                                    Common.LogInsertStatus(logID,
                                                    $"The provided Purchase Order Cancellation Detailer with PartPrefix: {purDet.PartsPartPrefix} and PartsNumber: {purDet.PartsPartsNumber} has failed. The Cancellation QTY Cannot be greater than {CancelledQty}.",
                                                    false, connString, apiCategory);

                                                    string emailSubject = $"The provided Purchase Order Cancellation Detailer with PartPrefix: {purDet.PartsPartPrefix} and PartsNumber: {purDet.PartsPartsNumber} Insertion failed.";
                                                    string emailBody = $"The insertion of the Purchase Order Cancellation Detailer with PartPrefix: {purDet.PartsPartPrefix} and PartsNumber: {purDet.PartsPartsNumber} has failed. The Cancellation QTY cannot be greater than {CancelledQty}.\n\n";

                                                    string emailTemplate = Common.GetEmailTemplate(partHeader, purDet.PartsPartPrefix, purDet.PartsPartsNumber, "Validation", fullUrl, false, emailBody);
                                                    Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                                                    inserted.Add(false);
                                                    continue;
                                                }
                                                else
                                                {
                                                    int insertedDetId = 0;
                                                    string insertDetQuery = @"
                                                    DECLARE @InsertedIds TABLE (OrderCancellationDetail_ID INT);
                                                    INSERT INTO PRT_OrderCancellationDetails
                                                    (
                                                        [OrderCancellation_ID],
                                                        [Parts_ID],
                                                        [Rate],
                                                        [PickedQuantity],
                                                        [OrderedQuantity],
                                                        [InvoicedQuantity],
                                                        [CancelledQuantity],
                                                        [Amount],
                                                        [WareHouse_ID],
                                                        [AllocatedQuantity],
                                                        [BackOrderQuantity],
                                                        [MRP],
                                                        [PartsOrderPartsDetail_ID]
                                                    )
                                                   OUTPUT INSERTED.OrderCancellationDetail_ID INTO @InsertedIds
                                                    VALUES
                                                    (
                                                        @OrderCancellation_ID,
                                                        @Parts_ID,
                                                        @Rate,
                                                        @PickedQuantity,
                                                        @OrderedQuantity,
                                                        @InvoicedQuantity,
                                                        @CancelledQuantity,
                                                        @Amount,
                                                        @WareHouse_ID,
                                                        @AllocatedQuantity,
                                                        @BackOrderQuantity,
                                                        @MRP,
                                                        @PartsOrderPartsDetail_ID
                                                    );
                                                     SELECT OrderCancellationDetail_ID FROM @InsertedIds;";
                                                    using (SqlCommand cmdDet = new SqlCommand(insertDetQuery, conn, tran))
                                                    {
                                                        cmdDet.CommandType = CommandType.Text; // Add this line!
                                                        cmdDet.Parameters.AddWithValue("@OrderCancellation_ID", insertedId);
                                                        cmdDet.Parameters.AddWithValue("@Parts_ID", partsID);
                                                        cmdDet.Parameters.AddWithValue("@Rate", rate);
                                                        cmdDet.Parameters.AddWithValue("@PickedQuantity", purDet.PickedQuantity);
                                                        cmdDet.Parameters.AddWithValue("@OrderedQuantity", orderedQuantity);
                                                        cmdDet.Parameters.AddWithValue("@InvoicedQuantity", invoicedQty);
                                                        cmdDet.Parameters.AddWithValue("@CancelledQuantity", purDet.CancelledQuantity);
                                                        cmdDet.Parameters.AddWithValue("@Amount", amount);
                                                        cmdDet.Parameters.AddWithValue("@WareHouse_ID", WareHouse_ID);
                                                        cmdDet.Parameters.AddWithValue("@AllocatedQuantity", 0);
                                                        cmdDet.Parameters.AddWithValue("@BackOrderQuantity", 0);
                                                        cmdDet.Parameters.AddWithValue("@MRP", 0);
                                                        cmdDet.Parameters.AddWithValue("@PartsOrderPartsDetail_ID", partsOrderPartsDetID.HasValue ? (object)partsOrderPartsDetID.Value : DBNull.Value);

                                                        insertedDetId = (int)cmdDet.ExecuteScalar();
                                                    }
                                                    if (insertedDetId > 0)
                                                    {
                                                        headCount = headCount + 1;
                                                        if (headCount == 1)
                                                        {
                                                            Common.LogInsertStatus(logID,
                                                            $"The provided order cancellation header with PurchaseOrderNumber: {Obj.PurchaseOrderNumber} has been successfully inserted.",
                                                            true, connString, apiCategory);
                                                            string emailSubjecthead = $"The provided order cancellation header with PurchaseOrderNumber: {Obj.PurchaseOrderNumber} has been successfully inserted.";
                                                            string emailTemplatehead = Common.GetEmailTemplatePurchaseOrderCancellation(partHeader, "Validation", fullUrl, true, Obj.PurchaseOrderNumber, emailSubjecthead);
                                                            Common.InsertEmailLog(conn, emailSubjecthead, emailTemplatehead, Recipient, ccRecipient, tran);
                                                        }

                                                        inserted.Add(true);

                                                        Common.LogInsertStatus(logID,
                                                        $"The provided order cancellation detailer with Part Prefix: {purDet.PartsPartPrefix} and Part Number :{purDet.PartsPartsNumber}has been successfully inserted.",
                                                        true, connString, apiCategory);
                                                        string emailSubjectDet = $"The provided order cancellation detailer with Part Prefix: {purDet.PartsPartPrefix} and Part Number :{purDet.PartsPartsNumber}has been successfully inserted.";
                                                        string emailTemplateDet = Common.GetEmailTemplate(partHeader, purDet.PartsPartPrefix, purDet.PartsPartsNumber, "Validation", fullUrl, true, emailSubjectDet);

                                                        Common.InsertEmailLog(conn, emailSubjectDet, emailTemplateDet, Recipient, ccRecipient, tran);

                                                        using (SqlCommand cmdDet = new SqlCommand("UP_AMERP_PURCHASE_ORDER_CANCELLATION_DETAILER_INSERT_INITIALIZE_2", conn, tran))
                                                        {
                                                            cmdDet.CommandType = CommandType.StoredProcedure; // Add this line!
                                                            cmdDet.Parameters.AddWithValue("@Parts_ID", partsID);
                                                            cmdDet.Parameters.AddWithValue("@PartsOrderPartsDetail_ID", partsOrderPartsDetailID);
                                                            cmdDet.Parameters.AddWithValue("@CancelledQuantity", purDet.CancelledQuantity);
                                                            cmdDet.Parameters.AddWithValue("@PartsOrderNumber", PartsOrderNumber);
                                                            cmdDet.Parameters.AddWithValue("@WareHouse_ID", WareHouse_ID);
                                                            cmdDet.Parameters.AddWithValue("@PurchaseOrder_ID", purchaseOrderID);
                                                            cmdDet.ExecuteScalar();
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    if (Obj.PartsOrderDetails.Count() == 0)
                                    {
                                        isJsonValid = false;
                                    }

                                    using (SqlCommand cmdDet = new SqlCommand("UP_AMERP_PURCHASE_ORDER_CANCELLATION_DETAILER_INSERT_INITIALIZE_3", conn, tran))
                                    {
                                        cmdDet.CommandType = CommandType.StoredProcedure; // Add this line!
                                        cmdDet.Parameters.AddWithValue("@PurchaseOrder_ID", purchaseOrderID);
                                        cmdDet.Parameters.AddWithValue("@OrderCancellation_ID", insertedId);
                                        cmdDet.ExecuteScalar();
                                    }
                                    if (inserted.Contains(true))
                                    {
                                        tran.Commit();
                                    }
                                    else
                                    {
                                        tran.Rollback();
                                    }
                                }
                            }
                            else
                            {
                                //string invalidColumnsMessage = string.Join(", ", invalidColumns2);
                                somePartsNotInserted = true;
                                someDetPartsNotInserted = true;
                                //Log the issue
                                Common.LogInsertStatus(logID,
                                $"The provided Purchase Order Cancellation header for Purchase Order Number: {Obj.PurchaseOrderNumber} does not contain a valid Prefix-Suffix.",
                                false, connString, apiCategory);

                                // Email subject and body
                                string emailSubject = "Purchase Order Cancellation Insertion Failed";
                                string emailBody = $"The Purchase Order Cancellation header for Purchase Order Number: {Obj.PurchaseOrderNumber} is missing a valid Prefix-Suffix.";

                                // Get the formatted email template
                                string emailTemplate = Common.GetEmailTemplatePurchaseOrderCancellation(
                                    partHeader, "Validation", fullUrl, false, Obj.PurchaseOrderNumber, emailBody);

                                // Insert the email log for notification
                                Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    somePartsNotInserted = true;
                    someDetPartsNotInserted = true;
                    tran.Rollback();
                    string errorMessage = $"Error in insertion of Purchase Order Cancellation with PurchaseOrderNumber: {Obj.PurchaseOrderNumber}. " +
                      $"Exception: {ex.Message}. StackTrace: {ex.StackTrace}";
                    Common.LogInsertStatus(logID,
                    errorMessage,
                                false, connString, apiCategory);
                    string emailSubject2 = $"Error in insertion of Purchase Order Cancellation with PurchaseOrderNumber: {Obj.PurchaseOrderNumber}. Row insertion failed.";
                    string emailBody2 = $"We encountered an error while attempting to insert the part with the following details into the system:\n\n" +
                   $"PurchaseOrderNumber: {Obj.PurchaseOrderNumber}\n" +

                   $"Error Message: {ex.Message}\n" +
                   $"Stack Trace: {ex.StackTrace}\n\n" +
                   $"Unfortunately, the row insertion failed due to an issue. Please check the provided details or contact support for further assistance.\n\n" +
                   $"If you have any further questions or need assistance, feel free to contact us.\n\n";

                    string emailTemplate = Common.GetEmailTemplatePurchaseOrderCancellation(partHeader, "Validation", fullUrl, false, Obj.PurchaseOrderNumber, emailBody2);
                    Common.InsertEmailLog(conn, emailSubject2, emailTemplate, Recipient, ccRecipient, tran);
                }
            }
            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    if (connection.State == ConnectionState.Closed || connection.State == ConnectionState.Broken)
                    {
                        connection.Open();
                    }
                    string updateQuery = @"
                    UPDATE SAPDMS_API_Request_Log
                    SET Status = @Status, ResponseMessage = @ResponseMessage
                    WHERE LogID = @LogID";

                    using (SqlCommand cmd = new SqlCommand(updateQuery, connection))
                    {
                        if (isJsonValid == false)
                        {
                            cmd.Parameters.AddWithValue("@Status", "Error");
                            cmd.Parameters.AddWithValue("@ResponseMessage", "Invalid Json.");
                            x = new
                            {
                                Message = "Invalid Json."
                            };
                        }
                        else
                         if (somePartsNotInserted && !someDetPartsNotInserted)
                        {
                            cmd.Parameters.AddWithValue("@Status", "Error");
                            cmd.Parameters.AddWithValue("@ResponseMessage", "Purchase Order Cancellaion header is invalid .");
                            x = new
                            {
                                Message = "Purchase Order Cancellaion header is invalid ."
                            };


                        }
                        else if (someDetPartsNotInserted && !somePartsNotInserted)
                        {
                            cmd.Parameters.AddWithValue("@Status", "Error");
                            cmd.Parameters.AddWithValue("@ResponseMessage", "Some or all Purchase Order Cancellation detail rows are invalid.");
                            x = new
                            {
                                Message = "Some or all Purchase Order Cancellation detail rows are invalid."
                            };
                        }
                        else if (somePartsNotInserted && someDetPartsNotInserted)
                        {
                            cmd.Parameters.AddWithValue("@Status", "Error");
                            cmd.Parameters.AddWithValue("@ResponseMessage", "Error in Insertion.");
                            x = new
                            {
                                Message = "Error in Insertion."
                            };
                        }
                        else
                        {
                            cmd.Parameters.AddWithValue("@Status", "Success");
                            cmd.Parameters.AddWithValue("@ResponseMessage", "Inserted Successfully!");
                            x = new
                            {
                                Message = "Inserted Successfully!"
                            };
                        }
                        cmd.Parameters.AddWithValue("@LogID", logID);
                        cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return new JsonResult(x);
        }
        #endregion

        #region PI
        /// <summary>
        /// DhanuShree/VINAY
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <param name="fullUrl"></param>
        /// <param name="clientIP"></param>
        /// <param name="Recipient"></param>
        /// <param name="ccRecipient"></param>
        /// <returns></returns>
        public static IActionResult PurchaseInvoiceInterface(PurchaseInvoiceInterfaceList Obj, string connString, int LogException, string fullUrl, string clientIP, string Recipient, string ccRecipient)
        {
            var Details = default(dynamic);
            string partHeader = "PurchaseInvoiceMaster";
            string apiEndpoint = fullUrl;
            string requestBody = JsonConvert.SerializeObject(Obj);

            int logID = 0;
            bool isRowValid = false;
            bool somePartsNotInserted = false;
            bool someDetPartsNotInserted = false;
            int StockChanged = 0;
            string apiCategory = "PurchaseInvoiceMaster";
            logID = Common.LogRequest(apiEndpoint, requestBody, clientIP, apiCategory, connString, LogException);
            int companyID = 0;
            int userid = 0;
            int branchID = 0;
            int supplierID = 0;
            int currencyID = 0;
            int supplierCount = 0;
            int modeOfShipmentID = 0;
            int taxStructureID = 0;
            List<StockLessPartDetails> Mainstockchangesparts = new List<StockLessPartDetails>();
            PRT_PurchaseInvoice headerRow = new PRT_PurchaseInvoice();

            bool isSupplierUnique = true;
            using (SqlConnection conn = new SqlConnection(connString))
            {

                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                {
                    conn.Open();
                }
                SqlTransaction tran = conn.BeginTransaction();
                try
                {
                    var columns = new Dictionary<string, bool>
                {
                    { "PurchaseInvoiceDate", true },
                    { "Party_Code", true },
                    { "Currency_Short_Name", true },
                    { "ExchangeRate", true },
                    { "SupplierInvoiceNumber", true },
                    { "SupplierInvoiceDate", true },
                    { "ModeOfShipment_Short_Name", true },
                    { "TotalInvoiceAmountInLocalCurrency", false },
                    { "LandingCostFactor", false },
                    { "Remarks", true },
                    { "TotalAmount", true },
                    { "DiscountPercentage", true },
                    { "DiscountAmount", true },
                    { "DiscountedAmount", true },
                    { "TaxStructure_Name", true },
                    { "TaxableOtherCharges", true },
                    { "TaxablePercentage", true },
                    { "TaxableOtherChargesAmount", true },
                    { "TaxOnTaxableOtherCharges", true },
                    { "TotalTaxableAmount", true },
                    { "TaxAmount", true },
                    { "NonTaxableOtherCharges", true },
                    { "NonTaxablePercentage", true },
                    { "NonTaxableOtherChargesAmount", true },
                    { "TotalInvoiceAmount", true },
                    { "FinancialYear", true },
                    { "BRANCH_SHORTNAME", true },
                    { "User_LognID", true },
                    { "Updated_Date", true },
                    { "Roundoff", true },
                    { "TotalPIAmount", true },
                    { "SAP_OrderNumber", true },
                    { "STATUS", true },
                    { "IsImport", true }
                };
                    JObject row = JObject.FromObject(new
                    {
                        PurchaseInvoiceDate = Obj.PurchaseInvoiceDate,
                        Party_Code = Obj.Party_Code,
                        Currency_Short_Name = Obj.Currency_Short_Name,
                        ExchangeRate = Obj.ExchangeRate,
                        SupplierInvoiceNumber = Obj.SupplierInvoiceNumber,
                        SupplierInvoiceDate = Obj.SupplierInvoiceDate,
                        ModeOfShipment_Short_Name = Obj.ModeOfShipment_Short_Name,
                        Remarks = Obj.Remarks,
                        TotalAmount = Obj.TotalAmount,
                        DiscountPercentage = Obj.DiscountPercentage,
                        DiscountAmount = Obj.DiscountAmount,
                        DiscountedAmount = Obj.DiscountedAmount,
                        TaxStructure_Name = Obj.TaxStructure_Name,
                        TaxableOtherCharges = Obj.TaxableOtherCharges,
                        TaxablePercentage = Obj.TaxablePercentage,
                        TaxableOtherChargesAmount = Obj.TaxableOtherChargesAmount,
                        TaxOnTaxableOtherCharges = Obj.TaxOnTaxableOtherCharges,
                        TotalTaxableAmount = Obj.TotalTaxableAmount,
                        TaxAmount = Obj.TaxAmount,
                        NonTaxableOtherCharges = Obj.NonTaxableOtherCharges,
                        NonTaxablePercentage = Obj.NonTaxablePercentage,
                        NonTaxableOtherChargesAmount = Obj.NonTaxableOtherChargesAmount,
                        TotalInvoiceAmount = Obj.TotalInvoiceAmount,
                        FinancialYear = Obj.FinancialYear,
                        BRANCH_SHORTNAME = Obj.BRANCH_SHORTNAME,
                        User_LognID = Obj.User_LognID,
                        Updated_Date = Obj.Updated_Date,
                        Roundoff = Obj.Roundoff,
                        TotalPIAmount = Obj.TotalPIAmount,
                        SAP_OrderNumber = Obj.SAP_OrderNumber,
                        STATUS = Obj.STATUS,
                        IsImport = Obj.IsImport
                    });
                    List<string> invalidColumns;
                    isRowValid = Common.ValidateAndLog(row, columns, out invalidColumns);
                    if (!isRowValid)
                    {
                        string invalidColumnsMessage = string.Join(", ", invalidColumns);
                        somePartsNotInserted = true;
                        Common.LogInsertStatus(logID,
                       $"The provided purchase invoice header with Supplier Invoice Number {Obj.SupplierInvoiceNumber} has null columns {invalidColumnsMessage}",
                       false, connString, apiCategory);
                        string emailSubject = $"Purchase Invoice Header with Supplier Invoice Number {Obj.SupplierInvoiceNumber} Insertion failed.";
                        string emailBody = $"The insertion of the purchase invoice header with Supplier Invoice Number '{Obj.SupplierInvoiceNumber}' has failed. " +
                        $"Missing columns: {invalidColumnsMessage}\n\n";

                        string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, false, emailBody);
                        Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);





                    }
                    else
                    {
                        string purchaseOrderNumbers = string.Join(",", Obj.PurchaseOrderDetails.Select(p => p.PurchaseOrderNumber));
                        using (SqlCommand cmd = new SqlCommand("UP_AMERP_PURCHASE_INVOICE_MASTER_INSERT_INITIALIZE_1", conn, tran))
                        {
                            cmd.CommandType = CommandType.StoredProcedure; // Add this line!
                            cmd.Parameters.AddWithValue("@Branch_ShortName", Obj.BRANCH_SHORTNAME);
                            cmd.Parameters.AddWithValue("@User_LognID", Obj.User_LognID);
                            cmd.Parameters.AddWithValue("@PartyCode", Obj.Party_Code);
                            cmd.Parameters.AddWithValue("@Currency_Short_Name", Obj.Currency_Short_Name);
                            cmd.Parameters.AddWithValue("@SupplierInvoiceNumber", Obj.SupplierInvoiceNumber);
                            cmd.Parameters.AddWithValue("@ModeOfShipment_Short_Name", Obj.ModeOfShipment_Short_Name);
                            cmd.Parameters.AddWithValue("@TaxStructure_Name", Obj.TaxStructure_Name);






                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.HasRows)
                                {
                                    while (reader.Read())
                                    {
                                        companyID = reader.IsDBNull(reader.GetOrdinal("Company_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Company_ID"));
                                        branchID = reader.IsDBNull(reader.GetOrdinal("Branch_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Branch_ID"));
                                        userid = reader.IsDBNull(reader.GetOrdinal("User_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("User_ID"));
                                        supplierID = reader.IsDBNull(reader.GetOrdinal("Supplier_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Supplier_ID"));
                                        currencyID = reader.IsDBNull(reader.GetOrdinal("CurrencyID")) ? 0 : reader.GetInt32(reader.GetOrdinal("CurrencyID"));
                                        supplierCount = reader.IsDBNull(reader.GetOrdinal("SupplierCount")) ? 0 : reader.GetInt32(reader.GetOrdinal("SupplierCount"));
                                        modeOfShipmentID = reader.IsDBNull(reader.GetOrdinal("ModeOfShipment_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("ModeOfShipment_ID"));
                                        taxStructureID = reader.IsDBNull(reader.GetOrdinal("TaxStructure_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("TaxStructure_ID"));

                                    }
                                }
                            }
                        }
                        if (supplierCount > 0)
                        {

                            somePartsNotInserted = true;
                            Common.LogInsertStatus(logID,
                           $"SupplierInvoiceNumber : {Obj.SupplierInvoiceNumber}already Exists",
                           false, connString, apiCategory);
                            string emailSubject = $"Purchase Invoice Header Insertion failed.";
                            string emailBody = $"SupplierInvoiceNumber : {Obj.SupplierInvoiceNumber}already Exists\"\n\n";
                            string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, false, emailBody);
                            Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                            isSupplierUnique = false;
                        }







                    }
                    if (isSupplierUnique)
                    {

                        if (isRowValid)
                        {
                            var columnsHeadIDs = new Dictionary<string, bool>
                        {
                            { "Company_ID", true },
                            { "Branch_ID", true },
                            { "User_ID", true },
                            { "Supplier_ID", true },
                            { "CurrencyID", true },
                            { "SupplierCount", false },
                            { "ModeOfShipment_ID", true },
                            { "TaxStructure_ID", true }
                        };
                            JObject rowHeadIDs = JObject.FromObject(new
                            {
                                Company_ID = companyID == 0 ? "" : companyID.ToString(),
                                Branch_ID = branchID == 0 ? "" : branchID.ToString(),
                                User_ID = userid == 0 ? "" : userid.ToString(),
                                Supplier_ID = supplierID == 0 ? "" : supplierID.ToString(),
                                CurrencyID = currencyID == 0 ? "" : currencyID.ToString(),
                                SupplierCount = supplierCount == 0 ? "" : supplierCount.ToString(),
                                ModeOfShipment_ID = modeOfShipmentID == 0 ? "" : modeOfShipmentID.ToString(),
                                TaxStructure_ID = taxStructureID == 0 ? "" : taxStructureID.ToString()
                            });
                            List<string> invalidColumns2;
                            isRowValid = Common.ValidateAndLog(rowHeadIDs, columnsHeadIDs, out invalidColumns2);
                            if (isRowValid)
                            {


                                headerRow.PurchaseInvoiceNumber = null;

                                headerRow.SupplierInvoiceNumber = Obj.SupplierInvoiceNumber;
                                headerRow.PurchaseInvoiceDate = Obj.PurchaseInvoiceDate;
                                headerRow.Supplier_ID = supplierID;
                                headerRow.Currency_ID = currencyID;
                                headerRow.ExchangeRate = Obj.ExchangeRate;
                                headerRow.SupplierInvoiceDate = Obj.SupplierInvoiceDate;
                                headerRow.IsImport = Obj.IsImport;
                                headerRow.ModeOfShipment_ID = modeOfShipmentID;
                                headerRow.TotalInvoiceAmountInLocalCurrency = Obj.TotalInvoiceAmountInLocalCurrency;
                                headerRow.LandingCostFactor = Obj.LandingCostFactor;
                                headerRow.Remarks = Obj.Remarks;
                                headerRow.TotalAmount = Obj.TotalAmount;
                                headerRow.DiscountPercentage = Obj.DiscountPercentage;
                                headerRow.DiscountAmount = Obj.DiscountAmount;
                                headerRow.DiscountedAmount = Obj.DiscountedAmount;
                                headerRow.TaxStructure_ID = taxStructureID;
                                headerRow.TaxableOtherCharges = Obj.TaxableOtherCharges;
                                headerRow.TaxablePercentage = Obj.TaxablePercentage;
                                headerRow.TaxableOtherChargesAmount = Obj.TaxableOtherChargesAmount;
                                headerRow.TaxOnTaxableOtherCharges = Obj.TaxableOtherChargesAmount;
                                headerRow.TotalTaxableAmount = Obj.TotalTaxableAmount;
                                headerRow.TaxAmount = Obj.TaxAmount;
                                headerRow.NonTaxableOtherCharges = Obj.NonTaxableOtherCharges;
                                headerRow.NonTaxablePercentage = Obj.NonTaxablePercentage;
                                headerRow.NonTaxableOtherChargesAmount = Obj.NonTaxableOtherChargesAmount;
                                headerRow.TotalInvoiceAmount = Obj.TotalInvoiceAmount;
                                headerRow.DocumentNumber = null;
                                headerRow.FinancialYear = Obj.FinancialYear;
                                headerRow.Company_ID = companyID;
                                headerRow.Branch_ID = branchID;
                                headerRow.Updated_By = userid;
                                headerRow.Updated_Date = Obj.Updated_Date;
                                headerRow.PurchaseGRN_ID = null;
                                headerRow.WareHouse_ID = null;
                                headerRow.PurchaseOrderClass_ID = null;
                                headerRow.ItemLevelTaxStructure_ID = taxStructureID;
                                headerRow.IsDealer = Obj.IsDealer;
                                headerRow.Roundoff = Obj.Roundoff;
                                headerRow.TotalPIAmount = Obj.TotalPIAmount;
                                headerRow.SAP_OrderNumber = Obj.SAP_OrderNumber;



                            }
                            else
                            {
                                string invalidColumnsMessage = string.Join(", ", invalidColumns2);
                                somePartsNotInserted = true;
                                Common.LogInsertStatus(logID,
                               $"The provided purchase invoice header with Supplier Invoice Number '{Obj.SupplierInvoiceNumber}' has invalid columns. {invalidColumnsMessage}",
                               false, connString, apiCategory);
                                string emailSubject = $"Purchase Invoice Header with Supplier Invoice Number '{Obj.SupplierInvoiceNumber}' Insertion failed.";
                                string emailBody = $"The insertion of the purchase invoice header with Supplier Invoice Number '{Obj.SupplierInvoiceNumber}' has failed. " +
                         $"Invalid columns: {invalidColumnsMessage}\n\n";
                                string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, false, emailBody);
                                Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                            }
                        }

                        bool isDetRowValid = false;
                        var columnsDet = new Dictionary<string, bool>
                        {
                            { "PurchaseOrderNumber", true },
                            { "Parts_PartPrefix", true },
                            { "Parts_PartsNumber", true },
                            { "Rate", false },
                            { "Quantity", false },
                            { "DiscountPercentage", true },
                            { "DiscountAmount", true },
                            { "TaxStructure_Name", true },
                            { "TaxAmount", true },
                            { "Amount", false },
                            { "LandingCostInLocalCurrency", false },
                            { "VarianceinRate", false },
                            { "DiscountedAmount", true },
                            { "MRP", true }
                        };
                        List<PRT_PurchaseInvoicePartsDetails> validRows = new List<PRT_PurchaseInvoicePartsDetails>();
                        foreach (var purDet in Obj.PurchaseOrderDetails)
                        {
                            JObject rowDet = JObject.FromObject(new
                            {
                                PurchaseOrderNumber = purDet.PurchaseOrderNumber,
                                Parts_PartPrefix = purDet.Parts_PartPrefix,
                                Parts_PartsNumber = purDet.Parts_PartsNumber,
                                Rate = purDet.Rate,
                                Quantity = purDet.Quantity,
                                DiscountPercentage = purDet.DiscountPercentage,
                                DiscountAmount = purDet.DiscountAmount,
                                TaxStructure_Name = purDet.TaxStructure_Name,
                                TaxAmount = purDet.TaxAmount,
                                Amount = purDet.Amount,
                                LandingCostInLocalCurrency = purDet.LandingCostInLocalCurrency,
                                VarianceinRate = purDet.VarianceinRate,
                                DiscountedAmount = purDet.DiscountedAmount,
                                MRP = purDet.MRP
                            });
                            List<string> invalidColumnsDet;
                            isDetRowValid = Common.ValidateAndLog(rowDet, columnsDet, out invalidColumnsDet);
                            if (!isDetRowValid)
                            {
                                string invalidColumnsMessage = string.Join(", ", invalidColumnsDet);
                                someDetPartsNotInserted = true;
                                Common.LogInsertStatus(logID,
                                 $"The provided purchase invoice detailer with purchase order number {purDet.PurchaseOrderNumber} has null columns. {invalidColumnsMessage}",
                                 false, connString, apiCategory);

                                string emailSubject = $"Purchase Invoice Detailer with purchase order number {purDet.PurchaseOrderNumber} insertion failed.";

                                string emailBody = $"Missing columns: {invalidColumnsMessage}\n\n";

                                string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, false, emailBody);

                                Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                                continue;
                            }
                            else
                            {

                                int purchaseOrderID = 0;
                                int purchaseOrderCount = 0;
                                int wareHouseID = 0;
                                int Part_ID = 0;
                                int TaxStructure_ID = 0;
                                using (SqlCommand cmd = new SqlCommand("UP_AMERP_PURCHASE_INVOICE_MASTER_INSERT_INITIALIZE_2", conn, tran))
                                {
                                    cmd.CommandType = CommandType.StoredProcedure; // Add this line!
                                    cmd.Parameters.AddWithValue("@PurchaseOrderNumber", purDet.PurchaseOrderNumber); // Replace with actual value
                                    cmd.Parameters.AddWithValue("@Parts_PartPrefix", purDet.Parts_PartPrefix);
                                    cmd.Parameters.AddWithValue("@Parts_PartsNumber", purDet.Parts_PartsNumber);
                                    cmd.Parameters.AddWithValue("@TaxStructure_Name", purDet.TaxStructure_Name);





                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        // Get column indices for the desired columns
                                        int purchaseOrderIDOrdinal = reader.GetOrdinal("PurchaseOrder_ID");
                                        int purchaseOrderCountOrdinal = reader.GetOrdinal("PurchaseOrderCount");
                                        int wareHouseIDOrdinal = reader.GetOrdinal("WareHouse_ID");
                                        int partIDOrdinal = reader.GetOrdinal("Part_ID");
                                        int taxStructureIDOrdinal = reader.GetOrdinal("TaxStructure_ID");

                                        // Read the data from the reader using the column indices
                                        if (reader.Read()) // Ensure that there is a row to read
                                        {

                                            purchaseOrderID = reader.IsDBNull(purchaseOrderIDOrdinal) ? 0 : reader.GetInt32(purchaseOrderIDOrdinal);
                                            purchaseOrderCount = reader.IsDBNull(purchaseOrderCountOrdinal) ? 0 : reader.GetInt32(purchaseOrderCountOrdinal);
                                            wareHouseID = reader.IsDBNull(wareHouseIDOrdinal) ? 0 : reader.GetInt32(wareHouseIDOrdinal);
                                            Part_ID = reader.IsDBNull(partIDOrdinal) ? 0 : reader.GetInt32(partIDOrdinal);
                                            TaxStructure_ID = reader.IsDBNull(taxStructureIDOrdinal) ? 0 : reader.GetInt32(taxStructureIDOrdinal);
                                        }

                                    }
                                }
                                if (purchaseOrderCount > 0)
                                {

                                    someDetPartsNotInserted = true;
                                    Common.LogInsertStatus(logID,
                               $"Purchase Invoice Already Created for  : {purDet.PurchaseOrderNumber}",
                               false, connString, apiCategory);
                                    string emailSubject = $"Purchase Invoice Detailer Insertion failed.";
                                    string emailBody = $"Purchase Invoice Already Created for  : {purDet.PurchaseOrderNumber}\"\n\n";
                                    string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, false, emailBody);
                                    Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                                    continue;
                                }
                                var columnsHeadIDs = new Dictionary<string, bool>
                            {
                                { "purchaseOrderID", true },

                                { "wareHouseID", true },
                                { "Part_ID", true },
                                { "TaxStructure_ID", true }

                            };

                                JObject rowHeadIDs = JObject.FromObject(new
                                {
                                    purchaseOrderID = purchaseOrderID == 0 ? "" : purchaseOrderID.ToString(),

                                    wareHouseID = wareHouseID == 0 ? "" : wareHouseID.ToString(),
                                    Part_ID = Part_ID == 0 ? "" : Part_ID.ToString(),
                                    TaxStructure_ID = TaxStructure_ID == 0 ? "" : TaxStructure_ID.ToString()

                                });
                                List<string> invalidColumns2;
                                isDetRowValid = Common.ValidateAndLog(rowHeadIDs, columnsHeadIDs, out invalidColumns2);
                                if (!isDetRowValid)
                                {
                                    string invalidColumnsMessage = string.Join(", ", invalidColumns2);
                                    someDetPartsNotInserted = true;
                                    Common.LogInsertStatus(logID,
                                 $"The provided purchase invoice detailer with purchase order number {purDet.PurchaseOrderNumber} has Invalid columns. {invalidColumnsMessage}",
                                 false, connString, apiCategory);

                                    string emailSubject = $"Purchase Invoice Detailer with purchase order number {purDet.PurchaseOrderNumber} insertion failed.";

                                    string emailBody = $"Invalid columns: {invalidColumnsMessage}\n\n";

                                    string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, false, emailBody);

                                    Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                                    continue;
                                }
                                else
                                {
                                    decimal? PendingPPO = 0;
                                    GNM_Parts Partsdetails = new GNM_Parts();
                                    PRT_PurchaseOrderPartsDetail orderdetail = new PRT_PurchaseOrderPartsDetail();

                                    string query = @"
                                    SELECT TOP 1 
                                       
                                        ApprovedQuantity, 
                                        InvoicedQuantity
                                       
                                    FROM PRT_PurchaseOrderPartsDetail 
                                    WHERE PurchaseOrder_ID = @PurchaseOrder_ID 
                                      AND Parts_ID = @Parts_ID";


                                    SqlCommand command = null;


                                    using (command = new SqlCommand(query, conn, tran))
                                    {
                                        command.CommandType = CommandType.Text;
                                        command.Parameters.AddWithValue("@PurchaseOrder_ID", purchaseOrderID);
                                        command.Parameters.AddWithValue("@Parts_ID", Part_ID);

                                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                        {
                                            conn.Open();
                                        }
                                        using (SqlDataReader reader = command.ExecuteReader())
                                        {
                                            // Check if a record exists
                                            if (reader.Read())
                                            {
                                                orderdetail = new PRT_PurchaseOrderPartsDetail
                                                {

                                                    ApprovedQuantity = reader["ApprovedQuantity"] as decimal?,
                                                    InvoicedQuantity = reader["InvoicedQuantity"] as decimal?

                                                };
                                            }
                                        }
                                    }

                                    string partquery = @"
                                    SELECT 
                                        Parts_ID, 
                                     
                                        Parts_PartPrefix, 
                                        Parts_PartsNumber, 
                                        Parts_PartsDescription
                                       
                                    FROM GNM_Parts 
                                    WHERE Parts_ID = @Parts_ID";



                                    using (SqlCommand command2 = new SqlCommand(partquery, conn, tran))
                                    {
                                        command2.CommandType = CommandType.Text;
                                        command2.Parameters.AddWithValue("@Parts_ID", Part_ID);


                                        using (SqlDataReader reader = command2.ExecuteReader())
                                        {
                                            // Check if a record exists
                                            if (reader.Read())
                                            {
                                                Partsdetails = new GNM_Parts
                                                {
                                                    Parts_ID = Convert.ToInt32(reader["Parts_ID"]),

                                                    Parts_PartPrefix = reader["Parts_PartPrefix"] as string,
                                                    Parts_PartsNumber = reader["Parts_PartsNumber"] as string,
                                                    Parts_PartsDescription = reader["Parts_PartsDescription"] as string,

                                                };
                                            }
                                        }
                                    }


                                    if (orderdetail != null)
                                    {
                                        PendingPPO = Convert.ToDecimal(orderdetail.ApprovedQuantity) - Convert.ToDecimal(orderdetail.InvoicedQuantity);
                                    }
                                    if (PendingPPO < Convert.ToDecimal(purDet.Quantity))
                                    {
                                        StockLessPartDetails stockchangesparts = new StockLessPartDetails();
                                        StockChanged = StockChanged + 1;
                                        stockchangesparts.Parts_ID = Part_ID;
                                        stockchangesparts.PartsPrefix = Partsdetails.Parts_PartPrefix;
                                        stockchangesparts.PartNumber = Partsdetails.Parts_PartsNumber;
                                        stockchangesparts.Parts = Partsdetails.Parts_PartsDescription;
                                        stockchangesparts.CurrentStock = Convert.ToDecimal(PendingPPO);
                                        stockchangesparts.Quantity = purDet.Quantity;
                                        Mainstockchangesparts.Add(stockchangesparts);
                                    }
                                    PRT_PurchaseInvoicePartsDetails purchaseInvoicePartsDetail = new PRT_PurchaseInvoicePartsDetails
                                    {
                                        PurchaseOrder_ID = purchaseOrderID,
                                        Parts_ID = Part_ID,
                                        WareHouse_ID = wareHouseID,
                                        TaxStructure_ID = TaxStructure_ID,
                                        Rate = purDet.Rate,
                                        Quantity = purDet.Quantity,
                                        DiscountPercentage = purDet.DiscountPercentage,
                                        DiscountAmount = purDet.DiscountAmount,
                                        TaxAmount = purDet.TaxAmount,
                                        Amount = purDet.Amount,
                                        LandingCostInLocalCurrency = purDet.LandingCostInLocalCurrency,
                                        VarianceinRate = purDet.VarianceinRate,
                                        DiscountedAmount = purDet.DiscountedAmount,
                                        MRP = purDet.MRP,
                                        PurchaseOrder_Number = purDet.PurchaseOrderNumber
                                    };

                                    headerRow.TotalAmount = headerRow.TotalAmount + purchaseInvoicePartsDetail.Amount;
                                    headerRow.DiscountedAmount = headerRow.DiscountedAmount + purchaseInvoicePartsDetail.TaxAmount;
                                    validRows.Add(purchaseInvoicePartsDetail);

                                }




                            }

                        }

                        if (StockChanged == 0)
                        {
                            if (Common.CheckPreffixSuffix(companyID, branchID, "PRT_PurchaseInvoice", connString, LogException))
                            {
                                string insertInvoiceQuery = @"
                                        INSERT INTO PRT_PurchaseInvoice 
                                        (
                                            PurchaseInvoiceNumber,
                                            PurchaseInvoiceDate,
                                            Supplier_ID,
                                            Currency_ID,
                                            ExchangeRate,
                                            SupplierInvoiceNumber,
                                            SupplierInvoiceDate,
                                            IsImport,
                                            ModeOfShipment_ID,
                                            TotalInvoiceAmountInLocalCurrency,
                                            LandingCostFactor,
                                            Remarks,
                                            TotalAmount,
                                            DiscountPercentage,
                                            DiscountAmount,
                                            DiscountedAmount,
                                            TaxStructure_ID,
                                            TaxableOtherCharges,
                                            TaxablePercentage,
                                            TaxableOtherChargesAmount,
                                            TaxOnTaxableOtherCharges,
                                            TotalTaxableAmount,
                                            TaxAmount,
                                            NonTaxableOtherCharges,
                                            NonTaxablePercentage,
                                            NonTaxableOtherChargesAmount,
                                            TotalInvoiceAmount,
                                            DocumentNumber,
                                            FinancialYear,
                                            Company_ID,
                                            Branch_ID,
                                            Updated_By,
                                            Updated_Date,
                                            PurchaseGRN_ID,
                                            WareHouse_ID,
                                            PurchaseOrderClass_ID,
                                            ItemLevelTaxStructure_ID,
                                            IsDealer,
                                            Roundoff,
                                            TotalPIAmount,
                                            SAP_OrderNumber
                                        )
                                        VALUES
                                        (
                                            @PurchaseInvoiceNumber,
                                            @PurchaseInvoiceDate,
                                            @Supplier_ID,
                                            @Currency_ID,
                                            @ExchangeRate,
                                            @SupplierInvoiceNumber,
                                            @SupplierInvoiceDate,
                                            @IsImport,
                                            @ModeOfShipment_ID,
                                            @TotalInvoiceAmountInLocalCurrency,
                                            @LandingCostFactor,
                                            @Remarks,
                                            @TotalAmount,
                                            @DiscountPercentage,
                                            @DiscountAmount,
                                            @DiscountedAmount,
                                            @TaxStructure_ID,
                                            @TaxableOtherCharges,
                                            @TaxablePercentage,
                                            @TaxableOtherChargesAmount,
                                            @TaxOnTaxableOtherCharges,
                                            @TotalTaxableAmount,
                                            @TaxAmount,
                                            @NonTaxableOtherCharges,
                                            @NonTaxablePercentage,
                                            @NonTaxableOtherChargesAmount,
                                            @TotalInvoiceAmount,
                                            @DocumentNumber,
                                            @FinancialYear,
                                            @Company_ID,
                                            @Branch_ID,
                                            @Updated_By,
                                            @Updated_Date,
                                            @PurchaseGRN_ID,
                                            @WareHouse_ID,
                                            @PurchaseOrderClass_ID,
                                            @ItemLevelTaxStructure_ID,
                                            @IsDealer,
                                            @Roundoff,
                                            @TotalPIAmount,
                                            @SAP_OrderNumber
                                        );
                                        SELECT SCOPE_IDENTITY();";
                                SqlCommand cmd = null;
                                using (cmd = new SqlCommand(insertInvoiceQuery, conn, tran))
                                {
                                    cmd.CommandType = CommandType.Text;
                                    cmd.Parameters.AddWithValue("@PurchaseInvoiceNumber", "");
                                    cmd.Parameters.AddWithValue("@PurchaseInvoiceDate", headerRow.PurchaseInvoiceDate);
                                    cmd.Parameters.AddWithValue("@Supplier_ID", headerRow.Supplier_ID);
                                    cmd.Parameters.AddWithValue("@Currency_ID", headerRow.Currency_ID);
                                    cmd.Parameters.AddWithValue("@ExchangeRate", headerRow.ExchangeRate);
                                    cmd.Parameters.AddWithValue("@SupplierInvoiceNumber", headerRow.SupplierInvoiceNumber ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@SupplierInvoiceDate", headerRow.SupplierInvoiceDate ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@IsImport", headerRow.IsImport);
                                    cmd.Parameters.AddWithValue("@ModeOfShipment_ID", headerRow.ModeOfShipment_ID ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@TotalInvoiceAmountInLocalCurrency", headerRow.TotalInvoiceAmountInLocalCurrency);
                                    cmd.Parameters.AddWithValue("@LandingCostFactor", headerRow.LandingCostFactor);
                                    cmd.Parameters.AddWithValue("@Remarks", headerRow.Remarks ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@TotalAmount", headerRow.TotalAmount ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@DiscountPercentage", headerRow.DiscountPercentage ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@DiscountAmount", headerRow.DiscountAmount ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@DiscountedAmount", headerRow.DiscountedAmount ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@TaxStructure_ID", headerRow.TaxStructure_ID ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@TaxableOtherCharges", headerRow.TaxableOtherCharges ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@TaxablePercentage", headerRow.TaxablePercentage ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@TaxableOtherChargesAmount", headerRow.TaxableOtherChargesAmount ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@TaxOnTaxableOtherCharges", headerRow.TaxOnTaxableOtherCharges ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@TotalTaxableAmount", headerRow.TotalTaxableAmount ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@TaxAmount", headerRow.TaxAmount ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@NonTaxableOtherCharges", headerRow.NonTaxableOtherCharges ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@NonTaxablePercentage", headerRow.NonTaxablePercentage ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@NonTaxableOtherChargesAmount", headerRow.NonTaxableOtherChargesAmount ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@TotalInvoiceAmount", headerRow.TotalInvoiceAmount ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@DocumentNumber", headerRow.DocumentNumber ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@FinancialYear", headerRow.FinancialYear);
                                    cmd.Parameters.AddWithValue("@Company_ID", headerRow.Company_ID);
                                    cmd.Parameters.AddWithValue("@Branch_ID", headerRow.Branch_ID);
                                    cmd.Parameters.AddWithValue("@Updated_By", headerRow.Updated_By);
                                    cmd.Parameters.AddWithValue("@Updated_Date", headerRow.Updated_Date);
                                    cmd.Parameters.AddWithValue("@PurchaseGRN_ID", headerRow.PurchaseGRN_ID ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@WareHouse_ID", headerRow.WareHouse_ID ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@PurchaseOrderClass_ID", headerRow.PurchaseOrderClass_ID ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@ItemLevelTaxStructure_ID", headerRow.ItemLevelTaxStructure_ID ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@IsDealer", headerRow.IsDealer ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@Roundoff", headerRow.Roundoff ?? (object)DBNull.Value);
                                    cmd.Parameters.AddWithValue("@TotalPIAmount", headerRow.TotalPIAmount);
                                    cmd.Parameters.AddWithValue("@SAP_OrderNumber", headerRow.SAP_OrderNumber);


                                    headerRow.PurchaseInvoice_ID = Convert.ToInt32(cmd.ExecuteScalar());
                                    Common.LogInsertStatus(logID,
                                               $"The provided purchase invoice header with supplier number '{headerRow.SupplierInvoiceNumber}' has been successfully inserted.Status:{Obj.STATUS}",
                                               true, connString, apiCategory);
                                    string emailSubject = $"The provided purchase invoice header with supplier number '{headerRow.SupplierInvoiceNumber}' has been successfully inserted.";
                                    string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, true, emailSubject);

                                    Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);

                                }
                                if (validRows != null && validRows.Count() > 0)
                                {
                                    string insertDetQuery = @"
                                                INSERT INTO PRT_PurchaseInvoicePartsDetails 
                                                (
                                                    PurchaseInvoice_ID,
                                                    PurchaseOrder_ID,
                                                    Parts_ID,
                                                    PartsOrder_ID,
                                                    Rate,
                                                    Quantity,
                                                    DiscountPercentage,
                                                    DiscountAmount,
                                                    TaxStructure_ID,
                                                    TaxAmount,
                                                    Amount,
                                                    LandingCostInLocalCurrency,
                                                    VarianceinRate,
                                                    DiscountedAmount,
                                                    WareHouse_ID,
                                                    MRP
                                                ) 
                                                VALUES 
                                                (
                                                    @PurchaseInvoice_ID,
                                                    @PurchaseOrder_ID,
                                                    @Parts_ID,
                                                    @PartsOrder_ID,
                                                    @Rate,
                                                    @Quantity,
                                                    @DiscountPercentage,
                                                    @DiscountAmount,
                                                    @TaxStructure_ID,
                                                    @TaxAmount,
                                                    @Amount,
                                                    @LandingCostInLocalCurrency,
                                                    @VarianceinRate,
                                                    @DiscountedAmount,
                                                    @WareHouse_ID,
                                                    @MRP
                                                )";
                                    SqlCommand cmd2;
                                    using (cmd2 = new SqlCommand(insertDetQuery, conn, tran))
                                    {
                                        foreach (var partDetail in validRows)
                                        {
                                            cmd2.Parameters.Clear(); // Clear previous parameters to avoid issues during the next iteration

                                            // Add parameters for the current partDetail
                                            cmd2.Parameters.AddWithValue("@PurchaseInvoice_ID", headerRow.PurchaseInvoice_ID);
                                            cmd2.Parameters.AddWithValue("@PurchaseOrder_ID", partDetail.PurchaseOrder_ID); // Handle nullable fields
                                            cmd2.Parameters.AddWithValue("@Parts_ID", partDetail.Parts_ID);
                                            cmd2.Parameters.AddWithValue("@PartsOrder_ID", partDetail.PartsOrder_ID ?? (object)DBNull.Value);
                                            cmd2.Parameters.AddWithValue("@Rate", partDetail.Rate);
                                            cmd2.Parameters.AddWithValue("@Quantity", partDetail.Quantity);
                                            cmd2.Parameters.AddWithValue("@DiscountPercentage", partDetail.DiscountPercentage ?? (object)DBNull.Value);
                                            cmd2.Parameters.AddWithValue("@DiscountAmount", partDetail.DiscountAmount ?? (object)DBNull.Value);
                                            cmd2.Parameters.AddWithValue("@TaxStructure_ID", partDetail.TaxStructure_ID ?? (object)DBNull.Value);
                                            cmd2.Parameters.AddWithValue("@TaxAmount", partDetail.TaxAmount ?? (object)DBNull.Value);
                                            cmd2.Parameters.AddWithValue("@Amount", partDetail.Amount);
                                            cmd2.Parameters.AddWithValue("@LandingCostInLocalCurrency", partDetail.LandingCostInLocalCurrency ?? (object)DBNull.Value);
                                            cmd2.Parameters.AddWithValue("@VarianceinRate", partDetail.VarianceinRate ?? (object)DBNull.Value);
                                            cmd2.Parameters.AddWithValue("@DiscountedAmount", partDetail.DiscountedAmount ?? (object)DBNull.Value);
                                            cmd2.Parameters.AddWithValue("@WareHouse_ID", partDetail.WareHouse_ID);
                                            cmd2.Parameters.AddWithValue("@MRP", partDetail.MRP ?? (object)DBNull.Value);


                                            int rowsAffected = cmd2.ExecuteNonQuery();
                                            if (rowsAffected > 0)
                                            {
                                                Common.LogInsertStatus(logID,
                                              $"The provided purchase invoice detailer with Purchase Order number '{partDetail.PurchaseOrder_Number}' has been successfully inserted.",
                                              true, connString, apiCategory);
                                                string emailSubject = $"The provided purchase invoice detailer with Purchase Order number '{partDetail.PurchaseOrder_Number}' has been successfully inserted.";
                                                string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, true, emailSubject);

                                                Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                                            }
                                        }

                                    }
                                }
                                SqlCommand command = null;
                                List<PRT_PurchaseInvoicePartsDetails> purchaseInvoicePartsAfterInsert = new List<PRT_PurchaseInvoicePartsDetails>();
                                string query = @"
                                        SELECT * 
                                        FROM PRT_PurchaseInvoicePartsDetails
                                        WHERE PurchaseInvoice_ID = @PurchaseInvoice_ID";
                                using (command = new SqlCommand(query, conn, tran))
                                {
                                    command.CommandType = CommandType.Text;
                                    command.Parameters.AddWithValue("@PurchaseInvoice_ID", headerRow.PurchaseInvoice_ID);


                                    using (SqlDataReader reader = command.ExecuteReader())
                                    {
                                        // Check if a record exists
                                        while (reader.Read())
                                        {
                                            var partDetail = new PRT_PurchaseInvoicePartsDetails
                                            {
                                                PurchaseInvoicePartsDetailID = Convert.ToInt32(reader["PurchaseInvoicePartsDetailID"]),
                                                PurchaseInvoice_ID = Convert.ToInt32(reader["PurchaseInvoice_ID"]),
                                                PurchaseOrder_ID = reader["PurchaseOrder_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["PurchaseOrder_ID"]),
                                                Parts_ID = Convert.ToInt32(reader["Parts_ID"]),
                                                PartsOrder_ID = reader["PartsOrder_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["PartsOrder_ID"]),
                                                Rate = Convert.ToDecimal(reader["Rate"]),
                                                Quantity = Convert.ToDecimal(reader["Quantity"]),
                                                DiscountPercentage = reader["DiscountPercentage"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["DiscountPercentage"]),
                                                DiscountAmount = reader["DiscountAmount"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["DiscountAmount"]),
                                                TaxStructure_ID = reader["TaxStructure_ID"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["TaxStructure_ID"]),
                                                TaxAmount = reader["TaxAmount"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["TaxAmount"]),
                                                Amount = Convert.ToDecimal(reader["Amount"]),
                                                LandingCostInLocalCurrency = reader["LandingCostInLocalCurrency"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["LandingCostInLocalCurrency"]),
                                                VarianceinRate = reader["VarianceinRate"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["VarianceinRate"]),
                                                DiscountedAmount = reader["DiscountedAmount"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["DiscountedAmount"]),
                                                WareHouse_ID = Convert.ToInt32(reader["WareHouse_ID"]),
                                                MRP = reader["MRP"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["MRP"])
                                            };
                                            purchaseInvoicePartsAfterInsert.Add(partDetail);
                                        }
                                    }
                                }

                                for (int i = 0; i < purchaseInvoicePartsAfterInsert.Count; i++)
                                {
                                    int purchaseOrderID = purchaseInvoicePartsAfterInsert[i].PurchaseOrder_ID != null ? purchaseInvoicePartsAfterInsert[i].PurchaseOrder_ID.Value : 0;


                                    AllocationLogicServices.UpdatePartsStock(purchaseInvoicePartsAfterInsert[i].Parts_ID, purchaseInvoicePartsAfterInsert[i].Quantity, (purchaseInvoicePartsAfterInsert[i].PurchaseOrder_ID != null ? purchaseInvoicePartsAfterInsert[i].PurchaseOrder_ID.Value : 0), purchaseInvoicePartsAfterInsert[i].WareHouse_ID, headerRow.PurchaseInvoiceDate, (purchaseInvoicePartsAfterInsert[i].LandingCostInLocalCurrency != null ? purchaseInvoicePartsAfterInsert[i].LandingCostInLocalCurrency.Value : 0), headerRow.Supplier_ID, companyID, branchID, connString, LogException);
                                    int PartId = purchaseInvoicePartsAfterInsert[i].Parts_ID;

                                }
                                tran.Commit();

                            }
                            else
                            {
                                someDetPartsNotInserted = true;
                                someDetPartsNotInserted = true;
                                Common.LogInsertStatus(logID,
                             $"Purchase Invoice Prefix-Suffix doesn't exist",
                             false, connString, apiCategory);

                                string emailSubject = $"Purchase Invoice insertion failed.";

                                string emailBody = $"Purchase Invoice Prefix-Suffix doesn't exist";

                                string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, false, emailBody);

                                Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                            }
                        }
                        else
                        {

                            someDetPartsNotInserted = true;
                            someDetPartsNotInserted = true;
                            Common.LogInsertStatus(logID,
                         $"Purchase Invoice Stock Changed",
                         false, connString, apiCategory);

                            string emailSubject = $"Purchase Invoice insertion failed.";

                            string emailBody = $"Purchase Invoice Stock Changed";

                            string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, false, emailBody);

                            Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                        }
                    }
                }
                catch (Exception ex)
                {
                    somePartsNotInserted = true;
                    someDetPartsNotInserted = true;
                    tran.Rollback();
                    string errorMessage = $"Error in insertion of Purchase Invoice with Supplier Invoice Number {Obj.SupplierInvoiceNumber}. " +
                      $"Exception: {ex.Message}. StackTrace: {ex.StackTrace}";
                    Common.LogInsertStatus(logID,
                    errorMessage,
                                false, connString, apiCategory);
                    string emailSubject2 = $"Error in insertion of Purchase Invoice with Supplier Invoice Number {Obj.SupplierInvoiceNumber}. ";
                    string emailBody2 = $"We encountered an error while attempting to insert the part with the following details into the system:\n\n" +
                   $"SupplierInvoiceNumber: {Obj.SupplierInvoiceNumber}\n" +

                   $"Error Message: {ex.Message}\n" +
                   $"Stack Trace: {ex.StackTrace}\n\n" +
                   $"Unfortunately, the row insertion failed due to an issue. Please check the provided details or contact support for further assistance.\n\n" +
                   $"If you have any further questions or need assistance, feel free to contact us.\n\n";

                    string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, false, emailBody2);
                    Common.InsertEmailLog(conn, emailSubject2, emailTemplate, Recipient, ccRecipient, tran);
                }




            }
            try
            {

                string updateQuery = @"
                UPDATE SAPDMS_API_Request_Log
                SET Status = @Status, ResponseMessage = @ResponseMessage
                WHERE LogID = @LogID";
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                    {
                        conn.Open();
                    }

                    using (SqlCommand cmd = new SqlCommand(updateQuery, conn))
                    {
                        if (somePartsNotInserted && !someDetPartsNotInserted)
                        {
                            cmd.Parameters.AddWithValue("@Status", "Error");
                            cmd.Parameters.AddWithValue("@ResponseMessage", "Purchase invoice header invalid .");
                            Details = new
                            {
                                Message = "Purchase invoice header invalid ."
                            };


                        }
                        else if (!somePartsNotInserted && someDetPartsNotInserted)
                        {
                            cmd.Parameters.AddWithValue("@Status", "Error");
                            cmd.Parameters.AddWithValue("@ResponseMessage", "Some or all purchase invoice detail entries are invalid.");
                            Details = new
                            {
                                Message = "Some or all purchase invoice detail entries are invalid."
                            };

                        }
                        else if (somePartsNotInserted && someDetPartsNotInserted)
                        {
                            cmd.Parameters.AddWithValue("@Status", "Error");
                            cmd.Parameters.AddWithValue("@ResponseMessage", "Error in Insertion.");
                            Details = new
                            {
                                Message = "Error in Insertion."
                            };
                        }
                        else
                        {
                            cmd.Parameters.AddWithValue("@Status", "Success");
                            cmd.Parameters.AddWithValue("@ResponseMessage", "Inserted Successfully!");
                            Details = new
                            {
                                Message = "Inserted Successfully!"
                            };




                        }

                        cmd.Parameters.AddWithValue("@LogID", logID);
                        cmd.ExecuteNonQuery();

                    }
                }

            }
            catch (Exception ex)
            {

            }




            return new JsonResult(Details);
        }
        #endregion



        //NEW METHOD
        public static IActionResult PI_Interface(PI_InterfaceList Obj, string connString, int LogException, string fullUrl, string clientIP, string Recipient, string ccRecipient)
        {
            bool somePartsNotInserted = false;
            string partHeader = "PurchaseInvoiceMaster";
            string apiEndpoint = fullUrl;
            string requestBody = JsonConvert.SerializeObject(Obj);
            var det = default(dynamic);
            List<string> detMsgList = new List<string>();
            int logID = 0;
            bool isJsonValid = false;
            List<string> poCount = new List<string>();

            // Group all parts by YanmarInvoiceNumber
            var invoiceGroups = Obj.piList.GroupBy(x => x.YanmarInvoiceNumber).ToList();

            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                    {
                        conn.Open();
                    }
                    SqlTransaction tran = conn.BeginTransaction();
                    string apiCategory = "PurchaseInvoiceMaster";
                    logID = Common.LogRequest(apiEndpoint, requestBody, clientIP, apiCategory, connString, LogException);

                    foreach (var invoiceGroup in invoiceGroups)
                    {
                        string yanmarInvoiceNumber = invoiceGroup.Key;
                        var partsForInvoice = invoiceGroup.ToList();
                        var firstPart = partsForInvoice.First();

                        JObject detailRow = new JObject
                {
                    { "DMSPurchaseOrderNumber", firstPart.DMSPurchaseOrderNumber },
                    { "DiscountPercentage", firstPart.DiscountPercentage },
                    { "DiscountAmount", firstPart.DiscountAmount },
                    { "PartNumber", firstPart.PartNumber},
                    { "InvoiceQty", firstPart.InvoiceQty},
                    { "InvoiceRate", firstPart.InvoiceRate },
                    { "TaxAmount", firstPart.TaxAmount },
                    { "YanmarInvoiceNumber", firstPart.YanmarInvoiceNumber },
                    { "YanmarInvoiceDate" ,firstPart.YanmarInvoiceDate },
                    { "CurrencyValue" ,firstPart.CurrencyValue },
                    { "SAP_SalesOrderNumber",firstPart.SAP_SalesOrderNumber },
                    { "Remarks", firstPart.Remarks }
                };
                        var columns2 = new Dictionary<string, bool>
                {
                    { "YanmarInvoiceNumber", true },
                    { "YanmarInvoiceDate", false },
                    { "CurrencyValue", true },
                    { "SAP_SalesOrderNumber", true },
                    { "Remarks", false },
                    { "DMSPurchaseOrderNumber", true },
                    { "DiscountPercentage", false },
                    { "DiscountAmount", false },
                    { "PartNumber", true },
                    { "InvoiceQty", true },
                    { "InvoiceRate", true },
                    { "TaxAmount", false }
                };
                        List<string> invalidHeaderColumns4;
                        bool isHeaderValid3 = Common.ValidateAndLog(detailRow, columns2, out invalidHeaderColumns4);
                        if (!isHeaderValid3)
                        {
                            string invalidColumnsMessage = string.Join(", ", invalidHeaderColumns4);
                            somePartsNotInserted = true;
                            Common.LogInsertStatus(logID,
                                $"The provided purchase invoice with Supplier Invoice Number {firstPart.YanmarInvoiceNumber} and Purchase Order Number {firstPart.DMSPurchaseOrderNumber} has null columns: {invalidColumnsMessage}",
                                false, connString, apiCategory);
                            string emailSubject = $"Purchase Invoice with Supplier Invoice Number {firstPart.YanmarInvoiceNumber} Insertion failed.";
                            string emailBody = $"The insertion of the purchase invoice with Supplier Invoice Number '{firstPart.YanmarInvoiceNumber}' and with Purchase Order Number {firstPart.DMSPurchaseOrderNumber} has failed. " +
                                $"Missing columns: {invalidColumnsMessage}\n\n";
                            string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, false, emailBody);
                            Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                            detMsgList.Add(emailBody);
                            continue;
                        }

                        // Prepare header row
                        PRT_PurchaseInvoice headerrow = new PRT_PurchaseInvoice();
                        decimal TotalInvoiceAmount = 0.0M;
                        headerrow.PRT_PurchaseInvoicePartsDetails = new List<PRT_PurchaseInvoicePartsDetails>();

                        // Get order for the first part (for header fields)
                        PRT_PurchaseOrder orderHeader = null;
                        string queryHeader = "SELECT TOP 1 * FROM PRT_PurchaseOrder WHERE PurchaseOrderNumber=@PurchaseOrderNumber";
                        using (SqlCommand cmd = new SqlCommand(queryHeader, conn, tran))
                        {
                            cmd.Parameters.AddWithValue("@PurchaseOrderNumber", firstPart.DMSPurchaseOrderNumber);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    orderHeader = new PRT_PurchaseOrder
                                    {
                                        Company_ID = reader.IsDBNull(reader.GetOrdinal("Company_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                        Branch_ID = reader.IsDBNull(reader.GetOrdinal("Branch_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Branch_ID")),
                                        PurchaseOrder_ID = reader.GetInt32(reader.GetOrdinal("PurchaseOrder_ID")),
                                        PurchaseOrderNumber = reader.GetString(reader.GetOrdinal("PurchaseOrderNumber")),
                                        PurchaseOrderVersion = reader.GetByte(reader.GetOrdinal("PurchaseOrderVersion")),
                                        PurchaseOrderDate = reader.GetDateTime(reader.GetOrdinal("PurchaseOrderDate")),
                                        TypeofPurchase_ID = reader.GetInt32(reader.GetOrdinal("TypeofPurchase_ID")),
                                        Supplier_ID = reader.GetInt32(reader.GetOrdinal("Supplier_ID")),
                                        PurchaseOrderClass_ID = reader.IsDBNull(reader.GetOrdinal("PurchaseOrderClass_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("PurchaseOrderClass_ID")),
                                        InvoiceAddress_ID = reader.GetInt32(reader.GetOrdinal("InvoiceAddress_ID")),
                                        ConsigneeAddress_ID = reader.GetInt32(reader.GetOrdinal("ConsigneeAddress_ID")),
                                        PaymentTerms = reader.GetString(reader.GetOrdinal("PaymentTerms")),
                                        Brand_ID = reader.IsDBNull(reader.GetOrdinal("Brand_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Brand_ID")),
                                        ProductType_ID = reader.IsDBNull(reader.GetOrdinal("ProductType_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ProductType_ID")),
                                        Model_ID = reader.IsDBNull(reader.GetOrdinal("Model_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                        SerialNumber = reader.GetString(reader.GetOrdinal("SerialNumber")),
                                        TotalOrderAmount = reader.GetDecimal(reader.GetOrdinal("TotalOrderAmount")),
                                        Remarks = reader.IsDBNull(reader.GetOrdinal("Remarks")) ? null : reader.GetString(reader.GetOrdinal("Remarks")),
                                        EnableVersion = reader.GetBoolean(reader.GetOrdinal("EnableVersion")),
                                        IsArchived = reader.GetBoolean(reader.GetOrdinal("IsArchived")),
                                        FinancialYear = reader.GetInt32(reader.GetOrdinal("FinancialYear")),
                                        Updated_by = reader.GetInt32(reader.GetOrdinal("Updated_by")),
                                        Updated_Date = reader.GetDateTime(reader.GetOrdinal("Updated_Date")),
                                        Expected_Delivery_Date = reader.IsDBNull(reader.GetOrdinal("Expected_Delivery_Date")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("Expected_Delivery_Date")),
                                        DocumentNumber = reader.IsDBNull(reader.GetOrdinal("DocumentNumber")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("DocumentNumber")),
                                        DiscountPercentage = reader.IsDBNull(reader.GetOrdinal("DiscountPercentage")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("DiscountPercentage")),
                                        DiscountAmount = reader.IsDBNull(reader.GetOrdinal("DiscountAmount")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("DiscountAmount")),
                                        DiscountedAmount = reader.IsDBNull(reader.GetOrdinal("DiscountedAmount")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("DiscountedAmount")),
                                        TaxStructure_ID = reader.IsDBNull(reader.GetOrdinal("TaxStructure_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("TaxStructure_ID")),
                                        TotalTaxableAmount = reader.IsDBNull(reader.GetOrdinal("TotalTaxableAmount")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("TotalTaxableAmount")),
                                        TaxAmount = reader.IsDBNull(reader.GetOrdinal("TaxAmount")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("TaxAmount")),
                                        PurchaseOrderStatus_ID = reader.GetInt32(reader.GetOrdinal("PurchaseOrderStatus_ID")),
                                        WareHouse_ID = reader.IsDBNull(reader.GetOrdinal("WareHouse_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WareHouse_ID")),
                                        POStatus_ID = reader.GetInt32(reader.GetOrdinal("POStatus_ID")),
                                        ModeOfShipment_ID = reader.IsDBNull(reader.GetOrdinal("ModeOfShipment_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ModeOfShipment_ID")),
                                        SUPPLIERQUOTATION_ID = reader.IsDBNull(reader.GetOrdinal("SUPPLIERQUOTATION_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("SUPPLIERQUOTATION_ID")),
                                        AttachmentCount = reader.IsDBNull(reader.GetOrdinal("AttachmentCount")) ? (byte?)null : reader.GetByte(reader.GetOrdinal("AttachmentCount")),
                                        IsUnderBreakDown = reader.IsDBNull(reader.GetOrdinal("IsUnderBreakDown")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsUnderBreakDown")),
                                        IsDealer = reader.IsDBNull(reader.GetOrdinal("IsDealer")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsDealer")),
                                        TallyGeneratedDate = reader.IsDBNull(reader.GetOrdinal("TallyGeneratedDate")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("TallyGeneratedDate")),
                                        TallySentStatus = reader.IsDBNull(reader.GetOrdinal("TallySentStatus")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("TallySentStatus")),
                                        IncoTerms = reader.IsDBNull(reader.GetOrdinal("IncoTerms")) ? null : reader.GetString(reader.GetOrdinal("IncoTerms"))
                                    };
                                }
                            }
                        }
                        if (orderHeader == null)
                        {
                            somePartsNotInserted = true;
                            Common.LogInsertStatus(logID,
                                $"The provided purchase invoice data with Supplier Invoice number {firstPart.YanmarInvoiceNumber} has invalid Purchase Order Number {firstPart.DMSPurchaseOrderNumber}",
                                false, connString, apiCategory);
                            string emailSubject = $"Purchase Invoice with Purchase Order Number {firstPart.DMSPurchaseOrderNumber} and Supplier Invoice number {firstPart.YanmarInvoiceNumber} Insertion failed.";
                            string emailBody = $"The provided purchase invoice data with Supplier Invoice number {firstPart.YanmarInvoiceNumber} has invalid Purchase Order Number {firstPart.DMSPurchaseOrderNumber}";
                            string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, false, emailBody);
                            detMsgList.Add(emailBody);
                            Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                            continue;
                        }

                        // Fill headerrow fields (lookups as in your original code)
                        string currencyname = firstPart.CurrencyValue;
                        int SupplierID = orderHeader.Supplier_ID;
                        bool isImport = false;
                        int currencyID = 0;
                        string queryIsImport = "SELECT TOP 1 IsImportExport FROM GNM_Party WHERE Party_ID = @SupplierID";
                        using (SqlCommand cmd = new SqlCommand(queryIsImport, conn, tran))
                        {
                            cmd.Parameters.AddWithValue("@SupplierID", SupplierID);
                            object result = cmd.ExecuteScalar();
                            if (result != null && result != DBNull.Value)
                                isImport = Convert.ToBoolean(result);
                        }
                        string queryCurrencyID = "SELECT TOP 1 RefMasterDetail_ID FROM GNM_RefMasterDetail WHERE RefMasterDetail_Short_Name = @CurrencyName";
                        using (SqlCommand cmd = new SqlCommand(queryCurrencyID, conn, tran))
                        {
                            cmd.Parameters.AddWithValue("@CurrencyName", currencyname);
                            object result = cmd.ExecuteScalar();
                            if (result != null && result != DBNull.Value)
                                currencyID = Convert.ToInt32(result);
                        }
                        string queryUser = "SELECT TOP 1 User_ID FROM GNM_User WHERE User_LoginID = @User_LoginID";
                        int userID = 0;
                        using (SqlCommand cmd = new SqlCommand(queryUser, conn, tran))
                        {
                            cmd.Parameters.AddWithValue("@User_LoginID", "quest_admin");
                            object result = cmd.ExecuteScalar();
                            if (result != null && result != DBNull.Value)
                                userID = Convert.ToInt32(result);
                        }
                        headerrow.Currency_ID = currencyID;
                        headerrow.Company_ID = orderHeader.Company_ID;
                        headerrow.Branch_ID = orderHeader.Branch_ID;
                        headerrow.ExchangeRate = 1;
                        headerrow.SupplierInvoiceNumber = firstPart.YanmarInvoiceNumber;
                        headerrow.SupplierInvoiceDate = Convert.ToDateTime(firstPart.YanmarInvoiceDate);
                        headerrow.Updated_By = userID;
                        headerrow.Updated_Date = DateTime.Now;
                        headerrow.IsImport = isImport;
                        headerrow.Remarks = firstPart.Remarks;
                        headerrow.IsDealer = orderHeader.IsDealer;
                        headerrow.SAP_OrderNumber = firstPart.SAP_SalesOrderNumber;
                        headerrow.ModeOfShipment_ID = orderHeader.ModeOfShipment_ID;
                        headerrow.PurchaseOrderClass_ID = orderHeader.PurchaseOrderClass_ID;
                        headerrow.Supplier_ID = orderHeader.Supplier_ID;

                        // Loop through all parts for this invoice
                        foreach (var part in partsForInvoice)
                        {
                            // Fetch order for each part (for PO ID, etc.)
                            PRT_PurchaseOrder order = null;
                            string query = "SELECT TOP 1 * FROM PRT_PurchaseOrder  WHERE PurchaseOrderNumber=@PurchaseOrderNumber";
                            using (SqlCommand cmd = new SqlCommand(query, conn, tran))
                            {
                                cmd.Parameters.AddWithValue("@PurchaseOrderNumber", part.DMSPurchaseOrderNumber);
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        order = new PRT_PurchaseOrder
                                        {
                                            PurchaseOrder_ID = reader.GetInt32(reader.GetOrdinal("PurchaseOrder_ID")),
                                            WareHouse_ID = reader.IsDBNull(reader.GetOrdinal("WareHouse_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WareHouse_ID")),
                                            Company_ID = reader.IsDBNull(reader.GetOrdinal("Company_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                            Branch_ID = reader.IsDBNull(reader.GetOrdinal("Branch_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Branch_ID")),
                                            Supplier_ID = reader.GetInt32(reader.GetOrdinal("Supplier_ID"))
                                        };
                                    }
                                }
                            }
                            if (order == null)
                                continue;

                            // Check for duplicate (SupplierInvoiceNumber, PurchaseOrder_ID)
                            int purchaseOrderCount = 0;
                            string queryPOExists = @"SELECT COUNT(*) FROM PRT_PurchaseInvoice A JOIN PRT_PurchaseInvoicePartsDetails B on A.PurchaseInvoice_ID=B.PurchaseInvoice_ID WHERE SupplierInvoiceNumber = @supinvicenum AND PurchaseOrder_ID = @purchaseOrderID";
                            using (SqlCommand cmd = new SqlCommand(queryPOExists, conn, tran))
                            {
                                cmd.Parameters.AddWithValue("@supinvicenum", part.YanmarInvoiceNumber);
                                cmd.Parameters.AddWithValue("@purchaseOrderID", order.PurchaseOrder_ID);
                                object result = cmd.ExecuteScalar();
                                if (result != null && result != DBNull.Value)
                                    purchaseOrderCount = Convert.ToInt32(result);
                            }
                            if (purchaseOrderCount > 0)
                                continue;

                            // Get part master
                            int partsID = 0;
                            GNM_Parts Partsmasterdata = new GNM_Parts();
                            string queryParts = @"SELECT TOP 1 * FROM GNM_Parts WHERE Parts_PartsNumber = @PartNumber";
                            using (SqlCommand cmd = new SqlCommand(queryParts, conn, tran))
                            {
                                cmd.Parameters.AddWithValue("@PartNumber", part.PartNumber);
                                using (SqlDataAdapter adapter = new SqlDataAdapter(cmd))
                                {
                                    DataTable partDataTable = new DataTable();
                                    adapter.Fill(partDataTable);
                                    if (partDataTable.Rows.Count > 0)
                                    {
                                        DataRow row = partDataTable.Rows[0];
                                        partsID = Convert.ToInt32(row["Parts_ID"]);
                                        Partsmasterdata = new GNM_Parts
                                        {
                                            Parts_ID = Convert.ToInt32(row["Parts_ID"]),
                                            Parts_PartPrefix = row["Parts_PartPrefix"].ToString(),
                                            Parts_PartsNumber = row["Parts_PartsNumber"].ToString(),
                                            ExciseDuty_ID = row["ExciseDuty_ID"] != DBNull.Value ? Convert.ToInt32(row["ExciseDuty_ID"]) : (int?)null,
                                        };
                                    }
                                }
                            }
                            if (partsID == 0)
                                continue;

                            // Get PO part detail
                            PRT_PurchaseOrderPartsDetail purchasepart = new PRT_PurchaseOrderPartsDetail();
                            string queryPurchaseDet = @"SELECT TOP 1 * FROM PRT_PurchaseOrderPartsDetail WHERE PurchaseOrder_ID = @PurchaseOrderID AND Parts_ID = @PartsID";
                            using (SqlCommand cmd = new SqlCommand(queryPurchaseDet, conn, tran))
                            {
                                cmd.Parameters.AddWithValue("@PurchaseOrderID", order.PurchaseOrder_ID);
                                cmd.Parameters.AddWithValue("@PartsID", partsID);
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        purchasepart = new PRT_PurchaseOrderPartsDetail
                                        {
                                            PurchaseOrderPartsDetail_ID = Convert.ToInt32(reader["PurchaseOrderPartsDetail_ID"]),
                                            PurchaseOrder_ID = Convert.ToInt32(reader["PurchaseOrder_ID"]),
                                            Parts_ID = Convert.ToInt32(reader["Parts_ID"]),
                                            SupplierPrice = Convert.ToDecimal(reader["SupplierPrice"]),
                                            RequestedQuantity = reader["RequestedQuantity"] != DBNull.Value ? Convert.ToDecimal(reader["RequestedQuantity"]) : (decimal?)null,
                                            ApprovedQuantity = reader["ApprovedQuantity"] != DBNull.Value ? Convert.ToDecimal(reader["ApprovedQuantity"]) : (decimal?)null,
                                            InvoicedQuantity = reader["InvoicedQuantity"] != DBNull.Value ? Convert.ToDecimal(reader["InvoicedQuantity"]) : (decimal?)null,
                                            BackOrderCancelledQuantity = reader["BackOrderCancelledQuantity"] != DBNull.Value ? Convert.ToDecimal(reader["BackOrderCancelledQuantity"]) : (decimal?)null,
                                            DiscountPercentage = reader["DiscountPercentage"] != DBNull.Value ? Convert.ToDecimal(reader["DiscountPercentage"]) : (decimal?)null,
                                            DiscountAmount = reader["DiscountAmount"] != DBNull.Value ? Convert.ToDecimal(reader["DiscountAmount"]) : (decimal?)null,
                                            TaxStructure_ID = reader["TaxStructure_ID"] != DBNull.Value ? Convert.ToInt32(reader["TaxStructure_ID"]) : (int?)null,
                                            TaxAmount = reader["TaxAmount"] != DBNull.Value ? Convert.ToDecimal(reader["TaxAmount"]) : (decimal?)null,
                                            Amount = Convert.ToDecimal(reader["Amount"]),
                                            PartsOrder_ID = reader["PartsOrder_ID"] != DBNull.Value ? Convert.ToInt32(reader["PartsOrder_ID"]) : (int?)null,
                                            DiscountedAmount = reader["DiscountedAmount"] != DBNull.Value ? Convert.ToDecimal(reader["DiscountedAmount"]) : (decimal?)null,
                                            MRP = reader["MRP"] != DBNull.Value ? Convert.ToDecimal(reader["MRP"]) : (decimal?)null
                                        };
                                    }
                                }
                            }
                            if (purchasepart.PurchaseOrderPartsDetail_ID == 0)
                                continue;

                            // Build part detail
                            PRT_PurchaseInvoicePartsDetails partdata = new PRT_PurchaseInvoicePartsDetails();
                            partdata.Parts_ID = partsID;
                            partdata.PurchaseInvoice_ID = headerrow.PurchaseInvoice_ID;
                            partdata.PurchaseOrder_ID = order.PurchaseOrder_ID;
                            partdata.Rate = part.InvoiceRate;
                            partdata.Quantity = part.InvoiceQty;
                            if (part.DiscountAmount.HasValue && part.DiscountPercentage.HasValue)
                            {
                                decimal expectedDiscountAmount = (partdata.Rate * partdata.Quantity) * (part.DiscountPercentage.Value / 100);
                                partdata.DiscountAmount = part.DiscountAmount;
                                partdata.DiscountPercentage = part.DiscountPercentage;
                            }
                            else if (part.DiscountAmount.HasValue)
                            {
                                partdata.DiscountAmount = part.DiscountAmount;
                                partdata.DiscountPercentage = 0;
                            }
                            else if (part.DiscountPercentage.HasValue)
                            {
                                partdata.DiscountPercentage = part.DiscountPercentage;
                                partdata.DiscountAmount = Math.Round((partdata.Rate * partdata.Quantity) * (partdata.DiscountPercentage.GetValueOrDefault() / 100), 2);
                            }
                            else
                            {
                                partdata.DiscountAmount = 0m;
                                partdata.DiscountPercentage = 0m;
                            }
                            partdata.DiscountedAmount = Math.Round((partdata.Rate * partdata.Quantity) - partdata.DiscountAmount.GetValueOrDefault(), 2);
                            partdata.MRP = purchasepart.MRP;
                            partdata.TaxStructure_ID = purchasepart.TaxStructure_ID;
                            if (part.TaxAmount == null || part.TaxAmount == 0)
                            {
                                decimal? TaxAmount = (purchasepart.TaxAmount / purchasepart.ApprovedQuantity) * partdata.Quantity;
                                partdata.TaxAmount = TaxAmount;
                                partdata.Amount = Convert.ToDecimal(TaxAmount) + (partdata.Rate * partdata.Quantity) - partdata.DiscountAmount.GetValueOrDefault();
                                partdata.LandingCostInLocalCurrency = TaxAmount + (partdata.Rate * partdata.Quantity) - partdata.DiscountAmount.GetValueOrDefault();
                            }
                            else
                            {
                                partdata.TaxAmount = part.TaxAmount;
                                partdata.Amount = part.TaxAmount + (partdata.Rate * partdata.Quantity) - partdata.DiscountAmount.GetValueOrDefault();
                                partdata.LandingCostInLocalCurrency = part.TaxAmount + (partdata.Rate * partdata.Quantity) - partdata.DiscountAmount.GetValueOrDefault();
                            }
                            partdata.WareHouse_ID = Convert.ToInt32(order.WareHouse_ID);
                            partdata.TaxStructure_ID = GetTaxStructureForPart(Partsmasterdata.ExciseDuty_ID, 0, Partsmasterdata.Parts_ID, SupplierID, Convert.ToInt32(order.Company_ID), Convert.ToInt32(order.Branch_ID), connString).FirstOrDefault(k => k.IsDefault) == null ? 0 : GetTaxStructureForPart(Partsmasterdata.ExciseDuty_ID, 0, Partsmasterdata.Parts_ID, SupplierID, Convert.ToInt32(order.Company_ID), Convert.ToInt32(order.Branch_ID), connString).FirstOrDefault(k => k.IsDefault).TaxStructure_ID;
                            TotalInvoiceAmount = TotalInvoiceAmount + partdata.Amount;
                            headerrow.PRT_PurchaseInvoicePartsDetails.Add(partdata);
                        }

                        // Insert header and all parts at once
                        if (headerrow.PRT_PurchaseInvoicePartsDetails.Count > 0)
                        {
                            decimal roundoffamount = TotalInvoiceAmount - Math.Round(TotalInvoiceAmount);
                            headerrow.Roundoff = roundoffamount;
                            headerrow.PurchaseInvoiceNumber = "";
                            headerrow.PurchaseInvoiceDate = DateTime.Now;
                            headerrow.Supplier_ID = orderHeader.Supplier_ID;
                            headerrow.TotalInvoiceAmount = TotalInvoiceAmount;
                            headerrow.TotalAmount = TotalInvoiceAmount;
                            headerrow.TotalInvoiceAmountInLocalCurrency = TotalInvoiceAmount;
                            headerrow.TotalTaxableAmount = TotalInvoiceAmount;
                            headerrow.DiscountPercentage = 0;
                            headerrow.DiscountAmount = 0;
                            headerrow.DiscountedAmount = 0;
                            headerrow.InvoiceAddressID = orderHeader.InvoiceAddress_ID;
                            headerrow.ConsigneeAddressID = orderHeader.ConsigneeAddress_ID;
                            headerrow.WareHouse_ID = orderHeader.WareHouse_ID;
                            headerrow.TotalPIAmount = Math.Round(TotalInvoiceAmount);

                            string insertInvoiceQuery = @"
                        INSERT INTO PRT_PurchaseInvoice 
                        (
                            PurchaseInvoiceNumber,
                            PurchaseInvoiceDate,
                            Supplier_ID,
                            Currency_ID,
                            ExchangeRate,
                            SupplierInvoiceNumber,
                            SupplierInvoiceDate,
                            IsImport,
                            ModeOfShipment_ID,
                            TotalInvoiceAmountInLocalCurrency,
                            LandingCostFactor,
                            Remarks,
                            TotalAmount,
                            DiscountPercentage,
                            DiscountAmount,
                            DiscountedAmount,
                            TaxStructure_ID,
                            TaxableOtherCharges,
                            TaxablePercentage,
                            TaxableOtherChargesAmount,
                            TaxOnTaxableOtherCharges,
                            TotalTaxableAmount,
                            TaxAmount,
                            NonTaxableOtherCharges,
                            NonTaxablePercentage,
                            NonTaxableOtherChargesAmount,
                            TotalInvoiceAmount,
                            DocumentNumber,
                            FinancialYear,
                            Company_ID,
                            Branch_ID,
                            Updated_By,
                            Updated_Date,
                            PurchaseGRN_ID,
                            WareHouse_ID,
                            PurchaseOrderClass_ID,
                            ItemLevelTaxStructure_ID,
                            IsDealer,
                            Roundoff,
                            TotalPIAmount,
                            SAP_OrderNumber,
                            InvoiceAddress_ID,
                            ConsigneeAddress_ID
                        )
                        VALUES
                        (
                            @PurchaseInvoiceNumber,
                            @PurchaseInvoiceDate,
                            @Supplier_ID,
                            @Currency_ID,
                            @ExchangeRate,
                            @SupplierInvoiceNumber,
                            @SupplierInvoiceDate,
                            @IsImport,
                            @ModeOfShipment_ID,
                            @TotalInvoiceAmountInLocalCurrency,
                            @LandingCostFactor,
                            @Remarks,
                            @TotalAmount,
                            @DiscountPercentage,
                            @DiscountAmount,
                            @DiscountedAmount,
                            @TaxStructure_ID,
                            @TaxableOtherCharges,
                            @TaxablePercentage,
                            @TaxableOtherChargesAmount,
                            @TaxOnTaxableOtherCharges,
                            @TotalTaxableAmount,
                            @TaxAmount,
                            @NonTaxableOtherCharges,
                            @NonTaxablePercentage,
                            @NonTaxableOtherChargesAmount,
                            @TotalInvoiceAmount,
                            @DocumentNumber,
                            @FinancialYear,
                            @Company_ID,
                            @Branch_ID,
                            @Updated_By,
                            @Updated_Date,
                            @PurchaseGRN_ID,
                            @WareHouse_ID,
                            @PurchaseOrderClass_ID,
                            @ItemLevelTaxStructure_ID,
                            @IsDealer,
                            @Roundoff,
                            @TotalPIAmount,
                            @SAP_OrderNumber,
                            @InvoiceAddress_ID,
                            @ConsigneeAddress_ID
                        );
                        SELECT SCOPE_IDENTITY();";
                            using (SqlCommand cmd = new SqlCommand(insertInvoiceQuery, conn, tran))
                            {
                                cmd.CommandType = CommandType.Text;
                                cmd.Parameters.AddWithValue("@PurchaseInvoiceNumber", "");
                                cmd.Parameters.AddWithValue("@PurchaseInvoiceDate", headerrow.PurchaseInvoiceDate);
                                cmd.Parameters.AddWithValue("@Supplier_ID", headerrow.Supplier_ID);
                                cmd.Parameters.AddWithValue("@Currency_ID", headerrow.Currency_ID);
                                cmd.Parameters.AddWithValue("@ExchangeRate", headerrow.ExchangeRate);
                                cmd.Parameters.AddWithValue("@SupplierInvoiceNumber", headerrow.SupplierInvoiceNumber ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@SupplierInvoiceDate", headerrow.SupplierInvoiceDate ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@IsImport", headerrow.IsImport);
                                cmd.Parameters.AddWithValue("@ModeOfShipment_ID", headerrow.ModeOfShipment_ID ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@TotalInvoiceAmountInLocalCurrency", headerrow.TotalInvoiceAmountInLocalCurrency);
                                cmd.Parameters.AddWithValue("@LandingCostFactor", headerrow.LandingCostFactor);
                                cmd.Parameters.AddWithValue("@Remarks", headerrow.Remarks ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@TotalAmount", headerrow.TotalAmount ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@DiscountPercentage", headerrow.DiscountPercentage ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@DiscountAmount", headerrow.DiscountAmount ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@DiscountedAmount", headerrow.DiscountedAmount ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@TaxStructure_ID", headerrow.TaxStructure_ID ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@TaxableOtherCharges", headerrow.TaxableOtherCharges ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@TaxablePercentage", headerrow.TaxablePercentage ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@TaxableOtherChargesAmount", headerrow.TaxableOtherChargesAmount ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@TaxOnTaxableOtherCharges", headerrow.TaxOnTaxableOtherCharges ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@TotalTaxableAmount", headerrow.TotalTaxableAmount ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@TaxAmount", headerrow.TaxAmount ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@NonTaxableOtherCharges", headerrow.NonTaxableOtherCharges ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@NonTaxablePercentage", headerrow.NonTaxablePercentage ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@NonTaxableOtherChargesAmount", headerrow.NonTaxableOtherChargesAmount ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@TotalInvoiceAmount", headerrow.TotalInvoiceAmount ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@DocumentNumber", headerrow.DocumentNumber ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@FinancialYear", headerrow.FinancialYear);
                                cmd.Parameters.AddWithValue("@Company_ID", headerrow.Company_ID);
                                cmd.Parameters.AddWithValue("@Branch_ID", headerrow.Branch_ID);
                                cmd.Parameters.AddWithValue("@Updated_By", headerrow.Updated_By);
                                cmd.Parameters.AddWithValue("@Updated_Date", headerrow.Updated_Date);
                                cmd.Parameters.AddWithValue("@PurchaseGRN_ID", headerrow.PurchaseGRN_ID ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@WareHouse_ID", headerrow.WareHouse_ID ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@PurchaseOrderClass_ID", headerrow.PurchaseOrderClass_ID ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@ItemLevelTaxStructure_ID", headerrow.ItemLevelTaxStructure_ID ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@IsDealer", headerrow.IsDealer ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@Roundoff", headerrow.Roundoff ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@TotalPIAmount", headerrow.TotalPIAmount ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@SAP_OrderNumber", headerrow.SAP_OrderNumber ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@InvoiceAddress_ID", headerrow.InvoiceAddressID);
                                cmd.Parameters.AddWithValue("@ConsigneeAddress_ID", headerrow.ConsigneeAddressID);
                                headerrow.PurchaseInvoice_ID = Convert.ToInt32(cmd.ExecuteScalar());
                            }

                            var xmlData = new XElement("PurchaseInvoicePartsDetails",
                                headerrow.PRT_PurchaseInvoicePartsDetails.Select(partDetail => new XElement("PartDetail",
                                    new XElement("PurchaseInvoice_ID", headerrow.PurchaseInvoice_ID),
                                    partDetail.PurchaseOrder_ID.HasValue ? new XElement("PurchaseOrder_ID", partDetail.PurchaseOrder_ID.Value) : null,
                                    new XElement("Parts_ID", partDetail.Parts_ID),
                                    partDetail.PartsOrder_ID.HasValue ? new XElement("PartsOrder_ID", partDetail.PartsOrder_ID.Value) : null,
                                    new XElement("Rate", partDetail.Rate.ToString(CultureInfo.InvariantCulture)),
                                    new XElement("Quantity", partDetail.Quantity.ToString(CultureInfo.InvariantCulture)),
                                    partDetail.DiscountPercentage.HasValue ? new XElement("DiscountPercentage", partDetail.DiscountPercentage.Value.ToString(CultureInfo.InvariantCulture)) : null,
                                    partDetail.DiscountAmount.HasValue ? new XElement("DiscountAmount", partDetail.DiscountAmount.Value.ToString(CultureInfo.InvariantCulture)) : null,
                                    partDetail.TaxStructure_ID.HasValue ? new XElement("TaxStructure_ID", partDetail.TaxStructure_ID.Value) : null,
                                    partDetail.TaxAmount.HasValue ? new XElement("TaxAmount", partDetail.TaxAmount.Value.ToString(CultureInfo.InvariantCulture)) : null,
                                    new XElement("Amount", partDetail.Amount.ToString(CultureInfo.InvariantCulture)),
                                    partDetail.LandingCostInLocalCurrency.HasValue ? new XElement("LandingCostInLocalCurrency", partDetail.LandingCostInLocalCurrency.Value.ToString(CultureInfo.InvariantCulture)) : null,
                                    partDetail.VarianceinRate.HasValue ? new XElement("VarianceinRate", partDetail.VarianceinRate.Value.ToString(CultureInfo.InvariantCulture)) : null,
                                    partDetail.DiscountedAmount.HasValue ? new XElement("DiscountedAmount", partDetail.DiscountedAmount.Value.ToString(CultureInfo.InvariantCulture)) : null,
                                    new XElement("WareHouse_ID", partDetail.WareHouse_ID),
                                    partDetail.MRP.HasValue ? new XElement("MRP", partDetail.MRP.Value.ToString(CultureInfo.InvariantCulture)) : null
                                ))
                            ).ToString();

                            using (SqlCommand cmd2 = new SqlCommand("InsertXMLPurchaseInvoicePartsDetails", conn, tran))
                            {
                                cmd2.CommandType = CommandType.StoredProcedure;
                                cmd2.Parameters.Add("@XmlData", SqlDbType.Xml).Value = xmlData;
                                cmd2.ExecuteNonQuery();
                            }

                            Common.LogInsertStatus(logID,
                                $"Purchase Invoice with supplier invoice number '{firstPart.YanmarInvoiceNumber}' has been Successfully Inserted",
                                true, connString, apiCategory);
                            string emailSubject = $"Purchase Invoice with Supplier Invoice Number '{firstPart.YanmarInvoiceNumber}' has been Successfully Inserted";
                            string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, true, emailSubject);

                            Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                            detMsgList.Add(emailSubject);
                        }
                    }

                    if (Obj.piList.Count() == 0)
                    {
                        detMsgList.Add("Invalid JSON");
                        tran.Rollback();
                    }
                    else
                    {
                        tran.Commit();
                        isJsonValid = true;
                    }
                    det = new { Messages = detMsgList.Distinct().ToList() };
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            try
            {
                string updateQuery = @"
            UPDATE SAPDMS_API_Request_Log
            SET Status = @Status, ResponseMessage = @ResponseMessage
            WHERE LogID = @LogID";
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                    {
                        conn.Open();
                    }

                    using (SqlCommand cmd = new SqlCommand(updateQuery, conn))
                    {
                        if (isJsonValid == false)
                        {
                            cmd.Parameters.AddWithValue("@Status", "Error");
                            cmd.Parameters.AddWithValue("@ResponseMessage", "Invalid JSON.");
                        }
                        else if (somePartsNotInserted)
                        {
                            cmd.Parameters.AddWithValue("@Status", "Error");
                            cmd.Parameters.AddWithValue("@ResponseMessage", "Error in Insertion.");
                        }
                        else if (isJsonValid == true)
                        {
                            cmd.Parameters.AddWithValue("@Status", "Success");
                            cmd.Parameters.AddWithValue("@ResponseMessage", "Inserted Successfully!");
                        }
                        cmd.Parameters.AddWithValue("@LogID", logID);
                        cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":DK67" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return new JsonResult(det);
        }



        //END




        #region PI INTERFACE 
        /// <summary>
        /// Modify by DK - 23-March-2025
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <param name="fullUrl"></param>
        /// <param name="clientIP"></param>
        /// <param name="Recipient"></param>
        /// <param name="ccRecipient"></param>
        /// <returns></returns>

        public static IActionResult PI_Interface_OLD(PI_InterfaceList Obj, string connString, int LogException, string fullUrl, string clientIP, string Recipient, string ccRecipient)
        {
            bool somePartsNotInserted = false;
            string partHeader = "PurchaseInvoiceMaster";
            string apiEndpoint = fullUrl;
            string requestBody = JsonConvert.SerializeObject(Obj);
            var det = default(dynamic);
            List<string> detMsgList = new List<string>();
            int logID = 0;
            int purchaseOrderCount = 0;
            bool isJsonValid = false;
            List<string> poCount = new List<string>();

            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    LS.LogSheetExporter.LogToTextFile(1, "DKLOGIN", "", "DKLOGIN-SUCC Call");
                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                    {
                        conn.Open();
                    }
                    SqlTransaction tran = conn.BeginTransaction();
                    string apiCategory = "PurchaseInvoiceMaster";
                    logID = Common.LogRequest(apiEndpoint, requestBody, clientIP, apiCategory, connString, LogException);
                    LS.LogSheetExporter.LogToTextFile(1, "DKLOGIN-1", "", "DKLOGIN-SUCC Call");
                    for (int h = 0; h < Obj.piList.Count(); h++)
                    {
                        JObject detailRow = new JObject
                            {
                                { "DMSPurchaseOrderNumber", Obj.piList[h].DMSPurchaseOrderNumber },
                                { "DiscountPercentage", Obj.piList[h].DiscountPercentage },
                                { "DiscountAmount", Obj.piList[h].DiscountAmount },
                                { "PartNumber", Obj.piList[h].PartNumber},
                                { "InvoiceQty",Obj.piList[h].InvoiceQty},
                                { "InvoiceRate", Obj.piList[h].InvoiceRate },
                                { "TaxAmount",Obj.piList[h].TaxAmount },
                                { "YanmarInvoiceNumber",Obj.piList[h].YanmarInvoiceNumber },
                                { "YanmarInvoiceDate" ,Obj.piList[h].YanmarInvoiceDate },
                                { "CurrencyValue" ,Obj.piList[h].CurrencyValue },
                                { "SAP_SalesOrderNumber",Obj.piList[h].SAP_SalesOrderNumber },
                                { "Remarks", Obj.piList[h].Remarks },
                            { "FreightCharges",Obj.piList[h].FreightCharges },
                            { "PackingCharges",Obj.piList[h].PackingCharges },
                          
                            
                            
                            };
                        var columns2 = new Dictionary<string, bool>
                        {
                            { "YanmarInvoiceNumber", true },
                            { "YanmarInvoiceDate", false },
                            { "CurrencyValue", true },
                            { "SAP_SalesOrderNumber", true },
                            { "Remarks", false },  // Optional field
                            // Detail fields (inside data11)
                            { "DMSPurchaseOrderNumber", true },
                            { "DiscountPercentage", false },
                            { "DiscountAmount", false },
                            { "PartNumber", true },
                            { "InvoiceQty", true },
                            { "InvoiceRate", true },
                            { "TaxAmount", false }
                        };
                        List<string> invalidHeaderColumns4;
                        bool isHeaderValid3 = Common.ValidateAndLog(detailRow, columns2, out invalidHeaderColumns4);
                        if (!isHeaderValid3)
                        {
                            string invalidColumnsMessage = string.Join(", ", invalidHeaderColumns4);
                            somePartsNotInserted = true;
                            Common.LogInsertStatus(logID,
                           $"The provided purchase invoice  with Supplier Invoice Number {Obj.piList[h].YanmarInvoiceNumber} and  Purchase Order Number {Obj.piList[h].DMSPurchaseOrderNumber} has null columns: {invalidColumnsMessage}",
                           false, connString, apiCategory);
                            string emailSubject = $"Purchase Invoice  with Supplier Invoice Number {Obj.piList[h].YanmarInvoiceNumber} Insertion failed.";
                            string emailBody = $"The insertion of the purchase invoice  with Supplier Invoice Number '{Obj.piList[h].YanmarInvoiceNumber}' and with Purchase Order Number {Obj.piList[h].DMSPurchaseOrderNumber} has failed. " +
                            $"Missing columns: {invalidColumnsMessage}\n\n";

                            string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, false, emailBody);
                            Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                            detMsgList.Add(emailBody);
                            continue;
                        }
                        PRT_PurchaseOrder order = new PRT_PurchaseOrder();
                        string DMSPurchaseOrderNumber = Obj.piList[h].DMSPurchaseOrderNumber;

                        string query = "SELECT TOP 1 * FROM PRT_PurchaseOrder WHERE PurchaseOrderNumber=@PurchaseOrderNumber";
                        using (SqlCommand cmd = new SqlCommand(query, conn, tran))
                        {
                            LS.LogSheetExporter.LogToTextFile(1, "DKLOGIN-PO", "", "DKLOGIN-SUCC Call");
                            cmd.Parameters.AddWithValue("@PurchaseOrderNumber", DMSPurchaseOrderNumber);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    order = new PRT_PurchaseOrder
                                    {
                                        Company_ID = reader.IsDBNull(reader.GetOrdinal("Company_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                        Branch_ID = reader.IsDBNull(reader.GetOrdinal("Branch_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Branch_ID")),
                                        PurchaseOrder_ID = reader.GetInt32(reader.GetOrdinal("PurchaseOrder_ID")),
                                        PurchaseOrderNumber = reader.GetString(reader.GetOrdinal("PurchaseOrderNumber")),
                                        PurchaseOrderVersion = reader.GetByte(reader.GetOrdinal("PurchaseOrderVersion")),
                                        PurchaseOrderDate = reader.GetDateTime(reader.GetOrdinal("PurchaseOrderDate")),
                                        TypeofPurchase_ID = reader.GetInt32(reader.GetOrdinal("TypeofPurchase_ID")),
                                        Supplier_ID = reader.GetInt32(reader.GetOrdinal("Supplier_ID")),
                                        PurchaseOrderClass_ID = reader.IsDBNull(reader.GetOrdinal("PurchaseOrderClass_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("PurchaseOrderClass_ID")),
                                        InvoiceAddress_ID = reader.GetInt32(reader.GetOrdinal("InvoiceAddress_ID")),
                                        ConsigneeAddress_ID = reader.GetInt32(reader.GetOrdinal("ConsigneeAddress_ID")),
                                        PaymentTerms = reader.GetString(reader.GetOrdinal("PaymentTerms")),
                                        Brand_ID = reader.IsDBNull(reader.GetOrdinal("Brand_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Brand_ID")),
                                        ProductType_ID = reader.IsDBNull(reader.GetOrdinal("ProductType_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ProductType_ID")),
                                        Model_ID = reader.IsDBNull(reader.GetOrdinal("Model_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                        SerialNumber = reader.GetString(reader.GetOrdinal("SerialNumber")),
                                        TotalOrderAmount = reader.GetDecimal(reader.GetOrdinal("TotalOrderAmount")),
                                        Remarks = reader.IsDBNull(reader.GetOrdinal("Remarks")) ? null : reader.GetString(reader.GetOrdinal("Remarks")),
                                        EnableVersion = reader.GetBoolean(reader.GetOrdinal("EnableVersion")),
                                        IsArchived = reader.GetBoolean(reader.GetOrdinal("IsArchived")),
                                        FinancialYear = reader.GetInt32(reader.GetOrdinal("FinancialYear")),
                                        Updated_by = reader.GetInt32(reader.GetOrdinal("Updated_by")),
                                        Updated_Date = reader.GetDateTime(reader.GetOrdinal("Updated_Date")),
                                        Expected_Delivery_Date = reader.IsDBNull(reader.GetOrdinal("Expected_Delivery_Date")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("Expected_Delivery_Date")),
                                        DocumentNumber = reader.IsDBNull(reader.GetOrdinal("DocumentNumber")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("DocumentNumber")),
                                        DiscountPercentage = reader.IsDBNull(reader.GetOrdinal("DiscountPercentage")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("DiscountPercentage")),
                                        DiscountAmount = reader.IsDBNull(reader.GetOrdinal("DiscountAmount")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("DiscountAmount")),
                                        DiscountedAmount = reader.IsDBNull(reader.GetOrdinal("DiscountedAmount")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("DiscountedAmount")),
                                        TaxStructure_ID = reader.IsDBNull(reader.GetOrdinal("TaxStructure_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("TaxStructure_ID")),
                                        TotalTaxableAmount = reader.IsDBNull(reader.GetOrdinal("TotalTaxableAmount")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("TotalTaxableAmount")),
                                        TaxAmount = reader.IsDBNull(reader.GetOrdinal("TaxAmount")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("TaxAmount")),
                                        PurchaseOrderStatus_ID = reader.GetInt32(reader.GetOrdinal("PurchaseOrderStatus_ID")),
                                        WareHouse_ID = reader.IsDBNull(reader.GetOrdinal("WareHouse_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WareHouse_ID")),
                                        POStatus_ID = reader.GetInt32(reader.GetOrdinal("POStatus_ID")),
                                        ModeOfShipment_ID = reader.IsDBNull(reader.GetOrdinal("ModeOfShipment_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ModeOfShipment_ID")),
                                        SUPPLIERQUOTATION_ID = reader.IsDBNull(reader.GetOrdinal("SUPPLIERQUOTATION_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("SUPPLIERQUOTATION_ID")),
                                        AttachmentCount = reader.IsDBNull(reader.GetOrdinal("AttachmentCount")) ? (byte?)null : reader.GetByte(reader.GetOrdinal("AttachmentCount")),
                                        IsUnderBreakDown = reader.IsDBNull(reader.GetOrdinal("IsUnderBreakDown")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsUnderBreakDown")),
                                        IsDealer = reader.IsDBNull(reader.GetOrdinal("IsDealer")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsDealer")),
                                        TallyGeneratedDate = reader.IsDBNull(reader.GetOrdinal("TallyGeneratedDate")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("TallyGeneratedDate")),
                                        TallySentStatus = reader.IsDBNull(reader.GetOrdinal("TallySentStatus")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("TallySentStatus")),
                                        IncoTerms = reader.IsDBNull(reader.GetOrdinal("IncoTerms")) ? null : reader.GetString(reader.GetOrdinal("IncoTerms")),
                                        //NonTaxableOtherChargesAmount = reader.IsDBNull(reader.GetOrdinal("FreightCharges")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("FreightCharges")),
                                        //TaxableOtherChargesAmount = reader.IsDBNull(reader.GetOrdinal("PackingCharges")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("PackingCharges")),
                                        ////TaxableOtherCharges = reader.IsDBNull(reader.GetOrdinal("PackingCharges")) ? string.Empty : "Packing chrg.",
                                        //NonTaxableOtherCharges = reader.IsDBNull(reader.GetOrdinal("FreightCharges")) ? string.Empty : "Freight chrg. / Packing chrg.",
                                    };
                                }
                            }
                        }
                        if (order != null)
                        {
                            List<PRT_PurchaseInvoicePartsDetails> partdetails = new List<PRT_PurchaseInvoicePartsDetails>();
                            PRT_PurchaseInvoice headerrow = new PRT_PurchaseInvoice();
                            decimal TotalInvoiceAmount = 0.0M;
                            List<PIList> partslist = Obj.piList.Where(a => a.DMSPurchaseOrderNumber == DMSPurchaseOrderNumber).ToList();
                            for (int j = 0; j < partslist.Count; j++)
                            {
                                string supinvicenum = Obj.piList[h].YanmarInvoiceNumber;

                                int purchaseOrderID = order.PurchaseOrder_ID;

                                //string queryPOExists = @"SELECT COUNT(*) 
                                // FROM PRT_PurchaseInvoice 
                                // WHERE SupplierInvoiceNumber = @supinvicenum";
                                //using (SqlCommand cmd = new SqlCommand(queryPOExists, conn, tran))
                                //{
                                //    cmd.Parameters.AddWithValue("@supinvicenum", supinvicenum);
                                //    object result = cmd.ExecuteScalar();
                                //    if (result != null && result != DBNull.Value)
                                //    {
                                //        purchaseOrderCount = Convert.ToInt32(result);
                                //    }
                                //}

                                // Check if this combination already exists
                                 string queryPOExists = @"SELECT COUNT(*) 
                                 FROM PRT_PurchaseInvoice 
                                 WHERE SupplierInvoiceNumber = @supinvicenum AND PurchaseOrder_ID = @purchaseOrderID";
                                using (SqlCommand cmd = new SqlCommand(queryPOExists, conn, tran))
                                {
                                    cmd.Parameters.AddWithValue("@supinvicenum", supinvicenum);
                                    cmd.Parameters.AddWithValue("@purchaseOrderID", purchaseOrderID);
                                    object result = cmd.ExecuteScalar();
                                    if (result != null && result != DBNull.Value)
                                    {
                                        purchaseOrderCount = Convert.ToInt32(result);
                                    }
                                }



                                if (purchaseOrderCount > 0)
                                {
                                    if (!poCount.Contains(DMSPurchaseOrderNumber))
                                    {
                                        somePartsNotInserted = true;
                                        Common.LogInsertStatus(logID,
                                       $"The provided purchase invoice  with Purchase Order Number {Obj.piList[h].DMSPurchaseOrderNumber} has duplicate Supplier Invoice Number {Obj.piList[h].YanmarInvoiceNumber}",
                                       false, connString, apiCategory);
                                        string emailSubject = $"Purchase Invoice  with Supplier Invoice Number {Obj.piList[h].YanmarInvoiceNumber} Insertion failed.";
                                        string emailBody = $"The provided purchase invoice  with Purchase Order Number {Obj.piList[h].DMSPurchaseOrderNumber} has duplicate Supplier Invoice Number {Obj.piList[h].YanmarInvoiceNumber}";

                                        string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, false, emailBody);
                                        Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                                        detMsgList.Add(emailBody);
                                        continue;
                                    }
                                    else
                                    {
                                        continue;
                                    }
                                }
                                else
                                {
                                    string currencyname = Obj.piList[h].CurrencyValue;
                                    int SupplierID = order.Supplier_ID;
                                    bool isImport = false;
                                    int currencyID = 0;
                                    string queryIsImport = "SELECT TOP 1 IsImportExport FROM GNM_Party WHERE Party_ID = @SupplierID";
                                    using (SqlCommand cmd = new SqlCommand(queryIsImport, conn, tran))
                                    {
                                        cmd.Parameters.AddWithValue("@SupplierID", SupplierID);
                                        object result = cmd.ExecuteScalar();
                                        if (result != null && result != DBNull.Value)
                                        {
                                            isImport = Convert.ToBoolean(result);
                                        }
                                    }
                                    string queryCurrencyID = "SELECT TOP 1 RefMasterDetail_ID FROM GNM_RefMasterDetail WHERE RefMasterDetail_Short_Name = @CurrencyName";
                                    using (SqlCommand cmd = new SqlCommand(queryCurrencyID, conn, tran))
                                    {
                                        cmd.Parameters.AddWithValue("@CurrencyName", currencyname);
                                        object result = cmd.ExecuteScalar();
                                        if (result != null && result != DBNull.Value)
                                        {
                                            currencyID = Convert.ToInt32(result);
                                        }

                                    }
                                    var columns3 = new Dictionary<string, bool>
                                {
                                    { "currencyID", true }

                                };
                                    JObject rowinv = JObject.FromObject(new
                                    {
                                        currencyID = (currencyID == 0) ? (int?)null : currencyID
                                    });

                                    List<string> invalidColumns;
                                    bool isRowNotValid = Common.ValidateAndLog(rowinv, columns3, out invalidColumns);
                                    if (!isRowNotValid)
                                    {
                                        somePartsNotInserted = true;
                                        string invalidColumnsMessage = string.Join(", ", invalidColumns);
                                        somePartsNotInserted = true;
                                        Common.LogInsertStatus(logID,
                                       $"The provided purchase invoice with Supplier Invoice Number '{Obj.piList[h].YanmarInvoiceNumber}' and  Purchase Order Number {Obj.piList[h].DMSPurchaseOrderNumber}has invalid columns. {invalidColumnsMessage}",
                                       false, connString, apiCategory);
                                        string emailSubject = $"Purchase Invoice Header with Supplier Invoice Number '{Obj.piList[h].YanmarInvoiceNumber}' Insertion failed.";
                                        string emailBody = $"The insertion of the purchase invoice  with Supplier Invoice Number '{Obj.piList[h].YanmarInvoiceNumber}' and  Purchase Order Number {Obj.piList[h].DMSPurchaseOrderNumber} has failed. " +
                                        $"Invalid columns: {invalidColumnsMessage}\n\n";
                                        string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, false, emailBody);
                                        Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                                        detMsgList.Add(emailBody);
                                        continue;

                                    }
                                    headerrow.Currency_ID = currencyID;
                                    headerrow.Company_ID = order.Company_ID;
                                    headerrow.Branch_ID = order.Branch_ID;
                                    //freight & Packing charge
                                    headerrow.NonTaxableOtherChargesAmount = (Obj.piList[h].FreightCharges) + (Obj.piList[h].PackingCharges);                                   
                                    headerrow.NonTaxableOtherCharges = Obj.piList[h].FreightChargesRemarks;
                                    //end
                                    headerrow.ExchangeRate = 1;
                                    headerrow.SupplierInvoiceNumber = Obj.piList[h].YanmarInvoiceNumber;
                                    headerrow.SupplierInvoiceDate = Convert.ToDateTime(Obj.piList[h].YanmarInvoiceDate);
                                    string queryUser = "SELECT TOP 1 User_ID FROM GNM_User WHERE User_LoginID = @User_LoginID";
                                    int userID = 0;
                                    using (SqlCommand cmd = new SqlCommand(queryUser, conn, tran))
                                    {
                                        cmd.Parameters.AddWithValue("@User_LoginID", "quest_admin");
                                        object result = cmd.ExecuteScalar();
                                        if (result != null && result != DBNull.Value)
                                        {
                                            userID = Convert.ToInt32(result);
                                        }
                                    }
                                    headerrow.Updated_By = userID;
                                    headerrow.Updated_Date = DateTime.Now;
                                    headerrow.IsImport = isImport;
                                    headerrow.Remarks = Obj.piList[h].Remarks;
                                    //headerrow.SAPDispatchNum = partslist[j].SAPDispatchNum;
                                    headerrow.IsDealer = order.IsDealer;
                                    headerrow.SAP_OrderNumber = Obj.piList[h].SAP_SalesOrderNumber;
                                    headerrow.ModeOfShipment_ID = order.ModeOfShipment_ID;
                                    headerrow.PurchaseOrderClass_ID = order.PurchaseOrderClass_ID;
                                    headerrow.Supplier_ID = order.Supplier_ID;

                                    PRT_PurchaseInvoicePartsDetails partdata = new PRT_PurchaseInvoicePartsDetails();
                                    string partnumber = partslist[j].PartNumber;
                                    int partsID = 0;
                                    GNM_Parts Partsmasterdata = new GNM_Parts();
                                    string queryParts = @"SELECT TOP 1 * FROM GNM_Parts WHERE Parts_PartsNumber = @PartNumber";
                                    using (SqlCommand cmd = new SqlCommand(queryParts, conn, tran))
                                    {
                                        cmd.Parameters.AddWithValue("@PartNumber", partnumber);
                                        using (SqlDataAdapter adapter = new SqlDataAdapter(cmd))
                                        {
                                            DataTable partDataTable = new DataTable();
                                            adapter.Fill(partDataTable);
                                            if (partDataTable.Rows.Count > 0)
                                            {
                                                DataRow row = partDataTable.Rows[0];
                                                partsID = Convert.ToInt32(row["Parts_ID"]);
                                                Partsmasterdata = new GNM_Parts
                                                {
                                                    Parts_ID = Convert.ToInt32(row["Parts_ID"]),

                                                    Parts_PartPrefix = row["Parts_PartPrefix"].ToString(),
                                                    Parts_PartsNumber = row["Parts_PartsNumber"].ToString(),
                                                    ExciseDuty_ID = row["ExciseDuty_ID"] != DBNull.Value ? Convert.ToInt32(row["ExciseDuty_ID"]) : (int?)null,
                                                };
                                            }
                                        }
                                    }
                                    if (partsID > 0)
                                    {
                                        int PurchaseOrderID = order.PurchaseOrder_ID;
                                        PRT_PurchaseOrderPartsDetail purchasepart = new PRT_PurchaseOrderPartsDetail();
                                        string queryPurchaseDet = @"SELECT TOP 1 * FROM PRT_PurchaseOrderPartsDetail 
                                    WHERE PurchaseOrder_ID = @PurchaseOrderID AND Parts_ID = @PartsID";
                                        using (SqlCommand cmd = new SqlCommand(queryPurchaseDet, conn, tran))
                                        {
                                            cmd.Parameters.AddWithValue("@PurchaseOrderID", PurchaseOrderID);
                                            cmd.Parameters.AddWithValue("@PartsID", partsID);
                                            using (SqlDataReader reader = cmd.ExecuteReader())
                                            {
                                                LS.LogSheetExporter.LogToTextFile(Convert.ToInt32(PurchaseOrderID), "DK32", "", "DK33-SUCC Call");
                                                if (reader.Read()) // If data is found
                                                {
                                                    purchasepart = new PRT_PurchaseOrderPartsDetail
                                                    {
                                                        PurchaseOrderPartsDetail_ID = Convert.ToInt32(reader["PurchaseOrderPartsDetail_ID"]),
                                                        PurchaseOrder_ID = Convert.ToInt32(reader["PurchaseOrder_ID"]),
                                                        Parts_ID = Convert.ToInt32(reader["Parts_ID"]),
                                                        SupplierPrice = Convert.ToDecimal(reader["SupplierPrice"]),
                                                        RequestedQuantity = reader["RequestedQuantity"] != DBNull.Value ? Convert.ToDecimal(reader["RequestedQuantity"]) : (decimal?)null,
                                                        ApprovedQuantity = reader["ApprovedQuantity"] != DBNull.Value ? Convert.ToDecimal(reader["ApprovedQuantity"]) : (decimal?)null,
                                                        InvoicedQuantity = reader["InvoicedQuantity"] != DBNull.Value ? Convert.ToDecimal(reader["InvoicedQuantity"]) : (decimal?)null,
                                                        BackOrderCancelledQuantity = reader["BackOrderCancelledQuantity"] != DBNull.Value ? Convert.ToDecimal(reader["BackOrderCancelledQuantity"]) : (decimal?)null,
                                                        DiscountPercentage = reader["DiscountPercentage"] != DBNull.Value ? Convert.ToDecimal(reader["DiscountPercentage"]) : (decimal?)null,
                                                        DiscountAmount = reader["DiscountAmount"] != DBNull.Value ? Convert.ToDecimal(reader["DiscountAmount"]) : (decimal?)null,
                                                        TaxStructure_ID = reader["TaxStructure_ID"] != DBNull.Value ? Convert.ToInt32(reader["TaxStructure_ID"]) : (int?)null,
                                                        TaxAmount = reader["TaxAmount"] != DBNull.Value ? Convert.ToDecimal(reader["TaxAmount"]) : (decimal?)null,
                                                        Amount = Convert.ToDecimal(reader["Amount"]),
                                                        PartsOrder_ID = reader["PartsOrder_ID"] != DBNull.Value ? Convert.ToInt32(reader["PartsOrder_ID"]) : (int?)null,
                                                        DiscountedAmount = reader["DiscountedAmount"] != DBNull.Value ? Convert.ToDecimal(reader["DiscountedAmount"]) : (decimal?)null,
                                                        MRP = reader["MRP"] != DBNull.Value ? Convert.ToDecimal(reader["MRP"]) : (decimal?)null
                                                    };
                                                }
                                            }
                                        }
                                        if (purchasepart.PurchaseOrderPartsDetail_ID != 0)
                                        {
                                            LS.LogSheetExporter.LogToTextFile(Convert.ToInt32(purchasepart.PurchaseOrderPartsDetail_ID), "DK12", "", "DK33-SUCC Call");
                                            purchasepart.InvoicedQuantity = purchasepart.InvoicedQuantity == null ? 0 : purchasepart.InvoicedQuantity;
                                            if (partslist[j].InvoiceQty <= (purchasepart.ApprovedQuantity - purchasepart.InvoicedQuantity))
                                            {
                                                LS.LogSheetExporter.LogToTextFile(Convert.ToInt32(partslist[j].InvoiceQty), "DK45", "", "DK33-SUCC Call");
                                                partdata.Parts_ID = partsID;
                                                partdata.PurchaseInvoice_ID = headerrow.PurchaseInvoice_ID;
                                                partdata.PurchaseOrder_ID = order.PurchaseOrder_ID;
                                                partdata.Rate = partslist[j].InvoiceRate;
                                                partdata.Quantity = partslist[j].InvoiceQty;
                                                if (partslist[j].DiscountAmount.HasValue && partslist[j].DiscountPercentage.HasValue)
                                                {
                                                    decimal expectedDiscountAmount = (partdata.Rate * partdata.Quantity) * (partslist[j].DiscountPercentage.Value / 100);
                                                    if (Math.Abs(partslist[j].DiscountAmount.Value - expectedDiscountAmount) > 0.01m) // Allowing a small tolerance for floating-point precision
                                                    {
                                                        somePartsNotInserted = true;
                                                        string errorMessage = $"The provided DiscountAmount {partslist[j].DiscountAmount.Value} does not match the expected amount {expectedDiscountAmount} based on the DiscountPercentage {partslist[j].DiscountPercentage.Value} for Purchase Order {Obj.piList[h].DMSPurchaseOrderNumber}, Part Number {partslist[j].PartNumber}.";
                                                        Common.LogInsertStatus(logID, errorMessage, false, connString, apiCategory);
                                                        string emailSubject = $"Discount Amount Validation Failed for Purchase Order {Obj.piList[h].DMSPurchaseOrderNumber}, Part Number {partslist[j].PartNumber}.";
                                                        string emailBody = $"The provided DiscountAmount {partslist[j].DiscountAmount.Value} does not match the expected amount {expectedDiscountAmount} based on the DiscountPercentage {partslist[j].DiscountPercentage.Value} for Purchase Order {Obj.piList[h].DMSPurchaseOrderNumber}, Part Number {partslist[j].PartNumber}.\n\n";
                                                        string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, false, emailBody);
                                                        Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                                                        detMsgList.Add(emailBody);
                                                        continue;
                                                    }
                                                    partdata.DiscountAmount = partslist[j].DiscountAmount;
                                                    partdata.DiscountPercentage = partslist[j].DiscountPercentage;
                                                }

                                                else if (partslist[j].DiscountAmount.HasValue)
                                                {
                                                    partdata.DiscountAmount = partslist[j].DiscountAmount;
                                                    partdata.DiscountPercentage = 0;
                                                }
                                                else if (partslist[j].DiscountPercentage.HasValue)
                                                {
                                                    partdata.DiscountPercentage = partslist[j].DiscountPercentage;
                                                    partdata.DiscountAmount = Math.Round((partdata.Rate * partdata.Quantity) * (partdata.DiscountPercentage.GetValueOrDefault() / 100), 2);

                                                }
                                                else
                                                {
                                                    partdata.DiscountAmount = 0m;
                                                    partdata.DiscountPercentage = 0m;
                                                }
                                                partdata.DiscountedAmount = Math.Round((partdata.Rate * partdata.Quantity) - partdata.DiscountAmount.GetValueOrDefault(), 2);
                                                partdata.MRP = purchasepart.MRP;
                                                partdata.TaxStructure_ID = purchasepart.TaxStructure_ID;
                                                if (partslist[j].TaxAmount == null || partslist[j].TaxAmount == 0)
                                                {
                                                    decimal? TaxAmount = (purchasepart.TaxAmount / purchasepart.ApprovedQuantity) * partdata.Quantity;
                                                    partdata.TaxAmount = TaxAmount;
                                                    partdata.Amount = Convert.ToDecimal(TaxAmount) + (partdata.Rate * partdata.Quantity) - partdata.DiscountAmount.GetValueOrDefault();
                                                    partdata.LandingCostInLocalCurrency = TaxAmount + (partdata.Rate * partdata.Quantity) - partdata.DiscountAmount.GetValueOrDefault();

                                                }
                                                else
                                                {
                                                    partdata.TaxAmount = partslist[j].TaxAmount;
                                                    partdata.Amount = partslist[j].TaxAmount + (partdata.Rate * partdata.Quantity) - partdata.DiscountAmount.GetValueOrDefault();
                                                    partdata.LandingCostInLocalCurrency = partslist[j].TaxAmount + (partdata.Rate * partdata.Quantity) - partdata.DiscountAmount.GetValueOrDefault();
                                                }
                                                partdata.WareHouse_ID = Convert.ToInt32(order.WareHouse_ID);
                                                partdata.TaxStructure_ID = GetTaxStructureForPart(Partsmasterdata.ExciseDuty_ID, 0, Partsmasterdata.Parts_ID, SupplierID, Convert.ToInt32(order.Company_ID), Convert.ToInt32(order.Branch_ID), connString).FirstOrDefault(k => k.IsDefault) == null ? 0 : GetTaxStructureForPart(Partsmasterdata.ExciseDuty_ID, 0, Partsmasterdata.Parts_ID, SupplierID, Convert.ToInt32(order.Company_ID), Convert.ToInt32(order.Branch_ID), connString).FirstOrDefault(k => k.IsDefault).TaxStructure_ID;
                                                TotalInvoiceAmount = TotalInvoiceAmount + partdata.Amount;
                                                if (Partsmasterdata.ExciseDuty_ID == 0 || Partsmasterdata.ExciseDuty_ID == null)
                                                {
                                                    somePartsNotInserted = true;
                                                    Common.LogInsertStatus(logID,
                                                    $"The provided purchase invoice with Purchase Order Number {Obj.piList[h].DMSPurchaseOrderNumber} and Supplier Invoice Number {Obj.piList[h].YanmarInvoiceNumber} does not have an HSN Code.",
                                                    false, connString, apiCategory);
                                                    string emailSubject = $"Purchase Invoice  with Supplier Invoice Number {Obj.piList[h].YanmarInvoiceNumber} Insertion failed.";
                                                    string emailBody = $"The provided purchase invoice with Purchase Order Number {Obj.piList[h].DMSPurchaseOrderNumber} and Supplier Invoice Number {Obj.piList[h].YanmarInvoiceNumber} does not have an HSN Code.";
                                                    string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, false, emailBody);
                                                    Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                                                    detMsgList.Add(emailBody);
                                                    continue;
                                                }
                                                else { headerrow.PRT_PurchaseInvoicePartsDetails.Add(partdata); }
                                            }
                                            else
                                            {
                                                if (!poCount.Contains(DMSPurchaseOrderNumber))
                                                {
                                                    somePartsNotInserted = true;
                                                    Common.LogInsertStatus(logID,
                                                    $"The provided purchase invoice with Purchase Order Number {Obj.piList[h].DMSPurchaseOrderNumber} and Supplier Invoice Number {Obj.piList[h].YanmarInvoiceNumber} either has an invoice quantity exceeding the approved quantity or a purchase invoice already exists for this purchase order."
    ,
                                                    false, connString, apiCategory);
                                                    string emailSubject = $"Purchase Invoice with Supplier Invoice Number {Obj.piList[h].YanmarInvoiceNumber} Insertion Failed.";
                                                    string emailBody = $"The provided purchase invoice with Purchase Order Number {Obj.piList[h].DMSPurchaseOrderNumber} and Supplier Invoice Number {Obj.piList[h].YanmarInvoiceNumber} either has an invoice quantity exceeding the approved quantity or a purchase invoice already exists for this purchase order.";
                                                    string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, false, emailBody);
                                                    Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                                                    detMsgList.Add(emailBody);
                                                    continue;
                                                }
                                                else
                                                {
                                                    continue;
                                                }
                                            }
                                        }
                                    }
                                    else
                                    {
                                        somePartsNotInserted = true;
                                        string invalidColumnsMessage = string.Join(", ", invalidColumns);
                                        somePartsNotInserted = true;
                                        Common.LogInsertStatus(logID,
                                       $"The provided purchase invoice with Purchase Order Number {Obj.piList[h].DMSPurchaseOrderNumber} and Supplier Invoice number{Obj.piList[h].YanmarInvoiceNumber}  has invalid PartNumber '{Obj.piList[h].PartNumber}'  .",
                                       false, connString, apiCategory);
                                        string emailSubject = $"The provided purchase invoice with Purchase Order Number {Obj.piList[h].DMSPurchaseOrderNumber} and Supplier Invoice number{Obj.piList[h].YanmarInvoiceNumber} insertion failed  .";
                                        string emailBody = $"The provided purchase invoice with Purchase Order Number {Obj.piList[h].DMSPurchaseOrderNumber} and Supplier Invoice number{Obj.piList[h].YanmarInvoiceNumber}  has invalid PartNumber '{Obj.piList[h].PartNumber}'  .";

                                        string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, false, emailBody);
                                        Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                                        detMsgList.Add(emailBody);
                                        continue;
                                    }
                                }

                            }
                            if (headerrow.PRT_PurchaseInvoicePartsDetails.Count > 0)
                            {
                                LS.LogSheetExporter.LogToTextFile(Convert.ToInt32(headerrow.PRT_PurchaseInvoicePartsDetails.Count), "DK33", "", "DK33-SUCC Call");
                                decimal roundoffamount = TotalInvoiceAmount - Math.Round(TotalInvoiceAmount);
                                headerrow.Roundoff = roundoffamount;
                                headerrow.PurchaseInvoiceNumber = "";
                                headerrow.PurchaseInvoiceDate = DateTime.Now;
                                headerrow.Supplier_ID = order.Supplier_ID;
                                headerrow.TotalInvoiceAmount = TotalInvoiceAmount;
                                headerrow.TotalAmount = TotalInvoiceAmount;
                                headerrow.TotalInvoiceAmountInLocalCurrency = TotalInvoiceAmount;
                                headerrow.TotalTaxableAmount = TotalInvoiceAmount;
                                headerrow.DiscountPercentage = 0;
                                headerrow.DiscountAmount = 0;
                                headerrow.DiscountedAmount = 0;
                                headerrow.InvoiceAddressID = order.InvoiceAddress_ID;
                                headerrow.ConsigneeAddressID = order.ConsigneeAddress_ID;
                                headerrow.WareHouse_ID = order.WareHouse_ID;
                                headerrow.TotalPIAmount = Math.Round(TotalInvoiceAmount);
                                headerrow.NonTaxableOtherCharges = ((partslist.Select(a => a.FreightCharges).FirstOrDefault()) + (partslist.Select(a => a.PackingCharges).FirstOrDefault())) > 0 ? "Freight chrg./Packing chrg." : "";
                                headerrow.NonTaxableOtherChargesAmount = (partslist.Select(a => a.FreightCharges).FirstOrDefault()) + (partslist.Select(a => a.PackingCharges).FirstOrDefault());

                                LS.LogSheetExporter.LogToTextFile(Convert.ToInt32(order.Branch_ID), connString, "", "DK33-SUCC Call");

                                if (Common.CheckPreffixSuffix(connString, LogException, Convert.ToInt32(order.Company_ID), Convert.ToInt32(order.Branch_ID), "PRT_PurchaseInvoice"))
                                {
                                    LS.LogSheetExporter.LogToTextFile(Convert.ToInt32(order.Branch_ID), "DK15", "", "DK33-SUCC Call");
                                    string insertInvoiceQuery = @"
                                        INSERT INTO PRT_PurchaseInvoice 
                                        (
                                            PurchaseInvoiceNumber,
                                            PurchaseInvoiceDate,
                                            Supplier_ID,
                                            Currency_ID,
                                            ExchangeRate,
                                            SupplierInvoiceNumber,
                                            SupplierInvoiceDate,
                                            IsImport,
                                            ModeOfShipment_ID,
                                            TotalInvoiceAmountInLocalCurrency,
                                            LandingCostFactor,
                                            Remarks,
                                            TotalAmount,
                                            DiscountPercentage,
                                            DiscountAmount,
                                            DiscountedAmount,
                                            TaxStructure_ID,
                                            TaxableOtherCharges,
                                            TaxablePercentage,
                                            TaxableOtherChargesAmount,
                                            TaxOnTaxableOtherCharges,
                                            TotalTaxableAmount,
                                            TaxAmount,
                                            NonTaxableOtherCharges,
                                            NonTaxablePercentage,
                                            NonTaxableOtherChargesAmount,
                                            TotalInvoiceAmount,
                                            DocumentNumber,
                                            FinancialYear,
                                            Company_ID,
                                            Branch_ID,
                                            Updated_By,
                                            Updated_Date,
                                            PurchaseGRN_ID,
                                            WareHouse_ID,
                                            PurchaseOrderClass_ID,
                                            ItemLevelTaxStructure_ID,
                                            IsDealer,
                                            Roundoff,
                                            TotalPIAmount,
                                            SAP_OrderNumber,
                                            InvoiceAddress_ID,
                                            ConsigneeAddress_ID
                                        )
                                        VALUES
                                        (
                                            @PurchaseInvoiceNumber,
                                            @PurchaseInvoiceDate,
                                            @Supplier_ID,
                                            @Currency_ID,
                                            @ExchangeRate,
                                            @SupplierInvoiceNumber,
                                            @SupplierInvoiceDate,
                                            @IsImport,
                                            @ModeOfShipment_ID,
                                            @TotalInvoiceAmountInLocalCurrency,
                                            @LandingCostFactor,
                                            @Remarks,
                                            @TotalAmount,
                                            @DiscountPercentage,
                                            @DiscountAmount,
                                            @DiscountedAmount,
                                            @TaxStructure_ID,
                                            @TaxableOtherCharges,
                                            @TaxablePercentage,
                                            @TaxableOtherChargesAmount,
                                            @TaxOnTaxableOtherCharges,
                                            @TotalTaxableAmount,
                                            @TaxAmount,
                                            @NonTaxableOtherCharges,
                                            @NonTaxablePercentage,
                                            @NonTaxableOtherChargesAmount,
                                            @TotalInvoiceAmount,
                                            @DocumentNumber,
                                            @FinancialYear,
                                            @Company_ID,
                                            @Branch_ID,
                                            @Updated_By,
                                            @Updated_Date,
                                            @PurchaseGRN_ID,
                                            @WareHouse_ID,
                                            @PurchaseOrderClass_ID,
                                            @ItemLevelTaxStructure_ID,
                                            @IsDealer,
                                            @Roundoff,
                                            @TotalPIAmount,
                                            @SAP_OrderNumber,
                                            @InvoiceAddress_ID,
                                            @ConsigneeAddress_ID
                                        );
                                        SELECT SCOPE_IDENTITY();";
                                    SqlCommand cmd = null;
                                    using (cmd = new SqlCommand(insertInvoiceQuery, conn, tran))
                                    {
                                        cmd.CommandType = CommandType.Text;
                                        cmd.Parameters.AddWithValue("@PurchaseInvoiceNumber", "");
                                        cmd.Parameters.AddWithValue("@PurchaseInvoiceDate", headerrow.PurchaseInvoiceDate);
                                        cmd.Parameters.AddWithValue("@Supplier_ID", headerrow.Supplier_ID);
                                        cmd.Parameters.AddWithValue("@Currency_ID", headerrow.Currency_ID);
                                        cmd.Parameters.AddWithValue("@ExchangeRate", headerrow.ExchangeRate);
                                        cmd.Parameters.AddWithValue("@SupplierInvoiceNumber", headerrow.SupplierInvoiceNumber ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@SupplierInvoiceDate", headerrow.SupplierInvoiceDate ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@IsImport", headerrow.IsImport);
                                        cmd.Parameters.AddWithValue("@ModeOfShipment_ID", headerrow.ModeOfShipment_ID ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@TotalInvoiceAmountInLocalCurrency", headerrow.TotalInvoiceAmountInLocalCurrency);
                                        cmd.Parameters.AddWithValue("@LandingCostFactor", headerrow.LandingCostFactor);
                                        cmd.Parameters.AddWithValue("@Remarks", headerrow.Remarks ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@TotalAmount", headerrow.TotalAmount ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@DiscountPercentage", headerrow.DiscountPercentage ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@DiscountAmount", headerrow.DiscountAmount ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@DiscountedAmount", headerrow.DiscountedAmount ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@TaxStructure_ID", headerrow.TaxStructure_ID ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@TaxableOtherCharges", headerrow.TaxableOtherCharges ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@TaxablePercentage", headerrow.TaxablePercentage ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@TaxableOtherChargesAmount", headerrow.TaxableOtherChargesAmount ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@TaxOnTaxableOtherCharges", headerrow.TaxOnTaxableOtherCharges ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@TotalTaxableAmount", headerrow.TotalTaxableAmount ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@TaxAmount", headerrow.TaxAmount ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@NonTaxableOtherCharges", headerrow.NonTaxableOtherCharges ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@NonTaxablePercentage", headerrow.NonTaxablePercentage ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@NonTaxableOtherChargesAmount", headerrow.NonTaxableOtherChargesAmount ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@TotalInvoiceAmount", headerrow.TotalInvoiceAmount ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@DocumentNumber", headerrow.DocumentNumber ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@FinancialYear", headerrow.FinancialYear);
                                        cmd.Parameters.AddWithValue("@Company_ID", headerrow.Company_ID);
                                        cmd.Parameters.AddWithValue("@Branch_ID", headerrow.Branch_ID);
                                        cmd.Parameters.AddWithValue("@Updated_By", headerrow.Updated_By);
                                        cmd.Parameters.AddWithValue("@Updated_Date", headerrow.Updated_Date);
                                        cmd.Parameters.AddWithValue("@PurchaseGRN_ID", headerrow.PurchaseGRN_ID ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@WareHouse_ID", headerrow.WareHouse_ID ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@PurchaseOrderClass_ID", headerrow.PurchaseOrderClass_ID ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@ItemLevelTaxStructure_ID", headerrow.ItemLevelTaxStructure_ID ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@IsDealer", headerrow.IsDealer ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@Roundoff", headerrow.Roundoff ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@TotalPIAmount", headerrow.TotalPIAmount ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@SAP_OrderNumber", headerrow.SAP_OrderNumber ?? (object)DBNull.Value);
                                        cmd.Parameters.AddWithValue("@InvoiceAddress_ID", headerrow.InvoiceAddressID);
                                        cmd.Parameters.AddWithValue("@ConsigneeAddress_ID", headerrow.ConsigneeAddressID);

                                        //cmd.Parameters.AddWithValue("@NonTaxableOtherCharges", headerrow.NonTaxableOtherCharges);
                                        //cmd.Parameters.AddWithValue("@NonTaxableOtherChargesAmount", headerrow.NonTaxableOtherChargesAmount);                                       
                                        headerrow.PurchaseInvoice_ID = Convert.ToInt32(cmd.ExecuteScalar());
                                    }
                                    if (headerrow.PurchaseInvoice_ID > 0)
                                    {
                                        var xmlData = new XElement("PurchaseInvoicePartsDetails",
                                         headerrow.PRT_PurchaseInvoicePartsDetails.Select(partDetail => new XElement("PartDetail",
                                             new XElement("PurchaseInvoice_ID", headerrow.PurchaseInvoice_ID),
                                             partDetail.PurchaseOrder_ID.HasValue ? new XElement("PurchaseOrder_ID", partDetail.PurchaseOrder_ID.Value) : null,
                                             new XElement("Parts_ID", partDetail.Parts_ID),
                                             partDetail.PartsOrder_ID.HasValue ? new XElement("PartsOrder_ID", partDetail.PartsOrder_ID.Value) : null,
                                             new XElement("Rate", partDetail.Rate.ToString(CultureInfo.InvariantCulture)), // Force correct decimal format
                                             new XElement("Quantity", partDetail.Quantity.ToString(CultureInfo.InvariantCulture)),
                                             partDetail.DiscountPercentage.HasValue ? new XElement("DiscountPercentage", partDetail.DiscountPercentage.Value.ToString(CultureInfo.InvariantCulture)) : null,
                                             partDetail.DiscountAmount.HasValue ? new XElement("DiscountAmount", partDetail.DiscountAmount.Value.ToString(CultureInfo.InvariantCulture)) : null,
                                             partDetail.TaxStructure_ID.HasValue ? new XElement("TaxStructure_ID", partDetail.TaxStructure_ID.Value) : null,
                                             partDetail.TaxAmount.HasValue ? new XElement("TaxAmount", partDetail.TaxAmount.Value.ToString(CultureInfo.InvariantCulture)) : null,
                                             new XElement("Amount", partDetail.Amount.ToString(CultureInfo.InvariantCulture)),
                                             partDetail.LandingCostInLocalCurrency.HasValue ? new XElement("LandingCostInLocalCurrency", partDetail.LandingCostInLocalCurrency.Value.ToString(CultureInfo.InvariantCulture)) : null,
                                             partDetail.VarianceinRate.HasValue ? new XElement("VarianceinRate", partDetail.VarianceinRate.Value.ToString(CultureInfo.InvariantCulture)) : null,
                                             partDetail.DiscountedAmount.HasValue ? new XElement("DiscountedAmount", partDetail.DiscountedAmount.Value.ToString(CultureInfo.InvariantCulture)) : null,
                                             new XElement("WareHouse_ID", partDetail.WareHouse_ID),
                                             partDetail.MRP.HasValue ? new XElement("MRP", partDetail.MRP.Value.ToString(CultureInfo.InvariantCulture)) : null
                                         ))
                                     ).ToString();

                                        using (SqlCommand cmd2 = new SqlCommand("InsertXMLPurchaseInvoicePartsDetails", conn, tran))
                                        {
                                            cmd2.CommandType = CommandType.StoredProcedure;
                                            cmd2.Parameters.Add("@XmlData", SqlDbType.Xml).Value = xmlData;  // Explicitly define XML parameter type
                                            cmd2.ExecuteNonQuery();
                                        }

                                        Common.LogInsertStatus(logID,
                                        $"Purchase Invoice with supplier invoice number '{Obj.piList[h].YanmarInvoiceNumber}' and Purchase Order Number {Obj.piList[h].DMSPurchaseOrderNumber} has been Successfully Inserted",
                                        true, connString, apiCategory);
                                        string emailSubject = $"Purchase Invoice with Supplier Invoice Number '{Obj.piList[h].YanmarInvoiceNumber}' and Purchase Order Number {Obj.piList[h].DMSPurchaseOrderNumber} has been Successfully Inserted";
                                        string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, true, emailSubject);

                                        Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                                        detMsgList.Add(emailSubject);
                                        poCount.Add(Obj.piList[h].DMSPurchaseOrderNumber);

                                        List<PRT_PurchaseInvoicePartsDetails> purchaseInvoicePartsAfterInsert = new List<PRT_PurchaseInvoicePartsDetails>();
                                        string querypurchaseInvoicePartsAfterInsert = "SELECT Parts_ID, Quantity, PurchaseOrder_ID, WareHouse_ID, LandingCostInLocalCurrency FROM PRT_PurchaseInvoicePartsDetails WHERE PurchaseInvoice_ID = @PurchaseInvoice_ID";
                                        using (SqlCommand cmd3 = new SqlCommand(querypurchaseInvoicePartsAfterInsert, conn, tran))
                                        {
                                            cmd3.Parameters.AddWithValue("@PurchaseInvoice_ID", headerrow.PurchaseInvoice_ID);
                                            using (SqlDataReader reader = cmd3.ExecuteReader())
                                            {
                                                while (reader.Read())
                                                {
                                                    purchaseInvoicePartsAfterInsert.Add(new PRT_PurchaseInvoicePartsDetails
                                                    {
                                                        Parts_ID = Convert.ToInt32(reader["Parts_ID"]),
                                                        Quantity = Convert.ToDecimal(reader["Quantity"]),
                                                        PurchaseOrder_ID = reader["PurchaseOrder_ID"] != DBNull.Value ? (int?)Convert.ToInt32(reader["PurchaseOrder_ID"]) : null,
                                                        WareHouse_ID = Convert.ToInt32(reader["WareHouse_ID"]),
                                                        LandingCostInLocalCurrency = reader["LandingCostInLocalCurrency"] != DBNull.Value ? (decimal?)Convert.ToDecimal(reader["LandingCostInLocalCurrency"]) : null
                                                    });
                                                }
                                            }
                                        }
                                        for (int i = 0; i < purchaseInvoicePartsAfterInsert.Count; i++)
                                        {
                                            AllocationLogicServices.UpdatePartsStock(purchaseInvoicePartsAfterInsert[i].Parts_ID, purchaseInvoicePartsAfterInsert[i].Quantity, (purchaseInvoicePartsAfterInsert[i].PurchaseOrder_ID != null ? purchaseInvoicePartsAfterInsert[i].PurchaseOrder_ID.Value : 0), purchaseInvoicePartsAfterInsert[i].WareHouse_ID, headerrow.PurchaseInvoiceDate, (purchaseInvoicePartsAfterInsert[i].LandingCostInLocalCurrency != null ? purchaseInvoicePartsAfterInsert[i].LandingCostInLocalCurrency.Value : 0), headerrow.Supplier_ID, Convert.ToInt32(headerrow.Company_ID), Convert.ToInt32(headerrow.Branch_ID), connString, LogException);
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            somePartsNotInserted = true;
                            Common.LogInsertStatus(logID,
                            $"The provided purchase invoice data with Supplier Invoice number {Obj.piList[h].YanmarInvoiceNumber} has invalid Purchase Order Number {Obj.piList[h].DMSPurchaseOrderNumber}  ",
                            false, connString, apiCategory);
                            string emailSubject = $"Purchase Invoice  with with Purchase Order Number {Obj.piList[h].DMSPurchaseOrderNumber}  and Supplier Invoice number {Obj.piList[h].YanmarInvoiceNumber} Insertion failed.";
                            string emailBody = $"The provided purchase invoice data with Supplier Invoice number {Obj.piList[h].YanmarInvoiceNumber} has invalid Purchase Order Number {Obj.piList[h].DMSPurchaseOrderNumber}  ";

                            string emailTemplate = Common.GetEmailTemplatePurchaseInvoice(partHeader, "Validation", fullUrl, false, emailBody);
                            detMsgList.Add(emailBody);
                            Common.InsertEmailLog(conn, emailSubject, emailTemplate, Recipient, ccRecipient, tran);
                        }
                    }
                    if (Obj.piList.Count() == 0)
                    {
                        detMsgList.Add("Invalid JSON");
                        tran.Rollback();
                    }
                    else
                    {
                        tran.Commit();
                        isJsonValid = true;
                    }
                    det = new { Messages = detMsgList.Distinct().ToList() };
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            try
            {
                string updateQuery = @"
                UPDATE SAPDMS_API_Request_Log
                SET Status = @Status, ResponseMessage = @ResponseMessage
                WHERE LogID = @LogID";
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                     {
                        conn.Open();
                    }

                    using (SqlCommand cmd = new SqlCommand(updateQuery, conn))
                    {
                        if (isJsonValid == false)
                        {
                            cmd.Parameters.AddWithValue("@Status", "Error");
                            cmd.Parameters.AddWithValue("@ResponseMessage", "Invalid JSON.");
                        }
                        else
                        if (somePartsNotInserted)
                        {
                            cmd.Parameters.AddWithValue("@Status", "Error");
                            cmd.Parameters.AddWithValue("@ResponseMessage", "Error in Insertion.");
                        }
                        else if (isJsonValid == true)
                        {
                            cmd.Parameters.AddWithValue("@Status", "Success");
                            cmd.Parameters.AddWithValue("@ResponseMessage", "Inserted Successfully!");
                        }
                        cmd.Parameters.AddWithValue("@LogID", logID);
                        cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":DK67" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return new JsonResult(det);
        }
        /// <summary>
        /// Modifed by DK
        /// </summary>
        /// <param name="ExciseDuty_ID"></param>
        /// <param name="Company_ID"></param>
        /// <param name="connectionString"></param>
        /// <returns></returns>
        public static List<TaxStructureForExciseDuty> GetTaxStructureDetail(int? ExciseDuty_ID, int Company_ID, string connectionString)
        {
            List<TaxStructureForExciseDuty> TaxStructureResult = new List<TaxStructureForExciseDuty>();
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    string query = "";

                    if (ExciseDuty_ID != null)
                    {
                        query = @"SELECT TaxStructure_Name, B.TaxStructure_ID, A.IsDefault 
                              FROM GNM_TaxStrucutreExciseDutyDtl A 
                              JOIN GNM_TaxStructure B ON A.TaxStructure_ID = B.TaxStructure_ID 
                              WHERE ExciseDuty_ID = @ExciseDuty_ID 
                              AND B.TaxStructure_IsActive = 1 
                              AND ISNULL(IsServiceTax, 0) != 1 
                              AND B.Company_ID = @Company_ID 
                              AND ((GETDATE() BETWEEN B.FromDate AND DATEADD(DAY, 1, B.ToDate)) 
                              OR (GETDATE() >= B.FromDate AND B.ToDate IS NULL))";

                        using (SqlCommand cmd = new SqlCommand(query, conn))
                        {
                            cmd.Parameters.AddWithValue("@ExciseDuty_ID", ExciseDuty_ID);
                            cmd.Parameters.AddWithValue("@Company_ID", Company_ID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    TaxStructureResult.Add(new TaxStructureForExciseDuty
                                    {
                                        TaxStructure_Name = reader["TaxStructure_Name"].ToString(),
                                        TaxStructure_ID = Convert.ToInt32(reader["TaxStructure_ID"]),
                                        IsDefault = Convert.ToBoolean(reader["IsDefault"])
                                    });
                                }
                            }
                        }

                        if (TaxStructureResult.Count == 0)
                        {
                            query += @" UNION 
                                    SELECT DISTINCT TaxStructure_Name, A.TaxStructure_ID, ISNULL(B.IsDefault, 0) AS IsDefault 
                                    FROM GNM_TaxStructure A 
                                    LEFT OUTER JOIN GNM_TaxStrucutreExciseDutyDtl B ON A.TaxStructure_ID = B.TaxStructure_ID 
                                    WHERE B.TaxStructure_ID IS NULL 
                                    AND A.TaxStructure_IsActive = 1 
                                    AND ISNULL(IsServiceTax, 0) != 1 
                                    AND A.Company_ID = @Company_ID 
                                    AND ((GETDATE() BETWEEN A.FromDate AND DATEADD(DAY, 1, A.ToDate)) 
                                    OR (GETDATE() >= A.FromDate AND A.ToDate IS NULL))";

                            using (SqlCommand cmd = new SqlCommand(query, conn))
                            {
                                cmd.Parameters.AddWithValue("@Company_ID", Company_ID);

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        TaxStructureResult.Add(new TaxStructureForExciseDuty
                                        {
                                            TaxStructure_Name = reader["TaxStructure_Name"].ToString(),
                                            TaxStructure_ID = Convert.ToInt32(reader["TaxStructure_ID"]),
                                            IsDefault = Convert.ToBoolean(reader["IsDefault"])
                                        });
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error: " + ex.Message);
            }

            return TaxStructureResult;
        }
        
        /// <summary>
        /// Modified by DK
        /// </summary>
        /// <param name="ExciseDuty_ID"></param>
        /// <param name="CurrentTaxStructure_ID"></param>
        /// <param name="Part_ID"></param>
        /// <param name="Party_ID"></param>
        /// <param name="Company_ID"></param>
        /// <param name="branchID"></param>
        /// <param name="connectionString"></param>
        /// <returns></returns>              
        public static List<TaxStructureForExciseDuty> GetTaxStructureForPart(int? ExciseDuty_ID, int? CurrentTaxStructure_ID, int Part_ID, int Party_ID, int Company_ID, int branchID, string connectionString)
        {
            List<TaxStructureForExciseDuty> TaxStructureList = new List<TaxStructureForExciseDuty>();

            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();

                    int? ExciseDuty_ID2 = null;
                    if (ExciseDuty_ID > 0)
                    {
                        string query = "SELECT ExciseDuty_ID FROM GNM_Parts WHERE Parts_ID = @Part_ID";
                        using (SqlCommand cmd = new SqlCommand(query, conn))
                        {
                            cmd.Parameters.AddWithValue("@Part_ID", Part_ID);
                            object result = cmd.ExecuteScalar();
                            if (result != DBNull.Value && result != null)
                            {
                                ExciseDuty_ID2 = Convert.ToInt32(result);
                            }
                        }
                    }

                    TaxStructureList = GetTaxStructureDetail(ExciseDuty_ID2, Company_ID, connectionString);

                    int? Party_StateID = null;
                    string stateQuery = "SELECT State_ID FROM GNM_Party WHERE Party_ID = @Party_ID";
                    using (SqlCommand cmd = new SqlCommand(stateQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@Party_ID", Party_ID);
                        object result = cmd.ExecuteScalar();
                        if (result != DBNull.Value && result != null)
                        {
                            Party_StateID = Convert.ToInt32(result);
                        }
                    }

                    int UserBranch_StateID = 0;
                    string branchQuery = "SELECT State_ID FROM GNM_Branch WHERE Branch_ID = @Branch_ID";
                    using (SqlCommand cmd = new SqlCommand(branchQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@Branch_ID", branchID);
                        object result = cmd.ExecuteScalar();
                        if (result != DBNull.Value && result != null)
                        {
                            UserBranch_StateID = Convert.ToInt32(result);
                        }
                    }

                    if (UserBranch_StateID != Party_StateID)
                    {
                        for (int i = 0; i < TaxStructureList.Count; i++)
                        {
                            TaxStructureList[i].IsDefault = !TaxStructureList[i].IsDefault;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return TaxStructureList;
        }
        #endregion


        //public static decimal CalculateTax(int taxStructureID, double baseAmount)
        //{
        //    decimal totalTaxAmount = 0.00M;
        //    try
        //    {

        //        List<TaxOutput> listOutput = getTaxDetails(taxStructureID, baseAmount);

        //        for (int i = 0; i < listOutput.Count; i++)
        //        {
        //            totalTaxAmount += listOutput[i].TaxAmount;
        //        }
        //    }
        //    catch (Exception ex)
        //    {

        //    }
        //    return totalTaxAmount;
        //}

        //public static List<TaxOutput> getTaxDetails(int taxStructureID, double baseAmount,string connString)
        //{
        //    string connectionString = connString; // Replace with your actual connection string
        //    DataTable dt = new DataTable();
        //    List<Input> inputList = new List<Input>();
        //    List<TaxOutput> listOutput = new List<TaxOutput>();

        //    using (SqlConnection connection = new SqlConnection(connectionString))
        //    {
        //        connection.Open();

        //        // Fetch tax details using SQL query
        //        string taxDetailsQuery = "SELECT TaxStructureDtl_FormulaValue, TaxType_ID, TaxType_IncludeBaseAmt, TaxType_Seq, TaxType_TaxPerc " +
        //                                 "FROM GNM_TaxStructureDtl WHERE TaxStructure_ID = @TaxStructureID";
        //        using (SqlCommand command = new SqlCommand(taxDetailsQuery, connection))
        //        {
        //            command.Parameters.AddWithValue("@TaxStructureID", taxStructureID);
        //            using (SqlDataReader reader = command.ExecuteReader())
        //            {
        //                while (reader.Read())
        //                {
        //                    Input rowObj = new Input
        //                    {
        //                        TaxStructureDtl_Formula = reader["TaxStructureDtl_FormulaValue"].ToString(),
        //                        TaxType_ID = Convert.ToInt32(reader["TaxType_ID"]),
        //                        TaxType_IncludeBaseAmt = Convert.ToBoolean(reader["TaxType_IncludeBaseAmt"]),
        //                        TaxType_Seq = Convert.ToInt32(reader["TaxType_Seq"]),
        //                        TaxType_TaxPerc = Convert.ToDecimal(reader["TaxType_TaxPerc"])
        //                    };
        //                    inputList.Add(rowObj);
        //                }
        //            }
        //        }

        //        // Fetch TaxTypeName for each TaxType_ID
        //        foreach (var input in inputList)
        //        {
        //            string taxTypeNameQuery = "SELECT RefMasterDetail_Name FROM GNM_RefMasterDetail WHERE RefMasterDetail_ID = @TaxTypeID";
        //            using (SqlCommand command = new SqlCommand(taxTypeNameQuery, connection))
        //            {
        //                command.Parameters.AddWithValue("@TaxTypeID", input.TaxType_ID);
        //                input.TaxTypeName = command.ExecuteScalar()?.ToString();
        //            }
        //        }
        //    }

        //    double taxAmount = 0.00;
        //    for (int i = 0; i < inputList.Count; i++)
        //    {
        //        if (i == 0)
        //        {
        //            taxAmount = Convert.ToDouble((baseAmount * Convert.ToDouble(inputList[i].TaxType_TaxPerc)) / 100);
        //            listOutput.Add(new TaxOutput()
        //            {
        //                SequenceNo = inputList[i].TaxType_Seq,
        //                TaxTypeID = inputList[i].TaxType_ID,
        //                DiscountPercentage = inputList[i].TaxType_TaxPerc,
        //                TaxableAmount = Convert.ToDecimal(baseAmount),
        //                TaxAmount = Convert.ToDecimal(taxAmount.ToString("0.00")),
        //                TaxType = inputList[i].TaxTypeName,
        //                taxStructureID = taxStructureID
        //            });
        //        }
        //        else
        //        {
        //            string formula = inputList[i].TaxStructureDtl_Formula;
        //            if (inputList[i].TaxType_IncludeBaseAmt)
        //            {
        //                if (!string.IsNullOrEmpty(formula))
        //                {
        //                    for (int j = 0; j < listOutput.Count; j++)
        //                    {
        //                        string replacable = listOutput[j].TaxTypeID + listOutput[j].TaxType;
        //                        formula = formula.Replace(replacable, listOutput[j].TaxAmount.ToString());
        //                    }
        //                }
        //                double totalBaseAmount = !string.IsNullOrEmpty(formula) ? Convert.ToDouble(dt.Compute(formula, "")) + baseAmount : baseAmount;
        //                taxAmount = Convert.ToDouble((totalBaseAmount * Convert.ToDouble(inputList[i].TaxType_TaxPerc)) / 100);
        //                listOutput.Add(new TaxOutput()
        //                {
        //                    SequenceNo = inputList[i].TaxType_Seq,
        //                    TaxTypeID = inputList[i].TaxType_ID,
        //                    DiscountPercentage = inputList[i].TaxType_TaxPerc,
        //                    TaxableAmount = Convert.ToDecimal(totalBaseAmount),
        //                    TaxAmount = Convert.ToDecimal(taxAmount.ToString("0.00")),
        //                    TaxType = inputList[i].TaxTypeName,
        //                    taxStructureID = taxStructureID
        //                });
        //            }
        //            else
        //            {
        //                if (listOutput.Count > 0)
        //                {
        //                    if (!string.IsNullOrEmpty(formula))
        //                    {
        //                        for (int j = 0; j < listOutput.Count; j++)
        //                        {
        //                            string replacable = listOutput[j].TaxTypeID + listOutput[j].TaxType;
        //                            formula = formula.Replace(replacable, listOutput[j].TaxAmount.ToString());
        //                        }
        //                    }
        //                    taxAmount = !string.IsNullOrEmpty(formula) ? Convert.ToDouble((Convert.ToDouble(dt.Compute(formula, "")) * Convert.ToDouble(inputList[i].TaxType_TaxPerc)) / 100) : 0;
        //                    listOutput.Add(new TaxOutput()
        //                    {
        //                        SequenceNo = inputList[i].TaxType_Seq,
        //                        TaxTypeID = inputList[i].TaxType_ID,
        //                        DiscountPercentage = inputList[i].TaxType_TaxPerc,
        //                        TaxableAmount = !string.IsNullOrEmpty(formula) ? Convert.ToDecimal(dt.Compute(formula, "")) : Convert.ToDecimal(baseAmount),
        //                        TaxAmount = Convert.ToDecimal(taxAmount.ToString("0.00")),
        //                        TaxType = inputList[i].TaxTypeName,
        //                        taxStructureID = taxStructureID
        //                    });
        //                }
        //            }
        //        }
        //    }
        //    return listOutput;
        //}


        #region GETSAPDMSLOG
        /// <summary>
        /// Modifed by DK
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="category"></param>
        /// <param name="status"></param>
        /// <param name="_connectionString"></param>
        /// <returns></returns>
        public static async Task<List<SAPDMSLogEntry>> GetSAPDMSLogEntries(
            DateTime? startDate,
            DateTime? endDate,
            string category,
            string status,
            string _connectionString)
        {
            List<SAPDMSLogEntry> logs = new List<SAPDMSLogEntry>();

            try
            {
                using (SqlConnection connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    // Base query
                    string query = @"
                    SELECT r.*, s.Message ,
                 CASE 
                        WHEN s.TYPE = 1 THEN 'Success' 
                        ELSE 'Error' 
                    END AS TransactionStatus,
                   Status AS MasterStatus
                    FROM SAPDMS_API_Request_Log r 
                    LEFT JOIN SAPDMS_API_Status_Log s 
                    ON r.LogID = s.LogID
                    WHERE 1=1"; // Always true, helps in appending conditions dynamically

                    // Dictionary to store SQL parameters
                    Dictionary<string, object> parameters = new Dictionary<string, object>();

                    if (startDate.HasValue)
                    {
                        query += " AND RequestTimestamp >= @startDate";
                        parameters.Add("@startDate", startDate.Value);
                    }
                    if (endDate.HasValue)
                    {
                        query += " AND RequestTimestamp <= @endDate";
                        parameters.Add("@endDate", endDate.Value);
                    }
                    if (!string.IsNullOrEmpty(category))
                    {
                        query += " AND Category LIKE @category";
                        parameters.Add("@category", $"%{category}%"); // Adding wildcards properly
                    }

                    if (!string.IsNullOrEmpty(status))
                    {
                        query += " AND Status = @status";
                        parameters.Add("@status", status);
                    }

                    query += " ORDER BY RequestTimestamp DESC;";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        // Add only parameters that exist
                        foreach (var param in parameters)
                        {
                            command.Parameters.AddWithValue(param.Key, param.Value);
                        }

                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                logs.Add(new SAPDMSLogEntry
                                {
                                    SAPDMSYAN_RequestLogID = reader.GetInt32(reader.GetOrdinal("LogID")),
                                    Category = reader["Category"] as string,
                                    MasterStatus = reader["MasterStatus"] as string,
                                    Message = reader["Message"] as string,
                                    RequestTimestamp = reader.GetDateTime(reader.GetOrdinal("RequestTimestamp")),
                                    API_Endpoint = reader["API_Endpoint"] as string,
                                    ClientIP = reader["ClientIP"] as string,
                                    TransactionStatus = reader["TransactionStatus"] as string
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return logs;
        }



        #endregion
    }
    public class TaxStructureForExciseDuty
    {
        public int TaxStructure_ID { get; set; }
        public string TaxStructure_Name { get; set; }
        public bool IsDefault { get; set; }
    }
    public class SAPDMSLogEntry
    {
        public int SAPDMSYAN_RequestLogID { get; set; } // Primary Key
        public string Category { get; set; }            // Log Category
        public string MasterStatus { get; set; }              // Log Status
        public string Message { get; set; }             // Status Message (from joined table)
        public DateTime RequestTimestamp { get; set; }  // Request Timestamp
        public string API_Endpoint { get; set; }
        public string ClientIP { get; set; }
        public string TransactionStatus { get; set; }
    }
    public class PI_InterfaceList
    {

        public List<PIList> piList { get; set; } = new List<PIList>();
    }
    public class PIList
    {
        public string YanmarInvoiceNumber { get; set; }
        public DateTime YanmarInvoiceDate { get; set; }
        public string CurrencyValue { get; set; }
        public string Remarks { get; set; }
        public string SAP_SalesOrderNumber { get; set; }
        public string DMSPurchaseOrderNumber { get; set; }

        public decimal? DiscountPercentage { get; set; }
        public decimal? DiscountAmount { get; set; }
        public string PartNumber { get; set; }
        public decimal InvoiceQty { get; set; }
        public decimal InvoiceRate { get; set; }

        public decimal TaxAmount { get; set; }
    public decimal? PackingCharges { get; set; }
    public decimal? FreightCharges { get; set; }
        public string PackingChargesRemarks { get; set; }
        public string FreightChargesRemarks { get; set; }

        public PIList()
        {
            if (CurrencyValue == null || CurrencyValue == "")
            {
                CurrencyValue = "INR";
            }
            if (YanmarInvoiceDate == null)
            {
                YanmarInvoiceDate = DateTime.Now;
            }
        }
    }

}
