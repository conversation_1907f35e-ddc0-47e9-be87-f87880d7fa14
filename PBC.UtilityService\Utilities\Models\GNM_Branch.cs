using System;
using System.Collections.Generic;

namespace PBC.UtilityService.Utilities.Models
{
    public partial class GNM_Branch
    {
        public GNM_Branch()
        {
            this.GNM_BranchLocale = new HashSet<GNM_BranchLocale>();
        }

        public int Branch_ID { get; set; }
        public int Company_ID { get; set; }
        public string Branch_Name { get; set; }
        public string Branch_ShortName { get; set; }
        public string Branch_ZipCode { get; set; }
        public int Country_ID { get; set; }
        public int State_ID { get; set; }
        public string Branch_Phone { get; set; }
        public string Branch_Fax { get; set; }
        public bool Branch_HeadOffice { get; set; }
        public bool Branch_Active { get; set; }
        public string Branch_Address { get; set; }
        public string Branch_Location { get; set; }
        public string Branch_Email { get; set; }
        public string Branch_Mobile { get; set; }
        public bool? Branch_External { get; set; }
        public int? TimeZoneID { get; set; }
        public int? Region_ID { get; set; }
        public string Branch_Code { get; set; }
        public string Branch_TaxNumber { get; set; }
        public string Branch_RegistrationNumber { get; set; }
        public DateTime? Branch_EstablishedDate { get; set; }
        public string Branch_Description { get; set; }
        public string Branch_Currency { get; set; }
        public string Branch_Language { get; set; }
        public DateTime? Created_Date { get; set; }
        public int? Created_By { get; set; }
        public DateTime? Modified_Date { get; set; }
        public int? Modified_By { get; set; }

        public virtual GNM_Company GNM_Company { get; set; }
        public virtual ICollection<GNM_BranchLocale> GNM_BranchLocale { get; set; }
    }
}
