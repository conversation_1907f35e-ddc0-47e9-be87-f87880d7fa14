{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=CoreSharedAPI;Trusted_Connection=true;MultipleActiveResultSets=true", "FSMGOLD": "Data Source=(localdb)\\mssqllocaldb;Initial Catalog=CoreSharedAPI;Integrated Security=True;MultipleActiveResultSets=true"}, "LogError": "1", "ServicePorts": {"CoreService": "5001", "HelpdeskService": "5002", "UtilitiesService": "5003", "AggregatorService": "5004", "WorkflowService": "5005"}, "ServiceUrls": {"HelpdeskService": "http://localhost:5002", "UtilitiesService": "http://localhost:5003", "AggregatorService": "http://localhost:5004", "WorkflowService": "http://localhost:5005"}}