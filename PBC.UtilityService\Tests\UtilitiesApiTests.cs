using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using Xunit;

namespace PBC.UtilityService.Tests
{
    public class UtilitiesApiTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public UtilitiesApiTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory;
            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task CalculateTotalPages_ValidInput_ReturnsCorrectResult()
        {
            // Arrange
            long numberOfRecords = 100;
            int pageSize = 10;

            // Act
            var response = await _client.GetAsync($"/api/Utilities/calculate-total-pages?numberOfRecords={numberOfRecords}&pageSize={pageSize}");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<int>(content);
            Assert.Equal(10, result);
        }

        [Fact]
        public async Task CalculateTotalPages_WithRemainder_ReturnsCorrectResult()
        {
            // Arrange
            long numberOfRecords = 105;
            int pageSize = 10;

            // Act
            var response = await _client.GetAsync($"/api/Utilities/calculate-total-pages?numberOfRecords={numberOfRecords}&pageSize={pageSize}");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<int>(content);
            Assert.Equal(11, result);
        }

        [Fact]
        public async Task CalculateTotalPages_InvalidPageSize_ReturnsBadRequest()
        {
            // Arrange
            long numberOfRecords = 100;
            int pageSize = 0;

            // Act
            var response = await _client.GetAsync($"/api/Utilities/calculate-total-pages?numberOfRecords={numberOfRecords}&pageSize={pageSize}");

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.BadRequest, response.StatusCode);
        }

        [Fact]
        public async Task IsDate_ValidDate_ReturnsTrue()
        {
            // Arrange
            string date = "2024-01-01";

            // Act
            var response = await _client.GetAsync($"/api/Utilities/is-date?date={date}");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<bool>(content);
            Assert.True(result);
        }

        [Fact]
        public async Task IsDate_InvalidDate_ReturnsFalse()
        {
            // Arrange
            string date = "invalid-date";

            // Act
            var response = await _client.GetAsync($"/api/Utilities/is-date?date={date}");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<bool>(content);
            Assert.False(result);
        }

        [Fact]
        public async Task IsNumeric_ValidNumber_ReturnsTrue()
        {
            // Arrange
            var requestBody = JsonSerializer.Serialize("123");

            // Act
            var response = await _client.PostAsync("/api/Utilities/is-numeric", 
                new StringContent(requestBody, Encoding.UTF8, "application/json"));

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<bool>(content);
            Assert.True(result);
        }

        [Fact]
        public async Task IsNumeric_InvalidNumber_ReturnsFalse()
        {
            // Arrange
            var requestBody = JsonSerializer.Serialize("abc");

            // Act
            var response = await _client.PostAsync("/api/Utilities/is-numeric", 
                new StringContent(requestBody, Encoding.UTF8, "application/json"));

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<bool>(content);
            Assert.False(result);
        }

        [Fact]
        public async Task CalculateMD5Hash_ValidInput_ReturnsHash()
        {
            // Arrange
            string input = "test";

            // Act
            var response = await _client.GetAsync($"/api/Utilities/calculate-md5-hash?input={input}");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<string>(content);
            Assert.NotNull(result);
            Assert.NotEmpty(result);
            // MD5 hash of "test" should be "098f6bcd4621d373cade4e832627b4f6"
            Assert.Equal("098f6bcd4621d373cade4e832627b4f6", result);
        }

        [Fact]
        public async Task Message_ValidInput_ReturnsMessageList()
        {
            // Arrange
            string message = "Test message";

            // Act
            var response = await _client.GetAsync($"/api/Utilities/message?message={Uri.EscapeDataString(message)}");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<List<string>>(content);
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal(message, result[0]);
        }

        [Fact]
        public async Task GetMonthName_ValidInput_ReturnsMonthName()
        {
            // Arrange
            int monthId = 1;
            string culture = "Resource_en";

            // Act
            var response = await _client.GetAsync($"/api/Utilities/get-month-name?id={monthId}&culture={culture}");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<string>(content);
            Assert.NotNull(result);
            // Note: This might return empty string if resource files are not set up
        }

        [Fact]
        public async Task GetMonthName_InvalidMonthId_ReturnsBadRequest()
        {
            // Arrange
            int monthId = 13; // Invalid month
            string culture = "Resource_en";

            // Act
            var response = await _client.GetAsync($"/api/Utilities/get-month-name?id={monthId}&culture={culture}");

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.BadRequest, response.StatusCode);
        }

        [Fact]
        public async Task GetPriority_ValidInput_ReturnsPriorityName()
        {
            // Arrange
            byte priorityId = 1;
            string culture = "Resource_en";

            // Act
            var response = await _client.GetAsync($"/api/Utilities/get-priority?id={priorityId}&culture={culture}");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<string>(content);
            Assert.NotNull(result);
            // Note: This might return empty string if resource files are not set up
        }

        [Fact]
        public async Task GetPriority_InvalidPriorityId_ReturnsBadRequest()
        {
            // Arrange
            byte priorityId = 5; // Invalid priority
            string culture = "Resource_en";

            // Act
            var response = await _client.GetAsync($"/api/Utilities/get-priority?id={priorityId}&culture={culture}");

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.BadRequest, response.StatusCode);
        }
    }
}
