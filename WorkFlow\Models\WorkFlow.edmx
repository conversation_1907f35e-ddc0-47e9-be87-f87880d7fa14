﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
    <Schema Namespace="WorkFlowModel.Store" Alias="Self" Provider="System.Data.SqlClient" ProviderManifestToken="2008" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityContainer Name="WorkFlowModelStoreContainer">
          <EntitySet Name="GNM_Email" EntityType="WorkFlowModel.Store.GNM_Email" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_Sms" EntityType="WorkFlowModel.Store.GNM_Sms" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_WFAction" EntityType="WorkFlowModel.Store.GNM_WFAction" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_WFActionLocale" EntityType="WorkFlowModel.Store.GNM_WFActionLocale" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_WFCase_Progress" EntityType="WorkFlowModel.Store.GNM_WFCase_Progress" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_WFChildActions" EntityType="WorkFlowModel.Store.GNM_WFChildActions" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_WFField" EntityType="WorkFlowModel.Store.GNM_WFField" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_WFFieldValue" EntityType="WorkFlowModel.Store.GNM_WFFieldValue" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_WFRole" EntityType="WorkFlowModel.Store.GNM_WFRole" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_WFRoleLocale" EntityType="WorkFlowModel.Store.GNM_WFRoleLocale" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_WFRoleUser" EntityType="WorkFlowModel.Store.GNM_WFRoleUser" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_WFStepLink" EntityType="WorkFlowModel.Store.GNM_WFStepLink" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_WFSteps" EntityType="WorkFlowModel.Store.GNM_WFSteps" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_WFStepsLocale" EntityType="WorkFlowModel.Store.GNM_WFStepsLocale" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_WFStepStatus" EntityType="WorkFlowModel.Store.GNM_WFStepStatus" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_WFStepStatusLocale" EntityType="WorkFlowModel.Store.GNM_WFStepStatusLocale" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_WFStepType" EntityType="WorkFlowModel.Store.GNM_WFStepType" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_WorkFlow" EntityType="WorkFlowModel.Store.GNM_WorkFlow" store:Type="Tables" Schema="dbo" />
          <EntitySet Name="GNM_WorkFlowParent" EntityType="WorkFlowModel.Store.GNM_WorkFlowParent" store:Type="Tables" Schema="dbo" />
          <AssociationSet Name="FK__GNM_WFAct__GNM_WorkFlow" Association="WorkFlowModel.Store.FK__GNM_WFAct__GNM_WorkFlow">
            <End Role="GNM_WorkFlow" EntitySet="GNM_WorkFlow" />
            <End Role="GNM_WFActionLocale" EntitySet="GNM_WFActionLocale" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFAct__WorkF__656C112C" Association="WorkFlowModel.Store.FK__GNM_WFAct__WorkF__656C112C">
            <End Role="GNM_WorkFlow" EntitySet="GNM_WorkFlow" />
            <End Role="GNM_WFAction" EntitySet="GNM_WFAction" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFActL__GNM_WFAction" Association="WorkFlowModel.Store.FK__GNM_WFActL__GNM_WFAction">
            <End Role="GNM_WFAction" EntitySet="GNM_WFAction" />
            <End Role="GNM_WFActionLocale" EntitySet="GNM_WFActionLocale" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFRol__WFRol__4E0988E7" Association="WorkFlowModel.Store.FK__GNM_WFRol__WFRol__4E0988E7">
            <End Role="GNM_WFRole" EntitySet="GNM_WFRole" />
            <End Role="GNM_WFRoleUser" EntitySet="GNM_WFRoleUser" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFRol__WorkF__475C8B58" Association="WorkFlowModel.Store.FK__GNM_WFRol__WorkF__475C8B58">
            <End Role="GNM_WorkFlow" EntitySet="GNM_WorkFlow" />
            <End Role="GNM_WFRole" EntitySet="GNM_WFRole" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFRoleLocale__WFRole_ID" Association="WorkFlowModel.Store.FK__GNM_WFRoleLocale__WFRole_ID">
            <End Role="GNM_WFRole" EntitySet="GNM_WFRole" />
            <End Role="GNM_WFRoleLocale" EntitySet="GNM_WFRoleLocale" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFSte__Addre__5B638405" Association="WorkFlowModel.Store.FK__GNM_WFSte__Addre__5B638405">
            <End Role="GNM_WFRole" EntitySet="GNM_WFRole" />
            <End Role="GNM_WFStepLink" EntitySet="GNM_WFStepLink" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFSte__FrmWF__5D4BCC77" Association="WorkFlowModel.Store.FK__GNM_WFSte__FrmWF__5D4BCC77">
            <End Role="GNM_WFSteps" EntitySet="GNM_WFSteps" />
            <End Role="GNM_WFStepLink" EntitySet="GNM_WFStepLink" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFSte__GNM_WFStepsLocale" Association="WorkFlowModel.Store.FK__GNM_WFSte__GNM_WFStepsLocale">
            <End Role="GNM_WFSteps" EntitySet="GNM_WFSteps" />
            <End Role="GNM_WFStepsLocale" EntitySet="GNM_WFStepsLocale" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFSte__ToWFS__5E3FF0B0" Association="WorkFlowModel.Store.FK__GNM_WFSte__ToWFS__5E3FF0B0">
            <End Role="GNM_WFSteps" EntitySet="GNM_WFSteps" />
            <End Role="GNM_WFStepLink" EntitySet="GNM_WFStepLink" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFSte__WFAct__5F3414E9" Association="WorkFlowModel.Store.FK__GNM_WFSte__WFAct__5F3414E9">
            <End Role="GNM_WFAction" EntitySet="GNM_WFAction" />
            <End Role="GNM_WFStepLink" EntitySet="GNM_WFStepLink" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFSte__WFSte__53C2623D" Association="WorkFlowModel.Store.FK__GNM_WFSte__WFSte__53C2623D">
            <End Role="GNM_WFStepType" EntitySet="GNM_WFStepType" />
            <End Role="GNM_WFSteps" EntitySet="GNM_WFSteps" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFSte__WFSte__54B68676" Association="WorkFlowModel.Store.FK__GNM_WFSte__WFSte__54B68676">
            <End Role="GNM_WFStepStatus" EntitySet="GNM_WFStepStatus" />
            <End Role="GNM_WFSteps" EntitySet="GNM_WFSteps" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFSte__WorkF__55AAAAAF" Association="WorkFlowModel.Store.FK__GNM_WFSte__WorkF__55AAAAAF">
            <End Role="GNM_WorkFlow" EntitySet="GNM_WorkFlow" />
            <End Role="GNM_WFSteps" EntitySet="GNM_WFSteps" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFSte__WorkF__60283922" Association="WorkFlowModel.Store.FK__GNM_WFSte__WorkF__60283922">
            <End Role="GNM_WorkFlow" EntitySet="GNM_WorkFlow" />
            <End Role="GNM_WFStepLink" EntitySet="GNM_WFStepLink" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFStepStatusLocale__GNM_WFStepStatus" Association="WorkFlowModel.Store.FK__GNM_WFStepStatusLocale__GNM_WFStepStatus">
            <End Role="GNM_WFStepStatus" EntitySet="GNM_WFStepStatus" />
            <End Role="GNM_WFStepStatusLocale" EntitySet="GNM_WFStepStatusLocale" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_WFField_WFField_ID" Association="WorkFlowModel.Store.FK_GNM_WFField_WFField_ID">
            <End Role="GNM_WFField" EntitySet="GNM_WFField" />
            <End Role="GNM_WFFieldValue" EntitySet="GNM_WFFieldValue" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_WorkFlow_WF_ID" Association="WorkFlowModel.Store.FK_GNM_WorkFlow_WF_ID">
            <End Role="GNM_WorkFlow" EntitySet="GNM_WorkFlow" />
            <End Role="GNM_WFFieldValue" EntitySet="GNM_WFFieldValue" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_WorkFlow_WorkFlow_ID" Association="WorkFlowModel.Store.FK_GNM_WorkFlow_WorkFlow_ID">
            <End Role="GNM_WorkFlow" EntitySet="GNM_WorkFlow" />
            <End Role="GNM_WFField" EntitySet="GNM_WFField" />
          </AssociationSet>
        </EntityContainer>
        <EntityType Name="GNM_Email">
          <Key>
            <PropertyRef Name="Email_ID" />
          </Key>
          <Property Name="Email_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="Email_Subject" Type="nvarchar" MaxLength="500" />
          <Property Name="Email_Body" Type="nvarchar(max)" />
          <Property Name="Email_To" Type="varchar" MaxLength="500" />
          <Property Name="Email_cc" Type="varchar" MaxLength="500" />
          <Property Name="Email_Bcc" Type="varchar" MaxLength="500" />
          <Property Name="Email_Queue_Date" Type="datetime" />
          <Property Name="Email_Sent_Date" Type="datetime" />
          <Property Name="Email_SentStatus" Type="bit" Nullable="false" />
          <Property Name="Email_Attachments" Type="nvarchar" MaxLength="500" />
          <Property Name="Email_IsError" Type="bit" />
          <Property Name="NoOfAttempts" Type="tinyint" />
        </EntityType>
        <EntityType Name="GNM_Sms">
          <Key>
            <PropertyRef Name="Sms_ID" />
          </Key>
          <Property Name="Sms_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="Sms_Text" Type="nvarchar" MaxLength="200" />
          <Property Name="Sms_Mobile_Number" Type="varchar" MaxLength="30" />
          <Property Name="Sms_Queue_Date" Type="datetime" />
          <Property Name="Sms_Sent_Date" Type="datetime" />
          <Property Name="Sms_SentStatus" Type="bit" Nullable="false" />
          <Property Name="Template_ID" Type="int" Nullable="false" />
          <Property Name="Parameter1_value" Type="varchar" MaxLength="100" />
          <Property Name="Parameter2_value" Type="varchar" MaxLength="100" />
          <Property Name="Parameter3_value" Type="varchar" MaxLength="100" />
          <Property Name="Parameter4_value" Type="varchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="GNM_WFAction">
          <Key>
            <PropertyRef Name="WFAction_ID" />
          </Key>
          <Property Name="WFAction_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="WorkFlow_ID" Type="int" Nullable="false" />
          <Property Name="WFAction_Name" Type="varchar" Nullable="false" MaxLength="30" />
          <Property Name="ActionCode" Type="varchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="GNM_WFActionLocale">
          <Key>
            <PropertyRef Name="WFActionLocale_ID" />
          </Key>
          <Property Name="WFActionLocale_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="WorkFlow_ID" Type="int" Nullable="false" />
          <Property Name="WFAction_ID" Type="int" Nullable="false" />
          <Property Name="Language_ID" Type="int" Nullable="false" />
          <Property Name="WFAction_Name" Type="nvarchar" Nullable="false" MaxLength="100" />
          <Property Name="ActionCode" Type="nvarchar" Nullable="false" MaxLength="100" />
        </EntityType>
        <EntityType Name="GNM_WFCase_Progress">
          <Key>
            <PropertyRef Name="WFCaseProgress_ID" />
          </Key>
          <Property Name="WFCaseProgress_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="WorkFlow_ID" Type="int" Nullable="false" />
          <Property Name="Transaction_ID" Type="int" Nullable="false" />
          <Property Name="WFSteps_ID" Type="int" Nullable="false" />
          <Property Name="Addresse_ID" Type="int" />
          <Property Name="Addresse_Flag" Type="tinyint" Nullable="false" />
          <Property Name="Received_Time" Type="datetime" Nullable="false" />
          <Property Name="Actioned_By" Type="int" />
          <Property Name="Action_Time" Type="datetime" />
          <Property Name="Action_Chosen" Type="int" />
          <Property Name="Action_Remarks" Type="varchar" MaxLength="500" />
          <Property Name="Locked_Ind" Type="bit" />
          <Property Name="WFNextStep_ID" Type="int" />
        </EntityType>
        <EntityType Name="GNM_WFChildActions">
          <Key>
            <PropertyRef Name="ChildActions_ID" />
          </Key>
          <Property Name="ChildActions_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="Object_ID" Type="int" Nullable="false" />
          <Property Name="Actions_Name" Type="varchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="GNM_WFField">
          <Key>
            <PropertyRef Name="WFField_ID" />
          </Key>
          <Property Name="WFField_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="WorkFlow_ID" Type="int" Nullable="false" />
          <Property Name="WorkFlowFieldName" Type="varchar" Nullable="false" MaxLength="100" />
        </EntityType>
        <EntityType Name="GNM_WFFieldValue">
          <Key>
            <PropertyRef Name="WFFieldValue_ID" />
          </Key>
          <Property Name="WFFieldValue_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="WFField_ID" Type="int" Nullable="false" />
          <Property Name="WorkFlow_ID" Type="int" Nullable="false" />
          <Property Name="Company_ID" Type="int" Nullable="false" />
          <Property Name="Transaction_ID" Type="int" Nullable="false" />
          <Property Name="WorkFlowFieldValue" Type="varchar" Nullable="false" MaxLength="100" />
        </EntityType>
        <EntityType Name="GNM_WFRole">
          <Key>
            <PropertyRef Name="WFRole_ID" />
          </Key>
          <Property Name="WFRole_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="WorkFlow_ID" Type="int" Nullable="false" />
          <Property Name="WFRole_Name" Type="varchar" Nullable="false" MaxLength="50" />
          <Property Name="WfRole_IsAdmin" Type="bit" Nullable="false" />
          <Property Name="WfRole_AutoAllocationAllowed" Type="bit" Nullable="false" />
          <Property Name="WFRole_IsRoleExternal" Type="bit" />
          <Property Name="WFRole_ExternalCompany_ID" Type="int" />
        </EntityType>
        <EntityType Name="GNM_WFRoleLocale">
          <Key>
            <PropertyRef Name="WFRoleLocale_ID" />
          </Key>
          <Property Name="WFRoleLocale_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="WFRole_ID" Type="int" Nullable="false" />
          <Property Name="WFRole_Name" Type="nvarchar" Nullable="false" MaxLength="100" />
          <Property Name="Language_ID" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="GNM_WFRoleUser">
          <Key>
            <PropertyRef Name="WFRoleUser_ID" />
          </Key>
          <Property Name="WFRoleUser_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="WFRole_ID" Type="int" Nullable="false" />
          <Property Name="UserID" Type="int" Nullable="false" />
          <Property Name="ApprovalLimit" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="GNM_WFStepLink">
          <Key>
            <PropertyRef Name="WFStepLink_ID" />
          </Key>
          <Property Name="WFStepLink_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="WorkFlow_ID" Type="int" Nullable="false" />
          <Property Name="Company_ID" Type="int" Nullable="false" />
          <Property Name="FrmWFSteps_ID" Type="int" Nullable="false" />
          <Property Name="WFAction_ID" Type="int" Nullable="false" />
          <Property Name="ToWFSteps_ID" Type="int" Nullable="false" />
          <Property Name="Addresse_WFRole_ID" Type="int" />
          <Property Name="Addresse_Flag" Type="tinyint" Nullable="false" />
          <Property Name="IsSMSSentToCustomer" Type="bit" Nullable="false" />
          <Property Name="IsEmailSentToCustomer" Type="bit" Nullable="false" />
          <Property Name="IsSMSSentToAddressee" Type="bit" Nullable="false" />
          <Property Name="IsEmailSentToAddresse" Type="bit" Nullable="false" />
          <Property Name="AutoAllocationAllowed" Type="bit" Nullable="false" />
          <Property Name="IsVersionEnabled" Type="bit" Nullable="false" />
          <Property Name="InvokeParentWF_ID" Type="int" />
          <Property Name="InvokeParentWFLink_ID" Type="int" />
          <Property Name="InvokeChildObject_ID" Type="int" />
          <Property Name="InvokeChildObjectAction" Type="int" />
          <Property Name="WFField_ID" Type="int" />
          <Property Name="AutoCondition" Type="varchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="GNM_WFSteps">
          <Key>
            <PropertyRef Name="WFSteps_ID" />
          </Key>
          <Property Name="WFSteps_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="WorkFlow_ID" Type="int" Nullable="false" />
          <Property Name="WFStep_Name" Type="varchar" Nullable="false" MaxLength="50" />
          <Property Name="WFStepType_ID" Type="int" Nullable="false" />
          <Property Name="WFStepStatus_ID" Type="int" Nullable="false" />
          <Property Name="WFStep_IsActive" Type="bit" Nullable="false" />
          <Property Name="BranchCode" Type="varchar" MaxLength="10" />
        </EntityType>
        <EntityType Name="GNM_WFStepsLocale">
          <Key>
            <PropertyRef Name="WFStepsLocale_ID" />
          </Key>
          <Property Name="WFStepsLocale_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="WFSteps_ID" Type="int" Nullable="false" />
          <Property Name="WFStep_Name" Type="nvarchar" Nullable="false" MaxLength="200" />
          <Property Name="Language_ID" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="GNM_WFStepStatus">
          <Key>
            <PropertyRef Name="WFStepStatus_ID" />
          </Key>
          <Property Name="WFStepStatus_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="WFStepStatus_Nm" Type="varchar" Nullable="false" MaxLength="100" />
          <Property Name="StepStatusCode" Type="varchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="GNM_WFStepStatusLocale">
          <Key>
            <PropertyRef Name="WFStepStatusLocale_ID" />
          </Key>
          <Property Name="WFStepStatusLocale_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="WFStepStatus_ID" Type="int" Nullable="false" />
          <Property Name="Language_ID" Type="int" Nullable="false" />
          <Property Name="WFStepStatus_Nm" Type="nvarchar" Nullable="false" MaxLength="200" />
          <Property Name="StepStatusCode" Type="varchar" Nullable="false" MaxLength="100" />
        </EntityType>
        <EntityType Name="GNM_WFStepType">
          <Key>
            <PropertyRef Name="WFStepType_ID" />
          </Key>
          <Property Name="WFStepType_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="WFStepType_Nm" Type="varchar" Nullable="false" MaxLength="100" />
        </EntityType>
        <EntityType Name="GNM_WorkFlow">
          <Key>
            <PropertyRef Name="WorkFlow_ID" />
          </Key>
          <Property Name="WorkFlow_ID" Type="int" Nullable="false" />
          <Property Name="WorkFlow_Name" Type="varchar" Nullable="false" MaxLength="100" />
          <Property Name="AllQueue_Filter_IsBranch" Type="bit" />
        </EntityType>
        <EntityType Name="GNM_WorkFlowParent">
          <Key>
            <PropertyRef Name="WorkFlowParent_ID" />
          </Key>
          <Property Name="WorkFlowParent_ID" Type="int" Nullable="false" StoreGeneratedPattern="Identity" />
          <Property Name="WorkFlow_ID" Type="int" />
          <Property Name="TXN_Table" Type="varchar" MaxLength="50" />
          <Property Name="Reference_Column" Type="varchar" MaxLength="50" />
          <Property Name="Parent_Table" Type="varchar" MaxLength="50" />
          <Property Name="Parent_Column" Type="varchar" MaxLength="50" />
          <Property Name="Parent_Level" Type="tinyint" />
          <Property Name="Parent_WFID" Type="tinyint" />
          <Property Name="PrimaryKey_Column" Type="varchar" MaxLength="50" />
        </EntityType>
        <Association Name="FK__GNM_WFAct__GNM_WorkFlow">
          <End Role="GNM_WorkFlow" Type="WorkFlowModel.Store.GNM_WorkFlow" Multiplicity="1" />
          <End Role="GNM_WFActionLocale" Type="WorkFlowModel.Store.GNM_WFActionLocale" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WorkFlow">
              <PropertyRef Name="WorkFlow_ID" />
            </Principal>
            <Dependent Role="GNM_WFActionLocale">
              <PropertyRef Name="WorkFlow_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFAct__WorkF__656C112C">
          <End Role="GNM_WorkFlow" Type="WorkFlowModel.Store.GNM_WorkFlow" Multiplicity="1" />
          <End Role="GNM_WFAction" Type="WorkFlowModel.Store.GNM_WFAction" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WorkFlow">
              <PropertyRef Name="WorkFlow_ID" />
            </Principal>
            <Dependent Role="GNM_WFAction">
              <PropertyRef Name="WorkFlow_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFActL__GNM_WFAction">
          <End Role="GNM_WFAction" Type="WorkFlowModel.Store.GNM_WFAction" Multiplicity="1" />
          <End Role="GNM_WFActionLocale" Type="WorkFlowModel.Store.GNM_WFActionLocale" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WFAction">
              <PropertyRef Name="WFAction_ID" />
            </Principal>
            <Dependent Role="GNM_WFActionLocale">
              <PropertyRef Name="WFAction_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFRol__WFRol__4E0988E7">
          <End Role="GNM_WFRole" Type="WorkFlowModel.Store.GNM_WFRole" Multiplicity="1" />
          <End Role="GNM_WFRoleUser" Type="WorkFlowModel.Store.GNM_WFRoleUser" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WFRole">
              <PropertyRef Name="WFRole_ID" />
            </Principal>
            <Dependent Role="GNM_WFRoleUser">
              <PropertyRef Name="WFRole_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFRol__WorkF__475C8B58">
          <End Role="GNM_WorkFlow" Type="WorkFlowModel.Store.GNM_WorkFlow" Multiplicity="1" />
          <End Role="GNM_WFRole" Type="WorkFlowModel.Store.GNM_WFRole" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WorkFlow">
              <PropertyRef Name="WorkFlow_ID" />
            </Principal>
            <Dependent Role="GNM_WFRole">
              <PropertyRef Name="WorkFlow_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFRoleLocale__WFRole_ID">
          <End Role="GNM_WFRole" Type="WorkFlowModel.Store.GNM_WFRole" Multiplicity="1" />
          <End Role="GNM_WFRoleLocale" Type="WorkFlowModel.Store.GNM_WFRoleLocale" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WFRole">
              <PropertyRef Name="WFRole_ID" />
            </Principal>
            <Dependent Role="GNM_WFRoleLocale">
              <PropertyRef Name="WFRole_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFSte__Addre__5B638405">
          <End Role="GNM_WFRole" Type="WorkFlowModel.Store.GNM_WFRole" Multiplicity="0..1" />
          <End Role="GNM_WFStepLink" Type="WorkFlowModel.Store.GNM_WFStepLink" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WFRole">
              <PropertyRef Name="WFRole_ID" />
            </Principal>
            <Dependent Role="GNM_WFStepLink">
              <PropertyRef Name="Addresse_WFRole_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFSte__FrmWF__5D4BCC77">
          <End Role="GNM_WFSteps" Type="WorkFlowModel.Store.GNM_WFSteps" Multiplicity="1" />
          <End Role="GNM_WFStepLink" Type="WorkFlowModel.Store.GNM_WFStepLink" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WFSteps">
              <PropertyRef Name="WFSteps_ID" />
            </Principal>
            <Dependent Role="GNM_WFStepLink">
              <PropertyRef Name="FrmWFSteps_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFSte__GNM_WFStepsLocale">
          <End Role="GNM_WFSteps" Type="WorkFlowModel.Store.GNM_WFSteps" Multiplicity="1" />
          <End Role="GNM_WFStepsLocale" Type="WorkFlowModel.Store.GNM_WFStepsLocale" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WFSteps">
              <PropertyRef Name="WFSteps_ID" />
            </Principal>
            <Dependent Role="GNM_WFStepsLocale">
              <PropertyRef Name="WFSteps_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFSte__ToWFS__5E3FF0B0">
          <End Role="GNM_WFSteps" Type="WorkFlowModel.Store.GNM_WFSteps" Multiplicity="1" />
          <End Role="GNM_WFStepLink" Type="WorkFlowModel.Store.GNM_WFStepLink" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WFSteps">
              <PropertyRef Name="WFSteps_ID" />
            </Principal>
            <Dependent Role="GNM_WFStepLink">
              <PropertyRef Name="ToWFSteps_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFSte__WFAct__5F3414E9">
          <End Role="GNM_WFAction" Type="WorkFlowModel.Store.GNM_WFAction" Multiplicity="1" />
          <End Role="GNM_WFStepLink" Type="WorkFlowModel.Store.GNM_WFStepLink" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WFAction">
              <PropertyRef Name="WFAction_ID" />
            </Principal>
            <Dependent Role="GNM_WFStepLink">
              <PropertyRef Name="WFAction_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFSte__WFSte__53C2623D">
          <End Role="GNM_WFStepType" Type="WorkFlowModel.Store.GNM_WFStepType" Multiplicity="1" />
          <End Role="GNM_WFSteps" Type="WorkFlowModel.Store.GNM_WFSteps" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WFStepType">
              <PropertyRef Name="WFStepType_ID" />
            </Principal>
            <Dependent Role="GNM_WFSteps">
              <PropertyRef Name="WFStepType_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFSte__WFSte__54B68676">
          <End Role="GNM_WFStepStatus" Type="WorkFlowModel.Store.GNM_WFStepStatus" Multiplicity="1" />
          <End Role="GNM_WFSteps" Type="WorkFlowModel.Store.GNM_WFSteps" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WFStepStatus">
              <PropertyRef Name="WFStepStatus_ID" />
            </Principal>
            <Dependent Role="GNM_WFSteps">
              <PropertyRef Name="WFStepStatus_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFSte__WorkF__55AAAAAF">
          <End Role="GNM_WorkFlow" Type="WorkFlowModel.Store.GNM_WorkFlow" Multiplicity="1" />
          <End Role="GNM_WFSteps" Type="WorkFlowModel.Store.GNM_WFSteps" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WorkFlow">
              <PropertyRef Name="WorkFlow_ID" />
            </Principal>
            <Dependent Role="GNM_WFSteps">
              <PropertyRef Name="WorkFlow_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFSte__WorkF__60283922">
          <End Role="GNM_WorkFlow" Type="WorkFlowModel.Store.GNM_WorkFlow" Multiplicity="1" />
          <End Role="GNM_WFStepLink" Type="WorkFlowModel.Store.GNM_WFStepLink" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WorkFlow">
              <PropertyRef Name="WorkFlow_ID" />
            </Principal>
            <Dependent Role="GNM_WFStepLink">
              <PropertyRef Name="WorkFlow_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFStepStatusLocale__GNM_WFStepStatus">
          <End Role="GNM_WFStepStatus" Type="WorkFlowModel.Store.GNM_WFStepStatus" Multiplicity="1" />
          <End Role="GNM_WFStepStatusLocale" Type="WorkFlowModel.Store.GNM_WFStepStatusLocale" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WFStepStatus">
              <PropertyRef Name="WFStepStatus_ID" />
            </Principal>
            <Dependent Role="GNM_WFStepStatusLocale">
              <PropertyRef Name="WFStepStatus_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_WFField_WFField_ID">
          <End Role="GNM_WFField" Type="WorkFlowModel.Store.GNM_WFField" Multiplicity="1" />
          <End Role="GNM_WFFieldValue" Type="WorkFlowModel.Store.GNM_WFFieldValue" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WFField">
              <PropertyRef Name="WFField_ID" />
            </Principal>
            <Dependent Role="GNM_WFFieldValue">
              <PropertyRef Name="WFField_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_WorkFlow_WF_ID">
          <End Role="GNM_WorkFlow" Type="WorkFlowModel.Store.GNM_WorkFlow" Multiplicity="1" />
          <End Role="GNM_WFFieldValue" Type="WorkFlowModel.Store.GNM_WFFieldValue" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WorkFlow">
              <PropertyRef Name="WorkFlow_ID" />
            </Principal>
            <Dependent Role="GNM_WFFieldValue">
              <PropertyRef Name="WorkFlow_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_WorkFlow_WorkFlow_ID">
          <End Role="GNM_WorkFlow" Type="WorkFlowModel.Store.GNM_WorkFlow" Multiplicity="1" />
          <End Role="GNM_WFField" Type="WorkFlowModel.Store.GNM_WFField" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WorkFlow">
              <PropertyRef Name="WorkFlow_ID" />
            </Principal>
            <Dependent Role="GNM_WFField">
              <PropertyRef Name="WorkFlow_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
      </Schema></edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="WorkFlowModel" Alias="Self" p1:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:p1="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityContainer Name="WorkFlowEntity" p1:LazyLoadingEnabled="true">
          <EntitySet Name="WF_Sms" EntityType="WorkFlowModel.WF_Sms" />
          <EntitySet Name="WF_WFAction" EntityType="WorkFlowModel.WF_WFAction" />
          <EntitySet Name="WF_WFCase_Progress" EntityType="WorkFlowModel.WF_WFCase_Progress" />
          <EntitySet Name="WF_WFField" EntityType="WorkFlowModel.WF_WFField" />
          <EntitySet Name="WF_WFFieldValue" EntityType="WorkFlowModel.WF_WFFieldValue" />
          <EntitySet Name="WF_WFRole" EntityType="WorkFlowModel.WF_WFRole" />
          <EntitySet Name="WF_WFRoleUser" EntityType="WorkFlowModel.WF_WFRoleUser" />
          <EntitySet Name="WF_WFSteps" EntityType="WorkFlowModel.WF_WFSteps" />
          <EntitySet Name="WF_WFStepStatus" EntityType="WorkFlowModel.WF_WFStepStatus" />
          <EntitySet Name="WF_WFStepType" EntityType="WorkFlowModel.WF_WFStepType" />
          <EntitySet Name="WF_WorkFlow" EntityType="WorkFlowModel.WF_WorkFlow" />
          <AssociationSet Name="FK__GNM_WFAct__WorkF__656C112C" Association="WorkFlowModel.FK__GNM_WFAct__WorkF__656C112C">
            <End Role="GNM_WorkFlow" EntitySet="WF_WorkFlow" />
            <End Role="GNM_WFAction" EntitySet="WF_WFAction" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_WFField_WFField_ID" Association="WorkFlowModel.FK_GNM_WFField_WFField_ID">
            <End Role="GNM_WFField" EntitySet="WF_WFField" />
            <End Role="GNM_WFFieldValue" EntitySet="WF_WFFieldValue" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_WorkFlow_WorkFlow_ID" Association="WorkFlowModel.FK_GNM_WorkFlow_WorkFlow_ID">
            <End Role="GNM_WorkFlow" EntitySet="WF_WorkFlow" />
            <End Role="GNM_WFField" EntitySet="WF_WFField" />
          </AssociationSet>
          <AssociationSet Name="FK_GNM_WorkFlow_WF_ID" Association="WorkFlowModel.FK_GNM_WorkFlow_WF_ID">
            <End Role="GNM_WorkFlow" EntitySet="WF_WorkFlow" />
            <End Role="GNM_WFFieldValue" EntitySet="WF_WFFieldValue" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFRol__WFRol__4E0988E7" Association="WorkFlowModel.FK__GNM_WFRol__WFRol__4E0988E7">
            <End EntitySet="WF_WFRole" Role="GNM_WFRole" />
            <End EntitySet="WF_WFRoleUser" Role="GNM_WFRoleUser" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFRol__WorkF__475C8B58" Association="WorkFlowModel.FK__GNM_WFRol__WorkF__475C8B58">
            <End Role="GNM_WorkFlow" EntitySet="WF_WorkFlow" />
            <End Role="GNM_WFRole" EntitySet="WF_WFRole" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFSte__WFSte__53C2623D" Association="WorkFlowModel.FK__GNM_WFSte__WFSte__53C2623D">
            <End Role="GNM_WFStepType" EntitySet="WF_WFStepType" />
            <End Role="GNM_WFSteps" EntitySet="WF_WFSteps" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFSte__WFSte__54B68676" Association="WorkFlowModel.FK__GNM_WFSte__WFSte__54B68676">
            <End Role="GNM_WFStepStatus" EntitySet="WF_WFStepStatus" />
            <End Role="GNM_WFSteps" EntitySet="WF_WFSteps" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFSte__WorkF__55AAAAAF" Association="WorkFlowModel.FK__GNM_WFSte__WorkF__55AAAAAF">
            <End Role="GNM_WorkFlow" EntitySet="WF_WorkFlow" />
            <End Role="GNM_WFSteps" EntitySet="WF_WFSteps" />
          </AssociationSet>
          <EntitySet Name="GNM_WorkFlowParent" EntityType="WorkFlowModel.GNM_WorkFlowParent" />
          <EntitySet Name="WF_WFChildActions" EntityType="WorkFlowModel.WF_WFChildActions" />
          <EntitySet Name="WF_WFStepLink" EntityType="WorkFlowModel.WF_WFStepLink" />
          <AssociationSet Name="FK__GNM_WFSte__WFAct__5F3414E9" Association="WorkFlowModel.FK__GNM_WFSte__WFAct__5F3414E9">
            <End Role="WF_WFAction" EntitySet="WF_WFAction" />
            <End Role="GNM_WFStepLink" EntitySet="WF_WFStepLink" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFSte__Addre__5B638405" Association="WorkFlowModel.FK__GNM_WFSte__Addre__5B638405">
            <End Role="WF_WFRole" EntitySet="WF_WFRole" />
            <End Role="GNM_WFStepLink" EntitySet="WF_WFStepLink" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFSte__FrmWF__5D4BCC77" Association="WorkFlowModel.FK__GNM_WFSte__FrmWF__5D4BCC77">
            <End Role="WF_WFSteps" EntitySet="WF_WFSteps" />
            <End Role="GNM_WFStepLink" EntitySet="WF_WFStepLink" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFSte__ToWFS__5E3FF0B0" Association="WorkFlowModel.FK__GNM_WFSte__ToWFS__5E3FF0B0">
            <End Role="WF_WFSteps" EntitySet="WF_WFSteps" />
            <End Role="GNM_WFStepLink" EntitySet="WF_WFStepLink" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFSte__WorkF__60283922" Association="WorkFlowModel.FK__GNM_WFSte__WorkF__60283922">
            <End Role="WF_WorkFlow" EntitySet="WF_WorkFlow" />
            <End Role="GNM_WFStepLink" EntitySet="WF_WFStepLink" />
          </AssociationSet>
          <EntitySet Name="WF_Email" EntityType="WorkFlowModel.WF_Email" />
          <EntitySet Name="WF_WFActionLocale" EntityType="WorkFlowModel.WF_WFActionLocale" />
          <EntitySet Name="WF_WFStepsLocale" EntityType="WorkFlowModel.WF_WFStepsLocale" />
          <EntitySet Name="WF_WFStepStatusLocale" EntityType="WorkFlowModel.WF_WFStepStatusLocale" />
          <AssociationSet Name="FK__GNM_WFActL__GNM_WFAction" Association="WorkFlowModel.FK__GNM_WFActL__GNM_WFAction">
            <End Role="WF_WFAction" EntitySet="WF_WFAction" />
            <End Role="GNM_WFActionLocale" EntitySet="WF_WFActionLocale" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFAct__GNM_WorkFlow" Association="WorkFlowModel.FK__GNM_WFAct__GNM_WorkFlow">
            <End Role="WF_WorkFlow" EntitySet="WF_WorkFlow" />
            <End Role="GNM_WFActionLocale" EntitySet="WF_WFActionLocale" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFSte__GNM_WFStepsLocale" Association="WorkFlowModel.FK__GNM_WFSte__GNM_WFStepsLocale">
            <End Role="WF_WFSteps" EntitySet="WF_WFSteps" />
            <End Role="GNM_WFStepsLocale" EntitySet="WF_WFStepsLocale" />
          </AssociationSet>
          <AssociationSet Name="FK__GNM_WFStepStatusLocale__GNM_WFStepStatus" Association="WorkFlowModel.FK__GNM_WFStepStatusLocale__GNM_WFStepStatus">
            <End Role="WF_WFStepStatus" EntitySet="WF_WFStepStatus" />
            <End Role="GNM_WFStepStatusLocale" EntitySet="WF_WFStepStatusLocale" />
          </AssociationSet>
          <EntitySet Name="WF_WFRoleLocale" EntityType="WorkFlowModel.WF_WFRoleLocale" />
          <AssociationSet Name="FK__GNM_WFRoleLocale__WFRole_ID" Association="WorkFlowModel.FK__GNM_WFRoleLocale__WFRole_ID">
            <End Role="WF_WFRole" EntitySet="WF_WFRole" />
            <End Role="GNM_WFRoleLocale" EntitySet="WF_WFRoleLocale" />
          </AssociationSet>
        </EntityContainer>
        <EntityType Name="WF_Sms">
          <Key>
            <PropertyRef Name="Sms_ID" />
          </Key>
          <Property Name="Sms_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="Sms_Text" Type="String" MaxLength="200" Unicode="true" FixedLength="false" />
          <Property Name="Sms_Mobile_Number" Type="String" MaxLength="30" Unicode="false" FixedLength="false" />
          <Property Name="Sms_Queue_Date" Type="DateTime" Precision="3" />
          <Property Name="Sms_Sent_Date" Type="DateTime" Precision="3" />
          <Property Name="Sms_SentStatus" Type="Boolean" Nullable="false" />
          <Property Type="Int32" Name="Template_ID" Nullable="false" />
          <Property Type="String" Name="Parameter1_value" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Parameter2_value" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Parameter3_value" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Parameter4_value" MaxLength="100" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="WF_WFAction">
          <Key>
            <PropertyRef Name="WFAction_ID" />
          </Key>
          <Property Name="WFAction_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="WorkFlow_ID" Type="Int32" Nullable="false" />
          <Property Name="WFAction_Name" Type="String" Nullable="false" MaxLength="30" Unicode="false" FixedLength="false" />
          <NavigationProperty Name="GNM_WorkFlow" Relationship="WorkFlowModel.FK__GNM_WFAct__WorkF__656C112C" FromRole="GNM_WFAction" ToRole="GNM_WorkFlow" />
          <NavigationProperty Name="GNM_WFStepLink" Relationship="WorkFlowModel.FK__GNM_WFSte__WFAct__5F3414E9" FromRole="WF_WFAction" ToRole="GNM_WFStepLink" />
          <NavigationProperty Name="GNM_WFActionLocale" Relationship="WorkFlowModel.FK__GNM_WFActL__GNM_WFAction" FromRole="WF_WFAction" ToRole="GNM_WFActionLocale" />
          <Property Type="String" Name="ActionCode" MaxLength="100" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="WF_WFCase_Progress">
          <Key>
            <PropertyRef Name="WFCaseProgress_ID" />
          </Key>
          <Property Name="WFCaseProgress_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="WorkFlow_ID" Type="Int32" Nullable="false" />
          <Property Name="Transaction_ID" Type="Int32" Nullable="false" />
          <Property Name="WFSteps_ID" Type="Int32" Nullable="false" />
          <Property Name="Addresse_ID" Type="Int32" />
          <Property Name="Addresse_Flag" Type="Byte" Nullable="false" />
          <Property Name="Received_Time" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="Actioned_By" Type="Int32" />
          <Property Name="Action_Time" Type="DateTime" Precision="3" />
          <Property Name="Action_Chosen" Type="Int32" />
          <Property Name="Action_Remarks" Type="String" MaxLength="500" Unicode="false" FixedLength="false" />
          <Property Name="Locked_Ind" Type="Boolean" />
          <Property Name="WFNextStep_ID" Type="Int32" />
        </EntityType>
        <EntityType Name="WF_WFField">
          <Key>
            <PropertyRef Name="WFField_ID" />
          </Key>
          <Property Name="WFField_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="WorkFlow_ID" Type="Int32" Nullable="false" />
          <Property Name="WorkFlowFieldName" Type="String" Nullable="false" MaxLength="100" Unicode="false" FixedLength="false" />
          <NavigationProperty Name="GNM_WFFieldValue" Relationship="WorkFlowModel.FK_GNM_WFField_WFField_ID" FromRole="GNM_WFField" ToRole="GNM_WFFieldValue" />
          <NavigationProperty Name="GNM_WorkFlow" Relationship="WorkFlowModel.FK_GNM_WorkFlow_WorkFlow_ID" FromRole="GNM_WFField" ToRole="GNM_WorkFlow" />
        </EntityType>
        <EntityType Name="WF_WFFieldValue">
          <Key>
            <PropertyRef Name="WFFieldValue_ID" />
          </Key>
          <Property Name="WFFieldValue_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="WFField_ID" Type="Int32" Nullable="false" />
          <Property Name="WorkFlow_ID" Type="Int32" Nullable="false" />
          <Property Name="Company_ID" Type="Int32" Nullable="false" />
          <Property Name="Transaction_ID" Type="Int32" Nullable="false" />
          <Property Name="WorkFlowFieldValue" Type="String" Nullable="false" MaxLength="100" Unicode="false" FixedLength="false" />
          <NavigationProperty Name="GNM_WFField" Relationship="WorkFlowModel.FK_GNM_WFField_WFField_ID" FromRole="GNM_WFFieldValue" ToRole="GNM_WFField" />
          <NavigationProperty Name="GNM_WorkFlow" Relationship="WorkFlowModel.FK_GNM_WorkFlow_WF_ID" FromRole="GNM_WFFieldValue" ToRole="GNM_WorkFlow" />
        </EntityType>
        <EntityType Name="WF_WFRole">
          <Key>
            <PropertyRef Name="WFRole_ID" />
          </Key>
          <Property Name="WFRole_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="WorkFlow_ID" Type="Int32" Nullable="false" />
          <Property Name="WFRole_Name" Type="String" Nullable="false" MaxLength="50" Unicode="false" FixedLength="false" />
          <Property Name="WfRole_IsAdmin" Type="Boolean" Nullable="false" />
          <Property Name="WfRole_AutoAllocationAllowed" Type="Boolean" Nullable="false" />
          <NavigationProperty Name="GNM_WFRoleUser" Relationship="WorkFlowModel.FK__GNM_WFRol__WFRol__4E0988E7" FromRole="GNM_WFRole" ToRole="GNM_WFRoleUser" />
          <NavigationProperty Name="GNM_WorkFlow" Relationship="WorkFlowModel.FK__GNM_WFRol__WorkF__475C8B58" FromRole="GNM_WFRole" ToRole="GNM_WorkFlow" />
          <Property Type="Boolean" Name="WFRole_IsRoleExternal" />
          <Property Type="Int32" Name="WFRole_ExternalCompany_ID" />
          <NavigationProperty Name="GNM_WFStepLink" Relationship="WorkFlowModel.FK__GNM_WFSte__Addre__5B638405" FromRole="WF_WFRole" ToRole="GNM_WFStepLink" />
          <NavigationProperty Name="GNM_WFRoleLocale" Relationship="WorkFlowModel.FK__GNM_WFRoleLocale__WFRole_ID" FromRole="WF_WFRole" ToRole="GNM_WFRoleLocale" />
        </EntityType>
        <EntityType Name="WF_WFRoleUser">
          <Key>
            <PropertyRef Name="WFRoleUser_ID" />
          </Key>
          <Property Name="WFRoleUser_ID" Nullable="false" annotation:StoreGeneratedPattern="Identity" Type="Int32" />
          <Property Name="WFRole_ID" Nullable="false" Type="Int32" />
          <Property Name="UserID" Nullable="false" Type="Int32" />
          <Property Name="ApprovalLimit" Nullable="false" Type="Int32" />
          <NavigationProperty Name="GNM_WFRole" Relationship="WorkFlowModel.FK__GNM_WFRol__WFRol__4E0988E7" FromRole="GNM_WFRoleUser" ToRole="GNM_WFRole" />
        </EntityType>
        <EntityType Name="WF_WFSteps">
          <Key>
            <PropertyRef Name="WFSteps_ID" />
          </Key>
          <Property Name="WFSteps_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="WorkFlow_ID" Type="Int32" Nullable="false" />
          <Property Name="WFStep_Name" Type="String" Nullable="false" MaxLength="50" Unicode="false" FixedLength="false" />
          <Property Name="WFStepType_ID" Type="Int32" Nullable="false" />
          <Property Name="WFStepStatus_ID" Type="Int32" Nullable="false" />
          <Property Name="WFStep_IsActive" Type="Boolean" Nullable="false" />
          <NavigationProperty Name="GNM_WFStepType" Relationship="WorkFlowModel.FK__GNM_WFSte__WFSte__53C2623D" FromRole="GNM_WFSteps" ToRole="GNM_WFStepType" />
          <NavigationProperty Name="GNM_WFStepStatus" Relationship="WorkFlowModel.FK__GNM_WFSte__WFSte__54B68676" FromRole="GNM_WFSteps" ToRole="GNM_WFStepStatus" />
          <NavigationProperty Name="GNM_WorkFlow" Relationship="WorkFlowModel.FK__GNM_WFSte__WorkF__55AAAAAF" FromRole="GNM_WFSteps" ToRole="GNM_WorkFlow" />
          <NavigationProperty Name="GNM_WFStepLink" Relationship="WorkFlowModel.FK__GNM_WFSte__FrmWF__5D4BCC77" FromRole="WF_WFSteps" ToRole="GNM_WFStepLink" />
          <NavigationProperty Name="GNM_WFStepLink1" Relationship="WorkFlowModel.FK__GNM_WFSte__ToWFS__5E3FF0B0" FromRole="WF_WFSteps" ToRole="GNM_WFStepLink" />
          <NavigationProperty Name="GNM_WFStepsLocale" Relationship="WorkFlowModel.FK__GNM_WFSte__GNM_WFStepsLocale" FromRole="WF_WFSteps" ToRole="GNM_WFStepsLocale" />
          <Property Type="String" Name="BranchCode" MaxLength="10" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="WF_WFStepStatus">
          <Key>
            <PropertyRef Name="WFStepStatus_ID" />
          </Key>
          <Property Name="WFStepStatus_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="WFStepStatus_Nm" Type="String" Nullable="false" MaxLength="100" Unicode="false" FixedLength="false" />
          <NavigationProperty Name="GNM_WFSteps" Relationship="WorkFlowModel.FK__GNM_WFSte__WFSte__54B68676" FromRole="GNM_WFStepStatus" ToRole="GNM_WFSteps" />
          <NavigationProperty Name="GNM_WFStepStatusLocale" Relationship="WorkFlowModel.FK__GNM_WFStepStatusLocale__GNM_WFStepStatus" FromRole="WF_WFStepStatus" ToRole="GNM_WFStepStatusLocale" />
          <Property Type="String" Name="StepStatusCode" MaxLength="100" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="WF_WFStepType">
          <Key>
            <PropertyRef Name="WFStepType_ID" />
          </Key>
          <Property Name="WFStepType_ID" Type="Int32" Nullable="false" p1:StoreGeneratedPattern="Identity" />
          <Property Name="WFStepType_Nm" Type="String" Nullable="false" MaxLength="100" Unicode="false" FixedLength="false" />
          <NavigationProperty Name="GNM_WFSteps" Relationship="WorkFlowModel.FK__GNM_WFSte__WFSte__53C2623D" FromRole="GNM_WFStepType" ToRole="GNM_WFSteps" />
        </EntityType>
        <EntityType Name="WF_WorkFlow">
          <Key>
            <PropertyRef Name="WorkFlow_ID" />
          </Key>
          <Property Name="WorkFlow_ID" Type="Int32" Nullable="false" />
          <Property Name="WorkFlow_Name" Type="String" Nullable="false" MaxLength="100" Unicode="false" FixedLength="false" />
          <Property Name="AllQueue_Filter_IsBranch" Type="Boolean" />
          <NavigationProperty Name="GNM_WFAction" Relationship="WorkFlowModel.FK__GNM_WFAct__WorkF__656C112C" FromRole="GNM_WorkFlow" ToRole="GNM_WFAction" />
          <NavigationProperty Name="GNM_WFField" Relationship="WorkFlowModel.FK_GNM_WorkFlow_WorkFlow_ID" FromRole="GNM_WorkFlow" ToRole="GNM_WFField" />
          <NavigationProperty Name="GNM_WFFieldValue" Relationship="WorkFlowModel.FK_GNM_WorkFlow_WF_ID" FromRole="GNM_WorkFlow" ToRole="GNM_WFFieldValue" />
          <NavigationProperty Name="GNM_WFRole" Relationship="WorkFlowModel.FK__GNM_WFRol__WorkF__475C8B58" FromRole="GNM_WorkFlow" ToRole="GNM_WFRole" />
          <NavigationProperty Name="GNM_WFSteps" Relationship="WorkFlowModel.FK__GNM_WFSte__WorkF__55AAAAAF" FromRole="GNM_WorkFlow" ToRole="GNM_WFSteps" />
          <NavigationProperty Name="GNM_WFStepLink" Relationship="WorkFlowModel.FK__GNM_WFSte__WorkF__60283922" FromRole="WF_WorkFlow" ToRole="GNM_WFStepLink" />
          <NavigationProperty Name="GNM_WFActionLocale" Relationship="WorkFlowModel.FK__GNM_WFAct__GNM_WorkFlow" FromRole="WF_WorkFlow" ToRole="GNM_WFActionLocale" />
        </EntityType>
        <Association Name="FK__GNM_WFAct__WorkF__656C112C">
          <End Role="GNM_WorkFlow" Type="WorkFlowModel.WF_WorkFlow" Multiplicity="1" />
          <End Role="GNM_WFAction" Type="WorkFlowModel.WF_WFAction" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WorkFlow">
              <PropertyRef Name="WorkFlow_ID" />
            </Principal>
            <Dependent Role="GNM_WFAction">
              <PropertyRef Name="WorkFlow_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_WFField_WFField_ID">
          <End Role="GNM_WFField" Type="WorkFlowModel.WF_WFField" Multiplicity="1" />
          <End Role="GNM_WFFieldValue" Type="WorkFlowModel.WF_WFFieldValue" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WFField">
              <PropertyRef Name="WFField_ID" />
            </Principal>
            <Dependent Role="GNM_WFFieldValue">
              <PropertyRef Name="WFField_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_WorkFlow_WorkFlow_ID">
          <End Role="GNM_WorkFlow" Type="WorkFlowModel.WF_WorkFlow" Multiplicity="1" />
          <End Role="GNM_WFField" Type="WorkFlowModel.WF_WFField" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WorkFlow">
              <PropertyRef Name="WorkFlow_ID" />
            </Principal>
            <Dependent Role="GNM_WFField">
              <PropertyRef Name="WorkFlow_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_GNM_WorkFlow_WF_ID">
          <End Role="GNM_WorkFlow" Type="WorkFlowModel.WF_WorkFlow" Multiplicity="1" />
          <End Role="GNM_WFFieldValue" Type="WorkFlowModel.WF_WFFieldValue" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WorkFlow">
              <PropertyRef Name="WorkFlow_ID" />
            </Principal>
            <Dependent Role="GNM_WFFieldValue">
              <PropertyRef Name="WorkFlow_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFRol__WFRol__4E0988E7">
          <End Type="WorkFlowModel.WF_WFRole" Multiplicity="1" Role="GNM_WFRole" />
          <End Type="WorkFlowModel.WF_WFRoleUser" Multiplicity="*" Role="GNM_WFRoleUser" />
          <ReferentialConstraint>
            <Principal Role="GNM_WFRole">
              <PropertyRef Name="WFRole_ID" />
            </Principal>
            <Dependent Role="GNM_WFRoleUser">
              <PropertyRef Name="WFRole_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFRol__WorkF__475C8B58">
          <End Role="GNM_WorkFlow" Type="WorkFlowModel.WF_WorkFlow" Multiplicity="1" />
          <End Role="GNM_WFRole" Type="WorkFlowModel.WF_WFRole" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WorkFlow">
              <PropertyRef Name="WorkFlow_ID" />
            </Principal>
            <Dependent Role="GNM_WFRole">
              <PropertyRef Name="WorkFlow_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFSte__WFSte__53C2623D">
          <End Role="GNM_WFStepType" Type="WorkFlowModel.WF_WFStepType" Multiplicity="1" />
          <End Role="GNM_WFSteps" Type="WorkFlowModel.WF_WFSteps" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WFStepType">
              <PropertyRef Name="WFStepType_ID" />
            </Principal>
            <Dependent Role="GNM_WFSteps">
              <PropertyRef Name="WFStepType_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFSte__WFSte__54B68676">
          <End Role="GNM_WFStepStatus" Type="WorkFlowModel.WF_WFStepStatus" Multiplicity="1" />
          <End Role="GNM_WFSteps" Type="WorkFlowModel.WF_WFSteps" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WFStepStatus">
              <PropertyRef Name="WFStepStatus_ID" />
            </Principal>
            <Dependent Role="GNM_WFSteps">
              <PropertyRef Name="WFStepStatus_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFSte__WorkF__55AAAAAF">
          <End Role="GNM_WorkFlow" Type="WorkFlowModel.WF_WorkFlow" Multiplicity="1" />
          <End Role="GNM_WFSteps" Type="WorkFlowModel.WF_WFSteps" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="GNM_WorkFlow">
              <PropertyRef Name="WorkFlow_ID" />
            </Principal>
            <Dependent Role="GNM_WFSteps">
              <PropertyRef Name="WorkFlow_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="GNM_WorkFlowParent">
          <Key>
            <PropertyRef Name="WorkFlowParent_ID" />
          </Key>
          <Property Type="Int32" Name="WorkFlowParent_ID" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Type="Int32" Name="WorkFlow_ID" />
          <Property Type="String" Name="TXN_Table" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Reference_Column" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Parent_Table" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Parent_Column" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Type="Byte" Name="Parent_Level" />
          <Property Type="Byte" Name="Parent_WFID" />
          <Property Type="String" Name="PrimaryKey_Column" MaxLength="50" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="WF_WFChildActions">
          <Key>
            <PropertyRef Name="ChildActions_ID" />
          </Key>
          <Property Type="Int32" Name="ChildActions_ID" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Type="Int32" Name="Object_ID" Nullable="false" />
          <Property Type="String" Name="Actions_Name" MaxLength="100" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="WF_WFStepLink">
          <Key>
            <PropertyRef Name="WFStepLink_ID" />
          </Key>
          <Property Type="Int32" Name="WFStepLink_ID" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Type="Int32" Name="WorkFlow_ID" Nullable="false" />
          <Property Type="Int32" Name="Company_ID" Nullable="false" />
          <Property Type="Int32" Name="FrmWFSteps_ID" Nullable="false" />
          <Property Type="Int32" Name="WFAction_ID" Nullable="false" />
          <Property Type="Int32" Name="ToWFSteps_ID" Nullable="false" />
          <Property Type="Int32" Name="Addresse_WFRole_ID" />
          <Property Type="Byte" Name="Addresse_Flag" Nullable="false" />
          <Property Type="Boolean" Name="IsSMSSentToCustomer" Nullable="false" />
          <Property Type="Boolean" Name="IsEmailSentToCustomer" Nullable="false" />
          <Property Type="Boolean" Name="IsSMSSentToAddressee" Nullable="false" />
          <Property Type="Boolean" Name="IsEmailSentToAddresse" Nullable="false" />
          <Property Type="Boolean" Name="AutoAllocationAllowed" Nullable="false" />
          <Property Type="Boolean" Name="IsVersionEnabled" Nullable="false" />
          <Property Type="Int32" Name="InvokeParentWF_ID" />
          <Property Type="Int32" Name="InvokeParentWFLink_ID" />
          <Property Type="Int32" Name="InvokeChildObject_ID" />
          <Property Type="Int32" Name="InvokeChildObjectAction" />
          <Property Type="Int32" Name="WFField_ID" />
          <Property Type="String" Name="AutoCondition" MaxLength="100" FixedLength="false" Unicode="false" />
          <NavigationProperty Name="GNM_WFAction" Relationship="WorkFlowModel.FK__GNM_WFSte__WFAct__5F3414E9" FromRole="GNM_WFStepLink" ToRole="WF_WFAction" />
          <NavigationProperty Name="GNM_WFRole" Relationship="WorkFlowModel.FK__GNM_WFSte__Addre__5B638405" FromRole="GNM_WFStepLink" ToRole="WF_WFRole" />
          <NavigationProperty Name="GNM_WFSteps" Relationship="WorkFlowModel.FK__GNM_WFSte__FrmWF__5D4BCC77" FromRole="GNM_WFStepLink" ToRole="WF_WFSteps" />
          <NavigationProperty Name="GNM_WFSteps1" Relationship="WorkFlowModel.FK__GNM_WFSte__ToWFS__5E3FF0B0" FromRole="GNM_WFStepLink" ToRole="WF_WFSteps" />
          <NavigationProperty Name="GNM_WorkFlow" Relationship="WorkFlowModel.FK__GNM_WFSte__WorkF__60283922" FromRole="GNM_WFStepLink" ToRole="WF_WorkFlow" />
        </EntityType>
        <Association Name="FK__GNM_WFSte__WFAct__5F3414E9">
          <End Type="WorkFlowModel.WF_WFAction" Role="WF_WFAction" Multiplicity="1" />
          <End Type="WorkFlowModel.WF_WFStepLink" Role="GNM_WFStepLink" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="WF_WFAction">
              <PropertyRef Name="WFAction_ID" />
            </Principal>
            <Dependent Role="GNM_WFStepLink">
              <PropertyRef Name="WFAction_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFSte__Addre__5B638405">
          <End Type="WorkFlowModel.WF_WFRole" Role="WF_WFRole" Multiplicity="0..1" />
          <End Type="WorkFlowModel.WF_WFStepLink" Role="GNM_WFStepLink" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="WF_WFRole">
              <PropertyRef Name="WFRole_ID" />
            </Principal>
            <Dependent Role="GNM_WFStepLink">
              <PropertyRef Name="Addresse_WFRole_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFSte__FrmWF__5D4BCC77">
          <End Type="WorkFlowModel.WF_WFSteps" Role="WF_WFSteps" Multiplicity="1" />
          <End Type="WorkFlowModel.WF_WFStepLink" Role="GNM_WFStepLink" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="WF_WFSteps">
              <PropertyRef Name="WFSteps_ID" />
            </Principal>
            <Dependent Role="GNM_WFStepLink">
              <PropertyRef Name="FrmWFSteps_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFSte__ToWFS__5E3FF0B0">
          <End Type="WorkFlowModel.WF_WFSteps" Role="WF_WFSteps" Multiplicity="1" />
          <End Type="WorkFlowModel.WF_WFStepLink" Role="GNM_WFStepLink" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="WF_WFSteps">
              <PropertyRef Name="WFSteps_ID" />
            </Principal>
            <Dependent Role="GNM_WFStepLink">
              <PropertyRef Name="ToWFSteps_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFSte__WorkF__60283922">
          <End Type="WorkFlowModel.WF_WorkFlow" Role="WF_WorkFlow" Multiplicity="1" />
          <End Type="WorkFlowModel.WF_WFStepLink" Role="GNM_WFStepLink" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="WF_WorkFlow">
              <PropertyRef Name="WorkFlow_ID" />
            </Principal>
            <Dependent Role="GNM_WFStepLink">
              <PropertyRef Name="WorkFlow_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="WF_Email">
          <Key>
            <PropertyRef Name="Email_ID" />
          </Key>
          <Property Type="Int32" Name="Email_ID" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Type="String" Name="Email_Subject" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Type="String" Name="Email_Body" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Type="String" Name="Email_To" MaxLength="500" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Email_cc" MaxLength="500" FixedLength="false" Unicode="false" />
          <Property Type="String" Name="Email_Bcc" MaxLength="500" FixedLength="false" Unicode="false" />
          <Property Type="DateTime" Name="Email_Queue_Date" Precision="3" />
          <Property Type="DateTime" Name="Email_Sent_Date" Precision="3" />
          <Property Type="Boolean" Name="Email_SentStatus" Nullable="false" />
          <Property Type="String" Name="Email_Attachments" MaxLength="500" FixedLength="false" Unicode="true" />
          <Property Type="Boolean" Name="Email_IsError" />
          <Property Type="Byte" Name="NoOfAttempts" />
        </EntityType>
        <EntityType Name="WF_WFActionLocale">
          <Key>
            <PropertyRef Name="WFActionLocale_ID" />
          </Key>
          <Property Type="Int32" Name="WFActionLocale_ID" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Type="Int32" Name="WorkFlow_ID" Nullable="false" />
          <Property Type="Int32" Name="WFAction_ID" Nullable="false" />
          <Property Type="Int32" Name="Language_ID" Nullable="false" />
          <Property Type="String" Name="WFAction_Name" Nullable="false" MaxLength="100" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="GNM_WFAction" Relationship="WorkFlowModel.FK__GNM_WFActL__GNM_WFAction" FromRole="GNM_WFActionLocale" ToRole="WF_WFAction" />
          <NavigationProperty Name="GNM_WorkFlow" Relationship="WorkFlowModel.FK__GNM_WFAct__GNM_WorkFlow" FromRole="GNM_WFActionLocale" ToRole="WF_WorkFlow" />
          <Property Type="String" Name="ActionCode" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
        </EntityType>
        <EntityType Name="WF_WFStepsLocale">
          <Key>
            <PropertyRef Name="WFStepsLocale_ID" />
          </Key>
          <Property Type="Int32" Name="WFStepsLocale_ID" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Type="Int32" Name="WFSteps_ID" Nullable="false" />
          <Property Type="String" Name="WFStep_Name" Nullable="false" MaxLength="200" FixedLength="false" Unicode="true" />
          <Property Type="Int32" Name="Language_ID" Nullable="false" />
          <NavigationProperty Name="GNM_WFSteps" Relationship="WorkFlowModel.FK__GNM_WFSte__GNM_WFStepsLocale" FromRole="GNM_WFStepsLocale" ToRole="WF_WFSteps" />
        </EntityType>
        <EntityType Name="WF_WFStepStatusLocale">
          <Key>
            <PropertyRef Name="WFStepStatusLocale_ID" />
          </Key>
          <Property Type="Int32" Name="WFStepStatusLocale_ID" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Type="Int32" Name="WFStepStatus_ID" Nullable="false" />
          <Property Type="Int32" Name="Language_ID" Nullable="false" />
          <Property Type="String" Name="WFStepStatus_Nm" Nullable="false" MaxLength="200" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="GNM_WFStepStatus" Relationship="WorkFlowModel.FK__GNM_WFStepStatusLocale__GNM_WFStepStatus" FromRole="GNM_WFStepStatusLocale" ToRole="WF_WFStepStatus" />
          <Property Type="String" Name="StepStatusCode" MaxLength="100" FixedLength="false" Unicode="false" Nullable="false" />
        </EntityType>
        <Association Name="FK__GNM_WFActL__GNM_WFAction">
          <End Type="WorkFlowModel.WF_WFAction" Role="WF_WFAction" Multiplicity="1" />
          <End Type="WorkFlowModel.WF_WFActionLocale" Role="GNM_WFActionLocale" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="WF_WFAction">
              <PropertyRef Name="WFAction_ID" />
            </Principal>
            <Dependent Role="GNM_WFActionLocale">
              <PropertyRef Name="WFAction_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFAct__GNM_WorkFlow">
          <End Type="WorkFlowModel.WF_WorkFlow" Role="WF_WorkFlow" Multiplicity="1" />
          <End Type="WorkFlowModel.WF_WFActionLocale" Role="GNM_WFActionLocale" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="WF_WorkFlow">
              <PropertyRef Name="WorkFlow_ID" />
            </Principal>
            <Dependent Role="GNM_WFActionLocale">
              <PropertyRef Name="WorkFlow_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFSte__GNM_WFStepsLocale">
          <End Type="WorkFlowModel.WF_WFSteps" Role="WF_WFSteps" Multiplicity="1" />
          <End Type="WorkFlowModel.WF_WFStepsLocale" Role="GNM_WFStepsLocale" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="WF_WFSteps">
              <PropertyRef Name="WFSteps_ID" />
            </Principal>
            <Dependent Role="GNM_WFStepsLocale">
              <PropertyRef Name="WFSteps_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__GNM_WFStepStatusLocale__GNM_WFStepStatus">
          <End Type="WorkFlowModel.WF_WFStepStatus" Role="WF_WFStepStatus" Multiplicity="1" />
          <End Type="WorkFlowModel.WF_WFStepStatusLocale" Role="GNM_WFStepStatusLocale" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="WF_WFStepStatus">
              <PropertyRef Name="WFStepStatus_ID" />
            </Principal>
            <Dependent Role="GNM_WFStepStatusLocale">
              <PropertyRef Name="WFStepStatus_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityType Name="WF_WFRoleLocale">
          <Key>
            <PropertyRef Name="WFRoleLocale_ID" />
          </Key>
          <Property Type="Int32" Name="WFRoleLocale_ID" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Type="Int32" Name="WFRole_ID" Nullable="false" />
          <Property Type="String" Name="WFRole_Name" Nullable="false" MaxLength="100" FixedLength="false" Unicode="true" />
          <Property Type="Int32" Name="Language_ID" Nullable="false" />
          <NavigationProperty Name="GNM_WFRole" Relationship="WorkFlowModel.FK__GNM_WFRoleLocale__WFRole_ID" FromRole="GNM_WFRoleLocale" ToRole="WF_WFRole" />
        </EntityType>
        <Association Name="FK__GNM_WFRoleLocale__WFRole_ID">
          <End Type="WorkFlowModel.WF_WFRole" Role="WF_WFRole" Multiplicity="1" />
          <End Type="WorkFlowModel.WF_WFRoleLocale" Role="GNM_WFRoleLocale" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="WF_WFRole">
              <PropertyRef Name="WFRole_ID" />
            </Principal>
            <Dependent Role="GNM_WFRoleLocale">
              <PropertyRef Name="WFRole_ID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
      </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="WorkFlowModelStoreContainer" CdmEntityContainer="WorkFlowEntity">
          <EntitySetMapping Name="WF_Sms">
            <EntityTypeMapping TypeName="WorkFlowModel.WF_Sms">
              <MappingFragment StoreEntitySet="GNM_Sms">
                <ScalarProperty Name="Parameter4_value" ColumnName="Parameter4_value" />
                <ScalarProperty Name="Parameter3_value" ColumnName="Parameter3_value" />
                <ScalarProperty Name="Parameter2_value" ColumnName="Parameter2_value" />
                <ScalarProperty Name="Parameter1_value" ColumnName="Parameter1_value" />
                <ScalarProperty Name="Template_ID" ColumnName="Template_ID" />
                <ScalarProperty Name="Sms_ID" ColumnName="Sms_ID" />
                <ScalarProperty Name="Sms_Text" ColumnName="Sms_Text" />
                <ScalarProperty Name="Sms_Mobile_Number" ColumnName="Sms_Mobile_Number" />
                <ScalarProperty Name="Sms_Queue_Date" ColumnName="Sms_Queue_Date" />
                <ScalarProperty Name="Sms_Sent_Date" ColumnName="Sms_Sent_Date" />
                <ScalarProperty Name="Sms_SentStatus" ColumnName="Sms_SentStatus" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_WFAction">
            <EntityTypeMapping TypeName="WorkFlowModel.WF_WFAction">
              <MappingFragment StoreEntitySet="GNM_WFAction">
                <ScalarProperty Name="ActionCode" ColumnName="ActionCode" />
                <ScalarProperty Name="WFAction_ID" ColumnName="WFAction_ID" />
                <ScalarProperty Name="WorkFlow_ID" ColumnName="WorkFlow_ID" />
                <ScalarProperty Name="WFAction_Name" ColumnName="WFAction_Name" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_WFCase_Progress">
            <EntityTypeMapping TypeName="WorkFlowModel.WF_WFCase_Progress">
              <MappingFragment StoreEntitySet="GNM_WFCase_Progress">
                <ScalarProperty Name="WFCaseProgress_ID" ColumnName="WFCaseProgress_ID" />
                <ScalarProperty Name="WorkFlow_ID" ColumnName="WorkFlow_ID" />
                <ScalarProperty Name="Transaction_ID" ColumnName="Transaction_ID" />
                <ScalarProperty Name="WFSteps_ID" ColumnName="WFSteps_ID" />
                <ScalarProperty Name="Addresse_ID" ColumnName="Addresse_ID" />
                <ScalarProperty Name="Addresse_Flag" ColumnName="Addresse_Flag" />
                <ScalarProperty Name="Received_Time" ColumnName="Received_Time" />
                <ScalarProperty Name="Actioned_By" ColumnName="Actioned_By" />
                <ScalarProperty Name="Action_Time" ColumnName="Action_Time" />
                <ScalarProperty Name="Action_Chosen" ColumnName="Action_Chosen" />
                <ScalarProperty Name="Action_Remarks" ColumnName="Action_Remarks" />
                <ScalarProperty Name="Locked_Ind" ColumnName="Locked_Ind" />
                <ScalarProperty Name="WFNextStep_ID" ColumnName="WFNextStep_ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_WFField">
            <EntityTypeMapping TypeName="WorkFlowModel.WF_WFField">
              <MappingFragment StoreEntitySet="GNM_WFField">
                <ScalarProperty Name="WFField_ID" ColumnName="WFField_ID" />
                <ScalarProperty Name="WorkFlow_ID" ColumnName="WorkFlow_ID" />
                <ScalarProperty Name="WorkFlowFieldName" ColumnName="WorkFlowFieldName" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_WFFieldValue">
            <EntityTypeMapping TypeName="WorkFlowModel.WF_WFFieldValue">
              <MappingFragment StoreEntitySet="GNM_WFFieldValue">
                <ScalarProperty Name="WFFieldValue_ID" ColumnName="WFFieldValue_ID" />
                <ScalarProperty Name="WFField_ID" ColumnName="WFField_ID" />
                <ScalarProperty Name="WorkFlow_ID" ColumnName="WorkFlow_ID" />
                <ScalarProperty Name="Company_ID" ColumnName="Company_ID" />
                <ScalarProperty Name="Transaction_ID" ColumnName="Transaction_ID" />
                <ScalarProperty Name="WorkFlowFieldValue" ColumnName="WorkFlowFieldValue" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_WFRole">
            <EntityTypeMapping TypeName="WorkFlowModel.WF_WFRole">
              <MappingFragment StoreEntitySet="GNM_WFRole">
                <ScalarProperty Name="WFRole_ExternalCompany_ID" ColumnName="WFRole_ExternalCompany_ID" />
                <ScalarProperty Name="WFRole_IsRoleExternal" ColumnName="WFRole_IsRoleExternal" />
                <ScalarProperty Name="WFRole_ID" ColumnName="WFRole_ID" />
                <ScalarProperty Name="WorkFlow_ID" ColumnName="WorkFlow_ID" />
                <ScalarProperty Name="WFRole_Name" ColumnName="WFRole_Name" />
                <ScalarProperty Name="WfRole_IsAdmin" ColumnName="WfRole_IsAdmin" />
                <ScalarProperty Name="WfRole_AutoAllocationAllowed" ColumnName="WfRole_AutoAllocationAllowed" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_WFRoleUser">
            <EntityTypeMapping TypeName="WorkFlowModel.WF_WFRoleUser">
              <MappingFragment StoreEntitySet="GNM_WFRoleUser">
                <ScalarProperty Name="WFRoleUser_ID" ColumnName="WFRoleUser_ID" />
                <ScalarProperty Name="WFRole_ID" ColumnName="WFRole_ID" />
                <ScalarProperty Name="UserID" ColumnName="UserID" />
                <ScalarProperty Name="ApprovalLimit" ColumnName="ApprovalLimit" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_WFSteps">
            <EntityTypeMapping TypeName="WorkFlowModel.WF_WFSteps">
              <MappingFragment StoreEntitySet="GNM_WFSteps">
                <ScalarProperty Name="BranchCode" ColumnName="BranchCode" />
                <ScalarProperty Name="WFSteps_ID" ColumnName="WFSteps_ID" />
                <ScalarProperty Name="WorkFlow_ID" ColumnName="WorkFlow_ID" />
                <ScalarProperty Name="WFStep_Name" ColumnName="WFStep_Name" />
                <ScalarProperty Name="WFStepType_ID" ColumnName="WFStepType_ID" />
                <ScalarProperty Name="WFStepStatus_ID" ColumnName="WFStepStatus_ID" />
                <ScalarProperty Name="WFStep_IsActive" ColumnName="WFStep_IsActive" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_WFStepStatus">
            <EntityTypeMapping TypeName="WorkFlowModel.WF_WFStepStatus">
              <MappingFragment StoreEntitySet="GNM_WFStepStatus">
                <ScalarProperty Name="StepStatusCode" ColumnName="StepStatusCode" />
                <ScalarProperty Name="WFStepStatus_ID" ColumnName="WFStepStatus_ID" />
                <ScalarProperty Name="WFStepStatus_Nm" ColumnName="WFStepStatus_Nm" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_WFStepType">
            <EntityTypeMapping TypeName="WorkFlowModel.WF_WFStepType">
              <MappingFragment StoreEntitySet="GNM_WFStepType">
                <ScalarProperty Name="WFStepType_ID" ColumnName="WFStepType_ID" />
                <ScalarProperty Name="WFStepType_Nm" ColumnName="WFStepType_Nm" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_WorkFlow">
            <EntityTypeMapping TypeName="WorkFlowModel.WF_WorkFlow">
              <MappingFragment StoreEntitySet="GNM_WorkFlow">
                <ScalarProperty Name="WorkFlow_ID" ColumnName="WorkFlow_ID" />
                <ScalarProperty Name="WorkFlow_Name" ColumnName="WorkFlow_Name" />
                <ScalarProperty Name="AllQueue_Filter_IsBranch" ColumnName="AllQueue_Filter_IsBranch" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="GNM_WorkFlowParent">
            <EntityTypeMapping TypeName="WorkFlowModel.GNM_WorkFlowParent">
              <MappingFragment StoreEntitySet="GNM_WorkFlowParent">
                <ScalarProperty Name="PrimaryKey_Column" ColumnName="PrimaryKey_Column" />
                <ScalarProperty Name="Parent_WFID" ColumnName="Parent_WFID" />
                <ScalarProperty Name="Parent_Level" ColumnName="Parent_Level" />
                <ScalarProperty Name="Parent_Column" ColumnName="Parent_Column" />
                <ScalarProperty Name="Parent_Table" ColumnName="Parent_Table" />
                <ScalarProperty Name="Reference_Column" ColumnName="Reference_Column" />
                <ScalarProperty Name="TXN_Table" ColumnName="TXN_Table" />
                <ScalarProperty Name="WorkFlow_ID" ColumnName="WorkFlow_ID" />
                <ScalarProperty Name="WorkFlowParent_ID" ColumnName="WorkFlowParent_ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_WFChildActions">
            <EntityTypeMapping TypeName="WorkFlowModel.WF_WFChildActions">
              <MappingFragment StoreEntitySet="GNM_WFChildActions">
                <ScalarProperty Name="Actions_Name" ColumnName="Actions_Name" />
                <ScalarProperty Name="Object_ID" ColumnName="Object_ID" />
                <ScalarProperty Name="ChildActions_ID" ColumnName="ChildActions_ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_WFStepLink">
            <EntityTypeMapping TypeName="WorkFlowModel.WF_WFStepLink">
              <MappingFragment StoreEntitySet="GNM_WFStepLink">
                <ScalarProperty Name="AutoCondition" ColumnName="AutoCondition" />
                <ScalarProperty Name="WFField_ID" ColumnName="WFField_ID" />
                <ScalarProperty Name="InvokeChildObjectAction" ColumnName="InvokeChildObjectAction" />
                <ScalarProperty Name="InvokeChildObject_ID" ColumnName="InvokeChildObject_ID" />
                <ScalarProperty Name="InvokeParentWFLink_ID" ColumnName="InvokeParentWFLink_ID" />
                <ScalarProperty Name="InvokeParentWF_ID" ColumnName="InvokeParentWF_ID" />
                <ScalarProperty Name="IsVersionEnabled" ColumnName="IsVersionEnabled" />
                <ScalarProperty Name="AutoAllocationAllowed" ColumnName="AutoAllocationAllowed" />
                <ScalarProperty Name="IsEmailSentToAddresse" ColumnName="IsEmailSentToAddresse" />
                <ScalarProperty Name="IsSMSSentToAddressee" ColumnName="IsSMSSentToAddressee" />
                <ScalarProperty Name="IsEmailSentToCustomer" ColumnName="IsEmailSentToCustomer" />
                <ScalarProperty Name="IsSMSSentToCustomer" ColumnName="IsSMSSentToCustomer" />
                <ScalarProperty Name="Addresse_Flag" ColumnName="Addresse_Flag" />
                <ScalarProperty Name="Addresse_WFRole_ID" ColumnName="Addresse_WFRole_ID" />
                <ScalarProperty Name="ToWFSteps_ID" ColumnName="ToWFSteps_ID" />
                <ScalarProperty Name="WFAction_ID" ColumnName="WFAction_ID" />
                <ScalarProperty Name="FrmWFSteps_ID" ColumnName="FrmWFSteps_ID" />
                <ScalarProperty Name="Company_ID" ColumnName="Company_ID" />
                <ScalarProperty Name="WorkFlow_ID" ColumnName="WorkFlow_ID" />
                <ScalarProperty Name="WFStepLink_ID" ColumnName="WFStepLink_ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_Email">
            <EntityTypeMapping TypeName="WorkFlowModel.WF_Email">
              <MappingFragment StoreEntitySet="GNM_Email">
                <ScalarProperty Name="NoOfAttempts" ColumnName="NoOfAttempts" />
                <ScalarProperty Name="Email_IsError" ColumnName="Email_IsError" />
                <ScalarProperty Name="Email_Attachments" ColumnName="Email_Attachments" />
                <ScalarProperty Name="Email_SentStatus" ColumnName="Email_SentStatus" />
                <ScalarProperty Name="Email_Sent_Date" ColumnName="Email_Sent_Date" />
                <ScalarProperty Name="Email_Queue_Date" ColumnName="Email_Queue_Date" />
                <ScalarProperty Name="Email_Bcc" ColumnName="Email_Bcc" />
                <ScalarProperty Name="Email_cc" ColumnName="Email_cc" />
                <ScalarProperty Name="Email_To" ColumnName="Email_To" />
                <ScalarProperty Name="Email_Body" ColumnName="Email_Body" />
                <ScalarProperty Name="Email_Subject" ColumnName="Email_Subject" />
                <ScalarProperty Name="Email_ID" ColumnName="Email_ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_WFActionLocale">
            <EntityTypeMapping TypeName="WorkFlowModel.WF_WFActionLocale">
              <MappingFragment StoreEntitySet="GNM_WFActionLocale">
                <ScalarProperty Name="ActionCode" ColumnName="ActionCode" />
                <ScalarProperty Name="WFAction_Name" ColumnName="WFAction_Name" />
                <ScalarProperty Name="Language_ID" ColumnName="Language_ID" />
                <ScalarProperty Name="WFAction_ID" ColumnName="WFAction_ID" />
                <ScalarProperty Name="WorkFlow_ID" ColumnName="WorkFlow_ID" />
                <ScalarProperty Name="WFActionLocale_ID" ColumnName="WFActionLocale_ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_WFStepsLocale">
            <EntityTypeMapping TypeName="WorkFlowModel.WF_WFStepsLocale">
              <MappingFragment StoreEntitySet="GNM_WFStepsLocale">
                <ScalarProperty Name="Language_ID" ColumnName="Language_ID" />
                <ScalarProperty Name="WFStep_Name" ColumnName="WFStep_Name" />
                <ScalarProperty Name="WFSteps_ID" ColumnName="WFSteps_ID" />
                <ScalarProperty Name="WFStepsLocale_ID" ColumnName="WFStepsLocale_ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_WFStepStatusLocale">
            <EntityTypeMapping TypeName="WorkFlowModel.WF_WFStepStatusLocale">
              <MappingFragment StoreEntitySet="GNM_WFStepStatusLocale">
                <ScalarProperty Name="StepStatusCode" ColumnName="StepStatusCode" />
                <ScalarProperty Name="WFStepStatus_Nm" ColumnName="WFStepStatus_Nm" />
                <ScalarProperty Name="Language_ID" ColumnName="Language_ID" />
                <ScalarProperty Name="WFStepStatus_ID" ColumnName="WFStepStatus_ID" />
                <ScalarProperty Name="WFStepStatusLocale_ID" ColumnName="WFStepStatusLocale_ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="WF_WFRoleLocale">
            <EntityTypeMapping TypeName="WorkFlowModel.WF_WFRoleLocale">
              <MappingFragment StoreEntitySet="GNM_WFRoleLocale">
                <ScalarProperty Name="Language_ID" ColumnName="Language_ID" />
                <ScalarProperty Name="WFRole_Name" ColumnName="WFRole_Name" />
                <ScalarProperty Name="WFRole_ID" ColumnName="WFRole_ID" />
                <ScalarProperty Name="WFRoleLocale_ID" ColumnName="WFRoleLocale_ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="True" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="True" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="None" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>