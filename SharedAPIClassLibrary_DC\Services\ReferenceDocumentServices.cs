﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class ReferenceDocumentServices
    {

        // Upload
        public static string templateDirectory = "Template";
        public static string BucketFilePath = "quest-partsassist\\EPC_UploadedFiles";
        public static DataTable ActualData = null;
        public static DataTable UploadedColumnNames = null;
        public static List<Attachements> AttachmentData = null;



        #region ::: To SaveFileToServer Uday Kumar J B 04-10-2024:::
        /// <summary>
        /// To SaveFileToServer
        /// </summary>
        public static string CreateRandomid()
        {

            string _alloedchars = "abcdefghijklmnopqrstuvwxyz01234567890";
            Random randnum = new Random((int)DateTime.Now.Ticks);
            char[] chars = new char[6];
            for (int i = 0; i < 6; i++)
            {
                chars[i] = _alloedchars[randnum.Next(_alloedchars.Length)];

            }

            return new string(chars);
        }


        public static IActionResult SaveFileToServer(IFormFile postedFile, string connString, SaveFileToServerList SaveFileToServerobj)
        {
            int TransactionID = 0;
            string responseScript = "<script>var x=window.open('','_self','','');window.opener = null;x.close();</script>";
            Common common = new Common();
            int BranchID = Convert.ToInt32(SaveFileToServerobj.Branch.ToString());
            TransactionID = SaveFileToServerobj.TransactionID == "" ? 0 : Convert.ToInt32(SaveFileToServerobj.TransactionID);
            string fullPath = string.Empty;
            string UserName = SaveFileToServerobj.User_Name.ToString();
            bool Alreadyexists = false;
            int UserID = Convert.ToInt32(SaveFileToServerobj.User_ID.ToString());
            int ObjectID = Convert.ToInt32(SaveFileToServerobj.ObjectID.ToString());
            string Description = SaveFileToServerobj.FileDescription.ToString();
            bool IsActive = SaveFileToServerobj.IsActive.ToString() == "checked" ? true : false;
            int? DetailID = SaveFileToServerobj.DetailID.ToString() == "" ? 0 : Convert.ToInt32(SaveFileToServerobj.DetailID.ToString());
            bool ISDuplicateAtta = Convert.ToBoolean(SaveFileToServerobj.ISDuplicateAtta.ToString());
            string Tablename = "GNM_REF_ATTACHMENTDETAIL";//Convert.ToString(Request.Params["Tablename"].ToString());
            Description = Common.DecryptString(Description);
            string extension = string.Empty;
            string contentType = postedFile.ContentType;
            try
            {
                if (postedFile != null && TransactionID == 0)
                {
                    string fileName = Path.GetFileName(postedFile.FileName);
                    string databaseFileName = fileName;
                    string tempLoc = $"Temp_{ObjectID}_{UserID}";
                    //string serverPath = Path.Combine(httpContext.Session.GetString("AppPathString"), "ReferenceDocuments");
                    string serverPath = BucketFilePath;
                    string path = Path.Combine(serverPath, tempLoc);
                    fullPath = Path.Combine(path, fileName);

                    // Fill the Attachment object
                    Attachements1[] attachments = new Attachements1[1];
                    attachments[0] = new Attachements1
                    {
                        FILE_NAME = databaseFileName,
                        FILEDESCRIPTION = Description,
                        OBJECTID = ObjectID,
                        TransactionID = TransactionID,
                        Tablename = Tablename,
                        DetailID = DetailID,
                        ATTACHMENTDETAIL_ID = 0,
                        AttachmentIDS = "tbl_" + CreateRandomid(),
                        UPLOADBY = UserName,
                        Upload = UserID,
                        IsActive = IsActive,
                        UPLOADDATESORT = Common.LocalTime(BranchID, DateTime.Now, connString).ToString("dd-MMM-yyyy hh:mm tt")
                    };

                    // Call to UploadAttachment
                    List<Attachements1> uploadResult = UploadAttachment(attachments, TransactionID, UserID, Convert.ToInt32(SaveFileToServerobj.Company_ID), 0, ObjectID, connString);

                    // Upload file using the common method
                    var rowsAffected = common.UploadFileItemsForImport1((Stream)postedFile, contentType, path, fileName);
                }

                return new ContentResult
                {
                    Content = responseScript,
                    ContentType = "text/html"
                };
            }
            catch (Exception ex)
            {
                // Log exception
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                return new ContentResult
                {
                    Content = $"<script>alert('Error occurred: {ex.Message}'); {responseScript}</script>",
                    ContentType = "text/html"
                };
            }
        }


        public static List<Attachements1> UploadAttachment(Attachements1[] ds, int TransactionID, int User_ID, int Company_ID, int DetailID, int ObjectID, string connString)
        {
            List<Attachements1> ds1 = new List<Attachements1>();
            string version = string.Empty;
            string Filename = string.Empty;
            string SrcPath = string.Empty;

            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int k = 0; k < ds.Count(); k++)
                    {
                        if (ds[k].FILE_NAME != null && ds[k].ATTACHMENTDETAIL_ID == 0)  // Insert new attachment
                        {
                            using (SqlCommand cmd = new SqlCommand("SP_ReferenceDocumentInsertAttachment", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;

                                // Add parameters for the stored procedure
                                cmd.Parameters.AddWithValue("@TransactionID", TransactionID);
                                cmd.Parameters.AddWithValue("@ObjectID", ds[k].OBJECTID);
                                cmd.Parameters.AddWithValue("@FileName", Common.DecryptString(ds[k].FILE_NAME));
                                cmd.Parameters.AddWithValue("@FileDescription", Common.DecryptString(ds[k].FILEDESCRIPTION));
                                cmd.Parameters.AddWithValue("@UploadBy", User_ID);
                                cmd.Parameters.AddWithValue("@UploadDate", Convert.ToDateTime(ds[k].UPLOADDATESORT));
                                cmd.Parameters.AddWithValue("@CompanyID", Company_ID);
                                cmd.Parameters.AddWithValue("@TableName", ds[k].Tablename);
                                cmd.Parameters.AddWithValue("@DetailID", DetailID);
                                cmd.Parameters.AddWithValue("@IsActive", ds[k].IsActive);

                                // Execute the stored procedure and get the inserted ID
                                int newAttachmentID = Convert.ToInt32(cmd.ExecuteScalar());


                                //string smp = Path.Combine(httpContext.Session.GetString("AppPathString"), "ReferenceDocuments");
                                string smp = BucketFilePath;
                                string dstPath = Path.Combine(smp, $"{ds[k].OBJECTID}-{newAttachmentID}-{Common.DecryptString(ds[k].FILE_NAME)}");
                                SrcPath = Path.Combine(smp, $"Temp_{Convert.ToInt32(ObjectID)}_{Convert.ToInt32(User_ID)}", Common.DecryptString(ds[k].FILE_NAME));

                                if (File.Exists(SrcPath))
                                {
                                    File.Move(SrcPath, dstPath);
                                }

                            }
                        }
                        else  // Update existing attachment
                        {
                            int RefAttachment_ID = ds[k].ATTACHMENTDETAIL_ID;

                            using (SqlCommand cmd = new SqlCommand("SP_ReferenceDocumentUpdateAttachment", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@RefAttachmentID", RefAttachment_ID);
                                cmd.Parameters.AddWithValue("@IsActive", ds[k].IsActive);

                                cmd.ExecuteNonQuery();

                                // Move file from temp to final location
                                //string smp = Path.Combine(httpContext.Session.GetString("AppPathString"), "ReferenceDocuments");
                                string smp = BucketFilePath;
                                string dstPath = Path.Combine(smp, $"{ds[k].OBJECTID}-{ds[k].ATTACHMENTDETAIL_ID}-{Common.DecryptString(ds[k].FILE_NAME)}");
                                SrcPath = Path.Combine(smp, $"Temp_{Convert.ToInt32(ObjectID)}_{Convert.ToInt32(User_ID)}", Common.DecryptString(ds[k].FILE_NAME));

                                if (File.Exists(SrcPath))
                                {
                                    File.Move(SrcPath, dstPath);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                return null;
            }

            return ds1;
        }
        #endregion


        #region ::: To SelectAttachmentDetailsAddMode Uday Kumar J B 04-10-2024:::
        /// <summary>
        /// To SelectAttachmentDetailsAddMode
        /// </summary>
        public static IActionResult SelectAttachmentDetailsAddMode(string connString, SelectAttachmentDetailsAddModeList SelectAttachmentDetailsAddModeobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            var JsonResult = default(dynamic);
            int count = 0;
            int total = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                string ViewLabel = CommonFunctionalities.GetResourceString(SelectAttachmentDetailsAddModeobj.UserCulture.ToString(), "view").ToString();
                int UserID = SelectAttachmentDetailsAddModeobj.User_ID;
                IQueryable<Attachements1> IQPOAttachmentDetails = null;
                IEnumerable<Attachements1> IEPOAttachmentDetails = null;
                int TransactionID = SelectAttachmentDetailsAddModeobj.TransactionID.ToString() == "" ? 0 : Convert.ToInt32(SelectAttachmentDetailsAddModeobj.TransactionID.ToString());
                int DetaildID = SelectAttachmentDetailsAddModeobj.DetailID.ToString() == "" ? 0 : Convert.ToInt32(SelectAttachmentDetailsAddModeobj.DetailID.ToString());
                string Tablename = SelectAttachmentDetailsAddModeobj.Tablename.ToString();

                List<Attachements1> Attache = GetAllAttachemnts(connString, TransactionID, Convert.ToInt32(SelectAttachmentDetailsAddModeobj.ObjectID.ToString()), DetaildID, Tablename);
                string jsonString = SelectAttachmentDetailsAddModeobj.AttachmentData;
                List<Attachements1> POAttachmentList = JsonConvert.DeserializeObject<List<Attachements1>>(jsonString);
                List<Attachements1> ActualList = new List<Attachements1>();
                ActualList.AddRange(Attache);
                if (POAttachmentList != null)
                {
                    ActualList.AddRange(POAttachmentList);
                }
                IEPOAttachmentDetails = (from a in ActualList
                                         select new Attachements1()
                                         {
                                             ATTACHMENTDETAIL_ID = a.ATTACHMENTDETAIL_ID,
                                             TransactionID = a.TransactionID,
                                             OBJECTID = a.OBJECTID,
                                             FILE_NAME = a.FILE_NAME,
                                             FILEDESCRIPTION = a.FILEDESCRIPTION,
                                             UPLOADBY = a.UPLOADBY,
                                             UPLOADDATESORT = a.UPLOADDATESORT,
                                             Upload = a.Upload,
                                             DetailID = a.DetailID,
                                             AttachmentIDS = a.AttachmentIDS,
                                             IsActive = a.IsActive
                                             //UPLOADDATESORT = DateTime.Now.ToString("dd-MMM-yyyy HH:mm")
                                         });

                IQPOAttachmentDetails = IEPOAttachmentDetails.AsQueryable().OrderByField(sidx, sord);
                if (_search)
                {
                    // Parse and decrypt the filters
                    Filters filtersObj = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(filters))).ToObject<Filters>();

                    // Ensure filtersObj is not null and its filter collection has more than 0 items
                    if (filtersObj != null && filtersObj.rules.Count > 0)
                    {
                        // Perform the FilterSearch operation
                        IQPOAttachmentDetails = IQPOAttachmentDetails.FilterSearch(filtersObj);
                    }
                }
                else if (advnce && !string.IsNullOrEmpty(advnceFilters))
                {
                    if (advnce != null)
                    {
                        AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<AdvanceFilter>();
                        IQPOAttachmentDetails = IQPOAttachmentDetails.AdvanceSearch(advnfilter);
                        page = 1;
                    }
                }

                count = IQPOAttachmentDetails.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                if (DetaildID > 0)
                {
                    JsonResult = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        data = (from a in IQPOAttachmentDetails
                                select new
                                {
                                    ID = a.ATTACHMENTDETAIL_ID,
                                    view = "<a title='" + ViewLabel + "' target='_blank' style='color:blue;' href='" + SelectAttachmentDetailsAddModeobj.AppPathString.ToString() + " /ReferenceDocuments/" + a.OBJECTID + "-" + a.TransactionID + "-" + a.DetailID + "-" + a.FILE_NAME + "?ver=" + DateTime.Now.Ticks.ToString() + "'' class='OpenAttachment' >" + ViewLabel + "</a>",
                                    //edit = "<img id='" + a.ATTACHMENTDETAIL_ID + "' src='" + Session["AppPathString"].ToString() + "/Content/Images/edit.gif' key='" + a.ATTACHMENTDETAIL_ID + "' class='' editmode='false'/>",
                                    edit = "<a title='Edit' href='#' style='font-size: 13px;' id='" + a.ATTACHMENTDETAIL_ID + "' key='" + a.ATTACHMENTDETAIL_ID + "' class='editAttachment' editmode='false' ><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                    delete = Convert.ToInt32(a.ATTACHMENTDETAIL_ID) == 0 ? "<input type='checkbox' key='" + a.AttachmentIDS + "' id='chk" + a.AttachmentIDS + "' class='chkToolsAttachmentDelete'/>" : "<input type='checkbox' key='" + a.ATTACHMENTDETAIL_ID + "' id='chk" + a.ATTACHMENTDETAIL_ID + "' class='chkToolsAttachmentDelete'/>",
                                    FILE_NAME = a.FILE_NAME,
                                    FILEDESCRIPTION = a.FILEDESCRIPTION,
                                    UPLOADBY = a.UPLOADBY,
                                    UPLOADDATESORT = a.UPLOADDATESORT,
                                    Upload = a.Upload,
                                    a.OBJECTID,
                                    a.DetailID,
                                    a.TransactionID,
                                    IsActive = a.IsActive,
                                    ISACTIVER = a.IsActive == true ? "Yes" : "No"
                                }).ToList().Paginate(page, rows)
                    };
                }
                else
                {
                    JsonResult = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        data = (from a in IQPOAttachmentDetails
                                select new
                                {
                                    ID = a.ATTACHMENTDETAIL_ID,
                                    view = Convert.ToInt32(a.TransactionID) == 0 ? "<a title='View' target='_blank' style='color:blue;' href='" + SelectAttachmentDetailsAddModeobj.AppPathString.ToString() + " /ReferenceDocuments/" + "Temp_" + a.OBJECTID + "_" + UserID + "/" + a.FILE_NAME + "?ver=" + DateTime.Now.Ticks.ToString() + "' class='OpenAttachment' >" + "View" + "</a>" : "<a title='View' target='_blank' style='color:blue;' href='" + SelectAttachmentDetailsAddModeobj.AppPathString.ToString() + " /ReferenceDocuments/" + a.OBJECTID + "-" + a.TransactionID + "-" + a.FILE_NAME + "?ver=" + DateTime.Now.Ticks.ToString() + "'' class='OpenAttachment' >" + "View" + "</a>",
                                    //edit = "<img id='" + a.ATTACHMENTDETAIL_ID + "' src='" + Session["AppPathString"].ToString() + "/Content/Images/edit.gif' key='" + a.ATTACHMENTDETAIL_ID + "' class='' editmode='false'/>",
                                    edit = "<a title='Edit' href='#' style='font-size: 13px;' id='" + a.ATTACHMENTDETAIL_ID + "' key='" + a.ATTACHMENTDETAIL_ID + "' class='editAttachment' editmode='false' ><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                    delete = Convert.ToInt32(a.ATTACHMENTDETAIL_ID) == 0 ? "<input type='checkbox' key='" + a.AttachmentIDS + "' id='chk" + a.AttachmentIDS + "' class='chkToolsAttachmentDelete'/>" : "<input type='checkbox' key='" + a.ATTACHMENTDETAIL_ID + "' id='chk" + a.ATTACHMENTDETAIL_ID + "' class='chkToolsAttachmentDelete'/>",
                                    FILE_NAME = a.FILE_NAME,
                                    FILEDESCRIPTION = a.FILEDESCRIPTION,
                                    UPLOADBY = a.UPLOADBY,
                                    UPLOADDATESORT = a.UPLOADDATESORT,
                                    Upload = a.Upload,
                                    a.OBJECTID,
                                    a.DetailID,
                                    a.TransactionID,
                                    IsActive = a.IsActive,
                                    ISACTIVER = a.IsActive == true ? "Yes" : "No"
                                }).ToList().Paginate(page, rows)
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return new JsonResult(JsonResult);
        }

        public static List<Attachements1> GetAllAttachemnts(string connString, int TransactionID, int ObjectID, int DetailID, string Tablename)
        {
            List<Attachements1> ds = new List<Attachements1>();

            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("SP_ReferenceDocumentGetAttachments", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@ObjectID", ObjectID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                Attachements1 attachment = new Attachements1
                                {
                                    ATTACHMENTDETAIL_ID = reader["REFATTACHMENTDETAIL_ID"] != DBNull.Value ? Convert.ToInt32(reader["REFATTACHMENTDETAIL_ID"]) : 0,
                                    TransactionID = reader["TRANSACTION_ID"] != DBNull.Value ? Convert.ToInt32(reader["TRANSACTION_ID"]) : 0,
                                    OBJECTID = reader["OBJECT_ID"] != DBNull.Value ? Convert.ToInt32(reader["OBJECT_ID"]) : 0,
                                    FILE_NAME = reader["FILENAME"] != DBNull.Value ? reader["FILENAME"].ToString() : string.Empty,
                                    FILEDESCRIPTION = reader["FILEDESCRIPTION"] != DBNull.Value ? reader["FILEDESCRIPTION"].ToString() : string.Empty,
                                    DetailID = reader["DETAIL_ID"] != DBNull.Value ? Convert.ToInt32(reader["DETAIL_ID"]) : 0,
                                    UPLOADDATE = reader["UPLOADDATE"] != DBNull.Value ? Convert.ToDateTime(reader["UPLOADDATE"]) : DateTime.MinValue,
                                    UPLOADDATESORT = reader["UPLOADDATE"] != DBNull.Value ? Convert.ToDateTime(reader["UPLOADDATE"]).ToString("dd-MMM-yyyy hh:mm tt") : string.Empty,
                                    UPLOADBY = reader["UPLOADBY"] != DBNull.Value ? reader["UPLOADBY"].ToString() : string.Empty,
                                    Upload = reader["UPLOADBY"] != DBNull.Value ? Convert.ToInt32(reader["UPLOADBY"]) : 0,
                                    AttachmentIDS = reader["REFATTACHMENTDETAIL_ID"] != DBNull.Value ? reader["REFATTACHMENTDETAIL_ID"].ToString() : string.Empty,
                                    PartID = reader["DETAIL_ID"] != DBNull.Value ? Convert.ToInt32(reader["DETAIL_ID"]) : 0,
                                    IsActive = reader["ISACTIVE"] != DBNull.Value ? Convert.ToBoolean(reader["ISACTIVE"]) : false
                                };
                                ds.Add(attachment);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return ds;
        }
        #endregion


        #region ::: To DeleteAttachments Uday Kumar J B 04-10-2024:::
        /// <summary>
        /// To DeleteAttachments
        /// </summary>
        public static string DeleteAttachments(Attachements1[] dsObj, string SMP, string connString)
        {
            string Msg = string.Empty;

            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int i = 0; i < dsObj.Count(); i++)
                    {
                        int ID = dsObj[i].ATTACHMENTDETAIL_ID;

                        if (ID != 0)
                        {
                            // Call the stored procedure to delete the row from the database
                            using (SqlCommand cmd = new SqlCommand("SP_ReferenceDocumentDeleteAttachmentByID", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@AttachmentDetailID", ID);

                                cmd.ExecuteNonQuery();
                            }

                            // Delete the file from the system
                            string filePath = SMP + "/" + dsObj[i].OBJECTID + "-" + dsObj[i].TransactionID + "-" + Common.DecryptString(dsObj[i].FILE_NAME);

                            if (System.IO.File.Exists(filePath))
                            {
                                System.IO.File.Delete(filePath);
                            }
                        }
                    }
                }

                Msg = "Deleted";
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Msg;
        }
        #endregion


        #region ::: SaveAttachment Refernce Document Uday kumar J B 04-10-2024:::
        /// <summary>
        /// Save Sales history Attachment
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult SaveAttachmentReferenceDocument(SaveAttachmentReferenceDocumentList OBJ, string connString, int LogException)
        {
            var jsonData = default(dynamic);
            try
            {
                int ObjectID = Common.GetObjectID("ReferenceDocument");
                int companyID = Convert.ToInt32(OBJ.Company_ID);

                //================================================================================================
                //------------------------------------------------------------     
                if (Convert.ToInt32(OBJ.TransactionID) == 0)
                {
                    if (AttachmentData != null)
                    {

                        string SrcPath = string.Empty;

                        List<Attachements1> dsattachment = new List<Attachements1>();
                        JObject jObj = JObject.Parse(OBJ.AttachmentData);
                        int Count = jObj["rows"].Count();
                        Attachements1[] ds = new Attachements1[Count];
                        for (int i = 0; i < Count; i++)
                        {
                            Attachements1 detail = new Attachements1();
                            ds[i] = detail;
                            JTokenReader reader = null;
                            reader = new JTokenReader(jObj["rows"][i]["REFATTACHMENTDETAIL_ID"]);
                            reader.Read();
                            ds[i].ATTACHMENTDETAIL_ID = Convert.ToInt32(reader.Value.ToString());
                            reader = new JTokenReader(jObj["rows"][i]["FILENAME"]);
                            reader.Read();
                            ds[i].FILE_NAME = Common.DecryptString(reader.Value.ToString());
                            reader = new JTokenReader(jObj["rows"][i]["FILEDESCRIPTION"]);
                            reader.Read();
                            ds[i].FILEDESCRIPTION = Common.DecryptString(reader.Value.ToString());

                            reader = new JTokenReader(jObj["rows"][i]["Upload"]);
                            reader.Read();
                            ds[i].Upload = Convert.ToInt32(reader.Value.ToString());

                            reader = new JTokenReader(jObj["rows"][i]["UPLOADDATE"]);
                            reader.Read();
                            ds[i].UPLOADDATE = Convert.ToDateTime(reader.Value.ToString());


                            reader = new JTokenReader(jObj["rows"][i]["OBJECT_ID"]);
                            reader.Read();
                            ds[i].OBJECTID = Convert.ToInt32(reader.Value.ToString());

                            reader = new JTokenReader(jObj["rows"][i]["ISACTIVE"]);
                            reader.Read();
                            ds[i].IsActive = Convert.ToBoolean(reader.Value.ToString());

                            ds[i].DetailID = 0;
                            ds[i].Tablename = OBJ.Tablename;
                            //string SMP = Server.MapPath(Session["AppPathString"] + "/ReferenceDocuments");
                            //string DstPath = SMP + "/" + ds[i].OBJECTID + "-" + TransactionID + "-" + Common.DecryptString(ds[i].FILE_NAME);
                            //SrcPath = SMP + "/" + "Temp_" + Convert.ToInt32(Session["ObjectID"].ToString()) + "_" + Convert.ToInt32(Session["User_ID"].ToString()) + "/" + Common.DecryptString(ds[i].FILE_NAME);

                            //if (System.IO.File.Exists(SrcPath))
                            //{
                            //    System.IO.File.Move(SrcPath, DstPath);
                            //}
                        }
                        List<Attachements1> c = UploadAttachment(ds, Convert.ToInt32(OBJ.TransactionID), Convert.ToInt32(OBJ.User_ID.ToString()), Convert.ToInt32(OBJ.Company_ID.ToString()), 0, Convert.ToInt32(ObjectID), connString);

                        AttachmentData = null;
                    }
                }
                else
                {
                    string SrcPath = string.Empty;

                    List<Attachements1> dsattachment = new List<Attachements1>();
                    JObject jObj = JObject.Parse(OBJ.AttachmentData);
                    int Count = jObj["rows"].Count();
                    Attachements1[] ds = new Attachements1[Count];
                    for (int i = 0; i < Count; i++)
                    {
                        Attachements1 detail = new Attachements1();
                        ds[i] = detail;
                        JTokenReader reader = null;
                        reader = new JTokenReader(jObj["rows"][i]["REFATTACHMENTDETAIL_ID"]);
                        reader.Read();
                        ds[i].ATTACHMENTDETAIL_ID = Convert.ToInt32(reader.Value.ToString());
                        reader = new JTokenReader(jObj["rows"][i]["FILENAME"]);
                        reader.Read();
                        ds[i].FILE_NAME = Common.DecryptString(reader.Value.ToString());
                        reader = new JTokenReader(jObj["rows"][i]["FILEDESCRIPTION"]);
                        reader.Read();
                        ds[i].FILEDESCRIPTION = Common.DecryptString(reader.Value.ToString());

                        reader = new JTokenReader(jObj["rows"][i]["Upload"]);
                        reader.Read();
                        ds[i].Upload = Convert.ToInt32(reader.Value.ToString());

                        reader = new JTokenReader(jObj["rows"][i]["UPLOADDATE"]);
                        reader.Read();
                        ds[i].UPLOADDATE = Convert.ToDateTime(reader.Value.ToString());


                        reader = new JTokenReader(jObj["rows"][i]["OBJECT_ID"]);
                        reader.Read();
                        ds[i].OBJECTID = Convert.ToInt32(reader.Value.ToString());

                        reader = new JTokenReader(jObj["rows"][i]["ISACTIVE"]);
                        reader.Read();
                        ds[i].IsActive = Convert.ToBoolean(reader.Value.ToString());

                        ds[i].DetailID = 0;
                        ds[i].Tablename = OBJ.Tablename;
                        //string SMP = Server.MapPath(Session["AppPathString"] + "/ReferenceDocuments");
                        //string DstPath = SMP + "/" + ds[i].OBJECTID + "-" + TransactionID + "-" + 0 + "-" + Common.DecryptString(ds[i].FILE_NAME);
                        //SrcPath = SMP + "/" + "Temp_" + Convert.ToInt32(Session["ObjectID"].ToString()) + "_" + Convert.ToInt32(Session["User_ID"].ToString()) + "/" + Common.DecryptString(ds[i].FILE_NAME);

                        //if (System.IO.File.Exists(SrcPath))
                        //{
                        //    System.IO.File.Move(SrcPath, DstPath);
                        //}
                    }
                    List<Attachements1> c = UploadAttachment(ds, Convert.ToInt32(OBJ.TransactionID), Convert.ToInt32(OBJ.User_ID.ToString()), Convert.ToInt32(OBJ.Company_ID.ToString()), 0, Convert.ToInt32(ObjectID), connString);

                }

                //List<Attachements1> dsattachdelete = new List<Attachements1>();
                //JObject jObj1 = new JObject();
                //jObj1 = JObject.Parse(Request.Params["AttachmentDelete"]);
                //int Count1 = jObj1["rows"].Count();
                //Attachements1[] ds1 = new Attachements1[Count1];
                //for (int i = 0; i < Count1; i++)
                //{
                //    Attachements1 detail = new Attachements1();
                //    ds1[i] = detail;
                //    JTokenReader reader = null;
                //    reader = new JTokenReader(jObj1["rows"][i]["id"]);
                //    reader.Read();
                //    ds1[i].ATTACHMENTDETAIL_ID = Convert.ToInt32(reader.Value.ToString());
                //    reader = new JTokenReader(jObj1["rows"][i]["FileName"]);
                //    reader.Read();
                //    ds1[i].FILE_NAME = Common.DecryptString(reader.Value.ToString());
                //    reader = new JTokenReader(jObj1["rows"][i]["Object_ID"]);
                //    reader.Read();
                //    ds1[i].OBJECTID = Convert.ToInt32(reader.Value.ToString());
                //    ds1[i].TransactionID = TransactionID;
                //}
                //DeleteAttachments(ds1, Server.MapPath(Session["AppPathString"] + "/ReferenceDocuments"));

            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }

        #endregion


        #region :::SelAllAttachment Uday kumar J B 04-10-2024:::
        /// <summary>
        /// SelAllAttachment
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult SelAllAttachment(string connString, SelAllAttachmentList SelAllAttachmentobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            var jsonData = default(dynamic);
            int BranchID = Convert.ToInt32(SelAllAttachmentobj.Branch.ToString());
            string ViewLabel = CommonFunctionalities.GetResourceString(SelAllAttachmentobj.UserCulture.ToString(), "view").ToString();

            try
            {
                int Count = 0;
                int Total = 0;
                int DetailID = SelAllAttachmentobj.DetailID.ToString() == "" ? 0 : Convert.ToInt32(SelAllAttachmentobj.DetailID.ToString());
                string ObjectName = SelAllAttachmentobj.ObjectNames.ToString();
                string Tablename = SelAllAttachmentobj.Tablename.ToString();

                // Step 1: Get ObjectID using ADO.NET
                int ObjectID = GetObjectIDData(connString, ObjectName);
                if (ObjectName == "ReferenceDocument")
                {
                    int objid = Convert.ToInt32(SelAllAttachmentobj.ObjectID.ToString());
                    string objdescription = GetObjectDescription(connString, objid);
                    if (objdescription == "Reference Document")
                    {
                        ObjectID = GetReferenceDocumentObjectID(connString, ObjectName);
                    }
                }

                // Step 2: Get all attachments using ADO.NET
                List<Attachements1> dsToolsDetails = GetAllAttachemnts(connString, SelAllAttachmentobj.TransactionID, ObjectID, DetailID, Tablename).ToList();

                // Step 3: Format the result
                var arrToolsList = dsToolsDetails.Select(a => new Attachements1
                {
                    view = a != null ? "<a title='" + ViewLabel + "' target='_blank' style='color:blue;' href='" + (SelAllAttachmentobj?.AppPathString ?? "") + "/ReferenceDocuments/" + (Convert.ToString(a.OBJECTID) ?? "") + "-" + (Convert.ToString(a.ATTACHMENTDETAIL_ID) ?? "") + "-" + (a.FILE_NAME ?? "") + "' class='OpenAttachments'>" + ViewLabel + "</a>" : "",
                    edit = a != null && a.ATTACHMENTDETAIL_ID != null ? "<a title='Edit' href='#' style='font-size: 13px;' id='" + a.ATTACHMENTDETAIL_ID + "' key='" + a.ATTACHMENTDETAIL_ID + "' class='editAttachment' editmode='false' ><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>" : "",
                    delete = a != null && Convert.ToInt32(a.ATTACHMENTDETAIL_ID) == 0 ? "<input type='checkbox' key='" + a.AttachmentIDS + "' id='chk" + a.AttachmentIDS + "' class='chkToolsAttachmentDelete'/>" : "<input type='checkbox' key='" + a.ATTACHMENTDETAIL_ID + "' id='chk" + a.ATTACHMENTDETAIL_ID + "' class='chkToolsAttachmentDelete'/>",
                    ATTACHMENTDETAIL_ID = (int)(a?.ATTACHMENTDETAIL_ID),
                    FILE_NAME = a?.FILE_NAME,
                    FILEDESCRIPTION = a?.FILEDESCRIPTION,
                    UPLOADBY = a?.UPLOADBY,
                    UPLOADDATE = (DateTime)(a?.UPLOADDATE),
                    UPLOADDATESORT = a?.UPLOADDATESORT != null ? Convert.ToDateTime(a.UPLOADDATESORT).ToString("dd-MMM-yyyy hh:mm tt") : "",
                    Upload = (int)(a?.Upload),
                    OBJECTID = (int)(a?.OBJECTID),
                    TransactionID = (int)(a?.TransactionID),
                    DetailID = a?.DetailID,
                    AttachmentIDS = a?.AttachmentIDS,
                    ID = (int)(a?.ATTACHMENTDETAIL_ID),
                    IsActive = a?.IsActive ?? false,
                    ISACTIVER = a?.IsActive == true ? "Yes" : "No"
                }).AsQueryable();


                // Step 4: Sorting and Filtering
                arrToolsList = arrToolsList.OrderByField(sidx, sord);
                if (_search)
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(filters))).ToObject<Filters>();
                    if (filtersObj != null && filtersObj.rules.Count > 0)
                    {
                        // Perform the FilterSearch operation
                        arrToolsList = arrToolsList.FilterSearch(filtersObj);
                    }

                }
                else if (advnce && !string.IsNullOrEmpty(advnceFilters))
                {
                    AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<AdvanceFilter>();
                    arrToolsList = arrToolsList.AdvanceSearch(advnfilter);
                    page = 1;
                }

                // Step 5: Pagination
                Count = arrToolsList.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                jsonData = new
                {
                    total = Total,
                    page = page,
                    records = Count,
                    data = arrToolsList.ToList().Paginate(page, rows),
                };
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return new JsonResult(jsonData);
        }

        // ADO.NET Helper Methods

        private static int GetObjectIDData(string connString, string objectName)
        {
            using (SqlConnection conn = new SqlConnection(connString))
            {
                using (SqlCommand cmd = new SqlCommand("SP_ReferenceDocumentGetObjectIDByObjectName", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@ObjectName", objectName);
                    conn.Open();
                    return Convert.ToInt32(cmd.ExecuteScalar());
                }
            }
        }

        private static string GetObjectDescription(string connString, int objid)
        {
            using (SqlConnection conn = new SqlConnection(connString))
            {
                using (SqlCommand cmd = new SqlCommand("SP_ReferenceDocumentGetObjectDescriptionById", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@ObjectID", objid);
                    conn.Open();
                    return cmd.ExecuteScalar().ToString();
                }
            }
        }

        private static int GetReferenceDocumentObjectID(string connString, string objectName)
        {
            using (SqlConnection conn = new SqlConnection(connString))
            {
                using (SqlCommand cmd = new SqlCommand("SP_ReferenceDocumentGetReferenceDocumentObjectID", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@ObjectName", objectName);
                    conn.Open();
                    return Convert.ToInt32(cmd.ExecuteScalar());
                }
            }
        }
        #endregion


        #region :::CheckAttachment Uday kumar J B 04-10-2024:::
        /// <summary>
        /// CheckAttachment
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult CheckAttachment(string connString, CheckAttachmentList CheckAttachmentobj)
        {
            // Step 1: Get the ObjectID using the Common method
            int ObjectID = Common.GetObjectID(CheckAttachmentobj.ObjectName);
            string attach = string.Empty;

            // Step 2: Use ADO.NET to call the stored procedure
            using (SqlConnection conn = new SqlConnection(connString))
            {
                using (SqlCommand cmd = new SqlCommand("SP_ReferenceDocumentCheckAttachmentByDetails", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;

                    // Step 3: Handle the file name and pass parameters
                    string FileName = CheckAttachmentobj.FileName;
                    if (FileName.Contains("\\"))
                    {
                        int lastIndex = FileName.LastIndexOf('\\') + 1;
                        int len = FileName.Length - lastIndex;
                        FileName = FileName.Substring(lastIndex, len);
                    }

                    cmd.Parameters.AddWithValue("@FileName", FileName);
                    cmd.Parameters.AddWithValue("@TransactionID", CheckAttachmentobj.TransactionID);
                    cmd.Parameters.AddWithValue("@ObjectID", ObjectID);
                    cmd.Parameters.AddWithValue("@DetailID", CheckAttachmentobj.DetailID);

                    conn.Open();

                    // Step 4: Execute the stored procedure and get the result
                    var result = cmd.ExecuteScalar();
                    attach = result?.ToString() ?? "No";
                }
            }

            return new JsonResult(attach);
        }
        #endregion


        #region :::ReferenceDocument obj and List classes Uday kumar J B 04-10-2024:::
        /// <summary>
        /// ReferenceDocument
        /// </summary>
        /// <returns>...</returns>
        public class CheckAttachmentList
        {
            public string FileName { get; set; }
            public int TransactionID { get; set; }
            public int DetailID { get; set; }
            public string ObjectName { get; set; }
        }

        public class SelAllAttachmentList
        {
            public int Branch { get; set; }
            public string UserCulture { get; set; }
            public int TransactionID { get; set; }
            public int DetailID { get; set; }
            public string ObjectNames { get; set; }
            public string Tablename { get; set; }
            public int ObjectID { get; set; }
            public string AppPathString { get; set; }
        }



        public class SaveAttachmentReferenceDocumentList
        {
            public int TransactionID { get; set; }
            public int DetailID { get; set; }
            public string Tablename { get; set; }
            public int Company_ID { get; set; }
            public string AttachmentData { get; set; }
            public int User_ID { get; set; }
            public int ObjectID { get; set; }
            public string AppPathString { get; set; }
        }


        public class SelectAttachmentDetailsAddModeList
        {
            public string UserCulture { get; set; }
            public int User_ID { get; set; }
            public int TransactionID { get; set; }
            public int DetailID { get; set; }
            public string Tablename { get; set; }
            public int ObjectID { get; set; }
            public string AttachmentData { get; set; }
            public string AppPathString { get; set; }
        }



        public class SaveFileToServerList
        {
            public int Branch { get; set; }
            public string User_Name { get; set; }
            public int User_ID { get; set; }
            public int ObjectID { get; set; }
            public string TransactionID { get; set; }
            public string FileDescription { get; set; }
            public bool IsActive { get; set; }
            public int DetailID { get; set; }
            public bool ISDuplicateAtta { get; set; }
            public int Company_ID { get; set; }
            public string AppPathString { get; set; }
        }
        #endregion


        #region :::ReferenceDocument  classes Uday kumar J B 04-10-2024:::
        /// <summary>
        /// ReferenceDocument
        /// </summary>
        /// <returns>...</returns>
        public class Attachements1
        {
            public int ATTACHMENTDETAIL_ID
            {
                get;
                set;
            }
            public string AttachmentIDS { get; set; }
            public int TransactionID { get; set; }
            public string FILE_NAME { get; set; }
            public string FILEDESCRIPTION { get; set; }
            public string UPLOADBY { get; set; }
            public DateTime UPLOADDATE { get; set; }
            public string UPLOADDATESORT { get; set; }
            public string delete { get; set; }
            public int Upload { get; set; }
            public string view { get; set; }
            public int OBJECTID { get; set; }
            public string DocumentType { get; set; }
            public int DocumentType_ID { get; set; }
            public int? DetailID { get; set; }
            public int ID { get; set; }
            public string edit { get; set; }
            public string Tablename { get; set; }

            public int PartID { get; set; }
            public string TransactionType { get; set; }
            public string TransactionNumber { get; set; }
            public string Date { get; set; }
            public string Amount { get; set; }
            public int ModelID { get; set; }
            public bool IsActive { get; set; }
            public string ISACTIVER { get; set; }
        }
        #endregion


    }
}
