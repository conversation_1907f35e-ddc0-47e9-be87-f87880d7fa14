﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Reflection;
using System.Resources;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.CoreProductMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class SRM_OperationsServices
    {

        int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));


        #region :::GetGlobalResourceObject   Uday Kumar J B 11-07-2024 :::
        /// <summary>
        /// To Get GlobalResourceObject 
        /// </summary>
        /// 
        public static string GetGlobalResourceObject(string cultureValue, string resourceKey, Assembly assembly = null)
        {
            try
            {
                if (assembly == null)
                {
                    assembly = Assembly.GetExecutingAssembly();
                }

                string cultureIdentifier = cultureValue.Replace("Resource_", "");
                string resourceNamespace = assembly.GetName().Name + ".App_GlobalResources.";
                string resourceFileName = "resource_" + cultureIdentifier.ToLowerInvariant();
                ResourceManager resourceManager = new ResourceManager(resourceNamespace + resourceFileName, assembly);
                string resourceValue = resourceManager.GetString(resourceKey);

                return resourceValue;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error accessing resource: {ex.Message}");
                return string.Empty;
            }
        }
        #endregion


        #region ::: Select Uday Kumar J B 02-08-2024  :::
        /// <summary>
        /// To Select Operation
        /// </summary>
        /// 
        public static IActionResult Select(string connString, SelectOperationList SelectOperationobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            var jsonData = new object();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                string AppPath = string.Empty;
                int Count = 0;
                int Total = 0;
                int Company_ID = Convert.ToInt32(SelectOperationobj.Company_ID);

                string YesE = GetGlobalResourceObject(SelectOperationobj.GeneralCulture.ToString(), "Yes")?.ToString() ?? "Yes";
                string NoE = GetGlobalResourceObject(SelectOperationobj.GeneralCulture.ToString(), "No")?.ToString() ?? "No";
                string YesL = GetGlobalResourceObject(SelectOperationobj.GeneralCulture.ToString(), "Yes")?.ToString() ?? "Yes";
                string NoL = GetGlobalResourceObject(SelectOperationobj.GeneralCulture.ToString(), "No")?.ToString() ?? "No";

                List<ParentCompanyObject> ParentCompanyDetails = new List<ParentCompanyObject>();
                List<OperationMaster> IEOperationMasterArray = new List<OperationMaster>();
                List<SRM_Operation> IEOperationList = new List<SRM_Operation>();

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Retrieve ParentCompanyDetails using stored procedure
                    using (SqlCommand command = new SqlCommand("GetParentCompanyDetailsoperations", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@Company_ID", Company_ID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ParentCompanyObject parentCompany = new ParentCompanyObject
                                {
                                    Company_ID = Convert.ToInt32(reader["Company_ID"]),
                                    Company_Name = reader["Company_Name"].ToString(),
                                    Company_Parent_ID = Convert.ToInt32(reader["Company_Parent_ID"])
                                };
                                ParentCompanyDetails.Add(parentCompany);
                            }
                        }
                    }

                    // Retrieve Operation List using stored procedure
                    using (SqlCommand command = new SqlCommand("GetOperationList", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                SRM_Operation operation = new SRM_Operation
                                {
                                    Operation_ID = Convert.ToInt32(reader["Operation_ID"]),
                                    Operation_Description = reader["Operation_Description"].ToString(),
                                    Operation_Code = reader["Operation_Code"].ToString(),
                                    FunctionGroup_ID = reader["FunctionGroup_ID"] != DBNull.Value ? Convert.ToInt32(reader["FunctionGroup_ID"]) : (int?)null,
                                    Skill_ID = reader["Skill_ID"] != DBNull.Value ? Convert.ToInt32(reader["Skill_ID"]) : (int?)null,
                                    Company_ID = Convert.ToInt32(reader["Company_ID"]),
                                    Operation_IsActive = reader["Operation_IsActive"] != DBNull.Value && Convert.ToBoolean(reader["Operation_IsActive"])
                                };
                                IEOperationList.Add(operation);
                            }
                        }
                    }

                    // Retrieve Function Groups using stored procedure
                    List<GNM_FunctionGroup> FunctionGroups = new List<GNM_FunctionGroup>();
                    using (SqlCommand command = new SqlCommand("GetFunctionGroups", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                GNM_FunctionGroup functionGroup = new GNM_FunctionGroup
                                {
                                    FunctionGroup_ID = Convert.ToInt32(reader["FunctionGroup_ID"]),
                                    FunctionGroup_Name = reader["FunctionGroup_Name"].ToString()
                                };
                                FunctionGroups.Add(functionGroup);
                            }
                        }
                    }

                    // Retrieve Skills using stored procedure
                    List<GNM_RefMasterDetail> Skills = new List<GNM_RefMasterDetail>();
                    using (SqlCommand command = new SqlCommand("GetSkills", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                GNM_RefMasterDetail skill = new GNM_RefMasterDetail
                                {
                                    RefMasterDetail_ID = Convert.ToInt32(reader["RefMasterDetail_ID"]),
                                    RefMasterDetail_Name = reader["RefMasterDetail_Name"].ToString()
                                };
                                Skills.Add(skill);
                            }
                        }
                    }

                    // Perform join operations and populate IEOperationMasterArray
                    foreach (var operation in IEOperationList)
                    {
                        string FunctionGroup_Name = string.Empty;
                        string SkillName = string.Empty;

                        var functionGroup = FunctionGroups.FirstOrDefault(fg => fg.FunctionGroup_ID == operation.FunctionGroup_ID);
                        if (functionGroup != null)
                        {
                            FunctionGroup_Name = functionGroup.FunctionGroup_Name;
                        }

                        var skill = Skills.FirstOrDefault(r => r.RefMasterDetail_ID == operation.Skill_ID);
                        if (skill != null)
                        {
                            SkillName = skill.RefMasterDetail_Name;
                        }

                        var parentCompany = ParentCompanyDetails.FirstOrDefault(pc => pc.Company_ID == operation.Company_ID);

                        IEOperationMasterArray.Add(new OperationMaster
                        {
                            Operation_ID = operation.Operation_ID,
                            Operation_Description = operation.Operation_Description,
                            Operation_Code = operation.Operation_Code,
                            FunctionGroup_Name = FunctionGroup_Name,
                            SkillName = SkillName,
                            Company_ID = operation.Company_ID,
                            Operation_IsActive = operation.Operation_IsActive ? YesE : NoE
                        });
                    }

                    if (SelectOperationobj.Language_ID != Convert.ToInt32(SelectOperationobj.GeneralLanguageID))
                    {
                        // Retrieve localized operation list using stored procedure
                        using (SqlCommand command = new SqlCommand("GetLocalizedOperationList", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@LanguageID", SelectOperationobj.Language_ID);
                            command.Parameters.AddWithValue("@CompanyID", Company_ID);

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    IEOperationMasterArray.Add(new OperationMaster
                                    {
                                        Operation_ID = Convert.ToInt32(reader["Operation_ID"]),
                                        Operation_Description = reader["Operation_Description"].ToString(),
                                        Operation_Code = reader["Operation_Code"].ToString(),
                                        FunctionGroup_Name = reader["FunctionGroup_Name"] == DBNull.Value ? string.Empty : reader["FunctionGroup_Name"].ToString(),
                                        SkillName = reader["RefMasterDetail_Name"] == DBNull.Value ? string.Empty : reader["RefMasterDetail_Name"].ToString(),
                                        Company_ID = Convert.ToInt32(reader["Company_ID"]),
                                        Operation_IsActive = Convert.ToBoolean(reader["Operation_IsActive"]) ? YesL : NoL
                                    });
                                }
                            }
                        }
                    }

                    connection.Close();
                }

                // Convert to IQueryable for further filtering and sorting
                IQueryable<OperationMaster> IQOperationMaster = IEOperationMasterArray.AsQueryable();

                // Perform search and filtering
                if (_search)
                {
                    Filters searchFilters = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    IQOperationMaster = IQOperationMaster.FilterSearch<OperationMaster>(searchFilters);
                }
                else if (advnce)
                {
                    AdvanceFilter advnceFilter = JObject.Parse(advnceFilters).ToObject<AdvanceFilter>();
                    IQOperationMaster = IQOperationMaster.AdvanceSearch<OperationMaster>(advnceFilter);
                }

                // Perform sorting
                IQOperationMaster = IQOperationMaster.OrderByField<OperationMaster>(sidx, sord);

                // Adjust page number if needed
                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                // Ensure page index is valid
                if (page <= 0)
                {
                    page = 1; // Start from page 1 if page index is less than or equal to 0
                }

                // Calculate the starting index for the current page
                int startIndex = (page - 1) * rows;

                // Calculate the number of items to retrieve for the current page
                int takeCount = Math.Min(rows, Count - startIndex);

                string viewTitle = GetGlobalResourceObject(SelectOperationobj.UserCulture.ToString(), "view")?.ToString() ?? "View";
                // Get the items for the current page
                var pagedData = IQOperationMaster.Skip(startIndex).Take(takeCount).Select(a => new
                {
                    edit = $"<a title='{viewTitle}' href='#' id='{a.Operation_ID}' Operation_Code='{a.Operation_Code}' key='{a.Operation_ID}' Company_ID='{a.Company_ID}' class='EditOperation font-icon-class' IsEditable='{(a.Company_ID == Company_ID ? "1" : "0")}'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                    delete = "<input type='checkbox' key='" + a.Operation_ID + "' defaultchecked='' id='chk" + a.Operation_ID + "' class='" + (a.Company_ID == Company_ID ? "OperationDelete" : "OperationDeleteNotAllowed") + "'/>",
                    Operation_Description = a.Operation_Description,
                    Operation_Code = a.Operation_Code,
                    FunctionGroup_Name = a.FunctionGroup_Name,
                    SkillName = a.SkillName,
                    a.Operation_IsActive,
                    Locale = "<a key='" + a.Operation_ID + "' Company_ID='" + a.Company_ID + "' src='" + AppPath + "/Content/local.png' class='OperatioLocale' alt='Localize' width='20' height='20'  title='Localize' IsAddOrEditable='" + (a.Company_ID == Company_ID ? "1" : "0") + "'><i class='fa fa-globe'></i></a>",
                    View = "<img id='" + a.Operation_ID + "' Company_ID='" + a.Company_ID + "' Operation_Code='" + a.Operation_Code + "'  src='" + AppPath + "/Content/plus.gif' key='" + a.Operation_ID + "' class='ViewOperationLocale'/>",
                }).ToList();

                // Prepare the final JSON response
                jsonData = new
                {
                    total = Total,
                    page = page,
                    rows = pagedData,
                    records = Count,
                    Lbl_Refresh = GetGlobalResourceObject(SelectOperationobj.UserCulture.ToString(), "refresh")?.ToString() ?? "Refresh",
                    Lbl_AdvanceSearch = GetGlobalResourceObject(SelectOperationobj.UserCulture.ToString(), "advancesearch")?.ToString() ?? "Advance Search"
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: Insert Uday Kumar J B 02-08-2024:::
        /// <summary>
        /// To Insert Operation header
        /// </summary>
        /// 

        public static IActionResult Insert(string connString, InsertOperationsList InsertOperationsobj)
        {
            var result = new { Operation_ID = 0 }; // Default result
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // Parse JSON data from request
                string json = InsertOperationsobj.data;
                var data = JsonConvert.DeserializeObject<JObject>(json);

                // Extract values from JSON
                string operationDescription = Common.DecryptString(data["Operation_Description"].ToString());
                string operationCode = Common.DecryptString(data["Operation_Code"].ToString());
                int functionGroupID = Convert.ToInt32(data["FunctionGroup_ID"]);
                int skillID = Convert.ToInt32(data["Skill_ID"]);
                byte operationSkillLevel = Convert.ToByte(data["Operation_SkillLevel"].ToString() == "" ? "0" : data["Operation_SkillLevel"].ToString());
                decimal? operationStandardTime = null;
                if (!string.IsNullOrEmpty(data["Operation_StandardTime"].ToString()))
                    operationStandardTime = Convert.ToDecimal(data["Operation_StandardTime"].ToString());

                decimal? operationTime = null;
                if (!string.IsNullOrEmpty(data["Operation_Time"].ToString()))
                    operationTime = Convert.ToDecimal(data["Operation_Time"].ToString());

                bool operationIsActive = Convert.ToBoolean(data["Operation_IsActive"]);

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Prepare command to execute stored procedure
                    SqlCommand command = new SqlCommand("sp_InsertOperation", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    // Add parameters
                    command.Parameters.AddWithValue("@Operation_Description", operationDescription);
                    command.Parameters.AddWithValue("@Operation_Code", operationCode);
                    command.Parameters.AddWithValue("@FunctionGroup_ID", functionGroupID);
                    command.Parameters.AddWithValue("@Skill_ID", skillID);
                    command.Parameters.AddWithValue("@Operation_SkillLevel", operationSkillLevel);
                    command.Parameters.AddWithValue("@Operation_StandardTime", (object)operationStandardTime ?? DBNull.Value); // Handle null values
                    command.Parameters.AddWithValue("@Operation_Time", (object)operationTime ?? DBNull.Value); // Handle null values
                    command.Parameters.AddWithValue("@Operation_IsActive", operationIsActive);
                    command.Parameters.AddWithValue("@Company_ID", Convert.ToInt32(InsertOperationsobj.Company_ID));
                    command.Parameters.AddWithValue("@ModifiedBy", Convert.ToInt32(InsertOperationsobj.User_ID));
                    command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                    // Add output parameter to retrieve new operation ID
                    SqlParameter outputParam = new SqlParameter("@NewOperationID", SqlDbType.Int) { Direction = ParameterDirection.Output };
                    command.Parameters.Add(outputParam);

                    // Execute command
                    command.ExecuteNonQuery();

                    // Retrieve new operation ID from output parameter
                    int newOperationID = (int)outputParam.Value;

                    // Update result with new Operation_ID
                    result = new { Operation_ID = newOperationID };

                    // Log insertion
                    DateTime loginDatetime = Convert.ToDateTime(InsertOperationsobj.LoggedINDateTime);
                    //gbl.InsertGPSDetails(
                    //    Convert.ToInt32(InsertOperationsobj.Company_ID),
                    //    Convert.ToInt32(InsertOperationsobj.Branch),
                    //    Convert.ToInt32(InsertOperationsobj.User_ID),
                    //    Convert.ToInt32(Common.GetObjectID("SRM_Operations")),
                    //    newOperationID,
                    //    0,
                    //    0,
                    //    "Inserted " + operationCode,
                    //    false,
                    //    Convert.ToInt32(InsertOperationsobj.MenuID),
                    //    loginDatetime,
                    //    null);
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(result);
        }
        #endregion


        #region ::: Update Uday Kumar J B 02-08-2024 :::
        /// <summary>
        /// To Update Operation header
        /// </summary>
        /// 

        public static IActionResult Update(string connString, UpdateOperationsList UpdateOperationsobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // Parse JSON data from request
                string json = UpdateOperationsobj.data;
                var data = JsonConvert.DeserializeObject<JObject>(json);

                // Extract values from JSON
                int operationID = Convert.ToInt32(data["Operation_ID"]);
                string operationDescription = Common.DecryptString(data["Operation_Description"].ToString());
                string operationCode = Common.DecryptString(data["Operation_Code"].ToString());
                int functionGroupID = Convert.ToInt32(data["FunctionGroup_ID"]);
                int skillID = Convert.ToInt32(data["Skill_ID"]);
                byte operationSkillLevel = Convert.ToByte(data["Operation_SkillLevel"].ToString() == "" ? "0" : data["Operation_SkillLevel"].ToString());
                decimal? operationStandardTime = string.IsNullOrEmpty(data["Operation_StandardTime"].ToString()) ? (decimal?)null : Convert.ToDecimal(data["Operation_StandardTime"].ToString());
                decimal? operationTime = string.IsNullOrEmpty(data["Operation_Time"].ToString()) ? (decimal?)null : Convert.ToDecimal(data["Operation_Time"].ToString());
                bool operationIsActive = Convert.ToBoolean(data["Operation_IsActive"]);

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    // Prepare command to execute stored procedure for update
                    using (SqlCommand command = new SqlCommand("sp_UpdateOperation", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        command.Parameters.AddWithValue("@Operation_ID", operationID);
                        command.Parameters.AddWithValue("@Operation_Description", operationDescription);
                        command.Parameters.AddWithValue("@Operation_Code", operationCode);
                        command.Parameters.AddWithValue("@FunctionGroup_ID", functionGroupID);
                        command.Parameters.AddWithValue("@Skill_ID", skillID);
                        command.Parameters.AddWithValue("@Operation_SkillLevel", operationSkillLevel);
                        command.Parameters.AddWithValue("@Operation_StandardTime", (object)operationStandardTime ?? DBNull.Value);
                        command.Parameters.AddWithValue("@Operation_Time", (object)operationTime ?? DBNull.Value);
                        command.Parameters.AddWithValue("@Operation_IsActive", operationIsActive);
                        command.Parameters.AddWithValue("@ModifiedBy", Convert.ToInt32(UpdateOperationsobj.User_ID));
                        command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                        // Execute the update
                        int rowsAffected = command.ExecuteNonQuery();

                        // Check if update was successful
                        if (rowsAffected > 0)
                        {
                            // Log update
                            DateTime loginDatetime = Convert.ToDateTime(UpdateOperationsobj.LoggedINDateTime);

                            // Retrieve the updated row using another stored procedure
                            using (SqlCommand selectCommand = new SqlCommand("sp_GetUpdatedOperation", connection))
                            {
                                selectCommand.CommandType = CommandType.StoredProcedure;
                                selectCommand.Parameters.AddWithValue("@Operation_ID", operationID);
                                using (SqlDataReader reader = selectCommand.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        int updatedOperationID = reader.GetInt32(0);
                                        string updatedOperationCode = reader.GetString(1);

                                        //gbl.InsertGPSDetails(
                                        //    Convert.ToInt32(UpdateOperationsobj.Company_ID),
                                        //    Convert.ToInt32(UpdateOperationsobj.Branch),
                                        //    Convert.ToInt32(UpdateOperationsobj.User_ID),
                                        //    Convert.ToInt32(Common.GetObjectID("SRM_Operations")),
                                        //    updatedOperationID,
                                        //    0,
                                        //    0,
                                        //    "Updated " + updatedOperationCode,
                                        //    false,
                                        //    Convert.ToInt32(UpdateOperationsobj.MenuID),
                                        //    loginDatetime,
                                        //    null);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(null);
        }
        #endregion


        #region ::: Delete Uday Kumar J B 02-08-2024:::
        /// <summary>
        ///To Delete Operation
        /// </summary> 
        /// 

        public static IActionResult Delete(string connString, DeleteOperationList DeleteOperationobj)
        {
            string msg = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // Parse JSON data from request
                string json = DeleteOperationobj.key;
                var data = JsonConvert.DeserializeObject<JObject>(json);

                // Iterate through each ID to delete
                foreach (var item in data["rows"])
                {
                    int id = Convert.ToInt32(item["id"]);

                    // Execute deletion stored procedure
                    using (SqlConnection connection = new SqlConnection(connString))
                    {
                        connection.Open();

                        // Prepare command to execute stored procedure
                        SqlCommand command = new SqlCommand("sp_DeleteOperation", connection);
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@Operation_ID", id);

                        // Execute the stored procedure
                        SqlDataReader reader = command.ExecuteReader();
                        if (reader.Read())
                        {
                            msg += reader["Message"].ToString();
                        }

                        // Close connection
                        connection.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle specific constraint violation error
                if (ex.InnerException?.InnerException?.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint") ?? false)
                {
                    msg += GetGlobalResourceObject(DeleteOperationobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    msg += "An error occurred while deleting.";
                    // Log exception if needed
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }
                }
            }

            return new JsonResult(msg);
        }
        #endregion


        #region ::: Export Uday Kumar J B 02-08-2024 :::
        /// <summary>
        /// Exporting Company Grid
        /// </summary>
        /// 

        //public static IActionResult Export(string connString, ExportOperationsList ExportOperationsobj)
        //{
        //    int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
        //    try
        //    {
        //        List<OperationMaster> operationList = new List<OperationMaster>();

        //        using (SqlConnection connection = new SqlConnection(connString))
        //        {
        //            connection.Open();

        //            SqlCommand command = new SqlCommand("sp_GetOperationsForExport", connection);
        //            command.CommandType = CommandType.StoredProcedure;

        //            using (SqlDataReader reader = command.ExecuteReader())
        //            {
        //                while (reader.Read())
        //                {
        //                    operationList.Add(new OperationMaster
        //                    {
        //                        Operation_Description = reader["Operation_Description"].ToString(),
        //                        Operation_Code = reader["Operation_Code"].ToString(),
        //                        FunctionGroup_Name = reader["FunctionGroup_Name"].ToString(),
        //                        SkillName = reader["SkillName"].ToString(),
        //                        Operation_IsActive = Convert.ToBoolean(reader["Operation_IsActive"]).ToString()
        //                    });
        //                }
        //            }

        //            connection.Close();
        //        }

        //        // Assuming ReportExport.Export is a method responsible for exporting
        //        DataTable operationDataTable = new DataTable();
        //        operationDataTable.Columns.Add("Description");
        //        operationDataTable.Columns.Add("Code");
        //        operationDataTable.Columns.Add("FunctionGroup");
        //        operationDataTable.Columns.Add("Skill");
        //        operationDataTable.Columns.Add("Active");

        //        foreach (var operation in operationList)
        //        {
        //            operationDataTable.Rows.Add(
        //                operation.Operation_Description,
        //                operation.Operation_Code,
        //                operation.FunctionGroup_Name,
        //                operation.SkillName,
        //                operation.Operation_IsActive
        //            );
        //        }

        //        // Call the export method
        //        ReportExport.Export(ExportOperationsobj.exprtType, operationDataTable, null, null, "Operation", "Operation");
        //    }
        //    catch (Exception ex)
        //    {
        //        // Log exception if needed
        //        if (LogException == 1)
        //        {
        //            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
        //        }
        //        throw; // Rethrow the exception to propagate it up the call stack
        //    }
        //    return new JsonResult(null);
        //}
        #endregion


        #region ::: SelectOperationProductDetail Uday Kumar J B 02-08-2024 :::
        /// <summary>
        /// To Select Operation Product Detail
        /// </summary>
        /// 

        public static IActionResult SelectOperationProductDetail(string connString, SelectOperationProductDetailList SelectOperationProductDetailobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int Count = 0;
                int Total = 0;
                int Company_ID = Convert.ToInt32(SelectOperationProductDetailobj.Company_ID);

                List<OperationProductDetail> operationProductDetails = new List<OperationProductDetail>();
                List<GNM_CompanyBrands> companyBrands = new List<GNM_CompanyBrands>();
                List<GNM_RefMasterDetail> brandList = new List<GNM_RefMasterDetail>();
                List<GNM_ProductType> productTypeList = new List<GNM_ProductType>();
                List<GNM_Model> modelList = new List<GNM_Model>();
                List<GNM_RefMasterDetailLocale> brandLocaleList = new List<GNM_RefMasterDetailLocale>();
                List<GNM_ProductTypeLocale> productTypeLocaleList = new List<GNM_ProductTypeLocale>();
                List<GNM_ModelLocale> modelLocaleList = new List<GNM_ModelLocale>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Get OperationProductDetail
                    using (SqlCommand cmd = new SqlCommand("spGetOperationProductDetail", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@OperationID", SelectOperationProductDetailobj.OperationID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                operationProductDetails.Add(new OperationProductDetail
                                {
                                    OperationProductDetail_ID = reader.GetInt32(reader.GetOrdinal("OperationProductDetail_ID")),
                                    Brand = reader.GetString(reader.GetOrdinal("Brand_ID")),
                                    ProductType_ID = reader.GetInt32(reader.GetOrdinal("ProductType_ID")),
                                    ModelID = reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                });
                            }
                        }
                    }

                    // Get Company Brands
                    using (SqlCommand cmd = new SqlCommand("spGetCompanyBrands", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", Company_ID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                companyBrands.Add(new GNM_CompanyBrands
                                {
                                    Brand_ID = reader.GetInt32(reader.GetOrdinal("Brand_ID")),
                                });
                            }
                        }
                    }

                    // Get Brand List
                    using (SqlCommand cmd = new SqlCommand("spGetActiveBrandDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                brandList.Add(new GNM_RefMasterDetail
                                {
                                    RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                    RefMasterDetail_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name")),
                                });
                            }
                        }
                    }

                    // Get Product Type List
                    using (SqlCommand cmd = new SqlCommand("spGetActiveProductTypes", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                productTypeList.Add(new GNM_ProductType
                                {
                                    ProductType_ID = reader.GetInt32(reader.GetOrdinal("ProductType_ID")),
                                    ProductType_Name = reader.GetString(reader.GetOrdinal("ProductType_Name")),
                                });
                            }
                        }
                    }

                    // Get Model List
                    using (SqlCommand cmd = new SqlCommand("spGetActiveModels", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                modelList.Add(new GNM_Model
                                {
                                    Model_ID = reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                    Model_Name = reader.GetString(reader.GetOrdinal("Model_Name")),
                                });
                            }
                        }
                    }

                    if (SelectOperationProductDetailobj.LanguageID != Convert.ToInt32(SelectOperationProductDetailobj.GeneralLanguageID))
                    {
                        // Get Localized Brand List
                        using (SqlCommand cmd = new SqlCommand("spGetLocalizedBrandDetails", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@LanguageID", SelectOperationProductDetailobj.LanguageID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    brandLocaleList.Add(new GNM_RefMasterDetailLocale
                                    {
                                        RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                        RefMasterDetail_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name")),
                                    });
                                }
                            }
                        }

                        // Get Localized Product Type List
                        using (SqlCommand cmd = new SqlCommand("spGetLocalizedProductTypes", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@LanguageID", SelectOperationProductDetailobj.LanguageID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    productTypeLocaleList.Add(new GNM_ProductTypeLocale
                                    {
                                        ProductType_ID = reader.GetInt32(reader.GetOrdinal("ProductType_ID")),
                                        ProductType_Name = reader.GetString(reader.GetOrdinal("ProductType_Name")),
                                    });
                                }
                            }
                        }

                        // Get Localized Model List
                        using (SqlCommand cmd = new SqlCommand("spGetLocalizedModels", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@LanguageID", SelectOperationProductDetailobj.LanguageID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    modelLocaleList.Add(new GNM_ModelLocale
                                    {
                                        Model_ID = reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                        Model_Name = reader.GetString(reader.GetOrdinal("Model_Name")),
                                    });
                                }
                            }
                        }
                    }
                }

                // Process data for JSON responses
                var brandData = (from cb in companyBrands
                                 join b in brandList on cb.Brand_ID equals b.RefMasterDetail_ID
                                 orderby b.RefMasterDetail_Name
                                 select new
                                 {
                                     ID = b.RefMasterDetail_ID,
                                     Name = b.RefMasterDetail_Name
                                 }).ToList();

                string JsonBrand = "-1:--" + GetGlobalResourceObject(SelectOperationProductDetailobj.GeneralCulture.ToString(), "select").ToString() + "--;";
                foreach (var item in brandData)
                {
                    JsonBrand += item.ID + ":" + item.Name + ";";
                }
                JsonBrand = JsonBrand.TrimEnd(new char[] { ';' });

                string JsonProductType = "-1:--" + GetGlobalResourceObject(SelectOperationProductDetailobj.GeneralCulture.ToString(), "select").ToString() + "--;";
                foreach (var item in productTypeList)
                {
                    JsonProductType += item.ProductType_ID + ":" + item.ProductType_Name + ";";
                }
                JsonProductType = JsonProductType.TrimEnd(new char[] { ';' });

                string JsonModel = "-1:--" + GetGlobalResourceObject(SelectOperationProductDetailobj.GeneralCulture.ToString(), "select").ToString() + "--;";
                foreach (var item in modelList)
                {
                    JsonModel += item.Model_ID + ":" + item.Model_Name + ";";
                }
                JsonModel = JsonModel.TrimEnd(new char[] { ';' });

                // Join and filter data based on language
                if (SelectOperationProductDetailobj.LanguageID == Convert.ToInt32(SelectOperationProductDetailobj.GeneralLanguageID))
                {
                    operationProductDetails = (from a in operationProductDetails
                                               join b in brandList on Convert.ToInt32(a.Brand) equals b.RefMasterDetail_ID into BrandAlias
                                               from BrandDetail in BrandAlias.DefaultIfEmpty()
                                               join c in productTypeList on a.ProductType_ID equals c.ProductType_ID into ProductTypeAlias
                                               from ProductTypeDetail in ProductTypeAlias.DefaultIfEmpty()
                                               join d in modelList on a.ModelID equals d.Model_ID into ModelAlias
                                               from ModelDetail in ModelAlias.DefaultIfEmpty()
                                               select new OperationProductDetail
                                               {
                                                   OperationProductDetail_ID = a.OperationProductDetail_ID,
                                                   Brand = BrandDetail != null ? BrandDetail.RefMasterDetail_Name : "Unknown",
                                                   ProductType = ProductTypeDetail != null ? ProductTypeDetail.ProductType_Name : "Unknown",
                                                   Model = ModelDetail != null ? ModelDetail.Model_Name : "Unknown",
                                                   ModelID = ModelDetail != null ? ModelDetail.Model_ID : 0,
                                                   ProductType_ID = ProductTypeDetail != null ? ProductTypeDetail.ProductType_ID : 0
                                               }).ToList();
                }
                else
                {
                    // Use locale-specific lists if language ID does not match general language ID
                    operationProductDetails = (from a in operationProductDetails
                                               join b in brandLocaleList on Convert.ToInt32(a.Brand) equals b.RefMasterDetail_ID into BrandAlias
                                               from BrandDetail in BrandAlias.DefaultIfEmpty()
                                               join c in productTypeLocaleList on a.ProductType_ID equals c.ProductType_ID into ProductTypeAlias
                                               from ProductTypeDetail in ProductTypeAlias.DefaultIfEmpty()
                                               join d in modelLocaleList on a.ModelID equals d.Model_ID into ModelAlias
                                               from ModelDetail in ModelAlias.DefaultIfEmpty()
                                               select new OperationProductDetail
                                               {
                                                   OperationProductDetail_ID = a.OperationProductDetail_ID,
                                                   Brand = BrandDetail != null ? BrandDetail.RefMasterDetail_Name : "Unknown",
                                                   ProductType = ProductTypeDetail != null ? ProductTypeDetail.ProductType_Name : "Unknown",
                                                   Model = ModelDetail != null ? ModelDetail.Model_Name : "Unknown",
                                                   ModelID = ModelDetail != null ? ModelDetail.Model_ID : 0,
                                                   ProductType_ID = ProductTypeDetail != null ? ProductTypeDetail.ProductType_ID : 0
                                               }).ToList();
                }

                // Sorting
                if (sord == "asc")
                {
                    operationProductDetails = operationProductDetails.OrderBy(x => x.GetType().GetProperty(sidx).GetValue(x, null)).ToList();
                }
                else
                {
                    operationProductDetails = operationProductDetails.OrderByDescending(x => x.GetType().GetProperty(sidx).GetValue(x, null)).ToList();
                }

                // Pagination
                Count = operationProductDetails.Count();
                Total = (int)Math.Ceiling((float)Count / (float)rows);

                var paginatedData = operationProductDetails.Skip((page - 1) * rows).Take(rows).Select(a => new
                {
                    ID = a.OperationProductDetail_ID,
                    edit = "<a title=" + GetGlobalResourceObject(SelectOperationProductDetailobj.GeneralCulture.ToString(), "edit").ToString() + " href='#' id='" + a.OperationProductDetail_ID + "' key='" + a.OperationProductDetail_ID + "' class='EditOperationProductDetail font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                    delete = "<input type='checkbox' key='" + a.OperationProductDetail_ID + "' defaultchecked='' id='chk" + a.OperationProductDetail_ID + "' class='OperationProductDetailDelete'/>",
                    Brand = a.Brand,
                    ProductType = a.ProductType,
                    Model = a.Model,
                    Model_ID = a.ModelID,
                    ProductType_ID = a.ProductType_ID
                }).ToList();

                var JsonResult = new JsonResult(new
                {
                    total = Total,
                    page,
                    rows = paginatedData,
                    records = Count,
                    JsonBrand,
                    JsonProductType,
                    JsonModel
                });

                return JsonResult;
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return new JsonResult(null);
        }
        #endregion


        #region ::: SelectOperationBranchDetail Uday Kumar J B 02-08-2024 :::
        /// <summary>
        /// To Select Operation Branch Detail
        /// </summary>
        /// 

        public static IActionResult SelectOperationBranchDetail(string connString, SelectOperationBranchDetailList SelectOperationBranchDetailobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            var JsonResult = new object();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int Count = 0;
                int Total = 0;
                int Company_ID = Convert.ToInt32(SelectOperationBranchDetailobj.Company_ID);
                string CompanyType = string.Empty;

                // Get Company Type using ADO.NET
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Get Company Type
                    using (SqlCommand cmd = new SqlCommand("GetCompanyType", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add(new SqlParameter("@CompanyID", Company_ID));
                        SqlParameter companyTypeParam = new SqlParameter("@CompanyType", SqlDbType.NVarChar, 10);
                        companyTypeParam.Direction = ParameterDirection.Output;
                        cmd.Parameters.Add(companyTypeParam);
                        cmd.ExecuteNonQuery();
                        CompanyType = companyTypeParam.Value.ToString();
                    }

                    // Get Branches
                    List<GNM_Branch> BranchList = new List<GNM_Branch>();
                    using (SqlCommand cmd = new SqlCommand("GetBranchesoperations", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add(new SqlParameter("@CompanyID", Company_ID));
                        cmd.Parameters.Add(new SqlParameter("@CompanyType", CompanyType));

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                GNM_Branch branch = new GNM_Branch
                                {
                                    Branch_ID = reader.GetInt32(reader.GetOrdinal("Branch_ID")),
                                    Branch_Name = reader.GetString(reader.GetOrdinal("Branch_Name")),
                                    // Map other fields as needed
                                };
                                BranchList.Add(branch);
                            }
                        }
                    }

                    // Generate JsonBranch string
                    string JsonBranch = "-1:--" + GetGlobalResourceObject(SelectOperationBranchDetailobj.GeneralCulture.ToString(), "select").ToString() + "--;";
                    foreach (var branch in BranchList)
                    {
                        JsonBranch += branch.Branch_ID + ":" + branch.Branch_Name + ";";
                    }
                    JsonBranch = JsonBranch.TrimEnd(';');

                    // Get Operation Branch Details
                    List<OperationBranchDetail> OperationBranchDetailList = new List<OperationBranchDetail>();
                    using (SqlCommand cmd = new SqlCommand("GetOperationBranchDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add(new SqlParameter("@OperationID", SelectOperationBranchDetailobj.OperationID));

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                OperationBranchDetail detail = new OperationBranchDetail
                                {
                                    OperationBranchDetail_ID = reader.GetInt32(reader.GetOrdinal("OperationBranchDetail_ID")),
                                    Branch_ID = reader.GetInt32(reader.GetOrdinal("Branch_ID")),
                                    // Map other fields as needed
                                };
                                OperationBranchDetailList.Add(detail);
                            }
                        }
                    }

                    // Get Branch Locale Details if LanguageID differs
                    if (SelectOperationBranchDetailobj.LanguageID != Convert.ToInt32(SelectOperationBranchDetailobj.GeneralLanguageID))
                    {
                        using (SqlCommand cmd = new SqlCommand("GetBranchLocaleDetails", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.Add(new SqlParameter("@LanguageID", SelectOperationBranchDetailobj.LanguageID));

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    int branchID = reader.GetInt32(reader.GetOrdinal("Branch_ID"));
                                    var branchLocaleDetail = OperationBranchDetailList.FirstOrDefault(b => b.Branch_ID == branchID);
                                    if (branchLocaleDetail != null)
                                    {
                                        branchLocaleDetail.Branch_Name = reader.GetString(reader.GetOrdinal("Branch_Name"));
                                    }
                                }
                            }
                        }
                    }

                    // Pagination and sorting
                    var sortedList = Sort_OBD(OperationBranchDetailList, sidx, sord);
                    Count = sortedList.Count;
                    Total = rows > 0 ? (int)Math.Ceiling((double)Count / rows) : 0;
                    var pagedList = sortedList.Skip((page - 1) * rows).Take(rows).ToList();

                    JsonResult = new
                    {
                        total = Total,
                        page = page,
                        rows = pagedList.Select(a => new
                        {
                            ID = a.OperationBranchDetail_ID,
                            edit = $"<a title='{GetGlobalResourceObject(SelectOperationBranchDetailobj.GeneralCulture.ToString(), "edit").ToString()}' href='#' id='{a.OperationBranchDetail_ID}' key='{a.OperationBranchDetail_ID}' class='EditOperationBranchDetail font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                            delete = $"<input type='checkbox' key='{a.OperationBranchDetail_ID}' defaultchecked='' id='chk{a.OperationBranchDetail_ID}' class='OperationBranchDetailDelete'/>",
                            Branch_Name = a.Branch_Name
                        }).ToList(),
                        records = Count,
                        JsonBranch
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(JsonResult);
        }

        // Sorting method (to replace OrderByField extension)
        private static List<T> Sort_OBD<T>(List<T> list, string sidx, string sord)
        {
            if (sord.ToLower() == "asc")
            {
                return list.OrderBy(x => x.GetType().GetProperty(sidx).GetValue(x)).ToList();
            }
            else
            {
                return list.OrderByDescending(x => x.GetType().GetProperty(sidx).GetValue(x)).ToList();
            }
        }
        #endregion


        #region ::: SelectOperationCheckListDetail Uday Kumar J B 02-08-2024 :::
        /// <summary>
        /// To Select Operation Branch Detail
        /// </summary>
        /// 

        public static IActionResult SelectOperationCheckListDetail(string connString, SelectOperationCheckListDetailList SelectOperationCheckListDetailobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            var JsonResult = new object();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                string AppPath = string.Empty;
                int Count = 0;
                int Total = 0;
                int Company_ID = Convert.ToInt32(SelectOperationCheckListDetailobj.Company_ID);

                List<SRM_OperationCheckListDetail> OperationCheckListDetailList = new List<SRM_OperationCheckListDetail>();
                List<SRM_OperationCheckListLocaleDetail> OperationCheckListLocaleDetailList = new List<SRM_OperationCheckListLocaleDetail>();

                // Get Operation CheckList Details using ADO.NET
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Get Operation CheckList Details
                    using (SqlCommand cmd = new SqlCommand("GetOperationCheckListDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add(new SqlParameter("@OperationID", SelectOperationCheckListDetailobj.OperationID));

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                SRM_OperationCheckListDetail detail = new SRM_OperationCheckListDetail
                                {
                                    OperationCheckListDetail_ID = reader.GetInt32(reader.GetOrdinal("OperationCheckListDetail_ID")),
                                    Operation_ID = reader.GetInt32(reader.GetOrdinal("Operation_ID")),
                                    CheckListDescription = reader.GetString(reader.GetOrdinal("CheckListDescription")),
                                    IsMandatory = reader.GetBoolean(reader.GetOrdinal("IsMandatory")),
                                    IsSpecialTools = reader.GetBoolean(reader.GetOrdinal("IsSpecialTools")),
                                    IsSafetyMeasures = reader.GetBoolean(reader.GetOrdinal("IsSafetyMeasures")),
                                    // Map other fields as needed
                                };
                                OperationCheckListDetailList.Add(detail);
                            }
                        }
                    }

                    // Get Operation CheckList Locale Details
                    using (SqlCommand cmd = new SqlCommand("GetOperationCheckListLocaleDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                SRM_OperationCheckListLocaleDetail localeDetail = new SRM_OperationCheckListLocaleDetail
                                {
                                    OperationCheckListLocaleDetail_ID = reader.GetInt32(reader.GetOrdinal("OperationCheckListLocaleDetail_ID")),
                                    OperationCheckListDetail_ID = reader.GetInt32(reader.GetOrdinal("OperationCheckListDetail_ID")),
                                    CheckListLocaleDescription = reader.GetString(reader.GetOrdinal("CheckListLocaleDescription")),
                                    // Map other fields as needed
                                };
                                OperationCheckListLocaleDetailList.Add(localeDetail);
                            }
                        }
                    }

                    // Join and map the details
                    var OperationCheckListDetails = from a in OperationCheckListDetailList
                                                    join b in OperationCheckListLocaleDetailList on a.OperationCheckListDetail_ID equals b.OperationCheckListDetail_ID into checklistLocale
                                                    from c in checklistLocale.DefaultIfEmpty(new SRM_OperationCheckListLocaleDetail { OperationCheckListLocaleDetail_ID = 0 })
                                                    select new OperationCheckListDetail
                                                    {
                                                        OperationCheckListDetail_ID = a.OperationCheckListDetail_ID,
                                                        CheckList_Description = a.CheckListDescription,
                                                        OperationCheckListLocaleDetail_ID = c.OperationCheckListLocaleDetail_ID,
                                                        CheckListLocale_Description = c.CheckListLocaleDescription,
                                                        IsMandatory = Convert.ToBoolean(a.IsMandatory),
                                                        IsSpecialTools = Convert.ToBoolean(a.IsSpecialTools),
                                                        IsSafetyMeasures = Convert.ToBoolean(a.IsSafetyMeasures)
                                                    };

                    // Convert to queryable for sorting and pagination
                    var IQOperationCheckListDetail = OperationCheckListDetails.AsQueryable();

                    // Sorting
                    if (sidx == "CheckList")
                    {
                        sidx = "CheckList_Description";
                    }

                    IQOperationCheckListDetail = Sort_OCD(IQOperationCheckListDetail, sidx, sord);

                    // Pagination
                    Count = IQOperationCheckListDetail.Count();
                    Total = rows > 0 ? (int)Math.Ceiling((double)Count / rows) : 0;
                    var pagedList = IQOperationCheckListDetail.Skip((page - 1) * rows).Take(rows).ToList();

                    JsonResult = new
                    {
                        total = Total,
                        page = page,
                        rows = pagedList.Select(a => new
                        {
                            a.OperationCheckListDetail_ID,
                            edit = $"<a title='{GetGlobalResourceObject(SelectOperationCheckListDetailobj.UserCulture.ToString(), "edit")}' href='#' id='{a.OperationCheckListDetail_ID}' key='{a.OperationCheckListDetail_ID}' class='EditOperationCheckListDetail font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                            delete = $"<input type='checkbox' key='{a.OperationCheckListDetail_ID}' defaultchecked='' id='chk{a.OperationCheckListDetail_ID}' class='OperationCheckListDetailDelete'/>",
                            CheckList = a.CheckList_Description,
                            CheckListLocale_Description = a.CheckListLocale_Description,
                            Locale = $"<a key='{a.OperationCheckListDetail_ID}' key1='{a.OperationCheckListLocaleDetail_ID}' key3='{a.CheckList_Description}' key4='{a.CheckListLocale_Description}' key5='{a.IsMandatory}' key6='{a.IsSafetyMeasures}' key7='{a.IsSpecialTools}' src='{AppPath}/Content/local.png' class='OperatioLocaleChecklist' alt='Localize' width='20' height='20' title='Localize'><i class='fa fa-globe'></i></a>",
                            IsMandatory = a.IsMandatory ? "Yes" : "No",
                            IsSpecialTools = a.IsSpecialTools ? "Yes" : "No",
                            IsSafetyMeasures = a.IsSafetyMeasures ? "Yes" : "No"
                        }).ToList(),
                        records = Count
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(JsonResult);
        }

        // Sorting method (to replace OrderByField extension)
        private static IQueryable<T> Sort_OCD<T>(IQueryable<T> list, string sidx, string sord)
        {
            if (sord.ToLower() == "asc")
            {
                return list.OrderBy(x => x.GetType().GetProperty(sidx).GetValue(x));
            }
            else
            {
                return list.OrderByDescending(x => x.GetType().GetProperty(sidx).GetValue(x));
            }
        }
        #endregion


        #region ::: SelectReferenceMaster Uday Kumar J B 02-08-2024:::
        /// <summary>
        /// To get Refrence Master records for a Master
        /// </summary> 
        /// 

        public static IActionResult SelectReferenceMaster(string connString, SelectReferenceMasteroperationList SelectReferenceMasteroperationobj)
        {
            var Masterdata = new object();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int GeneralLanguageID = Convert.ToInt32(SelectReferenceMasteroperationobj.GeneralLanguageID);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    if (SelectReferenceMasteroperationobj.LanguageID == GeneralLanguageID)
                    {
                        using (SqlCommand cmd = new SqlCommand("GetRefMasterDetailsoperation", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.Add(new SqlParameter("@ReferenceMasterName", SelectReferenceMasteroperationobj.ReferenceMasterName));
                            cmd.Parameters.Add(new SqlParameter("@GeneralLanguageID", GeneralLanguageID));

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                var referenceMasterData = new List<object>();
                                while (reader.Read())
                                {
                                    referenceMasterData.Add(new
                                    {
                                        ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                        Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"))
                                    });
                                }
                                Masterdata = new
                                {
                                    ReferenceMasterData = referenceMasterData
                                };
                            }
                        }
                    }
                    else
                    {
                        using (SqlCommand cmd = new SqlCommand("GetRefMasterDetailLocales", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.Add(new SqlParameter("@ReferenceMasterName", SelectReferenceMasteroperationobj.ReferenceMasterName));
                            cmd.Parameters.Add(new SqlParameter("@LanguageID", SelectReferenceMasteroperationobj.LanguageID));

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                var referenceMasterData = new List<object>();
                                while (reader.Read())
                                {
                                    referenceMasterData.Add(new
                                    {
                                        ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                        Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"))
                                    });
                                }
                                Masterdata = new
                                {
                                    ReferenceMasterData = referenceMasterData
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Masterdata);
        }
        #endregion


        #region ::: SelectProductType Uday Kumar J B 02-08-2024 :::
        /// <summary>
        /// To Select Product type
        /// </summary> 
        /// 

        public static IActionResult SelectProductType(string connString, SelectProductTypeLista SelectProductTypeobj)
        {
            var Masterdata = new object();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int GeneralLanguageID = Convert.ToInt32(SelectProductTypeobj.GeneralLanguageID);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    if (SelectProductTypeobj.LanguageID == GeneralLanguageID)
                    {
                        using (SqlCommand cmd = new SqlCommand("GetProductTypesoperation", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.Add(new SqlParameter("@BrandID", SelectProductTypeobj.BrandID));

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                var productTypeData = new List<object>();
                                while (reader.Read())
                                {
                                    productTypeData.Add(new
                                    {
                                        ID = reader.GetInt32(reader.GetOrdinal("ProductType_ID")),
                                        Name = reader.GetString(reader.GetOrdinal("ProductType_Name"))
                                    });
                                }
                                Masterdata = new
                                {
                                    ProductTypeData = productTypeData
                                };
                            }
                        }
                    }
                    else
                    {
                        using (SqlCommand cmd = new SqlCommand("GetProductTypeLocales", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.Add(new SqlParameter("@BrandID", SelectProductTypeobj.BrandID));
                            cmd.Parameters.Add(new SqlParameter("@LanguageID", SelectProductTypeobj.LanguageID));

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                var productTypeData = new List<object>();
                                while (reader.Read())
                                {
                                    productTypeData.Add(new
                                    {
                                        ID = reader.GetInt32(reader.GetOrdinal("ProductType_ID")),
                                        Name = reader.GetString(reader.GetOrdinal("ProductType_Name"))
                                    });
                                }
                                Masterdata = new
                                {
                                    ProductTypeData = productTypeData
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Masterdata);
        }
        #endregion


        #region ::: SelectModel Uday Kumar J B 02-08-2024:::
        /// <summary>
        /// To Select Model
        /// </summary> 
        /// 

        public static IActionResult SelectModel(string connString, SelectModelList SelectModelobj)
        {
            var Masterdata = new object();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int GeneralLanguageID = Convert.ToInt32(SelectModelobj.GeneralLanguageID);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    if (SelectModelobj.LanguageID == GeneralLanguageID)
                    {
                        using (SqlCommand cmd = new SqlCommand("GetModelsoperation", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.Add(new SqlParameter("@ProductTypeID", SelectModelobj.ProductTypeID));
                            cmd.Parameters.Add(new SqlParameter("@BrandID", SelectModelobj.BrandID));

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                var modelData = new List<object>();
                                while (reader.Read())
                                {
                                    modelData.Add(new
                                    {
                                        ID = reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                        Name = reader.GetString(reader.GetOrdinal("Model_Name"))
                                    });
                                }
                                Masterdata = new
                                {
                                    ModelData = modelData
                                };
                            }
                        }
                    }
                    else
                    {
                        using (SqlCommand cmd = new SqlCommand("GetModelLocales", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.Add(new SqlParameter("@ProductTypeID", SelectModelobj.ProductTypeID));
                            cmd.Parameters.Add(new SqlParameter("@BrandID", SelectModelobj.BrandID));
                            cmd.Parameters.Add(new SqlParameter("@LanguageID", SelectModelobj.LanguageID));

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                var modelData = new List<object>();
                                while (reader.Read())
                                {
                                    modelData.Add(new
                                    {
                                        ID = reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                        Name = reader.GetString(reader.GetOrdinal("Model_Name"))
                                    });
                                }
                                Masterdata = new
                                {
                                    ModelData = modelData
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Masterdata);
        }
        #endregion


        #region ::: SelectFunctionGroup Uday Kumar J B 02-08-2024 :::
        /// <summary>
        /// To get Function Group master
        /// </summary> 
        /// 
        public static IActionResult SelectFunctionGroup(string connString, SelectFunctionGroupList SelectFunctionGroupobj)
        {
            var Masterdata = new object();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int GeneralLanguageID = Convert.ToInt32(SelectFunctionGroupobj.GeneralLanguageID);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    if (SelectFunctionGroupobj.LanguageID == GeneralLanguageID)
                    {
                        using (SqlCommand cmd = new SqlCommand("GetFunctionGroupsoperation", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.Add(new SqlParameter("@CompanyID", SelectFunctionGroupobj.CompanyID));

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                var functionGroupData = new List<object>();
                                while (reader.Read())
                                {
                                    functionGroupData.Add(new
                                    {
                                        ID = reader.GetInt32(reader.GetOrdinal("FunctionGroup_ID")),
                                        Name = reader.GetString(reader.GetOrdinal("FunctionGroup_Name"))
                                    });
                                }
                                Masterdata = new
                                {
                                    FunctionGroupData = functionGroupData
                                };
                            }
                        }
                    }
                    else
                    {
                        using (SqlCommand cmd = new SqlCommand("GetFunctionGroupLocales", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.Add(new SqlParameter("@CompanyID", SelectFunctionGroupobj.CompanyID));
                            cmd.Parameters.Add(new SqlParameter("@LanguageID", SelectFunctionGroupobj.LanguageID));

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                var functionGroupData = new List<object>();
                                while (reader.Read())
                                {
                                    functionGroupData.Add(new
                                    {
                                        ID = reader.GetInt32(reader.GetOrdinal("FunctionGroup_ID")),
                                        Name = reader.GetString(reader.GetOrdinal("FunctionGroup_Name"))
                                    });
                                }
                                Masterdata = new
                                {
                                    FunctionGroupData = functionGroupData
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Masterdata);
        }
        #endregion


        #region ::: SaveOperationProductDetail Uday Kumar J B 02-08-2024 :::
        /// <summary>
        /// To Insert and Update Product type
        /// </summary>
        /// 

        public static IActionResult SaveOperationProductDetail(string connString, SaveOperationProductDetailList SaveOperationProductDetailobj)
        {
            var JsonData = new object();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                var jObj = JObject.Parse(SaveOperationProductDetailobj.data);
                int count = jObj["rows"].Count();
                var rowIndex = new List<int>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Check for existing records
                    for (int i = 0; i < count; i++)
                    {
                        var operationProductDetail = jObj["rows"].ElementAt(i).ToObject<SRM_OperationProductDetail>();

                        using (SqlCommand cmd = new SqlCommand("CheckOperationProductDetailExists", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.Add(new SqlParameter("@Operation_ID", operationProductDetail.Operation_ID));
                            cmd.Parameters.Add(new SqlParameter("@Brand_ID", operationProductDetail.Brand_ID));
                            cmd.Parameters.Add(new SqlParameter("@ProductType_ID", (object)operationProductDetail.ProductType_ID ?? DBNull.Value));
                            cmd.Parameters.Add(new SqlParameter("@Model_ID", (object)operationProductDetail.Model_ID ?? DBNull.Value));
                            cmd.Parameters.Add(new SqlParameter("@OperationProductDetail_ID", operationProductDetail.OperationProductDetail_ID));

                            var recordCount = (int)cmd.ExecuteScalar();
                            if (recordCount > 0)
                            {
                                rowIndex.Add(i);
                            }
                        }
                    }

                    // Insert or Update records
                    if (rowIndex.Count == 0)
                    {
                        for (int i = 0; i < count; i++)
                        {
                            var operationProductDetail = jObj["rows"].ElementAt(i).ToObject<SRM_OperationProductDetail>();

                            using (SqlCommand cmd = new SqlCommand("SaveOperationProductDetail", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.Add(new SqlParameter("@OperationProductDetail_ID", (object)operationProductDetail.OperationProductDetail_ID ?? DBNull.Value));
                                cmd.Parameters.Add(new SqlParameter("@Operation_ID", operationProductDetail.Operation_ID));
                                cmd.Parameters.Add(new SqlParameter("@Brand_ID", operationProductDetail.Brand_ID));
                                cmd.Parameters.Add(new SqlParameter("@ProductType_ID", (object)operationProductDetail.ProductType_ID ?? DBNull.Value));
                                cmd.Parameters.Add(new SqlParameter("@Model_ID", (object)operationProductDetail.Model_ID ?? DBNull.Value));

                                cmd.ExecuteNonQuery();
                            }
                        }
                        JsonData = new
                        {
                            IsExists = false,
                            rowsIndex = ""
                        };
                    }
                    else
                    {
                        JsonData = new
                        {
                            IsExists = true,
                            rowsIndex = rowIndex.ToArray()
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(JsonData);
        }
        #endregion


        #region ::: SaveOperationBranchDetail Uday Kumar J B 02-08-2024:::
        /// <summary>
        /// To Insert and Update Operation Branch Detail
        /// </summary>
        /// 

        public static IActionResult SaveOperationBranchDetail(string connString, SaveOperationBranchDetailList SaveOperationBranchDetailobj)
        {
            string Msg = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                var jObj = JObject.Parse(SaveOperationBranchDetailobj.data);
                int operationProductCount = jObj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int i = 0; i < operationProductCount; i++)
                    {
                        var operationBranchDetail = jObj["rows"].ElementAt(i).ToObject<JObject>();

                        int operationBranchDetailId = Convert.ToInt32(operationBranchDetail["OperationBranchDetail_ID"]?.ToString() ?? "0");
                        int operationId = Convert.ToInt32(operationBranchDetail["Operation_ID"]?.ToString());
                        int branchId = Convert.ToInt32(operationBranchDetail["Branch_ID"]?.ToString());

                        if (operationBranchDetailId > 0)
                        {
                            // Update existing record
                            using (SqlCommand cmd = new SqlCommand("UpdateOperationBranchDetail", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.Add(new SqlParameter("@OperationBranchDetail_ID", operationBranchDetailId));
                                cmd.Parameters.Add(new SqlParameter("@Branch_ID", branchId));
                                cmd.ExecuteNonQuery();
                            }
                        }
                        else
                        {
                            // Insert new record
                            using (SqlCommand cmd = new SqlCommand("InsertOperationBranchDetail", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.Add(new SqlParameter("@Operation_ID", operationId));
                                cmd.Parameters.Add(new SqlParameter("@Branch_ID", branchId));
                                cmd.ExecuteNonQuery();
                            }
                        }
                    }

                    Msg = "Saved";
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                Msg = string.Empty;
            }
            return new JsonResult(Msg);
        }
        #endregion


        #region ::: SaveOperationCheckListDetail Uday Kumar J B 06-08-2024 :::
        /// <summary>
        /// To Insert and Update Operation CheckList Detail
        /// </summary>
        /// 

        public static IActionResult SaveOperationCheckListDetail(string connString, SaveOperationCheckListDetailList SaveOperationCheckListDetailobj)
        {
            string Msg = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                var jObj = JObject.Parse(SaveOperationCheckListDetailobj.data);
                int operationCheckListCount = jObj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int i = 0; i < operationCheckListCount; i++)
                    {
                        var operationCheckListDetail = jObj["rows"].ElementAt(i).ToObject<JObject>();

                        int operationCheckListDetailId = Convert.ToInt32(operationCheckListDetail["OperationCheckListDetail_ID"]?.ToString() ?? "0");
                        int operationId = Convert.ToInt32(operationCheckListDetail["Operation_ID"]?.ToString());
                        string checkListDescription = Common.DecryptString(operationCheckListDetail["CheckList"]?.ToString());
                        bool isMandatory = Convert.ToBoolean(operationCheckListDetail["IsMandatory"]?.ToString());
                        bool isSpecialTools = Convert.ToBoolean(operationCheckListDetail["IsSpecialTools"]?.ToString());
                        bool isSafetyMeasures = Convert.ToBoolean(operationCheckListDetail["IsSafetyMeasures"]?.ToString());

                        if (operationCheckListDetailId > 0)
                        {
                            // Update existing record
                            using (SqlCommand cmd = new SqlCommand("UpdateOperationCheckListDetail", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.Add(new SqlParameter("@OperationCheckListDetail_ID", operationCheckListDetailId));
                                cmd.Parameters.Add(new SqlParameter("@CheckListDescription", checkListDescription));
                                cmd.Parameters.Add(new SqlParameter("@IsMandatory", isMandatory));
                                cmd.Parameters.Add(new SqlParameter("@IsSpecialTools", isSpecialTools));
                                cmd.Parameters.Add(new SqlParameter("@IsSafetyMeasures", isSafetyMeasures));
                                cmd.ExecuteNonQuery();
                            }
                        }
                        else
                        {
                            // Insert new record
                            using (SqlCommand cmd = new SqlCommand("InsertOperationCheckListDetail", conn))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.Add(new SqlParameter("@Operation_ID", operationId));
                                cmd.Parameters.Add(new SqlParameter("@CheckListDescription", checkListDescription));
                                cmd.Parameters.Add(new SqlParameter("@IsMandatory", isMandatory));
                                cmd.Parameters.Add(new SqlParameter("@IsSpecialTools", isSpecialTools));
                                cmd.Parameters.Add(new SqlParameter("@IsSafetyMeasures", isSafetyMeasures));
                                cmd.ExecuteNonQuery();
                            }
                        }
                    }

                    Msg = "Saved";
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                Msg = string.Empty;
            }
            return new JsonResult(Msg);
        }
        #endregion


        #region ::: DeleteOperationProductDetail Uday Kumar J B 06-08-2024:::
        /// <summary>
        ///To Delete OperationProductDetail
        /// </summary>  
        /// 

        public static IActionResult DeleteOperationProductDetail(string connString, DeleteOperationProductDetailList DeleteOperationProductDetailobj)
        {
            string msg = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                var jObj = JObject.Parse(DeleteOperationProductDetailobj.key);
                int count = jObj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int i = 0; i < count; i++)
                    {
                        var id = Convert.ToInt32(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"].ToString());

                        using (SqlCommand cmd = new SqlCommand("DeleteOperationProductDetail", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.Add(new SqlParameter("@OperationProductDetail_ID", id));

                            try
                            {
                                cmd.ExecuteNonQuery();
                            }
                            catch (SqlException ex)
                            {
                                // Handle specific SQL exceptions if needed
                                if (ex.Number == 547) // Foreign key constraint violation
                                {
                                    msg = GetGlobalResourceObject(DeleteOperationProductDetailobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                                    return new JsonResult(msg);
                                }
                                else
                                {
                                    throw; // Re-throw other exceptions
                                }
                            }
                        }
                    }

                    msg = GetGlobalResourceObject(DeleteOperationProductDetailobj.GeneralCulture.ToString(), "deletedsuccessfully").ToString();
                }
            }
            catch (Exception ex)
            {
                // Log exception
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                msg = string.Empty;
            }
            return new JsonResult(msg);
        }
        #endregion


        #region ::: DeleteOperationBranchDetail Uday Kumar J B 06-08-2024:::
        /// <summary>
        ///To Delete OperationBranchDetail
        /// </summary>  
        /// 
        public static IActionResult DeleteOperationBranchDetail(string connString, DeleteOperationBranchDetailList DeleteOperationBranchDetailobj)
        {
            string msg = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                var jObj = JObject.Parse(DeleteOperationBranchDetailobj.key);
                int count = jObj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int i = 0; i < count; i++)
                    {
                        int id = Convert.ToInt32(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"].ToString());

                        using (SqlCommand cmd = new SqlCommand("DeleteOperationBranchDetail", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.Add(new SqlParameter("@OperationBranchDetail_ID", id));

                            try
                            {
                                cmd.ExecuteNonQuery();
                            }
                            catch (SqlException ex)
                            {
                                // Handle specific SQL exceptions if needed
                                if (ex.Number == 547) // Foreign key constraint violation
                                {
                                    msg = GetGlobalResourceObject(DeleteOperationBranchDetailobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                                    return new JsonResult(msg);
                                }
                                else
                                {
                                    throw; // Re-throw other exceptions
                                }
                            }
                        }
                    }

                    msg = GetGlobalResourceObject(DeleteOperationBranchDetailobj.GeneralCulture.ToString(), "deletedsuccessfully").ToString();
                }
            }
            catch (Exception ex)
            {
                // Log exception
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                msg = string.Empty;
            }
            return new JsonResult(msg);
        }
        #endregion


        #region ::: DeleteOperationCheckListDetail Uday Kumar J B 06-08-2024:::
        /// <summary>
        ///To Delete Operation CheckList Detail
        /// </summary> 
        /// 

        public static IActionResult DeleteOperationCheckListDetail(string connString, DeleteOperationCheckListDetailList DeleteOperationCheckListDetailobj)
        {
            string msg = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                var jObj = JObject.Parse(DeleteOperationCheckListDetailobj.key);
                int count = jObj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int i = 0; i < count; i++)
                    {
                        int id = Convert.ToInt32(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"].ToString());

                        using (SqlCommand cmd = new SqlCommand("DeleteOperationCheckListDetail", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.Add(new SqlParameter("@OperationCheckListDetail_ID", id));

                            try
                            {
                                cmd.ExecuteNonQuery();
                            }
                            catch (SqlException ex)
                            {
                                // Handle specific SQL exceptions if needed
                                if (ex.Number == 50000) // Custom error raised by the stored procedure
                                {
                                    msg = GetGlobalResourceObject(DeleteOperationCheckListDetailobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                                    return new JsonResult(msg);
                                }
                                else
                                {
                                    throw; // Re-throw other exceptions
                                }
                            }
                        }
                    }

                    msg = GetGlobalResourceObject(DeleteOperationCheckListDetailobj.GeneralCulture.ToString(), "deletedsuccessfully").ToString();
                }
            }
            catch (Exception ex)
            {
                // Log exception
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                msg = string.Empty;
            }
            return new JsonResult(msg);
        }
        #endregion


        #region ::: SaveOperationLocale Uday Kumar J B 06-08-2024:::
        /// <summary>
        /// To Save Operation Locale
        /// </summary>
        /// 

        public static IActionResult SaveOperationLocale(string connString, SaveOperationLocaleList SaveOperationLocaleobj)
        {
            var result = new { OperationLocale_ID = 0 };
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                var jObj = JObject.Parse(SaveOperationLocaleobj.data);

                int operationId = Convert.ToInt32(jObj["Operation_ID"]);
                int operationLocaleId = Convert.ToInt32(jObj["OperationLocale_ID"]);
                string operationDescription = Common.DecryptString(jObj["Operation_Description"].ToString());
                int languageId = Convert.ToInt32(jObj["Language_ID"]);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("SaveOperationLocale", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.Add(new SqlParameter("@Operation_ID", operationId));
                        cmd.Parameters.Add(new SqlParameter("@OperationLocale_ID", operationLocaleId));
                        cmd.Parameters.Add(new SqlParameter("@Operation_Description", operationDescription));
                        cmd.Parameters.Add(new SqlParameter("@Language_ID", languageId));

                        SqlParameter outputIdParam = new SqlParameter("@OperationLocale_ID", SqlDbType.Int)
                        {
                            Direction = ParameterDirection.Output
                        };
                        cmd.Parameters.Add(outputIdParam);

                        SqlParameter isUpdateParam = new SqlParameter("@IsUpdate", SqlDbType.Bit)
                        {
                            Direction = ParameterDirection.Output
                        };
                        cmd.Parameters.Add(isUpdateParam);

                        cmd.ExecuteNonQuery();

                        int newOperationLocaleId = (int)outputIdParam.Value;
                        bool isUpdate = (bool)isUpdateParam.Value;

                        if (isUpdate)
                        {
                            //gbl.InsertGPSDetails(
                            //    Convert.ToInt32(SaveOperationLocaleobj.Company_ID),
                            //    Convert.ToInt32(SaveOperationLocaleobj.Branch),
                            //    Convert.ToInt32(SaveOperationLocaleobj.User_ID),
                            //    Convert.ToInt32(Common.GetObjectID("SRM_Operations")),
                            //    operationId, 0, 0, "Update", false,
                            //    Convert.ToInt32(SaveOperationLocaleobj.MenuID));
                        }
                        else
                        {
                            //gbl.InsertGPSDetails(
                            //    Convert.ToInt32(SaveOperationLocaleobj.Company_ID),
                            //    Convert.ToInt32(SaveOperationLocaleobj.Branch),
                            //    Convert.ToInt32(SaveOperationLocaleobj.User_ID),
                            //    Convert.ToInt32(Common.GetObjectID("SRM_Operations")),
                            //    operationId, 0, 0, "Insert", false,
                            //    Convert.ToInt32(SaveOperationLocaleobj.MenuID));
                        }

                        result = new
                        {
                            OperationLocale_ID = newOperationLocaleId
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                // Log exception
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(result);
        }
        #endregion


        #region ::: SaveOperationChecklistLocale Uday Kumar J B 06-08-2024 :::
        /// <summary>
        /// To Save Operation Locale
        /// </summary>
        /// 
        public static IActionResult SaveOperationChecklistLocale(string connString, SaveOperationChecklistLocaleList SaveOperationChecklistLocaleobj)
        {
            var result = new { OperationLocale_ID = 0 };
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                var jObj = JObject.Parse(SaveOperationChecklistLocaleobj.data);

                int operationCheckListLocaleDetailId = Convert.ToInt32(jObj["OperationCheckListLocaleDetail_ID"]);
                int operationCheckListDetailId = Convert.ToInt32(jObj["OperationCheckListDetail_ID"]);
                string checkListLocaleDescription = Common.DecryptString(jObj["CheckListLocaleDescription"].ToString());
                int languageId = Convert.ToInt32(jObj["Language_ID"]);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("SaveOperationChecklistLocale", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.Add(new SqlParameter("@OperationCheckListLocaleDetail_ID", operationCheckListLocaleDetailId));
                        cmd.Parameters.Add(new SqlParameter("@OperationCheckListDetail_ID", operationCheckListDetailId));
                        cmd.Parameters.Add(new SqlParameter("@CheckListLocaleDescription", checkListLocaleDescription));
                        cmd.Parameters.Add(new SqlParameter("@Language_ID", languageId));

                        SqlParameter outputIdParam = new SqlParameter("@Operation_ID", SqlDbType.Int)
                        {
                            Direction = ParameterDirection.Output
                        };
                        cmd.Parameters.Add(outputIdParam);

                        cmd.ExecuteNonQuery();

                        int newOperationLocaleId = (int)outputIdParam.Value;

                        result = new
                        {
                            OperationLocale_ID = newOperationLocaleId
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                // Log exception
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(result);
        }
        #endregion


        #region ::: CheckOperationCode Uday Kumar J B 06-08-2024:::
        /// <summary>
        /// To Check if OperationCode already exists
        /// </summary>
        /// 
        public static IActionResult CheckOperationCode(string connString, CheckOperationCodeList CheckOperationCodeobj)
        {
            int Count = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int Company_ID = Convert.ToInt32(CheckOperationCodeobj.Company_ID);
                int OperationCode = Convert.ToInt32(CheckOperationCodeobj.OperationCode);

                // Step 1: Get Parent Company Details
                IEnumerable<int> ParentCompanyIDs = GetParentCompanyDetails(Company_ID, connString);

                // Step 2: Check Operation Code
                if (ParentCompanyIDs != null && ParentCompanyIDs.Any())
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        using (SqlCommand cmd = new SqlCommand("CheckOperationCode", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@OperationCode", OperationCode);
                            cmd.Parameters.AddWithValue("@OperationID", CheckOperationCodeobj.OperationID);
                            cmd.Parameters.AddWithValue("@Company_ID", Company_ID);

                            conn.Open();
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.HasRows)
                                {
                                    Count = 1;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Count);
        }

        private static IEnumerable<int> GetParentCompanyDetails(int companyId, string connString)
        {
            List<int> parentCompanyIds = new List<int>();
            using (SqlConnection conn = new SqlConnection(connString))
            {
                using (SqlCommand cmd = new SqlCommand("GetParentCompanyDetails", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@Company_ID", companyId);

                    conn.Open();
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            parentCompanyIds.Add(reader.GetInt32(0)); // Assuming Company_ID is the first column
                        }
                    }
                }
            }
            return parentCompanyIds;
        }
        #endregion


        #region ::: CheckChecklist Uday Kumar J B 06-08-2024:::
        /// <summary>
        /// To check if the CheckList Description is already used
        /// </summary>
        /// 

        public static IActionResult CheckCheckListDescription(string connString, CheckCheckListDescriptionList CheckCheckListDescriptionobj)
        {
            int status = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                string checklistDescription = Common.DecryptString(CheckCheckListDescriptionobj.checklistDescription);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("CheckCheckListDescription", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CheckListDescription", checklistDescription);
                        cmd.Parameters.AddWithValue("@OperationID", CheckCheckListDescriptionobj.OperationID);
                        cmd.Parameters.AddWithValue("@PrimaryKey", CheckCheckListDescriptionobj.primaryKey);

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.HasRows)
                            {
                                status = 1;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(status);
        }
        #endregion


        #region ::: CheckChecklistLocale Uday Kumar J B 06-08-2024:::
        /// <summary>
        /// To check if the CheckList Description is already used
        /// </summary>
        /// 
        public static IActionResult CheckCheckListDescriptionLocale(string connString, CheckCheckListDescriptionLocaleList CheckCheckListDescriptionLocaleobj)
        {
            int status = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jObj = JObject.Parse(CheckCheckListDescriptionLocaleobj.data);

                int OperationLocale_ID = Convert.ToInt32(jObj["OperationCheckListLocaleDetail_ID"].ToString());
                int OperationChecklistID = Convert.ToInt32(jObj["Operation_ID"].ToString());
                string checklistDescription = Common.DecryptString(jObj["CheckListLocaleDescription"].ToString());

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("CheckCheckListDescriptionLocale", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CheckListLocaleDescription", checklistDescription);
                        cmd.Parameters.AddWithValue("@OperationID", OperationChecklistID);
                        cmd.Parameters.AddWithValue("@OperationCheckListLocaleDetail_ID", OperationLocale_ID);

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.HasRows)
                            {
                                status = 1;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(status);
        }
        #endregion


        #region ::: CheckBranch Uday Kumar J B 06-08-2024:::
        /// <summary>
        /// To Check Branch
        /// </summary>
        /// 

        public static IActionResult CheckBranch(string connString, CheckBranchList CheckBranchobj)
        {
            int Count = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("CheckBranch", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@BranchID", CheckBranchobj.BranchID);
                        cmd.Parameters.AddWithValue("@OperationID", CheckBranchobj.OperationID);
                        cmd.Parameters.AddWithValue("@OperationBranchDetailID", CheckBranchobj.OperationBranchDetailID);

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.HasRows)
                            {
                                Count = 1;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Count);
        }
        #endregion


        #region ::: CheckProductDetail Uday Kumar J B 06-08-2024:::
        /// <summary>
        /// To Check Product Detail
        /// </summary>
        /// 

        public static IActionResult CheckProductDetail(string connString, CheckProductDetailList CheckProductDetailobj)
        {
            int Count = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("CheckProductDetail", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@BrandID", CheckProductDetailobj.BrandID);
                        cmd.Parameters.AddWithValue("@ProductTypeID", (object)CheckProductDetailobj.ProductTypeID ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ModelID", (object)CheckProductDetailobj.ModelID ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@OperationID", CheckProductDetailobj.OperationID);
                        cmd.Parameters.AddWithValue("@OperationProductDetailID", CheckProductDetailobj.OperationProductDetailID);

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.HasRows)
                            {
                                Count = 1;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Count);
        }
        #endregion


        #region ::: SelectSRTHistory Uday Kumar J B 06-08-2024 :::
        /// <summary>
        /// To Select SRT History
        /// </summary>
        /// 

        public static IActionResult SelectSRTHistory(string connString, SelectSRTHistoryList SelectSRTHistoryobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            dynamic jsonResult = null;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int userLanguageID = Convert.ToInt32(SelectSRTHistoryobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectSRTHistoryobj.GeneralLanguageID);

                List<SRTHistoryDetails> OperationSRTdataList = new List<SRTHistoryDetails>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("SelectSRTHistory", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@OperationId", SelectSRTHistoryobj.OperationId);

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var historyDetails = new SRTHistoryDetails
                                {
                                    OperationStandardTimeUpdateLog_ID = reader.GetInt32(reader.GetOrdinal("OperationStandardTimeUpdateLog_ID")),
                                    ExistingStandardTime = reader.GetDecimal(reader.GetOrdinal("ExistingStandardTime")),
                                    UpdatedStandardTime = reader.GetDecimal(reader.GetOrdinal("UpdatedStandardTime")),
                                    UpdatedBy = (userLanguageID == generalLanguageID) ? reader["UpdatedByUserName"].ToString() : reader["UpdatedByUserLocaleName"].ToString(),
                                    UpdatedDate = reader.GetDateTime(reader.GetOrdinal("UpdatedDate")).ToString("dd-MMM-yyyy hh:mm tt"),
                                    ReferenceNumber = reader["ReferenceNumber"].ToString()
                                };
                                OperationSRTdataList.Add(historyDetails);
                            }
                        }
                    }
                }

                var IQSRTData = OperationSRTdataList.AsQueryable();

                if (_search)
                {
                    Filters searchFilters = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    if (searchFilters.rules.Count > 0)
                        IQSRTData = IQSRTData.FilterSearch<SRTHistoryDetails>(searchFilters);
                }
                else if (advnce)
                {
                    AdvanceFilter advnceFiltersObj = JObject.Parse(advnceFilters).ToObject<AdvanceFilter>();
                    IQSRTData = IQSRTData.AdvanceSearch<SRTHistoryDetails>(advnceFiltersObj);
                }

                IQSRTData = IQSRTData.OrderByField(sidx, sord);
                int count = IQSRTData.Count();
                int total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                jsonResult = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = IQSRTData
                            .Skip((page - 1) * rows)
                            .Take(rows)
                            .Select(a => new
                            {
                                a.OperationStandardTimeUpdateLog_ID,
                                ExistingStandardTime = a.ExistingStandardTime.ToString("0.00"),
                                a.ReferenceNumber,
                                a.UpdatedBy,
                                a.UpdatedDate,
                                UpdatedStandardTime = a.UpdatedStandardTime.ToString("0.00")
                            })
                            .ToList()
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonResult);
        }

        #endregion


        #region ::: SRM_Operations List and obj classes Uday Kumar J B 06-08-2024 :::
        /// <summary>
        /// SRM_Operations List and obj classes
        /// </summary>
        /// 
        public class SelectSRTHistoryList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int OperationId { get; set; }
        }

        public class CheckProductDetailList
        {
            public int BrandID { get; set; }
            public int? ProductTypeID { get; set; }
            public int? ModelID { get; set; }
            public int OperationID { get; set; }
            public int OperationProductDetailID { get; set; }
        }
        public class CheckBranchList
        {
            public int BranchID { get; set; }
            public int OperationID { get; set; }
            public int OperationBranchDetailID { get; set; }
        }
        public class CheckCheckListDescriptionLocaleList
        {
            public string data { get; set; }
        }
        public class CheckCheckListDescriptionList
        {
            public string checklistDescription { get; set; }
            public int OperationID { get; set; }
            public int primaryKey { get; set; }
        }

        public class CheckOperationCodeList
        {
            public int Company_ID { get; set; }
            public int OperationCode { get; set; }
            public int OperationID { get; set; }
        }
        public class SaveOperationChecklistLocaleList
        {
            public string data { get; set; }
        }
        public class SaveOperationLocaleList
        {
            public string data { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }
        public class DeleteOperationCheckListDetailList
        {
            public string key { get; set; }
            public string GeneralCulture { get; set; }
        }
        public class DeleteOperationBranchDetailList
        {
            public string key { get; set; }
            public string GeneralCulture { get; set; }
        }
        public class DeleteOperationProductDetailList
        {
            public string key { get; set; }
            public string GeneralCulture { get; set; }
        }

        public class SaveOperationCheckListDetailList
        {
            public string data { get; set; }
        }
        public class SaveOperationBranchDetailList
        {
            public string data { get; set; }
        }
        public class SaveOperationProductDetailList
        {
            public string data { get; set; }
        }
        public class SelectFunctionGroupList
        {
            public int GeneralLanguageID { get; set; }
            public int LanguageID { get; set; }
            public int CompanyID { get; set; }
        }
        public class SelectModelList
        {
            public int GeneralLanguageID { get; set; }
            public int LanguageID { get; set; }
            public int ProductTypeID { get; set; }
            public int BrandID { get; set; }
        }
        public class SelectProductTypeLista
        {
            public int GeneralLanguageID { get; set; }
            public int LanguageID { get; set; }
            public int BrandID { get; set; }

        }

        public class SelectReferenceMasteroperationList
        {
            public int GeneralLanguageID { get; set; }
            public int LanguageID { get; set; }
            public string ReferenceMasterName { get; set; }

        }
        public class SelectOperationCheckListDetailList
        {
            public int Company_ID { get; set; }
            public int OperationID { get; set; }
            public string UserCulture { get; set; }
        }

        public class SelectOperationBranchDetailList
        {
            public int Company_ID { get; set; }
            public string GeneralCulture { get; set; }
            public int OperationID { get; set; }
            public int LanguageID { get; set; }
            public string UserCulture { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        public class SelectOperationProductDetailList
        {
            public int Company_ID { get; set; }
            public int OperationID { get; set; }
            public int LanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string GeneralCulture { get; set; }
        }

        public class ExportOperationsList
        {
            public int exprtType { get; set; }
        }

        public class DeleteOperationList
        {
            public string key { get; set; }
            public string GeneralCulture { get; set; }
        }
        public class UpdateOperationsList
        {
            public string data { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int Branch { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }
        public class InsertOperationsList
        {
            public string data { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int Branch { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }

        public class SelectOperationList
        {
            public string GeneralCulture { get; set; }
            public int Company_ID { get; set; }
            public string UserCulture { get; set; }
            public int Language_ID { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        #endregion


        #region ::: SRM_Operations Classes Uday Kumar J B 02-8-2024:::
        /// <summary>
        /// SRM_Operations Classes
        /// </summary>

        #region ::: OperationCheckListDetail Uday Kumar J B 02-8-2024:::
        /// <summary>
        /// OperationCheckListDetail
        /// </summary>
        public class OperationCheckListDetail
        {
            public int OperationCheckListDetail_ID
            {
                get;
                set;
            }
            public int OperationCheckListLocaleDetail_ID
            {
                get;
                set;
            }

            public string CheckList_Description
            {
                get;
                set;
            }
            public string CheckListLocale_Description
            {
                get;
                set;
            }
            public bool IsMandatory
            {
                get;
                set;
            }
            public bool IsSpecialTools
            {
                get;
                set;
            }
            public bool IsSafetyMeasures
            {
                get;
                set;
            }

        }
        #endregion


        #region ::: OperationMaster Uday Kumar J B  02-08-2024 :::
        /// <summary>
        /// OperationMaster
        /// </summary>
        public class OperationMaster
        {
            public int Operation_ID
            {
                get;
                set;
            }
            public int Company_ID
            {
                get;
                set;
            }
            public string Operation_Description
            {
                get;
                set;
            }

            public string Operation_Code
            {
                get;
                set;
            }
            public string SkillName
            {
                get;
                set;
            }
            public string FunctionGroup_Name
            {
                get;
                set;
            }
            public string Operation_IsActive
            {
                get;
                set;
            }
        }
        #endregion


        #region ::: ParentCompanyObject Uday Kumar J B  02-08-2024 :::
        /// <summary>
        /// ParentCompanyObject
        /// </summary>
        public class ParentCompanyObject
        {
            public int Company_ID
            {
                get;
                set;
            }

            public string Company_Name
            {
                get;
                set;
            }

            public int Company_Parent_ID
            {
                get;
                set;
            }
        }
        #endregion


        #region ::: OperationProductDetail Uday Kumar J B 02-08-2024 :::
        /// <summary>
        /// OperationProductDetail
        /// </summary>
        public class OperationProductDetail
        {
            public int OperationProductDetail_ID
            {
                get;
                set;
            }

            public string Brand
            {
                get;
                set;
            }
            public string ProductType
            {
                get;
                set;
            }
            public string Model
            {
                get;
                set;
            }
            public int ModelID
            {
                get;
                set;
            }
            public int ProductType_ID
            {
                get;
                set;
            }
        }
        #endregion


        #region ::: OperationBranchDetail Uday Kumar J B 02-08-2024 :::
        /// <summary>
        /// OperationBranchDetail
        /// </summary>
        public class OperationBranchDetail
        {
            public int OperationBranchDetail_ID
            {
                get;
                set;
            }

            public string Branch_Name
            {
                get;
                set;
            }
            public int Branch_ID { get; set; }
        }
        #endregion


        #region ::: SRTHistoryDetails Uday Kumar J B 02-08-2024 :::
        /// <summary>
        /// SRTHistoryDetails
        /// </summary>
        public class SRTHistoryDetails
        {
            public decimal ExistingStandardTime
            {
                get;
                set;
            }
            public decimal UpdatedStandardTime
            {
                get;
                set;
            }

            public string UpdatedBy
            {
                get;
                set;
            }
            public string UpdatedDate
            {
                get;
                set;
            }
            public string ReferenceNumber
            {
                get;
                set;
            }
            public int OperationStandardTimeUpdateLog_ID
            {
                get;
                set;
            }
        }
        #endregion

        public partial class GNM_Model
        {
            public GNM_Model()
            {
                this.GNM_ModelLocale = new HashSet<GNM_ModelLocale>();
                this.GNM_ModelPriceDetails = new HashSet<GNM_ModelPriceDetails>();
                this.GNM_ModelQuickListDetails = new HashSet<GNM_ModelQuickListDetails>();
                this.GNM_ModelRedCarpetListDetails = new HashSet<GNM_ModelRedCarpetListDetails>();
                this.GNM_ModelSalesPriceDetails = new HashSet<GNM_ModelSalesPriceDetails>();
                this.GNM_ModelServiceChargeDetail = new HashSet<GNM_ModelServiceChargeDetail>();
                this.GNM_MODELSERVICETYPEDET = new HashSet<GNM_MODELSERVICETYPEDET>();
                this.GNM_ModelWarrantyDefinitionDetails = new HashSet<GNM_ModelWarrantyDefinitionDetails>();
            }

            public int Model_ID { get; set; }
            public int ProductType_ID { get; set; }
            public int Brand_ID { get; set; }
            public string Model_Name { get; set; }
            public bool Model_IsActive { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }
            public Nullable<int> ServiceType_ID { get; set; }
            public Nullable<int> ServiceFrequency { get; set; }
            public string Model_Description { get; set; }
            public Nullable<byte> AttachmentCount { get; set; }
            public string Series { get; set; }

            public virtual ICollection<GNM_ModelLocale> GNM_ModelLocale { get; set; }
            public virtual ICollection<GNM_ModelPriceDetails> GNM_ModelPriceDetails { get; set; }
            public virtual ICollection<GNM_ModelQuickListDetails> GNM_ModelQuickListDetails { get; set; }
            public virtual ICollection<GNM_ModelRedCarpetListDetails> GNM_ModelRedCarpetListDetails { get; set; }
            public virtual ICollection<GNM_ModelSalesPriceDetails> GNM_ModelSalesPriceDetails { get; set; }
            public virtual ICollection<GNM_ModelServiceChargeDetail> GNM_ModelServiceChargeDetail { get; set; }
            public virtual ICollection<GNM_MODELSERVICETYPEDET> GNM_MODELSERVICETYPEDET { get; set; }
            public virtual ICollection<GNM_ModelWarrantyDefinitionDetails> GNM_ModelWarrantyDefinitionDetails { get; set; }
        }
        public partial class GNM_ModelWarrantyDefinitionDetails
        {
            public int ModelWarrantyDefinitionDetails_ID { get; set; }
            public int Model_ID { get; set; }
            public Nullable<int> WarrantyType_ID { get; set; }
            public Nullable<int> ServiceType_ID { get; set; }
            public Nullable<bool> Applicable_Type { get; set; }
            public int WarrantyMonths { get; set; }
            public int WarrantyHours { get; set; }
            public System.DateTime EffectiveFrom { get; set; }
            public int Company_ID { get; set; }

            public virtual GNM_Model GNM_Model { get; set; }
        }

        public partial class GNM_MODELSERVICETYPEDET
        {
            public int MODELSERVICETYPEDET_ID { get; set; }
            public int MODEL_ID { get; set; }
            public int SERVICETYPE_ID { get; set; }
            public int COMPANY_ID { get; set; }

            public virtual GNM_Model GNM_Model { get; set; }
        }
        public partial class GNM_ModelServiceChargeDetail
        {
            public int ModelServiceChargeDetail_ID { get; set; }
            public Nullable<int> Model_ID { get; set; }
            public Nullable<int> ServiceType_ID { get; set; }
            public Nullable<decimal> ServiceCharge { get; set; }
            public Nullable<int> Company_ID { get; set; }
            public Nullable<System.DateTime> EffectiveDate { get; set; }

            public virtual GNM_Model GNM_Model { get; set; }
        }
        public partial class GNM_ModelSalesPriceDetails
        {
            public int ModelSalesPriceDetails_ID { get; set; }
            public int Model_ID { get; set; }
            public decimal ListPrice { get; set; }
            public decimal DealerNetPrice { get; set; }
            public decimal MRP { get; set; }
            public System.DateTime EffectiveFrom { get; set; }

            public virtual GNM_Model GNM_Model { get; set; }
        }
        public partial class GNM_ModelRedCarpetListDetailsLocale
        {
            public int ModelRedCarpetChecklistLocale_ID { get; set; }
            public int ModelRedCarpetChecklist_ID { get; set; }
            public string Description { get; set; }
            public Nullable<int> Language_ID { get; set; }

            public virtual GNM_ModelRedCarpetListDetails GNM_ModelRedCarpetListDetails { get; set; }
        }
        public partial class GNM_ModelRedCarpetListDetails
        {
            public GNM_ModelRedCarpetListDetails()
            {
                this.GNM_ModelRedCarpetListDetailsLocale = new HashSet<GNM_ModelRedCarpetListDetailsLocale>();
            }

            public int ModelRedCarpetChecklist_ID { get; set; }
            public int Model_ID { get; set; }
            public string Description { get; set; }
            public bool IsMandatory { get; set; }
            public bool IsPhotoRequired { get; set; }

            public virtual ICollection<GNM_ModelRedCarpetListDetailsLocale> GNM_ModelRedCarpetListDetailsLocale { get; set; }
            public virtual GNM_Model GNM_Model { get; set; }
        }
        public partial class GNM_ModelQuickListDetailsLocale
        {
            public int ModelQuickChecklistLocale_ID { get; set; }
            public int ModelQuickChecklist_ID { get; set; }
            public string Description { get; set; }
            public Nullable<int> Language_ID { get; set; }

            public virtual GNM_ModelQuickListDetails GNM_ModelQuickListDetails { get; set; }
        }

        public partial class GNM_ModelQuickListDetails
        {
            public GNM_ModelQuickListDetails()
            {
                this.GNM_ModelQuickListDetailsLocale = new HashSet<GNM_ModelQuickListDetailsLocale>();
            }

            public int ModelQuickChecklist_ID { get; set; }
            public int Model_ID { get; set; }
            public string Description { get; set; }
            public bool IsMandatory { get; set; }
            public bool IsPhotoRequired { get; set; }
            public Nullable<byte> IsDefaultCheck { get; set; }
            public string Minvalue { get; set; }
            public string Maxvalue { get; set; }

            public virtual ICollection<GNM_ModelQuickListDetailsLocale> GNM_ModelQuickListDetailsLocale { get; set; }
            public virtual GNM_Model GNM_Model { get; set; }
        }
        public partial class GNM_ModelPriceDetails
        {
            public int ModelPriceDetails_ID { get; set; }
            public int Model_ID { get; set; }
            public decimal ListPrice { get; set; }
            public System.DateTime EffectiveFrom { get; set; }
            public int Company_ID { get; set; }

            public virtual GNM_Model GNM_Model { get; set; }
        }
        public partial class GNM_ModelLocale
        {
            public int ModelLocale_ID { get; set; }
            public int Model_ID { get; set; }
            public string Model_Name { get; set; }
            public int Language_ID { get; set; }
            public string Model_Description { get; set; }

            public virtual GNM_Model GNM_Model { get; set; }
        }

        public partial class GNM_ProductType
        {
            public GNM_ProductType()
            {
                this.GNM_ProductTypeLocale = new HashSet<GNM_ProductTypeLocale>();
            }

            public int ProductType_ID { get; set; }
            public int Brand_ID { get; set; }
            public string ProductType_Name { get; set; }
            public bool ProductType_IsActive { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }

            public virtual ICollection<GNM_ProductTypeLocale> GNM_ProductTypeLocale { get; set; }
        }
        public partial class GNM_ProductTypeLocale
        {
            public int ProductTypeLocale_ID { get; set; }
            public int ProductType_ID { get; set; }
            public string ProductType_Name { get; set; }
            public int Language_ID { get; set; }

            public virtual GNM_ProductType GNM_ProductType { get; set; }
        }
        public partial class SRM_Operation
        {
            public SRM_Operation()
            {
                this.SRM_OperationBranchDetail = new HashSet<SRM_OperationBranchDetail>();
                this.SRM_OperationLocale = new HashSet<SRM_OperationLocale>();
                this.SRM_OperationCheckListDetail = new HashSet<SRM_OperationCheckListDetail>();
                this.SRM_OperationProductDetail = new HashSet<SRM_OperationProductDetail>();
                this.SRM_OperationStandardTimeUpdateLog = new HashSet<SRM_OperationStandardTimeUpdateLog>();
            }

            public int Operation_ID { get; set; }
            public int Company_ID { get; set; }
            public string Operation_Description { get; set; }
            public string Operation_Code { get; set; }
            public Nullable<int> FunctionGroup_ID { get; set; }
            public Nullable<int> Skill_ID { get; set; }
            public Nullable<byte> Operation_SkillLevel { get; set; }
            public Nullable<decimal> Operation_StandardTime { get; set; }
            public bool Operation_IsActive { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }
            public Nullable<decimal> Operation_Time { get; set; }

            public virtual ICollection<SRM_OperationBranchDetail> SRM_OperationBranchDetail { get; set; }
            public virtual ICollection<SRM_OperationLocale> SRM_OperationLocale { get; set; }
            public virtual ICollection<SRM_OperationCheckListDetail> SRM_OperationCheckListDetail { get; set; }
            public virtual ICollection<SRM_OperationProductDetail> SRM_OperationProductDetail { get; set; }
            public virtual ICollection<SRM_OperationStandardTimeUpdateLog> SRM_OperationStandardTimeUpdateLog { get; set; }
        }

        public partial class GNM_FunctionGroup
        {
            public GNM_FunctionGroup()
            {
                this.GNM_FunctionGroupLocale = new HashSet<GNM_FunctionGroupLocale>();
            }

            public int FunctionGroup_ID { get; set; }
            public int Company_ID { get; set; }
            public string FunctionGroup_Name { get; set; }
            public Nullable<int> Brand_ID { get; set; }
            public bool FunctionGroup_IsActive { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }

            public virtual ICollection<GNM_FunctionGroupLocale> GNM_FunctionGroupLocale { get; set; }
        }

        public partial class GNM_FunctionGroupLocale
        {
            public int FunctionGroupLocale_ID { get; set; }
            public int FunctionGroup_ID { get; set; }
            public string FunctionGroup_Name { get; set; }
            public int Language_ID { get; set; }

            public virtual GNM_FunctionGroup GNM_FunctionGroup { get; set; }
        }
        public partial class GNM_RefMasterDetailLocale
        {
            public int RefMasterDetailLocale_ID { get; set; }
            public int RefMasterDetail_ID { get; set; }
            public int RefMaster_ID { get; set; }
            public string RefMasterDetail_Short_Name { get; set; }
            public string RefMasterDetail_Name { get; set; }
            public int Language_ID { get; set; }

            public virtual GNM_RefMaster GNM_RefMaster { get; set; }
            public virtual GNM_RefMasterDetail GNM_RefMasterDetail { get; set; }
        }
        public partial class GNM_RefMasterDetail
        {
            public GNM_RefMasterDetail()
            {
                this.GNM_RefMasterDetailLocale = new HashSet<GNM_RefMasterDetailLocale>();
            }

            public int RefMasterDetail_ID { get; set; }
            public bool RefMasterDetail_IsActive { get; set; }
            public string RefMasterDetail_Short_Name { get; set; }
            public string RefMasterDetail_Name { get; set; }
            public Nullable<int> Company_ID { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }
            public int RefMaster_ID { get; set; }
            public bool RefMasterDetail_IsDefault { get; set; }
            public Nullable<int> Region_ID { get; set; }
            public string SystemCondition { get; set; }

            public virtual GNM_RefMaster GNM_RefMaster { get; set; }
            public virtual ICollection<GNM_RefMasterDetailLocale> GNM_RefMasterDetailLocale { get; set; }
        }

        public partial class SRM_OperationBranchDetail
        {
            public int OperationBranchDetail_ID { get; set; }
            public int Operation_ID { get; set; }
            public int Branch_ID { get; set; }

            public virtual SRM_Operation SRM_Operation { get; set; }
        }


        public partial class SRM_OperationLocale
        {
            public int OperationLocale_ID { get; set; }
            public int Operation_ID { get; set; }
            public string Operation_Description { get; set; }
            public int Language_ID { get; set; }

            public virtual SRM_Operation SRM_Operation { get; set; }
        }
        public partial class SRM_OperationCheckListDetail
        {
            public SRM_OperationCheckListDetail()
            {
                this.SRM_OperationCheckListLocaleDetail = new HashSet<SRM_OperationCheckListLocaleDetail>();
            }

            public int OperationCheckListDetail_ID { get; set; }
            public int Operation_ID { get; set; }
            public string CheckListDescription { get; set; }
            public Nullable<bool> IsMandatory { get; set; }
            public Nullable<bool> IsSpecialTools { get; set; }
            public Nullable<bool> IsSafetyMeasures { get; set; }

            public virtual SRM_Operation SRM_Operation { get; set; }
            public virtual ICollection<SRM_OperationCheckListLocaleDetail> SRM_OperationCheckListLocaleDetail { get; set; }
        }

        public partial class SRM_OperationCheckListLocaleDetail
        {
            public int OperationCheckListLocaleDetail_ID { get; set; }
            public int OperationCheckListDetail_ID { get; set; }
            public string CheckListLocaleDescription { get; set; }
            public int Language_ID { get; set; }

            public virtual SRM_OperationCheckListDetail SRM_OperationCheckListDetail { get; set; }
        }
        public partial class SRM_OperationProductDetail
        {
            public int OperationProductDetail_ID { get; set; }
            public int Operation_ID { get; set; }
            public int Brand_ID { get; set; }
            public Nullable<int> ProductType_ID { get; set; }
            public Nullable<int> Model_ID { get; set; }

            public virtual SRM_Operation SRM_Operation { get; set; }
        }

        public partial class SRM_OperationStandardTimeUpdateLog
        {
            public int OperationStandardTimeUpdateLog_ID { get; set; }
            public int Operation_ID { get; set; }
            public decimal ExistingStandardTime { get; set; }
            public decimal UpdatedStandardTime { get; set; }
            public int UpdatedBy { get; set; }
            public System.DateTime UpdatedDate { get; set; }
            public string ReferenceNumber { get; set; }

            public virtual SRM_Operation SRM_Operation { get; set; }
        }

        #endregion

    }
}
