
using System;
using System.Data;
using System.Data.SqlClient;
using PBC.UtilityService.Utilities.Models;

namespace PBC.UtilityService.Utilities
{
    public class WorkFlowCommon
    {
        #region chkforAdmin
        /// <summary>
        /// chkforAdmin
        /// </summary>
        /// <param name="userID"></param>
        /// <param name="WorkFlowName"></param>
        /// <param name="DBName"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static bool chkforAdmin(int userID, string WorkFlowName, string DBName, string connString, int LogException)
        {
            bool flag = false;
            try
            {

                int WorkFlowID = Common.GetWorkFlowID(WorkFlowName, DBName, connString, LogException);
                using (SqlConnection conn = new SqlConnection(connString))
                {


                    SqlCommand cmd = null;

                    try
                    {
                        string query = @"
                        SELECT COUNT(*)
                            FROM GNM_WFRoleUser AS uid
                            INNER JOIN GNM_WFRole AS role ON uid.WFRole_ID = role.WFRole_ID
                            WHERE uid.UserID = @UserID
                            AND role.WorkFlow_ID = @WorkFlowID
                            AND role.WfRole_IsAdmin = 1";
                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;

                            cmd.Parameters.AddWithValue("@UserID", userID);
                            cmd.Parameters.AddWithValue("@WorkFlowID", WorkFlowID);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            int result = (int)cmd.ExecuteScalar();

                            flag = result > 0;


                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }

            }
            catch (Exception ex)
            {
                flag = false;
                LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return flag;
        }

        #endregion

        #region LockRecord
        public static string LockRecord(string connString, int LogException, string UserCulture, int QuotationID, int userID, int CompanyID, string WorkFlowName, string DBName, int Branch_ID = 0)
        {
            string text = "";
            int workFlow_ID = 0;
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string queryWorkFlow = "SELECT WorkFlow_ID FROM GNM_WorkFlow WHERE WorkFlow_Name = @WorkFlowName";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(queryWorkFlow, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@WorkFlowName", WorkFlowName);




                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            object result = cmd.ExecuteScalar();
                            workFlow_ID = result != null ? Convert.ToInt32(result) : throw new InvalidOperationException("WorkFlow not found.");



                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }


                text = LockTransaction(connString, LogException, CompanyID, workFlow_ID, QuotationID, userID, 0, Branch_ID);
                text = CommonFunctionalities.GetResourceString(UserCulture.ToString(), text).ToString();
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return text;
        }

        #endregion

        #region LockTransaction
        public static string LockTransaction(string connString, int LogException, int companyID, int workFlowID, int transactionNumber, int userID, int transactionValue, int Branch_ID = 0)
        {
            string empty = string.Empty;
            int num = 0;
            WF_WFCase_Progress wF_WFCase_Progress = null;
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {



                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                    {
                        conn.Open();
                    }

                    try
                    {

                        using (SqlCommand command = new SqlCommand("SELECT TOP 1 WFAction_ID FROM GNM_WFAction WHERE WFAction_Name = @WFActionName", conn))
                        {
                            command.Parameters.AddWithValue("@WFActionName", "Lock");
                            object result = command.ExecuteScalar();
                            num = result != null ? Convert.ToInt32(result) : 0;
                        }
                        using (SqlCommand command = new SqlCommand(@"SELECT TOP 1 * FROM GNM_WFCase_Progress 
                                                    WHERE WorkFlow_ID = @WorkFlowID AND Transaction_ID = @TransactionID 
                                                    ORDER BY WFCaseProgress_ID DESC", conn))
                        {
                            command.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                            command.Parameters.AddWithValue("@TransactionID", transactionNumber);
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    wF_WFCase_Progress = new WF_WFCase_Progress
                                    {
                                        WFCaseProgress_ID = reader.GetInt32(reader.GetOrdinal("WFCaseProgress_ID")),
                                        WorkFlow_ID = reader.GetInt32(reader.GetOrdinal("WorkFlow_ID")),
                                        Transaction_ID = reader.GetInt32(reader.GetOrdinal("Transaction_ID")),
                                        WFSteps_ID = reader.GetInt32(reader.GetOrdinal("WFSteps_ID")),
                                        Addresse_ID = reader.IsDBNull(reader.GetOrdinal("Addresse_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Addresse_ID")),
                                        Addresse_Flag = reader.IsDBNull(reader.GetOrdinal("Addresse_Flag")) ? (byte)0 : reader.GetByte(reader.GetOrdinal("Addresse_Flag")),
                                        Received_Time = reader.GetDateTime(reader.GetOrdinal("Received_Time")),
                                        Actioned_By = reader.IsDBNull(reader.GetOrdinal("Actioned_By")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Actioned_By")),
                                        Action_Time = reader.IsDBNull(reader.GetOrdinal("Action_Time")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("Action_Time")),
                                        Action_Chosen = reader.IsDBNull(reader.GetOrdinal("Action_Chosen")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Action_Chosen")),
                                        Action_Remarks = reader.IsDBNull(reader.GetOrdinal("Action_Remarks")) ? null : reader.GetString(reader.GetOrdinal("Action_Remarks")),
                                        Locked_Ind = reader.IsDBNull(reader.GetOrdinal("Locked_Ind")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("Locked_Ind")),
                                        WFNextStep_ID = reader.IsDBNull(reader.GetOrdinal("WFNextStep_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WFNextStep_ID"))
                                    };

                                }
                            }
                        }
                        if (wF_WFCase_Progress != null)
                        {
                            if (wF_WFCase_Progress.Action_Chosen != num)
                            {
                                using (SqlCommand command = new SqlCommand(@"UPDATE GNM_WFCase_Progress 
                                                            SET Actioned_By = @UserID, 
                                                                Action_Time = @ActionTime, 
                                                                Action_Chosen = @ActionChosen 
                                                            WHERE WFCaseProgress_ID = @WFCaseProgressID", conn))
                                {
                                    command.Parameters.AddWithValue("@UserID", userID);
                                    command.Parameters.AddWithValue("@ActionTime", Branch_ID != 0 ? LocalTime(Branch_ID, DateTime.Now, connString) : DateTime.Now);
                                    command.Parameters.AddWithValue("@ActionChosen", num);
                                    command.Parameters.AddWithValue("@WFCaseProgressID", wF_WFCase_Progress.WFCaseProgress_ID);
                                    command.ExecuteNonQuery();
                                }
                                using (SqlCommand command = new SqlCommand(@"INSERT INTO GNM_WFCase_Progress 
                                                            (WorkFlow_ID, Transaction_ID, WFSteps_ID, Addresse_Flag, Addresse_ID, Received_Time, Locked_Ind) 
                                                            VALUES (@WorkFlowID, @TransactionID, @WFStepsID, @AddresseFlag, @AddresseID, @ReceivedTime, @LockedInd)", conn))
                                {
                                    command.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                                    command.Parameters.AddWithValue("@TransactionID", transactionNumber);
                                    command.Parameters.AddWithValue("@WFStepsID", wF_WFCase_Progress.WFSteps_ID);
                                    command.Parameters.AddWithValue("@AddresseFlag", 1);
                                    command.Parameters.AddWithValue("@AddresseID", userID);
                                    command.Parameters.AddWithValue("@ReceivedTime", Branch_ID != 0 ? LocalTime(Branch_ID, DateTime.Now, connString) : DateTime.Now);
                                    command.Parameters.AddWithValue("@LockedInd", true);
                                    command.ExecuteNonQuery();
                                }


                                empty = "TransactionLockedSuccessfully";
                            }
                            else
                            {
                                empty = "TransactionisalreadybeenLocked";
                            }
                        }
                        else
                        {
                            empty = "ErrorOccuredwhileLocking";
                        }

                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {

                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }





            }
            catch (Exception ex)
            {
                empty = "ErrorOccuredwhileUnLocking";
                LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return empty;
        }
        public static DateTime LocalTime(int Branch_ID, DateTime servertime, string strCon)
        {
            int num = 0;
            string destinationTimeZoneId = "";
            DateTime now = DateTime.Now;
            try
            {
                SqlCommand cmd = null;
                using (SqlConnection sqlConnection = new SqlConnection(strCon))
                {
                    cmd = new SqlCommand("select TimeZoneID from GNM_Branch where Branch_ID='" + Branch_ID + "'", sqlConnection);
                    sqlConnection.Open();
                    num = Convert.ToInt32(cmd.ExecuteScalar());
                    sqlConnection.Close();
                }

                try
                {
                    using (SqlConnection sqlConnection = new SqlConnection(strCon))
                    {
                        cmd = new SqlCommand("select RefMasterDetail_Name from GNM_RefMasterDetail where RefMasterDetail_ID='" + num + "'", sqlConnection);
                        sqlConnection.Open();
                        destinationTimeZoneId = cmd.ExecuteScalar().ToString();
                        sqlConnection.Close();
                    }

                    return TimeZoneInfo.ConvertTimeBySystemTimeZoneId(servertime, TimeZoneInfo.Local.Id, destinationTimeZoneId);
                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion

        #region
        public static string UnLockRecord(string connString, int LogException, string UserCulture, int jobcardID, int userID, int CompanyID, string WorkFlowName, string DBName, int Branch_ID = 0)
        {
            string text = "";
            int workFlowID = 0;
            try
            {

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string queryWorkFlow = "SELECT WorkFlow_ID FROM GNM_WorkFlow WHERE WorkFlow_Name = @WorkFlowName";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(queryWorkFlow, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@WorkFlowName", WorkFlowName);




                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            object result = cmd.ExecuteScalar();
                            workFlowID = result != null ? Convert.ToInt32(result) : throw new InvalidOperationException("WorkFlow not found.");



                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }

                text = UnLockTransaction(connString, LogException, CompanyID, workFlowID, jobcardID, userID, "", Branch_ID);
                text = CommonFunctionalities.GetResourceString(UserCulture.ToString(), text).ToString();
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return text;
        }
        #endregion

        #region UnLockTransaction
        public static string UnLockTransaction(string connString, int LogException, int companyID, int workFlowID, int transactionNumber, int userID, string remarks, int Branch_ID = 0)
        {
            string empty = string.Empty;
            int num2 = 0;
            SqlCommand cmd = null;
            WF_WFCase_Progress wF_WFCase_Progress = new WF_WFCase_Progress();
            WF_WFCase_Progress wF_WFCase_Progress2 = new WF_WFCase_Progress();
            byte addresse_Flag = 0;

            WF_WFCase_Progress wF_WFCase_Progress3 = new WF_WFCase_Progress();
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    //string queryWorkFlow = "SELECT TOP 1 WFAction_ID FROM GNM_WFAction WHERE UPPER(WFAction_Name) = 'UNLOCK'";

                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                    {
                        conn.Open();
                    }

                    try
                    {

                        using (cmd = new SqlCommand("SELECT TOP 1 WFAction_ID FROM GNM_WFAction WHERE UPPER(WFAction_Name) = 'UNLOCK'", conn))
                        {
                            object result = cmd.ExecuteScalar();
                            num2 = result != null ? Convert.ToInt32(result) : 0;
                        }
                        using (cmd = new SqlCommand(@"SELECT TOP 1 * FROM GNM_WFCase_Progress 
                                                WHERE WorkFlow_ID = @WorkFlowID AND Transaction_ID = @TransactionID AND Addresse_Flag = 0 
                                                ORDER BY WFCaseProgress_ID DESC", conn))
                        {
                            cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                            cmd.Parameters.AddWithValue("@TransactionID", transactionNumber);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    wF_WFCase_Progress = new WF_WFCase_Progress
                                    {
                                        WFCaseProgress_ID = reader.GetInt32(reader.GetOrdinal("WFCaseProgress_ID")),
                                        WorkFlow_ID = reader.GetInt32(reader.GetOrdinal("WorkFlow_ID")),
                                        Transaction_ID = reader.GetInt32(reader.GetOrdinal("Transaction_ID")),
                                        WFSteps_ID = reader.GetInt32(reader.GetOrdinal("WFSteps_ID")),
                                        Addresse_ID = reader.IsDBNull(reader.GetOrdinal("Addresse_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Addresse_ID")),
                                        Addresse_Flag = reader.GetByte(reader.GetOrdinal("Addresse_Flag")),
                                        Received_Time = reader.GetDateTime(reader.GetOrdinal("Received_Time")),
                                        Actioned_By = reader.IsDBNull(reader.GetOrdinal("Actioned_By")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Actioned_By")),
                                        Action_Time = reader.IsDBNull(reader.GetOrdinal("Action_Time")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("Action_Time")),
                                        Action_Chosen = reader.IsDBNull(reader.GetOrdinal("Action_Chosen")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Action_Chosen")),
                                        Action_Remarks = reader.IsDBNull(reader.GetOrdinal("Action_Remarks")) ? null : reader.GetString(reader.GetOrdinal("Action_Remarks")),
                                        Locked_Ind = reader.IsDBNull(reader.GetOrdinal("Locked_Ind")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("Locked_Ind")),
                                        WFNextStep_ID = reader.IsDBNull(reader.GetOrdinal("WFNextStep_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WFNextStep_ID"))
                                    };
                                }
                            }
                        }
                        using (cmd = new SqlCommand(@"SELECT TOP 1 * FROM GNM_WFCase_Progress 
                                                WHERE WorkFlow_ID = @WorkFlowID AND Transaction_ID = @TransactionID 
                                                ORDER BY WFCaseProgress_ID DESC", conn))
                        {

                            cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                            cmd.Parameters.AddWithValue("@TransactionID", transactionNumber);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    wF_WFCase_Progress2 = new WF_WFCase_Progress
                                    {
                                        WFCaseProgress_ID = reader.GetInt32(reader.GetOrdinal("WFCaseProgress_ID")),
                                        WorkFlow_ID = reader.GetInt32(reader.GetOrdinal("WorkFlow_ID")),
                                        Transaction_ID = reader.GetInt32(reader.GetOrdinal("Transaction_ID")),
                                        WFSteps_ID = reader.GetInt32(reader.GetOrdinal("WFSteps_ID")),
                                        Addresse_ID = reader.IsDBNull(reader.GetOrdinal("Addresse_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Addresse_ID")),
                                        Addresse_Flag = reader.GetByte(reader.GetOrdinal("Addresse_Flag")),
                                        Received_Time = reader.GetDateTime(reader.GetOrdinal("Received_Time")),
                                        Actioned_By = reader.IsDBNull(reader.GetOrdinal("Actioned_By")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Actioned_By")),
                                        Action_Time = reader.IsDBNull(reader.GetOrdinal("Action_Time")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("Action_Time")),
                                        Action_Chosen = reader.IsDBNull(reader.GetOrdinal("Action_Chosen")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Action_Chosen")),
                                        Action_Remarks = reader.IsDBNull(reader.GetOrdinal("Action_Remarks")) ? null : reader.GetString(reader.GetOrdinal("Action_Remarks")),
                                        Locked_Ind = reader.IsDBNull(reader.GetOrdinal("Locked_Ind")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("Locked_Ind")),
                                        WFNextStep_ID = reader.IsDBNull(reader.GetOrdinal("WFNextStep_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WFNextStep_ID"))
                                    };
                                }
                            }
                        }
                        addresse_Flag = ((wF_WFCase_Progress == null) ? Convert.ToByte(1) : Convert.ToByte(0));
                        using (cmd = new SqlCommand(@"SELECT TOP 1 * FROM GNM_WFCase_Progress 
                                                WHERE WorkFlow_ID = @WorkFlowID AND Transaction_ID = @TransactionID AND Addresse_Flag = 1 AND Action_Chosen IS NOT NULL 
                                                ORDER BY WFCaseProgress_ID DESC", conn))
                        {
                            cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                            cmd.Parameters.AddWithValue("@TransactionID", transactionNumber);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    wF_WFCase_Progress3 = new WF_WFCase_Progress
                                    {
                                        Addresse_ID = reader.GetInt32(reader.GetOrdinal("Addresse_ID"))
                                    };
                                }
                            }
                        }

                        if (wF_WFCase_Progress2 != null)
                        {
                            if (wF_WFCase_Progress2.Action_Chosen != num2)
                            {
                                using (cmd = new SqlCommand(@"UPDATE GNM_WFCase_Progress 
                                                        SET Actioned_By = @UserID, 
                                                            Action_Time = @ActionTime, 
                                                            Action_Chosen = @ActionChosen, 
                                                            Action_Remarks = @Remarks 
                                                        WHERE WFCaseProgress_ID = @WFCaseProgressID", conn))
                                {
                                    cmd.Parameters.AddWithValue("@UserID", userID);
                                    cmd.Parameters.AddWithValue("@ActionTime", Branch_ID != 0 ? LocalTime(Branch_ID, DateTime.Now, connString) : DateTime.Now);
                                    cmd.Parameters.AddWithValue("@ActionChosen", num2);
                                    cmd.Parameters.AddWithValue("@Remarks", remarks);
                                    cmd.Parameters.AddWithValue("@WFCaseProgressID", wF_WFCase_Progress2.WFCaseProgress_ID);
                                    cmd.ExecuteNonQuery();
                                }
                                using (cmd = new SqlCommand(@"INSERT INTO GNM_WFCase_Progress 
                                                        (WorkFlow_ID, Transaction_ID, WFSteps_ID, Addresse_Flag, Addresse_ID, Received_Time) 
                                                        VALUES (@WorkFlowID, @TransactionID, @WFStepsID, @AddresseFlag, @AddresseID, @ReceivedTime)", conn))
                                {
                                    cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                                    cmd.Parameters.AddWithValue("@TransactionID", transactionNumber);
                                    cmd.Parameters.AddWithValue("@WFStepsID", wF_WFCase_Progress2.WFSteps_ID);
                                    cmd.Parameters.AddWithValue("@AddresseFlag", addresse_Flag);
                                    cmd.Parameters.AddWithValue("@AddresseID", ((wF_WFCase_Progress == null) ? wF_WFCase_Progress3.Addresse_ID : wF_WFCase_Progress.Addresse_ID));
                                    cmd.Parameters.AddWithValue("@ReceivedTime", ((Branch_ID != 0) ? LocalTime(Branch_ID, DateTime.Now, connString) : DateTime.Now));
                                    cmd.ExecuteNonQuery();
                                }


                                return "TransactionUnLockedSuccessfully";
                            }

                            return "TransactionisalreadybeenUnLocked";
                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }

                return "ErrorOccuredwhileUnLocking";
            }
            catch (Exception)
            {
                return "ErrorOccuredwhileUnLocking";
            }
        }
        #endregion
    }
}