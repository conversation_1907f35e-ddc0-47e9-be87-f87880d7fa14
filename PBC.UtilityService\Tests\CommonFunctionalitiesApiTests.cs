using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using PBC.UtilityService.Services;
using PBC.UtilityService.Utilities.DTOs;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace PBC.UtilityService.Tests
{
    /// <summary>
    /// Integration tests for Common Functionalities API
    /// </summary>
    public class CommonFunctionalitiesApiTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public CommonFunctionalitiesApiTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory;
            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task GetResourceString_ShouldReturnString()
        {
            // Arrange
            var request = new GetResourceRequest
            {
                CultureValue = "Resource_en",
                ResourceKey = "TestKey"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/commonfunctionalities/resource-string", request);

            // Assert
            response.EnsureSuccessStatusCode();
            var result = await response.Content.ReadAsStringAsync();
            Assert.NotNull(result);
        }

        [Fact]
        public async Task ConvertToHours_ShouldReturnFormattedTime()
        {
            // Arrange
            var request = new ConvertToHoursRequest
            {
                Minutes = 150 // 2 hours 30 minutes
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/commonfunctionalities/convert-to-hours", request);

            // Assert
            response.EnsureSuccessStatusCode();
            var result = await response.Content.ReadAsStringAsync();
            var timeFormat = JsonSerializer.Deserialize<string>(result);
            Assert.Equal("2:30", timeFormat);
        }

        [Fact]
        public async Task LoadCompanies_ShouldReturnCompanies()
        {
            // Arrange
            var request = new LoadCompanyRequest
            {
                CompanyType = "TestType"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/commonfunctionalities/companies", request);

            // Assert
            response.EnsureSuccessStatusCode();
            var result = await response.Content.ReadAsStringAsync();
            Assert.NotNull(result);
        }

        [Fact]
        public async Task LoadBranches_ShouldReturnBranches()
        {
            // Arrange
            var request = new LoadBranchRequest
            {
                Active = true,
                CompanyID = 1
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/commonfunctionalities/branches", request);

            // Assert
            response.EnsureSuccessStatusCode();
            var result = await response.Content.ReadAsStringAsync();
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetAttachmentCount_ShouldReturnCount()
        {
            // Arrange
            var request = new GetAttachmentCountRequest
            {
                ObjectID = 1,
                TransactionID = 1,
                DetailID = 1
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/commonfunctionalities/attachment-count", request);

            // Assert
            response.EnsureSuccessStatusCode();
            var result = await response.Content.ReadAsStringAsync();
            Assert.NotNull(result);
        }

        [Fact]
        public void CommonFunctionalitiesService_ShouldBeRegistered()
        {
            // Arrange & Act
            var service = _factory.Services.GetService<ICommonFunctionalitiesService>();

            // Assert
            Assert.NotNull(service);
            Assert.IsType<CommonFunctionalitiesService>(service);
        }
    }
}
