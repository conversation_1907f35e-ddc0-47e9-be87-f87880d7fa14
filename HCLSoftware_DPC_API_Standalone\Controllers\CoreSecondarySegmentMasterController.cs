﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreSecondarySegmentMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;



namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreSecondarySegmentMasterController : ApiController
    {

        #region ::: Select Uday Kumar J B 09-08-2024:::
        /// <summary>
        /// To Select Secondary Segment for a Primary Segment
        /// </summary>
        /// 
        [Route("api/CoreSecondarySegmentMaster/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] SelectCoreSecondarySegmentList SelectCoreSecondarySegmentobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreSecondarySegmentMasterServices.Select(connstring, SelectCoreSecondarySegmentobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectReferenceMaster Uday Kumar J B 09-08-2024 :::
        /// <summary>
        /// To Select Primary Segment 
        /// </summary> 
        ///
        [Route("api/CoreSecondarySegmentMaster/SelectReferenceMaster")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectReferenceMaster([FromBody] SelectReferenceMasterCoreSecondarySegmentList SelectReferenceMasterCoreSecondarySegmentobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreSecondarySegmentMasterServices.SelectReferenceMaster(connString, SelectReferenceMasterCoreSecondarySegmentobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectParticularSecondarySegment Uday Kumar J B 09-08-2024:::
        /// <summary>
        /// To Select Particular Secondary Segment
        /// </summary>
        ///  
        [Route("api/CoreSecondarySegmentMaster/SelectParticularSecondarySegment")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularSecondarySegment([FromBody] SelectParticularSecondarySegmentList SelectParticularSecondarySegmentobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreSecondarySegmentMasterServices.SelectParticularSecondarySegment(connString, SelectParticularSecondarySegmentobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Save Uday Kumar J B 09-08-2024:::
        /// <summary>
        /// To Insert and Update Secondary Segment
        /// </summary>
        ///
        [Route("api/CoreSecondarySegmentMaster/Save")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Save([FromBody] SaveCoreSecondarySegmentMasterList SaveCoreSecondarySegmentMasterobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreSecondarySegmentMasterServices.Save(connString, SaveCoreSecondarySegmentMasterobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: UpdateLocale Uday Kumar J B 09-08-2024:::
        /// <summary>
        /// To Update Secondary Segment Locale
        /// </summary>
        /// 
        [Route("api/CoreSecondarySegmentMaster/UpdateLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateLocale([FromBody] UpdateLocaleListb UpdateLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreSecondarySegmentMasterServices.UpdateLocale(connString, UpdateLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Delete Uday Kumar J B 09-08-2024 :::
        /// <summary>
        /// To Delete Secondary Segment
        /// </summary>
        /// 
        [Route("api/CoreSecondarySegmentMaster/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteCoreSecondarySegmentList DeleteCoreSecondarySegmentobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreSecondarySegmentMasterServices.Delete(connString, DeleteCoreSecondarySegmentobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Export Uday Kumar J B 09-08-2024 :::
        /// <summary>
        /// To Export Secondary Segment
        /// </summary>
        /// 
        [Route("api/CoreSecondarySegmentMaster/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] ExportCoreSecondarySegmentList ExportCoreSecondarySegmentobj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportCoreSecondarySegmentobj.sidx;
            string sord = ExportCoreSecondarySegmentobj.sord;
            string filter = ExportCoreSecondarySegmentobj.filter;
            string advnceFilter = ExportCoreSecondarySegmentobj.advanceFilter;

            try
            {


                Object Response = await CoreSecondarySegmentMasterServices.Export(ExportCoreSecondarySegmentobj, connstring, filter, advnceFilter, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion


        #region ::: CheckSecondarySegment Uday Kumar J B 09-08-2024:::
        /// <summary>
        /// To Check Secondary Segment already exists 
        /// </summary>
        /// 
        /// 
        [Route("api/CoreSecondarySegmentMaster/CheckSecondarySegment")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckSecondarySegment([FromBody] CheckSecondarySegmentList CheckSecondarySegmentobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreSecondarySegmentMasterServices.CheckSecondarySegment(connString, CheckSecondarySegmentobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckSecondarySegmentLocale Uday Kumar J B 09-08-2024:::
        /// <summary>
        /// To Check SecondarySegment Locale already exists 
        /// </summary>
        /// 
        [Route("api/CoreSecondarySegmentMaster/CheckSecondarySegmentLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckSecondarySegmentLocale([FromBody] CheckSecondarySegmentLocaleList CheckSecondarySegmentLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreSecondarySegmentMasterServices.CheckSecondarySegmentLocale(connString, CheckSecondarySegmentLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


    }
}