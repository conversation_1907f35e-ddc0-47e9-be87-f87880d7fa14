﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreIssueSubAreaMasterController : ApiController
    {
        #region::: Select Reference Master /Mithun:::
        /// <summary>
        /// To select Reference Master
        /// </summary>
        /// <param name="SelectReferenceMasterObj"></param>
        /// <returns></returns>
        [Route("api/CoreIssueSubAreaMaster/SelectReferenceMaster")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectReferenceMaster([FromBody] SelectReferenceMasterList SelectReferenceMasterObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreIssueSubAreaMasterServices.SelectReferenceMaster(Conn, SelectReferenceMasterObj, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: CheckIssueSubArea /Mithun:::
        /// <summary>
        /// To Check IssueSubArea_Description already exists 
        /// </summary>
        [Route("api/CoreIssueSubAreaMaster/CheckIssueSubArea")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckIssueSubArea([FromBody] CheckIssueSubAreaList CheckIssueSubAreaObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreIssueSubAreaMasterServices.CheckIssueSubArea(CheckIssueSubAreaObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Select /Mithun:::
        /// <summary>
        /// To Select IssueSubArea for a IssueArea
        /// </summary>
        [Route("api/CoreIssueSubAreaMaster/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] SelectList SelectObj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreIssueSubAreaMasterServices.Select(SelectObj, Conn, LogException, sidx, sord, page, rows, _search, advnce, filters, Query);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }

        #endregion

        #region ::: Delete /Mithun:::
        /// <summary>
        /// To Delete Issue Sub Area
        /// </summary>
        [Route("api/CoreIssueSubAreaMaster/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteLista DeleteObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreIssueSubAreaMasterServices.Delete(DeleteObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: Save /Mithun:::
        /// <summary>
        /// To Insert and Update State
        /// </summary>
        [Route("api/CoreIssueSubAreaMaster/Save")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Save([FromBody] SaveLista SaveObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreIssueSubAreaMasterServices.Save(SaveObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion

        #region ::: UpdateLocale /Mithun:::
        /// <summary>
        /// To update local
        /// </summary>

        [Route("api/CoreIssueSubAreaMaster/UpdateLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateLocale([FromBody] UpdateLocaleList UpdateLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreIssueSubAreaMasterServices.UpdateLocale(UpdateLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }



        #endregion

        #region ::: CheckDefectIssueSubAreaLocale /Mithun:::
        /// <summary>
        /// To Check Defect Issue SubArea Locale
        /// </summary>
        [Route("api/CoreIssueSubAreaMaster/CheckDefectIssueSubAreaLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckDefectIssueSubAreaLocale([FromBody] CheckDefectIssueSubAreaLocaleList CheckDefectIssueSubAreaLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreIssueSubAreaMasterServices.CheckDefectIssueSubAreaLocale(CheckDefectIssueSubAreaLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectParticularIssueSubArea :::
        /// <summary>
        /// To Select Particular IssueSubArea
        /// </summary>
        [Route("api/CoreIssueSubAreaMaster/SelectParticularIssueSubArea")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularIssueSubArea([FromBody] SelectParticularIssueSubAreaList SelectParticularIssueSubAreaObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreIssueSubAreaMasterServices.SelectParticularIssueSubArea(SelectParticularIssueSubAreaObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region::: Export /Mithun :::

        [Route("api/CoreIssueSubAreaMaster/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] SelectList ExportObj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportObj.sidx;
            string sord = ExportObj.sord;
            string filters = ExportObj.filters;
            string Query = ExportObj.Query;
            try
            {


                Object Response = await CoreIssueSubAreaMasterServices.Export(ExportObj, connstring, LogException, filters, Query, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }

        #endregion

    }
}