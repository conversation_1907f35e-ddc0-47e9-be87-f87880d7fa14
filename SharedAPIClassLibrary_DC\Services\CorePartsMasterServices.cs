﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;
using static SharedAPIClassLibrary_AMERP.Utilities.CoreCompanyCalenderMasterServices;
using DocumentFormat.OpenXml.Drawing;
using DocumentFormat.OpenXml.Drawing.Diagrams;
using DocumentFormat.OpenXml.VariantTypes;
using System.Transactions;
using System.Drawing;
using static System.Net.WebRequestMethods;

using DocumentFormat.OpenXml.Wordprocessing;
using DocumentFormat.OpenXml.Bibliography;
using Newtonsoft.Json;
using DocumentFormat.OpenXml.Office.Word;
using System.Reflection.Emit;
using System.Data.SqlTypes;
using Amazon.Runtime.Internal.Transform;
using System.Linq.Expressions;
using System.IO;
using System.Diagnostics;
using Amazon.S3.Model;


namespace SharedAPIClassLibrary_AMERP
{
    public class CorePartsMasterServices
    {
        public static string AppPath = string.Empty;
        public static string BucketFilePath = "quest-partsassist\\EPC_UploadedFiles";

        #region ::: SelectParts:::
        /// <summary>
        /// To select the All parts 
        /// </summary>        
        public static IActionResult SelectParts(SelectPartsList Obj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters, bool advnce, string Query)
        {
            int count = 0;
            int total = 0;
            var x = default(dynamic);
            string PartsIDQuery = string.Empty;
            IQueryable<GNMParts> iQPrty = null;
            //GNM_User User = (GNM_User)Session["UserDetails"];

            int CompanyID = Obj.Company_ID;
            string countquery = "";
            string wherecondition = "";
            List<GNMPartsQuery> liParts = new List<GNMPartsQuery>();
            try
            {
                List<ParentCompanyObject> ParentCompanyDetails = new List<ParentCompanyObject>();
                //Query = ";WITH ParentComapany([Company_ID],[Company_Name],[Company_Parent_ID]) as (SELECT [Company_ID],[Company_Name],[Company_Parent_ID] FROM dbo.GNM_Company WHERE [Company_ID] = " + CompanyID + " UNION ALL SELECT child.[Company_ID], child.[Company_Name], child.[Company_Parent_ID] FROM dbo.GNM_Company AS child JOIN ParentComapany ON child.[Company_ID] = ParentComapany.[Company_Parent_ID])SELECT [Company_ID],[Company_Name],[Company_Parent_ID] FROM ParentComapany where Company_ID !=" + CompanyID + ";";
                //ParentCompanyDetails = CompanyClient.Database.SqlQuery(typeof(ParentCompanyObject), Query).Cast<ParentCompanyObject>().ToList();

                Query = @"
                    DECLARE @CompanyID INT = " + CompanyID + @";

                    -- Create a temporary table to hold the results
                    CREATE TABLE #TempCompany (
                        Company_ID INT,
                        Company_Name NVARCHAR(255),
                        Company_Parent_ID INT
                    );

                    -- Insert the initial company into the temporary table
                    INSERT INTO #TempCompany (Company_ID, Company_Name, Company_Parent_ID)
                    SELECT Company_ID, Company_Name, Company_Parent_ID
                    FROM dbo.GNM_Company
                    WHERE Company_ID = @CompanyID;

                    -- Declare a variable to control the loop
                    DECLARE @RowCount INT;

                    -- Loop to insert all child companies into the temporary table
                    SET @RowCount = 1; -- Initialize RowCount
                    WHILE @RowCount > 0
                    BEGIN
                        -- Insert child companies into the temporary table
                        INSERT INTO #TempCompany (Company_ID, Company_Name, Company_Parent_ID)
                        SELECT child.Company_ID, child.Company_Name, child.Company_Parent_ID
                        FROM dbo.GNM_Company AS child
                        JOIN #TempCompany AS parent ON child.Company_Parent_ID = parent.Company_ID
                        WHERE child.Company_ID NOT IN (SELECT Company_ID FROM #TempCompany);

                        -- Get the number of rows inserted in this iteration
                        SET @RowCount = @@ROWCOUNT;
                    END;

                    -- Select the results excluding the original CompanyID
                    SELECT Company_ID, Company_Name, Company_Parent_ID
                    FROM #TempCompany
                    WHERE Company_ID != @CompanyID;

                    -- Clean up
                    DROP TABLE #TempCompany;
                    ";

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    using (SqlCommand command = new SqlCommand(Query, connection))
                    {
                        connection.Open();

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ParentCompanyObject company = new ParentCompanyObject
                                {
                                    Company_ID = reader["Company_ID"] != DBNull.Value ? Convert.ToInt32(reader["Company_ID"]) : 0,
                                    Company_Name = reader["Company_Name"] != DBNull.Value ? reader["Company_Name"].ToString() : null,
                                    Company_Parent_ID = reader["Company_Parent_ID"] != DBNull.Value ? Convert.ToInt32(reader["Company_Parent_ID"]) : 0
                                };
                                ParentCompanyDetails.Add(company);
                            }
                        }
                    }
                }
                string ParentCompanyIDs = "";
                for (int i = 0; i < ParentCompanyDetails.Count(); i++)
                {
                    ParentCompanyIDs += ParentCompanyDetails.ElementAt(i).Company_ID.ToString() + ",";
                }
                ParentCompanyIDs = ParentCompanyIDs.TrimEnd(new char[] { ',' });



                if (_search)
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    if (filtersObj.rules.Count() > 0)
                    {
                        for (int i = 0; i < filtersObj.rules.Count(); i++)
                        {
                            if (filtersObj.rules.ElementAt(i).field == "CompName")
                            {
                                filtersObj.rules.ElementAt(i).field = "c.Company_Name";
                            }
                            else if (filtersObj.rules.ElementAt(i).field == "Name")
                            {
                                filtersObj.rules.ElementAt(i).field = "Parts_PartsNumber";
                            }
                            else if (filtersObj.rules.ElementAt(i).field == "Parts_PartsPrefix")
                            {
                                filtersObj.rules.ElementAt(i).field = "Parts_PartPrefix";
                            }
                            else if (filtersObj.rules.ElementAt(i).field == "PartsDescription")
                            {
                                filtersObj.rules.ElementAt(i).field = "Parts_PartsDescription";
                            }
                            else if (filtersObj.rules.ElementAt(i).field == "PartsCategory")
                            {
                                filtersObj.rules.ElementAt(i).field = "pc.Description";
                            }
                            else if (filtersObj.rules.ElementAt(i).field == "PartsFunctionGroup")
                            {
                                filtersObj.rules.ElementAt(i).field = "f.FunctionGroup_Name";
                            }
                            else if (filtersObj.rules.ElementAt(i).field == "UnitOfMeasurement")
                            {
                                filtersObj.rules.ElementAt(i).field = "r.RefMasterDetail_Name";
                            }

                            else if (filtersObj.rules.ElementAt(i).field == "IsComponent")
                            {
                                filtersObj.rules.ElementAt(i).field = "p.Parts_IsComponent";
                                filtersObj.rules.ElementAt(i).data = (filtersObj.rules.ElementAt(i).data.ToUpper() == "YES") ? "1" : "0";
                            }
                            else if (filtersObj.rules.ElementAt(i).field == "IsActive")
                            {
                                filtersObj.rules.ElementAt(i).field = "p.Parts_IsActive";
                                filtersObj.rules.ElementAt(i).data = (filtersObj.rules.ElementAt(i).data.ToUpper() == "YES") ? "1" : "0";
                            }


                            wherecondition = wherecondition + " AND " + filtersObj.rules.ElementAt(i).field + " like " + "'%" + filtersObj.rules.ElementAt(i).data + "%'";
                        }
                    }

                } //Advance Search
                if (advnce)
                {
                    string op = "";
                    AdvanceFilter advnfilter = JObject.Parse(Common.DecryptString(Query)).ToObject<AdvanceFilter>();

                    if (advnfilter.rules.Count() > 0)
                    {
                        for (int i = 0; i < advnfilter.rules.Count(); i++)
                        {
                            if (advnfilter.rules.ElementAt(i).Field == "CompName")
                            {
                                advnfilter.rules.ElementAt(i).Field = "c.Company_Name";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "Parts_PartsPrefix")
                            {
                                advnfilter.rules.ElementAt(i).Field = "Parts_PartPrefix";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "PartsDescription")
                            {
                                advnfilter.rules.ElementAt(i).Field = "Parts_PartsDescription";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "PartsCategory")
                            {
                                advnfilter.rules.ElementAt(i).Field = "pc.Description";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "PartsFunctionGroup")
                            {
                                advnfilter.rules.ElementAt(i).Field = "f.FunctionGroup_Name";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "UnitOfMeasurement")
                            {
                                advnfilter.rules.ElementAt(i).Field = "r.RefMasterDetail_Name";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "IsComponent")
                            {
                                advnfilter.rules.ElementAt(i).Field = "p.Parts_IsComponent";
                                advnfilter.rules.ElementAt(i).Data = (advnfilter.rules.ElementAt(i).Data.ToUpper() == "YES") ? "1" : "0";
                            }
                            else if (advnfilter.rules.ElementAt(i).Field == "IsActive")
                            {
                                advnfilter.rules.ElementAt(i).Field = "p.Parts_IsActive";
                                advnfilter.rules.ElementAt(i).Data = (advnfilter.rules.ElementAt(i).Data.ToUpper() == "YES") ? "1" : "0";
                            }


                            op = Common.getoperator(advnfilter.rules.ElementAt(i).Operator);

                            if (i == 0)
                            {
                                if (op == "like")
                                {
                                    wherecondition = wherecondition + " AND " + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + advnfilter.rules.ElementAt(i).Data + "%'";
                                }
                                else
                                {
                                    wherecondition = wherecondition + " AND " + advnfilter.rules.ElementAt(i).Field + " " + op + " '" + advnfilter.rules.ElementAt(i).Data + "'";
                                }
                            }
                            else
                            {
                                if (op == "like")
                                {
                                    wherecondition = wherecondition + " AND " + advnfilter.rules.ElementAt(i).Field + " " + op + " '%" + advnfilter.rules.ElementAt(i).Data + "%'";
                                }
                                else
                                {
                                    wherecondition = wherecondition + " " + advnfilter.rules.ElementAt(i).Condition + " " + advnfilter.rules.ElementAt(i).Field + " " + op + " '" + advnfilter.rules.ElementAt(i).Data + "'";
                                }
                            }
                        }
                    }
                }

                Query = " select * into #orp from (SELECT p.Parts_ID,Parts_PartPrefix,Parts_PartsNumber,Parts_PartsDescription,MovementType_ID,p.PartsCategory_ID,AttachmentCount,";
                Query = Query + " PartsFunctionGroup_ID,Parts_IsActive,p.UnitOfMeasurement_ID,p.Company_ID,Parts_IsLocal,Parts_IsComponent";
                Query = Query + " FROM GNM_Parts p join GNM_Company c on c.Company_ID=p.Company_ID left outer join PRM_PartsCategory pc on p.PartsCategory_ID=pc.PartsCategory_ID";
                Query = Query + " left outer join GNM_FunctionGroup f on p.PartsFunctionGroup_ID=f.FunctionGroup_ID left outer join GNM_RefMasterDetail r on p.UnitOfMeasurement_ID=r.RefMasterDetail_ID";
                Query = Query + " where p.Company_ID  =" + CompanyID + wherecondition;

                if (ParentCompanyDetails.Count > 0)
                {
                    Query = Query + " union select p.Parts_ID,Parts_PartPrefix,Parts_PartsNumber,Parts_PartsDescription,MovementType_ID,p.PartsCategory_ID,AttachmentCount,";
                    Query = Query + " PartsFunctionGroup_ID,Parts_IsActive,p.UnitOfMeasurement_ID,";
                    Query = Query + " p.Company_ID,Parts_IsLocal,Parts_IsComponent FROM GNM_Parts p join GNM_Company c on c.Company_ID=p.Company_ID";
                    Query = Query + " left outer join PRM_PartsCategory pc on p.PartsCategory_ID=pc.PartsCategory_ID left outer join GNM_FunctionGroup f on p.PartsFunctionGroup_ID=f.FunctionGroup_ID";
                    Query = Query + " left outer join GNM_RefMasterDetail r on p.UnitOfMeasurement_ID=r.RefMasterDetail_ID where  p.Company_ID in (" + ParentCompanyIDs + ") AND Parts_IsLocal=0" + wherecondition;
                }
                Query = Query + ") a";


                Query = Query + " ;with NewT as(select *,ROW_NUMBER( ) over(order by Parts_ID) rownumber from  #orp )";
                countquery = Query + " select count(Parts_ID) from #orp";
                //IEnumerable<int> rcount = PartsClient.Database.SqlQuery(typeof(int), countquery).Cast<int>();

                List<int> rcount = new List<int>();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    using (SqlCommand command = new SqlCommand(countquery, connection))
                    {
                        connection.Open();

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                // Assuming the query returns a single integer value per row
                                if (!reader.IsDBNull(0)) // Check for DBNull
                                {
                                    rcount.Add(reader.GetInt32(0)); // Reads the integer value from the first column
                                }
                            }
                        }
                    }
                }
                IEnumerable<int> result = rcount;
                count = rcount.ElementAt(0);
                //if (count < (rows * page) && count != 0)
                //{
                //    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                //}

                Query = Query + " SELECT * FROM NewT";// WHERE rownumber BETWEEN " + (((page - 1) * (rows)) + 1) + " AND " + (page * rows);
                Query = Query + " drop table #orp";

                //liParts = PartsClient.Database.SqlQuery(typeof(GNMPartsQuery), Query).Cast<GNMPartsQuery>().ToList();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    using (SqlCommand command = new SqlCommand(Query, connection))
                    {
                        connection.Open();

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                GNMPartsQuery part = new GNMPartsQuery
                                {
                                    Parts_ID = reader["Parts_ID"] != DBNull.Value ? (int)reader["Parts_ID"] : 0,
                                    Parts_PartPrefix = reader["Parts_PartPrefix"] != DBNull.Value ? reader["Parts_PartPrefix"].ToString() : null,
                                    Parts_PartsNumber = reader["Parts_PartsNumber"] != DBNull.Value ? reader["Parts_PartsNumber"].ToString() : null,
                                    Parts_PartsDescription = reader["Parts_PartsDescription"] != DBNull.Value ? reader["Parts_PartsDescription"].ToString() : null,
                                    MovementType_ID = reader["MovementType_ID"] != DBNull.Value ? (int?)reader["MovementType_ID"] : null,
                                    PartsCategory_ID = reader["PartsCategory_ID"] != DBNull.Value ? (int?)reader["PartsCategory_ID"] : null,
                                    PartsFunctionGroup_ID = reader["PartsFunctionGroup_ID"] != DBNull.Value ? (int?)reader["PartsFunctionGroup_ID"] : null,
                                    Parts_IsActive = reader["Parts_IsActive"] != DBNull.Value ? (bool)reader["Parts_IsActive"] : false,
                                    Company_ID = reader["Company_ID"] != DBNull.Value ? (int)reader["Company_ID"] : 0,
                                    Parts_IsLocal = reader["Parts_IsLocal"] != DBNull.Value ? (bool)reader["Parts_IsLocal"] : false,
                                    Parts_IsComponent = reader["Parts_IsComponent"] != DBNull.Value ? (bool)reader["Parts_IsComponent"] : false,
                                    UnitOfMeasurement_ID = reader["UnitOfMeasurement_ID"] != DBNull.Value ? (int?)reader["UnitOfMeasurement_ID"] : null,
                                    PartsDisposal_ID = reader["PartsDisposal_ID"] != DBNull.Value ? (int?)reader["PartsDisposal_ID"] : null,
                                    Parts_AliasPartPrefix = reader["Parts_AliasPartPrefix"] != DBNull.Value ? reader["Parts_AliasPartPrefix"].ToString() : null,
                                    Parts_AliasPartNumber = reader["Parts_AliasPartNumber"] != DBNull.Value ? reader["Parts_AliasPartNumber"].ToString() : null,
                                    PartType = reader["PartType"] != DBNull.Value ? (int?)reader["PartType"] : null,
                                    SalvagePart_ID = reader["SalvagePart_ID"] != DBNull.Value ? (int?)reader["SalvagePart_ID"] : null,
                                    Parts_Weight = reader["Parts_Weight"] != DBNull.Value ? (decimal?)reader["Parts_Weight"] : null,
                                    Parts_Dimensions = reader["Parts_Dimensions"] != DBNull.Value ? reader["Parts_Dimensions"].ToString() : null,
                                    ExciseDuty_ID = reader["ExciseDuty_ID"] != DBNull.Value ? (int?)reader["ExciseDuty_ID"] : null,
                                    Parts_IsHazardousGood = reader["Parts_IsHazardousGood"] != DBNull.Value ? (bool)reader["Parts_IsHazardousGood"] : false,
                                    IsKitPart = reader["IsKitPart"] != DBNull.Value ? (bool)reader["IsKitPart"] : false,
                                    Attachmentcount = reader["Attachmentcount"] != DBNull.Value ? (byte?)reader["Attachmentcount"] : null,
                                    Attachmentco = reader["Attachmentco"] != DBNull.Value ? reader["Attachmentco"].ToString() : null
                                };

                                liParts.Add(part);
                            }
                        }
                    }
                }

                if (Obj.PartsIDList != null)
                {
                    var PartsIDList = "";
                    PartsIDList = Obj.PartsIDList.ToString();
                    PartsIDList = PartsIDList.TrimEnd(new char[] { ',' });
                    PartsIDQuery = " SELECT p.Parts_ID,Parts_PartPrefix,Parts_PartsNumber,Parts_PartsDescription,MovementType_ID,p.PartsCategory_ID,AttachmentCount,";
                    PartsIDQuery = PartsIDQuery + " PartsFunctionGroup_ID,Parts_IsActive,p.UnitOfMeasurement_ID,p.Company_ID,Parts_IsLocal,Parts_IsComponent";
                    PartsIDQuery = PartsIDQuery + " FROM GNM_Parts p join GNM_Company c on c.Company_ID=p.Company_ID left outer join PRM_PartsCategory pc on p.PartsCategory_ID=pc.PartsCategory_ID";
                    PartsIDQuery = PartsIDQuery + " left outer join GNM_FunctionGroup f on p.PartsFunctionGroup_ID=f.FunctionGroup_ID left outer join GNM_RefMasterDetail r on p.UnitOfMeasurement_ID=r.RefMasterDetail_ID";
                    PartsIDQuery = PartsIDQuery + " where p.Parts_ID in (" + PartsIDList + ")" + wherecondition + "";
                    //liParts = PartsClient.Database.SqlQuery(typeof(GNMPartsQuery), PartsIDQuery).Cast<GNMPartsQuery>().ToList();
                    using (SqlConnection connection = new SqlConnection(constring))
                    {
                        using (SqlCommand command = new SqlCommand(PartsIDQuery, connection))
                        {
                            connection.Open();
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    GNMPartsQuery part = new GNMPartsQuery
                                    {
                                        Parts_ID = reader["Parts_ID"] != DBNull.Value ? (int)reader["Parts_ID"] : 0,
                                        Parts_PartPrefix = reader["Parts_PartPrefix"] != DBNull.Value ? reader["Parts_PartPrefix"].ToString() : null,
                                        Parts_PartsNumber = reader["Parts_PartsNumber"] != DBNull.Value ? reader["Parts_PartsNumber"].ToString() : null,
                                        Parts_PartsDescription = reader["Parts_PartsDescription"] != DBNull.Value ? reader["Parts_PartsDescription"].ToString() : null,
                                        MovementType_ID = reader["MovementType_ID"] != DBNull.Value ? (int?)reader["MovementType_ID"] : null,
                                        PartsCategory_ID = reader["PartsCategory_ID"] != DBNull.Value ? (int?)reader["PartsCategory_ID"] : null,
                                        PartsFunctionGroup_ID = reader["PartsFunctionGroup_ID"] != DBNull.Value ? (int?)reader["PartsFunctionGroup_ID"] : null,
                                        Parts_IsActive = reader["Parts_IsActive"] != DBNull.Value ? (bool)reader["Parts_IsActive"] : false,
                                        Company_ID = reader["Company_ID"] != DBNull.Value ? (int)reader["Company_ID"] : 0,
                                        Parts_IsLocal = reader["Parts_IsLocal"] != DBNull.Value ? (bool)reader["Parts_IsLocal"] : false,
                                        Parts_IsComponent = reader["Parts_IsComponent"] != DBNull.Value ? (bool)reader["Parts_IsComponent"] : false,
                                        UnitOfMeasurement_ID = reader["UnitOfMeasurement_ID"] != DBNull.Value ? (int?)reader["UnitOfMeasurement_ID"] : null,
                                        PartsDisposal_ID = reader["PartsDisposal_ID"] != DBNull.Value ? (int?)reader["PartsDisposal_ID"] : null,
                                        Parts_AliasPartPrefix = reader["Parts_AliasPartPrefix"] != DBNull.Value ? reader["Parts_AliasPartPrefix"].ToString() : null,
                                        Parts_AliasPartNumber = reader["Parts_AliasPartNumber"] != DBNull.Value ? reader["Parts_AliasPartNumber"].ToString() : null,
                                        PartType = reader["PartType"] != DBNull.Value ? (int?)reader["PartType"] : null,
                                        SalvagePart_ID = reader["SalvagePart_ID"] != DBNull.Value ? (int?)reader["SalvagePart_ID"] : null,
                                        Parts_Weight = reader["Parts_Weight"] != DBNull.Value ? (decimal?)reader["Parts_Weight"] : null,
                                        Parts_Dimensions = reader["Parts_Dimensions"] != DBNull.Value ? reader["Parts_Dimensions"].ToString() : null,
                                        ExciseDuty_ID = reader["ExciseDuty_ID"] != DBNull.Value ? (int?)reader["ExciseDuty_ID"] : null,
                                        Parts_IsHazardousGood = reader["Parts_IsHazardousGood"] != DBNull.Value ? (bool)reader["Parts_IsHazardousGood"] : false,
                                        IsKitPart = reader["IsKitPart"] != DBNull.Value ? (bool)reader["IsKitPart"] : false,
                                        Attachmentcount = reader["Attachmentcount"] != DBNull.Value ? (byte?)reader["Attachmentcount"] : null,
                                        Attachmentco = reader["Attachmentco"] != DBNull.Value ? reader["Attachmentco"].ToString() : null
                                    };

                                    liParts.Add(part);
                                }
                            }
                        }
                    }
                    count = liParts.Count;
                }
                else
                {
                    //liParts = PartsClient.Database.SqlQuery(typeof(GNMPartsQuery), Query).Cast<GNMPartsQuery>().ToList();
                    using (SqlConnection connection = new SqlConnection(constring))
                    {
                        using (SqlCommand command = new SqlCommand(Query, connection))
                        {
                            connection.Open();
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    GNMPartsQuery part = new GNMPartsQuery
                                    {
                                        Parts_ID = reader["Parts_ID"] != DBNull.Value ? (int)reader["Parts_ID"] : 0,
                                        Parts_PartPrefix = reader["Parts_PartPrefix"] != DBNull.Value ? reader["Parts_PartPrefix"].ToString() : null,
                                        Parts_PartsNumber = reader["Parts_PartsNumber"] != DBNull.Value ? reader["Parts_PartsNumber"].ToString() : null,
                                        Parts_PartsDescription = reader["Parts_PartsDescription"] != DBNull.Value ? reader["Parts_PartsDescription"].ToString() : null,
                                        MovementType_ID = reader["MovementType_ID"] != DBNull.Value ? (int?)reader["MovementType_ID"] : null,
                                        PartsCategory_ID = reader["PartsCategory_ID"] != DBNull.Value ? (int?)reader["PartsCategory_ID"] : null,
                                        PartsFunctionGroup_ID = reader["PartsFunctionGroup_ID"] != DBNull.Value ? (int?)reader["PartsFunctionGroup_ID"] : null,
                                        Parts_IsActive = reader["Parts_IsActive"] != DBNull.Value ? (bool)reader["Parts_IsActive"] : false,
                                        Company_ID = reader["Company_ID"] != DBNull.Value ? (int)reader["Company_ID"] : 0,
                                        Parts_IsLocal = reader["Parts_IsLocal"] != DBNull.Value ? (bool)reader["Parts_IsLocal"] : false,
                                        Parts_IsComponent = reader["Parts_IsComponent"] != DBNull.Value ? (bool)reader["Parts_IsComponent"] : false,
                                        UnitOfMeasurement_ID = reader["UnitOfMeasurement_ID"] != DBNull.Value ? (int?)reader["UnitOfMeasurement_ID"] : null,
                                        PartsDisposal_ID = reader["PartsDisposal_ID"] != DBNull.Value ? (int?)reader["PartsDisposal_ID"] : null,
                                        Parts_AliasPartPrefix = reader["Parts_AliasPartPrefix"] != DBNull.Value ? reader["Parts_AliasPartPrefix"].ToString() : null,
                                        Parts_AliasPartNumber = reader["Parts_AliasPartNumber"] != DBNull.Value ? reader["Parts_AliasPartNumber"].ToString() : null,
                                        PartType = reader["PartType"] != DBNull.Value ? (int?)reader["PartType"] : null,
                                        SalvagePart_ID = reader["SalvagePart_ID"] != DBNull.Value ? (int?)reader["SalvagePart_ID"] : null,
                                        Parts_Weight = reader["Parts_Weight"] != DBNull.Value ? (decimal?)reader["Parts_Weight"] : null,
                                        Parts_Dimensions = reader["Parts_Dimensions"] != DBNull.Value ? reader["Parts_Dimensions"].ToString() : null,
                                        ExciseDuty_ID = reader["ExciseDuty_ID"] != DBNull.Value ? (int?)reader["ExciseDuty_ID"] : null,
                                        Parts_IsHazardousGood = reader["Parts_IsHazardousGood"] != DBNull.Value ? (bool)reader["Parts_IsHazardousGood"] : false,
                                        IsKitPart = reader["IsKitPart"] != DBNull.Value ? (bool)reader["IsKitPart"] : false,
                                        Attachmentcount = reader["Attachmentcount"] != DBNull.Value ? (byte?)reader["Attachmentcount"] : null,
                                        Attachmentco = reader["Attachmentco"] != DBNull.Value ? reader["Attachmentco"].ToString() : null
                                    };

                                    liParts.Add(part);
                                }
                            }
                        }
                    }
                }

                string queryList = "SELECT * FROM GNM_RefMasterDetail;";
                List<GNM_RefMasterDetail> refMasterDetailList = new List<GNM_RefMasterDetail>();


                using (SqlConnection connection = new SqlConnection(constring))
                {

                    SqlCommand command = new SqlCommand(queryList, connection);


                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        while (reader.Read())
                        {
                            GNM_RefMasterDetail refMasterDetail = new GNM_RefMasterDetail
                            {
                                RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                RefMasterDetail_IsActive = reader.GetBoolean(reader.GetOrdinal("RefMasterDetail_IsActive")),
                                RefMasterDetail_Short_Name = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_Short_Name")) ? null : reader.GetString(reader.GetOrdinal("RefMasterDetail_Short_Name")),
                                RefMasterDetail_Name = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_Name")) ? null : reader.GetString(reader.GetOrdinal("RefMasterDetail_Name")),
                                Company_ID = reader.IsDBNull(reader.GetOrdinal("Company_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                ModifiedBy = reader.GetInt32(reader.GetOrdinal("ModifiedBy")),
                                ModifiedDate = reader.GetDateTime(reader.GetOrdinal("ModifiedDate")),
                                RefMaster_ID = reader.GetInt32(reader.GetOrdinal("RefMaster_ID")),
                                RefMasterDetail_IsDefault = reader.GetBoolean(reader.GetOrdinal("RefMasterDetail_IsDefault")),
                                Region_ID = reader.IsDBNull(reader.GetOrdinal("Region_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Region_ID")),
                                SystemCondition = reader.IsDBNull(reader.GetOrdinal("SystemCondition")) ? null : reader.GetString(reader.GetOrdinal("SystemCondition")),
                            };

                            refMasterDetailList.Add(refMasterDetail);
                        }
                        reader.Close();
                    }
                }
                string queryCompany = "SELECT * FROM GNM_Company;";
                List<GNM_Company> CompanyList = new List<GNM_Company>();


                using (SqlConnection connection = new SqlConnection(constring))
                {

                    SqlCommand command = new SqlCommand(queryCompany, connection);


                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        while (reader.Read())
                        {
                            GNM_Company company = new GNM_Company
                            {
                                Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                Company_Name = reader.IsDBNull(reader.GetOrdinal("Company_Name")) ? null : reader.GetString(reader.GetOrdinal("Company_Name")),
                                Company_ShortName = reader.IsDBNull(reader.GetOrdinal("Company_ShortName")) ? null : reader.GetString(reader.GetOrdinal("Company_ShortName")),
                                Currency_ID = reader.GetInt32(reader.GetOrdinal("Currency_ID")),
                                Company_Address = reader.IsDBNull(reader.GetOrdinal("Company_Address")) ? null : reader.GetString(reader.GetOrdinal("Company_Address")),
                                Company_Type = reader.IsDBNull(reader.GetOrdinal("Company_Type")) ? null : reader.GetString(reader.GetOrdinal("Company_Type")),
                                Company_Active = reader.GetBoolean(reader.GetOrdinal("Company_Active")),
                                Company_LogoName = reader.IsDBNull(reader.GetOrdinal("Company_LogoName")) ? null : reader.GetString(reader.GetOrdinal("Company_LogoName")),
                                Company_Parent_ID = reader.IsDBNull(reader.GetOrdinal("Company_Parent_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Company_Parent_ID")),
                                Remarks = reader.IsDBNull(reader.GetOrdinal("Remarks")) ? null : reader.GetString(reader.GetOrdinal("Remarks")),
                                DefaultGridSize = reader.GetByte(reader.GetOrdinal("DefaultGridSize")),
                                JobCardCushionHours = reader.IsDBNull(reader.GetOrdinal("JobCardCushionHours")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("JobCardCushionHours")),
                                ModifiedBy = reader.GetInt32(reader.GetOrdinal("ModifiedBy")),
                                ModifiedDate = reader.GetDateTime(reader.GetOrdinal("ModifiedDate")),
                                CompanyTheme_ID = reader.IsDBNull(reader.GetOrdinal("CompanyTheme_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("CompanyTheme_ID")),
                                QuotationValidity = reader.IsDBNull(reader.GetOrdinal("QuotationValidity")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("QuotationValidity")),
                                CompanyFont = reader.IsDBNull(reader.GetOrdinal("CompanyFont")) ? null : reader.GetString(reader.GetOrdinal("CompanyFont")),
                                InventoryCarryingFactoy_Percentage = reader.IsDBNull(reader.GetOrdinal("InventoryCarryingFactoy_Percentage")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("InventoryCarryingFactoy_Percentage")),
                                OrderingCost = reader.IsDBNull(reader.GetOrdinal("OrderingCost")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("OrderingCost")),
                                GNM_Company_Company_Relation = new HashSet<GNM_Company_Company_Relation>(), // Initialize empty collections
                                GNM_Company_Company_Relation1 = new HashSet<GNM_Company_Company_Relation>(),
                                GNM_CompanyBrands = new HashSet<GNM_CompanyBrands>(),
                                GNM_CompanyLocale = new HashSet<GNM_CompanyLocale>(),
                                GNM_Branch = new HashSet<GNM_Branch>(),
                                GNM_CompanyFinancialYear = new HashSet<GNM_CompanyFinancialYear>(),
                                GNM_HEADERFOOTERPRINT = new HashSet<GNM_HEADERFOOTERPRINT>(),
                                GNM_TERMSANDCONDITIONS = new HashSet<GNM_TERMSANDCONDITIONS>(),
                                GNM_HourlyRate = new HashSet<GNM_HourlyRate>(),
                                GNM_CompanyEmployee = new HashSet<GNM_CompanyEmployee>()
                            };

                            CompanyList.Add(company);
                        }
                        reader.Close();
                    }
                }

                string queryfunctiongroup = "SELECT * GNM_FunctionGroup;";
                List<GNM_FunctionGroup> FunctionGroupList = new List<GNM_FunctionGroup>();


                using (SqlConnection connection = new SqlConnection(constring))
                {

                    SqlCommand command = new SqlCommand(queryfunctiongroup, connection);


                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        while (reader.Read())
                        {
                            GNM_FunctionGroup functionGroup = new GNM_FunctionGroup
                            {
                                FunctionGroup_ID = reader.GetInt32(reader.GetOrdinal("FunctionGroup_ID")),
                                Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                FunctionGroup_Name = reader.IsDBNull(reader.GetOrdinal("FunctionGroup_Name")) ? null : reader.GetString(reader.GetOrdinal("FunctionGroup_Name")),
                                Brand_ID = reader.IsDBNull(reader.GetOrdinal("Brand_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Brand_ID")),
                                FunctionGroup_IsActive = reader.GetBoolean(reader.GetOrdinal("FunctionGroup_IsActive")),
                                ModifiedBy = reader.GetInt32(reader.GetOrdinal("ModifiedBy")),
                                ModifiedDate = reader.GetDateTime(reader.GetOrdinal("ModifiedDate")),
                            };

                            FunctionGroupList.Add(functionGroup);
                        }
                        reader.Close();
                    }
                }

                //Session["PartsExport"] = wherecondition; //Storing Where Condition
                //var arrParts = from a in liParts
                //               join b in GeneralClient.PRM_CorePartsCategory on a.PartsCategory_ID equals b.PartsCategory_ID into lojPC
                //               from Categ in lojPC.DefaultIfEmpty(new PRM_CorePartsCategory { PartsCategory_ID = 0, Description = "" })
                //               join c in FunctionGroupList on a.PartsFunctionGroup_ID equals c.FunctionGroup_ID into Part
                //               from parts in Part.DefaultIfEmpty(new GNM_FunctionGroup { FunctionGroup_ID = 0, FunctionGroup_Name = "" })
                //               join d in refMasterDetailList on a.UnitOfMeasurement_ID equals d.RefMasterDetail_ID
                //               join j in CompanyList on a.Company_ID equals j.Company_ID
                //               select new GNMParts
                //               {
                //                   Parts_ID = a.Parts_ID,
                //                   Comp_ID = Obj.Company_ID,
                //                   CompName = j.Company_Name,
                //                   Parts_PartsPrefix = a.Parts_PartPrefix,
                //                   Parts_PartsNumber = a.Parts_PartsNumber,
                //                   PartsDescription = a.Parts_PartsDescription,
                //                   PartsCategoryID = Categ.PartsCategory_ID,
                //                   PartsCategory = Categ.Description,
                //                   PartsFunctionGroupID = parts.FunctionGroup_ID,
                //                   PartsFunctionGroup = parts.FunctionGroup_Name,
                //                   UnitOfMeasurementID = d.RefMasterDetail_ID,
                //                   UnitOfMeasurement = d.RefMasterDetail_Name,
                //                   IsActive = a.Parts_IsActive == true ? "Yes" : "No",
                //                   IsComponent = a.Parts_IsComponent == true ? "Yes" : "No",
                //                   Attachmentcount = a.Attachmentcount
                //               };

                IQueryable<GNMParts> arrParts = null;
                iQPrty = arrParts.AsQueryable<GNMParts>();
                //Sorting 
                iQPrty = iQPrty.OrderByField<GNMParts>(sidx, sord);
                //  Session["PartsExport"] = iQPrty;

                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }
                x = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = (from a in iQPrty
                            select new
                            {
                                //edit = "<img id='" + a.Parts_ID + "' src='" + AppPath + "/Content/edit.gif' key='" + a.Parts_ID + "' class='editPartsMaster' Comp_ID='" + a.Comp_ID + "' />",
                                edit = "<a title=" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "view").ToString() + " href='#' style='font-size: 13px;' id='" + a.Parts_ID + "' key='" + a.Parts_ID + "' class='editPartsMaster' Comp_ID='" + a.Comp_ID + "'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input type='checkbox' key='" + a.Parts_ID + "' id='chk" + a.Parts_ID + "'  " + ((a.Comp_ID == CompanyID) ? "class='chkDelParts'" : "class='CannotchkDelParts'") + "/>",
                                Comp_ID = a.Comp_ID,
                                CompName = a.CompName,
                                Parts_ID = a.Parts_ID,
                                Parts_PartsPrefix = (a.Parts_PartsPrefix),
                                Parts_PartsNumber = (a.Parts_PartsNumber),
                                PartsDescription = (a.PartsDescription),
                                PartsCategory = a.PartsCategory,
                                PartsFunctionGroup = a.PartsFunctionGroup,
                                UnitOfMeasurement = a.UnitOfMeasurement,
                                IsActive = a.IsActive,
                                IsComponent = a.IsComponent,
                                //Local = "<img key='" + a.Parts_ID + "' src='" + AppPath + "/Content/local.png' class='PartsLocale' width='20' height='20' alt='Localize' title='Localize' Comp_ID='" + a.Comp_ID + "' />",
                                Local = "<a title='Localize' href='#' style='font-size: 13px;' key='" + a.Parts_ID + "'class='PartsLocale' width='20' height='20' alt='Localize' title='Localize' Comp_ID='" + a.Comp_ID + "'><i class='fa fa-globe'></i></a>",
                                //Attachmentcount = Convert.ToInt32(a.Attachmentcount) > 0 ? "<img id='" + a.Parts_ID + "' alt='Attachment' src='" + AppPath + "/Images/Attchmnt1616.png' />(" + a.Attachmentcount.ToString() + ")" : "",
                                Attachmentcount = Convert.ToInt32(a.Attachmentcount) > 0 ? "<a title='Attachment' href='#' style='font-size: 13px;' id='" + a.Parts_ID + "'><i class='fa fa-paperclip'></i></a>(" + a.Attachmentcount.ToString() + ")" : "",

                            }).ToList().Paginate(page, rows)
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(x);
        }



        #endregion




        #region::: InsertPartsPrice /Mithun:::
        /// <summary>
        /// Insert Parts Price
        /// </summary>
        /// <param name="Constring"></param>
        /// <param name="InsertPartsPriceObj"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult InsertPartsPrice(string Constring, InsertPartsPriceList InsertPartsPriceObj, int LogException)
        {

            JTokenReader jr = null;
            int Company_ID = Convert.ToInt32(InsertPartsPriceObj.Company_ID);
            var Details = default(dynamic);
            Dictionary<string, object> columnValues = new Dictionary<string, object>();
            bool allRowsValid = true;


            try
            {
                JObject jObj = JObject.Parse(InsertPartsPriceObj.key);
                int ContPersrowcount = jObj["rows"].Count();
                int? CustomerWarranty = null;
                List<bool> rowValidationResults = new List<bool>();

                using (SqlConnection conn = new SqlConnection(Constring))
                {
                    conn.Open();
                    using (var transaction = conn.BeginTransaction())
                    {
                        for (int i = 0; i < ContPersrowcount; i++)
                        {
                            JObject row = jObj["rows"].ElementAt(i).ToObject<JObject>();
                            row["Company_ID"] = Convert.ToInt32(InsertPartsPriceObj.Company_ID);
                            var columns = new Dictionary<string, bool>
                            {
                                { "Company_ID", true },
                                { "Part_ID", true },
                                { "Price", true },
                                { "MRP", true },
                                { "EDate", true },
                                { "Currency", true },
                                { "CustomerWarranty", false },
                                { "FCP", false },
                            };
                            List<string> invalidColumns;
                            bool isRowValid = Common.ValidateAndLog(row, columns, out invalidColumns);
                            if (!isRowValid)
                            {
                                allRowsValid = false;
                                break;
                            }
                            rowValidationResults.Add(isRowValid);
                        }
                        if (allRowsValid)
                        {
                            // Process valid rows
                            for (int i = 0; i < ContPersrowcount; i++)
                            {


                                jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["PriceKey"]);
                                jr.Read();
                                string PriceKey = jr.Value.ToString();

                                jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Part_ID"]);
                                jr.Read();
                                int Parts_ID = Convert.ToInt32(jr.Value.ToString());

                                jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Price"]);
                                jr.Read();
                                decimal ListPrice = Convert.ToDecimal(jr.Value.ToString());

                                jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["FCP"]);
                                jr.Read();
                                decimal? FCP = null;
                                if (jr.Value.ToString().Trim() != "")
                                {
                                    FCP = Convert.ToDecimal(jr.Value.ToString());
                                }

                                jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["MRP"]);
                                jr.Read();
                                decimal MRP = Convert.ToDecimal(jr.Value.ToString());

                                jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["EDate"]);
                                jr.Read();
                                DateTime PartsPriceDetail_EffectiveFrom = Convert.ToDateTime(jr.Value.ToString());

                                jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["CustomerWarranty"]);
                                jr.Read();
                                if (jr.Value.ToString() != "")
                                {
                                    CustomerWarranty = Convert.ToInt32(jr.Value.ToString());
                                }

                                jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Currency"]);
                                jr.Read();
                                int Currency = Convert.ToInt32(jr.Value.ToString());




                                if (PriceKey == "")
                                {
                                    // Call stored procedure for inserting
                                    using (SqlCommand cmd = new SqlCommand("UP_INS_AMERP_InsertPartsPriceDetail", conn, transaction))
                                    {
                                        cmd.CommandType = CommandType.StoredProcedure;
                                        cmd.Parameters.AddWithValue("@Parts_ID", Parts_ID);
                                        cmd.Parameters.AddWithValue("@ListPrice", ListPrice);
                                        cmd.Parameters.AddWithValue("@FCP", (object)FCP ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@MRP", MRP);
                                        cmd.Parameters.AddWithValue("@EffectiveFrom", PartsPriceDetail_EffectiveFrom);
                                        cmd.Parameters.AddWithValue("@CustomerWarranty", (object)CustomerWarranty ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                                        cmd.Parameters.AddWithValue("@Currency_ID", Currency);

                                        cmd.ExecuteNonQuery();
                                    }
                                    Details = new
                                    {
                                        Responsee = 1,
                                    };
                                }
                                else
                                {
                                    int PartsPriceId = Convert.ToInt32(PriceKey);

                                    // Call stored procedure for updating
                                    using (SqlCommand cmd = new SqlCommand("UP_UPD_AMERP_UpdatePartsPriceDetail", conn, transaction))
                                    {
                                        cmd.CommandType = CommandType.StoredProcedure;
                                        cmd.Parameters.AddWithValue("@PartsPriceDetail_ID", PartsPriceId);
                                        cmd.Parameters.AddWithValue("@ListPrice", ListPrice);
                                        cmd.Parameters.AddWithValue("@FCP", (object)FCP ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@MRP", MRP);
                                        cmd.Parameters.AddWithValue("@EffectiveFrom", PartsPriceDetail_EffectiveFrom);
                                        cmd.Parameters.AddWithValue("@CustomerWarranty", (object)CustomerWarranty ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@Currency_ID", Currency);

                                        cmd.ExecuteNonQuery();
                                    }
                                    Details = new
                                    {
                                        Responsee = 2,
                                    };
                                }

                                //gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"]), Convert.ToInt32(Session["Branch"]), Convert.ToInt32(Session["User_ID"]), Convert.ToInt32(Common.GetObjectID("CorePartsMaster")), Parts_ID, 0, 0, "Update", false, Convert.ToInt32(Session["MenuID"]));
                            }
                        }
                        else
                        {
                            transaction.Rollback();
                            Details = new
                            {
                                Responsee = 0,
                            };
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }

            return new JsonResult(Details);
        }

        #endregion

        #region::: SelectPartsProduct /Mithun:::
        /// <summary>
        /// To select Parts Product
        /// </summary>
        /// <param name="SelectPartsProductObj"></param>
        /// <param name="sidx"></param>
        /// <param name="rows"></param>
        /// <param name="id"></param>
        /// <param name="page"></param>
        /// <param name="sord"></param>
        /// <param name="_search"></param>
        /// <param name="nd"></param>
        /// <param name="filters"></param>
        /// <param name="constring"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult SelectPartsProduct(SelectPartsProductList SelectPartsProductObj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, string constring, int LogException)
        {
            int count = 0;
            int total = 0;
            var PartProductDet = new object();
            IQueryable<PartsProduct> IQPartProduct = null;
            var User = default(dynamic);
            try
            {
                //GNM_User User = (GNM_User)Session["UserDetails"];
                //User = SelectPartsProductObj.userDeatails;
                // Execute SQL query for PartsProductAssociation directly (as shown in previous responses)
                List<GNM_PartsProductAssociation> liPartProduct = new List<GNM_PartsProductAssociation>();
                List<GNM_RefMasterDetail> BrandData = new List<GNM_RefMasterDetail>();
                List<ProductType> ProductTypeData = new List<ProductType>();

                // Assuming you have a SqlConnection configured
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    string sqlQuery = @"
                        SELECT *
                        FROM GNM_PartsProductAssociation
                        WHERE Parts_ID = @Parts_ID
                            AND Company_ID = @Company_ID;
                    ";

                    SqlCommand command = new SqlCommand(sqlQuery, connection);
                    command.Parameters.AddWithValue("@Parts_ID", SelectPartsProductObj.id);
                    command.Parameters.AddWithValue("@Company_ID", SelectPartsProductObj.Company_ID);

                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        GNM_PartsProductAssociation partProductAssociation = new GNM_PartsProductAssociation();

                        // Map SqlDataReader fields to your GNM_PartsProductAssociation object properties
                        partProductAssociation.PartsProductAssociation_ID = reader.GetInt32(reader.GetOrdinal("PartsProductAssociation_ID"));
                        partProductAssociation.Parts_ID = reader.GetInt32(reader.GetOrdinal("Parts_ID"));
                        partProductAssociation.Brand_ID = reader.GetInt32(reader.GetOrdinal("Brand_ID"));
                        partProductAssociation.ProductType_ID = reader.IsDBNull(reader.GetOrdinal("ProductType_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("ProductType_ID"));
                        partProductAssociation.Model_ID = reader.IsDBNull(reader.GetOrdinal("Model_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Model_ID"));
                        partProductAssociation.Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID"));
                        partProductAssociation.FromSerialNumber = reader.IsDBNull(reader.GetOrdinal("FromSerialNumber")) ? null : reader.GetString(reader.GetOrdinal("FromSerialNumber"));
                        partProductAssociation.ToSerialNumber = reader.IsDBNull(reader.GetOrdinal("ToSerialNumber")) ? null : reader.GetString(reader.GetOrdinal("ToSerialNumber"));

                        // Add the mapped object to the list
                        liPartProduct.Add(partProductAssociation);
                    }
                    reader.Close();
                }

                count = liPartProduct.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                // Fetch Brands using SQL query
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    string sqlQueryBrands = @"
                        SELECT rd.RefMasterDetail_ID AS ID, rd.RefMasterDetail_Name AS Name
                        FROM GNM_RefMasterDetail rd
                        INNER JOIN GNM_RefMaster rm ON rd.RefMaster_ID = rm.RefMaster_ID
                        WHERE rm.RefMaster_Name = 'BRAND'
                            AND rd.RefMasterDetail_IsActive = 1
                        ORDER BY rd.RefMasterDetail_Name;
                    ";

                    SqlCommand commandBrands = new SqlCommand(sqlQueryBrands, connection);

                    connection.Open();
                    SqlDataReader readerBrands = commandBrands.ExecuteReader();

                    while (readerBrands.Read())
                    {
                        // Constructing each brand object
                        GNM_RefMasterDetail brand = new GNM_RefMasterDetail
                        {
                            RefMasterDetail_ID = readerBrands.GetInt32(readerBrands.GetOrdinal("ID")),
                            RefMasterDetail_Name = readerBrands.GetString(readerBrands.GetOrdinal("Name"))
                        };

                        BrandData.Add(brand);
                    }

                    readerBrands.Close();
                }

                // Fetch ProductTypes using SQL query
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    string sqlQueryProductTypes = @"
                        SELECT ProductType_ID AS ID, ProductType_Name AS Name
                        FROM GNM_ProductType
                        WHERE ProductType_IsActive = 1
                        ORDER BY ProductType_Name;
                    ";

                    SqlCommand commandProductTypes = new SqlCommand(sqlQueryProductTypes, connection);

                    connection.Open();
                    SqlDataReader readerProductTypes = commandProductTypes.ExecuteReader();

                    while (readerProductTypes.Read())
                    {
                        // Constructing each product type object
                        ProductType productType = new ProductType
                        {
                            ProductType_ID = readerProductTypes.GetInt32(readerProductTypes.GetOrdinal("ID")),
                            ProductType_Name = readerProductTypes.GetString(readerProductTypes.GetOrdinal("Name"))
                        };

                        ProductTypeData.Add(productType);
                    }

                    readerProductTypes.Close();
                }

                GetPartsProductList GetPartsProductListObj = new GetPartsProductList();

                IQPartProduct = (IQueryable<PartsProduct>)GetPartsProduct(GetPartsProductListObj, SelectPartsProductObj.id, constring);


                //IQPartProduct = GetPartsProduct(id,constring);

                IQPartProduct = IQPartProduct.OrderByField<PartsProduct>(sidx, sord);
                //FilterToolBar Search
                //if (_search)
                //{
                //    filters = JObject.Parse(Common.DecryptString(filters).ToObject<Filters>());
                //    if (filters.rules.Count() > 0)
                //        IQPartProduct = IQPartProduct.FilterSearch<PartsProduct>(filters);
                //}
                // Construct Brands string
                string Brands = "-1:--------Select--------;";
                foreach (var brand in BrandData)
                {
                    Brands += $"{brand.RefMasterDetail_ID}:{brand.RefMasterDetail_Name};";
                }
                Brands = Brands.TrimEnd(';');

                // Construct ProductType string
                string ProductType = "-1:--------Select--------;";
                foreach (var productType in ProductTypeData)
                {
                    ProductType += $"{productType.ProductType_ID}:{productType.ProductType_Name};";
                }
                ProductType = ProductType.TrimEnd(';');

                // Construct PartProductDet object as needed...

                PartProductDet = new
                {
                    total = total,
                    page = page,
                    records = count,
                    rows = (from a in IQPartProduct
                            select new
                            {
                                edit = "<a title=" + CommonFunctionalities.GetResourceString(SelectPartsProductObj.UserCulture.ToString(), "view").ToString() + " href='#' style='font-size: 13px;' id='" + a.ID + "' key='" + a.ID + "' class='editPartProductDetails' editmode='false' ><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = $"<input type='checkbox' key='{a.ID}' defaultchecked='' class='DelPartProduct'/>",
                                ID = a.ID,
                                Brand = a.Brand,
                                ProductType = a.ProductType,
                                Model_ID = a.Model_ID,
                                Model = a.Model,
                                Search = "",
                                a.BrandID,
                                a.ProductTypeID,
                                a.FromSerialNumber,
                                a.ToSerialNumber
                            }).ToList().Paginate(page, rows),
                    Brands = Brands,
                    ProductType = ProductType,
                    id = SelectPartsProductObj.id,
                    //filter = SelectPartsProductObj.filters,
                    //advanceFilter = SelectPartsProductObj.Query,
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(PartProductDet);

            // return Json(PartProductDet, JsonRequestBehavior.AllowGet);
        }

        #region::: GetPartsProduct  / Mithun:::
        /// <summary>
        /// Get Parts Product
        /// </summary>
        /// <param name="GetPartsProductObj"></param>
        /// <param name="id"></param>
        /// <param name="constring"></param>
        /// <returns></returns>
        public static IActionResult GetPartsProduct(GetPartsProductList GetPartsProductObj, int id, string constring)
        // private IQueryable<PartsProduct> GetPartsProduct(int id,string constring)
        {
            List<PartsProduct> partProducts = new List<PartsProduct>();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));

            try
            {
                var User = GetPartsProductObj.userDetails;
                int Company_ID = User[0].Company_ID;

                // Establish ADO.NET connection
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Create command for stored procedure
                    SqlCommand command = new SqlCommand("UP_AMERP_GetPartsProductByPartsIDAndCompanyID", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    // Add parameters
                    command.Parameters.AddWithValue("@Parts_ID", id);
                    command.Parameters.AddWithValue("@Company_ID", Company_ID);

                    // Execute command
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            PartsProduct partProduct = new PartsProduct();
                            partProduct.ID = (int)reader["PartsProductAssociation_ID"];
                            partProduct.Brand = reader["Brand"].ToString();
                            partProduct.ProductType = reader["ProductType"] != DBNull.Value ? reader["ProductType"].ToString() : "";
                            partProduct.Model_ID = reader["Model_ID"] != DBNull.Value ? (int)reader["Model_ID"] : 0;
                            partProduct.Model = reader["Model"].ToString();
                            partProduct.BrandID = (int)reader["BrandID"];
                            partProduct.ProductTypeID = (int)reader["ProductTypeID"];
                            partProduct.FromSerialNumber = reader["FromSerialNumber"].ToString();
                            partProduct.ToSerialNumber = reader["ToSerialNumber"].ToString();

                            partProducts.Add(partProduct);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return partProducts.AsQueryable();
            return new JsonResult(partProducts.AsQueryable());
        }
        #endregion


        #endregion

        #region::: InsertPartsProduct /Mithun:::
        /// <summary>
        /// Insert Parts Product
        /// </summary>
        /// <param name="InsertPartsProductObj"></param>
        /// <param name="Constring"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult InsertPartsProduct(InsertPartsProductList InsertPartsProductObj, string Constring, int LogException)
        {
            SqlConnection conn = null;
            SqlCommand cmd = null;
            string msg = string.Empty;
            try
            {
                conn = new SqlConnection(Constring);
                conn.Open();

                int comp_ID = Convert.ToInt32(InsertPartsProductObj.Company_ID);
                JObject jObj = JObject.Parse(InsertPartsProductObj.key);
                int ContPersrowcount = jObj["rows"].Count();

                for (int i = 0; i < ContPersrowcount; i++)
                {
                    string ID = jObj["rows"][i]["ID"].ToString();
                    int Parts_ID = Convert.ToInt32(jObj["rows"][i]["Part_ID"]);
                    int Brand_ID = Convert.ToInt32(jObj["rows"][i]["Brand"]);
                    int? ProductType_ID = Convert.ToInt32(jObj["rows"][i]["ProductType"]) == -1 ? null : (int?)Convert.ToInt32(jObj["rows"][i]["ProductType"]);
                    int? Model_ID = string.IsNullOrEmpty(jObj["rows"][i]["Model"].ToString()) ? null : (int?)Convert.ToInt32(jObj["rows"][i]["Model"]);
                    string FromSerialNumber = string.IsNullOrEmpty(jObj["rows"][i]["FromSerialNumber"].ToString()) ? null : jObj["rows"][i]["FromSerialNumber"].ToString();
                    string ToSerialNumber = string.IsNullOrEmpty(jObj["rows"][i]["ToSerialNumber"].ToString()) ? null : jObj["rows"][i]["ToSerialNumber"].ToString();

                    if (string.IsNullOrWhiteSpace(ID))
                    {
                        // Call insert stored procedure
                        cmd = new SqlCommand("UP_INS_AMERP_InsertPartsProduct", conn);
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Parts_ID", Parts_ID);
                        cmd.Parameters.AddWithValue("@Brand_ID", Brand_ID);
                        cmd.Parameters.AddWithValue("@ProductType_ID", (object)ProductType_ID ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Model_ID", (object)Model_ID ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@FromSerialNumber", (object)FromSerialNumber ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ToSerialNumber", (object)ToSerialNumber ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Company_ID", comp_ID);

                        cmd.ExecuteNonQuery();
                    }
                    else
                    {
                        int PartsProductID = Convert.ToInt32(ID);

                        // Call update stored procedure
                        cmd = new SqlCommand("UP_UPD_AMERP_UpdatePartsProduct", conn);
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@PartsProductAssociation_ID", PartsProductID);
                        cmd.Parameters.AddWithValue("@Brand_ID", Brand_ID);
                        cmd.Parameters.AddWithValue("@ProductType_ID", (object)ProductType_ID ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Model_ID", (object)Model_ID ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@FromSerialNumber", (object)FromSerialNumber ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ToSerialNumber", (object)ToSerialNumber ?? DBNull.Value);

                        cmd.ExecuteNonQuery();
                    }
                    //gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"]), Convert.ToInt32(Session["Branch"]), Convert.ToInt32(Session["User_ID"]), Convert.ToInt32(Common.GetObjectID("CorePartsMaster")), Parts_ID, 0, 0, "Update", false, Convert.ToInt32(Session["MenuID"]));
                    // Your additional logic here, like logging or other operations
                }
            }
            catch (Exception ex)
            {
                // Handle exceptions
                if (LogException == 1)
                {
                    // Log exceptions
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            finally
            {
                // Clean up resources
                if (cmd != null)
                {
                    cmd.Dispose();
                }
                if (conn != null && conn.State == ConnectionState.Open)
                {
                    conn.Close();
                    conn.Dispose();
                }
            }
            return new JsonResult(msg);
        }


        #endregion


        #region::: DeletePartProductDetail /Mithun:::
        /// <summary>
        /// Delete Part Product Detail
        /// </summary>
        /// <returns></returns>
        public static IActionResult DeletePartProductDetail(DeletePartProductDetailList DeletePartProductDetailObj, string constring)
        {
            int id = 0;
            string errorMsg = string.Empty;
            SqlConnection connection = null;
            SqlCommand command = null;
            try
            {
                // Parse JSON input
                JObject jObj = JObject.Parse(DeletePartProductDetailObj.key);
                int rowCount = jObj["rows"].Count();

                // Open connection
                using (connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Iterate over rows and delete
                    for (int i = 0; i < rowCount; i++)
                    {
                        id = jObj["rows"][i]["id"].Value<int>();

                        // Create and execute SQL command
                        string sql = "DELETE FROM GNM_PartsProductAssociation WHERE PartsProductAssociation_ID = @id";
                        command = new SqlCommand(sql, connection);
                        command.Parameters.AddWithValue("@id", id);
                        command.ExecuteNonQuery();
                    }
                }

                // Success message
                errorMsg = CommonFunctionalities.GetResourceString(DeletePartProductDetailObj.UserCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (SqlException ex)
            {
                if (ex.Number == 547) // Foreign key constraint violation
                {
                    errorMsg = CommonFunctionalities.GetResourceString(DeletePartProductDetailObj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    // Handle other SQL exceptions
                    errorMsg = ex.Message;
                }
            }
            catch (Exception ex)
            {
                // Handle other exceptions
                errorMsg = ex.Message;
            }
            finally
            {
                // Clean up resources
                if (command != null)
                {
                    command.Dispose();
                }
                if (connection != null && connection.State == ConnectionState.Open)
                {
                    connection.Close();
                }
            }

            //return errorMsg;
            return new JsonResult(errorMsg);
        }

        #endregion



        #region::: SelectPartsFreestock /Mithun:::
        /// <summary>
        /// Select Parts FreeStock
        /// </summary>
        /// <param name="SelectPartsFreeStockObj"></param>
        /// <param name="sidx"></param>
        /// <param name="sord"></param>
        /// <param name="page"></param>
        /// <param name="rows"></param>
        /// <param name="id"></param>
        /// <param name="constring"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult SelectPartsFreeStock(SelectPartsFreeStockList SelectPartsFreeStockObj, string sidx, string sord, int page, int rows, string filters, string constring, int LogException)
        {
            List<PartsStock> partStockList = new List<PartsStock>();
            int count = 0;
            int total = 0;
            var User = default(dynamic);
            //User = SelectPartsFreeStockObj.userDetails;
            int CompanyID = SelectPartsFreeStockObj.Company_ID;
            int BranchID = Convert.ToInt32(SelectPartsFreeStockObj.Branch);
            bool cnt = false;
            List<Prt_DamagedQuantity> damagedQuantities = new List<Prt_DamagedQuantity>();
            List<GNM_Branch> branches = new List<GNM_Branch>();

            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    SqlCommand command = connection.CreateCommand();
                    command.CommandType = CommandType.StoredProcedure;

                    // Retrieve branches
                    command.CommandText = "UP_AMERP_GetBranches";
                    command.Parameters.AddWithValue("@CompanyID", CompanyID);

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            GNM_Branch branch = new GNM_Branch
                            {
                                Branch_ID = Convert.ToInt32(reader["Branch_ID"]),
                                Branch_Name = reader["Branch_Name"].ToString()
                            };
                            branches.Add(branch);
                        }
                    }

                    // Retrieve damaged quantities
                    command.CommandText = "UP_AMERP_GetDamagedQuantities";
                    command.Parameters.Clear();
                    command.Parameters.AddWithValue("@PartID", SelectPartsFreeStockObj.id);

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            Prt_DamagedQuantity damagedQty = new Prt_DamagedQuantity
                            {
                                DamagedQuantity = Convert.ToInt32(reader["DamagedQuantity"]),
                                Branch_ID = Convert.ToInt32(reader["Branch_ID"]),
                                WareHouse_ID = Convert.ToInt32(reader["WareHouse_ID"]),
                                Parts_ID = Convert.ToInt32(reader["Parts_ID"])
                            };
                            damagedQuantities.Add(damagedQty);
                        }
                    }

                    // Retrieve parts stock details
                    command.CommandText = "UP_AMERP_GetPartsStockDetails";
                    command.Parameters.Clear();
                    command.Parameters.AddWithValue("@PartID", SelectPartsFreeStockObj.id);
                    command.Parameters.AddWithValue("@CompanyID", CompanyID);

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            PartsStock partStock = new PartsStock
                            {
                                ID = Convert.ToInt32(reader["PartsStockDetail_ID"]),
                                Branch = reader["Branch_Name"].ToString(),
                                BinLocation = reader["BinLocation_Name"].ToString(),
                                WareHouse = reader["WareHouseName"].ToString(),
                                FreeStock = reader["FreeStock"].ToString(),
                                BinStock = reader["BinStock"].ToString(),
                                TotalStock = reader["TotalStock"].ToString(),
                                Branch_ID = Convert.ToInt32(reader["Branch_ID"]),
                                WareHouse_ID = Convert.ToInt32(reader["WareHouse_ID"]),
                                FirstDemandDateStr = reader["FirstDemandDateStr"].ToString(),
                                LastDemandDateStr = reader["LastDemandDateStr"].ToString(),
                                FirstIssuedDateStr = reader["FirstIssuedDateStr"].ToString(),
                                LastIssuedDateStr = reader["LastIssuedDateStr"].ToString(),
                                LastStockCheckDateStr = reader["LastStockCheckDateStr"].ToString()
                            };

                            // Get DamagedQuantity for this part stock
                            partStock.DamagedQuantity = GetDamagedQuantity(partStock.Branch_ID, partStock.WareHouse_ID, damagedQuantities);

                            partStockList.Add(partStock);
                        }
                    }

                    // Calculate pagination details
                    count = partStockList.Count;
                    total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                }

                // Construct BranchArray
                string BranchArray = "-1:--Select--;";
                foreach (var branch in branches)
                {
                    BranchArray += branch.Branch_ID + ":" + branch.Branch_Name + ";";
                }
                BranchArray = BranchArray.TrimEnd(';');

                // Retrieve bin locations
                List<GNM_BinLocation> binLocations = new List<GNM_BinLocation>();
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    SqlCommand command = connection.CreateCommand();
                    command.CommandType = CommandType.StoredProcedure;
                    command.CommandText = "UP_AMERP_GetBinLocations";
                    command.Parameters.AddWithValue("@CompanyID", CompanyID);
                    command.Parameters.AddWithValue("@BranchID", BranchID);

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            GNM_BinLocation binLocation = new GNM_BinLocation
                            {
                                BinLocation_ID = Convert.ToInt32(reader["BinLocation_ID"]),
                                BinLocation_Name = reader["BinLocation_Name"].ToString()
                            };
                            binLocations.Add(binLocation);
                        }
                    }
                }

                // Construct BinLocationArray
                string BinLocationArray = "-1:--Select--;";
                foreach (var binLocation in binLocations)
                {
                    BinLocationArray += binLocation.BinLocation_ID + ":" + binLocation.BinLocation_Name + ";";
                }
                BinLocationArray = BinLocationArray.TrimEnd(';');

                // Construct PartProduct object to return as JsonResult
                var PartProduct = new
                {
                    total = total,
                    page = page,
                    records = count,
                    rows = (
                        from a in partStockList
                        select new
                        {
                            ID = a.ID,
                            edit = "<a title='" + CommonFunctionalities.GetResourceString(SelectPartsFreeStockObj.UserCulture.ToString(), "view").ToString() + "' href='#' style='font-size: 13px;' id='" + a.ID + "'  alt='Edit' key='" + a.ID + "' class='editPartFreeStock' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                            //delete = (IsStandAlone == true ? "<input type='checkbox' key='" + a.ID + "' defaultchecked='' class='DelPartFreeStock'/>" : string.Empty),
                            Branch = a.Branch,
                            BinLocation = a.BinLocation,
                            WareHouse = a.WareHouse,
                            FreeStock = a.FreeStock,
                            BinStock = a.BinStock,
                            TotalStock = a.TotalStock,
                            DamagedQuantity = a.DamagedQuantity,
                            FirstDemandDate = a.FirstDemandDateStr,
                            LastDemandDate = a.LastDemandDateStr,
                            FirstIssuedDate = a.FirstIssuedDateStr,
                            LastIssuedDate = a.LastIssuedDateStr,
                            LastStockCheckDate = a.LastStockCheckDateStr
                        }
                    ).ToList().Paginate(page, rows),
                    cnt = cnt,
                    //BranchArray = BranchArray,
                    //BinLocationArray = BinLocationArray
                };

                //return Json(PartProduct, JsonRequestBehavior.AllowGet);
                return new JsonResult(PartProduct);
            }
            catch (Exception ex)
            {
                // Handle exceptions and log if necessary
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                // return Json(new { error = ex.Message }, JsonRequestBehavior.AllowGet);
                return new JsonResult(ex.Message);
            }
        }

        #region::: GetDamagedQuantity :::

        public static string GetDamagedQuantity(int Branch_ID, int? WareHouse_ID, List<Prt_DamagedQuantity> DmgQty)
        {
            string DQty = "0";
            for (int i = 0; i < DmgQty.Count(); i++)
            {
                if ((DmgQty[i].Branch_ID == Branch_ID) && (DmgQty[i].WareHouse_ID == WareHouse_ID))
                {
                    DQty = DmgQty[i].DamagedQuantity.ToString().Split('.')[0];
                    break;
                }
            }
            return DQty;
        }
        #endregion
        #endregion

        #region::: LoadBinLocationArray /Mithun:::
        /// <summary>
        /// Load Bin Location Array
        /// </summary>
        /// <param name="BranchID"></param>
        /// <returns></returns>
        public static ActionResult LoadBinLocationArray(LoadBinLocationArrayList LoadBinLocationArrayObj, string constring, int LogException)
        {
            var BinLocations = new { BinLocationArray = new List<object>() };
            try
            {
                int CompanyID = Convert.ToInt32(LoadBinLocationArrayObj.Company_ID);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand("UP_AMERP_GetBinLocations", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@CompanyID", CompanyID);
                        command.Parameters.AddWithValue("@BranchID", LoadBinLocationArrayObj.BranchID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            var binLocationArray = new List<object>();
                            while (reader.Read())
                            {
                                binLocationArray.Add(new
                                {
                                    ID = reader.GetInt32(reader.GetOrdinal("BinLocation_ID")),
                                    Name = reader.GetString(reader.GetOrdinal("BinLocation_Name"))
                                });
                            }
                            BinLocations = new { BinLocationArray = binLocationArray };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            // return Json(BinLocations, JsonRequestBehavior.AllowGet);
            return new JsonResult(BinLocations);
        }

        #endregion


        #region::: Validate BinLocation /Mithun:::
        /// <summary>
        /// Validate BinLocation
        /// </summary>
        /// <param name="Constring"></param>
        /// <param name="ValidateBinLocationObj"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult ValidateBinLocation(string Constring, ValidateBinLocationList ValidateBinLocationObj, int LogException)
        {
            bool isExists = false;

            string query = "SELECT COUNT(*) FROM GNM_PartsStockDetail " +
                           "WHERE Parts_ID = @PartID AND BinLocation_ID = @BinLocation AND Branch_ID = @Branch AND PartsStockDetail_ID != @primKey";

            try
            {
                using (SqlConnection connection = new SqlConnection(Constring))
                {
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@PartID", ValidateBinLocationObj.PartID);
                        command.Parameters.AddWithValue("@BinLocation", ValidateBinLocationObj.BinLocation);
                        command.Parameters.AddWithValue("@Branch", ValidateBinLocationObj.Branch);
                        command.Parameters.AddWithValue("@primKey", ValidateBinLocationObj.primKey);

                        connection.Open();
                        int count = (int)command.ExecuteScalar();
                        isExists = count > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return isExists;
            return new JsonResult(isExists);
        }
        #endregion


        #region::: DeletePartFreeStock /Mithun:::
        /// <summary>
        /// Delete parts Free sock
        /// </summary>
        /// <returns></returns>

        public static IActionResult DeletePartFreeStock(DeletePartFreeStockList DeletePartFreeStockObj, string constring)
        {
            var Culture = "Resource_" + DeletePartFreeStockObj.Lang;
            JObject jObj = JObject.Parse(DeletePartFreeStockObj.key);
            int rowcount = jObj["rows"].Count();
            int id = 0;
            string errorMsg = string.Empty;
            SqlConnection conn = null;
            SqlTransaction transaction = null;

            try
            {
                conn = new SqlConnection(constring);
                conn.Open();
                transaction = conn.BeginTransaction();

                for (int i = 0; i < rowcount; i++)
                {
                    id = (int)jObj["rows"].ElementAt(i)["id"];

                    string deleteQuery = "DELETE FROM GNM_PartsStockDetail WHERE PartsStockDetail_ID = @PartsStockDetail_ID";

                    using (SqlCommand cmd = new SqlCommand(deleteQuery, conn, transaction))
                    {
                        cmd.Parameters.AddWithValue("@PartsStockDetail_ID", id);
                        cmd.ExecuteNonQuery();
                    }
                }

                transaction.Commit();

                //gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"]), Convert.ToInt32(Session["Branch"]), Convert.ToInt32(Session["User_ID"]), Convert.ToInt32(Common.GetObjectID("CorePartsMaster")), id, 0, 0, "Delete", false, Convert.ToInt32(Session["MenuID"]));

                errorMsg = CommonFunctionalities.GetResourceString(Culture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (SqlException ex)
            {
                if (ex.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    errorMsg = CommonFunctionalities.GetResourceString(Culture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    errorMsg = ex.Message;
                }

                if (transaction != null)
                {
                    transaction.Rollback();
                }
            }
            finally
            {
                if (conn != null && conn.State == System.Data.ConnectionState.Open)
                {
                    conn.Close();
                }
            }

            //return errorMsg;
            return new JsonResult(errorMsg);
        }


        #endregion

        #region::: Insert Supplier Details /Mithun:::
        /// <summary>
        /// Insert supplier Details 
        /// </summary>
        public static void InsertSupplierDetails(InsertSupplierDetailsList InsertSupplierDetailsObj, string constring, int LogException)
        {
            string branchData = InsertSupplierDetailsObj.key;


            int? intNullable = null;
            decimal? decNullable = null;
            DateTime? DateNullable = null;
            int Company_ID = Convert.ToInt32(InsertSupplierDetailsObj.Company_ID);
            bool isHeaderValid = true;

            try
            {
                JObject jObj = JObject.Parse(InsertSupplierDetailsObj.key);
                int Cont = jObj["rows"].Count();
                for (int i = 0; i < Cont; i++)
                {
                    JObject row = jObj["rows"].ElementAt(i).ToObject<JObject>();

                    var columns = new Dictionary<string, bool>
                            {
                                { "Part_ID", true },
                                { "SupplierID", true },
                                { "Supplier_price", true },
                                { "EffectiveFrom", true },
                                { "Currency", true },
                                { "SupplierYTAPrice", true },
                                { "SupplierYEAPrice", true },

                            };
                    List<string> invalidColumns;
                    bool isRowValid = Common.ValidateAndLog(row, columns, out invalidColumns);
                    if (!isRowValid)
                    {
                        isHeaderValid = false;
                        break;
                    }

                }

                if (isHeaderValid)
                {



                    int ContPersrowcount = jObj["rows"].Count();

                    using (SqlConnection connection = new SqlConnection(constring))
                    {
                        connection.Open();

                        for (int i = 0; i < ContPersrowcount; i++)
                        {
                            SqlCommand command = new SqlCommand();
                            command.Connection = connection;
                            command.CommandType = CommandType.Text;

                            // Extract values from JSON
                            string ID = jObj["rows"][i]["ID"].ToString().Trim();
                            int Parts_ID = Convert.ToInt32(jObj["rows"][i]["Part_ID"]);
                            int SupplierID = Convert.ToInt32(jObj["rows"][i]["SupplierID"]);
                            string Supplier_PartNum = jObj["rows"][i]["Supplier_PartNum"].ToString();
                            string Supplier_PartPre = jObj["rows"][i]["Supplier_PartPre"].ToString();
                            int Currency = Convert.ToInt32(jObj["rows"][i]["Currency"]);
                            string StdQty = jObj["rows"][i]["StdQty"].ToString();
                            string Supplier_price = jObj["rows"][i]["Supplier_price"].ToString();
                            string CostPrice = jObj["rows"][i]["CostPrice"].ToString();
                            DateTime EffectiveFrom = Convert.ToDateTime(jObj["rows"][i]["EffectiveFrom"]);

                            // ManufacturerWarranty and IsWarrantyIntimation
                            int? ManufacturWarranty = null;
                            if (!string.IsNullOrEmpty(jObj["rows"][i]["ManufacturerWarranty"].ToString()))
                            {
                                ManufacturWarranty = Convert.ToInt32(jObj["rows"][i]["ManufacturerWarranty"]);
                            }
                            bool IsWarrantyIntimation = Convert.ToBoolean(jObj["rows"][i]["IsWarrantyIntimation"]);

                            if (string.IsNullOrEmpty(ID))
                            {
                                // Insert new record using stored procedure
                                command.CommandText = "UP_INS_AMERP_InsertPartsSupplierDetail";
                                command.CommandType = CommandType.StoredProcedure;
                                //gbl.InsertGPSDetails(Convert.ToInt32(InsertSupplierDetailsObj.Company_ID), Convert.ToInt32(InsertSupplierDetailsObj.Branch), Convert.ToInt32(InsertSupplierDetailsObj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartsMaster",constring)), Parts_ID, 0, 0, "Update", false, Convert.ToInt32(InsertSupplierDetailsObj.MenuID));
                            }
                            else
                            {
                                // Update existing record using stored procedure
                                command.CommandText = "UP_UPD_AMERP_UpdatePartsSupplierDetail";
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@PartsSupplierDetail_ID", Convert.ToInt32(ID));
                                //gbl.InsertGPSDetails(Convert.ToInt32(InsertSupplierDetailsObj.Company_ID), Convert.ToInt32(InsertSupplierDetailsObj.Branch), Convert.ToInt32(InsertSupplierDetailsObj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartsMaster",constring)), Parts_ID, 0, 0, "Update", false, Convert.ToInt32(InsertSupplierDetailsObj.MenuID));
                            }

                            // Add parameters for both insert and update scenarios
                            command.Parameters.AddWithValue("@Parts_ID", Parts_ID);
                            command.Parameters.AddWithValue("@Supplier_ID", SupplierID);
                            command.Parameters.AddWithValue("@StandardPackingQuantity", string.IsNullOrEmpty(StdQty) ? (object)DBNull.Value : Convert.ToDecimal(StdQty));
                            command.Parameters.AddWithValue("@SupplierPrice", string.IsNullOrEmpty(Supplier_price) ? (object)DBNull.Value : Convert.ToDecimal(Supplier_price));
                            command.Parameters.AddWithValue("@Effectivefrom", EffectiveFrom);
                            command.Parameters.AddWithValue("@CostPrice", string.IsNullOrEmpty(CostPrice) ? (object)DBNull.Value : Convert.ToDecimal(CostPrice));
                            command.Parameters.AddWithValue("@Currency_ID", Currency);
                            command.Parameters.AddWithValue("@LastInvoicedDate", DateNullable ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@SupplierPartNumber", Common.DecryptString(Supplier_PartNum));
                            command.Parameters.AddWithValue("@Company_ID", Company_ID);
                            command.Parameters.AddWithValue("@SupplierPartPrefix", Common.DecryptString(Supplier_PartPre));
                            command.Parameters.AddWithValue("@ManufacturerWarranty", ManufacturWarranty ?? (object)DBNull.Value);
                            command.Parameters.AddWithValue("@IsWarrantyIntimation", IsWarrantyIntimation ? 1 : 0);
                            // command.Parameters.AddWithValue("@LatestPurchaseCost", LatestPurchaseCost ?? (object)DBNull.Value);
                            // command.Parameters.AddWithValue("@FirstGRNDate", FirstGRNDate ?? (object)DBNull.Value);
                            // command.Parameters.AddWithValue("@LastGRNDate", LastGRNDate ?? (object)DBNull.Value);
                            // command.Parameters.AddWithValue("@SupplierYTAPrice", SupplierYTAPrice ?? (object)DBNull.Value);
                            // command.Parameters.AddWithValue("@SupplierYEAPrice", SupplierYEAPrice ?? (object)DBNull.Value);
                            command.ExecuteNonQuery();

                            // Retrieve partsCategoryID using ADO.NET
                            int? partsCategoryID = null;
                            using (SqlCommand selectPartsCategoryCommand = new SqlCommand("SELECT PartsCategory_ID FROM GNM_Parts WHERE Parts_ID = @Parts_ID", connection))
                            {
                                selectPartsCategoryCommand.Parameters.AddWithValue("@Parts_ID", Parts_ID);
                                partsCategoryID = selectPartsCategoryCommand.ExecuteScalar() as int?;
                            }

                            // Further processing with partsCategoryID
                            if (partsCategoryID != null)
                            {
                                // Retrieve parentCompanyID using ADO.NET
                                int parentCompanyID = 0;
                                using (SqlCommand selectParentCompanyCommand = new SqlCommand("SELECT ISNULL(Company_Parent_ID, 0) FROM GNM_Company WHERE Company_ID = @Company_ID", connection))
                                {
                                    selectParentCompanyCommand.Parameters.AddWithValue("@Company_ID", Company_ID);
                                    parentCompanyID = Convert.ToInt32(selectParentCompanyCommand.ExecuteScalar());
                                }

                                // Check if parentCompanyID is valid
                                if (parentCompanyID != 0)
                                {
                                    // Retrieve supplierRow using ADO.NET
                                    GNM_Party supplierRow = null;
                                    using (SqlCommand selectSupplierCommand = new SqlCommand("SELECT * FROM GNM_Party WHERE PartyType = 4 AND Relationship_Company_ID = @ParentCompanyID AND Party_IsActive = 1 AND IsOEM = 1 AND Company_ID = @Company_ID", connection))
                                    {
                                        selectSupplierCommand.Parameters.AddWithValue("@ParentCompanyID", parentCompanyID);
                                        selectSupplierCommand.Parameters.AddWithValue("@Company_ID", Company_ID);

                                        using (SqlDataReader reader = selectSupplierCommand.ExecuteReader())
                                        {
                                            if (reader.Read())
                                            {
                                                supplierRow = new GNM_Party();
                                                // Map reader columns to supplierRow properties if needed
                                                supplierRow.Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID"));
                                                supplierRow.Party_IsActive = reader.GetBoolean(reader.GetOrdinal("Party_IsActive"));
                                                supplierRow.Party_IsLocked = reader.GetBoolean(reader.GetOrdinal("Party_IsLocked"));
                                                supplierRow.Party_Name = reader.GetString(reader.GetOrdinal("Party_Name"));
                                                supplierRow.Party_Location = reader.GetString(reader.GetOrdinal("Party_Location"));
                                                supplierRow.Party_Email = reader.GetString(reader.GetOrdinal("Party_Email"));
                                                supplierRow.Party_Phone = reader.GetString(reader.GetOrdinal("Party_Phone"));
                                                supplierRow.Party_Fax = reader.GetString(reader.GetOrdinal("Party_Fax"));
                                                supplierRow.Party_PaymentTerms = reader.GetString(reader.GetOrdinal("Party_PaymentTerms"));
                                                supplierRow.PartyType = reader.GetByte(reader.GetOrdinal("PartyType"));
                                                supplierRow.Party_Mobile = reader.GetString(reader.GetOrdinal("Party_Mobile"));
                                                supplierRow.ModifiedBY = reader.GetInt32(reader.GetOrdinal("ModifiedBY"));
                                                supplierRow.ModifiedDate = reader.GetDateTime(reader.GetOrdinal("ModifiedDate"));
                                            }
                                        }
                                    }

                                    // Check if supplierRow is found and process cateDefnitionRow
                                    if (supplierRow != null)
                                    {
                                        // Retrieve cateDefnitionRow using ADO.NET
                                        PRM_CorePartsCategoryDefinition cateDefnitionRow = null;
                                        using (SqlCommand selectCategoryDefinitionCommand = new SqlCommand("SELECT * FROM PRM_PartsCategoryDefinition WHERE PartsCategory_ID = @PartsCategory_ID AND Company_ID = @Company_ID", connection))
                                        {
                                            selectCategoryDefinitionCommand.Parameters.AddWithValue("@PartsCategory_ID", partsCategoryID);
                                            selectCategoryDefinitionCommand.Parameters.AddWithValue("@Company_ID", Company_ID);

                                            using (SqlDataReader reader = selectCategoryDefinitionCommand.ExecuteReader())
                                            {
                                                if (reader.Read())
                                                {
                                                    cateDefnitionRow = new PRM_CorePartsCategoryDefinition();
                                                    // Map reader columns to cateDefnitionRow properties if needed
                                                    cateDefnitionRow = new PRM_CorePartsCategoryDefinition();
                                                    cateDefnitionRow.PartsCategoryDefinition_ID = reader.GetInt32(reader.GetOrdinal("PartsCategoryDefinition_ID"));
                                                    cateDefnitionRow.PartsCategory_ID = reader.GetInt32(reader.GetOrdinal("PartsCategory_ID"));
                                                    cateDefnitionRow.ConversionFactor = reader.IsDBNull(reader.GetOrdinal("ConversionFactor")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("ConversionFactor"));
                                                    cateDefnitionRow.ProfitValue = reader.IsDBNull(reader.GetOrdinal("ProfitValue")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("ProfitValue"));
                                                    cateDefnitionRow.Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID"));
                                                    cateDefnitionRow.Branch_ID = reader.GetInt32(reader.GetOrdinal("Branch_ID"));
                                                    cateDefnitionRow.IsActive = reader.GetBoolean(reader.GetOrdinal("IsActive"));
                                                    cateDefnitionRow.MRPFactor = reader.IsDBNull(reader.GetOrdinal("MRPFactor")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("MRPFactor"));
                                                }
                                            }
                                        }

                                        // Process cateDefnitionRow
                                        if (cateDefnitionRow != null)
                                        {
                                            decimal ConversionFactor = cateDefnitionRow.ConversionFactor ?? 0.00M;
                                            decimal profitFactor = cateDefnitionRow.ProfitValue ?? 0.00M;
                                            decimal MRPFactor = cateDefnitionRow.MRPFactor ?? 0.00M;

                                            // Retrieve or create priceDetailRow using ADO.NET
                                            GNM_PartsPriceDetail priceDetailRow = null;
                                            using (SqlCommand selectPriceDetailCommand = new SqlCommand("SELECT * FROM GNM_PartsPriceDetail WHERE Parts_ID = @Parts_ID AND PartsPriceDetail_EffectiveFrom = @EffectiveFrom", connection))
                                            {
                                                selectPriceDetailCommand.Parameters.AddWithValue("@Parts_ID", Parts_ID);
                                                selectPriceDetailCommand.Parameters.AddWithValue("@EffectiveFrom", EffectiveFrom);

                                                using (SqlDataReader reader = selectPriceDetailCommand.ExecuteReader())
                                                {
                                                    if (reader.Read())
                                                    {
                                                        priceDetailRow = new GNM_PartsPriceDetail();
                                                        // Map reader columns to priceDetailRow properties if needed
                                                        priceDetailRow.PartsPriceDetail_ID = reader.GetInt32(reader.GetOrdinal("PartsPriceDetail_ID"));
                                                        priceDetailRow.Parts_ID = reader.GetInt32(reader.GetOrdinal("Parts_ID"));
                                                        priceDetailRow.PartsPriceDetail_ListPrice = reader.GetDecimal(reader.GetOrdinal("PartsPriceDetail_ListPrice"));
                                                        priceDetailRow.PartsPriceDetail_FormulaCostPrice = reader.IsDBNull(reader.GetOrdinal("PartsPriceDetail_FormulaCostPrice")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("PartsPriceDetail_FormulaCostPrice"));
                                                        priceDetailRow.PartsPriceDetail_MRP = reader.GetDecimal(reader.GetOrdinal("PartsPriceDetail_MRP"));
                                                        priceDetailRow.PartsPriceDetail_EffectiveFrom = reader.GetDateTime(reader.GetOrdinal("PartsPriceDetail_EffectiveFrom"));
                                                        priceDetailRow.Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID"));
                                                        priceDetailRow.CustomerWarranty = reader.IsDBNull(reader.GetOrdinal("CustomerWarranty")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("CustomerWarranty"));
                                                        priceDetailRow.Currency_ID = reader.IsDBNull(reader.GetOrdinal("Currency_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Currency_ID"));
                                                    }
                                                }
                                            }

                                            // Update or insert priceDetailRow
                                            if (priceDetailRow == null)
                                            {
                                                // Insert new priceDetailRow
                                                priceDetailRow = new GNM_PartsPriceDetail();
                                                priceDetailRow.Company_ID = Company_ID;
                                                priceDetailRow.Parts_ID = Parts_ID;
                                                priceDetailRow.PartsPriceDetail_EffectiveFrom = EffectiveFrom;
                                                priceDetailRow.PartsPriceDetail_FormulaCostPrice = string.IsNullOrEmpty(Supplier_price) ? decNullable : (Convert.ToDecimal(Supplier_price) * ConversionFactor);
                                                priceDetailRow.PartsPriceDetail_ListPrice = (decimal)(string.IsNullOrEmpty(Supplier_price) ? decNullable : (Convert.ToDecimal(Supplier_price) * ConversionFactor * profitFactor));
                                                priceDetailRow.PartsPriceDetail_MRP = (decimal)(string.IsNullOrEmpty(Supplier_price) ? decNullable : (Convert.ToDecimal(Supplier_price) * ConversionFactor * profitFactor * MRPFactor));

                                                // Execute insert command
                                                SqlCommand insertPriceDetailCommand = new SqlCommand(@"INSERT INTO GNM_PartsPriceDetail (Company_ID, Parts_ID, PartsPriceDetail_EffectiveFrom, PartsPriceDetail_FormulaCostPrice, PartsPriceDetail_ListPrice, PartsPriceDetail_MRP) 
                                                                                               VALUES (@Company_ID, @Parts_ID, @EffectiveFrom, @PartsPriceDetail_FormulaCostPrice, @PartsPriceDetail_ListPrice, @PartsPriceDetail_MRP)", connection);
                                                insertPriceDetailCommand.Parameters.AddWithValue("@Company_ID", priceDetailRow.Company_ID);
                                                insertPriceDetailCommand.Parameters.AddWithValue("@Parts_ID", priceDetailRow.Parts_ID);
                                                insertPriceDetailCommand.Parameters.AddWithValue("@EffectiveFrom", priceDetailRow.PartsPriceDetail_EffectiveFrom);
                                                insertPriceDetailCommand.Parameters.AddWithValue("@PartsPriceDetail_FormulaCostPrice", priceDetailRow.PartsPriceDetail_FormulaCostPrice);
                                                insertPriceDetailCommand.Parameters.AddWithValue("@PartsPriceDetail_ListPrice", priceDetailRow.PartsPriceDetail_ListPrice);
                                                insertPriceDetailCommand.Parameters.AddWithValue("@PartsPriceDetail_MRP", priceDetailRow.PartsPriceDetail_MRP);

                                                insertPriceDetailCommand.ExecuteNonQuery();

                                                // Log the insertion or update
                                                //    gbl.InsertGPSDetails(Convert.ToInt32(InsertSupplierDetailsObj.Company_ID), Convert.ToInt32(InsertSupplierDetailsObj.Branch), Convert.ToInt32(InsertSupplierDetailsObj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartsMaster",constring)), Parts_ID, 0, 0, "Update", false, Convert.ToInt32(InsertSupplierDetailsObj.MenuID));
                                            }
                                            else
                                            {
                                                // Update existing priceDetailRow
                                                priceDetailRow.PartsPriceDetail_FormulaCostPrice = string.IsNullOrEmpty(Supplier_price) ? decNullable : (Convert.ToDecimal(Supplier_price) * ConversionFactor);
                                                priceDetailRow.PartsPriceDetail_ListPrice = (decimal)(string.IsNullOrEmpty(Supplier_price) ? decNullable : (Convert.ToDecimal(Supplier_price) * ConversionFactor * profitFactor));
                                                priceDetailRow.PartsPriceDetail_MRP = (decimal)(string.IsNullOrEmpty(Supplier_price) ? decNullable : (Convert.ToDecimal(Supplier_price) * ConversionFactor * profitFactor * MRPFactor));

                                                // Execute update command
                                                SqlCommand updatePriceDetailCommand = new SqlCommand(@"UPDATE GNM_PartsPriceDetail 
                                                                                               SET PartsPriceDetail_FormulaCostPrice = @PartsPriceDetail_FormulaCostPrice, 
                                                                                                   PartsPriceDetail_ListPrice = @PartsPriceDetail_ListPrice, 
                                                                                                   PartsPriceDetail_MRP = @PartsPriceDetail_MRP 
                                                                                               WHERE Parts_ID = @Parts_ID AND PartsPriceDetail_EffectiveFrom = @EffectiveFrom", connection);
                                                updatePriceDetailCommand.Parameters.AddWithValue("@Parts_ID", priceDetailRow.Parts_ID);
                                                updatePriceDetailCommand.Parameters.AddWithValue("@EffectiveFrom", priceDetailRow.PartsPriceDetail_EffectiveFrom);
                                                updatePriceDetailCommand.Parameters.AddWithValue("@PartsPriceDetail_FormulaCostPrice", priceDetailRow.PartsPriceDetail_FormulaCostPrice);
                                                updatePriceDetailCommand.Parameters.AddWithValue("@PartsPriceDetail_ListPrice", priceDetailRow.PartsPriceDetail_ListPrice);
                                                updatePriceDetailCommand.Parameters.AddWithValue("@PartsPriceDetail_MRP", priceDetailRow.PartsPriceDetail_MRP);

                                                updatePriceDetailCommand.ExecuteNonQuery();

                                                // Log the insertion or update
                                                //gbl.InsertGPSDetails(Convert.ToInt32(InsertSupplierDetailsObj.Company_ID), Convert.ToInt32(InsertSupplierDetailsObj.Branch), Convert.ToInt32(InsertSupplierDetailsObj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartsMaster",constring)), Parts_ID, 0, 0, "Update", false, Convert.ToInt32(InsertSupplierDetailsObj.MenuID));
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    // Log exception
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }


        }

        #endregion

        #region ::: DeletePartSupplierDetails /Mithun:::
        /// <summary>
        ///to Delete Part Supplier Details
        /// </summary>        
        public static IActionResult DeletePartSupplierDetails(DeletePartSupplierDetailsList DeletePartSupplierDetailsObj, string constring)
        {
            string errorMsg = string.Empty;
            SqlConnection connection = null;
            SqlCommand command = null;
            var Culture = "Resource_" + DeletePartSupplierDetailsObj.Lang;

            try
            {
                connection = new SqlConnection(constring);
                connection.Open();

                JObject jObj = JObject.Parse(DeletePartSupplierDetailsObj.key);
                int rowcount = jObj["rows"].Count();

                for (int i = 0; i < rowcount; i++)
                {
                    int id = jObj["rows"][i]["id"].ToObject<int>();

                    // Create a SQL command to delete the record
                    string sql = "DELETE FROM GNM_PartsSupplierDetail WHERE PartsSupplierDetail_ID = @Id";
                    command = new SqlCommand(sql, connection);
                    command.Parameters.AddWithValue("@Id", id);

                    int rowsAffected = command.ExecuteNonQuery();
                    if (rowsAffected > 0)
                    {
                        // Log deletion details or perform any other necessary actions
                        // Example: Logging
                        //gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"]), Convert.ToInt32(Session["Branch"]), Convert.ToInt32(Session["User_ID"]), Convert.ToInt32(Common.GetObjectID("CorePartsMaster")), id, 0, 0, "Delete", false, Convert.ToInt32(Session["MenuID"]));
                    }
                }

                errorMsg = CommonFunctionalities.GetResourceString(Culture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (SqlException ex)
            {
                if (ex.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    errorMsg = CommonFunctionalities.GetResourceString(Culture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                // Handle other SQL exceptions if necessary
            }
            finally
            {
                // Clean up resources
                if (command != null)
                {
                    command.Dispose();
                }
                if (connection != null && connection.State == ConnectionState.Open)
                {
                    connection.Close();
                    connection.Dispose();
                }
            }

            //return errorMsg;
            return new JsonResult(errorMsg);
        }


        #endregion

        #region ::: ValidatePartNumber /Mithun::: 
        /// <summary>
        /// To Validate PartNumber for duplicate 
        /// </summary>

        public static IActionResult ValidatePartNumber(ValidatePartNumberList ValidatePartNumberObj, string constring, int LogException)
        {
            var Part = default(dynamic);
            string PartNumber = Common.DecryptString(ValidatePartNumberObj.PartNumber);
            string Prefix = Common.DecryptString(ValidatePartNumberObj.Prefix);
            string Query = string.Empty;
            bool IsDealerPart = false;
            List<ParentCompanyObject> ParentCompanyDetails = new List<ParentCompanyObject>();
            var UserDetails = default(dynamic);
            try
            {
                //GNM_User User = (GNM_User)Session["UserDetails"];

                // UserDetails = ValidatePartNumberObj.UserDetails;

                // ADO.NET setup
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Check if the part number already exists
                    Query = "SELECT * FROM GNM_Parts WHERE Parts_PartsNumber = @PartNumber AND Parts_PartPrefix = @Prefix AND Company_ID = @CompanyID AND Parts_ID != @Key";
                    using (SqlCommand cmd = new SqlCommand(Query, conn))
                    {
                        cmd.Parameters.AddWithValue("@PartNumber", PartNumber);
                        cmd.Parameters.AddWithValue("@Prefix", Prefix);
                        cmd.Parameters.AddWithValue("@CompanyID", ValidatePartNumberObj.Company_ID);
                        cmd.Parameters.AddWithValue("@Key", ValidatePartNumberObj.key);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.HasRows)
                            {
                                Part = new { type = "1", IsValid = false };
                                return new JsonResult(Part);
                            }
                        }
                    }

                    // Get Parent Company ID
                    Query = "SELECT Company_Parent_ID FROM GNM_Company WHERE Company_ID = @CompanyID";
                    int ParentCompID = 0;
                    using (SqlCommand cmd = new SqlCommand(Query, conn))
                    {
                        cmd.Parameters.AddWithValue("@CompanyID", ValidatePartNumberObj.Company_ID);
                        ParentCompID = Convert.ToInt32(cmd.ExecuteScalar());
                    }

                    // If the user company is not the parent company and the parent company ID is not zero
                    if (ValidatePartNumberObj.Company_ID != ParentCompID && ParentCompID != 0)
                    {
                        using (SqlCommand cmd = new SqlCommand("UP_AMERP_GetParentCompany_Details", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@CompanyID", ValidatePartNumberObj.Company_ID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ParentCompanyDetails.Add(new ParentCompanyObject
                                    {
                                        Company_ID = Convert.ToInt32(reader["Company_ID"].ToString()),
                                        Company_Name = reader["Company_Name"].ToString(),
                                        Company_Parent_ID = Convert.ToInt32(reader["Company_Parent_ID"].ToString())
                                    });
                                }
                            }
                        }
                    }
                    else
                    {
                        using (SqlCommand cmd = new SqlCommand("UP_AMERP_GetParentCompany_Details", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@CompanyID", ValidatePartNumberObj.Company_ID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    ParentCompanyDetails.Add(new ParentCompanyObject
                                    {
                                        Company_ID = Convert.ToInt32(reader["Company_ID"].ToString()),
                                        Company_Name = reader["Company_Name"].ToString(),
                                        Company_Parent_ID = Convert.ToInt32(reader["Company_Parent_ID"].ToString())
                                    });
                                }
                            }
                        }

                        IsDealerPart = true;
                    }

                    // Collect Parent Company IDs
                    string ParentCompanyIDs = string.Join(",", ParentCompanyDetails.Select(p => p.Company_ID));

                    if (!string.IsNullOrEmpty(ParentCompanyIDs))
                    {
                        Query = "SELECT * FROM GNM_Parts WHERE Parts_PartsNumber = @PartNumber AND Parts_PartPrefix = @Prefix AND Parts_ID != @Key AND Parts_IsLocal = 0 AND Company_ID IN (" + ParentCompanyIDs + ")";
                        using (SqlCommand cmd = new SqlCommand(Query, conn))
                        {
                            cmd.Parameters.AddWithValue("@PartNumber", PartNumber);
                            cmd.Parameters.AddWithValue("@Prefix", Prefix);
                            cmd.Parameters.AddWithValue("@Key", ValidatePartNumberObj.key);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (!reader.HasRows)
                                {
                                    Part = new { IsValid = true };
                                }
                                else
                                {
                                    Part = new { type = "2", IsValid = false, IsDealerPart };
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Part);
        }

        // ado sp
        #endregion

        #region ::: getPreviousDate /Mithun:::
        /// <summary>
        /// To select the All parts 
        /// </summary>       

        public static IActionResult getPreviousDate(getPreviousDateList getPreviousDateObj, string constring, int LogException)
        {
            string CurDate = "";

            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    string query = "";

                    if (string.IsNullOrEmpty(getPreviousDateObj.Currenteditmode))
                    {
                        query = "SELECT * FROM GNM_PartsPriceDetail WHERE Parts_ID = @PartID ORDER BY PartsPriceDetail_ID ASC";
                        using (SqlCommand command = new SqlCommand(query, connection))
                        {
                            command.Parameters.AddWithValue("@PartID", getPreviousDateObj.PartID);
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                List<GNM_PartsPriceDetail> liparts = new List<GNM_PartsPriceDetail>();

                                while (reader.Read())
                                {
                                    GNM_PartsPriceDetail part = new GNM_PartsPriceDetail
                                    {
                                        PartsPriceDetail_ID = reader.GetInt32(reader.GetOrdinal("PartsPriceDetail_ID")),
                                        Parts_ID = reader.GetInt32(reader.GetOrdinal("Parts_ID")),
                                        PartsPriceDetail_EffectiveFrom = reader.GetDateTime(reader.GetOrdinal("PartsPriceDetail_EffectiveFrom"))
                                        // Populate other properties as needed
                                    };
                                    liparts.Add(part);
                                }

                                if (liparts.Count > 0)
                                {
                                    GNM_PartsPriceDetail part = liparts.Last();
                                    CurDate = part.PartsPriceDetail_EffectiveFrom.ToString("dd-MMM-yyyy");
                                }
                            }
                        }
                    }
                    else
                    {
                        query = "SELECT TOP(2) * FROM GNM_PartsPriceDetail WHERE Parts_ID = @PartID ORDER BY PartsPriceDetail_ID DESC";
                        using (SqlCommand command = new SqlCommand(query, connection))
                        {
                            command.Parameters.AddWithValue("@PartID", getPreviousDateObj.PartID);
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                List<GNM_PartsPriceDetail> liparts = new List<GNM_PartsPriceDetail>();

                                while (reader.Read())
                                {
                                    GNM_PartsPriceDetail part = new GNM_PartsPriceDetail
                                    {
                                        PartsPriceDetail_ID = reader.GetInt32(reader.GetOrdinal("PartsPriceDetail_ID")),
                                        Parts_ID = reader.GetInt32(reader.GetOrdinal("Parts_ID")),
                                        PartsPriceDetail_EffectiveFrom = reader.GetDateTime(reader.GetOrdinal("PartsPriceDetail_EffectiveFrom"))
                                        // Populate other properties as needed
                                    };
                                    liparts.Add(part);
                                }

                                if (liparts.Count > 1)
                                {
                                    GNM_PartsPriceDetail part = liparts.Last();
                                    CurDate = part.PartsPriceDetail_EffectiveFrom.ToString("dd-MMM-yyyy");
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return CurDate;
            return new JsonResult(CurDate);
        }

        #endregion

        #region ::: PartProdExits /Mithun:::
        /// <summary>
        /// To check the Duplicate rows in details Grid
        /// </summary>       

        public static IActionResult PartProdExits(PartProdExitsList PartProdExitsObj, string constring, int LogException)
        {
            var jsonResult = default(dynamic);
            List<int> rowIndex = new List<int>();
            try
            {
                int Company_ID = Convert.ToInt32(PartProdExitsObj.Company_ID);
                JObject jObj = JObject.Parse(PartProdExitsObj.key);
                int ContPersrowcount = jObj["rows"].Count();

                using (SqlConnection connection = new SqlConnection(constring)) // Replace 'connectionString' with your actual connection string
                {
                    connection.Open();

                    for (int i = 0; i < ContPersrowcount; i++)
                    {
                        string ID = jObj["rows"][i]["ID"].ToString().Trim();
                        int PartsProductID = 0;
                        if (ID != "")
                        {
                            PartsProductID = Convert.ToInt32(ID);
                        }

                        int Parts_ID = Convert.ToInt32(jObj["rows"][i]["Part_ID"].ToString());
                        int Brand_ID = Convert.ToInt32(jObj["rows"][i]["Brand"].ToString());
                        int? ProductType_ID = jObj["rows"][i]["ProductType"].ToString() == "-1" ? (int?)null : Convert.ToInt32(jObj["rows"][i]["ProductType"].ToString());
                        int? Model_ID = string.IsNullOrEmpty(jObj["rows"][i]["Model"].ToString()) ? (int?)null : Convert.ToInt32(jObj["rows"][i]["Model"].ToString());

                        using (SqlCommand command = new SqlCommand())
                        {
                            command.Connection = connection;
                            command.CommandText = @"SELECT COUNT(*) FROM GNM_PartsProductAssociation 
                                            WHERE Parts_ID = @Parts_ID 
                                            AND Brand_ID = @Brand_ID 
                                            AND (ProductType_ID = @ProductType_ID OR (@ProductType_ID IS NULL AND ProductType_ID IS NULL)) 
                                            AND (Model_ID = @Model_ID OR (@Model_ID IS NULL AND Model_ID IS NULL)) 
                                            AND PartsProductAssociation_ID != @PartsProductID 
                                            AND Company_ID = @Company_ID";

                            command.Parameters.AddWithValue("@Parts_ID", Parts_ID);
                            command.Parameters.AddWithValue("@Brand_ID", Brand_ID);
                            command.Parameters.AddWithValue("@ProductType_ID", ProductType_ID.HasValue ? (object)ProductType_ID.Value : DBNull.Value);
                            command.Parameters.AddWithValue("@Model_ID", Model_ID.HasValue ? (object)Model_ID.Value : DBNull.Value);
                            command.Parameters.AddWithValue("@PartsProductID", PartsProductID);
                            command.Parameters.AddWithValue("@Company_ID", Company_ID);

                            int count = (int)command.ExecuteScalar();

                            if (count > 0)
                            {
                                rowIndex.Add(i);
                            }
                        }
                    }
                }

                jsonResult = new
                {
                    Exists = rowIndex.Count > 0,
                    rowsIndex = rowIndex.ToArray()
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    // Log your exception here
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);
        }

        #endregion

        #region ::: MakeLastRowEditable /Mithun:::
        /// <summary>
        ///  To Make Last Row Editable
        /// </summary>        

        public static IActionResult MakeLastRowEditable(MakeLastRowEditableList MakeLastRowEditableObj, string constring, int LogException)
        {
            GNM_PartsPriceDetail part = null;
            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    string query = @"
                    SELECT TOP 1 *
                    FROM GNM_PartsPriceDetail
                    WHERE Parts_ID = @PartID
                    ORDER BY PartsPriceDetail_ID DESC";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@PartID", MakeLastRowEditableObj.PartID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                part = new GNM_PartsPriceDetail
                                {
                                    PartsPriceDetail_ID = Convert.ToInt32(reader["PartsPriceDetail_ID"]),
                                    Parts_ID = Convert.ToInt32(reader["Parts_ID"]),
                                    // Initialize other properties as needed
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            // return part?.PartsPriceDetail_ID ?? -1; // Return -1 or another appropriate value if part is null
            return new JsonResult(part?.PartsPriceDetail_ID ?? -1);
        }

        #endregion

        #region ::: SelectProductType /Mithun:::
        /// <summary>
        /// To select ProductType
        /// </summary>       

        public static IActionResult SelectProductType(SelectProductTypeList SelectProductTypeObj, string constring, int LogException)
        {
            var jsonData = default(dynamic);
            try
            {
                List<object> prodTypeList = new List<object>();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    string query = @"
                SELECT ProductType_ID, ProductType_Name
                FROM GNM_ProductType
                WHERE ProductType_IsActive = 1 AND Brand_ID = @Brand_ID
                ORDER BY ProductType_Name";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Brand_ID", SelectProductTypeObj.id);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                prodTypeList.Add(new
                                {
                                    ID = reader["ProductType_ID"],
                                    Name = reader["ProductType_Name"]
                                });
                            }
                        }
                    }
                }

                jsonData = new
                {
                    ProductType = prodTypeList
                };

                //return Json(jsonData, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonData);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult("Error");
            }
        }

        #endregion

        #region ::: ValidateFunctionGroup /Mithun:::
        /// <summary>
        /// To Validate FunctionGroup 
        /// </summary>
        public static IActionResult ValidateFunctionGroup(ValidateFunctionGroupList ValidateFunctionGroupObj, string constring, int LogException)
        {
            var Part = default(dynamic);
            string FunGrp = Common.DecryptString(ValidateFunctionGroupObj.FunGrp);
            var jsonResponse = default(dynamic);
            GNM_FunctionGroup FunctionGList = null;
            try
            {
                //GNM_User User = (GNM_User)Session["UserDetails"];
                // GNM_User User = ValidateFunctionGroupObj.UserDetails.FirstOrDefault();


                List<GNM_FunctionGroup> liFG = new List<GNM_FunctionGroup>();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    string query = "SELECT * FROM GNM_FunctionGroup WHERE UPPER(FunctionGroup_Name) LIKE @FunGrp AND Company_ID = @Company_ID";
                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@FunGrp", "%" + FunGrp.ToUpper() + "%");
                        cmd.Parameters.AddWithValue("@Company_ID", ValidateFunctionGroupObj.Company_ID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                liFG.Add(new GNM_FunctionGroup
                                {
                                    FunctionGroup_ID = reader.GetInt32(reader.GetOrdinal("FunctionGroup_ID")),
                                    FunctionGroup_Name = reader.GetString(reader.GetOrdinal("FunctionGroup_Name")),
                                    FunctionGroup_IsActive = reader.GetBoolean(reader.GetOrdinal("FunctionGroup_IsActive")),
                                    Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID"))
                                });
                            }
                        }
                    }
                }

                if (liFG.Count == 0)
                {
                    Part = new
                    {
                        IsValid = false
                    };
                }
                else
                {
                    if (liFG.Count == 1)
                    {
                        FunctionGList = liFG.FirstOrDefault();
                    }
                    else
                    {
                        jsonResponse = new
                        {
                            Count = liFG.Count,
                            Name = liFG.Count > 0 ? FunGrp : ""
                        };
                        //return Json(jsonResponse, JsonRequestBehavior.AllowGet);
                        return new JsonResult(jsonResponse);
                    }

                    jsonResponse = new
                    {
                        IsValid = true,
                        IsActive = FunctionGList.FunctionGroup_IsActive,
                        ID = FunctionGList.FunctionGroup_ID,
                        Name = FunctionGList.FunctionGroup_Name
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                Part = new
                {
                    IsValid = false
                };
            }
            //return Json(jsonResponse, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResponse);
        }
        #endregion

        // front hand not changed
        #region ::: ValidateSupplierName /Mithun:::
        /// <summary>
        /// To Validate FunctionGroup 
        /// </summary>
        public static IActionResult ValidateSupplierName(ValidateSupplierNameList ValidateSupplierNameObj, string constring, int LogException)
        {
            var jsonData = default(dynamic);
            var Temp = default(dynamic);
            try
            {
                bool IsValidParty = false;
                bool IsExists = false;
                int PartID = Convert.ToInt32(ValidateSupplierNameObj.ID);
                string PartyName = Common.DecryptString(ValidateSupplierNameObj.Key);
                string mode = ValidateSupplierNameObj.mode;
                int Company_ID = Convert.ToInt32(ValidateSupplierNameObj.Company_ID);
                int Language_ID = Convert.ToInt32(ValidateSupplierNameObj.UserLanguageID);

                bool FilterPartyBasedonCompany = false;

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Check FilterPartyBasedonCompany
                    string paramQuery = "SELECT Param_value FROM GNM_CompParam WHERE Company_ID = @Company_ID AND Param_Name = 'FilterPartyBasedonCompany'";
                    using (SqlCommand cmd = new SqlCommand(paramQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                        var result = cmd.ExecuteScalar();
                        FilterPartyBasedonCompany = result != null && result.ToString().ToUpper() == "TRUE";
                    }

                    // Get PartyDetails
                    GNM_Party PartyDetails = null;
                    string partyQuery = FilterPartyBasedonCompany ?
                        "SELECT * FROM GNM_Party WHERE Party_Name = @PartyName AND Company_ID = @Company_ID AND PartyType = 4 AND Party_IsActive = 1" :
                        "SELECT * FROM GNM_Party WHERE Party_Name = @PartyName AND PartyType = 4 AND Party_IsActive = 1";

                    using (SqlCommand cmd = new SqlCommand(partyQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@PartyName", PartyName);
                        if (FilterPartyBasedonCompany)
                        {
                            cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                        }
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                PartyDetails = new GNM_Party
                                {
                                    Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                    Party_Name = reader.GetString(reader.GetOrdinal("Party_Name")),
                                    Party_IsActive = reader.GetBoolean(reader.GetOrdinal("Party_IsActive")),
                                    Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                    PartyType = reader.GetByte(reader.GetOrdinal("PartyType"))
                                };
                            }
                        }
                    }

                    IsValidParty = PartyDetails != null;

                    if (!IsValidParty)
                    {
                        // Get PartyDetailsList
                        List<GNM_Party> PartyDetailsList = new List<GNM_Party>();
                        string partyListQuery = FilterPartyBasedonCompany ?
                            "SELECT * FROM GNM_Party WHERE UPPER(Party_Name) LIKE @PartyName AND Company_ID = @Company_ID AND PartyType = 4 AND Party_IsActive = 1" :
                            "SELECT * FROM GNM_Party WHERE UPPER(Party_Name) LIKE @PartyName AND PartyType = 4 AND Party_IsActive = 1";

                        using (SqlCommand cmd = new SqlCommand(partyListQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@PartyName", "%" + PartyName.ToUpper() + "%");
                            if (FilterPartyBasedonCompany)
                            {
                                cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                            }
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    PartyDetailsList.Add(new GNM_Party
                                    {
                                        Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                        Party_Name = reader.GetString(reader.GetOrdinal("Party_Name")),
                                        Party_IsActive = reader.GetBoolean(reader.GetOrdinal("Party_IsActive")),
                                        Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                        PartyType = reader.GetByte(reader.GetOrdinal("PartyType"))
                                    });
                                }
                            }
                        }

                        if (PartyDetailsList.Count > 0)
                        {
                            if (PartyDetailsList.Count == 1)
                            {
                                PartyDetails = PartyDetailsList[0];
                                jsonData = new
                                {
                                    SupplierID = PartyDetails.Party_ID,
                                    Name = PartyDetails.Party_Name,
                                    IsExists = IsExists,
                                    IsValidPart = IsValidParty
                                };
                            }
                            else
                            {
                                jsonData = new
                                {
                                    Count = PartyDetailsList.Count,
                                    Name = PartyName
                                };
                            }
                            IsValidParty = true;
                        }
                    }
                    else
                    {
                        GNM_PartyLocale PartyLocaleDetails = null;
                        string partyLocaleQuery = FilterPartyBasedonCompany ?
                            "SELECT * FROM GNM_PartyLocale WHERE Party_Name = @PartyName AND Company_ID = @Company_ID AND Party_IsActive = 1 AND Language_ID = @Language_ID" :
                            "SELECT * FROM GNM_PartyLocale WHERE Party_Name = @PartyName AND Party_IsActive = 1 AND Language_ID = @Language_ID";

                        using (SqlCommand cmd = new SqlCommand(partyLocaleQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@PartyName", PartyName);
                            if (FilterPartyBasedonCompany)
                            {
                                cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                            }
                            cmd.Parameters.AddWithValue("@Language_ID", Language_ID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    PartyLocaleDetails = new GNM_PartyLocale
                                    {
                                        Party_Locale_ID = reader.GetInt32(reader.GetOrdinal("Party_Locale_ID")),
                                        Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                        Party_Name = reader.GetString(reader.GetOrdinal("Party_Name")),
                                        Party_Location = reader.GetString(reader.GetOrdinal("Party_Location")),
                                        Party_PaymentTerms = reader.GetString(reader.GetOrdinal("Party_PaymentTerms")),
                                        Language_ID = reader.GetInt32(reader.GetOrdinal("Language_ID")),
                                        Party_Address = reader.GetString(reader.GetOrdinal("Party_Address")),
                                        Party_Code = reader.GetString(reader.GetOrdinal("Party_Code"))
                                    };
                                }
                            }
                        }

                        int SuppID = PartyDetails.Party_ID;
                        if (mode == "add")
                        {
                            string existsQuery = "SELECT COUNT(*) FROM GNM_PartsSupplierDetail WHERE Parts_ID = @PartID AND Supplier_ID = @SuppID";
                            using (SqlCommand cmd = new SqlCommand(existsQuery, conn))
                            {
                                cmd.Parameters.AddWithValue("@PartID", PartID);
                                cmd.Parameters.AddWithValue("@SuppID", SuppID);
                                IsExists = (int)cmd.ExecuteScalar() > 0;
                            }
                        }
                        else if (mode == "edit")
                        {
                            int PrmID = Convert.ToInt32(ValidateSupplierNameObj.primID);
                            string existsQuery = "SELECT COUNT(*) FROM GNM_PartsSupplierDetail WHERE Parts_ID = @PartID AND Supplier_ID = @SuppID AND PartsSupplierDetail_ID != @PrmID";
                            using (SqlCommand cmd = new SqlCommand(existsQuery, conn))
                            {
                                cmd.Parameters.AddWithValue("@PartID", PartID);
                                cmd.Parameters.AddWithValue("@SuppID", SuppID);
                                cmd.Parameters.AddWithValue("@PrmID", PrmID);
                                IsExists = (int)cmd.ExecuteScalar() > 0;
                            }
                        }

                        jsonData = new
                        {
                            SupplierID = PartyDetails.Party_ID,
                            Name = PartyDetails.Party_Name,
                            IsExists = IsExists,
                            IsValidPart = IsValidParty
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }


        #endregion


        #region ::: validateModel /Mithun:::
        /// <summary>
        /// To Validate PartNumber for duplicate 
        /// </summary>
        public static IActionResult validateModel(validateModelList validateModelObj, string constring, int LogException)
        {
            var Model = default(dynamic);
            try
            {
                string ModelVal = Common.DecryptString(validateModelObj.ModelVal);
                //  GNM_User User = validateModelObj.UserDetails.FirstOrDefault();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    string query = "SELECT TOP 1 Model_IsActive, Model_ID FROM GNM_Model WHERE ProductType_ID = @Ptype AND UPPER(Model_Name) LIKE '%' + @ModelVal + '%'";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Ptype", validateModelObj.Ptype);
                        command.Parameters.AddWithValue("@ModelVal", ModelVal.ToUpper());

                        connection.Open();

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                Model = new
                                {
                                    IsValid = true,
                                    IsActive = reader.GetBoolean(reader.GetOrdinal("Model_IsActive")),
                                    ModelID = reader.GetInt32(reader.GetOrdinal("Model_ID"))
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            // return Uri.EscapeDataString(Newtonsoft.Json.JsonConvert.SerializeObject(Model));
            return new JsonResult(Uri.EscapeDataString(Newtonsoft.Json.JsonConvert.SerializeObject(Model)));
        }


        #endregion


        #region ::: SelectPartStockDropDowns /Mithun:::
        /// <summary>
        /// to Load Parts English Dropdowns
        /// </summary>
        public static IActionResult SelectPartStockDropDowns(SelectPartStockDropDownsList SelectPartStockDropDownsObj, string constring, int LogException)
        {
            //  GNM_User UserDetails = SelectPartStockDropDownsObj.UserDetails.FirstOrDefault();
            int companyID = SelectPartStockDropDownsObj.Company_ID;
            var jsonData = new object();
            int DefaultBranch = Convert.ToInt32(SelectPartStockDropDownsObj.Branch);
            List<GNM_WareHouse> warehouses = new List<GNM_WareHouse>();
            List<GNM_BinLocation> binLocations = new List<GNM_BinLocation>();
            List<GNM_Branch> branches = new List<GNM_Branch>();
            List<GNM_EmployeeBranch> employeeBranches = new List<GNM_EmployeeBranch>();

            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Retrieve warehouses
                    using (SqlCommand command = new SqlCommand(@"
                        SELECT WareHouse_ID, WareHouseName
                        FROM GNM_WareHouse
                        WHERE Company_ID = @CompanyID AND IsActive = 1", connection))
                    {
                        command.Parameters.AddWithValue("@CompanyID", companyID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                warehouses.Add(new GNM_WareHouse
                                {
                                    WareHouse_ID = Convert.ToInt32(reader["WareHouse_ID"]),
                                    WareHouseName = reader["WareHouseName"].ToString()
                                });
                            }
                        }
                    }

                    // Retrieve bin locations
                    using (SqlCommand command = new SqlCommand(@"
                        SELECT BinLocation_ID, BinLocation_Name
                        FROM GNM_BinLocation
                        WHERE Company_ID = @CompanyID AND BinLocation_IsActive = 1", connection))
                    {
                        command.Parameters.AddWithValue("@CompanyID", companyID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                binLocations.Add(new GNM_BinLocation
                                {
                                    BinLocation_ID = Convert.ToInt32(reader["BinLocation_ID"]),
                                    BinLocation_Name = reader["BinLocation_Name"].ToString()
                                });
                            }
                        }
                    }

                    // Retrieve branches
                    using (SqlCommand command = new SqlCommand(@"
                        SELECT Branch_ID, Branch_Name
                        FROM GNM_Branch
                        WHERE Company_ID = @CompanyID", connection))
                    {
                        command.Parameters.AddWithValue("@CompanyID", companyID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                branches.Add(new GNM_Branch
                                {
                                    Branch_ID = Convert.ToInt32(reader["Branch_ID"]),
                                    Branch_Name = reader["Branch_Name"].ToString()
                                });
                            }
                        }
                    }

                    // Retrieve employee branches
                    using (SqlCommand command = new SqlCommand(@"
                        SELECT Branch_ID
                        FROM GNM_EmployeeBranch
                        WHERE CompanyEmployee_ID = @EmployeeID", connection))
                    {
                        command.Parameters.AddWithValue("@EmployeeID", SelectPartStockDropDownsObj.Employee_ID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                employeeBranches.Add(new GNM_EmployeeBranch
                                {
                                    Branch_ID = Convert.ToInt32(reader["Branch_ID"])
                                });
                            }
                        }
                    }
                }

                // Construct jsonData object
                jsonData = new
                {
                    Branch = from a in branches
                             join b in employeeBranches on a.Branch_ID equals b.Branch_ID
                             orderby a.Branch_Name
                             select new
                             {
                                 ID = a.Branch_ID,
                                 Name = a.Branch_Name
                             },
                    DefBranch = DefaultBranch
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            // return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion

        #region ::: SelectWareHouseByBranch /Mithun:::
        /// <summary>
        /// to Load Parts English Dropdowns
        /// </summary>
        public static IActionResult SelectWareHouseByBranch(SelectWareHouseByBranchList SelectWareHouseByBranchObj, string constring, int LogException)
        {
            //GNM_User UserDetails = (GNM_User)Session["UserDetails"];
            //  GNM_User UserDetails = SelectWareHouseByBranchObj.UserDetails.FirstOrDefault();
            int companyID = SelectWareHouseByBranchObj.Company_ID;
            var jsonData = default(dynamic);
            int DefaultWH = 0;

            try
            {
                int wareMode = Convert.ToInt32(SelectWareHouseByBranchObj.wareMode);
                List<dynamic> warehouses = new List<dynamic>();
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    string query = wareMode == 0
                        ? "SELECT WareHouse_ID, WareHouseName, IsDefault FROM GNM_WareHouse WHERE Company_ID = @CompanyID AND IsActive = 1 AND Branch_ID = @BranchID AND WareHouseType_ID = 1 AND IsVisible = 1 ORDER BY WareHouseName"
                        : "SELECT WareHouse_ID, WareHouseName, IsDefault FROM GNM_WareHouse WHERE Company_ID = @CompanyID AND Branch_ID = @BranchID AND WareHouseType_ID = 1 ORDER BY WareHouseName";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@CompanyID", companyID);
                        command.Parameters.AddWithValue("@BranchID", SelectWareHouseByBranchObj.BranchID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                int wareHouseID = reader.GetInt32(reader.GetOrdinal("WareHouse_ID"));
                                string wareHouseName = reader.GetString(reader.GetOrdinal("WareHouseName"));
                                bool isDefault = reader.GetBoolean(reader.GetOrdinal("IsDefault"));

                                warehouses.Add(new
                                {
                                    ID = wareHouseID,
                                    Name = wareHouseName
                                });

                                if (isDefault)
                                {
                                    DefaultWH = wareHouseID;
                                }
                            }
                        }
                    }

                    if (SelectWareHouseByBranchObj.id != 0)
                    {
                        string stockQuery = "SELECT COUNT(*) FROM GNM_PartsStockDetail WHERE WareHouse_ID = @DefaultWH AND Parts_ID = @PartsID AND Branch_ID = @BranchID";
                        using (SqlCommand stockCommand = new SqlCommand(stockQuery, connection))
                        {
                            stockCommand.Parameters.AddWithValue("@DefaultWH", DefaultWH);
                            stockCommand.Parameters.AddWithValue("@PartsID", SelectWareHouseByBranchObj.id);
                            stockCommand.Parameters.AddWithValue("@BranchID", SelectWareHouseByBranchObj.BranchID);

                            int count = (int)stockCommand.ExecuteScalar();
                            DefaultWH = (count > 0) ? 0 : DefaultWH;
                        }
                    }
                }

                jsonData = new
                {
                    Ware = warehouses,
                    DefaultWareHouse = DefaultWH,
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion

        #region ::: Select Binlocation By wareHouse /Mithun:::
        /// <summary>
        /// to Load Parts English Dropdowns
        /// </summary>
        public static IActionResult SelectBinlocationByWareHouse(SelectBinlocationByWareHouseList SelectBinlocationByWareHouseObj, string constring, int LogException)
        {
            //GNM_User UserDetails = (GNM_User)Session["UserDetails"];
            // GNM_User UserDetails = SelectBinlocationByWareHouseObj.UserDetails.FirstOrDefault();
            int companyID = SelectBinlocationByWareHouseObj.Company_ID;
            var jsonData = default(dynamic);
            List<GNM_BinLocation> binLocations = new List<GNM_BinLocation>();
            int DefaultBinID = 0;

            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Retrieve bin locations
                    using (SqlCommand command = new SqlCommand("UP_SEL_AMERP_GetBinLocationsByWareHouse", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@CompanyID", companyID);
                        command.Parameters.AddWithValue("@WareHouseID", SelectBinlocationByWareHouseObj.WareHouseID);
                        command.Parameters.AddWithValue("@BranchID", SelectBinlocationByWareHouseObj.BranchID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var binLocation = new GNM_BinLocation
                                {
                                    BinLocation_ID = Convert.ToInt32(reader["BinLocation_ID"]),
                                    BinLocation_Name = reader["BinLocation_Name"].ToString(),
                                    BinLocation_IsDefault = Convert.ToBoolean(reader["BinLocation_IsDefault"])
                                };
                                binLocations.Add(binLocation);

                                if (binLocation.BinLocation_IsDefault)
                                {
                                    DefaultBinID = binLocation.BinLocation_ID;
                                }
                            }
                        }
                    }
                }

                // Construct jsonData object
                jsonData = new
                {
                    Bin = from a in binLocations
                          orderby a.BinLocation_Name
                          select new
                          {
                              ID = a.BinLocation_ID,
                              Name = a.BinLocation_Name
                          },
                    DefaultBin = DefaultBinID
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }


        #endregion

        #region ::: InsertPartsSockDetails /Mithun:::
        /// <summary>
        /// Method to insert the Parts Master Header Table
        /// </summary>   
        public static IActionResult InsertPartsSockDetails(InsertPartsSockDetailsList InsertPartsSockDetailsObj, string constring, int LogException)
        {
            //GNM_User User = (GNM_User)Session["UserDetails"];
            GNM_User User = InsertPartsSockDetailsObj.UserDetails.FirstOrDefault();
            int CompanyID = User.Company_ID;
            var x = default(dynamic);
            int? Nullableint = null;
            try
            {
                JTokenReader jsonReader = null;
                JObject jObj = JObject.Parse(InsertPartsSockDetailsObj.data);
                jsonReader = new JTokenReader(jObj["PartID"]);
                jsonReader.Read();
                int PartID = Convert.ToInt32(jsonReader.Value);

                jsonReader = new JTokenReader(jObj["StockDetails_ID"]);
                jsonReader.Read();
                string StockDetails_ID = jsonReader.Value.ToString();

                jsonReader = new JTokenReader(jObj["reordlvl"]);
                jsonReader.Read();
                int reordlvl = jsonReader.Value.ToString() == "" ? 0 : Convert.ToInt32(jsonReader.Value.ToString());

                jsonReader = new JTokenReader(jObj["reordqty"]);
                jsonReader.Read();
                int reordqty = jsonReader.Value.ToString() == "" ? 0 : Convert.ToInt32(jsonReader.Value.ToString());

                jsonReader = new JTokenReader(jObj["minqty"]);
                jsonReader.Read();
                decimal minqty = jsonReader.Value.ToString() == "" ? 0 : Convert.ToDecimal(jsonReader.Value.ToString());

                jsonReader = new JTokenReader(jObj["wrehu"]);
                jsonReader.Read();
                int wrehu = Convert.ToInt32(jsonReader.Value.ToString());

                jsonReader = new JTokenReader(jObj["bran"]);
                jsonReader.Read();
                int bran = Convert.ToInt32(jsonReader.Value.ToString());

                jsonReader = new JTokenReader(jObj["buffloc"]);
                jsonReader.Read();
                int? buffloc = jsonReader.Value.ToString() == "0" ? Nullableint : Convert.ToInt32(jsonReader.Value.ToString());

                jsonReader = new JTokenReader(jObj["binloc"]);
                jsonReader.Read();
                int binloc = Convert.ToInt32(jsonReader.Value.ToString());

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    SqlCommand cmd = new SqlCommand();
                    cmd.Connection = conn;

                    if (StockDetails_ID == "")
                    {
                        cmd.CommandText = @"
                    INSERT INTO GNM_PartsStockDetail
                    (Parts_ID, ReOrderLevel, ReOrderLevelQuantity, WareHouse_ID, Branch_ID, Company_ID, BinLocation_ID, BinlocationBuffer_ID, FreeStock, AllocatedQuantity, PickedQuantity, ReservedQuantity, BackOrderQuantity, PendingPurchaseOrderQuantity, PendingPartsOrderQuantity, DeviationStock, StockUsedInKits, MinOrderQty, GITQuantity, BinStock, TotalStock, IsBlocked)
                    OUTPUT INSERTED.PartsStockDetail_ID
                    VALUES (@PartID, @ReOrderLevel, @ReOrderLevelQuantity, @WareHouse_ID, @Branch_ID, @Company_ID, @BinLocation_ID, @BinlocationBuffer_ID, 0, 0, 0, 0, 0, 0, 0, 0, 0, @MinOrderQty, 0, 0, 0, 0)";

                        cmd.Parameters.AddWithValue("@PartID", PartID);
                        cmd.Parameters.AddWithValue("@ReOrderLevel", reordlvl);
                        cmd.Parameters.AddWithValue("@ReOrderLevelQuantity", reordqty);
                        cmd.Parameters.AddWithValue("@WareHouse_ID", wrehu);
                        cmd.Parameters.AddWithValue("@Branch_ID", bran);
                        cmd.Parameters.AddWithValue("@Company_ID", CompanyID);
                        cmd.Parameters.AddWithValue("@BinLocation_ID", binloc);
                        cmd.Parameters.AddWithValue("@BinlocationBuffer_ID", (object)buffloc ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@MinOrderQty", minqty);

                        int insertedId = (int)cmd.ExecuteScalar();
                        // gbl.InsertGPSDetails(Convert.ToInt32(InsertPartsSockDetailsObj.Company_ID), Convert.ToInt32(InsertPartsSockDetailsObj.Branch), Convert.ToInt32(InsertPartsSockDetailsObj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartsMaster",constring)), PartID, 0, 0, "Insert", false, Convert.ToInt32(InsertPartsSockDetailsObj.MenuID));
                        x = new
                        {
                            ID = insertedId
                        };
                    }
                    else
                    {
                        int ID = Convert.ToInt32(StockDetails_ID);

                        cmd.CommandText = @"
                    UPDATE GNM_PartsStockDetail
                    SET ReOrderLevel = @ReOrderLevel, 
                        ReOrderLevelQuantity = @ReOrderLevelQuantity, 
                        MinOrderQty = @MinOrderQty, 
                        WareHouse_ID = @WareHouse_ID, 
                        Branch_ID = @Branch_ID, 
                        BinLocation_ID = CASE WHEN BinLocation_ID != @BinLocation_ID THEN @BinLocation_ID ELSE BinLocation_ID END, 
                        OldBinLocation_ID = CASE WHEN BinLocation_ID != @BinLocation_ID THEN BinLocation_ID ELSE OldBinLocation_ID END,
                        BinlocationBuffer_ID = CASE WHEN BinlocationBuffer_ID != @BinlocationBuffer_ID THEN @BinlocationBuffer_ID ELSE BinlocationBuffer_ID END,
                        OldBufferBinLocation_ID = CASE WHEN BinlocationBuffer_ID != @BinlocationBuffer_ID THEN BinlocationBuffer_ID ELSE OldBufferBinLocation_ID END
                    WHERE PartsStockDetail_ID = @ID";

                        cmd.Parameters.AddWithValue("@ID", ID);
                        cmd.Parameters.AddWithValue("@ReOrderLevel", reordlvl);
                        cmd.Parameters.AddWithValue("@ReOrderLevelQuantity", reordqty);
                        cmd.Parameters.AddWithValue("@MinOrderQty", minqty);
                        cmd.Parameters.AddWithValue("@WareHouse_ID", wrehu);
                        cmd.Parameters.AddWithValue("@Branch_ID", bran);
                        cmd.Parameters.AddWithValue("@BinLocation_ID", binloc);
                        cmd.Parameters.AddWithValue("@BinlocationBuffer_ID", (object)buffloc ?? DBNull.Value);

                        cmd.ExecuteNonQuery();
                        // gbl.InsertGPSDetails(Convert.ToInt32(InsertPartsSockDetailsObj.Company_ID), Convert.ToInt32(InsertPartsSockDetailsObj.Branch), Convert.ToInt32(InsertPartsSockDetailsObj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartsMaster", constring)), PartID, 0, 0, "Updaate", false, Convert.ToInt32(InsertPartsSockDetailsObj.MenuID));
                        x = new
                        {
                            ID = ID
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                x = new
                {
                    PartsStockDetail_ID = ""
                };
            }
            //return Json(x, JsonRequestBehavior.AllowGet);
            return new JsonResult(x);
        }


        #endregion

        #region ::: Validate Branch /Mithun:::
        /// <summary>
        /// To check the Duplicate rows in details Grid
        /// </summary>       

        public static IActionResult ValidateBranchWareHouseDuplicate(ValidateBranchWareHouseDuplicateList ValidateBranchWareHouseDuplicateObj, string constring, int LogException)
        {
            bool isExists = false;
            //GNM_User User = (GNM_User)Session["UserDetails"];
            //GNM_User User = ValidateBranchWareHouseDuplicateObj.UserDetails.FirstOrDefault();

            try
            {
                int st = string.IsNullOrEmpty(ValidateBranchWareHouseDuplicateObj.StockID) ? 0 : Convert.ToInt32(ValidateBranchWareHouseDuplicateObj.StockID);

                string query = @"
                SELECT COUNT(*)
                FROM GNM_PartsStockDetail
                WHERE Parts_ID = @PartID
                    AND Branch_ID = @BrdID
                    AND WareHouse_ID = @WarhuID
                    AND PartsStockDetail_ID != @StockID";

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@PartID", ValidateBranchWareHouseDuplicateObj.PartID);
                        command.Parameters.AddWithValue("@BrdID", ValidateBranchWareHouseDuplicateObj.brdID);
                        command.Parameters.AddWithValue("@WarhuID", ValidateBranchWareHouseDuplicateObj.WarhuID);
                        command.Parameters.AddWithValue("@StockID", st);

                        connection.Open();
                        int count = (int)command.ExecuteScalar();
                        isExists = count > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            // return isExists;
            return new JsonResult(isExists);
        }


        #endregion

        #region::: Sales Non Sales YearReport Count /Mithun:::
        /// <summary>
        /// Sales Non Sales YearReport Count
        /// </summary>
        /// <param name="PartsID"></param>
        /// <returns></returns>
        public static IActionResult SalesNonSalesYearReportCount(SalesNonSalesYearReportCountList SalesNonSalesYearReportCountObj, string constring, int LogException)
        {
            int count = 0;
            string query = "";

            List<PartsConsumptionHistoryYear> ConsumptionClass = new List<PartsConsumptionHistoryYear>();
            try
            {
                //GNM_User User = (GNM_User)Session["UserDetails"];
                //  GNM_User User = SalesNonSalesYearReportCountObj.UserDetails.FirstOrDefault();
                int userid = SalesNonSalesYearReportCountObj.User_ID;
                int CompanyID = SalesNonSalesYearReportCountObj.Company_ID;

                query = "SELECT MAX(Year) AS Year, MAX(Parts_ID) AS PartsID, SUM(ISNULL(Issue_Quantity_Sales, 0)) AS SalesCount, SUM(ISNULL(Issue_Quantity_NonSales, 0)) AS NonSalesCount";
                query += " FROM PRT_StockConsumptionHistory WHERE Parts_ID = @PartsID GROUP BY Parts_ID, Year";

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    SqlCommand command = new SqlCommand(query, connection);
                    command.Parameters.AddWithValue("@PartsID", SalesNonSalesYearReportCountObj.PartsID);

                    connection.Open();
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            PartsConsumptionHistoryYear consumptionHistoryYear = new PartsConsumptionHistoryYear
                            {
                                Year = reader.IsDBNull(reader.GetOrdinal("Year")) ? default(int) : reader.GetInt32(reader.GetOrdinal("Year")),
                                PartsID = reader.IsDBNull(reader.GetOrdinal("PartsID")) ? default(int) : reader.GetInt32(reader.GetOrdinal("PartsID")),
                                SalesCount = reader.IsDBNull(reader.GetOrdinal("SalesCount")) ? default(int) : reader.GetInt32(reader.GetOrdinal("SalesCount")),
                                NonSalesCount = reader.IsDBNull(reader.GetOrdinal("NonSalesCount")) ? default(int) : reader.GetInt32(reader.GetOrdinal("NonSalesCount"))
                            };
                            ConsumptionClass.Add(consumptionHistoryYear);
                        }
                    }
                }
                count = ConsumptionClass.Count();
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                // return RedirectToAction("Error");
            }
            //return count;
            return new JsonResult(count);
        }

        #endregion

        #region ::: Select Parts Consumption Year /Mithun:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>

        public static IActionResult SelectSalesNonSalesYearReport(SelectSalesNonSalesYearReportList SelectSalesNonSalesYearReportObj, string constring, int LogException, string sidx, string sord, int page, int rows, string filters)
        {
            int count = 0;
            int total = 0;
            string query = "";
            dynamic jsonResult = null;

            List<PartsConsumptionHistoryYear> ConsumptionClass = new List<PartsConsumptionHistoryYear>();

            try
            {
                //GNM_User User = (GNM_User)Session["UserDetails"];
                //   GNM_User User = SelectSalesNonSalesYearReportObj.UserDetails.FirstOrDefault();
                int userid = SelectSalesNonSalesYearReportObj.User_ID;
                int CompanyID = SelectSalesNonSalesYearReportObj.Company_ID;

                query = "SELECT MAX(Year) AS Year, MAX(Parts_ID) AS PartsID, SUM(ISNULL(Issue_Quantity_Sales, 0)) AS SalesCount, SUM(ISNULL(Issue_Quantity_NonSales, 0)) AS NonSalesCount";
                query += " FROM PRT_StockConsumptionHistory WHERE Parts_ID = @PartsID GROUP BY Parts_ID, Year";

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    SqlCommand command = new SqlCommand(query, connection);
                    command.Parameters.AddWithValue("@PartsID", SelectSalesNonSalesYearReportObj.PartsID);

                    connection.Open();
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            PartsConsumptionHistoryYear consumptionHistoryYear = new PartsConsumptionHistoryYear
                            {
                                Year = reader.IsDBNull(reader.GetOrdinal("Year")) ? default(int) : reader.GetInt32(reader.GetOrdinal("Year")),
                                PartsID = reader.IsDBNull(reader.GetOrdinal("PartsID")) ? default(int) : reader.GetInt32(reader.GetOrdinal("PartsID")),
                                SalesCount = reader.IsDBNull(reader.GetOrdinal("SalesCount")) ? default(int) : reader.GetInt32(reader.GetOrdinal("SalesCount")),
                                NonSalesCount = reader.IsDBNull(reader.GetOrdinal("NonSalesCount")) ? default(int) : reader.GetInt32(reader.GetOrdinal("NonSalesCount"))
                            };
                            ConsumptionClass.Add(consumptionHistoryYear);
                        }
                    }
                }

                count = ConsumptionClass.Count();
                total = (int)Math.Ceiling((double)count / rows);

                var paginatedData = ConsumptionClass.Skip((page - 1) * rows).Take(rows).ToList();

                jsonResult = new
                {
                    TotalPages = total,
                    PageNo = page,
                    records = count,
                    YearData = paginatedData
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                // return RedirectToAction("Error");
            }

            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);
        }


        #endregion

        #region ::: Select Parts Consumption Count Month /Mihtun:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult SelectSalesNonSalesMonth(SelectSalesNonSalesMonthList SelectSalesNonSalesMonthObj, string constring, int LogException, string sidx, string sord, int page, int rows)
        {
            var jsonobj = default(dynamic);
            int count = 0;
            int total = 0;
            string query = "";
            var jsonResult = default(dynamic);
            List<PartsConsumptionHistoryMonth> ConsumptionClass = new List<PartsConsumptionHistoryMonth>();
            try
            {
                //GNM_User User = (GNM_User)Session["UserDetails"];
                // GNM_User User = SelectSalesNonSalesMonthObj.UserDetails.FirstOrDefault();
                int userid = SelectSalesNonSalesMonthObj.User_ID;
                int CompanyID = SelectSalesNonSalesMonthObj.Company_ID;

                query = "SELECT MAX(Year) AS Year, MAX(Month) AS Month, MAX(Parts_ID) AS PartsID, SUM(ISNULL(Issue_Quantity_Sales, 0)) AS SalesCount, " +
                        "SUM(ISNULL(Issue_Quantity_NonSales, 0)) AS NonSalesCount FROM PRT_StockConsumptionHistory " +
                        "WHERE Parts_ID = @PartsID AND Year = @Year GROUP BY Parts_ID, Year, Month";

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@PartsID", SelectSalesNonSalesMonthObj.PartsID);
                        cmd.Parameters.AddWithValue("@Year", SelectSalesNonSalesMonthObj.Year);

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                PartsConsumptionHistoryMonth partHistory = new PartsConsumptionHistoryMonth
                                {
                                    Year = reader.GetInt32(reader.GetOrdinal("Year")),
                                    Month = reader.GetInt32(reader.GetOrdinal("Month")),
                                    PartsID = reader.GetInt32(reader.GetOrdinal("PartsID")),
                                    SalesCount = reader.GetDecimal(reader.GetOrdinal("SalesCount")),
                                    NonSalesCount = reader.GetDecimal(reader.GetOrdinal("NonSalesCount"))
                                };
                                ConsumptionClass.Add(partHistory);
                            }
                        }
                    }
                }

                jsonResult = new
                {
                    TotalPages = total,
                    PageNo = page,
                    records = count,
                    MonthData = (from q in ConsumptionClass
                                 select new
                                 {
                                     Year = q.Year,
                                     Month = q.Month,
                                     MonthName = AMMSCore.Utilities.Utilities.GetMonthName(q.Month, SelectSalesNonSalesMonthObj.UserCulture),
                                     SalesCount = q.SalesCount,
                                     NonSalesCount = q.NonSalesCount
                                 }).ToArray()
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);

        }


        #endregion


        #region ::: Select Parts Consumption Count Day /Mithun:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult SelectSalesNonSalesDay(SelectSalesNonSalesDayList SelectSalesNonSalesDayObj, string constring, int LogException, string sidx, string sord, int page, int rows)
        {
            var jsonResult = default(dynamic);
            var ConsumptionClassFinal = new List<PartsConsumptionHistoryDay>();
            SqlConnection connection = null;
            SqlCommand command = null;
            SqlDataReader reader = null;

            try
            {
                connection = new SqlConnection(constring);
                connection.Open();

                // Query construction
                string query = "select CONVERT(date,MAX(SalesInvoiceDate),107) as Date,SUM(sd.Quantity-ISNULL(sd.ReturnQuantity,0)) as SalesCount,0 as NonSalesCount ";
                query += " from PRT_SalesInvoice s join PRT_SalesInvoiceDetails sd on s.SalesInvoice_ID=sd.SalesInvoice_ID ";
                query += " where Parts_ID=@PartsID and MONTH(SalesInvoiceDate)=@Month and FinancialYear=@Year group by Parts_ID,SalesInvoiceDate union all";
                query += " select CONVERT(date,MAX(DNSTNDate),107) as Date,0 as SalesCount,SUM(dnd.QuantityIssued-ISNULL(dnd.Returned_qty,0)) as NonSalesCount";
                query += " from PRT_DNSTN dn join PRT_DNSTNDetail dnd on dn.DNSTN_ID=dnd.DNSTN_ID where Parts_ID=@PartsID and MONTH(DNSTNDate)=@Month and FinancialYear=@Year";
                query += " group by Parts_ID,DNSTNDate";

                command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@PartsID", SelectSalesNonSalesDayObj.PartsID);
                command.Parameters.AddWithValue("@Month", SelectSalesNonSalesDayObj.Month);
                command.Parameters.AddWithValue("@Year", SelectSalesNonSalesDayObj.Year);

                reader = command.ExecuteReader();

                while (reader.Read())
                {
                    var consumptionDay = new PartsConsumptionHistoryDay();
                    consumptionDay.Date = Convert.ToDateTime(reader["Date"]);
                    consumptionDay.DateS = consumptionDay.Date.ToString("dd-MMM-yyyy");
                    consumptionDay.Month = SelectSalesNonSalesDayObj.Month;
                    consumptionDay.NonSalesCount = Convert.ToInt32(reader["NonSalesCount"]);
                    consumptionDay.SalesCount = Convert.ToInt32(reader["SalesCount"]);
                    consumptionDay.PartsID = SelectSalesNonSalesDayObj.PartsID;
                    consumptionDay.Year = SelectSalesNonSalesDayObj.Year;

                    ConsumptionClassFinal.Add(consumptionDay);
                }

                // Calculate total and count here if needed

                jsonResult = new
                {
                    TotalPages = 1, // Set total pages as needed
                    PageNo = page,
                    records = ConsumptionClassFinal.Count,
                    DayData = ConsumptionClassFinal
                };
            }
            catch (Exception ex)
            {
                // Handle exception logging or other actions here
                Console.WriteLine(ex.Message);
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            finally
            {
                // Ensure connections are properly closed
                if (reader != null)
                    reader.Close();
                if (command != null)
                    command.Dispose();
                if (connection != null && connection.State == ConnectionState.Open)
                    connection.Close();
            }

            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);
        }


        #endregion

        #region ::: Select Parts transaction Wise /Mithun:::
        /// <summary>
        /// To Select Records
        /// </summary>
        /// <returns>...</returns>

        public static IActionResult SelectSalesNonSalesTransaction(SelectSalesNonSalesTransactionList SelectSalesNonSalesTransactionObj, string constring, int LogException, string sidx, string sord, int page, int rows)
        {
            var jsonobj = default(dynamic);
            int count = 0;
            int total = 0;
            var jsonResult = default(dynamic);

            List<PartsConsumptionHistoryTransactionWise> ConsumptionClass = new List<PartsConsumptionHistoryTransactionWise>();
            List<PartsConsumptionHistoryTransactionWise> ConsumptionClassFinal = new List<PartsConsumptionHistoryTransactionWise>();
            try
            {
                //GNM_User User = (GNM_User)Session["UserDetails"];
                // GNM_User User = SelectSalesNonSalesTransactionObj.UserDetails.FirstOrDefault();
                int userid = SelectSalesNonSalesTransactionObj.User_ID;
                int CompanyID = SelectSalesNonSalesTransactionObj.Company_ID;

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    SqlCommand command = new SqlCommand("UP_SEL_AMERP_SelectSalesNonSalesTransaction", connection);
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@PartsID", SelectSalesNonSalesTransactionObj.PartsID);
                    command.Parameters.AddWithValue("@Year", SelectSalesNonSalesTransactionObj.Year);
                    command.Parameters.AddWithValue("@Month", SelectSalesNonSalesTransactionObj.Month);
                    command.Parameters.AddWithValue("@Day", SelectSalesNonSalesTransactionObj.Date.Day);

                    connection.Open();
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            ConsumptionClass.Add(new PartsConsumptionHistoryTransactionWise
                            {
                                InvoiceNumber = reader["InvoiceNumber"].ToString(),
                                SIID = reader["SIID"] != DBNull.Value ? Convert.ToInt32(reader["SIID"]) : 0,
                                DNNumber = reader["DNNumber"].ToString(),
                                DNID = reader["DNID"] != DBNull.Value ? Convert.ToInt32(reader["DNID"]) : 0,
                                Date = Convert.ToInt32(reader["Date"]),
                                SalesCount = Convert.ToInt32(reader["SalesCount"]),
                                NonSalesCount = Convert.ToInt32(reader["NonSalesCount"])
                            });
                        }
                    }
                }

                ConsumptionClassFinal = (from q in ConsumptionClass
                                         select new PartsConsumptionHistoryTransactionWise()
                                         {
                                             NonSalesCount = q.NonSalesCount,
                                             SalesCount = q.SalesCount,
                                             InvoiceNumber = q.InvoiceNumber,
                                             DNID = q.DNID,
                                             DNNumber = q.DNNumber,
                                             SIID = q.SIID
                                         }).ToList();

                jsonResult = new
                {
                    TotalPages = total,
                    PageNo = page,
                    records = count,
                    TransactionData = ConsumptionClassFinal
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);
        }


        #endregion

        #region ::: SelAllPendingPurchaseOrders /Mithun:::
        /// <summary>
        /// To display all the records
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult SelAllPendingPurchaseOrders(SelAllPendingPurchaseOrdersList SelAllPendingPurchaseOrdersObj, string constring, int LogException, string sidx, string sord, int page, int rows)
        {
            var jsonResult = default(dynamic);
            //GNM_User User = (GNM_User)Session["UserDetails"];
            //  GNM_User User = SelAllPendingPurchaseOrdersObj.UserDetails.FirstOrDefault();
            int companyID = SelAllPendingPurchaseOrdersObj.Company_ID;
            int userid = SelAllPendingPurchaseOrdersObj.User_ID;
            int count = 0;
            int total = 0;
            int branchID = Convert.ToInt32(SelAllPendingPurchaseOrdersObj.Branch);
            try
            {
                List<PendingPO> poPartsList = new List<PendingPO>();

                // First Query
                string query1 = "SELECT b.PurchaseOrder_ID, b.PurchaseOrderNumber, " +
                                "(ISNULL(a.ApprovedQuantity, 0) - ISNULL(a.InvoicedQuantity, 0) - ISNULL(a.BackOrderCancelledQuantity, 0)) AS Quantity, " +
                                "b.POStatus_ID, 0 AS IsPO, 'Purchase Order' AS Type " +
                                "FROM PRT_PurchaseOrderPartsDetail a " +
                                "INNER JOIN PRT_PurchaseOrder b ON a.PurchaseOrder_ID = b.PurchaseOrder_ID " +
                                "WHERE a.Parts_ID = @partsID AND b.Company_ID = @companyID AND b.Branch_ID = @branchID " +
                                "AND (ISNULL(a.ApprovedQuantity, 0) - ISNULL(a.InvoicedQuantity, 0) - ISNULL(a.BackOrderCancelledQuantity, 0)) > 0 " +
                                "AND WareHouse_ID = @warehouseID";

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(query1, connection))
                    {
                        command.Parameters.AddWithValue("@partsID", SelAllPendingPurchaseOrdersObj.PartsID);
                        command.Parameters.AddWithValue("@companyID", companyID);
                        command.Parameters.AddWithValue("@branchID", branchID);
                        command.Parameters.AddWithValue("@warehouseID", SelAllPendingPurchaseOrdersObj.warehouseID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                poPartsList.Add(new PendingPO
                                {
                                    PurchaseOrder_ID = reader.GetInt32(reader.GetOrdinal("PurchaseOrder_ID")),
                                    PurchaseOrderNumber = reader.GetString(reader.GetOrdinal("PurchaseOrderNumber")),
                                    Quantity = reader.GetDecimal(reader.GetOrdinal("Quantity")),
                                    POStatus_ID = reader.GetInt32(reader.GetOrdinal("POStatus_ID")),
                                    IsPO = reader.GetInt32(reader.GetOrdinal("IsPO")),
                                    Type = reader.GetString(reader.GetOrdinal("Type"))
                                });
                            }
                        }
                    }
                }

                // Fetch the status IDs from the database
                int poStatusID, poStatusApprovedID, poStatusCancelledID, poStatusPartialID, poStatusRejectedID;
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand("SELECT RMD.RefMasterDetail_ID, RM.RefMaster_Name FROM GNM_RefMasterDetail RMD JOIN GNM_RefMaster RM ON RMD.RefMaster_ID = RM.RefMaster_ID WHERE RM.RefMaster_Name = 'POSTATUS' AND RMD.RefMasterDetail_Short_Name = 'Closed'", connection))
                    {
                        poStatusID = (int)command.ExecuteScalar();
                    }
                    using (SqlCommand command = new SqlCommand("SELECT RMD.RefMasterDetail_ID, RM.RefMaster_Name FROM GNM_RefMasterDetail RMD JOIN GNM_RefMaster RM ON RMD.RefMaster_ID = RM.RefMaster_ID WHERE RM.RefMaster_Name = 'POSTATUS' AND RMD.RefMasterDetail_Short_Name = 'Approved'", connection))
                    {
                        poStatusApprovedID = (int)command.ExecuteScalar();
                    }
                    using (SqlCommand command = new SqlCommand("SELECT RMD.RefMasterDetail_ID, RM.RefMaster_Name FROM GNM_RefMasterDetail RMD JOIN GNM_RefMaster RM ON RMD.RefMaster_ID = RM.RefMaster_ID WHERE RM.RefMaster_Name = 'POSTATUS' AND RMD.RefMasterDetail_Short_Name = 'Cancelled'", connection))
                    {
                        poStatusCancelledID = (int)command.ExecuteScalar();
                    }
                    using (SqlCommand command = new SqlCommand("SELECT RMD.RefMasterDetail_ID, RM.RefMaster_Name FROM GNM_RefMasterDetail RMD JOIN GNM_RefMaster RM ON RMD.RefMaster_ID = RM.RefMaster_ID WHERE RM.RefMaster_Name = 'POSTATUS' AND RMD.RefMasterDetail_Short_Name = 'Partial'", connection))
                    {
                        poStatusPartialID = (int)command.ExecuteScalar();
                    }
                    using (SqlCommand command = new SqlCommand("SELECT RMD.RefMasterDetail_ID, RM.RefMaster_Name FROM GNM_RefMasterDetail RMD JOIN GNM_RefMaster RM ON RMD.RefMaster_ID = RM.RefMaster_ID WHERE RM.RefMaster_Name = 'POSTATUS' AND RMD.RefMasterDetail_Short_Name = 'Rejected'", connection))
                    {
                        poStatusRejectedID = (int)command.ExecuteScalar();
                    }
                }

                // Filter the results
                poPartsList = poPartsList.Where(a => a.POStatus_ID != poStatusID && (a.POStatus_ID == poStatusApprovedID || a.POStatus_ID == poStatusPartialID) && a.POStatus_ID != poStatusRejectedID && a.POStatus_ID != poStatusCancelledID).ToList();

                // Second Query
                string query2 = "SELECT a.StockTransferNumber AS PurchaseOrderNumber, a.StockTransferRequest_ID AS PurchaseOrder_ID, " +
                                "ISNULL(b.ApprovedQuantity, 0) - ISNULL(e.IssuedQuantity, 0) AS Quantity, c.PartsOrderStatus_ID AS POStatus_ID, " +
                                "1 AS IsPO, 'Stock Transfer Request' AS Type " +
                                "FROM PRT_StockTransferRequest a " +
                                "INNER JOIN PRT_StockTransferRequestDetails b ON a.StockTransferRequest_ID = b.StockTransferRequest_ID " +
                                "LEFT OUTER JOIN PRT_PartsOrder c ON a.PartsOrder_ID = c.PartsOrder_ID " +
                                "INNER JOIN PRT_PartsOrderPartsDetails d ON c.PartsOrder_ID = d.PartsOrder_ID AND d.Parts_ID = @partsID " +
                                "LEFT OUTER JOIN PRT_PartsOrderAllocation e ON d.PartsOrderPartsDetail_ID = e.PartsOrderPartsDetail_ID AND e.WareHouse_ID = a.SupplyingWareHouse_ID " +
                                "WHERE a.StockTransferRequest_ID NOT IN (SELECT StockTransferRequest_ID FROM PRT_StockReceiptGRN INNER JOIN PRT_DNSTN ON PRT_StockReceiptGRN.StockTransferNote_ID = PRT_DNSTN.StockTransferRequest_ID) " +
                                "AND b.Parts_ID = @partsID AND a.RequestingWareHouse_ID = @warehouseID AND a.Company_ID = @companyID AND a.Branch_ID = @branchID";

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(query2, connection))
                    {
                        command.Parameters.AddWithValue("@partsID", SelAllPendingPurchaseOrdersObj.PartsID);
                        command.Parameters.AddWithValue("@companyID", companyID);
                        command.Parameters.AddWithValue("@branchID", branchID);
                        command.Parameters.AddWithValue("@warehouseID", SelAllPendingPurchaseOrdersObj.warehouseID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                poPartsList.Add(new PendingPO
                                {
                                    PurchaseOrder_ID = reader.GetInt32(reader.GetOrdinal("PurchaseOrder_ID")),
                                    PurchaseOrderNumber = reader.GetString(reader.GetOrdinal("PurchaseOrderNumber")),
                                    Quantity = reader.GetDecimal(reader.GetOrdinal("Quantity")),
                                    POStatus_ID = reader.GetInt32(reader.GetOrdinal("POStatus_ID")),
                                    IsPO = reader.GetInt32(reader.GetOrdinal("IsPO")),
                                    Type = reader.GetString(reader.GetOrdinal("Type"))
                                });
                            }
                        }
                    }
                }

                // Fetch the status IDs for the second query
                int poStatusIDSTN, poStatusApprovedIDSTN, poStatusRejectedIDSTN;
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand("SELECT RMD.RefMasterDetail_ID, RM.RefMaster_Name FROM GNM_RefMasterDetail RMD JOIN GNM_RefMaster RM ON RMD.RefMaster_ID = RM.RefMaster_ID WHERE RM.RefMaster_Name = 'PARTSORDERSTATUS' AND RMD.RefMasterDetail_Short_Name = 'Rejected'", connection))
                    {
                        poStatusRejectedIDSTN = (int)command.ExecuteScalar();
                    }
                    using (SqlCommand command = new SqlCommand("SELECT RMD.RefMasterDetail_ID, RM.RefMaster_Name FROM GNM_RefMasterDetail RMD JOIN GNM_RefMaster RM ON RMD.RefMaster_ID = RM.RefMaster_ID WHERE RM.RefMaster_Name = 'PARTSORDERSTATUS' AND RMD.RefMasterDetail_Short_Name = 'Closed'", connection))
                    {
                        poStatusIDSTN = (int)command.ExecuteScalar();
                    }
                    using (SqlCommand command = new SqlCommand("SELECT RMD.RefMasterDetail_ID, RM.RefMaster_Name FROM GNM_RefMasterDetail RMD JOIN GNM_RefMaster RM ON RMD.RefMaster_ID = RM.RefMaster_ID WHERE RM.RefMaster_Name = 'PARTSORDERSTATUS' AND RMD.RefMasterDetail_Short_Name = 'Accepted'", connection))
                    {
                        poStatusApprovedIDSTN = (int)command.ExecuteScalar();
                    }
                }

                // Filter the second query results
                poPartsList = poPartsList.Where(a => a.POStatus_ID != poStatusIDSTN && a.POStatus_ID == poStatusApprovedIDSTN && a.POStatus_ID != poStatusRejectedIDSTN).ToList();

                count = poPartsList.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                jsonResult = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = poPartsList.Skip((page - 1) * rows).Take(rows).ToList()
                };
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);
        }


        #endregion

        #region ::: SelAllPendingPurchaseInvoice /Mithun:::
        /// <summary>
        /// To display all the records
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult SelAllPendingPurchaseInvoice(SelAllPendingPurchaseInvoiceList SelAllPendingPurchaseInvoiceObj, string constring, int LogException, string sidx, string sord, int page, int rows)
        {
            var jsonResult = default(dynamic);
            //GNM_User User = (GNM_User)Session["UserDetails"];
            //  GNM_User User = SelAllPendingPurchaseInvoiceObj.UserDetails.FirstOrDefault();
            int companyID = SelAllPendingPurchaseInvoiceObj.Company_ID;
            int userid = SelAllPendingPurchaseInvoiceObj.User_ID;
            int count = 0;
            int total = 0;
            int branchID = Convert.ToInt32(SelAllPendingPurchaseInvoiceObj.Branch);
            List<PendingPI> poPartsList = new List<PendingPI>();

            try
            {
                using (var connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Query for Purchase Invoices
                    string query1 = @"select a.PurchaseInvoiceNumber, a.PurchaseInvoice_ID, b.Quantity, 0 as IsPI, 'Purchase Invoice' as Type 
                              from PRT_PurchaseInvoice a 
                              inner join PRT_PurchaseInvoicePartsDetails b on a.PurchaseInvoice_ID = b.PurchaseInvoice_ID 
                              where b.Parts_ID = @PartsID and a.Company_ID = @CompanyID and a.PurchaseGRN_ID is null and b.WareHouse_ID = @WarehouseID";

                    using (var command = new SqlCommand(query1, connection))
                    {
                        command.Parameters.AddWithValue("@PartsID", SelAllPendingPurchaseInvoiceObj.partsID);
                        command.Parameters.AddWithValue("@CompanyID", companyID);
                        command.Parameters.AddWithValue("@WarehouseID", SelAllPendingPurchaseInvoiceObj.warehouseID);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                poPartsList.Add(new PendingPI
                                {
                                    PurchaseInvoice_ID = reader.GetInt32(reader.GetOrdinal("PurchaseInvoice_ID")),
                                    PurchaseInvoiceNumber = reader.GetString(reader.GetOrdinal("PurchaseInvoiceNumber")),
                                    Quantity = reader.GetDecimal(reader.GetOrdinal("Quantity")),
                                    IsPI = reader.GetInt32(reader.GetOrdinal("IsPI")),
                                    Type = reader.GetString(reader.GetOrdinal("Type"))
                                });
                            }
                        }
                    }

                    // Query for Stock Transfer Notes
                    string query2 = @"select PRT_DNSTN.DNSTN_ID as PurchaseInvoice_ID, PRT_DNSTN.DNSTNNumber as PurchaseInvoiceNumber, z.QuantityIssued as Quantity, 1 as IsPI, 'Stock Transfer Note' as Type 
                              from PRT_DNSTN  
                              inner join (select a.DNSTN_ID, SUM(ISNULL(b.QuantityIssued, 0)) as QuantityIssued 
                                          from PRT_DNSTN a 
                                          inner join PRT_DNSTNDetail b on a.DNSTN_ID = b.DNSTN_ID 
                                          inner join PRT_StockTransferRequest c on c.StockTransferRequest_ID = a.StockTransferRequest_ID 
                                          where b.Parts_ID = @PartsID and b.Warehouse_Id = c.SupplyingWareHouse_ID and a.DNSTN_ID not in 
                                                (select StockTransferNote_ID from PRT_StockReceiptGRN) 
                                          group by a.DNSTN_ID) as z on PRT_DNSTN.DNSTN_ID = z.DNSTN_ID";

                    using (var command = new SqlCommand(query2, connection))
                    {
                        command.Parameters.AddWithValue("@PartsID", SelAllPendingPurchaseInvoiceObj.partsID);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                poPartsList.Add(new PendingPI
                                {
                                    PurchaseInvoice_ID = reader.GetInt32(reader.GetOrdinal("PurchaseInvoice_ID")),
                                    PurchaseInvoiceNumber = reader.GetString(reader.GetOrdinal("PurchaseInvoiceNumber")),
                                    Quantity = reader.GetDecimal(reader.GetOrdinal("Quantity")),
                                    IsPI = reader.GetInt32(reader.GetOrdinal("IsPI")),
                                    Type = reader.GetString(reader.GetOrdinal("Type"))
                                });
                            }
                        }
                    }
                }

                count = poPartsList.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }
                jsonResult = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = poPartsList.Skip((page - 1) * rows).Take(rows).ToList()
                };
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);
        }


        #endregion

        // not changed in front hand
        #region ::: Select Field Search Salvage Parts /Mithun:::
        /// <summary>
        /// To Select Field Search Salvage Parts
        /// </summary>
        /// <param name="sidx"></param>
        /// <param name="sord"></param>
        /// <param name="page"></param>
        /// <param name="rows"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public static IActionResult SelectFieldSearchSalvageParts(SelectFieldSearchSalvagePartsList SelectFieldSearchSalvagePartsObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters)
        {
            string value = Common.DecryptString(SelectFieldSearchSalvagePartsObj.value);
            int Company_ID = Convert.ToInt32(SelectFieldSearchSalvagePartsObj.Company_ID);
            int SalvagePartid = 0;
            int ParentCompID = 0;

            List<FieldSearch> fieldSearchList = new List<FieldSearch>();
            IQueryable<FieldSearch> flSrch = null;
            int Count = 0;
            int Total = 0;

            using (SqlConnection connection = new SqlConnection(constring))
            {
                connection.Open();

                // Retrieve SalvagePartid
                string salvageQuery = "SELECT RefMasterDetail_ID FROM GNM_RefMasterDetail WHERE RefMasterDetail_Name = 'Salvage'";
                using (SqlCommand command = new SqlCommand(salvageQuery, connection))
                {
                    SalvagePartid = (int)command.ExecuteScalar();
                }

                // Retrieve ParentCompID
                string parentCompQuery = "SELECT Company_Parent_ID FROM GNM_Company WHERE Company_ID = @Company_ID";
                using (SqlCommand command = new SqlCommand(parentCompQuery, connection))
                {
                    command.Parameters.AddWithValue("@Company_ID", Company_ID);
                    ParentCompID = (int)command.ExecuteScalar();
                }

                // FilterToolBar Search
                //if (_search == "true")
                //{
                //    Filters filter = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                //    if (filters.rules.Count > 0)
                //    {
                //        value = Common.DecryptString(filters.rules.ElementAt(0).data);
                //    }
                //    else
                //    {
                //        value = "";
                //    }
                //}

                // Build the query
                string query = "SELECT Parts_ID, Parts_PartsNumber, Parts_PartsDescription, Parts_PartPrefix FROM GNM_Parts WHERE ";
                if (value == "")
                {
                    query += "(Company_ID = @Company_ID AND PartType = @SalvagePartid OR Company_ID = @ParentCompID AND PartType = @SalvagePartid) AND Parts_IsActive = 1";
                }
                else
                {
                    query += "Parts_PartsNumber LIKE '%' + @value + '%' AND PartType = @SalvagePartid AND Parts_IsActive = 1 AND (Company_ID = @Company_ID OR Company_ID = @ParentCompID)";
                }

                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Company_ID", Company_ID);
                    command.Parameters.AddWithValue("@SalvagePartid", SalvagePartid);
                    command.Parameters.AddWithValue("@ParentCompID", ParentCompID);
                    command.Parameters.AddWithValue("@value", value);

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            FieldSearch fs = new FieldSearch()
                            {
                                ID = reader.GetInt32(reader.GetOrdinal("Parts_ID")),
                                Name = reader.GetString(reader.GetOrdinal("Parts_PartsNumber")),
                                Description = reader.GetString(reader.GetOrdinal("Parts_PartsDescription")),
                                Partprefix = reader.GetString(reader.GetOrdinal("Parts_PartPrefix"))
                            };
                            fieldSearchList.Add(fs);
                        }
                    }
                }
            }

            // Convert List to IQueryable
            flSrch = fieldSearchList.AsQueryable();

            // Sorting 
            flSrch = flSrch.OrderByField(sidx, sord);

            Count = flSrch.Count();
            Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;

            if (Count < (rows * page) && Count != 0)
            {
                page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
            }

            var jsonData = new
            {
                total = Total,
                page = page,
                records = Count,
                rows = (from q in flSrch
                        select new
                        {
                            ID = q.ID,
                            Partprefix = q.Partprefix,
                            Name = q.Name,
                            q.Description,
                            Select = "<a title=" + CommonFunctionalities.GetResourceString(SelectFieldSearchSalvagePartsObj.UserCulture.ToString(), "select").ToString() + " href='#' style='font-size: 13px;' id='" + q.ID + "' class='FieldSrch' style='cursor:pointer'><i class='fa fa-check'></i></a>",
                        }
                ).ToList().Paginate(page, rows),
            };

            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }


        #endregion

        #region::: Select Part Stock Serial Number DetailsCount /Mithun:::
        /// <summary>
        /// Select Part Stock Serial Number DetailsCount
        /// </summary>
        /// <param name="PartsStockDetails_ID"></param>
        /// <returns></returns>
        public static IActionResult SelectPartStockSerialNumberDetailsCount(SelectPartStockSerialNumberDetailsCountList SelectPartStockSerialNumberDetailsCountObj, string constring, int LogException)
        {
            int Count = 0;
            try
            {
                //FSMTransactionsEntities PartsStockSerialNumClient = new FSMTransactionsEntities();
                //List<CoreGNM_PARTSSTOCKSERIALNUMBERDET> RecordCount = PartsStockSerialNumClient.CoreGNM_PARTSSTOCKSERIALNUMBERDET.Where(p => p.PARTSSTOCKDETAIL_ID == PartsStockDetails_ID).ToList();
                //Count = RecordCount.Count;

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    string query = @"
                    SELECT COUNT(*)
                    FROM CoreGNM_PARTSSTOCKSERIALNUMBERDET
                    WHERE PARTSSTOCKDETAIL_ID = @PartsStockDetails_ID";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@PartsStockDetails_ID", SelectPartStockSerialNumberDetailsCountObj.PartsStockDetails_ID);

                        Count = (int)cmd.ExecuteScalar();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Count;
            return new JsonResult(Count);
        }

        #endregion


        #region:::Select Part Stock Serial NumberDetails  /Mithun:::
        /// <summary>
        /// to Select Part Stock Serial NumberDetails
        /// </summary>
        /// <param name="SelectPartStockSerialNumberDetailsObj"></param>
        /// <param name="constring"></param>
        /// <param name="LogException"></param>
        /// <param name="sidx"></param>
        /// <param name="sord"></param>
        /// <param name="page"></param>
        /// <param name="rows"></param>
        /// <returns></returns>
        public static IActionResult SelectPartStockSerialNumberDetails(SelectPartStockSerialNumberDetailsList SelectPartStockSerialNumberDetailsObj, string constring, int LogException, string sidx, string sord, int page, int rows)
        {
            var jsonData = default(dynamic);
            int Count = 0;
            int Total = 0;
            try
            {
                //FSMTransactionsEntities PartsStockSerialNumClient = new FSMTransactionsEntities();
                IQueryable<CoreGNM_PARTSSTOCKSERIALNUMBERDET> PSSNIQ = null;
                // List<CoreGNM_PARTSSTOCKSERIALNUMBERDET> PartsStockDetailSerialNumList = PartsStockSerialNumClient.CoreGNM_PARTSSTOCKSERIALNUMBERDET.Where(e => e.PARTSSTOCKDETAIL_ID == PartsStockDetails_ID).ToList();

                List<CoreGNM_PARTSSTOCKSERIALNUMBERDET> PartsStockDetailSerialNumList = new List<CoreGNM_PARTSSTOCKSERIALNUMBERDET>();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    string query = @"
                    SELECT SERIALNUMBER, RECEIPTREFERNCE, RECEIPTREFERNCEDATE, ISSUEREFERENCE, ISSUEDATE
                    FROM CoreGNM_PARTSSTOCKSERIALNUMBERDET
                    WHERE PARTSSTOCKDETAIL_ID = @PartsStockDetails_ID";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@PartsStockDetails_ID", SelectPartStockSerialNumberDetailsObj.PartsStockDetails_ID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                PartsStockDetailSerialNumList.Add(new CoreGNM_PARTSSTOCKSERIALNUMBERDET
                                {
                                    SERIALNUMBER = reader["SERIALNUMBER"].ToString(),
                                    RECEIPTREFERNCE = reader["RECEIPTREFERNCE"] == DBNull.Value ? "" : reader["RECEIPTREFERNCE"].ToString(),
                                    RECEIPTREFERNCEDATE = reader["RECEIPTREFERNCEDATE"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["RECEIPTREFERNCEDATE"]),
                                    ISSUEREFERENCE = reader["ISSUEREFERENCE"] == DBNull.Value ? "" : reader["ISSUEREFERENCE"].ToString(),
                                    ISSUEDATE = reader["ISSUEDATE"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["ISSUEDATE"])
                                });
                            }
                        }
                    }
                }


                PSSNIQ = (from PL in PartsStockDetailSerialNumList
                          select new CoreGNM_PARTSSTOCKSERIALNUMBERDET()
                          {
                              SERIALNUMBER = PL.SERIALNUMBER,
                              RECEIPTREFERNCE = PL.RECEIPTREFERNCE == null ? "" : PL.RECEIPTREFERNCE,
                              RECEIPTREFERNCEDATE = PL.RECEIPTREFERNCEDATE,
                              ISSUEREFERENCE = PL.ISSUEREFERENCE == null ? "" : PL.ISSUEREFERENCE,
                              ISSUEDATE = PL.ISSUEDATE
                          }).ToList().AsQueryable();

                PSSNIQ = PSSNIQ.OrderByField<CoreGNM_PARTSSTOCKSERIALNUMBERDET>(sidx, sord);

                Count = PSSNIQ.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;

                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                jsonData = new
                {
                    total = Total,
                    page = page,
                    records = Count,
                    data = (from q in PSSNIQ.AsEnumerable()
                            select new
                            {
                                SerialNumber = q.SERIALNUMBER,
                                ReceiptReference = q.RECEIPTREFERNCE,
                                ReceiptReferenceDate = q.RECEIPTREFERNCEDATE == null ? "" : Convert.ToDateTime(q.RECEIPTREFERNCEDATE).ToString("dd-MMM-yyyy"),
                                IssueReference = q.ISSUEREFERENCE,
                                IssueReferenceDate = q.ISSUEDATE == null ? "" : Convert.ToDateTime(q.ISSUEDATE).ToString("dd-MMM-yyyy")
                            }
                   ).ToList().Paginate(page, rows),
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }
        #endregion

        #region ::: To Get GetObjectID /Mithun:::
        /// <summary>
        /// To GetObjectID the record
        /// </summary>
        /// <returns>...</returns>
        public static IActionResult GetObjectID(GetObjectIDList GetObjectIDObj, string constring, int LogException)
        {
            int objectID = 0;

            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    string query = "SELECT Object_ID FROM GNM_Object WHERE UPPER(Object_Name) = @Name";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Name", GetObjectIDObj.name.ToUpper());

                        object result = command.ExecuteScalar();
                        if (result != null)
                        {
                            objectID = Convert.ToInt32(result);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return objectID;
            return new JsonResult(objectID);
        }

        #endregion

        #region :::Get ProductID /Mithun:::
        /// <summary>
        /// to get Product ID
        /// </summary>
        /// <param name="GetProductIDObj"></param>
        /// <param name="constring"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult GetProductID(GetProductIDList GetProductIDObj, string constring, int LogException)
        {
            var jsondata = default(dynamic);
            var getObjectID = default(dynamic);
            var value = Common.DecryptString(GetProductIDObj.SerialNumber);

            try
            {
                int productid = 0;
                int ProductMstrObjectId = 0;

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    string query = "SELECT Product_ID FROM GNM_Product WHERE UPPER(Product_SerialNumber) = @SerialNumber";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@SerialNumber", value.ToUpper());

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                productid = reader.GetInt32(0);
                            }
                        }
                    }
                }

                GetObjectIDList GetObjectIDObj = new GetObjectIDList();
                GetObjectIDObj.name = "CoreProductMaster";
                getObjectID = GetObjectID(GetObjectIDObj, constring, LogException);
                ProductMstrObjectId = getObjectID.objectID;
                jsondata = new
                {
                    productid,
                    ProductMstrObjectId
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(jsondata, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsondata);
        }


        #endregion

        #region ::: InsertCompetitorPrice /Mithun:::
        /// <summary>
        /// Method to insert the Parts price details 
        /// </summary>   
        public static IActionResult InsertCompetitorPrice(InsertCompetitorPriceList InsertCompetitorPriceObj, string constring, int LogException)
        {
            JTokenReader jr = null;
            var jsonResult = default(dynamic);
            int Company_ID = Convert.ToInt32(InsertCompetitorPriceObj.Company_ID);
            int User_ID = Convert.ToInt32(InsertCompetitorPriceObj.User_ID);

            try
            {
                JObject jObj = JObject.Parse(InsertCompetitorPriceObj.key);
                int ContPersrowcount = jObj["rows"].Count();
                int? CustomerWarranty = null;

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    for (int i = 0; i < ContPersrowcount; i++)
                    {
                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["PriceKey"]);
                        jr.Read();
                        string PriceKey = jr.Value.ToString();

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Part_ID"]);
                        jr.Read();
                        int Parts_ID = Convert.ToInt32(jr.Value.ToString());

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["CompetitorName"]);
                        jr.Read();
                        string CompetitorName = Convert.ToString(jr.Value);

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["MRP"]);
                        jr.Read();
                        decimal MRP = Convert.ToDecimal(jr.Value.ToString());

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["MarketRulingPrice"]);
                        jr.Read();
                        decimal? MarketRulingPrice = null;
                        if (jr.Value.ToString() != "")
                        {
                            MarketRulingPrice = Convert.ToDecimal(jr.Value.ToString());
                        }

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Remarks"]);
                        jr.Read();
                        string Remarks = Convert.ToString(jr.Value);

                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["CPriceEffectiveFrom"]);
                        jr.Read();
                        DateTime CPriceEffectiveFrom = Convert.ToDateTime(jr.Value.ToString());

                        if (PriceKey == "")
                        {
                            using (SqlCommand command = new SqlCommand("UP_INS_AMERP_InsertCompetitorPrice", connection))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@Parts_ID", Parts_ID);
                                command.Parameters.AddWithValue("@CompetitorName", CompetitorName);
                                command.Parameters.AddWithValue("@MRP", MRP);
                                command.Parameters.AddWithValue("@MarketRulingPrice", (object)MarketRulingPrice ?? DBNull.Value);
                                command.Parameters.AddWithValue("@Competitor_EffectiveFrom", CPriceEffectiveFrom);
                                command.Parameters.AddWithValue("@Company_ID", Company_ID);
                                command.Parameters.AddWithValue("@Remarks", Remarks);
                                command.Parameters.AddWithValue("@ModifiedBy", User_ID);
                                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                                command.ExecuteNonQuery();
                            }
                        }
                        else
                        {
                            int PartsPriceId = Convert.ToInt32(PriceKey);

                            using (SqlCommand command = new SqlCommand("UP_UPD_AMERP_UpdateCompetitorPrice", connection))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@CompetitorPriceDetail_ID", PartsPriceId);
                                command.Parameters.AddWithValue("@Parts_ID", Parts_ID);
                                command.Parameters.AddWithValue("@CompetitorName", CompetitorName);
                                command.Parameters.AddWithValue("@MRP", MRP);
                                command.Parameters.AddWithValue("@MarketRulingPrice", (object)MarketRulingPrice ?? DBNull.Value);
                                command.Parameters.AddWithValue("@Competitor_EffectiveFrom", CPriceEffectiveFrom);
                                command.Parameters.AddWithValue("@Company_ID", Company_ID);
                                command.Parameters.AddWithValue("@Remarks", Remarks);
                                command.Parameters.AddWithValue("@ModifiedBy", User_ID);
                                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                                command.ExecuteNonQuery();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonResult);
        }

        #endregion

        #region ::: DeleteCompetitorPriceDetail /Mithun:::
        /// <summary>
        ///to delete Part product
        /// </summary>          
        public static IActionResult DeleteCompetitorPriceDetail(DeleteCompetitorPriceDetailList DeleteCompetitorPriceDetailObj, string constring)
        {
            JTokenReader jr = null;
            int id = 0;
            string errorMsg = string.Empty;

            try
            {
                JObject jObj = JObject.Parse(DeleteCompetitorPriceDetailObj.key);
                int rowcount = jObj["rows"].Count();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    for (int i = 0; i < rowcount; i++)
                    {
                        jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                        jr.Read();
                        id = Convert.ToInt32(jr.Value);

                        string deleteQuery = "DELETE FROM GNM_CompetitorPriceDetail WHERE CompetitorPriceDetail_ID = @id";

                        using (SqlCommand command = new SqlCommand(deleteQuery, connection))
                        {
                            command.Parameters.AddWithValue("@id", id);
                            command.ExecuteNonQuery();
                        }
                    }
                }

                // gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"]), Convert.ToInt32(Session["Branch"]), Convert.ToInt32(Session["User_ID"]), Convert.ToInt32(Common.GetObjectID("CorePartsMaster")), id, 0, 0, "Delete", false);
                errorMsg = CommonFunctionalities.GetResourceString(DeleteCompetitorPriceDetailObj.GeneralCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.InnerException != null && ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    errorMsg = CommonFunctionalities.GetResourceString(DeleteCompetitorPriceDetailObj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    errorMsg = ex.Message; // or some other fallback error message
                }
            }

            //return errorMsg;
            return new JsonResult(errorMsg);
        }

        #endregion

        #region ::: Select Parts Price /Mithun:::
        /// <summary>
        /// Select All Parts Order Cancellation
        /// </summary>
        private static IActionResult GetCompetitorPrice(string constring, int LogException, int id)
        {
            List<GNM_CompetitorPriceDetail> competitorPriceDetails = new List<GNM_CompetitorPriceDetail>();

            try
            {
                // Retrieve user details from session
                //GNM_User User = (GNM_User)Session["UserDetails"];
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Create command to fetch competitor prices
                    string query = @"
                        SELECT CompetitorPriceDetail_ID, CompetitorName, MRP, MarketRulingPrice, Competitor_EffectiveFrom, Remarks, Company_ID, ModifiedBy, ModifiedDate
                        FROM GNM_CompetitorPriceDetail
                        WHERE Parts_ID = @PartsID
                    ";

                    SqlCommand cmd = new SqlCommand(query, conn);
                    cmd.Parameters.AddWithValue("@PartsID", id);

                    // Execute reader to fetch data
                    SqlDataReader reader = cmd.ExecuteReader();
                    while (reader.Read())
                    {
                        GNM_CompetitorPriceDetail competitorPrice = new GNM_CompetitorPriceDetail
                        {
                            CompetitorPriceDetail_ID = Convert.ToInt32(reader["CompetitorPriceDetail_ID"]),
                            CompetitorName = reader["CompetitorName"].ToString(),
                            MRP = Convert.ToDecimal(reader["MRP"]),
                            MarketRulingPrice = Convert.ToDecimal(reader["MarketRulingPrice"]),
                            Competitor_EffectiveFrom = Convert.ToDateTime(reader["Competitor_EffectiveFrom"]),
                            Remarks = reader["Remarks"].ToString(),
                            Company_ID = Convert.ToInt32(reader["Company_ID"]),
                            ModifiedBy = Convert.ToInt32(reader["ModifiedBy"]),
                            ModifiedDate = Convert.ToDateTime(reader["ModifiedDate"])
                        };

                        competitorPriceDetails.Add(competitorPrice);
                    }

                    // Close the connection
                    conn.Close();
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    // Log exception if needed
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            // Return IQueryable (converted from List)
            IQueryable<GNM_CompetitorPriceDetail> IQPartsPrice = competitorPriceDetails.AsQueryable();
            //return IQPartsPrice;
            return new JsonResult(IQPartsPrice);
        }


        #endregion

        #region ::: SelectPartsPrice /Mithun:::
        /// <summary>
        ////to Select part price Details
        /// </summary>    
        public static IActionResult SelectCompetitorPartsPrice(SelectCompetitorPartsPriceList SelectCompetitorPartsPriceObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters)
        {
            int count = 0;
            int total = 0;
            var PartPriceDet = default(dynamic);
            IQueryable<GNM_CompetitorPriceDetail> IQPartsPrice = null;
            try
            {
                IQPartsPrice = (IQueryable<GNM_CompetitorPriceDetail>)GetCompetitorPrice(constring, LogException, SelectCompetitorPartsPriceObj.id);
                count = IQPartsPrice.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                IQPartsPrice = IQPartsPrice.OrderByField<GNM_CompetitorPriceDetail>(sidx, sord);
                //FilterToolBar Search
                //if (_search)
                //{
                //    Filters filters = JObject.Parse(Common.DecryptString(filters).ToObject<Filters>();
                //    if (filters.rules.Count() > 0)
                //        IQPartsPrice = IQPartsPrice.FilterSearch<GNM_CompetitorPriceDetail>(filters);
                //}
                PartPriceDet = new
                {
                    total = total,
                    page = page,
                    records = count,
                    rows = (from a in IQPartsPrice
                            select new
                            {
                                //edit = "<img id='" + a.CompetitorPriceDetail_ID + "' src='" + AppPath + "/Content/edit.gif' key='" + a.CompetitorPriceDetail_ID + "' class='editCompetitorDetails' editmode='false'/>",
                                edit = "<a title='Edit' href='#' id='" + a.CompetitorPriceDetail_ID + "' key='" + a.CompetitorPriceDetail_ID + "' class='editCompetitorDetails font-icon-class'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input type='checkbox' key='" + a.CompetitorPriceDetail_ID + "' defaultchecked=''  id='chk" + a.CompetitorPriceDetail_ID + "' class='DelCompetitorPartPrice'/>",
                                ID = a.CompetitorPriceDetail_ID,
                                a.Parts_ID,
                                CompetitorName = a.CompetitorName,
                                a.MRP,
                                MarketRulingPrice = a.MarketRulingPrice,
                                CPriceEffectiveFrom = Convert.ToDateTime(a.Competitor_EffectiveFrom).ToString("dd-MMM-yyyy"),
                                Remarks = a.Remarks,
                                a.Company_ID,
                                ModifiedDate = Convert.ToDateTime(a.ModifiedDate).ToString("dd-MMM-yyyy"),
                                ModifiedBy = GetUserNameById((int)a.ModifiedBy, constring),
                            }).ToList().Paginate(page, rows),
                    SelectCompetitorPartsPriceObj.id,
                    filter = filters,
                    //advanceFilter = Request.Params["Query"],
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(PartPriceDet, JsonRequestBehavior.AllowGet);
            return new JsonResult(PartPriceDet);
        }

        public static string GetUserNameById(int userId, string connectionString)
        {
            string userName = string.Empty;
            string query = "SELECT User_Name FROM GNM_User WHERE User_ID = @User_ID";

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    cmd.Parameters.AddWithValue("@User_ID", userId);
                    conn.Open();

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            userName = reader["User_Name"].ToString();
                        }
                    }
                }
            }

            return userName;
        }
        #endregion

        #region ::: LoadPartsDropdown /Mithun:::
        /// <summary>
        /// to Load Parts English Dropdowns
        /// </summary>
        public static IActionResult LoadPartsDropdown(LoadPartsDropdownList LoadPartsDropdownObj, string constring, int LogException)
        {
            //GNM_User UserDetails = (GNM_User)Session["UserDetails"];
            // GNM_User UserDetails = LoadPartsDropdownObj.UserDetails.FirstOrDefault();
            int companyID = LoadPartsDropdownObj.Company_ID;
            bool? IsCompSpec = null;
            var jsonData = default(dynamic);
            try
            {
                int ParentCompID = 0;
                List<object> UOMArr = new List<object>();
                List<object> PCArr = new List<object>();
                List<object> ParttypeArr = new List<object>();
                List<object> PartsDisposalArr = new List<object>();
                List<object> ExciseDutyData = new List<object>();
                List<object> MType = new List<object>();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Retrieve ParentCompID
                    using (SqlCommand command = new SqlCommand("SELECT Company_Parent_ID FROM GNM_Company WHERE Company_ID = @Company_ID", connection))
                    {
                        command.Parameters.AddWithValue("@Company_ID", companyID);
                        ParentCompID = Convert.ToInt32(command.ExecuteScalar());
                    }

                    // Retrieve UOM data
                    int uomID = 0;
                    using (SqlCommand command = new SqlCommand("SELECT RefMaster_ID FROM GNM_RefMaster WHERE RefMaster_Name = 'UNITOFMEASUREMENT'", connection))
                    {
                        uomID = Convert.ToInt32(command.ExecuteScalar());
                    }
                    using (SqlCommand command = new SqlCommand("SELECT RefMasterDetail_ID, RefMasterDetail_Name FROM GNM_RefMasterDetail WHERE RefMaster_ID = @RefMaster_ID AND RefMasterDetail_IsActive = 1", connection))
                    {
                        command.Parameters.AddWithValue("@RefMaster_ID", uomID);
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                UOMArr.Add(new
                                {
                                    ID = (int)reader["RefMasterDetail_ID"],
                                    Name = reader["RefMasterDetail_Name"].ToString()
                                });
                            }
                        }
                    }

                    // Retrieve Parts Categories
                    int PCID = 0;
                    using (SqlCommand command = new SqlCommand("SELECT PartsCategory_ID FROM PRM_PartsCategory WHERE Company_ID = @Company_ID AND IsActive = 1", connection))
                    {
                        command.Parameters.AddWithValue("@Company_ID", companyID);
                        PCID = Convert.ToInt32(command.ExecuteScalar());
                    }
                    using (SqlCommand command = new SqlCommand("SELECT PartsCategory_ID, Description FROM PRM_PartsCategory WHERE IsActive = 1 AND (Company_ID = @Company_ID OR Company_ID = @ParentCompID)", connection))
                    {
                        command.Parameters.AddWithValue("@Company_ID", companyID);
                        command.Parameters.AddWithValue("@ParentCompID", ParentCompID);
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                PCArr.Add(new
                                {
                                    ID = (int)reader["PartsCategory_ID"],
                                    Name = reader["Description"].ToString()
                                });
                            }
                        }
                    }

                    // Retrieve Part Types
                    int ParttypeID = 0;
                    using (SqlCommand command = new SqlCommand("SELECT RefMaster_ID FROM GNM_RefMaster WHERE RefMaster_Name = 'PARTTYPE'", connection))
                    {
                        ParttypeID = Convert.ToInt32(command.ExecuteScalar());
                    }
                    using (SqlCommand command = new SqlCommand("SELECT RefMasterDetail_ID, RefMasterDetail_Name FROM GNM_RefMasterDetail WHERE RefMaster_ID = @RefMaster_ID AND RefMasterDetail_IsActive = 1", connection))
                    {
                        command.Parameters.AddWithValue("@RefMaster_ID", ParttypeID);
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ParttypeArr.Add(new
                                {
                                    ID = (int)reader["RefMasterDetail_ID"],
                                    Name = reader["RefMasterDetail_Name"].ToString()
                                });
                            }
                        }
                    }

                    // Retrieve Parts Disposal Categories
                    using (SqlCommand command = new SqlCommand("SELECT * FROM GNM_PartsDisposalCategory", connection))
                    {
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                PartsDisposalArr.Add(new
                                {
                                    ID = (int)reader["PartsDisposalCategory_ID"],
                                    Name = reader["PartsDisposalCategory_Name"].ToString()
                                });
                            }
                        }
                    }

                    // Retrieve Excise Duty List
                    using (SqlCommand command = new SqlCommand("UP_AMERP_GetExciseDutyData", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ExciseDutyData.Add(new
                                {
                                    ID = (int)reader["RefMasterDetail_ID"],
                                    Name = reader["RefMasterDetail_Short_Name"].ToString()
                                });
                            }
                        }
                    }

                    // Retrieve Movement Types
                    using (SqlCommand command = new SqlCommand("SELECT * FROM GNM_MovementType WHERE IsActive = 1 AND (Company_ID = @Company_ID OR Company_ID = @ParentCompID)", connection))
                    {
                        command.Parameters.AddWithValue("@Company_ID", companyID);
                        command.Parameters.AddWithValue("@ParentCompID", ParentCompID);
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                MType.Add(new
                                {
                                    ID = (int)reader["MovementType_ID"],
                                    Name = reader["Description"].ToString()
                                });
                            }
                        }
                    }

                    // Construct jsonData object
                    jsonData = new
                    {
                        UOMArr = UOMArr,
                        PCArr = PCArr,
                        ParttypeArr = ParttypeArr,
                        PartsDisposalArr = PartsDisposalArr,
                        ExciseDutyData = ExciseDutyData,
                        MType = MType
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }


        #endregion

        #region ::: LoadPartsDropdownLocale /Mithun:::
        /// <summary>
        /// to Load Parts Locale Dropdowns
        /// </summary>
        public static IActionResult LoadPartsDropdownLocale(LoadPartsDropdownLocaleList LoadPartsDropdownLocaleObj, string constring, int LogException)
        {
            // GNM_User UserDetails = (GNM_User)Session["UserDetails"];
            int companyID = LoadPartsDropdownLocaleObj.Company_ID;
            bool? IsCompSpec = null;
            var jsonData = default(dynamic);

            try
            {
                int Language_ID = Convert.ToInt32(LoadPartsDropdownLocaleObj.UserLanguageID);
                int BranchID = Convert.ToInt32(LoadPartsDropdownLocaleObj.Branch);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Query for UOMArr
                    SqlCommand cmdUOM = new SqlCommand(@"SELECT b.RefMasterDetail_ID, b.RefMasterDetail_Name
                                                 FROM GNM_RefMaster a
                                                 JOIN GNM_RefMasterDetail c ON a.RefMaster_ID = c.RefMaster_ID
                                                 JOIN GNM_RefMasterDetailLocale b ON c.RefMasterDetail_ID = b.RefMasterDetail_ID
                                                 WHERE a.RefMaster_Name = 'UNITOFMEASUREMENT' 
                                                 AND c.RefMasterDetail_IsActive = 1
                                                 AND (a.IsCompanySpecific = @IsCompSpec OR a.IsCompanySpecific = 0)
                                                 ORDER BY b.RefMasterDetail_Name", conn);
                    cmdUOM.Parameters.AddWithValue("@IsCompSpec", IsCompSpec.HasValue ? (object)IsCompSpec.Value : DBNull.Value);

                    List<dynamic> UOMArr = new List<dynamic>();
                    using (SqlDataReader reader = cmdUOM.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            UOMArr.Add(new
                            {
                                ID = reader.GetInt32(0),
                                Name = reader.GetString(1)
                            });
                        }
                    }

                    // Query for ParttypeLocalArr
                    SqlCommand cmdParttype = new SqlCommand(@"SELECT b.RefMasterDetail_ID, b.RefMasterDetail_Name
                                                      FROM GNM_RefMaster a
                                                      JOIN GNM_RefMasterDetail c ON a.RefMaster_ID = c.RefMaster_ID
                                                      JOIN GNM_RefMasterDetailLocale b ON c.RefMasterDetail_ID = b.RefMasterDetail_ID
                                                      WHERE a.RefMaster_Name = 'PARTTYPE' 
                                                      AND c.RefMasterDetail_IsActive = 1
                                                      ORDER BY b.RefMasterDetail_Name", conn);

                    List<dynamic> ParttypeLocalArr = new List<dynamic>();
                    using (SqlDataReader reader = cmdParttype.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            ParttypeLocalArr.Add(new
                            {
                                ID = reader.GetInt32(0),
                                Name = reader.GetString(1)
                            });
                        }
                    }

                    // Query for PCArr
                    SqlCommand cmdPCArr = new SqlCommand(@"SELECT a.PartsCategory_ID, b.Description
                                                   FROM PRM_CorePartsCategory a
                                                   JOIN PRM_CorePartsCategoryLocale b ON a.PartsCategory_ID = b.PartsCategory_ID
                                                   WHERE a.Company_ID = @CompanyID AND a.IsActive = 1 AND b.Language_ID = @LanguageID
                                                   ORDER BY b.Description", conn);
                    cmdPCArr.Parameters.AddWithValue("@CompanyID", companyID);
                    cmdPCArr.Parameters.AddWithValue("@LanguageID", Language_ID);

                    List<dynamic> PCArr = new List<dynamic>();
                    using (SqlDataReader reader = cmdPCArr.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            PCArr.Add(new
                            {
                                ID = reader.GetInt32(0),
                                Name = reader.GetString(1)
                            });
                        }
                    }

                    // Query for MType
                    SqlCommand cmdMType = new SqlCommand(@"SELECT c.MovementTypeDefinition_ID, b.Description
                                                   FROM GNM_MovementType a
                                                   JOIN GNM_MovementTypeLocale b ON a.MovementType_ID = b.MovementType_ID
                                                   JOIN GNM_MovementTypeDefinition c ON a.MovementType_ID = c.MovementType_ID
                                                   WHERE a.IsActive = 1 AND a.Company_ID = @CompID AND c.Branch_ID = @BranchID
                                                   ORDER BY a.Description", conn);
                    cmdMType.Parameters.AddWithValue("@CompID", LoadPartsDropdownLocaleObj.Comp_ID);
                    cmdMType.Parameters.AddWithValue("@BranchID", BranchID);

                    List<dynamic> MType = new List<dynamic>();
                    using (SqlDataReader reader = cmdMType.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            MType.Add(new
                            {
                                ID = reader.GetInt32(0),
                                Name = reader.GetString(1)
                            });
                        }
                    }

                    // Query for PartsDisposalArr
                    SqlCommand cmdPartsDisposalArr = new SqlCommand(@"SELECT PartsDisposalCategory_ID, PartsDisposalCategory_Name 
                                                              FROM GNM_PartsDisposalCategoryLocale 
                                                              ORDER BY PartsDisposalCategory_Name", conn);

                    List<dynamic> PartsDisposalArr = new List<dynamic>();
                    using (SqlDataReader reader = cmdPartsDisposalArr.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            PartsDisposalArr.Add(new
                            {
                                ID = reader.GetInt32(0),
                                Name = reader.GetString(1)
                            });
                        }
                    }

                    // Query for ExciseDutyData
                    SqlCommand cmdExciseDuty = new SqlCommand(@"SELECT a.RefMasterDetail_ID, b.RefMasterDetail_Short_Name
                                                        FROM GNM_RefMasterDetail a
                                                        JOIN GNM_RefMasterDetailLocale b ON a.RefMasterDetail_ID = b.RefMasterDetail_ID
                                                        WHERE a.RefMaster_ID = (SELECT RefMaster_ID FROM GNM_RefMaster WHERE RefMaster_Name = 'EXCISETARIFFITEM')
                                                        AND a.RefMasterDetail_IsActive = 1
                                                        ORDER BY b.RefMasterDetail_Name", conn);

                    List<dynamic> ExciseDutyData = new List<dynamic>();
                    using (SqlDataReader reader = cmdExciseDuty.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            ExciseDutyData.Add(new
                            {
                                ID = reader.GetInt32(0),
                                Name = reader.GetString(1)
                            });
                        }
                    }

                    jsonData = new
                    {
                        UOMArr = UOMArr,
                        ParttypeLocalArr = ParttypeLocalArr,
                        PCArr = PCArr,
                        MType = MType,
                        PartsDisposalArr = PartsDisposalArr,
                        ExciseDutyData = ExciseDutyData
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion

        #region ::: SelectPartsPrice /Mithun:::
        /// <summary>
        ////to Select part price Details
        /// </summary>   
        public static IActionResult SelectPartsPrice(SelectPartsPriceList SelectPartsPriceObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters)
        {
            int count = 0;
            int total = 0;
            var PartPriceDet = default(dynamic);
            IQueryable<PartsPrice> IQPartsPrice = null; // Declare IQueryable<PartsPrice>


            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Fetch currency ID 
                    int currencyID = 0;
                    string currencyIDQuery = "SELECT TOP 1 RefMaster_ID FROM GNM_RefMaster WHERE RefMaster_Name = 'CURRENCY'";
                    using (SqlCommand cmdCurrencyID = new SqlCommand(currencyIDQuery, connection))
                    {
                        currencyID = (int)cmdCurrencyID.ExecuteScalar();
                    }

                    // Fetch currency details using stored procedure
                    List<string> currencyList = new List<string>();
                    string currencyDetailsStoredProcedure = "UP_AMERP_GetCurrencyDetails"; // Name of your stored procedure

                    using (SqlCommand cmdCurrencyDetails = new SqlCommand(currencyDetailsStoredProcedure, connection))
                    {
                        cmdCurrencyDetails.CommandType = CommandType.StoredProcedure;
                        cmdCurrencyDetails.Parameters.AddWithValue("@CurrencyName", "CURRENCY"); // Parameter for the stored procedure

                        using (SqlDataReader reader = cmdCurrencyDetails.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                int refMasterDetailID = Convert.ToInt32(reader["RefMasterDetail_ID"]);
                                string refMasterDetailName = reader["RefMasterDetail_Name"].ToString();
                                currencyList.Add(refMasterDetailID + ":" + refMasterDetailName);
                            }
                        }
                    }

                    // Prepare the Currency string
                    string Currency = "-1:---Select---;";
                    Currency += string.Join(";", currencyList);

                    // Call GetPartsPrice(id) method to retrieve IQueryable<PartsPrice>
                    IQPartsPrice = GetPartsPrice(SelectPartsPriceObj.id, constring, LogException);

                    count = IQPartsPrice.Count();
                    total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                    // Apply ordering using IQueryable extension method
                    IQPartsPrice = IQPartsPrice.OrderByField(sidx, sord);

                    // Apply filtering if _search is true
                    if (_search)
                    {
                        // Retrieve and parse filters
                        //Filters filters = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                        //if (filters.rules.Count > 0)
                        //{
                        //    // Filter IQueryable<PartsPrice> based on filters
                        //    IQPartsPrice = IQPartsPrice.FilterSearch(filters);
                        //    count = IQPartsPrice.Count(); // Update count after filtering
                        //    total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                        //}
                    }

                    // Prepare response object
                    PartPriceDet = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        rows = IQPartsPrice.Skip((page - 1) * rows).Take(rows).Select(a => new
                        {
                            edit = "<a title=" + CommonFunctionalities.GetResourceString(SelectPartsPriceObj.UserCulture.ToString(), "view").ToString() + " href='#' style='font-size: 13px;'  id='" + a.ID + "' key='" + a.ID + "' class='editPartPriceDetails' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                            delete = "<input type='checkbox' key='" + a.ID + "' defaultchecked=''  id='chk" + a.ID + "' class='DelPartPrice'/>",
                            a.ID,
                            a.ListPrice,
                            a.FormulaCostPrice,
                            a.MRPPrice,
                            a.EffectiveFrom,
                            a.CustomerWarranty,
                            a.BuyingCurrency
                        }),
                        SelectPartsPriceObj.id,
                        //filter = filters,
                        //advanceFilter = Query,
                        Currency = Currency
                    };

                    //return Json(PartPriceDet, JsonRequestBehavior.AllowGet);
                    return new JsonResult(PartPriceDet);
                }
            }
            catch (Exception ex)
            {
                // Handle exceptions
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(PartPriceDet, JsonRequestBehavior.AllowGet);
            return new JsonResult(PartPriceDet);
        }


        #endregion

        #region ::: SelectPartsPriceLocale /Mithun:::
        /// <summary>
        ////to Select part price Details
        /// </summary>   
        public static IActionResult SelectPartsPriceLocale(SelectPartsPriceLocaleList SelectPartsPriceLocaleObj, string constring, int LogException, string sidx, string sord, int page, int rows)
        {
            int count = 0;
            int total = 0;
            var PartPriceDet = new object();
            List<PartsPrice> partsPriceList = new List<PartsPrice>();
            // GNM_User User = (GNM_User)Session["UserDetails"];

            try
            {
                using (SqlConnection con = new SqlConnection(constring))
                {
                    con.Open();

                    // Get Parts Price Details
                    string query = @"
                SELECT a.PartsPriceDetail_ListPrice, a.PartsPriceDetail_FormulaCostPrice, 
                       a.PartsPriceDetail_MRP, a.PartsPriceDetail_EffectiveFrom, 
                       a.CustomerWarranty, b.RefMasterDetail_Name AS BuyingCurrency
                FROM GNM_PartsPriceDetail a
                LEFT JOIN GNM_RefMasterDetailLocale b ON a.Currency_ID = b.RefMasterDetail_ID
                WHERE a.Parts_ID = @Parts_ID AND a.Company_ID = @Company_ID";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@Parts_ID", SelectPartsPriceLocaleObj.id);
                        cmd.Parameters.AddWithValue("@Company_ID", SelectPartsPriceLocaleObj.Company_ID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                PartsPrice partPrice = new PartsPrice
                                {
                                    ListPrice = Convert.ToDecimal(reader["PartsPriceDetail_ListPrice"]).ToString("0.00"),
                                    FormulaCostPrice = reader["PartsPriceDetail_FormulaCostPrice"] == DBNull.Value ? "" : Convert.ToDecimal(reader["PartsPriceDetail_FormulaCostPrice"]).ToString("0.00"),
                                    MRPPrice = Convert.ToDecimal(reader["PartsPriceDetail_MRP"]).ToString("0.00"),
                                    EffectiveFromDate = Convert.ToDateTime(reader["PartsPriceDetail_EffectiveFrom"]),
                                    EffectiveFrom = Convert.ToDateTime(reader["PartsPriceDetail_EffectiveFrom"]).ToString("dd-MMM-yyyy"),
                                    CustomerWarranty = reader["CustomerWarranty"] == DBNull.Value ? string.Empty : reader["CustomerWarranty"].ToString(),
                                    BuyingCurrency = reader["BuyingCurrency"].ToString()
                                };
                                partsPriceList.Add(partPrice);
                            }
                        }
                    }

                    // Get count of records
                    count = partsPriceList.Count();
                    total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                    // Sort the data
                    var sortedList = partsPriceList.AsQueryable().OrderByField(sidx, sord);

                    // Pagination
                    PartPriceDet = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        rows = sortedList.Skip((page - 1) * rows).Take(rows).ToList()
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(PartPriceDet, JsonRequestBehavior.AllowGet);
            return new JsonResult(PartPriceDet);
        }

        #endregion



        #region ::: get Parts Price /Mithun:::
        /// <summary>
        /// Select All Parts Order Cancellation
        /// </summary>
        private static IQueryable<PartsPrice> GetPartsPrice(int id, string constring, int LogException)
        {
            List<PartsPrice> partPrices = new List<PartsPrice>();
            SelectPartsPriceList SelectPartsPriceObj = new SelectPartsPriceList();

            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Create SqlCommand for stored procedure
                    using (SqlCommand command = new SqlCommand("UP_AMERP_GetPartsPriceDetails", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // Add parameters to the stored procedure
                        command.Parameters.AddWithValue("@PartsID", id);

                        //GNM_User User = (GNM_User)HttpContext.Session["UserDetails"];
                        GNM_User User = SelectPartsPriceObj.UserDetails.FirstOrDefault();
                        command.Parameters.AddWithValue("@CompanyID", User.Company_ID);

                        // Execute the stored procedure
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                PartsPrice price = new PartsPrice();
                                price.ID = Convert.ToInt32(reader["ID"]);
                                price.ListPrice = reader["ListPrice"].ToString();
                                price.FormulaCostPrice = reader["FormulaCostPrice"].ToString();
                                price.MRPPrice = reader["MRPPrice"].ToString();
                                price.EffectiveFrom = reader["EffectiveFrom"].ToString();
                                price.EffectiveFromDate = Convert.ToDateTime(reader["EffectiveFromDate"]);
                                price.CustomerWarranty = reader["CustomerWarranty"].ToString();
                                price.BuyingCurrency = reader["BuyingCurrency"].ToString();

                                partPrices.Add(price);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle exceptions
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return partPrices.AsQueryable();
        }
        #endregion

        #region ::: DeletePart /Mithun:::
        /// <summary>
        /// to Delete the Parts
        /// </summary>
        public static IActionResult DeletePart(DeletePartList DeletePartObj, string constring)
        {
            string errorMsg = "";
            var Culture = "Resource_" + DeletePartObj.Lang;
            try
            {
                JObject jobj = JObject.Parse(DeletePartObj.key);
                int rowCount = jobj["rows"].Count();
                int BranchID = Convert.ToInt32(DeletePartObj.Branch);
                //GNM_User User = (GNM_User)Session["UserDetails"];
                GNM_User User = DeletePartObj.UserDetails.FirstOrDefault();
                int id = 0;

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    for (int i = 0; i < rowCount; i++)
                    {
                        id = Convert.ToInt32(jobj["rows"][i]["id"].ToString());

                        // Check for locale row and delete if exists
                        using (SqlCommand cmd = new SqlCommand("DELETE FROM GNM_PartsLocale WHERE Parts_ID = @PartsID", conn))
                        {
                            cmd.Parameters.AddWithValue("@PartsID", id);
                            cmd.ExecuteNonQuery();
                        }

                        // Delete the part
                        using (SqlCommand cmd = new SqlCommand("DELETE FROM GNM_Parts WHERE Parts_ID = @PartsID", conn))
                        {
                            cmd.Parameters.AddWithValue("@PartsID", id);
                            cmd.ExecuteNonQuery();
                        }

                        // Insert GPS details
                        //gbl.InsertGPSDetails(
                        //    Convert.ToInt32(DeletePartObj.Company_ID),
                        //    BranchID,
                        //    User.User_ID,
                        //    Common.GetObjectID("CorePartsMaster",constring),
                        //    id,
                        //    0,
                        //    0,
                        //    "Deleted Part Number " + id,
                        //    false,
                        //    Convert.ToInt32(DeletePartObj.MenuID),
                        //    Convert.ToDateTime(DeletePartObj.LoggedINDateTime)
                        //);
                    }

                    errorMsg = CommonFunctionalities.GetResourceString(Culture.ToString(), "deletedsuccessfully").ToString();
                }
            }
            catch (Exception ex)
            {
                if (ex.InnerException?.InnerException?.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint") == true)
                {
                    errorMsg = CommonFunctionalities.GetResourceString(Culture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
                else
                {
                    errorMsg = ex.Message;
                }
            }

            //return errorMsg;
            return new JsonResult(errorMsg);
        }
        #endregion


        #region ::: SelectParticularPartproduct /Mithun:::
        /// <summary>
        /// To select the Particular part product
        /// </summary>    
        public static IActionResult SelectParticularPart(SelectParticularPartList SelectParticularPartObj, string constring, int LogException)
        {
            int CompanyID = (int)SelectParticularPartObj.Company_ID;
            int BranchID = Convert.ToInt32(SelectParticularPartObj.Branch);
            // GNM_User User = (GNM_User)Session["UserDetails"];
            List<GNM_Parts> liParts = new List<GNM_Parts>();
            string partnumber = string.Empty;
            var JsonData = default(dynamic);
            int CorRemanpartId = 0;

            try
            {
                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    // Retrieve the parts and part number
                    using (SqlCommand command = new SqlCommand("SELECT * FROM GNM_Parts WHERE Parts_ID = @Parts_ID", connection))
                    {
                        command.Parameters.AddWithValue("@Parts_ID", SelectParticularPartObj.id);
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                liParts.Add(new GNM_Parts
                                {
                                    Parts_ID = (int)reader["Parts_ID"],
                                    Parts_PartsNumber = reader["Parts_PartsNumber"].ToString(),
                                    Parts_PartsDescription = reader["Parts_PartsDescription"].ToString(),
                                    PartsCategory_ID = reader["PartsCategory_ID"] as int?,
                                    PartsFunctionGroup_ID = reader["PartsFunctionGroup_ID"] as int?,
                                    UnitOfMeasurement_ID = (int)reader["UnitOfMeasurement_ID"],
                                    PartsDisposal_ID = reader["PartsDisposal_ID"] as int?,
                                    Parts_IsActive = (bool)reader["Parts_IsActive"],
                                    Parts_IsComponent = (bool)reader["Parts_IsComponent"],
                                    Parts_IsHazardousGood = (bool)reader["Parts_IsHazardousGood"],
                                    Parts_IsLocal = (bool)reader["Parts_IsLocal"],
                                    Parts_PartPrefix = reader["Parts_PartPrefix"].ToString(),
                                    Parts_AliasPartPrefix = reader["Parts_AliasPartPrefix"]?.ToString(),
                                    Parts_AliasPartNumber = reader["Parts_AliasPartNumber"]?.ToString(),
                                    MovementType_ID = (int)reader["MovementType_ID"],
                                    Parts_Weight = reader["Parts_Weight"] == DBNull.Value ? (decimal?)null : (decimal)reader["Parts_Weight"],
                                    Parts_Dimensions = reader["Parts_Dimensions"]?.ToString(),
                                    PartsCustomsCode_ID = reader["PartsCustomsCode_ID"] as int?,
                                    SupersessionType_ID = reader["SupersessionType_ID"] as int?,
                                    IsKitPart = (bool)reader["IsKitPart"],
                                    PartType = reader["PartType"] as int?,
                                    SalvagePart_ID = reader["SalvagePart_ID"] as int?,
                                    ExciseDuty_ID = reader["ExciseDuty_ID"] as int?,
                                });
                                partnumber = reader["Parts_PartsNumber"].ToString();
                            }
                        }
                    }

                    // Check REM_CORETYPE for COREPARTS_ID
                    using (SqlCommand command = new SqlCommand("SELECT COUNT(*) FROM REM_CORETYPE WHERE COREPARTS_ID = @COREPARTS_ID", connection))
                    {
                        command.Parameters.AddWithValue("@COREPARTS_ID", SelectParticularPartObj.id);
                        CorRemanpartId = (int)command.ExecuteScalar();
                    }

                    // Retrieve additional details for the parts
                    var partsDetails = new List<dynamic>();
                    using (SqlCommand command = new SqlCommand("UP_AMERP_GetPartsDetails", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@Parts_ID", SelectParticularPartObj.id);
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                decimal? partsWeight = null;
                                if (reader["Parts_Weight"] != DBNull.Value)
                                {
                                    partsWeight = (decimal)reader["Parts_Weight"];
                                }

                                partsDetails.Add(new
                                {
                                    Parts_ID = (int)reader["Parts_ID"],
                                    Parts_Number = Uri.UnescapeDataString(reader["Parts_PartsNumber"].ToString()),
                                    Parts_Desc = Uri.UnescapeDataString(reader["Parts_PartsDescription"].ToString()),
                                    Parts_PartsCategory = reader["PartsCategory_ID"] as int?,
                                    Parts_PartsCategoryValue = reader["PartsCategoryValue"]?.ToString(),
                                    Parts_PartsFunctionGroup = reader["Parts_PartsFunctionGroup"]?.ToString(),
                                    Parts_UOM = (int)reader["UnitOfMeasurement_ID"],
                                    Parts_Disposal_ID = reader["PartsDisposal_ID"] as int?,
                                    UnitOfMeasurement_IDValue = reader["UnitOfMeasurement_IDValue"]?.ToString(),
                                    PartsDisposalName = reader["PartsDisposalName"]?.ToString(),
                                    Parts_IsActive = (bool)reader["Parts_IsActive"],
                                    Parts_IsComponent = (bool)reader["Parts_IsComponent"],
                                    Parts_IsHazardous = (bool)reader["Parts_IsHazardous"],
                                    Parts_IsLocal = (bool)reader["Parts_IsLocal"],
                                    Parts_PartPrefix = reader["Parts_PartPrefix"].ToString(),
                                    Parts_AliasPartPrefix = reader["Parts_AliasPartPrefix"]?.ToString(),
                                    Parts_AliasPartNumber = reader["Parts_AliasPartNumber"]?.ToString(),
                                    MovementType_ID = (int)reader["MovementType_ID"],
                                    MovementTypeValue = reader["MovementTypeValue"]?.ToString(),
                                    Parts_Weight = partsWeight,
                                    Parts_Dimensions = reader["Parts_Dimensions"]?.ToString(),
                                    PartsCustomsCode_ID = reader["PartsCustomsCode_ID"] as int?,
                                    RefMasterDetail_Name = reader["RefMasterDetail_Name"]?.ToString(),
                                    IsKitPart = (bool)reader["IsKitPart"],
                                    PartType = reader["PartType"] as int?,
                                    PartTypeValue = reader["PartTypeValue"]?.ToString(),
                                    SalvagePart_ID = reader["SalvagePart_ID"] as int?,
                                    SalvagePartNumber = reader["SalvagePartNumber"]?.ToString(),
                                    Salvage_PartPrefix = reader["Salvage_PartPrefix"]?.ToString(),
                                    ExciseDuty_ID = reader["ExciseDuty_ID"] as int?,
                                    ExciseDuty_IDValue = reader["ExciseDuty_IDValue"]?.ToString()
                                });
                            }
                        }
                    }

                    JsonData = new
                    {
                        Part = partsDetails,
                        x = new List<int>(),  // Placeholder, as the original x value was not used
                        CorRemanpartId
                    };

                    //  gbl.InsertGPSDetails(Convert.ToInt32(SelectParticularPartObj.Company_ID.ToString()), BranchID, SelectParticularPartObj.User_ID, Common.GetObjectID("CorePartsMaster",constring), SelectParticularPartObj.id, 0, 0, "Viewed Part Number " + partnumber + "", false, Convert.ToInt32(SelectParticularPartObj.MenuID), Convert.ToDateTime(SelectParticularPartObj.LoggedINDateTime));
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ": " + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                throw;  // Re-throw the exception to be handled by the calling code or global exception handler
            }

            //return Json(JsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(JsonData);
        }

        #endregion

        #region::: Insert Parts :::
        /// <summary>
        /// Insert Parts
        /// </summary>
        /// <param name="Company_ID"></param>
        /// <param name="User_ID"></param>
        /// <param name="data"></param>
        /// <param name="constring"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        //public static IActionResult InsertPart(int Company_ID, int User_ID, string data, string constring, int LogException)
        //{
        //    var x = default(dynamic);
        //    try
        //    {
        //        JObject jObj = JObject.Parse(data);

        //        string PartName = string.IsNullOrWhiteSpace(jObj["PartName"].ToString()) ? null : Uri.UnescapeDataString(jObj["PartName"].ToString());
        //        string PartsDescription = string.IsNullOrWhiteSpace(jObj["PartsDescription"].ToString()) ? null : Uri.UnescapeDataString(jObj["PartsDescription"].ToString());
        //        int? UnitOfMeasurement_ID = string.IsNullOrWhiteSpace(jObj["UOM"].ToString()) ? (int?)null : Convert.ToInt32(jObj["UOM"]);
        //        int? PartsCategory_ID = string.IsNullOrWhiteSpace(jObj["PartsCategory"].ToString()) ? (int?)null : Convert.ToInt32(jObj["PartsCategory"]);
        //        int? PartsFunctionGroup_ID = string.IsNullOrWhiteSpace(jObj["PartsFunctionGroup"].ToString()) ? (int?)null : Convert.ToInt32(jObj["PartsFunctionGroup"]);
        //        bool Parts_IsActive = jObj["chkIsActive"].ToString() == "checked";
        //        bool Parts_IsComponent = jObj["ChkIsComponent"].ToString() == "checked";
        //        bool Parts_IsHazardousGood = jObj["chkIsHazardousGood"].ToString() == "checked";
        //        bool IsKitPart = jObj["StockUsedInKits"].ToString() == "checked";
        //        bool Parts_IsLocal = jObj["IsLocal"].ToString() == "checked";
        //        string PartPrefix = string.IsNullOrWhiteSpace(jObj["PartPrefix"].ToString()) ? null : jObj["PartPrefix"].ToString();
        //        string CustomsCode = string.IsNullOrWhiteSpace(jObj["CustomsCode"].ToString()) ? null : jObj["CustomsCode"].ToString();
        //        string AliasPartPrefix = string.IsNullOrWhiteSpace(jObj["AliasPartPrefix"].ToString()) ? null : jObj["AliasPartPrefix"].ToString();
        //        string AliasPartName = string.IsNullOrWhiteSpace(jObj["AliasPartName"].ToString()) ? null : jObj["AliasPartName"].ToString();
        //        int? MovementType_ID = string.IsNullOrWhiteSpace(jObj["MovementType"].ToString()) ? (int?)null : Convert.ToInt32(jObj["MovementType"]);
        //        decimal? Parts_Weight = string.IsNullOrWhiteSpace(jObj["Weight"].ToString()) ? (decimal?)null : Convert.ToDecimal(jObj["Weight"]);
        //        string Parts_Dimensions = string.IsNullOrWhiteSpace(jObj["Dimension"].ToString()) ? null : jObj["Dimension"].ToString();
        //        int? ExciseDuty_ID = string.IsNullOrWhiteSpace(jObj["ExciseDuty_ID"].ToString()) ? (int?)null : Convert.ToInt32(jObj["ExciseDuty_ID"]);
        //        int? SalvagePart_ID = string.IsNullOrWhiteSpace(jObj["SalvagePart_ID"].ToString()) ? (int?)null : Convert.ToInt32(jObj["SalvagePart_ID"]);
        //        int? PartType = string.IsNullOrWhiteSpace(jObj["Parttype"].ToString()) ? (int?)null : Convert.ToInt32(jObj["Parttype"]);
        //        int? PartsDisposal_ID = string.IsNullOrWhiteSpace(jObj["PartsDisposal_ID"].ToString()) ? (int?)null : Convert.ToInt32(jObj["PartsDisposal_ID"]);


        //        // Set up database connection
        //        using (SqlConnection conn = new SqlConnection(constring))
        //        {
        //            conn.Open();

        //            using (SqlCommand cmd = new SqlCommand("UP_INS_AMERP_InsertGNMPart1", conn))
        //            {
        //                cmd.CommandType = CommandType.StoredProcedure;

        //                // Add parameters to command
        //                cmd.Parameters.AddWithValue("@PartPrefix", string.IsNullOrEmpty(PartPrefix) ? (object)DBNull.Value : PartPrefix);
        //                cmd.Parameters.AddWithValue("@PartName", string.IsNullOrEmpty(PartName) ? (object)DBNull.Value : PartName);
        //                cmd.Parameters.AddWithValue("@PartsDescription", string.IsNullOrEmpty(PartsDescription) ? (object)DBNull.Value : PartsDescription);
        //                cmd.Parameters.AddWithValue("@AliasPartPrefix", string.IsNullOrEmpty(AliasPartPrefix) ? (object)DBNull.Value : AliasPartPrefix);
        //                cmd.Parameters.AddWithValue("@AliasPartName", string.IsNullOrEmpty(AliasPartName) ? (object)DBNull.Value : AliasPartName);
        //                cmd.Parameters.AddWithValue("@MovementType", MovementType_ID.HasValue ? MovementType_ID.Value : (object)DBNull.Value);
        //                cmd.Parameters.AddWithValue("@Weight", Parts_Weight.HasValue ? Parts_Weight.Value : (object)DBNull.Value);
        //                cmd.Parameters.AddWithValue("@Dimension", string.IsNullOrEmpty(Parts_Dimensions) ? (object)DBNull.Value : Parts_Dimensions);
        //                cmd.Parameters.AddWithValue("@PartsCategory", PartsCategory_ID.HasValue ? PartsCategory_ID.Value : (object)DBNull.Value);
        //                cmd.Parameters.AddWithValue("@PartsFunctionGroup", PartsFunctionGroup_ID.HasValue ? PartsFunctionGroup_ID.Value : (object)DBNull.Value);
        //                cmd.Parameters.AddWithValue("@CustomsCode", string.IsNullOrEmpty(CustomsCode) ? (object)DBNull.Value : CustomsCode);
        //                cmd.Parameters.AddWithValue("@UOM", UnitOfMeasurement_ID.HasValue ? UnitOfMeasurement_ID.Value : (object)DBNull.Value);
        //                cmd.Parameters.AddWithValue("@chkIsActive", Parts_IsActive);
        //                cmd.Parameters.AddWithValue("@chkIsHazadous", Parts_IsHazardousGood);
        //                cmd.Parameters.AddWithValue("@User_ID", User_ID);
        //                cmd.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
        //                cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
        //                cmd.Parameters.AddWithValue("@IsLocal", Parts_IsLocal);
        //                cmd.Parameters.AddWithValue("@chkIsComponent", Parts_IsComponent);
        //                cmd.Parameters.AddWithValue("@IsKitPart", IsKitPart);
        //                cmd.Parameters.AddWithValue("@ExciseDuty", ExciseDuty_ID > 0 ? ExciseDuty_ID : (object)DBNull.Value);
        //                cmd.Parameters.AddWithValue("@SalvagePart_ID", SalvagePart_ID > 0 ? SalvagePart_ID : (object)DBNull.Value);
        //                cmd.Parameters.AddWithValue("@Parttype", PartType > 0 ? PartType : (object)DBNull.Value);
        //                cmd.Parameters.AddWithValue("@PartsDisposal_ID", PartsDisposal_ID > 0 ? PartsDisposal_ID : (object)DBNull.Value);



        //                // Execute command
        //                var newPartID = cmd.ExecuteScalar();

        //                x = new
        //                {
        //                    Parts_ID = newPartID,
        //                    ExciseDuty_ID = ExciseDuty_ID,
        //                    Parts_PartsNumber = PartName
        //                };
        //            }
        //        }
        //    }
        //    catch (Exception)
        //    {
        //        x = new
        //        {
        //            Parts_ID = 0,
        //            ExciseDuty_ID = 0,
        //            Parts_PartsNumber = ""
        //        };
        //    }
        //    return new JsonResult(x);
        //}

        #endregion



        public static JsonResult InsertPart(int Company_ID, int User_ID, string data, string constring, int LogException)
        {
            var Details = default(dynamic);
            int? NullType = null;
            int NParts_ID = 0;
            decimal? NullWeight = null;
            var x = default(dynamic);
            try
            {

                JTokenReader jsonReader = null;
                JObject jObj = JObject.Parse(data);
                jObj["Company_ID"] = Convert.ToInt32(Company_ID);
                var headerColumns = new Dictionary<string, bool>
                {
                    { "Company_ID", true },//
                    { "PartID", true },//
                    
                    { "PartsDescription", true },//
                    
                    
                   
                    { "PartsCategory", true },//
                    { "chkIsActive", true },//
                 
                    
                    
                    { "PartPrefix", true },//
                 

                    { "MovementType", true },//
              
                   
                   
                    { "ExciseDuty_ID", true },//
                   
                    
                };
                List<string> invalidColumns;
                bool isHeaderValid = Common.ValidateAndLog(jObj, headerColumns, out invalidColumns);
                if (!isHeaderValid)
                {
                    x = new
                    {
                        NParts_ID = 0,
                        Parts_ID = 0,
                        ExciseDuty_ID = 0,
                        Parts_PartsNumber = "",
                    };
                    return new JsonResult(x);
                }
                jsonReader = new JTokenReader(jObj["PartID"]);
                jsonReader.Read();
                string Parts_ID = jsonReader.Value.ToString();
                jsonReader = new JTokenReader(jObj["PartName"]);
                jsonReader.Read();
                string PartName = Uri.UnescapeDataString(jsonReader.Value.ToString());
                jsonReader = new JTokenReader(jObj["PartsDescription"]);
                jsonReader.Read();
                string PartsDescription = Uri.UnescapeDataString(jsonReader.Value.ToString());
                jsonReader = new JTokenReader(jObj["UOM"]);
                jsonReader.Read();
                string UOM = jsonReader.Value.ToString();
                jsonReader = new JTokenReader(jObj["PartsCategory"]);
                jsonReader.Read();
                string PartsCategory = jsonReader.Value.ToString();
                jsonReader = new JTokenReader(jObj["PartsFunctionGroup"]);
                jsonReader.Read();
                string PartsFunctionGroup = jsonReader.Value.ToString();
                jsonReader = new JTokenReader(jObj["chkIsActive"]);
                jsonReader.Read();
                string chkIsActive = jsonReader.Value.ToString();
                jsonReader = new JTokenReader(jObj["ChkIsComponent"]);
                jsonReader.Read();
                string chkIsComponent = jsonReader.Value.ToString();
                jsonReader = new JTokenReader(jObj["chkIsHazardousGood"]);
                jsonReader.Read();
                string chkIsHazadous = jsonReader.Value.ToString();

                jsonReader = new JTokenReader(jObj["IsLocal"]);
                jsonReader.Read();
                string IsLocal = jsonReader.Value.ToString();

                jsonReader = new JTokenReader(jObj["PartPrefix"]);
                jsonReader.Read();
                string PartPrefix = jsonReader.Value.ToString();

                jsonReader = new JTokenReader(jObj["AliasPartPrefix"]);
                jsonReader.Read();
                string AliasPartPrefix = jsonReader.Value.ToString();

                jsonReader = new JTokenReader(jObj["AliasPartName"]);
                jsonReader.Read();
                string AliasPartName = jsonReader.Value.ToString();

                jsonReader = new JTokenReader(jObj["MovementType"]);
                jsonReader.Read();
                int MovementType = Convert.ToInt32(jsonReader.Value.ToString());

                jsonReader = new JTokenReader(jObj["Weight"]);
                jsonReader.Read();
                string Weight = jsonReader.Value.ToString();

                jsonReader = new JTokenReader(jObj["Dimension"]);
                jsonReader.Read();
                string Dimension = jsonReader.Value.ToString();

                jsonReader = new JTokenReader(jObj["CustomsCode"]);
                jsonReader.Read();
                string CustomsCode = jsonReader.Value.ToString();

                jsonReader = new JTokenReader(jObj["ExciseDuty_ID"]);
                jsonReader.Read();
                int? ExciseDuty = Convert.ToInt32(jsonReader.Value.ToString());
                if (ExciseDuty == 0)
                {
                    ExciseDuty = null;
                }
                //jsonReader = new JTokenReader(jObj["StockUsedInKits"]);
                //jsonReader.Read();
                //string StockUsedInKits = jsonReader.Value.ToString();
                //Added by kavitha for JoyGlobal changes-start
                int? SalvagePart_ID = null;
                jsonReader = new JTokenReader(jObj["SalvagePart_ID"]);
                jsonReader.Read();
                if (jsonReader.Value.ToString() == "")
                {
                    SalvagePart_ID = null;
                }
                else { SalvagePart_ID = Convert.ToInt32(jsonReader.Value.ToString()); }


                int? Parttype = null;
                jsonReader = new JTokenReader(jObj["Parttype"]);
                jsonReader.Read();
                if (jsonReader.Value.ToString() == "0")
                {
                    Parttype = null;
                }
                else { Parttype = Convert.ToInt32(jsonReader.Value.ToString()); }
                int? PartsDisposal_ID = null;
                jsonReader = new JTokenReader(jObj["PartsDisposal_ID"]);
                jsonReader.Read();
                if (jsonReader.Value.ToString() == "0")
                {
                    PartsDisposal_ID = null;
                }
                else { PartsDisposal_ID = Convert.ToInt32(jsonReader.Value.ToString()); }
                //Added by kavitha for JoyGlobal changes-end
                GNM_Parts InsRow = new GNM_Parts();
                InsRow.Parts_PartsNumber = Uri.UnescapeDataString(PartName);
                InsRow.UnitOfMeasurement_ID = Convert.ToInt32(UOM);
                InsRow.PartsCategory_ID = PartsCategory == "0" ? NullType : Convert.ToInt32(PartsCategory);
                InsRow.PartsFunctionGroup_ID = PartsFunctionGroup == "" ? NullType : Convert.ToInt32(PartsCategory);
                InsRow.Parts_IsActive = (chkIsActive == "checked") ? true : false;
                InsRow.Parts_IsComponent = (chkIsComponent == "checked") ? true : false;
                InsRow.Parts_IsHazardousGood = (chkIsHazadous == "checked") ? true : false;
                InsRow.Parts_PartsDescription = Uri.UnescapeDataString(PartsDescription);
                InsRow.Parts_IsLocal = (IsLocal == "checked") ? true : false;
                InsRow.Parts_PartPrefix = PartPrefix;
                InsRow.Parts_AliasPartPrefix = AliasPartPrefix;
                InsRow.Parts_AliasPartNumber = AliasPartName;
                InsRow.MovementType_ID = MovementType;
                InsRow.Parts_Weight = Weight == "" ? NullWeight : Convert.ToDecimal(Weight);
                InsRow.Parts_Dimensions = Dimension;
                //InsRow.PartsCustomsCode_ID = CustomsCode == "0" ? NullType : Convert.ToInt32(CustomsCode);
                InsRow.PartsCustomsCode_ID = NullType;
                //InsRow.SupersessionType_ID = StockUsedInKits == "" ? NullType : Convert.ToInt32(StockUsedInKits);
                InsRow.ExciseDuty_ID = ExciseDuty;
                InsRow.Company_ID = Company_ID;
                InsRow.ModifiedBy = User_ID;
                InsRow.ModifiedDate = DateTime.Now;
                //Added by kavitha for JoyGlobal changes-start
                InsRow.SalvagePart_ID = SalvagePart_ID;
                InsRow.PartType = Parttype;
                InsRow.PartsDisposal_ID = PartsDisposal_ID;
                //Added by kavitha for JoyGlobal changes-end
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    string xmlInput = $@"
                    <Parts>
                        <Part>
                            <PartName>{(string.IsNullOrEmpty(PartName) ? (object)DBNull.Value : Uri.UnescapeDataString(PartName))}</PartName>
                            <UOM>{(string.IsNullOrEmpty(UOM) ? (object)DBNull.Value : Convert.ToInt32(UOM))}</UOM>
                            <PartsCategory>{(string.IsNullOrEmpty(PartsCategory) || PartsCategory == "0" ? (object)DBNull.Value : Convert.ToInt32(PartsCategory))}</PartsCategory>
                            <PartsFunctionGroup>{(string.IsNullOrEmpty(PartsFunctionGroup) ? (object)DBNull.Value : Convert.ToInt32(PartsFunctionGroup))}</PartsFunctionGroup>
                            <chkIsActive>{(chkIsActive == "checked" ? 1 : 0)}</chkIsActive>
                            <chkIsComponent>{(chkIsComponent == "checked" ? 1 : 0)}</chkIsComponent>
                            <chkIsHazardous>{(chkIsHazadous == "checked" ? 1 : 0)}</chkIsHazardous>
                            <PartsDescription>{(string.IsNullOrEmpty(PartsDescription) ? (object)DBNull.Value : Uri.UnescapeDataString(PartsDescription))}</PartsDescription>
                            <IsLocal>{(IsLocal == "checked" ? 1 : 0)}</IsLocal>
                            <PartPrefix>{(string.IsNullOrEmpty(PartPrefix) || PartPrefix == "undefined" ? (object)DBNull.Value : PartPrefix)}</PartPrefix>
                            <AliasPartPrefix>{(string.IsNullOrEmpty(AliasPartPrefix) || AliasPartPrefix == "undefined" ? (object)DBNull.Value : AliasPartPrefix)}</AliasPartPrefix>
                            <AliasPartName>{(string.IsNullOrEmpty(AliasPartName) || AliasPartName == "undefined" ? (object)DBNull.Value : AliasPartName)}</AliasPartName>
                            <MovementType>{(MovementType == 0 ? (object)DBNull.Value : MovementType)}</MovementType>
                            <Weight>{(string.IsNullOrEmpty(Weight) ? (object)DBNull.Value : Convert.ToDecimal(Weight))}</Weight>
                            <Dimension>{(string.IsNullOrEmpty(Dimension) ? (object)DBNull.Value : Dimension)}</Dimension>
                            <CustomsCode>{(string.IsNullOrEmpty(CustomsCode) ? (object)DBNull.Value : CustomsCode)}</CustomsCode>
                            <ExciseDuty>{(ExciseDuty != null && ExciseDuty != 0 ? ExciseDuty : (object)DBNull.Value)}</ExciseDuty>
                            <Company_ID>{(Company_ID == 0 ? (object)DBNull.Value : Company_ID)}</Company_ID>
                            <User_ID>{User_ID}</User_ID>
                            <ModifiedDate>{DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss")}</ModifiedDate>
                            <SalvagePart_ID>{(SalvagePart_ID ?? (object)DBNull.Value)}</SalvagePart_ID>
                            <PartType>{(Parttype ?? (object)DBNull.Value)}</PartType>
                            <PartsDisposal_ID>{(PartsDisposal_ID ?? (object)DBNull.Value)}</PartsDisposal_ID>
                        </Part>
                    </Parts>";


                    SqlCommand cmd = new SqlCommand("UP_INS_AMERP_InsertGNMPart1", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@PartsXML", xmlInput);

                    conn.Open();
                    NParts_ID = Convert.ToInt32(cmd.ExecuteScalar());
                    conn.Close();
                }

                x = new
                {
                    NParts_ID = NParts_ID,
                    Parts_ID = Convert.ToInt32(Parts_ID),
                    ExciseDuty_ID = InsRow.ExciseDuty_ID == null ? 0 : InsRow.ExciseDuty_ID,
                    Parts_PartsNumber = InsRow.Parts_PartsNumber,
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                x = new
                {
                    NParts_ID = 0,
                    Parts_ID = 0,
                    ExciseDuty_ID = 0,
                    Parts_PartsNumber = "",
                };
            }
            return new JsonResult(x);
        }



        private static bool ValidateAndLog(JObject row, Dictionary<string, bool> columns)
        {
            bool isValid = true; // Assume all are valid initially

            foreach (var column in columns)
            {
                string columnName = column.Key;
                bool isMandatory = column.Value;

                // Validate mandatory columns
                if (isMandatory)
                {
                    var columnValue = row[columnName]?.ToString();

                    if (string.IsNullOrWhiteSpace(columnValue) ||
                        (int.TryParse(columnValue, out int intValue) && intValue == 0))
                    {
                        LS.LogSheetExporter.LogToTextFile(0,
                            $"Validation Error: {columnName} is mandatory but missing or invalid.",
                            nameof(ValidateAndLog),
                            $"Row data: {row.ToString()}");

                        // Mark as invalid
                        isValid = false;
                    }
                }
            }

            return isValid;
        }


        #region ::: Insert Part:::
        /// <summary>
        /// Method to insert the Parts Master Header Table
        /// </summary>   
        public static IActionResult Insert(PartInserList Obj, string constring, int LogException)
        {
            //GNM_User User = (GNM_User)Session["UserDetails"];

            int BranchID = Convert.ToInt32(Obj.Branch);
            int userID = Obj.User_ID;
            int CompanyID = Obj.Company_ID;
            int? NullType = null;
            decimal? NullWeight = null;
            var x = default(dynamic);
            try
            {
                var JR = default(dynamic);

                JR = InsertPart(CompanyID, userID, Obj.data, constring, LogException);
                //gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"]), Convert.ToInt32(Session["Branch"]), Convert.ToInt32(Session["User_ID"]), Convert.ToInt32(Common.GetObjectID("CorePartsMaster")), JR.Data.Parts_ID, 0, 0, "Insert", false);
                //gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"].ToString()), BranchID, User.User_ID, Common.GetObjectID("CorePartsMaster"), JR.Data.Parts_ID, 0, 0, "Inserted Part Number " + JR.Data.Parts_PartsNumber + "", false, Convert.ToInt32(Session["MenuID"]), Convert.ToDateTime(Session["LoggedINDateTime"]));

                string SMP = BucketFilePath;
                var result = JR.Value as dynamic;
                int ObjectID = Common.GetObjectID("CorePartsMaster");
                int History = 0;
                History = JR.Value.Parts_ID;
                if (JR.Value.Parts_ID != 0)
                {
                    int WarrentyID = JR.Data.Parts_ID;
                    if (Obj.AttachmentsData != null)
                    {

                        string SrcPath = string.Empty;

                        List<Attachements> dsattachment = new List<Attachements>();
                        JObject jObj1 = JObject.Parse(Obj.AttachmentsData);
                        int Count1 = jObj1["rows"].Count();
                        Attachements[] ds = new Attachements[Count1];
                        for (int i = 0; i < Count1; i++)
                        {
                            Attachements detail = new Attachements();
                            ds[i] = detail;
                            JTokenReader reader = null;
                            reader = new JTokenReader(jObj1["rows"][i]["ATTACHMENTDETAIL_ID"]);
                            reader.Read();
                            ds[i].ATTACHMENTDETAIL_ID = Convert.ToInt32(reader.Value.ToString());
                            reader = new JTokenReader(jObj1["rows"][i]["FILENAME"]);
                            reader.Read();
                            ds[i].FILE_NAME = Common.DecryptString(reader.Value.ToString());
                            reader = new JTokenReader(jObj1["rows"][i]["FILEDESCRIPTION"]);
                            reader.Read();
                            ds[i].FILEDESCRIPTION = Common.DecryptString(reader.Value.ToString());

                            reader = new JTokenReader(jObj1["rows"][i]["Upload"]);
                            reader.Read();
                            ds[i].Upload = Convert.ToInt32(reader.Value.ToString());

                            reader = new JTokenReader(jObj1["rows"][i]["UPLOADDATE"]);
                            reader.Read();
                            ds[i].UPLOADDATE = Convert.ToDateTime(reader.Value.ToString());


                            reader = new JTokenReader(jObj1["rows"][i]["OBJECT_ID"]);
                            reader.Read();
                            ds[i].OBJECTID = Convert.ToInt32(reader.Value.ToString());


                            ds[i].DetailID = 0;
                            string DstPath = SMP + ConfigurationManager.AppSettings.Get("AttachmentPath").ToString() + ds[i].OBJECTID + "-" + JR.Data.Parts_ID + "-" + Common.DecryptString(ds[i].FILE_NAME);
                            SrcPath = SMP + ConfigurationManager.AppSettings.Get("AttachmentPath").ToString() + "Temp_" + Convert.ToInt32(Obj.ObjectID.ToString()) + "_" + Convert.ToInt32(Obj.User_ID.ToString()) + ConfigurationManager.AppSettings.Get("AttachmentPath").ToString() + Common.DecryptString(ds[i].FILE_NAME);

                            if (!System.IO.File.Exists(DstPath))
                            {
                                System.IO.File.Move(SrcPath, DstPath);

                            }

                        }
                        List<Attachements> c = CommonFunctionalities.UploadAttachment(ds, JR.Data.Parts_ID, Convert.ToInt32(Obj.User_ID.ToString()), Convert.ToInt32(Obj.Company_ID.ToString()), 0, constring);

                        //Session["AttachmentData"] = null;
                    }

                    int Count2 = 0;
                    List<Attachements> dsattachdelete = new List<Attachements>();
                    JObject jObj2 = new JObject();
                    if (Obj.PartsAttachmentDelete != null)
                    {
                        jObj2 = JObject.Parse(Obj.PartsAttachmentDelete);
                        Count2 = jObj2["rows"].Count();
                    }
                    Attachements[] ds1 = new Attachements[Count2];
                    for (int i = 0; i < Count2; i++)
                    {
                        Attachements detail = new Attachements();
                        ds1[i] = detail;
                        JTokenReader reader = null;
                        reader = new JTokenReader(jObj2["rows"][i]["id"]);
                        reader.Read();
                        ds1[i].ATTACHMENTDETAIL_ID = Convert.ToInt32(reader.Value.ToString());
                        reader = new JTokenReader(jObj2["rows"][i]["FileName"]);
                        reader.Read();
                        ds1[i].FILE_NAME = Common.DecryptString(reader.Value.ToString());
                        reader = new JTokenReader(jObj2["rows"][i]["Object_ID"]);
                        reader.Read();
                        ds1[i].OBJECTID = Convert.ToInt32(reader.Value.ToString());
                        ds1[i].TransactionID = JR.Data.Parts_ID;
                    }
                    CommonFunctionalities.DeleteAttachments(ds1, SMP, constring);

                    int AttCount = CommonFunctionalities.GetAttachmentCount(ObjectID, JR.Data.Parts_ID, 0, constring);
                    //GNM_Parts AttachmentCountupdate = PartsClient.GNM_Parts.Where(a => a.Parts_ID == History).FirstOrDefault();
                    //AttachmentCountupdate.AttachmentCount = Convert.ToByte(AttCount);
                    //PartsClient.SaveChanges();

                    using (SqlConnection connection = new SqlConnection(constring))
                    {
                        connection.Open();

                        // Step 1: Retrieve the GNM_Parts record with the specified Parts_ID
                        string selectQuery = "SELECT AttachmentCount FROM GNM_Parts WHERE Parts_ID = @Parts_ID";
                        SqlCommand selectCommand = new SqlCommand(selectQuery, connection);
                        selectCommand.Parameters.AddWithValue("@Parts_ID", JR.Data.Parts_ID);

                        object existingRecord = selectCommand.ExecuteScalar();

                        if (existingRecord != null)
                        {
                            // Step 2: Update the AttachmentCount field
                            string updateQuery = "UPDATE GNM_Parts SET AttachmentCount = @AttachmentCount WHERE Parts_ID = @Parts_ID";
                            SqlCommand updateCommand = new SqlCommand(updateQuery, connection);
                            updateCommand.Parameters.AddWithValue("@AttachmentCount", AttCount);
                            updateCommand.Parameters.AddWithValue("@Parts_ID", JR.Data.Parts_ID);

                            updateCommand.ExecuteNonQuery();
                        }
                    }


                }
                x = new
                {
                    JR.Value.NParts_ID,
                    ExciseDuty_ID = JR.Value.ExciseDuty_ID == null ? 0 : JR.Value.ExciseDuty_ID
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                x = new
                {
                    Parts_ID = 0,
                    ExciseDuty_ID = 0
                };
            }
            return new JsonResult(x);
        }
        #endregion


        #region ::: Update Part:::
        /// <summary>
        /// Method to update the Parts Master Header Table
        /// </summary>   
        /// 
        public static IActionResult Update(UpdateInserList Obj, string constring, int LogException)
        {
            //GNM_User User = (GNM_User)Session["UserDetails"];
            int BranchID = Convert.ToInt32(Obj.Branch);
            int userID = Obj.User_ID;
            decimal? NullWeight = null;
            int? NullType = null;
            var jsonres = default(dynamic);
            try
            {
                JTokenReader jsonReader = null;
                JObject jObj = JObject.Parse(Obj.data);

                // Extract values from JSON
                int partsID = Convert.ToInt32(jObj["PartID"]);
                string partName = Uri.UnescapeDataString(jObj["PartName"].ToString());
                string partsDescription = Uri.UnescapeDataString(jObj["PartsDescription"].ToString());
                int uom = Convert.ToInt32(jObj["UOM"].ToString());
                int? partsCategory = string.IsNullOrEmpty(jObj["PartsCategory"].ToString()) || jObj["PartsCategory"].ToString() == "0" ? (int?)null : Convert.ToInt32(jObj["PartsCategory"].ToString());
                int? partsFunctionGroup = string.IsNullOrEmpty(jObj["PartsFunctionGroup"].ToString()) ? (int?)null : Convert.ToInt32(jObj["PartsFunctionGroup"].ToString());
                bool isActive = jObj["chkIsActive"].ToString() == "checked";
                bool isComponent = jObj["ChkIsComponent"].ToString() == "checked";
                bool isHazardous = jObj["chkIsHazardousGood"].ToString() == "checked";
                bool isLocal = jObj["IsLocal"].ToString() == "checked";
                string partPrefix = jObj["PartPrefix"].ToString();
                string aliasPartPrefix = jObj["AliasPartPrefix"].ToString();
                string aliasPartName = jObj["AliasPartName"].ToString();
                int movementType = Convert.ToInt32(jObj["MovementType"].ToString());
                decimal? weight = string.IsNullOrEmpty(jObj["Weight"].ToString()) ? (decimal?)null : Convert.ToDecimal(jObj["Weight"].ToString());
                string dimension = jObj["Dimension"].ToString();
                int? exciseDuty = string.IsNullOrEmpty(jObj["ExciseDuty_ID"].ToString()) ? (int?)null : Convert.ToInt32(jObj["ExciseDuty_ID"].ToString());
                int? salvagePartID = string.IsNullOrEmpty(jObj["SalvagePart_ID"].ToString()) ? (int?)null : Convert.ToInt32(jObj["SalvagePart_ID"].ToString());
                int? partType = string.IsNullOrEmpty(jObj["Parttype"].ToString()) || jObj["Parttype"].ToString() == "0" ? (int?)null : Convert.ToInt32(jObj["Parttype"].ToString());
                int? partsDisposalID = string.IsNullOrEmpty(jObj["PartsDisposal_ID"].ToString()) || jObj["PartsDisposal_ID"].ToString() == "0" ? (int?)null : Convert.ToInt32(jObj["PartsDisposal_ID"].ToString());

                // ADO.NET connection and command
                string connectionString = constring;
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();

                    // Call the stored procedure
                    using (SqlCommand command = new SqlCommand("UP_UPD_AMERP_UpdateGNMParts", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        command.Parameters.AddWithValue("@PartsID", partsID);
                        command.Parameters.AddWithValue("@PartName", partName);
                        command.Parameters.AddWithValue("@PartsDescription", partsDescription);
                        command.Parameters.AddWithValue("@UOM", uom);
                        command.Parameters.AddWithValue("@PartsCategory", (object)partsCategory ?? DBNull.Value);
                        command.Parameters.AddWithValue("@PartsFunctionGroup", (object)partsFunctionGroup ?? DBNull.Value);
                        command.Parameters.AddWithValue("@IsActive", isActive);
                        command.Parameters.AddWithValue("@IsComponent", isComponent);
                        command.Parameters.AddWithValue("@IsHazardous", isHazardous);
                        command.Parameters.AddWithValue("@IsLocal", isLocal);
                        command.Parameters.AddWithValue("@PartPrefix", string.IsNullOrEmpty(partPrefix) ? (object)DBNull.Value : partPrefix);
                        command.Parameters.AddWithValue("@AliasPartPrefix", string.IsNullOrEmpty(aliasPartPrefix) ? (object)DBNull.Value : aliasPartPrefix);
                        command.Parameters.AddWithValue("@AliasPartName", string.IsNullOrEmpty(aliasPartName) ? (object)DBNull.Value : aliasPartName);
                        command.Parameters.AddWithValue("@MovementType", movementType);
                        command.Parameters.AddWithValue("@Weight", (object)weight ?? DBNull.Value);
                        command.Parameters.AddWithValue("@Dimension", string.IsNullOrEmpty(dimension) ? (object)DBNull.Value : dimension);
                        command.Parameters.AddWithValue("@ExciseDuty", exciseDuty.HasValue ? (object)exciseDuty : DBNull.Value);
                        command.Parameters.AddWithValue("@SalvagePartID", salvagePartID.HasValue ? (object)salvagePartID : DBNull.Value);
                        command.Parameters.AddWithValue("@PartType", partType.HasValue ? (object)partType : DBNull.Value);
                        command.Parameters.AddWithValue("@PartsDisposalID", partsDisposalID.HasValue ? (object)partsDisposalID : DBNull.Value);
                        command.Parameters.AddWithValue("@ModifiedBy", userID);
                        command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                        // Execute the command
                        command.ExecuteNonQuery();
                    }
                }
                //gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"]), Convert.ToInt32(Session["Branch"]), Convert.ToInt32(Session["User_ID"]), Convert.ToInt32(Common.GetObjectID("CorePartsMaster")), PartsID, 0, 0, "Update", false);
                // gbl.InsertGPSDetails(Convert.ToInt32(Obj.Company_ID.ToString()), BranchID, Obj.User_ID, Common.GetObjectID("CorePartsMaster",constring), partsID, 0, 0, "Updated Part Number " + InsRow.Parts_PartsNumber + "", false, Convert.ToInt32(Obj.MenuID), Convert.ToDateTime(Obj.LoggedINDateTime));

                string SMP = BucketFilePath;
                int ObjectID = Common.GetObjectID("CorePartsMaster");
                int History = partsID;
                if (partsID != 0)
                {
                    int WarrentyID = partsID;
                    if (Obj.AttachmentsData != null)
                    {


                        string SrcPath = string.Empty;

                        List<Attachements> dsattachment = new List<Attachements>();
                        JObject jObj1 = JObject.Parse(Obj.AttachmentsData);
                        int Count1 = jObj1["rows"].Count();
                        Attachements[] ds = new Attachements[Count1];
                        for (int i = 0; i < Count1; i++)
                        {
                            Attachements detail = new Attachements();
                            ds[i] = detail;
                            JTokenReader reader = null;
                            reader = new JTokenReader(jObj1["rows"][i]["ATTACHMENTDETAIL_ID"]);
                            reader.Read();
                            ds[i].ATTACHMENTDETAIL_ID = Convert.ToInt32(reader.Value.ToString());
                            reader = new JTokenReader(jObj1["rows"][i]["FILENAME"]);
                            reader.Read();
                            ds[i].FILE_NAME = Common.DecryptString(reader.Value.ToString());
                            reader = new JTokenReader(jObj1["rows"][i]["FILEDESCRIPTION"]);
                            reader.Read();
                            ds[i].FILEDESCRIPTION = Common.DecryptString(reader.Value.ToString());

                            reader = new JTokenReader(jObj1["rows"][i]["Upload"]);
                            reader.Read();
                            ds[i].Upload = Convert.ToInt32(reader.Value.ToString());

                            reader = new JTokenReader(jObj1["rows"][i]["UPLOADDATE"]);
                            reader.Read();
                            ds[i].UPLOADDATE = Convert.ToDateTime(reader.Value.ToString());


                            reader = new JTokenReader(jObj1["rows"][i]["OBJECT_ID"]);
                            reader.Read();
                            ds[i].OBJECTID = Convert.ToInt32(reader.Value.ToString());


                            ds[i].DetailID = 0;
                            string DstPath = SMP + "/" + ds[i].OBJECTID + "-" + partsID + "-" + Common.DecryptString(ds[i].FILE_NAME);
                            SrcPath = SMP + "/" + "Temp_" + Convert.ToInt32(Obj.ObjectID.ToString()) + "_" + Convert.ToInt32(Obj.User_ID.ToString()) + "/" + Common.DecryptString(ds[i].FILE_NAME);

                            if (!System.IO.File.Exists(DstPath))
                            {
                                System.IO.File.Move(SrcPath, DstPath);

                            }

                        }
                        List<Attachements> c = CommonFunctionalities.UploadAttachment(ds, partsID, Convert.ToInt32(Obj.User_ID.ToString()), Convert.ToInt32(Obj.Company_ID.ToString()), 0, constring);

                        //Session["AttachmentData"] = null;
                    }


                    List<Attachements> dsattachdelete = new List<Attachements>();
                    JObject jObj2 = new JObject();
                    jObj2 = JObject.Parse(Obj.PartsAttachmentDelete);
                    int Count2 = jObj2["rows"].Count();
                    Attachements[] ds1 = new Attachements[Count2];
                    for (int i = 0; i < Count2; i++)
                    {
                        Attachements detail = new Attachements();
                        ds1[i] = detail;
                        JTokenReader reader = null;
                        reader = new JTokenReader(jObj2["rows"][i]["id"]);
                        reader.Read();
                        ds1[i].ATTACHMENTDETAIL_ID = Convert.ToInt32(reader.Value.ToString());
                        reader = new JTokenReader(jObj2["rows"][i]["FileName"]);
                        reader.Read();
                        ds1[i].FILE_NAME = Common.DecryptString(reader.Value.ToString());
                        reader = new JTokenReader(jObj2["rows"][i]["Object_ID"]);
                        reader.Read();
                        ds1[i].OBJECTID = Convert.ToInt32(reader.Value.ToString());
                        ds1[i].TransactionID = partsID;
                    }
                    CommonFunctionalities.DeleteAttachments(ds1, SMP, constring);

                    int AttCount = CommonFunctionalities.GetAttachmentCount(ObjectID, partsID, 0, constring);
                    //GNM_Parts AttachmentCountupdate = PartsClient.GNM_Parts.Where(a => a.Parts_ID == History).FirstOrDefault();
                    //AttachmentCountupdate.AttachmentCount = Convert.ToByte(AttCount);
                    //PartsClient.SaveChanges();

                    using (SqlConnection connection = new SqlConnection(constring))
                    {
                        connection.Open();

                        // Step 1: Retrieve the GNM_Parts record with the specified Parts_ID
                        string selectQuery = "SELECT AttachmentCount FROM GNM_Parts WHERE Parts_ID = @Parts_ID";
                        SqlCommand selectCommand = new SqlCommand(selectQuery, connection);
                        selectCommand.Parameters.AddWithValue("@Parts_ID", partsID);

                        object existingRecord = selectCommand.ExecuteScalar();

                        if (existingRecord != null)
                        {
                            // Step 2: Update the AttachmentCount field
                            string updateQuery = "UPDATE GNM_Parts SET AttachmentCount = @AttachmentCount WHERE Parts_ID = @Parts_ID";
                            SqlCommand updateCommand = new SqlCommand(updateQuery, connection);
                            updateCommand.Parameters.AddWithValue("@AttachmentCount", AttCount);
                            updateCommand.Parameters.AddWithValue("@Parts_ID", partsID);

                            updateCommand.ExecuteNonQuery();
                        }

                        using (SqlCommand command = new SqlCommand("SELECT ExciseDuty_ID FROM GNM_Parts WHERE Parts_ID = @PartsID", connection))
                        {
                            command.Parameters.AddWithValue("@PartsID", partsID);

                            // Execute the query and get the ExciseDuty_ID
                            var exciseDutyID = command.ExecuteScalar();
                            int exciseDutyIDValue = exciseDutyID == null ? 0 : Convert.ToInt32(exciseDutyID);

                            // Create the jsonres object
                            jsonres = new
                            {
                                ExciseDuty_ID = exciseDutyIDValue
                            };

                            return jsonres; // Return the JSON response
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonres);
        }
        #endregion

        #region ::: SelAllAttachmentList:::
        /// <summary>
        /// To display all the records
        /// </summary>
        /// <returns>...</returns>

        public static IActionResult SelAllAttachmentList(AllAttachmentList Obj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters)
        {
            var jsonResult = default(dynamic);
            //GNM_User User = (GNM_User)Session["UserDetails"];
            int companyID = Obj.Company_ID;
            int userID = Obj.User_ID;
            int branchID = Convert.ToInt32(Obj.Branch);
            int count = 0;
            int total = 0;
            int userLanguageID = Convert.ToInt32(Obj.UserLanguageID);
            int generalLanguageID = Convert.ToInt32(Obj.GeneralLanguageID);
            var attachments = new List<PartAttachments>();
            IQueryable<PartAttachments> PartsAttach = null;
            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Query to retrieve attachments for the specified part
                    string attachmentQuery = @"
                     SELECT a.PARTSATTACHMENTDETAIL_ID, a.FILENAME, a.FILEDESCRIPTION, a.FILETYPE, 
                        b.RefMasterDetail_Name, b.RefMasterDetail_Name
                         FROM GNM_PARTSATTACHMENTDETAIL a
                         JOIN GNM_RefMasterDetail b ON a.FILETYPE = b.RefMasterDetail_ID
                         LEFT JOIN GNM_RefMasterDetailLocale c ON a.FILETYPE = c.RefMasterDetail_ID 
                         AND c.Language_ID = @UserLanguageID
                         WHERE a.PARTS_ID = @PartsID";

                    using (SqlCommand cmd = new SqlCommand(attachmentQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@PartsID", Obj.partsID);
                        cmd.Parameters.AddWithValue("@UserLanguageID", userLanguageID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                string fileType = userLanguageID == generalLanguageID
                                    ? reader["RefMasterDetail_Name"].ToString()
                                    : reader["RefMasterDetail_Name"]?.ToString() ?? reader["RefMasterDetail_Name"].ToString();

                                attachments.Add(new PartAttachments
                                {
                                    ID = Convert.ToInt32(reader["PARTSATTACHMENTDETAIL_ID"]),
                                    view = "<a target='_blank' href='" + AppPath + "/PartsAttachments/" + Obj.partsID + "/" +
                                            (reader["FILENAME"].ToString()) + "' class='OpenAttachment' style='color:blue;text-decoration:underline'>View</a>",
                                    edit = "<a title=" + CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "view").ToString() +
                                           " href='#' style='font-size: 13px;' id='" + reader["PARTSATTACHMENTDETAIL_ID"] +
                                           "' key='" + reader["PARTSATTACHMENTDETAIL_ID"] + "' class='editPartAttachment'>" +
                                           "<i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                    delete = "<input type='checkbox' key='" + reader["PARTSATTACHMENTDETAIL_ID"] + "' class='DelPartAttachment'/>",
                                    FileName = reader["FILENAME"].ToString(),
                                    FileDescription = reader["FILEDESCRIPTION"].ToString(),
                                    FileType = fileType
                                });
                            }
                        }
                    }
                }

                PartsAttach = attachments.AsQueryable();
                count = PartsAttach.Count();
                total = rows > 0 ? (int)Math.Ceiling((double)count / rows) : 0;
                PartsAttach = PartsAttach.OrderByField<PartAttachments>(sidx, sord);
                //FilterToolBar Search
                if (_search)
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    if (filtersObj.rules.Count() > 0)
                        PartsAttach = PartsAttach.FilterSearch<PartAttachments>(filtersObj);
                }

                // Apply pagination
                PartsAttach = (IQueryable<PartAttachments>)PartsAttach.Skip((page - 1) * rows).Take(rows).ToList();

                jsonResult = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = attachments
                };
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonResult);
        }
        #endregion

        #region ::: LoadAttachmentDropDown :::
        public static IActionResult LoadAttachmentDropDown(LoadAttachmentDropDownList Obj, string constring, int LogException)
        {
            var jsonResult = new List<dynamic>();
            int userLanguageID = Convert.ToInt32(Obj.UserLanguageID);
            int generalLanguageID = Convert.ToInt32(Obj.GeneralLanguageID);

            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    SqlCommand cmd;

                    if (userLanguageID == generalLanguageID)
                    {
                        // Query for general language (no localization)
                        cmd = new SqlCommand(@"
                    SELECT a.RefMasterDetail_ID, a.RefMasterDetail_Name 
                    FROM GNM_RefMasterDetail a
                    JOIN GNM_RefMaster b ON a.RefMaster_ID = b.RefMaster_ID
                    WHERE b.RefMaster_Name = 'FILETYPE' 
                    AND a.RefMasterDetail_IsActive = 1
                    ORDER BY a.RefMasterDetail_Name", conn);
                    }
                    else
                    {
                        // Query for specific user language
                        cmd = new SqlCommand(@"
                    SELECT b.RefMasterDetail_ID, b.RefMasterDetail_Name 
                    FROM GNM_RefMasterDetail a
                    JOIN GNM_RefMaster c ON a.RefMaster_ID = c.RefMaster_ID
                    JOIN GNM_RefMasterDetailLocale b ON a.RefMasterDetail_ID = b.RefMasterDetail_ID
                    WHERE c.RefMaster_Name = 'FILETYPE' 
                    AND a.RefMasterDetail_IsActive = 1 
                    AND b.Language_ID = @UserLanguageID
                    ORDER BY b.RefMasterDetail_Name", conn);
                        cmd.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                    }

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            jsonResult.Add(new
                            {
                                RefMasterDetail_ID = reader["RefMasterDetail_ID"],
                                RefMasterDetail_Name = reader["RefMasterDetail_Name"]
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonResult);
        }
        #endregion


       

    }
    //public class PartsMasterDetails
    //{
    //    public string Parts_PartPrefix { get; set; } = "OEM";
    //    public string Parts_PartsNumber { get; set; } = string.Empty;
    //    public string Parts_PartsDescription { get; set; } = string.Empty;
    //    public string Parts_AliasPartPrefix { get; set; } = "OEM";
    //    public string Parts_AliasPartNumber { get; set; } = string.Empty;
    //    public string MovementType_Description { get; set; } = "Medium";
    //    public decimal Parts_Weight { get; set; } = 0;
    //    public string Parts_Dimensions { get; set; } = string.Empty;
    //    public string PartsCategory_Description { get; set; } = "General";
    //    public string FunctionGroup_Name { get; set; } = string.Empty;
    //    public string UnitOfMeasurement_Short_Name { get; set; } = string.Empty;
    //    public bool Parts_IsActive { get; set; } = true;
    //    public bool Parts_IsHazardousGood { get; set; } = false;

    //    public DateTime ModifiedDate { get; set; } = DateTime.UtcNow;

    //    public bool Parts_IsLocal { get; set; } = false;//
    //    public bool Parts_IsComponent { get; set; } = false;//
    //    public bool IsKitPart { get; set; } = false;
    //    public string ExciseDuty_SHORTNAME { get; set; } = string.Empty;
    //    public string SalvagePart_Parts_PartPrefix { get; set; } = "";//
    //    public string SalvagePart_Parts_PartsNumber { get; set; } = "";//
    //    public string PartType_SHORTNAME { get; set; } = "";//
    //    public string PartsDisposal_SHORTNAME { get; set; } = "";//





    //    public string Currency_Short_Name { get; set; } = "INR";//
    //    public string PartyCode { get; set; } = string.Empty;//
    //    public int StandardPackageQuantity { get; set; } = 1;//

    //    public DateTime SAPEffectiveDate { get; set; } = DateTime.Now;//





    //    public bool IsPurchasable { get; set; } = false;
    //    public bool BlockforSales {  get; set; } = false;

    //    public int MinimumOrderQty {  get; set; } = 0;

    //    public int ReorderLevel {  get; set; } = 0;

    //    public int ReorderLevelQty {  get; set; } = 0;
    //    public string Status { get; set; }="insert";



    //}



    public class PartsMasterDetails
    {
        private string _partsPartPrefix;
        private string _partsAliasPartPrefix;
        private string _movementTypeDescription;
        private string _partsCategoryDescription;
        private string _currencyShortName;
        private string _status;

        public string Parts_PartPrefix
        {
            get => string.IsNullOrWhiteSpace(_partsPartPrefix) ? "OEM" : _partsPartPrefix;
            set => _partsPartPrefix = string.IsNullOrWhiteSpace(value) ? "OEM" : value;
        }

        public string Parts_PartsNumber { get; set; } = string.Empty;
        public string Parts_PartsDescription { get; set; } = string.Empty;

        public string Parts_AliasPartPrefix
        {
            get => string.IsNullOrWhiteSpace(_partsAliasPartPrefix) ? "OEM" : _partsAliasPartPrefix;
            set => _partsAliasPartPrefix = string.IsNullOrWhiteSpace(value) ? "OEM" : value;
        }

        public string Parts_AliasPartNumber { get; set; } = string.Empty;

        public string MovementType_Description
        {
            get => string.IsNullOrWhiteSpace(_movementTypeDescription) ? "Medium" : _movementTypeDescription;
            set => _movementTypeDescription = string.IsNullOrWhiteSpace(value) ? "Medium" : value;
        }

        public decimal Parts_Weight { get; set; }
        public string Parts_Dimensions { get; set; } = string.Empty;

        public string PartsCategory_Description
        {
            get => string.IsNullOrWhiteSpace(_partsCategoryDescription) ? "General" : _partsCategoryDescription;
            set => _partsCategoryDescription = string.IsNullOrWhiteSpace(value) ? "General" : value;
        }

        public string FunctionGroup_Name { get; set; } = string.Empty;
        public string UnitOfMeasurement_Short_Name { get; set; } = string.Empty;

        public bool Parts_IsActive { get; set; } = true;
        public bool Parts_IsHazardousGood { get; set; }
        public DateTime ModifiedDate { get; set; } = DateTime.UtcNow;
        public bool Parts_IsLocal { get; set; }
        public bool Parts_IsComponent { get; set; }
        public bool IsKitPart { get; set; }
        public string ExciseDuty_SHORTNAME { get; set; } = string.Empty;
        public string SalvagePart_Parts_PartPrefix { get; set; } = string.Empty;
        public string SalvagePart_Parts_PartsNumber { get; set; } = string.Empty;
        public string PartType_SHORTNAME { get; set; } = string.Empty;
        public string PartsDisposal_SHORTNAME { get; set; } = string.Empty;

        public string Currency_Short_Name
        {
            get => string.IsNullOrWhiteSpace(_currencyShortName) ? "INR" : _currencyShortName;
            set => _currencyShortName = string.IsNullOrWhiteSpace(value) ? "INR" : value;
        }

        public string PartyCode { get; set; } = string.Empty;
        public int StandardPackageQuantity { get; set; } = 1;
        public DateTime SAPEffectiveDate { get; set; } = DateTime.Now;
        public bool IsPurchasable { get; set; }
        public bool BlockforSales { get; set; }
        public int MinimumOrderQty { get; set; }
        public int ReorderLevel { get; set; }
        public int ReorderLevelQty { get; set; }

        public string Status
        {
            get => string.IsNullOrWhiteSpace(_status) ? "insert" : _status;
            set => _status = string.IsNullOrWhiteSpace(value) ? "insert" : value;
        }
    }



    public class PartsMasterList
    {
        private string _branchShortName;
        private string _userLoginID;

        public string BRANCH_SHORTNAME
        {
            get => string.IsNullOrWhiteSpace(_branchShortName) ? "BNG" : _branchShortName;
            set => _branchShortName = string.IsNullOrWhiteSpace(value) ? "BNG" : value;
        }

        public string User_LoginID
        {
            get => string.IsNullOrWhiteSpace(_userLoginID) ? "Admin" : _userLoginID;
            set => _userLoginID = string.IsNullOrWhiteSpace(value) ? "Admin" : value;
        }

        public List<PartsMasterDetails> PartsDetails { get; set; } = new List<PartsMasterDetails>();

        public PartsMasterList()
        {
            BRANCH_SHORTNAME = BRANCH_SHORTNAME; // Ensures default assignment
            User_LoginID = User_LoginID; // Ensures default assignment
        }
    }


    //public class PartsMasterPriceDetails
    //{
    //    public string Parts_PartPrefix { get; set; } = "OEM";
    //    public string Parts_PartsNumber { get; set; } = string.Empty;
    //    public decimal PartsPriceDetail_ListPrice { get; set; } 
    //    public decimal PartsPriceDetail_MRP { get; set; }
    //    public DateTime PartsPriceDetail_EffectiveFrom { get; set; } 
    //    public DateTime PartsSupplierDetail_EffectiveFrom { get; set; } = DateTime.MinValue;
    //    public string BRANCH_SHORTNAME { get; set; } = "YANMAR";

    //    public string CURRENCY_SHORTNAME { get; set; } = "INR";

    //    public int? CustomerWarranty { get; set; } = 0;//
    //    public string Party_Code { get; set; } = string.Empty;
    //    public decimal StandardPackingQuantity { get; set; } = 1;

    //    public string SupplierPartNumber { get; set; } = string.Empty;//
    //    public string SupplierPartPrefix { get; set; } = string.Empty;//
    //    public decimal SupplierPrice { get; set; } 
    //    public decimal CostPrice { get; set; } 
    //    public decimal SupplierYTAPrice { get; set; } 
    //    public decimal SupplierYEAPrice { get; set; } 
    //}


    public class PartsMasterPriceDetails
    {
        public string _partsPartPrefix { get; set; }
        public string Parts_PartPrefix
        {
            get => string.IsNullOrWhiteSpace(_partsPartPrefix) ? "OEM" : _partsPartPrefix;
            set => _partsPartPrefix = string.IsNullOrWhiteSpace(value) ? "OEM" : value;
        }
        public string Parts_PartsNumber { get; set; }
        public decimal PartsPriceDetail_ListPrice { get; set; }
        public decimal PartsPriceDetail_MRP { get; set; }
        public DateTime PartsPriceDetail_EffectiveFrom { get; set; }
        public DateTime PartsSupplierDetail_EffectiveFrom { get; set; }
        public string _branchShortName { get; set; }
        public string BRANCH_SHORTNAME
        {
            get => string.IsNullOrWhiteSpace(_branchShortName) ? "BNG" : _branchShortName;
            set => _branchShortName = string.IsNullOrWhiteSpace(value) ? "BNG" : value;
        }
        public string CURRENCY_SHORTNAME { get; set; }
        public int? CustomerWarranty { get; set; }
        public string Party_Code { get; set; }
        public decimal StandardPackingQuantity { get; set; }
        public string SupplierPartNumber { get; set; }
        public string SupplierPartPrefix { get; set; }
        public decimal SupplierPrice { get; set; }
        public decimal CostPrice { get; set; }
        public decimal SupplierYTAPrice { get; set; }
        public decimal SupplierYEAPrice { get; set; }

        // Constructor to assign default values if null is passed
        public PartsMasterPriceDetails()
        {
            AssignDefaults();
        }

        public void AssignDefaults()
        {
            if (Parts_PartPrefix == null) Parts_PartPrefix = "OEM";
            if (Parts_PartsNumber == null) Parts_PartsNumber = string.Empty;
            if (BRANCH_SHORTNAME == null) BRANCH_SHORTNAME = string.Empty;
            if (CURRENCY_SHORTNAME == null) CURRENCY_SHORTNAME = "INR";
            if (Party_Code == null) Party_Code = string.Empty;
            if (SupplierPartNumber == null) SupplierPartNumber = string.Empty;
            if (SupplierPartPrefix == null) SupplierPartPrefix = string.Empty;

            // Handling nullable values
            CustomerWarranty = CustomerWarranty ?? 0;

            // Ensure standard packing quantity is at least 1
            StandardPackingQuantity = StandardPackingQuantity == 0 ? 1 : StandardPackingQuantity;

            // Handling dates
            if (PartsPriceDetail_EffectiveFrom == default(DateTime))
                PartsPriceDetail_EffectiveFrom = DateTime.UtcNow;

            if (PartsSupplierDetail_EffectiveFrom == default(DateTime))
                PartsSupplierDetail_EffectiveFrom = DateTime.MinValue;

            // Decimal values (default is 0, so no change needed)
        }
    }

    public class PartPriceDetailList
    {
        
        public List<PartsMasterPriceDetails> PartPriceDetails { get; set; } = new List<PartsMasterPriceDetails>();
    }

    public class PartMasterInsertList
    {
        public string Parts_PartPrefix { get; set; }
        public string Parts_PartsNumber { get; set; }
        public string Parts_PartsDescription { get; set; }
        public string Parts_AliasPartPrefix { get; set; }
        public string Parts_AliasPartNumber { get; set; }
        public string MovementType_Description { get; set; }
        public double Parts_Weight { get; set; }
        public string Parts_Dimensions { get; set; }
        public string PartsCategory_Description { get; set; }
        public string FunctionGroup_Name { get; set; }
        public string UnitOfMeasurement_Short_Name { get; set; }
        public bool Parts_IsActive { get; set; }
        public bool Parts_IsHazardousGood { get; set; }
        public string User_LognID { get; set; }
        public DateTime ModifiedDate { get; set; }
        public string BRANCH_SHORTNAME { get; set; }
        public bool Parts_IsLocal { get; set; }
        public bool Parts_IsComponent { get; set; }
        public bool IsKitPart { get; set; }
        public string ExciseDuty_SHORTNAME { get; set; }
        public string SalvagePart_Parts_PartPrefix { get; set; }
        public string SalvagePart_Parts_PartsNumber { get; set; }
        public string PartType_SHORTNAME { get; set; }
        public string PartsDisposal_SHORTNAME { get; set; }
    }


    public class LoadAttachmentDropDownList
    {
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
    }
    public class AllAttachmentList
    {
        public int partsID { get; set; }
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public int Branch { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public string UserCulture { get; set; }
    }
    public class PartAttachments
    {
        public int ID { get; set; }
        public string view { get; set; }
        public string edit { get; set; }
        public string delete { get; set; }
        public string FileName { get; set; }
        public string FileDescription { get; set; }
        public string FileType { get; set; }
    }
    public class UpdateInserList
    {
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public int Branch { get; set; }
        public int ObjectID { get; set; }
        public string AttachmentsData { get; set; }
        public string data { get; set; }
        public string PartsAttachmentDelete { get; set; }
        public int MenuID { get; set; }
        public DateTime LoggedINDateTime { get; set; }
    }
    public class PartInserList
    {
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public int Branch { get; set; }
        public int ObjectID { get; set; }
        public int MenuID { get; set; }
        public DateTime LoggedINDateTime { get; set; }
        public string AttachmentsData { get; set; }
        public string data { get; set; }
        public string PartsAttachmentDelete { get; set; }
    }
    public class SelectPartsList
    {
        public int Company_ID { get; set; }
        public string UserCulture { get; set; }
        public string PartsIDList { get; set; }
    }
    public class GNMParts
    {
        public string view { get; set; }
        public long Parts_ID { get; set; }
        public string Parts_PartsPrefix { get; set; }
        public string Parts_PartsNumber { get; set; }
        public string PartsDescription { get; set; }
        public string PartsCategory { get; set; }
        public string PartsFunctionGroup { get; set; }
        public string UnitOfMeasurement { get; set; }
        public string IsComponent { get; set; }
        public string IsActive { get; set; }
        public int Comp_ID { get; set; }
        public int MovementType_ID { get; set; }
        public string CompName { get; set; }
        public int PartsCategoryID { get; set; }
        public int PartsFunctionGroupID { get; set; }
        public int UnitOfMeasurementID { get; set; }
        public int PartsDisposal_ID { get; set; }
        //triveni
        public string aliasprefix { get; set; }
        public string aliaspartnum { get; set; }
        public string Movement { get; set; }
        public string PartyType { get; set; }
        public string PartsDisposal { get; set; }
        public string Salvageprefix { get; set; }
        public string SalvagePartNum { get; set; }
        public decimal? weight { get; set; }
        public string Dimension { get; set; }
        public string ExciseTariffCode { get; set; }
        public string Islocal { get; set; }
        public string isHazardous { get; set; }
        public string IsPartofKit { get; set; }
        public string IsKitPart { get; set; }
        //--end
        public byte? Attachmentcount { get; set; }
    }


    public class GNMPartsQuery
    {
        public int Parts_ID { get; set; }
        public string Parts_PartPrefix { get; set; }
        public string Parts_PartsNumber { get; set; }
        public string Parts_PartsDescription { get; set; }
        public int? MovementType_ID { get; set; }
        public int? PartsCategory_ID { get; set; }
        public int? PartsFunctionGroup_ID { get; set; }
        public bool Parts_IsActive { get; set; }
        public int Company_ID { get; set; }
        public bool Parts_IsLocal { get; set; }
        public bool Parts_IsComponent { get; set; }
        public int? UnitOfMeasurement_ID { get; set; }
        public int? PartsDisposal_ID { get; set; }
        //triveni
        public string Parts_AliasPartPrefix { get; set; }
        public string Parts_AliasPartNumber { get; set; }
        //public int MovementType_ID { get; set; }
        public int? PartType { get; set; }
        public int? SalvagePart_ID { get; set; }
        public decimal? Parts_Weight { get; set; }
        public string Parts_Dimensions { get; set; }
        public int? ExciseDuty_ID { get; set; }
        // public bool Parts_IsLocal { get; set; }
        public bool Parts_IsHazardousGood { get; set; }
        public bool IsKitPart { get; set; }
        //end
        public byte? Attachmentcount { get; set; }
        public string Attachmentco { get; set; }
    }
    public class SelectPartsPriceLocaleList
    {
        public int Company_ID { get; set; }
        public int id { get; set; }
    }
    public class LoadPartsDropdownLocaleList
    {
        public int Comp_ID { get; set; }
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int UserLanguageID { get; set; }
    }
    public class SelectParticularPartList
    {
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int User_ID { get; set; }
        public int MenuID { get; set; }
        public int id { get; set; }
        public DateTime LoggedINDateTime { get; set; }
    }
    public partial class CoreGNM_PARTSSTOCKSERIALNUMBERDET
    {
        public int PARTSSTOCKSERIALNUMBERDET_ID { get; set; }
        public int PARTSSTOCKDETAIL_ID { get; set; }
        public int PARTS_ID { get; set; }
        public string SERIALNUMBER { get; set; }
        public Nullable<decimal> COST { get; set; }
        public string RECEIPTREFERNCE { get; set; }
        public Nullable<System.DateTime> RECEIPTREFERNCEDATE { get; set; }
        public string ISSUEREFERENCE { get; set; }
        public Nullable<System.DateTime> ISSUEDATE { get; set; }
    }

    public class FieldSearch
    {
        public bool IsDealer { get; set; }
        public int ID { get; set; }
        public string Name { get; set; }
        public string CustomerName { get; set; }
        public string CallDate { get; set; }
        public string Model { get; set; }
        public string Description { get; set; }
        public decimal Rate { get; set; }
        public string Brand { get; set; }
        public string ProductType { get; set; }
        public int Brand_ID { get; set; }
        public int ProductType_ID { get; set; }
        public int Product_ID { get; set; }
        public string Model_Name
        {
            get;
            set;
        }
        public string Location { get; set; }
        public int? FinancialYear { get; set; }
        public DateTime CallDateSort { get; set; }
        public string PartNumber { get; set; }
        public string Partprefix { get; set; }
        public int JobCard_ID { get; set; }
        public string JobCardNumber { get; set; }
        public string SerialNumber { get; set; }
        public string EngineSerialNumber { get; set; }
        public DateTime JobCardDate { get; set; }
        public string Party_Name { get; set; }
        public decimal? FreeStock { get; set; }
        public string OrderDate { get; set; }
        public DateTime OrderDateSort { get; set; }
        public int FinancialYearJob { get; set; }
        public int UOM_ID { get; set; }
        public string UOM { get; set; }
        public string Date { get; set; }
        public string InvoiceNumber { get; set; }
        public string InvoiceType { get; set; }
        public int FinancialYearQuo { get; set; }
        public string RateSrv { get; set; }
        public string FinancialYearSrvFld { get; set; }
        public string MobileNumber { get; set; }
        public string Email { get; set; }
        public string RequestingBranch { get; set; }
        public string Warehouse { get; set; }
        public int WarehouseID { get; set; }
        public string SupplyingBranch { get; set; }
        public string EnquiryType { get; set; }
        public string ReferenceNumber { get; set; }
        public string ReferenceDate { get; set; }
        public DateTime? ReferenceDateSort { get; set; }
        public string InvoiceDate { get; set; }
        public string FinancialYearPackinglist { get; set; }
        public string PackingListID { get; set; }
        public string MTGRNID { get; set; }
        public string MTGRNFinancialYear { get; set; }
        public string InvoiceID { get; set; }
        public string FinancialYearString { get; set; }
        public string IDString { get; set; }
        public bool IsImportExport { get; set; }
        public string ProductComponentSerail { get; set; }
        public int PartyType { get; set; }
        public string PartyTypeStr { get; set; }
        public int ProductComponent_ID { get; set; }
        public int ServiceRequest_ID { get; set; }
        public string ServiceRequestNumber { get; set; }
        public string VCNumber { get; set; }
        public string IsActiveStr { get; set; }
        public bool IsActive { get; set; }
        public string Status { get; set; }
        public string JobCardDatesort { get; set; }
        public int? Party_ID { get; set; }
        public decimal? MRP { get; set; }
        public DateTime SalesInvoiceDate { get; set; }
        public int? Model_ID { get; set; }
        public int? MachineStatus_ID { get; set; }
        public int? ProductCustomer_ID { get; set; }
        public string Product_SerialNumber { get; set; }
        public int Company_ID { get; set; }
        //added by kavitha to dispaly order type in Delivery note and partsordercancellation fieldsearch
        public string OrderType { get; set; }
        //end
        public decimal? OperationWarrantyQuanity { get; set; }
        public string Party_Code { get; set; }
        public string PartyAddress_Address { get; set; }
        public string Reading_Unit { get; set; }
        public string Branch_ShortName { get; set; }
        public string employeecode { get; set; }
        public int? WO_ParentID { get; set; }
        public int WarrantyClaim_ID
        {
            get;
            set;
        }

        public int InternalInvoice_ID { get; set; }
        public string Product_UniqueNo { get; set; }

        public bool Party_IsActive { get; set; }
        public bool Party_IsLocked { get; set; }
        public string Party_Email { get; set; }
        public string Party_Phone { get; set; }
        public string Brand_Name { get; set; }
        public string ProductType_Name { get; set; }
        public int? ContractorID { get; set; }
        public string QuotationNumber { get; set; }
        public string KeyTagNumber { get; set; }
        public DateTime ScheduledStartDateTime { get; set; }
        public DateTime ScheduleEndDateTime { get; set; }
    }
    public partial class GNM_WareHouseLocale
    {
        public int WareHouseLocale_ID { get; set; }
        public int WareHouse_ID { get; set; }
        public int Language_ID { get; set; }
        public string WareHouseName { get; set; }

        public virtual GNM_WareHouse GNM_WareHouse { get; set; }
    }
    public partial class GNM_WareHouseOrderClass
    {
        public int WareHouseOrderClass_ID { get; set; }
        public int WareHouse_ID { get; set; }
        public int OrderClass_ID { get; set; }
        public Nullable<int> Branch_ID { get; set; }

        public virtual OrderClass PRM_OrderClass { get; set; }
        public virtual GNM_WareHouse GNM_WareHouse { get; set; }
    }
    public partial class OrderClassLocale
    {
        public int OrderClassLocale_ID { get; set; }
        public int OrderClass_ID { get; set; }
        public string OrderClass_Description { get; set; }
        public int Language_ID { get; set; }

        public virtual OrderClass PRM_OrderClass { get; set; }
    }
    public partial class OrderClass
    {
        public OrderClass()
        {
            this.GNM_WareHouseOrderClass = new HashSet<GNM_WareHouseOrderClass>();
            this.PRM_OrderClassLocale = new HashSet<OrderClassLocale>();
        }

        public int OrderClass_ID { get; set; }
        public int Company_ID { get; set; }
        public int PartOrderType_ID { get; set; }
        public string OrderClass_Description { get; set; }
        public Nullable<int> OrderClass_LeadTime { get; set; }
        public Nullable<decimal> OrderClass_Discount { get; set; }
        public Nullable<bool> OrderClass_IsConsiderForDemand { get; set; }
        public Nullable<bool> OrderClass_IsProductValidate { get; set; }
        public bool OrderClass_Type { get; set; }
        public bool OrderClass_IsActive { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }

        public virtual ICollection<GNM_WareHouseOrderClass> GNM_WareHouseOrderClass { get; set; }
        public virtual ICollection<OrderClassLocale> PRM_OrderClassLocale { get; set; }
    }
    public partial class GNM_WareHouse
    {
        public GNM_WareHouse()
        {
            this.GNM_WareHouseLocale = new HashSet<GNM_WareHouseLocale>();
            this.GNM_WareHouseOrderClass = new HashSet<GNM_WareHouseOrderClass>();
        }

        public int WareHouse_ID { get; set; }
        public string WareHouseName { get; set; }
        public bool IsActive { get; set; }
        public bool IsDefault { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public Nullable<int> WareHouseType_ID { get; set; }
        public Nullable<bool> IsVisible { get; set; }

        public virtual ICollection<GNM_WareHouseLocale> GNM_WareHouseLocale { get; set; }
        public virtual ICollection<GNM_WareHouseOrderClass> GNM_WareHouseOrderClass { get; set; }
    }
    public partial class GNM_FunctionGroupLocale
    {
        public int FunctionGroupLocale_ID { get; set; }
        public int FunctionGroup_ID { get; set; }
        public string FunctionGroup_Name { get; set; }
        public int Language_ID { get; set; }

        public virtual GNM_FunctionGroup GNM_FunctionGroup { get; set; }
    }
    public partial class GNM_FunctionGroup
    {
        public GNM_FunctionGroup()
        {
            this.GNM_FunctionGroupLocale = new HashSet<GNM_FunctionGroupLocale>();
        }

        public int FunctionGroup_ID { get; set; }
        public int Company_ID { get; set; }
        public string FunctionGroup_Name { get; set; }
        public Nullable<int> Brand_ID { get; set; }
        public bool FunctionGroup_IsActive { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }

        public virtual ICollection<GNM_FunctionGroupLocale> GNM_FunctionGroupLocale { get; set; }
    }
    public class ParentCompanyObject
    {
        public int Company_ID
        {
            get;
            set;
        }

        public string Company_Name
        {
            get;
            set;
        }

        public int Company_Parent_ID
        {
            get;
            set;
        }
    }
    public partial class PRM_CorePartsCategoryDefinition
    {
        public int PartsCategoryDefinition_ID { get; set; }
        public int PartsCategory_ID { get; set; }
        public Nullable<decimal> ConversionFactor { get; set; }
        public Nullable<decimal> ProfitValue { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public bool IsActive { get; set; }
        public Nullable<decimal> MRPFactor { get; set; }

        public virtual PRM_CorePartsCategory PRM_PartsCategory { get; set; }
    }
    public partial class PRM_CorePartsCategoryLocale
    {
        public int PartsCategoryLocale_ID { get; set; }
        public int PartsCategory_ID { get; set; }
        public int Language_ID { get; set; }
        public string Description { get; set; }

        public virtual PRM_CorePartsCategory PRM_PartsCategory { get; set; }
    }
    public partial class PRM_CorePartsCategory
    {
        public PRM_CorePartsCategory()
        {
            this.PRM_PartsCategoryDefinition = new HashSet<PRM_CorePartsCategoryDefinition>();
            this.PRM_PartsCategoryLocale = new HashSet<PRM_CorePartsCategoryLocale>();
        }

        public int PartsCategory_ID { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public string Code { get; set; }

        public virtual ICollection<PRM_CorePartsCategoryDefinition> PRM_PartsCategoryDefinition { get; set; }
        public virtual ICollection<PRM_CorePartsCategoryLocale> PRM_PartsCategoryLocale { get; set; }
    }
    public class Attachements
    {
        public int ATTACHMENTDETAIL_ID
        {
            get;
            set;
        }
        public string AttachmentIDS { get; set; }
        public int TransactionID { get; set; }
        public string FILE_NAME { get; set; }
        public string FILEDESCRIPTION { get; set; }
        public string UPLOADBY { get; set; }
        public DateTime UPLOADDATE { get; set; }
        public string UPLOADDATESORT { get; set; }
        public string delete { get; set; }
        public int Upload { get; set; }
        public string view { get; set; }
        public int OBJECTID { get; set; }
        public string DocumentType { get; set; }
        public int DocumentType_ID { get; set; }
        public int? DetailID { get; set; }
        public int ID { get; set; }
        public string edit { get; set; }
        public string Tablename { get; set; }

        public int PartID { get; set; }
        public string TransactionType { get; set; }
        public string TransactionNumber { get; set; }
        public string Date { get; set; }
        public string Amount { get; set; }
        public int ModelID { get; set; }
        public string Type { get; set; }
        public string Remarks { get; set; }
    }

    public class DeletePartList
    {

        public int Company_ID { get; set; }
        public int MenuID { get; set; }
        public string Lang { get; set; }
        public string key { get; set; }
        public int Branch { get; set; }
        public DateTime LoggedINDateTime { get; set; }

        public List<GNM_User> UserDetails { get; set; }


    }
    public class SelectPartsPriceList
    {
        public string UserCulture { set; get; }
        public int id { set; get; }

        public List<GNM_User> UserDetails { get; set; }
    }
    public class PartsPrice
    {
        public int ID { get; set; }
        public string ListPrice { get; set; }
        public string FormulaCostPrice { get; set; }
        public string MRPPrice { get; set; }
        public string EffectiveFrom { get; set; }
        public DateTime EffectiveFromDate { get; set; }
        public string BuyingCurrency { get; set; }
        public string CustomerWarranty { get; set; }
    }

    public class LoadPartsDropdownList
    {
        public int Comp_ID { get; set; }
        public int Company_ID { get; set; }
        public List<GNM_User> UserDetails { get; set; }
    }

    public class SelectCompetitorPartsPriceList
    {
        public int id { set; get; }
    }
    public class DeleteCompetitorPriceDetailList
    {
        public string GeneralCulture { set; get; }
        public string key { set; get; }
    }
    public class InsertCompetitorPriceList
    {
        public int Company_ID { set; get; }
        public int User_ID { set; get; }
        public string key { set; get; }
    }
    public class GetProductIDList
    {
        public string SerialNumber { set; get; }
    }
    public class GetObjectIDList
    {
        public string name { set; get; }
    }

    public class SelectPartStockSerialNumberDetailsList
    {
        public int PartsStockDetails_ID { set; get; }
    }
    public class SelectPartStockSerialNumberDetailsCountList
    {
        public int PartsStockDetails_ID { set; get; }
    }
    public class SelectFieldSearchSalvagePartsList
    {

        public int Company_ID { get; set; }
        public string value { get; set; }
        public string UserCulture { get; set; }
        public int Branch { get; set; }

        public List<GNM_User> UserDetails { get; set; }


    }
    public class SelAllPendingPurchaseInvoiceList
    {

        public int partsID { get; set; }
        public int warehouseID { get; set; }
        public int Branch { get; set; }
        public int Company_ID { set; get; }
        public int User_ID { set; get; }

        public List<GNM_User> UserDetails { get; set; }


    }
    public class PendingPI
    {
        public int PurchaseInvoice_ID { get; set; }
        public string PurchaseInvoiceNumber { get; set; }
        public decimal Quantity { get; set; }
        public int IsPI { get; set; }
        public string Type { get; set; }
    }

    public class SelAllPendingPurchaseOrdersList
    {

        public int PartsID { get; set; }
        public int warehouseID { get; set; }
        public int Branch { get; set; }
        public int Company_ID { set; get; }
        public int User_ID { set; get; }
        public List<GNM_User> UserDetails { get; set; }


    }

    public class PendingPO
    {
        public int PurchaseOrder_ID { get; set; }
        public string PurchaseOrderNumber { get; set; }
        public decimal Quantity { get; set; }
        public int POStatus_ID { get; set; }
        public int IsPO { get; set; }
        public string Type { get; set; }
    }

    public class SelectSalesNonSalesTransactionList
    {

        public int PartsID { get; set; }
        public int Year { get; set; }
        public int Month { get; set; }
        public DateTime Date { get; set; }
        public int Company_ID { set; get; }
        public int User_ID { set; get; }
        public List<GNM_User> UserDetails { get; set; }


    }
    public class PartsConsumptionHistoryTransactionWise
    {
        public int PartsID { get; set; }
        public string InvoiceNumber { get; set; }
        public int SIID { get; set; }
        public string DNNumber { get; set; }
        public int DNID { get; set; }
        public decimal SalesCount { get; set; }
        public int Year { get; set; }
        public int Month { get; set; }
        public int Date { get; set; }
        public decimal NonSalesCount { get; set; }
    }

    public class SelectSalesNonSalesDayList
    {

        public int PartsID { get; set; }
        public int Year { get; set; }
        public int Month { get; set; }


    }
    public class PartsConsumptionHistoryDay
    {
        public int PartsID { get; set; }
        public decimal SalesCount { get; set; }
        public int Year { get; set; }
        public int Month { get; set; }
        public DateTime Date { get; set; }
        public string DateS { get; set; }
        public decimal NonSalesCount { get; set; }
    }

    public class SelectSalesNonSalesMonthList
    {

        public int PartsID { get; set; }
        public int Year { get; set; }
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public string UserCulture { get; set; }
        public List<GNM_User> UserDetails { get; set; }

    }
    public class PartsConsumptionHistoryMonth
    {
        public int PartsID { get; set; }
        public decimal SalesCount { get; set; }
        public int Year { get; set; }
        public int Month { get; set; }
        public decimal NonSalesCount { get; set; }
    }



    public class SelectSalesNonSalesYearReportList
    {

        public int PartsID { get; set; }
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public List<GNM_User> UserDetails { get; set; }

    }

    public class SalesNonSalesYearReportCountList
    {
        public int PartsID { get; set; }
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public List<GNM_User> UserDetails { get; set; }

    }
    public class PartsConsumptionHistoryYear
    {
        public int PartsID { get; set; }
        public decimal SalesCount { get; set; }
        public int Year { get; set; }
        public decimal NonSalesCount { get; set; }
    }

    public class ValidateBranchWareHouseDuplicateList
    {
        public string StockID { get; set; }
        public int brdID { get; set; }
        public int PartID { get; set; }
        public int WarhuID { get; set; }

        public List<GNM_User> UserDetails { get; set; }

    }
    public class InsertPartsSockDetailsList
    {
        public string data { get; set; }
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int User_ID { get; set; }
        public int MenuID { get; set; }

        public List<GNM_User> UserDetails { get; set; }

    }
    public class SelectBinlocationByWareHouseList
    {
        public int BranchID { get; set; }
        public int WareHouseID { get; set; }
        public int wareMode { get; set; }
        public int Company_ID { get; set; }

        public List<GNM_User> UserDetails { get; set; }

    }
    public class SelectWareHouseByBranchList
    {
        public int BranchID { get; set; }
        public int id { get; set; }
        public int wareMode { get; set; }
        public int Company_ID { get; set; }

        public List<GNM_User> UserDetails { get; set; }

    }
    public class SelectPartStockDropDownsList
    {
        public int Branch { get; set; }
        public int Company_ID { get; set; }
        public int Employee_ID { get; set; }

        public List<GNM_User> UserDetails { get; set; }

    }
    public class validateModelList
    {
        public int Ptype { get; set; }
        public string ModelVal { get; set; }

        public List<GNM_User> UserDetails { get; set; }

    }
    public class ValidateSupplierNameList
    {

        public int ID { get; set; }
        public int primID { get; set; }
        public int Company_ID { get; set; }
        public int UserLanguageID { get; set; }
        public string Key { get; set; }
        public string mode { get; set; }



    }
    public class ValidateFunctionGroupList
    {

        public string FunGrp { get; set; }
        public int Company_ID { get; set; }

        public List<GNM_User> UserDetails { get; set; }

    }

    public class SelectProductTypeList
    {
        public int id { get; set; }

    }
    public class MakeLastRowEditableList
    {
        public int PartID { get; set; }

    }

    public class PartProdExitsList
    {
        public int Company_ID { get; set; }
        public string key { get; set; }

    }
    public class getPreviousDateList
    {
        public int PartID { get; set; }
        public string Currenteditmode { get; set; }

    }

    public class ValidatePartNumberList
    {
        public string key { get; set; }
        public string PartNumber { get; set; }
        public string Prefix { get; set; }
        public int Company_ID { get; set; }


    }
    public class DeletePartSupplierDetailsList
    {
        public string key { get; set; }
        public string Lang { get; set; }

    }
    public class InsertSupplierDetailsList
    {
        public string key { get; set; }
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public int User_ID { get; set; }
        public int MenuID { get; set; }

    }
    public class DeletePartFreeStockList
    {
        public string key { get; set; }
        public string Lang { get; set; }

    }
    public class ValidateBinLocationList
    {
        public int PartID { get; set; }
        public int Branch { get; set; }
        public int BinLocation { get; set; }
        public int primKey { get; set; }


    }
    public class LoadBinLocationArrayList
    {
        public int Company_ID { get; set; }
        public int BranchID { get; set; }


    }
    public class PartsStock
    {
        public int ID { get; set; }
        public string Branch { get; set; }
        public string BinLocation { get; set; }
        public string WareHouse { get; set; }
        public string FreeStock { get; set; }
        public string BinStock { get; set; }
        public string TotalStock { get; set; }
        public string DamagedQuantity { get; set; }
        public int Branch_ID { get; set; }
        public int? WareHouse_ID { get; set; }
        public string FirstDemandDateStr { get; set; }
        public string LastDemandDateStr { get; set; }
        public string FirstIssuedDateStr { get; set; }
        public string LastIssuedDateStr { get; set; }
        public string LastStockUpdatedDateStr { get; set; }
        public string LastStockCheckDateStr { get; set; }
    }
    public class SelectPartsFreeStockList
    {
        public List<GNM_User> userDetails { get; set; }
        public string UserCulture { get; set; }
        public int Branch { get; set; }
        public int id { get; set; }
        public int Company_ID { get; set; }

    }

    public class Prt_DamagedQuantity
    {
        public int Parts_ID { get; set; }
        public int Branch_ID { get; set; }
        public int WareHouse_ID { get; set; }
        public decimal? DamagedQuantity { get; set; }
    }

    public class DeletePartProductDetailList
    {
        public string key { get; set; }
        public string UserCulture { get; set; }

    }


    public class InsertPartsProductList
    {
        // public List<GNM_User> userDetails {  get; set; }    

        public int Company_ID { get; set; }
        public string key { get; set; }

    }
    public class GetPartsProductList
    {
        public List<GNM_User> userDetails { get; set; }

        public int Company_ID { get; set; }

    }

    public class SelectPartsProductList
    {
        public int Company_ID { get; set; }
        public int id { get; set; }
        public string UserCulture { get; set; }

    }
    public class InsertPartsPriceList
    {
        public int Company_ID { get; set; }
        public string key { get; set; }

    }
    public class ProductType
    {
        public int ProductType_ID { get; set; }
        public int ProductTypeLocale_ID { get; set; }
        public int Brand_ID { get; set; }
        public string ProductType_Name { get; set; }
        public string ProductTypeLocale_Name { get; set; }
        public bool ProductType_IsActive { get; set; }
    }
    public class PartsProduct
    {
        public int ID { get; set; }
        public string Brand { get; set; }
        public string ProductType { get; set; }
        public string Model { get; set; }
        public int Model_ID { get; set; }
        public int BrandID { get; set; }
        public int ProductTypeID { get; set; }
        public string FromSerialNumber { get; set; }
        public string ToSerialNumber { get; set; }
    }
    public partial class GNM_PARTSATTACHMENTDETAIL
    {
        public int PARTSATTACHMENTDETAIL_ID { get; set; }
        public int PARTS_ID { get; set; }
        public string FILENAME { get; set; }
        public string FILEDESCRIPTION { get; set; }
        public int FILETYPE { get; set; }

        public virtual GNM_Parts GNM_Parts { get; set; }
    }
    public partial class GNM_PartsLocale
    {
        public int Parts_Locale_ID { get; set; }
        public int Parts_ID { get; set; }
        public string Parts_PartsDescription { get; set; }
        public int Language_ID { get; set; }

        public virtual GNM_Parts GNM_Parts { get; set; }
    }
    public partial class GNM_PartsPriceDetail
    {
        public int PartsPriceDetail_ID { get; set; }
        public int Parts_ID { get; set; }
        public decimal PartsPriceDetail_ListPrice { get; set; }
        public Nullable<decimal> PartsPriceDetail_FormulaCostPrice { get; set; }
        public decimal PartsPriceDetail_MRP { get; set; }
        public System.DateTime PartsPriceDetail_EffectiveFrom { get; set; }
        public int Company_ID { get; set; }
        public Nullable<int> CustomerWarranty { get; set; }
        public Nullable<int> Currency_ID { get; set; }

        public virtual GNM_Parts GNM_Parts { get; set; }
    }
    public partial class GNM_PartsSupplierDetail
    {
        public int PartsSupplierDetail_ID { get; set; }
        public int Parts_ID { get; set; }
        public int Supplier_ID { get; set; }
        public Nullable<decimal> StandardPackingQuantity { get; set; }
        public Nullable<decimal> SupplierPrice { get; set; }
        public System.DateTime Effectivefrom { get; set; }
        public Nullable<decimal> CostPrice { get; set; }
        public int Currency_ID { get; set; }
        public Nullable<System.DateTime> LastInvoicedDate { get; set; }
        public string SupplierPartNumber { get; set; }
        public int Company_ID { get; set; }
        public string SupplierPartPrefix { get; set; }
        public Nullable<int> ManufacturerWarranty { get; set; }
        public Nullable<bool> IsWarrantyIntimation { get; set; }
        public Nullable<decimal> LatestPurchaseCost { get; set; }
        public Nullable<System.DateTime> FirstGRNDate { get; set; }
        public Nullable<System.DateTime> LastGRNDate { get; set; }
        public Nullable<decimal> SupplierYTAPrice { get; set; }
        public Nullable<decimal> SupplierYEAPrice { get; set; }

        public virtual GNM_Parts GNM_Parts { get; set; }
    }
    public partial class REM_CORETYPE
    {
        public int CORETYPE_ID { get; set; }
        public int COMPANY_ID { get; set; }
        public int COREPARTS_ID { get; set; }
        public int ORIGINALPARTS_ID { get; set; }
        public int REMANPARTS_ID { get; set; }
        public string REMARKS { get; set; }

        public virtual GNM_Parts GNM_Parts { get; set; }
        public virtual GNM_Parts GNM_Parts1 { get; set; }
        public virtual GNM_Parts GNM_Parts2 { get; set; }
    }
    public partial class GNM_PartsStockDetail
    {
        public int PartsStockDetail_ID { get; set; }
        public int Parts_ID { get; set; }
        public int Branch_ID { get; set; }
        public int Company_ID { get; set; }
        public Nullable<int> WareHouse_ID { get; set; }
        public Nullable<decimal> FreeStock { get; set; }
        public Nullable<decimal> AllocatedQuantity { get; set; }
        public Nullable<decimal> PickedQuantity { get; set; }
        public Nullable<decimal> ReservedQuantity { get; set; }
        public Nullable<decimal> BackOrderQuantity { get; set; }
        public Nullable<decimal> PendingPurchaseOrderQuantity { get; set; }
        public Nullable<decimal> PendingPartsOrderQuantity { get; set; }
        public Nullable<decimal> DeviationStock { get; set; }
        public Nullable<decimal> StockUsedInKits { get; set; }
        public int ReOrderLevel { get; set; }
        public decimal ReOrderLevelQuantity { get; set; }
        public decimal MinOrderQty { get; set; }
        public Nullable<decimal> GITQuantity { get; set; }
        public Nullable<decimal> BinStock { get; set; }
        public Nullable<decimal> TotalStock { get; set; }
        public Nullable<decimal> WeightedAverageCost { get; set; }
        public int BinLocation_ID { get; set; }
        public Nullable<int> BinlocationBuffer_ID { get; set; }
        public Nullable<int> OldBinLocation_ID { get; set; }
        public Nullable<int> OldBufferBinLocation_ID { get; set; }
        public bool IsBlocked { get; set; }
        public Nullable<System.DateTime> LastStockUpdatedDate { get; set; }
        public Nullable<int> Movement_ID { get; set; }
        public Nullable<System.DateTime> FirstDemandDate { get; set; }
        public Nullable<System.DateTime> LastDemandDate { get; set; }
        public Nullable<System.DateTime> FirstIssuedDate { get; set; }
        public Nullable<System.DateTime> LastIssuedDate { get; set; }
        public Nullable<System.DateTime> LastStockCheckDate { get; set; }
        public Nullable<decimal> MaximumStockLevel { get; set; }

        public virtual GNM_Parts GNM_Parts { get; set; }
    }
    public partial class GNM_CompetitorPriceDetail
    {
        public int CompetitorPriceDetail_ID { get; set; }
        public Nullable<int> Parts_ID { get; set; }
        public string CompetitorName { get; set; }
        public Nullable<decimal> MRP { get; set; }
        public Nullable<decimal> MarketRulingPrice { get; set; }
        public Nullable<System.DateTime> Competitor_EffectiveFrom { get; set; }
        public string Remarks { get; set; }
        public int Company_ID { get; set; }
        public Nullable<int> ModifiedBy { get; set; }
        public Nullable<System.DateTime> ModifiedDate { get; set; }

        public virtual GNM_Parts GNM_Parts { get; set; }
    }
    public partial class GNM_Parts
    {
        public GNM_Parts()
        {
            this.GNM_PARTSATTACHMENTDETAIL = new HashSet<GNM_PARTSATTACHMENTDETAIL>();
            this.GNM_PartsLocale = new HashSet<GNM_PartsLocale>();
            this.GNM_PartsPriceDetail = new HashSet<GNM_PartsPriceDetail>();
            this.GNM_PartsProductAssociation = new HashSet<GNM_PartsProductAssociation>();
            this.GNM_PartsSupplierDetail = new HashSet<GNM_PartsSupplierDetail>();
            this.REM_CORETYPE = new HashSet<REM_CORETYPE>();
            this.REM_CORETYPE1 = new HashSet<REM_CORETYPE>();
            this.REM_CORETYPE2 = new HashSet<REM_CORETYPE>();
            this.GNM_PartsStockDetail = new HashSet<GNM_PartsStockDetail>();
            this.GNM_CompetitorPriceDetail = new HashSet<GNM_CompetitorPriceDetail>();
        }

        public int Parts_ID { get; set; }
        public string Parts_Name { get; set; }
        public string Parts_PartPrefix { get; set; }
        public string Parts_PartsNumber { get; set; }
        public string Parts_PartsDescription { get; set; }
        public string Parts_AliasPartPrefix { get; set; }
        public string Parts_AliasPartNumber { get; set; }
        public Nullable<int> SuperceededPart_ID { get; set; }
        public Nullable<int> SuperceedingPart_ID { get; set; }
        public int MovementType_ID { get; set; }
        public Nullable<decimal> Parts_Weight { get; set; }
        public string Parts_Dimensions { get; set; }
        public Nullable<int> PartsCategory_ID { get; set; }
        public Nullable<int> PartsFunctionGroup_ID { get; set; }
        public Nullable<int> PartsCustomsCode_ID { get; set; }
        public int UnitOfMeasurement_ID { get; set; }
        public Nullable<int> SupersessionType_ID { get; set; }
        public bool Parts_IsActive { get; set; }
        public bool Parts_IsHazardousGood { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public int Company_ID { get; set; }
        public bool Parts_IsLocal { get; set; }
        public bool Parts_IsComponent { get; set; }
        public bool IsKitPart { get; set; }
        public Nullable<int> ExciseDuty_ID { get; set; }
        public Nullable<int> SalvagePart_ID { get; set; }
        public Nullable<int> PartType { get; set; }
        public Nullable<byte> AttachmentCount { get; set; }
        public Nullable<int> PartsDisposal_ID { get; set; }
        public Nullable<System.DateTime> IntroductionDate { get; set; }
        public Nullable<int> LatestSupersession_ID { get; set; }
        public Nullable<int> CustomerWarrantyInDays { get; set; }
        public Nullable<int> ReadingLimit { get; set; }

        public virtual ICollection<GNM_PARTSATTACHMENTDETAIL> GNM_PARTSATTACHMENTDETAIL { get; set; }
        public virtual ICollection<GNM_PartsLocale> GNM_PartsLocale { get; set; }
        public virtual ICollection<GNM_PartsPriceDetail> GNM_PartsPriceDetail { get; set; }
        public virtual ICollection<GNM_PartsProductAssociation> GNM_PartsProductAssociation { get; set; }
        public virtual ICollection<GNM_PartsSupplierDetail> GNM_PartsSupplierDetail { get; set; }
        public virtual ICollection<REM_CORETYPE> REM_CORETYPE { get; set; }
        public virtual ICollection<REM_CORETYPE> REM_CORETYPE1 { get; set; }
        public virtual ICollection<REM_CORETYPE> REM_CORETYPE2 { get; set; }
        public virtual ICollection<GNM_PartsStockDetail> GNM_PartsStockDetail { get; set; }
        public virtual ICollection<GNM_CompetitorPriceDetail> GNM_CompetitorPriceDetail { get; set; }
    }


    public partial class GNM_PartsProductAssociation
    {
        public int PartsProductAssociation_ID { get; set; }
        public int Parts_ID { get; set; }
        public int Brand_ID { get; set; }
        public Nullable<int> ProductType_ID { get; set; }
        public Nullable<int> Model_ID { get; set; }
        public int Company_ID { get; set; }
        public string FromSerialNumber { get; set; }
        public string ToSerialNumber { get; set; }

        public virtual GNM_Parts GNM_Parts { get; set; }
    }

    public partial class GNM_RefMasterDetail
    {
        public GNM_RefMasterDetail()
        {
            this.GNM_RefMasterDetailLocale = new HashSet<GNM_RefMasterDetailLocale>();
        }

        public int RefMasterDetail_ID { get; set; }
        public bool RefMasterDetail_IsActive { get; set; }
        public string RefMasterDetail_Short_Name { get; set; }
        public bool IsCompanySpecific { get; set; }
        public string RefMasterDetail_Name { get; set; }
        public Nullable<int> Company_ID { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public int RefMaster_ID { get; set; }
        public bool RefMasterDetail_IsDefault { get; set; }
        public Nullable<int> Region_ID { get; set; }
        public string SystemCondition { get; set; }

        public virtual GNM_RefMaster GNM_RefMaster { get; set; }
        public virtual ICollection<GNM_RefMasterDetailLocale> GNM_RefMasterDetailLocale { get; set; }
    }
    public partial class GNM_BinLocationLocale
    {
        public int BinLocationLocale_ID { get; set; }
        public int BinLocation_ID { get; set; }
        public int Language_ID { get; set; }
        public string BinLocation_Name { get; set; }

        public virtual GNM_BinLocation GNM_BinLocation { get; set; }
    }
    public partial class GNM_BinLocation
    {
        public GNM_BinLocation()
        {
            this.GNM_BinLocationLocale = new HashSet<GNM_BinLocationLocale>();
        }

        public int BinLocation_ID { get; set; }
        public int WareHouse_ID { get; set; }
        public string BinLocation_Name { get; set; }
        public bool BinLocation_IsActive { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public bool BinLocation_IsDefault { get; set; }

        public virtual ICollection<GNM_BinLocationLocale> GNM_BinLocationLocale { get; set; }
    }
    public partial class GNM_Party
    {
        public GNM_Party()
        {
            this.GNM_PartyBranchAssociation = new HashSet<GNM_PartyBranchAssociation>();
            this.GNM_PartyProductAssociation = new HashSet<GNM_PartyProductAssociation>();
            this.GNM_PartySkillset = new HashSet<GNM_PartySkillset>();
            this.GNM_ServiceSchedule = new HashSet<GNM_ServiceSchedule>();
            this.GNM_PartyLocale = new HashSet<GNM_PartyLocale>();
            this.GNM_PartySegmentDetails = new HashSet<GNM_PartySegmentDetails>();
            this.GNM_PartyTaxDetails = new HashSet<GNM_PartyTaxDetails>();
            this.GNM_PartyContactPersonDetails = new HashSet<GNM_PartyContactPersonDetails>();
            this.GNM_PartyAddress = new HashSet<GNM_PartyAddress>();
            this.GNM_PARTYCONTRACTDETAILS = new HashSet<GNM_PARTYCONTRACTDETAILS>();
            this.GNM_PartyTaxStructure = new HashSet<GNM_PartyTaxStructure>();
            this.GNM_PartyDiscount = new HashSet<GNM_PartyDiscount>();
            this.GNM_PartyPartsRatecontract = new HashSet<GNM_PartyPartsRatecontract>();
            this.GNM_PartyCreditLimitLog = new HashSet<GNM_PartyCreditLimitLog>();
            this.GNM_PartyAmount = new HashSet<GNM_PartyAmount>();
            this.GNM_CreditDetails = new HashSet<GNM_CreditDetails>();
        }

        public int Party_ID { get; set; }
        public bool Party_IsActive { get; set; }
        public bool Party_IsLocked { get; set; }
        public string Party_Name { get; set; }
        public string Party_Location { get; set; }
        public string Party_Email { get; set; }
        public string Party_Phone { get; set; }
        public string Party_Fax { get; set; }
        public string Party_PaymentTerms { get; set; }
        public byte PartyType { get; set; }
        public string Party_Mobile { get; set; }
        public int ModifiedBY { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public Nullable<int> Country_ID { get; set; }
        public Nullable<int> State_ID { get; set; }
        public Nullable<int> Company_ID { get; set; }
        public Nullable<bool> IsOEM { get; set; }
        public Nullable<bool> IsDealer { get; set; }
        public Nullable<int> Relationship_Branch_ID { get; set; }
        public Nullable<int> Relationship_Company_ID { get; set; }
        public Nullable<decimal> PartsCreditLimit { get; set; }
        public Nullable<decimal> SalesCreditLimit { get; set; }
        public Nullable<int> Currency_ID { get; set; }
        public bool IsImportExport { get; set; }
        public Nullable<decimal> ServiceCreditLimit { get; set; }
        public Nullable<int> PaymentDueDays { get; set; }
        public Nullable<decimal> PartsOutStandingCredit { get; set; }
        public Nullable<decimal> ServiceOutStandingCredit { get; set; }
        public Nullable<decimal> PartyAdvanceAmount { get; set; }
        public Nullable<decimal> RemanOutStandingCredit { get; set; }
        public Nullable<decimal> SalesOutStandingCredit { get; set; }
        public Nullable<int> CustomerType_ID { get; set; }
        public Nullable<bool> IsKeyCustomer { get; set; }
        public Nullable<int> Region_ID { get; set; }
        public string Party_Code { get; set; }
        public Nullable<bool> SupplierHasInterface { get; set; }
        public Nullable<byte> CustomerType { get; set; }
        public Nullable<int> CustomerLanguageID { get; set; }
        public Nullable<decimal> ServiceCreditLimitinUSD { get; set; }
        public Nullable<decimal> ServiceOutStandingCreditinUSD { get; set; }
        public Nullable<bool> IsPONumberMandatory { get; set; }
        public Nullable<decimal> CAD_ExchangeRate { get; set; }
        public Nullable<decimal> US_ExchangeRate { get; set; }
        public Nullable<bool> CreditExceededMailSent { get; set; }
        public Nullable<bool> IsInternalCustomer { get; set; }
        public Nullable<decimal> Variance_Percentage { get; set; }
        public Nullable<decimal> Variance_Value { get; set; }

        public virtual ICollection<GNM_PartyBranchAssociation> GNM_PartyBranchAssociation { get; set; }
        public virtual ICollection<GNM_PartyProductAssociation> GNM_PartyProductAssociation { get; set; }
        public virtual ICollection<GNM_PartySkillset> GNM_PartySkillset { get; set; }
        public virtual ICollection<GNM_ServiceSchedule> GNM_ServiceSchedule { get; set; }
        public virtual ICollection<GNM_PartyLocale> GNM_PartyLocale { get; set; }
        public virtual ICollection<GNM_PartySegmentDetails> GNM_PartySegmentDetails { get; set; }
        public virtual ICollection<GNM_PartyTaxDetails> GNM_PartyTaxDetails { get; set; }
        public virtual ICollection<GNM_PartyContactPersonDetails> GNM_PartyContactPersonDetails { get; set; }
        public virtual ICollection<GNM_PartyAddress> GNM_PartyAddress { get; set; }
        public virtual ICollection<GNM_PARTYCONTRACTDETAILS> GNM_PARTYCONTRACTDETAILS { get; set; }
        public virtual ICollection<GNM_PartyTaxStructure> GNM_PartyTaxStructure { get; set; }
        public virtual ICollection<GNM_PartyDiscount> GNM_PartyDiscount { get; set; }
        public virtual ICollection<GNM_PartyPartsRatecontract> GNM_PartyPartsRatecontract { get; set; }
        public virtual ICollection<GNM_PartyCreditLimitLog> GNM_PartyCreditLimitLog { get; set; }
        public virtual ICollection<GNM_PartyAmount> GNM_PartyAmount { get; set; }
        public virtual ICollection<GNM_CreditDetails> GNM_CreditDetails { get; set; }

    }
    public partial class GNM_CreditDetails
    {
        public int CreditDetails_ID { get; set; }
        public Nullable<int> Currency_ID { get; set; }
        public Nullable<int> Party_ID { get; set; }
        public Nullable<decimal> Parts_Credit_Limit { get; set; }
        public Nullable<decimal> Service_Credit_Limit { get; set; }
        public Nullable<decimal> Sales_Credit_Limit { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }
    public partial class GNM_PartyAmount
    {
        public int PartyAmount_ID { get; set; }
        public int Party_ID { get; set; }
        public int Company_ID { get; set; }
        public Nullable<decimal> PartsOutStandingCredit { get; set; }
        public Nullable<decimal> ServiceOutStandingCredit { get; set; }
        public Nullable<decimal> RemanOutStandingCredit { get; set; }
        public Nullable<decimal> SalesOutStandingCredit { get; set; }
        public Nullable<decimal> PartyAdvanceAmount { get; set; }
        public Nullable<int> Currency_ID { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }
    public partial class GNM_PartyCreditLimitLog
    {
        public int PartyCreditLimitLog_ID { get; set; }
        public System.DateTime UpdatedDateTime { get; set; }
        public int Currency_ID { get; set; }
        public decimal CreditLimit { get; set; }
        public string Remarks { get; set; }
        public Nullable<int> Party_ID { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }
    public partial class GNM_PartyPartsRatecontract
    {
        public int PartyPartsRateContract_ID { get; set; }
        public int Party_ID { get; set; }
        public int Part_ID { get; set; }
        public decimal Rate { get; set; }
        public Nullable<decimal> Quantity { get; set; }
        public System.DateTime Effective_FromDate { get; set; }
        public Nullable<System.DateTime> Effective_ToDate { get; set; }
        public Nullable<int> Currency_ID { get; set; }
        public Nullable<int> UploadedBy { get; set; }
        public Nullable<System.DateTime> UploadedDate { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }
    public partial class GNM_PartyDiscount
    {
        public int PartyDiscount_ID { get; set; }
        public int Party_ID { get; set; }
        public Nullable<decimal> Parts_Discount { get; set; }
        public Nullable<decimal> Service_Discount { get; set; }
        public Nullable<System.DateTime> Effective_Date { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }
    public partial class GNM_PartyTaxStructure
    {
        public int PartyTaxStructure_ID { get; set; }
        public int Party_ID { get; set; }
        public int TaxStructure_ID { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }
    public partial class GNM_PARTYCONTRACTDETAILS
    {
        public int PartyContract_ID { get; set; }
        public int Party_ID { get; set; }
        public string AgreementNumber { get; set; }
        public System.DateTime FromDate { get; set; }
        public System.DateTime ToDate { get; set; }
        public decimal ContractValue { get; set; }
        public string Unit { get; set; }
        public int Currency { get; set; }
        public string Remarks { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }
    public partial class GNM_PartyAddressLocale
    {
        public int PartyAddressLocale_ID { get; set; }
        public string PartyAddressLocale_Location { get; set; }
        public string PartyAddressLocale_Address { get; set; }
        public int Language_ID { get; set; }
        public int PartyAddress_ID { get; set; }

        public virtual GNM_PartyAddress GNM_PartyAddress { get; set; }
    }
    public partial class GNM_PartyAddress
    {
        public GNM_PartyAddress()
        {
            this.GNM_PartyAddressLocale = new HashSet<GNM_PartyAddressLocale>();
        }

        public int PartyAddress_ID { get; set; }
        public int Party_ID { get; set; }
        public string PartyAddress_Location { get; set; }
        public string PartyAddress_Address { get; set; }
        public int PartyAddress_LeadTimeInDays { get; set; }
        public Nullable<int> PartyAddress_CountryID { get; set; }
        public Nullable<int> PartyAddress_StateID { get; set; }
        public bool PartyAddress_Active { get; set; }
        public string PartyAddress_ZIP { get; set; }
        public bool IsDefault { get; set; }
        public Nullable<int> EquivalentConsignee_ID { get; set; }
        public Nullable<int> Region_ID { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
        public virtual ICollection<GNM_PartyAddressLocale> GNM_PartyAddressLocale { get; set; }
    }
    public partial class GNM_PartyContactLocale
    {
        public int PartyContactLocale_ID { get; set; }
        public int PartyContactPerson_ID { get; set; }
        public string PartyContact_Name { get; set; }
        public int Language_ID { get; set; }
        public string PartyContact_Department { get; set; }
        public string PartyContact_Remarks { get; set; }
        public Nullable<int> Party_ID { get; set; }

        public virtual GNM_PartyContactPersonDetails GNM_PartyContactPersonDetails { get; set; }
    }
    public partial class GNM_PartyContactPersonDetails
    {
        public GNM_PartyContactPersonDetails()
        {
            this.GNM_PartyContactLocale = new HashSet<GNM_PartyContactLocale>();
        }

        public int PartyContactPerson_ID { get; set; }
        public Nullable<int> Party_ID { get; set; }
        public string PartyContactPerson_Name { get; set; }
        public string PartyContactPerson_Email { get; set; }
        public string PartyContactPerson_Department { get; set; }
        public string PartyContactPerson_Mobile { get; set; }
        public string PartyContactPerson_Phone { get; set; }
        public bool Party_IsDefaultContact { get; set; }
        public bool PartyContactPerson_IsActive { get; set; }
        public string PartyContactPerson_Remarks { get; set; }
        public int Language_ID { get; set; }
        public Nullable<System.DateTime> PartyContactPerson_DOB { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
        public virtual ICollection<GNM_PartyContactLocale> GNM_PartyContactLocale { get; set; }
    }
    public partial class GNM_PartyTaxDetails
    {
        public int PartyTax_ID { get; set; }
        public int Party_ID { get; set; }
        public string PartyTax_TaxCode { get; set; }
        public string PartyTax_TaxCodeDescription { get; set; }
        public bool PartyTaxDetails_IsActive { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }
    public partial class GNM_PartySegmentDetails
    {
        public int PartySegment_ID { get; set; }
        public int Party_ID { get; set; }
        public int PrimarySegment_ID { get; set; }
        public Nullable<int> SecondarySegment_ID { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }
    public partial class GNM_PartyLocale
    {
        public int Party_Locale_ID { get; set; }
        public int Party_ID { get; set; }
        public string Party_Name { get; set; }
        public string Party_Location { get; set; }
        public string Party_PaymentTerms { get; set; }
        public int Language_ID { get; set; }
        public string Party_Address { get; set; }
        public string Party_Code { get; set; }
        public int Company_ID { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }
    public partial class GNM_ServiceSchedule
    {
        public int PartyServiceSchedule_ID { get; set; }
        public int Party_ID { get; set; }
        public int ServiceType_ID { get; set; }
        public System.DateTime ServiceDate { get; set; }
        public Nullable<byte> Status { get; set; }
        public string Closure_reason { get; set; }
        public Nullable<int> JobCard_ID { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }
    public partial class GNM_PartySkillset
    {
        public int Party_Skillset_ID { get; set; }
        public int Party_ID { get; set; }
        public int Party_Skillset_Rating { get; set; }
        public int Skillset_ID { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }
    public partial class GNM_PartyProductAssociation
    {
        public int PartyProduct_ID { get; set; }
        public int Party_ID { get; set; }
        public int Brand_ID { get; set; }
        public Nullable<int> ProductType_ID { get; set; }
        public Nullable<int> Model_ID { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }
    public partial class GNM_PartyBranchAssociation
    {
        public int PartyBranch_ID { get; set; }
        public int Party_ID { get; set; }
        public int Branch_ID { get; set; }

        public virtual GNM_Party GNM_Party { get; set; }
    }
}