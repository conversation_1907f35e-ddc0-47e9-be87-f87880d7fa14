﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using System;
using System.Collections.Generic;
using System.Linq;
using AMMSCore.Models;

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Org.BouncyCastle.Asn1.Cmp;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Data.SqlTypes;
using System.Drawing.Text;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Cryptography.Pkcs;
using System.Threading.Tasks;
using System.Web;
using System.Xml;
using System.Xml.Linq;
using WorkFlow.Models;

using LS = SharedAPIClassLibrary_AMERP.Utilities;
using System.Configuration;
using static SharedAPIClassLibrary_AMERP.CoreWorkFlowEscalation1Services;
using System.Text;
using System.Transactions;
using DocumentFormat.OpenXml.Office.Word;
using DocumentFormat.OpenXml.Office2019.Drawing.Model3D;
using Microsoft.CodeAnalysis.Operations;
using System.Security.AccessControl;
using static SharedAPIClassLibrary_AMERP.CoreEmailTemplateService;
using DocumentFormat.OpenXml.Office.MetaAttributes;
using static SharedAPIClassLibrary_AMERP.HelpDeskServiceRequestServices;
using static SharedAPIClassLibrary_DC.Utilities.Common;
using System.Net.Http;
using DocumentFormat.OpenXml.Drawing;
using System.Text.RegularExpressions;

namespace SharedAPIClassLibrary_AMERP.Services
{
    public class PRT_PurchaseOrderServices
    {
        static string custEmailCC = string.Empty;
        static string custEmailBCC = string.Empty;

        public static ActionResult PurchaseOrderInterface(PurchaseOrderInterfaceList Obj, string connString, int LogException)
        {
            string SMStoAssigneeText = string.Empty;
            string SMStoCustomerText = string.Empty;
            string CustomerMobNo = string.Empty;
            string CustomerEmailID = string.Empty;
            string EmailSub = string.Empty;

            string AddresseEmailCC = string.Empty;
            string AddresseEmailBCC = string.Empty;

            string custEmailCC = string.Empty;
            string custEmailBCC = string.Empty;

            string EmailtoAssigneeBody = string.Empty;
            string EmailtoCustomerBody = string.Empty;

            var jsonData = default(dynamic);
            bool POPrefix = true;

            string DbName = ConfigurationManager.AppSettings.Get("DbName");






        
       
           
           
          
          
           
            int PurchaseORDERIDDGPS = 0;


            try
            {
                int RoleID = 0;
                int WorkFlowID = WorkFlowCommon.GetWorkFlowID("PurchaseOrder", DbName, connString, LogException);
                int CurrentStepID = 0;
                int NextStepID = 0;
                int ActionID = 0;
                int AssignedTo = 0;
               
                Nullable<int> prodID = null;
                string ActionRemarks = string.Empty;
                string NextStapName = string.Empty;
                byte AddresseType = 1;
               
                int companyID = Obj.Company_ID;
                int userid = Obj.User_ID;
                int branchID = Convert.ToInt32(Obj.Branch);
                GNM_Party Party = new GNM_Party();
                GNM_Branch company = new GNM_Branch();
               
               
               
                CurrentStepID = Convert.ToInt32(Obj.CurrentStepID);
            
               
                ActionID = Convert.ToInt32(Obj.ActionID);
                if (ActionID > 0)
                {
                   
                    AssignedTo = Convert.ToInt32(Obj.AssignedTo);

                   
                    AddresseType = Convert.ToByte(Obj.AddresseType);

                   
                    ActionRemarks = Obj.ClosingDescription;
                }
                int wfCount = 0;
                int approvalStepID = 0;
                int StepStatuID = 0;
                string approvalStepName = "";
                string WFStepName = "";
                int poStatus = 0;
                string poStatusName = "";
                IEnumerable<WF_WFField> FieldList = null;
                List<WF_WFField> WF_FeildList = new List<WF_WFField>();

                JTokenReader jr = null;
                //DK - Code Added to Fetch WF Setp Status
                var NextStepResult = HelpDeskServiceRequestServices.GetNextStepType(companyID, WorkFlowID, CurrentStepID, ActionID, connString);
                WF_WFStepLink stepLinkRow = HelpDeskServiceRequestServices.GetRoleOrIndividualForFirstStep(companyID, WorkFlowID, connString);
                string rawJson = NextStepResult.Value.ToString();
                rawJson = rawJson.Replace("=", ":");
                rawJson = Regex.Replace(rawJson, @"(?<=:\s*)(\w+)(?=\s*,|\s*})", "\"$1\"");
                rawJson = rawJson.Replace("Infinity", "\"Infinity\"").Replace("-Infinity", "\"-Infinity\"").Replace("NaN", "\"NaN\"");
                JObject jObject = JObject.Parse(rawJson);
                jr = new JTokenReader(jObject["NextStepID"]);
                jr.Read();
                NextStepID = Convert.ToInt32(jr.Value);
                jr = new JTokenReader(jObject["NextStepType"]);
                jr.Read();
                NextStapName = jr.Value.ToString();
                jr = new JTokenReader(jObject["RoleID"]);
                jr.Read();
                RoleID = Convert.ToInt32(jr.Value);
                int stepStatus = 0;

                
                PRT_PurchaseOrder headerRow = Obj.PurchaseOrderData.FirstOrDefault();

                decimal ApprovedQty = 0;
                headerRow.PurchaseOrderNumber = Common.DecryptString(headerRow.PurchaseOrderNumber);
                headerRow.PurchaseOrderDate = DateTime.Now;
                headerRow.Company_ID = companyID;
                headerRow.Branch_ID = Convert.ToInt32(Obj.Branch);
                headerRow.SerialNumber = Common.DecryptString(headerRow.SerialNumber);
                headerRow.PaymentTerms = Common.DecryptString(headerRow.PaymentTerms);
                headerRow.Remarks = Common.DecryptString(headerRow.Remarks);
                headerRow.Model_ID = headerRow.Model_ID == 0 ? null : headerRow.Model_ID;
                headerRow.Brand_ID = headerRow.Brand_ID == 0 ? null : headerRow.Brand_ID;
                headerRow.ProductType_ID = headerRow.ProductType_ID == 0 ? null : headerRow.ProductType_ID;
                headerRow.PurchaseOrderClass_ID = headerRow.PurchaseOrderClass_ID == 0 ? null : headerRow.PurchaseOrderClass_ID;//added by Kavitha if Type of Purchase is Supplier Quotation ,OrderClass is not mandatory
                headerRow.POStatus_ID = poStatus;
                headerRow.IsDealer = headerRow.IsDealer;
                headerRow.IncoTerms = Common.DecryptString(headerRow.IncoTerms);
                headerRow.SUPPLIERQUOTATION_ID = headerRow.SUPPLIERQUOTATION_ID == 0 ? null : headerRow.SUPPLIERQUOTATION_ID;
                headerRow.TallySentStatus = false;
                PRM_OrderClass orderClass = new PRM_OrderClass();

                int? leadTime = null;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("UP_PRT_PURCHASEORDER_SAVE", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure; // Add this line!
                        cmd.Parameters.AddWithValue("@WorkFlowID", WorkFlowID);
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);
                        cmd.Parameters.AddWithValue("@CurrentStepID", CurrentStepID);
                        cmd.Parameters.AddWithValue("@ActionID", ActionID);
                        cmd.Parameters.AddWithValue("@NextStepID", NextStepID);
                        cmd.Parameters.AddWithValue("@PurchaseOrderClass_ID", headerRow.PurchaseOrderClass_ID);
                        cmd.Parameters.AddWithValue("@Model_ID", headerRow.Model_ID.HasValue ? (object)headerRow.Model_ID.Value : DBNull.Value);


                        cmd.Parameters.AddWithValue("@Product_SerialNumber",
                            string.IsNullOrEmpty(headerRow.SerialNumber) ? DBNull.Value : (object)headerRow.SerialNumber);

                        cmd.Parameters.AddWithValue("@SupplierID", headerRow.Supplier_ID);


                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }



                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                WF_FeildList.Add(new WF_WFField
                                {
                                    WorkFlowFieldName = reader.GetString(0),  // WorkFlowFieldName
                                    WFField_ID = reader.GetInt32(1)          // WFField_ID
                                });
                            }
                            FieldList = WF_FeildList.AsEnumerable();
                            if (reader.NextResult())
                            {
                                if (reader.Read())  // Read single row
                                {
                                    wfCount = reader.IsDBNull(reader.GetOrdinal("WorkFlowCount")) ? 0 : reader.GetInt32(reader.GetOrdinal("WorkFlowCount"));
                                    approvalStepID = reader.IsDBNull(reader.GetOrdinal("ApprovalStepID")) ? 0 : reader.GetInt32(reader.GetOrdinal("ApprovalStepID"));
                                    approvalStepName = reader.IsDBNull(reader.GetOrdinal("ApprovalStepName")) ? string.Empty : reader.GetString(reader.GetOrdinal("ApprovalStepName"));
                                    StepStatuID = reader.IsDBNull(reader.GetOrdinal("StepStatusID")) ? 0 : reader.GetInt32(reader.GetOrdinal("StepStatusID"));
                                    WFStepName = reader.IsDBNull(reader.GetOrdinal("StepStatusCode")) ? string.Empty : reader.GetString(reader.GetOrdinal("StepStatusCode"));
                                    poStatus = reader.IsDBNull(reader.GetOrdinal("POStatus")) ? 0 : reader.GetInt32(reader.GetOrdinal("POStatus"));
                                    poStatusName = reader.IsDBNull(reader.GetOrdinal("POStatusName")) ? string.Empty : reader.GetString(reader.GetOrdinal("POStatusName"));
                                    stepStatus = reader.IsDBNull(reader.GetOrdinal("WFStepStatus_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("WFStepStatus_ID"));
                                    orderClass.IsInterfaceOrderClass = reader.IsDBNull(reader.GetOrdinal("IsInterfaceOrderClass")) ? false : reader.GetBoolean(reader.GetOrdinal("IsInterfaceOrderClass"));
                                    leadTime = reader.IsDBNull(reader.GetOrdinal("OrderClass_LeadTime")) ? 0 : reader.GetInt32(reader.GetOrdinal("OrderClass_LeadTime"));

                                    if (headerRow.Model_ID != null && !string.IsNullOrEmpty(headerRow.SerialNumber))
                                    {
                                        prodID = reader.IsDBNull(reader.GetOrdinal("Product_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Product_ID"));
                                    }

                                }
                            }

                            headerRow.PurchaseOrderStatus_ID = stepStatus;

                        }
                    }
                }

                if (poStatusName == null)
                {
                    poStatusName = "";
                }





                DateTime expectedDeliverrtyDate = DateTime.Now;
                expectedDeliverrtyDate = expectedDeliverrtyDate.AddDays(Convert.ToDouble((leadTime != null ? leadTime.Value : 0)));
                headerRow.Expected_Delivery_Date = expectedDeliverrtyDate;

                headerRow.Updated_by = userid;
                headerRow.Updated_Date = DateTime.Now;

                if (headerRow.PurchaseOrder_ID == 0)
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }
                        if (poStatus == 0)
                        {
                            using (SqlCommand cmd = new SqlCommand(@"
                            SELECT TOP 1 RefMasterDetail_ID FROM GNM_RefMasterDetail 
                            WHERE RefMaster_ID = (SELECT RefMaster_ID FROM GNM_RefMaster WHERE RefMaster_Name = 'POSTATUS')", conn))
                            {
                                object result = cmd.ExecuteScalar();
                                if (result != null && result != DBNull.Value)
                                    poStatus = Convert.ToInt32(result);
                            }
                            headerRow.POStatus_ID = poStatus;
                        }
                        if (Common.CheckPreffixSuffix(connString, LogException, companyID,Obj.Branch, "PRT_PurchaseOrder"))
                        {
                            headerRow.PurchaseOrderVersion = 0;
                            using (SqlCommand cmd = new SqlCommand("INSERT INTO PRT_PurchaseOrder\r\n(\r\n    PurchaseOrderNumber,\r\n    PurchaseOrderDate,\r\n    Company_ID,\r\n    Branch_ID,\r\n    TypeofPurchase_ID,\r\n    Supplier_ID,\r\n    SUPPLIERQUOTATION_ID,\r\n    PurchaseOrderClass_ID,\r\n    ModeOfShipment_ID,\r\n    InvoiceAddress_ID,\r\n    ConsigneeAddress_ID,\r\n    PaymentTerms,\r\n    Remarks,\r\n    Brand_ID,\r\n    ProductType_ID,\r\n    IsUnderBreakDown,\r\n    Model_ID,\r\n    SerialNumber,\r\n    TotalOrderAmount,\r\n    WareHouse_ID,\r\n    EnableVersion,\r\n    IsArchived,\r\n    IsDealer,\r\n    FinancialYear,\r\n    DiscountPercentage,\r\n    DiscountAmount,\r\n    DiscountedAmount,\r\n    TaxStructure_ID,\r\n    TotalTaxableAmount,\r\n    TaxAmount\r\n,PurchaseOrderVersion,Updated_by,Updated_Date,Expected_Delivery_Date,PurchaseOrderStatus_ID,POStatus_ID,IncoTerms,TallySentStatus)\r\nVALUES\r\n(\r\n    @PurchaseOrderNumber, \r\n    @PurchaseOrderDate, \r\n    @Company_ID, \r\n    @Branch_ID, \r\n       @TypeofPurchase_ID, \r\n    @Supplier_ID, \r\n    @SUPPLIERQUOTATION_ID, \r\n    @PurchaseOrderClass_ID, \r\n    @ModeOfShipment_ID, \r\n    @InvoiceAddress_ID, \r\n    @ConsigneeAddress_ID, \r\n    @PaymentTerms, \r\n    @Remarks, \r\n    @Brand_ID, \r\n    @ProductType_ID, \r\n    @IsUnderBreakDown, \r\n    @Model_ID, \r\n    @SerialNumber, \r\n    @TotalOrderAmount, \r\n    @WareHouse_ID, \r\n    @EnableVersion, \r\n    @IsArchived, \r\n    @IsDealer, \r\n    @FinancialYear, \r\n    @DiscountPercentage, \r\n    @DiscountAmount, \r\n    @DiscountedAmount, \r\n    @TaxStructure_ID, \r\n    @TotalTaxableAmount, \r\n    @TaxAmount\r\n,@PurchaseOrderVersion,@Updated_by,@Updated_Date,@Expected_Delivery_Date,@PurchaseOrderStatus_ID,@POStatus_ID,@IncoTerms,@TallySentStatus); SELECT SCOPE_IDENTITY(); ", conn))
                            {
                                cmd.Parameters.AddWithValue("@PurchaseOrderNumber", "");
                                cmd.Parameters.AddWithValue("@PurchaseOrderDate", headerRow.PurchaseOrderDate);
                                cmd.Parameters.AddWithValue("@Company_ID", headerRow.Company_ID);
                                cmd.Parameters.AddWithValue("@Branch_ID", headerRow.Branch_ID);
                              
                                cmd.Parameters.AddWithValue("@TypeofPurchase_ID", headerRow.TypeofPurchase_ID);
                                cmd.Parameters.AddWithValue("@Supplier_ID", headerRow.Supplier_ID);
                                cmd.Parameters.AddWithValue("@SUPPLIERQUOTATION_ID",
                               headerRow.SUPPLIERQUOTATION_ID == 0 || headerRow.SUPPLIERQUOTATION_ID == null
                               ? (object)DBNull.Value
                               : headerRow.SUPPLIERQUOTATION_ID);
                                cmd.Parameters.AddWithValue("@PurchaseOrderClass_ID", headerRow.PurchaseOrderClass_ID);
                                cmd.Parameters.AddWithValue("@ModeOfShipment_ID", headerRow.ModeOfShipment_ID ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@InvoiceAddress_ID", headerRow.InvoiceAddress_ID);
                                cmd.Parameters.AddWithValue("@ConsigneeAddress_ID", headerRow.ConsigneeAddress_ID);
                                cmd.Parameters.AddWithValue("@PaymentTerms", headerRow.PaymentTerms);
                                cmd.Parameters.AddWithValue("@Remarks", string.IsNullOrEmpty(headerRow.Remarks) ? (object)DBNull.Value : headerRow.Remarks);

                                cmd.Parameters.AddWithValue("@Brand_ID", headerRow.Brand_ID ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@ProductType_ID", headerRow.ProductType_ID ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@IsUnderBreakDown", headerRow.IsUnderBreakDown);
                                cmd.Parameters.AddWithValue("@Model_ID", headerRow.Model_ID ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@SerialNumber", string.IsNullOrEmpty(headerRow.SerialNumber) ? (object)DBNull.Value : headerRow.SerialNumber);

                                cmd.Parameters.AddWithValue("@TotalOrderAmount", headerRow.TotalOrderAmount);
                                cmd.Parameters.AddWithValue("@WareHouse_ID", headerRow.WareHouse_ID);
                                cmd.Parameters.AddWithValue("@EnableVersion", headerRow.EnableVersion);
                                cmd.Parameters.AddWithValue("@IsArchived", headerRow.IsArchived);
                                cmd.Parameters.AddWithValue("@IsDealer", headerRow.IsDealer);
                                cmd.Parameters.AddWithValue("@FinancialYear", headerRow.FinancialYear);
                                cmd.Parameters.AddWithValue("@DiscountPercentage", headerRow.DiscountPercentage);
                                cmd.Parameters.AddWithValue("@DiscountAmount", headerRow.DiscountAmount);
                                cmd.Parameters.AddWithValue("@DiscountedAmount", headerRow.DiscountedAmount);
                                cmd.Parameters.AddWithValue("@TaxStructure_ID", headerRow.TaxStructure_ID ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@TotalTaxableAmount", headerRow.TotalTaxableAmount ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@TaxAmount", headerRow.TaxAmount ?? (object)DBNull.Value);
                                cmd.Parameters.AddWithValue("@PurchaseOrderVersion", headerRow.PurchaseOrderVersion );
                                cmd.Parameters.AddWithValue("@Updated_by", headerRow.Updated_by);
                                cmd.Parameters.AddWithValue("@Updated_Date", headerRow.Updated_Date);
                                cmd.Parameters.AddWithValue("@Expected_Delivery_Date", headerRow.Expected_Delivery_Date);
                                cmd.Parameters.AddWithValue("@PurchaseOrderStatus_ID", headerRow.PurchaseOrderStatus_ID);
                                cmd.Parameters.AddWithValue("@POStatus_ID", headerRow.POStatus_ID);
                                cmd.Parameters.AddWithValue("@IncoTerms", headerRow.IncoTerms);
                                cmd.Parameters.AddWithValue("@TallySentStatus", headerRow.TallySentStatus);

                                




                                object result = cmd.ExecuteScalar();
                                if (result != null && result != DBNull.Value)
                                {
                                    headerRow.PurchaseOrder_ID = Convert.ToInt32(result);
                                }
                            }
                            foreach (var det in headerRow.PRT_PurchaseOrderPartsDetail)
                            {
                                using (SqlCommand cmd = new SqlCommand(@"
                                    INSERT INTO PRT_PurchaseOrderPartsDetail
                                    (
                                        PurchaseOrder_ID,
                                        Parts_ID,
                                        SupplierPrice,
                                        RequestedQuantity,
                                        ApprovedQuantity,
                                        InvoicedQuantity,
                                        BackOrderCancelledQuantity,
                                        DiscountPercentage,
                                        DiscountAmount,
                                        TaxStructure_ID,
                                        TaxAmount,
                                        Amount,
                                        PartsOrder_ID,
                                        DiscountedAmount,
                                        MRP
                                    )
                                    VALUES
                                    (
                                        @PurchaseOrder_ID,
                                        @Parts_ID,
                                        @SupplierPrice,
                                        @RequestedQuantity,
                                        @ApprovedQuantity,
                                        @InvoicedQuantity,
                                        @BackOrderCancelledQuantity,
                                        @DiscountPercentage,
                                        @DiscountAmount,
                                        @TaxStructure_ID,
                                        @TaxAmount,
                                        @Amount,
                                        @PartsOrder_ID,
                                        @DiscountedAmount,
                                        @MRP
                                    )", conn))
                                {
                                    cmd.Parameters.AddWithValue("@PurchaseOrder_ID", headerRow.PurchaseOrder_ID);
                                    cmd.Parameters.AddWithValue("@Parts_ID", GetDbValue(det.Parts_ID));
                                    cmd.Parameters.AddWithValue("@SupplierPrice", GetDbValue(det.SupplierPrice));
                                    cmd.Parameters.AddWithValue("@RequestedQuantity", GetDbValue(det.RequestedQuantity));
                                    cmd.Parameters.AddWithValue("@ApprovedQuantity", GetDbValue(det.ApprovedQuantity));
                                    cmd.Parameters.AddWithValue("@InvoicedQuantity", GetDbValue(det.InvoicedQuantity));
                                    cmd.Parameters.AddWithValue("@BackOrderCancelledQuantity", GetDbValue(det.BackOrderCancelledQuantity));
                                    cmd.Parameters.AddWithValue("@DiscountPercentage", GetDbValue(det.DiscountPercentage));
                                    cmd.Parameters.AddWithValue("@DiscountAmount", GetDbValue(det.DiscountAmount));
                                    cmd.Parameters.AddWithValue("@TaxStructure_ID", GetDbValue(det.TaxStructure_ID));
                                    cmd.Parameters.AddWithValue("@TaxAmount", GetDbValue(det.TaxAmount));
                                    cmd.Parameters.AddWithValue("@Amount", GetDbValue(det.Amount));
                                    cmd.Parameters.AddWithValue("@PartsOrder_ID", GetDbValue(det.PartsOrder_ID));
                                    cmd.Parameters.AddWithValue("@DiscountedAmount", GetDbValue(det.DiscountedAmount));
                                    cmd.Parameters.AddWithValue("@MRP", GetDbValue(det.MRP));


                                    cmd.ExecuteNonQuery();
                                }
                            }


                            //gbl.InsertGPSDetails(companyID, branchID, userid, Convert.ToInt32(Common.GetObjectID("PRT_PurchaseOrder")), headerRow.PurchaseOrder_ID, 0, 0, "Insert", false);


                            if (headerRow.SUPPLIERQUOTATION_ID > 0)
                            {
                                using (SqlCommand cmd = new SqlCommand(@"
                                UPDATE PRT_SUPPLIERQUOTATION 
                                SET PURCHASEORDER_ID = @POID 
                                WHERE SUPPLIERQUOTATION_ID = @SQID", conn))
                                {
                                    cmd.Parameters.AddWithValue("@POID", headerRow.PurchaseOrder_ID);
                                    cmd.Parameters.AddWithValue("@SQID", headerRow.SUPPLIERQUOTATION_ID);
                                    cmd.ExecuteNonQuery();
                                }
                            }

                            PurchaseORDERIDDGPS = headerRow.PurchaseOrder_ID;
                            if (wfCount == 1)
                            {
                                List<Tuple<int, int>> partsDetails = new List<Tuple<int, int>>();
                                string selectPartsQuery = @"
                                SELECT Parts_ID, ApprovedQuantity
                                FROM PRT_PurchaseOrderPartsDetail
                                WHERE PurchaseOrder_ID = @POID";

                                using (SqlCommand cmdSelect = new SqlCommand(selectPartsQuery, conn))
                                {
                                    cmdSelect.Parameters.AddWithValue("@POID", headerRow.PurchaseOrder_ID);
                                    using (SqlDataReader reader = cmdSelect.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            int partsID = reader.GetInt32(0); // Parts_ID
                                            int approvedQuantity = reader.GetInt32(1); // ApprovedQuantity
                                            partsDetails.Add(new Tuple<int, int>(partsID, approvedQuantity));
                                        }
                                    }
                                }
                                foreach (var partDetail in partsDetails)
                                {
                                    int partsID = partDetail.Item1; // Get Parts_ID
                                    int approvedQuantity = partDetail.Item2;
                                    string checkQuery = @"
                                    SELECT COUNT(1) 
                                    FROM GNM_PartsStockDetail 
                                    WHERE Parts_ID = @PartsID AND WareHouse_ID = @WareHouse_ID";
                                    using (SqlCommand cmdCheck = new SqlCommand(checkQuery, conn))
                                    {
                                        cmdCheck.Parameters.AddWithValue("@PartsID", partsID);
                                        cmdCheck.Parameters.AddWithValue("@WareHouse_ID", headerRow.WareHouse_ID);
                                        int recordCount = (int)cmdCheck.ExecuteScalar();
                                        if (recordCount > 0)
                                        {
                                            string updateQuery = @"
                                            UPDATE GNM_PartsStockDetail 
                                            SET PendingPurchaseOrderQuantity = COALESCE(PendingPurchaseOrderQuantity, 0) + @ApprovedQty
                                            WHERE Parts_ID = @PartsID AND WareHouse_ID = @WareHouse_ID";
                                            using (SqlCommand cmdUpdate = new SqlCommand(updateQuery, conn))
                                            {
                                                cmdUpdate.Parameters.AddWithValue("@ApprovedQty", approvedQuantity);  // You should fetch the actual ApprovedQuantity based on the Parts_ID
                                                cmdUpdate.Parameters.AddWithValue("@PartsID", partsID);
                                                cmdUpdate.Parameters.AddWithValue("@WareHouse_ID", headerRow.WareHouse_ID);

                                                cmdUpdate.ExecuteNonQuery(); // Update the record
                                            }
                                        }
                                        else
                                        {
                                            string binLocationQuery = @"
                                            SELECT TOP 1 BinLocation_ID 
                                            FROM GNM_BinLocation 
                                            WHERE Branch_ID = @Branch_ID 
                                                AND Company_ID = @Company_ID 
                                                AND BinLocation_IsDefault = 1 
                                                AND BinLocation_IsActive = 1 
                                                AND WareHouse_ID = @WareHouse_ID";
                                            int binLocationID = 0;
                                            using (SqlCommand cmdBinLocation = new SqlCommand(binLocationQuery, conn))
                                            {
                                                cmdBinLocation.Parameters.AddWithValue("@Branch_ID", branchID);
                                                cmdBinLocation.Parameters.AddWithValue("@Company_ID", companyID);
                                                cmdBinLocation.Parameters.AddWithValue("@WareHouse_ID", headerRow.WareHouse_ID);

                                                object result = cmdBinLocation.ExecuteScalar();
                                                if (result != null && result != DBNull.Value)
                                                {
                                                    binLocationID = Convert.ToInt32(result);
                                                }
                                            }
                                            string insertQuery = @"
                                                INSERT INTO GNM_PartsStockDetail (Parts_ID, WareHouse_ID, Branch_ID, Company_ID, MinOrderQty, ReOrderLevel, ReOrderLevelQuantity, PendingPurchaseOrderQuantity, BinLocation_ID)
                                                VALUES (@PartsID, @WareHouse_ID, @Branch_ID, @Company_ID, 1, 1, 1, @ApprovedQty, @BinLocation_ID)";
                                            using (SqlCommand cmdInsert = new SqlCommand(insertQuery, conn))
                                            {
                                                cmdInsert.Parameters.AddWithValue("@PartsID", partsID);
                                                cmdInsert.Parameters.AddWithValue("@WareHouse_ID", headerRow.WareHouse_ID);
                                                cmdInsert.Parameters.AddWithValue("@Branch_ID", branchID);
                                                cmdInsert.Parameters.AddWithValue("@Company_ID", companyID);
                                                cmdInsert.Parameters.AddWithValue("@ApprovedQty", 1); // Use actual approved quantity here
                                                cmdInsert.Parameters.AddWithValue("@BinLocation_ID", binLocationID); // Set BinLocation_ID

                                                cmdInsert.ExecuteNonQuery(); // Insert the new record
                                            }


                                        }
                                    }

                                }
                            }

                            if (!Convert.ToBoolean(headerRow.IsDealer))
                            {

                                string selectPartyQuery = @"
                                SELECT Party_Mobile, Party_Email 
                                FROM GNM_Party
                                WHERE Party_ID = @SupplierID";

                                using (SqlCommand cmdSelectParty = new SqlCommand(selectPartyQuery, conn))
                                {
                                    cmdSelectParty.Parameters.AddWithValue("@SupplierID", headerRow.Supplier_ID);
                                    using (SqlDataReader reader = cmdSelectParty.ExecuteReader())
                                    {
                                        if (reader.Read())
                                        {
                                            CustomerMobNo = reader["Party_Mobile"].ToString();
                                            CustomerEmailID = reader["Party_Email"].ToString();
                                        }
                                    }
                                }

                            }
                            else
                            {

                                string selectCompanyQuery = @"
                                SELECT Branch_Mobile, Branch_Email 
                                FROM GNM_Branch
                                WHERE Branch_ID = @SupplierID";
                                using (SqlCommand cmdSelectCompany = new SqlCommand(selectCompanyQuery, conn))
                                {
                                    cmdSelectCompany.Parameters.AddWithValue("@SupplierID", headerRow.Supplier_ID);
                                    using (SqlDataReader reader = cmdSelectCompany.ExecuteReader())
                                    {
                                        if (reader.Read())
                                        {
                                            CustomerMobNo = reader["Branch_Mobile"].ToString();
                                            CustomerEmailID = reader["Branch_Email"].ToString();
                                        }
                                    }
                                }

                            }
                            EmailSub = "";
                            EmailtoAssigneeBody = "";
                            EmailtoCustomerBody = "";
                            SMStoAssigneeText = "";
                            SMStoCustomerText = "";
                            //added by Kavitha to add CC and BCC -start
                            AddresseEmailCC = "";
                            AddresseEmailBCC = ""; custEmailBCC = ""; custEmailCC = "";
                            //added by Kavitha to add CC and BCC -end
                            byte firstRoleorIndividual = stepLinkRow != null ? stepLinkRow.Addresse_Flag : Convert.ToByte(1);
                            int roleID = stepLinkRow != null ? stepLinkRow.Addresse_Flag == 0 ? Convert.ToInt32(stepLinkRow.Addresse_WFRole_ID) : Obj.User_ID : Obj.User_ID;

                            int ObjectID = Convert.ToInt32(Obj.ObjectID);
                            string ObjectName = null;


                            string selectQuery = @"
                            SELECT Object_Description
                            FROM GNM_Object
                            WHERE Object_ID = @ObjectID";
                            using (SqlCommand cmd = new SqlCommand(selectQuery, conn))
                            {

                                cmd.Parameters.AddWithValue("@ObjectID", ObjectID);

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {

                                        ObjectName = reader["Object_Description"].ToString();
                                    }
                                }
                            }

                            string ApplicationLink = Convert.ToString(Obj.ReLoginView) + ("?OID=" + Common.GetObjectID("PRT_PurchaseOrder") + "&TID=" + headerRow.PurchaseOrder_ID);


                            string BranchName = Obj.Branch.ToString();
                            string CompanyName = Obj.Company_Name.ToString();
                            string LanguageCode =Obj.Languagecode.ToString();
                            int CompID = Convert.ToInt32(Obj.Company_ID);

                            CommonMethodForEmailandSMSList emailAndSMSDetails = new CommonMethodForEmailandSMSList
                            {
                                TemplateCode = "PuOr001",
                                CompanyId = CompID,
                                LanguageCode = LanguageCode,

                                p1 = headerRow.PurchaseOrderNumber,
                                p2 = ObjectName,
                                p3 = DateTime.Now.ToString("dd-MMM-yyyy"),
                                p4 = ApplicationLink,
                                p5 = BranchName,
                                p6 = CompanyName


                            };


                            StringBuilder[] ETRes = CoreEmailTemplateService.CommonMethodForEmailandSMS(connString, emailAndSMSDetails);

                            string EmailSubject = ETRes[0].ToString();
                            string EmailBody = ETRes[1].ToString();
                            //added by Kavitha to add CC and BCC -start
                            if (ETRes[4] != null)
                            {
                                AddresseEmailCC = ETRes[4].ToString();
                            }
                            if (ETRes[3] != null)
                            { AddresseEmailBCC = ETRes[3].ToString(); }
                            //added by Kavitha to add CC and BCC -end
                            CommonMethodForEmailandSMSList emailAndSMSDetails1 = new CommonMethodForEmailandSMSList
                            {
                                TemplateCode = "PuOr002",
                                CompanyId = CompID,
                                LanguageCode = LanguageCode,

                                p1 = headerRow.PurchaseOrderNumber,
                                p2 = ObjectName,
                                p3 = DateTime.Now.ToString("dd-MMM-yyyy"),
                                p4 = BranchName,
                                p5 = CompanyName



                            };
                            StringBuilder[] ETRes1 = CoreEmailTemplateService.CommonMethodForEmailandSMS(connString, emailAndSMSDetails1);

                            string EmailBody1 = ETRes1[1].ToString();

                            EmailSub = EmailSubject;
                            EmailtoAssigneeBody = EmailBody;
                            EmailtoCustomerBody = EmailBody1;

                            if (ETRes1[4].Length > 0)
                            {
                                custEmailCC = ETRes1[4].ToString();
                            }
                            if (ETRes1[3].Length > 0)
                            { custEmailBCC = ETRes1[3].ToString(); }



                            CaseProgressObjects CPDetails = new CaseProgressObjects();
                            CPDetails.actionBy = Convert.ToInt32(Obj.User_ID);
                            CPDetails.actionID = ActionID;
                            CPDetails.actionRemarks = ActionRemarks;
                            CPDetails.actionTime = DateTime.Now;
                            CPDetails.addresseType = firstRoleorIndividual;
                            CPDetails.AssignTo = roleID;
                            CPDetails.CompanyID = Convert.ToInt32(Obj.Company_ID);
                            CPDetails.currentStepID = CurrentStepID;
                            CPDetails.customerEmailID = CustomerEmailID;
                            CPDetails.customerMobileNumber = CustomerMobNo;
                            CPDetails.emailBodyAddress = EmailtoAssigneeBody;
                            CPDetails.emailBodyCustomer = EmailtoCustomerBody;
                            CPDetails.emailSubAddressee = EmailSub;
                            CPDetails.emailSubCustomer = EmailSub;
                            //added by Kavitha to add CC and BCC -start
                            CPDetails.AddresseCC = AddresseEmailCC;
                            CPDetails.AddresseBcc = AddresseEmailBCC;
                            CPDetails.CustomerBcc = custEmailBCC;
                            CPDetails.customerCC = custEmailCC;
                            //added by Kavitha to add CC and BCC -end
                            CPDetails.NextStepID = CurrentStepID;
                            CPDetails.receivedTime = DateTime.Now;
                            CPDetails.RoleID = RoleID;
                            CPDetails.smsTextAddressee = SMStoAssigneeText;
                            CPDetails.smsTextCustomer = SMStoCustomerText;
                            CPDetails.transactionNumber = headerRow.PurchaseOrder_ID;
                            CPDetails.workFlowID = WorkFlowID;
                            SMSTemplate smsCustomer = new SMSTemplate();
                            SMSTemplate smsAssignee = new SMSTemplate();
                            InsertWorkFlowHistory(CPDetails, smsCustomer, smsAssignee,Obj.IsVersionAccess, connString);

                            if (ActionID > 0)
                            {
                                CPDetails.addresseType = AddresseType;
                                CPDetails.AssignTo = AssignedTo;
                                CPDetails.currentStepID = CurrentStepID;
                                CPDetails.NextStepID = NextStepID;

                                InsertWorkFlowHistory(CPDetails, smsCustomer, smsAssignee, Obj.IsVersionAccess, connString);

                                //Added by DK - 18-Dec-14
                                //IEnumerable<WF_WFField> FieldList = WorkFlowClient.WF_WFField.Where(i => i.WorkFlow_ID == WorkFlowID);
                                foreach (var x in FieldList)
                                {
                                    if (x.WorkFlowFieldName == "TotalOrderAmount")
                                    {
                                        HelpDeskServiceRequestServices.UpdateWorkFlowFieldValue(companyID, WorkFlowID, x.WFField_ID, headerRow.PurchaseOrder_ID, Convert.ToDecimal(headerRow.TotalOrderAmount).ToString(), connString);
                                    }
                                }
                                ForAutoAllocationList allocationList = new ForAutoAllocationList
                                {
                                    CompanyID = companyID,          // Example value for CompanyID
                                    WorkFlowID = WorkFlowID,       // Example value for WorkFlowID
                                    CurrentStepID = CurrentStepID,      // Example value for CurrentStepID
                                    ActionID = ActionID,          // Example value for ActionID
                                    TransactionNumber = headerRow.PurchaseOrder_ID, // Example value for TransactionNumber
                                    RoleID = RoleID,             // Example value for RoleID
                                    UserID = Obj.User_ID,          // Example value for UserID
                                    BranchID = branchID,           // Example value for BranchID
                                    UserCulture = ""   // Example value for UserCulture
                                };

                                ForAutoAllocation(companyID, WorkFlowID, CurrentStepID, ActionID, headerRow.PurchaseOrder_ID, RoleID, Obj.User_ID, connString, LogException);
                                //-------------------------
                            }


                            jsonData = new
                            {
                                IsPSExists = true,
                                IsSuccess = true,
                                PurchaseOrder_ID = headerRow.PurchaseOrder_ID,
                                PurchaseOrderNumber = headerRow.PurchaseOrderNumber
                            };
                        }
                        else
                        {
                            jsonData = new
                            {
                                IsPSExists = false,
                                IsSuccess = true,
                                PurchaseOrder_ID = headerRow.PurchaseOrder_ID,
                                PurchaseOrderNumber = ""
                            };
                        }
                    }
                }
                else
                {

                    for (int i = 0; i < headerRow.PRT_PurchaseOrderPartsDetail.Count; i++)
                    {
                        ApprovedQty = ApprovedQty + Convert.ToDecimal(headerRow.PRT_PurchaseOrderPartsDetail.ElementAt(i).ApprovedQuantity);
                    }
                    if (WFStepName.ToLower() == "approved".ToLower() && ApprovedQty == 0)
                    {
                        jsonData = new
                        {
                            IsPSExists = true,
                            IsSuccess = false,
                            POPrefix = true,
                            PurchaseOrder_ID = headerRow.PurchaseOrder_ID,
                            PurchaseOrderNumber = headerRow.PurchaseOrderNumber,
                            ApprovedQty
                        };
                    }//changes ends
                    else
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            PRT_PurchaseOrder editRow = new PRT_PurchaseOrder();

                            SqlCommand cmd;
                            bool IsVersion = false;
                            string queryPrt = "SELECT * FROM PRT_PurchaseOrder WHERE PurchaseOrder_ID = @PurchaseOrderID";
                            cmd = new SqlCommand(queryPrt, conn);
                            cmd.Parameters.AddWithValue("@PurchaseOrderID", headerRow.PurchaseOrder_ID);
                            SqlDataReader reader = cmd.ExecuteReader();
                            if (reader.Read()) // If a row is found
                            {
                                editRow = new PRT_PurchaseOrder
                                {
                                    PurchaseOrder_ID = Convert.ToInt32(reader["PurchaseOrder_ID"]),
                                    POStatus_ID = Convert.ToInt32(reader["POStatus_ID"]),
                                    Supplier_ID = Convert.ToInt32(reader["Supplier_ID"]),
                                    IsDealer = Convert.ToBoolean(reader["IsDealer"])

                                };
                            }


                            cmd = new SqlCommand("SELECT RefMasterDetail_ID FROM GNM_RefMasterDetail WHERE RefMasterDetail_Short_Name = 'Approved' AND EXISTS (SELECT 1 FROM GNM_RefMaster WHERE RefMaster_ID = GNM_RefMasterDetail.RefMaster_ID AND RefMaster_Name = 'POSTATUS')", conn);
                            int poStatusApprovedID = Convert.ToInt32(cmd.ExecuteScalar());

                            if (Obj.IsVersionAccess == true)
                            {
                                int id = headerRow.PurchaseOrder_ID;

                                int version = 0;
                                cmd = new SqlCommand("SELECT TOP 1 PurchaseOrderVersion FROM PRT_PurchaseOrder_H WHERE PurchaseOrder_ID = @id ORDER BY PurchaseOrderH_ID DESC", conn);
                                cmd.Parameters.AddWithValue("@id", id);
                                object versionResult = cmd.ExecuteScalar();
                                version = versionResult == null ? 0 : Convert.ToInt32(versionResult) + 1;




                                string query = "INSERT INTO PRT_PurchaseOrder_H (Company_ID,Branch_ID,PurchaseOrder_ID,PurchaseOrderNumber,PurchaseOrderVersion,PurchaseOrderDate,TypeofPurchase_ID,Supplier_ID,PurchaseOrderClass_ID,InvoiceAddress_ID,ConsigneeAddress_ID,PaymentTerms,Brand_ID,ProductType_ID,Model_ID,SerialNumber,TotalOrderAmount,Remarks,EnableVersion,IsArchived,FinancialYear,Updated_by,Updated_Date,Expected_Delivery_Date,DocumentNumber,DiscountPercentage,DiscountAmount,DiscountedAmount,TaxStructure_ID,TotalTaxableAmount,TaxAmount,PurchaseOrderStatus_ID,WareHouse_ID,POStatus_ID,ModeOfShipment_ID) SELECT Company_ID,Branch_ID,PurchaseOrder_ID,PurchaseOrderNumber," + version + ",PurchaseOrderDate,TypeofPurchase_ID,Supplier_ID,PurchaseOrderClass_ID,InvoiceAddress_ID,ConsigneeAddress_ID,PaymentTerms,Brand_ID,ProductType_ID,Model_ID,SerialNumber,TotalOrderAmount,Remarks,EnableVersion,IsArchived,FinancialYear,Updated_by,Updated_Date,Expected_Delivery_Date,DocumentNumber,DiscountPercentage,DiscountAmount,DiscountedAmount,TaxStructure_ID,TotalTaxableAmount,TaxAmount,PurchaseOrderStatus_ID,WareHouse_ID,POStatus_ID,ModeOfShipment_ID  FROM PRT_PurchaseOrder WHERE PurchaseOrder_ID=" + id + "";
                                cmd = new SqlCommand(query, conn);
                                cmd.Parameters.AddWithValue("@id", id);
                                cmd.ExecuteNonQuery();



                                cmd = new SqlCommand("SELECT TOP 1 PurchaseOrderH_ID FROM PRT_PurchaseOrder_H ORDER BY PurchaseOrderH_ID DESC", conn);
                                int PurchaseOrderHID = Convert.ToInt32(cmd.ExecuteScalar());


                                string query1 = "INSERT INTO dbo.PRT_PurchaseOrderPartsDetail_H (PurchaseOrderH_ID,PurchaseOrderPartsDetail_ID,PurchaseOrder_ID,Parts_ID,SupplierPrice,RequestedQuantity,ApprovedQuantity,InvoicedQuantity,BackOrderCancelledQuantity,DiscountPercentage,DiscountAmount,TaxStructure_ID,TaxAmount,Amount,PartsOrder_ID,DiscountedAmount) SELECT " + PurchaseOrderHID + ",PurchaseOrderPartsDetail_ID,PurchaseOrder_ID,Parts_ID,SupplierPrice,RequestedQuantity,ApprovedQuantity,InvoicedQuantity,BackOrderCancelledQuantity,DiscountPercentage,DiscountAmount,TaxStructure_ID,TaxAmount,Amount,PartsOrder_ID,DiscountedAmount FROM dbo.PRT_PurchaseOrderPartsDetail WHERE PurchaseOrder_ID=" + id + "";
                                cmd = new SqlCommand(query1, conn);
                                cmd.Parameters.AddWithValue("@id", id);
                                cmd.ExecuteNonQuery();


                                cmd = new SqlCommand("SELECT Parts_ID, ApprovedQuantity FROM PRT_PurchaseOrderPartsDetail WHERE PurchaseOrder_ID=@id", conn);
                                cmd.Parameters.AddWithValue("@id", id);
                                using (reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        int Parts_ID = Convert.ToInt32(reader["Parts_ID"]);
                                        decimal ApprovedQuantity = Convert.ToDecimal(reader["ApprovedQuantity"]);

                                        SqlCommand updateStockCmd = new SqlCommand("UPDATE GNM_PartsStockDetail SET PendingPurchaseOrderQuantity = PendingPurchaseOrderQuantity - @ApprovedQuantity WHERE Parts_ID = @Parts_ID AND WareHouse_ID = @WareHouse_ID", conn);
                                        updateStockCmd.Parameters.AddWithValue("@ApprovedQuantity", ApprovedQuantity);
                                        updateStockCmd.Parameters.AddWithValue("@Parts_ID", Parts_ID);
                                        updateStockCmd.Parameters.AddWithValue("@WareHouse_ID", headerRow.WareHouse_ID.Value);
                                        updateStockCmd.ExecuteNonQuery();
                                    }
                                }
                            }

                            cmd = new SqlCommand("UPDATE PRT_PurchaseOrder SET Brand_ID=@Brand_ID, DiscountAmount=@DiscountAmount, DiscountedAmount=@DiscountedAmount, DiscountPercentage=@DiscountPercentage, EnableVersion=@EnableVersion, Expected_Delivery_Date=@Expected_Delivery_Date, Model_ID=@Model_ID, PaymentTerms=@PaymentTerms, Remarks=@Remarks, ProductType_ID=@ProductType_ID, PurchaseOrderStatus_ID=@PurchaseOrderStatus_ID, SerialNumber=@SerialNumber, TaxAmount=@TaxAmount, TaxStructure_ID=@TaxStructure_ID, TotalOrderAmount=@TotalOrderAmount, TotalTaxableAmount=@TotalTaxableAmount, Supplier_ID=@Supplier_ID, PurchaseOrderClass_ID=@PurchaseOrderClass_ID, InvoiceAddress_ID=@InvoiceAddress_ID, ConsigneeAddress_ID=@ConsigneeAddress_ID, WareHouse_ID=@WareHouse_ID, IsUnderBreakDown=@IsUnderBreakDown, Updated_by=@Updated_by, IncoTerms=@IncoTerms, Updated_Date=@Updated_Date, ModeOfShipment_ID=@ModeOfShipment_ID, POStatus_ID=CASE WHEN @POStatus_ID != 0 THEN @POStatus_ID ELSE POStatus_ID END WHERE PurchaseOrder_ID=@PurchaseOrder_ID", conn);

                            cmd.Parameters.AddWithValue("@Brand_ID", headerRow.Brand_ID ?? (object)DBNull.Value);
                            cmd.Parameters.AddWithValue("@DiscountAmount", headerRow.DiscountAmount ?? (object)DBNull.Value);
                            cmd.Parameters.AddWithValue("@DiscountedAmount", headerRow.DiscountedAmount ?? (object)DBNull.Value);
                            cmd.Parameters.AddWithValue("@DiscountPercentage", headerRow.DiscountPercentage ?? (object)DBNull.Value);
                            cmd.Parameters.AddWithValue("@EnableVersion", headerRow.EnableVersion );
                            cmd.Parameters.AddWithValue("@Expected_Delivery_Date", headerRow.Expected_Delivery_Date ?? (object)DBNull.Value);
                            cmd.Parameters.AddWithValue("@Model_ID", headerRow.Model_ID ?? (object)DBNull.Value);
                            cmd.Parameters.AddWithValue("@PaymentTerms", string.IsNullOrEmpty(headerRow.PaymentTerms) ? (object)DBNull.Value : headerRow.PaymentTerms);
                            cmd.Parameters.AddWithValue("@Remarks", string.IsNullOrEmpty(headerRow.Remarks) ? (object)DBNull.Value : headerRow.Remarks);
                            cmd.Parameters.AddWithValue("@ProductType_ID", headerRow.ProductType_ID ?? (object)DBNull.Value);
                            cmd.Parameters.AddWithValue("@PurchaseOrderStatus_ID", headerRow.PurchaseOrderStatus_ID );
                            cmd.Parameters.AddWithValue("@SerialNumber", string.IsNullOrEmpty(headerRow.SerialNumber) ? (object)DBNull.Value : headerRow.SerialNumber);
                            cmd.Parameters.AddWithValue("@TaxAmount", headerRow.TaxAmount ?? (object)DBNull.Value);
                            cmd.Parameters.AddWithValue("@TaxStructure_ID", headerRow.TaxStructure_ID ?? (object)DBNull.Value);
                            cmd.Parameters.AddWithValue("@TotalOrderAmount", headerRow.TotalOrderAmount );
                            cmd.Parameters.AddWithValue("@TotalTaxableAmount", headerRow.TotalTaxableAmount ?? (object)DBNull.Value);
                            cmd.Parameters.AddWithValue("@Supplier_ID", headerRow.Supplier_ID );
                            cmd.Parameters.AddWithValue("@PurchaseOrderClass_ID", headerRow.PurchaseOrderClass_ID ?? (object)DBNull.Value);
                            cmd.Parameters.AddWithValue("@InvoiceAddress_ID", headerRow.InvoiceAddress_ID );
                            cmd.Parameters.AddWithValue("@ConsigneeAddress_ID", headerRow.ConsigneeAddress_ID );
                            cmd.Parameters.AddWithValue("@WareHouse_ID", headerRow.WareHouse_ID ?? (object)DBNull.Value);
                            cmd.Parameters.AddWithValue("@IsUnderBreakDown", headerRow.IsUnderBreakDown);
                            cmd.Parameters.AddWithValue("@Updated_by", userid);
                            cmd.Parameters.AddWithValue("@IncoTerms", string.IsNullOrEmpty(headerRow.IncoTerms) ? (object)DBNull.Value : headerRow.IncoTerms);
                            cmd.Parameters.AddWithValue("@Updated_Date", DateTime.Now);
                            cmd.Parameters.AddWithValue("@ModeOfShipment_ID", headerRow.ModeOfShipment_ID ?? (object)DBNull.Value);
                            cmd.Parameters.AddWithValue("@POStatus_ID", headerRow.POStatus_ID != 0 ? headerRow.POStatus_ID : editRow.POStatus_ID);
                            cmd.Parameters.AddWithValue("@PurchaseOrder_ID", headerRow.PurchaseOrder_ID);


                            cmd.ExecuteNonQuery();


                            foreach (var part in headerRow.PRT_PurchaseOrderPartsDetail)
                            {
                                if (part.PurchaseOrderPartsDetail_ID == 0)
                                {
                                    string insertQuery = @"INSERT INTO PRT_PurchaseOrderPartsDetail (PurchaseOrder_ID, Parts_ID, SupplierPrice, RequestedQuantity, ApprovedQuantity, InvoicedQuantity, BackOrderCancelledQuantity, DiscountPercentage, DiscountAmount, TaxStructure_ID, TaxAmount, Amount, PartsOrder_ID, DiscountedAmount) 
                                      VALUES (@PurchaseOrder_ID, @Parts_ID, @SupplierPrice, @RequestedQuantity, @ApprovedQuantity, @InvoicedQuantity, @BackOrderCancelledQuantity, @DiscountPercentage, @DiscountAmount, @TaxStructure_ID, @TaxAmount, @Amount, @PartsOrder_ID, @DiscountedAmount)";
                                    using (cmd = new SqlCommand(insertQuery, conn))
                                    {
                                        cmd.Parameters.AddWithValue("@PurchaseOrder_ID", headerRow.PurchaseOrder_ID);
                                        cmd.Parameters.AddWithValue("@Parts_ID", part.Parts_ID);
                                        cmd.Parameters.AddWithValue("@SupplierPrice", part.SupplierPrice);
                                        cmd.Parameters.AddWithValue("@RequestedQuantity", (object)part.RequestedQuantity ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@ApprovedQuantity", (object)part.ApprovedQuantity ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@InvoicedQuantity", (object)part.InvoicedQuantity ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@BackOrderCancelledQuantity", (object)part.BackOrderCancelledQuantity ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@DiscountPercentage", (object)part.DiscountPercentage ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@DiscountAmount", (object)part.DiscountAmount ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@TaxStructure_ID", (object)part.TaxStructure_ID ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@TaxAmount", (object)part.TaxAmount ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@Amount", part.Amount);
                                        cmd.Parameters.AddWithValue("@PartsOrder_ID", (object)part.PartsOrder_ID ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@DiscountedAmount", (object)part.DiscountedAmount ?? DBNull.Value);
                                        cmd.ExecuteNonQuery();
                                    }
                                }
                                else
                                {
                                    string updateQuery = @"UPDATE PRT_PurchaseOrderPartsDetail 
                                      SET Amount=@Amount, ApprovedQuantity=@ApprovedQuantity, BackOrderCancelledQuantity=@BackOrderCancelledQuantity, DiscountAmount=@DiscountAmount, DiscountPercentage=@DiscountPercentage, InvoicedQuantity=@InvoicedQuantity, Parts_ID=@Parts_ID, PartsOrder_ID=@PartsOrder_ID, RequestedQuantity=@RequestedQuantity, SupplierPrice=@SupplierPrice, TaxAmount=@TaxAmount, TaxStructure_ID=@TaxStructure_ID, DiscountedAmount=@DiscountedAmount
                                      WHERE PurchaseOrderPartsDetail_ID=@PurchaseOrderPartsDetail_ID";
                                    using (cmd = new SqlCommand(updateQuery, conn))
                                    {
                                        cmd.Parameters.AddWithValue("@Amount", part.Amount);
                                        cmd.Parameters.AddWithValue("@ApprovedQuantity", (object)part.ApprovedQuantity ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@BackOrderCancelledQuantity", (object)part.BackOrderCancelledQuantity ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@DiscountAmount", (object)part.DiscountAmount ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@DiscountPercentage", (object)part.DiscountPercentage ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@InvoicedQuantity", (object)part.InvoicedQuantity ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@Parts_ID", part.Parts_ID);
                                        cmd.Parameters.AddWithValue("@PartsOrder_ID", (object)part.PartsOrder_ID ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@RequestedQuantity", (object)part.RequestedQuantity ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@SupplierPrice", part.SupplierPrice);
                                        cmd.Parameters.AddWithValue("@TaxAmount", (object)part.TaxAmount ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@TaxStructure_ID", (object)part.TaxStructure_ID ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@DiscountedAmount", (object)part.DiscountedAmount ?? DBNull.Value);
                                        cmd.Parameters.AddWithValue("@PurchaseOrderPartsDetail_ID", part.PurchaseOrderPartsDetail_ID);
                                        cmd.ExecuteNonQuery();
                                    }
                                }
                            }
                            foreach (var delPart in Obj.PurchaseOrderPartsDelete)
                            {
                                string deleteQuery = "DELETE FROM PRT_PurchaseOrderPartsDetail WHERE PurchaseOrderPartsDetail_ID = @ID";
                                using (cmd = new SqlCommand(deleteQuery, conn))
                                {
                                    cmd.Parameters.AddWithValue("@ID", delPart.PartID);
                                    cmd.ExecuteNonQuery();
                                }
                            }

                            if ((wfCount > 1 && poStatusName.ToLower() == "Approved".ToLower() && NextStapName.ToLower() == "End".ToLower()) || (IsVersion && editRow.POStatus_ID >= poStatusApprovedID))
                            {
                                if (ApprovedQty > 0)
                                {
                                    string getPurchasePartsQuery = "SELECT * FROM PRT_PurchaseOrderPartsDetail WHERE PurchaseOrder_ID = @PurchaseOrderID";
                                    using (SqlCommand command = new SqlCommand(getPurchasePartsQuery, conn))
                                    {
                                        command.Parameters.AddWithValue("@PurchaseOrderID", headerRow.PurchaseOrder_ID);
                                        reader = command.ExecuteReader();
                                        while (reader.Read())
                                        {
                                            int partsID = Convert.ToInt32(reader["Parts_ID"]);
                                            string getPartsStockQuery = "SELECT * FROM GNM_PartsStockDetail WHERE Parts_ID = @PartsID AND WareHouse_ID = @WareHouseID";
                                            using (SqlCommand partsStockCommand = new SqlCommand(getPartsStockQuery, conn))
                                            {
                                                partsStockCommand.Parameters.AddWithValue("@PartsID", partsID);
                                                partsStockCommand.Parameters.AddWithValue("@WareHouseID", headerRow.WareHouse_ID.Value);
                                                SqlDataReader partsStockReader = partsStockCommand.ExecuteReader();
                                                if (partsStockReader.Read())
                                                {
                                                    decimal pendingQty = partsStockReader["PendingPurchaseOrderQuantity"] == DBNull.Value ? 0 : Convert.ToDecimal(partsStockReader["PendingPurchaseOrderQuantity"]);
                                                    decimal newPendingQty = pendingQty + Convert.ToDecimal(reader["ApprovedQuantity"]);
                                                    partsStockReader.Close();
                                                    string updatePartsStockQuery = "UPDATE GNM_PartsStockDetail SET PendingPurchaseOrderQuantity = @PendingQty WHERE Parts_ID = @PartsID AND WareHouse_ID = @WareHouseID";
                                                    using (SqlCommand updateCommand = new SqlCommand(updatePartsStockQuery, conn))
                                                    {
                                                        updateCommand.Parameters.AddWithValue("@PendingQty", newPendingQty);
                                                        updateCommand.Parameters.AddWithValue("@PartsID", partsID);
                                                        updateCommand.Parameters.AddWithValue("@WareHouseID", headerRow.WareHouse_ID.Value);
                                                        updateCommand.ExecuteNonQuery();
                                                    }

                                                }
                                                else
                                                {
                                                    partsStockReader.Close();
                                                    string insertPartsStockQuery = "INSERT INTO GNM_PartsStockDetail (WareHouse_ID, Branch_ID, Company_ID, MinOrderQty, Parts_ID, PendingPurchaseOrderQuantity, ReOrderLevel, ReOrderLevelQuantity, BinLocation_ID) VALUES (@WareHouseID, @BranchID, @CompanyID, @MinOrderQty, @PartsID, @PendingQty, @ReOrderLevel, @ReOrderLevelQty, @BinLocationID)";
                                                    using (SqlCommand insertCommand = new SqlCommand(insertPartsStockQuery, conn))
                                                    {
                                                        insertCommand.Parameters.AddWithValue("@WareHouseID", headerRow.WareHouse_ID.Value);
                                                        insertCommand.Parameters.AddWithValue("@BranchID", branchID);
                                                        insertCommand.Parameters.AddWithValue("@CompanyID", companyID);
                                                        insertCommand.Parameters.AddWithValue("@MinOrderQty", 1);
                                                        insertCommand.Parameters.AddWithValue("@PartsID", partsID);
                                                        insertCommand.Parameters.AddWithValue("@PendingQty", Convert.ToDecimal(reader["ApprovedQuantity"]));
                                                        insertCommand.Parameters.AddWithValue("@ReOrderLevel", 1);
                                                        insertCommand.Parameters.AddWithValue("@ReOrderLevelQty", 1);
                                                        insertCommand.Parameters.AddWithValue("@BinLocationID", GetDefaultBinLocationID(conn, branchID, companyID, headerRow.WareHouse_ID.Value));
                                                        insertCommand.ExecuteNonQuery();
                                                    }

                                                }
                                            }
                                        }
                                        reader.Close();
                                    }
                                    string getPartyQuery = "SELECT * FROM GNM_Party WHERE Party_ID = @SupplierID";
                                    using (SqlCommand partyCommand = new SqlCommand(getPartyQuery, conn))
                                    {
                                        partyCommand.Parameters.AddWithValue("@SupplierID", headerRow.Supplier_ID);
                                        SqlDataReader partyReader = partyCommand.ExecuteReader();
                                        if (partyReader.Read())
                                        {
                                            string customerMobNo = partyReader["Party_Mobile"].ToString();
                                            string customerEmailID = partyReader["Party_Email"].ToString();
                                            partyReader.Close();
                                            if (Obj.EnablePurchaseOrderInterface.ToString() == "True" && !editRow.IsDealer == false)
                                            {
                                                if (orderClass.IsInterfaceOrderClass == true && partyReader["SupplierHasInterface"].ToString() == "True")
                                                {
                                                    string systemEnvironmentCode = Convert.ToString(Obj.SystemEnvironmentCode);
                                                    string releaseID = Convert.ToString(Obj.releaseID);
                                                    string CreateCode = Convert.ToString(Obj.CreateCode);
                                                    string sendercode = Convert.ToString(Obj.sendercode);
                                                    string BODID = Convert.ToString(Obj.BODID);
                                                    string acknowledgecode = Convert.ToString(Obj.acknowledgecode);
                                                    SendPurchaseOrderDataAsync(companyID, branchID, headerRow.PurchaseOrder_ID, systemEnvironmentCode, releaseID, CreateCode, sendercode, BODID, acknowledgecode,connString);

                                                }
                                            }
                                        }
                                    }

                                }
                            }





                            if (!Convert.ToBoolean(headerRow.IsDealer))
                            {
                                string partyQuery = "SELECT Party_Mobile, Party_Email FROM GNM_Party WHERE Party_ID = @SupplierID";
                                using (SqlCommand command = new SqlCommand(partyQuery, conn))
                                {
                                    command.Parameters.AddWithValue("@SupplierID", headerRow.Supplier_ID);
                                    reader = command.ExecuteReader();
                                    if (reader.Read())
                                    {
                                        CustomerMobNo = reader["Party_Mobile"].ToString();
                                        CustomerEmailID = reader["Party_Email"].ToString();
                                    }
                                    reader.Close();
                                }


                            }
                            else
                            {
                                string branchQuery = "SELECT Branch_Mobile, Branch_Email FROM GNM_Branch WHERE Branch_ID = @SupplierID";
                                using (SqlCommand command = new SqlCommand(branchQuery, conn))
                                {
                                    command.Parameters.AddWithValue("@SupplierID", headerRow.Supplier_ID);
                                    reader = command.ExecuteReader();
                                    if (reader.Read())
                                    {
                                        CustomerMobNo = reader["Branch_Mobile"].ToString();
                                        CustomerEmailID = reader["Branch_Email"].ToString();
                                    }
                                    reader.Close();
                                }

                            }


                            int ObjectID = Convert.ToInt32(Obj.ObjectID);
                            string ObjectName = "";
                            string objectQuery = "SELECT Object_Description FROM GNM_Object WHERE Object_ID = @ObjectID";
                            using (SqlCommand command = new SqlCommand(objectQuery, conn))
                            {
                                command.Parameters.AddWithValue("@ObjectID", ObjectID);
                                reader = command.ExecuteReader();
                                if (reader.Read())
                                {
                                    ObjectName = reader["Object_Description"].ToString();
                                }
                                reader.Close();
                            }



                            string ApplicationLink = Convert.ToString(Obj.ReLoginView) + ("?OID=" + Common.GetObjectID("PRT_PurchaseOrder") + "&TID=" + headerRow.PurchaseOrder_ID);


                            string BranchName = Obj.Branch.ToString();
                            string CompanyName = Obj.Company_Name.ToString();
                            string LanguageCode = Obj.Languagecode.ToString();
                            int CompID = Convert.ToInt32(Obj.Company_ID);

                            CommonMethodForEmailandSMSList emailAndSMSDetails = new CommonMethodForEmailandSMSList
                            {
                                TemplateCode = "PuOr001",
                                CompanyId = CompID,
                                LanguageCode = LanguageCode,

                                p1 = headerRow.PurchaseOrderNumber,
                                p2 = ObjectName,
                                p3 = DateTime.Now.ToString("dd-MMM-yyyy"),
                                p4 = ApplicationLink,
                                p5 = BranchName,
                                p6 = CompanyName


                            };

                            StringBuilder[] ETRes = CoreEmailTemplateService.CommonMethodForEmailandSMS(connString, emailAndSMSDetails);

                            string EmailSubject = ETRes[0].ToString();
                            string EmailBody = ETRes[1].ToString();
                            //added by Kavitha to add CC and BCC -start
                            if (ETRes[4].Length > 0)
                            {
                                AddresseEmailCC = ETRes[4].ToString();
                            }
                            if (ETRes[3].Length > 0)
                            { AddresseEmailBCC = ETRes[3].ToString(); }
                            //added by Kavitha to add CC and BCC -end
                            CommonMethodForEmailandSMSList emailAndSMSDetails2 = new CommonMethodForEmailandSMSList
                            {
                                TemplateCode = "PuOr002",
                                CompanyId = CompID,
                                LanguageCode = LanguageCode,

                                p1 = headerRow.PurchaseOrderNumber,
                                p2 = ObjectName,
                                p3 = DateTime.Now.ToString("dd-MMM-yyyy"),
                                p4 = BranchName,
                                p5 = CompanyName



                            };
                            StringBuilder[] ETRes1 = CoreEmailTemplateService.CommonMethodForEmailandSMS(connString, emailAndSMSDetails2);

                            string EmailBody1 = ETRes1[1].ToString();

                            EmailSub = EmailSubject;
                            EmailtoAssigneeBody = EmailBody;
                            EmailtoCustomerBody = EmailBody1;
                            //added by Kavitha to add CC and BCC -start
                            if (ETRes1[4] != null)
                            {
                                custEmailCC = ETRes1[4].ToString();
                            }
                            if (ETRes1[3] != null)
                            { custEmailBCC = ETRes1[3].ToString(); }
                            //added by Kavitha to add CC and BCC -end
                            //--------- End --------Venkateshwari ---- 04-Feb-2015 ------------

                            if (Obj.IsVersionAccess)
                            {
                                byte firstRoleorIndividual = stepLinkRow != null ? stepLinkRow.Addresse_Flag : Convert.ToByte(1);
                                int roleID = stepLinkRow != null ? stepLinkRow.Addresse_Flag == 0 ? Convert.ToInt32(stepLinkRow.Addresse_WFRole_ID) : Obj.User_ID : Obj.User_ID;

                                CaseProgressObjects CPDetails = new CaseProgressObjects();
                                CPDetails.actionBy = Convert.ToInt32(Obj.User_ID);
                                CPDetails.actionID = ActionID;
                                CPDetails.actionRemarks = ActionRemarks;
                                CPDetails.actionTime = DateTime.Now;
                                CPDetails.addresseType = firstRoleorIndividual;
                                CPDetails.AssignTo = roleID;
                                CPDetails.CompanyID = Convert.ToInt32(Obj.Company_ID);
                                CPDetails.currentStepID = CurrentStepID;
                                CPDetails.customerEmailID = CustomerEmailID;
                                CPDetails.customerMobileNumber = CustomerMobNo;
                                CPDetails.emailBodyAddress = EmailtoAssigneeBody;
                                CPDetails.emailBodyCustomer = EmailtoCustomerBody;
                                CPDetails.emailSubAddressee = EmailSub;
                                CPDetails.emailSubCustomer = EmailSub;
                                CPDetails.NextStepID = CurrentStepID;
                                CPDetails.receivedTime = DateTime.Now;
                                CPDetails.RoleID = RoleID;
                                CPDetails.smsTextAddressee = SMStoAssigneeText;
                                CPDetails.smsTextCustomer = SMStoCustomerText;
                                CPDetails.transactionNumber = headerRow.PurchaseOrder_ID;
                                //added by Kavitha to add CC and BCC -start
                                CPDetails.AddresseCC = AddresseEmailCC;
                                CPDetails.AddresseBcc = AddresseEmailBCC;
                                CPDetails.CustomerBcc = custEmailBCC;
                                CPDetails.customerCC = custEmailCC;
                                //added by Kavitha to add CC and BCC -end
                                CPDetails.workFlowID = WorkFlowID;
                                SMSTemplate smsCustomer = new SMSTemplate();
                                SMSTemplate smsAssignee = new SMSTemplate();
                                InsertWorkFlowHistory(CPDetails, smsCustomer, smsAssignee, Obj.IsVersionAccess, connString);

                                if (ActionID > 0)
                                {
                                    CPDetails.addresseType = AddresseType;
                                    CPDetails.AssignTo = AssignedTo;
                                    CPDetails.currentStepID = CurrentStepID;
                                    CPDetails.NextStepID = NextStepID;

                                    InsertWorkFlowHistory(CPDetails, smsCustomer, smsAssignee, Obj.IsVersionAccess, connString);
                                    //Added by DK - 18-Dec-14
                                    //IEnumerable<WF_WFField> FieldList = WorkFlowClient.WF_WFField.Where(i => i.WorkFlow_ID == WorkFlowID);
                                    foreach (var x in FieldList)
                                    {
                                        if (x.WorkFlowFieldName == "TotalOrderAmount")
                                        {
                                            HelpDeskServiceRequestServices.UpdateWorkFlowFieldValue(companyID, WorkFlowID, x.WFField_ID, headerRow.PurchaseOrder_ID, Convert.ToDecimal(headerRow.TotalOrderAmount).ToString(), connString);
                                        }
                                    }
                                    ForAutoAllocation(companyID, WorkFlowID, CurrentStepID, ActionID, headerRow.PurchaseOrder_ID, RoleID, Obj.User_ID, connString, LogException);
                                    //-------------------------
                                }
                            }
                            else
                            {
                                if (ActionID > 0)
                                {
                                    CaseProgressObjects CPDetails = new CaseProgressObjects();
                                    CPDetails.actionBy = Convert.ToInt32(Obj.User_ID);
                                    CPDetails.actionID = ActionID;
                                    CPDetails.actionRemarks = ActionRemarks;
                                    CPDetails.actionTime = DateTime.Now;
                                    CPDetails.addresseType = AddresseType;
                                    CPDetails.AssignTo = AssignedTo;
                                    CPDetails.CompanyID = companyID;
                                    CPDetails.currentStepID = CurrentStepID;
                                    CPDetails.customerEmailID = "";
                                    CPDetails.customerMobileNumber = "";
                                    CPDetails.emailBodyAddress = EmailtoAssigneeBody;
                                    CPDetails.emailBodyCustomer = "";
                                    CPDetails.emailSubAddressee = EmailSub;
                                    CPDetails.emailSubCustomer = "";
                                    CPDetails.NextStepID = NextStepID;
                                    CPDetails.receivedTime = DateTime.Now;
                                    CPDetails.RoleID = RoleID;
                                    CPDetails.smsTextAddressee = "";
                                    CPDetails.smsTextCustomer = "";
                                    CPDetails.transactionNumber = headerRow.PurchaseOrder_ID;
                                    //added by Kavitha to add CC and BCC -start
                                    CPDetails.AddresseCC = AddresseEmailCC;
                                    CPDetails.AddresseBcc = AddresseEmailBCC;
                                    CPDetails.CustomerBcc = custEmailBCC;
                                    CPDetails.customerCC = custEmailCC;
                                    //added by Kavitha to add CC and BCC -end
                                    CPDetails.workFlowID = WorkFlowID;
                                    SMSTemplate smsCustomer = new SMSTemplate();
                                    SMSTemplate smsAssignee = new SMSTemplate();
                                    InsertWorkFlowHistory(CPDetails, smsCustomer, smsAssignee, Obj.IsVersionAccess, connString);
                                    //Added by DK - 18-Dec-14 To Update Purchase Order Total Amount  in WFField Table and Autoallocate Method Call
                                    //IEnumerable<WF_WFField> FieldList = WorkFlowClient.WF_WFField.Where(i => i.WorkFlow_ID == WorkFlowID);
                                    foreach (var x in FieldList)
                                    {
                                        if (x.WorkFlowFieldName == "TotalOrderAmount")
                                        {
                                            HelpDeskServiceRequestServices.UpdateWorkFlowFieldValue(companyID, WorkFlowID, x.WFField_ID, headerRow.PurchaseOrder_ID, Convert.ToDecimal(headerRow.TotalOrderAmount).ToString(), connString);
                                        }
                                    }
                                    ForAutoAllocation(companyID, WorkFlowID, CurrentStepID, ActionID, headerRow.PurchaseOrder_ID, RoleID, Obj.User_ID, connString, LogException);
                                    //-------------------------
                                    bool isChildInvokeReqd = HelpDeskServiceRequestServices.CheckIsInvokeChildObject(companyID, WorkFlowID, CurrentStepID, ActionID, NextStepID, connString);

                                    if (isChildInvokeReqd)
                                    {
                                        int parentCompanyID = 0;
                                        int parentbranchID = 0;
                                        string getParentCompanyIDQuery = "SELECT ISNULL(Company_Parent_ID, 0) FROM GNM_Company WHERE Company_ID = @Company_ID";
                                        using (cmd = new SqlCommand(getParentCompanyIDQuery, conn))
                                        {
                                            cmd.Parameters.AddWithValue("@Company_ID", companyID);
                                            object result = cmd.ExecuteScalar();
                                            parentCompanyID = result != DBNull.Value ? Convert.ToInt32(result) : 0;
                                        }


                                        bool isOEM = false;
                                        if (parentCompanyID != 0)
                                        {
                                            string getParentBranchIDQuery = "SELECT Branch_ID FROM GNM_Branch WHERE Company_ID = @ParentCompany_ID AND Branch_HeadOffice = 1";
                                            using (cmd = new SqlCommand(getParentBranchIDQuery, conn))
                                            {
                                                cmd.Parameters.AddWithValue("@ParentCompany_ID", parentCompanyID);
                                                object result = cmd.ExecuteScalar();
                                                parentbranchID = result != DBNull.Value ? Convert.ToInt32(result) : 0;
                                            }
                                            string checkOEMQuery = "SELECT TOP 1 * FROM GNM_Party WHERE PartyType = 4 AND Relationship_Company_ID = @ParentCompany_ID AND Party_IsActive = 1 AND IsOEM = 1 AND Company_ID = @Company_ID";
                                            using (cmd = new SqlCommand(checkOEMQuery, conn))
                                            {
                                                cmd.Parameters.AddWithValue("@ParentCompany_ID", parentCompanyID);
                                                cmd.Parameters.AddWithValue("@Company_ID", companyID);
                                                using (reader = cmd.ExecuteReader())
                                                {
                                                    if (reader.Read())
                                                    {
                                                        if (reader["Party_ID"] != DBNull.Value && Convert.ToInt32(reader["Party_ID"]) == headerRow.Supplier_ID)
                                                        {
                                                            isOEM = true;
                                                        }
                                                    }
                                                }
                                            }
                                        }

                                        int createPartsOrder = Obj.createPartsOrder != null ? Convert.ToInt32(Obj.createPartsOrder) : 1;
                                        if (createPartsOrder == 0 && isOEM)
                                        {
                                            //Modified by: Kiran Date:21-10-2013 QA-Iteration-3 Changes Begin
                                            if (Common.CheckPreffixSuffix(connString, LogException, Convert.ToInt32(parentCompanyID), parentbranchID, "PRT_PartsOrder"))
                                            {
                                                string checkApprovedQuantityQuery = "SELECT COUNT(*) FROM PRT_PurchaseOrderPartsDetail WHERE PurchaseOrder_ID = @PurchaseOrder_ID AND ApprovedQuantity > 0";



                                                using (cmd = new SqlCommand(checkApprovedQuantityQuery, conn))
                                                {
                                                    cmd.Parameters.AddWithValue("@PurchaseOrder_ID", headerRow.PurchaseOrder_ID);
                                                    int approvedQuantityCount = Convert.ToInt32(cmd.ExecuteScalar());

                                                    if (approvedQuantityCount > 0)
                                                    {
                                                        CreateOrder(CompID, Obj.Branch, userid, headerRow.PurchaseOrder_ID, BranchName, CompanyName, connString);
                                                    }
                                                }

                                            }
                                            else
                                            {
                                                POPrefix = false;
                                                throw new Exception();
                                            }
                                            //Changes Ends                                   
                                        }
                                    }
                                }
                            }
                        }
                        jsonData = new
                        {
                            IsPSExists = true,
                            IsSuccess = true,
                            POPrefix,
                            PurchaseOrder_ID = headerRow.PurchaseOrder_ID,
                            PurchaseOrderNumber = headerRow.PurchaseOrderNumber
                        };
                    }
                }


                //scope.Complete();
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                jsonData = new
                {
                    IsPSExists = true,
                    IsSuccess = false,
                    POPrefix,
                    PurchaseOrder_ID = 0,
                    PurchaseOrderNumber = ""
                };
            }

            //}
            //gbl.InsertGPSDetails(companyIDGPS, branchIDGPS, useridGPS, ObjectIDGPS, PurchaseORDERIDDGPS, 0, 0, "Inserted PurchaseOrderNumber ", false, MenuIDGPS, LogginIDGPS, null);
            return new JsonResult(jsonData);
        }
        public static void InsertWorkFlowHistory(CaseProgressObjects CPDetails, SMSTemplate SMSCutomerObj, SMSTemplate SMSAssigneeObj, bool IsVersionAccess, string connString)
        {
            try
            {
                int stepTypeID = 0;
                string connectionString = connString;  // Define your connection string here
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    SqlTransaction transaction = conn.BeginTransaction();
                    SqlCommand cmd;
                    if (!IsVersionAccess)
                    {
                        cmd = new SqlCommand("SELECT TOP 1 WFCaseProgress_ID FROM GNM_WFCase_Progress WHERE Transaction_ID = @TransactionNumber AND WorkFlow_ID = @WorkFlowID ORDER BY WFCaseProgress_ID DESC", conn, transaction);
                        cmd.Parameters.AddWithValue("@TransactionNumber", CPDetails.transactionNumber);
                        cmd.Parameters.AddWithValue("@WorkFlowID", CPDetails.workFlowID);

                        object result = cmd.ExecuteScalar();
                        if (result != null)
                        {
                            int wfCaseProgressID = Convert.ToInt32(result);

                            cmd = new SqlCommand("SELECT WFStepType_ID FROM GNM_WFSteps WHERE WFSteps_ID = @NextStepID", conn, transaction);
                            cmd.Parameters.AddWithValue("@NextStepID", CPDetails.NextStepID);
                            stepTypeID = Convert.ToInt32(cmd.ExecuteScalar());

                            SqlCommand updateCmd = new SqlCommand("UPDATE GNM_WFCase_Progress SET Action_Chosen = @ActionID, Action_Remarks = @ActionRemarks, Actioned_By = @ActionBy, Action_Time = @ActionTime, WFNextStep_ID = @NextStepID WHERE WFCaseProgress_ID = @WFCaseProgressID", conn, transaction);
                            updateCmd.Parameters.AddWithValue("@ActionID", CPDetails.actionID);
                            updateCmd.Parameters.AddWithValue("@ActionRemarks", CPDetails.actionRemarks);
                            updateCmd.Parameters.AddWithValue("@ActionBy", CPDetails.actionBy);
                            updateCmd.Parameters.AddWithValue("@ActionTime", CPDetails.actionTime);
                            updateCmd.Parameters.AddWithValue("@NextStepID", CPDetails.NextStepID);
                            updateCmd.Parameters.AddWithValue("@WFCaseProgressID", wfCaseProgressID);
                            updateCmd.ExecuteNonQuery();
                        }
                        else
                        {
                            cmd = new SqlCommand("SELECT WFStepType_ID FROM GNM_WFSteps WHERE WFSteps_ID = @CurrentStepID", conn, transaction);
                            cmd.Parameters.AddWithValue("@CurrentStepID", CPDetails.currentStepID);
                            stepTypeID = Convert.ToInt32(cmd.ExecuteScalar());
                        }
                    }
                    else
                    {
                        cmd = new SqlCommand("SELECT WFStepType_ID FROM GNM_WFSteps WHERE WFSteps_ID = @CurrentStepID", conn, transaction);
                        cmd.Parameters.AddWithValue("@CurrentStepID", CPDetails.currentStepID);
                        stepTypeID = Convert.ToInt32(cmd.ExecuteScalar());
                    }

                    string stepTypeName = "";
                    cmd = new SqlCommand("SELECT WFStepType_Nm FROM GNM_WFStepType WHERE WFStepType_ID = @StepTypeID", conn, transaction);
                    cmd.Parameters.AddWithValue("@StepTypeID", stepTypeID);
                    stepTypeName = cmd.ExecuteScalar().ToString();

                    if (stepTypeName.ToLower() != "end")
                    {
                        SqlCommand insertCmd = new SqlCommand("INSERT INTO GNM_WFCase_Progress (WorkFlow_ID, Transaction_ID, WFSteps_ID, Addresse_ID, Addresse_Flag, Received_Time) VALUES (@WorkFlowID, @TransactionNumber, @NextStepID, @AssignTo, @AddresseType, @ReceivedTime)", conn, transaction);
                        insertCmd.Parameters.AddWithValue("@WorkFlowID", CPDetails.workFlowID);
                        insertCmd.Parameters.AddWithValue("@TransactionNumber", CPDetails.transactionNumber);
                        insertCmd.Parameters.AddWithValue("@NextStepID", CPDetails.NextStepID);
                        insertCmd.Parameters.AddWithValue("@AssignTo", CPDetails.AssignTo);
                        insertCmd.Parameters.AddWithValue("@AddresseType", CPDetails.addresseType);
                        insertCmd.Parameters.AddWithValue("@ReceivedTime", CPDetails.receivedTime);
                        insertCmd.ExecuteNonQuery();
                    }

                    // If NextStepID is different from currentStepID
                    if (CPDetails.currentStepID != CPDetails.NextStepID)
                    {
                        // If the step type is not "Static", process further
                        if (stepTypeName.ToLower() != "static")
                        {
                            cmd = new SqlCommand("SELECT * FROM GNM_WFStepLink WHERE Company_ID = @CompanyID AND WorkFlow_ID = @WorkFlowID AND FrmWFSteps_ID = @CurrentStepID AND WFAction_ID = @ActionID AND ToWFSteps_ID = @NextStepID", conn, transaction);
                            cmd.Parameters.AddWithValue("@CompanyID", CPDetails.CompanyID);
                            cmd.Parameters.AddWithValue("@WorkFlowID", CPDetails.workFlowID);
                            cmd.Parameters.AddWithValue("@CurrentStepID", CPDetails.currentStepID);
                            cmd.Parameters.AddWithValue("@ActionID", CPDetails.actionID);
                            cmd.Parameters.AddWithValue("@NextStepID", CPDetails.NextStepID);

                            SqlDataReader stepLinkReader = cmd.ExecuteReader();
                            if (stepLinkReader.HasRows)
                            {
                                stepLinkReader.Read();
                                bool isSMSSentToAddressee = Convert.ToBoolean(stepLinkReader["IsSMSSentToAddressee"]);
                                bool isSMSSentToCustomer = Convert.ToBoolean(stepLinkReader["IsSMSSentToCustomer"]);
                                bool isEmailSentToAddresse = Convert.ToBoolean(stepLinkReader["IsEmailSentToAddresse"]);
                                bool isEmailSentToCustomer = Convert.ToBoolean(stepLinkReader["IsEmailSentToCustomer"]);
                                stepLinkReader.Close();

                                // Send SMS to Addressee if applicable
                                if (isSMSSentToAddressee)
                                {
                                    cmd = new SqlCommand("SELECT Employee_ID FROM GNM_User WHERE User_ID = @AssignTo", conn, transaction);
                                    cmd.Parameters.AddWithValue("@AssignTo", CPDetails.AssignTo);
                                    int employeeID = Convert.ToInt32(cmd.ExecuteScalar());

                                    cmd = new SqlCommand("SELECT Company_Employee_MobileNumber FROM GNM_CompanyEmployee WHERE Company_Employee_ID = @EmployeeID", conn, transaction);
                                    cmd.Parameters.AddWithValue("@EmployeeID", employeeID);
                                    string mobileNumber = cmd.ExecuteScalar().ToString();

                                    if (!string.IsNullOrEmpty(mobileNumber))
                                    {
                                        // Insert into WF_Sms and send SMS
                                        SqlCommand insertSmsCmd = new SqlCommand("INSERT INTO GNM_Sms (Sms_Text, Sms_Queue_Date, Sms_Mobile_Number, Parameter1_value, Parameter2_value, Parameter3_value, Parameter4_value, Template_ID) VALUES (@SmsText, @SmsQueueDate, @SmsMobileNumber, @Param1, @Param2, @Param3, @Param4, @TemplateID)", conn, transaction);
                                        insertSmsCmd.Parameters.AddWithValue("@SmsText", CPDetails.smsTextAddressee);
                                        insertSmsCmd.Parameters.AddWithValue("@SmsQueueDate", DateTime.Now);
                                        insertSmsCmd.Parameters.AddWithValue("@SmsMobileNumber", mobileNumber);
                                        insertSmsCmd.Parameters.AddWithValue("@Param1", SMSAssigneeObj.Param1);
                                        insertSmsCmd.Parameters.AddWithValue("@Param2", SMSAssigneeObj.Param2);
                                        insertSmsCmd.Parameters.AddWithValue("@Param3", SMSAssigneeObj.Param3);
                                        insertSmsCmd.Parameters.AddWithValue("@Param4", SMSAssigneeObj.Param4);
                                        insertSmsCmd.Parameters.AddWithValue("@TemplateID", SMSAssigneeObj.Template_ID);
                                        insertSmsCmd.ExecuteNonQuery();

                                        SMSData SMSHeader = new SMSData
                                        {
                                            Message = "QUEST -" + CPDetails.smsTextAddressee.Replace("&", "%26").Replace("#", "%23"),
                                            MobileNumber = mobileNumber,
                                            TemplateID = SMSAssigneeObj.Template_ID
                                        };
                                        Common.SendSMS(SMSHeader);
                                    }
                                }

                                // Send SMS to Customer if applicable
                                if (isSMSSentToCustomer && !string.IsNullOrEmpty(CPDetails.customerMobileNumber))
                                {
                                    cmd = new SqlCommand("INSERT INTO GNM_Sms (Sms_Text, Sms_Queue_Date, Sms_Mobile_Number, Parameter1_value, Parameter2_value, Parameter3_value, Parameter4_value, Template_ID) VALUES (@SmsText, @SmsQueueDate, @SmsMobileNumber, @Param1, @Param2, @Param3, @Param4, @TemplateID)", conn, transaction);
                                    cmd.Parameters.AddWithValue("@SmsText", CPDetails.smsTextCustomer);
                                    cmd.Parameters.AddWithValue("@SmsQueueDate", DateTime.Now);
                                    cmd.Parameters.AddWithValue("@SmsMobileNumber", CPDetails.customerMobileNumber);
                                    cmd.Parameters.AddWithValue("@Param1", SMSCutomerObj.Param1);
                                    cmd.Parameters.AddWithValue("@Param2", SMSCutomerObj.Param2);
                                    cmd.Parameters.AddWithValue("@Param3", SMSCutomerObj.Param3);
                                    cmd.Parameters.AddWithValue("@Param4", SMSCutomerObj.Param4);
                                    cmd.Parameters.AddWithValue("@TemplateID", SMSCutomerObj.Template_ID);
                                    cmd.ExecuteNonQuery();

                                    SMSData SMSHeader = new SMSData
                                    {
                                        Message = "QUEST -" + CPDetails.smsTextCustomer.Replace("&", "%26").Replace("#", "%23"),
                                        MobileNumber = CPDetails.customerMobileNumber,
                                        TemplateID = SMSCutomerObj.Template_ID
                                    };
                                    Common.SendSMS(SMSHeader);
                                }

                                // Send Email to Addressee if applicable
                                if (isEmailSentToAddresse)
                                {
                                    cmd = new SqlCommand("SELECT Employee_ID FROM GNM_User WHERE User_ID = @AssignTo", conn, transaction);
                                    cmd.Parameters.AddWithValue("@AssignTo", CPDetails.AssignTo);
                                    int employeeID = Convert.ToInt32(cmd.ExecuteScalar());

                                    cmd = new SqlCommand("SELECT Company_Employee_Email FROM GNM_CompanyEmployee WHERE Company_Employee_ID = @EmployeeID", conn, transaction);
                                    cmd.Parameters.AddWithValue("@EmployeeID", employeeID);
                                    string emailID = cmd.ExecuteScalar().ToString();

                                    SqlCommand insertEmailCmd = new SqlCommand("INSERT INTO GNM_Email (Email_To, Email_Subject, Email_Body, Email_cc, Email_Bcc, Email_Queue_Date) VALUES (@EmailTo, @EmailSubject, @EmailBody, @EmailCC, @EmailBCC, @EmailQueueDate)", conn, transaction);
                                    insertEmailCmd.Parameters.AddWithValue("@EmailTo", emailID);
                                    insertEmailCmd.Parameters.AddWithValue("@EmailSubject", CPDetails.emailSubAddressee);
                                    insertEmailCmd.Parameters.AddWithValue("@EmailBody", CPDetails.emailBodyAddress);
                                    insertEmailCmd.Parameters.AddWithValue("@EmailCC", CPDetails.AddresseCC);
                                    insertEmailCmd.Parameters.AddWithValue("@EmailBCC", CPDetails.AddresseBcc);
                                    insertEmailCmd.Parameters.AddWithValue("@EmailQueueDate", DateTime.Now);
                                    insertEmailCmd.ExecuteNonQuery();
                                }

                                // Send Email to Customer if applicable
                                if (isEmailSentToCustomer)
                                {
                                    SqlCommand insertEmailCmd = new SqlCommand("INSERT INTO GNM_Email (Email_To, Email_Subject, Email_Body, Email_cc, Email_Bcc, Email_Queue_Date) VALUES (@EmailTo, @EmailSubject, @EmailBody, @EmailCC, @EmailBCC, @EmailQueueDate)", conn, transaction);
                                    insertEmailCmd.Parameters.AddWithValue("@EmailTo", CPDetails.customerEmailID);
                                    insertEmailCmd.Parameters.AddWithValue("@EmailSubject", CPDetails.emailSubCustomer);
                                    insertEmailCmd.Parameters.AddWithValue("@EmailBody", CPDetails.emailBodyCustomer);
                                    insertEmailCmd.Parameters.AddWithValue("@EmailCC", custEmailCC);
                                    insertEmailCmd.Parameters.AddWithValue("@EmailBCC", custEmailBCC);
                                    insertEmailCmd.Parameters.AddWithValue("@EmailQueueDate", DateTime.Now);
                                    insertEmailCmd.ExecuteNonQuery();
                                }
                            }
                            stepLinkReader.Close();
                        }
                    }

                    // Commit the transaction
                    transaction.Commit();
                }
            }
            catch (Exception ex)
            {

            }
        }
        private static int GetDefaultBinLocationID(SqlConnection connection, int branchID, int companyID, int warehouseID)
        {
            string getBinLocationQuery = "SELECT BinLocation_ID FROM GNM_BinLocation WHERE Branch_ID = @BranchID AND Company_ID = @CompanyID AND BinLocation_IsDefault = 1 AND BinLocation_IsActive = 1 AND WareHouse_ID = @WareHouseID";
            using (SqlCommand command = new SqlCommand(getBinLocationQuery, connection))
            {
                command.Parameters.AddWithValue("@BranchID", branchID);
                command.Parameters.AddWithValue("@CompanyID", companyID);
                command.Parameters.AddWithValue("@WareHouseID", warehouseID);
                return Convert.ToInt32(command.ExecuteScalar());
            }
        }

        public static void ForAutoAllocation(int CompanyID, int WorkFlowID, int CurrentStepID, int ActionID, int TransactionNumber, int RoleID, int UserID, string connectionString, int LogException, string UserCulture = "")
        {
            string SMStoAssigneeText = string.Empty;
            string SMStoCustomerText = string.Empty;
            string CustomerMobNo = string.Empty;
            string CustomerEmailID = string.Empty;
            string EmailSub = string.Empty;
            string EmailtoAssigneeBody = string.Empty;
            string EmailtoCustomerBody = string.Empty;
            try
            {
                SMSTemplate smsCustomer = new SMSTemplate();
                SMSTemplate smsAssignee = new SMSTemplate();
                int ID = 0;
                int Count = 0;
                int SID = 0;
                PRT_PurchaseOrder POH = null;
                CaseProgressObjects CaseProgressObj = new CaseProgressObjects();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();

                    // Fetch StepLink Object (Assuming AutoAllocate is implemented separately)
                    WF_WFStepLink StepLinkObj = AutoAllocate(CompanyID, WorkFlowID, CurrentStepID, ActionID, TransactionNumber, connectionString);

                    if (StepLinkObj != null)
                    {
                        SID = StepLinkObj.ToWFSteps_ID;

                        // Fetch WFStepStatus_ID
                        int x = 0;
                        string stepStatusQuery = "SELECT WFStepStatus_ID FROM WF_WFSteps WHERE WFSteps_ID = @SID";
                        using (SqlCommand cmd = new SqlCommand(stepStatusQuery, connection))
                        {
                            cmd.Parameters.AddWithValue("@SID", SID);
                            object result = cmd.ExecuteScalar();
                            if (result != null) x = Convert.ToInt32(result);
                        }

                        // Fetch Purchase Order
                        string poQuery = "SELECT * FROM PRT_PurchaseOrder WHERE PurchaseOrder_ID = @TransactionNumber";
                        using (SqlCommand cmd = new SqlCommand(poQuery, connection))
                        {
                            cmd.Parameters.AddWithValue("@TransactionNumber", TransactionNumber);
                            SqlDataReader reader = cmd.ExecuteReader();

                            if (reader.Read())
                            {
                                POH = new PRT_PurchaseOrder
                                {
                                    PurchaseOrder_ID = Convert.ToInt32(reader["PurchaseOrder_ID"]),
                                    PurchaseOrderStatus_ID = x
                                };
                            }
                            reader.Close();
                        }

                        // Update Purchase Order Status
                        string updatePOQuery = "UPDATE PRT_PurchaseOrder SET PurchaseOrderStatus_ID = @x WHERE PurchaseOrder_ID = @TransactionNumber";
                        using (SqlCommand cmd = new SqlCommand(updatePOQuery, connection))
                        {
                            cmd.Parameters.AddWithValue("@x", x);
                            cmd.Parameters.AddWithValue("@TransactionNumber", TransactionNumber);
                            cmd.ExecuteNonQuery();
                        }

                        // Fetch Addresse_WFRole_ID
                        string wfRoleQuery = @"SELECT Addresse_WFRole_ID FROM WF_WFStepLink 
                                       WHERE FrmWFSteps_ID = @StepLinkID 
                                       AND WorkFlow_ID = @WorkFlowID 
                                       AND Company_ID = @CompanyID";
                        using (SqlCommand cmd = new SqlCommand(wfRoleQuery, connection))
                        {
                            cmd.Parameters.AddWithValue("@StepLinkID", StepLinkObj.ToWFSteps_ID);
                            cmd.Parameters.AddWithValue("@WorkFlowID", WorkFlowID);
                            cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                            object result = cmd.ExecuteScalar();
                            if (result != null) ID = Convert.ToInt32(result);
                        }

                        // Count Role Users
                        string countQuery = @"SELECT COUNT(*) FROM GNM_WFRoleUser A
                                      INNER JOIN GNM_User B ON A.UserID = B.User_ID
                                      WHERE B.Company_ID = @CompanyID AND A.WFRole_ID = @ID";
                        using (SqlCommand cmd = new SqlCommand(countQuery, connection))
                        {
                            cmd.Parameters.AddWithValue("@CompanyID", StepLinkObj.Company_ID);
                            cmd.Parameters.AddWithValue("@ID", ID);
                            Count = Convert.ToInt32(cmd.ExecuteScalar());
                        }

                        // Populate CaseProgress object
                        CaseProgressObj.RoleID = RoleID;
                        CaseProgressObj.CompanyID = CompanyID;
                        CaseProgressObj.transactionNumber = TransactionNumber;
                        CaseProgressObj.workFlowID = WorkFlowID;
                        CaseProgressObj.currentStepID = StepLinkObj.ToWFSteps_ID;
                        CaseProgressObj.receivedTime = DateTime.Now;
                        CaseProgressObj.actionID = StepLinkObj.WFAction_ID;
                        CaseProgressObj.actionBy = UserID;
                        CaseProgressObj.AssignTo = ID;
                        CaseProgressObj.actionRemarks = "Auto Allocated ";
                        CaseProgressObj.addresseType = 0;
                        CaseProgressObj.actionTime = DateTime.Now;
                        CaseProgressObj.smsTextAddressee = SMStoAssigneeText;
                        CaseProgressObj.smsTextCustomer = SMStoCustomerText;
                        CaseProgressObj.customerMobileNumber = CustomerMobNo;
                        CaseProgressObj.customerEmailID = CustomerEmailID;
                        CaseProgressObj.emailSubAddressee = EmailSub;
                        CaseProgressObj.emailBodyAddress = EmailtoAssigneeBody;
                        CaseProgressObj.emailBodyCustomer = EmailtoCustomerBody;
                        CaseProgressObj.emailSubCustomer = EmailSub;
                        CaseProgressObj.NextStepID = StepLinkObj.ToWFSteps_ID;

                        // Insert into Workflow History
                        if (Count > 1 || Count == 0)
                        {
                            insertWorkFlowHistory(connectionString, CaseProgressObj, smsCustomer, smsAssignee);
                        }
                        else
                        {
                            // Fetch Single User
                            string singleUserQuery = @"SELECT TOP 1 B.User_ID FROM GNM_WFRoleUser A 
                                               INNER JOIN GNM_User B ON A.UserID = B.User_ID 
                                               WHERE B.Company_ID = @CompanyID AND A.WFRole_ID = @ID";
                            using (SqlCommand cmd = new SqlCommand(singleUserQuery, connection))
                            {
                                cmd.Parameters.AddWithValue("@CompanyID", StepLinkObj.Company_ID);
                                cmd.Parameters.AddWithValue("@ID", ID);
                                object result = cmd.ExecuteScalar();
                                if (result != null) ID = Convert.ToInt32(result);
                            }

                            CaseProgressObj.AssignTo = ID;
                            CaseProgressObj.addresseType = 1;
                            insertWorkFlowHistory(connectionString, CaseProgressObj, smsCustomer, smsAssignee);
                        }

                        // Recursive Call
                        ForAutoAllocation(CompanyID, WorkFlowID, StepLinkObj.FrmWFSteps_ID, StepLinkObj.WFAction_ID, TransactionNumber, Convert.ToInt32(StepLinkObj.Addresse_WFRole_ID), UserID, connectionString, LogException);
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

        }


        public static int CreateOrder(int Company_ID, int Branch, int User_ID, int purchaseOrderID, string Branch_Name, string Company_Name, string connectionString)
        {
            int status = 0;
            try
            {
                PartsOrderHeaderRow newRow = new PartsOrderHeaderRow();



                int loggedinCompany = Convert.ToInt32(Company_ID);
                int loggedinBranchID = Convert.ToInt32(Branch);
                int modifiedBy = Convert.ToInt32(User_ID);

                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();

                    // Fetch necessary IDs
                    int objectID = GetScalarValue(conn, "SELECT Object_ID FROM GNM_Object WHERE Object_Name = 'PRT_PartsOrder'");
                    int partsOrderTypeID = GetScalarValue(conn, "SELECT RefMasterDetail_ID FROM GNM_RefMasterDetail WHERE RefMasterDetail_Short_Name = 'Customer' AND RefMasterDetail_ID IN (SELECT RefMaster_ID FROM GNM_RefMaster WHERE RefMaster_Name = 'PARTSORDERTYPE')");
                    int partsOrderStatusID = GetScalarValue(conn, "SELECT RefMasterDetail_ID FROM GNM_RefMasterDetail WHERE RefMasterDetail_Short_Name = 'Created' AND RefMasterDetail_ID IN (SELECT RefMaster_ID FROM GNM_RefMaster WHERE RefMaster_Name = 'PARTSORDERSTATUS')");

                    // Fetch Purchase Order
                    PRT_PurchaseOrder purchaseOrderRow = GetPurchaseOrder(conn, purchaseOrderID);
                    if (purchaseOrderRow == null)
                        throw new Exception("Purchase Order not found");

                    int taxStructureID = GetTaxStructureID(loggedinCompany, purchaseOrderRow.TaxStructure_ID ?? 0, connectionString);
                    newRow = MapPurchaseOrderToPartsOrder(purchaseOrderRow, partsOrderStatusID, partsOrderTypeID, taxStructureID, modifiedBy, connectionString);

                    // Fetch Supplier Company ID
                    int supplierCompanyID = GetScalarValue(conn, $"SELECT Relationship_Company_ID FROM GNM_Party WHERE Party_ID = {purchaseOrderRow.Supplier_ID}");
                    int customerID = GetScalarValue(conn, $"SELECT Party_ID FROM GNM_Party WHERE Relationship_Company_ID = {loggedinCompany} AND Relationship_Branch_ID = {loggedinBranchID} AND Company_ID = {supplierCompanyID}");

                    if (customerID != 0)
                    {
                        newRow.Party_ID = customerID;
                        int companyID = GetScalarValue(conn, $"SELECT Company_ID FROM GNM_Party WHERE Party_ID = {customerID}");
                        int branchID = GetScalarValue(conn, $"SELECT Branch_ID FROM GNM_Branch WHERE Company_ID = {companyID} AND Branch_HeadOffice = 1");
                        int consigneeAddressID = GetScalarValue(conn, $"SELECT PartyAddress_ID FROM GNM_PartyAddress WHERE Party_ID = {customerID}");

                        int wareHouseID = GetScalarValue(conn, $"SELECT WareHouse_ID FROM GNM_WareHouseOrderClass WHERE OrderClass_ID = {purchaseOrderRow.PurchaseOrderClass_ID} AND Branch_ID = {branchID}");

                        newRow.WareHouse_ID = wareHouseID;
                        newRow.CustomerOrderClass_ID = purchaseOrderRow.PurchaseOrderClass_ID == null ? 0 : Convert.ToInt32(purchaseOrderRow.PurchaseOrderClass_ID);
                        newRow.Company_ID = companyID;
                        newRow.Branch_ID = branchID;
                        newRow.ConsigneeAddress_ID = consigneeAddressID;
                        newRow.InvoiceAddress_ID = consigneeAddressID;
                        newRow.POPartsRows = GetPartsOrderPartsDetail(conn, purchaseOrderID, taxStructureID, loggedinCompany, connectionString);

                        int poID = InsertPartsOrder(newRow, User_ID, Branch_Name, Company_Name, connectionString);
                        if (poID == 0)
                            throw new Exception("Failed to create Parts Order");

                        List<PRT_PartsOrderPartsDetails> partsList = GetPartsOrderDetails(conn, poID);
                        foreach (var part in partsList)
                        {
                            AllocationLogicServices.UpdatePendingPartsOrderQty(part.Parts_ID, part.OrderQuantity, wareHouseID, companyID, branchID, connectionString);
                        }
                    }
                    else
                    {
                        throw new Exception("Customer ID not found");
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return status;
        }
        private static int GetScalarValue(SqlConnection conn, string query)
        {
            using (SqlCommand cmd = new SqlCommand(query, conn))
            {
                object result = cmd.ExecuteScalar();
                return result != null ? Convert.ToInt32(result) : 0;
            }
        }
        private static PRT_PurchaseOrder GetPurchaseOrder(SqlConnection conn, int purchaseOrderID)
        {
            using (SqlCommand cmd = new SqlCommand("SELECT * FROM PRT_PurchaseOrder WHERE PurchaseOrder_ID = @purchaseOrderID", conn))
            {
                cmd.Parameters.AddWithValue("@purchaseOrderID", purchaseOrderID);
                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        return new PRT_PurchaseOrder
                        {
                            PurchaseOrder_ID = purchaseOrderID,
                            DiscountAmount = reader.GetDecimal(reader.GetOrdinal("DiscountAmount")),
                            DiscountedAmount = reader.GetDecimal(reader.GetOrdinal("DiscountedAmount")),
                            PaymentTerms = reader.GetString(reader.GetOrdinal("PaymentTerms")),
                            TotalTaxableAmount = reader.GetDecimal(reader.GetOrdinal("TotalTaxableAmount")),
                            TotalOrderAmount = reader.GetDecimal(reader.GetOrdinal("TotalOrderAmount")),
                            DiscountPercentage = reader.GetDecimal(reader.GetOrdinal("DiscountPercentage")),
                            PurchaseOrderClass_ID = reader.IsDBNull(reader.GetOrdinal("PurchaseOrderClass_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("PurchaseOrderClass_ID")),
                            Supplier_ID = reader.GetInt32(reader.GetOrdinal("Supplier_ID")),
                            TaxStructure_ID = reader.IsDBNull(reader.GetOrdinal("TaxStructure_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("TaxStructure_ID"))
                        };
                    }
                }
            }
            return null;
        }
        private static List<PartsOrderPartsDetailRow> GetPartsOrderPartsDetail(SqlConnection conn, int purchaseOrderID, int taxStructureID, int loggedinCompany, string connString)
        {
            List<PartsOrderPartsDetailRow> partsList = new List<PartsOrderPartsDetailRow>();

            // Query to fetch parts details from the PRT_PurchaseOrderPartsDetail table
            string query = "SELECT Parts_ID, ApprovedQuantity, SupplierPrice, TaxStructure_ID, DiscountedAmount, DiscountAmount, DiscountPercentage, Amount " +
                           "FROM PRT_PurchaseOrderPartsDetail WHERE PurchaseOrder_ID = @purchaseOrderID";

            using (SqlCommand cmd = new SqlCommand(query, conn))
            {
                cmd.Parameters.AddWithValue("@purchaseOrderID", purchaseOrderID);

                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        // Initialize a new PartsOrderPartsDetailRow object
                        PartsOrderPartsDetailRow obj = new PartsOrderPartsDetailRow();

                        // Check if ApprovedQuantity is greater than 0
                        if (reader.GetInt32(reader.GetOrdinal("ApprovedQuantity")) > 0)
                        {
                            obj.Parts_ID = reader.GetInt32(reader.GetOrdinal("Parts_ID"));
                            obj.OrderQuantity = reader.GetInt32(reader.GetOrdinal("ApprovedQuantity"));
                            obj.AcceptedQuantity = reader.GetInt32(reader.GetOrdinal("ApprovedQuantity"));
                            obj.Rate = reader.GetDecimal(reader.GetOrdinal("SupplierPrice"));

                            // Get the tax structure ID from the database, fallback to 0 if not found
                            int detailTaxStructureID = reader.IsDBNull(reader.GetOrdinal("TaxStructure_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("TaxStructure_ID"));

                            if (detailTaxStructureID != 0)
                            {
                                // Retrieve the corresponding Parent Tax Structure ID
                                taxStructureID = GetTaxStructureID(conn, loggedinCompany, detailTaxStructureID);

                                // Calculate tax amount based on tax structure
                                double baseAmount = Convert.ToDouble(reader.GetDecimal(reader.GetOrdinal("DiscountedAmount")));
                                decimal taxAmount = CalculateTax(taxStructureID, baseAmount, connString);

                                obj.TaxStructure_ID = taxStructureID;
                                obj.TaxAmount = taxAmount;
                            }

                            obj.DiscountAmount = reader.GetDecimal(reader.GetOrdinal("DiscountAmount"));
                            obj.DiscountPercentage = reader.GetDecimal(reader.GetOrdinal("DiscountPercentage"));
                            obj.Amount = reader.GetDecimal(reader.GetOrdinal("Amount"));

                            // Add to the parts list
                            partsList.Add(obj);
                        }
                    }
                }
            }

            return partsList;
        }

        // Helper method to fetch the parent Tax Structure ID based on the child Tax Structure ID
        private static int GetTaxStructureID(SqlConnection conn, int loggedinCompany, int detailTaxStructureID)
        {
            string query = "SELECT ParentTaxStructure_ID FROM GNM_TaxStructureMapping " +
                           "WHERE Company_ID = @Company_ID AND ChildTaxStructure_ID = @ChildTaxStructure_ID";

            using (SqlCommand cmd = new SqlCommand(query, conn))
            {
                cmd.Parameters.AddWithValue("@Company_ID", loggedinCompany);
                cmd.Parameters.AddWithValue("@ChildTaxStructure_ID", detailTaxStructureID);

                return (int)cmd.ExecuteScalar(); // Returns the ParentTaxStructure_ID, or 0 if no match
            }
        }
        public static int GetTaxStructureID(int companyID, int childTaxStructureID, string connectionString)
        {
            int parentTaxStructureID = 0;

            string query = @"
        SELECT ParentTaxStructure_ID 
        FROM GNM_TaxStructureMapping 
        WHERE Company_ID = @CompanyID 
        AND ChildTaxStructure_ID = @ChildTaxStructureID";

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    cmd.Parameters.AddWithValue("@CompanyID", companyID);
                    cmd.Parameters.AddWithValue("@ChildTaxStructureID", childTaxStructureID);

                    conn.Open();
                    var result = cmd.ExecuteScalar();
                    if (result != null && result != DBNull.Value)
                    {
                        parentTaxStructureID = Convert.ToInt32(result);
                    }
                }
            }

            return parentTaxStructureID;
        }
        private static List<PRT_PartsOrderPartsDetails> GetPartsOrderDetails(SqlConnection conn, int partsOrderID)
        {
            List<PRT_PartsOrderPartsDetails> partsList = new List<PRT_PartsOrderPartsDetails>();

            string query = "SELECT * FROM PRT_PartsOrderPartsDetails WHERE PartsOrder_ID = @PartsOrderID";
            using (SqlCommand cmd = new SqlCommand(query, conn))
            {
                cmd.Parameters.AddWithValue("@PartsOrderID", partsOrderID);

                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        PRT_PartsOrderPartsDetails part = new PRT_PartsOrderPartsDetails
                        {
                            PartsOrder_ID = reader.GetInt32(reader.GetOrdinal("PartsOrder_ID")),
                            Parts_ID = reader.GetInt32(reader.GetOrdinal("Parts_ID")),
                            OrderQuantity = reader.GetDecimal(reader.GetOrdinal("OrderQuantity")),
                            AcceptedQuantity = reader.GetDecimal(reader.GetOrdinal("AcceptedQuantity")),
                            Rate = reader.GetDecimal(reader.GetOrdinal("Rate")),
                            TaxStructure_ID = reader.GetInt32(reader.GetOrdinal("TaxStructure_ID")),
                            DiscountAmount = reader.GetDecimal(reader.GetOrdinal("DiscountAmount")),
                            DiscountPercentage = reader.GetDecimal(reader.GetOrdinal("DiscountPercentage")),
                            Amount = reader.GetDecimal(reader.GetOrdinal("Amount"))
                        };
                        partsList.Add(part);
                    }
                }
            }

            return partsList;
        }


        private static object GetDbValue<T>(T value)
        {
            return value == null || EqualityComparer<T>.Default.Equals(value, default(T)) ? (object)DBNull.Value : value;
        }

        public static PartsOrderHeaderRow MapPurchaseOrderToPartsOrder(
    PRT_PurchaseOrder purchaseOrderRow,
    int partsOrderStatusID,
    int partsOrderTypeID,
    int taxStructureID,
    int modifiedBy, string connString)
        {
            PartsOrderHeaderRow newRow = new PartsOrderHeaderRow
            {
                DiscountAmount = purchaseOrderRow.DiscountAmount,
                PartsOrderDate = DateTime.Now,
                PartsOrderStatus_ID = partsOrderStatusID,
                PartsOrderType_ID = partsOrderTypeID,
                PartsOrderVersion = 0,
                PaymentTerms = purchaseOrderRow.PaymentTerms,
                PurchaseOrder_ID = purchaseOrderRow.PurchaseOrder_ID,
                DiscountedAmount = purchaseOrderRow.DiscountedAmount,
                TotalTaxableAmount = purchaseOrderRow.TotalTaxableAmount,
                TotalAmount = purchaseOrderRow.TotalOrderAmount,
                DiscountPercentage = purchaseOrderRow.DiscountPercentage,
                EnableVersion = false,
                IsArchived = false,
                IsBackToBackOrderAllocation = null,
                ModifiedBy = modifiedBy,
                ModifiedDate = DateTime.Now,
                OrderBranch_ID = null,
                PartyReferenceDetail = ""
            };

            if (taxStructureID != 0)
            {
                double baseAmount = newRow.DiscountedAmount.HasValue ? Convert.ToDouble(newRow.DiscountedAmount) : 0;
                decimal taxAmount = CalculateTax(taxStructureID, baseAmount, connString);  // Ensure you implement this method
                newRow.TaxStructure_ID = taxStructureID;
                newRow.TaxAmount = taxAmount;
            }

            return newRow;
        }


        public static int InsertPartsOrder(PartsOrderHeaderRow poDetail, int User_ID, string Branch_Name, string Company_Name, string connectionString)
        {
            int PartsOrderID = 0;


            PRT_PartsOrder poHdr = new PRT_PartsOrder();


            List<ResultForAction> resultList = new List<ResultForAction>();
            PRT_PartsOrderPartsDetails poprtdtl = null;

            int userID = Convert.ToInt32(User_ID);
            JTokenReader jr = null;
            dynamic s = null;
            var jsonResult = s;
            var jsonStatus = s;

            SMSTemplate smsCustomer = new SMSTemplate();
            SMSTemplate smsAssignee = new SMSTemplate();

            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();
                    string insertPoHeaderQuery = "INSERT INTO PRT_PartsOrder " +
                "(Branch_ID, Company_ID, ConsigneeAddress_ID, CustomerOrderClass_ID, DiscountAmount, DiscountedAmount, DiscountPercentage, " +
                "InvoiceAddress_ID, IsBackToBackOrderAllocation, JobCard_ID, JobCardNumber, ModifiedBy, ModifiedDate, OrderBranch_ID, " +
                "OrderBranchConsignee_ID, PartsOrderDate, PartsOrderStatus_ID, PartsOrderType_ID, Party_ID, PartyReferenceDetail, " +
                "PaymentTerms, PurchaseOrder_ID, ServiceRequest_ID, StockTransferRequest_ID, StockTransferRequestNumber, TaxAmount, " +
                "TaxStructure_ID, TotalAmount, TotalTaxableAmount, WareHouse_ID) " +
                "OUTPUT INSERTED.PartsOrder_ID " +
                "VALUES (@Branch_ID, @Company_ID, @ConsigneeAddress_ID, @CustomerOrderClass_ID, @DiscountAmount, @DiscountedAmount, " +
                "@DiscountPercentage, @InvoiceAddress_ID, @IsBackToBackOrderAllocation, @JobCard_ID, @JobCardNumber, @ModifiedBy, " +
                "@ModifiedDate, @OrderBranch_ID, @OrderBranchConsignee_ID, @PartsOrderDate, @PartsOrderStatus_ID, @PartsOrderType_ID, " +
                "@Party_ID, @PartyReferenceDetail, @PaymentTerms, @PurchaseOrder_ID, @ServiceRequest_ID, @StockTransferRequest_ID, " +
                "@StockTransferRequestNumber, @TaxAmount, @TaxStructure_ID, @TotalAmount, @TotalTaxableAmount, @WareHouse_ID)";

                    using (SqlCommand cmd = new SqlCommand(insertPoHeaderQuery, conn))
                    {
                        // Add parameters
                        cmd.Parameters.AddWithValue("@Branch_ID", poDetail.Branch_ID);
                        cmd.Parameters.AddWithValue("@Company_ID", poDetail.Company_ID);
                        cmd.Parameters.AddWithValue("@ConsigneeAddress_ID", poDetail.ConsigneeAddress_ID);
                        cmd.Parameters.AddWithValue("@CustomerOrderClass_ID", poDetail.CustomerOrderClass_ID);
                        cmd.Parameters.AddWithValue("@DiscountAmount", poDetail.DiscountAmount);
                        cmd.Parameters.AddWithValue("@DiscountedAmount", poDetail.DiscountedAmount);
                        cmd.Parameters.AddWithValue("@DiscountPercentage", poDetail.DiscountPercentage);
                        cmd.Parameters.AddWithValue("@InvoiceAddress_ID", poDetail.InvoiceAddress_ID);
                        cmd.Parameters.AddWithValue("@IsBackToBackOrderAllocation", poDetail.IsBackToBackOrderAllocation);
                        cmd.Parameters.AddWithValue("@JobCard_ID", poDetail.JobCard_ID);
                        cmd.Parameters.AddWithValue("@JobCardNumber", poDetail.JobCardNumber);
                        cmd.Parameters.AddWithValue("@ModifiedBy", poDetail.ModifiedBy);
                        cmd.Parameters.AddWithValue("@ModifiedDate", poDetail.ModifiedDate);
                        cmd.Parameters.AddWithValue("@OrderBranch_ID", poDetail.OrderBranch_ID);
                        cmd.Parameters.AddWithValue("@OrderBranchConsignee_ID", poDetail.OrderBranchConsignee_ID);
                        cmd.Parameters.AddWithValue("@PartsOrderDate", poDetail.PartsOrderDate);
                        cmd.Parameters.AddWithValue("@PartsOrderStatus_ID", poDetail.PartsOrderStatus_ID);
                        cmd.Parameters.AddWithValue("@PartsOrderType_ID", poDetail.PartsOrderType_ID);
                        cmd.Parameters.AddWithValue("@Party_ID", poDetail.Party_ID);
                        cmd.Parameters.AddWithValue("@PartyReferenceDetail", poDetail.PartyReferenceDetail);
                        cmd.Parameters.AddWithValue("@PaymentTerms", poDetail.PaymentTerms);
                        cmd.Parameters.AddWithValue("@PurchaseOrder_ID", poDetail.PurchaseOrder_ID);
                        cmd.Parameters.AddWithValue("@ServiceRequest_ID", poDetail.ServiceRequest_ID);
                        cmd.Parameters.AddWithValue("@StockTransferRequest_ID", poDetail.StockTransferRequest_ID);
                        cmd.Parameters.AddWithValue("@StockTransferRequestNumber", poDetail.StockTransferRequestNumber);
                        cmd.Parameters.AddWithValue("@TaxAmount", poDetail.TaxAmount);
                        cmd.Parameters.AddWithValue("@TaxStructure_ID", poDetail.TaxStructure_ID);
                        cmd.Parameters.AddWithValue("@TotalAmount", poDetail.TotalAmount);
                        cmd.Parameters.AddWithValue("@TotalTaxableAmount", poDetail.TotalTaxableAmount);
                        cmd.Parameters.AddWithValue("@WareHouse_ID", poDetail.WareHouse_ID);

                        // Execute the command and get the inserted PartsOrder_ID
                        PartsOrderID = (int)cmd.ExecuteScalar();
                    }
                    //poHdr.InvoiceType_ID = poDetail.InvoiceType_ID.Value;

                    foreach (var prts in poDetail.POPartsRows)
                    {
                        string insertPoPartsDetailsQuery = "INSERT INTO PRT_PartsOrderPartsDetails " +
                            "(PartsOrder_ID, AcceptedQuantity, Amount, DiscountAmount, DiscountPercentage, FreeStock, OrderQuantity, Parts_ID, " +
                            "Rate, TaxAmount, TaxStructure_ID, MRP) " +
                            "VALUES (@PartsOrder_ID, @AcceptedQuantity, @Amount, @DiscountAmount, @DiscountPercentage, @FreeStock, @OrderQuantity, " +
                            "@Parts_ID, @Rate, @TaxAmount, @TaxStructure_ID, @MRP)";

                        using (SqlCommand cmdParts = new SqlCommand(insertPoPartsDetailsQuery, conn))
                        {
                            cmdParts.Parameters.AddWithValue("@PartsOrder_ID", PartsOrderID);
                            cmdParts.Parameters.AddWithValue("@AcceptedQuantity", prts.AcceptedQuantity);
                            cmdParts.Parameters.AddWithValue("@Amount", prts.Amount);
                            cmdParts.Parameters.AddWithValue("@DiscountAmount", prts.DiscountAmount);
                            cmdParts.Parameters.AddWithValue("@DiscountPercentage", prts.DiscountPercentage);
                            cmdParts.Parameters.AddWithValue("@FreeStock", prts.FreeStock);
                            cmdParts.Parameters.AddWithValue("@OrderQuantity", prts.OrderQuantity);
                            cmdParts.Parameters.AddWithValue("@Parts_ID", prts.Parts_ID);
                            cmdParts.Parameters.AddWithValue("@Rate", prts.Rate);
                            cmdParts.Parameters.AddWithValue("@TaxAmount", prts.TaxAmount);
                            cmdParts.Parameters.AddWithValue("@TaxStructure_ID", prts.TaxStructure_ID);
                            cmdParts.Parameters.AddWithValue("@MRP", prts.MRP);

                            cmdParts.ExecuteNonQuery();
                        }
                    }







                    foreach (var prts in poDetail.POPartsRows)
                    {
                        if (poDetail.WareHouse_ID != null && poDetail.WareHouse_ID != 0)
                        {
                            string updatePartsStockQuery = "UPDATE GNM_PartsStockDetail SET " +
                                "LastDemandDate = CASE WHEN FirstDemandDate IS NULL THEN @Now ELSE LastDemandDate END, " +
                                "FirstDemandDate = CASE WHEN FirstDemandDate IS NULL THEN @Now ELSE FirstDemandDate END " +
                                "WHERE Branch_ID = @Branch_ID AND Company_ID = @Company_ID AND Parts_ID = @Parts_ID AND WareHouse_ID = @WareHouse_ID";

                            using (SqlCommand cmdPartsStock = new SqlCommand(updatePartsStockQuery, conn))
                            {
                                cmdPartsStock.Parameters.AddWithValue("@Now", DateTime.Now);
                                cmdPartsStock.Parameters.AddWithValue("@Branch_ID", poDetail.Branch_ID);
                                cmdPartsStock.Parameters.AddWithValue("@Company_ID", poDetail.Company_ID);
                                cmdPartsStock.Parameters.AddWithValue("@Parts_ID", prts.Parts_ID);
                                cmdPartsStock.Parameters.AddWithValue("@WareHouse_ID", poDetail.WareHouse_ID);

                                cmdPartsStock.ExecuteNonQuery();
                            }
                        }
                    }

                    WF_WFStepLink stepLinkRow = HelpDeskServiceRequestServices.GetRoleOrIndividualForFirstStep(poDetail.Company_ID, 5, connectionString);
                    byte firstRoleorIndividual = stepLinkRow != null ? stepLinkRow.Addresse_Flag : Convert.ToByte(1);
                    int roleID = stepLinkRow != null ? stepLinkRow.Addresse_Flag == 0 ? Convert.ToInt32(stepLinkRow.Addresse_WFRole_ID) : userID : userID;

                    jsonStatus = GetStatusofWorkFlowInterface(0, 5, poDetail.Company_ID, connectionString);

                    int currentstepid = 0;
                    JObject jObject = JObject.Parse(jsonStatus.Data.ToString());
                    jr = new JTokenReader(jObject["CurrentStepID"]);
                    jr.Read();
                    currentstepid = Convert.ToInt32(jr.Value);


                    WF_WFStepLink stlink = null;
                    string stlinkQuery = "SELECT TOP 1 * FROM WF_WFStepLink WHERE Company_ID = @Company_ID AND FrmWFSteps_ID = @FrmWFSteps_ID AND WorkFlow_ID = @WorkFlow_ID";
                    using (SqlCommand cmd = new SqlCommand(stlinkQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@Company_ID", poDetail.Company_ID);
                        cmd.Parameters.AddWithValue("@FrmWFSteps_ID", currentstepid);
                        cmd.Parameters.AddWithValue("@WorkFlow_ID", 5);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                stlink = new WF_WFStepLink
                                {
                                    // Assuming WF_WFStepLink has properties Company_ID, FrmWFSteps_ID, WorkFlow_ID, etc.
                                    Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                    FrmWFSteps_ID = reader.GetInt32(reader.GetOrdinal("FrmWFSteps_ID")),
                                    WorkFlow_ID = reader.GetInt32(reader.GetOrdinal("WorkFlow_ID")),
                                    // Set other properties as needed
                                };
                            }
                        }
                    }
                    string EmailSub = string.Empty;
                    string EmailtoAssigneeBody = string.Empty;
                    string EmailtoCustomerBody = string.Empty;

                    string ObjectName = "Parts Order";
                    EmailSub = "[AftermarketERP - Auto Mail] – " + ObjectName + " :" + poDetail.PartsOrderNumber;
                    EmailtoAssigneeBody = Common.GetEmailBodyAddressee(ObjectName, poDetail.PartsOrderNumber, Common.isAssigned, Branch_Name, Company_Name);
                    EmailtoCustomerBody = Common.GetEmailBodyCustomer(ObjectName, poDetail.PartsOrderNumber, Common.isCreated, Branch_Name, Company_Name);
                    GNM_Party party = null;
                    string partyQuery = "SELECT TOP 1 * FROM GNM_Party WHERE Party_ID = @Party_ID";
                    using (SqlCommand cmd = new SqlCommand(partyQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@Party_ID", poDetail.Party_ID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                party = new GNM_Party
                                {
                                    // Assuming GNM_Party has properties Party_ID, Party_Email, Party_Mobile, etc.
                                    Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                    Party_Email = reader.GetString(reader.GetOrdinal("Party_Email")),
                                    Party_Mobile = reader.GetString(reader.GetOrdinal("Party_Mobile")),
                                    // Set other properties as needed
                                };
                            }
                        }
                    }

                    CaseProgressObjects CPDetails = new CaseProgressObjects();
                    CPDetails.actionBy = userID;
                    CPDetails.actionID = stlink.WFAction_ID;
                    CPDetails.actionRemarks = "";
                    CPDetails.actionTime = DateTime.Now;
                    CPDetails.addresseType = firstRoleorIndividual;
                    CPDetails.AssignTo = roleID;
                    CPDetails.CompanyID = poDetail.Company_ID;
                    CPDetails.currentStepID = currentstepid;
                    CPDetails.customerEmailID = (party != null) ? party.Party_Email : "";
                    CPDetails.customerMobileNumber = (party != null) ? party.Party_Mobile : "";
                    CPDetails.emailBodyAddress = EmailtoAssigneeBody;
                    CPDetails.emailBodyCustomer = EmailtoCustomerBody;
                    CPDetails.emailSubAddressee = EmailSub;
                    CPDetails.emailSubCustomer = EmailSub;
                    CPDetails.NextStepID = currentstepid;
                    CPDetails.receivedTime = DateTime.Now;
                    CPDetails.RoleID = roleID;
                    CPDetails.smsTextAddressee = "";
                    CPDetails.smsTextCustomer = "";
                    CPDetails.transactionNumber = poDetail.PartsOrder_ID;
                    CPDetails.workFlowID = 5;
                    insertWorkFlowHistory(connectionString, CPDetails, smsCustomer, smsAssignee);


                    if (stlink.WFAction_ID > 0)
                    {
                        if (stlink.Addresse_Flag == 1)
                        {
                            resultList.AddRange(WorkFlowCommon.GetListOfStaffForaRoleID(connectionString, poDetail.Company_ID, (int)stlink.Addresse_WFRole_ID, 0, currentstepid, stlink.WFAction_ID, 0));

                            if (resultList.Count == 1)
                            {
                                CPDetails.addresseType = 1;
                                CPDetails.AssignTo = resultList.ElementAt(0).ID;
                            }
                            else
                            {
                                CPDetails.addresseType = 0;
                                CPDetails.AssignTo = Convert.ToInt32(stlink.Addresse_WFRole_ID);
                            }

                        }
                        else
                        {
                            CPDetails.addresseType = 0;
                            CPDetails.AssignTo = Convert.ToInt32(stlink.Addresse_WFRole_ID);
                        }


                        CPDetails.currentStepID = currentstepid;
                        CPDetails.NextStepID = stlink.ToWFSteps_ID;
                        insertWorkFlowHistory(connectionString, CPDetails, smsCustomer, smsAssignee);
                    }


                    PartsOrderID = poDetail.PartsOrder_ID;
                }
            }
            catch (Exception ex)
            {
                PartsOrderID = 0;
            }
            return PartsOrderID;
        }

        public static IActionResult GetStatusofWorkFlowInterface(int transactionNumber, int workFlowID, int CompID, string connectionString)
        {
            object obj = null;
            dynamic val = obj;
            dynamic val2 = obj;
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();

                    // Query to get the latest WF_WFCase_Progress entry
                    string getCaseProgressQuery = @"
                SELECT TOP 1 * 
                FROM WF_WFCase_Progress 
                WHERE Transaction_ID = @Transaction_ID AND WorkFlow_ID = @WorkFlow_ID 
                ORDER BY WFCaseProgress_ID DESC";

                    SqlCommand cmdCaseProgress = new SqlCommand(getCaseProgressQuery, conn);
                    cmdCaseProgress.Parameters.AddWithValue("@Transaction_ID", transactionNumber);
                    cmdCaseProgress.Parameters.AddWithValue("@WorkFlow_ID", workFlowID);

                    SqlDataReader readerCaseProgress = cmdCaseProgress.ExecuteReader();
                    WF_WFCase_Progress gnmCaseProgress = null;
                    if (readerCaseProgress.Read())
                    {
                        gnmCaseProgress = new WF_WFCase_Progress
                        {
                            WFCaseProgress_ID = readerCaseProgress.GetInt32(readerCaseProgress.GetOrdinal("WFCaseProgress_ID")),
                            Transaction_ID = readerCaseProgress.GetInt32(readerCaseProgress.GetOrdinal("Transaction_ID")),
                            WorkFlow_ID = readerCaseProgress.GetInt32(readerCaseProgress.GetOrdinal("WorkFlow_ID")),
                            WFSteps_ID = readerCaseProgress.GetInt32(readerCaseProgress.GetOrdinal("WFSteps_ID"))
                        };
                    }
                    readerCaseProgress.Close();

                    // If there is a valid WF_WFCase_Progress, get the current step
                    if (gnmCaseProgress != null)
                    {
                        string getWFStepQuery = @"
                    SELECT WFSteps_ID, WFStep_Name 
                    FROM WF_WFSteps 
                    WHERE WFSteps_ID = @WFSteps_ID";

                        SqlCommand cmdWFStep = new SqlCommand(getWFStepQuery, conn);
                        cmdWFStep.Parameters.AddWithValue("@WFSteps_ID", gnmCaseProgress.WFSteps_ID);

                        SqlDataReader readerWFStep = cmdWFStep.ExecuteReader();
                        if (readerWFStep.Read())
                        {
                            val = "{CurrentStepID:" + readerWFStep.GetInt32(readerWFStep.GetOrdinal("WFSteps_ID")) +
                                  ",CurrentStepName:'" + readerWFStep.GetString(readerWFStep.GetOrdinal("WFStep_Name")) + "'}";
                        }
                        readerWFStep.Close();
                    }
                    else
                    {
                        // If no WF_WFCase_Progress, query the steps with "BEGIN" type and match with WF_WFStepLink
                        string getWFStepBeginQuery = @"
                    SELECT a.WFSteps_ID, a.WFStep_Name
                    FROM WF_WFSteps a
                    JOIN WF_WFStepType b ON a.WFStepType_ID = b.WFStepType_ID
                    WHERE b.WFStepType_Nm = 'BEGIN' AND a.WorkFlow_ID = @WorkFlow_ID";

                        SqlCommand cmdWFStepBegin = new SqlCommand(getWFStepBeginQuery, conn);
                        cmdWFStepBegin.Parameters.AddWithValue("@WorkFlow_ID", workFlowID);

                        SqlDataReader readerWFStepBegin = cmdWFStepBegin.ExecuteReader();
                        List<dynamic> wfStepsList = new List<dynamic>();
                        while (readerWFStepBegin.Read())
                        {
                            wfStepsList.Add(new
                            {
                                WFSteps_ID = readerWFStepBegin.GetInt32(readerWFStepBegin.GetOrdinal("WFSteps_ID")),
                                WFStep_Name = readerWFStepBegin.GetString(readerWFStepBegin.GetOrdinal("WFStep_Name"))
                            });
                        }
                        readerWFStepBegin.Close();

                        // Loop through the results and check for WF_WFStepLink
                        foreach (var item in wfStepsList)
                        {
                            string checkWFStepLinkQuery = @"
                        SELECT COUNT(*) 
                        FROM WF_WFStepLink 
                        WHERE FrmWFSteps_ID = @WFSteps_ID AND Company_ID = @Company_ID";

                            SqlCommand cmdCheckWFStepLink = new SqlCommand(checkWFStepLinkQuery, conn);
                            cmdCheckWFStepLink.Parameters.AddWithValue("@WFSteps_ID", item.WFSteps_ID);
                            cmdCheckWFStepLink.Parameters.AddWithValue("@Company_ID", CompID);

                            int count = (int)cmdCheckWFStepLink.ExecuteScalar();
                            if (count > 0)
                            {
                                val = "{CurrentStepID:" + item.WFSteps_ID + ",CurrentStepName:'" + item.WFStep_Name + "'}";
                                break;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle exception (optional)
            }

            return new JsonResult(val);
        }

        public static decimal CalculateTax(int taxStructureID, double baseAmount, string connString)
        {
            decimal totalTaxAmount = 0.00M;
            try
            {

                List<TaxOutput> listOutput = getTaxDetails(taxStructureID, baseAmount, connString);

                for (int i = 0; i < listOutput.Count; i++)
                {
                    totalTaxAmount += listOutput[i].TaxAmount;
                }
            }
            catch (Exception ex)
            {
            }
            return totalTaxAmount;
        }



        public static List<TaxOutput> getTaxDetails(int taxStructureID, double baseAmount, string connString)
        {


            DataTable dt = new DataTable();
            List<GNM_TaxStructureDtl> taxDetailList = new List<GNM_TaxStructureDtl>();
            List<Input> inputList = new List<Input>();
            List<TaxOutput> listOutput = new List<TaxOutput>();
            using (SqlConnection conn = new SqlConnection(connString))
            {
                conn.Open();
                string taxDetailQuery = "SELECT * FROM GNM_TaxStructureDtl WHERE TaxStructure_ID = @TaxStructure_ID";
                using (SqlCommand cmd = new SqlCommand(taxDetailQuery, conn))
                {
                    cmd.Parameters.AddWithValue("@TaxStructure_ID", taxStructureID);
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            Input rowObj = new Input();
                            rowObj.TaxStructureDtl_Formula = reader["TaxStructureDtl_FormulaValue"].ToString();
                            rowObj.TaxType_ID = Convert.ToInt32(reader["TaxType_ID"]);
                            rowObj.TaxType_IncludeBaseAmt = Convert.ToBoolean(reader["TaxType_IncludeBaseAmt"]);
                            rowObj.TaxType_Seq = Convert.ToInt32(reader["TaxType_Seq"]);
                            rowObj.TaxType_TaxPerc = Convert.ToDecimal(reader["TaxType_TaxPerc"]);
                            inputList.Add(rowObj);
                        }
                    }
                }


                foreach (var input in inputList)
                {
                    string taxTypeNameQuery = "SELECT RefMasterDetail_Name FROM GNM_RefMasterDetail WHERE RefMasterDetail_ID = @TaxType_ID";
                    using (SqlCommand cmd = new SqlCommand(taxTypeNameQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@TaxType_ID", input.TaxType_ID);
                        var result = cmd.ExecuteScalar();
                        input.TaxTypeName = result != DBNull.Value ? result.ToString() : null;
                    }
                }

                double taxAmount = 0.00;
                for (int i = 0; i < inputList.Count; i++)
                {
                    if (i == 0)
                    {
                        taxAmount = Convert.ToDouble((baseAmount * Convert.ToDouble(inputList[i].TaxType_TaxPerc)) / 100);
                        listOutput.Add(new TaxOutput()
                        {
                            SequenceNo = inputList[i].TaxType_Seq,
                            TaxTypeID = inputList[i].TaxType_ID,
                            DiscountPercentage = inputList[i].TaxType_TaxPerc,
                            TaxableAmount = Convert.ToDecimal(baseAmount),
                            TaxAmount = Convert.ToDecimal(taxAmount.ToString("0.00")),
                            TaxType = inputList[i].TaxTypeName,
                            taxStructureID = taxStructureID
                        });
                    }
                    else
                    {
                        string formula = inputList[i].TaxStructureDtl_Formula;
                        if (inputList[i].TaxType_IncludeBaseAmt)
                        {
                            if (formula != null && formula != string.Empty)
                            {
                                for (int j = 0; j < listOutput.Count; j++)
                                {
                                    string replacable = listOutput[j].TaxTypeID + listOutput[j].TaxType;
                                    formula = formula.Replace(replacable, listOutput[j].TaxAmount.ToString());
                                }
                            }
                            double totalBaseAmount = formula != null && formula != string.Empty ? Convert.ToDouble(dt.Compute(formula, "")) + baseAmount : baseAmount;
                            taxAmount = Convert.ToDouble((totalBaseAmount * Convert.ToDouble(inputList[i].TaxType_TaxPerc)) / 100);
                            listOutput.Add(new TaxOutput()
                            {
                                SequenceNo = inputList[i].TaxType_Seq,
                                TaxTypeID = inputList[i].TaxType_ID,
                                DiscountPercentage = inputList[i].TaxType_TaxPerc,
                                TaxableAmount = Convert.ToDecimal(totalBaseAmount),
                                TaxAmount = Convert.ToDecimal(taxAmount.ToString("0.00")),
                                TaxType = inputList[i].TaxTypeName,
                                taxStructureID = taxStructureID
                            });
                        }
                        else
                        {

                            if (listOutput.Count > 0)
                            {
                                if (formula != null && formula != string.Empty)
                                {
                                    for (int j = 0; j < listOutput.Count; j++)
                                    {
                                        string replacable = listOutput[j].TaxTypeID + listOutput[j].TaxType;
                                        formula = formula.Replace(replacable, listOutput[j].TaxAmount.ToString());
                                    }
                                }
                                taxAmount = formula != null && formula != string.Empty ? Convert.ToDouble((Convert.ToDouble(dt.Compute(formula, "")) * Convert.ToDouble(inputList[i].TaxType_TaxPerc)) / 100) : 0;
                                listOutput.Add(new TaxOutput()
                                {
                                    SequenceNo = inputList[i].TaxType_Seq,
                                    TaxTypeID = inputList[i].TaxType_ID,
                                    DiscountPercentage = inputList[i].TaxType_TaxPerc,
                                    //Modified by Ravi on 11-Jun-2014 Changes begin
                                    TaxableAmount = formula != null && formula != string.Empty ? Convert.ToDecimal(dt.Compute(formula, "")) : Convert.ToDecimal(baseAmount),
                                    //Changes ends
                                    TaxAmount = Convert.ToDecimal(taxAmount.ToString("0.00")),
                                    TaxType = inputList[i].TaxTypeName,
                                    taxStructureID = taxStructureID
                                });
                            }
                        }
                    }
                }
            }
            return listOutput;
        }
        public static async Task SendPurchaseOrderDataAsync(int companyID, int branchID, int PurchaseOrderID, string systemEnvironmentCode, string releaseID, string CreateCode, string sendercode, string BODID, string acknowledgecode, string connString)
        {
            using (HttpClient client = new HttpClient())
            {
                // Prepare SQL queries
                string purchaseOrderQuery = "SELECT * FROM PRT_PurchaseOrder WHERE PurchaseOrder_ID = @PurchaseOrderID";
                string branchQuery = "SELECT Branch_ShortName, Country_ID FROM GNM_Branch WHERE Company_ID = @CompanyID AND Branch_HeadOffice = 1";
                string countryQuery = "SELECT RefMasterDetail_Short_Name FROM GNM_RefMasterDetail WHERE RefMasterDetail_ID = @CountryID";
                string orderClassQuery = "SELECT OrderClass_Description FROM PRM_OrderClass WHERE OrderClass_ID = @OrderClass_ID";
                string partsQuery = "SELECT * FROM PRT_PurchaseOrderPartsDetail WHERE PurchaseOrder_ID = @PurchaseOrderID AND ApprovedQuantity > 0";
                string partDetailQuery = "SELECT Parts_PartsDescription, Parts_PartsNumber FROM GNM_Parts WHERE Parts_ID = @PartsID";

                // Open the connection and query the necessary data
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    await conn.OpenAsync();

                    // Get purchase order header details
                    SqlCommand cmd = new SqlCommand(purchaseOrderQuery, conn);
                    cmd.Parameters.AddWithValue("@PurchaseOrderID", PurchaseOrderID);
                    SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var headerRow = new Dictionary<string, object>();

                    if (reader.Read())
                    {
                        headerRow["PurchaseOrderNumber"] = reader["PurchaseOrderNumber"];
                        headerRow["PurchaseOrderDate"] = reader["PurchaseOrderDate"];
                        headerRow["Expected_Delivery_Date"] = reader["Expected_Delivery_Date"];
                        headerRow["PurchaseOrderClass_ID"] = reader["PurchaseOrderClass_ID"];
                    }
                    reader.Close();

                    // Get Branch details
                    cmd = new SqlCommand(branchQuery, conn);
                    cmd.Parameters.AddWithValue("@CompanyID", companyID);
                    reader = await cmd.ExecuteReaderAsync();
                    var branchRow = new Dictionary<string, object>();

                    if (reader.Read())
                    {
                        branchRow["DealerID"] = reader["Branch_ShortName"];
                        branchRow["CountryID"] = reader["Country_ID"];
                    }
                    reader.Close();

                    // Get Country details
                    cmd = new SqlCommand(countryQuery, conn);
                    cmd.Parameters.AddWithValue("@CountryID", branchRow["CountryID"]);
                    reader = await cmd.ExecuteReaderAsync();
                    var countryRow = new Dictionary<string, object>();

                    if (reader.Read())
                    {
                        countryRow["DealerCountryCode"] = reader["RefMasterDetail_Short_Name"];
                    }
                    reader.Close();

                    // Get Order Type description
                    cmd = new SqlCommand(orderClassQuery, conn);
                    cmd.Parameters.AddWithValue("@OrderClass_ID", headerRow["PurchaseOrderClass_ID"]);
                    reader = await cmd.ExecuteReaderAsync();
                    var orderClassRow = new Dictionary<string, object>();

                    if (reader.Read())
                    {
                        orderClassRow["SupplierOrderType"] = reader["OrderClass_Description"];
                    }
                    reader.Close();

                    // Get Parts details
                    cmd = new SqlCommand(partsQuery, conn);
                    cmd.Parameters.AddWithValue("@PurchaseOrderID", PurchaseOrderID);
                    reader = await cmd.ExecuteReaderAsync();
                    var partsList = new List<Dictionary<string, object>>();

                    while (reader.Read())
                    {
                        var partRow = new Dictionary<string, object>();
                        partRow["Parts_ID"] = reader["Parts_ID"];
                        partRow["ApprovedQuantity"] = reader["ApprovedQuantity"];
                        partsList.Add(partRow);
                    }
                    reader.Close();

                    // Create JSON structure for sending the data
                    var jsonPayload = new Dictionary<string, object>
            {
                { "releaseID", releaseID },
                { "systemEnvironmentCode", systemEnvironmentCode },
                { "languageCode", "it-IT" },
                { "ApplicationArea", new Dictionary<string, object>
                    {
                        { "Sender", new Dictionary<string, object>
                            {
                                { "CreatorNameCode", CreateCode },
                                { "SenderNameCode", sendercode },
                                { "DealerNumberID", branchRow["DealerID"] },
                                { "StoreNumber", branchRow["DealerID"] },
                                { "DealerCountryCode", countryRow["DealerCountryCode"] }
                            }
                        },
                        { "CreationDateTime", Convert.ToDateTime(headerRow["PurchaseOrderDate"]).ToString("yyyy-MM-ddTHH:mm") },
                        { "BODID", BODID },
                        { "Destination", new Dictionary<string, object>
                            {
                                { "DestinationNameCode", "CLAAS" },
                                { "DealerTargetCountry", "IN" }
                            }
                        }
                    }
                },
                { "ProcessPartsOrderDataArea", new Dictionary<string, object>
                    {
                        { "Process", new Dictionary<string, object> { { "acknowledgeCode", acknowledgecode } } },
                        { "PartsOrder", new Dictionary<string, object>
                            {
                                { "PartsOrderHeader", new Dictionary<string, object>
                                    {
                                        { "DocumentDateTime", Convert.ToDateTime(headerRow["PurchaseOrderDate"]).ToString("yyyy-MM-ddTHH:mm") },
                                        { "DocumentIdentificationGroup", new Dictionary<string, object>
                                            {
                                                { "DocumentIdentification", new Dictionary<string, object> { { "DocumentID", headerRow["PurchaseOrderNumber"] } } }
                                            }
                                        },
                                        { "OrderTypeCode", orderClassRow["SupplierOrderType"] },
                                        { "RequestedShipDate", Convert.ToDateTime(headerRow["Expected_Delivery_Date"]).ToString("yyyy-MM-dd") }
                                    }
                                },
                                { "PartsOrderLine", new List<Dictionary<string, object>>() }
                            }
                        }
                    }
                }
            };

                    // For each part, add details to JSON
                    foreach (var part in partsList)
                    {
                        int partsID = Convert.ToInt32(part["Parts_ID"]);
                        cmd = new SqlCommand(partDetailQuery, conn);
                        cmd.Parameters.AddWithValue("@PartsID", partsID);
                        reader = await cmd.ExecuteReaderAsync();
                        var partDetail = new Dictionary<string, object>();

                        if (reader.Read())
                        {
                            partDetail["ItemIdDescription"] = reader["Parts_PartsDescription"];
                            partDetail["ItemID"] = reader["Parts_PartsNumber"];
                        }
                        reader.Close();

                        var partsOrderLine = new Dictionary<string, object>
                {
                    { "LineNumberString", partsList.IndexOf(part) + 1 },
                    { "OrderQuantity", part["ApprovedQuantity"] },
                    { "ItemIdDescription", partDetail["ItemIdDescription"] },
                    { "ItemIdentificationGroup", new Dictionary<string, object>
                        {
                            { "ItemIdentification", new Dictionary<string, object> { { "ItemID", partDetail["ItemID"] } } }
                        }
                    }
                };

                        // Cast 'ProcessPartsOrderDataArea' to Dictionary<string, object>
                        var processPartsOrderDataArea = jsonPayload["ProcessPartsOrderDataArea"] as Dictionary<string, object>;
                        if (processPartsOrderDataArea != null)
                        {
                            // Cast 'PartsOrder' to Dictionary<string, object>
                            var partsOrder = processPartsOrderDataArea["PartsOrder"] as Dictionary<string, object>;
                            if (partsOrder != null)
                            {
                                // Safely cast 'PartsOrderLine' to List<Dictionary<string, object>>
                                var partsOrderLineList = partsOrder["PartsOrderLine"] as List<Dictionary<string, object>>;
                                if (partsOrderLineList != null)
                                {
                                    // Add the new parts order line to the list
                                    partsOrderLineList.Add(partsOrderLine);
                                }
                                else
                                {
                                    // If 'PartsOrderLine' doesn't exist, create it
                                    partsOrder["PartsOrderLine"] = new List<Dictionary<string, object>> { partsOrderLine };
                                }
                            }
                        }
                    }

                    // Convert the JSON structure to a string
                    string jsonString = JsonConvert.SerializeObject(jsonPayload, Newtonsoft.Json.Formatting.Indented);

                    // Send the JSON data through an HTTP request (POST)
                    var content = new StringContent(jsonString, Encoding.UTF8, "application/json");
                    HttpResponseMessage response = await client.PostAsync("your_endpoint_url", content);

                    if (response.IsSuccessStatusCode)
                    {
                        Console.WriteLine("Data sent successfully!");
                    }
                    else
                    {
                        Console.WriteLine("Error sending data: " + response.StatusCode);
                    }
                }
            }
        }


    }
    public class PurchaseOrderInterfaceList
    {
        public int CurrentStepID { get; set; }
        public int ActionID { get; set; }
        public int AssignedTo { get; set; }
        public byte AddresseType { get; set; }
        public string ClosingDescription { get; set; }
        public int Company_ID { get; set; }
        public int User_ID { get; set; }
        public int Branch { get; set; }
        public int ObjectID { get; set; }
        public int ReLoginView { get; set; }
        public string Company_Name { get; set; }
        public string Languagecode { get; set; }
        public bool IsVersionAccess { get; set; }

        public List<PartRow> PurchaseOrderPartsDelete { get; set; }
        public bool EnablePurchaseOrderInterface { get; set; }
        public string SystemEnvironmentCode { get; set; }
        public string releaseID { get; set; }
        public string CreateCode { get; set; }
        public string sendercode { get; set; }
        public string BODID { get; set; }
        public string acknowledgecode { get; set; }
        public int createPartsOrder { get; set; }


        public List<PRT_PurchaseOrder> PurchaseOrderData { get; set; }


    }
    public class PartRow
    {
        public int PartID { get; set; }
        public string PartDescription { get; set; }
        public int Quantity { get; set; }
        // Add any other fields that the partrow contains
    }

    public class Input
    {
        private Boolean taxType_IncludeBaseAmt;
        private int taxType_ID;
        private decimal taxType_TaxPerc;
        private string taxStructureDtl_Formula;
        private int taxType_Seq;
        private string taxTypeName;

        public string TaxTypeName
        {
            get { return taxTypeName; }
            set { taxTypeName = value; }
        }
        public int TaxType_Seq
        {
            get { return taxType_Seq; }
            set { taxType_Seq = value; }
        }

        public string TaxStructureDtl_Formula
        {
            get { return taxStructureDtl_Formula; }
            set { taxStructureDtl_Formula = value; }
        }
        public decimal TaxType_TaxPerc
        {
            get { return taxType_TaxPerc; }
            set { taxType_TaxPerc = value; }
        }
        public int TaxType_ID
        {
            get { return taxType_ID; }
            set { taxType_ID = value; }
        }
        public Boolean TaxType_IncludeBaseAmt
        {
            get { return taxType_IncludeBaseAmt; }
            set { taxType_IncludeBaseAmt = value; }
        }
    }
    public class TaxOutput
    {
        private int sequenceNo;
        private int taxTypeID;
        private decimal discountPercentage;
        private decimal taxableAmount;
        private decimal taxAmount;
        private string taxType;
        private int TaxStructureID;

        public string TaxType
        {
            get { return taxType; }
            set { taxType = value; }
        }
        public decimal TaxAmount
        {
            get { return taxAmount; }
            set { taxAmount = value; }
        }
        public decimal TaxableAmount
        {
            get { return taxableAmount; }
            set { taxableAmount = value; }
        }
        public decimal DiscountPercentage
        {
            get { return discountPercentage; }
            set { discountPercentage = value; }
        }
        public int TaxTypeID
        {
            get { return taxTypeID; }
            set { taxTypeID = value; }
        }
        public int SequenceNo
        {
            get { return sequenceNo; }
            set { sequenceNo = value; }
        }
        public int taxStructureID
        {
            get { return TaxStructureID; }
            set { TaxStructureID = value; }
        }
    }
    public class PartsOrderHeaderRow
    {
        public bool? IsDealer { get; set; }
        public int PartsOrder_ID { get; set; }

        public string PartsOrderNumber { get; set; }

        public int? PartsOrderVersion { get; set; }

        public int PartsOrderType_ID { get; set; }


        public int? ServiceRequest_ID { get; set; }

        public int CustomerOrderClass_ID { get; set; }

        public int? Party_ID { get; set; }

        public int? OrderBranch_ID { get; set; }

        public DateTime PartsOrderDate { get; set; }

        public string PartyReferenceDetail { get; set; }

        //  public int? InvoiceType_ID { get; set; }

        public int? ConsigneeAddress_ID { get; set; }

        public int? InvoiceAddress_ID { get; set; }

        public string PaymentTerms { get; set; }


        public bool? EnableVersion { get; set; }

        public bool? IsArchived { get; set; }

        public int? PartsOrderStatus_ID { get; set; }

        public decimal? TotalAmount { get; set; }


        public int? PurchaseOrder_ID { get; set; }

        public bool? IsBackToBackOrderAllocation { get; set; }

        public decimal? DiscountPercentage { get; set; }

        public decimal? DiscountAmount { get; set; }

        public decimal? DiscountedAmount { get; set; }

        public int? TaxStructure_ID { get; set; }

        public decimal? TotalTaxableAmount { get; set; }

        public decimal? TaxAmount { get; set; }

        public int Company_ID { get; set; }

        public int Branch_ID { get; set; }

        public int? FinancialYear { get; set; }

        public int? ModifiedBy { get; set; }

        public DateTime? ModifiedDate { get; set; }

        public int? DocumentNumber { get; set; }

        public int? JobCard_ID { get; set; }

        public string JobCardNumber { get; set; }

        public int? StockTransferRequest_ID { get; set; }

        public string StockTransferRequestNumber { get; set; }

        public int? OrderBranchConsignee_ID { get; set; }

        public int? WareHouse_ID { get; set; }
        public int? Quotation_ID { get; set; }
        public decimal? MRP { get; set; }
        public List<PartsOrderPartsDetailRow> POPartsRows { get; set; }
        public List<PartsOrderPartsAllocationDetailRow> POPartsAllocationRows { get; set; }

    }
    public class PartsOrderPartsAllocationDetailRow
    {

        public int PartsOrderAllocation_ID { get; set; }

        public int PartsOrderPartsDetail_ID { get; set; }

        public int PartsOrder_ID { get; set; }

        public int WareHouse_ID { get; set; }

        public decimal? AllocatedQuantity { get; set; }

        public decimal? PickedQuantity { get; set; }


        public decimal? InvoicedQuantity { get; set; }

        public decimal? BackOrderQuantity { get; set; }


        public decimal? NullPickQuantity { get; set; }

        public decimal? CanceledQuantity { get; set; }

        public decimal? IssuedQuantity { get; set; }

        public decimal? ReturnedQuantity { get; set; }

    }
    public class PartsOrderPartsDetailRow
    {
        public int Parts_ID { get; set; }
        public decimal? MRP { get; set; }
        public decimal? FreeStock { get; set; }
        public decimal OrderQuantity { get; set; }
        public decimal AcceptedQuantity { get; set; }
        public decimal Rate { get; set; }
        public int? TaxStructure_ID { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal DiscountPercentage { get; set; }
        public decimal Amount { get; set; }
    }

    public partial class PRT_SUPPLIERQUOTATION
    {
        public PRT_SUPPLIERQUOTATION()
        {
            this.PRT_SQPARTSDETAIL = new HashSet<PRT_SQPARTSDETAIL>();
        }

        public int SUPPLIERQUOTATION_ID { get; set; }
        public string SUPPLIERQUOTATIONNUMBER { get; set; }
        public System.DateTime SUPPLIERQUOTATIONDATE { get; set; }
        public int REQUESTFORQUOTATION_ID { get; set; }
        public int SUPPLIER_ID { get; set; }
        public Nullable<int> LEADTIME { get; set; }
        public Nullable<decimal> DISCOUNTPERCENTAGE { get; set; }
        public Nullable<decimal> DISCOUNTAMOUNT { get; set; }
        public Nullable<decimal> DISCOUNTEDAMOUNT { get; set; }
        public string TAXABLEOTHERCHARGES { get; set; }
        public Nullable<decimal> TAXABLEPERCENTAGE { get; set; }
        public Nullable<decimal> TAXABLEOTHERCHARGESAMOUNT { get; set; }
        public Nullable<decimal> TOTALTAXABLEAMOUNT { get; set; }
        public Nullable<int> TAXSTRUCTURE_ID { get; set; }
        public Nullable<decimal> TAXAMOUNT { get; set; }
        public string NONTAXABLEOTHERCHARGES { get; set; }
        public Nullable<decimal> NONTAXABLEPERCENTAGE { get; set; }
        public Nullable<decimal> NONTAXABLEOTHERCHARGESAMOUNT { get; set; }
        public Nullable<decimal> ROUNDOFFAMOUNT { get; set; }
        public decimal SUPPLIERQUOTATIONTOTALAMOUNT { get; set; }
        public string REMARKS { get; set; }
        public string TERMSANDCONDITION { get; set; }
        public string PAYMENTTERMS { get; set; }
        public Nullable<System.DateTime> VALIDITYOFFER { get; set; }
        public int COMPANY_ID { get; set; }
        public int BRANCH_ID { get; set; }
        public int CREATEDBY { get; set; }
        public System.DateTime CREATEDDATE { get; set; }
        public Nullable<int> DOCUMENTNUMBER { get; set; }
        public int STATUS_ID { get; set; }
        public Nullable<int> FINANCIALYEAR { get; set; }
        public Nullable<int> PURCHASEORDER_ID { get; set; }

        public virtual PRT_REQUESTFORQUOTATION PRT_REQUESTFORQUOTATION { get; set; }
        public virtual ICollection<PRT_SQPARTSDETAIL> PRT_SQPARTSDETAIL { get; set; }
    }
    public partial class PRT_SQPARTSDETAIL
    {
        public int SQPARTSDETAIL_ID { get; set; }
        public int SUPPLIERQUOTATION_ID { get; set; }
        public int PARTS_ID { get; set; }
        public decimal QUOTEDQUANTITY { get; set; }
        public decimal QUOTEDRATE { get; set; }
        public decimal NEGOTIATEDQUANTITY { get; set; }
        public decimal NEGOTIATEDRATE { get; set; }
        public Nullable<decimal> DISCOUNTPERCENTAGELINELEVEL { get; set; }
        public Nullable<decimal> DISCOUNTAMOUNTLINELEVEL { get; set; }
        public Nullable<decimal> DISCOUNTEDAMOUNTLINELEVEL { get; set; }
        public Nullable<int> TAXSTRUCTURE_ID { get; set; }
        public Nullable<decimal> TAXAMOUNT { get; set; }
        public decimal AMOUNT { get; set; }

        public virtual PRT_SUPPLIERQUOTATION PRT_SUPPLIERQUOTATION { get; set; }
    }
    public partial class PRT_REQUESTFORQUOTATION
    {
        public PRT_REQUESTFORQUOTATION()
        {
            this.PRT_RFQATTACHMENTDETAIL = new HashSet<PRT_RFQATTACHMENTDETAIL>();
            this.PRT_RFQPARTSDETAIL = new HashSet<PRT_RFQPARTSDETAIL>();
            this.PRT_RFQSUPPLIERDETAIL = new HashSet<PRT_RFQSUPPLIERDETAIL>();
            this.PRT_SUPPLIERQUOTATION = new HashSet<PRT_SUPPLIERQUOTATION>();
        }

        public int REQUESTFORQUOTATION_ID { get; set; }
        public string REQUESTFORQUOTATIONNUMBER { get; set; }
        public System.DateTime REQUESTFORQUOTATIONDATE { get; set; }
        public System.DateTime VALIDITY { get; set; }
        public System.DateTime EXPECTEDDELIVERYDATE { get; set; }
        public int TRANSPORTMODE_ID { get; set; }
        public int CONSIGNEEADDRESS_ID { get; set; }
        public bool ISCLOSED { get; set; }
        public string REMARKS { get; set; }
        public int COMPANY_ID { get; set; }
        public int BRANCH_ID { get; set; }
        public int CREATEDBY { get; set; }
        public System.DateTime CREATEDDATE { get; set; }
        public Nullable<int> DOCUMENTNUMBER { get; set; }
        public Nullable<int> FINANCIALYEAR { get; set; }

        public virtual ICollection<PRT_RFQATTACHMENTDETAIL> PRT_RFQATTACHMENTDETAIL { get; set; }
        public virtual ICollection<PRT_RFQPARTSDETAIL> PRT_RFQPARTSDETAIL { get; set; }
        public virtual ICollection<PRT_RFQSUPPLIERDETAIL> PRT_RFQSUPPLIERDETAIL { get; set; }
        public virtual ICollection<PRT_SUPPLIERQUOTATION> PRT_SUPPLIERQUOTATION { get; set; }
    }
    public partial class PRT_RFQSUPPLIERDETAIL
    {
        public int RFQSUPPLIERDETAIL_ID { get; set; }
        public int REQUESTFORQUOTATION_ID { get; set; }
        public int SUPPLIER_ID { get; set; }
        public Nullable<int> SUPPLIERQUOTATION_ID { get; set; }

        public virtual PRT_REQUESTFORQUOTATION PRT_REQUESTFORQUOTATION { get; set; }
    }
    public partial class PRT_RFQATTACHMENTDETAIL
    {
        public int RFQATTACHMENTDETAIL_ID { get; set; }
        public int REQUESTFORQUOTATION_ID { get; set; }
        public string FILNAME { get; set; }
        public string FILEDESCRIPTION { get; set; }
        public int DOCUMENTTYPE_ID { get; set; }
        public int UPLOADEDBY { get; set; }
        public System.DateTime UPLOADEDDATE { get; set; }

        public virtual PRT_REQUESTFORQUOTATION PRT_REQUESTFORQUOTATION { get; set; }
    }
    public partial class PRT_RFQPARTSDETAIL
    {
        public int RFQPARTSDETAIL_ID { get; set; }
        public int REQUESTFORQUOTATION_ID { get; set; }
        public int PARTS_ID { get; set; }
        public decimal QUANTITY { get; set; }
        public decimal APPROVEDQUANTITY { get; set; }
        public Nullable<bool> ISFULLYRECEIVED { get; set; }

        public virtual PRT_REQUESTFORQUOTATION PRT_REQUESTFORQUOTATION { get; set; }
    }
    public class WF_WFField
    {
        public int WFField_ID { get; set; }

        public int WorkFlow_ID { get; set; }

        public string WorkFlowFieldName { get; set; }

        public virtual ICollection<WF_WFFieldValue> GNM_WFFieldValue { get; set; }

        public virtual WF_WorkFlow GNM_WorkFlow { get; set; }

        public WF_WFField()
        {
            GNM_WFFieldValue = new HashSet<WF_WFFieldValue>();
        }
    }
    public partial class PRM_OrderClass
    {
        public PRM_OrderClass()
        {
            this.PRM_OrderClassLocale = new HashSet<PRM_OrderClassLocale>();
            this.PRM_OrderClassPartyLeadDisc = new HashSet<PRM_OrderClassPartyLeadDisc>();
        }

        public int OrderClass_ID { get; set; }
        public int Company_ID { get; set; }
        public int PartOrderType_ID { get; set; }
        public string OrderClass_Description { get; set; }
        public Nullable<int> OrderClass_LeadTime { get; set; }
        public Nullable<decimal> OrderClass_Discount { get; set; }
        public Nullable<bool> OrderClass_IsConsiderForDemand { get; set; }
        public Nullable<bool> OrderClass_IsProductValidate { get; set; }
        public bool OrderClass_Type { get; set; }
        public bool OrderClass_IsActive { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public Nullable<bool> IsInterfaceOrderClass { get; set; }

        public virtual ICollection<PRM_OrderClassLocale> PRM_OrderClassLocale { get; set; }
        public virtual ICollection<PRM_OrderClassPartyLeadDisc> PRM_OrderClassPartyLeadDisc { get; set; }
    }

    public partial class PRM_OrderClassLocale
    {
        public int OrderClassLocale_ID { get; set; }
        public int OrderClass_ID { get; set; }
        public string OrderClass_Description { get; set; }
        public int Language_ID { get; set; }

        public virtual PRM_OrderClass PRM_OrderClass { get; set; }
    }
    public partial class PRM_OrderClassPartyLeadDisc
    {
        public int OrderClassPartyLeadDisc_ID { get; set; }
        public int OrderClass_ID { get; set; }
        public Nullable<bool> IsDealer { get; set; }
        public Nullable<int> Party_ID { get; set; }
        public Nullable<int> OrderClass_LeadTime { get; set; }
        public Nullable<decimal> OrderClass_Discount { get; set; }
        public int Company_ID { get; set; }
        public Nullable<int> PriceType { get; set; }

        public virtual PRM_OrderClass PRM_OrderClass { get; set; }
    }
}
