//------------------------------------------------------------------------------
// <auto-generated>
//    This code was generated from a template.
//
//    Manual changes to this file may cause unexpected behavior in your application.
//    Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace WorkFlow.Models
{
    using System;
    using System.Collections.Generic;
    
    public partial class WF_CompanyEmployee
    {
        public WF_CompanyEmployee()
        {
            this.GNM_User = new HashSet<WF_User>();
            this.GNM_EmployeeBranch = new HashSet<WF_EmployeeBranch>();
        }
    
        public int Company_Employee_ID { get; set; }
        public string Employee_ID { get; set; }
        public string Company_Employee_Name { get; set; }
        public int Company_ID { get; set; }
        public int Country_ID { get; set; }
        public int State_ID { get; set; }
        public string Company_Employee_MobileNumber { get; set; }
        public string Company_Employee_Landline_Number { get; set; }
        public string Company_Employee_ZipCode { get; set; }
        public Nullable<System.DateTime> Company_Employee_ActiveFrom { get; set; }
        public Nullable<System.DateTime> Company_Employee_ValidateUpTo { get; set; }
        public bool Company_Employee_Active { get; set; }
        public Nullable<int> Company_Employee_Manager_ID { get; set; }
        public string Company_Employee_Address { get; set; }
        public string Company_Employee_Location { get; set; }
        public string Company_Employee_Email { get; set; }
        public int Company_Employee_Department_ID { get; set; }
        public int Company_Employee_Designation_ID { get; set; }
        public int ModifiedBy { get; set; }
        public Nullable<System.DateTime> ModifiedDate { get; set; }
        public Nullable<decimal> HourlyRate { get; set; }
        public Nullable<int> Region_ID { get; set; }
        public Nullable<bool> IsEligibleForOT { get; set; }
        public Nullable<byte> ExemptionHours { get; set; }
        public Nullable<bool> IsOnJob { get; set; }
        public Nullable<int> JobID { get; set; }
    
        public virtual WF_Company GNM_Company { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail1 { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail2 { get; set; }
        public virtual ICollection<WF_User> GNM_User { get; set; }
        public virtual ICollection<WF_EmployeeBranch> GNM_EmployeeBranch { get; set; }
        public virtual WF_RefMasterDetail GNM_RefMasterDetail3 { get; set; }
    }
}
