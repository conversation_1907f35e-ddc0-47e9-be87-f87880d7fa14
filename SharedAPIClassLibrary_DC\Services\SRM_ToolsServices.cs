﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
// using DocumentFormat.OpenXml.Spreadsheet;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class SRM_ToolsServices
    {
        int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));


        #region ::: Select Tools Landing Grid Uday Kumar J B 12-07-2024 :::
        /// <summary>
        /// To select Tools Landing Grid
        /// </summary>
        ///
        public IQueryable<ToolsMaster> getLandingGridData(string connstring, SelectToolsMasterList selectToolsMasterObj)
        {
            IQueryable<ToolsMaster> IQToolsMaster = null;
            try
            {
                int Company_ID = Convert.ToInt32(selectToolsMasterObj.Company_ID);
                int LanguageID = Convert.ToInt32(selectToolsMasterObj.Language_ID);

                using (SqlConnection conn = new SqlConnection(connstring))
                {
                    using (SqlCommand cmd = new SqlCommand("GetLandingGridData", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", Company_ID);
                        cmd.Parameters.AddWithValue("@LanguageID", LanguageID);

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            List<ToolsMaster> toolsMasterList = new List<ToolsMaster>();
                            string YesE = CommonFunctionalities.GetGlobalResourceObject(selectToolsMasterObj.GeneralCulture.ToString(), "Yes").ToString();
                            string NoE = CommonFunctionalities.GetGlobalResourceObject(selectToolsMasterObj.GeneralCulture.ToString(), "No").ToString();

                            while (reader.Read())
                            {
                                ToolsMaster tool = new ToolsMaster
                                {
                                    Tools_ID = reader.GetInt32(reader.GetOrdinal("Tools_ID")),
                                    Tools_Name = reader.IsDBNull(reader.GetOrdinal("Tools_Name")) ? string.Empty : reader.GetInt32(reader.GetOrdinal("Tools_Name")).ToString(),
                                    SerialNumber = reader.IsDBNull(reader.GetOrdinal("SerialNumber")) ? string.Empty : reader.GetString(reader.GetOrdinal("SerialNumber")),
                                    Tools_Value = reader.IsDBNull(reader.GetOrdinal("Tools_Value")) ? 0 : reader.GetDecimal(reader.GetOrdinal("Tools_Value")),
                                    Tools_Rate = reader.IsDBNull(reader.GetOrdinal("Tool_Rate")) ? string.Empty : reader.GetDecimal(reader.GetOrdinal("Tool_Rate")).ToString("0.00"),
                                    IsActive = reader.GetString(reader.GetOrdinal("IsActive")) == "Yes" ? YesE : NoE,
                                    Tools_ValueSort = reader.IsDBNull(reader.GetOrdinal("Tools_ValueSort")) ? "0.00" : reader.GetDecimal(reader.GetOrdinal("Tools_ValueSort")).ToString("0.00")
                                };
                                toolsMasterList.Add(tool);
                            }

                            IQToolsMaster = toolsMasterList.AsQueryable();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return IQToolsMaster;
        }


        public string Toolname(string connstring, string Toolid)
        {
            string Toolname = string.Empty;
            try
            {
                int ToolidRef = Convert.ToInt32(Toolid);

                using (SqlConnection conn = new SqlConnection(connstring))
                {
                    using (SqlCommand cmd = new SqlCommand("SELECT RefMasterDetail_Name FROM GNM_RefMasterDetail WHERE RefMasterDetail_ID = @ToolidRef", conn))
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.Parameters.AddWithValue("@ToolidRef", ToolidRef);

                        conn.Open();
                        object result = cmd.ExecuteScalar();
                        if (result != null)
                        {
                            Toolname = result.ToString();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                // You might also want to handle or log the exception here.
            }

            return Toolname;
        }

        #endregion


        #region ::: Select Tools Landing Grid Uday Kumar J B 12-07-2024  :::
        /// <summary>
        /// To select Tools Landing Grid
        /// </summary>
        /// 

        public static IActionResult Select(string connstring, SelectToolsMasterList selectToolsMasterObj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            var jsonData = default(dynamic);
            string AppPath = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int Company_ID = Convert.ToInt32(selectToolsMasterObj.Company_ID);
                int Count = 0;
                int Total = 0;

                // Fetch data using getLandingGridData method
                IQueryable<ToolsMaster> IQToolsMaster = new SRM_ToolsServices().getLandingGridData(connstring, selectToolsMasterObj);

                // Apply search filters if present
                if (_search)
                {
                    var filtersObj = JsonConvert.DeserializeObject<Filters>(filters);
                    if (filtersObj.rules.Count() > 0)
                        IQToolsMaster = IQToolsMaster.FilterSearch(filtersObj);
                }

                // Apply advance filters if present
                if (advnce)
                {
                    var advnceFilterObj = JsonConvert.DeserializeObject<AdvanceFilter>(advnceFilters);
                    IQToolsMaster = IQToolsMaster.AdvanceSearch(advnceFilterObj);
                }

                // Local function for dynamic ordering
                IQueryable<T> OrderByField<T>(IQueryable<T> q, string SortField, string AscDesc)
                {
                    var param = Expression.Parameter(typeof(T), "p");
                    var prop = Expression.Property(param, SortField);
                    var exp = Expression.Lambda(prop, param);
                    string method = AscDesc.ToLower() == "desc" ? "OrderByDescending" : "OrderBy";
                    Type[] types = new Type[] { q.ElementType, exp.Body.Type };
                    var mce = Expression.Call(typeof(Queryable), method, types, q.Expression, exp);
                    return q.Provider.CreateQuery<T>(mce);
                }

                // Sorting
                IQToolsMaster = OrderByField(IQToolsMaster, sidx, sord);

                // Pagination
                Count = IQToolsMaster.Count();
                Total = rows > 0 ? (int)Math.Ceiling((double)Count / rows) : 0;
                IQToolsMaster = IQToolsMaster.Skip((page - 1) * rows).Take(rows);

                // Convert to List for further manipulation
                List<ToolsMaster> toolsMasterList = IQToolsMaster.ToList();

                // Transform the data for the grid
                var gridData = (from a in toolsMasterList
                                select new
                                {
                                    ID = a.Tools_ID,
                                    edit = "<a title='View' href='#' id='" + a.Tools_ID + "' ToolsName='" + a.Tools_Name + "' key='" + a.Tools_ID + "' class='EditTools font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                    delete = "<input type='checkbox' key='" + a.Tools_ID + "' defaultchecked='' id='chk" + a.Tools_ID + "' class='DeleteTools'/>",
                                    Tools_Name = a.Tools_Name,
                                    SerialNumber = a.SerialNumber,
                                    Tools_Value = a.Tools_Value,
                                    Tools_Rate = a.Tools_Rate,
                                    IsActive = a.IsActive,
                                    Tools_ValueSort = a.Tools_ValueSort,
                                    Locale = "<a key='" + a.Tools_ID + "' Company_ID='" + a.Company_ID + "' src='" + AppPath + "/Content/local.png' class='ToolsLocale' alt='Localize' width='20' height='20' title='Localize'><i class='fa fa-globe'></i></a>",
                                }).ToList();

                // Create the final JSON object
                jsonData = new
                {
                    total = Total,
                    page = page,
                    rows = gridData,
                    records = Count,
                    filter = filters,
                    advanceFilter = advnceFilters,
                    selectToolsMasterObj.Language_ID,
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }

        #endregion


        #region ::: Select Particular Tools in edit mode in the Grid Uday Kumar J B 12-07-2024  :::
        /// <summary>
        /// To Select Particular Tools in edit mode in the Grid
        /// </summary> 
        /// 
        public static IActionResult SelectParticularTools(string connString, SelectParticularToolsList SelectParticularToolsobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                var Jsondata = default(dynamic);

                int companyId = 0;
                int branchID = 0;
                int userID = 0;
                int menuID = 0;

                // Retrieve session variables
                if (SelectParticularToolsobj.Company_ID != null && SelectParticularToolsobj.Branch != null && SelectParticularToolsobj.UserDetails != null && SelectParticularToolsobj.MenuID != null)
                {
                    companyId = Convert.ToInt32(SelectParticularToolsobj.Company_ID);
                    branchID = Convert.ToInt32(SelectParticularToolsobj.Branch);
                    var userDetails = SelectParticularToolsobj.UserDetails;
                    userID = userDetails[0].User_ID;
                    menuID = Convert.ToInt32(SelectParticularToolsobj.MenuID);
                }

                // Initialize SRM_Tools object
                SRM_Tools Tools = null;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Retrieve Tools details using stored procedure
                    using (SqlCommand cmd = new SqlCommand("GetToolsDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@ToolsID", SelectParticularToolsobj.toolsID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                // Map data to SRM_Tools object
                                Tools = new SRM_Tools
                                {
                                    Tools_ID = reader.GetInt32(reader.GetOrdinal("Tools_ID")),
                                    Tools_Name = reader.IsDBNull(reader.GetOrdinal("Tools_Name")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Tools_Name")),
                                    SerialNumber = reader.IsDBNull(reader.GetOrdinal("SerialNumber")) ? string.Empty : reader.GetString(reader.GetOrdinal("SerialNumber")),
                                    Tools_Value = reader.IsDBNull(reader.GetOrdinal("Tools_Value")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("Tools_Value")),
                                    IsActive = reader.IsDBNull(reader.GetOrdinal("IsActive")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("IsActive")),
                                    Tools_Remarks = reader.IsDBNull(reader.GetOrdinal("Tools_Remarks")) ? string.Empty : reader.GetString(reader.GetOrdinal("Tools_Remarks")),
                                    Tool_Rate = reader.IsDBNull(reader.GetOrdinal("Tool_Rate")) ? (decimal?)null : reader.GetDecimal(reader.GetOrdinal("Tool_Rate")),
                                };
                            }
                        }
                    }
                }

                // Retrieve calendar event count for the tool
                int calendarEventCount = 0;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Retrieve count using stored procedure
                    using (SqlCommand cmd = new SqlCommand("GetCalendarEventResourceCount", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@ToolsID", SelectParticularToolsobj.toolsID);
                        cmd.Parameters.AddWithValue("@ResourceTypeID", SelectParticularToolsobj.ResourceType_ID);

                        object result = cmd.ExecuteScalar();
                        if (result != null && result != DBNull.Value)
                        {
                            calendarEventCount = Convert.ToInt32(result);
                        }
                    }
                }

                // Log the action
                //gbl.InsertGPSDetails(companyId, branchID, userID, Common.GetObjectID("ToolsMaster"), SelectParticularToolsobj.toolsID, 0, 0, "Viewed " + Tools?.Tools_Name + "", false, menuID, DateTime.Now);

                // Prepare JSON result object
                Jsondata = new
                {
                    Tools_ID = Tools?.Tools_ID,
                    Tools_Name = Tools?.Tools_Name,
                    SerialNumber = Tools?.SerialNumber,
                    Tools_Value = Tools?.Tools_Value,
                    IsActive = Tools?.IsActive,
                    Tools_Remarks = Tools?.Tools_Remarks,
                    Tools_Rate = Tools?.Tool_Rate,
                    CalCount = calendarEventCount
                };

                return new JsonResult(Jsondata);
            }
            catch (Exception ex)
            {
                // Log exceptions if needed
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(null);
        }

        #endregion


        #region ::: Insert Tools Data Uday Kumar J B 12-07-2024 :::
        /// <summary>
        /// To Insert Tools Data
        /// </summary> 
        ///
        public static IActionResult Insert(string connString, InsertToolsList insertToolsobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int Company_ID = Convert.ToInt32(insertToolsobj.Company_ID);

                // Parse JSON data from request
                string jsonData = insertToolsobj.DataTools;
                JObject jObj = JObject.Parse(jsonData);

                // Extract values from JSON
                int Tools_Name = Convert.ToInt32(jObj["Tools_Name"]);
                string SerialNumber = Convert.ToString(jObj["SerialNumber"]);
                decimal Tools_Value = Convert.ToDecimal(jObj["Tools_Value"]);
                bool IsActive = Convert.ToString(jObj["IsActive"]).Equals("Checked", StringComparison.OrdinalIgnoreCase); // Convert "Checked" to true
                string Tools_Remarks = Convert.ToString(jObj["Tools_Remarks"]);
                decimal? Tools_Rate = null;
                if (jObj["Tools_Rate"] != null && !string.IsNullOrEmpty(jObj["Tools_Rate"].ToString()))
                {
                    Tools_Rate = Convert.ToDecimal(jObj["Tools_Rate"]);
                }

                // Encrypt/Decrypt as needed
                SerialNumber = Common.DecryptString(SerialNumber);
                Tools_Remarks = Common.DecryptString(Tools_Remarks);

                // Insert into database using ADO.NET
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("InsertTools", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Tools_Name", Tools_Name);
                        cmd.Parameters.AddWithValue("@SerialNumber", SerialNumber);
                        cmd.Parameters.AddWithValue("@Tools_Value", Tools_Value);
                        cmd.Parameters.AddWithValue("@IsActive", IsActive);
                        cmd.Parameters.AddWithValue("@Tools_Remarks", Tools_Remarks);
                        cmd.Parameters.AddWithValue("@Tools_Rate", Tools_Rate);
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);

                        SqlParameter outputParam = new SqlParameter("@InsertedToolsID", SqlDbType.Int);
                        outputParam.Direction = ParameterDirection.Output;
                        cmd.Parameters.Add(outputParam);

                        cmd.ExecuteNonQuery();

                        int insertedToolsID = Convert.ToInt32(outputParam.Value);

                        // Log insertion
                        DateTime loginDatetime = Convert.ToDateTime(insertToolsobj.LoggedINDateTime);
                        //gbl.InsertGPSDetails(Company_ID, Convert.ToInt32(insertToolsobj.Branch), Convert.ToInt32(insertToolsobj.User_ID), Common.GetObjectID("ToolsMaster"), insertedToolsID, 0, 0, "Inserted " + Tools_Name, false, Convert.ToInt32(insertToolsobj.MenuID), loginDatetime, null);

                        var result = new
                        {
                            Tools_ID = insertedToolsID
                        };

                        return new JsonResult(result);
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle exceptions
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(null);
            }
        }
        #endregion


        #region ::: Update Tools Data Uday Kumar J B 12-07-2024 :::
        /// <summary>
        /// To Update Tools Data
        /// </summary> 
        ///
        public static IActionResult Update(string connString, UpdateList Updateobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int Company_ID = Convert.ToInt32(Updateobj.Company_ID);
                string jsonData = Updateobj.DataTools;
                JObject jObj = JObject.Parse(jsonData);

                int Tools_ID = Convert.ToInt32(jObj["Tools_ID"]);
                int Tools_Name = Convert.ToInt32(jObj["Tools_Name"]);
                string SerialNumber = Convert.ToString(jObj["SerialNumber"]);
                decimal Tools_Value = Convert.ToDecimal(jObj["Tools_Value"]);
                bool IsActive = Convert.ToString(jObj["IsActive"]).Equals("Checked", StringComparison.OrdinalIgnoreCase); // Convert "Checked" to true
                string Tools_Remarks = Convert.ToString(jObj["Tools_Remarks"]);
                decimal? Tools_Rate = null;
                if (!string.IsNullOrEmpty(Convert.ToString(jObj["Tools_Rate"])))
                {
                    Tools_Rate = Convert.ToDecimal(jObj["Tools_Rate"]);
                }

                // Encrypt/Decrypt as needed
                SerialNumber = Common.DecryptString(SerialNumber);
                Tools_Remarks = Common.DecryptString(Tools_Remarks);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("UpdateTools", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Tools_ID", Tools_ID);
                        cmd.Parameters.AddWithValue("@Tools_Name", Tools_Name);
                        cmd.Parameters.AddWithValue("@SerialNumber", SerialNumber);
                        cmd.Parameters.AddWithValue("@Tools_Value", Tools_Value);
                        cmd.Parameters.AddWithValue("@IsActive", IsActive);
                        cmd.Parameters.AddWithValue("@Tools_Remarks", Tools_Remarks);
                        cmd.Parameters.AddWithValue("@Tools_Rate", Tools_Rate);
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);

                        cmd.ExecuteNonQuery();

                        // Log update operation
                        DateTime loginDatetime = Convert.ToDateTime(Updateobj.LoggedINDateTime);
                        //gbl.InsertGPSDetails(Company_ID, Convert.ToInt32(Updateobj.Branch), Convert.ToInt32(Updateobj.User_ID), Common.GetObjectID("ToolsMaster"), Tools_ID, 0, 0, "Updated " + Tools_Name, false, Convert.ToInt32(Updateobj.MenuID), loginDatetime, null);
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                // Handle exceptions
            }
            return new JsonResult(null);
        }
        #endregion


        #region ::: Delete Tools Data Uday Kumar J B 12-07-2024  :::
        /// <summary>
        /// To Delete Tools Data
        /// </summary> 
        ///

        public static IActionResult Delete(string connString, DeleteListb Deleteobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string toolsMessage = "";
            try
            {
                JObject jObj = JObject.Parse(Deleteobj.Key);
                int count = jObj["rows"].Count();

                for (int i = 0; i < count; i++)
                {
                    JTokenReader jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jTR.Read();
                    int id = Convert.ToInt32(jTR.Value);

                    // Call the stored procedure to delete the tool
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        conn.Open();
                        using (SqlCommand cmd = new SqlCommand("DeleteTools", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Tools_ID", id);
                            cmd.Parameters.AddWithValue("@Company_ID", Convert.ToInt32(Deleteobj.Company_ID));

                            SqlParameter outputParam = new SqlParameter("@ToolsMessage", SqlDbType.NVarChar, -1);
                            outputParam.Direction = ParameterDirection.Output;
                            cmd.Parameters.Add(outputParam);

                            cmd.ExecuteNonQuery();

                            // Retrieve the output message
                            toolsMessage += Convert.ToString(cmd.Parameters["@ToolsMessage"].Value);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

                // Handle specific exceptions, if needed
            }
            return new JsonResult(toolsMessage);
        }
        #endregion


        #region ::: Export Tools Data Uday Kumar J B 12-07-2024 pending :::
        /// <summary>
        /// To Export Tools Data
        /// </summary> 
        ///
        //public void Export(int exprtType)
        //{
        //    try
        //    {
        //        int companyid = Convert.ToInt32(Session["Company_ID"]);
        //        DataTable ToolsDataTable = new DataTable();
        //        ToolsDataTable.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "Name").ToString());
        //        ToolsDataTable.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "serialNumber").ToString());
        //        ToolsDataTable.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "Value").ToString());
        //        ToolsDataTable.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "ToolRate").ToString());
        //        ToolsDataTable.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "Active").ToString());

        //        DataTable DtAlignment = new DataTable();
        //        DtAlignment.Columns.Add("Name");
        //        DtAlignment.Columns.Add("serialNumber");
        //        DtAlignment.Columns.Add("Value");
        //        DtAlignment.Columns.Add("ToolRate");
        //        DtAlignment.Columns.Add("Active");
        //        DtAlignment.Rows.Add(0, 0, 2, 2, 0);
        //        //-----------------------------------------------------------------------------------------------
        //        IQueryable<ToolsMaster> iToolsArray = null;
        //        int LanguageID = Convert.ToInt32(Request.Params["LanguageID"].ToString());
        //        //iToolsArray = (IQueryable<ToolsMaster>)Session["IQToolsMaster"];
        //        iToolsArray = getLandingGridData(LanguageID);

        //        if (Request.Params["filter"].ToString() != "null")
        //        {
        //            Filters filters = JObject.Parse(Common.DecryptString(Request.Params["filter"])).ToObject<Filters>();
        //            if (filters.rules.Count() > 0)
        //            {
        //                iToolsArray = iToolsArray.FilterSearch<ToolsMaster>(filters);
        //            }


        //        }
        //        if (Request.Params["advanceFilter"].ToString() != "null")
        //        {
        //            AdvanceFilter advnfilter = JObject.Parse(advanceFilter).ToObject<AdvanceFilter>();

        //            if (advnfilter.rules.Count() > 0)
        //            {
        //                iToolsArray = iToolsArray.AdvanceSearch<ToolsMaster>(advnfilter);
        //            }
        //        }

        //        iToolsArray = iToolsArray.OrderByField<ToolsMaster>(Request.Params["sidx"].ToString(), Request.Params["sord"].ToString());


        //        //---------------------------------------------------------------------------------------------------
        //        int Count = iToolsArray.Count();
        //        for (int i = 0; i < Count; i++)
        //        {
        //            ToolsDataTable.Rows.Add(iToolsArray.ElementAt(i).Tools_Name, iToolsArray.ElementAt(i).SerialNumber, iToolsArray.ElementAt(i).Tools_Value, iToolsArray.ElementAt(i).Tools_Rate, iToolsArray.ElementAt(i).IsActive);
        //        }
        //        DocumentExport.Export(exprtType, ToolsDataTable, DtAlignment, "Tools", HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "Tools").ToString());
        //    }
        //    catch (WebException wex)
        //    {
        //        LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
        //    }
        //    catch (Exception ex)
        //    {
        //        if (LogException == 1)
        //        {
        //            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
        //        }
        //    }
        //}
        #endregion


        #region ::: Load Tool Grid Locale Uday Kumar J B 12-07-2024  :::
        /// <summary>
        /// To LoadGridLocale
        /// </summary> 
        ///
        public static IActionResult LoadGridLocale(string connString, LoadGridLocaleToolMasterList LoadGridLocaleToolMasterobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int count = 0;
            int total = 0;
            string AppPath = string.Empty;
            var jsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                GNM_User User = LoadGridLocaleToolMasterobj.UserDetails.FirstOrDefault();
                int companyId = Convert.ToInt32(LoadGridLocaleToolMasterobj.Company_ID);

                // Fetch Tools data using ADO.NET
                DataTable toolsDataTable = new DataTable();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("LoadToolsGridData", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", companyId);
                        cmd.Parameters.AddWithValue("@SortColumn", sidx);
                        cmd.Parameters.AddWithValue("@SortOrder", sord);

                        SqlDataAdapter adapter = new SqlDataAdapter(cmd);
                        adapter.Fill(toolsDataTable);
                    }
                }

                // Calculate pagination
                count = toolsDataTable.Rows.Count;
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                // Prepare JSON data for jqGrid
                jsonData = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = (from DataRow row in toolsDataTable.Rows
                            select new
                            {
                                view = "<img id='" + row["Tools_ID"] + "' ToolsName='" + row["Tools_Name"] + "' src='" + AppPath + "/Content/plus.gif' key='" + row["Tools_ID"] + "' class='ViewToolsLocale'/>",
                                ID = Convert.ToInt32(row["Tools_ID"]),
                                Tools_Name = row["Tools_Name"].ToString(),
                                SerialNumber = row["SerialNumber"].ToString(),
                                Tools_Value = Convert.ToDecimal(row["Tools_Value"]),
                                IsActive = (Convert.ToBoolean(row["IsActive"]) ? "Yes" : "No")
                            }).ToList().Paginate(page, rows)
                };

                return new JsonResult(jsonData);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(null);
            }
        }
        #endregion


        #region ::: Select Single Tools Globe image in the Grid Uday Kumar J B 12-07-2024  :::
        /// <summary>
        /// To select the Single Tools when click on Globe image in the Grid
        /// </summary>  
        /// 
        public static IActionResult SelectSingleTools(string connString, SelectSingleToolsList SelectSingleToolsobj)
        {
            var tools = new List<object>();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("GetToolsDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@ToolsID", SelectSingleToolsobj.id);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                // Map data to C# object or anonymous type
                                var tool = new
                                {
                                    Tools_ID = reader.GetInt32(reader.GetOrdinal("Tools_ID")),
                                    Tools_Name = reader.IsDBNull(reader.GetOrdinal("Tools_Name")) ? null : reader.GetString(reader.GetOrdinal("Tools_Name")),
                                    SerialNumber = reader.GetString(reader.GetOrdinal("SerialNumber")),
                                    Tools_Value = reader.GetDecimal(reader.GetOrdinal("Tools_Value")),
                                    IsActive = reader.GetBoolean(reader.GetOrdinal("IsActive")),
                                    Tools_Remarks = reader.GetString(reader.GetOrdinal("Tools_Remarks")),
                                    Tools_Rate = reader.IsDBNull(reader.GetOrdinal("Tools_Rate")) ? "" : reader.GetDecimal(reader.GetOrdinal("Tools_Rate")).ToString("0.00")
                                };

                                tools.Add(tool);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle exceptions here
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            // Serialize the result to JSON and return it
            return new JsonResult(Newtonsoft.Json.JsonConvert.SerializeObject(tools));
        }
        #endregion


        #region ::: Select Single Tools Locale Uday Kumar J B 12-07-2024 :::
        /// <summary>
        ///  To select the Single Tools Locale when click on Globe image 
        /// </summary>  
        /// 

        public static IActionResult SelectSingleToolsLocale(string connString, SelectSingleToolsLocaleList SelectSingleToolsLocaleobj)
        {

            var service = new object(); // Change type as necessary
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                List<SRM_Tools> liTools = new List<SRM_Tools>();
                List<SRM_ToolsLocale> liToolsLocale = new List<SRM_ToolsLocale>();

                // Retrieve SRM_Tools data using stored procedure
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("GetToolsDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@ToolsID", SelectSingleToolsLocaleobj.id);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                SRM_Tools tool = new SRM_Tools
                                {
                                    Tools_ID = reader.GetInt32(reader.GetOrdinal("Tools_ID")),
                                    Tools_Name = reader.IsDBNull(reader.GetOrdinal("Tools_Name")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Tools_Name")),
                                    SerialNumber = reader.GetString(reader.GetOrdinal("SerialNumber")),
                                    Tools_Value = reader.GetDecimal(reader.GetOrdinal("Tools_Value")),
                                    IsActive = reader.GetBoolean(reader.GetOrdinal("IsActive")),
                                    Tool_Rate = reader.IsDBNull(reader.GetOrdinal("Tool_Rate")) ? null : (decimal?)reader.GetDecimal(reader.GetOrdinal("Tool_Rate"))
                                };

                                liTools.Add(tool);
                            }
                        }
                    }
                }

                // Retrieve SRM_ToolsLocale data using stored procedure
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("GetToolsLocaleDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@ToolsID", SelectSingleToolsLocaleobj.id);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                SRM_ToolsLocale toolLocale = new SRM_ToolsLocale
                                {
                                    Tools_ID = reader.GetInt32(reader.GetOrdinal("Tools_ID")),
                                    Tools_Name = reader.IsDBNull(reader.GetOrdinal("Tools_Name")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Tools_Name")),
                                    Tools_Remarks = reader.IsDBNull(reader.GetOrdinal("Tools_Remarks")) ? string.Empty : reader.GetString(reader.GetOrdinal("Tools_Remarks"))
                                };

                                liToolsLocale.Add(toolLocale);
                            }
                        }
                    }
                }

                // Determine Service based on data availability
                if (liToolsLocale.Count == 0)
                {
                    service = liTools.Select(a => new
                    {
                        Tools_ID = a.Tools_ID,
                        Tools_Name = "",
                        SerialNumber = a.SerialNumber,
                        Tools_Value = Convert.ToDecimal(a.Tools_Value),
                        IsActive = a.IsActive,
                        Tools_Remarks = "",
                        Tools_Rate = a.Tool_Rate == null ? "" : Convert.ToDecimal(a.Tool_Rate).ToString("0.00")
                    });
                }
                else
                {
                    service = from a in liTools
                              join b in liToolsLocale on a.Tools_ID equals b.Tools_ID
                              select new
                              {
                                  Tools_ID = a.Tools_ID,
                                  Tools_Name = b.Tools_Name,
                                  SerialNumber = a.SerialNumber,
                                  Tools_Value = Convert.ToDecimal(a.Tools_Value),
                                  IsActive = a.IsActive,
                                  Tools_Remarks = b.Tools_Remarks,
                                  Tools_Rate = a.Tool_Rate == null ? "" : Convert.ToDecimal(a.Tool_Rate).ToString("0.00")
                              };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Newtonsoft.Json.JsonConvert.SerializeObject(service));
        }
        #endregion


        #region ::: Insert Tools Locale  Uday Kumar J B 12-07-2024  :::
        /// <summary>
        ///  Method to Insert Tools Locale
        /// </summary> 
        /// 

        public static IActionResult InsertToolsLocale(string connString, InsertToolsLocaleList InsertToolsLocaleobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Parse JSON data
                    JObject jObj = JObject.Parse(InsertToolsLocaleobj.Key);

                    int ToolsID = Convert.ToInt32(jObj["ToolsID"].ToString());
                    string Name = jObj["Name"].ToString();
                    string Remarks = Common.DecryptString(jObj["Remarks"].ToString());
                    int LanguageID = Convert.ToInt32(InsertToolsLocaleobj.UserLanguageID);

                    // Call stored procedure
                    using (SqlCommand cmd = new SqlCommand("InsertOrUpdateToolsLocale", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@ToolsID", ToolsID);
                        cmd.Parameters.AddWithValue("@Name", Name);
                        cmd.Parameters.AddWithValue("@Remarks", Remarks);
                        cmd.Parameters.AddWithValue("@LanguageID", LanguageID);

                        cmd.ExecuteNonQuery();
                    }

                    // Optionally log successful operation or any other post-operation logic
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(null);
        }
        #endregion


        #region ::: Select ReferenceMaster  Uday Kumar J B 12-07-2024  :::
        /// <summary>
        ///  To SelectReferenceMaster
        /// </summary> 
        /// 
        public static IActionResult SelectReferenceMaster(string connString, SelectReferenceMasterListb SelectReferenceMasterobj)
        {
            var masterData = new List<object>();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int companyID = Convert.ToInt32(SelectReferenceMasterobj.Company_ID);
                int languageID = Convert.ToInt32(SelectReferenceMasterobj.UserLanguageID);

                DataTable dataTable = new DataTable();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("SelectReferenceMasterData", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);
                        cmd.Parameters.AddWithValue("@LanguageID", languageID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            dataTable.Load(reader);
                        }
                    }
                }

                // Convert DataTable to List of anonymous objects
                masterData = (from DataRow row in dataTable.Rows
                              select new
                              {
                                  ID = Convert.ToInt32(row["ID"]),
                                  Name = row["Name"].ToString()
                              }).ToList<object>(); // Convert to List<object>

                return new JsonResult(new { ReferenceMasterData = masterData });
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(null);
            }
        }
        #endregion


        #region ::: Check Duplicate Tools Name And Serial Number  Uday Kumar J B 12-07-2024 :::
        /// <summary>
        /// to check duplicate Tools Name and Serial number
        /// </summary>
        /// 
        public static IActionResult CheckToolsNameAndSerialNumber(string connString, CheckToolsNameAndSerialNumberList CheckToolsNameAndSerialNumberobj)
        {
            int status = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int Company_ID = Convert.ToInt32(CheckToolsNameAndSerialNumberobj.Company_ID);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("CheckToolsNameAndSerialNumber", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Tools_Name", CheckToolsNameAndSerialNumberobj.value);
                        cmd.Parameters.AddWithValue("@SerialNumber", Common.DecryptString(CheckToolsNameAndSerialNumberobj.serialNumber));
                        cmd.Parameters.AddWithValue("@PrimaryKey", CheckToolsNameAndSerialNumberobj.primaryKey);
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);

                        SqlParameter statusParam = new SqlParameter("@Status", SqlDbType.Int);
                        statusParam.Direction = ParameterDirection.Output;
                        cmd.Parameters.Add(statusParam);

                        cmd.ExecuteNonQuery();

                        status = Convert.ToInt32(cmd.Parameters["@Status"].Value);
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                return new JsonResult(null);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(null);
            }
            return new JsonResult(status);
        }
        #endregion


        #region ::: Display Events For User when click on calender image Uday Kumar J B 12-07-2024 :::
        /// <summary>
        /// To display Events For User when click on calender image
        /// </summary>
        /// <returns>...</returns>
        /// 
        public static ActionResult SelEventsForUser(string connString, SelEventsForUserList SelEventsForUserobj)
        {
            var jsonEvents = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Execute the stored procedure
                    using (SqlCommand cmd = new SqlCommand("SelEventsForUser", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@UserID", SelEventsForUserobj.userID);
                        cmd.Parameters.AddWithValue("@ResourceType_ID", SelEventsForUserobj.ResourceType_ID);

                        SqlDataAdapter adapter = new SqlDataAdapter(cmd);
                        DataTable dt = new DataTable();
                        adapter.Fill(dt);

                        // Convert DataTable to dynamic object (similar to LINQ query)
                        jsonEvents = dt.AsEnumerable().Select(row => new
                        {
                            Event_Name = row.Field<string>("Event_Name"),
                            Event_Location = row.Field<string>("Event_Location"),
                            startDate = row.Field<DateTime>("startDate").ToShortDateString(),
                            startTime = row.Field<DateTime>("startTime").ToShortTimeString(),
                            endDate = row.Field<DateTime>("endDate").ToShortDateString(),
                            endTime = row.Field<DateTime>("endTime").ToShortTimeString(),
                            Event_ID = row.Field<int>("Event_ID"),
                            allDay = row.Field<int>("allDay") == 1,
                            Closed = row.Field<int>("Closed") == 1,
                            JobNumber = row.Field<string>("JobNumber"),
                            JobID = row.Field<int>("JobID"),
                            EmpID = row.Field<int>("EmpID"),
                            ResourceType_ID = row.Field<int>("ResourceType_ID")
                        }).ToList();
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonEvents);
        }
        #endregion


        #region ::: SRM_Tools Obj and List Created Classes Uday Kumar J B 12-07-2024  :::
        /// <summary>
        /// To Obj and List Created Classes
        /// </summary>
        /// 
        public class SelEventsForUserList
        {
            public int userID { get; set; }
            public int ResourceType_ID { get; set; }

        }
        public class CheckToolsNameAndSerialNumberList
        {
            public int value { get; set; }
            public int primaryKey { get; set; }
            public string serialNumber { get; set; }

            public int Company_ID { get; set; }


        }

        public class SelectReferenceMasterListb
        {
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
        }

        public class InsertToolsLocaleList
        {
            public int UserLanguageID { get; set; }

            public string Key { get; set; }
        }
        public class SelectSingleToolsLocaleList
        {
            public int id { get; set; }
        }
        public class SelectSingleToolsList
        {
            public int id { get; set; }
        }
        public class LoadGridLocaleToolMasterList
        {
            public List<GNM_User> UserDetails { get; set; }
            public int Company_ID { get; set; }

        }
        public class DeleteListb
        {
            public int Company_ID { get; set; }

            public string Key { get; set; }
        }

        public class UpdateList
        {
            public int Company_ID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }

            public string DataTools { get; set; }

        }

        public class InsertToolsList
        {
            public int Company_ID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }

            public string DataTools { get; set; }

        }
        public class SelectParticularToolsList
        {
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int MenuID { get; set; }

            public int toolsID { get; set; }

            public int ResourceType_ID { get; set; }

            public List<GNM_User> UserDetails { get; set; }

        }

        #endregion


        #region ::: Tools Master class Uday Kumar J B 12-07-2024  :::
        /// <summary>
        /// To Tools Master Classes
        /// </summary>
        /// 
        public class ToolsMaster
        {
            public int Tools_ID { get; set; }
            public int Company_ID { get; set; }
            public string Tools_Name { get; set; }
            public string IsActive { get; set; }
            public string SerialNumber { get; set; }
            public decimal Tools_Value { get; set; }
            public string Tools_ValueSort { get; set; }


            public string Tools_Rate { get; set; }

            public string Tools_Remarks { get; set; }
            public int ToolsLocale_ID { get; set; }
        }
        #endregion


        #region ::: SelectToolsMasterList Uday Kumar J B 12-07-2024 :::
        /// <summary>
        /// To SelectToolsMasterList
        /// </summary>
        /// 
        public class SelectToolsMasterList
        {
            public int Language_ID { get; set; }
            public int Company_ID { get; set; }
            public string sidx { get; set; }

            public int page { get; set; }
            public string rows { get; set; }

            public string GeneralCulture { get; set; }


        }
        #endregion


        #region ::: SRM_Tools Classes Uday Kumar J B 12-07-2024  :::
        /// <summary>
        /// To SRM_Tools Classes
        /// </summary>
        /// 
        public partial class SRM_Tools
        {
            public SRM_Tools()
            {
                this.SRM_ToolsLocale = new HashSet<SRM_ToolsLocale>();
            }

            public int Tools_ID { get; set; }
            public Nullable<int> Company_ID { get; set; }
            public Nullable<int> Tools_Name { get; set; }
            public string SerialNumber { get; set; }
            public Nullable<decimal> Tools_Value { get; set; }
            public Nullable<bool> IsActive { get; set; }
            public string Tools_Remarks { get; set; }
            public Nullable<decimal> Tool_Rate { get; set; }
            public Nullable<int> Branch_ID { get; set; }

            public virtual ICollection<SRM_ToolsLocale> SRM_ToolsLocale { get; set; }
        }
        public partial class SRM_ToolsLocale
        {
            public int ToolsLocale_ID { get; set; }
            public int Tools_ID { get; set; }
            public Nullable<int> Tools_Name { get; set; }
            public Nullable<int> Language_ID { get; set; }
            public string Tools_Remarks { get; set; }

            public virtual SRM_Tools SRM_Tools { get; set; }
        }

        public partial class GNM_User
        {
            public GNM_User()
            {
                this.GNM_ATTACHMENTDETAIL = new HashSet<GNM_ATTACHMENTDETAIL>();
                this.GNM_GPSLOG = new HashSet<GNM_GPSLOG>();
                this.GNM_MODELATTACHMENTDETAIL = new HashSet<GNM_MODELATTACHMENTDETAIL>();
                this.GNM_REF_ATTACHMENTDETAIL = new HashSet<GNM_REF_ATTACHMENTDETAIL>();
                this.GNM_UserCompany = new HashSet<GNM_UserCompany>();
                this.GNM_UserLocale = new HashSet<GNM_UserLocale>();
                this.GNM_UserRole = new HashSet<GNM_UserRole>();
                this.GNM_WALKAROUNDATTACHMENTDETAIL = new HashSet<GNM_WALKAROUNDATTACHMENTDETAIL>();
                this.GNM_TAMSAdjustmentLog = new HashSet<GNM_TAMSAdjustmentLog>();
                this.GNM_RegilarizeWindowCloseLog = new HashSet<GNM_RegilarizeWindowCloseLog>();
                this.GNM_DashboardConfiguration = new HashSet<GNM_DashboardConfiguration>();
            }

            public int User_ID { get; set; }
            public string User_Name { get; set; }
            public string User_LoginID { get; set; }
            public string User_Password { get; set; }
            public bool User_IsActive { get; set; }
            public bool User_Locked { get; set; }
            public Nullable<int> User_LoginCount { get; set; }
            public Nullable<int> User_FailedCount { get; set; }
            public int Company_ID { get; set; }
            public int Language_ID { get; set; }
            public byte User_Type_ID { get; set; }
            public Nullable<int> Employee_ID { get; set; }
            public Nullable<int> Partner_ID { get; set; }
            public string LandingPage { get; set; }
            public string User_IPAddress { get; set; }
            public Nullable<int> WareHouse_ID { get; set; }
            public string ReleaseVersionPopup { get; set; }

            public virtual ICollection<GNM_ATTACHMENTDETAIL> GNM_ATTACHMENTDETAIL { get; set; }
            public virtual ICollection<GNM_GPSLOG> GNM_GPSLOG { get; set; }
            public virtual ICollection<GNM_MODELATTACHMENTDETAIL> GNM_MODELATTACHMENTDETAIL { get; set; }
            public virtual ICollection<GNM_REF_ATTACHMENTDETAIL> GNM_REF_ATTACHMENTDETAIL { get; set; }
            public virtual ICollection<GNM_UserCompany> GNM_UserCompany { get; set; }
            public virtual ICollection<GNM_UserLocale> GNM_UserLocale { get; set; }
            public virtual ICollection<GNM_UserRole> GNM_UserRole { get; set; }
            public virtual ICollection<GNM_WALKAROUNDATTACHMENTDETAIL> GNM_WALKAROUNDATTACHMENTDETAIL { get; set; }
            public virtual ICollection<GNM_TAMSAdjustmentLog> GNM_TAMSAdjustmentLog { get; set; }
            public virtual ICollection<GNM_RegilarizeWindowCloseLog> GNM_RegilarizeWindowCloseLog { get; set; }
            public virtual ICollection<GNM_DashboardConfiguration> GNM_DashboardConfiguration { get; set; }
        }
        public partial class GNM_ATTACHMENTDETAIL
        {
            public int ATTACHMENTDETAIL_ID { get; set; }
            public string FILENAME { get; set; }
            public string FILEDESCRIPTION { get; set; }
            public int UPLOADBY { get; set; }
            public System.DateTime UPLOADDATE { get; set; }
            public string REMARKS { get; set; }
            public int COMPANY_ID { get; set; }
            public int OBJECT_ID { get; set; }
            public int TRANSACTION_ID { get; set; }
            public Nullable<int> DETAIL_ID { get; set; }
            public string TABLE_NAME { get; set; }

            public virtual GNM_Object GNM_Object { get; set; }
            public virtual GNM_User GNM_User { get; set; }
        }

        public partial class GNM_GPSLOG
        {
            public int GPSLOG_ID { get; set; }
            public int OBJECT_ID { get; set; }
            public int RECORD_ID { get; set; }
            public double LATITUDE { get; set; }
            public double LONGITUDE { get; set; }
            public System.DateTime MODIFIEDDATE { get; set; }
            public int USER_ID { get; set; }
            public int COMPANY_ID { get; set; }
            public int BRANCH_ID { get; set; }
            public string ActionName { get; set; }
            public Nullable<bool> IsFromMobile { get; set; }
            public Nullable<System.DateTime> LOGINDATETIME { get; set; }
            public Nullable<System.DateTime> LOGOUTDATETIME { get; set; }
            public Nullable<int> Menu_ID { get; set; }
            public string HostName { get; set; }
            public string AddressFamily { get; set; }
            public string isTrustedHost { get; set; }
            public string AddressList { get; set; }
            public string LocalIPAddress { get; set; }

            public virtual GNM_Object GNM_Object { get; set; }
            public virtual GNM_User GNM_User { get; set; }
        }

        public partial class GNM_MODELATTACHMENTDETAIL
        {
            public int MODELATTACHMENTDETAIL_ID { get; set; }
            public int MODEL_ID { get; set; }
            public string FILENAME { get; set; }
            public string FILEDESCRIPTION { get; set; }
            public int UPLOADBY { get; set; }
            public System.DateTime UPLOADDATE { get; set; }
            public int OBJECT_ID { get; set; }
            public bool AUTODISPLAY { get; set; }
            public bool PRINT { get; set; }

            public virtual GNM_Object GNM_Object { get; set; }
            public virtual GNM_User GNM_User { get; set; }
        }

        public partial class GNM_REF_ATTACHMENTDETAIL
        {
            public int REFATTACHMENTDETAIL_ID { get; set; }
            public string FILENAME { get; set; }
            public string FILEDESCRIPTION { get; set; }
            public int UPLOADBY { get; set; }
            public System.DateTime UPLOADDATE { get; set; }
            public string REMARKS { get; set; }
            public int COMPANY_ID { get; set; }
            public int OBJECT_ID { get; set; }
            public int TRANSACTION_ID { get; set; }
            public Nullable<int> DETAIL_ID { get; set; }
            public string TABLE_NAME { get; set; }
            public Nullable<bool> ISACTIVE { get; set; }

            public virtual GNM_Object GNM_Object { get; set; }
            public virtual GNM_User GNM_User { get; set; }
        }

        public partial class GNM_UserCompany
        {
            public int UserCompanyDet_ID { get; set; }
            public int User_ID { get; set; }
            public int Company_ID { get; set; }

            public virtual GNM_User GNM_User { get; set; }
        }
        public partial class GNM_UserLocale
        {
            public int User_Locale_ID { get; set; }
            public int User_ID { get; set; }
            public int Language_ID { get; set; }
            public string User_Name { get; set; }

            public virtual GNM_User GNM_User { get; set; }
        }
        public partial class GNM_UserRole
        {
            public int UserRole_ID { get; set; }
            public int User_ID { get; set; }
            public int Role_ID { get; set; }

            public virtual GNM_Role GNM_Role { get; set; }
            public virtual GNM_User GNM_User { get; set; }
        }
        public partial class GNM_WALKAROUNDATTACHMENTDETAIL
        {
            public int ATTACHMENTDETAIL_ID { get; set; }
            public string FILENAME { get; set; }
            public string FILEDESCRIPTION { get; set; }
            public int UPLOADBY { get; set; }
            public System.DateTime UPLOADDATE { get; set; }
            public string REMARKS { get; set; }
            public int COMPANY_ID { get; set; }
            public int OBJECT_ID { get; set; }
            public int TRANSACTION_ID { get; set; }
            public Nullable<int> DETAIL_ID { get; set; }
            public string TABLE_NAME { get; set; }
            public string TYPE { get; set; }

            public virtual GNM_Object GNM_Object { get; set; }
            public virtual GNM_User GNM_User { get; set; }
        }

        public partial class GNM_TAMSAdjustmentLog
        {
            public int TAMSAdjustmentLog_ID { get; set; }
            public int Employee_ID { get; set; }
            public int ActivityType { get; set; }
            public System.DateTime Date { get; set; }
            public Nullable<System.DateTime> ActualStartDatetime { get; set; }
            public Nullable<System.DateTime> ActualEndDatetime { get; set; }
            public System.DateTime AdjustedStartDatetime { get; set; }
            public System.DateTime AdjustedEndDatetime { get; set; }
            public int AdjustedBy_ID { get; set; }
            public System.DateTime AdjustedDate { get; set; }
            public string LaborCode { get; set; }

            public virtual GNM_User GNM_User { get; set; }
        }
        public partial class GNM_RegilarizeWindowCloseLog
        {
            public int WindowCloseLog_ID { get; set; }
            public System.DateTime WindowClosDateTime { get; set; }
            public int USER_ID { get; set; }
            public int Employee_ID { get; set; }
            public int BRANCH_ID { get; set; }
            public Nullable<System.DateTime> FromDateTime { get; set; }
            public Nullable<System.DateTime> ToDateTime { get; set; }
            public int Shift_ID { get; set; }

            public virtual GNM_User GNM_User { get; set; }
        }
        public partial class GNM_DashboardConfiguration
        {
            public int DashboardConfiguration_ID { get; set; }
            public Nullable<int> User_ID { get; set; }
            public Nullable<int> Object_ID { get; set; }
            public Nullable<bool> IsVisible { get; set; }
            public Nullable<bool> IsExpand { get; set; }

            public virtual GNM_Object GNM_Object { get; set; }
            public virtual GNM_User GNM_User { get; set; }
        }
        public partial class GNM_Object
        {
            public GNM_Object()
            {
                this.GNM_RoleObject = new HashSet<GNM_RoleObject>();
                this.GNM_PrefixSuffix = new HashSet<GNM_PrefixSuffix>();
                this.GNM_GPSLOG = new HashSet<GNM_GPSLOG>();
                this.GNM_ATTACHMENTDETAIL = new HashSet<GNM_ATTACHMENTDETAIL>();
                this.GNM_MODELATTACHMENTDETAIL = new HashSet<GNM_MODELATTACHMENTDETAIL>();
                this.GNM_REF_ATTACHMENTDETAIL = new HashSet<GNM_REF_ATTACHMENTDETAIL>();
                this.GNM_WALKAROUNDATTACHMENTDETAIL = new HashSet<GNM_WALKAROUNDATTACHMENTDETAIL>();
                this.GNM_DashboardConfiguration = new HashSet<GNM_DashboardConfiguration>();
            }

            public int Object_ID { get; set; }
            public string Object_Name { get; set; }
            public string Read_Action { get; set; }
            public string Create_Action { get; set; }
            public string Update_Action { get; set; }
            public string Delete_Action { get; set; }
            public string Export_Action { get; set; }
            public string Print_Action { get; set; }
            public bool Object_IsActive { get; set; }
            public string Object_Description { get; set; }
            public string Import_Action { get; set; }
            public string Object_Type { get; set; }

            public virtual ICollection<GNM_RoleObject> GNM_RoleObject { get; set; }
            public virtual ICollection<GNM_PrefixSuffix> GNM_PrefixSuffix { get; set; }
            public virtual ICollection<GNM_GPSLOG> GNM_GPSLOG { get; set; }
            public virtual ICollection<GNM_ATTACHMENTDETAIL> GNM_ATTACHMENTDETAIL { get; set; }
            public virtual ICollection<GNM_MODELATTACHMENTDETAIL> GNM_MODELATTACHMENTDETAIL { get; set; }
            public virtual ICollection<GNM_REF_ATTACHMENTDETAIL> GNM_REF_ATTACHMENTDETAIL { get; set; }
            public virtual ICollection<GNM_WALKAROUNDATTACHMENTDETAIL> GNM_WALKAROUNDATTACHMENTDETAIL { get; set; }
            public virtual ICollection<GNM_DashboardConfiguration> GNM_DashboardConfiguration { get; set; }
        }

        public partial class GNM_RoleObject
        {
            public int RoleObject_ID { get; set; }
            public int Role_ID { get; set; }
            public int Object_ID { get; set; }
            public bool RoleObject_Create { get; set; }
            public bool RoleObject_Read { get; set; }
            public bool RoleObject_Update { get; set; }
            public bool RoleObject_Delete { get; set; }
            public bool RoleObject_Print { get; set; }
            public bool RoleObject_Export { get; set; }
            public bool RoleObject_Import { get; set; }

            public virtual GNM_Role GNM_Role { get; set; }
            public virtual GNM_Object GNM_Object { get; set; }
        }
        public partial class GNM_PrefixSuffix
        {
            public int PrefixSuffix_ID { get; set; }
            public int Company_ID { get; set; }
            public Nullable<int> Branch_ID { get; set; }
            public int Object_ID { get; set; }
            public int Start_Number { get; set; }
            public string Prefix { get; set; }
            public string Suffix { get; set; }
            public System.DateTime FromDate { get; set; }
            public System.DateTime ToDate { get; set; }
            public int ModifiedBY { get; set; }
            public System.DateTime ModifiedDate { get; set; }
            public Nullable<int> FinancialYear { get; set; }
            public Nullable<int> Company_FinancialYear_ID { get; set; }

            public virtual GNM_Object GNM_Object { get; set; }
        }

        public partial class GNM_Role
        {
            public GNM_Role()
            {
                this.GNM_RoleObject = new HashSet<GNM_RoleObject>();
                this.GNM_UserRole = new HashSet<GNM_UserRole>();
            }

            public int Role_ID { get; set; }
            public int Company_ID { get; set; }
            public string Role_Name { get; set; }

            public virtual ICollection<GNM_RoleObject> GNM_RoleObject { get; set; }
            public virtual ICollection<GNM_UserRole> GNM_UserRole { get; set; }
        }
        #endregion

    }
}
