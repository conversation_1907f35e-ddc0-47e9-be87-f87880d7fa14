﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreTermsAndConditionsServices
    {
        static string AppPath = string.Empty;
        #region ::: Save /Mithun:::
        /// <summary>
        /// To Save
        /// </summary>
        public static IActionResult Save(SaveTermsConditionList SaveObj, string constring, int LogException)
        {
            string result = string.Empty;
            try
            {
                int ID = Int32.Parse(SaveObj.ID);
                int ObjectID = Int32.Parse(SaveObj.ObjectID);
                int TermsID = Int32.Parse(SaveObj.TermsID);
                bool IsDefault = Convert.ToBoolean(SaveObj.Default);
                bool IsDefaultUpdate = Convert.ToBoolean(SaveObj.isDefaultupdate);
                int Company_ID = Convert.ToInt32(SaveObj.Company_ID);
                int Branch_ID = Convert.ToInt32(SaveObj.Branch);
                int UserID = Convert.ToInt32(SaveObj.User_ID);
                int MenuID = Convert.ToInt32(SaveObj.MenuID);
                string TermsConditions = SaveObj.TermsConditions.Trim() == "" ? null : Common.DecryptString(SaveObj.TermsConditions);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    using (SqlCommand cmd = new SqlCommand("Up_Ins_Upd_Am_Erp_SaveTermsAndConditions", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@ID", ID);
                        cmd.Parameters.AddWithValue("@ObjectID", ObjectID);
                        cmd.Parameters.AddWithValue("@TermsID", TermsID);
                        cmd.Parameters.AddWithValue("@IsDefault", IsDefault);
                        cmd.Parameters.AddWithValue("@CompanyID", Company_ID);
                        cmd.Parameters.AddWithValue("@BranchID", Branch_ID);
                        cmd.Parameters.AddWithValue("@TermsConditions", TermsConditions);
                        cmd.Parameters.AddWithValue("@IsDefaultUpdate", IsDefaultUpdate);
                        cmd.Parameters.AddWithValue("@UserID", UserID);
                        cmd.Parameters.AddWithValue("@MenuID", MenuID);

                        conn.Open();
                        result = cmd.ExecuteScalar().ToString();
                    }
                    //    gbl.InsertGPSDetails(Convert.ToInt32(SaveObj.Company_ID), Convert.ToInt32(SaveObj.Branch), Convert.ToInt32(SaveObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreTermsAndConditions",constring)), SaveObj.ID, 0, 0, "Insert", false, Convert.ToInt32(SaveObj.MenuID));

                }
            }
            catch (Exception ex)
            {
                result = "Error";
                if (LogException == 1)
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            //return result;
            return new JsonResult(result);
        }


        #endregion

        #region ::: Update /Mithun:::
        /// <summary>
        /// To Update
        /// </summary>    
        public static IActionResult Update(UpdateTermsConditionList UpdateObj, string constring, int LogException)
        {
            string result = string.Empty;
            try
            {
                int ID = Int32.Parse(UpdateObj.ID);
                int ObjectID = Int32.Parse(UpdateObj.ObjectID);
                int TermsID = Int32.Parse(UpdateObj.TermsID);
                bool Isdefault = Convert.ToBoolean(UpdateObj.Default);
                bool Isdefaultupdate = Convert.ToBoolean(UpdateObj.isDefaultupdate);
                int Company_ID = Convert.ToInt32(UpdateObj.Company_ID);
                int Branch_ID = Convert.ToInt32(UpdateObj.Branch);
                string TermsConditions = UpdateObj.TermsConditions == "" ? null : Common.DecryptString(UpdateObj.TermsConditions);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Upd_Am_Erp_UpdateTermsAndConditions", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@ID", ID);
                        cmd.Parameters.AddWithValue("@ObjectID", ObjectID);
                        cmd.Parameters.AddWithValue("@TermsID", TermsID);
                        cmd.Parameters.AddWithValue("@IsDefault", Isdefault);
                        cmd.Parameters.AddWithValue("@IsDefaultUpdate", Isdefaultupdate);
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);
                        cmd.Parameters.AddWithValue("@Branch_ID", Branch_ID);
                        cmd.Parameters.AddWithValue("@TermsConditions", TermsConditions);

                        SqlDataReader reader = cmd.ExecuteReader();
                        if (reader.Read())
                        {
                            result = reader["Result"].ToString();
                        }
                    }
                    //  gbl.InsertGPSDetails(Convert.ToInt32(UpdateObj.Company_ID), Convert.ToInt32(UpdateObj.Branch), Convert.ToInt32(UpdateObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreTermsAndConditions",constring)), ID, 0, 0, "Update", false, Convert.ToInt32(UpdateObj.MenuID));


                }
            }
            catch (Exception ex)
            {
                result = "Error";
                if (LogException == 1)
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            //return result;
            return new JsonResult(result);
        }

        #endregion

        #region :::UpdateLocale /Mithun:::
        /// <summary>
        /// UpdateLocale
        /// </summary>
        public static IActionResult UpdateLocale(UpdateLocaleTermsConditionList UpdateLocaleObj, string constring, int LogException)
        {
            string result = string.Empty;
            try
            {
                int ID = Int32.Parse(UpdateLocaleObj.ID);
                string termsConditions = UpdateLocaleObj.TermsConditions == "" ? null : Common.DecryptString(UpdateLocaleObj.TermsConditions);

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    SqlCommand command = new SqlCommand("Up_Ins_Upd_Am_Erp_UpdateTandCLocale", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.AddWithValue("@TERMSANDCONDITIONS_ID", ID);
                    command.Parameters.AddWithValue("@TermsConditions", (object)termsConditions ?? DBNull.Value);
                    connection.Open();
                    command.ExecuteNonQuery();
                }

                result = "Success";
                //  gbl.InsertGPSDetails(Convert.ToInt32(UpdateLocaleObj.Company_ID), Convert.ToInt32(UpdateLocaleObj.Branch), Convert.ToInt32(UpdateLocaleObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreTermsAndConditions",constring)), ID, 0, 0, "Update", false, Convert.ToInt32(UpdateLocaleObj.MenuID));
            }
            catch (Exception ex)
            {
                result = "Error";
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return result;
            return new JsonResult(result);
        }

        #endregion

        #region ::: SelectTermsConditionsGrid /Mithun:::
        /// <summary>
        /// SelectTermsConditionsGrid
        /// </summary>
        public static IActionResult SelectTermsConditionsGrid(SelectTermsConditionsGridList SelectTermsConditionsGridObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {
            try
            {
                var jsonResult = default(dynamic);
                string Refresh = CommonFunctionalities.GetResourceString(SelectTermsConditionsGridObj.UserCulture.ToString(), "refresh").ToString();

                int Company_ID = Convert.ToInt32(SelectTermsConditionsGridObj.Company_ID);
                int Branch_ID = Convert.ToInt32(SelectTermsConditionsGridObj.Branch);

                List<object> data = new List<object>();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    SqlCommand command = new SqlCommand("Up_Sel_Am_Erp_SelectTermsConditionsGrid", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.AddWithValue("@Company_ID", Company_ID);
                    command.Parameters.AddWithValue("@Branch_ID", Branch_ID);
                    command.Parameters.AddWithValue("@sidx", sidx);
                    command.Parameters.AddWithValue("@sord", sord);
                    command.Parameters.AddWithValue("@page", page);
                    command.Parameters.AddWithValue("@rows", rows);

                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        TermsConditionsGrid1 item = new TermsConditionsGrid1
                        {
                            ID = (int)reader["ID"],
                            Edit = "<a title=" + CommonFunctionalities.GetGlobalResourceObject(SelectTermsConditionsGridObj.UserCulture.ToString(), "view").ToString() + " href='#' style='font-size: 13px;' id='" + reader["ID"] + "' key='" + reader["ID"] + "' editmode='false' class='editTermsConditions' Tname='" + reader["Object"] + "'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                            Delete = "<input type='checkbox' key='" + reader["ID"] + "' id='chk" + reader["ID"] + "' class='chkTermsConditionsDelete'/>",
                            Object = reader["Object"].ToString(),
                            Terms = reader["Terms"].ToString(),
                            IsDefault = reader["IsDefault"].ToString(),
                            Locale = "<a title='Localize' href='#' style='font-size: 13px;' width='20' height='20' src='" + AppPath + "/Content/local.png'  class='TermsAndconditionLocale' id='" + reader["ID"] + "'  key='" + reader["ID"] + "'><i class='fa fa-globe'></i></a>"
                        };


                        data.Add(item);
                    }

                    reader.Close();

                    // Convert data list to IQueryable<TermsConditionsGrid>
                    IQueryable<TermsConditionsGrid1> IQTermsandConditions = data.Cast<TermsConditionsGrid1>().AsQueryable();



                    // Apply filtering if needed

                    if (_search)
                    {
                        // Double decrypt the filters string
                        string decryptedFilters = Common.DecryptString(filters); // First decryption
                        string doubleDecryptedFilters = Common.DecryptString(decryptedFilters); // Second decryption

                        // Parse the twice-decrypted filters into the Filters object
                        Filters filtersobj = JObject.Parse(doubleDecryptedFilters).ToObject<Filters>();

                        // Apply the filter search
                        IQTermsandConditions = IQTermsandConditions.FilterSearch<TermsConditionsGrid1>(filtersobj);
                    }

                    if (advnce)
                    {
                        AdvanceFilter advnfilter = JObject.Parse((Query)).ToObject<AdvanceFilter>();
                        IQTermsandConditions = IQTermsandConditions.AdvanceSearch<TermsConditionsGrid1>(advnfilter);
                    }

                    IQTermsandConditions = IQTermsandConditions.OrderByField<TermsConditionsGrid1>(sidx, sord);
                    // Calculate total records directly from the data retrieved
                    int totalCount = IQTermsandConditions.Count();
                    int total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(totalCount) / Convert.ToDouble(rows))) : 0;

                    // Create JSON result
                    jsonResult = new
                    {
                        total = total,
                        page = page,
                        records = totalCount,
                        data = IQTermsandConditions,
                        filter = filters,
                        advanceFilter = Query,
                        Refresh
                    };
                }

                //return Json(jsonResult, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonResult);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
        }

        #endregion

        #region ::: LoadObjectDropdown /Mithun:::
        /// <summary>
        /// to Load Object Dropdowns
        /// </summary>
        public static IActionResult LoadObjectDropdown(LoadObjectDropdownList LoadObjectDropdownObj, string constring, int LogException)
        {
            var jsonData = default(dynamic);

            try
            {
                //  CommonFunctionalitiesController CFC = new CommonFunctionalitiesController();
                //GNM_User UserDetails = (GNM_User)Session["UserDetails"];
                //GNM_User UserDetails = LoadObjectDropdownObj.UserDetails.FirstOrDefault();
                int UserLang = Convert.ToInt32(LoadObjectDropdownObj.UserLanguageID);
                int GenLang = Convert.ToInt32(LoadObjectDropdownObj.GeneralLanguageID);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    using (SqlCommand cmd = new SqlCommand("Up_Drpdwn_Am_Erp_GetObjectDropdownTandC", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            var objArr = new List<object>();
                            while (reader.Read())
                            {
                                objArr.Add(new
                                {
                                    ID = reader["Object_ID"],
                                    Name = reader["Object_Description"]
                                });
                            }

                            if (UserLang == GenLang)
                            {
                                jsonData = new
                                {
                                    ObjArr = objArr
                                };
                            }
                            else
                            {
                                jsonData = new
                                {
                                    ObjArr = objArr
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }

        #endregion

        #region ::: SelTaxForPopUp /Mithun:::
        /// <summary>
        /// SelTaxForPopUp
        /// </summary>

        public static IActionResult LoadTermsDropdown(LoadTermsDropdownList LoadTermsDropdownObj, string constring, int LogException)
        {
            try
            {
                var jsonResult = default(dynamic);
                string Refresh = CommonFunctionalities.GetGlobalResourceObject(LoadTermsDropdownObj.UserCulture.ToString(), "Refresh").ToString();

                int UserLang = Convert.ToInt32(LoadTermsDropdownObj.UserLanguageID);
                int GenLang = Convert.ToInt32(LoadTermsDropdownObj.GeneralLanguageID);
                int CompanyID = Convert.ToInt32(LoadTermsDropdownObj.Company_ID);

                List<DropdownValues> comptrmsMstr = new List<DropdownValues>();
                int DefaultWh = 0;

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    SqlCommand command = new SqlCommand("Up_Sel_Am_Erp_LoadTermsDropdownInTandC", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.AddWithValue("@UserLanguageID", UserLang);
                    command.Parameters.AddWithValue("@GeneralLanguageID", GenLang);
                    command.Parameters.AddWithValue("@CompanyID", CompanyID);

                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        comptrmsMstr.Add(new DropdownValues
                        {
                            ID = Convert.ToInt32(reader["ID"]),
                            Name = reader["Name"].ToString()
                        });
                    }

                    // Retrieve the DefaultWh value
                    if (reader.NextResult() && reader.Read())
                    {
                        DefaultWh = Convert.ToInt32(reader["DefaultWh"]);
                    }

                    reader.Close();
                }

                jsonResult = new
                {
                    terms = comptrmsMstr.OrderBy(q => q.Name).Select(q => new { q.ID, q.Name }),
                    DefaultWh
                };

                //return Json(jsonResult, JsonRequestBehavior.AllowGet);
                return new JsonResult(jsonResult);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(new { Error = "Error" }) { StatusCode = 500 };
            }
        }


        #endregion

        #region ::: Delete /Mithun:::
        /// <summary>
        /// Delete
        /// </summary>
        public static IActionResult Delete(DeleteTermsConditionList DeleteObj, string constring, int LogException)
        {
            string ErrorMsg = string.Empty;
            JObject jObj = null;
            JTokenReader jTR = null;
            int Count = 0;
            int ID = 0;
            var Culture = "Resource_" + DeleteObj.Lang;
            try
            {
                jObj = JObject.Parse(DeleteObj.key);
                Count = jObj["rows"].Count();

                int Company_ID = Convert.ToInt32(DeleteObj.Company_ID);
                int Branch_ID = Convert.ToInt32(DeleteObj.Branch);
                int UserID = Convert.ToInt32(DeleteObj.User_ID);
                int MenuID = Convert.ToInt32(DeleteObj.MenuID);

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    for (int i = 0; i < Count; i++)
                    {
                        jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                        jTR.Read();
                        ID = Convert.ToInt32(jTR.Value);

                        // Check if the record is a default record
                        bool isDefault;
                        string checkDefaultQuery = "SELECT ISDEFAULT FROM GNM_TERMSANDCONDITIONS WHERE TERMSANDCONDITIONS_ID = @ID";
                        using (SqlCommand cmd = new SqlCommand(checkDefaultQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@ID", ID);
                            isDefault = Convert.ToBoolean(cmd.ExecuteScalar());
                        }

                        if (isDefault)
                        {
                            ErrorMsg += CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "Defaultrecordcannotbedeleted").ToString();
                            continue;
                        }

                        // Delete associated locale record if exists
                        string deleteLocaleQuery = "DELETE FROM GNM_TERMSANDCONDITIONSLOCALE WHERE TERMSANDCONDITIONS_ID = @ID";
                        using (SqlCommand cmd = new SqlCommand(deleteLocaleQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@ID", ID);
                            cmd.ExecuteNonQuery();
                        }

                        // Delete the main record
                        string deleteMainQuery = "DELETE FROM GNM_TERMSANDCONDITIONS WHERE TERMSANDCONDITIONS_ID = @ID";
                        using (SqlCommand cmd = new SqlCommand(deleteMainQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@ID", ID);
                            cmd.ExecuteNonQuery();
                        }

                        // Insert GPS details
                        //  gbl.InsertGPSDetails(Convert.ToInt32(DeleteObj.Company_ID), Convert.ToInt32(DeleteObj.Branch), Convert.ToInt32(DeleteObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreTermsAndConditions",constring)), ID, 0, 0, "Delete", false, Convert.ToInt32(DeleteObj.MenuID));


                        ErrorMsg += CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "deletedsuccessfully").ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorMsg = CommonFunctionalities.GetGlobalResourceObject(Culture.ToString(), "Error").ToString();
                if (LogException == 1)
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            //return ErrorMsg;
            return new JsonResult(ErrorMsg);
        }

        #endregion

        #region ::: GetTermsConditions /Mithun:::
        /// <summary>
        /// GetTermsConditions
        /// </summary>
        public static IActionResult GetTermsConditions(GetTermsConditionsList GetTermsConditionsObj, string constring, int LogException)
        {
            var jsonResult = default(dynamic);

            try
            {
                //GNM_User User = (GNM_User)Session["UserDetails"];

                //  GNM_User User = GetTermsConditionsObj.UserDetails.FirstOrDefault();

                using (SqlConnection connection = new SqlConnection(constring))
                {
                    connection.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Sel_Am_Erp_GetTermsConditions", connection))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@ID", GetTermsConditionsObj.ID);
                        cmd.Parameters.AddWithValue("@UserID", GetTermsConditionsObj.User_ID);
                        cmd.Parameters.AddWithValue("@Company_ID", GetTermsConditionsObj.Company_ID);

                        // Execute the command
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                List<DropdownValues> comptrmsMstr = new List<DropdownValues>();
                                List<DropdownValues> ObjArr = new List<DropdownValues>();

                                if (reader.NextResult())
                                {
                                    while (reader.Read())
                                    {
                                        comptrmsMstr.Add(new DropdownValues
                                        {
                                            ID = Convert.ToInt32(reader["ID"]),
                                            Name = reader["Name"].ToString()
                                        });
                                    }
                                }

                                if (reader.NextResult())
                                {
                                    while (reader.Read())
                                    {
                                        ObjArr.Add(new DropdownValues
                                        {
                                            ID = Convert.ToInt32(reader["ID"]),
                                            Name = reader["Name"].ToString()
                                        });
                                    }
                                }

                                jsonResult = new
                                {
                                    ID = Convert.ToInt32(reader["ID"]),
                                    OBJECT_ID = Convert.ToInt32(reader["OBJECT_ID"]),
                                    TERMS_ID = Convert.ToInt32(reader["TERMS_ID"]),
                                    IsDefault = reader["IsDefault"].ToString(),
                                    TermsConditions = reader["TermsConditions"].ToString(),
                                    comptrmsMstr = comptrmsMstr,
                                    ObjArr = ObjArr
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle exceptions
                if (LogException == 1)
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);
        }

        #endregion

        #region ::: GetTermsConditionsLocale /Mithun:::
        /// <summary>
        /// GetTermsConditionsLocale
        /// </summary>
        public static IActionResult GetTermsConditionsLocale(GetTermsConditionsLocaleList GetTermsConditionsLocaleObj, string constring, int LogException)
        {
            var jsonResult = default(dynamic);
            //GNM_User User = (GNM_User)Session["UserDetails"];
            GNM_User User = GetTermsConditionsLocaleObj.UserDetails.FirstOrDefault();
            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();
                    string query = "Up_Sel_Am_Erp_GetTermsConditionsLocale"; // Assuming the stored procedure name
                    SqlCommand cmd = new SqlCommand(query, conn);
                    cmd.CommandType = CommandType.StoredProcedure;

                    // Add parameters
                    cmd.Parameters.AddWithValue("@ID", GetTermsConditionsLocaleObj.ID);

                    // Execute the command
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            jsonResult = new
                            {
                                ID = Convert.ToInt32(reader["TERMSANDCONDITIONS_ID"]),
                                TermsConditions = reader["TERMSANDCONDITIONS"].ToString(),
                            };
                        }
                    }
                }

                // Logging part
                // gbl.InsertGPSDetails(Convert.ToInt32(GetTermsConditionsLocaleObj.Company_ID), Convert.ToInt32(GetTermsConditionsLocaleObj.Branch), Convert.ToInt32(GetTermsConditionsLocaleObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreTermsAndConditions",constring)), GetTermsConditionsLocaleObj.ID, 0, 0, "Viewed", false, Convert.ToInt32(GetTermsConditionsLocaleObj.MenuID));
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            //return Json(jsonResult, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonResult);
        }


        #endregion


        #region ::: Data For Export :::
        public static IQueryable<TermsConditionsGrid1> GetTermsConditionsGridData(SelectTermsConditionsGridList ExportObj, string constring)
        {
            List<TermsConditionsGrid1> data = new List<TermsConditionsGrid1>();


            using (SqlConnection connection = new SqlConnection(constring))
            {
                SqlCommand command = new SqlCommand("Up_Sel_Am_Erp_SelectTermsConditionsGrid", connection);
                command.CommandType = CommandType.StoredProcedure;

                // Add parameters for the stored procedure
                command.Parameters.AddWithValue("@Company_ID", ExportObj.Company_ID);
                command.Parameters.AddWithValue("@Branch_ID", ExportObj.Branch);
                command.Parameters.AddWithValue("@sidx", ExportObj.sidx);
                command.Parameters.AddWithValue("@sord", ExportObj.sord);
                command.Parameters.AddWithValue("@page", ExportObj.page);
                command.Parameters.AddWithValue("@rows", ExportObj.rows);

                connection.Open();
                SqlDataReader reader = command.ExecuteReader();

                while (reader.Read())
                {
                    TermsConditionsGrid1 item = new TermsConditionsGrid1
                    {
                        ID = (int)reader["ID"],
                        Object = reader["Object"].ToString(),
                        Terms = reader["Terms"].ToString(),
                        IsDefault = reader["IsDefault"].ToString(),
                    };

                    // Add each record to the data list
                    data.Add(item);
                }

                reader.Close();
            }

            // Convert data list to IQueryable
            IQueryable<TermsConditionsGrid1> IQTermsandConditions = data.AsQueryable();

            return IQTermsandConditions;
        }


        #endregion


        #region ::: Export :::

        public static async Task<object> Export(SelectTermsConditionsGridList ExportObj, string constring, int LogException, string filters, string sidx, string sord, int page, int rows)
        {
            DataTable Dt = new DataTable();
            int Count = 0;
            try
            {
                // Call the new method to get the filtered, sorted IQueryable data
                IQueryable<TermsConditionsGrid1> IQTermsandConditions = GetTermsConditionsGridData(ExportObj, constring);


                // Apply filtering if necessary
                if (filters != "null" && filters != "undefined")
                {
                    string decryptedFilters = Common.DecryptString(filters); // First decryption
                    string doubleDecryptedFilters = Common.DecryptString(decryptedFilters); // Second decryption

                    // Parse the twice-decrypted filters into the Filters object
                    Filters filtersobj = JObject.Parse(doubleDecryptedFilters).ToObject<Filters>();

                    // Apply the filter search
                    IQTermsandConditions = IQTermsandConditions.FilterSearch<TermsConditionsGrid1>(filtersobj);
                }
                //if (advnce)
                //{
                //    AdvanceFilter advnfilter = JObject.Parse(Query).ToObject<AdvanceFilter>();
                //    IQTermsandConditions = IQTermsandConditions.AdvanceSearch<TermsConditionsGrid1>(advnfilter);
                //}

                // Apply sorting
                IQTermsandConditions = IQTermsandConditions.OrderByField<TermsConditionsGrid1>(sidx, sord);

                // Data export logic
                var TermsConditionArray = from a in IQTermsandConditions.AsEnumerable()
                                          select new
                                          {
                                              a.Terms,
                                              a.Object,
                                              a.IsDefault
                                          };

                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "Terms").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "Object").ToString());
                Dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "IsDefault").ToString());

                DataTable DtAlignment = new DataTable();
                DtAlignment.Columns.Add("Terms");
                DtAlignment.Columns.Add("Object");
                DtAlignment.Columns.Add("IsDefault");
                DtAlignment.Rows.Add(0, 0, 0);

                Count = TermsConditionArray.Count();
                if (Count > 0)
                {
                    for (int i = 0; i < Count; i++)
                    {
                        Dt.Rows.Add(TermsConditionArray.ElementAt(i).Terms, TermsConditionArray.ElementAt(i).Object, TermsConditionArray.ElementAt(i).IsDefault);
                    }

                    DataTable Dt1 = new DataTable();
                    ReportExportList reportExportList = new ReportExportList
                    {
                        Company_ID = ExportObj.Company_ID, // Assuming this is available in ExportObj
                        Branch = ExportObj.Branch.ToString(),
                        GeneralLanguageID = ExportObj.GeneralLanguageID,
                        UserLanguageID = ExportObj.UserLanguageID,
                        Options = Dt1,
                        dt = Dt,
                        Alignment = DtAlignment,
                        FileName = "Consignee", // Set a default or dynamic filename
                        Header = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "TermsAndConditions").ToString(), // Set a default or dynamic header
                        exprtType = ExportObj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                        UserCulture = ExportObj.UserCulture
                    };


                    var result = await ReportExport.Export(reportExportList, constring, LogException);
                    return result.Value;

                    // Export the data

                    //ReportExport.Export(exprtType, Dt, Dt1, DtAlignment, "TermsAndConditions", CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "TermsAndConditions").ToString());
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return null;
        }

        #endregion


        public class GetTermsConditionsLocaleList
        {
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public int ID { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }
        public class GetTermsConditionsList
        {
            public int ID { get; set; }
            public int User_ID { get; set; }
            public int Company_ID { get; set; }
            public List<GNM_User> UserDetails { get; set; }
        }

        public class DeleteTermsConditionList
        {

            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public string key { get; set; }
            public string Lang { get; set; }

        }
        public class LoadTermsDropdownList
        {
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string UserCulture { get; set; }
        }

        public class DropdownValues
        {
            public int ID
            {
                get;
                set;
            }
            public String Name
            {
                get;
                set;
            }
            public bool RefMasterDetail_IsDefault { get; set; }
        }
        public class LoadObjectDropdownList
        {
            public List<GNM_User> UserDetails { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        public class SelectTermsConditionsGridList
        {
            public int GeneralLanguageID { get; set; }
            public int UserLanguageID { get; set; }
            public int Branch { get; set; }
            public int Company_ID { get; set; }
            public string UserCulture { get; set; }
            public string filters { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public int page { get; set; }
            public int rows { get; set; }
            public int exprtType { get; set; }
        }
        public class TermsConditionsGrid1
        {
            public int ID { get; set; }
            public string Edit { get; set; }
            public string Delete { get; set; }
            public string Object { get; set; }
            public string Terms { get; set; }
            public string IsDefault { get; set; }
            public string Locale { get; set; }
        }

        public class UpdateLocaleTermsConditionList
        {
            public string ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public string TermsConditions { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }
        public class UpdateTermsConditionList
        {
            public string ID { get; set; }
            public string ObjectID { get; set; }
            public string TermsID { get; set; }
            public bool Default { get; set; }
            public bool isDefaultupdate { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public string TermsConditions { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }
        public class SaveTermsConditionList
        {
            public string ID { get; set; }
            public string ObjectID { get; set; }
            public string TermsID { get; set; }
            public bool Default { get; set; }
            public bool isDefaultupdate { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int MenuID { get; set; }
            public string TermsConditions { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }
    }
}
