﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CompanyParameterServices
    {
        public static string AppPath = null;
        #region ::: Select vinay n 26/9/24:::
        /// <summary>
        /// Select
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="sidx"></param>
        /// <param name="rows"></param>
        /// <param name="page"></param>
        /// <param name="sord"></param>
        /// <param name="_search"></param>
        /// <param name="nd"></param>
        /// <param name="filters"></param>
        /// <param name="advnce"></param>
        /// <param name="advanceFilter"></param>
        /// <param name="LogException"></param>
        /// <param name="constring"></param>
        /// <returns></returns>
        public static IActionResult Select(SelectCompanyParameterList Obj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advanceFilter, int LogException, string constring)
        {
            var jsonobj = default(dynamic);
            var companyParams = new List<GNM_CompParam>();
            var setupParams = new List<GNM_Setup_Parameter>();
            try
            {

                int Company_ID = Convert.ToInt32(Obj.Company_ID);
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    string query = "UP_SELECT_AM_ERP_Select_CompanyParameter";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;

                            command.Parameters.AddWithValue("@Company_ID", Company_ID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                // First result set: MovementTypeDefinition data

                                while (reader.Read())
                                {
                                    GNM_CompParam compParam = new GNM_CompParam
                                    {
                                        CompanyParam_ID = (int)reader["CompanyParam_ID"],
                                        Company_ID = (int)reader["Company_ID"],
                                        Param_Name = reader["Param_Name"].ToString(),
                                        Param_value = reader["Param_value"].ToString()
                                    };
                                    companyParams.Add(compParam);
                                }

                                if (reader.NextResult())
                                {

                                    while (reader.Read())
                                    {
                                        GNM_Setup_Parameter setupParam = new GNM_Setup_Parameter
                                        {
                                            Parameter_id = (int)reader["Parameter_id"],
                                            Object_Name = reader["Object_Name"].ToString(),
                                            Paramater_name = reader["Paramater_name"].ToString(),
                                            Permitted_values = reader["Permitted_values"].ToString(),
                                            Remarks = reader["Remarks"].ToString()
                                        };
                                        setupParams.Add(setupParam);
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log exception
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                    }
                    finally
                    {
                        command?.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                IQueryable<GNM_CompParam> IQCompanyParameter = companyParams.AsQueryable();
                ////Added By Sridhar U for FSM CR-5 Changes
                IEnumerable<GNM_Setup_Parameter> gnmCompanyparams = setupParams.AsEnumerable();
                string companyParamNames = "-1:--Select--;";
                for (int i = 0; i < gnmCompanyparams.Count(); i++)
                {
                    companyParamNames = companyParamNames + gnmCompanyparams.ElementAt(i).Paramater_name + ":" + gnmCompanyparams.ElementAt(i).Paramater_name.Replace(";", ":") + ";";
                }
                companyParamNames = companyParamNames.TrimEnd(new char[] { ';' });
                //Change Ends

                //Filter
                if (_search)
                {
                    string decodedValue = Uri.UnescapeDataString(filters);
                    Filters filtersObj = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<Filters>();
                    if (filtersObj.rules.Count() > 0)
                        IQCompanyParameter = IQCompanyParameter.FilterSearch<GNM_CompParam>(filtersObj);
                }
                //advance filter
                if (advnce)
                {
                    string decodedValue = Uri.UnescapeDataString(advanceFilter);
                    AdvanceFilter advnfilter = JObject.Parse(decodedValue).ToObject<AdvanceFilter>();
                    IQCompanyParameter = IQCompanyParameter.AdvanceSearch<GNM_CompParam>(advnfilter);
                }

                //sorting
                IQCompanyParameter = IQCompanyParameter.OrderByField<GNM_CompParam>(sidx, sord);
                //TempData["Export"] = IQCompanyParameter.ToList();

                int count = IQCompanyParameter.Count();
                int total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                jsonobj = new
                {
                    total = total,
                    page = page,
                    ////Added By Sridhar U for FSM CR-5 Changes
                    companyParamNames,
                    records = count,
                    //Change Ends
                    rows = (from a in IQCompanyParameter.AsEnumerable()
                            select new
                            {
                                ID = a.CompanyParam_ID,
                                edit = "<img id='" + a.CompanyParam_ID + "' src='" + AppPath + "/Content/edit.gif' key='" + a.CompanyParam_ID + "' class='editParameter' editmode='false'/>",
                                delete = "<input type='checkbox' key='" + a.CompanyParam_ID + "' defaultchecked=''  id='chk" + a.CompanyParam_ID + "' class='deleteParameter'/>",
                                a.Param_Name,
                                a.Param_value
                            }).ToList().Paginate(page, rows),
                };

            }
            catch (Exception ex)
            {
                if (LogException == 1) LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return new JsonResult(jsonobj);
        }
        #endregion
        #region ::: Save vinay n 26/9/24:::
        /// <summary>
        /// Save
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult Save(SaveCompanyParameterList Obj, string connString, int LogException)
        {
            string Msg = string.Empty;
            int Count = 0;
            try
            {
                int Company_ID = Convert.ToInt32(Obj.Company_ID);
                JObject jObj = JObject.Parse(Obj.data);
                Count = jObj["rows"].Count();

                GNM_CompParam CP = null;
                for (int i = 0; i < Count; i++)
                {
                    CP = jObj["rows"].ElementAt(i).ToObject<GNM_CompParam>();
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_Save_AM_ERP_Save_CompanyParameter";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;

                                command.Parameters.AddWithValue("@CompanyParam_ID", CP.CompanyParam_ID);
                                command.Parameters.AddWithValue("@Company_ID", Company_ID);
                                command.Parameters.AddWithValue("@Param_Name", Common.DecryptString(CP.Param_Name));
                                command.Parameters.AddWithValue("@Param_value", Common.DecryptString(CP.Param_value));


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                command.ExecuteNonQuery();
                            }
                        }
                        catch (Exception ex)
                        {
                            // Log exception
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }
                        }
                        finally
                        {
                            command?.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                    if (CP.CompanyParam_ID == 0)//insert
                    {

                        //gbl.InsertGPSDetails(Convert.ToInt32(Obj.Company_ID), Convert.ToInt32(Obj.Branch), Convert.ToInt32(Obj.User_ID), Convert.ToInt32(Common.GetObjectID("CompanyParameter")), CP.CompanyParam_ID, 0, 0, "Insert", false, Convert.ToInt32(Obj.MenuID));
                    }
                    else//update
                    {

                        //gbl.InsertGPSDetails(Convert.ToInt32(Obj.Company_ID), Convert.ToInt32(Obj.Branch), Convert.ToInt32(Obj.User_ID), Convert.ToInt32(Common.GetObjectID("CompanyParameter")), CP.CompanyParam_ID, 0, 0, "Update", false, Convert.ToInt32(Obj.MenuID));
                    }
                }

                Msg = "1";
            }
            catch (Exception ex)
            {
                if (LogException == 1) LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return new JsonResult(Msg);
        }
        #endregion
        #region ::: Delete  vinay n 26/9/24:::
        /// <summary>
        /// Delete
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static IActionResult Delete(DeleteCompanyParameterList Obj, string connString, int LogException)
        {
            string Msg = string.Empty;
            try
            {

                JObject jObj;
                jObj = JObject.Parse(Obj.key);
                int Count = jObj["rows"].Count();
                int ID = 0;
                GNM_CompParam DeleteParam = default(dynamic);

                for (int i = 0; i < Count; i++)
                {
                    JTokenReader jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jTR.Read();
                    ID = Convert.ToInt32(jTR.Value);

                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_Delete_AM_ERP_Delete_CompanyParameter";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;

                                command.Parameters.AddWithValue("@CompanyParam_ID", ID);


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                command.ExecuteNonQuery();
                            }
                        }
                        catch (Exception ex)
                        {
                            // Log exception
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }
                        }
                        finally
                        {
                            command?.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                }

                //gbl.InsertGPSDetails(Convert.ToInt32(Obj.Company_ID), Convert.ToInt32(Obj.Branch), Convert.ToInt32(Obj.User_ID), Convert.ToInt32(Common.GetObjectID("CompanyParameter")), ID, 0, 0, "Delete", false, Convert.ToInt32(Obj.MenuID));
                Msg += CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (Exception ex)
            {
                if (ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    Msg += CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
            }
            return new JsonResult(Msg);
        }
        #endregion
        #region Vinay n 26/9/24
        public static IActionResult DuplicateValidate(DuplicateValidateList Obj, string connString, int LogException)
        {
            var jsonobj = default(dynamic);
            string IsDuplicate = "false";
            string Permitted_Value = string.Empty;
            List<GNM_CompParam> list = new List<GNM_CompParam>();
            try
            {
                string Param_Name = Common.DecryptString(Obj.Param_Name.ToString());
                int CompanyParam_ID = Convert.ToInt32(Obj.CompanyParam_ID);
                int Company_ID = Convert.ToInt32(Obj.Company_ID);
                GNM_CompParam CP = new GNM_CompParam();
                //GNM_CompParam CP = CompanyClient.GNM_CompParam.Where(C => C.Company_ID == Company_ID && C.CompanyParam_ID != CompanyParam_ID && C.Param_Name == Param_Name).FirstOrDefault();
                //Permitted_Value = CompanyClient.GNM_Setup_Parameter.Where(A => A.Paramater_name == Param_Name).Select(B => B.Permitted_values).FirstOrDefault();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_Check_AM_ERP_DuplicateValidate_CompanyParameter";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;

                            command.Parameters.AddWithValue("@Company_ID", Company_ID);
                            command.Parameters.AddWithValue("@CompanyParam_ID", CompanyParam_ID);
                            command.Parameters.AddWithValue("@Param_Name", Param_Name);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    CP = new GNM_CompParam
                                    {
                                        CompanyParam_ID = (int)reader["CompanyParam_ID"],
                                        Company_ID = (int)reader["Company_ID"],
                                        Param_Name = reader["Param_Name"].ToString(),
                                        Param_value = reader["Param_value"].ToString()
                                    };
                                    list.Add(CP);
                                }
                                if (reader.NextResult())
                                {
                                    if (reader.Read())
                                    {
                                        Permitted_Value = reader["Permitted_Value"].ToString();

                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log exception
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                    }
                    finally
                    {
                        command?.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                IsDuplicate = list.Count == 0 ? "false" : "true";
            }
            catch (Exception ex)
            {
                if (LogException == 1) LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            jsonobj = new
            {
                IsDuplicate,
                Permitted_Value
            };
            return new JsonResult(jsonobj);
        }
        #endregion
        public static List<GNM_CompParam> SelectList(SelectCompanyParameterList Obj, int LogException, string constring)
        {
            var jsonobj = default(dynamic);
            var companyParams = new List<GNM_CompParam>();
            IQueryable<GNM_CompParam> IQCompanyParameter = null;
            var setupParams = new List<GNM_Setup_Parameter>();
            try
            {

                int Company_ID = Convert.ToInt32(Obj.Company_ID);
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    string query = "UP_SELECT_AM_ERP_Select_CompanyParameter";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;

                            command.Parameters.AddWithValue("@Company_ID", Company_ID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                // First result set: MovementTypeDefinition data

                                while (reader.Read())
                                {
                                    GNM_CompParam compParam = new GNM_CompParam
                                    {
                                        CompanyParam_ID = (int)reader["CompanyParam_ID"],
                                        Company_ID = (int)reader["Company_ID"],
                                        Param_Name = reader["Param_Name"].ToString(),
                                        Param_value = reader["Param_value"].ToString()
                                    };
                                    companyParams.Add(compParam);
                                }

                                if (reader.NextResult())
                                {

                                    while (reader.Read())
                                    {
                                        GNM_Setup_Parameter setupParam = new GNM_Setup_Parameter
                                        {
                                            Parameter_id = (int)reader["Parameter_id"],
                                            Object_Name = reader["Object_Name"].ToString(),
                                            Paramater_name = reader["Paramater_name"].ToString(),
                                            Permitted_values = reader["Permitted_values"].ToString(),
                                            Remarks = reader["Remarks"].ToString()
                                        };
                                        setupParams.Add(setupParam);
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log exception
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                    }
                    finally
                    {
                        command?.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                IQCompanyParameter = companyParams.AsQueryable();
                ////Added By Sridhar U for FSM CR-5 Changes
                IEnumerable<GNM_Setup_Parameter> gnmCompanyparams = setupParams.AsEnumerable();
                string companyParamNames = "-1:--Select--;";
                for (int i = 0; i < gnmCompanyparams.Count(); i++)
                {
                    companyParamNames = companyParamNames + gnmCompanyparams.ElementAt(i).Paramater_name + ":" + gnmCompanyparams.ElementAt(i).Paramater_name.Replace(";", ":") + ";";
                }
                companyParamNames = companyParamNames.TrimEnd(new char[] { ';' });
                //Change Ends

                //Filter
                if (Obj.filters.ToString() != "null" && Obj.filters.ToString() != "undefined")
                {
                    string decodedValue = Uri.UnescapeDataString(Obj.filters);
                    Filters filtersObj = JObject.Parse(Common.DecryptString(decodedValue)).ToObject<Filters>();
                    if (filtersObj.rules.Count() > 0)
                        IQCompanyParameter = IQCompanyParameter.FilterSearch<GNM_CompParam>(filtersObj);
                }
                //advance filter
                if (Obj.advanceFilter != null && Obj.advanceFilter.ToString() != "null" && Obj.advanceFilter.ToString() != "undefined")
                {
                    string decodedValue = Uri.UnescapeDataString(Obj.advanceFilter);
                    AdvanceFilter advnfilter = JObject.Parse(decodedValue).ToObject<AdvanceFilter>();
                    IQCompanyParameter = IQCompanyParameter.AdvanceSearch<GNM_CompParam>(advnfilter);
                }

                //sorting
                IQCompanyParameter = IQCompanyParameter.OrderByField<GNM_CompParam>(Obj.sidx, Obj.sord);
                //TempData["Export"] = IQCompanyParameter.ToList();





            }
            catch (Exception ex)
            {
                if (LogException == 1) LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return IQCompanyParameter.ToList();
        }
        #region ::: Export Vinay n 26/9/24:::
        /// <summary>
        /// Export
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        public static async Task<object> Export(SelectCompanyParameterList Obj, string connString, int LogException)
        {
            DataTable dt = new DataTable();
            try
            {
                List<GNM_CompParam> Data = (List<GNM_CompParam>)SelectList(Obj, LogException, connString);


                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.GeneralCulture.ToString(), "ParamName").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(Obj.GeneralCulture.ToString(), "ParamValue").ToString());
                int cnt = Data.Count;
                for (int i = 0; i < cnt; i++)
                {
                    dt.Rows.Add(Data[i].Param_Name, Data[i].Param_value);
                }
                DataTable DtAlignment = new DataTable();
                DtAlignment.Columns.Add("ParamName");
                DtAlignment.Columns.Add("ParamValue");
                DtAlignment.Rows.Add(0, 0);
                ExportList reportExportList = new ExportList
                {
                    Company_ID = Obj.Company_ID, // Assuming this is available in ExportObj
                    Branch = Obj.Branch_ID,
                    dt1 = DtAlignment,



                    dt = dt,

                    FileName = "CompanyParameter", // Set a default or dynamic filename
                    Header = CommonFunctionalities.GetResourceString(Obj.UserCulture.ToString(), "CompanyParameter").ToString(), // Set a default or dynamic header
                    exprtType = Obj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                    UserCulture = Obj.UserCulture
                };


                var result = await DocumentExport.Export(reportExportList, connString, LogException);
                return result.Value;




            }
            catch (Exception ex)
            {
                if (LogException == 1) LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return null;
        }
        #endregion
    }
    #region CompanyParameterListsAndClasses vinay 26/9/24
    public class SelectCompanyParameterList
    {
        public int Company_ID { get; set; }
        //export
        public string filters { get; set; }
        public string advanceFilter { get; set; }
        public string sord { get; set; }
        public string sidx { get; set; }
        public string GeneralCulture { get; set; }
        public int Branch_ID { get; set; }
        public string UserCulture { get; set; }
        public int exprtType { get; set; }
    }
    public class SaveCompanyParameterList
    {
        public int Company_ID { get; set; }
        public string data { get; set; }
        public int Branch { get; set; }
        public int User_ID { get; set; }

        public int MenuID { get; set; }

    }

    public class DeleteCompanyParameterList
    {
        public string key { get; set; }
        public string Company_ID { get; set; }
        public int Branch { get; set; }
        public string UserCulture { get; set; }
        public int User_ID { get; set; }
        public int MenuID { get; set; }
    }
    public class DuplicateValidateList
    {
        public string Param_Name { get; set; }
        public int CompanyParam_ID { get; set; }
        public int Company_ID { get; set; }
    }
    #endregion
    #region CompanyParameterMasterClasses vinay 26/9/24
    /// <summary>
    /// CompanyParameterMasterClasses
    /// </summary>
    public partial class GNM_CompParam
    {
        public int CompanyParam_ID { get; set; }
        public int Company_ID { get; set; }
        public string Param_Name { get; set; }
        public string Param_value { get; set; }
    }
    public partial class GNM_Setup_Parameter
    {
        public int Parameter_id { get; set; }
        public string Object_Name { get; set; }
        public string Paramater_name { get; set; }
        public string Permitted_values { get; set; }
        public string Remarks { get; set; }
    }
    #endregion
}
