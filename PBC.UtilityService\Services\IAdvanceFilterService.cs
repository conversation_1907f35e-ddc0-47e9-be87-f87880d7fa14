using PBC.UtilityService.Utilities.Models;
using PBC.UtilityService.Utilities.DTOs;

namespace PBC.UtilityService.Services
{
    /// <summary>
    /// Interface for AdvanceFilter service operations
    /// </summary>
    public interface IAdvanceFilterService
    {
        /// <summary>
        /// Gets all advance filters
        /// </summary>
        /// <returns>Collection of advance filters</returns>
        Task<IEnumerable<AdvanceFilterDto>> GetAllAsync();

        /// <summary>
        /// Gets advance filter by ID
        /// </summary>
        /// <param name="id">The filter ID</param>
        /// <returns>Advance filter if found, null otherwise</returns>
        Task<AdvanceFilterDto?> GetByIdAsync(int id);

        /// <summary>
        /// Gets advance filters by name (partial match)
        /// </summary>
        /// <param name="name">The filter name to search</param>
        /// <returns>Collection of matching advance filters</returns>
        Task<IEnumerable<AdvanceFilterDto>> GetByNameAsync(string name);

        /// <summary>
        /// Gets only active advance filters
        /// </summary>
        /// <returns>Collection of active advance filters</returns>
        Task<IEnumerable<AdvanceFilterDto>> GetActiveFiltersAsync();

        /// <summary>
        /// Creates new advance filter
        /// </summary>
        /// <param name="request">Create request</param>
        /// <returns>Created advance filter</returns>
        Task<AdvanceFilterDto> CreateAsync(CreateAdvanceFilterRequest request);

        /// <summary>
        /// Updates existing advance filter
        /// </summary>
        /// <param name="id">The filter ID</param>
        /// <param name="request">Update request</param>
        /// <returns>Updated advance filter if found, null otherwise</returns>
        Task<AdvanceFilterDto?> UpdateAsync(int id, UpdateAdvanceFilterRequest request);

        /// <summary>
        /// Deletes advance filter by ID
        /// </summary>
        /// <param name="id">The filter ID</param>
        /// <returns>True if deleted, false if not found</returns>
        Task<bool> DeleteAsync(int id);

        /// <summary>
        /// Checks if advance filter exists for the given ID
        /// </summary>
        /// <param name="id">The filter ID</param>
        /// <returns>True if exists, false otherwise</returns>
        Task<bool> ExistsAsync(int id);

        /// <summary>
        /// Validates a filter and generates SQL WHERE clause
        /// </summary>
        /// <param name="id">The filter ID</param>
        /// <returns>Validation response with SQL generation</returns>
        Task<FilterValidationResponse> ValidateAndGenerateSqlAsync(int id);

        /// <summary>
        /// Validates filter rules and generates SQL WHERE clause
        /// </summary>
        /// <param name="request">Filter request to validate</param>
        /// <returns>Validation response with SQL generation</returns>
        Task<FilterValidationResponse> ValidateAndGenerateSqlAsync(CreateAdvanceFilterRequest request);

        /// <summary>
        /// Duplicates an existing filter with a new name
        /// </summary>
        /// <param name="id">The filter ID to duplicate</param>
        /// <param name="newName">New name for the duplicated filter</param>
        /// <returns>Duplicated advance filter if original found, null otherwise</returns>
        Task<AdvanceFilterDto?> DuplicateAsync(int id, string newName);

        /// <summary>
        /// Gets rules for a specific filter
        /// </summary>
        /// <param name="filterId">The filter ID</param>
        /// <returns>Collection of rules for the filter</returns>
        Task<IEnumerable<RulesDto>> GetRulesByFilterIdAsync(int filterId);

        /// <summary>
        /// Adds a rule to an existing filter
        /// </summary>
        /// <param name="filterId">The filter ID</param>
        /// <param name="rule">Rule to add</param>
        /// <returns>Updated advance filter if found, null otherwise</returns>
        Task<AdvanceFilterDto?> AddRuleAsync(int filterId, CreateRulesRequest rule);

        /// <summary>
        /// Removes a rule from a filter
        /// </summary>
        /// <param name="filterId">The filter ID</param>
        /// <param name="ruleId">The rule ID to remove</param>
        /// <returns>Updated advance filter if found, null otherwise</returns>
        Task<AdvanceFilterDto?> RemoveRuleAsync(int filterId, int ruleId);
    }
}
