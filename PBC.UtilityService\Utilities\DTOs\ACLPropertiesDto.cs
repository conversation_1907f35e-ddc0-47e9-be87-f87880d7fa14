using System.ComponentModel.DataAnnotations;

namespace PBC.UtilityService.Utilities.DTOs
{
    /// <summary>
    /// Data Transfer Object for ACL Properties
    /// </summary>
    public class ACLPropertiesDto
    {
        /// <summary>
        /// Gets or sets the Object ID
        /// </summary>
        [Required]
        public int Object_ID { get; set; }

        /// <summary>
        /// Gets or sets the role object create permission
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "RoleObject_Create must be a non-negative integer")]
        public int RoleObject_Create { get; set; }

        /// <summary>
        /// Gets or sets the role object read permission
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "RoleObject_Read must be a non-negative integer")]
        public int RoleObject_Read { get; set; }

        /// <summary>
        /// Gets or sets the role object update permission
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "RoleObject_Update must be a non-negative integer")]
        public int RoleObject_Update { get; set; }

        /// <summary>
        /// Gets or sets the role object delete permission
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "RoleObject_Delete must be a non-negative integer")]
        public int RoleObject_Delete { get; set; }

        /// <summary>
        /// Gets or sets the role object print permission
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "RoleObject_Print must be a non-negative integer")]
        public int RoleObject_Print { get; set; }

        /// <summary>
        /// Gets or sets the role object export permission
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "RoleObject_Export must be a non-negative integer")]
        public int RoleObject_Export { get; set; }

        /// <summary>
        /// Gets or sets the role object import permission
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "RoleObject_Import must be a non-negative integer")]
        public int RoleObject_Import { get; set; }
    }

    /// <summary>
    /// Request DTO for creating ACL Properties
    /// </summary>
    public class CreateACLPropertiesRequest
    {
        /// <summary>
        /// Gets or sets the Object ID
        /// </summary>
        [Required]
        public int Object_ID { get; set; }

        /// <summary>
        /// Gets or sets the role object create permission
        /// </summary>
        public int RoleObject_Create { get; set; }

        /// <summary>
        /// Gets or sets the role object read permission
        /// </summary>
        public int RoleObject_Read { get; set; }

        /// <summary>
        /// Gets or sets the role object update permission
        /// </summary>
        public int RoleObject_Update { get; set; }

        /// <summary>
        /// Gets or sets the role object delete permission
        /// </summary>
        public int RoleObject_Delete { get; set; }

        /// <summary>
        /// Gets or sets the role object print permission
        /// </summary>
        public int RoleObject_Print { get; set; }

        /// <summary>
        /// Gets or sets the role object export permission
        /// </summary>
        public int RoleObject_Export { get; set; }

        /// <summary>
        /// Gets or sets the role object import permission
        /// </summary>
        public int RoleObject_Import { get; set; }
    }

    /// <summary>
    /// Request DTO for updating ACL Properties
    /// </summary>
    public class UpdateACLPropertiesRequest
    {
        /// <summary>
        /// Gets or sets the role object create permission
        /// </summary>
        public int? RoleObject_Create { get; set; }

        /// <summary>
        /// Gets or sets the role object read permission
        /// </summary>
        public int? RoleObject_Read { get; set; }

        /// <summary>
        /// Gets or sets the role object update permission
        /// </summary>
        public int? RoleObject_Update { get; set; }

        /// <summary>
        /// Gets or sets the role object delete permission
        /// </summary>
        public int? RoleObject_Delete { get; set; }

        /// <summary>
        /// Gets or sets the role object print permission
        /// </summary>
        public int? RoleObject_Print { get; set; }

        /// <summary>
        /// Gets or sets the role object export permission
        /// </summary>
        public int? RoleObject_Export { get; set; }

        /// <summary>
        /// Gets or sets the role object import permission
        /// </summary>
        public int? RoleObject_Import { get; set; }
    }
}
