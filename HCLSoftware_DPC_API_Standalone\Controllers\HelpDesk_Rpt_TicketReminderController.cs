﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.HelpDesk_Rpt_TicketReminderServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class HelpDesk_Rpt_TicketReminderController : ApiController
    {



        #region ::: LoadMachineTypeDropdown Uday Kumar J B 14-11-2024:::
        /// <summary>
        ////to Select Machine types based on the Brand
        /// </summary> 
        ///
        [Route("api/HelpDesk_Rpt_TicketReminder/LoadProductType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadProductType([FromBody] HelpDesk_Rpt_TicketReminderLoadProductTypeList HelpDesk_Rpt_TicketReminderLoadProductTypeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Rpt_TicketReminderServices.LoadProductType(HelpDesk_Rpt_TicketReminderLoadProductTypeobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: LoadModel Uday Kumar J B 14-11-2024:::
        /// <summary>
        ////to Select model based on the product type
        /// </summary> 
        /// 
        [Route("api/HelpDesk_Rpt_TicketReminder/LoadModel")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadModel([FromBody] HelpDesk_Rpt_TicketReminderLoadModelList HelpDesk_Rpt_TicketReminderLoadModelobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Rpt_TicketReminderServices.LoadModel(HelpDesk_Rpt_TicketReminderLoadModelobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: SelectBrand Uday Kumar J B 14-11-2024:::
        /// <summary>
        /// To select Brand
        /// </summary>  
        /// 
        [Route("api/HelpDesk_Rpt_TicketReminder/SelectBrandTypes")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectBrandTypes([FromBody] HelpDesk_Rpt_TicketReminderLoadModelList HelpDesk_Rpt_TicketReminderLoadModelobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Rpt_TicketReminderServices.SelectBrandTypes(HelpDesk_Rpt_TicketReminderLoadModelobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: SelectCustomerDueService Uday Kumar J B 14-11-2024:::
        /// <summary>
        /// to get the logged in users permission
        /// </summary>  
        [Route("api/HelpDesk_Rpt_TicketReminder/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] HelpDesk_Rpt_TicketReminderSelectList HelpDesk_Rpt_TicketReminderSelectobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Rpt_TicketReminderServices.Select(HelpDesk_Rpt_TicketReminderSelectobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion



        #region ::: SendReminder Uday Kumar J B 14-11-2024:::
        /// <summary>
        /// Send Reminder with email
        /// </summary> 
        [Route("api/HelpDesk_Rpt_TicketReminder/SendReminder")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SendReminder([FromBody] HelpDesk_Rpt_TicketReminderSendReminderlList HelpDesk_Rpt_TicketReminderSendReminderlobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDesk_Rpt_TicketReminderServices.SendReminder(HelpDesk_Rpt_TicketReminderSendReminderlobj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region ::: getPartyContactPersonDetails Uday Kumar J B 14-11-2024:::
        /// <summary>
        /// get Party Contact Person Details
        /// </summary>
        [Route("api/HelpDesk_Rpt_TicketReminder/getPartyContactPersonDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult getPartyContactPersonDetails([FromBody] HelpDesk_Rpt_TicketRemindergetPartyContactPersonDetailslList HelpDesk_Rpt_TicketRemindergetPartyContactPersonDetailslobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDesk_Rpt_TicketReminderServices.getPartyContactPersonDetails(HelpDesk_Rpt_TicketRemindergetPartyContactPersonDetailslobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion



        #region :::Export Uday Kumar J B 13-11-2024:::
        /// <summary>
        /// Export
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/HelpDesk_Rpt_TicketReminder/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] HelpDesk_Rpt_TicketReminderSelectList HelpDesk_Rpt_TicketReminderSelectobj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HelpDesk_Rpt_TicketReminderSelectobj.sidx;
            string sord = HelpDesk_Rpt_TicketReminderSelectobj.sord;
            string filter = HelpDesk_Rpt_TicketReminderSelectobj.filter;
            string advnceFilter = HelpDesk_Rpt_TicketReminderSelectobj.advanceFilter;

            try
            {


                Object Response = await HelpDesk_Rpt_TicketReminderServices.Export(HelpDesk_Rpt_TicketReminderSelectobj, connstring, LogException, filter, advnceFilter, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion



    }
}