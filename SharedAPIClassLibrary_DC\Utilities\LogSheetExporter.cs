﻿using System;
using System.IO;
using System.Text;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP.Utilities
{
    public class LogSheetExporter
    {
        private static string _LogFileName = "C:\\HCLTFS_SW\\AM_ERP\\API_DRIVEN\\AMERP-Microservice\\HCLSoftware_AMERP_API\\SharedAPIClassLibrary_DC\\Utilities\\ErrorLog.txt";

        public static void LogToTextFile(int ExId, string ExMessage, string ExDetails, string ExStackTrace)
        {
            try
            {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.AppendFormat("Exception Date:{0}", DateTime.Now);
                stringBuilder.AppendFormat("{0}Exception From:", Environment.NewLine);
                stringBuilder.AppendFormat("{0}{1}: {2}", Environment.NewLine, ExMessage, ExId);
                stringBuilder.AppendFormat("{0}{0}Exception Information{0}{1}", Environment.NewLine, ExDetails);
                stringBuilder.AppendFormat("{0}{0}Stack Trace{0}{1}", Environment.NewLine, ExStackTrace);
                StreamWriter streamWriter = File.AppendText(_LogFileName);
                streamWriter.WriteLine(stringBuilder.ToString());
                streamWriter.WriteLine("----------------------------------");
            }
            catch (Exception ex)
            {


                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

            }
        }
    }
}
