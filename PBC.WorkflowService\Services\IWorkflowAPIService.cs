using PBC.WorkflowService.Models;

namespace PBC.WorkflowService.Services
{
    public interface IWorkflowAPIService
    {
        void InsertWorkFlowHistory(string conn, CaseProgressObjects CPDetails, SMSTemplate SMSCutomerObj, SMSTemplate SMSAssigneeObj, int Branch_ID = 0);
        bool CheckIsInvokeChildObject(int companyID, int workFlowID, int stepID, int ActionID, int tostepid);
        string InvokeChildAction(int steplinkID);
        DateTime LocalTime(string Conn, int Branch_ID, DateTime servertime);
    }
}
