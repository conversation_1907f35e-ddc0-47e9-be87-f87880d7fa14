﻿using AMMSCore.Models;
using DocumentFormat.OpenXml.Office.Word;
using DocumentFormat.OpenXml.Office2010.Excel;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.PRM_AllocationPriorityServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP.Services
{
    public class CalculationServices
    {
        #region WAC_calculation
        /// <summary>
        /// WAC_calculation
        /// </summary>
        /// <param name="partID"></param>
        /// <param name="wareHouseID"></param>
        /// <param name="binlocationID"></param>
        /// <param name="receiptQty"></param>
        /// <param name="landingRate"></param>
        /// <param name="allocation"></param>
        /// <param name="GITUpdate"></param>
        /// <param name="TotalStockUpdate"></param>
        /// <param name="damageQty"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static WAC WAC_calculation(int partID, int wareHouseID, int binlocationID, decimal receiptQty, decimal landingRate, bool allocation, bool GITUpdate, bool TotalStockUpdate, decimal damageQty, string connString, int LogException)
        {
            WAC returnWAC = new WAC();
            GNM_PartsStockDetail partsStockDetails = new GNM_PartsStockDetail();
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                    {
                        conn.Open();
                    }
                    string query = @"
                SELECT 
                    PartsStockDetail_ID, Parts_ID, Branch_ID, Company_ID, WareHouse_ID, FreeStock, AllocatedQuantity, 
                    PickedQuantity, ReservedQuantity, BackOrderQuantity, PendingPurchaseOrderQuantity, PendingPartsOrderQuantity, 
                    DeviationStock, StockUsedInKits, ReOrderLevel, ReOrderLevelQuantity, MinOrderQty, GITQuantity, BinStock, 
                    TotalStock, WeightedAverageCost, BinLocation_ID, BinlocationBuffer_ID, OldBinLocation_ID, OldBufferBinLocation_ID, 
                    IsBlocked, LastStockUpdatedDate, Movement_ID, FirstDemandDate, LastDemandDate, FirstIssuedDate, LastIssuedDate, 
                    LastStockCheckDate, MaximumStockLevel
                FROM GNM_PartsStockDetail
                WHERE Parts_ID = @PartsID AND WareHouse_ID = @WareHouseID";
                    SqlCommand command = new SqlCommand(query, conn);
                    command.Parameters.AddWithValue("@PartsID", partID);
                    command.Parameters.AddWithValue("@WareHouseID", wareHouseID);
                    SqlDataReader reader = command.ExecuteReader();
                    if (reader.Read())
                    {
                        partsStockDetails = new GNM_PartsStockDetail
                        {
                            PartsStockDetail_ID = Convert.ToInt32(reader["PartsStockDetail_ID"]),
                            Parts_ID = Convert.ToInt32(reader["Parts_ID"]),
                            Branch_ID = Convert.ToInt32(reader["Branch_ID"]),
                            Company_ID = Convert.ToInt32(reader["Company_ID"]),
                            WareHouse_ID = reader["WareHouse_ID"] as int?,
                            FreeStock = reader["FreeStock"] as decimal?,
                            AllocatedQuantity = reader["AllocatedQuantity"] as decimal?,
                            PickedQuantity = reader["PickedQuantity"] as decimal?,
                            ReservedQuantity = reader["ReservedQuantity"] as decimal?,
                            BackOrderQuantity = reader["BackOrderQuantity"] as decimal?,
                            PendingPurchaseOrderQuantity = reader["PendingPurchaseOrderQuantity"] as decimal?,
                            PendingPartsOrderQuantity = reader["PendingPartsOrderQuantity"] as decimal?,
                            DeviationStock = reader["DeviationStock"] as decimal?,
                            StockUsedInKits = reader["StockUsedInKits"] as decimal?,
                            ReOrderLevel = Convert.ToInt32(reader["ReOrderLevel"]),
                            ReOrderLevelQuantity = Convert.ToDecimal(reader["ReOrderLevelQuantity"]),
                            MinOrderQty = Convert.ToDecimal(reader["MinOrderQty"]),
                            GITQuantity = reader["GITQuantity"] as decimal?,
                            BinStock = reader["BinStock"] as decimal?,
                            TotalStock = reader["TotalStock"] as decimal?,
                            WeightedAverageCost = reader["WeightedAverageCost"] as decimal?,
                            BinLocation_ID = Convert.ToInt32(reader["BinLocation_ID"]),
                            BinlocationBuffer_ID = reader["BinlocationBuffer_ID"] as int?,
                            OldBinLocation_ID = reader["OldBinLocation_ID"] as int?,
                            OldBufferBinLocation_ID = reader["OldBufferBinLocation_ID"] as int?,
                            IsBlocked = Convert.ToBoolean(reader["IsBlocked"]),
                            LastStockUpdatedDate = reader["LastStockUpdatedDate"] as DateTime?,
                            Movement_ID = reader["Movement_ID"] as int?,
                            FirstDemandDate = reader["FirstDemandDate"] as DateTime?,
                            LastDemandDate = reader["LastDemandDate"] as DateTime?,
                            FirstIssuedDate = reader["FirstIssuedDate"] as DateTime?,
                            LastIssuedDate = reader["LastIssuedDate"] as DateTime?,
                            LastStockCheckDate = reader["LastStockCheckDate"] as DateTime?,
                            MaximumStockLevel = reader["MaximumStockLevel"] as decimal?
                        };
                    }

                }


                if (partsStockDetails != null)
                {
                    decimal newWAC = ((partsStockDetails.BinStock != null ? partsStockDetails.BinStock.Value : 0) + receiptQty) != 0 ? (((partsStockDetails.BinStock != null ? partsStockDetails.BinStock.Value : 0) * (partsStockDetails.WeightedAverageCost != null ? partsStockDetails.WeightedAverageCost.Value : 0)) + (receiptQty * landingRate)) / ((partsStockDetails.BinStock != null ? partsStockDetails.BinStock.Value : 0) + receiptQty) : landingRate;
                    //Modified by: Kiran Date:25-11-2013 QA-Iteration-Missed out scenarios Changes Begin
                    returnWAC.ClosingWAC = Convert.ToDecimal(newWAC.ToString("0.00"));
                    //Changes Ends
                    returnWAC.ClosingStock = (partsStockDetails.BinStock != null ? partsStockDetails.BinStock.Value : 0) + receiptQty;
                    returnWAC.partID = partID;
                    returnWAC.wareHouseID = wareHouseID;
                }
                else
                {
                    returnWAC.ClosingWAC = landingRate;
                    returnWAC.ClosingStock = 0;
                    returnWAC.partID = partID;
                    returnWAC.wareHouseID = wareHouseID;
                }
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return returnWAC;
        }
        #endregion
        #region BackOrder_Allocation vinay n
        /// <summary>
        /// BackOrder_Allocation
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <param name="partID"></param>
        /// <param name="wareHouseID"></param>
        /// <param name="partsOrderID"></param>
        /// <param name="Company_ID"></param>
        /// <param name="branchID"></param>
        public static void BackOrder_Allocation(BackOrder_AllocationList Obj, string connString, int LogException, int partID, int wareHouseID, int partsOrderID, int Company_ID = 0, int branchID = 0)
        {
            try
            {

                if (Company_ID == 0) { Company_ID = Convert.ToInt32(Obj.Company_ID); }
                if (branchID == 0) { branchID = Convert.ToInt32(Obj.Branch); }

                List<int> partsOrderIDs = new List<int>();
                List<PartsOrderResult> resultList = new List<PartsOrderResult>();
                List<int> partsOrderBackOrderPartsDetails = new List<int>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = @"
                        SELECT a.PartsOrder_ID
                        FROM PRT_PartsOrderPartsDetails a
                        JOIN PRT_PartsOrderAllocation b
                            ON a.PartsOrderPartsDetail_ID = b.PartsOrderPartsDetail_ID
                        JOIN PRT_PartsOrder c
                            ON a.PartsOrder_ID = c.PartsOrder_ID
                        WHERE a.Parts_ID = @PartID
                            AND c.Company_ID = @Company_ID
                            AND b.WareHouse_ID = @WareHouseID
                            AND b.BackOrderQuantity > 0";




                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.Text;
                            command.Parameters.AddWithValue("@PartID", partID);
                            command.Parameters.AddWithValue("@Company_ID", Company_ID);
                            command.Parameters.AddWithValue("@WareHouseID", wareHouseID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {


                                while (reader.Read())
                                {
                                    resultList.Add(new PartsOrderResult
                                    {
                                        PartsOrder_ID = reader.GetInt32(reader.GetOrdinal("PartsOrder_ID"))
                                    });
                                }



                            }

                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                       
                    }
                }
                var x = (from a in resultList
                         select new
                         {
                             a.PartsOrder_ID
                         });
                foreach (var obj in x)
                {
                    partsOrderBackOrderPartsDetails.Add(obj.PartsOrder_ID);
                }
                SelBackOrderPartsOrderSortedOnPriorityList objList = new SelBackOrderPartsOrderSortedOnPriorityList()
                {
                    Company_ID = Obj.Company_ID,
                    Branch = Obj.Branch,
                };
                partsOrderIDs = SelBackOrderPartsOrderSortedOnPriority(objList, connString, LogException, partID, partsOrderBackOrderPartsDetails, true);

                if (partsOrderID == 0)
                {
                    AllocateBackOrderQuantity(connString, LogException, partsOrderIDs, partID, wareHouseID);
                }
                else
                {
                    // partsOrderIDs.Add(partsOrderID);
                   

                    decimal allocatedQuantity = 0;
                    int singlePartsorderID = partsOrderID;
                    decimal freeSTK = 0;
                    decimal? allocatedStock = null;
                    decimal? backOrderStock = null;
                    decimal backOrderQuantity = 0;
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string stockQuery = @"SELECT FreeStock, AllocatedQuantity, BackOrderQuantity 
                               FROM GNM_PartsStockDetail 
                               WHERE Parts_ID = @Parts_ID AND WareHouse_ID = @WareHouse_ID";
                        SqlCommand command = null;
                        try
                        {
                            using (command = new SqlCommand(stockQuery, conn))
                            {
                                command.CommandType = CommandType.Text;
                                command.Parameters.AddWithValue("@Parts_ID", partID);
                                command.Parameters.AddWithValue("@WareHouse_ID", wareHouseID);
                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        freeSTK = reader["FreeStock"] != DBNull.Value ? Convert.ToDecimal(reader["FreeStock"]) : 0;
                                        allocatedStock = reader["AllocatedQuantity"] != DBNull.Value ? (decimal?)reader["AllocatedQuantity"] : null;
                                        backOrderStock = reader["BackOrderQuantity"] != DBNull.Value ? (decimal?)reader["BackOrderQuantity"] : null;
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = @"
                             SELECT TOP 1 ISNULL(a.BackOrderQuantity, 0) AS BackOrderQuantity
                             FROM PRT_PSPartsOrderAllocation a
                             INNER JOIN PRT_PartsOrderPartsDetails b ON a.PRT_PartsOrderPartsDetails_ID = b.PRT_PartsOrderPartsDetails_ID
                             INNER JOIN PRT_PartsOrder c ON b.PRT_PartsOrder_ID = c.PartsOrder_ID
                             WHERE b.Parts_ID = @Parts_ID
                               AND c.PartsOrder_ID = @PartsOrder_ID
                               AND a.WareHouse_ID = @WareHouse_ID";
                        SqlCommand command = null;
                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.Text;
                                command.Parameters.AddWithValue("@Parts_ID", partID);
                                command.Parameters.AddWithValue("@WareHouse_ID", wareHouseID);
                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                object result = command.ExecuteScalar();
                                backOrderQuantity = result != null ? Convert.ToDecimal(result) : 0;
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                    if (freeSTK > 0)
                    {
                        decimal freestock = freeSTK != null ? freeSTK : 0;
                        if (freestock > backOrderQuantity)
                        {
                            allocatedQuantity = backOrderQuantity;
                        }
                        else
                        {
                            allocatedQuantity = freestock;
                        }
                        
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            string selectQuery = @"
                             SELECT TOP 1 AllocatedQuantity, BackOrderQuantity
                             FROM PRT_PSPartsOrderAllocation a
                             INNER JOIN PRT_PartsOrderPartsDetails b ON a.PRT_PartsOrderPartsDetails_ID = b.PRT_PartsOrderPartsDetails_ID
                             INNER JOIN PRT_PartsOrder c ON b.PRT_PartsOrder_ID = c.PartsOrder_ID
                             WHERE b.Parts_ID = @Parts_ID
                               AND c.PartsOrder_ID = @PartsOrder_ID
                               AND a.WareHouse_ID = @WareHouse_ID";
                            string updateQuery = @"
                                 UPDATE PRT_PSPartsOrderAllocation
                                 SET AllocatedQuantity = @AllocatedQuantity,
                                     BackOrderQuantity = @BackOrderQuantity
                                 WHERE PRT_PartsOrderPartsDetails_ID = (
                                     SELECT TOP 1 b.PRT_PartsOrderPartsDetails_ID
                                     FROM PRT_PSPartsOrderAllocation a
                                     INNER JOIN PRT_PartsOrderPartsDetails b ON a.PRT_PartsOrderPartsDetails_ID = b.PRT_PartsOrderPartsDetails_ID
                                     INNER JOIN PRT_PartsOrder c ON b.PRT_PartsOrder_ID = c.PartsOrder_ID
                                     WHERE b.Parts_ID = @Parts_ID
                                       AND c.PartsOrder_ID = @PartsOrder_ID
                                       AND a.WareHouse_ID = @WareHouse_ID
                                 )";

                            SqlCommand command = null;
                            try
                            {
                                using (command = new SqlCommand(selectQuery, conn))
                                {
                                    command.CommandType = CommandType.Text;
                                    command.Parameters.AddWithValue("@Parts_ID", partID);
                                    command.Parameters.AddWithValue("@WareHouse_ID", wareHouseID);
                                   
                                    decimal? allocatedQuantityDb = null;
                                    decimal? backOrderQuantityDb = null;
                                    using (SqlDataReader reader = command.ExecuteReader())
                                    {
                                        if (reader.Read())
                                        {
                                            allocatedQuantityDb = reader["AllocatedQuantity"] != DBNull.Value ? Convert.ToDecimal(reader["AllocatedQuantity"]) : 0;
                                            backOrderQuantityDb = reader["BackOrderQuantity"] != DBNull.Value ? Convert.ToDecimal(reader["BackOrderQuantity"]) : 0;
                                        }
                                    }
                                    decimal newAllocatedQuantity = (allocatedQuantityDb ?? 0) + allocatedQuantity;
                                    decimal? newBackOrderQuantity = backOrderQuantityDb.HasValue ? backOrderQuantityDb.Value - allocatedQuantity : (decimal?)null;
                                    using (SqlCommand updateCommand = new SqlCommand(updateQuery, conn))
                                    {
                                        updateCommand.Parameters.AddWithValue("@AllocatedQuantity", newAllocatedQuantity);
                                        updateCommand.Parameters.AddWithValue("@BackOrderQuantity", (object)newBackOrderQuantity ?? DBNull.Value);
                                        updateCommand.Parameters.AddWithValue("@Parts_ID", partID);
                                        updateCommand.Parameters.AddWithValue("@PartsOrder_ID", singlePartsorderID);
                                        updateCommand.Parameters.AddWithValue("@WareHouse_ID", wareHouseID);

                                        updateCommand.ExecuteNonQuery();
                                    }

                                }
                            }
                            catch (Exception ex)
                            {
                                if (LogException == 1)
                                {
                                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                }

                            }
                            finally
                            {
                                command.Dispose();
                                conn.Close();
                                conn.Dispose();
                                SqlConnection.ClearAllPools();
                            }
                        }

                        allocatedStock = allocatedStock != null ? allocatedStock + allocatedQuantity : allocatedQuantity;
                        backOrderStock = backOrderStock != null ? backOrderStock - allocatedQuantity : null;
                        freeSTK = freeSTK != null ? freeSTK - allocatedQuantity : 0;
                        freestock = freestock - allocatedQuantity;
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            string updateQueryPartsStock = @"UPDATE GNM_PartsStockDetail
                                SET AllocatedQuantity = @AllocatedQuantity,
                                    BackOrderQuantity = @BackOrderQuantity,
                                    FreeStock = @FreeStock
                                WHERE Parts_ID = @Parts_ID AND WareHouse_ID = @WareHouse_ID";

                            SqlCommand command = null;
                            try
                            {
                                using (command = new SqlCommand(updateQueryPartsStock, conn))
                                {
                                    command.CommandType = CommandType.Text;
                                    command.Parameters.AddWithValue("@AllocatedQuantity", allocatedStock);
                                    command.Parameters.AddWithValue("@BackOrderQuantity", (object)backOrderStock ?? DBNull.Value);
                                    command.Parameters.AddWithValue("@FreeStock", freeSTK);
                                    command.Parameters.AddWithValue("@Parts_ID", partID);
                                    command.Parameters.AddWithValue("@WareHouse_ID", wareHouseID);
                                    command.ExecuteNonQuery();

                                }
                            }
                            catch (Exception ex)
                            {
                                if (LogException == 1)
                                {
                                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                }

                            }
                            finally
                            {
                                command.Dispose();
                                conn.Close();
                                conn.Dispose();
                                SqlConnection.ClearAllPools();
                            }
                        }

                        if (freestock > 0)
                        {
                            AllocateBackOrderQuantity(connString, LogException, partsOrderIDs, partID, wareHouseID);
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion
        #region ::: AllocateBackOrderQuantity vinay n  :::
        /// <summary>
        /// AllocateBackOrderQuantity
        /// </summary>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <param name="partsOrderIDs"></param>
        /// <param name="partID"></param>
        /// <param name="wareHouseID"></param>
        public static void AllocateBackOrderQuantity(string connString, int LogException, List<int> partsOrderIDs, int partID, int wareHouseID)
        {
            try
            {


                for (int i = 0; i < partsOrderIDs.Count; i++)
                {
                    decimal freestock = 0;
                    decimal freeSTK = 0;
                    decimal? allocatedStock = null;
                    decimal? backOrderStock = null;
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string stockQuery = @"SELECT FreeStock, AllocatedQuantity, BackOrderQuantity 
                                      FROM GNM_PartsStockDetail 
                                      WHERE Parts_ID = @Parts_ID AND WareHouse_ID = @WareHouse_ID";



                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(stockQuery, conn))
                            {
                                command.CommandType = CommandType.Text;
                                command.Parameters.AddWithValue("@Parts_ID", partID);
                                command.Parameters.AddWithValue("@WareHouse_ID", wareHouseID);

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                using (SqlDataReader reader = command.ExecuteReader())
                                {


                                    if (reader.Read())
                                    {
                                        freeSTK = reader["FreeStock"] != DBNull.Value ? Convert.ToDecimal(reader["FreeStock"]) : 0;
                                        allocatedStock = reader["AllocatedQuantity"] != DBNull.Value ? (decimal?)reader["AllocatedQuantity"] : null;
                                        backOrderStock = reader["BackOrderQuantity"] != DBNull.Value ? (decimal?)reader["BackOrderQuantity"] : null;
                                    }



                                }
                                decimal allocatedQuantity = 0;
                                int singlePartsorderID = partsOrderIDs[i];

                                decimal backOrderQuantity = 0;

                                if (freeSTK > 0)
                                {
                                    freestock = freeSTK != null ? freeSTK : 0;

                                    string query = @"
                                    SELECT TOP 1 ISNULL(a.BackOrderQuantity, 0) AS BackOrderQuantity
                                    FROM PRT_PSPartsOrderAllocation a
                                    INNER JOIN PRT_PartsOrderPartsDetails b ON a.PRT_PartsOrderPartsDetails_ID = b.PRT_PartsOrderPartsDetails_ID
                                    INNER JOIN PRT_PartsOrder c ON b.PRT_PartsOrder_ID = c.PartsOrder_ID
                                    WHERE b.Parts_ID = @Parts_ID
                                      AND c.PartsOrder_ID = @PartsOrder_ID
                                      AND a.WareHouse_ID = @WareHouse_ID";
                                    using (command = new SqlCommand(query, conn))
                                    {
                                        command.Parameters.AddWithValue("@Parts_ID", partID);
                                        command.Parameters.AddWithValue("@PartsOrder_ID", singlePartsorderID);
                                        command.Parameters.AddWithValue("@WareHouse_ID", wareHouseID);

                                        object result = command.ExecuteScalar();
                                        backOrderQuantity = result != null ? Convert.ToDecimal(result) : 0;
                                    }

                                    if (freestock > backOrderQuantity)
                                    {
                                        allocatedQuantity = backOrderQuantity;
                                    }
                                    else
                                    {
                                        allocatedQuantity = freestock;
                                    }
                                    string selectQuery = @"
                                    SELECT TOP 1 AllocatedQuantity, BackOrderQuantity
                                    FROM PRT_PSPartsOrderAllocation a
                                    INNER JOIN PRT_PartsOrderPartsDetails b ON a.PRT_PartsOrderPartsDetails_ID = b.PRT_PartsOrderPartsDetails_ID
                                    INNER JOIN PRT_PartsOrder c ON b.PRT_PartsOrder_ID = c.PartsOrder_ID
                                    WHERE b.Parts_ID = @Parts_ID
                                      AND c.PartsOrder_ID = @PartsOrder_ID
                                      AND a.WareHouse_ID = @WareHouse_ID";
                                    string updateQuery = @"
                                        UPDATE PRT_PSPartsOrderAllocation
                                        SET AllocatedQuantity = @AllocatedQuantity,
                                            BackOrderQuantity = @BackOrderQuantity
                                        WHERE PRT_PartsOrderPartsDetails_ID = (
                                            SELECT TOP 1 b.PRT_PartsOrderPartsDetails_ID
                                            FROM PRT_PSPartsOrderAllocation a
                                            INNER JOIN PRT_PartsOrderPartsDetails b ON a.PRT_PartsOrderPartsDetails_ID = b.PRT_PartsOrderPartsDetails_ID
                                            INNER JOIN PRT_PartsOrder c ON b.PRT_PartsOrder_ID = c.PartsOrder_ID
                                            WHERE b.Parts_ID = @Parts_ID
                                              AND c.PartsOrder_ID = @PartsOrder_ID
                                              AND a.WareHouse_ID = @WareHouse_ID
                                        )";

                                    using (command = new SqlCommand(selectQuery, conn))
                                    {
                                        command.Parameters.AddWithValue("@Parts_ID", partID);
                                        command.Parameters.AddWithValue("@PartsOrder_ID", singlePartsorderID);
                                        command.Parameters.AddWithValue("@WareHouse_ID", wareHouseID);
                                        decimal? allocatedQuantityDb = null;
                                        decimal? backOrderQuantityDb = null;


                                        using (SqlDataReader reader = command.ExecuteReader())
                                        {
                                            if (reader.Read())
                                            {
                                                allocatedQuantityDb = reader["AllocatedQuantity"] != DBNull.Value ? Convert.ToDecimal(reader["AllocatedQuantity"]) : 0;
                                                backOrderQuantityDb = reader["BackOrderQuantity"] != DBNull.Value ? Convert.ToDecimal(reader["BackOrderQuantity"]) : 0;
                                            }
                                        }
                                        decimal newAllocatedQuantity = (allocatedQuantityDb ?? 0) + allocatedQuantity;
                                        decimal? newBackOrderQuantity = backOrderQuantityDb.HasValue ? backOrderQuantityDb.Value - allocatedQuantity : (decimal?)null;
                                        using (SqlCommand updateCommand = new SqlCommand(updateQuery, conn))
                                        {
                                            updateCommand.Parameters.AddWithValue("@AllocatedQuantity", newAllocatedQuantity);
                                            updateCommand.Parameters.AddWithValue("@BackOrderQuantity", (object)newBackOrderQuantity ?? DBNull.Value);
                                            updateCommand.Parameters.AddWithValue("@Parts_ID", partID);
                                            updateCommand.Parameters.AddWithValue("@PartsOrder_ID", singlePartsorderID);
                                            updateCommand.Parameters.AddWithValue("@WareHouse_ID", wareHouseID);

                                            updateCommand.ExecuteNonQuery();
                                        }
                                    }




                                    allocatedStock = allocatedStock != null ? allocatedStock + allocatedQuantity : allocatedQuantity;
                                    backOrderStock = backOrderStock != null ? backOrderStock - allocatedQuantity : null;
                                    freeSTK = freeSTK != null ? freeSTK - allocatedQuantity : 0;
                                    freestock = freestock - allocatedQuantity;
                                    string updateQueryPartsStock = @"UPDATE GNM_PartsStockDetail
                                       SET AllocatedQuantity = @AllocatedQuantity,
                                           BackOrderQuantity = @BackOrderQuantity,
                                           FreeStock = @FreeStock
                                       WHERE Parts_ID = @Parts_ID AND WareHouse_ID = @WareHouse_ID";
                                    using (SqlCommand updateCommand = new SqlCommand(updateQueryPartsStock, conn))
                                    {
                                        updateCommand.Parameters.AddWithValue("@AllocatedQuantity", allocatedStock);
                                        updateCommand.Parameters.AddWithValue("@BackOrderQuantity", (object)backOrderStock ?? DBNull.Value);
                                        updateCommand.Parameters.AddWithValue("@FreeStock", freeSTK);
                                        updateCommand.Parameters.AddWithValue("@Parts_ID", partID);
                                        updateCommand.Parameters.AddWithValue("@WareHouse_ID", wareHouseID);

                                        updateCommand.ExecuteNonQuery();
                                    }


                                    if (freestock <= 0)
                                    {
                                        break;
                                    }
                                }

                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }

                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion
        #region SelBackOrderPartsOrderSortedOnPriority vinay n
        /// <summary>
        /// SelBackOrderPartsOrderSortedOnPriority
        /// </summary>
        /// <param name="Obj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <param name="partID"></param>
        /// <param name="partsOrderID"></param>
        /// <param name="isSortedDescending"></param>
        /// <returns></returns>
        public static List<int> SelBackOrderPartsOrderSortedOnPriority(SelBackOrderPartsOrderSortedOnPriorityList Obj, string connString, int LogException, int partID, List<int> partsOrderID, bool isSortedDescending)
        {

            List<int> partsOrderIDs = new List<int>();
            try
            {
                int Company_ID = Convert.ToInt32(Obj.Company_ID);
                int branchID = Convert.ToInt32(Obj.Branch);

                List<PRM_PSAllocationPriority> allocationList = new List<PRM_PSAllocationPriority>();
                List<PRT_PSPartsOrder> partsOrderList = new List<PRT_PSPartsOrder>();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string partsOrderQuery = @"
                    SELECT PartsOrder_ID, PartsOrderType_ID, CustomerOrderClass_ID
                    FROM PRT_PartsOrder
                    WHERE Company_ID = @Company_ID";



                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(partsOrderQuery, conn))
                        {
                            command.CommandType = CommandType.Text;
                            command.Parameters.AddWithValue("@Company_ID", Company_ID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {


                                while (reader.Read())
                                {
                                    partsOrderList.Add(new PRT_PSPartsOrder
                                    {
                                        PartsOrder_ID = Convert.ToInt32(reader["PartsOrder_ID"]),
                                        PartsOrderType_ID = Convert.ToInt32(reader["PartsOrderType_ID"]),
                                        CustomerOrderClass_ID = Convert.ToInt32(reader["CustomerOrderClass_ID"])
                                    });
                                }



                            }

                        }
                        string allocationQuery = @"
                        SELECT OrderType_ID, OrderClass_ID, AllocationPriority_Priority
                        FROM PRM_AllocationPriority
                        WHERE Company_ID = @Company_ID";
                        using (command = new SqlCommand(allocationQuery, conn))
                        {
                            command.CommandType = CommandType.Text;
                            command.Parameters.AddWithValue("@Company_ID", Company_ID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {


                                while (reader.Read())
                                {
                                    allocationList.Add(new PRM_PSAllocationPriority
                                    {
                                        OrderType_ID = reader["OrderType_ID"] != DBNull.Value ? Convert.ToInt32(reader["OrderType_ID"]) : 0,
                                        OrderClass_ID = reader["OrderClass_ID"] != DBNull.Value ? Convert.ToInt32(reader["OrderClass_ID"]) : 0,
                                        AllocationPriority_Priority = reader["AllocationPriority_Priority"] != DBNull.Value ? Convert.ToInt32(reader["AllocationPriority_Priority"]) : 0
                                    });
                                }



                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        
                    }
                }

                if (isSortedDescending)
                {
                    var x = (from a in partsOrderID
                             join b in partsOrderList on a equals b.PartsOrder_ID
                             join c in allocationList on b.PartsOrderType_ID equals c.OrderType_ID
                             where b.CustomerOrderClass_ID == c.OrderClass_ID
                             orderby c.AllocationPriority_Priority descending
                             select new
                             {
                                 b.PartsOrder_ID
                             }).Distinct();

                    foreach (var xobj in x)
                    {
                        partsOrderIDs.Add(xobj.PartsOrder_ID);
                    }
                }
                else
                {
                    var x = (from a in partsOrderID
                             join b in partsOrderList on a equals b.PartsOrder_ID
                             join c in allocationList on b.PartsOrderType_ID equals c.OrderType_ID
                             where b.CustomerOrderClass_ID == c.OrderClass_ID
                             orderby c.AllocationPriority_Priority ascending
                             select new
                             {
                                 b.PartsOrder_ID
                             }).Distinct();

                    foreach (var xobj in x)
                    {
                        partsOrderIDs.Add(xobj.PartsOrder_ID);
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return partsOrderIDs;
        }
        #endregion
    }
    public class PartsOrderResult
    {
        public int PartsOrder_ID { get; set; }
    }
    public class BackOrder_AllocationList
    {
        public int Company_ID { get; set; }
        public int Branch { get; set; }
    }

    public class SelBackOrderPartsOrderSortedOnPriorityList
    {
        public int Company_ID { get; set; }
        public int Branch { get; set; }
    }
    public partial class PRM_PSAllocationPriority
    {
        public int AllocationPriority_ID { get; set; }
        public int Company_ID { get; set; }
        public int OrderType_ID { get; set; }
        public int OrderClass_ID { get; set; }
        public int AllocationPriority_Priority { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }
    }
    public partial class PRT_PSPartsOrderAllocation
    {
        public int PartsOrderAllocation_ID { get; set; }
        public int PartsOrderPartsDetail_ID { get; set; }
        public Nullable<int> WareHouse_ID { get; set; }
        public Nullable<decimal> AllocatedQuantity { get; set; }
        public Nullable<decimal> PickedQuantity { get; set; }
        public Nullable<decimal> InvoicedQuantity { get; set; }
        public Nullable<decimal> BackOrderQuantity { get; set; }
        public Nullable<decimal> NullPickQuantity { get; set; }
        public Nullable<decimal> CanceledQuantity { get; set; }
        public Nullable<decimal> IssuedQuantity { get; set; }
        public Nullable<decimal> ReturnedQuantity { get; set; }
        public int PartsOrder_ID { get; set; }
        public int Parts_ID { get; set; }

        public virtual PRT_PSPartsOrder PRT_PartsOrder { get; set; }
        public virtual PRT_PSPartsOrderPartsDetails PRT_PartsOrderPartsDetails { get; set; }
    }
    public partial class PRT_PSPartsOrder
    {
        public PRT_PSPartsOrder()
        {
            this.PRT_PartsOrderAllocation = new HashSet<PRT_PSPartsOrderAllocation>();
            this.PRT_PartsOrderPartsDetails = new HashSet<PRT_PSPartsOrderPartsDetails>();
        }

        public int PartsOrder_ID { get; set; }
        public string PartsOrderNumber { get; set; }
        public System.DateTime PartsOrderDate { get; set; }
        public int PartsOrderType_ID { get; set; }
        public Nullable<int> ServiceRequest_ID { get; set; }
        public Nullable<int> PartsQuotation_ID { get; set; }
        public int CustomerOrderClass_ID { get; set; }
        public Nullable<int> Party_ID { get; set; }
        public Nullable<int> OrderBranch_ID { get; set; }
        public string PartyReferenceDetail { get; set; }
        public Nullable<int> ConsigneeAddress_ID { get; set; }
        public Nullable<int> InvoiceAddress_ID { get; set; }
        public string PaymentTerms { get; set; }
        public Nullable<bool> EnableVersion { get; set; }
        public Nullable<bool> IsArchived { get; set; }
        public Nullable<decimal> TotalAmount { get; set; }
        public int PartsOrderStatus_ID { get; set; }
        public Nullable<int> PurchaseOrder_ID { get; set; }
        public Nullable<bool> IsBackToBackOrderAllocation { get; set; }
        public Nullable<decimal> DiscountPercentage { get; set; }
        public Nullable<decimal> DiscountAmount { get; set; }
        public Nullable<decimal> DiscountedAmount { get; set; }
        public Nullable<int> TaxStructure_ID { get; set; }
        public Nullable<decimal> TotalTaxableAmount { get; set; }
        public Nullable<decimal> TaxAmount { get; set; }
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public Nullable<int> FinancialYear { get; set; }
        public int ModifiedBy { get; set; }
        public System.DateTime ModifiedDate { get; set; }
        public Nullable<int> DocumentNumber { get; set; }
        public Nullable<int> PartsOrderVersion { get; set; }
        public Nullable<int> JobCard_ID { get; set; }
        public string JobCardNumber { get; set; }
        public Nullable<int> StockTransferRequest_ID { get; set; }
        public string StockTransferRequestNumber { get; set; }
        public Nullable<int> OrderBranchConsignee_ID { get; set; }
        public Nullable<int> WareHouse_ID { get; set; }

        public virtual ICollection<PRT_PSPartsOrderAllocation> PRT_PartsOrderAllocation { get; set; }
        public virtual ICollection<PRT_PSPartsOrderPartsDetails> PRT_PartsOrderPartsDetails { get; set; }
        public virtual PRT_PSPartsOrder PRT_PartsOrder1 { get; set; }
        public virtual PRT_PSPartsOrder PRT_PartsOrder2 { get; set; }
    }
    public partial class PRT_PSPartsOrderPartsDetails
    {
        public PRT_PSPartsOrderPartsDetails()
        {
            this.PRT_PartsOrderAllocation = new HashSet<PRT_PSPartsOrderAllocation>();
        }

        public int PartsOrderPartsDetail_ID { get; set; }
        public int PartsOrder_ID { get; set; }
        public int Parts_ID { get; set; }
        public decimal OrderQuantity { get; set; }
        public decimal Rate { get; set; }
        public Nullable<decimal> AcceptedQuantity { get; set; }
        public Nullable<decimal> FreeStock { get; set; }
        public Nullable<decimal> DiscountPercentage { get; set; }
        public Nullable<decimal> DiscountAmount { get; set; }
        public Nullable<int> TaxStructure_ID { get; set; }
        public Nullable<decimal> TaxAmount { get; set; }
        public Nullable<decimal> Amount { get; set; }

        public virtual PRT_PSPartsOrder PRT_PartsOrder { get; set; }
        public virtual ICollection<PRT_PSPartsOrderAllocation> PRT_PartsOrderAllocation { get; set; }
    }
}
