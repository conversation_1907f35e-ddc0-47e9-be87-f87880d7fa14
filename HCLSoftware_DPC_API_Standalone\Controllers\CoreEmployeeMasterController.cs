﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreEmployeeMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreEmployeeMasterController : ApiController
    {

        #region ::: SelectAllEmployeeDetails Uday Kumar J B 27-08-2024:::
        /// <summary>
        /// to select all the employees of the company
        /// </summary> 
        /// 
        [Route("api/CoreEmployeeMaster/SelectAllEmployeeDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectAllEmployeeDetails([FromBody] SelectAllEmployeeDetailsList SelectAllEmployeeDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreEmployeeMasterServices.SelectAllEmployeeDetails(connstring, SelectAllEmployeeDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: Export Uday Kumar J B 20-08-2024:::
        /// <summary>
        /// Exporting 
        /// </summary> 
        /// 
        [Route("api/CoreEmployeeMaster/EmployeeExport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> EmployeeExport([FromBody] EmployeeExportList EmployeeExportobj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = EmployeeExportobj.sidx;
            string sord = EmployeeExportobj.sord;
            string filter = EmployeeExportobj.filter;
            string advnceFilter = EmployeeExportobj.advanceFilter;

            try
            {


                Object Response = await CoreEmployeeMasterServices.EmployeeExport(EmployeeExportobj, connstring, filter, advnceFilter, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion


        #region ::: SelEmployeelocale Uday Kumar J B 27-08-2024:::
        /// <summary>
        /// to select the employees locale detail of the company
        /// </summary> 
        /// 
        [Route("api/CoreEmployeeMaster/SelEmployeelocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelEmployeelocale([FromBody] SelEmployeelocaleList SelEmployeelocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SelEmployeelocale(connString, SelEmployeelocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectSpecificEmployeeDetails Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// to select the specific employee detail of the company for editing
        /// </summary> 
        /// 
        [Route("api/CoreEmployeeMaster/SelectSpecificEmployeeDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSpecificEmployeeDetails([FromBody] SelectSpecificEmployeeDetailsList SelectSpecificEmployeeDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SelectSpecificEmployeeDetails(connString, SelectSpecificEmployeeDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: InsertEmployeeDetails Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// to insert the employee details
        /// </summary> 
        ///
        [Route("api/CoreEmployeeMaster/InsertEmployeeDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertEmployeeDetails([FromBody] InsertEmployeeDetailsList InsertEmployeeDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.InsertEmployeeDetails(connString, InsertEmployeeDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: UpdateEmployeeDetails Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// to update the employee details
        /// </summary> 
        /// 
        [Route("api/CoreEmployeeMaster/UpdateEmployeeDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateEmployeeDetails([FromBody] UpdateEmployeeDetailsList UpdateEmployeeDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.UpdateEmployeeDetails(connString, UpdateEmployeeDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: InsertEmployeeLocale Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// to insert the Employee locale details
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/InsertEmployeeLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertEmployeeLocale([FromBody] InsertEmployeeLocaleList InsertEmployeeLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.InsertEmployeeLocale(connString, InsertEmployeeLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: UpdateEmployeeLocale  Uday Kumar J B 27-08-2024:::
        /// <summary>
        /// to update the Employee locale details
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/UpdateEmployeeLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateEmployeeLocale([FromBody] UpdateEmployeeLocaleList UpdateEmployeeLocaleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.UpdateEmployeeLocale(connString, UpdateEmployeeLocaleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeleteCompanyEmployee Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// to Delete the Company Employee details
        /// </summary>
        ///
        [Route("api/CoreEmployeeMaster/DeleteCompanyEmployee")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteCompanyEmployee([FromBody] DeleteCompanyEmployeeList DeleteCompanyEmployeeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.DeleteCompanyEmployee(connString, DeleteCompanyEmployeeobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckEmployeeSkills Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// to check if the employee is already associated with the skills
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/CheckEmployeeSkills")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckEmployeeSkills([FromBody] CheckEmployeeSkillsList CheckEmployeeSkillsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.CheckEmployeeSkills(connString, CheckEmployeeSkillsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelAllEmployeeSkillset Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// To select all the skills of the particular employee
        /// </summary>
        ///  
        [Route("api/CoreEmployeeMaster/SelAllEmployeeSkillset")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelAllEmployeeSkillset([FromBody] SelAllEmployeeSkillsetList SelAllEmployeeSkillsetobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreEmployeeMasterServices.SelAllEmployeeSkillset(connstring, SelAllEmployeeSkillsetobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelEmployeeTrainingDetails Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// to select all the skills of the particular employee
        /// </summary>
        ///
        [Route("api/CoreEmployeeMaster/SelEmployeeTrainingDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelEmployeeTrainingDetails([FromBody] SelEmployeeTrainingDetailsList SelEmployeeTrainingDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreEmployeeMasterServices.SelEmployeeTrainingDetails(connstring, SelEmployeeTrainingDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckETODetails Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// CheckETODetails
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/CheckETODetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckETODetails([FromBody] CheckETODetailsList CheckETODetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.CheckETODetails(connString, CheckETODetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckETODetailsWithBankCode Uday kumar J B 27-08-2024:::
        /// <summary>
        /// CheckETODetailsWithBankCode
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/CheckETODetailsWithBankCode")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckETODetailsWithBankCode([FromBody] CheckETODetailsWithBankCodelist CheckETODetailsWithBankCodeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.CheckETODetailsWithBankCode(connString, CheckETODetailsWithBankCodeobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelEmployeeETODetails Uday Kumar J B 27-08-2024:::
        /// <summary>
        /// to select all the skills of the particular employee
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/SelEmployeeETODetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelEmployeeETODetails([FromBody] SelEmployeeETODetailsList SelEmployeeETODetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreEmployeeMasterServices.SelEmployeeETODetails(connstring, SelEmployeeETODetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelIncentiveLogDetails Uday Kumar J B 27-08-2024:::
        /// <summary>
        /// to select all the skills of the particular employee
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/SelIncentiveLogDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelIncentiveLogDetails([FromBody] SelIncentiveLogDetailsList SelIncentiveLogDetailsobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreEmployeeMasterServices.SelIncentiveLogDetails(connstring, SelIncentiveLogDetailsobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectSpecificEmployeeTrainingLocaleDetails Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// SelectSpecificEmployeeTrainingLocaleDetails
        /// </summary>
        ///
        [Route("api/CoreEmployeeMaster/SelectSpecificEmployeeTrainingLocaleDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectSpecificEmployeeTrainingLocaleDetails([FromBody] SelectSpecificEmployeeTrainingLocaleDetailsList SelectSpecificEmployeeTrainingLocaleDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SelectSpecificEmployeeTrainingLocaleDetails(connString, SelectSpecificEmployeeTrainingLocaleDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveEmpTrainingLocaleContactdetails Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// SaveEmpTrainingLocaleContactdetails
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/SaveEmpTrainingLocaleContactdetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveEmpTrainingLocaleContactdetails([FromBody] SaveEmpTrainingLocaleContactdetailsList SaveEmpTrainingLocaleContactdetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SaveEmpTrainingLocaleContactdetails(connString, SaveEmpTrainingLocaleContactdetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Insert Training details Uday Kumar J B 27-08-2024:::
        /// <summary>
        /// To Save Training Details
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/SaveTrainingDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveTrainingDetails([FromBody] SaveTrainingDetailsList SaveTrainingDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SaveTrainingDetails(connString, SaveTrainingDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Insert Employee ETO details Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// To Save Training Details
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/SaveEmployeeETODetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveEmployeeETODetails([FromBody] SaveEmployeeETODetailsList SaveEmployeeETODetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SaveEmployeeETODetails(connString, SaveEmployeeETODetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveSkills Uday Kumar J B 27-08-2024:::
        /// <summary>
        /// to update the skills of the particular employee 
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/SaveSkills")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveSkills([FromBody] SaveSkillsList SaveSkillsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SaveSkills(connString, SaveSkillsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeleteSkills Uday Kumar J B 27-08-2024 :::
        /// <summary>
        /// to Delete the Employee Skillset details
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/DeleteSkills")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteSkills([FromBody] DeleteSkillsList DeleteSkillsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.DeleteSkills(connString, DeleteSkillsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelAllEmployeeBranches  Uday Kumar J B 27-08-2024:::
        /// <summary>
        /// to select all the branches associated with the Employee
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/SelAllEmployeeBranches")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelAllEmployeeBranches([FromBody] SelAllEmployeeBranchesList SelAllEmployeeBranchesobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreEmployeeMasterServices.SelAllEmployeeBranches(connstring, SelAllEmployeeBranchesobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveEmployeeBranch  Uday Kumar J B 27-08-2024:::
        /// <summary>
        /// to save Employee Branch Association
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/SaveEmployeeBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveEmployeeBranch([FromBody] SaveEmployeeBranchList SaveEmployeeBranchobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SaveEmployeeBranch(connString, SaveEmployeeBranchobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveEmployeeBranchEditMode Uday Kumar J B 27-08-2024:::
        /// <summary>
        /// SaveEmployeeBranchEditMode
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/SaveEmployeeBranchEditMode")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveEmployeeBranchEditMode([FromBody] SaveEmployeeBranchEditModeList SaveEmployeeBranchEditModeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SaveEmployeeBranchEditMode(connString, SaveEmployeeBranchEditModeobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeleteEmployeeBranch Uday Kumar J B 27-08-2024  :::
        /// <summary>
        /// to Delete the Employee Branch details
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/DeleteEmployeeBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteEmployeeBranch([FromBody] DeleteEmployeeBranchList DeleteEmployeeBranchobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.DeleteEmployeeBranch(connString, DeleteEmployeeBranchobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckEmployeeBranch Uday Kumar J B 27-08-2024:::
        /// <summary>
        /// to check if the employee is already associated with the branch
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/CheckEmployeeBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckEmployeeBranch([FromBody] CheckEmployeeBranchList CheckEmployeeBranchobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.CheckEmployeeBranch(connString, CheckEmployeeBranchobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectState Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// to get all the states for the selected country
        /// </summary> 
        /// 
        [Route("api/CoreEmployeeMaster/SelectState")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectState([FromBody] SelectStateList SelectStateobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SelectState(connString, SelectStateobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckEmployeeID Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// to check if the Name is already used
        /// </summary>
        ///  
        [Route("api/CoreEmployeeMaster/CheckEmployeeID")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckEmployeeID([FromBody] CheckEmployeeIDList CheckEmployeeIDobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.CheckEmployeeID(connString, CheckEmployeeIDobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectAllEmployeeMastersData Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// SelectAllEmployeeMastersData
        /// </summary>
        ///  
        [Route("api/CoreEmployeeMaster/SelectAllEmployeeMastersData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectAllEmployeeMastersData([FromBody] SelectAllEmployeeMastersDataList SelectAllEmployeeMastersDataobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SelectAllEmployeeMastersData(connString, SelectAllEmployeeMastersDataobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeleteTrainingDetails Uday Kumar J B 28-08-2024 :::
        /// <summary>
        /// to Delete the Employee Skillset details
        /// </summary>
        ///  
        [Route("api/CoreEmployeeMaster/DeleteTrainingDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteTrainingDetails([FromBody] DeleteTrainingDetailsList DeleteTrainingDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.DeleteTrainingDetails(connString, DeleteTrainingDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeleteETODetails Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// to Delete the Employee Skillset details
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/DeleteETODetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteETODetails([FromBody] DeleteETODetailsList DeleteETODetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.DeleteETODetails(connString, DeleteETODetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectRegionForReport Uday Kumar J B 28-08-2024 :::
        /// <summary>
        ////to Select Region and its child
        /// </summary> 
        /// 
        [Route("api/CoreEmployeeMaster/SelectRegionForReport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectRegionForReport([FromBody] SelectRegionForReportList SelectRegionForReportobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SelectRegionForReport(connString, SelectRegionForReportobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectCompany Uday Kumar J B 28-08-2024 :::
        /// <summary>
        ////to Select Company and its child
        /// </summary> 
        ///  
        [Route("api/CoreEmployeeMaster/SelectCompanyForReport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectCompanyForReport([FromBody] SelectCompanyForReportList SelectCompanyForReportobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SelectCompanyForReport(connString, SelectCompanyForReportobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectBranchForReport Uday Kumar J B 28-08-2024 :::
        /// <summary>
        ////to Select Branch for Reports
        /// </summary> 
        ///  
        [Route("api/CoreEmployeeMaster/SelectBranchForReport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectBranchForReport([FromBody] SelectBranchForReportList SelectBranchForReportobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SelectBranchForReport(connString, SelectBranchForReportobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectRegionForReport Uday Kumar J B 28-08-2024 I think not used:::
        /// <summary>
        ////to Select Warehouse for Reports
        /// </summary> 
        ///  
        [Route("api/CoreEmployeeMaster/SelectWarehouseForReport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectWarehouseForReport([FromBody] SelectWarehouseForReportList SelectWarehouseForReportobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SelectWarehouseForReport(connString, SelectWarehouseForReportobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveFileToServer Uday Kumar J B 28-08-2024 need to do :::
        /// <summary>
        /// SaveFileToServer
        /// </summary>
        //[Route("api/CoreEmployeeMaster/SaveFileToServer")]
        //[HttpPost]
        //[JwtTokenValidationFilter]
        //public IHttpActionResult SaveFileToServer([FromBody] SaveFileToServerList SaveFileToServerobj)
        //{
        //    var Response = default(dynamic);
        //    int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
        //    try
        //    {
        //        String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
        //        Response = CoreEmployeeMasterServices.SaveFileToServer(connString, SaveFileToServerobj);
        //    }
        //    catch (Exception ex)
        //    {
        //        if (LogException == 1)
        //        {
        //            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
        //        }
        //    }

        //    return Ok(Response.Value);
        //}
        #endregion


        #region ::: SelAllEducationalQualification Uday Kumar J B 28-08-2024 :::
        /// <summary>
        /// to select all the skills of the particular employee
        /// </summary>
        ///
        [Route("api/CoreEmployeeMaster/SelEmployeeQualification")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelEmployeeQualification([FromBody] SelEmployeeQualificationList SelEmployeeQualificationobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreEmployeeMasterServices.SelEmployeeQualification(connstring, SelEmployeeQualificationobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeleteQualification Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// to Delete the Employee Skillset details
        /// </summary>
        ///  
        [Route("api/CoreEmployeeMaster/DeleteQualification")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteQualification([FromBody] DeleteQualificationList DeleteQualificationobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.DeleteQualification(connString, DeleteQualificationobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveQualification Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// to update the skills of the particular employee 
        /// </summary>
        ///  
        [Route("api/CoreEmployeeMaster/SaveQualification")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveQualification([FromBody] SaveQualificationList SaveQualificationobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SaveQualification(connString, SaveQualificationobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckEmployeeQualification Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// CheckEmployeeQualification
        /// </summary>
        ///  
        [Route("api/CoreEmployeeMaster/CheckEmployeeQualification")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckEmployeeQualification([FromBody] CheckEmployeeQualificationList CheckEmployeeQualificationobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.CheckEmployeeQualification(connString, CheckEmployeeQualificationobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelWorkingExperience Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// to select all the skills of the particular employee
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/SelWorkingExperience")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelWorkingExperience([FromBody] SelWorkingExperienceList SelWorkingExperienceobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = " ";


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreEmployeeMasterServices.SelWorkingExperience(connstring, SelWorkingExperienceobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeleteExperiance Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// to Delete the Employee Skillset details
        /// </summary>
        ///  
        [Route("api/CoreEmployeeMaster/DeleteExperience")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteExperience([FromBody] DeleteExperienceList DeleteExperienceobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.DeleteExperience(connString, DeleteExperienceobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveExperiance Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// to update the skills of the particular employee 
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/SaveExperience")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveExperience([FromBody] SaveExperienceList SaveExperienceobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SaveExperience(connString, SaveExperienceobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Select Shift Type Uday Kumar J B 28-08-2024:::
        /// <summary>
        ////to Select Branch for Reports
        /// </summary> 
        ///  
        [Route("api/CoreEmployeeMaster/SelectShiftType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectShiftType([FromBody] SelectShiftTypeList SelectShiftTypeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SelectShiftType(connString, SelectShiftTypeobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Select Shitf Days Uday Kumar J B 28-08-2024 :::
        /// <summary>
        ////to Select Branch for Reports
        /// </summary> 
        /// 
        [Route("api/CoreEmployeeMaster/SelectShiftDays")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectShiftDays([FromBody] SelectShiftDaysList SelectShiftDaysobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SelectShiftDays(connString, SelectShiftDaysobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Select Shitf  Uday Kumar J B 28-08-2024:::
        /// <summary>
        ////to Select Branch for Reports
        /// </summary> 
        /// 
        [Route("api/CoreEmployeeMaster/SelectShift")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectShift([FromBody] SelectShiftList SelectShiftobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SelectShift(connString, SelectShiftobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::  Insert TAMS Schedule Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// To Insert TAMS Schedule
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/SaveTAMSSchedule")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveTAMSSchedule([FromBody] SaveTAMSScheduleList SaveTAMSScheduleobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SaveTAMSSchedule(connString, SaveTAMSScheduleobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::  Insert TAMS Schedule Effective From Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// To Insert TAMS Schedule
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/SaveTAMSScheduleEffectiveFrom")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveTAMSScheduleEffectiveFrom([FromBody] SaveTAMSScheduleEffectiveFromList SaveTAMSScheduleEffectiveFromobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SaveTAMSScheduleEffectiveFrom(connString, SaveTAMSScheduleEffectiveFromobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelEmployeeTAMSSchedule Uday Kumar J B 28-08-2024 :::
        /// <summary>
        /// to select all the branches associated with the Employee
        /// </summary>
        ///   
        [Route("api/CoreEmployeeMaster/SelEmployeeTAMSSchedule")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelEmployeeTAMSSchedule([FromBody] SelEmployeeTAMSScheduleList SelEmployeeTAMSScheduleobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreEmployeeMasterServices.SelEmployeeTAMSSchedule(connstring, SelEmployeeTAMSScheduleobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelectAssociatedBranch Uday Kumar J B 28-08-2024:::
        /// <summary>
        ////to Select Branch for Downlines Association
        /// </summary> 
        /// 
        [Route("api/CoreEmployeeMaster/SelectAssociatedBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectAssociatedBranch([FromBody] SelectAssociatedBranchList SelectAssociatedBranchobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SelectAssociatedBranch(connString, SelectAssociatedBranchobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Select Employee Uday Kumar J B 28-08-2024 :::
        /// <summary>
        ////to Select Branch for Reports
        /// </summary> 
        /// 
        [Route("api/CoreEmployeeMaster/SelectEmployee")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectEmployee([FromBody] SelectEmployeeList SelectEmployeeobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SelectEmployee(connString, SelectEmployeeobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveEmployeeDownLines Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// to save Employee Branch Association
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/SaveEmployeeDownLines")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveEmployeeDownLines([FromBody] SaveEmployeeDownLinesList SaveEmployeeDownLinesobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.SaveEmployeeDownLines(connString, SaveEmployeeDownLinesobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SelAllEmployeeDownLines Uday Kumar J B 28-08-2024:::
        /// <summary>
        /// to select all the down lines associated with the Employee
        /// </summary>
        ///   
        [Route("api/CoreEmployeeMaster/SelAllEmployeeDownLines")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelAllEmployeeDownLines([FromBody] SelAllEmployeeDownLinesList SelAllEmployeeDownLinesobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["_advnce"]);
            string advnceFilters = " ";


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreEmployeeMasterServices.SelAllEmployeeDownLines(connstring, SelAllEmployeeDownLinesobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeleteDownLineDetails Uday Kumar J B 28-08-2024 :::
        /// <summary>
        /// to Delete the Employee DownLine details
        /// </summary>
        /// 
        [Route("api/CoreEmployeeMaster/DeleteDownLineDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteDownLineDetails([FromBody] DeleteDownLineDetailsList DeleteDownLineDetailsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreEmployeeMasterServices.DeleteDownLineDetails(connString, DeleteDownLineDetailsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


    }
}