﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.CoreProductMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreMovementTypeServices
    {

        #region ::: Select Uday Kumar J B 14-08-2024:::
        /// <summary>
        /// To select the All parts 
        /// </summary>    
        /// 

        public static IActionResult Select(string connString, SelectCoreMovementTypeList SelectCoreMovementTypeLandobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int count = 0;
            int total = 0;
            string AppPath = string.Empty;
            var x = default(dynamic);
            List<GNM_MovementType> liMovementType = new List<GNM_MovementType>();
            IQueryable<PRMMovementType> iQPrty = null;
            IEnumerable<PRMMovementType> iEPrty = null;
            int Company_ID = Convert.ToInt32(SelectCoreMovementTypeLandobj.Company_ID);
            string Yes = CommonFunctionalities.GetResourceString(SelectCoreMovementTypeLandobj.GeneralCulture.ToString(), "Yes").ToString();
            string No = CommonFunctionalities.GetResourceString(SelectCoreMovementTypeLandobj.GeneralCulture.ToString(), "No").ToString();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // GNM_User User = SelectCoreMovementTypeLandobj.UserDetails.FirstOrDefault();
                int CompanyID = SelectCoreMovementTypeLandobj.Company_ID;
                int BranchId = Convert.ToInt32(SelectCoreMovementTypeLandobj.Branch);

                List<ParentCompanyObject> ParentCompanyDetails = new List<ParentCompanyObject>();
                using (var connection = new SqlConnection(connString))
                {
                    connection.Open();
                    using (var command = new SqlCommand("Up_Sel_AM_ERP_GetParentCompanies", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@CompanyID", CompanyID);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ParentCompanyDetails.Add(new ParentCompanyObject
                                {
                                    Company_ID = Convert.ToInt32(reader["Company_ID"]),
                                    Company_Name = reader["Company_Name"].ToString(),
                                    Company_Parent_ID = Convert.ToInt32(reader["Company_Parent_ID"])
                                });
                            }
                        }
                    }
                }

                string ParentCompanyIDs = string.Join(",", ParentCompanyDetails.Select(pc => pc.Company_ID));

                using (var connection = new SqlConnection(connString))
                {
                    connection.Open();
                    using (var command = new SqlCommand("Up_Sel_AM_ERP_GetMovementTypes1", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@ParentCompanyIDs", ParentCompanyIDs);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                liMovementType.Add(new GNM_MovementType
                                {
                                    MovementType_ID = Convert.ToInt32(reader["MovementType_ID"]),
                                    Description = reader["Description"].ToString(),
                                    Company_ID = Convert.ToInt32(reader["Company_ID"]),
                                    IsActive = Convert.ToBoolean(reader["IsActive"])
                                });
                            }
                        }
                    }
                }

                iEPrty = from a in liMovementType
                         join c in ParentCompanyDetails on a.Company_ID equals c.Company_ID
                         select new PRMMovementType
                         {
                             MovementType_ID = a.MovementType_ID,
                             Description = a.Description,
                             Company = c.Company_Name,
                             Company_ID = a.Company_ID,
                             IsActive = a.IsActive ? Yes : No
                         };

                iQPrty = iEPrty.AsQueryable<PRMMovementType>();

                // FilterToolBar Search
                if (_search)
                {
                    var parsedFilters = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(filters))).ToObject<Filters>();
                    if (parsedFilters.rules.Count() > 0)
                        iQPrty = iQPrty.FilterSearch<PRMMovementType>(parsedFilters);
                }

                // Advance Search
                if (advnce)
                {

                    var advnFilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<AdvanceFilter>();
                    if (advnFilter.rules.Count() > 0)
                        iQPrty = iQPrty.AdvanceSearch<PRMMovementType>(advnFilter);
                }

                // Sorting 
                iQPrty = iQPrty.OrderByField<PRMMovementType>(sidx, sord);

                count = iQPrty.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                x = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = (from a in iQPrty
                            select new
                            {
                                edit = "<a title='Edit' href='#' id='" + a.MovementType_ID + "' key='" + a.MovementType_ID + "'  editmode='false'  " + ((a.Company_ID == Company_ID) ? "class='editMovementTypeMaster'" : "") + "><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input type='checkbox' key='" + a.MovementType_ID + "' id='chk" + a.MovementType_ID + "' " + ((a.Company_ID == Company_ID) ? "class='chkDelMovementTypeMaster'" : "class='CannotchkDelMovementTypeMaster'") + "/>",
                                ID = a.MovementType_ID,
                                Description = a.Description,
                                Company = a.Company,
                                IsActive = a.IsActive,
                                locale = "<img key='" + a.MovementType_ID + "' src='" + AppPath + "/Content/local.png'  " + ((a.Company_ID == Company_ID) ? "class='MovementTypeLocale'" : "") + " width='20' height='20' alt='Localize' title='Localize' />"
                            }).ToList().Paginate(page, rows)
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(x);
        }

        #endregion


        #region ::: Insert Uday Kumar J B 14-08-2024:::
        /// <summary>
        /// Method to insert the Parts Master Header Table
        /// </summary>   
        /// 

        public static IActionResult Insert(string connString, InsertCoreMovementTypeList InsertCoreMovementTypeobj)
        {
            var x = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // GNM_User user = InsertCoreMovementTypeobj.UserDetails.FirstOrDefault();
                JObject jObj = JObject.Parse(InsertCoreMovementTypeobj.key);
                int count = jObj["rows"].Count();

                for (int i = 0; i < count; i++)
                {
                    JObject row = jObj["rows"].ElementAt(i).ToObject<JObject>();
                    string movementTypeID = row["ID"].ToString();
                    string description = Uri.UnescapeDataString(Common.DecryptString(row["Desc"].ToString()));
                    string isActive = row["IsActive"].ToString() == "true" ? "1" : "0";

                    if (int.TryParse(movementTypeID, out int id))
                    {
                        UpdateMovementType(connString, id, description, isActive);
                        //gbl.InsertGPSDetails(
                        //    Convert.ToInt32(InsertCoreMovementTypeobj.Company_ID),
                        //    Convert.ToInt32(InsertCoreMovementTypeobj.Branch),
                        //    InsertCoreMovementTypeobj.User_ID,
                        //    Common.GetObjectID("CoreMovementType"),
                        //    id,
                        //    0,
                        //    0,
                        //    "Updated Movement Type",
                        //    false,
                        //    Convert.ToInt32(InsertCoreMovementTypeobj.MenuID),
                        //    Convert.ToDateTime(InsertCoreMovementTypeobj.LoggedINDateTime)
                        //);
                        x = new { MoventTypeID = movementTypeID };
                    }
                    else
                    {
                        int newId = InsertMovementType(connString, InsertCoreMovementTypeobj.Company_ID, description, isActive);
                        //gbl.InsertGPSDetails(
                        //    Convert.ToInt32(InsertCoreMovementTypeobj.Company_ID),
                        //    Convert.ToInt32(InsertCoreMovementTypeobj.Branch),
                        //    InsertCoreMovementTypeobj.User_ID,
                        //    Common.GetObjectID("CoreMovementType"),
                        //    newId,
                        //    0,
                        //    0,
                        //    "Inserted Movement Type",
                        //    false,
                        //    Convert.ToInt32(InsertCoreMovementTypeobj.MenuID),
                        //    Convert.ToDateTime(InsertCoreMovementTypeobj.LoggedINDateTime)
                        //);
                        x = new { MoventTypeID = newId };
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(x);
        }

        private static void UpdateMovementType(string connString, int id, string description, string isActive)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            using (SqlConnection conn = new SqlConnection(connString))
            {
                string query = "Up_Upd_AM_ERP_UpdateMovementType1";

                try
                {
                    using (SqlCommand command = new SqlCommand(query, conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@MovementType_ID", id);
                        command.Parameters.AddWithValue("@Description", description);
                        command.Parameters.AddWithValue("@IsActive", isActive);

                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }
                        command.ExecuteScalar();
                    }
                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }
                }
                finally
                {
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }
            }
        }

        private static int InsertMovementType(string connString, int companyId, string description, string isActive)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            using (SqlConnection conn = new SqlConnection(connString))
            {
                string query = "Up_Sel_AM_ERP_InsertMovementType";
                int newId = 0;

                try
                {
                    using (SqlCommand command = new SqlCommand(query, conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // Add parameters
                        command.Parameters.AddWithValue("@Company_ID", companyId);
                        command.Parameters.AddWithValue("@Description", description);
                        command.Parameters.AddWithValue("@IsActive", isActive);

                        // Define output parameter for the new MovementType_ID
                        SqlParameter outputIdParam = new SqlParameter
                        {
                            ParameterName = "@MovementType_ID",
                            SqlDbType = SqlDbType.Int,
                            Direction = ParameterDirection.Output
                        };
                        command.Parameters.Add(outputIdParam);

                        // Open connection and execute command
                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }
                        command.ExecuteNonQuery();

                        // Retrieve the new ID from the output parameter
                        newId = Convert.ToInt32(outputIdParam.Value);
                    }
                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }
                }
                finally
                {
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }

                return newId;
            }
        }

        #endregion


        #region ::: Delete Uday Kumar J B 14-08-2024:::
        /// <summary>
        /// to Delete the Parts
        /// </summary>
        /// 

        public static IActionResult Delete(string connString, DeleteCoreMovementTypeList DeleteCoreMovementTypeobj)
        {
            string errorMsg = "";

            try
            {
                JTokenReader reader = null;
                JObject jobj = JObject.Parse(DeleteCoreMovementTypeobj.key);
                int rowCount = jobj["rows"].Count();
                //GNM_User User = DeleteCoreMovementTypeobj.UserDetails.FirstOrDefault();
                int id = 0;

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();
                    using (SqlTransaction transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            for (int i = 0; i < rowCount; i++)
                            {
                                reader = new JTokenReader(jobj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                                reader.Read();
                                id = Convert.ToInt32(reader.Value);

                                using (SqlCommand command = new SqlCommand("DeleteMovementType", connection, transaction))
                                {
                                    command.CommandType = CommandType.StoredProcedure;
                                    command.Parameters.AddWithValue("@MovementType_ID", id);
                                    command.ExecuteNonQuery();
                                }
                            }

                            // Commit the transaction
                            transaction.Commit();

                            // Insert GPS details
                            // gbl.InsertGPSDetails(Convert.ToInt32(DeleteCoreMovementTypeobj.Company_ID.ToString()), Convert.ToInt32(DeleteCoreMovementTypeobj.Branch), DeleteCoreMovementTypeobj.User_ID, Common.GetObjectID("CoreMovementType"), id, 0, 0, "Deleted Movement Type ", false, Convert.ToInt32(DeleteCoreMovementTypeobj.MenuID), Convert.ToDateTime(DeleteCoreMovementTypeobj.LoggedINDateTime));

                            errorMsg = CommonFunctionalities.GetResourceString(DeleteCoreMovementTypeobj.GeneralCulture.ToString(), "deletedsuccessfully").ToString();
                        }
                        catch (SqlException ex)
                        {
                            transaction.Rollback();

                            if (ex.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                            {
                                errorMsg = CommonFunctionalities.GetResourceString(DeleteCoreMovementTypeobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                            }
                            else
                            {
                                throw;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the exception if needed
                errorMsg = "An error occurred while deleting the record.";
            }
            return new JsonResult(errorMsg);
        }
        #endregion


        #region ::: Export Uday Kumar J B 14-08-2024:::
        /// <summary>
        /// Exporting Parts Grid
        /// </summary>
        public static async Task<object> Export(ExportCoreMovementTypeList ExportCoreMovementTypeobj, string connString, string filter, string advnceFilter, string sidx, string sord)
        {
            DataTable dt = new DataTable();
            List<GNM_MovementType> liMovementType = new List<GNM_MovementType>();
            IQueryable<PRMMovementType> iQPrty = null;
            IEnumerable<PRMMovementType> iEPrty = new List<PRMMovementType>();
            int Company_ID = Convert.ToInt32(ExportCoreMovementTypeobj.Company_ID);
            string Yes = CommonFunctionalities.GetResourceString(ExportCoreMovementTypeobj.GeneralCulture.ToString(), "Yes").ToString();
            string No = CommonFunctionalities.GetResourceString(ExportCoreMovementTypeobj.GeneralCulture.ToString(), "No").ToString();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {

                int CompanyID = ExportCoreMovementTypeobj.Company_ID;
                int BranchId = Convert.ToInt32(ExportCoreMovementTypeobj.Branch);

                List<ParentCompanyObject> ParentCompanyDetails = new List<ParentCompanyObject>();
                using (var connection = new SqlConnection(connString))
                {
                    connection.Open();
                    using (var command = new SqlCommand("Up_Sel_AM_ERP_GetParentCompanies", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@CompanyID", CompanyID);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ParentCompanyDetails.Add(new ParentCompanyObject
                                {
                                    Company_ID = Convert.ToInt32(reader["Company_ID"]),
                                    Company_Name = reader["Company_Name"].ToString(),
                                    Company_Parent_ID = Convert.ToInt32(reader["Company_Parent_ID"])
                                });
                            }
                        }
                    }
                }

                string ParentCompanyIDs = string.Join(",", ParentCompanyDetails.Select(pc => pc.Company_ID));

                using (var connection = new SqlConnection(connString))
                {
                    connection.Open();
                    using (var command = new SqlCommand("Up_Sel_AM_ERP_GetMovementTypes1", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@ParentCompanyIDs", ParentCompanyIDs);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                liMovementType.Add(new GNM_MovementType
                                {
                                    MovementType_ID = Convert.ToInt32(reader["MovementType_ID"]),
                                    Description = reader["Description"].ToString(),
                                    Company_ID = Convert.ToInt32(reader["Company_ID"]),
                                    IsActive = Convert.ToBoolean(reader["IsActive"])
                                });
                            }
                        }
                    }
                    iEPrty = from a in liMovementType
                             join c in ParentCompanyDetails on a.Company_ID equals c.Company_ID
                             select new PRMMovementType
                             {
                                 Description = a.Description,
                                 Company = c.Company_Name,
                                 IsActive = a.IsActive ? Yes : No
                             };


                    if (ExportCoreMovementTypeobj.LanguageID == ExportCoreMovementTypeobj.GeneralLanguageID)
                    {
                        using (SqlCommand cmd = new SqlCommand("Up_Sel_AM_ERP_SelectMovementTypeLocale", connection))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@Company_ID", CompanyID);

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                List<PRMMovementType> movementTypes = new List<PRMMovementType>();

                                while (reader.Read())
                                {
                                    PRMMovementType item = new PRMMovementType
                                    {
                                        Description = reader["Description"].ToString(),
                                        Company = reader["Company"].ToString(),
                                        IsActive = Convert.ToString(reader["IsActive"])
                                    };

                                    movementTypes.Add(item);
                                }

                                iEPrty = movementTypes;
                            }
                        }
                    }
                }

                iQPrty = iEPrty.AsQueryable<PRMMovementType>();

                // FilterToolBar Search
                if (!string.IsNullOrEmpty(filter) && filter != "null" && filter != "undefined")
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filter)).ToObject<Filters>();
                    if (filtersObj.rules.Count > 0)
                        iQPrty = iQPrty.FilterSearch(filtersObj);
                }


                // Apply advanced filters if present
                if (!string.IsNullOrEmpty(advnceFilter) && advnceFilter != "null")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Common.DecryptString(advnceFilter)).ToObject<AdvanceFilter>();
                    iQPrty = iQPrty.AdvanceSearch(advnfilter);
                }
                iQPrty = iQPrty.OrderByField<PRMMovementType>(sidx, sord);

                var arrParts = iQPrty.ToList();

                int cnt = arrParts.AsEnumerable().Count();

                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportCoreMovementTypeobj.GeneralCulture.ToString(), "Company").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportCoreMovementTypeobj.GeneralCulture.ToString(), "description").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(ExportCoreMovementTypeobj.GeneralCulture.ToString(), "Active").ToString());
                for (int i = 0; i < cnt; i++)
                {
                    dt.Rows.Add(arrParts.ElementAt(i).Company, arrParts.ElementAt(i).Description, arrParts.ElementAt(i).IsActive);
                }
                DataTable dtAlignment = new DataTable();
                dtAlignment.Columns.Add("Company");
                dtAlignment.Columns.Add("description");
                dtAlignment.Columns.Add("Active");
                dtAlignment.Rows.Add(0, 0, 1);
                ExportList reportExportList = new ExportList
                {
                    Company_ID = ExportCoreMovementTypeobj.Company_ID, // Assuming this is available in ExportObj
                    Branch = ExportCoreMovementTypeobj.Branch,
                    dt1 = dtAlignment,


                    dt = dt,

                    FileName = "MovementType", // Set a default or dynamic filename
                    Header = CommonFunctionalities.GetResourceString(ExportCoreMovementTypeobj.UserCulture.ToString(), "MovementType").ToString(), // Set a default or dynamic header
                    exprtType = ExportCoreMovementTypeobj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                    UserCulture = ExportCoreMovementTypeobj.UserCulture
                };

                var result = await DocumentExport.Export(reportExportList, connString, LogException);
                return result.Value;
                // DocumentExport.Export(ExportCoreMovementTypeobj.exprtType, dt, dtAlignment, "MovementType", GetGlobalResourceObject(ExportCoreMovementTypeobj.GeneralCulture.ToString(), "MovementType").ToString());
                // gbl.InsertGPSDetails(Convert.ToInt32(ExportCoreMovementTypeobj.Company_ID.ToString()), Convert.ToInt32(ExportCoreMovementTypeobj.Branch), User.User_ID, Common.GetObjectID("CoreMovementType"), 0, 0, 0, "Movement Type-Export ", false, Convert.ToInt32(ExportCoreMovementTypeobj.MenuID), Convert.ToDateTime(ExportCoreMovementTypeobj.LoggedINDateTime));
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return null;
        }
        #endregion


        #region ::: InsertLocale Uday Kumar J B 14-08-2024:::
        /// <summary>
        /// To select the All parts 
        /// </summary> 
        /// 
        public static IActionResult InsertLocale(string connString, InsertLocaleCoreMovementTypeList InsertLocaleCoreMovementTypeobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var x = default(dynamic);
            string Desc = Common.DecryptString(InsertLocaleCoreMovementTypeobj.Desc);
            //  GNM_User User = InsertLocaleCoreMovementTypeobj.UserDetails.FirstOrDefault();
            GNM_MovementTypeLocale mtypeloc = null;
            GNM_CompanyLocale compL = null;

            try
            {
                // Get MovementTypeLocale
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "GetMovementTypeLocale";
                    using (SqlCommand command = new SqlCommand(query, conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@MovementType_ID", InsertLocaleCoreMovementTypeobj.ID);
                        command.Parameters.AddWithValue("@Language_ID", InsertLocaleCoreMovementTypeobj.Language_ID);

                        conn.Open();
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                mtypeloc = new GNM_MovementTypeLocale
                                {
                                    MovementTypeLocale_ID = reader.GetInt32(reader.GetOrdinal("MovementTypeLocale_ID")),
                                    MovementType_ID = reader.GetInt32(reader.GetOrdinal("MovementType_ID")),
                                    Description = reader.GetString(reader.GetOrdinal("Description")),
                                    Language_ID = reader.GetInt32(reader.GetOrdinal("Language_ID"))
                                };
                            }
                        }
                    }
                }

                // Get CompanyLocale
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "GetCompanyLocale";
                    using (SqlCommand command = new SqlCommand(query, conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@Company_ID", InsertLocaleCoreMovementTypeobj.Company_ID);

                        conn.Open();
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                compL = new GNM_CompanyLocale
                                {
                                    Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                    Company_Name = reader.GetString(reader.GetOrdinal("Company_Name"))
                                };
                            }
                        }
                    }
                }

                // Insert or Update MovementTypeLocale
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = mtypeloc == null ? "Up_Ins_AM_ERP_InsertMovementTypeLocale" : "Up_Upd_AM_ERP_UpdateMovementTypeLocale";
                    using (SqlCommand command = new SqlCommand(query, conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        if (mtypeloc == null)
                        {
                            command.Parameters.AddWithValue("@MovementType_ID", InsertLocaleCoreMovementTypeobj.ID);
                            command.Parameters.AddWithValue("@Description", Desc);
                            command.Parameters.AddWithValue("@Language_ID", InsertLocaleCoreMovementTypeobj.Language_ID);
                        }
                        else
                        {
                            command.Parameters.AddWithValue("@MovementTypeLocale_ID", mtypeloc.MovementTypeLocale_ID);
                            command.Parameters.AddWithValue("@Description", Desc);
                        }

                        conn.Open();
                        command.ExecuteScalar();
                    }
                }

                // Insert GPS details
                // gbl.InsertGPSDetails(Convert.ToInt32(InsertLocaleCoreMovementTypeobj.Company_ID), Convert.ToInt32(InsertLocaleCoreMovementTypeobj.Branch), Convert.ToInt32(InsertLocaleCoreMovementTypeobj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreMovementType")), InsertLocaleCoreMovementTypeobj.ID, 0, 0, "Update", false, Convert.ToInt32(InsertLocaleCoreMovementTypeobj.MenuID));

                x = new { MoventTypeID = mtypeloc == null ? InsertLocaleCoreMovementTypeobj.ID : mtypeloc.MovementTypeLocale_ID, compL.Company_Name };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(x);
        }
        #endregion


        #region ::: SelectSingleLocale Uday Kumar J B 14-08-2024 :::
        /// <summary>
        /// Select Single Locale
        /// </summary>
        /// 
        public static IActionResult SelectSingleLocale(string connString, SelectSingleLocaleList SelectSingleLocaleobj)
        {
            var x = new List<dynamic>();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                //GNM_User User = (GNM_User)Session["UserDetails"];

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Get MovementType
                    using (SqlCommand cmd = new SqlCommand("usp_GetMovementTypeByID", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@MovementType_ID", SelectSingleLocaleobj.ID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                int companyID = reader.GetInt32(reader.GetOrdinal("Company_ID"));
                                var movementTypeObj = new
                                {
                                    MovementType_ID = reader.GetInt32(reader.GetOrdinal("MovementType_ID")),
                                    Description = reader.GetString(reader.GetOrdinal("Description")),
                                    IsActive = reader.GetBoolean(reader.GetOrdinal("IsActive")),
                                    Company_ID = companyID,
                                    Company_Name = string.Empty,
                                    Company_NameL = string.Empty,
                                    MovementTypeLocale_ID = 0,
                                    DescriptionL = string.Empty
                                };

                                // Get MovementTypeLocale
                                using (SqlCommand cmdLoc = new SqlCommand("usp_GetMovementTypeLocaleByIDAndLanguage", conn))
                                {
                                    cmdLoc.CommandType = CommandType.StoredProcedure;
                                    cmdLoc.Parameters.AddWithValue("@MovementType_ID", movementTypeObj.MovementType_ID);
                                    cmdLoc.Parameters.AddWithValue("@Language_ID", SelectSingleLocaleobj.Language_ID);

                                    using (SqlDataReader readerLoc = cmdLoc.ExecuteReader())
                                    {
                                        if (readerLoc.Read())
                                        {
                                            movementTypeObj = new
                                            {
                                                movementTypeObj.MovementType_ID,
                                                movementTypeObj.Description,
                                                movementTypeObj.IsActive,
                                                movementTypeObj.Company_ID,
                                                movementTypeObj.Company_Name,
                                                movementTypeObj.Company_NameL,
                                                MovementTypeLocale_ID = readerLoc.GetInt32(readerLoc.GetOrdinal("MovementTypeLocale_ID")),
                                                DescriptionL = readerLoc.GetString(readerLoc.GetOrdinal("Description"))
                                            };
                                        }
                                    }
                                }

                                // Get Company and Locale
                                using (SqlCommand cmdComp = new SqlCommand("usp_GetCompanyAndLocaleByIDAndLanguage", conn))
                                {
                                    cmdComp.CommandType = CommandType.StoredProcedure;
                                    cmdComp.Parameters.AddWithValue("@Company_ID", movementTypeObj.Company_ID);
                                    cmdComp.Parameters.AddWithValue("@Language_ID", SelectSingleLocaleobj.Language_ID);

                                    using (SqlDataReader readerComp = cmdComp.ExecuteReader())
                                    {
                                        if (readerComp.Read())
                                        {
                                            movementTypeObj = new
                                            {
                                                movementTypeObj.MovementType_ID,
                                                movementTypeObj.Description,
                                                movementTypeObj.IsActive,
                                                movementTypeObj.Company_ID,
                                                Company_Name = readerComp.GetString(readerComp.GetOrdinal("Company_Name")),
                                                Company_NameL = readerComp.GetString(readerComp.GetOrdinal("Company_NameL")),
                                                movementTypeObj.MovementTypeLocale_ID,
                                                movementTypeObj.DescriptionL
                                            };
                                        }
                                    }
                                }

                                x.Add(movementTypeObj);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(x);
        }

        #endregion


        #region ::: SelectMovemenTypeLocale Uday Kumar J B 14-08-2024:::
        /// <summary>
        /// To select the All parts 
        /// </summary>  
        /// 

        public static IActionResult SelectMovemenTypeLocale(string connString, SelectMovemenTypeLocaleList SelectMovemenTypeLocaleobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int count = 0;
            int total = 0;
            var x = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string AppPath = string.Empty;
            try
            {
                // GNM_User User = SelectMovemenTypeLocaleobj.UserDetails.FirstOrDefault();
                int CompanyID = SelectMovemenTypeLocaleobj.Company_ID;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("Up_Sel_AM_ERP_SelectMovementTypeLocale", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", CompanyID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            List<object> dataList = new List<object>();

                            while (reader.Read())
                            {
                                object item = new
                                {
                                    view = "<img id='" + reader["ID"] + "' src='" + AppPath + "/Content/plus.gif' key='" + reader["ID"] + "' class='MovementTypeview' MovementTypeLocale='" + reader["Description"] + "'/>",
                                    ID = reader["ID"],
                                    Description = reader["Description"],
                                    Company = reader["Company"],
                                    IsActive = reader["IsActive"]
                                };

                                dataList.Add(item);
                            }

                            count = dataList.Count;
                            total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                            x = new
                            {
                                total = total,
                                page = page,
                                records = count,
                                data = dataList.Paginate(page, rows)
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(x);
        }
        #endregion


        #region ::: CheckMovementType Uday Kumar J B 14-08-2024 :::
        /// <summary>
        /// To Check if Movement type name Already exists /// 
        /// </summary>
        /// 

        public static IActionResult CheckMovementType(string connString, CheckMovementTypeList CheckMovementTypeobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            int Count = 0;
            try
            {
                string MovementTypeName = Common.DecryptString(CheckMovementTypeobj.MovementTypeName);
                int CompanyID = Convert.ToInt32(CheckMovementTypeobj.Company_ID.ToString());
                int PCompanyID = 0;

                // Get Parent Company ID using ADO.NET
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("GetParentCompanyID", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@CompanyID", CompanyID);

                        SqlParameter parentCompanyIdParam = new SqlParameter("@ParentCompanyID", SqlDbType.Int);
                        parentCompanyIdParam.Direction = ParameterDirection.Output;
                        cmd.Parameters.Add(parentCompanyIdParam);

                        cmd.ExecuteNonQuery();
                        PCompanyID = Convert.ToInt32(parentCompanyIdParam.Value);
                    }
                }

                // Check Movement Type using ADO.NET
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("Up_Sel_AM_ERP_CheckMovementType1", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@MovementTypeName", MovementTypeName);
                        cmd.Parameters.AddWithValue("@MovementTypeID", CheckMovementTypeobj.MovementTypeID);

                        SqlParameter returnParameter = cmd.Parameters.Add("@Count", SqlDbType.Int);
                        returnParameter.Direction = ParameterDirection.ReturnValue;

                        cmd.ExecuteNonQuery();

                        Count = Convert.ToInt32(returnParameter.Value);
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Count);
        }
        #endregion


        #region ::: CheckMovementTypeLocale Uday Kumar J B 14-08-2024:::
        /// <summary>
        /// To Check MovementTypeLocale already exists 
        /// </summary>
        /// 
        public static IActionResult CheckMovementTypeLocale(string connString, CheckMovementTypeLocaleList CheckMovementTypeLocaleobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            int Count = 0;
            try
            {
                string MovementTypeLocale = Common.DecryptString(CheckMovementTypeLocaleobj.MovementTypeLocale);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("CheckMovementTypeLocale", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@MovementTypeLocale", MovementTypeLocale);
                        cmd.Parameters.AddWithValue("@MovementTypeID", CheckMovementTypeLocaleobj.MovementTypeID);

                        SqlParameter returnParameter = cmd.Parameters.Add("@Count", SqlDbType.Int);
                        returnParameter.Direction = ParameterDirection.ReturnValue;

                        cmd.ExecuteNonQuery();

                        Count = Convert.ToInt32(returnParameter.Value);
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(Count);
        }
        #endregion


        #region ::: CoreMovementType list and obj classes Uday Kumar J B 14-08-2024:::
        /// <summary>
        /// Core Movement Type list and obj classes
        /// </summary>
        /// 
        public class ExportCoreMovementTypeList
        {
            public List<GNM_User> UserDetails { get; set; }
            public string GeneralCulture { get; set; }
            public int exprtType { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public int LanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public string UserCulture { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filter { get; set; }
            public string advanceFilter { get; set; }

        }

        public class CheckMovementTypeLocaleList
        {
            public string MovementTypeLocale { get; set; }
            public int MovementTypeID { get; set; }
        }
        public class CheckMovementTypeList
        {
            public string MovementTypeName { get; set; }
            public int MovementTypeID { get; set; }
            public int Company_ID { get; set; }
        }
        public class SelectMovemenTypeLocaleList
        {
            public List<GNM_User> UserDetails { get; set; }
            public int Company_ID { get; set; }
        }
        public class SelectSingleLocaleList
        {
            public List<GNM_User> UserDetails { get; set; }
            public int ID { get; set; }
            public int Language_ID { get; set; }
        }
        public class InsertLocaleCoreMovementTypeList
        {
            public string Desc { get; set; }
            public int ID { get; set; }
            public List<GNM_User> UserDetails { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int Language_ID { get; set; }
            public int MenuID { get; set; }
        }


        public class DeleteCoreMovementTypeList
        {
            public List<GNM_User> UserDetails { get; set; }
            public string key { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string GeneralCulture { get; set; }
            public int User_ID { get; set; }
        }

        public class InsertCoreMovementTypeList
        {
            public List<GNM_User> UserDetails { get; set; }
            public string key { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int MenuID { get; set; }
            public int User_ID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }
        public class SelectCoreMovementTypeList
        {
            public int Company_ID { get; set; }
            public string GeneralCulture { get; set; }
            public List<GNM_User> UserDetails { get; set; }
            public int Branch { get; set; }
            public int MenuID { get; set; }
        }
        #endregion


        #region ::: CoreMovementType Classes Uday Kumar J B 14-08-2024 :::
        /// <summary>
        /// CoreMovementType Classes
        /// </summary>
        /// 
        public partial class GNM_MovementType
        {
            public GNM_MovementType()
            {
                this.GNM_MovementTypeDefinition = new HashSet<GNM_MovementTypeDefinition>();
                this.GNM_MovementTypeLocale = new HashSet<GNM_MovementTypeLocale>();
            }

            public int MovementType_ID { get; set; }
            public string Description { get; set; }
            public int Company_ID { get; set; }
            public bool IsActive { get; set; }

            public virtual ICollection<GNM_MovementTypeDefinition> GNM_MovementTypeDefinition { get; set; }
            public virtual ICollection<GNM_MovementTypeLocale> GNM_MovementTypeLocale { get; set; }
        }

        public partial class GNM_MovementTypeDefinition
        {
            public int MovementTypeDefinition_ID { get; set; }
            public int MovementType_ID { get; set; }
            public int Company_ID { get; set; }
            public int Branch_ID { get; set; }
            public bool IsActive { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }
            public int Unmoved { get; set; }
            public int Slow { get; set; }
            public int Medium { get; set; }
            public int Fast { get; set; }

            public virtual GNM_MovementType GNM_MovementType { get; set; }
        }

        public partial class GNM_MovementTypeLocale
        {
            public int MovementTypeLocale_ID { get; set; }
            public int MovementType_ID { get; set; }
            public string Description { get; set; }
            public int Language_ID { get; set; }

            public virtual GNM_MovementType GNM_MovementType { get; set; }
        }

        public class PRMMovementType
        {
            public int MovementType_ID { get; set; }
            public string MovementType_Description { get; set; }
            public string IsActive { get; set; }
            public int UnMoved { get; set; }
            public int Slow { get; set; }
            public int Medium { get; set; }
            public int Fast { get; set; }
            public string Description { get; set; }
            public string Company { get; set; }
            public int Company_ID { get; set; }
        }

        public class ParentCompanyObject
        {
            public int Company_ID
            {
                get;
                set;
            }

            public string Company_Name
            {
                get;
                set;
            }

            public int? Company_Parent_ID
            {
                get;
                set;
            }
        }
        #endregion

    }
}
