﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Xml.Linq;
using WorkFlow.Models;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CoreExchangeRateMasterServices
    {

        #region ::: Load ExchangeMaster Landing Grid /Mihtun:::
        /// <summary>
        /// To Load Service type Landing Grid
        /// </summary>  

        public static IActionResult SelectAllExchangeRate(SelectAllExchangeRateList SelectAllExchangeRateObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, bool advnce, string filters, string Query)
        {
            try
            {
                int count = 0;
                int total = 0;
                //GNM_User User = (GNM_User)Session["UserDetails"];
                // GNM_User User = SelectAllExchangeRateObj.UserDetails.FirstOrDefault();
                int companyID = SelectAllExchangeRateObj.Company_ID;

                List<ExchangeRateMaster> exchangeRates = new List<ExchangeRateMaster>();
                List<GNM_RefMasterDetail> gnmCurrency = new List<GNM_RefMasterDetail>();
                List<GNM_RefMasterDetail> curr = new List<GNM_RefMasterDetail>();

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("SELECT * FROM GNM_EXCHANGERATEMASTER", conn))
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            exchangeRates.Add(new ExchangeRateMaster
                            {
                                EXCHANGERATE_ID = reader.GetInt32(reader.GetOrdinal("EXCHANGERATE_ID")),
                                EXCHANGERATE = reader.GetDecimal(reader.GetOrdinal("EXCHANGERATE")),
                                CURRENCY_ID = reader.GetInt32(reader.GetOrdinal("CURRENCY_ID")),
                                COMPANY_ID = reader.GetInt32(reader.GetOrdinal("COMPANY_ID")),
                                EFFECTIVEDATE = reader.GetDateTime(reader.GetOrdinal("EFFECTIVEDATE")).ToString("yyyy-MM-dd")
                            });
                        }
                    }

                    using (SqlCommand cmd = new SqlCommand("SELECT rd.* FROM GNM_RefMasterDetail rd JOIN GNM_RefMaster rm ON rd.RefMaster_ID = rm.RefMaster_ID WHERE rm.RefMaster_Name = 'CURRENCY'", conn))
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var refMasterDetail = new GNM_RefMasterDetail
                            {
                                RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                RefMasterDetail_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name")),
                                RefMasterDetail_IsActive = reader.GetBoolean(reader.GetOrdinal("RefMasterDetail_IsActive"))
                            };
                            gnmCurrency.Add(refMasterDetail);
                            if (refMasterDetail.RefMasterDetail_IsActive)
                            {
                                curr.Add(refMasterDetail);
                            }
                        }
                    }
                }

                var Currency = curr.OrderBy(i => i.RefMasterDetail_Name)
                                   .Select(a => new
                                   {
                                       a.RefMasterDetail_ID,
                                       a.RefMasterDetail_Name
                                   }).ToList();

                string JsonExchangeRate = "-1:--" + CommonFunctionalities.GetResourceString(SelectAllExchangeRateObj.GeneralCulture.ToString(), "select").ToString() + "--;";
                for (int i = 0; i < Currency.Count(); i++)
                {
                    JsonExchangeRate = JsonExchangeRate + Currency[i].RefMasterDetail_ID + ":" + Currency[i].RefMasterDetail_Name + ";";
                }
                JsonExchangeRate = JsonExchangeRate.TrimEnd(new char[] { ';' });

                var arrExchangeRate = from a in exchangeRates
                                      join curr1 in gnmCurrency on a.CURRENCY_ID equals curr1.RefMasterDetail_ID
                                      where a.COMPANY_ID == companyID
                                      select new ExchangeRateMaster
                                      {
                                          edit = "<a title='View' href='#' id='" + a.EXCHANGERATE_ID + "' key='" + a.EXCHANGERATE_ID + "' class='editEmployee font-icon-class' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                          EXCHANGERATE_ID = a.EXCHANGERATE_ID,
                                          EXCHANGERATE = Convert.ToDecimal(a.EXCHANGERATE),
                                          EXCHANGERATESORT = Convert.ToDecimal(a.EXCHANGERATE).ToString("0.00000"),
                                          CURRENCY = curr1.RefMasterDetail_Name.ToString(),
                                          EFFECTIVEDATE = a.EFFECTIVEDATE,
                                          EFFECTIVEDATESORT = a.EFFECTIVEDATESORT

                                      };

                var iQExchangeRate = arrExchangeRate.AsQueryable();


                if (_search)
                {
                    Filters filtersObj = JObject.Parse(filters).ToObject<Filters>();
                    iQExchangeRate = iQExchangeRate.FilterSearch(filtersObj);
                }
                else if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(Query)).ToObject<AdvanceFilter>();
                    iQExchangeRate = iQExchangeRate.AdvanceSearch(advnfilter);
                }

                iQExchangeRate = iQExchangeRate.OrderByField(sidx, sord);
                count = iQExchangeRate.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }

                var ExchangeRateArray = from a in iQExchangeRate.AsEnumerable()
                                        select new
                                        {
                                            edit = "<a id='" + a.EXCHANGERATE_ID + "' ' key='" + a.EXCHANGERATE_ID + "' class='editEmployee' editmode='false'/>",
                                            a.EXCHANGERATE_ID,
                                            EXCHANGERATE = a.EXCHANGERATE,
                                            CURRENCY = a.CURRENCY,
                                            EFFECTIVEDATE = a.EFFECTIVEDATE,
                                            EXCHANGERATESORT = a.EXCHANGERATESORT,
                                            EFFECTIVEDATESORT = a.EFFECTIVEDATESORT.ToString("yyyy-MM-dd")
                                        };

                var x = new
                {
                    JsonExchangeRate = JsonExchangeRate,
                    total = total,
                    page = page,
                    //filter = Request.Params["filters"],
                    //advanceFilter = Request.Params["Query"],
                    records = count,
                    data = ExchangeRateArray.ToList().Paginate(page, rows)
                };

                //return Json(x, JsonRequestBehavior.AllowGet);
                return new JsonResult(x);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                //return RedirectToAction("Error");
                return new JsonResult(wex);
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
                return new JsonResult(ex);
            }
        }


        #endregion

        #region ::: Save /Mithun:::
        /// <summary>
        /// To Insert and Update State
        /// </summary>
        public static IActionResult Insert(InsertExchangeRateList InsertObj, string constring, int LogException)
        {
            string Msg;
            try
            {
                //GNM_User User = (GNM_User)Session["UserDetails"];
                //  GNM_User User = InsertObj.UserDetails.FirstOrDefault();
                int companyID = InsertObj.Company_ID;

                JObject jObj = JObject.Parse(InsertObj.data);
                int Count = jObj["rows"].Count();

                // Create an XML string from the JSON data
                XDocument xDoc = new XDocument(new XElement("ExchangeRates",
                    from r in jObj["rows"]
                    select new XElement("ExchangeRate",
                        new XElement("CurrencyID", (int)r["CURRENCY_ID"]),
                        new XElement("ExchangeRate", (decimal)r["EXCHANGERATE"]),
                        new XElement("EffectiveDate", (DateTime)r["EFFECTIVEDATE"]),
                        new XElement("CompanyID", companyID)
                    )
                ));


                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("Up_Ins_AM_ERP_InsertExchangeRateDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add(new SqlParameter("@ExchangeRateDetails", SqlDbType.Xml) { Value = xDoc.ToString() });

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                int addedExchangeRateID = reader.GetInt32(0);

                                //gbl.InsertGPSDetails(
                                //    Convert.ToInt32(InsertObj.Company_ID),
                                //    Convert.ToInt32(InsertObj.Branch),
                                //    Convert.ToInt32(InsertObj.User_ID),
                                //    Convert.ToInt32(Common.GetObjectID("CoreExchangeRateMaster",constring)),
                                //    addedExchangeRateID, 0, 0, "Insert", false, Convert.ToInt32(InsertObj.MenuID)
                                //);
                            }
                        }
                    }
                }

                Msg = "Saved";
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                Msg = string.Empty;
            }
            //return Msg;
            return new JsonResult(Msg);
        }


        #endregion

        #region ::: CheckDuplicateRecord /Mithun:::
        /// <summary>
        /// To Check State already exists 
        /// </summary>
        public static IActionResult CheckDuplicateRecord(CheckDuplicateRecordList CheckDuplicateRecordObj, string constring, int LogException)
        {
            int CompanyID = Convert.ToInt32(CheckDuplicateRecordObj.Company_ID);
            int Count = 0;
            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    using (SqlCommand cmd = new SqlCommand("UP_SEL_AM_ERP_CheckDuplicateExchangeRate", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@ExchangeRateID", CheckDuplicateRecordObj.EXCHANGERATE_ID);
                        cmd.Parameters.AddWithValue("@CurrencyID", CheckDuplicateRecordObj.CURRENCY_ID);
                        cmd.Parameters.AddWithValue("@EffectiveDate", CheckDuplicateRecordObj.EFFECTIVEDATE);
                        cmd.Parameters.AddWithValue("@CompanyID", CompanyID);

                        SqlParameter countParam = new SqlParameter("@Count", SqlDbType.Int);
                        countParam.Direction = ParameterDirection.Output;
                        cmd.Parameters.Add(countParam);

                        conn.Open();
                        cmd.ExecuteNonQuery();
                        Count = (int)countParam.Value;
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            //return Count;
            return new JsonResult(Count);
        }


        #endregion

        #region ::: Data For Export /Mithun:::

        public static List<ExchangeRateMaster> GetExchangeRateData(SelectAllExchangeRateList ExportObj, string constring)
        {
            List<ExchangeRateMaster> exchangeRates = new List<ExchangeRateMaster>();
            List<GNM_RefMasterDetail> gnmCurrency = new List<GNM_RefMasterDetail>();

            using (SqlConnection conn = new SqlConnection(constring))
            {
                conn.Open();

                // Fetch exchange rates from GNM_EXCHANGERATEMASTER
                using (SqlCommand cmd = new SqlCommand("SELECT * FROM GNM_EXCHANGERATEMASTER", conn))
                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        exchangeRates.Add(new ExchangeRateMaster
                        {
                            EXCHANGERATE_ID = reader.GetInt32(reader.GetOrdinal("EXCHANGERATE_ID")),
                            EXCHANGERATE = reader.GetDecimal(reader.GetOrdinal("EXCHANGERATE")),
                            CURRENCY_ID = reader.GetInt32(reader.GetOrdinal("CURRENCY_ID")),
                            COMPANY_ID = reader.GetInt32(reader.GetOrdinal("COMPANY_ID")),
                            EFFECTIVEDATE = reader.GetDateTime(reader.GetOrdinal("EFFECTIVEDATE")).ToString("yyyy-MM-dd")
                        });
                    }
                }

                // Fetch currency details from GNM_RefMasterDetail
                using (SqlCommand cmd = new SqlCommand(@"SELECT rd.* 
                                                 FROM GNM_RefMasterDetail rd 
                                                 JOIN GNM_RefMaster rm 
                                                 ON rd.RefMaster_ID = rm.RefMaster_ID 
                                                 WHERE rm.RefMaster_Name = 'CURRENCY'", conn))
                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        gnmCurrency.Add(new GNM_RefMasterDetail
                        {
                            RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                            RefMasterDetail_Name = reader.GetString(reader.GetOrdinal("RefMasterDetail_Name")),
                            RefMasterDetail_IsActive = reader.GetBoolean(reader.GetOrdinal("RefMasterDetail_IsActive"))
                        });
                    }
                }
            }

            // Map the currency names to the exchange rates by CURRENCY_ID
            foreach (var rate in exchangeRates)
            {
                var currency = gnmCurrency.FirstOrDefault(c => c.RefMasterDetail_ID == (int)rate.CURRENCY_ID);
                if (currency != null)
                {
                    rate.CURRENCY = currency.RefMasterDetail_Name;
                }
            }

            return exchangeRates;
        }

        #endregion


        #region ::: Export /Mithun:::

        public static async Task<object> Export(SelectAllExchangeRateList ExportObj, string constring, int LogException, string filters, string Query, string sidx, string sord)
        {
            try
            {
                // Create DataTable for ExchangeRate
                DataTable DtExchangeRate = new DataTable();
                DtExchangeRate.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "Currency").ToString());
                DtExchangeRate.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "ExchangeRate").ToString());
                DtExchangeRate.Columns.Add(CommonFunctionalities.GetResourceString(ExportObj.GeneralCulture.ToString(), "EffectiveDate").ToString());

                // Get exchange rate data
                var exchangeRates = GetExchangeRateData(ExportObj, constring);
                var iQExchangeRate = exchangeRates.AsQueryable();

                // Apply filters if provided
                if (!string.IsNullOrEmpty(filters) && filters != "undefined")
                {
                    Filters filtersObj = JObject.Parse(filters).ToObject<Filters>();
                    iQExchangeRate = iQExchangeRate.FilterSearch(filtersObj);
                }
                else if (!string.IsNullOrEmpty(Query) && Query != "null" && Query != "undefined")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(Query)).ToObject<AdvanceFilter>();
                    iQExchangeRate = iQExchangeRate.AdvanceSearch(advnfilter);
                }

                // Apply sorting
                iQExchangeRate = iQExchangeRate.OrderByField(sidx, sord);

                // Populate DtExchangeRate with sorted data
                if (iQExchangeRate.Any())
                {
                    foreach (var rate in iQExchangeRate)
                    {
                        DtExchangeRate.Rows.Add(rate.CURRENCY, rate.EXCHANGERATE, rate.EFFECTIVEDATE);
                    }
                }
                else
                {
                    // Log or handle empty data scenario
                    LS.LogSheetExporter.LogToTextFile(0, "No data found for export", "Export Method", "Check exchange rate data population");
                }

                // Prepare ExportList object
                ExportList reportExportList = new ExportList
                {
                    Company_ID = ExportObj.Company_ID,
                    Branch = ExportObj.Branch_ID,
                    dt1 = DtExchangeRate, // Exporting the main DataTable
                    dt = DtExchangeRate,      // Criteria (if needed)
                    FileName = "ExchangeRate",  // Set a default or dynamic filename
                    Header = CommonFunctionalities.GetResourceString(ExportObj.UserCulture.ToString(), "ExchangeRate").ToString(),
                    exprtType = ExportObj.exprtType,  // Assuming 1 for Excel, adjust as needed
                    UserCulture = ExportObj.UserCulture
                };

                // Export document using prepared data
                var res = await DocumentExport.Export(reportExportList, constring, LogException);
                return res.Value;
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return null;
        }


        #endregion



        public class CheckDuplicateRecordList
        {
            public int Company_ID { get; set; }
            public int EXCHANGERATE_ID { get; set; }
            public int CURRENCY_ID { get; set; }
            public DateTime EFFECTIVEDATE { get; set; }
        }
        public class InsertExchangeRateList
        {
            public string data { get; set; }
            public int Company_ID { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public int Branch { get; set; }
            public List<GNM_User> UserDetails { get; set; }
            public DateTime LoggedINDateTime { get; set; }
        }


        public class SelectAllExchangeRateList
        {
            public List<GNM_User> UserDetails { get; set; }
            public string GeneralCulture { get; set; }
            public string UserCulture { get; set; }
            public int Company_ID { get; set; }
            public int Branch_ID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int UserLanguageID { get; set; }
            public int exprtType { get; set; }
            public string filters { get; set; }
            public string Query { get; set; }
            public bool _search { get; set; }
            public bool advnce { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }

        }
        public class ExchangeRateMaster
        {
            public int EXCHANGERATE_ID
            {
                get;
                set;
            }

            public decimal EXCHANGERATE
            {
                get;
                set;
            }

            public string EXCHANGERATESORT
            {
                get;
                set;
            }

            public string EFFECTIVEDATE
            {
                get;
                set;
            }
            public DateTime EFFECTIVEDATESORT
            {
                get;
                set;
            }

            public string CURRENCY
            {
                get;
                set;
            }
            public string edit { get; set; }
            public object CURRENCY_ID { get; internal set; }
            public int COMPANY_ID { get; internal set; }

        }
    }
}
