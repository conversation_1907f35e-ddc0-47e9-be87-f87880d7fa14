﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Internal;
using Microsoft.Extensions.Primitives;
using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.CoreImportInterfaceServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class CoreImportInterfaceController : ApiController
    {

        #region :::FillTableNames   Uday Kumar J B 03-10-2024 :::
        /// <summary>
        /// To Get FillTableNames 
        /// </summary>
        /// 
        [Route("api/CoreImportInterface/FillTableNames")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult FillTableNames()
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreImportInterfaceServices.FillTableNames(connString);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::FillTableNames   Uday Kumar J B 03-10-2024 :::
        /// <summary>
        /// To Get FillTableNames 
        /// </summary>
        /// 
        [Route("api/CoreImportInterface/FillColumnNames")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult FillColumnNames([FromBody] FillColumnNamesList FillColumnNamesobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreImportInterfaceServices.FillColumnNames(connString, FillColumnNamesobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: ImportInterface Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// ImportInterface 
        /// </summary>
        [Route("api/CoreImportInterface/ImportInterface")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> ImportInterface()
        {
            try
            {
                var provider = new MultipartMemoryStreamProvider();

                // Read the multipart form data into the provider
                await Request.Content.ReadAsMultipartAsync(provider);

                // Retrieve form data
                IFormFile postedFile = null;


                foreach (var file in provider.Contents)
                {
                    var fileName = file.Headers.ContentDisposition?.FileName?.Trim('"');
                    if (fileName != null)
                    {
                        // Retrieve ContentType from Headers
                        var contentType = file.Headers.ContentType?.MediaType;

                        // Read file content as a stream
                        var stream = await file.ReadAsStreamAsync();

                        // Construct FormFile
                        postedFile = new FormFile(stream, 0, stream.Length, file.Headers.ContentDisposition.Name.Trim('"'), fileName)
                        {
                            Headers = new HeaderDictionary
            {
                { "Content-Type", new StringValues(contentType) }
            },
                            ContentType = contentType // Set ContentType if needed
                        };

                        // Now you can use postedFile.ContentType if required
                    }
                }

                // Call your service method to process the data
                var Response = default(dynamic);
                Response = CoreImportInterfaceServices.ImportInterface(postedFile);

                return Ok(Response.Value);
            }
            catch (Exception ex)
            {
                // Log or handle exceptions
                return InternalServerError(ex);
            }
        }
        #endregion


        #region ::: MapColumns Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// MapColumns 
        /// </summary>
        [Route("api/CoreImportInterface/MapColumns")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult MapColumns([FromBody] MapColumnsList MapColumnsobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreImportInterfaceServices.MapColumns(connString, MapColumnsobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: Select Uday Kumar J B 30-09-2024:::
        /// <summary>
        /// To select All Log Report Data
        /// </summary>
        ///
        [Route("api/CoreImportInterface/MappedData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult MappedData([FromBody] MappedDataList MappedDataobj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreImportInterfaceServices.MappedData(connstring, MappedDataobj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: DeleteMapping Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// DeleteMapping 
        /// </summary>
        [Route("api/CoreImportInterface/DeleteMapping")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DeleteMapping([FromBody] DeleteMappingList DeleteMappingobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreImportInterfaceServices.DeleteMapping(connString, DeleteMappingobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SetUniqueKey Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// SetUniqueKey 
        /// </summary>
        [Route("api/CoreImportInterface/SetUniqueKey")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SetUniqueKey([FromBody] SetUniqueKeyList SetUniqueKeyobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreImportInterfaceServices.SetUniqueKey(connString, SetUniqueKeyobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: ImportToDataBase Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// ImportToDataBase 
        /// </summary>
        [Route("api/CoreImportInterface/ImportToDataBase")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult ImportToDataBase([FromBody] ImportToDataBaseList ImportToDataBaseobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreImportInterfaceServices.ImportToDataBase(connString, ImportToDataBaseobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: GetDatas Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// GetDatas 
        /// </summary>
        [Route("api/CoreImportInterface/GetDatas")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetDatas([FromBody] GetDataList GetDataobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreImportInterfaceServices.GetDatas(connString, GetDataobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: FillStoredFormat Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// FillStoredFormat 
        /// </summary>
        [Route("api/CoreImportInterface/FillStoredFormat")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult FillStoredFormat([FromBody] FillStoredFormatList FillStoredFormatobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreImportInterfaceServices.FillStoredFormat(connString, FillStoredFormatobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: CheckFormat Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// CheckFormat 
        /// </summary>
        [Route("api/CoreImportInterface/CheckFormat")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckFormat([FromBody] CheckFormatList CheckFormatobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreImportInterfaceServices.CheckFormat(connString, CheckFormatobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: SaveFormat Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// SaveFormat 
        /// </summary>
        [Route("api/CoreImportInterface/SaveFormat")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SaveFormat([FromBody] SaveFormatList SaveFormatobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreImportInterfaceServices.SaveFormat(connString, SaveFormatobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region ::: FillFormattedColumnNames Uday Kumar J B 03-10-2024:::
        /// <summary>
        /// FillFormattedColumnNames 
        /// </summary>
        [Route("api/CoreImportInterface/FillFormattedColumnNames")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult FillFormattedColumnNames([FromBody] FillFormattedColumnNamesList FillFormattedColumnNamesobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = CoreImportInterfaceServices.FillFormattedColumnNames(connString, FillFormattedColumnNamesobj);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



    }
}