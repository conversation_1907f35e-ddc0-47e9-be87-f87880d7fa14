﻿using AMMSCore.Models;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_AMERP.Utilities;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Threading.Tasks;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.CoreProductMasterServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class CorePartyMasterServices
    {

        #region :::SelectPartyMaster   Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// To Get SelectPartyMaster 
        /// </summary>
        /// 
        public static IQueryable<partydetails> GetPartyMaster(string connString, int Company_ID, string sidx, string sord, int page, int rows, bool Search, string Searchstring, bool AdvanceSearch, string AdvanceSearchstring, int partyType)
        {
            List<partydetails> partyList = new List<partydetails>();


            using (SqlConnection connection = new SqlConnection(connString))
            {
                SqlCommand command = new SqlCommand("UP_Get_AM_ERP_GetPartyMaster", connection);
                command.CommandType = CommandType.StoredProcedure;
                command.Parameters.AddWithValue("@partyType", partyType);
                command.Parameters.AddWithValue("@CompanyID", Company_ID);
                command.Parameters.AddWithValue("@page", page);     // Page number
                command.Parameters.AddWithValue("@rows", rows);

                connection.Open();
                SqlDataReader reader = command.ExecuteReader();

                while (reader.Read())
                {
                    partydetails party = new partydetails();

                    // Non-nullable fields with default values if null
                    party.Party_ID = !reader.IsDBNull(reader.GetOrdinal("Party_ID")) ? reader.GetInt32(reader.GetOrdinal("Party_ID")) : 0;
                    party.Party_IsActive = !reader.IsDBNull(reader.GetOrdinal("Party_IsActive")) ? reader.GetBoolean(reader.GetOrdinal("Party_IsActive")) : false;
                    party.Party_IsLocked = !reader.IsDBNull(reader.GetOrdinal("Party_IsLocked")) ? reader.GetBoolean(reader.GetOrdinal("Party_IsLocked")) : false;

                    // Nullable fields
                    party.Party_Code = !reader.IsDBNull(reader.GetOrdinal("Party_Code")) ? reader.GetString(reader.GetOrdinal("Party_Code")) : string.Empty;
                    party.Party_Name = !reader.IsDBNull(reader.GetOrdinal("Party_Name")) ? reader.GetString(reader.GetOrdinal("Party_Name")) : string.Empty;
                    party.Party_Location = !reader.IsDBNull(reader.GetOrdinal("Party_Location")) ? reader.GetString(reader.GetOrdinal("Party_Location")) : string.Empty;
                    party.Party_Email = !reader.IsDBNull(reader.GetOrdinal("Party_Email")) ? reader.GetString(reader.GetOrdinal("Party_Email")) : string.Empty;
                    party.Party_Phone = !reader.IsDBNull(reader.GetOrdinal("Party_Phone")) ? reader.GetString(reader.GetOrdinal("Party_Phone")) : string.Empty;
                    party.Party_Fax = !reader.IsDBNull(reader.GetOrdinal("Party_Fax")) ? reader.GetString(reader.GetOrdinal("Party_Fax")) : string.Empty;
                    party.PartyAddress_Address = !reader.IsDBNull(reader.GetOrdinal("PartyAddress_Address")) ? reader.GetString(reader.GetOrdinal("PartyAddress_Address")) : string.Empty;
                    party.Party_PaymentTerms = !reader.IsDBNull(reader.GetOrdinal("Party_PaymentTerms")) ? reader.GetString(reader.GetOrdinal("Party_PaymentTerms")) : string.Empty;

                    // Nullable integers
                    party.Country_ID = reader.IsDBNull(reader.GetOrdinal("Country_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Country_ID"));
                    party.State_ID = reader.IsDBNull(reader.GetOrdinal("State_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("State_ID"));
                    party.Company_ID = reader.IsDBNull(reader.GetOrdinal("Company_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Company_ID"));

                    // Nullable booleans
                    party.IsOEM = reader.IsDBNull(reader.GetOrdinal("IsOEM")) ? false : reader.GetBoolean(reader.GetOrdinal("IsOEM"));
                    party.IsDealer = reader.IsDBNull(reader.GetOrdinal("IsDealer")) ? false : reader.GetBoolean(reader.GetOrdinal("IsDealer"));

                    // Nullable decimals with default values for non-nullable fields
                    party.PartsCreditLimit = reader.IsDBNull(reader.GetOrdinal("PartsCreditLimit")) ? 0m : reader.GetDecimal(reader.GetOrdinal("PartsCreditLimit"));
                    party.SalesCreditLimit = reader.IsDBNull(reader.GetOrdinal("SalesCreditLimit")) ? 0m : reader.GetDecimal(reader.GetOrdinal("SalesCreditLimit"));
                    party.ServiceCreditLimit = reader.IsDBNull(reader.GetOrdinal("ServiceCreditLimit")) ? 0m : reader.GetDecimal(reader.GetOrdinal("ServiceCreditLimit"));
                    party.ServiceCreditLimitinUSD = reader.IsDBNull(reader.GetOrdinal("ServiceCreditLimitinUSD")) ? 0m : reader.GetDecimal(reader.GetOrdinal("ServiceCreditLimitinUSD"));

                    // Nullable strings with default empty values
                    party.Currency = !reader.IsDBNull(reader.GetOrdinal("Currency")) ? reader.GetString(reader.GetOrdinal("Currency")) : string.Empty;
                    party.Country = !reader.IsDBNull(reader.GetOrdinal("Country")) ? reader.GetString(reader.GetOrdinal("Country")) : string.Empty;
                    party.State = !reader.IsDBNull(reader.GetOrdinal("State")) ? reader.GetString(reader.GetOrdinal("State")) : string.Empty;
                    party.PartyTypestring = !reader.IsDBNull(reader.GetOrdinal("PartyTypestring")) ? reader.GetString(reader.GetOrdinal("PartyTypestring")) : string.Empty;
                    party.Company = !reader.IsDBNull(reader.GetOrdinal("Company")) ? reader.GetString(reader.GetOrdinal("Company")) : string.Empty;

                    // Add the populated object to the list
                    partyList.Add(party);
                }
                reader.Close();
            }

            return partyList.AsQueryable<partydetails>();
        }



        public static IActionResult SelectPartyMaster(string connString, SelectPartyMasterList SelectPartyMasterobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int count = 0;
            int total = 0;
            string AppPath = string.Empty;
            var jsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string QueryCount = "";
            IQueryable<partydetails> IQParty = null;
            int partyType = Convert.ToInt32(SelectPartyMasterobj.partyType);
            try
            {
                IQParty = GetPartyMaster(connString, SelectPartyMasterobj.Company_ID, sidx, sord, page, rows, _search, filters, advnce, advnceFilters, partyType);
                IQParty = IQParty.AsQueryable().OrderByField(sidx, sord);

                // FilterToolBar Search
                if (_search && !string.IsNullOrEmpty(Common.DecryptString(Uri.UnescapeDataString(filters))))
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(Uri.UnescapeDataString(filters))).ToObject<Filters>();
                    if (filtersObj != null && filtersObj.rules.Count > 0)
                    {
                        IQParty = IQParty.FilterSearch(filtersObj);
                    }
                }
                if (advnce && !string.IsNullOrEmpty(advnceFilters))
                {
                    AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<AdvanceFilter>();
                    IQParty = IQParty.AdvanceSearch(advnfilter);
                    page = 1;
                }

                count = IQParty.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }
                string Lbl_Refresh = CommonFunctionalities.GetGlobalResourceObject(SelectPartyMasterobj.UserCulture.ToString(), "refresh").ToString();
                string Lbl_AdvanceSearch = CommonFunctionalities.GetGlobalResourceObject(SelectPartyMasterobj.UserCulture.ToString(), "advancesearch").ToString();
                var title = CommonFunctionalities.GetGlobalResourceObject(SelectPartyMasterobj.UserCulture.ToString(), "view").ToString();
                jsonData = new
                {
                    total = total,
                    page = page,
                    records = count,
                    rows = (from q in IQParty
                            select new
                            {
                                edit = "<a title=\"" + title + "\" href='#' style='font-size: 13px;' key='" + q.Party_ID + "' alt='Edit' party='" + q.Party_Name + "' class='editPartyMaster' ><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input key='" + q.Party_ID + "' type='checkbox' defaultchecked='' class='DelPartyMaster' />",
                                q.Party_ID,
                                q.Party_Code,
                                Party_Name = q.Party_Name,
                                q.Party_Phone,
                                Party_Location = q.Party_Location,
                                q.PartyAddress_Address,
                                q.Party_Mobile,
                                Party_IsActive = q.Party_IsActive == true ? "Yes" : "No",
                                Local = "<a key='" + q.Party_ID + "' id='" + q.Party_ID + "' src='" + AppPath + "/Content/local.png' class='PartyLocale' width='20' height='20' alt='Localize' title='Localize'><i class='fa fa-globe'></i></a>",

                                q.Party_Email,
                                q.Party_Fax,

                            }
                    ).ToList(),
                    filter = filters,
                    advanceFilter = advnceFilters,
                    _search = _search,
                    advnce = advnce,
                    partyType = partyType,
                    sidx = sidx,
                    sord = sord,
                    NRows = rows,
                    IQParty,
                    Lbl_Refresh,
                    Lbl_AdvanceSearch

                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }

        #endregion


        #region :::PartyExport   Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// To Get PartyExport 
        /// </summary>
        /// 
        public static async Task<object> PartyExport(PartyExportList PartyExportobj, string connString, string filters, string advnceFilters, string sidx, string sord)
        {
            IQueryable<partydetails> IQParty = null;
            int partyType = Convert.ToInt32(PartyExportobj.partyTypeID);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                List<partydetails> iPurchaseOrderArray = new List<partydetails>();
                DataTable dt = new DataTable();
                int BranchID = Convert.ToInt32(PartyExportobj.Branch);
                dt.Columns.Add(CommonFunctionalities.GetResourceString(PartyExportobj.UserCulture.ToString(), "CustomerAccountNumber").ToString());
                dt.Columns.Add(CommonFunctionalities.GetResourceString(PartyExportobj.UserCulture.ToString(), "Party").ToString()); //1
                dt.Columns.Add(CommonFunctionalities.GetResourceString(PartyExportobj.UserCulture.ToString(), "Location").ToString());//2
                dt.Columns.Add(CommonFunctionalities.GetResourceString(PartyExportobj.UserCulture.ToString(), "Address").ToString());//2
                dt.Columns.Add(CommonFunctionalities.GetResourceString(PartyExportobj.UserCulture.ToString(), "Phone").ToString());//3
                dt.Columns.Add(CommonFunctionalities.GetResourceString(PartyExportobj.UserCulture.ToString(), "Mobile").ToString());//4
                dt.Columns.Add(CommonFunctionalities.GetResourceString(PartyExportobj.UserCulture.ToString(), "IsActive").ToString());//5
                                                                                                                                      //triveni
                dt.Columns.Add(CommonFunctionalities.GetResourceString(PartyExportobj.UserCulture.ToString(), "Email").ToString());//6
                dt.Columns.Add(CommonFunctionalities.GetResourceString(PartyExportobj.UserCulture.ToString(), "Fax").ToString());//7
                dt.Columns.Add(CommonFunctionalities.GetResourceString(PartyExportobj.UserCulture.ToString(), "Currency").ToString());//8
                dt.Columns.Add(CommonFunctionalities.GetResourceString(PartyExportobj.UserCulture.ToString(), "Country").ToString());//9
                dt.Columns.Add(CommonFunctionalities.GetResourceString(PartyExportobj.UserCulture.ToString(), "State").ToString());//10
                dt.Columns.Add(CommonFunctionalities.GetResourceString(PartyExportobj.UserCulture.ToString(), "PartyType").ToString());//11
                                                                                                                                       //dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "PartsCreditLimit").ToString());//12
                dt.Columns.Add(CommonFunctionalities.GetResourceString(PartyExportobj.UserCulture.ToString(), "CreditLimitinCAD").ToString());//13
                dt.Columns.Add(CommonFunctionalities.GetResourceString(PartyExportobj.UserCulture.ToString(), "CreditLimitinUSD").ToString());//14
                                                                                                                                              // dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "PaymentDueDays").ToString());//15
                dt.Columns.Add(CommonFunctionalities.GetResourceString(PartyExportobj.UserCulture.ToString(), "isForegin").ToString());//16
                                                                                                                                       //dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "IsOEM").ToString());//17
                                                                                                                                       //dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "IsDealer").ToString());//18
                dt.Columns.Add(CommonFunctionalities.GetResourceString(PartyExportobj.UserCulture.ToString(), "Company").ToString());//19
                dt.Columns.Add(CommonFunctionalities.GetResourceString(PartyExportobj.UserCulture.ToString(), "IsLock").ToString());//20
                                                                                                                                    //dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "Branch").ToString());//21
                                                                                                                                    //dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "CustomerType").ToString());//22
                                                                                                                                    //dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "PaymentTerms").ToString());//22

                DataTable dt1 = new DataTable();
                dt1.Columns.Add("code");
                dt1.Columns.Add("Party");
                dt1.Columns.Add("Address");
                dt1.Columns.Add("Location");
                dt1.Columns.Add("Phone");
                dt1.Columns.Add("Mobile");
                dt1.Columns.Add("Is Active");
                //triveni
                dt1.Columns.Add("Email");
                dt1.Columns.Add("Fax");
                dt1.Columns.Add("Currency");
                dt1.Columns.Add("Country");
                dt1.Columns.Add("State");
                dt1.Columns.Add("PartyType");
                //dt1.Columns.Add("PartsCreditLimit");
                dt1.Columns.Add("CreditLimitinCAD");
                dt1.Columns.Add("CreditLimitinUSD");
                //dt1.Columns.Add("PaymentDueDays");
                dt1.Columns.Add("isForegin");
                //dt1.Columns.Add("IsOEM");
                //dt1.Columns.Add("IsDealer");
                dt1.Columns.Add("Company");
                dt1.Columns.Add("IsLock");
                //dt1.Columns.Add("Branch");
                //dt1.Columns.Add("CustomerType");
                //dt1.Columns.Add("PaymentTerms");
                //end
                dt1.Rows.Add(0, 0, 0, 0, 2, 2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);

                IQParty = GetPartyMasterExport(connString, PartyExportobj.Company_ID, PartyExportobj.rows, PartyExportobj.page, sidx, sord, filters, advnceFilters, partyType);
                IQParty = IQParty.AsQueryable().OrderByField(sidx, sord);

                if (!string.IsNullOrEmpty(filters) && filters != "null" && filters != "undefined")
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    if (filtersObj.rules.Count > 0)
                        IQParty = IQParty.FilterSearch(filtersObj);
                }

                // Apply advanced filters if present
                if (!string.IsNullOrEmpty(advnceFilters) && advnceFilters != "null")
                {
                    AdvanceFilter advnfilter = JObject.Parse(Common.DecryptString(advnceFilters)).ToObject<AdvanceFilter>();
                    IQParty = IQParty.AdvanceSearch(advnfilter);
                }
                iPurchaseOrderArray = IQParty.ToList();

                int count = iPurchaseOrderArray.Count();
                for (int i = 0; i < count; i++)
                {
                    //dt.Rows.Add(PartyArraryArray.ElementAt(i).Party_Name, PartyArraryArray.ElementAt(i).Party_Location, PartyArraryArray.ElementAt(i).Party_Phone, PartyArraryArray.ElementAt(i).Party_Mobile, (PartyArraryArray.ElementAt(i).Party_IsActive == true ? "Yes" : "No"), PartyArraryArray.ElementAt(i).Party_Email, PartyArraryArray.ElementAt(i).Party_Fax, PartyArraryArray.ElementAt(i).Currency, PartyArraryArray.ElementAt(i).Country, PartyArraryArray.ElementAt(i).State, PartyArraryArray.ElementAt(i).PartyTypestring, PartyArraryArray.ElementAt(i).PartsCreditLimit, PartyArraryArray.ElementAt(i).ServiceCreditLimit, PartyArraryArray.ElementAt(i).SalesCreditLimit, PartyArraryArray.ElementAt(i).PaymentDueDays, PartyArraryArray.ElementAt(i).Isforeign, PartyArraryArray.ElementAt(i).IsDealerString, PartyArraryArray.ElementAt(i).Company, PartyArraryArray.ElementAt(i).IslockString, "", PartyArraryArray.ElementAt(i).Party_PaymentTerms);                         
                    dt.Rows.Add(iPurchaseOrderArray.ElementAt(i).Party_Code, iPurchaseOrderArray.ElementAt(i).Party_Name, iPurchaseOrderArray.ElementAt(i).Party_Location, iPurchaseOrderArray.ElementAt(i).PartyAddress_Address, iPurchaseOrderArray.ElementAt(i).Party_Phone, iPurchaseOrderArray.ElementAt(i).Party_Mobile, iPurchaseOrderArray.ElementAt(i).Party_IsActive == true ? "Yes" : "No", iPurchaseOrderArray.ElementAt(i).Party_Email, iPurchaseOrderArray.ElementAt(i).Party_Fax, iPurchaseOrderArray.ElementAt(i).Currency, (iPurchaseOrderArray.ElementAt(i).Country), (iPurchaseOrderArray.ElementAt(i).State), (iPurchaseOrderArray.ElementAt(i).PartyType == 1 ? "Customer" : iPurchaseOrderArray.ElementAt(i).PartyType == 2 ? "Prospect" : iPurchaseOrderArray.ElementAt(i).PartyType == 3 ? "Partner" : iPurchaseOrderArray.ElementAt(i).PartyType == 4 ? "Supplier" : iPurchaseOrderArray.ElementAt(i).PartyType == 5 ? "ClearingAgent" : iPurchaseOrderArray.ElementAt(i).PartyType == 6 ? "Transporter" : iPurchaseOrderArray.ElementAt(i).PartyType == 7 ? "Insurance" : iPurchaseOrderArray.ElementAt(i).PartyType == 8 ? "Financier" : iPurchaseOrderArray.ElementAt(i).PartyType == 10 ? "Vendor" : iPurchaseOrderArray.ElementAt(i).PartyType == 11 ? "Contractor" : "User"), iPurchaseOrderArray.ElementAt(i).ServiceCreditLimit, iPurchaseOrderArray.ElementAt(i).SalesCreditLimit, iPurchaseOrderArray.ElementAt(i).IsImportExport == true ? "Yes" : "No", iPurchaseOrderArray.ElementAt(i).Company, iPurchaseOrderArray.ElementAt(i).Party_IsLocked == true ? "Yes" : "No");//iPurchaseOrderArray.ElementAt(i).IsOEM == true ? "Yes" : "No", iPurchaseOrderArray.ElementAt(i).IsDealer == true ? "Yes" : "No",iPurchaseOrderArray.ElementAt(i).PaymentDueDays,iPurchaseOrderArray.ElementAt(i).CustomerType == 0 ? "External" :iPurchaseOrderArray.ElementAt(i).CustomerType==1? "Internal":"",iPurchaseOrderArray.ElementAt(i).Party_PaymentTerms
                }
                DataTable DtCriteria = new DataTable();
                ExportList reportExportList = new ExportList
                {
                    Company_ID = PartyExportobj.Company_ID, // Assuming this is available in ExportObj
                    Branch = PartyExportobj.Branch,
                    dt1 = dt1,
                    DtCriteria = DtCriteria,

                    dt = dt,

                    FileName = "Party", // Set a default or dynamic filename
                    Header = CommonFunctionalities.GetResourceString(PartyExportobj.UserCulture.ToString(), "Party").ToString(), // Set a default or dynamic header
                    exprtType = PartyExportobj.exprtType, // Assuming export type as 1 for Excel, adjust as needed
                    UserCulture = PartyExportobj.UserCulture
                };
                var result = await DocumentExport.Export(reportExportList, connString, LogException);
                return result.Value;

                // ReportExport.Export(exprtType, dt, DtCriteria, dt1, Partytype, Partytype);
                //   gbl.InsertGPSDetails(Convert.ToInt32(PartyExportobj.Company_ID.ToString()), BranchID, PartyExportobj.User_ID, Common.GetObjectID("CorePartyMaster"), 0, 0, 0, "Party-Export", false, Convert.ToInt32(PartyExportobj.MenuID), Convert.ToDateTime(PartyExportobj.LoggedINDateTime));
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                // RedirectToAction("Error");
            }
            return null;
        }


        public static IQueryable<partydetails> GetPartyMasterExport(string connString, int Company_ID, int rows, int page, string sidx, string sord, string Searchstring, string AdvanceSearchstring, int partyType)
        {
            List<partydetails> partyList = new List<partydetails>();
            using (SqlConnection connection = new SqlConnection(connString))
            {
                SqlCommand command = new SqlCommand("UP_Get_AM_ERP_GetPartyMaster", connection);
                command.CommandType = CommandType.StoredProcedure;
                command.Parameters.AddWithValue("@partyType", partyType);
                command.Parameters.AddWithValue("@CompanyID", Company_ID);
                command.Parameters.AddWithValue("@page", page);     // Page number
                command.Parameters.AddWithValue("@rows", rows);
                connection.Open();
                SqlDataReader reader = command.ExecuteReader();

                while (reader.Read())
                {
                    partydetails party = new partydetails();

                    // Non-nullable fields with default values if null
                    party.Party_ID = !reader.IsDBNull(reader.GetOrdinal("Party_ID")) ? reader.GetInt32(reader.GetOrdinal("Party_ID")) : 0;
                    party.Party_IsActive = !reader.IsDBNull(reader.GetOrdinal("Party_IsActive")) ? reader.GetBoolean(reader.GetOrdinal("Party_IsActive")) : false;
                    party.Party_IsLocked = !reader.IsDBNull(reader.GetOrdinal("Party_IsLocked")) ? reader.GetBoolean(reader.GetOrdinal("Party_IsLocked")) : false;

                    // Nullable fields
                    party.Party_Code = !reader.IsDBNull(reader.GetOrdinal("Party_Code")) ? reader.GetString(reader.GetOrdinal("Party_Code")) : string.Empty;
                    party.Party_Name = !reader.IsDBNull(reader.GetOrdinal("Party_Name")) ? reader.GetString(reader.GetOrdinal("Party_Name")) : string.Empty;
                    party.Party_Location = !reader.IsDBNull(reader.GetOrdinal("Party_Location")) ? reader.GetString(reader.GetOrdinal("Party_Location")) : string.Empty;
                    party.Party_Email = !reader.IsDBNull(reader.GetOrdinal("Party_Email")) ? reader.GetString(reader.GetOrdinal("Party_Email")) : string.Empty;
                    party.Party_Phone = !reader.IsDBNull(reader.GetOrdinal("Party_Phone")) ? reader.GetString(reader.GetOrdinal("Party_Phone")) : string.Empty;
                    party.Party_Fax = !reader.IsDBNull(reader.GetOrdinal("Party_Fax")) ? reader.GetString(reader.GetOrdinal("Party_Fax")) : string.Empty;
                    party.PartyAddress_Address = !reader.IsDBNull(reader.GetOrdinal("PartyAddress_Address")) ? reader.GetString(reader.GetOrdinal("PartyAddress_Address")) : string.Empty;
                    party.Party_PaymentTerms = !reader.IsDBNull(reader.GetOrdinal("Party_PaymentTerms")) ? reader.GetString(reader.GetOrdinal("Party_PaymentTerms")) : string.Empty;

                    // Nullable integers
                    party.Country_ID = reader.IsDBNull(reader.GetOrdinal("Country_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Country_ID"));
                    party.State_ID = reader.IsDBNull(reader.GetOrdinal("State_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("State_ID"));
                    party.Company_ID = reader.IsDBNull(reader.GetOrdinal("Company_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Company_ID"));

                    // Nullable booleans
                    party.IsOEM = reader.IsDBNull(reader.GetOrdinal("IsOEM")) ? false : reader.GetBoolean(reader.GetOrdinal("IsOEM"));
                    party.IsDealer = reader.IsDBNull(reader.GetOrdinal("IsDealer")) ? false : reader.GetBoolean(reader.GetOrdinal("IsDealer"));

                    // Nullable decimals with default values for non-nullable fields
                    party.PartsCreditLimit = reader.IsDBNull(reader.GetOrdinal("PartsCreditLimit")) ? 0m : reader.GetDecimal(reader.GetOrdinal("PartsCreditLimit"));
                    party.SalesCreditLimit = reader.IsDBNull(reader.GetOrdinal("SalesCreditLimit")) ? 0m : reader.GetDecimal(reader.GetOrdinal("SalesCreditLimit"));
                    party.ServiceCreditLimit = reader.IsDBNull(reader.GetOrdinal("ServiceCreditLimit")) ? 0m : reader.GetDecimal(reader.GetOrdinal("ServiceCreditLimit"));
                    party.ServiceCreditLimitinUSD = reader.IsDBNull(reader.GetOrdinal("ServiceCreditLimitinUSD")) ? 0m : reader.GetDecimal(reader.GetOrdinal("ServiceCreditLimitinUSD"));

                    // Nullable strings with default empty values
                    party.Currency = !reader.IsDBNull(reader.GetOrdinal("Currency")) ? reader.GetString(reader.GetOrdinal("Currency")) : string.Empty;
                    party.Country = !reader.IsDBNull(reader.GetOrdinal("Country")) ? reader.GetString(reader.GetOrdinal("Country")) : string.Empty;
                    party.State = !reader.IsDBNull(reader.GetOrdinal("State")) ? reader.GetString(reader.GetOrdinal("State")) : string.Empty;
                    party.PartyTypestring = !reader.IsDBNull(reader.GetOrdinal("PartyTypestring")) ? reader.GetString(reader.GetOrdinal("PartyTypestring")) : string.Empty;
                    party.Company = !reader.IsDBNull(reader.GetOrdinal("Company")) ? reader.GetString(reader.GetOrdinal("Company")) : string.Empty;

                    // Add the populated object to the list
                    partyList.Add(party);
                }
                reader.Close();
            }

            return partyList.AsQueryable<partydetails>();
        }
        #endregion


        #region ::: To Load Branch Uday Kumar J B 21-08-2024:::
        /// <summary>
        /// To Load Branch
        /// </summary>
        public static IEnumerable<GNM_Branch> LoadBranch(string connString, bool Active, int CompanyID)
        {
            List<GNM_Branch> branches = new List<GNM_Branch>();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand("Up_Get_Branches", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@CompanyID", CompanyID);
                        command.Parameters.AddWithValue("@Active", Active);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                GNM_Branch branch = new GNM_Branch
                                {
                                    Branch_ID = reader.GetInt32(reader.GetOrdinal("Branch_ID")),
                                    Branch_Name = reader.GetString(reader.GetOrdinal("Branch_Name")),
                                    Branch_Active = reader.GetBoolean(reader.GetOrdinal("Branch_Active"))
                                };

                                branches.Add(branch);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return branches;
        }
        #endregion


        #region :::SelectMasters   Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// To Get SelectMasters 
        /// </summary>
        /// 
        public static IActionResult SelectMasters(string connString, SelectMastersCorePartyMasterList SelectMastersCorePartyMasterobj)
        {
            var jsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int CompanyID = Convert.ToInt32(SelectMastersCorePartyMasterobj.Company_ID);
                List<GNM_RefMasterDetail> deptResults = new List<GNM_RefMasterDetail>();
                List<GNM_RefMasterDetail> prmSegResults = new List<GNM_RefMasterDetail>();
                List<GNM_RefMasterDetail> currResults = new List<GNM_RefMasterDetail>();
                List<GNM_RefMasterDetail> langResults = new List<GNM_RefMasterDetail>();
                List<GNM_SecondarySegment> secSegResults = new List<GNM_SecondarySegment>();
                List<ParentCompanyObject> parentCompanyResults = new List<ParentCompanyObject>();
                List<GNM_RefMasterDetail> skillsResults = new List<GNM_RefMasterDetail>();
                List<GNM_RefMasterDetail> brandResults = new List<GNM_RefMasterDetail>();
                List<GNM_ProductType> productTypeResults = new List<GNM_ProductType>();
                List<GNM_Model> modelResults = new List<GNM_Model>();
                List<GNM_ServiceType> GNM_ServiceTypeResults = new List<GNM_ServiceType>();
                IEnumerable<GNM_RefMasterDetail> Dept = null;
                IEnumerable<GNM_RefMasterDetail> prmSeg = null;
                IEnumerable<GNM_RefMasterDetail> curr = null;
                IEnumerable<GNM_RefMasterDetail> lang = null;
                IEnumerable<GNM_SecondarySegment> SecSeg = null;
                IEnumerable<ParentCompanyObject> ParentCompanyDetails = null;
                IEnumerable<GNM_RefMasterDetail> gnmMstrDtl = null;

                IEnumerable<GNM_RefMasterDetail> brand = null;

                IEnumerable<GNM_ProductType> prodType = null;

                IEnumerable<GNM_Model> model = null;
                int prtyID = Convert.ToInt32(SelectMastersCorePartyMasterobj.value);
                string Select = "-1:--" + CommonFunctionalities.GetResourceString(SelectMastersCorePartyMasterobj.GeneralCulture.ToString(), "Select").ToString() + "--;";
                string JobCard = Select;

                JobCard = JobCard.TrimEnd(new char[] { ';' });
                IEnumerable<GNM_Branch> brnch = LoadBranch(connString, true, CompanyID);

                IEnumerable<GNM_ServiceType> gnmSrvType = null;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_SELECT_AM_ERP_SelectMastersParty";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@CompanyID", CompanyID);




                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                // Read @TempDeptResults
                                while (reader.Read())
                                {
                                    GNM_RefMasterDetail deptData = new GNM_RefMasterDetail
                                    {
                                        RefMasterDetail_ID = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                        RefMasterDetail_Name = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_Name")) ? null : reader.GetString(reader.GetOrdinal("RefMasterDetail_Name")),
                                        IsCompanySpecific = reader.IsDBNull(reader.GetOrdinal("IsCompanySpecific")) ? false : reader.GetBoolean(reader.GetOrdinal("IsCompanySpecific")),
                                        Company_ID = reader.IsDBNull(reader.GetOrdinal("Company_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Company_ID"))
                                    };
                                    deptResults.Add(deptData);
                                }
                                Dept = deptResults.AsEnumerable();


                                // Move to the next result set
                                reader.NextResult();

                                // Read @TempPrmSegResults
                                while (reader.Read())
                                {
                                    GNM_RefMasterDetail prmSegData = new GNM_RefMasterDetail
                                    {
                                        RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                        RefMasterDetail_Name = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_Name")) ? null : reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"))
                                    };
                                    prmSegResults.Add(prmSegData);
                                }
                                prmSeg = prmSegResults.AsEnumerable();

                                // Move to the next result set
                                reader.NextResult();

                                // Read @TempCurrResults
                                while (reader.Read())
                                {
                                    GNM_RefMasterDetail currData = new GNM_RefMasterDetail
                                    {
                                        RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                        RefMasterDetail_Name = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_Name")) ? null : reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"))
                                    };
                                    currResults.Add(currData);
                                }
                                curr = currResults.AsEnumerable();

                                // Move to the next result set
                                reader.NextResult();

                                // Read @TempLangResults
                                while (reader.Read())
                                {
                                    GNM_RefMasterDetail langData = new GNM_RefMasterDetail
                                    {
                                        RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                        RefMasterDetail_Name = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_Name")) ? null : reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"))
                                    };
                                    langResults.Add(langData);
                                }
                                lang = langResults.AsEnumerable();

                                // Move to the next result set
                                reader.NextResult();

                                // Read @TempSecSegResults
                                while (reader.Read())
                                {
                                    GNM_SecondarySegment secSegData = new GNM_SecondarySegment
                                    {
                                        SecondarySegment_ID = reader.GetInt32(reader.GetOrdinal("SecondarySegment_ID")),
                                        SecondarySegment_Description = reader.IsDBNull(reader.GetOrdinal("SecondarySegment_Description")) ? null : reader.GetString(reader.GetOrdinal("SecondarySegment_Description"))
                                    };
                                    secSegResults.Add(secSegData);
                                }
                                SecSeg = secSegResults.AsEnumerable();

                                // Move to the next result set
                                reader.NextResult();

                                // Read @TempParentCompanyResults
                                while (reader.Read())
                                {
                                    ParentCompanyObject parentCompanyData = new ParentCompanyObject
                                    {
                                        Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                        Company_Name = reader.IsDBNull(reader.GetOrdinal("Company_Name")) ? null : reader.GetString(reader.GetOrdinal("Company_Name")),
                                        Company_Parent_ID = reader.GetInt32(reader.GetOrdinal("Company_Parent_ID"))
                                    };
                                    parentCompanyResults.Add(parentCompanyData);
                                }
                                ParentCompanyDetails = parentCompanyResults.AsEnumerable();

                                // Move to the next result set
                                reader.NextResult();

                                while (reader.Read())
                                {
                                    GNM_ServiceType GNM_ServiceTypeData = new GNM_ServiceType
                                    {

                                        ServiceType_Name = reader.IsDBNull(reader.GetOrdinal("ServiceType_Name")) ? null : reader.GetString(reader.GetOrdinal("ServiceType_Name"))
                                    };
                                    GNM_ServiceTypeResults.Add(GNM_ServiceTypeData);
                                }
                                gnmSrvType = GNM_ServiceTypeResults.AsEnumerable();
                                reader.NextResult();

                                // Read @TempSkillsResults
                                while (reader.Read())
                                {
                                    GNM_RefMasterDetail skillsData = new GNM_RefMasterDetail
                                    {

                                        RefMasterDetail_Name = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_Name")) ? null : reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"))
                                    };
                                    skillsResults.Add(skillsData);
                                }
                                gnmMstrDtl = skillsResults.AsEnumerable();

                                // Move to the next result set
                                reader.NextResult();

                                // Read @TempBrandResults
                                while (reader.Read())
                                {
                                    GNM_RefMasterDetail brandData = new GNM_RefMasterDetail
                                    {
                                        RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                        RefMasterDetail_Name = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_Name")) ? null : reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"))
                                    };
                                    brandResults.Add(brandData);
                                }
                                brand = brandResults.AsEnumerable();

                                // Move to the next result set
                                reader.NextResult();

                                // Read @TempProductTypeResults
                                while (reader.Read())
                                {
                                    GNM_ProductType productTypeData = new GNM_ProductType
                                    {
                                        ProductType_ID = reader.GetInt32(reader.GetOrdinal("ProductType_ID")),
                                        ProductType_Name = reader.IsDBNull(reader.GetOrdinal("ProductType_Name")) ? null : reader.GetString(reader.GetOrdinal("ProductType_Name"))
                                    };
                                    productTypeResults.Add(productTypeData);
                                }
                                prodType = productTypeResults.AsEnumerable();

                                // Move to the next result set
                                reader.NextResult();

                                // Read @TempModelResults
                                while (reader.Read())
                                {
                                    GNM_Model modelData = new GNM_Model
                                    {
                                        Model_ID = reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                        Model_Name = reader.IsDBNull(reader.GetOrdinal("Model_Name")) ? null : reader.GetString(reader.GetOrdinal("Model_Name"))
                                    };
                                    modelResults.Add(modelData);
                                }
                                model = modelResults.AsEnumerable();



                            }



                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                string SkillSet = Select;
                for (int i = 0; i < gnmMstrDtl.Count(); i++)
                {
                    SkillSet = SkillSet + gnmMstrDtl.ElementAt(i).RefMasterDetail_ID + ":" + gnmMstrDtl.ElementAt(i).RefMasterDetail_Name + ";";
                }
                SkillSet = SkillSet.TrimEnd(new char[] { ';' });

                string Department = Select;
                for (int i = 0; i < Dept.Count(); i++)
                {
                    Department = Department + Dept.ElementAt(i).RefMasterDetail_ID + ":" + Dept.ElementAt(i).RefMasterDetail_Name + ";";
                }
                Department = Department.TrimEnd(new char[] { ';' });


                string ServiceType = Select;
                for (int i = 0; i < gnmSrvType.Count(); i++)
                {
                    ServiceType = ServiceType + gnmSrvType.ElementAt(i).ServiceType_ID + ":" + gnmSrvType.ElementAt(i).ServiceType_Name + ";";
                }
                ServiceType = ServiceType.TrimEnd(new char[] { ';' });

                string Brand = Select;
                for (int i = 0; i < brand.Count(); i++)
                {
                    Brand = Brand + brand.ElementAt(i).RefMasterDetail_ID + ":" + brand.ElementAt(i).RefMasterDetail_Name + ";";
                }
                Brand = Brand.TrimEnd(new char[] { ';' });

                string PrimarySegment = Select;
                for (int i = 0; i < prmSeg.Count(); i++)
                {
                    PrimarySegment = PrimarySegment + prmSeg.ElementAt(i).RefMasterDetail_ID + ":" + prmSeg.ElementAt(i).RefMasterDetail_Name + ";";
                }
                PrimarySegment = PrimarySegment.TrimEnd(new char[] { ';' });

                string SecondarySegment = Select;
                for (int i = 0; i < SecSeg.Count(); i++)
                {
                    SecondarySegment = SecondarySegment + SecSeg.ElementAt(i).SecondarySegment_ID + ":" + SecSeg.ElementAt(i).SecondarySegment_Description + ";";
                }
                SecondarySegment = SecondarySegment.TrimEnd(new char[] { ';' });

                string Branch = Select;
                for (int i = 0; i < brnch.Count(); i++)
                {
                    Branch = Branch + brnch.ElementAt(i).Branch_ID + ":" + brnch.ElementAt(i).Branch_Name + ";";
                }
                Branch = Branch.TrimEnd(new char[] { ';' });

                string ProductType = Select;
                for (int i = 0; i < prodType.Count(); i++)
                {
                    ProductType = ProductType + prodType.ElementAt(i).ProductType_ID + ":" + prodType.ElementAt(i).ProductType_Name + ";";
                }
                ProductType = ProductType.TrimEnd(new char[] { ';' });

                string Currency = Select;
                for (int i = 0; i < curr.Count(); i++)
                {
                    Currency = Currency + curr.ElementAt(i).RefMasterDetail_ID + ":" + curr.ElementAt(i).RefMasterDetail_Name + ";";
                }
                Currency = Currency.TrimEnd(new char[] { ';' });

                string Language = Select;
                for (int i = 0; i < lang.Count(); i++)
                {
                    Language = Language + lang.ElementAt(i).RefMasterDetail_ID + ":" + lang.ElementAt(i).RefMasterDetail_Name + ";";
                }
                Language = Language.TrimEnd(new char[] { ';' });

                string Model = Select;
                for (int i = 0; i < model.Count(); i++)
                {
                    Model = Model + model.ElementAt(i).Model_ID + ":" + model.ElementAt(i).Model_Name + ";";
                }
                Model = Model.TrimEnd(new char[] { ';' });

                jsonData = new
                {
                    Brand = Brand,
                    Model = Model,
                    PrimarySegment = PrimarySegment,
                    SecondarySegment = SecondarySegment,
                    ProductType = ProductType,
                    SkillSet = SkillSet,
                    ServiceType = ServiceType,
                    Branch = Branch,
                    JobCard = JobCard,
                    Department = Department,
                    Currency = Currency,
                    Language = Language
                };
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    //
                }
                // return RedirectToAction("Error");
            }


            return new JsonResult(jsonData);
        }
        #endregion


        #region :::SelectMastersLocale   Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// To Get SelectMastersLocale 
        /// </summary>
        /// 

        public static IEnumerable<GNM_BranchLocale> LoadBranchLocaleParty(string connString, int UserLanguageID, bool Active, int CompanyID)
        {

            int Language_ID = Convert.ToInt32(UserLanguageID);
            List<GNM_BranchLocale> branchLocales = new List<GNM_BranchLocale>();
            IEnumerable<GNM_BranchLocale> BranchLocale = null;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_Load_AM_ERP_LoadBranchLocalePartyParty";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@CompanyID", CompanyID);
                            command.Parameters.AddWithValue("@Language_ID", Language_ID);
                            command.Parameters.AddWithValue("@Active", Active);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    GNM_BranchLocale branchLocale = new GNM_BranchLocale();
                                    for (int i = 0; i < reader.FieldCount; i++)
                                    {
                                        string columnName = reader.GetName(i);
                                        PropertyInfo property = typeof(GNM_BranchLocale).GetProperty(columnName);

                                        if (property != null && !reader.IsDBNull(i))
                                        {
                                            object value = reader.GetValue(i);
                                            property.SetValue(branchLocale, value);
                                        }
                                    }
                                    branchLocales.Add(branchLocale);

                                }
                                BranchLocale = branchLocales.AsEnumerable();


                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return BranchLocale;
        }
        public static IActionResult SelectMastersLocale(string connString, SelectMastersLocaleCorePartyMasterList SelectMastersLocaleCorePartyMasterListobj)
        {
            var jsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                List<GNM_RefMasterDetailLocale> brandResults = new List<GNM_RefMasterDetailLocale>();
                List<GNM_RefMasterDetailLocale> deptResults = new List<GNM_RefMasterDetailLocale>();
                List<GNM_ModelLocale> modelResults = new List<GNM_ModelLocale>();
                List<GNM_RefMasterDetailLocale> currResults = new List<GNM_RefMasterDetailLocale>();
                List<GNM_RefMasterDetailLocale> prmSegResults = new List<GNM_RefMasterDetailLocale>();
                List<GNM_SecondarySegmentLocale> secSegResults = new List<GNM_SecondarySegmentLocale>();
                List<ParentCompanyObject> parentCompanyResults = new List<ParentCompanyObject>();
                List<GNM_ServiceType> gnmSrvTypeResults = new List<GNM_ServiceType>();
                List<GNM_ServiceType> GNM_ServiceTypeLocaleResults = new List<GNM_ServiceType>();
                List<GNM_RefMasterDetailLocale> gnmMstrDtlResults = new List<GNM_RefMasterDetailLocale>();
                List<GNM_ProductTypeLocale> productTypeResults = new List<GNM_ProductTypeLocale>();
                List<Core_JobCardHeader> fsmJobCardResults = new List<Core_JobCardHeader>();
                IEnumerable<GNM_RefMasterDetailLocale> brand = null;
                IEnumerable<GNM_RefMasterDetailLocale> DeptL = null;
                IEnumerable<GNM_ModelLocale> model = null;
                IEnumerable<GNM_RefMasterDetailLocale> curr = null;
                IEnumerable<GNM_RefMasterDetailLocale> prmSeg = null;
                IEnumerable<GNM_SecondarySegmentLocale> SecSeg = null;
                IEnumerable<GNM_ProductTypeLocale> prodType = null;
                IEnumerable<GNM_ServiceType> gnmSrvType = null;
                IEnumerable<GNM_RefMasterDetailLocale> gnmMstrDtl = null;
                IEnumerable<Core_JobCardHeader> fsmJobCard = null;
                int CompanyID = Convert.ToInt32(SelectMastersLocaleCorePartyMasterListobj.Company_ID);
                int Language_ID = Convert.ToInt32(SelectMastersLocaleCorePartyMasterListobj.LanguageID);
                int prtyID = Convert.ToInt32(SelectMastersLocaleCorePartyMasterListobj.value);
                IEnumerable<GNM_BranchLocale> brnch = LoadBranchLocaleParty(connString, SelectMastersLocaleCorePartyMasterListobj.UserLanguageID, true, CompanyID);
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_SELECT_AM_ERP_SelectMastersLocaleParty";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@CompanyID", CompanyID);
                            command.Parameters.AddWithValue("@Language_ID", Language_ID);
                            command.Parameters.AddWithValue("@PartyID", prtyID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    GNM_RefMasterDetailLocale brandData = new GNM_RefMasterDetailLocale
                                    {
                                        RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                        RefMasterDetail_Name = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_Name")) ? null : reader.GetString(reader.GetOrdinal("RefMasterDetail_Name")),

                                    };
                                    brandResults.Add(brandData);
                                }
                                brand = brandResults.AsEnumerable();
                                reader.NextResult();
                                while (reader.Read())
                                {
                                    GNM_RefMasterDetailLocale deptData = new GNM_RefMasterDetailLocale
                                    {
                                        RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                        RefMasterDetail_Name = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_Name")) ? null : reader.GetString(reader.GetOrdinal("RefMasterDetail_Name")),

                                    };
                                    deptResults.Add(deptData);
                                }
                                DeptL = deptResults.AsEnumerable();
                                reader.NextResult();
                                while (reader.Read())
                                {
                                    GNM_ModelLocale modelData = new GNM_ModelLocale
                                    {
                                        Model_ID = reader.GetInt32(reader.GetOrdinal("Model_ID")),
                                        Model_Name = reader.IsDBNull(reader.GetOrdinal("Model_Name")) ? null : reader.GetString(reader.GetOrdinal("Model_Name"))
                                    };
                                    modelResults.Add(modelData);
                                }
                                model = modelResults.AsEnumerable();
                                reader.NextResult();
                                while (reader.Read())
                                {
                                    GNM_RefMasterDetailLocale currData = new GNM_RefMasterDetailLocale
                                    {
                                        RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                        RefMasterDetail_Name = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_Name")) ? null : reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"))
                                    };
                                    currResults.Add(currData);
                                }
                                curr = currResults.AsEnumerable();
                                reader.NextResult();
                                while (reader.Read())
                                {
                                    GNM_RefMasterDetailLocale prmSegData = new GNM_RefMasterDetailLocale
                                    {
                                        RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                        RefMasterDetail_Name = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_Name")) ? null : reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"))
                                    };
                                    prmSegResults.Add(prmSegData);
                                }
                                prmSeg = prmSegResults.AsEnumerable();
                                reader.NextResult();
                                while (reader.Read())
                                {
                                    GNM_SecondarySegmentLocale secSegData = new GNM_SecondarySegmentLocale
                                    {
                                        SecondarySegment_ID = reader.GetInt32(reader.GetOrdinal("SecondarySegment_ID")),
                                        SecondarySegment_Description = reader.IsDBNull(reader.GetOrdinal("SecondarySegment_Description")) ? null : reader.GetString(reader.GetOrdinal("SecondarySegment_Description"))
                                    };
                                    secSegResults.Add(secSegData);
                                }
                                SecSeg = secSegResults.AsEnumerable();
                                reader.NextResult();
                                while (reader.Read())
                                {
                                    GNM_ProductTypeLocale productTypeData = new GNM_ProductTypeLocale
                                    {
                                        ProductType_ID = reader.GetInt32(reader.GetOrdinal("ProductType_ID")),
                                        ProductType_Name = reader.IsDBNull(reader.GetOrdinal("ProductType_Name")) ? null : reader.GetString(reader.GetOrdinal("ProductType_Name"))
                                    };
                                    productTypeResults.Add(productTypeData);
                                }
                                prodType = productTypeResults.AsEnumerable();
                                reader.NextResult();
                                while (reader.Read())
                                {
                                    GNM_ServiceType gnmSrvTypeData = new GNM_ServiceType
                                    {
                                        ServiceType_ID = reader.GetInt32(reader.GetOrdinal("ServiceType_ID")),

                                    };
                                    gnmSrvTypeResults.Add(gnmSrvTypeData);
                                }
                                gnmSrvType = gnmSrvTypeResults.AsEnumerable();
                                reader.NextResult();
                                while (reader.Read())
                                {
                                    GNM_RefMasterDetailLocale gnmMstrDtlData = new GNM_RefMasterDetailLocale
                                    {
                                        RefMasterDetail_ID = reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                        RefMasterDetail_Name = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_Name")) ? null : reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"))
                                    };
                                    gnmMstrDtlResults.Add(gnmMstrDtlData);
                                }
                                gnmMstrDtl = gnmMstrDtlResults.AsEnumerable();
                                reader.NextResult();
                                while (reader.Read())
                                {
                                    Core_JobCardHeader fsmJobCardData = new Core_JobCardHeader
                                    {
                                        JobCard_ID = reader.GetInt32(reader.GetOrdinal("JobCard_ID")),
                                        JobCardNumber = reader.IsDBNull(reader.GetOrdinal("JobCardNumber")) ? null : reader.GetString(reader.GetOrdinal("JobCardNumber"))
                                    };
                                    fsmJobCardResults.Add(fsmJobCardData);
                                }
                                fsmJobCard = fsmJobCardResults.AsEnumerable();
                                reader.NextResult();
                                while (reader.Read())
                                {
                                    GNM_ServiceType GNM_ServiceTypeLocaleData = new GNM_ServiceType
                                    {
                                        ServiceType_ID = reader.GetInt32(reader.GetOrdinal("ServiceType_ID")),

                                    };
                                    GNM_ServiceTypeLocaleResults.Add(GNM_ServiceTypeLocaleData);
                                }

                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                var UserCulture = default(dynamic);
                string Select = "-1:--" + CommonFunctionalities.GetGlobalResourceObject(SelectMastersLocaleCorePartyMasterListobj.UserCulture.ToString(), "Select").ToString() + "--;";
                string JobCard = Select;
                for (int i = 0; i < fsmJobCard.Count(); i++)
                {
                    JobCard = JobCard + fsmJobCard.ElementAt(i).JobCard_ID + ":" + fsmJobCard.ElementAt(i).JobCardNumber + ";";
                }
                JobCard = JobCard.TrimEnd(new char[] { ';' });

                string Department = Select;
                for (int i = 0; i < DeptL.Count(); i++)
                {
                    Department = Department + DeptL.ElementAt(i).RefMasterDetail_ID + ":" + DeptL.ElementAt(i).RefMasterDetail_Name + ";";
                }
                Department = Department.TrimEnd(new char[] { ';' });

                string SkillSet = Select;
                for (int i = 0; i < gnmMstrDtl.Count(); i++)
                {
                    SkillSet = SkillSet + gnmMstrDtl.ElementAt(i).RefMasterDetail_ID + ":" + gnmMstrDtl.ElementAt(i).RefMasterDetail_Name + ";";
                }
                SkillSet = SkillSet.TrimEnd(new char[] { ';' });

                string ServiceType = Select;
                foreach (var serviceType in gnmSrvType)
                {
                    ServiceType += serviceType.ServiceType_ID + ":" + (serviceType.ServiceType_ID == null ? "" : serviceType.ServiceType_ID.ToString()) + ";";
                }
                ServiceType = ServiceType.TrimEnd(new char[] { ';' });

                string Brand = Select;
                for (int i = 0; i < brand.Count(); i++)
                {
                    Brand = Brand + brand.ElementAt(i).RefMasterDetail_ID + ":" + brand.ElementAt(i).RefMasterDetail_Name + ";";
                }
                Brand = Brand.TrimEnd(new char[] { ';' });

                string Model = Select;
                for (int i = 0; i < model.Count(); i++)
                {
                    Model = Model + model.ElementAt(i).Model_ID + ":" + model.ElementAt(i).Model_Name + ";";
                }

                Model = Model.TrimEnd(new char[] { ';' });

                string PrimarySegment = Select;
                for (int i = 0; i < prmSeg.Count(); i++)
                {
                    PrimarySegment = PrimarySegment + prmSeg.ElementAt(i).RefMasterDetail_ID + ":" + prmSeg.ElementAt(i).RefMasterDetail_Name + ";";
                }
                PrimarySegment = PrimarySegment.TrimEnd(new char[] { ';' });

                string SecondarySegment = Select;
                for (int i = 0; i < SecSeg.Count(); i++)
                {
                    SecondarySegment = SecondarySegment + SecSeg.ElementAt(i).SecondarySegment_ID + ":" + SecSeg.ElementAt(i).SecondarySegment_Description + ";";
                }

                SecondarySegment = SecondarySegment.TrimEnd(new char[] { ';' });
                string Branch = Select;
                for (int i = 0; i < brnch.Count(); i++)
                {
                    Branch = Branch + brnch.ElementAt(i).Branch_ID + ":" + brnch.ElementAt(i).Branch_Name + ";";
                }

                Branch = Branch.TrimEnd(new char[] { ';' });
                string ProductType = Select;
                for (int i = 0; i < prodType.Count(); i++)
                {
                    ProductType = ProductType + prodType.ElementAt(i).ProductType_ID + ":" + prodType.ElementAt(i).ProductType_Name + ";";
                }

                ProductType = ProductType.TrimEnd(new char[] { ';' });

                string Currency = Select;
                for (int i = 0; i < curr.Count(); i++)
                {
                    Currency = Currency + curr.ElementAt(i).RefMasterDetail_ID + ":" + curr.ElementAt(i).RefMasterDetail_Name + ";";
                }
                Currency = Currency.TrimEnd(new char[] { ';' });

                jsonData = new
                {
                    Brand = Brand,
                    Model = Model,
                    PrimarySegment = PrimarySegment,
                    SecondarySegment = SecondarySegment,
                    ProductType = ProductType,
                    SkillSet = SkillSet,
                    ServiceType = ServiceType,
                    Branch = Branch,
                    JobCard = JobCard,
                    Department = Department,
                    Currency = Currency
                };


            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                // return RedirectToAction("Error");
            }

            return new JsonResult(jsonData);
        }
        #endregion


        #region :::SelectProductType   Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// To Get SelectProductType 
        /// </summary>
        /// 
        public static IActionResult SelectProductType(string connString, SelectProductTypeLista SelectProductTypeobj)
        {
            var jsonData = default(dynamic);
            List<QuestionnaireLevelData> masterDataList = new List<QuestionnaireLevelData>();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_SELECT_AM_ERP_SelectProductTypeParty";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@BrandID", SelectProductTypeobj.id);
                            command.Parameters.AddWithValue("@GeneralLanguageCode", SelectProductTypeobj.GeneralLanguageCode);
                            command.Parameters.AddWithValue("@UserLanguageCode", SelectProductTypeobj.UserLanguageCode);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var Data = new QuestionnaireLevelData
                                    {
                                        ID = reader.GetInt32(reader.GetOrdinal("ID")),
                                        Name = reader.IsDBNull(reader.GetOrdinal("Name")) ? null : reader.GetString(reader.GetOrdinal("Name"))
                                    };
                                    masterDataList.Add(Data);
                                }


                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                jsonData = new
                {
                    ProductType = masterDataList
                };
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion


        #region :::SelectModel   Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// To Get SelectModel 
        /// </summary>
        /// 
        public static IActionResult SelectModel(string connString, SelectModelTypeList SelectModelTypeobj)
        {
            var jsonData = new List<object>(); // Initialize with an empty list or null
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (var connection = new SqlConnection(connString))
                {
                    connection.Open();

                    SqlCommand command = new SqlCommand("UP_Sel_AM_ERP_SelectModels", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    // Assuming you have Session variables for GeneralLanguageCode and UserLanguageCode
                    command.Parameters.AddWithValue("@ProductTypeID", SelectModelTypeobj.id);
                    command.Parameters.AddWithValue("@GeneralLanguageCode", SelectModelTypeobj.GeneralLanguageCode);
                    command.Parameters.AddWithValue("@UserLanguageCode", SelectModelTypeobj.UserLanguageCode);

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            jsonData.Add(new
                            {
                                ID = reader["ID"],
                                Name = reader["Name"]
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }
        #endregion


        #region :::SelectSecondarySegment   Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// To Get SelectSecondarySegment 
        /// </summary>
        /// 
        public static IActionResult SelectSecondarySegment(string connString, SelectSecondarySegmentlist SelectSecondarySegmentobj, int prtyID, int segID)
        {
            var jsonData = default(dynamic);
            var masterDataList = new List<QuestionnaireLevelData>();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_SELECT_AM_ERP_SelectSecondarySegmentParty";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@PartyID", prtyID);
                            command.Parameters.AddWithValue("@SegmentID", segID);
                            command.Parameters.AddWithValue("@GeneralLanguageCode", SelectSecondarySegmentobj.GeneralLanguageCode);
                            command.Parameters.AddWithValue("@UserLanguageCode", SelectSecondarySegmentobj.UserLanguageCode);
                            command.Parameters.AddWithValue("@PrimarySegment_ID", SelectSecondarySegmentobj.id);
                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var Data = new QuestionnaireLevelData
                                    {
                                        ID = reader.GetInt32(reader.GetOrdinal("ID")),
                                        Name = reader.IsDBNull(reader.GetOrdinal("Name")) ? null : reader.GetString(reader.GetOrdinal("Name"))
                                    };
                                    masterDataList.Add(Data);
                                }


                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                jsonData = new
                {
                    SecondarySegmnet = masterDataList
                };
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    // //
                }
                //  return RedirectToAction("Error");
            }

            return new JsonResult(jsonData);
        }
        #endregion


        #region :::SelectPartyMasterLocale   Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// To Get SelectPartyMasterLocale 
        /// </summary>
        /// 
        public static IActionResult SelectPartyMasterLocale(string connString, SelectPartyMasterLocaleList SelectPartyMasterLocaleobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            var jsonData = new object();
            SqlConnection conn = null;
            SqlCommand cmd = null;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int count = 0;
                int total = 0;
                string YesL = CommonFunctionalities.GetGlobalResourceObject(SelectPartyMasterLocaleobj.UserCulture.ToString(), "Yes").ToString();
                string NoL = CommonFunctionalities.GetGlobalResourceObject(SelectPartyMasterLocaleobj.UserCulture.ToString(), "No").ToString();
                int CompanyID = Convert.ToInt32(SelectPartyMasterLocaleobj.Company_ID);
                int partyType = Convert.ToInt32(SelectPartyMasterLocaleobj.partyType);
                int LangID = Convert.ToInt32(SelectPartyMasterLocaleobj.UserLanguageID);

                conn = new SqlConnection(connString);

                cmd = new SqlCommand("UP_Sel_AM_ERP_GetPartyMasterLocales", conn)
                {
                    CommandType = CommandType.StoredProcedure
                };
                cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                cmd.Parameters.AddWithValue("@PartyType", partyType);
                cmd.Parameters.AddWithValue("@LangID", LangID);

                conn.Open();

                // Use List to collect data
                List<GNM_PartyLocale> partyLocalesList = new List<GNM_PartyLocale>();
                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        partyLocalesList.Add(new GNM_PartyLocale
                        {
                            Party_Locale_ID = reader.GetInt32(reader.GetOrdinal("Party_Locale_ID")),
                            Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID")),
                            Party_Name = reader.GetString(reader.GetOrdinal("Party_Name")),
                            Party_Location = reader.GetString(reader.GetOrdinal("Party_Location")),
                            Party_PaymentTerms = reader.GetString(reader.GetOrdinal("Party_PaymentTerms")),
                            Language_ID = reader.GetInt32(reader.GetOrdinal("Language_ID")),
                            Party_Address = reader.GetString(reader.GetOrdinal("Party_Address")),
                            Party_Code = reader.GetString(reader.GetOrdinal("Party_Code")),
                        });
                    }
                }

                // Convert List to IQueryable
                IQueryable<GNM_PartyLocale> iQPrty = partyLocalesList.AsQueryable();

                if (_search)
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    iQPrty = iQPrty.FilterSearch<GNM_PartyLocale>(filtersObj);
                }
                else if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse(advnceFilters).ToObject<AdvanceFilter>();
                    iQPrty = iQPrty.AdvanceSearch<GNM_PartyLocale>(advnfilter);
                }

                // Sorting
                iQPrty = iQPrty.OrderByField<GNM_PartyLocale>(sidx, sord);

                count = iQPrty.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                jsonData = new
                {
                    total = total,
                    page = page,
                    records = count,
                    rows = iQPrty.Skip((page - 1) * rows).Take(rows).ToList()
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            finally
            {
                cmd.Dispose();
                conn.Close();
                conn.Dispose();
                SqlConnection.ClearAllPools();
            }

            return new JsonResult(jsonData);
        }

        #endregion


        #region :::SelectParticularParty   Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// To Get SelectParticularParty 
        /// </summary>
        /// 
        public static IActionResult SelectParticularParty(string connString, SelectParticularPartyList SelectParticularPartyobj)
        {
            var jsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {

                var PartyDetailsList = new List<GNM_Party>();
                var CountryDetailsList = new List<GNM_RefMasterDetail>();
                var StateDetailsList = new List<GNM_State>();
                var RefMasterList = new List<GNM_RefMaster>();
                var RefMasterDetailList = new List<GNM_RefMasterDetail>();
                var ContractorDetailsList = new List<GNM_Party>();
                var PartyLocaleList = new List<GNM_PartyLocale>();
                var PartyLocaleList2 = new List<GNM_Party>();
                var PartyLocaleList3 = new List<GNM_PartyLocale>();
                var CountryLocaleList = new List<GNM_RefMasterDetailLocale>();
                var StateLocaleList = new List<GNM_StateLocale>();
                var RefMasterDetailLocaleList = new List<GNM_RefMasterDetailLocale>();
                GNM_Party prty = null;
                IEnumerable<GNM_RefMasterDetail> cntry = null;
                IEnumerable<GNM_State> States = null;
                List<GNM_RefMaster> refMaster = null;
                List<GNM_RefMasterDetail> refDetail = null;
                GNM_PartyLocale gnmPrtyLocale = null;
                GNM_Party gNM_PartyLocale2 = null;
                GNM_PartyLocale gNM_PartyLocale3 = null;
                IEnumerable<GNM_RefMasterDetailLocale> cntryLocale = null;
                IEnumerable<GNM_StateLocale> StateLocale = null;
                List<GNM_RefMasterDetailLocale> refDetailLocale = null;
                var RefMasterDetail_Name = string.Empty;
                var RefMasterDetail_Name2 = string.Empty;
                int LangID = Convert.ToInt32(SelectParticularPartyobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectParticularPartyobj.GeneralLanguageID);
                int CompanyID = Convert.ToInt32(SelectParticularPartyobj.Company_ID);
                int BranchID = Convert.ToInt32(SelectParticularPartyobj.Branch);
                //GNM_User User = SelectParticularPartyobj.UserDetails.FirstOrDefault();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_SELECT_AM_ERP_SelectParticularPartyParty";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.Add(new SqlParameter("@Party_ID", SqlDbType.Int) { Value = SelectParticularPartyobj.id });
                            command.Parameters.Add(new SqlParameter("@BranchID", SqlDbType.Int) { Value = BranchID });
                            command.Parameters.Add(new SqlParameter("@LangID", SqlDbType.Int) { Value = LangID });
                            command.Parameters.Add(new SqlParameter("@generalLanguageID", SqlDbType.Int) { Value = generalLanguageID });
                            command.Parameters.Add(new SqlParameter("@CompanyID", SqlDbType.Int) { Value = CompanyID });
                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {

                                if (reader.HasRows)


                                    while (reader.Read())
                                    {
                                        GNM_Party party = new GNM_Party();

                                        party.Party_ID = reader.IsDBNull(reader.GetOrdinal("Party_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Party_ID"));
                                        party.Country_ID = reader.IsDBNull(reader.GetOrdinal("Country_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Country_ID"));
                                        party.State_ID = reader.IsDBNull(reader.GetOrdinal("State_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("State_ID"));
                                        party.PartyType = reader.IsDBNull(reader.GetOrdinal("PartyType")) ? (byte)0 : (byte)reader.GetByte(reader.GetOrdinal("PartyType"));
                                        party.Party_Code = reader.IsDBNull(reader.GetOrdinal("Party_Code")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Code"));
                                        party.Party_Phone = reader.IsDBNull(reader.GetOrdinal("Party_Phone")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Phone"));
                                        party.Party_Mobile = reader.IsDBNull(reader.GetOrdinal("Party_Mobile")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Mobile"));
                                        party.Party_Email = reader.IsDBNull(reader.GetOrdinal("Party_Email")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Email"));
                                        party.Party_Fax = reader.IsDBNull(reader.GetOrdinal("Party_Fax")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Fax"));
                                        party.IsOEM = reader.IsDBNull(reader.GetOrdinal("IsOEM")) ? false : reader.GetBoolean(reader.GetOrdinal("IsOEM"));
                                        party.IsDealer = reader.IsDBNull(reader.GetOrdinal("IsDealer")) ? false : reader.GetBoolean(reader.GetOrdinal("IsDealer"));
                                        party.PartsCreditLimit = reader.IsDBNull(reader.GetOrdinal("PartsCreditLimit")) ? 0 : reader.GetDecimal(reader.GetOrdinal("PartsCreditLimit"));
                                        party.SalesCreditLimit = reader.IsDBNull(reader.GetOrdinal("SalesCreditLimit")) ? 0 : reader.GetDecimal(reader.GetOrdinal("SalesCreditLimit"));
                                        party.PaymentDueDays = reader.IsDBNull(reader.GetOrdinal("PaymentDueDays")) ? 0 : reader.GetInt32(reader.GetOrdinal("PaymentDueDays"));
                                        party.Party_IsLocked = reader.IsDBNull(reader.GetOrdinal("Party_IsLocked")) ? false : reader.GetBoolean(reader.GetOrdinal("Party_IsLocked"));
                                        party.Region_ID = reader.IsDBNull(reader.GetOrdinal("Region_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Region_ID"));
                                        party.CustomerLanguageID = reader.IsDBNull(reader.GetOrdinal("CustomerLanguageID")) ? 0 : reader.GetInt32(reader.GetOrdinal("CustomerLanguageID"));
                                        party.IsImportExport = reader.IsDBNull(reader.GetOrdinal("IsImportExport")) ? false : reader.GetBoolean(reader.GetOrdinal("IsImportExport"));
                                        party.IsPONumberMandatory = reader.IsDBNull(reader.GetOrdinal("IsPONumberMandatory")) ? false : reader.GetBoolean(reader.GetOrdinal("IsPONumberMandatory"));
                                        party.Variance_Percentage = reader.IsDBNull(reader.GetOrdinal("Variance_Percentage")) ? 0 : reader.GetDecimal(reader.GetOrdinal("Variance_Percentage"));
                                        party.Variance_Value = reader.IsDBNull(reader.GetOrdinal("Variance_Value")) ? 0 : reader.GetDecimal(reader.GetOrdinal("Variance_Value"));
                                        party.ServiceCreditLimit = reader.IsDBNull(reader.GetOrdinal("ServiceCreditLimit")) ? 0 : reader.GetDecimal(reader.GetOrdinal("ServiceCreditLimit"));
                                        party.ServiceOutStandingCredit = reader.IsDBNull(reader.GetOrdinal("ServiceOutStandingCredit")) ? 0 : reader.GetDecimal(reader.GetOrdinal("ServiceOutStandingCredit"));
                                        party.ServiceCreditLimitinUSD = reader.IsDBNull(reader.GetOrdinal("ServiceCreditLimitinUSD")) ? 0 : reader.GetDecimal(reader.GetOrdinal("ServiceCreditLimitinUSD"));
                                        party.ServiceOutStandingCreditinUSD = reader.IsDBNull(reader.GetOrdinal("ServiceOutStandingCreditinUSD")) ? 0 : reader.GetDecimal(reader.GetOrdinal("ServiceOutStandingCreditinUSD"));
                                        party.Currency_ID = reader.IsDBNull(reader.GetOrdinal("Currency_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Currency_ID"));
                                        party.Party_IsActive = reader.IsDBNull(reader.GetOrdinal("Party_IsActive")) ? false : reader.GetBoolean(reader.GetOrdinal("Party_IsActive"));

                                        party.ModifiedDate = reader.IsDBNull(reader.GetOrdinal("ModifiedDate")) ? DateTime.MinValue : reader.GetDateTime(reader.GetOrdinal("ModifiedDate"));
                                        party.Relationship_Branch_ID = reader.IsDBNull(reader.GetOrdinal("Relationship_Branch_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Relationship_Branch_ID"));
                                        party.Relationship_Company_ID = reader.IsDBNull(reader.GetOrdinal("Relationship_Company_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Relationship_Company_ID"));
                                        party.PartsOutStandingCredit = reader.IsDBNull(reader.GetOrdinal("PartsOutStandingCredit")) ? 0 : reader.GetDecimal(reader.GetOrdinal("PartsOutStandingCredit"));

                                        party.PartyAdvanceAmount = reader.IsDBNull(reader.GetOrdinal("PartyAdvanceAmount")) ? 0 : reader.GetDecimal(reader.GetOrdinal("PartyAdvanceAmount"));
                                        party.SalesOutStandingCredit = reader.IsDBNull(reader.GetOrdinal("SalesOutStandingCredit")) ? 0 : reader.GetDecimal(reader.GetOrdinal("SalesOutStandingCredit"));
                                        party.CustomerType_ID = reader.IsDBNull(reader.GetOrdinal("CustomerType_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("CustomerType_ID"));
                                        party.IsKeyCustomer = reader.IsDBNull(reader.GetOrdinal("IsKeyCustomer")) ? false : reader.GetBoolean(reader.GetOrdinal("IsKeyCustomer"));
                                        party.SupplierHasInterface = reader.IsDBNull(reader.GetOrdinal("SupplierHasInterface")) ? false : reader.GetBoolean(reader.GetOrdinal("SupplierHasInterface"));
                                        party.CustomerType = reader.IsDBNull(reader.GetOrdinal("CustomerType")) ? (byte)0 : Convert.ToByte(reader.GetString(reader.GetOrdinal("CustomerType")));

                                        party.Party_Code = reader.IsDBNull(reader.GetOrdinal("Party_Code")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Code"));
                                        party.CAD_ExchangeRate = reader.IsDBNull(reader.GetOrdinal("CAD_ExchangeRate")) ? 0 : reader.GetDecimal(reader.GetOrdinal("CAD_ExchangeRate"));
                                        party.US_ExchangeRate = reader.IsDBNull(reader.GetOrdinal("US_ExchangeRate")) ? 0 : reader.GetDecimal(reader.GetOrdinal("US_ExchangeRate"));
                                        party.CreditExceededMailSent = reader.IsDBNull(reader.GetOrdinal("CreditExceededMailSent")) ? false : reader.GetBoolean(reader.GetOrdinal("CreditExceededMailSent"));
                                        party.IsInternalCustomer = reader.IsDBNull(reader.GetOrdinal("IsInternalCustomer")) ? false : reader.GetBoolean(reader.GetOrdinal("IsInternalCustomer"));
                                        party.Party_Name = reader.IsDBNull(reader.GetOrdinal("Party_Name")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Name"));
                                        party.Party_Location = reader.IsDBNull(reader.GetOrdinal("Party_Location")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Location"));
                                        party.Party_PaymentTerms = reader.IsDBNull(reader.GetOrdinal("Party_PaymentTerms")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_PaymentTerms"));
                                        // Add additional properties here as needed

                                        PartyDetailsList.Add(party);
                                    }
                                prty = PartyDetailsList.FirstOrDefault();
                                reader.NextResult();

                                while (reader.Read())
                                {
                                    CountryDetailsList.Add(new GNM_RefMasterDetail
                                    {
                                        RefMasterDetail_ID = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                        RefMasterDetail_Name = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_Name")) ? string.Empty : reader.GetString(reader.GetOrdinal("RefMasterDetail_Name")),


                                    });
                                }
                                cntry = CountryDetailsList.AsEnumerable();
                                reader.NextResult();

                                while (reader.Read())
                                {
                                    StateDetailsList.Add(new GNM_State
                                    {
                                        State_ID = reader.IsDBNull(reader.GetOrdinal("State_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("State_ID")),
                                        State_Name = reader.IsDBNull(reader.GetOrdinal("State_Name")) ? string.Empty : reader.GetString(reader.GetOrdinal("State_Name")),
                                        // Add other properties here
                                    });
                                }
                                States = StateDetailsList.AsEnumerable();
                                reader.NextResult();

                                while (reader.Read())
                                {
                                    RefMasterList.Add(new GNM_RefMaster
                                    {
                                        RefMaster_ID = reader.IsDBNull(reader.GetOrdinal("RefMaster_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("RefMaster_ID")),
                                        RefMaster_Name = reader.IsDBNull(reader.GetOrdinal("RefMaster_Name")) ? string.Empty : reader.GetString(reader.GetOrdinal("RefMaster_Name")),

                                        // Add other properties here
                                    });
                                }
                                refMaster = RefMasterList;
                                reader.NextResult();
                                while (reader.Read())
                                {
                                    RefMasterDetailList.Add(new GNM_RefMasterDetail
                                    {
                                        RefMasterDetail_ID = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                        RefMaster_ID = reader.IsDBNull(reader.GetOrdinal("RefMaster_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("RefMaster_ID")),
                                        RefMasterDetail_IsActive = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_IsActive")) ? false : reader.GetBoolean(reader.GetOrdinal("RefMasterDetail_IsActive")),
                                        RefMasterDetail_Name = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_Name")) ? string.Empty : reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"))
                                        // Add other properties here
                                    });
                                }
                                refDetail = RefMasterDetailList;
                                reader.NextResult();

                                reader.NextResult();

                                while (reader.Read())
                                {
                                    RefMasterDetail_Name = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_Name")) ? string.Empty : reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"));
                                }
                                reader.NextResult();
                                while (reader.Read())
                                {
                                    RefMasterDetail_Name2 = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_Name")) ? string.Empty : reader.GetString(reader.GetOrdinal("RefMasterDetail_Name"));
                                }

                                reader.NextResult();
                                while (reader.Read())
                                {
                                    PartyLocaleList.Add(new GNM_PartyLocale
                                    {
                                        Party_Locale_ID = reader.IsDBNull(reader.GetOrdinal("Party_Locale_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Party_Locale_ID")),
                                        Party_ID = reader.IsDBNull(reader.GetOrdinal("Party_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                        Party_Name = reader.IsDBNull(reader.GetOrdinal("Party_Name")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Name")),
                                        Party_Location = reader.IsDBNull(reader.GetOrdinal("Party_Location")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Location")),
                                        Party_PaymentTerms = reader.IsDBNull(reader.GetOrdinal("Party_PaymentTerms")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_PaymentTerms")),
                                        Language_ID = reader.IsDBNull(reader.GetOrdinal("Language_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Language_ID")),
                                        Party_Address = reader.IsDBNull(reader.GetOrdinal("Party_Address")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Address")),
                                        Party_Code = reader.IsDBNull(reader.GetOrdinal("Party_Code")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Code")),
                                        // Add other properties here as needed
                                    });
                                }
                                gnmPrtyLocale = PartyLocaleList.FirstOrDefault();

                                reader.NextResult();
                                while (reader.Read())
                                {
                                    CountryLocaleList.Add(new GNM_RefMasterDetailLocale
                                    {
                                        RefMasterDetail_ID = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                        Language_ID = reader.IsDBNull(reader.GetOrdinal("Language_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Language_ID")),
                                        RefMasterDetail_Name = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_Name")) ? string.Empty : reader.GetString(reader.GetOrdinal("RefMasterDetail_Name")),

                                    });
                                }
                                cntryLocale = CountryLocaleList.AsEnumerable();
                                reader.NextResult();
                                while (reader.Read())
                                {
                                    StateLocaleList.Add(new GNM_StateLocale
                                    {
                                        State_ID = reader.IsDBNull(reader.GetOrdinal("State_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("State_ID")),
                                        Language_ID = reader.IsDBNull(reader.GetOrdinal("Language_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Language_ID")),
                                        State_Name = reader.IsDBNull(reader.GetOrdinal("State_Name")) ? string.Empty : reader.GetString(reader.GetOrdinal("State_Name")),
                                        // Add other properties here
                                    });
                                }
                                StateLocale = StateLocaleList.AsEnumerable();
                                reader.NextResult();
                                while (reader.Read())
                                {
                                    RefMasterDetailLocaleList.Add(new GNM_RefMasterDetailLocale
                                    {
                                        RefMasterDetail_ID = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("RefMasterDetail_ID")),
                                        RefMaster_ID = reader.IsDBNull(reader.GetOrdinal("RefMaster_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("RefMaster_ID")),
                                        RefMasterDetail_Name = reader.IsDBNull(reader.GetOrdinal("RefMasterDetail_Name")) ? string.Empty : reader.GetString(reader.GetOrdinal("RefMasterDetail_Name")),

                                    });
                                }
                                refDetailLocale = RefMasterDetailLocaleList;

                                reader.NextResult();
                                while (reader.Read())
                                {
                                    PartyLocaleList2.Add(new GNM_Party
                                    {

                                        Party_ID = reader.IsDBNull(reader.GetOrdinal("Party_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Party_ID")),
                                        Party_Name = reader.IsDBNull(reader.GetOrdinal("Party_Name")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Name")),
                                        Party_Location = reader.IsDBNull(reader.GetOrdinal("Party_Location")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Location")),
                                        Party_PaymentTerms = reader.IsDBNull(reader.GetOrdinal("Party_PaymentTerms")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_PaymentTerms")),
                                        Party_Code = reader.IsDBNull(reader.GetOrdinal("Party_Code")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Code")),
                                        Party_IsActive = reader.IsDBNull(reader.GetOrdinal("Party_IsActive")) ? false : reader.GetBoolean(reader.GetOrdinal("Party_IsActive")),
                                        Party_Phone = reader.IsDBNull(reader.GetOrdinal("Party_Phone")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Phone")),
                                        Party_Email = reader.IsDBNull(reader.GetOrdinal("Party_Email")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Email")),
                                        Party_Fax = reader.IsDBNull(reader.GetOrdinal("Party_Fax")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Fax")),
                                        PartyType = reader.IsDBNull(reader.GetOrdinal("PartyType")) ? (byte)0 : reader.GetByte(reader.GetOrdinal("PartyType")),
                                        Country_ID = reader.IsDBNull(reader.GetOrdinal("Country_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Country_ID")),
                                        State_ID = reader.IsDBNull(reader.GetOrdinal("State_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("State_ID")),
                                        Relationship_Branch_ID = reader.IsDBNull(reader.GetOrdinal("Relationship_Branch_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Relationship_Branch_ID")),
                                        Relationship_Company_ID = reader.IsDBNull(reader.GetOrdinal("Relationship_Company_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Relationship_Company_ID")),
                                        IsOEM = reader.IsDBNull(reader.GetOrdinal("IsOEM")) ? false : reader.GetBoolean(reader.GetOrdinal("IsOEM")),
                                        IsDealer = reader.IsDBNull(reader.GetOrdinal("IsDealer")) ? false : reader.GetBoolean(reader.GetOrdinal("IsDealer")),
                                        PartsCreditLimit = reader.IsDBNull(reader.GetOrdinal("PartsCreditLimit")) ? 0 : reader.GetDecimal(reader.GetOrdinal("PartsCreditLimit")),
                                        SalesCreditLimit = reader.IsDBNull(reader.GetOrdinal("SalesCreditLimit")) ? 0 : reader.GetDecimal(reader.GetOrdinal("SalesCreditLimit")),
                                        Currency_ID = reader.IsDBNull(reader.GetOrdinal("Currency_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Currency_ID")),
                                        IsImportExport = reader.IsDBNull(reader.GetOrdinal("IsImportExport")) ? false : reader.GetBoolean(reader.GetOrdinal("IsImportExport")),
                                        Party_IsLocked = reader.IsDBNull(reader.GetOrdinal("Party_IsLocked")) ? false : reader.GetBoolean(reader.GetOrdinal("Party_IsLocked")),
                                        PaymentDueDays = reader.IsDBNull(reader.GetOrdinal("PaymentDueDays")) ? 0 : reader.GetInt32(reader.GetOrdinal("PaymentDueDays")),
                                        CustomerLanguageID = reader.IsDBNull(reader.GetOrdinal("CustomerLanguageID")) ? 0 : reader.GetInt32(reader.GetOrdinal("CustomerLanguageID")),
                                        Party_Mobile = reader.IsDBNull(reader.GetOrdinal("Party_Mobile")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Mobile")),
                                        CustomerType = reader.IsDBNull(reader.GetOrdinal("CustomerType")) ? (byte)0 : reader.GetByte(reader.GetOrdinal("CustomerType")),

                                        // Add more properties as needed
                                    });
                                    PartyLocaleList3.Add(new GNM_PartyLocale
                                    {
                                        Party_Locale_ID = reader.IsDBNull(reader.GetOrdinal("Party_Locale_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Party_Locale_ID")),
                                        Language_ID = reader.IsDBNull(reader.GetOrdinal("Language_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Language_ID")),
                                        Party_Address = reader.IsDBNull(reader.GetOrdinal("Party_Address")) ? string.Empty : reader.GetString(reader.GetOrdinal("Party_Address")),

                                    });

                                }
                                gNM_PartyLocale2 = PartyLocaleList2.FirstOrDefault();
                                gNM_PartyLocale3 = PartyLocaleList3.FirstOrDefault();

                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                var PartyLoc = default(dynamic);
                decimal? WIPCreditValueInCAD = 0.00M;
                decimal? WIPCreditValueInUSD = 0.00M;
                decimal? QuoteWIPCreditValueInCAD = 0.00M;
                decimal? QuoteWIPCreditValueInUSD = 0.00M;
                string Query1 = string.Empty;
                string Query2 = string.Empty;

                if (LangID != generalLanguageID)
                {

                    if (gnmPrtyLocale != null)
                    {
                        PartyLoc = new
                        {
                            Party_ID = gnmPrtyLocale.Party_ID,
                            Party_Locale_ID = gnmPrtyLocale.Party_Locale_ID,
                            Party_Location = gnmPrtyLocale.Party_Location,
                            Party_Name = gnmPrtyLocale.Party_Name,
                            Party_PaymentTerms = gnmPrtyLocale.Party_PaymentTerms,
                            Party_IsActive = gNM_PartyLocale2.Party_IsActive,
                            gNM_PartyLocale2.Party_Phone,
                            gNM_PartyLocale2.Party_Email,
                            gNM_PartyLocale2.Party_Fax,
                            gNM_PartyLocale2.PartyType,
                            gNM_PartyLocale2.Country_ID,
                            gNM_PartyLocale2.State_ID,
                            country = (from cntr in cntryLocale
                                       orderby cntr.RefMasterDetail_Name
                                       select new
                                       {
                                           ID = cntr.RefMasterDetail_ID,
                                           Name = cntr.RefMasterDetail_Name
                                       }),
                            State = (
                                            from state in StateLocale
                                            orderby state.State_Name
                                            select new
                                            {
                                                ID = state.State_ID,
                                                Name = state.State_Name
                                            }),
                            currencyArray = from a in refMaster
                                            join c in refDetail on a.RefMaster_ID equals c.RefMaster_ID
                                            join b in refDetailLocale on a.RefMaster_ID equals b.RefMaster_ID
                                            where a.RefMaster_Name == "CURRENCY" && b.Language_ID == LangID && c.RefMasterDetail_IsActive == true
                                            && (Convert.ToBoolean(a.IsCompanySpecific) ? c.Company_ID == CompanyID : c.Company_ID == c.Company_ID)
                                            orderby b.RefMasterDetail_Name
                                            select new
                                            {
                                                ID = b.RefMasterDetail_ID,
                                                Name = b.RefMasterDetail_Name
                                            },
                            languageArray = from a in refMaster
                                            join c in refDetail on a.RefMaster_ID equals c.RefMaster_ID
                                            where a.RefMaster_Name == "LANGUAGE" && c.RefMasterDetail_IsActive == true
                                            orderby c.RefMasterDetail_Name
                                            select new
                                            {
                                                ID = c.RefMasterDetail_ID,
                                                Name = c.RefMasterDetail_Name
                                            },
                            Branch_ID = gNM_PartyLocale2.Relationship_Branch_ID,
                            Company_ID = gNM_PartyLocale2.Relationship_Company_ID,
                            gNM_PartyLocale2.IsOEM,
                            gNM_PartyLocale2.IsDealer,
                            gNM_PartyLocale2.PartsCreditLimit,
                            gNM_PartyLocale2.SalesCreditLimit,
                            gNM_PartyLocale2.Currency_ID,
                            gNM_PartyLocale2.IsImportExport,
                            gNM_PartyLocale2.Party_IsLocked,
                            gNM_PartyLocale2.PaymentDueDays,// Added by Manjunatha P for Phase II correction 
                            Region_ID = prty.Region_ID,
                            gNM_PartyLocale2.CustomerLanguageID,
                            Region_Name = RefMasterDetail_Name,
                            gNM_PartyLocale2.Party_Code,
                            gNM_PartyLocale2.Party_Mobile,
                            gNM_PartyLocale2.CustomerType,
                            IsPONumberMandatory = prty.IsPONumberMandatory,
                            ServiceCreditLimit = prty.ServiceCreditLimit,// Convert.ToDecimal(result.PartyServiceCreditLimit).ToString("0.00"),//Convert.ToDecimal(result.PartyServiceOutStandingCredit).ToString("0.00")
                            ServiceOutStandingCredit = prty.ServiceOutStandingCredit,// (CheckIfContractor.ServiceOutStandingCredit < result.PartyServiceCreditLimit) ? Convert.ToDecimal(result.PartyServiceOutStandingCredit).ToString("0.00") : Convert.ToDecimal(0.00).ToString("0.00"),
                            ServiceCreditLimitinUSD = prty.ServiceCreditLimitinUSD,// Convert.ToDecimal(result.PartyServiceCreditLimitinUSD).ToString("0.00"),                                                   
                            ServiceOutStandingCreditinUSD = prty.ServiceOutStandingCreditinUSD,// (CheckIfContractor.ServiceOutStandingCreditinUSD < result.PartyServiceCreditLimitinUSD) ? Convert.ToDecimal(result.PartyServiceOutStandingCreditinUSD).ToString("0.00") : Convert.ToDecimal(0.00).ToString("0.00"),
                            CreditAvailableCAD = "0.00",// (CheckIfContractor.ServiceOutStandingCredit < result.PartyServiceCreditLimit) ? Convert.ToDecimal(result.PartyServiceCreditLimit - result.PartyServiceOutStandingCredit).ToString("0.00") : Convert.ToDecimal(result.PartyServiceOutStandingCredit).ToString("0.00"),
                            CreditAvailableUSD = "0.00",// (CheckIfContractor.ServiceOutStandingCreditinUSD < result.PartyServiceCreditLimitinUSD) ? Convert.ToDecimal(result.PartyServiceCreditLimitinUSD - result.PartyServiceOutStandingCreditinUSD).ToString("0.00") : Convert.ToDecimal(result.PartyServiceOutStandingCreditinUSD).ToString("0.00"),
                            TotalServiceCreditLimit = "0.00",// Convert.ToDecimal(result.TotalServiceCreditLimit).ToString("0.00"),
                            WIPCreditValueInCAD = "0.00",// (Convert.ToDecimal(WIPCreditValueInCAD) + Convert.ToDecimal(QuoteWIPCreditValueInCAD)).ToString("0.00"),
                            WIPCreditValueInUSD = "0.00",// (Convert.ToDecimal(WIPCreditValueInUSD) + Convert.ToDecimal(QuoteWIPCreditValueInUSD)).ToString("0.00")
                        };
                    }
                    else
                    {
                        PartyLoc = prty != null ? new
                        {
                            Party_Address = prty.Party_ID.ToString(),                        // Use null-coalescing operator to handle null
                            Party_ID = "",
                            Party_Locale_ID = "",
                            Party_Location = "",
                            Party_Name = "",
                            Party_PaymentTerms = "",
                            Party_IsActive = prty?.Party_IsActive ?? false, // Default to false if prty is null
                            Party_Phone = prty?.Party_Phone ?? "",
                            Party_Mobile = prty?.Party_Mobile ?? "",
                            Party_Email = prty?.Party_Email ?? "",
                            Party_Fax = prty?.Party_Fax ?? "",
                            PartyType = prty?.PartyType != null ? prty.PartyType.ToString() : "",
                            Country_ID = prty?.Country_ID ?? 0,
                            State_ID = prty?.State_ID ?? 0,
                            Party_Zip = "",
                            CustomerType = prty?.CustomerType != null ? prty.CustomerType.ToString() : "",
                            country = (from cntr in cntryLocale
                                       orderby cntr.RefMasterDetail_Name
                                       select new
                                       {
                                           ID = cntr.RefMasterDetail_ID,
                                           Name = cntr.RefMasterDetail_Name
                                       }),
                            State = (
                 from state in StateLocale
                 orderby state.State_Name
                 select new
                 {
                     ID = state.State_ID,
                     Name = state.State_Name
                 }),
                            currencyArray = from a in refMaster
                                            join c in refDetail on a.RefMaster_ID equals c.RefMaster_ID
                                            join b in refDetailLocale on a.RefMaster_ID equals b.RefMaster_ID
                                            where a.RefMaster_Name == "CURRENCY" && b.Language_ID == LangID && c.RefMasterDetail_IsActive == true
                                            && (Convert.ToBoolean(a.IsCompanySpecific) ? c.Company_ID == CompanyID : c.Company_ID == c.Company_ID)
                                            orderby b.RefMasterDetail_Name
                                            select new
                                            {
                                                ID = b.RefMasterDetail_ID,
                                                Name = b.RefMasterDetail_Name
                                            },
                            languageArray = from a in refMaster
                                            join c in refDetail on a.RefMaster_ID equals c.RefMaster_ID
                                            where a.RefMaster_Name == "LANGUAGE" && c.RefMasterDetail_IsActive == true
                                            orderby c.RefMasterDetail_Name
                                            select new
                                            {
                                                ID = c.RefMasterDetail_ID,
                                                Name = c.RefMasterDetail_Name
                                            },
                            Branch_ID = prty?.Relationship_Branch_ID ?? 0,
                            Company_ID = prty?.Relationship_Company_ID ?? 0,
                            Currency_ID = prty?.Currency_ID ?? 0,
                            IsOEM = prty?.IsOEM ?? false,
                            IsDealer = prty?.IsDealer ?? false,
                            PartsCreditLimit = prty?.PartsCreditLimit ?? 0.00m,
                            SalesCreditLimit = prty?.SalesCreditLimit ?? 0.00m,
                            PaymentDueDays = prty?.PaymentDueDays ?? 0,
                            Party_IsLocked = prty?.Party_IsLocked ?? false,
                            Region_ID = prty?.Region_ID ?? 0,
                            CustomerLanguageID = prty?.CustomerLanguageID ?? 0,
                            Region_Name = RefMasterDetail_Name,
                            IsImportExport = prty?.IsImportExport ?? false,
                            Party_Code = prty?.Party_Code ?? "",
                            IsPONumberMandatory = prty?.IsPONumberMandatory ?? false,
                            Variance_Percentage = prty?.Variance_Percentage ?? 0,
                            Variance_Value = prty?.Variance_Value ?? 0,
                            ServiceCreditLimit = prty?.ServiceCreditLimit ?? 0.00m,
                            ServiceOutStandingCredit = prty?.ServiceOutStandingCredit ?? 0.00m,
                            ServiceCreditLimitinUSD = prty?.ServiceCreditLimitinUSD ?? 0.00m,
                            ServiceOutStandingCreditinUSD = prty?.ServiceOutStandingCreditinUSD ?? 0.00m,
                            CreditAvailableCAD = "0.00",
                            CreditAvailableUSD = "0.00",
                            TotalServiceCreditLimit = "0.00",
                            WIPCreditValueInCAD = "0.00",
                            WIPCreditValueInUSD = "0.00"
                        } : null;
                    }
                }
                jsonData = new
                {
                    PartyEng = prty != null ? new
                    {
                        Party_IsLocked = prty.Party_IsLocked,
                        Party_ID = prty.Party_ID,
                        Party_Name = prty.Party_Name,
                        Party_NamewithoutK = (prty.Party_Name.LastIndexOf("-(K)") > 0 && prty.IsKeyCustomer == true) ? prty.Party_Name.Remove(prty.Party_Name.LastIndexOf("-(K)")) : prty.Party_Name,
                        ISKeycustomer = prty.IsKeyCustomer,
                        Party_Location = prty.Party_Location,
                        Party_Phone = prty.Party_Phone,
                        Party_Email = prty.Party_Email,
                        Party_Mobile = prty.Party_Mobile,
                        Party_Fax = prty.Party_Fax,
                        Party_PaymentTerms = prty.Party_PaymentTerms,
                        PartyType = prty.PartyType,
                        Country_ID = prty.Country_ID,
                        Branch_ID = prty.Relationship_Branch_ID,
                        Company_ID = prty.Relationship_Company_ID,
                        CustomerType = prty.CustomerType,
                        IsOEM = prty.IsOEM,
                        IsDealer = prty.IsDealer,
                        PartsCreditLimit = prty.PartsCreditLimit,
                        SalesCreditLimit = (prty.SalesCreditLimit.ToString() == "" || prty.SalesCreditLimit.ToString() == null) ? 0.00M : prty.SalesCreditLimit,
                        PaymentDueDays = prty.PaymentDueDays,
                        Currency_ID = prty.Currency_ID,
                        CustomerLanguageID = prty.CustomerLanguageID,
                        Variance_Percentage = prty.Variance_Percentage,
                        Variance_Value = prty.Variance_Value,
                        country = (from cntr in cntry
                                   orderby cntr.RefMasterDetail_Name
                                   select new
                                   {
                                       ID = cntr.RefMasterDetail_ID,
                                       Name = cntr.RefMasterDetail_Name
                                   }),
                        State_ID = prty.State_ID,
                        State = (from state in States
                                 orderby state.State_Name
                                 select new
                                 {
                                     ID = state.State_ID,
                                     Name = state.State_Name
                                 }),
                        currencyArray = (from a in refMaster
                                         join b in refDetail on a.RefMaster_ID equals b.RefMaster_ID
                                         where a.RefMaster_Name == "CURRENCY" && b.RefMasterDetail_IsActive == true && (Convert.ToBoolean(a.IsCompanySpecific) ? b.Company_ID == CompanyID : b.Company_ID == b.Company_ID)
                                         orderby b.RefMasterDetail_Name
                                         select new
                                         {
                                             ID = b.RefMasterDetail_ID,
                                             Name = b.RefMasterDetail_Name
                                         }),
                        languageArray = from a in refMaster
                                        join c in refDetail on a.RefMaster_ID equals c.RefMaster_ID
                                        where a.RefMaster_Name == "LANGUAGE" && c.RefMasterDetail_IsActive == true
                                        orderby c.RefMasterDetail_Name
                                        select new
                                        {
                                            ID = c.RefMasterDetail_ID,
                                            Name = c.RefMasterDetail_Name
                                        },
                        Party_IsActive = prty.Party_IsActive,
                        IsImportExport = prty.IsImportExport,
                        Party_Code = prty.Party_Code,
                        Region_ID = prty.Region_ID,
                        Region_Name = RefMasterDetail_Name2,
                        IsPONumberMandatory = prty.IsPONumberMandatory,
                        ServiceCreditLimit = prty.ServiceCreditLimit,
                        ServiceOutStandingCredit = prty.ServiceOutStandingCredit,
                        ServiceCreditLimitinUSD = prty.ServiceCreditLimitinUSD,
                        ServiceOutStandingCreditinUSD = prty.ServiceOutStandingCreditinUSD,
                        CreditAvailableCAD = "0.00",
                        CreditAvailableUSD = "0.00",
                        TotalServiceCreditLimit = "0.00",
                        WIPCreditValueInCAD = "0.00",
                        WIPCreditValueInUSD = "0.00"
                    } : null,
                    PartyLoc = prty != null ? prty.Party_Location : null
                };
                // gbl.InsertGPSDetails(Convert.ToInt32(SelectParticularPartyobj.Company_ID.ToString()), BranchID, User.User_ID, Common.GetObjectID("CorePartyMaster"), jsonData.PartyEng.Party_ID, 0, 0, "Viewed Party- " + jsonData.PartyEng.Party_Name + "", false, Convert.ToInt32(SelectParticularPartyobj.MenuID), Convert.ToDateTime(SelectParticularPartyobj.LoggedINDateTime));
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    //
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: SelectBranchTaxDetails Uday Kumar J B 21-08-2024:::
        /// <summary>
        /// to select all the branch tax details
        /// </summary> 
        /// 

        public static IActionResult SelectPartyTaxStructureDetails(string connString, SelectPartyTaxStructureDetailsList SelectPartyTaxStructureDetailsobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // GNM_User User = SelectPartyTaxStructureDetailsobj.UserDetails.FirstOrDefault();
                int companyID = SelectPartyTaxStructureDetailsobj.Company_ID;
                int userLanguageID = Convert.ToInt32(SelectPartyTaxStructureDetailsobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectPartyTaxStructureDetailsobj.GeneralLanguageID);
                int Company_ID = Convert.ToInt32(SelectPartyTaxStructureDetailsobj.Company_ID);
                bool IsGeneral = Convert.ToBoolean(SelectPartyTaxStructureDetailsobj.IsGeneral);
                int count = 0;
                int total = 0;

                string ViewLabel = CommonFunctionalities.GetGlobalResourceObject(SelectPartyTaxStructureDetailsobj.UserCulture.ToString(), "view").ToString();
                List<GNM_PartyTaxStructure> TaxStructureList = new List<GNM_PartyTaxStructure>();

                using (SqlConnection con = new SqlConnection(connString))
                {
                    con.Open();

                    // Fetch count of Party Tax Structure records
                    using (SqlCommand cmd = new SqlCommand("sp_GetPartyTaxStructureCount", con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Party_ID", SelectPartyTaxStructureDetailsobj.Party_ID);
                        count = (int)cmd.ExecuteScalar();
                    }

                    total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                    // Fetch Party Tax Structure details with sorting
                    using (SqlCommand cmd = new SqlCommand("sp_GetPartyTaxStructureDetails", con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Party_ID", SelectPartyTaxStructureDetailsobj.Party_ID);
                        cmd.Parameters.AddWithValue("@sidx", sidx);
                        cmd.Parameters.AddWithValue("@sord", sord);
                        SqlDataReader reader = cmd.ExecuteReader();

                        while (reader.Read())
                        {
                            TaxStructureList.Add(new GNM_PartyTaxStructure
                            {
                                PartyTaxStructure_ID = (int)reader["PartyTaxStructure_ID"],
                                TaxStructure_ID = (int)reader["TaxStructure_ID"],
                                // add remaining properties as required
                            });
                        }
                        reader.Close();
                    }
                }

                dynamic s = null;
                var x = s;

                // Fetch tax structure names
                List<dynamic> taxArray = new List<dynamic>();
                using (SqlConnection con = new SqlConnection(connString))
                {
                    con.Open();
                    string taxQuery = IsGeneral ? "sp_GetGeneralTaxStructure" : "sp_GetLocaleTaxStructure";
                    using (SqlCommand cmd = new SqlCommand(taxQuery, con))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Company_ID", Company_ID);

                        SqlDataReader reader = cmd.ExecuteReader();
                        while (reader.Read())
                        {
                            taxArray.Add(new
                            {
                                Tax_Structure_Header_id = (int)reader["TaxStructure_ID"],
                                Tax_Structure_Name = reader["TaxStructure_Name"].ToString()
                            });
                        }
                        reader.Close();
                    }
                }

                string PartyTaxStructure = "-1:--Select--;";
                foreach (var taxObj in taxArray)
                {
                    PartyTaxStructure += taxObj.Tax_Structure_Header_id + ":" + taxObj.Tax_Structure_Name.Replace(";", ":") + ";";
                }
                PartyTaxStructure = PartyTaxStructure.TrimEnd(new char[] { ';' });

                if (IsGeneral)
                {
                    var PartyTaxStructureList = from a in TaxStructureList
                                                join b in taxArray on a.TaxStructure_ID equals b.Tax_Structure_Header_id
                                                select new
                                                {
                                                    edit = "<a title='" + ViewLabel + "' href='#' style='font-size: 13px;' key='" + a.PartyTaxStructure_ID + "' id='" + a.PartyTaxStructure_ID + "'class='editPartyMasterTaxStructure'  editmode='false' ><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                                    delete = "<input type='checkbox' key='" + a.PartyTaxStructure_ID + "' id='chkPartyTaxStructure" + a.PartyTaxStructure_ID + "' class='chkPartyTaxStructureDelete'/>",
                                                    BranchTaxStructure_ID = a.PartyTaxStructure_ID,
                                                    Tax_Structure = b.Tax_Structure_Name
                                                };

                    var arr = PartyTaxStructureList.Skip((page - 1) * rows).Take(rows).ToList();
                    x = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        data = arr.ToArray(),
                        PartyTaxStructure
                    };
                }
                else
                {
                    // Handle locale-specific logic as in the original code
                    var PartyLocaleTaxStructureList = from a in TaxStructureList
                                                      join b in taxArray on a.TaxStructure_ID equals b.Tax_Structure_Header_id
                                                      where b.Language_ID == userLanguageID
                                                      select new
                                                      {
                                                          PartyTaxStructure_id = a.PartyTaxStructure_ID,
                                                          Tax_Structure = b.TaxStructure_Name
                                                      };

                    var arr = PartyLocaleTaxStructureList.Skip((page - 1) * rows).Take(rows).ToList();

                    x = new
                    {
                        total = total,
                        page = page,
                        records = PartyLocaleTaxStructureList.Count(),
                        data = arr.ToArray(),
                        PartyTaxStructure
                    };
                }

                return new JsonResult(x);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }

        #endregion


        #region ::: SavePartyTaxStructureDetail Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// to update branch tax 
        /// </summary>
        public static IActionResult SavePartyTaxStructureDetail(string connString, SavePartyTaxStructureDetailList SavePartyTaxStructureDetailobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jObj = JObject.Parse(SavePartyTaxStructureDetailobj.PartyTaxStructureData);

                string Party_ID = jObj["Party_ID"].ToString();
                int PartyId = Convert.ToInt32(Party_ID);
                int CompanyID = Convert.ToInt32(SavePartyTaxStructureDetailobj.Company_ID);
                int rowCount = jObj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int j = 0; j < rowCount; j++)
                    {
                        JObject row = (JObject)jObj["rows"][j];
                        string taxDetailID = row["taxDetailID"]?.ToString();
                        string tax = row["tax"].ToString();

                        using (SqlCommand cmd = new SqlCommand("UP_Ins_Upd_AM_ERP_SavePartyTaxStructureDetail", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;

                            cmd.Parameters.AddWithValue("@PartyID", PartyId);
                            cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                            cmd.Parameters.AddWithValue("@TaxStructure_ID", Convert.ToInt32(tax));

                            if (!string.IsNullOrEmpty(taxDetailID))
                            {
                                cmd.Parameters.AddWithValue("@TaxDetailID", Convert.ToInt32(taxDetailID));
                                cmd.Parameters.AddWithValue("@IsUpdate", 1);
                            }
                            else
                            {
                                cmd.Parameters.AddWithValue("@TaxDetailID", DBNull.Value);
                                cmd.Parameters.AddWithValue("@IsUpdate", 0);
                            }

                            cmd.ExecuteNonQuery();
                        }
                    }
                }

                //gbl.InsertGPSDetails(
                //    Convert.ToInt32(SavePartyTaxStructureDetailobj.Company_ID),
                //    Convert.ToInt32(SavePartyTaxStructureDetailobj.Branch),
                //    Convert.ToInt32(SavePartyTaxStructureDetailobj.User_ID),
                //    Convert.ToInt32(Common.GetObjectID("CoreCompanyMaster")),
                //    CompanyID,
                //    0,
                //    0,
                //    "Update",
                //    false,
                //    Convert.ToInt32(SavePartyTaxStructureDetailobj.MenuID)
                //);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }
        #endregion


        #region ::: DeletePartyTaxStructureDetails Uday Kumar J B 21-08-2024 :::
        /// <summary>
        /// to Delete the Branch tax details
        /// </summary>
        public static IActionResult DeletePartyTaxStructureDetails(string connString, DeletePartyTaxStructureDetailsList DeletePartyTaxStructureDetailsobj)
        {
            string errorMsg = "";
            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    SqlCommand command = new SqlCommand("UP_Del_AM_ERP_DeletePartyTaxStructure", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    JObject jobj = JObject.Parse(DeletePartyTaxStructureDetailsobj.key);
                    int rowCount = jobj["rows"].Count();

                    connection.Open();
                    for (int i = 0; i < rowCount; i++)
                    {
                        int id = jobj["rows"][i]["id"].ToObject<int>();
                        command.Parameters.Clear();
                        command.Parameters.AddWithValue("@PartyTaxStructure_ID", id);

                        int rowsAffected = command.ExecuteNonQuery();
                        if (rowsAffected == 0)
                        {
                            errorMsg += CommonFunctionalities.GetResourceString(DeletePartyTaxStructureDetailsobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                        }
                    }
                }

                errorMsg += CommonFunctionalities.GetResourceString(DeletePartyTaxStructureDetailsobj.UserCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (Exception ex)
            {
                errorMsg += ex.Message;
            }

            return new JsonResult(errorMsg);
        }
        #endregion


        #region ::: CheckBranchTaxDetails  Uday Kumar J B 21-08-2024:::
        /// <summary>
        /// to check if the Tax detail has already been selected for the branch
        /// </summary>
        public static IActionResult CheckBranchTaxDetails(string connString, CheckBranchTaxDetailsList CheckBranchTaxDetailsobj)
        {
            int status = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                List<GNM_PartyTaxStructure> checkRow = new List<GNM_PartyTaxStructure>();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_CHECK_AM_ERP_CheckBranchTaxDetailsParty";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@Party_ID", CheckBranchTaxDetailsobj.Party_ID);
                            command.Parameters.AddWithValue("@TaxStructure_ID", CheckBranchTaxDetailsobj.taxStructureID);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var Data = new GNM_PartyTaxStructure
                                    {
                                        PartyTaxStructure_ID = reader.GetInt32(reader.GetOrdinal("PartyTaxStructure_ID")),

                                    };
                                    checkRow.Add(Data);
                                }


                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                if (checkRow != null)
                {
                    for (int i = 0; i < checkRow.Count; i++)
                    {
                        if (checkRow[i].PartyTaxStructure_ID != CheckBranchTaxDetailsobj.primaryKey)
                        {
                            status = 1;
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }
            }
            return new JsonResult(status);
        }
        #endregion


        #region ::: SelectPartyContactDetails Uday Kumar J B 21-08-2024:::
        /// <summary>
        ////to Select Particular Contact Details Party based on ID Passed
        /// </summary>   
        /// 
        public static IActionResult SelectPartyContactDetails(string connString, SelectPartyContactDetailsList SelectPartyContactDetailsobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            var jsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));

            try
            {
                int count = 0;
                int total = 0;
                int value = Convert.ToInt32(SelectPartyContactDetailsobj.value);
                int Language_ID = Convert.ToInt32(SelectPartyContactDetailsobj.LanguageID);
                IEnumerable<GNM_PartyContactPersonDetails> PrtyCont = null;
                List<PartyContactPerson> partyContactPersons = new List<PartyContactPerson>();
                bool IsGeneral = Convert.ToBoolean(SelectPartyContactDetailsobj.IsGeneral);
                IQueryable<PartyContactPerson> iQPrtycont = null;
                IEnumerable<PartyContactPerson> prtyContdtl = null;

                string ViewLabel = CommonFunctionalities.GetGlobalResourceObject(SelectPartyContactDetailsobj.UserCulture, "view").ToString();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_SELECT_AM_ERP_SelectPartyContactDetailsParty";

                    using (SqlCommand command = new SqlCommand(query, conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@Party_ID", value);
                        command.Parameters.AddWithValue("@IsGeneral", IsGeneral);
                        command.Parameters.AddWithValue("@Language_ID", Language_ID);

                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                PartyContactPerson contactPerson = new PartyContactPerson
                                {
                                    PartyContactPerson_ID = reader.GetInt32(reader.GetOrdinal("PartyContactPerson_ID")),
                                    PartyContactPerson_IsActive = reader.GetBoolean(reader.GetOrdinal("PartyContactPerson_IsActive")) ? "Yes" : "No",
                                    Party_IsDefaultContact = reader.GetBoolean(reader.GetOrdinal("Party_IsDefaultContact")) ? "Yes" : "No",
                                    PartyContactPerson_Name = reader.IsDBNull(reader.GetOrdinal("PartyContactPerson_Name")) ? string.Empty : reader.GetString(reader.GetOrdinal("PartyContactPerson_Name")),
                                    PartyContactPerson_DOB = reader.IsDBNull(reader.GetOrdinal("PartyContactPerson_DOB")) ? string.Empty : reader.GetDateTime(reader.GetOrdinal("PartyContactPerson_DOB")).ToString("dd-MMM-yyyy"),
                                    PartyContactPerson_Department = reader.IsDBNull(reader.GetOrdinal("PartyContactPerson_Department")) ? string.Empty : reader.GetString(reader.GetOrdinal("PartyContactPerson_Department")),
                                    PartyContactPerson_Email = reader.IsDBNull(reader.GetOrdinal("PartyContactPerson_Email")) ? string.Empty : reader.GetString(reader.GetOrdinal("PartyContactPerson_Email")),
                                    PartyContactPerson_Phone = reader.IsDBNull(reader.GetOrdinal("PartyContactPerson_Phone")) ? string.Empty : reader.GetString(reader.GetOrdinal("PartyContactPerson_Phone")),
                                    PartyContactPerson_Mobile = reader.IsDBNull(reader.GetOrdinal("PartyContactPerson_Mobile")) ? string.Empty : reader.GetString(reader.GetOrdinal("PartyContactPerson_Mobile")),
                                    PartyContactPerson_Remarks = reader.IsDBNull(reader.GetOrdinal("PartyContactPerson_Remarks")) ? string.Empty : reader.GetString(reader.GetOrdinal("PartyContactPerson_Remarks")),
                                    Local = "<a key='" + reader.GetInt32(reader.GetOrdinal("PartyContactPerson_ID")) + "' id='" + reader.GetInt32(reader.GetOrdinal("PartyContactPerson_ID")) + "'  class='PartyContactLocale' alt='Localize' width='20' height='20'  title='Localize'><i class='fa fa-globe'></i></a>"
                                };

                                partyContactPersons.Add(contactPerson);
                            }
                        }
                    }
                }

                iQPrtycont = partyContactPersons.AsQueryable();

                // Filter ToolBar Search
                if (_search)
                {
                    Filters filterObj = JObject.Parse(filters).ToObject<Filters>();
                    iQPrtycont = iQPrtycont.FilterSearch(filterObj);
                }
                // Advance Search
                else if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse(advnceFilters).ToObject<AdvanceFilter>();
                    iQPrtycont = iQPrtycont.AdvanceSearch(advnfilter);
                }

                // Sorting 
                iQPrtycont = iQPrtycont.OrderByField(sidx, sord);

                count = iQPrtycont.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                jsonData = new
                {
                    total = total,
                    page = page,
                    records = count,
                    rows = iQPrtycont.Select(a => new
                    {
                        edit = "<a title='" + ViewLabel + "' href='#' style='font-size: 13px;' id='" + a.PartyContactPerson_ID + "' key='" + a.PartyContactPerson_ID + "' class='editPartyContactDetails' editmode='false' ><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                        delete = "<input type='checkbox' key='" + a.PartyContactPerson_ID + "' defaultchecked=''  id='chk" + a.PartyContactPerson_ID + "'  " + (a.IsDefault ? "class='CannotDelPartyContactDetails'" : "class='DelPartyContactDetails'") + "/>",
                        a.PartyContactPerson_ID,
                        a.PartyContactPerson_IsActive,
                        a.Party_IsDefaultContact,
                        a.PartyContactPerson_Name,
                        a.PartyContactPerson_DOB,
                        PartyContactPerson_Department = a.PartyContactPerson_Department ?? "",
                        a.PartyContactPerson_Email,
                        a.PartyContactPerson_Phone,
                        a.PartyContactPerson_Mobile,
                        a.PartyContactPerson_Remarks,
                        a.Local
                    }).ToList()
                };
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: SelectPartyTaxDetails Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///SelectPartyTaxDetails
        public static IActionResult SelectPartyTaxDetails(string connString, SelectPartyTaxDetailsList SelectPartyTaxDetailsobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            var jsonData = new object();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int totalRecords = 0;
                int value = Convert.ToInt32(SelectPartyTaxDetailsobj.value);

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    SqlCommand command = new SqlCommand("UP_Sel_AM_ERP_SelectPartyTaxDetails", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.AddWithValue("@Party_ID", value);
                    command.Parameters.AddWithValue("@Page", page);
                    command.Parameters.AddWithValue("@RowsPerPage", rows);
                    command.Parameters.AddWithValue("@SortColumn", sidx);
                    command.Parameters.AddWithValue("@SortOrder", sord);

                    command.Parameters.Add("@TotalRecords", SqlDbType.Int);
                    command.Parameters["@TotalRecords"].Direction = ParameterDirection.Output;

                    connection.Open();

                    List<object> rowsList = new List<object>();
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            rowsList.Add(new
                            {
                                edit = "<a title='" + CommonFunctionalities.GetGlobalResourceObject(SelectPartyTaxDetailsobj.UserCulture.ToString(), "view").ToString() + "' href='#' style='font-size: 13px;' id='" + reader["PartyTax_ID"] + "' key='" + reader["PartyTax_ID"] + "' class='editPartyTaxDetails' editmode='false' ><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input type='checkbox' key='" + reader["PartyTax_ID"] + "' defaultchecked=''  id='chk" + reader["PartyTax_ID"] + "' class='DelPartyTaxDetails'/>",
                                PartyTax_ID = reader["PartyTax_ID"],
                                PartyTax_TaxCode = reader["PartyTax_TaxCode"].ToString(),
                                PartyTax_TaxCodeDescription = reader["PartyTax_TaxCodeDescription"].ToString(),
                                PartyTaxDetails_IsActive = (bool)reader["PartyTaxDetails_IsActive"] ? "Yes" : "No"
                            });
                        }
                    }

                    int total = command.Parameters["@TotalRecords"].Value != DBNull.Value ? Convert.ToInt32(command.Parameters["@TotalRecords"].Value) : 0;

                    jsonData = new
                    {
                        total = total,
                        page = page,
                        records = totalRecords,
                        rows = rowsList
                    };
                }
            }
            catch (Exception ex)
            {
                // Handle exceptions
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    // Handle logging as needed
                }
            }

            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: PartyMasterData Uday Kumar J B 21-08-2024 :::
        /// <summary>
        ///PartyMasterData
        /// </summary> 
        public static IActionResult PartyMasterData(string connString, PartyMasterDataList PartyMasterDataobj)
        {
            var jsonData = new
            {
                country = new List<object>(),
                CustomerType = new List<object>()
            };
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            int userLanguageID = Convert.ToInt32(PartyMasterDataobj.UserLanguageID);
            int generalLanguageID = Convert.ToInt32(PartyMasterDataobj.GeneralLanguageID);
            int companyID = Convert.ToInt32(PartyMasterDataobj.Company_ID);

            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("UP_Get_AM_ERP_GetPartyMasterData", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                if (reader["DataType"].ToString() == "COUNTRY")
                                {
                                    jsonData.country.Add(new
                                    {
                                        ID = reader["ID"],
                                        Name = reader["Name"]
                                    });
                                }
                                else if (reader["DataType"].ToString() == "CUSTOMERTYPE")
                                {
                                    jsonData.CustomerType.Add(new
                                    {
                                        ID = reader["ID"],
                                        Name = reader["Name"]
                                    });
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: PartyMasterDataLocale Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///PartyMasterDataLocale
        /// </summary>   
        /// 
        public static IActionResult PartyMasterDataLocale(string connString)
        {
            var jsonData = new object();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("UP_Get_AM_ERP_GetPartyMasterDataLocale", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        conn.Open();
                        SqlDataReader reader = cmd.ExecuteReader();

                        var countries = new List<object>();

                        while (reader.Read())
                        {
                            countries.Add(new
                            {
                                ID = reader["ID"],
                                Name = reader["Name"]
                            });
                        }

                        jsonData = new
                        {
                            country = countries
                        };

                        reader.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }
        #endregion


        #region :::StateMasterData Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///StateMasterData
        /// </summary>
        public static IActionResult StateMasterData(string connString, StateMasterDataList StateMasterDataobj)
        {
            var jsonData = new { State = new List<object>() };
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    using (SqlCommand command = new SqlCommand("UP_Get_AM_ERP_GetStateMasterData", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new SqlParameter("@Country_ID", StateMasterDataobj.id));

                        connection.Open();
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            var states = new List<object>();
                            while (reader.Read())
                            {
                                states.Add(new
                                {
                                    ID = reader["State_ID"],
                                    Name = reader["State_Name"]
                                });
                            }
                            jsonData = new { State = states };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion


        #region :::StateMasterDataLocale Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///StateMasterData
        /// </summary> 
        public static IActionResult StateMasterDataLocale(string connString, StateMasterDataLocaleList StateMasterDataLocaleobj)
        {
            var jsonData = new { State = new List<object>() };
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (var connection = new SqlConnection(connString))
                {
                    using (var command = new SqlCommand("UP_Get_AM_ERP_GetStateMasterDataLocale", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new SqlParameter("@CountryID", SqlDbType.Int) { Value = StateMasterDataLocaleobj.id });

                        connection.Open();

                        using (var reader = command.ExecuteReader())
                        {
                            var states = new List<object>();

                            while (reader.Read())
                            {
                                states.Add(new
                                {
                                    ID = reader["ID"],
                                    Name = reader["Name"]
                                });
                            }

                            jsonData = new { State = states };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: PartyMasterSave Uday Kumar J B 21-08-2024:::
        /// <summary>
        /// to save the Party Master
        /// </summary>   
        public static IActionResult PartyMasterSave(string connString, PartyMasterSaveList PartyMasterSaveobj)
        {
            var jsonResult = new { IsSuccess = false };
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                //GNM_User User = PartyMasterSaveobj.UserDetails.FirstOrDefault();
                GNM_Party gnmPrty = JObject.Parse(PartyMasterSaveobj.key).ToObject<GNM_Party>();
                int BranchID = Convert.ToInt32(PartyMasterSaveobj.Branch);
                int newPartyID = 0;
                string action = gnmPrty.Party_ID == 0 ? "INSERT" : "UPDATE";
                gnmPrty.Party_Name = Common.DecryptString(gnmPrty.Party_Name);
                gnmPrty.Party_Location = Common.DecryptString(gnmPrty.Party_Location);
                gnmPrty.Party_Code = Common.DecryptString(gnmPrty.Party_Code);
                gnmPrty.Party_PaymentTerms = Common.DecryptString(gnmPrty.Party_PaymentTerms);
                DateTime loggedInDate = PartyMasterSaveobj.LoggedINDateTime;

                // Check if the date is outside the SQL Server valid range
                if (loggedInDate < new DateTime(1753, 1, 1) || loggedInDate > new DateTime(9999, 12, 31))
                {
                    loggedInDate = DateTime.Today; // Assign today's date if the value is out of range
                }
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("UP_Ins_Upd_AM_ERP_SavePartyMaster", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Set parameters
                        cmd.Parameters.AddWithValue("@Party_ID", gnmPrty.Party_ID);
                        cmd.Parameters.AddWithValue("@Party_Name", gnmPrty.Party_Name);
                        cmd.Parameters.AddWithValue("@Party_Location", gnmPrty.Party_Location);
                        cmd.Parameters.AddWithValue("@Party_Code", gnmPrty.Party_Code);
                        cmd.Parameters.AddWithValue("@Party_PaymentTerms", gnmPrty.Party_PaymentTerms);
                        cmd.Parameters.AddWithValue("@Relationship_Branch_ID", gnmPrty.Relationship_Branch_ID ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Relationship_Company_ID", gnmPrty.Relationship_Company_ID ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Region_ID", gnmPrty.Region_ID ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ModifiedBY", Convert.ToInt32(PartyMasterSaveobj.User_ID));
                        cmd.Parameters.AddWithValue("@Country_ID", gnmPrty.Country_ID);
                        cmd.Parameters.AddWithValue("@Company_ID", PartyMasterSaveobj.Company_ID);
                        cmd.Parameters.AddWithValue("@IsKeyCustomer", false);
                        cmd.Parameters.AddWithValue("@Party_Email", gnmPrty.Party_Email);
                        cmd.Parameters.AddWithValue("@Party_Fax", gnmPrty.Party_Fax);
                        cmd.Parameters.AddWithValue("@Party_IsActive", gnmPrty.Party_IsActive);
                        cmd.Parameters.AddWithValue("@Party_IsLocked", gnmPrty.Party_IsLocked);
                        cmd.Parameters.AddWithValue("@Party_Mobile", gnmPrty.Party_Mobile);
                        cmd.Parameters.AddWithValue("@Party_Phone", gnmPrty.Party_Phone);
                        cmd.Parameters.AddWithValue("@PartyType", gnmPrty.PartyType);
                        cmd.Parameters.AddWithValue("@State_ID", gnmPrty.State_ID);
                        cmd.Parameters.AddWithValue("@IsDealer", gnmPrty.IsDealer);
                        cmd.Parameters.AddWithValue("@IsOEM", gnmPrty.IsOEM);
                        cmd.Parameters.AddWithValue("@PartsCreditLimit", gnmPrty.PartsCreditLimit);
                        cmd.Parameters.AddWithValue("@SalesCreditLimit", gnmPrty.SalesCreditLimit);
                        cmd.Parameters.AddWithValue("@PaymentDueDays", gnmPrty.PaymentDueDays);
                        cmd.Parameters.AddWithValue("@Currency_ID", gnmPrty.Currency_ID);
                        cmd.Parameters.AddWithValue("@IsImportExport", gnmPrty.IsImportExport);
                        cmd.Parameters.AddWithValue("@CustomerType", gnmPrty.CustomerType);
                        cmd.Parameters.AddWithValue("@CustomerLanguageID", gnmPrty.CustomerLanguageID);
                        cmd.Parameters.AddWithValue("@IsPONumberMandatory", gnmPrty.IsPONumberMandatory);
                        cmd.Parameters.AddWithValue("@Variance_Percentage", gnmPrty.Variance_Percentage ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Variance_Value", gnmPrty.Variance_Value ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@LoggedINDateTime", loggedInDate);
                        cmd.Parameters.AddWithValue("@Action", action);

                        // Execute the command
                        cmd.ExecuteNonQuery();
                    }

                    // Insert GPS details
                    //gbl.InsertGPSDetails(PartyMasterSaveobj.Company_ID, BranchID, PartyMasterSaveobj.User_ID, Common.GetObjectID("CorePartyMaster"), newPartyID, 0, 0,
                    //    action == "INSERT" ? $"Inserted Party- {gnmPrty.Party_Name}" : $"Updated Party- {gnmPrty.Party_Name}", false,
                    //    Convert.ToInt32(PartyMasterSaveobj.MenuID), Convert.ToDateTime(PartyMasterSaveobj.LoggedINDateTime));

                    jsonResult = new { IsSuccess = true };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                jsonResult = new { IsSuccess = false };
            }
            finally
            {

                SqlConnection.ClearAllPools();

            }

            return new JsonResult(jsonResult);
        }
        #endregion


        #region ::: PartyMasterSaveLocale Uday Kumar J B 21-08-2024:::
        /// <summary>
        /// to save the Party Master
        /// </summary>   
        /// 

        public static IActionResult PartyMasterSaveLocale(string connString, PartyMasterSaveLocaleList PartyMasterSaveLocaleobj)
        {
            var jsonResult = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                // Parse and decrypt incoming data
                GNM_PartyLocale gnmPrty = JObject.Parse(PartyMasterSaveLocaleobj.key).ToObject<GNM_PartyLocale>();
                gnmPrty.Party_Location = Common.DecryptString(gnmPrty.Party_Location);
                gnmPrty.Party_Name = Common.DecryptString(gnmPrty.Party_Name);
                gnmPrty.Party_ID = gnmPrty.Party_ID;
                gnmPrty.Party_PaymentTerms = Common.DecryptString(gnmPrty.Party_PaymentTerms);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    SqlCommand cmd = new SqlCommand();
                    cmd.Connection = conn;

                    if (gnmPrty.Party_Locale_ID == 0)
                    {
                        // Insert new record
                        cmd.CommandText = "sp_InsertPartyLocale";
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@Party_Location", gnmPrty.Party_Location);
                        cmd.Parameters.AddWithValue("@Party_Name", gnmPrty.Party_Name);
                        cmd.Parameters.AddWithValue("@Party_ID", gnmPrty.Party_ID);
                        cmd.Parameters.AddWithValue("@Party_PaymentTerms", gnmPrty.Party_PaymentTerms);
                        cmd.Parameters.AddWithValue("@Language_ID", Convert.ToInt32(PartyMasterSaveLocaleobj.UserLanguageID));

                        SqlParameter outputPartyId = new SqlParameter("@Party_IDout", SqlDbType.Int)
                        {
                            Direction = ParameterDirection.Output
                        };
                        cmd.Parameters.Add(outputPartyId);

                        cmd.ExecuteNonQuery();
                        gnmPrty.Party_ID = (int)outputPartyId.Value;
                    }
                    else
                    {
                        // Update existing record
                        cmd.CommandText = "sp_UpdatePartyLocale";
                        cmd.CommandType = CommandType.StoredProcedure;

                        cmd.Parameters.AddWithValue("@Party_Locale_ID", gnmPrty.Party_Locale_ID);
                        cmd.Parameters.AddWithValue("@Party_ID", gnmPrty.Party_ID);
                        cmd.Parameters.AddWithValue("@Party_Location", gnmPrty.Party_Location);
                        cmd.Parameters.AddWithValue("@Party_Name", gnmPrty.Party_Name);
                        cmd.Parameters.AddWithValue("@Party_PaymentTerms", gnmPrty.Party_PaymentTerms);

                        cmd.ExecuteNonQuery();
                    }
                }

                // Insert GPS details (this part remains unchanged)
                // gbl.InsertGPSDetails(Convert.ToInt32(PartyMasterSaveLocaleobj.Company_ID), Convert.ToInt32(PartyMasterSaveLocaleobj.Branch), Convert.ToInt32(PartyMasterSaveLocaleobj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartyMaster")), gnmPrty.Party_ID, 0, 0, "Update", false, Convert.ToInt32(PartyMasterSaveLocaleobj.MenuID));

                jsonResult = gnmPrty.Party_Locale_ID.ToString();
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonResult);
        }

        #endregion


        #region ::: PartyContactPersonSave Uday Kumar J B 21-08-2024:::
        /// <summary>
        /// to save Party Contact Person Details
        /// </summary>  
        public static IActionResult PartyContactPersonSave(string connString, PartyContactPersonSaveList PartyContactPersonSaveobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                GNM_PartyContactPersonDetails psd = null;
                GNM_PartyContactPersonDetails psdUpd = null;
                JObject jObj = JObject.Parse(PartyContactPersonSaveobj.key);
                int rowcount = jObj["rows"].Count();
                bool IsDefault = false;

                for (int i = 0; i < rowcount; i++)
                {
                    psd = jObj["rows"].ElementAt(i).ToObject<GNM_PartyContactPersonDetails>();

                    psd.PartyContactPerson_Department = Common.DecryptString(psd.PartyContactPerson_Department);
                    psd.PartyContactPerson_Email = Common.DecryptString(psd.PartyContactPerson_Email);

                    psd.PartyContactPerson_Name = Common.DecryptString(psd.PartyContactPerson_Name);
                    psd.PartyContactPerson_DOB = psd.PartyContactPerson_DOB;

                    psd.PartyContactPerson_Remarks = Common.DecryptString(psd.PartyContactPerson_Remarks);
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_InsertUpdate_AM_ERP_PartyContactPersonSaveParty";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@PartyContactPerson_ID", psd.PartyContactPerson_ID);
                                command.Parameters.AddWithValue("@Party_ID", psd.Party_ID);
                                command.Parameters.AddWithValue("@PartyContactPerson_Department", Common.DecryptString(psd.PartyContactPerson_Department));
                                command.Parameters.AddWithValue("@PartyContactPerson_Email", Common.DecryptString(psd.PartyContactPerson_Email));
                                command.Parameters.AddWithValue("@PartyContactPerson_Name", Common.DecryptString(psd.PartyContactPerson_Name));
                                command.Parameters.AddWithValue("@PartyContactPerson_DOB", psd.PartyContactPerson_DOB);
                                command.Parameters.AddWithValue("@PartyContactPerson_Remarks", Common.DecryptString(psd.PartyContactPerson_Remarks));
                                command.Parameters.AddWithValue("@Party_IsDefaultContact", psd.Party_IsDefaultContact);
                                command.Parameters.AddWithValue("@PartyContactPerson_IsActive", psd.PartyContactPerson_IsActive);
                                command.Parameters.AddWithValue("@PartyContactPerson_Mobile", psd.PartyContactPerson_Mobile);
                                command.Parameters.AddWithValue("@PartyContactPerson_Phone", psd.PartyContactPerson_Phone);
                                command.Parameters.AddWithValue("@Language_ID", psd.Language_ID);


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                command.ExecuteNonQuery();

                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                    // gbl.InsertGPSDetails(Convert.ToInt32(PartyContactPersonSaveobj.Company_ID), Convert.ToInt32(PartyContactPersonSaveobj.Branch), Convert.ToInt32(PartyContactPersonSaveobj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartyMaster")), Convert.ToInt32(psd.Party_ID), 0, 0, "Update", false, Convert.ToInt32(PartyContactPersonSaveobj.MenuID));
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }

            }
            return new JsonResult(false);
        }
        #endregion


        #region ::: PartyTaxSave Uday Kumar J B 21-08-2024:::
        /// <summary>
        /// to save Party Tax
        /// </summary>  
        public static IActionResult PartyTaxSave(string connString, PartyTaxSaveList PartyTaxSaveobj)
        {
            var jsonResult = default(dynamic);
            bool result = false;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                GNM_PartyTaxDetails psd = null;
                GNM_PartyTaxDetails psdUpd = null;
                JObject jObj = JObject.Parse(PartyTaxSaveobj.key);
                int rowcount = jObj["rows"].Count();
                int count = 0;

                for (int i = 0; i < rowcount; i++)
                {
                    psd = jObj["rows"].ElementAt(i).ToObject<GNM_PartyTaxDetails>();
                    psd.PartyTax_TaxCode = Common.DecryptString(psd.PartyTax_TaxCode);
                    psd.PartyTax_TaxCodeDescription = Common.DecryptString(psd.PartyTax_TaxCodeDescription);
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_InsertUpdate_AM_ERP_PartyTaxSaveParty";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@PartyTax_ID", psd.PartyTax_ID);
                                command.Parameters.AddWithValue("@Party_ID", psd.Party_ID);
                                command.Parameters.AddWithValue("@PartyTax_TaxCode", Uri.UnescapeDataString(Common.DecryptString(psd.PartyTax_TaxCode)));
                                command.Parameters.AddWithValue("@PartyTax_TaxCodeDescription", Uri.UnescapeDataString(Common.DecryptString(psd.PartyTax_TaxCodeDescription)));
                                command.Parameters.AddWithValue("@PartyTaxDetails_IsActive", psd.PartyTaxDetails_IsActive);

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                result = Convert.ToBoolean(command.ExecuteScalar());

                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }

                    if (result)
                    {
                        // gbl.InsertGPSDetails(Convert.ToInt32(PartyTaxSaveobj.Company_ID), Convert.ToInt32(PartyTaxSaveobj.Branch), Convert.ToInt32(PartyTaxSaveobj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartyMaster")), Convert.ToInt32(psd.Party_ID), 0, 0, "Update", false, Convert.ToInt32(PartyTaxSaveobj.MenuID));
                    }
                    jsonResult = new
                    {
                        IsSuccess = result,
                    };


                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    // //
                }
            }
            return new JsonResult(jsonResult);
        }
        #endregion


        #region ::: PartsMasterDelete Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///to delete PartsMaster row
        /// </summary> 
        public static IActionResult PartyMasterDelete(string connString, PartyMasterDeletelist PartyMasterDeleteobj)
        {
            string errorMsg = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JTokenReader jr = null;
                JObject jObj = JObject.Parse(PartyMasterDeleteobj.key);
                int rowcount = jObj["rows"].Count();
                int id = 0;
                for (int i = 0; i < rowcount; i++)
                {
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jr.Read();
                    id = Convert.ToInt32(jr.Value);
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_Delete_AM_ERP_PartyMasterDeleteParty";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@Party_ID", id);

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                command.ExecuteScalar();
                                // gbl.InsertGPSDetails(Convert.ToInt32(PartyMasterDeleteobj.Company_ID), Convert.ToInt32(PartyMasterDeleteobj.Branch), Convert.ToInt32(PartyMasterDeleteobj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartyMaster")), Convert.ToInt32(id), 0, 0, "Delete", false, Convert.ToInt32(PartyMasterDeleteobj.MenuID));
                                // errorMsg = Session["DeleteMessage"].ToString();
                                errorMsg = CommonFunctionalities.GetResourceString(PartyMasterDeleteobj.UserCulture.ToString(), "deletedsuccessfully").ToString();
                            }
                        }
                        catch (SqlException ex)
                        {
                            errorMsg = CommonFunctionalities.GetResourceString(PartyMasterDeleteobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                        }

                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }

                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    // //
                }
            }
            return new JsonResult(errorMsg);
        }
        #endregion


        #region ::: DeletePartyContactDetail Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///to delete PartsMaster row
        /// </summary>   
        public static IActionResult DeletePartyContactDetail(string connString, DeletePartyContactDetailList DeletePartyContactDetailobj)
        {
            string errorMsg = string.Empty;
            int partyId = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JTokenReader jr = null;
                JObject jObj = JObject.Parse(DeletePartyContactDetailobj.key);
                int rowcount = jObj["rows"].Count();
                int id = 0;
                for (int i = 0; i < rowcount; i++)
                {
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jr.Read();
                    id = Convert.ToInt32(jr.Value);
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_Delete_AM_ERP_DeletePartyContactDetailParty";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@PartyContactPerson_ID", id);
                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        partyId = reader.GetInt32(reader.GetOrdinal("PartyID"));
                                    }
                                }
                                //  gbl.InsertGPSDetails(Convert.ToInt32(DeletePartyContactDetailobj.Company_ID), Convert.ToInt32(DeletePartyContactDetailobj.Branch), Convert.ToInt32(DeletePartyContactDetailobj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartyMaster")), Convert.ToInt32(partyId), 0, 0, "Delete", false, Convert.ToInt32(DeletePartyContactDetailobj.MenuID));
                                errorMsg = CommonFunctionalities.GetResourceString(DeletePartyContactDetailobj.UserCulture.ToString(), "deletedsuccessfully").ToString();

                            }
                        }
                        catch (SqlException ex)
                        {
                            errorMsg = CommonFunctionalities.GetResourceString(DeletePartyContactDetailobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                        }

                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    // //
                }
            }
            return new JsonResult(errorMsg);
        }
        #endregion


        #region ::: DeletePartyTaxDetail Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///to delete PartsMaster row
        /// </summary>      
        public static IActionResult DeletePartyTaxDetail(string connString, DeletePartyTaxDetailList DeletePartyTaxDetailobj)
        {
            string errorMsg = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int partyId = 0;
                JTokenReader jr = null;
                JObject jObj = JObject.Parse(DeletePartyTaxDetailobj.key);
                int rowcount = jObj["rows"].Count();
                int id = 0;
                GNM_PartyTaxDetails PartyTax = null;

                for (int i = 0; i < rowcount; i++)
                {
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jr.Read();
                    id = Convert.ToInt32(jr.Value);
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_Delete_AM_ERP_DeletePartyTaxDetailParty";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@PartyTax_ID", id);
                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        partyId = reader.GetInt32(reader.GetOrdinal("PartyID"));
                                    }
                                }
                                // gbl.InsertGPSDetails(Convert.ToInt32(DeletePartyTaxDetailobj.Company_ID), Convert.ToInt32(DeletePartyTaxDetailobj.Branch), Convert.ToInt32(DeletePartyTaxDetailobj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartyMaster")), Convert.ToInt32(partyId), 0, 0, "Delete", false, Convert.ToInt32(DeletePartyTaxDetailobj.MenuID));
                                errorMsg = CommonFunctionalities.GetResourceString(DeletePartyTaxDetailobj.UserCulture.ToString(), "deletedsuccessfully").ToString();

                            }
                        }
                        catch (SqlException ex)
                        {
                            errorMsg = CommonFunctionalities.GetResourceString(DeletePartyTaxDetailobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                        }

                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    // //
                }
            }
            return new JsonResult(errorMsg);
        }
        #endregion


        #region ::: PartyTaxDiscountSave Uday Kumar J B 21-08-2024:::
        /// <summary>
        /// to save Party Tax
        /// </summary>  
        public static IActionResult PartyTaxDiscountSave(string connString, PartyTaxDiscountSaveList PartyTaxDiscountSaveobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jObj = JObject.Parse(PartyTaxDiscountSaveobj.TaxDiscountData);
                int rowcount = jObj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int i = 0; i < rowcount; i++)
                    {
                        GNM_PartyDiscount partyDis = jObj["rows"].ElementAt(i).ToObject<GNM_PartyDiscount>();

                        using (SqlCommand cmd = new SqlCommand("UP_InsUpd_AM_ERP_SavePartyTaxDiscount", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;

                            cmd.Parameters.AddWithValue("@PartyDiscount_ID", partyDis.PartyDiscount_ID);
                            cmd.Parameters.AddWithValue("@Party_ID", partyDis.Party_ID);
                            cmd.Parameters.AddWithValue("@Parts_Discount", (object)partyDis.Parts_Discount ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@Service_Discount", (object)partyDis.Service_Discount ?? DBNull.Value);
                            cmd.Parameters.AddWithValue("@Effective_Date", partyDis.Effective_Date);

                            cmd.ExecuteNonQuery();
                        }

                        // Assuming gbl.InsertGPSDetails is a method to log details which can also be converted to use ADO.NET if needed
                        //gbl.InsertGPSDetails(
                        //    Convert.ToInt32(PartyTaxDiscountSaveobj.Company_ID),
                        //    Convert.ToInt32(PartyTaxDiscountSaveobj.Branch),
                        //    Convert.ToInt32(PartyTaxDiscountSaveobj.User_ID),
                        //    Convert.ToInt32(Common.GetObjectID("CorePartyMaster")),
                        //    partyDis.Party_ID,
                        //    0,
                        //    0,
                        //    "Update",
                        //    false,
                        //    Convert.ToInt32(PartyTaxDiscountSaveobj.MenuID)
                        //);
                    }
                    conn.Close();
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    // // Redirect to error action if needed
                }
            }
            return new JsonResult(false);
        }
        #endregion


        #region ::: SelectPartyTaxDiscountDetails Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///SelectPartyTaxDetails
        /// </summary> 
        public static IActionResult SelectPartyTaxDiscountDetails(string connString, SelectPartyTaxDiscountDetailsList SelectPartyTaxDiscountDetailsobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            var jsonData = new object();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string ViewLabel = CommonFunctionalities.GetGlobalResourceObject(SelectPartyTaxDiscountDetailsobj.UserCulture.ToString(), "view").ToString();
            try
            {
                int value = Convert.ToInt32(SelectPartyTaxDiscountDetailsobj.value);
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    SqlCommand command = new SqlCommand("UP_Get_AM_ERP_SelectPartyTaxDiscountDetails", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    // Parameters
                    command.Parameters.AddWithValue("@PartyID", value);
                    command.Parameters.AddWithValue("@SortColumn", sidx);
                    command.Parameters.AddWithValue("@SortOrder", sord);
                    command.Parameters.AddWithValue("@Page", page);
                    command.Parameters.AddWithValue("@PageSize", rows);

                    connection.Open();
                    SqlDataReader reader = command.ExecuteReader();

                    List<PartyDiscount> discounts = new List<PartyDiscount>();
                    int totalCount = 0;

                    if (reader.HasRows)
                    {
                        while (reader.Read())
                        {
                            PartyDiscount discount = new PartyDiscount();
                            discount.edit = "<a title='" + ViewLabel + "' href='#' style='font-size: 13px;' id='" + reader["PartyDiscount_ID"] + "' key='" + reader["PartyDiscount_ID"] + "' class='editPartyTaxDiscountDetails' editmode='false' ><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>";
                            discount.delete = "<input type='checkbox' key='" + reader["PartyDiscount_ID"] + "'  id='chk" + reader["PartyDiscount_ID"] + "' class='DelPartyTaxDiscountDetails'/>";
                            discount.PartyDiscount_ID = Convert.ToInt32(reader["PartyDiscount_ID"]);
                            discount.Parts_Discount = Convert.ToDecimal(reader["Parts_Discount"]);
                            discount.Service_Discount = Convert.ToDecimal(reader["Service_Discount"]);
                            discount.Effective_Date = Convert.ToDateTime(reader["Effective_Date"]).ToString("dd-MMM-yyyy") == "01-Jan-0001" ? "" : Convert.ToDateTime(reader["Effective_Date"]).ToString("dd-MMM-yyyy");

                            discounts.Add(discount);
                        }
                    }

                    // Read total count from the second result set
                    reader.NextResult();
                    if (reader.Read())
                    {
                        totalCount = Convert.ToInt32(reader["TotalCount"]);
                    }

                    // Build jsonData object
                    jsonData = new
                    {
                        total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(totalCount) / Convert.ToDouble(rows))) : 0,
                        page = page,
                        records = totalCount,
                        rows = discounts
                    };

                    reader.Close();
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    // // Redirect to error action if needed
                }
            }

            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: DeletePartyTaxDiscount Uday Kumar J B 21-08-2024:::
        /// <summary>
        /// to Delete the Branch Tax Codes
        /// </summary>
        public static IActionResult DeletePartyTaxDiscount(string connString, DeletePartyTaxDiscountList DeletePartyTaxDiscountobj)
        {
            string errorMsg = "";
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int CompanyID = Convert.ToInt32(DeletePartyTaxDiscountobj.Company_ID);
                JObject jobj = JObject.Parse(DeletePartyTaxDiscountobj.key);
                int rowCount = jobj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int i = 0; i < rowCount; i++)
                    {
                        int id = Convert.ToInt32(jobj["rows"].ElementAt(i)["id"]);

                        using (SqlCommand cmd = new SqlCommand("UP_Del_AM_ERP_DeletePartyTaxDiscount", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@ID", id);

                            try
                            {
                                cmd.ExecuteNonQuery();
                                //  gbl.InsertGPSDetails(Convert.ToInt32(DeletePartyTaxDiscountobj.Company_ID), Convert.ToInt32(DeletePartyTaxDiscountobj.Branch), Convert.ToInt32(DeletePartyTaxDiscountobj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreCompanyMaster")), CompanyID, 0, 0, "Delete", false, Convert.ToInt32(DeletePartyTaxDiscountobj.MenuID));
                            }
                            catch (SqlException ex)
                            {
                                if (ex.Message.Contains("Dependency found, cannot delete the record"))
                                {
                                    errorMsg += CommonFunctionalities.GetResourceString(DeletePartyTaxDiscountobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeleterecord").ToString();
                                }
                                else
                                {
                                    throw;
                                }
                            }
                        }
                    }
                    conn.Close();
                    errorMsg += CommonFunctionalities.GetResourceString(DeletePartyTaxDiscountobj.UserCulture.ToString(), "deletedsuccessfully").ToString();
                }

            }
            catch (Exception ex)
            {
                errorMsg += CommonFunctionalities.GetGlobalResourceObject(DeletePartyTaxDiscountobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeleterecord").ToString();
            }
            return new JsonResult(errorMsg);
        }
        #endregion


        #region::: PartySegmentDetails Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///PartySegmentDetails  
        /// </summary>
        public static IActionResult SelectPartySegmentDetail(string connString, SelectPartySegmentDetailList SelectPartySegmentDetailobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var jsonData = default(dynamic);
            try
            {
                int Language_ID = Convert.ToInt32(SelectPartySegmentDetailobj.LanguageID);
                int count = 0;
                int total = 0;
                int value = Convert.ToInt32(SelectPartySegmentDetailobj.value);
                bool IsGeneral = Convert.ToBoolean(SelectPartySegmentDetailobj.IsGeneral);
                string ViewLabel = CommonFunctionalities.GetGlobalResourceObject(SelectPartySegmentDetailobj.UserCulture.ToString(), "view").ToString();

                IQueryable<PartySegmentDetail> iQPrtySpcl = null;
                IEnumerable<PartySegmentDetail> prtySpcl = null;

                List<PartySegmentDetail> details = new List<PartySegmentDetail>();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_SELECT_AM_ERP_SelectPartySegmentDetailParty";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@Party_ID", value);
                            command.Parameters.AddWithValue("@IsGeneral", IsGeneral);
                            command.Parameters.AddWithValue("@Language_ID", Language_ID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    PartySegmentDetail detail = new PartySegmentDetail
                                    {
                                        PartySegment_ID = reader.GetInt32(reader.GetOrdinal("PartySegment_ID")),
                                        PrimarySegment = reader.GetString(reader.GetOrdinal("PrimarySegment")),
                                        SecondarySegment = reader.GetString(reader.GetOrdinal("SecondarySegment")),
                                    };
                                    details.Add(detail);
                                }

                                prtySpcl = details.AsEnumerable();
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                iQPrtySpcl = prtySpcl.AsQueryable<PartySegmentDetail>();

                //FilterToolBar Search
                if (_search)
                {
                    Filters filtersObj = JObject.Parse(filters).ToObject<Filters>();
                    iQPrtySpcl = iQPrtySpcl.FilterSearch<PartySegmentDetail>(filtersObj);
                }
                //Advance Search
                else if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse(advnceFilters).ToObject<AdvanceFilter>();
                    iQPrtySpcl = iQPrtySpcl.AdvanceSearch<PartySegmentDetail>(advnfilter);
                }
                //Sorting 
                iQPrtySpcl = iQPrtySpcl.OrderByField<PartySegmentDetail>(sidx, sord);

                count = iQPrtySpcl.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                jsonData = new
                {
                    total = total,
                    page = page,
                    records = count,
                    rows = (from q in iQPrtySpcl.AsEnumerable()
                            select new
                            {
                                edit = "<a title='" + ViewLabel + "' href='#' style='font-size: 13px;'  key='" + q.PartySegment_ID + "'  ' class='PSEdit' editmode='false' ><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input key='" + q.PartySegment_ID + "' type='checkbox' defaultchecked='' class='PSdelete' />",
                                q.PartySegment_ID,
                                PrimarySegment = q.PrimarySegment,
                                SecondarySegment = q.SecondarySegment,
                                IsActive = q.IsActive == true ? "Yes" : "No"
                            }
                    )
                };
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion


        #region::: PartySegmentSave Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///PartySegmentSave  
        /// </summary>
        public static IActionResult PartySegmentSave(string connString, PartySegmentSaveList PartySegmentSaveobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                GNM_PartySegmentDetails psd = null;
                JObject jObj = JObject.Parse(PartySegmentSaveobj.key);
                int rowcount = jObj["rows"].Count();

                for (int i = 0; i < rowcount; i++)
                {
                    psd = jObj["rows"].ElementAt(i).ToObject<GNM_PartySegmentDetails>();

                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_InsertUpdate_AM_ERP_PartySegmentSaveParty";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@PartySegment_ID", psd.PartySegment_ID);
                                command.Parameters.AddWithValue("@PrimarySegment_ID", psd.PrimarySegment_ID);
                                command.Parameters.AddWithValue("@SecondarySegment_ID", psd.SecondarySegment_ID);
                                command.Parameters.AddWithValue("@Party_ID", psd.Party_ID);


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                command.ExecuteScalar();
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                    //  gbl.InsertGPSDetails(Convert.ToInt32(PartySegmentSaveobj.Company_ID), Convert.ToInt32(PartySegmentSaveobj.Branch), Convert.ToInt32(PartySegmentSaveobj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartyMaster")), psd.Party_ID, 0, 0, "Update", false, Convert.ToInt32(PartySegmentSaveobj.MenuID));
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);

        }

        #endregion


        #region::: PartySegmentDelete Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///PartySegmentDelete  
        /// </summary>
        public static IActionResult PartySegmentDelete(string connString, PartySegmentDeleteList PartySegmentDeleteobj)
        {
            string exMsg = string.Empty;
            int deletedPartyId = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                GNM_PartySegmentDetails gnmWF = null;
                JTokenReader jr = null;
                JObject jObj = JObject.Parse(PartySegmentDeleteobj.key);
                int rowcount = jObj["rows"].Count();
                int id = 0;
                for (int i = 0; i < rowcount; i++)
                {
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jr.Read();
                    id = Convert.ToInt32(jr.Value);
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_Delete_AM_ERP_PartySegmentDeleteParty";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@PartySegment_ID", id);
                                command.Parameters.Add("@DeletedPartyID", SqlDbType.Int);
                                command.Parameters["@DeletedPartyID"].Direction = ParameterDirection.Output;


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                command.ExecuteNonQuery();
                                deletedPartyId = Convert.ToInt32(command.Parameters["@DeletedPartyID"].Value);

                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                }

                //  gbl.InsertGPSDetails(Convert.ToInt32(PartySegmentDeleteobj.Company_ID), Convert.ToInt32(PartySegmentDeleteobj.Branch), Convert.ToInt32(PartySegmentDeleteobj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartyMaster")), Convert.ToInt32(deletedPartyId), 0, 0, "Delete", false, Convert.ToInt32(PartySegmentDeleteobj.MenuID));
                // exMsg = Session["DeleteMessage"].ToString();
            }
            catch (Exception ex)
            {

                exMsg += CommonFunctionalities.GetResourceString(PartySegmentDeleteobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();

            }
            return new JsonResult(exMsg);
        }
        #endregion


        #region::: PartyExport Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///PartyExport  
        /// </summary>
        //public void PartyExport(int exprtType, string Partytype)
        //{
        //    try
        //    {
        //        StateEntities stateClient = new StateEntities();
        //        ReferenceMasterEntities RefMasterClient = new ReferenceMasterEntities();
        //        CompanyEntities companyclient = new CompanyEntities();
        //        PartyMasterEntities prtyMstrEnts = new PartyMasterEntities();
        //        IQueryable<partydetails> Result = null;
        //        List<partydetails> iPurchaseOrderArray = new List<partydetails>();
        //        DataTable dt = new DataTable();
        //        int BranchID = Convert.ToInt32(Session["Branch"]);
        //        GNM_User User = (GNM_User)Session["UserDetails"];
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "CustomerAccountNumber").ToString());
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "Party").ToString()); //1
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "Location").ToString());//2
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "Address").ToString());//2
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "Phone").ToString());//3
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "Mobile").ToString());//4
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "IsActive").ToString());//5
        //                                                                                                                      //triveni
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "Email").ToString());//6
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "Fax").ToString());//7
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "Currency").ToString());//8
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "Country").ToString());//9
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "State").ToString());//10
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "PartyType").ToString());//11
        //                                                                                                                       //dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "PartsCreditLimit").ToString());//12
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "CreditLimitinCAD").ToString());//13
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "CreditLimitinUSD").ToString());//14
        //                                                                                                                              // dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "PaymentDueDays").ToString());//15
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "isForegin").ToString());//16
        //                                                                                                                       //dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "IsOEM").ToString());//17
        //                                                                                                                       //dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "IsDealer").ToString());//18
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "Company").ToString());//19
        //        dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "IsLock").ToString());//20
        //                                                                                                                    //dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "Branch").ToString());//21
        //                                                                                                                    //dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "CustomerType").ToString());//22
        //                                                                                                                    //dt.Columns.Add(HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "PaymentTerms").ToString());//22

        //        DataTable dt1 = new DataTable();
        //        dt1.Columns.Add("code");
        //        dt1.Columns.Add("Party");
        //        dt1.Columns.Add("Address");
        //        dt1.Columns.Add("Location");
        //        dt1.Columns.Add("Phone");
        //        dt1.Columns.Add("Mobile");
        //        dt1.Columns.Add("Is Active");
        //        //triveni
        //        dt1.Columns.Add("Email");
        //        dt1.Columns.Add("Fax");
        //        dt1.Columns.Add("Currency");
        //        dt1.Columns.Add("Country");
        //        dt1.Columns.Add("State");
        //        dt1.Columns.Add("PartyType");
        //        //dt1.Columns.Add("PartsCreditLimit");
        //        dt1.Columns.Add("CreditLimitinCAD");
        //        dt1.Columns.Add("CreditLimitinUSD");
        //        //dt1.Columns.Add("PaymentDueDays");
        //        dt1.Columns.Add("isForegin");
        //        //dt1.Columns.Add("IsOEM");
        //        //dt1.Columns.Add("IsDealer");
        //        dt1.Columns.Add("Company");
        //        dt1.Columns.Add("IsLock");
        //        //dt1.Columns.Add("Branch");
        //        //dt1.Columns.Add("CustomerType");
        //        //dt1.Columns.Add("PaymentTerms");
        //        //end
        //        dt1.Rows.Add(0, 0, 0, 0, 2, 2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
        //        //List<partydetails> PartyArraryArray = new List<partydetails>();
        //        Result = GetPartyMaster(Request.Params["sidx"].ToString(), Request.Params["sord"].ToString(), Convert.ToInt32(Request.Params["page"].ToString()), 1000000, Request.Params["Search"].ToString(), Request.Params["filter"].ToString(), Request.Params["advnce"].ToString(), Request.Params["advanceFilter"].ToString(), Convert.ToInt32(Request.Params["partyTypeID"].ToString()));

        //        if (Request.Params["filter"].ToString() != "null")
        //        {
        //            Filters filters = JObject.Parse(Common.DecryptString(Request.Params["filter"])).ToObject<Filters>();
        //            if (filters.rules.Count() > 0)
        //            {
        //                Result = Result.FilterSearch<partydetails>(filters);
        //            }
        //        }
        //        if (Request.Params["advanceFilter"].ToString() != "null")
        //        {
        //            AdvanceFilter advnfilter = JObject.Parse(Request.Params["advanceFilter"]).ToObject<AdvanceFilter>();
        //            Result = Result.AdvanceSearch<partydetails>(advnfilter);
        //        }
        //        Result = Result.OrderByField<partydetails>(Request.Params["sidx"].ToString(), Request.Params["sord"].ToString());
        //        iPurchaseOrderArray = Result.ToList();
        //        int count = iPurchaseOrderArray.Count();
        //        for (int i = 0; i < count; i++)
        //        {
        //            //dt.Rows.Add(PartyArraryArray.ElementAt(i).Party_Name, PartyArraryArray.ElementAt(i).Party_Location, PartyArraryArray.ElementAt(i).Party_Phone, PartyArraryArray.ElementAt(i).Party_Mobile, (PartyArraryArray.ElementAt(i).Party_IsActive == true ? "Yes" : "No"), PartyArraryArray.ElementAt(i).Party_Email, PartyArraryArray.ElementAt(i).Party_Fax, PartyArraryArray.ElementAt(i).Currency, PartyArraryArray.ElementAt(i).Country, PartyArraryArray.ElementAt(i).State, PartyArraryArray.ElementAt(i).PartyTypestring, PartyArraryArray.ElementAt(i).PartsCreditLimit, PartyArraryArray.ElementAt(i).ServiceCreditLimit, PartyArraryArray.ElementAt(i).SalesCreditLimit, PartyArraryArray.ElementAt(i).PaymentDueDays, PartyArraryArray.ElementAt(i).Isforeign, PartyArraryArray.ElementAt(i).IsDealerString, PartyArraryArray.ElementAt(i).Company, PartyArraryArray.ElementAt(i).IslockString, "", PartyArraryArray.ElementAt(i).Party_PaymentTerms);                         
        //            dt.Rows.Add(iPurchaseOrderArray.ElementAt(i).Party_Code, iPurchaseOrderArray.ElementAt(i).Party_Name, iPurchaseOrderArray.ElementAt(i).Party_Location, iPurchaseOrderArray.ElementAt(i).PartyAddress_Address, iPurchaseOrderArray.ElementAt(i).Party_Phone, iPurchaseOrderArray.ElementAt(i).Party_Mobile, iPurchaseOrderArray.ElementAt(i).Party_IsActive == true ? "Yes" : "No", iPurchaseOrderArray.ElementAt(i).Party_Email, iPurchaseOrderArray.ElementAt(i).Party_Fax, iPurchaseOrderArray.ElementAt(i).Currency, (iPurchaseOrderArray.ElementAt(i).Country), (iPurchaseOrderArray.ElementAt(i).State), (iPurchaseOrderArray.ElementAt(i).PartyType == 1 ? "Customer" : iPurchaseOrderArray.ElementAt(i).PartyType == 2 ? "Prospect" : iPurchaseOrderArray.ElementAt(i).PartyType == 3 ? "Partner" : iPurchaseOrderArray.ElementAt(i).PartyType == 4 ? "Supplier" : iPurchaseOrderArray.ElementAt(i).PartyType == 5 ? "ClearingAgent" : iPurchaseOrderArray.ElementAt(i).PartyType == 6 ? "Transporter" : iPurchaseOrderArray.ElementAt(i).PartyType == 7 ? "Insurance" : iPurchaseOrderArray.ElementAt(i).PartyType == 8 ? "Financier" : iPurchaseOrderArray.ElementAt(i).PartyType == 10 ? "Vendor" : iPurchaseOrderArray.ElementAt(i).PartyType == 11 ? "Contractor" : "User"), iPurchaseOrderArray.ElementAt(i).ServiceCreditLimit, iPurchaseOrderArray.ElementAt(i).SalesCreditLimit, iPurchaseOrderArray.ElementAt(i).IsImportExport == true ? "Yes" : "No", iPurchaseOrderArray.ElementAt(i).Company, iPurchaseOrderArray.ElementAt(i).Party_IsLocked == true ? "Yes" : "No");//iPurchaseOrderArray.ElementAt(i).IsOEM == true ? "Yes" : "No", iPurchaseOrderArray.ElementAt(i).IsDealer == true ? "Yes" : "No",iPurchaseOrderArray.ElementAt(i).PaymentDueDays,iPurchaseOrderArray.ElementAt(i).CustomerType == 0 ? "External" :iPurchaseOrderArray.ElementAt(i).CustomerType==1? "Internal":"",iPurchaseOrderArray.ElementAt(i).Party_PaymentTerms
        //        }
        //        DataTable DtCriteria = new DataTable();
        //        ReportExport.Export(exprtType, dt, DtCriteria, dt1, Partytype, Partytype);
        //        gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"].ToString()), BranchID, User.User_ID, Common.GetObjectID("CorePartyMaster"), 0, 0, 0, "Party-Export", false, Convert.ToInt32(Session["MenuID"]), Convert.ToDateTime(Session["LoggedINDateTime"]));
        //    }
        //    catch (Exception ex)
        //    {
        //        if (LogException == 0)
        //        {
        //            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
        //        }
        //        // RedirectToAction("Error");
        //    }
        //}
        #endregion


        #region::: SelectPartySkillSet Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///SelectPartySkillSet  
        /// </summary>
        public static IActionResult SelectPartySkillSet(string connString, SelectPartySkillSetList SelectPartySkillSetobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            var jsonData = default(dynamic);
            int count = 0;
            int total = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                string ViewLabel = CommonFunctionalities.GetGlobalResourceObject(SelectPartySkillSetobj.UserCulture.ToString(), "view").ToString();
                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (var cmd = new SqlCommand("UP_Sel_AM_ERP_SelectPartySkillSet", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;

                        // Set parameters
                        cmd.Parameters.AddWithValue("@value", Convert.ToInt32(SelectPartySkillSetobj.value));
                        cmd.Parameters.AddWithValue("@IsGeneral", Convert.ToBoolean(SelectPartySkillSetobj.IsGeneral));
                        cmd.Parameters.AddWithValue("@pageIndex", page);
                        cmd.Parameters.AddWithValue("@pageSize", rows);
                        cmd.Parameters.AddWithValue("@sortColumn", sidx);
                        cmd.Parameters.AddWithValue("@sortOrder", sord);

                        // Execute reader
                        using (var reader = cmd.ExecuteReader())
                        {
                            // Read results
                            if (reader.Read())
                            {
                                total = Convert.ToInt32(reader["TotalRecords"]);
                            }

                            var rowsList = new List<object>();
                            while (reader.Read())
                            {
                                var row = new
                                {
                                    edit = "<a title='" + ViewLabel + "' href='#' style='font-size: 13px;'  key='" + reader["Party_Skillset_ID"] + "' class='edtPrtySkllSt' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                    delete = "<input key='" + reader["Party_Skillset_ID"] + "' type='checkbox' defaultchecked='' class='delPrtySkllSt' />",
                                    Party_Skillset_ID = reader["Party_Skillset_ID"],
                                    SkillSet = reader.GetString(reader.GetOrdinal("SkillSet")),
                                    Party_Skillset_Rating = reader["Party_Skillset_Rating"]
                                };
                                rowsList.Add(row);
                            }

                            jsonData = new
                            {
                                total = total,
                                page = page,
                                records = count,
                                rows = rowsList
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }
        #endregion


        #region::: SavePartySkillSet Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///SavePartySkillSet  
        /// </summary>
        public static IActionResult SavePartySkillSet(string connString, SavePartySkillSetList SavePartySkillSetobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    JObject jObj = JObject.Parse(SavePartySkillSetobj.key);
                    int rowCount = jObj["rows"].Count();

                    for (int i = 0; i < rowCount; i++)
                    {
                        GNM_PartySkillset gnmPrtySpcl = jObj["rows"][i].ToObject<GNM_PartySkillset>();
                        int partySkillsetID = gnmPrtySpcl.Party_Skillset_ID;
                        int skillsetID = gnmPrtySpcl.Skillset_ID;
                        int partySkillsetRating = gnmPrtySpcl.Party_Skillset_Rating;
                        int partyID = gnmPrtySpcl.Party_ID;

                        using (SqlCommand cmd = new SqlCommand("UP_InsUpd_AM_ERP_SavePartySkillset", connection))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;

                            cmd.Parameters.AddWithValue("@Party_Skillset_ID", partySkillsetID);
                            cmd.Parameters.AddWithValue("@Skillset_ID", skillsetID);
                            cmd.Parameters.AddWithValue("@Party_Skillset_Rating", partySkillsetRating);
                            cmd.Parameters.AddWithValue("@Party_ID", partyID);

                            cmd.ExecuteNonQuery();
                        }
                        // gbl.InsertGPSDetails(Convert.ToInt32(SavePartySkillSetobj.Company_ID), Convert.ToInt32(SavePartySkillSetobj.Branch), Convert.ToInt32(SavePartySkillSetobj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartyMaster")), gnmPrtySpcl.Party_ID, 0, 0, "Update", false, Convert.ToInt32(SavePartySkillSetobj.MenuID));
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(null);
        }
        #endregion


        #region::: SavePartyContractDtl Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///SavePartyContractDtl  
        /// </summary>
        public static IActionResult SavePartyContractDtl(string connString, SavePartyContractDtlList SavePartyContractDtlobj)
        {
            SqlConnection connection = new SqlConnection(connString);
            SqlCommand command = new SqlCommand("UP_InsUpd_AM_ERP_SavePartyContractDetails", connection);
            command.CommandType = CommandType.StoredProcedure;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                connection.Open();
                JObject jObj = JObject.Parse(SavePartyContractDtlobj.key);
                int rowCount = jObj["rows"].Count();

                for (int i = 0; i < rowCount; i++)
                {
                    GNM_PARTYCONTRACTDETAILS gnmPrtycontract = jObj["rows"].ElementAt(i).ToObject<GNM_PARTYCONTRACTDETAILS>();
                    gnmPrtycontract.Remarks = Uri.UnescapeDataString(Common.DecryptString(gnmPrtycontract.Remarks));

                    command.Parameters.Clear();
                    command.Parameters.AddWithValue("@PartyContractID", gnmPrtycontract.PartyContract_ID);
                    command.Parameters.AddWithValue("@PartyID", gnmPrtycontract.Party_ID);
                    command.Parameters.AddWithValue("@AgreementNumber", Uri.UnescapeDataString(gnmPrtycontract.AgreementNumber));
                    command.Parameters.AddWithValue("@FromDate", gnmPrtycontract.FromDate);
                    command.Parameters.AddWithValue("@ToDate", gnmPrtycontract.ToDate);
                    command.Parameters.AddWithValue("@ContractValue", gnmPrtycontract.ContractValue);
                    command.Parameters.AddWithValue("@Unit", gnmPrtycontract.Unit);
                    command.Parameters.AddWithValue("@Currency", gnmPrtycontract.Currency);
                    command.Parameters.AddWithValue("@Remarks", gnmPrtycontract.Remarks);

                    command.ExecuteNonQuery();
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            finally
            {
                connection.Close();
            }
            return new JsonResult(false);
        }
        #endregion


        #region::: DeletePartySkillSet Uday Kumar J B 21-08-2024:::
        /// <summary>
        ///DeletePartySkillSet  
        /// </summary>
        public static IActionResult DeletePartySkillSet(string connString, DeletePartySkillSetList DeletePartySkillSetobj)
        {
            string errorMsg = "";
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int CompanyID = Convert.ToInt32(DeletePartySkillSetobj.Company_ID);
                JObject jobj = JObject.Parse(DeletePartySkillSetobj.key);
                int rowCount = jobj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int i = 0; i < rowCount; i++)
                    {
                        int id = Convert.ToInt32(jobj["rows"].ElementAt(i)["id"]);

                        using (SqlCommand cmd = new SqlCommand("UP_Del_AM_ERP_DeletePartySkillset", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@ID", id);

                            try
                            {
                                cmd.ExecuteNonQuery();
                                //  gbl.InsertGPSDetails(Convert.ToInt32(DeletePartySkillSetobj.Company_ID), Convert.ToInt32(DeletePartySkillSetobj.Branch), Convert.ToInt32(DeletePartySkillSetobj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreCompanyMaster")), CompanyID, 0, 0, "Delete", false, Convert.ToInt32(DeletePartySkillSetobj.MenuID));
                            }
                            catch (SqlException ex)
                            {
                                if (ex.Message.Contains("Dependency found, cannot delete the record"))
                                {
                                    errorMsg += CommonFunctionalities.GetGlobalResourceObject(DeletePartySkillSetobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeleterecord").ToString();
                                }
                                else
                                {
                                    throw;
                                }
                            }
                        }
                    }
                    conn.Close();
                    errorMsg += CommonFunctionalities.GetGlobalResourceObject(DeletePartySkillSetobj.UserCulture.ToString(), "deletedsuccessfully").ToString();
                }

            }
            catch (Exception ex)
            {
                errorMsg += CommonFunctionalities.GetGlobalResourceObject(DeletePartySkillSetobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeleterecord").ToString();
            }
            return new JsonResult(errorMsg);
        }
        #endregion


        #region::: DeletePartyContractDtl Uday Kumar J B 22-08-2024:::
        /// <summary>
        ///DeletePartyContractDtl  
        /// </summary>
        public static IActionResult DeletePartyContractDtl(string connString, DeletePartyContractDtlList DeletePartyContractDtlobj)
        {
            string exMsg = string.Empty;
            JObject jobj = JObject.Parse(DeletePartyContractDtlobj.key);
            int rowCount = jobj["rows"].Count();
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int i = 0; i < rowCount; i++)
                    {
                        int id = Convert.ToInt32(jobj["rows"].ElementAt(i)["id"]);

                        using (SqlCommand cmd = new SqlCommand("UP_Del_AM_ERP_DeletePartyContractDtl", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@ID", id);

                            try
                            {
                                cmd.ExecuteNonQuery();
                                //  gbl.InsertGPSDetails(Convert.ToInt32(DeletePartyContractDtlobj.Company_ID, Convert.ToInt32(DeletePartyContractDtlobj.Branch), Convert.ToInt32(DeletePartyContractDtlobj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartyMaster")), id, 0, 0, "Delete", false, Convert.ToInt32(DeletePartyContractDtlobj.MenuID));
                            }
                            catch (SqlException ex)
                            {
                                if (ex.Message.Contains("Dependency found, cannot delete the record"))
                                {
                                    exMsg += CommonFunctionalities.GetResourceString(DeletePartyContractDtlobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeleterecord").ToString();
                                }
                                else
                                {
                                    throw;
                                }
                            }
                        }
                    }
                    conn.Close();
                    exMsg += CommonFunctionalities.GetResourceString(DeletePartyContractDtlobj.UserCulture.ToString(), "deletedsuccessfully").ToString();
                }
            }
            catch (Exception ex)
            {
                exMsg = ex.Message;

                // Handle specific exceptions if needed
                if (ex.InnerException is SqlException sqlEx && sqlEx.Number == 547) // Foreign key constraint violation
                {
                    exMsg += CommonFunctionalities.GetGlobalResourceObject(DeletePartyContractDtlobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
            }

            return new JsonResult(exMsg);
        }
        #endregion


        #region::: SelectPartyBranch Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectPartyBranch  
        /// </summary>
        public static IActionResult SelectPartyBranch(string connString, SelectPartyBranchList SelectPartyBranchobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filtersJson, bool advnce, string advnceFilters)
        {
            var jsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int count = 0;
                int total = 0;
                int value = Convert.ToInt32(SelectPartyBranchobj.value);
                bool IsGeneral = Convert.ToBoolean(SelectPartyBranchobj.IsGeneral);
                int Language_ID = Convert.ToInt32(SelectPartyBranchobj.LanguageID);
                List<PartyBranch> partyBranches = new List<PartyBranch>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_Select_AM_ERP_SelectPartyBranchParty";

                    using (SqlCommand command = new SqlCommand(query, conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@Party_ID", value);
                        command.Parameters.AddWithValue("@Language_ID", Language_ID);
                        command.Parameters.AddWithValue("@IsGeneral", IsGeneral);

                        conn.Open();
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                PartyBranch branch = new PartyBranch();
                                branch.PartyBranch_ID = Convert.ToInt32(reader["PartyBranch_ID"]);
                                branch.Branch = Convert.ToString(reader["Branch"]);
                                partyBranches.Add(branch);
                            }
                        }
                    }
                }

                var iQPrtyBrnch = partyBranches.AsQueryable();

                // FilterToolBar Search
                if (_search && !string.IsNullOrEmpty(filtersJson))
                {
                    Filters filters = JObject.Parse(filtersJson).ToObject<Filters>();
                    iQPrtyBrnch = iQPrtyBrnch.FilterSearch<PartyBranch>(filters);
                }
                // Advance Search
                else if (advnce && !string.IsNullOrEmpty(advnceFilters))
                {
                    AdvanceFilter advnfilter = JObject.Parse(advnceFilters).ToObject<AdvanceFilter>();
                    iQPrtyBrnch = iQPrtyBrnch.AdvanceSearch<PartyBranch>(advnfilter);
                }

                // Sorting 
                iQPrtyBrnch = iQPrtyBrnch.OrderByField<PartyBranch>(sidx, sord);

                count = iQPrtyBrnch.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                jsonData = new
                {
                    total = total,
                    page = page,
                    records = count,
                    rows = (from q in iQPrtyBrnch
                            select new
                            {
                                edit = "<a title='View' href='#' style='font-size: 13px;' key='" + q.PartyBranch_ID + "' class='edtPrtyBrnch' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input key='" + q.PartyBranch_ID + "' type='checkbox' defaultchecked='' class='delPrtyBrnch' />",
                                q.PartyBranch_ID,
                                Branch = q.Branch
                            })
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion


        #region::: SavePartyBranch Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SavePartyBranch  
        /// </summary>
        public static IActionResult SavePartyBranch(string connString, SavePartyBranchList SavePartyBranchobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                PartyBranch prtyBrnch = null;
                GNM_PartyBranchAssociation gnmPrtyBrnch = null;
                JObject jObj = JObject.Parse(SavePartyBranchobj.key);

                int rowcount = jObj["rows"].Count();
                for (int i = 0; i < rowcount; i++)
                {
                    prtyBrnch = jObj["rows"].ElementAt(i).ToObject<PartyBranch>();

                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_InsertUpdate_AM_ERP_SelectPartyBranchParty";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@PartyBranch_ID", prtyBrnch.PartyBranch_ID == 0 ? (object)DBNull.Value : prtyBrnch.PartyBranch_ID);
                                command.Parameters.AddWithValue("@Branch_ID", prtyBrnch.Branch_ID);
                                command.Parameters.AddWithValue("@Party_ID", prtyBrnch.Party_ID);


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                var result = command.ExecuteScalar();

                            }
                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                    //  gbl.InsertGPSDetails(Convert.ToInt32(SavePartyBranchobj.Company_ID), Convert.ToInt32(SavePartyBranchobj.Branch), Convert.ToInt32(SavePartyBranchobj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartyMaster")), prtyBrnch.Party_ID, 0, 0, "Update", false, Convert.ToInt32(SavePartyBranchobj.MenuID));
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(null);
        }
        #endregion


        #region::: DeletePartyBranch Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// DeletePartyBranch  
        /// </summary>
        public static IActionResult DeletePartyBranch(string connString, DeletePartyBranchList DeletePartyBranchobj)
        {
            string exMsg = string.Empty;
            string result = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                GNM_PartyBranchAssociation gnmPrtySpcl = null;
                JTokenReader jr = null;
                JObject jObj = JObject.Parse(DeletePartyBranchobj.key);
                int rowcount = jObj["rows"].Count();
                int id = 0;
                for (int i = 0; i < rowcount; i++)
                {
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jr.Read();
                    id = Convert.ToInt32(jr.Value);
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_Delete_AM_ERP_DeletePartyBranchParty";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@PartyBranch_ID", id);


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                object partyId = command.ExecuteScalar();
                                if (partyId != null)
                                {
                                    result = partyId.ToString();
                                }

                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                }

                // gbl.InsertGPSDetails(Convert.ToInt32(DeletePartyBranchobj.Company_ID), Convert.ToInt32(DeletePartyBranchobj.Branch), Convert.ToInt32(DeletePartyBranchobj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartyMaster")), Convert.ToInt32(result), 0, 0, "Delete", false, Convert.ToInt32(DeletePartyBranchobj.MenuID));
                exMsg = CommonFunctionalities.GetResourceString(DeletePartyBranchobj.UserCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (Exception ex)
            {

                exMsg += CommonFunctionalities.GetResourceString(DeletePartyBranchobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();

            }
            return new JsonResult(exMsg);
        }
        #endregion


        #region::: SelectPartyProductAssociation Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectPartyProductAssociation  
        /// </summary>
        public static IActionResult SelectPartyProductAssociation(string connString, SelectPartyProductAssociationList SelectPartyProductAssociationobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filtersJson, bool advnce, string advnceFilters)
        {
            dynamic jsonData = new { };
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int count = 0;
                int total = 0;
                int value = Convert.ToInt32(SelectPartyProductAssociationobj.value);
                bool IsGeneral = Convert.ToBoolean(SelectPartyProductAssociationobj.IsGeneral);
                int CompanyID = Convert.ToInt32(SelectPartyProductAssociationobj.Company_ID);
                string ViewLabel = CommonFunctionalities.GetGlobalResourceObject(SelectPartyProductAssociationobj.UserCulture.ToString(), "view").ToString();

                DataTable dt = new DataTable();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("UP_Sel_AM_ERP_SelectPartyProductAssociation", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@value", value);
                        cmd.Parameters.AddWithValue("@IsGeneral", IsGeneral);
                        cmd.Parameters.AddWithValue("@CompanyID", CompanyID);

                        if (!IsGeneral)
                        {
                            int Language_ID = Convert.ToInt32(SelectPartyProductAssociationobj.LanguageID);
                            cmd.Parameters.AddWithValue("@Language_ID", Language_ID);
                        }

                        SqlDataAdapter da = new SqlDataAdapter(cmd);
                        da.Fill(dt);
                    }
                }

                // Convert DataTable to List of PartyProductAssociation
                var prtyProdAss = dt.AsEnumerable().Select(row => new PartyProductAssociation
                {
                    PartyProduct_ID = row.Field<int>("PartyProduct_ID"),
                    Brand = row.Field<string>("Brand"),
                    Model = row.Field<string>("Model"),
                    ProductType = row.Field<string>("ProductType"),
                    BrandID = row.Field<int>("BrandID"),
                    ModelID = row.Field<int>("ModelID"),
                    ProductTypeID = row.Field<int>("ProductTypeID")
                }).AsQueryable();

                // FilterToolBar Search
                if (_search)
                {
                    if (!string.IsNullOrEmpty(filtersJson))
                    {
                        Filters filters = JObject.Parse(filtersJson).ToObject<Filters>();
                        prtyProdAss = prtyProdAss.FilterSearch<PartyProductAssociation>(filters);
                    }
                }
                // Advance Search
                else if (advnce)
                {
                    if (!string.IsNullOrEmpty(advnceFilters))
                    {
                        AdvanceFilter advnfilter = JObject.Parse(advnceFilters).ToObject<AdvanceFilter>();
                        prtyProdAss = prtyProdAss.AdvanceSearch<PartyProductAssociation>(advnfilter);
                    }
                }

                // Sorting
                prtyProdAss = prtyProdAss.OrderByField<PartyProductAssociation>(sidx, sord);

                count = prtyProdAss.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                jsonData = new
                {
                    total = total,
                    page = page,
                    records = count,
                    rows = (from q in prtyProdAss
                            select new
                            {
                                edit = "<a title='" + ViewLabel + "' href='#' style='font-size: 13px;' key='" + q.PartyProduct_ID + "' class='edtPrtyProdAss' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input key='" + q.PartyProduct_ID + "' type='checkbox' defaultchecked='' class='delPrtyProdAss' />",
                                q.PartyProduct_ID,
                                Brand = q.Brand,
                                ProductType = q.ProductType,
                                Model = q.Model,
                                q.BrandID,
                                q.ModelID,
                                q.ProductTypeID
                            }).ToList() // .Paginate(page, rows) // Uncomment if pagination is needed
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion


        #region::: SelectPartyProductAssociation Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectPartyProductAssociation  
        /// </summary>
        public static IActionResult SavePartyProductAssociation(string connString, SavePartyProductAssociationList SavePartyProductAssociationobj)
        {
            var jsonData = new { IsExists = false, rowsIndex = "" }; // Initialize default return data
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();
                    SqlCommand command = connection.CreateCommand();
                    command.CommandType = CommandType.StoredProcedure;
                    command.CommandText = "UP_InsUpd_AM_ERP_SavePartyProductAssociation"; // Replace with your actual stored procedure name

                    JObject jObj = JObject.Parse(SavePartyProductAssociationobj.key);
                    int rowcount = jObj["rows"].Count();
                    List<int> rowIndex = new List<int>();

                    for (int i = 0; i < rowcount; i++)
                    {
                        GNM_PartyProductAssociation gnmPrtyBrnch = jObj["rows"].ElementAt(i).ToObject<GNM_PartyProductAssociation>();

                        // Add parameters to the command
                        command.Parameters.Clear();
                        command.Parameters.AddWithValue("@Party_ID", gnmPrtyBrnch.Party_ID);
                        command.Parameters.AddWithValue("@Brand_ID", gnmPrtyBrnch.Brand_ID);
                        command.Parameters.AddWithValue("@ProductType_ID", gnmPrtyBrnch.ProductType_ID ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Model_ID", gnmPrtyBrnch.Model_ID ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@PartyProduct_ID", gnmPrtyBrnch.PartyProduct_ID);

                        SqlParameter returnParameter = command.Parameters.Add("@Result", SqlDbType.Int);
                        returnParameter.Direction = ParameterDirection.Output;

                        // Execute the stored procedure
                        command.ExecuteNonQuery();

                        int result = (int)returnParameter.Value;

                        if (result == 1)
                        {
                            rowIndex.Add(i);
                        }

                        // gbl.InsertGPSDetails(Convert.ToInt32(SavePartyProductAssociationobj.Company_ID), Convert.ToInt32(SavePartyProductAssociationobj.Branch), Convert.ToInt32(SavePartyProductAssociationobj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartyMaster")), gnmPrtyBrnch.Party_ID, 0, 0, "Update", false, Convert.ToInt32(SavePartyProductAssociationobj.MenuID));
                    }

                    if (rowIndex.Count > 0)
                    {
                        jsonData = new { IsExists = true, rowsIndex = string.Join(",", rowIndex) };
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle exceptions or log errors
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }
        #endregion


        #region::: DeletePartyProductAssociation Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// DeletePartyProductAssociation  
        /// </summary>
        public static IActionResult DeletePartyProductAssociation(string connString, DeletePartyProductAssociationList DeletePartyProductAssociationobj)
        {
            string exMsg = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int CompanyID = Convert.ToInt32(DeletePartyProductAssociationobj.Company_ID);
                JObject jobj = JObject.Parse(DeletePartyProductAssociationobj.key);
                int rowCount = jobj["rows"].Count();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int i = 0; i < rowCount; i++)
                    {
                        int id = Convert.ToInt32(jobj["rows"].ElementAt(i)["id"]);

                        using (SqlCommand cmd = new SqlCommand("UP_InsUpd_AM_ERP_DeletePartyProductAssociationSP", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@ID", id);

                            try
                            {
                                cmd.ExecuteNonQuery();
                                // gbl.InsertGPSDetails(Convert.ToInt32(DeletePartyProductAssociationobj.Company_ID), Convert.ToInt32(DeletePartyProductAssociationobj.Branch), Convert.ToInt32(DeletePartyProductAssociationobj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartyMaster")), id, 0, 0, "Delete", false, Convert.ToInt32(DeletePartyProductAssociationobj.MenuID));
                            }
                            catch (SqlException ex)
                            {
                                if (ex.Message.Contains("Dependency found, cannot delete the record"))
                                {
                                    exMsg += CommonFunctionalities.GetGlobalResourceObject(DeletePartyProductAssociationobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeleterecord").ToString();
                                }
                                else
                                {
                                    throw;
                                }
                            }
                        }
                    }
                    conn.Close();
                    exMsg = CommonFunctionalities.GetGlobalResourceObject(DeletePartyProductAssociationobj.UserCulture.ToString(), "deletedsuccessfully").ToString();
                }

            }
            catch (Exception ex)
            {
                exMsg += CommonFunctionalities.GetGlobalResourceObject(DeletePartyProductAssociationobj.GeneralCulture.ToString(), "Dependencyfoundcannotdeleterecord").ToString();
            }
            return new JsonResult(exMsg);
        }
        #endregion


        #region::: SelectPartyServiceSchedule Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectPartyServiceSchedule  
        /// </summary>
        public static IActionResult SelectPartyServiceSchedule(string connString, SelectPartyServiceScheduleList SelectPartyServiceScheduleobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filtersJson, bool advnce, string advnceFilters)
        {
            var jsonData = default(dynamic);
            int count = 0;
            int total = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                string ViewLabel = CommonFunctionalities.GetGlobalResourceObject(SelectPartyServiceScheduleobj.UserCulture.ToString(), "view").ToString();
                int value = Convert.ToInt32(SelectPartyServiceScheduleobj.value);
                bool IsGeneral = Convert.ToBoolean(SelectPartyServiceScheduleobj.IsGeneral);
                int CompanyID = Convert.ToInt32(SelectPartyServiceScheduleobj.Company_ID);
                int Language_ID = Convert.ToInt32(SelectPartyServiceScheduleobj.LanguageID);
                string Closed = CommonFunctionalities.GetResourceString(SelectPartyServiceScheduleobj.GeneralCulture.ToString(), "Closed").ToString();
                string Open = CommonFunctionalities.GetResourceString(SelectPartyServiceScheduleobj.GeneralCulture.ToString(), "Open").ToString();
                string ServiceScheduleStatus = "-1:--" + CommonFunctionalities.GetResourceString(SelectPartyServiceScheduleobj.GeneralCulture.ToString(), "select").ToString() + "--;";
                ServiceScheduleStatus = ServiceScheduleStatus + "1" + ":" + Closed + ";" + "0" + ":" + Open + ";";
                ServiceScheduleStatus = ServiceScheduleStatus.TrimEnd(new char[] { ';' });

                List<PartyServiceSchedule> result = new List<PartyServiceSchedule>();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_Select_AM_ERP_SelectPartyServiceScheduleParty";

                    using (SqlCommand command = new SqlCommand(query, conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@Party_ID", value);
                        command.Parameters.AddWithValue("@CompanyID", CompanyID);
                        command.Parameters.AddWithValue("@IsGeneral", IsGeneral);
                        command.Parameters.AddWithValue("@Language_ID", Language_ID);

                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                PartyServiceSchedule serviceSchedule = new PartyServiceSchedule
                                {
                                    Closure_Reason = reader["Closure_Reason"] != DBNull.Value ? reader["Closure_Reason"].ToString() : string.Empty,
                                    PartyServiceSchedule_ID = reader["PartyServiceSchedule_ID"] != DBNull.Value ? Convert.ToInt32(reader["PartyServiceSchedule_ID"]) : 0,
                                    ServiceType = reader["ServiceType"] != DBNull.Value ? reader["ServiceType"].ToString() : string.Empty,
                                    ServiceDate = reader["ServiceDate"] != DBNull.Value ? reader["ServiceDate"].ToString() : string.Empty,
                                    JobCardID = reader["JobCardID"] != DBNull.Value ? Convert.ToInt32(reader["JobCardID"]) : 0,
                                    JobCard = reader["JobCard"] != DBNull.Value ? reader["JobCard"].ToString() : string.Empty,
                                    Status = reader["Status"] != DBNull.Value ? reader["Status"].ToString() : string.Empty,
                                };
                                result.Add(serviceSchedule);
                            }
                        }
                    }
                }

                IQueryable<PartyServiceSchedule> iQPrtyProdAss = result.AsQueryable();

                // FilterToolBar Search
                if (_search)
                {
                    if (!string.IsNullOrEmpty(filtersJson))
                    {
                        Filters filters = JObject.Parse(filtersJson).ToObject<Filters>();
                        iQPrtyProdAss = iQPrtyProdAss.FilterSearch<PartyServiceSchedule>(filters);
                    }
                }
                // Advance Search
                else if (advnce)
                {
                    if (!string.IsNullOrEmpty(advnceFilters))
                    {
                        AdvanceFilter advnfilter = JObject.Parse(advnceFilters).ToObject<AdvanceFilter>();
                        iQPrtyProdAss = iQPrtyProdAss.AdvanceSearch<PartyServiceSchedule>(advnfilter);
                    }
                }

                // Sorting
                iQPrtyProdAss = iQPrtyProdAss.OrderByField<PartyServiceSchedule>(sidx, sord);

                count = iQPrtyProdAss.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                jsonData = new
                {
                    total = total,
                    page = page,
                    records = count,
                    rows = (from q in iQPrtyProdAss
                            select new
                            {
                                edit = "<a title='" + ViewLabel + "' href='#' style='font-size: 13px;' key='" + q.PartyServiceSchedule_ID + "' class='edtPrtySrvSched' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input key='" + q.PartyServiceSchedule_ID + "' type='checkbox' defaultchecked='' class='delPrtySrvSched' />",
                                q.PartyServiceSchedule_ID,
                                Closure_Reason = q.Closure_Reason,
                                q.JobCardID,
                                q.JobCard,
                                q.ServiceDate,
                                ServiceType = q.ServiceType,
                                q.Status,
                                Search = ""
                            }).ToList(),
                    ServiceScheduleStatus
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion


        #region::: SavePartyServiceSchedule Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SavePartyServiceSchedule  
        /// </summary>
        public static IActionResult SavePartyServiceSchedule(string connString, SavePartyServiceScheduleList SavePartyServiceScheduleobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jObj = JObject.Parse(SavePartyServiceScheduleobj.key);
                int rowcount = jObj["rows"].Count();

                for (int i = 0; i < rowcount; i++)
                {
                    GNM_ServiceSchedule gnmPrtyBrnch = jObj["rows"].ElementAt(i).ToObject<GNM_ServiceSchedule>();
                    gnmPrtyBrnch.Closure_reason = gnmPrtyBrnch.Closure_reason == null ? null : Common.DecryptString(gnmPrtyBrnch.Closure_reason);

                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        conn.Open();
                        SqlCommand cmd = new SqlCommand();
                        cmd.Connection = conn;

                        if (gnmPrtyBrnch.PartyServiceSchedule_ID == 0)
                        {
                            // Insert new record
                            cmd.CommandText = "sp_InsertPartyServiceSchedule";
                            cmd.CommandType = CommandType.StoredProcedure;
                        }
                        else
                        {
                            // Update existing record
                            cmd.CommandText = "sp_UpdatePartyServiceSchedule";
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@PartyServiceSchedule_ID", gnmPrtyBrnch.PartyServiceSchedule_ID);
                        }

                        // Common parameters
                        cmd.Parameters.AddWithValue("@Party_ID", gnmPrtyBrnch.Party_ID);
                        cmd.Parameters.AddWithValue("@ServiceType_ID", gnmPrtyBrnch.ServiceType_ID);
                        cmd.Parameters.AddWithValue("@ServiceDate", gnmPrtyBrnch.ServiceDate);
                        cmd.Parameters.AddWithValue("@Closure_reason", (object)gnmPrtyBrnch.Closure_reason ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@JobCard_ID", gnmPrtyBrnch.JobCard_ID);
                        cmd.Parameters.AddWithValue("@Status", gnmPrtyBrnch.Status);

                        cmd.ExecuteNonQuery();
                    }

                    // Insert GPS details
                    //  gbl.InsertGPSDetails(Convert.ToInt32(SavePartyServiceScheduleobj.Company_ID), Convert.ToInt32(SavePartyServiceScheduleobj.Branch), Convert.ToInt32(SavePartyServiceScheduleobj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartyMaster")), gnmPrtyBrnch.Party_ID, 0, 0, "Update", false, Convert.ToInt32(SavePartyServiceScheduleobj.MenuID));
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }

        #endregion


        #region::: DeletePartyServiceSchedule Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// DeletePartyServiceSchedule  
        /// </summary>
        public static IActionResult DeletePartyServiceSchedule(string connString, DeletePartyServiceScheduleList DeletePartyServiceScheduleobj)
        {
            string exMsg = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {


                JTokenReader jr = null;
                JObject jObj = JObject.Parse(DeletePartyServiceScheduleobj.key);
                int rowcount = jObj["rows"].Count();
                int id = 0;
                int Party_ID = 0;
                for (int i = 0; i < rowcount; i++)
                {
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jr.Read();
                    id = Convert.ToInt32(jr.Value);
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "UP_Delete_AM_ERP_DeletePartyServiceScheduleParty";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@PartyServiceSchedule_ID", id);

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {


                                        Party_ID = reader.GetInt32(reader.GetOrdinal("Party_ID"));


                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                }

                //  gbl.InsertGPSDetails(Convert.ToInt32(DeletePartyServiceScheduleobj.Company_ID), Convert.ToInt32(DeletePartyServiceScheduleobj.Branch), Convert.ToInt32(DeletePartyServiceScheduleobj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartyMaster")), Convert.ToInt32(Party_ID), 0, 0, "Delete", false, Convert.ToInt32(DeletePartyServiceScheduleobj.MenuID));
                exMsg = CommonFunctionalities.GetResourceString(DeletePartyServiceScheduleobj.UserCulture.ToString(), "deletedsuccessfully").ToString();
            }
            catch (Exception ex)
            {
                if (ex.InnerException.InnerException.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                {
                    exMsg += CommonFunctionalities.GetResourceString(DeletePartyServiceScheduleobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                }
            }
            return new JsonResult(exMsg);
        }
        #endregion


        #region::: SelectPartyProductDetails Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectPartyProductDetails  
        /// </summary>
        public static IActionResult SelectPartyProductDetails(string connString, SelectPartyProductDetailsList SelectPartyProductDetailsobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filtersJson, bool advnce, string advnceFilters)
        {
            var jsonData = new object();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int value = Convert.ToInt32(SelectPartyProductDetailsobj.value);
                int PartyType = Convert.ToInt32(SelectPartyProductDetailsobj.PartyType);
                bool IsGeneral = Convert.ToBoolean(SelectPartyProductDetailsobj.IsGeneral);
                int Language_ID = Convert.ToInt32(SelectPartyProductDetailsobj.LanguageID);
                using (var connection = new SqlConnection(connString))
                {
                    connection.Open();
                    IQueryable<PartyProductDetail> iQPrtyProdDtl = null;
                    IEnumerable<PartyProductDetail> prtyProdDtl = null;
                    using (var command = new SqlCommand("UP_Get_AM_ERP_GetPartyProductDetails", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // Set parameters
                        command.Parameters.AddWithValue("@PartyType", PartyType);
                        command.Parameters.AddWithValue("@value", value);
                        command.Parameters.AddWithValue("@IsGeneral", IsGeneral);
                        command.Parameters.AddWithValue("@Language_ID", Language_ID);

                        // Execute the command
                        using (var reader = command.ExecuteReader())
                        {
                            var partyProductDetails = new List<PartyProductDetail>();
                            while (reader.Read())
                            {
                                var detail = new PartyProductDetail
                                {
                                    Product_ID = reader.GetInt32(reader.GetOrdinal("Product_ID")),
                                    Brand = reader.GetString(reader.GetOrdinal("Brand")),
                                    Model = reader.GetString(reader.GetOrdinal("Model")),
                                    ProductType = reader.GetString(reader.GetOrdinal("ProductType")),
                                    SerialNumber = reader.GetString(reader.GetOrdinal("SerialNumber"))
                                };
                                partyProductDetails.Add(detail);
                            }

                            jsonData = new
                            {
                                total = (int)Math.Ceiling(partyProductDetails.Count / (double)rows),
                                page = page,
                                records = partyProductDetails.Count,
                                rows = partyProductDetails.Skip((page - 1) * rows).Take(rows).ToList()
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: SelLockLogDetails Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// to select Lock LogDetails
        /// </summary>
        public static IActionResult SelLockLogDetails(string connString, SelLockLogDetailsList SelLockLogDetailsobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            var result = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    SqlCommand command = new SqlCommand("UP_Get_AM_ERP_GetPartyBlockedLogDetails", connection);
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.AddWithValue("@PartyID", SelLockLogDetailsobj.PartyID);
                    command.Parameters.AddWithValue("@SortColumn", sidx);
                    command.Parameters.AddWithValue("@SortOrder", sord);
                    command.Parameters.AddWithValue("@Page", page);
                    command.Parameters.AddWithValue("@RowsPerPage", rows);

                    SqlDataAdapter adapter = new SqlDataAdapter(command);
                    DataTable dataTable = new DataTable();
                    adapter.Fill(dataTable);

                    List<object> resultList = new List<object>();
                    foreach (DataRow row in dataTable.Rows)
                    {
                        resultList.Add(new
                        {
                            PartyBlockedLog_ID = row["PartyBlockedLog_ID"],
                            Party_ID = row["Party_ID"],
                            Status = row["Status"],
                            BlockedDateTime = row["BlockedDateTime"],
                            Remarks = row["Remarks"]
                        });
                    }

                    var sortedEmployeeDataList = resultList.AsQueryable();

                    if (_search)
                    {
                        Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                        sortedEmployeeDataList = sortedEmployeeDataList.FilterSearch(filtersObj);
                    }
                    // Advance Search
                    else if (advnce)
                    {
                        AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<AdvanceFilter>();
                        sortedEmployeeDataList = sortedEmployeeDataList.AdvanceSearch(advnfilter);
                        page = 1;
                    }

                    int count = sortedEmployeeDataList.Count();
                    int total = rows > 0 ? (int)Math.Ceiling((double)count / rows) : 0;

                    result = new
                    {
                        total = total,
                        page = page,
                        records = count,
                        rows = resultList
                    };
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(result);
        }
        #endregion


        #region ::: SelCreditLimitLogDetails Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelCreditLimitLogDetails
        /// </summary>

        public static IActionResult SelCreditLimitLogDetails(string connString, SelCreditLimitLogDetailsList SelCreditLimitLogDetailsobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int userLanguageID = Convert.ToInt32(SelCreditLimitLogDetailsobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelCreditLimitLogDetailsobj.GeneralLanguageID);
                int count = 0;
                int total = 0;
                dynamic s = null;
                var x = s;

                // Fetch Data Using ADO.NET
                DataTable dtPartyBlockList = new DataTable();
                DataTable dtRefList = new DataTable();
                DataTable dtRefListLocale = new DataTable();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    // Fetch Party Credit Limit Log
                    using (SqlCommand cmd = new SqlCommand("SP_GetPartyCreditLimitLog", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@PartyID", SelCreditLimitLogDetailsobj.PartyID);
                        SqlDataAdapter da = new SqlDataAdapter(cmd);
                        da.Fill(dtPartyBlockList);
                    }

                    // Fetch RefMasterDetail
                    using (SqlCommand cmd = new SqlCommand("SP_GetRefMasterDetailCreditLimitLog", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        SqlDataAdapter da = new SqlDataAdapter(cmd);
                        da.Fill(dtRefList);
                    }

                    // Fetch RefMasterDetailLocale
                    using (SqlCommand cmd = new SqlCommand("SP_GetRefMasterDetailLocaleCreditLimitLog", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        SqlDataAdapter da = new SqlDataAdapter(cmd);
                        da.Fill(dtRefListLocale);
                    }
                }

                // LINQ-like join using ADO.NET
                IEnumerable<PartyCreditLimitLog> arrETOList = null;
                if (userLanguageID == generalLanguageID)
                {
                    arrETOList = from a in dtPartyBlockList.AsEnumerable()
                                 join b in dtRefList.AsEnumerable()
                                 on a["Currency_ID"] equals b["RefMasterDetail_ID"]
                                 where b["RefMasterDetail_Short_Name"].ToString() == SelCreditLimitLogDetailsobj.Currency
                                 select new PartyCreditLimitLog()
                                 {
                                     ID = Convert.ToInt32(a["PartyCreditLimitLog_ID"]),
                                     Party_ID = Convert.ToInt32(a["Party_ID"]),
                                     CreditLimit = Convert.ToDecimal(a["CreditLimit"]).ToString("0.00"),
                                     Currency = b["RefMasterDetail_Name"].ToString(),
                                     UpdatedDateTime = a["UpdatedDateTime"] == DBNull.Value ? "" : Convert.ToDateTime(a["UpdatedDateTime"]).ToString("dd-MMM-yyyy hh:mm tt"),
                                     Remarks = a["Remarks"].ToString()
                                 };
                }
                else
                {
                    arrETOList = from a in dtPartyBlockList.AsEnumerable()
                                 join b in dtRefList.AsEnumerable()
                                 on a["Currency_ID"] equals b["RefMasterDetail_ID"]
                                 join c in dtRefListLocale.AsEnumerable()
                                 on b["RefMasterDetail_ID"] equals c["RefMasterDetail_ID"]
                                 where b["RefMasterDetail_Short_Name"].ToString() == SelCreditLimitLogDetailsobj.Currency
                                 select new PartyCreditLimitLog()
                                 {
                                     ID = Convert.ToInt32(a["PartyCreditLimitLog_ID"]),
                                     Party_ID = Convert.ToInt32(a["Party_ID"]),
                                     CreditLimit = Convert.ToDecimal(a["CreditLimit"]).ToString("0.00"),
                                     Currency = c["RefMasterDetail_Name"].ToString(),
                                     UpdatedDateTime = a["UpdatedDateTime"] == DBNull.Value ? "" : Convert.ToDateTime(a["UpdatedDateTime"]).ToString("dd-MMM-yyyy hh:mm tt"),
                                     Remarks = a["Remarks"].ToString()
                                 };
                }

                // Filter and Sort
                var arrETOListIQ = arrETOList.AsQueryable();

                // FilterToolbar Search
                if (_search)
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    arrETOListIQ = arrETOListIQ.FilterSearch<PartyCreditLimitLog>(filtersObj);
                }
                else if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<AdvanceFilter>();
                    arrETOListIQ = arrETOListIQ.AdvanceSearch(advnfilter);
                    page = 1;
                }

                // Sorting 
                arrETOListIQ = arrETOListIQ.OrderByField<PartyCreditLimitLog>(sidx, sord);

                count = arrETOListIQ.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                x = new
                {
                    total = total,
                    page = page,
                    records = count,
                    rows = (from a in arrETOListIQ
                            select new
                            {
                                ID = a.ID,
                                Party_ID = a.Party_ID,
                                CreditLimit = a.CreditLimit,
                                Currency = a.Currency,
                                UpdatedDateTime = a.UpdatedDateTime,
                                Remarks = a.Remarks
                            }).ToList().Paginate(page, rows),
                };

                return new JsonResult(x);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }


        #endregion


        #region ::: SelPartyContractDetails Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelPartyContractDetails
        /// </summary>
        public static IActionResult SelPartyContractDetails(string connString, SelPartyContractDetailsList SelPartyContractDetailsobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filtersJson, bool advnce, string advnceFilters)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var jsonData = default(dynamic);
            try
            {
                int count = 0;
                int total = 0;
                int value = Convert.ToInt32(SelPartyContractDetailsobj.value);
                bool IsGeneral = Convert.ToBoolean(SelPartyContractDetailsobj.IsGeneral);
                int Language_ID = IsGeneral ? 0 : Convert.ToInt32(SelPartyContractDetailsobj.LanguageID);

                List<PartyContractDetail> prtyContractDtl = new List<PartyContractDetail>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    SqlCommand cmd = new SqlCommand("UP_Get_AM_ERP_GetPartyContractDetails", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@PartyID", value);
                    cmd.Parameters.AddWithValue("@IsGeneral", IsGeneral);
                    if (!IsGeneral)
                    {
                        cmd.Parameters.AddWithValue("@LanguageID", Language_ID);
                    }

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            prtyContractDtl.Add(new PartyContractDetail()
                            {
                                PartyContract_ID = reader.GetInt32(0),
                                AgreementNumber = reader.GetString(1),
                                FromDate = reader.GetString(2),
                                ToDate = reader.GetString(3),
                                ContractValue = reader.GetDecimal(4),
                                Unit = reader.GetString(5),
                                Currency = reader.GetString(6),
                                Remarks = reader.GetString(7)
                            });
                        }
                    }
                }

                IQueryable<PartyContractDetail> iQPrtyContractDtl = prtyContractDtl.AsQueryable();

                // FilterToolBar Search
                if (_search)
                {
                    Filters filters = JObject.Parse(filtersJson).ToObject<Filters>();
                    iQPrtyContractDtl = iQPrtyContractDtl.FilterSearch<PartyContractDetail>(filters);
                }
                // Advance Search
                else if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse(advnceFilters).ToObject<AdvanceFilter>();
                    iQPrtyContractDtl = iQPrtyContractDtl.AdvanceSearch<PartyContractDetail>(advnfilter);
                }

                // Sorting 
                iQPrtyContractDtl = iQPrtyContractDtl.OrderByField<PartyContractDetail>(sidx, sord);

                count = iQPrtyContractDtl.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                jsonData = new
                {
                    total = total,
                    page = page,
                    records = count,
                    rows = (from a in iQPrtyContractDtl.AsEnumerable()
                            select new
                            {
                                edit = "<a title='View' href='#' style='font-size: 13px;'  id='" + a.PartyContract_ID + "' key='" + a.PartyContract_ID + "' class='editPartyContractDetails' editmode='false'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                delete = "<input key='" + a.PartyContract_ID + "' type='checkbox' defaultchecked='' class='delPrtycontractdtl' />",
                                a.PartyContract_ID,
                                AgreementNumber = a.AgreementNumber,
                                FromDate = a.FromDate,
                                ToDate = a.ToDate,
                                ContractValue = a.ContractValue,
                                Unit = a.Unit,
                                Currency = a.Currency,
                                Remarks = Common.DecryptString(a.Remarks)
                            }
                    ).ToList()
                };
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: SelectFieldSearchJobCard Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectFieldSearchJobCard
        /// </summary>
        public static IActionResult SelectFieldSearchJobCard(string connString, SelectFieldSearchJobCardList SelectFieldSearchJobCardobj)
        {
            var jsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                string name = Common.DecryptString(SelectFieldSearchJobCardobj.Key);
                int Company_ID = Convert.ToInt32(SelectFieldSearchJobCardobj.Company_ID);
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_Select_AM_ERP_SelectFieldSearchJobCardParty";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@PartyID", SelectFieldSearchJobCardobj.PartyID);
                            command.Parameters.AddWithValue("@JobCardNumber", name);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (reader.HasRows)
                                {
                                    reader.Read();

                                    int count = reader.GetInt32(reader.GetOrdinal("Count"));
                                    if (count > 1)
                                    {
                                        jsonData = new
                                        {
                                            Count = count,
                                            Name = reader.GetString(reader.GetOrdinal("Name"))
                                        };
                                    }
                                    else
                                    {
                                        jsonData = new
                                        {
                                            ID = reader["ID"] != DBNull.Value ? reader.GetInt32(reader.GetOrdinal("ID")) : (int?)null,
                                            Name = reader["Name"] != DBNull.Value ? reader.GetString(reader.GetOrdinal("Name")) : string.Empty
                                        };
                                    }
                                }
                                else
                                {
                                    jsonData = new
                                    {
                                        ID = "",
                                        Name = ""
                                    };
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                //return RedirectToAction("Error");
            }
            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: PartySegmentExits Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// PartySegmentExits
        /// </summary>
        public static IActionResult PartySegmentExits(string connString, PartySegmentExitsList PartySegmentExitsobj)
        {
            int ID = Convert.ToInt32(PartySegmentExitsobj.Primid);
            bool segmentExists = false;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            using (SqlConnection conn = new SqlConnection(connString))
            {
                conn.Open();
                SqlCommand cmd = new SqlCommand("UP_Chk_AM_ERP_CheckPartySegmentExists", conn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@PartyID", PartySegmentExitsobj.partyID);
                cmd.Parameters.AddWithValue("@PrimarySegmentID", PartySegmentExitsobj.prim);
                cmd.Parameters.AddWithValue("@SecondarySegmentID", PartySegmentExitsobj.sec);
                cmd.Parameters.AddWithValue("@PartySegmentID", ID);

                segmentExists = (int)cmd.ExecuteScalar() == 1;
                conn.Close();
            }

            return new JsonResult(segmentExists);
        }
        #endregion


        #region ::: PartyProdExits Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// PartyProdExits
        /// </summary>
        public static IActionResult PartyProdExits(string connString, PartyProdExitsList PartyProdExitsobj)
        {
            bool exists = false;
            int ID = Convert.ToInt32(PartyProdExitsobj.Primid);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            using (SqlConnection conn = new SqlConnection(connString))
            {
                conn.Open();
                SqlCommand cmd = new SqlCommand("UP_Chk_AM_ERP_CheckPartyProductExists", conn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@PartyID", PartyProdExitsobj.partyID);
                cmd.Parameters.AddWithValue("@BrandID", PartyProdExitsobj.brnd);
                cmd.Parameters.AddWithValue("@ProductTypeID", (object)PartyProdExitsobj.prod ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@ModelID", (object)PartyProdExitsobj.mod ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@PartyProductID", ID);

                int count = (int)cmd.ExecuteScalar();
                exists = count > 0;
                conn.Close();

            }

            return new JsonResult(exists);
        }
        #endregion


        #region ::: PartySkillSetExits Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// PartySkillSetExits
        /// </summary>
        public static IActionResult PartySkillSetExits(string connString, PartySkillSetExitsList PartySkillSetExitsobj)
        {
            int skillsetID = Convert.ToInt32(PartySkillSetExitsobj.name);
            int partyID = Convert.ToInt32(PartySkillSetExitsobj.id);
            int ID = Convert.ToInt32(PartySkillSetExitsobj.Primid);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string result = "false";

            using (SqlConnection conn = new SqlConnection(connString))
            {
                conn.Open();
                SqlCommand cmd = new SqlCommand("UP_Chk_AM_ERP_CheckPartySkillSetExists", conn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@PartyID", partyID);
                cmd.Parameters.AddWithValue("@SkillsetID", skillsetID);
                cmd.Parameters.AddWithValue("@PartySkillsetID", ID);

                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        result = reader["Result"].ToString();
                    }
                }
                conn.Close();

            }

            return new JsonResult(result);
        }
        #endregion


        #region ::: PartyAgreementNumberExits Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// PartyAgreementNumberExits
        /// </summary>
        public static IActionResult PartyAgreementNumberExits(string connString, PartyAgreementNumberExitsList PartyAgreementNumberExitsobj)
        {
            string agreementNumber = PartyAgreementNumberExitsobj.name.ToString();
            int id = Convert.ToInt32(PartyAgreementNumberExitsobj.Primid);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string result = "false";

            using (SqlConnection conn = new SqlConnection(connString))
            {
                conn.Open();
                using (SqlCommand cmd = new SqlCommand("UP_Chk_AM_ERP_CheckPartyAgreementNumberExists", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@AgreementNumber", agreementNumber);
                    cmd.Parameters.AddWithValue("@ID", id);

                    result = cmd.ExecuteScalar().ToString();
                }
                conn.Close();

            }

            return new JsonResult(result);
        }
        #endregion


        #region ::: PartyBranchExits Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// PartyBranchExits
        /// </summary>
        public static IActionResult PartyBranchExits(string connString, PartyBranchExitsList PartyBranchExitsobj)
        {
            int BranchID = Convert.ToInt32(PartyBranchExitsobj.name);
            int partyID = Convert.ToInt32(PartyBranchExitsobj.id);
            int ID = Convert.ToInt32(PartyBranchExitsobj.Primid);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var result = string.Empty;
            using (SqlConnection conn = new SqlConnection(connString))
            {
                string query = "UP_Check_AM_ERP_PartyBranchExitsParty";

                SqlCommand command = null;

                try
                {
                    using (command = new SqlCommand(query, conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@PartyID", partyID);
                        command.Parameters.AddWithValue("@BranchID", BranchID);
                        command.Parameters.AddWithValue("@ID", ID);

                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }
                        var resultObj = command.ExecuteScalar();
                        if (resultObj != null)
                        {
                            result = resultObj.ToString().Trim(); // Assuming your stored procedure returns "true" or "false" as string
                        }

                    }
                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }
                finally
                {
                    command.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }
            }

            return new JsonResult(result.ToString());
        }
        #endregion


        #region ::: PartyPartyTax_TaxCodeExits Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// PartyPartyTax_TaxCodeExits
        /// </summary>
        public static IActionResult PartyPartyTax_TaxCodeExits(string connString, PartyPartyTax_TaxCodeExitsList PartyPartyTax_TaxCodeExitsobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var result = string.Empty;
            int partyID = Convert.ToInt32(PartyPartyTax_TaxCodeExitsobj.id);
            string Taxcode = (PartyPartyTax_TaxCodeExitsobj.name).ToString();
            int ID = Convert.ToInt32(PartyPartyTax_TaxCodeExitsobj.Primid);
            using (SqlConnection conn = new SqlConnection(connString))
            {
                string query = "UP_check_AM_ERP_PartyPartyTax_TaxCodeExitsParty";

                SqlCommand command = null;

                try
                {
                    using (command = new SqlCommand(query, conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@PartyID", partyID);
                        command.Parameters.AddWithValue("@Taxcode", Taxcode);
                        command.Parameters.AddWithValue("@ID", ID);

                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }
                        var resultObj = command.ExecuteScalar();
                        if (resultObj != null)
                        {
                            result = resultObj.ToString().Trim(); // Assuming your stored procedure returns "true" or "false" as string
                        }

                    }
                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    }

                }
                finally
                {
                    command.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }
            }
            return new JsonResult(result);
        }
        #endregion


        #region ::: PartyServiceExits Uday Kumar J B 22-08-2024 changed but doubt in 2nd Ajax:::
        /// <summary>
        /// PartyServiceExits
        /// </summary>
        public static IActionResult PartyServiceExits(string connString, PartyServiceExitsList PartyServiceExitsobj)
        {
            dynamic s = null;
            int result = 0;
            JTokenReader jr = null;
            var jsonResult = s;
            int? j = null;
            bool Exists = false;
            List<int> rowIndex = new List<int>();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jObj = JObject.Parse(PartyServiceExitsobj.key);
                int rowcount = jObj["rows"].Count();
                for (int i = 0; i < rowcount; i++)
                {
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["JobCard_ID"]);
                    jr.Read();
                    int? JobCard_ID = (jr.Value == null) ? 0 : Convert.ToInt32(jr.Value.ToString());
                    JobCard_ID = (JobCard_ID == 0) ? null : JobCard_ID;
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["ServiceType_ID"]);
                    jr.Read();
                    int ServiceType_ID = Convert.ToInt32(jr.Value.ToString());

                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["Party_ID"]);
                    jr.Read();
                    int Party_ID = Convert.ToInt32(jr.Value.ToString());
                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["ServiceDate"]);
                    jr.Read();
                    DateTime ServiceDate = Convert.ToDateTime(jr.Value.ToString()).Date;

                    jr = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["PartyServiceSchedule_ID"]);
                    jr.Read();
                    int PartyServiceSchedule_ID = Convert.ToInt32(jr.Value.ToString());

                    for (int k = 0; k < rowcount; k++)
                    {
                        using (SqlConnection conn = new SqlConnection(connString))
                        {
                            string query = "UP_check_AM_ERP_PartyServiceExitsParty";

                            SqlCommand command = null;

                            try
                            {
                                using (command = new SqlCommand(query, conn))
                                {
                                    command.CommandType = CommandType.StoredProcedure;
                                    command.Parameters.AddWithValue("@Party_ID", Party_ID);
                                    command.Parameters.AddWithValue("@JobCard_ID", (object)JobCard_ID ?? DBNull.Value);
                                    command.Parameters.AddWithValue("@ServiceType_ID", ServiceType_ID);
                                    command.Parameters.AddWithValue("@ServiceDate", ServiceDate);
                                    command.Parameters.AddWithValue("@PartyServiceSchedule_ID", PartyServiceSchedule_ID);

                                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                    {
                                        conn.Open();
                                    }
                                    result = (int)command.ExecuteScalar();

                                }
                            }
                            catch (Exception ex)
                            {
                                if (LogException == 1)
                                {
                                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                                }

                            }
                            finally
                            {
                                command.Dispose();
                                conn.Close();
                                conn.Dispose();
                                SqlConnection.ClearAllPools();
                            }
                        }
                        if (result > 0)
                        {
                            rowIndex.Add(i);
                            Exists = true;
                        }
                    }
                }

                jsonResult = new
                {
                    Exists,
                    rowsIndex = rowIndex.ToArray()
                };
            }
            catch (Exception ex)
            {
                Exists = true;
                jsonResult = new
                {
                    Exists,
                    rowsIndex = rowIndex.ToArray()
                };

                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonResult);
        }

        #endregion


        #region ::: PartyServiceExits Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// PartyServiceExits
        /// </summary>
        public static IActionResult CheckPartyExists(string connString, CheckPartyExistsList CheckPartyExistsobj)
        {
            int CompanyID = Convert.ToInt32(CheckPartyExistsobj.Company_ID);
            string name = Common.DecryptString(CheckPartyExistsobj.name);
            string location = Common.DecryptString(CheckPartyExistsobj.location);
            bool FilterPartyBasedonCompany = false;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    string filterQuery = "SELECT Param_value FROM GNM_CompParam WHERE Company_ID = @CompanyID AND Param_Name = 'FilterPartyBasedonCompany'";
                    using (SqlCommand filterCommand = new SqlCommand(filterQuery, connection))
                    {
                        filterCommand.Parameters.AddWithValue("@CompanyID", CompanyID);
                        connection.Open();
                        object result = filterCommand.ExecuteScalar();
                        if (result != null && result != DBNull.Value)
                        {
                            FilterPartyBasedonCompany = result.ToString().ToUpper() == "TRUE";
                        }
                    }
                }

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    SqlCommand command = new SqlCommand("UP_Sel_AM_ERP_CheckPartyExists", connection);
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@name", name);
                    command.Parameters.AddWithValue("@location", location);
                    command.Parameters.AddWithValue("@partyID", CheckPartyExistsobj.partyID);
                    command.Parameters.AddWithValue("@ParttypeID", CheckPartyExistsobj.ParttypeID);
                    command.Parameters.AddWithValue("@CompanyID", CompanyID);
                    command.Parameters.AddWithValue("@FilterPartyBasedonCompany", FilterPartyBasedonCompany);

                    connection.Open();
                    int count = (int)command.ExecuteScalar();
                    return new JsonResult(count > 0);
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }
        #endregion


        #region ::: CheckPartyLocaleExists Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// CheckPartyLocaleExists
        /// </summary>
        public static IActionResult CheckPartyLocaleExists(string connString, CheckPartyLocaleExistsList CheckPartyLocaleExistsobj)
        {
            string name = Common.DecryptString(CheckPartyLocaleExistsobj.name);
            string location = Common.DecryptString(CheckPartyLocaleExistsobj.location);
            int Language_ID = Convert.ToInt32(CheckPartyLocaleExistsobj.LanguageID);
            bool exists = false;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            using (SqlConnection conn = new SqlConnection(connString))
            {
                conn.Open();
                SqlCommand cmd = new SqlCommand("UP_Chk_AM_ERP_CheckPartyLocaleExists", conn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@Name", name);
                cmd.Parameters.AddWithValue("@Location", location);
                cmd.Parameters.AddWithValue("@PartyID", CheckPartyLocaleExistsobj.partyID);
                cmd.Parameters.AddWithValue("@ParttypeID", CheckPartyLocaleExistsobj.ParttypeID);
                cmd.Parameters.AddWithValue("@LanguageID", Language_ID);

                SqlParameter outputParam = new SqlParameter("@Exists", SqlDbType.Bit)
                {
                    Direction = ParameterDirection.Output
                };
                cmd.Parameters.Add(outputParam);

                cmd.ExecuteNonQuery();
                exists = (bool)outputParam.Value;
            }

            return new JsonResult(exists);
        }
        #endregion


        #region ::: SelectCompany Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectCompany
        /// </summary>
        public static IActionResult SelectCompany(string connString, SelectCompanyList SelectCompanyobj)
        {
            var companyList = new List<dynamic>();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int CompanyID = Convert.ToInt32(SelectCompanyobj.Company_ID);
                int userLanguageID = Convert.ToInt32(SelectCompanyobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectCompanyobj.GeneralLanguageID);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    SqlCommand cmd = new SqlCommand("GetCompanyList", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                    cmd.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                    cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var company = new
                            {
                                Company_ID = reader.GetInt32(0),
                                Company_Name = reader.GetString(1)
                            };
                            companyList.Add(company);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(companyList);
        }
        #endregion


        #region ::: SelectParentCompany Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectParentCompany
        /// </summary>
        public static IActionResult SelectParentCompany(string connString, SelectParentCompanyList SelectParentCompanyobj)
        {
            var result = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int CompanyID = Convert.ToInt32(SelectParentCompanyobj.Company_ID);
                int LanguageID = Convert.ToInt32(SelectParentCompanyobj.UserLanguageID);

                List<dynamic> companyDetails = new List<dynamic>();

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    SqlCommand cmd = new SqlCommand("UP_Get_AM_ERP_GetParentCompanyDetails", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                    cmd.Parameters.AddWithValue("@Language", SelectParentCompanyobj.language);
                    cmd.Parameters.AddWithValue("@LanguageID", LanguageID);

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            companyDetails.Add(new
                            {
                                Company_ID = reader.GetInt32(0),
                                Company_Name = reader.GetString(1)
                            });
                        }
                    }
                }

                result = companyDetails;
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(result);
        }
        #endregion


        #region ::: SelectDealers Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectDealers
        /// </summary>
        public static IActionResult SelectDealers(string connString, SelectDealersList SelectDealersobj)
        {
            int userLanguageID = Convert.ToInt32(SelectDealersobj.UserLanguageID);
            int generalLanguageID = Convert.ToInt32(SelectDealersobj.GeneralLanguageID);
            List<dynamic> result = new List<dynamic>();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            using (SqlConnection conn = new SqlConnection(connString))
            {
                conn.Open();
                SqlCommand cmd = new SqlCommand("UP_Get_AM_ERP_SelectDealers", conn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@CompanyID", SelectDealersobj.CompanyID);
                cmd.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        result.Add(new
                        {
                            Branch_ID = reader.GetInt32(0),
                            Branch_Name = reader.GetString(1)
                        });
                    }
                }
            }

            return new JsonResult(result);
        }
        #endregion


        #region ::: SelectAllConsignee Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectAllConsignee
        /// </summary>
        public static IActionResult SelectAllConsignee(string connString, SelectAllConsigneeList SelectAllConsigneeobj)
        {
            dynamic ConsigneeAddress = null;
            var consigneeAddressList = new List<dynamic>();
            int userLanguageID = Convert.ToInt32(SelectAllConsigneeobj.UserLanguageID);
            int generalLanguageID = Convert.ToInt32(SelectAllConsigneeobj.GeneralLanguageID);
            bool? isDealerExists = null;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            using (SqlConnection conn = new SqlConnection(connString))
            {
                conn.Open();
                SqlCommand cmd = new SqlCommand("UP_Get_AM_ERP_GetConsigneeDetails", conn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@PartyID", SelectAllConsigneeobj.PartyID);
                cmd.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        consigneeAddressList.Add(new
                        {
                            Consignee_ID = reader.GetInt32(0),
                            ConsigneeAddress = reader.GetString(1)
                        });
                    }

                    if (reader.NextResult() && reader.Read())
                    {
                        isDealerExists = reader.IsDBNull(0) ? (bool?)null : reader.GetBoolean(0);
                    }
                }
            }

            ConsigneeAddress = consigneeAddressList;

            var jsonResult = new
            {
                ConsigneeArray = ConsigneeAddress,
                IsDealerExists = isDealerExists
            };

            return new JsonResult(jsonResult);
        }
        #endregion


        #region ::: SelectAllConsignee Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectAllConsignee
        /// </summary>
        public static void SetPartyAddressesInActive(string connString, int Party_ID)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    using (SqlCommand command = new SqlCommand("UP_Set_AM_ERP_SetPartyAddressesInActive", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new SqlParameter("@Party_ID", Party_ID));

                        connection.Open();
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
        }
        public static IActionResult SavePartyAddressdetails(string connString, SavePartyAddressdetailsList SavePartyAddressdetailsobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jobj = JObject.Parse(SavePartyAddressdetailsobj.addressData);
                GNM_PartyAddress rowObj = jobj.ToObject<GNM_PartyAddress>();
                rowObj.PartyAddress_Location = Common.DecryptString(rowObj.PartyAddress_Location);
                rowObj.PartyAddress_Address = Uri.UnescapeDataString(Common.DecryptString(rowObj.PartyAddress_Address));
                rowObj.EquivalentConsignee_ID = rowObj.EquivalentConsignee_ID == 0 ? null : rowObj.EquivalentConsignee_ID;
                rowObj.Region_ID = rowObj.Region_ID == 0 ? null : rowObj.Region_ID;

                if (rowObj.IsDefault)
                {
                    SetPartyAddressesInActive(connString, rowObj.Party_ID);
                }

                int newPartyAddressID = 0;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    SqlCommand cmd = new SqlCommand("UP_InsUpd_AM_ERP_SavePartyAddressDetails", conn);
                    cmd.CommandType = CommandType.StoredProcedure;

                    cmd.Parameters.AddWithValue("@PartyAddress_ID", rowObj.PartyAddress_ID);
                    cmd.Parameters.AddWithValue("@Party_ID", rowObj.Party_ID);
                    cmd.Parameters.AddWithValue("@PartyAddress_Active", rowObj.PartyAddress_Active);
                    cmd.Parameters.AddWithValue("@PartyAddress_Address", rowObj.PartyAddress_Address);
                    cmd.Parameters.AddWithValue("@PartyAddress_CountryID", rowObj.PartyAddress_CountryID);
                    cmd.Parameters.AddWithValue("@PartyAddress_LeadTimeInDays", rowObj.PartyAddress_LeadTimeInDays);
                    cmd.Parameters.AddWithValue("@PartyAddress_Location", rowObj.PartyAddress_Location);
                    cmd.Parameters.AddWithValue("@PartyAddress_StateID", rowObj.PartyAddress_StateID);
                    cmd.Parameters.AddWithValue("@PartyAddress_ZIP", rowObj.PartyAddress_ZIP);
                    cmd.Parameters.AddWithValue("@IsDefault", rowObj.IsDefault);
                    cmd.Parameters.AddWithValue("@EquivalentConsignee_ID", rowObj.EquivalentConsignee_ID == null ? (object)DBNull.Value : rowObj.EquivalentConsignee_ID);
                    cmd.Parameters.AddWithValue("@Region_ID", rowObj.Region_ID == null ? (object)DBNull.Value : rowObj.Region_ID);

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            newPartyAddressID = Convert.ToInt32(reader["PartyAddress_ID"]);
                        }
                    }
                }

                //  gbl.InsertGPSDetails(Convert.ToInt32(SavePartyAddressdetailsobj.Company_ID), Convert.ToInt32(SavePartyAddressdetailsobj.Branch), Convert.ToInt32(SavePartyAddressdetailsobj.User_ID), Convert.ToInt32(Common.GetObjectID("CorePartyMaster")), rowObj.Party_ID, 0, 0, "Update", false, Convert.ToInt32(SavePartyAddressdetailsobj.MenuID));
                return new JsonResult(newPartyAddressID);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                return new JsonResult(0);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(0);
            }
        }
        #endregion


        #region ::: SelectAllCountry Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectAllCountry
        /// </summary>
        public static IActionResult SelectAllCountry(string connString)
        {
            var countryList = new List<dynamic>();

            using (SqlConnection connection = new SqlConnection(connString))
            {
                SqlCommand command = new SqlCommand("UP_Get_AM_ERP_GetActiveCountries", connection);
                command.CommandType = CommandType.StoredProcedure;

                connection.Open();

                using (SqlDataReader reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        countryList.Add(new
                        {
                            RefMasterDetail_ID = reader["RefMasterDetail_ID"],
                            RefMasterDetail_Name = reader["RefMasterDetail_Name"]
                        });
                    }
                }
            }

            var jsonResult = new
            {
                countryArray = countryList
            };

            return new JsonResult(jsonResult);
        }
        #endregion


        #region ::: SelectState Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectState
        /// </summary>
        public static IActionResult SelectState(string connString, SelectStateList SelectStateobj)
        {
            List<dynamic> regionArray = new List<dynamic>();

            using (SqlConnection connection = new SqlConnection(connString))
            {
                using (SqlCommand command = new SqlCommand("UP_Sel_AM_ERP_SelectState", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.Add(new SqlParameter("@CountryID", SelectStateobj.countryID));

                    connection.Open();
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            regionArray.Add(new
                            {
                                State_id = reader["State_id"],
                                State_Name = reader["State_Name"]
                            });
                        }
                    }
                }
            }

            return new JsonResult(regionArray);
        }
        #endregion


        #region ::: SelAllPartyAddress Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelAllPartyAddress
        /// </summary>
        public static IActionResult SelAllPartyAddress(string connString, SelAllPartyAddressList SelAllPartyAddressobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filtersJson, bool advnce, string advnceFilters)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var result = new
            {
                total = 0,
                page = 0,
                records = 0,
                data = new dynamic[] { },
                IsDefaultCount = 0
            };
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("UP_Sel_AM_ERP_SelAllPartyAddress", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@partyID", SelAllPartyAddressobj.partyID);
                        cmd.Parameters.AddWithValue("@page", page);
                        cmd.Parameters.AddWithValue("@rows", rows);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            List<dynamic> addressList = new List<dynamic>();
                            int total = 0, records = 0, isDefaultCount = 0;

                            while (reader.Read())
                            {
                                addressList.Add(new
                                {
                                    edit = $"<a title='{CommonFunctionalities.GetGlobalResourceObject(SelAllPartyAddressobj.UserCulture.ToString(), "view").ToString()}' href='#' style='font-size: 13px;' id='{reader["PartyAddress_ID"]}' key='{reader["PartyAddress_ID"]}' class='editPartyAddress'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon' ></i></a>",
                                    delete = $"<input type='checkbox' key='{reader["PartyAddress_ID"]}' id='chk{reader["PartyAddress_ID"]}' class='chkPartyAddressDelete'/>",
                                    PartAddressID = reader["PartyAddress_ID"],
                                    Location = reader["PartyAddress_Location"],
                                    Active = reader["Active"],
                                    IsDefault = reader["IsDefault"],
                                    //    Local = $"<a key='{reader["PartyAddress_ID"]}' id='{reader["PartyAddress_ID"]}' src='{Common.appPath}/Content/Images/local.png' class='PartyAddressLocale' alt='Localize' width='20' height='20' title='Localize'><i class='fa fa-globe'></i></a>"
                                });
                            }

                            if (reader.NextResult() && reader.Read())
                            {
                                total = (int)reader["Total"];
                                records = (int)reader["Records"];
                                isDefaultCount = (int)reader["IsDefaultCount"];
                            }

                            result = new
                            {
                                total = total,
                                page = page,
                                records = records,
                                data = addressList.ToArray(),
                                IsDefaultCount = isDefaultCount
                            };
                        }
                    }
                    conn.Close();
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(result);
        }
        #endregion


        #region ::: SelectSpecificPartyAddressDetails Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectSpecificPartyAddressDetails
        /// </summary>
        public static IActionResult SelectSpecificPartyAddressDetails(string connString, SelectSpecificPartyAddressDetailsList SelectSpecificPartyAddressDetailsobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var x = default(dynamic);
            try
            {
                using (SqlConnection connection = new SqlConnection(connString))
                {
                    using (SqlCommand command = new SqlCommand("UP_Get_AM_ERP_GetPartyAddressDetails", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.Add(new SqlParameter("@PartyAddress_ID", SelectSpecificPartyAddressDetailsobj.id));

                        connection.Open();
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                x = new
                                {
                                    Party_ID = reader["Party_ID"],
                                    PartyAddress_Active = reader["PartyAddress_Active"],
                                    PartyAddress_Address = reader["PartyAddress_Address"],
                                    PartyAddress_CountryID = reader["PartyAddress_CountryID"],
                                    PartyAddress_ID = reader["PartyAddress_ID"],
                                    PartyAddress_LeadTimeInDays = reader["PartyAddress_LeadTimeInDays"],
                                    PartyAddress_Location = reader["PartyAddress_Location"],
                                    PartyAddress_StateID = reader["PartyAddress_StateID"],
                                    PartyAddress_ZIP = reader["PartyAddress_ZIP"],
                                    IsDefault = reader["IsDefault"],
                                    EquivalentConsignee_ID = reader["EquivalentConsignee_ID"],
                                    Region_ID = reader["Region_ID"],
                                    Region_Name = reader["Region_Name"]
                                };
                            }
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(x);
        }
        #endregion


        #region ::: SelAllPartyAddressLocale Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelAllPartyAddressLocale
        /// </summary>
        public static IActionResult SelAllPartyAddressLocale(string connString, SelAllPartyAddressLocaleList SelAllPartyAddressLocaleobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filtersJson, bool advnce, string advnceFilters)
        {
            var x = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int count = 0;
                int total = 0;
                int Language_ID = Convert.ToInt32(SelAllPartyAddressLocaleobj.UserLanguageID);
                List<GNM_PartyAddress> partyList = new List<GNM_PartyAddress>();
                List<GNM_PartyAddressLocale> partyLocaleList = new List<GNM_PartyAddressLocale>();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_Get_AM_ERP_SelAllPartyAddressLocaleParty";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@PartyID", SelAllPartyAddressLocaleobj.partyID);
                            command.Parameters.AddWithValue("@LanguageID", Language_ID);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var partyAddress = new GNM_PartyAddress();
                                    for (int i = 0; i < reader.FieldCount; i++)
                                    {
                                        string columnName = reader.GetName(i);
                                        PropertyInfo property = typeof(GNM_PartyAddress).GetProperty(columnName);
                                        if (property != null && !reader.IsDBNull(i))
                                        {
                                            object value = reader.GetValue(i);
                                            property.SetValue(partyAddress, value);
                                        }
                                    }
                                    partyList.Add(partyAddress);
                                }
                                if (reader.NextResult())
                                {
                                    while (reader.Read())
                                    {
                                        var partyAddressLocale = new GNM_PartyAddressLocale();
                                        for (int i = 0; i < reader.FieldCount; i++)
                                        {
                                            string columnName = reader.GetName(i);
                                            PropertyInfo property = typeof(GNM_PartyAddressLocale).GetProperty(columnName);
                                            if (property != null && !reader.IsDBNull(i))
                                            {
                                                object value = reader.GetValue(i);
                                                property.SetValue(partyAddressLocale, value);
                                            }
                                        }
                                        partyLocaleList.Add(partyAddressLocale);
                                    }
                                }



                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                string ViewLabel = CommonFunctionalities.GetGlobalResourceObject(SelAllPartyAddressLocaleobj.UserCulture.ToString(), "view").ToString();
                string yes = CommonFunctionalities.GetGlobalResourceObject(SelAllPartyAddressLocaleobj.UserCulture.ToString(), "yes").ToString();
                string no = CommonFunctionalities.GetGlobalResourceObject(SelAllPartyAddressLocaleobj.UserCulture.ToString(), "no").ToString();
                var arrSkillsList = from a in partyList
                                    join b in partyLocaleList on a.PartyAddress_ID equals b.PartyAddress_ID into tempLoc
                                    from temp in tempLoc.DefaultIfEmpty(new GNM_PartyAddressLocale() { PartyAddressLocale_ID = 0, PartyAddressLocale_Location = "" })
                                    select new
                                    {
                                        // view = "<img id='" + a.PartyAddress_ID + "' src='" + Common.appPath + "/Content/Images/Plus.gif' key='" + a.PartyAddress_ID + "' class='viewLocalPartyAddress'/>",
                                        Location = temp.PartyAddressLocale_Location == "" ? "" : temp.PartyAddressLocale_Location,
                                        Active = a.PartyAddress_Active == true ? yes : no,
                                        IsDefault = a.IsDefault == true ? yes : no,
                                        PartAddressLocaleID = temp.PartyAddressLocale_ID
                                    };

                count = arrSkillsList.ToList().Count;
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                List<dynamic> arr = new List<dynamic>();
                for (int i = 0; i < arrSkillsList.ToList().Count; i++)
                {
                    //Modified by Manjunatha P for removing pagination
                    arr.Add(arrSkillsList.ToList()[i]);
                    //if ((i >= (page * rows) - rows) && (i < (page * rows) + rows))
                    //{

                    //}
                    //End
                }
                x = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = arr.ToArray(),
                };
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }
            }
            return new JsonResult(x);
        }
        #endregion


        #region ::: SelectSpecificPartyAddressLocaleDetails Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectSpecificPartyAddressLocaleDetails
        /// </summary>
        public static IActionResult SelectSpecificPartyAddressLocaleDetails(string connString, SelectSpecificPartyAddressLocaleDetailsList SelectSpecificPartyAddressLocaleDetailsobj)
        {
            var x = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int Language_ID = Convert.ToInt32(SelectSpecificPartyAddressLocaleDetailsobj.UserLanguageID);

                List<GNM_PartyAddress> englishRowArr = new List<GNM_PartyAddress>();
                List<GNM_PartyAddressLocale> localeRowArr = new List<GNM_PartyAddressLocale>();
                GNM_PartyAddress englishRow = new GNM_PartyAddress();
                GNM_PartyAddressLocale localeRow = new GNM_PartyAddressLocale();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_select_AM_ERP_SelectSpecificPartyAddressLocaleDetailsParty";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@PartyAddress_ID", SelectSpecificPartyAddressLocaleDetailsobj.id);
                            command.Parameters.AddWithValue("@Language_ID", Language_ID);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var partyAddress = new GNM_PartyAddress();
                                    for (int i = 0; i < reader.FieldCount; i++)
                                    {
                                        string columnName = reader.GetName(i);
                                        PropertyInfo property = typeof(GNM_PartyAddress).GetProperty(columnName);
                                        if (property != null && !reader.IsDBNull(i))
                                        {
                                            object value = reader.GetValue(i);
                                            property.SetValue(partyAddress, value);
                                        }
                                    }
                                    englishRowArr.Add(partyAddress);


                                }
                                englishRow = englishRowArr.FirstOrDefault();
                                if (reader.NextResult())
                                {
                                    while (reader.Read())
                                    {
                                        var partyAddressLocale = new GNM_PartyAddressLocale();
                                        for (int i = 0; i < reader.FieldCount; i++)
                                        {
                                            string columnName = reader.GetName(i);
                                            PropertyInfo property = typeof(GNM_PartyAddressLocale).GetProperty(columnName);
                                            if (property != null && !reader.IsDBNull(i))
                                            {
                                                object value = reader.GetValue(i);
                                                property.SetValue(partyAddressLocale, value);
                                            }
                                        }
                                        localeRowArr.Add(partyAddressLocale);
                                    }
                                    localeRow = localeRowArr.FirstOrDefault();

                                }



                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                if (localeRow != null)
                {
                    x = new
                    {
                        PartyAddress_AddressG = englishRow.PartyAddress_Address,
                        PartyAddress_IDG = englishRow.PartyAddress_ID,
                        PartyAddress_LocationG = englishRow.PartyAddress_Location,

                        PartyAddressLocale_ID = localeRow.PartyAddressLocale_ID,
                        PartyAddressLocale_AddressN = localeRow.PartyAddressLocale_Address,
                        PartyAddressLocale_LocationN = localeRow.PartyAddressLocale_Location
                    };
                }
                else
                {
                    x = new
                    {
                        PartyAddress_AddressG = englishRow.PartyAddress_Address,
                        PartyAddress_IDG = englishRow.PartyAddress_ID,
                        PartyAddress_LocationG = englishRow.PartyAddress_Location,

                        PartyAddressLocale_ID = "",
                        PartyAddressLocale_AddressN = "",
                        PartyAddressLocale_LocationN = ""
                    };
                }

            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);

                }
            }

            return new JsonResult(x);
        }
        #endregion


        #region ::: SelectSpecificPartyContactLocaleDetails Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectSpecificPartyContactLocaleDetails
        /// </summary>

        public static IActionResult SelectSpecificPartyContactLocaleDetails(string connString, SelectSpecificPartyContactLocaleDetailsList SelectSpecificPartyContactLocaleDetailsobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            var x = default(dynamic);
            try
            {
                int Language_ID = Convert.ToInt32(SelectSpecificPartyContactLocaleDetailsobj.UserLanguageID);

                using (SqlConnection con = new SqlConnection(connString))
                {
                    con.Open();

                    // Fetch English Row
                    SqlCommand cmdEnglish = new SqlCommand("SP_GetPartyContactPersonDetails", con);
                    cmdEnglish.CommandType = CommandType.StoredProcedure;
                    cmdEnglish.Parameters.AddWithValue("@PartyContactPerson_ID", SelectSpecificPartyContactLocaleDetailsobj.id);

                    GNM_PartyContactPersonDetails englishRow = null;
                    using (SqlDataReader reader = cmdEnglish.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            englishRow = new GNM_PartyContactPersonDetails
                            {
                                PartyContactPerson_Name = reader["PartyContactPerson_Name"].ToString(),
                                PartyContactPerson_ID = Convert.ToInt32(reader["PartyContactPerson_ID"]),
                                PartyContactPerson_Department = reader["PartyContactPerson_Department"].ToString(),
                                PartyContactPerson_Remarks = reader["PartyContactPerson_Remarks"].ToString()
                            };
                        }
                    }

                    // Fetch Localized Row
                    SqlCommand cmdLocale = new SqlCommand("SP_GetPartyContactLocale", con);
                    cmdLocale.CommandType = CommandType.StoredProcedure;
                    cmdLocale.Parameters.AddWithValue("@PartyContactPerson_ID", SelectSpecificPartyContactLocaleDetailsobj.id);
                    cmdLocale.Parameters.AddWithValue("@Language_ID", Language_ID);

                    GNM_PartyContactLocale localeRow = null;
                    using (SqlDataReader reader = cmdLocale.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            localeRow = new GNM_PartyContactLocale
                            {
                                PartyContactLocale_ID = Convert.ToInt32(reader["PartyContactLocale_ID"]),
                                PartyContact_Name = reader["PartyContact_Name"].ToString(),
                                PartyContact_Department = reader["PartyContact_Department"].ToString(),
                                PartyContact_Remarks = reader["PartyContact_Remarks"].ToString()
                            };
                        }
                    }

                    // Construct result
                    if (localeRow != null)
                    {
                        x = new
                        {
                            PartyContactNameG = englishRow.PartyContactPerson_Name,
                            PartyPersonContact_IDG = englishRow.PartyContactPerson_ID,
                            PartyDepG = englishRow.PartyContactPerson_Department,
                            PartyRemarksG = englishRow.PartyContactPerson_Remarks,

                            PartyContactLocale_ID = localeRow.PartyContactLocale_ID,
                            PartyContactNameN = localeRow.PartyContact_Name,
                            PartyDepN = localeRow.PartyContact_Department,
                            PartyRemarksN = localeRow.PartyContact_Remarks,
                        };
                    }
                    else
                    {
                        x = new
                        {
                            PartyContactNameG = englishRow.PartyContactPerson_Name,
                            PartyPersonContact_IDG = englishRow.PartyContactPerson_ID,
                            PartyDepG = englishRow.PartyContactPerson_Department,
                            PartyRemarksG = englishRow.PartyContactPerson_Remarks,

                            PartyContactLocale_ID = "",
                            PartyContactNameN = "",
                            PartyDepN = "",
                            PartyRemarksN = "",
                        };
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(x);
        }

        #endregion


        #region ::: SavePartyLocaleAddressdetails Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SavePartyLocaleAddressdetails
        /// </summary>
        public static IActionResult SavePartyLocaleAddressdetails(string connString, SavePartyLocaleAddressdetailsList SavePartyLocaleAddressdetailsobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {

                JObject jobj = JObject.Parse(SavePartyLocaleAddressdetailsobj.LocaleData);
                GNM_PartyAddressLocale rowObj = jobj.ToObject<GNM_PartyAddressLocale>();
                rowObj.PartyAddressLocale_Address = Uri.UnescapeDataString(Common.DecryptString(rowObj.PartyAddressLocale_Address));
                rowObj.PartyAddressLocale_Location = Uri.UnescapeDataString(Common.DecryptString(rowObj.PartyAddressLocale_Location));
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "UP_InsertUpdate_AM_ERP_SavePartyLocaleAddressdetailsParty";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@PartyAddressLocale_ID", rowObj.PartyAddressLocale_ID);
                            command.Parameters.AddWithValue("@PartyAddressLocale_Address", rowObj.PartyAddressLocale_Address);
                            command.Parameters.AddWithValue("@PartyAddressLocale_Location", rowObj.PartyAddressLocale_Location);
                            command.Parameters.AddWithValue("@Language_ID", rowObj.Language_ID);
                            command.Parameters.AddWithValue("@PartyAddress_ID", rowObj.PartyAddress_ID);


                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            command.ExecuteScalar();

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                return new JsonResult(rowObj.PartyAddressLocale_ID);
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
                return new JsonResult(0);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return new JsonResult(0);
            }
        }
        #endregion


        #region ::: SavePartyLocaleContactdetails Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SavePartyLocaleContactdetails
        /// </summary>
        public static IActionResult SavePartyLocaleContactdetails(string connString, SavePartyLocaleContactdetailsList SavePartyLocaleContactdetailsobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jobj = JObject.Parse(SavePartyLocaleContactdetailsobj.LocaleData);
                GNM_PartyContactLocale rowObj = jobj.ToObject<GNM_PartyContactLocale>();
                rowObj.PartyContact_Name = Common.DecryptString(rowObj.PartyContact_Name);
                rowObj.PartyContact_Department = Common.DecryptString(rowObj.PartyContact_Department);
                rowObj.PartyContact_Remarks = Common.DecryptString(rowObj.PartyContact_Remarks);

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand("Up_SavePartyLocaleContactDetails", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@PartyContactLocale_ID", rowObj.PartyContactLocale_ID);
                        command.Parameters.AddWithValue("@PartyContact_Name", rowObj.PartyContact_Name);
                        command.Parameters.AddWithValue("@PartyContact_Department", rowObj.PartyContact_Department);
                        command.Parameters.AddWithValue("@PartyContact_Remarks", rowObj.PartyContact_Remarks);

                        SqlParameter outputParam = new SqlParameter("@NewPartyContactLocale_ID", SqlDbType.Int)
                        {
                            Direction = ParameterDirection.Output
                        };
                        command.Parameters.Add(outputParam);

                        command.ExecuteNonQuery();

                        int newPartyContactLocaleID = (int)outputParam.Value;
                        return new JsonResult(new { PartyContactLocale_ID = newPartyContactLocaleID });
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(null);
        }
        #endregion


        #region ::: DeletePartyAddressDetail Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// DeletePartyAddressDetail
        /// </summary>
        public static IActionResult DeletePartyAddressDetail(string connString, DeletePartyAddressDetailList DeletePartyAddressDetailobj)
        {
            string errorMsg = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jObj = JObject.Parse(DeletePartyAddressDetailobj.key);
                int rowcount = jObj["rows"].Count();
                int id = 0;

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    for (int i = 0; i < rowcount; i++)
                    {
                        id = Convert.ToInt32(jObj["rows"].ElementAt(i)["id"]);
                        using (SqlCommand command = new SqlCommand("Up_DeletePartyAddressDetail", connection))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@PartyAddress_ID", id);

                            SqlParameter errorMessageParam = new SqlParameter("@ErrorMessage", SqlDbType.NVarChar, 500)
                            {
                                Direction = ParameterDirection.Output
                            };
                            command.Parameters.Add(errorMessageParam);

                            command.ExecuteNonQuery();

                            string resultMessage = errorMessageParam.Value.ToString();
                            if (resultMessage.Contains("Dependency found"))
                            {
                                errorMsg = CommonFunctionalities.GetResourceString(DeletePartyAddressDetailobj.UserCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                            }
                            else
                            {
                                errorMsg = CommonFunctionalities.GetResourceString(DeletePartyAddressDetailobj.UserCulture.ToString(), "deletedsuccessfully").ToString();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                errorMsg = CommonFunctionalities.GetResourceString(DeletePartyAddressDetailobj.UserCulture.ToString(), "AnErrorOccurred").ToString();
            }

            return new JsonResult(errorMsg);
        }
        #endregion


        #region ::: SelectCurrency Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectCurrency
        /// </summary>
        public static IActionResult SelectCurrency(string connString, SelectCurrencyList SelectCurrencyobj)
        {
            var currencyArray = new List<dynamic>();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int userLanguageID = Convert.ToInt32(SelectCurrencyobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectCurrencyobj.GeneralLanguageID);
                int CompanyID = Convert.ToInt32(SelectCurrencyobj.Company_ID);

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand("Up_SelectCurrency", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@CompanyID", CompanyID);
                        command.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                        command.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                currencyArray.Add(new
                                {
                                    RefMasterDetail_ID = reader["RefMasterDetail_ID"],
                                    RefMasterDetail_Name = reader["RefMasterDetail_Name"]
                                });
                            }
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            var jsonResult = new
            {
                currencyArray = currencyArray
            };
            return new JsonResult(jsonResult);
        }
        #endregion


        #region ::: SelectLanguage Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectLanguage
        /// </summary>
        public static IActionResult SelectLanguage(string connString, SelectLanguageList SelectLanguageobj)
        {
            var languageArray = new List<dynamic>();
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int userLanguageID = Convert.ToInt32(SelectLanguageobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(SelectLanguageobj.GeneralLanguageID);
                int CompanyID = Convert.ToInt32(SelectLanguageobj.Company_ID);

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand("Up_SelectLanguage", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@CompanyID", CompanyID);
                        command.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                        command.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                languageArray.Add(new
                                {
                                    RefMasterDetail_ID = reader["RefMasterDetail_ID"],
                                    RefMasterDetail_Name = reader["RefMasterDetail_Name"]
                                });
                            }
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            var jsonResult = new
            {
                languageArray = languageArray
            };
            return new JsonResult(jsonResult);
        }
        #endregion


        #region ::: CheckContactPerson Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// CheckContactPerson
        /// </summary>
        public static IActionResult CheckContactPerson(string connString, CheckContactPersonList CheckContactPersonobj)
        {
            string result = "0";
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                string Name = Common.DecryptString(CheckContactPersonobj.PartyContactPersonName);
                string Mobile = Common.DecryptString(CheckContactPersonobj.PartyContactPersonMobile);
                int PartyID = Convert.ToInt32(CheckContactPersonobj.PartyID);
                int ID = Convert.ToInt32(CheckContactPersonobj.PartyContactPersonID);

                using (SqlConnection connection = new SqlConnection(connString))
                {
                    connection.Open();

                    using (SqlCommand command = new SqlCommand("Up_CheckContactPerson", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@PartyID", PartyID);
                        command.Parameters.AddWithValue("@PartyContactPersonID", ID);
                        command.Parameters.AddWithValue("@PartyContactPersonName", Name);
                        command.Parameters.AddWithValue("@PartyContactPersonMobile", Mobile);

                        int count = (int)command.ExecuteScalar();

                        result = count > 0 ? "1" : "0";
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(result);
        }
        #endregion


        #region ::: getContract Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// getContract
        /// </summary>
        public static IActionResult getContract(string connString, getContractList getContractobj)
        {
            int status = 0;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                GNM_PARTYCONTRACTDETAILS gnmPrtycontract = null;
                GNM_PARTYCONTRACTDETAILS gnmPrtycontractUpd = null;
                JObject jObj = JObject.Parse(getContractobj.key);
                JTokenReader jr = null;
                int rowcount = jObj["rows"].Count();
                for (int i = 0; i < rowcount; i++)
                {
                    jr = new JTokenReader(jObj["rows"][i]["PreviousFromDate"]);
                    jr.Read();
                    DateTime PreviousFromDate = Convert.ToDateTime(jr.Value.ToString());

                    jr = new JTokenReader(jObj["rows"][i]["PreviousToDate"]);
                    jr.Read();
                    DateTime PreviousToDate = Convert.ToDateTime(jr.Value.ToString());

                    jr = new JTokenReader(jObj["rows"][i]["PartyContractFromRowid"]);
                    jr.Read();
                    string PartyContractFromRowid = jr.Value.ToString();

                    jr = new JTokenReader(jObj["rows"][i]["PartyContractToRowid"]);
                    jr.Read();
                    string PartyContractToRowid = jr.Value.ToString();
                    if ((getContractobj.CurrentFromDateid != PartyContractFromRowid) && (getContractobj.CurrentToDateid != PartyContractToRowid))
                    {

                        if (((getContractobj.CurrentFromDate <= PreviousFromDate) && (getContractobj.CurrentToDate >= PreviousFromDate)) || ((getContractobj.CurrentFromDate <= PreviousToDate) && (getContractobj.CurrentToDate >= PreviousToDate)) || ((getContractobj.CurrentFromDate >= PreviousFromDate) && (getContractobj.CurrentToDate <= PreviousToDate)))
                        {
                            status = status + 1;
                        }

                    }

                    //if((CurrentFromDateid! =PartyContractFromRowid) &&( CurrentToDateid !=PartyContractToRowid))){
                    //if (((FDate <= PreviousFromDate) && (ToDate >= PreviousFromDate)) || ((FDate <= PreviousToDate) && (ToDate >= PreviousToDate)) || ((FDate >= PreviousFromDate) && (ToDate <= PreviousToDate)))
                    //{
                    //    DateCount = 1;
                    //}
                    //else
                    //{
                    //    DateCount = 0;
                    //}
                    //}
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(status);

        }
        #endregion


        #region ::: SelectSpecificPartyContactDetails Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectSpecificPartyContactDetails
        /// </summary>
        public static IActionResult SelectSpecificPartyContactDetails(string connString, SelectSpecificPartyContactDetailsList SelectSpecificPartyContactDetailsobj)
        {
            var JsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int objectid = Convert.ToInt32(SelectSpecificPartyContactDetailsobj.ObjectID);
                // dynamic jr = Common.InitialSetup(objectid);
                dynamic jr = " ";
                int Language_ID = Convert.ToInt32(SelectSpecificPartyContactDetailsobj.UserLanguageID);
                int GeneralLang_ID = Convert.ToInt32(SelectSpecificPartyContactDetailsobj.GeneralLanguageID);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Step 1: Fetch General Party Contact Details
                    SqlCommand cmdGeneral = new SqlCommand("SP_GetPartyContactDetails", conn);
                    cmdGeneral.CommandType = CommandType.StoredProcedure;
                    cmdGeneral.Parameters.AddWithValue("@PartyContactPerson_ID", SelectSpecificPartyContactDetailsobj.id);

                    SqlDataReader readerGeneral = cmdGeneral.ExecuteReader();
                    if (readerGeneral.Read())
                    {
                        var englishRow = new
                        {
                            PartyContactPerson_ID = readerGeneral.GetInt32(0),
                            PartyContactPerson_Name = readerGeneral.GetString(1),
                            PartyContactPerson_Department = readerGeneral.GetString(2),
                            PartyContactPerson_Email = readerGeneral.GetString(3),
                            PartyContactPerson_Mobile = readerGeneral.GetString(4)
                        };

                        readerGeneral.Close();

                        if (GeneralLang_ID == Language_ID)
                        {
                            JsonData = new
                            {
                                jr,
                                PartyContact_ID = englishRow.PartyContactPerson_ID,
                                PartyContactPerson_Name = englishRow.PartyContactPerson_Name,
                                PartyContactPerson_Department = englishRow.PartyContactPerson_Department,
                                PartyContactPerson_Email = englishRow.PartyContactPerson_Email,
                                PartyContactPerson_Mobile = englishRow.PartyContactPerson_Mobile
                            };
                        }
                        else
                        {
                            // Step 2: Fetch Localized Party Contact Details
                            SqlCommand cmdLocale = new SqlCommand("SP_GetPartyContactLocaleDetails", conn);
                            cmdLocale.CommandType = CommandType.StoredProcedure;
                            cmdLocale.Parameters.AddWithValue("@PartyContactPerson_ID", SelectSpecificPartyContactDetailsobj.id);
                            cmdLocale.Parameters.AddWithValue("@Language_ID", Language_ID);

                            SqlDataReader readerLocale = cmdLocale.ExecuteReader();
                            if (readerLocale.Read())
                            {
                                JsonData = new
                                {
                                    jr,
                                    PartyContact_ID = readerLocale.GetInt32(0),
                                    PartyContactPerson_Name = readerLocale.GetString(1),
                                    PartyContactPerson_Department = readerLocale.GetString(2),
                                    PartyContactPerson_Email = englishRow.PartyContactPerson_Email,
                                    PartyContactPerson_Mobile = englishRow.PartyContactPerson_Mobile
                                };
                            }
                            else
                            {
                                JsonData = new
                                {
                                    jr,
                                    PartyContact_ID = englishRow.PartyContactPerson_ID,
                                    PartyContactPerson_Name = englishRow.PartyContactPerson_Name,
                                    PartyContactPerson_Department = englishRow.PartyContactPerson_Department,
                                    PartyContactPerson_Email = englishRow.PartyContactPerson_Email,
                                    PartyContactPerson_Mobile = englishRow.PartyContactPerson_Mobile
                                };
                            }

                            readerLocale.Close();
                        }
                    }
                }
            }
            catch (WebException wex)
            {
                LS.LogSheetExporter.LogToTextFile(wex.HResult, wex.Status.ToString(), wex.TargetSite.ToString(), wex.StackTrace);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(JsonData);
        }
        #endregion


        #region ::: UpdateContactPersonMaster Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// UpdateContactPersonMaster
        /// </summary>
        public static IActionResult UpdateContactPersonMaster(string connString, UpdateContactPersonMasterList UpdateContactPersonMasterobj)
        {
            var jsonResult = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int userLanguageID = Convert.ToInt32(UpdateContactPersonMasterobj.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(UpdateContactPersonMasterobj.GeneralLanguageID);
                int ContactPerson_ID = Convert.ToInt32(UpdateContactPersonMasterobj.id);

                GNM_PartyContactPersonDetails gnmContPrty = JObject.Parse(UpdateContactPersonMasterobj.Data).ToObject<GNM_PartyContactPersonDetails>();
                gnmContPrty.PartyContactPerson_Name = Uri.UnescapeDataString(gnmContPrty.PartyContactPerson_Name);
                gnmContPrty.PartyContactPerson_Mobile = Uri.UnescapeDataString(gnmContPrty.PartyContactPerson_Mobile);
                gnmContPrty.PartyContactPerson_Email = Uri.UnescapeDataString(gnmContPrty.PartyContactPerson_Email);
                gnmContPrty.PartyContactPerson_Department = Uri.UnescapeDataString(gnmContPrty.PartyContactPerson_Department);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    if (userLanguageID == generalLanguageID)
                    {
                        // Update general contact details
                        SqlCommand cmdGeneral = new SqlCommand("SP_UpdatePartyContactDetails", conn);
                        cmdGeneral.CommandType = CommandType.StoredProcedure;
                        cmdGeneral.Parameters.AddWithValue("@PartyContactPerson_ID", ContactPerson_ID);
                        cmdGeneral.Parameters.AddWithValue("@PartyContactPerson_Name", Common.DecryptString(gnmContPrty.PartyContactPerson_Name));
                        cmdGeneral.Parameters.AddWithValue("@PartyContactPerson_Department", Common.DecryptString(gnmContPrty.PartyContactPerson_Department));
                        cmdGeneral.Parameters.AddWithValue("@PartyContactPerson_Mobile", Common.DecryptString(gnmContPrty.PartyContactPerson_Mobile));
                        cmdGeneral.Parameters.AddWithValue("@PartyContactPerson_Email", Common.DecryptString(gnmContPrty.PartyContactPerson_Email));

                        cmdGeneral.ExecuteNonQuery();
                    }
                    else
                    {
                        // Update general contact mobile and email
                        SqlCommand cmdGeneral = new SqlCommand("SP_UpdatePartyContactDetails", conn);
                        cmdGeneral.CommandType = CommandType.StoredProcedure;
                        cmdGeneral.Parameters.AddWithValue("@PartyContactPerson_ID", ContactPerson_ID);
                        cmdGeneral.Parameters.AddWithValue("@PartyContactPerson_Name", DBNull.Value); // Name not updated
                        cmdGeneral.Parameters.AddWithValue("@PartyContactPerson_Department", DBNull.Value); // Department not updated
                        cmdGeneral.Parameters.AddWithValue("@PartyContactPerson_Mobile", Common.DecryptString(gnmContPrty.PartyContactPerson_Mobile));
                        cmdGeneral.Parameters.AddWithValue("@PartyContactPerson_Email", Common.DecryptString(gnmContPrty.PartyContactPerson_Email));

                        cmdGeneral.ExecuteNonQuery();

                        // Update localized contact details
                        SqlCommand cmdLocale = new SqlCommand("SP_UpdatePartyContactLocaleDetails", conn);
                        cmdLocale.CommandType = CommandType.StoredProcedure;
                        cmdLocale.Parameters.AddWithValue("@PartyContactPerson_ID", ContactPerson_ID);
                        cmdLocale.Parameters.AddWithValue("@PartyContact_Name", Common.DecryptString(gnmContPrty.PartyContactPerson_Name));
                        cmdLocale.Parameters.AddWithValue("@PartyContact_Department", Common.DecryptString(gnmContPrty.PartyContactPerson_Department));

                        cmdLocale.ExecuteNonQuery();
                    }
                }

                jsonResult = new
                {
                    Result = "Success",
                    Name = Common.DecryptString(gnmContPrty.PartyContactPerson_Name),
                    ID = ContactPerson_ID
                };
            }
            catch (Exception ex)
            {
                jsonResult = new
                {
                    Result = "Fail",
                    Name = "",
                    ID = 0
                };

                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonResult);
        }
        #endregion


        #region ::: SelectPartyCreditDetails Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// SelectPartyCreditDetails
        /// </summary>
        public static IActionResult SelectPartyCreditDetails(string connString, SelectPartyCreditDetailsList SelectPartyCreditDetailsobj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advnceFilters)
        {
            int userLanguageID = Convert.ToInt32(SelectPartyCreditDetailsobj.UserLanguageID);
            int generalLanguageID = Convert.ToInt32(SelectPartyCreditDetailsobj.GeneralLanguageID);
            var jsonData = default(dynamic);
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                int count = 0;
                int total = 0;
                int value = Convert.ToInt32(SelectPartyCreditDetailsobj.value);
                bool IsLang = (userLanguageID == generalLanguageID);

                List<GNMCreditDetails> creditDetailsList = new List<GNMCreditDetails>();

                using (SqlConnection conn = new SqlConnection(ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString))
                {
                    using (SqlCommand cmd = new SqlCommand("USP_GetPartyCreditDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@Party_ID", value);
                        cmd.Parameters.AddWithValue("@IsLang", IsLang);

                        conn.Open();
                        using (SqlDataReader dr = cmd.ExecuteReader())
                        {
                            while (dr.Read())
                            {
                                creditDetailsList.Add(new GNMCreditDetails
                                {
                                    CreditDetails_ID = dr.GetInt32(dr.GetOrdinal("CreditDetails_ID")),
                                    Currency_ID = dr.IsDBNull(dr.GetOrdinal("Currency_ID")) ? (int?)null : dr.GetInt32(dr.GetOrdinal("Currency_ID")),
                                    Party_ID = dr.IsDBNull(dr.GetOrdinal("Party_ID")) ? (int?)null : dr.GetInt32(dr.GetOrdinal("Party_ID")),
                                    Currency = dr.IsDBNull(dr.GetOrdinal("Currency")) ? null : dr.GetString(dr.GetOrdinal("Currency")),
                                    Parts_Credit_Limit = dr.IsDBNull(dr.GetOrdinal("Parts_Credit_Limit")) ? (decimal?)null : dr.GetDecimal(dr.GetOrdinal("Parts_Credit_Limit")),
                                    Service_Credit_Limit = dr.IsDBNull(dr.GetOrdinal("Service_Credit_Limit")) ? (decimal?)null : dr.GetDecimal(dr.GetOrdinal("Service_Credit_Limit")),
                                    Sales_Credit_Limit = dr.IsDBNull(dr.GetOrdinal("Sales_Credit_Limit")) ? (decimal?)null : dr.GetDecimal(dr.GetOrdinal("Sales_Credit_Limit"))
                                });
                            }
                        }
                    }
                }

                // Keep the list as IQueryable for later use in filtering
                var queryableCreditDetailsList = creditDetailsList.AsQueryable().OrderByField(sidx, sord);

                // Apply filters and sorting as in the LINQ version
                if (_search)
                {
                    Filters filtersObj = JObject.Parse(Common.DecryptString(filters)).ToObject<Filters>();
                    queryableCreditDetailsList = queryableCreditDetailsList.FilterSearch<GNMCreditDetails>(filtersObj);
                }
                else if (advnce)
                {
                    AdvanceFilter advnfilter = JObject.Parse(Uri.UnescapeDataString(advnceFilters)).ToObject<AdvanceFilter>();
                    queryableCreditDetailsList = queryableCreditDetailsList.AdvanceSearch<GNMCreditDetails>(advnfilter);
                }

                // Convert to list for paging
                var pagedCreditDetailsList = queryableCreditDetailsList.ToList();

                count = pagedCreditDetailsList.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;

                jsonData = new
                {
                    total = total,
                    page = page,
                    records = count,
                    rows = pagedCreditDetailsList
                        .Skip((page - 1) * rows)
                        .Take(rows)
                        .Select(a => new
                        {
                            edit = $"<a title='View' href='#' style='font-size: 13px;' id='{a.CreditDetails_ID}' key='{a.CreditDetails_ID}' data-view='{a.Currency_ID}' Currency_Name='{a.Currency}'  class='editCreditDetails' editmode='false' ><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                            delete = $"<input type='checkbox' key='{a.CreditDetails_ID}' id='chk{a.CreditDetails_ID}' class='DelCreditDetails'/>",
                            Service_Credit_Limit = Convert.ToDecimal(a.Service_Credit_Limit),
                            Sales_Credit_Limit = Convert.ToDecimal(a.Sales_Credit_Limit),
                            Parts_Credit_Limit = Convert.ToDecimal(a.Parts_Credit_Limit),
                            Party_ID = a.Party_ID,
                            CreditDetails_ID = a.CreditDetails_ID,
                            Currency = a.Currency
                        }).ToList(),
                };
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonData);
        }
        #endregion


        #region ::: PartyCreditSave Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// PartyCreditSave
        /// </summary>
        public static IActionResult PartyCreditSave(string connString, PartyCreditSaveList PartyCreditSaveobj)
        {
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jObj = JObject.Parse(PartyCreditSaveobj.key);
                int rowcount = jObj["rows"].Count();

                for (int i = 0; i < rowcount; i++)
                {
                    GNM_CreditDetails psd = jObj["rows"].ElementAt(i).ToObject<GNM_CreditDetails>();

                    using (SqlConnection conn = new SqlConnection(ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString))
                    {
                        conn.Open();
                        SqlCommand cmd = new SqlCommand();
                        cmd.Connection = conn;

                        if (psd.CreditDetails_ID == 0) // Insert new record
                        {
                            cmd.CommandText = "USP_InsertPartyCreditDetails";
                            cmd.CommandType = CommandType.StoredProcedure;

                            cmd.Parameters.AddWithValue("@Party_ID", psd.Party_ID);
                            cmd.Parameters.AddWithValue("@Currency_ID", psd.Currency_ID);
                            cmd.Parameters.AddWithValue("@Parts_Credit_Limit", psd.Parts_Credit_Limit);
                            cmd.Parameters.AddWithValue("@Service_Credit_Limit", psd.Service_Credit_Limit);
                            cmd.Parameters.AddWithValue("@Sales_Credit_Limit", psd.Sales_Credit_Limit);

                            SqlParameter outputIdParam = new SqlParameter("@CreditDetails_ID", SqlDbType.Int)
                            {
                                Direction = ParameterDirection.Output
                            };
                            cmd.Parameters.Add(outputIdParam);

                            cmd.ExecuteNonQuery();

                            psd.CreditDetails_ID = (int)outputIdParam.Value; // Get the newly inserted ID
                        }
                        else // Update existing record
                        {
                            cmd.CommandText = "USP_UpdatePartyCreditDetails";
                            cmd.CommandType = CommandType.StoredProcedure;

                            cmd.Parameters.AddWithValue("@CreditDetails_ID", psd.CreditDetails_ID);
                            cmd.Parameters.AddWithValue("@Currency_ID", psd.Currency_ID);
                            cmd.Parameters.AddWithValue("@Parts_Credit_Limit", psd.Parts_Credit_Limit);
                            cmd.Parameters.AddWithValue("@Service_Credit_Limit", psd.Service_Credit_Limit);
                            cmd.Parameters.AddWithValue("@Sales_Credit_Limit", psd.Sales_Credit_Limit);

                            cmd.ExecuteNonQuery();
                        }
                    }

                    // Assuming gbl.InsertGPSDetails method handles GPS tracking or audit logging
                    //gbl.InsertGPSDetails(
                    //    Convert.ToInt32(PartyCreditSaveobj.Company_ID),
                    //    Convert.ToInt32(PartyCreditSaveobj.Branch),
                    //    Convert.ToInt32(PartyCreditSaveobj.User_ID),
                    //    Convert.ToInt32(Common.GetObjectID("CorePartyMaster")),
                    //    Convert.ToInt32(psd.Party_ID),
                    //    0, 0, "Update", false, Convert.ToInt32(PartyCreditSaveobj.MenuID)
                    //);
                }
            }
            catch (Exception ex)
            {
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(false);
        }
        #endregion


        #region ::: PartyCreditDelete Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// PartyCreditDelete
        /// </summary>
        public static IActionResult PartyCreditDelete(string connString, PartyCreditDeleteList PartyCreditDeleteonbj)
        {
            string exMsg = string.Empty;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                JObject jObj = JObject.Parse(PartyCreditDeleteonbj.key);
                int rowcount = jObj["rows"].Count();
                int id = 0;

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    for (int i = 0; i < rowcount; i++)
                    {
                        id = Convert.ToInt32(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);

                        using (SqlCommand cmd = new SqlCommand("USP_DeletePartyCreditDetails", conn))
                        {
                            cmd.CommandType = CommandType.StoredProcedure;
                            cmd.Parameters.AddWithValue("@CreditDetails_ID", id);

                            try
                            {
                                cmd.ExecuteNonQuery();
                            }
                            catch (SqlException sqlEx)
                            {
                                // Check for foreign key violation
                                if (sqlEx.Message.Contains("The DELETE statement conflicted with the REFERENCE constraint"))
                                {
                                    exMsg += CommonFunctionalities.GetGlobalResourceObject(PartyCreditDeleteonbj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords").ToString();
                                    return new JsonResult(exMsg); // Return the error message immediately
                                }
                                else
                                {
                                    throw; // Rethrow unexpected exceptions
                                }
                            }
                        }

                        // Log the deletion
                        //gbl.InsertGPSDetails(
                        //    Convert.ToInt32(PartyCreditDeleteonbj.Company_ID),
                        //    Convert.ToInt32(PartyCreditDeleteonbj.Branch),
                        //    Convert.ToInt32(PartyCreditDeleteonbj.User_ID),
                        //    Convert.ToInt32(Common.GetObjectID("CorePartyMaster")),
                        //    Convert.ToInt32(id), // Assuming this is the correct Party_ID to log
                        //    0, 0, "Delete", false, Convert.ToInt32(PartyCreditDeleteonbj.MenuID)
                        //);
                    }
                }

                // exMsg = Session["DeleteMessage"].ToString(); // Assuming this is a success message
            }
            catch (Exception ex)
            {
                // Log general exceptions
                if (LogException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                exMsg = "An error occurred while trying to delete the records."; // You can customize this message
            }
            return new JsonResult(exMsg);
        }
        #endregion


        #region ::: CorePartyMaster list and obj classes Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// CorePartyMaster list and obj classes
        /// </summary>
        /// 
        public class PartyExportList
        {
            public string partyType { get; set; }
            public int partyTypeID { get; set; }
            public int exprtType { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public string UserCulture { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public string sidx { get; set; }
            public string sord { get; set; }
            public string filter { get; set; }
            public string advanceFilter { get; set; }
            public int rows { get; set; }
            public int page { get; set; }
        }
        public class PartyCreditDeleteList
        {
            public int Company_ID { get; set; }
            public string key { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public string GeneralCulture { get; set; }
        }

        public class PartyCreditSaveList
        {
            public int Company_ID { get; set; }
            public string key { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }

        public class SelectPartyCreditDetailsList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int value { get; set; }
        }

        public class UpdateContactPersonMasterList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int id { get; set; }
            public string Data { get; set; }
        }

        public class SelectSpecificPartyContactDetailsList
        {
            public int ObjectID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int id { get; set; }
        }

        public class getContractList
        {
            public DateTime CurrentToDate { get; set; }
            public string CurrentToDateid { get; set; }
            public DateTime CurrentFromDate { get; set; }
            public string CurrentFromDateid { get; set; }
            public string key { get; set; }
        }
        public class CheckContactPersonList
        {
            public string PartyContactPersonName { get; set; }
            public string PartyContactPersonMobile { get; set; }
            public int PartyID { get; set; }
            public int PartyContactPersonID { get; set; }
        }


        public class SelectLanguageList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Company_ID { get; set; }
        }

        public class SelectCurrencyList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Company_ID { get; set; }
        }

        public class DeletePartyAddressDetailList
        {
            public string key { get; set; }
            public string UserCulture { get; set; }
        }


        public class SavePartyLocaleContactdetailsList
        {
            public string LocaleData { get; set; }
        }

        public class SavePartyLocaleAddressdetailsList
        {
            public string LocaleData { get; set; }
        }
        public class SelectSpecificPartyContactLocaleDetailsList
        {
            public int UserLanguageID { get; set; }
            public int id { get; set; }
        }

        public class SelectSpecificPartyAddressLocaleDetailsList
        {
            public int UserLanguageID { get; set; }
            public int id { get; set; }
        }

        public class SelAllPartyAddressLocaleList
        {
            public int UserLanguageID { get; set; }
            public int partyID { get; set; }
            public string UserCulture { get; set; }
        }

        public class SelectSpecificPartyAddressDetailsList
        {
            public int id { get; set; }
        }
        public class SelAllPartyAddressList
        {
            public int partyID { get; set; }
            public string UserCulture { get; set; }
        }

        public class SelectStateList
        {
            public int countryID { get; set; }
        }



        public class SavePartyAddressdetailsList
        {
            public string addressData { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }

        public class SelectAllConsigneeList
        {
            public int GeneralLanguageID { get; set; }
            public int UserLanguageID { get; set; }
            public int PartyID { get; set; }
        }

        public class SelectDealersList
        {
            public int language { get; set; }
            public int CompanyID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int UserLanguageID { get; set; }
        }

        public class SelectParentCompanyList
        {
            public int language { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
        }

        public class SelectCompanyList
        {
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
        }
        public class CheckPartyLocaleExistsList
        {
            public string name { get; set; }
            public string location { get; set; }
            public int partyID { get; set; }
            public int ParttypeID { get; set; }
            public int LanguageID { get; set; }
        }
        public class CheckPartyExistsList
        {
            public string name { get; set; }
            public string location { get; set; }
            public int partyID { get; set; }
            public int ParttypeID { get; set; }
            public int Company_ID { get; set; }
        }

        public class PartyServiceExitsList
        {
            public string key { get; set; }
        }


        public class PartyPartyTax_TaxCodeExitsList
        {
            public int name { get; set; }
            public int id { get; set; }
            public int Primid { get; set; }
        }

        public class PartyBranchExitsList
        {
            public int name { get; set; }
            public int id { get; set; }
            public int Primid { get; set; }
        }


        public class PartyAgreementNumberExitsList
        {
            public int name { get; set; }
            public int Primid { get; set; }
        }
        public class PartySkillSetExitsList
        {
            public int name { get; set; }
            public int id { get; set; }
            public int Primid { get; set; }
        }


        public class PartyProdExitsList
        {
            public int brnd { get; set; }
            public int? prod { get; set; }
            public int? mod { get; set; }
            public int partyID { get; set; }
            public int Primid { get; set; }
        }

        public class PartySegmentExitsList
        {
            public int Primid { get; set; }
            public int partyID { get; set; }
            public int prim { get; set; }
            public int sec { get; set; }
        }


        public class SelectFieldSearchJobCardList
        {
            public int Company_ID { get; set; }
            public string Key { get; set; }
            public int PartyID { get; set; }
        }

        public class SelPartyContractDetailsList
        {
            public int value { get; set; }
            public bool IsGeneral { get; set; }
            public int LanguageID { get; set; }
        }

        public class SelCreditLimitLogDetailsList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int PartyID { get; set; }
            public string Currency { get; set; }
        }

        public class SelLockLogDetailsList
        {
            public int PartyID { get; set; }
        }
        public class SelectPartyProductDetailsList
        {
            public int value { get; set; }
            public bool IsGeneral { get; set; }
            public int LanguageID { get; set; }
            public int PartyType { get; set; }
        }

        public class DeletePartyServiceScheduleList
        {
            public int Company_ID { get; set; }
            public string key { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public string UserCulture { get; set; }
            public string GeneralCulture { get; set; }
        }
        public class SavePartyServiceScheduleList
        {
            public int Company_ID { get; set; }
            public string key { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }

        }
        public class SelectPartyServiceScheduleList
        {
            public int value { get; set; }
            public bool IsGeneral { get; set; }
            public int LanguageID { get; set; }
            public int Company_ID { get; set; }
            public string UserCulture { get; set; }
            public string GeneralCulture { get; set; }
        }

        public class DeletePartyProductAssociationList
        {
            public int Company_ID { get; set; }
            public string key { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public string UserCulture { get; set; }
            public string GeneralCulture { get; set; }
        }
        public class SavePartyProductAssociationList
        {
            public int Company_ID { get; set; }
            public string key { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }

        }

        public class SelectPartyProductAssociationList
        {
            public int value { get; set; }
            public bool IsGeneral { get; set; }
            public int LanguageID { get; set; }
            public int Company_ID { get; set; }
            public string UserCulture { get; set; }

        }

        public class DeletePartyBranchList
        {
            public int Company_ID { get; set; }
            public string key { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public string UserCulture { get; set; }
            public string GeneralCulture { get; set; }

        }
        public class SavePartyBranchList
        {
            public int Company_ID { get; set; }
            public string key { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }

        }

        public class SelectPartyBranchList
        {
            public int value { get; set; }
            public bool IsGeneral { get; set; }
            public int LanguageID { get; set; }
        }


        public class DeletePartyContractDtlList
        {
            public int Company_ID { get; set; }
            public string key { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public string UserCulture { get; set; }
            public string GeneralCulture { get; set; }
        }
        public class DeletePartySkillSetList
        {
            public int Company_ID { get; set; }
            public string key { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public string UserCulture { get; set; }
            public string GeneralCulture { get; set; }
        }

        public class SavePartyContractDtlList
        {
            public string key { get; set; }
        }

        public class SavePartySkillSetList
        {
            public int Company_ID { get; set; }
            public string key { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }

        public class SelectPartySkillSetList
        {
            public string UserCulture { get; set; }
            public int value { get; set; }
            public bool IsGeneral { get; set; }
        }
        public class PartySegmentDeleteList
        {
            public int Company_ID { get; set; }
            public string key { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public string GeneralCulture { get; set; }
        }
        public class PartySegmentSaveList
        {
            public string key { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }

        public class SelectPartySegmentDetailList
        {
            public int LanguageID { get; set; }
            public int value { get; set; }
            public bool IsGeneral { get; set; }
            public string UserCulture { get; set; }
        }

        public class DeletePartyTaxDiscountList
        {
            public int Company_ID { get; set; }
            public string key { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public string GeneralCulture { get; set; }
            public string UserCulture { get; set; }
        }

        public class SelectPartyTaxDiscountDetailsList
        {
            public string UserCulture { get; set; }
            public int value { get; set; }
        }

        public class PartyTaxDiscountSaveList
        {
            public string TaxDiscountData { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }

        public class DeletePartyTaxDetailList
        {
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public string key { get; set; }
            public string UserCulture { get; set; }
        }

        public class DeletePartyContactDetailList
        {
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public string key { get; set; }
            public string UserCulture { get; set; }
        }

        public class PartyMasterDeletelist
        {
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public string key { get; set; }
            public string UserCulture { get; set; }

        }

        public class PartyTaxSaveList
        {
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
            public string key { get; set; }
        }
        public class PartyContactPersonSaveList
        {
            public string key { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }

        public class PartyMasterSaveLocaleList
        {
            public string key { get; set; }
            public int UserLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public int MenuID { get; set; }
        }
        public class PartyMasterSaveList
        {
            public List<GNM_User> UserDetails { get; set; }
            public int Company_ID { get; set; }
            public string key { get; set; }
            public int Branch { get; set; }
            public int User_ID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public int MenuID { get; set; }
        }
        public class StateMasterDataLocaleList
        {
            public int id { get; set; }
        }

        public class StateMasterDataList
        {
            public int id { get; set; }
        }

        public class PartyMasterDataList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Company_ID { get; set; }
        }

        public class SelectPartyTaxDetailsList
        {
            public int value { get; set; }
            public string UserCulture { get; set; }
        }

        public class SelectPartyContactDetailsList
        {
            public int value { get; set; }
            public int LanguageID { get; set; }
            public string UserCulture { get; set; }
            public bool IsGeneral { get; set; }
        }
        public class CheckBranchTaxDetailsList
        {
            public int Party_ID { get; set; }
            public int primaryKey { get; set; }
            public int taxStructureID { get; set; }
        }
        public class DeletePartyTaxStructureDetailsList
        {
            public string key { get; set; }
            public string UserCulture { get; set; }
        }
        public class SavePartyTaxStructureDetailList
        {
            public string PartyTaxStructureData { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public int MenuID { get; set; }
        }
        public class SelectPartyTaxStructureDetailsList
        {
            public List<GNM_User> UserDetails { get; set; }
            public string UserCulture { get; set; }
            public int Company_ID { get; set; }
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public bool IsGeneral { get; set; }
            public int Party_ID { get; set; }
        }

        public class SelectParticularPartyList
        {
            public int UserLanguageID { get; set; }
            public int GeneralLanguageID { get; set; }
            public int Company_ID { get; set; }
            public int Branch { get; set; }
            public List<GNM_User> UserDetails { get; set; }
            public int MenuID { get; set; }
            public DateTime LoggedINDateTime { get; set; }
            public int id { get; set; }
        }

        public class SelectPartyMasterLocaleList
        {
            public string UserCulture { get; set; }
            public int Company_ID { get; set; }
            public int partyType { get; set; }
            public int UserLanguageID { get; set; }
        }
        public class SelectSecondarySegmentlist
        {
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
            public int id { get; set; }
        }
        public class SelectModelTypeList
        {
            public int id { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
        }
        public class SelectProductTypeLista
        {
            public int id { get; set; }
            public string GeneralLanguageCode { get; set; }
            public string UserLanguageCode { get; set; }
        }

        public class SelectMastersLocaleCorePartyMasterList
        {
            public int Company_ID { get; set; }
            public string UserCulture { get; set; }
            public int LanguageID { get; set; }
            public int UserLanguageID { get; set; }
            public int value { get; set; }
        }

        public class SelectMastersCorePartyMasterList
        {
            public int Company_ID { get; set; }
            public int value { get; set; }
            public string GeneralCulture { get; set; }
        }

        public class SelectPartyMasterList
        {
            public int partyType { get; set; }
            public string PartywhereCon { get; set; }
            public int Company_ID { get; set; }
            public string UserCulture { get; set; }

        }
        #endregion


        #region ::: PartyProductDetail  classes Uday Kumar J B 22-08-2024:::
        /// <summary>
        /// PartyProductDetail
        /// </summary>
        public class PartyProductDetail
        {
            public int Product_ID { get; set; }
            public string Brand { get; set; }
            public string ProductType { get; set; }
            public string Model { get; set; }
            public string SerialNumber { get; set; }
            public string Name { get; set; }
        }
        public class PartyDiscount
        {
            public int PartyDiscount_ID { get; set; }
            public int Party_ID { get; set; }
            public decimal Parts_Discount { get; set; }
            public decimal Service_Discount { get; set; }
            public string Effective_Date { get; set; }
            public string edit { get; set; }
            public string delete { get; set; }
        }


        public class PartyContactPerson
        {
            public int PartyContactPerson_ID { get; set; }
            public string PartyContactPerson_IsActive { get; set; }
            public string Party_IsDefaultContact { get; set; }
            public bool IsDefault { get; set; }
            public string PartyContactPerson_Name { get; set; }
            public string PartyContactPerson_DOB { get; set; }
            public string PartyContactPerson_Department { get; set; }
            public string PartyContactPerson_Email { get; set; }
            public string PartyContactPerson_Phone { get; set; }
            public string PartyContactPerson_Mobile { get; set; }
            public string PartyContactPerson_Remarks { get; set; }
            public string Local { get; set; }
        }


        public class QuestionnaireLevelData
        {
            public int ID { get; set; }
            public string Name { get; set; }
        }
        public partial class GNMCreditDetails
        {
            public string edit { get; set; }
            public string delete { get; set; }
            public int CreditDetails_ID { get; set; }
            public Nullable<int> Currency_ID { get; set; }
            public string Currency { get; set; }
            public Nullable<int> Party_ID { get; set; }
            public Nullable<decimal> Parts_Credit_Limit { get; set; }
            public Nullable<decimal> Service_Credit_Limit { get; set; }
            public Nullable<decimal> Sales_Credit_Limit { get; set; }

            public virtual GNM_Party GNM_Party { get; set; }
        }
        public partial class Core_JobCardHeader
        {
            public int JobCard_ID { get; set; }
            public Nullable<int> Company_ID { get; set; }
            public Nullable<int> Branch_ID { get; set; }
            public string JobCardNumber { get; set; }
            public Nullable<System.DateTime> JobCardDate { get; set; }
            public Nullable<int> JobCardStatus_ID { get; set; }
            public Nullable<int> ServiceRequest_ID { get; set; }
            public Nullable<int> Quotation_ID { get; set; }
            public int Party_ID { get; set; }
            public Nullable<int> Model_ID { get; set; }
            public Nullable<int> Brand_ID { get; set; }
            public Nullable<int> ProductType_ID { get; set; }
            public string SerialNumber { get; set; }
            public Nullable<int> Reading { get; set; }
            public int JobPriority { get; set; }
            public bool IsComponent { get; set; }
            public Nullable<int> DelayReason_ID { get; set; }
            public Nullable<System.DateTime> ActualStartDate { get; set; }
            public Nullable<System.DateTime> ActualEndDate { get; set; }
            public Nullable<System.DateTime> PlannedStartDate { get; set; }
            public string ActionForNextService { get; set; }
            public string CustomerComplaint { get; set; }
            public string CauseOfFailure { get; set; }
            public string CorrectiveAction { get; set; }
            public Nullable<System.DateTime> PlannedCompletionDate { get; set; }
            public Nullable<int> FailureReading { get; set; }
            public Nullable<int> RepairReading { get; set; }
            public Nullable<int> ProductLocation_ID { get; set; }
            public Nullable<int> ServiceType_ID { get; set; }
            public Nullable<int> PrimarySegment_ID { get; set; }
            public Nullable<int> SecondarySegment_ID { get; set; }
            public Nullable<int> DocumentNumber { get; set; }
            public Nullable<int> FinancialYear { get; set; }
            public Nullable<int> PartyContactPersonDetails_ID { get; set; }
            public Nullable<int> MachineStatus_ID { get; set; }
            public Nullable<int> Campaign_ID { get; set; }
            public string TransactionNumber { get; set; }
            public Nullable<int> Billto_ID { get; set; }
            public Nullable<int> CancelledReasn_ID { get; set; }
            public Nullable<int> Charge_To { get; set; }
            public string CouponNumber { get; set; }
            public Nullable<int> InsuranceParty_ID { get; set; }
            public string QuotationNumber { get; set; }
            public string ServiceRequestNumber { get; set; }
            public bool IsUnderwarranty { get; set; }
            public Nullable<int> ProductCSA_ID { get; set; }
            public Nullable<int> Invoice_ID { get; set; }
            public string InvoiceNumber { get; set; }
            public Nullable<int> InternalInvoice_ID { get; set; }
            public Nullable<int> ServiceInvoice_ID { get; set; }
            public Nullable<int> WarrantyClaim_ID { get; set; }
            public Nullable<int> ProductWarranty_ID { get; set; }
            public Nullable<int> MandatoryClaim_ID { get; set; }
            public Nullable<short> AttachmentCount { get; set; }
            public string Remarks { get; set; }
            public Nullable<bool> IsEarlyWarning { get; set; }
            public Nullable<decimal> ServiceChargeAmount { get; set; }
            public byte JobCardVersion { get; set; }
            public bool IsArchived { get; set; }
            public string GUIDJobCard { get; set; }
            public Nullable<byte> IsDealerList { get; set; }
            public Nullable<bool> IsBreakdown { get; set; }
            public Nullable<System.DateTime> JobActualStartDateTime { get; set; }
            public Nullable<System.DateTime> JobActualEndDateTime { get; set; }
            public Nullable<bool> IsNewJobAdded { get; set; }
            public Nullable<bool> IsInsideWork { get; set; }
            public string GatePass_No { get; set; }
            public Nullable<System.DateTime> GatePass_InDate { get; set; }
            public Nullable<System.DateTime> GatePass_OutDate { get; set; }
            public Nullable<decimal> GatePass_OutsideCharges { get; set; }
            public Nullable<int> Bay_ID { get; set; }
            public Nullable<System.DateTime> JobPlannedCompletionDate { get; set; }
            public string JobNumber { get; set; }
            public Nullable<System.DateTime> JobDate { get; set; }
            public Nullable<int> Job_SequenceID { get; set; }
            public Nullable<int> Current_AssignTo { get; set; }
            public Nullable<int> WO_ParentID { get; set; }
            public Nullable<int> ParentQuotation_ID { get; set; }
            public Nullable<int> OutsidePartner_ID { get; set; }
            public string Product_UniqueNumber { get; set; }
            public Nullable<decimal> AmountReceived { get; set; }
            public Nullable<System.DateTime> JobPlannedStartDate { get; set; }
            public Nullable<int> WorkSiteAddress_ID { get; set; }
            public Nullable<decimal> PartyDiscount { get; set; }
            public string EarlyWarningComments { get; set; }
            public Nullable<int> ContractorID { get; set; }
            public Nullable<int> ContractorContactPerson_ID { get; set; }
            public Nullable<int> Charge_To_Type { get; set; }
            public Nullable<bool> IsRework { get; set; }
            public Nullable<bool> IsUpsell { get; set; }
            public Nullable<int> Rework_WorkOrderID { get; set; }
            public Nullable<int> UpsellEmployee_ID { get; set; }
            public Nullable<int> CustomerRating { get; set; }
            public Nullable<System.DateTime> CustomerFeedBackDate { get; set; }
            public string CustomerFeedBack { get; set; }
            public Nullable<bool> IsNegetiveFeedBack { get; set; }
            public string PONumber { get; set; }
            public string FrontViewRemarks { get; set; }
            public string RearViewRemarks { get; set; }
            public string FrontRightViewRemarks { get; set; }
            public string FrontLeftViewRemarks { get; set; }
            public Nullable<decimal> StreetSideFrontTypePressure { get; set; }
            public Nullable<decimal> StreetSideRearType1Pressure { get; set; }
            public Nullable<decimal> StreetSideRearType2Pressure { get; set; }
            public Nullable<decimal> CurbSideFrontTypePressure { get; set; }
            public Nullable<decimal> CurbSideRearType1Pressure { get; set; }
            public Nullable<decimal> CurbSideRearType2Pressure { get; set; }
            public Nullable<bool> IsSentToSAP { get; set; }
            public Nullable<int> FixedPrice_ID { get; set; }
            public Nullable<decimal> FuelGaugeBefore { get; set; }
            public Nullable<decimal> FuelGaugeAfter { get; set; }
            public string SAPInvoiceNumber { get; set; }
            public Nullable<decimal> USCADExchangeRate { get; set; }
            public Nullable<bool> IsUpdateQuoteParts { get; set; }
            public Nullable<bool> IsUpdateQuotelabour { get; set; }
            public Nullable<bool> IsUpdateQuoteMisc { get; set; }
            public Nullable<bool> IsFristApproval { get; set; }
            public Nullable<bool> IsEdited { get; set; }
            public Nullable<int> EditedBy { get; set; }
            public Nullable<bool> IsReadyForSAPprocess { get; set; }
            public Nullable<bool> AdditionJobEstimate { get; set; }
            public Nullable<bool> IsVehicleArrived { get; set; }
            public Nullable<System.DateTime> SAPInvoiceDate { get; set; }
            public Nullable<byte> AddresseType { get; set; }
            public Nullable<decimal> BilledInvoicePayableHours { get; set; }
            public Nullable<decimal> ActualInvoicePayableHours { get; set; }
            public Nullable<decimal> BilledInternalInvoiceHours { get; set; }
            public Nullable<decimal> ActualInternalInvoiceHours { get; set; }
            public Nullable<decimal> BilledWarrantyChargedHours { get; set; }
            public Nullable<decimal> ActualWarrantyChargedHours { get; set; }
            public string Billed_VS_Actual_DiscrepancyReason { get; set; }
            public Nullable<int> BilledActualSI_Reason_ID { get; set; }
            public Nullable<int> BilledActualII_Reason_ID { get; set; }
            public Nullable<int> BilledActualWC_Reason_ID { get; set; }
            public Nullable<int> SAPInvoiceErrorStatus_ID { get; set; }
            public Nullable<bool> IsVariance { get; set; }
            public Nullable<bool> IsBranchMobileRate { get; set; }
            public Nullable<int> WarrantyType_ID { get; set; }
            public Nullable<System.DateTime> ModifiedBy { get; set; }
            public string KeyTagNumber { get; set; }
            public Nullable<int> ScheduledType_ID { get; set; }
            public Nullable<int> Year { get; set; }
            public Nullable<byte> Month { get; set; }
            public Nullable<System.DateTime> SentDate { get; set; }
            public Nullable<bool> IsAllWOClosed { get; set; }
            public Nullable<int> ShipTo_ID { get; set; }
            public Nullable<System.DateTime> ActualDepartureDateTime { get; set; }
            public Nullable<System.DateTime> ActualArrivalDateTime { get; set; }
            public Nullable<byte> TechScheduleStatus { get; set; }
        }

        public class partydetails
        {
            public int Party_ID { get; set; }
            public bool Party_IsActive { get; set; }
            public bool Party_IsLocked { get; set; }
            public string Party_Code { get; set; }
            public string Party_Name { get; set; }
            public string Party_Location { get; set; }
            public string Party_Email { get; set; }
            public string Party_Phone { get; set; }
            public string Party_Fax { get; set; }
            public string PartyAddress_Address { get; set; }
            public string Party_PaymentTerms { get; set; }
            public byte PartyType { get; set; }
            public string Party_Mobile { get; set; }
            public int ModifiedBY { get; set; }
            public System.DateTime ModifiedDate { get; set; }
            public int? Country_ID { get; set; }
            public int? State_ID { get; set; }
            public int? Company_ID { get; set; }
            public bool? IsOEM { get; set; }
            public bool? IsDealer { get; set; }
            public int? Relationship_Branch_ID { get; set; }
            public int? Relationship_Company_ID { get; set; }
            public decimal? PartsCreditLimit { get; set; }
            public decimal? SalesCreditLimit { get; set; }
            public int? Currency_ID { get; set; }
            public bool IsImportExport { get; set; }
            public decimal? ServiceCreditLimit { get; set; }
            public decimal? ServiceCreditLimitinUSD { get; set; }
            public int? PaymentDueDays { get; set; }
            public decimal? PartsOutStandingCredit { get; set; }
            public decimal? ServiceOutStandingCredit { get; set; }
            public decimal? PartyAdvanceAmount { get; set; }
            public decimal? RemanOutStandingCredit { get; set; }
            public decimal? SalesOutStandingCredit { get; set; }
            public int? CustomerType_ID { get; set; }
            public string Currency { get; set; }
            public string Country { get; set; }
            public string State { get; set; }
            public string PartyTypestring { get; set; }
            public string Isforeign { get; set; }
            public string IsActiveString { get; set; }
            public string IsDealerString { get; set; }
            public string IsOEMString { get; set; }
            public string Company { get; set; }
            public string IslockString { get; set; }
            public string CustomerTypestr { get; set; }
            public byte? CustomerType { get; set; }

        }


        public class PartySegmentDetail
        {
            int partySegmentID = 0;
            public int PartySegment_ID
            {
                get
                {
                    return partySegmentID;
                }
                set
                {
                    partySegmentID = value;
                }
            }
            string primarysegment = string.Empty;
            public string PrimarySegment
            {
                get
                {
                    return primarysegment;
                }
                set
                {
                    primarysegment = value;
                }
            }
            string secondarysegment = string.Empty;
            public string SecondarySegment
            {
                get
                {
                    return secondarysegment;
                }
                set
                {
                    secondarysegment = value;
                }
            }
            bool isactive = false;
            public bool IsActive
            {
                get
                {
                    return isactive;
                }
                set
                {
                    isactive = value;
                }
            }


        }

        public class PartyCreditLimitLog
        {
            public int ID { get; set; }
            public int? Party_ID { get; set; }
            public string Currency { get; set; }
            public string UpdatedDateTime { get; set; }
            public string CreditLimit { get; set; }
            public string Remarks { get; set; }
        }
        public class ParentCompanyObject
        {
            public int Company_ID
            {
                get;
                set;
            }

            public string Company_Name
            {
                get;
                set;
            }

            public int Company_Parent_ID
            {
                get;
                set;
            }
        }

        public class PartyBranch
        {
            int partybranchid = 0;
            public int PartyBranch_ID
            {
                get
                {
                    return partybranchid;
                }
                set
                {
                    partybranchid = value;
                }
            }
            string branch = string.Empty;
            public string Branch
            {
                get
                {
                    return branch;
                }
                set
                {
                    branch = value;
                }
            }
            int branchid = 0;
            public int Branch_ID
            {
                get
                {
                    return branchid;
                }
                set
                {
                    branchid = value;
                }
            }
            int partyid = 0;
            public int Party_ID
            {
                get
                {
                    return partyid;
                }
                set
                {
                    partyid = value;
                }
            }
        }

        public class PartyProductAssociation
        {
            public int PartyProduct_ID
            {
                get;
                set;
            }
            public string Brand
            {
                get;
                set;
            }
            public int BrandID
            {
                get;
                set;
            }

            public int ProductTypeID
            {
                get;
                set;
            }
            public string ProductType
            {
                get;
                set;
            }

            public int ModelID
            {
                get;
                set;
            }
            public string Model
            {
                get;
                set;
            }
            public bool Model_IsActive
            {
                get;
                set;
            }

            public int Party_ID
            {
                get;
                set;
            }
            public bool IsFixedPrice { get; set; }

        }

        public class PartyContractDetail
        {
            public int PartyContract_ID { get; set; }
            public string AgreementNumber { get; set; }
            public string FromDate { get; set; }
            public string ToDate { get; set; }
            public decimal ContractValue { get; set; }
            public string Unit { get; set; }
            public string Currency { get; set; }
            public string Remarks { get; set; }
        }

        public class PartyServiceSchedule
        {

            public int PartyServiceSchedule_ID
            {
                get;

                set;

            }
            public int JobCardID
            {
                get;

                set;

            }
            public string ServiceType
            {
                get;

                set;
            }




            public string JobCard
            {
                get;

                set;
            }



            public string Status
            {
                get;

                set;
            }


            public string Closure_Reason
            {
                get;

                set;

            }


            public string ServiceDate
            {
                get;

                set;
            }

        }

        public partial class GNM_ProductType
        {
            public GNM_ProductType()
            {
                this.GNM_ProductTypeLocale = new HashSet<GNM_ProductTypeLocale>();
            }

            public int ProductType_ID { get; set; }
            public int Brand_ID { get; set; }
            public string ProductType_Name { get; set; }
            public bool ProductType_IsActive { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }

            public virtual ICollection<GNM_ProductTypeLocale> GNM_ProductTypeLocale { get; set; }
        }
        public partial class GNM_ProductTypeLocale
        {
            public int ProductTypeLocale_ID { get; set; }
            public int ProductType_ID { get; set; }
            public string ProductType_Name { get; set; }
            public int Language_ID { get; set; }

            public virtual GNM_ProductType GNM_ProductType { get; set; }
        }
        public partial class GNM_ModelQuickListDetails
        {
            public GNM_ModelQuickListDetails()
            {
                this.GNM_ModelQuickListDetailsLocale = new HashSet<GNM_ModelQuickListDetailsLocale>();
            }

            public int ModelQuickChecklist_ID { get; set; }
            public int Model_ID { get; set; }
            public string Description { get; set; }
            public bool IsMandatory { get; set; }
            public bool IsPhotoRequired { get; set; }
            public Nullable<byte> IsDefaultCheck { get; set; }
            public string Minvalue { get; set; }
            public string Maxvalue { get; set; }

            public virtual ICollection<GNM_ModelQuickListDetailsLocale> GNM_ModelQuickListDetailsLocale { get; set; }
            public virtual GNM_Model GNM_Model { get; set; }
        }
        public partial class GNM_ModelPriceDetails
        {
            public int ModelPriceDetails_ID { get; set; }
            public int Model_ID { get; set; }
            public decimal ListPrice { get; set; }
            public System.DateTime EffectiveFrom { get; set; }
            public int Company_ID { get; set; }

            public virtual GNM_Model GNM_Model { get; set; }
        }
        public partial class GNM_ModelLocale
        {
            public int ModelLocale_ID { get; set; }
            public int Model_ID { get; set; }
            public string Model_Name { get; set; }
            public int Language_ID { get; set; }
            public string Model_Description { get; set; }

            public virtual GNM_Model GNM_Model { get; set; }
        }
        public partial class GNM_Model
        {
            public GNM_Model()
            {
                this.GNM_ModelLocale = new HashSet<GNM_ModelLocale>();
                this.GNM_ModelPriceDetails = new HashSet<GNM_ModelPriceDetails>();
                this.GNM_ModelQuickListDetails = new HashSet<GNM_ModelQuickListDetails>();
                this.GNM_ModelRedCarpetListDetails = new HashSet<GNM_ModelRedCarpetListDetails>();
                this.GNM_ModelSalesPriceDetails = new HashSet<GNM_ModelSalesPriceDetails>();
                this.GNM_ModelServiceChargeDetail = new HashSet<GNM_ModelServiceChargeDetail>();
                this.GNM_MODELSERVICETYPEDET = new HashSet<GNM_MODELSERVICETYPEDET>();
                this.GNM_ModelWarrantyDefinitionDetails = new HashSet<GNM_ModelWarrantyDefinitionDetails>();
            }

            public int Model_ID { get; set; }
            public int ProductType_ID { get; set; }
            public int Brand_ID { get; set; }
            public string Model_Name { get; set; }
            public bool Model_IsActive { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }
            public Nullable<int> ServiceType_ID { get; set; }
            public Nullable<int> ServiceFrequency { get; set; }
            public string Model_Description { get; set; }
            public Nullable<byte> AttachmentCount { get; set; }
            public string Series { get; set; }

            public virtual ICollection<GNM_ModelLocale> GNM_ModelLocale { get; set; }
            public virtual ICollection<GNM_ModelPriceDetails> GNM_ModelPriceDetails { get; set; }
            public virtual ICollection<GNM_ModelQuickListDetails> GNM_ModelQuickListDetails { get; set; }
            public virtual ICollection<GNM_ModelRedCarpetListDetails> GNM_ModelRedCarpetListDetails { get; set; }
            public virtual ICollection<GNM_ModelSalesPriceDetails> GNM_ModelSalesPriceDetails { get; set; }
            public virtual ICollection<GNM_ModelServiceChargeDetail> GNM_ModelServiceChargeDetail { get; set; }
            public virtual ICollection<GNM_MODELSERVICETYPEDET> GNM_MODELSERVICETYPEDET { get; set; }
            public virtual ICollection<GNM_ModelWarrantyDefinitionDetails> GNM_ModelWarrantyDefinitionDetails { get; set; }
        }
        public partial class GNM_ModelWarrantyDefinitionDetails
        {
            public int ModelWarrantyDefinitionDetails_ID { get; set; }
            public int Model_ID { get; set; }
            public Nullable<int> WarrantyType_ID { get; set; }
            public Nullable<int> ServiceType_ID { get; set; }
            public Nullable<bool> Applicable_Type { get; set; }
            public int WarrantyMonths { get; set; }
            public int WarrantyHours { get; set; }
            public System.DateTime EffectiveFrom { get; set; }
            public int Company_ID { get; set; }

            public virtual GNM_Model GNM_Model { get; set; }
        }

        public partial class GNM_MODELSERVICETYPEDET
        {
            public int MODELSERVICETYPEDET_ID { get; set; }
            public int MODEL_ID { get; set; }
            public int SERVICETYPE_ID { get; set; }
            public int COMPANY_ID { get; set; }

            public virtual GNM_Model GNM_Model { get; set; }
        }
        public partial class GNM_ModelServiceChargeDetail
        {
            public int ModelServiceChargeDetail_ID { get; set; }
            public Nullable<int> Model_ID { get; set; }
            public Nullable<int> ServiceType_ID { get; set; }
            public Nullable<decimal> ServiceCharge { get; set; }
            public Nullable<int> Company_ID { get; set; }
            public Nullable<System.DateTime> EffectiveDate { get; set; }

            public virtual GNM_Model GNM_Model { get; set; }
        }
        public partial class GNM_ModelSalesPriceDetails
        {
            public int ModelSalesPriceDetails_ID { get; set; }
            public int Model_ID { get; set; }
            public decimal ListPrice { get; set; }
            public decimal DealerNetPrice { get; set; }
            public decimal MRP { get; set; }
            public System.DateTime EffectiveFrom { get; set; }

            public virtual GNM_Model GNM_Model { get; set; }
        }
        public partial class GNM_ModelRedCarpetListDetailsLocale
        {
            public int ModelRedCarpetChecklistLocale_ID { get; set; }
            public int ModelRedCarpetChecklist_ID { get; set; }
            public string Description { get; set; }
            public Nullable<int> Language_ID { get; set; }

            public virtual GNM_ModelRedCarpetListDetails GNM_ModelRedCarpetListDetails { get; set; }
        }
        public partial class GNM_ModelRedCarpetListDetails
        {
            public GNM_ModelRedCarpetListDetails()
            {
                this.GNM_ModelRedCarpetListDetailsLocale = new HashSet<GNM_ModelRedCarpetListDetailsLocale>();
            }

            public int ModelRedCarpetChecklist_ID { get; set; }
            public int Model_ID { get; set; }
            public string Description { get; set; }
            public bool IsMandatory { get; set; }
            public bool IsPhotoRequired { get; set; }

            public virtual ICollection<GNM_ModelRedCarpetListDetailsLocale> GNM_ModelRedCarpetListDetailsLocale { get; set; }
            public virtual GNM_Model GNM_Model { get; set; }
        }
        public partial class GNM_ModelQuickListDetailsLocale
        {
            public int ModelQuickChecklistLocale_ID { get; set; }
            public int ModelQuickChecklist_ID { get; set; }
            public string Description { get; set; }
            public Nullable<int> Language_ID { get; set; }

            public virtual GNM_ModelQuickListDetails GNM_ModelQuickListDetails { get; set; }
        }
        public partial class GNM_StateLocale
        {
            public int StateLocale_ID { get; set; }
            public int State_ID { get; set; }
            public string State_Name { get; set; }
            public int Language_ID { get; set; }

            public virtual GNM_State GNM_State { get; set; }
        }
        public partial class GNM_State
        {
            public GNM_State()
            {
                this.GNM_StateLocale = new HashSet<GNM_StateLocale>();
            }

            public int State_ID { get; set; }
            public int Country_ID { get; set; }
            public string State_Name { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }
            public bool State_IsActive { get; set; }
            public Nullable<int> Region_ID { get; set; }
            public string StateCode { get; set; }
            public string Stcd { get; set; }

            public virtual ICollection<GNM_StateLocale> GNM_StateLocale { get; set; }
        }

        public partial class GNM_SecondarySegment
        {
            public GNM_SecondarySegment()
            {
                this.GNM_SecondarySegmentLocale = new HashSet<GNM_SecondarySegmentLocale>();
            }

            public int SecondarySegment_ID { get; set; }
            public int PrimarySegment_ID { get; set; }
            public bool SecondarySegment_IsActive { get; set; }
            public string SecondarySegment_Description { get; set; }
            public int ModifiedBy { get; set; }
            public System.DateTime ModifiedDate { get; set; }

            public virtual ICollection<GNM_SecondarySegmentLocale> GNM_SecondarySegmentLocale { get; set; }
        }
        #endregion


    }
}
