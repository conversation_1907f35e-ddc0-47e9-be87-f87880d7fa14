﻿using Microsoft.IdentityModel.Tokens;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IdentityModel.Tokens.Jwt;
using System.Reflection;
using System.Resources;
using System.Security.Claims;

namespace SharedAPIClassLibrary_DC
{
    public class UserLoginModel
    {
        public string Username { get; set; }
        public string Password { get; set; }
        // Add other properties as needed
    }
    public class JwtService
    {
        private readonly string _secretKey;
        private static SqlCommand sqlComm;
        private static SqlConnection conn;

        Common Commo = new Common();
        public static string isPreviewAuthorValue = string.Empty;
        public JwtService(string secretKey)
        {
            _secretKey = secretKey;
        }
        private static readonly string SecretKey = "CwF5uSoYuSGhpVz9xKQovBZWjJorr/zUxMkByRP4FRE="; // Store this securely
        private static readonly string Issuer = "https://localhost:44317/api/JWT/JWTGenOnLogin"; // Replace with your API's URL
        private static readonly string Audience = "DCPARTY$46"; // Customize as needed
        private static readonly int TokenExpiryMinutes = 60; // Token expiration time

        public string GenerateJwtTokenGenazure()
        {
            var tokenString = "";
            tokenString = GenerateJwtToken();

            return tokenString;
        }
        public string GenerateJwtToken()
        {
            var securityKey = new SymmetricSecurityKey(Convert.FromBase64String(SecretKey));
            var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);
            var header = new JwtHeader(credentials);

            // Create the token payload (claims)
            var claims = new[]
            {
            //new Claim(ClaimTypes.Name, model.Username),
            new Claim("exp", DateTimeOffset.Now.AddMinutes(TokenExpiryMinutes).ToUnixTimeSeconds().ToString()),
            new Claim("iss", Issuer),
            new Claim("aud", Audience),
        };

            var payload = new JwtPayload(claims);

            // Create the JWT token
            var jwtSecurityToken = new JwtSecurityToken(header, payload);
            var tokenString = "";

            try
            {
                // Sign the token to create the signature
                var tokenHandler = new JwtSecurityTokenHandler();
                tokenString = tokenHandler.WriteToken(jwtSecurityToken);
            }
            catch (Exception ex)
            {
                // Handle exceptions (log or rethrow)
            }

            return tokenString;
        }


        ////public static IActionResult GetCatalogueAssemblyBOMList(NovaCatalougeBOMInitialLoad NovaCatalougeBOMInitialLoadObj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters,string Connectionstring)
        ////{

        ////    Common Commo = new Common();
        ////    var jsonReader = default(dynamic);
        ////    string Conn = Connectionstring; 
        ////    int TotalPages = 0;
        ////    int TotalRecords = 0;
        ////    int Service = 0;

        ////    //string sidx = HttpContext.Current.Request.Params["sidx"];
        ////    //int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
        ////    //int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
        ////    //string sord = HttpContext.Current.Request.Params["sord"];
        ////    //bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
        ////    //long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
        ////    //string filters = "";
        ////    //if (HttpContext.Current.Request.Params["filters"] == null)
        ////    //{
        ////    //    filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
        ////    //}
        ////    //else
        ////    //{
        ////    //    filters = HttpContext.Current.Request.Params["filters"];
        ////    //}


        ////    IEnumerable<PartAssemblyViewer> BOMIE = null;
        ////    IQueryable<PartAssemblyViewer> BOMIQ = null;
        ////    List<PartAssemblyViewer> BOMList = new List<PartAssemblyViewer>();
        ////    int UserID = Convert.ToInt32(NovaCatalougeBOMInitialLoadObj.UserID);
        ////    int Part_Assembly_ID = 0;
        ////    if (NovaCatalougeBOMInitialLoadObj.Part_Assembly_No != "" && NovaCatalougeBOMInitialLoadObj.Part_Assembly_No != null)
        ////    {
        ////        Part_Assembly_ID = Convert.ToInt32(NovaCatalougeBOMInitialLoadObj.Part_Assembly_No);
        ////    }
        ////    else
        ////    {
        ////        Part_Assembly_ID = Convert.ToInt32(NovaCatalougeBOMInitialLoadObj.Section);
        ////    }


        ////    string cmdText = "";
        ////    int ShortNumber = 0;
        ////    string ShortCode = string.Empty;
        ////    string ModelName = string.Empty;            
        ////    PartAssemblyHeader AssyDetails = new PartAssemblyHeader();
        ////    try
        ////    {
        ////        using (SqlConnection ConnSql = new SqlConnection(Conn))
        ////        {
        ////            SqlDataReader DataReader = null;
        ////            string Query = null;
        ////            if (NovaCatalougeBOMInitialLoadObj.BrandCode.ToString().ToUpper() == "PREVOST")
        ////            {
        ////                cmdText = "Usp_Sel_User_Drawing_BOM_List1";
        ////                if (NovaCatalougeBOMInitialLoadObj.Lang.ToString().ToUpper() == "EN")
        ////                {
        ////                    Query = ("SELECT Part_Assembly_No, Part_Assembly_Description, IIF(Bookmark_ID IS NULL, 0, Bookmark_ID) AS BID, Part_Assembly_ID FROM MA_Part_Assembly " +
        ////                                " LEFT JOIN TR_Manage_Bookmarks ON Bookmark_Part_Assembly_ID = Part_Assembly_ID AND Bookmark_User_ID=" + UserID +
        ////                                " WHERE Part_Assembly_No = (SELECT Catalogue_OrderNo FROM MA_Catalogue WHERE Catalogue_ID = (SELECT Vehicle_Catalogue_ID FROM MA_Vehicle WHERE Vehicle_ID = " + Part_Assembly_ID + "))");
        ////                }
        ////                else
        ////                {
        ////                    Query = ("SELECT Part_Assembly_No,IIF(MA_Part_Assembly_Locale.Part_Assembly_Description ='' OR MA_Part_Assembly_Locale.Part_Assembly_Description IS NULL,MA_Part_Assembly.Part_Assembly_Description,MA_Part_Assembly_Locale.Part_Assembly_Description) , IIF(Bookmark_ID IS NULL, 0, Bookmark_ID) AS BID, MA_Part_Assembly.Part_Assembly_ID FROM MA_Part_Assembly " +
        ////                                "LEFT JOIN TR_Manage_Bookmarks ON Bookmark_Part_Assembly_ID = Part_Assembly_ID AND Bookmark_User_ID=" + UserID +
        ////                                " LEFT JOIN MA_Part_Assembly_Locale ON MA_Part_Assembly_Locale.Part_Assembly_ID = MA_Part_Assembly.Part_Assembly_ID " +
        ////                                " LEFT JOIN MA_Language ON MA_Part_Assembly_Locale.Part_Assembly_Language_ID = MA_Language.Langauge_ID " +
        ////                                " WHERE Part_Assembly_No = (SELECT Catalogue_OrderNo FROM MA_Catalogue WHERE Catalogue_ID = (SELECT Vehicle_Catalogue_ID FROM MA_Vehicle WHERE Vehicle_ID = " + Part_Assembly_ID + "))");
        ////                }
        ////            }
        ////            else
        ////            {
        ////                cmdText = "Usp_Sel_User_Catalogue_Drawing_BOM_List";

        ////                if (NovaCatalougeBOMInitialLoadObj.Lang.ToString().ToUpper() == "EN")
        ////                {
        ////                    Query = ("SELECT Part_Assembly_No, Part_Assembly_Description, IIF(Bookmark_ID IS NULL, 0, Bookmark_ID) AS BID, Part_Assembly_ID FROM MA_Part_Assembly " +
        ////                                " LEFT JOIN TR_Manage_Bookmarks ON Bookmark_Part_Assembly_ID = Part_Assembly_ID AND Bookmark_User_ID=" + UserID +
        ////                                " WHERE Part_Assembly_No = (SELECT Catalogue_OrderNo FROM MA_Catalogue WHERE Catalogue_ID = " + Part_Assembly_ID + ")");
        ////                }
        ////                else
        ////                {
        ////                    Query = ("SELECT Part_Assembly_No,IIF(MA_Part_Assembly_Locale.Part_Assembly_Description ='' OR MA_Part_Assembly_Locale.Part_Assembly_Description IS NULL,MA_Part_Assembly.Part_Assembly_Description,MA_Part_Assembly_Locale.Part_Assembly_Description) , IIF(Bookmark_ID IS NULL, 0, Bookmark_ID) AS BID, MA_Part_Assembly.Part_Assembly_ID FROM MA_Part_Assembly " +
        ////                                "LEFT JOIN TR_Manage_Bookmarks ON Bookmark_Part_Assembly_ID = Part_Assembly_ID AND Bookmark_User_ID=" + UserID +
        ////                                " LEFT JOIN MA_Part_Assembly_Locale ON MA_Part_Assembly_Locale.Part_Assembly_ID = MA_Part_Assembly.Part_Assembly_ID " +
        ////                                " LEFT JOIN MA_Language ON MA_Part_Assembly_Locale.Part_Assembly_Language_ID = MA_Language.Langauge_ID " +
        ////                                " WHERE Part_Assembly_No = (SELECT Catalogue_OrderNo FROM MA_Catalogue WHERE Catalogue_ID = " + Part_Assembly_ID + ")");
        ////                }
        ////            }

        ////            using (SqlCommand sqlComm = new SqlCommand(Query, ConnSql))
        ////            {
        ////                if (ConnSql.State == ConnectionState.Closed)
        ////                {
        ////                    ConnSql.Open();
        ////                }
        ////                DataReader = sqlComm.ExecuteReader();
        ////                if (DataReader.HasRows)
        ////                {
        ////                    while (DataReader.Read())
        ////                    {
        ////                        AssyDetails.Part_Assembly_No = DataReader[0].ToString();
        ////                        AssyDetails.Part_Assembly_Description = DataReader[1].ToString();
        ////                        AssyDetails.BID = Convert.ToInt32(DataReader[2]);
        ////                        AssyDetails.Part_Assembly_ID = Convert.ToInt32(DataReader[3]);
        ////                    }
        ////                }
        ////            }
        ////            using (SqlCommand SqlCmd = new SqlCommand(cmdText, ConnSql))
        ////            {
        ////                DataReader = null;
        ////                int MenuID = Commo.GetObjectID("AssemblyPartViewer", NovaCatalougeBOMInitialLoadObj.DCTYPE, NovaCatalougeBOMInitialLoadObj.UserID, Conn);
        ////                SqlCmd.CommandText = cmdText;
        ////                SqlCmd.CommandType = CommandType.StoredProcedure;

        ////                if (NovaCatalougeBOMInitialLoadObj.BrandCode.ToString().ToUpper() == "PREVOST")
        ////                {
        ////                    SqlCmd.Parameters.AddWithValue("@Vehicle_ID", Part_Assembly_ID);

        ////                    string Query1 = ("SELECT TOP 1 CAST(VinshortNumber AS INT), VinshortCode, MA_Model.Model_Name FROM MA_Vehicle LEFT JOIN MA_Catalogue ON MA_Vehicle.Vehicle_Catalogue_ID = MA_Catalogue.Catalogue_ID LEFT JOIN MA_Model ON MA_Catalogue.Catalogue_Model_ID = MA_Model.Model_ID WHERE Vehicle_ID = " + Part_Assembly_ID);
        ////                    DataReader = null;
        ////                    using (SqlCommand sqlComm = new SqlCommand(Query1, ConnSql))
        ////                    {
        ////                        if (ConnSql.State == ConnectionState.Closed)
        ////                        {
        ////                            ConnSql.Open();
        ////                        }
        ////                        DataReader = sqlComm.ExecuteReader();
        ////                        if (DataReader.HasRows)
        ////                        {
        ////                            while (DataReader.Read())
        ////                            {
        ////                                ShortNumber = Convert.ToInt32(DataReader[0].ToString());
        ////                                ShortCode = DataReader[1].ToString();
        ////                                ModelName = DataReader[2].ToString();
        ////                            }
        ////                        }
        ////                    }

        ////                    SqlCmd.Parameters.AddWithValue("@IsRange", 0);
        ////                    SqlCmd.Parameters.AddWithValue("@FromSN", ShortNumber);
        ////                    SqlCmd.Parameters.AddWithValue("@ToSN", DBNull.Value);
        ////                    SqlCmd.Parameters.AddWithValue("@Model_IC", DBNull.Value);
        ////                    SqlCmd.Parameters.AddWithValue("@Vehicle_IC", Part_Assembly_ID);
        ////                }
        ////                else
        ////                {
        ////                    SqlCmd.Parameters.AddWithValue("@CatalogueID", Part_Assembly_ID);

        ////                }

        ////                SqlCmd.Parameters.AddWithValue("@User_ID", UserID);
        ////                SqlCmd.Parameters.AddWithValue("@MENU_ID", MenuID);
        ////                SqlCmd.Parameters.Add("@HasServiceLink", SqlDbType.Int).Direction = ParameterDirection.Output;
        ////                if (ConnSql.State == ConnectionState.Closed)
        ////                {
        ////                    ConnSql.Open();
        ////                }
        ////                SqlCmd.CommandTimeout = 0;
        ////                DataReader = SqlCmd.ExecuteReader();
        ////                int Count = 0;
        ////                if (DataReader.HasRows)
        ////                {
        ////                    while (DataReader.Read())
        ////                    {
        ////                        PartAssemblyViewer SingleRecord = new PartAssemblyViewer();
        ////                        SingleRecord.Part_Assembly_ID = int.Parse(DataReader["SetMember_Part_ID"].ToString());                                
        ////                        SingleRecord.ManageBOM_Item_No = (Convert.ToBoolean(DataReader["Hide_Item_No"]) == true ? (NovaCatalougeBOMInitialLoadObj.BrandCode.ToString().ToUpper() == "PREVOST" ? "NS" : "") : (DataReader["ManageBOM_Qty"].ToString() == "" && Convert.ToBoolean(DataReader["Is_DSPEC_Part"]) == true && DataReader["ManageBOM_Remarks"].ToString() == "" ? "" : DataReader["Item_No"].ToString()));                             
        ////                        SingleRecord.MFG_Code = (DataReader["GlobalCode"].ToString() == "1" ? "" : (DataReader["SetNumber_MFGCode"].ToString() == "DSP" ? "" : DataReader["SetNumber_MFGCode"].ToString()));

        ////                        if (NovaCatalougeBOMInitialLoadObj.BrandCode.ToString().ToUpper() == "PREVOST")
        ////                        {
        ////                            switch (DataReader["GlobalCode"].ToString().Split(' ')[0].Trim())
        ////                            {
        ////                                case "1":
        ////                                    SingleRecord.Part_Assembly_No = "N.S.S.";
        ////                                    break;
        ////                                case "2":
        ////                                    SingleRecord.Part_Assembly_No = "N.S.P.";
        ////                                    break;
        ////                                case "3":
        ////                                    SingleRecord.Part_Assembly_No = "N.S.A.";
        ////                                    break;
        ////                                case "4":
        ////                                    SingleRecord.Part_Assembly_No = "N.P.N.";
        ////                                    break;
        ////                                case "P":
        ////                                    SingleRecord.Part_Assembly_No = "Call order desk";
        ////                                    break;
        ////                                case "V":
        ////                                    SingleRecord.Part_Assembly_No = "Refer To Volvo";
        ////                                    break;
        ////                                default:
        ////                                    SingleRecord.Part_Assembly_No = DataReader["SetNumber"].ToString();
        ////                                    break;
        ////                            }
        ////                        }
        ////                        else
        ////                        {
        ////                            SingleRecord.Part_Assembly_No = DataReader["SetNumber"].ToString();
        ////                        }
        ////                        SingleRecord.Part_Assembly_Description = HttpUtility.HtmlEncode(DataReader["Part_Assembly_Description"].ToString());
        ////                        SingleRecord.ManageBOM_Qty = DataReader["ManageBOM_Qty"].ToString();
        ////                        SingleRecord.Part_Assembly_Specification = DataReader["Part_Assembly_Specification"].ToString();
        ////                        SingleRecord.ManageBOM_Font_ID = int.Parse(DataReader["ManageBOM_Font_ID"].ToString());
        ////                        SingleRecord.Part_Assembly_IsSupersession = Convert.ToBoolean(DataReader["Part_Assembly_IsSupersession"]);
        ////                        SingleRecord.Part_Assembly_Remarks = DataReader["ManageBOM_Remarks"].ToString();
        ////                        SingleRecord.Img = Convert.ToBoolean(DataReader["Img"]);
        ////                        SingleRecord.Part_Assembly_IsPurchasable = (Convert.ToBoolean(DataReader["Part_Assembly_IsPurchasable"]) == true ? "<span class='ClsBOMShoppingCart'><span class='fa fa-cart-plus'></span></span>" : "");
        ////                        SingleRecord.Part_Assembly_IsAssembly = (Convert.ToBoolean(DataReader["Part_Assembly_IsAssembly"]) == true ? "Yes" : "No");
        ////                        SingleRecord.Part_Has_Recommended = Convert.ToBoolean(DataReader["Part_Has_Recommended"]);                               
        ////                        SingleRecord.BOM_HotSpot_Coordinates = DataReader["File_NameWithXY"].ToString();
        ////                        SingleRecord.Font_Name = DataReader["Font_Name"].ToString();
        ////                        SingleRecord.Font_Size = Convert.ToInt32(DataReader["Font_Size"].ToString());
        ////                        SingleRecord.Font_Style = DataReader["Font_Style"].ToString();
        ////                        SingleRecord.Font_Colour = DataReader["Font_Colour"].ToString();                               
        ////                        SingleRecord.RowNumber = ++Count;
        ////                        SingleRecord.RecommendedParts_ID = (SingleRecord.Part_Has_Recommended == true ? "1" : "");
        ////                        SingleRecord.ManageBOM_ID = DataReader["ManageBOM_ID"].ToString();
        ////                        SingleRecord.ManageBOM_MaskingAction_ID = DataReader["ManageBOM_MaskingAction_ID"].ToString();
        ////                        SingleRecord.Part_Assembly_AlternateNo = DataReader["Part_Assembly_AlternateNo"].ToString();
        ////                        SingleRecord.Vendor_Name = DataReader["Vendor_Name"].ToString();
        ////                        SingleRecord.VendorPartInfo_PartNo = DataReader["VendorPartInfo_PartNo"].ToString();
        ////                        SingleRecord.FunctionGroup = DataReader["FunctionGroup"].ToString();
        ////                        SingleRecord.Concat_Item_No = DataReader["Concat_Item_No"].ToString();
        ////                        SingleRecord.Author_Notes = DataReader["NoteText"].ToString();
        ////                        SingleRecord.DATE_CREATED = DataReader["DATE_CREATED"].ToString();
        ////                        SingleRecord.French = (DataReader["French"].ToString() != "" ? HttpUtility.HtmlEncode(DataReader["French"].ToString()) : DataReader["Dictionary_Locale_Description"].ToString());
        ////                        SingleRecord.Mfrer = DataReader["Mfrer"].ToString();
        ////                        SingleRecord.Part_Status = DataReader["Part_Status"].ToString();
        ////                        SingleRecord.REVISION = DataReader["REVISION"].ToString();
        ////                        if (NovaCatalougeBOMInitialLoadObj.BrandCode.ToString().ToUpper() == "PREVOST")
        ////                        {
        ////                            SingleRecord.Stackno = DataReader["Stockno"].ToString();
        ////                        }
        ////                        else
        ////                        {
        ////                            SingleRecord.StockNo1 = DataReader["StockNo1"].ToString();
        ////                            SingleRecord.StockNo2 = DataReader["StockNo2"].ToString();
        ////                            SingleRecord.StockNo3 = DataReader["StockNo3"].ToString();
        ////                            SingleRecord.StockNo4 = DataReader["StockNo4"].ToString();
        ////                            SingleRecord.StockNo5 = DataReader["StockNo5"].ToString();
        ////                        }

        ////                        if (NovaCatalougeBOMInitialLoadObj.BrandCode.ToString().ToUpper() == "PREVOST")
        ////                        {
        ////                            if (DataReader["GlobalCode"].ToString().Split(' ')[0].Trim() == "U")
        ////                            {
        ////                            }
        ////                            else
        ////                            {
        ////                                BOMList.Add(SingleRecord);
        ////                            }
        ////                        }
        ////                        else
        ////                        {
        ////                            BOMList.Add(SingleRecord);
        ////                        }
        ////                    }
        ////                }
        ////                ConnSql.Close();
        ////                Service = Convert.ToInt32(SqlCmd.Parameters["@HasServiceLink"].Value.ToString());
        ////                #region :::Sorting-Searching:::
        ////                BOMIE = BOMList;

        ////                if (_search)
        ////                {
        ////                    JQGridfilters _filters = JsonConvert.DeserializeObject<JQGridfilters>(filters);
        ////                    BOMIQ = BOMIE.AsQueryable();
        ////                    BOMIQ = BOMIQ.FilterSearch(_filters);
        ////                }
        ////                else
        ////                {
        ////                    BOMIQ = BOMIE.Select(c => c).AsQueryable().JQGridSorting(sidx, sord);
        ////                }
        ////                #endregion

        ////                #region::: Pagination :::
        ////                BOMIE = BOMIQ.AsEnumerable();
        ////                //Session["ExportBOMDetails"] = BOMIE; DK - WRITE SEPRATE METHOD FOR EXPORT
        ////                try
        ////                {
        ////                    TotalPages = Convert.ToInt32(Math.Ceiling((double)BOMIE.Count() / (double)rows));
        ////                    TotalRecords = BOMIE.Count();
        ////                    BOMIE = BOMIE.Skip((page - 1) * rows).Take(rows).ToList();

        ////                    List<ShoppingCartClsForGrid> ShoppingCartPartIDList = new List<ShoppingCartClsForGrid>();
        ////                    if (NovaCatalougeBOMInitialLoadObj.CurrentShoppingCartData != null)
        ////                    {
        ////                        ShoppingCartPartIDList = (List<ShoppingCartClsForGrid>)(NovaCatalougeBOMInitialLoadObj.CurrentShoppingCartData);
        ////                        ShoppingCartPartIDList = ShoppingCartPartIDList.Where(S => S.BrandID == Convert.ToInt32(NovaCatalougeBOMInitialLoadObj.BrandID)).Select(S => S).ToList();
        ////                        foreach (var item in ShoppingCartPartIDList)
        ////                        {
        ////                            int PartID = BOMIE.Where(B => B.Part_Assembly_ID == item.ID).Select(B => B.Part_Assembly_ID).FirstOrDefault();
        ////                            if (PartID > 0)
        ////                            {
        ////                                foreach (var Update in BOMIE)
        ////                                {
        ////                                    if (Update.Part_Assembly_ID == PartID)
        ////                                    {
        ////                                        Update.Part_Assembly_IsPurchasable = "<span class='ClsBOMShoppingCart ClsAdded'><span class='fa fa-shopping-cart'></span></span>"; ;
        ////                                    }
        ////                                }
        ////                            }
        ////                        }
        ////                    }
        ////                }
        ////                #endregion

        ////                catch (Exception ex)
        ////                {                            
        ////                    var Request = new System.Net.Http.HttpRequestMessage();
        ////                    SharedAPIClassLibrary_DC.Utilities.ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(UserID),"");
        ////                }
        ////                jsonReader = new
        ////                {
        ////                    root = BOMIE,
        ////                    page = page,
        ////                    total = TotalPages,
        ////                    records = TotalRecords,
        ////                    AssyID = AssyDetails.Part_Assembly_ID,
        ////                    AssyNo = AssyDetails.Part_Assembly_No,
        ////                    AssyDescription = AssyDetails.Part_Assembly_Description,
        ////                    BCount = AssyDetails.BID,
        ////                    HasServiceLink = Service,
        ////                    FromCatalogue = true,
        ////                    Vinshortcode = ShortCode,
        ////                    VINModelName = ModelName,
        ////                    Vehicle = Part_Assembly_ID
        ////                };


        ////            }
        ////        }
        ////    }
        ////    catch (Exception ex)
        ////    {
        ////        var Request = new System.Net.Http.HttpRequestMessage();
        ////        SharedAPIClassLibrary_DC.Utilities.ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(UserID), Conn);
        ////    }
        ////    finally
        ////    {
        ////        SqlConnection.ClearAllPools();
        ////    }
        ////    // return new OkObjectResult(jsonReader);

        ////    //var settings = new JsonSerializerSettings
        ////    //{
        ////    //    ContractResolver = new CamelCasePropertyNamesContractResolver()
        ////    //};


        ////    //var serializedJson = JsonConvert.SerializeObject(jsonData);




        ////    return new JsonResult(jsonReader);


        ////    //return contentResult;

        ////}


        //#region To Get Catalogue Assembly BOM List - Siddesh.P
        ///// <summary>
        ///// 
        ///// </summary>
        ///// <returns>...</returns>
        //public static IActionResult GetCatalogueAssemblyBOMList(NovaCatalougeBOMInitialLoad NovaCatalougeBOMInitialLoadObj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, string Connectionstring)
        //{

        //    //JwtService objdwnfile = new JwtService("");
        //    //string result = objdwnfile.GetFileDownloadUrl();

        //    Common Commo = new Common();
        //    var jsonReader = default(dynamic);
        //    string Conn = Connectionstring;
        //    //Conn = "Data Source=quest-dev-rds-enc.cew1ojzgpj8q.ap-south-1.rds.amazonaws.com; Initial Catalog=PartsAssist_Nova_LiveRep; User ID=sd_dev;password=**********;multipleactiveresultsets=True;TrustServerCertificate=true;";

        //    int TotalPages = 0;
        //    int TotalRecords = 0;
        //    int Service = 0;

        //    //string sidx = HttpContext.Current.Request.Params["sidx"];
        //    //int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
        //    //int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
        //    //string sord = HttpContext.Current.Request.Params["sord"];
        //    //bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
        //    //long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
        //    //string filters = "";
        //    //if (HttpContext.Current.Request.Params["filters"] == null)
        //    //{
        //    //    filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
        //    //}
        //    //else
        //    //{
        //    //    filters = HttpContext.Current.Request.Params["filters"];
        //    //}
        //    int Part_Assembly_ID = 0;
        //    if (NovaCatalougeBOMInitialLoadObj.IsExternal)
        //    {
        //        _search = true;
        //        Part_Assembly_ID = Commo.GetAssemblyIDOnAssemblyNo(NovaCatalougeBOMInitialLoadObj.Part_Assembly_No, NovaCatalougeBOMInitialLoadObj.MFG_Code, Connectionstring, NovaCatalougeBOMInitialLoadObj.UserID);
        //    }
        //    IEnumerable<PartAssemblyViewer> BOMIE = null;
        //    IQueryable<PartAssemblyViewer> BOMIQ = null;
        //    List<PartAssemblyViewer> BOMList = new List<PartAssemblyViewer>();
        //    int UserID = Convert.ToInt32(NovaCatalougeBOMInitialLoadObj.UserID);

        //    if (NovaCatalougeBOMInitialLoadObj.Part_Assembly_No != "" && NovaCatalougeBOMInitialLoadObj.Part_Assembly_No != null)
        //    {
        //        Part_Assembly_ID = Convert.ToInt32(NovaCatalougeBOMInitialLoadObj.Part_Assembly_No);
        //    }
        //    else
        //    {
        //        Part_Assembly_ID = Convert.ToInt32(NovaCatalougeBOMInitialLoadObj.Section);
        //    }


        //    string cmdText = "";
        //    int ShortNumber = 0;
        //    string ShortCode = string.Empty;
        //    string ModelName = string.Empty;
        //    PartAssemblyHeader AssyDetails = new PartAssemblyHeader();
        //    try
        //    {
        //        using (SqlConnection ConnSql = new SqlConnection(Conn))
        //        {
        //            SqlDataReader DataReader = null;
        //            string Query = null;
        //            if (NovaCatalougeBOMInitialLoadObj.BrandCode.ToString().ToUpper() == "PREVOST")
        //            {
        //                cmdText = "Usp_Sel_User_Drawing_BOM_List1";
        //                if (NovaCatalougeBOMInitialLoadObj.Lang.ToString().ToUpper() == "EN")
        //                {
        //                    Query = ("SELECT Part_Assembly_No, Part_Assembly_Description, IIF(Bookmark_ID IS NULL, 0, Bookmark_ID) AS BID, Part_Assembly_ID FROM MA_Part_Assembly " +
        //                                " LEFT JOIN TR_Manage_Bookmarks ON Bookmark_Part_Assembly_ID = Part_Assembly_ID AND Bookmark_User_ID=" + UserID +
        //                                " WHERE Part_Assembly_No = (SELECT Catalogue_OrderNo FROM MA_Catalogue WHERE Catalogue_ID = (SELECT Vehicle_Catalogue_ID FROM MA_Vehicle WHERE Vehicle_ID = " + Part_Assembly_ID + "))");
        //                }
        //                else
        //                {
        //                    Query = ("SELECT Part_Assembly_No,IIF(MA_Part_Assembly_Locale.Part_Assembly_Description ='' OR MA_Part_Assembly_Locale.Part_Assembly_Description IS NULL,MA_Part_Assembly.Part_Assembly_Description,MA_Part_Assembly_Locale.Part_Assembly_Description) , IIF(Bookmark_ID IS NULL, 0, Bookmark_ID) AS BID, MA_Part_Assembly.Part_Assembly_ID FROM MA_Part_Assembly " +
        //                                "LEFT JOIN TR_Manage_Bookmarks ON Bookmark_Part_Assembly_ID = Part_Assembly_ID AND Bookmark_User_ID=" + UserID +
        //                                " LEFT JOIN MA_Part_Assembly_Locale ON MA_Part_Assembly_Locale.Part_Assembly_ID = MA_Part_Assembly.Part_Assembly_ID " +
        //                                " LEFT JOIN MA_Language ON MA_Part_Assembly_Locale.Part_Assembly_Language_ID = MA_Language.Langauge_ID " +
        //                                " WHERE Part_Assembly_No = (SELECT Catalogue_OrderNo FROM MA_Catalogue WHERE Catalogue_ID = (SELECT Vehicle_Catalogue_ID FROM MA_Vehicle WHERE Vehicle_ID = " + Part_Assembly_ID + "))");
        //                }
        //            }
        //            else
        //            {
        //                cmdText = "Usp_Sel_User_Catalogue_Drawing_BOM_List";

        //                if (NovaCatalougeBOMInitialLoadObj.Lang.ToString().ToUpper() == "EN")
        //                {
        //                    Query = ("SELECT Part_Assembly_No, Part_Assembly_Description, IIF(Bookmark_ID IS NULL, 0, Bookmark_ID) AS BID, Part_Assembly_ID FROM MA_Part_Assembly " +
        //                                " LEFT JOIN TR_Manage_Bookmarks ON Bookmark_Part_Assembly_ID = Part_Assembly_ID AND Bookmark_User_ID=" + UserID +
        //                                " WHERE Part_Assembly_No = (SELECT Catalogue_OrderNo FROM MA_Catalogue WHERE Catalogue_ID = " + Part_Assembly_ID + ")");
        //                }
        //                else
        //                {
        //                    Query = ("SELECT Part_Assembly_No,IIF(MA_Part_Assembly_Locale.Part_Assembly_Description ='' OR MA_Part_Assembly_Locale.Part_Assembly_Description IS NULL,MA_Part_Assembly.Part_Assembly_Description,MA_Part_Assembly_Locale.Part_Assembly_Description) , IIF(Bookmark_ID IS NULL, 0, Bookmark_ID) AS BID, MA_Part_Assembly.Part_Assembly_ID FROM MA_Part_Assembly " +
        //                                "LEFT JOIN TR_Manage_Bookmarks ON Bookmark_Part_Assembly_ID = Part_Assembly_ID AND Bookmark_User_ID=" + UserID +
        //                                " LEFT JOIN MA_Part_Assembly_Locale ON MA_Part_Assembly_Locale.Part_Assembly_ID = MA_Part_Assembly.Part_Assembly_ID " +
        //                                " LEFT JOIN MA_Language ON MA_Part_Assembly_Locale.Part_Assembly_Language_ID = MA_Language.Langauge_ID " +
        //                                " WHERE Part_Assembly_No = (SELECT Catalogue_OrderNo FROM MA_Catalogue WHERE Catalogue_ID = " + Part_Assembly_ID + ")");
        //                }
        //            }

        //            using (SqlCommand sqlComm = new SqlCommand(Query, ConnSql))
        //            {
        //                if (ConnSql.State == System.Data.ConnectionState.Closed)
        //                {
        //                    ConnSql.Open();
        //                }
        //                DataReader = sqlComm.ExecuteReader();
        //                if (DataReader.HasRows)
        //                {
        //                    while (DataReader.Read())
        //                    {
        //                        AssyDetails.Part_Assembly_No = DataReader[0].ToString();
        //                        AssyDetails.Part_Assembly_Description = DataReader[1].ToString();
        //                        AssyDetails.BID = Convert.ToInt32(DataReader[2]);
        //                        AssyDetails.Part_Assembly_ID = Convert.ToInt32(DataReader[3]);
        //                    }
        //                }
        //            }
        //            using (SqlCommand SqlCmd = new SqlCommand(cmdText, ConnSql))
        //            {
        //                DataReader = null;
        //                int MenuID = Commo.GetObjectID("AssemblyPartViewer", NovaCatalougeBOMInitialLoadObj.DCTYPE, NovaCatalougeBOMInitialLoadObj.UserID, Conn);
        //                SqlCmd.CommandText = cmdText;
        //                SqlCmd.CommandType = CommandType.StoredProcedure;

        //                if (NovaCatalougeBOMInitialLoadObj.BrandCode.ToString().ToUpper() == "PREVOST")
        //                {
        //                    SqlCmd.Parameters.AddWithValue("@Vehicle_ID", Part_Assembly_ID);

        //                    string Query1 = ("SELECT TOP 1 CAST(VinshortNumber AS INT), VinshortCode, MA_Model.Model_Name FROM MA_Vehicle LEFT JOIN MA_Catalogue ON MA_Vehicle.Vehicle_Catalogue_ID = MA_Catalogue.Catalogue_ID LEFT JOIN MA_Model ON MA_Catalogue.Catalogue_Model_ID = MA_Model.Model_ID WHERE Vehicle_ID = " + Part_Assembly_ID);
        //                    DataReader = null;
        //                    using (SqlCommand sqlComm = new SqlCommand(Query1, ConnSql))
        //                    {
        //                        if (ConnSql.State == ConnectionState.Closed)
        //                        {
        //                            ConnSql.Open();
        //                        }
        //                        DataReader = sqlComm.ExecuteReader();
        //                        if (DataReader.HasRows)
        //                        {
        //                            while (DataReader.Read())
        //                            {
        //                                ShortNumber = Convert.ToInt32(DataReader[0].ToString());
        //                                ShortCode = DataReader[1].ToString();
        //                                ModelName = DataReader[2].ToString();
        //                            }
        //                        }
        //                    }

        //                    SqlCmd.Parameters.AddWithValue("@IsRange", 0);
        //                    SqlCmd.Parameters.AddWithValue("@FromSN", ShortNumber);
        //                    SqlCmd.Parameters.AddWithValue("@ToSN", DBNull.Value);
        //                    SqlCmd.Parameters.AddWithValue("@Model_IC", DBNull.Value);
        //                    SqlCmd.Parameters.AddWithValue("@Vehicle_IC", Part_Assembly_ID);
        //                }
        //                else
        //                {
        //                    SqlCmd.Parameters.AddWithValue("@CatalogueID", Part_Assembly_ID);

        //                }

        //                SqlCmd.Parameters.AddWithValue("@User_ID", UserID);
        //                SqlCmd.Parameters.AddWithValue("@MENU_ID", MenuID);
        //                SqlCmd.Parameters.AddWithValue("@Language_ID", NovaCatalougeBOMInitialLoadObj.Lang.ToString().ToLower() == "en" ? 0 : Convert.ToInt32(NovaCatalougeBOMInitialLoadObj.Langauge_ID));
        //                SqlCmd.Parameters.Add("@HasServiceLink", SqlDbType.Int).Direction = ParameterDirection.Output;
        //                if (ConnSql.State == ConnectionState.Closed)
        //                {
        //                    ConnSql.Open();
        //                }
        //                SqlCmd.CommandTimeout = 0;
        //                DataReader = SqlCmd.ExecuteReader();
        //                int Count = 0;
        //                if (DataReader.HasRows)
        //                {
        //                    while (DataReader.Read())
        //                    {
        //                        PartAssemblyViewer SingleRecord = new PartAssemblyViewer();
        //                        SingleRecord.Part_Assembly_ID = int.Parse(DataReader["SetMember_Part_ID"].ToString());
        //                        SingleRecord.ManageBOM_Item_No = (Convert.ToBoolean(DataReader["Hide_Item_No"]) == true ? (NovaCatalougeBOMInitialLoadObj.BrandCode.ToString().ToUpper() == "PREVOST" ? "NS" : "") : (DataReader["ManageBOM_Qty"].ToString() == "" && Convert.ToBoolean(DataReader["Is_DSPEC_Part"]) == true && DataReader["ManageBOM_Remarks"].ToString() == "" ? "" : DataReader["Item_No"].ToString()));
        //                        SingleRecord.MFG_Code = (DataReader["GlobalCode"].ToString() == "1" ? "" : (DataReader["SetNumber_MFGCode"].ToString() == "DSP" ? "" : DataReader["SetNumber_MFGCode"].ToString()));

        //                        if (NovaCatalougeBOMInitialLoadObj.BrandCode.ToString().ToUpper() == "PREVOST")
        //                        {
        //                            switch (DataReader["GlobalCode"].ToString().Split(' ')[0].Trim())
        //                            {
        //                                case "1":
        //                                    SingleRecord.Part_Assembly_No = "N.S.S.";
        //                                    break;
        //                                case "2":
        //                                    SingleRecord.Part_Assembly_No = "N.S.P.";
        //                                    break;
        //                                case "3":
        //                                    SingleRecord.Part_Assembly_No = "N.S.A.";
        //                                    break;
        //                                case "4":
        //                                    SingleRecord.Part_Assembly_No = "N.P.N.";
        //                                    break;
        //                                case "P":
        //                                    SingleRecord.Part_Assembly_No = "Call order desk";
        //                                    break;
        //                                case "V":
        //                                    SingleRecord.Part_Assembly_No = "Refer To Volvo";
        //                                    break;
        //                                default:
        //                                    SingleRecord.Part_Assembly_No = DataReader["SetNumber"].ToString();
        //                                    break;
        //                            }
        //                        }
        //                        else
        //                        {
        //                            SingleRecord.Part_Assembly_No = DataReader["SetNumber"].ToString();
        //                        }
        //                        SingleRecord.Part_Assembly_Description = NovaCatalougeBOMInitialLoadObj.Lang.ToString().ToLower() == "en" ? HttpUtility.HtmlEncode(DataReader["Part_Assembly_Description"].ToString()) : (DataReader["French"].ToString() != "" ? HttpUtility.HtmlEncode(DataReader["French"].ToString()) : DataReader["Dictionary_Locale_Description"].ToString());
        //                        SingleRecord.ManageBOM_Qty = DataReader["ManageBOM_Qty"].ToString();
        //                        SingleRecord.Part_Assembly_Specification = DataReader["Part_Assembly_Specification"].ToString();
        //                        SingleRecord.ManageBOM_Font_ID = int.Parse(DataReader["ManageBOM_Font_ID"].ToString());
        //                        SingleRecord.Part_Assembly_IsSupersession = Convert.ToBoolean(DataReader["Part_Assembly_IsSupersession"]);
        //                        SingleRecord.Part_Assembly_Remarks = DataReader["ManageBOM_Remarks"].ToString();
        //                        SingleRecord.Img = Convert.ToBoolean(DataReader["Img"]);
        //                        SingleRecord.Part_Assembly_IsPurchasable = (Convert.ToBoolean(DataReader["Part_Assembly_IsPurchasable"]) == true ? "<span class='ClsBOMShoppingCart'><span class='fa fa-cart-plus'></span></span>" : "");
        //                        SingleRecord.Part_Assembly_IsAssembly = (Convert.ToBoolean(DataReader["Part_Assembly_IsAssembly"]) == true ? "Yes" : "No");
        //                        SingleRecord.Part_Has_Recommended = Convert.ToBoolean(DataReader["Part_Has_Recommended"]);
        //                        SingleRecord.BOM_HotSpot_Coordinates = DataReader["File_NameWithXY"].ToString();
        //                        SingleRecord.Font_Name = DataReader["Font_Name"].ToString();
        //                        SingleRecord.Font_Size = Convert.ToInt32(DataReader["Font_Size"].ToString());
        //                        SingleRecord.Font_Style = DataReader["Font_Style"].ToString();
        //                        SingleRecord.Font_Colour = DataReader["Font_Colour"].ToString();
        //                        SingleRecord.RowNumber = ++Count;
        //                        SingleRecord.RecommendedParts_ID = (SingleRecord.Part_Has_Recommended == true ? "1" : "");
        //                        SingleRecord.ManageBOM_ID = DataReader["ManageBOM_ID"].ToString();
        //                        SingleRecord.ManageBOM_MaskingAction_ID = DataReader["ManageBOM_MaskingAction_ID"].ToString();
        //                        SingleRecord.Part_Assembly_AlternateNo = DataReader["Part_Assembly_AlternateNo"].ToString();
        //                        SingleRecord.Vendor_Name = DataReader["Vendor_Name"].ToString();
        //                        SingleRecord.VendorPartInfo_PartNo = DataReader["VendorPartInfo_PartNo"].ToString();
        //                        SingleRecord.FunctionGroup = DataReader["FunctionGroup"].ToString();
        //                        SingleRecord.Concat_Item_No = DataReader["Concat_Item_No"].ToString();
        //                        SingleRecord.Author_Notes = DataReader["NoteText"].ToString();
        //                        SingleRecord.DATE_CREATED = DataReader["DATE_CREATED"].ToString();
        //                        SingleRecord.French = (DataReader["French"].ToString() != "" ? HttpUtility.HtmlEncode(DataReader["French"].ToString()) : DataReader["Dictionary_Locale_Description"].ToString());
        //                        SingleRecord.Mfrer = DataReader["Mfrer"].ToString();
        //                        SingleRecord.Part_Status = DataReader["Part_Status"].ToString();
        //                        SingleRecord.REVISION = DataReader["REVISION"].ToString();
        //                        if (NovaCatalougeBOMInitialLoadObj.BrandCode.ToString().ToUpper() == "PREVOST")
        //                        {
        //                            SingleRecord.Stackno = DataReader["Stockno"].ToString();
        //                        }
        //                        else
        //                        {
        //                            SingleRecord.StockNo1 = DataReader["StockNo1"].ToString();
        //                            SingleRecord.StockNo2 = DataReader["StockNo2"].ToString();
        //                            SingleRecord.StockNo3 = DataReader["StockNo3"].ToString();
        //                            SingleRecord.StockNo4 = DataReader["StockNo4"].ToString();
        //                            SingleRecord.StockNo5 = DataReader["StockNo5"].ToString();
        //                        }

        //                        if (NovaCatalougeBOMInitialLoadObj.BrandCode.ToString().ToUpper() == "PREVOST")
        //                        {
        //                            if (DataReader["GlobalCode"].ToString().Split(' ')[0].Trim() == "U")
        //                            {
        //                            }
        //                            else
        //                            {
        //                                BOMList.Add(SingleRecord);
        //                            }
        //                        }
        //                        else
        //                        {
        //                            BOMList.Add(SingleRecord);
        //                        }
        //                    }
        //                }
        //                ConnSql.Close();
        //                Service = Convert.ToInt32(SqlCmd.Parameters["@HasServiceLink"].Value.ToString());
        //                #region :::Sorting-Searching:::
        //                BOMIE = BOMList;

        //                if (_search)
        //                {
        //                    JQGridfilters _filters = null;
        //                    if (NovaCatalougeBOMInitialLoadObj.IsExternal)
        //                    {
        //                        _filters = JsonConvert.DeserializeObject<JQGridfilters>(NovaCatalougeBOMInitialLoadObj.filterValue);
        //                    }
        //                    else
        //                    {
        //                        _filters = JsonConvert.DeserializeObject<JQGridfilters>(filters);
        //                    }
        //                    BOMIQ = BOMIE.AsQueryable();
        //                    BOMIQ = BOMIQ.FilterSearch(_filters);
        //                }
        //                else
        //                {
        //                    BOMIQ = BOMIE.Select(c => c).AsQueryable().JQGridSorting(sidx, sord);
        //                }
        //                #endregion

        //                #region::: Pagination :::
        //                BOMIE = BOMIQ.AsEnumerable();
        //                //Session["ExportBOMDetails"] = BOMIE; DK - WRITE SEPRATE METHOD FOR EXPORT
        //                try
        //                {
        //                    TotalPages = Convert.ToInt32(Math.Ceiling((double)BOMIE.Count() / (double)rows));
        //                    TotalRecords = BOMIE.Count();
        //                    BOMIE = BOMIE.Skip((page - 1) * rows).Take(rows).ToList();

        //                    List<ShoppingCartClsForGrid> ShoppingCartPartIDList = new List<ShoppingCartClsForGrid>();
        //                    if (NovaCatalougeBOMInitialLoadObj.CurrentShoppingCartData != null)
        //                    {
        //                        ShoppingCartPartIDList = (List<ShoppingCartClsForGrid>)(NovaCatalougeBOMInitialLoadObj.CurrentShoppingCartData);
        //                        ShoppingCartPartIDList = ShoppingCartPartIDList.Where(S => S.BrandID == Convert.ToInt32(NovaCatalougeBOMInitialLoadObj.BrandID)).Select(S => S).ToList();
        //                        foreach (var item in ShoppingCartPartIDList)
        //                        {
        //                            int PartID = BOMIE.Where(B => B.Part_Assembly_ID == item.ID).Select(B => B.Part_Assembly_ID).FirstOrDefault();
        //                            if (PartID > 0)
        //                            {
        //                                foreach (var Update in BOMIE)
        //                                {
        //                                    if (Update.Part_Assembly_ID == PartID)
        //                                    {
        //                                        Update.Part_Assembly_IsPurchasable = "<span class='ClsBOMShoppingCart ClsAdded'><span class='fa fa-shopping-cart'></span></span>"; ;
        //                                    }
        //                                }
        //                            }
        //                        }
        //                    }
        //                }
        //                #endregion







        //                catch (Exception ex)
        //                {
        //                    var Request = new System.Net.Http.HttpRequestMessage();
        //                    SharedAPIClassLibrary_DC.Utilities.ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(UserID), Connectionstring);
        //                }
        //                jsonReader = new
        //                {
        //                    root = BOMIE,
        //                    page = page,
        //                    total = TotalPages,
        //                    records = TotalRecords,
        //                    AssyID = AssyDetails.Part_Assembly_ID,
        //                    AssyNo = AssyDetails.Part_Assembly_No,
        //                    AssyDescription = AssyDetails.Part_Assembly_Description,
        //                    BCount = AssyDetails.BID,
        //                    HasServiceLink = Service,
        //                    FromCatalogue = true,
        //                    Vinshortcode = ShortCode,
        //                    VINModelName = ModelName,
        //                    Vehicle = Part_Assembly_ID
        //                };


        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        var Request = new System.Net.Http.HttpRequestMessage();
        //        SharedAPIClassLibrary_DC.Utilities.ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(UserID), Connectionstring);
        //    }
        //    finally
        //    {
        //        SqlConnection.ClearAllPools();
        //    }
        //    // return new OkObjectResult(jsonReader);

        //    //var settings = new JsonSerializerSettings
        //    //{
        //    //    ContractResolver = new CamelCasePropertyNamesContractResolver()
        //    //};


        //    //var serializedJson = JsonConvert.SerializeObject(jsonData);




        //    return new JsonResult(jsonReader);


        //    //return contentResult;

        //}
        //#endregion
        //public List<DataCls> GetDatafromClassLib(string connectionString)
        //{
        //    string query = "SELECT top 10 * FROM MA_User";
        //    List<DataCls> videos = new List<DataCls>();
        //    DataCls videoheader;
        //    SqlCommand Sqlcmd;
        //    SqlDataReader Sqldataread;

        //    // Use configuration during local development
        //    //var connectionString = configuration["EPC"];

        //    // Use Environment.GetEnvironmentVariable in Azure environment
        //    if (string.IsNullOrEmpty(connectionString))
        //    {
        //        connectionString = Environment.GetEnvironmentVariable("EPC");
        //    }
        //    using (SqlConnection connection = new SqlConnection(connectionString))
        //    {
        //        try
        //        {
        //            connection.Open();

        //            Sqlcmd = new SqlCommand(query, connection);
        //            Sqldataread = Sqlcmd.ExecuteReader();

        //            while (Sqldataread.Read())
        //            {
        //                videoheader = new DataCls();
        //                videoheader.ID = (Sqldataread["User_Customer_ID"] == DBNull.Value) ? 0 : Convert.ToInt32(Sqldataread["User_Customer_ID"]);
        //                videoheader.Name = (Sqldataread["User_FirstName"] == DBNull.Value) ? "" : Convert.ToString(Sqldataread["User_FirstName"]).ToString();

        //                videos.Add(videoheader);
        //            }
        //        }
        //        catch (Exception ex)
        //        {
        //            // Handle exceptions (log or rethrow)
        //        }
        //        finally
        //        {
        //            connection.Close();
        //        }
        //    }
        //    return videos;
        //}


        //#region To Get Illustration Data - Siddesh.P
        ///// <summary>
        ///// Returning illustration path and name for the selected toplevels,assemblies,parts
        ///// </summary>
        ///// <param name="as per below"></param>
        ///// <returns>Json</returns>
        //public static IActionResult GetIllustrations(NovaCatalougeIllustrationsLoad NovaIllustrationsLoadObj, string Connectionstring)
        //{
        //    Common Commo = new Common();
        //    var Details = default(dynamic);

        //    string Conn = Connectionstring;
        //    //string Conn = "Data Source=sqlserverkrcyotba2vfgs.database.windows.net; Initial Catalog=PartsAssist_Nova_LiveRep; User ID=azuresqladmin;password=**********;multipleactiveresultsets=True;";//configuration["EPC"];
        //    int Part_Assembly_ID = Convert.ToInt32(NovaIllustrationsLoadObj.ID);
        //    if(NovaIllustrationsLoadObj.IsExternal)
        //    {
        //        Part_Assembly_ID = Commo.GetAssemblyIDOnAssemblyNo(NovaIllustrationsLoadObj.Part_Assembly_No, NovaIllustrationsLoadObj.MFG_Code, Connectionstring, NovaIllustrationsLoadObj.UserID);
        //    }
        //    int GetPartData = Convert.ToInt32(NovaIllustrationsLoadObj.GetPart);

        //    int ISRange = -1;
        //    string FromSerialNumber = string.Empty;
        //    string ToSerialNumber = string.Empty;
        //    string Model = string.Empty;
        //    var VehicleIC = default(dynamic);
        //    int? intFromSerialNumber = 0;
        //    int? intToSerialNumber = 0;
        //    string AWSBucketPrifix = string.Empty;

        //    if (NovaIllustrationsLoadObj.DCTYPE == 2)
        //    {
        //        if (NovaIllustrationsLoadObj.TopLevelSectionVIN.ToString() != "" && NovaIllustrationsLoadObj.FromSerialNumber.ToString() == "")
        //        {
        //            FromSerialNumber = NovaIllustrationsLoadObj.TopLevelSectionVINShortCode.ToString();
        //            ToSerialNumber = NovaIllustrationsLoadObj.ToSerialNo.ToString();
        //            Model = NovaIllustrationsLoadObj.Model.ToString();
        //            VehicleIC = Convert.ToInt32(NovaIllustrationsLoadObj.TopLevelSectionVIN.ToString() == "" ? "0" : NovaIllustrationsLoadObj.TopLevelSectionVIN.ToString());
        //            VehicleIC = (VehicleIC == 0 ? DBNull.Value : VehicleIC);
        //        }
        //        else
        //        {
        //            FromSerialNumber = NovaIllustrationsLoadObj.FromSerialNumber.ToString();
        //            ToSerialNumber = NovaIllustrationsLoadObj.ToSerialNo.ToString();
        //            Model = NovaIllustrationsLoadObj.Model.ToString();
        //            VehicleIC = Convert.ToInt32(NovaIllustrationsLoadObj.Vehicle.ToString() == "" ? "0" : NovaIllustrationsLoadObj.Vehicle.ToString());
        //            VehicleIC = (VehicleIC == 0 ? DBNull.Value : VehicleIC);
        //        }

        //        if (FromSerialNumber != "" && ToSerialNumber != "")
        //        {
        //            intFromSerialNumber = Commo.GetVinShortNumber(FromSerialNumber, Convert.ToInt32(NovaIllustrationsLoadObj.DCTYPE), NovaIllustrationsLoadObj.UserID, Connectionstring);
        //            intToSerialNumber = Commo.GetVinShortNumber(ToSerialNumber, Convert.ToInt32(NovaIllustrationsLoadObj.DCTYPE), NovaIllustrationsLoadObj.UserID, Connectionstring);
        //            ISRange = 1;
        //        }
        //        else if (FromSerialNumber != "" || ToSerialNumber != "")
        //        {
        //            if (FromSerialNumber != "")
        //            {
        //                intFromSerialNumber = Commo.GetVinShortNumber(FromSerialNumber, Convert.ToInt32(NovaIllustrationsLoadObj.DCTYPE), NovaIllustrationsLoadObj.UserID, Connectionstring);
        //            }
        //            else if (ToSerialNumber != "" && FromSerialNumber == "")
        //            {
        //                intFromSerialNumber = Commo.GetVinShortNumber(ToSerialNumber, Convert.ToInt32(NovaIllustrationsLoadObj.DCTYPE), NovaIllustrationsLoadObj.UserID, Connectionstring);
        //            }
        //            ISRange = 0;
        //        }
        //        if (FromSerialNumber == "" && ToSerialNumber == "" && Model != "")
        //        {
        //            intFromSerialNumber = 0;
        //            ISRange = 0;
        //        }
        //    }

        //    if (GetPartData == 1 && NovaIllustrationsLoadObj.DCTYPE == 2)
        //    {

        //        using (SqlConnection ConnSql = new SqlConnection(Conn))
        //        {
        //            SqlDataReader DataReader = null;
        //            string Query = ("SELECT Part_Assembly_ID FROM MA_Part_Assembly " +
        //                                    " LEFT JOIN TR_Manage_Bookmarks ON Bookmark_Part_Assembly_ID = Part_Assembly_ID " +
        //                                    "WHERE Part_Assembly_No = (SELECT Catalogue_OrderNo FROM MA_Catalogue WHERE Catalogue_ID = (SELECT Vehicle_Catalogue_ID FROM MA_Vehicle WHERE Vehicle_ID = " + Part_Assembly_ID + "))");

        //            string ShortCodeQuery = ("SELECT TOP 1 VinshortCode FROM MA_Vehicle WHERE Vehicle_ID = " + Part_Assembly_ID);
        //            using (SqlCommand sqlComm = new SqlCommand(ShortCodeQuery, ConnSql))
        //            {
        //                if (ConnSql.State == ConnectionState.Closed)
        //                {
        //                    ConnSql.Open();
        //                }
        //                DataReader = sqlComm.ExecuteReader();
        //                if (DataReader.HasRows)
        //                {
        //                    while (DataReader.Read())
        //                    {
        //                        FromSerialNumber = DataReader[0].ToString();
        //                    }
        //                }
        //            }

        //            using (SqlCommand sqlComm = new SqlCommand(Query, ConnSql))
        //            {
        //                if (ConnSql.State == ConnectionState.Closed)
        //                {
        //                    ConnSql.Open();
        //                }
        //                DataReader = sqlComm.ExecuteReader();
        //                if (DataReader.HasRows)
        //                {
        //                    while (DataReader.Read())
        //                    {
        //                        Part_Assembly_ID = Convert.ToInt32(DataReader[0]);
        //                    }
        //                }
        //            }
        //        }
        //    }

        //    try
        //    {

        //        List<AssemblyIllustration> AssemblyIllustrationsList = new List<AssemblyIllustration>();
        //        var GetAttachmentsPath = string.Empty;
        //        var FolderPath = string.Empty;
        //        if (NovaIllustrationsLoadObj.DCTYPE == 1)
        //        {
        //            //GetAttachmentsPath = ConfigurationManager.AppSettings.Get("EPCGetImagePath").ToString() + "Novabus_OCR_Drawings\\";
        //            //FolderPath = ConfigurationManager.AppSettings.Get("EPCStoringImagePath").ToString() + "Novabus_OCR_Drawings\\";
        //            AWSBucketPrifix = "Images/Novabus_OCR_Drawings";
        //        }
        //        else if (NovaIllustrationsLoadObj.DCTYPE == 2)
        //        {
        //            //GetAttachmentsPath = ConfigurationManager.AppSettings.Get("EPCGetImagePath").ToString() + "Prevost_OCR_Drawings\\";
        //            //FolderPath = ConfigurationManager.AppSettings.Get("EPCStoringImagePath").ToString() + "Prevost_OCR_Drawings\\";
        //            AWSBucketPrifix = "Images/Prevost_OCR_Drawings";
        //        }


        //        DataSet ds = new DataSet("AssemblyIllData");

        //        using (SqlConnection conn = new SqlConnection(Conn))
        //        {
        //            SqlCommand sqlComm = null;
        //            if (NovaIllustrationsLoadObj.DCTYPE == 2)
        //            {
        //                sqlComm = new SqlCommand("Usp_Sel_User_Drawing_List", conn);
        //            }
        //            else if (NovaIllustrationsLoadObj.DCTYPE == 1)
        //            {
        //                sqlComm = new SqlCommand("Usp_Sel_User_Drawing_List_Order", conn);
        //            }

        //            if (NovaIllustrationsLoadObj.DCTYPE == 2)
        //            {
        //                if (ISRange == 0 || ISRange == 1 && ISRange != -1)
        //                {
        //                    sqlComm.Parameters.AddWithValue("@IsRange", ISRange);
        //                }
        //                else
        //                {
        //                    sqlComm.Parameters.AddWithValue("@IsRange", DBNull.Value);
        //                }

        //                if (intFromSerialNumber == 0)
        //                {
        //                    sqlComm.Parameters.AddWithValue("@FromSN", DBNull.Value);
        //                }
        //                else
        //                {
        //                    sqlComm.Parameters.AddWithValue("@FromSN", intFromSerialNumber);
        //                }

        //                if (intToSerialNumber == 0)
        //                {
        //                    sqlComm.Parameters.AddWithValue("@ToSN", DBNull.Value);
        //                }
        //                else
        //                {
        //                    sqlComm.Parameters.AddWithValue("@ToSN", intToSerialNumber);
        //                }

        //                if (Model == "")
        //                {
        //                    sqlComm.Parameters.AddWithValue("@Model_IC", DBNull.Value);
        //                }
        //                else
        //                {
        //                    sqlComm.Parameters.AddWithValue("@Model_IC", Model);
        //                }

        //                sqlComm.Parameters.AddWithValue("@Vehicle_IC", VehicleIC);

        //            }

        //            sqlComm.Parameters.AddWithValue("@Part_Assembly_ID", Part_Assembly_ID);
        //            sqlComm.CommandType = CommandType.StoredProcedure;
        //            SqlDataAdapter da = new SqlDataAdapter();
        //            if (conn.State == ConnectionState.Closed)
        //            {
        //                conn.Open();
        //            }
        //            da.SelectCommand = sqlComm;
        //            da.Fill(ds);

        //            string ReturnPath = string.Empty;

        //                foreach (DataRow item in ds.Tables[0].Rows)
        //                {
        //                    AssemblyIllustration SingleRecord = new AssemblyIllustration();
        //                    SingleRecord.ManageDrawing_ID = Convert.ToInt32(item["ManageDrawing_ID"]);
        //                    SingleRecord.ManageDrawing_Part_Assembly_ID = Convert.ToInt32(item["ManageDrawing_Part_Assembly_ID"]);
        //                    SingleRecord.ManageDrawing_File_Path = (item["ManageDrawing_File_Name"].ToString() != "" && item["ManageDrawing_File_Name"].ToString() != null ? item["ManageDrawing_File_Name"].ToString() : "");
        //                    SingleRecord.ManageDrawing_File_Name = item["ManageDrawing_File_Name"].ToString();
        //                    SingleRecord.ManageDrawing_Sequence = item["ManageDrawing_Sequence"] == "" || item["ManageDrawing_Sequence"] == DBNull.Value ? 0 : Convert.ToInt32(item["ManageDrawing_Sequence"]);
        //                    //if (System.IO.File.Exists(FolderPath + SingleRecord.ManageDrawing_File_Path))
        //                    //{

        //                    //    SingleRecord.ManageDrawing_File_Path = GetAttachmentsPath + SingleRecord.ManageDrawing_File_Path;
        //                    //    
        //                    //}
        //                    SingleRecord.ManageDrawing_File_Path = Commo.GetAWSObjectURL(SingleRecord.ManageDrawing_File_Path, AWSBucketPrifix);
        //                    AssemblyIllustrationsList.Add(SingleRecord);
        //                }
        //            }                

        //        if (AssemblyIllustrationsList.Count > 0)
        //        {
        //            Details = new
        //            {
        //                ImageData = true,
        //                Data = AssemblyIllustrationsList
        //            };
        //        }
        //        else
        //        {
        //            Details = new
        //            {
        //                ImageData = false,
        //                //ImageNotAvailable = HttpContext.GetGlobalResourceObject(NovaIllustrationsLoadObj.Culture.ToString(), "ResNoImageAvailable").ToString()
        //            };
        //        }
        //    }
        //    catch (Exception ex)
        //    {

        //        var Request = new System.Net.Http.HttpRequestMessage();
        //        ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaIllustrationsLoadObj.UserID), Connectionstring);
        //        Commo.errorlog(ex.Message, ex.StackTrace);

        //    }

        //    return new JsonResult(Details);
        //}
        //#endregion


        //#region Catalogue Order serach Dash Board - Siddesh.P - 07-Nov-2023 :::
        ///// <summary>
        /////DK - 07-Nov-2023 To VIN/ORDER LIST Dashboard 
        ///// </summary>
        ///// <param name="as per below"></param>
        ///// <returns>Json</returns>

        //public static IActionResult GetTreeData(string constring, OrderBasedTreeLoad NovaTreeLoadObject)
        //{
        //    Common Commo = new Common();
        //    var jsonData = default(dynamic);
        //    try
        //    {
        //        string Conn = constring;
        //        if ((NovaTreeLoadObject.sectionvalue).ToString() != "")
        //        { NovaTreeLoadObject.Section = Convert.ToInt32(NovaTreeLoadObject.sectionvalue); }

        //        var LanguageID = NovaTreeLoadObject.Langauge_ID;
        //        string CurrentLanguageTre = NovaTreeLoadObject.Lang.ToString();
        //        int LoginUser_ID = Convert.ToInt32(NovaTreeLoadObject.UserID);
        //        if (CurrentLanguageTre == "en")
        //        {
        //            LanguageID = 0;
        //        }

        //        var CatalogueName = string.Empty;


        //        //if (NovaTreeLoadObj.DataList != null)
        //        //{
        //        //    int a = 10;
        //        //}


        //        int Model_ID = 0;
        //        Model_ID = Convert.ToInt32(NovaTreeLoadObject.Section);

        //        if (NovaTreeLoadObject.FromInsideCatlougeSearch != null)
        //        {
        //            if (NovaTreeLoadObject.FromInsideCatlougeSearch.ToString() != "")
        //            {
        //                if (NovaTreeLoadObject.FromInsideCatlougeSearch.ToString() == "true")
        //                {
        //                    Model_ID = Convert.ToInt32(NovaTreeLoadObject.TopLevelIDValue);
        //                }
        //            }
        //        }

        //        bool IsRange = Convert.ToBoolean(NovaTreeLoadObject.IsRange);
        //        int intRow = 0;
        //        int intPCC_ID = 0; int intPrevPCC_ID = 0;
        //        int intMA_ID = 0; int intPrevMA_ID = 0;
        //        int intSAL1_ID = 0; int intPrevSAL1_ID = 0;
        //        int intSAL2_ID = 0; int intPrevSAL2_ID = 0;
        //        int intSAL3_ID = 0; int intPrevSAL3_ID = 0;
        //        int intSAL4_ID = 0; int intPrevSAL4_ID = 0;
        //        int intSAL5_ID = 0; //int intPrevSAL5_ID = 0;
        //        int intSAL6_ID = 0; //int intPrevSAL6_ID = 0;


        //        string intPCC_ID1 = string.Empty; string intPrevPCC_ID1 = string.Empty;
        //        string intMA_ID1 = string.Empty; string intPrevMA_ID1 = string.Empty;
        //        string intSAL1_ID1 = string.Empty; string intPrevSAL1_ID1 = string.Empty;
        //        string intSAL2_ID1 = string.Empty; string intPrevSAL2_ID1 = string.Empty;
        //        string intSAL3_ID1 = string.Empty; string intPrevSAL3_ID1 = string.Empty;
        //        string intSAL4_ID1 = string.Empty; string intPrevSAL4_ID1 = string.Empty;
        //        string intSAL5_ID1 = string.Empty; string intSAL6_ID1 = string.Empty;

        //        string SecNoSecDocBind = string.Empty;
        //        bool Checknextid = false;
        //        byte OrderType = Convert.ToByte(1);
        //        DataSet ds = new DataSet("TreeView");
        //        using (SqlConnection conn = new SqlConnection(Conn))
        //        {
        //            SqlCommand sqlComm = new SqlCommand("Sp_EPCTreeBuild", conn);
        //            sqlComm.Parameters.AddWithValue("@Vehicle_ID", Model_ID);
        //            sqlComm.Parameters.AddWithValue("@IsRange", DBNull.Value);
        //            sqlComm.Parameters.AddWithValue("@FromSN", DBNull.Value);
        //            sqlComm.Parameters.AddWithValue("@ToSN", DBNull.Value);
        //            sqlComm.Parameters.AddWithValue("@Model_IC", DBNull.Value);
        //            sqlComm.Parameters.AddWithValue("@User_ID", LoginUser_ID);
        //            sqlComm.Parameters.AddWithValue("@Language_ID", LanguageID);  // DK - Added on 03-Sep-21
        //            sqlComm.CommandType = CommandType.StoredProcedure;
        //            SqlDataAdapter da = new SqlDataAdapter();
        //            sqlComm.CommandTimeout = 100;
        //            da.SelectCommand = sqlComm;
        //            da.Fill(ds);
        //        }

        //        string HTMLForTree = "<ul>";
        //        //Start Loop Here
        //        foreach (DataRow dr in ds.Tables[0].Rows)
        //        {
        //            //Catalogue Level
        //            if (intRow == 0)
        //            {
        //                //HTMLForTree = HTMLForTree + "<li IsModel=\"1\" id=\"" + dr["Part_Assembly_ID"].ToString() + "\" class=\"folder expanded\"><b IsModel=\"1\" id=\"" + dr["Part_Assembly_ID"].ToString() + "\" style=\"font-weight: 100\">" + dr["Part_Assembly_Description"].ToString() + " </b>";
        //                HTMLForTree = HTMLForTree + "<li IsModel=\"1\" id=\"" + dr["Part_Assembly_ID"].ToString() + "\" class=\"folder clsExtension expanded\"><b IsChapter=\"1\" id=\"" + dr["Part_Assembly_ID"].ToString() + "\" style=\"font-weight: 100\">" + HttpUtility.HtmlEncode(dr["Part_Assembly_Description"].ToString()) + " </b>";
        //                if (dr["Sec_IC"].ToString() != "")
        //                {
        //                    HTMLForTree = HTMLForTree + "<ul>";
        //                }
        //            }

        //            //Top Section Level
        //            if (Int32.Parse(NovaTreeLoadObject.TreeLevel.ToString()) >= 1 && intPCC_ID != Convert.ToInt32(dr["Sec_IC"].ToString()) || intPCC_ID1 != dr["Sec_Desc"].ToString())
        //            {
        //                if (intSAL6_ID != 0 || intSAL6_ID1 != string.Empty)
        //                {
        //                    HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul></ul>";
        //                }
        //                else if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //                {
        //                    HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul>";
        //                }
        //                else if (intSAL4_ID != 0 || intSAL4_ID1 != string.Empty)
        //                {
        //                    HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul>";
        //                }
        //                else if (intSAL3_ID != 0 || intSAL3_ID1 != string.Empty)
        //                {
        //                    HTMLForTree = HTMLForTree + "</ul></ul></ul></ul>";
        //                }
        //                else if (intSAL2_ID != 0 || intSAL2_ID1 != string.Empty)
        //                {
        //                    HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                }
        //                else if (intSAL1_ID != 0 || intSAL1_ID1 != string.Empty)
        //                {
        //                    HTMLForTree = HTMLForTree + "</ul></ul>";
        //                }
        //                else if (intRow != 0 && Checknextid == false)
        //                {
        //                    HTMLForTree = HTMLForTree + "</ul>";
        //                }
        //                intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0; intSAL3_ID = 0; intSAL2_ID = 0; intSAL1_ID = 0; intMA_ID = 0;
        //                intSAL6_ID1 = string.Empty; intSAL5_ID1 = string.Empty; intSAL4_ID1 = string.Empty;
        //                intSAL3_ID1 = string.Empty; intSAL2_ID1 = string.Empty; intSAL1_ID1 = string.Empty; intMA_ID1 = string.Empty;

        //                intPrevPCC_ID = intPCC_ID;
        //                intPrevPCC_ID1 = intPCC_ID1;

        //                //HTMLForTree = HTMLForTree + "<li id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"1\" data-view=" + HttpUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " class=\"folder clsExtension\"><b id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"1\" data-view=" + HttpUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " style=\"font-weight: 100;\">" + HttpUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " </b>";



        //                if (dr["Sec_IC"].ToString() != "342392")
        //                {
        //                    HTMLForTree = HTMLForTree + "<li id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"1\" data-view=" + HttpUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " class=\"folder clsExtension\"><b id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"1\" data-view=" + HttpUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " style=\"font-weight: 100;\">" + HttpUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " </b>";
        //                }
        //                else if (dr["Sec_IC"].ToString() == "342392")
        //                {
        //                    if (dr["Sec_No"].ToString().Contains("http://") || dr["Sec_No"].ToString().Contains("https://"))
        //                    {
        //                        HTMLForTree = HTMLForTree + "<li id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"1\" data-view=" + HttpUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"1\" data-view=" + HttpUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + HttpUtility.HtmlEncode(dr["Sec_No"].ToString()) + "'>" + HttpUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + "</a></b>";
        //                    }
        //                    else
        //                    {
        //                        string AWSPrefix = "Images/AssemblyServiceLinkAttachments";
        //                        HTMLForTree = HTMLForTree + "<li id ='\"" + dr["Sec_IC"].ToString() + "\"' IsChapter='\"-1\"' data-view='" + HttpUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + "' class='\"clsExtension\"'><b class='far fa-dot-circle' style='color:red'></b> <b id='" + dr["Sec_IC"].ToString() + "' IsChapter='\"-1\"' data-view=" + HttpUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " style='\"font-weight: 100;\"'><a target='_blank' style='color:red;text-decoration:underline;' href='" + (Commo.GetAWSObjectURL(HttpUtility.HtmlEncode(dr["Sec_No"].ToString()), AWSPrefix)) + "'>" + HttpUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + "</a></b>";
        //                    }
        //                }

        //                intPCC_ID = Convert.ToInt32(dr["Sec_IC"].ToString());
        //                intPCC_ID1 = dr["Sec_Desc"].ToString();
        //            }

        //            var jdf = dr["SSec_IC"].ToString();
        //            //Sub Section / Main Assembly Level
        //            if (jdf != "")
        //            {
        //                Checknextid = false;
        //                if (Int32.Parse(NovaTreeLoadObject.TreeLevel.ToString()) >= 2 && intMA_ID != Convert.ToInt32(dr["SSec_IC"]) || intMA_ID1 != dr["SSec_Desc"].ToString())
        //                //if (Int32.Parse(GetTDataPartAssemblyLevelObj.TreeLevel.ToString()) >= 2 && intMA_ID != Convert.ToInt32(dr["SSec_IC"].ToString())) //intMA_ID !=
        //                {
        //                    if (intSAL6_ID != 0 || intSAL6_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul>";
        //                    }
        //                    else if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul>";
        //                    }
        //                    else if (intSAL4_ID != 0 || intSAL4_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul></ul></ul>";
        //                    }
        //                    else if (intSAL3_ID != 0 || intSAL3_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                    }
        //                    else if (intSAL2_ID != 0 || intSAL2_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul>";
        //                    }
        //                    else if (intSAL1_ID != 0 || intSAL1_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul>";
        //                    }


        //                    if (intPrevPCC_ID != intPCC_ID || intPrevPCC_ID1 != intPCC_ID1)
        //                    {
        //                        intPrevPCC_ID = intPCC_ID;
        //                        intPrevPCC_ID1 = intPCC_ID1;
        //                        HTMLForTree = HTMLForTree + "<ul>";
        //                    }
        //                    intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0; intSAL3_ID = 0; intSAL2_ID = 0; intSAL1_ID = 0;
        //                    intSAL6_ID1 = string.Empty; intSAL5_ID1 = string.Empty; intSAL4_ID1 = string.Empty; intSAL3_ID1 = string.Empty;
        //                    intSAL2_ID1 = string.Empty; intSAL1_ID1 = string.Empty;

        //                    intPrevMA_ID = intMA_ID;
        //                    intPrevMA_ID1 = intMA_ID1;

        //                    // APPEND PARTS ASSIST SERVCE DOCUMENTS - NOVA ONLY 
        //                    if (SecNoSecDocBind != dr["Sec_No"].ToString())
        //                    {
        //                        using (SqlConnection conn = new SqlConnection(Conn))
        //                        {
        //                            var LanguageIDV = NovaTreeLoadObject.Langauge_ID;
        //                            DataSet dsSD = new DataSet("TreeView");
        //                            string MainSectionCode = "";
        //                            string[] SectionCodesarr = dr["Sec_No"].ToString().Split('-');
        //                            MainSectionCode = SectionCodesarr[1].ToString();
        //                            SqlCommand sqlComm = new SqlCommand("SEl_ServiceDocumentsforTree_NOVA", conn);
        //                            sqlComm.Parameters.AddWithValue("@OrderNumber", dr["Part_assembly_No"].ToString());
        //                            sqlComm.Parameters.AddWithValue("@SectionCode", MainSectionCode);
        //                            sqlComm.Parameters.AddWithValue("@DocumentLanguage_ID", LanguageIDV);  // DK - Added on 03-Sep-21
        //                            sqlComm.Parameters.AddWithValue("@User_ID", LoginUser_ID);
        //                            sqlComm.CommandType = CommandType.StoredProcedure;
        //                            SqlDataAdapter daSD = new SqlDataAdapter();
        //                            sqlComm.CommandTimeout = 100;
        //                            daSD.SelectCommand = sqlComm;
        //                            daSD.Fill(dsSD);

        //                            //string FullServiceSectionPath = "TESTPath//";

        //                            if (dsSD.Tables[0].Rows.Count > 0)
        //                            {
        //                                HTMLForTree = HTMLForTree + "<li id=\"li" + 0 + "\" IsChapter='0' data-view=" + 0 + " class=\"folder clsExtension\"><b id=\"" + 0 + "\" IsChapter=\"0\" data-view=" + dr["SSec_No"].ToString() + " style=\"font-weight: 100;color:green;\">" + GetGlobalResourceObject(NovaTreeLoadObject.Culture.ToString(), "ResServiceDocuments").ToString() + " </b>";
        //                                HTMLForTree = HTMLForTree + "<ul>";
        //                                foreach (DataRow dr1 in dsSD.Tables[0].Rows)
        //                                {
        //                                    string AWSPrefix = "ServiceDocument/docs/" + dr1.Field<string>("DocumentTypeCode");
        //                                    string FullServiceSectionPath = Commo.GetAWSObjectURL(dr1.Field<string>("DocumentFileName") + "." + dr1.Field<string>("FileType_Extension"), AWSPrefix);
        //                                    HTMLForTree = HTMLForTree + "<li id=\"li" + dr1["DocumentTypeCode"].ToString() + "\" IsChapter='0' data-view=" + dr1["DocumentFileName"].ToString() + " class=\"clsExtension\"><b class='far fa fa-cog' style='color:green'></b> <b  id=\"" + dr1["DocumentTypeCode"].ToString() + "\" IsChapter=\"0\" data-view=" + dr1["DocumentTypeName"].ToString() + " style=\"font-weight: 100;color:green\">" + "<a target='_blank' style='color:green;text-decoration:underline;' href='" + FullServiceSectionPath + "'>" + " " + dr1["ServiceDocumentLinkDesc"] + "</a>" + "</b>";

        //                                }
        //                                HTMLForTree = HTMLForTree + "</ul>";
        //                            }

        //                            //ITP START DOCUMENTS
        //                            DataSet dsITP = new DataSet("TreeViewITP");
        //                            string MainSectionCodeITP = "";
        //                            string[] SectionCodesarrITP = dr["Sec_No"].ToString().Split('-');
        //                            MainSectionCodeITP = SectionCodesarrITP[1].ToString();
        //                            SqlCommand sqlCommITP = new SqlCommand("SEl_ITP_DocumentsforTree_NOVA", conn);
        //                            sqlCommITP.Parameters.AddWithValue("@OrderNumber", dr["Part_assembly_No"].ToString());
        //                            sqlCommITP.Parameters.AddWithValue("@SectionCode", MainSectionCodeITP);
        //                            sqlCommITP.Parameters.AddWithValue("@DocumentLanguage_ID", LanguageIDV);  // DK - Added on 03-Sep-21
        //                            sqlCommITP.Parameters.AddWithValue("@User_ID", LoginUser_ID);
        //                            sqlCommITP.CommandType = CommandType.StoredProcedure;
        //                            SqlDataAdapter dAITP = new SqlDataAdapter();
        //                            sqlCommITP.CommandTimeout = 100;
        //                            dAITP.SelectCommand = sqlCommITP;
        //                            dAITP.Fill(dsITP);

        //                            //string FullServiceSectionPath = "TESTPath//";

        //                            if (dsITP.Tables[0].Rows.Count > 0)
        //                            {
        //                                HTMLForTree = HTMLForTree + "<li id=\"li" + 0 + "\" IsChapter='0' data-view=" + 0 + " class=\"folder clsExtension\"><b id=\"" + 0 + "\" IsChapter=\"0\" data-view=" + 0 + " style=\"font-weight: 100;color:brown;\">" + GetGlobalResourceObject(NovaTreeLoadObject.Culture.ToString(), "ITPDocuments").ToString() + " </b>";
        //                                HTMLForTree = HTMLForTree + "<ul>";
        //                                foreach (DataRow dr1 in dsITP.Tables[0].Rows)
        //                                {
        //                                    string AWSPrefix = "ServiceDocument/docs/" + dr1.Field<string>("DocumentTypeCode");
        //                                    string FullServiceSectionPath = Commo.GetAWSObjectURL(dr1.Field<string>("DocumentFileName") + "." + dr1.Field<string>("FileType_Extension"), AWSPrefix);
        //                                    HTMLForTree = HTMLForTree + "<li id=\"li" + dr1["DocumentTypeCode"].ToString() + "\" IsChapter='0' data-view=" + dr1["DocumentFileName"].ToString() + " class=\" clsExtension\"><b class='far fa fa-cog' style='color:brown'></b>  <b id=\"" + dr1["DocumentTypeCode"].ToString() + "\" IsChapter=\"0\" data-view=" + dr1["DocumentTypeName"].ToString() + " style=\"font-weight: 100;color:brown\">" + "<a target='_blank' style='color:brown;text-decoration:underline;' href='" + FullServiceSectionPath + "'>" + dr1["ServiceDocumentLinkDesc"] + "</a>" + "</b>";
        //                                }
        //                                HTMLForTree = HTMLForTree + "</ul>";
        //                            }
        //                            //END ITP

        //                            //START ----Other Documents Other the Service Documnets and ITP --Without Folder Root
        //                            DataSet dsOtherDoc = new DataSet("TreeViewOthertype");
        //                            string MainSectionCodeOtherDoc = "";
        //                            string[] SectionCodesarrOtherDoc = dr["Sec_No"].ToString().Split('-');
        //                            MainSectionCodeOtherDoc = SectionCodesarrOtherDoc[1].ToString();
        //                            SqlCommand sqlCommOtherDoc = new SqlCommand("SEl_OtherTypeDocumentsforTree_NOVA", conn);
        //                            sqlCommOtherDoc.Parameters.AddWithValue("@OrderNumber", dr["Part_assembly_No"].ToString());
        //                            sqlCommOtherDoc.Parameters.AddWithValue("@SectionCode", MainSectionCodeOtherDoc);
        //                            sqlCommOtherDoc.Parameters.AddWithValue("@DocumentLanguage_ID", LanguageIDV);  // DK - Added on 03-Sep-21
        //                            sqlCommOtherDoc.Parameters.AddWithValue("@User_ID", LoginUser_ID);
        //                            sqlCommOtherDoc.CommandType = CommandType.StoredProcedure;
        //                            SqlDataAdapter dAOtherdoc = new SqlDataAdapter();
        //                            sqlCommOtherDoc.CommandTimeout = 100;
        //                            dAOtherdoc.SelectCommand = sqlCommOtherDoc;
        //                            dAOtherdoc.Fill(dsOtherDoc);


        //                            if (dsOtherDoc.Tables[0].Rows.Count > 0)
        //                            {

        //                                foreach (DataRow dr1 in dsOtherDoc.Tables[0].Rows)
        //                                {
        //                                    string AWSPrefix = "ServiceDocument/docs/" + dr1.Field<string>("DocumentTypeCode");
        //                                    string FullServiceSectionPath = Commo.GetAWSObjectURL(dr1.Field<string>("DocumentFileName") + "." + dr1.Field<string>("FileType_Extension"), AWSPrefix);
        //                                    HTMLForTree = HTMLForTree + "<li id=\"li" + dr1["DocumentTypeCode"].ToString() + "\" IsChapter='0' data-view=" + dr1["DocumentFileName"].ToString() + " class=\" clsExtension\"><b class='far fa fa-square' style='color:darkviolet'></b> <b id=\"" + dr1["DocumentTypeCode"].ToString() + "\" IsChapter=\"0\" data-view=" + dr1["DocumentTypeName"].ToString() + " style=\"font-weight: 100;color:green\">" + "<a target='_blank' style='color:purple;text-decoration:underline;' href='" + FullServiceSectionPath + "'>" + dr1["ServiceDocumentLinkDesc"] + "</a>" + "</b>";
        //                                }

        //                            }
        //                            //END ----Other Documents Other the Service Documnets and 

        //                        }

        //                    }
        //                    //END


        //                    if (dr["SSec_IC"].ToString() != "342392")
        //                    {
        //                        HTMLForTree = HTMLForTree + "<li id=\"li" + dr["SSec_IC"].ToString() + "\" IsChapter='0' data-view=" + dr["SSec_No"].ToString() + " class=\"folder clsExtension\"><b id=\"" + dr["SSec_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + dr["SSec_No"].ToString() + " style=\"font-weight: 100;\">" + HttpUtility.HtmlEncode(dr["SSec_Desc"].ToString()) + " </b>";
        //                    }
        //                    else if (dr["SSec_IC"].ToString() == "342392")
        //                    {
        //                        if (dr["SSec_No"].ToString().Contains("http://") || dr["SSec_No"].ToString().Contains("https://"))
        //                        {
        //                            HTMLForTree = HTMLForTree + "<li id=\"" + dr["SSec_IC"].ToString() + "\" IsChapter=\"1\" data-view=" + HttpUtility.HtmlEncode(dr["SSec_Desc"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["SSec_IC"].ToString() + "\" IsChapter=\"1\" data-view=" + HttpUtility.HtmlEncode(dr["SSec_Desc"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + HttpUtility.HtmlEncode(dr["SSec_No"].ToString()) + "'>" + HttpUtility.HtmlEncode(dr["SSec_Desc"].ToString()) + "</a></b>";
        //                        }
        //                        else
        //                        {
        //                            string AWSPrefix = "Images/AssemblyServiceLinkAttachments";
        //                            HTMLForTree = HTMLForTree + "<li id ='\"" + dr["SSec_IC"].ToString() + "\"' IsChapter='\"-1\"' data-view='" + HttpUtility.HtmlEncode(dr["SSec_Desc"].ToString()) + "' class='\"clsExtension\"'><b class='far fa-dot-circle' style='color:red'></b> <b id='" + dr["SSec_IC"].ToString() + "' IsChapter='\"-1\"' data-view=" + HttpUtility.HtmlEncode(dr["SSec_Desc"].ToString()) + " style='\"font-weight: 100;\"'><a target='_blank' style='color:red;text-decoration:underline;' href='" + (Commo.GetAWSObjectURL(HttpUtility.HtmlEncode(dr["SSec_No"].ToString()), AWSPrefix)) + "'>" + HttpUtility.HtmlEncode(dr["SSec_Desc"].ToString()) + "</a></b>";
        //                        }
        //                    }

        //                    intMA_ID = Convert.ToInt32(dr["SSec_IC"].ToString());
        //                    intMA_ID1 = dr["SSec_Desc"].ToString();
        //                    SecNoSecDocBind = dr["Sec_No"].ToString();
        //                }
        //                else
        //                {

        //                }
        //            }
        //            else
        //            {
        //                Checknextid = true;
        //                HTMLForTree = HTMLForTree + "</li>";
        //            }

        //            //Sub Assembly Level 1
        //            if (Int32.Parse(NovaTreeLoadObject.TreeLevel.ToString()) >= 3 && dr["L1_IC"].ToString() != "")
        //            {
        //                if (intSAL1_ID != Convert.ToInt32(dr["L1_IC"].ToString()) || intSAL1_ID1 != dr["L1_Desc"].ToString())
        //                {
        //                    if (intSAL6_ID != 0 || intSAL6_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul>";
        //                    }
        //                    else if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul></ul></ul>";
        //                    }
        //                    else if (intSAL4_ID != 0 || intSAL4_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                    }
        //                    else if (intSAL3_ID != 0 || intSAL3_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul>";
        //                    }
        //                    else if (intSAL2_ID != 0 || intSAL2_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul>";
        //                    }

        //                    if (intPrevMA_ID != intMA_ID || intPrevMA_ID1 != intMA_ID1)
        //                    {
        //                        intPrevMA_ID = intMA_ID;
        //                        intPrevMA_ID1 = intMA_ID1;
        //                        HTMLForTree = HTMLForTree + "<ul>";
        //                    }
        //                    intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0; intSAL3_ID = 0; intSAL2_ID = 0;
        //                    intSAL6_ID1 = string.Empty; intSAL5_ID1 = string.Empty; intSAL4_ID1 = string.Empty;
        //                    intSAL3_ID1 = string.Empty; intSAL2_ID1 = string.Empty;

        //                    intPrevSAL1_ID = intSAL1_ID;
        //                    intPrevSAL1_ID1 = intSAL1_ID1;
        //                    if (dr["L1_IC"].ToString() != "342392")
        //                    {
        //                        HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L1_IC"].ToString() + "\" IsChapter='0' data-view=" + HttpUtility.HtmlEncode(dr["L1_No"].ToString()) + " class=\"clsExtension\"><b id=\"" + dr["L1_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + HttpUtility.HtmlEncode(dr["L1_No"].ToString()) + " style=\"font-weight: 100;\">" + HttpUtility.HtmlEncode(dr["L1_Desc"].ToString()) + "</b>";
        //                    }
        //                    else if (dr["L1_IC"].ToString() == "342392")
        //                    {
        //                        using (SqlConnection connSM = new SqlConnection(Conn))
        //                        {
        //                            //START ----Other Documents Other the Service Documnets and ITP --Without Folder Root
        //                            DataSet dsSMOAC = new DataSet("ServiceManualsAccess");
        //                            string ServiceManualsAccessC = "";
        //                            ServiceManualsAccessC = dr["L1_No"].ToString();
        //                            SqlCommand sqlCommSMOCDocC = new SqlCommand("SEl_ServiceManualAccessCheck_NOVA", connSM);
        //                            sqlCommSMOCDocC.Parameters.AddWithValue("@ServiceSection", ServiceManualsAccessC);
        //                            sqlCommSMOCDocC.Parameters.AddWithValue("@Order_ID", Model_ID);
        //                            sqlCommSMOCDocC.Parameters.AddWithValue("@User_ID", LoginUser_ID);
        //                            sqlCommSMOCDocC.CommandType = CommandType.StoredProcedure;
        //                            SqlDataAdapter dASMOAC = new SqlDataAdapter();
        //                            sqlCommSMOCDocC.CommandTimeout = 100;
        //                            dASMOAC.SelectCommand = sqlCommSMOCDocC;
        //                            dASMOAC.Fill(dsSMOAC);

        //                            if (dsSMOAC.Tables[0].Rows.Count > 0)
        //                            {
        //                                using (SqlConnection connSMA = new SqlConnection(Conn))
        //                                {
        //                                    //START ----Other Documents Other the Service Documnets and ITP --Without Folder Root
        //                                    DataSet dsSMOA = new DataSet("ServiceManualsAccess");
        //                                    string ServiceManualsAccess = "";
        //                                    ServiceManualsAccess = dr["L1_No"].ToString();
        //                                    SqlCommand sqlCommSMOCDoc = new SqlCommand("SEl_ServiceManualAccess_NOVA", connSMA);
        //                                    sqlCommSMOCDoc.Parameters.AddWithValue("@ServiceSection", ServiceManualsAccess);
        //                                    sqlCommSMOCDoc.Parameters.AddWithValue("@Order_ID", Model_ID);
        //                                    sqlCommSMOCDoc.Parameters.AddWithValue("@IsServiceManualAcess", 1);
        //                                    sqlCommSMOCDoc.Parameters.AddWithValue("@User_ID", LoginUser_ID);
        //                                    sqlCommSMOCDoc.CommandType = CommandType.StoredProcedure;
        //                                    SqlDataAdapter dASMOA = new SqlDataAdapter();
        //                                    sqlCommSMOCDoc.CommandTimeout = 100;
        //                                    dASMOA.SelectCommand = sqlCommSMOCDoc;
        //                                    dASMOA.Fill(dsSMOA);


        //                                    if (dsSMOA.Tables[0].Rows.Count > 0)
        //                                    {

        //                                        string CurrentLanguage = NovaTreeLoadObject.Lang.ToString();

        //                                        string PathServS = dr["ServiceSectionLink"].ToString();
        //                                        string ReplaceLang = "";

        //                                        string FilenameForAWS = "";
        //                                        string Prefixpath = "";
        //                                        if (CurrentLanguage == "en")
        //                                        {
        //                                            ReplaceLang = PathServS.Replace("ENGLISH", "EN");
        //                                            ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                            string[] pathComponents = ReplaceLang.Split('/');
        //                                            Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                            FilenameForAWS = pathComponents[2];
        //                                        }
        //                                        else if (CurrentLanguage == "Fr")
        //                                        {
        //                                            ReplaceLang = PathServS.Replace("FRENCH", "FR");
        //                                            ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                            string[] pathComponents = ReplaceLang.Split('/');
        //                                            Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                            FilenameForAWS = pathComponents[2];
        //                                        }

        //                                        string AWSPrefix = "Service_Section_And_SubSection_Manuals/MANUE/ENTR/ENTR-LFS/" + Prefixpath;
        //                                        string FullServiceSectionPath = Commo.GetAWSObjectURL(FilenameForAWS.Trim(), AWSPrefix);
        //                                        //START --.Adding to Ftech Dyanamic Service Manuls Section Desctiption in Tree --06-Dec-21
        //                                        string SectionDescription = string.Empty;
        //                                        //using (SqlConnection conn = new SqlConnection(Conn))
        //                                        //{
        //                                        //    var LanguageIDV = NovaTreeLoadObject.Langauge_ID;
        //                                        //    DataTable dtServiceManul = new DataTable();
        //                                        //    SqlCommand sqlComm = new SqlCommand("SEl_ServiceManualsforTree_NOVA", conn);
        //                                        //    sqlComm.Parameters.AddWithValue("@Catalogue_ID", Model_ID);
        //                                        //    sqlComm.Parameters.AddWithValue("@SectionCode", dr["L1_No"].ToString());
        //                                        //    sqlComm.Parameters.AddWithValue("@Language_ID", CurrentLanguage);
        //                                        //    sqlComm.Parameters.AddWithValue("@User_ID", LoginUser_ID);
        //                                        //    sqlComm.CommandType = CommandType.StoredProcedure;
        //                                        //    sqlComm.CommandTimeout = 100;
        //                                        //    SqlDataAdapter adp = new SqlDataAdapter(sqlComm);
        //                                        //    adp.Fill(dtServiceManul);
        //                                        //    if (dtServiceManul.Rows.Count > 0)
        //                                        //    {
        //                                        //        SectionDescription = dtServiceManul.Rows[0]["Service_SubSection_Description"].ToString();
        //                                        //        SectionDescription = dr["L1_No"].ToString() + " " + SectionDescription;
        //                                        //    }
        //                                        //    else
        //                                        //    {
        //                                        //        SectionDescription = HttpUtility.HtmlEncode(dr["L1_Desc"].ToString());
        //                                        //    }
        //                                        //}

        //                                        if (dr["L1_No"].ToString().Contains("http://") || dr["L1_No"].ToString().Contains("https://"))
        //                                        {
        //                                            HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L1_IC"].ToString() + "\" IsChapter='0' data-view=" + HttpUtility.HtmlEncode(dr["L1_No"].ToString()) + " class=\"clsExtension\"> <b class='far fa-dot-circle' style='color:red'></b> <b  id=\"" + dr["L1_IC"].ToString() + "\" IsChapter=\"0\"  data-view=" + HttpUtility.HtmlEncode(dr["L1_No"].ToString()) + " style=\"font-weight: 100;color:blue\" >" + "<a target='_blank' style='color:red;text-decoration:underline;' href='" + HttpUtility.HtmlEncode(dr["L1_No"].ToString()) + "'>" + HttpUtility.HtmlEncode(dr["L1_Desc"].ToString()) + "</a>" + "</b>";

        //                                        }
        //                                        else
        //                                        {
        //                                            string AZUREPrefix = "Images/AssemblyServiceLinkAttachments";
        //                                            HTMLForTree = HTMLForTree + "<li id='\"li" + dr["L1_IC"].ToString() + "\"' IsChapter='0' data-view='" + HttpUtility.HtmlEncode(dr["L1_No"].ToString()) + "' class='\"clsExtension\"'> <b class='far fa-dot-circle' style='color:red'></b> <b  id='\"" + dr["L1_IC"].ToString() + "\"' IsChapter='0'  data-view='" + HttpUtility.HtmlEncode(dr["L1_No"].ToString()) + "' style='\"font-weight: 100;color:blue\"' >" + "<a target='_blank' style='color:red;text-decoration:underline;' href='" + Commo.GetAWSObjectURL(HttpUtility.HtmlEncode(dr["L1_No"].ToString()), AZUREPrefix) + "'>" + HttpUtility.HtmlEncode(dr["L1_Desc"].ToString()) + "</a>" + "</b>";

        //                                        }


        //                                        //HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L1_IC"].ToString() + "\" IsChapter='0' data-view=" + HttpUtility.HtmlEncode(dr["L1_No"].ToString()) + " class=\"clsExtension\" > <i class='ClsTreeServiceManualsOrders' data-view=" + dr["L1_No"].ToString() + "><b class='far fa-dot-circle'   style ='color:red' ></b></i> <b  id=\"" + dr["L1_IC"].ToString() + "\" IsChapter=\"0\"  data-view=" + dr["L1_No"].ToString() + " style=\"font-weight: 100;color:blue\" >" + "<a target='_blank' style='color:red;text-decoration:underline;' href='" + FullServiceSectionPath + "'>" + SectionDescription + "</a>" + "</b>";

        //                                    }
        //                                }
        //                            }
        //                            else
        //                            {
        //                                string CurrentLanguage = NovaTreeLoadObject.Lang.ToString();
        //                                string PathServS = dr["ServiceSectionLink"].ToString();
        //                                string ReplaceLang = "";

        //                                string FilenameForAWS = "";
        //                                string Prefixpath = "";
        //                                if (CurrentLanguage == "en")
        //                                {
        //                                    ReplaceLang = PathServS.Replace("ENGLISH", "EN");
        //                                    ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                    string[] pathComponents = ReplaceLang.Split('/');
        //                                    Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                    FilenameForAWS = pathComponents[2];
        //                                }
        //                                else if (CurrentLanguage == "Fr")
        //                                {
        //                                    ReplaceLang = PathServS.Replace("FRENCH", "FR");
        //                                    ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                    string[] pathComponents = ReplaceLang.Split('/');
        //                                    Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                    FilenameForAWS = pathComponents[2];
        //                                }

        //                                string AWSPrefix = "Service_Section_And_SubSection_Manuals/MANUE/ENTR/ENTR-LFS/" + Prefixpath;
        //                                string FullServiceSectionPath = Commo.GetAWSObjectURL(FilenameForAWS.Trim(), AWSPrefix);

        //                                string SectionDescription = string.Empty;
        //                                //using (SqlConnection conn = new SqlConnection(Conn))
        //                                //{
        //                                //    var LanguageIDV = NovaTreeLoadObject.Langauge_ID;
        //                                //    DataTable dtServiceManul = new DataTable();
        //                                //    SqlCommand sqlComm = new SqlCommand("SEl_ServiceManualsforTree_NOVA", conn);
        //                                //    sqlComm.Parameters.AddWithValue("@Catalogue_ID", Model_ID);
        //                                //    sqlComm.Parameters.AddWithValue("@SectionCode", dr["L1_No"].ToString());
        //                                //    sqlComm.Parameters.AddWithValue("@Language_ID", CurrentLanguage);
        //                                //    sqlComm.Parameters.AddWithValue("@User_ID", LoginUser_ID);
        //                                //    sqlComm.CommandType = CommandType.StoredProcedure;
        //                                //    sqlComm.CommandTimeout = 100;
        //                                //    SqlDataAdapter adp = new SqlDataAdapter(sqlComm);
        //                                //    adp.Fill(dtServiceManul);
        //                                //    if (dtServiceManul.Rows.Count > 0)
        //                                //    {
        //                                //        SectionDescription = dtServiceManul.Rows[0]["Service_SubSection_Description"].ToString();
        //                                //        SectionDescription = dr["L1_No"].ToString() + " " + SectionDescription;
        //                                //    }
        //                                //    else
        //                                //    {
        //                                //        SectionDescription = HttpUtility.HtmlEncode(dr["L1_Desc"].ToString());
        //                                //    }
        //                                //}



        //                                //Commented below lin by DK 31-May-24 and added if cond to take form parts master service link 
        //                                //HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L1_IC"].ToString() + "\" IsChapter='0' data-view=" + dr["L1_No"].ToString() + " class=\"clsExtension\" > <i class='ClsTreeServiceManualsOrders' data-view=" + dr["L1_No"].ToString() + "><b class='far fa-dot-circle'   style ='color:red' ></b></i> <b  id=\"" + dr["L1_IC"].ToString() + "\" IsChapter=\"0\"  data-view=" + dr["L1_No"].ToString() + " style=\"font-weight: 100;color:blue\" >" + "<a target='_blank' style='color:red;text-decoration:underline;' href='" + FullServiceSectionPath + "'>" + SectionDescription + "</a>" + "</b>";

        //                                if (dr["L1_No"].ToString().Contains("http://") || dr["L1_No"].ToString().Contains("https://"))
        //                                {
        //                                    HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L1_IC"].ToString() + "\" IsChapter='0' data-view=" + HttpUtility.HtmlEncode(dr["L1_No"].ToString()) + " class=\"clsExtension\"> <b class='far fa-dot-circle' style='color:red'></b> <b  id=\"" + dr["L1_IC"].ToString() + "\" IsChapter=\"0\"  data-view=" + HttpUtility.HtmlEncode(dr["L1_No"].ToString()) + " style=\"font-weight: 100;color:blue\" >" + "<a target='_blank' style='color:red;text-decoration:underline;' href='" + HttpUtility.HtmlEncode(dr["L1_No"].ToString()) + "'>" + HttpUtility.HtmlEncode(dr["L1_Desc"].ToString()) + "</a>" + "</b>";

        //                                }
        //                                else
        //                                {
        //                                    string AZUREPrefix = "Images/AssemblyServiceLinkAttachments";
        //                                    HTMLForTree = HTMLForTree + "<li id='\"li" + dr["L1_IC"].ToString() + "\"' IsChapter='0' data-view='" + HttpUtility.HtmlEncode(dr["L1_No"].ToString()) + "' class='\"clsExtension\"'> <b class='far fa-dot-circle' style='color:red'></b> <b  id='\"" + dr["L1_IC"].ToString() + "\"' IsChapter='0'  data-view='" + HttpUtility.HtmlEncode(dr["L1_No"].ToString()) + "' style='\"font-weight: 100;color:blue\"' >" + "<a target='_blank' style='color:red;text-decoration:underline;' href='" + Commo.GetAWSObjectURL(HttpUtility.HtmlEncode(dr["L1_No"].ToString()), AZUREPrefix) + "'>" + HttpUtility.HtmlEncode(dr["L1_Desc"].ToString()) + "</a>" + "</b>";

        //                                }

        //                            }
        //                        }

        //                    }
        //                    intSAL1_ID = Convert.ToInt32(dr["L1_IC"].ToString());
        //                    intSAL1_ID1 = dr["L1_Desc"].ToString();
        //                }
        //            }
        //            else
        //            {
        //                intSAL1_ID = 0;
        //                intSAL1_ID1 = string.Empty;
        //            }
        //            //Sub Assembly Level 2
        //            if (Int32.Parse(NovaTreeLoadObject.TreeLevel.ToString()) >= 4 && dr["L2_IC"].ToString() != "")
        //            {
        //                if (intSAL2_ID != Convert.ToInt32(dr["L2_IC"].ToString()) || intSAL2_ID1 != dr["L2_Desc"].ToString())
        //                {
        //                    if (intSAL6_ID != 0 || intSAL6_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul></ul></ul>";
        //                    }
        //                    else if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                    }
        //                    else if (intSAL4_ID != 0 || intSAL4_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul>";
        //                    }
        //                    else if (intSAL3_ID != 0 || intSAL3_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul>";
        //                    }

        //                    if (intPrevSAL1_ID != intSAL1_ID || intPrevSAL1_ID1 != intSAL1_ID1)
        //                    {
        //                        intPrevSAL1_ID = intSAL1_ID;
        //                        intPrevSAL1_ID1 = intSAL1_ID1;
        //                        HTMLForTree = HTMLForTree + "<ul>";
        //                    }
        //                    intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0; intSAL3_ID = 0;
        //                    intSAL6_ID1 = string.Empty; intSAL5_ID1 = string.Empty; intSAL4_ID1 = string.Empty; intSAL3_ID1 = string.Empty;
        //                    intPrevSAL2_ID = intSAL2_ID;
        //                    intPrevSAL2_ID1 = intSAL2_ID1;
        //                    HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L2_IC"].ToString() + "\" IsChapter='0' data-view=" + HttpUtility.HtmlEncode(dr["L2_No"].ToString()) + " class=\"clsExtension\"><b id=\"" + dr["L2_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + HttpUtility.HtmlEncode(dr["L2_No"].ToString()) + " style=\"font-weight: 100;\">" + HttpUtility.HtmlEncode(dr["L2_Desc"].ToString()) + "</b>";
        //                    intSAL2_ID = Convert.ToInt32(dr["L2_IC"].ToString());
        //                    intSAL2_ID1 = dr["L2_Desc"].ToString();
        //                }
        //            }
        //            else
        //            {
        //                intSAL2_ID = 0;
        //                intSAL2_ID1 = string.Empty;
        //            }
        //            //Sub Assembly Level 3
        //            if (Int32.Parse(NovaTreeLoadObject.TreeLevel.ToString()) >= 5 && dr["L3_IC"].ToString() != "")
        //            {
        //                if (intSAL3_ID != Convert.ToInt32(dr["L3_IC"].ToString()) || intSAL3_ID1 != dr["L3_Desc"].ToString())
        //                {
        //                    if (intSAL6_ID != 0 || intSAL6_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                    }
        //                    else if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul>";
        //                    }
        //                    else if (intSAL4_ID != 0 || intSAL4_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul>";
        //                    }

        //                    if (intPrevSAL2_ID != intSAL2_ID || intPrevSAL2_ID1 != intSAL2_ID1)
        //                    {
        //                        intPrevSAL2_ID = intSAL2_ID;
        //                        intPrevSAL2_ID1 = intSAL2_ID1;
        //                        HTMLForTree = HTMLForTree + "<ul>";
        //                    }
        //                    intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0;
        //                    intSAL6_ID1 = string.Empty; intSAL5_ID1 = string.Empty; intSAL4_ID1 = string.Empty;
        //                    intPrevSAL3_ID = intSAL3_ID;
        //                    intPrevSAL3_ID1 = intSAL3_ID1;
        //                    HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L3_IC"].ToString() + "\" IsChapter='0' data-view=" + HttpUtility.HtmlEncode(dr["L3_No"].ToString()) + " class=\"clsExtension\"><b id=\"" + dr["L3_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + HttpUtility.HtmlEncode(dr["L3_No"].ToString()) + " style=\"font-weight: 100;\">" + HttpUtility.HtmlEncode(dr["L3_Desc"].ToString()) + "</b>";
        //                    intSAL3_ID = Convert.ToInt32(dr["L3_IC"].ToString());
        //                    intSAL3_ID1 = dr["L3_Desc"].ToString();
        //                }
        //            }
        //            else
        //            {
        //                intSAL3_ID = 0;
        //                intSAL3_ID1 = string.Empty;
        //            }
        //            //Sub Assembly Level 4
        //            if (Int32.Parse(NovaTreeLoadObject.TreeLevel.ToString()) >= 6 && dr["L4_IC"].ToString() != "")
        //            {
        //                if (intSAL4_ID != Convert.ToInt32(dr["L4_IC"].ToString()) || intSAL4_ID1 != dr["L4_Desc"].ToString())
        //                {
        //                    if (intSAL6_ID != 0 || intSAL6_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul>";
        //                    }
        //                    else if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul>";
        //                    }

        //                    if (intPrevSAL3_ID != intSAL3_ID || intPrevSAL3_ID1 != intSAL3_ID1)
        //                    {
        //                        intPrevSAL3_ID = intSAL3_ID;
        //                        intPrevSAL3_ID1 = intSAL3_ID1;
        //                        HTMLForTree = HTMLForTree + "<ul>";
        //                    }
        //                    intSAL6_ID = 0; intSAL5_ID = 0;
        //                    intSAL6_ID1 = string.Empty; intSAL5_ID1 = string.Empty;

        //                    intPrevSAL4_ID = intSAL4_ID;
        //                    intPrevSAL4_ID1 = intSAL4_ID1;
        //                    HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L4_IC"].ToString() + "\" IsChapter='0' data-view=" + HttpUtility.HtmlEncode(dr["L4_No"].ToString()) + " class=\"clsExtension\"><b id=\"" + dr["L4_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + HttpUtility.HtmlEncode(dr["L4_No"].ToString()) + " style=\"font-weight: 100;\">" + HttpUtility.HtmlEncode(dr["L4_Desc"].ToString()) + "</b>";
        //                    intSAL4_ID = Convert.ToInt32(dr["L4_IC"].ToString());
        //                    intSAL4_ID1 = dr["L4_Desc"].ToString();
        //                }
        //            }
        //            else
        //            {
        //                intSAL4_ID = 0;
        //                intSAL4_ID1 = string.Empty;
        //            }
        //            intRow = intRow + 1;
        //        }

        //        //loop end
        //        if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //        {
        //            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul></ul></ul></ul>";
        //        }
        //        else if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //        {
        //            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul></ul></ul>";
        //        }
        //        else if (intSAL4_ID != 0 || intSAL4_ID1 != string.Empty)
        //        {
        //            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul></ul>";
        //        }
        //        else if (intSAL3_ID != 0 || intSAL3_ID1 != string.Empty)
        //        {
        //            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul>";
        //        }
        //        else if (intSAL2_ID != 0 || intSAL2_ID1 != string.Empty)
        //        {
        //            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul>";
        //        }
        //        else if (intSAL1_ID != 0 || intSAL1_ID1 != string.Empty)
        //        {
        //            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul>";
        //        }
        //        else if (intMA_ID != 0 || intMA_ID1 != string.Empty)
        //        {
        //            HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //        }
        //        else
        //        {
        //            HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //        }
        //        intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0; intSAL3_ID = 0; intSAL2_ID = 0; intSAL1_ID = 0; intMA_ID = 0; intPCC_ID = 0;
        //        intSAL6_ID1 = string.Empty; intSAL5_ID1 = string.Empty; intSAL4_ID1 = string.Empty; intSAL3_ID1 = string.Empty;
        //        intSAL2_ID1 = string.Empty; intSAL1_ID1 = string.Empty; intMA_ID1 = string.Empty; intPCC_ID1 = string.Empty;
        //        jsonData = new
        //        {
        //            HTMLForTree,
        //            CatalogueSortOrder = 1,
        //            TreeLevel = 6

        //            //ModelImageData = GetImageForModel(Model_ID)
        //        };
        //    }
        //    catch (Exception ex)
        //    {
        //        var Request = new System.Net.Http.HttpRequestMessage();
        //        ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObject.UserID), constring);
        //        jsonData = new
        //        {
        //            FirstOccuranceAssembly_ID = 0,
        //            HTMLForTree = "",
        //            CatalogueSortOrder = 1,
        //            TreeLevel = 6
        //        };
        //    }
        //    return new JsonResult(jsonData);
        //}
        //#endregion

        //#region Catalogue Order serach Dash Board -Siddesh.P- 07-Nov-2023 :::
        ///// <summary>
        /////  DK - 07-Nov-2023 To VIN/ORDER LIST Dashboard 
        ///// </summary>
        ///// <param name="as per below"></param>
        ///// <returns>Json</returns>
        //public static IActionResult GetTreeDataNew(string constring, VINBasedTreeLoad VINBasedTreeLoadObj)
        //{
        //    Common Commo = new Common();
        //    var jsonData = default(dynamic);
        //    string Conn = constring;
        //    try
        //    {
        //        if (VINBasedTreeLoadObj.sectionvalue.ToString() != "")
        //        { VINBasedTreeLoadObj.Section = Convert.ToInt32(VINBasedTreeLoadObj.sectionvalue); }

        //        //var AppPathImg = ConfigurationManager.AppSettings.Get("EPCGetImagePath") + "AssemblyServiceLink/";
        //        int ISRange = -1;
        //        string FromSerialNumber = string.Empty;
        //        string ToSerialNumber = string.Empty;
        //        string Model = string.Empty;
        //        var VehicleIC = default(dynamic);
        //        int? intFromSerialNumber = 0;
        //        int? intToSerialNumber = 0;

        //        int Model_ID = Convert.ToInt32(VINBasedTreeLoadObj.Section);
        //        // int Model_ID= Convert.ToInt32(Request.Params["sectionvalue"]); 


        //        if (VINBasedTreeLoadObj.FromInsideCatlougeSearch != null)
        //        {
        //            if (VINBasedTreeLoadObj.FromInsideCatlougeSearch.ToString() != "")
        //            {
        //                if (VINBasedTreeLoadObj.FromInsideCatlougeSearch.ToString() == "true")
        //                {
        //                    Model_ID = Convert.ToInt32(VINBasedTreeLoadObj.TopLevelIDValue);
        //                }
        //            }
        //        }

        //        if (VINBasedTreeLoadObj.TopLevelSectionVIN.ToString() != "")
        //        {
        //            FromSerialNumber = VINBasedTreeLoadObj.TopLevelSectionVINShortCode.ToString();
        //            ToSerialNumber = VINBasedTreeLoadObj.ToSerialNo.ToString();
        //            Model = VINBasedTreeLoadObj.Model.ToString();
        //            VehicleIC = Convert.ToInt32(VINBasedTreeLoadObj.TopLevelSectionVIN.ToString() == "" ? "0" : VINBasedTreeLoadObj.TopLevelSectionVIN.ToString());
        //            VehicleIC = (VehicleIC == 0 ? DBNull.Value : VehicleIC);
        //            Model_ID = VehicleIC;
        //        }
        //        else
        //        {
        //            VINBasedTreeLoadObj.TopLevelSectionVIN = "";
        //            FromSerialNumber = VINBasedTreeLoadObj.FromSerialNumber.ToString();
        //            ToSerialNumber = VINBasedTreeLoadObj.ToSerialNo.ToString();
        //            Model = VINBasedTreeLoadObj.Model.ToString();
        //            //VehicleIC = Convert.ToInt32(Request.Params["Vehicle"].ToString() == "" ? "0" : Request.Params["Vehicle"].ToString());
        //            //VehicleIC = (VehicleIC == 0 ? DBNull.Value : VehicleIC);
        //        }

        //        if (FromSerialNumber != "" && ToSerialNumber != "")
        //        {
        //            intFromSerialNumber = Commo.GetVinShortNumber(FromSerialNumber, VINBasedTreeLoadObj.DCTYPE, VINBasedTreeLoadObj.UserID, constring);
        //            intToSerialNumber = Commo.GetVinShortNumber(ToSerialNumber, VINBasedTreeLoadObj.DCTYPE, VINBasedTreeLoadObj.UserID, constring);
        //            ISRange = 1;
        //        }
        //        else if (FromSerialNumber != "" || ToSerialNumber != "")
        //        {
        //            if (FromSerialNumber != "")
        //            {
        //                intFromSerialNumber = Commo.GetVinShortNumber(FromSerialNumber, VINBasedTreeLoadObj.DCTYPE, VINBasedTreeLoadObj.UserID, constring);
        //            }
        //            else if (ToSerialNumber != "" && FromSerialNumber == "")
        //            {
        //                intFromSerialNumber = Commo.GetVinShortNumber(ToSerialNumber, VINBasedTreeLoadObj.DCTYPE, VINBasedTreeLoadObj.UserID, constring);
        //            }
        //            ISRange = 0;
        //        }
        //        else if (FromSerialNumber == "" && ToSerialNumber == "" && Model != "")
        //        {
        //            intFromSerialNumber = 0;
        //            intToSerialNumber = 0;
        //            ISRange = 0;
        //        }

        //        VINBasedTreeLoadObj.CatalogueSortOrder = "1";
        //        VINBasedTreeLoadObj.TreeLevel = 6;
        //        var LanguageID = VINBasedTreeLoadObj.Langauge_ID;
        //        string CurrentLanguageTre = VINBasedTreeLoadObj.Lang.ToString();
        //        if (CurrentLanguageTre == "en")
        //        {
        //            LanguageID = 0;
        //        }

        //        var CatalogueName = string.Empty;
        //        bool IsRange = Convert.ToBoolean(VINBasedTreeLoadObj.IsRange);
        //        int intRow = 0;
        //        int intPCC_ID = 0; int intPrevPCC_ID = 0;
        //        int intMA_ID = 0; int intPrevMA_ID = 0;
        //        int intSAL1_ID = 0; int intPrevSAL1_ID = 0;
        //        int intSAL2_ID = 0; int intPrevSAL2_ID = 0;
        //        int intSAL3_ID = 0; int intPrevSAL3_ID = 0;
        //        int intSAL4_ID = 0; int intPrevSAL4_ID = 0;
        //        int intSAL5_ID = 0; //int intPrevSAL5_ID = 0;
        //        int intSAL6_ID = 0;

        //        string intPCC_ID1 = string.Empty; string intPrevPCC_ID1 = string.Empty;
        //        string intMA_ID1 = string.Empty; string intPrevMA_ID1 = string.Empty;
        //        string intSAL1_ID1 = string.Empty; string intPrevSAL1_ID1 = string.Empty;
        //        string intSAL2_ID1 = string.Empty; string intPrevSAL2_ID1 = string.Empty;
        //        string intSAL3_ID1 = string.Empty; string intPrevSAL3_ID1 = string.Empty;
        //        string intSAL4_ID1 = string.Empty; string intPrevSAL4_ID1 = string.Empty;
        //        string intSAL5_ID1 = string.Empty; string intSAL6_ID1 = string.Empty;

        //        bool Checknextid = false;
        //        byte OrderType = Convert.ToByte(VINBasedTreeLoadObj.CatalogueSortOrder);
        //        DataSet ds = new DataSet("TreeView");
        //        using (SqlConnection conn = new SqlConnection(Conn))
        //        {
        //            SqlCommand sqlComm = new SqlCommand("Sp_EPCTreeBuild_VIN", conn);
        //            if (Convert.ToBoolean(VINBasedTreeLoadObj.SelectFromTopLevel) == true)
        //            {
        //                sqlComm.Parameters.AddWithValue("@Vehicle_ID", DBNull.Value);
        //                sqlComm.Parameters.AddWithValue("@Assembly_ID", Model_ID);
        //            }
        //            else
        //            {
        //                if (VINBasedTreeLoadObj.TopLevelSectionVIN.ToString() != "")
        //                {
        //                    sqlComm.Parameters.AddWithValue("@Vehicle_ID", Model_ID);
        //                    sqlComm.Parameters.AddWithValue("@Assembly_ID", DBNull.Value);
        //                }
        //                else
        //                {
        //                    if (VINBasedTreeLoadObj.CatalogueTree.ToString() == "true")
        //                    {
        //                        if (VINBasedTreeLoadObj.FromTopLevelSectionVIN.ToString() == "1" && VINBasedTreeLoadObj.CatalogueTree.ToString() == "true")
        //                        {
        //                            sqlComm.Parameters.AddWithValue("@Vehicle_ID", Model_ID);
        //                            sqlComm.Parameters.AddWithValue("@Assembly_ID", DBNull.Value);
        //                        }
        //                        if ((VINBasedTreeLoadObj.FromTopLevelSectionVIN.ToString() == "" || VINBasedTreeLoadObj.FromTopLevelSectionVIN == null || VINBasedTreeLoadObj.FromTopLevelSectionVIN == "undefined") && VINBasedTreeLoadObj.CatalogueTree.ToString() == "true")
        //                        {
        //                            sqlComm.Parameters.AddWithValue("@Vehicle_ID", DBNull.Value);
        //                            sqlComm.Parameters.AddWithValue("@Assembly_ID", Model_ID);
        //                        }
        //                    }

        //                    if (VINBasedTreeLoadObj.TopLevelSectionVIN.ToString() == "" && VINBasedTreeLoadObj.CatalogueTree.ToString() == "false")
        //                    {
        //                        sqlComm.Parameters.AddWithValue("@Vehicle_ID", DBNull.Value);
        //                        sqlComm.Parameters.AddWithValue("@Assembly_ID", Model_ID);
        //                    }


        //                }
        //            }

        //            if (VINBasedTreeLoadObj.DCTYPE.ToString() == "2")
        //            {
        //                if (ISRange == 0 || ISRange == 1 && ISRange != -1)
        //                {
        //                    sqlComm.Parameters.AddWithValue("@IsRange", ISRange);
        //                }
        //                else
        //                {
        //                    sqlComm.Parameters.AddWithValue("@IsRange", DBNull.Value);
        //                }

        //                if (intFromSerialNumber == 0)
        //                {
        //                    sqlComm.Parameters.AddWithValue("@FromSN", DBNull.Value);
        //                }
        //                else
        //                {
        //                    sqlComm.Parameters.AddWithValue("@FromSN", intFromSerialNumber);
        //                }

        //                if (intToSerialNumber == 0)
        //                {
        //                    sqlComm.Parameters.AddWithValue("@ToSN", DBNull.Value);
        //                }
        //                else
        //                {
        //                    sqlComm.Parameters.AddWithValue("@ToSN", intToSerialNumber);
        //                }

        //                if (Model == "")
        //                {
        //                    sqlComm.Parameters.AddWithValue("@Model_IC", DBNull.Value);
        //                }
        //                else
        //                {
        //                    sqlComm.Parameters.AddWithValue("@Model_IC", Model);
        //                }
        //            }
        //            sqlComm.Parameters.AddWithValue("@Language_ID", LanguageID); //--- French Tree Check for Prevost
        //            sqlComm.CommandType = CommandType.StoredProcedure;
        //            SqlDataAdapter da = new SqlDataAdapter();
        //            sqlComm.CommandTimeout = 100;
        //            da.SelectCommand = sqlComm;
        //            da.Fill(ds);
        //        }
        //        //string HTMLForTree = "&nbsp;&nbsp;<span id=\"IDBtnExpandTree\" style=\"color: blue; text-decoration: underline; font-size: 12px; cursor: pointer\"> " + HttpContext.GetGlobalResourceObject(Session["Culture"].ToString(), "ResExpandAll").ToString() + " </span>&nbsp;&nbsp;&nbsp;<span id=\"IDBtnCollapseTree\" style=\"color: blue; text-decoration: underline; font-size: 12px; cursor: pointer\"> " + HttpContext.GetGlobalResourceObject(Session["Culture"].ToString(), "ResCollapseAll").ToString() + " </span>&nbsp;&nbsp;";
        //        string HTMLForTree = "<ul>";
        //        //Start Loop Here
        //        foreach (DataRow dr in ds.Tables[0].Rows)
        //        {
        //            //Catalogue Level
        //            if (intRow == 0)
        //            {
        //                //HTMLForTree = HTMLForTree + "<li IsModel=\"1\" id=\"" + dr["Part_Assembly_ID"].ToString() + "\" class=\"folder expanded\"><b IsModel=\"1\" id=\"" + dr["Part_Assembly_ID"].ToString() + "\" style=\"font-weight: 100\">" + dr["Part_Assembly_Description"].ToString() + " </b>";
        //                HTMLForTree = HTMLForTree + "<li IsModel=\"1\" id=\"" + dr["Part_Assembly_ID"].ToString() + "\" class=\"folder clsExtension expanded\"><b IsChapter=\"1\" id=\"" + dr["Part_Assembly_ID"].ToString() + "\" style=\"font-weight: 100\">" + HttpUtility.HtmlEncode(dr["Part_Assembly_Description"].ToString()) + " </b>";
        //                if (dr["Sec_IC"].ToString() != "")
        //                {
        //                    HTMLForTree = HTMLForTree + "<ul>";
        //                }
        //            }

        //            //Top Section Level
        //            if (Int32.Parse(VINBasedTreeLoadObj.TreeLevel.ToString()) >= 1 && intPCC_ID != Convert.ToInt32(dr["Sec_IC"].ToString()) || intPCC_ID1 != dr["Sec_Desc"].ToString())
        //            {
        //                if (intSAL6_ID != 0 || intSAL6_ID1 != string.Empty)
        //                {
        //                    HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul></ul>";
        //                }
        //                else if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //                {
        //                    HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul>";
        //                }
        //                else if (intSAL4_ID != 0 || intSAL4_ID1 != string.Empty)
        //                {
        //                    HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul>";
        //                }
        //                else if (intSAL3_ID != 0 || intSAL3_ID1 != string.Empty)
        //                {
        //                    HTMLForTree = HTMLForTree + "</ul></ul></ul></ul>";
        //                }
        //                else if (intSAL2_ID != 0 || intSAL2_ID1 != string.Empty)
        //                {
        //                    HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                }
        //                else if (intSAL1_ID != 0 || intSAL1_ID1 != string.Empty)
        //                {
        //                    HTMLForTree = HTMLForTree + "</ul></ul>";
        //                }
        //                else if (intRow != 0 && Checknextid == false)
        //                {
        //                    HTMLForTree = HTMLForTree + "</ul>";
        //                }
        //                intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0; intSAL3_ID = 0; intSAL2_ID = 0; intSAL1_ID = 0; intMA_ID = 0;
        //                intSAL6_ID1 = string.Empty; intSAL5_ID1 = string.Empty; intSAL4_ID1 = string.Empty;
        //                intSAL3_ID1 = string.Empty; intSAL2_ID1 = string.Empty; intSAL1_ID1 = string.Empty; intMA_ID1 = string.Empty;

        //                intPrevPCC_ID = intPCC_ID;
        //                intPrevPCC_ID1 = intPCC_ID1;


        //                if (dr["Sec_IC"].ToString() != "342392")
        //                {
        //                    HTMLForTree = HTMLForTree + "<li id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"1\" data-view=" + HttpUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " class=\"folder clsExtension\"><b id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"1\" data-view=" + HttpUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " style=\"font-weight: 100;\">" + HttpUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " </b>";
        //                }
        //                else if (dr["Sec_IC"].ToString() == "342392")
        //                {
        //                    if (VINBasedTreeLoadObj.DCTYPE.ToString().ToUpper() == "2")
        //                    {
        //                        if (dr["Sec_No"].ToString().Contains("http://") || dr["Sec_No"].ToString().Contains("https://"))
        //                        {
        //                            HTMLForTree = HTMLForTree + "<li id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"1\" data-view=" + HttpUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"1\" data-view=" + HttpUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + HttpUtility.HtmlEncode(dr["Sec_No"].ToString()) + "'>" + HttpUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + "</a></b>";
        //                        }
        //                        else
        //                        {
        //                            string AWSPrefix = "Images/AssemblyServiceLink";
        //                            HTMLForTree = HTMLForTree + "<li id ='\"" + dr["Sec_IC"].ToString() + "\"' IsChapter='\"-1\"' data-view='" + HttpUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + "' class='\"clsExtension\"'><b class='far fa-dot-circle' style='color:red'></b> <b id='" + dr["Sec_IC"].ToString() + "' IsChapter='\"-1\"' data-view=" + HttpUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " style='\"font-weight: 100;\"'><a target='_blank' style='color:red;text-decoration:underline;' href='" + (Commo.GetAWSObjectURL(HttpUtility.HtmlEncode(dr["Sec_No"].ToString()), AWSPrefix)) + "'>" + HttpUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + "</a></b>";
        //                        }
        //                    }
        //                    else
        //                    {
        //                        string CurrentLanguage = VINBasedTreeLoadObj.Lang.ToString();

        //                        string PathServS = dr["ServiceSectionLink"].ToString();
        //                        string ReplaceLang = "";
        //                        //if (CurrentLanguage == "en")
        //                        //{
        //                        //    ReplaceLang = PathServS.Replace("ENGLISH", "EN");
        //                        //}
        //                        //else if (CurrentLanguage == "Fr")
        //                        //{
        //                        //    ReplaceLang = PathServS.Replace("FRENCH", "FR");
        //                        //}
        //                        //string FullServiceSectionPath = ServiceSectionFilePath + "\\" + ReplaceLang;
        //                        string FilenameForAWS = "";
        //                        string Prefixpath = "";
        //                        if (CurrentLanguage == "en")
        //                        {
        //                            ReplaceLang = PathServS.Replace("ENGLISH", "EN");
        //                            ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                            string[] pathComponents = ReplaceLang.Split('/');
        //                            Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                            FilenameForAWS = pathComponents[2];
        //                        }
        //                        else if (CurrentLanguage == "Fr")
        //                        {
        //                            ReplaceLang = PathServS.Replace("FRENCH", "FR");
        //                            ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                            string[] pathComponents = ReplaceLang.Split('/');
        //                            Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                            FilenameForAWS = pathComponents[2];
        //                        }

        //                        string AWSPrefix = "Service_Section_And_SubSection_Manuals/MANUE/ENTR/ENTR-LFS/" + Prefixpath;
        //                        string FullServiceSectionPath = Commo.GetAWSObjectURL(FilenameForAWS.Trim(), AWSPrefix);
        //                        //string CurrentLanguage = VINBasedTreeLoadObj.Lang.ToString();
        //                        //string GetServiceSectionServerPath = ConfigurationManager.AppSettings.Get("EPCGetServerPath").ToString();
        //                        //string GetServiceSectionPubLishPath = ConfigurationManager.AppSettings.Get("EPCGetPublicationPath").ToString();
        //                        //string ConServiceSectionServerPubLishPath = GetServiceSectionServerPath + GetServiceSectionPubLishPath;
        //                        //string ServiceSectionFilePath = ConServiceSectionServerPubLishPath;
        //                        //string PathServS = dr["ServiceSectionLink"].ToString();
        //                        //string ReplaceLang = "";
        //                        //if (CurrentLanguage == "en")
        //                        //{
        //                        //    ReplaceLang = PathServS.Replace("ENGLISH", "EN");
        //                        //}
        //                        //else if (CurrentLanguage == "Fr")
        //                        //{
        //                        //    ReplaceLang = PathServS.Replace("ENGLISH", "FR");
        //                        //}
        //                        //string FullServiceSectionPath = ServiceSectionFilePath + "\\" + ReplaceLang;

        //                        HTMLForTree = HTMLForTree + "<li id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + dr["Sec_Desc"].ToString() + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + dr["Sec_Desc"].ToString() + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + FullServiceSectionPath + "'>" + HttpUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + "</a></b>";
        //                    }
        //                }

        //                //HTMLForTree = HTMLForTree + "<li id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"1\" data-view=" + dr["Sec_Desc"].ToString() + " class=\"folder clsExtension\"><b id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"1\" data-view=" + dr["Sec_Desc"].ToString() + " style=\"font-weight: 100;\">" + dr["Sec_Desc"].ToString() + " </b>";
        //                intPCC_ID = Convert.ToInt32(dr["Sec_IC"].ToString());
        //                intPCC_ID1 = dr["Sec_Desc"].ToString();
        //            }

        //            var jdf = dr["SSec_IC"].ToString();
        //            //Sub Section / Main Assembly Level
        //            if (jdf != "")
        //            {
        //                Checknextid = false;
        //                if (Int32.Parse(VINBasedTreeLoadObj.TreeLevel.ToString()) >= 2 && intMA_ID != Convert.ToInt32(dr["SSec_IC"]) || intMA_ID1 != dr["SSec_Desc"].ToString())
        //                //if (Int32.Parse(GetTDataPartAssemblyLevelObj.TreeLevel.ToString()) >= 2 && intMA_ID != Convert.ToInt32(dr["SSec_IC"].ToString())) //intMA_ID !=
        //                {
        //                    if (intSAL6_ID != 0 || intSAL6_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul>";
        //                    }
        //                    else if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul>";
        //                    }
        //                    else if (intSAL4_ID != 0 || intSAL4_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul></ul></ul>";
        //                    }
        //                    else if (intSAL3_ID != 0 || intSAL3_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                    }
        //                    else if (intSAL2_ID != 0 || intSAL2_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul>";
        //                    }
        //                    else if (intSAL1_ID != 0 || intSAL1_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul>";
        //                    }

        //                    if (intPrevPCC_ID != intPCC_ID || intPrevPCC_ID1 != intPCC_ID1)
        //                    {
        //                        intPrevPCC_ID = intPCC_ID;
        //                        intPrevPCC_ID1 = intPCC_ID1;
        //                        HTMLForTree = HTMLForTree + "<ul>";
        //                    }
        //                    intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0; intSAL3_ID = 0; intSAL2_ID = 0; intSAL1_ID = 0;
        //                    intSAL6_ID1 = string.Empty; intSAL5_ID1 = string.Empty; intSAL4_ID1 = string.Empty; intSAL3_ID1 = string.Empty;
        //                    intSAL2_ID1 = string.Empty; intSAL1_ID1 = string.Empty;

        //                    intPrevMA_ID = intMA_ID;
        //                    intPrevMA_ID1 = intMA_ID1;


        //                    if (dr["SSec_IC"].ToString() != "342392")
        //                    {
        //                        HTMLForTree = HTMLForTree + "<li id=\"li" + dr["SSec_IC"].ToString() + "\" IsChapter='0' data-view=" + HttpUtility.HtmlEncode(dr["SSec_No"].ToString()) + " class=\"folder clsExtension\"><b id=\"" + dr["SSec_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + HttpUtility.HtmlEncode(dr["SSec_No"].ToString()) + " style=\"font-weight: 100;\">" + HttpUtility.HtmlEncode(dr["SSec_Desc"].ToString()) + " </b>";
        //                    }
        //                    else if (dr["SSec_IC"].ToString() == "342392")
        //                    {
        //                        if (VINBasedTreeLoadObj.DCTYPE.ToString().ToUpper() == "2")
        //                        {
        //                            if (dr["SSec_No"].ToString().Contains("http://") || dr["SSec_No"].ToString().Contains("https://"))
        //                            {
        //                                HTMLForTree = HTMLForTree + "<li id=\"li" + dr["SSec_IC"].ToString() + "\" IsChapter='0' data-view=" + HttpUtility.HtmlEncode(dr["SSec_No"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["SSec_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + HttpUtility.HtmlEncode(dr["SSec_No"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + HttpUtility.HtmlEncode(dr["SSec_No"].ToString()) + "'>" + HttpUtility.HtmlEncode(dr["SSec_Desc"].ToString()) + "</a></b>";
        //                            }
        //                            else
        //                            {
        //                                string AWSPrefix = "Images/AssemblyServiceLink";
        //                                HTMLForTree = HTMLForTree + "<li id='\"li" + dr["SSec_IC"].ToString() + "\"' IsChapter='0' data-view='" + HttpUtility.HtmlEncode(dr["SSec_No"].ToString()) + "' class='\"clsExtension\"'><b class='far fa-dot-circle' style='color:red'></b> <b id='\"" + dr["SSec_IC"].ToString() + "\"' IsChapter='\"0\"' data-view='" + HttpUtility.HtmlEncode(dr["SSec_No"].ToString()) + "' style='\"font-weight: 100;\"'><a target='_blank' style='color:red;text-decoration:underline;' href='" + Commo.GetAWSObjectURL(HttpUtility.HtmlEncode(dr["SSec_No"].ToString()), AWSPrefix) + "'>" + HttpUtility.HtmlEncode(dr["SSec_Desc"].ToString()) + "</a></b>";
        //                            }
        //                        }
        //                        else
        //                        {
        //                            string CurrentLanguage = VINBasedTreeLoadObj.Lang.ToString();

        //                            string PathServS = dr["ServiceSectionLink"].ToString();
        //                            string ReplaceLang = "";
        //                            //if (CurrentLanguage == "en")
        //                            //{
        //                            //    ReplaceLang = PathServS.Replace("ENGLISH", "EN");
        //                            //}
        //                            //else if (CurrentLanguage == "Fr")
        //                            //{
        //                            //    ReplaceLang = PathServS.Replace("FRENCH", "FR");
        //                            //}
        //                            //string FullServiceSectionPath = ServiceSectionFilePath + "\\" + ReplaceLang;
        //                            string FilenameForAWS = "";
        //                            string Prefixpath = "";
        //                            if (CurrentLanguage == "en")
        //                            {
        //                                ReplaceLang = PathServS.Replace("ENGLISH", "EN");
        //                                ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                string[] pathComponents = ReplaceLang.Split('/');
        //                                Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                FilenameForAWS = pathComponents[2];
        //                            }
        //                            else if (CurrentLanguage == "Fr")
        //                            {
        //                                ReplaceLang = PathServS.Replace("FRENCH", "FR");
        //                                ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                string[] pathComponents = ReplaceLang.Split('/');
        //                                Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                FilenameForAWS = pathComponents[2];
        //                            }

        //                            string AWSPrefix = "Service_Section_And_SubSection_Manuals/MANUE/ENTR/ENTR-LFS/" + Prefixpath;
        //                            string FullServiceSectionPath = Commo.GetAWSObjectURL(FilenameForAWS.Trim(), AWSPrefix);
        //                            //string CurrentLanguage = VINBasedTreeLoadObj.Lang.ToString();
        //                            //string GetServiceSectionServerPath = ConfigurationManager.AppSettings.Get("EPCGetServerPath").ToString();
        //                            //string GetServiceSectionPubLishPath = ConfigurationManager.AppSettings.Get("EPCGetPublicationPath").ToString();
        //                            //string ConServiceSectionServerPubLishPath = GetServiceSectionServerPath + GetServiceSectionPubLishPath;
        //                            //string ServiceSectionFilePath = ConServiceSectionServerPubLishPath;
        //                            //string PathServS = dr["ServiceSectionLink"].ToString();
        //                            //string ReplaceLang = "";
        //                            //if (CurrentLanguage == "en")
        //                            //{
        //                            //    ReplaceLang = PathServS.Replace("ENGLISH", "EN");
        //                            //}
        //                            //else if (CurrentLanguage == "Fr")
        //                            //{
        //                            //    ReplaceLang = PathServS.Replace("ENGLISH", "FR");
        //                            //}
        //                            //string FullServiceSectionPath = ServiceSectionFilePath + "\\" + ReplaceLang;
        //                            HTMLForTree = HTMLForTree + "<li id=\"li" + dr["SSec_IC"].ToString() + "\" IsChapter='0' data-view=" + HttpUtility.HtmlEncode(dr["SSec_No"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["SSec_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + HttpUtility.HtmlEncode(dr["SSec_No"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + FullServiceSectionPath + "'>" + HttpUtility.HtmlEncode(dr["SSec_Desc"].ToString()) + "</a></b>";
        //                        }
        //                    }

        //                    //HTMLForTree = HTMLForTree + "<li id=\"li" + dr["SSec_IC"].ToString() + "\" IsChapter='0' data-view=" + dr["SSec_No"].ToString() + " class=\"folder clsExtension\"><b id=\"" + dr["SSec_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + dr["SSec_No"].ToString() + " style=\"font-weight: 100;\">" + dr["SSec_Desc"].ToString() + " </b>";
        //                    intMA_ID = Convert.ToInt32(dr["SSec_IC"].ToString());
        //                    intMA_ID1 = dr["SSec_Desc"].ToString();
        //                }
        //                else
        //                {

        //                }
        //            }
        //            else
        //            {
        //                Checknextid = true;
        //                HTMLForTree = HTMLForTree + "</li>";
        //            }

        //            //Sub Assembly Level 1
        //            if (Int32.Parse(VINBasedTreeLoadObj.TreeLevel.ToString()) >= 3 && dr["L1_IC"].ToString() != "")
        //            {
        //                if (intSAL1_ID != Convert.ToInt32(dr["L1_IC"].ToString()) || intSAL1_ID1 != dr["L1_Desc"].ToString())
        //                {
        //                    if (intSAL6_ID != 0 || intSAL6_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul>";
        //                    }
        //                    else if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul></ul></ul>";
        //                    }
        //                    else if (intSAL4_ID != 0 || intSAL4_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                    }
        //                    else if (intSAL3_ID != 0 || intSAL3_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul>";
        //                    }
        //                    else if (intSAL2_ID != 0 || intSAL2_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul>";
        //                    }

        //                    if (intPrevMA_ID != intMA_ID || intPrevMA_ID1 != intMA_ID1)
        //                    {
        //                        intPrevMA_ID = intMA_ID;
        //                        intPrevMA_ID1 = intMA_ID1;
        //                        HTMLForTree = HTMLForTree + "<ul>";
        //                    }
        //                    intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0; intSAL3_ID = 0; intSAL2_ID = 0;
        //                    intSAL6_ID1 = string.Empty; intSAL5_ID1 = string.Empty; intSAL4_ID1 = string.Empty;
        //                    intSAL3_ID1 = string.Empty; intSAL2_ID1 = string.Empty;

        //                    intPrevSAL1_ID = intSAL1_ID;
        //                    intPrevSAL1_ID1 = intSAL1_ID1;
        //                    if (dr["L1_IC"].ToString() != "342392")
        //                    {
        //                        HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L1_IC"].ToString() + "\" IsChapter='0' data-view=" + HttpUtility.HtmlEncode(dr["L1_No"].ToString()) + " class=\"clsExtension\"><b id=\"" + dr["L1_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + HttpUtility.HtmlEncode(dr["L1_No"].ToString()) + " style=\"font-weight: 100;\">" + HttpUtility.HtmlEncode(dr["L1_Desc"].ToString()) + "</b>";
        //                    }
        //                    else if (dr["L1_IC"].ToString() == "342392")
        //                    {
        //                        if (VINBasedTreeLoadObj.DCTYPE.ToString() == "2")
        //                        {
        //                            if (dr["L1_No"].ToString().Contains("http://") || dr["L1_No"].ToString().Contains("https://"))
        //                            {
        //                                HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L1_IC"].ToString() + "\" IsChapter='0' data-view=" + HttpUtility.HtmlEncode(dr["L1_No"].ToString()) + " class=\"clsExtension\"> <b class='far fa-dot-circle' style='color:red'></b> <b  id=\"" + dr["L1_IC"].ToString() + "\" IsChapter=\"0\"  data-view=" + HttpUtility.HtmlEncode(dr["L1_No"].ToString()) + " style=\"font-weight: 100;color:blue\" >" + "<a target='_blank' style='color:red;text-decoration:underline;' href='" + HttpUtility.HtmlEncode(dr["L1_No"].ToString()) + "'>" + HttpUtility.HtmlEncode(dr["L1_Desc"].ToString()) + "</a>" + "</b>";
        //                            }
        //                            else
        //                            {
        //                                string AWSPrefix = "Images/AssemblyServiceLink";
        //                                HTMLForTree = HTMLForTree + "<li id='\"li" + dr["L1_IC"].ToString() + "\"' IsChapter='0' data-view='" + HttpUtility.HtmlEncode(dr["L1_No"].ToString()) + "' class='\"clsExtension\"'> <b class='far fa-dot-circle' style='color:red'></b> <b  id='\"" + dr["L1_IC"].ToString() + "\"' IsChapter='0'  data-view='" + HttpUtility.HtmlEncode(dr["L1_No"].ToString()) + "' style='\"font-weight: 100;color:blue\"' >" + "<a target='_blank' style='color:red;text-decoration:underline;' href='" + Commo.GetAWSObjectURL(HttpUtility.HtmlEncode(dr["L1_No"].ToString()), AWSPrefix) + "'>" + HttpUtility.HtmlEncode(dr["L1_Desc"].ToString()) + "</a>" + "</b>";
        //                            }
        //                        }
        //                        else
        //                        {
        //                            string CurrentLanguage = VINBasedTreeLoadObj.Lang.ToString();
        //                            string PathServS = dr["ServiceSectionLink"].ToString();
        //                            string ReplaceLang = "";
        //                            string FilenameForAWS = "";
        //                            string Prefixpath = "";
        //                            if (CurrentLanguage == "en")
        //                            {
        //                                ReplaceLang = PathServS.Replace("ENGLISH", "EN");
        //                                ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                string[] pathComponents = ReplaceLang.Split('/');
        //                                Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                FilenameForAWS = pathComponents[2];
        //                            }
        //                            else if (CurrentLanguage == "Fr")
        //                            {
        //                                ReplaceLang = PathServS.Replace("FRENCH", "FR");
        //                                ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                string[] pathComponents = ReplaceLang.Split('/');
        //                                Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                FilenameForAWS = pathComponents[2];
        //                            }

        //                            string AWSPrefix = "Service_Section_And_SubSection_Manuals/MANUE/ENTR/ENTR-LFS/" + Prefixpath;
        //                            string FullServiceSectionPath = Commo.GetAWSObjectURL(FilenameForAWS.Trim(), AWSPrefix);

        //                            HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L1_IC"].ToString() + "\" IsChapter='0' data-view=" + HttpUtility.HtmlEncode(dr["L1_No"].ToString()) + " class=\"clsExtension\"> <b class='far fa-dot-circle' style='color:red'></b> <b  id=\"" + dr["L1_IC"].ToString() + "\" IsChapter=\"0\"  data-view=" + HttpUtility.HtmlEncode(dr["L1_No"].ToString()) + " style=\"font-weight: 100;color:blue\" >" + "<a target='_blank' style='color:red;text-decoration:underline;' href='" + FullServiceSectionPath + "'>" + HttpUtility.HtmlEncode(dr["L1_Desc"].ToString()) + "</a>" + "</b>";
        //                        }
        //                    }
        //                    intSAL1_ID = Convert.ToInt32(dr["L1_IC"].ToString());
        //                    intSAL1_ID1 = dr["L1_Desc"].ToString();
        //                }
        //            }

        //            else
        //            {
        //                intSAL1_ID = 0;
        //                intSAL1_ID1 = string.Empty;
        //            }
        //            //Sub Assembly Level 2
        //            if (Int32.Parse(VINBasedTreeLoadObj.TreeLevel.ToString()) >= 4 && dr["L2_IC"].ToString() != "")
        //            {
        //                if (intSAL2_ID != Convert.ToInt32(dr["L2_IC"].ToString()) || intSAL2_ID1 != dr["L2_Desc"].ToString())
        //                {
        //                    if (intSAL6_ID != 0 || intSAL6_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul></ul></ul>";
        //                    }
        //                    else if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                    }
        //                    else if (intSAL4_ID != 0 || intSAL4_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul>";
        //                    }
        //                    else if (intSAL3_ID != 0 || intSAL3_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul>";
        //                    }

        //                    if (intPrevSAL1_ID != intSAL1_ID || intPrevSAL1_ID1 != intSAL1_ID1)
        //                    {
        //                        intPrevSAL1_ID = intSAL1_ID;
        //                        intPrevSAL1_ID1 = intSAL1_ID1;
        //                        HTMLForTree = HTMLForTree + "<ul>";
        //                    }
        //                    intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0; intSAL3_ID = 0;
        //                    intSAL6_ID1 = string.Empty; intSAL5_ID1 = string.Empty; intSAL4_ID1 = string.Empty; intSAL3_ID1 = string.Empty;
        //                    intPrevSAL2_ID = intSAL2_ID;
        //                    intPrevSAL2_ID1 = intSAL2_ID1;


        //                    if (dr["L2_IC"].ToString() != "342392")
        //                    {
        //                        HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L2_IC"].ToString() + "\" IsChapter='0' data-view=" + HttpUtility.HtmlEncode(dr["L2_No"].ToString()) + " class=\"clsExtension\"><b id=\"" + dr["L2_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + HttpUtility.HtmlEncode(dr["L2_No"].ToString()) + " style=\"font-weight: 100;\">" + HttpUtility.HtmlEncode(dr["L2_Desc"].ToString()) + "</b>";
        //                    }
        //                    else if (dr["L2_IC"].ToString() == "342392")
        //                    {
        //                        if (VINBasedTreeLoadObj.DCTYPE.ToString().ToUpper() == "2")
        //                        {
        //                            if (dr["L2_No"].ToString().Contains("http://") || dr["L2_No"].ToString().Contains("https://"))
        //                            {
        //                                HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L2_IC"].ToString() + "\" IsChapter='0' data-view=" + HttpUtility.HtmlEncode(dr["L2_No"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["L2_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + HttpUtility.HtmlEncode(dr["L2_No"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + HttpUtility.HtmlEncode(dr["L2_No"].ToString()) + "'>" + HttpUtility.HtmlEncode(dr["L2_Desc"].ToString()) + "</a></b>";
        //                            }
        //                            else
        //                            {
        //                                string AWSPrefix = "Images/AssemblyServiceLink";
        //                                HTMLForTree = HTMLForTree + "<li id='\"li" + dr["L2_IC"].ToString() + "\"' IsChapter='0' data-view='" + dr["L2_No"].ToString() + "' class='\"clsExtension\"'><b class='far fa-dot-circle' style='color:red'></b> <b id='\"" + dr["L2_IC"].ToString() + "\"' IsChapter='0' data-view='" + HttpUtility.HtmlEncode(dr["L2_No"].ToString()) + "' style='font-weight: 100;'><a target='_blank' style='color:red;text-decoration:underline;' href='" + Commo.GetAWSObjectURL(HttpUtility.HtmlEncode(dr["L2_No"].ToString()), AWSPrefix) + "'>" + HttpUtility.HtmlEncode(dr["L2_Desc"].ToString()) + "</a></b>";
        //                            }
        //                        }
        //                        else
        //                        {
        //                            string CurrentLanguage = VINBasedTreeLoadObj.Lang.ToString();
        //                            string PathServS = dr["ServiceSectionLink"].ToString();
        //                            string ReplaceLang = "";
        //                            string FilenameForAWS = "";
        //                            string Prefixpath = "";
        //                            if (CurrentLanguage == "en")
        //                            {
        //                                ReplaceLang = PathServS.Replace("ENGLISH", "EN");
        //                                ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                string[] pathComponents = ReplaceLang.Split('/');
        //                                Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                FilenameForAWS = pathComponents[2];
        //                            }
        //                            else if (CurrentLanguage == "Fr")
        //                            {
        //                                ReplaceLang = PathServS.Replace("FRENCH", "FR");
        //                                ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                string[] pathComponents = ReplaceLang.Split('/');
        //                                Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                FilenameForAWS = pathComponents[2];
        //                            }

        //                            string AWSPrefix = "Service_Section_And_SubSection_Manuals/MANUE/ENTR/ENTR-LFS/" + Prefixpath;
        //                            string FullServiceSectionPath = Commo.GetAWSObjectURL(FilenameForAWS.Trim(), AWSPrefix);
        //                            HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L2_IC"].ToString() + "\" IsChapter='0' data-view=" + HttpUtility.HtmlEncode(dr["L2_No"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["L2_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + HttpUtility.HtmlEncode(dr["L2_No"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + FullServiceSectionPath + "'>" + HttpUtility.HtmlEncode(dr["L2_Desc"].ToString()) + "</a></b>";
        //                        }
        //                    }

        //                    //HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L2_IC"].ToString() + "\" IsChapter='0' data-view=" + dr["L2_No"].ToString() + " class=\"clsExtension\"><b id=\"" + dr["L2_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + dr["L2_No"].ToString() + " style=\"font-weight: 100;\">" + dr["L2_Desc"].ToString() + "</b>";
        //                    intSAL2_ID = Convert.ToInt32(dr["L2_IC"].ToString());
        //                    intSAL2_ID1 = dr["L2_Desc"].ToString();
        //                }
        //            }
        //            else
        //            {
        //                intSAL2_ID = 0;
        //                intSAL2_ID1 = string.Empty;
        //            }
        //            //Sub Assembly Level 3
        //            if (Int32.Parse(VINBasedTreeLoadObj.TreeLevel.ToString()) >= 5 && dr["L3_IC"].ToString() != "")
        //            {
        //                if (intSAL3_ID != Convert.ToInt32(dr["L3_IC"].ToString()) || intSAL3_ID1 != dr["L3_Desc"].ToString())
        //                {
        //                    if (intSAL6_ID != 0 || intSAL6_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                    }
        //                    else if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul>";
        //                    }
        //                    else if (intSAL4_ID != 0 || intSAL4_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul>";
        //                    }

        //                    if (intPrevSAL2_ID != intSAL2_ID || intPrevSAL2_ID1 != intSAL2_ID1)
        //                    {
        //                        intPrevSAL2_ID = intSAL2_ID;
        //                        intPrevSAL2_ID1 = intSAL2_ID1;
        //                        HTMLForTree = HTMLForTree + "<ul>";
        //                    }
        //                    intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0;
        //                    intSAL6_ID1 = string.Empty; intSAL5_ID1 = string.Empty; intSAL4_ID1 = string.Empty;
        //                    intPrevSAL3_ID = intSAL3_ID;
        //                    intPrevSAL3_ID1 = intSAL3_ID1;


        //                    if (dr["L3_IC"].ToString() != "342392")
        //                    {
        //                        HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L3_IC"].ToString() + "\" IsChapter='0' data-view=" + HttpUtility.HtmlEncode(dr["L3_No"].ToString()) + " class=\"clsExtension\"><b id=\"" + dr["L3_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + HttpUtility.HtmlEncode(dr["L3_No"].ToString()) + " style=\"font-weight: 100;\">" + HttpUtility.HtmlEncode(dr["L3_Desc"].ToString()) + "</b>";
        //                    }
        //                    else if (dr["L3_IC"].ToString() == "342392")
        //                    {
        //                        if (VINBasedTreeLoadObj.DCTYPE.ToString() == "2")
        //                        {
        //                            if (dr["L3_No"].ToString().Contains("http://") || dr["L3_No"].ToString().Contains("https://"))
        //                            {
        //                                HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L3_IC"].ToString() + "\" IsChapter='0' data-view=" + HttpUtility.HtmlEncode(dr["L3_No"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["L3_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + HttpUtility.HtmlEncode(dr["L3_No"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + HttpUtility.HtmlEncode(dr["L2_No"].ToString()) + "'>" + HttpUtility.HtmlEncode(dr["L3_Desc"].ToString()) + "</a></b>";
        //                            }
        //                            else
        //                            {
        //                                string AWSPrefix = "Images/AssemblyServiceLink";
        //                                HTMLForTree = HTMLForTree + "<li id='\"li" + dr["L3_IC"].ToString() + "\"' IsChapter='0' data-view='" + HttpUtility.HtmlEncode(dr["L3_No"].ToString()) + "' class='\"clsExtension\"'><b class='far fa-dot-circle' style='color:red'></b> <b id='\"" + dr["L3_IC"].ToString() + "\"' IsChapter='0' data-view='" + HttpUtility.HtmlEncode(dr["L3_No"].ToString()) + "' style='font-weight: 100;'><a target='_blank' style='color:red;text-decoration:underline;' href='" + Commo.GetAWSObjectURL(HttpUtility.HtmlEncode(dr["L3_No"].ToString()), AWSPrefix) + "'>" + HttpUtility.HtmlEncode(dr["L3_Desc"].ToString()) + "</a></b>";
        //                            }
        //                        }
        //                        else
        //                        {
        //                            string CurrentLanguage = VINBasedTreeLoadObj.Lang.ToString();
        //                            string PathServS = dr["ServiceSectionLink"].ToString();
        //                            string ReplaceLang = "";
        //                            string FilenameForAWS = "";
        //                            string Prefixpath = "";
        //                            if (CurrentLanguage == "en")
        //                            {
        //                                ReplaceLang = PathServS.Replace("ENGLISH", "EN");
        //                                ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                string[] pathComponents = ReplaceLang.Split('/');
        //                                Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                FilenameForAWS = pathComponents[2];
        //                            }
        //                            else if (CurrentLanguage == "Fr")
        //                            {
        //                                ReplaceLang = PathServS.Replace("FRENCH", "FR");
        //                                ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                string[] pathComponents = ReplaceLang.Split('/');
        //                                Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                FilenameForAWS = pathComponents[2];
        //                            }

        //                            string AWSPrefix = "Service_Section_And_SubSection_Manuals/MANUE/ENTR/ENTR-LFS/" + Prefixpath;
        //                            string FullServiceSectionPath = Commo.GetAWSObjectURL(FilenameForAWS.Trim(), AWSPrefix);
        //                            HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L3_IC"].ToString() + "\" IsChapter='0' data-view=" + HttpUtility.HtmlEncode(dr["L3_No"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["L3_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + HttpUtility.HtmlEncode(dr["L3_No"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + FullServiceSectionPath + "'>" + HttpUtility.HtmlEncode(dr["L3_Desc"].ToString()) + "</a></b>";
        //                            //HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L2_IC"].ToString() + "\" IsChapter='0' data-view=" + dr["L2_No"].ToString() + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["L2_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + dr["L2_No"].ToString() + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + FullServiceSectionPath + "'>" + dr["L2_Desc"].ToString() + "</a></b>";
        //                        }
        //                    }

        //                    //HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L3_IC"].ToString() + "\" IsChapter='0' data-view=" + dr["L3_No"].ToString() + " class=\"clsExtension\"><b id=\"" + dr["L3_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + dr["L3_No"].ToString() + " style=\"font-weight: 100;\">" + dr["L3_Desc"].ToString() + "</b>";
        //                    intSAL3_ID = Convert.ToInt32(dr["L3_IC"].ToString());
        //                    intSAL3_ID1 = dr["L3_Desc"].ToString();
        //                }
        //            }
        //            else
        //            {
        //                intSAL3_ID = 0;
        //                intSAL3_ID1 = string.Empty;
        //            }
        //            //Sub Assembly Level 4
        //            if (Int32.Parse(VINBasedTreeLoadObj.TreeLevel.ToString()) >= 6 && dr["L4_IC"].ToString() != "")
        //            {
        //                if (intSAL4_ID != Convert.ToInt32(dr["L4_IC"].ToString()) || intSAL4_ID1 != dr["L4_Desc"].ToString())
        //                {
        //                    if (intSAL6_ID != 0 || intSAL6_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul></ul>";
        //                    }
        //                    else if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //                    {
        //                        HTMLForTree = HTMLForTree + "</ul>";
        //                    }

        //                    if (intPrevSAL3_ID != intSAL3_ID || intPrevSAL3_ID1 != intSAL3_ID1)
        //                    {
        //                        intPrevSAL3_ID = intSAL3_ID;
        //                        intPrevSAL3_ID1 = intSAL3_ID1;
        //                        HTMLForTree = HTMLForTree + "<ul>";
        //                    }
        //                    intSAL6_ID = 0; intSAL5_ID = 0;
        //                    intSAL6_ID1 = string.Empty; intSAL5_ID1 = string.Empty;

        //                    intPrevSAL4_ID = intSAL4_ID;
        //                    intPrevSAL4_ID1 = intSAL4_ID1;


        //                    if (dr["L4_IC"].ToString() != "342392")
        //                    {
        //                        HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L4_IC"].ToString() + "\" IsChapter='0' data-view=" + HttpUtility.HtmlEncode(dr["L4_No"].ToString()) + " class=\"clsExtension\"><b id=\"" + dr["L4_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + HttpUtility.HtmlEncode(dr["L4_No"].ToString()) + " style=\"font-weight: 100;\">" + HttpUtility.HtmlEncode(dr["L4_Desc"].ToString()) + "</b>";
        //                    }
        //                    else if (dr["L4_IC"].ToString() == "342392")
        //                    {
        //                        if (VINBasedTreeLoadObj.DCTYPE.ToString().ToUpper() == "2")
        //                        {
        //                            if (dr["L4_No"].ToString().Contains("http://") || dr["L4_No"].ToString().Contains("https://"))
        //                            {
        //                                HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L4_IC"].ToString() + "\" IsChapter='0' data-view=" + HttpUtility.HtmlEncode(dr["L4_No"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["L4_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + HttpUtility.HtmlEncode(dr["L4_No"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + HttpUtility.HtmlEncode(dr["L4_No"].ToString()) + "'>" + HttpUtility.HtmlEncode(dr["L4_Desc"].ToString()) + "</a></b>";
        //                            }
        //                            else
        //                            {
        //                                string AWSPrefix = "Images/AssemblyServiceLink";
        //                                HTMLForTree = HTMLForTree + "<li id='\"li" + dr["L4_IC"].ToString() + "\"' IsChapter='0' data-view='" + HttpUtility.HtmlEncode(dr["L4_No"].ToString()) + "' class='\"clsExtension\"'><b class='far fa-dot-circle' style='color:red'></b> <b id='\"" + dr["L4_IC"].ToString() + "\"' IsChapter='0' data-view='" + HttpUtility.HtmlEncode(dr["L4_No"].ToString()) + "' style='font-weight: 100;'><a target='_blank' style='color:red;text-decoration:underline;' href='" + Commo.GetAWSObjectURL(HttpUtility.HtmlEncode(dr["L4_No"].ToString()), AWSPrefix) + "'>" + HttpUtility.HtmlEncode(dr["L4_Desc"].ToString()) + "</a></b>";
        //                            }
        //                        }
        //                        else
        //                        {
        //                            string CurrentLanguage = VINBasedTreeLoadObj.Lang.ToString();
        //                            string PathServS = dr["ServiceSectionLink"].ToString();
        //                            string ReplaceLang = "";
        //                            string FilenameForAWS = "";
        //                            string Prefixpath = "";
        //                            if (CurrentLanguage == "en")
        //                            {
        //                                ReplaceLang = PathServS.Replace("ENGLISH", "EN");
        //                                ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                string[] pathComponents = ReplaceLang.Split('/');
        //                                Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                FilenameForAWS = pathComponents[2];
        //                            }
        //                            else if (CurrentLanguage == "Fr")
        //                            {
        //                                ReplaceLang = PathServS.Replace("FRENCH", "FR");
        //                                ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                string[] pathComponents = ReplaceLang.Split('/');
        //                                Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                FilenameForAWS = pathComponents[2];
        //                            }

        //                            string AWSPrefix = "Service_Section_And_SubSection_Manuals/MANUE/ENTR/ENTR-LFS/" + Prefixpath;
        //                            string FullServiceSectionPath = Commo.GetAWSObjectURL(FilenameForAWS.Trim(), AWSPrefix);
        //                            HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L4_IC"].ToString() + "\" IsChapter='0' data-view=" + HttpUtility.HtmlEncode(dr["L4_No"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["L4_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + HttpUtility.HtmlEncode(dr["L4_No"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + FullServiceSectionPath + "'>" + HttpUtility.HtmlEncode(dr["L4_Desc"].ToString()) + "</a></b>";
        //                        }
        //                    }


        //                    //HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L4_IC"].ToString() + "\" IsChapter='0' data-view=" + dr["L4_No"].ToString() + " class=\"clsExtension\"><b id=\"" + dr["L4_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + dr["L4_No"].ToString() + " style=\"font-weight: 100;\">" + dr["L4_Desc"].ToString() + "</b>";
        //                    intSAL4_ID = Convert.ToInt32(dr["L4_IC"].ToString());
        //                    intSAL4_ID1 = dr["L4_Desc"].ToString();
        //                }
        //            }
        //            else
        //            {
        //                intSAL4_ID = 0;
        //                intSAL4_ID1 = string.Empty;
        //            }
        //            intRow = intRow + 1;
        //        }

        //        //loop end
        //        if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //        {
        //            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul></ul></ul></ul>";
        //        }
        //        else if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //        {
        //            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul></ul></ul>";
        //        }
        //        else if (intSAL4_ID != 0 || intSAL4_ID1 != string.Empty)
        //        {
        //            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul></ul>";
        //        }
        //        else if (intSAL3_ID != 0 || intSAL3_ID1 != string.Empty)
        //        {
        //            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul>";
        //        }
        //        else if (intSAL2_ID != 0 || intSAL2_ID1 != string.Empty)
        //        {
        //            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul>";
        //        }
        //        else if (intSAL1_ID != 0 || intSAL1_ID1 != string.Empty)
        //        {
        //            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul>";
        //        }
        //        else if (intMA_ID != 0 || intMA_ID1 != string.Empty)
        //        {
        //            HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //        }
        //        else
        //        {
        //            HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //        }
        //        intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0; intSAL3_ID = 0; intSAL2_ID = 0; intSAL1_ID = 0; intMA_ID = 0; intPCC_ID = 0;
        //        intSAL6_ID1 = string.Empty; intSAL5_ID1 = string.Empty; intSAL4_ID1 = string.Empty; intSAL3_ID1 = string.Empty;
        //        intSAL2_ID1 = string.Empty; intSAL1_ID1 = string.Empty; intMA_ID1 = string.Empty; intPCC_ID1 = string.Empty;
        //        jsonData = new
        //        {
        //            HTMLForTree

        //            //ModelImageData = GetImageForModel(Model_ID)
        //        };
        //    }
        //    catch (Exception ex)
        //    {
        //        var Request = new System.Net.Http.HttpRequestMessage();
        //        ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(VINBasedTreeLoadObj.UserID), constring);
        //        jsonData = new
        //        {
        //            FirstOccuranceAssembly_ID = 0,
        //            HTMLForTree = ""
        //        };
        //    }
        //    return new JsonResult(jsonData);
        //}
        //#endregion

        //#region Catalogue Order serach Dash Board - Siddesh.P - 07-Nov-2023 :::
        ///// <summary>
        /////  - To VIN/ORDER LIST Dashboard 
        ///// </summary>
        ///// <param name="as per below"></param>
        ///// <returns>Json</returns>
        //public static IActionResult GetServiceLinkAttachmentGrid(string constring, InputAttachment InputAttachmentobj, string sidxx, int rowss, int pagee, string sordd, bool _searchh, long ndd, string filterss)
        //{
        //    string sidx = sidxx;
        //    int rows = Convert.ToInt32(rowss);
        //    int page = Convert.ToInt32(pagee);
        //    string sord = sordd;
        //    bool _search = Convert.ToBoolean(_searchh);
        //    long nd = Convert.ToInt64(ndd);
        //    string filters = filterss;
        //    IEnumerable<TempAttachment_Poko> AttachIE = null;
        //    IQueryable<TempAttachment_Poko> AttachIQ = null;
        //    string OrderID = InputAttachmentobj.OrderNumber;
        //    string OrderNumber = InputAttachmentobj.OrderNumber;
        //    int PartNumber = InputAttachmentobj.PartNumber;
        //    int TotalPages = 0;
        //    int TotalRecords = 0;
        //    string AWSPrefix = string.Empty;
        //    if (InputAttachmentobj.ServiceAttachmentDropval == "1")
        //    {
        //        OrderID = InputAttachmentobj.TopLevelIDValue;
        //        PartNumber = InputAttachmentobj.Section;
        //    }
        //    else
        //    {
        //        OrderNumber = null;
        //        PartNumber = InputAttachmentobj.Section;
        //    }
        //    OrderNumber = string.Empty;
        //    JQGridfilters filterobject = null;
        //    try
        //    {
        //        string FromSerialNumber = string.Empty;
        //        string ToSerialNumber = string.Empty;
        //        string Model = string.Empty;
        //        int Language_ID = InputAttachmentobj.Langauge_ID;
        //        List<TempAttachment_Poko> AttachmentList = null;

        //        var AppPathImg = ConfigurationManager.AppSettings.Get("EPCGetImagePath");
        //        Common Comm = new Common();
        //        string ConnectionString = constring;
        //        using (SqlConnection ConnSql = new SqlConnection(ConnectionString))
        //        {
        //            using (SqlDataAdapter da = new SqlDataAdapter())
        //            {
        //                DataTable dt = new DataTable();
        //                SqlCommand sqlComm = new SqlCommand("Up_Sel_ServiceLinkAttachmentsList", ConnSql);
        //                sqlComm.Parameters.AddWithValue("@OrderID", string.IsNullOrEmpty(OrderID) == true ? 0 : Convert.ToInt32(OrderID));
        //                sqlComm.Parameters.AddWithValue("@Language_ID", Language_ID);
        //                sqlComm.Parameters.AddWithValue("@Part_Assembly_ID", PartNumber);
        //                sqlComm.Parameters.AddWithValue("@User_ID", InputAttachmentobj.User_ID);
        //                da.SelectCommand = sqlComm;
        //                da.SelectCommand.CommandType = CommandType.StoredProcedure;
        //                da.Fill(dt);
        //                AttachIE = (from dr in dt.AsEnumerable()
        //                            select new TempAttachment_Poko
        //                            {
        //                                ServiceLink_ID = dr.Field<int>("ServiceLink_ID"),
        //                                ServiceLink_Part_Assembly_ID = dr.Field<int>("ServiceLink_Part_Assembly_ID"),
        //                                ServiceLink_Sequence = dr.Field<byte>("ServiceLink_Sequence").ToString(),
        //                                ServiceLink_FileNameOrURL = dr.Field<string>("ServiceLink_FileNameOrURL"),
        //                                Category_Name = dr.Field<string>("Category_Name"),

        //                                SectionNumber = dr.Field<string>("SectionNumber"),
        //                                SectionFileName = dr.Field<string>("SectionFileName"),
        //                                SectionDesc = dr.Field<string>("SectionDesc"),
        //                                ServiceLink_UserName = dr.Field<string>("ServiceLink_UserName"),
        //                                ServiceLink_DateTimeString = dr.Field<DateTime>("ServiceLink_DateTime").ToString("dd-MMM-yyyy HH:mm")
        //                            }).ToList();
        //            }
        //        }
        //        AttachmentList = new List<TempAttachment_Poko>();
        //        foreach (var item in AttachIE)
        //        {
        //            TempAttachment_Poko SingleAttachment = new TempAttachment_Poko();
        //            SingleAttachment.ServiceLink_ID = item.ServiceLink_ID;
        //            SingleAttachment.Category_Name = item.Category_Name;
        //            if (item.Attachment_Is_URL == true)
        //            {
        //                //SingleAttachment.Attachment_URL_View = "<span style='background:transparent;border:none;color:black;text-decoration:underline;cursor:pointer;' class='clsFileDesc' link='" + (item.Attachment_Is_URL == true ? item.File_Name : (item.File_Name.Contains("http://") ? item.File_Name : "http://" + item.File_Name)) + "'>" + "<i class='fa fa-eye'>" + "</span>";
        //                SingleAttachment.AttachmentView = "<span style='background:transparent;border:none;color:black;text-decoration:underline;cursor:pointer;' class='clsFileDesc' link='" + (item.Attachment_Is_URL == true ? item.SectionFileName : (item.SectionFileName.Contains("http://") ? item.SectionFileName : "http://" + item.SectionFileName)) + "'>" + "<i class='fa fa-download'>" + "</span>";

        //            }
        //            else
        //            {
        //                string CurrentLanguage = InputAttachmentobj.Lang;
        //                //string GetServiceSectionServerPath = ConfigurationManager.AppSettings.Get("EPCGetServerPath").ToString();
        //                //string GetServiceSectionPubLishPath = ConfigurationManager.AppSettings.Get("EPCGetPublicationPath").ToString();
        //                //string ConServiceSectionServerPubLishPath = GetServiceSectionServerPath + GetServiceSectionPubLishPath;
        //                //string ServiceSectionFilePath = ConServiceSectionServerPubLishPath;
        //                string PathServS = item.ServiceLink_FileNameOrURL.ToString();
        //                string ReplaceLang = "";
        //                string FilenameForAWS = "";
        //                string Prefixpath = "";
        //                if (CurrentLanguage == "en")
        //                {
        //                    ReplaceLang = PathServS.Replace("ENGLISH", "EN");
        //                    ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                    string[] pathComponents = ReplaceLang.Split('/');
        //                    Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                    FilenameForAWS = pathComponents[2];
        //                }
        //                else if (CurrentLanguage == "Fr")
        //                {
        //                    ReplaceLang = PathServS.Replace("FRENCH", "FR");
        //                    ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                    string[] pathComponents = ReplaceLang.Split('/');
        //                    Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                    FilenameForAWS = pathComponents[2];
        //                }
        //                //string FullServiceSectionPath = ServiceSectionFilePath + "\\" + ReplaceLang;
        //                AWSPrefix = "Service_Section_And_SubSection_Manuals/MANUE/ENTR/ENTR-LFS/" + Prefixpath;
        //                //SingleAttachment.Attachment_URL_View = "<a target='_blank' href='" + FullServiceSectionPath + "' class='OpenAttachment' style='background:transparent;border:none;color:black;text-decoration:underline;cursor:pointer;'>" + "<i class='fa fa-eye'>" + "</a>";
        //                //SingleAttachment.AttachmentView = "<a target='_blank' href='" + FullServiceSectionPath + "' class='OpenAttachment' style='background:transparent;border:none;color:black;text-decoration:underline;cursor:pointer;'>" + "<i class='fa fa-download'>" + "</a>";
        //                SingleAttachment.AttachmentView = "<span style='background:transparent;border:none;color:black;text-decoration:underline;cursor:pointer;' class='OpenServiceLinkAttachment'><i class='fa fa-download'></span>";
        //                //SingleAttachment.AttachmentView = "<a target='_blank' href='" + Comm.GetAWSObjectURL(FilenameForAWS, AWSPrefix) + "' class='OpenAttachment' style='background:transparent;border:none;color:black;text-decoration:underline;cursor:pointer;'>" + "<i class='fa fa-download'>" + "</a>";

        //            }

        //            SingleAttachment.ServiceLink_Sequence = item.ServiceLink_Sequence;
        //            SingleAttachment.SectionFileName = item.SectionFileName;
        //            SingleAttachment.ServiceLink_FileNameOrURL = Path.GetFileNameWithoutExtension(item.ServiceLink_FileNameOrURL);
        //            SingleAttachment.SectionNumber = item.SectionNumber;
        //            SingleAttachment.SectionDesc = item.SectionDesc;
        //            SingleAttachment.Category_Name = Path.GetFileNameWithoutExtension(item.Category_Name);
        //            SingleAttachment.ServiceLink_User_ID = item.ServiceLink_User_ID;
        //            SingleAttachment.ServiceLink_UserName = item.ServiceLink_UserName;
        //            SingleAttachment.ServiceLink_DateTimeString = Comm.FrenchDateConvertIncludingTimewithoutsec(item.ServiceLink_DateTimeString.ToString(), InputAttachmentobj.Lang);
        //            AttachmentList.Add(SingleAttachment);
        //        }

        //        AttachIE = AttachmentList;
        //        #region ::: Sorting-Searching :::                
        //        AttachIQ = AttachIE.AsQueryable().Select(d => d);
        //        if (_search == true)
        //        {
        //            //filtering
        //            filters = Common.DecryptString(filters);
        //            filterobject = JsonConvert.DeserializeObject<JQGridfilters>(filters);
        //            AttachIQ = AttachIQ.FilterSearch(filterobject);
        //        }
        //        AttachIE = AttachIQ.AsEnumerable();
        //        AttachIQ = AttachIE.AsQueryable().OrderByField(sidx, sord);
        //        #endregion
        //        #region::: Pagination :::
        //        AttachIE = AttachIQ.AsEnumerable();
        //        try
        //        {
        //            TotalRecords = AttachIE.Count();
        //            TotalPages = Convert.ToInt32(Math.Ceiling((double)AttachIE.Count() / (double)rows));
        //            AttachIE = AttachIE.Skip((page - 1) * rows).Take(rows).ToList();
        //        }
        //        catch (Exception ex)
        //        {
        //            var Request = new System.Net.Http.HttpRequestMessage();
        //            ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(InputAttachmentobj.User_ID), "");
        //            //ExceptionLogger.ErrorLog(ex, Request, InputAttachmentobj.User_ID);
        //        }
        //        #endregion
        //    }
        //    catch (Exception ex)
        //    {
        //        var Request = new System.Net.Http.HttpRequestMessage();
        //        ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(InputAttachmentobj.User_ID), constring);
        //        //ExceptionLogger.ErrorLog(ex, Request, InputAttachmentobj.User_ID);
        //    }
        //    var jsonReader = new
        //    {
        //        root = AttachIE,
        //        page = page,
        //        total = TotalPages,
        //        records = TotalRecords,
        //        filter = filters
        //    };
        //    return new JsonResult(jsonReader);
        //}
        //#endregion

        //#region Catalogue Order serach Dash Board - Siddesh.P - 07-Nov-2023 :::
        ///// <summary>
        /////  - To VIN/ORDER LIST Dashboard 
        ///// </summary>
        ///// <param name="as per below"></param>
        ///// <returns>Json</returns>
        //public static IActionResult GetAssemblyBOMList(string constring, NovaCatalougeBOMListLoad NovaCatalougeBOMListLoadObj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters)
        //{
        //    Common Commo = new Common();
        //    string Conn = constring;
        //    string ispublished = string.Empty;
        //    bool IsShoppingCartPrev = false;
        //    //bool shoppingcartfornova = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["ShoppingCartChangeNova"].ToString());
        //    //bool shoppingcartforprevost = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["ShoppingCartChangePrevost"].ToString());

        //    bool shoppingcartfornova = true;
        //    bool shoppingcartforprevost = true;
        //    // Create a new cookie header
        //    //var cookieHeader = new System.Net.Http.Headers.CookieHeaderValue("my_cookie_key", "cookie_valueDK");

        //    //// Set additional options
        //    //cookieHeader.HttpOnly = false;
        //    //cookieHeader.Secure = false; // Consider using secure flag in production
        //    ////cookieHeader.Domain = "example.com", // Adjust based on your domain
        //    ////.Path = "/", // Adjust based on your path requirements
        //    //// Manually set the SameSite attribute
        //    //string cookieHeaderValue = cookieHeader.ToString();
        //    //cookieHeaderValue += "; SameSite=None"; // Adjust as needed

        //    // Add the modified cookie header to the response headers
        //    //HttpContext.Current.Response.AddHeader("Set-Cookie", cookieHeaderValue);

        //    //if (HttpContext.Current.Request.Params["filters"] == null)
        //    //{
        //    //    filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
        //    //}
        //    //else
        //    //{
        //    //    filters = HttpContext.Current.Request.Params["filters"];
        //    //}

        //    if (shoppingcartfornova == false && NovaCatalougeBOMListLoadObj.DCTYPE.ToString().ToUpper() == "1")
        //    {
        //        IsShoppingCartPrev = false;
        //    }
        //    else if (shoppingcartforprevost && NovaCatalougeBOMListLoadObj.DCTYPE.ToString().ToUpper() == "2")
        //    {
        //        IsShoppingCartPrev = true;
        //    }
        //    else if (shoppingcartfornova && NovaCatalougeBOMListLoadObj.DCTYPE.ToString().ToUpper() == "1")
        //    {
        //        IsShoppingCartPrev = true;
        //    }
        //    var jsonReader = default(dynamic);
        //    int TotalPages = 0;
        //    int TotalRecords = 0;
        //    int Service = 0;
        //    IEnumerable<PartAssemblyViewer> BOMIE = null;
        //    IQueryable<PartAssemblyViewer> BOMIQ = null;
        //    List<PartAssemblyViewer> BOMList = new List<PartAssemblyViewer>();
        //    int SelectedType = 0;
        //    int UserID = Convert.ToInt32(NovaCatalougeBOMListLoadObj.UserID);
        //    string cmdText = string.Empty;

        //    int Part_Assembly_ID = 0;
        //    if (NovaCatalougeBOMListLoadObj.Part_Assembly_No != "" && NovaCatalougeBOMListLoadObj.Part_Assembly_No != null && NovaCatalougeBOMListLoadObj.Part_Assembly_No != "undefined")
        //    {
        //        Part_Assembly_ID = Convert.ToInt32(NovaCatalougeBOMListLoadObj.Part_Assembly_No);
        //        SelectedType = 14;
        //    }
        //    else
        //    {
        //        Part_Assembly_ID = Convert.ToInt32(NovaCatalougeBOMListLoadObj.Section);
        //        SelectedType = 15;
        //    }

        //    int Menu_ID = Commo.GetObjectID("AssemblyPart", NovaCatalougeBOMListLoadObj.DCTYPE, NovaCatalougeBOMListLoadObj.UserID, Conn);
        //    UserLog UserData = new UserLog() { Menu_ID = Menu_ID, Record_ID = Convert.ToInt32(Part_Assembly_ID), ActionType = SelectedType, User_ID = Convert.ToInt32(NovaCatalougeBOMListLoadObj.UserID) };

        //    using (SqlConnection conn = new SqlConnection(Conn))
        //    {
        //        string ispublishedquery = "SELECT Part_Is_Published FROM MA_Part_Assembly where Part_Assembly_ID=" + Part_Assembly_ID;
        //        if (conn.State == ConnectionState.Closed)
        //        {
        //            conn.Open();
        //        }
        //        SqlCommand _CMD = new SqlCommand(ispublishedquery, conn);
        //        SqlDataReader DataReaderPublished = null;
        //        DataReaderPublished = _CMD.ExecuteReader();

        //        if (DataReaderPublished.HasRows)
        //        {
        //            while (DataReaderPublished.Read())
        //            {
        //                ispublished = DataReaderPublished[0].ToString();
        //            }
        //        }
        //    }
        //    if (isPreviewAuthorValue == "PreviewAuthor")
        //    {
        //        if (ispublished == "True" || ispublished == "true")
        //        {
        //            if (NovaCatalougeBOMListLoadObj.DCTYPE == 2)
        //            {
        //                cmdText = "Usp_Sel_User_Drawing_BOM_List_VIN";
        //            }
        //            else
        //            {
        //                cmdText = "Usp_Sel_User_Drawing_BOM_List";
        //            }
        //        }
        //        else
        //        {
        //            cmdText = "Usp_Sel_User_Drawing_BOM_ListAuthor";
        //        }
        //    }
        //    else
        //    {
        //        if (NovaCatalougeBOMListLoadObj.DCTYPE == 2)
        //        {
        //            cmdText = "Usp_Sel_User_Drawing_BOM_List_VIN";
        //        }
        //        else
        //        {
        //            cmdText = "Usp_Sel_User_Drawing_BOM_List";
        //        }
        //    }
        //    PartAssemblyHeader AssyDetails = new PartAssemblyHeader();
        //    try
        //    {
        //        int ISRange = -1;//Request.Params["FromSerialNumber"].ToString();
        //        string FromSerialNumber = string.Empty;
        //        string ToSerialNumber = string.Empty;
        //        string Model = string.Empty;
        //        var VehicleIC = default(dynamic);
        //        int? intFromSerialNumber = 0;
        //        int? intToSerialNumber = 0;
        //        NovaCatalougeBOMListLoadObj.FromSerialNumber = NovaCatalougeBOMListLoadObj.FromSerialNumber ?? "";
        //        NovaCatalougeBOMListLoadObj.ToSerialNo = NovaCatalougeBOMListLoadObj.ToSerialNo ?? "";
        //        NovaCatalougeBOMListLoadObj.TopLevelSectionVIN = NovaCatalougeBOMListLoadObj.TopLevelSectionVIN ?? "";
        //        NovaCatalougeBOMListLoadObj.Vehicle = NovaCatalougeBOMListLoadObj.Vehicle ?? "";
        //        NovaCatalougeBOMListLoadObj.Model = NovaCatalougeBOMListLoadObj.Model ?? "";
        //        if (NovaCatalougeBOMListLoadObj.DCTYPE.ToString().ToUpper() == "2")
        //        {
        //            if (NovaCatalougeBOMListLoadObj.TopLevelSectionVIN.ToString() != "" && NovaCatalougeBOMListLoadObj.FromSerialNumber.ToString() == "")
        //            {
        //                FromSerialNumber = NovaCatalougeBOMListLoadObj.TopLevelSectionVINShortCode.ToString();
        //                ToSerialNumber = NovaCatalougeBOMListLoadObj.ToSerialNo.ToString();
        //                Model = (NovaCatalougeBOMListLoadObj.Model.ToString() == "" ? NovaCatalougeBOMListLoadObj.TopLevelSectionVINModel.ToString() : NovaCatalougeBOMListLoadObj.Model.ToString());
        //                VehicleIC = Convert.ToInt32(NovaCatalougeBOMListLoadObj.TopLevelSectionVIN.ToString() == "" ? "0" : NovaCatalougeBOMListLoadObj.TopLevelSectionVIN.ToString());
        //                VehicleIC = (VehicleIC == 0 ? DBNull.Value : VehicleIC);
        //            }
        //            else
        //            {
        //                //Session["TopLevelSectionVIN"] = "";
        //                //Common.SetSessionAndCookie("TopLevelSectionVIN", ""); //add to cookie and return it and store in localstorage and send back as parameter where required in another API method
        //                FromSerialNumber = NovaCatalougeBOMListLoadObj.FromSerialNumber.ToString();
        //                ToSerialNumber = NovaCatalougeBOMListLoadObj.ToSerialNo.ToString();
        //                Model = NovaCatalougeBOMListLoadObj.Model.ToString();
        //                VehicleIC = Convert.ToInt32(NovaCatalougeBOMListLoadObj.Vehicle.ToString() == "" ? "0" : NovaCatalougeBOMListLoadObj.Vehicle.ToString());
        //                VehicleIC = (VehicleIC == 0 ? DBNull.Value : VehicleIC);
        //            }

        //            if (FromSerialNumber != "" && ToSerialNumber != "")
        //            {
        //                intFromSerialNumber = Commo.GetVinShortNumber(FromSerialNumber, NovaCatalougeBOMListLoadObj.DCTYPE, NovaCatalougeBOMListLoadObj.UserID, constring);
        //                intToSerialNumber = Commo.GetVinShortNumber(ToSerialNumber, NovaCatalougeBOMListLoadObj.DCTYPE, NovaCatalougeBOMListLoadObj.UserID, constring);
        //                ISRange = 1;
        //            }
        //            else if (FromSerialNumber != "" || ToSerialNumber != "")
        //            {
        //                if (FromSerialNumber != "")
        //                {
        //                    intFromSerialNumber = Commo.GetVinShortNumber(FromSerialNumber, NovaCatalougeBOMListLoadObj.DCTYPE, NovaCatalougeBOMListLoadObj.UserID, constring);
        //                }
        //                else if (ToSerialNumber != "" && FromSerialNumber == "")
        //                {
        //                    intFromSerialNumber = Commo.GetVinShortNumber(ToSerialNumber, NovaCatalougeBOMListLoadObj.DCTYPE, NovaCatalougeBOMListLoadObj.UserID, constring);
        //                }
        //                ISRange = 0;
        //            }
        //            if (FromSerialNumber == "" && ToSerialNumber == "" && Model != "")
        //            {
        //                intFromSerialNumber = 0;
        //                ISRange = 0;
        //            }
        //        }
        //        using (SqlConnection ConnSql = new SqlConnection(Conn))
        //        {
        //            SqlDataReader DataReader = null;
        //            string Query = String.Empty;
        //            if (NovaCatalougeBOMListLoadObj.Lang.ToString().ToUpper() == "EN")
        //            {
        //                if (NovaCatalougeBOMListLoadObj.DCTYPE == 2)
        //                {
        //                    Query = ("SELECT TOP 1 Part_Assembly_No, Part_Assembly_Description + IIF(ISNULL(Part_Assembly_Specification,'') = '', '', ' ' + Part_Assembly_Specification + ' ') + IIF(ISNULL(C.ManageBOM_FROM_SN_S_Code, '') = '', '', ' From  ' + ISNULL(C.ManageBOM_FROM_SN_S_Code, '') + ' ') + IIF(ISNULL(C.ManageBOM_To_SN_S_Code, '') = '', '', ' To  ' + ISNULL(C.ManageBOM_To_SN_S_Code, '')) + IIF(ISNULL(C.ManageBOM_Model_Name, '') = '', '', ' Model ' + C.ManageBOM_Model_Name), " +
        //                            " IIF(Bookmark_ID IS NULL, 0, Bookmark_ID) AS BID FROM MA_Part_Assembly " +
        //                            " LEFT JOIN TR_Manage_Bookmarks ON Bookmark_Part_Assembly_ID = Part_Assembly_ID AND Bookmark_User_ID=" + UserID +
        //                            " LEFT JOIN(SELECT TOP 1 * FROM MA_ManageBOM WHERE ManageBOM_Part_ID = " + Part_Assembly_ID + " ORDER BY LEN(ManageBOM_Item_No), ManageBOM_Item_No, ManageBOM_Sequence) C ON C.ManageBOM_Part_ID = Part_Assembly_ID " +
        //                            " WHERE Part_Assembly_ID = " + Part_Assembly_ID);
        //                }
        //                else
        //                {
        //                    Query = ("SELECT Part_Assembly_No, Part_Assembly_Description, IIF(Bookmark_ID IS NULL, 0, Bookmark_ID) AS BID FROM MA_Part_Assembly " +
        //                                "LEFT JOIN TR_Manage_Bookmarks ON Bookmark_Part_Assembly_ID = Part_Assembly_ID AND Bookmark_User_ID=" + UserID +
        //                                " WHERE Part_Assembly_ID = " + Part_Assembly_ID);
        //                }
        //            }
        //            else
        //            {
        //                if (NovaCatalougeBOMListLoadObj.DCTYPE == 2)
        //                {
        //                    Query = ("SELECT Part_Assembly_No, IIF(MA_Part_Assembly_Locale.Part_Assembly_Description = '' OR MA_Part_Assembly_Locale.Part_Assembly_Description IS NULL,MA_Part_Assembly.Part_Assembly_Description,MA_Part_Assembly_Locale.Part_Assembly_Description)  + IIF(ISNULL(Part_Assembly_Specification,'') = '', '', ' ' + Part_Assembly_Specification + ' ') + IIF(ISNULL(C.ManageBOM_FROM_SN_S_Code,'')='','',' De '+ ISNULL(C.ManageBOM_FROM_SN_S_Code,''))+IIF(ISNULL(C.ManageBOM_To_SN_S_Code,'')='','',' À '+ ISNULL(C.ManageBOM_To_SN_S_Code,''))+IIF(ISNULL(C.ManageBOM_Model_Name,'')='','',' Modèle '+C.ManageBOM_Model_Name), " +
        //                            " IIF(Bookmark_ID IS NULL, 0, Bookmark_ID) AS BID FROM MA_Part_Assembly " +
        //                            " LEFT JOIN TR_Manage_Bookmarks ON Bookmark_Part_Assembly_ID = MA_Part_Assembly.Part_Assembly_ID AND Bookmark_User_ID=" + UserID +
        //                            " LEFT JOIN MA_Part_Assembly_Locale ON MA_Part_Assembly_Locale.Part_Assembly_ID = MA_Part_Assembly.Part_Assembly_ID " +
        //                            " LEFT JOIN MA_Language ON MA_Part_Assembly_Locale.Part_Assembly_Language_ID = MA_Language.Langauge_ID " +
        //                            " LEFT JOIN(SELECT TOP 1 * FROM MA_ManageBOM WHERE ManageBOM_Part_ID = " + Part_Assembly_ID + " ORDER BY LEN(ManageBOM_Item_No), ManageBOM_Item_No, ManageBOM_Sequence) C ON C.ManageBOM_Part_ID = MA_Part_Assembly.Part_Assembly_ID " +
        //                            " WHERE MA_Part_Assembly.Part_Assembly_ID = " + Part_Assembly_ID);
        //                }
        //                else
        //                {
        //                    Query = ("SELECT Part_Assembly_No, IIF(MA_Part_Assembly_Locale.Part_Assembly_Description ='' OR MA_Part_Assembly_Locale.Part_Assembly_Description IS NULL,MA_Part_Assembly.Part_Assembly_Description,MA_Part_Assembly_Locale.Part_Assembly_Description) , IIF(Bookmark_ID IS NULL, 0, Bookmark_ID) AS BID FROM MA_Part_Assembly " +
        //                                " LEFT JOIN TR_Manage_Bookmarks ON Bookmark_Part_Assembly_ID = MA_Part_Assembly.Part_Assembly_ID AND Bookmark_User_ID=" + UserID +
        //                                " LEFT JOIN MA_Part_Assembly_Locale ON MA_Part_Assembly_Locale.Part_Assembly_ID = MA_Part_Assembly.Part_Assembly_ID " +
        //                                " LEFT JOIN MA_Language ON MA_Part_Assembly_Locale.Part_Assembly_Language_ID = MA_Language.Langauge_ID " +
        //                                " WHERE MA_Part_Assembly.Part_Assembly_ID = " + Part_Assembly_ID);
        //                }
        //            }
        //            using (SqlCommand sqlComm = new SqlCommand(Query, ConnSql))
        //            {
        //                if (ConnSql.State == ConnectionState.Closed)
        //                {
        //                    ConnSql.Open();
        //                }
        //                DataReader = sqlComm.ExecuteReader();
        //                if (DataReader.HasRows)
        //                {
        //                    while (DataReader.Read())
        //                    {
        //                        AssyDetails.Part_Assembly_No = DataReader[0].ToString();
        //                        AssyDetails.Part_Assembly_Description = DataReader[1].ToString();
        //                        AssyDetails.BID = Convert.ToInt32(DataReader[2]);
        //                    }
        //                }
        //            }
        //            using (SqlCommand SqlCmd = new SqlCommand(cmdText, ConnSql))
        //            {
        //                DataReader = null;
        //                int MenuID = Commo.GetMenuID("AssemblyPart", NovaCatalougeBOMListLoadObj.DCTYPE, NovaCatalougeBOMListLoadObj.UserID, Conn);
        //                //int MenuID = Commo.GetObjectID("AssemblyPartViewer");
        //                SqlCmd.CommandText = cmdText;
        //                SqlCmd.CommandType = CommandType.StoredProcedure;
        //                SqlCmd.Parameters.AddWithValue("@Assembly_ID", Part_Assembly_ID);

        //                if (NovaCatalougeBOMListLoadObj.DCTYPE == 2)
        //                {
        //                    if (ISRange == 0 || ISRange == 1 && ISRange != -1)
        //                    {
        //                        SqlCmd.Parameters.AddWithValue("@IsRange", ISRange);
        //                    }
        //                    else
        //                    {
        //                        SqlCmd.Parameters.AddWithValue("@IsRange", DBNull.Value);
        //                    }

        //                    if (intFromSerialNumber == 0)
        //                    {
        //                        SqlCmd.Parameters.AddWithValue("@FromSN", DBNull.Value);
        //                    }
        //                    else
        //                    {
        //                        SqlCmd.Parameters.AddWithValue("@FromSN", intFromSerialNumber);
        //                    }

        //                    if (intToSerialNumber == 0)
        //                    {
        //                        SqlCmd.Parameters.AddWithValue("@ToSN", DBNull.Value);
        //                    }
        //                    else
        //                    {
        //                        SqlCmd.Parameters.AddWithValue("@ToSN", intToSerialNumber);
        //                    }

        //                    if (Model == "")
        //                    {
        //                        SqlCmd.Parameters.AddWithValue("@Model_IC", DBNull.Value);
        //                    }
        //                    else
        //                    {
        //                        SqlCmd.Parameters.AddWithValue("@Model_IC", Model);
        //                    }

        //                    SqlCmd.Parameters.AddWithValue("@Vehicle_IC", VehicleIC);
        //                    SqlCmd.Parameters.AddWithValue("@User_ID", UserID);
        //                }
        //                else
        //                {
        //                    SqlCmd.Parameters.AddWithValue("@User_ID", Convert.ToInt32(UserID));


        //                }
        //                SqlCmd.Parameters.AddWithValue("@MENU_ID", MenuID);
        //                SqlCmd.Parameters.AddWithValue("@Language_ID", NovaCatalougeBOMListLoadObj.Langauge_ID);
        //                SqlCmd.Parameters.Add("@HasServiceLink", SqlDbType.Int).Direction = ParameterDirection.Output;

        //                if (ConnSql.State == ConnectionState.Closed)
        //                {
        //                    ConnSql.Open();
        //                }
        //                SqlCmd.CommandTimeout = 100;
        //                DataReader = SqlCmd.ExecuteReader();
        //                int Count = 0;
        //                if (DataReader.HasRows)
        //                {
        //                    while (DataReader.Read())
        //                    {
        //                        PartAssemblyViewer SingleRecord = new PartAssemblyViewer();
        //                        SingleRecord.Part_Assembly_ID = int.Parse(DataReader["SetMember_Part_ID"].ToString());

        //                        SingleRecord.ManageBOM_Item_No = (Convert.ToBoolean(DataReader["Hide_Item_No"]) == true ? (NovaCatalougeBOMListLoadObj.DCTYPE == 2 ? "NS" : "") : DataReader["Item_No"].ToString());

        //                        SingleRecord.MFG_Code = (DataReader["GlobalCode"].ToString() == "1" ? "" : (DataReader["SetNumber_MFGCode"].ToString() == "DSP" ? "" : DataReader["SetNumber_MFGCode"].ToString()));

        //                        if (NovaCatalougeBOMListLoadObj.DCTYPE == 2)
        //                        {
        //                            SingleRecord.MFG_Code = (DataReader["GlobalCode"].ToString() == "1" ? "" : (DataReader["SetNumber_MFGCode"].ToString() == "DSP" ? "" : (DataReader["SetNumber_MFGCode"].ToString() == "NOVA" || DataReader["SetNumber_MFGCode"].ToString() == "PREV" || DataReader["SetNumber_MFGCode"].ToString() == "VOLV") ? DataReader["SetNumber_MFGCode"].ToString() : ""));
        //                            switch (DataReader["GlobalCode"].ToString().Split(' ')[0].Trim())
        //                            {
        //                                case "1":
        //                                    SingleRecord.Part_Assembly_No = "N.S.S.";
        //                                    break;
        //                                case "2":
        //                                    SingleRecord.Part_Assembly_No = "N.S.P.";
        //                                    break;
        //                                case "3":
        //                                    SingleRecord.Part_Assembly_No = "N.S.A.";
        //                                    break;
        //                                case "4":
        //                                    SingleRecord.Part_Assembly_No = "N.P.N.";
        //                                    break;
        //                                case "P":
        //                                    SingleRecord.Part_Assembly_No = "Call order desk";
        //                                    break;
        //                                case "V":
        //                                    SingleRecord.Part_Assembly_No = "Refer To Volvo";
        //                                    break;
        //                                default:
        //                                    SingleRecord.Part_Assembly_No = DataReader["SetNumber"].ToString();
        //                                    break;
        //                            }
        //                        }
        //                        else
        //                        {
        //                            SingleRecord.Part_Assembly_No = HttpUtility.HtmlEncode(DataReader["SetNumber"].ToString());
        //                        }
        //                        SingleRecord.Part_Assembly_Description = NovaCatalougeBOMListLoadObj.Lang.ToString().ToLower() == "en" ? HttpUtility.HtmlEncode(DataReader["Part_Assembly_Description"].ToString().Replace("\n", "")) : (DataReader["French"].ToString() != "" ? HttpUtility.HtmlEncode(DataReader["French"].ToString().Replace("\n", "")) : DataReader["Dictionary_Locale_Description"].ToString());
        //                        SingleRecord.ManageBOM_Qty = DataReader["ManageBOM_Qty"].ToString();
        //                        SingleRecord.Part_Assembly_Specification = HttpUtility.HtmlEncode(DataReader["Part_Assembly_Specification"].ToString());
        //                        SingleRecord.ManageBOM_Font_ID = int.Parse(DataReader["ManageBOM_Font_ID"].ToString());
        //                        SingleRecord.Part_Assembly_IsSupersession = Convert.ToBoolean(DataReader["Part_Assembly_IsSupersession"]);
        //                        SingleRecord.Part_Assembly_Remarks = DataReader["ManageBOM_Remarks"].ToString();
        //                        SingleRecord.Img = Convert.ToBoolean(DataReader["Img"]);
        //                        SingleRecord.Part_Assembly_IsPurchasable = (Convert.ToBoolean(DataReader["Part_Assembly_IsPurchasable"]) == true ? "<span class='ClsBOMShoppingCart'><span class='fa fa-cart-plus'></span></span>" : "");
        //                        SingleRecord.Part_Assembly_IsAssembly = (Convert.ToBoolean(DataReader["Part_Assembly_IsAssembly"]) == true ? "Yes" : "No");
        //                        SingleRecord.Part_Has_Recommended = Convert.ToBoolean(DataReader["Part_Has_Recommended"]);
        //                        SingleRecord.BOM_HotSpot_Coordinates = DataReader["File_NameWithXY"].ToString();
        //                        SingleRecord.Font_Name = DataReader["Font_Name"].ToString();
        //                        SingleRecord.Font_Size = Convert.ToInt32(DataReader["Font_Size"].ToString());
        //                        SingleRecord.Font_Style = DataReader["Font_Style"].ToString();
        //                        SingleRecord.Font_Colour = DataReader["Font_Colour"].ToString();
        //                        SingleRecord.RowNumber = ++Count;
        //                        SingleRecord.RecommendedParts_ID = (SingleRecord.Part_Has_Recommended == true ? "1" : "");
        //                        SingleRecord.ManageBOM_ID = DataReader["ManageBOM_ID"].ToString();
        //                        SingleRecord.ManageBOM_MaskingAction_ID = DataReader["ManageBOM_MaskingAction_ID"].ToString();
        //                        SingleRecord.Part_Assembly_AlternateNo = DataReader["Part_Assembly_AlternateNo"].ToString();
        //                        SingleRecord.Vendor_Name = DataReader["Vendor_Name"].ToString();
        //                        SingleRecord.VendorPartInfo_PartNo = DataReader["VendorPartInfo_PartNo"].ToString();
        //                        SingleRecord.FunctionGroup = DataReader["FunctionGroup"].ToString();
        //                        SingleRecord.Concat_Item_No = DataReader["Concat_Item_No"].ToString();
        //                        SingleRecord.Author_Notes = DataReader["NoteText"].ToString();

        //                        SingleRecord.DATE_CREATED = DataReader["DATE_CREATED"].ToString();
        //                        SingleRecord.French = (DataReader["French"].ToString() != "" ? HttpUtility.HtmlEncode(DataReader["French"].ToString().Replace("\n", "")) : DataReader["Dictionary_Locale_Description"].ToString());
        //                        SingleRecord.Mfrer = DataReader["Mfrer"].ToString();
        //                        SingleRecord.Part_Status = DataReader["Part_Status"].ToString();
        //                        SingleRecord.REVISION = DataReader["REVISION"].ToString();
        //                        if (NovaCatalougeBOMListLoadObj.DCTYPE == 2)
        //                        {
        //                            SingleRecord.Stackno = DataReader["Stockno"].ToString();
        //                        }
        //                        else
        //                        {
        //                            SingleRecord.StockNo1 = DataReader["StockNo1"].ToString();
        //                            SingleRecord.StockNo2 = DataReader["StockNo2"].ToString();
        //                            SingleRecord.StockNo3 = DataReader["StockNo3"].ToString();
        //                            SingleRecord.StockNo4 = DataReader["StockNo4"].ToString();
        //                            SingleRecord.StockNo5 = DataReader["StockNo5"].ToString();
        //                        }
        //                        SingleRecord.IsShoppingCartPart = IsShoppingCartPrev == false ? false : DataReader["IsShoppingCartPart"] is DBNull ? false : Convert.ToBoolean(DataReader["IsShoppingCartPart"]);
        //                        SingleRecord.IsBOMinShoppingCart = IsShoppingCartPrev == false ? false : DataReader["IsShoppingCartPart"] is DBNull ? false : Convert.ToBoolean(DataReader["IsBOMinShoppingCart"]);
        //                        SingleRecord.IsAddToShoppingCart = IsShoppingCartPrev == false ? "" : SingleRecord.IsShoppingCartPart == true && SingleRecord.IsBOMinShoppingCart == true ? GetGlobalResourceObject(NovaCatalougeBOMListLoadObj.Culture, "ResYes").ToString() : GetGlobalResourceObject(NovaCatalougeBOMListLoadObj.Culture, "ResNo").ToString();

        //                        if (NovaCatalougeBOMListLoadObj.DCTYPE == 2)
        //                        {
        //                            if (DataReader["GlobalCode"].ToString().Trim() == "U")
        //                            {

        //                            }
        //                            else
        //                            {
        //                                BOMList.Add(SingleRecord);
        //                            }
        //                        }
        //                        else
        //                        {
        //                            BOMList.Add(SingleRecord);
        //                        }
        //                    }
        //                }
        //                BOMIE = BOMList;

        //                ConnSql.Close();
        //                Service = Convert.ToInt32(SqlCmd.Parameters["@HasServiceLink"].Value.ToString());
        //                #region :::Sorting-Searching:::                       
        //                if (_search)
        //                {
        //                    filters = Common.DecryptString(filters);
        //                    JQGridfilters _filters = JsonConvert.DeserializeObject<JQGridfilters>(filters);
        //                    BOMIQ = BOMIE.AsQueryable();
        //                    BOMIQ = BOMIQ.FilterSearch(_filters);
        //                }
        //                else
        //                {
        //                    BOMIQ = BOMIE.Select(c => c).AsQueryable().JQGridSorting(sidx, sord);
        //                }
        //                #endregion

        //                #region::: Pagination :::
        //                BOMIE = BOMIQ.AsEnumerable();
        //                //Session["ExportBOMDetails"] = BOMIE; write seprate method for Export
        //                try
        //                {
        //                    TotalRecords = BOMIE.Count();
        //                    TotalPages = Convert.ToInt32(Math.Ceiling((double)BOMIE.Count() / (double)rows));
        //                    BOMIE = BOMIE.Skip((page - 1) * rows).Take(rows).ToList();
        //                    //if (TotalRecords > 0)                            
        //                    //Session["BOMDetails"] = BOMIE;  --check
        //                    List<ShoppingCartClsForGrid> ShoppingCartPartIDList = new List<ShoppingCartClsForGrid>();
        //                    if (IsShoppingCartPrev == false)
        //                    {
        //                        if (NovaCatalougeBOMListLoadObj.CurrentShoppingCartData != null)
        //                        {
        //                            ShoppingCartPartIDList = (List<ShoppingCartClsForGrid>)(NovaCatalougeBOMListLoadObj.CurrentShoppingCartData);
        //                            ShoppingCartPartIDList = ShoppingCartPartIDList.Where(S => S.BrandID == Convert.ToInt32(NovaCatalougeBOMListLoadObj.BrandID)).Select(S => S).ToList();
        //                            foreach (var item in ShoppingCartPartIDList)
        //                            {
        //                                int PartID = BOMIE.Where(B => B.Part_Assembly_ID == item.ID).Select(B => B.Part_Assembly_ID).FirstOrDefault();
        //                                if (PartID > 0)
        //                                {
        //                                    foreach (var Update in BOMIE)
        //                                    {
        //                                        if (Update.Part_Assembly_ID == PartID)
        //                                        {
        //                                            Update.Part_Assembly_IsPurchasable = "<span class='ClsBOMShoppingCart ClsAdded'><span class='fa fa-shopping-cart'></span></span>"; ;
        //                                        }
        //                                    }
        //                                }
        //                            }
        //                        }
        //                    }
        //                    else
        //                    {
        //                        foreach (var Update in BOMIE)
        //                        {
        //                            if (Update.IsShoppingCartPart == true && Update.IsBOMinShoppingCart == true)
        //                            {
        //                                Update.Part_Assembly_IsPurchasable = "<span class='ClsBOMShoppingCart ClsAdded ClsHighLight'><span class='fa fa-shopping-cart'></span></span>"; ;
        //                            }
        //                            else if (Update.IsShoppingCartPart == true && Update.IsBOMinShoppingCart == false)
        //                            {
        //                                Update.Part_Assembly_IsPurchasable = "<span class='ClsBOMShoppingCart ClsHighLight'><span class='fa fa-cart-plus'></span></span>"; ;
        //                            }

        //                        }
        //                    }
        //                }
        //                #endregion

        //                catch (Exception ex)
        //                {
        //                    var Request = new System.Net.Http.HttpRequestMessage();
        //                    ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaCatalougeBOMListLoadObj.UserID), constring);
        //                }
        //                jsonReader = new
        //                {
        //                    root = BOMIE,
        //                    page = page,
        //                    total = TotalPages,
        //                    records = TotalRecords,
        //                    AssyID = Part_Assembly_ID,
        //                    AssyNo = AssyDetails.Part_Assembly_No,
        //                    AssyDescription = AssyDetails.Part_Assembly_Description,
        //                    BCount = AssyDetails.BID,
        //                    HasServiceLink = Service,
        //                    PartBySearch = NovaCatalougeBOMListLoadObj.PartBySearch,
        //                    FromCatalogue = false,
        //                    filter = filters,
        //                    Vinshortcode = FromSerialNumber,
        //                    VINModelName = Model,
        //                    Vehicle = VehicleIC,
        //                    TopLevelSectionVIN = ""

        //                };
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {

        //        var Request = new System.Net.Http.HttpRequestMessage();
        //        ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaCatalougeBOMListLoadObj.UserID), constring);
        //    }
        //    finally
        //    {
        //        SqlConnection.ClearAllPools();
        //    }
        //    return new JsonResult(jsonReader);
        //}
        //#endregion

        //#region JWT token Gen - Siddesh.P
        ///// <summary>
        ///// - To generate JWT TOKEN
        ///// </summary>
        ///// <param name="model"></param>
        ///// <returns></returns>

        //public static IActionResult JWTGenOnLogin(UserLoginModel model)
        //{
        //    var securityKey = new SymmetricSecurityKey(Convert.FromBase64String(SecretKey));
        //    var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);
        //    var header = new JwtHeader(credentials);
        //    int TokenExpiryMinutes = Convert.ToInt32(ConfigurationManager.AppSettings.Get("DCAPITimeOut")); //Token expiration time
        //    // Create the token payload (claims)
        //    var claims = new[]
        //    {
        //        //new Claim(ClaimTypes.Name, model.Username),
        //        new Claim("exp", DateTimeOffset.Now.AddMinutes(60).ToUnixTimeSeconds().ToString()),
        //        new Claim("iss", Issuer),
        //        new Claim("aud", Audience),
        //    };
        //    var payload = new JwtPayload(claims);
        //    // Create the JWT token
        //    var jwtSecurityToken = new JwtSecurityToken(header, payload);
        //    var tokenString = "";
        //    try
        //    {
        //        // Sign the token to create the signature
        //        var tokenHandler = new JwtSecurityTokenHandler();
        //        tokenString = tokenHandler.WriteToken(jwtSecurityToken);
        //    }
        //    catch (Exception ex)
        //    {
        //        var Request = new System.Net.Http.HttpRequestMessage();
        //        ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(model.Username), "");
        //    }
        //    return new JsonResult(tokenString);
        //}
        //#endregion



        //#region ::: To Export Search order details Details::: -Siddesh.p 
        ///// <summary>
        /////  To generate JWT TOKEN
        ///// </summary>
        ///// <returns></returns>
        //public static IActionResult ExportSerachOrderDetails1(string connstring, ExportDashboardOrderList ExportDashboardOrderListObj)
        //{
        //    string Conn = connstring;
        //    DataTable dt1 = new DataTable();
        //    HttpResponseMessage responseExl = new HttpResponseMessage(HttpStatusCode.OK);
        //    IEnumerable<Order> OrderDetailsIE = null;
        //    IQueryable<Order> OrderDetailsIQ = null;
        //    List<Order> OrderList = new List<Order>();

        //    int UserID = Convert.ToInt32(ExportDashboardOrderListObj.UserID);
        //    DataSet ds = new DataSet("OrderData");
        //    using (SqlConnection conn = new SqlConnection(Conn))
        //    {
        //        SqlCommand sqlComm = new SqlCommand("USP_Sel_UserAssociatedOrdersOrByVehicles_Search", conn);
        //        sqlComm.Parameters.AddWithValue("@USERID", UserID);
        //        sqlComm.Parameters.AddWithValue("@CONDITION", ExportDashboardOrderListObj.Condition);
        //        sqlComm.Parameters.AddWithValue("@OrderByColumn", ExportDashboardOrderListObj.sidx);
        //        sqlComm.Parameters.AddWithValue("@OrderByDirection", ExportDashboardOrderListObj.sord);
        //        sqlComm.CommandType = CommandType.StoredProcedure;
        //        SqlDataAdapter da = new SqlDataAdapter();
        //        if (conn.State == ConnectionState.Closed)
        //        {
        //            conn.Open();
        //        }
        //        sqlComm.CommandTimeout = 100;
        //        da.SelectCommand = sqlComm;
        //        da.Fill(ds);
        //        OrderList = ds.Tables[0].AsEnumerable().Select(dataRow => new Order
        //        {
        //            Customer_Name = dataRow.Field<string>("Customer_Name"),
        //            Order_ID = dataRow.Field<int>("Catalogue_ID"),
        //            Brand_ID = dataRow.Field<int>("Brand_ID"),
        //            Brand_Name = dataRow.Field<string>("Brand_Name"),
        //            Model_ID = dataRow.Field<int>("Model_ID"),
        //            Model_Name = dataRow.Field<string>("Model_Name"),
        //            ProductType_ID = dataRow.Field<int>("ProductType_ID"),
        //            Product_Type_Name = dataRow.Field<string>("ProductType_Name"),
        //            Order_No = dataRow.Field<string>("Catalogue_OrderNo"),
        //            //Order_Date = dataRow.Field<DateTime>("Catalogue_OrderDate").ToString("dd-MMM-yyyy") == "01-Jan-1900" ? "" : Commo.FrenchDateConvert((dataRow.Field<DateTime>("Catalogue_OrderDate")).ToString("dd-MMM-yyyy"), ExportDashboardOrderListObj.Lang.ToString()),
        //            Remarks = dataRow.Field<string>("Catalogue_Remarks"),
        //            Part_Assembly_id = dataRow.Field<int>("Part_Assembly_id"),
        //            Vehicle_ID = dataRow.Field<int>("Vehicle_ID"),
        //            Vehicle_RoadNo = dataRow.Field<string>("Vehicle_RoadNo"),
        //            Vehicle_VIN = dataRow.Field<string>("Vehicle_VIN"),
        //            Vehicle_VIN_ShortCode = dataRow.Field<string>("VinshortNumber"),
        //            //Count_Of_Vehicle = dataRow.Field<int>("Count_Of_Vehicle")
        //        }).ToList();
        //        if (ExportDashboardOrderListObj.Model.ToUpper() == "YES")
        //        {
        //            int ModelID = Convert.ToInt32(ExportDashboardOrderListObj.ModelVal);
        //            OrderList = OrderList.Select(O => O).Where(O => O.Model_ID == ModelID).ToList();
        //        }
        //        else if (ExportDashboardOrderListObj.Model.ToUpper() != "NO")
        //        {
        //            int ModelID = Convert.ToInt32(ExportDashboardOrderListObj.Model);
        //            OrderList = OrderList.Select(O => O).Where(O => O.Model_ID == ModelID).ToList();
        //        }
        //    }
        //    OrderDetailsIE = OrderList;
        //    if (ExportDashboardOrderListObj.filter != null)
        //    {
        //        var FilteredRecord = ExportDashboardOrderListObj.filter.ToString();
        //        FilteredRecord = Common.DecryptString(ExportDashboardOrderListObj.filter.ToString());
        //        JQGridfilters filterobject = JsonConvert.DeserializeObject<JQGridfilters>(FilteredRecord);
        //        OrderDetailsIQ = OrderDetailsIE.AsQueryable().Select(d => d);
        //        OrderDetailsIQ = OrderDetailsIQ.FilterSearch(filterobject);
        //    }
        //    else
        //    {
        //        //sorting
        //        if (ExportDashboardOrderListObj.sidx.ToString() != "Order_Date")
        //        {
        //            OrderDetailsIQ = OrderDetailsIE.AsQueryable().JQGridSorting(ExportDashboardOrderListObj.sidx.ToString(), ExportDashboardOrderListObj.sord.ToString());
        //        }
        //        string Order_No = ExportDashboardOrderListObj.sidx.ToString();
        //        if (Order_No == "Vehicle_VIN")
        //        {
        //            Order_No = "Order_No";
        //            OrderDetailsIQ = OrderDetailsIE.AsQueryable().JQGridSorting(Order_No, ExportDashboardOrderListObj.sord.ToString());
        //        }
        //        OrderDetailsIQ = OrderDetailsIE.AsQueryable();
        //    }
        //    var UserDetailsArray = from a in OrderDetailsIQ.AsEnumerable()
        //                           select new Order
        //                           {
        //                               Customer_Name = a.Customer_Name,
        //                               Brand_Name = a.Brand_Name,
        //                               Model_Name = a.Model_Name,
        //                               Product_Type_Name = a.Product_Type_Name,
        //                               Order_No = a.Order_No,
        //                               Order_Date = a.Order_Date,
        //                               Count_Of_Vehicle = a.Count_Of_Vehicle

        //                           };
        //    IDataReader reader = ObjectReader.Create(UserDetailsArray, "Brand_Name", "Product_Type_Name", "Model_Name", "Order_No", "Order_Date", "Customer_Name", "Count_Of_Vehicle");
        //    dt1.Load(reader);
        //    int Count = UserDetailsArray.AsEnumerable().Count();
        //    using (var xlb = new ClosedXML.Excel.XLWorkbook())
        //    {
        //        var ws = xlb.Worksheets.Add("OrderDetails");

        //        // Populate the worksheet with data, customize as needed



        //        #region 
        //        List<string> ColNames = new List<string>();
        //        ColNames.Add(GetGlobalResourceObject(ExportDashboardOrderListObj.Culture.ToString(), "ResBrand").ToString());
        //        ColNames.Add(GetGlobalResourceObject(ExportDashboardOrderListObj.Culture.ToString(), "ResProductType").ToString());
        //        ColNames.Add(GetGlobalResourceObject(ExportDashboardOrderListObj.Culture.ToString(), "ResModel").ToString());
        //        ColNames.Add(GetGlobalResourceObject(ExportDashboardOrderListObj.Culture.ToString(), "ResOrder#").ToString());
        //        ColNames.Add(GetGlobalResourceObject(ExportDashboardOrderListObj.Culture.ToString(), "ResLastImportDate").ToString());
        //        ColNames.Add(GetGlobalResourceObject(ExportDashboardOrderListObj.Culture.ToString(), "ResCustomer").ToString());
        //        ColNames.Add(GetGlobalResourceObject(ExportDashboardOrderListObj.Culture.ToString(), "ResNumberOfVehicles").ToString());

        //        #endregion

        //        var rangeTitle = ws.Range(3, 1, 3, ColNames.Count);   // range for row 2, column 1 to row 2, column titles.Count
        //                                                              //var rangeTitle = ws.Range(2, 1, 2, ColNames.Count);   // range for row 2, column 1 to row 2, column titles.Count
        //                                                              // Need to add columns names with in rangeTitle 
        //        ws.Cell(1, 1).Value = GetGlobalResourceObject(ExportDashboardOrderListObj.Culture.ToString(), "ResOrderDetails").ToString();
        //        ws.Range(1, 1, 1, ColNames.Count).Merge().AddToNamed("Titles");
        //        for (int i = 0; i < ColNames.Count; i++)
        //        {
        //            var columnNumber = i + 1;
        //            ws.Cell(3, columnNumber).Value = ColNames[i];

        //        }
        //        rangeTitle.AddToNamed("Titles");

        //        // write data from row 4 onwards
        //        if (dt1 != null)
        //        {
        //            ws.Cell(4, 1).InsertData(dt1);
        //        }
        //        else
        //        {
        //            ws.Cell(4, 1).Value = "No data to show";
        //        }

        //        // styles
        //        var titlesStyle = xlb.Style;
        //        titlesStyle.Font.Bold = true;
        //        titlesStyle.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
        //        titlesStyle.Fill.BackgroundColor = XLColor.Gray;

        //        // style titles row
        //        xlb.NamedRanges.NamedRange("Titles").Ranges.Style = titlesStyle;
        //        ws.Columns().AdjustToContents(1, 60);

        //        //////////////////// Border to excel sheet /////////////////////////////////
        //        IXLRange range = ws.Range(ws.Cell(4, 1).Address, ws.Cell(Count + 3, ColNames.Count).Address);

        //        byte[] fileBytes;
        //        // Save the workbook to a MemoryStream
        //        using (var stream = new System.IO.MemoryStream())
        //        {
        //            xlb.SaveAs(stream);
        //            stream.Position = 0;
        //            fileBytes = stream.ToArray();
        //            HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
        //            response.Content = new ByteArrayContent(fileBytes);
        //            response.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        //            response.Content.Headers.ContentDisposition = new System.Net.Http.Headers.ContentDispositionHeaderValue("attachment")
        //            {
        //                FileName = "OrderDetails.xlsx"
        //            };
        //            return new JsonResult(response);

        //        }
        //    }
        //}
        //#endregion

        //#region // =============To Get Tree Data Assembly Level :: Siddesh.P =============
        ///// <summary>
        ///// Returning tree data details in HTML format for selected top level or assembly when click parts search with in top level if we select part from in that top level search in that catalog view when click on tree button   
        ///// </summary>
        //public static IActionResult GetTDataPartAssemblyLevel(string connstring, GetTDataPartAssemblyLevel GetTDataPartAssemblyLevelObj)
        //{
        //    Common Commo = new Common();
        //    string Culturevalue = string.Empty;
        //    Culturevalue = "Resource_" + GetTDataPartAssemblyLevelObj.Lang;
        //    string Conn = connstring;
        //    //string TableName = string.Empty;

        //    var jsonData = default(dynamic);
        //    try
        //    {
        //        using (SqlConnection conn = new SqlConnection(Conn))
        //        {
        //            using (SqlDataAdapter da = new SqlDataAdapter())
        //            {
        //                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
        //                {
        //                    conn.Open();
        //                }

        //                try
        //                {

        //                    if (GetTDataPartAssemblyLevelObj.sectionvalue != "")
        //                    {
        //                        GetTDataPartAssemblyLevelObj.Section = Convert.ToInt32(GetTDataPartAssemblyLevelObj.sectionvalue.ToString());
        //                    }

        //                    GetTDataPartAssemblyLevelObj.CatalogueSortOrder = 1;
        //                    GetTDataPartAssemblyLevelObj.TreeLevel = 6;

        //                    //Session["CatalogueSortOrder"] = 1;
        //                    //Session["TreeLevel"] = 6;
        //                    int Part_Assembly_ID = 0;
        //                    if (GetTDataPartAssemblyLevelObj.Part_Assembly != "" && GetTDataPartAssemblyLevelObj.Part_Assembly != null)
        //                    {
        //                        Part_Assembly_ID = Convert.ToInt32(GetTDataPartAssemblyLevelObj.Part_Assembly);
        //                    }
        //                    else
        //                    {
        //                        Part_Assembly_ID = Convert.ToInt32(GetTDataPartAssemblyLevelObj.Section);
        //                        //Part_Assembly_ID= Convert.ToInt32(Request.Params["sectionvalue"]);
        //                    }

        //                    //var AppPathImg = ConfigurationManager.AppSettings.Get("EPCGetImagePath") + "AssemblyServiceLink/";
        //                    int ISRange = -1;//Request.Params["FromSerialNumber"].ToString();
        //                    string FromSerialNumber = string.Empty;
        //                    string ToSerialNumber = string.Empty;
        //                    string Model = string.Empty;
        //                    var VehicleIC = default(dynamic);
        //                    int? intFromSerialNumber = 0;
        //                    int? intToSerialNumber = 0;

        //                    int Model_ID = Convert.ToInt32(GetTDataPartAssemblyLevelObj.Section);
        //                    //int Model_ID = Convert.ToInt32(Request.Params["sectionvalue"]);

        //                    //DK --Uncmmonet -- issue to check use this FOR NOVA ONLY --Get top Level ID if Selected Parent is section sub section or Order ONLY logic to bring TREE
        //                    //if (Session["BrandCode"].ToString().ToUpper().ToUpper() == "NOVA")
        //                    //{
        //                    //    using (SqlConnection connNovaPr = new SqlConnection(Session["DBCS"].ToString()))
        //                    //    {                       
        //                    //        if (connNovaPr.State == ConnectionState.Closed || connNovaPr.State == ConnectionState.Broken) { connNovaPr.Open(); }
        //                    //        DataTable dtToplevelID = new DataTable();
        //                    //        SqlCommand sqlComm = new SqlCommand(" EXEC GetTopIDSectionSubSectionOrder " + Part_Assembly_ID + "", connNovaPr);//passing PartID but in this parent assemblyid
        //                    //        dtToplevelID.Load(sqlComm.ExecuteReader());
        //                    //        int TopLevelID = 0;
        //                    //        if (dtToplevelID.Rows.Count > 0)
        //                    //        {
        //                    //            TopLevelID = (Convert.ToInt32(dtToplevelID.Rows[0]["TopLevelID"]));    
        //                    //            if(TopLevelID!=0)
        //                    //            {
        //                    //                Part_Assembly_ID = (Convert.ToInt32(dtToplevelID.Rows[0]["TopLevelID"]));
        //                    //                Session["SelectAssemblyBOM"] = "1";
        //                    //            }
        //                    //            else
        //                    //            {
        //                    //                Session["SelectAssemblyBOM"] = "0";
        //                    //            }
        //                    //        }
        //                    //    }
        //                    //}
        //                    //


        //                    if (GetTDataPartAssemblyLevelObj.DCTYPE == 2)
        //                    {
        //                        if (GetTDataPartAssemblyLevelObj.TopLevelSectionVIN.ToString() != "")
        //                        {
        //                            FromSerialNumber = GetTDataPartAssemblyLevelObj.TopLevelSectionVINShortCode.ToString();
        //                            ToSerialNumber = GetTDataPartAssemblyLevelObj.ToSerialNo.ToString();
        //                            Model = GetTDataPartAssemblyLevelObj.Model.ToString();
        //                            VehicleIC = Convert.ToInt32(GetTDataPartAssemblyLevelObj.TopLevelSectionVIN.ToString() == "" ? "0" : GetTDataPartAssemblyLevelObj.TopLevelSectionVIN.ToString());
        //                            VehicleIC = (VehicleIC == 0 ? DBNull.Value : VehicleIC);
        //                            Model_ID = VehicleIC;
        //                        }
        //                        else
        //                        {
        //                            GetTDataPartAssemblyLevelObj.TopLevelSectionVIN = "";
        //                            FromSerialNumber = GetTDataPartAssemblyLevelObj.FromSerialNumber.ToString();
        //                            ToSerialNumber = GetTDataPartAssemblyLevelObj.ToSerialNo.ToString();
        //                            Model = GetTDataPartAssemblyLevelObj.Model.ToString();
        //                            //VehicleIC = Convert.ToInt32(Request.Params["Vehicle"].ToString() == "" ? "0" : Request.Params["Vehicle"].ToString());
        //                            //VehicleIC = (VehicleIC == 0 ? DBNull.Value : VehicleIC);
        //                        }
        //                    }

        //                    if (FromSerialNumber != "" && ToSerialNumber != "")
        //                    {
        //                        intFromSerialNumber = Commo.GetVinShortNumber(FromSerialNumber, GetTDataPartAssemblyLevelObj.DCTYPE, GetTDataPartAssemblyLevelObj.UserID, connstring);


        //                        intToSerialNumber = Commo.GetVinShortNumber(ToSerialNumber, GetTDataPartAssemblyLevelObj.DCTYPE, GetTDataPartAssemblyLevelObj.UserID, connstring);
        //                        ISRange = 1;
        //                    }
        //                    else if (FromSerialNumber != "" || ToSerialNumber != "")
        //                    {
        //                        if (FromSerialNumber != "")
        //                        {
        //                            intFromSerialNumber = Commo.GetVinShortNumber(FromSerialNumber, GetTDataPartAssemblyLevelObj.DCTYPE, GetTDataPartAssemblyLevelObj.UserID, connstring);
        //                        }
        //                        else if (ToSerialNumber != "" && FromSerialNumber == "")
        //                        {
        //                            intFromSerialNumber = Commo.GetVinShortNumber(ToSerialNumber, GetTDataPartAssemblyLevelObj.DCTYPE, GetTDataPartAssemblyLevelObj.UserID, connstring);
        //                        }
        //                        ISRange = 0;
        //                    }
        //                    else if (FromSerialNumber == "" && ToSerialNumber == "" && Model != "")
        //                    {
        //                        intFromSerialNumber = 0;
        //                        intToSerialNumber = 0;
        //                        ISRange = 0;
        //                    }

        //                    int intRow = 0;
        //                    int intPCC_ID = 0; int intPrevPCC_ID = 0;
        //                    int intMA_ID = 0; int intPrevMA_ID = 0;
        //                    int intSAL1_ID = 0; int intPrevSAL1_ID = 0;
        //                    int intSAL2_ID = 0; int intPrevSAL2_ID = 0;
        //                    int intSAL3_ID = 0; int intPrevSAL3_ID = 0;
        //                    int intSAL4_ID = 0; int intPrevSAL4_ID = 0;
        //                    int intSAL5_ID = 0; //int intPrevSAL5_ID = 0;
        //                    int intSAL6_ID = 0; //int intPrevSAL6_ID = 0;
        //                    byte OrderType = Convert.ToByte(GetTDataPartAssemblyLevelObj.CatalogueSortOrder);

        //                    string intPCC_ID1 = string.Empty; string intPrevPCC_ID1 = string.Empty;
        //                    string intMA_ID1 = string.Empty; string intPrevMA_ID1 = string.Empty;
        //                    string intSAL1_ID1 = string.Empty; string intPrevSAL1_ID1 = string.Empty;
        //                    string intSAL2_ID1 = string.Empty; string intPrevSAL2_ID1 = string.Empty;
        //                    string intSAL3_ID1 = string.Empty; string intPrevSAL3_ID1 = string.Empty;
        //                    string intSAL4_ID1 = string.Empty; string intPrevSAL4_ID1 = string.Empty;
        //                    string intSAL5_ID1 = string.Empty; string intSAL6_ID1 = string.Empty;

        //                    bool Checknextid = false;
        //                    DataSet ds = new DataSet("TreeView");
        //                    using (SqlConnection conn1 = new SqlConnection(GetTDataPartAssemblyLevelObj.DBCS.ToString()))
        //                    {
        //                        SqlCommand sqlComm = null;
        //                        if (GetTDataPartAssemblyLevelObj.SelectAssemblyBOM.ToString() == "0")
        //                        {
        //                            sqlComm = new SqlCommand("Up_AssemblyBuild", conn1);
        //                            sqlComm.Parameters.AddWithValue("@Assembl_ID", Part_Assembly_ID);
        //                            sqlComm.CommandType = CommandType.StoredProcedure;
        //                            SqlDataAdapter da1 = new SqlDataAdapter();
        //                            da1.SelectCommand = sqlComm;
        //                            da1.Fill(ds);

        //                        }
        //                        else
        //                        {
        //                            var LanguageID = GetTDataPartAssemblyLevelObj.Langauge_ID;
        //                            string CurrentLanguage = GetTDataPartAssemblyLevelObj.Lang.ToString();
        //                            if (CurrentLanguage == "en")
        //                            {
        //                                LanguageID = 0;
        //                            }
        //                            int Catalogue_ID = 0;
        //                            using (SqlConnection connC = new SqlConnection(GetTDataPartAssemblyLevelObj.DBCS.ToString()))
        //                            {
        //                                var SqlQuery = "SELECT  Catalogue_ID FROM MA_Catalogue WHERE Catalogue_Part_Assembly_ID = " + Part_Assembly_ID;

        //                                if (connC.State == ConnectionState.Closed)
        //                                {
        //                                    connC.Open();
        //                                }
        //                                SqlCommand _CMD = new SqlCommand(SqlQuery, connC);

        //                                SqlDataReader DataReader = null;
        //                                DataReader = _CMD.ExecuteReader();

        //                                if (DataReader.HasRows)
        //                                {
        //                                    while (DataReader.Read())
        //                                    {
        //                                        Catalogue_ID = Convert.ToInt32(DataReader[0]);
        //                                    }
        //                                }
        //                            }
        //                            if (conn.State == ConnectionState.Closed)
        //                            {
        //                                conn.Open();
        //                            }
        //                            sqlComm = new SqlCommand("Sp_EPCTreeBuild", conn);
        //                            sqlComm.Parameters.AddWithValue("@Vehicle_ID", Catalogue_ID);
        //                            sqlComm.Parameters.AddWithValue("@IsRange", DBNull.Value);
        //                            sqlComm.Parameters.AddWithValue("@FromSN", DBNull.Value);
        //                            sqlComm.Parameters.AddWithValue("@ToSN", DBNull.Value);
        //                            sqlComm.Parameters.AddWithValue("@Model_IC", DBNull.Value);
        //                            sqlComm.Parameters.AddWithValue("@Assembly_ID", DBNull.Value);
        //                            sqlComm.Parameters.AddWithValue("@Language_ID", LanguageID);
        //                            sqlComm.CommandType = CommandType.StoredProcedure;
        //                            SqlDataAdapter da2 = new SqlDataAdapter();
        //                            da2.SelectCommand = sqlComm;
        //                            da2.Fill(ds);
        //                        }


        //                        if (GetTDataPartAssemblyLevelObj.DCTYPE == 2)
        //                        {
        //                            if (ISRange == 0 || ISRange == 1 && ISRange != -1)
        //                            {
        //                                sqlComm.Parameters.AddWithValue("@IsRange", ISRange);
        //                            }
        //                            else
        //                            {
        //                                sqlComm.Parameters.AddWithValue("@IsRange", DBNull.Value);
        //                            }

        //                            if (intFromSerialNumber == 0)
        //                            {
        //                                sqlComm.Parameters.AddWithValue("@FromSN", DBNull.Value);
        //                            }
        //                            else
        //                            {
        //                                sqlComm.Parameters.AddWithValue("@FromSN", intFromSerialNumber);
        //                            }

        //                            if (intToSerialNumber == 0)
        //                            {
        //                                sqlComm.Parameters.AddWithValue("@ToSN", DBNull.Value);
        //                            }
        //                            else
        //                            {
        //                                sqlComm.Parameters.AddWithValue("@ToSN", intToSerialNumber);
        //                            }

        //                            if (Model == "")
        //                            {
        //                                sqlComm.Parameters.AddWithValue("@Model_IC", DBNull.Value);
        //                            }
        //                            else
        //                            {
        //                                sqlComm.Parameters.AddWithValue("@Model_IC", Model);
        //                            }

        //                            //sqlComm.Parameters.AddWithValue("@Vehicle_IC", VehicleIC);
        //                        }


        //                    }

        //                    //string HTMLForTree = "&nbsp;&nbsp;<span id=\"IDBtnExpandTree\" style=\"color: blue; text-decoration: underline; font-size: 12px; cursor: pointer\"> " + HttpContext.GetGlobalResourceObject(Session["Culture"].ToString(), "ResExpandAll").ToString() + " </span>&nbsp;&nbsp;&nbsp;<span id=\"IDBtnCollapseTree\" style=\"color: blue; text-decoration: underline; font-size: 12px; cursor: pointer\"> " + HttpContext.GetGlobalResourceObject(Session["Culture"].ToString(), "ResCollapseAll").ToString() + " </span>&nbsp;&nbsp;";
        //                    //string HTMLForTree = "<div id=\"IdDivCollapsaExpTree\"><ul id=><li><img src=\"~/Content/Themes/VBNA_Theme_EPC/White Icons/Expand All.png\" /><li><li><img src=\"~/Content/Themes/VBNA_Theme_EPC/White Icons/Collapse All.png\" /></li></ul></div>";
        //                    string HTMLForTree = string.Empty;
        //                    if (GetTDataPartAssemblyLevelObj.DCTYPE == 2)
        //                    {
        //                        HTMLForTree = HTMLForTree + "<ul>";
        //                        //Start Loop Here
        //                        foreach (DataRow dr in ds.Tables[0].Rows)
        //                        {
        //                            //Catalogue Level
        //                            if (intRow == 0)
        //                            {
        //                                //HTMLForTree = HTMLForTree + "<li IsModel=\"1\" id=\"" + dr["Part_Assembly_ID"].ToString() + "\" class=\"folder expanded\"><b IsModel=\"1\" id=\"" + dr["Part_Assembly_ID"].ToString() + "\" style=\"font-weight: 100\">" + dr["Part_Assembly_Description"].ToString() + " </b>";
        //                                HTMLForTree = HTMLForTree + "<li IsModel=\"1\" id=\"" + dr["Part_Assembly_ID"].ToString() + "\" class=\"folder clsExtension expanded\"><b IsChapter=\"1\" id=\"" + dr["Part_Assembly_ID"].ToString() + "\" style=\"font-weight: 100\">" + System.Net.WebUtility.HtmlEncode(dr["Part_Assembly_Description"].ToString()) + " </b>";
        //                                if (dr["Sec_IC"].ToString() != "")
        //                                {
        //                                    HTMLForTree = HTMLForTree + "<ul>";
        //                                }
        //                            }

        //                            //Top Section Level
        //                            if (Int32.Parse(GetTDataPartAssemblyLevelObj.TreeLevel.ToString()) >= 1 && intPCC_ID != Convert.ToInt32(dr["Sec_IC"].ToString()) || intPCC_ID1 != dr["Sec_Desc"].ToString())
        //                            {
        //                                if (intSAL6_ID != 0 || intSAL6_ID1 != string.Empty)
        //                                {
        //                                    HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul></ul>";
        //                                }
        //                                else if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //                                {
        //                                    HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul>";
        //                                }
        //                                else if (intSAL4_ID != 0 || intSAL4_ID1 != string.Empty)
        //                                {
        //                                    HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul>";
        //                                }
        //                                else if (intSAL3_ID != 0 || intSAL3_ID1 != string.Empty)
        //                                {
        //                                    HTMLForTree = HTMLForTree + "</ul></ul></ul></ul>";
        //                                }
        //                                else if (intSAL2_ID != 0 || intSAL2_ID1 != string.Empty)
        //                                {
        //                                    HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                                }
        //                                else if (intSAL1_ID != 0 || intSAL1_ID1 != string.Empty)
        //                                {
        //                                    HTMLForTree = HTMLForTree + "</ul></ul>";
        //                                }
        //                                else if (intRow != 0 && Checknextid == false)
        //                                {
        //                                    HTMLForTree = HTMLForTree + "</ul>";
        //                                }
        //                                intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0; intSAL3_ID = 0; intSAL2_ID = 0; intSAL1_ID = 0; intMA_ID = 0;
        //                                intSAL6_ID1 = string.Empty; intSAL5_ID1 = string.Empty; intSAL4_ID1 = string.Empty;
        //                                intSAL3_ID1 = string.Empty; intSAL2_ID1 = string.Empty; intSAL1_ID1 = string.Empty; intMA_ID1 = string.Empty;

        //                                intPrevPCC_ID = intPCC_ID;
        //                                intPrevPCC_ID1 = intPCC_ID1;


        //                                if (dr["Sec_IC"].ToString() != "342392")
        //                                {
        //                                    HTMLForTree = HTMLForTree + "<li id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"1\" data-view=" + System.Net.WebUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " class=\"folder clsExtension\"><b id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"1\" data-view=" + System.Net.WebUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " style=\"font-weight: 100;\">" + System.Net.WebUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " </b>";
        //                                }
        //                                else if (dr["Sec_IC"].ToString() == "342392")
        //                                {
        //                                    if (GetTDataPartAssemblyLevelObj.DCTYPE == 2)
        //                                    {
        //                                        if (dr["Sec_No"].ToString().Contains("http://") || dr["Sec_No"].ToString().Contains("https://"))
        //                                        {
        //                                            HTMLForTree = HTMLForTree + "<li id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + System.Net.WebUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + System.Net.WebUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + System.Net.WebUtility.HtmlEncode(dr["Sec_No"].ToString()) + "'>" + System.Net.WebUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + "</a></b>";
        //                                        }
        //                                        else
        //                                        {
        //                                            string AWSPrefix = "Images/AssemblyServiceLink";
        //                                            HTMLForTree = HTMLForTree + "<li id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + System.Net.WebUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + System.Net.WebUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + (Commo.GetAWSObjectURL(System.Net.WebUtility.HtmlEncode(dr["Sec_No"].ToString()), AWSPrefix)) + "'>" + System.Net.WebUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + "</a></b>";
        //                                        }
        //                                    }
        //                                    else
        //                                    {
        //                                        string CurrentLanguage = GetTDataPartAssemblyLevelObj.Lang.ToString();
        //                                        string PathServS = dr["ServiceSectionLink"].ToString();
        //                                        string ReplaceLang = "";
        //                                        string FilenameForAWS = "";
        //                                        string Prefixpath = "";
        //                                        if (CurrentLanguage == "en")
        //                                        {
        //                                            ReplaceLang = PathServS.Replace("ENGLISH", "EN");
        //                                            ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                            string[] pathComponents = ReplaceLang.Split('/');
        //                                            Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                            FilenameForAWS = pathComponents[2];
        //                                        }
        //                                        else if (CurrentLanguage == "Fr")
        //                                        {
        //                                            ReplaceLang = PathServS.Replace("FRENCH", "FR");
        //                                            ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                            string[] pathComponents = ReplaceLang.Split('/');
        //                                            Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                            FilenameForAWS = pathComponents[2];
        //                                        }

        //                                        string AWSPrefix = "Service_Section_And_SubSection_Manuals/MANUE/ENTR/ENTR-LFS/" + Prefixpath;
        //                                        string FullServiceSectionPath = Commo.GetAWSObjectURL(FilenameForAWS.Trim(), AWSPrefix);

        //                                        HTMLForTree = HTMLForTree + "<li id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + System.Net.WebUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + System.Net.WebUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + FullServiceSectionPath + "'>" + System.Net.WebUtility.HtmlEncode(dr["Sec_Desc"].ToString()) + "</a></b>";
        //                                    }
        //                                }

        //                                //HTMLForTree = HTMLForTree + "<li id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"1\" data-view=" + dr["Sec_Desc"].ToString() + " class=\"folder clsExtension\"><b id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"1\" data-view=" + dr["Sec_Desc"].ToString() + " style=\"font-weight: 100;\">" + dr["Sec_Desc"].ToString() + " </b>";

        //                                intPCC_ID = Convert.ToInt32(dr["Sec_IC"].ToString());
        //                                intPCC_ID1 = dr["Sec_Desc"].ToString();
        //                            }

        //                            var jdf = dr["SSec_IC"].ToString();
        //                            //Sub Section / Main Assembly Level
        //                            if (jdf != "")
        //                            {
        //                                Checknextid = false;
        //                                if (Int32.Parse(GetTDataPartAssemblyLevelObj.TreeLevel.ToString()) >= 2 && intMA_ID != Convert.ToInt32(dr["SSec_IC"]) || intMA_ID1 != dr["SSec_Desc"].ToString())
        //                                //if (Int32.Parse(GetTDataPartAssemblyLevelObj.TreeLevel.ToString()) >= 2 && intMA_ID != Convert.ToInt32(dr["SSec_IC"].ToString())) //intMA_ID !=
        //                                {
        //                                    if (intSAL6_ID != 0 || intSAL6_ID1 != string.Empty)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul>";
        //                                    }
        //                                    else if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul>";
        //                                    }
        //                                    else if (intSAL4_ID != 0 || intSAL4_ID1 != string.Empty)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul></ul></ul>";
        //                                    }
        //                                    else if (intSAL3_ID != 0 || intSAL3_ID1 != string.Empty)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                                    }
        //                                    else if (intSAL2_ID != 0 || intSAL2_ID1 != string.Empty)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul>";
        //                                    }
        //                                    else if (intSAL1_ID != 0 || intSAL1_ID1 != string.Empty)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul>";
        //                                    }

        //                                    if (intPrevPCC_ID != intPCC_ID || intPrevPCC_ID1 != intPCC_ID1)
        //                                    {
        //                                        intPrevPCC_ID = intPCC_ID;
        //                                        intPrevPCC_ID1 = intPCC_ID1;
        //                                        HTMLForTree = HTMLForTree + "<ul>";
        //                                    }
        //                                    intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0; intSAL3_ID = 0; intSAL2_ID = 0; intSAL1_ID = 0;
        //                                    intSAL6_ID1 = string.Empty; intSAL5_ID1 = string.Empty; intSAL4_ID1 = string.Empty; intSAL3_ID1 = string.Empty;
        //                                    intSAL2_ID1 = string.Empty; intSAL1_ID1 = string.Empty;

        //                                    intPrevMA_ID = intMA_ID;
        //                                    intPrevMA_ID1 = intMA_ID1;


        //                                    if (dr["SSec_IC"].ToString() != "342392")
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "<li id=\"li" + dr["SSec_IC"].ToString() + "\" IsChapter='0' data-view=" + System.Net.WebUtility.HtmlEncode(dr["SSec_No"].ToString()) + " class=\"folder clsExtension\"><b id=\"" + dr["SSec_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + System.Net.WebUtility.HtmlEncode(dr["SSec_No"].ToString()) + " style=\"font-weight: 100;\">" + System.Net.WebUtility.HtmlEncode(dr["SSec_Desc"].ToString()) + " </b>";
        //                                    }
        //                                    else if (dr["SSec_IC"].ToString() == "342392")
        //                                    {
        //                                        if (GetTDataPartAssemblyLevelObj.DCTYPE == 2)
        //                                        {
        //                                            if (dr["SSec_No"].ToString().Contains("http://") || dr["SSec_No"].ToString().Contains("https://"))
        //                                            {
        //                                                HTMLForTree = HTMLForTree + "<li id=\"li" + dr["SSec_IC"].ToString() + "\" IsChapter='0' data-view=" + System.Net.WebUtility.HtmlEncode(dr["SSec_No"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["SSec_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + System.Net.WebUtility.HtmlEncode(dr["SSec_No"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + System.Net.WebUtility.HtmlEncode(dr["SSec_No"].ToString()) + "'>" + System.Net.WebUtility.HtmlEncode(dr["SSec_Desc"].ToString()) + "</a></b>";
        //                                            }
        //                                            else
        //                                            {
        //                                                string AWSPrefix = "Images/AssemblyServiceLink";
        //                                                HTMLForTree = HTMLForTree + "<li id=\"li" + dr["SSec_IC"].ToString() + "\" IsChapter='0' data-view=" + System.Net.WebUtility.HtmlEncode(dr["SSec_No"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["SSec_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + System.Net.WebUtility.HtmlEncode(dr["SSec_No"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + Commo.GetAWSObjectURL(System.Net.WebUtility.HtmlEncode(dr["SSec_No"].ToString()), AWSPrefix) + "'>" + System.Net.WebUtility.HtmlEncode(dr["SSec_Desc"].ToString()) + "</a></b>";
        //                                            }
        //                                        }
        //                                        else
        //                                        {
        //                                            string CurrentLanguage = GetTDataPartAssemblyLevelObj.Lang.ToString();
        //                                            string PathServS = dr["ServiceSectionLink"].ToString();
        //                                            string ReplaceLang = "";
        //                                            string FilenameForAWS = "";
        //                                            string Prefixpath = "";
        //                                            if (CurrentLanguage == "en")
        //                                            {
        //                                                ReplaceLang = PathServS.Replace("ENGLISH", "EN");
        //                                                ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                                string[] pathComponents = ReplaceLang.Split('/');
        //                                                Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                                FilenameForAWS = pathComponents[2];
        //                                            }
        //                                            else if (CurrentLanguage == "Fr")
        //                                            {
        //                                                ReplaceLang = PathServS.Replace("FRENCH", "FR");
        //                                                ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                                string[] pathComponents = ReplaceLang.Split('/');
        //                                                Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                                FilenameForAWS = pathComponents[2];
        //                                            }

        //                                            string AWSPrefix = "Service_Section_And_SubSection_Manuals/MANUE/ENTR/ENTR-LFS/" + Prefixpath;
        //                                            string FullServiceSectionPath = Commo.GetAWSObjectURL(FilenameForAWS.Trim(), AWSPrefix);
        //                                            HTMLForTree = HTMLForTree + "<li id=\"li" + dr["SSec_IC"].ToString() + "\" IsChapter='0' data-view=" + System.Net.WebUtility.HtmlEncode(dr["SSec_No"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["SSec_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + System.Net.WebUtility.HtmlEncode(dr["SSec_No"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + FullServiceSectionPath + "'>" + System.Net.WebUtility.HtmlEncode(dr["SSec_Desc"].ToString()) + "</a></b>";
        //                                        }
        //                                    }

        //                                    //HTMLForTree = HTMLForTree + "<li id=\"li" + dr["SSec_IC"].ToString() + "\" IsChapter='0' data-view=" + dr["SSec_No"].ToString() + " class=\"folder clsExtension\"><b id=\"" + dr["SSec_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + dr["SSec_No"].ToString() + " style=\"font-weight: 100;\">" + dr["SSec_Desc"].ToString() + " </b>";
        //                                    intMA_ID = Convert.ToInt32(dr["SSec_IC"].ToString());
        //                                    intMA_ID1 = dr["SSec_Desc"].ToString();
        //                                }
        //                                else
        //                                {

        //                                }
        //                            }
        //                            else
        //                            {
        //                                Checknextid = true;
        //                                HTMLForTree = HTMLForTree + "</li>";
        //                            }

        //                            //Sub Assembly Level 1
        //                            if (Int32.Parse(GetTDataPartAssemblyLevelObj.TreeLevel.ToString()) >= 3 && dr["L1_IC"].ToString() != "")
        //                            {
        //                                if (intSAL1_ID != Convert.ToInt32(dr["L1_IC"].ToString()) || intSAL1_ID1 != dr["L1_Desc"].ToString())
        //                                {
        //                                    if (intSAL6_ID != 0 || intSAL6_ID1 != string.Empty)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul>";
        //                                    }
        //                                    else if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul></ul></ul>";
        //                                    }
        //                                    else if (intSAL4_ID != 0 || intSAL4_ID1 != string.Empty)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                                    }
        //                                    else if (intSAL3_ID != 0 || intSAL3_ID1 != string.Empty)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul>";
        //                                    }
        //                                    else if (intSAL2_ID != 0 || intSAL2_ID1 != string.Empty)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul>";
        //                                    }

        //                                    if (intPrevMA_ID != intMA_ID || intPrevMA_ID1 != intMA_ID1)
        //                                    {
        //                                        intPrevMA_ID = intMA_ID;
        //                                        intPrevMA_ID1 = intMA_ID1;
        //                                        HTMLForTree = HTMLForTree + "<ul>";
        //                                    }
        //                                    intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0; intSAL3_ID = 0; intSAL2_ID = 0;
        //                                    intSAL6_ID1 = string.Empty; intSAL5_ID1 = string.Empty; intSAL4_ID1 = string.Empty;
        //                                    intSAL3_ID1 = string.Empty; intSAL2_ID1 = string.Empty;

        //                                    intPrevSAL1_ID = intSAL1_ID;
        //                                    intPrevSAL1_ID1 = intSAL1_ID1;
        //                                    if (dr["L1_IC"].ToString() != "342392")
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L1_IC"].ToString() + "\" IsChapter='0' data-view=" + System.Net.WebUtility.HtmlEncode(dr["L1_No"].ToString()) + " class=\"clsExtension\"><b id=\"" + dr["L1_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + System.Net.WebUtility.HtmlEncode(dr["L1_No"].ToString()) + " style=\"font-weight: 100;\">" + System.Net.WebUtility.HtmlEncode(dr["L1_Desc"].ToString()) + "</b>";
        //                                    }
        //                                    else if (dr["L1_IC"].ToString() == "342392")
        //                                    {
        //                                        if (GetTDataPartAssemblyLevelObj.DCTYPE == 2)
        //                                        {
        //                                            if (dr["L1_No"].ToString().Contains("http://") || dr["L1_No"].ToString().Contains("https://"))
        //                                            {
        //                                                HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L1_IC"].ToString() + "\" IsChapter='0' data-view=" + System.Net.WebUtility.HtmlEncode(dr["L1_No"].ToString()) + " class=\"clsExtension\"> <b class='far fa-dot-circle' style='color:red'></b> <b  id=\"" + dr["L1_IC"].ToString() + "\" IsChapter=\"0\"  data-view=" + System.Net.WebUtility.HtmlEncode(dr["L1_No"].ToString()) + " style=\"font-weight: 100;color:blue\" >" + "<a target='_blank' style='color:red;text-decoration:underline;' href='" + System.Net.WebUtility.HtmlEncode(dr["L1_No"].ToString()) + "'>" + System.Net.WebUtility.HtmlEncode(dr["L1_Desc"].ToString()) + "</a>" + "</b>";
        //                                            }
        //                                            else
        //                                            {
        //                                                string AWSPrefix = "Images/AssemblyServiceLink";
        //                                                HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L1_IC"].ToString() + "\" IsChapter='0' data-view=" + System.Net.WebUtility.HtmlEncode(dr["L1_No"].ToString()) + " class=\"clsExtension\"> <b class='far fa-dot-circle' style='color:red'></b> <b  id=\"" + dr["L1_IC"].ToString() + "\" IsChapter=\"0\"  data-view=" + System.Net.WebUtility.HtmlEncode(dr["L1_No"].ToString()) + " style=\"font-weight: 100;color:blue\" >" + "<a target='_blank' style='color:red;text-decoration:underline;' href='" + Commo.GetAWSObjectURL(System.Net.WebUtility.HtmlEncode(dr["L1_No"].ToString()), AWSPrefix) + "'>" + System.Net.WebUtility.HtmlEncode(dr["L1_Desc"].ToString()) + "</a>" + "</b>";
        //                                            }
        //                                        }
        //                                        else
        //                                        {
        //                                            string CurrentLanguage = GetTDataPartAssemblyLevelObj.Lang.ToString();
        //                                            string PathServS = dr["ServiceSectionLink"].ToString();
        //                                            string ReplaceLang = "";
        //                                            string FilenameForAWS = "";
        //                                            string Prefixpath = "";
        //                                            if (CurrentLanguage == "en")
        //                                            {
        //                                                ReplaceLang = PathServS.Replace("ENGLISH", "EN");
        //                                                ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                                string[] pathComponents = ReplaceLang.Split('/');
        //                                                Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                                FilenameForAWS = pathComponents[2];
        //                                            }
        //                                            else if (CurrentLanguage == "Fr")
        //                                            {
        //                                                ReplaceLang = PathServS.Replace("FRENCH", "FR");
        //                                                ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                                string[] pathComponents = ReplaceLang.Split('/');
        //                                                Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                                FilenameForAWS = pathComponents[2];
        //                                            }

        //                                            string AWSPrefix = "Service_Section_And_SubSection_Manuals/MANUE/ENTR/ENTR-LFS/" + Prefixpath;
        //                                            string FullServiceSectionPath = Commo.GetAWSObjectURL(FilenameForAWS.Trim(), AWSPrefix);

        //                                            HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L1_IC"].ToString() + "\" IsChapter='0' data-view=" + System.Net.WebUtility.HtmlEncode(dr["L1_No"].ToString()) + " class=\"clsExtension\"> <b class='far fa-dot-circle' style='color:red'></b> <b  id=\"" + dr["L1_IC"].ToString() + "\" IsChapter=\"0\"  data-view=" + System.Net.WebUtility.HtmlEncode(dr["L1_No"].ToString()) + " style=\"font-weight: 100;color:blue\" >" + "<a target='_blank' style='color:red;text-decoration:underline;' href='" + FullServiceSectionPath + "'>" + System.Net.WebUtility.HtmlEncode(dr["L1_Desc"].ToString()) + "</a>" + "</b>";
        //                                        }
        //                                    }
        //                                    intSAL1_ID = Convert.ToInt32(dr["L1_IC"].ToString());
        //                                    intSAL1_ID1 = dr["L1_Desc"].ToString();
        //                                }
        //                            }
        //                            //if (Int32.Parse(GetTDataPartAssemblyLevelObj.TreeLevel.ToString()) >= 3 && dr["L1_IC"].ToString() != "")
        //                            //{
        //                            //    if (intSAL1_ID != Convert.ToInt32(dr["L1_IC"].ToString()) || intSAL1_ID1 != dr["L1_Desc"].ToString())
        //                            //    {
        //                            //        if (intSAL6_ID != 0 || intSAL6_ID1 != string.Empty)
        //                            //        {
        //                            //            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul>";
        //                            //        }
        //                            //        else if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //                            //        {
        //                            //            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul>";
        //                            //        }
        //                            //        else if (intSAL4_ID != 0 || intSAL4_ID1 != string.Empty)
        //                            //        {
        //                            //            HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                            //        }
        //                            //        else if (intSAL3_ID != 0 || intSAL3_ID1 != string.Empty)
        //                            //        {
        //                            //            HTMLForTree = HTMLForTree + "</ul></ul>";
        //                            //        }
        //                            //        else if (intSAL2_ID != 0 || intSAL2_ID1 != string.Empty)
        //                            //        {
        //                            //            HTMLForTree = HTMLForTree + "</ul>";
        //                            //        }

        //                            //        if (intPrevMA_ID != intMA_ID || intPrevMA_ID1 != intMA_ID1)
        //                            //        {
        //                            //            intPrevMA_ID = intMA_ID;
        //                            //            intPrevMA_ID1 = intMA_ID1;
        //                            //            HTMLForTree = HTMLForTree + "<ul>";
        //                            //        }
        //                            //        intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0; intSAL3_ID = 0; intSAL2_ID = 0;
        //                            //        intSAL6_ID1 = string.Empty; intSAL5_ID1 = string.Empty; intSAL4_ID1 = string.Empty;
        //                            //        intSAL3_ID1 = string.Empty; intSAL2_ID1 = string.Empty;

        //                            //        intPrevSAL1_ID = intSAL1_ID;
        //                            //        intPrevSAL1_ID1 = intSAL1_ID1;
        //                            //        HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L1_IC"].ToString() + "\" IsChapter='0' data-view=" + dr["L1_No"].ToString() + " class=\"clsExtension\"><b id=\"" + dr["L1_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + dr["L1_No"].ToString() + " style=\"font-weight: 100;\">" + dr["L1_Desc"].ToString() + "</b>";
        //                            //        intSAL1_ID = Convert.ToInt32(dr["L1_IC"].ToString());
        //                            //        intSAL1_ID1 = dr["L1_Desc"].ToString();
        //                            //    }
        //                            //}
        //                            else
        //                            {
        //                                intSAL1_ID = 0;
        //                                intSAL1_ID1 = string.Empty;
        //                            }
        //                            //Sub Assembly Level 2
        //                            if (Int32.Parse(GetTDataPartAssemblyLevelObj.TreeLevel.ToString()) >= 4 && dr["L2_IC"].ToString() != "")
        //                            {
        //                                if (intSAL2_ID != Convert.ToInt32(dr["L2_IC"].ToString()) || intSAL2_ID1 != dr["L2_Desc"].ToString())
        //                                {
        //                                    if (intSAL6_ID != 0 || intSAL6_ID1 != string.Empty)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul></ul></ul>";
        //                                    }
        //                                    else if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                                    }
        //                                    else if (intSAL4_ID != 0 || intSAL4_ID1 != string.Empty)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul>";
        //                                    }
        //                                    else if (intSAL3_ID != 0 || intSAL3_ID1 != string.Empty)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul>";
        //                                    }

        //                                    if (intPrevSAL1_ID != intSAL1_ID || intPrevSAL1_ID1 != intSAL1_ID1)
        //                                    {
        //                                        intPrevSAL1_ID = intSAL1_ID;
        //                                        intPrevSAL1_ID1 = intSAL1_ID1;
        //                                        HTMLForTree = HTMLForTree + "<ul>";
        //                                    }
        //                                    intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0; intSAL3_ID = 0;
        //                                    intSAL6_ID1 = string.Empty; intSAL5_ID1 = string.Empty; intSAL4_ID1 = string.Empty; intSAL3_ID1 = string.Empty;
        //                                    intPrevSAL2_ID = intSAL2_ID;
        //                                    intPrevSAL2_ID1 = intSAL2_ID1;


        //                                    if (dr["L2_IC"].ToString() != "342392")
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L2_IC"].ToString() + "\" IsChapter='0' data-view=" + System.Net.WebUtility.HtmlEncode(dr["L2_No"].ToString()) + " class=\"clsExtension\"><b id=\"" + dr["L2_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + System.Net.WebUtility.HtmlEncode(dr["L2_No"].ToString()) + " style=\"font-weight: 100;\">" + System.Net.WebUtility.HtmlEncode(dr["L2_Desc"].ToString()) + "</b>";
        //                                    }
        //                                    else if (dr["L2_IC"].ToString() == "342392")
        //                                    {
        //                                        if (GetTDataPartAssemblyLevelObj.DCTYPE == 2)
        //                                        {
        //                                            if (dr["L2_No"].ToString().Contains("http://") || dr["L2_No"].ToString().Contains("https://"))
        //                                            {
        //                                                HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L2_IC"].ToString() + "\" IsChapter='0' data-view=" + System.Net.WebUtility.HtmlEncode(dr["L2_No"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["L2_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + System.Net.WebUtility.HtmlEncode(dr["L2_No"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + System.Net.WebUtility.HtmlEncode(dr["L2_No"].ToString()) + "'>" + System.Net.WebUtility.HtmlEncode(dr["L2_Desc"].ToString()) + "</a></b>";
        //                                            }
        //                                            else
        //                                            {
        //                                                string AWSPrefix = "Images/AssemblyServiceLink";
        //                                                HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L2_IC"].ToString() + "\" IsChapter='0' data-view=" + System.Net.WebUtility.HtmlEncode(dr["L2_No"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["L2_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + System.Net.WebUtility.HtmlEncode(dr["L2_No"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + Commo.GetAWSObjectURL(System.Net.WebUtility.HtmlEncode(dr["L2_No"].ToString()), AWSPrefix) + "'>" + System.Net.WebUtility.HtmlEncode(dr["L2_Desc"].ToString()) + "</a></b>";
        //                                            }
        //                                        }
        //                                        else
        //                                        {
        //                                            string CurrentLanguage = GetTDataPartAssemblyLevelObj.Lang.ToString();
        //                                            string PathServS = dr["ServiceSectionLink"].ToString();
        //                                            string ReplaceLang = "";
        //                                            string FilenameForAWS = "";
        //                                            string Prefixpath = "";
        //                                            if (CurrentLanguage == "en")
        //                                            {
        //                                                ReplaceLang = PathServS.Replace("ENGLISH", "EN");
        //                                                ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                                string[] pathComponents = ReplaceLang.Split('/');
        //                                                Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                                FilenameForAWS = pathComponents[2];
        //                                            }
        //                                            else if (CurrentLanguage == "Fr")
        //                                            {
        //                                                ReplaceLang = PathServS.Replace("FRENCH", "FR");
        //                                                ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                                string[] pathComponents = ReplaceLang.Split('/');
        //                                                Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                                FilenameForAWS = pathComponents[2];
        //                                            }

        //                                            string AWSPrefix = "Service_Section_And_SubSection_Manuals/MANUE/ENTR/ENTR-LFS/" + Prefixpath;
        //                                            string FullServiceSectionPath = Commo.GetAWSObjectURL(FilenameForAWS.Trim(), AWSPrefix);
        //                                            HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L2_IC"].ToString() + "\" IsChapter='0' data-view=" + System.Net.WebUtility.HtmlEncode(dr["L2_No"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["L2_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + System.Net.WebUtility.HtmlEncode(dr["L2_No"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + FullServiceSectionPath + "'>" + System.Net.WebUtility.HtmlEncode(dr["L2_Desc"].ToString()) + "</a></b>";
        //                                        }
        //                                    }

        //                                    //HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L2_IC"].ToString() + "\" IsChapter='0' data-view=" + dr["L2_No"].ToString() + " class=\"clsExtension\"><b id=\"" + dr["L2_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + dr["L2_No"].ToString() + " style=\"font-weight: 100;\">" + dr["L2_Desc"].ToString() + "</b>";
        //                                    intSAL2_ID = Convert.ToInt32(dr["L2_IC"].ToString());
        //                                    intSAL2_ID1 = dr["L2_Desc"].ToString();
        //                                }
        //                            }
        //                            else
        //                            {
        //                                intSAL2_ID = 0;
        //                                intSAL2_ID1 = string.Empty;
        //                            }
        //                            //Sub Assembly Level 3
        //                            if (Int32.Parse(GetTDataPartAssemblyLevelObj.TreeLevel.ToString()) >= 5 && dr["L3_IC"].ToString() != "")
        //                            {
        //                                if (intSAL3_ID != Convert.ToInt32(dr["L3_IC"].ToString()) || intSAL3_ID1 != dr["L3_Desc"].ToString())
        //                                {
        //                                    if (intSAL6_ID != 0 || intSAL6_ID1 != string.Empty)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                                    }
        //                                    else if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul>";
        //                                    }
        //                                    else if (intSAL4_ID != 0 || intSAL4_ID1 != string.Empty)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul>";
        //                                    }

        //                                    if (intPrevSAL2_ID != intSAL2_ID || intPrevSAL2_ID1 != intSAL2_ID1)
        //                                    {
        //                                        intPrevSAL2_ID = intSAL2_ID;
        //                                        intPrevSAL2_ID1 = intSAL2_ID1;
        //                                        HTMLForTree = HTMLForTree + "<ul>";
        //                                    }
        //                                    intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0;
        //                                    intSAL6_ID1 = string.Empty; intSAL5_ID1 = string.Empty; intSAL4_ID1 = string.Empty;
        //                                    intPrevSAL3_ID = intSAL3_ID;
        //                                    intPrevSAL3_ID1 = intSAL3_ID1;


        //                                    if (dr["L3_IC"].ToString() != "342392")
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L3_IC"].ToString() + "\" IsChapter='0' data-view=" + System.Net.WebUtility.HtmlEncode(dr["L3_No"].ToString()) + " class=\"clsExtension\"><b id=\"" + dr["L3_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + System.Net.WebUtility.HtmlEncode(dr["L3_No"].ToString()) + " style=\"font-weight: 100;\">" + System.Net.WebUtility.HtmlEncode(dr["L3_Desc"].ToString()) + "</b>";
        //                                    }
        //                                    else if (dr["L3_IC"].ToString() == "342392")
        //                                    {
        //                                        if (GetTDataPartAssemblyLevelObj.DCTYPE == 2)
        //                                        {
        //                                            if (dr["L3_No"].ToString().Contains("http://") || dr["L3_No"].ToString().Contains("https://"))
        //                                            {
        //                                                HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L3_IC"].ToString() + "\" IsChapter='0' data-view=" + System.Net.WebUtility.HtmlEncode(dr["L3_No"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["L3_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + System.Net.WebUtility.HtmlEncode(dr["L3_No"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + System.Net.WebUtility.HtmlEncode(dr["L2_No"].ToString()) + "'>" + System.Net.WebUtility.HtmlEncode(dr["L3_Desc"].ToString()) + "</a></b>";
        //                                            }
        //                                            else
        //                                            {
        //                                                string AWSPrefix = "Images/AssemblyServiceLink";
        //                                                HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L3_IC"].ToString() + "\" IsChapter='0' data-view=" + System.Net.WebUtility.HtmlEncode(dr["L3_No"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["L3_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + Commo.GetAWSObjectURL(System.Net.WebUtility.HtmlEncode(dr["L3_No"].ToString()), AWSPrefix) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + Commo.GetAWSObjectURL(System.Net.WebUtility.HtmlEncode(dr["L3_No"].ToString()), AWSPrefix) + "'>" + System.Net.WebUtility.HtmlEncode(dr["L3_Desc"].ToString()) + "</a></b>";
        //                                            }
        //                                        }
        //                                        else
        //                                        {
        //                                            string CurrentLanguage = GetTDataPartAssemblyLevelObj.Lang.ToString();
        //                                            string PathServS = dr["ServiceSectionLink"].ToString();
        //                                            string ReplaceLang = "";
        //                                            string FilenameForAWS = "";
        //                                            string Prefixpath = "";
        //                                            if (CurrentLanguage == "en")
        //                                            {
        //                                                ReplaceLang = PathServS.Replace("ENGLISH", "EN");
        //                                                ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                                string[] pathComponents = ReplaceLang.Split('/');
        //                                                Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                                FilenameForAWS = pathComponents[2];
        //                                            }
        //                                            else if (CurrentLanguage == "Fr")
        //                                            {
        //                                                ReplaceLang = PathServS.Replace("FRENCH", "FR");
        //                                                ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                                string[] pathComponents = ReplaceLang.Split('/');
        //                                                Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                                FilenameForAWS = pathComponents[2];
        //                                            }

        //                                            string AWSPrefix = "Service_Section_And_SubSection_Manuals/MANUE/ENTR/ENTR-LFS/" + Prefixpath;
        //                                            string FullServiceSectionPath = Commo.GetAWSObjectURL(FilenameForAWS.Trim(), AWSPrefix);
        //                                            HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L3_IC"].ToString() + "\" IsChapter='0' data-view=" + System.Net.WebUtility.HtmlEncode(dr["L3_No"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["L3_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + System.Net.WebUtility.HtmlEncode(dr["L3_No"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + FullServiceSectionPath + "'>" + System.Net.WebUtility.HtmlEncode(dr["L3_Desc"].ToString()) + "</a></b>";
        //                                            //HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L2_IC"].ToString() + "\" IsChapter='0' data-view=" + dr["L2_No"].ToString() + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["L2_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + dr["L2_No"].ToString() + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + FullServiceSectionPath + "'>" + dr["L2_Desc"].ToString() + "</a></b>";
        //                                        }
        //                                    }

        //                                    //HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L3_IC"].ToString() + "\" IsChapter='0' data-view=" + dr["L3_No"].ToString() + " class=\"clsExtension\"><b id=\"" + dr["L3_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + dr["L3_No"].ToString() + " style=\"font-weight: 100;\">" + dr["L3_Desc"].ToString() + "</b>";
        //                                    intSAL3_ID = Convert.ToInt32(dr["L3_IC"].ToString());
        //                                    intSAL3_ID1 = dr["L3_Desc"].ToString();
        //                                }
        //                            }
        //                            else
        //                            {
        //                                intSAL3_ID = 0;
        //                                intSAL3_ID1 = string.Empty;
        //                            }
        //                            //Sub Assembly Level 4
        //                            if (Int32.Parse(GetTDataPartAssemblyLevelObj.TreeLevel.ToString()) >= 6 && dr["L4_IC"].ToString() != "")
        //                            {
        //                                if (intSAL4_ID != Convert.ToInt32(dr["L4_IC"].ToString()) || intSAL4_ID1 != dr["L4_Desc"].ToString())
        //                                {
        //                                    if (intSAL6_ID != 0 || intSAL6_ID1 != string.Empty)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul>";
        //                                    }
        //                                    else if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul>";
        //                                    }

        //                                    if (intPrevSAL3_ID != intSAL3_ID || intPrevSAL3_ID1 != intSAL3_ID1)
        //                                    {
        //                                        intPrevSAL3_ID = intSAL3_ID;
        //                                        intPrevSAL3_ID1 = intSAL3_ID1;
        //                                        HTMLForTree = HTMLForTree + "<ul>";
        //                                    }
        //                                    intSAL6_ID = 0; intSAL5_ID = 0;
        //                                    intSAL6_ID1 = string.Empty; intSAL5_ID1 = string.Empty;

        //                                    intPrevSAL4_ID = intSAL4_ID;
        //                                    intPrevSAL4_ID1 = intSAL4_ID1;


        //                                    if (dr["L4_IC"].ToString() != "342392")
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L4_IC"].ToString() + "\" IsChapter='0' data-view=" + System.Net.WebUtility.HtmlEncode(dr["L4_No"].ToString()) + " class=\"clsExtension\"><b id=\"" + dr["L4_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + System.Net.WebUtility.HtmlEncode(dr["L4_No"].ToString()) + " style=\"font-weight: 100;\">" + System.Net.WebUtility.HtmlEncode(dr["L4_Desc"].ToString()) + "</b>";
        //                                    }
        //                                    else if (dr["L4_IC"].ToString() == "342392")
        //                                    {
        //                                        if (GetTDataPartAssemblyLevelObj.DCTYPE == 2)
        //                                        {
        //                                            if (dr["L4_No"].ToString().Contains("http://") || dr["L4_No"].ToString().Contains("https://"))
        //                                            {
        //                                                HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L4_IC"].ToString() + "\" IsChapter='0' data-view=" + System.Net.WebUtility.HtmlEncode(dr["L4_No"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["L4_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + System.Net.WebUtility.HtmlEncode(dr["L4_No"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + System.Net.WebUtility.HtmlEncode(dr["L4_No"].ToString()) + "'>" + System.Net.WebUtility.HtmlEncode(dr["L4_Desc"].ToString()) + "</a></b>";
        //                                            }
        //                                            else
        //                                            {
        //                                                string AWSPrefix = "Images/AssemblyServiceLink";
        //                                                HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L4_IC"].ToString() + "\" IsChapter='0' data-view=" + System.Net.WebUtility.HtmlEncode(dr["L4_No"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["L4_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + Commo.GetAWSObjectURL(System.Net.WebUtility.HtmlEncode(dr["L4_No"].ToString()), AWSPrefix) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + Commo.GetAWSObjectURL(System.Net.WebUtility.HtmlEncode(dr["L4_No"].ToString()), AWSPrefix) + "'>" + System.Net.WebUtility.HtmlEncode(dr["L4_Desc"].ToString()) + "</a></b>";
        //                                            }
        //                                        }
        //                                        else
        //                                        {
        //                                            string CurrentLanguage = GetTDataPartAssemblyLevelObj.Lang.ToString();
        //                                            string PathServS = dr["ServiceSectionLink"].ToString();
        //                                            string ReplaceLang = "";
        //                                            string FilenameForAWS = "";
        //                                            string Prefixpath = "";
        //                                            if (CurrentLanguage == "en")
        //                                            {
        //                                                ReplaceLang = PathServS.Replace("ENGLISH", "EN");
        //                                                ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                                string[] pathComponents = ReplaceLang.Split('/');
        //                                                Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                                FilenameForAWS = pathComponents[2];
        //                                            }
        //                                            else if (CurrentLanguage == "Fr")
        //                                            {
        //                                                ReplaceLang = PathServS.Replace("FRENCH", "FR");
        //                                                ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                                string[] pathComponents = ReplaceLang.Split('/');
        //                                                Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                                FilenameForAWS = pathComponents[2];
        //                                            }

        //                                            string AWSPrefix = "Service_Section_And_SubSection_Manuals/MANUE/ENTR/ENTR-LFS/" + Prefixpath;
        //                                            string FullServiceSectionPath = Commo.GetAWSObjectURL(FilenameForAWS.Trim(), AWSPrefix);
        //                                            HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L4_IC"].ToString() + "\" IsChapter='0' data-view=" + System.Net.WebUtility.HtmlEncode(dr["L4_No"].ToString()) + " class=\"clsExtension\"><b class='far fa-dot-circle' style='color:red'></b> <b id=\"" + dr["L4_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + System.Net.WebUtility.HtmlEncode(dr["L4_No"].ToString()) + " style=\"font-weight: 100;\"><a target='_blank' style='color:red;text-decoration:underline;' href='" + FullServiceSectionPath + "'>" + System.Net.WebUtility.HtmlEncode(dr["L4_Desc"].ToString()) + "</a></b>";
        //                                        }
        //                                    }


        //                                    //HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L4_IC"].ToString() + "\" IsChapter='0' data-view=" + dr["L4_No"].ToString() + " class=\"clsExtension\"><b id=\"" + dr["L4_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + dr["L4_No"].ToString() + " style=\"font-weight: 100;\">" + dr["L4_Desc"].ToString() + "</b>";
        //                                    intSAL4_ID = Convert.ToInt32(dr["L4_IC"].ToString());
        //                                    intSAL4_ID1 = dr["L4_Desc"].ToString();
        //                                }
        //                            }
        //                            else
        //                            {
        //                                intSAL4_ID = 0;
        //                                intSAL4_ID1 = string.Empty;
        //                            }
        //                            intRow = intRow + 1;
        //                        }

        //                        //loop end
        //                        if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //                        {
        //                            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul></ul></ul></ul>";
        //                        }
        //                        else if (intSAL5_ID != 0 || intSAL5_ID1 != string.Empty)
        //                        {
        //                            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul></ul></ul>";
        //                        }
        //                        else if (intSAL4_ID != 0 || intSAL4_ID1 != string.Empty)
        //                        {
        //                            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul></ul>";
        //                        }
        //                        else if (intSAL3_ID != 0 || intSAL3_ID1 != string.Empty)
        //                        {
        //                            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul>";
        //                        }
        //                        else if (intSAL2_ID != 0 || intSAL2_ID1 != string.Empty)
        //                        {
        //                            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul>";
        //                        }
        //                        else if (intSAL1_ID != 0 || intSAL1_ID1 != string.Empty)
        //                        {
        //                            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul>";
        //                        }
        //                        else if (intMA_ID != 0 || intMA_ID1 != string.Empty)
        //                        {
        //                            HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                        }
        //                        else
        //                        {
        //                            HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                        }
        //                        intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0; intSAL3_ID = 0; intSAL2_ID = 0; intSAL1_ID = 0; intMA_ID = 0; intPCC_ID = 0;
        //                        intSAL6_ID1 = string.Empty; intSAL5_ID1 = string.Empty; intSAL4_ID1 = string.Empty; intSAL3_ID1 = string.Empty;
        //                        intSAL2_ID1 = string.Empty; intSAL1_ID1 = string.Empty; intMA_ID1 = string.Empty; intPCC_ID1 = string.Empty;
        //                        jsonData = new
        //                        {
        //                            HTMLForTree
        //                        };
        //                    }
        //                    else
        //                    {
        //                        //string HTMLForTree = "&nbsp;&nbsp;<span id=\"IDBtnExpandTree\" style=\"color: blue; text-decoration: underline; font-size: 12px; cursor: pointer\"> " + HttpContext.GetGlobalResourceObject(Session["Culture"].ToString(), "ResExpandAll").ToString() + " </span>&nbsp;&nbsp;&nbsp;<span id=\"IDBtnCollapseTree\" style=\"color: blue; text-decoration: underline; font-size: 12px; cursor: pointer\"> " + HttpContext.GetGlobalResourceObject(Session["Culture"].ToString(), "ResCollapseAll").ToString() + " </span>&nbsp;&nbsp;";
        //                        HTMLForTree = HTMLForTree + "<ul>";
        //                        //Start Loop Here
        //                        foreach (DataRow dr in ds.Tables[0].Rows)
        //                        {
        //                            //Catalogue Level
        //                            if (GetTDataPartAssemblyLevelObj.SelectAssemblyBOM.ToString() != "0")
        //                            {
        //                                if (intRow == 0)
        //                                {
        //                                    HTMLForTree = HTMLForTree + "<li IsModel=\"1\" id=\"" + dr["Part_Assembly_ID"].ToString() + "\" class=\"folder expanded\"><b IsModel=\"1\" id=\"" + dr["Part_Assembly_ID"].ToString() + "\" style=\"font-weight: 100\">" + dr["Part_Assembly_Description"].ToString() + " </b>";
        //                                    if (dr["Sec_IC"].ToString() != "")
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "<ul>";
        //                                    }
        //                                }
        //                            }
        //                            //Top Section Level
        //                            if (Int32.Parse(GetTDataPartAssemblyLevelObj.TreeLevel.ToString()) >= 1 && intPCC_ID != Convert.ToInt32(dr["Sec_IC"].ToString()))
        //                            {
        //                                if (intSAL6_ID != 0)
        //                                {
        //                                    HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul></ul>";
        //                                }
        //                                else if (intSAL5_ID != 0)
        //                                {
        //                                    HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul>";
        //                                }
        //                                else if (intSAL4_ID != 0)
        //                                {
        //                                    HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul>";
        //                                }
        //                                else if (intSAL3_ID != 0)
        //                                {
        //                                    HTMLForTree = HTMLForTree + "</ul></ul></ul></ul>";
        //                                }
        //                                else if (intSAL2_ID != 0)
        //                                {
        //                                    HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                                }
        //                                else if (intSAL1_ID != 0)
        //                                {
        //                                    HTMLForTree = HTMLForTree + "</ul></ul>";
        //                                }
        //                                else if (intRow != 0)
        //                                {
        //                                    HTMLForTree = HTMLForTree + "</ul>";
        //                                }
        //                                intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0; intSAL3_ID = 0; intSAL2_ID = 0; intSAL1_ID = 0; intMA_ID = 0;
        //                                intPrevPCC_ID = intPCC_ID;
        //                                HTMLForTree = HTMLForTree + "<li id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"1\" data-view = " + dr["Sec_No"].ToString() + " class=\"folder clsExtension\"><b id=\"" + dr["Sec_IC"].ToString() + "\" IsChapter=\"1\" data-view = " + dr["Sec_No"].ToString() + " style=\"font-weight: 100;\">" + dr["Sec_Desc"].ToString() + " </b>";
        //                                intPCC_ID = Convert.ToInt32(dr["Sec_IC"].ToString());
        //                            }
        //                            //Sub Section / Main Assembly Level
        //                            if (Int32.Parse(GetTDataPartAssemblyLevelObj.TreeLevel.ToString()) >= 2 && intMA_ID != Convert.ToInt32(dr["SSec_IC"] == DBNull.Value ? 0 : (int)dr["SSec_IC"]))
        //                            {
        //                                if (intSAL6_ID != 0)
        //                                {
        //                                    HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul>";
        //                                }
        //                                else if (intSAL5_ID != 0)
        //                                {
        //                                    HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul>";
        //                                }
        //                                else if (intSAL4_ID != 0)
        //                                {
        //                                    HTMLForTree = HTMLForTree + "</ul></ul></ul></ul>";
        //                                }
        //                                else if (intSAL3_ID != 0)
        //                                {
        //                                    HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                                }
        //                                else if (intSAL2_ID != 0)
        //                                {
        //                                    HTMLForTree = HTMLForTree + "</ul></ul>";
        //                                }
        //                                else if (intSAL1_ID != 0)
        //                                {
        //                                    HTMLForTree = HTMLForTree + "</ul>";
        //                                }

        //                                if (intPrevPCC_ID != intPCC_ID)
        //                                {
        //                                    intPrevPCC_ID = intPCC_ID;
        //                                    HTMLForTree = HTMLForTree + "<ul>";
        //                                }
        //                                intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0; intSAL3_ID = 0; intSAL2_ID = 0; intSAL1_ID = 0;
        //                                intPrevMA_ID = intMA_ID;
        //                                HTMLForTree = HTMLForTree + "<li id=\"li" + dr["SSec_IC"].ToString() + "\" IsChapter='0' data-view = " + dr["SSec_No"].ToString() + " class=\"folder clsExtension\"><b id=\"" + dr["SSec_IC"].ToString() + "\" IsChapter=\"0\" data-view = " + dr["SSec_No"].ToString() + " style=\"font-weight: 100;\">" + dr["SSec_Desc"].ToString() + " </b>";
        //                                intMA_ID = Convert.ToInt32(dr["SSec_IC"].ToString());
        //                            }
        //                            //Sub Assembly Level 1
        //                            if (Int32.Parse(GetTDataPartAssemblyLevelObj.TreeLevel.ToString()) >= 3 && dr["L1_IC"].ToString() != "")
        //                            {
        //                                //if (intSAL1_ID != Convert.ToInt32(dr["L1_IC"].ToString()))
        //                                if (intSAL1_ID != Convert.ToInt32(dr["L1_IC"].ToString()) || intSAL1_ID1 != dr["L1_Desc"].ToString())
        //                                {
        //                                    if (intSAL6_ID != 0)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul>";
        //                                    }
        //                                    else if (intSAL5_ID != 0)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul></ul></ul>";
        //                                    }
        //                                    else if (intSAL4_ID != 0)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                                    }
        //                                    else if (intSAL3_ID != 0)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul>";
        //                                    }
        //                                    else if (intSAL2_ID != 0)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul>";
        //                                    }

        //                                    if (intPrevMA_ID != intMA_ID)
        //                                    {
        //                                        intPrevMA_ID = intMA_ID;
        //                                        HTMLForTree = HTMLForTree + "<ul>";
        //                                    }
        //                                    intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0; intSAL3_ID = 0; intSAL2_ID = 0;
        //                                    intSAL6_ID1 = string.Empty; intSAL5_ID1 = string.Empty; intSAL4_ID1 = string.Empty;
        //                                    intSAL3_ID1 = string.Empty; intSAL2_ID1 = string.Empty;

        //                                    intPrevSAL1_ID = intSAL1_ID;
        //                                    intPrevSAL1_ID1 = intSAL1_ID1;
        //                                    if (dr["L1_IC"].ToString() != "342392")
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L1_IC"].ToString() + "\" IsChapter='0' data-view=" + dr["L1_No"].ToString() + " class=\"clsExtension\"><b id=\"" + dr["L1_IC"].ToString() + "\" IsChapter=\"0\" data-view=" + dr["L1_No"].ToString() + " style=\"font-weight: 100;\">" + dr["L1_Desc"].ToString() + "</b>";
        //                                    }
        //                                    else if (dr["L1_IC"].ToString() == "342392")
        //                                    {
        //                                        string CurrentLanguage = GetTDataPartAssemblyLevelObj.Lang.ToString();
        //                                        string PathServS = dr["ServiceSectionLink"].ToString();
        //                                        string ReplaceLang = "";
        //                                        string FilenameForAWS = "";
        //                                        string Prefixpath = "";
        //                                        if (CurrentLanguage == "en")
        //                                        {
        //                                            ReplaceLang = PathServS.Replace("ENGLISH", "EN");
        //                                            ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                            string[] pathComponents = ReplaceLang.Split('/');
        //                                            Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                            FilenameForAWS = pathComponents[2];
        //                                        }
        //                                        else if (CurrentLanguage == "Fr")
        //                                        {
        //                                            ReplaceLang = PathServS.Replace("FRENCH", "FR");
        //                                            ReplaceLang = ReplaceLang.Replace('\\', '/');
        //                                            string[] pathComponents = ReplaceLang.Split('/');
        //                                            Prefixpath = pathComponents[0] + '/' + pathComponents[1];
        //                                            FilenameForAWS = pathComponents[2];
        //                                        }

        //                                        string AWSPrefix = "Service_Section_And_SubSection_Manuals/MANUE/ENTR/ENTR-LFS/" + Prefixpath;
        //                                        string FullServiceSectionPath = Commo.GetAWSObjectURL(FilenameForAWS.Trim(), AWSPrefix);

        //                                        HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L1_IC"].ToString() + "\" IsChapter='0' data-view=" + dr["L1_No"].ToString() + " class=\"clsExtension\"> <b class='far fa-dot-circle' style='color:red'></b> <b  id=\"" + dr["L1_IC"].ToString() + "\" IsChapter=\"0\"  data-view=" + dr["L1_No"].ToString() + " style=\"font-weight: 100;color:blue\" >" + "<a target='_blank' style='color:red;text-decoration:underline;' href='" + FullServiceSectionPath + "'>" + dr["L1_Desc"].ToString() + "</a>" + "</b>";

        //                                    }
        //                                    intSAL1_ID = Convert.ToInt32(dr["L1_IC"].ToString());
        //                                    intSAL1_ID1 = dr["L1_Desc"].ToString();
        //                                }
        //                            }
        //                            else
        //                            {
        //                                intSAL1_ID = 0;
        //                                intSAL1_ID1 = string.Empty;
        //                            }
        //                            //Sub Assembly Level 2
        //                            if (Int32.Parse(GetTDataPartAssemblyLevelObj.TreeLevel.ToString()) >= 4 && dr["L2_IC"].ToString() != "")
        //                            {
        //                                if (intSAL2_ID != Convert.ToInt32(dr["L2_IC"].ToString()))
        //                                {
        //                                    if (intSAL6_ID != 0)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul></ul></ul>";
        //                                    }
        //                                    else if (intSAL5_ID != 0)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                                    }
        //                                    else if (intSAL4_ID != 0)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul>";
        //                                    }
        //                                    else if (intSAL3_ID != 0)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul>";
        //                                    }

        //                                    if (intPrevSAL1_ID != intSAL1_ID)
        //                                    {
        //                                        intPrevSAL1_ID = intSAL1_ID;
        //                                        HTMLForTree = HTMLForTree + "<ul>";
        //                                    }
        //                                    intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0; intSAL3_ID = 0;
        //                                    intPrevSAL2_ID = intSAL2_ID;
        //                                    HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L2_IC"].ToString() + "\" IsChapter='0' data-view = " + dr["L2_No"].ToString() + " class=\"clsExtension\"><b id=\"" + dr["L2_IC"].ToString() + "\" IsChapter=\"0\" data-view = " + dr["L2_No"].ToString() + " style=\"font-weight: 100;\">" + dr["L2_Desc"].ToString() + "</b>";
        //                                    intSAL2_ID = Convert.ToInt32(dr["L2_IC"].ToString());
        //                                }
        //                            }
        //                            else
        //                            {
        //                                intSAL2_ID = 0;
        //                            }
        //                            //Sub Assembly Level 3
        //                            if (Int32.Parse(GetTDataPartAssemblyLevelObj.TreeLevel.ToString()) >= 5 && dr["L3_IC"].ToString() != "")
        //                            {
        //                                if (intSAL3_ID != Convert.ToInt32(dr["L3_IC"].ToString()))
        //                                {
        //                                    if (intSAL6_ID != 0)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                                    }
        //                                    else if (intSAL5_ID != 0)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul>";
        //                                    }
        //                                    else if (intSAL4_ID != 0)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul>";
        //                                    }

        //                                    if (intPrevSAL2_ID != intSAL2_ID)
        //                                    {
        //                                        intPrevSAL2_ID = intSAL2_ID;
        //                                        HTMLForTree = HTMLForTree + "<ul>";
        //                                    }
        //                                    intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0;
        //                                    intPrevSAL3_ID = intSAL3_ID;
        //                                    HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L3_IC"].ToString() + "\" IsChapter='0' data-view = " + dr["L3_No"].ToString() + " class=\"clsExtension\"><b id=\"" + dr["L3_IC"].ToString() + "\" IsChapter=\"0\" data-view = " + dr["L3_No"].ToString() + " style=\"font-weight: 100;\">" + dr["L3_Desc"].ToString() + "</b>";
        //                                    intSAL3_ID = Convert.ToInt32(dr["L3_IC"].ToString());
        //                                }
        //                            }
        //                            else
        //                            {
        //                                intSAL3_ID = 0;
        //                            }
        //                            //Sub Assembly Level 4
        //                            if (Int32.Parse(GetTDataPartAssemblyLevelObj.TreeLevel.ToString()) >= 6 && dr["L4_IC"].ToString() != "")
        //                            {
        //                                if (intSAL4_ID != Convert.ToInt32(dr["L4_IC"].ToString()))
        //                                {
        //                                    if (intSAL6_ID != 0)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul></ul>";
        //                                    }
        //                                    else if (intSAL5_ID != 0)
        //                                    {
        //                                        HTMLForTree = HTMLForTree + "</ul>";
        //                                    }

        //                                    if (intPrevSAL3_ID != intSAL3_ID)
        //                                    {
        //                                        intPrevSAL3_ID = intSAL3_ID;
        //                                        HTMLForTree = HTMLForTree + "<ul>";
        //                                    }
        //                                    intSAL6_ID = 0; intSAL5_ID = 0;
        //                                    intPrevSAL4_ID = intSAL4_ID;
        //                                    HTMLForTree = HTMLForTree + "<li id=\"li" + dr["L4_IC"].ToString() + "\" IsChapter='0' data-view = " + dr["L4_No"].ToString() + " class=\"clsExtension\"><b id=\"" + dr["L4_IC"].ToString() + "\" IsChapter=\"0\" data-view = " + dr["L4_No"].ToString() + " style=\"font-weight: 100;\">" + dr["L4_Desc"].ToString() + "</b>";
        //                                    intSAL4_ID = Convert.ToInt32(dr["L4_IC"].ToString());
        //                                }
        //                            }
        //                            else
        //                            {
        //                                intSAL4_ID = 0;
        //                            }
        //                            intRow = intRow + 1;
        //                        }
        //                        if (intSAL5_ID != 0)
        //                        {
        //                            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul></ul></ul></ul>";
        //                        }
        //                        else if (intSAL5_ID != 0)
        //                        {
        //                            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul></ul></ul>";
        //                        }
        //                        else if (intSAL4_ID != 0)
        //                        {
        //                            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul></ul>";
        //                        }
        //                        else if (intSAL3_ID != 0)
        //                        {
        //                            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul></ul>";
        //                        }
        //                        else if (intSAL2_ID != 0)
        //                        {
        //                            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul></ul>";
        //                        }
        //                        else if (intSAL1_ID != 0)
        //                        {
        //                            HTMLForTree = HTMLForTree + "</ul></ul></ul></ul>";
        //                        }
        //                        else if (intMA_ID != 0)
        //                        {
        //                            HTMLForTree = HTMLForTree + "</ul></ul></ul>";
        //                        }
        //                        else
        //                        {
        //                            HTMLForTree = HTMLForTree + "</ul></ul>";
        //                        }
        //                        intSAL6_ID = 0; intSAL5_ID = 0; intSAL4_ID = 0; intSAL3_ID = 0; intSAL2_ID = 0; intSAL1_ID = 0; intMA_ID = 0; intPCC_ID = 0;
        //                        jsonData = new
        //                        {
        //                            HTMLForTree,
        //                            //ModelImageData = GetImageForModel(Model_ID)
        //                        };
        //                    }
        //                    jsonData = new
        //                    {
        //                        CatalogueSortOrder = 1,
        //                        TreeLevel = 6
        //                    };

        //                }

        //                catch (Exception e)
        //                {
        //                    //LogHelper.WriteLog(e);
        //                    jsonData = new
        //                    {
        //                        FirstOccuranceAssembly_ID = 0,
        //                        HTMLForTree = ""

        //                    };
        //                }

        //            }
        //        }
        //    }
        //    catch (Exception e)
        //    {
        //        var Request = new System.Net.Http.HttpRequestMessage();
        //        ExceptionLogger.ErrorLog(e, Request, Convert.ToInt32(GetTDataPartAssemblyLevelObj.UserID), connstring);
        //    }
        //    finally
        //    {
        //        SqlConnection.ClearAllPools();
        //    }

        //    return new JsonResult(jsonData);

        //}

        //#endregion

        //#region Service link attachment used in assemblies - Siddesh.P :23-Jan-24
        ///// <summary>
        ///// Returning List of Assemblies the servicelink attachment used
        ///// </summary>

        //public static IActionResult GetServiceLinkAttachmentsUsedInAssemblies(string connstring, GetServiceLinkAttachmentsUsedInAssemblies GetServiceLinkAttachmentsUsedInAssembliesobj)
        //{
        //    string Culturevalue = string.Empty;
        //    Culturevalue = "Resource_" + GetServiceLinkAttachmentsUsedInAssembliesobj.Lang;
        //    string Conn = connstring;
        //    //string TableName = string.Empty;

        //    var JsonReader = default(dynamic);
        //    try
        //    {
        //        using (SqlConnection conn = new SqlConnection(Conn))
        //        {
        //            using (SqlDataAdapter da = new SqlDataAdapter())
        //            {
        //                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
        //                {
        //                    conn.Open();
        //                }

        //                List<PartUsedAssembly> PartUsedAssemliesList = new List<PartUsedAssembly>();
        //                string ServAttachID = GetServiceLinkAttachmentsUsedInAssembliesobj.ServiceAttachID.ToString();
        //                try
        //                {
        //                    string SPName = string.Empty;
        //                    if (GetServiceLinkAttachmentsUsedInAssembliesobj.DCTYPE == 1) { SPName = "Usp_Sel_ServiceLink_Used_In_Assembly_List"; }
        //                    else { SPName = "Usp_Sel_ServiceLink_Used_In_Assembly_List_VIN"; }
        //                    SqlDataReader DataReader = null;
        //                    SqlCommand sqlComm = new SqlCommand(SPName, conn);
        //                    sqlComm.Parameters.AddWithValue("@FILENAME", ServAttachID);
        //                    sqlComm.CommandType = CommandType.StoredProcedure;
        //                    if (conn.State == ConnectionState.Closed)
        //                    {
        //                        conn.Open();
        //                    }
        //                    DataReader = sqlComm.ExecuteReader();

        //                    while (DataReader.Read())
        //                    {
        //                        PartUsedAssembly SingleRecord = new PartUsedAssembly();
        //                        SingleRecord.Part_Assembly_ID = int.Parse(DataReader["Part_Assembly_ID"].ToString());
        //                        SingleRecord.MFG_Code = DataReader["MFG_Code"].ToString();
        //                        SingleRecord.Part_Assembly_No = DataReader["Part_Assembly_No"].ToString();
        //                        SingleRecord.Item_No = DataReader["Item_No"].ToString();
        //                        SingleRecord.Qty = DataReader["Qty"].ToString();
        //                        SingleRecord.Part_Assembly_Description = DataReader["Part_Assembly_Description"].ToString();
        //                        SingleRecord.Part_Assembly_IsAssembly = DataReader["Part_Assembly_IsAssembly"].ToString();
        //                        PartUsedAssemliesList.Add(SingleRecord);
        //                    }
        //                }
        //                catch (Exception ex)
        //                {
        //                    var Request = new System.Net.Http.HttpRequestMessage();
        //                    ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(GetServiceLinkAttachmentsUsedInAssembliesobj.UserID), "");
        //                }
        //                finally
        //                {
        //                    SqlConnection.ClearAllPools();
        //                }
        //                JsonReader = new
        //                {
        //                    Count = PartUsedAssemliesList.Count,
        //                    AssemblyData = PartUsedAssemliesList
        //                };

        //            }
        //        }
        //    }
        //    catch (Exception e)
        //    {
        //        var Request = new System.Net.Http.HttpRequestMessage();
        //        ExceptionLogger.ErrorLog(e, Request, Convert.ToInt32(GetServiceLinkAttachmentsUsedInAssembliesobj.UserID), connstring);
        //    }
        //    finally
        //    {
        //        SqlConnection.ClearAllPools();
        //    }

        //    return new JsonResult(JsonReader);

        //}
        //#endregion

        //#region Service link attachment used in orders -Siddesh.P :23-Jan-24
        ///// <summary>
        ///// Returning List of orders the servicelink attachment used
        ///// </summary>

        //public static IActionResult GetServiceLinkAttachmentsUsedInOrder(string connstring, GetServiceLinkAttachmentsUsedInOrder GetServiceLinkAttachmentsUsedInOrderObj)
        //{

        //    string OrderNumber = GetServiceLinkAttachmentsUsedInOrderObj.OrderNumber;
        //    int PartNumber = GetServiceLinkAttachmentsUsedInOrderObj.PartNumber;
        //    string SectionNum = GetServiceLinkAttachmentsUsedInOrderObj.SectionNum;
        //    string FromServiceManual = GetServiceLinkAttachmentsUsedInOrderObj.FromServiceManual;
        //    string Culturevalue = string.Empty;
        //    Culturevalue = "Resource_" + GetServiceLinkAttachmentsUsedInOrderObj.Lang;
        //    string Conn = connstring;
        //    //string TableName = string.Empty;

        //    var JsonReader = default(dynamic);
        //    try
        //    {
        //        using (SqlConnection conn = new SqlConnection(Conn))
        //        {
        //            using (SqlDataAdapter da = new SqlDataAdapter())
        //            {
        //                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
        //                {
        //                    conn.Open();
        //                }
        //                List<PartUsedAssembly> PartUsedAssemliesList = new List<PartUsedAssembly>();
        //                //string SectionNumber = Request.Params["SectionNum"].ToString();
        //                //string OrderNumber = Request.Params["OrderNum"].ToString();
        //                string[] OrderNumberArr = OrderNumber.Split('-');
        //                // Session["FromServiceManual"] = FromServiceManual;
        //                try
        //                {
        //                    SqlDataReader DataReader = null;
        //                    SqlCommand sqlComm = new SqlCommand("Usp_Sel_ServiceLink_OrderList", conn);
        //                    sqlComm.Parameters.AddWithValue("@SectionNumber", SectionNum);
        //                    sqlComm.Parameters.AddWithValue("@OrderNumber", OrderNumberArr.Count() > 0 ? OrderNumberArr[0] : OrderNumber);
        //                    sqlComm.Parameters.AddWithValue("@Part_Assembly_ID", PartNumber);
        //                    sqlComm.CommandType = CommandType.StoredProcedure;
        //                    if (conn.State == ConnectionState.Closed)
        //                    {
        //                        conn.Open();
        //                    }
        //                    DataReader = sqlComm.ExecuteReader();

        //                    while (DataReader.Read())
        //                    {
        //                        PartUsedAssembly SingleRecord = new PartUsedAssembly();
        //                        SingleRecord.Part_Assembly_ID = int.Parse(DataReader["Part_Assembly_ID"].ToString());
        //                        SingleRecord.MFG_Code = DataReader["MFG_Code"].ToString();
        //                        SingleRecord.Part_Assembly_No = DataReader["Part_Assembly_No"].ToString();
        //                        SingleRecord.Item_No = DataReader["Item_No"].ToString();
        //                        SingleRecord.Qty = DataReader["Qty"].ToString();
        //                        SingleRecord.Part_Assembly_Description = DataReader["Part_Assembly_Description"].ToString();
        //                        SingleRecord.Part_Assembly_IsAssembly = DataReader["Part_Assembly_IsAssembly"].ToString();
        //                        PartUsedAssemliesList.Add(SingleRecord);
        //                    }

        //                }
        //                catch (Exception ex)
        //                {
        //                    var Request = new System.Net.Http.HttpRequestMessage();
        //                    ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(GetServiceLinkAttachmentsUsedInOrderObj.UserID), "");
        //                }
        //                finally
        //                {
        //                    SqlConnection.ClearAllPools();
        //                }
        //                JsonReader = new
        //                {
        //                    Count = PartUsedAssemliesList.Count,
        //                    AssemblyData = PartUsedAssemliesList
        //                };
        //            }
        //        }
        //    }
        //    catch (Exception e)
        //    {
        //        var Request = new System.Net.Http.HttpRequestMessage();
        //        ExceptionLogger.ErrorLog(e, Request, Convert.ToInt32(GetServiceLinkAttachmentsUsedInOrderObj.UserID), connstring);
        //    }
        //    finally
        //    {
        //        SqlConnection.ClearAllPools();
        //    }
        //    return new JsonResult(JsonReader);
        //}
        //#endregion

        public static string GetGlobalResourceObject(string cultureValue, string resourceKey, Assembly assembly = null)
        {
            try
            {
                if (assembly == null)
                {
                    assembly = Assembly.GetExecutingAssembly();
                }

                string cultureIdentifier = cultureValue.Replace("Resource_", "");

                string resourceNamespace = assembly.GetName().Name + ".App_GlobalResources.";

                string resourceFileName = "resource_" + cultureIdentifier.ToLowerInvariant();

                ResourceManager resourceManager = new ResourceManager(resourceNamespace + resourceFileName, assembly);

                string resourceValue = resourceManager.GetString(resourceKey);

                return resourceValue;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error accessing resource: {ex.Message}");
                return string.Empty;
            }
        }
    }

    public class DataCls
    {
        public int ID { get; set; }
        public string Name { get; set; }
    }
    public class NovaCatalougeBOMInitialLoad
    {
        public int Section { get; set; }
        public int Langauge_ID { get; set; }
        public string Lang { get; set; }
        public int UserID { get; set; }
        public int BrandID { get; set; }
        public string Part_Assembly_No { get; set; }
        //public List<ShoppingCartClsForGrid> CurrentShoppingCartData { get; set; }
        public string BrandCode { get; set; }
        public int DCTYPE { get; set; }

        public bool _search { get; set; }
        public int nd { get; set; }
        public int rows { get; set; }
        public int page { get; set; }
        public string sidx { get; set; }
        public string sord { get; set; }

        public string UserMail { get; set; }

        public bool IsExternal { get; set; }
        public string MFG_Code { get; set; }
        public string filterValue { get; set; }
    }

    public class PartAssemblyViewer
    {
        public Int64 Row_Nump { get; set; }
        public int? Part_AssemblyID { get; set; }
        public string ManageBOM_ID { get; set; }
        public int Part_Assembly_ID { get; set; }
        public bool ManageBOM_Is_Hotspot { get; set; }
        public string UOM_Name { get; set; }
        public string ManageBOM_Item_No { get; set; }
        public string MFG_Code { get; set; }
        public string Part_Assembly_No { get; set; }
        public string Part_Assembly_AlternateNo { get; set; }
        public string Part_Assembly_Description { get; set; }

        public string French_Description { get; set; }
        public string ManageBOM_Qty { get; set; }
        public string Part_Assembly_Specification { get; set; }
        public int ManageBOM_Font_ID { get; set; }
        public bool Part_Assembly_IsSupersession { get; set; }
        public string Part_Assembly_IsSupersession_Text { get; set; }
        public string Part_Assembly_Remarks { get; set; }
        public bool Img { get; set; }
        public bool Part_Has_Recommended { get; set; }
        public string Part_Has_Recommended_Text { get; set; }
        public string Part_Assembly_IsPurchasable { get; set; }
        public string Part_Assembly_IsAssembly { get; set; } = "false";
        public string ManageDrawing_File_Name { get; set; }
        public string BOM_HotSpot_Coordinates { get; set; }
        public string Font_Name { get; set; }
        public int Font_Size { get; set; }
        public string Font_Style { get; set; }
        public string Font_Colour { get; set; }
        public int BOM_HotSpot_Drawing_ID { get; set; }
        public int RowNumber { get; set; }
        public string RecommendedParts_ID { get; set; }
        public string Is_TopSection { get; set; }
        public string Is_Section { get; set; }
        public string Is_SubSection { get; set; }
        public string Part_Assembly_IsRecommendedPart { get; set; }
        public string Part_Assembly_IsSetMember { get; set; }
        public string Vendor_Code { get; set; }
        public string Vendor_Name { get; set; }
        public string VendorPartInfo_PartNo { get; set; }
        public string Customer_Name { get; set; }
        public string ManageBOM_MaskingAction_ID { get; set; }
        public string Author_Notes { get; set; }
        public int BrandID { get; set; }
        public string FunctionGroup { get; set; }
        public string Assembly_Count { get; set; }
        public string Concat_Item_No { get; set; }
        public string Text { get; set; }
        public string DATE_CREATED { get; set; }
        public string French { get; set; }
        public string Mfrer { get; set; }
        public string Part_Status { get; set; }
        public string REVISION { get; set; }
        public string Stackno { get; set; }

        public string StockNo1 { get; set; }

        public string StockNo2 { get; set; }

        public string StockNo3 { get; set; }

        public string StockNo4 { get; set; }

        public string StockNo5 { get; set; }
        public string NoteText { get; set; }
        public string OrderNumber { get; set; }
        public string ParentMFGCODE { get; set; }
        public string Parent_Assembly_No { get; set; }

        public string Parent_Part_Assembly_Description { get; set; }
        public string Part_Assembly_CreatedDate { get; set; }
        public string Part_Assembly_ModifiedDate { get; set; }
        public string Is_Purchasable { get; set; }
        public string Part_Assembly_IsActive { get; set; }
        public string Part_Assembly_ShowVendorPartToCustomer { get; set; }
        public string Part_Assembly_IsSuperseeded { get; set; }
        public string Part_Assembly_IsSuperseeding { get; set; }
        public string Is_PartSection { get; set; }
        public string Show_Set_Mem_In_Part_List { get; set; }
        public string Is_ServiceSection { get; set; }
        public string Part_Assembly_IsVisible { get; set; }
        public string OfflineData { get; set; }
        public bool Add_to_Favourite { get; set; }
        public string OfflineDataDatetime { get; set; }
        public bool IsShoppingCartPart { get; internal set; }
        public bool IsBOMinShoppingCart { get; internal set; }
        public string IsAddToShoppingCart { get; set; }
        public string Catalogue_DeliveryDate { get; set; }
        public int Count_Of_Vehicle { get; set; }

        public int Section { get; set; }

        public int UserID { get; set; }

        public int DCTYPE { get; set; }

        public List<AvailableCustomerPartInfo> CustomerInfo { get; set; }

        //public string Hide_Item_No { get; set; }
        public string UserMail { get; set; }
    }
    public class AvailableCustomerPartInfo
    {
        public int Customer_ID { get; set; }
        public string Customer_GUIConfig { get; set; }
        public string Customer_FrenchShortCode { get; set; }
        public string Customer_ShortCode { get; set; }
        public string Customer_Code { get; set; }

        public string Customer_Name { get; set; }

        public string OriginalColName { get; set; }
        public string DisplayColName { get; set; }
        public string GridColName { get; set; }
        public string SortNumber { get; set; }
        public bool IsVisble { get; set; }
        public string Align { get; set; }
        public string width { get; set; }

        public int Width { get; set; }
    }
    public class PartAssemblyHeader
    {
        public int Part_Assembly_ID { get; set; }
        public string Part_Assembly_No { get; set; }
        public string Part_Assembly_Description { get; set; }
        public int BID { get; set; }
    }
    public class NovaCatalougeIllustrationsLoad
    {
        public int? ID { get; set; }
        public int GetPart { get; set; }
        public string TopLevelSectionVIN { get; set; }
        public string FromSerialNumber { get; set; }
        public string Culture { get; set; }

        public string TopLevelSectionVINShortCode { get; set; }
        public string ToSerialNo { get; set; }
        public string Model { get; set; }
        public string Vehicle { get; set; }
        public string Lang { get; set; }
        public int UserID { get; set; }
        public string BrandCode { get; set; }
        public int DCTYPE { get; set; }
        public string UserMail { get; set; }
        public bool IsExternal { get; set; }
        public string FileName { get; set; }
        public string Part_Assembly_No { get; set; }
        public string MFG_Code { get; set; }
    }
    public class AssemblyIllustration
    {
        public int ManageDrawing_ID { get; set; }
        public int ManageDrawing_Part_Assembly_ID { get; set; }
        public string ManageDrawing_File_Name { get; set; }
        public string ManageDrawing_File_Path { get; set; }
        public string ManageDrawing_File_Description { get; set; }
        public bool ManageDrawing_File_Is_Active { get; set; }
        public int ManageDrawing_UploadedBy { get; set; }
        public DateTime ManageDrawing_UploadedDate { get; set; }
        public int? ManageDrawing_Sequence { get; set; }
    }
    public class OrderBasedTreeLoad
    {
        public int sectionvalue { get; set; }
        public int Section { get; set; }
        public int Langauge_ID { get; set; }
        public string Lang { get; set; }
        public int UserID { get; set; }
        public int TopLevelIDValue { get; set; }
        public int? IsRange { get; set; }
        public string CatalogueSortOrder { get; set; }
        public string BrandCode { get; set; }
        public int TreeLevel { get; set; }
        public string FromInsideCatlougeSearch { get; set; }

        public string Culture { get; set; }
        public int DCTYPE { get; set; }
        public List<PartAssemblyViewer> DataList { get; set; }
        public string UserMail { get; set; }
    }
    public class VINBasedTreeLoad
    {
        public int sectionvalue { get; set; }
        public int Section { get; set; }
        public int Langauge_ID { get; set; }
        public string Lang { get; set; }
        public int UserID { get; set; }
        public int TopLevelIDValue { get; set; }
        public int? IsRange { get; set; }
        public string CatalogueSortOrder { get; set; }
        public string BrandCode { get; set; }
        public int TreeLevel { get; set; }
        public string FromInsideCatlougeSearch { get; set; }
        public string Culture { get; set; }
        public int DCTYPE { get; set; }
        public string FromTopLevelSectionVIN { get; set; }
        public string SelectFromTopLevel { get; set; }
        public string CatalogueTree { get; set; }
        public string TopLevelSectionVIN { get; set; }
        public string TopLevelSectionVINShortCode { get; set; }
        public string ToSerialNo { get; set; }
        public string Model { get; set; }
        public string FromSerialNumber { get; set; }

        public string UserMail { get; set; }
    }
    public class InputAttachment
    {
        public int RecordID { get; set; }
        public string FromSerialNumber { get; set; }
        public string ToSerialNo { get; set; }
        public string Model { get; set; }
        public string Menu { get; set; }
        public int User_ID { get; set; }
        public string Lang { get; set; }
        public int DCTYPE { get; set; }
        public string TopLevelSectionVIN { get; set; }
        public string TopLevelSectionVINShortCode { get; set; }
        public string Vehicle { get; set; }
        public string OrderNumber { get; set; }
        public int PartNumber { get; set; }
        public string ServiceAttachmentDropval { get; set; }
        public string TopLevelIDValue { get; set; }
        public int Section { get; set; }
        public int Langauge_ID { get; set; }
        public string UserMail { get; set; }
        public bool IsExternal { get; set; }
        public string MFG_Code { get; set; }
        public string Part_Assembly_No { get; set; }

    }
    public class TempAttachment_Poko
    {
        public bool IsTopLevel { get; set; }
        public string Data { get; set; }
        public int DCTYPE { get; set; }
        public int AttachmentCategory_ID { get; set; }
        public int AttachmentCategoryDet_ID { get; set; }
        public string AttachmentCategory_Name { get; set; }
        public int Attachment_ID { get; set; }
        public int FileType_ID { get; set; }
        public int Record_ID { get; set; }
        public int User_ID { get; set; }
        public int AttachmentTypeDetail_ID { get; set; }
        public int AttachmentType_ID { get; set; }
        public int Menu_ID { get; set; }
        public string FileType_Name { get; set; }
        public string File_Name { get; set; }
        public string File_Description { get; set; }
        public string UserName { get; set; }
        public int UploadedBy { get; set; }
        public string AttachedDate_DF { get; set; }
        public DateTime UploadedDate { get; set; }
        public string UploadedDateTime { get; set; }
        public bool File_IsActive { get; set; }
        public bool IsImageExists { get; set; }
        public string ImagePath { get; set; }
        public string FileExtension { get; set; }
        public bool Attachment_Is_URL { get; set; }
        public string Attachment_Sequence { get; set; }
        public int Attachment_Sequence_Int { get; set; }
        public string AttachmentView { get; set; }
        public int Attachment_Menu_ID { get; set; }
        public int Attachment_Record_ID { get; set; }
        public string Attachment_Folder { get; set; }
        public string Menu_Name { get; set; }
        public string Attachment_URL_View { get; set; }
        public string FileName { get; set; }
        public string TimeTaken { get; set; }
        public string LogRemarks { get; set; }
        public string Attachment_Model { get; set; }
        public string Attachment_FROM_SN_S_Code { get; set; }
        public string Attachment_FROM_SN_Number { get; set; }
        public string Attachment_TO_SN_S_Code { get; set; }
        public string Attachment_TO_SN_S_Number { get; set; }
        public string Model_Ids { get; internal set; }
        public object AttachmentCategoryLocale_Name { get; internal set; }
        public int ServiceLink_ID { get; set; }
        public int ServiceLink_Part_Assembly_ID { get; set; }
        public string ServiceLink_Sequence { get; set; }
        public int ServiceLink_Sequence_Int { get; set; }
        public string ServiceLink_FileNameOrURL { get; set; }
        public string SectionNumber { get; set; }
        public string SectionFileName { get; set; }
        public int ServiceLink_User_ID { get; set; }
        public string ServiceLink_UserName { get; set; }
        public string ServiceLink_DateTimeString { get; set; }
        public DateTime ServiceLink_DateTime { get; set; }
        public string SectionDesc { get; set; }
        public string Category_Name { get; set; }
        public string Lang { get; set; }
        public string Attachment_folderPath { get; internal set; }

        public string UserMail { get; set; }
        public int Attachment_Object_ID { get; set; }
        public int RecordID { get; set; }
        public int ObjectID { get; set; }
        public string Model_Assn { get; set; }

        public string FromSNCode { get; set; }
        public string FromSNNumber { get; set; }
        public string ToSNCode { get; set; }
        public string ToSNNumber { get; set; }

        public int AttachmentID { get; set; }

        public bool URL { get; set; }
        public int BrandID { get; set; }
    }
    public class NovaCatalougeBOMListLoad
    {
        public string Lang { get; set; }
        public int UserID { get; set; }
        public int BrandID { get; set; }
        public string Part_Assembly_No { get; set; }
        public string Section { get; set; }
        public string Vehicle { get; set; }
        public string FromSerialNumber { get; set; }
        public string TopLevelSectionVIN { get; set; }
        public string TopLevelSectionVINShortCode { get; set; }
        public string ToSerialNo { get; set; }
        public string TopLevelSectionVINModel { get; set; }
        public string Model { get; set; }
        public string PartBySearch { get; set; }
        //public List<ShoppingCartClsForGrid> CurrentShoppingCartData { get; set; }
        public string BrandCode { get; set; }
        public string Culture { get; set; }
        public int DCTYPE { get; set; }
        public int Langauge_ID { get; set; }
        public string UserMail { get; set; }
    }
    public class UserLog
    {
        public int Menu_ID { get; set; }
        public int Record_ID { get; set; }
        public int ActionType { get; set; }
        public int User_ID { get; set; }
    }

    public class GetServiceLinkAttachmentsUsedInOrder
    {
        public int UserID { get; set; }
        public int DCTYPE { get; set; }
        public int ObjectID { get; set; }
        public string Lang { get; set; }
        public string ServiceAttachID { get; set; }
        public string OrderNumber { get; set; }
        public int PartNumber { get; set; }
        public string SectionNum { get; set; }
        public string FromServiceManual { get; set; }
        public string UserMail { get; set; }
    }

    public class GetServiceLinkAttachmentsUsedInAssemblies
    {
        public int UserID { get; set; }
        public int DCTYPE { get; set; }
        public int ObjectID { get; set; }
        public string Lang { get; set; }
        public string ServiceAttachID { get; set; }
        public string UserMail { get; set; }
    }

    public class GetTDataPartAssemblyLevel
    {
        public int UserID { get; set; }
        public int DCTYPE { get; set; }
        public int ObjectID { get; set; }
        public string Lang { get; set; }

        public string sectionvalue { get; set; }
        public string Part_Assembly { get; set; }
        public string ToSerialNo { get; set; }
        public string Model { get; set; }
        public string FromSerialNumber { get; set; }

        //------
        public int Section { get; set; }
        public string BrandCode { get; set; }
        public string TopLevelSectionVIN { get; set; }
        public string TopLevelSectionVINShortCode { get; set; }
        public int CatalogueSortOrder { get; set; }
        public string DBCS { get; set; }
        public string SelectAssemblyBOM { get; set; }
        public int Langauge_ID { get; set; }
        public int TreeLevel { get; set; }
        public string UserMail { get; set; }

    }
    public class ExportDashboardOrderList
    {
        public int UserID { get; set; }
        public int DCTYPE { get; set; }
        public string sidx { get; set; }
        public string sord { get; set; }
        public string Condition { get; set; }
        public int ModelVal { get; set; }
        public string Model { get; set; }
        public string filter { get; set; }
        public string Culture { get; set; }
        public string Lang { get; set; }
        public string PartNumber { get; set; }
        public string Catalogue { get; set; }
        public string UserMail { get; set; }
    }
}
