﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class HelpDeskTicketReportController : ApiController
    {
        #region GetObjectID
        /// <summary>
        /// GetObjectID
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetObjectID")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetObjectID([FromBody] TicketReport_GetObjectIDList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetObjectID(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetStatus
        /// <summary>
        /// GetStatus
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetStatus")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetStatus([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetStatus(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetTicketType
        /// <summary>
        /// GetTicketType
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetTicketType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetTicketType([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetTicketType(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetCallMode
        /// <summary>
        /// GetCallMode
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetCallMode")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetCallMode([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetCallMode(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetCallPriority
        /// <summary>
        /// GetCallPriority
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetCallPriority")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetCallPriority([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetCallPriority(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetCallComplexity
        /// <summary>
        /// GetCallComplexity
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetCallComplexity")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetCallComplexity([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetCallComplexity(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetTicketLocation
        /// <summary>
        /// GetTicketLocation
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetTicketLocation")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetTicketLocation([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetTicketLocation(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetcallOwner
        /// <summary>
        /// GetcallOwner
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetcallOwner")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetcallOwner([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetcallOwner(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetFunctionGroup
        /// <summary>
        /// GetFunctionGroup
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetFunctionGroup")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetFunctionGroup([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetFunctionGroup(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetIssueCategory
        /// <summary>
        /// GetIssueCategory
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetIssueCategory")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetIssueCategory([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetIssueCategory(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetClosureType
        /// <summary>
        /// GetClosureType
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetClosureType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetClosureType([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetClosureType(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetRating
        /// <summary>
        /// GetRating
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetRating")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetRating([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetRating(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetResolutionDaysHours
        /// <summary>
        /// GetResolutionDaysHours
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetResolutionDaysHours")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetResolutionDaysHours([FromBody] TicketReport_ChartDetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetResolutionDaysHours(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetResponseDaysHours
        /// <summary>
        /// GetResponseDaysHours
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetResponseDaysHours")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetResponseDaysHours([FromBody] TicketReport_ChartDetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetResponseDaysHours(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetAgeingValues
        /// <summary>
        /// GetAgeingValues
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetAgeingValues")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetAgeingValues([FromBody] TicketReport_ChartDetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetAgeingValues(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region WeekValueofPerticulrMonth
        /// <summary>
        /// WeekValueofPerticulrMonth
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/WeekValueofPerticulrMonth")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult WeekValueofPerticulrMonth([FromBody] TicketReport_ParticulerDetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.WeekValueofPerticulrMonth(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region DateOfPerticularWeek
        /// <summary>
        /// DateOfPerticularWeek
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/DateOfPerticularWeek")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DateOfPerticularWeek([FromBody] TicketReport_ParticulerDetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.DateOfPerticularWeek(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetStatusChart
        /// <summary>
        /// GetStatusChart
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetStatusChart")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetStatusChart([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetStatusChart(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetTicketTypeChart
        /// <summary>
        /// GetTicketTypeChart
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetTicketTypeChart")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetTicketTypeChart([FromBody] TicketReport_ChartDetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetTicketTypeChart(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetCallModeChart
        /// <summary>
        /// GetCallModeChart
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetCallModeChart")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetCallModeChart([FromBody] TicketReport_ChartDetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetCallModeChart(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetCallPriorityChart
        /// <summary>
        /// GetCallPriorityChart
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetCallPriorityChart")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetCallPriorityChart([FromBody] TicketReport_ChartDetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetCallPriorityChart(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetCallComplexityChart
        /// <summary>
        /// GetCallComplexityChart
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetCallComplexityChart")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetCallComplexityChart([FromBody] TicketReport_ChartDetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetCallComplexityChart(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region YearlyPCDReports
        /// <summary>
        /// YearlyPCDReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/YearlyPCDReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult YearlyPCDReports([FromBody] TicketReport_ChartDetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.YearlyPCDReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetTicketLocationChart
        /// <summary>
        /// GetTicketLocationChart
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetTicketLocationChart")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetTicketLocationChart([FromBody] TicketReport_ChartDetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetTicketLocationChart(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetcallOwnerChart
        /// <summary>
        /// GetcallOwnerChart
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetcallOwnerChart")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetcallOwnerChart([FromBody] TicketReport_ChartDetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetcallOwnerChart(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetFunctionGroupChart
        /// <summary>
        /// GetFunctionGroupChart
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetFunctionGroupChart")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetFunctionGroupChart([FromBody] TicketReport_ChartDetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetFunctionGroupChart(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetIssueCategoryChart
        /// <summary>
        /// GetIssueCategoryChart
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetIssueCategoryChart")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetIssueCategoryChart([FromBody] TicketReport_ChartDetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetIssueCategoryChart(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetClosureTypeChart
        /// <summary>
        /// GetClosureTypeChart
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetClosureTypeChart")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetClosureTypeChart([FromBody] TicketReport_ChartDetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetClosureTypeChart(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetRatingCharts
        /// <summary>
        /// GetRatingCharts
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetRatingCharts")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetRatingCharts([FromBody] TicketReport_ChartDetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetRatingCharts(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region YearlyFeedbackReports
        /// <summary>
        /// YearlyFeedbackReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/YearlyFeedbackReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult YearlyFeedbackReports([FromBody] TicketReport_ChartDetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.YearlyFeedbackReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region YearlyClosedReports
        /// <summary>
        /// YearlyClosedReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/YearlyClosedReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult YearlyClosedReports([FromBody] TicketReport_ChartDetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.YearlyClosedReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetResolutionDaysHoursChart
        /// <summary>
        /// GetResolutionDaysHoursChart
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetResolutionDaysHoursChart")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetResolutionDaysHoursChart([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetResolutionDaysHoursChart(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region GetResponseDaysHoursChart
        /// <summary>
        /// GetResponseDaysHoursChart
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetResponseDaysHoursChart")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetResponseDaysHoursChart([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetResponseDaysHoursChart(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region QuaterlyStatusReports
        /// <summary>
        /// QuaterlyStatusReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/QuaterlyStatusReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult QuaterlyStatusReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.QuaterlyStatusReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region QuaterlyTicketTypeReports
        /// <summary>
        /// QuaterlyTicketTypeReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/QuaterlyTicketTypeReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult QuaterlyTicketTypeReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.QuaterlyTicketTypeReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region QuaterlyCallModeReports
        /// <summary>
        /// QuaterlyCallModeReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/QuaterlyCallModeReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult QuaterlyCallModeReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.QuaterlyCallModeReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region QuaterlyCallPriotityReports
        /// <summary>
        /// QuaterlyCallPriotityReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/QuaterlyCallPriotityReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult QuaterlyCallPriotityReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.QuaterlyCallPriotityReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region QuaterlyCallComplexityReports
        /// <summary>
        /// QuaterlyCallComplexityReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/QuaterlyCallComplexityReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult QuaterlyCallComplexityReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.QuaterlyCallComplexityReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region QuaterlyLocationReports
        /// <summary>
        /// QuaterlyLocationReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/QuaterlyLocationReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult QuaterlyLocationReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.QuaterlyLocationReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region QuaterlyPCDReports
        /// <summary>
        /// QuaterlyPCDReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/QuaterlyPCDReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult QuaterlyPCDReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.QuaterlyPCDReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region QuaterlyCallOwnerReports
        /// <summary>
        /// QuaterlyCallOwnerReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/QuaterlyCallOwnerReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult QuaterlyCallOwnerReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.QuaterlyCallOwnerReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region QuaterlyFunctionGroupReports
        /// <summary>
        /// QuaterlyFunctionGroupReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/QuaterlyFunctionGroupReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult QuaterlyFunctionGroupReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.QuaterlyFunctionGroupReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region QuaterlyIssueCategoryReports
        /// <summary>
        /// QuaterlyIssueCategoryReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/QuaterlyIssueCategoryReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult QuaterlyIssueCategoryReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.QuaterlyIssueCategoryReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region QuaterlyClosedTicketReports
        /// <summary>
        /// QuaterlyClosedTicketReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/QuaterlyClosedTicketReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult QuaterlyClosedTicketReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.QuaterlyClosedTicketReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region QuaterlyClosureTypeReports
        /// <summary>
        /// QuaterlyClosureTypeReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/QuaterlyClosureTypeReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult QuaterlyClosureTypeReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.QuaterlyClosureTypeReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region QuaterlyRatingReports
        /// <summary>
        /// QuaterlyRatingReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/QuaterlyRatingReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult QuaterlyRatingReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.QuaterlyRatingReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region QuaterlyFeedbackReports
        /// <summary>
        /// QuaterlyFeedbackReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/QuaterlyFeedbackReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult QuaterlyFeedbackReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.QuaterlyFeedbackReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region AllQuarterlyReports
        /// <summary>
        /// AllQuarterlyReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/AllQuarterlyReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult AllQuarterlyReports([FromBody] AllQuarterlyReportsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.AllQuarterlyReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region MonthlyStatusReports
        /// <summary>
        /// MonthlyStatusReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/MonthlyStatusReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult MonthlyStatusReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.MonthlyStatusReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region MonthlyTicketTypeReports
        /// <summary>
        /// MonthlyTicketTypeReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/MonthlyTicketTypeReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult MonthlyTicketTypeReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.MonthlyTicketTypeReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region MonthlyCallMOdeReports
        /// <summary>
        /// MonthlyCallMOdeReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/MonthlyCallMOdeReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult MonthlyCallMOdeReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.MonthlyCallMOdeReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region MonthlyCallPriorityReports
        /// <summary>
        /// MonthlyCallPriorityReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/MonthlyCallPriorityReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult MonthlyCallPriorityReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.MonthlyCallPriorityReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region MonthlyCallComplexityReports
        /// <summary>
        /// MonthlyCallComplexityReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/MonthlyCallComplexityReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult MonthlyCallComplexityReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.MonthlyCallComplexityReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region MonthlyLocationReports
        /// <summary>
        /// MonthlyLocationReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/MonthlyLocationReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult MonthlyLocationReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.MonthlyLocationReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region MonthlyPCDReports
        /// <summary>
        /// MonthlyPCDReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/MonthlyPCDReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult MonthlyPCDReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.MonthlyPCDReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region MonthlyCallOwnereports
        /// <summary>
        /// MonthlyCallOwnereports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/MonthlyCallOwnereports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult MonthlyCallOwnereports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.MonthlyCallOwnereports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region MonthlyFunctionGroupReports
        /// <summary>
        /// MonthlyFunctionGroupReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/MonthlyFunctionGroupReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult MonthlyFunctionGroupReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.MonthlyFunctionGroupReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region MonthlyIssueCategoryreports
        /// <summary>
        /// MonthlyIssueCategoryreports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/MonthlyIssueCategoryreports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult MonthlyIssueCategoryreports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.MonthlyIssueCategoryreports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region MonthlyClosedTicketreports
        /// <summary>
        /// MonthlyClosedTicketreports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/MonthlyClosedTicketreports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult MonthlyClosedTicketreports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.MonthlyClosedTicketreports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region MonthlyClosureTypereports
        /// <summary>
        /// MonthlyClosureTypereports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/MonthlyClosureTypereports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult MonthlyClosureTypereports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.MonthlyClosureTypereports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region MonthlyRatingreports
        /// <summary>
        /// MonthlyRatingreports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/MonthlyRatingreports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult MonthlyRatingreports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.MonthlyRatingreports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region MonthlyFeedBackreports
        /// <summary>
        /// MonthlyFeedBackreports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/MonthlyFeedBackreports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult MonthlyFeedBackreports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.MonthlyFeedBackreports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region AllMonthlyReports
        /// <summary>
        /// AllMonthlyReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/AllMonthlyReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult AllMonthlyReports([FromBody] AllMonthlyReportsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.AllMonthlyReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region WeeklyStatusReports
        /// <summary>
        /// WeeklyStatusReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/WeeklyStatusReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult WeeklyStatusReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.WeeklyStatusReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region WeeklyTicketTypeReports
        /// <summary>
        /// WeeklyTicketTypeReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/WeeklyTicketTypeReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult WeeklyTicketTypeReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.WeeklyTicketTypeReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region WeeklyCallMOdeReports
        /// <summary>
        /// WeeklyCallMOdeReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/WeeklyCallMOdeReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult WeeklyCallMOdeReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.WeeklyCallMOdeReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region WeeklyCallPriorityReports
        /// <summary>
        /// WeeklyCallPriorityReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/WeeklyCallPriorityReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult WeeklyCallPriorityReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.WeeklyCallPriorityReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region WeeklyCallComplexityReports
        /// <summary>
        /// WeeklyCallComplexityReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/WeeklyCallComplexityReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult WeeklyCallComplexityReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.WeeklyCallComplexityReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region WeeklyLocationReports
        /// <summary>
        /// WeeklyLocationReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/WeeklyLocationReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult WeeklyLocationReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.WeeklyLocationReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region WeeklyPCDReports
        /// <summary>
        /// WeeklyPCDReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/WeeklyPCDReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult WeeklyPCDReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.WeeklyPCDReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region WeeklyCallOwnerReports
        /// <summary>
        /// WeeklyCallOwnerReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/WeeklyCallOwnerReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult WeeklyCallOwnerReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.WeeklyCallOwnerReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region WeeklyFunctionGroupReports
        /// <summary>
        /// WeeklyFunctionGroupReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/WeeklyFunctionGroupReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult WeeklyFunctionGroupReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.WeeklyFunctionGroupReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region WeeklyIssueCategoryReports
        /// <summary>
        /// WeeklyIssueCategoryReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/WeeklyIssueCategoryReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult WeeklyIssueCategoryReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.WeeklyIssueCategoryReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region WeeklyClosedTicketReports
        /// <summary>
        /// WeeklyClosedTicketReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/WeeklyClosedTicketReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult WeeklyClosedTicketReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.WeeklyClosedTicketReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region WeeklyClosureTypeReports
        /// <summary>
        /// WeeklyClosureTypeReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/WeeklyClosureTypeReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult WeeklyClosureTypeReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.WeeklyClosureTypeReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region WeeklyRatingReports
        /// <summary>
        /// WeeklyRatingReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/WeeklyRatingReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult WeeklyRatingReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.WeeklyRatingReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region WeeklyFeedBackReports
        /// <summary>
        /// WeeklyFeedBackReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/WeeklyFeedBackReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult WeeklyFeedBackReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.WeeklyFeedBackReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region AllWeeklyReports
        /// <summary>
        /// AllWeeklyReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/AllWeeklyReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult AllWeeklyReports([FromBody] AllWeeklyReportsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.AllWeeklyReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region DailyStatusReports
        /// <summary>
        /// DailyStatusReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/DailyStatusReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DailyStatusReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.DailyStatusReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region DailyTicketTypeReports
        /// <summary>
        /// DailyTicketTypeReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/DailyTicketTypeReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DailyTicketTypeReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.DailyTicketTypeReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region DailyCallModeReports
        /// <summary>
        /// DailyCallModeReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/DailyCallModeReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DailyCallModeReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.DailyCallModeReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region DailyCallPriorityReports
        /// <summary>
        /// DailyCallPriorityReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/DailyCallPriorityReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DailyCallPriorityReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.DailyCallPriorityReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region DailyCallComplexityReports
        /// <summary>
        /// DailyCallComplexityReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/DailyCallComplexityReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DailyCallComplexityReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.DailyCallComplexityReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region DailyLocationReports
        /// <summary>
        /// DailyLocationReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/DailyLocationReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DailyLocationReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.DailyLocationReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region DailyPCDReports
        /// <summary>
        /// DailyPCDReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/DailyPCDReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DailyPCDReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.DailyPCDReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region DailyCallOwnerReports
        /// <summary>
        /// DailyCallOwnerReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/DailyCallOwnerReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DailyCallOwnerReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.DailyCallOwnerReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region DailyFunctionGroupReports
        /// <summary>
        /// DailyFunctionGroupReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/DailyFunctionGroupReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DailyFunctionGroupReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.DailyFunctionGroupReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region DailyIssueCategoryReports
        /// <summary>
        /// DailyIssueCategoryReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/DailyIssueCategoryReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DailyIssueCategoryReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.DailyIssueCategoryReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region DailyClosedTicketReports
        /// <summary>
        /// DailyClosedTicketReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/DailyClosedTicketReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DailyClosedTicketReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.DailyClosedTicketReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region DailyClosureTypeReports
        /// <summary>
        /// DailyClosureTypeReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/DailyClosureTypeReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DailyClosureTypeReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.DailyClosureTypeReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region DailyRatingReports
        /// <summary>
        /// DailyRatingReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/DailyRatingReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DailyRatingReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.DailyRatingReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region DailyFeedBackReports
        /// <summary>
        /// DailyFeedBackReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/DailyFeedBackReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult DailyFeedBackReports([FromBody] TicketReport_DetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.DailyFeedBackReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion



        #region AllDailyReports
        /// <summary>
        /// AllDailyReports
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/AllDailyReports")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult AllDailyReports([FromBody] AllDailyReportsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.AllDailyReports(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: GetGridData :::
        /// <summary>
        /// GetGridData
        /// </summary>  
        [Route("api/HelpDeskTicketReport/GetGridData")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetGridData([FromBody] TicketReport_GetGridDataList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskTicketReportServices.GetGridData(Obj, Conn, LogException, sidx, sord, page, rows, _search, advnce, Query, filters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return Ok(Response.Value);

        }
        #endregion

        #region:::TicketReportExport 
        /// <summary>
        /// TicketReportExport
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskTicketReport/TicketReportExport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> TicketReportExport([FromBody] TicketReport_ExportList Obj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            //string sidx = Obj.sidx;
            //string sord = Obj.sord;
            //string filter = Obj.filter;
            //string advnceFilter = Obj.advanceFilter;

            try
            {


                object Response = await HelpDeskTicketReportServices.TicketReportExport(Obj, connstring, LogException);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion

        #region:::TicketViewAllDataExport 
        /// <summary>
        /// TicketViewAllDataExport
        /// </summary>
        /// <returns>...</returns>
        [Route("api/HelpDeskTicketReport/TicketViewAllDataExport")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> TicketViewAllDataExport([FromBody] TicketReport_ExportList Obj)
        {

            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            //string sidx = Obj.sidx;
            //string sord = Obj.sord;
            //string filter = Obj.filter;
            //string advnceFilter = Obj.advanceFilter;

            try
            {


                object Response = await HelpDeskTicketReportServices.TicketViewAllDataExport(Obj, connstring, LogException);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion

        #region ::: GetViewAllDataGrid :::
        /// <summary>
        /// GetViewAllDataGrid
        /// </summary>  
        [Route("api/HelpDeskTicketReport/GetViewAllDataGrid")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetViewAllDataGrid([FromBody] TicketReport_GetViewAllDataGridList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskTicketReportServices.GetViewAllDataGrid(Obj, Conn, LogException, sidx, sord, page, rows, _search, advnce, Query, filters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return Ok(Response.Value);

        }
        #endregion

        #region GetRegion
        /// <summary>
        /// GetRegion
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetRegion")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetRegion([FromBody] TicketReport_GetRegionList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetRegion(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion

        #region GetBranch
        /// <summary>
        /// GetBranch
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetBranch")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetBranch([FromBody] TicketReport_GetBranchList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetBranch(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion

        #region GetBrand
        /// <summary>
        /// GetBrand
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetBrand")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetBrand([FromBody] TicketReport_GetBrandList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetBrand(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion

        #region GetProductType
        /// <summary>
        /// GetProductType
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetProductType")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetProductType([FromBody] TicketReport_GetProductTypeList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetProductType(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion

        #region GetSerialNumDetails
        /// <summary>
        /// GetSerialNumDetails
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetSerialNumDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetSerialNumDetails([FromBody] TicketReport_GetSerialNumDetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetSerialNumDetails(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion

        #region GetVehiclDetails
        /// <summary>
        /// GetVehiclDetails
        /// </summary>
        /// <returns></returns>
        [Route("api/HelpDeskTicketReport/GetVehiclDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetVehiclDetails([FromBody] TicketReport_GetVehiclDetailsList Obj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = HelpDeskTicketReportServices.GetVehiclDetails(Obj, connString, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: SelectFieldSearchSerialNumber :::
        /// <summary>
        /// SelectFieldSearchSerialNumber
        /// </summary>  
        [Route("api/HelpDeskTicketReport/SelectFieldSearchSerialNumber")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectFieldSearchSerialNumber([FromBody] TicketReport_SelectFieldSearchSerialNumberList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskTicketReportServices.SelectFieldSearchSerialNumber(Obj, Conn, LogException, sidx, sord, page, rows, _search, advnce, Query, filters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return Ok(Response.Value);

        }
        #endregion

        #region ::: SelectFieldSearchParty :::
        /// <summary>
        /// SelectFieldSearchParty
        /// </summary>  
        [Route("api/HelpDeskTicketReport/SelectFieldSearchParty")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectFieldSearchParty([FromBody] TicketReport_SelectFieldSearchPartyList Obj)
        {
            var Response = default(dynamic);
            string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            string Query = HttpContext.Current.Request.Params["Query"];
            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = HelpDeskTicketReportServices.SelectFieldSearchParty(Obj, Conn, LogException, sidx, sord, page, rows, _search, advnce, Query, filters);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return Ok(Response.Value);

        }
        #endregion
    }
}