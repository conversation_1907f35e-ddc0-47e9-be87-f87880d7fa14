﻿using SharedAPIClassLibrary_AMERP.Services;
using SharedAPIClassLibrary_AMERP;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;

using System.Configuration;
using LS = SharedAPIClassLibrary_AMERP.Utilities;
using static SharedAPIClassLibrary_AMERP.CoreCompanyMasterServices;
using System.Net;
using System.Threading.Tasks;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class YANMARAPIController : ApiController
    {
        #region ::: PartsMasterInsert ::: 
        [Route("api/CorePartsMaster/PartsMasterInsert")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PartsMasterInsert([FromBody] PartsMasterList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string recipient = ConfigurationManager.AppSettings["EmailToAddress"] ?? "";
                string ccRecipient = ConfigurationManager.AppSettings["EmailCcAddress"] ?? "";
                string clientIP = HttpContext.Current.Request.UserHostAddress;
                string baseUrl = HttpContext.Current.Request.Url.GetLeftPart(UriPartial.Authority);
                string fullUrl = baseUrl + HttpContext.Current.Request.RawUrl;
                string Conn = ConfigurationManager.ConnectionStrings["YANMAR"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = YANMARAPISERVICES.PartsMasterInsert(Obj, Conn, LogException, clientIP, fullUrl, recipient, ccRecipient);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region ::: InsertPartsMasterPriceAndSupplierDetails ::: 
        [Route("api/CorePartsMaster/InsertPartsMasterPriceAndSupplierDetails")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertPartsMasterPriceAndSupplierDetails([FromBody] PartPriceDetailList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string recipient = ConfigurationManager.AppSettings["EmailToAddress"] ?? "";
                string ccRecipient = ConfigurationManager.AppSettings["EmailCcAddress"] ?? "";
                string clientIP = HttpContext.Current.Request.UserHostAddress;
                string baseUrl = HttpContext.Current.Request.Url.GetLeftPart(UriPartial.Authority);
                string fullUrl = baseUrl + HttpContext.Current.Request.RawUrl;

                string Conn = ConfigurationManager.ConnectionStrings["YANMAR"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = YANMARAPISERVICES.InsertPartsMasterPriceAndSupplierDetails(Conn, Obj, LogException, clientIP, fullUrl, recipient, ccRecipient);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region :::InsertBranchMaster /Vinay:::
        /// <summary>
        /// InsertBranchMaster
        /// </summary>
        [Route("api/YANMARAPI/InsertDealerMaster")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult InsertDealerMaster([FromBody] DealerMasterDetails Obj)
        {
            var Response = default(dynamic);
            string recipient = ConfigurationManager.AppSettings["EmailToAddress"] ?? "";
            string ccRecipient = ConfigurationManager.AppSettings["EmailCcAddress"] ?? "";
            string clientIP = HttpContext.Current.Request.UserHostAddress;
            string baseUrl = HttpContext.Current.Request.Url.GetLeftPart(UriPartial.Authority);
            string fullUrl = baseUrl + HttpContext.Current.Request.RawUrl;
            string Conn = ConfigurationManager.ConnectionStrings["YANMAR"].ConnectionString;
            int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));

            try
            {
                Response = YANMARAPISERVICES.InsertDealerMaster(Obj, Conn, LogException, clientIP, fullUrl, recipient, ccRecipient);

            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }
            return Ok(Response.Value);

        }
        #endregion

        #region::: PurchaseOrderCancellationInterface /Vinay:::
        /// <summary>
        /// PurchaseOrderCancellationInterface
        /// </summary> Modify by DK - 23-March-2025
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/YANMARAPI/PurchaseOrderCancellationInterface")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PurchaseOrderCancellationInterface([FromBody] PurchaseOrderCancellationInterfaceList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string recipient = ConfigurationManager.AppSettings["EmailToAddress"] ?? "";
                string ccRecipient = ConfigurationManager.AppSettings["EmailCcAddress"] ?? "";
                string clientIP = HttpContext.Current.Request.UserHostAddress;
                string baseUrl = HttpContext.Current.Request.Url.GetLeftPart(UriPartial.Authority);
                string fullUrl = baseUrl + HttpContext.Current.Request.RawUrl;
                string Conn = ConfigurationManager.ConnectionStrings["YANMAR"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = YANMARAPISERVICES.PurchaseOrderCancellationInterface(Obj, Conn, LogException, fullUrl, clientIP, recipient, ccRecipient);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region::: PurchaseInvoiceInterface /Vinay:::
        /// <summary>
        /// PurchaseInvoiceInterface
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/YANMARAPI/PurchaseInvoiceInterface")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PurchaseInvoiceInterface([FromBody] PurchaseInvoiceInterfaceList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string recipient = ConfigurationManager.AppSettings["EmailToAddress"] ?? "";
                string ccRecipient = ConfigurationManager.AppSettings["EmailCcAddress"] ?? "";
                string clientIP = HttpContext.Current.Request.UserHostAddress;
                string baseUrl = HttpContext.Current.Request.Url.GetLeftPart(UriPartial.Authority);
                string fullUrl = baseUrl + HttpContext.Current.Request.RawUrl;
                string Conn = ConfigurationManager.ConnectionStrings["YANMAR"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));               
                try
                {
                    Response = YANMARAPISERVICES.PurchaseInvoiceInterface(Obj, Conn, LogException, fullUrl, clientIP, recipient, ccRecipient);

                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region::: PurchaseInvoiceInterface /Vinay:::
        /// <summary>
        /// PurchaseInvoiceInterface
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/YANMARAPI/PI_InterfaceDK")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PI_Interface_DK([FromBody] PI_InterfaceList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string recipient = ConfigurationManager.AppSettings["EmailToAddress"] ?? "";
                string ccRecipient = ConfigurationManager.AppSettings["EmailCcAddress"] ?? "";
                string clientIP = HttpContext.Current.Request.UserHostAddress;
                string baseUrl = HttpContext.Current.Request.Url.GetLeftPart(UriPartial.Authority);
                string fullUrl = baseUrl + HttpContext.Current.Request.RawUrl;
                string Conn = ConfigurationManager.ConnectionStrings["YANMAR"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                //Response = YANMARAPISERVICES.PI_Interface(Obj, Conn, LogException,fullUrl,clientIP,recipient,ccRecipient);
                try
                {
                    Response = YANMARAPISERVICES.PI_Interface(Obj, Conn, LogException, fullUrl, clientIP, recipient, ccRecipient);

                }
                catch (Exception ex)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":DK123" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        /// <summary>
        /// Modify by DK - 23-March-2025
        /// </summary>
        /// <param name="Obj"></param>
        /// <returns></returns>
        [Route("api/YANMARAPI/PI_Interface")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult PI_Interface([FromBody] PI_InterfaceList Obj)
        {
            var Response = default(dynamic);
            try
            {
                string recipient = ConfigurationManager.AppSettings["EmailToAddress"] ?? "";
                string ccRecipient = ConfigurationManager.AppSettings["EmailCcAddress"] ?? "";
                string clientIP = HttpContext.Current.Request.UserHostAddress;
                string baseUrl = HttpContext.Current.Request.Url.GetLeftPart(UriPartial.Authority);
                string fullUrl = baseUrl + HttpContext.Current.Request.RawUrl;
                string Conn = ConfigurationManager.ConnectionStrings["YANMAR"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));

                //Response = "TEST DK"; // Simulated Response
                Response = YANMARAPISERVICES.PI_Interface(Obj, Conn, LogException, fullUrl, clientIP, recipient, ccRecipient);
            }
            catch (Exception ex)
            {
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":DK123" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);                
            }
            return Ok(Response.Value);
        }

        [Route("api/YANMARAPI/Log")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> GetSAPDMSLogs(
       DateTime? startDate = null,
       DateTime? endDate = null,
       string category = null,
       string status = null)
        {
            try
            {

                string conn = ConfigurationManager.ConnectionStrings["YANMAR"].ConnectionString;

                var logs = await YANMARAPISERVICES.GetSAPDMSLogEntries(startDate, endDate, category, status, conn);

                return Ok(new { success = true, data = logs });
            }
            catch (Exception ex)
            {
                return Content(HttpStatusCode.InternalServerError, new
                {
                    success = false,
                    message = "Internal Server Error",
                    error = ex.Message
                });
            }
        }

    }
}