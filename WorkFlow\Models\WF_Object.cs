//------------------------------------------------------------------------------
// <auto-generated>
//    This code was generated from a template.
//
//    Manual changes to this file may cause unexpected behavior in your application.
//    Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace WorkFlow.Models
{
    using System;
    using System.Collections.Generic;
    
    public partial class WF_Object
    {
        public WF_Object()
        {
            this.GNM_PrefixSuffix = new HashSet<WF_PrefixSuffix>();
            this.GNM_RoleObject = new HashSet<WF_RoleObject>();
        }
    
        public int Object_ID { get; set; }
        public string Object_Name { get; set; }
        public string Read_Action { get; set; }
        public string Create_Action { get; set; }
        public string Update_Action { get; set; }
        public string Delete_Action { get; set; }
        public string Export_Action { get; set; }
        public string Print_Action { get; set; }
        public bool Object_IsActive { get; set; }
        public string Object_Description { get; set; }
        public string Import_Action { get; set; }
        public string Object_Type { get; set; }
    
        public virtual ICollection<WF_PrefixSuffix> GNM_PrefixSuffix { get; set; }
        public virtual ICollection<WF_RoleObject> GNM_RoleObject { get; set; }
    }
}
