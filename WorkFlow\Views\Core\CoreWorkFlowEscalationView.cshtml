﻿@{
    ViewBag.Title = "WorkFlow";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>CoreWorkFlowEscalationView</title>
     <script type="text/javascript">
         $(document).ready(function () {
             var AccessObj = '';
             var editlabel = '@HttpContext.GetGlobalResourceObject("Resource_en", "Edit")';
             var deletelabel = '@HttpContext.GetGlobalResourceObject("Resource_en", "Delete")';
             var JQWFEscalationGrid = $('#TblWFEscalationGrid');
             var JDdlSelCompany = document.getElementById('SelCompany');
             var JDdlSelWorkFlow = document.getElementById('SelWorkFlow');
             var JQDdlSelCompany = $('#SelCompany');
             var JQDdlSelWorkFlow = $('#SelWorkFlow');
             var DefaultGridSize = '@Session["DefaultGridSize"].ToString()'
             var separator = { sepclass: 'ui-separator', sepcontent: '' };
             var HasRows = false;
             ////------------------------------------------Function to Get Role Definition--------------------------------////
             $.fn.InitialSetup = function () {
                 try {
                     $.ajax({
                         url: $.absoluteurl('/CoreWorkFlowEscalation/InitialSetup'),
                         success: function (response) {
                             AccessObj = response.Data;
                             //--Added---Manjunatha P-----For FSM Enhancement
                             $.DestoryFilterToolbar(['TblWFEscalationGrid']); //End
                             $.PageInitialLoad();
                         }
                     });
                 } catch (e) {
                     $.LogException('InitialSetup', e.description, e.name, e.number);
                 }
             }
             $(this).InitialSetup();
             $.PageInitialLoad = function () {
                 try {
                     $.LoadDropDown(JDdlSelCompany, JQDdlSelCompany, '/CoreWorkFlowEscalation/SelectCompany', 0);
                     $.LoadDropDown(JDdlSelWorkFlow, JQDdlSelWorkFlow, '/CoreWorkFlowEscalation/SelectWorkFlow', 0);
                     $.LoadingWFEscalationGrid();
                     $.SelectDefaultValInDDL('SelCompany');
                     $.SelectDefaultValInDDL('SelWorkFlow');
                     $('#SelWorkFlow').change();
                 } catch (e) {
                     $.LogException('PageInitialLoad', e.description, e.name, e.number);
                 }
             }
             ////-----------------------Function To Load Masters-----------------------////
             $.LoadDropDown = function (docObject, JObject, Action, icValue) {
                 try {
                     $.ClearDropdown(docObject);
                     $.ajax({
                         url: $.absoluteurl(Action), async: false, success: function (response) {
                             JObject.LoadOptions(response.Data, 'Name', 'ID');
                             docObject.selectedIndex = icValue;
                         }
                     });
                 } catch (e) {
                     $.LogException('LoadDropDown', e.description, e.name, e.number);
                 }
             }
             //----------------------------------------------------------------------------------------------------------------------//
             $.fn.disableDotDeleteForSLA = function (len, prec, e) {
                 if (e.which == 8 || e.which == 46) {
                     var value = $(this).val();
                     if (value.indexOf('.') != '-1') {
                         return true;
                     }
                     else {
                         if (value.length > (len - prec)) {
                             var oldval = '';
                             var id = $(this).attr('id');
                             for (i = 0; i < value.length; i++) {
                                 if (e.target.selectionStart == i) {
                                     oldval = oldval.concat('.');
                                 }
                                 oldval = oldval.concat(value[i]);
                             }
                             document.getElementById(id).value = oldval;
                         }
                     }
                 }
             }
             //----------------------------------------------------------------------------------------------------------------------//
             $.fn.checkDecimalForSLA = function (len, prec, e) {
                 if (e.which == 8 || e.which == 0) {
                     return true;
                 }
                 else if ((e.which < 48 || e.which > 57) && e.which != 46) {
                     e.preventDefault();
                 }
                 else if (e.which == 46 && ($(this).val().indexOf('.')) != -1) {
                     e.preventDefault();
                 }
                 else if (($(this).val().indexOf('.')) != -1 && $(this).val().length <= len) {
                     if ((($(this).val().indexOf('.') + 1) == $(this).val().length) && (e.which == 55 || e.which == 56 || e.which == 57 || e.which == 54))
                         e.preventDefault();
                     else if ($(this).val().substr(($(this).val().indexOf('.') + 1), $(this).val().length).length > 1) {
                         e.preventDefault();
                     }
                 }
                 else {
                     var value = $(this).val();
                     if (e.which == 46) {
                         if (value.indexOf('.') == '-1' && prec != 0) {
                             return true;
                         }
                         else {
                             e.preventDefault();
                         }
                     }
                     else {
                         if (value.indexOf('.') == '-1' && value.length < (len - prec)) {
                             return true;
                         }
                         else if (value.indexOf('.') != '-1') {

                             if ((value.substr(value.indexOf('.')).length - 1) < prec && e.target.selectionStart > value.indexOf('.')) {
                                 return true;
                             }
                             else if (e.target.selectionStart < (len - prec) && (value.substr(0, value.indexOf('.')).length < (len - prec)) && (e.target.selectionStart - 1 <= value.indexOf('.'))) {

                                 return true;
                             }
                             else {
                                 e.preventDefault();
                             }
                         }
                         else {
                             e.preventDefault();
                         }
                     }
                 }
             };
             $("#SelCompany").change(function () {
                 //--Added---Manjunatha P-----For FSM Enhancement
                 $.DestoryFilterToolbar(['TblWFEscalationGrid']); //End
                 if (SelCompany.value != 0 && SelWorkFlow.value != 0) {
                     $.ReLoadWFEscalationGrid('json');
                 }
                 else if (SelCompany.value == 0) {
                     $.ReLoadWFEscalationGrid('clientSide');
                 }
             })
             $("#SelWorkFlow").change(function () {
                 //--Added---Manjunatha P-----For FSM Enhancement
                 $.DestoryFilterToolbar(['TblWFEscalationGrid']); //End
                 if (SelCompany.value != 0 && SelWorkFlow.value != 0) {
                     $.ReLoadWFEscalationGrid('json');
                 }
                 else if (SelWorkFlow.value == 0) {
                     $.ReLoadWFEscalationGrid('clientSide');
                 }
             })

             $.PrimSegChange = function (mainID, rowID) {
                 var grd = jQuery("#TblWFEscalationGrid");
                 var rowIDS = grd.jqGrid('getDataIDs');
                 var isexist = false;
                 var primID = '#' + $(grd.getCell(rowID, 'RoleName')).attr('id');
                 var primValue = document.getElementById($(grd.getCell(rowID, 'RoleName')).attr('id')).value;
                 var secID = '#' + $(grd.getCell(rowID, 'UserName')).attr('id');
                 var secValue = document.getElementById($(grd.getCell(rowID, 'UserName')).attr('id')).value;
                 if (primValue != '-1' && secValue != '-1') {
                     for (i = 0; i < rowIDS.length; i++) {
                         var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');

                         if (editmode != 'false' && rowIDS[i] != rowID) {
                             var colValue1 = document.getElementById($(grd.getCell(rowIDS[i], 'RoleName')).attr('id')).value;
                             var colValue2 = document.getElementById($(grd.getCell(rowIDS[i], 'UserName')).attr('id')).value;

                             if (primValue == colValue1 && secValue == colValue2) {
                                 isexist = true;
                                 alert('@HttpContext.GetGlobalResourceObject("Resource_en", "CombinationAlreadyExists")');
                                 $('#' + mainID).attr('value', '-1');
                                 return;
                             }
                         }
                     }
                     $.ajax(
                             {
                                 url: $.url('/CoreWorkFlowEscalation/EscAlreadyExits?ActualRID=0&ActualUID=0'),
                                 type: 'POST',
                                 data: { prim: primValue, sec: secValue },
                                 datatype: 'json',
                                 success: function (data) {

                                     if (data == 'True') {
                                         $('#' + mainID).attr('value', '-1');
                                         alert('@HttpContext.GetGlobalResourceObject("Resource_en", "CombinationAlreadyExists")');
                                    }
                                },
                                error: function (e) {

                                    alert('@HttpContext.GetGlobalResourceObject("Resource_en", "CombinationAlreadyExists")');
                                }
                            });
                            }
             }
             $.EscUniqueness = function (rowID) {
                 var grd = $('#TblWFEscalationGrid');
                 var CC = '#' + $(grd.getCell(rowID, 'RoleName')).attr('id');
                 var CP = '#' + $(grd.getCell(rowID, 'UserName')).attr('id');
                 var CCval = document.getElementById($(grd.getCell(rowID, 'RoleName')).attr('id')).value;
                 var CPval = document.getElementById($(grd.getCell(rowID, 'UserName')).attr('id')).value;
                 $(CC).change(function () { $.ProdChange($(this).attr('id'), rowID, CCval, CPval); });
                 $(CP).change(function () { $.ProdChange($(this).attr('id'), rowID, CCval, CPval); });
             };
             $.ProdChange = function (mainID, rowID, ActCC, ActCP) {
                 var grd = $('#TblWFEscalationGrid');
                 var rowIDS = grd.jqGrid('getDataIDs');
                 var isexist = false;
                 CompxID = jQuery("#TblWFEscalationGrid").getCell(rowID, 'RoleName');
                 PriorID = jQuery("#TblWFEscalationGrid").getCell(rowID, 'UserName');
                 var CCID = '#' + $(grd.getCell(rowID, 'RoleName')).attr('id');
                 var CCValue = document.getElementById($(grd.getCell(rowID, 'RoleName')).attr('id')).value;
                 var CPID = '#' + $(grd.getCell(rowID, 'UserName')).attr('id');
                 var CPValue = document.getElementById($(grd.getCell(rowID, 'UserName')).attr('id')).value;
                 if (CPValue != -1 && CCValue != -1) {
                     for (i = 0; i < rowIDS.length; i++) {
                         var editmode = $(grd.getCell(rowIDS[i], 'edit')).attr('editmode');
                         if (editmode != 'false' && rowIDS[i] != rowID) {
                             var colValue1 = document.getElementById($(grd.getCell(rowIDS[i], 'RoleName')).attr('id')).value;
                             var colValue2 = document.getElementById($(grd.getCell(rowIDS[i], 'UserName')).attr('id')).value;
                             if (CCValue == colValue1 && CPValue == colValue2) {
                                 isexist = true;
                                 alert('@HttpContext.GetGlobalResourceObject("Resource_en", "CombinationAlreadyExists")');
                                 $('#' + mainID).attr('value', '-1');
                                 return;
                             }
                         }
                     }
                     if (CCValue == ActCC && CPValue == ActCP) {
                         return;
                     }
                     else {
                         $.ajax({
                             url: $.url('/CoreWorkFlowEscalation/EscAlreadyExits?ActualRID=' + ActCC + '&ActualUID=' + ActCP),
                             type: 'POST',
                             data: { prim: CCValue, sec: CPValue },
                             datatype: 'json',
                             success: function (data) {
                                 if (data == 'True') {
                                     $('#' + mainID).attr('value', '-1');
                                     alert('@HttpContext.GetGlobalResourceObject("Resource_en", "CombinationAlreadyExists")');
                                }
                            }
                        });
                    }
                }
             }
             $.EscSegmentUniqueness = function (rowID) {
                 var grd = jQuery("#TblWFEscalationGrid");
                 var prim = '#' + $(grd.getCell(rowID, 'RoleName')).attr('id');
                 $(prim).attr('duplicate', 'false');
                 $(prim).attr('exists', 'false');
                 var sec = '#' + $(grd.getCell(rowID, 'UserName')).attr('id');
                 $(sec).attr('duplicate', 'false');
                 $(sec).attr('exists', 'false');
                 $(prim).change(function () { $.PrimSegChange($(this).attr('id'), rowID); });
                 $(sec).change(function () { $.PrimSegChange($(this).attr('id'), rowID) });
             };
             ////------------------------------------------------------------------------------------////
             $.LoadingWFEscalationGrid = function () {
                 try {
                     JQWFEscalationGrid.jqGrid({
                         url: $.absoluteurl(AccessObj.SelectAction + '?Company_ID=' + SelCompany.value + '&WorkFlow_ID=' + SelWorkFlow.value),
                         datatype: 'clientSide',
                         mtype: 'POST',
                         colNames: ['', '@HttpContext.GetGlobalResourceObject("Resource_en", "edit")', '@HttpContext.GetGlobalResourceObject("Resource_en", "Delete")', '@HttpContext.GetGlobalResourceObject("Resource_en", "Role")', '@HttpContext.GetGlobalResourceObject("Resource_en", "EscalationHours")', '@HttpContext.GetGlobalResourceObject("Resource_en", "EscalateTo")', '@HttpContext.GetGlobalResourceObject("Resource_en", "IsEmail")', '@HttpContext.GetGlobalResourceObject("Resource_en", "IsSms")', '@HttpContext.GetGlobalResourceObject("Resource_en", "CCToAssignee")'],
                     colModel: [
                         { name: 'ID', index: 'ID', key: true, hidden: true },
                         { name: 'edit', sortable: false, editable: false, search: false, width: 30, align: 'center' },
                         { name: 'delete', sortable: false, editable: false, search: false, width: 50, align: 'center', hidden: !AccessObj.IsDelete },
                         {
                             name: 'RoleName', index: 'RoleName', width: 250, editable: true, sortable: true, search: true, advanceSearch: true, advncOptions: { name: '@HttpContext.GetGlobalResourceObject("Resource_en", "Role")', IsInteger: false, IsMandatory: true },
                             edittype: 'select', editoptions: { value: function () { return RoleName; }, style: 'width:250px' }
                         },
                        {
                            name: 'Escalation_Hours', index: 'Escalation_Hours', width: 130, editable: true, sortable: true, search: true, advanceSearch: true, advncOptions: { name: '@HttpContext.GetGlobalResourceObject("Resource_en", "EscalationHours")', IsInteger: true, IsMandatory: true },
                            align: 'right', edittype: 'text', editoptions: { dataEvents: [{ type: 'keypress', fn: function (e) { $(this).checkDecimalForSLA(5, 2, e) } }, { type: 'keyup', fn: function (e) { $(this).disableDotDeleteForSLA(5, 2, e); } }], style: 'text-align:right' },
                        },
                        {
                            name: 'UserName', index: 'UserName', width: 170, editable: true, sortable: true, search: true, advanceSearch: true, advncOptions: { name: '@HttpContext.GetGlobalResourceObject("Resource_en", "EscalateTo")', IsInteger: false, IsMandatory: true },
                            edittype: 'select', editoptions: { value: function () { return EmployeeNames; }, style: 'width:150px' }
                        },
                        {
                            name: 'Escalate_IsEmail', index: 'Escalate_IsEmail', align: 'center', width: 75, editable: true, sortable: true, search: true, advanceSearch: true, advncOptions: { name: '@HttpContext.GetGlobalResourceObject("Resource_en", "IsEmail")', IsInteger: false, IsMandatory: true },
                            edittype: 'checkbox', editoptions: { value: "Yes:No", dataEvents: [{ type: 'click', fn: function () { $(this).check(); } }] }
                        },
                        {
                            name: 'Escalate_IsMobile', index: 'Escalate_IsMobile', align: 'center', width: 75, editable: true, sortable: true, search: true, advanceSearch: true, advncOptions: { name: '@HttpContext.GetGlobalResourceObject("Resource_en", "IsSms")', IsInteger: false, IsMandatory: true },
                            edittype: 'checkbox', editoptions: { value: "Yes:No", dataEvents: [{ type: 'click', fn: function () { $(this).check(); } }] }
                        },
                         {
                             name: 'CCToAssignee', index: 'CCToAssignee', align: 'center', width: 120, editable: true, sortable: true, search: true, advanceSearch: true, advncOptions: { name: '@HttpContext.GetGlobalResourceObject("Resource_en", "CCToAssignee")', IsInteger: false, IsMandatory: true },
                             edittype: 'checkbox', editoptions: { value: "Yes:No", dataEvents: [{ type: 'click', fn: function () { $(this).check(); } }] }
                         }
                     ],
                     pager: '#divWFEscPager',
                     rowNum: DefaultGridSize,
                     rowList: [DefaultGridSize, 5, 10, 15, 25],
                     viewrecords: true,
                     sortname: 'Escalation_Hours',
                     sortorder: 'asc',
                     caption: '@HttpContext.GetGlobalResourceObject("Resource_en", "WorkFlowEscalation")',
                     jsonReader: {
                         root: 'rows',
                         total: 'TotalPages',
                         page: 'PageNo',
                         records: 'RecordCount',
                         repeatitems: false
                     },
                     width: (screen.width - 0.35 * screen.width),
                     height: '100%',
                     loadComplete: function (data) {
                         try {
                             //--Added---Manjunatha P-----For FSM Enhancement
                             $.ToggleFilterToolbar('TblWFEscalationGrid'); //End
                             $('#gs_Escalation_Hours').keypress(function (e) { $(this).checkDecimalForSLA(5, 2, e) })
                             RoleName = data.WFRoleNames;
                             EmployeeNames = data.EmployeeNames;
                             HasRows = data.HasRows;
                             if (AccessObj.IsEdit) {
                                 JQWFEscalationGrid.makeRowEditable('EscalationEdit', AccessObj.IsEdit);
                                 $('.EscalationEdit').click(function () {
                                     var rowid = $(this).parent('td').parent('tr').attr('id');
                                     $.EscUniqueness(rowid);
                                 });
                             }
                             if (AccessObj.IsDelete) {
                                 JQWFEscalationGrid.checkable('EscalationDelete');
                             }
                         }
                         catch (e) { }
                     }
                 });
                 JQWFEscalationGrid.advanceSrch();
                 jQuery("#TblWFEscalationGrid").navGrid('#divWFEscPager', { view: false, add: false, edit: false, del: false, search: false, refresh: false });
                 jQuery("#TblWFEscalationGrid").jqGrid('filterToolbar', {
                     searchOnEnter: true, stringResult: true,
                     beforeSearch: function () {
                         var f = jQuery("#TblWFEscalationGrid").getGridParam('postData').filters;
                         if (f != undefined) {
                             f = $.EncryptString(f);
                             jQuery("#TblWFEscalationGrid").setGridParam({ search: true, postData: { filters: f } });
                             jQuery("#TblWFEscalationGrid").trigger('reloadGrid');
                             return true;
                         }
                     },
                     afterSearch: function () {
                         var f = jQuery("#TblWFEscalationGrid").getGridParam('postData').filters;
                         if (f != undefined) {
                             f = $.DecryptString(f);
                             jQuery("#TblWFEscalationGrid").setGridParam({ search: true, postData: { filters: f } });
                         }
                     }
                 });

                 if (AccessObj.IsAdd) {
                     JQWFEscalationGrid.navButtonAdd('#divWFEscPager', {
                         buttonicon: 'ui-icon-plus',
                         caption: '',
                         title: '@HttpContext.GetGlobalResourceObject("Resource_en", "Add")',
                             position: 'first',
                             onClickButton: function () {
                                 try {
                                     if (JQDdlSelCompany.attr('value') != 0 && JQDdlSelWorkFlow.attr('value') != 0) {
                                         JQWFEscalationGrid.addGridRow('NewWFEscalationdelete');
                                         var rowIDS = jQuery("#TblWFEscalationGrid").jqGrid('getDataIDs');
                                         var id = rowIDS[rowIDS.length - 1];
                                         $.EscSegmentUniqueness(id);
                                     }
                                 } catch (e) {
                                     $.LogException('WFEscalationsAddClick', e.description, e.name, e.number);
                                 }
                             }
                         });
                     }

                     if (AccessObj.IsDelete) {
                         JQWFEscalationGrid.navButtonAdd('#divWFEscPager', {
                             buttonicon: 'ui-icon-minus',
                             caption: '',
                             title: '@HttpContext.GetGlobalResourceObject("Resource_en", "Delete")',
                            position: 'last',
                            onClickButton: function () {
                                try {
                                    if (JQDdlSelCompany.attr('value') != 0 && JQDdlSelWorkFlow.attr('value') != 0) {
                                        jQuery("#TblWFEscalationGrid").delGridRows($.absoluteurl(AccessObj.DeleteAction));
                                    }
                                } catch (e) {
                                    $.LogException('WFEscalationDeleteClick', e.description, e.name, e.number);
                                }
                            }
                        });
                    }
                    if (AccessObj.IsAdd || AccessObj.IsEdit) {
                        $("#TblWFEscalationGrid").navButtonAdd('#divWFEscPager', {
                            buttonicon: 'ui-icon-disk',
                            caption: '',
                            title: '@HttpContext.GetGlobalResourceObject("Resource_en", "Save")',
                             position: 'last',
                             onClickButton: function () {
                                 try {
                                     var JTblWFEscalationGrid = document.getElementById('TblWFEscalationGrid');
                                     Count = 0;
                                     BaseCount = 0;
                                     var rowData = jQuery("#TblWFEscalationGrid").jqGrid('getDataIDs');
                                     var WFEscalationMasterData = '{rows:[';
                                     for (var i = 1; i <= rowData.length; i++) {
                                         var Data = JTblWFEscalationGrid.rows[i];
                                         var CurrentMode = $(Data.children[1].innerHTML).attr('editmode');
                                         if (CurrentMode != 'false') {
                                             BaseCount = BaseCount + 1;
                                             var Escalation_ID = ($.trim(Data.children[0].firstChild.nodeValue) == "" ? 0 : Data.children[0].firstChild.nodeValue);
                                             var WFRole_ID = $.trim(Data.children[3].firstChild.value);
                                             var Escalate_To_EmployeeID = $.trim(Data.children[5].firstChild.value);
                                             var Escalation_Hours = $.trim(Data.children[4].firstChild.value);
                                             var Escalate_IsEmail = (Data.children[6].firstChild.checked);
                                             var Escalate_IsMobile = (Data.children[7].firstChild.checked);
                                             var CCToAssignee = (Data.children[8].firstChild.checked);
                                             if (WFRole_ID != '-1') {
                                                 $.AddRemoveValidationCSS(Data.children[3].firstChild, 0);
                                             }
                                             else {
                                                 $.AddRemoveValidationCSS(Data.children[3].firstChild, 1);
                                             }
                                             if (Escalate_To_EmployeeID != '-1') {
                                                 $.AddRemoveValidationCSS(Data.children[5].firstChild, 0);
                                             }
                                             else {
                                                 $.AddRemoveValidationCSS(Data.children[5].firstChild, 1);
                                             }
                                             if (Escalation_Hours != '') {
                                                 $.AddRemoveValidationCSS(Data.children[4].firstChild, 0);
                                             }
                                             else {
                                                 $.AddRemoveValidationCSS(Data.children[4].firstChild, 1);
                                             }
                                             if (WFRole_ID != '-1' && Escalate_To_EmployeeID != '-1' && Escalation_Hours != '') {

                                                 Count = Count + 1;
                                                 WFEscalationMasterData = WFEscalationMasterData.concat('{Escalation_ID:\'' + Escalation_ID + '\',WFRole_ID:\'' + WFRole_ID + '\',Escalate_To_EmployeeID:\'' + Escalate_To_EmployeeID + '\',Escalation_Hours:\'' + Escalation_Hours + '\',Escalate_IsEmail:\'' + Escalate_IsEmail + '\',Escalate_IsMobile:\'' + Escalate_IsMobile + '\',CCToAssignee:\'' + CCToAssignee + '\'},');
                                             }
                                         }
                                     }
                                     WFEscalationMasterData = WFEscalationMasterData.concat(']}');
                                     if (BaseCount == Count && Count > 0 && BaseCount > 0) {
                                         $.ajax({
                                             cache: false,
                                             url: $.absoluteurl(AccessObj.AddAction + '?Company_ID=' + SelCompany.value + '&WorkFlow_ID=' + SelWorkFlow.value),
                                             data: { data: WFEscalationMasterData },
                                             datatype: 'json',
                                             type: 'POST',
                                             success: function (response) {
                                                 if (response == 'Saved') {
                                                     alert('@HttpContext.GetGlobalResourceObject("Resource_en", "SavedSuccessfully")');
                                                    jQuery("#TblWFEscalationGrid").trigger('reloadGrid');
                                                }
                                                else {
                                                    alert('@HttpContext.GetGlobalResourceObject("Resource_en", "ErrorSaving")');
                                                }
                                            },
                                            error: function (c, d, e) {
                                                alert(d);
                                            }
                                        });
                                    }
                                    else if (BaseCount > Count) {
                                        alert('@HttpContext.GetGlobalResourceObject("Resource_en", "Fieldshighlightedaremandatory")');
                                    }

                            } catch (e) {
                                $.LogException('WFEscalationMasterLandingGridEnglishSaveClick', e.description, e.name, e.number);
                            }
                            }
                         });
                }

                JQWFEscalationGrid.navButtonAdd('#divWFEscPager', {
                    buttonicon: 'ui-icon-search',
                    caption: '',
                    title: '@HttpContext.GetGlobalResourceObject("Resource_en", "advancesearch")',
                         position: 'last',
                         onClickButton: function () {
                             try {
                                 if (JQDdlSelCompany.attr('value') != 0 && JQDdlSelWorkFlow.attr('value') != 0) {
                                     JQWFEscalationGrid.advanceSrch('show');
                                 }
                             } catch (e) {
                                 $.LogException('HolidaysSearchClick', e.description, e.name, e.number);
                             }
                         }
                     });
                     if (AccessObj.IsExport) {
                         jQuery("#TblWFEscalationGrid").documentExport({ url: $.absoluteurl(AccessObj.ExportAction) });
                         JQWFEscalationGrid.navButtonAdd('#divWFEscPager', {
                             buttonicon: 'ui-icon-document',
                             caption: '',
                             title: '@HttpContext.GetGlobalResourceObject("Resource_en", "Export")',
                             position: 'last',
                             onClickButton: function () {
                                 try {
                                     if (JQDdlSelCompany.attr('value') != 0 && JQDdlSelWorkFlow.attr('value') != 0 && HasRows == true) {
                                         if (jQuery("#TblWFEscalationGrid").jqGrid('getDataIDs').length > 0) {
                                             jQuery("#TblWFEscalationGrid").documentExport('setParameters', '&CompanyName=' + $.EncryptString($('#SelCompany option:selected').text()) + '&WorkFlowName=' + $.EncryptString($('#SelWorkFlow option:selected').text()));
                                             jQuery("#TblWFEscalationGrid").documentExport("show");
                                         }
                                     }
                                 } catch (e) {
                                     $.LogException('HolidaysExportClick', e.description, e.name, e.number);
                                 }
                             }
                         });
                     }
                     JQWFEscalationGrid.navButtonAdd('#divWFEscPager', {
                         buttonicon: 'ui-icon-refresh',
                         caption: '',
                         title: '@HttpContext.GetGlobalResourceObject("Resource_en", "refresh")',
                         position: 'last',
                         onClickButton: function () {
                             try {
                                 $.ReLoadWFEscalationGrid('json');
                             } catch (e) {
                                 $.LogException('HolidaysRefreshClick', e.description, e.name, e.number);
                             }
                         }
                     });
                 } catch (e) {
                     $.LogException('LoadingEscalationGrid', e.description, e.name, e.number);
                 }
             }
             ////-----------------------Function to ReLoad WFEscalation Grid -----------------------////
             $.ReLoadWFEscalationGrid = function (type) {
                 try {
                     jQuery("#TblWFEscalationGrid").setGridParam({ datatype: type });
                     jQuery("#TblWFEscalationGrid").setGridParam({ url: $.absoluteurl(AccessObj.SelectAction + '?Company_ID=' + $('#SelCompany option:selected').val() + '&WorkFlow_ID=' + SelWorkFlow.value) })
                     jQuery("#TblWFEscalationGrid").trigger("reloadGrid");
                 } catch (e) {
                     $.LogException('ReLoadWFEscalationGrid', e.description, e.name, e.number);
                 }
             }

             ////-----------------------Function To Log Exception-----------------------////
             $.LogException = function (MethodName, Description, ErrorType, Number) {
                 $.post(
                     $.absoluteurl("/CoreWorkFlowEscalation/LogClientException?MethodName=" + MethodName + "&Description=" + Description + "&ErrorType=" + ErrorType + "&Number=" + Number),
                     function () { },
                     "Text");
             }
             ////-------------------------------------------------------------------------------------////
         });
         </script>
</head>
    <body><br />
        <table>
            <tr style="height:23px;">
                <td><span style="font-family:Calibri;font-size:15px">@HttpContext.GetGlobalResourceObject("Resource_en", "company")</span></td>
                <td style="height:23px;">&nbsp;&nbsp;
                    <select id="SelCompany" class="dropdown">
                        <option value="0">---@HttpContext.GetGlobalResourceObject("Resource_en", "select")---</option>
                    </select>
                </td>
                <td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span style="font-family:Calibri;font-size:15px">@HttpContext.GetGlobalResourceObject("Resource_en", "workflow")</span></td>
                <td style="height:23px;">&nbsp;&nbsp;
                    <select id="SelWorkFlow" class="dropdown">
                        <option value="0">---@HttpContext.GetGlobalResourceObject("Resource_en", "select")---</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td colspan="4"><br />
                    <table id="TblWFEscalationGrid">
                    <tr>
                        <td />
                    </tr>
                    </table>
                    <div id="divWFEscPager"></div>
                </td>
            </tr>
        </table>
    </body>
</html>
         
