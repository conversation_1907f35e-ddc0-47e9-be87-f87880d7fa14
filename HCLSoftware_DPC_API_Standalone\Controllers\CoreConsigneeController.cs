﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using LS = SharedAPIClassLibrary_AMERP.Utilities;


namespace HCLSoftware_DPC_API_Standalone.Controllers
{

    public class CoreConsigneeController : ApiController
    {

        #region::: LoadBranchDropdown /Vinay:::
        /// <summary>
        /// LoadBranchDropdown
        /// </summary>
        /// <param name="LoadBranchDropdownObj"></param>
        /// <param name="constring"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        [Route("api/CoreConsignee/LoadBranchDropdown")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult LoadBranchDropdown([FromBody] LoadBranchDropdownList LoadBranchDropdownObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreConsigneeServices.LoadBranchDropdown(LoadBranchDropdownObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion


        #region ::: SelectFieldSearchDealerBranches Uday Kumar J B 22-07-2024:::
        /// <summary>
        /// SelectFieldSearchDealerBranches
        /// </summary>
        /// <param name="sidx"></param>
        /// <param name="sord"></param>
        /// <param name="page"></param>
        /// <param name="rows"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        /// 
        [Route("api/CoreConsignee/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] GetAllconsigneeList SelectConsigneeObj)
        {
            var Response = default(dynamic);
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = "";
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);

            string advnceFilters = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = CoreConsigneeServices.Select(connstring, SelectConsigneeObj, sidx, rows, page, sord, _search, nd, filters, advnce, advnceFilters);




            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion

        #region Save/Vinay
        /// <summary>
        /// Save
        /// </summary>
        /// <param name="SaveObj"></param>
        /// <returns></returns>

        [Route("api/CoreConsignee/Save")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Save([FromBody] SaveList SaveObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreConsigneeServices.Save(SaveObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region CheckConsignee /Vinay N
        /// <summary>
        /// CheckConsignee
        /// </summary>
        /// <param name="CheckConsigneeObj"></param>
        /// <returns></returns>
        [Route("api/CoreConsignee/CheckConsignee")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckConsignee([FromBody] CheckConsigneeList CheckConsigneeObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreConsigneeServices.CheckConsignee(CheckConsigneeObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region CheckConsigneeAddress /Vinay N
        /// <summary>
        /// CheckConsigneeAddress
        /// </summary>
        /// <param name="CheckConsigneeAddressObj"></param>
        /// <returns></returns>
        [Route("api/CoreConsignee/CheckConsigneeAddress")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckConsigneeAddress([FromBody] CheckConsigneeAddressList CheckConsigneeAddressObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreConsigneeServices.CheckConsigneeAddress(CheckConsigneeAddressObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion

        #region SelectParticularConsignee /Vinay N
        /// <summary>
        /// SelectParticularConsignee
        /// </summary>
        /// <param name="SelectParticularConsigneeObj"></param>
        /// <returns></returns>
        [Route("api/CoreConsignee/SelectParticularConsignee")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult SelectParticularConsignee([FromBody] SelectParticularConsigneeList SelectParticularConsigneeObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreConsigneeServices.SelectParticularConsignee(SelectParticularConsigneeObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion
        #region Delete /Vinay N
        /// <summary>
        /// Delete
        /// </summary>
        /// <param name="DeleteObj"></param>
        /// <returns></returns>
        [Route("api/CoreConsignee/Delete")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Delete([FromBody] DeleteList DeleteObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreConsigneeServices.Delete(DeleteObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion

        #region UpdateLocale /Vinay N
        /// <summary>
        /// UpdateLocale
        /// </summary>
        /// <param name="UpdateLocaleObj"></param>
        /// <returns></returns>
        [Route("api/CoreConsignee/UpdateLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult UpdateLocale([FromBody] UpdateLocaleList UpdateLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreConsigneeServices.UpdateLocale(UpdateLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }

        #endregion

        #region CheckConsigneeLocale
        /// <summary>
        /// CheckConsigneeLocale
        /// </summary>
        /// <param name="CheckConsigneeLocaleObj"></param>
        /// <returns></returns>
        [Route("api/CoreConsignee/CheckConsigneeLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckConsigneeLocale([FromBody] CheckConsigneeLocaleList CheckConsigneeLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreConsigneeServices.CheckConsigneeLocale(CheckConsigneeLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion
        #region CheckConsigneeAddressLocale
        /// <summary>
        /// CheckConsigneeAddressLocale
        /// </summary>
        /// <param name="CheckConsigneeAddressLocaleObj"></param>
        /// <returns></returns>
        [Route("api/CoreConsignee/CheckConsigneeAddressLocale")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckConsigneeAddressLocale([FromBody] CheckConsigneeAddressLocaleList CheckConsigneeAddressLocaleObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreConsigneeServices.CheckConsigneeAddressLocale(CheckConsigneeAddressLocaleObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region CheckWareHouse
        /// <summary>
        /// CheckWareHouse
        /// </summary>
        /// <param name="CheckWareHouseObj"></param>
        /// <returns></returns>
        [Route("api/CoreConsignee/CheckWareHouse")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult CheckWareHouse([FromBody] CheckWareHouseList CheckWareHouseObj)
        {
            var Response = default(dynamic);
            try
            {
                string Conn = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                int LogException = Convert.ToInt32(ConfigurationManager.AppSettings.Get("LogError"));
                Response = CoreConsigneeServices.CheckWareHouse(CheckWareHouseObj, Conn, LogException);
            }
            catch (Exception ex)
            {
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(NovaTreeLoadObj.UserID));
                LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return Ok(Response.Value);
        }
        #endregion

        #region Export vinay
        /// <summary>
        /// Export
        /// </summary>
        /// <param name="ExportObj"></param>
        /// <returns></returns>
        [Route("api/CoreConsignee/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] GetAllconsigneeList ExportObj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = ExportObj.sidx;

            string sord = ExportObj.sord;


            string filters = ExportObj.filter;


            string advnceFilters = ExportObj.advanceFilter;



            try
            {


                Object Response = await CoreConsigneeServices.Export(ExportObj, connstring, LogException, filters, advnceFilters, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }


        }
        #endregion




    }
}
