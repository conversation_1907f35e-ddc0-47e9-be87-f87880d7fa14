﻿using SharedAPIClassLibrary_AMERP;
using System;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using static SharedAPIClassLibrary_AMERP.Helpdesk_Rpt_ResponseTimeServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace HCLSoftware_DPC_API_Standalone.Controllers
{
    public class Helpdesk_Rpt_ResponseTimeController : ApiController
    {



        #region :::Select Uday Kumar J B 13-11-2024:::
        /// <summary>
        /// Select.
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/Helpdesk_Rpt_ResponseTime/Select")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult Select([FromBody] Helpdesk_Rpt_ResponseTimeSelectList Helpdesk_Rpt_ResponseTimeSelectobj)
        {
            var Response = default(dynamic);
            string connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));

            string sidx = HttpContext.Current.Request.Params["sidx"];
            int rows = Convert.ToInt32(HttpContext.Current.Request.Params["rows"]);
            int page = Convert.ToInt32(HttpContext.Current.Request.Params["page"]);
            string sord = HttpContext.Current.Request.Params["sord"];
            bool _search = Convert.ToBoolean(HttpContext.Current.Request.Params["_search"]);
            long nd = Convert.ToInt64(HttpContext.Current.Request.Params["nd"]);
            string filters = HttpContext.Current.Request.Params["filters"];
            bool advnce = Convert.ToBoolean(HttpContext.Current.Request.Params["advnce"]);
            string Query = HttpContext.Current.Request.Params["Query"];


            if (HttpContext.Current.Request.Params["filters"] == null)
            {
                filters = HttpContext.Current.Request.Params["customFilterParameter[filters]"];
            }
            else
            {
                filters = HttpContext.Current.Request.Params["filters"];
            }
            try
            {
                Response = Helpdesk_Rpt_ResponseTimeServices.Select(Helpdesk_Rpt_ResponseTimeSelectobj, connString, LogException, _search, filters, Query, advnce, sidx, sord, page, rows);

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

            }
            return Ok(Response.Value);
        }
        #endregion


        #region ::: GetActionTaken Uday Kumar J B 13-11-2024:::
        /// <summary>
        /// GetActionTaken
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/Helpdesk_Rpt_ResponseTime/GetActionTaken")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult GetActionTaken([FromBody] Helpdesk_Rpt_ResponseTimeGetActionTakenList Helpdesk_Rpt_ResponseTimeGetActionTakenobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            string Dbname = ConfigurationManager.AppSettings.Get("DbName");
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = Helpdesk_Rpt_ResponseTimeServices.GetActionTaken(Helpdesk_Rpt_ResponseTimeGetActionTakenobj, connString, Dbname, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::getWorkingHours Not Used Uday Kumar J B 13-11-2024:::
        /// <summary>
        /// getWorkingHours
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/Helpdesk_Rpt_ResponseTime/getWorkingHours")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public IHttpActionResult getWorkingHours([FromBody] Helpdesk_Rpt_ResponseTimegetWorkingHoursList Helpdesk_Rpt_ResponseTimegetWorkingHoursobj)
        {
            var Response = default(dynamic);
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            string Dbname = ConfigurationManager.AppSettings.Get("DbName");
            try
            {
                String connString = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
                Response = Helpdesk_Rpt_ResponseTimeServices.getWorkingHours(Helpdesk_Rpt_ResponseTimegetWorkingHoursobj, connString, Dbname, LogException);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return Ok(Response.Value);
        }
        #endregion


        #region :::Export Uday Kumar J B 13-11-2024:::
        /// <summary>
        /// Export
        /// </summary>
        /// <returns>...</returns>
        /// 
        [Route("api/Helpdesk_Rpt_ResponseTime/Export")]
        [HttpPost]
        [JwtTokenValidationFilter]
        public async Task<IHttpActionResult> Export([FromBody] Helpdesk_Rpt_ResponseTimeSelectList Helpdesk_Rpt_ResponseTimeSelectobj)
        {
            string connstring = ConfigurationManager.ConnectionStrings["FSMGOLD"].ConnectionString;
            int LogException = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings.Get("LogError"));
            string Dbname = ConfigurationManager.AppSettings.Get("DbName");

            string sidx = Helpdesk_Rpt_ResponseTimeSelectobj.sidx;
            string sord = Helpdesk_Rpt_ResponseTimeSelectobj.sord;
            string filter = Helpdesk_Rpt_ResponseTimeSelectobj.filter;
            string advnceFilter = Helpdesk_Rpt_ResponseTimeSelectobj.advanceFilter;

            try
            {


                Object Response = await Helpdesk_Rpt_ResponseTimeServices.Export(Helpdesk_Rpt_ResponseTimeSelectobj, Dbname, connstring, LogException, filter, advnceFilter, sidx, sord);
                return Ok(Response);
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
                return InternalServerError(ex);

            }

        }
        #endregion


    }
}