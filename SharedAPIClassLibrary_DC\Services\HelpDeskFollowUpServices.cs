﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SharedAPIClassLibrary_DC.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using WorkFlow.Models;
using static SharedAPIClassLibrary_AMERP.CoreEmailTemplateService;
using static SharedAPIClassLibrary_AMERP.HelpDeskServiceRequestServices;
using LS = SharedAPIClassLibrary_AMERP.Utilities;

namespace SharedAPIClassLibrary_AMERP
{
    public class HelpDeskFollowUpServices
    {
        // Variable Declaration
        public static string AppPath = string.Empty;
        //++++++++++++++++++++++++++++++++++++++++++++++++++++++
        #region GetInitialData
        /// <summary>
        /// GetInitialData
        /// </summary>
        /// <returns></returns>
        public static IActionResult GetInitialData(GetInitialDataList OBJ, string connString, int LogException)
        {
            var JsonResult = default(dynamic);
            try
            {
                int userLanguageID = Convert.ToInt32(OBJ.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(OBJ.GeneralLanguageID);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();
                    using (SqlCommand cmd = new SqlCommand("USP_HelpDeskFollowUpGetInitialData", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            string FollowUpModeNames = "-1:--select--;";
                            while (reader.Read())
                            {
                                int refMasterDetailID = reader.GetInt32(0);
                                string refMasterDetailName = reader.GetString(1);
                                FollowUpModeNames += $"{refMasterDetailID}:{refMasterDetailName};";
                            }
                            FollowUpModeNames = FollowUpModeNames.TrimEnd(';');

                            reader.NextResult();

                            string FollowUpStatusNames = "-1:--select--;";
                            while (reader.Read())
                            {
                                int refMasterDetailID = reader.GetInt32(0);
                                string refMasterDetailName = reader.GetString(1);
                                FollowUpStatusNames += $"{refMasterDetailID}:{refMasterDetailName};";
                            }
                            FollowUpStatusNames = FollowUpStatusNames.TrimEnd(';');

                            JsonResult = new
                            {
                                FollowUpModeNames,
                                FollowUpStatusNames,
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(JsonResult);
        }
        #endregion

        #region ::: SelHDFollowUpDetails:::
        /// <summary>
        /// SelHDFollowUpDetails
        /// </summary>
        public static IActionResult SelHDFollowUpDetails(SelHDFollowUpDetailsList OBJ, string connString, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters)
        {
            var jsonResult = default(dynamic);
            try
            {
                int count = 0;
                int total = 0;
                int userLanguageID = Convert.ToInt32(OBJ.UserLanguageID);
                int generalLanguageID = Convert.ToInt32(OBJ.GeneralLanguageID);
                Convert.ToInt32(OBJ.ServiceRequestID);

                IQueryable<ServiceRequestFollowUpDetail> IQServiceRequestFollowUpDetail = null;
                List<ServiceRequestFollowUpDetail> IQServiceRequestFollowUpDetailList = null;
                IQServiceRequestFollowUpDetailList = GetAllServiceRequestFollowUpDetails(connString, userLanguageID, generalLanguageID, LogException);
                IQServiceRequestFollowUpDetail = IQServiceRequestFollowUpDetailList.AsQueryable();
                if (_search)
                {
                    Filters filtersOBJ = JObject.Parse(Common.DecryptString(Common.DecryptString(filters))).ToObject<Filters>();
                    if ((filtersOBJ.rules).Count != 0)
                    {
                        IQServiceRequestFollowUpDetail = IQServiceRequestFollowUpDetail.FilterSearch<ServiceRequestFollowUpDetail>(filtersOBJ);
                    }
                }

                IQServiceRequestFollowUpDetail = IQServiceRequestFollowUpDetail.OrderByField<ServiceRequestFollowUpDetail>(sidx, sord);

                //if (generalLanguageID == userLanguageID)
                //{
                //    Session["IQServiceRequestFollowUpDetail"] = IQServiceRequestFollowUpDetail.AsEnumerable();
                //}
                count = IQServiceRequestFollowUpDetail.Count();
                total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(count) / Convert.ToDouble(rows))) : 0;
                if (count < (rows * page) && count != 0)
                {
                    page = (count / rows) + ((count % rows) == 0 ? 0 : 1);
                }
                jsonResult = new
                {
                    total = total,
                    page = page,
                    records = count,
                    data = (from a in IQServiceRequestFollowUpDetail.AsEnumerable()
                            select new
                            {
                                ID = a.ServiceRequestFollowUpDetails_ID,
                                edit = "<img id='" + a.ServiceRequestFollowUpDetails_ID + "' src='" + AppPath + "/Content/Images/edit.gif' key='" + a.ServiceRequestFollowUpDetails_ID + "' editmode='false' class='editHDSRFollowUp'/>",
                                delete = "<input type='checkbox' key='" + a.ServiceRequestFollowUpDetails_ID + "' id='chk" + a.ServiceRequestFollowUpDetails_ID + "' class='chkHDSRFollowUpDelete'/>",
                                FollowUpDescription = a.FollowUpDescription,
                                FollowUpMode_Name = a.FollowUpMode_Name,
                                FollowUpStatus_Name = a.FollowUpStatus_Name,
                                StartDateandTime = a.StartDateandTime.ToString("dd-MMM-yyyy hh:mm tt"),
                                EndDateandTime = a.EndDateandTime == null ? ("") : Convert.ToDateTime(a.EndDateandTime).ToString("dd-MMM-yyyy hh:mm tt"),
                                sendreminder = "<img id='" + a.ServiceRequestFollowUpDetails_ID + "' src='" + AppPath + "/Content/Images/Remind.png' key='" + a.ServiceRequestFollowUpDetails_ID + "' value='Send Reminder' class='chkSendReminder'/>",
                                FUInvite = "<img id='" + a.ServiceRequestFollowUpDetails_ID + "' src='" + AppPath + "/Content/Images/Inv.png' key='" + a.ServiceRequestFollowUpDetails_ID + "'value='Invite' class='InviteServerClass'/>",
                                Remarks = a.Remarks,
                                ServiceRequest_ID = a.ServiceRequest_ID,
                                EnquiryNumber = a.ServiceRequestNumber,
                                EnquiryType = a.CaseType,
                                Date = a.ServiceRequestDate.ToString("dd-MMM-yyyy hh:mm tt"),
                                Customer = a.PartyName,
                                IssueArea = a.IssueAreaName,
                                EnquiryStatus = a.Status,
                                PartyID = a.PartyID
                            }).ToList(),
                    //FollowUpModeNames,
                    //FollowUpStatusNames,
                    filter = filters,
                };

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonResult);
        }
        #endregion

        #region ::: Get All Service Request Follow Up Details:::
        /// <summary>
        /// Get All Service Request Follow Up Details
        /// </summary>
        public static List<ServiceRequestFollowUpDetail> GetAllServiceRequestFollowUpDetails(string connString, int userLanguageID, int generalLanguageID, int logException)
        {
            var followUpDetailsList = new List<ServiceRequestFollowUpDetail>();

            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    using (SqlCommand cmd = new SqlCommand("USP_GetAllServiceRequestFollowUpDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@UserLanguageID", userLanguageID);
                        cmd.Parameters.AddWithValue("@GeneralLanguageID", generalLanguageID);

                        conn.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var detail = new ServiceRequestFollowUpDetail
                                {
                                    ServiceRequest_ID = reader["ServiceRequest_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["ServiceRequest_ID"]),
                                    ServiceRequestNumber = reader["ServiceRequestNumber"] == DBNull.Value ? null : reader["ServiceRequestNumber"].ToString(),
                                    ServiceRequestDate = reader["ServiceRequestDate"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(reader["ServiceRequestDate"]),
                                    CaseType = reader["CaseType"] == DBNull.Value ? null : reader["CaseType"].ToString(),
                                    PartyName = reader["PartyName"] == DBNull.Value ? null : reader["PartyName"].ToString(),
                                    PartyID = reader["PartyID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["PartyID"]),
                                    IssueAreaName = reader["IssueAreaName"] == DBNull.Value ? null : reader["IssueAreaName"].ToString(),
                                    Status = reader["Status"] == DBNull.Value ? null : reader["Status"].ToString(),
                                    ServiceRequestFollowUpDetails_ID = reader["ServiceRequestFollowUpDetails_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["ServiceRequestFollowUpDetails_ID"]),
                                    FollowUpDescription = reader["FollowUpDescription"] == DBNull.Value ? null : reader["FollowUpDescription"].ToString(),
                                    FollowUpMode_ID = reader["FollowUpMode_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["FollowUpMode_ID"]),
                                    FollowUpMode_Name = reader["FollowUpMode_Name"] == DBNull.Value ? null : reader["FollowUpMode_Name"].ToString(),
                                    FollowUpStatus_ID = reader["FollowUpStatus_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["FollowUpStatus_ID"]),
                                    FollowUpStatus_Name = reader["FollowUpStatus_Name"] == DBNull.Value ? null : reader["FollowUpStatus_Name"].ToString(),
                                    StartDateandTime = reader["StartDateandTime"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(reader["StartDateandTime"]),
                                    EndDateandTime = reader["EndDateandTime"] == DBNull.Value ? default(DateTime) : Convert.ToDateTime(reader["EndDateandTime"]),
                                    Remarks = reader["Remarks"] == DBNull.Value ? null : reader["Remarks"].ToString()
                                };


                                followUpDetailsList.Add(detail);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (logException == 0)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return followUpDetailsList;
        }
        #endregion


        #region ::: SendReminder:::
        /// <summary>
        /// SendReminder
        /// </summary>
        public static IActionResult SendReminder(SendReminderList OBJ, string connString, int LogException)
        {
            string emailstatus = "Email has been sent to contact person";

            try
            {
                // Initialize dependencies
                int BranchID = Convert.ToInt32(OBJ.Branch.ToString());
                int companyID = OBJ.Company_ID;
                string Languagecode = OBJ.UserLanguageCode.ToString() != OBJ.GeneralLanguageCode.ToString()
                                      ? OBJ.UserLanguageCode.ToString() : "";

                // Deserialize JSON data
                // Deserialize the 'Data' object to List<GNMServices>
                JObject jobj = JObject.Parse(OBJ.Data.ToString());
                List<GNMServices> slist = JsonConvert.DeserializeObject<List<GNMServices>>(jobj["rows"].ToString());

                // Deserialize the 'ContactPersondata' object to List<ContactPersonDetails>
                JObject jobjcnt = JObject.Parse(OBJ.ContactPersondata.ToString());
                List<ContactPersonDetails> slistcnt = JsonConvert.DeserializeObject<List<ContactPersonDetails>>(jobjcnt["rows"].ToString());

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    conn.Open();

                    // Loop through each service and contact person
                    foreach (var service in slist)
                    {
                        List<ContactPersonDetails> slistcntdtls = slistcnt.Where(l => l.PartyID == service.Party_ID).ToList();

                        foreach (var contact in slistcntdtls)
                        {

                            CommonMethodForEmailandSMSList emailData = new CommonMethodForEmailandSMSList
                            {
                                TemplateCode = "Follow Up Reminder",
                                CompanyId = companyID,
                                LanguageCode = Languagecode,
                                BranchId = BranchID,
                                p1 = service.FollowUpStatus_Name,
                                p2 = service.FollowUpDescription,
                                p3 = service.StartDateandTime,
                                p4 = service.EndDateandTime,
                                p5 = service.FollowUpMode_Name,
                                p6 = service.Remarks,
                                p7 = contact.ContactPersonName,
                                p8 = contact.ContactPersonMobile,
                                p9 = contact.ContactPersonEmail,
                                p10 = service.EnquiryType,
                                p11 = service.EnquiryNumber,
                                p12 = service.Date,
                                p13 = service.Customer,
                                p14 = service.IssueArea
                            };
                            StringBuilder[] ETRes = CommonMethodForEmailandSMS(connString, emailData);

                            string EmailSubject = ETRes[0].ToString();
                            string EmailBody = ETRes[1].ToString();
                            string EmailTO = contact.ContactPersonEmail;

                            if (!string.IsNullOrEmpty(EmailTO))
                            {
                                using (SqlCommand cmd = new SqlCommand("usp_AddEmailRecord", conn))
                                {
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.Parameters.AddWithValue("@EmailSubject", EmailSubject);
                                    cmd.Parameters.AddWithValue("@EmailBody", EmailBody);
                                    cmd.Parameters.AddWithValue("@EmailTo", EmailTO);
                                    cmd.Parameters.AddWithValue("@EmailQueueDate", Common.LocalTime(BranchID, DateTime.Now, connString));
                                    cmd.ExecuteNonQuery();
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, $"{ex.GetType().FullName}: {ex.Message}", ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(emailstatus);
        }

        #endregion

        #region ::: UpdateHDFollowUpDetails:::
        /// <summary>
        /// UpdateHDFollowUpDetails
        /// </summary>
        public static IActionResult UpdateHDFollowUpDetails(UpdateHDFollowUpDetailsList OBJ, string connString, int LogException)
        {
            var jsonResult = default(dynamic);
            JObject jObjz = null;

            try
            {
                string FollowUpsDetailData = OBJ.FollowUpsDetailData.ToString();
                jObjz = JObject.Parse(FollowUpsDetailData);

                using (var conn = new SqlConnection(connString))
                {
                    conn.Open();
                    SqlTransaction transaction = conn.BeginTransaction(); // Begin a transaction

                    try
                    {
                        int Count = jObjz["rows"].Count();
                        for (int i = 0; i < Count; i++)
                        {
                            HD_SRFollowUpDetails FRow = jObjz["rows"].ElementAt(i).ToObject<HD_SRFollowUpDetails>();

                            // Call stored procedure for updating or inserting HD_SRFollowUpDetails
                            using (SqlCommand cmd = new SqlCommand("USP_UpdateOrInsert_SRFollowUpDetails", conn, transaction))
                            {
                                cmd.CommandType = CommandType.StoredProcedure;
                                cmd.Parameters.AddWithValue("@SRFollowUpDetails_ID", FRow.SRFollowUpDetails_ID);
                                cmd.Parameters.AddWithValue("@ServiceRequest_ID", FRow.ServiceRequest_ID);
                                cmd.Parameters.AddWithValue("@FollowUpDescription", Common.DecryptString(Common.DecryptString(FRow.FollowUpDescription)));
                                cmd.Parameters.AddWithValue("@FollowUpMode_ID", FRow.FollowUpMode_ID);
                                cmd.Parameters.AddWithValue("@FollowUpStatus_ID", FRow.FollowUpStatus_ID);
                                cmd.Parameters.AddWithValue("@StartDateandTime", FRow.StartDateandTime);
                                cmd.Parameters.AddWithValue("@EndDateandTime", FRow.EndDateandTime);
                                cmd.Parameters.AddWithValue("@Remarks", Common.DecryptString(Common.DecryptString(FRow.Remarks)));

                                cmd.ExecuteNonQuery(); // Execute the stored procedure
                            }

                            // Handle the HD_SRFollowUpInviteDetails collection
                            foreach (var inviteDetail in FRow.HD_SRFollowUpInviteDetails)
                            {
                                using (SqlCommand cmd = new SqlCommand("USP_UpdateOrInsert_SRFollowUpInviteDetails", conn, transaction))
                                {
                                    cmd.CommandType = CommandType.StoredProcedure;
                                    cmd.Parameters.AddWithValue("@SRFollowUpInviteDetails_ID", inviteDetail.SRFollowUpInviteDetails_ID);
                                    cmd.Parameters.AddWithValue("@Employee_ID", inviteDetail.Employee_ID == 0 ? (object)DBNull.Value : inviteDetail.Employee_ID);
                                    cmd.Parameters.AddWithValue("@Party_ID", inviteDetail.Party_ID == 0 ? (object)DBNull.Value : inviteDetail.Party_ID);
                                    cmd.Parameters.AddWithValue("@ServiceRequest_ID", inviteDetail.ServiceRequest_ID);
                                    cmd.Parameters.AddWithValue("@Invitee_Type", inviteDetail.Invitee_Type);
                                    cmd.Parameters.AddWithValue("@IsAttended", inviteDetail.IsAttended);
                                    cmd.Parameters.AddWithValue("@SRFollowUpDetails_ID", inviteDetail.SRFollowUpDetails_ID);

                                    cmd.ExecuteNonQuery(); // Execute the stored procedure
                                }
                            }
                        }

                        transaction.Commit();
                        jsonResult = new { Result = "Success" };
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();

                        jsonResult = new
                        {
                            Result = "Fail",
                            ErrorMessage = ex.Message,
                        };

                        if (LogException == 1)
                        {
                            LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                jsonResult = new
                {
                    Result = "Fail",
                    ErrorMessage = ex.Message,
                };

                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }

            return new JsonResult(jsonResult);
        }

        #endregion

        #region ::: SelHDFollowUpInviteDetails:::
        /// <summary>
        /// SelHDFollowUpInviteDetails
        /// </summary>
        public static IActionResult SelHDFollowUpInviteDetails(SelHDFollowUpInviteDetailsList OBJ, string connString, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters)
        {
            int count = 0;
            var dataList = new List<object>();
            var jsonResult = new
            {
                total = 0,
                page = page,
                records = 0,
                data = new List<object>()
            };

            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    // First command to get the data
                    using (SqlCommand cmd = new SqlCommand("USP_SelHDFollowUpInviteDetails", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.AddWithValue("@SRIDGlobal", OBJ.SRIDGlobal);
                        cmd.Parameters.AddWithValue("@SRFollowUpDetails_ID", OBJ.SRFollowUpDetails_ID);
                        cmd.Parameters.AddWithValue("@sidx", sidx);
                        cmd.Parameters.AddWithValue("@sord", sord);
                        cmd.Parameters.AddWithValue("@page", page);
                        cmd.Parameters.AddWithValue("@rows", rows);

                        conn.Open();
                        SqlDataReader reader = cmd.ExecuteReader();
                        while (reader.Read())
                        {
                            dataList.Add(new
                            {
                                ID = reader["SRFollowUpInviteDetails_ID"],
                                Emp_Cus_ID = reader["Invitee_Type"] == DBNull.Value ? 0 : (reader["Invitee_Type"].ToString() == "1" ? reader["Party_ID"] : reader["Employee_ID"]),
                                edit = $"<img id='{reader["SRFollowUpInviteDetails_ID"]}' src='/Content/Images/edit.gif' key='{reader["SRFollowUpInviteDetails_ID"]}' editmode='false' class='editInviteClass' />",
                                delete = $"<input type='checkbox' key='{reader["SRFollowUpInviteDetails_ID"]}' id='chk{reader["SRFollowUpInviteDetails_ID"]}' class='chkInviteDelete' />",
                                IsCustomer = reader["Invitee_Type"].ToString() == "1" ? "Yes" : "No",
                                Name = reader["Name"],
                                Search = "",
                                IsAttended = reader["IsAttended"].ToString() == "1" ? "Yes" : "No"
                            });
                        }
                        reader.Close();
                    }
                    count = dataList.Count();
                }

                jsonResult = new
                {
                    total = (int)Math.Ceiling((double)count / rows),
                    page = page,
                    records = count,
                    data = dataList
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LS.LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }
            }
            return new JsonResult(jsonResult);
        }
        #endregion

    }

    //Properties

    public partial class SendReminderList
    {
        public string UserLanguageCode { get; set; }
        public string GeneralLanguageCode { get; set; }
        public int ServiceRequestID { get; set; }
        public int Company_ID { get; set; }
        public string Branch { get; set; }
        public string Data { get; set; }
        public string ContactPersondata { get; set; }
    }
    public partial class UpdateHDFollowUpDetailsList
    {
        public string FollowUpsDetailData { get; set; }
    }
    public partial class ServiceRequestFollowUpDetail
    {
        public int ServiceRequestFollowUpDetails_ID { get; set; }
        public int ServiceRequest_ID { get; set; }
        public string ServiceRequestNumber { get; set; }
        public string FollowUpSubject { get; set; }
        public string FollowUpDescription { get; set; }
        public DateTime StartDateandTime { get; set; }
        public DateTime? EndDateandTime { get; set; }
        public int FollowUpMode_ID { get; set; }
        public int FollowUpStatus_ID { get; set; }
        public string FollowUpMode_Name { get; set; }
        public string FollowUpStatus_Name { get; set; }
        public string Remarks { get; set; }
        public DateTime ServiceRequestDate { get; set; }
        public string CaseType { get; set; }
        public string PartyName { get; set; }
        public string IssueAreaName { get; set; }
        public string Status { get; set; }
        public int PartyID { get; set; }
        public string EnquiryNumber { get; set; }
        public string Mode { get; set; }
        public string Invitee { get; set; }

    }

    #region :::GNMServices :::
    /// <summary>
    ///  public class to declare the properties
    /// </summary>   
    public partial class GNMServices
    {
        public int Company_ID { get; set; }
        public int Product_ID { get; set; }
        public int Party_ID { get; set; }
        public string FollowUpStatus_Name { get; set; }
        public string FollowUpDescription { get; set; }
        public string StartDateandTime { get; set; }
        public string EndDateandTime { get; set; }
        public string FollowUpMode_Name { get; set; }
        public string Remarks { get; set; }
        public string EnquiryType { get; set; }
        public string EnquiryNumber { get; set; }
        public string Date { get; set; }
        public string Customer { get; set; }
        public string IssueArea { get; set; }
        public string CustomerContactEmail { get; set; }
        public string BrandName { get; set; }
        public string SerialNumber { get; set; }
        public int? CurrentHMR { get; set; }
        public string Unique { get; set; }
        public string NextServiceType { get; set; }
        //public string NextServiceDateStr { get; set; }
        public string NextServiceDate { get; set; }
        public string LastServiceDealer { get; set; }
        public int DateLeft { get; set; }
        public string CompanyIDs { get; set; }
        public string CompanyName { get; set; }
        public string RegionName { get; set; }
        public string SendReminder { get; set; }
        public int RownumberID { get; set; }
        public List<ContactPersonDetails> CntDetails { get; set; }
    }
    public partial class ContactPersonDetails
    {
        public string CustomerName { get; set; }
        public string ContactPersonName { get; set; }
        public string ContactPersonEmail { get; set; }
        public string ContactPersonMobile { get; set; }
        public int PartyID { get; set; }
        public int RownumberID { get; set; }
    }
    #endregion

    public partial class SRFollowUpDetails
    {

        public int SRFollowUpDetails_ID { get; set; }
        public int ServiceRequest_ID { get; set; }
        public string FollowUpDescription { get; set; }
        public DateTime StartDateandTime { get; set; }
        public Nullable<System.DateTime> EndDateandTime { get; set; }
        public int FollowUpMode_ID { get; set; }
        public int FollowUpStatus_ID { get; set; }
        public string Remarks { get; set; }
        public virtual ICollection<SRFollowUpInviteDetails> HD_SRFollowUpInviteDetails { get; set; }

    }
    public partial class SRFollowUpInviteDetails
    {
        public int SRFollowUpInviteDetails_ID { get; set; }
        public int ServiceRequest_ID { get; set; }
        public int SRFollowUpDetails_ID { get; set; }
        public bool Invitee_Type { get; set; }
        public Nullable<int> Employee_ID { get; set; }
        public Nullable<int> Party_ID { get; set; }
        public bool IsAttended { get; set; }

    }

}
