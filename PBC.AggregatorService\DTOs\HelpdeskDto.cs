namespace PBC.AggregatorService.DTOs
{
    #region ::: SelectDealerName :::
    public class SelectDealerNameList
    {
        public string value { get; set; } = string.Empty;
        public int Company_ID { get; set; }
        public int Branch { get; set; }
        public string UserCulture { get; set; } = string.Empty;
    }
    #endregion

    #region ::: InitialSetup :::
    public class InitialSetupList
    {
        public int ObjectId { get; set; }
        public int User_ID { get; set; }
        public string HelpDesk { get; set; } = string.Empty;
        public int Company_ID { get; set; }
        public bool NeedToChangepassword { get; set; }
    }
    #endregion

    #region ::: CheckAddPermissions :::
    public class CheckAddPermissionsRequest
    {
        public string Name { get; set; } = string.Empty;
        public string WFName { get; set; } = string.Empty;
        public string HelpDesk { get; set; } = string.Empty;
        public int CompanyId { get; set; }
        public int UserId { get; set; }
    }
    #endregion

    #region ::: ValidateCalldateAndPCD :::
    public class validateCalldateandPCDList
    {
        public DateTime PCD { get; set; }
        public DateTime Calldate { get; set; }
    }
    #endregion

    #region ::: CheckBayWorkshopAvailability :::
    public class CheckBayWorkshopAvailabilityList
    {
        public DateTime ExpectedArrivalDate { get; set; }
        public DateTime ExpectedDepartureDate { get; set; }
        public bool IsFromQuote { get; set; }
        public int Quotation_ID { get; set; }
        public int Branch { get; set; }
        public int VIN { get; set; }
        public int ServiceRequest_ID { get; set; }
    }
    #endregion

    #region ::: CheckForWorkshopBlockOverlap :::
    public class CheckForWorkshopBlockOverlapList
    {
        public DateTime ExpectedArrivalDate { get; set; }
        public DateTime ExpectedDepartureDate { get; set; }
        public bool IsFromQuote { get; set; }
        public int Quotation_ID { get; set; }
        public int Branch { get; set; }
        public int VIN { get; set; }
        public int ServiceRequest_ID { get; set; }
    }
    #endregion

    #region ::: SelectFieldSearchParty :::
    public class SelectFieldSearchParty2List
    {
        public string starts_with { get; set; } = string.Empty;
        public int type { get; set; }
        public int LangID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int Company_ID { get; set; }
    }

    public class SelectFieldSearchPartyRequest
    {
        public SelectFieldSearchParty2List Obj { get; set; } = new();
        public string Sidx { get; set; } = string.Empty;
        public string Sord { get; set; } = string.Empty;
        public int Page { get; set; }
        public int Rows { get; set; }
        public bool Search { get; set; }
        public string Filters { get; set; } = string.Empty;
    }
    #endregion

    #region ::: GetCustomerData :::
    public class GetDataUserLindingList
    {
        public string starts_with { get; set; } = string.Empty;
        public int type { get; set; }
        public int LangID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int Company_ID { get; set; }
    }

    public class GetCustomerDataRequest
    {
        public GetDataUserLindingList Obj { get; set; } = new();
    }
    #endregion

    #region ::: GetDealerData :::
    public class GetDealerDataList
    {
        public string starts_with { get; set; } = string.Empty;
        public int type { get; set; }
        public int LangID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int Company_ID { get; set; }
    }

    public class GetDealerDataRequest
    {
        public GetDealerDataList Obj { get; set; } = new();
    }
    #endregion

    #region ::: GetProductDetails :::
    public class GetProductDetailsUserLandingList
    {
        public int ProductID { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
    }

    public class GetProductDetailsRequest
    {
        public GetProductDetailsUserLandingList Obj { get; set; } = new();
    }
    #endregion

    #region ::: CheckDuplicateContactPerson :::
    public class checkDuplicateContactPersonList
    {
        public string CPName { get; set; } = string.Empty;
        public int Party_ID { get; set; }
    }

    public class CheckDuplicateContactPersonRequest
    {
        public checkDuplicateContactPersonList Obj { get; set; } = new();
    }
    #endregion

    #region ::: GetOpenCampaignDetails :::
    public class GetOpenCampaignDetailsList
    {
        public int User_ID { get; set; }
        public int Company_ID { get; set; }
        public string CompanyIDs { get; set; } = string.Empty;
        public string BranchIDs { get; set; } = string.Empty;
        public int Branch { get; set; }
        public int Language_ID { get; set; }
        public string GeneralLanguageCode { get; set; } = string.Empty;
        public string UserLanguageCode { get; set; } = string.Empty;
        public string UserCulture { get; set; } = string.Empty;
        public int mode { get; set; }
        public int FromManager { get; set; }
        public int StatusID { get; set; }
        public string Legendfilter { get; set; } = "All";
    }

    public class GetOpenCampaignDetailsRequest
    {
        public GetOpenCampaignDetailsList Obj { get; set; } = new();
        public string Sidx { get; set; } = string.Empty;
        public int Rows { get; set; }
        public int Page { get; set; }
        public string Sord { get; set; } = string.Empty;
        public bool Search { get; set; }
        public long Nd { get; set; }
        public string Filters { get; set; } = string.Empty;
        public bool Advnce { get; set; }
        public string Query { get; set; } = string.Empty;
    }
    #endregion

    #region ::: GetInitialData :::
    public class GetInitialDataList
    {
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
        public int Company_ID { get; set; }
        public string UserCulture { get; set; } = string.Empty;
    }
    #endregion

    #region ::: SelectServiceRequest :::
    public class SelectServiceRequestList
    {
        public int User_ID { get; set; }
        public int Company_ID { get; set; }
        public string CompanyIDs { get; set; } = string.Empty;
        public string BranchIDs { get; set; } = string.Empty;
        public int Branch { get; set; }
        public int Language_ID { get; set; }
        public string GeneralLanguageCode { get; set; } = string.Empty;
        public string UserLanguageCode { get; set; } = string.Empty;
        public string UserCulture { get; set; } = string.Empty;
        public int mode { get; set; }
        public int FromManager { get; set; }
        public int StatusID { get; set; }
        public string Legendfilter { get; set; } = "All";
    }

    public class SelectServiceRequestRequest
    {
        public SelectServiceRequestList Obj { get; set; } = new();
        public string Sidx { get; set; } = string.Empty;
        public int Rows { get; set; }
        public int Page { get; set; }
        public string Sord { get; set; } = string.Empty;
        public bool Search { get; set; }
        public long Nd { get; set; }
        public string Filters { get; set; } = string.Empty;
        public bool Advnce { get; set; }
        public string Query { get; set; } = string.Empty;
    }
    #endregion

    #region ::: GetWorkFlowSummary :::
    public class GetWorkFlowSummaryList
    {
        public int Company_ID { get; set; }
        public int Branch_ID { get; set; }
        public int WorkFlow_ID { get; set; }
        public int User_ID { get; set; }
        public int Branch { get; set; }
        public string UserCulture { get; set; } = string.Empty;
    }
    #endregion

    #region ::: SaveCustomer :::
    public class SaveCustomerList
    {
        public int Branch { get; set; }
        public string key { get; set; } = string.Empty;
        public string PartyAddress { get; set; } = string.Empty;
        public string PartyTaxDetails { get; set; } = string.Empty;
        public int User_ID { get; set; }
        public int Company_ID { get; set; }
    }
    #endregion

    #region ::: TabPosition :::
    public class TabPosition
    {
        public int ID { get; set; }
        public bool Visibility { get; set; }
        public int Position { get; set; }
    }
    #endregion

    #region ::: UpdateIsEditTicket :::
    public class UpdateIsEditTicketList
    {
        public int ServiceRequest_ID { get; set; }
        public int Type { get; set; } // 1 - editing, 2 - saved
        public int User_ID { get; set; }
    }
    #endregion
}
